{"compilerOptions": {"outDir": "lib", "emitDeclarationOnly": true, "composite": true, "target": "ES2024", "strict": false, "strictFunctionTypes": true, "strictNullChecks": true, "allowJs": true, "sourceMap": true, "moduleResolution": "bundler", "resolveJsonModule": true, "esModuleInterop": true, "skipLibCheck": true, "allowSyntheticDefaultImports": true, "noUnusedLocals": true, "verbatimModuleSyntax": true, "forceConsistentCasingInFileNames": true, "allowImportingTsExtensions": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "noImplicitThis": true, "moduleDetection": "force", "jsx": "preserve", "jsxImportSource": "vue", "rootDir": ".", "lib": ["DOM", "DOM.Iterable", "ESNext", "ESNext.Array"]}, "include": ["./framework/ngApp/**/*.js", "./framework/views/**/*.js", "./framework/views_vuejs/**/*.js"], "exclude": ["node_modules", "dist", "./eslint.config.mjs", "./assets/", "./common/", "./ical/", "./reports/", "./resources/", "./sql_scripts/", "./sqlserver/", "./framework/vendor/**", "./framework/lib/**"]}
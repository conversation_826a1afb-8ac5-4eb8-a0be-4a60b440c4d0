import path from 'path';
import globals from 'globals';
import { globalIgnores } from 'eslint/config';
import tsParser from '@typescript-eslint/parser';
import typescriptEslint from 'typescript-eslint';
import html from 'eslint-plugin-html';
import eslint<PERSON>onfig<PERSON>rettier from 'eslint-config-prettier/flat';
import jsdoc from 'eslint-plugin-jsdoc';
import eslint from '@eslint/js';
import pluginOxlint from 'eslint-plugin-oxlint';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

export default typescriptEslint.config(
    globalIgnores([
        './framework/assets/**',
        './framework/commands/**',
        './framework/data/**',
        './framework/lib/**',
        './framework/vendor/**',
        './framework/amchart/**',
        './common/**',
        './assets/**',
        '**/build/**',
        '**/public/**',
        '**/coverage/**',
    ]),
    {
        files: ['**/*.js', '**/*.ts'],
        languageOptions: {
            parser: tsParser,
            parserOptions: {
                projectService: true,
                project: path.resolve(__dirname, './tsconfig.json'),
                tsconfigRootDir: __dirname,
                ecmaVersion: 'latest',
                sourceType: 'module',
            },
            globals: {
                ga: 'readonly',
                $: 'readonly',
                axios: 'readonly',
                tinymce: 'readonly',
                angular: 'readonly',
                ...globals.browser,
            },
        },
        extends: [
            eslint.configs.recommended,
            ...typescriptEslint.configs.recommended,
            jsdoc.configs['flat/recommended'],
        ],
        rules: {
            // JSDoc
            'jsdoc/tag-lines': ['warn', 'any', { startLines: 1 }],
            'prefer-const': 'error',
            'prefer-template': 'error',
            'prefer-promise-reject-errors': 'off',

            // this rule, if on, would require explicit return type on the `render` function
            '@typescript-eslint/explicit-function-return-type': 'off',
            '@typescript-eslint/consistent-type-imports': ['error', { prefer: 'type-imports' }],

            // in plain CommonJS modules, you can't use `import foo = require('foo')` to pass this rule, so it has to be disabled
            '@typescript-eslint/no-unused-vars': [
                'error',
                {
                    args: 'all',
                    argsIgnorePattern: '^_',
                    caughtErrors: 'all',
                    caughtErrorsIgnorePattern: '^_',
                    destructuredArrayIgnorePattern: '^_',
                    varsIgnorePattern: '^_',
                    ignoreRestSiblings: true,
                },
            ],
            // allow debugger during development only
            'no-debugger': process.env.NODE_ENV === 'production' ? 'error' : 'off',
        },
    },
    {
        plugins: { html },
        settings: {
            'html/html-extensions': ['.html', '.php'],
        },
        files: ['**/*.html'],
        extends: [eslint.configs.recommended],
        rules: {
            'prefer-const': 'error',
            'prefer-template': 'error',
            'prefer-promise-reject-errors': 'off',
        },
        languageOptions: {
            globals: {
                ga: 'readonly',
                $: 'readonly',
                axios: 'readonly',
                tinymce: 'readonly',
                angular: 'readonly',
                ...globals.browser,
            },
        },
    },
    ...pluginOxlint.configs['flat/recommended'],
    eslintConfigPrettier,
);

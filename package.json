{"private": true, "version": "1.1.0", "scripts": {"style": "prettier framework --check", "prepare": "husky", "lint": "eslint framework", "release": "commit-and-tag-version", "commit": "commit"}, "engines": {"node": ">=22.14.0"}, "lint-staged": {"**/*": ["prettier --write --ignore-unknown"]}, "commitlint": {"extends": ["@commitlint/config-conventional"], "scope-case": ["lower-case", "kebab-case"]}, "dependencies": {"@types/angular": "^1.8.9", "@types/chosen-js": "^1.8.6", "amcharts3": "^3.21.15", "angular": "^1.8.3", "chosen-js": "^1.8.7", "conventional-changelog": "^7.0.2", "jquery": "^3.7.1", "jquery.uix.multiselect": "github:yanickrochon/jquery.uix.multiselect"}, "devDependencies": {"@commitlint/cli": "^19.8.0", "@commitlint/config-conventional": "^19.8.0", "@commitlint/prompt-cli": "^19.8.0", "@eslint/js": "^9.27.0", "@types/jquery": "^3.5.32", "@types/node": "^22.15.3", "@typescript-eslint/parser": "^8.32.1", "commit-and-tag-version": "^12.5.1", "conventional-changelog-conventionalcommits": "^9.0.0", "eslint": "^9.26.0", "eslint-config-prettier": "^10.1.2", "eslint-plugin-html": "^8.1.3", "eslint-plugin-jsdoc": "^50.6.11", "eslint-plugin-oxlint": "^0.16.10", "eslint-plugin-prettier": "^5.3.1", "globals": "^16.0.0", "husky": "^9.1.7", "lint-staged": "^15.5.1", "prettier": "^3.5.3", "rimraf": "^6.0.1", "typescript": "^5.8.2", "typescript-eslint": "^8.32.0"}}
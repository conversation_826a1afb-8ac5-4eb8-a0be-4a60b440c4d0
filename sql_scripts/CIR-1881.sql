use npms;

CREATE TABLE [dbo].[quick_reports](
	[qr_id] [tinyint] IDENTITY(1,1) NOT NULL,
	[qr_text] [varchar](50) NULL,
	[qr_url] [varchar](100) NULL
) ON [PRIMARY]
GO


CREATE TABLE [dbo].[quick_report_access](
	[qa_id] [int] IDENTITY(1,1) NOT NULL,
	[qa_qr_id] [tinyint] NULL,
	[qa_user] [int] NULL
) ON [PRIMARY]
GO

SET IDENTITY_INSERT [dbo].[quick_reports] ON
GO
INSERT [dbo].[quick_reports] ([qr_id], [qr_text], [qr_url]) VALUES (1, N'Tenant Activity', N'?module=managementReports&command=tenantActivity')
GO
INSERT [dbo].[quick_reports] ([qr_id], [qr_text], [qr_url]) VALUES (2, N'Arrears Report', N'?module=accountingReports&command=unpaidDebtors')
GO
INSERT [dbo].[quick_reports] ([qr_id], [qr_text], [qr_url]) VALUES (3, N'Generate Arrears Statements', N'?module=ar&command=generateArrearsStatement')
GO
INSERT [dbo].[quick_reports] ([qr_id], [qr_text], [qr_url]) VALUES (4, N'Property Report', N'?module=managementReports&command=propertyreport')
GO
INSERT [dbo].[quick_reports] ([qr_id], [qr_text], [qr_url]) VALUES (5, N'Owners Reports', N'?module=managementReports&command=ownerReport')
GO
INSERT [dbo].[quick_reports] ([qr_id], [qr_text], [qr_url]) VALUES (6, N'Tenancy Schedule', N'?module=managementReports&command=tenancyReport')
GO
INSERT [dbo].[quick_reports] ([qr_id], [qr_text], [qr_url]) VALUES (7, N'Custom Reports', N'?module=managementReports&command=reports')
GO
INSERT [dbo].[quick_reports] ([qr_id], [qr_text], [qr_url]) VALUES (8, N'Sales Report', N'?module=retail&command=salesReport')
GO
INSERT [dbo].[quick_reports] ([qr_id], [qr_text], [qr_url]) VALUES (9, N'Cash Book', N'?module=accountingReports&command=cashBook')
GO
INSERT [dbo].[quick_reports] ([qr_id], [qr_text], [qr_url]) VALUES (10, N'Diary Report', N'?module=home&command=diaryReport')
GO
SET IDENTITY_INSERT [dbo].[quick_reports] OFF
GO
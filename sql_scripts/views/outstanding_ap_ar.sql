DROP VIEW IF EXISTS outstanding_balances;
DROP VIEW IF EXISTS outstanding_ar;
DROP VIEW IF EXISTS outstanding_ap;
DROP VIEW IF EXISTS PMVDF_AR_BAL;


CREATE VIEW [dbo].[outstanding_ar]
AS
SELECT        X0.batch_nr AS batch_number, X0.batch_line_nr AS batch_line_number, X0.debtor_code, X0.ref_2 AS property_code, ISNULL(X0.fund,'') AS fund, X0.ref_3 AS account_code, X0.ref_4 AS lease_code, X0.ref_5 AS unit_code, X0.description, X0.artr_gst_code AS tax_code,
                         X0.trans_amt AS gross_amount, X0.artr_net_amt AS net_amount, X0.artr_tax_amt AS tax_amount, X0.spare_date_1 AS from_date, X0.spare_date_2 AS to_date, X0.trans_date AS transaction_date, X0.due_date,
                         X0.artr_gst_inv_no AS invoice_number, X0.artr_gst_inv_dt AS invoice_date, X0.trans_type AS transaction_type, X0.trans_amt - COALESCE (SUM(- X1.pmxd_alloc_amt), CONVERT(money, 0.00))
                         - COALESCE (SUM(X1_2.pmxd_alloc_amt), CONVERT(money, 0.00)) AS outstanding_gross, X0.artr_net_amt - COALESCE (SUM(- X1.pmxd_alloc_amt + X1.pmxd_tax_amt), CONVERT(money, 0.00))
                         - COALESCE (SUM(X1_2.pmxd_alloc_amt + X1_2.pmxd_tax_amt), CONVERT(money, 0.00)) AS outstanding_net, X0.artr_tax_amt - COALESCE (SUM(- X1.pmxd_tax_amt), CONVERT(money, 0.00))
                         - COALESCE (SUM(- X1_2.pmxd_tax_amt), CONVERT(money, 0.00)) AS outstanding_tax
FROM            dbo.ar_transaction AS X0 LEFT OUTER JOIN
                             (SELECT        pmxd_t_batch, pmxd_t_line, SUM(pmxd_alloc_amt) AS pmxd_alloc_amt, SUM(pmxd_tax_amt) AS pmxd_tax_amt
                               FROM            dbo.pmxd_ar_alloc
                               GROUP BY pmxd_t_batch, pmxd_t_line) AS X1 ON X0.batch_nr = X1.pmxd_t_batch AND X0.batch_line_nr = X1.pmxd_t_line LEFT OUTER JOIN
                             (SELECT        pmxd_f_batch, pmxd_f_line, SUM(pmxd_alloc_amt) AS pmxd_alloc_amt, SUM(pmxd_tax_amt) AS pmxd_tax_amt
                               FROM            dbo.pmxd_ar_alloc AS pmxd_ar_alloc_1
                               GROUP BY pmxd_f_batch, pmxd_f_line) AS X1_2 ON X0.batch_nr = X1_2.pmxd_f_batch AND X0.batch_line_nr = X1_2.pmxd_f_line
WHERE        (X0.trans_type IN ('INV', 'CRE'))
GROUP BY X0.batch_nr, X0.batch_line_nr, X0.artr_tax_amt, X0.debtor_code, X0.ref_2, X0.fund, X0.ref_3, X0.ref_4, X0.ref_5, X0.artr_gst_code, X0.trans_amt, X0.artr_net_amt, X0.spare_date_1, X0.spare_date_2, X0.trans_date, X0.due_date,
                         X0.artr_gst_inv_no, X0.artr_gst_inv_dt, X0.trans_type, X0.description
HAVING        (X0.trans_amt - COALESCE (SUM(- X1.pmxd_alloc_amt), CONVERT(money, 0.00)) - COALESCE (SUM(X1_2.pmxd_alloc_amt), CONVERT(money, 0.00)) <> 0)

---------------------------------------------------------------------------------------------------

CREATE VIEW outstanding_ap
AS
SELECT        X0.recovered, X0.description, X0.aptr_year AS year, X0.aptr_period AS period, X0.held_flag, X0.po_record_no, ISNULL(X0.fund,'') AS fund, X0.bank AS bank_code, X0.batch_nr AS batch_number, X0.batch_line_nr AS batch_line_number,
                         X0.creditor_code, X0.ref_2 AS property_code, X0.ref_3 AS account_code, X0.ref_4 AS lease_code, X0.ref_5 AS unit_code, X0.aptr_gst_group AS tax_code, X0.trans_amt AS gross_amount, X0.aptr_net_amt AS net_amount,
                         X0.aptr_tax_amt AS tax_amount, X0.spare_date_1 AS from_date, X0.spare_date_2 AS to_date, X0.trans_date AS transaction_date, X0.due_date, X0.ref_1 AS invoice_number, X0.trans_type AS transaction_type,
                         X0.trans_amt - COALESCE (SUM(- X1.pmxc_alloc_amt), CONVERT(money, 0.00)) - COALESCE (SUM(X1_2.pmxc_alloc_amt), CONVERT(money, 0.00)) AS outstanding_gross,
                         X0.aptr_net_amt - COALESCE (SUM(- X1.pmxc_alloc_amt - X1.pmxc_tax_amt), CONVERT(money, 0.00)) + COALESCE (SUM(- X1_2.pmxc_alloc_amt - X1_2.pmxc_tax_amt), CONVERT(money, 0.00)) AS outstanding_net,
                         X0.aptr_tax_amt - COALESCE (SUM(X1.pmxc_tax_amt), CONVERT(money, 0.00)) + COALESCE (SUM(- X1_2.pmxc_tax_amt), CONVERT(money, 0.00)) AS outstanding_tax
FROM            dbo.ap_transaction AS X0 LEFT OUTER JOIN
                             (SELECT        pmxc_t_batch, pmxc_t_line, SUM(pmxc_alloc_amt) AS pmxc_alloc_amt, SUM(pmxc_tax_amt) AS pmxc_tax_amt
                               FROM            dbo.pmxc_ap_alloc
                               GROUP BY pmxc_t_batch, pmxc_t_line) AS X1 ON X0.batch_nr = X1.pmxc_t_batch AND X0.batch_line_nr = X1.pmxc_t_line LEFT OUTER JOIN
                             (SELECT        pmxc_f_batch, pmxc_f_line, SUM(pmxc_alloc_amt) AS pmxc_alloc_amt, SUM(pmxc_tax_amt) AS pmxc_tax_amt
                               FROM            dbo.pmxc_ap_alloc AS pmxc_ap_alloc_1
                               GROUP BY pmxc_f_batch, pmxc_f_line) AS X1_2 ON X0.batch_nr = X1_2.pmxc_f_batch AND X0.batch_line_nr = X1_2.pmxc_f_line
WHERE        (X0.trans_type IN ('INV', 'CRE'))
GROUP BY X0.recovered, X0.description, X0.aptr_year, X0.aptr_period, X0.held_flag, X0.po_record_no, X0.fund, X0.bank, X0.batch_nr, X0.batch_line_nr, X0.aptr_tax_amt, X0.creditor_code, X0.ref_2, X0.ref_3, X0.ref_4, X0.ref_5,
                         X0.aptr_gst_group, X0.trans_amt, X0.aptr_net_amt, X0.spare_date_1, X0.spare_date_2, X0.trans_date, X0.due_date, X0.ref_1, X0.trans_type
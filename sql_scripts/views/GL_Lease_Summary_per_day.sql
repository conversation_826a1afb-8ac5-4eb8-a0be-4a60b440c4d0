CREATE VIEW [dbo].[GL_Lease_Summary_per_day] AS
SELECT
    a.pmpr_prop_type         AS district_code
  , b.pmzz_desc              AS district
  , a.pmpr_prop_group        AS shire_code
  , c.pmzz_desc              AS shire
  , a.pmpr_prop              AS property_code
  , a.pmpr_name              AS property_name
  , a.pmpr_street            AS property_address
  , d.pmle_lease             AS lease_code
  , d.pmle_name              AS lease_name
  , d.pmle_description       AS lease_description
  , e.account_id             AS account_code
  , f.pmca_gl_account_group2 AS account_group
  , f.pmca_name              AS account_description
  , e.method                 AS gl_method
  , e.transaction_date
  , SUM
        (
            CASE
                WHEN e.transaction_type IN ('INV'
                                          , 'CRE')
                    AND method                   = 1
                    AND f.pmca_gl_account_group2 = 'INC.OWN'
                    THEN e.transaction_amount
                    ELSE 0
            END
        )
    * - 1 AS charges
  , SUM
        (
            CASE
                WHEN e.transaction_type IN ('INV'
                                          , 'CRE')
                    AND method       = 1
                    AND e.account_id = g.gstOutputTax
                    THEN e.transaction_amount
                    ELSE 0
            END
        )
    * - 1 AS charges_gst
  , SUM
        (
            CASE
                WHEN e.transaction_type IN ('CSH')
                    AND method                   = 2
                    AND f.pmca_gl_account_group2 = 'INC.OWN'
                    THEN e.transaction_amount
                    ELSE 0
            END
        )
    AS receipts
  , SUM
        (
            CASE
                WHEN e.transaction_type IN ('CSH')
                    AND method       = 2
                    AND e.account_id = g.gstInputTax
                    THEN e.transaction_amount
                    ELSE 0
            END
        )
    AS receipts_gst
FROM
    dbo.pmpr_property AS a
    INNER JOIN
        dbo.pmzz_param AS b
        ON
            a.pmpr_prop_type    = b.pmzz_code
            AND b.pmzz_par_type = 'PROPERTY'
    INNER JOIN
        dbo.pmzz_param AS c
        ON
            a.pmpr_prop_group   = c.pmzz_code
            AND c.pmzz_par_type = 'PROPGROUP'
    INNER JOIN
        dbo.pmle_lease AS d
        ON
            a.pmpr_prop = d.pmle_prop
    LEFT OUTER JOIN
        dbo.gl_transaction AS e
        ON
            a.pmpr_prop      = e.property_id
            AND d.pmle_lease = e.lease_id
    INNER JOIN
        dbo.pmca_chart AS f
        ON
            e.account_id = f.pmca_code
    LEFT OUTER JOIN
        dbo.gl_linked_accounts AS g
        ON
            e.account_id    = g.gstOutputTax
            OR e.account_id = g.gstInputTax
GROUP BY
    a.pmpr_prop_type
  , a.pmpr_prop_group
  , b.pmzz_desc
  , c.pmzz_desc
  , a.pmpr_prop
  , a.pmpr_name
  , a.pmpr_street
  , d.pmle_lease
  , d.pmle_name
  , d.pmle_description
  , e.account_id
  , e.method
  , e.transaction_date
  , f.pmca_gl_account_group2
  , f.pmca_name
GO
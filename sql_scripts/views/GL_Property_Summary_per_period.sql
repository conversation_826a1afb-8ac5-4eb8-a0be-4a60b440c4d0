CREATE VIEW [dbo].[GL_Property_Summary_per_period] AS
SELECT
    a.pmpr_prop_type         AS district_code
  , b.pmzz_desc              AS district
  , a.pmpr_prop_group        AS shire_code
  , c.pmzz_desc              AS shire
  , a.pmpr_prop              AS property_code
  , a.pmpr_name              AS property_name
  , e.accountID              AS account_code
  , f.pmca_type              AS account_type
  , f.pmca_gl_account_group1 AS account_group1
  , f.pmca_gl_account_group2 AS account_group2
  , f.pmca_name              AS account_description
  , e.period
  , e.year
  , g.pmcp_start_dt AS start_date
  , g.pmcp_end_dt   AS end_date
  , g.pmcp_closed   AS calendar_closed
  , e.balanceCash *
        (
            CASE
                WHEN f.pmca_type = 'I'
                    THEN - 1
                    ELSE 1
            END
        )
                                                                    AS cash_balance
  , ISNULL(SUM(h.pmrp_b_c_amt), 0) + ISNULL(SUM(i.pmep_b_c_amt), 0) AS cash_budget
  , e.balanceAccruals              *
        (
            CASE
                WHEN f.pmca_type = 'I'
                    THEN - 1
                    ELSE 1
            END
        )
                                                                    AS accruals_balance
  , ISNULL(SUM(h.pmrp_b_a_amt), 0) + ISNULL(SUM(i.pmep_b_a_amt), 0) AS accruals_budget
FROM
    dbo.pmpr_property AS a
    INNER JOIN
        dbo.pmzz_param AS b
        ON
            a.pmpr_prop_type    = b.pmzz_code
            AND b.pmzz_par_type = 'PROPERTY'
    INNER JOIN
        dbo.pmzz_param AS c
        ON
            a.pmpr_prop_group   = c.pmzz_code
            AND c.pmzz_par_type = 'PROPGROUP'
    LEFT OUTER JOIN
        dbo.gl_trial_balance AS e
        ON
            a.pmpr_prop = e.propertyID
    INNER JOIN
        dbo.pmca_chart AS f
        ON
            e.accountID = f.pmca_code
    INNER JOIN
        dbo.pmcp_prop_cal AS g
        ON
            a.pmpr_prop  = g.pmcp_prop
            AND e.year   = g.pmcp_year
            AND e.period = g.pmcp_period
    LEFT OUTER JOIN
        dbo.pmrp_b_rev_per AS h
        ON
            e.accountID      = h.pmrp_acc
            AND e.propertyID = h.pmrp_prop
            AND e.period     = h.pmrp_per
            AND e.year       = h.pmrp_year
    LEFT OUTER JOIN
        dbo.pmep_b_exp_per AS i
        ON
            e.accountID      = i.pmep_exp_acc
            AND e.propertyID = i.pmep_prop
            AND e.period     = i.pmep_per
            AND e.year       = i.pmep_year
GROUP BY
    a.pmpr_prop_type
  , a.pmpr_prop_group
  , b.pmzz_desc
  , c.pmzz_desc
  , a.pmpr_prop
  , a.pmpr_name
  , e.accountID
  , f.pmca_type
  , f.pmca_gl_account_group1
  , f.pmca_gl_account_group2
  , f.pmca_name
  , e.period
  , e.year
  , e.balanceAccruals
  , e.balanceCash
  , g.pmcp_start_dt
  , g.pmcp_end_dt
  , g.pmcp_closed
GO
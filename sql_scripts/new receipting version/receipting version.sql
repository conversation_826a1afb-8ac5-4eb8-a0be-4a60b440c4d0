/* 

RUN IT ONE BY ONE FOR EACH DATABASE
CAUSE SOME MIGHT HAVE UPDATED COLUMNS
AND OTHERS DON'T

*/
ALTER TABLE [dbo].[documents]
ADD [document_seq] INT DEFAULT(0);

ALTER TABLE pmbk_bank
ADD pmbk_last_zero_rcpt numeric(12) NULL DEFAULT(0);
pmbk_download_statement_client_number
ALTER TABLE pmbk_bank
ADD pmbk_download_statement_client_number varchar(60) NULL DEFAULmainT(NULL);

ALTER TABLE pmrc_receipt
ADD pmrc_zero_receipt_no numeric(12) NULL DEFAULT(NULL);

ALTER TABLE [pmuc_unall_csh] 
ALTER COLUMN [pmuc_create_user] char(100) NULL;

ALTER TABLE [pmuc_unall_csh] 
ALTER COLUMN [pmuc_mod_user] char(100) NULL;

ALTER TABLE [pmxd_ar_alloc] 
ALTER COLUMN [pmxd_create_user] char(100) NULL;

ALTER TABLE [pmxd_ar_alloc] 
ALTER COLUMN [pmxd_mod_user] char(100) NULL;

CREATE TABLE [dbo].[pmrc_receipt_files_trans](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[file_code] [varchar](50) NULL,
	[pattern] [varchar](max) NOT NULL,
	[amount] [decimal](19, 2) NOT NULL,
	[trans_date] [date] NULL,
	[trans_type] [varchar](50) NULL,
	[trans_type_name] [varchar](50) NULL,
	[debtor_code] [varchar](50) NOT NULL,
	[lease_code] [varchar](50) NOT NULL,
	[property_code] [varchar](50) NOT NULL,
	[crn] [varchar](50) NOT NULL,
	[receipt_id] [int] NULL,
	[receipt_user] [varchar](50) NULL,
	[receipted_at] [datetime] NULL,
	[adjustment_id] VARCHAR(50),
  	[adjustment_user] VARCHAR(50),
  	[adjustment_at] DATETIME,
  	[bank_code] VARCHAR(50),
  	[bsb_no] VARCHAR(100),
  	[bank_account_no] VARCHAR(100),
	[create_user] [varchar](50) NOT NULL,
	[created_at] [datetime] NOT NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY];

CREATE TABLE [dbo].[pmrc_receipt_files_trans_amounts](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[total_amount] [decimal](19, 2) NOT NULL,
	[amount] [decimal](19, 2) NOT NULL,
	[debtor_code] [varchar](50) NOT NULL,
	[lease_code] [varchar](50) NOT NULL,
	[property_code] [varchar](50) NOT NULL,
	[account_code] [varchar](50) NOT NULL,
	[receipt_id] [int] NULL,
	[create_user] [varchar](50) NOT NULL,
	[created_at] [datetime] NOT NULL
) ON [PRIMARY];
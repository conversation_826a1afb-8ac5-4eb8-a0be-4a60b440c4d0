-- PER CLIENT
CREATE TABLE postmark_suppressions (
	id INT IDENTITY(1,1) NOT NULL PRIMARY KEY,
    email VARCHAR(200) NOT NULL,
    type VARCHAR(100) NOT NULL,
    origin VARCHAR(100) NULL,
    status VARCHAR(50) NULl,
    created_at VARCHAR(100) NULL,
    updated_at VARCHAR(100) NULL
);

ALTER TABLE postmark_suppressions
DROP COLUMN created_at, updated_at;

-- npms
-- FOR WEBHOOK TESTING PURPOSES
CREATE TABLE postmark_webhook_logs (
    id INT IDENTITY(1,1) NOT NULL PRIMARY KEY,
    form TEXT NULL,
    created_at DATETIME DEFAULT(CURRENT_TIMESTAMP)
)
ALTER TABLE pmpr_property
    ADD pmpr_is_ledger TINYINT NOT NULL DEFAULT 0;

ALTER TABLE pmle_lease
    ADD pmle_sub_ledger_type varchar(20) NULL;


INSERT INTO pmzz_param VALUES
 ('LEDGER', 'BOND', 'Bonds'),
 ('LEDGER', 'LEASE', 'Property/Unit for Lease'),
 ('LEDGER', 'OTHER', 'Other Deposits'),
 ('LEDGER', 'SALE', 'Property for Sale');

INSERT INTO pmzz_param VALUES
('SLTYPE', 'AGENT', 'Agent'),
('SLTYPE', 'BOND', 'Bond'),
('SLTYPE', 'LESSEE', 'Lessee'),
('SLTYPE', 'LESSOR', 'Lessor'),
('SLTYPE', 'MARKETING', 'Marketing'),
('SLTYPE', 'PURCHASER', 'Purchaser'),
('SLTYPE', 'SELLER', 'Seller'),
('SLTYPE', 'SUPPLIER', 'Supplier');
-- LEDGER - Ledger Category
    -- Bonds
    -- Other Deposits
    -- Property for Sale
    -- Property/Unit for Lease

-- SLTYPE - Sub-Ledger Type
    -- seller
    -- marketing?
    -- purchaser
    -- lessee
    -- lessor
    -- bond
    -- supplier
    -- agent

DROP VIEW IF EXISTS dailyCashBookBySubLedger;

CREATE VIEW dailyCashBookBySubLedger
AS

SELECT property, lease, allocDate, SUM(changeDaily) as cib,
SUM (SUM(changeDaily)) OVER (PARTITION BY property,lease ORDER BY property, lease, allocDate) AS dailyBalance
FROM
(
    SELECT property, lease, allocDate, SUM(balance) as changeDaily
    FROM
        (SELECT pmxd_prop as property, pmxd_lease as lease, pmxd_alloc_dt as allocDate, SUM(pmxd_alloc_amt*-1) as balance
        FROM pmxd_ar_alloc
        WHERE pmxd_f_type = 'CSH'
        GROUP BY pmxd_prop, pmxd_lease, pmxd_alloc_dt
        ) arp
    GROUP BY property, lease, allocDate

    UNION ALL

    SELECT property, lease, allocDate, SUM(balance*-1) as changeDaily
    FROM
        (SELECT pmxc_prop as property, pmxc_lease as lease, pmxc_alloc_dt as allocDate, SUM(pmxc_alloc_amt*-1) as balance
        FROM pmxc_ap_alloc
        WHERE pmxc_f_type = 'PAY'
        GROUP BY pmxc_prop, pmxc_lease, pmxc_alloc_dt
        ) ap

    GROUP BY property, lease, allocDate
    UNION ALL

	SELECT pmuc_prop as property, pmuc_lease as lease, pmuc_rcpt_dt as allocDate, SUM(pmuc_amt*-1) as changeDaily
	FROM pmuc_unall_csh
	GROUP BY pmuc_prop, pmuc_lease, pmuc_rcpt_dt
) x
JOIN pmpr_property ON pmpr_prop = property
AND pmpr_is_ledger = 1
GROUP BY property, lease,  allocDate

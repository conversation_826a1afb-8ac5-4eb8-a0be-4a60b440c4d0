CREATE TABLE [dbo].[owner_menu](
	[owner_menu_id] [int] IDENTITY(1,1) NOT NULL,
	[owner_client_name] varchar(50) NULL,
	[owner_page_id] [int] NULL
) ON [PRIMARY]
GO;

INSERT INTO owner_menu(owner_client_name , owner_page_id )
SELECT database_name,
pageID
FROM modules m, pages p, menuGroups g, userGroups u ,database_list
WHERE g.menuGroupID = p.menuGroupID AND g.groupID = u.groupID AND u.groupCode = 'O' AND p.moduleID=m.moduleID
And g.title in ('Reporting','Financials')
ORDER by database_name, p.menuGroupID
GO;
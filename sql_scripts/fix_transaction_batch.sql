DELETE FROM transaction_batch_no WHERE trans_type = 'GL_SOURCE_';
UPDATE transaction_batch_no SET batch_nr =
                                    CASE
                                        WHEN (SELECT ISNULL(MAX(batch_nr),0) FROM ap_transaction) >= (SELECT ISNULL(MAX(batch_nr),0) AS max_batch FROM ap_batch)
                                            THEN (SELECT ISNULL(MAX(batch_nr),0) FROM ap_transaction)
                                        ELSE (SELECT ISNULL(MAX(batch_nr),0) FROM ap_batch)
                                        END
WHERE trans_type = 'AP';

UPDATE transaction_batch_no SET batch_nr = (SELECT ISNULL(MAX(batch_nr),0) FROM ar_transaction)
WHERE trans_type = 'AR';

UPDATE transaction_batch_no SET batch_nr = (SELECT MAX(pmxc_alloc_nr) FROM pmxc_ap_alloc)
WHERE trans_type = 'AP_ALLOC';

UPDATE transaction_batch_no SET batch_nr = (SELECT MAX(pmxd_alloc_nr) FROM pmxd_ar_alloc)
WHERE trans_type = 'AR_ALLOC';

UPDATE transaction_batch_no SET batch_nr = 1000000 WHERE batch_nr = 0;
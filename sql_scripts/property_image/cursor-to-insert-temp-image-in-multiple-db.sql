use npms
DECLARE @useDB as NVARCHAR(50); --Declare variables to hold the output from the cursor.
DECLAR<PERSON> @DBCursor as CURSOR; --Declare the cursor object;
DECLARE @Sql as  NVARCHAR(2000); 
--Assign the query to the cursor.--
SET @DBCursor = CURSOR FOR
select 
RTRIM(database_name) AS useDB
from database_list --where database_name != 'APG_test';

OPEN @DBCursor; --Open the cursor.

FETCH NEXT FROM @DBCursor INTO @useDB; -- Fetch the first row
WHILE @@FETCH_STATUS = 0
BEGIN
-- PRINT @useDB; 
Select @Sql = 'Use ' + @useDB +
' CREATE TABLE [dbo].[temp_image](
	[img_id] [int] IDENTITY(1,1) NOT NULL,
	[img_prop_id] [varchar](50) NOT NULL,
	[img_type] [varchar](50) NOT NULL,
	[img_title] [varchar](50) NULL,
	[img_description] [varchar](200)NULL,
	[img_filename] [varchar](150) NOT NULL,
	[img_date_created] [datetime2](7) NOT NULL,
	[img_created_by] [varchar](50) NOT NULL,
	[img_date_updated] [datetime2](7) NULL,
	[img_updated_by] [varchar](50) NULL,
	[img_status] [int] NOT NULL,
 CONSTRAINT [PK_temp_image] PRIMARY KEY CLUSTERED 
(
	[img_id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]';
Exec sp_executesql @Sql;

 FETCH NEXT FROM @DBCursor INTO @useDB;
	END
CLOSE @DBCursor;
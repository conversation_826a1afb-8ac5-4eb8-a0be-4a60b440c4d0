CREATE TABLE [dbo].[sso_login_attempt_details] (
    [ID] INT NOT NULL IDENTITY PRIMARY KEY,
    [created_at] DATETIME NOT NULL DEFAULT (GETDATE()),
    [user_id] int,
    [client_ip] VARCHAR(100), --
    [client_browser] VARCHAR(50), --
    [client_device] VARCHAR(50),
    [client_os] VARCHAR(50),
    [client_location] VARCHAR(255),
    [attempt_counter] int,
    [email_warning_sent] BIT,
    [success_flag] BIT DEFAULT 0
    );


ALTER TABLE sso_logs ADD client_os VARCHAR(100) DEFAULT NULL;
ALTER TABLE sso_logs ADD client_device VARCHAR(100) DEFAULT NULL;
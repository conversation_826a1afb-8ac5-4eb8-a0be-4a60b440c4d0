--PER CLIENT

CREATE TABLE [dbo].[cost_per_company](
	[cpc_id] [int] IDENTITY(1,1) NOT NULL,
	[cpc_year] [varchar](4) NULL,
	[cpc_fte] [numeric](10, 2) NULL,
	[cpc_rent] [numeric](10, 2) NULL,
	[cpc_insurance] [numeric](10, 2) NULL,
	[cpc_utilities] [numeric](10, 2) NULL,
	[cpc_subscription] [numeric](10, 2) NULL,
	[cpc_gov_fee] [numeric](10, 2) NULL,
	[cpc_parking] [numeric](10, 2) NULL,
	[cpc_others] [numeric](10, 2) NULL,
	[cpc_total] [numeric](10, 2) NULL,
	[cpc_total_fte] [numeric](10, 2) NULL,
	[cpc_salary] [numeric](10, 2) NULL,
	[cpc_superannuation] [numeric](10, 2) NULL,
	[cpc_bonuses] [numeric](10, 2) NULL,
	[cpc_trust_others] [numeric](10, 2) NULL,
	[cpc_trust_total] [numeric](10, 2) NULL,
	[cpc_trus_fte] [numeric](10, 2) NULL,
	[cpc_cost_of_desk] [numeric](10, 2) NULL,
	[cpc_total_cost] [numeric](10, 2) NULL,
	[cpc_it] [numeric](10, 2) NULL,
	[cpc_clerical] [numeric](10, 2) NULL,
	[cpc_misc] [numeric](10, 2) NULL,
	[cpc_trust_other_total] [numeric](10, 2) NULL
) ON [PRIMARY]
GO

DROP TABLE [dbo].[cost_per_portfolio]

CREATE TABLE [dbo].[cost_per_portfolio](
	[cpp_id] [int] IDENTITY(1,1) NOT NULL,
	[cpp_portfolio] [varchar](50) NULL,
	[cpp_year] [varchar](100) NULL,
	[cpp_fte] [numeric](10, 2) NULL,
	[cpp_cost] [numeric](10, 2) NULL,
	[cpp_percentage] [numeric](10, 2) NULL,
	[cpp_salary] [numeric](10, 2) NULL,
	[cpp_superannuation] [numeric](10, 2) NULL,
	[cpp_bonus] [numeric](10, 2) NULL,
	[cpp_others] [numeric](10, 2) NULL,
	[cpp_total] [numeric](10, 2) NULL,
	[cpp_portfolio_cost] [numeric](10, 2) NULL,
) ON [PRIMARY]
GO



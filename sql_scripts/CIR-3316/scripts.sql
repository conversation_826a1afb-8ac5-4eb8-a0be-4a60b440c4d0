use Compton_Green;
CREATE VIEW [dot_property_group] AS
select pp.pmpr_prop,
pp.pmpr_prop_group,
pp.pmpr_name,
pp.pmpr_postcode,
pp.pmpr_city,
pp.pmpr_street,
pp.pmpr_state,
pl.pmle_name,
pl.pmle_street,
pl.pmle_city,
pl.pmle_state,
pl.pmle_postcode,
pl.pmle_lease,
pl.pmle_status,
pl.pmle_status_description,
pl.pmle_com_dt as leaseCommencementDate,
pl.pmle_exp_dt as leaseExpireDate,
pua.pmua_to_dt as unitLeaseExpiryDate,
pua.pmua_from_dt as unitLeaseStartDate,
pua.pmua_unit,
pua.pmua_status,
pu.pmpu_unit,
pu.pmpu_desc
from pmpr_property pp
left join pmua_unit_area pua on pp.pmpr_prop = pua.pmua_prop 
left join pmpu_p_unit pu on pua.pmua_prop = pu.pmpu_prop AND  pua.pmua_unit = pu.pmpu_unit
left join pmle_lease pl on pl.pmle_prop = pp.pmpr_prop and pl.pmle_lease = pua.pmua_lease
<?php

$accounts_Total_monthly_income_received  = array(
    1003,
    1001,
    1034,
    1403,
    3501,
    3503,
    3511,
    3562,
    3603,
    3551,
    3625,
    1401,
    1530,
    10030,
    3502,
    3622,
    10062,
    10063,
    10069
    );

$accounts_Rent_Free_Compensation_Waiver  = array(1034);

$accounts_Monthly_expenses_General_Repairs_Maintenance  = array(
    6595,
    6603,
    6614,
    6617,
    6622,
    6627,
    6643,
    6644,
    6636,
    6653,
    5595,
    5603,
    5614,
    5617,
    5643,
    5644,
    5636,
    5653,
    4595,
    4603,
    4614,
    4617,
    4622,
    4627,
    4628,
    4643,
    4644,
    4636,
    4653
    );

$accounts_Monthly_expenses_Electrical = array(
    6615,
    6625,
    6615,
    6625,
    6615,
    6625,
    5615,
    5625,
    5615,
    5625,
    5615,
    5625,
    4615,
    4625,
    4615,
    4625,
    4615,
    4625
    );

$accounts_Monthly_expenses_Plumbing = array(
    6629,
    6635,
    6531,
    6604,
    5629,
    5635,
    5531,
    5604,
    4629,
    4635,
    4531,
    4604
    );

$accounts_Monthly_expenses_Gardening_Cleaning = array(
    6581,
    6551,
    6552,
    6553,
    6554,
    6555,
    6556,
    6557,
    5581,
    5551,
    5552,
    5553,
    5554,
    5555,
    5556,
    5557,
    4581,
    4582,
    4551,
    4552,
    4553,
    4554,
    4555,
    4556,
    4557
    );

$accounts_Monthly_expenses_Security = array(
    5651,
    6651,
    4651,
    4684
);

$accounts_Monthly_expenses_Council_rates = array(
    6501,
    5501,
    4501
    );

$accounts_SRO_Land_Tax = array(
    6502,
    5502,
    4502
);

$accounts_Monthly_expenses_Water_rates = array(
    6503,
    6511,
    6512,
    5503,
    5511,
    5512,
    4503,
    4511,
    4512
    );

$accounts_Owners_Corporation_Strata_Fees = array(
    6572,
    6661,
    6662,
    6201,
    6202,
    6204,
    6562,
    6561,
    6571,
    6705,
    5572,
    5661,
    5662,
    5201,
    5202,
    5204,
    5561,
    5591,
    5571,
    4572,
    4661,
    4662,
    4201,
    4202,
    4204,
    4560,
    4561,
    4591,
    4571,
    4300
    );

$accounts_Inspection_Fees = array(4682);

$accounts_Management_Fees = array(
    6671,
    6672,
    6695,
    5671,
    5672,
    5695,
    4671,
    4672,
    4695
    );

$accounts_Other_Miscellanious_Charges_and_Fees = array(
    6513,
    6521,
    6522,
    6523,
    6524,
    6563,
    6564,
    6101,
    6000,
    6681,
    6683,
    6687,
    6688,
    6689,
    6692,
    6693,
    6694,
    6701,
    6702,
    6703,
    6704,
    6707,
    6708,
    6726,
    6732,
    5513,
    5521,
    5522,
    5523,
    5524,
    5562,
    5563,
    5304,
    5673,
    5681,
    5687,
    5688,
    5689,
    5692,
    5693,
    5694,
    5701,
    5702,
    5703,
    5704,
    5707,
    5708,
    5726,
    5732,
    4513,
    4521,
    4522,
    4523,
    4524,
    4562,
    4563,
    4101,
    4305,
    4670,
    4673,
    4680,
    4681,
    4683,
    4687,
    4688,
    4689,
    4692,
    4693,
    4694,
    4700,
    4701,
    4702,
    4703,
    4704,
    4705,
    4707,
    4708,
    4726,
    4732
    );

$fromDate = '01/07/2023';
$toDate = '30/06/2024';

$fromPeriod = 1;
$toPeriod = 12;
$year = 2024;

//AND pmxc_acc IN (
//    ". implode(",", $accounts_Total_monthly_income_received) .",
//    ". implode(",", $accounts_Rent_Free_Compensation_Waiver) .",
//    ". implode(",", $accounts_Monthly_expenses_General_Repairs_Maintenance) .",
//    ". implode(",", $accounts_Monthly_expenses_Electrical) .",
//    ". implode(",", $accounts_Monthly_expenses_Plumbing) .",
//    ". implode(",", $accounts_Monthly_expenses_Gardening_Cleaning) .",
//    ". implode(",", $accounts_Monthly_expenses_Security) .",
//    ". implode(",", $accounts_Monthly_expenses_Council_rates) .",
//    ". implode(",", $accounts_SRO_Land_Tax) .",
//    ". implode(",", $accounts_Monthly_expenses_Water_rates) .",
//    ". implode(",", $accounts_Owners_Corporation_Strata_Fees) .",
//    ". implode(",", $accounts_Inspection_Fees) .",
//    ". implode(",", $accounts_Management_Fees) .",
//    ". implode(",", $accounts_Other_Miscellanious_Charges_and_Fees) ."
//)

$gstSQL = "SELECT property, SUM(tax_total) as tax_total FROM 
(SELECT pmxc_prop as property, SUM(pmxc_tax_amt) as tax_total
FROM pmxc_ap_alloc 
WHERE pmxc_period >= $fromPeriod AND pmxc_period <= $toPeriod AND pmxc_year = $year AND pmxc_f_type = 'PAY'
GROUP BY pmxc_prop
UNION ALL
SELECT pmxd_prop as property, SUM(pmxd_tax_amt) as tax_total
FROM pmxd_ar_alloc 
WHERE pmxd_period >= $fromPeriod AND pmxd_period <= $toPeriod AND pmxd_year = $year AND pmxd_f_type = 'CSH'
GROUP BY pmxd_prop) x
GROUP BY property";

$paymentsToOwnerSQL = "SELECT pmxc_prop, SUM(pmxc_alloc_amt) as amount, SUM(pmxc_tax_amt) as tax_amount
		FROM pmxc_ap_alloc
		WHERE (pmxc_acc IN
                          (SELECT pmcg_acc
                            FROM pmcg_chart_grp
                            WHERE (pmcg_grp = 'TRACC3')
                            AND (pmcg_subgrp = 'BSPMTREMI')))
                AND (pmxc_period >= $fromPeriod AND pmxc_period <= $toPeriod AND pmxc_year = $year)
                AND (pmxc_f_type = 'PAY')
		GROUP BY pmxc_prop";

$leaseSQL = "SELECT 
	ROW_NUMBER() OVER (PARTITION BY pmua_prop, pmua_unit ORDER BY pmua_to_dt DESC) AS rn,
	pmua_prop, pmua_unit, pmua_from_dt, pmua_to_dt,
	pmle_lease, pmle_name as tenant_name, pmle_com_dt as lease_start_date, pmle_exp_dt as lease_expiry_date, 
	CASE WHEN pmle_exp_dt >= CONVERT(datetime, '$toDate', 103) THEN 'Leased' 
		WHEN pmle_exp_dt < CONVERT(datetime, '$toDate', 103) THEN 'Overholding' 
		ELSE 'Vacant'
	END as lease_status,

	CASE WHEN pmua_status = 'V' THEN DATEADD(DAY,-1,pmua_from_dt)
		WHEN pmua_to_dt = '2999-12-31' THEN NULL
		ELSE NULL
	END as vacate_date,

	CASE WHEN pmua_status = 'V' THEN DATEDIFF(DAY, DATEADD(DAY,-1,pmua_from_dt), CONVERT(datetime, '$toDate', 103))
		WHEN pmua_to_dt = '2999-12-31' THEN NULL
		ELSE NULL
	END as total_days_vacant  

FROM pmua_unit_area
LEFT JOIN pmle_lease
	ON pmua_prop = pmle_prop
	AND pmua_lease = pmle_lease";

//$arrearsSQL = "SELECT property_code, debtor_code, SUM(outstanding_gross) as arrears_amount
//  FROM outstanding_ar
//  GROUP BY property_code, debtor_code";
$arrearsSQL = "SELECT PropertyCode, LeaseCode, SUM(OutstandingGross) as arrears_amount FROM FN_get_outstanding_AR_active_properties_all_leases (
   CONVERT(datetime, '$toDate', 103)
  ,CONVERT(datetime, '$toDate', 103))
GROUP BY PropertyCode, LeaseCode";

$fyIncomeSQL = $fyExpenseSQL = array();

for($i=2018; $i<=2027; $i++)
{
    $fyIncomeSQL[$i] = "SELECT propertyID, SUM(balanceCash) as year_income FROM gl_trial_balance
            WHERE accountID IN (". implode(",", $accounts_Total_monthly_income_received) .")
            AND year = $i
            GROUP by propertyID";

    $fyExpenseSQL[$i] = "SELECT propertyID,  SUM(balanceCash) as year_expense FROM gl_trial_balance
            WHERE accountID IN (
                ". implode(",", $accounts_Rent_Free_Compensation_Waiver) .",
                ". implode(",", $accounts_Monthly_expenses_General_Repairs_Maintenance) .",
                ". implode(",", $accounts_Monthly_expenses_Electrical) .",
                ". implode(",", $accounts_Monthly_expenses_Plumbing) .",
                ". implode(",", $accounts_Monthly_expenses_Gardening_Cleaning) .",
                ". implode(",", $accounts_Monthly_expenses_Security) .",
                ". implode(",", $accounts_Monthly_expenses_Council_rates) .",
                ". implode(",", $accounts_SRO_Land_Tax) .",
                ". implode(",", $accounts_Monthly_expenses_Water_rates) .",
                ". implode(",", $accounts_Owners_Corporation_Strata_Fees) .",
                ". implode(",", $accounts_Inspection_Fees) .",
                ". implode(",", $accounts_Management_Fees) .",
                ". implode(",", $accounts_Other_Miscellanious_Charges_and_Fees) ."
                                )
            AND year = $i
            GROUP by propertyID";
}

//echo $fyIncomeSQL .'<br><br>';



$sql = "select 
prop.pmpr_prop_group,
prop.pmpr_prop,
prop.pmpr_street,
unit.pmpu_unit,
prop.pmpr_city,
prop.pmpr_postcode,

SUM(CASE WHEN trial_balance_total.accountID IN (". implode(",", $accounts_Total_monthly_income_received) .")
    THEN trial_balance_total.total
    ELSE 0 END) Total_monthly_income_received,

SUM(CASE WHEN trial_balance_total.accountID IN (". implode(",", $accounts_Rent_Free_Compensation_Waiver) .")
    THEN trial_balance_total.total
    ELSE 0 END) Rent_Free_Compensation_Waiver,

SUM(CASE WHEN trial_balance_total.accountID IN (". implode(",", $accounts_Monthly_expenses_General_Repairs_Maintenance) .")
    THEN trial_balance_total.total
    ELSE 0 END) Monthly_expenses_General_Repairs_Maintenance,

SUM(CASE WHEN trial_balance_total.accountID IN (". implode(",", $accounts_Monthly_expenses_Electrical) .")
    THEN trial_balance_total.total
    ELSE 0 END) Monthly_expenses_Electrical,

SUM(CASE WHEN trial_balance_total.accountID IN (". implode(",", $accounts_Monthly_expenses_Plumbing) .")
    THEN trial_balance_total.total
    ELSE 0 END) Monthly_expenses_Plumbing,

SUM(CASE WHEN trial_balance_total.accountID IN (". implode(",", $accounts_Monthly_expenses_Gardening_Cleaning) .")
    THEN trial_balance_total.total
    ELSE 0 END) Monthly_expenses_Gardening_Cleaning,

SUM(CASE WHEN trial_balance_total.accountID IN (". implode(",", $accounts_Monthly_expenses_Security) .")
    THEN trial_balance_total.total
    ELSE 0 END) Monthly_expenses_Security,

SUM(CASE WHEN trial_balance_total.accountID IN (". implode(",", $accounts_Monthly_expenses_Council_rates) .")
    THEN trial_balance_total.total
    ELSE 0 END) Monthly_expenses_Council_rates,

SUM(CASE WHEN trial_balance_total.accountID IN (". implode(",", $accounts_SRO_Land_Tax) .")
    THEN trial_balance_total.total
    ELSE 0 END) SRO_Land_Tax,

SUM(CASE WHEN trial_balance_total.accountID IN (". implode(",", $accounts_Monthly_expenses_Water_rates) .")
    THEN trial_balance_total.total
    ELSE 0 END) Monthly_expenses_Water_rates,

SUM(CASE WHEN trial_balance_total.accountID IN (". implode(",", $accounts_Owners_Corporation_Strata_Fees) .")
    THEN trial_balance_total.total
    ELSE 0 END) Owners_Corporation_Strata_Fees,

SUM(CASE WHEN trial_balance_total.accountID IN (". implode(",", $accounts_Inspection_Fees) .")
    THEN trial_balance_total.total
    ELSE 0 END) Inspection_Fees,

SUM(CASE WHEN trial_balance_total.accountID IN (". implode(",", $accounts_Management_Fees) .")
    THEN trial_balance_total.total
    ELSE 0 END) Management_Fees,

SUM(CASE WHEN trial_balance_total.accountID IN (". implode(",", $accounts_Other_Miscellanious_Charges_and_Fees) .")
    THEN trial_balance_total.total
    ELSE 0 END) Other_Miscellanious_Charges_and_Fees,
    
gst_totals.tax_total as GST_Paid_Received,

payments_to_owner.amount as Total_Paid_To_DTP,

COALESCE(tenant_name,'') as tenant_name,

arrears.arrears_amount as current_arrears_balance,

lease_start_date,
lease_expiry_date,
lease_status,
vacate_date,
total_days_vacant";

for($i=2018; $i<=2027; $i++)
{
    $sql .= ", fyIncome$i.year_income income_$i 
            , fyExpense$i.year_expense expense_$i";
}

$sql .= " FROM pmpr_property prop
JOIN pmpu_p_unit unit
	ON unit.pmpu_prop = prop.pmpr_prop

LEFT JOIN (SELECT propertyID, accountID, SUM(balanceCash) total
    FROM gl_trial_balance 
    WHERE period >= $fromPeriod AND period <= $toPeriod AND year = $year
    GROUP BY propertyID, accountID) as trial_balance_total
	ON trial_balance_total.propertyID = prop.pmpr_prop
	
LEFT JOIN ($gstSQL) as gst_totals
    ON gst_totals.property = prop.pmpr_prop
    
LEFT JOIN ($paymentsToOwnerSQL) as payments_to_owner
    ON  payments_to_owner.pmxc_prop = prop.pmpr_prop

LEFT JOIN ($leaseSQL) as lease
    ON lease.pmua_prop = prop.pmpr_prop
    AND lease.pmua_unit = unit.pmpu_unit
    AND lease.rn = 1

LEFT JOIN ($arrearsSQL) as arrears
    ON arrears.PropertyCode = prop.pmpr_prop
    AND arrears.LeaseCode = lease.pmle_lease
";

for($i=2018; $i<=2027; $i++)
{
    $sql .= " LEFT JOIN ($fyIncomeSQL[$i]) as fyIncome$i
                ON fyIncome$i.propertyID = prop.pmpr_prop
     
            LEFT JOIN ($fyExpenseSQL[$i]) as fyExpense$i
                ON fyExpense$i.propertyID = prop.pmpr_prop";
}
//LEFT JOIN ($fyExpenseSQL) as fyExpense
//    ON fyIncome.propertyID = prop.pmpr_prop
//

//$sql .= " WHERE pmpr_prop IN ('ALBE10-01', 'BORR16', 'ALBE42-02 ')";

$sql .= " GROUP BY 
    prop.pmpr_prop_group,
    prop.pmpr_prop,
    prop.pmpr_street,
    unit.pmpu_unit,
    prop.pmpr_city,
    prop.pmpr_postcode,
    gst_totals.tax_total,
    payments_to_owner.amount,
    tenant_name,
    arrears.arrears_amount,
    lease_start_date,
    lease_expiry_date,
    lease_status,
    vacate_date,
    total_days_vacant
";

for($i=2018; $i<=2027; $i++)
{
    $sql .= ",fyIncome$i.year_income ,fyExpense$i.year_expense";
}

$sql .= " ORDER BY prop.pmpr_prop_group, prop.pmpr_prop";

//echo $sql; // Project Level


// ----------------------------------------------------------------------------------------------------------------
//Finance team

//Total of cash Banked - Revenue Account Adjustment
$sql = "SELECT pmpr_prop_group, SUM(CASE WHEN pmxd_period = 1 THEN pmxd_alloc_amt-pmxd_tax_amt ELSE 0 END)*-1 as jul2023
,SUM(CASE WHEN pmxd_period = 2 THEN pmxd_alloc_amt-pmxd_tax_amt ELSE 0 END)*-1 as aug2023
,SUM(CASE WHEN pmxd_period = 3 THEN pmxd_alloc_amt-pmxd_tax_amt ELSE 0 END)*-1 as sep2023
,SUM(CASE WHEN pmxd_period = 4 THEN pmxd_alloc_amt-pmxd_tax_amt ELSE 0 END)*-1 as oct2023
,SUM(CASE WHEN pmxd_period = 5 THEN pmxd_alloc_amt-pmxd_tax_amt ELSE 0 END)*-1 as nov2023
,SUM(CASE WHEN pmxd_period = 6 THEN pmxd_alloc_amt-pmxd_tax_amt ELSE 0 END)*-1 as dec2023
,SUM(CASE WHEN pmxd_period = 7 THEN pmxd_alloc_amt-pmxd_tax_amt ELSE 0 END)*-1 as jan2024
,SUM(CASE WHEN pmxd_period = 8 THEN pmxd_alloc_amt-pmxd_tax_amt ELSE 0 END)*-1 as feb2024
,SUM(CASE WHEN pmxd_period = 9 THEN pmxd_alloc_amt-pmxd_tax_amt ELSE 0 END)*-1 as mar2024
,SUM(CASE WHEN pmxd_period = 11 THEN pmxd_alloc_amt-pmxd_tax_amt ELSE 0 END)*-1 as apr2024
,SUM(CASE WHEN pmxd_period = 11 THEN pmxd_alloc_amt-pmxd_tax_amt ELSE 0 END)*-1 as may2024
,SUM(CASE WHEN pmxd_period = 12 THEN pmxd_alloc_amt-pmxd_tax_amt ELSE 0 END)*-1 as jun2024
FROM pmpr_property 
JOIN pmxd_ar_alloc on pmxd_prop = pmpr_prop AND pmxd_f_type = 'CSH'
WHERE pmxd_year = $year
GROUP BY pmpr_prop_group
ORDER BY pmpr_prop_group";
//echo $sql;
//--AND pmpr_prop_group = 'PLX' for checking single group


//Total of water rates - To Recognise Property Water Rates for properties
$sql = "SELECT pmpr_prop_group, SUM(CASE WHEN pmxc_period = 1 THEN pmxc_alloc_amt+pmxc_tax_amt ELSE 0 END)*-1  as jul2023
,SUM(CASE WHEN pmxc_period = 2 THEN pmxc_alloc_amt+pmxc_tax_amt ELSE 0 END)*-1 as aug2023
,SUM(CASE WHEN pmxc_period = 3 THEN pmxc_alloc_amt+pmxc_tax_amt ELSE 0 END)*-1 as sep2023
,SUM(CASE WHEN pmxc_period = 4 THEN pmxc_alloc_amt+pmxc_tax_amt ELSE 0 END)*-1 as oct2023
,SUM(CASE WHEN pmxc_period = 5 THEN pmxc_alloc_amt+pmxc_tax_amt ELSE 0 END)*-1 as nov2023
,SUM(CASE WHEN pmxc_period = 6 THEN pmxc_alloc_amt+pmxc_tax_amt ELSE 0 END)*-1 as dec2023
,SUM(CASE WHEN pmxc_period = 7 THEN pmxc_alloc_amt+pmxc_tax_amt ELSE 0 END)*-1 as jan2024
,SUM(CASE WHEN pmxc_period = 8 THEN pmxc_alloc_amt+pmxc_tax_amt ELSE 0 END)*-1 as feb2024
,SUM(CASE WHEN pmxc_period = 9 THEN pmxc_alloc_amt+pmxc_tax_amt ELSE 0 END)*-1 as mar2024
,SUM(CASE WHEN pmxc_period = 11 THEN pmxc_alloc_amt+pmxc_tax_amt ELSE 0 END)*-1 as apr2024
,SUM(CASE WHEN pmxc_period = 11 THEN pmxc_alloc_amt+pmxc_tax_amt ELSE 0 END)*-1 as may2024
,SUM(CASE WHEN pmxc_period = 12 THEN pmxc_alloc_amt+pmxc_tax_amt ELSE 0 END)*-1 as jun2024
FROM pmpr_property 
JOIN pmxc_ap_alloc 
    ON pmxc_prop = pmpr_prop
    AND pmxc_acc IN (". implode(",", $accounts_Monthly_expenses_Water_rates) .")
WHERE pmxc_year = $year
GROUP BY pmpr_prop_group
ORDER BY pmpr_prop_group";
//echo $sql;

//total of council rates - To Recognise Property Council Rates for properties
$sql = "SELECT pmpr_prop_group, SUM(CASE WHEN pmxc_period = 1 THEN pmxc_alloc_amt+pmxc_tax_amt ELSE 0 END)*-1  as jul2023
,SUM(CASE WHEN pmxc_period = 2 THEN pmxc_alloc_amt+pmxc_tax_amt ELSE 0 END)*-1 as aug2023
,SUM(CASE WHEN pmxc_period = 3 THEN pmxc_alloc_amt+pmxc_tax_amt ELSE 0 END)*-1 as sep2023
,SUM(CASE WHEN pmxc_period = 4 THEN pmxc_alloc_amt+pmxc_tax_amt ELSE 0 END)*-1 as oct2023
,SUM(CASE WHEN pmxc_period = 5 THEN pmxc_alloc_amt+pmxc_tax_amt ELSE 0 END)*-1 as nov2023
,SUM(CASE WHEN pmxc_period = 6 THEN pmxc_alloc_amt+pmxc_tax_amt ELSE 0 END)*-1 as dec2023
,SUM(CASE WHEN pmxc_period = 7 THEN pmxc_alloc_amt+pmxc_tax_amt ELSE 0 END)*-1 as jan2024
,SUM(CASE WHEN pmxc_period = 8 THEN pmxc_alloc_amt+pmxc_tax_amt ELSE 0 END)*-1 as feb2024
,SUM(CASE WHEN pmxc_period = 9 THEN pmxc_alloc_amt+pmxc_tax_amt ELSE 0 END)*-1 as mar2024
,SUM(CASE WHEN pmxc_period = 11 THEN pmxc_alloc_amt+pmxc_tax_amt ELSE 0 END)*-1 as apr2024
,SUM(CASE WHEN pmxc_period = 11 THEN pmxc_alloc_amt+pmxc_tax_amt ELSE 0 END)*-1 as may2024
,SUM(CASE WHEN pmxc_period = 12 THEN pmxc_alloc_amt+pmxc_tax_amt ELSE 0 END)*-1 as jun2024
FROM pmpr_property 
JOIN pmxc_ap_alloc 
    ON pmxc_prop = pmpr_prop
    AND pmxc_acc IN (". implode(",", $accounts_Monthly_expenses_Council_rates) .")
WHERE pmxc_year = $year
GROUP BY pmpr_prop_group
ORDER BY pmpr_prop_group";
//echo $sql;

//total of maintenance columns L, M, N, O & P, T - To Recognise Property Maintenance Costs for properties
$sql = "SELECT pmpr_prop_group, SUM(CASE WHEN pmxc_period = 1 THEN pmxc_alloc_amt+pmxc_tax_amt ELSE 0 END)*-1  as jul2023
,SUM(CASE WHEN pmxc_period = 2 THEN pmxc_alloc_amt+pmxc_tax_amt ELSE 0 END)*-1 as aug2023
,SUM(CASE WHEN pmxc_period = 3 THEN pmxc_alloc_amt+pmxc_tax_amt ELSE 0 END)*-1 as sep2023
,SUM(CASE WHEN pmxc_period = 4 THEN pmxc_alloc_amt+pmxc_tax_amt ELSE 0 END)*-1 as oct2023
,SUM(CASE WHEN pmxc_period = 5 THEN pmxc_alloc_amt+pmxc_tax_amt ELSE 0 END)*-1 as nov2023
,SUM(CASE WHEN pmxc_period = 6 THEN pmxc_alloc_amt+pmxc_tax_amt ELSE 0 END)*-1 as dec2023
,SUM(CASE WHEN pmxc_period = 7 THEN pmxc_alloc_amt+pmxc_tax_amt ELSE 0 END)*-1 as jan2024
,SUM(CASE WHEN pmxc_period = 8 THEN pmxc_alloc_amt+pmxc_tax_amt ELSE 0 END)*-1 as feb2024
,SUM(CASE WHEN pmxc_period = 9 THEN pmxc_alloc_amt+pmxc_tax_amt ELSE 0 END)*-1 as mar2024
,SUM(CASE WHEN pmxc_period = 11 THEN pmxc_alloc_amt+pmxc_tax_amt ELSE 0 END)*-1 as apr2024
,SUM(CASE WHEN pmxc_period = 11 THEN pmxc_alloc_amt+pmxc_tax_amt ELSE 0 END)*-1 as may2024
,SUM(CASE WHEN pmxc_period = 12 THEN pmxc_alloc_amt+pmxc_tax_amt ELSE 0 END)*-1 as jun2024
FROM pmpr_property 
JOIN pmxc_ap_alloc 
    ON pmxc_prop = pmpr_prop
    AND pmxc_acc IN (
        ". implode(",", $accounts_Monthly_expenses_General_Repairs_Maintenance) ."
        ,". implode(",", $accounts_Monthly_expenses_Electrical) ."
        ,". implode(",", $accounts_Monthly_expenses_Plumbing) ."
        ,". implode(",", $accounts_Monthly_expenses_Gardening_Cleaning) ."
        ,". implode(",", $accounts_Monthly_expenses_Security) ."
        ,". implode(",", $accounts_Owners_Corporation_Strata_Fees) ."
        )
WHERE pmxc_year = $year
GROUP BY pmpr_prop_group
ORDER BY pmpr_prop_group";
//echo $sql;


// gst PAID on the above expenses  - GST Payable
$sql = "SELECT pmpr_prop_group, SUM(CASE WHEN pmxc_period = 1 THEN pmxc_tax_amt ELSE 0 END)  as jul2023
,SUM(CASE WHEN pmxc_period = 2 THEN pmxc_tax_amt ELSE 0 END) as aug2023
,SUM(CASE WHEN pmxc_period = 3 THEN pmxc_tax_amt ELSE 0 END) as sep2023
,SUM(CASE WHEN pmxc_period = 4 THEN pmxc_tax_amt ELSE 0 END) as oct2023
,SUM(CASE WHEN pmxc_period = 5 THEN pmxc_tax_amt ELSE 0 END) as nov2023
,SUM(CASE WHEN pmxc_period = 6 THEN pmxc_tax_amt ELSE 0 END) as dec2023
,SUM(CASE WHEN pmxc_period = 7 THEN pmxc_tax_amt ELSE 0 END) as jan2024
,SUM(CASE WHEN pmxc_period = 8 THEN pmxc_tax_amt ELSE 0 END) as feb2024
,SUM(CASE WHEN pmxc_period = 9 THEN pmxc_tax_amt ELSE 0 END) as mar2024
,SUM(CASE WHEN pmxc_period = 11 THEN pmxc_tax_amt ELSE 0 END) as apr2024
,SUM(CASE WHEN pmxc_period = 11 THEN pmxc_tax_amt ELSE 0 END) as may2024
,SUM(CASE WHEN pmxc_period = 12 THEN pmxc_tax_amt ELSE 0 END) as jun2024
FROM pmpr_property 
JOIN pmxc_ap_alloc 
    ON pmxc_prop = pmpr_prop
    AND pmxc_acc IN (
        ". implode(",", $accounts_Monthly_expenses_General_Repairs_Maintenance) ."
        ,". implode(",", $accounts_Monthly_expenses_Electrical) ."
        ,". implode(",", $accounts_Monthly_expenses_Plumbing) ."
        ,". implode(",", $accounts_Monthly_expenses_Gardening_Cleaning) ."
        ,". implode(",", $accounts_Monthly_expenses_Security) ."
        ,". implode(",", $accounts_Owners_Corporation_Strata_Fees) ."
        ,". implode(",", $accounts_Monthly_expenses_Council_rates) ."
        ,". implode(",", $accounts_Monthly_expenses_Water_rates) ."
        )
WHERE pmxc_year = $year
GROUP BY pmpr_prop_group
ORDER BY pmpr_prop_group";
//echo $sql;

//GST DR Income - GST Recoveries
$sql = "SELECT pmpr_prop_group, SUM(CASE WHEN pmxd_period = 1 THEN pmxd_tax_amt ELSE 0 END)*-1  as jul2023
,SUM(CASE WHEN pmxd_period = 2 THEN pmxd_tax_amt ELSE 0 END)*-1  as aug2023
,SUM(CASE WHEN pmxd_period = 3 THEN pmxd_tax_amt ELSE 0 END)*-1  as sep2023
,SUM(CASE WHEN pmxd_period = 4 THEN pmxd_tax_amt ELSE 0 END)*-1  as oct2023
,SUM(CASE WHEN pmxd_period = 5 THEN pmxd_tax_amt ELSE 0 END)*-1  as nov2023
,SUM(CASE WHEN pmxd_period = 6 THEN pmxd_tax_amt ELSE 0 END)*-1  as dec2023
,SUM(CASE WHEN pmxd_period = 7 THEN pmxd_tax_amt ELSE 0 END)*-1  as jan2024
,SUM(CASE WHEN pmxd_period = 8 THEN pmxd_tax_amt ELSE 0 END)*-1  as feb2024
,SUM(CASE WHEN pmxd_period = 9 THEN pmxd_tax_amt ELSE 0 END)*-1  as mar2024
,SUM(CASE WHEN pmxd_period = 11 THEN pmxd_tax_amt ELSE 0 END)*-1  as apr2024
,SUM(CASE WHEN pmxd_period = 11 THEN pmxd_tax_amt ELSE 0 END)*-1  as may2024
,SUM(CASE WHEN pmxd_period = 12 THEN pmxd_tax_amt ELSE 0 END)*-1  as jun2024
FROM pmpr_property 
JOIN pmxd_ar_alloc on pmxd_prop = pmpr_prop AND pmxd_f_type = 'CSH'
WHERE pmxd_year = $year
GROUP BY pmpr_prop_group
ORDER BY pmpr_prop_group";
//echo $sql;
    DROP VIEW IF EXISTS agency_fees

    CREATE VIEW agency_fees
        AS
        SELECT
                pmxc_ap_alloc.pmxc_prop as propertyID,
                pmcm_mast_cal.pmcm_year as calYear,
                pmcm_mast_cal.pmcm_period as calPeriod ,
                SUM((ISNULL(pmxc_ap_alloc.pmxc_alloc_amt,0) * -1) / (1 + ISNULL(pmgs_rate,0)/100))  as netAmt,
                ISNULL(pmca_chart.pmca_fees,0) as fees,
                pmca_chart.pmca_code AS feesCode,
                pmca_chart.pmca_name AS feesName     
        	FROM pmxc_ap_alloc pmxc_ap_alloc
            LEFT JOIN pmco_company pmco_company ON
                pmxc_ap_alloc.pmxc_s_creditor = pmco_company.pmco_code
            LEFT JOIN pmca_chart pmca_chart ON
                pmxc_ap_alloc.pmxc_acc = pmca_chart.pmca_code
            LEFT JOIN pmcm_mast_cal ON
                pmxc_alloc_dt BETWEEN pmcm_mast_cal.pmcm_start_dt AND pmcm_mast_cal.pmcm_end_dt
                AND pmcm_mast_cal.pmcm_code = 'Financial'
        	LEFT JOIN pmgs_gst
                    ON pmgs_gst.pmgs_code = pmco_company.pmco_gst_code
                	AND pmxc_alloc_dt >= pmgs_from_dt AND pmxc_alloc_dt <= pmgs_to_dt
            WHERE (
                    (pmxc_ap_alloc.pmxc_f_type='PAY')
                     AND (pmco_company.pmco_s_agent=1)
                )
            GROUP BY
                pmxc_ap_alloc.pmxc_prop,
                pmcm_mast_cal.pmcm_year,
                pmcm_mast_cal.pmcm_period,
                pmca_chart.pmca_fees,
                pmca_chart.pmca_code,
                pmca_chart.pmca_name
        
--        SELECT
--            pmxc_ap_alloc.pmxc_prop as propertyID,
--            pmxc_ap_alloc.pmxc_year as calYear,
--            pmxc_ap_alloc.pmxc_period as calPeriod,
--            SUM((ISNULL(pmxc_ap_alloc.pmxc_alloc_amt,0) * -1) / (1 + ISNULL(pmgs_rate,0)/100))  as netAmt,
--            ISNULL(pmca_chart.pmca_fees,0) as fees,
--            pmca_chart.pmca_code AS feesCode,
--            pmca_chart.pmca_name AS feesName
--
--        FROM pmxc_ap_alloc pmxc_ap_alloc
--        LEFT JOIN pmco_company pmco_company ON
--            pmxc_ap_alloc.pmxc_s_creditor = pmco_company.pmco_code
--        LEFT JOIN pmca_chart pmca_chart ON
--            pmxc_ap_alloc.pmxc_acc = pmca_chart.pmca_code
--        LEFT JOIN pmgs_gst
--            ON pmgs_gst.pmgs_code = pmco_company.pmco_gst_code
--        	AND pmxc_alloc_dt >= pmgs_from_dt AND pmxc_alloc_dt <= pmgs_to_dt
--        WHERE (
--                (pmxc_ap_alloc.pmxc_f_type='PAY')
--                 AND (pmco_company.pmco_s_agent=1)
--            )
--        GROUP BY
--            pmxc_ap_alloc.pmxc_prop,
--            pmxc_ap_alloc.pmxc_year,
--            pmxc_ap_alloc.pmxc_period,
--            pmca_chart.pmca_fees,
--            pmca_chart.pmca_code,
--            pmca_chart.pmca_name
--    GO


DROP VIEW IF EXISTS agency_fees_budget

CREATE VIEW agency_fees_budget
AS
SELECT
    pmep_prop as propertyID,
    pmcm_mast_cal.pmcm_year as calYear,
    pmcm_mast_cal.pmcm_period as calPeriod,
    pmep_b_c_amt as budgetAmt,
    pmep_exp_acc as budgetAccount
FROM pmep_b_exp_per pmep_b_exp_per
LEFT JOIN pmpr_property pmpr_property
    ON pmep_b_exp_per.pmep_prop = pmpr_property.pmpr_prop
LEFT JOIN pmca_chart pmca_chart
    ON pmep_b_exp_per.pmep_exp_acc = pmca_chart.pmca_code
LEFT JOIN pmcp_prop_cal pmcp_prop_cal
    ON pmcp_prop_cal.pmcp_prop = pmep_prop
    AND pmcp_prop_cal.pmcp_year = pmep_year
    AND pmcp_prop_cal.pmcp_period = pmep_per
LEFT JOIN pmcm_mast_cal pmcm_mast_cal
    ON pmcp_prop_cal.pmcp_end_dt BETWEEN pmcm_mast_cal.pmcm_start_dt AND pmcm_mast_cal.pmcm_end_dt
    AND pmcm_code = 'Financial'
WHERE pmca_chart.pmca_fees = 1

--SELECT
--	pmep_prop as propertyID,
--	pmep_b_exp_per.pmep_year as calYear,
--	pmep_b_exp_per.pmep_per as calPeriod,
--	pmep_b_c_amt as budgetAmt,
--	pmep_exp_acc as budgetAccount
--FROM pmep_b_exp_per pmep_b_exp_per
--LEFT JOIN pmpr_property pmpr_property
--	ON pmep_b_exp_per.pmep_prop = pmpr_property.pmpr_prop
--LEFT JOIN pmca_chart pmca_chart
--	ON pmep_b_exp_per.pmep_exp_acc = pmca_chart.pmca_code
--WHERE pmca_chart.pmca_fees = 1
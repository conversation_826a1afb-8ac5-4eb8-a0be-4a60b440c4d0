ALTER TABLE pmbh_b_head ADD pmbh_input_type INT DEFAULT NULL;
ALTER TABLE pmbh_b_head ADD pmbh_vo_balanced BIT DEFAULT 0;
ALTER TABLE pmbh_b_head ADD pmbh_man_fee_balanced BIT DEFAULT 0;
ALTER TABLE pmbh_b_head ADD pmbh_charges_balanced BIT DEFAULT 0;
ALTER TABLE pmbh_b_head ADD pmbh_modified_by NVARCHAR(255) DEFAULT NULL;

ALTER TABLE property_budget_activity_logs ADD input_type INT DEFAULT NULL;
UPDATE pmzz_param SET pmzz_desc = '2' WHERE pmzz_par_type = 'BUDGET' AND pmzz_code = 'VERSION'
    INSERT INTO pmzz_param(pmzz_par_type, pmzz_code, pmzz_desc) VALUES('BUDGET','LATESTNAME','BETA')
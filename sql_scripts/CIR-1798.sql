--CLIENT
CREATE TABLE [email_config_addl] (
  [id] INT NOT NULL IDENTITY PRIMARY KEY,
  [type] TINYINT NOT NULL DEFAULT (0),
  [send_from] VARCHAR(100) NULL DEFAULT NULL,
  [from_name] VA<PERSON><PERSON>R(100) NULL DEFAULT NULL,
  [reply_to] VARCHAR(100) NULL DEFAULT NULL,
  [status] TINYINT NOT NULL DEFAULT (0),
  [created_by] VARCHAR(100) NULL DEFAULT NULL,
  [modified_by] VARCHAR(100) NULL DEFAULT NULL,
  [created_at] DATETIME NOT NULL DEFAULT (GETDATE()),
  [modified_at] DATETIME NOT NULL DEFAULT (GETDATE()),
);

--NPMS
ALTER TABLE email_log
ADD sender VARCHAR(100) NULL DEFAULT NULL;
/**
*
* Run on ALL CLIENT DBs
* document_transactions_link
* document_transactions_link.document_id is linked to documents.documentID
*
**/

DROP TABLE document_transactions_link;
CREATE TABLE document_transactions_link
(
    id INT IDENTITY(1,1),
    type CHAR(12),
    header_id VARCHAR(15),
    line_id INT NULL,
    document_id INT
);

ALTER TABLE temp_ocr_ap ADD attachment_identifier VARCHAR(15) NULL; /* linked to document_transactions_link.header_id */

/** Run to remove temp_ocr_ap.attachment_id **/
ALTER TABLE temp_ocr_ap DROP COLUMN attachment_id;
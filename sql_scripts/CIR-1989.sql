USE npms;

ALTER TABLE scheduledTasks
ADD error_code varchar (50) NULL;

ALTER TABLE scheduledTasks2
ADD error_code varchar (50) NULL;

ALTER TABLE scheduledTasks3
ADD error_code varchar (50) NULL;

ALTER TABLE scheduledTasksLog
ADD error_code varchar (50) NULL;

ALTER TABLE scheduledTasksLog2
ADD error_code varchar (50) NULL;

ALTER TABLE scheduledTasksLog3
ADD error_code varchar (50) NULL;

CREATE TABLE [dbo].[scheduledTasksAllard](
  [queueID] [int] IDENTITY(1,1) NOT NULL,
  [databaseID] [int] NOT NULL,
  [type] [tinyint] NOT NULL,
  [command] [varchar](150) NOT NULL,
  [data] [text] NULL,
  [count] [tinyint] NOT NULL DEFAULT 0,
  [createdDate] [datetime] NOT NULL,
  [createdBy] [varchar](30) NULL,
  [lastAccessed] [datetime] NULL,
  [error_code][varchar](50) NULL
);

CREATE TABLE [dbo].[scheduledTasksLogAllard](
  [queueID] [int] NOT NULL,
  [databaseID] [int] NOT NULL,
  [type] [tinyint] NOT NULL,
  [command] [varchar](150) NOT NULL,
  [data] [text] NULL,
  [count] [tinyint] NOT NULL,
  [createdDate] [datetime] NOT NULL,
  [createdBy] [varchar](30) NULL,
  [lastAccessed] [datetime] NULL,
  [error_code][varchar](50) NULL
);

CREATE TABLE [dbo].[scheduledTasksBRMelbourne](
 [queueID] [int] IDENTITY(1,1) NOT NULL,
  [databaseID] [int] NOT NULL,
  [type] [tinyint] NOT NULL,
  [command] [varchar](150) NOT NULL,
  [data] [text] NULL,
  [count] [tinyint] NOT NULL DEFAULT 0,
  [createdDate] [datetime] NOT NULL,
  [createdBy] [varchar](30) NULL,
  [lastAccessed] [datetime] NULL,
  [error_code][varchar](50) NULL
);

CREATE TABLE [dbo].[scheduledTasksLogBRMelbourne](
  [queueID] [int] NOT NULL,
  [databaseID] [int] NOT NULL,
  [type] [tinyint] NOT NULL,
  [command] [varchar](150) NOT NULL,
  [data] [text] NULL,
  [count] [tinyint] NOT NULL,
  [createdDate] [datetime] NOT NULL,
  [createdBy] [varchar](30) NULL,
  [lastAccessed] [datetime] NULL,
  [error_code][varchar](50) NULL
);

CREATE TABLE [dbo].[scheduledTasksBRWA](
  [queueID] [int] IDENTITY(1,1) NOT NULL,
  [databaseID] [int] NOT NULL,
  [type] [tinyint] NOT NULL,
  [command] [varchar](150) NOT NULL,
  [data] [text] NULL,
  [count] [tinyint] NOT NULL DEFAULT 0,
  [createdDate] [datetime] NOT NULL,
  [createdBy] [varchar](30) NULL,
  [lastAccessed] [datetime] NULL,
  [error_code][varchar](50) NULL
);

CREATE TABLE [dbo].[scheduledTasksLogBRWA](
  [queueID] [int] NOT NULL,
  [databaseID] [int] NOT NULL,
  [type] [tinyint] NOT NULL,
  [command] [varchar](150) NOT NULL,
  [data] [text] NULL,
  [count] [tinyint] NOT NULL,
  [createdDate] [datetime] NOT NULL,
  [createdBy] [varchar](30) NULL,
  [lastAccessed] [datetime] NULL,
  [error_code][varchar](50) NULL
);
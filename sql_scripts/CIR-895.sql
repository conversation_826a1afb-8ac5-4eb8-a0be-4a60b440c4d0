-- NOT needed anymore

-- insert new report
-- INSERT INTO [dbo].[report]
--            ([reportID]
--            ,[reportName]
--            ,[reportType]
--            ,[reportDescription]
--            ,[reportPeriod]
--            ,[reportParameters]
--            ,[reportOrientation]
--            ,[sequence])
--      VALUES
--            (36
--            ,'Residential Simple Report'
--            ,1
--            ,'Residential Simple Report'
--            ,'D'
--            ,null
--            ,1
--            ,36);
--
-- -- insert sub report
-- INSERT INTO [dbo].[sub_report]
--            ([reportID]
--            ,[subReportName]
--            ,[subReportFile]
--            ,[dependency]
--            ,[sequence])
--      VALUES
--            (36
--            ,'Owner Statement'
--            ,'residential/simpleReport.php'
--            ,0
--            ,null)

DELETE FROM report
WHERE reportID = 36
AND reportName = 'Residential Simple Report';

DELETE FROM sub_report
WHERE reportID = 36;
USE npms;

CREATE TABLE [dbo].[scheduledTasksFitzroys](
  [queueID] [int] IDENTITY(1,1) NOT NULL,
  [databaseID] [int] NOT NULL,
  [type] [tinyint] NOT NULL,
  [command] [varchar](150) NOT NULL,
  [data] [text] NULL,
  [count] [tinyint] NOT NULL DEFAULT 0,
  [createdDate] [datetime] NOT NULL,
  [createdBy] [varchar](30) NULL,
  [lastAccessed] [datetime] NULL,
  [error_code][varchar](50) NULL
);

CREATE TABLE [dbo].[scheduledTasksLogFitzroys](
  [queueID] [int] NOT NULL,
  [databaseID] [int] NOT NULL,
  [type] [tinyint] NOT NULL,
  [command] [varchar](150) NOT NULL,
  [data] [text] NULL,
  [count] [tinyint] NOT NULL,
  [createdDate] [datetime] NOT NULL,
  [createdBy] [varchar](30) NULL,
  [lastAccessed] [datetime] NULL,
  [error_code][varchar](50) NULL
);


CREATE TABLE [dbo].[scheduledTasksFacey_Property](
  [queueID] [int] IDENTITY(1,1) NOT NULL,
  [databaseID] [int] NOT NULL,
  [type] [tinyint] NOT NULL,
  [command] [varchar](150) NOT NULL,
  [data] [text] NULL,
  [count] [tinyint] NOT NULL DEFAULT 0,
  [createdDate] [datetime] NOT NULL,
  [createdBy] [varchar](30) NULL,
  [lastAccessed] [datetime] NULL,
  [error_code][varchar](50) NULL
);

CREATE TABLE [dbo].[scheduledTasksLogFacey_Property](
  [queueID] [int] NOT NULL,
  [databaseID] [int] NOT NULL,
  [type] [tinyint] NOT NULL,
  [command] [varchar](150) NOT NULL,
  [data] [text] NULL,
  [count] [tinyint] NOT NULL,
  [createdDate] [datetime] NOT NULL,
  [createdBy] [varchar](30) NULL,
  [lastAccessed] [datetime] NULL,
  [error_code][varchar](50) NULL
);

CREATE TABLE [dbo].[scheduledTasksCSS](
  [queueID] [int] IDENTITY(1,1) NOT NULL,
  [databaseID] [int] NOT NULL,
  [type] [tinyint] NOT NULL,
  [command] [varchar](150) NOT NULL,
  [data] [text] NULL,
  [count] [tinyint] NOT NULL DEFAULT 0,
  [createdDate] [datetime] NOT NULL,
  [createdBy] [varchar](30) NULL,
  [lastAccessed] [datetime] NULL,
  [error_code][varchar](50) NULL
);

CREATE TABLE [dbo].[scheduledTasksLogCSS](
  [queueID] [int] NOT NULL,
  [databaseID] [int] NOT NULL,
  [type] [tinyint] NOT NULL,
  [command] [varchar](150) NOT NULL,
  [data] [text] NULL,
  [count] [tinyint] NOT NULL,
  [createdDate] [datetime] NOT NULL,
  [createdBy] [varchar](30) NULL,
  [lastAccessed] [datetime] NULL,
  [error_code][varchar](50) NULL
);

CREATE TABLE [dbo].[scheduledTasksTeska](
  [queueID] [int] IDENTITY(1,1) NOT NULL,
  [databaseID] [int] NOT NULL,
  [type] [tinyint] NOT NULL,
  [command] [varchar](150) NOT NULL,
  [data] [text] NULL,
  [count] [tinyint] NOT NULL DEFAULT 0,
  [createdDate] [datetime] NOT NULL,
  [createdBy] [varchar](30) NULL,
  [lastAccessed] [datetime] NULL,
  [error_code][varchar](50) NULL
);

CREATE TABLE [dbo].[scheduledTasksLogTeska](
  [queueID] [int] NOT NULL,
  [databaseID] [int] NOT NULL,
  [type] [tinyint] NOT NULL,
  [command] [varchar](150) NOT NULL,
  [data] [text] NULL,
  [count] [tinyint] NOT NULL,
  [createdDate] [datetime] NOT NULL,
  [createdBy] [varchar](30) NULL,
  [lastAccessed] [datetime] NULL,
  [error_code][varchar](50) NULL
);

CREATE TABLE [dbo].[scheduledTasksNichols](
  [queueID] [int] IDENTITY(1,1) NOT NULL,
  [databaseID] [int] NOT NULL,
  [type] [tinyint] NOT NULL,
  [command] [varchar](150) NOT NULL,
  [data] [text] NULL,
  [count] [tinyint] NOT NULL DEFAULT 0,
  [createdDate] [datetime] NOT NULL,
  [createdBy] [varchar](30) NULL,
  [lastAccessed] [datetime] NULL,
  [error_code][varchar](50) NULL
);

CREATE TABLE [dbo].[scheduledTasksLogNichols](
  [queueID] [int] NOT NULL,
  [databaseID] [int] NOT NULL,
  [type] [tinyint] NOT NULL,
  [command] [varchar](150) NOT NULL,
  [data] [text] NULL,
  [count] [tinyint] NOT NULL,
  [createdDate] [datetime] NOT NULL,
  [createdBy] [varchar](30) NULL,
  [lastAccessed] [datetime] NULL,
  [error_code][varchar](50) NULL
);

CREATE TABLE [dbo].[scheduledTasksCrabtree](
  [queueID] [int] IDENTITY(1,1) NOT NULL,
  [databaseID] [int] NOT NULL,
  [type] [tinyint] NOT NULL,
  [command] [varchar](150) NOT NULL,
  [data] [text] NULL,
  [count] [tinyint] NOT NULL DEFAULT 0,
  [createdDate] [datetime] NOT NULL,
  [createdBy] [varchar](30) NULL,
  [lastAccessed] [datetime] NULL,
  [error_code][varchar](50) NULL
);

CREATE TABLE [dbo].[scheduledTasksLogCrabtree](
  [queueID] [int] NOT NULL,
  [databaseID] [int] NOT NULL,
  [type] [tinyint] NOT NULL,
  [command] [varchar](150) NOT NULL,
  [data] [text] NULL,
  [count] [tinyint] NOT NULL,
  [createdDate] [datetime] NOT NULL,
  [createdBy] [varchar](30) NULL,
  [lastAccessed] [datetime] NULL,
  [error_code][varchar](50) NULL
);




DROP INDEX idx_queue_email_database_id ON queue_email;
DROP INDEX idx_queue_email_batch_code ON queue_email;
DROP INDEX idx_queue_email_resend_batch_code_from ON queue_email;
DROP INDEX idx_queue_email_resend_id_from ON queue_email;
DROP INDEX idx_queue_email_attachments_queue_email_id ON queue_email_attachments;

DROP INDEX idx_queue_email_cc_attachments_queue_email_id ON queue_email_cc;

SELECT name
FROM sys.foreign_keys
WHERE referenced_object_id = object_id('queue_email') AND parent_object_id = object_id('queue_email_attachments');
ALTER TABLE queue_email_attachments
DROP CONSTRAINT FK__queue_ema__queue__7DEDA633;

SELECT name
FROM sys.foreign_keys
WHERE referenced_object_id = object_id('queue_email') AND parent_object_id = object_id('queue_email_cc');
ALTER TABLE queue_email_cc
DROP CONSTRAINT FK__queue_ema__queue__01BE3717;
SELECT name
FROM sys.foreign_keys
WHERE referenced_object_id = object_id('queue_email') AND parent_object_id = object_id('queue_email_recipient');
ALTER TABLE queue_email_recipient
DROP CONSTRAINT FK__queue_ema__queue__01BE3717;

SELECT name
FROM sys.foreign_keys
WHERE referenced_object_id = object_id('queue_email_page_source') AND parent_object_id = object_id('queue_email');
ALTER TABLE queue_email
DROP CONSTRAINT FK__queue_ema__queue__01BE3717;

drop TABLE queue_email
drop TABLE queue_email_attachments
drop TABLE queue_email_recipient
drop TABLE queue_email_cc




CREATE TABLE queue_email (
                             id INT IDENTITY(1,1) PRIMARY KEY,
                             created_at DATETIME NOT NULL DEFAULT (GETDATE()),
                             created_by VARCHAR(255),
                             database_id INT NOT NULL,
                             email_log_label_id INT NULL,
                             batch_no VARCHAR(16) NULL,
                             sender_email NVARCHAR(255) NOT NULL,
                             sender_name NVARCHAR(255),
                             subject NVARCHAR(255) NOT NULL,
                             body NVARCHAR(MAX) NOT NULL,
                             priority BIT DEFAULT 0,
                             sending_schedule_date DATETIME NOT NULL DEFAULT (GETDATE()),
                             sent_at DATETIME,
                             attempt_count INT DEFAULT 0,
                             one_email_recipient BIT DEFAULT 0,
                             identifier CHAR(40) NULL,
                             status INT DEFAULT 0,
                             resend_batch_code_from VARCHAR(16) NULL,
                             resend_id_from INT NULL,
                             error_message NVARCHAR(MAX) NULL,
);



CREATE TABLE queue_email_recipient (
                                id INT IDENTITY(1,1) PRIMARY KEY,
                                created_at DATETIME NOT NULL DEFAULT (GETDATE()),
                                created_by VARCHAR(255),
                                queue_email_id INT,
                                recipient_email NVARCHAR(255),
                                overwritten_recipient NVARCHAR(255),
                                is_failed BIT NULL,
                                error_message VARCHAR(1000) NULL
);


CREATE TABLE queue_email_cc (
                                id INT IDENTITY(1,1) PRIMARY KEY,
                                created_at DATETIME NOT NULL DEFAULT (GETDATE()),
                                created_by VARCHAR(255),
                                queue_email_id INT,
                                cc_email NVARCHAR(255),
                                cc_email_overwritten NVARCHAR(255),
                                is_failed BIT NULL,
                                error_message VARCHAR(1000) NULL
);

CREATE TABLE queue_email_attachments (
                                         id INT IDENTITY(1,1) PRIMARY KEY,
                                         created_at DATETIME NOT NULL DEFAULT (GETDATE()),
                                         created_by VARCHAR(255),
                                         queue_email_id INT,
                                         file_name NVARCHAR(255),
                                         file_path NVARCHAR(255),
                                         file_content_type NVARCHAR(255)
);

ALTER TABLE email_log
ADD queue_email_id INT;
ALTER TABLE email_log
    ADD status VARCHAR(100);

CREATE INDEX idx_queue_email_database_id ON queue_email (database_id);
CREATE INDEX idx_queue_email_batch_no ON queue_email (batch_no);
CREATE INDEX idx_queue_email_resend_batch_code_from ON queue_email (resend_batch_code_from);
CREATE INDEX idx_queue_email_resend_id_from ON queue_email (resend_id_from);
CREATE INDEX idx_queue_email_cc_attachments_queue_email_id ON queue_email_cc (queue_email_id);
CREATE INDEX idx_queue_email_attachments_queue_email_id ON queue_email_attachments (queue_email_id);

CREATE INDEX idx_queue_email_recipient_queue_email_id ON queue_email_recipient (queue_email_id);


INSERT INTO [email_settings] ([database_id], [category], [field], [value], [created_by])
SELECT database_id, 'config', 'QUEUE_ENABLED', '1', 352
FROM database_list



select * from queue_email
select * from queue_email_attachments
select * from queue_email_cc
select top 1 * from email_log
SELECT name
FROM sys.foreign_keys
WHERE referenced_object_id = object_id('queue_email') AND parent_object_id = object_id('queue_email_cc');
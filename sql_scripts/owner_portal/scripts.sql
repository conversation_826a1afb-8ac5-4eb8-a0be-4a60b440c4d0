--property management agreements
ALTER TABLE [dbo].[pmpm_p_management]
ADD [pmpm_publish_to_owner] bit DEFAULT 'FALSE';

ALTER TABLE [dbo].[temp_pmpm_p_management]
ADD [pmpm_publish_to_owner] bit DEFAULT 'FALSE';

--property insurance
ALTER TABLE [dbo].[pmin_p_insurance]
ADD [pmin_publish_to_owner] bit DEFAULT 'FALSE';

--lease insurance
ALTER TABLE [dbo].[pmin_l_insurance]
ADD [pmin_publish_to_owner] bit DEFAULT 'FALSE';

ALTER TABLE [dbo].[temp_pmin_l_insurance]
ADD [pmin_publish_to_owner] bit DEFAULT 'FALSE';

--lease guarantee
ALTER TABLE [dbo].[pmgu_l_guarantee]
ADD [pmgu_publish_to_owner] bit DEFAULT 'FALSE';

ALTER TABLE [dbo].[temp_pmgu_l_guarantee]
ADD [pmgu_publish_to_owner] bit DEFAULT 'FALSE';


--npms database
ALTER TABLE [dbo].[states]
ADD [capital] varchar(20) DEFAULT '';

ALTER TABLE [dbo].[states]
ADD [latitude] varchar(20) DEFAULT '';

ALTER TABLE [dbo].[states]
ADD [longitude] varchar(20) DEFAULT '';



UPDATE states SET latitude  = '-31.9505', longitude = '115.8605', capital = 'Perth' WHERE countryCode = 'AU' AND stateCode = 'WA';
UPDATE states SET latitude  = '-33.8688', longitude = '151.2093', capital = 'Sydney' WHERE countryCode = 'AU' AND stateCode = 'NSW';
UPDATE states SET latitude  = '-37.8136', longitude = '144.9631', capital = 'Melbourne' WHERE countryCode = 'AU' AND stateCode = 'VIC';
UPDATE states SET latitude  = '-27.4698', longitude = '153.0251', capital = 'Brisbane' WHERE countryCode = 'AU' AND stateCode = 'QLD';
UPDATE states SET latitude  = '-34.9285', longitude = '138.6007', capital = 'Adelaide' WHERE countryCode = 'AU' AND stateCode = 'SA';
UPDATE states SET latitude  = '-12.4634', longitude = '130.8456', capital = 'Darwin' WHERE countryCode = 'AU' AND stateCode = 'NT';
UPDATE states SET latitude  = '-35.2809', longitude = '149.13', capital = 'Canberra' WHERE countryCode = 'AU' AND stateCode = 'ACT';
UPDATE states SET latitude  = '-42.8821', longitude = '147.3272', capital = 'Hobart' WHERE countryCode = 'AU' AND stateCode = 'TAS';
--client DB

-- remove <PERSON><PERSON><PERSON><PERSON>IN<PERSON> before UPDATE OR INSERT
DECLARE @sql NVARCHAR(MAX)
WHILE 1=1
BEGIN
SELECT TOP 1 @sql = N'alter table ar_transaction drop constraint ['+dc.NAME+N']'
from sys.default_constraints dc
         JOIN sys.columns c
              ON c.default_object_id = dc.object_id
WHERE
        dc.parent_object_id = OBJECT_ID('ar_transaction')
  AND c.name = N'batch_journal'
    IF @@ROWCOUNT = 0 BREAK
    EXEC (@sql)
END

-- update char to int if column exists
ALTER TABLE ar_transaction ALTER COLUMN batch_journal INT NULL;
ALTER TABLE ap_transaction ALTER COLUMN batch_journal INT NULL;

-- add column
ALTER TABLE ar_transaction ADD batch_journal INT NULL;
ALTER TABLE ap_transaction ADD batch_journal INT NULL;

-- update empty strings and 0 to NULL (apply to all)
UPDATE ar_transaction SET batch_journal = NULL WHERE batch_journal = 0 OR batch_journal = ''
UPDATE ap_transaction SET batch_journal = NULL WHERE batch_journal = 0 OR batch_journal = ''

-- run on NON Burgess Rawson client
UPDATE ar_transaction SET batch_journal = NULL

-- script to create update script for batch journal
SELECT  *, CONCAT('UPDATE ar_transaction SET batch_journal = x WHERE batch_journal = ',batch_journal, ';')
FROM
    (SELECT batch_journal,
            ROW_NUMBER() OVER (PARTITION BY batch_journal ORDER BY batch_journal) AS RowNumber
     FROM ar_transaction WHERE batch_journal != 0
    ) AS a
WHERE   a.RowNumber = 1


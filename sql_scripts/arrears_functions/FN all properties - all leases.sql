
/****** Object:  UserDefinedFunction [dbo].[FN_get_outstanding_AR_all_active_leases]    Script Date: 06/05/2021 11:20:40 am ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


-- =============================================
-- Author:		<Author,,Name>
-- Create date: <Create Date,,>
-- Description:	will include inactive properties and leases (SL excluded)
-- =============================================
CREATE FUNCTION [dbo].[FN_get_outstanding_AR_all_properties_all_leases]
(	
	-- Add the parameters for the function here
	@invoiceDate AS DATETIME,
	@receiptUpToDate AS DATETIME
)
RETURNS TABLE 
AS
RETURN 
(

-- Add the SELECT statement with parameter references here
SELECT PropertyCode,
PropertyName,
PortfolioCode,
PortfolioManagerName,
LeaseCode,
LeaseName,
UnitCode,
UnitName,
DebtorCode, 
DebtorName,
description,
InvoiceDate,
DueDate,
FromDate, 
ToDate,
AccountID, 
AccountCodeName,
OriginalNet, 
OriginalGST, 
OutstandingNet,
OutstandingGST, 
OutstandingGross,
TransactionType,
TransactionDate,
TotalAllocated,
TotalReallocated,
InvoiceNumber

	FROM (
		SELECT A.ref_2 AS PropertyCode, PP.pmpr_name AS PropertyName, pmpr_portfolio AS PortfolioCode, portmgr.pmzz_desc AS PortfolioManagerName, A.ref_4 AS LeaseCode,
             PL.pmle_name AS LeaseName, A.ref_5 AS UnitCode, PU.pmpu_desc AS unitName, A.debtor_code AS DebtorCode, PC.pmco_name AS DebtorName,
			 A.description,
            CASE WHEN A.trans_type = 'CRE' THEN A.trans_date ELSE A.due_date END AS DueDate, A.artr_gst_inv_dt AS InvoiceDate,
             A.spare_date_1 AS FromDate, A.spare_date_2 AS ToDate,
             A.ref_3 AS AccountID, PCH.pmca_name AS AccountCodeName, A.trans_amt AS OriginalNet, A.artr_tax_amt AS OriginalGST,
             A.artr_net_amt - COALESCE (SUM(- X1.pmxd_alloc_amt + X1.pmxd_tax_amt), CONVERT(MONEY, 0.00)) - COALESCE (SUM(X2.pmxd_alloc_amt + X2.pmxd_tax_amt),
			 CONVERT(MONEY, 0.00)) AS OutstandingNet, A.artr_tax_amt - COALESCE (SUM(- X1.pmxd_tax_amt), CONVERT(MONEY, 0.00)) - COALESCE (SUM(- X2.pmxd_tax_amt),
			 CONVERT(MONEY, 0.00)) AS OutstandingGST, A.trans_amt - COALESCE (SUM(- X1.pmxd_alloc_amt), CONVERT(MONEY, 0.00)) - COALESCE (SUM(X2.pmxd_alloc_amt),
			 CONVERT(MONEY, 0.00)) AS OutstandingGross, A.trans_type AS TransactionType, A.trans_date AS TransactionDate, 0 AS TotalAllocated,
			 0 AS TotalReallocated, A.artr_gst_inv_no AS InvoiceNumber
		FROM ar_transaction AS A
		JOIN pmpr_property AS PP ON PP.pmpr_prop = A.ref_2 AND pmpr_portfolio != 'SL'
		LEFT OUTER JOIN pmzz_param as portmgr ON (portmgr.pmzz_par_type = 'PORTMGR' AND pmpr_portfolio = portmgr.pmzz_code)
		JOIN pmle_lease AS PL ON A.ref_4 = PL.pmle_lease AND A.ref_2 = PL.pmle_prop
		JOIN pmco_company AS PC ON PC.pmco_code = A.debtor_code
		LEFT JOIN pmpu_p_unit AS PU ON A.ref_5 = PU.pmpu_unit AND A.ref_2 = PU.pmpu_prop
		JOIN pmca_chart AS PCH ON PCH.pmca_code = A.ref_3
		LEFT OUTER JOIN (
			SELECT pmxd_t_batch, pmxd_t_line, SUM(pmxd_alloc_amt) AS pmxd_alloc_amt, SUM(pmxd_tax_amt) AS pmxd_tax_amt
			FROM dbo.pmxd_ar_alloc
			WHERE (pmxd_ar_alloc.pmxd_alloc_dt <= CONVERT(DATETIME, @receiptUpToDate, 103))
			GROUP BY pmxd_t_batch, pmxd_t_line
		) AS X1 ON A.batch_nr = X1.pmxd_t_batch AND A.batch_line_nr = X1.pmxd_t_line
		LEFT OUTER JOIN (
			SELECT pmxd_f_batch, pmxd_f_line, SUM(pmxd_alloc_amt) AS pmxd_alloc_amt, SUM(pmxd_tax_amt) AS pmxd_tax_amt
			FROM dbo.pmxd_ar_alloc AS pmxd_ar_alloc_1
			GROUP BY pmxd_f_batch, pmxd_f_line
		) AS X2 ON A.batch_nr = X2.pmxd_f_batch AND A.batch_line_nr = X2.pmxd_f_line
		WHERE ((A.due_date <= CONVERT(DATETIME, @invoiceDate, 103) AND A.trans_type != 'CRE') 
			OR (A.trans_type = 'CRE' AND CONVERT(date, A.trans_date) <= CONVERT(datetime, @receiptUpToDate, 103)))
		AND ((A.artr_gst_inv_no != '0' AND A.trans_type != 'CRE') OR (A.trans_type = 'CRE'))
		GROUP BY A.batch_nr, A.batch_line_nr, A.artr_tax_amt, 
		A.debtor_code, A.ref_2, A.fund, A.ref_3, A.ref_4, A.ref_5, PU.pmpu_desc, A.artr_gst_code, A.trans_amt,
		A.artr_net_amt, A.spare_date_1, A.spare_date_2, A.trans_date, A.due_date, A.artr_gst_inv_no,
		A.artr_gst_inv_dt, A.trans_type, A.description, PP.pmpr_name, PL.pmle_name,
		PC.pmco_name, PCH.pmca_name, pmpr_portfolio, portmgr.pmzz_desc
	) AS U
	WHERE OutstandingGross != 0
		AND (dueDate <= CONVERT(datetime, @invoiceDate, 103)
		OR (transactionType = 'CRE' AND CONVERT(date, transactionDate) <= CONVERT(datetime, @invoiceDate, 103))
		OR (dueDate IS NULL AND (totalAllocated != 0 OR totalReallocated != 0) AND transactionType = 'CRE' AND CONVERT(date, transactionDate) <= CONVERT(datetime, @invoiceDate, 103)))
		
		UNION ALL
		
	SELECT B.pmuc_prop AS PropertyCode, 
			P.pmpr_name AS PropertyName,
			pmpr_portfolio AS PortfolioCode, 
			portmgr.pmzz_desc AS PortfolioManagerName,
			pmuc_lease AS LeaseCode,
			PL.pmle_name AS LeaseName,
			'' AS unitCode,
			'' AS unitName,
			B.pmuc_s_debtor AS DebtorCode, 
			PC.pmco_name AS DebtorName,
			B.pmuc_desc AS description,
			pmuc_rcpt_dt AS InvoiceDate,
			NULL AS DueDate,
			NULL AS FromDate,
			NULL AS ToDate,
			B.pmuc_acc AS AccountID, 
			PCH.pmca_name AS AccountCodeName,
			B.pmuc_net_amt AS OrignalNet, 
			0 AS OriginalGST, 
			B.pmuc_net_amt AS OutstandingNet,
			B.pmuc_tax_amt AS OutstandingGST, 
			B.pmuc_amt AS OutstandingGross,
			'CSH' AS TransactionType,
			NULL AS TransactionDate,
			0 AS TotalAllocated, 
			0 AS TotalReallocated,
			0 AS InvoiceNumber
		FROM pmuc_unall_csh AS B
		JOIN pmpr_property AS P ON P.pmpr_prop = B.pmuc_prop AND pmpr_portfolio != 'SL'
		LEFT OUTER JOIN pmzz_param as portmgr ON (portmgr.pmzz_par_type = 'PORTMGR' AND pmpr_portfolio = portmgr.pmzz_code)
		JOIN pmle_lease AS PL ON B.pmuc_lease = PL.pmle_lease AND B.pmuc_prop = PL.pmle_prop
		JOIN pmco_company AS PC ON PC.pmco_code = B.pmuc_s_debtor
		JOIN pmca_chart AS PCH ON PCH.pmca_code = B.pmuc_acc
		WHERE (pmuc_rcpt_dt < CONVERT(datetime, @receiptUpToDate,103))
		AND pmuc_rev_dt IS NULL

        UNION ALL

	SELECT
        pmxd_prop as PropertyCode,
        P.pmpr_name AS PropertyName,
        p.pmpr_portfolio AS PortfolioCode,
        portmgr.pmzz_desc AS PortfolioManagerName,
        pmxd_lease as LeaseCode,
        PL.pmle_name AS LeaseName,
        '' AS unitCode,
        '' AS unitName,
        e.pmxd_s_debtor AS DebtorCode,
        PC.pmco_name AS DebtorName,
        'Prepayment' AS description,
        pmxd_alloc_dt AS InvoiceDate,
        NULL AS DueDate,
        NULL AS FromDate,
        NULL AS ToDate,
        pmxd_acc AS AccountID,
        PCH.pmca_name AS AccountCodeName,
        pmxd_alloc_amt AS OrignalNet,
        0 AS OriginalGST,
        pmxd_alloc_amt-pmxd_tax_amt AS OutstandingNet,
        pmxd_tax_amt AS OutstandingGST,
        pmxd_alloc_amt AS OutstandingGross,
        'CSH' AS TransactionType,
        NULL AS TransactionDate,
        0 AS TotalAllocated,
        0 AS TotalReallocated,
        0 AS InvoiceNumber
    FROM pmxd_ar_alloc e
    JOIN pmpr_property AS P ON P.pmpr_prop = e.pmxd_prop AND P.pmpr_portfolio != 'SL'
    LEFT OUTER JOIN pmzz_param as portmgr ON (portmgr.pmzz_par_type = 'PORTMGR' AND pmpr_portfolio = portmgr.pmzz_code)
    JOIN pmle_lease AS PL ON e.pmxd_lease = PL.pmle_lease AND e.pmxd_prop = PL.pmle_prop
    JOIN pmco_company AS PC ON PC.pmco_code = e.pmxd_s_debtor
    JOIN pmca_chart AS PCH ON PCH.pmca_code = e.pmxd_acc
    WHERE pmxd_alloc_dt <= CONVERT(datetime, @invoiceDate, 103)
        AND NOT EXISTS
                (SELECT *
                    FROM ar_transaction r
                    WHERE((batch_nr = e.pmxd_t_batch AND batch_line_nr = e.pmxd_t_line))
                    AND trans_date <= CONVERT(datetime, @invoiceDate, 103))
)
GO


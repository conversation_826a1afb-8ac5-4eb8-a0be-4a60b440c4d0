IF COL_LENGTH('pmpk_p_key', 'reasonActivate') IS NULL
BEGIN
    alter table pmpk_p_key add reasonActivate text;
END
IF COL_LENGTH('pmkd_key_detail', 'pmkd_comp') IS NULL
BEGIN
    alter table pmkd_key_detail add pmkd_comp varchar(10) not null default '';
END
IF COL_LENGTH('pmkd_key_detail', 'pmkd_checked_out_notes') IS NULL
BEGIN
	alter table pmkd_key_detail add pmkd_checked_out_notes text
END
IF COL_LENGTH('pmkd_key_detail', 'pmkd_checked_in_notes') IS NULL
BEGIN
	alter table pmkd_key_detail add pmkd_checked_in_notes text;
END


/**
*
* Run on api_main
* Tables that will handle deactivated rows for oauth_clients and oauth_access_tokens
*
**/

CREATE TABLE deactivated_oauth_clients
(
    id INT IDENTITY(1,1),
    user_id INT,
    client_id NVARCHAR(80),
    client_secret NVARCHAR(80) NULL,
    redirect_uri NVARCHAR(2000) NULL,
    grant_types NVARCHAR(80) NULL,
    scope NVARCHAR(100) NULL,
    database_id INT,
    created_at DATETIME2 NOT NULL DEFAULT (GETDATE()),
    updated_at DATETIME2 NULL,
    deactivated_at DATETIME2 NOT NULL DEFAULT (GETDATE())
);

/**********************************************************/
CREATE TABLE deactivated_oauth_access_tokens
(
    id INT IDENTITY(1,1),
    access_token NVARCHAR(40) NOT NULL,
    client_id NVARCHAR(80) NOT NULL,
    user_id NVARCHAR(255) NULL,
    expires DATETIME2 NOT NULL,
    scope NVARCHAR(100) NULL,
    deactivated_at DATETIME2 NOT NULL DEFAULT (GETDATE())
);

--NOTE: Run only if dropping this table
DROP TABLE deactivated_oauth_clients;
DROP TABLE deactivated_oauth_access_tokens; --this table is for local testing purposes only


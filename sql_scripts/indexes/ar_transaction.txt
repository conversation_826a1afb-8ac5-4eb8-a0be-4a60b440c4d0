DROP INDEX ar_transaction.ar_transactionI2;
DROP INDEX ar_transaction.ar_transactionI3;
DROP INDEX ar_transaction.ar_transactionI4;
DROP INDEX ar_transaction.ar_transactionI5;
DROP INDEX ar_transaction.ar_transactionI6;
DROP INDEX ar_transaction.ar_transactionI7;
DROP INDEX ar_transaction.ar_transactionI8;
DROP INDEX ar_transaction.ar_transactionI9;

CREATE INDEX customer_code_ind ON ar_transaction (customer_code);
CREATE INDEX trans_date_ind ON ar_transaction (trans_date DESC);
CREATE INDEX trans_type_ind ON ar_transaction (trans_type);
CREATE INDEX property_ind ON ar_transaction (ref_2);
CREATE INDEX property_account_ind ON ar_transaction (ref_2, ref_3);
CREATE INDEX property_lease_ind ON ar_transaction (ref_2, ref_4);
CREATE INDEX property_lease_account_ind ON ar_transaction (ref_2, ref_4, ref_3);
CREATE INDEX period_year_ind ON ar_transaction (artr_year, artr_period);
CREATE INDEX property_fund_ind ON ar_transaction (ref_2, fund);
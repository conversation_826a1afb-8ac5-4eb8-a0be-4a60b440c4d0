DROP INDEX ap_transaction.ap_transactionI2;
DROP INDEX ap_transaction.ap_transactionI3;
DROP INDEX ap_transaction.ap_transactionI4;
DROP INDEX ap_transaction.ap_transactionI5;
DROP INDEX ap_transaction.ap_transactionI6;
DROP INDEX ap_transaction.ap_transactionI7;
DROP INDEX ap_transaction.ap_transactionI8;
DROP INDEX ap_transaction.ap_transactionI9;
DROP INDEX ap_transaction.ap_transactionI10;
DROP INDEX ap_transaction.ap_transactionI11;
DROP INDEX ap_transaction.ap_transactionI12;

CREATE INDEX trans_date_ind ON ap_transaction (trans_date DESC);
CREATE INDEX supplier_code_ind ON ap_transaction (supplier_code);
CREATE INDEX property_ind ON ap_transaction (ref_2);
CREATE INDEX property_account_code_ind ON ap_transaction (ref_2, ref_3);
CREATE INDEX account_code_ind ON ap_transaction (ref_3);
CREATE INDEX trans_type ON ap_transaction (trans_type);
CREATE INDEX period_year_ind ON ap_transaction (aptr_year, aptr_period);
CREATE INDEX property_fund_ind ON ap_transaction (ref_2, fund);

DROP INDEX ar_transaction.ar_transactionI2;
DROP INDEX ar_transaction.ar_transactionI3;
DROP INDEX ar_transaction.ar_transactionI4;
DROP INDEX ar_transaction.ar_transactionI5;
DROP INDEX ar_transaction.ar_transactionI6;
DROP INDEX ar_transaction.ar_transactionI7;
DROP INDEX ar_transaction.ar_transactionI8;
DROP INDEX ar_transaction.ar_transactionI9;

CREATE INDEX customer_code_ind ON ar_transaction (customer_code);
CREATE INDEX trans_date_ind ON ar_transaction (trans_date DESC);
CREATE INDEX trans_type_ind ON ar_transaction (trans_type);
CREATE INDEX property_ind ON ar_transaction (ref_2);
CREATE INDEX property_account_ind ON ar_transaction (ref_2, ref_3);
CREATE INDEX property_lease_ind ON ar_transaction (ref_2, ref_4);
CREATE INDEX property_lease_account_ind ON ar_transaction (ref_2, ref_4, ref_3);
CREATE INDEX period_year_ind ON ar_transaction (artr_year, artr_period);
CREATE INDEX property_fund_ind ON ar_transaction (ref_2, fund);

DROP INDEX pmxd_ar_alloc.pmxd_ar_allocI2;
DROP INDEX pmxd_ar_alloc.pmxd_ar_allocI3;
DROP INDEX pmxd_ar_alloc.pmxd_ar_allocI4;
DROP INDEX pmxd_ar_alloc.pmxd_ar_allocI5;
DROP INDEX pmxd_ar_alloc.pmxd_ar_allocI6;

CREATE INDEX from_batch_line_type_ind ON pmxd_ar_alloc (pmxd_f_batch, pmxd_f_line, pmxd_f_type);
CREATE INDEX to_batch_line_type_ind ON pmxd_ar_alloc (pmxd_t_batch, pmxd_t_line, pmxd_t_type);
CREATE INDEX allocation_date_ind ON pmxd_ar_alloc (pmxd_alloc_dt);
CREATE INDEX property_ind ON pmxd_ar_alloc (pmxd_prop);
CREATE INDEX property_lease_ind ON pmxd_ar_alloc (pmxd_prop, pmxd_lease);
CREATE INDEX property_account_ind ON pmxd_ar_alloc (pmxd_prop, pmxd_acc);
CREATE INDEX property_fund_ind ON pmxd_ar_alloc (pmxd_prop, fund);
CREATE INDEX period_year_ind ON pmxd_ar_alloc (pmxd_year, pmxd_period);


DROP INDEX pmxc_ap_alloc.pmxc_ap_allocI2;
DROP INDEX pmxc_ap_alloc.pmxc_ap_allocI3;
DROP INDEX pmxc_ap_alloc.pmxc_ap_allocI4;
DROP INDEX pmxc_ap_alloc.pmxc_ap_allocI5;
DROP INDEX pmxc_ap_alloc.pmxc_ap_allocI6;

CREATE INDEX from_batch_line_type_ind ON pmxc_ap_alloc (pmxc_f_batch, pmxc_f_line, pmxc_f_type);
CREATE INDEX to_batch_line_type_ind ON pmxc_ap_alloc (pmxc_t_batch, pmxc_t_line, pmxc_t_type);
CREATE INDEX allocation_date_ind ON pmxc_ap_alloc (pmxc_alloc_dt);
CREATE INDEX property_ind ON pmxc_ap_alloc (pmxc_prop);
CREATE INDEX property_account_ind ON pmxc_ap_alloc (pmxc_prop, pmxc_acc);
CREATE INDEX property_fund_ind ON pmxc_ap_alloc (pmxc_prop, fund);
CREATE INDEX period_year_ind ON pmxc_ap_alloc (pmxc_year, pmxc_period);

DROP INDEX pmpr_property.pmpr_propertyI2;
DROP INDEX pmpr_property.pmpr_propertyI3;
DROP INDEX pmpr_property.pmpr_propertyI4;
DROP INDEX pmpr_property.pmpr_propertyI5;
DROP INDEX pmpr_property.pmpr_propertyI6;
DROP INDEX pmpr_property.pmpr_propertyI7;
DROP INDEX pmpr_property.pmpr_propertyI8;
DROP INDEX pmpr_property.pmpr_propertyI9;
DROP INDEX pmpr_property.pmpr_propertyI10;
DROP INDEX pmpr_property.idx_pmpr_delete;

CREATE INDEX delete_ind ON pmpr_property (pmpr_delete);
CREATE INDEX property_delete_ind ON pmpr_property (pmpr_prop, pmpr_delete);
CREATE INDEX portfolio_ind ON pmpr_property (pmpr_portfolio);
CREATE INDEX portfolio_delete_ind ON pmpr_property (pmpr_portfolio, pmpr_delete);
CREATE INDEX portfolio_next_charge_date_ind ON pmpr_property (pmpr_portfolio, pmpr_m_upd_dt);
CREATE INDEX group_ind ON pmpr_property (pmpr_prop_group);
CREATE INDEX type_ind ON pmpr_property (pmpr_prop_type);

DROP INDEX pmle_lease.pmle_leaseI2;
DROP INDEX pmle_lease.pmle_leaseI3;
DROP INDEX pmle_lease.pmle_leaseI4;
DROP INDEX pmle_lease.pmle_leaseI5;
DROP INDEX pmle_lease.pmle_leaseI6;
DROP INDEX pmle_lease.pmle_leaseI7;
DROP INDEX pmle_lease.pmle_leaseI8;

CREATE INDEX property_lease_ind ON pmle_lease (pmle_prop, pmle_lease);
CREATE INDEX property_lease_status_ind ON pmle_lease (pmle_prop, pmle_lease, pmle_status);

DROP INDEX pmco_company.pmco_companyI3;
DROP INDEX pmco_company.pmco_companyI4;
DROP INDEX pmco_company.pmco_companyI2;

CREATE INDEX active_ind ON pmco_company (pmco_active);
CREATE INDEX owner_ind ON pmco_company (pmco_owner);
CREATE INDEX supplier_ind ON pmco_company (pmco_supplier);
CREATE INDEX debtor_ind ON pmco_company (pmco_debtor);

DROP INDEX gl_transaction.fk_batch_source;

CREATE INDEX source_batch_ind ON gl_transaction (source, batch_id);
CREATE INDEX property_ind ON gl_transaction (property_id);
CREATE INDEX property_account_ind ON gl_transaction (property_id, account_id);
CREATE INDEX property_period_year_ind ON gl_transaction (property_id, year, period);

CREATE INDEX property_ind ON gl_trial_balance (propertyID);
CREATE INDEX property_account_ind ON gl_trial_balance (propertyID, accountID);
CREATE INDEX property_period_year_ind ON gl_trial_balance (propertyID, year, period);

CREATE INDEX property_lease_ind ON documents (primaryID, secondaryID);

CREATE INDEX property_lease_invoice_ind ON document_invoice (property_id, lease_id, invoice_number);

CREATE INDEX property_period_year_ind ON pmcp_prop_cal (pmcp_prop, pmcp_year, pmcp_period);
CREATE INDEX property_start_end_date_ind ON pmcp_prop_cal (pmcp_prop, pmcp_start_dt, pmcp_end_dt);
CREATE INDEX property_closed_ind ON pmcp_prop_cal (pmcp_prop, pmcp_closed);

DROP INDEX pmlc_l_charge.pmlc_l_chargeI2;
DROP INDEX pmlc_l_charge.pmlc_l_chargeI3;
DROP INDEX pmlc_l_charge.pmlc_l_chargeI4;

CREATE INDEX property_lease_unit_ind ON pmlc_l_charge (pmlc_prop, pmlc_lease, pmlc_unit);

DROP INDEX pmla_l_c_amt.pmla_l_c_amtI2;

CREATE INDEX property_lease_unit_ind ON pmla_l_c_amt (pmla_prop, pmla_lease, pmla_unit, pmla_serial);

CREATE INDEX account_group_1_ind ON pmca_chart (pmca_gl_account_group1);
CREATE INDEX account_group_2_ind ON pmca_chart (pmca_gl_account_group2);

CREATE INDEX property_lease_ind ON pmuc_unall_csh (pmuc_prop, pmuc_lease);

ALTER TABLE pmca_chart
add primary key (pmca_code);

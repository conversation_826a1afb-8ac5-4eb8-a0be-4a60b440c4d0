DROP INDEX ap_transaction.ap_transactionI2;
DROP INDEX ap_transaction.ap_transactionI3;
DROP INDEX ap_transaction.ap_transactionI4;
DROP INDEX ap_transaction.ap_transactionI5;
DROP INDEX ap_transaction.ap_transactionI6;
DROP INDEX ap_transaction.ap_transactionI7;
DROP INDEX ap_transaction.ap_transactionI8;
DROP INDEX ap_transaction.ap_transactionI9;
DROP INDEX ap_transaction.ap_transactionI10;
DROP INDEX ap_transaction.ap_transactionI11;
DROP INDEX ap_transaction.ap_transactionI12;

CREATE INDEX trans_date_ind ON ap_transaction (trans_date DESC);
CREATE INDEX supplier_code_ind ON ap_transaction (supplier_code);
CREATE INDEX property_ind ON ap_transaction (ref_2);
CREATE INDEX property_account_code_ind ON ap_transaction (ref_2, ref_3);
CREATE INDEX account_code_ind ON ap_transaction (ref_3);
CREATE INDEX trans_type ON ap_transaction (trans_type);
CREATE INDEX period_year_ind ON ap_transaction (aptr_year, aptr_period);
CREATE INDEX property_fund_ind ON ap_transaction (ref_2, fund);
DROP TABLE IF EXISTS postcodes_geo;

CREATE TABLE postcodes_geo (
  id int IDENTITY(1,1) PRIMARY KEY,
  postcode varchar(5),
  suburb varchar(100),
  state varchar(4),
  latitude decimal(6,3),
  longitude decimal(6,3)
)



INSERT INTO postcodes_geo (postcode, suburb, state, latitude, longitude) VALUES
('200', 'Australian National University', 'ACT', -35.280, 149.120),
('221', 'Barton', 'ACT', -35.200, 149.100),
('1001', 'Sydney', 'NSW', -33.790, 151.270),
('1002', 'Sydney', 'NSW', -33.790, 151.270),
('1003', 'Sydney', 'NSW', -33.790, 151.270),
('1004', 'Sydney', 'NSW', -33.790, 151.270),
('1005', 'Sydney', 'NSW', -33.790, 151.270),
('1006', 'Sydney', 'NSW', -33.790, 151.270),
('1007', 'Sydney', 'NSW', -33.790, 151.270),
('1008', 'Sydney', 'NSW', -33.790, 151.270),
('1009', 'Sydney', 'NSW', -33.790, 151.270),
('1010', 'Sydney', 'NSW', -33.790, 151.270),
('1011', 'Sydney', 'NSW', -33.790, 151.270),
('1020', 'Sydney', 'NSW', -33.790, 151.270),
('1021', 'Sydney', 'NSW', -33.790, 151.270),
('1022', 'Sydney', 'NSW', -33.790, 151.270),
('1023', 'Sydney', 'NSW', -33.790, 151.270),
('1025', 'Sydney', 'NSW', -33.790, 151.270),
('1026', 'Sydney', 'NSW', -33.790, 151.270),
('1027', 'Sydney', 'NSW', -33.790, 151.270),
('1028', 'Sydney', 'NSW', -33.790, 151.270),
('1029', 'Sydney', 'NSW', -33.790, 151.270),
('1030', 'Sydney', 'NSW', -33.790, 151.270),
('1031', 'Sydney', 'NSW', -33.790, 151.270),
('1032', 'Sydney', 'NSW', -33.660, 150.870),
('1033', 'Sydney', 'NSW', -33.790, 151.270),
('1034', 'Sydney', 'NSW', -33.790, 151.270),
('1035', 'Sydney', 'NSW', -33.790, 151.270),
('1036', 'Sydney', 'NSW', -33.790, 151.270),
('1037', 'Sydney', 'NSW', -33.790, 151.270),
('1038', 'Sydney', 'NSW', -33.790, 151.270),
('1039', 'Sydney', 'NSW', -33.790, 151.270),
('1040', 'Sydney', 'NSW', -33.790, 151.270),
('1041', 'Sydney', 'NSW', -33.790, 151.270),
('1042', 'Sydney', 'NSW', -33.790, 151.270),
('1043', 'Sydney', 'NSW', -33.790, 151.270),
('1044', 'Sydney', 'NSW', -33.790, 151.270),
('1045', 'Sydney', 'NSW', -33.790, 151.270),
('1046', 'Sydney', 'NSW', -33.790, 151.270),
('1100', 'Sydney', 'NSW', -33.790, 151.270),
('1101', 'Sydney', 'NSW', -33.790, 151.270),
('1105', 'Sydney', 'NSW', -33.790, 151.270),
('1106', 'Sydney', 'NSW', -33.790, 151.270),
('1107', 'Sydney', 'NSW', -33.790, 151.270),
('1108', 'Sydney', 'NSW', -33.790, 151.270),
('1109', 'Sydney', 'NSW', -33.790, 151.270),
('1110', 'Sydney', 'NSW', -33.790, 151.270),
('1112', 'Sydney', 'NSW', -33.790, 151.270),
('1113', 'Sydney', 'NSW', -33.790, 151.270),
('1114', 'Sydney', 'NSW', -33.790, 151.270),
('1115', 'Sydney', 'NSW', -33.790, 151.270),
('1116', 'Sydney', 'NSW', -33.670, 150.870),
('1117', 'Sydney', 'NSW', -33.660, 150.870),
('1118', 'Sydney', 'NSW', -33.790, 151.270),
('1119', 'Sydney', 'NSW', -33.790, 151.270),
('1120', 'Sydney', 'NSW', -33.790, 151.270),
('1121', 'Sydney', 'NSW', -33.790, 151.270),
('1122', 'Sydney', 'NSW', -33.790, 151.270),
('1123', 'Sydney', 'NSW', -33.790, 151.270),
('1124', 'Sydney', 'NSW', -33.790, 151.270),
('1125', 'Sydney', 'NSW', -33.790, 151.270),
('1126', 'Sydney', 'NSW', -33.790, 151.270),
('1127', 'Sydney', 'NSW', -33.790, 151.270),
('1128', 'Sydney', 'NSW', -33.790, 151.270),
('1129', 'Sydney', 'NSW', -33.790, 151.270),
('1130', 'Sydney', 'NSW', -33.790, 151.270),
('1131', 'Sydney', 'NSW', -33.790, 151.270),
('1132', 'Sydney', 'NSW', -33.660, 150.870),
('1133', 'Sydney', 'NSW', -33.790, 151.270),
('1134', 'Sydney', 'NSW', -33.790, 151.270),
('1135', 'Sydney', 'NSW', -33.790, 151.270),
('1136', 'Sydney', 'NSW', -33.790, 151.270),
('1137', 'Sydney', 'NSW', -33.790, 151.270),
('1138', 'Sydney', 'NSW', -33.790, 151.270),
('1139', 'Sydney', 'NSW', -33.790, 151.270),
('1140', 'Sydney', 'NSW', -33.790, 151.270),
('1141', 'Sydney', 'NSW', -33.790, 151.270),
('1142', 'Sydney', 'NSW', -33.790, 151.270),
('1143', 'Sydney', 'NSW', -33.790, 151.270),
('1144', 'Sydney', 'NSW', -33.790, 151.270),
('1145', 'Sydney', 'NSW', -33.790, 151.270),
('1146', 'Sydney', 'NSW', -33.790, 151.270),
('1147', 'Sydney', 'NSW', -33.790, 151.270),
('1148', 'Sydney', 'NSW', -33.790, 151.270),
('1149', 'Sydney', 'NSW', -33.790, 151.270),
('1150', 'Sydney', 'NSW', -33.790, 151.270),
('1151', 'Sydney', 'NSW', -33.790, 151.270),
('1152', 'Sydney', 'NSW', -33.790, 151.270),
('1153', 'Sydney', 'NSW', -33.790, 151.270),
('1154', 'Sydney', 'NSW', -33.790, 151.270),
('1155', 'Sydney', 'NSW', -33.790, 151.270),
('1156', 'Sydney', 'NSW', -33.790, 151.270),
('1157', 'Sydney', 'NSW', -33.790, 151.270),
('1158', 'Sydney', 'NSW', -33.790, 151.270),
('1159', 'Sydney', 'NSW', -33.790, 151.270),
('1160', 'Sydney', 'NSW', -33.790, 151.270),
('1161', 'Sydney', 'NSW', -33.790, 151.270),
('1162', 'Sydney', 'NSW', -33.790, 151.270),
('1163', 'Sydney', 'NSW', -33.790, 151.270),
('1164', 'Sydney', 'NSW', -33.790, 151.270),
('1165', 'Sydney', 'NSW', -33.790, 151.270),
('1166', 'Sydney', 'NSW', -33.790, 151.270),
('1167', 'Sydney', 'NSW', -33.790, 151.270),
('1168', 'Sydney', 'NSW', -33.790, 151.270),
('1169', 'Sydney', 'NSW', -33.790, 151.270),
('1170', 'Sydney', 'NSW', -33.790, 151.270),
('1171', 'Sydney', 'NSW', -33.790, 151.270),
('1172', 'Sydney', 'NSW', -33.790, 151.270),
('1173', 'Sydney', 'NSW', -33.790, 151.270),
('1174', 'Sydney', 'NSW', -33.790, 151.270),
('1175', 'Sydney', 'NSW', -33.790, 151.270),
('1176', 'Sydney', 'NSW', -33.790, 151.270),
('1177', 'Sydney', 'NSW', -33.790, 151.270),
('1178', 'Sydney', 'NSW', -33.790, 151.270),
('1179', 'Sydney', 'NSW', -33.790, 151.270),
('1180', 'Sydney', 'NSW', -33.790, 151.270),
('1181', 'Sydney', 'NSW', -33.790, 151.270),
('1182', 'Sydney', 'NSW', -33.790, 151.270),
('1183', 'Sydney', 'NSW', -33.790, 151.270),
('1184', 'Sydney', 'NSW', -33.790, 151.270),
('1185', 'Sydney', 'NSW', -33.790, 151.270),
('1186', 'Sydney', 'NSW', -33.790, 151.270),
('1187', 'Sydney', 'NSW', -33.790, 151.270),
('1188', 'Sydney', 'NSW', -33.790, 151.270),
('1189', 'Sydney', 'NSW', -33.790, 151.270),
('1190', 'Sydney', 'NSW', -33.790, 151.270),
('1191', 'Sydney', 'NSW', -33.790, 151.270),
('1192', 'Sydney', 'NSW', -34.790, 147.690),
('1193', 'Sydney', 'NSW', -33.790, 151.270),
('1194', 'Sydney', 'NSW', -33.790, 151.270),
('1195', 'Sydney', 'NSW', -33.790, 151.270),
('1196', 'Sydney', 'NSW', -33.790, 151.270),
('1197', 'Sydney', 'NSW', -33.790, 151.270),
('1198', 'Sydney', 'NSW', -33.790, 151.270),
('1199', 'Sydney', 'NSW', -33.790, 151.270),
('1200', 'Sydney', 'NSW', -33.790, 151.270),
('1201', 'Sydney', 'NSW', -33.790, 151.270),
('1202', 'Sydney', 'NSW', -33.790, 151.270),
('1203', 'Sydney', 'NSW', -33.790, 151.270),
('1204', 'Sydney', 'NSW', -33.790, 151.270),
('1205', 'Sydney', 'NSW', -33.790, 151.270),
('1206', 'Sydney', 'NSW', -33.790, 151.270),
('1207', 'Sydney', 'NSW', -33.790, 151.270),
('1208', 'Haymarket', 'NSW', -29.820, 151.660),
('1209', 'Australia Square', 'NSW', -33.890, 151.180),
('1210', 'Australia Square', 'NSW', -33.890, 151.180),
('1211', 'Australia Square', 'NSW', -33.890, 151.180),
('1212', 'Australia Square', 'NSW', -33.890, 151.180),
('1213', 'Australia Square', 'NSW', -33.890, 151.180),
('1214', 'Australia Square', 'NSW', -33.890, 151.180),
('1215', 'Australia Square', 'NSW', -33.890, 151.180),
('1216', 'Grosvenor Place', 'NSW', -33.740, 151.030),
('1217', 'Grosvenor Place', 'NSW', -33.740, 151.030),
('1218', 'Grosvenor Place', 'NSW', -33.740, 151.030),
('1219', 'Grosvenor Place', 'NSW', -33.740, 151.030),
('1220', 'Grosvenor Place', 'NSW', -33.740, 151.030),
('1221', 'Royal Exchange', 'NSW', -33.870, 151.210),
('1222', 'Royal Exchange', 'NSW', -33.870, 151.210),
('1223', 'Royal Exchange', 'NSW', -33.870, 151.210),
('1224', 'Royal Exchange', 'NSW', -33.870, 151.210),
('1225', 'Royal Exchange', 'NSW', -33.870, 151.210),
('1226', 'Queen Victoria Building', 'NSW', -33.870, 151.210),
('1227', 'Queen Victoria Building', 'NSW', -33.870, 151.210),
('1228', 'Queen Victoria Building', 'NSW', -33.870, 151.210),
('1229', 'Queen Victoria Building', 'NSW', -33.870, 151.210),
('1230', 'Queen Victoria Building', 'NSW', -33.870, 151.210),
('1231', 'Sydney South', 'NSW', -33.820, 151.040),
('1232', 'Sydney South', 'NSW', -33.820, 151.040),
('1233', 'Sydney South', 'NSW', -33.820, 151.040),
('1234', 'Sydney South', 'NSW', -33.820, 151.040),
('1235', 'Sydney South', 'NSW', -33.820, 151.040),
('1236', 'Haymarket', 'NSW', -29.820, 151.660),
('1237', 'Haymarket', 'NSW', -29.820, 151.660),
('1238', 'Haymarket', 'NSW', -29.820, 151.660),
('1239', 'Haymarket', 'NSW', -29.820, 151.660),
('1240', 'Haymarket', 'NSW', -29.820, 151.660),
('1291', 'Sydney', 'NSW', -33.790, 151.270),
('1292', 'Sydney', 'NSW', -33.790, 151.270),
('1293', 'Sydney', 'NSW', -33.790, 151.270),
('1294', 'Sydney', 'NSW', -33.790, 151.270),
('1295', 'Sydney', 'NSW', -33.790, 151.270),
('1296', 'Sydney', 'NSW', -33.790, 151.270),
('1297', 'Sydney', 'NSW', -33.790, 151.270),
('1298', 'Sydney', 'NSW', -33.790, 151.270),
('1299', 'Sydney', 'NSW', -33.790, 151.270),
('1300', 'Darlinghurst', 'NSW', -33.880, 151.220),
('1311', 'Eastern Suburbs MC', 'NSW', 0.000, 0.000),
('1312', 'Eastern Suburbs MC', 'NSW', 0.000, 0.000),
('1313', 'Eastern Suburbs MC', 'NSW', 0.000, 0.000),
('1314', 'Eastern Suburbs MC', 'NSW', 0.000, 0.000),
('1315', 'Eastern Suburbs MC', 'NSW', 0.000, 0.000),
('1316', 'Eastern Suburbs MC', 'NSW', 0.000, 0.000),
('1317', 'Eastern Suburbs MC', 'NSW', 0.000, 0.000),
('1318', 'Eastern Suburbs MC', 'NSW', 0.000, 0.000),
('1319', 'Eastern Suburbs MC', 'NSW', 0.000, 0.000),
('1320', 'Eastern Suburbs MC', 'NSW', 0.000, 0.000),
('1321', 'Eastern Suburbs MC', 'NSW', 0.000, 0.000),
('1322', 'Eastern Suburbs MC', 'NSW', 0.000, 0.000),
('1323', 'Eastern Suburbs MC', 'NSW', 0.000, 0.000),
('1324', 'Eastern Suburbs MC', 'NSW', 0.000, 0.000),
('1325', 'Eastern Suburbs MC', 'NSW', 0.000, 0.000),
('1326', 'Eastern Suburbs MC', 'NSW', 0.000, 0.000),
('1327', 'Eastern Suburbs MC', 'NSW', 0.000, 0.000),
('1328', 'Eastern Suburbs MC', 'NSW', 0.000, 0.000),
('1329', 'Eastern Suburbs MC', 'NSW', 0.000, 0.000),
('1330', 'Eastern Suburbs MC', 'NSW', 0.000, 0.000),
('1331', 'Eastern Suburbs MC', 'NSW', 0.000, 0.000),
('1332', 'Eastern Suburbs MC', 'NSW', 0.000, 0.000),
('1333', 'Eastern Suburbs MC', 'NSW', 0.000, 0.000),
('1334', 'Eastern Suburbs MC', 'NSW', 0.000, 0.000),
('1335', 'Potts Point', 'NSW', -33.820, 151.120),
('1340', 'Kings Cross', 'NSW', -35.540, 148.020),
('1350', 'Woollahra', 'NSW', 0.000, 0.000),
('1355', 'Bondi Junction', 'NSW', -33.890, 151.260),
('1360', 'Double Bay', 'NSW', -33.880, 151.240),
('1391', 'Ato Activity Statements', 'NSW', 0.000, 0.000),
('1401', 'Broadway', 'NSW', -33.880, 151.200),
('1416', 'Southern Suburbs MC', 'NSW', 0.000, 0.000),
('1419', 'Southern Suburbs MC', 'NSW', 0.000, 0.000),
('1420', 'Strawberry Hills', 'NSW', -33.730, 150.930),
('1422', 'Strawberry Hills', 'NSW', -33.730, 150.930),
('1423', 'Strawberry Hills', 'NSW', -33.730, 150.930),
('1424', 'Strawberry Hills', 'NSW', -33.730, 150.930),
('1425', 'Strawberry Hills', 'NSW', -33.730, 150.930),
('1426', 'Strawberry Hills', 'NSW', -33.730, 150.930),
('1427', 'Strawberry Hills', 'NSW', -33.730, 150.930),
('1428', 'Strawberry Hills', 'NSW', -33.730, 150.930),
('1429', 'Strawberry Hills', 'NSW', -33.730, 150.930),
('1430', 'Eveleigh', 'NSW', -33.890, 151.200),
('1435', 'Alexandria', 'NSW', -33.710, 151.110),
('1440', 'Waterloo', 'NSW', -33.900, 151.060),
('1441', 'Waterloo', 'NSW', -33.900, 151.060),
('1445', 'Rosebery', 'NSW', -34.080, 151.010),
('1450', 'Camperdown', 'NSW', -30.310, 153.140),
('1455', 'Botany', 'NSW', -33.950, 151.200),
('1460', 'Mascot', 'NSW', -33.930, 151.210),
('1465', 'Kensington', 'NSW', -33.890, 151.140),
('1466', 'Unsw Sydney', 'NSW', -33.910, 151.230),
('1470', 'Drummoyne', 'NSW', -33.840, 151.150),
('1475', 'Marrickville', 'NSW', -33.910, 151.150),
('1476', 'Marrickville', 'NSW', -33.910, 151.150),
('1480', 'Kingsgrove', 'NSW', -33.940, 151.100),
('1481', 'Hurstville BC', 'NSW', 0.000, 0.000),
('1484', 'Kingsgrove DC', 'NSW', 0.000, 0.000),
('1485', 'Kogarah', 'NSW', 0.000, 0.000),
('1487', 'Kogarah', 'NSW', 0.000, 0.000),
('1490', 'Miranda', 'NSW', -34.040, 151.110),
('1493', 'Hurstville', 'NSW', -33.980, 151.090),
('1495', 'Caringbah', 'NSW', -34.050, 151.120),
('1499', 'Sutherland', 'NSW', -34.020, 151.060),
('1515', 'West Chatswood', 'NSW', -33.820, 151.210),
('1560', 'Northbridge', 'NSW', 0.000, 0.000),
('1565', 'Milsons Point', 'NSW', -33.870, 151.190),
('1570', 'Artarmon', 'NSW', -33.810, 151.190),
('1582', 'Crows Nest', 'NSW', -33.830, 151.200),
('1585', 'Crows Nest', 'NSW', -33.830, 151.200),
('1590', 'St Leonards', 'NSW', -33.290, 151.470),
('1595', 'Lane Cove', 'NSW', -33.790, 151.190),
('1597', 'Lane Cove', 'NSW', -33.790, 151.190),
('1602', 'Lane Cove DC', 'NSW', 0.000, 0.000),
('1608', 'Lane Cove DC', 'NSW', 0.000, 0.000),
('1610', 'Lane Cove DC', 'NSW', 0.000, 0.000),
('1611', 'Lane Cove DC', 'NSW', 0.000, 0.000),
('1630', 'Hornsby', 'NSW', -33.710, 151.100),
('1635', 'Hornsby Westfield', 'NSW', 0.000, 0.000),
('1639', 'Frenchs Forest', 'NSW', -33.790, 151.250),
('1640', 'Frenchs Forest', 'NSW', -33.790, 151.250),
('1655', 'Manly', 'NSW', -33.330, 151.510),
('1658', 'Mona Vale', 'NSW', -33.700, 151.220),
('1660', 'Mona Vale', 'NSW', -33.700, 151.220),
('1670', 'North Ryde BC', 'NSW', 0.000, 0.000),
('1675', 'Gladesville', 'NSW', -33.830, 151.140),
('1680', 'Ryde', 'NSW', -33.760, 151.140),
('1685', 'West Ryde', 'NSW', -33.800, 151.090),
('1690', 'Northern Suburbs MC', 'NSW', 0.000, 0.000),
('1691', 'Northern Suburbs MC', 'NSW', 0.000, 0.000),
('1692', 'Northern Suburbs MC', 'NSW', 0.000, 0.000),
('1693', 'Northern Suburbs MC', 'NSW', 0.000, 0.000),
('1694', 'Northern Suburbs MC', 'NSW', 0.000, 0.000),
('1695', 'Northern Suburbs MC', 'NSW', 0.000, 0.000),
('1696', 'Northern Suburbs MC', 'NSW', 0.000, 0.000),
('1697', 'Northern Suburbs MC', 'NSW', 0.000, 0.000),
('1698', 'Northern Suburbs MC', 'NSW', 0.000, 0.000),
('1699', 'Northern Suburbs MC', 'NSW', 0.000, 0.000),
('1700', 'Ermington', 'NSW', -33.950, 151.210),
('1701', 'Rydalmere BC', 'NSW', 0.000, 0.000),
('1710', 'Epping', 'NSW', -33.780, 151.120),
('1712', 'Epping', 'NSW', -33.780, 151.120),
('1715', 'Pennant Hills', 'NSW', -33.760, 151.050),
('1730', 'Seven Hills', 'NSW', -33.760, 150.970),
('1740', 'Parramatta', 'NSW', -33.890, 151.140),
('1741', 'Parramatta', 'NSW', -33.890, 151.140),
('1750', 'North Parramatta', 'NSW', -33.860, 151.020),
('1755', 'Baulkham Hills', 'NSW', -33.770, 150.970),
('1765', 'Castle Hill', 'NSW', -33.740, 151.030),
('1771', 'Pennant Hills', 'NSW', -33.760, 151.050),
('1781', 'Seven Hills MC', 'NSW', 0.000, 0.000),
('1790', 'St Marys', 'NSW', -33.860, 151.200),
('1797', 'Penrith South DC', 'NSW', 0.000, 0.000),
('1800', 'Ashfield', 'NSW', -34.100, 150.780),
('1805', 'Burwood', 'NSW', -33.890, 151.100),
('1811', 'Silverwater', 'NSW', -33.820, 151.050),
('1816', 'Strathfield', 'NSW', -33.880, 151.090),
('1819', 'Strathfield', 'NSW', -33.880, 151.090),
('1825', 'Lidcombe', 'NSW', 0.000, 0.000),
('1826', 'Lidcombe', 'NSW', 0.000, 0.000),
('1830', 'Granville', 'NSW', -33.860, 150.950),
('1831', 'Granville', 'NSW', -33.860, 150.950),
('1835', 'Auburn', 'NSW', -33.880, 151.020),
('1848', 'Guildford', 'NSW', -33.850, 150.970),
('1851', 'Wetherill Park DC', 'NSW', 0.000, 0.000),
('1860', 'Fairfield', 'NSW', -33.850, 150.960),
('1871', 'Liverpool', 'NSW', -33.890, 151.100),
('1875', 'Moorebank', 'NSW', -33.950, 150.920),
('1885', 'Bankstown', 'NSW', -33.910, 151.020),
('1888', 'Bankstown', 'NSW', -33.910, 151.020),
('1890', 'Ingleburn', 'NSW', -33.960, 150.800),
('1891', 'Milperra', 'NSW', -33.930, 151.000),
('1900', 'Leightonfield MC', 'NSW', 0.000, 0.000),
('1902', 'Leightonfield MC', 'NSW', 0.000, 0.000),
('2000', 'Barangaroo', 'NSW', -33.860, 151.210),
('2000', 'Dawes Point', 'NSW', -33.860, 151.210),
('2000', 'Haymarket', 'NSW', -33.860, 151.210),
('2000', 'Millers Point', 'NSW', -33.860, 151.210),
('2000', 'Parliament House', 'NSW', -33.860, 151.210),
('2000', 'Sydney', 'NSW', -33.860, 151.210),
('2000', 'Sydney South', 'NSW', -33.860, 151.210),
('2000', 'The Rocks', 'NSW', -33.860, 151.210),
('2001', 'Sydney', 'NSW', -33.790, 151.270),
('2002', 'World Square', 'NSW', -35.970, 146.410),
('2004', 'Alexandria MC', 'NSW', 0.000, 0.000),
('2004', 'Eastern Suburbs MC', 'NSW', 0.000, 0.000),
('2006', 'The University Of Sydney', 'NSW', -33.890, 151.190),
('2007', 'Broadway', 'NSW', -33.880, 151.200),
('2007', 'Ultimo', 'NSW', -33.880, 151.200),
('2008', 'Chippendale', 'NSW', -33.890, 151.200),
('2008', 'Darlington', 'NSW', -33.890, 151.200),
('2009', 'Pyrmont', 'NSW', -33.870, 151.190),
('2010', 'Darlinghurst', 'NSW', -33.880, 151.220),
('2010', 'Surry Hills', 'NSW', -33.880, 151.220),
('2011', 'Elizabeth Bay', 'NSW', -33.870, 151.230),
('2011', 'Hmas Kuttabul', 'NSW', -33.870, 151.230),
('2011', 'Potts Point', 'NSW', -33.870, 151.230),
('2011', 'Rushcutters Bay', 'NSW', -33.870, 151.230),
('2011', 'Woolloomooloo', 'NSW', -33.870, 151.230),
('2012', 'Strawberry Hills', 'NSW', -33.730, 150.930),
('2013', 'Strawberry Hills', 'NSW', -33.730, 150.930),
('2015', 'Alexandria', 'NSW', -33.900, 151.200),
('2015', 'Beaconsfield', 'NSW', -33.900, 151.200),
('2015', 'Eveleigh', 'NSW', -33.900, 151.200),
('2016', 'Redfern', 'NSW', -33.890, 151.200),
('2017', 'Waterloo', 'NSW', -33.900, 151.210),
('2017', 'Zetland', 'NSW', -33.900, 151.210),
('2018', 'Eastlakes', 'NSW', -33.930, 151.210),
('2018', 'Rosebery', 'NSW', -33.930, 151.210),
('2019', 'Banksmeadow', 'NSW', -33.960, 151.210),
('2019', 'Botany', 'NSW', -33.960, 151.210),
('2020', 'Mascot', 'NSW', -33.930, 151.190),
('2020', 'Sydney Domestic Airport', 'NSW', -33.930, 151.190),
('2020', 'Sydney International Airport', 'NSW', -33.930, 151.190),
('2021', 'Centennial Park', 'NSW', -33.890, 151.220),
('2021', 'Moore Park', 'NSW', -33.890, 151.220),
('2021', 'Paddington', 'NSW', -33.890, 151.220),
('2022', 'Bondi Junction', 'NSW', -33.890, 151.250),
('2022', 'Bondi Junction Plaza', 'NSW', -33.890, 151.250),
('2022', 'Queens Park', 'NSW', -33.890, 151.250),
('2023', 'Bellevue Hill', 'NSW', -33.890, 151.260),
('2024', 'Bronte', 'NSW', -33.900, 151.260),
('2024', 'Waverley', 'NSW', -33.900, 151.260),
('2025', 'Woollahra', 'NSW', -33.890, 151.240),
('2026', 'Bondi', 'NSW', -33.890, 151.260),
('2026', 'Bondi Beach', 'NSW', -33.890, 151.260),
('2026', 'North Bondi', 'NSW', -33.890, 151.260),
('2026', 'Tamarama', 'NSW', -33.890, 151.260),
('2027', 'Darling Point', 'NSW', -33.870, 151.240),
('2027', 'Edgecliff', 'NSW', -33.870, 151.240),
('2027', 'Hmas Rushcutters', 'NSW', -33.870, 151.240),
('2027', 'Point Piper', 'NSW', -33.870, 151.240),
('2028', 'Double Bay', 'NSW', -33.880, 151.240),
('2029', 'Rose Bay', 'NSW', -33.870, 151.280),
('2030', 'Dover Heights', 'NSW', -33.870, 151.280),
('2030', 'Hmas Watson', 'NSW', -33.870, 151.280),
('2030', 'Rose Bay North', 'NSW', -33.870, 151.280),
('2030', 'Vaucluse', 'NSW', -33.870, 151.280),
('2030', 'Watsons Bay', 'NSW', -33.870, 151.280),
('2031', 'Clovelly', 'NSW', -33.910, 151.260),
('2031', 'Clovelly West', 'NSW', -33.910, 151.260),
('2031', 'Randwick', 'NSW', -33.910, 151.260),
('2031', 'St Pauls', 'NSW', -33.910, 151.260),
('2032', 'Daceyville', 'NSW', -33.930, 151.230),
('2032', 'Kingsford', 'NSW', -33.930, 151.230),
('2033', 'Kensington', 'NSW', -33.910, 151.220),
('2034', 'Coogee', 'NSW', -33.920, 151.250),
('2034', 'South Coogee', 'NSW', -33.920, 151.250),
('2035', 'Maroubra', 'NSW', -33.950, 151.240),
('2035', 'Maroubra South', 'NSW', -33.950, 151.240),
('2035', 'Pagewood', 'NSW', -33.950, 151.240),
('2036', 'Chifley', 'NSW', -33.980, 151.240),
('2036', 'Eastgardens', 'NSW', -33.980, 151.240),
('2036', 'Hillsdale', 'NSW', -33.980, 151.240),
('2036', 'La Perouse', 'NSW', -33.980, 151.240),
('2036', 'Little Bay', 'NSW', -33.980, 151.240),
('2036', 'Malabar', 'NSW', -33.980, 151.240),
('2036', 'Matraville', 'NSW', -33.980, 151.240),
('2036', 'Phillip Bay', 'NSW', -33.980, 151.240),
('2036', 'Port Botany', 'NSW', -33.980, 151.240),
('2037', 'Forest Lodge', 'NSW', -33.880, 151.180),
('2037', 'Glebe', 'NSW', -33.880, 151.180),
('2038', 'Annandale', 'NSW', -33.880, 151.170),
('2039', 'Rozelle', 'NSW', -33.860, 151.170),
('2040', 'Leichhardt', 'NSW', -33.880, 151.160),
('2040', 'Lilyfield', 'NSW', -33.880, 151.160),
('2041', 'Balmain', 'NSW', -33.860, 151.180),
('2041', 'Balmain East', 'NSW', -33.860, 151.180),
('2041', 'Birchgrove', 'NSW', -33.860, 151.180),
('2042', 'Enmore', 'NSW', -33.900, 151.170),
('2042', 'Newtown', 'NSW', -33.900, 151.170),
('2043', 'Erskineville', 'NSW', -33.900, 151.190),
('2044', 'St Peters', 'NSW', -33.910, 151.180),
('2044', 'Sydenham', 'NSW', -33.910, 151.180),
('2044', 'Tempe', 'NSW', -33.910, 151.180),
('2045', 'Haberfield', 'NSW', -33.880, 151.140),
('2046', 'Abbotsford', 'NSW', -33.850, 151.130),
('2046', 'Canada Bay', 'NSW', -33.850, 151.130),
('2046', 'Chiswick', 'NSW', -33.850, 151.130),
('2046', 'Five Dock', 'NSW', -33.850, 151.130),
('2046', 'Rodd Point', 'NSW', -33.850, 151.130),
('2046', 'Russell Lea', 'NSW', -33.850, 151.130),
('2046', 'Wareemba', 'NSW', -33.850, 151.130),
('2047', 'Drummoyne', 'NSW', -33.850, 151.150),
('2048', 'Stanmore', 'NSW', -33.900, 151.170),
('2048', 'Westgate', 'NSW', -33.900, 151.170),
('2049', 'Lewisham', 'NSW', -33.890, 151.140),
('2049', 'Petersham', 'NSW', -33.890, 151.140),
('2049', 'Petersham North', 'NSW', -33.890, 151.140),
('2050', 'Camperdown', 'NSW', -33.890, 151.180),
('2050', 'Missenden Road', 'NSW', -33.890, 151.180),
('2052', 'Unsw Sydney', 'NSW', -33.910, 151.230),
('2055', 'North Sydney', 'NSW', -33.800, 151.100),
('2057', 'Chatswood', 'NSW', -33.790, 151.190),
('2058', 'Northern Suburbs MC', 'NSW', 0.000, 0.000),
('2059', 'North Sydney', 'NSW', -33.800, 151.100),
('2060', 'Hmas Platypus', 'NSW', -33.840, 151.190),
('2060', 'Hmas Waterhen', 'NSW', -33.840, 151.190),
('2060', 'Lavender Bay', 'NSW', -33.840, 151.190),
('2060', 'Mcmahons Point', 'NSW', -33.840, 151.190),
('2060', 'North Sydney', 'NSW', -33.840, 151.190),
('2060', 'North Sydney Shoppingworld', 'NSW', -33.840, 151.190),
('2060', 'Waverton', 'NSW', -33.840, 151.190),
('2061', 'Kirribilli', 'NSW', -33.850, 151.210),
('2061', 'Milsons Point', 'NSW', -33.850, 151.210),
('2062', 'Cammeray', 'NSW', -33.820, 151.210),
('2063', 'Northbridge', 'NSW', -33.820, 151.220),
('2064', 'Artarmon', 'NSW', -33.810, 151.190),
('2065', 'Crows Nest', 'NSW', -33.830, 151.200),
('2065', 'Greenwich', 'NSW', -33.830, 151.200),
('2065', 'Naremburn', 'NSW', -33.830, 151.200),
('2065', 'Royal North Shore Hospital', 'NSW', -33.830, 151.200),
('2065', 'St Leonards', 'NSW', -33.830, 151.200),
('2065', 'Wollstonecraft', 'NSW', -33.830, 151.200),
('2066', 'Lane Cove', 'NSW', -33.810, 151.170),
('2066', 'Lane Cove North', 'NSW', -33.810, 151.170),
('2066', 'Lane Cove West', 'NSW', -33.810, 151.170),
('2066', 'Linley Point', 'NSW', -33.810, 151.170),
('2066', 'Longueville', 'NSW', -33.810, 151.170),
('2066', 'Northwood', 'NSW', -33.810, 151.170),
('2066', 'Riverview', 'NSW', -33.810, 151.170),
('2067', 'Chatswood', 'NSW', -33.800, 151.190),
('2067', 'Chatswood West', 'NSW', -33.800, 151.190),
('2068', 'Castlecrag', 'NSW', -33.800, 151.210),
('2068', 'Middle Cove', 'NSW', -33.800, 151.210),
('2068', 'North Willoughby', 'NSW', -33.800, 151.210),
('2068', 'Willoughby', 'NSW', -33.800, 151.210),
('2068', 'Willoughby East', 'NSW', -33.800, 151.210),
('2068', 'Willoughby North', 'NSW', -33.800, 151.210),
('2069', 'Castle Cove', 'NSW', -33.780, 151.200),
('2069', 'Roseville', 'NSW', -33.780, 151.200),
('2069', 'Roseville Chase', 'NSW', -33.780, 151.200),
('2070', 'East Lindfield', 'NSW', -33.770, 151.190),
('2070', 'Lindfield', 'NSW', -33.770, 151.190),
('2070', 'Lindfield West', 'NSW', -33.770, 151.190),
('2071', 'East Killara', 'NSW', -33.750, 151.170),
('2071', 'Killara', 'NSW', -33.750, 151.170),
('2072', 'Gordon', 'NSW', -33.760, 151.160),
('2073', 'Pymble', 'NSW', -33.740, 151.140),
('2073', 'West Pymble', 'NSW', -33.740, 151.140),
('2074', 'North Turramurra', 'NSW', -33.710, 151.150),
('2074', 'South Turramurra', 'NSW', -33.710, 151.150),
('2074', 'Turramurra', 'NSW', -33.710, 151.150),
('2074', 'Warrawee', 'NSW', -33.710, 151.150),
('2075', 'St Ives', 'NSW', -33.730, 151.160),
('2075', 'St Ives Chase', 'NSW', -33.730, 151.160),
('2076', 'Normanhurst', 'NSW', -33.720, 151.100),
('2076', 'North Wahroonga', 'NSW', -33.720, 151.100),
('2076', 'Wahroonga', 'NSW', -33.720, 151.100),
('2077', 'Asquith', 'NSW', -33.690, 151.110),
('2077', 'Hornsby', 'NSW', -33.690, 151.110),
('2077', 'Hornsby Heights', 'NSW', -33.690, 151.110),
('2077', 'Waitara', 'NSW', -33.690, 151.110),
('2079', 'Mount Colah', 'NSW', -33.660, 151.120),
('2080', 'Mount Kuring-Gai', 'NSW', -33.630, 151.230),
('2081', 'Berowra', 'NSW', -33.620, 151.150),
('2081', 'Cowan', 'NSW', -33.620, 151.150),
('2082', 'Berowra Creek', 'NSW', -33.610, 151.140),
('2082', 'Berowra Heights', 'NSW', -33.610, 151.140),
('2082', 'Berowra Waters', 'NSW', -33.610, 151.140),
('2083', 'Bar Point', 'NSW', -33.510, 151.160),
('2083', 'Brooklyn', 'NSW', -33.510, 151.160),
('2083', 'Cheero Point', 'NSW', -33.510, 151.160),
('2083', 'Cogra Bay', 'NSW', -33.510, 151.160),
('2083', 'Dangar Island', 'NSW', -33.510, 151.160),
('2083', 'Milsons Passage', 'NSW', -33.510, 151.160),
('2083', 'Mooney Mooney', 'NSW', -33.510, 151.160),
('2084', 'Cottage Point', 'NSW', -33.620, 151.200),
('2084', 'Duffys Forest', 'NSW', -33.620, 151.200),
('2084', 'Terrey Hills', 'NSW', -33.620, 151.200),
('2085', 'Belrose', 'NSW', -33.740, 151.210),
('2085', 'Belrose West', 'NSW', -33.740, 151.210),
('2085', 'Davidson', 'NSW', -33.740, 151.210),
('2086', 'Frenchs Forest', 'NSW', -33.750, 151.230),
('2086', 'Frenchs Forest East', 'NSW', -33.750, 151.230),
('2087', 'Forestville', 'NSW', -33.760, 151.210),
('2087', 'Killarney Heights', 'NSW', -33.760, 151.210),
('2088', 'Mosman', 'NSW', -33.830, 151.240),
('2088', 'Spit Junction', 'NSW', -33.830, 151.240),
('2089', 'Neutral Bay', 'NSW', -33.830, 151.220),
('2089', 'Neutral Bay Junction', 'NSW', -33.830, 151.220),
('2090', 'Cremorne', 'NSW', -33.830, 151.230),
('2090', 'Cremorne Junction', 'NSW', -33.830, 151.230),
('2090', 'Cremorne Point', 'NSW', -33.830, 151.230),
('2091', 'Hmas Penguin', 'NSW', 0.000, 0.000),
('2092', 'Seaforth', 'NSW', -33.800, 151.250),
('2093', 'Balgowlah', 'NSW', -33.790, 151.260),
('2093', 'Balgowlah Heights', 'NSW', -33.790, 151.260),
('2093', 'Clontarf', 'NSW', -33.790, 151.260),
('2093', 'Manly Vale', 'NSW', -33.790, 151.260),
('2093', 'North Balgowlah', 'NSW', -33.790, 151.260),
('2094', 'Fairlight', 'NSW', -33.790, 151.270),
('2095', 'Manly', 'NSW', -33.800, 151.290),
('2095', 'Manly East', 'NSW', -33.800, 151.290),
('2096', 'Curl Curl', 'NSW', -33.770, 151.290),
('2096', 'Freshwater', 'NSW', -33.770, 151.290),
('2096', 'Queenscliff', 'NSW', -33.770, 151.290),
('2097', 'Collaroy', 'NSW', -33.740, 151.300),
('2097', 'Collaroy Beach', 'NSW', -33.740, 151.300),
('2097', 'Collaroy Plateau West', 'NSW', -33.740, 151.300),
('2097', 'Wheeler Heights', 'NSW', -33.740, 151.300),
('2099', 'Cromer', 'NSW', -33.740, 151.280),
('2099', 'Dee Why', 'NSW', -33.740, 151.280),
('2099', 'Narraweena', 'NSW', -33.740, 151.280),
('2099', 'North Curl Curl', 'NSW', -33.740, 151.280),
('2100', 'Allambie Heights', 'NSW', -33.770, 151.250),
('2100', 'Beacon Hill', 'NSW', -33.770, 151.250),
('2100', 'Brookvale', 'NSW', -33.770, 151.250),
('2100', 'North Manly', 'NSW', -33.770, 151.250),
('2100', 'Oxford Falls', 'NSW', -33.770, 151.250),
('2100', 'Warringah Mall', 'NSW', -33.770, 151.250),
('2101', 'Elanora Heights', 'NSW', -33.700, 151.280),
('2101', 'Ingleside', 'NSW', -33.700, 151.280),
('2101', 'Narrabeen', 'NSW', -33.700, 151.280),
('2101', 'North Narrabeen', 'NSW', -33.700, 151.280),
('2102', 'Warriewood', 'NSW', -33.690, 151.300),
('2102', 'Warriewood Shopping Square', 'NSW', -33.690, 151.300),
('2103', 'Mona Vale', 'NSW', -33.680, 151.300),
('2104', 'Bayview', 'NSW', -33.660, 151.300),
('2105', 'Church Point', 'NSW', -33.640, 151.280),
('2105', 'Elvina Bay', 'NSW', -33.640, 151.280),
('2105', 'Lovett Bay', 'NSW', -33.640, 151.280),
('2105', 'Morning Bay', 'NSW', -33.640, 151.280),
('2105', 'Scotland Island', 'NSW', -33.640, 151.280),
('2106', 'Newport', 'NSW', -33.660, 151.310),
('2106', 'Newport Beach', 'NSW', -33.660, 151.310),
('2107', 'Avalon', 'NSW', -33.640, 151.330),
('2107', 'Avalon Beach', 'NSW', -33.640, 151.330),
('2107', 'Bilgola', 'NSW', -33.640, 151.330),
('2107', 'Clareville', 'NSW', -33.640, 151.330),
('2107', 'Whale Beach', 'NSW', -33.640, 151.330),
('2108', 'Coasters Retreat', 'NSW', -33.600, 151.300),
('2108', 'Great Mackerel Beach', 'NSW', -33.600, 151.300),
('2108', 'Palm Beach', 'NSW', -33.600, 151.300),
('2109', 'Macquarie University', 'NSW', -33.770, 151.110),
('2110', 'Hunters Hill', 'NSW', -33.830, 151.150),
('2110', 'Woolwich', 'NSW', -33.830, 151.150),
('2111', 'Boronia Park', 'NSW', -33.820, 151.140),
('2111', 'Gladesville', 'NSW', -33.820, 151.140),
('2111', 'Henley', 'NSW', -33.820, 151.140),
('2111', 'Huntleys Cove', 'NSW', -33.820, 151.140),
('2111', 'Huntleys Point', 'NSW', -33.820, 151.140),
('2111', 'Monash Park', 'NSW', -33.820, 151.140),
('2111', 'Tennyson Point', 'NSW', -33.820, 151.140),
('2112', 'Denistone East', 'NSW', -33.800, 151.100),
('2112', 'Putney', 'NSW', -33.800, 151.100),
('2112', 'Ryde', 'NSW', -33.800, 151.100),
('2113', 'Blenheim Road', 'NSW', -33.800, 151.130),
('2113', 'East Ryde', 'NSW', -33.800, 151.130),
('2113', 'Macquarie Centre', 'NSW', -33.800, 151.130),
('2113', 'Macquarie Park', 'NSW', -33.800, 151.130),
('2113', 'North Ryde', 'NSW', -33.800, 151.130),
('2114', 'Denistone', 'NSW', -33.800, 151.080),
('2114', 'Denistone West', 'NSW', -33.800, 151.080),
('2114', 'Meadowbank', 'NSW', -33.800, 151.080),
('2114', 'Melrose Park', 'NSW', -33.800, 151.080),
('2114', 'West Ryde', 'NSW', -33.800, 151.080),
('2115', 'Ermington', 'NSW', -33.810, 151.050),
('2116', 'Rydalmere', 'NSW', -33.810, 151.030),
('2117', 'Dundas', 'NSW', -33.800, 151.040),
('2117', 'Dundas Valley', 'NSW', -33.800, 151.040),
('2117', 'Oatlands', 'NSW', -33.800, 151.040),
('2117', 'Telopea', 'NSW', -33.800, 151.040),
('2118', 'Carlingford', 'NSW', -33.780, 151.050),
('2118', 'Carlingford Court', 'NSW', -33.780, 151.050),
('2118', 'Carlingford North', 'NSW', -33.780, 151.050),
('2118', 'Kingsdene', 'NSW', -33.780, 151.050),
('2119', 'Beecroft', 'NSW', -33.750, 151.060),
('2119', 'Cheltenham', 'NSW', -33.750, 151.060),
('2120', 'Pennant Hills', 'NSW', -33.740, 151.070),
('2120', 'Thornleigh', 'NSW', -33.740, 151.070),
('2120', 'Westleigh', 'NSW', -33.740, 151.070),
('2121', 'Epping', 'NSW', -33.770, 151.080),
('2121', 'North Epping', 'NSW', -33.770, 151.080),
('2122', 'Eastwood', 'NSW', -33.790, 151.080),
('2122', 'Marsfield', 'NSW', -33.790, 151.080),
('2123', 'Parramatta', 'NSW', -33.890, 151.140),
('2124', 'Parramatta', 'NSW', -33.890, 151.140),
('2125', 'West Pennant Hills', 'NSW', -33.750, 151.040),
('2126', 'Cherrybrook', 'NSW', -33.720, 151.040),
('2127', 'Newington', 'NSW', -33.850, 151.080),
('2127', 'Sydney Olympic Park', 'NSW', -33.850, 151.080),
('2127', 'Wentworth Point', 'NSW', -33.850, 151.080),
('2128', 'Silverwater', 'NSW', -33.840, 151.050),
('2129', 'Sydney Markets', 'NSW', -33.870, 151.190),
('2130', 'Summer Hill', 'NSW', -33.890, 151.140),
('2131', 'Ashfield', 'NSW', -33.890, 151.130),
('2132', 'Croydon', 'NSW', -33.880, 151.110),
('2133', 'Croydon Park', 'NSW', -33.900, 151.110),
('2133', 'Enfield South', 'NSW', -33.900, 151.110),
('2134', 'Burwood', 'NSW', -33.880, 151.100),
('2134', 'Burwood North', 'NSW', -33.880, 151.100),
('2135', 'Strathfield', 'NSW', -33.870, 151.090),
('2136', 'Burwood Heights', 'NSW', -33.890, 151.100),
('2136', 'Enfield', 'NSW', -33.890, 151.100),
('2136', 'Strathfield South', 'NSW', -33.890, 151.100),
('2137', 'Breakfast Point', 'NSW', -33.840, 151.110),
('2137', 'Cabarita', 'NSW', -33.840, 151.110),
('2137', 'Concord', 'NSW', -33.840, 151.110),
('2137', 'Mortlake', 'NSW', -33.840, 151.110),
('2137', 'North Strathfield', 'NSW', -33.840, 151.110),
('2138', 'Concord West', 'NSW', -33.850, 151.090),
('2138', 'Liberty Grove', 'NSW', -33.850, 151.090),
('2138', 'Rhodes', 'NSW', -33.850, 151.090),
('2139', 'Concord Repatriation Hospital', 'NSW', -33.840, 151.100),
('2140', 'Homebush', 'NSW', -33.860, 151.080),
('2140', 'Homebush South', 'NSW', -33.860, 151.080),
('2140', 'Homebush West', 'NSW', -33.860, 151.080),
('2141', 'Berala', 'NSW', -33.870, 151.030),
('2141', 'Lidcombe', 'NSW', -33.870, 151.030),
('2141', 'Lidcombe North', 'NSW', -33.870, 151.030),
('2141', 'Rookwood', 'NSW', -33.870, 151.030),
('2142', 'Blaxcell', 'NSW', -33.850, 151.010),
('2142', 'Camellia', 'NSW', -33.850, 151.010),
('2142', 'Clyde', 'NSW', -33.850, 151.010),
('2142', 'Granville', 'NSW', -33.850, 151.010),
('2142', 'Holroyd', 'NSW', -33.850, 151.010),
('2142', 'Rosehill', 'NSW', -33.850, 151.010),
('2142', 'South Granville', 'NSW', -33.850, 151.010),
('2143', 'Birrong', 'NSW', -33.890, 151.020),
('2143', 'Potts Hill', 'NSW', -33.890, 151.020),
('2143', 'Regents Park', 'NSW', -33.890, 151.020),
('2144', 'Auburn', 'NSW', -33.850, 151.030),
('2145', 'Constitution Hill', 'NSW', -33.800, 150.950),
('2145', 'Girraween', 'NSW', -33.800, 150.950),
('2145', 'Greystanes', 'NSW', -33.800, 150.950),
('2145', 'Mays Hill', 'NSW', -33.800, 150.950),
('2145', 'Pemulwuy', 'NSW', -33.800, 150.950),
('2145', 'Pendle Hill', 'NSW', -33.800, 150.950),
('2145', 'South Wentworthville', 'NSW', -33.800, 150.950),
('2145', 'Wentworthville', 'NSW', -33.800, 150.950),
('2145', 'Westmead', 'NSW', -33.800, 150.950),
('2146', 'Old Toongabbie', 'NSW', -33.790, 150.970),
('2146', 'Toongabbie', 'NSW', -33.790, 150.970),
('2146', 'Toongabbie East', 'NSW', -33.790, 150.970),
('2147', 'Kings Langley', 'NSW', -33.740, 150.920),
('2147', 'Lalor Park', 'NSW', -33.740, 150.920),
('2147', 'Seven Hills', 'NSW', -33.740, 150.920),
('2147', 'Seven Hills West', 'NSW', -33.740, 150.920),
('2148', 'Arndell Park', 'NSW', -33.790, 150.870),
('2148', 'Blacktown', 'NSW', -33.790, 150.870),
('2148', 'Blacktown Westpoint', 'NSW', -33.790, 150.870),
('2148', 'Huntingwood', 'NSW', -33.790, 150.870),
('2148', 'Kings Park', 'NSW', -33.790, 150.870),
('2148', 'Marayong', 'NSW', -33.790, 150.870),
('2148', 'Prospect', 'NSW', -33.790, 150.870),
('2150', 'Harris Park', 'NSW', -33.820, 151.010),
('2150', 'Parramatta', 'NSW', -33.820, 151.010),
('2150', 'Parramatta Westfield', 'NSW', -33.820, 151.010),
('2151', 'North Parramatta', 'NSW', -33.800, 151.010),
('2151', 'North Rocks', 'NSW', -33.800, 151.010),
('2152', 'Northmead', 'NSW', -33.780, 150.990),
('2153', 'Baulkham Hills', 'NSW', -33.760, 150.990),
('2153', 'Bella Vista', 'NSW', -33.760, 150.990),
('2153', 'Winston Hills', 'NSW', -33.760, 150.990),
('2154', 'Castle Hill', 'NSW', -33.730, 151.010),
('2155', 'Beaumont Hills', 'NSW', -33.700, 150.950),
('2155', 'Kellyville', 'NSW', -33.700, 150.950),
('2155', 'Kellyville Ridge', 'NSW', -33.700, 150.950),
('2155', 'Rouse Hill', 'NSW', -33.700, 150.950),
('2156', 'Annangrove', 'NSW', -33.660, 150.940),
('2156', 'Glenhaven', 'NSW', -33.660, 150.940),
('2156', 'Kenthurst', 'NSW', -33.660, 150.940),
('2157', 'Canoelands', 'NSW', -33.510, 151.060),
('2157', 'Forest Glen', 'NSW', -33.510, 151.060),
('2157', 'Glenorie', 'NSW', -33.510, 151.060),
('2158', 'Dural', 'NSW', -33.680, 151.030),
('2158', 'Middle Dural', 'NSW', -33.680, 151.030),
('2158', 'Round Corner', 'NSW', -33.680, 151.030),
('2159', 'Arcadia', 'NSW', -33.620, 151.050),
('2159', 'Berrilee', 'NSW', -33.620, 151.050),
('2159', 'Fiddletown', 'NSW', -33.620, 151.050),
('2159', 'Galston', 'NSW', -33.620, 151.050),
('2160', 'Merrylands', 'NSW', -33.840, 150.990),
('2160', 'Merrylands West', 'NSW', -33.840, 150.990),
('2161', 'Guildford', 'NSW', -33.850, 150.990),
('2161', 'Guildford West', 'NSW', -33.850, 150.990),
('2161', 'Old Guildford', 'NSW', -33.850, 150.990),
('2161', 'Yennora', 'NSW', -33.850, 150.990),
('2162', 'Chester Hill', 'NSW', -33.880, 151.000),
('2162', 'Sefton', 'NSW', -33.880, 151.000),
('2163', 'Carramar', 'NSW', -33.880, 150.960),
('2163', 'Lansdowne', 'NSW', -33.880, 150.960),
('2163', 'Villawood', 'NSW', -33.880, 150.960),
('2164', 'Smithfield', 'NSW', -33.850, 150.940),
('2164', 'Smithfield West', 'NSW', -33.850, 150.940),
('2164', 'Wetherill Park', 'NSW', -33.850, 150.940),
('2164', 'Woodpark', 'NSW', -33.850, 150.940),
('2165', 'Fairfield', 'NSW', -33.870, 150.960),
('2165', 'Fairfield East', 'NSW', -33.870, 150.960),
('2165', 'Fairfield Heights', 'NSW', -33.870, 150.960),
('2165', 'Fairfield West', 'NSW', -33.870, 150.960),
('2166', 'Cabramatta', 'NSW', -33.900, 150.940),
('2166', 'Cabramatta West', 'NSW', -33.900, 150.940),
('2166', 'Canley Heights', 'NSW', -33.900, 150.940),
('2166', 'Canley Vale', 'NSW', -33.900, 150.940),
('2166', 'Lansvale', 'NSW', -33.900, 150.940),
('2167', 'Glenfield', 'NSW', -33.970, 150.890),
('2168', 'Ashcroft', 'NSW', -33.920, 150.900),
('2168', 'Busby', 'NSW', -33.920, 150.900),
('2168', 'Cartwright', 'NSW', -33.920, 150.900),
('2168', 'Green Valley', 'NSW', -33.920, 150.900),
('2168', 'Heckenberg', 'NSW', -33.920, 150.900),
('2168', 'Hinchinbrook', 'NSW', -33.920, 150.900),
('2168', 'Miller', 'NSW', -33.920, 150.900),
('2168', 'Sadleir', 'NSW', -33.920, 150.900),
('2170', 'Casula', 'NSW', -33.950, 150.910),
('2170', 'Casula Mall', 'NSW', -33.950, 150.910),
('2170', 'Chipping Norton', 'NSW', -33.950, 150.910),
('2170', 'Hammondville', 'NSW', -33.950, 150.910),
('2170', 'Liverpool', 'NSW', -33.950, 150.910),
('2170', 'Liverpool South', 'NSW', -33.950, 150.910),
('2170', 'Liverpool Westfield', 'NSW', -33.950, 150.910),
('2170', 'Lurnea', 'NSW', -33.950, 150.910),
('2170', 'Moorebank', 'NSW', -33.950, 150.910),
('2170', 'Mount Pritchard', 'NSW', -33.950, 150.910),
('2170', 'Prestons', 'NSW', -33.950, 150.910),
('2170', 'Warwick Farm', 'NSW', -33.950, 150.910),
('2171', 'Cecil Hills', 'NSW', -33.880, 150.850),
('2171', 'Elizabeth Hills', 'NSW', -33.880, 150.850),
('2171', 'Horningsea Park', 'NSW', -33.880, 150.850),
('2171', 'Hoxton Park', 'NSW', -33.880, 150.850),
('2171', 'Len Waters Estate', 'NSW', -33.880, 150.850),
('2171', 'Middleton Grange', 'NSW', -33.880, 150.850),
('2171', 'West Hoxton', 'NSW', -33.880, 150.850),
('2172', 'Pleasure Point', 'NSW', -33.970, 150.990),
('2172', 'Sandy Point', 'NSW', -33.970, 150.990),
('2172', 'Voyager Point', 'NSW', -33.970, 150.990),
('2173', 'Holsworthy', 'NSW', -33.950, 150.950),
('2173', 'Wattle Grove', 'NSW', -33.950, 150.950),
('2174', 'Edmondson Park', 'NSW', 0.000, 0.000),
('2174', 'Ingleburn Milpo', 'NSW', 0.000, 0.000),
('2175', 'Horsley Park', 'NSW', -33.850, 150.850),
('2176', 'Abbotsbury', 'NSW', -33.880, 150.870),
('2176', 'Bossley Park', 'NSW', -33.880, 150.870),
('2176', 'Edensor Park', 'NSW', -33.880, 150.870),
('2176', 'Greenfield Park', 'NSW', -33.880, 150.870),
('2176', 'Prairiewood', 'NSW', -33.880, 150.870),
('2176', 'St Johns Park', 'NSW', -33.880, 150.870),
('2176', 'Wakeley', 'NSW', -33.880, 150.870),
('2177', 'Bonnyrigg', 'NSW', -33.890, 150.890),
('2177', 'Bonnyrigg Heights', 'NSW', -33.890, 150.890),
('2178', 'Cecil Park', 'NSW', -33.870, 150.840),
('2178', 'Kemps Creek', 'NSW', -33.870, 150.840),
('2178', 'Mount Vernon', 'NSW', -33.870, 150.840),
('2179', 'Austral', 'NSW', -33.930, 150.810),
('2179', 'Leppington', 'NSW', -33.930, 150.810),
('2190', 'Chullora', 'NSW', -33.890, 151.060),
('2190', 'Greenacre', 'NSW', -33.890, 151.060),
('2190', 'Mount Lewis', 'NSW', -33.890, 151.060),
('2191', 'Belfield', 'NSW', -33.900, 151.080),
('2192', 'Belmore', 'NSW', -33.920, 151.090),
('2193', 'Ashbury', 'NSW', -33.900, 151.120),
('2193', 'Canterbury', 'NSW', -33.900, 151.120),
('2193', 'Hurlstone Park', 'NSW', -33.900, 151.120),
('2194', 'Campsie', 'NSW', -33.910, 151.100),
('2195', 'Lakemba', 'NSW', -33.920, 151.080),
('2195', 'Wiley Park', 'NSW', -33.920, 151.080),
('2196', 'Punchbowl', 'NSW', -33.930, 151.050),
('2196', 'Roselands', 'NSW', -33.930, 151.050),
('2197', 'Bass Hill', 'NSW', -33.900, 150.990),
('2198', 'Georges Hall', 'NSW', -33.910, 150.980),
('2199', 'Yagoona', 'NSW', -33.910, 151.030),
('2199', 'Yagoona West', 'NSW', -33.910, 151.030),
('2200', 'Bankstown', 'NSW', -33.920, 151.030),
('2200', 'Bankstown Aerodrome', 'NSW', -33.920, 151.030),
('2200', 'Bankstown North', 'NSW', -33.920, 151.030),
('2200', 'Bankstown Square', 'NSW', -33.920, 151.030),
('2200', 'Condell Park', 'NSW', -33.920, 151.030),
('2200', 'Manahan', 'NSW', -33.920, 151.030),
('2200', 'Mount Lewis', 'NSW', -33.920, 151.030),
('2203', 'Dulwich Hill', 'NSW', -33.900, 151.140),
('2204', 'Marrickville', 'NSW', -33.910, 151.160),
('2204', 'Marrickville Metro', 'NSW', -33.910, 151.160),
('2204', 'Marrickville South', 'NSW', -33.910, 151.160),
('2205', 'Arncliffe', 'NSW', -33.940, 151.150),
('2205', 'Turrella', 'NSW', -33.940, 151.150),
('2205', 'Wolli Creek', 'NSW', -33.940, 151.150),
('2206', 'Clemton Park', 'NSW', -33.930, 151.100),
('2206', 'Earlwood', 'NSW', -33.930, 151.100),
('2207', 'Bardwell Park', 'NSW', -33.930, 151.130),
('2207', 'Bardwell Valley', 'NSW', -33.930, 151.130),
('2207', 'Bexley', 'NSW', -33.930, 151.130),
('2207', 'Bexley North', 'NSW', -33.930, 151.130),
('2207', 'Bexley South', 'NSW', -33.930, 151.130),
('2208', 'Kingsgrove', 'NSW', -33.940, 151.100),
('2208', 'Kingsway West', 'NSW', -33.940, 151.100),
('2209', 'Beverly Hills', 'NSW', -33.950, 151.080),
('2209', 'Narwee', 'NSW', -33.950, 151.080),
('2210', 'Lugarno', 'NSW', -33.980, 151.050),
('2210', 'Peakhurst', 'NSW', -33.980, 151.050),
('2210', 'Peakhurst Heights', 'NSW', -33.980, 151.050),
('2210', 'Riverwood', 'NSW', -33.980, 151.050),
('2211', 'Padstow', 'NSW', -33.950, 151.040),
('2211', 'Padstow Heights', 'NSW', -33.950, 151.040),
('2212', 'Revesby', 'NSW', -33.950, 151.020),
('2212', 'Revesby Heights', 'NSW', -33.950, 151.020),
('2212', 'Revesby North', 'NSW', -33.950, 151.020),
('2213', 'East Hills', 'NSW', -33.960, 150.990),
('2213', 'Panania', 'NSW', -33.960, 150.990),
('2213', 'Picnic Point', 'NSW', -33.960, 150.990),
('2214', 'Milperra', 'NSW', -33.940, 150.990),
('2216', 'Banksia', 'NSW', -33.950, 151.140),
('2216', 'Brighton-Le-Sands', 'NSW', -33.950, 151.140),
('2216', 'Kyeemagh', 'NSW', -33.950, 151.140),
('2216', 'Rockdale', 'NSW', -33.950, 151.140),
('2217', 'Beverley Park', 'NSW', -33.980, 151.130),
('2217', 'Kogarah', 'NSW', -33.980, 151.130),
('2217', 'Kogarah Bay', 'NSW', -33.980, 151.130),
('2217', 'Monterey', 'NSW', -33.980, 151.130),
('2217', 'Ramsgate', 'NSW', -33.980, 151.130),
('2217', 'Ramsgate Beach', 'NSW', -33.980, 151.130),
('2218', 'Allawah', 'NSW', -33.970, 151.110),
('2218', 'Carlton', 'NSW', -33.970, 151.110),
('2219', 'Dolls Point', 'NSW', -33.990, 151.150),
('2219', 'Sandringham', 'NSW', -33.990, 151.150),
('2219', 'Sans Souci', 'NSW', -33.990, 151.150),
('2220', 'Hurstville', 'NSW', -33.970, 151.100),
('2220', 'Hurstville Grove', 'NSW', -33.970, 151.100),
('2220', 'Hurstville Westfield', 'NSW', -33.970, 151.100),
('2221', 'Blakehurst', 'NSW', -33.990, 151.110),
('2221', 'Carss Park', 'NSW', -33.990, 151.110),
('2221', 'Connells Point', 'NSW', -33.990, 151.110),
('2221', 'Kyle Bay', 'NSW', -33.990, 151.110),
('2221', 'South Hurstville', 'NSW', -33.990, 151.110),
('2222', 'Penshurst', 'NSW', -33.960, 151.090),
('2223', 'Mortdale', 'NSW', -33.970, 151.080),
('2223', 'Oatley', 'NSW', -33.970, 151.080),
('2224', 'Kangaroo Point', 'NSW', -34.000, 151.100),
('2224', 'Sylvania', 'NSW', -34.000, 151.100),
('2224', 'Sylvania Southgate', 'NSW', -34.000, 151.100),
('2224', 'Sylvania Waters', 'NSW', -34.000, 151.100),
('2225', 'Oyster Bay', 'NSW', -34.000, 151.090),
('2226', 'Bonnet Bay', 'NSW', -34.010, 151.050),
('2226', 'Como', 'NSW', -34.010, 151.050),
('2226', 'Jannali', 'NSW', -34.010, 151.050),
('2227', 'Gymea', 'NSW', -34.030, 151.090),
('2227', 'Gymea Bay', 'NSW', -34.030, 151.090),
('2228', 'Miranda', 'NSW', -34.030, 151.100),
('2228', 'Yowie Bay', 'NSW', -34.030, 151.100),
('2229', 'Caringbah', 'NSW', -34.040, 151.120),
('2229', 'Caringbah South', 'NSW', -34.040, 151.120),
('2229', 'Dolans Bay', 'NSW', -34.040, 151.120),
('2229', 'Lilli Pilli', 'NSW', -34.040, 151.120),
('2229', 'Port Hacking', 'NSW', -34.040, 151.120),
('2229', 'Taren Point', 'NSW', -34.040, 151.120),
('2230', 'Bundeena', 'NSW', -34.090, 151.150),
('2230', 'Burraneer', 'NSW', -34.090, 151.150),
('2230', 'Cronulla', 'NSW', -34.090, 151.150),
('2230', 'Maianbar', 'NSW', -34.090, 151.150),
('2230', 'Woolooware', 'NSW', -34.090, 151.150),
('2231', 'Kurnell', 'NSW', -34.010, 151.200),
('2232', 'Audley', 'NSW', -34.080, 151.060),
('2232', 'Garie', 'NSW', -34.080, 151.060),
('2232', 'Grays Point', 'NSW', -34.080, 151.060),
('2232', 'Kareela', 'NSW', -34.080, 151.060),
('2232', 'Kirrawee', 'NSW', -34.080, 151.060),
('2232', 'Loftus', 'NSW', -34.080, 151.060),
('2232', 'Sutherland', 'NSW', -34.080, 151.060),
('2232', 'Woronora', 'NSW', -34.080, 151.060),
('2233', 'Engadine', 'NSW', -34.070, 151.010),
('2233', 'Heathcote', 'NSW', -34.070, 151.010),
('2233', 'Waterfall', 'NSW', -34.070, 151.010),
('2233', 'Woronora Heights', 'NSW', -34.070, 151.010),
('2233', 'Yarrawarrah', 'NSW', -34.070, 151.010),
('2234', 'Alfords Point', 'NSW', -33.990, 151.020),
('2234', 'Bangor', 'NSW', -33.990, 151.020),
('2234', 'Barden Ridge', 'NSW', -33.990, 151.020),
('2234', 'Illawong', 'NSW', -33.990, 151.020),
('2234', 'Lucas Heights', 'NSW', -33.990, 151.020),
('2234', 'Menai', 'NSW', -33.990, 151.020),
('2234', 'Menai Central', 'NSW', -33.990, 151.020),
('2250', 'Bucketty', 'NSW', -33.110, 151.140),
('2250', 'Calga', 'NSW', -33.110, 151.140),
('2250', 'Central Mangrove', 'NSW', -33.110, 151.140),
('2250', 'East Gosford', 'NSW', -33.110, 151.140),
('2250', 'Erina', 'NSW', -33.110, 151.140),
('2250', 'Erina Fair', 'NSW', -33.110, 151.140),
('2250', 'Glenworth Valley', 'NSW', -33.110, 151.140),
('2250', 'Gosford', 'NSW', -33.110, 151.140),
('2250', 'Greengrove', 'NSW', -33.110, 151.140),
('2250', 'Holgate', 'NSW', -33.110, 151.140),
('2250', 'Kariong', 'NSW', -33.110, 151.140),
('2250', 'Kulnura', 'NSW', -33.110, 151.140),
('2250', 'Lisarow', 'NSW', -33.110, 151.140),
('2250', 'Lower Mangrove', 'NSW', -33.110, 151.140),
('2250', 'Mangrove Creek', 'NSW', -33.110, 151.140),
('2250', 'Mangrove Mountain', 'NSW', -33.110, 151.140),
('2250', 'Matcham', 'NSW', -33.110, 151.140),
('2250', 'Mooney Mooney Creek', 'NSW', -33.110, 151.140),
('2250', 'Mount Elliot', 'NSW', -33.110, 151.140),
('2250', 'Mount White', 'NSW', -33.110, 151.140),
('2250', 'Narara', 'NSW', -33.110, 151.140),
('2250', 'Niagara Park', 'NSW', -33.110, 151.140),
('2250', 'North Gosford', 'NSW', -33.110, 151.140),
('2250', 'Peats Ridge', 'NSW', -33.110, 151.140),
('2250', 'Point Clare', 'NSW', -33.110, 151.140),
('2250', 'Point Frederick', 'NSW', -33.110, 151.140),
('2250', 'Somersby', 'NSW', -33.110, 151.140),
('2250', 'Springfield', 'NSW', -33.110, 151.140),
('2250', 'Tascott', 'NSW', -33.110, 151.140),
('2250', 'Ten Mile Hollow', 'NSW', -33.110, 151.140),
('2250', 'Upper Mangrove', 'NSW', -33.110, 151.140),
('2250', 'Wendoree Park', 'NSW', -33.110, 151.140),
('2250', 'West Gosford', 'NSW', -33.110, 151.140),
('2250', 'Wyoming', 'NSW', -33.110, 151.140),
('2251', 'Avoca Beach', 'NSW', -33.460, 151.430),
('2251', 'Bensville', 'NSW', -33.460, 151.430),
('2251', 'Bouddi', 'NSW', -33.460, 151.430),
('2251', 'Copacabana', 'NSW', -33.460, 151.430),
('2251', 'Davistown', 'NSW', -33.460, 151.430),
('2251', 'Green Point', 'NSW', -33.460, 151.430),
('2251', 'Kincumber', 'NSW', -33.460, 151.430),
('2251', 'Kincumber South', 'NSW', -33.460, 151.430),
('2251', 'Macmasters Beach', 'NSW', -33.460, 151.430),
('2251', 'Picketts Valley', 'NSW', -33.460, 151.430),
('2251', 'Saratoga', 'NSW', -33.460, 151.430),
('2251', 'Yattalunga', 'NSW', -33.460, 151.430),
('2252', 'Central Coast MC', 'NSW', 0.000, 0.000),
('2256', 'Blackwall', 'NSW', -33.500, 151.330),
('2256', 'Horsfield Bay', 'NSW', -33.500, 151.330),
('2256', 'Koolewong', 'NSW', -33.500, 151.330),
('2256', 'Little Wobby', 'NSW', -33.500, 151.330),
('2256', 'Patonga', 'NSW', -33.500, 151.330),
('2256', 'Pearl Beach', 'NSW', -33.500, 151.330),
('2256', 'Phegans Bay', 'NSW', -33.500, 151.330),
('2256', 'Wondabyne', 'NSW', -33.500, 151.330),
('2256', 'Woy Woy', 'NSW', -33.500, 151.330),
('2256', 'Woy Woy Bay', 'NSW', -33.500, 151.330),
('2257', 'Booker Bay', 'NSW', -33.510, 151.340),
('2257', 'Box Head', 'NSW', -33.510, 151.340),
('2257', 'Daleys Point', 'NSW', -33.510, 151.340),
('2257', 'Empire Bay', 'NSW', -33.510, 151.340),
('2257', 'Ettalong Beach', 'NSW', -33.510, 151.340),
('2257', 'Hardys Bay', 'NSW', -33.510, 151.340),
('2257', 'Killcare', 'NSW', -33.510, 151.340),
('2257', 'Killcare Heights', 'NSW', -33.510, 151.340),
('2257', 'Pretty Beach', 'NSW', -33.510, 151.340),
('2257', 'St Huberts Island', 'NSW', -33.510, 151.340),
('2257', 'Umina Beach', 'NSW', -33.510, 151.340),
('2257', 'Wagstaffe', 'NSW', -33.510, 151.340),
('2258', 'Fountaindale', 'NSW', -33.340, 151.390),
('2258', 'Kangy Angy', 'NSW', -33.340, 151.390),
('2258', 'Ourimbah', 'NSW', -33.340, 151.390),
('2258', 'Palm Grove', 'NSW', -33.340, 151.390),
('2258', 'Palmdale', 'NSW', -33.340, 151.390),
('2259', 'Alison', 'NSW', -33.270, 151.400),
('2259', 'Bushells Ridge', 'NSW', -33.270, 151.400),
('2259', 'Cedar Brush Creek', 'NSW', -33.270, 151.400),
('2259', 'Chain Valley Bay', 'NSW', -33.270, 151.400),
('2259', 'Crangan Bay', 'NSW', -33.270, 151.400),
('2259', 'Dooralong', 'NSW', -33.270, 151.400),
('2259', 'Durren Durren', 'NSW', -33.270, 151.400),
('2259', 'Frazer Park', 'NSW', -33.270, 151.400),
('2259', 'Freemans', 'NSW', -33.270, 151.400),
('2259', 'Gwandalan', 'NSW', -33.270, 151.400),
('2259', 'Halloran', 'NSW', -33.270, 151.400),
('2259', 'Hamlyn Terrace', 'NSW', -33.270, 151.400),
('2259', 'Jilliby', 'NSW', -33.270, 151.400),
('2259', 'Kanwal', 'NSW', -33.270, 151.400),
('2259', 'Kiar', 'NSW', -33.270, 151.400),
('2259', 'Kingfisher Shores', 'NSW', -33.270, 151.400),
('2259', 'Lake Munmorah', 'NSW', -33.270, 151.400),
('2259', 'Lemon Tree', 'NSW', -33.270, 151.400),
('2259', 'Little Jilliby', 'NSW', -33.270, 151.400),
('2259', 'Mannering Park', 'NSW', -33.270, 151.400),
('2259', 'Mardi', 'NSW', -33.270, 151.400),
('2259', 'Moonee', 'NSW', -33.270, 151.400),
('2259', 'Point Wolstoncroft', 'NSW', -33.270, 151.400),
('2259', 'Ravensdale', 'NSW', -33.270, 151.400),
('2259', 'Rocky Point', 'NSW', -33.270, 151.400),
('2259', 'Summerland Point', 'NSW', -33.270, 151.400),
('2259', 'Tacoma', 'NSW', -33.270, 151.400),
('2259', 'Tacoma South', 'NSW', -33.270, 151.400),
('2259', 'Tuggerah', 'NSW', -33.270, 151.400),
('2259', 'Tuggerawong', 'NSW', -33.270, 151.400),
('2259', 'Wadalba', 'NSW', -33.270, 151.400),
('2259', 'Wallarah', 'NSW', -33.270, 151.400),
('2259', 'Warnervale', 'NSW', -33.270, 151.400),
('2259', 'Watanobbi', 'NSW', -33.270, 151.400),
('2259', 'Woongarrah', 'NSW', -33.270, 151.400),
('2259', 'Wybung', 'NSW', -33.270, 151.400),
('2259', 'Wyee', 'NSW', -33.270, 151.400),
('2259', 'Wyee Point', 'NSW', -33.270, 151.400),
('2259', 'Wyong', 'NSW', -33.270, 151.400),
('2259', 'Wyong Creek', 'NSW', -33.270, 151.400),
('2259', 'Wyongah', 'NSW', -33.270, 151.400),
('2259', 'Yarramalong', 'NSW', -33.270, 151.400),
('2260', 'Erina Heights', 'NSW', -33.430, 151.410),
('2260', 'Forresters Beach', 'NSW', -33.430, 151.410),
('2260', 'North Avoca', 'NSW', -33.430, 151.410),
('2260', 'Terrigal', 'NSW', -33.430, 151.410),
('2260', 'Wamberal', 'NSW', -33.430, 151.410),
('2261', 'Bateau Bay', 'NSW', -33.380, 151.480),
('2261', 'Bay Village', 'NSW', -33.380, 151.480),
('2261', 'Berkeley Vale', 'NSW', -33.380, 151.480),
('2261', 'Blue Bay', 'NSW', -33.380, 151.480),
('2261', 'Chittaway Bay', 'NSW', -33.380, 151.480),
('2261', 'Chittaway Point', 'NSW', -33.380, 151.480),
('2261', 'Glenning Valley', 'NSW', -33.380, 151.480),
('2261', 'Killarney Vale', 'NSW', -33.380, 151.480),
('2261', 'Long Jetty', 'NSW', -33.380, 151.480),
('2261', 'Magenta', 'NSW', -33.380, 151.480),
('2261', 'Shelly Beach', 'NSW', -33.380, 151.480),
('2261', 'The Entrance', 'NSW', -33.380, 151.480),
('2261', 'The Entrance North', 'NSW', -33.380, 151.480),
('2261', 'Toowoon Bay', 'NSW', -33.380, 151.480),
('2261', 'Tumbi Umbi', 'NSW', -33.380, 151.480),
('2262', 'Blue Haven', 'NSW', -33.210, 151.490),
('2262', 'Budgewoi', 'NSW', -33.210, 151.490),
('2262', 'Budgewoi Peninsula', 'NSW', -33.210, 151.490),
('2262', 'Buff Point', 'NSW', -33.210, 151.490),
('2262', 'Colongra', 'NSW', -33.210, 151.490),
('2262', 'Doyalson', 'NSW', -33.210, 151.490),
('2262', 'Doyalson North', 'NSW', -33.210, 151.490),
('2262', 'Halekulani', 'NSW', -33.210, 151.490),
('2262', 'San Remo', 'NSW', -33.210, 151.490),
('2263', 'Canton Beach', 'NSW', -33.270, 151.540),
('2263', 'Charmhaven', 'NSW', -33.270, 151.540),
('2263', 'Gorokan', 'NSW', -33.270, 151.540),
('2263', 'Lake Haven', 'NSW', -33.270, 151.540),
('2263', 'Norah Head', 'NSW', -33.270, 151.540),
('2263', 'Noraville', 'NSW', -33.270, 151.540),
('2263', 'Toukley', 'NSW', -33.270, 151.540),
('2264', 'Balcolyn', 'NSW', -33.100, 151.550),
('2264', 'Bonnells Bay', 'NSW', -33.100, 151.550),
('2264', 'Brightwaters', 'NSW', -33.100, 151.550),
('2264', 'Dora Creek', 'NSW', -33.100, 151.550),
('2264', 'Eraring', 'NSW', -33.100, 151.550),
('2264', 'Mandalong', 'NSW', -33.100, 151.550),
('2264', 'Mirrabooka', 'NSW', -33.100, 151.550),
('2264', 'Morisset', 'NSW', -33.100, 151.550),
('2264', 'Morisset Park', 'NSW', -33.100, 151.550),
('2264', 'Myuna Bay', 'NSW', -33.100, 151.550),
('2264', 'Silverwater', 'NSW', -33.100, 151.550),
('2264', 'Sunshine', 'NSW', -33.100, 151.550),
('2264', 'Windermere Park', 'NSW', -33.100, 151.550),
('2264', 'Yarrawonga Park', 'NSW', -33.100, 151.550),
('2265', 'Cooranbong', 'NSW', -33.080, 151.450),
('2265', 'Martinsville', 'NSW', -33.080, 151.450),
('2267', 'Wangi Wangi', 'NSW', -33.070, 151.580),
('2278', 'Barnsley', 'NSW', -32.930, 151.590),
('2278', 'Killingworth', 'NSW', -32.930, 151.590),
('2278', 'Wakefield', 'NSW', -32.930, 151.590),
('2280', 'Belmont', 'NSW', -33.040, 151.660),
('2280', 'Belmont North', 'NSW', -33.040, 151.660),
('2280', 'Belmont South', 'NSW', -33.040, 151.660),
('2280', 'Croudace Bay', 'NSW', -33.040, 151.660),
('2280', 'Floraville', 'NSW', -33.040, 151.660),
('2280', 'Jewells', 'NSW', -33.040, 151.660),
('2280', 'Marks Point', 'NSW', -33.040, 151.660),
('2280', 'Valentine', 'NSW', -33.040, 151.660),
('2281', 'Blacksmiths', 'NSW', -33.080, 151.650),
('2281', 'Cams Wharf', 'NSW', -33.080, 151.650),
('2281', 'Catherine Hill Bay', 'NSW', -33.080, 151.650),
('2281', 'Caves Beach', 'NSW', -33.080, 151.650),
('2281', 'Little Pelican', 'NSW', -33.080, 151.650),
('2281', 'Murrays Beach', 'NSW', -33.080, 151.650),
('2281', 'Nords Wharf', 'NSW', -33.080, 151.650),
('2281', 'Pelican', 'NSW', -33.080, 151.650),
('2281', 'Pinny Beach', 'NSW', -33.080, 151.650),
('2281', 'Swansea', 'NSW', -33.080, 151.650),
('2281', 'Swansea Heads', 'NSW', -33.080, 151.650),
('2282', 'Eleebana', 'NSW', -32.990, 151.640),
('2282', 'Lakelands', 'NSW', -32.990, 151.640),
('2282', 'Warners Bay', 'NSW', -32.990, 151.640),
('2283', 'Arcadia Vale', 'NSW', -33.060, 151.580),
('2283', 'Awaba', 'NSW', -33.060, 151.580),
('2283', 'Balmoral', 'NSW', -33.060, 151.580),
('2283', 'Blackalls Park', 'NSW', -33.060, 151.580),
('2283', 'Bolton Point', 'NSW', -33.060, 151.580),
('2283', 'Buttaba', 'NSW', -33.060, 151.580),
('2283', 'Carey Bay', 'NSW', -33.060, 151.580),
('2283', 'Coal Point', 'NSW', -33.060, 151.580),
('2283', 'Fassifern', 'NSW', -33.060, 151.580),
('2283', 'Fennell Bay', 'NSW', -33.060, 151.580),
('2283', 'Fishing Point', 'NSW', -33.060, 151.580),
('2283', 'Kilaben Bay', 'NSW', -33.060, 151.580),
('2283', 'Rathmines', 'NSW', -33.060, 151.580),
('2283', 'Ryhope', 'NSW', -33.060, 151.580),
('2283', 'Toronto', 'NSW', -33.060, 151.580),
('2284', 'Argenton', 'NSW', -32.930, 151.630),
('2284', 'Boolaroo', 'NSW', -32.930, 151.630),
('2284', 'Booragul', 'NSW', -32.930, 151.630),
('2284', 'Marmong Point', 'NSW', -32.930, 151.630),
('2284', 'Speers Point', 'NSW', -32.930, 151.630),
('2284', 'Teralba', 'NSW', -32.930, 151.630),
('2284', 'Woodrising', 'NSW', -32.930, 151.630),
('2285', 'Cameron Park', 'NSW', -32.930, 151.660),
('2285', 'Cardiff', 'NSW', -32.930, 151.660),
('2285', 'Cardiff Heights', 'NSW', -32.930, 151.660),
('2285', 'Cardiff South', 'NSW', -32.930, 151.660),
('2285', 'Edgeworth', 'NSW', -32.930, 151.660),
('2285', 'Glendale', 'NSW', -32.930, 151.660),
('2285', 'Macquarie Hills', 'NSW', -32.930, 151.660),
('2286', 'Holmesville', 'NSW', -32.910, 151.580),
('2286', 'Seahampton', 'NSW', -32.910, 151.580),
('2286', 'West Wallsend', 'NSW', -32.910, 151.580),
('2287', 'Birmingham Gardens', 'NSW', -32.890, 151.690),
('2287', 'Elermore Vale', 'NSW', -32.890, 151.690),
('2287', 'Fletcher', 'NSW', -32.890, 151.690),
('2287', 'Maryland', 'NSW', -32.890, 151.690),
('2287', 'Minmi', 'NSW', -32.890, 151.690),
('2287', 'Rankin Park', 'NSW', -32.890, 151.690),
('2287', 'Wallsend', 'NSW', -32.890, 151.690),
('2287', 'Wallsend South', 'NSW', -32.890, 151.690),
('2289', 'Adamstown', 'NSW', -32.930, 151.730),
('2289', 'Adamstown Heights', 'NSW', -32.930, 151.730),
('2289', 'Garden Suburb', 'NSW', -32.930, 151.730),
('2289', 'Highfields', 'NSW', -32.930, 151.730),
('2289', 'Kotara', 'NSW', -32.930, 151.730),
('2289', 'Kotara Fair', 'NSW', -32.930, 151.730),
('2289', 'Kotara South', 'NSW', -32.930, 151.730),
('2290', 'Bennetts Green', 'NSW', -33.000, 151.690),
('2290', 'Charlestown', 'NSW', -33.000, 151.690),
('2290', 'Dudley', 'NSW', -33.000, 151.690),
('2290', 'Gateshead', 'NSW', -33.000, 151.690),
('2290', 'Hillsborough', 'NSW', -33.000, 151.690),
('2290', 'Kahibah', 'NSW', -33.000, 151.690),
('2290', 'Mount Hutton', 'NSW', -33.000, 151.690),
('2290', 'Redhead', 'NSW', -33.000, 151.690),
('2290', 'Tingira Heights', 'NSW', -33.000, 151.690),
('2290', 'Whitebridge', 'NSW', -33.000, 151.690),
('2291', 'Merewether', 'NSW', -32.940, 151.750),
('2291', 'Merewether Heights', 'NSW', -32.940, 151.750),
('2291', 'The Junction', 'NSW', -32.940, 151.750),
('2292', 'Broadmeadow', 'NSW', -32.920, 151.740),
('2292', 'Hamilton North', 'NSW', -32.920, 151.740),
('2293', 'Maryville', 'NSW', -32.910, 151.750),
('2293', 'Wickham', 'NSW', -32.910, 151.750),
('2294', 'Carrington', 'NSW', -32.920, 151.770),
('2295', 'Fern Bay', 'NSW', -32.850, 151.810),
('2295', 'Stockton', 'NSW', -32.850, 151.810),
('2296', 'Islington', 'NSW', -32.910, 151.750),
('2297', 'Tighes Hill', 'NSW', -32.910, 151.750),
('2298', 'Georgetown', 'NSW', -32.910, 151.730),
('2298', 'Waratah', 'NSW', -32.910, 151.730),
('2298', 'Waratah West', 'NSW', -32.910, 151.730),
('2299', 'Jesmond', 'NSW', -32.900, 151.690),
('2299', 'Lambton', 'NSW', -32.900, 151.690),
('2299', 'North Lambton', 'NSW', -32.900, 151.690),
('2300', 'Bar Beach', 'NSW', -32.940, 151.770),
('2300', 'Cooks Hill', 'NSW', -32.940, 151.770),
('2300', 'Newcastle', 'NSW', -32.940, 151.770),
('2300', 'Newcastle East', 'NSW', -32.940, 151.770),
('2300', 'The Hill', 'NSW', -32.940, 151.770),
('2302', 'Newcastle West', 'NSW', -32.920, 151.760),
('2303', 'Hamilton', 'NSW', -32.920, 151.750),
('2303', 'Hamilton East', 'NSW', -32.920, 151.750),
('2303', 'Hamilton South', 'NSW', -32.920, 151.750),
('2304', 'Kooragang', 'NSW', -32.880, 151.750),
('2304', 'Mayfield', 'NSW', -32.880, 151.750),
('2304', 'Mayfield East', 'NSW', -32.880, 151.750),
('2304', 'Mayfield North', 'NSW', -32.880, 151.750),
('2304', 'Mayfield West', 'NSW', -32.880, 151.750),
('2304', 'Sandgate', 'NSW', -32.880, 151.750),
('2304', 'Warabrook', 'NSW', -32.880, 151.750),
('2305', 'Kotara East', 'NSW', -32.930, 151.710),
('2305', 'New Lambton', 'NSW', -32.930, 151.710),
('2305', 'New Lambton Heights', 'NSW', -32.930, 151.710),
('2306', 'Windale', 'NSW', -33.000, 151.680),
('2307', 'Shortland', 'NSW', -32.880, 151.690),
('2308', 'Callaghan', 'NSW', -35.130, 147.320),
('2308', 'Newcastle University', 'NSW', -35.130, 147.320),
('2309', 'Dangar', 'NSW', -30.350, 148.890),
('2310', 'Hunter Region MC', 'NSW', 0.000, 0.000),
('2311', 'Allynbrook', 'NSW', -32.360, 151.540),
('2311', 'Bingleburra', 'NSW', -32.360, 151.540),
('2311', 'Carrabolla', 'NSW', -32.360, 151.540),
('2311', 'East Gresford', 'NSW', -32.360, 151.540),
('2311', 'Eccleston', 'NSW', -32.360, 151.540),
('2311', 'Gresford', 'NSW', -32.360, 151.540),
('2311', 'Halton', 'NSW', -32.360, 151.540),
('2311', 'Lewinsbrook', 'NSW', -32.360, 151.540),
('2311', 'Lostock', 'NSW', -32.360, 151.540),
('2311', 'Mount Rivers', 'NSW', -32.360, 151.540),
('2311', 'Upper Allyn', 'NSW', -32.360, 151.540),
('2312', 'Minimbah', 'NSW', -32.150, 152.360),
('2312', 'Nabiac', 'NSW', -32.150, 152.360),
('2314', 'Williamtown Raaf', 'NSW', -32.800, 151.840),
('2315', 'Corlette', 'NSW', -32.720, 152.110),
('2315', 'Fingal Bay', 'NSW', -32.720, 152.110),
('2315', 'Nelson Bay', 'NSW', -32.720, 152.110),
('2315', 'Shoal Bay', 'NSW', -32.720, 152.110),
('2316', 'Anna Bay', 'NSW', -32.780, 152.080),
('2316', 'Boat Harbour', 'NSW', -32.780, 152.080),
('2316', 'Bobs Farm', 'NSW', -32.780, 152.080),
('2316', 'Fishermans Bay', 'NSW', -32.780, 152.080),
('2316', 'One Mile', 'NSW', -32.780, 152.080),
('2316', 'Taylors Beach', 'NSW', -32.780, 152.080),
('2317', 'Salamander Bay', 'NSW', -32.720, 152.080),
('2317', 'Soldiers Point', 'NSW', -32.720, 152.080),
('2318', 'Campvale', 'NSW', -32.770, 151.850),
('2318', 'Ferodale', 'NSW', -32.770, 151.850),
('2318', 'Fullerton Cove', 'NSW', -32.770, 151.850),
('2318', 'Medowie', 'NSW', -32.770, 151.850),
('2318', 'Oyster Cove', 'NSW', -32.770, 151.850),
('2318', 'Salt Ash', 'NSW', -32.770, 151.850),
('2318', 'Williamtown', 'NSW', -32.770, 151.850),
('2319', 'Lemon Tree Passage', 'NSW', -32.730, 152.040),
('2319', 'Mallabula', 'NSW', -32.730, 152.040),
('2319', 'Tanilba Bay', 'NSW', -32.730, 152.040),
('2319', 'Tilligerry Creek', 'NSW', -32.730, 152.040),
('2320', 'Aberglasslyn', 'NSW', -32.690, 151.530),
('2320', 'Allandale', 'NSW', -32.690, 151.530),
('2320', 'Anambah', 'NSW', -32.690, 151.530),
('2320', 'Bolwarra', 'NSW', -32.690, 151.530),
('2320', 'Bolwarra Heights', 'NSW', -32.690, 151.530),
('2320', 'Farley', 'NSW', -32.690, 151.530),
('2320', 'Glen Oak', 'NSW', -32.690, 151.530),
('2320', 'Gosforth', 'NSW', -32.690, 151.530),
('2320', 'Hillsborough', 'NSW', -32.690, 151.530),
('2320', 'Horseshoe Bend', 'NSW', -32.690, 151.530),
('2320', 'Keinbah', 'NSW', -32.690, 151.530),
('2320', 'Largs', 'NSW', -32.690, 151.530),
('2320', 'Lorn', 'NSW', -32.690, 151.530),
('2320', 'Louth Park', 'NSW', -32.690, 151.530),
('2320', 'Maitland', 'NSW', -32.690, 151.530),
('2320', 'Maitland North', 'NSW', -32.690, 151.530),
('2320', 'Maitland Vale', 'NSW', -32.690, 151.530),
('2320', 'Melville', 'NSW', -32.690, 151.530),
('2320', 'Mindaribba', 'NSW', -32.690, 151.530),
('2320', 'Mount Dee', 'NSW', -32.690, 151.530),
('2320', 'Oakhampton', 'NSW', -32.690, 151.530),
('2320', 'Oakhampton Heights', 'NSW', -32.690, 151.530),
('2320', 'Pokolbin', 'NSW', -32.690, 151.530),
('2320', 'Rosebrook', 'NSW', -32.690, 151.530),
('2320', 'Rothbury', 'NSW', -32.690, 151.530),
('2320', 'Rutherford', 'NSW', -32.690, 151.530),
('2320', 'South Maitland', 'NSW', -32.690, 151.530),
('2320', 'Telarah', 'NSW', -32.690, 151.530),
('2320', 'Wallalong', 'NSW', -32.690, 151.530),
('2320', 'Windella', 'NSW', -32.690, 151.530),
('2321', 'Berry Park', 'NSW', -32.760, 151.650),
('2321', 'Butterwick', 'NSW', -32.760, 151.650),
('2321', 'Clarence Town', 'NSW', -32.760, 151.650),
('2321', 'Cliftleigh', 'NSW', -32.760, 151.650),
('2321', 'Duckenfield', 'NSW', -32.760, 151.650),
('2321', 'Duns Creek', 'NSW', -32.760, 151.650),
('2321', 'Gillieston Heights', 'NSW', -32.760, 151.650),
('2321', 'Glen Martin', 'NSW', -32.760, 151.650),
('2321', 'Glen William', 'NSW', -32.760, 151.650),
('2321', 'Harpers Hill', 'NSW', -32.760, 151.650),
('2321', 'Heddon Greta', 'NSW', -32.760, 151.650),
('2321', 'Hinton', 'NSW', -32.760, 151.650),
('2321', 'Lochinvar', 'NSW', -32.760, 151.650),
('2321', 'Luskintyre', 'NSW', -32.760, 151.650),
('2321', 'Morpeth', 'NSW', -32.760, 151.650),
('2321', 'Oswald', 'NSW', -32.760, 151.650),
('2321', 'Phoenix Park', 'NSW', -32.760, 151.650),
('2321', 'Raworth', 'NSW', -32.760, 151.650),
('2321', 'Windermere', 'NSW', -32.760, 151.650),
('2321', 'Woodville', 'NSW', -32.760, 151.650),
('2322', 'Beresfield', 'NSW', -32.800, 151.660),
('2322', 'Black Hill', 'NSW', -32.800, 151.660),
('2322', 'Chisholm', 'NSW', -32.800, 151.660),
('2322', 'Hexham', 'NSW', -32.800, 151.660),
('2322', 'Lenaghan', 'NSW', -32.800, 151.660),
('2322', 'Stockrington', 'NSW', -32.800, 151.660),
('2322', 'Tarro', 'NSW', -32.800, 151.660),
('2322', 'Thornton', 'NSW', -32.800, 151.660),
('2322', 'Tomago', 'NSW', -32.800, 151.660),
('2322', 'Woodberry', 'NSW', -32.800, 151.660),
('2323', 'Ashtonfield', 'NSW', -32.770, 151.600),
('2323', 'Brunkerville', 'NSW', -32.770, 151.600),
('2323', 'Buchanan', 'NSW', -32.770, 151.600),
('2323', 'Buttai', 'NSW', -32.770, 151.600),
('2323', 'East Maitland', 'NSW', -32.770, 151.600),
('2323', 'Four Mile Creek', 'NSW', -32.770, 151.600),
('2323', 'Freemans Waterhole', 'NSW', -32.770, 151.600),
('2323', 'Green Hills', 'NSW', -32.770, 151.600),
('2323', 'Metford', 'NSW', -32.770, 151.600),
('2323', 'Metford DC', 'NSW', -32.770, 151.600),
('2323', 'Mount Vincent', 'NSW', -32.770, 151.600),
('2323', 'Mulbring', 'NSW', -32.770, 151.600),
('2323', 'Pitnacree', 'NSW', -32.770, 151.600),
('2323', 'Richmond Vale', 'NSW', -32.770, 151.600),
('2323', 'Tenambit', 'NSW', -32.770, 151.600),
('2324', 'Balickera', 'NSW', -32.670, 151.810),
('2324', 'Brandy Hill', 'NSW', -32.670, 151.810),
('2324', 'Bundabah', 'NSW', -32.670, 151.810),
('2324', 'Carrington', 'NSW', -32.670, 151.810),
('2324', 'Eagleton', 'NSW', -32.670, 151.810),
('2324', 'East Seaham', 'NSW', -32.670, 151.810),
('2324', 'Hawks Nest', 'NSW', -32.670, 151.810),
('2324', 'Heatherbrae', 'NSW', -32.670, 151.810),
('2324', 'Karuah', 'NSW', -32.670, 151.810),
('2324', 'Limeburners Creek', 'NSW', -32.670, 151.810),
('2324', 'Millers Forest', 'NSW', -32.670, 151.810),
('2324', 'Nelsons Plains', 'NSW', -32.670, 151.810),
('2324', 'North Arm Cove', 'NSW', -32.670, 151.810),
('2324', 'Osterley', 'NSW', -32.670, 151.810),
('2324', 'Pindimar', 'NSW', -32.670, 151.810),
('2324', 'Raymond Terrace', 'NSW', -32.670, 151.810),
('2324', 'Raymond Terrace East', 'NSW', -32.670, 151.810),
('2324', 'Seaham', 'NSW', -32.670, 151.810),
('2324', 'Swan Bay', 'NSW', -32.670, 151.810),
('2324', 'Tahlee', 'NSW', -32.670, 151.810),
('2324', 'Tea Gardens', 'NSW', -32.670, 151.810),
('2324', 'Twelve Mile Creek', 'NSW', -32.670, 151.810),
('2325', 'Aberdare', 'NSW', -32.840, 151.380),
('2325', 'Abernethy', 'NSW', -32.840, 151.380),
('2325', 'Bellbird', 'NSW', -32.840, 151.380),
('2325', 'Bellbird Heights', 'NSW', -32.840, 151.380),
('2325', 'Cedar Creek', 'NSW', -32.840, 151.380),
('2325', 'Cessnock', 'NSW', -32.840, 151.380),
('2325', 'Cessnock West', 'NSW', -32.840, 151.380),
('2325', 'Congewai', 'NSW', -32.840, 151.380),
('2325', 'Corrabare', 'NSW', -32.840, 151.380),
('2325', 'Ellalong', 'NSW', -32.840, 151.380),
('2325', 'Elrington', 'NSW', -32.840, 151.380),
('2325', 'Greta Main', 'NSW', -32.840, 151.380),
('2325', 'Kearsley', 'NSW', -32.840, 151.380),
('2325', 'Kitchener', 'NSW', -32.840, 151.380),
('2325', 'Laguna', 'NSW', -32.840, 151.380),
('2325', 'Lovedale', 'NSW', -32.840, 151.380),
('2325', 'Millfield', 'NSW', -32.840, 151.380),
('2325', 'Moruben', 'NSW', -32.840, 151.380),
('2325', 'Mount View', 'NSW', -32.840, 151.380),
('2325', 'Nulkaba', 'NSW', -32.840, 151.380),
('2325', 'Olney', 'NSW', -32.840, 151.380),
('2325', 'Paxton', 'NSW', -32.840, 151.380),
('2325', 'Paynes Crossing', 'NSW', -32.840, 151.380),
('2325', 'Pelton', 'NSW', -32.840, 151.380),
('2325', 'Quorrobolong', 'NSW', -32.840, 151.380),
('2325', 'Sweetmans Creek', 'NSW', -32.840, 151.380),
('2325', 'Wollombi', 'NSW', -32.840, 151.380),
('2326', 'Abermain', 'NSW', -32.810, 151.430),
('2326', 'Bishops Bridge', 'NSW', -32.810, 151.430),
('2326', 'Loxford', 'NSW', -32.810, 151.430),
('2326', 'Neath', 'NSW', -32.810, 151.430),
('2326', 'Sawyers Gully', 'NSW', -32.810, 151.430),
('2326', 'Weston', 'NSW', -32.810, 151.430),
('2327', 'Kurri Kurri', 'NSW', -32.820, 151.480),
('2327', 'Pelaw Main', 'NSW', -32.820, 151.480),
('2327', 'Stanford Merthyr', 'NSW', -32.820, 151.480),
('2328', 'Bureen', 'NSW', -32.460, 150.740),
('2328', 'Dalswinton', 'NSW', -32.460, 150.740),
('2328', 'Denman', 'NSW', -32.460, 150.740),
('2328', 'Giants Creek', 'NSW', -32.460, 150.740),
('2328', 'Hollydeen', 'NSW', -32.460, 150.740),
('2328', 'Kerrabee', 'NSW', -32.460, 150.740),
('2328', 'Mangoola', 'NSW', -32.460, 150.740),
('2328', 'Martindale', 'NSW', -32.460, 150.740),
('2328', 'Widden', 'NSW', -32.460, 150.740),
('2328', 'Yarrawa', 'NSW', -32.460, 150.740),
('2329', 'Cassilis', 'NSW', -31.510, 150.640),
('2329', 'Merriwa', 'NSW', -31.510, 150.640),
('2329', 'Uarbry', 'NSW', -31.510, 150.640),
('2330', 'Appletree Flat', 'NSW', -32.510, 150.860),
('2330', 'Big Ridge', 'NSW', -32.510, 150.860),
('2330', 'Big Yengo', 'NSW', -32.510, 150.860),
('2330', 'Bowmans Creek', 'NSW', -32.510, 150.860),
('2330', 'Bridgman', 'NSW', -32.510, 150.860),
('2330', 'Broke', 'NSW', -32.510, 150.860),
('2330', 'Bulga', 'NSW', -32.510, 150.860),
('2330', 'Camberwell', 'NSW', -32.510, 150.860),
('2330', 'Carrowbrook', 'NSW', -32.510, 150.860),
('2330', 'Clydesdale', 'NSW', -32.510, 150.860),
('2330', 'Combo', 'NSW', -32.510, 150.860),
('2330', 'Darlington', 'NSW', -32.510, 150.860),
('2330', 'Doyles Creek', 'NSW', -32.510, 150.860),
('2330', 'Dunolly', 'NSW', -32.510, 150.860),
('2330', 'Dural', 'NSW', -32.510, 150.860),
('2330', 'Dyrring', 'NSW', -32.510, 150.860),
('2330', 'Falbrook', 'NSW', -32.510, 150.860),
('2330', 'Fern Gully', 'NSW', -32.510, 150.860),
('2330', 'Fordwich', 'NSW', -32.510, 150.860),
('2330', 'Garland Valley', 'NSW', -32.510, 150.860),
('2330', 'Glendon', 'NSW', -32.510, 150.860),
('2330', 'Glendon Brook', 'NSW', -32.510, 150.860),
('2330', 'Glennies Creek', 'NSW', -32.510, 150.860),
('2330', 'Glenridding', 'NSW', -32.510, 150.860),
('2330', 'Goorangoola', 'NSW', -32.510, 150.860),
('2330', 'Gouldsville', 'NSW', -32.510, 150.860),
('2330', 'Gowrie', 'NSW', -32.510, 150.860),
('2330', 'Greenlands', 'NSW', -32.510, 150.860),
('2330', 'Hambledon Hill', 'NSW', -32.510, 150.860),
('2330', 'Hebden', 'NSW', -32.510, 150.860),
('2330', 'Howes Valley', 'NSW', -32.510, 150.860),
('2330', 'Howick', 'NSW', -32.510, 150.860),
('2330', 'Hunterview', 'NSW', -32.510, 150.860),
('2330', 'Jerrys Plains', 'NSW', -32.510, 150.860),
('2330', 'Lemington', 'NSW', -32.510, 150.860),
('2330', 'Long Point', 'NSW', -32.510, 150.860),
('2330', 'Maison Dieu', 'NSW', -32.510, 150.860),
('2330', 'Mcdougalls Hill', 'NSW', -32.510, 150.860),
('2330', 'Middle Falbrook', 'NSW', -32.510, 150.860),
('2330', 'Milbrodale', 'NSW', -32.510, 150.860),
('2330', 'Mirannie', 'NSW', -32.510, 150.860),
('2330', 'Mitchells Flat', 'NSW', -32.510, 150.860),
('2330', 'Mount Olive', 'NSW', -32.510, 150.860),
('2330', 'Mount Royal', 'NSW', -32.510, 150.860),
('2330', 'Mount Thorley', 'NSW', -32.510, 150.860),
('2330', 'Obanvale', 'NSW', -32.510, 150.860),
('2330', 'Putty', 'NSW', -32.510, 150.860),
('2330', 'Ravensworth', 'NSW', -32.510, 150.860),
('2330', 'Redbournberry', 'NSW', -32.510, 150.860),
('2330', 'Reedy Creek', 'NSW', -32.510, 150.860),
('2330', 'Rixs Creek', 'NSW', -32.510, 150.860),
('2330', 'Roughit', 'NSW', -32.510, 150.860),
('2330', 'Scotts Flat', 'NSW', -32.510, 150.860),
('2330', 'Sedgefield', 'NSW', -32.510, 150.860),
('2330', 'Singleton', 'NSW', -32.510, 150.860),
('2330', 'Singleton Heights', 'NSW', -32.510, 150.860),
('2330', 'St Clair', 'NSW', -32.510, 150.860),
('2330', 'Warkworth', 'NSW', -32.510, 150.860),
('2330', 'Wattle Ponds', 'NSW', -32.510, 150.860),
('2330', 'Westbrook', 'NSW', -32.510, 150.860),
('2330', 'Whittingham', 'NSW', -32.510, 150.860),
('2330', 'Wollemi', 'NSW', -32.510, 150.860),
('2330', 'Wylies Flat', 'NSW', -32.510, 150.860),
('2331', 'Singleton Military Area', 'NSW', -32.690, 151.180),
('2331', 'Singleton Milpo', 'NSW', -32.690, 151.180),
('2333', 'Baerami', 'NSW', -32.390, 150.470),
('2333', 'Baerami Creek', 'NSW', -32.390, 150.470),
('2333', 'Bengalla', 'NSW', -32.390, 150.470),
('2333', 'Castle Rock', 'NSW', -32.390, 150.470),
('2333', 'Edderton', 'NSW', -32.390, 150.470),
('2333', 'Gungal', 'NSW', -32.390, 150.470),
('2333', 'Kayuga', 'NSW', -32.390, 150.470),
('2333', 'Liddell', 'NSW', -32.390, 150.470),
('2333', 'Manobalai', 'NSW', -32.390, 150.470),
('2333', 'Mccullys Gap', 'NSW', -32.390, 150.470),
('2333', 'Muscle Creek', 'NSW', -32.390, 150.470),
('2333', 'Muswellbrook', 'NSW', -32.390, 150.470),
('2333', 'Sandy Hollow', 'NSW', -32.390, 150.470),
('2333', 'Wybong', 'NSW', -32.390, 150.470),
('2334', 'Greta', 'NSW', -32.680, 151.390),
('2335', 'Belford', 'NSW', -32.650, 151.280),
('2335', 'Branxton', 'NSW', -32.650, 151.280),
('2335', 'Dalwood', 'NSW', -32.650, 151.280),
('2335', 'East Branxton', 'NSW', -32.650, 151.280),
('2335', 'Elderslie', 'NSW', -32.650, 151.280),
('2335', 'Lambs Valley', 'NSW', -32.650, 151.280),
('2335', 'Leconfield', 'NSW', -32.650, 151.280),
('2335', 'Lower Belford', 'NSW', -32.650, 151.280),
('2335', 'North Rothbury', 'NSW', -32.650, 151.280),
('2335', 'Stanhope', 'NSW', -32.650, 151.280),
('2336', 'Aberdeen', 'NSW', -32.160, 150.890),
('2336', 'Dartbrook', 'NSW', -32.160, 150.890),
('2336', 'Davis Creek', 'NSW', -32.160, 150.890),
('2336', 'Rossgole', 'NSW', -32.160, 150.890),
('2336', 'Rouchel', 'NSW', -32.160, 150.890),
('2336', 'Rouchel Brook', 'NSW', -32.160, 150.890),
('2336', 'Upper Dartbrook', 'NSW', -32.160, 150.890),
('2336', 'Upper Rouchel', 'NSW', -32.160, 150.890),
('2337', 'Belltrees', 'NSW', -31.990, 151.120),
('2337', 'Brawboy', 'NSW', -31.990, 151.120),
('2337', 'Bunnan', 'NSW', -31.990, 151.120),
('2337', 'Dry Creek', 'NSW', -31.990, 151.120),
('2337', 'Ellerston', 'NSW', -31.990, 151.120),
('2337', 'Glenbawn', 'NSW', -31.990, 151.120),
('2337', 'Glenrock', 'NSW', -31.990, 151.120),
('2337', 'Gundy', 'NSW', -31.990, 151.120),
('2337', 'Kars Springs', 'NSW', -31.990, 151.120),
('2337', 'Middle Brook', 'NSW', -31.990, 151.120),
('2337', 'Moobi', 'NSW', -31.990, 151.120),
('2337', 'Moonan Brook', 'NSW', -31.990, 151.120),
('2337', 'Moonan Flat', 'NSW', -31.990, 151.120),
('2337', 'Murulla', 'NSW', -31.990, 151.120),
('2337', 'Omadale', 'NSW', -31.990, 151.120),
('2337', 'Owens Gap', 'NSW', -31.990, 151.120),
('2337', 'Pages Creek', 'NSW', -31.990, 151.120),
('2337', 'Parkville', 'NSW', -31.990, 151.120),
('2337', 'Scone', 'NSW', -31.990, 151.120),
('2337', 'Segenhoe', 'NSW', -31.990, 151.120),
('2337', 'Stewarts Brook', 'NSW', -31.990, 151.120),
('2337', 'Tomalla', 'NSW', -31.990, 151.120),
('2337', 'Waverly', 'NSW', -31.990, 151.120),
('2337', 'Wingen', 'NSW', -31.990, 151.120),
('2337', 'Woolooma', 'NSW', -31.990, 151.120),
('2338', 'Ardglen', 'NSW', -31.730, 150.790),
('2338', 'Blandford', 'NSW', -31.730, 150.790),
('2338', 'Crawney', 'NSW', -31.730, 150.790),
('2338', 'Green Creek', 'NSW', -31.730, 150.790),
('2338', 'Murrurundi', 'NSW', -31.730, 150.790),
('2338', 'Pages River', 'NSW', -31.730, 150.790),
('2338', 'Sandy Creek', 'NSW', -31.730, 150.790),
('2338', 'Scotts Creek', 'NSW', -31.730, 150.790),
('2338', 'Timor', 'NSW', -31.730, 150.790),
('2339', 'Big Jacks Creek', 'NSW', -31.770, 150.620),
('2339', 'Braefield', 'NSW', -31.770, 150.620),
('2339', 'Cattle Creek', 'NSW', -31.770, 150.620),
('2339', 'Chilcotts Creek', 'NSW', -31.770, 150.620),
('2339', 'Little Jacks Creek', 'NSW', -31.770, 150.620),
('2339', 'Macdonalds Creek', 'NSW', -31.770, 150.620),
('2339', 'Parraweena', 'NSW', -31.770, 150.620),
('2339', 'Warrah', 'NSW', -31.770, 150.620),
('2339', 'Warrah Creek', 'NSW', -31.770, 150.620),
('2339', 'Willow Tree', 'NSW', -31.770, 150.620),
('2340', 'Appleby', 'NSW', -30.960, 150.830),
('2340', 'Barry', 'NSW', -30.960, 150.830),
('2340', 'Bective', 'NSW', -30.960, 150.830),
('2340', 'Bithramere', 'NSW', -30.960, 150.830),
('2340', 'Bowling Alley Point', 'NSW', -30.960, 150.830),
('2340', 'Calala', 'NSW', -30.960, 150.830),
('2340', 'Carroll', 'NSW', -30.960, 150.830),
('2340', 'Daruka', 'NSW', -30.960, 150.830),
('2340', 'Duncans Creek', 'NSW', -30.960, 150.830),
('2340', 'Dungowan', 'NSW', -30.960, 150.830),
('2340', 'East Tamworth', 'NSW', -30.960, 150.830),
('2340', 'Garoo', 'NSW', -30.960, 150.830),
('2340', 'Gidley', 'NSW', -30.960, 150.830),
('2340', 'Goonoo Goonoo', 'NSW', -30.960, 150.830),
('2340', 'Gowrie', 'NSW', -30.960, 150.830),
('2340', 'Hallsville', 'NSW', -30.960, 150.830),
('2340', 'Hanging Rock', 'NSW', -30.960, 150.830),
('2340', 'Hillvue', 'NSW', -30.960, 150.830),
('2340', 'Keepit', 'NSW', -30.960, 150.830),
('2340', 'Kingswood', 'NSW', -30.960, 150.830),
('2340', 'Loomberah', 'NSW', -30.960, 150.830),
('2340', 'Moore Creek', 'NSW', -30.960, 150.830),
('2340', 'Nemingha', 'NSW', -30.960, 150.830),
('2340', 'North Tamworth', 'NSW', -30.960, 150.830),
('2340', 'Nundle', 'NSW', -30.960, 150.830),
('2340', 'Ogunbil', 'NSW', -30.960, 150.830),
('2340', 'Oxley Vale', 'NSW', -30.960, 150.830),
('2340', 'Piallamore', 'NSW', -30.960, 150.830),
('2340', 'Somerton', 'NSW', -30.960, 150.830),
('2340', 'South Tamworth', 'NSW', -30.960, 150.830),
('2340', 'Taminda', 'NSW', -30.960, 150.830),
('2340', 'Tamworth', 'NSW', -30.960, 150.830),
('2340', 'Timbumburi', 'NSW', -30.960, 150.830),
('2340', 'Wallamore', 'NSW', -30.960, 150.830),
('2340', 'Warral', 'NSW', -30.960, 150.830),
('2340', 'Weabonga', 'NSW', -30.960, 150.830),
('2340', 'West Tamworth', 'NSW', -30.960, 150.830),
('2340', 'Westdale', 'NSW', -30.960, 150.830),
('2340', 'Woolomin', 'NSW', -30.960, 150.830),
('2341', 'Werris Creek', 'NSW', -31.350, 150.620),
('2342', 'Currabubula', 'NSW', -31.260, 150.730),
('2342', 'Piallaway', 'NSW', -31.260, 150.730),
('2343', 'Blackville', 'NSW', -31.660, 150.300),
('2343', 'Borambil', 'NSW', -31.660, 150.300),
('2343', 'Bundella', 'NSW', -31.660, 150.300),
('2343', 'Caroona', 'NSW', -31.660, 150.300),
('2343', 'Colly Blue', 'NSW', -31.660, 150.300),
('2343', 'Coomoo Coomoo', 'NSW', -31.660, 150.300),
('2343', 'Pine Ridge', 'NSW', -31.660, 150.300),
('2343', 'Quipolly', 'NSW', -31.660, 150.300),
('2343', 'Quirindi', 'NSW', -31.660, 150.300),
('2343', 'Spring Ridge', 'NSW', -31.660, 150.300),
('2343', 'Wallabadah', 'NSW', -31.660, 150.300),
('2343', 'Warrah Ridge', 'NSW', -31.660, 150.300),
('2343', 'Windy', 'NSW', -31.660, 150.300),
('2343', 'Yannergee', 'NSW', -31.660, 150.300),
('2343', 'Yarraman', 'NSW', -31.660, 150.300),
('2344', 'Duri', 'NSW', -31.220, 150.820),
('2344', 'Winton', 'NSW', -31.220, 150.820),
('2345', 'Attunga', 'NSW', -30.930, 150.850),
('2345', 'Garthowen', 'NSW', -30.930, 150.850),
('2346', 'Borah Creek', 'NSW', -30.610, 150.510),
('2346', 'Halls Creek', 'NSW', -30.610, 150.510),
('2346', 'Klori', 'NSW', -30.610, 150.510),
('2346', 'Manilla', 'NSW', -30.610, 150.510),
('2346', 'Namoi River', 'NSW', -30.610, 150.510),
('2346', 'New Mexico', 'NSW', -30.610, 150.510),
('2346', 'Rushes Creek', 'NSW', -30.610, 150.510),
('2346', 'Upper Manilla', 'NSW', -30.610, 150.510),
('2346', 'Warrabah', 'NSW', -30.610, 150.510),
('2346', 'Wimborne', 'NSW', -30.610, 150.510),
('2346', 'Wongo Creek', 'NSW', -30.610, 150.510),
('2347', 'Banoon', 'NSW', -30.530, 150.440),
('2347', 'Barraba', 'NSW', -30.530, 150.440),
('2347', 'Cobbadah', 'NSW', -30.530, 150.440),
('2347', 'Gulf Creek', 'NSW', -30.530, 150.440),
('2347', 'Gundamulda', 'NSW', -30.530, 150.440),
('2347', 'Ironbark', 'NSW', -30.530, 150.440),
('2347', 'Lindesay', 'NSW', -30.530, 150.440),
('2347', 'Longarm', 'NSW', -30.530, 150.440),
('2347', 'Mayvale', 'NSW', -30.530, 150.440),
('2347', 'Red Hill', 'NSW', -30.530, 150.440),
('2347', 'Thirloene', 'NSW', -30.530, 150.440),
('2347', 'Upper Horton', 'NSW', -30.530, 150.440),
('2347', 'Woodsreef', 'NSW', -30.530, 150.440),
('2348', 'New England MC', 'NSW', 0.000, 0.000),
('2350', 'Aberfoyle', 'NSW', -30.240, 152.010),
('2350', 'Abington', 'NSW', -30.240, 152.010),
('2350', 'Argyle', 'NSW', -30.240, 152.010),
('2350', 'Armidale', 'NSW', -30.240, 152.010),
('2350', 'Boorolong', 'NSW', -30.240, 152.010),
('2350', 'Castle Doyle', 'NSW', -30.240, 152.010),
('2350', 'Dangarsleigh', 'NSW', -30.240, 152.010),
('2350', 'Donald Creek', 'NSW', -30.240, 152.010),
('2350', 'Dumaresq', 'NSW', -30.240, 152.010),
('2350', 'Duval', 'NSW', -30.240, 152.010),
('2350', 'Enmore', 'NSW', -30.240, 152.010),
('2350', 'Hillgrove', 'NSW', -30.240, 152.010),
('2350', 'Invergowrie', 'NSW', -30.240, 152.010),
('2350', 'Jeogla', 'NSW', -30.240, 152.010),
('2350', 'Kellys Plains', 'NSW', -30.240, 152.010),
('2350', 'Lyndhurst', 'NSW', -30.240, 152.010),
('2350', 'Puddledock', 'NSW', -30.240, 152.010),
('2350', 'Saumarez', 'NSW', -30.240, 152.010),
('2350', 'Saumarez Ponds', 'NSW', -30.240, 152.010),
('2350', 'Thalgarrah', 'NSW', -30.240, 152.010),
('2350', 'Tilbuster', 'NSW', -30.240, 152.010),
('2350', 'Wards Mistake', 'NSW', -30.240, 152.010),
('2350', 'West Armidale', 'NSW', -30.240, 152.010),
('2350', 'Wollomombi', 'NSW', -30.240, 152.010),
('2350', 'Wongwibinda', 'NSW', -30.240, 152.010),
('2351', 'University Of New England', 'NSW', -30.490, 151.640),
('2352', 'Kootingal', 'NSW', -31.060, 151.050),
('2352', 'Limbri', 'NSW', -31.060, 151.050),
('2352', 'Mulla Creek', 'NSW', -31.060, 151.050),
('2352', 'Tintinhull', 'NSW', -31.060, 151.050),
('2353', 'Moonbi', 'NSW', -30.950, 151.050),
('2354', 'Kentucky', 'NSW', -31.270, 151.550),
('2354', 'Kentucky South', 'NSW', -31.270, 151.550),
('2354', 'Niangala', 'NSW', -31.270, 151.550),
('2354', 'Nowendoc', 'NSW', -31.270, 151.550),
('2354', 'Walcha', 'NSW', -31.270, 151.550),
('2354', 'Walcha Road', 'NSW', -31.270, 151.550),
('2354', 'Wollun', 'NSW', -31.270, 151.550),
('2354', 'Woolbrook', 'NSW', -31.270, 151.550),
('2354', 'Yarrowitch', 'NSW', -31.270, 151.550),
('2355', 'Bendemeer', 'NSW', -30.880, 151.160),
('2355', 'Retreat', 'NSW', -30.880, 151.160),
('2355', 'Watsons Creek', 'NSW', -30.880, 151.160),
('2356', 'Gwabegar', 'NSW', -30.620, 148.970),
('2357', 'Bomera', 'NSW', -31.510, 149.790),
('2357', 'Box Ridge', 'NSW', -31.510, 149.790),
('2357', 'Bugaldie', 'NSW', -31.510, 149.790),
('2357', 'Coonabarabran', 'NSW', -31.510, 149.790),
('2357', 'Dandry', 'NSW', -31.510, 149.790),
('2357', 'Gowang', 'NSW', -31.510, 149.790),
('2357', 'Purlewaugh', 'NSW', -31.510, 149.790),
('2357', 'Rocky Glen', 'NSW', -31.510, 149.790),
('2357', 'Tannabar', 'NSW', -31.510, 149.790),
('2357', 'Ulamambri', 'NSW', -31.510, 149.790),
('2357', 'Wattle Springs', 'NSW', -31.510, 149.790),
('2358', 'Arding', 'NSW', -30.590, 151.560),
('2358', 'Balala', 'NSW', -30.590, 151.560),
('2358', 'Gostwyck', 'NSW', -30.590, 151.560),
('2358', 'Kingstown', 'NSW', -30.590, 151.560),
('2358', 'Mihi', 'NSW', -30.590, 151.560),
('2358', 'Rocky River', 'NSW', -30.590, 151.560),
('2358', 'Salisbury Plains', 'NSW', -30.590, 151.560),
('2358', 'Torryburn', 'NSW', -30.590, 151.560),
('2358', 'Uralla', 'NSW', -30.590, 151.560),
('2358', 'Yarrowyck', 'NSW', -30.590, 151.560),
('2359', 'Aberdeen', 'NSW', -30.000, 151.080),
('2359', 'Bakers Creek', 'NSW', -30.000, 151.080),
('2359', 'Bundarra', 'NSW', -30.000, 151.080),
('2359', 'Camerons Creek', 'NSW', -30.000, 151.080),
('2360', 'Auburn Vale', 'NSW', -29.840, 151.040),
('2360', 'Brodies Plains', 'NSW', -29.840, 151.040),
('2360', 'Bukkulla', 'NSW', -29.840, 151.040),
('2360', 'Cherry Tree Hill', 'NSW', -29.840, 151.040),
('2360', 'Copeton', 'NSW', -29.840, 151.040),
('2360', 'Elsmore', 'NSW', -29.840, 151.040),
('2360', 'Gilgai', 'NSW', -29.840, 151.040),
('2360', 'Graman', 'NSW', -29.840, 151.040),
('2360', 'Gum Flat', 'NSW', -29.840, 151.040),
('2360', 'Howell', 'NSW', -29.840, 151.040),
('2360', 'Inverell', 'NSW', -29.840, 151.040),
('2360', 'Kings Plains', 'NSW', -29.840, 151.040),
('2360', 'Little Plain', 'NSW', -29.840, 151.040),
('2360', 'Long Plain', 'NSW', -29.840, 151.040),
('2360', 'Mount Russell', 'NSW', -29.840, 151.040),
('2360', 'Newstead', 'NSW', -29.840, 151.040),
('2360', 'Nullamanna', 'NSW', -29.840, 151.040),
('2360', 'Oakwood', 'NSW', -29.840, 151.040),
('2360', 'Paradise', 'NSW', -29.840, 151.040),
('2360', 'Rob Roy', 'NSW', -29.840, 151.040),
('2360', 'Sapphire', 'NSW', -29.840, 151.040),
('2360', 'Spring Mountain', 'NSW', -29.840, 151.040),
('2360', 'Stanborough', 'NSW', -29.840, 151.040),
('2360', 'Swanbrook', 'NSW', -29.840, 151.040),
('2360', 'Wallangra', 'NSW', -29.840, 151.040),
('2360', 'Wandera', 'NSW', -29.840, 151.040),
('2360', 'Woodstock', 'NSW', -29.840, 151.040),
('2361', 'Ashford', 'NSW', -29.320, 151.100),
('2361', 'Atholwood', 'NSW', -29.320, 151.100),
('2361', 'Bonshaw', 'NSW', -29.320, 151.100),
('2361', 'Limestone', 'NSW', -29.320, 151.100),
('2361', 'Pindaroi', 'NSW', -29.320, 151.100),
('2365', 'Backwater', 'NSW', -30.080, 151.880),
('2365', 'Bald Blair', 'NSW', -30.080, 151.880),
('2365', 'Baldersleigh', 'NSW', -30.080, 151.880),
('2365', 'Bassendean', 'NSW', -30.080, 151.880),
('2365', 'Ben Lomond', 'NSW', -30.080, 151.880),
('2365', 'Black Mountain', 'NSW', -30.080, 151.880),
('2365', 'Briarbrook', 'NSW', -30.080, 151.880),
('2365', 'Brockley', 'NSW', -30.080, 151.880),
('2365', 'Brushy Creek', 'NSW', -30.080, 151.880),
('2365', 'Falconer', 'NSW', -30.080, 151.880),
('2365', 'Georges Creek', 'NSW', -30.080, 151.880),
('2365', 'Glen Nevis', 'NSW', -30.080, 151.880),
('2365', 'Glencoe', 'NSW', -30.080, 151.880),
('2365', 'Green Hills', 'NSW', -30.080, 151.880),
('2365', 'Guyra', 'NSW', -30.080, 151.880),
('2365', 'Llangothlin', 'NSW', -30.080, 151.880),
('2365', 'Maybole', 'NSW', -30.080, 151.880),
('2365', 'Mount Mitchell', 'NSW', -30.080, 151.880),
('2365', 'New Valley', 'NSW', -30.080, 151.880),
('2365', 'Oban', 'NSW', -30.080, 151.880),
('2365', 'South Guyra', 'NSW', -30.080, 151.880),
('2365', 'Tenterden', 'NSW', -30.080, 151.880),
('2365', 'The Basin', 'NSW', -30.080, 151.880),
('2365', 'The Gulf', 'NSW', -30.080, 151.880),
('2365', 'Tubbamurra', 'NSW', -30.080, 151.880),
('2365', 'Wandsworth', 'NSW', -30.080, 151.880),
('2369', 'Old Mill', 'NSW', -29.910, 151.210),
('2369', 'Stannifer', 'NSW', -29.910, 151.210),
('2369', 'Tingha', 'NSW', -29.910, 151.210),
('2370', 'Bald Nob', 'NSW', -29.640, 151.960),
('2370', 'Diehard', 'NSW', -29.640, 151.960),
('2370', 'Dundee', 'NSW', -29.640, 151.960),
('2370', 'Furracabad', 'NSW', -29.640, 151.960),
('2370', 'Gibraltar Range', 'NSW', -29.640, 151.960),
('2370', 'Glen Elgin', 'NSW', -29.640, 151.960),
('2370', 'Glen Innes', 'NSW', -29.640, 151.960),
('2370', 'Kingsgate', 'NSW', -29.640, 151.960),
('2370', 'Kookabookra', 'NSW', -29.640, 151.960),
('2370', 'Lambs Valley', 'NSW', -29.640, 151.960),
('2370', 'Matheson', 'NSW', -29.640, 151.960),
('2370', 'Moggs Swamp', 'NSW', -29.640, 151.960),
('2370', 'Moogem', 'NSW', -29.640, 151.960),
('2370', 'Morven', 'NSW', -29.640, 151.960),
('2370', 'Newton Boyd', 'NSW', -29.640, 151.960),
('2370', 'Pinkett', 'NSW', -29.640, 151.960),
('2370', 'Rangers Valley', 'NSW', -29.640, 151.960),
('2370', 'Red Range', 'NSW', -29.640, 151.960),
('2370', 'Reddestone', 'NSW', -29.640, 151.960),
('2370', 'Shannon Vale', 'NSW', -29.640, 151.960),
('2370', 'Stonehenge', 'NSW', -29.640, 151.960),
('2370', 'Swan Vale', 'NSW', -29.640, 151.960),
('2370', 'Wellingrove', 'NSW', -29.640, 151.960),
('2370', 'Yarrowford', 'NSW', -29.640, 151.960),
('2371', 'Capoompeta', 'NSW', -29.390, 152.040),
('2371', 'Deepwater', 'NSW', -29.390, 152.040),
('2371', 'Emmaville', 'NSW', -29.390, 152.040),
('2371', 'Rocky Creek', 'NSW', -29.390, 152.040),
('2371', 'Stannum', 'NSW', -29.390, 152.040),
('2371', 'The Gulf', 'NSW', -29.390, 152.040),
('2371', 'Torrington', 'NSW', -29.390, 152.040),
('2371', 'Wellington Vale', 'NSW', -29.390, 152.040),
('2371', 'Yellow Dam', 'NSW', -29.390, 152.040),
('2372', 'Back Creek', 'NSW', -28.930, 151.700),
('2372', 'Bolivia', 'NSW', -28.930, 151.700),
('2372', 'Boonoo Boonoo', 'NSW', -28.930, 151.700),
('2372', 'Boorook', 'NSW', -28.930, 151.700),
('2372', 'Carrolls Creek', 'NSW', -28.930, 151.700),
('2372', 'Cullendore', 'NSW', -28.930, 151.700),
('2372', 'Forest Land', 'NSW', -28.930, 151.700),
('2372', 'Liston', 'NSW', -28.930, 151.700),
('2372', 'Mole River', 'NSW', -28.930, 151.700),
('2372', 'Rivertree', 'NSW', -28.930, 151.700),
('2372', 'Rocky River', 'NSW', -28.930, 151.700),
('2372', 'Sandy Flat', 'NSW', -28.930, 151.700),
('2372', 'Sandy Hill', 'NSW', -28.930, 151.700),
('2372', 'Silent Grove', 'NSW', -28.930, 151.700),
('2372', 'Tarban', 'NSW', -28.930, 151.700),
('2372', 'Tenterfield', 'NSW', -28.930, 151.700),
('2372', 'Timbarra', 'NSW', -28.930, 151.700),
('2372', 'Willsons Downfall', 'NSW', -28.930, 151.700),
('2372', 'Woodside', 'NSW', -28.930, 151.700),
('2372', 'Wylie Creek', 'NSW', -28.930, 151.700),
('2379', 'Goolhi', 'NSW', -31.070, 149.710),
('2379', 'Mullaley', 'NSW', -31.070, 149.710),
('2379', 'Napier Lane', 'NSW', -31.070, 149.710),
('2379', 'Nombi', 'NSW', -31.070, 149.710),
('2380', 'Blue Vale', 'NSW', -30.800, 150.200),
('2380', 'Emerald Hill', 'NSW', -30.800, 150.200),
('2380', 'Ghoolendaadi', 'NSW', -30.800, 150.200),
('2380', 'Gunnedah', 'NSW', -30.800, 150.200),
('2380', 'Kelvin', 'NSW', -30.800, 150.200),
('2380', 'Marys Mount', 'NSW', -30.800, 150.200),
('2380', 'Milroy', 'NSW', -30.800, 150.200),
('2380', 'Orange Grove', 'NSW', -30.800, 150.200),
('2380', 'Rangari', 'NSW', -30.800, 150.200),
('2381', 'Breeza', 'NSW', -31.240, 150.460),
('2381', 'Curlewis', 'NSW', -31.240, 150.460),
('2381', 'Premer', 'NSW', -31.240, 150.460),
('2381', 'Tambar Springs', 'NSW', -31.240, 150.460),
('2382', 'Boggabri', 'NSW', -30.700, 150.040),
('2382', 'Maules Creek', 'NSW', -30.700, 150.040),
('2382', 'Wean', 'NSW', -30.700, 150.040),
('2382', 'Willala', 'NSW', -30.700, 150.040),
('2386', 'Burren Junction', 'NSW', -30.110, 148.970),
('2386', 'Drildool', 'NSW', -30.110, 148.970),
('2386', 'Nowley', 'NSW', -30.110, 148.970),
('2387', 'Bulyeroi', 'NSW', -29.780, 149.090),
('2387', 'Rowena', 'NSW', -29.780, 149.090),
('2388', 'Boolcarroll', 'NSW', -30.090, 149.440),
('2388', 'Cuttabri', 'NSW', -30.090, 149.440),
('2388', 'Jews Lagoon', 'NSW', -30.090, 149.440),
('2388', 'Merah North', 'NSW', -30.090, 149.440),
('2388', 'Pilliga', 'NSW', -30.090, 149.440),
('2388', 'Spring Plains', 'NSW', -30.090, 149.440),
('2388', 'The Pilliga', 'NSW', -30.090, 149.440),
('2388', 'Wee Waa', 'NSW', -30.090, 149.440),
('2388', 'Yarrie Lake', 'NSW', -30.090, 149.440),
('2390', 'Baan Baa', 'NSW', -30.600, 149.970),
('2390', 'Back Creek', 'NSW', -30.600, 149.970),
('2390', 'Berrigal', 'NSW', -30.600, 149.970),
('2390', 'Bohena Creek', 'NSW', -30.600, 149.970),
('2390', 'Bullawa Creek', 'NSW', -30.600, 149.970),
('2390', 'Couradda', 'NSW', -30.600, 149.970),
('2390', 'Edgeroi', 'NSW', -30.600, 149.970),
('2390', 'Eulah Creek', 'NSW', -30.600, 149.970),
('2390', 'Harparary', 'NSW', -30.600, 149.970),
('2390', 'Jacks Creek', 'NSW', -30.600, 149.970),
('2390', 'Kaputar', 'NSW', -30.600, 149.970),
('2390', 'Narrabri', 'NSW', -30.600, 149.970),
('2390', 'Narrabri West', 'NSW', -30.600, 149.970),
('2390', 'Rocky Creek', 'NSW', -30.600, 149.970),
('2390', 'Tarriaro', 'NSW', -30.600, 149.970),
('2390', 'Turrawan', 'NSW', -30.600, 149.970),
('2395', 'Binnaway', 'NSW', -31.550, 149.380),
('2395', 'Ropers Road', 'NSW', -31.550, 149.380),
('2395', 'Weetaliba', 'NSW', -31.550, 149.380),
('2396', 'Baradine', 'NSW', -30.940, 149.070),
('2396', 'Barwon', 'NSW', -30.940, 149.070),
('2396', 'Goorianawa', 'NSW', -30.940, 149.070),
('2396', 'Kenebri', 'NSW', -30.940, 149.070),
('2397', 'Bellata', 'NSW', -29.920, 149.790),
('2397', 'Millie', 'NSW', -29.920, 149.790),
('2398', 'Gurley', 'NSW', -29.740, 149.800),
('2399', 'Biniguy', 'NSW', -29.580, 150.140),
('2399', 'Pallamallawa', 'NSW', -29.580, 150.140),
('2400', 'Ashley', 'NSW', -29.320, 149.810),
('2400', 'Bullarah', 'NSW', -29.320, 149.810),
('2400', 'Crooble', 'NSW', -29.320, 149.810),
('2400', 'Mallowa', 'NSW', -29.320, 149.810),
('2400', 'Moree', 'NSW', -29.320, 149.810),
('2400', 'Moree East', 'NSW', -29.320, 149.810),
('2400', 'Terry Hie Hie', 'NSW', -29.320, 149.810),
('2400', 'Tulloona', 'NSW', -29.320, 149.810),
('2401', 'Gravesend', 'NSW', -29.580, 150.340),
('2402', 'Balfours Peak', 'NSW', -29.510, 150.750),
('2402', 'Coolatai', 'NSW', -29.510, 150.750),
('2402', 'Warialda', 'NSW', -29.510, 150.750),
('2402', 'Warialda Rail', 'NSW', -29.510, 150.750),
('2403', 'Delungra', 'NSW', -29.650, 150.830),
('2403', 'Gragin', 'NSW', -29.650, 150.830),
('2403', 'Myall Creek', 'NSW', -29.650, 150.830),
('2404', 'Bangheet', 'NSW', -29.800, 150.480),
('2404', 'Bingara', 'NSW', -29.800, 150.480),
('2404', 'Dinoga', 'NSW', -29.800, 150.480),
('2404', 'Elcombe', 'NSW', -29.800, 150.480),
('2404', 'Gineroi', 'NSW', -29.800, 150.480),
('2404', 'Keera', 'NSW', -29.800, 150.480),
('2404', 'Pallal', 'NSW', -29.800, 150.480),
('2404', 'Riverview', 'NSW', -29.800, 150.480),
('2404', 'Upper Bingara', 'NSW', -29.800, 150.480),
('2405', 'Boomi', 'NSW', -28.730, 149.580),
('2405', 'Garah', 'NSW', -28.730, 149.580),
('2406', 'Mungindi', 'NSW', -29.000, 149.100),
('2406', 'Mungindi', 'QLD', -29.000, 149.100),
('2406', 'Weemelah', 'NSW', -29.000, 149.100),
('2408', 'Blue Nobby', 'NSW', -28.930, 150.390),
('2408', 'North Star', 'NSW', -28.930, 150.390),
('2408', 'Yallaroi', 'NSW', -28.930, 150.390),
('2409', 'Boggabilla', 'NSW', -28.740, 150.420),
('2409', 'Boonal', 'NSW', -28.740, 150.420),
('2410', 'Twin Rivers', 'NSW', -29.050, 150.640),
('2410', 'Yetman', 'NSW', -29.050, 150.640),
('2411', 'Croppa Creek', 'NSW', -29.130, 150.380),
('2415', 'Monkerai', 'NSW', -32.290, 151.860),
('2415', 'Nooroo', 'NSW', -32.290, 151.860),
('2415', 'Stroud Road', 'NSW', -32.290, 151.860),
('2415', 'Upper Karuah River', 'NSW', -32.290, 151.860),
('2415', 'Weismantels', 'NSW', -32.290, 151.860),
('2420', 'Alison', 'NSW', -32.430, 151.770),
('2420', 'Bandon Grove', 'NSW', -32.430, 151.770),
('2420', 'Bendolba', 'NSW', -32.430, 151.770),
('2420', 'Brookfield', 'NSW', -32.430, 151.770),
('2420', 'Cambra', 'NSW', -32.430, 151.770),
('2420', 'Chichester', 'NSW', -32.430, 151.770),
('2420', 'Dungog', 'NSW', -32.430, 151.770),
('2420', 'Flat Tops', 'NSW', -32.430, 151.770),
('2420', 'Fosterton', 'NSW', -32.430, 151.770),
('2420', 'Hanleys Creek', 'NSW', -32.430, 151.770),
('2420', 'Hilldale', 'NSW', -32.430, 151.770),
('2420', 'Main Creek', 'NSW', -32.430, 151.770),
('2420', 'Marshdale', 'NSW', -32.430, 151.770),
('2420', 'Martins Creek', 'NSW', -32.430, 151.770),
('2420', 'Munni', 'NSW', -32.430, 151.770),
('2420', 'Salisbury', 'NSW', -32.430, 151.770),
('2420', 'Stroud Hill', 'NSW', -32.430, 151.770),
('2420', 'Sugarloaf', 'NSW', -32.430, 151.770),
('2420', 'Tabbil Creek', 'NSW', -32.430, 151.770),
('2420', 'Underbank', 'NSW', -32.430, 151.770),
('2420', 'Wallaringa', 'NSW', -32.430, 151.770),
('2420', 'Wallarobba', 'NSW', -32.430, 151.770),
('2420', 'Wirragulla', 'NSW', -32.430, 151.770),
('2421', 'Fishers Hill', 'NSW', -32.510, 151.530),
('2421', 'Paterson', 'NSW', -32.510, 151.530),
('2421', 'Summer Hill', 'NSW', -32.510, 151.530),
('2421', 'Tocal', 'NSW', -32.510, 151.530),
('2421', 'Torryburn', 'NSW', -32.510, 151.530),
('2421', 'Vacy', 'NSW', -32.510, 151.530),
('2421', 'Webbers Creek', 'NSW', -32.510, 151.530),
('2422', 'Back Creek', 'NSW', -31.970, 152.070),
('2422', 'Bakers Creek', 'NSW', -31.970, 152.070),
('2422', 'Barrington', 'NSW', -31.970, 152.070),
('2422', 'Barrington Tops', 'NSW', -31.970, 152.070),
('2422', 'Baxters Ridge', 'NSW', -31.970, 152.070),
('2422', 'Belbora', 'NSW', -31.970, 152.070),
('2422', 'Berrico', 'NSW', -31.970, 152.070),
('2422', 'Bindera', 'NSW', -31.970, 152.070),
('2422', 'Bowman', 'NSW', -31.970, 152.070),
('2422', 'Bowman Farm', 'NSW', -31.970, 152.070),
('2422', 'Bretti', 'NSW', -31.970, 152.070),
('2422', 'Bulliac', 'NSW', -31.970, 152.070),
('2422', 'Bundook', 'NSW', -31.970, 152.070),
('2422', 'Callaghans Creek', 'NSW', -31.970, 152.070),
('2422', 'Cobark', 'NSW', -31.970, 152.070),
('2422', 'Coneac', 'NSW', -31.970, 152.070),
('2422', 'Copeland', 'NSW', -31.970, 152.070),
('2422', 'Craven', 'NSW', -31.970, 152.070),
('2422', 'Craven Plateau', 'NSW', -31.970, 152.070),
('2422', 'Curricabark', 'NSW', -31.970, 152.070),
('2422', 'Dewitt', 'NSW', -31.970, 152.070),
('2422', 'Faulkland', 'NSW', -31.970, 152.070),
('2422', 'Forbesdale', 'NSW', -31.970, 152.070),
('2422', 'Gangat', 'NSW', -31.970, 152.070),
('2422', 'Giro', 'NSW', -31.970, 152.070),
('2422', 'Glen Ward', 'NSW', -31.970, 152.070),
('2422', 'Gloucester', 'NSW', -31.970, 152.070),
('2422', 'Gloucester Tops', 'NSW', -31.970, 152.070),
('2422', 'Invergordon', 'NSW', -31.970, 152.070),
('2422', 'Kia Ora', 'NSW', -31.970, 152.070),
('2422', 'Mares Run', 'NSW', -31.970, 152.070),
('2422', 'Mernot', 'NSW', -31.970, 152.070),
('2422', 'Mograni', 'NSW', -31.970, 152.070),
('2422', 'Moppy', 'NSW', -31.970, 152.070),
('2422', 'Rawdon Vale', 'NSW', -31.970, 152.070),
('2422', 'Rookhurst', 'NSW', -31.970, 152.070),
('2422', 'Stratford', 'NSW', -31.970, 152.070),
('2422', 'Terreel', 'NSW', -31.970, 152.070),
('2422', 'Tibbuc', 'NSW', -31.970, 152.070),
('2422', 'Titaatee Creek', 'NSW', -31.970, 152.070),
('2422', 'Tugrabakh', 'NSW', -31.970, 152.070),
('2422', 'Wallanbah', 'NSW', -31.970, 152.070),
('2422', 'Wards River', 'NSW', -31.970, 152.070),
('2422', 'Waukivory', 'NSW', -31.970, 152.070),
('2422', 'Woko', 'NSW', -31.970, 152.070),
('2423', 'Bombah Point', 'NSW', -32.490, 152.280),
('2423', 'Boolambayte', 'NSW', -32.490, 152.280),
('2423', 'Bulahdelah', 'NSW', -32.490, 152.280),
('2423', 'Bungwahl', 'NSW', -32.490, 152.280),
('2423', 'Coolongolook', 'NSW', -32.490, 152.280),
('2423', 'Crawford River', 'NSW', -32.490, 152.280),
('2423', 'Markwell', 'NSW', -32.490, 152.280),
('2423', 'Mayers Flat', 'NSW', -32.490, 152.280),
('2423', 'Mungo Brush', 'NSW', -32.490, 152.280),
('2423', 'Myall Lake', 'NSW', -32.490, 152.280),
('2423', 'Nerong', 'NSW', -32.490, 152.280),
('2423', 'Seal Rocks', 'NSW', -32.490, 152.280),
('2423', 'Topi Topi', 'NSW', -32.490, 152.280),
('2423', 'Upper Myall', 'NSW', -32.490, 152.280),
('2423', 'Violet Hill', 'NSW', -32.490, 152.280),
('2423', 'Wang Wauk', 'NSW', -32.490, 152.280),
('2423', 'Warranulla', 'NSW', -32.490, 152.280),
('2423', 'Willina', 'NSW', -32.490, 152.280),
('2423', 'Wootton', 'NSW', -32.490, 152.280),
('2423', 'Yagon', 'NSW', -32.490, 152.280),
('2424', 'Caffreys Flat', 'NSW', -31.800, 152.070),
('2424', 'Cells River', 'NSW', -31.800, 152.070),
('2424', 'Cooplacurripa', 'NSW', -31.800, 152.070),
('2424', 'Cundle Flat', 'NSW', -31.800, 152.070),
('2424', 'Knorrit Flat', 'NSW', -31.800, 152.070),
('2424', 'Knorrit Forest', 'NSW', -31.800, 152.070),
('2424', 'Mount George', 'NSW', -31.800, 152.070),
('2424', 'Number One', 'NSW', -31.800, 152.070),
('2424', 'Tiri', 'NSW', -31.800, 152.070),
('2425', 'Allworth', 'NSW', -32.540, 151.960),
('2425', 'Booral', 'NSW', -32.540, 151.960),
('2425', 'Girvan', 'NSW', -32.540, 151.960),
('2425', 'Stroud', 'NSW', -32.540, 151.960),
('2425', 'The Branch', 'NSW', -32.540, 151.960),
('2425', 'Washpool', 'NSW', -32.540, 151.960),
('2426', 'Coopernook', 'NSW', -31.830, 152.610),
('2426', 'Langley Vale', 'NSW', -31.830, 152.610),
('2426', 'Moto', 'NSW', -31.830, 152.610),
('2427', 'Crowdy Head', 'NSW', -31.840, 152.740),
('2427', 'Harrington', 'NSW', -31.840, 152.740),
('2428', 'Blueys Beach', 'NSW', -32.350, 152.540),
('2428', 'Boomerang Beach', 'NSW', -32.350, 152.540),
('2428', 'Booti Booti', 'NSW', -32.350, 152.540),
('2428', 'Charlotte Bay', 'NSW', -32.350, 152.540),
('2428', 'Coomba Bay', 'NSW', -32.350, 152.540),
('2428', 'Coomba Park', 'NSW', -32.350, 152.540),
('2428', 'Darawank', 'NSW', -32.350, 152.540),
('2428', 'Elizabeth Beach', 'NSW', -32.350, 152.540),
('2428', 'Forster', 'NSW', -32.350, 152.540),
('2428', 'Forster Shopping Village', 'NSW', -32.350, 152.540),
('2428', 'Green Point', 'NSW', -32.350, 152.540),
('2428', 'Pacific Palms', 'NSW', -32.350, 152.540),
('2428', 'Sandbar', 'NSW', -32.350, 152.540),
('2428', 'Shallow Bay', 'NSW', -32.350, 152.540),
('2428', 'Smiths Lake', 'NSW', -32.350, 152.540),
('2428', 'Tarbuck Bay', 'NSW', -32.350, 152.540),
('2428', 'Tiona', 'NSW', -32.350, 152.540),
('2428', 'Tuncurry', 'NSW', -32.350, 152.540),
('2428', 'Wallingat', 'NSW', -32.350, 152.540),
('2428', 'Wallis Lake', 'NSW', -32.350, 152.540),
('2428', 'Whoota', 'NSW', -32.350, 152.540),
('2429', 'Bobin', 'NSW', -31.730, 152.280),
('2429', 'Boorganna', 'NSW', -31.730, 152.280),
('2429', 'Bucca Wauka', 'NSW', -31.730, 152.280),
('2429', 'Bulga Forest', 'NSW', -31.730, 152.280),
('2429', 'Bunyah', 'NSW', -31.730, 152.280),
('2429', 'Burrell Creek', 'NSW', -31.730, 152.280),
('2429', 'Caparra', 'NSW', -31.730, 152.280),
('2429', 'Cedar Party', 'NSW', -31.730, 152.280),
('2429', 'Comboyne', 'NSW', -31.730, 152.280),
('2429', 'Dingo Forest', 'NSW', -31.730, 152.280),
('2429', 'Dollys Flat', 'NSW', -31.730, 152.280),
('2429', 'Dyers Crossing', 'NSW', -31.730, 152.280),
('2429', 'Elands', 'NSW', -31.730, 152.280),
('2429', 'Firefly', 'NSW', -31.730, 152.280),
('2429', 'Innes View', 'NSW', -31.730, 152.280),
('2429', 'Karaak Flat', 'NSW', -31.730, 152.280),
('2429', 'Khatambuhl', 'NSW', -31.730, 152.280),
('2429', 'Killabakh', 'NSW', -31.730, 152.280),
('2429', 'Killawarra', 'NSW', -31.730, 152.280),
('2429', 'Kimbriki', 'NSW', -31.730, 152.280),
('2429', 'Kippaxs', 'NSW', -31.730, 152.280),
('2429', 'Krambach', 'NSW', -31.730, 152.280),
('2429', 'Kundibakh', 'NSW', -31.730, 152.280),
('2429', 'Marlee', 'NSW', -31.730, 152.280),
('2429', 'Mooral Creek', 'NSW', -31.730, 152.280),
('2429', 'Strathcedar', 'NSW', -31.730, 152.280),
('2429', 'The Bight', 'NSW', -31.730, 152.280),
('2429', 'Tipperary', 'NSW', -31.730, 152.280),
('2429', 'Warriwillah', 'NSW', -31.730, 152.280),
('2429', 'Wherrol Flat', 'NSW', -31.730, 152.280),
('2429', 'Wingham', 'NSW', -31.730, 152.280),
('2429', 'Yarratt Forest', 'NSW', -31.730, 152.280),
('2430', 'Black Head', 'NSW', -32.070, 152.540),
('2430', 'Bohnock', 'NSW', -32.070, 152.540),
('2430', 'Bootawa', 'NSW', -32.070, 152.540),
('2430', 'Brimbin', 'NSW', -32.070, 152.540),
('2430', 'Cabbage Tree Island', 'NSW', -32.070, 152.540),
('2430', 'Chatham', 'NSW', -32.070, 152.540),
('2430', 'Croki', 'NSW', -32.070, 152.540),
('2430', 'Cundletown', 'NSW', -32.070, 152.540),
('2430', 'Diamond Beach', 'NSW', -32.070, 152.540),
('2430', 'Dumaresq Island', 'NSW', -32.070, 152.540),
('2430', 'Failford', 'NSW', -32.070, 152.540),
('2430', 'Ghinni Ghinni', 'NSW', -32.070, 152.540),
('2430', 'Glenthorne', 'NSW', -32.070, 152.540),
('2430', 'Hallidays Point', 'NSW', -32.070, 152.540),
('2430', 'Hillville', 'NSW', -32.070, 152.540),
('2430', 'Jones Island', 'NSW', -32.070, 152.540),
('2430', 'Kiwarrak', 'NSW', -32.070, 152.540),
('2430', 'Koorainghat', 'NSW', -32.070, 152.540),
('2430', 'Kundle Kundle', 'NSW', -32.070, 152.540),
('2430', 'Lansdowne', 'NSW', -32.070, 152.540),
('2430', 'Lansdowne Forest', 'NSW', -32.070, 152.540),
('2430', 'Manning Point', 'NSW', -32.070, 152.540),
('2430', 'Melinga', 'NSW', -32.070, 152.540),
('2430', 'Mitchells Island', 'NSW', -32.070, 152.540),
('2430', 'Mondrook', 'NSW', -32.070, 152.540),
('2430', 'Old Bar', 'NSW', -32.070, 152.540),
('2430', 'Oxley Island', 'NSW', -32.070, 152.540),
('2430', 'Pampoolah', 'NSW', -32.070, 152.540),
('2430', 'Possum Brush', 'NSW', -32.070, 152.540),
('2430', 'Purfleet', 'NSW', -32.070, 152.540),
('2430', 'Rainbow Flat', 'NSW', -32.070, 152.540),
('2430', 'Red Head', 'NSW', -32.070, 152.540),
('2430', 'Saltwater', 'NSW', -32.070, 152.540),
('2430', 'Tallwoods Village', 'NSW', -32.070, 152.540),
('2430', 'Taree', 'NSW', -32.070, 152.540),
('2430', 'Taree South', 'NSW', -32.070, 152.540),
('2430', 'Tinonee', 'NSW', -32.070, 152.540),
('2430', 'Upper Lansdowne', 'NSW', -32.070, 152.540),
('2430', 'Wallabi Point', 'NSW', -32.070, 152.540),
('2431', 'Arakoon', 'NSW', -30.890, 153.070),
('2431', 'Jerseyville', 'NSW', -30.890, 153.070),
('2431', 'South West Rocks', 'NSW', -30.890, 153.070),
('2439', 'Batar Creek', 'NSW', -31.660, 152.680),
('2439', 'Black Creek', 'NSW', -31.660, 152.680),
('2439', 'Kendall', 'NSW', -31.660, 152.680),
('2439', 'Kerewong', 'NSW', -31.660, 152.680),
('2439', 'Kew', 'NSW', -31.660, 152.680),
('2439', 'Logans Crossing', 'NSW', -31.660, 152.680),
('2439', 'Lorne', 'NSW', -31.660, 152.680),
('2439', 'Rossglen', 'NSW', -31.660, 152.680),
('2439', 'Swans Crossing', 'NSW', -31.660, 152.680),
('2439', 'Upsalls Creek', 'NSW', -31.660, 152.680),
('2440', 'Aldavilla', 'NSW', -31.060, 152.770),
('2440', 'Austral Eden', 'NSW', -31.060, 152.770),
('2440', 'Bellbrook', 'NSW', -31.060, 152.770),
('2440', 'Bellimbopinni', 'NSW', -31.060, 152.770),
('2440', 'Belmore River', 'NSW', -31.060, 152.770),
('2440', 'Burnt Bridge', 'NSW', -31.060, 152.770),
('2440', 'Carrai', 'NSW', -31.060, 152.770),
('2440', 'Clybucca', 'NSW', -31.060, 152.770),
('2440', 'Collombatti', 'NSW', -31.060, 152.770),
('2440', 'Comara', 'NSW', -31.060, 152.770),
('2440', 'Corangula', 'NSW', -31.060, 152.770),
('2440', 'Crescent Head', 'NSW', -31.060, 152.770),
('2440', 'Deep Creek', 'NSW', -31.060, 152.770),
('2440', 'Dondingalong', 'NSW', -31.060, 152.770),
('2440', 'East Kempsey', 'NSW', -31.060, 152.770),
('2440', 'Euroka', 'NSW', -31.060, 152.770),
('2440', 'Frederickton', 'NSW', -31.060, 152.770),
('2440', 'Gladstone', 'NSW', -31.060, 152.770),
('2440', 'Greenhill', 'NSW', -31.060, 152.770),
('2440', 'Hampden Hall', 'NSW', -31.060, 152.770),
('2440', 'Hat Head', 'NSW', -31.060, 152.770),
('2440', 'Hickeys Creek', 'NSW', -31.060, 152.770),
('2440', 'Kempsey', 'NSW', -31.060, 152.770),
('2440', 'Kinchela', 'NSW', -31.060, 152.770),
('2440', 'Lower Creek', 'NSW', -31.060, 152.770),
('2440', 'Millbank', 'NSW', -31.060, 152.770),
('2440', 'Mooneba', 'NSW', -31.060, 152.770),
('2440', 'Moparrabah', 'NSW', -31.060, 152.770),
('2440', 'Mungay Creek', 'NSW', -31.060, 152.770),
('2440', 'Old Station', 'NSW', -31.060, 152.770),
('2440', 'Pola Creek', 'NSW', -31.060, 152.770),
('2440', 'Rainbow Reach', 'NSW', -31.060, 152.770),
('2440', 'Seven Oaks', 'NSW', -31.060, 152.770),
('2440', 'Sherwood', 'NSW', -31.060, 152.770),
('2440', 'Skillion Flat', 'NSW', -31.060, 152.770),
('2440', 'Smithtown', 'NSW', -31.060, 152.770),
('2440', 'South Kempsey', 'NSW', -31.060, 152.770),
('2440', 'Summer Island', 'NSW', -31.060, 152.770),
('2440', 'Temagog', 'NSW', -31.060, 152.770),
('2440', 'Toorooka', 'NSW', -31.060, 152.770),
('2440', 'Turners Flat', 'NSW', -31.060, 152.770),
('2440', 'Verges Creek', 'NSW', -31.060, 152.770),
('2440', 'West Kempsey', 'NSW', -31.060, 152.770),
('2440', 'Willawarrin', 'NSW', -31.060, 152.770),
('2440', 'Willi Willi', 'NSW', -31.060, 152.770),
('2440', 'Wittitrin', 'NSW', -31.060, 152.770),
('2440', 'Yarravel', 'NSW', -31.060, 152.770),
('2440', 'Yessabah', 'NSW', -31.060, 152.770),
('2441', 'Allgomera', 'NSW', -30.820, 152.830),
('2441', 'Ballengarra', 'NSW', -30.820, 152.830),
('2441', 'Barraganyatti', 'NSW', -30.820, 152.830),
('2441', 'Bonville', 'NSW', -30.820, 152.830),
('2441', 'Bril Bril', 'NSW', -30.820, 152.830),
('2441', 'Brinerville', 'NSW', -30.820, 152.830),
('2441', 'Cooperabung', 'NSW', -30.820, 152.830),
('2441', 'Eungai Creek', 'NSW', -30.820, 152.830),
('2441', 'Eungai Rail', 'NSW', -30.820, 152.830),
('2441', 'Fishermans Reach', 'NSW', -30.820, 152.830),
('2441', 'Grassy Head', 'NSW', -30.820, 152.830),
('2441', 'Gum Scrub', 'NSW', -30.820, 152.830),
('2441', 'Hacks Ferry', 'NSW', -30.820, 152.830),
('2441', 'Kippara', 'NSW', -30.820, 152.830),
('2441', 'Kundabung', 'NSW', -30.820, 152.830),
('2441', 'Rollands Plains', 'NSW', -30.820, 152.830),
('2441', 'Stuarts Point', 'NSW', -30.820, 152.830),
('2441', 'Tamban', 'NSW', -30.820, 152.830),
('2441', 'Telegraph Point', 'NSW', -30.820, 152.830),
('2441', 'Upper Rollands Plains', 'NSW', -30.820, 152.830),
('2441', 'Yarrahapinni', 'NSW', -30.820, 152.830),
('2442', 'Kempsey Msc', 'NSW', 0.000, 0.000),
('2442', 'Mid North Coast Msc', 'NSW', 0.000, 0.000),
('2443', 'Bobs Creek', 'NSW', -31.580, 152.740),
('2443', 'Camden Head', 'NSW', -31.580, 152.740),
('2443', 'Coralville', 'NSW', -31.580, 152.740),
('2443', 'Crowdy Bay National Park', 'NSW', -31.580, 152.740),
('2443', 'Deauville', 'NSW', -31.580, 152.740),
('2443', 'Diamond Head', 'NSW', -31.580, 152.740),
('2443', 'Dunbogan', 'NSW', -31.580, 152.740),
('2443', 'Hannam Vale', 'NSW', -31.580, 152.740),
('2443', 'Herons Creek', 'NSW', -31.580, 152.740),
('2443', 'Johns River', 'NSW', -31.580, 152.740),
('2443', 'Lakewood', 'NSW', -31.580, 152.740),
('2443', 'Laurieton', 'NSW', -31.580, 152.740),
('2443', 'Middle Brother', 'NSW', -31.580, 152.740),
('2443', 'Moorland', 'NSW', -31.580, 152.740),
('2443', 'North Brother', 'NSW', -31.580, 152.740),
('2443', 'North Haven', 'NSW', -31.580, 152.740),
('2443', 'Stewarts River', 'NSW', -31.580, 152.740),
('2443', 'Waitui', 'NSW', -31.580, 152.740),
('2443', 'West Haven', 'NSW', -31.580, 152.740),
('2444', 'Blackmans Point', 'NSW', -31.400, 152.850),
('2444', 'Fernbank Creek', 'NSW', -31.400, 152.850),
('2444', 'Flynns Beach', 'NSW', -31.400, 152.850),
('2444', 'Lighthouse Beach', 'NSW', -31.400, 152.850),
('2444', 'Limeburners Creek', 'NSW', -31.400, 152.850),
('2444', 'North Shore', 'NSW', -31.400, 152.850),
('2444', 'Port Macquarie', 'NSW', -31.400, 152.850),
('2444', 'Port Macquarie BC', 'NSW', -31.400, 152.850),
('2444', 'Riverside', 'NSW', -31.400, 152.850),
('2444', 'Settlement City', 'NSW', -31.400, 152.850),
('2444', 'The Hatch', 'NSW', -31.400, 152.850),
('2444', 'Thrumster', 'NSW', -31.400, 152.850),
('2445', 'Bonny Hills', 'NSW', -31.590, 152.840),
('2445', 'Grants Beach', 'NSW', -31.590, 152.840),
('2445', 'Jolly Nose', 'NSW', -31.590, 152.840),
('2445', 'Lake Cathie', 'NSW', -31.590, 152.840),
('2446', 'Bagnoo', 'NSW', -31.460, 152.530),
('2446', 'Bago', 'NSW', -31.460, 152.530),
('2446', 'Banda Banda', 'NSW', -31.460, 152.530),
('2446', 'Beechwood', 'NSW', -31.460, 152.530),
('2446', 'Bellangry', 'NSW', -31.460, 152.530),
('2446', 'Birdwood', 'NSW', -31.460, 152.530),
('2446', 'Brombin', 'NSW', -31.460, 152.530),
('2446', 'Byabarra', 'NSW', -31.460, 152.530),
('2446', 'Cairncross', 'NSW', -31.460, 152.530),
('2446', 'Crosslands', 'NSW', -31.460, 152.530),
('2446', 'Debenham', 'NSW', -31.460, 152.530),
('2446', 'Doyles River', 'NSW', -31.460, 152.530),
('2446', 'Ellenborough', 'NSW', -31.460, 152.530),
('2446', 'Forbes River', 'NSW', -31.460, 152.530),
('2446', 'Frazers Creek', 'NSW', -31.460, 152.530),
('2446', 'Gearys Flat', 'NSW', -31.460, 152.530),
('2446', 'Hartys Plains', 'NSW', -31.460, 152.530),
('2446', 'Hollisdale', 'NSW', -31.460, 152.530),
('2446', 'Huntingdon', 'NSW', -31.460, 152.530),
('2446', 'Hyndmans Creek', 'NSW', -31.460, 152.530),
('2446', 'Kindee', 'NSW', -31.460, 152.530),
('2446', 'King Creek', 'NSW', -31.460, 152.530),
('2446', 'Lake Innes', 'NSW', -31.460, 152.530),
('2446', 'Long Flat', 'NSW', -31.460, 152.530),
('2446', 'Lower Pappinbarra', 'NSW', -31.460, 152.530),
('2446', 'Marlo Merrican', 'NSW', -31.460, 152.530),
('2446', 'Mortons Creek', 'NSW', -31.460, 152.530),
('2446', 'Mount Seaview', 'NSW', -31.460, 152.530),
('2446', 'Pappinbarra', 'NSW', -31.460, 152.530),
('2446', 'Pembrooke', 'NSW', -31.460, 152.530),
('2446', 'Pipeclay', 'NSW', -31.460, 152.530),
('2446', 'Rawdon Island', 'NSW', -31.460, 152.530),
('2446', 'Redbank', 'NSW', -31.460, 152.530),
('2446', 'Rosewood', 'NSW', -31.460, 152.530),
('2446', 'Sancrox', 'NSW', -31.460, 152.530),
('2446', 'Toms Creek', 'NSW', -31.460, 152.530),
('2446', 'Upper Pappinbarra', 'NSW', -31.460, 152.530),
('2446', 'Wauchope', 'NSW', -31.460, 152.530),
('2446', 'Werrikimbe', 'NSW', -31.460, 152.530),
('2446', 'Yarras', 'NSW', -31.460, 152.530),
('2446', 'Yippin Creek', 'NSW', -31.460, 152.530),
('2447', 'Bakers Creek', 'NSW', -30.800, 152.730),
('2447', 'Burrapine', 'NSW', -30.800, 152.730),
('2447', 'Congarinni', 'NSW', -30.800, 152.730),
('2447', 'Congarinni North', 'NSW', -30.800, 152.730),
('2447', 'Donnellyville', 'NSW', -30.800, 152.730),
('2447', 'Gumma', 'NSW', -30.800, 152.730),
('2447', 'Macksville', 'NSW', -30.800, 152.730),
('2447', 'Newee Creek', 'NSW', -30.800, 152.730),
('2447', 'North Macksville', 'NSW', -30.800, 152.730),
('2447', 'Scotts Head', 'NSW', -30.800, 152.730),
('2447', 'Talarm', 'NSW', -30.800, 152.730),
('2447', 'Taylors Arm', 'NSW', -30.800, 152.730),
('2447', 'Thumb Creek', 'NSW', -30.800, 152.730),
('2447', 'Upper Taylors Arm', 'NSW', -30.800, 152.730),
('2447', 'Utungun', 'NSW', -30.800, 152.730),
('2447', 'Warrell Creek', 'NSW', -30.800, 152.730),
('2447', 'Way Way', 'NSW', -30.800, 152.730),
('2447', 'Wirrimbi', 'NSW', -30.800, 152.730),
('2447', 'Yarranbella', 'NSW', -30.800, 152.730),
('2448', 'Hyland Park', 'NSW', -30.620, 153.000),
('2448', 'Nambucca Heads', 'NSW', -30.620, 153.000),
('2448', 'Valla', 'NSW', -30.620, 153.000),
('2448', 'Valla Beach', 'NSW', -30.620, 153.000),
('2449', 'Argents Hill', 'NSW', -30.620, 152.750),
('2449', 'Bowraville', 'NSW', -30.620, 152.750),
('2449', 'Buckra Bendinni', 'NSW', -30.620, 152.750),
('2449', 'Girralong', 'NSW', -30.620, 152.750),
('2449', 'Kennaicle Creek', 'NSW', -30.620, 152.750),
('2449', 'Killiekrankie', 'NSW', -30.620, 152.750),
('2449', 'Missabotti', 'NSW', -30.620, 152.750),
('2449', 'South Arm', 'NSW', -30.620, 152.750),
('2449', 'Tewinga', 'NSW', -30.620, 152.750),
('2450', 'Boambee', 'NSW', -30.340, 153.070),
('2450', 'Brooklana', 'NSW', -30.340, 153.070),
('2450', 'Bucca', 'NSW', -30.340, 153.070),
('2450', 'Coffs Harbour', 'NSW', -30.340, 153.070),
('2450', 'Coffs Harbour Jetty', 'NSW', -30.340, 153.070),
('2450', 'Coffs Harbour Plaza', 'NSW', -30.340, 153.070),
('2450', 'Coramba', 'NSW', -30.340, 153.070),
('2450', 'Glenreagh', 'NSW', -30.340, 153.070),
('2450', 'Karangi', 'NSW', -30.340, 153.070),
('2450', 'Korora', 'NSW', -30.340, 153.070),
('2450', 'Lowanna', 'NSW', -30.340, 153.070),
('2450', 'Moonee Beach', 'NSW', -30.340, 153.070),
('2450', 'Nana Glen', 'NSW', -30.340, 153.070),
('2450', 'North Boambee Valley', 'NSW', -30.340, 153.070),
('2450', 'Sapphire Beach', 'NSW', -30.340, 153.070),
('2450', 'Sherwood', 'NSW', -30.340, 153.070),
('2450', 'Ulong', 'NSW', -30.340, 153.070),
('2450', 'Upper Orara', 'NSW', -30.340, 153.070),
('2452', 'Boambee East', 'NSW', -30.340, 153.080),
('2452', 'Sawtell', 'NSW', -30.340, 153.080),
('2452', 'Toormina', 'NSW', -30.340, 153.080),
('2453', 'Bielsdown Hills', 'NSW', -30.140, 152.600),
('2453', 'Billys Creek', 'NSW', -30.140, 152.600),
('2453', 'Bostobrick', 'NSW', -30.140, 152.600),
('2453', 'Cascade', 'NSW', -30.140, 152.600),
('2453', 'Clouds Creek', 'NSW', -30.140, 152.600),
('2453', 'Deer Vale', 'NSW', -30.140, 152.600),
('2453', 'Dorrigo', 'NSW', -30.140, 152.600),
('2453', 'Dorrigo Mountain', 'NSW', -30.140, 152.600),
('2453', 'Dundurrabin', 'NSW', -30.140, 152.600),
('2453', 'Ebor', 'NSW', -30.140, 152.600),
('2453', 'Fernbrook', 'NSW', -30.140, 152.600),
('2453', 'Hernani', 'NSW', -30.140, 152.600),
('2453', 'Marengo', 'NSW', -30.140, 152.600),
('2453', 'Megan', 'NSW', -30.140, 152.600),
('2453', 'Moonpar', 'NSW', -30.140, 152.600),
('2453', 'Never Never', 'NSW', -30.140, 152.600),
('2453', 'North Dorrigo', 'NSW', -30.140, 152.600),
('2453', 'Tallowwood Ridge', 'NSW', -30.140, 152.600),
('2453', 'Tyringham', 'NSW', -30.140, 152.600),
('2453', 'Wild Cattle Creek', 'NSW', -30.140, 152.600),
('2454', 'Bellingen', 'NSW', -30.450, 152.900),
('2454', 'Brierfield', 'NSW', -30.450, 152.900),
('2454', 'Bundagen', 'NSW', -30.450, 152.900),
('2454', 'Darkwood', 'NSW', -30.450, 152.900),
('2454', 'Fernmount', 'NSW', -30.450, 152.900),
('2454', 'Gleniffer', 'NSW', -30.450, 152.900),
('2454', 'Kalang', 'NSW', -30.450, 152.900),
('2454', 'Mylestom', 'NSW', -30.450, 152.900),
('2454', 'Raleigh', 'NSW', -30.450, 152.900),
('2454', 'Repton', 'NSW', -30.450, 152.900),
('2454', 'Spicketts Creek', 'NSW', -30.450, 152.900),
('2454', 'Thora', 'NSW', -30.450, 152.900),
('2454', 'Valery', 'NSW', -30.450, 152.900),
('2455', 'Urunga', 'NSW', -30.520, 152.980),
('2456', 'Arrawarra', 'NSW', -30.060, 153.190),
('2456', 'Arrawarra Headland', 'NSW', -30.060, 153.190),
('2456', 'Corindi Beach', 'NSW', -30.060, 153.190),
('2456', 'Emerald Beach', 'NSW', -30.060, 153.190),
('2456', 'Mullaway', 'NSW', -30.060, 153.190),
('2456', 'Red Rock', 'NSW', -30.060, 153.190),
('2456', 'Safety Beach', 'NSW', -30.060, 153.190),
('2456', 'Sandy Beach', 'NSW', -30.060, 153.190),
('2456', 'Upper Corindi', 'NSW', -30.060, 153.190),
('2456', 'Woolgoolga', 'NSW', -30.060, 153.190),
('2460', 'Alumy Creek', 'NSW', -29.050, 152.590),
('2460', 'Barcoongere', 'NSW', -29.050, 152.590),
('2460', 'Barretts Creek', 'NSW', -29.050, 152.590),
('2460', 'Baryulgil', 'NSW', -29.050, 152.590),
('2460', 'Blaxlands Creek', 'NSW', -29.050, 152.590),
('2460', 'Bom Bom', 'NSW', -29.050, 152.590),
('2460', 'Bookram', 'NSW', -29.050, 152.590),
('2460', 'Braunstone', 'NSW', -29.050, 152.590),
('2460', 'Brushgrove', 'NSW', -29.050, 152.590),
('2460', 'Buccarumbi', 'NSW', -29.050, 152.590),
('2460', 'Calamia', 'NSW', -29.050, 152.590),
('2460', 'Cangai', 'NSW', -29.050, 152.590),
('2460', 'Carnham', 'NSW', -29.050, 152.590),
('2460', 'Carrs Creek', 'NSW', -29.050, 152.590),
('2460', 'Carrs Island', 'NSW', -29.050, 152.590),
('2460', 'Carrs Peninsular', 'NSW', -29.050, 152.590),
('2460', 'Chaelundi', 'NSW', -29.050, 152.590),
('2460', 'Chambigne', 'NSW', -29.050, 152.590),
('2460', 'Clarenza', 'NSW', -29.050, 152.590),
('2460', 'Clifden', 'NSW', -29.050, 152.590),
('2460', 'Coaldale', 'NSW', -29.050, 152.590),
('2460', 'Collum Collum', 'NSW', -29.050, 152.590),
('2460', 'Coombadjha', 'NSW', -29.050, 152.590),
('2460', 'Copmanhurst', 'NSW', -29.050, 152.590),
('2460', 'Coutts Crossing', 'NSW', -29.050, 152.590),
('2460', 'Cowper', 'NSW', -29.050, 152.590),
('2460', 'Crowther Island', 'NSW', -29.050, 152.590),
('2460', 'Dalmorton', 'NSW', -29.050, 152.590),
('2460', 'Deep Creek', 'NSW', -29.050, 152.590),
('2460', 'Dilkoon', 'NSW', -29.050, 152.590),
('2460', 'Dirty Creek', 'NSW', -29.050, 152.590),
('2460', 'Dumbudgery', 'NSW', -29.050, 152.590),
('2460', 'Eatonsville', 'NSW', -29.050, 152.590),
('2460', 'Eighteen Mile', 'NSW', -29.050, 152.590),
('2460', 'Elland', 'NSW', -29.050, 152.590),
('2460', 'Fine Flower', 'NSW', -29.050, 152.590),
('2460', 'Fortis Creek', 'NSW', -29.050, 152.590),
('2460', 'Glenugie', 'NSW', -29.050, 152.590),
('2460', 'Grafton', 'NSW', -29.050, 152.590),
('2460', 'Grafton West', 'NSW', -29.050, 152.590),
('2460', 'Great Marlow', 'NSW', -29.050, 152.590),
('2460', 'Gurranang', 'NSW', -29.050, 152.590),
('2460', 'Halfway Creek', 'NSW', -29.050, 152.590),
('2460', 'Heifer Station', 'NSW', -29.050, 152.590),
('2460', 'Jackadgery', 'NSW', -29.050, 152.590),
('2460', 'Junction Hill', 'NSW', -29.050, 152.590),
('2460', 'Kangaroo Creek', 'NSW', -29.050, 152.590),
('2460', 'Koolkhan', 'NSW', -29.050, 152.590),
('2460', 'Kremnos', 'NSW', -29.050, 152.590),
('2460', 'Kungala', 'NSW', -29.050, 152.590),
('2460', 'Kyarran', 'NSW', -29.050, 152.590),
('2460', 'Lanitza', 'NSW', -29.050, 152.590),
('2460', 'Lawrence', 'NSW', -29.050, 152.590),
('2460', 'Levenstrath', 'NSW', -29.050, 152.590),
('2460', 'Lilydale', 'NSW', -29.050, 152.590),
('2460', 'Lionsville', 'NSW', -29.050, 152.590),
('2460', 'Lower Southgate', 'NSW', -29.050, 152.590),
('2460', 'Malabugilmah', 'NSW', -29.050, 152.590),
('2460', 'Moleville Creek', 'NSW', -29.050, 152.590),
('2460', 'Mountain View', 'NSW', -29.050, 152.590),
('2460', 'Mylneford', 'NSW', -29.050, 152.590),
('2460', 'Newbold', 'NSW', -29.050, 152.590),
('2460', 'Nymboida', 'NSW', -29.050, 152.590),
('2460', 'Pulganbar', 'NSW', -29.050, 152.590),
('2460', 'Punchbowl', 'NSW', -29.050, 152.590),
('2460', 'Ramornie', 'NSW', -29.050, 152.590),
('2460', 'Rushforth', 'NSW', -29.050, 152.590),
('2460', 'Sandy Crossing', 'NSW', -29.050, 152.590),
('2460', 'Seelands', 'NSW', -29.050, 152.590),
('2460', 'Shannondale', 'NSW', -29.050, 152.590),
('2460', 'Smiths Creek', 'NSW', -29.050, 152.590),
('2460', 'South Arm', 'NSW', -29.050, 152.590),
('2460', 'South Grafton', 'NSW', -29.050, 152.590),
('2460', 'Southampton', 'NSW', -29.050, 152.590),
('2460', 'Southgate', 'NSW', -29.050, 152.590),
('2460', 'Stockyard Creek', 'NSW', -29.050, 152.590),
('2460', 'The Pinnacles', 'NSW', -29.050, 152.590),
('2460', 'The Whiteman', 'NSW', -29.050, 152.590),
('2460', 'Towallum', 'NSW', -29.050, 152.590),
('2460', 'Trenayr', 'NSW', -29.050, 152.590),
('2460', 'Tyndale', 'NSW', -29.050, 152.590),
('2460', 'Upper Copmanhurst', 'NSW', -29.050, 152.590),
('2460', 'Upper Fine Flower', 'NSW', -29.050, 152.590),
('2460', 'Warragai Creek', 'NSW', -29.050, 152.590),
('2460', 'Washpool', 'NSW', -29.050, 152.590),
('2460', 'Waterview', 'NSW', -29.050, 152.590),
('2460', 'Waterview Heights', 'NSW', -29.050, 152.590),
('2460', 'Wells Crossing', 'NSW', -29.050, 152.590),
('2460', 'Whiteman Creek', 'NSW', -29.050, 152.590),
('2460', 'Winegrove', 'NSW', -29.050, 152.590),
('2460', 'Wombat Creek', 'NSW', -29.050, 152.590),
('2462', 'Calliope', 'NSW', -29.620, 153.070),
('2462', 'Coldstream', 'NSW', -29.620, 153.070),
('2462', 'Diggers Camp', 'NSW', -29.620, 153.070),
('2462', 'Gilletts Ridge', 'NSW', -29.620, 153.070),
('2462', 'Lake Hiawatha', 'NSW', -29.620, 153.070),
('2462', 'Lavadia', 'NSW', -29.620, 153.070),
('2462', 'Minnie Water', 'NSW', -29.620, 153.070),
('2462', 'Pillar Valley', 'NSW', -29.620, 153.070),
('2462', 'Swan Creek', 'NSW', -29.620, 153.070),
('2462', 'Tucabia', 'NSW', -29.620, 153.070),
('2462', 'Ulmarra', 'NSW', -29.620, 153.070),
('2462', 'Wooli', 'NSW', -29.620, 153.070),
('2463', 'Ashby', 'NSW', -29.420, 153.190),
('2463', 'Ashby Heights', 'NSW', -29.420, 153.190),
('2463', 'Ashby Island', 'NSW', -29.420, 153.190),
('2463', 'Brooms Head', 'NSW', -29.420, 153.190),
('2463', 'Gulmarrad', 'NSW', -29.420, 153.190),
('2463', 'Ilarwill', 'NSW', -29.420, 153.190),
('2463', 'Jacky Bulbin Flat', 'NSW', -29.420, 153.190),
('2463', 'James Creek', 'NSW', -29.420, 153.190),
('2463', 'Maclean', 'NSW', -29.420, 153.190),
('2463', 'Palmers Channel', 'NSW', -29.420, 153.190),
('2463', 'Palmers Island', 'NSW', -29.420, 153.190),
('2463', 'Sandon', 'NSW', -29.420, 153.190),
('2463', 'Shark Creek', 'NSW', -29.420, 153.190),
('2463', 'Taloumbi', 'NSW', -29.420, 153.190),
('2463', 'The Sandon', 'NSW', -29.420, 153.190),
('2463', 'Townsend', 'NSW', -29.420, 153.190),
('2463', 'Tullymorgan', 'NSW', -29.420, 153.190),
('2463', 'Woodford Island', 'NSW', -29.420, 153.190),
('2464', 'Angourie', 'NSW', -29.480, 153.360),
('2464', 'Freeburn Island', 'NSW', -29.480, 153.360),
('2464', 'Micalo Island', 'NSW', -29.480, 153.360),
('2464', 'Wooloweyah', 'NSW', -29.480, 153.360),
('2464', 'Yamba', 'NSW', -29.480, 153.360),
('2464', 'Yuraygir', 'NSW', -29.480, 153.360),
('2465', 'Harwood', 'NSW', -29.420, 153.240),
('2466', 'Iluka', 'NSW', -29.410, 153.350),
('2466', 'The Freshwater', 'NSW', -29.410, 153.350),
('2466', 'Woody Head', 'NSW', -29.410, 153.350),
('2469', 'Alice', 'NSW', -29.340, 153.000),
('2469', 'Banyabba', 'NSW', -29.340, 153.000),
('2469', 'Bean Creek', 'NSW', -29.340, 153.000),
('2469', 'Bingeebeebra Creek', 'NSW', -29.340, 153.000),
('2469', 'Bonalbo', 'NSW', -29.340, 153.000),
('2469', 'Bottle Creek', 'NSW', -29.340, 153.000),
('2469', 'Bulldog', 'NSW', -29.340, 153.000),
('2469', 'Bungawalbin', 'NSW', -29.340, 153.000),
('2469', 'Busbys Flat', 'NSW', -29.340, 153.000),
('2469', 'Cambridge Plateau', 'NSW', -29.340, 153.000),
('2469', 'Camira', 'NSW', -29.340, 153.000),
('2469', 'Capeen Creek', 'NSW', -29.340, 153.000),
('2469', 'Chatsworth', 'NSW', -29.340, 153.000),
('2469', 'Clearfield', 'NSW', -29.340, 153.000),
('2469', 'Coongbar', 'NSW', -29.340, 153.000),
('2469', 'Culmaran Creek', 'NSW', -29.340, 153.000),
('2469', 'Deep Creek', 'NSW', -29.340, 153.000),
('2469', 'Drake', 'NSW', -29.340, 153.000),
('2469', 'Drake Village', 'NSW', -29.340, 153.000),
('2469', 'Duck Creek', 'NSW', -29.340, 153.000),
('2469', 'Ewingar', 'NSW', -29.340, 153.000),
('2469', 'Gibberagee', 'NSW', -29.340, 153.000),
('2469', 'Goodwood Island', 'NSW', -29.340, 153.000),
('2469', 'Gorge Creek', 'NSW', -29.340, 153.000),
('2469', 'Haystack', 'NSW', -29.340, 153.000),
('2469', 'Hogarth Range', 'NSW', -29.340, 153.000),
('2469', 'Jacksons Flat', 'NSW', -29.340, 153.000),
('2469', 'Joes Box', 'NSW', -29.340, 153.000),
('2469', 'Keybarbin', 'NSW', -29.340, 153.000),
('2469', 'Kippenduff', 'NSW', -29.340, 153.000),
('2469', 'Louisa Creek', 'NSW', -29.340, 153.000),
('2469', 'Lower Bottle Creek', 'NSW', -29.340, 153.000),
('2469', 'Lower Duck Creek', 'NSW', -29.340, 153.000),
('2469', 'Lower Peacock', 'NSW', -29.340, 153.000),
('2469', 'Mallanganee', 'NSW', -29.340, 153.000),
('2469', 'Mookima Wybra', 'NSW', -29.340, 153.000),
('2469', 'Mororo', 'NSW', -29.340, 153.000),
('2469', 'Mount Marsh', 'NSW', -29.340, 153.000),
('2469', 'Mummulgum', 'NSW', -29.340, 153.000),
('2469', 'Myrtle Creek', 'NSW', -29.340, 153.000),
('2469', 'Old Bonalbo', 'NSW', -29.340, 153.000),
('2469', 'Paddys Flat', 'NSW', -29.340, 153.000),
('2469', 'Pagans Flat', 'NSW', -29.340, 153.000),
('2469', 'Peacock Creek', 'NSW', -29.340, 153.000),
('2469', 'Pikapene', 'NSW', -29.340, 153.000),
('2469', 'Rappville', 'NSW', -29.340, 153.000),
('2469', 'Sandilands', 'NSW', -29.340, 153.000),
('2469', 'Simpkins Creek', 'NSW', -29.340, 153.000),
('2469', 'Six Mile Swamp', 'NSW', -29.340, 153.000),
('2469', 'Tabulam', 'NSW', -29.340, 153.000),
('2469', 'Theresa Creek', 'NSW', -29.340, 153.000),
('2469', 'Tunglebung', 'NSW', -29.340, 153.000),
('2469', 'Upper Duck Creek', 'NSW', -29.340, 153.000),
('2469', 'Warregah Island', 'NSW', -29.340, 153.000),
('2469', 'Whiporie', 'NSW', -29.340, 153.000),
('2469', 'Woombah', 'NSW', -29.340, 153.000),
('2469', 'Wyan', 'NSW', -29.340, 153.000),
('2469', 'Yabbra', 'NSW', -29.340, 153.000),
('2470', 'Babyl Creek', 'NSW', -28.730, 152.800),
('2470', 'Backmede', 'NSW', -28.730, 152.800),
('2470', 'Casino', 'NSW', -28.730, 152.800),
('2470', 'Coombell', 'NSW', -28.730, 152.800),
('2470', 'Dobies Bight', 'NSW', -28.730, 152.800),
('2470', 'Doubtful Creek', 'NSW', -28.730, 152.800),
('2470', 'Dyraaba', 'NSW', -28.730, 152.800),
('2470', 'Ellangowan', 'NSW', -28.730, 152.800),
('2470', 'Fairy Hill', 'NSW', -28.730, 152.800),
('2470', 'Irvington', 'NSW', -28.730, 152.800),
('2470', 'Leeville', 'NSW', -28.730, 152.800),
('2470', 'Lower Dyraaba', 'NSW', -28.730, 152.800),
('2470', 'Mongogarie', 'NSW', -28.730, 152.800),
('2470', 'Naughtons Gap', 'NSW', -28.730, 152.800),
('2470', 'North Casino', 'NSW', -28.730, 152.800),
('2470', 'Piora', 'NSW', -28.730, 152.800),
('2470', 'Sextonville', 'NSW', -28.730, 152.800),
('2470', 'Shannon Brook', 'NSW', -28.730, 152.800),
('2470', 'Spring Grove', 'NSW', -28.730, 152.800),
('2470', 'Stratheden', 'NSW', -28.730, 152.800),
('2470', 'Upper Mongogarie', 'NSW', -28.730, 152.800),
('2470', 'Woodview', 'NSW', -28.730, 152.800),
('2470', 'Woolners Arm', 'NSW', -28.730, 152.800),
('2470', 'Yorklea', 'NSW', -28.730, 152.800),
('2471', 'Bora Ridge', 'NSW', -29.050, 153.230),
('2471', 'Codrington', 'NSW', -29.050, 153.230),
('2471', 'Coraki', 'NSW', -29.050, 153.230),
('2471', 'East Coraki', 'NSW', -29.050, 153.230),
('2471', 'Green Forest', 'NSW', -29.050, 153.230),
('2471', 'Greenridge', 'NSW', -29.050, 153.230),
('2471', 'North Woodburn', 'NSW', -29.050, 153.230),
('2471', 'Swan Bay', 'NSW', -29.050, 153.230),
('2471', 'Tatham', 'NSW', -29.050, 153.230),
('2471', 'West Coraki', 'NSW', -29.050, 153.230),
('2472', 'Broadwater', 'NSW', -29.010, 153.440),
('2472', 'Buckendoon', 'NSW', -29.010, 153.440),
('2472', 'Esk', 'NSW', -29.010, 153.440),
('2472', 'Kilgin', 'NSW', -29.010, 153.440),
('2472', 'Moonem', 'NSW', -29.010, 153.440),
('2472', 'New Italy', 'NSW', -29.010, 153.440),
('2472', 'Rileys Hill', 'NSW', -29.010, 153.440),
('2472', 'Tabbimoble', 'NSW', -29.010, 153.440),
('2472', 'The Gap', 'NSW', -29.010, 153.440),
('2472', 'Trustums Hill', 'NSW', -29.010, 153.440),
('2472', 'Woodburn', 'NSW', -29.010, 153.440),
('2473', 'Bundjalung', 'NSW', -29.090, 153.380),
('2473', 'Doonbah', 'NSW', -29.090, 153.380),
('2473', 'Evans Head', 'NSW', -29.090, 153.380),
('2473', 'Iron Gates', 'NSW', -29.090, 153.380),
('2473', 'South Evans Head', 'NSW', -29.090, 153.380),
('2474', 'Afterlee', 'NSW', -28.590, 152.830),
('2474', 'Barkers Vale', 'NSW', -28.590, 152.830),
('2474', 'Border Ranges', 'NSW', -28.590, 152.830),
('2474', 'Cawongla', 'NSW', -28.590, 152.830),
('2474', 'Cedar Point', 'NSW', -28.590, 152.830),
('2474', 'Collins Creek', 'NSW', -28.590, 152.830),
('2474', 'Cougal', 'NSW', -28.590, 152.830),
('2474', 'Dairy Flat', 'NSW', -28.590, 152.830),
('2474', 'Eden Creek', 'NSW', -28.590, 152.830),
('2474', 'Edenville', 'NSW', -28.590, 152.830),
('2474', 'Ettrick', 'NSW', -28.590, 152.830),
('2474', 'Fawcetts Plain', 'NSW', -28.590, 152.830),
('2474', 'Findon Creek', 'NSW', -28.590, 152.830),
('2474', 'Geneva', 'NSW', -28.590, 152.830),
('2474', 'Ghinni Ghi', 'NSW', -28.590, 152.830),
('2474', 'Gradys Creek', 'NSW', -28.590, 152.830),
('2474', 'Green Pigeon', 'NSW', -28.590, 152.830),
('2474', 'Grevillia', 'NSW', -28.590, 152.830),
('2474', 'Homeleigh', 'NSW', -28.590, 152.830),
('2474', 'Horse Station Creek', 'NSW', -28.590, 152.830),
('2474', 'Horseshoe Creek', 'NSW', -28.590, 152.830),
('2474', 'Iron Pot Creek', 'NSW', -28.590, 152.830),
('2474', 'Kilgra', 'NSW', -28.590, 152.830),
('2474', 'Kyogle', 'NSW', -28.590, 152.830),
('2474', 'Little Back Creek', 'NSW', -28.590, 152.830),
('2474', 'Loadstone', 'NSW', -28.590, 152.830),
('2474', 'Lynchs Creek', 'NSW', -28.590, 152.830),
('2474', 'New Park', 'NSW', -28.590, 152.830),
('2474', 'Old Grevillia', 'NSW', -28.590, 152.830),
('2474', 'Roseberry', 'NSW', -28.590, 152.830),
('2474', 'Roseberry Creek', 'NSW', -28.590, 152.830),
('2474', 'Rukenvale', 'NSW', -28.590, 152.830),
('2474', 'Sawpit Creek', 'NSW', -28.590, 152.830),
('2474', 'Sherwood', 'NSW', -28.590, 152.830),
('2474', 'Smiths Creek', 'NSW', -28.590, 152.830),
('2474', 'Terrace Creek', 'NSW', -28.590, 152.830),
('2474', 'The Risk', 'NSW', -28.590, 152.830),
('2474', 'Toonumbar', 'NSW', -28.590, 152.830),
('2474', 'Unumgar', 'NSW', -28.590, 152.830),
('2474', 'Upper Eden Creek', 'NSW', -28.590, 152.830),
('2474', 'Upper Horseshoe Creek', 'NSW', -28.590, 152.830),
('2474', 'Wadeville', 'NSW', -28.590, 152.830),
('2474', 'Warrazambil Creek', 'NSW', -28.590, 152.830),
('2474', 'West Wiangaree', 'NSW', -28.590, 152.830),
('2474', 'Wiangaree', 'NSW', -28.590, 152.830),
('2474', 'Wyneden', 'NSW', -28.590, 152.830),
('2475', 'Tooloom', 'NSW', -28.620, 152.420),
('2475', 'Upper Tooloom', 'NSW', -28.620, 152.420),
('2475', 'Urbenville', 'NSW', -28.620, 152.420),
('2476', 'Boomi Creek', 'NSW', -28.370, 152.320),
('2476', 'Brumby Plains', 'NSW', -28.370, 152.320),
('2476', 'Koreelah', 'NSW', -28.370, 152.320),
('2476', 'Legume', 'NSW', -28.370, 152.320),
('2476', 'Lindesay Creek', 'NSW', -28.370, 152.320),
('2476', 'Muli Muli', 'NSW', -28.370, 152.320),
('2476', 'Old Koreelah', 'NSW', -28.370, 152.320),
('2476', 'The Glen', 'NSW', -28.370, 152.320),
('2476', 'Woodenbong', 'NSW', -28.370, 152.320),
('2477', 'Alstonvale', 'NSW', -28.810, 153.450),
('2477', 'Alstonville', 'NSW', -28.810, 153.450),
('2477', 'Bagotville', 'NSW', -28.810, 153.450),
('2477', 'Cabbage Tree Island', 'NSW', -28.810, 153.450),
('2477', 'Dalwood', 'NSW', -28.810, 153.450),
('2477', 'East Wardell', 'NSW', -28.810, 153.450),
('2477', 'Goat Island', 'NSW', -28.810, 153.450),
('2477', 'Lynwood', 'NSW', -28.810, 153.450),
('2477', 'Meerschaum Vale', 'NSW', -28.810, 153.450),
('2477', 'Pearces Creek', 'NSW', -28.810, 153.450),
('2477', 'Rous', 'NSW', -28.810, 153.450),
('2477', 'Rous Mill', 'NSW', -28.810, 153.450),
('2477', 'Tuckombil', 'NSW', -28.810, 153.450),
('2477', 'Uralba', 'NSW', -28.810, 153.450),
('2477', 'Wardell', 'NSW', -28.810, 153.450),
('2477', 'Wollongbar', 'NSW', -28.810, 153.450),
('2478', 'Ballina', 'NSW', -28.870, 153.560),
('2478', 'Coolgardie', 'NSW', -28.870, 153.560),
('2478', 'Cumbalum', 'NSW', -28.870, 153.560),
('2478', 'East Ballina', 'NSW', -28.870, 153.560),
('2478', 'Empire Vale', 'NSW', -28.870, 153.560),
('2478', 'Keith Hall', 'NSW', -28.870, 153.560),
('2478', 'Lennox Head', 'NSW', -28.870, 153.560),
('2478', 'Patchs Beach', 'NSW', -28.870, 153.560),
('2478', 'Pimlico', 'NSW', -28.870, 153.560),
('2478', 'Pimlico Island', 'NSW', -28.870, 153.560),
('2478', 'Skennars Head', 'NSW', -28.870, 153.560),
('2478', 'South Ballina', 'NSW', -28.870, 153.560),
('2478', 'Teven', 'NSW', -28.870, 153.560),
('2478', 'Tintenbar', 'NSW', -28.870, 153.560),
('2478', 'West Ballina', 'NSW', -28.870, 153.560),
('2479', 'Bangalow', 'NSW', -28.690, 153.520),
('2479', 'Binna Burra', 'NSW', -28.690, 153.520),
('2479', 'Brooklet', 'NSW', -28.690, 153.520),
('2479', 'Coopers Shoot', 'NSW', -28.690, 153.520),
('2479', 'Coorabell', 'NSW', -28.690, 153.520),
('2479', 'Fernleigh', 'NSW', -28.690, 153.520),
('2479', 'Knockrow', 'NSW', -28.690, 153.520),
('2479', 'Mcleods Shoot', 'NSW', -28.690, 153.520),
('2479', 'Nashua', 'NSW', -28.690, 153.520),
('2479', 'Newrybar', 'NSW', -28.690, 153.520),
('2479', 'Possum Creek', 'NSW', -28.690, 153.520),
('2480', 'Bentley', 'NSW', -28.610, 153.040),
('2480', 'Bexhill', 'NSW', -28.610, 153.040),
('2480', 'Blakebrook', 'NSW', -28.610, 153.040),
('2480', 'Blue Knob', 'NSW', -28.610, 153.040),
('2480', 'Boat Harbour', 'NSW', -28.610, 153.040),
('2480', 'Booerie Creek', 'NSW', -28.610, 153.040),
('2480', 'Boorabee Park', 'NSW', -28.610, 153.040),
('2480', 'Booyong', 'NSW', -28.610, 153.040),
('2480', 'Bungabbee', 'NSW', -28.610, 153.040),
('2480', 'Caniaba', 'NSW', -28.610, 153.040),
('2480', 'Chilcotts Grass', 'NSW', -28.610, 153.040),
('2480', 'Clovass', 'NSW', -28.610, 153.040),
('2480', 'Clunes', 'NSW', -28.610, 153.040),
('2480', 'Coffee Camp', 'NSW', -28.610, 153.040),
('2480', 'Corndale', 'NSW', -28.610, 153.040),
('2480', 'Dorroughby', 'NSW', -28.610, 153.040),
('2480', 'Dungarubba', 'NSW', -28.610, 153.040),
('2480', 'Dunoon', 'NSW', -28.610, 153.040),
('2480', 'East Lismore', 'NSW', -28.610, 153.040),
('2480', 'Eltham', 'NSW', -28.610, 153.040),
('2480', 'Eureka', 'NSW', -28.610, 153.040),
('2480', 'Federal', 'NSW', -28.610, 153.040),
('2480', 'Fernside', 'NSW', -28.610, 153.040),
('2480', 'Georgica', 'NSW', -28.610, 153.040),
('2480', 'Girards Hill', 'NSW', -28.610, 153.040),
('2480', 'Goolmangar', 'NSW', -28.610, 153.040),
('2480', 'Goonellabah', 'NSW', -28.610, 153.040),
('2480', 'Howards Grass', 'NSW', -28.610, 153.040),
('2480', 'Jiggi', 'NSW', -28.610, 153.040),
('2480', 'Keerrong', 'NSW', -28.610, 153.040),
('2480', 'Koonorigan', 'NSW', -28.610, 153.040),
('2480', 'Lagoon Grass', 'NSW', -28.610, 153.040),
('2480', 'Larnook', 'NSW', -28.610, 153.040),
('2480', 'Leycester', 'NSW', -28.610, 153.040),
('2480', 'Lillian Rock', 'NSW', -28.610, 153.040),
('2480', 'Lindendale', 'NSW', -28.610, 153.040),
('2480', 'Lismore', 'NSW', -28.610, 153.040),
('2480', 'Lismore Heights', 'NSW', -28.610, 153.040),
('2480', 'Loftville', 'NSW', -28.610, 153.040),
('2480', 'Marom Creek', 'NSW', -28.610, 153.040),
('2480', 'Mckees Hill', 'NSW', -28.610, 153.040),
('2480', 'Mcleans Ridges', 'NSW', -28.610, 153.040),
('2480', 'Modanville', 'NSW', -28.610, 153.040),
('2480', 'Monaltrie', 'NSW', -28.610, 153.040),
('2480', 'Mountain Top', 'NSW', -28.610, 153.040),
('2480', 'Nightcap', 'NSW', -28.610, 153.040),
('2480', 'Nimbin', 'NSW', -28.610, 153.040),
('2480', 'North Lismore', 'NSW', -28.610, 153.040),
('2480', 'Numulgi', 'NSW', -28.610, 153.040),
('2480', 'Repentance Creek', 'NSW', -28.610, 153.040),
('2480', 'Richmond Hill', 'NSW', -28.610, 153.040),
('2480', 'Rock Valley', 'NSW', -28.610, 153.040),
('2480', 'Rosebank', 'NSW', -28.610, 153.040),
('2480', 'Ruthven', 'NSW', -28.610, 153.040),
('2480', 'South Gundurimba', 'NSW', -28.610, 153.040),
('2480', 'South Lismore', 'NSW', -28.610, 153.040),
('2480', 'Stony Chute', 'NSW', -28.610, 153.040),
('2480', 'Terania Creek', 'NSW', -28.610, 153.040),
('2480', 'The Channon', 'NSW', -28.610, 153.040),
('2480', 'Tregeagle', 'NSW', -28.610, 153.040),
('2480', 'Tucki Tucki', 'NSW', -28.610, 153.040),
('2480', 'Tuckurimba', 'NSW', -28.610, 153.040),
('2480', 'Tullera', 'NSW', -28.610, 153.040),
('2480', 'Tuncester', 'NSW', -28.610, 153.040),
('2480', 'Tuntable Creek', 'NSW', -28.610, 153.040),
('2480', 'Whian Whian', 'NSW', -28.610, 153.040),
('2480', 'Woodlawn', 'NSW', -28.610, 153.040),
('2480', 'Wyrallah', 'NSW', -28.610, 153.040),
('2481', 'Broken Head', 'NSW', -28.720, 153.590),
('2481', 'Byron Bay', 'NSW', -28.720, 153.590),
('2481', 'Ewingsdale', 'NSW', -28.720, 153.590),
('2481', 'Hayters Hill', 'NSW', -28.720, 153.590),
('2481', 'Myocum', 'NSW', -28.720, 153.590),
('2481', 'Skinners Shoot', 'NSW', -28.720, 153.590),
('2481', 'Suffolk Park', 'NSW', -28.720, 153.590),
('2481', 'Talofa', 'NSW', -28.720, 153.590),
('2481', 'Tyagarah', 'NSW', -28.720, 153.590),
('2482', 'Goonengerry', 'NSW', -28.610, 153.440),
('2482', 'Huonbrook', 'NSW', -28.610, 153.440),
('2482', 'Koonyum Range', 'NSW', -28.610, 153.440),
('2482', 'Main Arm', 'NSW', -28.610, 153.440),
('2482', 'Montecollum', 'NSW', -28.610, 153.440),
('2482', 'Mullumbimby', 'NSW', -28.610, 153.440),
('2482', 'Mullumbimby Creek', 'NSW', -28.610, 153.440),
('2482', 'Palmwoods', 'NSW', -28.610, 153.440),
('2482', 'Upper Coopers Creek', 'NSW', -28.610, 153.440),
('2482', 'Upper Main Arm', 'NSW', -28.610, 153.440),
('2482', 'Upper Wilsons Creek', 'NSW', -28.610, 153.440),
('2482', 'Wanganui', 'NSW', -28.610, 153.440),
('2482', 'Wilsons Creek', 'NSW', -28.610, 153.440),
('2483', 'Billinudgel', 'NSW', -28.500, 153.530),
('2483', 'Brunswick Heads', 'NSW', -28.500, 153.530),
('2483', 'Burringbar', 'NSW', -28.500, 153.530),
('2483', 'Crabbes Creek', 'NSW', -28.500, 153.530),
('2483', 'Middle Pocket', 'NSW', -28.500, 153.530),
('2483', 'Mooball', 'NSW', -28.500, 153.530),
('2483', 'New Brighton', 'NSW', -28.500, 153.530),
('2483', 'Ocean Shores', 'NSW', -28.500, 153.530),
('2483', 'Sleepy Hollow', 'NSW', -28.500, 153.530),
('2483', 'South Golden Beach', 'NSW', -28.500, 153.530),
('2483', 'The Pocket', 'NSW', -28.500, 153.530),
('2483', 'Upper Burringbar', 'NSW', -28.500, 153.530),
('2483', 'Wooyung', 'NSW', -28.500, 153.530),
('2483', 'Yelgun', 'NSW', -28.500, 153.530),
('2484', 'Back Creek', 'NSW', -28.400, 153.170),
('2484', 'Bray Park', 'NSW', -28.400, 153.170),
('2484', 'Brays Creek', 'NSW', -28.400, 153.170),
('2484', 'Byangum', 'NSW', -28.400, 153.170),
('2484', 'Byrrill Creek', 'NSW', -28.400, 153.170),
('2484', 'Cedar Creek', 'NSW', -28.400, 153.170),
('2484', 'Chillingham', 'NSW', -28.400, 153.170),
('2484', 'Chowan Creek', 'NSW', -28.400, 153.170),
('2484', 'Clothiers Creek', 'NSW', -28.400, 153.170),
('2484', 'Commissioners Creek', 'NSW', -28.400, 153.170),
('2484', 'Condong', 'NSW', -28.400, 153.170),
('2484', 'Crystal Creek', 'NSW', -28.400, 153.170),
('2484', 'Cudgera Creek', 'NSW', -28.400, 153.170),
('2484', 'Doon Doon', 'NSW', -28.400, 153.170),
('2484', 'Dulguigan', 'NSW', -28.400, 153.170),
('2484', 'Dum Dum', 'NSW', -28.400, 153.170),
('2484', 'Dunbible', 'NSW', -28.400, 153.170),
('2484', 'Dungay', 'NSW', -28.400, 153.170),
('2484', 'Eungella', 'NSW', -28.400, 153.170),
('2484', 'Eviron', 'NSW', -28.400, 153.170),
('2484', 'Farrants Hill', 'NSW', -28.400, 153.170),
('2484', 'Fernvale', 'NSW', -28.400, 153.170),
('2484', 'Hopkins Creek', 'NSW', -28.400, 153.170),
('2484', 'Kielvale', 'NSW', -28.400, 153.170),
('2484', 'Kunghur', 'NSW', -28.400, 153.170),
('2484', 'Kunghur Creek', 'NSW', -28.400, 153.170),
('2484', 'Kynnumboon', 'NSW', -28.400, 153.170),
('2484', 'Limpinwood', 'NSW', -28.400, 153.170),
('2484', 'Mebbin', 'NSW', -28.400, 153.170),
('2484', 'Midginbil', 'NSW', -28.400, 153.170),
('2484', 'Mount Burrell', 'NSW', -28.400, 153.170),
('2484', 'Mount Warning', 'NSW', -28.400, 153.170),
('2484', 'Murwillumbah', 'NSW', -28.400, 153.170),
('2484', 'Murwillumbah South', 'NSW', -28.400, 153.170),
('2484', 'Nobbys Creek', 'NSW', -28.400, 153.170),
('2484', 'North Arm', 'NSW', -28.400, 153.170),
('2484', 'Numinbah', 'NSW', -28.400, 153.170),
('2484', 'Nunderi', 'NSW', -28.400, 153.170),
('2484', 'Palmvale', 'NSW', -28.400, 153.170),
('2484', 'Pumpenbil', 'NSW', -28.400, 153.170),
('2484', 'Reserve Creek', 'NSW', -28.400, 153.170),
('2484', 'Round Mountain', 'NSW', -28.400, 153.170),
('2484', 'Rowlands Creek', 'NSW', -28.400, 153.170),
('2484', 'Smiths Creek', 'NSW', -28.400, 153.170),
('2484', 'South Murwillumbah', 'NSW', -28.400, 153.170),
('2484', 'Stokers Siding', 'NSW', -28.400, 153.170),
('2484', 'Terragon', 'NSW', -28.400, 153.170),
('2484', 'Tomewin', 'NSW', -28.400, 153.170),
('2484', 'Tyalgum', 'NSW', -28.400, 153.170),
('2484', 'Tyalgum Creek', 'NSW', -28.400, 153.170),
('2484', 'Tygalgah', 'NSW', -28.400, 153.170),
('2484', 'Uki', 'NSW', -28.400, 153.170),
('2484', 'Upper Crystal Creek', 'NSW', -28.400, 153.170),
('2484', 'Urliup', 'NSW', -28.400, 153.170),
('2484', 'Wardrop Valley', 'NSW', -28.400, 153.170),
('2484', 'Zara', 'NSW', -28.400, 153.170),
('2485', 'Tweed Heads', 'NSW', -28.180, 153.540),
('2485', 'Tweed Heads West', 'NSW', -28.180, 153.540),
('2486', 'Banora Point', 'NSW', -28.210, 153.540),
('2486', 'Bilambil', 'NSW', -28.210, 153.540),
('2486', 'Bilambil Heights', 'NSW', -28.210, 153.540),
('2486', 'Bungalora', 'NSW', -28.210, 153.540),
('2486', 'Carool', 'NSW', -28.210, 153.540),
('2486', 'Cobaki', 'NSW', -28.210, 153.540),
('2486', 'Cobaki Lakes', 'NSW', -28.210, 153.540),
('2486', 'Duroby', 'NSW', -28.210, 153.540),
('2486', 'Glengarrie', 'NSW', -28.210, 153.540),
('2486', 'Piggabeen', 'NSW', -28.210, 153.540),
('2486', 'Terranora', 'NSW', -28.210, 153.540),
('2486', 'Tweed Heads South', 'NSW', -28.210, 153.540),
('2486', 'Upper Duroby', 'NSW', -28.210, 153.540),
('2487', 'Casuarina', 'NSW', -28.300, 153.570),
('2487', 'Chinderah', 'NSW', -28.300, 153.570),
('2487', 'Cudgen', 'NSW', -28.300, 153.570),
('2487', 'Duranbah', 'NSW', -28.300, 153.570),
('2487', 'Fingal Head', 'NSW', -28.300, 153.570),
('2487', 'Kings Forest', 'NSW', -28.300, 153.570),
('2487', 'Kingscliff', 'NSW', -28.300, 153.570),
('2487', 'Stotts Creek', 'NSW', -28.300, 153.570),
('2488', 'Bogangar', 'NSW', -28.330, 153.540),
('2488', 'Cabarita Beach', 'NSW', -28.330, 153.540),
('2488', 'Tanglewood', 'NSW', -28.330, 153.540),
('2489', 'Hastings Point', 'NSW', -28.360, 153.580),
('2489', 'Pottsville', 'NSW', -28.360, 153.580),
('2489', 'Pottsville Beach', 'NSW', -28.360, 153.580),
('2490', 'North Tumbulgum', 'NSW', -28.270, 153.470),
('2490', 'Tumbulgum', 'NSW', -28.270, 153.470),
('2500', 'Coniston', 'NSW', -34.440, 150.890),
('2500', 'Gwynneville', 'NSW', -34.440, 150.890),
('2500', 'Keiraville', 'NSW', -34.440, 150.890),
('2500', 'Mangerton', 'NSW', -34.440, 150.890),
('2500', 'Mount Keira', 'NSW', -34.440, 150.890),
('2500', 'Mount Saint Thomas', 'NSW', -34.440, 150.890),
('2500', 'North Wollongong', 'NSW', -34.440, 150.890),
('2500', 'Spring Hill', 'NSW', -34.440, 150.890),
('2500', 'West Wollongong', 'NSW', -34.440, 150.890),
('2500', 'Wollongong', 'NSW', -34.440, 150.890),
('2500', 'Wollongong DC', 'NSW', -34.440, 150.890),
('2500', 'Wollongong West', 'NSW', -34.440, 150.890),
('2502', 'Cringila', 'NSW', -34.470, 150.870),
('2502', 'Lake Heights', 'NSW', -34.470, 150.870),
('2502', 'Primbee', 'NSW', -34.470, 150.870),
('2502', 'Warrawong', 'NSW', -34.470, 150.870),
('2505', 'Port Kembla', 'NSW', -34.490, 150.890),
('2506', 'Berkeley', 'NSW', -34.480, 150.840),
('2508', 'Coalcliff', 'NSW', -34.240, 150.980),
('2508', 'Darkes Forest', 'NSW', -34.240, 150.980),
('2508', 'Helensburgh', 'NSW', -34.240, 150.980),
('2508', 'Lilyvale', 'NSW', -34.240, 150.980),
('2508', 'Maddens Plains', 'NSW', -34.240, 150.980),
('2508', 'Otford', 'NSW', -34.240, 150.980),
('2508', 'Stanwell Park', 'NSW', -34.240, 150.980),
('2508', 'Stanwell Tops', 'NSW', -34.240, 150.980),
('2508', 'Woronora Dam', 'NSW', -34.240, 150.980),
('2515', 'Austinmer', 'NSW', -34.310, 150.930),
('2515', 'Clifton', 'NSW', -34.310, 150.930),
('2515', 'Coledale', 'NSW', -34.310, 150.930),
('2515', 'Scarborough', 'NSW', -34.310, 150.930),
('2515', 'Thirroul', 'NSW', -34.310, 150.930),
('2515', 'Wombarra', 'NSW', -34.310, 150.930),
('2516', 'Bulli', 'NSW', -34.330, 150.910),
('2517', 'Russell Vale', 'NSW', -34.360, 150.900),
('2517', 'Woonona', 'NSW', -34.360, 150.900),
('2517', 'Woonona East', 'NSW', -34.360, 150.900),
('2518', 'Bellambi', 'NSW', -34.370, 150.910),
('2518', 'Corrimal', 'NSW', -34.370, 150.910),
('2518', 'Corrimal East', 'NSW', -34.370, 150.910),
('2518', 'East Corrimal', 'NSW', -34.370, 150.910),
('2518', 'Tarrawanna', 'NSW', -34.370, 150.910),
('2518', 'Towradgi', 'NSW', -34.370, 150.910),
('2519', 'Balgownie', 'NSW', -34.390, 150.880),
('2519', 'Fairy Meadow', 'NSW', -34.390, 150.880),
('2519', 'Fernhill', 'NSW', -34.390, 150.880),
('2519', 'Mount Ousley', 'NSW', -34.390, 150.880),
('2519', 'Mount Pleasant', 'NSW', -34.390, 150.880),
('2520', 'Wollongong', 'NSW', -33.940, 151.140),
('2522', 'University Of Wollongong', 'NSW', -34.410, 150.880),
('2525', 'Figtree', 'NSW', -34.440, 150.860),
('2526', 'Cordeaux', 'NSW', -34.380, 150.780),
('2526', 'Cordeaux Heights', 'NSW', -34.380, 150.780),
('2526', 'Dombarton', 'NSW', -34.380, 150.780),
('2526', 'Farmborough Heights', 'NSW', -34.380, 150.780),
('2526', 'Kembla Grange', 'NSW', -34.380, 150.780),
('2526', 'Kembla Heights', 'NSW', -34.380, 150.780),
('2526', 'Mount Kembla', 'NSW', -34.380, 150.780),
('2526', 'Unanderra', 'NSW', -34.380, 150.780),
('2526', 'Unanderra DC', 'NSW', -34.380, 150.780),
('2527', 'Albion Park', 'NSW', -34.570, 150.780),
('2527', 'Albion Park Rail', 'NSW', -34.570, 150.780),
('2527', 'Calderwood', 'NSW', -34.570, 150.780),
('2527', 'Croom', 'NSW', -34.570, 150.780),
('2527', 'North Macquarie', 'NSW', -34.570, 150.780),
('2527', 'Tongarra', 'NSW', -34.570, 150.780),
('2527', 'Tullimbar', 'NSW', -34.570, 150.780),
('2527', 'Yellow Rock', 'NSW', -34.570, 150.780),
('2528', 'Barrack Heights', 'NSW', -34.570, 150.860),
('2528', 'Barrack Point', 'NSW', -34.570, 150.860),
('2528', 'Lake Illawarra', 'NSW', -34.570, 150.860),
('2528', 'Mount Warrigal', 'NSW', -34.570, 150.860),
('2528', 'Warilla', 'NSW', -34.570, 150.860),
('2528', 'Windang', 'NSW', -34.570, 150.860),
('2529', 'Blackbutt', 'NSW', -34.570, 150.830),
('2529', 'Dunmore', 'NSW', -34.570, 150.830),
('2529', 'Flinders', 'NSW', -34.570, 150.830),
('2529', 'Oak Flats', 'NSW', -34.570, 150.830),
('2529', 'Oak Flats DC', 'NSW', -34.570, 150.830),
('2529', 'Shell Cove', 'NSW', -34.570, 150.830),
('2529', 'Shellharbour', 'NSW', -34.570, 150.830),
('2529', 'Shellharbour City Centre', 'NSW', -34.570, 150.830),
('2530', 'Avondale', 'NSW', -34.510, 150.730),
('2530', 'Brownsville', 'NSW', -34.510, 150.730),
('2530', 'Cleveland', 'NSW', -34.510, 150.730),
('2530', 'Dapto', 'NSW', -34.510, 150.730),
('2530', 'Haywards Bay', 'NSW', -34.510, 150.730),
('2530', 'Horsley', 'NSW', -34.510, 150.730),
('2530', 'Huntley', 'NSW', -34.510, 150.730),
('2530', 'Kanahooka', 'NSW', -34.510, 150.730),
('2530', 'Koonawarra', 'NSW', -34.510, 150.730),
('2530', 'Marshall Mount', 'NSW', -34.510, 150.730),
('2530', 'Penrose', 'NSW', -34.510, 150.730),
('2530', 'Wongawilli', 'NSW', -34.510, 150.730),
('2530', 'Yallah', 'NSW', -34.510, 150.730),
('2533', 'Bombo', 'NSW', -34.660, 150.850),
('2533', 'Curramore', 'NSW', -34.660, 150.850),
('2533', 'Jamberoo', 'NSW', -34.660, 150.850),
('2533', 'Jerrara', 'NSW', -34.660, 150.850),
('2533', 'Kiama', 'NSW', -34.660, 150.850),
('2533', 'Kiama Downs', 'NSW', -34.660, 150.850),
('2533', 'Kiama Heights', 'NSW', -34.660, 150.850),
('2533', 'Minnamurra', 'NSW', -34.660, 150.850),
('2533', 'Saddleback Mountain', 'NSW', -34.660, 150.850),
('2534', 'Broughton Village', 'NSW', -34.730, 150.770),
('2534', 'Foxground', 'NSW', -34.730, 150.770),
('2534', 'Gerringong', 'NSW', -34.730, 150.770),
('2534', 'Gerroa', 'NSW', -34.730, 150.770),
('2534', 'Rose Valley', 'NSW', -34.730, 150.770),
('2534', 'Toolijooa', 'NSW', -34.730, 150.770),
('2534', 'Werri Beach', 'NSW', -34.730, 150.770),
('2534', 'Willow Vale', 'NSW', -34.730, 150.770),
('2535', 'Back Forest', 'NSW', -34.850, 150.680),
('2535', 'Bellawongarah', 'NSW', -34.850, 150.680),
('2535', 'Berry', 'NSW', -34.850, 150.680),
('2535', 'Berry Mountain', 'NSW', -34.850, 150.680),
('2535', 'Brogers Creek', 'NSW', -34.850, 150.680),
('2535', 'Broughton', 'NSW', -34.850, 150.680),
('2535', 'Broughton Vale', 'NSW', -34.850, 150.680),
('2535', 'Budderoo', 'NSW', -34.850, 150.680),
('2535', 'Bundewallah', 'NSW', -34.850, 150.680),
('2535', 'Coolangatta', 'NSW', -34.850, 150.680),
('2535', 'Far Meadow', 'NSW', -34.850, 150.680),
('2535', 'Jaspers Brush', 'NSW', -34.850, 150.680),
('2535', 'Shoalhaven Heads', 'NSW', -34.850, 150.680),
('2535', 'Wattamolla', 'NSW', -34.850, 150.680),
('2535', 'Woodhill', 'NSW', -34.850, 150.680),
('2536', 'Batehaven', 'NSW', -35.730, 150.200),
('2536', 'Batemans Bay', 'NSW', -35.730, 150.200),
('2536', 'Benandarah', 'NSW', -35.730, 150.200),
('2536', 'Bimbimbie', 'NSW', -35.730, 150.200),
('2536', 'Buckenbowra', 'NSW', -35.730, 150.200),
('2536', 'Catalina', 'NSW', -35.730, 150.200),
('2536', 'Currowan', 'NSW', -35.730, 150.200),
('2536', 'Denhams Beach', 'NSW', -35.730, 150.200),
('2536', 'Depot Beach', 'NSW', -35.730, 150.200),
('2536', 'Durras North', 'NSW', -35.730, 150.200),
('2536', 'East Lynne', 'NSW', -35.730, 150.200),
('2536', 'Guerilla Bay', 'NSW', -35.730, 150.200),
('2536', 'Jeremadra', 'NSW', -35.730, 150.200),
('2536', 'Lilli Pilli', 'NSW', -35.730, 150.200),
('2536', 'Long Beach', 'NSW', -35.730, 150.200),
('2536', 'Maloneys Beach', 'NSW', -35.730, 150.200),
('2536', 'Malua Bay', 'NSW', -35.730, 150.200),
('2536', 'Mogo', 'NSW', -35.730, 150.200),
('2536', 'Nelligen', 'NSW', -35.730, 150.200),
('2536', 'North Batemans Bay', 'NSW', -35.730, 150.200),
('2536', 'Pebbly Beach', 'NSW', -35.730, 150.200),
('2536', 'Rosedale', 'NSW', -35.730, 150.200),
('2536', 'Runnyford', 'NSW', -35.730, 150.200),
('2536', 'South Durras', 'NSW', -35.730, 150.200),
('2536', 'Sunshine Bay', 'NSW', -35.730, 150.200),
('2536', 'Surf Beach', 'NSW', -35.730, 150.200),
('2536', 'Surfside', 'NSW', -35.730, 150.200),
('2536', 'Woodlands', 'NSW', -35.730, 150.200),
('2537', 'Bergalia', 'NSW', -35.980, 150.110),
('2537', 'Bingie', 'NSW', -35.980, 150.110),
('2537', 'Broulee', 'NSW', -35.980, 150.110),
('2537', 'Coila', 'NSW', -35.980, 150.110),
('2537', 'Congo', 'NSW', -35.980, 150.110),
('2537', 'Deua', 'NSW', -35.980, 150.110),
('2537', 'Deua River Valley', 'NSW', -35.980, 150.110),
('2537', 'Kiora', 'NSW', -35.980, 150.110),
('2537', 'Meringo', 'NSW', -35.980, 150.110),
('2537', 'Mogendoura', 'NSW', -35.980, 150.110),
('2537', 'Moruya', 'NSW', -35.980, 150.110),
('2537', 'Moruya Heads', 'NSW', -35.980, 150.110),
('2537', 'Mossy Point', 'NSW', -35.980, 150.110),
('2537', 'Tomakin', 'NSW', -35.980, 150.110),
('2537', 'Turlinjah', 'NSW', -35.980, 150.110),
('2537', 'Tuross Head', 'NSW', -35.980, 150.110),
('2537', 'Wamban', 'NSW', -35.980, 150.110),
('2538', 'Brooman', 'NSW', -35.490, 150.230),
('2538', 'Little Forest', 'NSW', -35.490, 150.230),
('2538', 'Milton', 'NSW', -35.490, 150.230),
('2538', 'Mogood', 'NSW', -35.490, 150.230),
('2538', 'Morton', 'NSW', -35.490, 150.230),
('2538', 'Porters Creek', 'NSW', -35.490, 150.230),
('2538', 'Woodburn', 'NSW', -35.490, 150.230),
('2538', 'Woodstock', 'NSW', -35.490, 150.230),
('2539', 'Bawley Point', 'NSW', -35.520, 150.390),
('2539', 'Bendalong', 'NSW', -35.520, 150.390),
('2539', 'Berringer Lake', 'NSW', -35.520, 150.390),
('2539', 'Burrill Lake', 'NSW', -35.520, 150.390),
('2539', 'Cockwhy', 'NSW', -35.520, 150.390),
('2539', 'Conjola', 'NSW', -35.520, 150.390),
('2539', 'Conjola Park', 'NSW', -35.520, 150.390),
('2539', 'Croobyar', 'NSW', -35.520, 150.390),
('2539', 'Cunjurong Point', 'NSW', -35.520, 150.390),
('2539', 'Dolphin Point', 'NSW', -35.520, 150.390),
('2539', 'Fishermans Paradise', 'NSW', -35.520, 150.390),
('2539', 'Kings Point', 'NSW', -35.520, 150.390),
('2539', 'Kioloa', 'NSW', -35.520, 150.390),
('2539', 'Lake Conjola', 'NSW', -35.520, 150.390),
('2539', 'Lake Tabourie', 'NSW', -35.520, 150.390),
('2539', 'Manyana', 'NSW', -35.520, 150.390),
('2539', 'Mollymook', 'NSW', -35.520, 150.390),
('2539', 'Mollymook Beach', 'NSW', -35.520, 150.390),
('2539', 'Mount Kingiman', 'NSW', -35.520, 150.390),
('2539', 'Narrawallee', 'NSW', -35.520, 150.390),
('2539', 'Pointer Mountain', 'NSW', -35.520, 150.390),
('2539', 'Pretty Beach', 'NSW', -35.520, 150.390),
('2539', 'Termeil', 'NSW', -35.520, 150.390),
('2539', 'Ulladulla', 'NSW', -35.520, 150.390),
('2539', 'Yadboro', 'NSW', -35.520, 150.390),
('2539', 'Yatte Yattah', 'NSW', -35.520, 150.390),
('2540', 'Bamarang', 'NSW', -34.890, 150.530),
('2540', 'Barringella', 'NSW', -34.890, 150.530),
('2540', 'Basin View', 'NSW', -34.890, 150.530),
('2540', 'Beecroft Peninsula', 'NSW', -34.890, 150.530),
('2540', 'Berrara', 'NSW', -34.890, 150.530),
('2540', 'Bewong', 'NSW', -34.890, 150.530),
('2540', 'Bolong', 'NSW', -34.890, 150.530),
('2540', 'Boolijah', 'NSW', -34.890, 150.530),
('2540', 'Bream Beach', 'NSW', -34.890, 150.530),
('2540', 'Browns Mountain', 'NSW', -34.890, 150.530),
('2540', 'Brundee', 'NSW', -34.890, 150.530),
('2540', 'Buangla', 'NSW', -34.890, 150.530),
('2540', 'Burrier', 'NSW', -34.890, 150.530),
('2540', 'Callala Bay', 'NSW', -34.890, 150.530),
('2540', 'Callala Beach', 'NSW', -34.890, 150.530),
('2540', 'Cambewarra', 'NSW', -34.890, 150.530),
('2540', 'Cambewarra Village', 'NSW', -34.890, 150.530),
('2540', 'Comberton', 'NSW', -34.890, 150.530),
('2540', 'Comerong Island', 'NSW', -34.890, 150.530),
('2540', 'Cudmirrah', 'NSW', -34.890, 150.530),
('2540', 'Culburra Beach', 'NSW', -34.890, 150.530),
('2540', 'Currarong', 'NSW', -34.890, 150.530),
('2540', 'Erowal Bay', 'NSW', -34.890, 150.530),
('2540', 'Ettrema', 'NSW', -34.890, 150.530),
('2540', 'Falls Creek', 'NSW', -34.890, 150.530),
('2540', 'Greenwell Point', 'NSW', -34.890, 150.530),
('2540', 'Hmas Albatross', 'NSW', -34.890, 150.530),
('2540', 'Hmas Creswell', 'ACT', -34.890, 150.530),
('2540', 'Huskisson', 'NSW', -34.890, 150.530),
('2540', 'Hyams Beach', 'NSW', -34.890, 150.530),
('2540', 'Illaroo', 'NSW', -34.890, 150.530),
('2540', 'Jerrawangala', 'NSW', -34.890, 150.530),
('2540', 'Jervis Bay', 'ACT', -34.890, 150.530),
('2540', 'Kinghorne', 'NSW', -34.890, 150.530),
('2540', 'Longreach', 'NSW', -34.890, 150.530),
('2540', 'Mayfield', 'NSW', -34.890, 150.530),
('2540', 'Meroo Meadow', 'NSW', -34.890, 150.530),
('2540', 'Mondayong', 'NSW', -34.890, 150.530),
('2540', 'Moollattoo', 'NSW', -34.890, 150.530),
('2540', 'Mundamia', 'NSW', -34.890, 150.530),
('2540', 'Myola', 'NSW', -34.890, 150.530),
('2540', 'Nowra Hill', 'NSW', -34.890, 150.530),
('2540', 'Nowra Naval Po', 'NSW', -34.890, 150.530),
('2540', 'Numbaa', 'NSW', -34.890, 150.530),
('2540', 'Old Erowal Bay', 'NSW', -34.890, 150.530),
('2540', 'Orient Point', 'NSW', -34.890, 150.530),
('2540', 'Parma', 'NSW', -34.890, 150.530),
('2540', 'Pyree', 'NSW', -34.890, 150.530),
('2540', 'Sanctuary Point', 'NSW', -34.890, 150.530),
('2540', 'St Georges Basin', 'NSW', -34.890, 150.530),
('2540', 'Sussex Inlet', 'NSW', -34.890, 150.530),
('2540', 'Swanhaven', 'NSW', -34.890, 150.530),
('2540', 'Tallowal', 'NSW', -34.890, 150.530),
('2540', 'Tapitallee', 'NSW', -34.890, 150.530),
('2540', 'Terara', 'NSW', -34.890, 150.530),
('2540', 'Tomerong', 'NSW', -34.890, 150.530),
('2540', 'Tullarwalla', 'NSW', -34.890, 150.530),
('2540', 'Twelve Mile Peg', 'NSW', -34.890, 150.530),
('2540', 'Vincentia', 'NSW', -34.890, 150.530),
('2540', 'Wandandian', 'NSW', -34.890, 150.530),
('2540', 'Watersleigh', 'NSW', -34.890, 150.530),
('2540', 'Wollumboola', 'NSW', -34.890, 150.530),
('2540', 'Woollamia', 'NSW', -34.890, 150.530),
('2540', 'Worrigee', 'NSW', -34.890, 150.530),
('2540', 'Worrowing Heights', 'NSW', -34.890, 150.530),
('2540', 'Wrights Beach', 'NSW', -34.890, 150.530),
('2540', 'Yalwal', 'NSW', -34.890, 150.530),
('2540', 'Yerriyong', 'NSW', -34.890, 150.530),
('2541', 'Bangalee', 'NSW', -34.840, 150.570),
('2541', 'Bomaderry', 'NSW', -34.840, 150.570),
('2541', 'North Nowra', 'NSW', -34.840, 150.570),
('2541', 'Nowra', 'NSW', -34.840, 150.570),
('2541', 'Nowra DC', 'NSW', -34.840, 150.570),
('2541', 'Nowra East', 'NSW', -34.840, 150.570),
('2541', 'Nowra North', 'NSW', -34.840, 150.570),
('2541', 'South Nowra', 'NSW', -34.840, 150.570),
('2541', 'West Nowra', 'NSW', -34.840, 150.570),
('2545', 'Belowra', 'NSW', -36.150, 149.710),
('2545', 'Bodalla', 'NSW', -36.150, 149.710),
('2545', 'Cadgee', 'NSW', -36.150, 149.710),
('2545', 'Eurobodalla', 'NSW', -36.150, 149.710),
('2545', 'Nerrigundah', 'NSW', -36.150, 149.710),
('2545', 'Potato Point', 'NSW', -36.150, 149.710),
('2546', 'Akolele', 'NSW', -36.360, 150.080),
('2546', 'Barragga Bay', 'NSW', -36.360, 150.080),
('2546', 'Bermagui', 'NSW', -36.360, 150.080),
('2546', 'Central Tilba', 'NSW', -36.360, 150.080),
('2546', 'Corunna', 'NSW', -36.360, 150.080),
('2546', 'Cuttagee', 'NSW', -36.360, 150.080),
('2546', 'Dalmeny', 'NSW', -36.360, 150.080),
('2546', 'Dignams Creek', 'NSW', -36.360, 150.080),
('2546', 'Kianga', 'NSW', -36.360, 150.080),
('2546', 'Murrah', 'NSW', -36.360, 150.080),
('2546', 'Mystery Bay', 'NSW', -36.360, 150.080),
('2546', 'Narooma', 'NSW', -36.360, 150.080),
('2546', 'North Narooma', 'NSW', -36.360, 150.080),
('2546', 'Tilba Tilba', 'NSW', -36.360, 150.080),
('2546', 'Tinpot', 'NSW', -36.360, 150.080),
('2546', 'Wadbilliga', 'NSW', -36.360, 150.080),
('2546', 'Wallaga Lake', 'NSW', -36.360, 150.080),
('2548', 'Berrambool', 'NSW', -36.880, 149.920),
('2548', 'Bournda', 'NSW', -36.880, 149.920),
('2548', 'Merimbula', 'NSW', -36.880, 149.920),
('2548', 'Mirador', 'NSW', -36.880, 149.920),
('2548', 'Tura Beach', 'NSW', -36.880, 149.920),
('2548', 'Yellow Pinch', 'NSW', -36.880, 149.920),
('2549', 'Bald Hills', 'NSW', -36.910, 149.850),
('2549', 'Broadwater', 'NSW', -36.910, 149.850),
('2549', 'Greigs Flat', 'NSW', -36.910, 149.850),
('2549', 'Lochiel', 'NSW', -36.910, 149.850),
('2549', 'Millingandi', 'NSW', -36.910, 149.850),
('2549', 'Nethercote', 'NSW', -36.910, 149.850),
('2549', 'Pambula', 'NSW', -36.910, 149.850),
('2549', 'Pambula Beach', 'NSW', -36.910, 149.850),
('2549', 'South Pambula', 'NSW', -36.910, 149.850),
('2550', 'Angledale', 'NSW', -36.640, 149.860),
('2550', 'Bega', 'NSW', -36.640, 149.860),
('2550', 'Bemboka', 'NSW', -36.640, 149.860),
('2550', 'Black Range', 'NSW', -36.640, 149.860),
('2550', 'Brogo', 'NSW', -36.640, 149.860),
('2550', 'Buckajo', 'NSW', -36.640, 149.860),
('2550', 'Burragate', 'NSW', -36.640, 149.860),
('2550', 'Candelo', 'NSW', -36.640, 149.860),
('2550', 'Chinnock', 'NSW', -36.640, 149.860),
('2550', 'Cobargo', 'NSW', -36.640, 149.860),
('2550', 'Coolagolite', 'NSW', -36.640, 149.860),
('2550', 'Coolangubra', 'NSW', -36.640, 149.860),
('2550', 'Coopers Gully', 'NSW', -36.640, 149.860),
('2550', 'Devils Hole', 'NSW', -36.640, 149.860),
('2550', 'Doctor George Mountain', 'NSW', -36.640, 149.860),
('2550', 'Frogs Hollow', 'NSW', -36.640, 149.860),
('2550', 'Greendale', 'NSW', -36.640, 149.860),
('2550', 'Jellat Jellat', 'NSW', -36.640, 149.860),
('2550', 'Kalaru', 'NSW', -36.640, 149.860),
('2550', 'Kameruka', 'NSW', -36.640, 149.860),
('2550', 'Kanoona', 'NSW', -36.640, 149.860),
('2550', 'Kingswood', 'NSW', -36.640, 149.860),
('2550', 'Mogareeka', 'NSW', -36.640, 149.860),
('2550', 'Mogilla', 'NSW', -36.640, 149.860),
('2550', 'Morans Crossing', 'NSW', -36.640, 149.860),
('2550', 'Mumbulla Mountain', 'NSW', -36.640, 149.860),
('2550', 'Myrtle Mountain', 'NSW', -36.640, 149.860),
('2550', 'Nelson', 'NSW', -36.640, 149.860),
('2550', 'New Buildings', 'NSW', -36.640, 149.860),
('2550', 'Numbugga', 'NSW', -36.640, 149.860),
('2550', 'Pericoe', 'NSW', -36.640, 149.860),
('2550', 'Quaama', 'NSW', -36.640, 149.860),
('2550', 'Reedy Swamp', 'NSW', -36.640, 149.860),
('2550', 'Rocky Hall', 'NSW', -36.640, 149.860),
('2550', 'South Wolumla', 'NSW', -36.640, 149.860),
('2550', 'Stony Creek', 'NSW', -36.640, 149.860),
('2550', 'Tanja', 'NSW', -36.640, 149.860),
('2550', 'Tantawangalo', 'NSW', -36.640, 149.860),
('2550', 'Tarraganda', 'NSW', -36.640, 149.860),
('2550', 'Tathra', 'NSW', -36.640, 149.860),
('2550', 'Toothdale', 'NSW', -36.640, 149.860),
('2550', 'Towamba', 'NSW', -36.640, 149.860),
('2550', 'Verona', 'NSW', -36.640, 149.860),
('2550', 'Wallagoot', 'NSW', -36.640, 149.860),
('2550', 'Wandella', 'NSW', -36.640, 149.860),
('2550', 'Wapengo', 'NSW', -36.640, 149.860),
('2550', 'Wog Wog', 'NSW', -36.640, 149.860),
('2550', 'Wolumla', 'NSW', -36.640, 149.860),
('2550', 'Wyndham', 'NSW', -36.640, 149.860),
('2550', 'Yambulla', 'NSW', -36.640, 149.860),
('2550', 'Yankees Creek', 'NSW', -36.640, 149.860),
('2550', 'Yowrie', 'NSW', -36.640, 149.860),
('2551', 'Boydtown', 'NSW', -37.100, 149.880),
('2551', 'Eden', 'NSW', -37.100, 149.880),
('2551', 'Edrom', 'NSW', -37.100, 149.880),
('2551', 'Green Cape', 'NSW', -37.100, 149.880),
('2551', 'Kiah', 'NSW', -37.100, 149.880),
('2551', 'Nadgee', 'NSW', -37.100, 149.880),
('2551', 'Narrabarba', 'NSW', -37.100, 149.880),
('2551', 'Nullica', 'NSW', -37.100, 149.880),
('2551', 'Nungatta', 'NSW', -37.100, 149.880),
('2551', 'Nungatta South', 'NSW', -37.100, 149.880),
('2551', 'Timbillica', 'NSW', -37.100, 149.880),
('2551', 'Wonboyn', 'NSW', -37.100, 149.880),
('2551', 'Wonboyn Lake', 'NSW', -37.100, 149.880),
('2551', 'Wonboyn North', 'NSW', -37.100, 149.880),
('2555', 'Badgerys Creek', 'NSW', -33.880, 150.740),
('2556', 'Bringelly', 'NSW', -33.950, 150.730),
('2557', 'Catherine Field', 'NSW', -33.990, 150.770),
('2557', 'Gregory Hills', 'NSW', -33.990, 150.770),
('2557', 'Rossmore', 'NSW', -33.990, 150.770),
('2558', 'Eagle Vale', 'NSW', -34.040, 150.810),
('2558', 'Eschol Park', 'NSW', -34.040, 150.810),
('2558', 'Kearns', 'NSW', -34.040, 150.810),
('2559', 'Blairmount', 'NSW', -34.050, 150.800),
('2559', 'Claymore', 'NSW', -34.050, 150.800),
('2560', 'Airds', 'NSW', -34.080, 150.830),
('2560', 'Ambarvale', 'NSW', -34.080, 150.830),
('2560', 'Appin', 'NSW', -34.080, 150.830),
('2560', 'Blair Athol', 'NSW', -34.080, 150.830),
('2560', 'Bradbury', 'NSW', -34.080, 150.830),
('2560', 'Campbelltown', 'NSW', -34.080, 150.830),
('2560', 'Campbelltown North', 'NSW', -34.080, 150.830),
('2560', 'Cataract', 'NSW', -34.080, 150.830),
('2560', 'Englorie Park', 'NSW', -34.080, 150.830),
('2560', 'Gilead', 'NSW', -34.080, 150.830),
('2560', 'Glen Alpine', 'NSW', -34.080, 150.830),
('2560', 'Kentlyn', 'NSW', -34.080, 150.830),
('2560', 'Leumeah', 'NSW', -34.080, 150.830),
('2560', 'Macarthur Square', 'NSW', -34.080, 150.830),
('2560', 'Rosemeadow', 'NSW', -34.080, 150.830),
('2560', 'Ruse', 'NSW', -34.080, 150.830),
('2560', 'St Helens Park', 'NSW', -34.080, 150.830),
('2560', 'Wedderburn', 'NSW', -34.080, 150.830),
('2560', 'Woodbine', 'NSW', -34.080, 150.830),
('2563', 'Menangle Park', 'NSW', -34.100, 150.760),
('2564', 'Glenquarie', 'NSW', -33.990, 150.890),
('2564', 'Long Point', 'NSW', -33.990, 150.890),
('2564', 'Macquarie Fields', 'NSW', -33.990, 150.890),
('2565', 'Bardia', 'NSW', -33.990, 150.840),
('2565', 'Denham Court', 'NSW', -33.990, 150.840),
('2565', 'Ingleburn', 'NSW', -33.990, 150.840),
('2565', 'Macquarie Links', 'NSW', -33.990, 150.840),
('2566', 'Bow Bowing', 'NSW', -34.020, 150.840),
('2566', 'Minto', 'NSW', -34.020, 150.840),
('2566', 'Minto DC', 'NSW', -34.020, 150.840),
('2566', 'Minto Heights', 'NSW', -34.020, 150.840),
('2566', 'Raby', 'NSW', -34.020, 150.840),
('2566', 'St Andrews', 'NSW', -34.020, 150.840),
('2566', 'Varroville', 'NSW', -34.020, 150.840),
('2567', 'Currans Hill', 'NSW', -34.050, 150.760),
('2567', 'Harrington Park', 'NSW', -34.050, 150.760),
('2567', 'Mount Annan', 'NSW', -34.050, 150.760),
('2567', 'Narellan', 'NSW', -34.050, 150.760),
('2567', 'Narellan DC', 'NSW', -34.050, 150.760),
('2567', 'Narellan Vale', 'NSW', -34.050, 150.760),
('2567', 'Smeaton Grange', 'NSW', -34.050, 150.760),
('2568', 'Menangle', 'NSW', -34.110, 150.750),
('2569', 'Douglas Park', 'NSW', -34.190, 150.710),
('2570', 'Belimbla Park', 'NSW', -34.080, 150.540),
('2570', 'Bickley Vale', 'NSW', -34.080, 150.540),
('2570', 'Brownlow Hill', 'NSW', -34.080, 150.540),
('2570', 'Camden', 'NSW', -34.080, 150.540),
('2570', 'Camden Park', 'NSW', -34.080, 150.540),
('2570', 'Camden South', 'NSW', -34.080, 150.540),
('2570', 'Cawdor', 'NSW', -34.080, 150.540),
('2570', 'Cobbitty', 'NSW', -34.080, 150.540),
('2570', 'Elderslie', 'NSW', -34.080, 150.540),
('2570', 'Ellis Lane', 'NSW', -34.080, 150.540),
('2570', 'Glenmore', 'NSW', -34.080, 150.540),
('2570', 'Grasmere', 'NSW', -34.080, 150.540),
('2570', 'Kirkham', 'NSW', -34.080, 150.540),
('2570', 'Mount Hunter', 'NSW', -34.080, 150.540),
('2570', 'Nattai', 'NSW', -34.080, 150.540),
('2570', 'Oakdale', 'NSW', -34.080, 150.540),
('2570', 'Oran Park', 'NSW', -34.080, 150.540),
('2570', 'Orangeville', 'NSW', -34.080, 150.540),
('2570', 'Spring Farm', 'NSW', -34.080, 150.540),
('2570', 'The Oaks', 'NSW', -34.080, 150.540),
('2570', 'Theresa Park', 'NSW', -34.080, 150.540),
('2570', 'Werombi', 'NSW', -34.080, 150.540),
('2571', 'Balmoral', 'NSW', -34.290, 150.530),
('2571', 'Buxton', 'NSW', -34.290, 150.530),
('2571', 'Couridjah', 'NSW', -34.290, 150.530),
('2571', 'Maldon', 'NSW', -34.290, 150.530),
('2571', 'Mowbray Park', 'NSW', -34.290, 150.530),
('2571', 'Picton', 'NSW', -34.290, 150.530),
('2571', 'Razorback', 'NSW', -34.290, 150.530),
('2571', 'Wilton', 'NSW', -34.290, 150.530),
('2572', 'Lakesland', 'NSW', -34.180, 150.530),
('2572', 'Thirlmere', 'NSW', -34.180, 150.530),
('2573', 'Tahmoor', 'NSW', -34.220, 150.590),
('2574', 'Avon', 'NSW', -34.350, 150.630),
('2574', 'Bargo', 'NSW', -34.350, 150.630),
('2574', 'Pheasants Nest', 'NSW', -34.350, 150.630),
('2574', 'Yanderra', 'NSW', -34.350, 150.630),
('2575', 'Alpine', 'NSW', -34.410, 150.520),
('2575', 'Aylmerton', 'NSW', -34.410, 150.520),
('2575', 'Braemar', 'NSW', -34.410, 150.520),
('2575', 'Bullio', 'NSW', -34.410, 150.520),
('2575', 'Colo Vale', 'NSW', -34.410, 150.520),
('2575', 'High Range', 'NSW', -34.410, 150.520),
('2575', 'Hill Top', 'NSW', -34.410, 150.520),
('2575', 'Joadja', 'NSW', -34.410, 150.520),
('2575', 'Mittagong', 'NSW', -34.410, 150.520),
('2575', 'Mount Lindsey', 'NSW', -34.410, 150.520),
('2575', 'Wattle Ridge', 'NSW', -34.410, 150.520),
('2575', 'Welby', 'NSW', -34.410, 150.520),
('2575', 'Willow Vale', 'NSW', -34.410, 150.520),
('2575', 'Woodlands', 'NSW', -34.410, 150.520),
('2575', 'Yerrinbool', 'NSW', -34.410, 150.520),
('2576', 'Bowral', 'NSW', -34.540, 150.390),
('2576', 'Burradoo', 'NSW', -34.540, 150.390),
('2576', 'East Bowral', 'NSW', -34.540, 150.390),
('2576', 'East Kangaloon', 'NSW', -34.540, 150.390),
('2576', 'Glenquarry', 'NSW', -34.540, 150.390),
('2576', 'Kangaloon', 'NSW', -34.540, 150.390),
('2577', 'Avoca', 'NSW', -34.610, 150.480),
('2577', 'Bangadilly', 'NSW', -34.610, 150.480),
('2577', 'Barren Grounds', 'NSW', -34.610, 150.480),
('2577', 'Barrengarry', 'NSW', -34.610, 150.480),
('2577', 'Beaumont', 'NSW', -34.610, 150.480),
('2577', 'Belanglo', 'NSW', -34.610, 150.480),
('2577', 'Berrima', 'NSW', -34.610, 150.480),
('2577', 'Budgong', 'NSW', -34.610, 150.480),
('2577', 'Burrawang', 'NSW', -34.610, 150.480),
('2577', 'Canyonleigh', 'NSW', -34.610, 150.480),
('2577', 'Carrington Falls', 'NSW', -34.610, 150.480),
('2577', 'Fitzroy Falls', 'NSW', -34.610, 150.480),
('2577', 'Hanging Rock', 'NSW', -34.610, 150.480),
('2577', 'Kangaroo Valley', 'NSW', -34.610, 150.480),
('2577', 'Knights Hill', 'NSW', -34.610, 150.480),
('2577', 'Macquarie Pass', 'NSW', -34.610, 150.480),
('2577', 'Manchester Square', 'NSW', -34.610, 150.480),
('2577', 'Medway', 'NSW', -34.610, 150.480),
('2577', 'Meryla', 'NSW', -34.610, 150.480),
('2577', 'Moss Vale', 'NSW', -34.610, 150.480),
('2577', 'Mount Murray', 'NSW', -34.610, 150.480),
('2577', 'New Berrima', 'NSW', -34.610, 150.480),
('2577', 'Paddys River', 'NSW', -34.610, 150.480),
('2577', 'Red Rocks', 'NSW', -34.610, 150.480),
('2577', 'Robertson', 'NSW', -34.610, 150.480),
('2577', 'Sutton Forest', 'NSW', -34.610, 150.480),
('2577', 'Upper Kangaroo River', 'NSW', -34.610, 150.480),
('2577', 'Upper Kangaroo Valley', 'NSW', -34.610, 150.480),
('2577', 'Werai', 'NSW', -34.610, 150.480),
('2577', 'Wildes Meadow', 'NSW', -34.610, 150.480),
('2578', 'Bundanoon', 'NSW', -34.630, 150.320),
('2579', 'Big Hill', 'NSW', -34.560, 149.990),
('2579', 'Brayton', 'NSW', -34.560, 149.990),
('2579', 'Exeter', 'NSW', -34.560, 149.990),
('2579', 'Marulan', 'NSW', -34.560, 149.990),
('2579', 'Penrose', 'NSW', -34.560, 149.990),
('2579', 'Tallong', 'NSW', -34.560, 149.990),
('2579', 'Wingello', 'NSW', -34.560, 149.990),
('2580', 'Bannaby', 'NSW', -34.440, 149.960),
('2580', 'Bannister', 'NSW', -34.440, 149.960),
('2580', 'Baw Baw', 'NSW', -34.440, 149.960),
('2580', 'Boxers Creek', 'NSW', -34.440, 149.960),
('2580', 'Brisbane Grove', 'NSW', -34.440, 149.960),
('2580', 'Bungonia', 'NSW', -34.440, 149.960),
('2580', 'Carrick', 'NSW', -34.440, 149.960),
('2580', 'Chatsbury', 'NSW', -34.440, 149.960),
('2580', 'Currawang', 'NSW', -34.440, 149.960),
('2580', 'Curraweela', 'NSW', -34.440, 149.960),
('2580', 'Golspie', 'NSW', -34.440, 149.960),
('2580', 'Goulburn', 'NSW', -34.440, 149.960),
('2580', 'Goulburn DC', 'NSW', -34.440, 149.960),
('2580', 'Goulburn North', 'NSW', -34.440, 149.960),
('2580', 'Greenwich Park', 'NSW', -34.440, 149.960),
('2580', 'Gundary', 'NSW', -34.440, 149.960),
('2580', 'Jerrong', 'NSW', -34.440, 149.960),
('2580', 'Kingsdale', 'NSW', -34.440, 149.960),
('2580', 'Lake Bathurst', 'NSW', -34.440, 149.960),
('2580', 'Lower Boro', 'NSW', -34.440, 149.960),
('2580', 'Mayfield', 'NSW', -34.440, 149.960),
('2580', 'Middle Arm', 'NSW', -34.440, 149.960),
('2580', 'Mount Fairy', 'NSW', -34.440, 149.960),
('2580', 'Mummel', 'NSW', -34.440, 149.960),
('2580', 'Myrtleville', 'NSW', -34.440, 149.960),
('2580', 'Paling Yards', 'NSW', -34.440, 149.960),
('2580', 'Parkesbourne', 'NSW', -34.440, 149.960),
('2580', 'Pomeroy', 'NSW', -34.440, 149.960),
('2580', 'Quialigo', 'NSW', -34.440, 149.960),
('2580', 'Richlands', 'NSW', -34.440, 149.960),
('2580', 'Roslyn', 'NSW', -34.440, 149.960),
('2580', 'Run-O-Waters', 'NSW', -34.440, 149.960),
('2580', 'Stonequarry', 'NSW', -34.440, 149.960),
('2580', 'Tarago', 'NSW', -34.440, 149.960),
('2580', 'Taralga', 'NSW', -34.440, 149.960),
('2580', 'Tarlo', 'NSW', -34.440, 149.960),
('2580', 'Tirrannaville', 'NSW', -34.440, 149.960),
('2580', 'Towrang', 'NSW', -34.440, 149.960),
('2580', 'Wayo', 'NSW', -34.440, 149.960),
('2580', 'Wiarborough', 'NSW', -34.440, 149.960),
('2580', 'Windellama', 'NSW', -34.440, 149.960),
('2580', 'Wombeyan Caves', 'NSW', -34.440, 149.960),
('2580', 'Woodhouselee', 'NSW', -34.440, 149.960),
('2580', 'Yalbraith', 'NSW', -34.440, 149.960),
('2580', 'Yarra', 'NSW', -34.440, 149.960),
('2581', 'Bellmount Forest', 'NSW', -34.920, 149.240),
('2581', 'Bevendale', 'NSW', -34.920, 149.240),
('2581', 'Biala', 'NSW', -34.920, 149.240),
('2581', 'Blakney Creek', 'NSW', -34.920, 149.240),
('2581', 'Breadalbane', 'NSW', -34.920, 149.240),
('2581', 'Broadway', 'NSW', -34.920, 149.240),
('2581', 'Collector', 'NSW', -34.920, 149.240),
('2581', 'Cullerin', 'NSW', -34.920, 149.240),
('2581', 'Dalton', 'NSW', -34.920, 149.240),
('2581', 'Gunning', 'NSW', -34.920, 149.240),
('2581', 'Gurrundah', 'NSW', -34.920, 149.240),
('2581', 'Lade Vale', 'NSW', -34.920, 149.240),
('2581', 'Lake George', 'NSW', -34.920, 149.240),
('2581', 'Lerida', 'NSW', -34.920, 149.240),
('2581', 'Merrill', 'NSW', -34.920, 149.240),
('2581', 'Oolong', 'NSW', -34.920, 149.240),
('2581', 'Wollogorang', 'NSW', -34.920, 149.240),
('2582', 'Bango', 'NSW', -34.750, 148.960),
('2582', 'Berremangra', 'NSW', -34.750, 148.960),
('2582', 'Boambolo', 'NSW', -34.750, 148.960),
('2582', 'Bookham', 'NSW', -34.750, 148.960),
('2582', 'Bowning', 'NSW', -34.750, 148.960),
('2582', 'Burrinjuck', 'NSW', -34.750, 148.960),
('2582', 'Cavan', 'NSW', -34.750, 148.960),
('2582', 'Good Hope', 'NSW', -34.750, 148.960),
('2582', 'Jeir', 'NSW', -34.750, 148.960),
('2582', 'Jerrawa', 'NSW', -34.750, 148.960),
('2582', 'Kangiara', 'NSW', -34.750, 148.960),
('2582', 'Laverstock', 'NSW', -34.750, 148.960),
('2582', 'Manton', 'NSW', -34.750, 148.960),
('2582', 'Marchmont', 'NSW', -34.750, 148.960),
('2582', 'Mullion', 'NSW', -34.750, 148.960),
('2582', 'Murrumbateman', 'NSW', -34.750, 148.960),
('2582', 'Narrangullen', 'NSW', -34.750, 148.960),
('2582', 'Wee Jasper', 'NSW', -34.750, 148.960),
('2582', 'Woolgarlo', 'NSW', -34.750, 148.960),
('2582', 'Yass', 'NSW', -34.750, 148.960),
('2582', 'Yass River', 'NSW', -34.750, 148.960),
('2583', 'Bigga', 'NSW', -34.080, 149.150),
('2583', 'Binda', 'NSW', -34.080, 149.150),
('2583', 'Crooked Corner', 'NSW', -34.080, 149.150),
('2583', 'Crookwell', 'NSW', -34.080, 149.150),
('2583', 'Fullerton', 'NSW', -34.080, 149.150),
('2583', 'Grabben Gullen', 'NSW', -34.080, 149.150),
('2583', 'Grabine', 'NSW', -34.080, 149.150),
('2583', 'Laggan', 'NSW', -34.080, 149.150),
('2583', 'Limerick', 'NSW', -34.080, 149.150),
('2583', 'Lost River', 'NSW', -34.080, 149.150),
('2583', 'Narrawa', 'NSW', -34.080, 149.150),
('2583', 'Peelwood', 'NSW', -34.080, 149.150),
('2583', 'Pejar', 'NSW', -34.080, 149.150),
('2583', 'Rugby', 'NSW', -34.080, 149.150),
('2583', 'Tuena', 'NSW', -34.080, 149.150),
('2583', 'Wheeo', 'NSW', -34.080, 149.150),
('2584', 'Binalong', 'NSW', -34.670, 148.630),
('2585', 'Galong', 'NSW', -34.600, 148.560),
('2586', 'Boorowa', 'NSW', -34.440, 148.720),
('2586', 'Frogmore', 'NSW', -34.440, 148.720),
('2586', 'Godfreys Creek', 'NSW', -34.440, 148.720),
('2586', 'Murringo', 'NSW', -34.440, 148.720),
('2586', 'Reids Flat', 'NSW', -34.440, 148.720),
('2586', 'Rye Park', 'NSW', -34.440, 148.720),
('2586', 'Taylors Flat', 'NSW', -34.440, 148.720),
('2587', 'Harden', 'NSW', -34.550, 148.370),
('2587', 'Kingsvale', 'NSW', -34.550, 148.370),
('2587', 'Mcmahons Reef', 'NSW', -34.550, 148.370),
('2587', 'Murrumburrah', 'NSW', -34.550, 148.370),
('2587', 'Nubba', 'NSW', -34.550, 148.370),
('2587', 'Wombat', 'NSW', -34.550, 148.370),
('2588', 'Wallendbeen', 'NSW', -34.520, 148.160),
('2590', 'Bethungra', 'NSW', -34.760, 147.850),
('2590', 'Cootamundra', 'NSW', -34.760, 147.850),
('2590', 'Illabo', 'NSW', -34.760, 147.850),
('2594', 'Berthong', 'NSW', -34.430, 148.070),
('2594', 'Bribbaree', 'NSW', -34.430, 148.070),
('2594', 'Bulla Creek', 'NSW', -34.430, 148.070),
('2594', 'Burrangong', 'NSW', -34.430, 148.070),
('2594', 'Kikiamah', 'NSW', -34.430, 148.070),
('2594', 'Maimuru', 'NSW', -34.430, 148.070),
('2594', 'Memagong', 'NSW', -34.430, 148.070),
('2594', 'Milvale', 'NSW', -34.430, 148.070),
('2594', 'Monteagle', 'NSW', -34.430, 148.070),
('2594', 'Thuddungra', 'NSW', -34.430, 148.070),
('2594', 'Tubbul', 'NSW', -34.430, 148.070),
('2594', 'Weedallion', 'NSW', -34.430, 148.070),
('2594', 'Young', 'NSW', -34.430, 148.070),
('2620', 'Burra', 'NSW', -35.580, 149.230),
('2620', 'Carwoola', 'NSW', -35.580, 149.230),
('2620', 'Clear Range', 'NSW', -35.580, 149.230),
('2620', 'Crestwood', 'NSW', -35.580, 149.230),
('2620', 'Environa', 'NSW', -35.580, 149.230),
('2620', 'Googong', 'NSW', -35.580, 149.230),
('2620', 'Greenleigh', 'NSW', -35.580, 149.230),
('2620', 'Gundaroo', 'NSW', -35.580, 149.230),
('2620', 'Karabar', 'NSW', -35.580, 149.230),
('2620', 'Kowen Forest', 'ACT', -35.580, 149.230),
('2620', 'Michelago', 'NSW', -35.580, 149.230),
('2620', 'Oaks Estate', 'ACT', -35.580, 149.230),
('2620', 'Queanbeyan', 'NSW', -35.580, 149.230),
('2620', 'Queanbeyan DC', 'NSW', -35.580, 149.230),
('2620', 'Queanbeyan East', 'NSW', -35.580, 149.230),
('2620', 'Queanbeyan West', 'NSW', -35.580, 149.230),
('2620', 'Royalla', 'NSW', -35.580, 149.230),
('2620', 'Sutton', 'NSW', -35.580, 149.230),
('2620', 'Tharwa', 'ACT', -35.580, 149.230),
('2620', 'The Angle', 'NSW', -35.580, 149.230),
('2620', 'The Ridgeway', 'NSW', -35.580, 149.230),
('2620', 'Tinderry', 'NSW', -35.580, 149.230),
('2620', 'Top Naas', 'ACT', -35.580, 149.230),
('2620', 'Tralee', 'NSW', -35.580, 149.230),
('2620', 'Urila', 'NSW', -35.580, 149.230),
('2620', 'Wamboin', 'NSW', -35.580, 149.230),
('2620', 'Williamsdale', 'NSW', -35.580, 149.230),
('2620', 'Williamsdale', 'ACT', -35.580, 149.230),
('2620', 'Yarrow', 'NSW', -35.580, 149.230),
('2621', 'Anembo', 'NSW', -35.810, 149.430),
('2621', 'Bungendore', 'NSW', -35.810, 149.430),
('2621', 'Bywong', 'NSW', -35.810, 149.430),
('2621', 'Forbes Creek', 'NSW', -35.810, 149.430),
('2621', 'Hoskinstown', 'NSW', -35.810, 149.430),
('2621', 'Primrose Valley', 'NSW', -35.810, 149.430),
('2621', 'Rossi', 'NSW', -35.810, 149.430),
('2622', 'Araluen', 'NSW', -35.650, 149.810),
('2622', 'Back Creek', 'NSW', -35.650, 149.810),
('2622', 'Ballalaba', 'NSW', -35.650, 149.810),
('2622', 'Bendoura', 'NSW', -35.650, 149.810),
('2622', 'Berlang', 'NSW', -35.650, 149.810),
('2622', 'Bombay', 'NSW', -35.650, 149.810),
('2622', 'Boro', 'NSW', -35.650, 149.810),
('2622', 'Braidwood', 'NSW', -35.650, 149.810),
('2622', 'Budawang', 'NSW', -35.650, 149.810),
('2622', 'Bulee', 'NSW', -35.650, 149.810),
('2622', 'Charleys Forest', 'NSW', -35.650, 149.810),
('2622', 'Coolumburra', 'NSW', -35.650, 149.810),
('2622', 'Corang', 'NSW', -35.650, 149.810),
('2622', 'Durran Durra', 'NSW', -35.650, 149.810),
('2622', 'Endrick', 'NSW', -35.650, 149.810),
('2622', 'Farringdon', 'NSW', -35.650, 149.810),
('2622', 'Harolds Cross', 'NSW', -35.650, 149.810),
('2622', 'Hereford Hall', 'NSW', -35.650, 149.810),
('2622', 'Jembaicumbene', 'NSW', -35.650, 149.810),
('2622', 'Jerrabattgulla', 'NSW', -35.650, 149.810),
('2622', 'Jinden', 'NSW', -35.650, 149.810),
('2622', 'Jingera', 'NSW', -35.650, 149.810),
('2622', 'Kindervale', 'NSW', -35.650, 149.810),
('2622', 'Krawarree', 'NSW', -35.650, 149.810),
('2622', 'Larbert', 'NSW', -35.650, 149.810),
('2622', 'Majors Creek', 'NSW', -35.650, 149.810),
('2622', 'Manar', 'NSW', -35.650, 149.810),
('2622', 'Marlowe', 'NSW', -35.650, 149.810),
('2622', 'Merricumbene', 'NSW', -35.650, 149.810),
('2622', 'Monga', 'NSW', -35.650, 149.810),
('2622', 'Mongarlowe', 'NSW', -35.650, 149.810),
('2622', 'Mulloon', 'NSW', -35.650, 149.810),
('2622', 'Murrengenburg', 'NSW', -35.650, 149.810),
('2622', 'Neringla', 'NSW', -35.650, 149.810),
('2622', 'Nerriga', 'NSW', -35.650, 149.810),
('2622', 'Northangera', 'NSW', -35.650, 149.810),
('2622', 'Oallen', 'NSW', -35.650, 149.810),
('2622', 'Palerang', 'NSW', -35.650, 149.810),
('2622', 'Quiera', 'NSW', -35.650, 149.810),
('2622', 'Reidsdale', 'NSW', -35.650, 149.810),
('2622', 'Sassafras', 'NSW', -35.650, 149.810),
('2622', 'Snowball', 'NSW', -35.650, 149.810),
('2622', 'St George', 'NSW', -35.650, 149.810),
('2622', 'Tianjara', 'NSW', -35.650, 149.810),
('2622', 'Tolwong', 'NSW', -35.650, 149.810),
('2622', 'Tomboye', 'NSW', -35.650, 149.810),
('2622', 'Touga', 'NSW', -35.650, 149.810),
('2622', 'Warri', 'NSW', -35.650, 149.810),
('2622', 'Wog Wog', 'NSW', -35.650, 149.810),
('2622', 'Wyanbene', 'NSW', -35.650, 149.810),
('2623', 'Captains Flat', 'NSW', -35.550, 149.450),
('2624', 'Perisher Valley', 'NSW', -36.180, 148.440),
('2625', 'Thredbo', 'NSW', -36.510, 148.300),
('2626', 'Bredbo', 'NSW', -35.960, 149.150),
('2626', 'Bumbalong', 'NSW', -35.960, 149.150),
('2626', 'Colinton', 'NSW', -35.960, 149.150),
('2627', 'Crackenback', 'NSW', -36.440, 148.510),
('2627', 'East Jindabyne', 'NSW', -36.440, 148.510),
('2627', 'Grosses Plain', 'NSW', -36.440, 148.510),
('2627', 'Ingebirah', 'NSW', -36.440, 148.510),
('2627', 'Jindabyne', 'NSW', -36.440, 148.510),
('2627', 'Kalkite', 'NSW', -36.440, 148.510),
('2627', 'Kosciuszko', 'NSW', -36.440, 148.510),
('2627', 'Kosciuszko National Park', 'NSW', -36.440, 148.510),
('2627', 'Moonbah', 'NSW', -36.440, 148.510),
('2627', 'Pilot Wilderness', 'NSW', -36.440, 148.510),
('2628', 'Avonside', 'NSW', -36.440, 148.700),
('2628', 'Beloka', 'NSW', -36.440, 148.700),
('2628', 'Berridale', 'NSW', -36.440, 148.700),
('2628', 'Braemar Bay', 'NSW', -36.440, 148.700),
('2628', 'Byadbo Wilderness', 'NSW', -36.440, 148.700),
('2628', 'Cootralantra', 'NSW', -36.440, 148.700),
('2628', 'Dalgety', 'NSW', -36.440, 148.700),
('2628', 'Eucumbene', 'NSW', -36.440, 148.700),
('2628', 'Hill Top', 'NSW', -36.440, 148.700),
('2628', 'Nimmo', 'NSW', -36.440, 148.700),
('2628', 'Numbla Vale', 'NSW', -36.440, 148.700),
('2628', 'Paupong', 'NSW', -36.440, 148.700),
('2628', 'Rocky Plain', 'NSW', -36.440, 148.700),
('2628', 'Snowy Plain', 'NSW', -36.440, 148.700),
('2629', 'Adaminaby', 'NSW', -36.000, 148.770),
('2629', 'Anglers Reach', 'NSW', -36.000, 148.770),
('2629', 'Bolaro', 'NSW', -36.000, 148.770),
('2629', 'Cabramurra', 'NSW', -36.000, 148.770),
('2629', 'Long Plain', 'NSW', -36.000, 148.770),
('2629', 'Old Adaminaby', 'NSW', -36.000, 148.770),
('2629', 'Providence Portal', 'NSW', -36.000, 148.770),
('2629', 'Tantangara', 'NSW', -36.000, 148.770),
('2629', 'Yaouk', 'NSW', -36.000, 148.770),
('2630', 'Arable', 'NSW', -36.370, 148.950),
('2630', 'Badja', 'NSW', -36.370, 148.950),
('2630', 'Billilingra', 'NSW', -36.370, 148.950),
('2630', 'Binjura', 'NSW', -36.370, 148.950),
('2630', 'Bobundara', 'NSW', -36.370, 148.950),
('2630', 'Buckenderra', 'NSW', -36.370, 148.950),
('2630', 'Bungarby', 'NSW', -36.370, 148.950),
('2630', 'Bunyan', 'NSW', -36.370, 148.950),
('2630', 'Carlaminda', 'NSW', -36.370, 148.950),
('2630', 'Chakola', 'NSW', -36.370, 148.950),
('2630', 'Coolringdon', 'NSW', -36.370, 148.950),
('2630', 'Cooma', 'NSW', -36.370, 148.950),
('2630', 'Cooma North', 'NSW', -36.370, 148.950),
('2630', 'Countegany', 'NSW', -36.370, 148.950),
('2630', 'Dairymans Plains', 'NSW', -36.370, 148.950),
('2630', 'Dangelong', 'NSW', -36.370, 148.950),
('2630', 'Dry Plain', 'NSW', -36.370, 148.950),
('2630', 'Frying Pan', 'NSW', -36.370, 148.950),
('2630', 'Glen Fergus', 'NSW', -36.370, 148.950),
('2630', 'Ironmungy', 'NSW', -36.370, 148.950),
('2630', 'Jerangle', 'NSW', -36.370, 148.950),
('2630', 'Jimenbuen', 'NSW', -36.370, 148.950),
('2630', 'Maffra', 'NSW', -36.370, 148.950),
('2630', 'Middle Flat', 'NSW', -36.370, 148.950),
('2630', 'Middlingbank', 'NSW', -36.370, 148.950),
('2630', 'Murrumbucca', 'NSW', -36.370, 148.950),
('2630', 'Myalla', 'NSW', -36.370, 148.950),
('2630', 'Numeralla', 'NSW', -36.370, 148.950),
('2630', 'Peak View', 'NSW', -36.370, 148.950),
('2630', 'Pine Valley', 'NSW', -36.370, 148.950),
('2630', 'Polo Flat', 'NSW', -36.370, 148.950),
('2630', 'Rhine Falls', 'NSW', -36.370, 148.950),
('2630', 'Rock Flat', 'NSW', -36.370, 148.950),
('2630', 'Rose Valley', 'NSW', -36.370, 148.950),
('2630', 'Shannons Flat', 'NSW', -36.370, 148.950),
('2630', 'Springfield', 'NSW', -36.370, 148.950),
('2630', 'The Brothers', 'NSW', -36.370, 148.950),
('2630', 'Tuross', 'NSW', -36.370, 148.950),
('2630', 'Wambrook', 'NSW', -36.370, 148.950),
('2631', 'Ando', 'NSW', -36.740, 149.260),
('2631', 'Boco', 'NSW', -36.740, 149.260),
('2631', 'Creewah', 'NSW', -36.740, 149.260),
('2631', 'Glen Allen', 'NSW', -36.740, 149.260),
('2631', 'Greenlands', 'NSW', -36.740, 149.260),
('2631', 'Holts Flat', 'NSW', -36.740, 149.260),
('2631', 'Jincumbilly', 'NSW', -36.740, 149.260),
('2631', 'Kybeyan', 'NSW', -36.740, 149.260),
('2631', 'Mount Cooper', 'NSW', -36.740, 149.260),
('2631', 'Nimmitabel', 'NSW', -36.740, 149.260),
('2631', 'Steeple Flat', 'NSW', -36.740, 149.260),
('2631', 'Winifred', 'NSW', -36.740, 149.260),
('2632', 'Bibbenluke', 'NSW', -36.820, 149.280),
('2632', 'Bombala', 'NSW', -36.820, 149.280),
('2632', 'Bondi Forest', 'NSW', -36.820, 149.280),
('2632', 'Bukalong', 'NSW', -36.820, 149.280),
('2632', 'Cambalong', 'NSW', -36.820, 149.280),
('2632', 'Cathcart', 'NSW', -36.820, 149.280),
('2632', 'Coolumbooka', 'NSW', -36.820, 149.280),
('2632', 'Craigie', 'NSW', -36.820, 149.280),
('2632', 'Gunningrah', 'NSW', -36.820, 149.280),
('2632', 'Lords Hill', 'NSW', -36.820, 149.280),
('2632', 'Merriangaah', 'NSW', -36.820, 149.280),
('2632', 'Mila', 'NSW', -36.820, 149.280),
('2632', 'Mount Darragh', 'NSW', -36.820, 149.280),
('2632', 'Paddys Flat', 'NSW', -36.820, 149.280),
('2632', 'Palarang', 'NSW', -36.820, 149.280),
('2632', 'Quidong', 'NSW', -36.820, 149.280),
('2632', 'Rockton', 'NSW', -36.820, 149.280),
('2632', 'Rosemeath', 'NSW', -36.820, 149.280),
('2633', 'Corrowong', 'NSW', -36.930, 148.830),
('2633', 'Delegate', 'NSW', -36.930, 148.830),
('2633', 'Tombong', 'NSW', -36.930, 148.830),
('2640', 'Albury', 'NSW', -36.080, 146.910),
('2640', 'Bungowannah', 'NSW', -36.080, 146.910),
('2640', 'East Albury', 'NSW', -36.080, 146.910),
('2640', 'Ettamogah', 'NSW', -36.080, 146.910),
('2640', 'Glenroy', 'NSW', -36.080, 146.910),
('2640', 'Lavington DC', 'NSW', -36.080, 146.910),
('2640', 'Moorwatha', 'NSW', -36.080, 146.910),
('2640', 'North Albury', 'NSW', -36.080, 146.910),
('2640', 'Ournie', 'NSW', -36.080, 146.910),
('2640', 'South Albury', 'NSW', -36.080, 146.910),
('2640', 'Splitters Creek', 'NSW', -36.080, 146.910),
('2640', 'Table Top', 'NSW', -36.080, 146.910),
('2640', 'Talmalmo', 'NSW', -36.080, 146.910),
('2640', 'Thurgoona', 'NSW', -36.080, 146.910),
('2640', 'West Albury', 'NSW', -36.080, 146.910),
('2640', 'Wirlinga', 'NSW', -36.080, 146.910),
('2640', 'Wymah', 'NSW', -36.080, 146.910),
('2641', 'Hamilton Valley', 'NSW', -36.040, 146.920),
('2641', 'Lavington', 'NSW', -36.040, 146.920),
('2641', 'Springdale Heights', 'NSW', -36.040, 146.920),
('2642', 'Bidgeemia', 'NSW', -35.440, 146.440),
('2642', 'Brocklesby', 'NSW', -35.440, 146.440),
('2642', 'Burrumbuttock', 'NSW', -35.440, 146.440),
('2642', 'Geehi', 'NSW', -35.440, 146.440),
('2642', 'Gerogery', 'NSW', -35.440, 146.440),
('2642', 'Glenellen', 'NSW', -35.440, 146.440),
('2642', 'Greg Greg', 'NSW', -35.440, 146.440),
('2642', 'Indi', 'NSW', -35.440, 146.440),
('2642', 'Jagumba', 'NSW', -35.440, 146.440),
('2642', 'Jagungal Wilderness', 'NSW', -35.440, 146.440),
('2642', 'Jindera', 'NSW', -35.440, 146.440),
('2642', 'Jingellic', 'NSW', -35.440, 146.440),
('2642', 'Khancoban', 'NSW', -35.440, 146.440),
('2642', 'Murray Gorge', 'NSW', -35.440, 146.440),
('2642', 'Rand', 'NSW', -35.440, 146.440),
('2642', 'Tooma', 'NSW', -35.440, 146.440),
('2642', 'Walbundrie', 'NSW', -35.440, 146.440),
('2642', 'Welaregang', 'NSW', -35.440, 146.440),
('2642', 'Wrathall', 'NSW', -35.440, 146.440),
('2642', 'Yerong Creek', 'NSW', -35.440, 146.440),
('2643', 'Howlong', 'NSW', -35.960, 146.610),
('2644', 'Bowna', 'NSW', -35.960, 147.130),
('2644', 'Coppabella', 'NSW', -35.960, 147.130),
('2644', 'Holbrook', 'NSW', -35.960, 147.130),
('2644', 'Lankeys Creek', 'NSW', -35.960, 147.130),
('2644', 'Little Billabong', 'NSW', -35.960, 147.130),
('2644', 'Mountain Creek', 'NSW', -35.960, 147.130),
('2644', 'Mullengandra', 'NSW', -35.960, 147.130),
('2644', 'Wantagong', 'NSW', -35.960, 147.130),
('2644', 'Woomargama', 'NSW', -35.960, 147.130),
('2644', 'Yarara', 'NSW', -35.960, 147.130),
('2645', 'Cullivel', 'NSW', -35.130, 146.130),
('2645', 'Urana', 'NSW', -35.130, 146.130),
('2646', 'Balldale', 'NSW', -35.850, 146.520),
('2646', 'Collendina', 'NSW', -35.850, 146.520),
('2646', 'Coreen', 'NSW', -35.850, 146.520),
('2646', 'Corowa', 'NSW', -35.850, 146.520),
('2646', 'Daysdale', 'NSW', -35.850, 146.520),
('2646', 'Goombargana', 'NSW', -35.850, 146.520),
('2646', 'Hopefield', 'NSW', -35.850, 146.520),
('2646', 'Lowesdale', 'NSW', -35.850, 146.520),
('2646', 'Nyora', 'NSW', -35.850, 146.520),
('2646', 'Oaklands', 'NSW', -35.850, 146.520),
('2646', 'Redlands', 'NSW', -35.850, 146.520),
('2646', 'Rennie', 'NSW', -35.850, 146.520),
('2646', 'Ringwood', 'NSW', -35.850, 146.520),
('2646', 'Sanger', 'NSW', -35.850, 146.520),
('2646', 'Savernake', 'NSW', -35.850, 146.520),
('2647', 'Mulwala', 'NSW', -35.950, 145.960),
('2648', 'Anabranch', 'NSW', -34.150, 142.090),
('2648', 'Curlwaa', 'NSW', -34.150, 142.090),
('2648', 'Pan Ban', 'NSW', -34.150, 142.090),
('2648', 'Pooncarie', 'NSW', -34.150, 142.090),
('2648', 'Rufus River', 'NSW', -34.150, 142.090),
('2648', 'Scotia', 'NSW', -34.150, 142.090),
('2648', 'Wentworth', 'NSW', -34.150, 142.090),
('2649', 'Laurel Hill', 'NSW', -35.600, 148.090),
('2649', 'Nurenmerenmong', 'NSW', -35.600, 148.090),
('2650', 'Alfredtown', 'NSW', -35.160, 147.510),
('2650', 'Ashmont', 'NSW', -35.160, 147.510),
('2650', 'Belfrayden', 'NSW', -35.160, 147.510),
('2650', 'Big Springs', 'NSW', -35.160, 147.510),
('2650', 'Bomen', 'NSW', -35.160, 147.510),
('2650', 'Book Book', 'NSW', -35.160, 147.510),
('2650', 'Boorooma', 'NSW', -35.160, 147.510),
('2650', 'Borambola', 'NSW', -35.160, 147.510),
('2650', 'Bourkelands', 'NSW', -35.160, 147.510),
('2650', 'Brucedale', 'NSW', -35.160, 147.510),
('2650', 'Bulgary', 'NSW', -35.160, 147.510),
('2650', 'Burrandana', 'NSW', -35.160, 147.510),
('2650', 'Carabost', 'NSW', -35.160, 147.510),
('2650', 'Cartwrights Hill', 'NSW', -35.160, 147.510),
('2650', 'Collingullie', 'NSW', -35.160, 147.510),
('2650', 'Cookardinia', 'NSW', -35.160, 147.510),
('2650', 'Currawarna', 'NSW', -35.160, 147.510),
('2650', 'Downside', 'NSW', -35.160, 147.510),
('2650', 'East Wagga Wagga', 'NSW', -35.160, 147.510),
('2650', 'Estella', 'NSW', -35.160, 147.510),
('2650', 'Euberta', 'NSW', -35.160, 147.510),
('2650', 'Eunanoreenya', 'NSW', -35.160, 147.510),
('2650', 'Galore', 'NSW', -35.160, 147.510),
('2650', 'Gelston Park', 'NSW', -35.160, 147.510),
('2650', 'Glenfield Park', 'NSW', -35.160, 147.510),
('2650', 'Gobbagombalin', 'NSW', -35.160, 147.510),
('2650', 'Gregadoo', 'NSW', -35.160, 147.510),
('2650', 'Harefield', 'NSW', -35.160, 147.510),
('2650', 'Hillgrove', 'NSW', -35.160, 147.510),
('2650', 'Kooringal', 'NSW', -35.160, 147.510),
('2650', 'Kyeamba', 'NSW', -35.160, 147.510),
('2650', 'Lake Albert', 'NSW', -35.160, 147.510),
('2650', 'Lloyd', 'NSW', -35.160, 147.510),
('2650', 'Maxwell', 'NSW', -35.160, 147.510),
('2650', 'Moorong', 'NSW', -35.160, 147.510),
('2650', 'Mount Austin', 'NSW', -35.160, 147.510),
('2650', 'North Wagga Wagga', 'NSW', -35.160, 147.510),
('2650', 'Oberne Creek', 'NSW', -35.160, 147.510),
('2650', 'Oura', 'NSW', -35.160, 147.510),
('2650', 'Pulletop', 'NSW', -35.160, 147.510),
('2650', 'Rowan', 'NSW', -35.160, 147.510),
('2650', 'San Isidore', 'NSW', -35.160, 147.510),
('2650', 'Springvale', 'NSW', -35.160, 147.510),
('2650', 'Tatton', 'NSW', -35.160, 147.510),
('2650', 'The Gap', 'NSW', -35.160, 147.510),
('2650', 'Tolland', 'NSW', -35.160, 147.510),
('2650', 'Turvey Park', 'NSW', -35.160, 147.510),
('2650', 'Wagga Wagga', 'NSW', -35.160, 147.510),
('2650', 'Wagga Wagga BC', 'NSW', -35.160, 147.510),
('2650', 'Wallacetown', 'NSW', -35.160, 147.510),
('2650', 'Wantabadgery', 'NSW', -35.160, 147.510),
('2650', 'Yarragundry', 'NSW', -35.160, 147.510),
('2650', 'Yathella', 'NSW', -35.160, 147.510),
('2651', 'Forest Hill', 'NSW', -32.710, 151.550),
('2651', 'Wagga Wagga Raaf', 'NSW', -32.710, 151.550),
('2652', 'Boorga', 'NSW', -34.040, 146.030),
('2652', 'Boree Creek', 'NSW', -34.040, 146.030),
('2652', 'Goolgowi', 'NSW', -34.040, 146.030),
('2652', 'Grong Grong', 'NSW', -34.040, 146.030),
('2652', 'Gumly Gumly', 'NSW', -34.040, 146.030),
('2652', 'Humula', 'NSW', -34.040, 146.030),
('2652', 'Ladysmith', 'NSW', -34.040, 146.030),
('2652', 'Landervale', 'NSW', -34.040, 146.030),
('2652', 'Mangoplah', 'NSW', -34.040, 146.030),
('2652', 'Marrar', 'NSW', -34.040, 146.030),
('2652', 'Matong', 'NSW', -34.040, 146.030),
('2652', 'Merriwagga', 'NSW', -34.040, 146.030),
('2652', 'Murrulebale', 'NSW', -34.040, 146.030),
('2652', 'Old Junee', 'NSW', -34.040, 146.030),
('2652', 'Rosewood', 'NSW', -34.040, 146.030),
('2652', 'Tabbita', 'NSW', -34.040, 146.030),
('2652', 'Tarcutta', 'NSW', -34.040, 146.030),
('2652', 'Uranquinty', 'NSW', -34.040, 146.030),
('2653', 'Burra', 'NSW', -35.830, 148.070),
('2653', 'Courabyra', 'NSW', -35.830, 148.070),
('2653', 'Glenroy', 'NSW', -35.830, 148.070),
('2653', 'Mannus', 'NSW', -35.830, 148.070),
('2653', 'Maragle', 'NSW', -35.830, 148.070),
('2653', 'Munderoo', 'NSW', -35.830, 148.070),
('2653', 'Paddys River', 'NSW', -35.830, 148.070),
('2653', 'Taradale', 'NSW', -35.830, 148.070),
('2653', 'Tumbarumba', 'NSW', -35.830, 148.070),
('2653', 'Westdale', 'NSW', -35.830, 148.070),
('2653', 'Willigobung', 'NSW', -35.830, 148.070),
('2655', 'French Park', 'NSW', -35.320, 147.190),
('2655', 'Kubura', 'NSW', -35.320, 147.190),
('2655', 'The Rock', 'NSW', -35.320, 147.190),
('2655', 'Tootool', 'NSW', -35.320, 147.190),
('2656', 'Brookdale', 'NSW', -35.120, 147.000),
('2656', 'Brookong', 'NSW', -35.120, 147.000),
('2656', 'Fargunyah', 'NSW', -35.120, 147.000),
('2656', 'Lockhart', 'NSW', -35.120, 147.000),
('2656', 'Milbrulong', 'NSW', -35.120, 147.000),
('2656', 'Osborne', 'NSW', -35.120, 147.000),
('2656', 'Urangeline', 'NSW', -35.120, 147.000),
('2656', 'Urangeline East', 'NSW', -35.120, 147.000),
('2658', 'Henty', 'NSW', -35.440, 146.990),
('2658', 'Munyabla', 'NSW', -35.440, 146.990),
('2658', 'Pleasant Hills', 'NSW', -35.440, 146.990),
('2658', 'Ryan', 'NSW', -35.440, 146.990),
('2659', 'Alma Park', 'NSW', -35.600, 146.790),
('2659', 'Walla Walla', 'NSW', -35.600, 146.790),
('2660', 'Culcairn', 'NSW', -35.670, 147.000),
('2660', 'Morven', 'NSW', -35.670, 147.000),
('2661', 'Kapooka', 'NSW', -35.150, 147.300),
('2663', 'Cowabbie', 'NSW', -35.150, 145.980),
('2663', 'Erin Vale', 'NSW', -35.150, 145.980),
('2663', 'Eurongilly', 'NSW', -35.150, 145.980),
('2663', 'Junee', 'NSW', -35.150, 145.980),
('2663', 'Marinna', 'NSW', -35.150, 145.980),
('2663', 'Wantiool', 'NSW', -35.150, 145.980),
('2665', 'Ardlethan', 'NSW', -34.360, 146.900),
('2665', 'Ariah Park', 'NSW', -34.360, 146.900),
('2665', 'Barellan', 'NSW', -34.360, 146.900),
('2665', 'Beckom', 'NSW', -34.360, 146.900),
('2665', 'Bectric', 'NSW', -34.360, 146.900),
('2665', 'Binya', 'NSW', -34.360, 146.900),
('2665', 'Kamarah', 'NSW', -34.360, 146.900),
('2665', 'Mirrool', 'NSW', -34.360, 146.900),
('2665', 'Moombooldool', 'NSW', -34.360, 146.900),
('2665', 'Quandary', 'NSW', -34.360, 146.900),
('2665', 'Tara', 'NSW', -34.360, 146.900),
('2665', 'Walleroobie', 'NSW', -34.360, 146.900),
('2666', 'Combaning', 'NSW', -34.460, 147.680),
('2666', 'Dirnaseer', 'NSW', -34.460, 147.680),
('2666', 'Gidginbung', 'NSW', -34.460, 147.680),
('2666', 'Grogan', 'NSW', -34.460, 147.680),
('2666', 'Junee Reefs', 'NSW', -34.460, 147.680),
('2666', 'Mimosa', 'NSW', -34.460, 147.680),
('2666', 'Morangarell', 'NSW', -34.460, 147.680),
('2666', 'Narraburra', 'NSW', -34.460, 147.680),
('2666', 'Pucawan', 'NSW', -34.460, 147.680),
('2666', 'Reefton', 'NSW', -34.460, 147.680),
('2666', 'Sebastopol', 'NSW', -34.460, 147.680),
('2666', 'Springdale', 'NSW', -34.460, 147.680),
('2666', 'Temora', 'NSW', -34.460, 147.680),
('2666', 'Trungley Hall', 'NSW', -34.460, 147.680),
('2668', 'Barmedman', 'NSW', -34.180, 147.410),
('2669', 'Erigolia', 'NSW', -33.500, 146.800),
('2669', 'Girral', 'NSW', -33.500, 146.800),
('2669', 'Kikoira', 'NSW', -33.500, 146.800),
('2669', 'Melbergen', 'NSW', -33.500, 146.800),
('2669', 'Naradhan', 'NSW', -33.500, 146.800),
('2669', 'Rankins Springs', 'NSW', -33.500, 146.800),
('2669', 'Tallimba', 'NSW', -33.500, 146.800),
('2669', 'Tullibigeal', 'NSW', -33.500, 146.800),
('2669', 'Ungarie', 'NSW', -33.500, 146.800),
('2669', 'Weethalle', 'NSW', -33.500, 146.800),
('2671', 'Alleena', 'NSW', -34.110, 147.140),
('2671', 'Back Creek', 'NSW', -34.110, 147.140),
('2671', 'Burcher', 'NSW', -34.110, 147.140),
('2671', 'Lake Cowal', 'NSW', -34.110, 147.140),
('2671', 'North Yalgogrin', 'NSW', -34.110, 147.140),
('2671', 'West Wyalong', 'NSW', -34.110, 147.140),
('2671', 'Wyalong', 'NSW', -34.110, 147.140),
('2672', 'Curlew Waters', 'NSW', -33.390, 146.580),
('2672', 'Lake Cargelligo', 'NSW', -33.390, 146.580),
('2672', 'Murrin Bridge', 'NSW', -33.390, 146.580),
('2675', 'Hillston', 'NSW', -33.480, 145.530),
('2675', 'Lake Brewster', 'NSW', -33.480, 145.530),
('2675', 'Monia Gap', 'NSW', -33.480, 145.530),
('2675', 'Roto', 'NSW', -33.480, 145.530),
('2675', 'Wallanthery', 'NSW', -33.480, 145.530),
('2678', 'Charles Sturt University', 'NSW', -35.060, 147.350),
('2678', 'Riverina Msc', 'NSW', -35.060, 147.350),
('2680', 'Beelbangera', 'NSW', -34.260, 146.100),
('2680', 'Benerembah', 'NSW', -34.260, 146.100),
('2680', 'Bilbul', 'NSW', -34.260, 146.100),
('2680', 'Griffith', 'NSW', -34.260, 146.100),
('2680', 'Griffith DC', 'NSW', -34.260, 146.100),
('2680', 'Griffith East', 'NSW', -34.260, 146.100),
('2680', 'Hanwood', 'NSW', -34.260, 146.100),
('2680', 'Kooba', 'NSW', -34.260, 146.100),
('2680', 'Lake Wyangan', 'NSW', -34.260, 146.100),
('2680', 'Nericon', 'NSW', -34.260, 146.100),
('2680', 'Tharbogang', 'NSW', -34.260, 146.100),
('2680', 'Warburn', 'NSW', -34.260, 146.100),
('2680', 'Warrawidgee', 'NSW', -34.260, 146.100),
('2680', 'Widgelli', 'NSW', -34.260, 146.100),
('2680', 'Willbriggie', 'NSW', -34.260, 146.100),
('2680', 'Yoogali', 'NSW', -34.260, 146.100),
('2681', 'Myall Park', 'NSW', -34.180, 146.110),
('2681', 'Yenda', 'NSW', -34.180, 146.110),
('2700', 'Bundure', 'NSW', -35.030, 146.610),
('2700', 'Colinroobie', 'NSW', -35.030, 146.610),
('2700', 'Corobimilla', 'NSW', -35.030, 146.610),
('2700', 'Cudgel', 'NSW', -35.030, 146.610),
('2700', 'Euroley', 'NSW', -35.030, 146.610),
('2700', 'Gillenbah', 'NSW', -35.030, 146.610),
('2700', 'Kywong', 'NSW', -35.030, 146.610),
('2700', 'Morundah', 'NSW', -35.030, 146.610),
('2700', 'Narrandera', 'NSW', -35.030, 146.610),
('2700', 'Sandigo', 'NSW', -35.030, 146.610),
('2701', 'Berry Jerry', 'NSW', -34.820, 147.200),
('2701', 'Coolamon', 'NSW', -34.820, 147.200),
('2701', 'Methul', 'NSW', -34.820, 147.200),
('2701', 'Rannock', 'NSW', -34.820, 147.200),
('2702', 'Ganmain', 'NSW', -34.790, 147.040),
('2703', 'Yanco', 'NSW', -34.630, 146.400),
('2705', 'Brobenah', 'NSW', -34.490, 146.430),
('2705', 'Corbie Hill', 'NSW', -34.490, 146.430),
('2705', 'Gogeldrie', 'NSW', -34.490, 146.430),
('2705', 'Leeton', 'NSW', -34.490, 146.430),
('2705', 'Merungle Hill', 'NSW', -34.490, 146.430),
('2705', 'Murrami', 'NSW', -34.490, 146.430),
('2705', 'Stanbridge', 'NSW', -34.490, 146.430),
('2705', 'Whitton', 'NSW', -34.490, 146.430),
('2706', 'Darlington Point', 'NSW', -34.560, 146.010),
('2707', 'Argoon', 'NSW', -34.860, 145.670),
('2707', 'Coleambally', 'NSW', -34.860, 145.670),
('2708', 'Albury Msc', 'NSW', 0.000, 0.000),
('2708', 'Murray Region MC', 'NSW', 0.000, 0.000),
('2710', 'Barratta', 'NSW', -35.230, 144.490),
('2710', 'Birganbigil', 'NSW', -35.230, 144.490),
('2710', 'Booroorban', 'NSW', -35.230, 144.490),
('2710', 'Bullatale', 'NSW', -35.230, 144.490),
('2710', 'Caldwell', 'NSW', -35.230, 144.490),
('2710', 'Calimo', 'NSW', -35.230, 144.490),
('2710', 'Conargo', 'NSW', -35.230, 144.490),
('2710', 'Coree', 'NSW', -35.230, 144.490),
('2710', 'Deniliquin', 'NSW', -35.230, 144.490),
('2710', 'Hartwood', 'NSW', -35.230, 144.490),
('2710', 'Lindifferon', 'NSW', -35.230, 144.490),
('2710', 'Mathoura', 'NSW', -35.230, 144.490),
('2710', 'Mayrung', 'NSW', -35.230, 144.490),
('2710', 'Moonbria', 'NSW', -35.230, 144.490),
('2710', 'Morago', 'NSW', -35.230, 144.490),
('2710', 'Pretty Pine', 'NSW', -35.230, 144.490),
('2710', 'Steam Plains', 'NSW', -35.230, 144.490),
('2710', 'Stud Park', 'NSW', -35.230, 144.490),
('2710', 'Wakool', 'NSW', -35.230, 144.490),
('2710', 'Wandook', 'NSW', -35.230, 144.490),
('2710', 'Wanganella', 'NSW', -35.230, 144.490),
('2710', 'Warragoon', 'NSW', -35.230, 144.490),
('2710', 'Willurah', 'NSW', -35.230, 144.490),
('2711', 'Booligal', 'NSW', -33.680, 144.750),
('2711', 'Carrathool', 'NSW', -33.680, 144.750),
('2711', 'Clare', 'NSW', -33.680, 144.750),
('2711', 'Corrong', 'NSW', -33.680, 144.750),
('2711', 'Gunbar', 'NSW', -33.680, 144.750),
('2711', 'Hay', 'NSW', -33.680, 144.750),
('2711', 'Hay South', 'NSW', -33.680, 144.750),
('2711', 'Keri Keri', 'NSW', -33.680, 144.750),
('2711', 'Maude', 'NSW', -33.680, 144.750),
('2711', 'One Tree', 'NSW', -33.680, 144.750),
('2711', 'Oxley', 'NSW', -33.680, 144.750),
('2711', 'Waugorah', 'NSW', -33.680, 144.750),
('2711', 'Yanga', 'NSW', -33.680, 144.750),
('2712', 'Berrigan', 'NSW', -35.660, 145.810),
('2712', 'Boomanoomana', 'NSW', -35.660, 145.810),
('2713', 'Blighty', 'NSW', -35.590, 145.290),
('2713', 'Finley', 'NSW', -35.590, 145.290),
('2713', 'Logie Brae', 'NSW', -35.590, 145.290),
('2713', 'Myrtle Park', 'NSW', -35.590, 145.290),
('2714', 'Aratula', 'NSW', -35.510, 145.050),
('2714', 'Pine Lodge', 'NSW', -35.510, 145.050),
('2714', 'Tocumwal', 'NSW', -35.510, 145.050),
('2714', 'Tuppal', 'NSW', -35.510, 145.050),
('2715', 'Arumpo', 'NSW', -33.870, 142.890),
('2715', 'Balranald', 'NSW', -33.870, 142.890),
('2715', 'Hatfield', 'NSW', -33.870, 142.890),
('2715', 'Mungo', 'NSW', -33.870, 142.890),
('2716', 'Four Corners', 'NSW', -35.350, 145.530),
('2716', 'Gala Vale', 'NSW', -35.350, 145.530),
('2716', 'Jerilderie', 'NSW', -35.350, 145.530),
('2716', 'Mabins Well', 'NSW', -35.350, 145.530),
('2716', 'Mairjimmy', 'NSW', -35.350, 145.530),
('2717', 'Dareton', 'NSW', -34.090, 142.040),
('2720', 'Argalong', 'NSW', -35.320, 148.450),
('2720', 'Blowering', 'NSW', -35.320, 148.450),
('2720', 'Bogong Peaks Wilderness', 'NSW', -35.320, 148.450),
('2720', 'Bombowlee', 'NSW', -35.320, 148.450),
('2720', 'Bombowlee Creek', 'NSW', -35.320, 148.450),
('2720', 'Buddong', 'NSW', -35.320, 148.450),
('2720', 'Couragago', 'NSW', -35.320, 148.450),
('2720', 'Gadara', 'NSW', -35.320, 148.450),
('2720', 'Gilmore', 'NSW', -35.320, 148.450),
('2720', 'Gocup', 'NSW', -35.320, 148.450),
('2720', 'Goobarragandra', 'NSW', -35.320, 148.450),
('2720', 'Jones Bridge', 'NSW', -35.320, 148.450),
('2720', 'Killimicat', 'NSW', -35.320, 148.450),
('2720', 'Lacmalac', 'NSW', -35.320, 148.450),
('2720', 'Little River', 'NSW', -35.320, 148.450),
('2720', 'Minjary', 'NSW', -35.320, 148.450),
('2720', 'Mundongo', 'NSW', -35.320, 148.450),
('2720', 'Pinbeyan', 'NSW', -35.320, 148.450),
('2720', 'Red Hill', 'NSW', -35.320, 148.450),
('2720', 'Talbingo', 'NSW', -35.320, 148.450),
('2720', 'Tumorrama', 'NSW', -35.320, 148.450),
('2720', 'Tumut', 'NSW', -35.320, 148.450),
('2720', 'Tumut Plains', 'NSW', -35.320, 148.450),
('2720', 'Wereboldera', 'NSW', -35.320, 148.450),
('2720', 'Wermatong', 'NSW', -35.320, 148.450),
('2720', 'Windowie', 'NSW', -35.320, 148.450),
('2720', 'Wyangle', 'NSW', -35.320, 148.450),
('2720', 'Yarrangobilly', 'NSW', -35.320, 148.450),
('2721', 'Bland', 'NSW', -33.990, 147.680),
('2721', 'Quandialla', 'NSW', -33.990, 147.680),
('2722', 'Brungle', 'NSW', -34.820, 148.080),
('2722', 'Brungle Creek', 'NSW', -34.820, 148.080),
('2722', 'Burra Creek', 'NSW', -34.820, 148.080),
('2722', 'Darbalara', 'NSW', -34.820, 148.080),
('2722', 'Gundagai', 'NSW', -34.820, 148.080),
('2722', 'Jones Creek', 'NSW', -34.820, 148.080),
('2722', 'Muttama', 'NSW', -34.820, 148.080),
('2722', 'Nangus', 'NSW', -34.820, 148.080),
('2722', 'South Gundagai', 'NSW', -34.820, 148.080),
('2725', 'Stockinbingal', 'NSW', -34.510, 147.880),
('2726', 'Jugiong', 'NSW', -34.910, 148.320),
('2727', 'Adjungbilly', 'NSW', -35.080, 148.410),
('2727', 'Coolac', 'NSW', -35.080, 148.410),
('2727', 'Gobarralong', 'NSW', -35.080, 148.410),
('2729', 'Adelong', 'NSW', -35.310, 148.060),
('2729', 'Bangadang', 'NSW', -35.310, 148.060),
('2729', 'Black Creek', 'NSW', -35.310, 148.060),
('2729', 'Califat', 'NSW', -35.310, 148.060),
('2729', 'Cooleys Creek', 'NSW', -35.310, 148.060),
('2729', 'Darlow', 'NSW', -35.310, 148.060),
('2729', 'Ellerslie', 'NSW', -35.310, 148.060),
('2729', 'Grahamstown', 'NSW', -35.310, 148.060),
('2729', 'Mount Adrah', 'NSW', -35.310, 148.060),
('2729', 'Mount Horeb', 'NSW', -35.310, 148.060),
('2729', 'Mundarlo', 'NSW', -35.310, 148.060),
('2729', 'Sandy Gully', 'NSW', -35.310, 148.060),
('2729', 'Sharps Creek', 'NSW', -35.310, 148.060),
('2729', 'Tumblong', 'NSW', -35.310, 148.060),
('2729', 'Westwood', 'NSW', -35.310, 148.060),
('2729', 'Wondalga', 'NSW', -35.310, 148.060),
('2729', 'Yaven Creek', 'NSW', -35.310, 148.060),
('2730', 'Batlow', 'NSW', -31.490, 152.660),
('2730', 'Green Hills', 'NSW', -31.490, 152.660),
('2730', 'Kunama', 'NSW', -31.490, 152.660),
('2730', 'Lower Bago', 'NSW', -31.490, 152.660),
('2731', 'Bunnaloo', 'NSW', -35.790, 144.630),
('2731', 'Moama', 'NSW', -35.790, 144.630),
('2731', 'Tantonan', 'NSW', -35.790, 144.630),
('2731', 'Thyra', 'NSW', -35.790, 144.630),
('2731', 'Womboota', 'NSW', -35.790, 144.630),
('2732', 'Barham', 'NSW', -35.630, 144.130),
('2732', 'Burraboi', 'NSW', -35.630, 144.130),
('2732', 'Cobramunga', 'NSW', -35.630, 144.130),
('2732', 'Gonn', 'NSW', -35.630, 144.130),
('2732', 'Noorong', 'NSW', -35.630, 144.130),
('2732', 'Thule', 'NSW', -35.630, 144.130),
('2732', 'Tullakool', 'NSW', -35.630, 144.130),
('2733', 'Dhuragoon', 'NSW', -35.190, 144.160),
('2733', 'Moulamein', 'NSW', -35.190, 144.160),
('2733', 'Niemur', 'NSW', -35.190, 144.160),
('2734', 'Cunninyeuk', 'NSW', -35.250, 143.900),
('2734', 'Dilpurra', 'NSW', -35.250, 143.900),
('2734', 'Kyalite', 'NSW', -35.250, 143.900),
('2734', 'Mellool', 'NSW', -35.250, 143.900),
('2734', 'Moolpa', 'NSW', -35.250, 143.900),
('2734', 'Stony Crossing', 'NSW', -35.250, 143.900),
('2734', 'Tooranie', 'NSW', -35.250, 143.900),
('2734', 'Wetuppa', 'NSW', -35.250, 143.900),
('2735', 'Koraleigh', 'NSW', -35.660, 144.140),
('2735', 'Speewa', 'NSW', -35.660, 144.140),
('2736', 'Goodnight', 'NSW', -34.960, 143.340),
('2736', 'Tooleybuc', 'NSW', -34.960, 143.340),
('2737', 'Euston', 'NSW', -34.510, 142.850),
('2738', 'Gol Gol', 'NSW', -34.180, 142.220),
('2738', 'Monak', 'NSW', -34.180, 142.220),
('2739', 'Buronga', 'NSW', -34.170, 142.180),
('2745', 'Glenmore Park', 'NSW', -33.790, 150.670),
('2745', 'Greendale', 'NSW', -33.790, 150.670),
('2745', 'Luddenham', 'NSW', -33.790, 150.670),
('2745', 'Mulgoa', 'NSW', -33.790, 150.670),
('2745', 'Regentville', 'NSW', -33.790, 150.670),
('2745', 'Wallacia', 'NSW', -33.790, 150.670),
('2747', 'Cambridge Gardens', 'NSW', -33.740, 150.720),
('2747', 'Cambridge Park', 'NSW', -33.740, 150.720),
('2747', 'Claremont Meadows', 'NSW', -33.740, 150.720),
('2747', 'Kingswood', 'NSW', -33.740, 150.720),
('2747', 'Llandilo', 'NSW', -33.740, 150.720),
('2747', 'Shanes Park', 'NSW', -33.740, 150.720),
('2747', 'Werrington', 'NSW', -33.740, 150.720),
('2747', 'Werrington County', 'NSW', -33.740, 150.720),
('2747', 'Werrington Downs', 'NSW', -33.740, 150.720),
('2748', 'Orchard Hills', 'NSW', -33.780, 150.720),
('2749', 'Castlereagh', 'NSW', -33.670, 150.680),
('2749', 'Cranebrook', 'NSW', -33.670, 150.680),
('2750', 'Emu Heights', 'NSW', -33.740, 150.650),
('2750', 'Emu Plains', 'NSW', -33.740, 150.650),
('2750', 'Jamisontown', 'NSW', -33.740, 150.650),
('2750', 'Leonay', 'NSW', -33.740, 150.650),
('2750', 'Penrith', 'NSW', -33.740, 150.650),
('2750', 'Penrith Plaza', 'NSW', -33.740, 150.650),
('2750', 'Penrith South', 'NSW', -33.740, 150.650),
('2750', 'South Penrith', 'NSW', -33.740, 150.650),
('2751', 'Penrith', 'NSW', -33.730, 151.280),
('2752', 'Silverdale', 'NSW', -33.940, 150.580),
('2752', 'Warragamba', 'NSW', -33.940, 150.580),
('2753', 'Agnes Banks', 'NSW', -33.620, 150.710),
('2753', 'Bowen Mountain', 'NSW', -33.620, 150.710),
('2753', 'Grose Vale', 'NSW', -33.620, 150.710),
('2753', 'Grose Wold', 'NSW', -33.620, 150.710),
('2753', 'Hobartville', 'NSW', -33.620, 150.710),
('2753', 'Londonderry', 'NSW', -33.620, 150.710),
('2753', 'Richmond', 'NSW', -33.620, 150.710),
('2753', 'Richmond Lowlands', 'NSW', -33.620, 150.710),
('2753', 'Yarramundi', 'NSW', -33.620, 150.710),
('2754', 'North Richmond', 'NSW', -33.580, 150.720),
('2754', 'Tennyson', 'NSW', -33.580, 150.720),
('2754', 'The Slopes', 'NSW', -33.580, 150.720),
('2755', 'Richmond Raaf', 'NSW', -33.600, 150.800),
('2756', 'Bligh Park', 'NSW', -33.640, 150.790),
('2756', 'Cattai', 'NSW', -33.640, 150.790),
('2756', 'Central Colo', 'NSW', -33.640, 150.790),
('2756', 'Clarendon', 'NSW', -33.640, 150.790),
('2756', 'Colo', 'NSW', -33.640, 150.790),
('2756', 'Colo Heights', 'NSW', -33.640, 150.790),
('2756', 'Cornwallis', 'NSW', -33.640, 150.790),
('2756', 'Cumberland Reach', 'NSW', -33.640, 150.790),
('2756', 'Ebenezer', 'NSW', -33.640, 150.790),
('2756', 'Freemans Reach', 'NSW', -33.640, 150.790),
('2756', 'Glossodia', 'NSW', -33.640, 150.790),
('2756', 'Lower Portland', 'NSW', -33.640, 150.790),
('2756', 'Maroota', 'NSW', -33.640, 150.790),
('2756', 'Mcgraths Hill', 'NSW', -33.640, 150.790),
('2756', 'Mellong', 'NSW', -33.640, 150.790),
('2756', 'Mulgrave', 'NSW', -33.640, 150.790),
('2756', 'Pitt Town', 'NSW', -33.640, 150.790),
('2756', 'Pitt Town Bottoms', 'NSW', -33.640, 150.790),
('2756', 'Sackville', 'NSW', -33.640, 150.790),
('2756', 'Sackville North', 'NSW', -33.640, 150.790),
('2756', 'Scheyville', 'NSW', -33.640, 150.790),
('2756', 'South Maroota', 'NSW', -33.640, 150.790),
('2756', 'South Windsor', 'NSW', -33.640, 150.790),
('2756', 'Upper Colo', 'NSW', -33.640, 150.790),
('2756', 'Wilberforce', 'NSW', -33.640, 150.790),
('2756', 'Windsor', 'NSW', -33.640, 150.790),
('2756', 'Windsor Downs', 'NSW', -33.640, 150.790),
('2756', 'Womerah', 'NSW', -33.640, 150.790),
('2757', 'Kurmond', 'NSW', -33.550, 150.700),
('2758', 'Berambing', 'NSW', -33.540, 150.440),
('2758', 'Bilpin', 'NSW', -33.540, 150.440),
('2758', 'Blaxlands Ridge', 'NSW', -33.540, 150.440),
('2758', 'East Kurrajong', 'NSW', -33.540, 150.440),
('2758', 'Kurrajong', 'NSW', -33.540, 150.440),
('2758', 'Kurrajong Heights', 'NSW', -33.540, 150.440),
('2758', 'Kurrajong Hills', 'NSW', -33.540, 150.440),
('2758', 'Mount Tomah', 'NSW', -33.540, 150.440),
('2758', 'Mountain Lagoon', 'NSW', -33.540, 150.440),
('2758', 'The Devils Wilderness', 'NSW', -33.540, 150.440),
('2758', 'Wheeny Creek', 'NSW', -33.540, 150.440),
('2759', 'Erskine Park', 'NSW', -33.810, 150.790),
('2759', 'St Clair', 'NSW', -33.810, 150.790),
('2760', 'Colyton', 'NSW', -33.780, 150.790),
('2760', 'North St Marys', 'NSW', -33.780, 150.790),
('2760', 'Oxley Park', 'NSW', -33.780, 150.790),
('2760', 'Ropes Crossing', 'NSW', -33.780, 150.790),
('2760', 'St Marys', 'NSW', -33.780, 150.790),
('2760', 'St Marys East', 'NSW', -33.780, 150.790),
('2760', 'St Marys South', 'NSW', -33.780, 150.790),
('2761', 'Colebee', 'NSW', -33.730, 150.870),
('2761', 'Dean Park', 'NSW', -33.730, 150.870),
('2761', 'Glendenning', 'NSW', -33.730, 150.870),
('2761', 'Hassall Grove', 'NSW', -33.730, 150.870),
('2761', 'Oakhurst', 'NSW', -33.730, 150.870),
('2761', 'Plumpton', 'NSW', -33.730, 150.870),
('2762', 'Schofields', 'NSW', -33.700, 150.890),
('2763', 'Acacia Gardens', 'NSW', -33.730, 150.910),
('2763', 'Quakers Hill', 'NSW', -33.730, 150.910),
('2765', 'Berkshire Park', 'NSW', -33.670, 150.800),
('2765', 'Box Hill', 'NSW', -33.670, 150.800),
('2765', 'Maraylya', 'NSW', -33.670, 150.800),
('2765', 'Marsden Park', 'NSW', -33.670, 150.800),
('2765', 'Nelson', 'NSW', -33.670, 150.800),
('2765', 'Oakville', 'NSW', -33.670, 150.800),
('2765', 'Riverstone', 'NSW', -33.670, 150.800),
('2765', 'Vineyard', 'NSW', -33.670, 150.800),
('2766', 'Eastern Creek', 'NSW', -33.800, 150.850),
('2766', 'Rooty Hill', 'NSW', -33.800, 150.850),
('2767', 'Doonside', 'NSW', -33.770, 150.870),
('2767', 'Woodcroft', 'NSW', -33.770, 150.870),
('2768', 'Glenwood', 'NSW', -33.740, 150.920),
('2768', 'Parklea', 'NSW', -33.740, 150.920),
('2768', 'Stanhope Gardens', 'NSW', -33.740, 150.920),
('2769', 'The Ponds', 'NSW', -34.050, 150.750),
('2770', 'Bidwill', 'NSW', -33.730, 150.820),
('2770', 'Blackett', 'NSW', -33.730, 150.820),
('2770', 'Dharruk', 'NSW', -33.730, 150.820),
('2770', 'Emerton', 'NSW', -33.730, 150.820),
('2770', 'Hebersham', 'NSW', -33.730, 150.820),
('2770', 'Lethbridge Park', 'NSW', -33.730, 150.820),
('2770', 'Minchinbury', 'NSW', -33.730, 150.820),
('2770', 'Mount Druitt', 'NSW', -33.730, 150.820),
('2770', 'Mount Druitt Village', 'NSW', -33.730, 150.820),
('2770', 'Shalvey', 'NSW', -33.730, 150.820),
('2770', 'Tregear', 'NSW', -33.730, 150.820),
('2770', 'Whalan', 'NSW', -33.730, 150.820),
('2770', 'Willmot', 'NSW', -33.730, 150.820),
('2773', 'Glenbrook', 'NSW', -33.770, 150.620),
('2773', 'Lapstone', 'NSW', -33.770, 150.620),
('2774', 'Blaxland', 'NSW', -33.740, 150.610),
('2774', 'Blaxland East', 'NSW', -33.740, 150.610),
('2774', 'Mount Riverview', 'NSW', -33.740, 150.610),
('2774', 'Warrimoo', 'NSW', -33.740, 150.610),
('2775', 'Central Macdonald', 'NSW', -33.330, 150.980),
('2775', 'Fernances', 'NSW', -33.330, 150.980),
('2775', 'Gunderman', 'NSW', -33.330, 150.980),
('2775', 'Higher Macdonald', 'NSW', -33.330, 150.980),
('2775', 'Laughtondale', 'NSW', -33.330, 150.980),
('2775', 'Leets Vale', 'NSW', -33.330, 150.980),
('2775', 'Lower Macdonald', 'NSW', -33.330, 150.980),
('2775', 'Marlow', 'NSW', -33.330, 150.980),
('2775', 'Mogo Creek', 'NSW', -33.330, 150.980),
('2775', 'Perrys Crossing', 'NSW', -33.330, 150.980),
('2775', 'Singletons Mill', 'NSW', -33.330, 150.980),
('2775', 'Spencer', 'NSW', -33.330, 150.980),
('2775', 'St Albans', 'NSW', -33.330, 150.980),
('2775', 'Upper Macdonald', 'NSW', -33.330, 150.980),
('2775', 'Webbs Creek', 'NSW', -33.330, 150.980),
('2775', 'Wisemans Ferry', 'NSW', -33.330, 150.980),
('2775', 'Wrights Creek', 'NSW', -33.330, 150.980),
('2776', 'Faulconbridge', 'NSW', -33.700, 150.530),
('2777', 'Hawkesbury Heights', 'NSW', -33.670, 150.650),
('2777', 'Springwood', 'NSW', -33.670, 150.650),
('2777', 'Sun Valley', 'NSW', -33.670, 150.650),
('2777', 'Valley Heights', 'NSW', -33.670, 150.650),
('2777', 'Winmalee', 'NSW', -33.670, 150.650),
('2777', 'Yellow Rock', 'NSW', -33.670, 150.650),
('2778', 'Linden', 'NSW', -33.790, 150.040),
('2778', 'Woodford', 'NSW', -33.790, 150.040),
('2779', 'Hazelbrook', 'NSW', -33.720, 150.450),
('2780', 'Katoomba', 'NSW', -33.710, 150.310),
('2780', 'Katoomba DC', 'NSW', -33.710, 150.310),
('2780', 'Leura', 'NSW', -33.710, 150.310),
('2780', 'Medlow Bath', 'NSW', -33.710, 150.310),
('2782', 'Wentworth Falls', 'NSW', -33.710, 150.380),
('2783', 'Lawson', 'NSW', -33.720, 150.430),
('2784', 'Bullaburra', 'NSW', -33.720, 150.410),
('2785', 'Blackheath', 'NSW', -33.640, 150.280),
('2785', 'Megalong', 'NSW', -33.640, 150.280),
('2786', 'Bell', 'NSW', -33.510, 150.280),
('2786', 'Dargan', 'NSW', -33.510, 150.280),
('2786', 'Mount Irvine', 'NSW', -33.510, 150.280),
('2786', 'Mount Victoria', 'NSW', -33.510, 150.280),
('2786', 'Mount Wilson', 'NSW', -33.510, 150.280),
('2787', 'Black Springs', 'NSW', -33.840, 149.710),
('2787', 'Chatham Valley', 'NSW', -33.840, 149.710),
('2787', 'Duckmaloi', 'NSW', -33.840, 149.710),
('2787', 'Edith', 'NSW', -33.840, 149.710),
('2787', 'Gingkin', 'NSW', -33.840, 149.710),
('2787', 'Gurnang', 'NSW', -33.840, 149.710),
('2787', 'Hazelgrove', 'NSW', -33.840, 149.710),
('2787', 'Jaunter', 'NSW', -33.840, 149.710),
('2787', 'Kanangra', 'NSW', -33.840, 149.710),
('2787', 'Mayfield', 'NSW', -33.840, 149.710),
('2787', 'Mount Olive', 'NSW', -33.840, 149.710),
('2787', 'Mount Werong', 'NSW', -33.840, 149.710),
('2787', 'Mozart', 'NSW', -33.840, 149.710),
('2787', 'Norway', 'NSW', -33.840, 149.710),
('2787', 'Oberon', 'NSW', -33.840, 149.710),
('2787', 'Porters Retreat', 'NSW', -33.840, 149.710),
('2787', 'Shooters Hill', 'NSW', -33.840, 149.710),
('2787', 'Tarana', 'NSW', -33.840, 149.710),
('2787', 'The Meadows', 'NSW', -33.840, 149.710),
('2790', 'Ben Bullen', 'NSW', -33.220, 150.020),
('2790', 'Blackmans Flat', 'NSW', -33.220, 150.020),
('2790', 'Bowenfels', 'NSW', -33.220, 150.020),
('2790', 'Clarence', 'NSW', -33.220, 150.020),
('2790', 'Cobar Park', 'NSW', -33.220, 150.020),
('2790', 'Corney Town', 'NSW', -33.220, 150.020),
('2790', 'Cullen Bullen', 'NSW', -33.220, 150.020),
('2790', 'Doctors Gap', 'NSW', -33.220, 150.020),
('2790', 'Ganbenang', 'NSW', -33.220, 150.020),
('2790', 'Hampton', 'NSW', -33.220, 150.020),
('2790', 'Hartley', 'NSW', -33.220, 150.020),
('2790', 'Hartley Vale', 'NSW', -33.220, 150.020),
('2790', 'Hassans Walls', 'NSW', -33.220, 150.020),
('2790', 'Hermitage Flat', 'NSW', -33.220, 150.020),
('2790', 'Jenolan', 'NSW', -33.220, 150.020),
('2790', 'Kanimbla', 'NSW', -33.220, 150.020),
('2790', 'Lidsdale', 'NSW', -33.220, 150.020),
('2790', 'Lithgow', 'NSW', -33.220, 150.020),
('2790', 'Lithgow DC', 'NSW', -33.220, 150.020),
('2790', 'Little Hartley', 'NSW', -33.220, 150.020),
('2790', 'Littleton', 'NSW', -33.220, 150.020),
('2790', 'Lowther', 'NSW', -33.220, 150.020),
('2790', 'Marrangaroo', 'NSW', -33.220, 150.020),
('2790', 'Mckellars Park', 'NSW', -33.220, 150.020),
('2790', 'Morts Estate', 'NSW', -33.220, 150.020),
('2790', 'Mount Lambie', 'NSW', -33.220, 150.020),
('2790', 'Newnes', 'NSW', -33.220, 150.020),
('2790', 'Newnes Plateau', 'NSW', -33.220, 150.020),
('2790', 'Oaky Park', 'NSW', -33.220, 150.020),
('2790', 'Pottery Estate', 'NSW', -33.220, 150.020),
('2790', 'Rydal', 'NSW', -33.220, 150.020),
('2790', 'Sheedys Gully', 'NSW', -33.220, 150.020),
('2790', 'Sodwalls', 'NSW', -33.220, 150.020),
('2790', 'South Bowenfels', 'NSW', -33.220, 150.020),
('2790', 'South Littleton', 'NSW', -33.220, 150.020),
('2790', 'Springvale', 'NSW', -33.220, 150.020),
('2790', 'State Mine Gully', 'NSW', -33.220, 150.020),
('2790', 'Vale Of Clwydd', 'NSW', -33.220, 150.020),
('2790', 'Wolgan Valley', 'NSW', -33.220, 150.020),
('2790', 'Wollangambe', 'NSW', -33.220, 150.020),
('2791', 'Carcoar', 'NSW', -33.610, 149.140),
('2791', 'Errowanbang', 'NSW', -33.610, 149.140),
('2792', 'Burnt Yards', 'NSW', -33.590, 149.030),
('2792', 'Mandurama', 'NSW', -33.590, 149.030),
('2793', 'Darbys Falls', 'NSW', -33.930, 148.860),
('2793', 'Roseberg', 'NSW', -33.930, 148.860),
('2793', 'Woodstock', 'NSW', -33.930, 148.860),
('2794', 'Bumbaldry', 'NSW', -33.910, 148.460),
('2794', 'Cowra', 'NSW', -33.910, 148.460),
('2794', 'Hovells Creek', 'NSW', -33.910, 148.460),
('2794', 'Mount Collins', 'NSW', -33.910, 148.460),
('2794', 'Wattamondara', 'NSW', -33.910, 148.460),
('2795', 'Abercrombie', 'NSW', -33.910, 149.330),
('2795', 'Abercrombie River', 'NSW', -33.910, 149.330),
('2795', 'Arkell', 'NSW', -33.910, 149.330),
('2795', 'Arkstone', 'NSW', -33.910, 149.330),
('2795', 'Bald Ridge', 'NSW', -33.910, 149.330),
('2795', 'Ballyroe', 'NSW', -33.910, 149.330),
('2795', 'Bathampton', 'NSW', -33.910, 149.330),
('2795', 'Bathurst', 'NSW', -33.910, 149.330),
('2795', 'Billywillinga', 'NSW', -33.910, 149.330),
('2795', 'Brewongle', 'NSW', -33.910, 149.330),
('2795', 'Bruinbun', 'NSW', -33.910, 149.330),
('2795', 'Burraga', 'NSW', -33.910, 149.330),
('2795', 'Caloola', 'NSW', -33.910, 149.330),
('2795', 'Charles Sturt University', 'NSW', -33.910, 149.330),
('2795', 'Charlton', 'NSW', -33.910, 149.330),
('2795', 'Clear Creek', 'NSW', -33.910, 149.330),
('2795', 'Colo', 'NSW', -33.910, 149.330),
('2795', 'Copperhannia', 'NSW', -33.910, 149.330),
('2795', 'Cow Flat', 'NSW', -33.910, 149.330),
('2795', 'Crudine', 'NSW', -33.910, 149.330),
('2795', 'Curragh', 'NSW', -33.910, 149.330),
('2795', 'Dark Corner', 'NSW', -33.910, 149.330),
('2795', 'Dog Rocks', 'NSW', -33.910, 149.330),
('2795', 'Dunkeld', 'NSW', -33.910, 149.330),
('2795', 'Duramana', 'NSW', -33.910, 149.330),
('2795', 'Eglinton', 'NSW', -33.910, 149.330),
('2795', 'Essington', 'NSW', -33.910, 149.330),
('2795', 'Evans Plains', 'NSW', -33.910, 149.330),
('2795', 'Fitzgeralds Valley', 'NSW', -33.910, 149.330),
('2795', 'Forest Grove', 'NSW', -33.910, 149.330),
('2795', 'Fosters Valley', 'NSW', -33.910, 149.330),
('2795', 'Freemantle', 'NSW', -33.910, 149.330),
('2795', 'Gemalla', 'NSW', -33.910, 149.330),
('2795', 'Georges Plains', 'NSW', -33.910, 149.330),
('2795', 'Gilmandyke', 'NSW', -33.910, 149.330),
('2795', 'Glanmire', 'NSW', -33.910, 149.330),
('2795', 'Gormans Hill', 'NSW', -33.910, 149.330),
('2795', 'Gowan', 'NSW', -33.910, 149.330),
('2795', 'Hobbys Yards', 'NSW', -33.910, 149.330),
('2795', 'Isabella', 'NSW', -33.910, 149.330),
('2795', 'Jeremy', 'NSW', -33.910, 149.330),
('2795', 'Judds Creek', 'NSW', -33.910, 149.330),
('2795', 'Kelso', 'NSW', -33.910, 149.330),
('2795', 'Killongbutta', 'NSW', -33.910, 149.330),
('2795', 'Kirkconnell', 'NSW', -33.910, 149.330),
('2795', 'Laffing Waters', 'NSW', -33.910, 149.330),
('2795', 'Limekilns', 'NSW', -33.910, 149.330),
('2795', 'Llanarth', 'NSW', -33.910, 149.330),
('2795', 'Locksley', 'NSW', -33.910, 149.330),
('2795', 'Meadow Flat', 'NSW', -33.910, 149.330),
('2795', 'Milkers Flat', 'NSW', -33.910, 149.330),
('2795', 'Millah Murrah', 'NSW', -33.910, 149.330),
('2795', 'Mitchell', 'NSW', -33.910, 149.330),
('2795', 'Moorilda', 'NSW', -33.910, 149.330),
('2795', 'Mount David', 'NSW', -33.910, 149.330),
('2795', 'Mount Panorama', 'NSW', -33.910, 149.330),
('2795', 'Mount Rankin', 'NSW', -33.910, 149.330),
('2795', 'Napoleon Reef', 'NSW', -33.910, 149.330),
('2795', 'Newbridge', 'NSW', -33.910, 149.330),
('2795', 'OConnell', 'NSW', -33.910, 149.330),
('2795', 'Orton Park', 'NSW', -33.910, 149.330),
('2795', 'Paling Yards', 'NSW', -33.910, 149.330),
('2795', 'Palmers Oaky', 'NSW', -33.910, 149.330),
('2795', 'Peel', 'NSW', -33.910, 149.330),
('2795', 'Perthville', 'NSW', -33.910, 149.330),
('2795', 'Raglan', 'NSW', -33.910, 149.330),
('2795', 'Robin Hill', 'NSW', -33.910, 149.330),
('2795', 'Rock Forest', 'NSW', -33.910, 149.330),
('2795', 'Rockley', 'NSW', -33.910, 149.330),
('2795', 'Rockley Mount', 'NSW', -33.910, 149.330),
('2795', 'Sofala', 'NSW', -33.910, 149.330),
('2795', 'South Bathurst', 'NSW', -33.910, 149.330),
('2795', 'Stewarts Mount', 'NSW', -33.910, 149.330),
('2795', 'Sunny Corner', 'NSW', -33.910, 149.330),
('2795', 'Tambaroora', 'NSW', -33.910, 149.330),
('2795', 'Tannas Mount', 'NSW', -33.910, 149.330),
('2795', 'The Lagoon', 'NSW', -33.910, 149.330),
('2795', 'The Rocks', 'NSW', -33.910, 149.330),
('2795', 'Triangle Flat', 'NSW', -33.910, 149.330),
('2795', 'Trunkey Creek', 'NSW', -33.910, 149.330),
('2795', 'Turondale', 'NSW', -33.910, 149.330),
('2795', 'Twenty Forests', 'NSW', -33.910, 149.330),
('2795', 'Upper Turon', 'NSW', -33.910, 149.330),
('2795', 'Walang', 'NSW', -33.910, 149.330),
('2795', 'Wambool', 'NSW', -33.910, 149.330),
('2795', 'Wattle Flat', 'NSW', -33.910, 149.330),
('2795', 'Watton', 'NSW', -33.910, 149.330),
('2795', 'West Bathurst', 'NSW', -33.910, 149.330),
('2795', 'White Rock', 'NSW', -33.910, 149.330),
('2795', 'Wiagdon', 'NSW', -33.910, 149.330),
('2795', 'Wimbledon', 'NSW', -33.910, 149.330),
('2795', 'Winburndale', 'NSW', -33.910, 149.330),
('2795', 'Windradyne', 'NSW', -33.910, 149.330),
('2795', 'Wisemans Creek', 'NSW', -33.910, 149.330),
('2795', 'Yarras', 'NSW', -33.910, 149.330),
('2795', 'Yetholme', 'NSW', -33.910, 149.330),
('2796', 'Bathurst MC', 'NSW', 0.000, 0.000),
('2797', 'Garland', 'NSW', -33.710, 149.030),
('2797', 'Lyndhurst', 'NSW', -33.710, 149.030),
('2798', 'Byng', 'NSW', -33.340, 149.250),
('2798', 'Forest Reefs', 'NSW', -33.340, 149.250),
('2798', 'Guyong', 'NSW', -33.340, 149.250),
('2798', 'Millthorpe', 'NSW', -33.340, 149.250),
('2798', 'Spring Terrace', 'NSW', -33.340, 149.250),
('2798', 'Tallwood', 'NSW', -33.340, 149.250),
('2799', 'Barry', 'NSW', -33.650, 149.270),
('2799', 'Blayney', 'NSW', -33.650, 149.270),
('2799', 'Browns Creek', 'NSW', -33.650, 149.270),
('2799', 'Fitzgeralds Mount', 'NSW', -33.650, 149.270),
('2799', 'Kings Plains', 'NSW', -33.650, 149.270),
('2799', 'Neville', 'NSW', -33.650, 149.270),
('2799', 'Vittoria', 'NSW', -33.650, 149.270),
('2800', 'Belgravia', 'NSW', -33.120, 149.030),
('2800', 'Borenore', 'NSW', -33.120, 149.030),
('2800', 'Cadia', 'NSW', -33.120, 149.030),
('2800', 'Canobolas', 'NSW', -33.120, 149.030),
('2800', 'Cargo', 'NSW', -33.120, 149.030),
('2800', 'Clergate', 'NSW', -33.120, 149.030),
('2800', 'Clifton Grove', 'NSW', -33.120, 149.030),
('2800', 'Emu Swamp', 'NSW', -33.120, 149.030),
('2800', 'Four Mile Creek', 'NSW', -33.120, 149.030),
('2800', 'Huntley', 'NSW', -33.120, 149.030),
('2800', 'Kangaroobie', 'NSW', -33.120, 149.030),
('2800', 'Kerrs Creek', 'NSW', -33.120, 149.030),
('2800', 'Lewis Ponds', 'NSW', -33.120, 149.030),
('2800', 'Lidster', 'NSW', -33.120, 149.030),
('2800', 'Lower Lewis Ponds', 'NSW', -33.120, 149.030),
('2800', 'Lucknow', 'NSW', -33.120, 149.030),
('2800', 'March', 'NSW', -33.120, 149.030),
('2800', 'Mullion Creek', 'NSW', -33.120, 149.030),
('2800', 'Nangar', 'NSW', -33.120, 149.030),
('2800', 'Nashdale', 'NSW', -33.120, 149.030),
('2800', 'Ophir', 'NSW', -33.120, 149.030),
('2800', 'Orange', 'NSW', -33.120, 149.030),
('2800', 'Orange DC', 'NSW', -33.120, 149.030),
('2800', 'Orange East', 'NSW', -33.120, 149.030),
('2800', 'Panuara', 'NSW', -33.120, 149.030),
('2800', 'Shadforth', 'NSW', -33.120, 149.030),
('2800', 'Spring Creek', 'NSW', -33.120, 149.030),
('2800', 'Spring Hill', 'NSW', -33.120, 149.030),
('2800', 'Springside', 'NSW', -33.120, 149.030),
('2800', 'Summer Hill Creek', 'NSW', -33.120, 149.030),
('2800', 'Waldegrave', 'NSW', -33.120, 149.030),
('2800', 'Windera', 'NSW', -33.120, 149.030),
('2803', 'Bendick Murrell', 'NSW', -34.160, 148.450),
('2803', 'Crowther', 'NSW', -34.160, 148.450),
('2803', 'Wirrimah', 'NSW', -34.160, 148.450),
('2804', 'Billimari', 'NSW', -33.680, 148.620),
('2804', 'Canowindra', 'NSW', -33.680, 148.620),
('2804', 'Moorbel', 'NSW', -33.680, 148.620),
('2804', 'Nyrang Creek', 'NSW', -33.680, 148.620),
('2805', 'Gooloogong', 'NSW', -33.650, 148.410),
('2806', 'Eugowra', 'NSW', -33.430, 148.370),
('2807', 'Koorawatha', 'NSW', -34.040, 148.550),
('2808', 'Wyangala', 'NSW', -33.940, 149.050),
('2809', 'Greenethorpe', 'NSW', -34.040, 148.400),
('2810', 'Bimbi', 'NSW', -33.990, 147.930),
('2810', 'Caragabal', 'NSW', -33.990, 147.930),
('2810', 'Glenelg', 'NSW', -33.990, 147.930),
('2810', 'Grenfell', 'NSW', -33.990, 147.930),
('2810', 'Piney Range', 'NSW', -33.990, 147.930),
('2810', 'Pinnacle', 'NSW', -33.990, 147.930),
('2810', 'Pullabooka', 'NSW', -33.990, 147.930),
('2810', 'Warraderry', 'NSW', -33.990, 147.930),
('2820', 'Apsley', 'NSW', -32.600, 148.960),
('2820', 'Arthurville', 'NSW', -32.600, 148.960),
('2820', 'Bakers Swamp', 'NSW', -32.600, 148.960),
('2820', 'Bodangora', 'NSW', -32.600, 148.960),
('2820', 'Comobella', 'NSW', -32.600, 148.960),
('2820', 'Curra Creek', 'NSW', -32.600, 148.960),
('2820', 'Dripstone', 'NSW', -32.600, 148.960),
('2820', 'Farnham', 'NSW', -32.600, 148.960),
('2820', 'Gollan', 'NSW', -32.600, 148.960),
('2820', 'Lake Burrendong', 'NSW', -32.600, 148.960),
('2820', 'Maryvale', 'NSW', -32.600, 148.960),
('2820', 'Medway', 'NSW', -32.600, 148.960),
('2820', 'Montefiores', 'NSW', -32.600, 148.960),
('2820', 'Mookerawa', 'NSW', -32.600, 148.960),
('2820', 'Mount Aquila', 'NSW', -32.600, 148.960),
('2820', 'Mount Arthur', 'NSW', -32.600, 148.960),
('2820', 'Mumbil', 'NSW', -32.600, 148.960),
('2820', 'Nanima', 'NSW', -32.600, 148.960),
('2820', 'Neurea', 'NSW', -32.600, 148.960),
('2820', 'Spicers Creek', 'NSW', -32.600, 148.960),
('2820', 'Stuart Town', 'NSW', -32.600, 148.960),
('2820', 'Suntop', 'NSW', -32.600, 148.960),
('2820', 'Walmer', 'NSW', -32.600, 148.960),
('2820', 'Wellington', 'NSW', -32.600, 148.960),
('2820', 'Wuuluman', 'NSW', -32.600, 148.960),
('2820', 'Yarragal', 'NSW', -32.600, 148.960),
('2821', 'Burroway', 'NSW', -32.060, 148.260),
('2821', 'Narromine', 'NSW', -32.060, 148.260),
('2823', 'Bundemar', 'NSW', -31.840, 148.180),
('2823', 'Cathundral', 'NSW', -31.840, 148.180),
('2823', 'Dandaloo', 'NSW', -31.840, 148.180),
('2823', 'Gin Gin', 'NSW', -31.840, 148.180),
('2823', 'Trangie', 'NSW', -31.840, 148.180),
('2824', 'Beemunnel', 'NSW', -31.670, 147.860),
('2824', 'Eenaweena', 'NSW', -31.670, 147.860),
('2824', 'Marthaguy', 'NSW', -31.670, 147.860),
('2824', 'Mount Foster', 'NSW', -31.670, 147.860),
('2824', 'Mount Harris', 'NSW', -31.670, 147.860),
('2824', 'Mumblebone Plain', 'NSW', -31.670, 147.860),
('2824', 'Oxley', 'NSW', -31.670, 147.860),
('2824', 'Pigeonbah', 'NSW', -31.670, 147.860),
('2824', 'Ravenswood', 'NSW', -31.670, 147.860),
('2824', 'Red Hill', 'NSW', -31.670, 147.860),
('2824', 'Snakes Plain', 'NSW', -31.670, 147.860),
('2824', 'Tenandra', 'NSW', -31.670, 147.860),
('2824', 'Warren', 'NSW', -31.670, 147.860),
('2825', 'Babinda', 'NSW', -31.940, 146.480),
('2825', 'Bobadah', 'NSW', -31.940, 146.480),
('2825', 'Bogan', 'NSW', -31.940, 146.480),
('2825', 'Buddabadah', 'NSW', -31.940, 146.480),
('2825', 'Canonba', 'NSW', -31.940, 146.480),
('2825', 'Five Ways', 'NSW', -31.940, 146.480),
('2825', 'Honeybugle', 'NSW', -31.940, 146.480),
('2825', 'Miandetta', 'NSW', -31.940, 146.480),
('2825', 'Mulla', 'NSW', -31.940, 146.480),
('2825', 'Mullengudgery', 'NSW', -31.940, 146.480),
('2825', 'Murrawombie', 'NSW', -31.940, 146.480),
('2825', 'Nyngan', 'NSW', -31.940, 146.480),
('2825', 'Pangee', 'NSW', -31.940, 146.480),
('2827', 'Bearbong', 'NSW', -31.660, 148.880),
('2827', 'Biddon', 'NSW', -31.660, 148.880),
('2827', 'Breelong', 'NSW', -31.660, 148.880),
('2827', 'Collie', 'NSW', -31.660, 148.880),
('2827', 'Curban', 'NSW', -31.660, 148.880),
('2827', 'Gilgandra', 'NSW', -31.660, 148.880),
('2827', 'Merrigal', 'NSW', -31.660, 148.880),
('2828', 'Black Hollow', 'NSW', -31.110, 148.860),
('2828', 'Bourbah', 'NSW', -31.110, 148.860),
('2828', 'Gulargambone', 'NSW', -31.110, 148.860),
('2828', 'Mount Tenandra', 'NSW', -31.110, 148.860),
('2828', 'Quanda', 'NSW', -31.110, 148.860),
('2828', 'Tonderburine', 'NSW', -31.110, 148.860),
('2828', 'Warrumbungle', 'NSW', -31.110, 148.860),
('2829', 'Billeroy', 'NSW', -31.120, 148.370),
('2829', 'Combara', 'NSW', -31.120, 148.370),
('2829', 'Conimbia', 'NSW', -31.120, 148.370),
('2829', 'Coonamble', 'NSW', -31.120, 148.370),
('2829', 'Gilgooma', 'NSW', -31.120, 148.370),
('2829', 'Magometon', 'NSW', -31.120, 148.370),
('2829', 'Nebea', 'NSW', -31.120, 148.370),
('2829', 'Pine Grove', 'NSW', -31.120, 148.370),
('2829', 'Teridgerie', 'NSW', -31.120, 148.370),
('2829', 'Urawilkie', 'NSW', -31.120, 148.370),
('2829', 'Wingadee', 'NSW', -31.120, 148.370),
('2830', 'Ballimore', 'NSW', -32.200, 148.900),
('2830', 'Barbigal', 'NSW', -32.200, 148.900),
('2830', 'Beni', 'NSW', -32.200, 148.900),
('2830', 'Boothenba', 'NSW', -32.200, 148.900),
('2830', 'Brocklehurst', 'NSW', -32.200, 148.900),
('2830', 'Bruah', 'NSW', -32.200, 148.900),
('2830', 'Bunglegumbie', 'NSW', -32.200, 148.900),
('2830', 'Burrabadine', 'NSW', -32.200, 148.900),
('2830', 'Butlers Falls', 'NSW', -32.200, 148.900),
('2830', 'Coolbaggie', 'NSW', -32.200, 148.900),
('2830', 'Cumboogle', 'NSW', -32.200, 148.900),
('2830', 'Dubbo', 'NSW', -32.200, 148.900),
('2830', 'Dubbo DC', 'NSW', -32.200, 148.900),
('2830', 'Dubbo East', 'NSW', -32.200, 148.900),
('2830', 'Dubbo Grove', 'NSW', -32.200, 148.900),
('2830', 'Dubbo West', 'NSW', -32.200, 148.900),
('2830', 'Eschol', 'NSW', -32.200, 148.900),
('2830', 'Eulomogo', 'NSW', -32.200, 148.900),
('2830', 'Glengerra', 'NSW', -32.200, 148.900),
('2830', 'Goonoo Forest', 'NSW', -32.200, 148.900),
('2830', 'Jones Creek', 'NSW', -32.200, 148.900),
('2830', 'Kickabil', 'NSW', -32.200, 148.900),
('2830', 'Minore', 'NSW', -32.200, 148.900),
('2830', 'Mogriguy', 'NSW', -32.200, 148.900),
('2830', 'Mountain Creek', 'NSW', -32.200, 148.900),
('2830', 'Muronbung', 'NSW', -32.200, 148.900),
('2830', 'Murrumbidgerie', 'NSW', -32.200, 148.900),
('2830', 'Rawsonville', 'NSW', -32.200, 148.900),
('2830', 'Terramungamine', 'NSW', -32.200, 148.900),
('2830', 'Toongi', 'NSW', -32.200, 148.900),
('2830', 'Wambangalang', 'NSW', -32.200, 148.900),
('2830', 'Yarrabar', 'NSW', -32.200, 148.900),
('2831', 'Armatree', 'NSW', -31.450, 148.410),
('2831', 'Balladoran', 'NSW', -31.450, 148.410),
('2831', 'Brenda', 'NSW', -31.450, 148.410),
('2831', 'Bullagreen', 'NSW', -31.450, 148.410),
('2831', 'Byrock', 'NSW', -31.450, 148.410),
('2831', 'Carinda', 'NSW', -31.450, 148.410),
('2831', 'Coolabah', 'NSW', -31.450, 148.410),
('2831', 'Elong Elong', 'NSW', -31.450, 148.410),
('2831', 'Eumungerie', 'NSW', -31.450, 148.410),
('2831', 'Geurie', 'NSW', -31.450, 148.410),
('2831', 'Girilambone', 'NSW', -31.450, 148.410),
('2831', 'Goodooga', 'NSW', -31.450, 148.410),
('2831', 'Gungalman', 'NSW', -31.450, 148.410),
('2831', 'Hermidale', 'NSW', -31.450, 148.410),
('2831', 'Macquarie Marshes', 'NSW', -31.450, 148.410),
('2831', 'Merrygoen', 'NSW', -31.450, 148.410),
('2831', 'Neilrex', 'NSW', -31.450, 148.410),
('2831', 'Nevertire', 'NSW', -31.450, 148.410),
('2831', 'Nubingerie', 'NSW', -31.450, 148.410),
('2831', 'Nymagee', 'NSW', -31.450, 148.410),
('2831', 'Pine Clump', 'NSW', -31.450, 148.410),
('2831', 'Ponto', 'NSW', -31.450, 148.410),
('2831', 'Quambone', 'NSW', -31.450, 148.410),
('2831', 'Terrabella', 'NSW', -31.450, 148.410),
('2831', 'The Marra', 'NSW', -31.450, 148.410),
('2831', 'Tooloon', 'NSW', -31.450, 148.410),
('2831', 'Tooraweenah', 'NSW', -31.450, 148.410),
('2831', 'Westella', 'NSW', -31.450, 148.410),
('2831', 'Wongarbon', 'NSW', -31.450, 148.410),
('2832', 'Come By Chance', 'NSW', -29.110, 147.900),
('2832', 'Cryon', 'NSW', -29.110, 147.900),
('2832', 'Cumborah', 'NSW', -29.110, 147.900),
('2832', 'Walgett', 'NSW', -29.110, 147.900),
('2833', 'Collarenebri', 'NSW', -29.550, 148.580),
('2834', 'Angledool', 'NSW', -29.430, 147.980),
('2834', 'Lightning Ridge', 'NSW', -29.430, 147.980),
('2835', 'Bulla', 'NSW', -32.030, 144.440),
('2835', 'Canbelego', 'NSW', -32.030, 144.440),
('2835', 'Cobar', 'NSW', -32.030, 144.440),
('2835', 'Cubba', 'NSW', -32.030, 144.440),
('2835', 'Gilgunnia', 'NSW', -32.030, 144.440),
('2835', 'Irymple', 'NSW', -32.030, 144.440),
('2835', 'Kerrigundi', 'NSW', -32.030, 144.440),
('2835', 'Kulwin', 'NSW', -32.030, 144.440),
('2835', 'Lerida', 'NSW', -32.030, 144.440),
('2835', 'Noona', 'NSW', -32.030, 144.440),
('2835', 'Sandy Creek', 'NSW', -32.030, 144.440),
('2835', 'Tindarey', 'NSW', -32.030, 144.440),
('2836', 'White Cliffs', 'NSW', -30.810, 142.880),
('2836', 'Wilcannia', 'NSW', -30.810, 142.880),
('2839', 'Bogan', 'NSW', -30.200, 146.540),
('2839', 'Brewarrina', 'NSW', -30.200, 146.540),
('2839', 'Collerina', 'NSW', -30.200, 146.540),
('2839', 'Gongolgon', 'NSW', -30.200, 146.540),
('2839', 'Narran Lake', 'NSW', -30.200, 146.540),
('2839', 'Talawanta', 'NSW', -30.200, 146.540),
('2839', 'Weilmoringle', 'NSW', -30.200, 146.540),
('2840', 'Bourke', 'NSW', -29.190, 145.880),
('2840', 'Enngonia', 'NSW', -29.190, 145.880),
('2840', 'Fords Bridge', 'NSW', -29.190, 145.880),
('2840', 'Gumbalie', 'NSW', -29.190, 145.880),
('2840', 'Gunderbooka', 'NSW', -29.190, 145.880),
('2840', 'Hungerford', 'NSW', -29.190, 145.880),
('2840', 'Louth', 'NSW', -29.190, 145.880),
('2840', 'Tilpa', 'NSW', -29.190, 145.880),
('2840', 'Wanaaring', 'NSW', -29.190, 145.880),
('2840', 'Yantabulla', 'NSW', -29.190, 145.880),
('2842', 'Mendooran', 'NSW', -31.820, 149.120),
('2842', 'Mollyan', 'NSW', -31.820, 149.120),
('2842', 'Wattle Springs', 'NSW', -31.820, 149.120),
('2842', 'Yarragrin', 'NSW', -31.820, 149.120),
('2843', 'Coolah', 'NSW', -31.770, 149.610),
('2844', 'Birriwa', 'NSW', -32.120, 149.470),
('2844', 'Dunedoo', 'NSW', -32.120, 149.470),
('2844', 'Leadville', 'NSW', -32.120, 149.470),
('2845', 'Wallerawang', 'NSW', -33.410, 150.060),
('2846', 'Capertee', 'NSW', -33.150, 149.990),
('2846', 'Glen Davis', 'NSW', -33.150, 149.990),
('2846', 'Round Swamp', 'NSW', -33.150, 149.990),
('2847', 'Portland', 'NSW', -33.350, 149.980),
('2848', 'Brogans Creek', 'NSW', -32.970, 149.960),
('2848', 'Charbon', 'NSW', -32.970, 149.960),
('2848', 'Clandulla', 'NSW', -32.970, 149.960),
('2848', 'Kandos', 'NSW', -32.970, 149.960),
('2849', 'Bogee', 'NSW', -32.980, 150.100),
('2849', 'Breakfast Creek', 'NSW', -32.980, 150.100),
('2849', 'Budden', 'NSW', -32.980, 150.100),
('2849', 'Bylong', 'NSW', -32.980, 150.100),
('2849', 'Camboon', 'NSW', -32.980, 150.100),
('2849', 'Carwell', 'NSW', -32.980, 150.100),
('2849', 'Coggan', 'NSW', -32.980, 150.100),
('2849', 'Coxs Creek', 'NSW', -32.980, 150.100),
('2849', 'Coxs Crown', 'NSW', -32.980, 150.100),
('2849', 'Dabee', 'NSW', -32.980, 150.100),
('2849', 'Dungeree', 'NSW', -32.980, 150.100),
('2849', 'Dunville Loop', 'NSW', -32.980, 150.100),
('2849', 'Ginghi', 'NSW', -32.980, 150.100),
('2849', 'Glen Alice', 'NSW', -32.980, 150.100),
('2849', 'Growee', 'NSW', -32.980, 150.100),
('2849', 'Kelgoola', 'NSW', -32.980, 150.100),
('2849', 'Lee Creek', 'NSW', -32.980, 150.100),
('2849', 'Mount Marsden', 'NSW', -32.980, 150.100),
('2849', 'Murrumbo', 'NSW', -32.980, 150.100),
('2849', 'Nullo Mountain', 'NSW', -32.980, 150.100),
('2849', 'Olinda', 'NSW', -32.980, 150.100),
('2849', 'Pinnacle Swamp', 'NSW', -32.980, 150.100),
('2849', 'Pyangle', 'NSW', -32.980, 150.100),
('2849', 'Reedy Creek', 'NSW', -32.980, 150.100),
('2849', 'Rylstone', 'NSW', -32.980, 150.100),
('2849', 'Upper Bylong', 'NSW', -32.980, 150.100),
('2849', 'Upper Growee', 'NSW', -32.980, 150.100),
('2849', 'Upper Nile', 'NSW', -32.980, 150.100),
('2849', 'Wirraba', 'NSW', -32.980, 150.100),
('2850', 'Aarons Pass', 'NSW', -32.860, 149.800),
('2850', 'Apple Tree Flat', 'NSW', -32.860, 149.800),
('2850', 'Avisford', 'NSW', -32.860, 149.800),
('2850', 'Bara', 'NSW', -32.860, 149.800),
('2850', 'Barigan', 'NSW', -32.860, 149.800),
('2850', 'Ben Buckley', 'NSW', -32.860, 149.800),
('2850', 'Bocoble', 'NSW', -32.860, 149.800),
('2850', 'Bombira', 'NSW', -32.860, 149.800),
('2850', 'Botobolar', 'NSW', -32.860, 149.800),
('2850', 'Buckaroo', 'NSW', -32.860, 149.800),
('2850', 'Budgee Budgee', 'NSW', -32.860, 149.800),
('2850', 'Burrundulla', 'NSW', -32.860, 149.800),
('2850', 'Caerleon', 'NSW', -32.860, 149.800),
('2850', 'Canadian Lead', 'NSW', -32.860, 149.800),
('2850', 'Carcalgong', 'NSW', -32.860, 149.800),
('2850', 'Collingwood', 'NSW', -32.860, 149.800),
('2850', 'Cooks Gap', 'NSW', -32.860, 149.800),
('2850', 'Cooyal', 'NSW', -32.860, 149.800),
('2850', 'Cross Roads', 'NSW', -32.860, 149.800),
('2850', 'Cudgegong', 'NSW', -32.860, 149.800),
('2850', 'Cullenbone', 'NSW', -32.860, 149.800),
('2850', 'Cumbo', 'NSW', -32.860, 149.800),
('2850', 'Erudgere', 'NSW', -32.860, 149.800),
('2850', 'Eurunderee', 'NSW', -32.860, 149.800),
('2850', 'Frog Rock', 'NSW', -32.860, 149.800),
('2850', 'Galambine', 'NSW', -32.860, 149.800),
('2850', 'Glen Ayr', 'NSW', -32.860, 149.800),
('2850', 'Grattai', 'NSW', -32.860, 149.800),
('2850', 'Green Gully', 'NSW', -32.860, 149.800),
('2850', 'Hargraves', 'NSW', -32.860, 149.800),
('2850', 'Havilah', 'NSW', -32.860, 149.800),
('2850', 'Hayes Gap', 'NSW', -32.860, 149.800),
('2850', 'Hill End', 'NSW', -32.860, 149.800),
('2850', 'Home Rule', 'NSW', -32.860, 149.800),
('2850', 'Ilford', 'NSW', -32.860, 149.800),
('2850', 'Kains Flat', 'NSW', -32.860, 149.800),
('2850', 'Linburn', 'NSW', -32.860, 149.800),
('2850', 'Lue', 'NSW', -32.860, 149.800),
('2850', 'Maitland Bar', 'NSW', -32.860, 149.800),
('2850', 'Menah', 'NSW', -32.860, 149.800),
('2850', 'Meroo', 'NSW', -32.860, 149.800),
('2850', 'Milroy', 'NSW', -32.860, 149.800),
('2850', 'Mogo', 'NSW', -32.860, 149.800),
('2850', 'Monivae', 'NSW', -32.860, 149.800),
('2850', 'Moolarben', 'NSW', -32.860, 149.800),
('2850', 'Mount Frome', 'NSW', -32.860, 149.800),
('2850', 'Mount Knowles', 'NSW', -32.860, 149.800),
('2850', 'Mount Vincent', 'NSW', -32.860, 149.800),
('2850', 'Mudgee', 'NSW', -32.860, 149.800),
('2850', 'Mullamuddy', 'NSW', -32.860, 149.800),
('2850', 'Munghorn', 'NSW', -32.860, 149.800),
('2850', 'Piambong', 'NSW', -32.860, 149.800),
('2850', 'Putta Bucca', 'NSW', -32.860, 149.800),
('2850', 'Pyramul', 'NSW', -32.860, 149.800),
('2850', 'Queens Pinch', 'NSW', -32.860, 149.800),
('2850', 'Razorback', 'NSW', -32.860, 149.800),
('2850', 'Riverlea', 'NSW', -32.860, 149.800),
('2850', 'Running Stream', 'NSW', -32.860, 149.800),
('2850', 'Sallys Flat', 'NSW', -32.860, 149.800),
('2850', 'Spring Flat', 'NSW', -32.860, 149.800),
('2850', 'St Fillans', 'NSW', -32.860, 149.800),
('2850', 'Stony Creek', 'NSW', -32.860, 149.800),
('2850', 'Tambaroora', 'NSW', -32.860, 149.800),
('2850', 'Tichular', 'NSW', -32.860, 149.800),
('2850', 'Totnes Valley', 'NSW', -32.860, 149.800),
('2850', 'Triamble', 'NSW', -32.860, 149.800),
('2850', 'Turill', 'NSW', -32.860, 149.800),
('2850', 'Twelve Mile', 'NSW', -32.860, 149.800),
('2850', 'Ulan', 'NSW', -32.860, 149.800),
('2850', 'Ullamalla', 'NSW', -32.860, 149.800),
('2850', 'Wilbetree', 'NSW', -32.860, 149.800),
('2850', 'Wilpinjong', 'NSW', -32.860, 149.800),
('2850', 'Windeyer', 'NSW', -32.860, 149.800),
('2850', 'Wollar', 'NSW', -32.860, 149.800),
('2850', 'Worlds End', 'NSW', -32.860, 149.800),
('2850', 'Yarrabin', 'NSW', -32.860, 149.800),
('2850', 'Yarrawonga', 'NSW', -32.860, 149.800),
('2852', 'Barneys Reef', 'NSW', -32.190, 149.530),
('2852', 'Beryl', 'NSW', -32.190, 149.530),
('2852', 'Biraganbil', 'NSW', -32.190, 149.530),
('2852', 'Bungaba', 'NSW', -32.190, 149.530),
('2852', 'Cope', 'NSW', -32.190, 149.530),
('2852', 'Cumbandry', 'NSW', -32.190, 149.530),
('2852', 'Goolma', 'NSW', -32.190, 149.530),
('2852', 'Gulgong', 'NSW', -32.190, 149.530),
('2852', 'Mebul', 'NSW', -32.190, 149.530),
('2852', 'Merotherie', 'NSW', -32.190, 149.530),
('2852', 'Stubbo', 'NSW', -32.190, 149.530),
('2852', 'Tallawang', 'NSW', -32.190, 149.530),
('2864', 'Boree', 'NSW', -33.230, 148.840),
('2864', 'Bowan Park', 'NSW', -33.230, 148.840),
('2864', 'Cudal', 'NSW', -33.230, 148.840),
('2864', 'Murga', 'NSW', -33.230, 148.840),
('2864', 'Toogong', 'NSW', -33.230, 148.840),
('2865', 'Bocobra', 'NSW', -33.100, 148.540),
('2865', 'Gumble', 'NSW', -33.100, 148.540),
('2865', 'Manildra', 'NSW', -33.100, 148.540),
('2866', 'Amaroo', 'NSW', -33.180, 148.930),
('2866', 'Boomey', 'NSW', -33.180, 148.930),
('2866', 'Cundumbul', 'NSW', -33.180, 148.930),
('2866', 'Euchareena', 'NSW', -33.180, 148.930),
('2866', 'Garra', 'NSW', -33.180, 148.930),
('2866', 'Larras Lee', 'NSW', -33.180, 148.930),
('2866', 'Molong', 'NSW', -33.180, 148.930),
('2867', 'Baldry', 'NSW', -32.870, 148.500),
('2867', 'Cumnock', 'NSW', -32.870, 148.500),
('2867', 'Eurimbla', 'NSW', -32.870, 148.500),
('2867', 'Loombah', 'NSW', -32.870, 148.500),
('2867', 'Yullundry', 'NSW', -32.870, 148.500),
('2868', 'Bournewood', 'NSW', -32.750, 148.750),
('2868', 'Little River', 'NSW', -32.750, 148.750),
('2868', 'North Yeoval', 'NSW', -32.750, 148.750),
('2868', 'Obley', 'NSW', -32.750, 148.750),
('2868', 'Upper Obley', 'NSW', -32.750, 148.750),
('2868', 'Yeoval', 'NSW', -32.750, 148.750),
('2869', 'Peak Hill', 'NSW', -32.730, 148.190),
('2869', 'Tomingley', 'NSW', -32.730, 148.190),
('2869', 'Trewilga', 'NSW', -32.730, 148.190),
('2870', 'Alectown', 'NSW', -32.930, 148.260),
('2870', 'Bumberry', 'NSW', -32.930, 148.260),
('2870', 'Cookamidgera', 'NSW', -32.930, 148.260),
('2870', 'Cooks Myalls', 'NSW', -32.930, 148.260),
('2870', 'Goonumbla', 'NSW', -32.930, 148.260),
('2870', 'Mandagery', 'NSW', -32.930, 148.260),
('2870', 'Parkes', 'NSW', -32.930, 148.260),
('2870', 'Tichborne', 'NSW', -32.930, 148.260),
('2871', 'Bandon', 'NSW', -33.500, 148.310),
('2871', 'Bedgerebong', 'NSW', -33.500, 148.310),
('2871', 'Bundaburrah', 'NSW', -33.500, 148.310),
('2871', 'Calarie', 'NSW', -33.500, 148.310),
('2871', 'Carrawabbity', 'NSW', -33.500, 148.310),
('2871', 'Corinella', 'NSW', -33.500, 148.310),
('2871', 'Cumbijowa', 'NSW', -33.500, 148.310),
('2871', 'Daroobalgie', 'NSW', -33.500, 148.310),
('2871', 'Fairholme', 'NSW', -33.500, 148.310),
('2871', 'Forbes', 'NSW', -33.500, 148.310),
('2871', 'Garema', 'NSW', -33.500, 148.310),
('2871', 'Grawlin', 'NSW', -33.500, 148.310),
('2871', 'Gunning Gap', 'NSW', -33.500, 148.310),
('2871', 'Jemalong', 'NSW', -33.500, 148.310),
('2871', 'Mulyandry', 'NSW', -33.500, 148.310),
('2871', 'Ooma', 'NSW', -33.500, 148.310),
('2871', 'Warroo', 'NSW', -33.500, 148.310),
('2871', 'Weelong', 'NSW', -33.500, 148.310),
('2871', 'Wirrinya', 'NSW', -33.500, 148.310),
('2871', 'Yarragong', 'NSW', -33.500, 148.310),
('2873', 'Albert', 'NSW', -32.420, 147.510),
('2873', 'Miamley', 'NSW', -32.420, 147.510),
('2873', 'Tottenham', 'NSW', -32.420, 147.510),
('2874', 'Tullamore', 'NSW', -32.630, 147.560),
('2875', 'Bruie Plains', 'NSW', -32.780, 147.860),
('2875', 'Fifield', 'NSW', -32.780, 147.860),
('2875', 'Ootha', 'NSW', -32.780, 147.860),
('2875', 'Trundle', 'NSW', -32.780, 147.860),
('2875', 'Yarrabandai', 'NSW', -32.780, 147.860),
('2876', 'Bogan Gate', 'NSW', -33.110, 147.800),
('2876', 'Gunningbland', 'NSW', -33.110, 147.800),
('2876', 'Nelungaloo', 'NSW', -33.110, 147.800),
('2877', 'Boona Mount', 'NSW', -33.090, 147.150),
('2877', 'Condobolin', 'NSW', -33.090, 147.150),
('2877', 'Derriwong', 'NSW', -33.090, 147.150),
('2877', 'Eremerang', 'NSW', -33.090, 147.150),
('2877', 'Euabalong', 'NSW', -33.090, 147.150),
('2877', 'Euabalong West', 'NSW', -33.090, 147.150),
('2877', 'Kiacatoo', 'NSW', -33.090, 147.150),
('2877', 'Mount Hope', 'NSW', -33.090, 147.150),
('2877', 'Mulguthrie', 'NSW', -33.090, 147.150),
('2878', 'Ivanhoe', 'NSW', -32.830, 143.860),
('2878', 'Mossgiel', 'NSW', -32.830, 143.860),
('2879', 'Menindee', 'NSW', -32.260, 142.410),
('2879', 'Sunset Strip', 'NSW', -32.260, 142.410),
('2880', 'Broken Hill', 'NSW', -31.960, 141.470),
('2880', 'Broken Hill North', 'NSW', -31.960, 141.470),
('2880', 'Broken Hill West', 'NSW', -31.960, 141.470),
('2880', 'Broughams Gate', 'NSW', -31.960, 141.470),
('2880', 'Fowlers Gap', 'NSW', -31.960, 141.470),
('2880', 'Little Topar', 'NSW', -31.960, 141.470),
('2880', 'Milparinka', 'NSW', -31.960, 141.470),
('2880', 'Mutawintji', 'NSW', -31.960, 141.470),
('2880', 'Packsaddle', 'NSW', -31.960, 141.470),
('2880', 'Silverton', 'NSW', -31.960, 141.470),
('2880', 'South Broken Hill', 'NSW', -31.960, 141.470),
('2880', 'Tibooburra', 'NSW', -31.960, 141.470),
('2890', 'Australian Defence Forces', 'NSW', 0.000, 0.000),
('2891', 'Sydney Gateway Facility', 'NSW', 0.000, 0.000),
('2898', 'Lord Howe Island', 'NSW', -31.550, 159.080),
('2899', 'Norfolk Island', 'NSW', -36.080, 146.930),
('2900', 'Greenway', 'ACT', -35.420, 149.070),
('2900', 'Tuggeranong', 'ACT', -35.420, 149.070),
('2901', 'Tuggeranong DC', 'ACT', 0.000, 0.000),
('2902', 'Kambah', 'ACT', -35.380, 149.050),
('2902', 'Kambah Village', 'ACT', -35.380, 149.050),
('2903', 'Erindale Centre', 'ACT', -35.400, 149.100),
('2903', 'Oxley', 'ACT', -35.400, 149.100),
('2903', 'Wanniassa', 'ACT', -35.400, 149.100),
('2904', 'Fadden', 'ACT', -35.400, 149.120),
('2904', 'Gowrie', 'ACT', -35.400, 149.120),
('2904', 'Macarthur', 'ACT', -35.400, 149.120),
('2904', 'Monash', 'ACT', -35.400, 149.120),
('2905', 'Bonython', 'ACT', -35.430, 149.080),
('2905', 'Calwell', 'ACT', -35.430, 149.080),
('2905', 'Chisholm', 'ACT', -35.430, 149.080),
('2905', 'Gilmore', 'ACT', -35.430, 149.080),
('2905', 'Isabella Plains', 'ACT', -35.430, 149.080),
('2905', 'Richardson', 'ACT', -35.430, 149.080),
('2905', 'Theodore', 'ACT', -35.430, 149.080),
('2906', 'Banks', 'ACT', -35.520, 149.080),
('2906', 'Conder', 'ACT', -35.520, 149.080),
('2906', 'Gordon', 'ACT', -35.520, 149.080),
('2911', 'Crace', 'ACT', -35.220, 149.120),
('2911', 'Mitchell', 'ACT', -35.220, 149.120),
('2912', 'Gungahlin', 'ACT', -35.520, 149.080),
('2913', 'Casey', 'ACT', -35.200, 149.130),
('2913', 'Franklin', 'ACT', -35.200, 149.130),
('2913', 'Ginninderra Village', 'ACT', -35.200, 149.130),
('2913', 'Kinlyside', 'ACT', -35.200, 149.130),
('2913', 'Ngunnawal', 'ACT', -35.200, 149.130),
('2913', 'Nicholls', 'ACT', -35.200, 149.130),
('2913', 'Palmerston', 'ACT', -35.200, 149.130),
('2913', 'Taylor', 'ACT', -35.200, 149.130),
('2914', 'Amaroo', 'ACT', -35.170, 149.130),
('2914', 'Bonner', 'ACT', -35.170, 149.130),
('2914', 'Forde', 'ACT', -35.170, 149.130),
('2914', 'Harrison', 'ACT', -35.170, 149.130),
('2914', 'Moncrieff', 'ACT', -35.170, 149.130),
('3000', 'Melbourne', 'VIC', -37.810, 144.970),
('3001', 'Melbourne', 'VIC', -38.370, 144.770),
('3002', 'East Melbourne', 'VIC', -37.820, 144.990),
('3003', 'West Melbourne', 'VIC', -37.810, 144.940),
('3004', 'Melbourne', 'VIC', -37.840, 144.980),
('3004', 'St Kilda Road Central', 'VIC', -37.840, 144.980),
('3005', 'World Trade Centre', 'VIC', -37.820, 144.950),
('3006', 'South Wharf', 'VIC', -37.820, 144.970),
('3006', 'Southbank', 'VIC', -37.820, 144.970),
('3008', 'Docklands', 'VIC', -37.810, 144.950),
('3010', 'University Of Melbourne', 'VIC', -37.800, 144.960),
('3011', 'Footscray', 'VIC', -37.800, 144.900),
('3011', 'Seddon', 'VIC', -37.800, 144.900),
('3011', 'Seddon West', 'VIC', -37.800, 144.900),
('3012', 'Brooklyn', 'VIC', -37.810, 144.850),
('3012', 'Kingsville', 'VIC', -37.810, 144.850),
('3012', 'Kingsville West', 'VIC', -37.810, 144.850),
('3012', 'Maidstone', 'VIC', -37.810, 144.850),
('3012', 'Tottenham', 'VIC', -37.810, 144.850),
('3012', 'West Footscray', 'VIC', -37.810, 144.850),
('3013', 'Yarraville', 'VIC', -37.820, 144.890),
('3013', 'Yarraville West', 'VIC', -37.820, 144.890),
('3015', 'Newport', 'VIC', -37.840, 144.880),
('3015', 'South Kingsville', 'VIC', -37.840, 144.880),
('3015', 'Spotswood', 'VIC', -37.840, 144.880),
('3016', 'Williamstown', 'VIC', -37.860, 144.900),
('3016', 'Williamstown North', 'VIC', -37.860, 144.900),
('3018', 'Altona', 'VIC', -37.870, 144.830),
('3018', 'Seaholme', 'VIC', -37.870, 144.830),
('3019', 'Braybrook', 'VIC', -37.780, 144.860),
('3019', 'Braybrook North', 'VIC', -37.780, 144.860),
('3019', 'Robinson', 'VIC', -37.780, 144.860),
('3020', 'Albion', 'VIC', -37.780, 144.820),
('3020', 'Glengala', 'VIC', -37.780, 144.820),
('3020', 'Sunshine', 'VIC', -37.780, 144.820),
('3020', 'Sunshine North', 'VIC', -37.780, 144.820),
('3020', 'Sunshine West', 'VIC', -37.780, 144.820),
('3021', 'Albanvale', 'VIC', -37.750, 144.770),
('3021', 'Kealba', 'VIC', -37.750, 144.770),
('3021', 'Kings Park', 'VIC', -37.750, 144.770),
('3021', 'St Albans', 'VIC', -37.750, 144.770),
('3022', 'Ardeer', 'VIC', -37.780, 144.800),
('3022', 'Deer Park East', 'VIC', -37.780, 144.800),
('3023', 'Burnside', 'VIC', -37.750, 144.750),
('3023', 'Burnside Heights', 'VIC', -37.750, 144.750),
('3023', 'Cairnlea', 'VIC', -37.750, 144.750),
('3023', 'Caroline Springs', 'VIC', -37.750, 144.750),
('3023', 'Deer Park', 'VIC', -37.750, 144.750),
('3023', 'Deer Park North', 'VIC', -37.750, 144.750),
('3023', 'Ravenhall', 'VIC', -37.750, 144.750),
('3024', 'Mambourin', 'VIC', -37.900, 144.560),
('3024', 'Mount Cottrell', 'VIC', -37.900, 144.560),
('3024', 'Wyndham Vale', 'VIC', -37.900, 144.560),
('3025', 'Altona East', 'VIC', -37.840, 144.860),
('3025', 'Altona Gate', 'VIC', -37.840, 144.860),
('3025', 'Altona North', 'VIC', -37.840, 144.860),
('3026', 'Laverton North', 'VIC', -37.840, 144.800),
('3027', 'Williams Landing', 'VIC', -37.860, 144.760),
('3028', 'Altona Meadows', 'VIC', -37.870, 144.780),
('3028', 'Laverton', 'VIC', -37.870, 144.780),
('3028', 'Seabrook', 'VIC', -37.870, 144.780),
('3029', 'Hoppers Crossing', 'VIC', -37.880, 144.700),
('3029', 'Tarneit', 'VIC', -37.880, 144.700),
('3029', 'Truganina', 'VIC', -37.880, 144.700),
('3030', 'Cocoroc', 'VIC', -37.970, 144.580),
('3030', 'Derrimut', 'VIC', -37.970, 144.580),
('3030', 'Point Cook', 'VIC', -37.970, 144.580),
('3030', 'Quandong', 'VIC', -37.970, 144.580),
('3030', 'Werribee', 'VIC', -37.970, 144.580),
('3030', 'Werribee South', 'VIC', -37.970, 144.580),
('3031', 'Flemington', 'VIC', -37.790, 144.930),
('3031', 'Kensington', 'VIC', -37.790, 144.930),
('3032', 'Ascot Vale', 'VIC', -37.780, 144.920),
('3032', 'Highpoint City', 'VIC', -37.780, 144.920),
('3032', 'Maribyrnong', 'VIC', -37.780, 144.920),
('3032', 'Travancore', 'VIC', -37.780, 144.920),
('3033', 'Keilor East', 'VIC', -37.740, 144.800),
('3034', 'Avondale Heights', 'VIC', -37.740, 144.820),
('3036', 'Keilor', 'VIC', -37.720, 144.830),
('3036', 'Keilor North', 'VIC', -37.720, 144.830),
('3037', 'Calder Park', 'VIC', -37.720, 144.780),
('3037', 'Delahey', 'VIC', -37.720, 144.780),
('3037', 'Hillside', 'VIC', -37.720, 144.780),
('3037', 'Sydenham', 'VIC', -37.720, 144.780),
('3037', 'Taylors Hill', 'VIC', -37.720, 144.780),
('3038', 'Keilor Downs', 'VIC', -37.730, 144.810),
('3038', 'Keilor Lodge', 'VIC', -37.730, 144.810),
('3038', 'Taylors Lakes', 'VIC', -37.730, 144.810),
('3038', 'Watergardens', 'VIC', -37.730, 144.810),
('3039', 'Moonee Ponds', 'VIC', -37.770, 144.920),
('3040', 'Aberfeldie', 'VIC', -37.760, 144.900),
('3040', 'Essendon', 'VIC', -37.760, 144.900),
('3040', 'Essendon West', 'VIC', -37.760, 144.900),
('3041', 'Essendon Fields', 'VIC', -37.740, 144.910),
('3041', 'Essendon North', 'VIC', -37.740, 144.910),
('3041', 'Strathmore', 'VIC', -37.740, 144.910),
('3041', 'Strathmore Heights', 'VIC', -37.740, 144.910),
('3042', 'Airport West', 'VIC', -37.710, 144.890),
('3042', 'Keilor Park', 'VIC', -37.710, 144.890),
('3042', 'Niddrie', 'VIC', -37.710, 144.890),
('3043', 'Gladstone Park', 'VIC', -37.690, 144.880),
('3043', 'Gowanbrae', 'VIC', -37.690, 144.880),
('3043', 'Tullamarine', 'VIC', -37.690, 144.880),
('3044', 'Pascoe Vale', 'VIC', -37.730, 144.940),
('3044', 'Pascoe Vale South', 'VIC', -37.730, 144.940),
('3045', 'Melbourne Airport', 'VIC', -37.670, 144.830),
('3046', 'Glenroy', 'VIC', -37.700, 144.920),
('3046', 'Hadfield', 'VIC', -37.700, 144.920),
('3046', 'Oak Park', 'VIC', -37.700, 144.920),
('3047', 'Broadmeadows', 'VIC', -37.680, 144.920),
('3047', 'Dallas', 'VIC', -37.680, 144.920),
('3047', 'Jacana', 'VIC', -37.680, 144.920),
('3048', 'Coolaroo', 'VIC', -37.650, 144.930),
('3048', 'Meadow Heights', 'VIC', -37.650, 144.930),
('3049', 'Attwood', 'VIC', -37.670, 144.890),
('3049', 'Westmeadows', 'VIC', -37.670, 144.890),
('3050', 'Royal Melbourne Hospital', 'VIC', -37.800, 144.960),
('3051', 'Hotham Hill', 'VIC', -37.910, 145.060),
('3051', 'North Melbourne', 'VIC', -37.910, 145.060),
('3052', 'Melbourne University', 'VIC', -37.800, 144.960),
('3052', 'Parkville', 'VIC', -37.800, 144.960),
('3053', 'Carlton', 'VIC', -37.780, 144.970),
('3053', 'Carlton South', 'VIC', -37.780, 144.970),
('3054', 'Carlton North', 'VIC', -37.780, 144.970),
('3054', 'Princes Hill', 'VIC', -37.780, 144.970),
('3055', 'Brunswick South', 'VIC', -37.770, 144.940),
('3055', 'Brunswick West', 'VIC', -37.770, 144.940),
('3055', 'Moonee Vale', 'VIC', -37.770, 144.940),
('3055', 'Moreland West', 'VIC', -37.770, 144.940),
('3056', 'Brunswick', 'VIC', -37.760, 144.940),
('3056', 'Brunswick Lower', 'VIC', -37.760, 144.940),
('3056', 'Brunswick North', 'VIC', -37.760, 144.940),
('3057', 'Brunswick East', 'VIC', -37.760, 144.980),
('3057', 'Lygon Street North', 'VIC', -37.760, 144.980),
('3057', 'Sumner', 'VIC', -37.760, 144.980),
('3058', 'Batman', 'VIC', -37.730, 144.960),
('3058', 'Coburg', 'VIC', -37.730, 144.960),
('3058', 'Coburg North', 'VIC', -37.730, 144.960),
('3058', 'Merlynston', 'VIC', -37.730, 144.960),
('3058', 'Moreland', 'VIC', -37.730, 144.960),
('3059', 'Greenvale', 'VIC', -37.640, 144.890),
('3060', 'Fawkner', 'VIC', -37.760, 144.900),
('3060', 'Fawkner East', 'VIC', -37.760, 144.900),
('3060', 'Fawkner North', 'VIC', -37.760, 144.900),
('3061', 'Campbellfield', 'VIC', -37.640, 144.950),
('3062', 'Somerton', 'VIC', -37.640, 144.940),
('3063', 'Oaklands Junction', 'VIC', -37.630, 144.840),
('3063', 'Yuroke', 'VIC', -37.630, 144.840),
('3064', 'Craigieburn', 'VIC', -37.600, 144.940),
('3064', 'Donnybrook', 'VIC', -37.600, 144.940),
('3064', 'Kalkallo', 'VIC', -37.600, 144.940),
('3064', 'Mickleham', 'VIC', -37.600, 144.940),
('3064', 'Roxburgh Park', 'VIC', -37.600, 144.940),
('3065', 'Fitzroy', 'VIC', -37.800, 144.980),
('3066', 'Collingwood', 'VIC', -37.800, 144.980),
('3066', 'Collingwood North', 'VIC', -37.800, 144.980),
('3067', 'Abbotsford', 'VIC', -37.800, 145.000),
('3068', 'Clifton Hill', 'VIC', -37.790, 144.990),
('3068', 'Fitzroy North', 'VIC', -37.790, 144.990),
('3070', 'Northcote', 'VIC', -37.770, 145.000),
('3070', 'Northcote South', 'VIC', -37.770, 145.000),
('3071', 'Thornbury', 'VIC', -37.760, 145.000),
('3072', 'Gilberton', 'VIC', -37.740, 145.030),
('3072', 'Northland Centre', 'VIC', -37.740, 145.030),
('3072', 'Preston', 'VIC', -37.740, 145.030),
('3072', 'Preston Lower', 'VIC', -37.740, 145.030),
('3072', 'Preston South', 'VIC', -37.740, 145.030),
('3072', 'Preston West', 'VIC', -37.740, 145.030),
('3072', 'Regent West', 'VIC', -37.740, 145.030),
('3073', 'Keon Park', 'VIC', -37.690, 145.010),
('3073', 'Reservoir', 'VIC', -37.690, 145.010),
('3073', 'Reservoir East', 'VIC', -37.690, 145.010),
('3073', 'Reservoir North', 'VIC', -37.690, 145.010),
('3073', 'Reservoir South', 'VIC', -37.690, 145.010),
('3074', 'Thomastown', 'VIC', -37.680, 145.010),
('3075', 'Lalor', 'VIC', -37.670, 145.020),
('3075', 'Lalor Plaza', 'VIC', -37.670, 145.020),
('3076', 'Epping', 'VIC', -37.640, 145.010),
('3076', 'Epping DC', 'VIC', -37.640, 145.010),
('3078', 'Alphington', 'VIC', -37.780, 145.030),
('3078', 'Fairfield', 'VIC', -37.780, 145.030),
('3079', 'Ivanhoe', 'VIC', -37.770, 145.040),
('3079', 'Ivanhoe East', 'VIC', -37.770, 145.040),
('3079', 'Ivanhoe North', 'VIC', -37.770, 145.040),
('3081', 'Bellfield', 'VIC', -37.750, 145.050),
('3081', 'Heidelberg Heights', 'VIC', -37.750, 145.050),
('3081', 'Heidelberg Rgh', 'VIC', -37.750, 145.050),
('3081', 'Heidelberg West', 'VIC', -37.750, 145.050),
('3082', 'Mill Park', 'VIC', -37.670, 145.060),
('3083', 'Bundoora', 'VIC', -37.700, 145.070),
('3083', 'Kingsbury', 'VIC', -37.700, 145.070),
('3083', 'La Trobe University', 'VIC', -37.700, 145.070),
('3084', 'Banyule', 'VIC', -37.740, 145.090),
('3084', 'Eaglemont', 'VIC', -37.740, 145.090),
('3084', 'Heidelberg', 'VIC', -37.740, 145.090),
('3084', 'Rosanna', 'VIC', -37.740, 145.090),
('3084', 'Viewbank', 'VIC', -37.740, 145.090),
('3085', 'Macleod', 'VIC', -37.730, 145.070),
('3085', 'Macleod West', 'VIC', -37.730, 145.070),
('3085', 'Yallambie', 'VIC', -37.730, 145.070),
('3086', 'La Trobe University', 'VIC', -37.720, 145.050),
('3087', 'Watsonia', 'VIC', -37.710, 145.080),
('3087', 'Watsonia North', 'VIC', -37.710, 145.080),
('3088', 'Briar Hill', 'VIC', -37.710, 145.120),
('3088', 'Greensborough', 'VIC', -37.710, 145.120),
('3088', 'St Helena', 'VIC', -37.710, 145.120),
('3089', 'Diamond Creek', 'VIC', -37.640, 145.220),
('3090', 'Plenty', 'VIC', -37.670, 145.120),
('3091', 'Yarrambat', 'VIC', -37.640, 145.130),
('3093', 'Lower Plenty', 'VIC', -37.730, 145.090),
('3094', 'Montmorency', 'VIC', -37.720, 145.120),
('3095', 'Eltham', 'VIC', -37.710, 145.150),
('3095', 'Eltham North', 'VIC', -37.710, 145.150),
('3095', 'Research', 'VIC', -37.710, 145.150),
('3096', 'Wattle Glen', 'VIC', -37.670, 145.190),
('3097', 'Bend Of Islands', 'VIC', -37.700, 145.280),
('3097', 'Kangaroo Ground', 'VIC', -37.700, 145.280),
('3097', 'Watsons Creek', 'VIC', -37.700, 145.280),
('3099', 'Arthurs Creek', 'VIC', -37.590, 145.220),
('3099', 'Cottles Bridge', 'VIC', -37.590, 145.220),
('3099', 'Hurstbridge', 'VIC', -37.590, 145.220),
('3099', 'Nutfield', 'VIC', -37.590, 145.220),
('3099', 'Strathewen', 'VIC', -37.590, 145.220),
('3101', 'Cotham', 'VIC', -37.810, 145.040),
('3101', 'Kew', 'VIC', -37.810, 145.040),
('3102', 'Kew East', 'VIC', -37.800, 145.050),
('3103', 'Balwyn', 'VIC', -37.810, 145.080),
('3103', 'Balwyn East', 'VIC', -37.810, 145.080),
('3104', 'Balwyn North', 'VIC', -37.790, 145.070),
('3104', 'Greythorn', 'VIC', -37.790, 145.070),
('3105', 'Bulleen', 'VIC', -37.770, 145.080),
('3105', 'Bulleen South', 'VIC', -37.770, 145.080),
('3106', 'Templestowe', 'VIC', -37.770, 145.120),
('3107', 'Templestowe Lower', 'VIC', -37.760, 145.100),
('3108', 'Doncaster', 'VIC', -37.780, 145.120),
('3109', 'Doncaster East', 'VIC', -37.810, 145.190),
('3109', 'Doncaster Heights', 'VIC', -37.810, 145.190),
('3109', 'The Pines', 'VIC', -37.810, 145.190),
('3109', 'Tunstall Square Po', 'VIC', -37.810, 145.190),
('3110', 'Nunawading BC', 'VIC', 0.000, 0.000),
('3111', 'Donvale', 'VIC', -38.180, 144.470),
('3113', 'North Warrandyte', 'VIC', -37.730, 145.220),
('3113', 'Warrandyte', 'VIC', -37.730, 145.220),
('3114', 'Park Orchards', 'VIC', -37.780, 145.210),
('3115', 'Wonga Park', 'VIC', -37.740, 145.270),
('3116', 'Chirnside Park', 'VIC', -37.750, 145.330),
('3121', 'Burnley', 'VIC', -37.830, 145.010),
('3121', 'Burnley North', 'VIC', -37.830, 145.010),
('3121', 'Cremorne', 'VIC', -37.830, 145.010),
('3121', 'Richmond', 'VIC', -37.830, 145.010),
('3121', 'Richmond East', 'VIC', -37.830, 145.010),
('3121', 'Richmond North', 'VIC', -37.830, 145.010),
('3121', 'Richmond South', 'VIC', -37.830, 145.010),
('3122', 'Auburn South', 'VIC', -37.840, 145.050),
('3122', 'Glenferrie South', 'VIC', -37.840, 145.050),
('3122', 'Hawthorn', 'VIC', -37.840, 145.050),
('3122', 'Hawthorn North', 'VIC', -37.840, 145.050),
('3122', 'Hawthorn West', 'VIC', -37.840, 145.050),
('3123', 'Auburn', 'VIC', -37.830, 145.040),
('3123', 'Hawthorn East', 'VIC', -37.830, 145.040),
('3124', 'Camberwell', 'VIC', -37.820, 145.060),
('3124', 'Camberwell North', 'VIC', -37.820, 145.060),
('3124', 'Camberwell South', 'VIC', -37.820, 145.060),
('3124', 'Camberwell West', 'VIC', -37.820, 145.060),
('3124', 'Hartwell', 'VIC', -37.820, 145.060),
('3124', 'Middle Camberwell', 'VIC', -37.820, 145.060),
('3125', 'Bennettswood', 'VIC', -37.840, 145.120),
('3125', 'Burwood', 'VIC', -37.840, 145.120),
('3125', 'Surrey Hills South', 'VIC', -37.840, 145.120),
('3126', 'Camberwell East', 'VIC', -37.840, 145.090),
('3126', 'Canterbury', 'VIC', -37.840, 145.090),
('3127', 'Mont Albert', 'VIC', -37.820, 145.100),
('3127', 'Surrey Hills', 'VIC', -37.820, 145.100),
('3127', 'Surrey Hills North', 'VIC', -37.820, 145.100),
('3128', 'Box Hill', 'VIC', -37.820, 145.120),
('3128', 'Box Hill Central', 'VIC', -37.820, 145.120),
('3128', 'Box Hill South', 'VIC', -37.820, 145.120),
('3128', 'Houston', 'VIC', -37.820, 145.120),
('3128', 'Wattle Park', 'VIC', -37.820, 145.120),
('3129', 'Box Hill North', 'VIC', -37.800, 145.130),
('3129', 'Kerrimuir', 'VIC', -37.800, 145.130),
('3129', 'Mont Albert North', 'VIC', -37.800, 145.130),
('3130', 'Blackburn', 'VIC', -37.820, 145.150),
('3130', 'Blackburn North', 'VIC', -37.820, 145.150),
('3130', 'Blackburn South', 'VIC', -37.820, 145.150),
('3130', 'Laburnum', 'VIC', -37.820, 145.150),
('3131', 'Brentford Square', 'VIC', -37.840, 145.180),
('3131', 'Forest Hill', 'VIC', -37.840, 145.180),
('3131', 'Nunawading', 'VIC', -37.840, 145.180),
('3132', 'Mitcham', 'VIC', -37.820, 145.190),
('3132', 'Mitcham North', 'VIC', -37.820, 145.190),
('3132', 'Rangeview', 'VIC', -37.820, 145.190),
('3133', 'Vermont', 'VIC', -37.840, 145.190),
('3133', 'Vermont South', 'VIC', -37.840, 145.190),
('3134', 'Heathwood', 'VIC', -37.810, 145.230),
('3134', 'Ringwood', 'VIC', -37.810, 145.230),
('3134', 'Ringwood North', 'VIC', -37.810, 145.230),
('3134', 'Warrandyte South', 'VIC', -37.810, 145.230),
('3134', 'Warranwood', 'VIC', -37.810, 145.230),
('3135', 'Bedford Road', 'VIC', -37.820, 145.250),
('3135', 'Heathmont', 'VIC', -37.820, 145.250),
('3135', 'Ringwood East', 'VIC', -37.820, 145.250),
('3136', 'Croydon', 'VIC', -37.800, 145.280),
('3136', 'Croydon Hills', 'VIC', -37.800, 145.280),
('3136', 'Croydon North', 'VIC', -37.800, 145.280),
('3136', 'Croydon South', 'VIC', -37.800, 145.280),
('3137', 'Kilsyth', 'VIC', -37.800, 145.310),
('3137', 'Kilsyth South', 'VIC', -37.800, 145.310),
('3138', 'Mooroolbark', 'VIC', -37.770, 145.330),
('3139', 'Beenak', 'VIC', -37.800, 145.540),
('3139', 'Don Valley', 'VIC', -37.800, 145.540),
('3139', 'Hoddles Creek', 'VIC', -37.800, 145.540),
('3139', 'Launching Place', 'VIC', -37.800, 145.540),
('3139', 'Seville', 'VIC', -37.800, 145.540),
('3139', 'Seville East', 'VIC', -37.800, 145.540),
('3139', 'Wandin East', 'VIC', -37.800, 145.540),
('3139', 'Wandin North', 'VIC', -37.800, 145.540),
('3139', 'Woori Yallock', 'VIC', -37.800, 145.540),
('3139', 'Yellingbo', 'VIC', -37.800, 145.540),
('3140', 'Lilydale', 'VIC', -37.760, 145.350),
('3141', 'Chapel Street North', 'VIC', -36.990, 144.060),
('3141', 'Domain Road Po', 'VIC', -36.990, 144.060),
('3141', 'South Yarra', 'VIC', -36.990, 144.060),
('3142', 'Hawksburn', 'VIC', -37.840, 145.000),
('3142', 'Toorak', 'VIC', -37.840, 145.000),
('3143', 'Armadale', 'VIC', -37.860, 145.020),
('3143', 'Armadale North', 'VIC', -37.860, 145.020),
('3144', 'Kooyong', 'VIC', -37.840, 145.030),
('3144', 'Malvern', 'VIC', -37.840, 145.030),
('3144', 'Malvern North', 'VIC', -37.840, 145.030),
('3145', 'Caulfield East', 'VIC', -37.880, 145.040),
('3145', 'Central Park', 'VIC', -37.880, 145.040),
('3145', 'Darling', 'VIC', -37.880, 145.040),
('3145', 'Darling South', 'VIC', -37.880, 145.040),
('3145', 'Malvern East', 'VIC', -37.880, 145.040),
('3145', 'Wattletree Road Po', 'VIC', -37.880, 145.040),
('3146', 'Glen Iris', 'VIC', -37.850, 145.070),
('3147', 'Ashburton', 'VIC', -37.860, 145.080),
('3147', 'Ashwood', 'VIC', -37.860, 145.080),
('3148', 'Chadstone', 'VIC', -37.890, 145.080),
('3148', 'Chadstone Centre', 'VIC', -37.890, 145.080),
('3148', 'Holmesglen', 'VIC', -37.890, 145.080),
('3148', 'Jordanville', 'VIC', -37.890, 145.080),
('3149', 'Mount Waverley', 'VIC', -37.880, 145.130),
('3149', 'Pinewood', 'VIC', -37.880, 145.130),
('3149', 'Syndal', 'VIC', -37.880, 145.130),
('3150', 'Brandon Park', 'VIC', -37.880, 145.170),
('3150', 'Glen Waverley', 'VIC', -37.880, 145.170),
('3150', 'Wheelers Hill', 'VIC', -37.880, 145.170),
('3151', 'Burwood East', 'VIC', -37.860, 145.140),
('3151', 'Burwood Heights', 'VIC', -37.860, 145.140),
('3152', 'Knox City Centre', 'VIC', -37.870, 145.240),
('3152', 'Studfield', 'VIC', -37.870, 145.240),
('3152', 'Wantirna', 'VIC', -37.870, 145.240),
('3152', 'Wantirna South', 'VIC', -37.870, 145.240),
('3153', 'Bayswater', 'VIC', -37.840, 145.270),
('3153', 'Bayswater North', 'VIC', -37.840, 145.270),
('3154', 'The Basin', 'VIC', -37.850, 145.310),
('3155', 'Boronia', 'VIC', -37.860, 145.280),
('3156', 'Ferntree Gully', 'VIC', -37.880, 145.300),
('3156', 'Lysterfield', 'VIC', -37.880, 145.300),
('3156', 'Lysterfield South', 'VIC', -37.880, 145.300),
('3156', 'Mountain Gate', 'VIC', -37.880, 145.300),
('3156', 'Upper Ferntree Gully', 'VIC', -37.880, 145.300),
('3158', 'Upwey', 'VIC', -37.900, 145.330),
('3159', 'Menzies Creek', 'VIC', -37.920, 145.400),
('3159', 'Selby', 'VIC', -37.920, 145.400),
('3160', 'Belgrave', 'VIC', -37.910, 145.360),
('3160', 'Belgrave Heights', 'VIC', -37.910, 145.360),
('3160', 'Belgrave South', 'VIC', -37.910, 145.360),
('3160', 'Tecoma', 'VIC', -37.910, 145.360),
('3161', 'Caulfield Junction', 'VIC', -38.030, 145.310),
('3161', 'Caulfield North', 'VIC', -38.030, 145.310),
('3162', 'Caulfield', 'VIC', -37.880, 145.030),
('3162', 'Caulfield South', 'VIC', -37.880, 145.030),
('3162', 'Hopetoun Gardens', 'VIC', -37.880, 145.030),
('3163', 'Booran Road Po', 'VIC', -37.890, 145.060),
('3163', 'Carnegie', 'VIC', -37.890, 145.060),
('3163', 'Glen Huntly', 'VIC', -37.890, 145.060),
('3163', 'Murrumbeena', 'VIC', -37.890, 145.060),
('3164', 'Dandenong South', 'VIC', -38.020, 145.240),
('3165', 'Bentleigh East', 'VIC', -37.930, 145.060),
('3165', 'Coatesville', 'VIC', -37.930, 145.060),
('3166', 'Hughesdale', 'VIC', -37.900, 145.080),
('3166', 'Huntingdale', 'VIC', -37.900, 145.080),
('3166', 'Oakleigh', 'VIC', -37.900, 145.080),
('3166', 'Oakleigh East', 'VIC', -37.900, 145.080),
('3167', 'Oakleigh South', 'VIC', -37.930, 145.100),
('3168', 'Clayton', 'VIC', -37.930, 145.120),
('3168', 'Notting Hill', 'VIC', -37.930, 145.120),
('3169', 'Clarinda', 'VIC', -37.940, 145.100),
('3169', 'Clayton South', 'VIC', -37.940, 145.100),
('3170', 'Mulgrave', 'VIC', -37.870, 145.100),
('3170', 'Waverley Gardens', 'VIC', -37.870, 145.100),
('3171', 'Sandown Village', 'VIC', -37.950, 145.160),
('3171', 'Springvale', 'VIC', -37.950, 145.160),
('3172', 'Dingley Village', 'VIC', -37.970, 145.120),
('3172', 'Springvale South', 'VIC', -37.970, 145.120),
('3173', 'Keysborough', 'VIC', -37.990, 145.150),
('3174', 'Noble Park', 'VIC', -37.970, 145.180),
('3174', 'Noble Park East', 'VIC', -37.970, 145.180),
('3174', 'Noble Park North', 'VIC', -37.970, 145.180),
('3175', 'Bangholme', 'VIC', -38.030, 145.180),
('3175', 'Dandenong', 'VIC', -38.030, 145.180),
('3175', 'Dandenong East', 'VIC', -38.030, 145.180),
('3175', 'Dandenong North', 'VIC', -38.030, 145.180),
('3175', 'Dandenong Plaza', 'VIC', -38.030, 145.180),
('3175', 'Dandenong South', 'VIC', -38.030, 145.180),
('3175', 'Dunearn', 'VIC', -38.030, 145.180),
('3176', 'Scoresby BC', 'VIC', 0.000, 0.000),
('3177', 'Doveton', 'VIC', -38.000, 145.240),
('3177', 'Eumemmerring', 'VIC', -38.000, 145.240),
('3178', 'Rowville', 'VIC', -37.930, 145.240),
('3179', 'Scoresby', 'VIC', -37.860, 145.260),
('3180', 'Knoxfield', 'VIC', -37.890, 145.250),
('3181', 'Prahran', 'VIC', -37.850, 144.990),
('3181', 'Prahran East', 'VIC', -37.850, 144.990),
('3181', 'Windsor', 'VIC', -37.850, 144.990),
('3182', 'St Kilda', 'VIC', -37.870, 144.980),
('3182', 'St Kilda South', 'VIC', -37.870, 144.980),
('3182', 'St Kilda West', 'VIC', -37.870, 144.980),
('3183', 'Balaclava', 'VIC', -37.870, 145.000),
('3183', 'St Kilda East', 'VIC', -37.870, 145.000),
('3184', 'Brighton Road', 'VIC', -37.880, 145.000),
('3184', 'Elwood', 'VIC', -37.880, 145.000),
('3185', 'Elsternwick', 'VIC', -37.880, 145.000),
('3185', 'Gardenvale', 'VIC', -37.880, 145.000),
('3185', 'Ripponlea', 'VIC', -37.880, 145.000),
('3186', 'Brighton', 'VIC', -37.910, 144.990),
('3186', 'Brighton North', 'VIC', -37.910, 144.990),
('3186', 'Dendy', 'VIC', -37.910, 144.990),
('3186', 'Were Street Po', 'VIC', -37.910, 144.990),
('3187', 'Brighton East', 'VIC', -37.900, 145.000),
('3187', 'North Road', 'VIC', -37.900, 145.000),
('3188', 'Hampton', 'VIC', -37.930, 145.030),
('3188', 'Hampton East', 'VIC', -37.930, 145.030),
('3188', 'Hampton North', 'VIC', -37.930, 145.030),
('3189', 'Moorabbin', 'VIC', -37.930, 145.040),
('3189', 'Moorabbin East', 'VIC', -37.930, 145.040),
('3189', 'Wishart', 'VIC', -37.930, 145.040),
('3190', 'Highett', 'VIC', -37.950, 145.030),
('3191', 'Sandringham', 'VIC', -37.950, 145.010),
('3192', 'Cheltenham', 'VIC', -37.960, 145.060),
('3192', 'Cheltenham East', 'VIC', -37.960, 145.060),
('3192', 'Cheltenham North', 'VIC', -37.960, 145.060),
('3192', 'Southland Centre', 'VIC', -37.960, 145.060),
('3193', 'Beaumaris', 'VIC', -37.990, 145.030),
('3193', 'Black Rock', 'VIC', -37.990, 145.030),
('3193', 'Black Rock North', 'VIC', -37.990, 145.030),
('3193', 'Cromer', 'VIC', -37.990, 145.030),
('3194', 'Mentone', 'VIC', -37.980, 145.060),
('3194', 'Mentone East', 'VIC', -37.980, 145.060),
('3194', 'Moorabbin Airport', 'VIC', -37.980, 145.060),
('3195', 'Aspendale', 'VIC', -38.030, 145.100),
('3195', 'Aspendale Gardens', 'VIC', -38.030, 145.100),
('3195', 'Braeside', 'VIC', -38.030, 145.100),
('3195', 'Mordialloc', 'VIC', -38.030, 145.100),
('3195', 'Mordialloc North', 'VIC', -38.030, 145.100),
('3195', 'Parkdale', 'VIC', -38.030, 145.100),
('3195', 'Waterways', 'VIC', -38.030, 145.100),
('3196', 'Bonbeach', 'VIC', -38.060, 145.120),
('3196', 'Chelsea', 'VIC', -38.060, 145.120),
('3196', 'Chelsea Heights', 'VIC', -38.060, 145.120),
('3196', 'Edithvale', 'VIC', -38.060, 145.120),
('3197', 'Carrum', 'VIC', -38.080, 145.120),
('3197', 'Patterson Lakes', 'VIC', -38.080, 145.120),
('3198', 'Belvedere Park', 'VIC', -38.110, 145.160),
('3198', 'Seaford', 'VIC', -38.110, 145.160),
('3199', 'Frankston', 'VIC', -38.150, 145.120),
('3199', 'Frankston East', 'VIC', -38.150, 145.120),
('3199', 'Frankston Heights', 'VIC', -38.150, 145.120),
('3199', 'Frankston South', 'VIC', -38.150, 145.120),
('3199', 'Karingal', 'VIC', -38.150, 145.120),
('3199', 'Karingal Centre', 'VIC', -38.150, 145.120),
('3200', 'Frankston North', 'VIC', -38.170, 145.190),
('3200', 'Pines Forest', 'VIC', -38.170, 145.190),
('3201', 'Carrum Downs', 'VIC', -38.090, 145.190),
('3202', 'Heatherton', 'VIC', -37.970, 145.210),
('3204', 'Bentleigh', 'VIC', -37.920, 145.040),
('3204', 'Mckinnon', 'VIC', -37.920, 145.040),
('3204', 'Ormond', 'VIC', -37.920, 145.040),
('3204', 'Patterson', 'VIC', -37.920, 145.040),
('3205', 'South Melbourne', 'VIC', -37.930, 145.030),
('3205', 'South Melbourne DC', 'VIC', -37.930, 145.030),
('3206', 'Albert Park', 'VIC', -37.840, 144.960),
('3206', 'Middle Park', 'VIC', -37.840, 144.960),
('3207', 'Garden City', 'VIC', -37.830, 144.960),
('3207', 'Port Melbourne', 'VIC', -37.830, 144.960),
('3211', 'Little River', 'VIC', -37.970, 144.530),
('3212', 'Avalon', 'VIC', -38.020, 144.410),
('3212', 'Lara', 'VIC', -38.020, 144.410),
('3212', 'Point Wilson', 'VIC', -38.020, 144.410),
('3214', 'Corio', 'VIC', -38.070, 144.360),
('3214', 'Norlane', 'VIC', -38.070, 144.360),
('3214', 'North Shore', 'VIC', -38.070, 144.360),
('3215', 'Bell Park', 'VIC', -38.110, 144.330),
('3215', 'Bell Post Hill', 'VIC', -38.110, 144.330),
('3215', 'Drumcondra', 'VIC', -38.110, 144.330),
('3215', 'Geelong North', 'VIC', -38.110, 144.330),
('3215', 'Hamlyn Heights', 'VIC', -38.110, 144.330),
('3215', 'North Geelong', 'VIC', -38.110, 144.330),
('3215', 'Rippleside', 'VIC', -38.110, 144.330),
('3216', 'Belmont', 'VIC', -38.180, 144.340),
('3216', 'Freshwater Creek', 'VIC', -38.180, 144.340),
('3216', 'Grovedale', 'VIC', -38.180, 144.340),
('3216', 'Grovedale East', 'VIC', -38.180, 144.340),
('3216', 'Highton', 'VIC', -38.180, 144.340),
('3216', 'Marshall', 'VIC', -38.180, 144.340),
('3216', 'Mount Duneed', 'VIC', -38.180, 144.340),
('3216', 'Wandana Heights', 'VIC', -38.180, 144.340),
('3216', 'Waurn Ponds', 'VIC', -38.180, 144.340),
('3217', 'Deakin University', 'VIC', -38.200, 144.300),
('3218', 'Geelong West', 'VIC', -38.140, 144.350),
('3218', 'Herne Hill', 'VIC', -38.140, 144.350),
('3218', 'Manifold Heights', 'VIC', -38.140, 144.350),
('3219', 'Breakwater', 'VIC', -38.180, 144.380),
('3219', 'East Geelong', 'VIC', -38.180, 144.380),
('3219', 'Newcomb', 'VIC', -38.180, 144.380),
('3219', 'St Albans Park', 'VIC', -38.180, 144.380),
('3219', 'Thomson', 'VIC', -38.180, 144.380),
('3219', 'Whittington', 'VIC', -38.180, 144.380),
('3220', 'Bareena', 'VIC', -38.180, 145.110),
('3220', 'Geelong', 'VIC', -38.180, 145.110),
('3220', 'Newtown', 'VIC', -38.180, 145.110),
('3220', 'South Geelong', 'VIC', -38.180, 145.110),
('3221', 'Anakie', 'VIC', -37.900, 144.250),
('3221', 'Barrabool', 'VIC', -37.900, 144.250),
('3221', 'Batesford', 'VIC', -37.900, 144.250),
('3221', 'Bellarine', 'VIC', -37.900, 144.250),
('3221', 'Ceres', 'VIC', -37.900, 144.250),
('3221', 'Fyansford', 'VIC', -37.900, 144.250),
('3221', 'Geelong MC', 'VIC', -37.900, 144.250),
('3221', 'Gnarwarre', 'VIC', -37.900, 144.250),
('3221', 'Grey River', 'VIC', -37.900, 144.250),
('3221', 'Kennett River', 'VIC', -37.900, 144.250),
('3221', 'Lovely Banks', 'VIC', -37.900, 144.250),
('3221', 'Moolap', 'VIC', -37.900, 144.250),
('3221', 'Moorabool', 'VIC', -37.900, 144.250),
('3221', 'Murgheboluc', 'VIC', -37.900, 144.250),
('3221', 'Separation Creek', 'VIC', -37.900, 144.250),
('3221', 'Staughton Vale', 'VIC', -37.900, 144.250),
('3221', 'Stonehaven', 'VIC', -37.900, 144.250),
('3221', 'Sugarloaf', 'VIC', -37.900, 144.250),
('3221', 'Wallington', 'VIC', -37.900, 144.250),
('3221', 'Wongarra', 'VIC', -37.900, 144.250),
('3221', 'Wye River', 'VIC', -37.900, 144.250),
('3222', 'Clifton Springs', 'VIC', -38.160, 144.560),
('3222', 'Curlewis', 'VIC', -38.160, 144.560),
('3222', 'Drysdale', 'VIC', -38.160, 144.560),
('3222', 'Mannerim', 'VIC', -38.160, 144.560),
('3222', 'Marcus Hill', 'VIC', -38.160, 144.560),
('3223', 'Indented Head', 'VIC', -38.140, 144.710),
('3223', 'Portarlington', 'VIC', -38.140, 144.710),
('3223', 'St Leonards', 'VIC', -38.140, 144.710),
('3224', 'Leopold', 'VIC', -38.180, 144.460),
('3225', 'Point Lonsdale', 'VIC', -38.290, 144.610),
('3225', 'Queenscliff', 'VIC', -38.290, 144.610),
('3225', 'Swan Bay', 'VIC', -38.290, 144.610),
('3225', 'Swan Island', 'VIC', -38.290, 144.610),
('3226', 'Ocean Grove', 'VIC', -38.270, 144.540),
('3227', 'Barwon Heads', 'VIC', -38.280, 144.490),
('3227', 'Breamlea', 'VIC', -38.280, 144.490),
('3227', 'Connewarre', 'VIC', -38.280, 144.490),
('3228', 'Bellbrae', 'VIC', -38.330, 144.260),
('3228', 'Bells Beach', 'VIC', -38.330, 144.260),
('3228', 'Jan Juc', 'VIC', -38.330, 144.260),
('3228', 'Torquay', 'VIC', -38.330, 144.260),
('3230', 'Anglesea', 'VIC', -38.410, 144.190),
('3231', 'Aireys Inlet', 'VIC', -38.460, 144.110),
('3231', 'Big Hill', 'VIC', -38.460, 144.110),
('3231', 'Eastern View', 'VIC', -38.460, 144.110),
('3231', 'Fairhaven', 'VIC', -38.460, 144.110),
('3231', 'Moggs Creek', 'VIC', -38.460, 144.110),
('3232', 'Lorne', 'VIC', -38.520, 144.000),
('3233', 'Apollo Bay', 'VIC', -38.750, 143.670),
('3233', 'Cape Otway', 'VIC', -38.750, 143.670),
('3233', 'Marengo', 'VIC', -38.750, 143.670),
('3233', 'Petticoat Creek', 'VIC', -38.750, 143.670),
('3233', 'Skenes Creek', 'VIC', -38.750, 143.670),
('3233', 'Skenes Creek North', 'VIC', -38.750, 143.670),
('3235', 'Benwerrin', 'VIC', -38.470, 143.930),
('3235', 'Boonah', 'VIC', -38.470, 143.930),
('3235', 'Deans Marsh', 'VIC', -38.470, 143.930),
('3235', 'Pennyroyal', 'VIC', -38.470, 143.930),
('3236', 'Forrest', 'VIC', -38.520, 143.710),
('3236', 'Mount Sabine', 'VIC', -38.520, 143.710),
('3237', 'Aire Valley', 'VIC', -38.690, 143.560),
('3237', 'Beech Forest', 'VIC', -38.690, 143.560),
('3237', 'Ferguson', 'VIC', -38.690, 143.560),
('3237', 'Gellibrand Lower', 'VIC', -38.690, 143.560),
('3237', 'Wattle Hill', 'VIC', -38.690, 143.560),
('3237', 'Weeaproinah', 'VIC', -38.690, 143.560),
('3237', 'Wyelangta', 'VIC', -38.690, 143.560),
('3237', 'Yuulong', 'VIC', -38.690, 143.560),
('3238', 'Glenaire', 'VIC', -38.780, 143.430),
('3238', 'Hordern Vale', 'VIC', -38.780, 143.430),
('3238', 'Johanna', 'VIC', -38.780, 143.430),
('3238', 'Lavers Hill', 'VIC', -38.780, 143.430),
('3239', 'Carlisle River', 'VIC', -38.560, 143.400),
('3239', 'Chapple Vale', 'VIC', -38.560, 143.400),
('3239', 'Gellibrand', 'VIC', -38.560, 143.400),
('3239', 'Kennedys Creek', 'VIC', -38.560, 143.400),
('3240', 'Buckley', 'VIC', -38.220, 144.080),
('3240', 'Gherang', 'VIC', -38.220, 144.080),
('3240', 'Modewarre', 'VIC', -38.220, 144.080),
('3240', 'Moriac', 'VIC', -38.220, 144.080),
('3240', 'Mount Moriac', 'VIC', -38.220, 144.080),
('3240', 'Paraparap', 'VIC', -38.220, 144.080),
('3241', 'Bambra', 'VIC', -38.370, 143.950),
('3241', 'Ombersley', 'VIC', -38.370, 143.950),
('3241', 'Wensleydale', 'VIC', -38.370, 143.950),
('3241', 'Winchelsea', 'VIC', -38.370, 143.950),
('3241', 'Winchelsea South', 'VIC', -38.370, 143.950),
('3241', 'Wurdiboluc', 'VIC', -38.370, 143.950),
('3242', 'Birregurra', 'VIC', -38.310, 143.790),
('3243', 'Barwon Downs', 'VIC', -38.470, 143.760),
('3243', 'Murroon', 'VIC', -38.470, 143.760),
('3243', 'Warncoort', 'VIC', -38.470, 143.760),
('3243', 'Whoorel', 'VIC', -38.470, 143.760),
('3249', 'Alvie', 'VIC', -38.240, 143.480),
('3249', 'Balintore', 'VIC', -38.240, 143.480),
('3249', 'Barongarook', 'VIC', -38.240, 143.480),
('3249', 'Barongarook West', 'VIC', -38.240, 143.480),
('3249', 'Barramunga', 'VIC', -38.240, 143.480),
('3249', 'Coragulac', 'VIC', -38.240, 143.480),
('3249', 'Corunnun', 'VIC', -38.240, 143.480),
('3249', 'Dreeite', 'VIC', -38.240, 143.480),
('3249', 'Dreeite South', 'VIC', -38.240, 143.480),
('3249', 'Gerangamete', 'VIC', -38.240, 143.480),
('3249', 'Irrewarra', 'VIC', -38.240, 143.480),
('3249', 'Irrewillipe', 'VIC', -38.240, 143.480),
('3249', 'Irrewillipe East', 'VIC', -38.240, 143.480),
('3249', 'Kawarren', 'VIC', -38.240, 143.480),
('3249', 'Larpent', 'VIC', -38.240, 143.480),
('3249', 'Nalangil', 'VIC', -38.240, 143.480),
('3249', 'Ondit', 'VIC', -38.240, 143.480),
('3249', 'Pirron Yallock', 'VIC', -38.240, 143.480),
('3249', 'Pomborneit East', 'VIC', -38.240, 143.480),
('3249', 'Swan Marsh', 'VIC', -38.240, 143.480),
('3249', 'Tanybryn', 'VIC', -38.240, 143.480),
('3249', 'Warrion', 'VIC', -38.240, 143.480),
('3249', 'Wool Wool', 'VIC', -38.240, 143.480),
('3249', 'Yeo', 'VIC', -38.240, 143.480),
('3249', 'Yeodene', 'VIC', -38.240, 143.480),
('3250', 'Colac', 'VIC', -38.340, 143.580),
('3250', 'Colac East', 'VIC', -38.340, 143.580),
('3250', 'Colac West', 'VIC', -38.340, 143.580),
('3250', 'Elliminyt', 'VIC', -38.340, 143.580),
('3251', 'Beeac', 'VIC', -38.190, 143.640),
('3251', 'Cundare', 'VIC', -38.190, 143.640),
('3251', 'Cundare North', 'VIC', -38.190, 143.640),
('3251', 'Eurack', 'VIC', -38.190, 143.640),
('3251', 'Weering', 'VIC', -38.190, 143.640),
('3254', 'Cororooke', 'VIC', -38.300, 143.520),
('3260', 'Bookaar', 'VIC', -38.130, 143.100),
('3260', 'Bostocks Creek', 'VIC', -38.130, 143.100),
('3260', 'Bungador', 'VIC', -38.130, 143.100),
('3260', 'Camperdown', 'VIC', -38.130, 143.100),
('3260', 'Carpendeit', 'VIC', -38.130, 143.100),
('3260', 'Chocolyn', 'VIC', -38.130, 143.100),
('3260', 'Gnotuk', 'VIC', -38.130, 143.100),
('3260', 'Kariah', 'VIC', -38.130, 143.100),
('3260', 'Koallah', 'VIC', -38.130, 143.100),
('3260', 'Leslie Manor', 'VIC', -38.130, 143.100),
('3260', 'Pomborneit', 'VIC', -38.130, 143.100),
('3260', 'Pomborneit North', 'VIC', -38.130, 143.100),
('3260', 'Skibo', 'VIC', -38.130, 143.100),
('3260', 'South Purrumbete', 'VIC', -38.130, 143.100),
('3260', 'Stonyford', 'VIC', -38.130, 143.100),
('3260', 'Tandarook', 'VIC', -38.130, 143.100),
('3260', 'Tesbury', 'VIC', -38.130, 143.100),
('3260', 'Weerite', 'VIC', -38.130, 143.100),
('3264', 'Terang', 'VIC', -38.240, 142.910),
('3265', 'Boorcan', 'VIC', -38.210, 143.010),
('3265', 'Cudgee', 'VIC', -38.210, 143.010),
('3265', 'Dixie', 'VIC', -38.210, 143.010),
('3265', 'Ecklin South', 'VIC', -38.210, 143.010),
('3265', 'Ellerslie', 'VIC', -38.210, 143.010),
('3265', 'Framlingham', 'VIC', -38.210, 143.010),
('3265', 'Framlingham East', 'VIC', -38.210, 143.010),
('3265', 'Garvoc', 'VIC', -38.210, 143.010),
('3265', 'Glenormiston North', 'VIC', -38.210, 143.010),
('3265', 'Glenormiston South', 'VIC', -38.210, 143.010),
('3265', 'Kolora', 'VIC', -38.210, 143.010),
('3265', 'Laang', 'VIC', -38.210, 143.010),
('3265', 'Noorat', 'VIC', -38.210, 143.010),
('3265', 'Noorat East', 'VIC', -38.210, 143.010),
('3265', 'Panmure', 'VIC', -38.210, 143.010),
('3265', 'Taroon', 'VIC', -38.210, 143.010),
('3265', 'The Sisters', 'VIC', -38.210, 143.010),
('3266', 'Bullaharre', 'VIC', -38.340, 143.140),
('3266', 'Cobden', 'VIC', -38.340, 143.140),
('3266', 'Cobrico', 'VIC', -38.340, 143.140),
('3266', 'Elingamite', 'VIC', -38.340, 143.140),
('3266', 'Elingamite North', 'VIC', -38.340, 143.140),
('3266', 'Glenfyne', 'VIC', -38.340, 143.140),
('3266', 'Jancourt', 'VIC', -38.340, 143.140),
('3266', 'Jancourt East', 'VIC', -38.340, 143.140),
('3266', 'Naroghid', 'VIC', -38.340, 143.140),
('3266', 'Simpson', 'VIC', -38.340, 143.140),
('3267', 'Scotts Creek', 'VIC', -38.450, 143.110),
('3268', 'Ayrford', 'VIC', -38.420, 142.860),
('3268', 'Brucknell', 'VIC', -38.420, 142.860),
('3268', 'Cooriemungle', 'VIC', -38.420, 142.860),
('3268', 'Cowleys Creek', 'VIC', -38.420, 142.860),
('3268', 'Curdies River', 'VIC', -38.420, 142.860),
('3268', 'Curdievale', 'VIC', -38.420, 142.860),
('3268', 'Heytesbury Lower', 'VIC', -38.420, 142.860),
('3268', 'Newfield', 'VIC', -38.420, 142.860),
('3268', 'Nirranda', 'VIC', -38.420, 142.860),
('3268', 'Nirranda East', 'VIC', -38.420, 142.860),
('3268', 'Nirranda South', 'VIC', -38.420, 142.860),
('3268', 'Nullawarre', 'VIC', -38.420, 142.860),
('3268', 'Nullawarre North', 'VIC', -38.420, 142.860),
('3268', 'Paaratte', 'VIC', -38.420, 142.860),
('3268', 'The Cove', 'VIC', -38.420, 142.860),
('3268', 'Timboon', 'VIC', -38.420, 142.860),
('3268', 'Timboon West', 'VIC', -38.420, 142.860),
('3269', 'Port Campbell', 'VIC', -38.620, 143.000),
('3269', 'Princetown', 'VIC', -38.620, 143.000),
('3269', 'Waarre', 'VIC', -38.620, 143.000),
('3270', 'Peterborough', 'VIC', -38.580, 142.860),
('3271', 'Darlington', 'VIC', -38.000, 143.050),
('3271', 'Dundonnell', 'VIC', -38.000, 143.050),
('3271', 'Pura Pura', 'VIC', -38.000, 143.050),
('3272', 'Mortlake', 'VIC', -38.080, 142.810),
('3272', 'Woorndoo', 'VIC', -38.080, 142.810),
('3273', 'Hexham', 'VIC', -38.060, 142.610),
('3274', 'Caramut', 'VIC', -37.910, 142.520),
('3275', 'Mailors Flat', 'VIC', -38.300, 142.460),
('3276', 'Minjah', 'VIC', -38.040, 142.440),
('3276', 'Woolsthorpe', 'VIC', -38.040, 142.440),
('3277', 'Allansford', 'VIC', -38.390, 142.590),
('3277', 'Mepunga', 'VIC', -38.390, 142.590),
('3277', 'Mepunga East', 'VIC', -38.390, 142.590),
('3277', 'Mepunga West', 'VIC', -38.390, 142.590),
('3277', 'Naringal', 'VIC', -38.390, 142.590),
('3277', 'Naringal East', 'VIC', -38.390, 142.590),
('3278', 'Purnim', 'VIC', -38.280, 142.620),
('3278', 'Purnim West', 'VIC', -38.280, 142.620),
('3279', 'Ballangeich', 'VIC', -38.210, 142.660),
('3279', 'Wangoom', 'VIC', -38.210, 142.660),
('3280', 'Dennington', 'VIC', -38.360, 142.440),
('3280', 'Warrnambool', 'VIC', -38.360, 142.440),
('3280', 'Warrnambool East', 'VIC', -38.360, 142.440),
('3280', 'Warrnambool West', 'VIC', -38.360, 142.440),
('3281', 'Bushfield', 'VIC', -38.330, 142.510),
('3281', 'Grassmere', 'VIC', -38.330, 142.510),
('3281', 'Winslow', 'VIC', -38.330, 142.510),
('3281', 'Woodford', 'VIC', -38.330, 142.510),
('3282', 'Illowa', 'VIC', -38.330, 142.410),
('3282', 'Koroit', 'VIC', -38.330, 142.410),
('3283', 'Crossley', 'VIC', -38.310, 142.330),
('3283', 'Killarney', 'VIC', -38.310, 142.330),
('3283', 'Kirkstall', 'VIC', -38.310, 142.330),
('3283', 'Southern Cross', 'VIC', -38.310, 142.330),
('3283', 'Tarrone', 'VIC', -38.310, 142.330),
('3283', 'Tower Hill', 'VIC', -38.310, 142.330),
('3283', 'Warrong', 'VIC', -38.310, 142.330),
('3283', 'Willatook', 'VIC', -38.310, 142.330),
('3283', 'Yangery', 'VIC', -38.310, 142.330),
('3283', 'Yarpturk', 'VIC', -38.310, 142.330),
('3284', 'Orford', 'VIC', -38.210, 142.150),
('3284', 'Port Fairy', 'VIC', -38.210, 142.150),
('3285', 'Codrington', 'VIC', -38.270, 141.970),
('3285', 'Narrawong', 'VIC', -38.270, 141.970),
('3285', 'Rosebrook', 'VIC', -38.270, 141.970),
('3285', 'St Helens', 'VIC', -38.270, 141.970),
('3285', 'Toolong', 'VIC', -38.270, 141.970),
('3285', 'Tyrendarra', 'VIC', -38.270, 141.970),
('3285', 'Tyrendarra East', 'VIC', -38.270, 141.970),
('3285', 'Yambuk', 'VIC', -38.270, 141.970),
('3286', 'Condah Swamp', 'VIC', -37.970, 141.830),
('3286', 'Knebsworth', 'VIC', -37.970, 141.830),
('3286', 'Macarthur', 'VIC', -37.970, 141.830),
('3286', 'Warrabkook', 'VIC', -37.970, 141.830),
('3287', 'Hawkesdale', 'VIC', -38.110, 142.320),
('3287', 'Minhamite', 'VIC', -38.110, 142.320),
('3289', 'Gazette', 'VIC', -37.900, 142.170),
('3289', 'Gerrigerrup', 'VIC', -37.900, 142.170),
('3289', 'Penshurst', 'VIC', -37.900, 142.170),
('3289', 'Purdeet', 'VIC', -37.900, 142.170),
('3289', 'Tabor', 'VIC', -37.900, 142.170),
('3292', 'Nelson', 'VIC', -38.050, 141.010),
('3293', 'Glenthompson', 'VIC', -37.640, 142.550),
('3293', 'Nareeb', 'VIC', -37.640, 142.550),
('3293', 'Narrapumelap South', 'VIC', -37.640, 142.550),
('3294', 'Dunkeld', 'VIC', -37.650, 142.340),
('3294', 'Karabeal', 'VIC', -37.650, 142.340),
('3294', 'Mirranatwa', 'VIC', -37.650, 142.340),
('3294', 'Moutajup', 'VIC', -37.650, 142.340),
('3294', 'Victoria Point', 'VIC', -37.650, 142.340),
('3294', 'Victoria Valley', 'VIC', -37.650, 142.340),
('3294', 'Woodhouse', 'VIC', -37.650, 142.340),
('3300', 'Byaduk North', 'VIC', -37.880, 141.960),
('3300', 'Hamilton', 'VIC', -37.880, 141.960),
('3301', 'Bochara', 'VIC', -37.700, 141.930),
('3301', 'Broadwater', 'VIC', -37.700, 141.930),
('3301', 'Buckley Swamp', 'VIC', -37.700, 141.930),
('3301', 'Byaduk', 'VIC', -37.700, 141.930),
('3301', 'Croxton East', 'VIC', -37.700, 141.930),
('3301', 'Hensley Park', 'VIC', -37.700, 141.930),
('3301', 'Morgiana', 'VIC', -37.700, 141.930),
('3301', 'Mount Napier', 'VIC', -37.700, 141.930),
('3301', 'Strathkellar', 'VIC', -37.700, 141.930),
('3301', 'Tahara', 'VIC', -37.700, 141.930),
('3301', 'Tarrington', 'VIC', -37.700, 141.930),
('3301', 'Wannon', 'VIC', -37.700, 141.930),
('3301', 'Warrayure', 'VIC', -37.700, 141.930),
('3301', 'Yatchaw', 'VIC', -37.700, 141.930),
('3301', 'Yulecart', 'VIC', -37.700, 141.930),
('3302', 'Branxholme', 'VIC', -37.860, 141.790),
('3302', 'Grassdale', 'VIC', -37.860, 141.790),
('3303', 'Breakaway Creek', 'VIC', -38.030, 141.810),
('3303', 'Condah', 'VIC', -38.030, 141.810),
('3303', 'Hotspur', 'VIC', -38.030, 141.810),
('3303', 'Lake Condah', 'VIC', -38.030, 141.810),
('3303', 'Wallacedale', 'VIC', -38.030, 141.810),
('3304', 'Bessiebelle', 'VIC', -38.150, 141.970),
('3304', 'Dartmoor', 'VIC', -38.150, 141.970),
('3304', 'Drik Drik', 'VIC', -38.150, 141.970),
('3304', 'Drumborg', 'VIC', -38.150, 141.970),
('3304', 'Greenwald', 'VIC', -38.150, 141.970),
('3304', 'Heywood', 'VIC', -38.150, 141.970),
('3304', 'Homerton', 'VIC', -38.150, 141.970),
('3304', 'Lyons', 'VIC', -38.150, 141.970),
('3304', 'Milltown', 'VIC', -38.150, 141.970),
('3304', 'Mumbannar', 'VIC', -38.150, 141.970),
('3304', 'Myamyn', 'VIC', -38.150, 141.970),
('3304', 'Winnap', 'VIC', -38.150, 141.970),
('3305', 'Allestree', 'VIC', -38.270, 141.650),
('3305', 'Bolwarra', 'VIC', -38.270, 141.650),
('3305', 'Cape Bridgewater', 'VIC', -38.270, 141.650),
('3305', 'Cashmore', 'VIC', -38.270, 141.650),
('3305', 'Dutton Way', 'VIC', -38.270, 141.650),
('3305', 'Gorae', 'VIC', -38.270, 141.650),
('3305', 'Gorae West', 'VIC', -38.270, 141.650),
('3305', 'Heathmere', 'VIC', -38.270, 141.650),
('3305', 'Mount Richmond', 'VIC', -38.270, 141.650),
('3305', 'Portland', 'VIC', -38.270, 141.650),
('3305', 'Portland North', 'VIC', -38.270, 141.650),
('3305', 'Portland West', 'VIC', -38.270, 141.650),
('3309', 'Digby', 'VIC', -37.790, 141.500),
('3310', 'Merino', 'VIC', -37.720, 141.550),
('3310', 'Tahara West', 'VIC', -37.720, 141.550),
('3311', 'Casterton', 'VIC', -37.580, 141.410),
('3311', 'Corndale', 'VIC', -37.580, 141.410),
('3312', 'Bahgallah', 'VIC', -37.640, 141.370),
('3312', 'Brimboal', 'VIC', -37.640, 141.370),
('3312', 'Carapook', 'VIC', -37.640, 141.370),
('3312', 'Chetwynd', 'VIC', -37.640, 141.370),
('3312', 'Dergholm', 'VIC', -37.640, 141.370),
('3312', 'Dorodong', 'VIC', -37.640, 141.370),
('3312', 'Dunrobin', 'VIC', -37.640, 141.370),
('3312', 'Henty', 'VIC', -37.640, 141.370),
('3312', 'Killara', 'VIC', -37.640, 141.370),
('3312', 'Lake Mundi', 'VIC', -37.640, 141.370),
('3312', 'Lindsay', 'VIC', -37.640, 141.370),
('3312', 'Nangeela', 'VIC', -37.640, 141.370),
('3312', 'Poolaijelo', 'VIC', -37.640, 141.370),
('3312', 'Powers Creek', 'VIC', -37.640, 141.370),
('3312', 'Sandford', 'VIC', -37.640, 141.370),
('3312', 'Strathdownie', 'VIC', -37.640, 141.370),
('3312', 'Wando Bridge', 'VIC', -37.640, 141.370),
('3312', 'Wando Vale', 'VIC', -37.640, 141.370),
('3312', 'Warrock', 'VIC', -37.640, 141.370),
('3314', 'Bulart', 'VIC', -37.590, 141.940),
('3314', 'Cavendish', 'VIC', -37.590, 141.940),
('3314', 'Glenisla', 'VIC', -37.590, 141.940),
('3314', 'Grampians', 'VIC', -37.590, 141.940),
('3314', 'Mooralla', 'VIC', -37.590, 141.940),
('3315', 'Brit Brit', 'VIC', -37.440, 141.760),
('3315', 'Clover Flat', 'VIC', -37.440, 141.760),
('3315', 'Coleraine', 'VIC', -37.440, 141.760),
('3315', 'Coojar', 'VIC', -37.440, 141.760),
('3315', 'Culla', 'VIC', -37.440, 141.760),
('3315', 'Gringegalgona', 'VIC', -37.440, 141.760),
('3315', 'Gritjurk', 'VIC', -37.440, 141.760),
('3315', 'Hilgay', 'VIC', -37.440, 141.760),
('3315', 'Konongwootong', 'VIC', -37.440, 141.760),
('3315', 'Melville Forest', 'VIC', -37.440, 141.760),
('3315', 'Muntham', 'VIC', -37.440, 141.760),
('3315', 'Nareen', 'VIC', -37.440, 141.760),
('3315', 'Paschendale', 'VIC', -37.440, 141.760),
('3315', 'Tahara Bridge', 'VIC', -37.440, 141.760),
('3315', 'Tarrayoukyan', 'VIC', -37.440, 141.760),
('3315', 'Tarrenlea', 'VIC', -37.440, 141.760),
('3315', 'Wootong Vale', 'VIC', -37.440, 141.760),
('3317', 'Harrow', 'VIC', -37.120, 141.600),
('3318', 'Charam', 'VIC', -36.990, 141.510),
('3318', 'Connewirricoo', 'VIC', -36.990, 141.510),
('3318', 'Edenhope', 'VIC', -36.990, 141.510),
('3318', 'Kadnook', 'VIC', -36.990, 141.510),
('3318', 'Langkoop', 'VIC', -36.990, 141.510),
('3318', 'Patyah', 'VIC', -36.990, 141.510),
('3318', 'Ullswater', 'VIC', -36.990, 141.510),
('3319', 'Apsley', 'VIC', -36.970, 141.080),
('3319', 'Benayeo', 'VIC', -36.970, 141.080),
('3319', 'Bringalbert', 'VIC', -36.970, 141.080),
('3321', 'Hesse', 'VIC', -38.110, 143.860),
('3321', 'Inverleigh', 'VIC', -38.110, 143.860),
('3321', 'Wingeel', 'VIC', -38.110, 143.860),
('3322', 'Cressy', 'VIC', -38.030, 143.640),
('3323', 'Berrybank', 'VIC', -37.990, 143.490),
('3323', 'Duverney', 'VIC', -37.990, 143.490),
('3323', 'Foxhow', 'VIC', -37.990, 143.490),
('3324', 'Lismore', 'VIC', -37.950, 143.340),
('3324', 'Mingay', 'VIC', -37.950, 143.340),
('3324', 'Mount Bute', 'VIC', -37.950, 143.340),
('3325', 'Derrinallum', 'VIC', -37.950, 143.220),
('3325', 'Larralea', 'VIC', -37.950, 143.220),
('3325', 'Vite Vite', 'VIC', -37.950, 143.220),
('3325', 'Vite Vite North', 'VIC', -37.950, 143.220),
('3328', 'Teesdale', 'VIC', -38.040, 144.050),
('3329', 'Barunah Park', 'VIC', -38.020, 143.860),
('3329', 'Barunah Plains', 'VIC', -38.020, 143.860),
('3329', 'Shelford', 'VIC', -38.020, 143.860),
('3330', 'Rokewood', 'VIC', -37.840, 143.680),
('3331', 'Bannockburn', 'VIC', -38.050, 144.170),
('3331', 'Gheringhap', 'VIC', -38.050, 144.170),
('3331', 'Maude', 'VIC', -38.050, 144.170),
('3331', 'Russells Bridge', 'VIC', -38.050, 144.170),
('3331', 'She Oaks', 'VIC', -38.050, 144.170),
('3331', 'Steiglitz', 'VIC', -38.050, 144.170),
('3331', 'Sutherlands Creek', 'VIC', -38.050, 144.170),
('3332', 'Lethbridge', 'VIC', -37.960, 144.140),
('3333', 'Bamganie', 'VIC', -37.920, 144.030),
('3333', 'Meredith', 'VIC', -37.920, 144.030),
('3334', 'Bungal', 'VIC', -37.710, 144.090),
('3334', 'Cargerie', 'VIC', -37.710, 144.090),
('3334', 'Elaine', 'VIC', -37.710, 144.090),
('3334', 'Morrisons', 'VIC', -37.710, 144.090),
('3334', 'Mount Doran', 'VIC', -37.710, 144.090),
('3335', 'Plumpton', 'VIC', -37.690, 144.690),
('3335', 'Rockbank', 'VIC', -37.690, 144.690),
('3337', 'Kurunjang', 'VIC', -37.680, 144.590),
('3337', 'Melton', 'VIC', -37.680, 144.590),
('3337', 'Melton West', 'VIC', -37.680, 144.590),
('3337', 'Toolern Vale', 'VIC', -37.680, 144.590),
('3338', 'Brookfield', 'VIC', -37.700, 144.540),
('3338', 'Exford', 'VIC', -37.700, 144.540),
('3338', 'Eynesbury', 'VIC', -37.700, 144.540),
('3338', 'Melton South', 'VIC', -37.700, 144.540),
('3340', 'Bacchus Marsh', 'VIC', -37.680, 144.440),
('3340', 'Balliang', 'VIC', -37.680, 144.440),
('3340', 'Balliang East', 'VIC', -37.680, 144.440),
('3340', 'Coimadai', 'VIC', -37.680, 144.440),
('3340', 'Darley', 'VIC', -37.680, 144.440),
('3340', 'Glenmore', 'VIC', -37.680, 144.440),
('3340', 'Hopetoun Park', 'VIC', -37.680, 144.440),
('3340', 'Long Forest', 'VIC', -37.680, 144.440),
('3340', 'Maddingley', 'VIC', -37.680, 144.440),
('3340', 'Merrimu', 'VIC', -37.680, 144.440),
('3340', 'Parwan', 'VIC', -37.680, 144.440),
('3340', 'Rowsley', 'VIC', -37.680, 144.440),
('3341', 'Dales Creek', 'VIC', -37.520, 144.310),
('3341', 'Greendale', 'VIC', -37.520, 144.310),
('3341', 'Korobeit', 'VIC', -37.520, 144.310),
('3341', 'Myrniong', 'VIC', -37.520, 144.310),
('3341', 'Pentland Hills', 'VIC', -37.520, 144.310),
('3342', 'Ballan', 'VIC', -37.600, 144.230),
('3342', 'Beremboke', 'VIC', -37.600, 144.230),
('3342', 'Blakeville', 'VIC', -37.600, 144.230),
('3342', 'Bunding', 'VIC', -37.600, 144.230),
('3342', 'Colbrook', 'VIC', -37.600, 144.230),
('3342', 'Durdidwarrah', 'VIC', -37.600, 144.230),
('3342', 'Fiskville', 'VIC', -37.600, 144.230),
('3342', 'Ingliston', 'VIC', -37.600, 144.230),
('3342', 'Mount Wallace', 'VIC', -37.600, 144.230),
('3345', 'Gordon', 'VIC', -37.590, 144.100),
('3350', 'Alfredton', 'VIC', -37.560, 143.820),
('3350', 'Bakery Hill', 'VIC', -37.560, 143.820),
('3350', 'Ballarat', 'VIC', -37.560, 143.820),
('3350', 'Ballarat Central', 'VIC', -37.560, 143.820),
('3350', 'Ballarat East', 'VIC', -37.560, 143.820),
('3350', 'Ballarat North', 'VIC', -37.560, 143.820),
('3350', 'Ballarat West', 'VIC', -37.560, 143.820),
('3350', 'Black Hill', 'VIC', -37.560, 143.820),
('3350', 'Brown Hill', 'VIC', -37.560, 143.820),
('3350', 'Canadian', 'VIC', -37.560, 143.820),
('3350', 'Eureka', 'VIC', -37.560, 143.820),
('3350', 'Golden Point', 'VIC', -37.560, 143.820),
('3350', 'Invermay Park', 'VIC', -37.560, 143.820),
('3350', 'Lake Wendouree', 'VIC', -37.560, 143.820),
('3350', 'Mount Clear', 'VIC', -37.560, 143.820),
('3350', 'Mount Helen', 'VIC', -37.560, 143.820),
('3350', 'Mount Pleasant', 'VIC', -37.560, 143.820),
('3350', 'Nerrina', 'VIC', -37.560, 143.820),
('3350', 'Newington', 'VIC', -37.560, 143.820),
('3350', 'Redan', 'VIC', -37.560, 143.820),
('3350', 'Soldiers Hill', 'VIC', -37.560, 143.820),
('3351', 'Berringa', 'VIC', -37.770, 143.680),
('3351', 'Bo Peep', 'VIC', -37.770, 143.680),
('3351', 'Cape Clear', 'VIC', -37.770, 143.680),
('3351', 'Carngham', 'VIC', -37.770, 143.680),
('3351', 'Chepstowe', 'VIC', -37.770, 143.680),
('3351', 'Haddon', 'VIC', -37.770, 143.680),
('3351', 'Hillcrest', 'VIC', -37.770, 143.680),
('3351', 'Illabarook', 'VIC', -37.770, 143.680),
('3351', 'Lake Bolac', 'VIC', -37.770, 143.680),
('3351', 'Mininera', 'VIC', -37.770, 143.680),
('3351', 'Mount Emu', 'VIC', -37.770, 143.680),
('3351', 'Nerrin Nerrin', 'VIC', -37.770, 143.680),
('3351', 'Newtown', 'VIC', -37.770, 143.680),
('3351', 'Nintingbool', 'VIC', -37.770, 143.680),
('3351', 'Piggoreet', 'VIC', -37.770, 143.680),
('3351', 'Pitfield', 'VIC', -37.770, 143.680),
('3351', 'Rokewood Junction', 'VIC', -37.770, 143.680),
('3351', 'Ross Creek', 'VIC', -37.770, 143.680),
('3351', 'Scarsdale', 'VIC', -37.770, 143.680),
('3351', 'Smythes Creek', 'VIC', -37.770, 143.680),
('3351', 'Smythesdale', 'VIC', -37.770, 143.680),
('3351', 'Snake Valley', 'VIC', -37.770, 143.680),
('3351', 'Springdallah', 'VIC', -37.770, 143.680),
('3351', 'Staffordshire Reef', 'VIC', -37.770, 143.680),
('3351', 'Streatham', 'VIC', -37.770, 143.680),
('3351', 'Wallinduc', 'VIC', -37.770, 143.680),
('3351', 'Westmere', 'VIC', -37.770, 143.680),
('3352', 'Addington', 'VIC', -37.380, 143.670),
('3352', 'Ballarat Roadside Delivery', 'VIC', -37.380, 143.670),
('3352', 'Blowhard', 'VIC', -37.380, 143.670),
('3352', 'Bolwarrah', 'VIC', -37.380, 143.670),
('3352', 'Bonshaw', 'VIC', -37.380, 143.670),
('3352', 'Brewster', 'VIC', -37.380, 143.670),
('3352', 'Bullarook', 'VIC', -37.380, 143.670),
('3352', 'Bungaree', 'VIC', -37.380, 143.670),
('3352', 'Bunkers Hill', 'VIC', -37.380, 143.670),
('3352', 'Burrumbeet', 'VIC', -37.380, 143.670),
('3352', 'Cambrian Hill', 'VIC', -37.380, 143.670),
('3352', 'Cardigan', 'VIC', -37.380, 143.670),
('3352', 'Cardigan Village', 'VIC', -37.380, 143.670),
('3352', 'Chapel Flat', 'VIC', -37.380, 143.670),
('3352', 'Clarendon', 'VIC', -37.380, 143.670),
('3352', 'Claretown', 'VIC', -37.380, 143.670),
('3352', 'Clarkes Hill', 'VIC', -37.380, 143.670),
('3352', 'Corindhap', 'VIC', -37.380, 143.670),
('3352', 'Dereel', 'VIC', -37.380, 143.670),
('3352', 'Dunnstown', 'VIC', -37.380, 143.670),
('3352', 'Durham Lead', 'VIC', -37.380, 143.670),
('3352', 'Enfield', 'VIC', -37.380, 143.670),
('3352', 'Ercildoune', 'VIC', -37.380, 143.670),
('3352', 'Garibaldi', 'VIC', -37.380, 143.670),
('3352', 'Glen Park', 'VIC', -37.380, 143.670),
('3352', 'Glenbrae', 'VIC', -37.380, 143.670),
('3352', 'Gong Gong', 'VIC', -37.380, 143.670),
('3352', 'Grenville', 'VIC', -37.380, 143.670),
('3352', 'Invermay', 'VIC', -37.380, 143.670),
('3352', 'Lal Lal', 'VIC', -37.380, 143.670),
('3352', 'Lamplough', 'VIC', -37.380, 143.670),
('3352', 'Langi Kal Kal', 'VIC', -37.380, 143.670),
('3352', 'Learmonth', 'VIC', -37.380, 143.670),
('3352', 'Leigh Creek', 'VIC', -37.380, 143.670),
('3352', 'Lexton', 'VIC', -37.380, 143.670),
('3352', 'Magpie', 'VIC', -37.380, 143.670),
('3352', 'Millbrook', 'VIC', -37.380, 143.670),
('3352', 'Miners Rest', 'VIC', -37.380, 143.670),
('3352', 'Mitchell Park', 'VIC', -37.380, 143.670),
('3352', 'Mollongghip', 'VIC', -37.380, 143.670),
('3352', 'Mount Bolton', 'VIC', -37.380, 143.670),
('3352', 'Mount Egerton', 'VIC', -37.380, 143.670),
('3352', 'Mount Mercer', 'VIC', -37.380, 143.670),
('3352', 'Mount Rowan', 'VIC', -37.380, 143.670),
('3352', 'Napoleons', 'VIC', -37.380, 143.670),
('3352', 'Navigators', 'VIC', -37.380, 143.670),
('3352', 'Pootilla', 'VIC', -37.380, 143.670),
('3352', 'Scotchmans Lead', 'VIC', -37.380, 143.670),
('3352', 'Scotsburn', 'VIC', -37.380, 143.670),
('3352', 'Springbank', 'VIC', -37.380, 143.670),
('3352', 'Sulky', 'VIC', -37.380, 143.670),
('3352', 'Wallace', 'VIC', -37.380, 143.670),
('3352', 'Warrenheip', 'VIC', -37.380, 143.670),
('3352', 'Wattle Flat', 'VIC', -37.380, 143.670),
('3352', 'Waubra', 'VIC', -37.380, 143.670),
('3352', 'Weatherboard', 'VIC', -37.380, 143.670),
('3352', 'Werneth', 'VIC', -37.380, 143.670),
('3352', 'Windermere', 'VIC', -37.380, 143.670),
('3352', 'Yendon', 'VIC', -37.380, 143.670),
('3353', 'Ballarat', 'VIC', -37.780, 144.840),
('3354', 'Bakery Hill', 'VIC', -37.560, 143.870),
('3354', 'Ballarat MC', 'VIC', -37.560, 143.870),
('3355', 'Lake Gardens', 'VIC', -37.550, 143.820),
('3355', 'Mitchell Park', 'VIC', -37.550, 143.820),
('3355', 'Wendouree', 'VIC', -37.550, 143.820),
('3355', 'Wendouree Village', 'VIC', -37.550, 143.820),
('3356', 'Delacombe', 'VIC', -37.590, 143.830),
('3356', 'Sebastopol', 'VIC', -37.590, 143.830),
('3357', 'Buninyong', 'VIC', -37.650, 143.920),
('3357', 'Scotsmans Lead', 'VIC', -37.650, 143.920),
('3360', 'Happy Valley', 'VIC', -37.690, 143.560),
('3360', 'Linton', 'VIC', -37.690, 143.560),
('3360', 'Mannibadar', 'VIC', -37.690, 143.560),
('3360', 'Pittong', 'VIC', -37.690, 143.560),
('3360', 'Willowvale', 'VIC', -37.690, 143.560),
('3361', 'Bradvale', 'VIC', -37.780, 143.410),
('3361', 'Carranballac', 'VIC', -37.780, 143.410),
('3361', 'Skipton', 'VIC', -37.780, 143.410),
('3363', 'Creswick', 'VIC', -37.420, 143.890),
('3363', 'Creswick North', 'VIC', -37.420, 143.890),
('3363', 'Dean', 'VIC', -37.420, 143.890),
('3363', 'Glendaruel', 'VIC', -37.420, 143.890),
('3363', 'Langdons Hill', 'VIC', -37.420, 143.890),
('3363', 'Mount Beckworth', 'VIC', -37.420, 143.890),
('3363', 'Tourello', 'VIC', -37.420, 143.890),
('3364', 'Allendale', 'VIC', -37.370, 143.940),
('3364', 'Ascot', 'VIC', -37.370, 143.940),
('3364', 'Bald Hills', 'VIC', -37.370, 143.940),
('3364', 'Barkstead', 'VIC', -37.370, 143.940),
('3364', 'Blampied', 'VIC', -37.370, 143.940),
('3364', 'Broomfield', 'VIC', -37.370, 143.940),
('3364', 'Cabbage Tree', 'VIC', -37.370, 143.940),
('3364', 'Campbelltown', 'VIC', -37.370, 143.940),
('3364', 'Coghills Creek', 'VIC', -37.370, 143.940),
('3364', 'Glendonald', 'VIC', -37.370, 143.940),
('3364', 'Kingston', 'VIC', -37.370, 143.940),
('3364', 'Kooroocheang', 'VIC', -37.370, 143.940),
('3364', 'Lawrence', 'VIC', -37.370, 143.940),
('3364', 'Mount Prospect', 'VIC', -37.370, 143.940),
('3364', 'Newlyn', 'VIC', -37.370, 143.940),
('3364', 'Newlyn North', 'VIC', -37.370, 143.940),
('3364', 'Rocklyn', 'VIC', -37.370, 143.940),
('3364', 'Smeaton', 'VIC', -37.370, 143.940),
('3364', 'Smokeytown', 'VIC', -37.370, 143.940),
('3364', 'Springmount', 'VIC', -37.370, 143.940),
('3364', 'Strathlea', 'VIC', -37.370, 143.940),
('3364', 'Werona', 'VIC', -37.370, 143.940),
('3370', 'Clunes', 'VIC', -37.290, 143.790),
('3370', 'Glengower', 'VIC', -37.290, 143.790),
('3370', 'Mount Cameron', 'VIC', -37.290, 143.790),
('3370', 'Ullina', 'VIC', -37.290, 143.790),
('3371', 'Amherst', 'VIC', -37.150, 143.670),
('3371', 'Burnbank', 'VIC', -37.150, 143.670),
('3371', 'Caralulup', 'VIC', -37.150, 143.670),
('3371', 'Dunach', 'VIC', -37.150, 143.670),
('3371', 'Evansford', 'VIC', -37.150, 143.670),
('3371', 'Lillicur', 'VIC', -37.150, 143.670),
('3371', 'Mount Glasgow', 'VIC', -37.150, 143.670),
('3371', 'Red Lion', 'VIC', -37.150, 143.670),
('3371', 'Stony Creek', 'VIC', -37.150, 143.670),
('3371', 'Talbot', 'VIC', -37.150, 143.670),
('3373', 'Beaufort', 'VIC', -37.430, 143.380),
('3373', 'Chute', 'VIC', -37.430, 143.380),
('3373', 'Cross Roads', 'VIC', -37.430, 143.380),
('3373', 'Lake Goldsmith', 'VIC', -37.430, 143.380),
('3373', 'Lake Wongan', 'VIC', -37.430, 143.380),
('3373', 'Main Lead', 'VIC', -37.430, 143.380),
('3373', 'Mena Park', 'VIC', -37.430, 143.380),
('3373', 'Nerring', 'VIC', -37.430, 143.380),
('3373', 'Raglan', 'VIC', -37.430, 143.380),
('3373', 'Stockyard Hill', 'VIC', -37.430, 143.380),
('3373', 'Stoneleigh', 'VIC', -37.430, 143.380),
('3373', 'Trawalla', 'VIC', -37.430, 143.380),
('3373', 'Waterloo', 'VIC', -37.430, 143.380),
('3374', 'Great Western', 'VIC', 0.000, 0.000),
('3375', 'Ballyrogan', 'VIC', -37.430, 143.130),
('3375', 'Bayindeen', 'VIC', -37.430, 143.130),
('3375', 'Buangor', 'VIC', -37.430, 143.130),
('3375', 'Middle Creek', 'VIC', -37.430, 143.130),
('3377', 'Ararat', 'VIC', -37.280, 142.930),
('3377', 'Armstrong', 'VIC', -37.280, 142.930),
('3377', 'Bulgana', 'VIC', -37.280, 142.930),
('3377', 'Cathcart', 'VIC', -37.280, 142.930),
('3377', 'Crowlands', 'VIC', -37.280, 142.930),
('3377', 'Denicull Creek', 'VIC', -37.280, 142.930),
('3377', 'Dobie', 'VIC', -37.280, 142.930),
('3377', 'Dunneworthy', 'VIC', -37.280, 142.930),
('3377', 'Eversley', 'VIC', -37.280, 142.930),
('3377', 'Langi Logan', 'VIC', -37.280, 142.930),
('3377', 'Maroona', 'VIC', -37.280, 142.930),
('3377', 'Mount Cole', 'VIC', -37.280, 142.930),
('3377', 'Mount Cole Creek', 'VIC', -37.280, 142.930),
('3377', 'Moyston', 'VIC', -37.280, 142.930),
('3377', 'Norval', 'VIC', -37.280, 142.930),
('3377', 'Rhymney', 'VIC', -37.280, 142.930),
('3377', 'Rocky Point', 'VIC', -37.280, 142.930),
('3377', 'Rossbridge', 'VIC', -37.280, 142.930),
('3377', 'Shays Flat', 'VIC', -37.280, 142.930),
('3377', 'Warrak', 'VIC', -37.280, 142.930),
('3378', 'Tatyoon', 'VIC', -37.530, 142.940),
('3378', 'Yalla-Y-Poora', 'VIC', -37.530, 142.940),
('3379', 'Bornes Hill', 'VIC', -37.530, 142.520),
('3379', 'Chatsworth', 'VIC', -37.530, 142.520),
('3379', 'Mafeking', 'VIC', -37.530, 142.520),
('3379', 'Stavely', 'VIC', -37.530, 142.520),
('3379', 'Wickliffe', 'VIC', -37.530, 142.520),
('3379', 'Willaura', 'VIC', -37.530, 142.520),
('3379', 'Willaura North', 'VIC', -37.530, 142.520),
('3380', 'Stawell', 'VIC', -37.160, 142.700),
('3380', 'Stawell West', 'VIC', -37.160, 142.700),
('3381', 'Bellellen', 'VIC', -36.940, 143.210),
('3381', 'Bellfield', 'VIC', -36.940, 143.210),
('3381', 'Black Range', 'VIC', -36.940, 143.210),
('3381', 'Fyans Creek', 'VIC', -36.940, 143.210),
('3381', 'Halls Gap', 'VIC', -36.940, 143.210),
('3381', 'Illawarra', 'VIC', -36.940, 143.210),
('3381', 'Lake Fyans', 'VIC', -36.940, 143.210),
('3381', 'Lake Lonsdale', 'VIC', -36.940, 143.210),
('3381', 'Mokepilly', 'VIC', -36.940, 143.210),
('3381', 'Mount Dryden', 'VIC', -36.940, 143.210),
('3381', 'Pomonal', 'VIC', -36.940, 143.210),
('3384', 'Barkly', 'VIC', -36.980, 143.200),
('3384', 'Concongella', 'VIC', -36.980, 143.200),
('3384', 'Frenchmans', 'VIC', -36.980, 143.200),
('3384', 'Joel Joel', 'VIC', -36.980, 143.200),
('3384', 'Joel South', 'VIC', -36.980, 143.200),
('3384', 'Landsborough', 'VIC', -36.980, 143.200),
('3384', 'Landsborough West', 'VIC', -36.980, 143.200),
('3384', 'Navarre', 'VIC', -36.980, 143.200),
('3384', 'Tulkara', 'VIC', -36.980, 143.200),
('3384', 'Wattle Creek', 'VIC', -36.980, 143.200),
('3385', 'Dadswells Bridge', 'VIC', -36.920, 142.510),
('3385', 'Deep Lead', 'VIC', -36.920, 142.510),
('3385', 'Glenorchy', 'VIC', -36.920, 142.510),
('3385', 'Ledcourt', 'VIC', -36.920, 142.510),
('3385', 'Lubeck', 'VIC', -36.920, 142.510),
('3385', 'Riachella', 'VIC', -36.920, 142.510),
('3385', 'Roses Gap', 'VIC', -36.920, 142.510),
('3385', 'Wal Wal', 'VIC', -36.920, 142.510),
('3387', 'Bolangum', 'VIC', -36.670, 142.870),
('3387', 'Callawadda', 'VIC', -36.670, 142.870),
('3387', 'Campbells Bridge', 'VIC', -36.670, 142.870),
('3387', 'Germania', 'VIC', -36.670, 142.870),
('3387', 'Greens Creek', 'VIC', -36.670, 142.870),
('3387', 'Kanya', 'VIC', -36.670, 142.870),
('3387', 'Marnoo', 'VIC', -36.670, 142.870),
('3387', 'Marnoo West', 'VIC', -36.670, 142.870),
('3387', 'Morrl Morrl', 'VIC', -36.670, 142.870),
('3387', 'Wallaloo', 'VIC', -36.670, 142.870),
('3387', 'Wallaloo East', 'VIC', -36.670, 142.870),
('3388', 'Banyena', 'VIC', -36.570, 142.820),
('3388', 'Rupanyup', 'VIC', -36.570, 142.820),
('3390', 'Kewell', 'VIC', -36.450, 142.420),
('3390', 'Murtoa', 'VIC', -36.450, 142.420),
('3391', 'Brim', 'VIC', -36.070, 142.520),
('3392', 'Boolite', 'VIC', -36.460, 142.590),
('3392', 'Minyip', 'VIC', -36.460, 142.590),
('3392', 'Sheep Hills', 'VIC', -36.460, 142.590),
('3393', 'Aubrey', 'VIC', -36.330, 142.360),
('3393', 'Bangerang', 'VIC', -36.330, 142.360),
('3393', 'Cannum', 'VIC', -36.330, 142.360),
('3393', 'Crymelon', 'VIC', -36.330, 142.360),
('3393', 'Kellalac', 'VIC', -36.330, 142.360),
('3393', 'Lah', 'VIC', -36.330, 142.360),
('3393', 'Warracknabeal', 'VIC', -36.330, 142.360),
('3393', 'Wilkur', 'VIC', -36.330, 142.360),
('3393', 'Willenabrina', 'VIC', -36.330, 142.360),
('3395', 'Beulah', 'VIC', -35.940, 142.420),
('3395', 'Kenmare', 'VIC', -35.940, 142.420),
('3395', 'Reedy Dam', 'VIC', -35.940, 142.420),
('3395', 'Rosebery', 'VIC', -35.940, 142.420),
('3396', 'Hopetoun', 'VIC', -35.730, 142.360),
('3400', 'Horsham', 'VIC', -36.710, 142.200),
('3400', 'Horsham West', 'VIC', -36.710, 142.200),
('3401', 'Blackheath', 'VIC', -36.460, 142.310),
('3401', 'Brimpaen', 'VIC', -36.460, 142.310),
('3401', 'Bungalally', 'VIC', -36.460, 142.310),
('3401', 'Cherrypool', 'VIC', -36.460, 142.310),
('3401', 'Dooen', 'VIC', -36.460, 142.310),
('3401', 'Drung', 'VIC', -36.460, 142.310),
('3401', 'Gymbowen', 'VIC', -36.460, 142.310),
('3401', 'Haven', 'VIC', -36.460, 142.310),
('3401', 'Jung', 'VIC', -36.460, 142.310),
('3401', 'Kalkee', 'VIC', -36.460, 142.310),
('3401', 'Kanagulk', 'VIC', -36.460, 142.310),
('3401', 'Karnak', 'VIC', -36.460, 142.310),
('3401', 'Laharum', 'VIC', -36.460, 142.310),
('3401', 'Longerenong', 'VIC', -36.460, 142.310),
('3401', 'Lower Norton', 'VIC', -36.460, 142.310),
('3401', 'Mckenzie Creek', 'VIC', -36.460, 142.310),
('3401', 'Mockinya', 'VIC', -36.460, 142.310),
('3401', 'Murra Warra', 'VIC', -36.460, 142.310),
('3401', 'Nurcoung', 'VIC', -36.460, 142.310),
('3401', 'Nurrabiel', 'VIC', -36.460, 142.310),
('3401', 'Pimpinio', 'VIC', -36.460, 142.310),
('3401', 'Quantong', 'VIC', -36.460, 142.310),
('3401', 'Riverside', 'VIC', -36.460, 142.310),
('3401', 'Rocklands', 'VIC', -36.460, 142.310),
('3401', 'St Helens Plains', 'VIC', -36.460, 142.310),
('3401', 'Telangatuk East', 'VIC', -36.460, 142.310),
('3401', 'Toolondo', 'VIC', -36.460, 142.310),
('3401', 'Vectis', 'VIC', -36.460, 142.310),
('3401', 'Wail', 'VIC', -36.460, 142.310),
('3401', 'Wallup', 'VIC', -36.460, 142.310),
('3401', 'Wartook', 'VIC', -36.460, 142.310),
('3401', 'Wonwondah', 'VIC', -36.460, 142.310),
('3401', 'Zumsteins', 'VIC', -36.460, 142.310),
('3402', 'Horsham', 'VIC', -37.160, 142.670),
('3407', 'Balmoral', 'VIC', -37.250, 141.840),
('3407', 'Englefield', 'VIC', -37.250, 141.840),
('3407', 'Gatum', 'VIC', -37.250, 141.840),
('3407', 'Pigeon Ponds', 'VIC', -37.250, 141.840),
('3407', 'Vasey', 'VIC', -37.250, 141.840),
('3409', 'Arapiles', 'VIC', -36.780, 141.760),
('3409', 'Clear Lake', 'VIC', -36.780, 141.760),
('3409', 'Douglas', 'VIC', -36.780, 141.760),
('3409', 'Duchembegarra', 'VIC', -36.780, 141.760),
('3409', 'Grass Flat', 'VIC', -36.780, 141.760),
('3409', 'Jilpanger', 'VIC', -36.780, 141.760),
('3409', 'Miga Lake', 'VIC', -36.780, 141.760),
('3409', 'Mitre', 'VIC', -36.780, 141.760),
('3409', 'Natimuk', 'VIC', -36.780, 141.760),
('3409', 'Noradjuha', 'VIC', -36.780, 141.760),
('3409', 'Tooan', 'VIC', -36.780, 141.760),
('3409', 'Wombelano', 'VIC', -36.780, 141.760),
('3412', 'Goroke', 'VIC', -36.750, 141.470),
('3413', 'Minimay', 'VIC', -36.710, 141.180),
('3413', 'Neuarpurr', 'VIC', -36.710, 141.180),
('3413', 'Ozenkadnook', 'VIC', -36.710, 141.180),
('3413', 'Peronne', 'VIC', -36.710, 141.180),
('3414', 'Antwerp', 'VIC', -36.300, 142.020),
('3414', 'Dimboola', 'VIC', -36.300, 142.020),
('3414', 'Tarranyurk', 'VIC', -36.300, 142.020),
('3415', 'Miram', 'VIC', -36.380, 141.360),
('3418', 'Broughton', 'VIC', -36.170, 141.330),
('3418', 'Gerang Gerung', 'VIC', -36.170, 141.330),
('3418', 'Glenlee', 'VIC', -36.170, 141.330),
('3418', 'Kiata', 'VIC', -36.170, 141.330),
('3418', 'Lawloit', 'VIC', -36.170, 141.330),
('3418', 'Little Desert', 'VIC', -36.170, 141.330),
('3418', 'Lorquon', 'VIC', -36.170, 141.330),
('3418', 'Netherby', 'VIC', -36.170, 141.330),
('3418', 'Nhill', 'VIC', -36.170, 141.330),
('3418', 'Yanac', 'VIC', -36.170, 141.330),
('3419', 'Kaniva', 'VIC', -36.380, 141.240),
('3420', 'Lillimur', 'VIC', -36.360, 141.120),
('3420', 'Serviceton', 'VIC', -36.360, 141.120),
('3420', 'Telopea Downs', 'VIC', -36.360, 141.120),
('3423', 'Jeparit', 'VIC', -36.140, 141.990),
('3424', 'Albacutya', 'VIC', -35.690, 141.970),
('3424', 'Rainbow', 'VIC', -35.690, 141.970),
('3424', 'Yaapeet', 'VIC', -35.690, 141.970),
('3427', 'Diggers Rest', 'VIC', -37.630, 144.720),
('3428', 'Bulla', 'VIC', -37.640, 144.800),
('3429', 'Sunbury', 'VIC', -37.580, 144.730),
('3429', 'Wildwood', 'VIC', -37.580, 144.730),
('3430', 'Clarkefield', 'VIC', -37.480, 144.750),
('3431', 'Riddells Creek', 'VIC', -37.460, 144.670),
('3432', 'Bolinda', 'VIC', -37.430, 144.780),
('3433', 'Monegeetta', 'VIC', 0.000, 0.000),
('3434', 'Cherokee', 'VIC', -37.390, 144.640),
('3434', 'Kerrie', 'VIC', -37.390, 144.640),
('3434', 'Romsey', 'VIC', -37.390, 144.640),
('3434', 'Springfield', 'VIC', -37.390, 144.640),
('3435', 'Benloch', 'VIC', -37.190, 144.690),
('3435', 'Goldie', 'VIC', -37.190, 144.690),
('3435', 'Lancefield', 'VIC', -37.190, 144.690),
('3435', 'Nulla Vale', 'VIC', -37.190, 144.690),
('3437', 'Bullengarook', 'VIC', -37.520, 144.480),
('3437', 'Gisborne', 'VIC', -37.520, 144.480),
('3437', 'Gisborne South', 'VIC', -37.520, 144.480),
('3438', 'New Gisborne', 'VIC', -37.460, 144.600),
('3440', 'Macedon', 'VIC', -37.400, 144.590),
('3441', 'Mount Macedon', 'VIC', -37.400, 144.590),
('3442', 'Ashbourne', 'VIC', -37.390, 144.450),
('3442', 'Cadello', 'VIC', -37.390, 144.450),
('3442', 'Carlsruhe', 'VIC', -37.390, 144.450),
('3442', 'Cobaw', 'VIC', -37.390, 144.450),
('3442', 'Hesket', 'VIC', -37.390, 144.450),
('3442', 'Newham', 'VIC', -37.390, 144.450),
('3442', 'Rochford', 'VIC', -37.390, 144.450),
('3442', 'Woodend', 'VIC', -37.390, 144.450),
('3442', 'Woodend North', 'VIC', -37.390, 144.450),
('3444', 'Barfold', 'VIC', -37.090, 144.510),
('3444', 'Baynton', 'VIC', -37.090, 144.510),
('3444', 'Baynton East', 'VIC', -37.090, 144.510),
('3444', 'Edgecombe', 'VIC', -37.090, 144.510),
('3444', 'Glenhope', 'VIC', -37.090, 144.510),
('3444', 'Greenhill', 'VIC', -37.090, 144.510),
('3444', 'Kyneton', 'VIC', -37.090, 144.510),
('3444', 'Kyneton South', 'VIC', -37.090, 144.510),
('3444', 'Langley', 'VIC', -37.090, 144.510),
('3444', 'Lauriston', 'VIC', -37.090, 144.510),
('3444', 'Lyal', 'VIC', -37.090, 144.510),
('3444', 'Metcalfe East', 'VIC', -37.090, 144.510),
('3444', 'Mia Mia', 'VIC', -37.090, 144.510),
('3444', 'Myrtle Creek', 'VIC', -37.090, 144.510),
('3444', 'Pastoria', 'VIC', -37.090, 144.510),
('3444', 'Pastoria East', 'VIC', -37.090, 144.510),
('3444', 'Pipers Creek', 'VIC', -37.090, 144.510),
('3444', 'Redesdale', 'VIC', -37.090, 144.510),
('3444', 'Sidonia', 'VIC', -37.090, 144.510),
('3444', 'Spring Hill', 'VIC', -37.090, 144.510),
('3444', 'Tylden', 'VIC', -37.090, 144.510),
('3444', 'Tylden South', 'VIC', -37.090, 144.510),
('3446', 'Drummond North', 'VIC', -37.200, 144.290),
('3446', 'Malmsbury', 'VIC', -37.200, 144.290),
('3447', 'Taradale', 'VIC', -37.130, 144.360),
('3448', 'Elphinstone', 'VIC', -37.110, 144.340),
('3448', 'Metcalfe', 'VIC', -37.110, 144.340),
('3448', 'Sutton Grange', 'VIC', -37.110, 144.340),
('3450', 'Castlemaine', 'VIC', -37.060, 144.220),
('3450', 'Moonlight Flat', 'VIC', -37.060, 144.220),
('3451', 'Barkers Creek', 'VIC', -37.030, 144.240),
('3451', 'Campbells Creek', 'VIC', -37.030, 144.240),
('3451', 'Chewton', 'VIC', -37.030, 144.240),
('3451', 'Chewton Bushlands', 'VIC', -37.030, 144.240),
('3451', 'Faraday', 'VIC', -37.030, 144.240),
('3451', 'Fryerstown', 'VIC', -37.030, 144.240),
('3451', 'Glenluce', 'VIC', -37.030, 144.240),
('3451', 'Golden Point', 'VIC', -37.030, 144.240),
('3451', 'Gower', 'VIC', -37.030, 144.240),
('3451', 'Guildford', 'VIC', -37.030, 144.240),
('3451', 'Irishtown', 'VIC', -37.030, 144.240),
('3451', 'Mckenzie Hill', 'VIC', -37.030, 144.240),
('3451', 'Muckleford', 'VIC', -37.030, 144.240),
('3451', 'Tarilta', 'VIC', -37.030, 144.240),
('3451', 'Vaughan', 'VIC', -37.030, 144.240),
('3451', 'Yapeen', 'VIC', -37.030, 144.240),
('3453', 'Harcourt', 'VIC', -37.000, 144.260),
('3453', 'Harcourt North', 'VIC', -37.000, 144.260),
('3453', 'Ravenswood', 'VIC', -37.000, 144.260),
('3453', 'Ravenswood South', 'VIC', -37.000, 144.260),
('3458', 'Barrys Reef', 'VIC', -37.450, 144.290),
('3458', 'Blackwood', 'VIC', -37.450, 144.290),
('3458', 'Fern Hill', 'VIC', -37.450, 144.290),
('3458', 'Lerderderg', 'VIC', -37.450, 144.290),
('3458', 'Little Hampton', 'VIC', -37.450, 144.290),
('3458', 'Newbury', 'VIC', -37.450, 144.290),
('3458', 'North Blackwood', 'VIC', -37.450, 144.290),
('3458', 'Trentham', 'VIC', -37.450, 144.290),
('3458', 'Trentham East', 'VIC', -37.450, 144.290),
('3460', 'Basalt', 'VIC', -37.310, 144.090),
('3460', 'Daylesford', 'VIC', -37.310, 144.090),
('3461', 'Bullarto', 'VIC', -37.390, 144.220),
('3461', 'Bullarto South', 'VIC', -37.390, 144.220),
('3461', 'Clydesdale', 'VIC', -37.390, 144.220),
('3461', 'Coomoora', 'VIC', -37.390, 144.220),
('3461', 'Denver', 'VIC', -37.390, 144.220),
('3461', 'Drummond', 'VIC', -37.390, 144.220),
('3461', 'Dry Diggings', 'VIC', -37.390, 144.220),
('3461', 'Eganstown', 'VIC', -37.390, 144.220),
('3461', 'Elevated Plains', 'VIC', -37.390, 144.220),
('3461', 'Franklinford', 'VIC', -37.390, 144.220),
('3461', 'Glenlyon', 'VIC', -37.390, 144.220),
('3461', 'Hepburn', 'VIC', -37.390, 144.220),
('3461', 'Hepburn Springs', 'VIC', -37.390, 144.220),
('3461', 'Korweinguboora', 'VIC', -37.390, 144.220),
('3461', 'Leonards Hill', 'VIC', -37.390, 144.220),
('3461', 'Lyonville', 'VIC', -37.390, 144.220),
('3461', 'Mount Franklin', 'VIC', -37.390, 144.220),
('3461', 'Musk', 'VIC', -37.390, 144.220),
('3461', 'Musk Vale', 'VIC', -37.390, 144.220),
('3461', 'Porcupine Ridge', 'VIC', -37.390, 144.220),
('3461', 'Sailors Falls', 'VIC', -37.390, 144.220),
('3461', 'Sailors Hill', 'VIC', -37.390, 144.220),
('3461', 'Shepherds Flat', 'VIC', -37.390, 144.220),
('3461', 'Spargo Creek', 'VIC', -37.390, 144.220),
('3461', 'Strangways', 'VIC', -37.390, 144.220),
('3461', 'Wheatsheaf', 'VIC', -37.390, 144.220),
('3461', 'Yandoit', 'VIC', -37.390, 144.220),
('3461', 'Yandoit Hills', 'VIC', -37.390, 144.220),
('3462', 'Green Gully', 'VIC', -37.110, 144.100),
('3462', 'Joyces Creek', 'VIC', -37.110, 144.100),
('3462', 'Muckleford South', 'VIC', -37.110, 144.100),
('3462', 'Newstead', 'VIC', -37.110, 144.100),
('3462', 'Sandon', 'VIC', -37.110, 144.100),
('3462', 'Welshmans Reef', 'VIC', -37.110, 144.100),
('3463', 'Baringhup', 'VIC', -36.980, 143.950),
('3463', 'Baringhup West', 'VIC', -36.980, 143.950),
('3463', 'Bradford', 'VIC', -36.980, 143.950),
('3463', 'Eastville', 'VIC', -36.980, 143.950),
('3463', 'Laanecoorie', 'VIC', -36.980, 143.950),
('3463', 'Maldon', 'VIC', -36.980, 143.950),
('3463', 'Neereman', 'VIC', -36.980, 143.950),
('3463', 'Nuggetty', 'VIC', -36.980, 143.950),
('3463', 'Shelbourne', 'VIC', -36.980, 143.950),
('3463', 'Tarrengower', 'VIC', -36.980, 143.950),
('3463', 'Walmer', 'VIC', -36.980, 143.950),
('3463', 'Woodstock West', 'VIC', -36.980, 143.950),
('3464', 'Carisbrook', 'VIC', -37.010, 143.800),
('3465', 'Adelaide Lead', 'VIC', -37.070, 143.680),
('3465', 'Alma', 'VIC', -37.070, 143.680),
('3465', 'Bowenvale', 'VIC', -37.070, 143.680),
('3465', 'Bung Bong', 'VIC', -37.070, 143.680),
('3465', 'Cotswold', 'VIC', -37.070, 143.680),
('3465', 'Craigie', 'VIC', -37.070, 143.680),
('3465', 'Daisy Hill', 'VIC', -37.070, 143.680),
('3465', 'Flagstaff', 'VIC', -37.070, 143.680),
('3465', 'Golden Point', 'VIC', -37.070, 143.680),
('3465', 'Havelock', 'VIC', -37.070, 143.680),
('3465', 'Homebush', 'VIC', -37.070, 143.680),
('3465', 'Majorca', 'VIC', -37.070, 143.680),
('3465', 'Maryborough', 'VIC', -37.070, 143.680),
('3465', 'Moolort', 'VIC', -37.070, 143.680),
('3465', 'Moonlight Flat', 'VIC', -37.070, 143.680),
('3465', 'Natte Yallock', 'VIC', -37.070, 143.680),
('3465', 'Rathscar', 'VIC', -37.070, 143.680),
('3465', 'Rathscar West', 'VIC', -37.070, 143.680),
('3465', 'Simson', 'VIC', -37.070, 143.680),
('3465', 'Timor', 'VIC', -37.070, 143.680),
('3465', 'Timor West', 'VIC', -37.070, 143.680),
('3465', 'Wareek', 'VIC', -37.070, 143.680),
('3467', 'Avoca', 'VIC', -37.090, 143.470),
('3468', 'Amphitheatre', 'VIC', -37.180, 143.400),
('3468', 'Mount Lonarch', 'VIC', -37.180, 143.400),
('3469', 'Elmhurst', 'VIC', -37.180, 143.250),
('3469', 'Glenlofty', 'VIC', -37.180, 143.250),
('3469', 'Glenlogie', 'VIC', -37.180, 143.250),
('3469', 'Glenpatrick', 'VIC', -37.180, 143.250),
('3469', 'Nowhere Creek', 'VIC', -37.180, 143.250),
('3472', 'Bet Bet', 'VIC', -36.920, 143.760),
('3472', 'Betley', 'VIC', -36.920, 143.760),
('3472', 'Bromley', 'VIC', -36.920, 143.760),
('3472', 'Dunluce', 'VIC', -36.920, 143.760),
('3472', 'Dunolly', 'VIC', -36.920, 143.760),
('3472', 'Eddington', 'VIC', -36.920, 143.760),
('3472', 'Goldsborough', 'VIC', -36.920, 143.760),
('3472', 'Inkerman', 'VIC', -36.920, 143.760),
('3472', 'Mcintyre', 'VIC', -36.920, 143.760),
('3472', 'Moliagul', 'VIC', -36.920, 143.760),
('3472', 'Mount Hooghly', 'VIC', -36.920, 143.760),
('3475', 'Archdale', 'VIC', -36.830, 143.500),
('3475', 'Archdale Junction', 'VIC', -36.830, 143.500),
('3475', 'Bealiba', 'VIC', -36.830, 143.500),
('3475', 'Burkes Flat', 'VIC', -36.830, 143.500),
('3475', 'Cochranes Creek', 'VIC', -36.830, 143.500),
('3475', 'Emu', 'VIC', -36.830, 143.500),
('3475', 'Logan', 'VIC', -36.830, 143.500),
('3477', 'Avon Plains', 'VIC', 0.000, 0.000),
('3477', 'Beazleys Bridge', 'VIC', 0.000, 0.000),
('3477', 'Carapooee', 'VIC', 0.000, 0.000),
('3477', 'Carapooee West', 'VIC', 0.000, 0.000),
('3477', 'Coonooer Bridge', 'VIC', 0.000, 0.000),
('3477', 'Coonooer West', 'VIC', 0.000, 0.000),
('3477', 'Dalyenong', 'VIC', 0.000, 0.000),
('3477', 'Gooroc', 'VIC', 0.000, 0.000),
('3477', 'Gowar East', 'VIC', 0.000, 0.000),
('3477', 'Grays Bridge', 'VIC', 0.000, 0.000),
('3477', 'Gre Gre', 'VIC', 0.000, 0.000),
('3477', 'Gre Gre North', 'VIC', 0.000, 0.000),
('3477', 'Gre Gre South', 'VIC', 0.000, 0.000),
('3477', 'Kooreh', 'VIC', 0.000, 0.000),
('3477', 'Marnoo East', 'VIC', 0.000, 0.000),
('3477', 'Moolerr', 'VIC', 0.000, 0.000),
('3477', 'Moyreisk', 'VIC', 0.000, 0.000),
('3477', 'Paradise', 'VIC', 0.000, 0.000),
('3477', 'Redbank', 'VIC', 0.000, 0.000),
('3477', 'Rostron', 'VIC', 0.000, 0.000),
('3477', 'Slaty Creek', 'VIC', 0.000, 0.000),
('3477', 'St Arnaud East', 'VIC', 0.000, 0.000),
('3477', 'St Arnaud North', 'VIC', 0.000, 0.000),
('3477', 'Stuart Mill', 'VIC', 0.000, 0.000),
('3477', 'Sutherland', 'VIC', 0.000, 0.000),
('3477', 'Swanwater', 'VIC', 0.000, 0.000),
('3477', 'Tottington', 'VIC', 0.000, 0.000),
('3477', 'Traynors Lagoon', 'VIC', 0.000, 0.000),
('3477', 'Winjallok', 'VIC', 0.000, 0.000),
('3477', 'York Plains', 'VIC', 0.000, 0.000),
('3478', 'Dooboobetic', 'VIC', -36.540, 142.920),
('3478', 'Medlyn', 'VIC', -36.540, 142.920),
('3478', 'Moonambel', 'VIC', -36.540, 142.920),
('3478', 'Percydale', 'VIC', -36.540, 142.920),
('3478', 'St Arnaud', 'VIC', -36.540, 142.920),
('3478', 'Tanwood', 'VIC', -36.540, 142.920),
('3478', 'Warrenmang', 'VIC', -36.540, 142.920),
('3478', 'Yawong Hills', 'VIC', -36.540, 142.920),
('3480', 'Areegra', 'VIC', -36.240, 142.690),
('3480', 'Carron', 'VIC', -36.240, 142.690),
('3480', 'Cope Cope', 'VIC', -36.240, 142.690),
('3480', 'Corack', 'VIC', -36.240, 142.690),
('3480', 'Corack East', 'VIC', -36.240, 142.690),
('3480', 'Donald', 'VIC', -36.240, 142.690),
('3480', 'Gil Gil', 'VIC', -36.240, 142.690),
('3480', 'Jeffcott', 'VIC', -36.240, 142.690),
('3480', 'Jeffcott North', 'VIC', -36.240, 142.690),
('3480', 'Laen', 'VIC', -36.240, 142.690),
('3480', 'Laen East', 'VIC', -36.240, 142.690),
('3480', 'Laen North', 'VIC', -36.240, 142.690),
('3480', 'Lake Buloke', 'VIC', -36.240, 142.690),
('3480', 'Lawler', 'VIC', -36.240, 142.690),
('3480', 'Litchfield', 'VIC', -36.240, 142.690),
('3480', 'Rich Avon', 'VIC', -36.240, 142.690),
('3480', 'Rich Avon East', 'VIC', -36.240, 142.690),
('3480', 'Rich Avon West', 'VIC', -36.240, 142.690),
('3480', 'Swanwater West', 'VIC', -36.240, 142.690),
('3482', 'Massey', 'VIC', -36.230, 142.860),
('3482', 'Morton Plains', 'VIC', -36.230, 142.860),
('3482', 'Warmur', 'VIC', -36.230, 142.860),
('3482', 'Watchem', 'VIC', -36.230, 142.860),
('3482', 'Watchem West', 'VIC', -36.230, 142.860),
('3483', 'Ballapur', 'VIC', -35.980, 142.750),
('3483', 'Birchip', 'VIC', -35.980, 142.750),
('3483', 'Birchip West', 'VIC', -35.980, 142.750),
('3483', 'Curyo', 'VIC', -35.980, 142.750),
('3483', 'Jil Jil', 'VIC', -35.980, 142.750),
('3483', 'Karyrie', 'VIC', -35.980, 142.750),
('3483', 'Kinnabulla', 'VIC', -35.980, 142.750),
('3483', 'Marlbed', 'VIC', -35.980, 142.750),
('3483', 'Narraport', 'VIC', -35.980, 142.750),
('3483', 'Whirily', 'VIC', -35.980, 142.750),
('3485', 'Banyan', 'VIC', -35.640, 142.770),
('3485', 'Watchupga', 'VIC', -35.640, 142.770),
('3485', 'Willangie', 'VIC', -35.640, 142.770),
('3485', 'Woomelang', 'VIC', -35.640, 142.770),
('3487', 'Lascelles', 'VIC', -35.610, 142.580),
('3488', 'Speed', 'VIC', -35.400, 142.440),
('3488', 'Turriff', 'VIC', -35.400, 142.440),
('3488', 'Turriff East', 'VIC', -35.400, 142.440),
('3489', 'Tempy', 'VIC', -35.340, 142.480),
('3490', 'Big Desert', 'VIC', -35.200, 141.600),
('3490', 'Boinka', 'VIC', -35.200, 141.600),
('3490', 'Kulwin', 'VIC', -35.200, 141.600),
('3490', 'Mittyack', 'VIC', -35.200, 141.600),
('3490', 'Murray-Sunset', 'VIC', -35.200, 141.600),
('3490', 'Ouyen', 'VIC', -35.200, 141.600),
('3490', 'Torrita', 'VIC', -35.200, 141.600),
('3490', 'Tutye', 'VIC', -35.200, 141.600),
('3491', 'Patchewollock', 'VIC', -35.380, 142.190),
('3494', 'Carwarp', 'VIC', -34.460, 142.230),
('3494', 'Colignan', 'VIC', -34.460, 142.230),
('3494', 'Iraak', 'VIC', -34.460, 142.230),
('3494', 'Nangiloc', 'VIC', -34.460, 142.230),
('3496', 'Cardross', 'VIC', -34.290, 142.150),
('3496', 'Cullulleraine', 'VIC', -34.290, 142.150),
('3496', 'Lindsay Point', 'VIC', -34.290, 142.150),
('3496', 'Meringur', 'VIC', -34.290, 142.150),
('3496', 'Merrinee', 'VIC', -34.290, 142.150),
('3496', 'Neds Corner', 'VIC', -34.290, 142.150),
('3496', 'Red Cliffs', 'VIC', -34.290, 142.150),
('3496', 'Sunnycliffs', 'VIC', -34.290, 142.150),
('3496', 'Werrimull', 'VIC', -34.290, 142.150),
('3498', 'Irymple', 'VIC', -34.230, 142.180),
('3500', 'Mildura', 'VIC', -34.180, 142.160),
('3500', 'Mildura West', 'VIC', -34.180, 142.160),
('3500', 'Paringi', 'NSW', -34.180, 142.160),
('3501', 'Hattah', 'VIC', -34.850, 142.330),
('3501', 'Koorlong', 'VIC', -34.850, 142.330),
('3501', 'Mildura Centre Plaza', 'VIC', -34.850, 142.330),
('3501', 'Mildura South', 'VIC', -34.850, 142.330),
('3501', 'Nichols Point', 'VIC', -34.850, 142.330),
('3502', 'Mildura', 'VIC', -37.970, 145.260),
('3505', 'Birdwoodton', 'VIC', -34.200, 142.060),
('3505', 'Cabarita', 'VIC', -34.200, 142.060),
('3505', 'Merbein', 'VIC', -34.200, 142.060),
('3505', 'Merbein South', 'VIC', -34.200, 142.060),
('3505', 'Merbein West', 'VIC', -34.200, 142.060),
('3505', 'Wargan', 'VIC', -34.200, 142.060),
('3505', 'Yelta', 'VIC', -34.200, 142.060),
('3506', 'Cowangie', 'VIC', -35.280, 141.400),
('3507', 'Walpeup', 'VIC', -35.190, 142.020),
('3509', 'Linga', 'VIC', -35.170, 141.690),
('3509', 'Underbool', 'VIC', -35.170, 141.690),
('3512', 'Carina', 'VIC', -35.220, 141.090),
('3512', 'Murrayville', 'VIC', -35.220, 141.090),
('3512', 'Panitya', 'VIC', -35.220, 141.090),
('3515', 'Marong', 'VIC', -36.740, 144.130),
('3515', 'Shelbourne', 'VIC', -36.740, 144.130),
('3515', 'Wilsons Hill', 'VIC', -36.740, 144.130),
('3516', 'Bridgewater', 'VIC', -36.590, 143.920),
('3516', 'Bridgewater North', 'VIC', -36.590, 143.920),
('3516', 'Bridgewater On Loddon', 'VIC', -36.590, 143.920),
('3516', 'Derby', 'VIC', -36.590, 143.920),
('3516', 'Leichardt', 'VIC', -36.590, 143.920),
('3516', 'Yarraberb', 'VIC', -36.590, 143.920),
('3517', 'Bears Lagoon', 'VIC', -36.300, 143.920),
('3517', 'Brenanah', 'VIC', -36.300, 143.920),
('3517', 'Glenalbyn', 'VIC', -36.300, 143.920),
('3517', 'Inglewood', 'VIC', -36.300, 143.920),
('3517', 'Jarklin', 'VIC', -36.300, 143.920),
('3517', 'Kingower', 'VIC', -36.300, 143.920),
('3517', 'Kurting', 'VIC', -36.300, 143.920),
('3517', 'Powlett Plains', 'VIC', -36.300, 143.920),
('3517', 'Rheola', 'VIC', -36.300, 143.920),
('3517', 'Salisbury West', 'VIC', -36.300, 143.920),
('3517', 'Serpentine', 'VIC', -36.300, 143.920),
('3518', 'Berrimal', 'VIC', -36.500, 143.460),
('3518', 'Borung', 'VIC', -36.500, 143.460),
('3518', 'Fentons Creek', 'VIC', -36.500, 143.460),
('3518', 'Fernihurst', 'VIC', -36.500, 143.460),
('3518', 'Fiery Flat', 'VIC', -36.500, 143.460),
('3518', 'Kurraca', 'VIC', -36.500, 143.460),
('3518', 'Kurraca West', 'VIC', -36.500, 143.460),
('3518', 'Mysia', 'VIC', -36.500, 143.460),
('3518', 'Nine Mile', 'VIC', -36.500, 143.460),
('3518', 'Richmond Plains', 'VIC', -36.500, 143.460),
('3518', 'Skinners Flat', 'VIC', -36.500, 143.460),
('3518', 'Wedderburn', 'VIC', -36.500, 143.460),
('3518', 'Wedderburn Junction', 'VIC', -36.500, 143.460),
('3518', 'Wehla', 'VIC', -36.500, 143.460),
('3518', 'Woolshed Flat', 'VIC', -36.500, 143.460),
('3518', 'Woosang', 'VIC', -36.500, 143.460),
('3520', 'Kinypanial', 'VIC', -36.330, 143.840),
('3520', 'Korong Vale', 'VIC', -36.330, 143.840),
('3521', 'Pyalong', 'VIC', -37.120, 144.880),
('3522', 'Glenhope East', 'VIC', -37.130, 144.750),
('3522', 'Tooborac', 'VIC', -37.130, 144.750),
('3523', 'Argyle', 'VIC', -36.940, 144.730),
('3523', 'Costerfield', 'VIC', -36.940, 144.730),
('3523', 'Derrinal', 'VIC', -36.940, 144.730),
('3523', 'Heathcote', 'VIC', -36.940, 144.730),
('3523', 'Heathcote South', 'VIC', -36.940, 144.730),
('3523', 'Knowsley', 'VIC', -36.940, 144.730),
('3523', 'Ladys Pass', 'VIC', -36.940, 144.730),
('3523', 'Moormbool West', 'VIC', -36.940, 144.730),
('3523', 'Mount Camel', 'VIC', -36.940, 144.730),
('3523', 'Redcastle', 'VIC', -36.940, 144.730),
('3525', 'Barrakee', 'VIC', -36.270, 143.430),
('3525', 'Buckrabanyule', 'VIC', -36.270, 143.430),
('3525', 'Charlton', 'VIC', -36.270, 143.430),
('3525', 'Chirrip', 'VIC', -36.270, 143.430),
('3525', 'Granite Flat', 'VIC', -36.270, 143.430),
('3525', 'Lake Marmal', 'VIC', -36.270, 143.430),
('3525', 'Nareewillock', 'VIC', -36.270, 143.430),
('3525', 'Terrappee', 'VIC', -36.270, 143.430),
('3525', 'Wooroonook', 'VIC', -36.270, 143.430),
('3525', 'Wychitella', 'VIC', -36.270, 143.430),
('3525', 'Wychitella North', 'VIC', -36.270, 143.430),
('3525', 'Yeungroon', 'VIC', -36.270, 143.430),
('3525', 'Yeungroon East', 'VIC', -36.270, 143.430),
('3527', 'Bunguluke', 'VIC', -36.060, 143.390),
('3527', 'Dumosa', 'VIC', -36.060, 143.390),
('3527', 'Glenloth', 'VIC', -36.060, 143.390),
('3527', 'Glenloth East', 'VIC', -36.060, 143.390),
('3527', 'Jeruk', 'VIC', -36.060, 143.390),
('3527', 'Ninyeunook', 'VIC', -36.060, 143.390),
('3527', 'Teddywaddy', 'VIC', -36.060, 143.390),
('3527', 'Teddywaddy West', 'VIC', -36.060, 143.390),
('3527', 'Thalia', 'VIC', -36.060, 143.390),
('3527', 'Towaninny', 'VIC', -36.060, 143.390),
('3527', 'Towaninny South', 'VIC', -36.060, 143.390),
('3527', 'Wycheproof', 'VIC', -36.060, 143.390),
('3527', 'Wycheproof South', 'VIC', -36.060, 143.390),
('3529', 'Kalpienung', 'VIC', -35.780, 143.260),
('3529', 'Nullawil', 'VIC', -35.780, 143.260),
('3530', 'Culgoa', 'VIC', -35.720, 143.110),
('3530', 'Sutton', 'VIC', -35.720, 143.110),
('3530', 'Wangie', 'VIC', -35.720, 143.110),
('3530', 'Warne', 'VIC', -35.720, 143.110),
('3531', 'Berriwillock', 'VIC', -35.640, 142.990),
('3531', 'Boigbeat', 'VIC', -35.640, 142.990),
('3533', 'Bimbourie', 'VIC', -35.360, 142.790),
('3533', 'Lake Tyrrell', 'VIC', -35.360, 142.790),
('3533', 'Myall', 'VIC', -35.360, 142.790),
('3533', 'Nandaly', 'VIC', -35.360, 142.790),
('3533', 'Ninda', 'VIC', -35.360, 142.790),
('3533', 'Nyarrin', 'VIC', -35.360, 142.790),
('3533', 'Pier Milan', 'VIC', -35.360, 142.790),
('3533', 'Sea Lake', 'VIC', -35.360, 142.790),
('3533', 'Straten', 'VIC', -35.360, 142.790),
('3533', 'Tyenna', 'VIC', -35.360, 142.790),
('3533', 'Tyrrell', 'VIC', -35.360, 142.790),
('3533', 'Tyrrell Downs', 'VIC', -35.360, 142.790),
('3537', 'Barraport', 'VIC', -36.010, 143.670),
('3537', 'Barraport West', 'VIC', -36.010, 143.670),
('3537', 'Boort', 'VIC', -36.010, 143.670),
('3537', 'Canary Island', 'VIC', -36.010, 143.670),
('3537', 'Catumnal', 'VIC', -36.010, 143.670),
('3537', 'Gredgwin', 'VIC', -36.010, 143.670),
('3537', 'Leaghur', 'VIC', -36.010, 143.670),
('3537', 'Minmindie', 'VIC', -36.010, 143.670),
('3537', 'Yando', 'VIC', -36.010, 143.670),
('3540', 'Cannie', 'VIC', -35.760, 143.450),
('3540', 'Oakvale', 'VIC', -35.760, 143.450),
('3540', 'Quambatook', 'VIC', -35.760, 143.450),
('3542', 'Cokum', 'VIC', -35.760, 143.280),
('3542', 'Lalbert', 'VIC', -35.760, 143.280),
('3542', 'Tittybong', 'VIC', -35.760, 143.280),
('3544', 'Chinangin', 'VIC', -35.510, 143.200),
('3544', 'Gowanford', 'VIC', -35.510, 143.200),
('3544', 'Murnungin', 'VIC', -35.510, 143.200),
('3544', 'Springfield', 'VIC', -35.510, 143.200),
('3544', 'Ultima', 'VIC', -35.510, 143.200),
('3544', 'Ultima East', 'VIC', -35.510, 143.200),
('3544', 'Waitchie', 'VIC', -35.510, 143.200),
('3546', 'Bolton', 'VIC', -34.960, 142.880),
('3546', 'Chinkapook', 'VIC', -34.960, 142.880),
('3546', 'Cocamba', 'VIC', -34.960, 142.880),
('3546', 'Gerahmin', 'VIC', -34.960, 142.880),
('3546', 'Manangatang', 'VIC', -34.960, 142.880),
('3546', 'Turoar', 'VIC', -34.960, 142.880),
('3546', 'Winnambool', 'VIC', -34.960, 142.880),
('3549', 'Annuello', 'VIC', -34.850, 142.830),
('3549', 'Bannerton', 'VIC', -34.850, 142.830),
('3549', 'Happy Valley', 'VIC', -34.850, 142.830),
('3549', 'Liparoo', 'VIC', -34.850, 142.830),
('3549', 'Robinvale', 'VIC', -34.850, 142.830),
('3549', 'Robinvale Irrigation District Section B', 'VIC', -34.850, 142.830),
('3549', 'Robinvale Irrigation District Section C', 'VIC', -34.850, 142.830),
('3549', 'Robinvale Irrigation District Section D', 'VIC', -34.850, 142.830),
('3549', 'Robinvale Irrigation District Section E', 'VIC', -34.850, 142.830),
('3549', 'Tol Tol', 'VIC', -34.850, 142.830),
('3549', 'Wandown', 'VIC', -34.850, 142.830),
('3549', 'Wemen', 'VIC', -34.850, 142.830),
('3550', 'Bendigo', 'VIC', -36.760, 144.280),
('3550', 'Bendigo South', 'VIC', -36.760, 144.280),
('3550', 'East Bendigo', 'VIC', -36.760, 144.280),
('3550', 'Flora Hill', 'VIC', -36.760, 144.280),
('3550', 'Ironbark', 'VIC', -36.760, 144.280),
('3550', 'Kennington', 'VIC', -36.760, 144.280),
('3550', 'Long Gully', 'VIC', -36.760, 144.280),
('3550', 'North Bendigo', 'VIC', -36.760, 144.280),
('3550', 'Quarry Hill', 'VIC', -36.760, 144.280),
('3550', 'Sandhurst East', 'VIC', -36.760, 144.280),
('3550', 'Spring Gully', 'VIC', -36.760, 144.280),
('3550', 'Strathdale', 'VIC', -36.760, 144.280),
('3550', 'West Bendigo', 'VIC', -36.760, 144.280),
('3550', 'White Hills', 'VIC', -36.760, 144.280),
('3551', 'Arnold', 'VIC', -36.690, 143.870),
('3551', 'Arnold West', 'VIC', -36.690, 143.870),
('3551', 'Ascot', 'VIC', -36.690, 143.870),
('3551', 'Axe Creek', 'VIC', -36.690, 143.870),
('3551', 'Axedale', 'VIC', -36.690, 143.870),
('3551', 'Bagshot', 'VIC', -36.690, 143.870),
('3551', 'Bagshot North', 'VIC', -36.690, 143.870),
('3551', 'Bendigo Forward', 'VIC', -36.690, 143.870),
('3551', 'Cornella', 'VIC', -36.690, 143.870),
('3551', 'Emu Creek', 'VIC', -36.690, 143.870),
('3551', 'Eppalock', 'VIC', -36.690, 143.870),
('3551', 'Epsom', 'VIC', -36.690, 143.870),
('3551', 'Huntly', 'VIC', -36.690, 143.870),
('3551', 'Huntly North', 'VIC', -36.690, 143.870),
('3551', 'Junortoun', 'VIC', -36.690, 143.870),
('3551', 'Kimbolton', 'VIC', -36.690, 143.870),
('3551', 'Lake Eppalock', 'VIC', -36.690, 143.870),
('3551', 'Llanelly', 'VIC', -36.690, 143.870),
('3551', 'Lockwood', 'VIC', -36.690, 143.870),
('3551', 'Lockwood South', 'VIC', -36.690, 143.870),
('3551', 'Longlea', 'VIC', -36.690, 143.870),
('3551', 'Maiden Gully', 'VIC', -36.690, 143.870),
('3551', 'Mandurang', 'VIC', -36.690, 143.870),
('3551', 'Mandurang South', 'VIC', -36.690, 143.870),
('3551', 'Minto', 'VIC', -36.690, 143.870),
('3551', 'Murphys Creek', 'VIC', -36.690, 143.870),
('3551', 'Myola', 'VIC', -36.690, 143.870),
('3551', 'Newbridge', 'VIC', -36.690, 143.870),
('3551', 'Painswick', 'VIC', -36.690, 143.870),
('3551', 'Sedgwick', 'VIC', -36.690, 143.870),
('3551', 'Strathfieldsaye', 'VIC', -36.690, 143.870),
('3551', 'Tarnagulla', 'VIC', -36.690, 143.870),
('3551', 'Toolleen', 'VIC', -36.690, 143.870),
('3551', 'Waanyarra', 'VIC', -36.690, 143.870),
('3551', 'Wellsford', 'VIC', -36.690, 143.870),
('3551', 'Woodstock On Loddon', 'VIC', -36.690, 143.870),
('3552', 'Bendigo', 'VIC', -37.830, 145.010),
('3554', 'Bendigo DC', 'VIC', 0.000, 0.000),
('3555', 'Big Hill', 'VIC', -36.830, 144.230),
('3555', 'Golden Gully', 'VIC', -36.830, 144.230),
('3555', 'Golden Square', 'VIC', -36.830, 144.230),
('3555', 'Kangaroo Flat', 'VIC', -36.830, 144.230),
('3555', 'Lansell Plaza', 'VIC', -36.830, 144.230),
('3556', 'California Gully', 'VIC', -36.730, 144.260),
('3556', 'Campbells Forest', 'VIC', -36.730, 144.260),
('3556', 'Eaglehawk', 'VIC', -36.730, 144.260),
('3556', 'Eaglehawk North', 'VIC', -36.730, 144.260),
('3556', 'Jackass Flat', 'VIC', -36.730, 144.260),
('3556', 'Myers Flat', 'VIC', -36.730, 144.260),
('3556', 'Sailors Gully', 'VIC', -36.730, 144.260),
('3556', 'Sebastian', 'VIC', -36.730, 144.260),
('3556', 'Whipstick', 'VIC', -36.730, 144.260),
('3556', 'Woodvale', 'VIC', -36.730, 144.260),
('3557', 'Barnadown', 'VIC', -36.650, 144.510),
('3557', 'Fosterville', 'VIC', -36.650, 144.510),
('3557', 'Goornong', 'VIC', -36.650, 144.510),
('3557', 'Muskerry', 'VIC', -36.650, 144.510),
('3558', 'Burnewang', 'VIC', -36.490, 144.710),
('3558', 'Corop West', 'VIC', -36.490, 144.710),
('3558', 'Elmore', 'VIC', -36.490, 144.710),
('3558', 'Hunter', 'VIC', -36.490, 144.710),
('3558', 'Runnymede', 'VIC', -36.490, 144.710),
('3559', 'Avonmore', 'VIC', -36.530, 144.600),
('3559', 'Burramboot', 'VIC', -36.530, 144.600),
('3559', 'Colbinabbin', 'VIC', -36.530, 144.600),
('3559', 'Corop', 'VIC', -36.530, 144.600),
('3559', 'Gobarup', 'VIC', -36.530, 144.600),
('3561', 'Ballendella', 'VIC', -36.300, 144.670),
('3561', 'Bamawm', 'VIC', -36.300, 144.670),
('3561', 'Bamawm Extension', 'VIC', -36.300, 144.670),
('3561', 'Bonn', 'VIC', -36.300, 144.670),
('3561', 'Diggora', 'VIC', -36.300, 144.670),
('3561', 'Fairy Dell', 'VIC', -36.300, 144.670),
('3561', 'Nanneella', 'VIC', -36.300, 144.670),
('3561', 'Rochester', 'VIC', -36.300, 144.670),
('3561', 'Timmering', 'VIC', -36.300, 144.670),
('3562', 'Torrumbarry', 'VIC', -35.980, 144.460),
('3563', 'Lockington', 'VIC', -36.270, 144.480),
('3564', 'Echuca', 'VIC', -36.130, 144.730),
('3564', 'Echuca South', 'VIC', -36.130, 144.730),
('3564', 'Echuca Village', 'VIC', -36.130, 144.730),
('3564', 'Echuca West', 'VIC', -36.130, 144.730),
('3564', 'Kanyapella', 'VIC', -36.130, 144.730),
('3564', 'Patho', 'VIC', -36.130, 144.730),
('3564', 'Roslynmead', 'VIC', -36.130, 144.730),
('3564', 'Wharparilla', 'VIC', -36.130, 144.730),
('3565', 'Kotta', 'VIC', -36.190, 144.530),
('3566', 'Gunbower', 'VIC', -35.960, 144.370),
('3567', 'Horfield', 'VIC', -35.890, 144.240),
('3567', 'Leitchville', 'VIC', -35.890, 144.240),
('3568', 'Burkes Bridge', 'VIC', -35.840, 144.340),
('3568', 'Cohuna', 'VIC', -35.840, 144.340),
('3568', 'Cullen', 'VIC', -35.840, 144.340),
('3568', 'Daltons Bridge', 'VIC', -35.840, 144.340),
('3568', 'Gannawarra', 'VIC', -35.840, 144.340),
('3568', 'Keely', 'VIC', -35.840, 144.340),
('3568', 'Macorna North', 'VIC', -35.840, 144.340),
('3568', 'Mcmillans', 'VIC', -35.840, 144.340),
('3568', 'Mead', 'VIC', -35.840, 144.340),
('3568', 'Mincha West', 'VIC', -35.840, 144.340),
('3568', 'Wee Wee Rup', 'VIC', -35.840, 144.340),
('3570', 'Auchmore', 'VIC', -36.470, 144.110),
('3570', 'Drummartin', 'VIC', -36.470, 144.110),
('3570', 'Kamarooka', 'VIC', -36.470, 144.110),
('3570', 'Neilborough', 'VIC', -36.470, 144.110),
('3570', 'Raywood', 'VIC', -36.470, 144.110),
('3571', 'Dingee', 'VIC', -36.370, 144.230),
('3571', 'Kamarooka North', 'VIC', -36.370, 144.230),
('3571', 'Pompapiel', 'VIC', -36.370, 144.230),
('3571', 'Tandarra', 'VIC', -36.370, 144.230),
('3572', 'Milloo', 'VIC', -36.360, 144.380),
('3572', 'Piavella', 'VIC', -36.360, 144.380),
('3572', 'Prairie', 'VIC', -36.360, 144.380),
('3572', 'Tennyson', 'VIC', -36.360, 144.380),
('3573', 'Calivil', 'VIC', -36.300, 144.090),
('3573', 'Mitiamo', 'VIC', -36.300, 144.090),
('3573', 'Pine Grove', 'VIC', -36.300, 144.090),
('3573', 'Terrick Terrick East', 'VIC', -36.300, 144.090),
('3575', 'Gladfield', 'VIC', -36.040, 143.990),
('3575', 'Jungaburra', 'VIC', -36.040, 143.990),
('3575', 'Loddon Vale', 'VIC', -36.040, 143.990),
('3575', 'Mincha', 'VIC', -36.040, 143.990),
('3575', 'Mologa', 'VIC', -36.040, 143.990),
('3575', 'Pyramid Hill', 'VIC', -36.040, 143.990),
('3575', 'Sylvaterre', 'VIC', -36.040, 143.990),
('3575', 'Terrick Terrick', 'VIC', -36.040, 143.990),
('3575', 'Yarrawalla', 'VIC', -36.040, 143.990),
('3576', 'Durham Ox', 'VIC', -36.200, 143.960),
('3579', 'Appin', 'VIC', -35.880, 143.870),
('3579', 'Appin South', 'VIC', -35.880, 143.870),
('3579', 'Bael Bael', 'VIC', -35.880, 143.870),
('3579', 'Beauchamp', 'VIC', -35.880, 143.870),
('3579', 'Benjeroop', 'VIC', -35.880, 143.870),
('3579', 'Budgerum East', 'VIC', -35.880, 143.870),
('3579', 'Capels Crossing', 'VIC', -35.880, 143.870),
('3579', 'Dingwall', 'VIC', -35.880, 143.870),
('3579', 'Fairley', 'VIC', -35.880, 143.870),
('3579', 'Gonn Crossing', 'VIC', -35.880, 143.870),
('3579', 'Kerang', 'VIC', -35.880, 143.870),
('3579', 'Kerang East', 'VIC', -35.880, 143.870),
('3579', 'Koroop', 'VIC', -35.880, 143.870),
('3579', 'Lake Meran', 'VIC', -35.880, 143.870),
('3579', 'Macorna', 'VIC', -35.880, 143.870),
('3579', 'Meering West', 'VIC', -35.880, 143.870),
('3579', 'Milnes Bridge', 'VIC', -35.880, 143.870),
('3579', 'Murrabit', 'VIC', -35.880, 143.870),
('3579', 'Murrabit West', 'VIC', -35.880, 143.870),
('3579', 'Myall', 'VIC', -35.880, 143.870),
('3579', 'Mystic Park', 'VIC', -35.880, 143.870),
('3579', 'Normanville', 'VIC', -35.880, 143.870),
('3579', 'Pine View', 'VIC', -35.880, 143.870),
('3579', 'Reedy Lake', 'VIC', -35.880, 143.870),
('3579', 'Sandhill Lake', 'VIC', -35.880, 143.870),
('3579', 'Teal Point', 'VIC', -35.880, 143.870),
('3579', 'Tragowel', 'VIC', -35.880, 143.870),
('3579', 'Wandella', 'VIC', -35.880, 143.870),
('3579', 'Westby', 'VIC', -35.880, 143.870),
('3580', 'Koondrook', 'VIC', -35.640, 144.110),
('3581', 'Lake Charm', 'VIC', -35.620, 143.800),
('3583', 'Tresco', 'VIC', -35.510, 143.640),
('3584', 'Lake Boga', 'VIC', -35.460, 143.630),
('3584', 'Tresco West', 'VIC', -35.460, 143.630),
('3585', 'Castle Donnington', 'VIC', -35.420, 143.590),
('3585', 'Chillingollah', 'VIC', -35.420, 143.590),
('3585', 'Fish Point', 'VIC', -35.420, 143.590),
('3585', 'Goschen', 'VIC', -35.420, 143.590),
('3585', 'Kunat', 'VIC', -35.420, 143.590),
('3585', 'Meatian', 'VIC', -35.420, 143.590),
('3585', 'Murray Downs', 'NSW', -35.420, 143.590),
('3585', 'Nowie', 'VIC', -35.420, 143.590),
('3585', 'Nyrraby', 'VIC', -35.420, 143.590),
('3585', 'Pira', 'VIC', -35.420, 143.590),
('3585', 'Polisbet', 'VIC', -35.420, 143.590),
('3585', 'Speewa', 'VIC', -35.420, 143.590),
('3585', 'Swan Hill', 'VIC', -35.420, 143.590),
('3585', 'Swan Hill Pioneer', 'VIC', -35.420, 143.590),
('3585', 'Swan Hill West', 'VIC', -35.420, 143.590),
('3585', 'Winlaton', 'VIC', -35.420, 143.590),
('3586', 'Bulga', 'VIC', -35.300, 143.360),
('3586', 'Mallan', 'NSW', -35.300, 143.360),
('3586', 'Murrawee', 'VIC', -35.300, 143.360),
('3586', 'Murraydale', 'VIC', -35.300, 143.360),
('3586', 'Pental Island', 'VIC', -35.300, 143.360),
('3586', 'Tyntynder', 'VIC', -35.300, 143.360),
('3586', 'Tyntynder South', 'VIC', -35.300, 143.360),
('3588', 'Woorinen South', 'VIC', -35.270, 143.450),
('3589', 'Woorinen', 'VIC', -35.260, 143.470),
('3589', 'Woorinen North', 'VIC', -35.260, 143.470),
('3590', 'Beverford', 'VIC', -35.240, 143.480),
('3591', 'Vinifera', 'VIC', -35.220, 143.410),
('3594', 'Nyah', 'VIC', -35.150, 143.360),
('3595', 'Nyah West', 'VIC', -35.190, 143.350),
('3596', 'Miralie', 'VIC', -35.120, 143.330),
('3596', 'Towan', 'VIC', -35.120, 143.330),
('3596', 'Wood Wood', 'VIC', -35.120, 143.330),
('3597', 'Kenley', 'VIC', -34.860, 143.340),
('3597', 'Kooloonong', 'VIC', -34.860, 143.340),
('3597', 'Lake Powell', 'VIC', -34.860, 143.340),
('3597', 'Narrung', 'VIC', -34.860, 143.340),
('3597', 'Natya', 'VIC', -34.860, 143.340),
('3597', 'Piangil', 'VIC', -34.860, 143.340),
('3599', 'Boundary Bend', 'VIC', -34.740, 143.140),
('3607', 'Tabilk', 'VIC', -36.850, 145.200),
('3608', 'Bailieston', 'VIC', -36.700, 145.030),
('3608', 'Goulburn Weir', 'VIC', -36.700, 145.030),
('3608', 'Graytown', 'VIC', -36.700, 145.030),
('3608', 'Kirwans Bridge', 'VIC', -36.700, 145.030),
('3608', 'Mitchellstown', 'VIC', -36.700, 145.030),
('3608', 'Nagambie', 'VIC', -36.700, 145.030),
('3608', 'Wahring', 'VIC', -36.700, 145.030),
('3608', 'Wirrate', 'VIC', -36.700, 145.030),
('3610', 'Dhurringile', 'VIC', -36.540, 145.280),
('3610', 'Moorilim', 'VIC', -36.540, 145.280),
('3610', 'Murchison', 'VIC', -36.540, 145.280),
('3610', 'Murchison East', 'VIC', -36.540, 145.280),
('3610', 'Murchison North', 'VIC', -36.540, 145.280),
('3612', 'Moora', 'VIC', -36.590, 144.950),
('3612', 'Rushworth', 'VIC', -36.590, 144.950),
('3612', 'Wanalta', 'VIC', -36.590, 144.950),
('3612', 'Waranga Shores', 'VIC', -36.590, 144.950),
('3612', 'Whroo', 'VIC', -36.590, 144.950),
('3614', 'Toolamba', 'VIC', -36.480, 145.260),
('3614', 'Toolamba West', 'VIC', -36.480, 145.260),
('3616', 'Cooma', 'VIC', -36.420, 145.070),
('3616', 'Gillieston', 'VIC', -36.420, 145.070),
('3616', 'Girgarre East', 'VIC', -36.420, 145.070),
('3616', 'Harston', 'VIC', -36.420, 145.070),
('3616', 'Mooroopna North West', 'VIC', -36.420, 145.070),
('3616', 'Tatura', 'VIC', -36.420, 145.070),
('3616', 'Tatura East', 'VIC', -36.420, 145.070),
('3616', 'Waranga', 'VIC', -36.420, 145.070),
('3617', 'Byrneside', 'VIC', -36.420, 145.150),
('3618', 'Merrigum', 'VIC', -36.370, 145.170),
('3619', 'Kyabram', 'VIC', -37.660, 144.940),
('3620', 'Kyabram', 'VIC', -36.310, 145.050),
('3620', 'Kyabram South', 'VIC', -36.310, 145.050),
('3620', 'Lancaster', 'VIC', -36.310, 145.050),
('3620', 'St Germains', 'VIC', -36.310, 145.050),
('3620', 'Taripta', 'VIC', -36.310, 145.050),
('3620', 'Wyuna', 'VIC', -36.310, 145.050),
('3620', 'Wyuna East', 'VIC', -36.310, 145.050),
('3621', 'Kyvalley', 'VIC', -36.270, 144.850),
('3621', 'Tongala', 'VIC', -36.270, 144.850),
('3621', 'Yambuna', 'VIC', -36.270, 144.850),
('3622', 'Koyuga', 'VIC', -36.160, 144.770),
('3622', 'Strathallan', 'VIC', -36.160, 144.770),
('3623', 'Carag Carag', 'VIC', -36.460, 144.900),
('3623', 'Stanhope', 'VIC', -36.460, 144.900),
('3623', 'Stanhope South', 'VIC', -36.460, 144.900),
('3624', 'Girgarre', 'VIC', -36.440, 145.070),
('3629', 'Ardmona', 'VIC', -36.380, 145.300),
('3629', 'Coomboona', 'VIC', -36.380, 145.300),
('3629', 'Mooroopna', 'VIC', -36.380, 145.300),
('3629', 'Mooroopna North', 'VIC', -36.380, 145.300),
('3629', 'Undera', 'VIC', -36.380, 145.300),
('3630', 'Branditt', 'VIC', -36.360, 145.400),
('3630', 'Caniambo', 'VIC', -36.360, 145.400),
('3630', 'Colliver', 'VIC', -36.360, 145.400),
('3630', 'Dunkirk', 'VIC', -36.360, 145.400),
('3630', 'Shepparton', 'VIC', -36.360, 145.400),
('3630', 'Shepparton South', 'VIC', -36.360, 145.400),
('3631', 'Arcadia', 'VIC', -36.540, 145.360),
('3631', 'Arcadia South', 'VIC', -36.540, 145.360),
('3631', 'Cosgrove', 'VIC', -36.540, 145.360),
('3631', 'Cosgrove South', 'VIC', -36.540, 145.360),
('3631', 'Grahamvale', 'VIC', -36.540, 145.360),
('3631', 'Karramomus', 'VIC', -36.540, 145.360),
('3631', 'Kialla', 'VIC', -36.540, 145.360),
('3631', 'Kialla East', 'VIC', -36.540, 145.360),
('3631', 'Kialla West', 'VIC', -36.540, 145.360),
('3631', 'Lemnos', 'VIC', -36.540, 145.360),
('3631', 'Orrvale', 'VIC', -36.540, 145.360),
('3631', 'Pine Lodge', 'VIC', -36.540, 145.360),
('3631', 'Shepparton East', 'VIC', -36.540, 145.360),
('3631', 'Shepparton North', 'VIC', -36.540, 145.360),
('3632', 'Shepparton', 'VIC', -36.540, 145.600),
('3633', 'Congupna', 'VIC', -36.300, 145.480),
('3634', 'Bunbartha', 'VIC', -36.220, 145.340),
('3634', 'Katandra', 'VIC', -36.220, 145.340),
('3634', 'Katandra West', 'VIC', -36.220, 145.340),
('3634', 'Marionvale', 'VIC', -36.220, 145.340),
('3634', 'Marungi', 'VIC', -36.220, 145.340),
('3634', 'Tallygaroopna', 'VIC', -36.220, 145.340),
('3634', 'Zeerust', 'VIC', -36.220, 145.340),
('3635', 'Kaarimba', 'VIC', -36.160, 145.300),
('3635', 'Mundoona', 'VIC', -36.160, 145.300),
('3635', 'Wunghnu', 'VIC', -36.160, 145.300),
('3636', 'Drumanure', 'VIC', -36.140, 145.500),
('3636', 'Invergordon', 'VIC', -36.140, 145.500),
('3636', 'Naring', 'VIC', -36.140, 145.500),
('3636', 'Numurkah', 'VIC', -36.140, 145.500),
('3637', 'Waaia', 'VIC', -36.050, 145.330),
('3637', 'Yalca', 'VIC', -36.050, 145.330),
('3638', 'Kotupna', 'VIC', -36.150, 145.170),
('3638', 'Nathalia', 'VIC', -36.150, 145.170),
('3638', 'Yielima', 'VIC', -36.150, 145.170),
('3639', 'Barmah', 'VIC', -36.020, 144.970),
('3639', 'Lower Moira', 'VIC', -36.020, 144.970),
('3639', 'Picola', 'VIC', -36.020, 144.970),
('3639', 'Picola West', 'VIC', -36.020, 144.970),
('3640', 'Katunga', 'VIC', -35.970, 145.460),
('3641', 'Bearii', 'VIC', -35.920, 145.330),
('3641', 'Mywee', 'VIC', -35.920, 145.330),
('3641', 'Strathmerton', 'VIC', -35.920, 145.330),
('3641', 'Ulupna', 'VIC', -35.920, 145.330),
('3643', 'Cobram', 'VIC', -35.960, 145.630),
('3644', 'Barooga', 'NSW', -35.910, 145.690),
('3644', 'Cobram', 'VIC', -35.910, 145.690),
('3644', 'Cobram East', 'VIC', -35.910, 145.690),
('3644', 'Koonoomoo', 'VIC', -35.910, 145.690),
('3644', 'Lalalty', 'NSW', -35.910, 145.690),
('3644', 'Muckatah', 'VIC', -35.910, 145.690),
('3644', 'Yarroweyah', 'VIC', -35.910, 145.690),
('3646', 'Dookie', 'VIC', -36.330, 145.690),
('3646', 'Mount Major', 'VIC', -36.330, 145.690),
('3646', 'Nalinga', 'VIC', -36.330, 145.690),
('3646', 'Waggarandall', 'VIC', -36.330, 145.690),
('3646', 'Yabba North', 'VIC', -36.330, 145.690),
('3646', 'Yabba South', 'VIC', -36.330, 145.690),
('3646', 'Youanmite', 'VIC', -36.330, 145.690),
('3647', 'Dookie College', 'VIC', -36.400, 145.700),
('3649', 'Katamatite', 'VIC', -36.080, 145.690),
('3649', 'Katamatite East', 'VIC', -36.080, 145.690),
('3658', 'Broadford', 'VIC', -37.200, 145.050),
('3658', 'Clonbinane', 'VIC', -37.200, 145.050),
('3658', 'Flowerdale', 'VIC', -37.200, 145.050),
('3658', 'Hazeldene', 'VIC', -37.200, 145.050),
('3658', 'Reedy Creek', 'VIC', -37.200, 145.050),
('3658', 'Strath Creek', 'VIC', -37.200, 145.050),
('3658', 'Sugarloaf Creek', 'VIC', -37.200, 145.050),
('3658', 'Sunday Creek', 'VIC', -37.200, 145.050),
('3658', 'Tyaak', 'VIC', -37.200, 145.050),
('3658', 'Waterford Park', 'VIC', -37.200, 145.050),
('3659', 'Tallarook', 'VIC', -37.130, 145.020),
('3660', 'Caveat', 'VIC', -37.090, 145.500),
('3660', 'Dropmore', 'VIC', -37.090, 145.500),
('3660', 'Highlands', 'VIC', -37.090, 145.500),
('3660', 'Hilldene', 'VIC', -37.090, 145.500),
('3660', 'Kerrisdale', 'VIC', -37.090, 145.500),
('3660', 'Northwood', 'VIC', -37.090, 145.500),
('3660', 'Seymour', 'VIC', -37.090, 145.500),
('3660', 'Seymour South', 'VIC', -37.090, 145.500),
('3660', 'Trawool', 'VIC', -37.090, 145.500),
('3660', 'Whiteheads Creek', 'VIC', -37.090, 145.500),
('3661', 'Seymour', 'VIC', -38.200, 146.540),
('3662', 'Puckapunyal', 'VIC', 0.000, 0.000),
('3662', 'Puckapunyal Milpo', 'VIC', 0.000, 0.000),
('3663', 'Mangalore', 'VIC', -36.940, 145.160),
('3664', 'Avenel', 'VIC', -36.890, 145.230),
('3664', 'Upton Hill', 'VIC', -36.890, 145.230),
('3665', 'Locksley', 'VIC', -36.820, 145.360),
('3665', 'Longwood', 'VIC', -36.820, 145.360),
('3666', 'Balmattum', 'VIC', -36.710, 145.640),
('3666', 'Creightons Creek', 'VIC', -36.710, 145.640),
('3666', 'Euroa', 'VIC', -36.710, 145.640),
('3666', 'Gooram', 'VIC', -36.710, 145.640),
('3666', 'Kelvin View', 'VIC', -36.710, 145.640),
('3666', 'Kithbrook', 'VIC', -36.710, 145.640),
('3666', 'Longwood East', 'VIC', -36.710, 145.640),
('3666', 'Miepoll', 'VIC', -36.710, 145.640),
('3666', 'Moglonemby', 'VIC', -36.710, 145.640),
('3666', 'Molka', 'VIC', -36.710, 145.640),
('3666', 'Pranjip', 'VIC', -36.710, 145.640),
('3666', 'Riggs Creek', 'VIC', -36.710, 145.640),
('3666', 'Ruffy', 'VIC', -36.710, 145.640),
('3666', 'Sheans Creek', 'VIC', -36.710, 145.640),
('3666', 'Strathbogie', 'VIC', -36.710, 145.640),
('3666', 'Tarcombe', 'VIC', -36.710, 145.640),
('3669', 'Boho', 'VIC', -36.700, 145.770),
('3669', 'Boho South', 'VIC', -36.700, 145.770),
('3669', 'Creek Junction', 'VIC', -36.700, 145.770),
('3669', 'Earlston', 'VIC', -36.700, 145.770),
('3669', 'Gowangardie', 'VIC', -36.700, 145.770),
('3669', 'Koonda', 'VIC', -36.700, 145.770),
('3669', 'Marraweeney', 'VIC', -36.700, 145.770),
('3669', 'Tamleugh', 'VIC', -36.700, 145.770),
('3669', 'Tamleugh North', 'VIC', -36.700, 145.770),
('3669', 'Upotipotpon', 'VIC', -36.700, 145.770),
('3669', 'Violet Town', 'VIC', -36.700, 145.770),
('3670', 'Baddaginnie', 'VIC', -36.590, 145.860),
('3670', 'Tarnook', 'VIC', -36.590, 145.860),
('3670', 'Warrenbayne', 'VIC', -36.590, 145.860),
('3671', 'Benalla', 'VIC', -36.390, 145.420),
('3672', 'Benalla', 'VIC', -36.550, 145.990),
('3672', 'Benalla West', 'VIC', -36.550, 145.990),
('3673', 'Broken Creek', 'VIC', -36.430, 145.890),
('3673', 'Goomalibee', 'VIC', -36.430, 145.890),
('3673', 'Lima', 'VIC', -36.430, 145.890),
('3673', 'Lima East', 'VIC', -36.430, 145.890),
('3673', 'Lima South', 'VIC', -36.430, 145.890),
('3673', 'Lurg', 'VIC', -36.430, 145.890),
('3673', 'Molyullah', 'VIC', -36.430, 145.890),
('3673', 'Moorngag', 'VIC', -36.430, 145.890),
('3673', 'Samaria', 'VIC', -36.430, 145.890),
('3673', 'Swanpool', 'VIC', -36.430, 145.890),
('3673', 'Tatong', 'VIC', -36.430, 145.890),
('3673', 'Upper Lurg', 'VIC', -36.430, 145.890),
('3673', 'Upper Ryans Creek', 'VIC', -36.430, 145.890),
('3673', 'Winton', 'VIC', -36.430, 145.890),
('3673', 'Winton North', 'VIC', -36.430, 145.890),
('3675', 'Boweya', 'VIC', -36.270, 146.130),
('3675', 'Boweya North', 'VIC', -36.270, 146.130),
('3675', 'Glenrowan', 'VIC', -36.270, 146.130),
('3675', 'Glenrowan West', 'VIC', -36.270, 146.130),
('3675', 'Greta', 'VIC', -36.270, 146.130),
('3675', 'Greta South', 'VIC', -36.270, 146.130),
('3675', 'Greta West', 'VIC', -36.270, 146.130),
('3675', 'Hansonville', 'VIC', -36.270, 146.130),
('3675', 'Mount Bruno', 'VIC', -36.270, 146.130),
('3675', 'Taminick', 'VIC', -36.270, 146.130),
('3676', 'Wangaratta', 'VIC', -36.340, 146.340),
('3677', 'Wangaratta', 'VIC', -36.350, 146.300),
('3677', 'Wangaratta West', 'VIC', -36.350, 146.300),
('3677', 'Yarrunga', 'VIC', -36.350, 146.300),
('3678', 'Bobinawarrah', 'VIC', -36.520, 146.500),
('3678', 'Boorhaman', 'VIC', -36.520, 146.500),
('3678', 'Boorhaman East', 'VIC', -36.520, 146.500),
('3678', 'Bowser', 'VIC', -36.520, 146.500),
('3678', 'Byawatha', 'VIC', -36.520, 146.500),
('3678', 'Carboor', 'VIC', -36.520, 146.500),
('3678', 'Cheshunt', 'VIC', -36.520, 146.500),
('3678', 'Cheshunt South', 'VIC', -36.520, 146.500),
('3678', 'Docker', 'VIC', -36.520, 146.500),
('3678', 'Dockers Plains', 'VIC', -36.520, 146.500),
('3678', 'East Wangaratta', 'VIC', -36.520, 146.500),
('3678', 'Edi', 'VIC', -36.520, 146.500),
('3678', 'Edi Upper', 'VIC', -36.520, 146.500),
('3678', 'Everton', 'VIC', -36.520, 146.500),
('3678', 'Everton Upper', 'VIC', -36.520, 146.500),
('3678', 'Killawarra', 'VIC', -36.520, 146.500),
('3678', 'King Valley', 'VIC', -36.520, 146.500),
('3678', 'Laceby', 'VIC', -36.520, 146.500),
('3678', 'Londrigan', 'VIC', -36.520, 146.500),
('3678', 'Markwood', 'VIC', -36.520, 146.500),
('3678', 'Meadow Creek', 'VIC', -36.520, 146.500),
('3678', 'Milawa', 'VIC', -36.520, 146.500),
('3678', 'North Wangaratta', 'VIC', -36.520, 146.500),
('3678', 'Oxley', 'VIC', -36.520, 146.500),
('3678', 'Oxley Flats', 'VIC', -36.520, 146.500),
('3678', 'Peechelba', 'VIC', -36.520, 146.500),
('3678', 'Peechelba East', 'VIC', -36.520, 146.500),
('3678', 'Rose River', 'VIC', -36.520, 146.500),
('3678', 'Tarrawingee', 'VIC', -36.520, 146.500),
('3678', 'Wabonga', 'VIC', -36.520, 146.500),
('3678', 'Waldara', 'VIC', -36.520, 146.500),
('3678', 'Wangandary', 'VIC', -36.520, 146.500),
('3678', 'Wangaratta Forward', 'VIC', -36.520, 146.500),
('3678', 'Wangaratta South', 'VIC', -36.520, 146.500),
('3678', 'Whitlands', 'VIC', -36.520, 146.500),
('3682', 'Boralma', 'VIC', -36.240, 146.410),
('3682', 'Lilliput', 'VIC', -36.240, 146.410),
('3682', 'Norong', 'VIC', -36.240, 146.410),
('3682', 'Springhurst', 'VIC', -36.240, 146.410),
('3683', 'Chiltern', 'VIC', -36.150, 146.610),
('3683', 'Chiltern Valley', 'VIC', -36.150, 146.610),
('3683', 'Cornishtown', 'VIC', -36.150, 146.610),
('3685', 'Boorhaman North', 'VIC', -36.100, 146.220),
('3685', 'Brimin', 'VIC', -36.100, 146.220),
('3685', 'Browns Plains', 'VIC', -36.100, 146.220),
('3685', 'Carlyle', 'VIC', -36.100, 146.220),
('3685', 'Gooramadda', 'VIC', -36.100, 146.220),
('3685', 'Great Southern', 'VIC', -36.100, 146.220),
('3685', 'Lake Moodemere', 'VIC', -36.100, 146.220),
('3685', 'Prentice North', 'VIC', -36.100, 146.220),
('3685', 'Rutherglen', 'VIC', -36.100, 146.220),
('3687', 'Wahgunyah', 'VIC', -36.010, 146.400),
('3688', 'Barnawartha', 'VIC', -36.110, 146.670),
('3688', 'Indigo Valley', 'VIC', -36.110, 146.670),
('3689', 'Wodonga', 'VIC', -37.680, 144.990),
('3690', 'West Wodonga', 'VIC', -36.100, 146.820),
('3690', 'Wodonga', 'VIC', -36.100, 146.820),
('3690', 'Wodonga Plaza', 'VIC', -36.100, 146.820),
('3691', 'Allans Flat', 'VIC', -36.280, 146.910),
('3691', 'Bandiana', 'VIC', -36.280, 146.910),
('3691', 'Baranduda', 'VIC', -36.280, 146.910),
('3691', 'Barnawartha North', 'VIC', -36.280, 146.910),
('3691', 'Bellbridge', 'VIC', -36.280, 146.910),
('3691', 'Berringama', 'VIC', -36.280, 146.910),
('3691', 'Bethanga', 'VIC', -36.280, 146.910),
('3691', 'Bonegilla', 'VIC', -36.280, 146.910),
('3691', 'Bungil', 'VIC', -36.280, 146.910),
('3691', 'Castle Creek', 'VIC', -36.280, 146.910),
('3691', 'Coral Bank', 'VIC', -36.280, 146.910),
('3691', 'Dederang', 'VIC', -36.280, 146.910),
('3691', 'Ebden', 'VIC', -36.280, 146.910),
('3691', 'Gateway Island', 'VIC', -36.280, 146.910),
('3691', 'Glen Creek', 'VIC', -36.280, 146.910),
('3691', 'Gundowring', 'VIC', -36.280, 146.910),
('3691', 'Huon Creek', 'VIC', -36.280, 146.910),
('3691', 'Kancoona', 'VIC', -36.280, 146.910),
('3691', 'Kergunyah', 'VIC', -36.280, 146.910),
('3691', 'Kergunyah South', 'VIC', -36.280, 146.910),
('3691', 'Kiewa', 'VIC', -36.280, 146.910),
('3691', 'Killara', 'VIC', -36.280, 146.910),
('3691', 'Lake Hume Village', 'NSW', -36.280, 146.910),
('3691', 'Leneva', 'VIC', -36.280, 146.910),
('3691', 'Lucyvale', 'VIC', -36.280, 146.910),
('3691', 'Mongans Bridge', 'VIC', -36.280, 146.910),
('3691', 'Osbornes Flat', 'VIC', -36.280, 146.910),
('3691', 'Running Creek', 'VIC', -36.280, 146.910),
('3691', 'Staghorn Flat', 'VIC', -36.280, 146.910),
('3691', 'Talgarno', 'VIC', -36.280, 146.910),
('3691', 'Tangambalanga', 'VIC', -36.280, 146.910),
('3691', 'Thologolong', 'VIC', -36.280, 146.910),
('3691', 'Upper Gundowring', 'VIC', -36.280, 146.910),
('3691', 'Wodonga Forward', 'VIC', -36.280, 146.910),
('3694', 'Bandiana Milpo', 'VIC', -36.140, 146.920),
('3695', 'Charleroi', 'VIC', -36.300, 147.140),
('3695', 'Huon', 'VIC', -36.300, 147.140),
('3695', 'Sandy Creek', 'VIC', -36.300, 147.140),
('3697', 'Tawonga', 'VIC', -36.670, 147.210),
('3698', 'Tawonga South', 'VIC', 0.000, 0.000),
('3699', 'Bogong', 'VIC', -36.810, 147.220),
('3699', 'Falls Creek', 'VIC', -36.810, 147.220),
('3699', 'Mount Beauty', 'VIC', -36.810, 147.220),
('3699', 'Nelse', 'VIC', -36.810, 147.220),
('3700', 'Bullioh', 'VIC', -36.180, 147.340),
('3700', 'Georges Creek', 'VIC', -36.180, 147.340),
('3700', 'Jarvis Creek', 'VIC', -36.180, 147.340),
('3700', 'Tallangatta', 'VIC', -36.180, 147.340),
('3700', 'Tallangatta East', 'VIC', -36.180, 147.340),
('3701', 'Dartmouth', 'VIC', -36.530, 147.500),
('3701', 'Eskdale', 'VIC', -36.530, 147.500),
('3701', 'Granya', 'VIC', -36.530, 147.500),
('3701', 'Mitta Mitta', 'VIC', -36.530, 147.500),
('3701', 'Old Tallangatta', 'VIC', -36.530, 147.500),
('3701', 'Shelley', 'VIC', -36.530, 147.500),
('3701', 'Tallandoon', 'VIC', -36.530, 147.500),
('3701', 'Tallangatta South', 'VIC', -36.530, 147.500),
('3701', 'Tallangatta Valley', 'VIC', -36.530, 147.500),
('3704', 'Koetong', 'VIC', -36.100, 147.540),
('3705', 'Cudgewa', 'VIC', -36.190, 147.770),
('3707', 'Biggara', 'VIC', -36.280, 148.030),
('3707', 'Bringenbrong', 'NSW', -36.280, 148.030),
('3707', 'Colac Colac', 'VIC', -36.280, 148.030),
('3707', 'Corryong', 'VIC', -36.280, 148.030),
('3707', 'Nariel Valley', 'VIC', -36.280, 148.030),
('3707', 'Thowgla Valley', 'VIC', -36.280, 148.030),
('3707', 'Tom Groggin', 'VIC', -36.280, 148.030),
('3707', 'Towong', 'VIC', -36.280, 148.030),
('3707', 'Towong Upper', 'VIC', -36.280, 148.030),
('3708', 'Tintaldra', 'VIC', -36.050, 147.930),
('3709', 'Burrowye', 'VIC', -36.050, 147.560),
('3709', 'Guys Forest', 'VIC', -36.050, 147.560),
('3709', 'Mount Alfred', 'VIC', -36.050, 147.560),
('3709', 'Pine Mountain', 'VIC', -36.050, 147.560),
('3709', 'Walwa', 'VIC', -36.050, 147.560),
('3711', 'Buxton', 'VIC', -37.420, 145.710),
('3712', 'Rubicon', 'VIC', -37.330, 145.860),
('3712', 'Thornton', 'VIC', -37.330, 145.860),
('3713', 'Eildon', 'VIC', -37.230, 145.910),
('3713', 'Lake Eildon', 'VIC', -37.230, 145.910),
('3713', 'Taylor Bay', 'VIC', -37.230, 145.910),
('3714', 'Acheron', 'VIC', -37.250, 145.700),
('3714', 'Alexandra', 'VIC', -37.250, 145.700),
('3714', 'Cathkin', 'VIC', -37.250, 145.700),
('3714', 'Crystal Creek', 'VIC', -37.250, 145.700),
('3714', 'Devils River', 'VIC', -37.250, 145.700),
('3714', 'Fawcett', 'VIC', -37.250, 145.700),
('3714', 'Koriella', 'VIC', -37.250, 145.700),
('3714', 'Maintongoon', 'VIC', -37.250, 145.700),
('3714', 'Taggerty', 'VIC', -37.250, 145.700),
('3714', 'Whanregarwen', 'VIC', -37.250, 145.700),
('3715', 'Ancona', 'VIC', -36.970, 145.790),
('3715', 'Merton', 'VIC', -36.970, 145.790),
('3715', 'Woodfield', 'VIC', -36.970, 145.790),
('3717', 'Flowerdale', 'VIC', -37.340, 145.290),
('3717', 'Ghin Ghin', 'VIC', -37.340, 145.290),
('3717', 'Glenburn', 'VIC', -37.340, 145.290),
('3717', 'Homewood', 'VIC', -37.340, 145.290),
('3717', 'Killingworth', 'VIC', -37.340, 145.290),
('3717', 'Limestone', 'VIC', -37.340, 145.290),
('3717', 'Murrindindi', 'VIC', -37.340, 145.290),
('3717', 'Yea', 'VIC', -37.340, 145.290),
('3718', 'Molesworth', 'VIC', -37.130, 145.530),
('3719', 'Gobur', 'VIC', -37.010, 145.620),
('3719', 'Kanumbra', 'VIC', -37.010, 145.620),
('3719', 'Terip Terip', 'VIC', -37.010, 145.620),
('3719', 'Yarck', 'VIC', -37.010, 145.620),
('3720', 'Bonnie Doon', 'VIC', -37.020, 145.860),
('3722', 'Barwite', 'VIC', -37.010, 146.220),
('3722', 'Mansfield', 'VIC', -37.010, 146.220),
('3722', 'Mirimbah', 'VIC', -37.010, 146.220),
('3723', 'Archerton', 'VIC', -36.890, 146.260),
('3723', 'Barjarg', 'VIC', -36.890, 146.260),
('3723', 'Boorolite', 'VIC', -36.890, 146.260),
('3723', 'Bridge Creek', 'VIC', -36.890, 146.260),
('3723', 'Delatite', 'VIC', -36.890, 146.260),
('3723', 'Enochs Point', 'VIC', -36.890, 146.260),
('3723', 'Gaffneys Creek', 'VIC', -36.890, 146.260),
('3723', 'Goughs Bay', 'VIC', -36.890, 146.260),
('3723', 'Howes Creek', 'VIC', -36.890, 146.260),
('3723', 'Howqua', 'VIC', -36.890, 146.260),
('3723', 'Howqua Hills', 'VIC', -36.890, 146.260),
('3723', 'Howqua Inlet', 'VIC', -36.890, 146.260),
('3723', 'Jamieson', 'VIC', -36.890, 146.260),
('3723', 'Kevington', 'VIC', -36.890, 146.260),
('3723', 'Knockwood', 'VIC', -36.890, 146.260),
('3723', 'Macs Cove', 'VIC', -36.890, 146.260),
('3723', 'Maindample', 'VIC', -36.890, 146.260),
('3723', 'Matlock', 'VIC', -36.890, 146.260),
('3723', 'Merrijig', 'VIC', -36.890, 146.260),
('3723', 'Mount Buller', 'VIC', -36.890, 146.260),
('3723', 'Mountain Bay', 'VIC', -36.890, 146.260),
('3723', 'Piries', 'VIC', -36.890, 146.260),
('3723', 'Sawmill Settlement', 'VIC', -36.890, 146.260),
('3723', 'Tolmie', 'VIC', -36.890, 146.260),
('3723', 'Woods Point', 'VIC', -36.890, 146.260),
('3724', 'Mansfield', 'VIC', -37.050, 146.080),
('3725', 'Boxwood', 'VIC', -36.320, 145.800),
('3725', 'Chesney Vale', 'VIC', -36.320, 145.800),
('3725', 'Goorambat', 'VIC', -36.320, 145.800),
('3725', 'Major Plains', 'VIC', -36.320, 145.800),
('3725', 'Stewarton', 'VIC', -36.320, 145.800),
('3726', 'Bungeet', 'VIC', -36.280, 146.060),
('3726', 'Bungeet West', 'VIC', -36.280, 146.060),
('3726', 'Devenish', 'VIC', -36.280, 146.060),
('3726', 'Thoona', 'VIC', -36.280, 146.060),
('3727', 'Almonds', 'VIC', -36.230, 146.060),
('3727', 'Lake Rowan', 'VIC', -36.230, 146.060),
('3727', 'Pelluebla', 'VIC', -36.230, 146.060),
('3727', 'St James', 'VIC', -36.230, 146.060),
('3727', 'Yundool', 'VIC', -36.230, 146.060),
('3728', 'Boomahnoomoonah', 'VIC', -36.100, 146.080),
('3728', 'Tungamah', 'VIC', -36.100, 146.080),
('3728', 'Wilby', 'VIC', -36.100, 146.080),
('3728', 'Youarang', 'VIC', -36.100, 146.080),
('3730', 'Bathumi', 'VIC', -36.030, 146.080),
('3730', 'Boosey', 'VIC', -36.030, 146.080),
('3730', 'Bundalong', 'VIC', -36.030, 146.080),
('3730', 'Bundalong South', 'VIC', -36.030, 146.080),
('3730', 'Burramine', 'VIC', -36.030, 146.080),
('3730', 'Burramine South', 'VIC', -36.030, 146.080),
('3730', 'Esmond', 'VIC', -36.030, 146.080),
('3730', 'Telford', 'VIC', -36.030, 146.080),
('3730', 'Yarrawonga', 'VIC', -36.030, 146.080),
('3730', 'Yarrawonga South', 'VIC', -36.030, 146.080),
('3732', 'Moyhu', 'VIC', -36.580, 146.380),
('3732', 'Myrrhee', 'VIC', -36.580, 146.380),
('3733', 'Whitfield', 'VIC', -36.750, 146.410),
('3735', 'Bowmans Forest', 'VIC', -36.520, 146.580),
('3735', 'Whorouly', 'VIC', -36.520, 146.580),
('3735', 'Whorouly East', 'VIC', -36.520, 146.580),
('3735', 'Whorouly South', 'VIC', -36.520, 146.580),
('3736', 'Myrtleford', 'VIC', -36.460, 146.480),
('3737', 'Abbeyard', 'VIC', -36.980, 146.780),
('3737', 'Barwidgee', 'VIC', -36.980, 146.780),
('3737', 'Buffalo River', 'VIC', -36.980, 146.780),
('3737', 'Dandongadale', 'VIC', -36.980, 146.780),
('3737', 'Gapsted', 'VIC', -36.980, 146.780),
('3737', 'Havilah', 'VIC', -36.980, 146.780),
('3737', 'Merriang', 'VIC', -36.980, 146.780),
('3737', 'Merriang South', 'VIC', -36.980, 146.780),
('3737', 'Mudgegonga', 'VIC', -36.980, 146.780),
('3737', 'Myrtleford', 'VIC', -36.980, 146.780),
('3737', 'Nug Nug', 'VIC', -36.980, 146.780),
('3737', 'Rosewhite', 'VIC', -36.980, 146.780),
('3737', 'Selwyn', 'VIC', -36.980, 146.780),
('3737', 'Wonnangatta', 'VIC', -36.980, 146.780),
('3738', 'Ovens', 'VIC', -36.610, 146.790),
('3739', 'Eurobin', 'VIC', -36.700, 146.850),
('3740', 'Buckland', 'VIC', -36.820, 146.850),
('3740', 'Mount Buffalo', 'VIC', -36.820, 146.850),
('3740', 'Porepunkah', 'VIC', -36.820, 146.850),
('3741', 'Bright', 'VIC', -36.730, 146.960),
('3741', 'Freeburgh', 'VIC', -36.730, 146.960),
('3741', 'Germantown', 'VIC', -36.730, 146.960),
('3741', 'Harrietville', 'VIC', -36.730, 146.960),
('3741', 'Hotham Heights', 'VIC', -36.730, 146.960),
('3741', 'Mount Hotham', 'VIC', -36.730, 146.960),
('3741', 'Smoko', 'VIC', -36.730, 146.960),
('3744', 'Wandiligong', 'VIC', -36.750, 146.980),
('3746', 'Eldorado', 'VIC', -36.300, 146.580),
('3747', 'Beechworth', 'VIC', -36.360, 146.690),
('3747', 'Murmungee', 'VIC', -36.360, 146.690),
('3747', 'Stanley', 'VIC', -36.360, 146.690),
('3747', 'Woolshed', 'VIC', -36.360, 146.690),
('3747', 'Wooragee', 'VIC', -36.360, 146.690),
('3749', 'Bruarong', 'VIC', -36.410, 146.860),
('3749', 'Yackandandah', 'VIC', -36.410, 146.860),
('3750', 'Wollert', 'VIC', -38.380, 144.810),
('3751', 'Woodstock', 'VIC', -37.690, 144.890),
('3752', 'Morang South', 'VIC', -37.650, 145.100),
('3752', 'South Morang', 'VIC', -37.650, 145.100),
('3753', 'Beveridge', 'VIC', -37.470, 145.000),
('3754', 'Doreen', 'VIC', -37.620, 145.150),
('3754', 'Mernda', 'VIC', -37.620, 145.150),
('3755', 'Yan Yean', 'VIC', -37.570, 145.140),
('3756', 'Chintin', 'VIC', -37.400, 144.800),
('3756', 'Darraweit Guim', 'VIC', -37.400, 144.800),
('3756', 'Upper Plenty', 'VIC', -37.400, 144.800),
('3756', 'Wallan', 'VIC', -37.400, 144.800),
('3757', 'Eden Park', 'VIC', -37.450, 145.110),
('3757', 'Humevale', 'VIC', -37.450, 145.110),
('3757', 'Kinglake Central', 'VIC', -37.450, 145.110),
('3757', 'Kinglake West', 'VIC', -37.450, 145.110),
('3757', 'Pheasant Creek', 'VIC', -37.450, 145.110),
('3757', 'Whittlesea', 'VIC', -37.450, 145.110),
('3758', 'Heathcote Junction', 'VIC', -37.380, 145.030),
('3758', 'Wandong', 'VIC', -37.380, 145.030),
('3759', 'Panton Hill', 'VIC', -37.640, 145.240),
('3760', 'Smiths Gully', 'VIC', -37.620, 145.290),
('3761', 'St Andrews', 'VIC', 0.000, 0.000),
('3762', 'Bylands', 'VIC', 0.000, 0.000),
('3763', 'Kinglake', 'VIC', -37.480, 145.280),
('3764', 'Forbes', 'VIC', -37.310, 144.890),
('3764', 'Glenaroua', 'VIC', -37.310, 144.890),
('3764', 'High Camp', 'VIC', -37.310, 144.890),
('3764', 'Kilmore', 'VIC', -37.310, 144.890),
('3764', 'Kilmore East', 'VIC', -37.310, 144.890),
('3764', 'Moranding', 'VIC', -37.310, 144.890),
('3764', 'Tantaraboo', 'VIC', -37.310, 144.890),
('3764', 'Willowmavin', 'VIC', -37.310, 144.890),
('3765', 'Montrose', 'VIC', -37.810, 145.340),
('3766', 'Kalorama', 'VIC', -37.820, 145.370),
('3767', 'Mount Dandenong', 'VIC', -37.840, 145.360),
('3770', 'Coldstream', 'VIC', -37.720, 145.380),
('3770', 'Gruyere', 'VIC', -37.720, 145.380),
('3770', 'Yering', 'VIC', -37.720, 145.380),
('3775', 'Christmas Hills', 'VIC', -37.650, 145.320),
('3775', 'Dixons Creek', 'VIC', -37.650, 145.320),
('3775', 'Steels Creek', 'VIC', -37.650, 145.320),
('3775', 'Tarrawarra', 'VIC', -37.650, 145.320),
('3775', 'Yarra Glen', 'VIC', -37.650, 145.320),
('3777', 'Badger Creek', 'VIC', -37.690, 145.540),
('3777', 'Castella', 'VIC', -37.690, 145.540),
('3777', 'Chum Creek', 'VIC', -37.690, 145.540),
('3777', 'Healesville', 'VIC', -37.690, 145.540),
('3777', 'Healesville Main Street', 'VIC', -37.690, 145.540),
('3777', 'Healesville Post Shop', 'VIC', -37.690, 145.540),
('3777', 'Mount Toolebewong', 'VIC', -37.690, 145.540),
('3777', 'Toolangi', 'VIC', -37.690, 145.540),
('3778', 'Fernshaw', 'VIC', -37.630, 145.700),
('3778', 'Narbethong', 'VIC', -37.630, 145.700),
('3779', 'Cambarville', 'VIC', -37.560, 145.890),
('3779', 'Marysville', 'VIC', -37.560, 145.890),
('3781', 'Cockatoo', 'VIC', -37.940, 145.490),
('3781', 'Mount Burnett', 'VIC', -37.940, 145.490),
('3781', 'Nangana', 'VIC', -37.940, 145.490),
('3782', 'Avonsleigh', 'VIC', -37.920, 145.470),
('3782', 'Clematis', 'VIC', -37.920, 145.470),
('3782', 'Emerald', 'VIC', -37.920, 145.470),
('3782', 'Macclesfield', 'VIC', -37.920, 145.470),
('3783', 'Gembrook', 'VIC', -37.950, 145.550),
('3785', 'Tremont', 'VIC', -38.270, 144.490),
('3786', 'Ferny Creek', 'VIC', -37.880, 145.340),
('3787', 'Sassafras', 'VIC', -37.880, 145.370),
('3787', 'Sassafras Gully', 'VIC', -37.880, 145.370),
('3788', 'Olinda', 'VIC', -37.860, 145.360),
('3789', 'Sherbrooke', 'VIC', -37.880, 145.360),
('3791', 'Kallista', 'VIC', -37.900, 145.410),
('3792', 'The Patch', 'VIC', -37.890, 145.390),
('3793', 'Monbulk', 'VIC', -37.870, 145.410),
('3795', 'Silvan', 'VIC', -37.840, 145.410),
('3796', 'Mount Evelyn', 'VIC', -37.790, 145.380),
('3797', 'Gilderoy', 'VIC', -37.860, 145.710),
('3797', 'Gladysdale', 'VIC', -37.860, 145.710),
('3797', 'Powelltown', 'VIC', -37.860, 145.710),
('3797', 'Three Bridges', 'VIC', -37.860, 145.710),
('3797', 'Yarra Junction', 'VIC', -37.860, 145.710),
('3799', 'Big Pats Creek', 'VIC', -37.760, 145.750),
('3799', 'East Warburton', 'VIC', -37.760, 145.750),
('3799', 'Mcmahons Creek', 'VIC', -37.760, 145.750),
('3799', 'Millgrove', 'VIC', -37.760, 145.750),
('3799', 'Reefton', 'VIC', -37.760, 145.750),
('3799', 'Warburton', 'VIC', -37.760, 145.750),
('3799', 'Wesburn', 'VIC', -37.760, 145.750),
('3800', 'Monash University', 'VIC', -37.910, 145.130),
('3802', 'Endeavour Hills', 'VIC', -37.980, 145.250),
('3803', 'Hallam', 'VIC', -38.000, 145.270),
('3804', 'Narre Warren East', 'VIC', -37.970, 145.360),
('3804', 'Narre Warren North', 'VIC', -37.970, 145.360),
('3805', 'Fountain Gate', 'VIC', -38.020, 145.300),
('3805', 'Narre Warren', 'VIC', -38.020, 145.300),
('3805', 'Narre Warren South', 'VIC', -38.020, 145.300),
('3806', 'Berwick', 'VIC', -38.030, 145.340),
('3806', 'Harkaway', 'VIC', -38.030, 145.340),
('3807', 'Beaconsfield', 'VIC', -38.050, 145.370),
('3807', 'Guys Hill', 'VIC', -38.050, 145.370),
('3808', 'Beaconsfield Upper', 'VIC', -38.000, 145.410),
('3808', 'Dewhurst', 'VIC', -38.000, 145.410),
('3809', 'Officer', 'VIC', -38.060, 145.410),
('3809', 'Officer South', 'VIC', -38.060, 145.410),
('3810', 'Pakenham', 'VIC', -38.080, 145.490),
('3810', 'Pakenham South', 'VIC', -38.080, 145.490),
('3810', 'Pakenham Upper', 'VIC', -38.080, 145.490),
('3810', 'Rythdale', 'VIC', -38.080, 145.490),
('3812', 'Maryknoll', 'VIC', -38.040, 145.610),
('3812', 'Nar Nar Goon', 'VIC', -38.040, 145.610),
('3812', 'Nar Nar Goon North', 'VIC', -38.040, 145.610),
('3813', 'Tynong', 'VIC', -38.090, 145.630),
('3813', 'Tynong North', 'VIC', -38.090, 145.630),
('3814', 'Cora Lynn', 'VIC', -38.150, 145.610),
('3814', 'Garfield', 'VIC', -38.150, 145.610),
('3814', 'Garfield North', 'VIC', -38.150, 145.610),
('3814', 'Vervale', 'VIC', -38.150, 145.610),
('3815', 'Bunyip', 'VIC', -38.100, 145.720),
('3815', 'Bunyip North', 'VIC', -38.100, 145.720),
('3815', 'Iona', 'VIC', -38.100, 145.720),
('3815', 'Tonimbuk', 'VIC', -38.100, 145.720),
('3816', 'Labertouche', 'VIC', -38.050, 145.830),
('3816', 'Longwarry', 'VIC', -38.050, 145.830),
('3816', 'Longwarry North', 'VIC', -38.050, 145.830),
('3816', 'Modella', 'VIC', -38.050, 145.830),
('3818', 'Athlone', 'VIC', -38.240, 145.780),
('3818', 'Drouin', 'VIC', -38.240, 145.780),
('3818', 'Drouin East', 'VIC', -38.240, 145.780),
('3818', 'Drouin South', 'VIC', -38.240, 145.780),
('3818', 'Drouin West', 'VIC', -38.240, 145.780),
('3818', 'Hallora', 'VIC', -38.240, 145.780),
('3818', 'Jindivick', 'VIC', -38.240, 145.780),
('3818', 'Ripplebrook', 'VIC', -38.240, 145.780),
('3820', 'Bona Vista', 'VIC', -38.210, 145.960),
('3820', 'Lillico', 'VIC', -38.210, 145.960),
('3820', 'Warragul', 'VIC', -38.210, 145.960),
('3821', 'Brandy Creek', 'VIC', -38.100, 145.920),
('3821', 'Bravington', 'VIC', -38.100, 145.920),
('3821', 'Buln Buln', 'VIC', -38.100, 145.920),
('3821', 'Buln Buln East', 'VIC', -38.100, 145.920),
('3821', 'Crossover', 'VIC', -38.100, 145.920),
('3821', 'Ellinbank', 'VIC', -38.100, 145.920),
('3821', 'Ferndale', 'VIC', -38.100, 145.920),
('3821', 'Lardner', 'VIC', -38.100, 145.920),
('3821', 'Nilma', 'VIC', -38.100, 145.920),
('3821', 'Nilma North', 'VIC', -38.100, 145.920),
('3821', 'Rokeby', 'VIC', -38.100, 145.920),
('3821', 'Seaview', 'VIC', -38.100, 145.920),
('3821', 'Shady Creek', 'VIC', -38.100, 145.920),
('3821', 'Tetoora Road', 'VIC', -38.100, 145.920),
('3821', 'Torwood', 'VIC', -38.100, 145.920),
('3821', 'Warragul South', 'VIC', -38.100, 145.920),
('3821', 'Warragul West', 'VIC', -38.100, 145.920),
('3822', 'Cloverlea', 'VIC', -38.240, 146.000),
('3822', 'Darnum', 'VIC', -38.240, 146.000),
('3822', 'Gainsborough', 'VIC', -38.240, 146.000),
('3823', 'Allambee', 'VIC', -38.260, 146.030),
('3823', 'Yarragon', 'VIC', -38.260, 146.030),
('3823', 'Yarragon South', 'VIC', -38.260, 146.030),
('3824', 'Childers', 'VIC', -38.300, 146.110),
('3824', 'Narracan', 'VIC', -38.300, 146.110),
('3824', 'Thorpdale South', 'VIC', -38.300, 146.110),
('3824', 'Trafalgar', 'VIC', -38.300, 146.110),
('3824', 'Trafalgar East', 'VIC', -38.300, 146.110),
('3824', 'Trafalgar South', 'VIC', -38.300, 146.110),
('3825', 'Aberfeldy', 'VIC', -37.700, 146.360),
('3825', 'Amor', 'VIC', -37.700, 146.360),
('3825', 'Boola', 'VIC', -37.700, 146.360),
('3825', 'Caringal', 'VIC', -37.700, 146.360),
('3825', 'Coalville', 'VIC', -37.700, 146.360),
('3825', 'Coopers Creek', 'VIC', -37.700, 146.360),
('3825', 'Erica', 'VIC', -37.700, 146.360),
('3825', 'Fumina', 'VIC', -37.700, 146.360),
('3825', 'Fumina South', 'VIC', -37.700, 146.360),
('3825', 'Hernes Oak', 'VIC', -37.700, 146.360),
('3825', 'Hill End', 'VIC', -37.700, 146.360),
('3825', 'Jacob Creek', 'VIC', -37.700, 146.360),
('3825', 'Jericho', 'VIC', -37.700, 146.360),
('3825', 'Moe', 'VIC', -37.700, 146.360),
('3825', 'Moe South', 'VIC', -37.700, 146.360),
('3825', 'Moondarra', 'VIC', -37.700, 146.360),
('3825', 'Newborough', 'VIC', -37.700, 146.360),
('3825', 'Newborough East', 'VIC', -37.700, 146.360),
('3825', 'Rawson', 'VIC', -37.700, 146.360),
('3825', 'Tanjil', 'VIC', -37.700, 146.360),
('3825', 'Tanjil South', 'VIC', -37.700, 146.360),
('3825', 'Thalloo', 'VIC', -37.700, 146.360),
('3825', 'Thomson', 'VIC', -37.700, 146.360),
('3825', 'Toombon', 'VIC', -37.700, 146.360),
('3825', 'Walhalla', 'VIC', -37.700, 146.360),
('3825', 'Walhalla East', 'VIC', -37.700, 146.360),
('3825', 'Westbury', 'VIC', -37.700, 146.360),
('3825', 'Willow Grove', 'VIC', -37.700, 146.360),
('3825', 'Yallourn', 'VIC', -37.700, 146.360),
('3825', 'Yallourn North', 'VIC', -37.700, 146.360),
('3831', 'Neerim', 'VIC', -37.960, 145.990),
('3831', 'Neerim East', 'VIC', -37.960, 145.990),
('3831', 'Neerim South', 'VIC', -37.960, 145.990),
('3832', 'Nayook', 'VIC', -37.910, 145.930),
('3832', 'Neerim Junction', 'VIC', -37.910, 145.930),
('3832', 'Neerim North', 'VIC', -37.910, 145.930),
('3833', 'Ada', 'VIC', -37.840, 145.850),
('3833', 'Baw Baw', 'VIC', -37.840, 145.850),
('3833', 'Baw Baw Village', 'VIC', -37.840, 145.850),
('3833', 'Gentle Annie', 'VIC', -37.840, 145.850),
('3833', 'Icy Creek', 'VIC', -37.840, 145.850),
('3833', 'Loch Valley', 'VIC', -37.840, 145.850),
('3833', 'Noojee', 'VIC', -37.840, 145.850),
('3833', 'Piedmont', 'VIC', -37.840, 145.850),
('3833', 'Tanjil Bren', 'VIC', -37.840, 145.850),
('3833', 'Toorongo', 'VIC', -37.840, 145.850),
('3833', 'Vesper', 'VIC', -37.840, 145.850),
('3835', 'Thorpdale', 'VIC', -38.330, 146.120),
('3840', 'Driffield', 'VIC', -38.280, 146.320),
('3840', 'Hazelwood', 'VIC', -38.280, 146.320),
('3840', 'Hazelwood North', 'VIC', -38.280, 146.320),
('3840', 'Hazelwood South', 'VIC', -38.280, 146.320),
('3840', 'Jeeralang', 'VIC', -38.280, 146.320),
('3840', 'Jeeralang Junction', 'VIC', -38.280, 146.320),
('3840', 'Maryvale', 'VIC', -38.280, 146.320),
('3840', 'Mid Valley', 'VIC', -38.280, 146.320),
('3840', 'Morwell', 'VIC', -38.280, 146.320),
('3840', 'Morwell East', 'VIC', -38.280, 146.320),
('3840', 'Morwell Upper', 'VIC', -38.280, 146.320),
('3841', 'Gippsland MC', 'VIC', 0.000, 0.000),
('3842', 'Churchill', 'VIC', -38.320, 146.420),
('3844', 'Blackwarry', 'VIC', -38.410, 146.640),
('3844', 'Callignee', 'VIC', -38.410, 146.640),
('3844', 'Callignee North', 'VIC', -38.410, 146.640),
('3844', 'Callignee South', 'VIC', -38.410, 146.640),
('3844', 'Carrajung', 'VIC', -38.410, 146.640),
('3844', 'Carrajung Lower', 'VIC', -38.410, 146.640),
('3844', 'Carrajung South', 'VIC', -38.410, 146.640),
('3844', 'Flynn', 'VIC', -38.410, 146.640),
('3844', 'Flynns Creek', 'VIC', -38.410, 146.640),
('3844', 'Koornalla', 'VIC', -38.410, 146.640),
('3844', 'Loy Yang', 'VIC', -38.410, 146.640),
('3844', 'Mount Tassie', 'VIC', -38.410, 146.640),
('3844', 'Traralgon', 'VIC', -38.410, 146.640),
('3844', 'Traralgon East', 'VIC', -38.410, 146.640),
('3844', 'Traralgon South', 'VIC', -38.410, 146.640),
('3844', 'Tyers', 'VIC', -38.410, 146.640),
('3847', 'Hiamdale', 'VIC', -38.260, 146.750),
('3847', 'Nambrok', 'VIC', -38.260, 146.750),
('3847', 'Rosedale', 'VIC', -38.260, 146.750),
('3847', 'Willung', 'VIC', -38.260, 146.750),
('3847', 'Willung South', 'VIC', -38.260, 146.750),
('3850', 'Sale', 'VIC', -38.420, 147.080),
('3850', 'Sale North', 'VIC', -38.420, 147.080),
('3850', 'Wurruk', 'VIC', -38.420, 147.080),
('3851', 'Airly', 'VIC', -38.040, 147.080),
('3851', 'Bundalaguah', 'VIC', -38.040, 147.080),
('3851', 'Clydebank', 'VIC', -38.040, 147.080),
('3851', 'Cobains', 'VIC', -38.040, 147.080),
('3851', 'Darriman', 'VIC', -38.040, 147.080),
('3851', 'Dutson', 'VIC', -38.040, 147.080),
('3851', 'Dutson Downs', 'VIC', -38.040, 147.080),
('3851', 'Flamingo Beach', 'VIC', -38.040, 147.080),
('3851', 'Fulham', 'VIC', -38.040, 147.080),
('3851', 'Giffard', 'VIC', -38.040, 147.080),
('3851', 'Giffard West', 'VIC', -38.040, 147.080),
('3851', 'Glomar Beach', 'VIC', -38.040, 147.080),
('3851', 'Golden Beach', 'VIC', -38.040, 147.080),
('3851', 'Kilmany', 'VIC', -38.040, 147.080),
('3851', 'Lake Wellington', 'VIC', -38.040, 147.080),
('3851', 'Loch Sport', 'VIC', -38.040, 147.080),
('3851', 'Longford', 'VIC', -38.040, 147.080),
('3851', 'Montgomery', 'VIC', -38.040, 147.080),
('3851', 'Myrtlebank', 'VIC', -38.040, 147.080),
('3851', 'Paradise Beach', 'VIC', -38.040, 147.080),
('3851', 'Pearsondale', 'VIC', -38.040, 147.080),
('3851', 'Seacombe', 'VIC', -38.040, 147.080),
('3851', 'Seaspray', 'VIC', -38.040, 147.080),
('3851', 'Somerton Park', 'VIC', -38.040, 147.080),
('3851', 'Stradbroke', 'VIC', -38.040, 147.080),
('3851', 'The Heart', 'VIC', -38.040, 147.080),
('3851', 'The Honeysuckles', 'VIC', -38.040, 147.080),
('3852', 'East Sale', 'VIC', -38.110, 147.130),
('3852', 'Sale East Raaf', 'VIC', -38.110, 147.130),
('3853', 'Sale', 'VIC', -38.100, 146.060),
('3854', 'Glengarry', 'VIC', -38.120, 146.570),
('3854', 'Glengarry North', 'VIC', -38.120, 146.570),
('3854', 'Glengarry West', 'VIC', -38.120, 146.570),
('3856', 'Toongabbie', 'VIC', -38.070, 146.670),
('3857', 'Cowwarr', 'VIC', -38.000, 146.690),
('3858', 'Arbuckle', 'VIC', -37.970, 146.690),
('3858', 'Billabong', 'VIC', -37.970, 146.690),
('3858', 'Buragwonduc', 'VIC', -37.970, 146.690),
('3858', 'Crookayan', 'VIC', -37.970, 146.690),
('3858', 'Dawson', 'VIC', -37.970, 146.690),
('3858', 'Denison', 'VIC', -37.970, 146.690),
('3858', 'Gillum', 'VIC', -37.970, 146.690),
('3858', 'Glenfalloch', 'VIC', -37.970, 146.690),
('3858', 'Glenmaggie', 'VIC', -37.970, 146.690),
('3858', 'Heyfield', 'VIC', -37.970, 146.690),
('3858', 'Howitt Plains', 'VIC', -37.970, 146.690),
('3858', 'Licola', 'VIC', -37.970, 146.690),
('3858', 'Licola North', 'VIC', -37.970, 146.690),
('3858', 'Reynard', 'VIC', -37.970, 146.690),
('3858', 'Sargood', 'VIC', -37.970, 146.690),
('3858', 'Seaton', 'VIC', -37.970, 146.690),
('3858', 'Tamboritha', 'VIC', -37.970, 146.690),
('3858', 'Winnindoo', 'VIC', -37.970, 146.690),
('3858', 'Worrowing', 'VIC', -37.970, 146.690),
('3858', 'Yangoura', 'VIC', -37.970, 146.690),
('3859', 'Maffra West Upper', 'VIC', -37.890, 146.850),
('3859', 'Newry', 'VIC', -37.890, 146.850),
('3859', 'Tinamba', 'VIC', -37.890, 146.850),
('3859', 'Tinamba West', 'VIC', -37.890, 146.850),
('3860', 'Boisdale', 'VIC', -37.880, 146.990),
('3860', 'Briagolong', 'VIC', -37.880, 146.990),
('3860', 'Bushy Park', 'VIC', -37.880, 146.990),
('3860', 'Coongulla', 'VIC', -37.880, 146.990),
('3860', 'Koorool', 'VIC', -37.880, 146.990),
('3860', 'Maffra', 'VIC', -37.880, 146.990),
('3860', 'Monomak', 'VIC', -37.880, 146.990),
('3860', 'Moroka', 'VIC', -37.880, 146.990),
('3860', 'Nap Nap Marra', 'VIC', -37.880, 146.990),
('3860', 'Riverslea', 'VIC', -37.880, 146.990),
('3860', 'Toolome', 'VIC', -37.880, 146.990),
('3860', 'Valencia Creek', 'VIC', -37.880, 146.990),
('3860', 'Woolenook', 'VIC', -37.880, 146.990),
('3860', 'Wrathung', 'VIC', -37.880, 146.990),
('3860', 'Wrixon', 'VIC', -37.880, 146.990),
('3862', 'Budgee Budgee', 'VIC', -37.570, 147.190),
('3862', 'Cobbannah', 'VIC', -37.570, 147.190),
('3862', 'Cowa', 'VIC', -37.570, 147.190),
('3862', 'Crooked River', 'VIC', -37.570, 147.190),
('3862', 'Dargo', 'VIC', -37.570, 147.190),
('3862', 'Hawkhurst', 'VIC', -37.570, 147.190),
('3862', 'Hollands Landing', 'VIC', -37.570, 147.190),
('3862', 'Llowalong', 'VIC', -37.570, 147.190),
('3862', 'Meerlieu', 'VIC', -37.570, 147.190),
('3862', 'Miowera', 'VIC', -37.570, 147.190),
('3862', 'Moornapa', 'VIC', -37.570, 147.190),
('3862', 'Munro', 'VIC', -37.570, 147.190),
('3862', 'Perry Bridge', 'VIC', -37.570, 147.190),
('3862', 'Stockdale', 'VIC', -37.570, 147.190),
('3862', 'Stratford', 'VIC', -37.570, 147.190),
('3862', 'Waterford', 'VIC', -37.570, 147.190),
('3862', 'Wongungarra', 'VIC', -37.570, 147.190),
('3864', 'Fernbank', 'VIC', -37.860, 147.340),
('3864', 'Glenaladale', 'VIC', -37.860, 147.340),
('3864', 'The Fingerboard', 'VIC', -37.860, 147.340),
('3865', 'Lindenow', 'VIC', -37.800, 147.460),
('3869', 'Jumbuk', 'VIC', -38.400, 146.420),
('3869', 'Yinnar', 'VIC', -38.400, 146.420),
('3869', 'Yinnar South', 'VIC', -38.400, 146.420),
('3870', 'Boolarra', 'VIC', -38.380, 146.280),
('3870', 'Boolarra South', 'VIC', -38.380, 146.280),
('3870', 'Budgeree', 'VIC', -38.380, 146.280),
('3870', 'Grand Ridge', 'VIC', -38.380, 146.280),
('3870', 'Johnstones Hill', 'VIC', -38.380, 146.280),
('3871', 'Allambee Reserve', 'VIC', -38.290, 146.050),
('3871', 'Allambee South', 'VIC', -38.290, 146.050),
('3871', 'Baromi', 'VIC', -38.290, 146.050),
('3871', 'Darlimurla', 'VIC', -38.290, 146.050),
('3871', 'Delburn', 'VIC', -38.290, 146.050),
('3871', 'Dollar', 'VIC', -38.290, 146.050),
('3871', 'Mirboo', 'VIC', -38.290, 146.050),
('3871', 'Mirboo North', 'VIC', -38.290, 146.050),
('3873', 'Gormandale', 'VIC', -38.290, 146.720),
('3874', 'Cherrilong', 'VIC', -38.380, 146.790),
('3874', 'Mcloughlins Beach', 'VIC', -38.380, 146.790),
('3874', 'Woodside', 'VIC', -38.380, 146.790),
('3874', 'Woodside Beach', 'VIC', -38.380, 146.790),
('3874', 'Woodside North', 'VIC', -38.380, 146.790),
('3875', 'Bairnsdale', 'VIC', -37.830, 147.630),
('3875', 'Banksia Peninsula', 'VIC', -37.830, 147.630),
('3875', 'Bengworden', 'VIC', -37.830, 147.630),
('3875', 'Broadlands', 'VIC', -37.830, 147.630),
('3875', 'Bullumwaal', 'VIC', -37.830, 147.630),
('3875', 'Calulu', 'VIC', -37.830, 147.630),
('3875', 'Clifton Creek', 'VIC', -37.830, 147.630),
('3875', 'Deptford', 'VIC', -37.830, 147.630),
('3875', 'East Bairnsdale', 'VIC', -37.830, 147.630),
('3875', 'Ellaswood', 'VIC', -37.830, 147.630),
('3875', 'Fairy Dell', 'VIC', -37.830, 147.630),
('3875', 'Flaggy Creek', 'VIC', -37.830, 147.630),
('3875', 'Forge Creek', 'VIC', -37.830, 147.630),
('3875', 'Goon Nure', 'VIC', -37.830, 147.630),
('3875', 'Granite Rock', 'VIC', -37.830, 147.630),
('3875', 'Hillside', 'VIC', -37.830, 147.630),
('3875', 'Iguana Creek', 'VIC', -37.830, 147.630),
('3875', 'Lindenow South', 'VIC', -37.830, 147.630),
('3875', 'Lucknow', 'VIC', -37.830, 147.630),
('3875', 'Marthavale', 'VIC', -37.830, 147.630),
('3875', 'Melwood', 'VIC', -37.830, 147.630),
('3875', 'Merrijig', 'VIC', -37.830, 147.630),
('3875', 'Mount Taylor', 'VIC', -37.830, 147.630),
('3875', 'Newlands Arm', 'VIC', -37.830, 147.630),
('3875', 'Ryans', 'VIC', -37.830, 147.630),
('3875', 'Sarsfield', 'VIC', -37.830, 147.630),
('3875', 'Tabberabbera', 'VIC', -37.830, 147.630),
('3875', 'Walpa', 'VIC', -37.830, 147.630),
('3875', 'Waterholes', 'VIC', -37.830, 147.630),
('3875', 'Wentworth', 'VIC', -37.830, 147.630),
('3875', 'Woodglen', 'VIC', -37.830, 147.630),
('3875', 'Wuk Wuk', 'VIC', -37.830, 147.630),
('3875', 'Wy Yung', 'VIC', -37.830, 147.630),
('3878', 'Eagle Point', 'VIC', -37.890, 147.670),
('3880', 'Boole Poole', 'VIC', -37.920, 147.690),
('3880', 'Ocean Grange', 'VIC', -37.920, 147.690),
('3880', 'Paynesville', 'VIC', -37.920, 147.690),
('3880', 'Raymond Island', 'VIC', -37.920, 147.690),
('3882', 'Nicholson', 'VIC', -37.820, 147.730),
('3885', 'Brumby', 'VIC', -37.710, 147.830),
('3885', 'Bruthen', 'VIC', -37.710, 147.830),
('3885', 'Buchan', 'VIC', -37.710, 147.830),
('3885', 'Buchan South', 'VIC', -37.710, 147.830),
('3885', 'Butchers Ridge', 'VIC', -37.710, 147.830),
('3885', 'Gelantipy', 'VIC', -37.710, 147.830),
('3885', 'Mossiface', 'VIC', -37.710, 147.830),
('3885', 'Murrindal', 'VIC', -37.710, 147.830),
('3885', 'Suggan Buggan', 'VIC', -37.710, 147.830),
('3885', 'Tambo Upper', 'VIC', -37.710, 147.830),
('3885', 'Timbarra', 'VIC', -37.710, 147.830),
('3885', 'W Tree', 'VIC', -37.710, 147.830),
('3885', 'Wiseleigh', 'VIC', -37.710, 147.830),
('3885', 'Wulgulmerang', 'VIC', -37.710, 147.830),
('3885', 'Wulgulmerang East', 'VIC', -37.710, 147.830),
('3885', 'Wulgulmerang West', 'VIC', -37.710, 147.830),
('3885', 'Yalmy', 'VIC', -37.710, 147.830),
('3886', 'Newmerella', 'VIC', -37.740, 148.430),
('3887', 'Lake Tyers', 'VIC', -37.840, 148.120),
('3887', 'Nowa Nowa', 'VIC', -37.840, 148.120),
('3887', 'Wairewa', 'VIC', -37.840, 148.120),
('3888', 'Bendoc', 'VIC', -37.150, 148.880),
('3888', 'Bete Bolong', 'VIC', -37.150, 148.880),
('3888', 'Bete Bolong North', 'VIC', -37.150, 148.880),
('3888', 'Bonang', 'VIC', -37.150, 148.880),
('3888', 'Brodribb River', 'VIC', -37.150, 148.880),
('3888', 'Cabanandra', 'VIC', -37.150, 148.880),
('3888', 'Cape Conran', 'VIC', -37.150, 148.880),
('3888', 'Corringle', 'VIC', -37.150, 148.880),
('3888', 'Deddick Valley', 'VIC', -37.150, 148.880),
('3888', 'Goongerah', 'VIC', -37.150, 148.880),
('3888', 'Haydens Bog', 'VIC', -37.150, 148.880),
('3888', 'Jarrahmond', 'VIC', -37.150, 148.880),
('3888', 'Marlo', 'VIC', -37.150, 148.880),
('3888', 'Martins Creek', 'VIC', -37.150, 148.880),
('3888', 'Nurran', 'VIC', -37.150, 148.880),
('3888', 'Orbost', 'VIC', -37.150, 148.880),
('3888', 'Simpsons Creek', 'VIC', -37.150, 148.880),
('3888', 'Tostaree', 'VIC', -37.150, 148.880),
('3888', 'Tubbut', 'VIC', -37.150, 148.880),
('3888', 'Waygara', 'VIC', -37.150, 148.880),
('3888', 'Wombat Creek', 'VIC', -37.150, 148.880),
('3889', 'Bellbird Creek', 'VIC', -37.930, 145.910),
('3889', 'Bemm River', 'VIC', -37.930, 145.910),
('3889', 'Cabbage Tree Creek', 'VIC', -37.930, 145.910),
('3889', 'Club Terrace', 'VIC', -37.930, 145.910),
('3889', 'Combienbar', 'VIC', -37.930, 145.910),
('3889', 'Errinundra', 'VIC', -37.930, 145.910),
('3889', 'Manorina', 'VIC', -37.930, 145.910),
('3890', 'Buldah', 'VIC', -37.270, 149.140),
('3890', 'Cann River', 'VIC', -37.270, 149.140),
('3890', 'Chandlers Creek', 'VIC', -37.270, 149.140),
('3890', 'Noorinbee', 'VIC', -37.270, 149.140),
('3890', 'Noorinbee North', 'VIC', -37.270, 149.140),
('3890', 'Tamboon', 'VIC', -37.270, 149.140),
('3890', 'Tonghi Creek', 'VIC', -37.270, 149.140),
('3891', 'Genoa', 'VIC', -37.480, 149.590),
('3891', 'Gipsy Point', 'VIC', -37.480, 149.590),
('3891', 'Maramingo Creek', 'VIC', -37.480, 149.590),
('3891', 'Wallagaraugh', 'VIC', -37.480, 149.590),
('3891', 'Wangarabell', 'VIC', -37.480, 149.590),
('3891', 'Wingan River', 'VIC', -37.480, 149.590),
('3891', 'Wroxham', 'VIC', -37.480, 149.590),
('3892', 'Mallacoota', 'VIC', -37.550, 149.730),
('3893', 'Double Bridges', 'VIC', -37.590, 147.890),
('3893', 'Stirling', 'VIC', -37.590, 147.890),
('3893', 'Tambo Crossing', 'VIC', -37.590, 147.890),
('3895', 'Doctors Flat', 'VIC', -37.300, 147.750),
('3895', 'Ensay', 'VIC', -37.300, 147.750),
('3895', 'Ensay North', 'VIC', -37.300, 147.750),
('3895', 'Reedy Flat', 'VIC', -37.300, 147.750),
('3896', 'Bindi', 'VIC', -37.110, 147.810),
('3896', 'Brookville', 'VIC', -37.110, 147.810),
('3896', 'Nunniong', 'VIC', -37.110, 147.810),
('3896', 'Swifts Creek', 'VIC', -37.110, 147.810),
('3896', 'Tongio', 'VIC', -37.110, 147.810),
('3898', 'Anglers Rest', 'VIC', -37.020, 147.470),
('3898', 'Bingo Munjie', 'VIC', -37.020, 147.470),
('3898', 'Bundara', 'VIC', -37.020, 147.470),
('3898', 'Cassilis', 'VIC', -37.020, 147.470),
('3898', 'Cobungra', 'VIC', -37.020, 147.470),
('3898', 'Dinner Plain', 'VIC', -37.020, 147.470),
('3898', 'Glen Valley', 'VIC', -37.020, 147.470),
('3898', 'Glen Wills', 'VIC', -37.020, 147.470),
('3898', 'Hinnomunjie', 'VIC', -37.020, 147.470),
('3898', 'Omeo', 'VIC', -37.020, 147.470),
('3898', 'Omeo Valley', 'VIC', -37.020, 147.470),
('3898', 'Shannonvale', 'VIC', -37.020, 147.470),
('3900', 'Benambra', 'VIC', -36.950, 147.700),
('3900', 'Cobberas', 'VIC', -36.950, 147.700),
('3902', 'Bumberrah', 'VIC', -37.790, 147.830),
('3902', 'Johnsonville', 'VIC', -37.790, 147.830),
('3903', 'Swan Reach', 'VIC', -37.810, 147.840),
('3904', 'Metung', 'VIC', -37.870, 147.850),
('3909', 'Kalimna', 'VIC', -37.860, 147.920),
('3909', 'Kalimna West', 'VIC', -37.860, 147.920),
('3909', 'Lake Bunga', 'VIC', -37.860, 147.920),
('3909', 'Lake Tyers Beach', 'VIC', -37.860, 147.920),
('3909', 'Lakes Entrance', 'VIC', -37.860, 147.920),
('3909', 'Nungurner', 'VIC', -37.860, 147.920),
('3909', 'Nyerimilang', 'VIC', -37.860, 147.920),
('3909', 'Toorloo Arm', 'VIC', -37.860, 147.920),
('3910', 'Langwarrin', 'VIC', -38.150, 145.190),
('3911', 'Baxter', 'VIC', -38.200, 145.160),
('3911', 'Langwarrin South', 'VIC', -38.200, 145.160),
('3912', 'Pearcedale', 'VIC', -38.200, 145.230),
('3912', 'Somerville', 'VIC', -38.200, 145.230),
('3913', 'Tyabb', 'VIC', -38.260, 145.190),
('3915', 'Hastings', 'VIC', -38.310, 145.190),
('3915', 'Tuerong', 'VIC', -38.310, 145.190),
('3916', 'Merricks', 'VIC', -38.390, 145.090),
('3916', 'Point Leo', 'VIC', -38.390, 145.090),
('3916', 'Shoreham', 'VIC', -38.390, 145.090),
('3918', 'Bittern', 'VIC', -38.340, 145.180),
('3919', 'Crib Point', 'VIC', -38.370, 145.200),
('3920', 'Hmas Cerberus', 'VIC', 0.000, 0.000),
('3921', 'French Island', 'VIC', -38.340, 145.340),
('3922', 'Cowes', 'VIC', -38.450, 145.240),
('3922', 'Silverleaves', 'VIC', -38.450, 145.240),
('3922', 'Smiths Beach', 'VIC', -38.450, 145.240),
('3922', 'Summerlands', 'VIC', -38.450, 145.240),
('3922', 'Sunderland Bay', 'VIC', -38.450, 145.240),
('3922', 'Sunset Strip', 'VIC', -38.450, 145.240),
('3922', 'Surf Beach', 'VIC', -38.450, 145.240),
('3922', 'Ventnor', 'VIC', -38.450, 145.240),
('3922', 'Wimbledon Heights', 'VIC', -38.450, 145.240),
('3923', 'Rhyll', 'VIC', -38.460, 145.310),
('3925', 'Cape Woolamai', 'VIC', -38.540, 145.340),
('3925', 'Churchill Island', 'VIC', -38.540, 145.340),
('3925', 'Newhaven', 'VIC', -38.540, 145.340),
('3925', 'San Remo', 'VIC', -38.540, 145.340),
('3926', 'Balnarring', 'VIC', -38.370, 145.130),
('3926', 'Balnarring Beach', 'VIC', -38.370, 145.130),
('3926', 'Merricks Beach', 'VIC', -38.370, 145.130),
('3926', 'Merricks North', 'VIC', -38.370, 145.130),
('3927', 'Somers', 'VIC', -38.380, 145.150),
('3928', 'Main Ridge', 'VIC', -38.400, 144.970),
('3929', 'Flinders', 'VIC', -38.470, 145.020),
('3930', 'Kunyung', 'VIC', -38.190, 145.080),
('3930', 'Mount Eliza', 'VIC', -38.190, 145.080),
('3931', 'Mornington', 'VIC', -38.240, 145.060),
('3933', 'Moorooduc', 'VIC', -38.240, 145.090),
('3934', 'Mount Martha', 'VIC', -38.300, 144.990),
('3936', 'Arthurs Seat', 'VIC', -38.350, 144.950),
('3936', 'Dromana', 'VIC', -38.350, 144.950),
('3936', 'Safety Beach', 'VIC', -38.350, 144.950),
('3937', 'Red Hill', 'VIC', -38.370, 145.010),
('3937', 'Red Hill South', 'VIC', -38.370, 145.010),
('3938', 'Mccrae', 'VIC', -37.990, 145.220),
('3939', 'Boneo', 'VIC', -38.400, 144.900),
('3939', 'Cape Schanck', 'VIC', -38.400, 144.900),
('3939', 'Fingal', 'VIC', -38.400, 144.900),
('3939', 'Rosebud', 'VIC', -38.400, 144.900),
('3939', 'Rosebud Plaza', 'VIC', -38.400, 144.900),
('3940', 'Rosebud West', 'VIC', 0.000, 0.000),
('3941', 'Rye', 'VIC', -38.370, 144.820),
('3941', 'St Andrews Beach', 'VIC', -38.370, 144.820),
('3941', 'Tootgarook', 'VIC', -38.370, 144.820),
('3942', 'Blairgowrie', 'VIC', -38.360, 144.770),
('3943', 'Sorrento', 'VIC', -38.340, 144.740),
('3944', 'Portsea', 'VIC', -38.340, 144.710),
('3945', 'Jeetho', 'VIC', -37.970, 145.160),
('3945', 'Krowera', 'VIC', -37.970, 145.160),
('3945', 'Loch', 'VIC', -37.970, 145.160),
('3945', 'Woodleigh', 'VIC', -37.970, 145.160),
('3946', 'Bena', 'VIC', -38.430, 145.720),
('3950', 'Kardella South', 'VIC', -38.450, 145.880),
('3950', 'Korumburra', 'VIC', -38.450, 145.880),
('3950', 'Korumburra South', 'VIC', -38.450, 145.880),
('3950', 'Strzelecki', 'VIC', -38.450, 145.880),
('3950', 'Whitelaw', 'VIC', -38.450, 145.880),
('3951', 'Arawata', 'VIC', -38.390, 145.890),
('3951', 'Fairbank', 'VIC', -38.390, 145.890),
('3951', 'Jumbunna', 'VIC', -38.390, 145.890),
('3951', 'Kardella', 'VIC', -38.390, 145.890),
('3951', 'Kongwak', 'VIC', -38.390, 145.890),
('3951', 'Moyarra', 'VIC', -38.390, 145.890),
('3951', 'Outtrim', 'VIC', -38.390, 145.890),
('3951', 'Ranceby', 'VIC', -38.390, 145.890),
('3953', 'Berrys Creek', 'VIC', -38.410, 146.070),
('3953', 'Boorool', 'VIC', -38.410, 146.070),
('3953', 'Hallston', 'VIC', -38.410, 146.070),
('3953', 'Koorooman', 'VIC', -38.410, 146.070),
('3953', 'Leongatha', 'VIC', -38.410, 146.070),
('3953', 'Leongatha North', 'VIC', -38.410, 146.070),
('3953', 'Leongatha South', 'VIC', -38.410, 146.070),
('3953', 'Mardan', 'VIC', -38.410, 146.070),
('3953', 'Mount Eccles', 'VIC', -38.410, 146.070),
('3953', 'Mount Eccles South', 'VIC', -38.410, 146.070),
('3953', 'Nerrena', 'VIC', -38.410, 146.070),
('3953', 'Ruby', 'VIC', -38.410, 146.070),
('3953', 'Trida', 'VIC', -38.410, 146.070),
('3953', 'Wild Dog Valley', 'VIC', -38.410, 146.070),
('3954', 'Koonwarra', 'VIC', -38.550, 145.950),
('3956', 'Dumbalk', 'VIC', -38.530, 146.100),
('3956', 'Dumbalk North', 'VIC', -38.530, 146.100),
('3956', 'Meeniyan', 'VIC', -38.530, 146.100),
('3956', 'Middle Tarwin', 'VIC', -38.530, 146.100),
('3956', 'Tarwin', 'VIC', -38.530, 146.100),
('3956', 'Tarwin Lower', 'VIC', -38.530, 146.100),
('3956', 'Venus Bay', 'VIC', -38.530, 146.100),
('3956', 'Walkerville', 'VIC', -38.530, 146.100),
('3956', 'Walkerville South', 'VIC', -38.530, 146.100),
('3957', 'Stony Creek', 'VIC', -37.940, 145.270),
('3958', 'Buffalo', 'VIC', -38.640, 146.070),
('3959', 'Fish Creek', 'VIC', -38.690, 146.080),
('3959', 'Sandy Point', 'VIC', -38.690, 146.080),
('3959', 'Waratah Bay', 'VIC', -38.690, 146.080),
('3960', 'Bennison', 'VIC', -38.670, 146.270),
('3960', 'Boolarong', 'VIC', -38.670, 146.270),
('3960', 'Foster', 'VIC', -38.670, 146.270),
('3960', 'Foster North', 'VIC', -38.670, 146.270),
('3960', 'Gunyah', 'VIC', -38.670, 146.270),
('3960', 'Mount Best', 'VIC', -38.670, 146.270),
('3960', 'Shallow Inlet', 'VIC', -38.670, 146.270),
('3960', 'Tidal River', 'VIC', -38.670, 146.270),
('3960', 'Turtons Creek', 'VIC', -38.670, 146.270),
('3960', 'Wilsons Promontory', 'VIC', -38.670, 146.270),
('3960', 'Wonga', 'VIC', -38.670, 146.270),
('3960', 'Woorarra West', 'VIC', -38.670, 146.270),
('3960', 'Yanakie', 'VIC', -38.670, 146.270),
('3962', 'Agnes', 'VIC', -38.670, 146.400),
('3962', 'Toora', 'VIC', -38.670, 146.400),
('3962', 'Toora North', 'VIC', -38.670, 146.400),
('3962', 'Wonyip', 'VIC', -38.670, 146.400),
('3962', 'Woorarra East', 'VIC', -38.670, 146.400),
('3964', 'Port Franklin', 'VIC', -38.680, 146.270),
('3965', 'Port Welshpool', 'VIC', -38.690, 146.450),
('3966', 'Binginwarri', 'VIC', -38.580, 146.450),
('3966', 'Hazel Park', 'VIC', -38.580, 146.450),
('3966', 'Welshpool', 'VIC', -38.580, 146.450),
('3967', 'Hedley', 'VIC', -37.690, 144.960),
('3971', 'Alberton', 'VIC', -38.610, 146.670),
('3971', 'Alberton West', 'VIC', -38.610, 146.670),
('3971', 'Balook', 'VIC', -38.610, 146.670),
('3971', 'Calrossie', 'VIC', -38.610, 146.670),
('3971', 'Devon North', 'VIC', -38.610, 146.670),
('3971', 'Gelliondale', 'VIC', -38.610, 146.670),
('3971', 'Hiawatha', 'VIC', -38.610, 146.670),
('3971', 'Hunterston', 'VIC', -38.610, 146.670),
('3971', 'Jack River', 'VIC', -38.610, 146.670),
('3971', 'Langsborough', 'VIC', -38.610, 146.670),
('3971', 'Macks Creek', 'VIC', -38.610, 146.670),
('3971', 'Madalya', 'VIC', -38.610, 146.670),
('3971', 'Manns Beach', 'VIC', -38.610, 146.670),
('3971', 'Port Albert', 'VIC', -38.610, 146.670),
('3971', 'Robertsons Beach', 'VIC', -38.610, 146.670),
('3971', 'Snake Island', 'VIC', -38.610, 146.670),
('3971', 'Staceys Bridge', 'VIC', -38.610, 146.670),
('3971', 'Tarra Valley', 'VIC', -38.610, 146.670),
('3971', 'Tarraville', 'VIC', -38.610, 146.670),
('3971', 'Won Wron', 'VIC', -38.610, 146.670),
('3971', 'Yarram', 'VIC', -38.610, 146.670),
('3975', 'Lynbrook', 'VIC', -38.050, 145.260),
('3975', 'Lyndhurst', 'VIC', -38.050, 145.260),
('3976', 'Hampton Park', 'VIC', -38.030, 145.270),
('3977', 'Botanic Ridge', 'VIC', -38.210, 145.320),
('3977', 'Cannons Creek', 'VIC', -38.210, 145.320),
('3977', 'Cranbourne', 'VIC', -38.210, 145.320),
('3977', 'Cranbourne East', 'VIC', -38.210, 145.320),
('3977', 'Cranbourne North', 'VIC', -38.210, 145.320),
('3977', 'Cranbourne South', 'VIC', -38.210, 145.320),
('3977', 'Cranbourne West', 'VIC', -38.210, 145.320),
('3977', 'Devon Meadows', 'VIC', -38.210, 145.320),
('3977', 'Junction Village', 'VIC', -38.210, 145.320),
('3977', 'Sandhurst', 'VIC', -38.210, 145.320),
('3977', 'Skye', 'VIC', -38.210, 145.320),
('3978', 'Cardinia', 'VIC', -38.150, 145.420),
('3978', 'Clyde', 'VIC', -38.150, 145.420),
('3978', 'Clyde North', 'VIC', -38.150, 145.420),
('3979', 'Almurta', 'VIC', -38.430, 145.560),
('3979', 'Glen Alvie', 'VIC', -38.430, 145.560),
('3979', 'Kernot', 'VIC', -38.430, 145.560),
('3980', 'Blind Bight', 'VIC', -38.210, 145.340),
('3980', 'Tooradin', 'VIC', -38.210, 145.340),
('3980', 'Warneet', 'VIC', -38.210, 145.340),
('3981', 'Bayles', 'VIC', -38.180, 145.570),
('3981', 'Catani', 'VIC', -38.180, 145.570),
('3981', 'Dalmore', 'VIC', -38.180, 145.570),
('3981', 'Heath Hill', 'VIC', -38.180, 145.570),
('3981', 'Koo Wee Rup', 'VIC', -38.180, 145.570),
('3981', 'Koo Wee Rup North', 'VIC', -38.180, 145.570),
('3981', 'Yannathan', 'VIC', -38.180, 145.570),
('3984', 'Adams Estate', 'VIC', -38.320, 145.540),
('3984', 'Caldermeade', 'VIC', -38.320, 145.540),
('3984', 'Corinella', 'VIC', -38.320, 145.540),
('3984', 'Coronet Bay', 'VIC', -38.320, 145.540),
('3984', 'Grantville', 'VIC', -38.320, 145.540),
('3984', 'Jam Jerrup', 'VIC', -38.320, 145.540),
('3984', 'Lang Lang', 'VIC', -38.320, 145.540),
('3984', 'Lang Lang East', 'VIC', -38.320, 145.540),
('3984', 'Monomeith', 'VIC', -38.320, 145.540),
('3984', 'Pioneer Bay', 'VIC', -38.320, 145.540),
('3984', 'Queensferry', 'VIC', -38.320, 145.540),
('3984', 'Tenby Point', 'VIC', -38.320, 145.540),
('3984', 'The Gurdies', 'VIC', -38.320, 145.540),
('3987', 'Nyora', 'VIC', -38.330, 145.670),
('3988', 'Mountain View', 'VIC', -38.290, 145.890),
('3988', 'Poowong', 'VIC', -38.290, 145.890),
('3988', 'Poowong East', 'VIC', -38.290, 145.890),
('3988', 'Poowong North', 'VIC', -38.290, 145.890),
('3990', 'Glen Forbes', 'VIC', -38.450, 145.530),
('3991', 'Bass', 'VIC', -38.480, 145.470),
('3992', 'Blackwood Forest', 'VIC', -38.490, 145.620),
('3992', 'Dalyston', 'VIC', -38.490, 145.620),
('3992', 'Ryanston', 'VIC', -38.490, 145.620),
('3992', 'West Creek', 'VIC', -38.490, 145.620),
('3995', 'Anderson', 'VIC', -38.530, 145.450),
('3995', 'Archies Creek', 'VIC', -38.530, 145.450),
('3995', 'Cape Paterson', 'VIC', -38.530, 145.450),
('3995', 'Harmers Haven', 'VIC', -38.530, 145.450),
('3995', 'Kilcunda', 'VIC', -38.530, 145.450),
('3995', 'Lance Creek', 'VIC', -38.530, 145.450),
('3995', 'North Wonthaggi', 'VIC', -38.530, 145.450),
('3995', 'South Dudley', 'VIC', -38.530, 145.450),
('3995', 'St Clair', 'VIC', -38.530, 145.450),
('3995', 'Wattle Bank', 'VIC', -38.530, 145.450),
('3995', 'Wonthaggi', 'VIC', -38.530, 145.450),
('3995', 'Woolamai', 'VIC', -38.530, 145.450),
('3996', 'Inverloch', 'VIC', -38.630, 145.730),
('3996', 'Pound Creek', 'VIC', -38.630, 145.730),
('6000', 'City Delivery Centre', 'WA', -31.920, 115.910),
('6000', 'Perth', 'WA', -31.920, 115.910),
('6000', 'Perth GPO', 'WA', -31.920, 115.910),
('6001', 'Perth', 'WA', -31.990, 115.760),
('6003', 'Highgate', 'WA', -31.940, 115.870),
('6003', 'Northbridge', 'WA', -31.940, 115.870),
('6004', 'East Perth', 'WA', -31.940, 115.880),
('6005', 'Kings Park', 'WA', -31.970, 115.840),
('6005', 'West Perth', 'WA', -31.970, 115.840),
('6006', 'North Perth', 'WA', -31.980, 115.760),
('6007', 'Leederville', 'WA', -31.940, 115.840),
('6007', 'West Leederville', 'WA', -31.940, 115.840),
('6008', 'Daglish', 'WA', -31.950, 115.810),
('6008', 'Shenton Park', 'WA', -31.950, 115.810),
('6008', 'Subiaco', 'WA', -31.950, 115.810),
('6008', 'Subiaco East', 'WA', -31.950, 115.810),
('6009', 'Broadway Nedlands', 'WA', -31.980, 115.810),
('6009', 'Crawley', 'WA', -31.980, 115.810),
('6009', 'Dalkeith', 'WA', -31.980, 115.810),
('6009', 'Nedlands', 'WA', -31.980, 115.810),
('6009', 'Nedlands DC', 'WA', -31.980, 115.810),
('6010', 'Claremont', 'WA', -31.980, 115.780),
('6010', 'Claremont North', 'WA', -31.980, 115.780),
('6010', 'Karrakatta', 'WA', -31.980, 115.780),
('6010', 'Mount Claremont', 'WA', -31.980, 115.780),
('6010', 'Swanbourne', 'WA', -31.980, 115.780),
('6011', 'Cottesloe', 'WA', -32.000, 115.760),
('6011', 'Peppermint Grove', 'WA', -32.000, 115.760),
('6012', 'Mosman Park', 'WA', -32.010, 115.760),
('6014', 'Floreat', 'WA', -31.940, 115.790),
('6014', 'Floreat Forum', 'WA', -31.940, 115.790),
('6014', 'Jolimont', 'WA', -31.940, 115.790),
('6014', 'Wembley', 'WA', -31.940, 115.790),
('6015', 'City Beach', 'WA', -31.940, 115.750),
('6016', 'Glendalough', 'WA', -31.920, 115.810),
('6016', 'Mount Hawthorn', 'WA', -31.920, 115.810),
('6017', 'Herdsman', 'WA', -31.910, 115.810),
('6017', 'Osborne Park', 'WA', -31.910, 115.810),
('6017', 'Osborne Park DC', 'WA', -31.910, 115.810),
('6018', 'Churchlands', 'WA', -31.920, 115.790),
('6018', 'Doubleview', 'WA', -31.920, 115.790),
('6018', 'Gwelup', 'WA', -31.920, 115.790),
('6018', 'Gwelup DC', 'WA', -31.920, 115.790),
('6018', 'Innaloo', 'WA', -31.920, 115.790),
('6018', 'Karrinyup', 'WA', -31.920, 115.790),
('6018', 'Woodlands', 'WA', -31.920, 115.790),
('6019', 'Scarborough', 'WA', -31.890, 115.760),
('6019', 'Wembley Downs', 'WA', -31.890, 115.760),
('6020', 'Carine', 'WA', -31.850, 115.780),
('6020', 'Marmion', 'WA', -31.850, 115.780),
('6020', 'North Beach', 'WA', -31.850, 115.780),
('6020', 'Sorrento', 'WA', -31.850, 115.780),
('6020', 'Watermans Bay', 'WA', -31.850, 115.780),
('6021', 'Balcatta', 'WA', -31.860, 115.820),
('6021', 'Stirling', 'WA', -31.860, 115.820),
('6022', 'Hamersley', 'WA', -31.850, 115.800),
('6023', 'Duncraig', 'WA', -31.830, 115.770),
('6024', 'Greenwood', 'WA', -31.830, 115.800),
('6024', 'Warwick', 'WA', -31.830, 115.800),
('6025', 'Craigie', 'WA', -31.780, 115.770),
('6025', 'Hillarys', 'WA', -31.780, 115.770),
('6025', 'Kallaroo', 'WA', -31.780, 115.770),
('6025', 'Padbury', 'WA', -31.780, 115.770),
('6026', 'Kingsley', 'WA', -31.810, 115.790),
('6026', 'Woodvale', 'WA', -31.810, 115.790),
('6027', 'Beldon', 'WA', -31.780, 115.760),
('6027', 'Connolly', 'WA', -31.780, 115.760),
('6027', 'Edgewater', 'WA', -31.780, 115.760),
('6027', 'Heathridge', 'WA', -31.780, 115.760),
('6027', 'Joondalup', 'WA', -31.780, 115.760),
('6027', 'Joondalup DC', 'WA', -31.780, 115.760),
('6027', 'Mullaloo', 'WA', -31.780, 115.760),
('6027', 'Ocean Reef', 'WA', -31.780, 115.760),
('6028', 'Burns Beach', 'WA', -31.730, 115.720),
('6028', 'Currambine', 'WA', -31.730, 115.720),
('6028', 'Iluka', 'WA', -31.730, 115.720),
('6028', 'Kinross', 'WA', -31.730, 115.720),
('6029', 'Trigg', 'WA', -31.880, 115.750),
('6030', 'Clarkson', 'WA', -31.680, 115.720),
('6030', 'Merriwa', 'WA', -31.680, 115.720),
('6030', 'Mindarie', 'WA', -31.680, 115.720),
('6030', 'Quinns Rocks', 'WA', -31.680, 115.720),
('6030', 'Ridgewood', 'WA', -31.680, 115.720),
('6030', 'Tamala Park', 'WA', -31.680, 115.720),
('6031', 'Banksia Grove', 'WA', -31.700, 115.810),
('6031', 'Carramar', 'WA', -31.700, 115.810),
('6031', 'Neerabup', 'WA', -31.700, 115.810),
('6032', 'Nowergup', 'WA', -31.640, 115.740),
('6033', 'Carabooda', 'WA', -31.600, 115.750),
('6034', 'Eglinton', 'WA', -31.850, 115.810),
('6035', 'Yanchep', 'WA', -31.550, 115.630),
('6036', 'Butler', 'WA', -31.650, 115.710),
('6036', 'Jindalee', 'WA', -31.650, 115.710),
('6037', 'Two Rocks', 'WA', -31.510, 115.600),
('6038', 'Alkimos', 'WA', -32.290, 115.720),
('6041', 'Caraban', 'WA', -31.350, 115.530),
('6041', 'Gabbadah', 'WA', -31.350, 115.530),
('6041', 'Guilderton', 'WA', -31.350, 115.530),
('6041', 'Wilbinga', 'WA', -31.350, 115.530),
('6041', 'Woodridge', 'WA', -31.350, 115.530),
('6042', 'Seabird', 'WA', -31.250, 115.450),
('6043', 'Breton Bay', 'WA', -31.200, 115.420),
('6043', 'Ledge Point', 'WA', -31.200, 115.420),
('6044', 'Karakin', 'WA', -31.050, 115.420),
('6044', 'Lancelin', 'WA', -31.050, 115.420),
('6044', 'Nilgen', 'WA', -31.050, 115.420),
('6044', 'Wedge Island', 'WA', -31.050, 115.420),
('6050', 'Coolbinia', 'WA', -31.910, 115.860),
('6050', 'Menora', 'WA', -31.910, 115.860),
('6050', 'Mount Lawley', 'WA', -31.910, 115.860),
('6051', 'Maylands', 'WA', -31.930, 115.890),
('6052', 'Bedford', 'WA', -31.910, 115.890),
('6052', 'Inglewood', 'WA', -31.910, 115.890),
('6053', 'Bayswater', 'WA', -31.920, 115.910),
('6054', 'Ashfield', 'WA', -31.920, 115.940),
('6054', 'Bassendean', 'WA', -31.920, 115.940),
('6054', 'Bassendean DC', 'WA', -31.920, 115.940),
('6054', 'Eden Hill', 'WA', -31.920, 115.940),
('6054', 'Kiara', 'WA', -31.920, 115.940),
('6054', 'Lockridge', 'WA', -31.920, 115.940),
('6055', 'Caversham', 'WA', -31.870, 115.980),
('6055', 'Guildford', 'WA', -31.870, 115.980),
('6055', 'Hazelmere', 'WA', -31.870, 115.980),
('6055', 'Henley Brook', 'WA', -31.870, 115.980),
('6055', 'South Guildford', 'WA', -31.870, 115.980),
('6055', 'West Swan', 'WA', -31.870, 115.980),
('6056', 'Baskerville', 'WA', -31.800, 116.030),
('6056', 'Bellevue', 'WA', -31.800, 116.030),
('6056', 'Boya', 'WA', -31.800, 116.030),
('6056', 'Greenmount', 'WA', -31.800, 116.030),
('6056', 'Helena Valley', 'WA', -31.800, 116.030),
('6056', 'Herne Hill', 'WA', -31.800, 116.030),
('6056', 'Jane Brook', 'WA', -31.800, 116.030),
('6056', 'Koongamia', 'WA', -31.800, 116.030),
('6056', 'Middle Swan', 'WA', -31.800, 116.030),
('6056', 'Midland', 'WA', -31.800, 116.030),
('6056', 'Midvale', 'WA', -31.800, 116.030),
('6056', 'Millendon', 'WA', -31.800, 116.030),
('6056', 'Red Hill', 'WA', -31.800, 116.030),
('6056', 'Stratton', 'WA', -31.800, 116.030),
('6056', 'Swan View', 'WA', -31.800, 116.030),
('6056', 'Viveash', 'WA', -31.800, 116.030),
('6056', 'Woodbridge', 'WA', -31.800, 116.030),
('6057', 'High Wycombe', 'WA', -31.940, 116.010),
('6057', 'Maida Vale', 'WA', -31.940, 116.010),
('6058', 'Forrestfield', 'WA', -31.990, 116.010),
('6059', 'Dianella', 'WA', -31.880, 115.860),
('6060', 'Dog Swamp', 'WA', -31.910, 115.850),
('6060', 'Joondanna', 'WA', -31.910, 115.850),
('6060', 'Tuart Hill', 'WA', -31.910, 115.850),
('6060', 'Yokine', 'WA', -31.910, 115.850),
('6061', 'Balga', 'WA', -31.860, 115.840),
('6061', 'Mirrabooka', 'WA', -31.860, 115.840),
('6061', 'Nollamara', 'WA', -31.860, 115.840),
('6061', 'Westminster', 'WA', -31.860, 115.840),
('6062', 'Embleton', 'WA', -31.900, 115.920),
('6062', 'Morley', 'WA', -31.900, 115.920),
('6062', 'Noranda', 'WA', -31.900, 115.920),
('6063', 'Beechboro', 'WA', -31.870, 115.920),
('6064', 'Alexander Heights', 'WA', -31.830, 115.870),
('6064', 'Girrawheen', 'WA', -31.830, 115.870),
('6064', 'Koondoola', 'WA', -31.830, 115.870),
('6064', 'Marangaroo', 'WA', -31.830, 115.870),
('6065', 'Ashby', 'WA', -31.730, 115.790),
('6065', 'Darch', 'WA', -31.730, 115.790),
('6065', 'Hocking', 'WA', -31.730, 115.790),
('6065', 'Kingsway', 'WA', -31.730, 115.790),
('6065', 'Landsdale', 'WA', -31.730, 115.790),
('6065', 'Madeley', 'WA', -31.730, 115.790),
('6065', 'Pearsall', 'WA', -31.730, 115.790),
('6065', 'Sinagra', 'WA', -31.730, 115.790),
('6065', 'Tapping', 'WA', -31.730, 115.790),
('6065', 'Wangara', 'WA', -31.730, 115.790),
('6065', 'Wangara DC', 'WA', -31.730, 115.790),
('6065', 'Wanneroo', 'WA', -31.730, 115.790),
('6066', 'Ballajura', 'WA', -31.840, 115.890),
('6067', 'Cullacabardee', 'WA', -31.820, 115.910),
('6068', 'Whiteman', 'WA', -31.830, 115.940),
('6069', 'Aveley', 'WA', -31.770, 116.010),
('6069', 'Belhus', 'WA', -31.770, 116.010),
('6069', 'Brigadoon', 'WA', -31.770, 116.010),
('6069', 'Ellenbrook', 'WA', -31.770, 116.010),
('6069', 'Ellenbrook East', 'WA', -31.770, 116.010),
('6069', 'The Vines', 'WA', -31.770, 116.010),
('6069', 'Upper Swan', 'WA', -31.770, 116.010),
('6070', 'Darlington', 'WA', -31.920, 116.080),
('6071', 'Glen Forrest', 'WA', -31.910, 116.100),
('6071', 'Hovea', 'WA', -31.910, 116.100),
('6072', 'Mahogany Creek', 'WA', -31.870, 116.180),
('6073', 'Mundaring', 'WA', -31.900, 116.170),
('6073', 'Mundaring DC', 'WA', -31.900, 116.170),
('6074', 'Sawyers Valley', 'WA', -31.900, 116.200),
('6076', 'Bickley', 'WA', -32.010, 116.090),
('6076', 'Carmel', 'WA', -32.010, 116.090),
('6076', 'Gooseberry Hill', 'WA', -32.010, 116.090),
('6076', 'Hacketts Gully', 'WA', -32.010, 116.090),
('6076', 'Kalamunda', 'WA', -32.010, 116.090),
('6076', 'Lesmurdie', 'WA', -32.010, 116.090),
('6076', 'Paulls Valley', 'WA', -32.010, 116.090),
('6076', 'Pickering Brook', 'WA', -32.010, 116.090),
('6076', 'Piesse Brook', 'WA', -32.010, 116.090),
('6076', 'Reservoir', 'WA', -32.010, 116.090),
('6076', 'Walliston', 'WA', -32.010, 116.090),
('6076', 'Walliston DC', 'WA', -32.010, 116.090),
('6077', 'Gnangara', 'WA', 0.000, 0.000),
('6077', 'Jandabup', 'WA', 0.000, 0.000),
('6078', 'Mariginiup', 'WA', 0.000, 0.000),
('6078', 'Pinjar', 'WA', 0.000, 0.000),
('6079', 'Lexia', 'WA', 0.000, 0.000),
('6079', 'Melaleuca', 'WA', 0.000, 0.000),
('6081', 'Parkerville', 'WA', -31.880, 116.150),
('6081', 'Stoneville', 'WA', -31.880, 116.150),
('6082', 'Bailup', 'WA', -31.740, 116.300),
('6082', 'Mount Helena', 'WA', -31.740, 116.300),
('6083', 'Gidgegannup', 'WA', -31.790, 116.200),
('6083', 'Morangup', 'WA', -31.790, 116.200),
('6084', 'Avon Valley National Park', 'WA', -31.620, 116.210),
('6084', 'Bullsbrook', 'WA', -31.620, 116.210),
('6084', 'Chittering', 'WA', -31.620, 116.210),
('6084', 'Lower Chittering', 'WA', -31.620, 116.210),
('6084', 'Walyunga National Park', 'WA', -31.620, 116.210),
('6090', 'Malaga', 'WA', -31.860, 115.890),
('6100', 'Burswood', 'WA', -31.960, 115.900),
('6100', 'Lathlain', 'WA', -31.960, 115.900),
('6100', 'Victoria Park', 'WA', -31.960, 115.900),
('6101', 'Carlisle', 'WA', -31.980, 115.910),
('6101', 'Carlisle North', 'WA', -31.980, 115.910),
('6101', 'Carlisle South', 'WA', -31.980, 115.910),
('6101', 'East Victoria Park', 'WA', -31.980, 115.910),
('6102', 'Bentley', 'WA', -32.000, 115.920),
('6102', 'Bentley DC', 'WA', -32.000, 115.920),
('6102', 'Bentley South', 'WA', -32.000, 115.920),
('6102', 'St James', 'WA', -32.000, 115.920),
('6103', 'Rivervale', 'WA', -31.960, 115.910),
('6104', 'Ascot', 'WA', -31.940, 115.920),
('6104', 'Belmont', 'WA', -31.940, 115.920),
('6104', 'Redcliffe', 'WA', -31.940, 115.920),
('6105', 'Cloverdale', 'WA', -31.960, 115.930),
('6105', 'Kewdale', 'WA', -31.960, 115.930),
('6105', 'Perth Airport', 'WA', -31.960, 115.930),
('6106', 'Welshpool', 'WA', -31.990, 115.950),
('6106', 'Welshpool DC', 'WA', -31.990, 115.950),
('6107', 'Beckenham', 'WA', -32.010, 115.960),
('6107', 'Cannington', 'WA', -32.010, 115.960),
('6107', 'East Cannington', 'WA', -32.010, 115.960),
('6107', 'Kenwick', 'WA', -32.010, 115.960),
('6107', 'Queens Park', 'WA', -32.010, 115.960),
('6107', 'Wattle Grove', 'WA', -32.010, 115.960),
('6107', 'Wilson', 'WA', -32.010, 115.960),
('6108', 'Thornlie', 'WA', -32.050, 115.960),
('6109', 'Maddington', 'WA', -32.050, 115.980),
('6109', 'Orange Grove', 'WA', -32.050, 115.980),
('6110', 'Gosnells', 'WA', -32.070, 116.000),
('6110', 'Huntingdale', 'WA', -32.070, 116.000),
('6110', 'Martin', 'WA', -32.070, 116.000),
('6110', 'Southern River', 'WA', -32.070, 116.000),
('6111', 'Ashendon', 'WA', -32.100, 116.190),
('6111', 'Camillo', 'WA', -32.100, 116.190),
('6111', 'Canning Mills', 'WA', -32.100, 116.190),
('6111', 'Champion Lakes', 'WA', -32.100, 116.190),
('6111', 'Karragullen', 'WA', -32.100, 116.190),
('6111', 'Kelmscott', 'WA', -32.100, 116.190),
('6111', 'Kelmscott DC', 'WA', -32.100, 116.190),
('6111', 'Lesley', 'WA', -32.100, 116.190),
('6111', 'Roleystone', 'WA', -32.100, 116.190),
('6112', 'Armadale', 'WA', -32.150, 116.010),
('6112', 'Bedfordale', 'WA', -32.150, 116.010),
('6112', 'Brookdale', 'WA', -32.150, 116.010),
('6112', 'Forrestdale', 'WA', -32.150, 116.010),
('6112', 'Harrisdale', 'WA', -32.150, 116.010),
('6112', 'Haynes', 'WA', -32.150, 116.010),
('6112', 'Hilbert', 'WA', -32.150, 116.010),
('6112', 'Mount Nasura', 'WA', -32.150, 116.010),
('6112', 'Mount Richon', 'WA', -32.150, 116.010),
('6112', 'Piara Waters', 'WA', -32.150, 116.010),
('6112', 'Seville Grove', 'WA', -32.150, 116.010),
('6112', 'Wungong', 'WA', -32.150, 116.010),
('6121', 'Oakford', 'WA', -32.210, 115.920),
('6121', 'Oldbury', 'WA', -32.210, 115.920),
('6122', 'Byford', 'WA', -32.220, 116.010),
('6122', 'Cardup', 'WA', -32.220, 116.010),
('6122', 'Darling Downs', 'WA', -32.220, 116.010),
('6122', 'Karrakup', 'WA', -32.220, 116.010),
('6123', 'Mundijong', 'WA', -32.300, 115.990),
('6123', 'Whitby', 'WA', -32.300, 115.990),
('6124', 'Jarrahdale', 'WA', -32.340, 116.070),
('6125', 'Hopeland', 'WA', -32.360, 115.900),
('6125', 'Mardella', 'WA', -32.360, 115.900),
('6125', 'Serpentine', 'WA', -32.360, 115.900),
('6126', 'Keysbrook', 'WA', -32.440, 115.980),
('6147', 'Langford', 'WA', -32.040, 115.940),
('6147', 'Lynwood', 'WA', -32.040, 115.940),
('6147', 'Parkwood', 'WA', -32.040, 115.940),
('6148', 'Ferndale', 'WA', -32.030, 115.920),
('6148', 'Riverton', 'WA', -32.030, 115.920),
('6148', 'Rossmoyne', 'WA', -32.030, 115.920),
('6148', 'Shelley', 'WA', -32.030, 115.920),
('6149', 'Bull Creek', 'WA', -32.060, 115.860),
('6149', 'Leeming', 'WA', -32.060, 115.860),
('6150', 'Bateman', 'WA', -32.060, 115.840),
('6150', 'Murdoch', 'WA', -32.060, 115.840),
('6150', 'Winthrop', 'WA', -32.060, 115.840),
('6151', 'Kensington', 'WA', -31.990, 115.880),
('6151', 'South Perth', 'WA', -31.990, 115.880),
('6151', 'South Perth Angelo St', 'WA', -31.990, 115.880),
('6152', 'Como', 'WA', -31.990, 115.870),
('6152', 'Karawara', 'WA', -31.990, 115.870),
('6152', 'Manning', 'WA', -31.990, 115.870),
('6152', 'Salter Point', 'WA', -31.990, 115.870),
('6152', 'Waterford', 'WA', -31.990, 115.870),
('6153', 'Applecross', 'WA', -32.020, 115.830),
('6153', 'Applecross North', 'WA', -32.020, 115.830),
('6153', 'Ardross', 'WA', -32.020, 115.830),
('6153', 'Brentwood', 'WA', -32.020, 115.830),
('6153', 'Canning Bridge Applecross', 'WA', -32.020, 115.830),
('6153', 'Mount Pleasant', 'WA', -32.020, 115.830),
('6154', 'Alfred Cove', 'WA', -32.030, 115.810),
('6154', 'Booragoon', 'WA', -32.030, 115.810),
('6154', 'Myaree', 'WA', -32.030, 115.810),
('6155', 'Canning Vale', 'WA', -32.060, 115.920),
('6155', 'Canning Vale DC', 'WA', -32.060, 115.920),
('6155', 'Canning Vale East', 'WA', -32.060, 115.920),
('6155', 'Canning Vale South', 'WA', -32.060, 115.920),
('6155', 'Willetton', 'WA', -32.060, 115.920),
('6156', 'Attadale', 'WA', -32.030, 115.800),
('6156', 'Melville', 'WA', -32.030, 115.800),
('6156', 'Willagee', 'WA', -32.030, 115.800),
('6156', 'Willagee Central', 'WA', -32.030, 115.800),
('6157', 'Bicton', 'WA', -32.030, 115.780),
('6157', 'Palmyra', 'WA', -32.030, 115.780),
('6157', 'Palmyra DC', 'WA', -32.030, 115.780),
('6158', 'East Fremantle', 'WA', -32.040, 115.760),
('6159', 'North Fremantle', 'WA', -32.030, 115.750),
('6160', 'Fremantle', 'WA', -32.040, 115.760),
('6161', 'Rottnest Island', 'WA', -32.010, 115.500),
('6162', 'Beaconsfield', 'WA', -32.070, 115.760),
('6162', 'South Fremantle', 'WA', -32.070, 115.760),
('6162', 'White Gum Valley', 'WA', -32.070, 115.760),
('6163', 'Bibra Lake', 'WA', -32.090, 115.840),
('6163', 'Bibra Lake DC', 'WA', -32.090, 115.840),
('6163', 'Coolbellup', 'WA', -32.090, 115.840),
('6163', 'Hamilton Hill', 'WA', -32.090, 115.840),
('6163', 'Hilton', 'WA', -32.090, 115.840),
('6163', 'Kardinya', 'WA', -32.090, 115.840),
('6163', 'North Coogee', 'WA', -32.090, 115.840),
('6163', 'North Lake', 'WA', -32.090, 115.840),
('6163', 'O\'Connor', 'WA', -32.090, 115.840),
('6163', 'Samson', 'WA', -32.090, 115.840),
('6163', 'Spearwood', 'WA', -32.090, 115.840),
('6164', 'Atwell', 'WA', -32.130, 115.860),
('6164', 'Aubin Grove', 'WA', -32.130, 115.860),
('6164', 'Banjup', 'WA', -32.130, 115.860),
('6164', 'Beeliar', 'WA', -32.130, 115.860),
('6164', 'Cockburn Central', 'WA', -32.130, 115.860),
('6164', 'Hammond Park', 'WA', -32.130, 115.860),
('6164', 'Jandakot', 'WA', -32.130, 115.860),
('6164', 'South Lake', 'WA', -32.130, 115.860),
('6164', 'Success', 'WA', -32.130, 115.860),
('6164', 'Yangebup', 'WA', -32.130, 115.860),
('6165', 'Hope Valley', 'WA', -32.190, 115.800),
('6165', 'Naval Base', 'WA', -32.190, 115.800),
('6166', 'Coogee', 'WA', -32.120, 115.770),
('6166', 'Henderson', 'WA', -32.120, 115.770),
('6166', 'Munster', 'WA', -32.120, 115.770),
('6166', 'Wattleup', 'WA', -32.120, 115.770),
('6167', 'Anketell', 'WA', -32.230, 115.870),
('6167', 'Bertram', 'WA', -32.230, 115.870),
('6167', 'Calista', 'WA', -32.230, 115.870),
('6167', 'Casuarina', 'WA', -32.230, 115.870),
('6167', 'Kwinana Beach', 'WA', -32.230, 115.870),
('6167', 'Kwinana Town Centre', 'WA', -32.230, 115.870),
('6167', 'Mandogalup', 'WA', -32.230, 115.870),
('6167', 'Medina', 'WA', -32.230, 115.870),
('6167', 'Orelia', 'WA', -32.230, 115.870),
('6167', 'Parmelia', 'WA', -32.230, 115.870),
('6167', 'Postans', 'WA', -32.230, 115.870),
('6167', 'The Spectacles', 'WA', -32.230, 115.870),
('6167', 'Wandi', 'WA', -32.230, 115.870),
('6168', 'Cooloongup', 'WA', -32.300, 115.760),
('6168', 'East Rockingham', 'WA', -32.300, 115.760),
('6168', 'Garden Island', 'WA', -32.300, 115.760),
('6168', 'Hillman', 'WA', -32.300, 115.760),
('6168', 'Peron', 'WA', -32.300, 115.760),
('6168', 'Rockingham', 'WA', -32.300, 115.760),
('6168', 'Rockingham Beach', 'WA', -32.300, 115.760),
('6168', 'Rockingham DC', 'WA', -32.300, 115.760),
('6169', 'Safety Bay', 'WA', -32.300, 115.710),
('6169', 'Shoalwater', 'WA', -32.300, 115.710),
('6169', 'Waikiki', 'WA', -32.300, 115.710),
('6169', 'Warnbro', 'WA', -32.300, 115.710),
('6170', 'Leda', 'WA', -32.260, 115.810),
('6170', 'Wellard', 'WA', -32.260, 115.810),
('6171', 'Baldivis', 'WA', -32.330, 115.820),
('6172', 'Port Kennedy', 'WA', -32.380, 115.750),
('6173', 'Secret Harbour', 'WA', -32.400, 115.750),
('6174', 'Golden Bay', 'WA', -32.430, 115.750),
('6175', 'Singleton', 'WA', -32.450, 115.760),
('6176', 'Karnup', 'WA', -32.360, 115.910),
('6180', 'Lakelands', 'WA', -31.780, 115.860),
('6180', 'Parklands', 'WA', -31.780, 115.860),
('6181', 'Stake Hill', 'WA', 0.000, 0.000),
('6182', 'Keralup', 'WA', 0.000, 0.000),
('6207', 'Myara', 'WA', -32.490, 116.070),
('6207', 'Nambeelup', 'WA', -32.490, 116.070),
('6207', 'North Dandalup', 'WA', -32.490, 116.070),
('6207', 'Solus', 'WA', -32.490, 116.070),
('6207', 'Whittaker', 'WA', -32.490, 116.070),
('6208', 'Blythewood', 'WA', -32.640, 115.870),
('6208', 'Fairbridge', 'WA', -32.640, 115.870),
('6208', 'Meelon', 'WA', -32.640, 115.870),
('6208', 'Nirimba', 'WA', -32.640, 115.870),
('6208', 'North Yunderup', 'WA', -32.640, 115.870),
('6208', 'Oakley', 'WA', -32.640, 115.870),
('6208', 'Pinjarra', 'WA', -32.640, 115.870),
('6208', 'Point Grey', 'WA', -32.640, 115.870),
('6208', 'Ravenswood', 'WA', -32.640, 115.870),
('6208', 'South Yunderup', 'WA', -32.640, 115.870),
('6208', 'West Pinjarra', 'WA', -32.640, 115.870),
('6209', 'Barragup', 'WA', -32.560, 115.770),
('6209', 'Furnissdale', 'WA', -32.560, 115.770),
('6210', 'Coodanup', 'WA', -32.550, 115.750),
('6210', 'Dudley Park', 'WA', -32.550, 115.750),
('6210', 'Erskine', 'WA', -32.550, 115.750),
('6210', 'Falcon', 'WA', -32.550, 115.750),
('6210', 'Greenfields', 'WA', -32.550, 115.750),
('6210', 'Halls Head', 'WA', -32.550, 115.750),
('6210', 'Madora Bay', 'WA', -32.550, 115.750),
('6210', 'Mandurah', 'WA', -32.550, 115.750),
('6210', 'Mandurah DC', 'WA', -32.550, 115.750),
('6210', 'Mandurah East', 'WA', -32.550, 115.750),
('6210', 'Mandurah North', 'WA', -32.550, 115.750),
('6210', 'Meadow Springs', 'WA', -32.550, 115.750),
('6210', 'San Remo', 'WA', -32.550, 115.750),
('6210', 'Silver Sands', 'WA', -32.550, 115.750),
('6210', 'Wannanup', 'WA', -32.550, 115.750),
('6211', 'Bouvard', 'WA', -32.690, 115.650),
('6211', 'Clifton', 'WA', -32.690, 115.650),
('6211', 'Dawesville', 'WA', -32.690, 115.650),
('6211', 'Herron', 'WA', -32.690, 115.650),
('6213', 'Banksiadale', 'WA', -32.620, 116.080),
('6213', 'Dwellingup', 'WA', -32.620, 116.080),
('6213', 'Etmilyn', 'WA', -32.620, 116.080),
('6213', 'Holyoake', 'WA', -32.620, 116.080),
('6213', 'Inglehope', 'WA', -32.620, 116.080),
('6213', 'Marrinup', 'WA', -32.620, 116.080),
('6213', 'Teesdale', 'WA', -32.620, 116.080),
('6214', 'Birchmont', 'WA', -32.730, 115.760),
('6214', 'Coolup', 'WA', -32.730, 115.760),
('6214', 'West Coolup', 'WA', -32.730, 115.760),
('6215', 'Hamel', 'WA', -32.880, 115.920),
('6215', 'Lake Clifton', 'WA', -32.880, 115.920),
('6215', 'Nanga Brook', 'WA', -32.880, 115.920),
('6215', 'Preston Beach', 'WA', -32.880, 115.920),
('6215', 'Wagerup', 'WA', -32.880, 115.920),
('6215', 'Waroona', 'WA', -32.880, 115.920),
('6218', 'Yarloop', 'WA', -32.960, 115.900),
('6220', 'Cookernup', 'WA', -33.000, 115.890),
('6220', 'Harvey', 'WA', -33.000, 115.890),
('6220', 'Hoffman', 'WA', -33.000, 115.890),
('6220', 'Myalup', 'WA', -33.000, 115.890),
('6220', 'Uduc', 'WA', -33.000, 115.890),
('6220', 'Warawarrup', 'WA', -33.000, 115.890),
('6221', 'Mornington', 'WA', -33.150, 115.940),
('6221', 'Wokalup', 'WA', -33.150, 115.940),
('6223', 'Benger', 'WA', -33.170, 115.860),
('6224', 'Beela', 'WA', -33.230, 115.910),
('6224', 'Brunswick', 'WA', -33.230, 115.910),
('6225', 'Allanson', 'WA', -33.340, 116.100),
('6225', 'Bowelling', 'WA', -33.340, 116.100),
('6225', 'Buckingham', 'WA', -33.340, 116.100),
('6225', 'Cardiff', 'WA', -33.340, 116.100),
('6225', 'Collie', 'WA', -33.340, 116.100),
('6225', 'Collie Burn', 'WA', -33.340, 116.100),
('6225', 'Harris River', 'WA', -33.340, 116.100),
('6225', 'Lyalls Mill', 'WA', -33.340, 116.100),
('6225', 'Mcalinden', 'WA', -33.340, 116.100),
('6225', 'Muja', 'WA', -33.340, 116.100),
('6225', 'Mumballup', 'WA', -33.340, 116.100),
('6225', 'Mungalup', 'WA', -33.340, 116.100),
('6225', 'Noggerup', 'WA', -33.340, 116.100),
('6225', 'Palmer', 'WA', -33.340, 116.100),
('6225', 'Preston Settlement', 'WA', -33.340, 116.100),
('6225', 'Shotts', 'WA', -33.340, 116.100),
('6225', 'Worsley', 'WA', -33.340, 116.100),
('6225', 'Yourdamung Lake', 'WA', -33.340, 116.100),
('6226', 'Roelands', 'WA', -33.290, 115.830),
('6227', 'Burekup', 'WA', -33.310, 115.810),
('6228', 'Waterloo', 'WA', -33.340, 115.770),
('6229', 'Picton', 'WA', -33.350, 115.690),
('6229', 'Picton East', 'WA', -33.350, 115.690),
('6230', 'Bunbury', 'WA', -33.330, 115.640),
('6230', 'Carey Park', 'WA', -33.330, 115.640),
('6230', 'College Grove', 'WA', -33.330, 115.640),
('6230', 'Dalyellup', 'WA', -33.330, 115.640),
('6230', 'Davenport', 'WA', -33.330, 115.640),
('6230', 'East Bunbury', 'WA', -33.330, 115.640),
('6230', 'Gelorup', 'WA', -33.330, 115.640),
('6230', 'Glen Iris', 'WA', -33.330, 115.640),
('6230', 'Pelican Point', 'WA', -33.330, 115.640),
('6230', 'South Bunbury', 'WA', -33.330, 115.640),
('6230', 'Usher', 'WA', -33.330, 115.640),
('6230', 'Vittoria', 'WA', -33.330, 115.640),
('6230', 'Withers', 'WA', -33.330, 115.640),
('6231', 'Bunbury', 'WA', -33.360, 115.660),
('6232', 'Eaton', 'WA', -33.320, 115.700),
('6232', 'Millbridge', 'WA', -33.320, 115.700),
('6233', 'Australind', 'WA', -33.280, 115.710),
('6233', 'Binningup', 'WA', -33.280, 115.710),
('6233', 'Leschenault', 'WA', -33.280, 115.710),
('6233', 'Parkfield', 'WA', -33.280, 115.710),
('6233', 'Wellesley', 'WA', -33.280, 115.710),
('6236', 'Crooked Brook', 'WA', -33.470, 115.810),
('6236', 'Dardanup', 'WA', -33.470, 115.810),
('6236', 'Dardanup West', 'WA', -33.470, 115.810),
('6236', 'Ferguson', 'WA', -33.470, 115.810),
('6236', 'Henty', 'WA', -33.470, 115.810),
('6236', 'Paradise', 'WA', -33.470, 115.810),
('6236', 'Wellington Forest', 'WA', -33.470, 115.810),
('6236', 'Wellington Mill', 'WA', -33.470, 115.810),
('6237', 'Boyanup', 'WA', -33.480, 115.730),
('6237', 'Elgin', 'WA', -33.480, 115.730),
('6237', 'Gwindinup', 'WA', -33.480, 115.730),
('6237', 'North Boyanup', 'WA', -33.480, 115.730),
('6237', 'Stratham', 'WA', -33.480, 115.730),
('6237', 'The Plains', 'WA', -33.480, 115.730),
('6239', 'Argyle', 'WA', -33.550, 115.770),
('6239', 'Beelerup', 'WA', -33.550, 115.770),
('6239', 'Brookhampton', 'WA', -33.550, 115.770),
('6239', 'Charley Creek', 'WA', -33.550, 115.770),
('6239', 'Donnybrook', 'WA', -33.550, 115.770),
('6239', 'Glen Mervyn', 'WA', -33.550, 115.770),
('6239', 'Paynedale', 'WA', -33.550, 115.770),
('6239', 'Queenwood', 'WA', -33.550, 115.770),
('6239', 'Thomson Brook', 'WA', -33.550, 115.770),
('6239', 'Upper Capel', 'WA', -33.550, 115.770),
('6239', 'Yabberup', 'WA', -33.550, 115.770),
('6240', 'Lowden', 'WA', -33.560, 115.990),
('6243', 'Wilga', 'WA', -33.700, 116.290),
('6243', 'Wilga West', 'WA', -33.700, 116.290),
('6244', 'Boyup Brook', 'WA', -33.830, 116.390),
('6244', 'Chowerup', 'WA', -33.830, 116.390),
('6244', 'Dinninup', 'WA', -33.830, 116.390),
('6244', 'Kulikup', 'WA', -33.830, 116.390),
('6244', 'Mayanup', 'WA', -33.830, 116.390),
('6244', 'Scotts Brook', 'WA', -33.830, 116.390),
('6244', 'Tonebridge', 'WA', -33.830, 116.390),
('6244', 'Trigwell', 'WA', -33.830, 116.390),
('6251', 'Brazier', 'WA', -33.770, 115.830),
('6251', 'Kirup', 'WA', -33.770, 115.830),
('6251', 'Newlands', 'WA', -33.770, 115.830),
('6252', 'Mullalyup', 'WA', -33.750, 115.950),
('6253', 'Balingup', 'WA', -33.790, 115.980),
('6253', 'Grimwade', 'WA', -33.790, 115.980),
('6253', 'Southampton', 'WA', -33.790, 115.980),
('6254', 'Greenbushes', 'WA', -33.850, 116.060),
('6254', 'North Greenbushes', 'WA', -33.850, 116.060),
('6255', 'Benjinup', 'WA', -33.790, 116.300),
('6255', 'Bridgetown', 'WA', -33.790, 116.300),
('6255', 'Catterick', 'WA', -33.790, 116.300),
('6255', 'Hester', 'WA', -33.790, 116.300),
('6255', 'Hester Brook', 'WA', -33.790, 116.300),
('6255', 'Kangaroo Gully', 'WA', -33.790, 116.300),
('6255', 'Winnejup', 'WA', -33.790, 116.300),
('6256', 'Glenlynn', 'WA', -34.010, 116.150),
('6256', 'Kingston', 'WA', -34.010, 116.150),
('6256', 'Maranup', 'WA', -34.010, 116.150),
('6256', 'Sunnyside', 'WA', -34.010, 116.150),
('6256', 'Wandillup', 'WA', -34.010, 116.150),
('6256', 'Yornup', 'WA', -34.010, 116.150),
('6258', 'Balbarrup', 'WA', -34.230, 116.200),
('6258', 'Crowea', 'WA', -34.230, 116.200),
('6258', 'Deanmill', 'WA', -34.230, 116.200),
('6258', 'Diamond Tree', 'WA', -34.230, 116.200),
('6258', 'Dingup', 'WA', -34.230, 116.200),
('6258', 'Dixvale', 'WA', -34.230, 116.200),
('6258', 'Donnelly River', 'WA', -34.230, 116.200),
('6258', 'Glenoran', 'WA', -34.230, 116.200),
('6258', 'Jardee', 'WA', -34.230, 116.200),
('6258', 'Lake Muir', 'WA', -34.230, 116.200),
('6258', 'Linfarne', 'WA', -34.230, 116.200),
('6258', 'Manjimup', 'WA', -34.230, 116.200),
('6258', 'Middlesex', 'WA', -34.230, 116.200),
('6258', 'Mordalup', 'WA', -34.230, 116.200),
('6258', 'Palgarup', 'WA', -34.230, 116.200),
('6258', 'Perup', 'WA', -34.230, 116.200),
('6258', 'Quinninup', 'WA', -34.230, 116.200),
('6258', 'Ringbark', 'WA', -34.230, 116.200),
('6258', 'Smith Brook', 'WA', -34.230, 116.200),
('6258', 'Upper Warren', 'WA', -34.230, 116.200),
('6258', 'Wilgarrup', 'WA', -34.230, 116.200),
('6258', 'Yanmah', 'WA', -34.230, 116.200),
('6260', 'Beedelup', 'WA', -34.350, 115.940),
('6260', 'Biddelia', 'WA', -34.350, 115.940),
('6260', 'Callcup', 'WA', -34.350, 115.940),
('6260', 'Channybearup', 'WA', -34.350, 115.940),
('6260', 'Collins', 'WA', -34.350, 115.940),
('6260', 'Eastbrook', 'WA', -34.350, 115.940),
('6260', 'Lake Jasper', 'WA', -34.350, 115.940),
('6260', 'Peerabeelup', 'WA', -34.350, 115.940),
('6260', 'Pemberton', 'WA', -34.350, 115.940),
('6260', 'Yeagarup', 'WA', -34.350, 115.940),
('6262', 'Boorara Brook', 'WA', -34.690, 116.210),
('6262', 'Meerup', 'WA', -34.690, 116.210),
('6262', 'Northcliffe', 'WA', -34.690, 116.210),
('6262', 'Shannon', 'WA', -34.690, 116.210),
('6262', 'Windy Harbour', 'WA', -34.690, 116.210),
('6271', 'Capel', 'WA', -33.560, 115.560),
('6271', 'Capel River', 'WA', -33.560, 115.560),
('6271', 'Forrest Beach', 'WA', -33.560, 115.560),
('6271', 'Peppermint Grove Beach', 'WA', -33.560, 115.560),
('6271', 'Stirling Estate', 'WA', -33.560, 115.560),
('6275', 'Barrabup', 'WA', -33.840, 115.660),
('6275', 'Carlotta', 'WA', -33.840, 115.660),
('6275', 'Cundinup', 'WA', -33.840, 115.660),
('6275', 'Darradup', 'WA', -33.840, 115.660),
('6275', 'East Nannup', 'WA', -33.840, 115.660),
('6275', 'Jalbarragup', 'WA', -33.840, 115.660),
('6275', 'Jarrahwood', 'WA', -33.840, 115.660),
('6275', 'Nannup', 'WA', -33.840, 115.660),
('6275', 'Scott River East', 'WA', -33.840, 115.660),
('6275', 'Yoganup', 'WA', -33.840, 115.660),
('6280', 'Abba River', 'WA', -33.680, 115.460),
('6280', 'Abbey', 'WA', -33.680, 115.460),
('6280', 'Acton Park', 'WA', -33.680, 115.460),
('6280', 'Ambergate', 'WA', -33.680, 115.460),
('6280', 'Anniebrook', 'WA', -33.680, 115.460),
('6280', 'Boallia', 'WA', -33.680, 115.460),
('6280', 'Bovell', 'WA', -33.680, 115.460),
('6280', 'Broadwater', 'WA', -33.680, 115.460),
('6280', 'Busselton', 'WA', -33.680, 115.460),
('6280', 'Carbunup River', 'WA', -33.680, 115.460),
('6280', 'Chapman Hill', 'WA', -33.680, 115.460),
('6280', 'Geographe', 'WA', -33.680, 115.460),
('6280', 'Hithergreen', 'WA', -33.680, 115.460),
('6280', 'Jindong', 'WA', -33.680, 115.460),
('6280', 'Kalgup', 'WA', -33.680, 115.460),
('6280', 'Kaloorup', 'WA', -33.680, 115.460),
('6280', 'Kealy', 'WA', -33.680, 115.460),
('6280', 'Ludlow', 'WA', -33.680, 115.460),
('6280', 'Marybrook', 'WA', -33.680, 115.460),
('6280', 'Metricup', 'WA', -33.680, 115.460),
('6280', 'North Jindong', 'WA', -33.680, 115.460),
('6280', 'Reinscourt', 'WA', -33.680, 115.460),
('6280', 'Ruabon', 'WA', -33.680, 115.460),
('6280', 'Sabina River', 'WA', -33.680, 115.460),
('6280', 'Siesta Park', 'WA', -33.680, 115.460),
('6280', 'Tutunup', 'WA', -33.680, 115.460),
('6280', 'Vasse', 'WA', -33.680, 115.460),
('6280', 'Walsall', 'WA', -33.680, 115.460),
('6280', 'West Busselton', 'WA', -33.680, 115.460),
('6280', 'Wilyabrup', 'WA', -33.680, 115.460),
('6280', 'Wonnerup', 'WA', -33.680, 115.460),
('6280', 'Yalyalup', 'WA', -33.680, 115.460),
('6280', 'Yelverton', 'WA', -33.680, 115.460),
('6280', 'Yoongarillup', 'WA', -33.680, 115.460),
('6281', 'Dunsborough', 'WA', -33.620, 115.110),
('6281', 'Eagle Bay', 'WA', -33.620, 115.110),
('6281', 'Naturaliste', 'WA', -33.620, 115.110),
('6281', 'Quedjinup', 'WA', -33.620, 115.110),
('6281', 'Quindalup', 'WA', -33.620, 115.110),
('6282', 'Yallingup', 'WA', -33.650, 115.030),
('6282', 'Yallingup Siding', 'WA', -33.650, 115.030),
('6284', 'Baudin', 'WA', -33.870, 115.420),
('6284', 'Cowaramup', 'WA', -33.870, 115.420),
('6284', 'Gracetown', 'WA', -33.870, 115.420),
('6284', 'Treeton', 'WA', -33.870, 115.420),
('6285', 'Bramley', 'WA', -33.910, 115.030),
('6285', 'Burnside', 'WA', -33.910, 115.030),
('6285', 'Gnarabup', 'WA', -33.910, 115.030),
('6285', 'Margaret River', 'WA', -33.910, 115.030),
('6285', 'Osmington', 'WA', -33.910, 115.030),
('6285', 'Prevelly', 'WA', -33.910, 115.030),
('6285', 'Rosa Brook', 'WA', -33.910, 115.030),
('6285', 'Rosa Glen', 'WA', -33.910, 115.030),
('6285', 'Schroeder', 'WA', -33.910, 115.030),
('6286', 'Boranup', 'WA', -34.130, 115.050),
('6286', 'Forest Grove', 'WA', -34.130, 115.050),
('6286', 'Redgate', 'WA', -34.130, 115.050),
('6286', 'Witchcliffe', 'WA', -34.130, 115.050),
('6288', 'Alexandra Bridge', 'WA', -34.160, 115.200),
('6288', 'Courtenay', 'WA', -34.160, 115.200),
('6288', 'Hamelin Bay', 'WA', -34.160, 115.200),
('6288', 'Karridale', 'WA', -34.160, 115.200),
('6288', 'Nillup', 'WA', -34.160, 115.200),
('6288', 'Scott River', 'WA', -34.160, 115.200),
('6288', 'Warner Glen', 'WA', -34.160, 115.200),
('6290', 'Augusta', 'WA', -34.320, 115.160),
('6290', 'Deepdene', 'WA', -34.320, 115.160),
('6290', 'East Augusta', 'WA', -34.320, 115.160),
('6290', 'Kudardup', 'WA', -34.320, 115.160),
('6290', 'Leeuwin', 'WA', -34.320, 115.160),
('6290', 'Molloy Island', 'WA', -34.320, 115.160),
('6302', 'Badgin', 'WA', -31.850, 117.010),
('6302', 'Balladong', 'WA', -31.850, 117.010),
('6302', 'Burges', 'WA', -31.850, 117.010),
('6302', 'Caljie', 'WA', -31.850, 117.010),
('6302', 'Cold Harbour', 'WA', -31.850, 117.010),
('6302', 'Daliak', 'WA', -31.850, 117.010),
('6302', 'Flint', 'WA', -31.850, 117.010),
('6302', 'Flynn', 'WA', -31.850, 117.010),
('6302', 'Gilgering', 'WA', -31.850, 117.010),
('6302', 'Greenhills', 'WA', -31.850, 117.010),
('6302', 'Gwambygine', 'WA', -31.850, 117.010),
('6302', 'Inkpen', 'WA', -31.850, 117.010),
('6302', 'Kauring', 'WA', -31.850, 117.010),
('6302', 'Malebelling', 'WA', -31.850, 117.010),
('6302', 'Mount Hardey', 'WA', -31.850, 117.010),
('6302', 'Mount Observation', 'WA', -31.850, 117.010),
('6302', 'Narraloggan', 'WA', -31.850, 117.010),
('6302', 'Quellington', 'WA', -31.850, 117.010),
('6302', 'St Ronans', 'WA', -31.850, 117.010),
('6302', 'Talbot', 'WA', -31.850, 117.010),
('6302', 'Talbot West', 'WA', -31.850, 117.010),
('6302', 'Wilberforce', 'WA', -31.850, 117.010),
('6302', 'York', 'WA', -31.850, 117.010),
('6304', 'Bally Bally', 'WA', -32.180, 117.120),
('6304', 'Beverley', 'WA', -32.180, 117.120),
('6304', 'Dale', 'WA', -32.180, 117.120),
('6304', 'East Beverley', 'WA', -32.180, 117.120),
('6304', 'Kokeby', 'WA', -32.180, 117.120),
('6304', 'Morbinning', 'WA', -32.180, 117.120),
('6304', 'Westdale', 'WA', -32.180, 117.120),
('6306', 'Aldersyde', 'WA', -32.370, 117.310),
('6306', 'Brookton', 'WA', -32.370, 117.310),
('6306', 'Bulyee', 'WA', -32.370, 117.310),
('6306', 'Jelcobine', 'WA', -32.370, 117.310),
('6306', 'Kweda', 'WA', -32.370, 117.310),
('6308', 'Codjatotine', 'WA', -32.680, 116.820),
('6308', 'Dwarda', 'WA', -32.680, 116.820),
('6308', 'East Pingelly', 'WA', -32.680, 116.820),
('6308', 'Gillimanning', 'WA', -32.680, 116.820),
('6308', 'Hastings', 'WA', -32.680, 116.820),
('6308', 'Pingelly', 'WA', -32.680, 116.820),
('6308', 'Pumphreys Bridge', 'WA', -32.680, 116.820),
('6308', 'Springs', 'WA', -32.680, 116.820),
('6308', 'Wandering', 'WA', -32.680, 116.820),
('6308', 'West Pingelly', 'WA', -32.680, 116.820),
('6309', 'East Popanyinning', 'WA', -32.630, 117.260),
('6309', 'Popanyinning', 'WA', -32.630, 117.260),
('6309', 'Stratherne', 'WA', -32.630, 117.260),
('6309', 'West Popanyinning', 'WA', -32.630, 117.260),
('6311', 'Commodine', 'WA', -32.770, 117.310),
('6311', 'Contine', 'WA', -32.770, 117.310),
('6311', 'Cuballing', 'WA', -32.770, 117.310),
('6311', 'Dryandra', 'WA', -32.770, 117.310),
('6311', 'Lol Gray', 'WA', -32.770, 117.310),
('6311', 'Townsendale', 'WA', -32.770, 117.310),
('6311', 'Wardering', 'WA', -32.770, 117.310),
('6311', 'Yornaning', 'WA', -32.770, 117.310),
('6312', 'Boundain', 'WA', -32.950, 117.360),
('6312', 'Dumberning', 'WA', -32.950, 117.360),
('6312', 'Hillside', 'WA', -32.950, 117.360),
('6312', 'Minigin', 'WA', -32.950, 117.360),
('6312', 'Narrogin', 'WA', -32.950, 117.360),
('6312', 'Narrogin Valley', 'WA', -32.950, 117.360),
('6312', 'Nomans Lake', 'WA', -32.950, 117.360),
('6312', 'Toolibin', 'WA', -32.950, 117.360),
('6312', 'Yilliminning', 'WA', -32.950, 117.360),
('6313', 'Highbury', 'WA', -33.060, 117.150),
('6315', 'Arthur River', 'WA', -33.340, 117.030),
('6315', 'Ballaying', 'WA', -33.340, 117.030),
('6315', 'Cancanning', 'WA', -33.340, 117.030),
('6315', 'Collanilling', 'WA', -33.340, 117.030),
('6315', 'Dongolocking', 'WA', -33.340, 117.030),
('6315', 'Gundaring', 'WA', -33.340, 117.030),
('6315', 'Jaloran', 'WA', -33.340, 117.030),
('6315', 'Lime Lake', 'WA', -33.340, 117.030),
('6315', 'Minding', 'WA', -33.340, 117.030),
('6315', 'Piesseville', 'WA', -33.340, 117.030),
('6315', 'Wagin', 'WA', -33.340, 117.030),
('6315', 'Wedgecarrup', 'WA', -33.340, 117.030),
('6316', 'Boyerine', 'WA', -33.500, 117.410),
('6316', 'Cartmeticup', 'WA', -33.500, 117.410),
('6316', 'Glencoe', 'WA', -33.500, 117.410),
('6316', 'Kenmare', 'WA', -33.500, 117.410),
('6316', 'Westwood', 'WA', -33.500, 117.410),
('6316', 'Woodanilling', 'WA', -33.500, 117.410),
('6317', 'Badgebup', 'WA', -33.630, 117.900),
('6317', 'Bullock Hills', 'WA', -33.630, 117.900),
('6317', 'Carrolup', 'WA', -33.630, 117.900),
('6317', 'Coblinine', 'WA', -33.630, 117.900),
('6317', 'Coyrecup', 'WA', -33.630, 117.900),
('6317', 'Datatine', 'WA', -33.630, 117.900),
('6317', 'Ewlyamartup', 'WA', -33.630, 117.900),
('6317', 'Katanning', 'WA', -33.630, 117.900),
('6317', 'Marracoonda', 'WA', -33.630, 117.900),
('6317', 'Moojebing', 'WA', -33.630, 117.900),
('6317', 'Murdong', 'WA', -33.630, 117.900),
('6317', 'Pinwernying', 'WA', -33.630, 117.900),
('6317', 'South Datatine', 'WA', -33.630, 117.900),
('6317', 'South Glencoe', 'WA', -33.630, 117.900),
('6318', 'Broomehill', 'WA', -33.850, 117.640),
('6318', 'Broomehill East', 'WA', -33.850, 117.640),
('6318', 'Broomehill Village', 'WA', -33.850, 117.640),
('6318', 'Broomehill West', 'WA', -33.850, 117.640),
('6320', 'Bobalong', 'WA', -34.010, 117.560),
('6320', 'Borderdale', 'WA', -34.010, 117.560),
('6320', 'Dartnall', 'WA', -34.010, 117.560),
('6320', 'Lake Toolbrunup', 'WA', -34.010, 117.560),
('6320', 'Moonies Hill', 'WA', -34.010, 117.560),
('6320', 'Tambellup', 'WA', -34.010, 117.560),
('6320', 'Wansbrough', 'WA', -34.010, 117.560),
('6321', 'Cranbrook', 'WA', -34.300, 117.560),
('6322', 'Tenterden', 'WA', -34.360, 117.560),
('6323', 'Kendenup', 'WA', -34.500, 117.590),
('6324', 'Denbarker', 'WA', -34.720, 117.510),
('6324', 'Forest Hill', 'WA', -34.720, 117.510),
('6324', 'Mount Barker', 'WA', -34.720, 117.510),
('6324', 'Perillup', 'WA', -34.720, 117.510),
('6324', 'Porongurup', 'WA', -34.720, 117.510),
('6324', 'South Stirling', 'WA', -34.720, 117.510),
('6324', 'Takalarup', 'WA', -34.720, 117.510),
('6324', 'Woogenellup', 'WA', -34.720, 117.510),
('6326', 'Narrikup', 'WA', -34.770, 117.700),
('6327', 'Redmond', 'WA', -34.890, 117.690),
('6327', 'Redmond West', 'WA', -34.890, 117.690),
('6328', 'Cheynes', 'WA', -34.840, 118.340),
('6328', 'Gnowellen', 'WA', -34.840, 118.340),
('6328', 'Green Range', 'WA', -34.840, 118.340),
('6328', 'Kojaneerup South', 'WA', -34.840, 118.340),
('6328', 'Manypeaks', 'WA', -34.840, 118.340),
('6328', 'Mettler', 'WA', -34.840, 118.340),
('6328', 'Palmdale', 'WA', -34.840, 118.340),
('6328', 'Wellstead', 'WA', -34.840, 118.340),
('6330', 'Albany', 'WA', -35.020, 117.880),
('6330', 'Bayonet Head', 'WA', -35.020, 117.880),
('6330', 'Big Grove', 'WA', -35.020, 117.880),
('6330', 'Bornholm', 'WA', -35.020, 117.880),
('6330', 'Centennial Park', 'WA', -35.020, 117.880),
('6330', 'Collingwood Heights', 'WA', -35.020, 117.880),
('6330', 'Collingwood Park', 'WA', -35.020, 117.880),
('6330', 'Cuthbert', 'WA', -35.020, 117.880),
('6330', 'Drome', 'WA', -35.020, 117.880),
('6330', 'Elleker', 'WA', -35.020, 117.880),
('6330', 'Emu Point', 'WA', -35.020, 117.880),
('6330', 'Frenchman Bay', 'WA', -35.020, 117.880),
('6330', 'Gledhow', 'WA', -35.020, 117.880),
('6330', 'Goode Beach', 'WA', -35.020, 117.880),
('6330', 'Green Valley', 'WA', -35.020, 117.880),
('6330', 'Kalgan', 'WA', -35.020, 117.880),
('6330', 'King River', 'WA', -35.020, 117.880),
('6330', 'Kronkup', 'WA', -35.020, 117.880),
('6330', 'Lange', 'WA', -35.020, 117.880),
('6330', 'Little Grove', 'WA', -35.020, 117.880),
('6330', 'Lockyer', 'WA', -35.020, 117.880),
('6330', 'Lower King', 'WA', -35.020, 117.880),
('6330', 'Lowlands', 'WA', -35.020, 117.880),
('6330', 'Marbelup', 'WA', -35.020, 117.880),
('6330', 'Mckail', 'WA', -35.020, 117.880),
('6330', 'Middleton Beach', 'WA', -35.020, 117.880),
('6330', 'Millbrook', 'WA', -35.020, 117.880),
('6330', 'Milpara', 'WA', -35.020, 117.880),
('6330', 'Mira Mar', 'WA', -35.020, 117.880),
('6330', 'Mount Clarence', 'WA', -35.020, 117.880),
('6330', 'Mount Elphinstone', 'WA', -35.020, 117.880),
('6330', 'Mount Melville', 'WA', -35.020, 117.880),
('6330', 'Nanarup', 'WA', -35.020, 117.880),
('6330', 'Napier', 'WA', -35.020, 117.880),
('6330', 'Nullaki', 'WA', -35.020, 117.880),
('6330', 'Orana', 'WA', -35.020, 117.880),
('6330', 'Port Albany', 'WA', -35.020, 117.880),
('6330', 'Robinson', 'WA', -35.020, 117.880),
('6330', 'Sandpatch', 'WA', -35.020, 117.880),
('6330', 'Seppings', 'WA', -35.020, 117.880),
('6330', 'Spencer Park', 'WA', -35.020, 117.880),
('6330', 'Torbay', 'WA', -35.020, 117.880),
('6330', 'Torndirrup', 'WA', -35.020, 117.880),
('6330', 'Vancouver Peninsula', 'WA', -35.020, 117.880),
('6330', 'Walmsley', 'WA', -35.020, 117.880),
('6330', 'Warrenup', 'WA', -35.020, 117.880),
('6330', 'West Cape Howe', 'WA', -35.020, 117.880),
('6330', 'Willyung', 'WA', -35.020, 117.880),
('6330', 'Yakamia', 'WA', -35.020, 117.880),
('6330', 'Youngs Siding', 'WA', -35.020, 117.880),
('6331', 'Albany DC', 'WA', 0.000, 0.000),
('6332', 'Albany Po', 'WA', -32.050, 115.970),
('6333', 'Bow Bridge', 'WA', -34.970, 116.950),
('6333', 'Denmark', 'WA', -34.970, 116.950),
('6333', 'Hay', 'WA', -34.970, 116.950),
('6333', 'Hazelvale', 'WA', -34.970, 116.950),
('6333', 'Kentdale', 'WA', -34.970, 116.950),
('6333', 'Kordabup', 'WA', -34.970, 116.950),
('6333', 'Mount Lindesay', 'WA', -34.970, 116.950),
('6333', 'Mount Romance', 'WA', -34.970, 116.950),
('6333', 'Nornalup', 'WA', -34.970, 116.950),
('6333', 'Ocean Beach', 'WA', -34.970, 116.950),
('6333', 'Parryville', 'WA', -34.970, 116.950),
('6333', 'Peaceful Bay', 'WA', -34.970, 116.950),
('6333', 'Scotsdale', 'WA', -34.970, 116.950),
('6333', 'Shadforth', 'WA', -34.970, 116.950),
('6333', 'Tingledale', 'WA', -34.970, 116.950),
('6333', 'Trent', 'WA', -34.970, 116.950),
('6333', 'William Bay', 'WA', -34.970, 116.950),
('6335', 'Gnowangerup', 'WA', -33.940, 118.010),
('6335', 'Jackitup', 'WA', -33.940, 118.010),
('6335', 'Kebaringup', 'WA', -33.940, 118.010),
('6335', 'Pallinup', 'WA', -33.940, 118.010),
('6336', 'Cowalellup', 'WA', -34.100, 118.550),
('6336', 'Mills Lake', 'WA', -34.100, 118.550),
('6336', 'Mindarabin', 'WA', -34.100, 118.550),
('6336', 'Needilup', 'WA', -34.100, 118.550),
('6336', 'Ongerup', 'WA', -34.100, 118.550),
('6336', 'Toompup', 'WA', -34.100, 118.550),
('6337', 'Fitzgerald', 'WA', -33.750, 119.460),
('6337', 'Gairdner', 'WA', -33.750, 119.460),
('6337', 'Jacup', 'WA', -33.750, 119.460),
('6337', 'Jerramungup', 'WA', -33.750, 119.460),
('6337', 'West Fitzgerald', 'WA', -33.750, 119.460),
('6338', 'Amelup', 'WA', -34.240, 118.220),
('6338', 'Borden', 'WA', -34.240, 118.220),
('6338', 'Boxwood Hill', 'WA', -34.240, 118.220),
('6338', 'Bremer Bay', 'WA', -34.240, 118.220),
('6338', 'Magitup', 'WA', -34.240, 118.220),
('6338', 'Monjebup', 'WA', -34.240, 118.220),
('6338', 'Nalyerlup', 'WA', -34.240, 118.220),
('6338', 'North Stirlings', 'WA', -34.240, 118.220),
('6338', 'Stirling Range National Park', 'WA', -34.240, 118.220),
('6341', 'Nyabing', 'WA', -33.540, 118.150),
('6343', 'Pingrup', 'WA', -33.650, 118.520),
('6346', 'Fitzgerald River National Park', 'WA', -34.070, 119.590),
('6346', 'Jerdacuttup', 'WA', -34.070, 119.590),
('6346', 'Ravensthorpe', 'WA', -34.070, 119.590),
('6346', 'West River', 'WA', -34.070, 119.590),
('6348', 'Hopetoun', 'WA', -33.870, 120.160),
('6350', 'Dumbleyung', 'WA', -33.310, 117.740),
('6350', 'Nairibin', 'WA', -33.310, 117.740),
('6350', 'Nippering', 'WA', -33.310, 117.740),
('6351', 'Moulyinning', 'WA', -33.190, 117.920),
('6351', 'North Moulyinning', 'WA', -33.190, 117.920),
('6352', 'Kukerin', 'WA', -33.190, 118.080),
('6352', 'Merilup', 'WA', -33.190, 118.080),
('6352', 'North Kukerin', 'WA', -33.190, 118.080),
('6352', 'South Kukerin', 'WA', -33.190, 118.080),
('6353', 'Beenong', 'WA', -33.020, 118.660),
('6353', 'Buniche', 'WA', -33.020, 118.660),
('6353', 'Kuender', 'WA', -33.020, 118.660),
('6353', 'Lake Grace', 'WA', -33.020, 118.660),
('6353', 'Mallee Hill', 'WA', -33.020, 118.660),
('6353', 'Neendaling', 'WA', -33.020, 118.660),
('6353', 'North Burngup', 'WA', -33.020, 118.660),
('6353', 'North Lake Grace', 'WA', -33.020, 118.660),
('6353', 'South Lake Grace', 'WA', -33.020, 118.660),
('6353', 'Tarin Rock', 'WA', -33.020, 118.660),
('6355', 'Dunn Rock', 'WA', -33.430, 119.520),
('6355', 'East Newdegate', 'WA', -33.430, 119.520),
('6355', 'Holt Rock', 'WA', -33.430, 119.520),
('6355', 'Lake Biddy', 'WA', -33.430, 119.520),
('6355', 'Lake Camm', 'WA', -33.430, 119.520),
('6355', 'Little Italy', 'WA', -33.430, 119.520),
('6355', 'Magenta', 'WA', -33.430, 119.520),
('6355', 'Mount Sheridan', 'WA', -33.430, 119.520),
('6355', 'Newdegate', 'WA', -33.430, 119.520),
('6355', 'South Newdegate', 'WA', -33.430, 119.520),
('6355', 'Varley', 'WA', -33.430, 119.520),
('6356', 'Hatter Hill', 'WA', -33.090, 119.660),
('6356', 'Lake King', 'WA', -33.090, 119.660),
('6356', 'Mount Madden', 'WA', -33.090, 119.660),
('6357', 'Pingaring', 'WA', -32.760, 118.630),
('6358', 'Karlgarin', 'WA', -32.500, 118.710),
('6359', 'Forrestania', 'WA', -32.440, 119.780),
('6359', 'Hyden', 'WA', -32.440, 119.780),
('6361', 'Harrismith', 'WA', -32.930, 117.880),
('6361', 'Tincurrin', 'WA', -32.930, 117.880),
('6363', 'Dudinin', 'WA', -32.870, 117.900),
('6363', 'Walyurin', 'WA', -32.870, 117.900),
('6365', 'Jilakin', 'WA', -32.680, 118.440),
('6365', 'Jitarning', 'WA', -32.680, 118.440),
('6365', 'Kulin', 'WA', -32.680, 118.440),
('6365', 'Kulin West', 'WA', -32.680, 118.440),
('6367', 'Kondinin', 'WA', -32.500, 118.270),
('6368', 'South Kumminin', 'WA', -32.210, 118.330),
('6369', 'Mount Walker', 'WA', -32.070, 118.760),
('6369', 'Narembeen', 'WA', -32.070, 118.760),
('6369', 'Wadderin', 'WA', -32.070, 118.760),
('6369', 'West Holleton', 'WA', -32.070, 118.760),
('6369', 'Woolocutty', 'WA', -32.070, 118.760),
('6370', 'East Wickepin', 'WA', -32.770, 117.710),
('6370', 'Kirk Rock', 'WA', -32.770, 117.710),
('6370', 'Malyalling', 'WA', -32.770, 117.710),
('6370', 'Wickepin', 'WA', -32.770, 117.710),
('6370', 'Wogolin', 'WA', -32.770, 117.710),
('6372', 'Yealering', 'WA', -32.600, 117.680),
('6373', 'Bullaring', 'WA', -32.500, 117.740),
('6375', 'Adamsvale', 'WA', -32.250, 117.720),
('6375', 'Bilbarin', 'WA', -32.250, 117.720),
('6375', 'Corrigin', 'WA', -32.250, 117.720),
('6375', 'Gorge Rock', 'WA', -32.250, 117.720),
('6375', 'Kunjin', 'WA', -32.250, 117.720),
('6375', 'Kurrenkutten', 'WA', -32.250, 117.720),
('6383', 'Badjaling', 'WA', -31.900, 117.440),
('6383', 'Balkuling', 'WA', -31.900, 117.440),
('6383', 'Cubbine', 'WA', -31.900, 117.440),
('6383', 'Dangin', 'WA', -31.900, 117.440),
('6383', 'Doodenanning', 'WA', -31.900, 117.440),
('6383', 'Dulbelling', 'WA', -31.900, 117.440),
('6383', 'Mount Stirling', 'WA', -31.900, 117.440),
('6383', 'Quairading', 'WA', -31.900, 117.440),
('6383', 'South Quairading', 'WA', -31.900, 117.440),
('6383', 'Wamenusking', 'WA', -31.900, 117.440),
('6383', 'Yoting', 'WA', -31.900, 117.440),
('6384', 'Pantapin', 'WA', -31.950, 117.660),
('6385', 'Kwolyin', 'WA', -31.930, 117.760),
('6386', 'Shackleton', 'WA', -31.930, 117.840),
('6390', 'Bannister', 'WA', -32.670, 116.520),
('6390', 'Boddington', 'WA', -32.670, 116.520),
('6390', 'Crossman', 'WA', -32.670, 116.520),
('6390', 'Lower Hotham', 'WA', -32.670, 116.520),
('6390', 'Marradong', 'WA', -32.670, 116.520),
('6390', 'Mount Cooke', 'WA', -32.670, 116.520),
('6390', 'Mount Wells', 'WA', -32.670, 116.520),
('6390', 'North Bannister', 'WA', -32.670, 116.520),
('6390', 'Ranford', 'WA', -32.670, 116.520),
('6390', 'Upper Murray', 'WA', -32.670, 116.520),
('6390', 'Wuraming', 'WA', -32.670, 116.520),
('6391', 'Quindanning', 'WA', -33.040, 116.570),
('6391', 'Williams', 'WA', -33.040, 116.570),
('6392', 'Bokal', 'WA', -33.470, 116.910),
('6392', 'Dardadine', 'WA', -33.470, 116.910),
('6392', 'Darkan', 'WA', -33.470, 116.910),
('6392', 'Meeking', 'WA', -33.470, 116.910),
('6393', 'Duranillin', 'WA', -33.510, 116.680),
('6393', 'Moodiarrup', 'WA', -33.510, 116.680),
('6394', 'Beaufort River', 'WA', -33.570, 117.030),
('6394', 'Boilup', 'WA', -33.570, 117.030),
('6394', 'Boscabel', 'WA', -33.570, 117.030),
('6394', 'Changerup', 'WA', -33.570, 117.030),
('6394', 'Mokup', 'WA', -33.570, 117.030),
('6394', 'Muradup', 'WA', -33.570, 117.030),
('6394', 'Orchid Valley', 'WA', -33.570, 117.030),
('6394', 'Qualeup', 'WA', -33.570, 117.030),
('6395', 'Cherry Tree Pool', 'WA', -33.700, 117.230),
('6395', 'Jingalup', 'WA', -33.700, 117.230),
('6395', 'Kojonup', 'WA', -33.700, 117.230),
('6395', 'Lumeah', 'WA', -33.700, 117.230),
('6395', 'Mobrup', 'WA', -33.700, 117.230),
('6395', 'Ryansbrook', 'WA', -33.700, 117.230),
('6396', 'Frankland River', 'WA', -34.310, 116.980),
('6397', 'Rocky Gully', 'WA', -34.480, 117.100),
('6398', 'Broke', 'WA', -34.920, 116.470),
('6398', 'North Walpole', 'WA', -34.920, 116.470),
('6398', 'Walpole', 'WA', -34.920, 116.470),
('6401', 'Buckland', 'WA', -31.540, 116.610),
('6401', 'Burlong', 'WA', -31.540, 116.610),
('6401', 'Cunjardine', 'WA', -31.540, 116.610),
('6401', 'Irishtown', 'WA', -31.540, 116.610),
('6401', 'Jennacubbine', 'WA', -31.540, 116.610),
('6401', 'Jennapullin', 'WA', -31.540, 116.610),
('6401', 'Malabaine', 'WA', -31.540, 116.610),
('6401', 'Meenaar', 'WA', -31.540, 116.610),
('6401', 'Mokine', 'WA', -31.540, 116.610),
('6401', 'Muluckine', 'WA', -31.540, 116.610),
('6401', 'Mumberkine', 'WA', -31.540, 116.610),
('6401', 'Muresk', 'WA', -31.540, 116.610),
('6401', 'Northam', 'WA', -31.540, 116.610),
('6401', 'Rossmore', 'WA', -31.540, 116.610),
('6401', 'Southern Brook', 'WA', -31.540, 116.610),
('6401', 'Spencers Brook', 'WA', -31.540, 116.610),
('6401', 'Throssell', 'WA', -31.540, 116.610),
('6401', 'Wongamine', 'WA', -31.540, 116.610),
('6403', 'Grass Valley', 'WA', -31.600, 116.810),
('6405', 'Greenwoods Valley', 'WA', -31.510, 116.940),
('6405', 'Meckering', 'WA', -31.510, 116.940),
('6405', 'Quelagetting', 'WA', -31.510, 116.940),
('6405', 'Warding East', 'WA', -31.510, 116.940),
('6407', 'Cunderdin', 'WA', -31.650, 117.230),
('6407', 'Waeel', 'WA', -31.650, 117.230),
('6407', 'Watercarrin', 'WA', -31.650, 117.230),
('6407', 'Wyola West', 'WA', -31.650, 117.230),
('6407', 'Youndegin', 'WA', -31.650, 117.230),
('6409', 'North Tammin', 'WA', -31.520, 117.430),
('6409', 'South Tammin', 'WA', -31.520, 117.430),
('6409', 'Tammin', 'WA', -31.520, 117.430),
('6410', 'Daadenning Creek', 'WA', -31.620, 117.590),
('6410', 'Kellerberrin', 'WA', -31.620, 117.590),
('6410', 'Mount Caroline', 'WA', -31.620, 117.590),
('6410', 'North Kellerberrin', 'WA', -31.620, 117.590),
('6411', 'Doodlakine', 'WA', -31.610, 117.880),
('6411', 'South Doodlakine', 'WA', -31.610, 117.880),
('6412', 'Baandee', 'WA', -31.580, 117.990),
('6412', 'North Baandee', 'WA', -31.580, 117.990),
('6413', 'Hines Hill', 'WA', -31.530, 118.080),
('6414', 'Nangeenan', 'WA', -31.470, 118.150),
('6415', 'Goomarin', 'WA', -31.240, 118.410),
('6415', 'Korbel', 'WA', -31.240, 118.410),
('6415', 'Merredin', 'WA', -31.240, 118.410),
('6415', 'Nokaning', 'WA', -31.240, 118.410),
('6415', 'Norpa', 'WA', -31.240, 118.410),
('6415', 'Tandegin', 'WA', -31.240, 118.410),
('6418', 'Bruce Rock', 'WA', -31.880, 118.150),
('6419', 'Ardath', 'WA', -32.030, 118.100),
('6420', 'Cramphorne', 'WA', -31.800, 118.560),
('6420', 'Muntadgin', 'WA', -31.800, 118.560),
('6421', 'Burracoppin', 'WA', -31.400, 118.480),
('6421', 'South Burracoppin', 'WA', -31.400, 118.480),
('6421', 'Warralakin', 'WA', -31.400, 118.480),
('6422', 'Walgoolan', 'WA', -31.370, 118.600),
('6423', 'Boodarockin', 'WA', -31.000, 118.860),
('6423', 'Carrabin', 'WA', -31.000, 118.860),
('6423', 'Warrachuppin', 'WA', -31.000, 118.860),
('6423', 'Westonia', 'WA', -31.000, 118.860),
('6424', 'Bodallin', 'WA', -31.370, 118.850),
('6424', 'North Bodallin', 'WA', -31.370, 118.850),
('6424', 'South Bodallin', 'WA', -31.370, 118.850),
('6425', 'Dulyalbin', 'WA', -31.560, 119.150),
('6425', 'Moorine Rock', 'WA', -31.560, 119.150),
('6426', 'Corinthia', 'WA', -31.150, 119.150),
('6426', 'Ghooli', 'WA', -31.150, 119.150),
('6426', 'Holleton', 'WA', -31.150, 119.150),
('6426', 'Marvel Loch', 'WA', -31.150, 119.150),
('6426', 'Mount Hampton', 'WA', -31.150, 119.150),
('6426', 'Mount Holland', 'WA', -31.150, 119.150),
('6426', 'Mount Jackson', 'WA', -31.150, 119.150),
('6426', 'Mount Palmer', 'WA', -31.150, 119.150),
('6426', 'Parker Range', 'WA', -31.150, 119.150),
('6426', 'Skeleton Rock', 'WA', -31.150, 119.150),
('6426', 'South Yilgarn', 'WA', -31.150, 119.150),
('6426', 'Southern Cross', 'WA', -31.150, 119.150),
('6426', 'Turkey Hill', 'WA', -31.150, 119.150),
('6426', 'Yellowdine', 'WA', -31.150, 119.150),
('6427', 'Koolyanobbing', 'WA', -31.110, 119.400),
('6428', 'Babakin', 'WA', -32.130, 118.020),
('6429', 'Boorabbin', 'WA', -30.950, 120.160),
('6429', 'Bullabulling', 'WA', -30.950, 120.160),
('6429', 'Coolgardie', 'WA', -30.950, 120.160),
('6429', 'Karramindie', 'WA', -30.950, 120.160),
('6429', 'Londonderry', 'WA', -30.950, 120.160),
('6429', 'Mount Burges', 'WA', -30.950, 120.160),
('6429', 'Victoria Rock', 'WA', -30.950, 120.160),
('6429', 'Wallaroo', 'WA', -30.950, 120.160),
('6430', 'Binduli', 'WA', -30.810, 121.380),
('6430', 'Broadwood', 'WA', -30.810, 121.380),
('6430', 'Hannans', 'WA', -30.810, 121.380),
('6430', 'Kalgoorlie', 'WA', -30.810, 121.380),
('6430', 'Karlkurla', 'WA', -30.810, 121.380),
('6430', 'Lamington', 'WA', -30.810, 121.380),
('6430', 'Mullingar', 'WA', -30.810, 121.380),
('6430', 'Piccadilly', 'WA', -30.810, 121.380),
('6430', 'Somerville', 'WA', -30.810, 121.380),
('6430', 'South Kalgoorlie', 'WA', -30.810, 121.380),
('6430', 'West Kalgoorlie', 'WA', -30.810, 121.380),
('6430', 'West Lamington', 'WA', -30.810, 121.380),
('6430', 'Williamstown', 'WA', -30.810, 121.380),
('6430', 'Yilkari', 'WA', -30.810, 121.380),
('6431', 'Boorara', 'WA', -30.800, 121.640),
('6431', 'Brown Hill', 'WA', -30.800, 121.640),
('6431', 'Bulong', 'WA', -30.800, 121.640),
('6431', 'Emu Flat', 'WA', -30.800, 121.640),
('6431', 'Feysville', 'WA', -30.800, 121.640),
('6431', 'Kanowna', 'WA', -30.800, 121.640),
('6431', 'Kookynie', 'WA', -30.800, 121.640),
('6431', 'Kurnalpi', 'WA', -30.800, 121.640),
('6431', 'Lakewood', 'WA', -30.800, 121.640),
('6431', 'Ora Banda', 'WA', -30.800, 121.640),
('6431', 'Plumridge Lakes', 'WA', -30.800, 121.640),
('6431', 'Trafalgar', 'WA', -30.800, 121.640),
('6431', 'Warburton', 'WA', -30.800, 121.640),
('6432', 'Boulder', 'WA', -30.780, 121.490),
('6432', 'Fimiston', 'WA', -30.780, 121.490),
('6432', 'South Boulder', 'WA', -30.780, 121.490),
('6432', 'Victory Heights', 'WA', -30.780, 121.490),
('6433', 'Hannans Po', 'WA', -31.880, 115.920),
('6433', 'Kalgoorlie Po', 'WA', -31.880, 115.920),
('6433', 'Kalgoorlie Po', 'WA', -31.880, 115.920),
('6434', 'Cundeelee', 'WA', -30.500, 123.120),
('6434', 'Forrest', 'WA', -30.500, 123.120),
('6434', 'Parkeston', 'WA', -30.500, 123.120),
('6434', 'Rawlinna', 'WA', -30.500, 123.120),
('6434', 'Zanthus', 'WA', -30.500, 123.120),
('6436', 'Menzies', 'WA', -29.690, 121.030),
('6436', 'Ularring', 'WA', -29.690, 121.030),
('6437', 'Leinster', 'WA', -27.450, 120.550),
('6437', 'Sir Samuel', 'WA', -27.450, 120.550),
('6438', 'Lake Darlot', 'WA', -27.790, 121.580),
('6438', 'Leonora', 'WA', -27.790, 121.580),
('6440', 'Bandya', 'WA', -27.910, 122.330),
('6440', 'Beadell', 'WA', -27.910, 122.330),
('6440', 'Cosmo Newbery', 'WA', -27.910, 122.330),
('6440', 'Lake Wells', 'WA', -27.910, 122.330),
('6440', 'Laverton', 'WA', -27.910, 122.330),
('6440', 'Neale', 'WA', -27.910, 122.330),
('6442', 'Kambalda East', 'WA', -31.210, 121.620),
('6442', 'Kambalda West', 'WA', -31.210, 121.620),
('6443', 'Balladonia', 'WA', -32.470, 123.830),
('6443', 'Caiguna', 'WA', -32.470, 123.830),
('6443', 'Cocklebiddy', 'WA', -32.470, 123.830),
('6443', 'Dundas', 'WA', -32.470, 123.830),
('6443', 'Eucla', 'WA', -32.470, 123.830),
('6443', 'Fraser Range', 'WA', -32.470, 123.830),
('6443', 'Higginsville', 'WA', -32.470, 123.830),
('6443', 'Madura', 'WA', -32.470, 123.830),
('6443', 'Mundrabilla', 'WA', -32.470, 123.830),
('6443', 'Norseman', 'WA', -32.470, 123.830),
('6443', 'Widgiemooltha', 'WA', -32.470, 123.830),
('6445', 'North Cascade', 'WA', -32.910, 121.100),
('6445', 'Salmon Gums', 'WA', -32.910, 121.100),
('6446', 'Grass Patch', 'WA', -33.230, 121.720),
('6447', 'Lort River', 'WA', -33.350, 121.390),
('6447', 'Mount Ney', 'WA', -33.350, 121.390),
('6447', 'Scaddan', 'WA', -33.350, 121.390),
('6447', 'Wittenoom Hills', 'WA', -33.350, 121.390),
('6448', 'Gibson', 'WA', -33.640, 121.810),
('6450', 'Bandy Creek', 'WA', -33.820, 121.940),
('6450', 'Beaumont', 'WA', -33.820, 121.940),
('6450', 'Boyatup', 'WA', -33.820, 121.940),
('6450', 'Cape Le Grand', 'WA', -33.820, 121.940),
('6450', 'Cascade', 'WA', -33.820, 121.940),
('6450', 'Castletown', 'WA', -33.820, 121.940),
('6450', 'Chadwick', 'WA', -33.820, 121.940),
('6450', 'Condingup', 'WA', -33.820, 121.940),
('6450', 'Coomalbidgup', 'WA', -33.820, 121.940),
('6450', 'Dalyup', 'WA', -33.820, 121.940),
('6450', 'East Munglinup', 'WA', -33.820, 121.940),
('6450', 'Esperance', 'WA', -33.820, 121.940),
('6450', 'Howick', 'WA', -33.820, 121.940),
('6450', 'Merivale', 'WA', -33.820, 121.940),
('6450', 'Monjingup', 'WA', -33.820, 121.940),
('6450', 'Munglinup', 'WA', -33.820, 121.940),
('6450', 'Myrup', 'WA', -33.820, 121.940),
('6450', 'Neridup', 'WA', -33.820, 121.940),
('6450', 'Nulsen', 'WA', -33.820, 121.940),
('6450', 'Pink Lake', 'WA', -33.820, 121.940),
('6450', 'Sinclair', 'WA', -33.820, 121.940),
('6450', 'West Beach', 'WA', -33.820, 121.940),
('6450', 'Windabout', 'WA', -33.820, 121.940),
('6452', 'Buraminya', 'WA', -32.920, 122.900),
('6452', 'Cape Arid', 'WA', -32.920, 122.900),
('6452', 'Israelite Bay', 'WA', -32.920, 122.900),
('6460', 'Goomalling', 'WA', -31.300, 116.830),
('6460', 'Hulongine', 'WA', -31.300, 116.830),
('6460', 'Karranadgin', 'WA', -31.300, 116.830),
('6460', 'Ucarty West', 'WA', -31.300, 116.830),
('6460', 'Walyormouring', 'WA', -31.300, 116.830),
('6461', 'Dowerin', 'WA', -31.200, 117.030),
('6461', 'Koomberkine', 'WA', -31.200, 117.030),
('6462', 'Hindmarsh', 'WA', -31.270, 117.230),
('6462', 'Minnivale', 'WA', -31.270, 117.230),
('6462', 'Ucarty', 'WA', -31.270, 117.230),
('6463', 'Benjaberring', 'WA', -31.140, 117.290),
('6465', 'Manmanning', 'WA', -30.870, 117.040),
('6466', 'Cadoux', 'WA', -30.770, 117.140),
('6467', 'Burakin', 'WA', -30.530, 117.170),
('6468', 'Goodlands', 'WA', -30.110, 117.210),
('6468', 'Kalannie', 'WA', -30.110, 117.210),
('6468', 'Petrudor', 'WA', -30.110, 117.210),
('6470', 'Kulja', 'WA', -30.340, 117.340),
('6472', 'Beacon', 'WA', -30.450, 117.870),
('6472', 'Bimbijy', 'WA', -30.450, 117.870),
('6472', 'Cleary', 'WA', -30.450, 117.870),
('6472', 'Karroun Hill', 'WA', -30.450, 117.870),
('6472', 'Mouroubra', 'WA', -30.450, 117.870),
('6472', 'Remlap', 'WA', -30.450, 117.870),
('6472', 'Tampu', 'WA', -30.450, 117.870),
('6473', 'North Wialki', 'WA', -30.330, 118.220),
('6473', 'Wialki', 'WA', -30.330, 118.220),
('6475', 'Badgerin Rock', 'WA', -30.690, 117.270),
('6475', 'Booralaming', 'WA', -30.690, 117.270),
('6475', 'Dukin', 'WA', -30.690, 117.270),
('6475', 'Koorda', 'WA', -30.690, 117.270),
('6475', 'Lake Margarette', 'WA', -30.690, 117.270),
('6475', 'Mollerin', 'WA', -30.690, 117.270),
('6475', 'Newcarlbeon', 'WA', -30.690, 117.270),
('6476', 'Gabbin', 'WA', -30.800, 117.680),
('6477', 'Bencubbin', 'WA', -30.810, 117.860),
('6477', 'Welbungin', 'WA', -30.810, 117.860),
('6479', 'Barbalin', 'WA', -30.880, 118.110),
('6479', 'Bonnie Rock', 'WA', -30.880, 118.110),
('6479', 'Dandanning', 'WA', -30.880, 118.110),
('6479', 'Elachbutting', 'WA', -30.880, 118.110),
('6479', 'Karloning', 'WA', -30.880, 118.110),
('6479', 'Lake Brown', 'WA', -30.880, 118.110),
('6479', 'Mukinbudin', 'WA', -30.880, 118.110),
('6479', 'Wattoning', 'WA', -30.880, 118.110),
('6479', 'Wilgoyne', 'WA', -30.880, 118.110),
('6480', 'Nukarni', 'WA', -31.290, 118.200),
('6484', 'Bullfinch', 'WA', -30.980, 119.120),
('6484', 'Ennuin', 'WA', -30.980, 119.120),
('6484', 'Lake Deborah', 'WA', -30.980, 119.120),
('6485', 'Cowcowing', 'WA', -30.990, 117.450),
('6485', 'Korrelocking', 'WA', -30.990, 117.450),
('6485', 'Nalkain', 'WA', -30.990, 117.450),
('6485', 'Nembudding', 'WA', -30.990, 117.450),
('6485', 'Wyalkatchem', 'WA', -30.990, 117.450),
('6487', 'North Yelbeni', 'WA', -31.060, 117.660),
('6487', 'South Yelbeni', 'WA', -31.060, 117.660),
('6487', 'Yelbeni', 'WA', -31.060, 117.660),
('6488', 'North Trayning', 'WA', -31.040, 117.790),
('6488', 'South Trayning', 'WA', -31.040, 117.790),
('6488', 'Trayning', 'WA', -31.040, 117.790),
('6489', 'Kununoppin', 'WA', -31.110, 117.920),
('6489', 'North Kununoppin', 'WA', -31.110, 117.920),
('6489', 'South Kununoppin', 'WA', -31.110, 117.920),
('6490', 'Burran Rock', 'WA', -31.260, 118.010),
('6490', 'Chandler', 'WA', -31.260, 118.010),
('6490', 'Elabbin', 'WA', -31.260, 118.010),
('6490', 'Kwelkan', 'WA', -31.260, 118.010),
('6490', 'Nungarin', 'WA', -31.260, 118.010),
('6490', 'Talgomine', 'WA', -31.260, 118.010),
('6501', 'Muchea', 'WA', -31.600, 115.990),
('6502', 'Bindoon', 'WA', -31.390, 116.100),
('6502', 'Bindoon Training Area', 'WA', -31.390, 116.100),
('6503', 'Bambun', 'WA', -31.420, 115.900),
('6503', 'Beermullah', 'WA', -31.420, 115.900),
('6503', 'Boonanarring', 'WA', -31.420, 115.900),
('6503', 'Breera', 'WA', -31.420, 115.900),
('6503', 'Coonabidgee', 'WA', -31.420, 115.900),
('6503', 'Cowalla', 'WA', -31.420, 115.900),
('6503', 'Cullalla', 'WA', -31.420, 115.900),
('6503', 'Gingin', 'WA', -31.420, 115.900),
('6503', 'Ginginup', 'WA', -31.420, 115.900),
('6503', 'Granville', 'WA', -31.420, 115.900),
('6503', 'Lennard Brook', 'WA', -31.420, 115.900),
('6503', 'Mindarra', 'WA', -31.420, 115.900),
('6503', 'Moondah', 'WA', -31.420, 115.900),
('6503', 'Moore River National Park', 'WA', -31.420, 115.900),
('6503', 'Muckenburra', 'WA', -31.420, 115.900),
('6503', 'Neergabby', 'WA', -31.420, 115.900),
('6503', 'Orange Springs', 'WA', -31.420, 115.900),
('6503', 'Red Gully', 'WA', -31.420, 115.900),
('6503', 'Wanerie', 'WA', -31.420, 115.900),
('6503', 'Yeal', 'WA', -31.420, 115.900),
('6504', 'Mooliabeenee', 'WA', -31.330, 116.030),
('6505', 'Wannamal', 'WA', -31.170, 115.930),
('6506', 'Mogumber', 'WA', -31.020, 115.960),
('6507', 'Cataby', 'WA', -30.740, 115.540),
('6507', 'Cooljarloo', 'WA', -30.740, 115.540),
('6507', 'Dandaragan', 'WA', -30.740, 115.540),
('6507', 'Mimegarra', 'WA', -30.740, 115.540),
('6507', 'Regans Ford', 'WA', -30.740, 115.540),
('6507', 'Yathroo', 'WA', -30.740, 115.540),
('6509', 'Glentromie', 'WA', -30.890, 116.240),
('6509', 'New Norcia', 'WA', -30.890, 116.240),
('6509', 'Waddington', 'WA', -30.890, 116.240),
('6509', 'Yarawindah', 'WA', -30.890, 116.240),
('6510', 'Barberton', 'WA', -30.730, 116.030),
('6510', 'Berkshire Valley', 'WA', -30.730, 116.030),
('6510', 'Gillingarra', 'WA', -30.730, 116.030),
('6510', 'Koojan', 'WA', -30.730, 116.030),
('6510', 'Moora', 'WA', -30.730, 116.030),
('6510', 'Walebing', 'WA', -30.730, 116.030),
('6511', 'Cervantes', 'WA', -30.500, 115.080),
('6512', 'Coomberdale', 'WA', -30.440, 116.040),
('6512', 'Namban', 'WA', -30.440, 116.040),
('6513', 'Gunyidi', 'WA', -30.150, 116.080),
('6513', 'Watheroo', 'WA', -30.150, 116.080),
('6514', 'Green Head', 'WA', -30.060, 114.970),
('6514', 'Leeman', 'WA', -30.060, 114.970),
('6515', 'Coorow', 'WA', -29.880, 116.020),
('6515', 'Eganu', 'WA', -29.880, 116.020),
('6515', 'Marchagee', 'WA', -29.880, 116.020),
('6515', 'Waddy Forest', 'WA', -29.880, 116.020),
('6516', 'Jurien Bay', 'WA', -30.310, 115.040),
('6517', 'Carnamah', 'WA', -29.690, 115.890),
('6518', 'Eneabba', 'WA', -29.820, 115.270),
('6518', 'Warradarge', 'WA', -29.820, 115.270),
('6519', 'Arrino', 'WA', -29.440, 115.630),
('6519', 'Arrowsmith East', 'WA', -29.440, 115.630),
('6519', 'Dudawa', 'WA', -29.440, 115.630),
('6519', 'Kadathinni', 'WA', -29.440, 115.630),
('6519', 'Three Springs', 'WA', -29.440, 115.630),
('6519', 'Womarden', 'WA', -29.440, 115.630),
('6521', 'Badgingarra', 'WA', -30.390, 115.500),
('6521', 'Boothendarra', 'WA', -30.390, 115.500),
('6521', 'Grey', 'WA', -30.390, 115.500),
('6521', 'Hill River', 'WA', -30.390, 115.500),
('6521', 'Nambung', 'WA', -30.390, 115.500),
('6522', 'Bundanoon', 'WA', -29.300, 115.490),
('6522', 'Holmwood', 'WA', -29.300, 115.490),
('6522', 'Ikewa', 'WA', -29.300, 115.490),
('6522', 'Lockier', 'WA', -29.300, 115.490),
('6522', 'Mingenew', 'WA', -29.300, 115.490),
('6522', 'Mooriary', 'WA', -29.300, 115.490),
('6522', 'Mount Budd', 'WA', -29.300, 115.490),
('6522', 'Nangetty', 'WA', -29.300, 115.490),
('6522', 'Yandanooka', 'WA', -29.300, 115.490),
('6522', 'Yarragadee', 'WA', -29.300, 115.490),
('6525', 'Allanooka', 'WA', -29.050, 115.030),
('6525', 'Arrowsmith', 'WA', -29.050, 115.030),
('6525', 'Bonniefield', 'WA', -29.050, 115.030),
('6525', 'Bookara', 'WA', -29.050, 115.030),
('6525', 'Dongara', 'WA', -29.050, 115.030),
('6525', 'Irwin', 'WA', -29.050, 115.030),
('6525', 'Milo', 'WA', -29.050, 115.030),
('6525', 'Mount Adams', 'WA', -29.050, 115.030),
('6525', 'Mount Horner', 'WA', -29.050, 115.030),
('6525', 'Port Denison', 'WA', -29.050, 115.030),
('6525', 'Springfield', 'WA', -29.050, 115.030),
('6525', 'Yardarino', 'WA', -29.050, 115.030),
('6528', 'Mount Hill', 'WA', -28.980, 114.910),
('6528', 'South Greenough', 'WA', -28.980, 114.910),
('6528', 'Walkaway', 'WA', -28.980, 114.910),
('6530', 'Beachlands', 'WA', -28.790, 114.600),
('6530', 'Beresford', 'WA', -28.790, 114.600),
('6530', 'Bluff Point', 'WA', -28.790, 114.600),
('6530', 'Geraldton', 'WA', -28.790, 114.600),
('6530', 'Geraldton DC', 'WA', -28.790, 114.600),
('6530', 'Houtman Abrolhos', 'WA', -28.790, 114.600),
('6530', 'Karloo', 'WA', -28.790, 114.600),
('6530', 'Mahomets Flats', 'WA', -28.790, 114.600),
('6530', 'Meru', 'WA', -28.790, 114.600),
('6530', 'Moresby', 'WA', -28.790, 114.600),
('6530', 'Mount Tarcoola', 'WA', -28.790, 114.600),
('6530', 'Rangeway', 'WA', -28.790, 114.600),
('6530', 'Spalding', 'WA', -28.790, 114.600),
('6530', 'Strathalbyn', 'WA', -28.790, 114.600),
('6530', 'Sunset Beach', 'WA', -28.790, 114.600),
('6530', 'Tarcoola Beach', 'WA', -28.790, 114.600),
('6530', 'Utakarra', 'WA', -28.790, 114.600),
('6530', 'Waggrakine', 'WA', -28.790, 114.600),
('6530', 'Wandina', 'WA', -28.790, 114.600),
('6530', 'Webberton', 'WA', -28.790, 114.600),
('6530', 'West End', 'WA', -28.790, 114.600),
('6530', 'Wonthella', 'WA', -28.790, 114.600),
('6530', 'Woorree', 'WA', -28.790, 114.600),
('6531', 'Geraldton Po', 'WA', -32.280, 115.740),
('6532', 'Ajana', 'WA', -27.950, 114.610),
('6532', 'Binnu', 'WA', -27.950, 114.610),
('6532', 'Bootenal', 'WA', -27.950, 114.610),
('6532', 'Bringo', 'WA', -27.950, 114.610),
('6532', 'Buller', 'WA', -27.950, 114.610),
('6532', 'Burma Road', 'WA', -27.950, 114.610),
('6532', 'Cape Burney', 'WA', -27.950, 114.610),
('6532', 'Carrarang', 'WA', -27.950, 114.610),
('6532', 'Coburn', 'WA', -27.950, 114.610),
('6532', 'Coolcalalaya', 'WA', -27.950, 114.610),
('6532', 'Dartmoor', 'WA', -27.950, 114.610),
('6532', 'Deepdale', 'WA', -27.950, 114.610),
('6532', 'Dindiloa', 'WA', -27.950, 114.610),
('6532', 'Drummond Cove', 'WA', -27.950, 114.610),
('6532', 'Durawah', 'WA', -27.950, 114.610),
('6532', 'East Chapman', 'WA', -27.950, 114.610),
('6532', 'East Nabawa', 'WA', -27.950, 114.610),
('6532', 'East Yuna', 'WA', -27.950, 114.610),
('6532', 'Ellendale', 'WA', -27.950, 114.610),
('6532', 'Eradu', 'WA', -27.950, 114.610),
('6532', 'Eradu South', 'WA', -27.950, 114.610),
('6532', 'Eurardy', 'WA', -27.950, 114.610),
('6532', 'Georgina', 'WA', -27.950, 114.610),
('6532', 'Glenfield', 'WA', -27.950, 114.610),
('6532', 'Greenough', 'WA', -27.950, 114.610),
('6532', 'Hamelin Pool', 'WA', -27.950, 114.610),
('6532', 'Hickety', 'WA', -27.950, 114.610),
('6532', 'Howatharra', 'WA', -27.950, 114.610),
('6532', 'Kojarena', 'WA', -27.950, 114.610),
('6532', 'Marrah', 'WA', -27.950, 114.610),
('6532', 'Meadow', 'WA', -27.950, 114.610),
('6532', 'Minnenooka', 'WA', -27.950, 114.610),
('6532', 'Moonyoonooka', 'WA', -27.950, 114.610),
('6532', 'Mount Erin', 'WA', -27.950, 114.610),
('6532', 'Nabawa', 'WA', -27.950, 114.610),
('6532', 'Nanson', 'WA', -27.950, 114.610),
('6532', 'Naraling', 'WA', -27.950, 114.610),
('6532', 'Narngulu', 'WA', -27.950, 114.610),
('6532', 'Narra Tarra', 'WA', -27.950, 114.610),
('6532', 'Nerren Nerren', 'WA', -27.950, 114.610),
('6532', 'Nolba', 'WA', -27.950, 114.610),
('6532', 'North Eradu', 'WA', -27.950, 114.610),
('6532', 'Northern Gully', 'WA', -27.950, 114.610),
('6532', 'Oakajee', 'WA', -27.950, 114.610),
('6532', 'Rockwell', 'WA', -27.950, 114.610),
('6532', 'Rudds Gully', 'WA', -27.950, 114.610),
('6532', 'Sandsprings', 'WA', -27.950, 114.610),
('6532', 'South Yuna', 'WA', -27.950, 114.610),
('6532', 'Tamala', 'WA', -27.950, 114.610),
('6532', 'Tibradden', 'WA', -27.950, 114.610),
('6532', 'Toolonga', 'WA', -27.950, 114.610),
('6532', 'Valentine', 'WA', -27.950, 114.610),
('6532', 'Wandana', 'WA', -27.950, 114.610),
('6532', 'West Binnu', 'WA', -27.950, 114.610),
('6532', 'White Peak', 'WA', -27.950, 114.610),
('6532', 'Wicherina', 'WA', -27.950, 114.610),
('6532', 'Wicherina South', 'WA', -27.950, 114.610),
('6532', 'Yetna', 'WA', -27.950, 114.610),
('6532', 'Yuna', 'WA', -27.950, 114.610),
('6535', 'Alma', 'WA', -28.230, 114.690),
('6535', 'Bowes', 'WA', -28.230, 114.690),
('6535', 'East Bowes', 'WA', -28.230, 114.690),
('6535', 'Gregory', 'WA', -28.230, 114.690),
('6535', 'Horrocks', 'WA', -28.230, 114.690),
('6535', 'Isseka', 'WA', -28.230, 114.690),
('6535', 'Northampton', 'WA', -28.230, 114.690),
('6535', 'Ogilvie', 'WA', -28.230, 114.690),
('6535', 'Sandy Gully', 'WA', -28.230, 114.690),
('6535', 'Yallabatharra', 'WA', -28.230, 114.690),
('6536', 'Kalbarri', 'WA', -27.710, 114.160),
('6536', 'Kalbarri National Park', 'WA', -27.710, 114.160),
('6536', 'Zuytdorp', 'WA', -27.710, 114.160),
('6537', 'Denham', 'WA', -25.930, 113.530),
('6537', 'Dirk Hartog Island', 'WA', -25.930, 113.530),
('6537', 'Francois Peron National Park', 'WA', -25.930, 113.530),
('6537', 'Monkey Mia', 'WA', -25.930, 113.530),
('6537', 'Nanga', 'WA', -25.930, 113.530),
('6537', 'Shark Bay', 'WA', -25.930, 113.530),
('6537', 'Useless Loop', 'WA', -25.930, 113.530),
('6556', 'Beechina', 'WA', -31.860, 116.320),
('6556', 'Chidlow', 'WA', -31.860, 116.320),
('6556', 'Gorrie', 'WA', -31.860, 116.320),
('6556', 'Malmalling', 'WA', -31.860, 116.320),
('6556', 'The Lakes', 'WA', -31.860, 116.320),
('6558', 'Wooroloo', 'WA', -31.800, 116.310),
('6560', 'Wundowie', 'WA', -31.750, 116.390),
('6562', 'Bakers Hill', 'WA', -31.750, 116.460),
('6562', 'Copley', 'WA', -31.750, 116.460),
('6562', 'Woottating', 'WA', -31.750, 116.460),
('6564', 'Clackline', 'WA', -31.720, 116.510),
('6566', 'Bejoording', 'WA', -31.380, 116.530),
('6566', 'Carani', 'WA', -31.380, 116.530),
('6566', 'Coondle', 'WA', -31.380, 116.530),
('6566', 'Culham', 'WA', -31.380, 116.530),
('6566', 'Dumbarton', 'WA', -31.380, 116.530),
('6566', 'Hoddys Well', 'WA', -31.380, 116.530),
('6566', 'Katrine', 'WA', -31.380, 116.530),
('6566', 'Nunile', 'WA', -31.380, 116.530),
('6566', 'Toodyay', 'WA', -31.380, 116.530),
('6566', 'West Toodyay', 'WA', -31.380, 116.530),
('6567', 'Dewars Pool', 'WA', -31.460, 116.420),
('6567', 'Julimar', 'WA', -31.460, 116.420),
('6567', 'Moondyne', 'WA', -31.460, 116.420),
('6568', 'Bolgart', 'WA', -31.270, 116.510),
('6568', 'Wattening', 'WA', -31.270, 116.510),
('6568', 'Wyening', 'WA', -31.270, 116.510),
('6569', 'Calingiri', 'WA', -31.090, 116.450),
('6569', 'Old Plains', 'WA', -31.090, 116.450),
('6571', 'Yerecoin', 'WA', -30.920, 116.390),
('6572', 'Piawaning', 'WA', 0.000, 0.000),
('6574', 'Bindi Bindi', 'WA', -30.630, 116.360),
('6574', 'Gabalong', 'WA', -30.630, 116.360),
('6575', 'Miling', 'WA', -30.490, 116.360),
('6603', 'Konnongorring', 'WA', -31.010, 116.760),
('6603', 'Lake Hinds', 'WA', -31.010, 116.760),
('6603', 'Lake Ninan', 'WA', -31.010, 116.760),
('6603', 'Mocardy', 'WA', -31.010, 116.760),
('6603', 'Wongan Hills', 'WA', -31.010, 116.760),
('6605', 'Kondut', 'WA', -30.710, 116.700),
('6606', 'Ballidu', 'WA', -30.600, 116.770),
('6606', 'East Ballidu', 'WA', -30.600, 116.770),
('6606', 'West Ballidu', 'WA', -30.600, 116.770),
('6608', 'East Damboring', 'WA', -30.480, 116.810),
('6608', 'Marne', 'WA', -30.480, 116.810),
('6608', 'Pithara', 'WA', -30.480, 116.810),
('6609', 'Dalwallinu', 'WA', -30.280, 116.660),
('6609', 'Nugadong', 'WA', -30.280, 116.660),
('6609', 'Xantippe', 'WA', -30.280, 116.660),
('6612', 'Jibberding', 'WA', -30.020, 116.780),
('6612', 'Miamoon', 'WA', -30.020, 116.780),
('6612', 'Paynes Find', 'WA', -30.020, 116.780),
('6612', 'Wubin', 'WA', -30.020, 116.780),
('6613', 'Buntine', 'WA', -29.990, 116.570),
('6614', 'Maya', 'WA', -29.880, 116.500),
('6616', 'Latham', 'WA', -29.760, 116.440),
('6620', 'Perenjori', 'WA', -29.440, 116.290),
('6620', 'Rothsay', 'WA', -29.440, 116.290),
('6623', 'Bowgada', 'WA', -29.330, 116.150),
('6623', 'Bunjil', 'WA', -29.330, 116.150),
('6623', 'Gutha', 'WA', -29.330, 116.150),
('6623', 'Koolanooka', 'WA', -29.330, 116.150),
('6623', 'Morawa', 'WA', -29.330, 116.150),
('6623', 'Pintharuka', 'WA', -29.330, 116.150),
('6625', 'Merkanooka', 'WA', 0.000, 0.000),
('6627', 'Canna', 'WA', -28.900, 115.860),
('6628', 'Tardun', 'WA', -28.790, 115.750),
('6630', 'Devils Creek', 'WA', -28.640, 115.480),
('6630', 'Mullewa', 'WA', -28.640, 115.480),
('6630', 'Murchison', 'WA', -28.640, 115.480),
('6630', 'Nerramyne', 'WA', -28.640, 115.480),
('6630', 'Nunierra', 'WA', -28.640, 115.480),
('6630', 'West Casuarinas', 'WA', -28.640, 115.480),
('6630', 'Wongoondy', 'WA', -28.640, 115.480),
('6630', 'Woolgorong', 'WA', -28.640, 115.480),
('6631', 'Pindar', 'WA', -28.480, 115.790),
('6632', 'Ambania', 'WA', -28.700, 115.120),
('6632', 'Tenindewa', 'WA', -28.700, 115.120),
('6635', 'South Murchison', 'WA', -27.320, 116.570),
('6635', 'Yalgoo', 'WA', -27.320, 116.570),
('6638', 'Cooladar Hill', 'WA', -28.480, 118.170),
('6638', 'Daggar Hills', 'WA', -28.480, 118.170),
('6638', 'Mount Magnet', 'WA', -28.480, 118.170),
('6638', 'Paynesville', 'WA', -28.480, 118.170),
('6639', 'Sandstone', 'WA', -28.200, 119.650),
('6640', 'Cue', 'WA', -27.420, 117.900),
('6640', 'East Murchison', 'WA', -27.420, 117.900),
('6640', 'Lake Austin', 'WA', -27.420, 117.900),
('6640', 'Reedy', 'WA', -27.420, 117.900),
('6640', 'Weld Range', 'WA', -27.420, 117.900),
('6642', 'Angelo River', 'WA', -24.080, 118.010),
('6642', 'Capricorn', 'WA', -24.080, 118.010),
('6642', 'Kumarina', 'WA', -24.080, 118.010),
('6642', 'Meekatharra', 'WA', -24.080, 118.010),
('6642', 'Peak Hill', 'WA', -24.080, 118.010),
('6646', 'Lake Carnegie', 'WA', -26.340, 122.260),
('6646', 'Little Sandy Desert', 'WA', -26.340, 122.260),
('6646', 'Wiluna', 'WA', -26.340, 122.260),
('6701', 'Babbage Island', 'WA', -24.870, 113.630),
('6701', 'Bernier Island', 'WA', -24.870, 113.630),
('6701', 'Brockman', 'WA', -24.870, 113.630),
('6701', 'Brown Range', 'WA', -24.870, 113.630),
('6701', 'Carbla', 'WA', -24.870, 113.630),
('6701', 'Carnarvon', 'WA', -24.870, 113.630),
('6701', 'Coral Bay', 'WA', -24.870, 113.630),
('6701', 'Dorre Island', 'WA', -24.870, 113.630),
('6701', 'East Carnarvon', 'WA', -24.870, 113.630),
('6701', 'Gilroyd', 'WA', -24.870, 113.630),
('6701', 'Greys Plain', 'WA', -24.870, 113.630),
('6701', 'Inggarda', 'WA', -24.870, 113.630),
('6701', 'Kennedy Range', 'WA', -24.870, 113.630),
('6701', 'Kingsford', 'WA', -24.870, 113.630),
('6701', 'Lyndon', 'WA', -24.870, 113.630),
('6701', 'Macleod', 'WA', -24.870, 113.630),
('6701', 'Massey Bay', 'WA', -24.870, 113.630),
('6701', 'Minilya', 'WA', -24.870, 113.630),
('6701', 'Morgantown', 'WA', -24.870, 113.630),
('6701', 'Ningaloo', 'WA', -24.870, 113.630),
('6701', 'North Plantations', 'WA', -24.870, 113.630),
('6701', 'South Carnarvon', 'WA', -24.870, 113.630),
('6701', 'South Plantations', 'WA', -24.870, 113.630),
('6701', 'Talisker', 'WA', -24.870, 113.630),
('6701', 'Woodleigh', 'WA', -24.870, 113.630),
('6701', 'Wooramel', 'WA', -24.870, 113.630),
('6701', 'Yalardy', 'WA', -24.870, 113.630),
('6701', 'Yandoo Creek', 'WA', -24.870, 113.630),
('6705', 'East Lyons River', 'WA', -24.230, 117.380),
('6705', 'Gascoyne Junction', 'WA', -24.230, 117.380),
('6705', 'Gascoyne River', 'WA', -24.230, 117.380),
('6705', 'West Lyons River', 'WA', -24.230, 117.380),
('6707', 'Cape Range National Park', 'WA', -22.160, 113.920),
('6707', 'Exmouth', 'WA', -22.160, 113.920),
('6707', 'Exmouth Gulf', 'WA', -22.160, 113.920),
('6707', 'Learmonth', 'WA', -22.160, 113.920),
('6707', 'North West Cape', 'WA', -22.160, 113.920),
('6710', 'Cane', 'WA', -22.100, 115.860),
('6710', 'Onslow', 'WA', -22.100, 115.860),
('6710', 'Peedamulla', 'WA', -22.100, 115.860),
('6710', 'Talandji', 'WA', -22.100, 115.860),
('6710', 'Yannarie', 'WA', -22.100, 115.860),
('6711', 'Thevenard Island', 'WA', -21.460, 115.000),
('6712', 'Barrow Island', 'WA', -20.860, 115.410),
('6713', 'Dampier', 'WA', -20.660, 116.710),
('6713', 'Dampier Archipelago', 'WA', -20.660, 116.710),
('6714', 'Antonymyre', 'WA', -20.660, 117.090),
('6714', 'Balla Balla', 'WA', -20.660, 117.090),
('6714', 'Baynton', 'WA', -20.660, 117.090),
('6714', 'Bulgarra', 'WA', -20.660, 117.090),
('6714', 'Burrup', 'WA', -20.660, 117.090),
('6714', 'Cleaverville', 'WA', -20.660, 117.090),
('6714', 'Cooya Pooya', 'WA', -20.660, 117.090),
('6714', 'Gap Ridge', 'WA', -20.660, 117.090),
('6714', 'Gnoorea', 'WA', -20.660, 117.090),
('6714', 'Karratha', 'WA', -20.660, 117.090),
('6714', 'Karratha Industrial Estate', 'WA', -20.660, 117.090),
('6714', 'Maitland', 'WA', -20.660, 117.090),
('6714', 'Mardie', 'WA', -20.660, 117.090),
('6714', 'Millars Well', 'WA', -20.660, 117.090),
('6714', 'Mount Anketell', 'WA', -20.660, 117.090),
('6714', 'Mulataga', 'WA', -20.660, 117.090),
('6714', 'Nickol', 'WA', -20.660, 117.090),
('6714', 'Pegs Creek', 'WA', -20.660, 117.090),
('6714', 'Sherlock', 'WA', -20.660, 117.090),
('6714', 'Stove Hill', 'WA', -20.660, 117.090),
('6716', 'Fortescue', 'WA', -21.650, 116.130),
('6716', 'Hamersley Range', 'WA', -21.650, 116.130),
('6716', 'Millstream', 'WA', -21.650, 116.130),
('6716', 'Pannawonica', 'WA', -21.650, 116.130),
('6718', 'Roebourne', 'WA', -20.780, 117.150),
('6718', 'Whim Creek', 'WA', -20.780, 117.150),
('6720', 'Cossack', 'WA', -20.680, 117.190),
('6720', 'Point Samson', 'WA', -20.680, 117.190),
('6720', 'Wickham', 'WA', -20.680, 117.190),
('6721', 'Indee', 'WA', -20.790, 118.600),
('6721', 'Mundabullangana', 'WA', -20.790, 118.600),
('6721', 'Pardoo', 'WA', -20.790, 118.600),
('6721', 'Port Hedland', 'WA', -20.790, 118.600),
('6721', 'Redbank', 'WA', -20.790, 118.600),
('6721', 'Strelley', 'WA', -20.790, 118.600),
('6721', 'Wallareenya', 'WA', -20.790, 118.600),
('6721', 'Wedgefield', 'WA', -20.790, 118.600),
('6722', 'Boodarie', 'WA', -20.400, 118.570),
('6722', 'De Grey', 'WA', -20.400, 118.570),
('6722', 'Finucane', 'WA', -20.400, 118.570),
('6722', 'Pippingarra', 'WA', -20.400, 118.570),
('6722', 'South Hedland', 'WA', -20.400, 118.570),
('6725', 'Bilingurr', 'WA', -17.910, 122.250),
('6725', 'Broome', 'WA', -17.910, 122.250),
('6725', 'Dampier Peninsula', 'WA', -17.910, 122.250),
('6725', 'Djugun', 'WA', -17.910, 122.250),
('6725', 'Eighty Mile Beach', 'WA', -17.910, 122.250),
('6725', 'Gingerah', 'WA', -17.910, 122.250),
('6725', 'Lagrange', 'WA', -17.910, 122.250),
('6725', 'Minyirr', 'WA', -17.910, 122.250),
('6725', 'Roebuck', 'WA', -17.910, 122.250),
('6725', 'Waterbank', 'WA', -17.910, 122.250),
('6726', 'Cable Beach', 'WA', -17.950, 122.200),
('6728', 'Camballin', 'WA', -17.990, 124.180),
('6728', 'Derby', 'WA', -17.990, 124.180),
('6728', 'Geegully Creek', 'WA', -17.990, 124.180),
('6728', 'Kimbolton', 'WA', -17.990, 124.180),
('6728', 'King Leopold Ranges', 'WA', -17.990, 124.180),
('6728', 'Meda', 'WA', -17.990, 124.180),
('6728', 'St George Ranges', 'WA', -17.990, 124.180),
('6728', 'Willare', 'WA', -17.990, 124.180),
('6731', 'Cockatoo Island', 'WA', -16.090, 123.610),
('6733', 'Koolan Island', 'WA', -16.130, 123.740),
('6740', 'Drysdale River', 'WA', -14.910, 126.670),
('6740', 'Kalumburu', 'WA', -14.910, 126.670),
('6740', 'Mitchell Plateau', 'WA', -14.910, 126.670),
('6740', 'Oombulgurri', 'WA', -14.910, 126.670),
('6740', 'Prince Regent River', 'WA', -14.910, 126.670),
('6740', 'Wyndham', 'WA', -14.910, 126.670),
('6743', 'Cambridge Gulf', 'WA', -15.170, 128.550),
('6743', 'Durack', 'WA', -15.170, 128.550),
('6743', 'Gibb', 'WA', -15.170, 128.550),
('6743', 'Kununurra', 'WA', -15.170, 128.550),
('6743', 'Lake Argyle', 'WA', -15.170, 128.550),
('6743', 'Warmun', 'WA', -15.170, 128.550),
('6751', 'Chichester', 'WA', -21.910, 118.090),
('6751', 'Innawanga', 'WA', -21.910, 118.090),
('6751', 'Juna Downs', 'WA', -21.910, 118.090),
('6751', 'Karijini', 'WA', -21.910, 118.090),
('6751', 'Mount Sheila', 'WA', -21.910, 118.090),
('6751', 'Mulga Downs', 'WA', -21.910, 118.090),
('6751', 'Nanutarra', 'WA', -21.910, 118.090),
('6751', 'Rocklea', 'WA', -21.910, 118.090),
('6751', 'Tom Price', 'WA', -21.910, 118.090),
('6751', 'Wittenoom', 'WA', -21.910, 118.090),
('6753', 'Newman', 'WA', -23.360, 119.740),
('6754', 'Paraburdoo', 'WA', -23.170, 117.750),
('6758', 'Nullagine', 'WA', -21.910, 120.200),
('6760', 'Marble Bar', 'WA', -21.170, 119.740),
('6762', 'Telfer', 'WA', -21.710, 122.230),
('6765', 'Fitzroy Crossing', 'WA', -18.190, 125.570),
('6765', 'Mount Hardman', 'WA', -18.190, 125.570),
('6770', 'Halls Creek', 'WA', -18.220, 127.670),
('6770', 'Mcbeath', 'WA', -18.220, 127.670),
('6770', 'Mueller Ranges', 'WA', -18.220, 127.670),
('6770', 'Ord River', 'WA', -18.220, 127.670),
('6770', 'Purnululu', 'WA', -18.220, 127.670),
('6770', 'Sturt Creek', 'WA', -18.220, 127.670),
('6770', 'Tanami', 'WA', -18.220, 127.670),
('6798', 'Christmas Island', 'WA', -10.490, 105.640),
('6799', 'Home Island Cocos (Keeling) Islands', 'WA', -12.170, 96.830),
('6799', 'West Island Cocos (Keeling) Islands', 'WA', -12.170, 96.830),
('6800', 'Perth', 'WA', -31.990, 115.760),
('6803', 'Northbridge', 'WA', 0.000, 0.000),
('6809', 'Perth', 'WA', -31.990, 115.760),
('6817', 'Perth', 'WA', -31.990, 115.760),
('6820', 'Perth', 'WA', -31.990, 115.760),
('6827', 'Perth', 'WA', -31.990, 115.760),
('6830', 'Perth', 'WA', -31.990, 115.760),
('6831', 'Perth St Georges Tce', 'WA', -31.960, 115.860),
('6832', 'Perth Adelaide Tce', 'WA', -31.960, 115.870),
('6837', 'Perth', 'WA', -31.990, 115.760),
('6838', 'Perth', 'WA', -31.990, 115.760),
('6839', 'Perth', 'WA', -31.990, 115.760),
('6840', 'Perth', 'WA', -31.990, 115.760),
('6841', 'Perth', 'WA', -31.990, 115.760),
('6842', 'Perth', 'WA', -31.990, 115.760),
('6843', 'Perth', 'WA', -31.990, 115.760),
('6844', 'Perth', 'WA', -31.990, 115.760),
('6845', 'Perth', 'WA', -31.990, 115.760),
('6846', 'Perth', 'WA', -31.990, 115.760),
('6847', 'Perth', 'WA', -31.990, 115.760),
('6848', 'Perth', 'WA', -31.990, 115.760),
('6849', 'Perth BC', 'WA', 0.000, 0.000),
('6850', 'Cloisters Square Po', 'WA', -33.660, 115.330),
('6865', 'Northbridge', 'WA', 0.000, 0.000),
('6872', 'West Perth', 'WA', -31.940, 115.880),
('6892', 'East Perth', 'WA', -31.780, 115.820),
('6900', 'Leederville', 'WA', 0.000, 0.000),
('6901', 'West Leederville', 'WA', -31.940, 115.880),
('6902', 'Leederville', 'WA', 0.000, 0.000),
('6903', 'Leederville', 'WA', 0.000, 0.000),
('6904', 'Subiaco Po', 'WA', -31.940, 115.830),
('6905', 'Northlands Po', 'WA', 0.000, 0.000),
('6906', 'North Perth', 'WA', -31.980, 115.760),
('6907', 'Nedlands', 'WA', 0.000, 0.000),
('6909', 'Nedlands', 'WA', 0.000, 0.000),
('6910', 'Claremont', 'WA', -31.980, 115.770),
('6911', 'Cottesloe', 'WA', -32.400, 115.760),
('6912', 'Mosman Park', 'WA', -32.010, 115.760),
('6913', 'Wembley', 'WA', 0.000, 0.000),
('6914', 'Balcatta', 'WA', -31.860, 115.820),
('6915', 'Mount Hawthorn', 'WA', -31.950, 115.850),
('6916', 'Osborne Park', 'WA', -31.890, 115.800),
('6916', 'Osborne Park DC', 'WA', -31.890, 115.800),
('6917', 'Osborne Park', 'WA', -31.890, 115.800),
('6918', 'Innaloo', 'WA', 0.000, 0.000),
('6919', 'Joondalup DC', 'WA', 0.000, 0.000),
('6920', 'North Beach', 'WA', -31.860, 115.780),
('6921', 'Karrinyup', 'WA', -31.880, 115.790),
('6922', 'Scarborough', 'WA', -31.900, 115.800),
('6923', 'Hillarys', 'WA', 0.000, 0.000),
('6924', 'Greenwood', 'WA', -32.560, 115.800),
('6925', 'Walliston DC', 'WA', 0.000, 0.000),
('6926', 'Kalamunda', 'WA', -31.940, 116.010),
('6929', 'Mount Lawley', 'WA', -31.950, 115.850),
('6931', 'Maylands', 'WA', 0.000, 0.000),
('6932', 'Inglewood', 'WA', -31.920, 115.880),
('6933', 'Bayswater', 'WA', -31.910, 115.900),
('6934', 'Bassendean', 'WA', -31.920, 115.920),
('6935', 'Guildford', 'WA', -31.920, 115.920),
('6936', 'Midland DC', 'WA', 0.000, 0.000),
('6937', 'Tuart Hill', 'WA', -31.900, 115.850),
('6938', 'Tuart Hill', 'WA', -31.900, 115.850),
('6939', 'Tuart Hill', 'WA', -31.900, 115.850),
('6940', 'Tuart Hill', 'WA', -31.900, 115.850),
('6941', 'Mirrabooka', 'WA', -31.840, 115.860),
('6942', 'Bassendean DC', 'WA', 0.000, 0.000),
('6943', 'Morley', 'WA', -31.890, 115.890),
('6944', 'Malaga', 'WA', -31.860, 115.900),
('6945', 'Malaga DC', 'WA', 0.000, 0.000),
('6946', 'Wanneroo', 'WA', -31.620, 115.720),
('6947', 'Wangara DC', 'WA', 0.000, 0.000),
('6951', 'South Perth', 'WA', -32.070, 115.820),
('6952', 'Como', 'WA', -31.890, 115.820),
('6953', 'Applecross', 'WA', 0.000, 0.000),
('6954', 'Booragoon', 'WA', 0.000, 0.000),
('6955', 'Willetton', 'WA', 0.000, 0.000),
('6956', 'Melville', 'WA', -31.990, 115.850),
('6957', 'Palmyra', 'WA', 0.000, 0.000),
('6958', 'Royal Australian Navy Warships', 'WA', 0.000, 0.000),
('6959', 'Fremantle', 'WA', -32.070, 115.990),
('6960', 'Myaree BC', 'WA', 0.000, 0.000),
('6961', 'Palmyra DC', 'WA', 0.000, 0.000),
('6963', 'Hamilton Hill', 'WA', -32.080, 115.790),
('6964', 'Success', 'WA', -32.020, 115.860),
('6965', 'Bibra Lake DC', 'WA', 0.000, 0.000),
('6966', 'Kwinana', 'WA', -32.140, 115.860),
('6967', 'Rockingham DC', 'WA', 0.000, 0.000),
('6968', 'Rockingham', 'WA', -32.110, 115.780),
('6969', 'Rockingham Beach', 'WA', -32.260, 115.750),
('6970', 'Canning Vale DC', 'WA', 0.000, 0.000),
('6979', 'Victoria Park', 'WA', -31.950, 115.900),
('6980', 'Cannington', 'WA', 0.000, 0.000),
('6981', 'East Victoria Park', 'WA', -31.950, 115.880),
('6982', 'Bentley', 'WA', -31.870, 116.170),
('6983', 'Bentley DC', 'WA', 0.000, 0.000),
('6984', 'Belmont', 'WA', -31.970, 115.930),
('6985', 'Cloverdale', 'WA', -33.630, 115.600),
('6986', 'Welshpool DC', 'WA', 0.000, 0.000),
('6987', 'Cannington', 'WA', 0.000, 0.000),
('6988', 'Thornlie', 'WA', -32.050, 115.960),
('6989', 'Maddington', 'WA', -32.040, 116.010),
('6990', 'Gosnells', 'WA', -32.060, 116.010),
('6991', 'Kelmscott', 'WA', 0.000, 0.000),
('6992', 'Armadale', 'WA', -31.960, 115.920),
('6997', 'Kelmscott DC', 'WA', 0.000, 0.000),
('8001', 'Melbourne', 'VIC', -38.370, 144.770),
('8002', 'East Melbourne', 'VIC', -38.110, 145.150),
('8003', 'Collins Street East', 'VIC', 0.000, 0.000),
('8004', 'St Kilda Road', 'VIC', -37.840, 144.980),
('8005', 'World Trade Centre', 'VIC', -37.820, 144.950),
('8006', 'Abeckett Street', 'VIC', -37.810, 144.960),
('8007', 'Collins Street West', 'VIC', 0.000, 0.000),
('8008', 'St Kilda Road Central', 'VIC', 0.000, 0.000),
('8009', 'Flinders Lane', 'VIC', -37.820, 144.960),
('8010', 'Law Courts', 'VIC', -38.190, 146.290),
('8011', 'Little Lonsdale Street', 'VIC', -37.810, 144.960),
('8012', 'Docklands', 'VIC', 0.000, 0.000),
('8045', 'Melbourne', 'VIC', -38.370, 144.770),
('8051', 'Melbourne', 'VIC', -38.370, 144.770),
('8060', 'Melbourne', 'VIC', -38.370, 144.770),
('8061', 'Melbourne', 'VIC', -38.370, 144.770),
('8066', 'Melbourne', 'VIC', -38.370, 144.770),
('8069', 'Melbourne', 'VIC', -38.370, 144.770),
('8070', 'Melbourne', 'VIC', -38.370, 144.770),
('8071', 'Melbourne', 'VIC', -38.370, 144.770),
('8102', 'Melbourne', 'VIC', -38.370, 144.770),
('8103', 'Melbourne', 'VIC', -38.370, 144.770),
('8107', 'Melbourne', 'VIC', -38.370, 144.770),
('8108', 'Melbourne', 'VIC', -38.370, 144.770),
('8111', 'Melbourne', 'VIC', -38.370, 144.770),
('8120', 'Melbourne', 'VIC', -38.370, 144.770),
('8205', 'Melbourne', 'VIC', -38.370, 144.770),
('8383', 'Melbourne', 'VIC', -38.370, 144.770),
('8386', 'Melbourne', 'VIC', -38.370, 144.770),
('8388', 'Melbourne', 'VIC', -38.370, 144.770),
('8390', 'Melbourne', 'VIC', -38.370, 144.770),
('8393', 'Melbourne', 'VIC', -38.370, 144.770),
('8394', 'Melbourne', 'VIC', -38.370, 144.770),
('8396', 'Melbourne', 'VIC', -38.370, 144.770),
('8399', 'Melbourne', 'VIC', -38.370, 144.770),
('8576', 'Ivanhoe', 'VIC', -37.760, 145.040),
('8627', 'Camberwell', 'VIC', -37.840, 145.060),
('8873', 'Port Melbourne', 'VIC', -37.850, 144.890)
DROP TABLE [dbo].[property_expenses_budget];
CREATE TABLE [dbo].[property_expenses_budget] (
  [ID] INT NOT NULL IDENTITY PRIMARY KEY,
  [created_at] DATETIME NOT NULL DEFAULT (GETDATE()),
  [created_by] INT NULL,
  [property_code] varchar(20) NOT NULL,
  [account_code] varchar(20) NOT NULL,
  [financial_year] varchar(4) NOT NULL,
  [year_budget_cash_value] decimal(19,6) DEFAULT 0,
  [year_budget_cash_forecast_value] decimal(19,6) DEFAULT 0,
  [year_budget_accruals_value] decimal(19,6) DEFAULT 0,
  [year_budget_accruals_forecast_value] decimal(19,6) DEFAULT 0,
  [allocation_type_id] INT NULL,
  [split_type_breakdown_id] INT NULL,
  [split_start_period] INT NULL,
  [comment] varchar(100)  NULL
);
DROP TABLE [dbo].[property_allocation_type_budget];
CREATE TABLE [dbo].[property_allocation_type_budget] (
  [ID] INT NOT NULL IDENTITY PRIMARY KEY,
  [created_at] DATETIME NOT NULL DEFAULT (GETDATE()),
  [created_by] INT NULL,
  [allocation_desc] varchar(255)
);

DROP TABLE [dbo].[property_split_type_budget];
CREATE TABLE [dbo].[property_split_type_budget] (
  [ID] INT NOT NULL IDENTITY PRIMARY KEY,
  [created_at] DATETIME NOT NULL DEFAULT (GETDATE()),
  [created_by] INT NULL,
  [split_type_desc] varchar(50)
);

ALTER TABLE [dbo].[property_budget_expense_breakdown]
ADD [parking_bays] INT DEFAULT 0;


update property_budget_expense_breakdown set parking_bays = 0;


TRUNCATE TABLE property_split_type_budget;
INSERT INTO property_split_type_budget(created_by,split_type_desc)
VALUES(545,'Monthly');
INSERT INTO property_split_type_budget(created_by,split_type_desc)
VALUES(545,'Quarterly');
INSERT INTO property_split_type_budget(created_by,split_type_desc)
VALUES(545,'Half yearly');
INSERT INTO property_split_type_budget(created_by,split_type_desc)
VALUES(545,'Yearly');
INSERT INTO property_split_type_budget(created_by,split_type_desc)
VALUES(545,'Custom');


TRUNCATE TABLE property_allocation_type_budget;
INSERT INTO property_allocation_type_budget(created_by,allocation_desc)
VALUES(545,'By net lettable area');
INSERT INTO property_allocation_type_budget(created_by,allocation_desc)
VALUES(545,'By net lettable area - allocated vacant to MISC lease');
INSERT INTO property_allocation_type_budget(created_by,allocation_desc)
VALUES(545,'Input custom amount');
INSERT INTO property_allocation_type_budget(created_by,allocation_desc)
VALUES(545,'Input custom percentage');
INSERT INTO property_allocation_type_budget(created_by,allocation_desc)
VALUES(545,'By number of car bays');



CREATE TABLE [dbo].[property_recoverable_split] (
    [ID] INT NOT NULL IDENTITY PRIMARY KEY,
    [created_at] DATETIME NOT NULL DEFAULT (GETDATE()),
    [created_by] INT NULL,
    [property_code] VARCHAR(11),
    [account_code] VARCHAR(11)
    );


CREATE TABLE [dbo].[property_recoverable_split_units] (
    [ID] INT NOT NULL IDENTITY PRIMARY KEY,
    [created_at] DATETIME NOT NULL DEFAULT (GETDATE()),
    [created_by] INT NULL,
    [property_recoverable_split_id] INT NOT NULL,
    [unit_code] VARCHAR(11),
    [unit_name] VARCHAR(100),
    [unit_percentage] DECIMAL(16,2)
    );

CREATE TABLE [dbo].[temp_property_recoverable_split] (
    [ID] INT NOT NULL IDENTITY PRIMARY KEY,
    [created_at] DATETIME NOT NULL DEFAULT (GETDATE()),
    [created_by] INT NULL,
    [property_code] VARCHAR(11),
    [account_code] VARCHAR(11)
    );


CREATE TABLE [dbo].[temp_property_recoverable_split_units] (
    [ID] INT NOT NULL IDENTITY PRIMARY KEY,
    [created_at] DATETIME NOT NULL DEFAULT (GETDATE()),
    [created_by] INT NULL,
    [property_recoverable_split_id] INT NOT NULL,
    [unit_code] VARCHAR(11),
    [unit_name] VARCHAR(100),
    [unit_percentage] DECIMAL(16,2)
    );

 CREATE TABLE [dbo].[prmf_fees](
	[prmf_id] [int] IDENTITY(1,1) NOT NULL,
	[prmf_account] [char](10) NULL,
	[prmf_method] [numeric](1, 0) NULL,
	[prmf_amount] [decimal](19, 2) NULL,
	[prmf_percentage] [numeric](8, 3) NULL,
	[prmf_start_period] [date] NULL,
	[prmf_end_period] [date] NULL,
	[prmf_prop] [char](10) NULL
) ON [PRIMARY]


INSERT INTO prmf_fees (prmf_account , prmf_method ,prmf_amount, prmf_percentage,
  prmf_start_period ,prmf_end_period , prmf_prop)
select  pmpr_c_acc,  pmpr_c_type , pmpr_c_val ,	  pmpr_c_pct  , '1900-01-01' , '2999-12-31',pmpr_prop
	from pmpr_property

ALTER TABLE pmrcf_p_fee_acc ADD pmrcf_prmf_id INT NOT NULL DEFAULT (0);

ALTER TABLE pmrcf_p_fee_acc
DROP CONSTRAINT pmrcf_p_fee_acc_pk ;

ALTER TABLE pmrcf_p_fee_acc
ADD CONSTRAINT pmrcf_p_fee_acc_pk PRIMARY KEY (pmrcf_prop ,pmrcf_acc , pmrcf_prmf_id);


UPDATE
  pmrcf_p_fee_acc
SET
  pmrcf_p_fee_acc.pmrcf_prmf_id = prmf_id
FROM
  pmrcf_p_fee_acc
INNER JOIN
  prmf_fees
ON
  pmrcf_prop = prmf_prop

  


-- NPMS only
ALTER TABLE database_list ADD timezone varchar(max)


CREATE TABLE [dbo].[timezones](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[timezone_code] [varchar](50) NULL,
	[timezone_name] [varchar](100) NULL,
	[timezone_utc] [varchar](50) NULL,
	[timezone_info] [varchar](100) NULL,
 CONSTRAINT [PK_timezones] PRIMARY KEY CLUSTERED
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO


INSERT INTO timezones(timezone_code, timezone_name, timezone_utc, timezone_info)
values


('AWST','Australian Western Standard Time','+08:00','W. Australia Standard Time'),
('ACST','Australian Central Standard Time','+09:30','AUS Central Standard Time'),
('AEST','Australian Eastern Standard Time','+10:00','E. Australia Standard Time'),
('ACDT','Australian Central Daylight Time','+10:30','Cen. Australia Standard Time'),
('AEDT','Australian Eastern Daylight Time','+11:00','AUS Eastern Standard Time'),
('NZST','New Zealand Standard Time','+13:00','New Zealand Standard Time')

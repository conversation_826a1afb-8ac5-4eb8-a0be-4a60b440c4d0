--per client
CREATE TABLE [dbo].[tj_history](
	[tjh_id] [int] IDENTITY(1,1) NOT NULL,
	[tjh_trans_date] [date] NULL,
	[tjh_from_prop] [varchar](50) NULL,
	[tjh_from_lease] [varchar](50) NULL,
	[tjh_from_account] [varchar](50) NULL,
	[tjh_from_desc] [varchar](100) NULL,
	[tjh_from_fdate] [date] NULL,
	[tjh_from_tdate] [date] NULL,
	[tjh_from_amount] [numeric](10, 2) NULL,
	[tjh_from_tax] [varchar](20) NULL,
	[tjh_from_net] [numeric](10, 2) NULL,
	[tjh_from_gst] [numeric](10, 2) NULL,
	[tjh_to_prop] [varchar](50) NULL,
	[tjh_to_lease] [varchar](50) NULL,
	[tjh_to_account] [varchar](50) NULL,
	[tjh_to_desc] [varchar](100) NULL,
	[tjh_to_fdate] [date] NULL,
	[tjh_to_tdate] [date] NULL,
	[tjh_to_tax] [varchar](20) NULL,
	[tjh_to_net] [numeric](10, 2) NULL,
	[tjh_to_gst] [numeric](10, 2) NULL,
	[tjh_created_by] [varchar](100) NULL,
	[tjh_created_date] [date] NULL,
	[tjh_source] [varchar](10) NULL,
	[tjh_creditor] [varchar](50) NULL,
	[tjh_from_unit] [varchar](50) NULL,
	[tjh_to_unit] [varchar](50) NULL
) ON [PRIMARY]
GO
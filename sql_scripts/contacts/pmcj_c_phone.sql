UPDATE pmcj_c_phone
SET pmcj_ph_code = 'PHONE'
WHERE pmcj_ph_code = 'PHONETYPE1'

UPDATE pmlj_l_phone
SET pmlj_ph_code = 'PHONE'
WHERE pmlj_ph_code = 'PHONETYPE1'


UPDATE pmcj_c_phone
SET pmcj_ph_code = 'MOBILE'
WHERE pmcj_ph_code = 'PHONETYPE2'

UPDATE pmlj_l_phone
SET pmlj_ph_code = 'MOBILE'
WHERE pmlj_ph_code = 'PHONETYPE2'


UPDATE pmcj_c_phone
SET pmcj_ph_code = 'EMAIL'
WHERE pmcj_ph_code = 'PHONETYPE4'

UPDATE pmlj_l_phone
SET pmlj_ph_code = 'EMAIL'
WHERE pmlj_ph_code = 'PHONETYPE4'


UPDATE pmcj_c_phone
SET pmcj_ph_code = 'SALU'
WHERE pmcj_ph_code = 'SALUTATION'

UPDATE pmlj_l_phone
SET pmlj_ph_code = 'SALU'
WHERE pmlj_ph_code = 'SALUTATION'


UPDATE pmcj_c_phone
SET pmcj_ph_code = 'MOBILE'
WHERE pmcj_ph_code = 'M'

UPDATE pmcj_c_phone
SET pmcj_ph_code = 'EMAIL'
WHERE pmcj_ph_code = 'E'

UPDATE pmcj_c_phone
SET pmcj_ph_code = 'PHONE'
WHERE pmcj_ph_code = 'P'

UPDATE pmcj_c_phone
SET pmcj_ph_code = 'PHONETYPE3'
WHERE pmcj_ph_code = 'X'
ALTER TABLE temp_ar_transaction ADD multiLeasesChargesID tinyint NULL DEFAULT NULL;

CREATE TABLE [dbo].[temp_multi_charges_detail](
	[temp_detail_id] [int] IDENTITY(1,1) NOT NULL,
	[temp_charge_review_id] [int] NULL,
	[temp_prop] [varchar](50) NULL,
	[temp_lease] [varchar](50) NULL,
	[temp_review] [varchar](50) NULL,
	[temp_date] [date] NULL,
	[temp_charge_old_amount] [money] NULL,
	[temp_charge_new_amount] [money] NULL,
	[temp_back_charge_amount] [money] NULL,
	[temp_letter_filename] [varchar](100) NULL,
	[temp_unit_code] [varchar](50) NULL,
	[temp_unit_serial] [tinyint] NULL,
	[temp_letter_template] [tinyint] NULL,
 CONSTRAINT [PK_temp_multi_charges_detail] PRIMARY KEY CLUSTERED 
(
	[temp_detail_id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

CREATE TABLE [dbo].[temp_multi_charges_header](
	[temp_charge_review_id] [int] IDENTITY(1,1) NOT NULL,
	[temp_start_date] [date] NULL,
	[temp_end_date] [date] NULL,
	[temp_review_type] [varchar](50) NULL,
	[temp_cpi_old] [money] NULL,
	[temp_cpi_current] [money] NULL,
	[temp_cpi_increase] [money] NULL,
	[temp_cpi_ages] [tinyint] NULL,
	[temp_cpi_index_name] [varchar](50) NULL,
	[temp_cpi_index_available] [int] NULL,
	[temp_cpi_manual] [tinyint] NULL,
	[temp_email_tenant] [tinyint] NULL,
	[temp_email_me] [tinyint] NULL,
	[temp_created_date] [datetime] NULL,
	[temp_created_by] [varchar](50) NULL,
	[temp_letterhead] [tinyint] NULL,
	[temp_invoice_date] [date] NULL,
	[temp_due_date] [date] NULL,
	[temp_generate_invoice] [tinyint] NULL,
	[temp_status] [tinyint] NULL,
	[temp_rejected_date] [date] NULL,
	[temp_rejected_by] [varchar](100) NULL,
	[temp_rejected_comment] [text] NULL,
	[temp_process_by] [varchar](100) NULL,
	[temp_process_date] [date] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
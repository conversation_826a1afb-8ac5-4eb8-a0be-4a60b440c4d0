/*
Navicat SQL Server Data Transfer

Source Server         : Cirrus8-home
Source Server Version : 150000
Source Host           : DESKTOP-GJJGRJ9\SQLEXPRESS01:1433
Source Database       : Civium
Source Schema         : dbo

Target Server Type    : SQL Server
Target Server Version : 150000
File Encoding         : 65001

Date: 2020-04-13 14:33:32
*/


-- ----------------------------
-- Table structure for adhoc_sundries
-- ----------------------------
CREATE TABLE [dbo].[adhoc_sundries] (
[id] int NOT NULL IDENTITY(1,1) ,
[trans_id] int NULL ,
[property_code] varchar(50) NOT NULL ,
[lease_code] varchar(50) NULL ,
[unit_id] varchar(50) NULL ,
[unit_charge_id] int NULL ,
[description] varchar(250) NOT NULL ,
[account_code] varchar(50) NOT NULL ,
[amount] decimal(19,2) NOT NULL ,
[type] varchar(50) NOT NULL ,
[status] varchar(100) NOT NULL ,
[created_by] varchar(150) NOT NULL ,
[created_at] datetime NOT NULL ,
[updated_by] varchar(150) NULL ,
[updated_at] datetime NULL ,
[deleted_by] varchar(150) NULL ,
[deleted_at] datetime NULL 
);

ALTER TABLE [dbo].[adhoc_sundries] ADD PRIMARY KEY ([id]);

CREATE TABLE [dbo].[temp_adhoc_sundries] (
[id] int NOT NULL IDENTITY(1,1) ,
[trans_id] int NULL ,
[property_code] varchar(50) NOT NULL ,
[lease_code] varchar(50) NULL ,
[unit_id] varchar(50) NULL ,
[unit_charge_id] int NULL ,
[description] varchar(250) NOT NULL ,
[account_code] varchar(50) NOT NULL ,
[amount] decimal(19,2) NOT NULL ,
[type] varchar(50) NOT NULL ,
[created_by] varchar(150) NOT NULL ,
[created_at] datetime NOT NULL ,
);

ALTER TABLE [dbo].[temp_adhoc_sundries] ADD PRIMARY KEY ([id]);
-- ----------------------------
-- Indexes structure for table adhoc_sundries
-- ----------------------------

-- ----------------------------
-- Primary Key structure for table adhoc_sundries
-- ----------------------------

ALTER TABLE adhoc_sundries 
ALTER COLUMN status int;

ALTER TABLE adhoc_sundries ADD CONSTRAINT DF_adhoc_sundries_status DEFAULT 0 FOR status;
/* done on dev and live */

CREATE TABLE [dbo].[lease_status_logs] (
  [ID] INT NOT NULL IDENTITY PRIMARY KEY,
  [created_at] DATETIME NOT NULL DEFAULT (GETUTCDATE()),
  [created_by] INT,
  [property_code] VARCHAR(11),
  [lease_code] VARCHAR(11),
  [lease_status] VARCHAR(10)
)

INSERT INTO lease_status_logs (property_code,lease_code,lease_status,created_at,created_by)
SELECT pmle_prop,pmle_lease,'CREATED',pmle_create_dt,user_created FROM pmle_lease
WHERE pmle_create_dt is not null AND user_created is not null


//per client
CREATE TABLE [dbo].[tier_list](
	[tier_id] [int] IDENTITY(1,1) NOT NULL,
	[tier_com_id] [int] NULL,
	[tier_from] [numeric](10, 2) NULL,
	[tier_to] [numeric](10, 2) NULL,
	[tier_percent] [numeric](5, 2) NULL
) ON [PRIMARY]
GO


CREATE TABLE [dbo].[commission](
	[com_id] [int] IDENTITY(1,1) NOT NULL,
	[com_ledger] [varchar](50) NULL,
	[com_lease] [varchar](50) NULL,
	[com_asking] [numeric](10, 2) NULL,
	[com_selling] [numeric](10, 2) NULL,
	[com_type] [tinyint] NULL,
	[com_amount] [numeric](10, 2) NULL,
	[com_percentage] [numeric](10, 2) NULL,
	[com_agent_type] [varchar](50) NULL,
	[com_agent_desc] [varchar](250) NULL,
	[com_agent_account] [varchar](50) NULL,
	[com_agent_amount] [numeric](10, 2) NULL,
	[created_by] [varchar](150) NOT NULL,
	[created_at] [datetime] NOT NULL,
	[updated_by] [varchar](150) NULL,
	[updated_at] [datetime] NULL
) ON [PRIMARY]
GO


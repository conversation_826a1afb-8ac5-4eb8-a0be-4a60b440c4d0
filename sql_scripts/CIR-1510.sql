/**
*
* <PERSON><PERSON><PERSON> SQL STATEMENTS FOR audit_trust_account
*
**/

ALTER TABLE audit_trust_account ADD pmbk_acc_name VARCHAR(100) NULL;
ALTER TABLE audit_trust_account ADD original_pmbk_acc_name VARCHAR(100) NULL;
ALTER TABLE audit_trust_account ADD pmbk_account VARCHAR(15) NULL;
ALTER TABLE audit_trust_account ADD original_pmbk_account VARCHAR(15) NULL;
ALTER TABLE audit_trust_account ADD pmbk_bsb VARCHAR(10) NULL;
ALTER TABLE audit_trust_account ADD original_pmbk_bsb VARCHAR(10) NULL;
ALTER TABLE audit_trust_account ADD pmbk_bank_name VARCHAR(100) NULL;
ALTER TABLE audit_trust_account ADD original_pmbk_bank_name VARCHAR(100) NULL;
ALTER TABLE audit_trust_account ADD pmbk_branch_name VARCHAR(100) NULL;
ALTER TABLE audit_trust_account ADD original_pmbk_branch_name VARCHAR(100) NULL;
ALTER TABLE audit_trust_account ADD pmbk_bpay_biller_code VARCHAR(20) NULL;
ALTER TABLE audit_trust_account ADD original_pmbk_bpay_biller_code VARCHAR(20) NULL;

ALTER TABLE audit_trust_account ADD pmbk_victoria_owner_corp bit not NULL DEFAULT 0;
ALTER TABLE audit_trust_account ADD original_pmbk_victoria_owner_corp bit not NULL DEFAULT 0;
ALTER TABLE audit_trust_account ADD pmbk_term_notice bit not NULL DEFAULT 0;
ALTER TABLE audit_trust_account ADD original_pmbk_term_notice bit not NULL DEFAULT 0;

/***********************/

/**
*
* for audit_trust_account
*
**/
CREATE TABLE audit_trust_account
(
	audit_id INT IDENTITY(1,1),
	pmbk_code VARCHAR(10),
	file_id INT,
	pmbk_street VARCHAR(80),
	pmbk_city VARCHAR(40),
	pmbk_state VARCHAR(10),
	pmbk_postcode VARCHAR(10),
	pmbk_short_name VARCHAR(10),
	pmbk_dr_bar TINYINT NOT NULL DEFAULT NULL,
	pmbk_rcpt_type VARCHAR(5),
	pmbk_tax_code VARCHAR(10),
	pmbk_db_inst VARCHAR(3),
	pmbk_db_name VARCHAR(30),
	pmbk_db_id VARCHAR(6),
	pmbk_db_remit VARCHAR(16),
	pmbk_deft TINYINT NOT NULL DEFAULT (0),
	pmbk_payway TINYINT NOT NULL DEFAULT (0),
	pmbk_credit_card TINYINT NOT NULL DEFAULT (0),
	pmbk_provider VARCHAR(100),
	pmbk_payway_code VARCHAR(100),
	pmbk_country VARCHAR(3),
	pmbk_currency VARCHAR(3),
	pmbk_foreign_currency VARCHAR(3),
	pmbk_dir_dep TINYINT NOT NULL DEFAULT (1),
	pmbk_bpay_batch_id VARCHAR(20),
	pmbk_bpay_user_id VARCHAR(20),
	pmbk_show_eft_info TINYINT NOT NULL DEFAULT (1),
	pmbk_prop_ref TINYINT NOT NULL DEFAULT (1),
	showOwnerCompanyOnReceipt TINYINT NOT NULL DEFAULT (0),
	pmbk_download_statement_client_id VARCHAR(60),
	pmbk_download_statement_password VARCHAR(60),
	pmbk_download_statement_client_number VARCHAR(50),
	pmbk_direct_upload_username VARCHAR(60),
	pmbk_direct_upload_password VARCHAR(60),
	pmbk_owner_ref TINYINT NOT NULL DEFAULT (1),
	pmbk_property_ref TINYINT NOT NULL DEFAULT (1),
	pmbk_duedate_ref TINYINT NOT NULL DEFAULT (0),
	original_pmbk_street VARCHAR(80),
	original_pmbk_city VARCHAR(40),
	original_pmbk_state VARCHAR(10),
	original_pmbk_postcode VARCHAR(10),
	original_pmbk_short_name VARCHAR(10),
	original_pmbk_dr_bar TINYINT NOT NULL DEFAULT NULL,
	original_pmbk_rcpt_type VARCHAR(5),
	original_pmbk_tax_code VARCHAR(10),
	original_pmbk_db_inst VARCHAR(3),
	original_pmbk_db_name VARCHAR(30),
	original_pmbk_db_id VARCHAR(6),
	original_pmbk_db_remit VARCHAR(16),
	original_pmbk_deft TINYINT NOT NULL DEFAULT (0),
	original_pmbk_payway TINYINT NOT NULL DEFAULT (0),
	original_pmbk_credit_card TINYINT NOT NULL DEFAULT (0),
	original_pmbk_provider VARCHAR(100),
	original_pmbk_payway_code VARCHAR(100),
	original_pmbk_country VARCHAR(3),
	original_pmbk_currency VARCHAR(3),
	original_pmbk_foreign_currency VARCHAR(3),
	original_pmbk_dir_dep TINYINT NOT NULL DEFAULT (1),
	original_pmbk_bpay_batch_id VARCHAR(20),
	original_pmbk_bpay_user_id VARCHAR(20),
	original_pmbk_show_eft_info TINYINT NOT NULL DEFAULT (1),
	original_pmbk_prop_ref TINYINT NOT NULL DEFAULT (1),
	original_showOwnerCompanyOnReceipt TINYINT NOT NULL DEFAULT (0),
	original_pmbk_download_statement_client_id VARCHAR(60),
	original_pmbk_download_statement_password VARCHAR(60),
	original_pmbk_download_statement_client_number VARCHAR(50),
	original_pmbk_direct_upload_username VARCHAR(60),
	original_pmbk_direct_upload_password VARCHAR(60),
	original_pmbk_owner_ref TINYINT NOT NULL DEFAULT (1),
	original_pmbk_property_ref TINYINT NOT NULL DEFAULT (1),
	original_pmbk_duedate_ref TINYINT NOT NULL DEFAULT (0),
	reason NVARCHAR(255),
	status TINYINT NOT NULL DEFAULT (0),
	authoriser INT,
	date_created DATETIME NULL,
	user_created INT,
	date_updated DATETIME NULL,
	user_updated INT
);

DROP TABLE audit_trust_account;
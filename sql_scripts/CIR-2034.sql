-- client DB - currently uses long report ID (2)
INSERT INTO [dbo].[sub_report]
([reportID]
    ,[subReportName]
    ,[subReportFile]
    ,[dependency]
    ,[sequence])
VALUES
    (2
        ,'Expense Prepayments'
        ,'GL_Journal/expensePrepayments.php'
        ,0
        ,12);

INSERT INTO [dbo].[sub_report]
([reportID]
    ,[subReportName]
    ,[subReportFile]
    ,[dependency]
    ,[sequence])
VALUES
    (2
        ,'Income Charged in Advance'
        ,'GL_Journal/incomeChargedInAdvance.php'
        ,0
        ,13);

INSERT INTO [dbo].[sub_report]
([reportID]
    ,[subReportName]
    ,[subReportFile]
    ,[dependency]
    ,[sequence])
VALUES
    (2
        ,'Expense Accruals'
        ,'GL_Journal/expenseAccruals.php'
        ,0
        ,14);

INSERT INTO [dbo].[sub_report]
([reportID]
    ,[subReportName]
    ,[subReportFile]
    ,[dependency]
    ,[sequence])
VALUES
    (2
        ,'Income Accruals'
        ,'GL_Journal/incomeAccruals.php'
        ,0
        ,15);
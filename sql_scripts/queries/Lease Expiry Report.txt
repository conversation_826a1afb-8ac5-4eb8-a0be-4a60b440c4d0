SELECT
    e.pmzz_desc   as shire
  , a.pmle_name   as lease_name
  , a.pmle_exp_dt as expiry
  , a.pmle_com_dt as commencement
  , CASE
        WHEN pmlc_freq = 'M'
            THEN 'Month'
        WHEN pmlc_freq = 'W'
            THEN 'Week'
        WHEN pmlc_freq = 'Y'
            THEN 'Year'
        WHEN pmlc_freq = 'Q'
            THEN 'Quarter'
        WHEN pmlc_freq = 'F'
            THEN 'Fortnight'
        WHEN pmlc_freq = 'K'
            THEN '4 weeks'
    END      as frequency
  , pmla_amt as yearly_amount
  , CASE
        WHEN pmlc_freq = 'M'
            THEN ROUND(pmla_amt/12,2)
        WHEN pmlc_freq = 'W'
            THEN ROUND(pmla_amt/52,2)
        WHEN pmlc_freq = 'Y'
            THEN ROUND(pmla_amt,2)
        WHEN pmlc_freq = 'Q'
            THEN ROUND(pmla_amt/4,2)
        WHEN pmlc_freq = 'F'
            THEN ROUND(pmla_amt/26,2)
        WHEN pmlc_freq = 'K'
            THEN ROUND(pmla_amt/13,2)
    END                     as amount
  , pmle_status_description as comment
FROM
    pmle_lease a
    LEFT JOIN
        pmlc_l_charge b
        ON
            a.pmle_prop       = b.pmlc_prop
            AND a.pmle_lease  = b.pmlc_lease
            AND pmlc_chg_type = 'R'
            AND
            (
                b.pmlc_stop       = 0
                OR b.pmlc_stop_dt > '2018-03-31'
            )
    LEFT JOIN
        pmla_l_c_amt c
        ON
            b.pmlc_lease         = c.pmla_lease
            AND b.pmlc_prop      = c.pmla_prop
            AND b.pmlc_serial    = c.pmla_serial
            AND c.pmla_start_dt <= '2018-03-31'
            AND c.pmla_end_dt   >= '2018-03-31'
    JOIN
        pmpr_property d
        ON
            a.pmle_prop = d.pmpr_prop
    JOIN
        pmzz_param e
        ON
            d.pmpr_prop_group   = e.pmzz_code
            AND e.pmzz_par_type = 'PROPGROUP'
WHERE
    pmle_exp_dt     >= '2018-03-01'
    AND pmle_exp_dt <= '2018-03-31'
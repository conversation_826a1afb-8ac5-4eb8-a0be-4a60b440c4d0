SELECT
    CASE
        WHEN account_type = 'I'
            THEN 'Income'
            ELSE 'Expense'
    END as type_name
  , district_code
  , district
  , account_code
  , account_description
  , SUM
        (
            CASE
                WHEN start_date  >= '2018-01-01'
                    AND end_date <= '2018-04-30'
                    THEN cash_balance
                    ELSE 0
            END
        )
    as cash_actual
  , SUM
        (
            CASE
                WHEN start_date  >= '2017-12-01'
                    AND end_date <= '2018-04-30'
                    THEN cash_budget
                    ELSE 0
            END
        )
    as cash_budget
  , SUM
        (
            CASE
                WHEN year       = b.pmcp_year
                    AND period <= b.pmcp_period
                    THEN cash_balance
                    ELSE 0
            END
        )
    as cash_ytd_actual
  , SUM
        (
            CASE
                WHEN year       = b.pmcp_year
                    AND period <= b.pmcp_period
                    THEN cash_budget
                    ELSE 0
            END
        )
    as cash_ytd_budget
  , SUM
        (
            CASE
                WHEN start_date  >= '2018-01-01'
                    AND end_date <= '2018-04-30'
                    THEN accruals_balance
                    ELSE 0
            END
        )
    as accruals_actual
  , SUM
        (
            CASE
                WHEN start_date  >= '2017-12-01'
                    AND end_date <= '2018-04-30'
                    THEN accruals_budget
                    ELSE 0
            END
        )
    as accruals_budget
  , SUM
        (
            CASE
                WHEN year       = b.pmcp_year
                    AND period <= b.pmcp_period
                    THEN accruals_balance
                    ELSE 0
            END
        )
    as accruals_ytd_actual
  , SUM
        (
            CASE
                WHEN year       = b.pmcp_year
                    AND period <= b.pmcp_period
                    THEN accruals_budget
                    ELSE 0
            END
        )
    as accruals_ytd_budget
FROM
    GL_Property_Summary_per_period a
    JOIN
        pmcp_prop_cal b
        ON
            a.property_code    = b.pmcp_prop
            AND pmcp_start_dt <= '2018-04-30'
            AND pmcp_end_dt   >= '2018-04-30'

WHERE
    account_group2 IN ('INC.OWN'
                     , 'EXP.OWN')

GROUP BY
    account_type
  , district_code
  , district
  , account_code
  , account_description
ORDER BY
    account_type DESC
  , district
  , account_code
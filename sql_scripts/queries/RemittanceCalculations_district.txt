/* for income and expense*/
SELECT
      CASE
            WHEN account_type = 'I'
                THEN 'Income'
            WHEN account_type = 'E'
                THEN 'Expense'
    		ELSE 'Balance'
        END as type_name
  , district_code
  , district
  , account_group2
  , SUM
        (
            CASE
                WHEN start_date  >= '2018-01-01'
                    AND end_date <= '2018-04-30'
                    THEN cash_balance
                    ELSE 0
            END
        )
    as month_total
  , SUM
        (
            CASE
                WHEN year       = b.pmcp_year
                    AND period <= b.pmcp_period
                    THEN cash_balance
                    ELSE 0
            END
        )
    as ytd_total,
  	(
  SELECT
  sum(x.cash_balance)
  FROM
  GL_Property_Summary_per_period x
  WHERE
  x.account_group2         = a.account_group2
  AND x.district_code        = a.district_code
  AND x.start_date < '2018-04-01'
  )
      as opening
FROM
    GL_Property_Summary_per_period a
    JOIN
        pmcp_prop_cal b
        ON
            a.property_code    = b.pmcp_prop
            AND pmcp_start_dt <= '2018-04-30'
            AND pmcp_end_dt   >= '2018-04-30'

LEFT JOIN gl_linked_accounts c
	ON a.account_code = c.gstOutputTax
WHERE
    account_group2 IN ('INC.OWN', 'EXP.OWN') OR a.account_code = c.gstOutputTax

GROUP BY
    account_type
  , district_code
  , district
  , account_group2
ORDER BY
    account_type DESC
  , district
;


 /*for GST received and paid*/
SELECT
    CASE
        WHEN a.accountID = gstOutputTax
            THEN 'GST received'
            ELSE 'GST paid'
    END as label
  , a.accountID
  , SUM
        (
            CASE
                WHEN a.year      = b.pmcp_year
                    AND a.period = b.pmcp_period
                    THEN balanceCash
                    ELSE 0
            END
        )
    as month_amount
  , SUM
        (
            CASE
                WHEN a.year       = b.pmcp_year
                    AND a.period <= b.pmcp_period
                    THEN balanceCash
                    ELSE 0
            END
        )
    as ytd_amount
FROM
    gl_trial_balance a
    JOIN
        pmcp_prop_cal b
        ON
            a.propertyID       = b.pmcp_prop
            AND pmcp_start_dt <= '2018-04-30'
            AND pmcp_end_dt   >= '2018-04-30'
    JOIN
        gl_linked_accounts c
        ON
            a.accountID    = gstOutputTax
            OR a.accountID = gstInputTax
GROUP BY
    a.accountID
  , gstInputTax
  , gstOutputTax
;


 /* for remittance per day*/
SELECT
    transaction_date
  , SUM(transaction_amount)
FROM
    gl_transaction a
    JOIN
        pmca_chart b
        ON
            a.account_id = b.pmca_code
WHERE
    b.pmca_gl_account_group2 = 'EXP.DIS'
    AND a.transaction_date  >= '2017-08-01'
    AND a.transaction_date  <= '2017-08-31'
    AND method               = 2
GROUP BY
    transaction_date
;

 /* for remittance ytd*/
SELECT
    SUM(transaction_amount)
FROM
    gl_transaction a
    JOIN
        pmca_chart b
        ON
            a.account_id = b.pmca_code
    JOIN
        pmcp_prop_cal c
        ON
            a.property_id        = c.pmcp_prop
            AND c.pmcp_start_dt <= '2017-08-31'
            AND c.pmcp_end_dt   >= '2017-08-31'
            AND a.year           = c.pmcp_year
            AND a.period        <= c.pmcp_period
WHERE
    b.pmca_gl_account_group2 = 'EXP.DIS'
    AND method               = 2
;

USE BR_WA_PTA_Import;
SELECT
    g.pmzz_desc        as district
  , e.pmzz_desc        as shire
  , d.pmpr_city        as location
  , ''                 as purpose
  , SUM(f.pmua_area)   as area
  , SUM(pmla_amt)      as yearly_amount
  , SUM(pmla_amt) * 10 as land_value
from
    pmle_lease a
    JOIN
        pmlc_l_charge b
        ON
            a.pmle_prop       = b.pmlc_prop
            AND a.pmle_lease  = b.pmlc_lease
            AND pmlc_chg_type = 'R'
    JOIN
        pmla_l_c_amt c
        ON
            b.pmlc_lease         = c.pmla_lease
            AND b.pmlc_prop      = c.pmla_prop
            AND b.pmlc_serial    = c.pmla_serial
            AND c.pmla_start_dt <= '2018-03-31'
            AND c.pmla_end_dt   >= '2018-03-31'
    JOIN
        pmpr_property d
        ON
            a.pmle_prop = d.pmpr_prop
    JOIN
        pmzz_param e
        ON
            d.pmpr_prop_group   = e.pmzz_code
            AND e.pmzz_par_type = 'PROPGROUP'
    JOIN
        pmua_unit_area f
        ON
            f.pmua_prop         = d.pmpr_prop
            AND f.pmua_lease    = a.pmle_lease
            AND f.pmua_from_dt <= '2018-03-31'
            AND f.pmua_to_dt   >= '2018-03-31'
    JOIN
        pmzz_param g
        ON
            d.pmpr_prop_group   = g.pmzz_code
            AND g.pmzz_par_type = 'PROPERTY'
WHERE
    b.pmlc_stop        = 0
    OR b.pmlc_stop_dt >= '2018-03-01'
GROUP BY
    e.pmzz_desc
  , g.pmzz_desc
  , d.pmpr_city
;
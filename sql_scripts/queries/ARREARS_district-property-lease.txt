SELECT
    district
  , property_code
  , property_address as suburb
  , lease_code
  , lease_name
  , (
        SELECT
            SUM((x.charges + x.charges_gst) - (x.receipts + x.receipts_gst))
        FROM
            GL_Lease_Summary_per_day x
        WHERE
            x.lease_code           = a.lease_code
            AND x.district_code        = a.district_code
            AND x.property_code    = a.property_code
            AND x.transaction_date < '2018-04-01'
    )
    as opening
  , SUM
        (
            CASE
                WHEN transaction_date    >= '2018-04-01'
                    AND transaction_date <= '2018-04-30'
                    THEN charges
                    ELSE 0
            END
        )
    as charges
  , SUM
        (
            CASE
                WHEN transaction_date    >= '2018-04-01'
                    AND transaction_date <= '2018-04-30'
                    THEN charges_gst
                    ELSE 0
            END
        )
    as charges_gst
  , SUM
        (
            CASE
                WHEN transaction_date    >= '2018-04-01'
                    AND transaction_date <= '2018-04-30'
                    THEN receipts
                    ELSE 0
            END
        )
    as receipts
  , SUM
        (
            CASE
                WHEN transaction_date    >= '2018-04-01'
                    AND transaction_date <= '2018-04-30'
                    THEN receipts_gst
                    ELSE 0
            END
        )
    as receipts_gst
  , (
        SELECT
            SUM((x.charges + x.charges_gst) - (x.receipts + x.receipts_gst))
        FROM
            GL_Lease_Summary_per_day x
        WHERE
            x.lease_code            = a.lease_code
            AND x.district_code        = a.district_code
            AND x.property_code     = a.property_code
            AND x.transaction_date <= '2018-04-30'
    )
    as closing
FROM
    GL_Lease_Summary_per_day a
GROUP BY
    district_code
  , district
  , property_code
  , property_address
  , lease_code
  , lease_name
;
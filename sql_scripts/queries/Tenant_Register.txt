SELECT
    c.pmzz_desc        AS district
  , pmle_lease         AS lease_code
  , pmle_name          AS lease_name
  , pmle_com_dt        AS start_date
  , pmle_exp_dt        AS expiry_date
  , pmle_description   AS premises
  , d.pmzz_desc        AS lease_type
  , e.pmzz_desc        AS tenant_type
  , pmle_t_name        AS tenant_name
  , f.categoryName     AS retail_category
  , g.subCategoryName  AS retail_sub_category
  , h.fineCategoryName AS retail_fine_category

FROM
    pmle_lease a
    JOIN
        pmpr_property b
        ON
            a.pmle_prop = b.pmpr_prop
    JOIN
        pmzz_param c
        ON
            b.pmpr_prop_group   = c.pmzz_code
            AND c.pmzz_par_type = 'PROPERTY'
    JOIN
        pmzz_param d
        ON
            a.pmle_l_type       = d.pmzz_code
            AND d.pmzz_par_type = 'LEASE'
    JOIN
        pmzz_param e
        ON
            a.pmle_ten_type     = e.pmzz_code
            AND e.pmzz_par_type = 'TENANT'
    LEFT JOIN
        retail_category f
        ON
            f.categoryID = a.pmle_retail_cat
    LEFT JOIN
        retail_sub_category g
        ON
            g.subCategoryID = a.pmle_retail_sub_cat
    LEFT JOIN
        retail_fine_category h
        ON
            h.fineCategoryID = a.pmle_retail_fine_cat
WHERE
    (
        pmle_status = 'C'
    )
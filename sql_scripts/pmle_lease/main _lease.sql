ALTER TABLE pmle_lease
  ADD pmle_main_lease bit not NULL DEFAULT 0

ALTER TABLE temp_pmle_lease
  ADD pmle_main_lease bit not NULL DEFAULT 0



UPDATE  table1
SET     pmle_main_lease = 1
from
(SELECT * FROM
(

select pmle_prop , pmle_debtor ,pmle_lease ,  pmle_main_lease , pmle_status , ROW_NUMBER() OVER(PARTITION BY pmle_prop , pmle_debtor ORDER BY pmle_prop DESC) AS "RowNumber"
from pmle_lease where pmle_status = 'C'
 )
T where RowNumber = 1) as table1
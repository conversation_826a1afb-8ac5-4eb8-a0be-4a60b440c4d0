-- for all clients
DROP VIEW agency_fees
CREATE VIEW agency_fees
    AS
    SELECT
        pmxc_ap_alloc.pmxc_prop as propertyID,
        -- pmxc_ap_alloc.pmxc_year as calYear,
        -- pmxc_ap_alloc.pmxc_period as calPeriod,
		pmcm_mast_cal.pmcm_year as calYear,
		pmcm_mast_cal.pmcm_period as calPeriod ,
        SUM((ISNULL(pmxc_ap_alloc.pmxc_alloc_amt,0) * -1) / 1.1)  as netAmt,
        ISNULL(pmca_chart.pmca_fees,0) as fees,
        pmca_chart.pmca_code AS feesCode,
        pmca_chart.pmca_name AS feesName

    FROM pmxc_ap_alloc pmxc_ap_alloc
    LEFT JOIN pmco_company pmco_company ON
        pmxc_ap_alloc.pmxc_s_creditor = pmco_company.pmco_code
    LEFT JOIN pmca_chart pmca_chart ON
        pmxc_ap_alloc.pmxc_acc = pmca_chart.pmca_code
	LEFT JOIN pmcm_mast_cal ON
		pmxc_alloc_dt BETWEEN pmcm_mast_cal.pmcm_r_start_dt AND pmcm_mast_cal.pmcm_end_dt
		AND pmcm_mast_cal.pmcm_code = 'Financial'
    WHERE (
            (pmxc_ap_alloc.pmxc_f_type='PAY')
             AND (pmco_company.pmco_s_agent=1)
        )
    GROUP BY
        pmxc_ap_alloc.pmxc_prop,
        pmcm_mast_cal.pmcm_year,
		pmcm_mast_cal.pmcm_period,
        pmca_chart.pmca_fees,
        pmca_chart.pmca_code,
        pmca_chart.pmca_name

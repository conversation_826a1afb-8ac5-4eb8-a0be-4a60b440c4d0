/**
*
* test: APG_Test. Tables already existing in APG_Test in Manila Server only
*
**/
DROP TABLE ocr_ap_transactions;

CREATE TABLE ocr_ap_transactions
(
	id INT IDENTITY(1,1),
	supplier_code VARCHAR(100),
	property_code VARCHAR(100),
	supplier_allocated_account_code VARCHAR(100),
	trans_date DATE,
	bpay_biller_code VARCHAR(50),
	bpay_crn VARCHAR(50),
	bsb VARCHAR(50),
	bank_account VARCHAR(50),
	tax_code VARCHAR(10),
	gross_amount NUMERIC(19,2) NOT NULL DEFAULT (0),
	tax_amount NUMERIC(19,2) NOT NULL DEFAULT (0),
	net_amount NUMERIC(19,2) NOT NULL DEFAULT (0),
	created_at DATETIME NULL,
	temp_ocr_ap_pm_id INT,
	batch_nr VARCHAR(50),
	batch_line_nr INT
);

/** create statement above was updated with the new columns 08-14-2018 **/

/**
08-14-2018: Add new script to alter existing ocr_ap_transactions
 */

/* 1) */ ALTER TABLE ocr_ap_transactions ADD temp_ocr_ap_pm_id INT;
/* 2) */ ALTER TABLE ocr_ap_transactions ADD batch_nr VARCHAR(50);
/* 3) */ ALTER TABLE ocr_ap_transactions ADD batch_line_nr INT;
/**
*
* test: APG. Tables already existing in APG in Manila Server only [16092019]
*
**/
DROP TABLE ai_audit_trail;

CREATE TABLE ai_audit_trail
(
	id INT IDENTITY(1,1),
	temp_ocr_ap_id INT,
	fc_batch_id VARCHAR(100),
	page INT,
	action_type INT,
	header_values NVARCHAR(MAX),
	ap_line_values NVARCHAR(MAX),
	ar_line_values NVARCHAR(MAX),
	user_email VARCHAR(100),
	created_at DATETIME NULL,
	invoice_number VARCHAR(100) DEFAULT NULL,
	supplier_code VARCHAR(100) DEFAULT NULL,
);

/** 03/10/2019 - Adjustment for ai_audit_trail - add new columns for invoice_number and supplier_code */
ALTER TABLE ai_audit_trail ADD invoice_number VARCHAR(100) DEFAULT NULL;
ALTER TABLE ai_audit_trail ADD supplier_code VARCHAR(100) DEFAULT NULL;

/** Adjustment for temp_ocr_ar_ai and temp_ocr_ar_pm tables: add manual_allocation_type 1 - amount OR 2 - percentage share */
ALTER TABLE temp_ocr_ar_ai ADD manual_allocation_type INT NOT NULL DEFAULT (0);
ALTER TABLE temp_ocr_ar_pm ADD manual_allocation_type INT NOT NULL DEFAULT (0);

/** 15/10/2019 - Adjustment for AI AR header and line tables - START (currently in APG manila only but run this SQL scripts first on dev and live) */
/** For temp_ocr_ar_ai **/
ALTER TABLE temp_ocr_ar_ai ADD has_man_fees INT NOT NULL DEFAULT (0);
ALTER TABLE temp_ocr_ar_ai ADD man_fees_acct_code VARCHAR(50) DEFAULT NULL;
ALTER TABLE temp_ocr_ar_ai ADD man_fees_acct_desc VARCHAR(200) DEFAULT NULL;
ALTER TABLE temp_ocr_ar_ai ADD man_fees_percentage DECIMAL(19, 2) DEFAULT NULL;
/** For temp_ocr_ar_line_ai **/
ALTER TABLE temp_ocr_ar_line_ai ADD has_man_fees INT NOT NULL DEFAULT (0);
ALTER TABLE temp_ocr_ar_line_ai ADD man_fees_acct_code VARCHAR(50) DEFAULT NULL;
ALTER TABLE temp_ocr_ar_line_ai ADD man_fees_acct_desc VARCHAR(200) DEFAULT NULL;
ALTER TABLE temp_ocr_ar_line_ai ADD man_fees_percentage DECIMAL(19, 2) DEFAULT NULL;
/** For temp_ocr_ar_pm **/
ALTER TABLE temp_ocr_ar_pm ADD has_man_fees INT NOT NULL DEFAULT (0);
ALTER TABLE temp_ocr_ar_pm ADD man_fees_acct_code VARCHAR(50) DEFAULT NULL;
ALTER TABLE temp_ocr_ar_pm ADD man_fees_acct_desc VARCHAR(200) DEFAULT NULL;
ALTER TABLE temp_ocr_ar_pm ADD man_fees_percentage DECIMAL(19, 2) DEFAULT NULL;
/** For temp_ocr_ar_line_pm **/
ALTER TABLE temp_ocr_ar_line_pm ADD has_man_fees INT NOT NULL DEFAULT (0);
ALTER TABLE temp_ocr_ar_line_pm ADD man_fees_acct_code VARCHAR(50) DEFAULT NULL;
ALTER TABLE temp_ocr_ar_line_pm ADD man_fees_acct_desc VARCHAR(200) DEFAULT NULL;
ALTER TABLE temp_ocr_ar_line_pm ADD man_fees_percentage DECIMAL(19, 2) DEFAULT NULL;
/** 15/10/2019 - Adjustment for AI AR header and line tables - END */

/**
*
* Run this on dev and live
* for temp_ocr_ap_line_ai and temp_ocr_ap_line_pm ADD ap_batch_nr and ap_line_no
* for temp_ocr_ar_line_ai and temp_ocr_ar_line_pm ADD ar_batch_nr and ar_line_no
* for new column for_owner_reimbursement to handle flag
*
**/

/** Step 1) **/
ALTER TABLE temp_ocr_ap_line_ai ADD ap_batch_nr VARCHAR(100) NULL DEFAULT NULL;
/** Step 2) **/
ALTER TABLE temp_ocr_ap_line_ai ADD ap_line_no INT NULL DEFAULT NULL;

/** Step 3) **/
ALTER TABLE temp_ocr_ap_line_pm ADD ap_batch_nr VARCHAR(100) NULL DEFAULT NULL;
/** Step 4) **/
ALTER TABLE temp_ocr_ap_line_pm ADD ap_line_no INT NULL DEFAULT NULL;

/** Step 5) **/
ALTER TABLE temp_ocr_ar_line_ai ADD ar_batch_nr VARCHAR(100) NULL DEFAULT NULL;
/** Step 6) **/
ALTER TABLE temp_ocr_ar_line_ai ADD ar_line_no INT NULL DEFAULT NULL;

/** Step 7) **/
ALTER TABLE temp_ocr_ar_line_pm ADD ar_batch_nr VARCHAR(100) NULL DEFAULT NULL;
/** Step 8) **/
ALTER TABLE temp_ocr_ar_line_pm ADD ar_line_no INT NULL DEFAULT NULL;

/** ADD THE FF TO HANDLE SINGLE AR LINE ITEMS **/
/** Step 9) **/
ALTER TABLE temp_ocr_ar_ai ADD ar_batch_nr VARCHAR(100) NULL DEFAULT NULL;
/** Step 10) **/
ALTER TABLE temp_ocr_ar_ai ADD ar_line_no INT NULL DEFAULT NULL;
/** Step 11) **/
ALTER TABLE temp_ocr_ar_pm ADD ar_batch_nr VARCHAR(100) NULL DEFAULT NULL;
/** Step 12) **/
ALTER TABLE temp_ocr_ar_pm ADD ar_line_no INT NULL DEFAULT NULL;


/**
*
* Run this on dev and live
* for temp_ocr_ap_ai and temp_ocr_ap_pm
* for new column for_owner_reimbursement to handle flag
*
**/

/** Step 1) Run hold_ap alter query on temp_ocr_ap_ai then do step 2 **/
ALTER TABLE temp_ocr_ap_line_ai ADD hold_ap TINYINT NOT NULL DEFAULT 0;

/** Step 2) Run for_owner_reimbursement alter query on temp_ocr_ap_ai **/
ALTER TABLE temp_ocr_ap_line_ai ADD for_owner_reimbursement TINYINT NOT NULL DEFAULT 0;

/** OPTIONAL for those client DB with missing hold_ap columns in temp_ocr_line_pm; **/
ALTER TABLE temp_ocr_ap_line_pm ADD hold_ap TINYINT NOT NULL DEFAULT 0;

/** Step 3) **/
ALTER TABLE temp_ocr_ap_line_pm ADD for_owner_reimbursement TINYINT NOT NULL DEFAULT 0;


/**
*
* test: APG_Test and BR_WA_test. Tables already existing in APG_Test and BR_WA_test in Manila Server
*
**/
DROP TABLE temp_ocr_ap_pm;
DROP TABLE temp_ocr_ap_line_pm;

CREATE TABLE temp_ocr_ap_pm
(
	id INT IDENTITY(1,1),
	temp_ocr_ap_ai_id INT,
	supplier_code VARCHAR(50),
	supplier_abn VARCHAR(50),
	invoice_no VARCHAR(50),
	order_no VARCHAR(50),
	invoice_date DATE,
	due_date DATE,
	bpay_reference VARCHAR(50),
	bpay_crn VARCHAR(50),
	payment_bsb VARCHAR(50),
	payment_account VARCHAR(50),
	status TINYINT NOT NULL DEFAULT (0),
	created_by VARCHAR(100),
	created_at DATETIME NULL,
	attach_to_ar TINYINT NOT NULL DEFAULT (0),
	attach_to_owner TINYINT NOT NULL DEFAULT (0),
	modified_by VARCHAR(100) NULL DEFAULT NULL,
	modified_at DATETIME NULL DEFAULT NULL,
	for_owner_reimbursement TINYINT NOT NULL DEFAULT (0),
	pm_check_payment_details TINYINT NOT NULL DEFAULT (0)
);

CREATE TABLE temp_ocr_ap_line_pm
(
	id INT IDENTITY(1,1),
	temp_ocr_ap_pm_id INT,
	line_no INT,
	property_code VARCHAR(50),
	trans_type VARCHAR(10),
	account_code VARCHAR(50),
	description VARCHAR(255),
	from_date DATE,
	to_date DATE,
	due_date DATE,
	tax_code VARCHAR(10),
	gross_amount NUMERIC(19,2),
	tax_amount NUMERIC(19,2),
	net_amount NUMERIC(19,2),
	for_owner_reimbursement TINYINT NOT NULL DEFAULT (0),
  ap_batch_nr VARCHAR(100) NULL DEFAULT NULL,
  ap_line_no INT NULL DEFAULT NULL
);
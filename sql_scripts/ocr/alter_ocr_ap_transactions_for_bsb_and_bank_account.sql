/**
*
* test: APG_Test. Alter existing table ocr_ap_transactions to add bsb and bank account columns
*
**/

ALTER TABLE ocr_ap_transactions ADD bsb VARCHAR(50) NULL;
ALTER TABLE ocr_ap_transactions ADD bank_account VARCHAR(50) NULL;

DROP TABLE ocr_ap_transactions;

CREATE TABLE ocr_ap_transactions
(
	id INT IDENTITY(1,1),
	supplier_code VARCHAR(100),
	property_code VARCHAR(100),
	supplier_allocated_account_code VARCHAR(100),
	trans_date DATE,
	bpay_biller_code VARCHAR(50),
	bpay_crn VARCHAR(50),
	bsb VARCHAR(50),
	bank_account VARCHAR(50),
	tax_code VARCHAR(10),
	gross_amount NUMERIC(19,2) NOT NULL DEFAULT (0),
	tax_amount NUMERIC(19,2) NOT NULL DEFAULT (0),
	net_amount NUMERIC(19,2) NOT NULL DEFAULT (0),
	created_at DATETIME NULL
);
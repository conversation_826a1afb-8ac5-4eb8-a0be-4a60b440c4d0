/**
*
* Run this on dev and live
* for single: temp_ocr_ar_ai - temp_ocr_ar_pm and multiple: temp_ocr_ar_line_ai - temp_ocr_ar_line_pm
* for new column ai_interim to handle flag for ar entries with interim invoice
*
**/

/** ALTER TABLE ar_transaction ADD ai_interim TINYINT DEFAULT NULL; **/

/** Step 1) **/
ALTER TABLE temp_ocr_ar_ai ADD ai_interim TINYINT NOT NULL DEFAULT 0;

/** Step 2) **/
ALTER TABLE temp_ocr_ar_line_ai ADD ai_interim TINYINT NOT NULL DEFAULT 0;

/** Step 3) **/
ALTER TABLE temp_ocr_ar_pm ADD ai_interim TINYINT NOT NULL DEFAULT 0;

/** Step 4) **/
ALTER TABLE temp_ocr_ar_line_pm ADD ai_interim TINYINT NOT NULL DEFAULT 0;

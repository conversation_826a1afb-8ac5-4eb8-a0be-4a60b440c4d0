/**
*
* test: BR_WA_Test. Tables already existing in BR_WA_Test in Manila Server
*
**/
DROP TABLE temp_ocr_ar_ai;
DROP TABLE temp_ocr_ar_line_ai;

CREATE TABLE temp_ocr_ar_ai
(
id INT IDENTITY(1,1),
temp_ocr_ap_id INT,
ap_line_no INT,
entry_type INT,
property_code VARCHAR(50),
lease_code VARCHAR(50),
bank_account_code VARCHAR(50),
debtor_code VARCHAR(50),
transaction_date DATE,
due_date DATE,
transaction_type VARCHAR(50),
account_code VARCHAR(50),
description VARCHAR(255),
from_date DATE,
to_date DATE,
tax_code VARCHAR(50),
gross_amount NUMERIC(19, 2),
tax_amount NUMERIC(19, 2),
net_amount NUMERIC(19, 2),
percentage_method INT,
process_status TINYINT NOT NULL DEFAULT (0)
);

CREATE TABLE temp_ocr_ar_line_ai
(
id INT IDENTITY(1,1),
temp_ocr_ar_ai_id INT,
line_no INT,
lease_code VARCHAR(50),
lease_type VARCHAR(50),
unit_id VARCHAR(50),
unit_desc VARCHAR(255),
lease_from_date DATE,
lease_to_date DATE,
days_charged INT,
unit_area NUMERIC(19, 2),
share_percentage NUMERIC(19, 2),
share_amount NUMERIC(19, 2),
ar_batch_nr VARCHAR(100) NULL DEFAULT NULL,
ar_line_no INT NULL DEFAULT NULL
);
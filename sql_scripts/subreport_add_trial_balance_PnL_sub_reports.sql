INSERT [dbo].[sub_report] ([reportID], [subReportName], [subReportFile], [dependency]) VALUES (1, N'Trial Balance Cash', N'trialBalanceCashSubReport.php', 0);
INSERT [dbo].[sub_report] ([reportID], [subReportName], [subReportFile], [dependency]) VALUES (1, N'Trial Balance Accruals', N'trialBalanceAccrualsSubReport.php', 0);
INSERT [dbo].[sub_report] ([reportID], [subReportName], [subReportFile], [dependency]) VALUES (1, N'Profit and Loss Cash - Selected and Previous 3 Months', N'profitAndLossCash3MonthsSubReport.php', 0);
INSERT [dbo].[sub_report] ([reportID], [subReportName], [subReportFile], [dependency]) VALUES (1, N'Profit and Loss Cash - per Month', N'profitAndLossCashPerMonthSubReport.php', 0);
INSERT [dbo].[sub_report] ([reportID], [subReportName], [subReportFile], [dependency]) VALUES (1, N'Profit and Loss Cash - Actual VS Budget', N'profitAndLossCashActualVsBudgetSubReport.php', 0);
INSERT [dbo].[sub_report] ([reportID], [subReportName], [subReportFile], [dependency]) VALUES (1, N'Profit and Loss Cash - Actual VS Prior Year', N'profitAndLossCashActualVsPriorYearSubReport.php', 0);
INSERT [dbo].[sub_report] ([reportID], [subReportName], [subReportFile], [dependency]) VALUES (1, N'Profit and Loss Accruals - Selected and Previous 3 Months', N'profitAndLossAccruals3MonthsSubReport.php', 0);
INSERT [dbo].[sub_report] ([reportID], [subReportName], [subReportFile], [dependency]) VALUES (1, N'Profit and Loss Accruals - per Month', N'profitAndLossAccrualsPerMonthSubReport.php', 0);
INSERT [dbo].[sub_report] ([reportID], [subReportName], [subReportFile], [dependency]) VALUES (1, N'Profit and Loss Accruals - Actual VS Budget', N'profitAndLossAccrualsActualVsBudgetSubReport.php', 0);
INSERT [dbo].[sub_report] ([reportID], [subReportName], [subReportFile], [dependency]) VALUES (1, N'Profit and Loss Accruals - Actual VS Prior Year', N'profitAndLossAccrualsActualVsPriorYearSubReport.php', 0);

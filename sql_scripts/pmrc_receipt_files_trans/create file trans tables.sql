CREATE TABLE [dbo].[pmrc_receipt_files_trans](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[file_code] [varchar](50) NULL,
	[pattern] [varchar](max) NOT NULL,
	[amount] [decimal](19, 2) NOT NULL,
	[trans_date] [date] NULL,
	[trans_type] [varchar](50) NULL,
	[trans_type_name] [varchar](50) NULL,
	[debtor_code] [varchar](50) NOT NULL,
	[lease_code] [varchar](50) NOT NULL,
	[property_code] [varchar](50) NOT NULL,
	[crn] [varchar](50) NOT NULL,
	[receipt_id] [int] NULL,
	[receipt_user] [varchar](50) NULL,
	[receipted_at] [datetime] NULL,
	[adjustment_id] VARCHAR(50),
  	[adjustment_user] VARCHAR(50),
  	[adjustment_at] DATETIME,
  	[bank_code] VARCHAR(50),
  	[bsb_no] VARCHAR(100),
  	[bank_account_no] VARCHAR(100),
	[create_user] [varchar](50) NOT NULL,
	[created_at] [datetime] NOT NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY];

CREATE TABLE [dbo].[pmrc_receipt_files_trans_amounts](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[total_amount] [decimal](19, 2) NOT NULL,
	[amount] [decimal](19, 2) NOT NULL,
	[debtor_code] [varchar](50) NOT NULL,
	[lease_code] [varchar](50) NOT NULL,
	[property_code] [varchar](50) NOT NULL,
	[account_code] [varchar](50) NOT NULL,
	[receipt_id] [int] NULL,
	[create_user] [varchar](50) NOT NULL,
	[created_at] [datetime] NOT NULL
) ON [PRIMARY];
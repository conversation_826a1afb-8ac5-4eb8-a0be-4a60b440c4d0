
CREATE TABLE [dbo].[temp_prmf_fees] (
    [prmf_id] INT NOT NULL IDENTITY PRIMARY KEY,
    [prmf_account] VARCHAR(10),
    [prmf_method] NUMERIC(1),
    [prmf_amount] DECIMAL(19, 2),
    [prmf_percentage] NUMERIC(8, 3),
    [prmf_start_period] DATE,
    [prmf_end_period] DATE,
    [prmf_prop] VARCHAR(10)
    );

CREATE TABLE [dbo].[temp_pmpk_p_key] (
    [pmpk_id] INT NOT NULL IDENTITY PRIMARY KEY,
    [pmpk_prop] VARCHAR(10),
    [pmpk_key] VARCHAR(10),
    [pmpk_desc] VARCHAR(10),
    [pmpk_status] VARCHAR(10),
    [pmpk_deleted_at] DATE,
    [pmpk_detail_id] INT,
    [user_created] INT,
    [date_created] datetime2,
    [user_updated] INT,
    [date_updated] datetime2,
    [reasonDeleted] TEXT,
    [reasonActivate] TEXT
    );

CREATE TABLE [dbo].[temp_pmpfa_p_fund_acc] (
    [pmpfa_partition_id] VARCHAR(10),
    [pmpfa_property] VARCHAR(10),
    [pmpfa_account_code] VARCHAR(10)
    );

CREATE TABLE [dbo].[temp_pmpf_p_fund] (
    [pmpf_partition_id] VARCHAR(10),
    [pmpf_property] VARCHAR(10),
    [pmpf_fund_name] VARCHAR(10)
    );


CREATE TABLE [dbo].[temp_pmkd_key_detail] (
    [pmkd_id] INT NOT NULL IDENTITY PRIMARY KEY,
    [pmkd_prop] VARCHAR(10),
    [pmkd_key] VARCHAR(20),
    [pmkd_key_taker] VARCHAR(60),
    [pmkd_checked_out] DATE,
    [pmkd_return_due] DATE,
    [pmkd_date_returned] DATE,
    [pmkd_key_filename_out] VARCHAR(255),
    [pmkd_key_filename_in] VARCHAR(255),
    [user_created] INT,
    [date_created] DATETIME2,
    [user_updated] INT,
    [date_updated] datetime2,
    [pmkd_comp] VARCHAR(10),
    [pmkd_checked_out_notes] TEXT,
    [pmkd_checked_in_notes] TEXT,
    [old_pmkd_id] INT,
    [pmkd_in_document_id] INT,
    [pmkd_out_document_id] INT
    );

ALTER TABLE [dbo].[temp_pmrcf_p_fee_acc]
    ADD [pmrcf_desc] VARCHAR(40);

ALTER TABLE [dbo].[temp_pmrcf_p_fee_acc]
    ADD [pmrcf_prmf_id] INT;

ALTER TABLE [dbo].[temp_pmrcf_p_fee_acc]
    ADD [ID] INT NOT NULL IDENTITY;




CREATE TABLE [dbo].[temp_pmin_p_insurance] (
    [id] int  IDENTITY(1,1) NOT NULL,
    [created_at] datetime DEFAULT (getdate()) NULL,
    [pmin_type] varchar(60) COLLATE SQL_Latin1_General_CP1_CI_AS  NULL,
    [pmin_insurer] varchar(160) COLLATE SQL_Latin1_General_CP1_CI_AS  NULL,
    [pmin_premium_amount] decimal(18)  NULL,
    [pmin_policy_number] varchar(60) COLLATE SQL_Latin1_General_CP1_CI_AS  NULL,
    [pmin_expire_date] date  NULL,
    [pmin_paid_date] date  NULL,
    [pmin_note] text COLLATE SQL_Latin1_General_CP1_CI_AS  NULL,
    [pmin_file_link] varchar(500) COLLATE SQL_Latin1_General_CP1_CI_AS  NULL,
    [pmin_prop] varchar(60) COLLATE SQL_Latin1_General_CP1_CI_AS  NULL,
    [pmin_start_date] datetime  NULL,
    [pmin_publish_to_owner] bit DEFAULT ('FALSE') NULL,
    [pmin_document_id] int DEFAULT NULL NULL
    )


CREATE TABLE [dbo].[temp_pmpt_p_contact] (
    [pmpt_prop] char(10) COLLATE SQL_Latin1_General_CP1_CI_AS  NOT NULL,
    [pmpt_serial] numeric(12)  NOT NULL,
    [pmpt_name] char(40) COLLATE SQL_Latin1_General_CP1_CI_AS  NULL,
    [pmpt_create_dt] datetime  NULL,
    [pmpt_active] bit  NULL,
    [pmpt_type] varchar(20) COLLATE SQL_Latin1_General_CP1_CI_AS  NULL,
    [pmpt_primary] bit DEFAULT ((0)) NULL
    )

CREATE TABLE [dbo].[temp_pmpj_p_phone] (
    [pmpj_prop] char(10) COLLATE SQL_Latin1_General_CP1_CI_AS  NOT NULL,
    [pmpj_c_serial] numeric(12)  NOT NULL,
    [pmpj_serial] numeric(12)  NOT NULL,
    [pmpj_ph_code] char(10) COLLATE SQL_Latin1_General_CP1_CI_AS  NULL,
    [pmpj_phone_no] char(60) COLLATE SQL_Latin1_General_CP1_CI_AS  NULL,
    [pmpj_primary] bit  NULL,
    [ID] int  IDENTITY(1,1) NOT NULL
    )
ALTER TABLE [dbo].[temp_documents]
    ADD [include_owner_report] bit DEFAULT ((0)) NULL;

ALTER TABLE [dbo].[temp_documents]
    ADD [period_date] date  NULL;
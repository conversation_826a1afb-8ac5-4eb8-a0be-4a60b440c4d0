-- npms
CREATE TABLE country_defaults (
	id INT IDENTITY(1,1),
  country_code VARCHAR(4) NOT NULL,
  business_label VARCHAR(10) NOT NULL,
  business_length INT NOT NULL,
  display_state TINYINT NOT NULL DEFAULT 1,
  display_bsb TINYINT NOT NULL DEFAULT 1,
  post_code_length INT NOT NULL,
  bank_account_length INT NOT NULL,
  status TINYINT NOT NULL DEFAULT 0,
  created_at DATETIME NOT NULL DEFAULT (GETDATE()),
  updated_at DATETIME NOT NULL DEFAULT (GETDATE())
);

-- CLIENT
ALTER TABLE cms_system
ADD cms_sy_country VARCHAR(10) default NULL;
INSERT INTO [dbo].[report]
           ([reportID]
           ,[reportName]
           ,[reportType]
           ,[reportDescription]
           ,[reportPeriod]
           ,[reportParameters]
           ,[reportOrientation]
           ,[sequence])
     VALUES
           (35
           ,'CSV Report'
           ,23
           ,'CSV Report'
           ,'M'
           ,NULL
           ,1
           ,28);

INSERT INTO [dbo].[sub_report]
           ([reportID]
           ,[subReportName]
           ,[subReportFile]
           ,[dependency]
           ,[sequence])
     VALUES
           (35
           ,'GL Extract'
           ,'glExtract.php'
           ,0
           ,NULL);

INSERT INTO [dbo].[sub_report]
           ([reportID]
           ,[subReportName]
           ,[subReportFile]
           ,[dependency]
           ,[sequence])
     VALUES
           (35
           ,'Suite Level Income'
           ,'APNSuiteLevelIncomeAccruals.php'
           ,0
           ,NULL);

INSERT INTO [dbo].[sub_report]
           ([reportID]
           ,[subReportName]
           ,[subReportFile]
           ,[dependency]
           ,[sequence])
     VALUES
           (35
           ,'Profit and Loss Extract'
           ,'APNProfitAndLossExtract.php'
           ,0
           ,NULL);

INSERT INTO [dbo].[sub_report]
           ([reportID]
           ,[subReportName]
           ,[subReportFile]
           ,[dependency]
           ,[sequence])
     VALUES
           (35
           ,'Tenant Balances *All Periods'
           ,'APNTenantBalances.php'
           ,0
           ,NULL);

INSERT INTO [dbo].[sub_report]
           ([reportID]
           ,[subReportName]
           ,[subReportFile]
           ,[dependency]
           ,[sequence])
     VALUES
           (35
           ,'Property Budget vs Actuals'
           ,'APNPropertyBudgetVsActuals.php'
           ,0
           ,NULL);

INSERT INTO [dbo].[sub_report]
           ([reportID]
           ,[subReportName]
           ,[subReportFile]
           ,[dependency]
           ,[sequence])
     VALUES
           (35
           ,'Tenancy Schedule'
           ,'APNTenancySchedule.php'
           ,0
           ,NULL);

INSERT INTO [dbo].[sub_report]
           ([reportID]
           ,[subReportName]
           ,[subReportFile]
           ,[dependency]
           ,[sequence])
     VALUES
           (35
           ,'Tenancy Schedule (current only)'
           ,'APNTenancyScheduleCurrent.php'
           ,0
           ,NULL);

INSERT INTO [dbo].[sub_report]
           ([reportID]
           ,[subReportName]
           ,[subReportFile]
           ,[dependency]
           ,[sequence])
     VALUES
           (35
           ,'Tenancy Schedule (charges)'
           ,'APNTenancyScheduleCharges.php'
           ,0
           ,NULL);
/**
*
* For npms > cpi_index table
*
**/

/* DROP TABLE cpi_index; */

CREATE TABLE cpi_index
(
	id INT IDENTITY(1,1),
	quarter INT,
	cpi_year INT,
	index_name VARCHAR(50),
	title VARCHAR(50),
	cpi_index decimal(10, 2) NOT NULL DEFAULT(0)
);

/*
INSERT WITH DATA
 */

USE [npms]
GO
/****** Object:  Table [dbo].[cpi_index]    Script Date: 6/4/2019 4:51:08 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[cpi_index](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[quarter] [int] NULL,
	[cpi_year] [int] NULL,
	[index_name] [varchar](50) NULL,
	[title] [varchar](50) NULL,
	[cpi_index] [decimal](10, 2) NOT NULL
) ON [PRIMARY]
GO
SET IDENTITY_INSERT [dbo].[cpi_index] ON
GO
INSERT [dbo].[cpi_index] ([id], [quarter], [cpi_year], [index_name], [title], [cpi_index]) VALUES (1, 3, 2016, N'AUSTRALIA', N'Sep-2016', CAST(109.40 AS Decimal(10, 2)))
GO
INSERT [dbo].[cpi_index] ([id], [quarter], [cpi_year], [index_name], [title], [cpi_index]) VALUES (2, 4, 2016, N'AUSTRALIA', N'Dec-2016', CAST(110.00 AS Decimal(10, 2)))
GO
INSERT [dbo].[cpi_index] ([id], [quarter], [cpi_year], [index_name], [title], [cpi_index]) VALUES (3, 1, 2017, N'AUSTRALIA', N'Mar-2017', CAST(110.50 AS Decimal(10, 2)))
GO
INSERT [dbo].[cpi_index] ([id], [quarter], [cpi_year], [index_name], [title], [cpi_index]) VALUES (4, 2, 2017, N'AUSTRALIA', N'Jun-2017', CAST(110.70 AS Decimal(10, 2)))
GO
INSERT [dbo].[cpi_index] ([id], [quarter], [cpi_year], [index_name], [title], [cpi_index]) VALUES (5, 3, 2017, N'AUSTRALIA', N'Sep-2017', CAST(111.40 AS Decimal(10, 2)))
GO
INSERT [dbo].[cpi_index] ([id], [quarter], [cpi_year], [index_name], [title], [cpi_index]) VALUES (6, 4, 2017, N'AUSTRALIA', N'Dec-2017', CAST(112.10 AS Decimal(10, 2)))
GO
INSERT [dbo].[cpi_index] ([id], [quarter], [cpi_year], [index_name], [title], [cpi_index]) VALUES (7, 1, 2018, N'AUSTRALIA', N'Mar-2018', CAST(112.60 AS Decimal(10, 2)))
GO
INSERT [dbo].[cpi_index] ([id], [quarter], [cpi_year], [index_name], [title], [cpi_index]) VALUES (8, 2, 2018, N'AUSTRALIA', N'Jun-2018', CAST(113.00 AS Decimal(10, 2)))
GO
INSERT [dbo].[cpi_index] ([id], [quarter], [cpi_year], [index_name], [title], [cpi_index]) VALUES (9, 3, 2018, N'AUSTRALIA', N'Sep-2018', CAST(113.50 AS Decimal(10, 2)))
GO
INSERT [dbo].[cpi_index] ([id], [quarter], [cpi_year], [index_name], [title], [cpi_index]) VALUES (10, 4, 2018, N'AUSTRALIA', N'Dec-2018', CAST(114.10 AS Decimal(10, 2)))
GO
INSERT [dbo].[cpi_index] ([id], [quarter], [cpi_year], [index_name], [title], [cpi_index]) VALUES (11, 3, 2016, N'PERTH', N'Sep-2016', CAST(108.60 AS Decimal(10, 2)))
GO
INSERT [dbo].[cpi_index] ([id], [quarter], [cpi_year], [index_name], [title], [cpi_index]) VALUES (12, 4, 2016, N'PERTH', N'Dec-2016', CAST(109.00 AS Decimal(10, 2)))
GO
INSERT [dbo].[cpi_index] ([id], [quarter], [cpi_year], [index_name], [title], [cpi_index]) VALUES (13, 1, 2017, N'PERTH', N'Mar-2017', CAST(109.00 AS Decimal(10, 2)))
GO
INSERT [dbo].[cpi_index] ([id], [quarter], [cpi_year], [index_name], [title], [cpi_index]) VALUES (14, 2, 2017, N'PERTH', N'Jun-2017', CAST(109.00 AS Decimal(10, 2)))
GO
INSERT [dbo].[cpi_index] ([id], [quarter], [cpi_year], [index_name], [title], [cpi_index]) VALUES (15, 3, 2017, N'PERTH', N'Sep-2017', CAST(109.50 AS Decimal(10, 2)))
GO
INSERT [dbo].[cpi_index] ([id], [quarter], [cpi_year], [index_name], [title], [cpi_index]) VALUES (16, 4, 2017, N'PERTH', N'Dec-2017', CAST(109.90 AS Decimal(10, 2)))
GO
INSERT [dbo].[cpi_index] ([id], [quarter], [cpi_year], [index_name], [title], [cpi_index]) VALUES (17, 1, 2018, N'PERTH', N'Mar-2018', CAST(110.00 AS Decimal(10, 2)))
GO
INSERT [dbo].[cpi_index] ([id], [quarter], [cpi_year], [index_name], [title], [cpi_index]) VALUES (18, 2, 2018, N'PERTH', N'Jun-2018', CAST(110.20 AS Decimal(10, 2)))
GO
INSERT [dbo].[cpi_index] ([id], [quarter], [cpi_year], [index_name], [title], [cpi_index]) VALUES (19, 3, 2018, N'PERTH', N'Sep-2018', CAST(110.80 AS Decimal(10, 2)))
GO
INSERT [dbo].[cpi_index] ([id], [quarter], [cpi_year], [index_name], [title], [cpi_index]) VALUES (20, 4, 2018, N'PERTH', N'Dec-2018', CAST(111.30 AS Decimal(10, 2)))
GO
INSERT [dbo].[cpi_index] ([id], [quarter], [cpi_year], [index_name], [title], [cpi_index]) VALUES (21, 3, 2016, N'SYDNEY', N'Sep-2016', CAST(110.40 AS Decimal(10, 2)))
GO
INSERT [dbo].[cpi_index] ([id], [quarter], [cpi_year], [index_name], [title], [cpi_index]) VALUES (22, 4, 2016, N'SYDNEY', N'Dec-2016', CAST(110.90 AS Decimal(10, 2)))
GO
INSERT [dbo].[cpi_index] ([id], [quarter], [cpi_year], [index_name], [title], [cpi_index]) VALUES (23, 1, 2017, N'SYDNEY', N'Mar-2017', CAST(111.30 AS Decimal(10, 2)))
GO
INSERT [dbo].[cpi_index] ([id], [quarter], [cpi_year], [index_name], [title], [cpi_index]) VALUES (24, 2, 2017, N'SYDNEY', N'Jun-2017', CAST(111.70 AS Decimal(10, 2)))
GO
INSERT [dbo].[cpi_index] ([id], [quarter], [cpi_year], [index_name], [title], [cpi_index]) VALUES (25, 3, 2017, N'SYDNEY', N'Sep-2017', CAST(112.50 AS Decimal(10, 2)))
GO
INSERT [dbo].[cpi_index] ([id], [quarter], [cpi_year], [index_name], [title], [cpi_index]) VALUES (26, 4, 2017, N'SYDNEY', N'Dec-2017', CAST(113.30 AS Decimal(10, 2)))
GO
INSERT [dbo].[cpi_index] ([id], [quarter], [cpi_year], [index_name], [title], [cpi_index]) VALUES (27, 1, 2018, N'SYDNEY', N'Mar-2018', CAST(113.60 AS Decimal(10, 2)))
GO
INSERT [dbo].[cpi_index] ([id], [quarter], [cpi_year], [index_name], [title], [cpi_index]) VALUES (28, 2, 2018, N'SYDNEY', N'Jun-2018', CAST(114.00 AS Decimal(10, 2)))
GO
INSERT [dbo].[cpi_index] ([id], [quarter], [cpi_year], [index_name], [title], [cpi_index]) VALUES (29, 3, 2018, N'SYDNEY', N'Sep-2018', CAST(114.70 AS Decimal(10, 2)))
GO
INSERT [dbo].[cpi_index] ([id], [quarter], [cpi_year], [index_name], [title], [cpi_index]) VALUES (30, 4, 2018, N'SYDNEY', N'Dec-2018', CAST(115.20 AS Decimal(10, 2)))
GO
INSERT [dbo].[cpi_index] ([id], [quarter], [cpi_year], [index_name], [title], [cpi_index]) VALUES (31, 3, 2016, N'ADELAIDE', N'Sep-2016', CAST(108.40 AS Decimal(10, 2)))
GO
INSERT [dbo].[cpi_index] ([id], [quarter], [cpi_year], [index_name], [title], [cpi_index]) VALUES (32, 4, 2016, N'ADELAIDE', N'Dec-2016', CAST(108.70 AS Decimal(10, 2)))
GO
INSERT [dbo].[cpi_index] ([id], [quarter], [cpi_year], [index_name], [title], [cpi_index]) VALUES (33, 1, 2017, N'ADELAIDE', N'Mar-2017', CAST(109.10 AS Decimal(10, 2)))
GO
INSERT [dbo].[cpi_index] ([id], [quarter], [cpi_year], [index_name], [title], [cpi_index]) VALUES (34, 2, 2017, N'ADELAIDE', N'Jun-2017', CAST(109.20 AS Decimal(10, 2)))
GO
INSERT [dbo].[cpi_index] ([id], [quarter], [cpi_year], [index_name], [title], [cpi_index]) VALUES (35, 3, 2017, N'ADELAIDE', N'Sep-2017', CAST(110.40 AS Decimal(10, 2)))
GO
INSERT [dbo].[cpi_index] ([id], [quarter], [cpi_year], [index_name], [title], [cpi_index]) VALUES (36, 4, 2017, N'ADELAIDE', N'Dec-2017', CAST(111.20 AS Decimal(10, 2)))
GO
INSERT [dbo].[cpi_index] ([id], [quarter], [cpi_year], [index_name], [title], [cpi_index]) VALUES (37, 1, 2018, N'ADELAIDE', N'Mar-2018', CAST(111.60 AS Decimal(10, 2)))
GO
INSERT [dbo].[cpi_index] ([id], [quarter], [cpi_year], [index_name], [title], [cpi_index]) VALUES (38, 2, 2018, N'ADELAIDE', N'Jun-2018', CAST(112.10 AS Decimal(10, 2)))
GO
INSERT [dbo].[cpi_index] ([id], [quarter], [cpi_year], [index_name], [title], [cpi_index]) VALUES (39, 3, 2018, N'ADELAIDE', N'Sep-2018', CAST(112.40 AS Decimal(10, 2)))
GO
INSERT [dbo].[cpi_index] ([id], [quarter], [cpi_year], [index_name], [title], [cpi_index]) VALUES (40, 4, 2018, N'ADELAIDE', N'Dec-2018', CAST(113.00 AS Decimal(10, 2)))
GO
INSERT [dbo].[cpi_index] ([id], [quarter], [cpi_year], [index_name], [title], [cpi_index]) VALUES (41, 3, 2016, N'BRISBANE', N'Sep-2016', CAST(109.70 AS Decimal(10, 2)))
GO
INSERT [dbo].[cpi_index] ([id], [quarter], [cpi_year], [index_name], [title], [cpi_index]) VALUES (42, 4, 2016, N'BRISBANE', N'Dec-2016', CAST(110.20 AS Decimal(10, 2)))
GO
INSERT [dbo].[cpi_index] ([id], [quarter], [cpi_year], [index_name], [title], [cpi_index]) VALUES (43, 1, 2017, N'BRISBANE', N'Mar-2017', CAST(110.50 AS Decimal(10, 2)))
GO
INSERT [dbo].[cpi_index] ([id], [quarter], [cpi_year], [index_name], [title], [cpi_index]) VALUES (44, 2, 2017, N'BRISBANE', N'Jun-2017', CAST(110.00 AS Decimal(10, 2)))
GO
INSERT [dbo].[cpi_index] ([id], [quarter], [cpi_year], [index_name], [title], [cpi_index]) VALUES (45, 3, 2017, N'BRISBANE', N'Sep-2017', CAST(111.40 AS Decimal(10, 2)))
GO
INSERT [dbo].[cpi_index] ([id], [quarter], [cpi_year], [index_name], [title], [cpi_index]) VALUES (46, 4, 2017, N'BRISBANE', N'Dec-2017', CAST(112.30 AS Decimal(10, 2)))
GO
INSERT [dbo].[cpi_index] ([id], [quarter], [cpi_year], [index_name], [title], [cpi_index]) VALUES (47, 1, 2018, N'BRISBANE', N'Mar-2018', CAST(112.40 AS Decimal(10, 2)))
GO
INSERT [dbo].[cpi_index] ([id], [quarter], [cpi_year], [index_name], [title], [cpi_index]) VALUES (48, 2, 2018, N'BRISBANE', N'Jun-2018', CAST(112.90 AS Decimal(10, 2)))
GO
INSERT [dbo].[cpi_index] ([id], [quarter], [cpi_year], [index_name], [title], [cpi_index]) VALUES (49, 3, 2018, N'BRISBANE', N'Sep-2018', CAST(113.40 AS Decimal(10, 2)))
GO
INSERT [dbo].[cpi_index] ([id], [quarter], [cpi_year], [index_name], [title], [cpi_index]) VALUES (50, 4, 2018, N'BRISBANE', N'Dec-2018', CAST(114.00 AS Decimal(10, 2)))
GO
INSERT [dbo].[cpi_index] ([id], [quarter], [cpi_year], [index_name], [title], [cpi_index]) VALUES (51, 3, 2016, N'DARWIN', N'Sep-2016', CAST(108.70 AS Decimal(10, 2)))
GO
INSERT [dbo].[cpi_index] ([id], [quarter], [cpi_year], [index_name], [title], [cpi_index]) VALUES (52, 4, 2016, N'DARWIN', N'Dec-2016', CAST(108.60 AS Decimal(10, 2)))
GO
INSERT [dbo].[cpi_index] ([id], [quarter], [cpi_year], [index_name], [title], [cpi_index]) VALUES (53, 1, 2017, N'DARWIN', N'Mar-2017', CAST(108.50 AS Decimal(10, 2)))
GO
INSERT [dbo].[cpi_index] ([id], [quarter], [cpi_year], [index_name], [title], [cpi_index]) VALUES (54, 2, 2017, N'DARWIN', N'Jun-2017', CAST(108.80 AS Decimal(10, 2)))
GO
INSERT [dbo].[cpi_index] ([id], [quarter], [cpi_year], [index_name], [title], [cpi_index]) VALUES (55, 3, 2017, N'DARWIN', N'Sep-2017', CAST(109.40 AS Decimal(10, 2)))
GO
INSERT [dbo].[cpi_index] ([id], [quarter], [cpi_year], [index_name], [title], [cpi_index]) VALUES (56, 4, 2017, N'DARWIN', N'Dec-2017', CAST(109.70 AS Decimal(10, 2)))
GO
INSERT [dbo].[cpi_index] ([id], [quarter], [cpi_year], [index_name], [title], [cpi_index]) VALUES (57, 1, 2018, N'DARWIN', N'Mar-2018', CAST(109.70 AS Decimal(10, 2)))
GO
INSERT [dbo].[cpi_index] ([id], [quarter], [cpi_year], [index_name], [title], [cpi_index]) VALUES (58, 2, 2018, N'DARWIN', N'Jun-2018', CAST(110.10 AS Decimal(10, 2)))
GO
INSERT [dbo].[cpi_index] ([id], [quarter], [cpi_year], [index_name], [title], [cpi_index]) VALUES (59, 3, 2018, N'DARWIN', N'Sep-2018', CAST(110.80 AS Decimal(10, 2)))
GO
INSERT [dbo].[cpi_index] ([id], [quarter], [cpi_year], [index_name], [title], [cpi_index]) VALUES (60, 4, 2018, N'DARWIN', N'Dec-2018', CAST(111.00 AS Decimal(10, 2)))
GO
INSERT [dbo].[cpi_index] ([id], [quarter], [cpi_year], [index_name], [title], [cpi_index]) VALUES (61, 3, 2016, N'HOBART', N'Sep-2016', CAST(107.10 AS Decimal(10, 2)))
GO
INSERT [dbo].[cpi_index] ([id], [quarter], [cpi_year], [index_name], [title], [cpi_index]) VALUES (62, 4, 2016, N'HOBART', N'Dec-2016', CAST(108.00 AS Decimal(10, 2)))
GO
INSERT [dbo].[cpi_index] ([id], [quarter], [cpi_year], [index_name], [title], [cpi_index]) VALUES (63, 1, 2017, N'HOBART', N'Mar-2017', CAST(108.90 AS Decimal(10, 2)))
GO
INSERT [dbo].[cpi_index] ([id], [quarter], [cpi_year], [index_name], [title], [cpi_index]) VALUES (64, 2, 2017, N'HOBART', N'Jun-2017', CAST(108.90 AS Decimal(10, 2)))
GO
INSERT [dbo].[cpi_index] ([id], [quarter], [cpi_year], [index_name], [title], [cpi_index]) VALUES (65, 3, 2017, N'HOBART', N'Sep-2017', CAST(109.20 AS Decimal(10, 2)))
GO
INSERT [dbo].[cpi_index] ([id], [quarter], [cpi_year], [index_name], [title], [cpi_index]) VALUES (66, 4, 2017, N'HOBART', N'Dec-2017', CAST(110.30 AS Decimal(10, 2)))
GO
INSERT [dbo].[cpi_index] ([id], [quarter], [cpi_year], [index_name], [title], [cpi_index]) VALUES (67, 1, 2018, N'HOBART', N'Mar-2018', CAST(111.10 AS Decimal(10, 2)))
GO
INSERT [dbo].[cpi_index] ([id], [quarter], [cpi_year], [index_name], [title], [cpi_index]) VALUES (68, 2, 2018, N'HOBART', N'Jun-2018', CAST(111.50 AS Decimal(10, 2)))
GO
INSERT [dbo].[cpi_index] ([id], [quarter], [cpi_year], [index_name], [title], [cpi_index]) VALUES (69, 3, 2018, N'HOBART', N'Sep-2018', CAST(112.20 AS Decimal(10, 2)))
GO
INSERT [dbo].[cpi_index] ([id], [quarter], [cpi_year], [index_name], [title], [cpi_index]) VALUES (70, 4, 2018, N'HOBART', N'Dec-2018', CAST(113.60 AS Decimal(10, 2)))
GO
INSERT [dbo].[cpi_index] ([id], [quarter], [cpi_year], [index_name], [title], [cpi_index]) VALUES (71, 3, 2016, N'MELBOURNE', N'Sep-2016', CAST(109.10 AS Decimal(10, 2)))
GO
INSERT [dbo].[cpi_index] ([id], [quarter], [cpi_year], [index_name], [title], [cpi_index]) VALUES (72, 4, 2016, N'MELBOURNE', N'Dec-2016', CAST(109.90 AS Decimal(10, 2)))
GO
INSERT [dbo].[cpi_index] ([id], [quarter], [cpi_year], [index_name], [title], [cpi_index]) VALUES (73, 1, 2017, N'MELBOURNE', N'Mar-2017', CAST(110.90 AS Decimal(10, 2)))
GO
INSERT [dbo].[cpi_index] ([id], [quarter], [cpi_year], [index_name], [title], [cpi_index]) VALUES (74, 2, 2017, N'MELBOURNE', N'Jun-2017', CAST(111.00 AS Decimal(10, 2)))
GO
INSERT [dbo].[cpi_index] ([id], [quarter], [cpi_year], [index_name], [title], [cpi_index]) VALUES (75, 3, 2017, N'MELBOURNE', N'Sep-2017', CAST(111.50 AS Decimal(10, 2)))
GO
INSERT [dbo].[cpi_index] ([id], [quarter], [cpi_year], [index_name], [title], [cpi_index]) VALUES (76, 4, 2017, N'MELBOURNE', N'Dec-2017', CAST(112.30 AS Decimal(10, 2)))
GO
INSERT [dbo].[cpi_index] ([id], [quarter], [cpi_year], [index_name], [title], [cpi_index]) VALUES (77, 1, 2018, N'MELBOURNE', N'Mar-2018', CAST(113.30 AS Decimal(10, 2)))
GO
INSERT [dbo].[cpi_index] ([id], [quarter], [cpi_year], [index_name], [title], [cpi_index]) VALUES (78, 2, 2018, N'MELBOURNE', N'Jun-2018', CAST(113.80 AS Decimal(10, 2)))
GO
INSERT [dbo].[cpi_index] ([id], [quarter], [cpi_year], [index_name], [title], [cpi_index]) VALUES (79, 3, 2018, N'MELBOURNE', N'Sep-2018', CAST(114.00 AS Decimal(10, 2)))
GO
INSERT [dbo].[cpi_index] ([id], [quarter], [cpi_year], [index_name], [title], [cpi_index]) VALUES (80, 4, 2018, N'MELBOURNE', N'Dec-2018', CAST(114.60 AS Decimal(10, 2)))
GO
INSERT [dbo].[cpi_index] ([id], [quarter], [cpi_year], [index_name], [title], [cpi_index]) VALUES (81, 1, 2019, N'SYDNEY', N'Mar-2019', CAST(115.10 AS Decimal(10, 2)))
GO
INSERT [dbo].[cpi_index] ([id], [quarter], [cpi_year], [index_name], [title], [cpi_index]) VALUES (82, 1, 2019, N'MELBOURNE', N'Mar-2019', CAST(114.70 AS Decimal(10, 2)))
GO
INSERT [dbo].[cpi_index] ([id], [quarter], [cpi_year], [index_name], [title], [cpi_index]) VALUES (83, 1, 2019, N'BRISBANE', N'Mar-2019', CAST(114.10 AS Decimal(10, 2)))
GO
INSERT [dbo].[cpi_index] ([id], [quarter], [cpi_year], [index_name], [title], [cpi_index]) VALUES (84, 1, 2019, N'ADELAIDE', N'Mar-2019', CAST(113.10 AS Decimal(10, 2)))
GO
INSERT [dbo].[cpi_index] ([id], [quarter], [cpi_year], [index_name], [title], [cpi_index]) VALUES (85, 1, 2019, N'PERTH', N'Mar-2019', CAST(111.20 AS Decimal(10, 2)))
GO
INSERT [dbo].[cpi_index] ([id], [quarter], [cpi_year], [index_name], [title], [cpi_index]) VALUES (86, 1, 2019, N'HOBART', N'Mar-2019', CAST(113.40 AS Decimal(10, 2)))
GO
INSERT [dbo].[cpi_index] ([id], [quarter], [cpi_year], [index_name], [title], [cpi_index]) VALUES (87, 1, 2019, N'DARWIN', N'Mar-2019', CAST(110.10 AS Decimal(10, 2)))
GO
INSERT [dbo].[cpi_index] ([id], [quarter], [cpi_year], [index_name], [title], [cpi_index]) VALUES (88, 1, 2019, N'AUSTRALIA', N'Mar-2019', CAST(114.10 AS Decimal(10, 2)))
GO
SET IDENTITY_INSERT [dbo].[cpi_index] OFF
GO
ALTER TABLE [dbo].[cpi_index] ADD  DEFAULT ((0)) FOR [cpi_index]
GO
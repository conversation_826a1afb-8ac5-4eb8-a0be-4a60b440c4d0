
-- CHECK IF HAS AGENT PARAMETER TYPE
IF EXISTS( SELECT * FROM pmzt_par_type where pmzt_par_type = 'AGENT')
	BEGIN
		DELETE FROM pmzt_par_type where pmzt_par_type = 'AGENTS'
	END
ELSE
	-- <PERSON><PERSON>K IF HAS AGENTS
	IF EXISTS( SELECT * FROM pmzt_par_type where pmzt_par_type = 'AGENTS')
		BEGIN
			-- UPDATE PARAMETER TYPE TO AGENT
			UPDATE pmzt_par_type set pmzt_par_type = 'AGENT' WHERE pmzt_par_type = 'AGENTS'
			-- UPDATE PARAM AGENTS TO AGENT
			UPDATE pmzz_param set pmzz_par_type = 'AGENT' WHERE pmzz_par_type = 'AGENTS'
		END
	ELSE
		BEGIN
			-- INSERT PARAMETER TYPE
			INSERT INTO pmzt_par_type VALUES ('AGENT','Leasing and sales agents')
		END

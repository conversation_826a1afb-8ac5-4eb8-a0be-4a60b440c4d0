/**
*
* Add separate tables for npms > scheduledTasks2-3 along with scheduledTasksLog2-3 tables
*
**/

/** For 2nd scheduledTask-related tables **/
CREATE TABLE [dbo].[scheduledTasks2](
[queueID] [int] IDENTITY(1,1) NOT NULL,
[databaseID] [int] NOT NULL,
[type] [tinyint] NOT NULL,
[command] [varchar](150) NOT NULL,
[data] [text] NULL,
[count] [tinyint] NOT NULL DEFAULT 0,
[createdDate] [datetime] NOT NULL,
[createdBy] [varchar](100) NULL,
[lastAccessed] [datetime] NULL
);

CREATE TABLE [dbo].[scheduledTasksLog2](
  [queueID] [int] NOT NULL,
  [databaseID] [int] NOT NULL,
  [type] [tinyint] NOT NULL,
  [command] [varchar](150) NOT NULL,
  [data] [text] NULL,
  [count] [tinyint] NOT NULL,
  [createdDate] [datetime] NOT NULL,
  [createdBy] [varchar](30) NULL,
  [lastAccessed] [datetime] NULL
);

/** For 3rd scheduledTask-related tables **/
CREATE TABLE [dbo].[scheduledTasks3](
[queueID] [int] IDENTITY(1,1) NOT NULL,
[databaseID] [int] NOT NULL,
[type] [tinyint] NOT NULL,
[command] [varchar](150) NOT NULL,
[data] [text] NULL,
[count] [tinyint] NOT NULL DEFAULT 0,
[createdDate] [datetime] NOT NULL,
[createdBy] [varchar](100) NULL,
[lastAccessed] [datetime] NULL
);

CREATE TABLE [dbo].[scheduledTasksLog3](
  [queueID] [int] NOT NULL,
  [databaseID] [int] NOT NULL,
  [type] [tinyint] NOT NULL,
  [command] [varchar](150) NOT NULL,
  [data] [text] NULL,
  [count] [tinyint] NOT NULL,
  [createdDate] [datetime] NOT NULL,
  [createdBy] [varchar](30) NULL,
  [lastAccessed] [datetime] NULL
);
SELECT *
FROM pmzz_param
where pmzz_par_type = 'PHONETYPE'

INSERT INTO pmzz_param (pmzz_par_type, pmzz_code, pmzz_desc)
VALUES ('PHONETYPE', 'SALU', 'Salutation');

INSERT INTO pmzz_param (pmzz_par_type, pmzz_code, pmzz_desc)
VALUES ('PPHONETYPE', 'ADDRESS', 'Address');

INSERT INTO pmzz_param (pmzz_par_type, pmzz_code, pmzz_desc)
VALUES ('LPHONETYPE', 'ADDRESS', 'Address');

INSERT INTO pmzz_param (pmzz_par_type, pmzz_code, pmzz_desc)
VALUES ('CPHONETYPE', 'ADDRESS', 'Address');

INSERT INTO pmzz_param (pmzz_par_type, pmzz_code, pmzz_desc)
VALUES ('PPHONETYPE', 'HOME', 'Home');

INSERT INTO pmzz_param (pmzz_par_type, pmzz_code, pmzz_desc)
VALUES ('CPHONETYPE', 'HOME', 'Home');

INSERT INTO pmzz_param (pmzz_par_type, pmzz_code, pmzz_desc)
VALUES ('LPHONETYPE', 'HOME', 'Home');

UPDATE pmzz_param
SET pmzz_code = 'PHONE'
WHERE pmzz_code = 'PHONETYPE1'

UPDATE pmzz_param
SET pmzz_code = 'MOBILE'
WHERE pmzz_code = 'PHONETYPE2'

UPDATE pmzz_param
SET pmzz_code = 'EMAIL'
WHERE pmzz_code = 'PHONETYPE4'

UPDATE pmzz_param
SET pmzz_code = 'SALU'
WHERE pmzz_code = 'SALUTATION'

UPDATE pmzz_param
SET pmzz_code = 'EMAIL'
WHERE pmzz_code = 'E'

UPDATE pmzz_param
SET pmzz_code = 'MOBILE'
WHERE pmzz_code = 'M'

UPDATE pmzz_param
SET pmzz_code = 'PHONE'
WHERE pmzz_code = 'P'



EXEC sp_rename 'bank_receipts', 'bank_transactions';
GO
ALTER TABLE [dbo].[bank_transactions] ADD description VARCHAR(MAX) NULL; 
GO
ALTER TABLE [dbo].[bank_transactions] ADD file_line_no INT NULL; 
GO
ALTER TABLE [dbo].[bank_transactions] ADD line_type VARCHAR(5) NULL; 
GO
ALTER TABLE [dbo].[bank_transactions]
        ADD is_matched Bit NULL
 CONSTRAINT DF__bank_transactions_is_matched 
    DEFAULT (0)
WITH VALUES 
GO
ALTER TABLE [dbo].[bank_transactions]
        ADD is_reconciled Bit NULL
 CONSTRAINT DF__bank_transactions_is_reconciled 
    DEFAULT (0)
WITH VALUES 
GO
ALTER TABLE [dbo].[bank_transactions] ADD method VARCHAR(10) NULL; 
GO
ALTER TABLE [dbo].[bank_transactions] ADD reference VARCHAR(MAX) NULL; 
GO
ALTER TABLE [dbo].[bank_transactions] ADD balance decimal(19,2) NULL DEFAULT ((0)); 
GO
ALTER TABLE [dbo].[bank_transactions] ALTER COLUMN debtor_code varchar(50) null;
GO
ALTER TABLE [dbo].[bank_transactions] ALTER COLUMN lease_code varchar(50) null;
GO
ALTER TABLE [dbo].[bank_transactions] ALTER COLUMN property_code varchar(50) null;
GO
ALTER TABLE [dbo].[bank_transactions] ALTER COLUMN crn varchar(50) null;
GO
CREATE TABLE [dbo].[bank_transactions_header] (
[id] int NOT NULL IDENTITY(1,1) ,
[bank_code] varchar(50) NOT NULL ,
[file_code] varchar(50) NULL ,
[file_date] date NOT NULL ,
[opening_bal] decimal(19,2) NOT NULL DEFAULT ((0)) ,
[closing_bal] decimal(19,2) NOT NULL DEFAULT ((0)) ,
[upload_user] varchar(50) NOT NULL ,
[uploaded_at] datetime NOT NULL 
)
GO
CREATE TABLE [dbo].[bank_transactions_recon] (
[id] int NOT NULL IDENTITY(1,1) ,
[bank_trans_id] int NOT NULL ,
[file_code] varchar(50) NOT NULL ,
[bank_code] varchar(50) NOT NULL ,
[join_code] varchar(50) NULL ,
[trans_type] varchar(15) NOT NULL ,
[ref_code] varchar(50) NULL ,
[ref_no] varchar(50) NULL ,
[amount] decimal(19,2) NOT NULL DEFAULT ((0)) ,
[recon_date] date NULL ,
[recon_user] varchar(50) NULL ,
[recon_at] datetime NULL ,
)
GO
ALTER TABLE bank_transactions ADD adjustment_amount decimal(19,2) NULL
GO
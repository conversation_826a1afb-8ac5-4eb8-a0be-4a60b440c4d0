CREATE TABLE queue_email_activity_log (
  id INT IDENTITY(1,1) PRIMARY KEY,
  created_at DATETIME NOT NULL DEFAULT (GETDATE()),
  recipient_email NVARCHAR(255),
  queue_email_id INT,
  email_log_id INT NULL,
  sent_at DATETIME NULL,
  status INT DEFAULT 0, --0 = waiting, 1 = success, 2 = failed
  error_message VARCHAR(1000) NULL
);

CREATE INDEX idx_queue_email_activity_log_queue_email_id ON queue_email_activity_log (queue_email_id);
CREATE INDEX idx_queue_email_activity_log_email_log_id ON queue_email_activity_log (email_log_id);
CREATE INDEX idx_queue_email_activity_log_status ON queue_email_activity_log (status);
CREATE INDEX idx_queue_email_activity_log_recipient_email ON queue_email_activity_log (status);

DROP INDEX idx_queue_email_activity_log_queue_email_id ON queue_email_activity_log;
DROP INDEX idx_queue_email_activity_log_email_log_id ON queue_email_activity_log;
DROP INDEX idx_queue_email_activity_log_status ON queue_email_activity_log;

drop TABLE queue_email_activity_log


ALTER TABLE pmrc_receipt
        ADD pmrc_clear_days INT NULL
 CONSTRAINT DF__pmrc_receipt_pmrc_clear_days 
    DEFAULT (0)
WITH VALUES 
GO
ALTER TABLE temp_receipt
        ADD clearDays INT NULL
 CONSTRAINT DF__temp_receipt_clearDays 
    DEFAULT (0)
WITH VALUES 
GO
ALTER TABLE pmrc_receipt
        ADD pmrc_clear_date DATE NULL 
GO
ALTER TABLE temp_receipt
        ADD clearDate DATE NULL

-- INSERTS FOR PARAMS
INSERT INTO [dbo].[pmzt_par_type] ([pmzt_par_type], [pmzt_desc]) VALUES ('RCLEARDAYS', 'Receipting type clearance days          ');
INSERT INTO [dbo].[pmzz_param] ([pmzz_par_type], [pmzz_code], [pmzz_desc], [pmzz_type], [pmzz_percentage]) VALUES ('RCLEARDAYS', 'CD-BPA    ', '1', '0', '.00');
INSERT INTO [dbo].[pmzz_param] ([pmzz_par_type], [pmzz_code], [pmzz_desc], [pmzz_type], [pmzz_percentage]) VALUES ('RCLEARDAYS', 'CD-CHQ    ', '3', '0', '.00');
INSERT INTO [dbo].[pmzz_param] ([pmzz_par_type], [pmzz_code], [pmzz_desc], [pmzz_type], [pmzz_percentage]) VALUES ('RCLEARDAYS', 'CD-CSH    ', '0', '0', '.00');
INSERT INTO [dbo].[pmzz_param] ([pmzz_par_type], [pmzz_code], [pmzz_desc], [pmzz_type], [pmzz_percentage]) VALUES ('RCLEARDAYS', 'CD-DDR    ', '3', '0', '.00');
INSERT INTO [dbo].[pmzz_param] ([pmzz_par_type], [pmzz_code], [pmzz_desc], [pmzz_type], [pmzz_percentage]) VALUES ('RCLEARDAYS', 'CD-DIR    ', '0', '0', '.00');

/* done on live */
/* done on dev */


CREATE TABLE [dbo].[lease_inspections] (
  [ID] INT NOT NULL IDENTITY PRIMARY KEY,
  [created_at] DATETIME NOT NULL DEFAULT (GETUTCDATE()),
  [created_by] INT,
  [property_code] VARCHAR(11),
  [lease_code] VARCHAR(11),
  [frequency] CHAR(1),
  [scheduled_date] DATETIME,
  [inspection_type] VARCHAR(100),
  [pim_template] VARCHAR(100),
  [comment] VARCHAR(150),
  [pass] VARCHAR(10),
  [completion] BIT,
  [charge] DECIMAL(19,2),
  [completed_by] INT,
  [completed_date] DATETIME,
  [attached_document_id] INT
)

CREATE TABLE [dbo].[lease_inspections_temp] (
  [ID] INT NOT NULL IDENTITY PRIMARY KEY,
  [created_at] DATETIME NOT NULL DEFAULT (GETUTCDATE()),
  [created_by] INT,
  [property_code] VARCHAR(11),
  [lease_code] VARCHAR(11),
  [varsionID] INT,
  [frequency] CHAR(1),
  [scheduled_date] DATETIME,
  [inspection_type] VARCHAR(100),
  [pim_template] VARCHAR(100),
  [comment] VARCHAR(150),
  [pass] VARCHAR(10),
  [completion] BIT,
  [charge] DECIMAL(19,2),
  [completed_by] INT,
  [completed_date] DATETIME,
  [attached_document_id] INT
)

ALTER TABLE [dbo].[pmdy_diary]
ADD [lease_inspection_id] INT DEFAULT 0;

ALTER TABLE [dbo].[pmpr_property]
ADD [pmpr_inspec_fee] BIT DEFAULT 0;

ALTER TABLE [dbo].[pmpr_property]
ADD [pmpr_inspec_amount] DECIMAL(19,2) DEFAULT 0;

ALTER TABLE [dbo].[pmpr_property]
ADD [pmpr_inspec_pay_account] VARCHAR(100)  DEFAULT '';

ALTER TABLE [dbo].[pmpr_property]
ADD [pmpr_inspec_frequency] CHAR(1) DEFAULT 'M';

ALTER TABLE [dbo].[pmpr_property]
ADD [pmpr_inspec_recovery] BIT DEFAULT 0;

ALTER TABLE [dbo].[pmpr_property]
ADD [pmpr_inspec_recovery_account] VARCHAR(100)  DEFAULT '';



ALTER TABLE [dbo].[temp_pmpr_property]
ADD [pmpr_inspec_fee] BIT DEFAULT 0;

ALTER TABLE [dbo].[temp_pmpr_property]
ADD [pmpr_inspec_amount] DECIMAL(19,2)  DEFAULT 0;

ALTER TABLE [dbo].[temp_pmpr_property]
ADD [pmpr_inspec_pay_account] VARCHAR(100);

ALTER TABLE [dbo].[temp_pmpr_property]
ADD [pmpr_inspec_frequency] CHAR(1) DEFAULT 'M';

ALTER TABLE [dbo].[temp_pmpr_property]
ADD [pmpr_inspec_recovery] BIT DEFAULT 0;

ALTER TABLE [dbo].[temp_pmpr_property]
ADD [pmpr_inspec_recovery_account] VARCHAR(100)  DEFAULT '';

ALTER TABLE [dbo].[temp_pmdy_diary]
ADD [lease_inspection_id] INT DEFAULT 0;


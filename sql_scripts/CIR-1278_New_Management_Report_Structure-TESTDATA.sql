USE [APG]
GO
/****** Object:  Table [dbo].[mgmt_sub_report_table_column_config]    Script Date: 10/05/2021 8:11:45 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[mgmt_sub_report_table_column_config](
	[column_id] [int] IDENTITY(1,1) NOT NULL,
	[column_label] [varchar](255) NULL,
	[table_id] [int] NULL,
	[sub_report_id] [int] NULL,
	[alignment] [varchar](255) NULL,
	[is_readonly] [tinyint] NULL,
	[max_characters] [int] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[mgmt_sub_report_table_config]    Script Date: 10/05/2021 8:11:45 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[mgmt_sub_report_table_config](
	[table_id] [int] IDENTITY(1,1) NOT NULL,
	[table_description] [varchar](255) NULL,
	[function_call] [varchar](255) NULL,
	[mgmt_report_id] [int] NULL,
	[sub_report_id] [int] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[mgmt_sub_report_table_data]    Script Date: 10/05/2021 8:11:45 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[mgmt_sub_report_table_data](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[property_code] [varchar](100) NULL,
	[sub_report_id] [int] NULL,
	[cal_period] [int] NULL,
	[cal_year] [int] NULL,
	[table_id] [int] NULL,
	[row_no] [int] NULL,
	[column_1] [nvarchar](max) NULL,
	[column_2] [nvarchar](max) NULL,
	[column_3] [nvarchar](max) NULL,
	[column_4] [nvarchar](max) NULL,
	[column_5] [nvarchar](max) NULL,
	[column_6] [nvarchar](max) NULL,
	[column_7] [nvarchar](max) NULL,
	[column_8] [nvarchar](max) NULL,
	[column_9] [nvarchar](max) NULL,
	[column_10] [nvarchar](max) NULL,
	[column_11] [nvarchar](max) NULL,
	[column_12] [nvarchar](max) NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
SET IDENTITY_INSERT [dbo].[mgmt_sub_report_table_column_config] ON 

INSERT [dbo].[mgmt_sub_report_table_column_config] ([column_id], [column_label], [table_id], [sub_report_id], [alignment], [is_readonly], [max_characters]) VALUES (1, N'Property Code', 1, 2, N'Left', 0, 50)
INSERT [dbo].[mgmt_sub_report_table_column_config] ([column_id], [column_label], [table_id], [sub_report_id], [alignment], [is_readonly], [max_characters]) VALUES (2, N'Property Name', 1, 2, N'Left', 0, 50)
INSERT [dbo].[mgmt_sub_report_table_column_config] ([column_id], [column_label], [table_id], [sub_report_id], [alignment], [is_readonly], [max_characters]) VALUES (3, N'Lease Code', 1, 2, N'Left', 0, 50)
INSERT [dbo].[mgmt_sub_report_table_column_config] ([column_id], [column_label], [table_id], [sub_report_id], [alignment], [is_readonly], [max_characters]) VALUES (4, N'Lease Name', 1, 2, N'Left', 0, 50)
INSERT [dbo].[mgmt_sub_report_table_column_config] ([column_id], [column_label], [table_id], [sub_report_id], [alignment], [is_readonly], [max_characters]) VALUES (5, N'Lease Code', 2, 2, N'Left', 0, 100)
INSERT [dbo].[mgmt_sub_report_table_column_config] ([column_id], [column_label], [table_id], [sub_report_id], [alignment], [is_readonly], [max_characters]) VALUES (6, N'Lease Name', 2, 2, N'Left', 0, 100)
INSERT [dbo].[mgmt_sub_report_table_column_config] ([column_id], [column_label], [table_id], [sub_report_id], [alignment], [is_readonly], [max_characters]) VALUES (7, N'Lease Description', 2, 2, N'Left', 0, 100)
SET IDENTITY_INSERT [dbo].[mgmt_sub_report_table_column_config] OFF
SET IDENTITY_INSERT [dbo].[mgmt_sub_report_table_config] ON 

INSERT [dbo].[mgmt_sub_report_table_config] ([table_id], [table_description], [function_call], [mgmt_report_id], [sub_report_id]) VALUES (1, N'Property Lease Details', N'dbGetPropertyLeaseDetailsTblData', 1, 2)
INSERT [dbo].[mgmt_sub_report_table_config] ([table_id], [table_description], [function_call], [mgmt_report_id], [sub_report_id]) VALUES (2, N'Tenant Details', N'dbGetLeaseDetailsTblData', 1, 2)
SET IDENTITY_INSERT [dbo].[mgmt_sub_report_table_config] OFF
SET IDENTITY_INSERT [dbo].[mgmt_sub_report_table_data] ON 

INSERT [dbo].[mgmt_sub_report_table_data] ([id], [property_code], [sub_report_id], [cal_period], [cal_year], [table_id], [row_no], [column_1], [column_2], [column_3], [column_4], [column_5], [column_6], [column_7], [column_8], [column_9], [column_10], [column_11], [column_12]) VALUES (1, N'ATWE46', 2, 6, 2020, 1, 1, N'ATWE46', N'46 Attwell Street', N'CAMERA', N'Camera Dynamicstest SAMPLE TEST', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL)
INSERT [dbo].[mgmt_sub_report_table_data] ([id], [property_code], [sub_report_id], [cal_period], [cal_year], [table_id], [row_no], [column_1], [column_2], [column_3], [column_4], [column_5], [column_6], [column_7], [column_8], [column_9], [column_10], [column_11], [column_12]) VALUES (2, N'ATWE46', 2, 6, 2020, 1, 2, N'ATWE46', N'46 Attwell Street', N'DAVION', N'J&R Hancock Pty Ltd T/A Davion Enterpris', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL)
INSERT [dbo].[mgmt_sub_report_table_data] ([id], [property_code], [sub_report_id], [cal_period], [cal_year], [table_id], [row_no], [column_1], [column_2], [column_3], [column_4], [column_5], [column_6], [column_7], [column_8], [column_9], [column_10], [column_11], [column_12]) VALUES (3, N'ATWE46', 2, 6, 2020, 1, 3, N'ATWE46', N'46 Attwell Street', N'FLOASI', N'Floortech''s Asia Pacific Pty Ltd', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL)
INSERT [dbo].[mgmt_sub_report_table_data] ([id], [property_code], [sub_report_id], [cal_period], [cal_year], [table_id], [row_no], [column_1], [column_2], [column_3], [column_4], [column_5], [column_6], [column_7], [column_8], [column_9], [column_10], [column_11], [column_12]) VALUES (4, N'ATWE46', 2, 6, 2020, 1, 4, N'ATWE46', N'46 Attwell Street', N'KINRIV', N'King RIVER Resources LIMIteD', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL)
INSERT [dbo].[mgmt_sub_report_table_data] ([id], [property_code], [sub_report_id], [cal_period], [cal_year], [table_id], [row_no], [column_1], [column_2], [column_3], [column_4], [column_5], [column_6], [column_7], [column_8], [column_9], [column_10], [column_11], [column_12]) VALUES (5, N'ATWE46', 2, 6, 2020, 1, 5, N'ATWE46', N'46 Attwell Street', N'PWORLD', N'P World Products', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL)
INSERT [dbo].[mgmt_sub_report_table_data] ([id], [property_code], [sub_report_id], [cal_period], [cal_year], [table_id], [row_no], [column_1], [column_2], [column_3], [column_4], [column_5], [column_6], [column_7], [column_8], [column_9], [column_10], [column_11], [column_12]) VALUES (6, N'ATWE46', 2, 6, 2020, 1, 6, N'ATWE46', N'46 Attwell Street', N'KFOCON', N'Kform Contracting Pty Ltd', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL)
INSERT [dbo].[mgmt_sub_report_table_data] ([id], [property_code], [sub_report_id], [cal_period], [cal_year], [table_id], [row_no], [column_1], [column_2], [column_3], [column_4], [column_5], [column_6], [column_7], [column_8], [column_9], [column_10], [column_11], [column_12]) VALUES (7, N'ATWE46', 2, 6, 2020, 1, 7, N'ATWE46', N'46 Attwell Street', N'PERSAN', N'Perth Sandstone Garden Products', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL)
INSERT [dbo].[mgmt_sub_report_table_data] ([id], [property_code], [sub_report_id], [cal_period], [cal_year], [table_id], [row_no], [column_1], [column_2], [column_3], [column_4], [column_5], [column_6], [column_7], [column_8], [column_9], [column_10], [column_11], [column_12]) VALUES (8, N'ATWE46', 2, 6, 2020, 1, 8, N'ATWE46', N'46 Attwell Street', N'PRICEW', N'Pricewise Pavers Pty Ltd', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL)
INSERT [dbo].[mgmt_sub_report_table_data] ([id], [property_code], [sub_report_id], [cal_period], [cal_year], [table_id], [row_no], [column_1], [column_2], [column_3], [column_4], [column_5], [column_6], [column_7], [column_8], [column_9], [column_10], [column_11], [column_12]) VALUES (9, N'ATWE46', 2, 6, 2020, 1, 9, N'ATWE46', N'46 Attwell Street', N'VICKER', N'Paul John Vickers', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL)
INSERT [dbo].[mgmt_sub_report_table_data] ([id], [property_code], [sub_report_id], [cal_period], [cal_year], [table_id], [row_no], [column_1], [column_2], [column_3], [column_4], [column_5], [column_6], [column_7], [column_8], [column_9], [column_10], [column_11], [column_12]) VALUES (10, N'ATWE46', 2, 6, 2020, 1, 10, N'ATWE46', N'46 Attwell Street', N'REDSMI', N'P & S Redmond & C Smith', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL)
INSERT [dbo].[mgmt_sub_report_table_data] ([id], [property_code], [sub_report_id], [cal_period], [cal_year], [table_id], [row_no], [column_1], [column_2], [column_3], [column_4], [column_5], [column_6], [column_7], [column_8], [column_9], [column_10], [column_11], [column_12]) VALUES (11, N'ATWE46', 2, 6, 2020, 1, 11, N'ATWE46', N'46 Attwell Street', N'STEVEL', N'Stevan Veljanoski T/as Minetech Auto Electrical', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL)
INSERT [dbo].[mgmt_sub_report_table_data] ([id], [property_code], [sub_report_id], [cal_period], [cal_year], [table_id], [row_no], [column_1], [column_2], [column_3], [column_4], [column_5], [column_6], [column_7], [column_8], [column_9], [column_10], [column_11], [column_12]) VALUES (12, N'ABER91U1', 2, 6, 2020, 1, 1, N'ABER91U1', N'Unit 1, 91 Aberdeen Street', N'CGCINT', N'CGC International Pty Ltd', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL)
INSERT [dbo].[mgmt_sub_report_table_data] ([id], [property_code], [sub_report_id], [cal_period], [cal_year], [table_id], [row_no], [column_1], [column_2], [column_3], [column_4], [column_5], [column_6], [column_7], [column_8], [column_9], [column_10], [column_11], [column_12]) VALUES (13, N'ABER91U1', 2, 6, 2020, 1, 2, N'ABER91U1', N'Unit 1, 91 Aberdeen Street', N'INTERN', N'International Student Centre', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL)
INSERT [dbo].[mgmt_sub_report_table_data] ([id], [property_code], [sub_report_id], [cal_period], [cal_year], [table_id], [row_no], [column_1], [column_2], [column_3], [column_4], [column_5], [column_6], [column_7], [column_8], [column_9], [column_10], [column_11], [column_12]) VALUES (14, N'ABER91U1', 2, 6, 2020, 1, 3, N'ABER91U1', N'Unit 1, 91 Aberdeen Street', N'NEWSBO', N'News Book Pty Ltd ', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL)
INSERT [dbo].[mgmt_sub_report_table_data] ([id], [property_code], [sub_report_id], [cal_period], [cal_year], [table_id], [row_no], [column_1], [column_2], [column_3], [column_4], [column_5], [column_6], [column_7], [column_8], [column_9], [column_10], [column_11], [column_12]) VALUES (15, N'ATWE46', 2, 6, 2020, 2, 1, N'CAMERA', N'Camera Dynamicstest SAMPLE TEST', N'Unit 4, 46 Atwell Street LOCATION', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL)
INSERT [dbo].[mgmt_sub_report_table_data] ([id], [property_code], [sub_report_id], [cal_period], [cal_year], [table_id], [row_no], [column_1], [column_2], [column_3], [column_4], [column_5], [column_6], [column_7], [column_8], [column_9], [column_10], [column_11], [column_12]) VALUES (16, N'ATWE46', 2, 6, 2020, 2, 2, N'DAVION', N'J&R Hancock Pty Ltd T/A Davion Enterpris', N'Unit 3, 46 Atwell Street', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL)
SET IDENTITY_INSERT [dbo].[mgmt_sub_report_table_data] OFF
ALTER TABLE [dbo].[mgmt_sub_report_table_column_config] ADD  DEFAULT ((0)) FOR [is_readonly]
GO
ALTER TABLE [dbo].[mgmt_sub_report_table_column_config] ADD  DEFAULT ((0)) FOR [max_characters]
GO



update mgmt_sub_report_comment_config
set default_font_format = 1
where comment_description in ('External','Centre','Marketing')

update mgmt_sub_report_table_config
set has_action_btns = 1
where sub_report_id in ( select sub_report_id from mgmt_sub_report
where sub_report_file_path in ('propertyReport/Lease_Equity/detailedArrearsReport.php' , 'propertyReport/Lease_Equity/rentReviewReport.php' ) )


delete from mgmt_sub_report_table_column_config where column_label = 'Since Paid' and sub_report_id in ( select sub_report_id from mgmt_sub_report
where sub_report_file_path = 'propertyReport/Lease_Equity/detailedArrearsReport.php' )

update mgmt_sub_report_table_column_config set column_id = 6
  where column_label = 'Total' and column_id = 7

  update mgmt_sub_report_table_config set is_carry_over = 1 where table_description = ' Public Liability Insurance Claims'

delete from mgmt_sub_report_upload_config
where upload_description = 'Upload Image 2'
and sub_report_id in ( select sub_report_id from mgmt_sub_report
where mgmt_sub_report_name = 'Marketing Plan' )  	

 update mgmt_sub_report_table_config set function_call = 'dbGetThreeComments'
 where table_description = 'Bank Guarantee(Input)'

  
delete  mgmt_sub_report_table_column_config
where table_id in ( select table_id from mgmt_sub_report_table_config where table_description = 'Bank Guarantee(Input)')
and column_label = 'Expiry Date'

----copy new sub report
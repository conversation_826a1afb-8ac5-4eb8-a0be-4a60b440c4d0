/*
Navicat SQL Server Data Transfer

Source Server         : Cirrus8-home
Source Server Version : 150000
Source Host           : DESKTOP-GJJGRJ9\SQLEXPRESS01:1433
Source Database       : npms
Source Schema         : dbo

Target Server Type    : SQL Server
Target Server Version : 150000
File Encoding         : 65001

Date: 2020-03-30 12:14:03
*/


-- ----------------------------
-- Table structure for system_alerts
-- ----------------------------
DROP TABLE [dbo].[system_alerts]
GO
CREATE TABLE [dbo].[system_alerts] (
[id] int NOT NULL IDENTITY(1,1) ,
[title] varchar(225) NOT NULL ,
[description] text NOT NULL ,
[url] varchar(225) NULL ,
[for_roles] varchar(25) NOT NULL ,
[is_active] bit NOT NULL DEFAULT ((0)), 
[created_at] datetime NOT NULL ,
[created_by] varchar(100) NULL ,
[deleted_at] datetime NULL ,
[updated_at] datetime NULL 
)


GO
DBCC CHECKIDENT(N'[dbo].[system_alerts]', RESEED, 2)
GO

-- ----------------------------
-- Indexes structure for table system_alerts
-- ----------------------------

-- ----------------------------
-- Primary Key structure for table system_alerts
-- ----------------------------
ALTER TABLE [dbo].[system_alerts] ADD PRIMARY KEY ([id])
GO

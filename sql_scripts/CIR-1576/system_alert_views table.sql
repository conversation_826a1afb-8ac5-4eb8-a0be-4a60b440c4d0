/*
Navicat SQL Server Data Transfer

Source Server         : Cirrus8-home
Source Server Version : 150000
Source Host           : DESKTOP-GJJGRJ9\SQLEXPRESS01:1433
Source Database       : npms
Source Schema         : dbo

Target Server Type    : SQL Server
Target Server Version : 150000
File Encoding         : 65001

Date: 2020-03-30 14:35:19
*/


-- ----------------------------
-- Table structure for system_alert_views
-- ----------------------------
DROP TABLE [dbo].[system_alert_views]
GO
CREATE TABLE [dbo].[system_alert_views] (
[id] int NOT NULL IDENTITY(1,1) ,
[alert_id] int NOT NULL ,
[user_id] int NOT NULL ,
[is_seen] bit NOT NULL DEFAULT ((0)) ,
[is_dismissed] bit NOT NULL DEFAULT ((0)) ,
[seen_at] datetime NULL ,
[dismissed_at] datetime NULL ,
[database_id] int NULL 
)


GO

-- ----------------------------
-- Indexes structure for table system_alert_views
-- ----------------------------

-- ----------------------------
-- Primary Key structure for table system_alert_views
-- ----------------------------
ALTER TABLE [dbo].[system_alert_views] ADD PRIMARY KEY ([id])
GO


ALTER TABLE [dbo].[pmpj_p_phone]
ADD [ID] INT NOT NULL IDENTITY;

ALTER TABLE [dbo].[pmlj_l_phone]
ADD [ID] INT NOT NULL IDENTITY;
ALTER TABLE [dbo].[temp_pmlj_l_phone]
ADD [ID] INT NOT NULL IDENTITY;

ALTER TABLE [dbo].[pmcj_c_phone]
ADD [ID] INT NOT NULL IDENTITY;
ALTER TABLE [dbo].[temp_pmcj_c_phone]
ADD [ID] INT NOT NULL IDENTITY;

ALTER TABLE [dbo].[pmco_company]
ADD [ID] INT NOT NULL IDENTITY;
ALTER TABLE [dbo].[temp_pmco_company]
ADD [ID] INT NOT NULL IDENTITY;

CREATE TABLE [dbo].[email_address_list] (
  [ID] INT NOT NULL IDENTITY PRIMARY KEY,
  [created_at] DATETIME NOT NULL DEFAULT (GETDATE()),
  [created_by] INT,
  [contact_table_name] VARCHAR(20),--tells what contact table
  [contact_id] INT,
  [email_purpose_param] VARCHAR(12),--
  [email_address] VARCHAR(100)--email address
)

DELETE FROM pmzt_par_type WHERE pmzt_par_type IN ('OWN_GEN','TEN_GEN','SUP_GEN');
DELETE FROM pmzz_param WHERE pmzz_par_type IN ('OWN_GEN','TEN_GEN','SUP_GEN');


DELETE FROM pmzt_par_type WHERE

INSERT INTO pmzt_par_type(pmzt_par_type,pmzt_desc) VALUES('OWN_GEN','Owner');
INSERT INTO pmzt_par_type(pmzt_par_type,pmzt_desc) VALUES('TEN_GEN','Tenant');
INSERT INTO pmzt_par_type(pmzt_par_type,pmzt_desc) VALUES('SUP_GEN','Supplier');
INSERT INTO pmzz_param(pmzz_par_type,pmzz_code,pmzz_desc) VALUES('OWN_GEN','OWNRPT','Owner Report') ;
INSERT INTO pmzz_param(pmzz_par_type,pmzz_code,pmzz_desc) VALUES('OWN_GEN','OWNREM','Remittance') ;
INSERT INTO pmzz_param(pmzz_par_type,pmzz_code,pmzz_desc) VALUES('OWN_GEN','OWNGEN','General Letters (Owner)') ;
INSERT INTO pmzz_param(pmzz_par_type,pmzz_code,pmzz_desc) VALUES('TEN_GEN','TENTAXIN','Tax Invoice (Interim Only)') ;
INSERT INTO pmzz_param(pmzz_par_type,pmzz_code,pmzz_desc) VALUES('TEN_GEN','TENTAXINF','Tax Invoice (Not Interim)') ;
INSERT INTO pmzz_param(pmzz_par_type,pmzz_code,pmzz_desc) VALUES('TEN_GEN','TENARRS','Arrears') ;
INSERT INTO pmzz_param(pmzz_par_type,pmzz_code,pmzz_desc) VALUES('TEN_GEN','TENRENREV','Rent Reviews') ;
INSERT INTO pmzz_param(pmzz_par_type,pmzz_code,pmzz_desc) VALUES('TEN_GEN','TENBUDGET','Budget') ;
INSERT INTO pmzz_param(pmzz_par_type,pmzz_code,pmzz_desc) VALUES('TEN_GEN','TENGEN','General Letters (Tenant)') ;
INSERT INTO pmzz_param(pmzz_par_type,pmzz_code,pmzz_desc) VALUES('SUP_GEN','SUPREM','Remittance') ;
INSERT INTO pmzz_param(pmzz_par_type,pmzz_code,pmzz_desc) VALUES('SUP_GEN','SUPGEN','General Letters (Supplier)') ;

INSERT INTO pmzz_param(pmzz_par_type,pmzz_code,pmzz_desc) VALUES('OWN_GEN','OWNTAXGEN','Tax Invoice (General)') ;

delete from pmzz_param where pmzz_code = 'OWNTAXGEN'


--Default company owner
TRUNCATE TABLE email_address_list;
INSERT INTO email_address_list(created_by,contact_table_name,contact_id,email_purpose_param,email_address)
SELECT 1,'pmco_company',ID,'',pmco_email FROM pmco_company WHERE pmco_owner = 1 AND pmco_email <> '';

INSERT INTO email_address_list(created_by,contact_table_name,contact_id,email_purpose_param,email_address)
SELECT 1,'pmco_company',ID,'OWNRPT',pmco_email FROM pmco_company WHERE pmco_owner = 1 AND pmco_email <> '';
INSERT INTO email_address_list(created_by,contact_table_name,contact_id,email_purpose_param,email_address)
SELECT 1,'pmco_company',ID,'OWNREM',pmco_email FROM pmco_company WHERE pmco_owner = 1 AND pmco_email <> '';
INSERT INTO email_address_list(created_by,contact_table_name,contact_id,email_purpose_param,email_address)
SELECT 1,'pmco_company',ID,'OWNGEN',pmco_email FROM pmco_company WHERE pmco_owner = 1 AND pmco_email <> '';

--Default company tenant
INSERT INTO email_address_list(created_by,contact_table_name,contact_id,email_purpose_param,email_address)
SELECT 1,'pmco_company',ID,'TENTAXIN',pmco_email FROM pmco_company WHERE pmco_debtor = 1 AND pmco_email <> '';
INSERT INTO email_address_list(created_by,contact_table_name,contact_id,email_purpose_param,email_address)
SELECT 1,'pmco_company',ID,'TENARRS',pmco_email FROM pmco_company WHERE pmco_debtor = 1 AND pmco_email <> '';
INSERT INTO email_address_list(created_by,contact_table_name,contact_id,email_purpose_param,email_address)
SELECT 1,'pmco_company',ID,'TENRENREV',pmco_email FROM pmco_company WHERE pmco_debtor = 1 AND pmco_email <> '';
INSERT INTO email_address_list(created_by,contact_table_name,contact_id,email_purpose_param,email_address)
SELECT 1,'pmco_company',ID,'TENBUDGET',pmco_email FROM pmco_company WHERE pmco_debtor = 1 AND pmco_email <> ''
INSERT INTO email_address_list(created_by,contact_table_name,contact_id,email_purpose_param,email_address)
SELECT 1,'pmco_company',ID,'TENGEN',pmco_email FROM pmco_company WHERE pmco_debtor = 1 AND pmco_email <> ''

--Default company supplier
INSERT INTO email_address_list(created_by,contact_table_name,contact_id,email_purpose_param,email_address)
SELECT 1,'pmco_company',ID,'SUPREM',pmco_email FROM pmco_company WHERE pmco_supplier = 1 AND pmco_email <> '';
INSERT INTO email_address_list(created_by,contact_table_name,contact_id,email_purpose_param,email_address)
SELECT 1,'pmco_company',ID,'SUPGEN',pmco_email FROM pmco_company WHERE pmco_supplier = 1 AND pmco_email <> '';


INSERT INTO pmzt_par_type(pmzt_par_type,pmzt_desc)
VALUES('EMAILCEN','Email Centralisation');
INSERT INTO pmzz_param(pmzz_par_type,pmzz_code,pmzz_desc)
VALUES('EMAILCEN','SENDMAIL','1');
INSERT INTO pmzz_param(pmzz_par_type,pmzz_code,pmzz_desc)
VALUES('EMAILCEN','SETUPEMAIL','1');



UPDATE pmzz_param SET pmzz_desc = 'Invoices & Receipts'
WHERE pmzz_code = 'TENTAXIN'
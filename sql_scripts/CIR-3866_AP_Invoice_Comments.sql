/**
*
* Run on all client DB
*
**/

CREATE TABLE invoice_line_notes
(
    id INT IDENTITY(1,1) PRIMARY KEY,
    invoice_batch_number VARCHAR(100) NOT NULL,
    invoice_line_number INT NOT NULL,
    note NVARCHAR(MAX),
    version INT NOT NULL DEFAULT 1,
    updated_by VARCHAR(100) NULL DEFAULT NULL,
    created_at DATETIME2(4) NOT NULL DEFAULT (GETDATE()),
    updated_at DATETIME2(4) NULL,
    deleted_at DATETIME2(4) NULL
);

/**
*
* Alter temp_ocr_ap_line_ai and temp_ocr_ap_line_pm to add a new field: comments -> which will save ap line comments while the automated invoice is still for approval
* Once the automated invoice is approved, it will be also saved in the link table and note table (with the ap_batch_nr provided)
*
**/
ALTER TABLE temp_ocr_ap_line_ai ADD comments NVARCHAR(MAX) NULL;
ALTER TABLE temp_ocr_ap_line_pm ADD comments NVARCHAR(MAX) NULL;

/**
*
* Run on ALL CLIENT DBs
* trx_type: 1 if CAAPS and 2 if Flexicapture (or others)
*
**/

DROP TABLE ai_comparison_detail;
CREATE TABLE ai_comparison_detail
(
    id INT IDENTITY(1,1),
    main_ai_id INT NULL,
    fc_batch_id VARCHAR(50),
    ocr_invoice_no VARCHAR(50) NULL,
    fin_invoice_no VARCHAR(50) NULL,
    ocr_invoice_date DATE NULL,
    fin_invoice_date DATE NULL,
    ocr_due_date DATE NULL,
    fin_due_date DATE NULL,
    ocr_supplier_code VARCHAR(50) NULL,
    fin_supplier_code VARCHAR(50) NULL,
    ocr_bpay_reference VARCHAR(50) NULL,
    fin_bpay_reference VARCHAR(50) NULL,
    ocr_bpay_crn VARCHAR(50) NULL,
    fin_bpay_crn VARCHAR(50) NULL,
    ocr_property_code VARCHAR(50) NULL,
    fin_property_code VARCHAR(50) NULL,
    ocr_account_code VARCHAR(50) NULL,
    fin_account_code VARCHAR(50) NULL,
    ocr_line_items VARCHAR(MAX) NULL,
    created_at DATETIME NOT NULL DEFAULT (GETDATE()),
    trx_type INT NULL,
    temp_ocr_ap_pm_id INT NULL,
    processed_at DATETIME NULL
);

ALTER TABLE temp_ocr_ap_pm ADD has_caaps_changes TINYINT NOT NULL DEFAULT (0);
ALTER TABLE temp_ocr_ap_pm ADD has_c8_changes TINYINT NOT NULL DEFAULT (0);

--NOTE: Only run the script below if temp_ocr_ap_pm_id IS NOT YET EXISTING in your ai_comparison_detail table
ALTER TABLE ai_comparison_detail ADD temp_ocr_ap_pm_id INT NULL;


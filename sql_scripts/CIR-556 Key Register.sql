DROP TABLE IF EXISTS [dbo].[pmpk_p_key]
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[pmpk_p_key](
	[pmpk_id] [int] IDENTITY(1,1) NOT NULL,
	[pmpk_prop] [char](10) NOT NULL,
	[pmpk_key] [char](20) NOT NULL,
	[pmpk_desc] [char](40) NOT NULL,
	[pmpk_status] [char](1) NOT NULL,
	[pmpk_deleted_at] [datetime] NULL,
	[pmpk_detail_id] [int] NOT NULL DEFAULT 0,
 CONSTRAINT [pmpk_p_key_pk] PRIMARY KEY CLUSTERED 
(
	[pmpk_id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 90) ON [PRIMARY]
) ON [PRIMARY]
GO


DROP TABLE IF EXISTS [dbo].[pmkd_key_detail]
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[pmkd_key_detail](
	[pmkd_id] [int] IDENTITY(1,1) NOT NULL,
	[pmkd_prop] [char](10) NOT NULL,
	[pmkd_key] [char](20) NOT NULL,
	[pmkd_key_taker] [char](40) NOT NULL,
	[pmkd_checked_out] [date] NULL,
	[pmkd_return_due] [date] NULL,
	[pmkd_date_returned] [date] NULL,
	
 CONSTRAINT [pmkd_key_detail_pk] PRIMARY KEY CLUSTERED 
(
	[pmkd_id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 90) ON [PRIMARY]
) ON [PRIMARY]
GO

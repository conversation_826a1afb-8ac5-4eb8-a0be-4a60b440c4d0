/* done on dev and live */

ALTER TABLE [pmpt_p_contact]
ADD [pmpt_primary] BIT DEFAULT 0;

ALTER TABLE [pmlt_l_contact]
ADD [pmlt_primary] BIT DEFAULT 0;

ALTER TABLE [pmct_c_contact]
ADD [pmct_primary] BIT DEFAULT 0;

/*================= run after the ones above ===================================*/

update pmpt_p_contact set pmpt_primary = 0;
update pmlt_l_contact set pmlt_primary = 0;
update temp_pmlt_l_contact set pmlt_primary = 0;
update pmct_c_contact set pmct_primary = 0;

update pmpt_p_contact set pmpt_primary = 1 where pmpt_serial = 1;
update pmlt_l_contact set pmlt_primary = 1 where pmlt_serial = 1;
update temp_pmlt_l_contact set pmlt_primary = 1 where pmlt_serial = 1;
update pmct_c_contact set pmct_primary = 1 where pmct_serial = 1;

/*================= run after the ones above ===================================*/

CREATE TABLE [lease_status_logs] (
  [ID] INT NOT NULL IDENTITY PRIMARY KEY,
  [created_at] DATETIME NOT NULL DEFAULT (GETUTCDATE()),
  [created_by] INT,
  [property_code] VARCHAR(11),
  [lease_code] VARCHAR(11),
  [lease_status] VARCHAR(10)
);

INSERT INTO lease_status_logs (property_code,lease_code,lease_status,created_at,created_by)
SELECT pmle_prop,pmle_lease,'CREATED',pmle_create_dt,user_created FROM pmle_lease
WHERE pmle_create_dt is not null AND user_created is not null;


CREATE TABLE [property_man_fee_acc_budget] (
  [ID] INT NOT NULL IDENTITY PRIMARY KEY,
  [created_at] DATETIME NOT NULL DEFAULT (GETDATE()),
  [financial_year] varchar(4) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
  [property_code] varchar(10) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
  [account_code_from] varchar(10) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
  [man_fee_pct] decimal(19,2) NULL,
  [account_code_to] varchar(10) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
  [man_fee_desc] varchar(40) COLLATE SQL_Latin1_General_CP1_CI_AS NULL
);

CREATE TABLE [property_man_fee_budget] (
  [ID] INT NOT NULL IDENTITY PRIMARY KEY,
  [created_at] DATETIME NOT NULL DEFAULT (GETDATE()),
  [financial_year] INT,
  [property_code] varchar(10) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
  [account_code] varchar(10) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
  [man_method_type] INT NULL,
  [man_fee_val] decimal(19,2) NULL,
  [man_fee_pct] decimal(19,2) NULL,
);

CREATE TABLE [property_budget_expense_breakdown] (
  [ID] INT NOT NULL IDENTITY PRIMARY KEY,
  [created_at] DATETIME NOT NULL DEFAULT (GETDATE()),
  [financial_year] int,
  [property_code] varchar(10) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
  [lease_code] varchar(10) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
  [lease_name] varchar(100) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
  [floor_code] varchar(10) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
  [floor_name] varchar(100) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
  [unit_code] varchar(10) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
  [unit_name] varchar(100) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
  [unit_area] decimal(19,2) NULL,
  [unit_status] varchar(10) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
  [account_code] varchar(10) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
  [input_type] int,
  [split_type] int NULL,
  [custom_value] decimal(19,2) NULL,
  [custom_percentage] decimal(19,2) NULL,
  [status] int,
);

CREATE TABLE [property_budget_activity_logs] (
  [ID] INT NOT NULL IDENTITY PRIMARY KEY,
  [created_at] DATETIME NOT NULL DEFAULT (GETDATE()),
  [financial_year] int,
  [property_code] varchar(10) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
  [act_desc] varchar(150) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
  [act_comment] varchar(150) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
  [created_by] varchar(100) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL
);
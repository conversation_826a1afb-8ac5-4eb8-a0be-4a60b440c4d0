--per client
CREATE TABLE [dbo].[retail_benchmarks_fine](
	[fineCategoryID] [int] NOT NULL,
	[noStores] [numeric](6, 1) NULL,
	[glaPerCentre] [numeric](6, 0) NULL,
	[turnoverPerCentre] [numeric](18, 0) NULL,
	[glaPerStore] [numeric](6, 0) NULL,
	[turnoverPerStore] [numeric](18, 0) NULL,
	[glaPercentage] [numeric](4, 1) NULL,
	[turnoverPercentage] [numeric](4, 1) NULL,
	[occupancyCostArea] [numeric](6, 0) NULL,
	[occupancyCostPercentage] [numeric](4, 1) NULL,
	[benchmarkID] [int] NOT NULL IDENTITY(1,1) PRIMARY KEY,
	[year] [numeric](4, 0) NULL
) ON [PRIMARY]


CREATE TABLE [dbo].[retail_benchmarks_cat](
	[categoryID] [int] NOT NULL,
	[noStores] [numeric](6, 0) NULL,
	[glaPerCentre] [numeric](6, 0) NULL,
	[turnoverPerCentre] [numeric](18, 0) NULL,
	[glaPerStore] [numeric](6, 0) NULL,
	[turnoverPerStore] [numeric](18, 0) NULL,
	[glaPercentage] [numeric](4, 1) NULL,
	[turnoverPercentage] [numeric](4, 1) NULL,
	[occupancyCostArea] [numeric](6, 0) NULL,
	[occupancyCostPercentage] [numeric](4, 1) NULL,
	[benchmarkID] [int] NOT NULL  IDENTITY(1,1) PRIMARY KEY,
	[year] [numeric](4, 0) NULL
) ON [PRIMARY]


CREATE TABLE [dbo].[retail_benchmarks_sub](
	[subCategoryID] [int] NOT NULL,
	[noStores] [numeric](6, 1) NULL,
	[glaPerCentre] [numeric](6, 0) NULL,
	[turnoverPerCentre] [numeric](18, 0) NULL,
	[glaPerStore] [numeric](6, 0) NULL,
	[turnoverPerStore] [numeric](18, 0) NULL,
	[glaPercentage] [numeric](4, 1) NULL,
	[turnoverPercentage] [numeric](4, 1) NULL,
	[occupancyCostArea] [numeric](6, 0) NULL,
	[occupancyCostPercentage] [numeric](4, 1) NULL,
	[benchmarkID] [int] NOT NULL IDENTITY(1,1) PRIMARY KEY,
	[year] [numeric](4, 0) NULL
) ON [PRIMARY]



delete from retail_fine_category;
delete from retail_category;
delete from retail_sub_category;
delete from retail_benchmarks_fine;
delete from retail_benchmarks_cat;
delete from retail_benchmarks_sub;


DROP TABLE [dbo].[retail_sub_category]
 
CREATE TABLE [dbo].[retail_sub_category](
	[subCategoryID] [int] NOT NULL IDENTITY(1,1) PRIMARY KEY,
	[categoryID] [int] NOT NULL,
	[subCategoryName] [varchar](50) NULL
) ON [PRIMARY]

ALTER TABLE [dbo].[retail_category]
ADD [categorySeq] INT  NULL ;

ALTER TABLE [dbo].[retail_sub_category]
ADD [subCategorySeq] INT  NULL ;

ALTER TABLE [dbo].[retail_fine_category]
ADD [fineCategorySeq] INT  NULL ;




Insert into retail_benchmarks_fine(fineCategoryID,year,occupancyCostPercentage) select fineCategoryID, year,occupancyCostPercentage from npms.dbo.retail_benchmarks_fine;
Insert into retail_benchmarks_cat(categoryID,year,occupancyCostPercentage) select categoryID, year,occupancyCostPercentage from npms.dbo.retail_benchmarks_cat;
Insert into retail_benchmarks_sub(subCategoryID,year,occupancyCostPercentage) select subCategoryID, year,occupancyCostPercentage from npms.dbo.retail_benchmarks_sub;


SET IDENTITY_INSERT [dbo].retail_fine_category ON ;
Insert into retail_fine_category(fineCategoryID,fineCategoryName,subCategoryID)  select fineCategoryID,fineCategoryName,subCategoryID from npms.dbo.retail_fine_category;
SET IDENTITY_INSERT [dbo].retail_fine_category OFF ;

SET IDENTITY_INSERT [dbo].retail_category ON ;
Insert into retail_category(categoryID,categoryName) select categoryID,categoryName from npms.dbo.retail_category;
SET IDENTITY_INSERT [dbo].retail_category OFF ;

SET IDENTITY_INSERT [dbo].retail_sub_category ON ;
Insert into retail_sub_category(subCategoryID,categoryID,subCategoryName) select subCategoryID,categoryID,subCategoryName from npms.dbo.retail_sub_category;
SET IDENTITY_INSERT [dbo].retail_sub_category OFF ;

update retail_category set categorySeq = categoryID
update retail_sub_category set subCategorySeq = subCategoryID
update retail_fine_category set fineCategorySeq = fineCategoryID	
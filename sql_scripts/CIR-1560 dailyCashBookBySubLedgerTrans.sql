DROP VIEW dailyCashBookBySubLedgerTrans;

CREATE VIEW dailyCashBookBySubLedgerTrans
AS

SELECT *
FROM
(
    SELECT 'receipt' transactionType, 1 typeOrder, 
		CONVERT(char(10), t1.pmxd_alloc_dt, 103) transactionDate, 
		pmxd_alloc_dt dateSort, 
		t1.pmxd_prop propertyID, 
		t1.pmxd_acc accountID, 
		t1.pmxd_lease leaseSupplierID, 
		(SELECT TOP 1 t4.pmle_name 
			FROM pmle_lease t4 
			WHERE t4.pmle_prop=t1.pmxd_prop 
				AND t4.pmle_lease=t1.pmxd_lease) leaseSupplierName, 
		t2.description description, 
		ROUND((t1.pmxd_alloc_amt - t1.pmxd_tax_amt) * -1, 2) netAmount, 
		t1.pmxd_tax_amt * -1 gstAmount, 
		t1.pmxd_alloc_amt * -1 totalAmount, 
		CONVERT(char(10), t2.spare_date_1, 103) fromDate, 
		CONVERT(char(10), t2.spare_date_2, 103) toDate, 
		t3.spare_1 receiptNumber,
		'' receiptNumber2, 
		t2.ref_1 chequeNumber, 
		t2.seq sequenceNumber, 
		t2.batch_nr paymentsExtraLine, 
		t3.ref_1 paymentReference, 
		'A' orderSequence, 
		t1.pmxd_create_date cDate,
		t1.pmxd_create_time cTime, 
		t2.batch_nr batchNumber, 
		t2.batch_line_nr lineNumber, 
		t1.pmxd_s_debtor debtorID,
		'' supplierID,
		'' supplierName
	FROM pmxd_ar_alloc t1 
	INNER JOIN ar_transaction t2 ON 
		(t2.batch_nr=t1.pmxd_t_batch AND t2.batch_line_nr=t1.pmxd_t_line) 
	INNER JOIN ar_transaction t3 ON 
		(t3.batch_nr = t1.pmxd_f_batch AND t3.batch_line_nr = t1.pmxd_f_line) 
	WHERE t1.pmxd_f_type='CSH' 

	UNION ALL 

	SELECT 'payment' transactionType, 2 typeOrder, 
		CONVERT(char(10), t2.pmxc_alloc_dt, 103) transactionDate, 
		t2.pmxc_alloc_dt dateSort, 
		t2.pmxc_prop propertyID, 
		t2.pmxc_acc accountID, 
		t2.pmxc_lease leaseSupplierID, 
		(SELECT TOP 1 t4.pmle_name 
			FROM pmle_lease t4 
			WHERE t4.pmle_prop=t2.pmxc_prop 
				AND t4.pmle_lease=t2.pmxc_lease) leaseSupplierName, 
		t1.description description, 
		t2.pmxc_alloc_amt + t2.pmxc_tax_amt netAmount, -1 * t2.pmxc_tax_amt gstAmount, 
		t2.pmxc_alloc_amt totalAmount, 
		CONVERT(char(10), t1.spare_date_1, 103) fromDate, 
		CONVERT(char(10), t1.spare_date_2, 103) toDate, 
		t1.ref_1 receiptNumber, 
		t3.ref_1 receiptNumber2,
		t1.ref_1 chequeNumber, 
		t2.pmxc_f_line sequenceNumber, 
		t2.pmxc_f_batch paymentsExtraLine, 
		'' paymentReference, 
		'B' orderSequence,
		t2.pmxc_create_date cDate, 
		t2.pmxc_create_time cTime, 
		t1.batch_nr batchNumber, 
		t1.batch_line_nr lineNumber, 
		'' debtorID,
		t1.supplier_code supplierID,
		(SELECT TOP 1 t5.pmco_name 
			FROM pmco_company t5
			WHERE t5.pmco_code=t1.supplier_code) supplierName
		
	FROM ap_transaction t1 
	INNER JOIN pmxc_ap_alloc t2 ON 
		(t2.pmxc_t_batch=t1.batch_nr AND t2.pmxc_t_line=t1.batch_line_nr)
	INNER JOIN ap_transaction t3 ON
		(t3.batch_nr = t2.pmxc_f_batch
		AND t3.batch_line_nr = t2.pmxc_f_line)
	WHERE t2.pmxc_f_type='PAY' 
) x
ALTER TABLE temp_standing_charges
ADD serial NUMERIC (12,0);

DROP VIEW v_standing_charges;

CREATE VIEW v_standing_charges AS
SELECT     batchID, propertyID, leaseID, transactionID, accountID, description, transactionAmount, processStatus, frequency, CONVERT(char(10), fromDate, 103) AS fromDate,
           CONVERT(char(10), toDate, 103) AS toDate, taxAmount, netAmount,
           ISNULL((SELECT     TOP 1 transactionAmount
                   FROM          temp_standing_charges AS t
                   WHERE      (accountID = s.accountID) AND (propertyID = s.propertyID) AND (leaseID = s.leaseID) AND (toDate = DATEADD(d, - 1, s.fromDate))
                     AND processStatus = 0
                  ),0) AS lastAmount,
           sequence, debtorID, transactionDate, transactionType, bankID, unitID, taxCode, transactionYear, transactionPeriod, transactionStatus, reportID, comments, serial
FROM         temp_standing_charges AS s

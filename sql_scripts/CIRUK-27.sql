-- all client db
UPDATE pmas_acc_subgrp
set pmas_desc = REPLACE(
        REPLACE(
                pmas_desc COLLATE Latin1_General_CS_AS,
                'Variable Outgoing' COLLATE Latin1_General_CS_AS,
                'Service Charge'
        ),
        'VARIABLE OUTGOINGS' COLLATE Latin1_General_CS_AS,
        'SERVICE CHARGE'
                )
WHERE pmas_desc LIKE 'Variable outgoing%'

------------

UPDATE gl_account_groups
set gl_group_description = REPLACE(
        REPLACE(
                gl_group_description COLLATE Latin1_General_CS_AS,
                'Variable Outgoings' COLLATE Latin1_General_CS_AS,
                'Service Charge'
        ),
        'VARIABLE OUTGOINGS' COLLATE Latin1_General_CS_AS,
        'SERVICE CHARGE'
                           )
WHERE gl_group_description LIKE 'Variable outgoing%'

------

UPDATE pmat_acc_sub_type
set pmat_desc = REPLACE(
        pmat_desc,
        'VARIABLE OUTGOINGS' COLLATE Latin1_General_CS_AS,
        'SERVICE CHARGE'
                )
WHERE pmat_desc LIKE 'Variable outgoing%';

------

UPDATE pmat_acc_sub_type
set pmat_head_text = REPLACE(
        pmat_head_text,
        'VARIABLE OUTGOINGS' COLLATE Latin1_General_CS_AS,
        'SERVICE CHARGE'
                     )
WHERE pmat_head_text LIKE 'Variable outgoing%';

------

UPDATE pmat_acc_sub_type
set pmat_tot_text = REPLACE(
        pmat_tot_text,
        'VARIABLE OUTGOINGS' COLLATE Latin1_General_CS_AS,
        'SERVICE CHARGE'
                    )
WHERE pmat_tot_text LIKE 'Variable outgoing%';

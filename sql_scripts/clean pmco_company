CREATE TABLE pmco_company2(
	[pmco_code] [char](10) NOT NULL UNIQUE,
	[pmco_name] [varchar](255) NULL,
	[pmco_street] [char](80) NULL,
	[pmco_city] [char](40) NULL,
	[pmco_state] [char](8) NULL,
	[pmco_postcode] [char](8) NULL,
	[pmco_dir_bank] [bit] NULL,
	[pmco_b_bsb] [char](10) NULL,
	[pmco_b_acc_no] [char](15) NULL,
	[pmco_b_acc_name] [char](40) NULL,
	[pmco_b_name] [char](40) NULL,
	[pmco_pps_no] [char](20) NULL,
	[pmco_gst_no] [char](22) NULL,
	[pmco_gst_code] [char](10) NULL,
	[pmco_owner] [bit] NULL,
	[pmco_o_pay_acc] [char](10) NULL,
	[pmco_o_chart] [bit] NULL,
	[pmco_supplier] [bit] NULL,
	[pmco_s_type] [char](10) NULL,
	[pmco_s_agent] [bit] NULL,
	[pmco_debtor] [bit] NULL,
	[pmco_d_chq_days] [numeric](2, 0) NULL,
	[pmco_e_company] [char](10) NULL,
	[pmco_e_debtor] [char](10) NULL,
	[pmco_e_customer] [char](10) NULL,
	[pmco_e_supplier] [char](10) NULL,
	[pmco_e_creditor] [char](10) NULL,
	[pmco_pay_pri] [numeric](2, 0) NULL,
	[pmco_eft_ref] [numeric](11, 0) NULL,
	[pmco_os_copies] [numeric](5, 0) NULL,
	[pmco_gst_exempt] [bit] NULL,
	[pmco_recharge] [bit] NULL,
	[pmco_country] [char](10) NULL,
	[pmco_o_inc_acc] [char](10) NULL,
	[pmco_active] [bit] NULL DEFAULT 1,
	[pmco_update_dt] [datetime] NULL,
	[pmco_update_user] [char](50) NULL,
	[pmco_create_dt] [datetime] NULL,
	[pmco_create_user] [char](50) NULL,
	[pmco_email] [varchar](400) NULL,
	[pmco_invoice_mask] [char](16) NULL,
	[pmco_bpay_ref] [char](10) NULL,
	[pmco_pay_method] [tinyint] NOT NULL DEFAULT 3,
	[pmco_invoice_address] [char](400) NULL,
	[pmco_disable_invoice_dates] [bit] NULL
);

------------------------------------------------------------------------------------
INSERT INTO pmco_company2([pmco_code]
                         ,[pmco_name]
                         ,[pmco_street]
                         ,[pmco_city]
                         ,[pmco_state]
                         ,[pmco_postcode]
                         ,[pmco_dir_bank]
                         ,[pmco_b_bsb]
                         ,[pmco_b_acc_no]
                         ,[pmco_b_acc_name]
                         ,[pmco_b_name]
                         ,[pmco_pps_no]
                         ,[pmco_gst_no]
                         ,[pmco_gst_code]
                         ,[pmco_owner]
                         ,[pmco_o_pay_acc]
                         ,[pmco_o_chart]
                         ,[pmco_supplier]
                         ,[pmco_s_type]
                         ,[pmco_s_agent]
                         ,[pmco_debtor]
                         ,[pmco_d_chq_days]
                         ,[pmco_e_company]
                         ,[pmco_e_debtor]
                         ,[pmco_e_customer]
                         ,[pmco_e_supplier]
                         ,[pmco_e_creditor]
                         ,[pmco_pay_pri]
                         ,[pmco_eft_ref]
                         ,[pmco_os_copies]
                         ,[pmco_gst_exempt]
                         ,[pmco_recharge]
                         ,[pmco_country]
                         ,[pmco_o_inc_acc]
                         ,[pmco_active]
                         ,[pmco_update_dt]
                         ,[pmco_update_user]
                         ,[pmco_create_dt]
                         ,[pmco_create_user]
                         ,[pmco_email]
                         ,[pmco_invoice_mask]
                         ,[pmco_bpay_ref]
                         ,[pmco_pay_method]
                         ,[pmco_invoice_address]
                         ,[pmco_disable_invoice_dates])
SELECT [pmco_code]
      ,[pmco_name]
      ,[pmco_street]
      ,[pmco_city]
      ,[pmco_state]
      ,[pmco_postcode]
      ,[pmco_dir_bank]
      ,[pmco_b_bsb]
      ,[pmco_b_acc_no]
      ,[pmco_b_acc_name]
      ,[pmco_b_name]
      ,[pmco_pps_no]
      ,[pmco_gst_no]
      ,[pmco_gst_code]
      ,[pmco_owner]
      ,[pmco_o_pay_acc]
      ,[pmco_o_chart]
      ,[pmco_supplier]
      ,[pmco_s_type]
      ,[pmco_s_agent]
      ,[pmco_debtor]
      ,[pmco_d_chq_days]
      ,[pmco_e_company]
      ,[pmco_e_debtor]
      ,[pmco_e_customer]
      ,[pmco_e_supplier]
      ,[pmco_e_creditor]
      ,[pmco_pay_pri]
      ,[pmco_eft_ref]
      ,[pmco_os_copies]
      ,[pmco_gst_exempt]
      ,[pmco_recharge]
      ,[pmco_country]
      ,[pmco_o_inc_acc]
      ,[pmco_active]
      ,[pmco_update_dt]
      ,[pmco_update_user]
      ,[pmco_create_dt]
      ,[pmco_create_user]
      ,[pmco_email]
      ,[pmco_invoice_mask]
      ,[pmco_bpay_ref]
      ,[pmco_pay_method]
      ,[pmco_invoice_address]
      ,[pmco_disable_invoice_dates]
FROM
(SELECT ROW_NUMBER() OVER (PARTITION BY pmco_code ORDER BY pmco_code DESC) AS rn,*
FROM pmco_company WHERE pmco_code != '') x WHERE rn = 1

------------------------------------------------------------------------------------


EXEC sp_rename 'pmco_company', 'pmco_company_old';

------------------------------------------------------------------------------------

EXEC sp_rename 'pmco_company2', 'pmco_company';

------------------------------------------------------------------------------------
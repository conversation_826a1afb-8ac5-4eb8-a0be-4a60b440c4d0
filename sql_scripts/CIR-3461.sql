-- all client db
CREATE SEQUENCE seq_ap_transaction
    START WITH 1000001
    INCREMENT BY 1 ;

CREATE SEQUENCE seq_ar_transaction
    START WITH 1000001
    INCREMENT BY 1 ;

CREATE SEQUENCE seq_ap_allocation
    START WITH 1000001
    INCREMENT BY 1 ;

CREATE SEQUENCE seq_ar_allocation
    START WITH 1000001
    INCREMENT BY 1 ;


-- update next number **USE THE DANGEROUS OPTION IN QUERY MANAGER
-- run one by one
--select CONCAT('ALTER SEQUENCE seq_ap_transaction RESTART WITH ' , MAX(CAST(batch_nr as INT))+1,  ';') FROM ap_transaction;
--select CONCAT('ALTER SEQUENCE seq_ar_transaction RESTART WITH ' , MAX(CAST(batch_nr as INT))+1,  ';') FROM ar_transaction;
--select CONCAT('ALTER SEQUENCE seq_ap_allocation RESTART WITH ' , MAX(CAST(pmxc_alloc_nr as INT))+1,  ';') FROM pmxc_ap_alloc;
--select CONCAT('ALTER SEQUENCE seq_ar_allocation RESTART WITH ' , MAX(CAST(pmxd_alloc_nr as INT))+1,  ';') FROM pmxd_ar_alloc;

SELECT CONCAT('ALTER SEQUENCE seq_ap_transaction RESTART WITH ', batch_nr + 1000, ';')
FROM transaction_batch_no WHERE trans_type = 'AP';

SELECT CONCAT('ALTER SEQUENCE seq_ar_transaction RESTART WITH ', batch_nr + 1000, ';')
FROM transaction_batch_no WHERE trans_type = 'AR';

SELECT CONCAT('ALTER SEQUENCE seq_ap_allocation RESTART WITH ', batch_nr + 1000, ';')
FROM transaction_batch_no WHERE trans_type = 'AP_ALLOC';

SELECT CONCAT('ALTER SEQUENCE seq_ar_allocation RESTART WITH ', batch_nr + 1000, ';')
FROM transaction_batch_no WHERE trans_type = 'AR_ALLOC';
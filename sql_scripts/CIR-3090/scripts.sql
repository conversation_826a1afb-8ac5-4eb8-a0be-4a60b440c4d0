
--first script to run
UPDATE pmlc_l_charge
SET pmlc_gst_code = 'GSTFREE'
WHERE force_gst_free = 1

--second script to run
UPDATE pmlc_l_charge
SET pmlc_gst_code = 'TAXABLE'
WHERE force_gst_free <> 1

--third script to run
UPDATE pmlc_l_charge
SET pmlc_gst_code = 'GSTFREE'
WHERE pmlc_prop IN (
    SELECT pmpr_prop FROM pmpr_property WHERE pmlc_prop = pmpr_prop AND pmpr_prop_type = 'RESI'
)
   OR
        pmlc_lease IN (
        SELECT pmle_lease FROM pmle_lease WHERE pmlc_lease = pmle_lease AND pmle_ten_type = 'RESI'
    )


-- check data for all RESIDENTIAL
SELECT pmlc_gst_code, force_gst_free FROM pmlc_l_charge
WHERE pmlc_prop IN (
    SELECT pmpr_prop FROM pmpr_property WHERE pmlc_prop = pmpr_prop AND pmpr_prop_type = 'RESI'
)
   OR
        pmlc_lease IN (
        SELECT pmle_lease FROM pmle_lease WHERE pmlc_lease = pmle_lease AND pmle_ten_type = 'RESI'
    )
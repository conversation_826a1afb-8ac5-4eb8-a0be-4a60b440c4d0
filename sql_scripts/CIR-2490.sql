-- PER CLIENT
CREATE TABLE [dbo].[ledger_activity_logs](
	[id] [int] IDENTITY(1,1) NOT NULL PRIMARY KEY,
	[prop_id] [char](10) NULL,
	[section] [varchar](50) NULL,
	[table_name] [varchar](50) NULL,
	[column_name] [varchar](50) NULL,
	[additional_info] [varchar](1000) NULL,
	[old_value] [varchar](1000) NULL,
	[new_value] [varchar](1000) NULL,
	[modified_by] [int] NOT NULL,
	[modified_at] [datetime] NOT NULL,
	[status] [varchar](20) NULL,
 );
 
ALTER TABLE [dbo].[ledger_activity_logs] ADD  DEFAULT (getdate()) FOR [modified_at];

CREATE TABLE [dbo].[subledger_activity_logs](
	[id] [int] IDENTITY(1,1) NOT NULL PRIMARY KEY,
	[prop_id] [char](10) NULL,
	[lease_id] [char](10) NULL,
	[section] [varchar](50) NULL,
	[table_name] [varchar](50) NULL,
	[column_name] [varchar](50) NULL,
	[additional_info] [varchar](1000) NULL,
	[old_value] [varchar](1000) NULL,
	[new_value] [varchar](1000) NULL,
	[modified_by] [int] NOT NULL,
	[modified_at] [datetime] NOT NULL,
	[status] [varchar](20) NULL,
 );
 
ALTER TABLE [dbo].[subledger_activity_logs] ADD  DEFAULT (getdate()) FOR [modified_at];


-- PER CLIENT
ALTER TABLE ledger_activity_logs
ALTER COLUMN old_value TEXT NULL;

ALTER TABLE ledger_activity_logs
ALTER COLUMN new_value TEXT NULL;

ALTER TABLE subledger_activity_logs
ALTER COLUMN old_value TEXT NULL;

ALTER TABLE subledger_activity_logs
ALTER COLUMN new_value TEXT NULL;


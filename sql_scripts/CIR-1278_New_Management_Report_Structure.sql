/**
*
* All Client Tables
* Main prefix for all tables related to Management Reports: Property Report - mgmt_
* _config suffix used to all configuration tables under mgmt report
* _data suffix used to all data tables under mgmt report
**/

CREATE TABLE mgmt_report
(
    id INT IDENTITY(1,1),
    mgmt_report_name VARCHAR(100),
    description VARCHAR(255),
    report_orientation TINYINT NULL DEFAULT 1
);

--default_status values:
--1: Mandatory
--2: Included by default but can be changed
--3: Excluded by default but can be changed
CREATE TABLE mgmt_sub_report
(
    mgmt_report_id INT NOT NULL, -- PK1: this is linked to mgmt_report.id
    sub_report_id INT NOT NULL, --PK2
    mgmt_sub_report_name VARCHAR(100),
    description VARCHAR(255),
    sub_report_file_path VARCHAR(100),
    default_status TINYINT NULL DEFAULT 1 --defaults to Mandatory
);

--Set mgmt_report_id and sub_report_id as both PRIMARY KEYS
ALTER TABLE mgmt_sub_report ADD CONSTRAINT PK_Mgmt_Sub_Report PRIMARY KEY (mgmt_report_id, sub_report_id);

-----------------------------------------------------------------------------------
--- Sub Report Selection Data ---
--CREATE TABLE mgmt_sub_report_selection_config -- updated from _config to _data
CREATE TABLE mgmt_sub_report_selection_data
(
    id INT IDENTITY(1,1),
    property_code VARCHAR(100),
    cal_period INT,
    cal_year INT,
    mgmt_report_id INT, -- this is linked to mgmt_report.id
    sub_report_id INT, -- this is linked to mgmt_sub_report.sub_report_id
    is_selected TINYINT NULL DEFAULT 1, -- set to 1 to make it included by default
    is_complete TINYINT NULL DEFAULT 0 -- set to 0 to tag it as incomplete by default
);
-----------------------------------------------------------------------------------
--- Table Configuration ---
CREATE TABLE mgmt_sub_report_table_config
(
    table_id INT IDENTITY(1,1),
    table_description VARCHAR(255),
    function_call VARCHAR(255),
    mgmt_report_id INT, -- this is linked to mgmt_report.id
    sub_report_id INT, -- this is linked to mgmt_sub_report.sub_report_id
    has_action_btns TINYINT NULL DEFAULT 0, -- this will display action buttons per table if set to 1
    has_comment TINYINT NULL DEFAULT 0, -- this will enable comment modal on the datatable if set to 1
    priority INT NULL
);

--FOR alter SQL only if above table already exists without this column - START
ALTER TABLE mgmt_sub_report_table_config ADD has_action_btns TINYINT NOT NULL DEFAULT 0;
ALTER TABLE mgmt_sub_report_table_config ADD has_comment TINYINT NOT NULL DEFAULT 0;
ALTER TABLE mgmt_sub_report_table_config ADD priority INT NULL;
--FOR alter SQL only - END

--- Table Column Configuration ---
CREATE TABLE mgmt_sub_report_table_column_config
(
    id INT IDENTITY(1,1),
    column_id INT,
    column_label VARCHAR(255),
    table_id INT, -- this is linked to mgmt_table_config.table_id
    sub_report_id INT, -- this is linked to mgmt_sub_report.sub_report_id
    alignment VARCHAR(255),
    is_sortable TINYINT NULL DEFAULT 0, --added 14-09-2021
    is_readonly TINYINT NULL DEFAULT 0, --FOR CONFIRMATION:  with Philip the default values for this
    max_characters INT NULL DEFAULT 0,
    input_type VARCHAR(255)
);

--FOR alter SQL only if above table already exists without this column - START
ALTER TABLE mgmt_sub_report_table_column_config ADD is_sortable TINYINT NOT NULL DEFAULT 0; --added 14-09-2021
ALTER TABLE mgmt_sub_report_table_column_config ADD input_type VARCHAR(255) NULL;
--FOR alter SQL only - END

--- Table Data ---
--default_comment_format
----- 1: set to plain text format
----- 2: set to bullet format
----- 3: set to listed format
CREATE TABLE mgmt_sub_report_table_data
(
    id INT IDENTITY(1,1),
    property_code VARCHAR(100),
    sub_report_id INT, -- this is linked to mgmt_sub_report.sub_report_id
    cal_period INT,
    cal_year INT,
    table_id INT,
    row_no INT,
    column_1 VARCHAR(150),
    column_2 VARCHAR(150),
    column_3 VARCHAR(150),
    column_4 VARCHAR(150),
    column_5 VARCHAR(150),
    column_6 VARCHAR(150),
    column_7 VARCHAR(150),
    column_8 VARCHAR(150),
    column_9 VARCHAR(150),
    column_10 VARCHAR(150),
    column_11 VARCHAR(150),
    column_12 VARCHAR(150),
    column_13 VARCHAR(150), -- for the action buttons IF ENABLED
    column_0 NVARCHAR(MAX), -- specifically for comments or longtexts
    sort_order INT NULL DEFAULT 1, -- used for saving of sorting order 1 being first row and so on
    default_comment_format TINYINT NULL DEFAULT 1, --sets column_0 aka comments col to plain text format
    modified_by VARCHAR(100) NULL DEFAULT NULL,
    modified_at DATETIME NULL DEFAULT NULL
);
--FOR alter SQL only if above table already exists without this column - START
ALTER TABLE mgmt_sub_report_table_data ADD column_13 INT NULL DEFAULT 1; -- for the action buttons IF ENABLED
ALTER TABLE mgmt_sub_report_table_data ADD sort_order INT NULL DEFAULT 1;
--FOR alter SQL only - END
-----------------------------------------------------------------------------------
--- Sub Report Uploads Configuration ---
CREATE TABLE mgmt_sub_report_upload_config
(
    id INT IDENTITY(1,1),
    upload_description VARCHAR(255),
    sub_report_id INT, -- this is linked to mgmt_sub_report.sub_report_id
    max_upload_size INT NULL DEFAULT 0, -- Sets limit size for upload. Value will be in kilobytes
    priority INT NULL
);

--FOR alter SQL only if above table already exists without this column - START
ALTER TABLE mgmt_sub_report_upload_config ADD priority INT NULL;
ALTER TABLE mgmt_sub_report_upload_config ADD upload_description VARCHAR(255) NULL;
--FOR alter SQL only - END

--- Sub Report Upload Data ---
CREATE TABLE mgmt_sub_report_upload_data
(
    id INT IDENTITY(1,1),
    property_code VARCHAR(100),
    sub_report_id INT, -- this is linked to mgmt_sub_report.sub_report_id
    upload_config_id INT, -- this is linked to mgmt_sub_report_upload_config.id
    cal_period INT,
    cal_year INT,
    file_type NVARCHAR(MAX),
    file_name NVARCHAR(MAX),
    file_path NVARCHAR(MAX),
    created_by VARCHAR(100) NULL DEFAULT NULL,
    created_at DATETIME NULL DEFAULT NULL,
    modified_by VARCHAR(100) NULL DEFAULT NULL,
    modified_at DATETIME NULL DEFAULT NULL,
    is_temp TINYINT NULL DEFAULT 0
);

--FOR alter SQL only if above table already exists without this column - START
ALTER TABLE mgmt_sub_report_upload_data ADD upload_config_id INT NULL;
--FOR alter SQL only - END

-----------------------------------------------------------------------------------
--- Comments Configuration ---
--default_note_format
----- 1: set to plain text format
----- 2: set to bullet format
----- 3: set to listed format
--default_font_format
----- 1: set to regular text format
----- 2: set to bold format
CREATE TABLE mgmt_sub_report_comment_config
(
    config_id INT IDENTITY(1,1),
    sub_report_id INT, -- this is linked to mgmt_sub_report.sub_report_id
    comment_description VARCHAR(255),
    line_limit INT NULL DEFAULT 0,
    max_characters INT NULL DEFAULT 0,
    default_font_format TINYINT NULL DEFAULT 1, -- 1 is regular text
    default_note_format TINYINT NULL DEFAULT 1, --set to plain text format
    default_font_size INT NULL DEFAULT 8, --set to size 8 as default
    priority INT NULL
);

--FOR alter SQL only if above table already exists without this column - START
ALTER TABLE mgmt_sub_report_comment_config ADD priority INT NULL;
--FOR alter SQL only - END

--- Comments Data (this is equivalent to existing report_note table) ---
-- converted separated format columns (format_bulleted-format_listed-format_plaintxt) into single format column note_format:
----- 1: set to plain text format
----- 2: set to bullet format
----- 3: set to listed format
CREATE TABLE mgmt_sub_report_comment_data
(
    id INT IDENTITY(1,1),
    property_code VARCHAR(100),
    sub_report_id INT, /** replaced report_id INTO sub_report_id in report_note */
    comment_config_id INT, --UPD: linked to mgmt_sub_report_comment_config.config_id --originally report_note.comment_box_id linked to npms.report_sub_config.rc_comment_line
    cd_period INT, --rn_period
    cd_year INT, --rn_year
    note_text NVARCHAR(MAX), --SHOULD BE NVARCHAR(MAX)
    created_by VARCHAR(100) NULL DEFAULT NULL,
    created_at DATETIME NULL DEFAULT NULL,
    modified_by VARCHAR(100) NULL DEFAULT NULL,
    modified_at DATETIME NULL DEFAULT NULL,
    note_format TINYINT NULL DEFAULT 1 -- sets to plain text as default
);
--FOR alter SQL only if above table has the existing column already - START
ALTER TABLE mgmt_sub_report_comment_data ALTER COLUMN note_text NVARCHAR(MAX);
--FOR alter SQL only - END

--per client
ALTER TABLE sub_report ADD is_management_report tinyint NULL DEFAULT (0);
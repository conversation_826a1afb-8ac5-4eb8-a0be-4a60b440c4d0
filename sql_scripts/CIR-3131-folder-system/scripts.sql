--CLIENTS
-- drop table folder_system
-- drop table document_directory
CREATE TABLE document_directory (id INT IDENTITY PRIMARY KEY, name <PERSON><PERSON><PERSON><PERSON>(60) NOT NULL, parent_id INT, folder_module VARCHAR(60) NULL, property_code VA<PERSON>HAR(20) NULL, lease_code VA<PERSON>HAR(20) NULL, company_code VA<PERSON>HAR(20) NULL, archived bit DEFAULT 0 NOT NULL, is_system bit DEFAULT 0 NOT NULL, sort_order INT DEFAULT 99 NOT NULL, created_at DATETIME DEFAULT GETDATE(), modified_at DATETIME);


-- INSERT INTO document_directory(name, parent_id, folder_module, property_code, lease_code, company_code, sort_order, is_system)
-- VALUES('OWNER REPORTS', null, 'PROPERTY', null, null, null, 1, 1);
-- INSERT INTO document_directory(name, parent_id, folder_module, property_code, lease_code, company_code, sort_order)
-- VALUES('PROPERTY FOLDER', null, 'PROPERTY', null, null, null, 2);
-- INSERT INTO document_directory(name, parent_id, folder_module, property_code, lease_code, company_code, sort_order)
-- VALUES('LEASE FOLDER', null, 'LEASE', null, null, null, 3);
-- INSERT INTO document_directory(name, parent_id, folder_module, property_code, lease_code, company_code, sort_order)
-- VALUES('COMPANY FOLDER', null, 'COMPANY', null, null, null, 4);
--
--
-- INSERT INTO document_directory(name, parent_id, folder_module, property_code, lease_code, company_code, sort_order)
-- VALUES('ABER91U2 - Folder', null, null, 'ABER91U2', null, null, 5);
-- INSERT INTO document_directory(name, parent_id, folder_module, property_code, lease_code, company_code, sort_order)
-- VALUES('WALT69 - Folder', null, null, 'WALT69', null, null, 6);
--
-- INSERT INTO document_directory(name, parent_id, folder_module, property_code, lease_code, company_code, sort_order)
-- VALUES('ABER91U2 - KBAGLO - Folder 1', null, null, 'ABER91U2', 'KBAGLO', null, 7);
-- INSERT INTO document_directory(name, parent_id, folder_module, property_code, lease_code, company_code, sort_order)
-- VALUES('ABER91U2 - KBAGLO - Folder 2', null, null, 'ABER91U2', 'KBAGLO', null, 8);
--
-- INSERT INTO document_directory(name, parent_id, folder_module, property_code, lease_code, company_code, sort_order)
-- VALUES('101MAI - Folder 1', null, null, null, null, '101MAI', 9);
--
-- INSERT INTO document_directory(name, parent_id, folder_module, property_code, lease_code, company_code, sort_order)
-- VALUES('Ledger 1', null, 'LEDGER', null, null, null, 10);
--
-- INSERT INTO document_directory(name, parent_id, folder_module, property_code, lease_code, company_code, sort_order)
-- VALUES('Ledger 2', null, 'LEDGER', null, null, null, 11);
--
-- INSERT INTO document_directory(name, parent_id, folder_module, property_code, lease_code, company_code, sort_order)
-- VALUES('Ledger 3', null, 'LEDGER', null, null, null, 12);
--
-- INSERT INTO document_directory(name, parent_id, folder_module, property_code, lease_code, company_code, sort_order)
-- VALUES('Sub-Ledger 1', null, 'SUB-LEDGER', null, null, null, 10);
--
-- INSERT INTO document_directory(name, parent_id, folder_module, property_code, lease_code, company_code, sort_order)
-- VALUES('Sub-Ledger 2', null, 'SUB-LEDGER', null, null, null, 11);
--
-- INSERT INTO document_directory(name, parent_id, folder_module, property_code, lease_code, company_code, sort_order)
-- VALUES('Sub-Ledger 3', null, 'SUB-LEDGER', null, null, null, 12);
--
--
-- --
-- WITH document_directory_CTE AS (
--     SELECT id, name, parent_id,
--            CAST(name AS NVARCHAR(MAX)) AS path
--     FROM document_directory
--     WHERE parent_id IS NULL -- Select root folders
--
--     UNION ALL
--
--     SELECT fs.id, fs.name, fs.parent_id,
--            CAST(cte.path + '/' + fs.name AS NVARCHAR(MAX)) AS path
--     FROM document_directory_CTE cte
--              JOIN document_directory fs ON cte.id = fs.parent_id
-- )
-- SELECT id, name, parent_id, path
-- FROM document_directory_CTE;

EXEC sp_rename 'documents.folder_id', 'directory_id', 'COLUMN';
EXEC sp_rename 'temp_documents.folder_id', 'directory_id', 'COLUMN';

alter table documents add directory_id INT NULL
alter table temp_documents add directory_id INT NULL

ALTER TABLE [documents] ADD Bit NULL DEFAULT (0) WITH VALUES
ALTER TABLE [temp_documents] ADD archived_file Bit NULL DEFAULT (0) WITH VALUES
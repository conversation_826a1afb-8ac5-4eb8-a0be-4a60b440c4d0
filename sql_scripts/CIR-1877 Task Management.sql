
DELETE FROM pmzz_param WHERE pmzz_par_type = 'TASKCTGRY'
INSERT INTO pmzz_param (pmzz_par_type, pmzz_code, pmzz_desc)
values
	('TASKCTGRY', 'GE', 'General'),
	('TASKCTGRY', 'CO', 'Company'),
	('TASKCTGRY', 'DI', 'Diary Item'),
	('TASKCTGRY', 'LE', 'Lease'),
	('TASKCTGRY', 'PR', 'Property')

DELETE FROM pmzz_param WHERE pmzz_par_type = 'TASKSTATUS'
INSERT INTO pmzz_param (pmzz_par_type, pmzz_code, pmzz_desc)
values
	('TASKSTATUS', 'NOTSTARTED', 'Not Started'),
	('TASKSTATUS', 'PROGRESS', 'Work in Progress'),
	('TASKSTATUS', 'HOLD', 'On Hold'),
	('TASKSTATUS', 'CLOSED', 'Closed')
DROP TABLE task_management
GO
CREATE TABLE task_management(
	[task_id] [int] IDENTITY(1,1) NOT NULL PRIMARY KEY,
	[task_no] [int] NOT NULL,
	[task_code] [varchar](10) NOT NULL,
	[task_subject] [varchar](50) NOT NULL,
	[task_type] [varchar](10) NOT NULL,
	[task_step] [varchar](10) NOT NULL,
	[task_description] [text] NULL,
	[task_assignee] [int] NOT NULL,
	[task_status] [varchar](10) NOT NULL,
	[task_company] [varchar](10) NULL,
	[task_property] [varchar](10) NULL,
	[task_lease] [varchar](10) NULL,
	[task_diary] [varchar](200) NULL,
	[created_by] [varchar](120) NOT NULL,
	[created_date] [datetime] NOT NULL,
	[modified_by] [varchar](120) NULL,
	[modified_date] [datetime] NULL
)
GO

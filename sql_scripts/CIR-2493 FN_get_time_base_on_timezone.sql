-- FOR ALL CLIENT
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO



-- =============================================
-- Author:		<Author,,Name>
-- Create date: <Create Date,,>
-- Description:	<Description,,>
-- =============================================
CREATE FUNCTION [dbo].[FN_get_time_base_on_timezone]
(	
	-- Add the parameters for the function here
		@timezone AS varchar(max),
		@toChangeDateTime AS DATETIME
)
RETURNS DATETIME 
AS
BEGIN

DECLARE @defaultTimezoneName VARCHAR(max) = 'W. Australia Standard Time'
DECLARE @defaultTimezone BIGINT = Datediff( MINUTE, @toChangeDateTime AT TIME ZONE @defaultTimezoneName , getdate() )

IF @timezone = ''
BEGIN
	SET @timezone = 'W. Australia Standard Time'
END
DECLARE @clientTimezoneName VARCHAR(max) = @timezone
DECLARE @clientTimezoneOffset BIGINT = Datediff( MINUTE, @toChangeDateTime AT TIME ZONE @clientTimezoneName , getdate() )

DECLARE @timeDiff BIGINT  = ( @clientTimezoneOffset - @defaultTimezone )
RETURN  DATEADD(MINUTE, @timeDiff, @toChangeDateTime)

END
GO





ALTER TABLE [dbo].[letter_template]
ADD [range_start] int NULL

ALTER TABLE [dbo].[letter_template]
ADD [range_end] int NULL

ALTER TABLE [dbo].[letter_template]
ADD [is_custom] bit DEFAULT  0 NULL

UPDATE
  letter_template
SET
  letter_template.is_custom = arrears_letter_templates.is_custom,
  letter_template.range_start = arrears_letter_templates.range_start,
  letter_template.range_end = arrears_letter_templates.range_end
FROM
  letter_template
INNER JOIN
  arrears_letter_templates
ON
  arrears_letter_templates.id = letter_template.letter_template_id
WHERE
  letter_template.letter_category_id = 1
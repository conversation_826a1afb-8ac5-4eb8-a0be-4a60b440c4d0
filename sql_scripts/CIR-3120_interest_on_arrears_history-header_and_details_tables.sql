/**
*
* Run on ALL CLIENT DBs
* Header table: interest_on_arrears_history
* Details table: interest_on_arrears_history_details
* NOTE: interest_on_arrears_history_details.header_id is linked to interest_on_arrears_history.id
*
**/

DROP TABLE interest_on_arrears_history;
CREATE TABLE interest_on_arrears_history
(
    id INT IDENTITY(1,1),
    processed_date DATETIME NULL,
    created_by VARCHAR(255) NULL DEFAULT NULL,
    from_date DATE,
    to_date DATE,
    filepath VARCHAR(255)
);

DROP TABLE interest_on_arrears_history_details;
CREATE TABLE interest_on_arrears_history_details
(
    id INT IDENTITY(1,1),
    header_id INT,
    property_code VARCHAR(50),
    amount NUMERIC(19,2)
);
--PER CLIENT

CREATE TABLE [dbo].[budget_per_portfolio](
	[bpp_id] [int] IDENTITY(1,1) NOT NULL,
	[bpp_propertyID] [varchar](50) NULL,
	[bpp_account] [varchar](50) NULL,
	[bpp_year] [varchar](4) NULL,
	[bpp_amount] [numeric](10, 2) NULL
) ON [PRIMARY]
GO

CREATE TABLE [dbo].[cost_per_portfolio](
	[cpp_id] [int] IDENTITY(1,1) NOT NULL,
	[cpp_portfolio] [varchar](50) NULL,
	[cpp_type] [varchar](100) NULL,
	[cpp_amount] [numeric](10, 2) NULL,
	[cpp_year] [varchar](4) NULL
) ON [PRIMARY]
GO

ALTER TABLE pmpr_property ADD pmpr_inactive_date date NULL DEFAULT NULL;

UPDATE pmpr_property SET pmpr_inactive_date = pmpr_update_dt
where pmpr_delete = 1

USE npms;
alter table database_list add short_name varchar(50)  null DEFAULT NULL;

CREATE TABLE [dbo].[linked_database](
	[link_id] [int] IDENTITY(1,1) NOT NULL,
	[link_db_id] [varchar](50) NULL,
	[link_database] [varchar](50) NULL
) ON [PRIMARY]
GO
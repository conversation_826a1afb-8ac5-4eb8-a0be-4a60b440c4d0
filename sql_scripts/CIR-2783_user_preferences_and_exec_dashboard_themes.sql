SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[user_preferences](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[user_id] [int] NOT NULL,
	[database_id] [int] NOT NULL,
	[preference_code] [varchar](50) NULL,
	[preference_value] [varchar](250) NULL,
	[created_at] [datetime] NOT NULL,
	[updated_at] [datetime] NOT NULL,
 CONSTRAINT [PK_user_preferences] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[exec_dashboard_themes](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[theme_name] [varchar](100) NULL,
 CONSTRAINT [PK_exec_dashboard_themes] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
SET IDENTITY_INSERT [dbo].[exec_dashboard_themes] ON 

INSERT [dbo].[exec_dashboard_themes] ([id], [theme_name]) VALUES (1, N'Dataviz')
INSERT [dbo].[exec_dashboard_themes] ([id], [theme_name]) VALUES (2, N'Frozen')
INSERT [dbo].[exec_dashboard_themes] ([id], [theme_name]) VALUES (3, N'Kelly')
INSERT [dbo].[exec_dashboard_themes] ([id], [theme_name]) VALUES (4, N'Material')
INSERT [dbo].[exec_dashboard_themes] ([id], [theme_name]) VALUES (5, N'Moonrise')
INSERT [dbo].[exec_dashboard_themes] ([id], [theme_name]) VALUES (6, N'Spirited')
SET IDENTITY_INSERT [dbo].[exec_dashboard_themes] OFF
GO

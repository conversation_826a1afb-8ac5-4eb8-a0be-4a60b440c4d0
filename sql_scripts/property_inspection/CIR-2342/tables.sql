
-- ----------------------------
-- Table structure for property_inspections
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[property_inspections]') AND type IN ('U'))
DROP TABLE [dbo].[property_inspections]
    GO

CREATE TABLE [dbo].[property_inspections] (
    [ID] int  IDENTITY(1,1),
    [created_at] datetime DEFAULT getutcdate() NOT NULL,
    [created_by] int  NULL,
    [property_code] varchar(11) COLLATE SQL_Latin1_General_CP1_CI_AS  NULL,
    [frequency] char(1) COLLATE SQL_Latin1_General_CP1_CI_AS  NULL,
    [scheduled_date] datetime  NULL,
    [inspection_type] varchar(100) COLLATE SQL_Latin1_General_CP1_CI_AS  NULL,
    [pim_template] varchar(100) COLLATE SQL_Latin1_General_CP1_CI_AS  NULL,
    [comment] varchar(1000) COLLATE SQL_Latin1_General_CP1_CI_AS  NULL,
    [pass] varchar(10) COLLATE SQL_Latin1_General_CP1_CI_AS  NULL,
    [completion] bit  NULL,
    [charge] decimal(19,2)  NULL,
    [completed_by] int  NULL,
    [completed_date] datetime  NULL,
    [attached_document_id] int  NULL
    )
    GO

    -- ----------------------------
-- Table structure for property_inspections_temp
-- ----------------------------
    IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[property_inspections_temp]') AND type IN ('U'))
DROP TABLE [dbo].[property_inspections_temp]
    GO

CREATE TABLE [dbo].[property_inspections_temp] (
    [ID] int  IDENTITY(1,1),
    [created_at] datetime DEFAULT getutcdate() NOT NULL,
    [created_by] int  NULL,
    [property_code] varchar(11) COLLATE SQL_Latin1_General_CP1_CI_AS  NULL,
    [frequency] char(1) COLLATE SQL_Latin1_General_CP1_CI_AS  NULL,
    [scheduled_date] datetime  NULL,
    [inspection_type] varchar(100) COLLATE SQL_Latin1_General_CP1_CI_AS  NULL,
    [pim_template] varchar(100) COLLATE SQL_Latin1_General_CP1_CI_AS  NULL,
    [comment] varchar(1000) COLLATE SQL_Latin1_General_CP1_CI_AS  NULL,
    [pass] varchar(10) COLLATE SQL_Latin1_General_CP1_CI_AS  NULL,
    [completion] bit  NULL,
    [charge] decimal(19,2)  NULL,
    [completed_by] int  NULL,
    [completed_date] datetime  NULL,
    [attached_document_id] int  NULL
    )
    GO



ALTER TABLE [dbo].[pmdy_diary]
    ADD [property_inspection_id] INT;
ALTER TABLE [dbo].[temp_pmdy_diary]
    ADD [property_inspection_id] INT;
-- PER CLIENT

ALTER TABLE cms_system ADD cms_sy_email_client VARCHAR(20) NULL;
UPDATE cms_system SET cms_sy_email_client = 'smtp';
ALTER TABLE cms_system DROP COLUMN cms_sy_email_client;

-- NPMS
CREATE TABLE email_settings (
	id INT IDENTITY(1,1) NOT NULL PRIMARY KEY,
    database_id INT NOT NULL,
    category VARCHAR(50) NOT NULL,
    field VARCHAR(50) NOT NULL,
    value VARCHAR(1000) NULL,
    created_by INT NULL,
    created_at DATETIME NULL,
    updated_by INT NULL,
    updated_at DATETIME NULL
);

-- NPMS
ALTER TABLE email_log
ADD postmark_message_id VARCHAR(500) NULL;

-- NPMS
CREATE TABLE postmark_logs (
	id INT IDENTITY(1,1) NOT NULL PRIMARY KEY,
    postmark_id VARCHAR(500) NULL,
    server_token VARCHAR(500) NULL,
    [to] VARCHAR(500) NULL,
    [from] VARCHAR(500) NULL,
    subject VARCHAR(1000) NULL,
    html_body TEXT NULL,
    text_body TEXT NULL,
    tag VARCHAR(500) NULL,
    track VARCHAR(500) NULL,
    reply_to VARCHAR(500) NULL,
    cc VARCHAR(500) NULL,
    bcc VARCHAR(500) NULL,
    headers TEXT NULL,
    attachments TEXT NULL,
    code VARCHAR(500) NULL,
    message VARCHAR(500) NULL,
    sent VARCHAR(500) NULL
);

ALTER TABLE postmark_logs DROP COLUMN html_body;
ALTER TABLE postmark_logs DROP COLUMN text_body;
ALTER TABLE postmark_logs DROP COLUMN tag;
ALTER TABLE postmark_logs DROP COLUMN track;
ALTER TABLE postmark_logs DROP COLUMN headers;
ALTER TABLE postmark_logs DROP COLUMN attachments;

ALTER TABLE email_log_attachment ADD file_name VARCHAR(1000) NULL;
ALTER TABLE email_log_attachment ADD content_type VARCHAR(500) NULL;

ALTER TABLE postmark_logs ADD details TEXT NULL;
ALTER TABLE postmark_logs ADD updated_at DATETIME NULL;


-- NPMS
CREATE TABLE site_options (
	id INT IDENTITY(1,1) NOT NULL PRIMARY KEY,
    category VARCHAR(50) NOT NULL,
    field VARCHAR(50) NOT NULL,
    value VARCHAR(1000) NULL,
    created_by INT NULL,
    created_at DATETIME NULL,
    updated_by INT NULL,
    updated_at DATETIME NULL
);

INSERT INTO site_options (category, field, value)
VALUES ('email', 'use_postmark', 0);
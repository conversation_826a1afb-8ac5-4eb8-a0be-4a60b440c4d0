/// CLIENTS DB
CREATE TABLE utility_meter (
	meter_id int IDENTITY(1,1) NOT NULL,
	property_code varchar(50) NOT NULL,
	meter_number  varchar(50) NOT NULL,
	meter_type	  varchar(50) NOT NULL,
	meter_description varchar(80),
	meter_charges numeric(12,4) NOT NULL DEFAULT 0.0000,
	account_code  varchar(50) NOT NULL,
	is_inactive int NOT NULL DEFAULT 0,
	created_by varchar(120) NOT NULL,
	created_date datetime NOT NULL,
	modified_by varchar(120) NULL,
	modified_date datetime NULL
	)

CREATE TABLE utility_charges (
	charges_id int IDENTITY(1,1) NOT NULL,
	property_code varchar(50) NOT NULL,
	meter_id INT NOT NULL,
	charges_type varchar(50) NOT NULL,
	charges_description varchar(80),
	charges_amount numeric(12,2) NOT NULL DEFAULT 0.00,
	charges_account_code  varchar(50) NOT NULL,
	is_inactive int NOT NULL DEFAULT 0,
	created_by varchar(120) NOT NULL,
	created_date datetime NOT NULL,
	modified_by varchar(120) NULL,
	modified_date datetime NULL
	)


CREATE TABLE utility_allocation (
	allocation_id int IDENTITY(1,1) NOT NULL,
	property_code varchar(50) NOT NULL,
	meter_id INT NOT NULL,
	unit_code varchar(50) NOT NULL,
	alloc_percentage numeric(12,2) NOT NULL DEFAULT 0.00,
	is_inactive int NOT NULL DEFAULT 0,
	created_by varchar(120) NOT NULL,
	created_date datetime NOT NULL,
	modified_by varchar(120) NULL,
	modified_date datetime NULL
	)


CREATE TABLE utility_reading (
	reading_id int IDENTITY(1,1) NOT NULL,
	property_code varchar(50) NOT NULL,
	meter_id INT NOT NULL,
	reading_date date NOT NULL,
	reading_present numeric(12,0) NOT NULL DEFAULT 0,
	reading_previous numeric(12,0) NOT NULL DEFAULT 0,
	reading_consumption numeric(12,0) NOT NULL DEFAULT 0,
	reading_unit_charges numeric(12,4) NOT NULL DEFAULT 0.0000,
	reading_total_unit_charges numeric(12,4) NOT NULL DEFAULT 0.0000,
	reading_additional_charges numeric(12,2) NOT NULL DEFAULT 0.00,
	reading_invoice INT NOT NULL DEFAULT 0,
	reading_days numeric(12,0) NOT NULL DEFAULT 0,
	is_invoice_generated INT NOT NULL DEFAULT 0,
	is_inactive int NOT NULL DEFAULT 0,
	created_by varchar(120) NOT NULL,
	created_date datetime NOT NULL,
	modified_by varchar(120) NULL,
	modified_date datetime NULL
	)


CREATE TABLE utility_reading_charges (
	reading_charges_id int IDENTITY(1,1) NOT NULL,
	reading_id int,
	lease_code varchar(10),
	reading_charges_type varchar(50) NOT NULL,
	reading_charges_description varchar(80) NOT NULL,
	reading_charges_account varchar(50) NOT NULL,
	reading_charges_amount numeric(12,2) NOT NULL DEFAULT 0.00,
	reading_charges_days numeric(12,0) NOT NULL DEFAULT 0,
	reading_charges_total numeric(12,2) NOT NULL DEFAULT 0.00,
	is_inactive int NOT NULL DEFAULT 0,
	created_by varchar(120) NOT NULL,
	created_date datetime NOT NULL,
	modified_by varchar(120) NULL,
	modified_date datetime NULL
	)


ALTER TABLE utility_meter ADD CONSTRAINT utility_meter_pk PRIMARY KEY (meter_id)
ALTER TABLE utility_charges ADD CONSTRAINT utility_charges_pk PRIMARY KEY (charges_id)
ALTER TABLE utility_allocation ADD CONSTRAINT utility_allocation_pk PRIMARY KEY (allocation_id)
ALTER TABLE utility_reading ADD CONSTRAINT utility_reading_pk PRIMARY KEY (reading_id)
ALTER TABLE utility_reading_charges ADD CONSTRAINT utility_reading_charges_pk PRIMARY KEY (reading_charges_id)

ALTER TABLE utility_charges ADD CONSTRAINT FK_utility_charges_meter_id FOREIGN KEY (meter_id) REFERENCES utility_meter(meter_id);
ALTER TABLE utility_allocation ADD CONSTRAINT FK_utility_allocation_meter_id FOREIGN KEY (meter_id) REFERENCES utility_meter(meter_id);
ALTER TABLE utility_reading ADD CONSTRAINT FK_utility_reading_meter_id FOREIGN KEY (meter_id) REFERENCES utility_meter(meter_id);
ALTER TABLE utility_reading_charges ADD CONSTRAINT FK_utility_reading_charges_reading_id FOREIGN KEY (reading_id) REFERENCES utility_reading(reading_id);


INSERT INTO pmzz_param VALUES('UTIL_TYPE','WATER','Water',NULL, NULL)
INSERT INTO pmzz_param VALUES('UTIL_TYPE','GAS','Gas',NULL, NULL)
INSERT INTO pmzz_param VALUES('UTIL_TYPE','ELECTRICT','Electricity',NULL, NULL)

INSERT INTO pmzz_param VALUES('UTIL_CHARG','DAILY','Daily',NULL, NULL)
INSERT INTO pmzz_param VALUES('UTIL_CHARG','SERV_CHRG','Service Charge',NULL, NULL)

INSERT INTO pmzt_par_type VALUES ('UTIL_TYPE','Utility Type')
INSERT INTO pmzt_par_type VALUES ('UTIL_CHARG','Utility Charges')


-- for clients

DROP VIEW [dbo].[agency_fees_budget]
GO


CREATE VIEW [dbo].[agency_fees_budget]
AS
SELECT
	pmep_prop as propertyID,
	pmcm_mast_cal.pmcm_year as calYear,
	pmcm_mast_cal.pmcm_period as calPeriod,
	pmep_b_c_amt as budgetAmt,
	pmep_exp_acc as budgetAccount
FROM pmep_b_exp_per pmep_b_exp_per
LEFT JOIN pmpr_property pmpr_property
	ON pmep_b_exp_per.pmep_prop = pmpr_property.pmpr_prop
LEFT JOIN pmca_chart pmca_chart
	ON pmep_b_exp_per.pmep_exp_acc = pmca_chart.pmca_code
LEFT JOIN pmcp_prop_cal pmcp_prop_cal
	ON pmcp_prop_cal.pmcp_prop = pmep_prop
	AND pmcp_prop_cal.pmcp_year = pmep_year
	AND pmcp_prop_cal.pmcp_period = pmep_per
LEFT JOIN pmcm_mast_cal pmcm_mast_cal
	ON pmcp_prop_cal.pmcp_end_dt BETWEEN pmcm_mast_cal.pmcm_start_dt AND pmcm_mast_cal.pmcm_end_dt
	AND pmcm_code = 'Financial'
WHERE pmca_chart.pmca_fees = 1
GO


-----PER CLIENT---------------
--------------------------------------------------------------------------------------------------------------------
CREATE TABLE [dbo].[pmpd_w_door](
	[pmpd_prop] [char](10) NOT NULL,
	[pmpd_code] [char](10) NOT NULL,
	[pmpd_desc] [char](40) NULL,
	[pmpd_external] [bit] NULL,
	[pmpd_divisor] [numeric](2, 0) NULL,
	[pmpd_active] [bit] NULL
) ON [PRIMARY]
--------------------------------------------------------------------------------------------------------------------
--------------------------------------------------------------------------------------------------------------------
CREATE TABLE [dbo].[pmdc_website_count](
	[pmdc_prop] [char](10) NOT NULL,
	[pmdc_door] [char](10) NOT NULL,
	[pmdc_date] [datetime] NOT NULL,
	[pmdc_count] [numeric](11, 0) NULL,
 CONSTRAINT [pmdc_pmdc_website_count_count_pk] PRIMARY KEY CLUSTERED 
(
	[pmdc_prop] ASC,
	[pmdc_door] ASC,
	[pmdc_date] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 90, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
--------------------------------------------------------------------------------------------------------------------
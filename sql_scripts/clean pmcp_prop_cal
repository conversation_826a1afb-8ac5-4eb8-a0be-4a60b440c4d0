CREATE TABLE pmcp_prop_cal2(
	[pmcp_prop] [char](10) NOT NULL,
	[pmcp_year] [numeric](4, 0) NOT NULL,
	[pmcp_period] [numeric](2, 0) NOT NULL,
	[pmcp_start_dt] [datetime] NULL,
	[pmcp_end_dt] [datetime] NULL,
	[pmcp_closed] [bit] NULL,
	[pmcp_cpi] [numeric](12, 7) NULL,
	[pmcp_retail] [bit] NULL DEFAULT 0,
	[pmcp_r_start_dt] [datetime] NULL,
	[pmcp_r_end_dt] [datetime] NULL,
	[pmcp_r_desc] [char](10) NULL,
	[pmcp_r_closed] [bit] NULL DEFAULT 0,
	[pmcp_id] [int] IDENTITY(1,1) NOT NULL,
	[pmcp_gl_closed] [bit] NULL DEFAULT 0,
 CONSTRAINT [pmcp_prop_cal_id_pk] PRIMARY KEY CLUSTERED
(
	[pmcp_id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 90) ON [PRIMARY]
) ON [PRIMARY]

------------------------------------------------------------------------------------

INSERT INTO pmcp_prop_cal2 (pmcp_prop, pmcp_year, pmcp_period, pmcp_start_dt, pmcp_end_dt, pmcp_closed, pmcp_cpi, pmcp_retail, pmcp_r_start_dt, pmcp_r_end_dt, pmcp_r_desc, pmcp_r_closed, pmcp_gl_closed)
SELECT  pmcp_prop, pmcp_year, pmcp_period, pmcp_start_dt, pmcp_end_dt, pmcp_closed, pmcp_cpi, pmcp_retail, pmcp_r_start_dt, pmcp_r_end_dt, pmcp_r_desc, pmcp_r_closed, pmcp_gl_closed FROM (
SELECT ROW_NUMBER() OVER (PARTITION BY pmcp_prop, pmcp_year, pmcp_period ORDER BY pmcp_closed DESC) AS rn, pmcp_prop, pmcp_year, pmcp_period, pmcp_start_dt, pmcp_end_dt, pmcp_closed, pmcp_cpi, pmcp_retail, pmcp_r_start_dt, pmcp_r_end_dt, pmcp_r_desc, pmcp_r_closed, pmcp_gl_closed
FROM pmcp_prop_cal WHERE pmcp_prop != ''
) x WHERE rn = 1

------------------------------------------------------------------------------------

EXEC sp_rename 'pmcp_prop_cal', 'pmcp_prop_cal_old';

------------------------------------------------------------------------------------

EXEC sp_rename 'pmcp_prop_cal2', 'pmcp_prop_cal';

------------------------------------------------------------------------------------

ALTER TABLE pmcp_prop_cal
ADD CONSTRAINT UC_prop_year_period UNIQUE (pmcp_prop, pmcp_year, pmcp_period);

------------------------------------------------------------------------------------
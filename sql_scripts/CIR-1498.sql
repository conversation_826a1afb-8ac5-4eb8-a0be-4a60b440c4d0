CREATE TABLE [dbo].[page_categories] (
	[id][int] IDENTITY(1,1) NOT NULL,
	[user_subgroup_id][int] NOT NULL,
	[name][varchar](50) NOT NULL,
	[slug][varchar](75) NOT NULL,
	[link][varchar](200) NULL DEFAULT NULL,
	[icon][varchar](100) NULL DEFAULT NULL,
    [module][tinyint] NOT NULL DEFAULT (1),
	[status][tinyint] NOT NULL,
	[order][int] NOT NULL,
)  ON [PRIMARY]

CREATE TABLE [dbo].[page_routes] (
	[id][int] IDENTITY(1,1) NOT NULL,
	[page_category_id][int] NOT NULL,
	[page_subcategory_id][int] NULL,
	[name][varchar](50) NOT NULL,
	[slug][varchar](75) NOT NULL,
	[route][varchar](100) NOT NULL,
    [featured][tinyint] NOT NULL DEFAULT (0),
    [display][tinyint] NOT NULL DEFAULT (1),
    [admin][tinyint] NOT NULL DEFAULT (0),
	[status][tinyint] NOT NULL,
	[order][int] NOT NULL,
)  ON [PRIMARY]

CREATE TABLE page_subcategories (
	[id][int] IDENTITY(1,1) NOT NULL,
	[page_category_id][int] NOT NULL,
	[name][varchar](50) NOT NULL,
	[slug][varchar](75) NOT NULL,
    [display][tinyint] NOT NULL DEFAULT (1),
	[status][tinyint] NOT NULL,
	[order][int] NOT NULL,
)  ON [PRIMARY]
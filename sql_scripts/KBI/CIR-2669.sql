
--kbi_main
drop table property_insurance_extra
CREATE TABLE [dbo].[property_insurance_extra] (
    [ID] INT NOT NULL IDENTITY PRIMARY KEY,
    [created_at] DATETIME NOT NULL DEFAULT (GETDATE()),
    [created_by] INT,
    [saleforce_status] VARCHAR(100),
    [property_code] VARCHAR(15),
    [business_name] VARCHAR(100),
    [property_street] VARCHAR(80),
    [property_suburb] VARCHAR(40),
    [property_state] VARCHAR(8),
    [property_postcode] VARCHAR(8),
    [gross_annual_rental] DECIMAL(16,2),
    [policy_start_date] VARCHAR(12),
    [requestor_name] VARCHAR(30),
    [requestor_email] VARCHAR(100),
    [requestor_phone] VARCHAR(30),
    [current_insurer] VARCHAR(100),
    [building_sum_insured] VARCHAR(18),
    [public_liability_limit] VARCHAR(100),
    [notes] VARCHAR(2000),
    [year_built] VARCHAR(4),
    [walls] VARCHAR(100),
    [floors] VARCHAR(100),
    [roof] VARCHAR(100),
    [acp_cladding] VARCHAR(100),
    [eps] VARCHAR(100),
    [asbestos] VARCHAR(100),
    [asbestos_register] VARCHAR(12),
    [heritage] VARCHAR(100),
    [rewired] VARCHAR(12),
    [replumbed] VARCHAR(12),
    [sprinklers] VARCHAR(100),
    [smoke_detector] VARCHAR(100),
    [monitored_fire] VARCHAR(100),
    [fire_hoses] VARCHAR(100),
    [monitored_alarm] VARCHAR(100),
    [cctv] VARCHAR(100),
    [security_guards] VARCHAR(100),
    [deadlocks] VARCHAR(100),
    );

--kbi_claims
drop table property_insurance_claims
CREATE TABLE [dbo].[property_insurance_claims] (
    [ID] INT NOT NULL IDENTITY PRIMARY KEY,
    [created_at] DATETIME NOT NULL DEFAULT (GETDATE()),
    [created_by] INT,
    [property_insurance_extra_id] INT,
    [claim_description] VARCHAR(400),
    [claim_date] VARCHAR(12),
    [claim_amount] decimal(16,2)
    );


--kbi_tenants
drop table kbi_tenants
CREATE TABLE [dbo].[property_insurance_tenants] (
    [ID] INT NOT NULL IDENTITY PRIMARY KEY,
    [created_at] DATETIME NOT NULL DEFAULT (GETDATE()),
    [created_by] INT,
    [property_insurance_extra_id] INT,
    [floor_name] VARCHAR(100),
    [unit_code] VARCHAR(15),
    [unit_name] VARCHAR(100),
    [unit_sqm] VARCHAR(18),
    [unit_status] VARCHAR(15),
    [lease_code] VARCHAR(15),
    [lease_name] VARCHAR(200),
    [occupation] VARCHAR(100)
    );


--kbi_insurers
drop table kbi_insurers
CREATE TABLE [dbo].[property_insurance_insurers] (
    [ID] INT NOT NULL IDENTITY PRIMARY KEY,
    [created_at] DATETIME NOT NULL DEFAULT (GETDATE()),
    [created_by] INT,
    [property_insurance_extra_id] INT,
    [insurer] VARCHAR(100),
    [policy_no] VARCHAR(100),
    [file_name] VARCHAR(150),
    [file_location] TEXT
    );



--kbi_attachments
drop table kbi_attachments
CREATE TABLE [dbo].[property_insurance_attachments] (
    [ID] INT NOT NULL IDENTITY PRIMARY KEY,
    [created_at] DATETIME NOT NULL DEFAULT (GETDATE()),
    [created_by] INT,
    [property_insurance_extra_id] INT,
    [attachment_id] INT
    );

--NPMS: attachments
drop table attachments
CREATE TABLE [npms].[dbo].[attachments] (
    [ID] INT NOT NULL IDENTITY PRIMARY KEY,
    [created_at] DATETIME NOT NULL DEFAULT (GETDATE()),
    [created_by] INT,
    [client_id] INT,
    [trans_type] VARCHAR(50),
    [file_type] VARCHAR(20),
    [file_name] VARCHAR(150),
    [file_location] TEXT,
    );
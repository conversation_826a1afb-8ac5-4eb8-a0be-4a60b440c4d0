DROP VIEW IF EXISTS dailyCashBook;

CREATE VIEW dailyCashBook
AS

SELECT property, allocDate, SUM(changeDaily) as cib,
SUM (SUM(changeDaily)) OVER (PARTITION BY property ORDER BY property, allocDate) AS dailyBalance
FROM
(
    SELECT property, allocDate, SUM(balance) as changeDaily
    FROM
        (SELECT pmxd_prop as property, pmxd_alloc_dt as allocDate, SUM(pmxd_alloc_amt*-1) as balance
        FROM pmxd_ar_alloc
        WHERE pmxd_f_type = 'CSH'
        GROUP BY pmxd_prop, pmxd_alloc_dt
        ) arp
    GROUP BY property, allocDate

    UNION ALL

    SELECT property, allocDate, SUM(balance*-1) as changeDaily
    FROM
        (SELECT pmxc_prop as property, pmxc_alloc_dt as allocDate, SUM(pmxc_alloc_amt*-1) as balance
        FROM pmxc_ap_alloc
        WHERE pmxc_f_type = 'PAY'
        GROUP BY pmxc_prop, pmxc_alloc_dt
        ) ap
    GROUP BY property, allocDate

    UNION ALL

	SELECT pmuc_prop as property, pmuc_rcpt_dt as allocDate, SUM(pmuc_amt*-1) as changeDaily
	FROM pmuc_unall_csh
	GROUP BY pmuc_prop, pmuc_rcpt_dt
) x
GROUP BY property, allocDate
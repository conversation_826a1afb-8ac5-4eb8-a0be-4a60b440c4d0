/* done on dev and live */

CREATE TABLE [dbo].[property_man_fee_acc_budget] (
  [ID] INT NOT NULL IDENTITY PRIMARY KEY,
  [created_at] DATETIME NOT NULL DEFAULT (GETDATE()),
  [financial_year] varchar(4) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
  [property_code] varchar(10) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
  [account_code_from] varchar(10) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
  [man_fee_pct] decimal(19,2) NULL,
  [account_code_to] varchar(10) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
  [man_fee_desc] varchar(40) COLLATE SQL_Latin1_General_CP1_CI_AS NULL
)
GO
CREATE TABLE [dbo].[property_man_fee_budget] (
  [ID] INT NOT NULL IDENTITY PRIMARY KEY,
  [created_at] DATETIME NOT NULL DEFAULT (GETDATE()),
  [financial_year] INT,
  [property_code] varchar(10) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
  [account_code] varchar(10) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
  [man_method_type] INT NULL,
  [man_fee_val] decimal(19,2) NULL,
  [man_fee_pct] decimal(19,2) NULL,
)
GO

CREATE TABLE [dbo].[property_budget_expense_breakdown] (
  [ID] INT NOT NULL IDENTITY PRIMARY KEY,
  [created_at] DATETIME NOT NULL DEFAULT (GETDATE()),
  [financial_year] int,
  [property_code] varchar(10) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
  [lease_code] varchar(10) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
  [lease_name] varchar(100) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
  [floor_code] varchar(10) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
  [floor_name] varchar(100) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
  [unit_code] varchar(10) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
  [unit_name] varchar(100) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
  [unit_area] decimal(19,2) NULL,
  [unit_status] varchar(10) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
  [account_code] varchar(10) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
  [input_type] int,
  [split_type] int NULL,
  [custom_value] decimal(19,2) NULL,
  [custom_percentage] decimal(19,2) NULL,
  [status] int,
)
GO​

CREATE TABLE [dbo].[property_budget_activity_logs] (
  [ID] INT NOT NULL IDENTITY PRIMARY KEY,
  [created_at] DATETIME NOT NULL DEFAULT (GETDATE()),
  [financial_year] int,
  [property_code] varchar(10) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
  [act_desc] varchar(150) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
  [act_comment] varchar(150) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
  [created_by] varchar(100) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL
)
GO​


<?php

//error_reporting(E_ALL ^ E_NOTICE);

  session_start();

  if (!function_exists("ob_get_clean")) {
    function ob_get_clean() {
        $ob_contents = ob_get_contents();
        ob_end_clean();
        return $ob_contents;
    }
}


include('config.php');


define('CLIENTDB',$_SESSION['currentDB']);
define('CLIENTDSN',COREDSN . $_SESSION['currentDB']);
if (!isset($_SESSION['user_name'])) { redirectUser(NPMSURL); }

$dbh = new MSSQL(CLIENTDSN);
$dbh->selectDatabase(CLIENTDB);


        echo '<link rel="stylesheet" href="'.ASSET_DOMAIN.'assets/css/data.css" type="text/css" />
              <b>mapping calendar info</b><br /><br />
              <ul>';

        //-- translate calendars
        $sql = "SELECT DISTINCT pmcm_code FROM pmcm_mast_cal";
        $calendars = $dbh->executeSet($sql);
        foreach ($calendars as $calendar) {
          $check = $dbh->executeScalar("SELECT pmmc_name FROM pmmc_cal_types WHERE pmmc_name='{$calendar[pmcm_code]}'");
          if (!$check) {
            $dbh->executeNonQuery("INSERT INTO  pmmc_cal_types (pmmc_name, pmmc_description, pmmc_frequency) VALUES ('{$calendar[pmcm_code]}','{$calendar[pmcm_code]}','M')");
            echo 'inserted ' . $calendar['pmcm_code'] . '<br/>';
          }
        }
        
        
        $propertyList = $dbh->executeSet("SELECT pmpr_prop AS propertyID FROM pmpr_property");
        foreach ($propertyList as $property) {
          $propertyID = $property['propertyID'];
        
          $match = $dbh->executeSingle("SELECT
                                       pmmc_id AS calendarID,
  pmmc_name AS calendarName,
  COUNT(pmcp_prop) AS calendarCount
  FROM
  pmcp_prop_cal,
  pmcm_mast_cal,
  pmmc_cal_types,
  pmpr_property
  WHERE
  pmmc_name=pmcm_code AND
  pmcp_start_dt=pmcm_start_dt AND
  pmcp_end_dt=pmcm_end_dt AND
  pmcp_year=pmcm_year AND
  pmcp_period=pmcm_period AND
  pmpr_prop=pmcp_prop 
  AND pmpr_prop = '{$propertyID}'
  GROUP BY pmmc_id, pmmc_name
ORDER BY calendarCount DESC");
        echo $propertyID . ' -> ' . $match['calendarName'] . '<br/>';
        $dbh->executeNonQuery("UPDATE pmpr_property SET pmpr_cal_type = '{$match[calendarID]}' WHERE pmpr_prop='{$propertyID}'");
        }
        
     


?>
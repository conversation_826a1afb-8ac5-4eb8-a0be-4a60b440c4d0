<?php

error_reporting(E_ALL ^ E_NOTICE);

  //-- if ob_get_clean doesnt exist, define the function
  if (!function_exists("ob_get_clean")) {
    function ob_get_clean() {
        $ob_contents = ob_get_contents();
        ob_end_clean();
        return $ob_contents;
    }
}


function unsetAll(&$items) {
  foreach ($items as $id => $item) unset($items[$id]);
}


  //-- include and invoke the session handler
  include 'lib/classes/Session.php';
  $sess = new Session();

  include_once 'config.php';


  $referrer = 'http://' . $_SERVER['HTTP_HOST'];
  if ($referrer == HTTPHOST) {
    $sess->set('referrer',HTTPHOST . $_SERVER['SCRIPT_NAME'] . '?' .$_SERVER['QUERY_STRING']);
  } else {
    $sess->drop('referrer');
  }
  $sess->drop('queries');

  //-- invoke the database handler and create a new connection to the system database
  $dbh = new MSSQL(SYSTEMDSN);
  $dbList = dbGetDatabaseList();

  //-- set a type if you want to limit the tasks being processed to a single type (ie - AR/AP/standing charges/whatever - anything can be assigned an arbitrary type at the insertion stage)
  if (isset($_REQUEST['type'])) $type = $_REQUEST['type'];


    $queue = new Queue($type);
    $queue->fetch(TASK_THRESHOLD);
    $startList =  $queue->totalItems;

     logScheduler(date('h:i:sA d/m/Y') . ' <b>Scheduler started (' . $startList . ' items)...</b>');
  dbUpdateStatistic('SchedulerLastRun',time());

  do {
    $task = $queue->next(TASK_THRESHOLD);

    if ($task) {
        $databaseID = $task['databaseID'];
        $database = dbGetDatabase($databaseID);
        //logData(print_array($database));
        if ($database) {
            $clientDB = $database['database_name'];
            $clientDSN = COREDSN . $database['database_name'];
          //logData ('DATABASE => ' . $clientDB);
        }

        //-- set up the user
        $sess->set('registered',true);

	//-- need to register in the database that a user has logged in!
	$userDetails = dbGetUserDetails($task['createdBy']);
        if ($userDetails) $sess->bindAttributesFrom($userDetails);
        $sess->set('dbList', $dbList);
        $sess->set('un',$sess->get('user_name'));
        $sess->set('clientID',$databaseID);
        $sess->set('old_client_id',$databaseID);
        $sess->set('userID',$sess->get('un'));
        $sess->set('currentDB',$database['database_name']);
        $sess->set('database',$database['description']);


        //-- set up global variables used for determining report paths
        $clientDirectory = str_replace(' ','',$database['description']);
        $pathPrefix = REPORTPATH . "/";
        $sess->set('pathPrefix',$pathPrefix);
        $sess->set('clientDirectory',$clientDirectory);
        $globalDownloadLink = "" . $clientDirectory;

        extract($sess->items,EXTR_OVERWRITE);


          //logData(print_array(get_defined_vars()));
        //print_r(get_defined_vars());


        $context = $task['data'];
        $GLOBALS = $context;

        $context[IS_TASK] = true;
        $context[TASK_COMPLETE] = false;



        //-- if a module and command is set (framework prerequisite) then fetch the command
        if (isset($context['module']) && isset($context['command'])) {
        logScheduler('<b>Scheduled Task</b> ' . $context['command']);
          $output = fetchCommand($context['command'],$context['module']);
          //-- a successful run will return TASK_COMPLETE = true in context
          if ($context[TASK_COMPLETE]) {
            $queue->delete();
            logScheduler('<b>Task Complete</b>');
          }
          $context[TASK_COMPLETE] = false;
        }


    }

  } while ($task != null);


  //-- on success work out how many items remain and send a report to the admin
    $queue->fetch(TASK_THRESHOLD);
    $endList = $queue->totalItems;
    $completed = $startList-$endList;
    //sendMail(ADMINMAIL_ADDRESS,ADMINMAIL_NAME,'&nbsp;',"{$startList} items scheduled, {$completed} items completed - {$endList} items remaining");








?>
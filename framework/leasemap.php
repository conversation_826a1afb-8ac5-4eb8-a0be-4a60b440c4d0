<?php

//error_reporting(E_ALL ^ E_NOTICE);

  session_start();

  if (!function_exists("ob_get_clean")) {
    function ob_get_clean() {
        $ob_contents = ob_get_contents();
        ob_end_clean();
        return $ob_contents;
    }
}


//include('config.php');


define('CLIENTDB',$_SESSION['currentDB']);
define('CLIENTDSN',COREDSN . $_SESSION['currentDB']);
if (!isset($_SESSION['user_name'])) { redirectUser(NPMSURL); }

$dbh = new MSSQL(CLIENTDSN);




        echo '<link rel="stylesheet" href="'.ASSET_DOMAIN.'assets/css/data.css" type="text/css" />
              <b>mapping lease info</b><br /><br />
              <ul>';

        $dbMap = array('nscv2','gwv2','fps','lancom','lsv2','vsa','pprop','rn');

        $sql = "
SELECT l.pmlu_prop AS propertyID, r.pmpr_name AS propertyName, l.pmlu_unit as unitID, l.pmlu_lease as leaseID,l.pmlu_occ_from_dt AS fromDate,l.pmlu_occ_to_dt AS toDate, p.pmua_status
FROM  pmlu_l_unit l, pmua_unit_area p, pmpr_property r
WHERE l.pmlu_unit = p.pmua_unit
AND l.pmlu_prop = p.pmua_prop
AND l.pmlu_prop = r.pmpr_prop
AND (l.pmlu_occ_from_dt = p.pmua_from_dt
AND l.pmlu_occ_to_dt = p.pmua_to_dt)
ORDER BY l.pmlu_lease";
        $leaseList = $dbh->executeSet($sql);

                $sql = "
SELECT l.pmlu_prop AS propertyID, r.pmpr_name AS propertyName, l.pmlu_unit as unitID, l.pmlu_lease as leaseID,CONVERT(char(10), l.pmlu_occ_from_dt, 103) AS fromDate,CONVERT(char(10), l.pmlu_occ_to_dt, 103) AS toDate, p.pmua_status, CONVERT(char(10), p.pmua_from_dt, 103) AS ufromDate,CONVERT(char(10), p.pmua_to_dt, 103) AS utoDate
FROM  pmlu_l_unit l, pmua_unit_area p, pmpr_property r
WHERE l.pmlu_unit = p.pmua_unit
AND l.pmlu_prop = p.pmua_prop
AND l.pmlu_prop = r.pmpr_prop
AND (l.pmlu_occ_from_dt > p.pmua_from_dt
AND l.pmlu_occ_to_dt < p.pmua_to_dt)
ORDER BY l.pmlu_lease";
        $leaseRangedList = $dbh->executeSet($sql);



      foreach ($leaseList as $lease) {
            echo '<li><b>' . $lease['leaseID']. '</b> ';

                    $sql = "UPDATE pmua_unit_area SET pmua_lease='{$lease['leaseID']}'
                    WHERE pmua_prop='{$lease['propertyID']}'
                    AND pmua_unit='{$lease['unitID']}'
                    AND pmua_from_dt=CONVERT(date,'{$lease['fromDate']}',103)
                    AND pmua_to_dt=CONVERT(date,'{$lease['toDate']}',103)";
                    $dbh->executeNonQuery($sql);

            echo $lease['fromDate'] .'  to ' . $lease['toDate'] . ' mapped ...</li>';

            }


        echo '</ul><br/><b>table mapping complete :  ' . count($leaseList) . ' records</b><br/><br/>';

        echo '<b>Units with range matching leases -</b> <ul>';

        foreach ($leaseRangedList as $unit) {
            echo '<li>Property <b>' . $unit['propertyID'] . '</b> Unit <b>' . $unit['unitID'] . '</b> From <b>' . $unit['fromDate'] . '</b> To <b>' . $unit['toDate'] . '</b> Unit Area From <b>' . $unit['ufromDate'] . '</b> Unit Area To <b>' . $unit['utoDate'] . '</b></li>';
        }

        echo '</ul><br/><b>' . count($leaseRangedList) . ' records</b><br/><br/>';



        $sql = "SELECT pmua_prop,pmua_unit,CAST(pmua_from_dt AS char(11)) as pmua_from_dt,CAST(pmua_to_dt AS char(11)) as pmua_to_dt FROM pmua_unit_area WHERE pmua_lease IS NULL AND pmua_status='O'";
        $results = $dbh->executeSet($sql);

        echo '<b>Units missing leases -</b> <ul>';

        foreach ($results as $unit) {
            echo '<li>Property <b>' . $unit['pmua_prop'] . '</b> Unit <b>' . $unit['pmua_unit'] . '</b> From <b>' . $unit['pmua_from_dt'] . '</b> To <b>' . $unit['pmua_to_dt'] . '</b></li>';
        }

        echo '</ul><br/><b>' . count($results) . ' records</b><br/><br/>';




?>
<?php
//-- include and invoke the session handler
include 'lib/classes/Session.php';
$sess = new Session ();
//$dispatch = array();

//session_start();
ob_start();

if (!function_exists("ob_get_clean")) {
    function ob_get_clean() {
        $ob_contents = ob_get_contents();
        ob_end_clean();
        return $ob_contents;
    }
}

function validateLogin($userID)
{
    global $sess, $context;
    $verifyLogin = dbGetLoginStats ($userID);
    if ($verifyLogin) {
        $authKeySaved = $verifyLogin['authKey'];
        $authKeyCurrent = md5 ($userID . session_id () . $_SERVER['HTTP_USER_AGENT'] . $sess->get ('_timestamp'));
        if ($authKeyCurrent != $authKeySaved)
        {
            $sess->destroy ();
            $context['action'] = 'timeout';
            executeCommand ('logout');
            exit ();
        }
        pingUser ($userID, $sess->get ('groupID'), HTTPHOST . $_SERVER['REQUEST_URI']);
        setcookie ( 'login' , $sess->get ('un'), time () + 60 * 60 * 24 * 30, '/', 'cirrus8.com.au', true);
        setcookie ( 'uid' , $userID, time () + 60 * 60 * 24 * 30, '/', 'cirrus8.com.au', true);
    }
    else {
        //-- if the user is set but no login entry listed - someone has logged you out :)
        if ($userID)
        {
            $sess->destroy ();
            $context['action'] = 'logout';
            executeCommand ('logout');
            exit ();
        }
    }
}

include('config.php');


date_default_timezone_set ('Australia/Perth');
define ('TODAY', date ('d/m/Y'));

$referrer = 'https://' . $_SERVER['HTTP_HOST'];


if ($referrer == HTTPHOST)
{
    $sess->set ('referrer', HTTPHOST . $_SERVER['SCRIPT_NAME'] . '?' . $_SERVER['QUERY_STRING']);
    // echo HTTPHOST . $_SERVER['SCRIPT_NAME'] . '?' .$_SERVER['QUERY_STRING'];
}
else $sess->drop ('referrer');

$sess->drop ('queries');

$clientDB = $sess->get ('currentDB');
$clientDSN = COREDSN . $sess->get ('currentDB');
// define ('CLIENTDB', $sess->get ('currentDB'));
// define ('CLIENTDSN', COREDSN . $sess->get ('currentDB'));

//-- invoke the database handler and create a new connection to the system database

$dbh = new MSSQL (SYSTEMDSN);

if(SSO_ENABLED){
    if ($_GET['command'] == 'noAccess'){
        executeCommand ('noAccess');
        exit();
    }
    else if ($_GET['command'] == 'home' && $_GET['action'] == 'login' && $_GET['type'] == 'baseauth' && isset($_GET['token'])){
        executeCommand ('baseauth');
    }
    // else if ($_GET['command'] == 'login' && isset($_GET['token'])){
    // 	executeCommand ('baseauth');
    // }
    else{
        if (!$sess->get ('userID')){
            header('Location: '.SSO_LOGIN);
        }
        else{
            if($sess->get ('userID')){
                $userID = $sess->get ('userID');
                $verifyLogin = dbGetLoginStats ($userID);
                if ($verifyLogin) {
                    $authKeySaved = $verifyLogin['authKey'];
                    $authKeyCurrent = md5 ($userID . session_id () . $_SERVER['HTTP_USER_AGENT'] . $sess->get ('_timestamp'));
                    if (!SSO_ENABLED && $authKeyCurrent != $authKeySaved){
                        $sess->destroy ();
                        header("Location: ".logout_url);
                        exit();
                    }
                    pingUser ($userID, $sess->get ('groupID'), HTTPHOST . $_SERVER['REQUEST_URI']);
                    setcookie ( 'login' , $sess->get ('un'), time () + 60 * 60 * 24 * 30, '/', 'cirrus8.com.au', true);
                    setcookie ( 'uid' , $userID, time () + 60 * 60 * 24 * 30, '/', 'cirrus8.com.au', true);

                    // download content --- start

                    global $pathPrefix;

                    include_once('config.php');

                    $pathPrefix_uploads = UPLOAD_BASE . "/";
                    $pathPrefix_uploads = '';

                    if ($_REQUEST['fileID']) {
                        $filename = $_REQUEST['fileID'];
                        if ($_REQUEST['filename']) $DLfilename = $_REQUEST['filename'];
                        if ($filename) $filename = decodeParameter($filename);
                        $filePath = $pathPrefix_uploads . $filename;


//                        pre_print_r($_REQUEST['fileID']);
//                        pre_print_r($filename);
//                        pre_print_r($_REQUEST['filename']);

                        $hasAccess = true;

                        //$userDBDirectories = array('LJHookerCommercialToowoomba', 'OfficeofTownshipLeasingNT'); //used for testing
                        //pre_print_r($_SESSION['databaseGroupList']);
//                        foreach ($_SESSION['databaseGroupList'] as $dbName) {
//                            $dbNameSlash = str_replace(' ','', $dbName).'/';
//
//                            if (strpos($filename, $dbNameSlash) !== false) {
//                                $hasAccess = true;
//                                break;
//                            }
//
//                        }

                        //pre_print_r($hasAccess);
                        //echo $filename; die();

                        if (file_exists($filePath) AND $hasAccess) {

                            $extension = substr($filePath, strrpos($filePath,'.')+1);

                            // required for IE, otherwise Content-disposition is ignored
                            ini_set('zlib.output_compression','Off');

                            switch($extension)
                            {
                                case "pdf": $ctype="application/pdf"; break;
                                case "exe": $ctype="application/octet-stream"; break;
                                case "zip": $ctype="application/zip"; break;
                                case "doc": $ctype="application/msword"; break;
                                case "xls": $ctype="application/vnd.ms-excel"; break;
                                case "xlsx": $ctype="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"; break;
                                case "ppt": $ctype="application/vnd.ms-powerpoint"; break;
                                case "gif": $ctype="image/gif"; break;
                                case "png": $ctype="image/png"; break;
                                case "jpeg": $ctype="image/jpeg"; break;
                                case "jpg": $ctype="image/jpg"; break;
                                case "aba" : $ctype="text/plain"; break;
                                default: $ctype="application/force-download";
                            }




                            header("Pragma: public"); // required
                            header("Expires: 0");
                            header("Cache-Control: must-revalidate, post-check=0, pre-check=0");
                            header("Cache-Control: private",false); // required for certain browsers
                            header("Content-Type: $ctype");
                            header("Content-Transfer-Encoding: none");
                            // change, added quotes to allow spaces in filenames
                            header('Content-Disposition: attachment; filename="'.basename($DLfilename).'";');

                            header("Content-Length: ".filesize($filePath));
                            ob_end_clean();
                            readfile($filePath);
                            exit();


                        } else {

                            exit();
                            $host  = $_SERVER['HTTP_HOST'];
                            $uri   = rtrim(dirname($_SERVER['PHP_SELF']), '/\\');
                            $extra = 'index.php';
                            header("Location: http://$host$uri/$extra?command=error");
                            exit();

                            echo 'File cannot be found - please contact a Tracc Administrator'; }
                    }
                    // -----------------------
                }
                else {
//                    if (!SSO_ENABLED && $userID){
                    $sess->destroy ();
                    header("Location: ".logout_url);
                    exit ();
//                    }
                }
            }
        }
    }
}
else{
    if ($sess->get ('userID')) validateLogin ($sess->get ('userID'));
    else
    {
        if ($_GET['command'] == 'termsOfUse') executeCommand ('termsOfUse');
        else if ($_GET['command'] == 'resetPassword') executeCommand ('resetPassword');
        else if ($_GET['command'] == 'changePassword') executeCommand ('changePassword');
        else if ($_GET['command'] == 'twoFA') executeCommand ('twoFA');
        else if ($_GET['command'] == 'orLogins') executeCommand ('orLogins');
        else if (isset($_GET['code']) && isset($_GET['state'])){
            $context['state'] = $_GET['state'];
            if(preg_match("/cirrus8AzureLogin(\s|$|\.|\,)/", $_GET['state'])){
                $context['action'] = "azureLogin";
                executeCommand ('orLogins');
            }
            else if(preg_match("/cirrus8GoogleLogin(\s|$|\.|\,)/", $_GET['state'])){
                $context['action'] = "googleLogin";
                executeCommand ('orLogins');
            }
            else
                executeCommand ('login');
        }
        else executeCommand ('login');
        exit ();
    }
}

//$host  = $_SERVER['HTTP_HOST'];
//$uri   = rtrim(dirname($_SERVER['PHP_SELF']), '/\\');
//$extra = 'index.php';
//
//
//header("Location: http://$host$uri/$extra?command=error");


?>
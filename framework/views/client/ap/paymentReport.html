<h1>Payment Report</h1>
<b>This payment report shows all payments for the payment type you specify and between the date range you select, exporting the data to PDF.</b>
<br/><br/>

<? if (isset($var['downloadPath'])) { ?> <?=renderDownloadLink($var['downloadPath']) ?><? } ?>

<table class="data-grid" cellspacing="0" cellpadding="0" border="0">
<?=renderErrors($var['validationErrors']) ?>
	<tr class="<?=alternateNextRow()?>">
		<td class="title">Bank Account</td>
		<td>
			<?=renderSmartSearch ('bankID', 'bankID', 'bankAccountName', $var['bankList'], $var['bankID'], 'content', 'index.php?command=paymentReport&module=ap&action=changeBank', 'required="true"')?>
		</td>
	</tr>
    <tr  class="<?=alternateNextRow()?>">
        <td  class="title">Payment Type</td>
        <td><?=renderRadioGroup('paymentType',$var['paymentTypeList'],$var['paymentType'],'','onClick="return ajaxContainer(this.id,\'content\',\'?module=ap&command=paymentReport&action=search\')"') ?> </td>
    </tr>
    <tr  class="<?=alternateNextRow()?>">
        <td  class="title">Date Range</td>
        <td><?=renderSmartDate('fromDate',$var['fromDate']) ?> to <?=renderSmartDate('toDate',$var['toDate']) ?> </td>
    </tr>
    <tr  class="footer">
        <td  colspan="2"><? renderButton('btnSubmit','search &rarr;','onClick="return ajaxContainer(this.id,\'content\',\'?module=ap&command=paymentReport&action=search\')"') ?></td>
    </tr>
</table>

<?php if(isset($var['transactions'])): ?>
<script type="text/javascript">
	$('#main_container').animate({scrollTop: '0'}, 300);
</script>
<?php $itemsCount = count($var['transactions'] ?? []); $i = 0;?>
<br/><br/>
<div style="background: #ACD7EC;padding: 5px 10px;border: 2px solid #3489A1;">
	<h3><?=$var['agentName']?></h3>
	<p><b>Payment Report </b>&nbsp;&nbsp;&nbsp;<?=$var['paymentMethod'] . ' Run ' . $var['batchNumber']?></p>
	<p><b>Payment Date </b>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<?=$var['paymentDate']?></p>
</div>
<br/>
<table class="data-grid" cellspacing="0" cellpadding="0" border="0">
	<tr class="subTitle">
		<? 
			$rowsTotal = 9;
			$bsbDisplay = displayBsbFromSession();

			if ($var['paymentMethod'] == 'EFT') {
				if ($bsbDisplay) {
					$rowsTotal++;
				}
			} else {
				$rowsTotal++;
			}
		?>

		<td>Type</td>
		<td>Invoice Date</td>
		<td>Invoice No.</td>

		<? if($var['paymentMethod'] == 'EFT') { ?>
			<? if (displayBsbFromSession()) { ?>
				<td><?=bsbLabelFromSession()?></td>
			<? } ?>
		<? } else { ?>
			<td>Biller Code</td>
		<? } ?>
		
		<td><?=($var['paymentMethod'] == 'EFT' ?  'Account Number' : 'BPAY Ref.' )?></td>
		<td>Property</td>
		<td>Description</td>
		<td>From</td>
		<td>To</td>
		<td align="right">Amount</td>
	</tr>
	<?php foreach($var['transactions'] as $transaction): $i++;?>
		<?php if(isset($transaction['creditor'])): ?>
		<tr class="subHeader">
			<td colspan="2"><b>Company <?=$transaction['creditor']['credId']?></b></td>
			<td colspan="4"><b><?=$transaction['creditor']['credName']?></b></td>
			<td colspan="<?=($rowsTotal - 6)?>"><b><?=$transaction['creditor']['credAdd']?></b></td>
		</tr>
		<?php endif; ?>
		<?php if($transaction['items']['type'] != ''): ?>
		<tr>
			<td><?=$transaction['items']['type']?></td>
			<td><?=$transaction['items']['invDate']?></td>
			<td><?=$transaction['items']['invNum']?></td>

			<? if($var['paymentMethod'] == 'EFT') { ?>
				<? if (displayBsbFromSession()) { ?>
					<td><?=formatWithDelimiter($transaction['items']['bsb'])?></td>
				<? } ?>
			<? } else { ?>
				<td><?=$transaction['items']['billerCode']?></td>
			<? } ?>
			
			<td><?=($transaction['items']['bankAccountNumber'] ? $transaction['items']['bankAccountNumber'] : $transaction['items']['bpayReference'])?></td>
			<td><?=$transaction['items']['propName']?></td>
			<td><?=$transaction['items']['desc']?></td>
			<td><?=$transaction['items']['fromDate']?></td>
			<td><?=$transaction['items']['toDate']?></td>
			<td align="right"><?=toMoney($transaction['items']['amnt'])?></td>
		</tr>
		<?php endif; ?>
		<?php if(isset($transaction['lastCreditorTotal'])): ?>
		<tr>
			<td colspan="<?=($rowsTotal - 1)?>"><b><?=$transaction['lastCreditorTotal']['paymentMethod'] . ' Number ' . $transaction['lastCreditorTotal']['lastPaymentNum']?></b></td>
			<td align="right"><b><?=toMoney($transaction['lastCreditorTotal']['creditorTotal'])?></b></td>
		</tr>
		<?php endif; ?>
		<?php if($i == $itemsCount): ?>
		<?php if(isset($var['lastCreditorTotal'])):?>
		<tr>
			<td colspan="<?=($rowsTotal - 1)?>"><b><?=$var['lastCreditorTotal']['paymentMethod'] . ' Number ' . $var['lastCreditorTotal']['lastPaymentNum']?></b></td>
			<td align="right"><b><?=toMoney($var['lastCreditorTotal']['creditorTotal'])?></b></td>
		</tr>
		<?php endif; ?>
		<tr>
			<td colspan="<?=($rowsTotal - 1)?>"><b>Payment Total</b></td>
			<td align="right"><strong><?=toMoney($var['total'])?></strong></td>
		</tr>
		<?php endif; ?>
	<?php endforeach; ?>
</table>
<br/><br/><br/>
<?php endif ;?>


<? if ($var['batchList']) { ?>
<table class="data-grid" cellspacing="0" cellpadding="0" border="0">
	<tr class="fieldDescription">
		<td>Run Number</td>
		<td>Reference</td>
		<td>Entries</td>
		<td>Bank</td>
		<td>Date</td>
		<td>User ID</td>
		<td>Total Amount</td>
		<td>Report</td>
	</tr>
	<? foreach ($var['batchList'] as $batch) { ?>
	<tr class="<?=alternateNextRow()?>">
		<td><?=$batch['batchNumber']?></td>
		<td><?=$batch['reference']?> <? if($batch['reference'] != $batch['referenceEnd']) { ?> &rarr; <?=$batch['referenceEnd']?><? } ?></td>
		<td><?=$batch['entries']?></td>
		<td><?=$batch['bank']?></td>
		<td><?=$batch['batchDate']?></td>
		<td><?=$batch['userID']?></td>
		<td><?=toMoney($batch['batchAmount'])?></td>
		<td>
			<input type="button" onClick="return ajaxContainer(null,'content','?command=paymentReport&module=ap&action=fetch&batchNumber=<?=$batch['batchNumber']?>&paymentDate=<?=$batch['batchDate']?>')" class="ui-button ui-widget ui-state-default ui-button-text-only" value="View on PDF"/>
			&nbsp;&nbsp;&nbsp;&nbsp;
			<input type="button" onClick="return ajaxContainer(null,'content','?command=paymentReport&module=ap&action=screenView&batchNumber=<?=$batch['batchNumber']?>&paymentDate=<?=$batch['batchDate']?>')" class="ui-button ui-widget ui-state-default ui-button-text-only" value="View on Screen"/>
		</td>
	</tr>
	<? } ?>
</table>
<? } ?>
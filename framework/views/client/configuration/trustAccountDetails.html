
<h1>Change <?=ucwords(strtolower($_SESSION['country_default']['trust_account']))?> Details <a href="https://cirrus8.atlassian.net/wiki/spaces/WTCKC/pages/8519690/Manage+Bank+Accounts" target="new"><img src="assets/images/icons/help.png" class="icon" title="help"/></a></h1>

<?=renderMessage($var['statusMessage'],'class="infoBox"')?>

<? if (($var['action'] == 'verify' && empty ($var['validationErrors'])) || $var['action'] == 'viewApproved' || $var['action'] == 'viewRejected' || $var['action'] == 'reassign' || ($var['action'] == 'reassigned' && !empty ($var['validationErrors']))) {?>
	<table border="0" class="data-grid" cellpadding="0" cellspacing="0">
		<?=renderErrors($var['validationErrors']) ?>
		<tr>
			<td>
				<div>
					<div>
						<div class="descriptionTxt">&nbsp;</div>
						<div class="headTxt"><?php if($var['action'] == 'viewApproved') : ?>Previous Detail<?php else : ?>Current Detail<?php endif; ?></div>
						<div class="headTxt">Modified Detail</div>

<!--						<div class="descriptionTxt"><strong>Company</strong></div>-->
<!--						<div class="originalTxt"><?=$var['original']['bankID']?> - <?=$var['original']['bankAccountName']?></div>-->
<!--						<div class="modifiedTxt"><?=$var['new']['bankID']?> - <?=$var['new']['bankAccountName']?></div>-->

						<div class="descriptionTxt"><strong>Bank Account Name</strong></div>
						<div class="originalTxt<?=($var['original']['bankAccountName'] != $var['new']['bankAccountName'])?' bold':''?>"><?=$var['original']['bankAccountName']?></div>
						<div class="modifiedTxt<?=($var['original']['bankAccountName'] != $var['new']['bankAccountName'])?' bold':''?>"><?=$var['new']['bankAccountName']?></div>

						<? ////if ($_SESSION['country_default']['display_bsb']) { ?>
						<? //if (cdf_isShown('display_bsb', $var['bankCountry'])) { ?>
						<? if (cdf_isShown('display_bsb', $var['new']['bankCountry'])) { ?>

						<?
							$bsb_format = cdf_displayLabel('bsb_format', $var['new']['bankCountry']);
							$bsb_delimiter = $bsb_format['delimiter'];
							$bsb_frequency = $bsb_format['delimiter_frequency'];
						?>

						<div class="descriptionTxt"><strong><?=cdf_displayLabel('bsb_label', $var['new']['bankCountry'])?> No</strong></div>
						<div class="originalTxt<?=($var['original']['bankBSB'] != $var['new']['bankBSB'])?' bold':''?>"><?=formatWithDelimiter($var['original']['bankBSB'], $bsb_delimiter, $bsb_frequency)?></div>
						<div class="modifiedTxt<?=($var['original']['bankBSB'] != $var['new']['bankBSB'])?' bold':''?>"><?=formatWithDelimiter($var['new']['bankBSB'], $bsb_delimiter, $bsb_frequency)?></div>
						<? } ?>

						<div class="descriptionTxt"><strong>Acc No</strong></div>
						<div class="originalTxt<?=($var['original']['bankAccount'] != $var['new']['bankAccount'])?' bold':''?>"><?=$var['original']['bankAccount']?></div>
						<div class="modifiedTxt<?=($var['original']['bankAccount'] != $var['new']['bankAccount'])?' bold':''?>"><?=$var['new']['bankAccount']?></div>

						<div class="descriptionTxt"><strong>Biller Code</strong></div>
						<div class="originalTxt<?=($var['original']['bpayBillerCode'] != $var['new']['bpayBillerCode'])?' bold':''?>"><?=$var['original']['bpayBillerCode']?></div>
						<div class="modifiedTxt<?=($var['original']['bpayBillerCode'] != $var['new']['bpayBillerCode'])?' bold':''?>"><?=$var['new']['bpayBillerCode']?></div>

						<div class="descriptionTxt"><strong>Bank Name</strong></div>
						<div class="originalTxt<?=($var['original']['bankName'] != $var['new']['bankName'])?' bold':''?>"><?=$var['original']['bankName']?></div>
						<div class="modifiedTxt<?=($var['original']['bankName'] != $var['new']['bankName'])?' bold':''?>"><?=$var['new']['bankName']?></div>

						<div class="descriptionTxt"><strong>Branch Name</strong></div>
						<div class="originalTxt<?=($var['original']['bankBranchName'] != $var['new']['bankBranchName'])?' bold':''?>"><?=$var['original']['bankBranchName']?></div>
						<div class="modifiedTxt<?=($var['original']['bankBranchName'] != $var['new']['bankBranchName'])?' bold':''?>"><?=$var['new']['bankBranchName']?></div>

						<!--
						<div class="descriptionTxt"><strong>Payment Method</strong></div>
						<div class="originalTxt<?=($var['original']['paymentMethod'] != $var['new']['paymentMethod'])?' bold':''?>"><?=$var['paymentMethodList'][$var['original']['paymentMethod']]?></div>
						<div class="modifiedTxt<?=($var['original']['paymentMethod'] != $var['new']['paymentMethod'])?' bold':''?>"><?=$var['paymentMethodList'][$var['new']['paymentMethod']]?></div>
						-->

						<!-- Trust account details - START -->
						<? if($var['original']['bankCode'] == 'MBL'){ ?>
							<!--THIS IS MBL <?=$var['original']['bankCode']?>-->
							<div class="descriptionTxt"><strong>Dir. Upload:Username(MAC)</strong></div>
							<div class="originalTxt<?=($var['original']['directUploadUsername'] != $var['new']['directUploadUsername'])?' bold':''?>"><?=$var['original']['directUploadUsername']?></div>
							<div class="modifiedTxt<?=($var['original']['directUploadUsername'] != $var['new']['directUploadUsername'])?' bold':''?>"><?=$var['new']['directUploadUsername']?></div>
							<!-- Direct Upload: Password is not included -->
							<div class="descriptionTxt"><strong>Dir. Download:Username(MAC)</strong></div>
							<div class="originalTxt<?=($var['original']['downloadStatementClientID'] != $var['new']['downloadStatementClientID'])?' bold':''?>"><?=$var['original']['downloadStatementClientID']?></div>
							<div class="modifiedTxt<?=($var['original']['downloadStatementClientID'] != $var['new']['downloadStatementClientID'])?' bold':''?>"><?=$var['new']['downloadStatementClientID']?></div>
							<!-- Direct Download: Password is not included -->
							<div class="descriptionTxt">Dir. Download:Client/Cust. No.</strong></div>
							<div class="originalTxt<?=($var['original']['downloadStatementClientNumber'] != $var['new']['downloadStatementClientNumber'])?' bold':''?>"><?=$var['original']['downloadStatementClientNumber']?></div>
							<div class="modifiedTxt<?=($var['original']['downloadStatementClientNumber'] != $var['new']['downloadStatementClientNumber'])?' bold':''?>"><?=$var['new']['downloadStatementClientNumber']?></div>
						<? } ?>

						<div class="descriptionTxt"><strong>Street</strong></div>
						<div class="originalTxt<?=($var['original']['bankStreet'] != $var['new']['bankStreet'])?' bold':''?>"><?=$var['original']['bankStreet']?></div>
						<div class="modifiedTxt<?=($var['original']['bankStreet'] != $var['new']['bankStreet'])?' bold':''?>"><?=$var['new']['bankStreet']?></div>

						<div class="descriptionTxt"><strong><?=ucwords(strtolower($_SESSION['country_default']['suburb']))?></strong></div>
						<div class="originalTxt<?=($var['original']['bankCity'] != $var['new']['bankCity'])?' bold':''?>"><?=$var['original']['bankCity']?></div>
						<div class="modifiedTxt<?=($var['original']['bankCity'] != $var['new']['bankCity'])?' bold':''?>"><?=$var['new']['bankCity']?></div>

						<? if (cdf_isShown('display_state', $var['new']['bankCountry'])) { ?>
						<div class="descriptionTxt"><strong>State</strong></div>
						<div class="originalTxt<?=($var['original']['bankState'] != $var['new']['bankState'])?' bold':''?>"><?=$var['original']['bankState']?></div>
						<div class="modifiedTxt<?=($var['original']['bankState'] != $var['new']['bankState'])?' bold':''?>"><?=$var['new']['bankState']?></div>
						<? } ?>

						<div class="descriptionTxt"><strong>Post Code</strong></div>
						<div class="originalTxt<?=($var['original']['bankPostCode'] != $var['new']['bankPostCode'])?' bold':''?>"><?=$var['original']['bankPostCode']?></div>
						<div class="modifiedTxt<?=($var['original']['bankPostCode'] != $var['new']['bankPostCode'])?' bold':''?>"><?=$var['new']['bankPostCode']?></div>

						<div class="descriptionTxt"><strong>Country</strong></div>
						<div class="originalTxt<?=($var['original']['bankCountry'] != $var['new']['bankCountry'])?' bold':''?>"><?=$var['mappedCountryList'][$var['original']['bankCountry']]?></div>
						<div class="modifiedTxt<?=($var['original']['bankCountry'] != $var['new']['bankCountry'])?' bold':''?>"><?=$var['mappedCountryList'][$var['new']['bankCountry']]?></div>

						<div class="descriptionTxt"><strong>Local Currency</strong></div>
						<div class="originalTxt<?=($var['original']['bankCurrency'] != $var['new']['bankCurrency'])?' bold':''?>"><?=$var['currencyList'][$var['original']['bankCurrency']]?></div>
						<div class="modifiedTxt<?=($var['original']['bankCurrency'] != $var['new']['bankCurrency'])?' bold':''?>"><?=$var['currencyList'][$var['new']['bankCurrency']]?></div>

						<div class="descriptionTxt"><strong>Foreign Currency</strong></div>
						<div class="originalTxt<?=($var['original']['foreignCurrency'] != $var['new']['foreignCurrency'])?' bold':''?>"><?=$var['currencyList'][$var['original']['foreignCurrency']]?></div>
						<div class="modifiedTxt<?=($var['original']['foreignCurrency'] != $var['new']['foreignCurrency'])?' bold':''?>"><?=$var['currencyList'][$var['new']['foreignCurrency']]?></div>

						<div class="descriptionTxt"><strong>Tax Code</strong></div>
						<div class="originalTxt<?=($var['original']['bankTaxCode'] != $var['new']['bankTaxCode'])?' bold':''?>"><?=$var['original']['bankTaxCode']?></div>
						<div class="modifiedTxt<?=($var['original']['bankTaxCode'] != $var['new']['bankTaxCode'])?' bold':''?>"><?=$var['new']['bankTaxCode']?></div>

						<div class="descriptionTxt"><strong>Receipt Type</strong></div>
						<div class="originalTxt<?=($var['original']['receiptType'] != $var['new']['receiptType'])?' bold':''?>"><?=$var['receiptTypeList'][$var['original']['receiptType']]?></div>
						<div class="modifiedTxt<?=($var['original']['receiptType'] != $var['new']['receiptType'])?' bold':''?>"><?=$var['receiptTypeList'][$var['new']['receiptType']]?></div>

						<div class="descriptionTxt"><strong>Debit Barred</strong></div>
						<div class="originalTxt<?=($var['original']['debitBarred'] != $var['new']['debitBarred'])?' bold':''?>"><?=$var['yesNoOption'][$var['original']['debitBarred']]?></div>
						<div class="modifiedTxt<?=($var['original']['debitBarred'] != $var['new']['debitBarred'])?' bold':''?>"><?=$var['yesNoOption'][$var['new']['debitBarred']]?></div>

						<div class="descriptionTxt"><strong>DEFT Payment</strong></div>
						<div class="originalTxt<?=($var['original']['deft'] != $var['new']['deft'])?' bold':''?>"><?=$var['yesNoOption'][$var['original']['deft']]?></div>
						<div class="modifiedTxt<?=($var['original']['deft'] != $var['new']['deft'])?' bold':''?>"><?=$var['yesNoOption'][$var['new']['deft']]?></div>

						<div class="descriptionTxt"><strong>PayWay Payment</strong></div>
						<div class="originalTxt<?=($var['original']['payway'] != $var['new']['payway'])?' bold':''?>"><?=$var['yesNoOption'][$var['original']['payway']]?></div>
						<div class="modifiedTxt<?=($var['original']['payway'] != $var['new']['payway'])?' bold':''?>"><?=$var['yesNoOption'][$var['new']['payway']]?></div>

						<div class="descriptionTxt"><strong>Credit Card Payment</strong></div>
						<div class="originalTxt<?=($var['original']['creditCard'] != $var['new']['creditCard'])?' bold':''?>"><?=$var['yesNoOption'][$var['original']['creditCard']]?></div>
						<div class="modifiedTxt<?=($var['original']['creditCard'] != $var['new']['creditCard'])?' bold':''?>"><?=$var['yesNoOption'][$var['new']['creditCard']]?></div>

						<div class="descriptionTxt"><strong>Direct Debit</strong></div>
						<div class="originalTxt<?=($var['original']['directDeposit'] != $var['new']['directDeposit'])?' bold':''?>"><?=$var['yesNoOption'][$var['original']['directDeposit']]?></div>
						<div class="modifiedTxt<?=($var['original']['directDeposit'] != $var['new']['directDeposit'])?' bold':''?>"><?=$var['yesNoOption'][$var['new']['directDeposit']]?></div>

						<div class="descriptionTxt"><strong>Show Owner Company on Receipt</strong></div>
						<div class="originalTxt<?=($var['original']['showOwnerCompanyOnReceipt'] != $var['new']['showOwnerCompanyOnReceipt'])?' bold':''?>"><?=$var['yesNoOption'][$var['original']['showOwnerCompanyOnReceipt']]?></div>
						<div class="modifiedTxt<?=($var['original']['showOwnerCompanyOnReceipt'] != $var['new']['showOwnerCompanyOnReceipt'])?' bold':''?>"><?=$var['yesNoOption'][$var['new']['showOwnerCompanyOnReceipt']]?></div>

						<div class="descriptionTxt"><strong>Show EFT information on invoice</strong></div>
						<div class="originalTxt<?=($var['original']['showEftInfo'] != $var['new']['showEftInfo'])?' bold':''?>"><?=$var['yesNoOption'][$var['original']['showEftInfo']]?></div>
						<div class="modifiedTxt<?=($var['original']['showEftInfo'] != $var['new']['showEftInfo'])?' bold':''?>"><?=$var['yesNoOption'][$var['new']['showEftInfo']]?></div>

						<div class="descriptionTxt"><strong>EFT Reference</strong></div>
						<div class="originalTxt<?=($var['original']['eftReference'] != $var['new']['eftReference'])?' bold':''?>"><?=$var['eftReferenceList'][$var['original']['eftReference']]?></div>
						<div class="modifiedTxt<?=($var['original']['eftReference'] != $var['new']['eftReference'])?' bold':''?>"><?=$var['eftReferenceList'][$var['new']['eftReference']]?></div>

						<div class="descriptionTxt"><strong>Include Property code in Invoice Reference</strong></div>
						<div class="originalTxt<?=($var['original']['showPropertyCode'] != $var['new']['showPropertyCode'])?' bold':''?>"><?=$var['yesNoOption'][$var['original']['showPropertyCode']]?></div>
						<div class="modifiedTxt<?=($var['original']['showPropertyCode'] != $var['new']['showPropertyCode'])?' bold':''?>"><?=$var['yesNoOption'][$var['new']['showPropertyCode']]?></div>

						<div class="descriptionTxt"><strong>Show Owner Details on Invoice</strong></div>
						<div class="originalTxt<?=($var['original']['showOwnerDetail'] != $var['new']['showOwnerDetail'])?' bold':''?>"><?=$var['yesNoOption'][$var['original']['showOwnerDetail']]?></div>
						<div class="modifiedTxt<?=($var['original']['showOwnerDetail'] != $var['new']['showOwnerDetail'])?' bold':''?>"><?=$var['yesNoOption'][$var['new']['showOwnerDetail']]?></div>

						<div class="descriptionTxt"><strong>Show Property Details on Invoice</strong></div>
						<div class="originalTxt<?=($var['original']['showPropertyDetail'] != $var['new']['showPropertyDetail'])?' bold':''?>"><?=$var['yesNoOption'][$var['original']['showPropertyDetail']]?></div>
						<div class="modifiedTxt<?=($var['original']['showPropertyDetail'] != $var['new']['showPropertyDetail'])?' bold':''?>"><?=$var['yesNoOption'][$var['new']['showPropertyDetail']]?></div>

						<div class="descriptionTxt"><strong>Show due date in invoice line items</strong></div>
						<div class="originalTxt<?=($var['original']['showDueDate'] != $var['new']['showDueDate'])?' bold':''?>"><?=$var['yesNoOption'][$var['original']['showDueDate']]?></div>
						<div class="modifiedTxt<?=($var['original']['showDueDate'] != $var['new']['showDueDate'])?' bold':''?>"><?=$var['yesNoOption'][$var['new']['showDueDate']]?></div>


						<div class="descriptionTxt"><strong>Victoria Owner Corp Invoice Format</strong></div>
						<div class="originalTxt<?=($var['original']['victoriaOwnerCorp'] != $var['new']['victoriaOwnerCorp'])?' bold':''?>"><?=$var['yesNoOption'][$var['original']['victoriaOwnerCorp']]?></div>
						<div class="modifiedTxt<?=($var['original']['victoriaOwnerCorp'] != $var['new']['victoriaOwnerCorp'])?' bold':''?>"><?=$var['yesNoOption'][$var['new']['victoriaOwnerCorp']]?></div>

						<div class="descriptionTxt"><strong>Terms / Notice for <?=ucwords(strtolower($_SESSION['country_default']['strata']))?> Invoices</strong></div>
						<div class="originalTxt<?=($var['original']['termNoticeStrata'] != $var['new']['termNoticeStrata'])?' bold':''?>"><?=$var['yesNoOption'][$var['original']['termNoticeStrata']]?></div>
						<div class="modifiedTxt<?=($var['original']['termNoticeStrata'] != $var['new']['termNoticeStrata'])?' bold':''?>"><?=$var['yesNoOption'][$var['new']['termNoticeStrata']]?></div>

						<div class="descriptionTxt"><strong>Terms / Notice PDF File	<?=$var['original']['strataFile'];?></strong></div>
						<div class="originalTxt"><? if($var['original']['strataFile']){?><a href="download.php?fileID=<?=encodeParameter($var['original']['strataFile'])?>"><img src="assets/images/icons/pdf.png" alt="Adobe Logo" class="icon" />&nbsp; Download</a><? }?></div>
						<div class="modifiedTxt"><? if($var['new']['strataFile']){?><a href="download.php?fileID=<?=encodeParameter($var['new']['strataFile'])?>"><img src="assets/images/icons/pdf.png" alt="Adobe Logo" class="icon" />&nbsp; Download</a><? }?></div>

						<div class="descriptionTxt"><strong>User ID</strong></div>
						<div class="originalTxt<?=($var['original']['bankUserID'] != $var['new']['bankUserID'])?' bold':''?>"><?=$var['original']['bankUserID']?></div>
						<div class="modifiedTxt<?=($var['original']['bankUserID'] != $var['new']['bankUserID'])?' bold':''?>"><?=$var['new']['bankUserID']?></div>

						<div class="descriptionTxt"><strong>NAB Customer BPAY Batch ID</strong></div>
						<div class="originalTxt<?=($var['original']['bpayBatchID'] != $var['new']['bpayBatchID'])?' bold':''?>"><?=$var['original']['bpayBatchID']?></div>
						<div class="modifiedTxt<?=($var['original']['bpayBatchID'] != $var['new']['bpayBatchID'])?' bold':''?>"><?=$var['new']['bpayBatchID']?></div>

						<div class="descriptionTxt"><strong>User Name</strong></div>
						<div class="originalTxt<?=($var['original']['bankUserName'] != $var['new']['bankUserName'])?' bold':''?>"><?=$var['original']['bankUserName']?></div>
						<div class="modifiedTxt<?=($var['original']['bankUserName'] != $var['new']['bankUserName'])?' bold':''?>"><?=$var['new']['bankUserName']?></div>

						<div class="descriptionTxt"><strong>Financial Institution</strong></div>
						<div class="originalTxt<?=($var['original']['financialInstitution'] != $var['new']['financialInstitution'])?' bold':''?>"><?=$var['original']['financialInstitution']?></div>
						<div class="modifiedTxt<?=($var['original']['financialInstitution'] != $var['new']['financialInstitution'])?' bold':''?>"><?=$var['new']['financialInstitution']?></div>

						<div class="descriptionTxt"><strong>Remittance</strong></div>
						<div class="originalTxt<?=($var['original']['bankRemittance'] != $var['new']['bankRemittance'])?' bold':''?>"><?=$var['original']['bankRemittance']?></div>
						<div class="modifiedTxt<?=($var['original']['bankRemittance'] != $var['new']['bankRemittance'])?' bold':''?>"><?=$var['new']['bankRemittance']?></div>

						<div class="descriptionTxt"><strong>Customer BPAY User/Customer ID	</strong></div>
						<div class="originalTxt<?=($var['original']['bpayUserID'] != $var['new']['bpayUserID'])?' bold':''?>"><?=$var['original']['bpayUserID']?></div>
						<div class="modifiedTxt<?=($var['original']['bpayUserID'] != $var['new']['bpayUserID'])?' bold':''?>"><?=$var['new']['bpayUserID']?></div>
						<!-- Trust account details - END -->

						<? if ($var['new']['paymentMethod'] == PAY_EFT) { ?>

						<?
							$bsb_format = cdf_displayLabel('bsb_format', $var['bankCountry']);
							$bsb_delimiter = $bsb_format['delimiter'];
							$bsb_frequency = $bsb_format['delimiter_frequency'];
						?>


						<? ////if ($_SESSION['country_default']['display_bsb']) { ?>
							<? if (cdf_isShown('display_bsb', $var['bankCountry'])) { ?>
							<div class="descriptionTxt"><strong><?=cdf_displayLabel('bsb_label', $var['bankCountry'])?></strong></div>
							<div class="originalTxt<?=($var['original']['bsbNumber'] != $var['new']['bsbNumber'])?' bold':''?>"><?=formatWithDelimiter($var['original']['bsbNumber'], $bsb_delimiter, $bsb_frequency)?></div>
							<div class="modifiedTxt<?=($var['original']['bsbNumber'] != $var['new']['bsbNumber'])?' bold':''?>"><?=formatWithDelimiter($var['new']['bsbNumber'], $bsb_delimiter, $bsb_frequency)?></div>
							<? } ?>

							<div class="descriptionTxt"><strong>Bank Account Number</strong></div>
							<div class="originalTxt<?=($var['original']['bankAccountNumber'] != $var['new']['bankAccountNumber'])?' bold':''?>"><?=$var['original']['bankAccountNumber']?></div>
							<div class="modifiedTxt<?=($var['original']['bankAccountNumber'] != $var['new']['bankAccountNumber'])?' bold':''?>"><?=$var['new']['bankAccountNumber']?></div>

							<div class="descriptionTxt"><strong>Bank Account Name</strong></div>
							<div class="originalTxt<?=($var['original']['bankAccountName'] != $var['new']['bankAccountName'])?' bold':''?>"><?=$var['original']['bankAccountName']?></div>
							<div class="modifiedTxt<?=($var['original']['bankAccountName'] != $var['new']['bankAccountName'])?' bold':''?>"><?=$var['new']['bankAccountName']?></div>

							<div class="descriptionTxt"><strong>Bank Name</strong></div>
							<div class="originalTxt<?=($var['original']['bankName'] != $var['new']['bankName'])?' bold':''?>"><?=$var['original']['bankName']?></div>
							<div class="modifiedTxt<?=($var['original']['bankName'] != $var['new']['bankName'])?' bold':''?>"><?=$var['new']['bankName']?></div>

						<? } elseif ($var['new']['paymentMethod'] == PAY_BPAY) { ?>
							<div class="descriptionTxt"><strong>Biller Code</strong></div>
							<div class="originalTxt<?=($var['original']['bpayBillerCode'] != $var['new']['bpayBillerCode'])?' bold':''?>"><?=$var['original']['bpayBillerCode']?></div>
							<div class="modifiedTxt<?=($var['original']['bpayBillerCode'] != $var['new']['bpayBillerCode'])?' bold':''?>"><?=$var['new']['bpayBillerCode']?></div>

						<? } elseif ($var['new']['paymentMethod'] == PAY_CHQ) { ?>
							<div class="descriptionTxt"><strong>Cheque Clearance</strong></div>
							<div class="originalTxt<?=($var['original']['chequeDays'] != $var['new']['chequeDays'])?' bold':''?>"><?=$var['original']['chequeDays']?></div>
							<div class="modifiedTxt<?=($var['original']['chequeDays'] != $var['new']['chequeDays'])?' bold':''?>"><?=$var['new']['chequeDays']?></div>
						<? } ?>

						<div class="descriptionTxt"><strong>Reason</strong></div>
						<div class="spanTxt"><?=$var['new']['reason']?></div>

						<? if ($var['action'] == 'verify') { ?>
							<div class="descriptionTxt"><strong>Date Submitted</strong></div>
							<div class="spanTxt"><?=$var['new']['dateCreated']?></div>
							<div class="descriptionTxt"><strong>Submitted by</strong></div>
							<div class="spanTxt"><?=$var['new']['submittedBy']?></div>
							<div class="descriptionTxt"><strong>For Approval by</strong></div>
							<div class="spanTxt"><?=$var['new']['authoriser']?></div>
						<? } else if (($var['action'] == 'reassign') || ($var['action'] == 'reassigned')) { ?>
							<div class="descriptionTxt"><strong>Date Submitted</strong></div>
							<div class="spanTxt"><?=$var['new']['dateCreated']?></div>
							<div class="descriptionTxt"><strong>Submitted by</strong></div>
							<div class="spanTxt"><?=$var['new']['submittedBy']?></div>
							<div class="descriptionTxt"><strong>For Approval by</strong></div>
							<div class="spanTxt">
								<?=renderDropDownList ('authoriser', 'userID', 'name', $var['userList'], $var['new']['authoriser'])?>
							</div>
						<? } else { ?>
							<div class="descriptionTxt"><strong>Date</strong></div>
							<div class="originalTxt"><?=$var['new']['dateCreated']?></div>
							<div class="modifiedTxt"><?=$var['new']['dateUpdated']?></div>

							<div class="descriptionTxt"><strong>&nbsp;</strong></div>
							<div class="originalTxt"><?=$var['new']['submittedBy']?></div>
							<div class="modifiedTxt"><?=$var['new']['modifiedBy']?></div>
						<? } ?>

						<? if ($var['new']['filename']) { ?>
							<div class="descriptionTxt"><strong>Attachment</strong></div>
							<div class="spanTxt"><a href="download.php?fileID=<?=encodeParameter($var['new']['downloadLink'])?>"><?=$var['new']['filename']?></a></div>
						<? } ?>
					</div>
				</div>
				<div style="float:right;margin:10px;">
				<?php if(($var['action'] == 'verify') && ((isset($var['selectedAuthoriser'])) && ($var['selectedAuthoriser']))) : ?>
					<?=renderButton('btnApprove','Approve','onclick="return ajaxContainer(this.id,\'content\',\'?module=' . $var['module'] . '&command=' . $var['command'] . '&auditID=' . $var['auditID'] . '&action=approve\')"')?>
					<?=renderButton('btnReject','Reject','onclick="return ajaxContainer(this.id,\'content\',\'?module=' . $var['module'] . '&command=' . $var['command'] . '&auditID=' . $var['auditID'] . '&action=reject\')"')?>
				<? endif; ?>
				<!-- Disable reassign -->
				<?php if(($var['action'] == 'verify') && ($_SESSION['user_id'] == $var['new']['userCreated'])) : ?>
					<?//=renderButton('btnReassign','Reassign','onclick="return ajaxContainer(this.id,\'content\',\'?module=' . $var['module'] . '&command=' . $var['command'] . '&auditID=' . $var['auditID'] . '&action=reassign\')"')?>
				<? endif; ?>
				<?php if(($var['action'] == 'reassign' || $var['action'] == 'reassigned') && ($_SESSION['user_id'] == $var['new']['userCreated'])) : ?>
					<?=renderButton('btnReassigned','Save','onclick="return ajaxContainer(this.id,\'content\',\'?module=' . $var['module'] . '&command=' . $var['command'] . '&auditID=' . $var['auditID'] . '&action=reassigned\')"')?>
				<? endif; ?>
				</div>
				<? if ($var['new']['downloadLink']) { ?>

				<? } ?>
			</td>
		</tr>
	</table>
<? //} else { ?> <!-- o -->
<? } else if($var['isPM'] == 0) { ?>
	<form action="?module=<?=$var['module']?>&command=<?=$var['command']?>&action=submit&sc=with-file-upload" enctype="multipart/form-data" id="form" method="post" name="form">
		<?=renderHidden('max_file_size',********)?>

		<? if ($var['companyID']) { ?>
			<? if ($var['paymentMethod'] != PAY_EFT) { ?>
				<? if (displayBsbFromSession()) { ?>
				<?=renderHidden('bsbNumber',$var['bsbNumber'])?>
				<? } ?>

				<?=renderHidden('bankAccountNumber',$var['bankAccountNumber'])?>
				<?=renderHidden('bankAccountName',$var['bankAccountName'])?>
				<?=renderHidden('bankName',$var['bankName'])?>
			<? } ?>
			<? if ($var['paymentMethod'] != PAY_BPAY) { ?>
				<?=renderHidden('bpayBillerCode',$var['bpayBillerCode'])?>
			<? } ?>
			<?=renderHidden('debtor',$var['debtor'])?>
		<? } ?>
		<table border="0" class="data-grid" cellpadding="0" cellspacing="0">
			<?=renderErrors($var['validationErrors']) ?>
			<? if (!$var['noAuthoriser']) { ?>
				<!--
				<tr>
					<td class="title">Company</td>
					<td class="required">*</td>
					<td><?=renderSmartSearch('companyID','companyID','companyName',$var['companyList'],$var['companyID'],'content','?module=' . $var['module'] . '&command=' . $var['command'] . '&action=retrieve');?></td>
				</tr>
				-->
				<tr>
					<td class="title"><?=ucwords(strtolower($_SESSION['country_default']['trust_account']))?></td>
					<td class="required">*</td>
					<td><?=renderSmartSearch('bankID','bankID','bankAccountName',$var['bankAccountList'],$var['bankID'],'content','?module=' . $var['module'] . '&command=' . $var['command'] . '&action=retrieve');?></td>
				</tr>
				<? //if ($var['companyID']) { ?>
				<? if ($var['bankID']) { ?>
					<!-- ROWS - Uneditable section - START -->
					<tr class="<?=alternateNextRow ()?>">
						<td class="title2">Bank ID</td>
						<td class="required">&nbsp;</td>
						<td>
							<?=$var['bankID']?>
							<?=renderHidden ('bankID', $var['bankID'])?>
						</td>
					</tr>
					<tr class="<?=alternateNextRow ()?>">
						<td class="title2">Name</td>
						<td class="required">&nbsp;</td>
						<td>
							<?=$var['bankAccountName']?>
							<?=renderHidden ('bankAccountName', $var['bankAccountName'])?>
						</td>
					</tr>

					<?
						$bsb_format = cdf_displayLabel('bsb_format', $var['bankCountry']);
						$bsb_delimiter = $bsb_format['delimiter'];
						$bsb_frequency = $bsb_format['delimiter_frequency'];
					?>

					<? ////if ($_SESSION['country_default']['display_bsb']) { ?>
					<? if (cdf_isShown('display_bsb', $var['bankCountry'])) { ?>
					<tr class="<?=alternateNextRow ()?>">
						<td class="title2"><?=cdf_displayLabel('bsb_label', $var['bankCountry'])?> No</td>
						<td class="required">&nbsp;</td>
						<td>
							<?=formatWithDelimiter($var['bankBSB'], $bsb_delimiter, $bsb_frequency)?>
							<?=renderHidden ('bankBSB', $var['bankBSB'])?>
						</td>
					</tr>
					<? } ?>

					<tr class="<?=alternateNextRow ()?>">
						<td class="title2">Acc No</td>
						<td class="required">&nbsp;</td>
						<td>
							<?=$var['bankAccount']?>
							<?=renderHidden ('bankAccount', $var['bankAccount'])?>
						</td>
					</tr>
					<tr class="<?=alternateNextRow ()?>">
						<td class="title2">Biller Code</td>
						<td class="required">&nbsp;</td>
						<td>
							<?=$var['bpayBillerCode']?>
							<?=renderHidden ('bpayBillerCode', $var['bpayBillerCode'])?>
						</td>
					</tr>
					<tr class="<?=alternateNextRow ()?>">
						<td class="title2">Bank Name</td>
						<td class="required">&nbsp;</td>
						<td>
							<?=$var['bankName']?>
							<?=renderHidden ('bankName', $var['bankName'])?>
						</td>
					</tr>
					<tr class="<?=alternateNextRow ()?>">
						<td class="title2">Branch Name</td>
						<td class="required">&nbsp;</td>
						<td>
							<?=$var['bankBranchName']?>
							<?=renderHidden ('bankBranchName', $var['bankBranchName'])?>
						</td>
					</tr>
					<!-- ROWS - Uneditable section - END -->

					<? if ($var['bankCode'] == 'MBL') { ?>
						<tr class="subTitle"><td colspan="3">Macquarie Credentials</td></tr> <!-- HEADER - Macquarie Credentials -->
						<!-- ROWS - Macquarie Credentials - START -->
						<tr class="highlight">
							<td align="left" colspan="3">Direct Upload</td>
						</tr>
						<tr class="<?=alternateNextRow()?>">
							<td class="title2">Username (MAC)</td>
							<td class="required"></td>
							<td><?=renderTextBox ('directUploadUsername', $var['directUploadUsername'])?></td>
						</tr>
						<tr class="<?=alternateNextRow()?>">
							<td class="title2">Password</td>
							<td class="required"></td>
							<td><?=renderPasswordBox ('directUploadPassword', $var['directUploadPassword'], 'autocomplete="new-password"')?></td>
						</tr>

						<tr class="highlight">
							<td align="left" colspan="3">Direct Download</td>
						</tr>
						<tr class="<?=alternateNextRow()?>">
							<td class="title2">Username (MAC)</td>
							<td class="required"></td>
							<td><?=renderTextBox ('downloadStatementClientID', $var['downloadStatementClientID'])?></td>
						</tr>
						<!-- For Download Statement Password, got part from else statement from if statement if var action is equal to add -->
						<tr class="<?=alternateNextRow()?>">
							<td class="title2">Password</td>
							<td class="required"></td>
							<td><?=renderPasswordBoxWithPlaceHolder ('downloadStatementPassword', $var['downloadStatementPassword'] ? '' : '')?></td>
						</tr>
						<tr class="<?=alternateNextRow()?>">
							<td class="title2">Client / Customer Number</td>
							<td class="required"></td>
							<td><?=renderTextBox ('downloadStatementClientNumber', $var['downloadStatementClientNumber'])?></td>
						</tr>
					<? } /* End of if statement for MBL */ ?>
					<!-- ROWS - Macquarie Credentials - END -->

					<tr class="subTitle"><td colspan="3">Location</td></tr> <!-- HEADER - Location -->
					<!-- ROWS - Location - START -->
					<tr class="<?=alternateNextRow ()?>">
						<td class="title2">Street</td>
						<td class="required">*</td>
						<td><?=renderTextBox ('bankStreet', $var['bankStreet'])?></td>
					</tr>
					<tr class="<?=alternateNextRow ()?>">
						<td class="title2"><?=ucwords(strtolower($_SESSION['country_default']['suburb']))?></td>
						<td class="required">*</td>
						<td><?=renderTextBox ('bankCity', $var['bankCity'])?></td>
					</tr>

					<? ////if (cdf_displayLabel('display_state')) { ?>
					<? if (cdf_isShown('display_state', $var['bankCountry'])) { ?>
					<tr class="<?=alternateNextRow ()?>">
						<td class="title2">State</td>
						<td class="required">*</td>
						<td><?=renderDropDownList ('bankState', 'stateCode', 'stateName', $var['stateList'], $var['bankState'])?></td>
					</tr>
					<? } ?>

					<tr class="<?=alternateNextRow ()?>">
						<td class="title2">Post Code</td>
						<td class="required">*</td>
<!--						<td><?=renderTextBox ('bankPostCode', $var['bankPostCode'], 'maxlength="' . $_SESSION['country_default']['post_code_length'] . '" size="7"')?></td>-->
						<td><?=renderTextBox ('bankPostCode', $var['bankPostCode'], 'size="10"')?></td>
					</tr>
					<tr class="<?=alternateNextRow ()?>">
						<td class="title2">Country</td>
						<td class="required">*</td>
<!--						<td><?=renderDropDownList ('bankCountry', 'countryCode', 'countryName', $var['countryList'], $var['bankCountry'])?></td>-->
						<td><?=renderDropDownList ('bankCountry', 'countryCode', 'countryName', $var['countryList'], $var['bankCountry'], 'onChange="return ajaxContainer(this.id,\'content\',\'?action='.$var['action'].'&change=changeCountry&command=trustAccountDetails&module=configuration\')"')?></td>
					</tr>
					<tr class="<?=alternateNextRow ()?>">
						<td class="title2">Local Currency</td>
						<td class="required">*</td>
						<td><?=renderSimpleDropDownList ('bankCurrency', $var['currencyList'], $var['bankCurrency'])?></td>
					</tr>
					<tr class="<?=alternateNextRow ()?>">
						<td class="title2">Foreign Currency</td>
						<td class="required">*</td>
						<td><?=renderSimpleDropDownList ('foreignCurrency', $var['currencyList'], $var['foreignCurrency'])?></td>
					</tr>
					<tr class="<?=alternateNextRow ()?>">
						<td class="title2">Tax Code</td>
						<td class="required">&nbsp;</td>
						<td><?=renderDropDownList ('bankTaxCode', 'taxCode', 'taxDescription', $var['bankTaxList'], $var['bankTaxCode'])?></td>
					</tr>
					<!-- ROWS - Location - END -->

					<tr class="subTitle"><td colspan="3">Receipt</td></tr> <!-- HEADER - Receipt -->
					<!-- ROWS - Receipt - START -->
					<tr class="<?=alternateNextRow ()?>">
						<td class="title2">Receipt Type</td>
						<td class="required">*</td>
						<td><?=renderSimpleDropDownList ('receiptType', $var['receiptTypeList'], $var['receiptType'])?></td>
					</tr>
					<!-- ROWS - Receipt - END -->

					<tr class="subTitle"><td colspan="3">Direct Banking</td></tr> <!-- HEADER - Direct Banking -->
					<!-- ROWS - Direct Banking - START -->
					<tr class="<?=alternateNextRow ()?>">
						<td class="title2">Debit Barred</td>
						<td class="required">*</td>
						<td><?=renderRadioGroup ('debitBarred', $var['yesNoOption'], $var['debitBarred']); ?>
							<font style="color:red;"><b> Setting debit barred to NO will allow the bank account to be overdrawn. <?=ucwords(strtolower($_SESSION['country_default']['trust_account']))?>s should always have the setting as Yes.</b></font>
						</td>
					</tr>
					<tr class="<?=alternateNextRow ()?>">
						<td class="title2">DEFT Payment</td>
						<td class="required">*</td>
						<td><?=renderRadioGroup ('deft', $var['yesNoOption'], $var['deft']);?>
							<font style="color:red;"><b> Only set DEFT payment to YES if you bank with Macquarie and utilise their DEFT payment services. This results in DEFT details appearing on their tenant invoice.</b></font>
						</td>
					</tr>

					<tr class="<?=alternateNextRow ()?>">
						<td class="title2">PayWay Payment</td>
						<td class="required">*</td>
						<td><?=renderRadioGroup ('payway', $var['yesNoOption'], $var['payway']);?>
							<font style="color:red;"><b> Set PayWay to Yes to enable Westpac PayWay to appear on tenant invoices.</b></font>
						</td>
					</tr>

					<tr class="<?=alternateNextRow ()?>" id="paywayRow" style='<?=$var['payway'] ? "" : "display: none"?>'>
					<td class="title2">PayWay Biller Code</td>
					<td class="required">*</td>
					<td><?=renderTextBox ('paywayBillerCode', $var['paywayBillerCode'])?></td>
					</tr>

					<tr class="<?=alternateNextRow ()?>">
						<td class="title2">Credit Card Payment</td>
						<td class="required">*</td>
						<td><?=renderRadioGroup ('creditCard', $var['yesNoOption'], $var['creditCard']);?>
							<font style="color:red;"><b> Set the Credit Card option to yes to enable credit card payments details to appear on tenant invoices. You will also need to specify a link to your provider. </b></font>
						</td>
					</tr>

					<tr class="<?=alternateNextRow ()?>" id="providerRow" style='<?=$var['creditCard'] ? "" : "display: none"?>'>
					<td class="title2">Provider</td>
					<td class="required">*</td>
					<td><?=renderTextBox ('provider', $var['provider'])?></td>
					</tr>

					<tr class="<?=alternateNextRow ()?>">
						<td class="title2">Direct Debit</td>
						<td class="required">*</td>
						<td><?=renderRadioGroup ('directDeposit', $var['yesNoOption'], $var['directDeposit']);?>
							<font style="color:red;"><b> Only set direct debit to YES if you collect payments from tenants by direct debit. This will be noted on the tenant invoice that they do not need to pay themselves as the amount will be direct debited. This will allow direct debit files to be created.</b></font>
						</td>
					</tr>
					<tr class="<?=alternateNextRow ()?>">
						<td class="title2">Show Owner Company on Receipt</td>
						<td class="required">*</td>
						<td><?=renderRadioGroup ('showOwnerCompanyOnReceipt', $var['yesNoOption'], $var['showOwnerCompanyOnReceipt']); ?>
							<font style="color:red;"><b> </b></font>
						</td>
					</tr>
					<tr class="<?=alternateNextRow ()?>">
						<td class="title2">Show EFT information on invoice</td>
						<td class="required">*</td>
						<td><?=renderRadioGroup ('showEftInfo', $var['yesNoOption'], $var['showEftInfo']);?>
						</td>
					</tr>
					<tr class="<?=alternateNextRow ()?>">
						<td class="title2">EFT Reference</td>
						<td class="required">*</td>
						<td><?=renderRadioGroup ('eftReference', $var['eftReferenceList'], $var['eftReference']);?>
						</td>
					</tr>
					<tr class="<?=alternateNextRow ()?>">
						<td class="title2">Include Property code in Invoice Reference</td>
						<td class="required">*</td>
						<td><?=renderRadioGroup ('showPropertyCode', $var['yesNoOption'], $var['showPropertyCode']);?>
						</td>
					</tr>
					<tr class="<?=alternateNextRow ()?>">
						<td class="title2">Show Owner Details on Invoice</td>
						<td class="required">*</td>
						<td><?=renderRadioGroup ('showOwnerDetail', $var['yesNoOption'], $var['showOwnerDetail']);?>
						</td>
					</tr>
					<tr class="<?=alternateNextRow ()?>">
						<td class="title2">Show Property Details on Invoice</td>
						<td class="required">*</td>
						<td><?=renderRadioGroup ('showPropertyDetail', $var['yesNoOption'], $var['showPropertyDetail']);?>
						</td>
					</tr>
					<tr class="<?=alternateNextRow ()?>">
						<td class="title2">Show due date in invoice line items</td>
						<td class="required">*</td>
						<td><?=renderRadioGroup ('showDueDate', $var['yesNoOption'], $var['showDueDate']);?>
						</td>
					</tr>
					<tr class="<?=alternateNextRow ()?>">
						<td class="title2">User ID</td>
						<td class="required">&nbsp;</td>
						<td><?=renderTextBox ('bankUserID', $var['bankUserID'])?></td>
					</tr>
					<tr class="<?=alternateNextRow ()?>">
						<td class="title2">NAB Customer BPAY Batch ID</td>
						<td class="required">&nbsp;</td>
						<td><?=renderTextBox ('bpayBatchID', $var['bpayBatchID'], 'maxlength="10"')?>
							<font style="color:red;"><b> &nbsp;&nbsp;&nbsp;If you bank with NAB and have batch BPAYs enabled then enter your unique BPAY batch ID in this field. This will allow you to create batch BPAY files for import.</b></font>
						</td>
					</tr>
					<tr class="<?=alternateNextRow ()?>">
						<td class="title2">User Name</td>
						<td class="required">&nbsp;</td>
						<td><?=renderTextBox ('bankUserName', $var['bankUserName'])?></td>
					</tr>
					<tr class="<?=alternateNextRow ()?>">
						<td class="title2">Financial Institution</td>
						<td class="required">&nbsp;</td>
						<td><?=renderTextBox ('financialInstitution', $var['financialInstitution'])?></td>
					</tr>
					<tr class="<?=alternateNextRow ()?>">
						<td class="title2">Remittance</td>
						<td class="required">&nbsp;</td>
						<td><?=renderTextBox ('bankRemittance', $var['bankRemittance'])?></td>
					</tr>
					<tr class="<?=alternateNextRow ()?>">
						<td class="title2" nowrap="true">Customer BPAY User/Customer ID</td>
						<td class="required">&nbsp;</td>
						<td><?=renderTextBox ('bpayUserID', $var['bpayUserID'], 'maxlength="10"')?>
							<font style="color:red;"><b> &nbsp;&nbsp;&nbsp;If you are registered with BPAY payments system and have a BPAY biller code, then inserting the number here will cause BPAY details to be displayed on tenant invoices and allow tenants to pay by BPAY. </b></font>
						</td>
					</tr>
					<!-- ROWS - Direct Banking - END -->
					<!--------------------------------------------------------------------------------------------->
					<!--
					<tr>
						<td class="title">Preferred Payment Method</td>
						<td class="required">*</td>
						<td><?=renderRadioGroup ('paymentMethod', $var['paymentMethodList'], $var['paymentMethod'], '', 'onClick="return ajaxContainer(this.id, \'content\',\'?module=' . $var['module'] . '&command=' . $var ['command'] . '\');"')?></td>
					</tr>
					-->
					<? //if ($var['paymentMethod'] == PAY_EFT) { ?>
						<!--
						<tr>
							<td class="title">BSB</td>
							<td class="required">*</td>
							<td><?=renderTextBox('bsbNumber',$var['bsbNumber'],'size="15" maxlength="6"')?></td>
						</tr>
						<tr>
							<td class="title">Bank Account Number</td>
							<td class="required">*</td>
							<td><?=renderTextBox('bankAccountNumber',$var['bankAccountNumber'],'size="15" maxlength="9"')?></td>
						</tr>
						<tr>
							<td class="title">Account Name</td>
							<td class="required">*</td>
							<td><?=renderTextBox('bankAccountName',$var['bankAccountName'],'size="30"')?></td>
						</tr>
						<tr>
							<td class="title">Bank Name</td>
							<td class="required">&nbsp;</td>
							<td><?=renderTextBox('bankName',$var['bankName'],'size="30"')?></td>
						</tr>
						-->
					<? //} elseif ($var['paymentMethod'] == PAY_BPAY) { ?>
						<!--
						<tr>
							<td class="title">BPAY Biller Code</td>
							<td class="required">*</td>
							<td><?=renderTextBox('bpayBillerCode',$var['bpayBillerCode'],'size="7" maxlength="10"')?></td>
						</tr>
						-->
					<? //} elseif ($var['paymentMethod'] == PAY_CHQ) { ?>
						<!--
						<tr>
							<td class="title">Cheque Clearance</td>
							<td class="required">*</td>
							<td><?=renderSimpleDropDownList('chequeDays',$var['dayList'],$var['chequeDays'])?></td>
						</tr>
						-->
					<? //} ?>
					<tr>
						<td class="title">Reason</td>
						<td class="required">*</td>
						<td><?=renderTextBox('reason',$var['reason'],'size="100" maxlength="255"')?></td>
					</tr>
					<tr>
						<td class="title">Attachment</td>
						<td class="required">&nbsp;</td>
						<td><input type="file" name="file" id="file" class="attachment-upload" /></td>
					</tr>
					<tr>
						<td class="title">Authoriser</td>
						<td class="required">*</td>
						<td><?=renderDropDownList ('authoriser', 'userID', 'name', $var['userList'], $var['authoriser'])?></td>
					</tr>

					<tr class="footer">
						<td colspan="3">
							<?=renderButton('btnSubmit','Submit for Approval &rarr;')?>
						</td>
					</tr>
				<? } ?>
			<? } ?>
		</table>
	</form>
<? } ?>

<script type="text/javascript" language="javascript">
	var readURL = function(input) {
		var file = input.files[0];
		var fileType = file["type"];
		var fileSize = file["size"];

		if(fileSize > 5242880){
			alert("Unable to upload file because the size exceeds the maximum allowed file size of 5MB.");
			document.getElementById('file').value = ""; //reset value of attachment since file size is not allowed
		}
	}

	$(".attachment-upload").on('change', function(){
		readURL(this);
	});

	$(function()
	{
		$("#creditCard").change(function()
		{
			if($("[name='creditCard']:checked").val() == 1 )
				$("#providerRow").css("display","");
			else
			{
				$("#providerRow").css("display","none");
				$("[name='provider']").val('');
			}

		});

		$("#payway").change(function()
		{
			if($("[name='payway']:checked").val() == 1 )
				$("#paywayRow").css("display","");
			else
			{
				$("#paywayRow").css("display","none");
				$("[name='paywayBillerCode']").val('');
			}

		});

        //---Script to disable submit after first submission
        $("#form").submit(function () {
            $("#btnSubmit").attr("disabled", true);
            return true;
        });

	});
	function confirmDebitBarred(radio) {

		var ask = confirm("IMPORTANT – setting debit barred to No will allow the bank account to be overdrawn. Client accounts should always have the setting as Yes. Click OK to continue with the change.");

		var labels = document.getElementsByTagName('LABEL');
		for (var i = 0; i < labels.length; i++) {
			if (labels[i].htmlFor != '') {
				var elem = document.getElementById(labels[i].htmlFor);
				if (elem)
					elem.label = labels[i];
			}
		}

		if (ask) {
			//alert("You clicked ok"+radio.value);
		}
		else {
			//radio.label.aria-pressed = false;
			if (radio.id == 'debitBarred_1') //yes
			{
				eval('debitBarred_1.label.setAttribute("class","ui-button ui-widget ui-state-default ui-button-text-only ui-corner-left")');
				eval('debitBarred_0.label.setAttribute("class","ui-state-active ui-button ui-widget ui-state-default ui-button-text-only ui-corner-right")');
			}
			else if (radio.id == 'debitBarred_0') //no
			{
				//alert('no');
				eval('debitBarred_1.label.setAttribute("class","ui-button ui-widget ui-state-default ui-button-text-only ui-corner-left ui-state-active")');
				eval('debitBarred_0.label.setAttribute("class","ui-button ui-widget ui-state-default ui-button-text-only ui-corner-right")');
			}

			return false;
		}
	}
</script>
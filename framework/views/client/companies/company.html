
<div id="manage-company">
<? if ($_SESSION['readOnlyAccess'] != 1) { ?>
<link type="text/css" href="<?=ASSET_DOMAIN?>assets/semantic-ui/semantic.css?<?=CSS_VERSION?>" rel="stylesheet" />
<!--<script type="text/javascript" src="<?=ASSET_DOMAIN?>assets/semantic-ui/semantic.js?<?=JS_VERSION?>"></script>-->
<link type="text/css" href="assets/autocompleteSuburb/autocompleteSuburb.css?<?=CSS_VERSION?>" rel="stylesheet" />
<script type="text/javascript" src="assets/autocompleteSuburb/autocompleteSuburb.js?<?=JS_VERSION?>"></script>
<h1>New Company <a href="<?= $var['HELPLINKS']['pm_company_help'] ?>" target="new">&nbsp; <img src="<?=ASSET_DOMAIN?>assets/images/icons/help.png" class="icon" title="help"/></a></h1>
<b>The new company form is used to advise <?=ucwords(strtolower($_SESSION['country_default']['trust_account']))?>s of new supplier/s and owner/s details.</b>
<br/>The system classifies tenants (debtor), owners and suppliers as companies. The tenant is normally set up as a debtor company when the new lease form is completed. The owner and supplier are set up by completing this company form.
<br/><br/>

<?=renderMessage($var['message'],'class="infoBox"')?>
<?=renderHidden('ownerPaymentAccount',$var['ownerPaymentAccount'])?>
<?=renderHidden('companyStatus',$var['companyStatus'])?>
<?=renderHidden('primaryCompanyID',$var['primaryCompanyID'])?>

<table class="data-grid" cellpadding="0" cellspacing="0" border="0">
	<?=renderErrors($var['validationErrors']) ?>
	<? if($var['companyStatus'] == 3){?>
	<tr class="<?=alternateNextRow()?>">
		<td colspan="3" align="right"><strong>Rejected Feedback  &nbsp; &nbsp;</strong><textarea cols="60" rows="5" id="comments" name="comments" disabled><?=$var['rejectedComment']?></textarea></td>
	</tr>
	<? }?>
			<tr class="<?=alternateNextRow()?>"><td class="title">Company Code</td><td class="required"></td>
				<td>
					<?if($var['companyStatus'] == 3){ ?>
					<?=$var['companyID']?>
					<?=renderHidden('companyID',$var['companyID'])?>
					<?}else{ ?>
                        <?=renderSmartSearchTextBox('companyID',strtoupper($var['companyID']),'content','index.php?command=company&module=companies&action=checkCompany','required="true" onChange="return smartSearch(this,\'companyIDList\');"')?>
                        <? if ($var['exists']) { ?>This company already exists
					<? }?>
					<? if (($var['existsLive'] ) || ($var['status'] > 0)) { ?>
					<a href="#" id="viewDetails" onclick="return ajaxContainer(null,'content','?command=view&module=companies&action=switchView')">view details</a>
					<? } else { ?>
					<a href="#" id="viewDetails" onclick="return ajaxContainer(null,'content','?command=company&module=companies&action=switchView')">view details</a>
					<? } ?>
					<? } ?>

			</td>
			</tr>
<tr class="<?=alternateNextRow()?>">
	<td class="title">Company Type</td>
	<td class="required"></td>
	<td>
		<?=renderCheckBox('debtor','Debtor',true,$var['debtor'],'onClick="return ajaxContainer(this.id, \'content\',\'?module=companies&command=company\');"')?>
		<?=renderCheckBox('owner','Owner',true,$var['owner'], 'onClick="return ajaxContainer(this.id, \'content\',\'?action=selectOwner&module=companies&command=company\');"')?>
		<?=renderCheckBox('supplier','Supplier',true, $var['supplier'],'onClick="return ajaxContainer(this.id, \'content\',\'?module=companies&command=company\');"')?>
		<? if ($var['supplier']) { ?><?=renderSimpleDropDownList('supplierType',$var['supplierTypeList'],$var['supplierType'])?><? } ?>
		<?php
			if($var['is_cirrus_fm'] && (bool)$var['supplier'] && !(bool)$var['debtor'] && !(bool)$var['owner'] && !(bool)$var['supplierAgent']){
				renderCheckBox('cirrusfm','Enable on CirrusFM',true, $var['cirrusfm'],'onClick="return ajaxContainer(this.id, \'content\',\'?module=companies&command=company\');"');
			}
			else{
				renderHidden('cirrusfm',"0");
			}
		?>
	</td>
</tr>

<tr class="<?=alternateNextRow()?>">
    <td class="title">Company Name</td>
    <td class="required">*</td>
    <td>
        <?=renderTextBox('companyName',$var['companyName'],'size="30"')?>
        <? if($var['clientCompany'] == 'AU') { ?>
            <? renderButton('btnLookupABNName','Look up Company Name','onclick="return ajaxContainer(this.id,\'content\',\'?command=company&module=companies&action=lookupCompanyName\')"')?>
        <? } ?>
		<? if (isset($var['ABNDetails']) && isset($var['ABNDetails']->name) ) { ?>
			<? renderABNReplaceButton('replaceCompanyName','  ','title="Update Company Name to '.$var['ABNDetails']->name.'" onclick="return replaceABNDetails(\'replaceCompanyName\', \'companyName\', \''.$var['ABNDetails']->name.'\', \'input\')"', true)?>
            <span class="abn-details <? if($var['companyName'] == $var['ABNDetails']->name) { echo 'matched'; }?>">
				<? echo $var['ABNDetails']->name; ?>
			</span>
		<? } ?>
    </td>
</tr>
<? if(isset($var['abnSearchResults'])) { ?>
    <tr class="<?=alternateNextRow()?>"></tr>
        <td class="title">Matching Names</td>
        <td class="required"></td>
        <td>
            <?=renderDropDownList('selectedABN','abn','formattedName',$var['abnSearchResults'],$var['abnSearchSelected'],'onChange="return ajaxContainer(this.id,\'content\',\'?command=company&module=companies&action=selectABN\')"')?>
            <? renderButton('hideABNSearchResukts','Hide Search Results','onclick="return ajaxContainer(this.id,\'content\',\'?command=company&module=companies&action=hideABNResults\')"')?>
        </td>
    </tr>
<? } ?>
    <tr class="<?=alternateNextRow()?>">
        <td class="title">Company Group</td>
        <td class="required"></td>
        <td>
            <?=renderSimpleDropDownList('companyGroup',$var['compGroupList'],$var['companyGroup'])?>
        </td>
    </tr>
<tr class="<?=alternateNextRow()?>">
    <td class="title"><?=$_SESSION['country_default']['business_label']?></td>
    <td class="required"></td>
    <td>
        <? if ($_SESSION['country_default']['business_prefix']) { ?>
        <span class="business-prefix"><?=$_SESSION['country_default']['business_prefix']?></span>
        <? } ?>

        <?=renderTextBox('businessNumber',$var['businessNumber'],'size="30" maxlength="22" onBlur="return ajaxContainer(null, \'content\',\'?command=company&module=companies&action=checkABN\')"')?>
        <? if($var['clientCompany'] == 'AU') { ?>
            <? renderButton('btnLookupABN','Look up ABN','onclick="return ajaxContainer(this.id,\'content\',\'?command=company&module=companies&action=lookupABN\')"')?>
        <? } ?>
        <? if(isset($var['ABNStatus']) && $var['ABNStatus'] == 'Cancelled') { ?>
            <div>
                <span class="crimson">Selected ABN is cancelled</span>
            </div>
        <? } ?>
        <div>
            <span class="crimson"><?=$var['abnMessage']?></span>
        </div>
    </td>
</tr>
<? if(isset($var['ABNResult'])) { ?>
    <tr class="<?=alternateNextRow()?>">
        <td class="title vertical-a-top">
            ABN Details
            <img src="<?=ASSET_DOMAIN?>assets/images/icons/help.png" class="icon" title="Values retrieved from ABN and Company Name Lookup">
        </td>
        <td class="required"></td>
        <td>
            <div id="ABNDetailsHolder">
                <table>
                    <tr>
                        <td class="table-header">Company Name</td>
                        <td><? echo $var['ABNResult']->name ?></td>
                    </tr>
                    <tr>
                        <td class="table-header">ABN</td>
                        <td>
                            <? echo $var['businessNumber'] ?>
                            <? if($var['ABNStatus'] == 'Cancelled') { ?>
                                <span class="crimson">
                                    <? echo '('.$var['ABNStatus'].')' ?>
                                </span>
                            <? } ?>
                        </td>
                    </tr>
                    <tr>
                        <td class="table-header">ACN</td>
                        <td><? echo $var['ABNResult']->ASICNumber ?></td>
                    </tr>
                    <tr>
                        <td class="table-header">Company Type</td>
                        <td><? echo $var['ABNResult']->entityType->entityDescription ?></td>
                    </tr>
                    <tr>
                        <td class="table-header"><?= $_SESSION['country_default']['tax_label'] ?> Status</td>
                        <td><? echo $var['ABNResult']->taxStatus; ?></td>
                    </tr>
                    <? if(isset($var['ABNResult']->mainTradingName)) { ?>
                    <tr name="mainTradingName">
                        <td class="table-header">Main Trading Name</td>
                        <td><? echo $var['ABNResult']->mainTradingName->organisationName ?></td>
                    </tr>
                    <? } ?>
                    <? if(isset($var['ABNResult']->otherTradingName)) { ?>
                    <tr name="otherTradingName">
                        <td colspan=2>Other Trading Names</td>
                    </tr>
                    <tr name="otherTradingNames">
                        <td colspan=2>
                            <ul>
                                <? foreach($var['ABNResult']->otherTradingName as $item) { ?>
                                    <li><? echo $item->organisationName.' ('.$item->effectiveFrom.') '; ?></li>
                                <? } ?>
                            </ul>
                        </td>
                    </tr>
                    <? } ?>
                    <tr>
                        <td class="table-header">State Code</td>
                        <td><? echo $var['ABNResult']->mainBusinessPhysicalAddress->stateCode ?></td>
                    </tr>
                    <tr>
                        <td class="table-header">Post Code</td>
                        <td><? echo $var['ABNResult']->mainBusinessPhysicalAddress->postcode ?></td>
                    </tr>
                </table>
            </div>
        </td>
    </tr>
<? } ?>

    <tr class="subTitle"><td colspan="3">Company Details</td></tr>
	<tr class="<?=alternateNextRow()?>"><td class="title">Country</td><td class="required">*</td><td><?=renderDropDownList('country','countryCode','countryName',$var['countryList'],$var['country'],'onChange="return ajaxContainer(this.id,\'content\',\'?action=changeCountry&command=company&module=companies\')"')?></td></tr>
    <tr class="<?=alternateNextRow()?>"><td class="title">Address</td><td class="required">*</td><td><!--//onfocus="limitTextarea(this,2,40)"//-->
        <?=renderTextArea('address',$var['address'],'rows="2" cols="60"  maxlength="74"')?></td></tr>
    <tr class="<?=alternateNextRow()?>"><td class="title"><?=ucwords(strtolower($_SESSION['country_default']['suburb']))?></td><td class="required">*</td><td class="autocompleteSuburb" style="width:300px;"><?=renderTextBox('city',$var['city'],'autocomplete="off"')?></td></tr>
    <? if (cdf_isShown('display_state', $var['country'])) { ?>
    <tr class="<?=alternateNextRow()?>">
        <td class="title">State</td>
        <td class="required">*</td>
        <td>
            <?=renderDropDownList('state','stateCode','stateName',$var['stateList'],$var['state'],'onChange="return updateSuburbList();"')?>
            <? if (isset($var['ABNDetails']) && isset($var['ABNDetails']->state) ) { ?>
                <? renderABNReplaceButton('replaceState','  ','title="Update State to '.$var['ABNDetails']->state.'" onclick="return replaceABNDetails(\'replaceState\', \'state\', \''.$var['ABNDetails']->state.'\', \'select\')"', true)?>
                <span class="abn-details <? if($var['state'] == $var['ABNDetails']->state) { echo 'matched'; }?>">
                    <? echo $var['ABNDetails']->state; ?>
                </span>
            <? } ?>
        </td>
    </tr>
    <? } ?>
<tr class="<?=alternateNextRow()?>">
    <td class="title">Post Code</td>
    <td class="required">*</td>
    <td>
        <?=renderTextBox('postCode',$var['postCode'],'size="10" maxlength="8"')?>
        <? if (isset($var['ABNDetails']) && isset($var['ABNDetails']->postal) ) { ?>
			<? renderABNReplaceButton('replacePostCode','  ','title="Update Post Code to '.$var['ABNDetails']->postal.'" onclick="return replaceABNDetails(\'replacePostCode\', \'postCode\', \''.$var['ABNDetails']->postal.'\', \'input\')"', true)?>
            <span class="abn-details <? if($var['postCode'] == $var['ABNDetails']->postal) { echo 'matched'; }?>">
				<? echo $var['ABNDetails']->postal; ?>
			</span>
		<? } ?>
    </td>
</tr>

<tr class="<?=alternateNextRow()?>">
	<td class="title">Email Address</td>
	<td class="required">&nbsp;</td>
	<td>
		<?=renderTextBox('email',$var['email'],'size="50"')?>
        <span class="email-centralisation-popup"><?if($var['email_cen_setup']) echo renderPopUpCentralEmailAddressV1($var['emailAddressBookList'],$var['tableName'],$var['primaryCompanyID'])?></span>
	</td>
</tr>

<? if (!$var['debtor'] OR ($var['debtor'] AND ($var['owner'] || $var['supplier']))) { ?>
    <tr class="subTitle"><td colspan="3">Banking Details</td></tr>
    <tr class="<?=alternateNextRow()?>"><td class="title">Preferred Payment Method</td><td class="required"></td><td><? renderRadioGroup('paymentMethod',$var['paymentMethodList'],$var['paymentMethod'],'','onClick="return ajaxContainer(this.id, \'content\',\'?action=paymentType&module=companies&command=company\');"'); ?></td></tr>

    <? if ($var['paymentMethod'] == PAY_EFT) { ?>
        <? if($var['debtor'] == 1): ?>
            <tr class="<?=alternateNextRow()?>"><td class="title">Direct Debit</td><td class="required"></td><td><?=renderRadioGroup ('directDebit', $var['yesNoOption'], $var['directDebit']);?></td></tr>
        <? endif;?>
        <? if (displayBsbFromSession()) { ?>
            <tr class="<?=alternateNextRow()?>"><td class="title"><?=bsbLabelFromSession()?></td><td class="required">*</td><td><?=renderTextBox('bsbNumber',$var['bsbNumber'],'size="7" maxlength="' . bsbLengthFromSession() . '"')?></td></tr>
        <? } ?>
        <tr class="<?=alternateNextRow()?>"><td class="title">Bank Account Number</td><td class="required">*</td><td><?=renderTextBox('bankAccountNumber',$var['bankAccountNumber'],'size="10" maxlength="16"')?></td></tr>
        <tr class="<?=alternateNextRow()?>"><td class="title">Account Name</td><td class="required">*</td><td><?=renderTextBox('bankAccountName',$var['bankAccountName'],'size="30"')?></td></tr>
        <tr class="<?=alternateNextRow()?>"><td class="title">Bank Name</td><td class="required"></td><td><?=renderTextBox('bankName',$var['bankName'],'size="30"')?></td></tr>
    <? } else { ?>
        <tr class="hidden <?=alternateNextRow()?>"><td colspan="3"><?=renderHidden('bsbNumber',$var['bsbNumber'])?><?=renderHidden('bankAccountNumber',$var['bankAccountNumber'])?><?=renderHidden('bankAccountName',$var['bankAccountName'])?><?=renderHidden('bankName',$var['bankName'])?></td></tr>
    <? } ?>
    <? if ($var['paymentMethod'] == PAY_BPAY && $var['country'] == 'AU') { ?>
        <tr class="<?=alternateNextRow()?>"><td class="title">BPAY Biller Code</td><td class="required">*</td><td><?=renderTextBox('bpayBillerCode',$var['bpayBillerCode'],'size="7" maxlength="10"')?></td></tr>
    <? } else { ?>
        <tr class="hidden <?=alternateNextRow()?>"><td colspan="3"><?=renderHidden('bpayBillerCode',$var['bpayBillerCode'])?></td></tr>
    <? } ?>
<? } ?>

<? if (!$var['debtor'] OR ($var['debtor'] AND ($var['owner'] || $var['supplier']))) { ?>
<tr class="subTitle"><td colspan="3">Tax Details</td></tr>
<tr class="<?=alternateNextRow()?>">
    <td class="title">Tax Status</td>
    <td class="required">*</td>
    <td>
        <?=renderDropDownList('taxCode','taxCode','taxDescription',$var['taxRateList'],$var['taxCode'])?>
        <? if (isset($var['ABNDetails']) && isset($var['ABNDetails']->gst) ) { ?>
            <? renderABNReplaceButton('replaceTaxStatus','  ','title="Update Tax Status to '.$var['ABNDetails']->gst.'" onclick="return replaceABNDetails(\'replaceTaxStatus\', \'taxCode\', \''.$var['ABNDetails']->gst.'\', \'select\')"', true)?>
            <span class="abn-details <? if($var['taxCode'] == $var['ABNDetails']->gst) { echo 'matched'; }?>">
                <? echo $var['ABNDetails']->taxStatus; ?>
            </span>
        <? } ?>
    </td>
</tr>
<? } ?>
	<tr>
		<td colspan="10" id="contact">
			<?= $var['UserControl::TempContact'] ?>
		</td>
	</tr>
<tr class="footer"><td colspan="3"><? renderButton('btnSubmit','Submit','onclick="return ajaxContainer(this.id,\'content\',\'?command=company&module=companies&action=saveUpdate\')"')?></td></tr>


</table>

<? } else { ?>
    <center>
        <br>
        <div class="login-container">
            <div class="login-logo"></div>
        </div>
        <div class="slogan">
            <div class="slogan-text">
                <p>Not accessible to Read-Only users, to upgrade your license <NAME_EMAIL></p>
            </div>
        </div>

    </center>
<? } ?>

</div>

<script>
	$('document').ready( function(e){
		var country = $('#country').find(":selected").val();
		var stateVal = $('#state').find(":selected").val();

        if (country == 'AU') {
            var arrSuburb = [];
            $.getJSON("assets/autocompleteSuburb/australianSuburb.json", function(data) {
                $.each(data, function(key, value) {
                    if (value.State == stateVal) {
                        if ($.inArray(value.suburb, arrSuburb) === -1) {
                            arrSuburb.push({suburb: value.suburb,label: `${value.suburb  }, ${  value.State  } ${  value.pcode}`})
                        }
                    }
                })
            });

            $('#city').keypress( function(e){ autocompleteSuburb(document.getElementById("city"), arrSuburb); });
            $('#city').change(function() { setTimeout( function() { updatePostCode(); }, 500); });
        }

        // check company if exist
		$('#companyID').on( 'keydown', function( e ) {
            if( e.which == 9 ) {
                ajaxContainer(null, 'content','?command=company&module=companies&action=checkCompany');
            }
        } );
	});

    function updatePostCode(){
		var selectedText = $("#city").val();
		var cDataArry = selectedText.split(',');
		var postDataArry = cDataArry[1].split(' ');
		$('#city').val(cDataArry[0]);
        if(postDataArry[2].length == 3){
            var postCode = `0${  postDataArry[2]}`;
            $('#postCode').val(postCode);
        }
        else{
            $('#postCode').val(postDataArry[2]);
        }
	};

    function updateSuburbList(){
        var country = $('#country').find(":selected").val();
        var stateVal = $('#state').find(":selected").val();

        if (country == 'AU') {
            var arrSuburb = [];
            $.getJSON("assets/autocompleteSuburb/australianSuburb.json", function(data) {
                $.each(data, function(key, value) {
                    if (value.State == stateVal) {
                        if ($.inArray(value.suburb, arrSuburb) === -1) {
                            arrSuburb.push({suburb: value.suburb,label: `${value.suburb  }, ${  value.State  } ${  value.pcode}`})
                        }
                    }
                })
            });

            $('#city').keypress( function(e){ autocompleteSuburb(document.getElementById("city"), arrSuburb); });
            $('#city').change(function() { setTimeout( function() { updatePostCode(); }, 500); });
        }
    }
</script>


<script>
	var debtorChecked = jQuery("#debtor").attr('checked');
	var ownerChecked = jQuery("#owner").attr('checked');
	var supplierChecked = jQuery("#supplier").attr('checked');
    var typesChecked = 0;

    // By default, show all checkboxes
    if (jQuery('.TEN_GEN').is(':hidden')) jQuery('.TEN_GEN').show();
    if (jQuery('.OWN_GEN').is(':hidden')) jQuery('.OWN_GEN').show();
    if (jQuery('.SUP_GEN').is(':hidden')) jQuery('.SUP_GEN').show();

    // Checking for Debtor company type
    if (debtorChecked && debtorChecked == 'checked') {
        typesChecked++;
    } else {
        jQuery('.TEN_GEN').hide();
        jQuery('.TEN_GEN .ui.form .field').each(function(index) {
            jQuery(this).find('input[type=checkbox]').prop('checked', false);
        });
    }

    // Checking for Owner company type
    if (ownerChecked && ownerChecked == 'checked') {
        typesChecked++;
    } else {
        jQuery('.OWN_GEN').hide();
        jQuery('.OWN_GEN .ui.form .field').each(function(index) {
            jQuery(this).find('input[type=checkbox]').prop('checked', false);
        });
    }

    // Checking for Supplier company type
    if (supplierChecked && supplierChecked == 'checked') {
        typesChecked++;
    } else {
        jQuery('.SUP_GEN').hide();
        jQuery('.SUP_GEN .ui.form .field').each(function(index) {
            jQuery(this).find('input[type=checkbox]').prop('checked', false);
        });
    }

    // Show/Hide Email Centralisation Popup icon based on selected company type
    if (typesChecked > 0) {
        jQuery('.email-centralisation-popup').show();
    } else {
        jQuery('.email-centralisation-popup').hide();
    }
</script>

<link rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/datatables.net-dt/2.1.7/css/dataTables.dataTables.min.css"
      integrity="sha512-JbyOZyqfBvhWNzVXZy2zUX9Hhp8+JGL15feGcRq1JnS1ZIxEdECKSqT+eLuZ8BvvzGHkxrBdu+EuJLdHk+kQ8g=="
      crossorigin="anonymous" referrerpolicy="no-referrer"/>
<script src="https://cdnjs.cloudflare.com/ajax/libs/datatables.net/2.1.7/dataTables.min.js"
        integrity="sha512-uQwfH1NYeXU6Whr3PrKxExRgtnfVkcs30HKaAQtReHSVwQv040Spq22cZo1MaQZshLDTuIncxqWJfIVXyr/GSQ=="
        crossorigin="anonymous" referrerpolicy="no-referrer"></script>

<?=renderMessage ($var['statusMessage'], 'class="infoBox"');?>
<? if ($var['format'] == FILETYPE_SCREEN AND $var['companyDetails'] AND is_array ($var['companyDetails'])) { ?>
<br><br>
<div style="overflow: auto;">
    <table id="myTable" border="0" cellpadding="0" cellspacing="0" class="data-grid row-border hover order-column"
           width="100%">
        <thead>
        <tr class="subTitle">
            <td align="center">Company Code</td>
            <td align="center">Company Name</td>
            <td align="center">Street</td>
            <td align="center"><?=ucwords(strtolower($_SESSION['country_default']['suburb']))?></td>
            <? if ($_SESSION['country_default']['display_state']) { ?>
            <td align="center">State</td>
            <? } ?>
            <td align="center">Post Code</td>
            <? if (displayBsbFromSession()) { ?>
            <td align="center"><?=bsbLabelFromSession()?></td>
            <? } ?>
            <td align="center">Account Number</td>
            <td align="center">Account Name</td>
            <? if (cdf_isAU()) { ?>
            <td align="center">BPAY Biller Code</td>
            <? } ?>
            <td align="center"><?=$_SESSION['country_default']['business_label']?></td>
            <td align="center"><?=$_SESSION['country_default']['tax_label']?> Code</td>
            <td align="center">Owner</td>
            <td align="center">Supplier</td>
            <td align="center">Debtor</td>
            <td align="center">Payment Method</td>
            <td align="center">E-mail Address</td>
            <td align="center">CirrusFM Enabled</td>
        </tr>
        </thead>
        <tbody>
        <? foreach ($var['companyDetails'] as $k => $v) { ?>
        <tr class="<?=alternateNextRow()?>">
            <td><?=$v['companyID']?></td>
            <td><?=$v['companyName']?></td>
            <td><?=$v['address']?></td>
            <td><?=$v['city']?></td>
            <? if ($_SESSION['country_default']['display_state']) { ?>
            <td><?=$v['state']?></td>
            <? } ?>
            <td><?=$v['postCode']?></td>
            <? if (displayBsbFromSession()) { ?>
            <td><?=$v['bsbNumber']?></td>
            <? } ?>
            <td><?=$v['bankAccountNumber']?></td>
            <td><?=$v['bankAccountName']?></td>
            <? if (cdf_isAU()) { ?>
            <td><?=$v['bpayBillerCode']?></td>
            <? } ?>
            <td><?=$v['businessNumber']?></td>
            <td><?=$v['taxCode']?></td>
            <td><?=$v['owner']?></td>
            <td><?=$v['supplier']?></td>
            <td><?=$v['debtor']?></td>
            <td><?=$v['paymentMethod']?></td>
            <td>
                <? if (strpos($v['email'], ';') !== false) {
								$eAddresses = explode(';', $v['email']);
								$emailCount = count($eAddresses ?? []);
								$counter = 1;

								foreach ($eAddresses as $email) {
								?>
                <a href="mailto:<?=preg_replace('/\s+/', '', $email)?>"><?=preg_replace('/\s+/', '', $email)?></a><? if ($counter < ($emailCount)) { ?>
                ;&nbsp;<?} ?>
                <? $counter++;
								}
							} else {
								if ($v['email']) { ?>
                <a href="mailto:<?=preg_replace('/\s+/', '', $v['email'])?>"><?=$v['email']?></a>
                <? }
							} ?>
            </td>
            <td align="center"><img src="assets/images/icons/<?=$v['cirrusfm']?>.png"/></td>
        </tr>
        <? } ?>
        </tbody>
    </table>
</div>
<? } else { ?>
<?=renderDownloadLink ($var['downloadPath']);?>
<? } ?>

<script type="text/javascript">
    $(document).ready(function () {
        if ($('#myTable').DataTable() != null) {
            $('#myTable').DataTable().destroy();
        }
        var table = $('#myTable').DataTable({});

        $('#myTable tbody')
            .on('mouseenter', 'td', function () {
                var colIdx = table.cell(this).index().column;

                $(table.cells().nodes()).removeClass('highlight');
                $(table.column(colIdx).nodes()).addClass('highlight');
            });
    });
</script>

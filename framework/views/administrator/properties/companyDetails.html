

<table class="data-grid" bgcolor="white" cellpadding="3" cellspacing="0" border="0">
	<tr class="subTitle">
		<td colspan="2" style="margin-left:15%;"><?=$var['companyID']?> : <?=$var['companyName']?></td>
	</tr>

	<tr>

		<td valign="top" class="left-side">
			<table class="company-details-left-table">
                <!--
				<tr class="subTitle">
                    <td colspan="2"> Company Details</td>
                </tr>
                -->
				<td valign="top" class="title">Owner Address</td><td><?=$var['companyAddress']?><br/>
					<span><?=$var['companyCity']?>,&nbsp;<?=$var['companyPostCode']?></span>
					<br />
					<span>
						<? if (cdf_isShown('display_state', $var['companyCountry'])) { ?>
							<?=dbGetStateName($var['companyState'], $var['companyCountry'])?>,&nbsp;
						<? } ?>
					</span>
					<span>
						<?=dbGetCountryName($var['companyCountry'])?>
					</span>
				</td>
				</tr>
				<!--//tr class="title"><td>Email Address</td>
					<td><?=$var['companyEmail']?></td>
				</tr//-->
				<? if (strpos($var['companyEmail'], ';') !== false) {
					$compEmail = explode(';', $var['companyEmail']);
					$counter = 0;

					foreach ($compEmail as $email) {
						if ($counter == 0) {
							$counter++;
						?>
							<tr class="title">
								<td>Email Address</td>
								<td><a href="mailto:<?=preg_replace('/\s+/', '', $email)?>"><?=preg_replace('/\s+/', '', $email)?></a></td>
							</tr>
						<? } else { ?>
							<tr class="title">
								<td></td>
								<td><a href="mailto:<?=preg_replace('/\s+/', '', $email)?>"><?=preg_replace('/\s+/', '', $email)?></a></td>
							</tr>
						<? } ?>
					<? }
				} else { ?>
					<tr class="title">
						<td>Email Address</td>
						<td>
							<? if ($var['companyEmail']) { ?>
							<a href="mailto:<?=$var['companyEmail']?>"><?=$var['companyEmail']?></a>
							<? } ?>
						</td>
					</tr>
				<? } ?>
                <!--
				<tr>
					<td class="title">Direct Banking?</td><td><?=(($var['companyDirectBanking']) ? 'yes' : 'no')?></td>
				</tr>
				-->
				<!--//? if ($var['companyDirectBanking']) { ?//-->
				<? if (intval($var['companyPayMethod']) == 1) { ?>
					<? if (displayBsbFromSession()) { ?>
						<tr>
							<td class="title"><?=bsbLabelFromSession()?></td><td><?=formatWithDelimiter($var['companyAccountBSB'])?></td>
						</tr>
					<? } ?>
				<tr>
					<td class="title">Account Number</td><td><?=$var['companyAccountNumber']?></td>
				</tr>
				<tr>
					<td class="title">Account Name</td><td><?=$var['companyAccountName']?></td>
				</tr>
				<tr>
					<td class="title">Bank</td><td><?=$var['companyBankName']?></td>
				</tr>
				<? } ?>

				<tr>
					<td class="title"><?=$_SESSION['country_default']['business_label']?></td><td><? if ($var['companyABN'] && $var['companyABN'] != '') { ?><?=$_SESSION['country_default']['business_prefix']?><? } ?><?=$var['companyABN']?></td>
				</tr>
				<tr>
					<td class="title"><?=$_SESSION['country_default']['tax_label']?> Code</td><td><?=$var['companyGSTCode']?></td>
				</tr>
                <!--
				<tr>
					<td class="title">Is Owner?</td><td><?=(($var['companyIsOwner']) ? 'yes' : 'no')?></td>
				</tr>
				<tr>
					<td class="title">Is Supplier?</td><td><?=(($var['companyIsSupplier']) ? 'yes' : 'no')?></td>
				</tr>
				<tr>
					<td class="title">Is Debtor?</td><td><?=(($var['companyIsDebtor']) ? 'yes' : 'no')?></td>
				</tr>
				-->

			</table>
		</td>


    <?=renderMessage ($var['message'], 'class="infoBox"');?>


		<!--<td class="right-side">-->
			<!--<?if($var['from_name']) {?>-->
			<!--<table class="company-details-left-table">-->
				<!--<tr class="subTitle"><td colspan="2"> Send SMS</td></tr>-->

				<!--<tr>-->
					<!--<td>-->
						<!--&lt;!&ndash;<textarea id="smsMessage" name="smsMessage" rows="3" cols="100">&ndash;&gt;-->
						<!--&lt;!&ndash;<?=$var['final_source']?></textarea>&ndash;&gt;-->
						<!--<textarea name="smsMessage"  rows="4" cols="100" onKeyDown="smsCount(this, 'smsCountChars' ?? []);"-->
								  <!--onKeyUp="smsCount(this, 'smsCountChars' ?? []);"><?=$var['final_source']?></textarea>-->
					<!--</td>-->
				<!--</tr>-->
				<!--<tr>-->
					<!--<td class="title">NOTE:<br/>-->
						<!--<div style="font-weight:normal" id="smsCountChars">Length: 0 of 160</div>-->
					<!--</td>-->
				<!--</tr>-->
				<!--<tr class="highlight">-->
					<!--<td class="left" colspan="3">-->
						<!--<? renderButton('btnSend', 'SendSMS', ' onClick="return ajaxContainer(this.id, \'content\',\'?module=ar&command=smsSending&action=send\')"') ?>-->
					<!--</td>-->
				<!--</tr>-->

			<!--</table>-->
			<!--<?}?>-->
		<!--</td>-->

		<td valign="top" class="top">
			<!--<table>-->
			<!--<thead>-->
			<!--<tr>-->
			<!--<th>Contact Name</th>-->
			<!--<br>-->
			<!--<th>Contact Type</th>-->
			<!--<th></th>-->
			<!--</tr>-->
			<!--</thead>-->
			<!--<tbody>-->

			<? if ($var['contacts']) { ?>

				<?
				$itemsToCheck = array('Mobile Phone', 'Owner Mobile', 'Owner Moble', 'Service Mobile', 'Tenant Mobile');
				?>

				<? foreach ($var['contacts'] as $c) { ?>
				<div class="contact-box">
					<b><?=$c['contactName']?></b><br>
					<?=$var['roleList'][$c['contactRole']]?><br>

					<? foreach ($c['details'] as $d) { ?>
					<b><?=$var['phoneList'][$d['detailCode']]?></b>
						
						<? if ($var['phoneList'][$d['detailCode']] == 'E-Mail') { ?>
							
							<? if (strpos($d['detail'], ';') !== false) { 
								$eAddresses = explode(';', $d['detail']);
								$emailCount = count($eAddresses ?? []);
								$counter = 1;

								foreach ($eAddresses as $email) { 
								?>
									<a href="mailto:<?=preg_replace('/\s+/', '', $email)?>"><?=preg_replace('/\s+/', '', $email)?></a><? if ($counter < ($emailCount)) { ?>;&nbsp;<?} ?>
									<? $counter++;
								}
							} else if (strpos($d['detail'], ',') !== false) { 
								$eAddresses = explode(',', $d['detail']);
								$emailCount = count($eAddresses ?? []);
								$counter = 1;

								foreach ($eAddresses as $email) { ?>
									<a href="mailto:<?=preg_replace('/\s+/', '', $email)?>"><?=preg_replace('/\s+/', '', $email)?></a><? if ($counter < ($emailCount)) { ?>;&nbsp;<?} ?>
									<? $counter++; 
								}
							} else { 
								if ($d['detail']) { ?>
								<a href="mailto:<?=preg_replace('/\s+/', '', $d['detail'])?>"><?=$d['detail']?></a>
								<? } 
							} ?>
						<? } elseif (in_array($var['phoneList'][$d['detailCode']], $itemsToCheck)) { 
								if ($d['detail']) { ?>
								<a href="tel:<?=str_replace(' ', '', preg_replace('/[^0-9]/', '', $d['detail']))?>"><?=$d['detail']?></a>
								<? }
						} else { ?>
							<?=$d['detail']?>
						<? } ?>
						
					<br/>
					<? } ?>
				</div>

				<? } ?>

			<? } ?>

			<!--</tbody>-->
			<!--</table>-->
		</td>
	</tr>
	<tr class="footer">
		<td>&nbsp;</td>
		<td>&nbsp;</td>
	</tr>
</table>

<style>
	.left-side .company-details-left-table {
		width: 100% !important;
	}
	/*.company-details-left-table {*/
	/**/
	/*}*/
	.company-details-left-table tr td {
		margin: 0px;
		padding: 0px;
		font-size: 11px;
		line-height: 25px;
		border-bottom: transparent;
	}

	.company-details-left-table .title {
		width: 200px;
		text-align: left;
		font-weight: bold;
	}
	.company-details-left-table #smsMessage {
		margin: 0px; width: 593px; height: 219px;
	}

	a:focus {
		outline: none;
	}
</style>


<link type="text/css" href="<?=ASSET_DOMAIN?>assets/autocompleteSuburb/autocompleteSuburb.css?<?=CSS_VERSION?>" rel="stylesheet" />
<script type="text/javascript" src="<?=ASSET_DOMAIN?>assets/autocompleteSuburb/autocompleteSuburb.js?<?=JS_VERSION?>"></script>

<style>
    #savePropertyDetailBtn {
        /*display: none; !* Hidden by default *!*/
        position: fixed; /* Fixed/sticky position */
        bottom: 20px; /* Place the button at the bottom of the page */
        right: 30px; /* Place the button 30px from the right */
        z-index: 99; /* Make sure it does not overlap */
        border: none; /* Remove borders */
        outline: none; /* Remove outline */
        background: #3489a1; /* Set a background color */
        color: white; /* Text color */
        cursor: pointer; /* Add a mouse pointer on hover */
        padding: 15px; /* Some padding */
        border-radius: 50%; /* Rounded corners */
        width: 20px;
    }
    #propertyMgmtFeeAccountList_chosen {
        width: 300px !important;
    }
    #image{
        width: 100px;
    }
</style>

<?= renderHidden('trialBalanceClosingBalance', str_replace(',','',str_replace('$','',toMoney($var['trialBalanceClosingBalance'])))) ?>
<?= renderHidden('propertyInactiveOld', $var['propertyInactive']) ?>
<!--<button onclick="topFunction()" id="goTopBtn" title="Go to top"></button>-->
<table class="data-grid" cellpadding="0" cellspacing="0" border="0">
    <tr class="subMenu">
        <td colspan="2">

            <a href="#manageAgreementLink">Management Agreement</a>
            <a href="#bankAccountLink">Bank Account</a>
            <a href="#feesLink">Management Fees</a>
            <!--<a href="#withholdOwnerFundsLink">Withhold Owner Funds </a>-->
            <!--<a href="#reportingLink">Reporting</a>-->
            <a href="#fundPartitionLink">Property Fund</a>
            <!--<a href="#parkingLink">Parking</a>-->
            <? if (isset($var['saved'])) { ?>
            <a href="#ownerLink">Owner Shares</a>
            <a href="#floorsLink">Floors</a>
            <a href="#unitFloorsLink">Unit for Floor</a>
            <a href="#keysLink">Keys</a>
            <a href="#sundryLink">Sundry Charges</a>
            <a href="#propertyNotesLink">Property Notes</a>
            <a href="#diaryLink">Diary</a>
            <a href="#calendarLink">Calendar</a>
            <a href="#contactLink">Contacts</a>
            <a href="#documentLink">Document</a>
            <a href="#propertyInspectionLink">Inspection</a>
            <a href="#propertyInsuranceLink">Insurance</a>
            <!--            <a href="#ownerShareReport">Owner Share Report</a>-->
            <a href="#communicationHistory">Communication History</a>
            <!--<a href="#balanceLink">Take on Blances</a>-->
            <? } ?>
        </td>
    </tr>

</table>

<? if($var['propertyEntryType'] == 1){ ?>
<table class="data-grid" cellpadding="0" cellspacing="0" border="0">
    <tr class="">
        <td rowspan="3" style="width:180px;">
            <div id="image">
                <?=$var['UserControl::Image'] ?>
            </div>
        </td>
    </tr>
    <tr class="altRow">
    </tr>
    <tr class="altRow">
        <td class="title" valign="bottom" ><strong>Bank Balances</strong></td>
        <td align="right" valign="bottom" width="100"  ><strong>Total</strong><br><?=toMoney($var['trialBalanceClosingBalance'])?></td>
        <?php if(isset($var['fund'])) {?>
        <td align="right" valign="bottom" width="100" ><strong>Total <br>Main Property</strong><br><?=toMoney($var['trialBalanceClosingBalanceMain'])?></td>
        <?php } ?>
        <td align="right" valign="bottom" width="100" ><strong>Owners</strong><br><?=toMoney($var['ownersIncomeClosing'])?></td>
        <td align="right" valign="bottom" width="100"><strong>Outgoings</strong><br><?=toMoney($var['voClosing'])?></td>
        <td align="right" valign="bottom" width="100" ><strong>Recoverables</strong><br><?=toMoney($var['directRecoverablesClosing'])?></td>
        <?php if(isset($var['fund'])) {?>
        <?php foreach($var['fund'] as $k => $v) {?>
        <td align="right" valign="bottom" width="100" ><strong><?=$v['ownerName']?></strong><br><?=toMoney($v['closingBalance'])?></td>
        <?php } ?>
        <?php } ?>
        <td></td>
    </tr>
    <tr>
        <td></td>
    </tr>
</table>
<? } ?>
<table class="data-grid" cellpadding="0" cellspacing="0" border="0">
    <tr class="<?= (($var['propertyInactive']) ? 'warningTitle' : 'subTitle') ?>">
        <td>Property Status</td>
        <td colspan="3">
            <? renderRadioGroup('propertyInactive',$var['propStatus'],$var['propertyInactive'],'','')?>
            <div id='propertyInactiveDateDiv' style="display: inline">
            <?=renderSmartDate("propertyInactiveDate", $var['propertyInactiveDate']) ?>
            </div>
    </tr>

    <tr class="altRow">
        <td class="title">Name</td>
        <td class="required">*</td>
        <td><input type="text" id="propertyName" name="propertyName" value="<?= $var['propertyName'] ?>" size="70"
                   recommended="true" validation="^[^`']*$" msg="invalid characters" onBlur="validateField(this)"/>

            <?= renderButton('btnDelete', 'Delete Property', 'onclick="return ajaxConfirmContainer(this.id,\'Are you sure you want to delete the property?\',\'content\',\'?command=home&module=properties&action=deleteProperty&propertyID=' . $var['propertyID'] . '\', \'modalPropertyDelete\')"'); ?>
        </td>
    </tr>
    <tr class="row">
        <td class="title">Address</td>
        <td class="required">*</td>
        <td><input type="text" id="propertyAddress" name="propertyAddress" value="<?= $var['propertyAddress'] ?>"
                   size="70" validation="^[^`']*$" msg="invalid characters" recommended="true"
                   onBlur="validateField(this)"/></td>
    </tr>

    <tr class="altRow">
        <td class="title"><?=ucwords(strtolower($_SESSION['country_default']['suburb']))?></td>
        <td class="required">*</td>
        <td  class="autocompleteSuburb" style="width:300px;">
            <?=renderTextBox('propertyCity',$var['propertyCity'],'autocomplete="off"')?>
        </td>
    </tr>
    <tr class="row">
        <td class="title">Post Code</td>
        <td class="required">*</td>
        <td><input type="text" id="propertyPostCode" name="propertyPostCode" value="<?=$var['propertyPostCode']?>" size="10" /></td>
    </tr>
    <? if (cdf_isShown('display_state', $var['propertyCountry'])) { ?>
        <tr class="altRow">
            <td class="title">State</td><td class="required">*</td>
            <td>
                <?=renderDropDownList('propertyState','stateCode','stateName',$var['stateList'],$var['propertyState'],'onChange="return ajaxContainer(this.id,\'content\',\'?action=changeState&module=properties&command=home\')"')?>
            </td>
        </tr>
    <? } ?>
    <tr class="altRow">
        <td class="title">Country</td>
        <td class="required">*</td>
        <td>
            <?=renderDropDownList('propertyCountry','countryCode','countryName',$var['countryList'],$var['propertyCountry'],'onChange="return ajaxContainer(this.id,\'content\',\'?action=changeCountry&command=home&module=properties\')"')?>
        </td>
    </tr>
    <tr class="altRow">
        <td class="title">Next Charge Date</td>
        <td class="required"></td>
        <td><?= renderSmartDate("propertyChargeDate", $var['propertyChargeDate']) ?> &nbsp; &nbsp; <img src="<?=ASSET_DOMAIN?>assets/images/icons/help.png" class="icon" title="Leave as default"/></td>
    </tr>
    <? if($var['propertyEntryType'] == 2){ ?>
    <tr class="altRow">
        <td class="title">Property Image</td><td class="required">*</td>
        <td >
            <div id="image">
                <?= $var['UserControl::Image'] ?>
            </div>
        </td>
    </tr>
    <? } ?>
    <tr class="subTitle">
        <td colspan="3">Management Details</td>
    </tr>
    <tr class="altRow">
        <td class="title">Property Agent</td>
        <td class="required">&nbsp;</td>
        <td><?=renderKeyedDropDownList("propertyAgent","companyID","companyName",$var['propertyAgentList'],$var['propertyAgent'],'  onBlur="validateField(this)" recommended="true"')?></td>
    </tr>
    <tr class="row">
        <td class="title"><?=ucwords(strtolower($_SESSION['country_default']['property_manager']))?></td>
        <td class="required">*</td>

        <td><?=renderKeyedDropDownList("propertyManager","parameterID","parameterDescription",$var['propertyManagerList'],$var['propertyManager'],'onBlur="validateField(this)" recommended="true"')?></td>
    </tr>
    <tr class="altRow">
        <td class="title">Principal Owner</td>
        <td class="required">*</td>
        <td><?=renderKeyedDropDownList("propertyOwner","companyID","companyName",$var['propertyOwnerList'],$var['propertyOwner'],'onBlur="validateField(this)" recommended="true"')?>
            <a title=""
               onclick="return modalDialog('?module=properties&command=companyDetails&companyID=<?=$var['propertyOwner']?>',{autoOpen: true, modal:true, title: '<?=$var['propertyOwner']?>', height:'800', width:1000});">Company
                Information</a></td>
    </tr>
    <tr class="row">
        <td class="title">Property Type</td>
        <td class="required">&nbsp;</td>
        <td><?=renderDropDownList("propertyType","parameterID","parameterDescription",$var['propertyTypeList'],$var['propertyType'],'onBlur="validateField(this)" recommended="true"')?></td>
    </tr>
    <tr class="altRow">
        <td class="title">Remittance Office</td>
        <td>&nbsp;</td>
        <td><?=renderKeyedDropDownList("propertyRemittanceOffice","officeID","officeName",$var['propertyRemittanceOfficeList'],$var['propertyRemittanceOffice'],'onBlur="validateField(this)" recommended="true"')?></td>
    </tr>
    <tr class="altRow">
        <td class="title">Property Group</td>
        <td>&nbsp;</td>
        <td><?=renderSimpleDropDownList("propertyGroupType",$var['propertyGroupTypeList'],$var['propertyGroupType'],'recommended="true"')?></td>
    </tr>
    <tr class="altRow">
        <td class="title">Payment Run Group</td>
        <td>&nbsp;</td>
        <td><?=renderSimpleDropDownList("paymentGroup",$var['propertyPayGroupList'],$var['paymentGroup'],'recommended="true"')?></td>
    </tr>
    <tr class="altRow">
        <td class="title">Accounting Basis</td>
        <td>&nbsp;</td>
        <td>
            <? if(!$var['propertyAccountingBasis']) {$var['propertyAccountingBasis']= 'C';} ?>
            <? renderRadioGroup('propertyAccountingBasis',$var['accountingBasisList'],$var['propertyAccountingBasis'],'','')?>
        </td>
    </tr>
    <tr class="altRow">
        <td class="title">Lease Type</td>
        <td>&nbsp;</td>
        <td>
            <?=renderDropDownList ("propertyLeaseType","parameterID","parameterDescription",$var['propertyLeaseTypeList'],$var['propertyLeaseType'],'')?>
        </td>
    </tr>
    <tr class="row">
        <td colspan="3">
            <a name="manageAgreementLink"></a>
            <div id="managementAgreement">
                <?=$var['UserControl::ManagementAgreement']?>
            </div>
        </td>
    </tr>
</table>


<a name="bankAccountLink"></a>
<table class="data-grid" cellpadding="0" cellspacing="0" border="0">
    <tr class="subTitle">
        <td colspan="4">Bank Account</td>
    </tr>
    <tr class="row">
        <td class="title">Bank Account</td>
        <? if ($var['propertyBankAccount'] != '') { ?>
        <td class="required"></td>
        <td><?= $var['propertyBankAccount'] ?> - <?= $var['propertyBankAccountName'] ?><br><font size="1"><em>To
            change the associated <?=ucwords(strtolower($_SESSION['country_default']['trust_account']))?> for this property please contact support -
            <EMAIL></em></font></td>
        <?= renderHidden('propertyBankAccount', $var['propertyBankAccount']) ?>
        <? } else { ?>
        <td class="required">*</td>
        <td colspan="2"><?= renderKeyedDropDownList('propertyBankAccount', 'bankID', 'bankAccountName', $var['propertyBankAccountList'], $var['propertyBankAccount'], 'onBlur="validateField(this)" recommended="true"') ?></td>
        <!-- <td><?= renderKeyedDropDownList("propertyBankAccount", "bankAccountID", "bankAccountName", $var['propertyBankAccountList'], $var['propertyBankAccount'], 'onBlur="validateField(this)" required="true"') ?></td>
                            changed above line variable bankAccountID changed to bankID  -->
        <? } ?>
    </tr>
    <tr class="altRow">
        <td class="title">&nbsp;</td>
        <td class="required"></td>
        <td colspan="2"><?= renderCheckBox('propertyUseEFTOnInvoice', "Use Principal Owner's EFT details on Tenant Invoices (Owner is paid direct by tenants)", '1', $var['propertyUseEFTOnInvoice'], 'onBlur="validateField(this)"') ?></td>
    </tr>
    <tr class="altRow">
        <td class="title">&nbsp;</td>
        <td class="required"></td>
        <td colspan="2"><?= renderCheckBox('propertyAutoPayOwner', "Allow Owner Payments (Default setting for Payment Runs)", '1', $var['propertyAutoPayOwner'], 'onBlur="validateField(this)"') ?></td>
    </tr>
    <!--tr class="altRow">
                <td class="title">Rent Only Payment
                <td><? //=renderCheckBox ('propertyRentOnlyYesNo', '', '1', $var['propertyRentOnlyYesNo'], 'onClick="return ajaxContainer(this.id, \'content\',\'?command=home&module=properties&action=changeRentOnly\')"')?>
                <? //if ($var['propertyRentOnlyYesNo'])
    //echo renderRadioGroup ('propertyRentOnly', $var['rentOnlyOption'], $var['propertyRentOnly']);?>
                </td>

            </tr-->
    <?php if ((isset($var['allowBankPartitionFlag'])) && ($var['allowBankPartitionFlag'])) : ?>
    <tr class="altRow">
        <td class="title"></td>
        <td class="required"></td>
        <td colspan="2">
            <?= renderCheckBox('bankPartitioning', 'Apply partitioning to the property', '1', $var['bankPartitioning'], '') ?>
        </td>
    </tr>
    <?php endif; ?>
    <tr class="altRow">
        <td class="title">Default Amount to withhold from Owner</td>
        <td class="required"></td>
        <td colspan="2"><?=renderTextBox ('withholdOwnerAmount', $var['withholdOwnerAmount'], 'class="right" onDblClick="backToZero(this)"
			    title="Insert an amount in this field representing the funds that you would like retained in the property after owner payments."')?></td>
    </tr>
    <tr class="altRow">
        <td class="title">Default Note for withheld amount</td>
        <td class="required"></td>
        <td colspan="2"><?=renderTextBox ('withholdOwnerComment', $var['withholdOwnerComment'], 'size="50"
			    title="Input the reason why withholding the amount from the owner payment. Any notes provided here will be displayed on the payment form."')?></td>
    </tr>

    </table>
    <table class="data-grid" cellpadding="0" cellspacing="0" border="0">

    <tr class="subTitle">
        <td colspan="6">Management Fees &rarr;
            <a name="feesLink"></a>
            <input type="button" value="new" onClick="return ajaxContainer(this.id, 'content','?command=home&module=properties&action=newManagementFees', {propertyID:$('#propertyID').val()})" />
        </td>
    </tr>

    <tr class="info">
        <td class="title">Note:</td>
        <td colspan="6"><b>Select an existing management fee from the list below or create a new management fee by clicking on 'new'</b>
        </td>
    </tr>

    <tr class="fieldDescription">
        <td>Account</td>
        <td width="15%">From</td>
        <td width="15%">To</td>
        <td width="30%">Method</td>
        <td width="100">Amount/Percentage</td>
        <td></td>
    </tr>
     <?   foreach ($var['managementFeeList'] as $key => $fee) {    ?>
    <tr  onclick="return ajaxContainer(this.id, 'content','?command=home&module=properties&action=selectManagementFeePeriod&managementFeesID=<?=$fee['prmf_id'];?>',
    {managementFeesID: <?= $fee['prmf_id'] ;?> ,propertyID:$('#propertyID').val()})" class="<?= $fee['prmf_id'] == $var['managementFeesID'] ? 'selectedFees' : '' ?>" style="cursor:pointer;" onMouseOver="this.style.backgroundColor='#F5D76E'" onMouseOut="this.style.backgroundColor='#FFFFFF'" >
        <td><?= $fee['prmf_account'];?></td>
        <td><?=  ($fee['prmf_start_period']) ;?></td>
        <td><?=  ($fee['prmf_end_period']) ;?></td>
        <td width="300" ><?= $var['propertyMgmtFeeMethodList'][$fee['prmf_method']] ;?></td>
        <td align="right"><?=  ($fee['prmf_method'] == 2 ? toMoney($fee['prmf_amount']) : "") . ( in_array($fee['prmf_method'],[3,5]) ? toMoney($fee['prmf_percentage'],"") . "%" : "") . ( $fee['prmf_method'] == 4  ? toMoney($fee['pmrcf_pct'],"") . "%" : "") ;?></td>
        <td align="right">
            <input type="image" src="<?=ASSET_DOMAIN?>assets/images/icons/delete.png"
              onClick="event.stopPropagation();return ajaxConfirmContainer(null, 'Are you sure you want to delete this fee?', 'content','?action=deleteFees&module=properties&command=home&propertyID=<?=$var['propertyID']?>&managementFeesID=<?=$fee['prmf_id']?>&feesStartDate=<?=$fee['prmf_start_period']?>');"/>
        </td>
    </tr>

    <? if($fee['prmf_id'] == $var['managementFeesID'] || $fee['prmf_id'] == $var['FeesID'] ){ ?>
    <tr >
        <td colspan="6" style="border:7px solid #c9ebfc">
            <div id="managementFeesPeriod" ><?=$var['UserControl::managementFeesPeriod']?></div>
        </td>
    </tr>
    <tr style="background-color: #c9ebfc;text-align: right;">
        <td colspan="6">
            <input type="button" value="Save Management Fee Detail" onClick="return ajaxContainer(this.id, 'content','?command=home&module=properties&action=saveManagementFees&managementFeesID=<?= $fee['prmf_id'];?>', {propertyID:$('#propertyID').val()})" />
        </td>
    </tr>
    <? } ?>
    <? } ?>

    <?  if( $var['showFees']  ){ ?>
    <tr >
        <td colspan="6" style="border:7px solid #c9ebfc">
            <div id="managementFeesPeriod" ><?=$var['UserControl::managementFeesPeriod']?></div>
        </td>
    </tr>
    <tr style="background-color: #c9ebfc;text-align: right;">
        <td colspan="6">
            <input type="button" value="Save Management Fee Detail" onClick="return ajaxContainer(this.id, 'content','?command=home&module=properties&action=saveManagementFees&managementFeesID=0', {propertyID:$('#propertyID').val()})" />
        </td>
    </tr>
    <? } ?>
</table>


<table class="data-grid" cellpadding="0" cellspacing="0" border="0">
    <tr class="subTitle">
        <td colspan="3">Reporting</td>
    </tr>
    <tr class="row">
        <td class="title"><?=$_SESSION['country_default']['tax_label']?> Status</td>
        <td><?=$var['ownerGSTstatus']?></td>
    </tr>
    <tr class="altRow">
        <td class="title">Owner Report Type</td>
        <td><?=renderSimpleDropDownList("propertyReportType",$var['propertyReportTypeList'],$var['propertyReportType'],'recommended="true"')?></td>
    </tr>
    <tr class="row">
        <td class="title"><?=$_SESSION['country_default']['tax_label']?> Basis</td>
        <td><?=renderRadioGroup('propertyGSTBasis', $var['propertyReportGSTBasisList'], $var['propertyGSTBasis'] )?></td>
    </tr>
    <tr class="altRow">
        <td class="title"><?=$_SESSION['country_default']['tax_label']?> Report Period</td>
        <td><?=renderRadioGroup('propertyGSTReportPeriod', $var['propertyReportGSTPeriodList'], $var['propertyGSTReportPeriod'] )?></td>
    </tr>
    <tr class="row">
        <td class="title">&nbsp;</td>
        <td><?=renderCheckBox("propertyRetail","Is this a Retail property?","1",$var['propertyRetail'],'' )?>
            &nbsp;&nbsp;&nbsp;<em><strong>Relates to reporting</strong></em>
        </td>
    </tr>

    <tr class="row">
        <td class="title">&nbsp;</td>
        <td><?= renderCheckBox("propertyStrata", "Is this a " . ucwords($_SESSION['country_default']['strata']) . " property?", "1", $var['propertyStrata'], ' onClick="return ajaxContainer(this.id, \'content\',\'?command=home&module=properties&action=isRetailStrata\')"') ?>
            &nbsp;&nbsp;&nbsp;<em><strong>Relates to reporting</strong></em>
        </td>
    </tr>

    <tr class="row">
        <td class="title">&nbsp;</td>
        <td><?= renderCheckBox("propertyAttachToOwnerReport", "Default Setting: Attach Supplier Invoices to Owner Report", "1", $var['propertyAttachToOwnerReport'], '') ?>
<!--            &nbsp;&nbsp;&nbsp;<em><strong>Relates to reporting</strong></em>-->
        </td>
    </tr>

</table>
<br/>
<div id="propertyRecoverableSplit">
    <?= $var['UserControl::PropertyRecoverableSplit'] ?>
</div>
<br/>
<a name="fundPartitionLink"></a>
<div id="fundPartition">
    <?=$var['UserControl::FundPartition']?>
</div>

<? if($var['propertyEntryType'] == 2){ ?>
<table class="data-grid" cellpadding="0" cellspacing="0" border="0">
    <tr class="subTitle">
        <td colspan="3">Calendar used</td>
    </tr>
    <tr class="row">
        <td class="title">Property Calendar:</td><td class="required">*</td>
        <td><?=renderSimpleDropDownList("propertyEnd",$var['YearEnd'],$var['propertyEnd'],'recommended="true"')?></td>
    </tr>

</table>
<? } ?>
<a name="bondPropertyLink"></a>
<div id="bondProperty">
    <?=$var['UserControl::BondProperty']?>
</div>

<a name="parkingLink"></a>
<div id="parking">
    <table class="data-grid" cellpadding="0" cellspacing="0" border="0">
        <tr class="subTitle">
            <td colspan="3">Parking Bays</td>
        </tr>
        <tr class="altRow">
            <td class="title">Leasable Parking Bays</td>
            <td class="required"></td>
            <td><input type="text" name="propertyParkingLeased" id="propertyParkingLeased"
                       value="<?=$var['propertyParkingLeased']?>"/></td>
        </tr>
        <tr class="row">
            <td class="title">Licensed Parking Bays</td>
            <td class="required"></td>
            <td><input type="text" name="propertyParkingLicensed" id="propertyParkingLicensed"
                       value="<?=$var['propertyParkingLicensed']?>"/></td>
        </tr>
        <tr class="altRow">
            <td class="title">Casual Parking Bays</td>
            <td class="required"></td>
            <td><input type="text" name="propertyParkingCasual" id="propertyParkingCasual"
                       value="<?=$var['propertyParkingCasual']?>"/></td>
        </tr>
    </table>
</div>

<!-- <a name="inspectionLink"></a>
<div id="inspection">
    <table class="data-grid" cellpadding="0" cellspacing="0" border="0">
        <tr class="subTitle">
            <td colspan="3">Property Inspection</td>
        </tr>
        <tr class="row">
            <td class="title">Inspection</td>
            <td><?=renderRadioGroup('propertyInspection', $var['yesNoOptionBoolean'], $var['propertyInspection'] )?></td>
        </tr>
        <tr class="altrow inspectionClass <?=$var['propertyInspection'] ? '' : 'hidden' ?>">
            <td class="title">Inspection Amount</td>
            <td>
                $ <input type="text" name="propertyInspectionAmount" id="propertyInspectionAmount" onBlur="validateField(this)" validation="^[0-9.]*$" msg="please use numbers only" required="true" value="<?=$var['propertyInspectionAmount']?>" size="20" />
            </td>
        </tr>
        <tr class="altRow inspectionClass <?=$var['propertyInspection'] ? '' : 'hidden' ?>">
            <td class="title">Payment Account</td>
            <td>
                <?php
						$params['keyed'] = true;
						$params['keyField'] = 'accountID';
						$params['valueField'] = 'accountName';
						$params['groupField'] = 'accountGroupDesc';
						$params['groupDescriptions'] = $var['accountGroupList2'];
						$params['textAttributes'] = 'required = "true"';
						renderGroupedSmartSearchByCodeByName ('propertyInspectionAccount', $var['propertyInspectionAccountList'], $var['propertyInspectionAccount'], $params);
					?>
                <?//=renderKeyedDropDownList("propertyInspectionAccount",'accountID','accountName',$var['propertyInspectionAccountList'],$var['propertyInspectionAccount'],'recommended="true"')?>
            </td>
        </tr>
        <tr class="row inspectionClass <?=$var['propertyInspection'] ? '' : 'hidden' ?>">
            <td class="title">Frequency</td>
            <td><?=renderRadioGroup('propertyInspectionFrequency', $var['propertyInspectionFrequencyList'], $var['propertyInspectionFrequency'] )?></td>
        </tr>
        <tr class="row inspectionClass <?=$var['propertyInspection'] ? '' : 'hidden' ?>">
            <td class="title">Recoverable</td>
            <td><?=renderRadioGroup('propertyInspectionRecoverable', $var['yesNoOptionBoolean'], $var['propertyInspectionRecoverable'] )?></td>
        </tr>
        <tr class="altRow inspectionClass inspectionRecClass <?=$var['propertyInspection'] ? '' : 'hidden' ?>  <?=$var['propertyInspectionRecoverable'] ? '' : 'hidden' ?>">
            <td class="title">Recoverable Account</td>
            <td>							<?php
						$params['keyed'] = true;
						$params['keyField'] = 'accountID';
						$params['valueField'] = 'accountName';
						$params['groupField'] = 'accountGroupDesc';
						$params['groupDescriptions'] = $var['accountGroupList2'];
						$params['textAttributes'] = 'required = "true"';
						renderGroupedSmartSearchByCodeByName ('propertyInspectionRecoverableAccount', $var['propertyInspectionAccountListInc'], $var['propertyInspectionRecoverableAccount'], $params);
						?></td>
        </tr>
    </table>
</div> -->

<a name="propertyFees"></a>
<div id="propertyFees">
    <table class="data-grid" cellpadding="0" cellspacing="0" border="0">
        <tr class="subTitle"><td colspan="3">Fees Setup</td></tr>
        <!-- PROPERTY INSPECTION -->
        <tr class="subHeader"><td colspan="10">Property Inspection Fee</td></tr>
            <tr class="row">
                <td class="title">Inspection</td>
                <td><?=renderRadioGroup('propertyInspection', $var['yesNoOptionBoolean'], $var['propertyInspection'] )?></td>
            </tr>
            <tr class="altrow inspectionClass <?=$var['propertyInspection'] ? '' : 'hidden' ?>">
                <td class="title">Inspection Amount</td>
                <td>
                    <?=$_SESSION['country_default']['currency_symbol']?> <input type="text" name="propertyInspectionAmount" id="propertyInspectionAmount" onBlur="validateField(this)" validation="^[0-9.]*$" msg="please use numbers only" required="true" value="<?=$var['propertyInspectionAmount']?>" size="20" />
                </td>
            </tr>
            <tr class="altRow inspectionClass <?=$var['propertyInspection'] ? '' : 'hidden' ?>">
                <td class="title">Payment Account</td>
                <td>
                    <?php
                            $params['keyed'] = true;
                            $params['keyField'] = 'accountID';
                            $params['valueField'] = 'accountName';
                            $params['groupField'] = 'accountGroupDesc';
                            $params['groupDescriptions'] = $var['accountGroupList2'];
                            $params['textAttributes'] = 'required = "true"';
                            renderGroupedSmartSearchByCodeByName ('propertyInspectionAccount', $var['propertyInspectionAccountList'], $var['propertyInspectionAccount'], $params);
                        ?>
                    <?//=renderKeyedDropDownList("propertyInspectionAccount",'accountID','accountName',$var['propertyInspectionAccountList'],$var['propertyInspectionAccount'],'recommended="true"')?>
                </td>
            </tr>
            <!-- <tr class="row inspectionClass <?=$var['propertyInspection'] ? '' : 'hidden' ?>">
                <td class="title">Recoverable</td>
                <td><?=renderRadioGroup('propertyInspectionRecoverable', $var['yesNoOptionBoolean'], $var['propertyInspectionRecoverable'] )?></td>
            </tr>
            <tr class="altRow inspectionClass inspectionRecClass <?=$var['propertyInspection'] ? '' : 'hidden' ?>  <?=$var['propertyInspectionRecoverable'] ? '' : 'hidden' ?>">
                <td class="title">Recoverable Account</td>
                <td>                            <?php
                            $params['keyed'] = true;
                            $params['keyField'] = 'accountID';
                            $params['valueField'] = 'accountName';
                            $params['groupField'] = 'accountGroupDesc';
                            $params['groupDescriptions'] = $var['accountGroupList2'];
                            $params['textAttributes'] = 'required = "true"';
                            renderGroupedSmartSearchByCodeByName ('propertyInspectionRecoverableAccount', $var['propertyInspectionAccountListInc'], $var['propertyInspectionRecoverableAccount'], $params);
                            ?></td>
            </tr> -->
            <tr class="row inspectionClass <?=$var['propertyInspection'] ? '' : 'hidden' ?>">
                <td class="title">Frequency</td>
                <td><?=renderRadioGroup('propertyInspectionFrequency', $var['propertyInspectionFrequencyList'], $var['propertyInspectionFrequency'] )?></td>
            </tr>
        <!-- Rent Review INSPECTION -->
        <tr class="subHeader"><td colspan="10">Rent Review Fee</td></tr>
        <tr class="row">
            <td class="title">Rent Review Fee</td>
            <td><?=renderRadioGroup('rentReviewFee', $var['yesNoOptionBoolean'], $var['rentReviewFee'] )?></td>
        </tr>
        <tr class="altrow rentReviewFeeClass <?=$var['rentReviewFee'] ? '' : 'hidden' ?>">
            <td class="title">Amount</td>
            <td>
                <?=$_SESSION['country_default']['currency_symbol']?> <input type="text" name="rentReviewFeeAmount" id="rentReviewFeeAmount" onBlur="validateField(this)" validation="^[0-9.]*$" msg="please use numbers only" required="true" value="<?=$var['rentReviewFeeAmount']?>" size="20" />
            </td>
        </tr>
        <tr class="altrow rentReviewFeeClass <?=$var['rentReviewFee'] ? '' : 'hidden' ?>">
            <td class="title">Description</td>
            <td>
                 <?=renderTextBox('rentReviewFeeDescription',$var['rentReviewFeeDescription'],'autocomplete="off"')?>
            </td>
        </tr>
        <tr class="altRow rentReviewFeeClass <?=$var['rentReviewFee'] ? '' : 'hidden' ?>">
            <td class="title">Expense Account</td>
            <td>
                <?php
                        $params['keyed'] = true;
                        $params['keyField'] = 'accountID';
                        $params['valueField'] = 'accountName';
                        $params['groupField'] = 'accountGroupDesc';
                        $params['groupDescriptions'] = $var['accountGroupList2'];
                        $params['textAttributes'] = 'required = "true"';
                        renderGroupedSmartSearchByCodeByName ('rentReviewFeeExpenseAccount', $var['RentReviewFeeAccountListExp'], $var['rentReviewFeeExpenseAccount'], $params);
                ?>
            </td>
        </tr>
    </table>
</div>




<img class="ngMainCom hide-when-loading"
     src="<?=ASSET_DOMAIN?>assets/images/icons/accept.png" id="savePropertyDetailBtn" onclick="return ajaxContainer(this.id,'content','?command=home&module=properties&action=submit')"
     data-tooltip="Save Property Details"/>
<table class="data-grid hide-when-loading" cellpadding="0" cellspacing="0" border="0">
    <tr class="footer">
        <td colspan="2"><? renderButton('btnSave', 'Save Property Details', 'onclick="return ajaxContainer(this.id, \'content\',\'?command=home&module=properties&action=submit\')"') ?></td>
    </tr>
</table>

<!--</fieldset>-->
<? if (isset($var['saved'])) { ?>
<br/><br/>
<a id="ref1" name="ownerLink"></a>
<div id="ownerShares">
    <?= $var['UserControl::OwnerShares'] ?>
</div>
<br/><br/>
<a name="withholdOwnerFundsLink"></a>
<div id="withholdOwnerFunds">
    <?= $var['UserControl::withholdOwnerFunds'] ?>
</div>
<br/><br/>
<a name="floorsLink"></a>
<div id="floors">
    <?= $var['UserControl::Floors'] ?>
</div>
<br/><br/>
<a name="unitFloorsLink"></a>
<div id="units">
    <?= $var['UserControl::Units'] ?>
</div>
<br/><br/>
<a name="keysLink"></a>
<div id="keys">
    <?= $var['UserControl::Keys'] ?>
</div>
<br/><br/>
<a name="sundryLink"></a>
<div id="sundryCharges">
    <?= $var['UserControl::Sundry'] ?>
</div>
<br/><br/>
<a name="propertyNotesLink"></a>
<div id="propertyNotes">
    <?= $var['UserControl::Notes'] ?>
</div>
<br/><br/>
<a name="diaryLink"></a>
<div id="diary">
    <?= $var['UserControl::Diary'] ?>
</div>
<br/><br/>
<a name="calendarLink"></a>
<div id="calendars">
    <?= $var['UserControl::Calendar'] ?>
</div>
<div id="retailcalendars">
    <?= $var['UserControl::RetailCalendar'] ?>
    <!-- BETA -->
</div>

<br/><br/>
<a name="contactLink"></a>
<div id="contact">
    <?= $var['UserControl::Contact'] ?>
</div>
<br/><br/>
<a name="documentLink"></a>
<div id="document">
    <?= $var['UserControl::Document'] ?>
</div>
<br/><br/>
<a name="propertyInspectionLink"></a>
<div id="propertyInspectionSection">
    <?= $var['UserControl::PropertyInspection'] ?>
</div>
<br/><br/>
<a name="propertyInsuranceLink"></a>
<div id="propertyInsurance">
    <?= $var['UserControl::PropertyInsurance'] ?>
</div>



<!--<br/><br/>-->
<!--<a name="ownerShareReportLink"></a>-->
<!--<div id="ownerShareReport">-->
<!--    <?= $var['UserControl::OwnerShareReport'] ?>-->
<!--</div>-->

<br/><br/>
<a name="communicationHistory"></a>
<table class="data-grid" cellspacing="0" cellpadding="0">
    <tr class="subTitle">
        <td colspan="4">Communication History
            <?renderRadioGroup ('commHistory', $var['communicationHistory'], $var['commHistory'], '', 'onClick=\'$(".comContent").css("display","none");$("." +  $(this).val() +"Content").css("display","block");\'')?>
        </td>

    </tr>
</table>

<? if ($var['propertyEntryType'] == 1) { ?>
<div class="EContent comContent" >
<div id="letterHistory"></div>
</div>
<?=renderHidden('letterHistoryPageURL',$var['letterHistoryPage'])?>
<script>
    $(function () {
        $("#letterHistory").load("<?=$var['letterHistoryPage']?>");
    });
</script>
<div class="comContent SContent" style="display: none">
<table id="myTable" class="data-grid row-border hover order-column " cellspacing="0" width="100%" border="1" style="border-collapse: collapse">
    <thead>
    <tr class="subTitle">
        <th>Property Code</th>
        <th>Lease Code</th>
        <th>Date Sent</th>
        <th>SMS Message</th>
        <th>Recipients</th>
    </tr>
    </thead>
    <tbody>
    <? if ($var['smsMessage']) { ?>
    <?
        foreach ($var['smsMessage'] as $key => $sms) {

    ?>
    <tr id="item_<?=$key?>" class="<?=alternateNextRow()?>">
        <td align="center"  class="data"><?=$sms['property_code']?></td>
        <td align="center"  class="data"><?=$sms['lease_code']?></td>
        <td align="center"  class="data"><?=$sms['date_sent']?></td>
        <td align="left"  class="data" width="350px"><?=$sms['sms_message']?></td>
        <td align="left"  class="data" width="350px">
            <a href="tel:<?=str_replace(' ', '', preg_replace('/[^0-9]/', '', $sms['recepient_no']))?>"><?=$sms['recepient_no']?></a>
        </td>
    </tr>
    <? } ?>
    <? } ?>
    </tbody>
</table>
</div>
<? } ?>


<br/><br/>
<a name="balanceLink"></a>
<div id="openingBalances">
    <?= $var['UserControl::OpeningBalances'] ?>
</div>
<? } ?>

<script type="text/javascript" language="javascript">
    $(document).ready(function() {
        // ONLY FOR AU
		var country = $('#propertyCountry').find(":selected").val();
        var stateVal = $('#propertyState').find(":selected").val();

        if (country == 'AU') {
            var arrSuburb = [];
            $.getJSON("assets/autocompleteSuburb/australianSuburb.json", function(data) {
                $.each(data, function(key, value) {
                    if(value.State == stateVal){
                        if ($.inArray(value.suburb, arrSuburb) === -1) {
                            arrSuburb.push({suburb: value.suburb,label: `${value.suburb  }, ${  value.State  } ${  value.pcode}`})
                        }
                    }
                })
            });

            $('#propertyCity').keypress( function(e){ autocompleteSuburb(document.getElementById("propertyCity"), arrSuburb); });
            $('#propertyCity').change(function() { setTimeout( function() { updatePropertyPostCode(); }, 500); });
        }

        showHideInactiveDate();
    });

    function updatePropertyPostCode(){
        var selectedText = $("#propertyCity").val();
        var cDataArry = selectedText.split(',');
        var postDataArry = cDataArry[1].split(' ');
        $('#propertyCity').val(cDataArry[0]);

        if(postDataArry[2].length == 3){
            var postCode = `0${  postDataArry[2]}`;
            $('#propertyPostCode').val(postCode);
        }
        else{
            $('#propertyPostCode').val(postDataArry[2]);
        }
    };





    if (document.getElementById("propertyInactive").value == 1) disableForm('postback', true, 'propertyInactive,propertyIDList,propertyID,btnActivate,propertyIDButton');
    $("#propertyInspection").click(function () {
        var inspection = document.getElementsByName('propertyInspection');
        for (var i = 0, length = inspection.length; i < length; i++)
        {
            if (inspection[i].checked)
            {
                $(".inspectionClass").removeClass('hidden');
                break;
            } else {
                $(".inspectionClass").addClass('hidden');;
                break;
            }
        }
    });
    $("#rentReviewFee").click(function () {
        var rentReviewFee = document.getElementsByName('rentReviewFee');
        for (var i = 0, length = rentReviewFee.length; i < length; i++)
        {
            if (rentReviewFee[i].checked)
            {
                $(".rentReviewFeeClass").removeClass('hidden');
                break;
            } else {
                $(".rentReviewFeeClass").addClass('hidden');;
                break;
            }
        }
    });
    $("#propertyInspectionRecoverable").click(function () {
        var inspection = document.getElementsByName('propertyInspectionRecoverable');
        for (var i = 0, length = inspection.length; i < length; i++)
        {
            if (inspection[i].checked)
            {
                $(".inspectionRecClass").removeClass('hidden');
                break;
            } else {
                $(".inspectionRecClass").addClass('hidden');
                break;
            }
        }
    });

    $('input[type=radio][name=propertyInactive]').change(function() {
        const property_code = $("#propertyID").val();
        const propertyInactiveDate = $("#propertyInactiveDate").val();
        let trial_balance_closing_balance = $("#trialBalanceClosingBalance").val();
        const propertyInactiveOld = $("#propertyInactiveOld").val();

        if (this.value == '0' && this.value != propertyInactiveOld) {
            ajaxContainer(null, 'content',`?action=activate&module=properties&command=home&propertyID=${  property_code  }&propertyInactiveDate=${  propertyInactiveDate}` );
        }
        else if (this.value == '1' && this.value != propertyInactiveOld) {

            try {
                trial_balance_closing_balance = parseFloat(trial_balance_closing_balance);
                if(trial_balance_closing_balance>0){
                    var message = `The property currently has a balance of $${trial_balance_closing_balance}\n` +
                    `\tAre you sure you want to make it inactive?`;
                    var dialogID = '#dialog-confirm';
                    var tag = $(dialogID);
                    if (tag.length == 0) tag = $(`<div id="${  dialogID  }" title=""><div class="ui-icon ui-icon-alert" style="float: left; margin: 0 7px 20px 0;"></div><div style="margin-left: 23px;">${  message  }</div></div>`).appendTo("body");

                    tag.dialog({
                        resizable: false,
                        modal: true,
                        buttons: {
                            'Ok': function() {
                                ajaxContainer(null, 'content',`?action=activate&module=properties&command=home&propertyID=${  property_code  }&propertyInactiveDate=${  propertyInactiveDate}` );
                                $(this).dialog('close');
                            },
                            Cancel: function() {
                                $("#propertyInactive_0").click();
                                $(this).dialog('close');
                            }
                        }
                    });

                }
                else{
                    ajaxContainer(null, 'content',`?action=activate&module=properties&command=home&propertyID=${  property_code  }&propertyInactiveDate=${  propertyInactiveDate}` );
                }

            } catch (e) {
            }

        }
    });

    $("#propertyInactive").click(function () {
        showHideInactiveDate();
    });

    function showHideInactiveDate()
    {
        if ($("input[name='propertyInactive']:checked").val() == 1) {
            $('#propertyInactiveDateDiv').show();
        }
        else
            $('#propertyInactiveDateDiv').hide();
    }


</script>

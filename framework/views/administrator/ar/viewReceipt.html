<?=renderMessage($var['statusMessage'],'class="infoBox"')?>
<?=renderDownloadLink($var['downloadPath']) ?>

<?=renderErrors($var['validationErrors'],false) ?>


<form id="receipts_form">
    <h1>
        <? if (isset($var['receiptID'])) { ?>
        <a href="?command=viewReceipt&module=ar">Receipt List</a> >
        <? } ?>
        Receipts
    </h1>
    This section allows you to view a submitted receipt. (<a href="?command=receipt&module=ar">Enter a new receipt</a>)
    <br/><br/>

    <? if (isset($var['receiptID'])) { ?>
    <a href="?command=viewReceipt&module=ar">search again</a>
    <br/><br/>
    <? if ($var['isEditable']) { ?>
    <div id="frontContent">
        <br/><br/><br/>
        <table>
            <tr>
                <td>
                    <div class="infoBox">
                        <?=renderCheckbox('groupDepositSlip','Group Deposit Slips',true,$var['groupDepositSlip'],'')?>
                    </div>
                    <br/>
                    <div id="loginBox">
                        <div id="loginTop"></div>
                        <div id="loginContent"><br/>
                            <table cellpadding="3" cellspacing="0" border="0">
                                <tr>
                                    <td colspan="2">
                                        <b>Would you like to approve this receipt now?</b>
                                        <br/><br/>
                                    </td>
                                </tr>
                                <tr>
                                    <td align="left">
                                        <?=renderButton('btnApprove','Approve','onClick="return ajaxContainer(this.id, \'content\',\'?command=viewReceipt&module=ar&action=approve&receiptID=' . $var['receiptID'] . '\')"')?>
                                    </td>
                                    <td align="right">
                                        <?=renderButton('btnReject','Reject','onClick="return ajaxContainer(this.id, \'content\',\'?command=viewReceipt&module=ar&action=reject&receiptID=' . $var['receiptID'] . '\')"')?>
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div id="loginBottom"></div>
                    </div>
                    <br/><br/>
                </td>
                <td>
                    <img src="<?=ASSET_DOMAIN?>assets/images/front_brace.gif"/>
                </td>
                <td valign="top">
                    <ul>
                        <li>If you feel that the information is complete and correct, you may choose to approve theform by responding 'approve'.</li>
                        <li>Otherwise, you may respond 'reject' and the form will be rejected and the client notified.</li>
                    </ul>
                    <br/>
                    <b>If you have any feedback to be sent to the client, add them below<b>
                        <br/>
                        <?=renderTextArea('comments',$var['comments'],'rows="6" cols="50"')?>
                        <br/><br/><br/><br/>
                </td>
            </tr>
        </table>
    </div>
    <? } ?>
    <table class="data-grid" cellspacing="0" cellpadding="3" border="0">
        <tr class="subTitle">
            <td colspan="3">Tenant Details</td>
        </tr>
        <tr class="<?=alternateNextRow()?>">
            <td class="title">Debtor</td>
            <td class="required">&nbsp;</td>
            <td>
                <b><?=$var['debtorID']?></b>  <?=$var['debtorName']?>
            </td>
        </tr>
        <tr class="<?=alternateNextRow()?>">
            <td class="title"><?=$var['is_ledger'] ? 'Ledger' : 'Property' ?></td>
            <td class="required">&nbsp;</td>
            <td><b><?=$var['propertyID']?></b>  <?=$var['propertyName']?></td>
        </tr>
        <tr class="<?=alternateNextRow()?>">
            <td class="title"><?=$var['is_ledger'] ? 'Sub-Ledger' : 'Lease' ?></td>
            <td class="required">&nbsp;</td>
            <td><b><?=$var['leaseID']?></b>  <?=$var['leaseName']?></td>
        </tr>
        <tr class="<?=alternateNextRow()?>">
            <td class="title">Trading Name</td>
            <td class="required">&nbsp;</td>
            <td><?=$var['tenantName']?></td>
        </tr>
        <tr class="<?=alternateNextRow()?>">
            <td class="title">Premises</td>
            <td class="required">&nbsp;</td>
            <td><?=$var['leaseDescription']?></td>
        </tr>
        <tr class="subTitle">
            <td colspan="3">Payment Details</td>
        </tr>
        <tr class="<?=alternateNextRow()?>">
            <td class="title"><?=ucwords(strtolower($_SESSION['country_default']['trust_account']))?></td>
            <td class="required">&nbsp;</td>
            <td>
                (<b><?=$var['accountID']?></b>) <?=$var['accountName']?>
            </td>
        </tr>
        <tr class="<?=alternateNextRow()?>">
            <td class="title">Payment Type</td>
            <td class="required">&nbsp;</td>
            <td><?=$var['paymentTypeList'][$var['paymentType']]?></td>
        </tr>
        <? if ($var['paymentType'] == 'CHQ') { ?>
        <tr class="<?=alternateNextRow()?>">
            <td class="title">Drawer Name</td>
            <td class="required">&nbsp;</td>
            <td><?=$var['drawerName']?></td>
        </tr>
        <tr class="<?=alternateNextRow()?>">
            <td class="title">Cheque Number</td>
            <td class="required">&nbsp;</td>
            <td><?=$var['chequeNumber']?></td>
        </tr>
        <tr class="<?=alternateNextRow()?>">
            <td class="title">Bank</td>
            <td class="required">&nbsp;</td>
            <td><?=$var['bankList'][$var['bankID']]?></td>
        </tr>
        <? if (displayBsbFromSession()) { ?>
            <tr class="<?=alternateNextRow()?>">
                <td class="title"><?=bsbLabelFromSession()?></td>
                <td class="required">&nbsp;</td>
                <td><?=formatWithDelimiter($var['bsbNumber'])?></td>
            </tr>
        <? } ?>
        <? } ?>
        <? if ($var['paymentType'] == 'CSH') { ?>
        <? } ?>
        <? if ($var['paymentType'] == 'DIR') { ?>
        <tr class="<?=alternateNextRow()?>">
            <td class="title">Direct Deposit Name</td>
            <td class="required">&nbsp;</td>
            <td><?=$var['depositName']?></td>
        </tr>
        <? } ?>
        <?php if($var['processStatus'] != 3) { ?>
        <tr class="<?=alternateNextRow()?>">
            <td class="title">Date</td>
            <td class="required">&nbsp;</td>
            <td>
                <?=renderSmartDate('transactionDate',$var['transactionDate'])?> <?=renderButton('btnUpdate','Update','onClick="return ajaxContainer(this.id, \'content\',\'?command=viewReceipt&module=ar&action=updateDate&receiptID=' . $var['receiptID'] . '\')"')?>
            </td>
        </tr>
        <?php } else { ?>
        <tr class="<?=alternateNextRow()?>">
            <td class="title">Date</td>
            <td class="required">&nbsp;</td>
            <td>
                <?=$var['transactionDate']?>
                <?=renderHidden('transactionDate', $var['transactionDate'])?>
            </td>
        </tr>
        <?php } ?>

        <tr class="<?= alternateNextRow() ?>">
            <td class="title">Clearance Days</td>
            <td class="required"></td>
            <td>
                <?= renderHidden('clearDate', $var['clearDate']) ?>
                <?= renderSimpleDropDownList('clearDays', $var['clearDaysOpts'], $var['clearDays'],  'onChange="return ajaxContainer(this.id, \'content\',\'?command=viewReceipt&module=ar&action=changeClearanceDays&receiptID=' . $var['receiptID'] . '\')"') ?>
                <? if($var['clearDays'] != 0 && $var['clearDate'] != ""){ ?>
                <span>&nbsp; Clear Date: <?= $var['clearDate'] ?></span>
                <? } ?>
            </td>
        </tr>

        <tr class="highlight">
            <td class="title">Amount</td>
            <td class="required">&nbsp;</td>
            <td><?=toMoney($var['transactionAmount'])?></td>
        </tr>
        <tr class="<?=alternateNextRow()?>">
            <td class="title">Receipt Number</td>
            <td class="required"></td>
            <td><?=$var['receiptNumber']?></td>
        </tr>
    </table>
    <table class="data-grid" width="100%" cellpadding="0" cellspacing="0" border="0">
        <tr class="subTitle">
            <td colspan="10">Allocated Amounts</td>
        </tr>
        <tr class="fieldDescription">
            <td>Date</td>
            <td><?=$var['is_ledger'] ? 'Ledger' : 'Property' ?></td>
            <td><?=$var['is_ledger'] ? 'Sub-Ledger' : 'Lease' ?></td>
            <td>Type</td>
            <td>Invoice</td>
            <td>Account</td>
            <td>Description</td>
            <td style="text-align:right">Unpaid Amount (Original)</td>
            <td style="text-align:right">Unpaid Amount (Current)</td>
            <td style="text-align:right">Applied Amount</td>
        </tr>
        <? foreach ($var['allocatedList'] as $key => $row) { ?>
        <tr class="<?=(($row['current'] < $row['appliedAmount']) ? 'warning' : alternateNextRow())?>">
            <td><?=$row['transactionDate']?></td>
            <td><?=$row['propertyID']?></td>
            <td><?=$row['leaseID']?></td>
            <td><?=$row['transactionType']?> </td>
            <td><?=$row['invoiceNumber']?></td>
            <td><?=$row['accountID']?></td>
            <td><?=$row['description']?></td>
            <td style="text-align:right"><?=toMoney($row['amount'])?></td>
            <td style="text-align:right"><?=toMoney($row['current'])?></td>
            <td style="text-align:right">
                <?=toMoney($row['appliedAmount'])?>
            </td>
        </tr>
        <? } ?>
        <tr class="highlight">
            <td colspan="7"></td>
            <td style="text-align:right"><?=toMoney($var['unpaidTotal'])?></td>
            <td style="text-align:right"></td>
            <td style="text-align:right"><?=toMoney($var['appliedTotal'])?></td>
        </tr>
    </table>
    <? if ($var['unallocatedList'] !== null) { ?>
    <table class="data-grid" width="100%" cellpadding="0" cellspacing="0" border="0">
        <tr class="subTitle">
            <td colspan="8">Unallocated Amounts</td>
        </tr>
        <tr class="fieldDescription">
            <td><?=$var['is_ledger'] ? 'Ledger' : 'Property' ?></td>
            <td><?=$var['is_ledger'] ? 'Sub-Ledger' : 'Lease' ?></td>
            <td>Account</td>
            <td>Description</td>
            <td style="text-align:right">Amount</td>
            <td align="right">Tax Amount</td>
            <td align="center">Update</td>
        </tr>
        <? foreach ($var['unallocatedList'] as $key => $unallocated) { ?>
        <tr class="<?=alternateNextRow()?>">
            <td><?=$unallocated['propertyID']?></td>
            <td><?=$unallocated['leaseID']?></td>
            <td><?=$unallocated['accountID']?></td>
            <td><?=renderTextBox('unallocatedDescription_' . $key, $unallocated['description'], ' size="100" ')?></td>
            <td align="right"><?=toMoney($unallocated['unallocatedAmount'])?></td>
            <td align="right"><?=toMoney($unallocated['taxAmount'])?></td>
            <td align="right"><?=renderButton('btnSave','Save','onClick="return ajaxContainer(this.id,\'content\',\'?command=viewReceipt&module=ar&action=updateDescription&receiptID=' . $var['receiptID'] . '\')"')?></td>
        </tr>
        <? } ?>
    </table>
    <? } ?>
    <? } else {  ?>
    <table class="data-grid" width="100%" cellpadding="0" cellspacing="0" border="0">
        <tr class="fieldDescription">
            <td>Select <?=ucwords(strtolower($_SESSION['country_default']['portfolio_manager']));?></td>
            <td>From Date</td>
            <td>To Date</td>
            <td colspan="2">With Status</td>
        </tr>
        <tr class="<?=alternateNextRow()?>">
            <td><?= renderKeyedDropDownListV2('portfolioType','parameterID' ,'parameterDescription', $var['portfolioListMode'], $var['portfolioType'], 'recommended="true"  onBlur="validateField(this)"',true,'All') ?></td>
            <td><? renderSmartDate('fromDate',$var['fromDate'],'') ?></td>
            <td><? renderSmartDate('toDate',$var['toDate'],'') ?></td>
            <td><?=renderSimpleDropDownList('status',$var['statusList'], $var['status'],'onChange="return ajaxContainer(this.id, \'content\',\'?command=viewReceipt&module=ar\');"','','','','All'); ?></td>
            <td class="right"><?=renderButton('btnSubmit','Search &rarr;','onClick="return ajaxContainer(this.id, \'content\',\'?command=viewReceipt&module=ar\');"'); ?></td>
        </tr>
    </table>
    <br/>
    <table class="data-grid" cellspacing="0" cellpadding="3" border="0">
        <tr class="fieldDescription">
            <td align="center"><?=renderSelectGroup('chkBankDeposit')?></td>
            <td>Date</td>
            <td>Debtor</td>
            <td><?=$var['is_ledger'] ? 'Ledger' : 'Property' ?></td>
            <td><?=$var['is_ledger'] ? 'Sub-Ledger' : 'Lease' ?></td>
            <td>Payment Type</td>
            <td style="text-align:right">Amount</td>
            <td>Created By</td>
            <td>Status</td>
            <td>View</td>
            <td style="text-align:right">Delete</td>
            <!--td style="text-align:right">Complete</td-->
        </tr>
        <? foreach($var['receiptList'] as $row) { ?>
        <tr class="<?=alternateNextRow()?>">
            <td align="center"><? renderCheckBox('receiptID_' . $row['receiptID'],'',true,$row['bankDeposit'], ' class="chkBankDeposit"') ?></td>
            <td><?=$row['transactionDate']?></td>
            <td><?=$row['debtorID']?></td>
            <td><?=$row['propertyID']?></td>
            <td><?=$row['leaseID']?></td>
            <td><?=$var['paymentTypeList'][$row['paymentType']]?></td>
            <td style="text-align:right"><?=toMoney($row['transactionAmount'])?></td>
            <td><?=$row['createUser']?> on <?=$row['createDate']?></td>
            <td>
                <img class="icon" src="<?=ASSET_DOMAIN?>assets/images/icons/<?=$var['taskIcon'][$row['processStatus']]?>"
                     alt="<?=$var['taskDescription'][$row['processStatus']]?>"/>&nbsp;<?=$var['taskDescription'][$row['processStatus']]?>
            </td>
            <td>
                <a href="?command=viewReceipt&module=ar&receiptID=<?=$row['receiptID']?>">View</a>
            </td>
            <td style="text-align:right">
                <? if($row['processStatus']<3) { ?>
                <a href="#" onClick="return ajaxCall(null, 'content','?command=viewReceipt&module=ar&action=delete&receiptID=<?=$row['receiptID']?>')">
                    <img src="<?=ASSET_DOMAIN?>assets/images/icons/delete.png"/>
                </a>
                <? } ?>
            </td>
        </tr>
        <? } ?>


        <tr class="footer">
            <td colspan="12"><? renderButton('btnBankDeposit','Generate Bank Deposit Slip &rarr;','onClick="return ajaxContainer(this.id, \'content\',\'?command=viewReceipt&module=ar&action=bankDepositSlip\')"') ?></td>
        </tr>
    </table>
    <? } ?>
</form>
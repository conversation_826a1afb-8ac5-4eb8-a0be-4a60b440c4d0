<link type="text/css" href="<?=ASSET_DOMAIN?>assets/semantic-ui/semantic.css?<?=CSS_VERSION?>" rel="stylesheet" />
<link type="text/css" href="assets/angular-1.8.3/angular-auto-complete.css?<?=CSS_VERSION?>" rel="stylesheet" />
<link type="text/css" href="assets/css/bootstrap-grid.css?<?=CSS_VERSION?>" rel="stylesheet"/>
<link type="text/css" href="assets/app/customStyles.css?<?=CSS_VERSION?>" rel="stylesheet"/>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/datatables.net-dt/2.1.7/css/dataTables.dataTables.min.css" integrity="sha512-JbyOZyqfBvhWNzVXZy2zUX9Hhp8+JGL15feGcRq1JnS1ZIxEdECKSqT+eLuZ8BvvzGHkxrBdu+EuJLdHk+kQ8g==" crossorigin="anonymous" referrerpolicy="no-referrer" />
<script src="https://cdnjs.cloudflare.com/ajax/libs/datatables.net/2.1.7/dataTables.min.js" integrity="sha512-uQwfH1NYeXU6Whr3PrKxExRgtnfVkcs30HKaAQtReHSVwQv040Spq22cZo1MaQZshLDTuIncxqWJfIVXyr/GSQ==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
<script type="text/javascript" language="javascript" src="assets/angular-1.8.3/angular.min.js?<?=JS_VERSION?>"></script>
<script type="text/javascript" language="javascript" src="assets/angular-1.8.3/chosen.js?<?=JS_VERSION?>"></script>
<script type="text/javascript" language="javascript" src="assets/angular-1.8.3/angular-sanitize.js"></script>
<script type="text/javascript" language="javascript" src="assets/angular-1.8.3/angular-animate.min.js"></script>
<script type="text/javascript" language="javascript" src="assets/angular-1.8.3/lodash.min.js"></script>
<script type="text/javascript" language="javascript" src="assets/angular-1.8.3/angular-auto-complete.js"></script>
<script type="text/javascript" language="javascript" src="assets/js/ng-datatable.js"></script>
<script type="text/javascript" language="javascript" src="ngApp/main.js?<?=JS_VERSION?>"></script>

<?php 
	htmlerStart('receipt','ngApp/ar/receipting.js');
	titler('Receipts','This section allows you to create a new receipt. During the search process, if you select a debtor with multiple properties or leases and do not refine your selection - you will be able to receipt against all returned tenants.');
?>
<div id="fileDownload">

</div>

<div id="dl_link_div"  ng-show="!downloadSundry" >
	<p id="download_link_div"></p>
</div>
<div   ng-show="downloadSundry"    >
	<div  ng-bind-html="downloadSundry" ></div>
</div>
<div   ng-show="downloadReceipt"    >
	<div  ng-bind-html="downloadReceipt" ></div>
</div>
<input type="hidden" name="methodFilter" id="methodFilter" value="<?= $var['methodFilterStr']; ?>">
<input type="hidden" name="useMethod" id="useMethod" value="<?= $var['method']; ?>">
<input type="hidden" name="useLeaseID" id="useLeaseID" value="<?= $var['leaseID']; ?>">
<?php if(isset($var['useAmt'])): ?>
	<input type="hidden" name="useAmt" id="useAmt" value="<?= $var['useAmt']; ?>">
<? endif; ?>
<input type="hidden" name="useFileTransID" id="useFileTransID" value="<?= $var['fid']; ?>">
<input type="hidden" name="dropsCodes" id="dropsCodes" value="<?= $var['dropsStr']; ?>">
<input type="hidden" name="clearDaysDefault" id="clearDaysDefault" value="<?= $var['clearDaysDefault']; ?>">
<input type="hidden" name="propertyManagerLabel" id="propertyManagerLabel" value="<?=ucwords(strtolower($_SESSION['country_default']['property_manager']))?>">
<!-- Top Search -->
	<table class="data-grid" cellpadding="3" cellspacing="0" border="0" style="display: none"  id="showTahle">
		<tr >
			<td class="title">Select a Bank Account</td>
			<td class="required">*</td>
			<td colspan="100%">
				<?php
				$params = array(
						"ng-model" 		=> 'bank_acct_id',
						"data-chosen" 	=> null,
						"ng-change" 	=> "loadSearchDetails('bankSrch')",
						"style" 		=> 'width:300px;',
				);
				selecter('bank_acct_id','bank_acct_id',$var['bankList'],"",$params);

				$params = array(
						'ng-click'=>"loadSearchDetails('bankSrch')",
						'style'=>'margin-top: -2px;margin-left:5px;',
						'class'=>'ui-button ui-button-text-only ui-button-text'
				);
				buttoner('<img src="' . ASSET_DOMAIN . 'assets/images/icons/arrow_right.png" title="Search" style="height: 13px;">',"btn-refresh-bank",$params);
				?>
			</td>
		</tr>
		<tr  ng-show="methodSelectShow">
			<td class="title">Select a Method</td>
			<td class="required">*</td>
			<td style="width: 120px;">
				<?php
					$params = array(
						"ng-model" 		=> 'method',
						"ng-change"		=> 'methodChange()',
						"data-chosen" 	=> null,
						"ng-options" 	=> 'item.value as item.label for item in methodFilter',
						"style" 		=> 'width:140px;',
					); 
					selecter('method','method',array(),"",$params);
				?>
			</td>
			<td>
				<div ng-show="methodShow" ng-cloak>
					<?php foreach($var['drops'] as $drop){?>
						<div ng-show="method == '<?= $drop ?>'" ng-cloak style="display: inline-block;">
							<?php
								$params = array(
									"ng-model" 		=> 'method_'.$drop.'_id',
									"data-chosen" 	=> null,
									"ng-cloak" 		=> null,
									"ng-change"		=> "loadSearch('".$drop."')",
									"style" 		=> 'width:300px;',
								);
								selecter('method_'.$drop.'_id','method_'.$drop.'_id',$var[$drop.'List'],"",$params);
								$params = array(
									'ng-click'=>"loadSearch('".$drop."')",
									'ng-show'=>'methodShow && method_id',
									'style'=>'margin-top: -2px;margin-left:5px;',
									'class'=>'ui-button ui-button-text-only ui-button-text'
								);
								buttoner('<img src="'. ASSET_DOMAIN . 'assets/images/icons/arrow_right.png" title="Search" style="height: 13px;">',"btn-method-".$drop,$params);
							?>
						</div>
					<?php } ?>

					<?php
						$params = array(
							"ng-if" 		=> "method != 'leases'",
							"ng-model" 		=> 'method_id',
							"data-chosen" 	=> null,
							"ng-cloak" 		=> null,
							"ng-change"		=> 'loadSearch()',
							"ng-options" 	=> 'dd.value as dd.label for dd in methodList',
							"style" 		=> 'width:300px;',
						); 
						//selecter('method_id','method_id',array(),"",$params);
						//"ng-if" 		=> "method == 'leases'",
						$params = array(
							"ng-model" 		=> 'method_id',
							"data-chosen" 	=> null,
							"ng-cloak" 		=> null,
							"ng-change"		=> 'loadSearch()',
							"ng-options" 	=> 'dd.value as dd.label group by dd.category for dd in methodList',
							"style" 		=> 'width:300px;',
						);
						//selecter('method_id','method_id',array(),"",$params);


					?>
				</div>
				<div ng-show="invoiceShow" ng-cloak>
					<?php
						$params = array(
							'ng-model'=>'invoice_no',
							'htmler-key-enter'=>"loadInvoiceDetails()",
							'auto-complete'=>'autoCompleteInvoices',
							'placeholder'=>'Type Invoice Number'
						); 
						textboxer('invoice_no','invoice_no','',$params);
						$params = array(
							'ng-click'=>"loadInvoiceDetails()",
							'style'=>'margin-top: -2px;margin-left:5px;',
							'class'=>'ui-button ui-button-text-only ui-button-text'
						);
						buttoner('<img src="' . ASSET_DOMAIN . 'assets/images/icons/arrow_right.png" title="Search" style="height: 13px;">',"btn-method",$params);
						$params = array(
							'ng-click'=>'clearInvoiceDetails()',
							'style'=>'margin-top: -2px;margin-left:5px;',
							'class'=>'ui-button ui-button-text-only ui-button-text'
						); 
						buttoner('<img src="' . ASSET_DOMAIN . 'assets/images/icons/delete.png" title="Search" style="height: 13px;">',"btn-method",$params);
					?>
				</div>
				<div ng-show="crnShow" ng-cloak>
					<?php
						$params = array(
							'ng-model'=>'crn_no',
							'placeholder'=>'Type CRN',
							'htmler-key-enter'=>"loadCRNDetails()",
							'auto-complete'=>'autoCompleteCRN'
						);  
						textboxer('crn_no','crn_no','',$params); 
						$params = array(
							'ng-click'=>"loadCRNDetails()",
							'style'=>'margin-top: -2px;margin-left:5px;',
							'class'=>'ui-button ui-button-text-only ui-button-text'
						);
						buttoner('<img src="' . ASSET_DOMAIN . 'assets/images/icons/arrow_right.png" title="Search" style="height: 13px;">',"btn-method",$params);
						$params = array(
							'ng-click'=>'clearCRNDetails()',
							'style'=>'margin-top: -2px;margin-left:5px;',
							'class'=>'ui-button ui-button-text-only ui-button-text'
						); 
						buttoner('<img src="' . ASSET_DOMAIN . 'assets/images/icons/delete.png" title="Search" style="height: 13px;">',"btn-method",$params);
					?>
				</div>
			</td>
		</tr>
		<tr ng-show="propertiesSrchShow" ng-cloak>
			<td class="title">Select a {{ (leaseLabel == "Lease" ? "Property" : "Ledger" ) }}</td>
			<td class="required">&nbsp;</td>
			<td colspan="100%">
				<?php
					$params = array(
						"ng-model" 		=> 'property_id',
						"data-chosen" 	=> null,
						"ng-options" 	=> 'dd.value as dd.label for dd in propertiesSrch',
						"ng-change" 	=> "loadSearchDetails('propertySrch')",
						"style" 		=> 'width:300px;',
					); 
					selecter('property_id','property_id',array(),"",$params);

					$params = array(
						'ng-click'=>"loadSearchDetails('propertySrch')",
						'style'=>'margin-top: -2px;margin-left:5px;',
						'class'=>'ui-button ui-button-text-only ui-button-text'
					);
					buttoner('<img src="' . ASSET_DOMAIN . 'assets/images/icons/arrow_right.png" title="Search" style="height: 13px;">',"btn-refresh-prop",$params);
				?>
			</td>
		</tr>	
		<tr ng-show="leaseDebtorsSrchShow" ng-cloak>
			<td class="title">Select a Debtor</td>
			<td class="required">&nbsp;</td>
			<td colspan="100%">
				<?php
					$params = array(
						"ng-model" 		=> 'debtor_id',
						"data-chosen" 	=> null,
						"ng-options" 	=> 'dd.value as dd.label for dd in leaseDebtorsSrch',
						"ng-change" 	=> "loadSearchDetails('debtorSrch')",
						"style" 		=> 'width:300px;',
					); 
					selecter('debtor_id','debtor_id',array(),"",$params);

					$params = array(
						'ng-click'=>"loadSearchDetails('debtorSrch')",
						'style'=>'margin-top: -2px;margin-left:5px;',
						'class'=>'ui-button ui-button-text-only ui-button-text'
					);
					buttoner('<img src="' . ASSET_DOMAIN . 'assets/images/icons/arrow_right.png" title="Search" style="height: 13px;">',"btn-refresh-deb",$params);
				?>
			</td>
		</tr>
		<tr ng-show="leasesSrchShow" ng-cloak >
			<td class="title">Select a {{ leaseLabel }}</td>
			<td class="required">&nbsp;</td>
			<td colspan="100%">
				<span ng-show="method == 'ledgerProperties'" >
				<?php
					$params = array(
						"ng-model" 		=> 'lease_id',
						"data-chosen" 	=> null,
						//"ng-options" 	=> 'dd.value as dd.label for dd in leasesSrch',
						"ng-options" 	=> 'dd.value as dd.label for dd in leasesSrch',
						"ng-change" 	=> "loadSearchDetails('leaseSrch')",
						"style" 		=> 'width:300px;',
					); 
					selecter('lease_id','lease_id',array(),"",$params);

					$params = array(
						'ng-click'=>"loadSearchDetails('leaseSrch')",
						'style'=>'margin-top: -2px;margin-left:5px;',
						'class'=>'ui-button ui-button-text-only ui-button-text'
					);			
				?>
				</span>
				<span ng-show="method != 'ledgerProperties'" >
				<?php
					$params = array(
						"ng-model" 		=> 'lease_id',
						"data-chosen" 	=> null,
						//"ng-options" 	=> 'dd.value as dd.label for dd in leasesSrch',
						"ng-options" 	=> 'dd.value as dd.label group by dd.category for dd in leasesSrch',
						"ng-change" 	=> "loadSearchDetails('leaseSrch')",
						"style" 		=> 'width:300px;',
					); 
					selecter('lease_id','lease_id',array(),"",$params);

					$params = array(
						'ng-click'=>"loadSearchDetails('leaseSrch')",
						'style'=>'margin-top: -2px;margin-left:5px;',
						'class'=>'ui-button ui-button-text-only ui-button-text'
					);			
				?>
				</span>
				<?php buttoner('<img src="' . ASSET_DOMAIN . 'assets/images/icons/arrow_right.png" title="Search" style="height: 13px;">',"btn-refresh-lease",$params); ?>
			</td>
		</tr>

		</tr>
		<tr ng-show="txnShow" ng-cloak>
			<td class="title">Import</td>
			<td class="required">&nbsp;</td>
			<td colspan="100%">


				<div class="ui action input">
					<input upload-file type="file" name="file" >
					<button class="tiny ui icon button"  >
						<i class="ui upload icon">
						</i>
						Upload
					</button>
				</div>
			</td>
		</tr>	
		<tr ng-show="txnButton" ng-cloak>
			<td class="title">Or Select Business Date</td>
			<td class="required">&nbsp;</td>
			<td colspan="100%">
				<?php 
					dater("txn_file_date","txn_file_date","",array('ng-model'=>'txn_file_date'));
				?>
				<?php
					$params = array(
									"ng-click" 			=> "downloadTxnFile()",
									'style'				=> "margin-left:10px;",
									'ng-cloak' 			=> null
								   ); 
					buttoner("Download from Macquarie Bank","download-txn-btn",$params); 
				?>
			</td>
		</tr>	
	</table>
<!-- Refine Search -->
	<div class="htmler-accordion" ng-show="txnShow" style="border-top:0px;" ng-cloak>
		<div class="item" ng-repeat="(transDate,dateList) in file_trans_list_grp" ng-if="dateList.datas && dateList.datas.length > 0" ng-class="{active:$parent.activedion==$index}">
			<div class="head" ng-click="$parent.activedion=($parent.activedion == $index ? null : $index)">
				Process receipts for {{transDate}}
				<span class="icon" ng-if="$parent.activedion!=$index">&#43;</span>
				<span class="icon" ng-if="$parent.activedion==$index">&#8722;</span>
			</div>
			<div class="body" ng-show="$parent.activedion==$index" ng-cloak>
				<div class="content no-padding">
					<div style="padding:0;min-height: 25px;">
						<table class="data-grid data-tbl" cellpadding="3" cellspacing="0" border="0">
							<tr>
								<?php labeler('Search Statement Description'); ?>
								<td>
									<?php
										$params = array(
											'ng-model'=>'dateList.filters.keyword',
											'ng-keyup'=>'filterTxnDateListTbl(transDate)'
										); 
										textboxer('sta_desc_filter','sta_desc_filter','',$params);
									?>
									<?php
										$params = array(
											'ng-cloak' 			=> 	null,
											'ng-if' 			=>  'dateList.filters.keyword',
											'ng-click' 			=>  'clearKeywordFilterTxnDateListTbl(transDate)',
											'title'				=> 	'Clear search keyword',
											'style' 			=>  'display: inline-block;vertical-align: middle;',
										);
										linker('<img src="' . ASSET_DOMAIN . 'assets/images/icons/delete.png">',"#",$params);		
									?>
								</td>
								<td></td>
								<td style="text-align: right;">
									<span style="display: inline-block;margin-right: 20px;">
										<?php 
											$params = array(
														"ng-model" 		=> 'dateList.showDeleted',
														'ng-change' 	=> "getDeletedBankReceipt(transDate)",
														"style" 		=> "vertical-align:middle;margin-right:5px;margin-top:-3px;",
													  );
											checkboxer("Show Deleted","showDeleted-{{transDate}}","showDeleted-{{transDate}}","1",false,$params);
										?>	
									</span>
									<?php
										$params = array(
											"class" 	 => 	"ui-button ui-widget ui-state-default ui-corner-all ui-button-text-only",
											"style" 	 => 	"float:right;padding:5px 10px;margin-left:5px;",
											"ng-class"	 => 	"{'disabled ui-state-disabled':!dateList.chAll}",
											"ng-disabled"=> 	"!dateList.chAll",
											"ng-click" 	 => 	"deleteTxnTransMultiple(transDate)",
										);
										buttoner("Delete Checked ({{countCheckedBankReceipt(transDate)}})","delete-btn-{{dateList.indx}}",$params);
									?>									
								</td>
							</tr>
						</table>
					</div>
					<table class="data-grid data-tbls" cellpadding="0" cellspacing="0" border="0">
						<tr class="fieldDescription">
							<td><?php checkboxer("","check-all-{{dateList.indx}}","check-all-{{dateList.indx}}","1",false,array("ng-model"=>'dateList.chAll',"ng-change"=>"groupFileNoReceipTransCheckAll(transDate)")); ?></td>
							<td>Description</td>
							<td>Payment Type</td>
							<td style="width:210px;">Debtor</td>
							<td style="width:210px;">Property</td>
							<td style="width:310px;">Lease</td>
							<td>CRN</td>
							<td class="nums">Amount</td>
							<td></td>			    
						</tr>
						<?php datableRower('data','dateList',array('ng-if'=>'!data.processed','ng-show'=>"data.srchShow",'ng-class'=>"{'bg-blue': file_trans_key != null &&  file_trans_key == data.list_key,'bg-red':!data.debtor_code,'bg-disabled':data.deleted}")) ?>
							<td>
								<?php 
									$params = array(
												"ng-if" 		=> '!data.deleted',
												"ng-model" 		=> 'data.ch',
												'ng-change' 	=> "groupFileNoReceipTransCheckWatch(transDate)"
											  );
									checkboxer("","ch-txn-{{data.list_key}}","ch-txn-{{data.list_key}}","1",false,$params);
								?>	
							</td>
							<td>{{data.description}}</td>
							<td>{{data.trans_type_name}}</td>
							<td style="width:210px;">
								<span title="{{data.debtor_code+' '+data.debtor_name}}" ng-cloak>
									{{data.debtor_code+' '+data.debtor_name | htmlerStrRestrict: 25}}
								</span>
							</td>
							<td style="width:210px;">
								<span title="{{data.property_code+' '+data.property_name}}" ng-cloak>
									{{data.property_code+' '+data.property_name | htmlerStrRestrict: 25}}
								</span>
							</td>
							<td style="width:310px;">
								<span title="{{data.lease_code+' '+data.lease_name}}" ng-cloak>
									{{data.lease_code+' '+data.lease_name | htmlerStrRestrict: 35}}
								</span>
							</td>
							<td>
								<span ng-show="data.crn" title="{{data.crn | htmlerStrRestrict: 7:true}}" ng-cloak>
									{{data.crn | htmlerStrRestrict: 7:true}}
								</span>
							</td>
							<td class="nums">{{data.amountTxt}}</td>
							<td style="text-align: center;">
								<?php
									$params = array(
										'class'		=> 	'data0.-tbls-icon',
										'style'		=>  "margin-left:10px;",
										'ng-click'	=> 	'fileTransAdjustmentAsk(data.list_key)',
										'ng-show' 	=> 	'!data.deleted',
										'title'		=> 	'Skip receipt & Add as adjustment'
									);
									//linker('<img src="' . ASSET_DOMAIN . 'assets/images/icons/split.png">',"#",$params);
									$params = array(
										'class'		=> 	'data0.-tbls-icon',
										'style'		=>  "margin-left:10px;",
										'ng-click'	=> 	'populateReceiptCharges(data.list_key,true)',
										'title'		=> 	'View Receipt for processing',
										'ng-show' 	=> 	'!data.deleted',
										'ng-cloak' 	=> 	null,
									);
									linker('<img src="' . ASSET_DOMAIN . 'assets/images/icons/run.png">',"#",$params);
								?>
							</td>
						</tr>
					</table>
					<div class="datatable-bottom">
					    <div class="dt-col"></div>
					    <div class="dt-col">
					        <?= paginater('dateList') ?>
					    </div>
					</div>	
				</div>
			</div>
		</div>		
		<p ng-show="Object.keys(file_trans_list_grp).length === 0" style="text-align: center;" ng-cloak>No transactions found.</p>
	</div>
	<table class="data-grid data-tbls" cellpadding="3" cellspacing="0" border="0" ng-show="refineSrchList && refineSrchList.length > 1 && !txnMatching.enabled" ng-cloak>
		<tr class="subTitle"><td class="fieldTitle" colspan="100%">Matching Records - Click to refine your search</td></tr>
		<tr class="fieldDescription">
			<td>Debtor</td>
			<td>{{ (leaseLabel == "Lease" ? "Property" : "Ledger" ) }}</td>
			<td>{{ leaseLabel }}</td>
			<td>Description</td>
			<td  ng-if="method != 'ledgerProperties' && method != 'subLedgerProperties'" >Status</td>
		</tr>
		<tr ng-repeat="(key,data) in refineSrchList" class="has-hover" ng-click="selectSrchResult(data.debtorID,data.propertyID,data.leaseID)">
			<td><strong>{{data.debtorID}}</strong> {{data.debtorName}}</td>
			<td><strong>{{data.propertyID}}</strong> {{data.propertyName}}</td>
			<td><strong>{{data.leaseID}}</strong> {{data.leaseName}}</td>
			<td>{{data.description}}</td>
			<td ng-if="method != 'ledgerProperties' && method != 'subLedgerProperties'" ><strong>{{data.leaseStatus == 'C' ? "Current" : "Vacated"}}</strong></td>		
		</tr>
	</table>
<div ng-show="file_trans_list[file_trans_key]" ng-cloak style="border-bottom:3px solid #DDD">
	<table id="main-header-form" class="data-grid data-tbls" cellpadding="3" cellspacing="0" border="0">
		<!-- <tr class="subTitle"><td class="fieldTitle" colspan="100%">Payment Details - Receipting by TXN</td></tr> -->
		<tr class="subTitle"><td class="fieldTitle" colspan="100%">Payment Details</td></tr>
	</table>
	<table ng-show="file_trans_list[file_trans_key].onEdit" class="data-grid data-tbls" cellpadding="3" cellspacing="0" border="0" ng-cloak>
		<tr><?php labeler('Bank Statement Description'); ?>
			<td>
				<strong>{{file_trans_list[file_trans_key].description}}</strong>&nbsp;&nbsp;&nbsp;
				<span class="txt-gray" style="font-style: italic;">
					{{file_trans_list[file_trans_key].trans_type_name}} - {{file_trans_list[file_trans_key].trans_date}}
				</span>
			</td>
		</tr>
		<tr>
			<td colspan="100%" style="padding:0">
				<table class="data-grid data-tbls" cellpadding="3" cellspacing="0" border="0">
					<tr class="fieldDescription">
						<td style="width:30%;">Select Match Method</td>
						<td style="width:15%;">
							<span ng-hide="txnMatching.by == 'debtor' || file_trans_list[file_trans_key].debtorDrop.length == 0 || !txnMatching.mainVal" cloak>
								Debtor
							</span>
						</td>
						<td style="width:15%;">
							<span ng-hide="txnMatching.by == 'property' || file_trans_list[file_trans_key].propertyDrop.length == 0 || !txnMatching.mainVal" cloak>
								Property
							</span>
						</td>
						<td style="width:15%;">
							<span ng-hide="txnMatching.by == 'lease' || file_trans_list[file_trans_key].leaseDrop.length == 0 || !txnMatching.mainVal" cloak>
								Lease
							</span>
						</td>
						<td style="text-align:right;width:5%;"></td>
					</tr>
					<tr>
						<td style="width:30%;">
							<?php
								$params = array(
									"ng-model" 		=> 'txnMatching.by',
									"data-chosen" 	=> null,
									"ng-options" 	=> 'dd.value as dd.label for dd in txnMatching.methods',
									"style" 		=> 'width:210px;',
									"ng-change" 	=> "onChangeTxnMatchingDrop('method')",
								); 
								selecter('txnMatchingBy','txnMatchingBy',array(),"",$params);
							?>	
							<div ng-hide="txnMatching.by == 'invoice' || txnMatching.by == 'crn'" style="display: inline-block;margin-left:15px;" ng-cloak>
								<?php
									$params = array(
										"ng-if" 		=> "txnMatching.by == 'lease'",
										"ng-model" 		=> 'txnMatching.mainVal',
										"data-chosen" 	=> null,
										"ng-cloak" 		=> null,
										"ng-options" 	=> 'dd.value as dd.label group by dd.category for dd in txnMatching.mainDrop',
										"style" 		=> 'width:210px;',
										"ng-change" 	=> "onChangeTxnMatchingDrop('main')",
									);
									selecter('txnMatching-mainVal','txnMatching-mainVal',array(),"",$params);

									$params = array(
										"ng-if" 		=> "txnMatching.by != 'lease'",
										"ng-model" 		=> 'txnMatching.mainVal',
										"data-chosen" 	=> null,
										"ng-cloak" 		=> null,
										"ng-options" 	=> 'dd.value as dd.label for dd in txnMatching.mainDrop',
										"style" 		=> 'width:210px;',
										"ng-change" 	=> "onChangeTxnMatchingDrop('main')",
									); 
									selecter('txnMatching-mainVal','txnMatching-mainVal',array(),"",$params);
									
									$params = array(
										"title" 	=> "View Tenant Activity",
										'ng-cloak' 	=> null,
										"ng-show" 	=> "file_trans_list[file_trans_key].leaseID && txnMatching.by == 'lease'",
										'class'		=> 'data0.-tbls-icon',
										'style'		=> 'margin-left:5px;vertical-align:middle;',
										"ng-click" 	=> 'viewFileTransTenantAct()',
									);
									linker('<img src="' . ASSET_DOMAIN . 'assets/images/icons/trial.png">',"#",$params);
								?>	
							</div>
							<div ng-show="txnMatching.by == 'crn'" style="display: inline-block;margin-left:15px;" ng-cloak>
								<?php
									$params = array(
										'ng-model' 			=> 'txnMatching.mainVal',
										'auto-complete' 	=> 'autoCompleteCRN',
										'htmler-key-enter' 	=> "loadCRNDetails()",
										'placeholder' 		=> 'Type CRN Number'
									); 
									textboxer('txnMatching-mainValCRN','txnMatching-mainValCRN','',$params);
									$params = array(
										'ng-click'=>"loadCRNDetails()",
										'style'=>'margin-top: 0.5px;margin-left:5px;',
										'class'=>'ui-button ui-button-text-only ui-button-text'
									);
									buttoner('<img src="' . ASSET_DOMAIN . 'assets/images/icons/arrow_right.png" title="Search" style="height: 13px;">',"btn-method",$params);
									$params = array(
										'ng-click'=>'clearCRNDetails()',
										'style'=>'margin-top: 0.5px;margin-left:5px;',
										'class'=>'ui-button ui-button-text-only ui-button-text'
									); 
									buttoner('<img src="' . ASSET_DOMAIN . 'assets/images/icons/delete.png" title="Search" style="height: 13px;">',"btn-method",$params);
								?>
							</div>
							<div ng-show="txnMatching.by == 'invoice'" style="display: inline-block;margin-left:15px;" ng-cloak>
								<?php
									$params = array(
										'ng-model' 			=> 'txnMatching.mainVal',
										'auto-complete' 	=> 'autoCompleteInvoices',
										'htmler-key-enter' 	=> "loadInvoiceDetails()",
										'placeholder' 		=> 'Type Invoice Number'
									); 
									textboxer('txnMatching-mainValInvoice','txnMatching-mainValInvoice','',$params);
									$params = array(
										'ng-click'=>"loadInvoiceDetails()",
										'style'=>'margin-top: 0.5px;margin-left:5px;',
										'class'=>'ui-button ui-button-text-only ui-button-text'
									);
									buttoner('<img src="' . ASSET_DOMAIN . 'assets/images/icons/arrow_right.png" title="Search" style="height: 13px;">',"btn-method",$params);
									$params = array(
										'ng-click'=>'clearInvoiceDetails()',
										'style'=>'margin-top: 0.5px;margin-left:5px;',
										'class'=>'ui-button ui-button-text-only ui-button-text'
									); 
									buttoner('<img src="' . ASSET_DOMAIN . 'assets/images/icons/delete.png" title="Search" style="height: 13px;">',"btn-method",$params);
								?>
							</div>
						</td>
						<td style="width:15%;">
							<div ng-hide="txnMatching.by == 'debtor' || file_trans_list[file_trans_key].debtorDrop.length == 0 || !txnMatching.mainVal" cloak>
								<?php
									$params = array(
										"ng-model" 		=> 'file_trans_list[file_trans_key].debtorID',
										"data-chosen" 	=> null,
										"ng-options" 	=> 'dd.value as dd.label for dd in file_trans_list[file_trans_key].debtorDrop',
										"style" 		=> 'width:210px;',
										"ng-change" 	=> "onChangeTxnMatchingDrop('debtor')",
									); 
									selecter('txnMatching-debtorID','txnMatching-debtorID',array(),"",$params);
								?>
							</div>
						</td>
						<td style="width:15%;">
							<div ng-hide="txnMatching.by == 'property' || file_trans_list[file_trans_key].propertyDrop.length == 0 || !txnMatching.mainVal" cloak>
								<?php
									$params = array(
										"ng-model" 		=> 'file_trans_list[file_trans_key].propertyID',
										"data-chosen" 	=> null,
										"ng-options" 	=> 'dd.value as dd.label for dd in file_trans_list[file_trans_key].propertyDrop',
										"style" 		=> 'width:210px;',
										"ng-change" 	=> "onChangeTxnMatchingDrop('property')",
									); 
									selecter('txnMatching-propertyID','txnMatching-propertyID',array(),"",$params);
								?>
							</div>
						</td>
						<td style="width:15%;">
							<div ng-hide="txnMatching.by == 'lease' || file_trans_list[file_trans_key].leaseDrop.length == 0 || !txnMatching.mainVal" cloak>
								<?php
									$params = array(
										"ng-model" 		=> 'file_trans_list[file_trans_key].leaseID',
										"data-chosen" 	=> null,
										"ng-options" 	=> 'dd.value as dd.label group by dd.category for dd in file_trans_list[file_trans_key].leaseDrop',
										"style" 		=> 'width:210px;',
										"ng-change" 	=> "onChangeTxnMatchingDrop('lease')",
									); 
									selecter('txnMatching-leaseID','txnMatching-leaseID',array(),"",$params);
								?>
								<?php
									$params = array(
										"title" 	=>"View Tenant Activity",
										'ng-cloak' 	=> null,
										"ng-show" 	=> 'file_trans_list[file_trans_key].leaseID',
										'class'		=> 'data0.-tbls-icon',
										'style'		=> 'margin-left:5px;vertical-align:middle;',
										"ng-click" 	=> 'viewFileTransTenantAct()',
									);
									linker('<img src="' . ASSET_DOMAIN . 'assets/images/icons/trial.png">',"#",$params);
								?>
							</div>
						</td>
						<td style="text-align:right;width:5%;"><?php
							$params = array(
								'ng-cloak' 	=> 	null,
								'title'		=> 	'Cancel editing line',
								'ng-click'	=> 	'editCurrentFileTrans(true)',
							);
							//buttoner("Cancel","cance-unalloc-btn",$params);	

							$params = array(
								'title'		=> 	'Cancel editing line',
								'ng-cloak' 	=> null,								
								'class'		=> 'data0.-tbls-icon',
								'style'		=> 'vertical-align:middle;',
								"ng-click" 	=> 'editCurrentFileTrans(true)',
							);
							linker('<img src="' . ASSET_DOMAIN . 'assets/images/icons/delete.png">',"#",$params);

						?></td>
					</tr>
				</table>
			</td>
		</tr>
		<tr class="footer" ng-show="rec_list.length == 0 && !debtor_id" ng-cloak>
			<td colspan="100%" style="border-top:0;">
				<?php
					buttoner("Skip and go-to Next","skip-next-btn",array("ng-click"=>"skipNextBankReceipt()","ng-show"=>"file_trans_key != null && getNextBankReceiptLine()","ng-cloak"=>null,"style"=>"float:right;margin-right: 5px;"));
					buttoner("Add as Adjustment and go-to Next","process-next-btn",array("ng-click"=>"fileTransAdjustmentAsk(file_trans_key)","ng-show"=>"file_trans_key != null && getNextBankReceiptLine()","ng-cloak"=>null,"style"=>"float:right;margin-right: 5px;"));
					buttoner("Add as Adjustment","process-next-btn",array("ng-click"=>"fileTransAdjustmentAsk(file_trans_key)","ng-show"=>"file_trans_key != null && !getNextBankReceiptLine()","ng-cloak"=>null,"style"=>"float:right;margin-right: 5px;"));			
				?>	
			</td>
		</tr>
	</table>
	<table ng-show="!file_trans_list[file_trans_key].onEdit" class="data-grid data-tbls" cellpadding="3" cellspacing="0" border="0">
		<tr class="fieldDescription">
			<td class="date">Trans Date</td>
			<td>Payment Type</td>
			<td style="width:310px;">Description</td>
			<td style="width:210px;">Debtor</td>
			<td style="width:210px;">Property</td>
			<td style="width:210px;">Lease</td>
			<td style="width:80px;">CRN</td>
			<td class="nums">Amount</td>
			<td class="nums"></td>
		</tr>
		<tr>
			<td class="date">{{file_trans_list[file_trans_key].trans_date}}</td>
			<td>{{file_trans_list[file_trans_key].trans_type_name}}</td>
			<td><strong>{{file_trans_list[file_trans_key].description}}</strong></td>
			<td style="width:210px;">
				<span ng-show="(file_trans_list[file_trans_key].debtor_code)" title="{{file_trans_list[file_trans_key].debtor_code+' - '+file_trans_list[file_trans_key].debtor_name}}" ng-cloak>
					{{file_trans_list[file_trans_key].debtor_code+' - '+file_trans_list[file_trans_key].debtor_name | htmlerStrRestrict: 30}}
				</span>
			</td>
			<td style="width:210px;">
				<span ng-show="(file_trans_list[file_trans_key].property_code)" title="{{file_trans_list[file_trans_key].property_code+' - '+file_trans_list[file_trans_key].property_name}}" ng-cloak>
					{{file_trans_list[file_trans_key].property_code+' - '+file_trans_list[file_trans_key].property_name | htmlerStrRestrict: 30}}
				</span>
			</td>
			<td style="width:210px;">
				<span ng-show="(file_trans_list[file_trans_key].lease_code)" title="{{file_trans_list[file_trans_key].lease_code+' - '+file_trans_list[file_trans_key].lease_name}}" ng-cloak>
					{{file_trans_list[file_trans_key].lease_code+' - '+file_trans_list[file_trans_key].lease_name | htmlerStrRestrict: 30}}
				</span>
				<?php
					$params = array(
						"title" 	=>"{{file_trans_list[file_trans_key].lease_code+' - '+file_trans_list[file_trans_key].lease_name}}",
						'ng-cloak' 	=> null,
						"ng-show" 	=> 'file_trans_list[file_trans_key].lease_code',
						'class'		=> 'data0.-tbls-icon',
						"ng-click" 	=> 'viewFileTransTenantAct()',
					);
					//linker("{{file_trans_list[file_trans_key].lease_code+' - '+file_trans_list[file_trans_key].lease_name | htmlerStrRestrict: 30}}","#",$params);				
					$params = array(
						"title" 	=>"View Tenant Activity",
						'ng-cloak' 	=> null,
						"ng-show" 	=> 'file_trans_list[file_trans_key].lease_code',
						'class'		=> 'data0.-tbls-icon',
						'style'		=> 'margin-left:5px;vertical-align:middle;',
						"ng-click" 	=> 'viewFileTransTenantAct()',
					);
					linker('<img src="' . ASSET_DOMAIN . 'assets/images/icons/trial.png">',"#",$params);
				?>
			</td>
			<td style="width:210px;">
				<span title="{{file_trans_list[file_trans_key].crn}}" ng-cloak>
					{{file_trans_list[file_trans_key].crn | htmlerStrRestrict: 7:true}}
				</span>
			</td>
			<td class="nums">{{file_trans_list[file_trans_key].amountTxt}}</td>
			<td class="nums">
				<?php
					$params = array(
						'ng-cloak' 	=> 	null,
						'ng-show' 	=> 	'!file_trans_list[file_trans_key].onEdit',
						'title'		=> 	'Edit line',
						'class'		=> 	'data0.-tbls-icon',
						'style'		=>  "margin-left:10px;",
						'ng-click'	=> 	'editCurrentFileTrans()',
					);
					linker('<img src="' . ASSET_DOMAIN . 'assets/images/icons/edit.png">',"#",$params);
				?>
			</td>
		</tr>
	</table>
</div>
<div ng-show="hideByCRN" ng-cloak>
	<table class="data-grid data-tbls" cellpadding="3" cellspacing="0" border="0" ng-cloak>
		<tr class="subTitle"><td class="fieldTitle" colspan="100%">You have selected a lease within an inactive property, to receipt to this lease mark the property active.</td></tr>		
	</table>
</div>
<div ng-show="bank_acct_id && debtor_id && !hideByCRN" ng-cloak>
	<!-- Payment header form -->
		<table class="data-grid data-tbls" cellpadding="3" cellspacing="0" border="0">
			<tr class="subTitle" ng-show="!file_trans_list[file_trans_key]" ng-cloak>
				<td class="fieldTitle" colspan="100%">
					Payment Details
				</td>
			</tr>
			<tr>
				<td class="title"><?=ucwords(strtolower($_SESSION['country_default']['trust_account']))?></td>
				<td class="required">&nbsp;</td>
				<td>
					<span ng-show="selected.bankID" ng-cloak>
						<strong>({{selected.bankID}})</strong>
						{{selected.bankAccountName}}
						<strong ng-show="lastReconDateMsg" ng-cloak>({{lastReconDateMsg}})</strong>
					</span>
				</td>
			</tr>
			<tr>
				<td class="title">Payment Type</td>
				<td class="required">*</td>
				<td>
					<?php
						$params = array(
							"ng-model" 		=> 'payment_type',
							"data-chosen" 	=> null,
							"ng-change"		=> 'paymentTypeChange()',
							"ng-options" 	=> 'dd.value as dd.label for dd in payment_type_list',
							"style" 		=> 'width:140px;',
						); 
						selecter('method','method',array(),"",$params);
					?>
				</td>
			</tr>
			<tr ng-show="payment_type == 'CHQ' || payment_type == 'CSH'" ng-cloak>
				<td class="title">Payer Name</td>
				<td class="required">&nbsp;</td>
				<td><?php textboxer("pay_name","pay_name","",array("ng-model"=>"pay_name"));?></td>
			</tr>
			<tr ng-show="payment_type == 'CHQ' || payment_type == 'CSH'" ng-cloak>
				<td class="title">Payer Address</td>
				<td class="required">&nbsp;</td>
				<td><?php textboxer("pay_add","pay_add","",array("ng-model"=>"pay_add"));?></td>
			</tr>
			<tr ng-show="payment_type == 'CHQ' || payment_type == 'CSH'" ng-cloak>
				<td class="title">Payer <?=ucwords(strtolower($_SESSION['country_default']['suburb']))?></td>
				<td class="required">&nbsp;</td>
				<td><?php textboxer("pay_suburb","pay_suburb","",array("ng-model"=>"pay_suburb"));?></td>
			</tr>
			<tr ng-show="(payment_type == 'CHQ' || payment_type == 'CSH') && address_cdf.display_state" ng-cloak>
				<td class="title">Payer State</td>
				<td class="required">&nbsp;</td>
				<td><?php textboxer("pay_state","pay_state","",array("ng-model"=>"pay_state"));?></td>
			</tr>
			<tr ng-show="(payment_type == 'DIR' || payment_type == 'CHQ' || payment_type == 'BPA' || payment_type == 'EPO')" ng-cloak>
				<td class="title">Group Deposit Slips</td>
				<td class="required">&nbsp;</td>
				<td><?php checkboxer("","grp_depo_slip","grp_depo_slip","1",false,array("ng-model" => 'grp_depo_slip',
																						"ng-disabled"=>"file_trans_list[file_trans_key] && file_trans_list[file_trans_key].trans_type == 'CHQ'") ); ?></td>
			</tr>
			<tr ng-show="payment_type == 'DIR'" ng-cloak>
				<td class="title">Direct Deposit Name</td>
				<td class="required">*</td>
				<td><?php textboxer("dir_depo_name","dir_depo_name","",array("ng-model"=>"dir_depo_name"));?></td>
			</tr>
			<tr ng-show="payment_type == 'CHQ'" ng-cloak>
				<td class="title">Drawer Name</td>
				<td class="required">*</td>
				<td><?php textboxer("chq_drawer_name","chq_drawer_name","",array("ng-model"=>"chq_drawer_name"));?></td>		
			</tr>
			<tr ng-show="payment_type == 'CHQ'" ng-cloak>
				<td class="title">Cheque Number</td>
				<td class="required">*</td>
				<td><?php textboxer("chq_number","chq_number","",array("ng-model"=>"chq_number"));?></td>		
			</tr>
			<tr ng-show="payment_type == 'CHQ'" ng-cloak>
				<td class="title">Bank</td>
				<td class="required">*</td>
				<td>
					<?php
						$params = array(
							"ng-model" 		=> 'chq_bank_id',
							"data-chosen" 	=> null,
							"ng-options" 	=> 'dd.value as dd.label for dd in chequeBankList',
							"style" 		=> 'width:300px;',
						); 
						selecter('chq_bank_id','chq_bank_id',array(),"",$params);
					?>
				</td>
			</tr>
			<tr ng-show="payment_type == 'CHQ' && general_cdf.display_bsb" ng-cloak>
				<td class="title">{{ general_cdf.bsb_label }}</td>
				<td class="required">*</td>
				<td><?php textboxer("chq_bsb","chq_bsb","",array("ng-model"=>"chq_bsb"));?></td>		
			</tr>
			<tr>
				<td class="title">Receipt Date</td>
				<td class="required">*</td>
				<td>
					<?php dater("rec_date","rec_date","",array('ng-model'=>'rec_date','ng-change'=>"clearanceDaysSetDate()"));?>
					<span ng-show="file_trans_list[file_trans_key]" ng-cloak>
						<strong>
							(File Trans Date: {{file_trans_list[file_trans_key].trans_date}})
						</strong>
					</span>
				</td>
			</tr>
			<tr>
				<td class="title">@Bank</td>
				<td class="required">*</td>
				<td>
					<?php dater("bank_date","bank_date","",array('ng-model'=>'bank_date'));?>					
				</td>
			</tr>
			<tr ng-cloak>
				<td class="title">Clearance Days</td>
				<td class="required"></td>
				<td>
					<?php
						$params = array(
							"ng-model" 		=> 'clearance_days',
							"data-chosen" 	=> null,
							'ng-change' 	=> "clearanceDaysSetDate()",
							"style" 		=> 'width:80px;',
						); 
						selecter('clearance_days','clearance_days',[
							['label' => 'None','value'=>0],
							['label' => '1','value'=>'1'],
							['label' => '2','value'=>'2'],
							['label' => '3','value'=>'3'],
							['label' => '4','value'=>'4'],
							['label' => '5','value'=>'5'],
							['label' => '6','value'=>'6'],
						],"",$params);
					?>
					<span ng-if="clearance_days != 0 && clearance_date != ''">&nbsp;Clear Date: {{clearance_date}}</span>
				</td>
			</tr>
			<tr>
				<td class="title">Amount</td>
				<td class="required">*</td>
				<td>
					<?php 
						$params = array(
										"ng-model"					=> 	"rec_amount",
										'ng-dblclick'				=>	'allocateAllAndFill()',
										'ng-disabled'				=>	'file_trans_list[file_trans_key]',
										"ng-blur"					=> 	"calculateTotals();formatNumber('rec_amount');validateInvoice()",
										"htmler-select-all-click" 	=> 	null,
										"htmler-number-input" 		=> 	null,
										"style"						=> 	"text-align:right;"
									   );
						textboxer("rec_amount","rec_amount","",$params);
						$params = array(
							'id' 		=> 	'allocate-oldest-btn',
							'class' 	=> 	'data-tbls-icon',
							'ng-click'	=> 	'allocateAuto()',
							'title'		=> 	'Auto Allocate Amount'
						);
						linker('<img src="' . ASSET_DOMAIN . 'assets/images/icons/refresh.png">',"#",$params);
						
						$params = array(
							'id' 		=> 	'allocate-oldest-btn',
							'class' 	=> 	'data-tbls-icon',
							'ng-click'	=> 	'allocateOldest()',
							'title'		=> 	'Allocate Oldest Amount'
						);
						linker('<img src="' . ASSET_DOMAIN . 'assets/images/icons/run.png">',"#",$params);
					?>
				</td>
			</tr>
			<tr ng-show="payment_type == 'CHQ' || payment_type == 'CSH'" ng-cloak>
				<td class="title">Receipt Number</td>
				<td class="required">&nbsp;</td>
				<td><?php textboxer("rec_no","rec_no","",array("ng-model"=>"rec_no"));?></td>
			</tr>
			<tr class="highlight" >
				<td colspan="100%" style="text-align: right;font-size: 12px;">
					<span style="display: inline-block;margin-right: 10px;font-weight: bold;">
						Unpaid Balance {{unpaid_bal | htmlerFormatAmt}}
					</span>
					&ndash;
					<span style="display: inline-block;margin-left: 10px;margin-right: 10px;font-weight: bold">
						Balance Still to Apply {{apply_bal | htmlerFormatAmt}}
					</span>
					&ndash;
					<span style="display: inline-block;margin-left: 10px;font-weight: bold">
						Applied Amount {{applied_total | htmlerFormatAmt}}
					</span>
				</td>
			</tr>

			<!-- RECEIPTING NOTES -->
			<tr class="subTitle clickable" colspan="100%" ng-if="(rec_list_notes | htmlerObjectLength) > 0" ng-click="showHideRecListNotes()">
				<td class="fieldTitle" colspan="100%">Receipting Notes<span class="minMaxIcon" ng-if="!rec_list_notes_show">&#43;</span><span class="minMaxIcon" ng-if="rec_list_notes_show">&#8722;</span></td>
			</tr>
			<tr class="fieldDescription" ng-repeat-start="(leaseCode,lineData) in rec_list_notes" ng-if="(rec_list_notes | htmlerObjectLength) > 0" ng-show="rec_list_notes_show">
				<td colspan="100%">{{lineData.property}} - {{leaseCode}} - {{lineData.name}}</td>			    
			</tr>
			<tr ng-repeat-end ng-if="(rec_list_notes | htmlerObjectLength) > 0"  ng-show="rec_list_notes_show"  class="bg-orange">
				<td colspan="100%">
					<ul>
						<li ng-repeat="(key,data) in lineData.notes" id="note-{{ data.id }}" style="position:relative;">
							<span class="noteDesc" style="display:block;white-space: pre-wrap;margin-bottom:5px; padding-right:40px;" data-content="{{data.desc}}" ng-bind-html="data.desc"></span>
							<span class="noteDetails txt-gray" data-property="{{ data.propertyID}}" data-lease="{{ data.leaseID }}" data-timestamp="{{ data.date }}" data-user="{{ data.by }}">{{data.date}} by {{data.by}}</span>
							<span class="noteOptions" style="position:absolute;top:8px;right:0px;">
								<?php 
									$params = array(
										'ng-click' => "receiptingNoteLoadModal(data)",
										'title' => "Update Receipting Note",
										'style' => "text-decoration: none",
										'htmler-show-modal' => "#receipting-note-modal",
										
									);
									linker('<img src="' . ASSET_DOMAIN . 'assets/images/icons/edit.png" alt="Edit this note">',"#",$params);
								?>  
								<a href="#" class="deleteReceiptingNote" style="margin-left:5px;" ng-click="openConfirmDeleteDialog(data)" ><img src="assets/images/icons/delete.png" alt="Delete this note" /></a>    
							</span>
						</li>
					</ul>
				</td>			    
			</tr>

			<tr class="footer">
				<td colspan="100%" style="text-align: right;">
					<?= linker("Allocate Oldest","#",array('ng-click'=>'allocateOldest()'))." / "; ?> 
					<?= linker("Allocate All","#",array('ng-click'=>"allocateAll()"))." / "; ?> 
					<?= linker("Allocate None","#",array('ng-click'=>"allocateNone()")); ?>
					<span ng-if="method == 'leaseDebtors'"> / </span> 
					<?php
						linker("Enable Credits","#",array('ng-click'=>"showEnableLineCreditsWarningMsg()",'ng-if'=>"method == 'leaseDebtors' && !creditForceEnable")); 
						linker("Disable Credits","#",array('ng-click'=>"enableLineCredits()",'ng-if'=>"method == 'leaseDebtors' && creditForceEnable")); 
					?> 
				</td>
			</tr>
		</table>
	<!-- Payment Details -->
		<table class="data-grid data-tbls" cellpadding="3" cellspacing="0" border="0">
			<!-- Line details -->
				<tr class="fieldDescription">
					<td class="chks">#</td>
					<td class="txts-date">Inv Date</td>
					<td class="txts-date">Due Date</td>
					<td>{{ (leaseLabel == "Lease" ? "Property" : "Ledger" ) }}</td>
					<td>{{ leaseLabel }}</td>
					<td>Type</td>
					<td>Invoice</td>
					<td>From-To Date</td>
					<td>Account</td>
					<td class="center" ng-show="withBonds" >Bonds</td>
					<td>Description</td>
					<td class="nums">Unpaid Amount</td>
					<td class="center" >Credit Remainder</td>
					<td class="nums" style="width:230px;">Applied Amount</td>
					 <!-- style="padding-right: 35px;" -->
				</tr>
				<tr 
					ng-repeat="(key,data) in rec_list" ng-show="rec_list && rec_list.length > 0" 
					ng-class="{
								'bg-green': rec_list[key].line_amount == rec_list[key].unpaidAmount,
								'bg-orange':rec_list[key].line_amount != 0 && rec_list[key].line_amount != rec_list[key].unpaidAmount
							  }" 
					ng-cloak>
					<td class="chks">{{key+1}}</td>
					<td class="txts-date">{{data.transactionDate}}</td>
					<td class="txts-date">{{data.dueDate}}</td>
					<td>{{data.propertyID}}</td>
					<td>{{data.leaseID}}</td>
					<td>{{data.transactionType}}</td>
					<td>{{data.invoiceNumber}}</td>
					<td>{{data.fromDate}} - {{data.toDate}}</td>
					<td>{{data.accountID}}</td>
					<td  ng-show="withBonds" class="center"><input ng-model="rec_list[key].credit_bonds" type="checkbox" ng-disabled="data.transactionType != 'INV' || !data.bonds || rec_amount < 0 "  ></td>
					<td>{{data.description}}</td>
					<td class="nums">
						<?php
							$params = array(
								'ng-show' 			=> "rec_list[key].unpaidAmount != rec_list[key].amount",
								'ng-click' 			=> "payHistoryLoadModal(rec_list[key])",
								'title' 			=> "View payment history",
								'style' 			=> "text-decoration: none",
								'htmler-show-modal'	=> "#pay-history-modal"
							);
							linker('<img src="' . ASSET_DOMAIN . 'assets/images/icons/history.png"  class="txt-icon">',"#",$params);
						?>
						<span ng-class="{'txt-bold': rec_list[key].unpaidAmount != rec_list[key].amount}">{{data.unpaidAmount | htmlerFormatAmt}}</span>
					</td>
					<td class="center"><input ng-model="rec_list[key].credit_remaining" type="checkbox" ng-disabled="data.invoiceNumber == '0' || !data.invoiceNumber || data.transactionType != 'INV' || rec_amount < 0"  title="Credit remainder of invoice" ></td>
					<td class="nums" style="width:200px;">
						<?php
							$params = array(
								'ng-show' 			=> "rec_list[key].linkedAps",
								'ng-click' 			=> "apDetailsLoadModal(rec_list[key])",
								'title' 			=> "View linked AP details",
								'style' 			=> "text-decoration: none;display:inline-block;margin-right:5px;",
								'htmler-show-modal'	=> "#ap-details-modal"
							);
							linker('<img src="' . ASSET_DOMAIN . 'assets/images/icons/open.png"  class="txt-icon">',"#",$params);
							$params = array(
								'class'		=> 	'data-tbls-icon',
								'ng-click'	=> 	'clearLnAmount(key)',
								'ng-cloak'	=> 	null,
								'ng-show'	=>  'rec_list[key].line_amount && rec_list[key].line_amount != 0',
								'title'		=> 	'Clear amount'
							);
							linker('<img src="' . ASSET_DOMAIN . 'assets/images/icons/delete.png">',"#",$params);

							$params = array(
										'ng-model'					=> 	'rec_list[key].line_amount',
										'ng-dblclick'				=>	'allLnAmount(key)',
										'ng-disabled'				=>	"(method == 'leaseDebtors' && (data.transactionType == 'CRE' || data.transactionType == 'CSH') && !creditForceEnable) || (rec_amount < 0 && data.transactionType != 'CRE' && data.transactionType != 'CSH' )",
										'ng-blur'					=>	"checkLnAmount(key);formatNumberRpt('rec_list',key,'line_amount')",
										'htmler-number-input'		=> 	null,
										'htmler-select-all-click'	=> 	null,
										'style'						=> 	'text-align:right'
									  );
							textboxer("rec_amounts[{{key}}]","rec_amounts-{{key}}","",$params);
						?>
					</td>
				</tr>
				<tr class="highlight" >
					<td ng-show="withBonds" >&nbsp;</td>
					<td colspan="12" style="text-align: left;font-size: 12px;font-weight: bold">
							Print Receipt <?php checkboxer("","print_receipt","print_receipt","1",false,array("ng-model" => 'print_receipt') ); ?>&nbsp;&nbsp;&nbsp;
							Email Receipt <?php checkboxer("","email_receipt","email_receipt","1",false,array("ng-model" => 'email_receipt','ng-disabled'=>'print_receipt ? false : true') ); ?>
					</td>
					<td style="text-align: right;font-size: 12px;">
						<span style="display: inline-block;font-weight: bold;">
							Total Unpaid {{total_ln_unpaid | htmlerFormatAmt}}
						</span>
					</td>
				</tr>
		</table>
		<div style="width:100%;">
			<table class="data-grid data-tbls" cellpadding="3" cellspacing="0" border="0" width="100%" >
				<!-- Unallocated Amounts form -->
					<tr class="subTitle" ng-show="showUnallocTbl" ng-cloak><td class="fieldTitle" colspan="100%">Credits and Sundry Receipts</td></tr>
					<tr class="fieldDescription" ng-show="showUnallocTbl" ng-cloak>
						<td class="chks">#</td>
						<td >{{ (leaseLabel == "Lease" ? "Property" : "Ledger" ) }}</td>
						<td >{{ leaseLabel }}</td>
						<td ng-if="method == 'ledgerProperties' || method == 'subLedgerProperties'" ></td>
						<td colspan="2">Account</td>
						<td colspan="2">Description</td>
						<td>From</td>
						<td>To</td>
						<td>Tax</td>
						<td class="center">Sundry Receipt</td>
						<td   style="text-align: right">Amount</td>
						<td  colspan="3">&nbsp;</td>
					</tr>
					<tr ng-repeat="(key,data) in unallocList" ng-show="unallocList.length > 0" ng-cloak>
						<td class="chks">{{key+1}}</td>
						<td>{{data.propertyID}}</td>
						<td>{{data.leaseID}}</td>
						<td ng-if="method == 'ledgerProperties' || method == 'subLedgerProperties'" ></td>
						<td colspan="2">{{data.account}}</td>
						<td colspan="2">{{data.description}}</td>
	                    <td>{{data.fromDate}}</td>
	                    <td>{{data.toDate}}</td>
						<td>{{data.taxType}}</td>
						<td class="center" ><input type="checkbox"ng-checked="data.sundry " disabled ></td>
						<td  style="text-align: right">{{data.amount | htmlerFormatAmt}}</td>
						<td  colspan="2">
							<?php
								$params = array(
									'class'		=> 	'data0.-tbls-icon',
									'ng-click'	=> 	'removeUnalloc(key)',
									'title'		=> 	'Remove unallocated line'
								);
								linker('<img src="' . ASSET_DOMAIN . 'assets/images/icons/delete.png">',"#",$params);
							?>
						</td>
					</tr>
					<tr ng-show="showUnallocTbl" ng-cloak>
						<td class="chks">&nbsp;</td>
						<td class="chosenSelect" >
							<?php
								$params = array(
									"ng-model" 		=> 'unalloc_new_propertyID',
									"data-chosen" 	=> null,
									"ng-disabled" 	=> "method != 'leaseDebtors'",
									'ng-change' 	=> "matchUnallocPropLease('property')",
									"ng-options" 	=> 'dd.value as dd.label for dd in propertiesSrch',
									"style" 		=> 'width:140px;',
								); 
								selecter('new_unalloc_property_id','new_unalloc_property_id',array(),"",$params);
							?>
						</td>
						<td class="chosenSelect">
							<?php
								$params = array(
									"ng-model" 		=> 'unalloc_new_leaseID',
									"data-chosen" 	=> null,
									"ng-disabled" 	=> "method != 'leaseDebtors'",
									"ng-options" 	=> 'dd.value as dd.label for dd in leasesSrch',
									'ng-change' 	=> "matchUnallocPropLease('lease')",
									"style" 		=> 'width:140px;',
								); 
								selecter('new_unalloc_lease_id','new_unalloc_lease_id',array(),"",$params);
							?>
						</td>
						<td ng-if="method == 'ledgerProperties' || method == 'subLedgerProperties'"  >
							<?php					
									$params = array(
										'ng-click'	=> 	'allocateOldest()',
										'title'		=> 	'Add Sub-Ledger',
										'htmler-show-modal'	=> "#sub-Ledger-details-modal",
										);
									linker('<img src="' . ASSET_DOMAIN . 'assets/images/icons/add.png">',"#",$params);
							?>
						</td>
						<td colspan="2" class="chosenSelect">
							<?php
								$params = array(
									"ng-model" 		=> 'unalloc_new_account',
									"data-chosen" 	=> null,
									"style" 		=> 'width:140px;',
									"ng-options" 	=> 'dd.value as dd.label group by dd.category for dd in newUnllocAccuntList',
								); 
								selecter('unalloc_new_account','unalloc_new_account',array(),"",$params);
							?>
						</td>
						<td colspan="2"><?php textboxer("unalloc_new_description","unalloc_new_description","",array('maxlength'=>90,'ng-model'=>'unalloc_new_description','style'=>'width:160px;')); ?></td>
						<td><?php dater("unalloc_date_from","unalloc_date_from","",array('ng-model'=>'unalloc_date_from'));?></td>
						<td><?php dater("unalloc_date_to","unalloc_date_to","",array('ng-model'=>'unalloc_date_to'));?></td>
						<td class="chosenSelect">
							<?php
								$params = array(
									"ng-model" 		=> 'unalloc_new_tax',
									"data-chosen" 	=> null,
									"style" 		=> 'width:120px;',
									"ng-options" 	=> 'dd.value as dd.label for dd in taxRatesList',
								); 
								selecter('unalloc_new_tax','unalloc_new_tax',array(),"",$params);
							?>
						</td>
						<td class="center" ><input type="checkbox" ng-model="unalloc_new_sundry"  title="Receipt sundry amount"  ></td>
						<td style="text-align: left" colspan="{{ method == 'ledgerProperties' || method == 'subLedgerProperties' ? 6 : 5 }}" >
							<?php
								$params = array('ng-model'=>'unalloc_new_amount',
												"htmler-select-all-click" 	=> 	null,
												"htmler-number-input" 		=> 	null,
												'ng-dblclick' 				=>  'unallocateFill()',
												'style'						=>	'text-align:right;width:80px;'); 
								textboxer("unalloc_new_amount","unalloc_new_amount","",$params); 
							?>
							<?php buttoner("Add","add-unalloc-btn",array('ng-click'=>'addUnalloc()') ); ?>
						</td>
					</tr>
			</table>
		</div>		
		<table class="data-grid data-tbls" cellpadding="3" cellspacing="0" border="0">
			<tr class="footer" ng-show="(!file_trans_key || !file_trans_list[file_trans_key]) || (file_trans_key && file_trans_list[file_trans_key] && !file_trans_list[file_trans_key].edit)" ng-cloak>
				<td colspan="100%">
					<?php				
						buttoner("Add Credit / Sundry Receipts","show-unalloc-btn",array("ng-show"=>"!showUnallocTbl","ng-cloak"=>null,"ng-click"=>"showAddNewUnallocRow()","style"=>"float:left;",'ng-disabled'	=>	"(rec_amount < 0 && data.transactionType != 'CRE' )"));
						buttoner("Remove All Credits / Sundry Receipts","show-unalloc-btn",array("ng-show"=>"showUnallocTbl","ng-cloak"=>null,"ng-click"=>"removeAllUnalloc()","style"=>"float:left;"));
						
						buttoner("Skip and go-to Next","skip-next-btn",array("ng-click"=>"skipNextBankReceipt()","ng-show"=>"file_trans_key != null && getNextBankReceiptLine()","ng-cloak"=>null,"style"=>"float:right;margin-right: 5px;"));
						buttoner("Add as Adjustment and go-to Next","process-next-btn",array("ng-click"=>"fileTransAdjustmentAsk(file_trans_key)","ng-show"=>"file_trans_key != null && getNextBankReceiptLine()","ng-cloak"=>null,"style"=>"float:right;margin-right: 5px;"));
						buttoner("Process Receipt and go-to Next","process-next-btn",array("ng-click"=>"submitForm(true)","ng-show"=>"file_trans_key != null && getNextBankReceiptLine()","ng-cloak"=>null,"style"=>"float:right;margin-right: 5px;"));
						buttoner("Add as Adjustment","process-next-btn",array("ng-click"=>"fileTransAdjustmentAsk(file_trans_key)","ng-show"=>"file_trans_key != null && !getNextBankReceiptLine()","ng-cloak"=>null,"style"=>"float:right;margin-right: 5px;"));
						buttoner("Process Receipt","process-btn",array("ng-click"=>"submitForm()","style"=>"float:right;margin-right: 5px;"));
					?>
				</td>
			</tr>
		</table>
	<div>
		<img src="<?=ASSET_DOMAIN?>assets/images/icons/accept.png" class="floating-submit" ng-click="submitForm()" title="Process Receipt" ng-show="(!file_trans_key || !file_trans_list[file_trans_key]) || (file_trans_key && file_trans_list[file_trans_key] && !file_trans_list[file_trans_key].edit)" ng-cloak>
	</div>
</div>
<div style="margin-bottom: 20%;"></div>
<div class="htmler-modal-window small" id="input-cheque-modal">
	<div class="htmler-modal-container">
		<div class="head">
			Input Cheque Number
			<?php linker("&times;","#",array('htmler-close-modal'=>'#input-cheque-modal','class'=>'close')) ?>

		</div>
		<div class=" no-padding">
			<table>
				<tr>
					<td align="center" ><?php textboxer("cheque_number","cheque_number","",array("ng-model"=>"cheque_number","htmler-select-all-click" 	=> 	null, "htmler-number-input" => null,'maxlength'=>10));?></td>
					<td align="center" ><?php buttoner("Process Refund","process-btn",array("ng-click"=>"submitForm()","style"=>"")); ?></td>
				</tr>
				<tr>
					<td colspan="2" style="font-size: 12px;">
						Note: The next cheque number is {{ chequeNo }}.
					</td>
				</tr>
			</table>
		</div>
	</div>
</div>
<div class="htmler-modal-window small" id="input-prn-modal">
	<div class="htmler-modal-container">
		<div class="head">
			Input Payment Reference Number
			<?php linker("&times;","#",array('htmler-close-modal'=>'#input-prn-modal','class'=>'close')) ?>

		</div>
		<div class=" no-padding">
			<table>
				<tr>
					<td align="center" ><?php textboxer("prn_number","prn_number","",array("ng-model"=>"prn_number",'maxlength'=>20));?></td>
					<td align="center" ><?php buttoner("Process Refund","process-btn",array("ng-click"=>"submitForm()","style"=>"")); ?></td>
				</tr>
				<tr>
					<td colspan="2" style="font-size: 12px;">
						Note: The next payment reference number is {{ PaymentRefNo }}.
					</td>
				</tr>
			</table>
		</div>
	</div>
</div>
<div class="htmler-modal-window wide" id="pay-history-modal">
	<div class="htmler-modal-container">
		<div class="head">
			Payment History
			<?php linker("&times;","#",array('htmler-close-modal'=>'#pay-history-modal','class'=>'close')) ?>
		</div>
		<div class="body no-padding">
			<table class="data-grid" cellpadding="3" cellspacing="0" border="0">
				<tr>
					<td class="title">Property - Lease</td>
					<td class="required">&nbsp;</td>
					<td colspan="100%">{{payHistory.propertyID}} - {{payHistory.leaseID}}</td>
				</tr>
				<tr>
					<td class="title">Date</td>
					<td class="required">&nbsp;</td>
					<td colspan="100%">{{payHistory.transactionDate}}</td>
				</tr>
				<tr>
					<td class="title">Description</td>
					<td class="required">&nbsp;</td>
					<td colspan="100%">{{payHistory.description}}</td>
				</tr>
				<tr>
					<td class="title">Total Amount</td>
					<td class="required">&nbsp;</td>
					<td colspan="100%"><strong>{{payHistory.totalAmount | htmlerFormatAmt}}</strong></td>
				</tr>
			</table>			
			<table class="data-grid data-tbls" cellpadding="3" cellspacing="0" border="0">
				<thead>
					<tr class="fieldDescription">
						<td class="chks">#</td>
						<td class="txts-date">Trans Date</td>
						<td>Bank ID</td>
						<td>Receipt Number</td>
						<td class="nums">Tax Amount</td>
						<td class="nums">Amount</td>
						<td>Receipted By</td>
						<td>Receipt Date</td>
					</tr>
				</thead>
				<tbody>
					<tr ng-repeat="(key,data) in payHistory.list" >
						<td class="chks">{{key+1}}</td>
						<td class="txts-date">{{data.transDate}}</td>
						<td>{{data.bankID}}</td>
						<td>{{data.receiptNumber}}</td>
						<td class="nums">{{data.taxAmount | htmlerFormatAmt}}</td>
						<td class="nums">{{data.allocAmount | htmlerFormatAmt}}</td>
						<td>{{data.modUser}}</td>
						<td>{{data.modDate}}</td>
					</tr>												
					<tr class="footer" >
						<td colspan="6" style="text-align: right;font-size: 12px;">
							<strong>Total Paid {{payHistory.allocTotal | htmlerFormatAmt}}</strong>
						</td>
						<td colspan="2" style="text-align: right;font-size: 12px;">&nbsp;</td>
					</tr>
				</tbody>			
			</table>
		</div>
	</div>
</div>
<div class="htmler-modal-window wide" id="ap-details-modal" style="top:-30px;">
	<div class="htmler-modal-container">
		<div class="head">
			AP Linked Details
			<?php linker("&times;","#",array('htmler-close-modal'=>'#ap-details-modal','class'=>'close')) ?>
		</div>
		<div class="body no-padding">
			<table class="data-grid" cellpadding="3" cellspacing="0" border="0">
				<tr>
					<td class="title">Property - Lease</td>
					<td class="required">&nbsp;</td>
					<td colspan="100%">{{apDetails.propertyID}} - {{apDetails.leaseID}}</td>
				</tr>
			</table>
			<table class="data-grid data-tbls" cellpadding="3" cellspacing="0" border="0">
				<thead>
					<tr class="fieldDescription">
						<td class="chks">#</td>
						<td class="txts-date">Invoice Date</td>
						<td>Invoice No.</td>
						<td>Description</td>
						<td>Account</td>
						<td>Supplier</td>
						<td class="txts-date">Due Date</td>
						<td class="nums">Net Amount</td>
						<td class="nums">Tax Amount</td>
						<td class="nums">Total Charged</td>
						<td class="nums">Total Paid</td>
						<td class="center">
							<?php 
								$params = array(
									'ng-model' 		=> "apDetailsHoldAll",
									'ng-change' 	=> "apDetailsHoldAllCh()",
									'style'			=> "vertical-align: middle;margin-top: -3px;"
								);
								checkboxer("","apDetails-hold-all","apDetails-hold-all","1",false,$params); 
							?>
							&nbsp; On Hold 
						</td>
					</tr>
				</thead>
				<tbody>
					<tr ng-repeat="(key,data) in apDetails.list" >
						<td class="chks">{{key+1}}</td>
						<td class="txts-date">{{data.transDate}}</td>
						<td>{{data.invoiceNumber}}</td>
						<td>{{data.description}}</td>
						<td>{{data.bankID}}</td>
						<td>{{data.supplierID}}</td>
						<td class="txts-date">{{data.dueDate}}</td>
						<td class="nums">{{data.netAmount | htmlerFormatAmt}}</td>
						<td class="nums">{{data.taxAmount | htmlerFormatAmt}}</td>
						<td class="nums">{{data.total_charged | htmlerFormatAmt}}</td>
						<td class="nums">{{data.total_paid | htmlerFormatAmt}}</td>
						<td class="center">
							<?php 
								$params = array(
									'ng-model' 		=> "apDetails.list[key].hold",
									'ng-checked' 	=> "apDetails.list[key].hold",
									'ng-change' 	=> "apDetailsHoldAllChWatch()",
								);
								checkboxer("","apDetails-hold-{{key}}","apDetails-hold[{{key}}]","1",false,$params); 
							?>
						</td>
					</tr>
				</tbody>			
			</table>			
		</div>
		<div class="foot">
			<?php
				buttoner("Update Details","update-aps-btn",array("ng-model"=>"updateApsBtn","ng-click"=>"updateApDetails()"));
				echo "&nbsp;";
				buttoner("Close & Update Details","update-aps-btn",array('htmler-close-modal'=> "#ap-details-modal","ng-model"=>"updateApsBtn","ng-click"=>"updateApDetails()"));
			?>
		</div>
	</div>
</div>
<div class="htmler-modal-window ultra" id="txn-temp-modal">
	<div class="htmler-modal-container">
		<div class="head">
			Confirm Matched Details
			<?php linker("&times;","#",array('htmler-close-modal'=>'#txn-temp-modal','class'=>'close')) ?>
		</div>
		<!-- <td>Bank Details</td>
		<td>Bank Account</td> -->
		<div class="body no-padding" style="max-height:75vh !important">
			<table class="data-grid data-tbl" cellpadding="3" cellspacing="0" border="0">
				<tr>
					<?php labeler('Transaction Date'); ?><td>{{txnTempTransDate}}</td>
				</tr>
				<tr>
					<?php labeler('Bank Details'); ?><td>{{txnTempBankDetail}}</td>
				</tr>
			</table>
			<table class="data-grid data-tbl" cellpadding="3" cellspacing="0" border="0">
				<tr>
					<?php labeler('Search Statement Description'); ?>
					<td>
						<?php
							$params = array(
								'ng-model'=>'txn_temp_tbl.filters.keyword',
								'ng-keyup'=>'filterTxnTempTbl()'
							); 
							textboxer('sta_desc','sta_desc','',$params);
						?>
						<?php
							$params = array(
								'ng-cloak' 			=> 	null,
								'ng-if' 			=>  'txn_temp_tbl.filters.keyword',
								'ng-click' 			=>  'clearKeywordFilterTxnTempTbl()',
								'title'				=> 	'Clear search keyword',
								'style' 			=>  'display: inline-block;vertical-align: middle;',
							);
							linker('<img src="' . ASSET_DOMAIN . 'assets/images/icons/delete.png">',"#",$params);		
						?>
					</td>
					<td></td>
					<td></td>
				</tr>
			</table>
			<table class="data-grid data-tbls" cellpadding="0" cellspacing="0" border="0">
				<tr class="fieldDescription">
					<td>Bank Statement Description</td>
					<td style="text-align: center;">Debtor Only</td>
					<td>Suggested Leases</td>
					<td>Debtor</td>
					<td>Select a {{ (leaseLabel == "Lease" ? "Property" : "Ledger" ) }}</td>
					<td>Select a {{ leaseLabel }}</td>
					<td>CRN</td>
					<td class="nums">Amount</td>
					<td>&nbsp;</td>
					<td class="chks"><?php checkboxer("","check-all-temp","check-all-temp","1",false,array("ng-model"=>'txnTempChAll',"ng-change"=>'tempTxnFileCheckAll()')); ?></td>
				</tr>
				<?php datableRower('data','txn_temp_tbl',array('ng-show'=>"data.srchShow",'ng-class'=>"{'bg-red' : data.matches.length == 0,'bg-orange': data.matches.length > 1,'bg-grayer': data.deleted}")) ?>
					<td>{{data.trans_desc1}} {{data.trans_desc2}}</td>
					<td style="text-align: center;">
						<?php 
							$params = array(
								'ng-model'		=>	"data.debtorOnly",
								'ng-checked'	=>	"data.debtorOnly",
								'ng-change' 	=>	"fileLineDebtoryOnly(data.line_key)",
								'ng-disabled' 	=>  '!data.edit',
								'tabindex'		=>	"-1"
							);
							checkboxer("","txtTempDebtorOnly","","1",false,$params); 
						?>
					</td>
					<td>
						<div ng-show="data.matches.length > 2">
							<span ng-if="!data.edit" title="{{data.useLeaseName}}">{{data.useLeaseName | htmlerStrRestrict: 25}}</span>
							<?php
								$params = array(
									"ng-model" 		=> 'data.useLeaseID',
									"data-chosen" 	=> null,
									'ng-if' 		=> 'data.edit && !data.debtorOnly',
									"style" 		=> 'width:200px;',
									'ng-change' 	=> "selectSuggestion(data.line_key,data.useLeaseID)",
									"ng-options" 	=> "mt.value as mt.label for mt in data.matches",
								); 
								selecter('txnTempSuggested','txnTempSuggested',array(),"",$params);
							?>
							<!-- "ng-options" 	=> "mt.leaseID as (mt.leaseID+' '+mt.leaseName+' ('+mt.propertyID+')') for mt in data.matches", -->
						</div>
					</td>
					<td>
						<!-- <span ng-if="data.edit || !data.debtorOnly" title="{{data.debtorID+' '+data.debtorName}}">{{data.debtorID+' '+data.debtorName | htmlerStrRestrict: 25}}</span> -->
						<span ng-if="!data.edit" title="{{data.debtorID+' '+data.debtorName}}">{{data.debtorID+' '+data.debtorName | htmlerStrRestrict: 25}}</span>
						<span ng-if="data.edit && !data.debtorOnly" title="{{data.debtorID+' '+data.debtorName}}">{{data.debtorID+' '+data.debtorName | htmlerStrRestrict: 25}}</span>
						<?php
							$params = array(
								"ng-model" 		=> 'data.debtorID',
								"data-chosen" 	=> null,
								'ng-if' 		=> 'data.edit && data.debtorOnly',
								"style" 		=> 'width:200px;',
								"ng-options" 	=> 'dd.value as dd.label for dd in data.debtorDrop',
								'ng-change' 	=> "debtorChangeTempDrop(data.line_key)",
							); 
							selecter('txnTempDebtor','txnTempDebtor',array(),"",$params);
						?>
						<!-- "ng-options" 	=> 'dd.value as dd.label for dd in txnTempDebtorList', -->
					</td>					
					<td>
						<span ng-if="!data.edit" title="{{data.propertyID+' '+data.propertyName}}">{{data.propertyID+' '+data.propertyName | htmlerStrRestrict: 25}}</span>
						<?php
							$params = array(
								"ng-model" 		=> 'data.propertyID',
								"data-chosen" 	=> null,
								'ng-if' 		=> 'data.edit && !data.debtorOnly',
								"style" 		=> 'width:200px;',
								"ng-options" 	=> "dd.value as dd.label for dd in data.propertyDrop",
								'ng-change' 	=> "propertyChangeTempDrop(data.line_key)",
							); 
							selecter('txnTempProperties','txnTempProperties',array(),"",$params);
						?>
						<!-- "ng-options" 	=> "dd.value as dd.label for dd in txnTempPropertiesList | filter: filterTempDropDowns('debtorID',data.debtorID)", -->
					</td>
					<td>
						<span ng-if="!data.edit" title="{{data.leaseID+' '+data.leaseName}}">{{data.leaseID+' '+data.leaseName | htmlerStrRestrict: 25}}</span>
						<?php
							$params = array(
								"ng-model" 		=> 'data.leaseID',
								"data-chosen" 	=> null,
								'ng-if' 		=> 'data.edit && !data.debtorOnly',
								"style" 		=> 'width:200px;',
								"ng-options" 	=> "dd.value as dd.label for dd in data.leaseDrop",
								'ng-change' 	=> "leaseChangeTempDrop(data.line_key)",
							); 
							selecter('txnTempLease','txnTempLease',array(),"",$params);
						?>
						 <!-- | filter: filterTempDropDowns('propertyID',data.propertyID)	 -->
						<!-- "ng-options" 	=> "dd.value as dd.label for dd in txnTempLeasesList |  filter: filterTempDropDowns('propertyID',data.propertyID)", -->
					</td>
					<td>
						<span ng-if="data.crn">{{data.crn.substring(0, data.crn.length - 1)}}</span>
					</td>
					<td class="nums">{{data.trans_amount | htmlerFormatAmt}}</td>
					<td style="text-align: center;">
								<!-- 'ng-click'	=> 	'data.edit = true;data.ch = false',					 -->
						<?php
							$params = array(
								'ng-if' 	=>  '!data.edit',
								'class'		=> 	'data0.-tbls-icon',		
								'ng-click'	=> 	"tempLineconfirm(data.line_key,false)",					
								'title'		=> 	'Edit Match'
							);
							linker('<img src="' . ASSET_DOMAIN . 'assets/images/icons/edit.png">',"#",$params);

							$params = array(
								'ng-if' 	=>  'data.edit',
								'ng-click'	=> 	"tempLineconfirm(data.line_key,true)",					
								'class'		=> 	'data0.-tbls-icon',								
								'title'		=> 	'Confirm Match'
							);
							linker('<img src="' . ASSET_DOMAIN . 'assets/images/icons/accept.png">',"#",$params);
						?>
								<!-- 'ng-click'	=> 	'data.edit = false;data.ch = true',					 -->
					</td>
					<td>
						<?php 
							$params = array(
											"ng-disabled"	=>	'data.edit',
											"ng-model"		=>	'data.ch',
											"ng-change"		=>	'tempTxnFileCheckWatch()'
										   );
							checkboxer("","ch-temp-{{key}}","ch-temp-{{key}}","1",false,$params); 
						?>
					</td>
				</tr>
			</table>
		</div>
		<div class="foot">
			<div class="datatable-bottom paginater">
		        <?= paginater('txn_temp_tbl') ?>
			</div>	
			<span style="vertical-align: middle">({{txn_temp_ch_ctr}} Checked)</span>&nbsp;
			<?php
				$params = array(
					"ng-click" => "saveFileTempTrans()",
					"style"    => "margin-right: 10px"
				);
				buttoner("Save Checked Lines","save-match-btn",$params);
				$params = array(
					"htmler-close-modal" => '#txn-temp-modal'
				);
				buttoner("Cancel","cancel-match-btn",$params);
			?>
		</div>
	</div>
</div>
<div class="htmler-modal-window" id="ask-to-adj-modal">
	<div class="htmler-modal-container">
		<div class="head">
			File Transaction to Adjustment
			<?php linker("&times;","#",array('htmler-close-modal'=>'#ask-to-adj-modal','class'=>'close')) ?>
		</div>
		<div class="body no-padding" style="min-height: 80px;max-height: 80vh;">		
			<table class="data-grid data-tbls" cellpadding="3" cellspacing="0" border="0">
				<tr>
					<td colspan="100%">
						<p style="text-align: center;margin-top: 15px;margin-bottom: 15px;">
							Skip receipt & add to bank reconciliation adjustments? 
						</p>
					</td>
				</tr>
				<tr>
					<td class="title">Adjustment Date</td>
					<td class="required">*</td>
					<td>
						<?php dater("adj_date","adj_date","",array('ng-model'=>'rec_date'));?>
						<span ng-show="file_trans_list[file_trans_key]" ng-cloak>
							<strong>
								(File Trans Date: {{file_trans_list[file_trans_key].trans_date}})
							</strong>
						</span>
					</td>
				</tr>
			</table>									
		</div>
		<div class="foot">
			<?php
				$params = array(
					"ng-click" => "fileTransAdjustment()",
					"style"    => "margin-right: 10px"
				);
				buttoner("Confirm","save-to-adj-btn",$params);
				$params = array(
					"htmler-close-modal" => '#ask-to-adj-modal'
				);
				buttoner("Cancel","cancel-to-adj-btn",$params);
			?>
		</div>
	</div>
</div>
<div class="htmler-modal-window" id="about-credits-modal">
	<div class="htmler-modal-container">
		<div class="head">
			Warning
			<?php linker("&times;","#",array('htmler-close-modal'=>'#about-credits-modal','class'=>'close')) ?>
		</div>
		<div class="body no-padding" style="min-height: 80px;max-height: 80vh;">
		<p style="text-align: center;margin-top: 25px;">
			Using credit balances while receipting by debtor could overdraw property balances. Do you wish to proceed? 
		</p>							
		</div>
		<div class="foot">
			<?php
				$params = array(
					"ng-click" => "enableLineCredits()",
					"style"    => "margin-right: 10px"
				);
				buttoner("Yes","save-to-adj-btn",$params);
				$params = array(
					"htmler-close-modal" => '#about-credits-modal'
				);
				buttoner("No","cancel-to-adj-btn",$params);
			?>
		</div>
	</div>
</div>
<div class="htmler-modal-window" id="download-txn-modal">
	<div class="htmler-modal-container">
		<div class="head">
			Macquire Bank
			<?php linker("&times;","#",array('htmler-close-modal'=>'#download-txn-modal','class'=>'close')) ?>
		</div>
		<div class="body no-padding" style="min-height: 150px;max-height: 80vh;">
			<table class="data-grid" cellpadding="3" cellspacing="0" border="0">
							
				<!-- <tr>
					<td class="title">Select Bank account</td>
					<td class="required">&nbsp;</td>
					<td colspan="100%">
						<?php
							$params = array(
								"ng-model" 		=> 'txn_file_bank',
								"data-chosen" 	=> null,
								"ng-options" 	=> 'dd.value as dd.label for dd in bankAccounts',
								"style" 		=> 'width:300px;',
							); 
							selecter('bank_acct_id','bank_acct_id',array(),"",$params);
						?>
					</td>
				</tr> -->
			</table>					
		</div>
		<div class="foot">
			<?php
				$params = array(
					"ng-click" => "downloadTxnFile()"
				);
				buttoner("Download","go-download-txn-btn",$params);
			?>
		</div>
	</div>
</div>
<div class="htmler-modal-window" id="vacated-warning-modal">
	<div class="htmler-modal-container">
		<div class="head">
			Warning
			<?php linker("&times;","#",array('htmler-close-modal'=>'#vacated-warning-modal','class'=>'close')) ?>
		</div>
		<div class="body no-padding" style="min-height: 80px;max-height: 80vh;">
		<p style="text-align: left;margin-top: 25px;margin-left: 13px;">
			You are receipting to a vacated lease. Do you want to continue?
		</p>							
		</div>
		<div class="foot">
			<?php
				$params = array(
					"ng-click" => "submitForm(vacatedsNextFile)",
					"style"    => "margin-right: 10px"
				);
				buttoner("Yes","continue-process-btn",$params);
				$params = array(
					"htmler-close-modal" => '#vacated-warning-modal'
				);
				buttoner("No","cancel-process-btn",$params);
			?>
		</div>
	</div>
</div>
<div class="htmler-modal-window" id="future-warning-modal">
	<div class="htmler-modal-container">
		<div class="head">
			Warning
			<?php linker("&times;","#",array('htmler-close-modal'=>'#future-warning-modal','class'=>'close')) ?>
		</div>
		<div class="body no-padding" style="min-height: 80px;max-height: 80vh;">
		<p style="text-align: left;margin-top: 25px;margin-left: 13px;">
			Under normal circumstances, receipts should not be future dated. Are you sure you want to receipt on a future date?
		</p>
		</div>
		<div class="foot">
			<?php
				$params = array(
					"ng-click" => "submitForm(vacatedsNextFile)",
					"style"    => "margin-right: 10px"
				);
				buttoner("Yes","continue-process-btn",$params);
				$params = array(
					"htmler-close-modal" => '#future-warning-modal'
				);
				buttoner("No","cancel-process-btn",$params);
			?>
		</div>
	</div>
</div>
<div class="htmler-modal-window wide" id="tenant-act-modal">
	<div class="htmler-modal-container">
		<div class="head">
			Tenant Activity
			<?php linker("&times;","#",array('htmler-close-modal'=>'#tenant-act-modal','class'=>'close')) ?>
		</div>
		<div class="body no-padding" style="max-height:75vh !important">
			<table class="data-grid data-tbl" cellpadding="3" cellspacing="0" border="0">
				<tr><?php labeler('Property'); ?><td>{{tenant_act_tbl.details.property_code}} - {{tenant_act_tbl.details.property_name}}</td></tr>
				<tr><?php labeler('Lease'); ?><td>{{tenant_act_tbl.details.lease_code}} - {{tenant_act_tbl.details.lease_name}}</td></tr>
				<tr><?php labeler('Reporting Period'); ?><td>
					<?php 
						dater("tenant_act_from","tenant_act_from","",array('ng-model'=>'tenant_act_tbl.from','ng-blur'=>'viewFileTransTenantAct()'),false,true,"blur");
					?>
					<span>&nbsp; to &nbsp;</span>
					<?php 
						dater("tenant_act_to","tenant_act_to","",array('ng-model'=>'tenant_act_tbl.to','ng-blur'=>'viewFileTransTenantAct()'),false,true,"blur");
					?>
				</td></tr>
			</table>
			<table class="data-grid data-tbls" cellpadding="0" cellspacing="0" border="0">
				<tr class="subTitle">
					<td colspan="5">&nbsp;</td>
					<td colspan="2" style="text-align: left">Date Range</td>
					<td colspan="2" style="text-align: right">Charges</td>
					<td class="nums">&nbsp;</td>
					<td colspan="2" style="text-align: right">Receipt</td>
					<td class="nums">&nbsp;</td>
					<td class="nums">&nbsp;</td>
					<td class="nums">&nbsp;</td>
				</tr>
				<tr class="fieldDescription">
					<td class="date">Trans Date</td>
					<td>Account Code</td>
					<td>Description</td>
					<td>Type</td>
					<td>INV/REC #</td>
					<td class="date">From</td>
					<td class="date">To</td>
					<td class="nums">Net</td>
					<td class="nums">GST</td>
					<td class="nums">Total Charged</td>
					<td class="nums">Net</td>
					<td class="nums">GST</td>
					<td class="nums">Total Received</td>
					<td class="nums">Sub-Total</td>
					<td class="nums">Balance</td>
				</tr>
				<tr class="highlight">
					<td >Opening Balance</td>
					<td colspan="14" class="nums">{{tenant_act_tbl.opening | htmlerFormatAmt}}</td>
				</tr>
				<tr ng-repeat="(key,data) in tenant_act_tbl.datas">
					<td class="date">{{data.transactionDate}}</td>
					<td>{{data.accountCode}}</td>
					<td>
						<span title="{{data.description}}" ng-cloak>
							{{data.description | htmlerStrRestrict: 30}}
						</span>
					</td>
					<td>{{data.transactionType}}</td>
					<td>{{data.invoiceNumber}}</td>
					<td class="date">{{data.fromDate}}</td>
					<td class="date">{{data.toDate}}</td>
					<td class="nums">
						<span ng-if="data.chgNet != 0">{{data.chgNet | htmlerFormatAmt}}</span>
					</td>
					<td class="nums">
						<span ng-if="data.chgGST != 0">{{data.chgGST | htmlerFormatAmt}}</span>
					</td>
					<td class="nums">
						<span ng-if="data.chgGross != 0">{{data.chgGross | htmlerFormatAmt}}</span>
					</td>
					<td class="nums">
						<span ng-if="data.recNet != 0">{{data.recNet | htmlerFormatAmt}}</span>
					</td>
					<td class="nums">
						<span ng-if="data.recGST != 0">{{data.recGST | htmlerFormatAmt}}</span>
					</td>
					<td class="nums">
						<span ng-if="data.recGross != 0">{{data.recGross | htmlerFormatAmt}}</span>
					</td>
					<td class="nums">
						<span ng-if="data.subtotal">{{data.subtotal | htmlerFormatAmt}}</span>
					</td>
					<td class="nums">
						<span>{{data.balance | htmlerFormatAmt}}</span>
					</td>
				</tr>
				<tr class="highlight">
					<td >Closing Balance</td>
					<td colspan="14" class="nums">{{tenant_act_tbl.closing | htmlerFormatAmt}}</td>
				</tr>
			</table>
		</div>
	</div>
</div>

<div class="htmler-modal-window" id="sub-Ledger-details-modal">
	<div class="htmler-modal-container">
		<div class="head">
			Add Sub-Ledger
			<?php linker("&times;","#",array('htmler-close-modal'=>'#sub-Ledger-details-modal','class'=>'close')) ?>
		</div>
		<div class="body no-padding" style="min-height:400px;overflow:auto;">
			<table class="data-grid data-tbls" cellpadding="3" cellspacing="0" border="0">			
				<tbody>
					<tr>
						<td>
							Code
						</td>
						<td>
							<?php
							textboxer("newSubLedgerCode","newSubLedgerCode",'',['ng-model'=>'newSubLedgerCode','maxlength'=>10]);
							?>
						</td>
					</tr>
					<tr>
						<td>
							Type
						</td>
						<td>
							<?php
							$params = array(
								"ng-model" 		=> 'newSubLedgerType',
								"data-chosen" 	=> null,
								"ng-options" 	=> 'dd.value as dd.label for dd in subLedgerTypeSrch',
								"style" 		=> 'width:180px;',
									);
								selecter('newSubLedgerType','newSubLedgerType',array(),"",$params);
							?>
						</td>
					</tr>
					<tr>
						<td>
							Name
						</td>
						<td>
							<?php
							textboxer("newSubLedgerName","newSubLedgerName",'',['ng-model'=>'newSubLedgerName']);
							?>
						</td>
					</tr>
					<tr>
						<td>
							Company
						</td>
						<td>
							<?php
							$params = array(
								"ng-model" 		=> 'newSubLedgerCompany',
								"data-chosen" 	=> null,
								"ng-options" 	=> 'dd.value as dd.label for dd in companiesSrch',
								"style" 		=> 'width:180px;',
							);
							selecter('newSubLedgerCompany','newSubLedgerCompany',array(),"",$params);
							?>
						</td>
					</tr>
				</tbody>			
			</table>			
		</div>
		<div class="foot">
			<?php
				buttoner("Save","update-ledger-btn",array("ng-click"=>"saveSubLedger()"));
			?>
		</div>
	</div>
</div>

<div class="htmler-modal-window update-notes-modal" id="receipting-note-modal">
	<div class="htmler-modal-container modal-content">
		<div class="head modal-header">
			Update Receipting Note
			<?php linker("&times;","#",array('htmler-close-modal'=>'#receipting-note-modal','class'=>'close')) ?>
		</div>
		<div class="no-padding modal-body">
			<div class="note-header">
				<span><strong><span class="note-timestamp">{{receiptingNote.timestamp}}</span> by <span class="note-user">{{receiptingNote.user}}</span></strong></span>
			</div>

			<div class="note-body-wrapper">
				<textarea class="noteTextArea note-body" ng-model="receiptingNote.desc" name="note-content", wrap="hard" style="min-height:80px;" spellcheck="false" maxlength="7900">{{ receiptingNote.desc }}</textarea>
			</div>
		</div>
		<div class="modal-footer">
			<button id="update-receipting-note-btn" class="update-note-btn" ng-click="updateNote($event)">Update</button>
		</div>
	</div>
</div>

<!--//div class="htmler-modal-window small" id="confirm-delete-note">
	<div class="htmler-modal-container">
		<div class="head">
			&nbsp;
			<?php linker("&times;","#",array('htmler-close-modal'=>'#confirm-delete-note','class'=>'close')) ?>
		</div>
		<div class="no-padding">
			<table>
				<tr>
					<td style="font-size: 12px;">
						Confirm
					</td>
				</tr>
			</table>
		</div>
	</div>
</div//-->

<style>
	.ui.dimmer {
		background-color: rgba(33, 33, 33, 0.5);
	}

	.ui-dialog-titlebar-close {
		text-indent: 1px !important;
	}

	.ui-dialog-buttonset .ui-button {
		padding-top: 0.55em;
	}

	button.ui-dialog-titlebar-close.ui-confirm2-close-btn span {
		position: relative;
		top: -2px;
	}

	button.ui-dialog-titlebar-close.ui-confirm2-close-btn, button.ui-dialog-titlebar-close.ui-confirm2-close-btn span {
		font-size: 20px;
		font-weight: bold;
	}
</style>
<?php htmlerEnd(); ?>
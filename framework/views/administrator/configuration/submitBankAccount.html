<script src="<?=ASSET_DOMAIN?>assets/file-uploader/jquery.fileuploader.min.js?<?=JS_VERSION?>" type="text/javascript"></script>
<style>
    .form-important-label {
        color: red;
        font-weight: bold;
    }
</style>

<?if (!$var['success'] AND $var['action'] != 'delete') { ?>
	<? if ($var['action'] == 'add') { ?>
		<h1>New Bank Account</h1>
	<? } else { ?>
		<h1>Modify Bank Account</h1>
	<? } ?>
	<?=renderMessage ($var['statusMessage'], 'class="infoBox"')?>

	<table cellpadding="0" cellspacing="0" class="data-grid">
		<tbody>
			<?=renderErrors ($var['validationErrors'])?>
			<? if ($var['action'] == 'add') { ?>
				<tr class="<?=alternateNextRow ()?>">
					<td class="title2">Bank ID</td>
					<td class="required">*</td>
					<td><?=renderTextBox ('bankID', $var['bankID'], 'maxlength="10" size="7"')?></td>
				</tr>
			<? } else { ?>
				<tr class="<?=alternateNextRow ()?>">
					<td class="title2">Bank ID</td>
					<td class="required">&nbsp;</td>
					<td>
						<?=$var['bankID']?>
						<?=renderHidden ('bankID', $var['bankID'])?>
					</td>
				</tr>
			<? } ?>

			<!--<tr class="<?=alternateNextRow ()?>">-->
				<!--<td class="title2">Name</td>-->
				<!--<td class="required">*</td>-->
				<!--<td><?=renderTextBox ('bankAccountName', $var['bankAccountName'])?></td>-->
			<!--</tr>-->

			<tr class="<?=alternateNextRow ()?>">
				<td class="title2">Name</td>
				<td class="required">*</td>
				<td><?=renderTextBox ('bankAccountName', $var['bankAccountName'])?></td>
			</tr>
			<? if (displayBsbFromSession()) { ?>
			<tr class="<?=alternateNextRow ()?>">
				<td class="title2"><?= bsbLabelFromSession() ?> No</td>
				<td class="required">*</td>
				<td><?=renderTextBox ('bankBSB', $var['bankBSB'], 'maxlength="6" size="7"')?></td>
			</tr>
			<? } ?>
			<tr class="<?=alternateNextRow ()?>">
				<td class="title2">Acc No</td>
				<td class="required">*</td>
				<td><?=renderTextBox ('bankAccount', $var['bankAccount'], 'maxlength="'.cdf_displayLabel('bank_account_length', $var['bankCountry']).'"')?></td>
			</tr>
			<tr class="<?=alternateNextRow ()?>">
				<td class="title2">Biller Code</td>
				<td class="required">&nbsp;</td>
				<td><?=renderTextBox ('bpayBillerCode', $var['bpayBillerCode'], 'maxlength="10" size="7"')?></td>
			</tr>


				<tr class="<?=alternateNextRow ()?>">
					<td class="title2">Bank Name</td>
					<td class="required">*</td>
					<? if ($var['action'] == 'add') { ?>
						<td><?=renderSimpleDropDownList ('bankCode', $var['bankAcountList'], $var['bankCode'], ' onChange="return ajaxContainer(this.id,\'content\',\'?command=submitBankAccount&module=configuration&action=add\')" ')?></td>
					<? } else { ?>
						<td><?=renderSimpleDropDownList ('bankCode', $var['bankAcountList'], $var['bankCode'])?></td>
					<? } ?>
				</tr>



			<tr class="<?=alternateNextRow ()?>">
				<td class="title2">Branch Name</td>
				<td class="required">*</td>
				<td><?=renderTextBox ('bankBranchName', $var['bankBranchName'])?></td>
			</tr>


				<? if ($var['bankCode'] == 'MBL') { ?>
						<tr class="subTitle"><td colspan="3">Macquarie Credentials</td></tr>

						<tr class="highlight">
							<td align="left" colspan="3">Direct Upload</td>
						</tr>

						<tr class="<?=alternateNextRow()?>">
							<td class="title2">Username (MAC)</td>
							<td class="required"></td>
							<td><?=renderTextBox ('directUploadUsername', $var['pmbk_direct_upload_username'])?></td>
						</tr>


						<tr class="<?=alternateNextRow()?>">
								<td class="title2">Password</td>
								<td class="required"></td>
								<td><?=renderPasswordBox ('directUploadPassword', $var['pmbk_direct_upload_password'], 'autocomplete="new-password"')?></td>
                        </tr>

<!--                        <tr class="--><?//=alternateNextRow()?><!--">-->
<!--							<td class="title2">BPAY ClientID</td>-->
<!--							<td class="required"></td>-->
<!--							<td>--><?//=renderTextBox ('bpayClientID', $var['pmbk_bpay_client_id'])?><!--</td>-->
<!--						</tr>-->
<!---->
<!---->
<!--						<tr class="--><?//=alternateNextRow()?><!--">-->
<!--								<td class="title2">BPAY Password</td>-->
<!--								<td class="required"></td>-->
<!--								<td>--><?//=renderPasswordBoxWithPlaceHolder ('bpayPassword', $var['pmbk_bpay_password'] ? '': '')?><!--</td>-->
<!--                        </tr>-->


<!--						<tr class="--><?//=alternateNextRow()?><!--">-->
<!--							<td class="title2">EFT ClientID</td>-->
<!--							<td class="required"></td>-->
<!--							<td>--><?//=renderTextBox ('eftClientID', $var['pmbk_eft_client_id'])?><!--</td>-->
<!--						</tr>-->


<!--						<tr class="--><?//=alternateNextRow()?><!--">-->
<!--							<td class="title2">EFT Password</td>-->
<!--							<td class="required"></td>-->
<!--							<td>--><?//=renderPasswordBoxWithPlaceHolder ('eftPassword', $var['pmbk_eft_password'] ? '' : '')?><!--</td>-->
<!--						</tr>-->

						<tr class="highlight">
							<td align="left" colspan="3">Direct Download</td>
						</tr>
						<tr class="<?=alternateNextRow()?>">
							<td class="title2">Username (MAC)</td>
							<td class="required"></td>
							<td><?=renderTextBox ('downloadStatementClientID', $var['pmbk_download_statement_client_id'])?></td>
						</tr>

						<? if($var['action'] == 'add') { ?>
							<tr class="<?=alternateNextRow()?>">
								<td class="title2">Password</td>
								<td class="required"></td>
								<td><?=renderPasswordBox ('downloadStatementPassword',  $var['pmbk_download_statement_client_id'])?></td>
							</tr>
						<? } else { ?>
							<tr class="<?=alternateNextRow()?>">
								<td class="title2">Password</td>
								<td class="required"></td>
								<td><?=renderPasswordBoxWithPlaceHolder ('downloadStatementPassword', $var['downloadStatementPassword'] ? '' : '')?></td>
							</tr>
						<? } ?>

						<tr class="<?=alternateNextRow()?>">
							<td class="title2">Client / Customer Number</td>
							<td class="required"></td>
							<td><?=renderTextBox ('downloadStatementClientNumber', $var['pmbk_download_statement_client_number'])?></td>
						</tr>
				<? } //end of if ?>


			<tr class="subTitle"><td colspan="3">Location</td></tr>
			<tr class="<?=alternateNextRow ()?>">
				<td class="title2">Street</td>
				<td class="required">*</td>
				<td><?=renderTextBox ('bankStreet', $var['bankStreet'])?></td>
			</tr>
			<tr class="<?=alternateNextRow ()?>">
				<td class="title2"><?=ucwords(strtolower($_SESSION['country_default']['suburb']))?></td>
				<td class="required">*</td>
				<td><?=renderTextBox ('bankCity', $var['bankCity'])?></td>
			</tr>
			<? if (cdf_isShown('display_state', $var['bankCountry'])) { ?>
			<tr class="<?=alternateNextRow ()?>">
				<td class="title2">State</td>
				<td class="required">*</td>
				<td><?=renderDropDownList ('bankState', 'stateCode', 'stateName', $var['stateList'], $var['bankState'])?></td>
			</tr>
			<? } ?>
			<tr class="<?=alternateNextRow ()?>">
				<td class="title2">Post Code</td>
				<td class="required">*</td>
				<td><?=renderTextBox ('bankPostCode', $var['bankPostCode'], 'size="10"')?></td>
			</tr>
			<tr class="<?=alternateNextRow ()?>">
				<td class="title2">Country</td>
				<td class="required">*</td>
				<td><?=renderDropDownList ('bankCountry', 'countryCode', 'countryName', $var['countryList'], $var['bankCountry'], 'onChange="return ajaxContainer(this.id,\'content\',\'?action='.$var['action'].'&change=changeCountry&command=submitBankAccount&module=configuration\')"')?></td>
			</tr>
			<tr class="<?=alternateNextRow ()?>">
				<td class="title2">Local Currency</td>
				<td class="required">*</td>
				<td><?=renderSimpleDropDownList ('bankCurrency', $var['currencyList'], $var['bankCurrency'])?></td>
			</tr>
			<tr class="<?=alternateNextRow ()?>">
				<td class="title2">Foreign Currency</td>
				<td class="required">*</td>
				<td><?=renderSimpleDropDownList ('foreignCurrency', $var['currencyList'], $var['foreignCurrency'])?></td>
			</tr>
			<tr class="<?=alternateNextRow ()?>">
				<td class="title2">Tax Code</td>
				<td class="required">&nbsp;</td>
				<td><?=renderDropDownList ('bankTaxCode', 'taxCode', 'taxDescription', $var['bankTaxList'], $var['bankTaxCode'])?></td>
			</tr>
			<tr class="subTitle"><td colspan="3">Receipt</td></tr>
			<tr class="<?=alternateNextRow ()?>">
				<td class="title2">Receipt Type</td>
				<td class="required">*</td>
				<td><?=renderSimpleDropDownList ('receiptType', $var['receiptTypeList'], $var['receiptType'])?></td>
			</tr>
			<tr class="subTitle"><td colspan="3">Direct Banking</td></tr>
			<tr class="<?=alternateNextRow ()?>">
				<td class="title2">Debit Barred</td>
				<td class="required">*</td>
				<td><?=renderRadioGroup ('debitBarred', $var['yesNoOption'], $var['debitBarred']); ?>
					<span class="form-important-label"> Setting debit barred to NO will allow the bank account to be overdrawn. <?=ucwords(strtolower($_SESSION['country_default']['trust_account']))?>s should always have the setting as Yes.</span>
				</td>
			</tr>
			<tr class="<?=alternateNextRow ()?>">
				<td class="title2">DEFT Payment</td>
				<td class="required">*</td>
				<td><?=renderRadioGroup ('deft', $var['yesNoOption'], $var['deft']);?>
				<span class="form-important-label"> Only set DEFT payment to YES if you bank with Macquarie and utilise their DEFT payment services. This results in DEFT details appearing on their tenant invoice.</span>
				</td>
			</tr>

			<tr class="<?=alternateNextRow ()?>">
				<td class="title2">PayWay Payment</td>
				<td class="required">*</td>
				<td><?=renderRadioGroup ('payway', $var['yesNoOption'], $var['payway']);?>
					<span class="form-important-label"> Set PayWay to Yes to enable Westpac PayWay to appear on tenant invoices.</span>
				</td>
			</tr>

			<tr class="<?=alternateNextRow ()?>" id="paywayRow" style='<?=$var['payway'] ? "" : "display: none"?>'>
			<td class="title2">PayWay Biller Code</td>
			<td class="required">*</td>
			<td><?=renderTextBox ('paywayBillerCode', $var['paywayBillerCode'])?></td>
			</tr>

			<tr class="<?=alternateNextRow ()?>">
				<td class="title2">PayID Payment</td>
				<td class="required">*</td>
				<td><?=renderRadioGroup ('payID', $var['yesNoOption'], $var['payID']);?>
					<span class="form-important-label"> Set PayID to Yes to enable PayID to appear on tenant invoices.</span>
				</td>
			</tr>

			<tr class="<?=alternateNextRow ()?>" id="payIDRow" style='<?=$var['payID'] ? "" : "display: none"?>'>
			<td class="title2">PayID Reference</td>
			<td class="required">*</td>
			<td><?=renderTextBox ('payIDCode', $var['payIDCode'])?></td>
			</tr>

			<tr class="<?=alternateNextRow ()?>">
				<td class="title2">Credit Card Payment</td>
				<td class="required">*</td>
				<td><?=renderRadioGroup ('creditCard', $var['yesNoOption'], $var['creditCard']);?>
					<span class="form-important-label"> Set the Credit Card option to yes to enable credit card payments details to appear on tenant invoices. You will also need to specify a link to your provider. </span>
				</td>
			</tr>

			<tr class="<?=alternateNextRow ()?>" id="providerRow" style='<?=$var['creditCard'] ? "" : "display: none"?>'>
				<td class="title2">Provider</td>
				<td class="required">*</td>
				<td><?=renderTextBox ('provider', $var['provider'])?></td>
			</tr>

			<tr class="<?=alternateNextRow ()?>">
				<td class="title2">Direct Debit</td>
				<td class="required">*</td>
				<td><?=renderRadioGroup ('directDeposit', $var['yesNoOption'], $var['directDeposit']);?>
				<span class="form-important-label"> Only set direct debit to YES if you collect payments from tenants by direct debit. This will be noted on the tenant invoice that they do not need to pay themselves as the amount will be direct debited. This will allow direct debit files to be created.</span>
				</td>
			</tr>
			<tr class="<?=alternateNextRow ()?>">
				<td class="title2">Show Owner Company on Receipt</td>
				<td class="required">*</td>
				<td><?=renderRadioGroup ('showOwnerCompanyOnReceipt', $var['yesNoOption'], $var['showOwnerCompanyOnReceipt']); ?>
					<span class="form-important-label"> </span>
				</td>
			</tr>
            <tr class="<?=alternateNextRow ()?>">
				<td class="title2">Show EFT information on invoice</td>
				<td class="required">*</td>
				<td><?=renderRadioGroup ('showEftInfo', $var['yesNoOption'], $var['showEftInfo']);?>
				</td>
			</tr>

			<tr class="<?=alternateNextRow ()?>">
				<td class="title2">EFT Reference</td>
				<td class="required">*</td>
				<td><?=renderRadioGroup ('eftReference', $var['eftReferenceList'], $var['eftReference']);?>
				</td>
			</tr>

			<tr class="<?=alternateNextRow ()?>">
				<td class="title2">Include Property code in Invoice Reference</td>
				<td class="required">*</td>
				<td><?=renderRadioGroup ('showPropertyCode', $var['yesNoOption'], $var['showPropertyCode']);?>
				</td>
			</tr>
			<tr class="<?=alternateNextRow ()?>">
				<td class="title2">Show Owner Details on Invoice</td>
				<td class="required">*</td>
				<td><?=renderRadioGroup ('showOwnerDetail', $var['yesNoOption'], $var['showOwnerDetail']);?>
				</td>
			</tr>
			<tr class="<?=alternateNextRow ()?>">
				<td class="title2">Show Property Details on Invoice</td>
				<td class="required">*</td>
				<td><?=renderRadioGroup ('showPropertyDetail', $var['yesNoOption'], $var['showPropertyDetail']);?>
				</td>
			</tr>
			<tr class="<?=alternateNextRow ()?>">
				<td class="title2">Show due date in invoice line items</td>
				<td class="required">*</td>
				<td><?=renderRadioGroup ('showDueDate', $var['yesNoOption'], $var['showDueDate']);?>
				</td>
			</tr>
			<tr class="<?=alternateNextRow ()?>">
				<td class="title2">Victoria Owner Corp Invoice Format</td>
				<td class="required">*</td>
				<td><?=renderRadioGroup ('victoriaOwnerCorp', $var['yesNoOption'], $var['victoriaOwnerCorp']);?>
				</td>
			</tr>
			<tr class="<?=alternateNextRow ()?>">
				<td class="title2">Terms / Notice for <?=ucwords(strtolower($_SESSION['country_default']['strata']))?> Invoices</td>
				<td class="required">*</td>
				<td><?=renderRadioGroup ('termNoticeStrata', $var['yesNoOption'], $var['termNoticeStrata']);?>
				</td>
			</tr>

			<tr id="strataInvoiceRow" class="<?=alternateNextRow ()?>" style='<?=$var['termNoticeStrata'] ? "" : "display: none"?>' >
				<td class="title2">Terms / Notice PDF File</td>
				<td class="required"></td>
				<td>
					<input type="file" name="NoticeStrataFiles" id="insFilesEdit">
					<? if($var['NoticeStrataLetter']){?>
					<div id="fileContainer">
					<input type="hidden" name="NoticeStrataLetter" value="<?=encodeParameter($var['NoticeStrataLetter']);?>" >
					<a href="download.php?fileID=<?=encodeParameter($var['NoticeStrataLetter'])?>"><img src="assets/images/icons/pdf.png" alt="Adobe Logo" class="icon" />&nbsp; Download</a>&nbsp;&nbsp;
					 <?=renderButton ('btnBankAccount', '<img width=15 src="assets/images/icons/delete_v2.png">', 'onClick="deleteFile();"')?>
					</div>
					<? } ?>
				</td>
			</tr>

			<tr class="<?=alternateNextRow ()?>">
				<td class="title2">Payment User ID</td>
				<td class="required">&nbsp;</td>
				<td><?=renderTextBox ('bankUserID', $var['bankUserID'])?></td>
			</tr>

			<tr class="<?=alternateNextRow ()?>">
				<td class="title2">Debit User ID</td>
				<td class="required">&nbsp;</td>
				<td><?=renderTextBox ('debitUserID', $var['debitUserID'])?></td>
			</tr>

			<tr class="<?=alternateNextRow ()?>">
				<td class="title2">NAB Customer BPAY Batch ID</td>
				<td class="required">&nbsp;</td>
				<td><?=renderTextBox ('bpayBatchID', $var['bpayBatchID'], 'maxlength="10"')?>
				<span class="form-important-label"> &nbsp;&nbsp;&nbsp;If you bank with NAB and have batch BPAYs enabled then enter your unique BPAY batch ID in this field. This will allow you to create batch BPAY files for import.</span>
				</td>
			</tr>
			<tr class="<?=alternateNextRow ()?>">
				<td class="title2">Westpac Corporate Username</td>
				<td class="required">&nbsp;</td>
				<td><?=renderTextBox ('wbBankUserName', $var['wbBankUserName'])?></td>
			</tr>
			<tr class="<?=alternateNextRow ()?>">
				<td class="title2">User Name</td>
				<td class="required">&nbsp;</td>
				<td><?=renderTextBox ('bankUserName', $var['bankUserName'])?></td>
			</tr>
			<tr class="<?=alternateNextRow ()?>">
				<td class="title2">Financial Institution</td>
				<td class="required">&nbsp;</td>
				<td><?=renderTextBox ('financialInstitution', $var['financialInstitution'])?></td>
			</tr>
			<tr class="<?=alternateNextRow ()?>">
				<td class="title2">Remittance</td>
				<td class="required">&nbsp;</td>
				<td><?=renderTextBox ('bankRemittance', $var['bankRemittance'])?></td>
			</tr>
			<tr class="<?=alternateNextRow ()?>">
				<td class="title2" nowrap="true">Customer BPAY User/Customer ID</td>
				<td class="required">&nbsp;</td>
				<td><?=renderTextBox ('bpayUserID', $var['bpayUserID'], 'maxlength="10"')?>
				<span class="form-important-label"> &nbsp;&nbsp;&nbsp;If you are registered with BPAY payments system and have a BPAY biller code, then inserting the number here will cause BPAY details to be displayed on tenant invoices and allow tenants to pay by BPAY. </span>
				</td>
			</tr>
			<tr class="highlight">
				<td align="right" colspan="3"><?=renderButton ('btnBankAccount', ($var['action'] == 'add') ? 'Submit' : 'Save', 'onClick="updateInsurance();"')?></td>
			</tr>
		</tbody>
	</table>
<? } ?>

<script type="text/javascript" language="javascript">
$(function()
{
	$("#creditCard").change(function()
	{
	   	if($("[name='creditCard']:checked").val() == 1 )
	   	    $("#providerRow").css("display","");
	   	else
		{
            $("#providerRow").css("display","none");
            $("[name='provider']").val('');
		}

	});

    $("#payway").change(function()
    {
        if($("[name='payway']:checked").val() == 1 )
            $("#paywayRow").css("display","");
        else
        {
            $("#paywayRow").css("display","none");
            $("[name='paywayBillerCode']").val('');
        }

    });

    $("#payID").change(function()
    {
        if($("[name='payID']:checked").val() == 1 )
            $("#payIDRow").css("display","");
        else
        {
            $("#payIDRow").css("display","none");
            $("[name='payIDCode']").val('');
        }

    });

	$("#termNoticeStrata").change(function()
	{
		if($("[name='termNoticeStrata']:checked").val() == 1 )
			$("#strataInvoiceRow").css("display","");
		else
		{
			$("#strataInvoiceRow").css("display","none");
		}

	});

	$('input[name="NoticeStrataFiles"]').fileuploader({
		extensions:  ['pdf'],
		theme: 'onebutton'
	});

    $('table').on("click","button#btnBankAccount", function() {
        //---Script to disable submit after first submission
        $('button[name="btnBankAccount"]').prop('disabled', true);

        setTimeout(function() {
            $('button[name="btnBankAccount"]').prop('disabled', false);
        }, 1000);
    });

});
function deleteFile() {
	$("#content").append("<input type='hidden' name='deleteFile' value=1 />");
	$("#fileContainer").remove();
}
function updateInsurance () {

		var formData = new FormData();
		var fileInput = document.getElementById('insFilesEdit');
		if(fileInput){
			var file = fileInput.files[0];
			formData.append('NoticeStrataFiles', file);
		}

		var obj = getElementsByTagNames ('input,select,textarea', document.getElementById("content"));
		for (var i=0; i < obj.length; i++)
		{

			var element = obj[i];

			// if the element is a radio button or checkbox, it's selected if the 'checked' attribute is set to true
			if ((element.type == 'radio') || (element.type == 'checkbox'))
			{
				if (element.checked) formData.append(element.name , element.value);
				// if the multiple attribute of the element is true (valid for multiple line select boxes) ...
			}
			else if (element.multiple)
			{
				var children = element.getElementsByTagName ('option');

				// grab the children, check if they are 'selected' and build a concatenated string of values
				if (children)
				{
					var count = 0;
					var output = '';
					for (var j=0; j<children.length; j++)
					{
						var child = children[j];
						if (child.selected)
						{
							if (count == 0) { output = child.value; }
							else { output += `::${  child.value}`; }
							count++;
						}
					}
					if (output != '') formData.append(element.name , output);
				}

				//-- otherwise check if a value exists for the field (is not empty) and if so, add it to the hash
			}
			else if (element.value != '') formData.append(element.name , element.value);
		}


		// $('#loading').css('display','block');
		$.ajax({
			type: "POST",
			processData: false,
			contentType: false,
			data: formData,
			url: "index.php?action=<?= $var['action'];?>&module=configuration&command=submitBankAccount&bankID=<?= $var['bankID'];?>&do=process",
			success: function(result) {

				$('#content').html(result);
				ajaxCallback("content");
			},
		});

}
function confirmDebitBarred(radio) {

    var ask = confirm("IMPORTANT – setting debit barred to No will allow the bank account to be overdrawn. <?=ucwords(strtolower($_SESSION['country_default']['trust_account']))?>s should always have the setting as Yes. Click OK to continue with the change.");

	var labels = document.getElementsByTagName('LABEL');
		for (var i = 0; i < labels.length; i++) {
			if (labels[i].htmlFor != '') {
				 var elem = document.getElementById(labels[i].htmlFor);
				 if (elem)
					elem.label = labels[i];
			}
		}

    if (ask) {
        //alert("You clicked ok"+radio.value);
    }
    else {
		//radio.label.aria-pressed = false;
		if (radio.id == 'debitBarred_1') //yes
		{
			eval('debitBarred_1.label.setAttribute("class","ui-button ui-widget ui-state-default ui-button-text-only ui-corner-left")');
			eval('debitBarred_0.label.setAttribute("class","ui-state-active ui-button ui-widget ui-state-default ui-button-text-only ui-corner-right")');
		}
		else if (radio.id == 'debitBarred_0') //no
		{
			//alert('no');
			eval('debitBarred_1.label.setAttribute("class","ui-button ui-widget ui-state-default ui-button-text-only ui-corner-left ui-state-active")');
			eval('debitBarred_0.label.setAttribute("class","ui-button ui-widget ui-state-default ui-button-text-only ui-corner-right")');
		}

        return false;
    }
}
</script>
<link href="<?=ASSET_DOMAIN?>assets/sweetalert2_9/sweetalert2.min.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="<?=ASSET_DOMAIN?>assets/sweetalert2_9/sweetalert2.min.js"></script>
<?php if ($var['action'] == 'previewEmail') { ?>
<p><strong><?= $var['letterEmailSubject'] ?></strong></p>
<hr/>
<p><?= $var['letterEmailBody'] ?></p>
<? } elseif ($var['action'] == 'previewDocument') { ?>
<?= $var['letterTemplateBody'] ?>
<? } else {

    if (!$var['viewOnly']) { ?>
<h1>Communication History</h1>
<p>List of letters sent through the system via e-mail using the templates.</p>
<? } ?>
<?= renderMessage($var['statusMessage'], 'class="infoBox"') ?>
<?= renderErrors($var['validationErrors']) ?>

<script type="text/javascript">
    $(document).ready(function () {
        $('.lcDownloadDocument').click(function () {
            ajaxDownload(null, 'content', '?module=<?=$var['module']?>&command=<?=$var['command']?>&letterHistoryID=' + this.id + '&action=downloadDocument');
        });
        $('.lcPreviewEmail').click(function () {
            modalDialog('?module=<?=$var['module']?>&command=<?=$var['command']?>&letterHistoryID=' + this.id + '&action=previewEmail', {
                autoOpen: true,
                modal: true,
                title: 'E-mail Content',
                height: 'auto',
                width: 'auto'
            })
        });
        $('.lcPreviewDocument').click(function () {
            modalDialog('?module=<?=$var['module']?>&command=<?=$var['command']?>&letterHistoryID=' + this.id + '&action=previewDocument', {
                autoOpen: true,
                modal: true,
                title: 'Letter Content',
                height: 'auto',
                width: 'auto'
            })
        });
        $('.lcResendEmail').click(function () {
            ajaxContainer(null,'content','?module=<?=$var['module']?>&command=<?=$var['command']?>&letterHistoryID=' + this.id + '&action=resendEmail');
        });
    });
    $('.spanToolTip').tooltip({
        content: function () {
            return $(this).attr('title').replace(/\;/g, '<br />');
        },
        items: 'span',
        track: true
    });
</script>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/datatables.net-dt/2.1.7/css/dataTables.dataTables.min.css" integrity="sha512-JbyOZyqfBvhWNzVXZy2zUX9Hhp8+JGL15feGcRq1JnS1ZIxEdECKSqT+eLuZ8BvvzGHkxrBdu+EuJLdHk+kQ8g==" crossorigin="anonymous" referrerpolicy="no-referrer" />
<script src="https://cdnjs.cloudflare.com/ajax/libs/datatables.net/2.1.7/dataTables.min.js" integrity="sha512-uQwfH1NYeXU6Whr3PrKxExRgtnfVkcs30HKaAQtReHSVwQv040Spq22cZo1MaQZshLDTuIncxqWJfIVXyr/GSQ==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/datatables-plugins/2.1.7/sorting/date-euro.min.js" integrity="sha512-MVwJ6gT6T7dJorH9794/E0Gf0LHJOvDYXEyvy58++0X4QLM92knwD+xWglp5H3F2aILN33LLZpJKJUzUa5kGUQ==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
<?php
    if (!$var['viewOnly']) {

        ?>

<table class="data-grid tableHive">
    <tr class="<?= alternateNextRow() ?>">
        <td>Category</td>
        <td><?= renderDropDownList('letterCategoryID', 'letterCategoryID', 'letterCategoryName', $var['categoryList'], $var['letterCategoryID'], 'onChange="return ajaxContainer(this.id,\'content\',\'?module=' . $var['module'] . '&command=' . $var['command'] . '\')"') ?></td>
    </tr>
    <tr class="<?= alternateNextRow() ?>">
        <td>Property</td>
        <td>
            <?php
                    $params['container'] = 'content';
                    $params['url'] = 'index.php?command=' . $var['command'] . '&module=' . $var['module'];
                    $params['keyed'] = true;
                    $params['keyField'] = 'propertyID';
                    $params['valueField'] = 'propertyName';
                    $params['groupField'] = 'propertyStatus';
                    $params['groupDescriptions'] = array(0 => 'Active', 1 => 'Inactive');
            $params['textAttributes'] = '';
            renderGroupedSmartSearch('propertyCode', $var['propertyGroupedList'], $var['propertyCode'], $params);
            ?>
        </td>
    </tr>
    <tr class="<?= alternateNextRow() ?>">
        <td>Lease</td>
        <td>
            <?php
                    $params['container'] = 'content';
                    $params['url'] = 'index.php?command=' . $var['command'] . '&module=' . $var['module'];
                    $params['keyed'] = true;
                    $params['keyField'] = 'leaseID';
                    $params['valueField'] = 'leaseName';
                    $params['groupField'] = 'leaseStatus';
                    $params['groupDescriptions'] = array('C' => 'Current', 'L' => 'Vacated');
            $params['textAttributes'] = '';
            renderGroupedSmartSearch('leaseCode', $var['leaseList'], $var['leaseCode'], $params);
            ?>
        </td>
    </tr>
    <tr class="<?= alternateNextRow() ?>">
        <td>Recipient</td>
        <td><?= renderTextBox('letterRecipient', $var['letterRecipient'], 'onBlur="return ajaxContainer(this.id,\'content\',\'?module=' . $var['module'] . '&command=' . $var['command'] . '\')"') ?></td>
    </tr>
</table>
<br>
<?php
    }
    ?>
<?php if (!empty ($var['letterHistoryList'])) { ?>
<div style="overflow: auto;">
    <table id="myTable" class="data-grid row-border hover order-column" cellspacing="0" width="100%" border="1" style="border-collapse: collapse">
        <thead>
        <tr class="subTitle">
            <th>Category</th>
            <? if(!$var['propertyCode'] AND !$var['leaseCode']) { ?><th>Property Code</th> <? } ?>
            <? if(!$var['leaseCode']){ ?><th>Lease Code</th> <? } ?>
            <th>Subject</th>
            <th>Recipients</th>
            <th>Date Sent</th>
            <th>Download</th>
        </tr>
        </thead>
        <tbody>
        <?php if (!empty ($var['letterHistoryList'])) { ?>
        <?php foreach ($var['letterHistoryList'] as $k => $v) { ?>
        <tr class="<?=alternateNextRow ()?>">
            <td>
                <? if($v['emailStatusMessage'] !== '') { ?>
                <div class="tooltip">
                    <img src="<?=ASSET_DOMAIN?>assets/images/icons/warning.png"/>
                    <span class="tooltiptext" style="margin-left: 0px;width: max-content;"><?=$v['emailStatusMessage']?></span>
                </div>
                <? } ?>
                <?php if ($v['source'] == 1) { ?>
                Manually Added
                <?php } else { ?>
                <?=$v['letterCategoryName']?>
                <?php } ?>
            </td>
            <? if(!$var['propertyCode'] AND !$var['leaseCode']) { ?><td><?=$v['propertyCode']?></td> <? } ?>
            <? if(!$var['leaseCode']){ ?><td><?=$v['leaseCode']?></td> <? } ?>
            <?php if (!$v['downloadStatus']) { ?>
            <td>
                <? if ($v['emailLogId'] ) { ?>

                <?php if (!$v['validateEmailSubject'] && strlen ($v['letterEmailSubject']) >= 50) { ?>
                <span class="spanToolTip" title="<?=$v['letterEmailSubject']?>"><strong><a title="View Postmark Email" onclick="return viewPostMark('<?=$v['letterHistoryID']?>','<?=$v['postmarkMessageId']?>');" id="<?=$v['postmarkMessageId']?>"><?=substr ($v['letterEmailSubject'], 0, 50)?></a></strong>...</span>
                <?php } else { ?>
                <strong><a title="View Postmark Email" onclick="return viewPostMark('<?=$v['letterHistoryID']?>','<?=$v['postmarkMessageId']?>');" id="<?=$v['postmarkMessageId']?>"><?=$v['letterEmailSubject']?></a></strong>
                <?php } ?>
                <? } else { ?>
                    <?php if (strlen ($v['letterEmailSubject']) >= 50) { ?>
                    <span class="spanToolTip" title="<?=$v['letterEmailSubject']?>"><?=substr ($v['letterEmailSubject'], 0, 50)?>...</span>
                    <?php } else { ?>
                    <?=$v['letterEmailSubject']?>
                    <?php } ?>
                <? } ?>

            </td>
            <td>
                <? if (strpos($v['letterRecipient'], ';') !== false) {
                    $eAddresses = explode(';', $v['letterRecipient']);
                    $emailCount = count($eAddresses ?? []);
                    $counter = 1;

                    foreach ($eAddresses as $email) {
                    ?>
                        <a href="mailto:<?=preg_replace('/\s+/', '', $email)?>"><?=preg_replace('/\s+/', '', $email)?></a><? if ($counter < ($emailCount)) { ?>;&nbsp;<?} ?>
                        <? $counter++;
                    }
                } else {
                    if ($v['letterRecipient']) { ?>
                        <a href="mailto:<?=preg_replace('/\s+/', '', $v['letterRecipient'])?>"><?=$v['letterRecipient']?></a>
                    <? }
                } ?>
            </td>
            <?php } else { ?>
            <td>&nbsp;</td>
            <td>&nbsp;</td>
            <?php } ?>
            <td style="text-wrap: nowrap">
                <span class="hidden"><?=$v['dateCreated2']?></span>
                <?php if ($v['source'] == 1) { ?>
                <?=toClientTimeZone($v['dateCreated2'], 'd/m/Y')?>
                <?php } else { ?>
                <?=toClientTimeZone($v['dateCreated2'], 'd/m/Y h:i A')?>
                <?php } ?>
                <? if($v['emailLogHistorySize'] > 1) { ?>
                <div class="tooltip">
                    <img src="assets/images/icons/history-icon-colored.png" height="16px" width="16px" class="txt-icon">
                    <span class="tooltiptext" style="margin-left: -150px;width: max-content;bottom: unset;">
                        <table>
                            <tr>
                                <td></td>
                                <td>Date</td>
                            </tr>
                            <? foreach($v['emailLogHistory'] as $rowHistoryData) { ?>
                                <tr>
                                    <td>
                                        <? if($rowHistoryData['emailStatusMessage'] !== '') { ?>
                                        <img src="<?=ASSET_DOMAIN?>assets/images/icons/warning.png"/>
                                        <? } ?>
                                    </td>
                                    <td>
                                        <?=$rowHistoryData['sentDate']?>
                                    </td>
                                </tr>
                            <? } ?>
                        </table>
                    </span>
                </div>
                <? } ?>
            </td>
            <?php global $pathPrefix, $clientDirectory;
                              $v['filePath'] = str_replace ($pathPrefix, '', $v['filePath']);
                              $v['filePath'] = str_replace ($clientDirectory.'/', '', $v['filePath']);
                              $v['filePath2'] = $v['filePath'];
							  if ($v['filePath2'] == '/') $v['filePath2'] = null;
                              if (!file_exists($v['filePath2']) AND $v['filePath2'] != '')
                                    $v['filePath2'] = $pathPrefix . $clientDirectory . '/' .$v['filePath2'];
                              if (!file_exists($v['filePath2']) AND $v['filePath2'] != '')
                                    $v['filePath2'] = '';
                              if(file_exists($v['filePath2'])AND $v['filePath2'] != '')
                                {
                            ?>
            <td><a href="download.php?fileID=<?=encodeParameter( $clientDirectory .'/'.$v['filePath'])?>">download <img alt="<?=$v['letterFormat']?>" border="0" height="16" src="<?=ASSET_DOMAIN?>assets/images/icons/<?=$v['letterFormat']?>.png" width="16" /></a></td>
            <?php }else{ ?>
            <td></td>
            <?php } ?>
        </tr>
        <?php } ?>
        <?php } else { ?>
        <tr class="<?=alternateNextRow ()?>"><td class="center" colspan="7">No letter history to display.</td></tr>
        <?php } ?>
        </tbody>
    </table>
</div>
<? if($_REQUEST['action'] != 'print') { ?>
<script type="text/javascript">
    $(document).ready(function() {
        if ($('#myTable').DataTable() != null) {
            $('#myTable').DataTable().destroy();
        }
        var table = $('#myTable').DataTable({
            "iDisplayLength": 25,
            "aaSorting": []
        });
    } );


    function viewPostMark(letterHistoryId, postmarkID){
        // e.preventDefault();
        var $this = $(this);

        $("#loading").show();

        var url = `?module=administration&command=viewPostmarkEmail&action=view&id=${  postmarkID }&letter_history_id=${  letterHistoryId}`;

        $.ajax({
            url: url,
            type: 'GET',
            processData: false,
            contentType: false,
            success: function (data, status, jqxhr) {
                $("#loading").hide();

                if (!data.includes('emlvwlg-body')) {
                    Swal.fire({
                        title: '',
                        html: "Something went wrong.",
                        width: '500px',
                        allowOutsideClick: false,
                    });
                } else {
                    Swal.fire({
                        title: '',
                        html: data,
                        width: '1000px',
                        allowOutsideClick: false,
                        showCancelButton: true,
                        cancelButtonText: 'Close',
                        confirmButtonText: 'Resend Email',
                        reverseButtons: true,
                        showLoaderOnConfirm: true,
                        preConfirm: function() {
                            return new Promise(function (resolve, reject) {
                                const _fetch = `?module=administration&command=viewPostmarkEmail&action=resend&id=${  postmarkID  }&letter_history_id=${  letterHistoryId}`;

                                $.ajax({
                                    url: _fetch,
                                    type: 'GET',
                                    processData: false,
                                    contentType: false,
                                    success: function (data, status, jqxhr) {
                                        if (data) {
                                            $("#letterHistory").load($("#letterHistoryPageURL").val());
                                            Swal.fire('', 'Email has been resent.','success');
                                        } else {
                                            Swal.fire('', 'Email cannot be resent.', 'error');
                                        }
                                        resolve();
                                    }, error: function (jqxhr, status, msg) {
                                        console.log('error');
                                        reject('Error');
                                    }
                                });
                            }).catch(err => {
                                alert(`error: ${err}`)
                                return false
                            });
                        },
                        didOpen: function() {
                            const container = $('.emlvwlg-content-inner');
                            const shadow = $('#iframe-shadow');

                            shadow.contents().find("a").each(function(index) {
                                $(this).addClass('links-disabled');
                                $(this).on("click", function(event) {
                                    event.preventDefault();
                                    event.stopPropagation();
                                });
                            });

                            container.addClass('has-borders');
                        },
                    });
                }
            }, error: function (jqxhr, status, msg) {
            }
        });
    }
</script>
<? } ?>

<?php } else { ?>
<table>

    <tr class="<?= alternateNextRow() ?>">
        <td class="center" colspan="9">No letter history to display.</td>
    </tr>
</table>
<?php } ?>
<? } ?>

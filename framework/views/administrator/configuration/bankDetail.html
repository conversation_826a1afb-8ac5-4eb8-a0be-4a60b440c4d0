<script type="text/javascript">
    $(document).ready(function () {
        $('#companyIDButton').trigger('blur');

        setTimeout(function () {
            if ($('#company_id').val() != '') {
                $('#companyID').val($('#company_id').val());
                $('#companyIDButton').trigger('click');
            }
        }, 100);

        var readURL = function (input) {
            var file = input.files[0];
            var fileType = file["type"];
            var fileSize = file["size"];

            if (fileSize > 5242880) {
                alert("Unable to upload file because the size exceeds the maximum allowed file size of 5MB.");
                input.value = null;
            }
        }

        $('#file').on('change', function () {
            readURL(this);
        });
    });
</script>

<input type="hidden" id="company_id" value="<?= $var['compID'] ?>">
<h1>Change Company Banking Details <a href="<?= $var['HELPLINKS']['ta_bank_detail_help'] ?>" target="new"><img
    src="<?=ASSET_DOMAIN?>assets/images/icons/help.png" class="icon" title="help"/></a></h1>

<?=renderMessage($var['statusMessage'],'class="infoBox"')?>

<? if (($var['action'] == 'verify' && empty ($var['validationErrors'])) || $var['action'] == 'viewApproved' || $var['action'] == 'viewRejected' || $var['action'] == 'reassign' || ($var['action'] == 'reassigned' && !empty ($var['validationErrors']))) {?>
<table border="0" class="data-grid" cellpadding="0" cellspacing="0">
    <?=renderErrors($var['validationErrors']) ?>
    <tr>
        <td>
            <div>
                <div>
                    <div class="descriptionTxt">&nbsp;</div>
                    <div class="headTxt"><?php if($var['action'] == 'viewApproved') : ?>Previous Detail<?php else : ?>
                        Current Detail<?php endif; ?></div>
                    <div class="headTxt">Modified Detail</div>

                    <div class="descriptionTxt"><strong>Company</strong></div>
                    <div class="originalTxt"><?=$var['original']['companyID']?>
                        - <?=$var['original']['companyName']?></div>
                    <div class="modifiedTxt"><?=$var['new']['companyID']?> - <?=$var['new']['companyName']?></div>

                    <div class="descriptionTxt"><strong>Payment Method</strong></div>
                    <div
                        class="originalTxt<?=($var['original']['paymentMethod'] != $var['new']['paymentMethod'])?' bold':''?>"><?=$var['paymentMethodList'][$var['original']['paymentMethod']]?></div>
                    <div
                        class="modifiedTxt<?=($var['original']['paymentMethod'] != $var['new']['paymentMethod'])?' bold':''?>"><?=$var['paymentMethodList'][$var['new']['paymentMethod']]?></div>

                    <? if ($var['new']['paymentMethod'] == PAY_EFT) { ?>
                    <? if($var['original']['debtor'] == 1 ): ?>
                    <div class="descriptionTxt"><strong>Direct Debit</strong></div>
                    <div
                        class="originalTxt<?=($var['original']['directDebit'] != $var['new']['directDebit'])?' bold':''?>"><?=$var['original']['directDebit'] ? "Yes" : "No" ?></div>
                    <div
                        class="modifiedTxt<?=($var['original']['directDebit'] != $var['new']['directDebit'])?' bold':''?>"><?=$var['new']['directDebit'] ? "Yes" : "No" ?></div>
                    <? endif;?>

                    <? if (displayBsbFromSession()) { ?>
                    <div class="descriptionTxt"><strong><?=bsbLabelFromSession()?></strong></div>
                    <div
                        class="originalTxt<?=($var['original']['bsbNumber'] != $var['new']['bsbNumber'])?' bold':''?>"><?=formatWithDelimiter($var['original']['bsbNumber'])?></div>
                    <div
                        class="modifiedTxt<?=($var['original']['bsbNumber'] != $var['new']['bsbNumber'])?' bold':''?>"><?=formatWithDelimiter($var['new']['bsbNumber'])?></div>
                    <? } ?>

                    <div class="descriptionTxt"><strong>Bank Account Number</strong></div>
                    <div
                        class="originalTxt<?=($var['original']['bankAccountNumber'] != $var['new']['bankAccountNumber'])?' bold':''?>"><?=$var['original']['bankAccountNumber']?></div>
                    <div
                        class="modifiedTxt<?=($var['original']['bankAccountNumber'] != $var['new']['bankAccountNumber'])?' bold':''?>"><?=$var['new']['bankAccountNumber']?></div>

                    <div class="descriptionTxt"><strong>Bank Account Name</strong></div>
                    <div
                        class="originalTxt<?=($var['original']['bankAccountName'] != $var['new']['bankAccountName'])?' bold':''?>"><?=$var['original']['bankAccountName']?></div>
                    <div
                        class="modifiedTxt<?=($var['original']['bankAccountName'] != $var['new']['bankAccountName'])?' bold':''?>"><?=$var['new']['bankAccountName']?></div>

                    <div class="descriptionTxt"><strong>Bank Name</strong></div>
                    <div
                        class="originalTxt<?=($var['original']['bankName'] != $var['new']['bankName'])?' bold':''?>"><?=$var['original']['bankName']?></div>
                    <div
                        class="modifiedTxt<?=($var['original']['bankName'] != $var['new']['bankName'])?' bold':''?>"><?=$var['new']['bankName']?></div>

                    <? } elseif ($var['new']['paymentMethod'] == PAY_BPAY) { ?>
                    <div class="descriptionTxt"><strong>Biller Code</strong></div>
                    <div
                        class="originalTxt<?=($var['original']['bpayBillerCode'] != $var['new']['bpayBillerCode'])?' bold':''?>"><?=$var['original']['bpayBillerCode']?></div>
                    <div
                        class="modifiedTxt<?=($var['original']['bpayBillerCode'] != $var['new']['bpayBillerCode'])?' bold':''?>"><?=$var['new']['bpayBillerCode']?></div>

                    <? } elseif ($var['new']['paymentMethod'] == PAY_CHQ) { ?>
                    <div class="descriptionTxt"><strong>Cheque Clearance</strong></div>
                    <div
                        class="originalTxt<?=($var['original']['chequeDays'] != $var['new']['chequeDays'])?' bold':''?>"><?=$var['original']['chequeDays']?></div>
                    <div
                        class="modifiedTxt<?=($var['original']['chequeDays'] != $var['new']['chequeDays'])?' bold':''?>"><?=$var['new']['chequeDays']?></div>
                    <? } ?>

                    <div class="descriptionTxt"><strong>Reason</strong></div>
                    <div class="spanTxt"><?=htmlspecialchars($var['new']['reason'])?></div>

                    <? if ($var['action'] == 'verify') { ?>
                    <div class="descriptionTxt"><strong>Date Submitted</strong></div>
                    <div class="spanTxt"><?=$var['new']['dateCreated']?></div>
                    <div class="descriptionTxt"><strong>Submitted by</strong></div>
                    <div class="spanTxt"><?=$var['new']['submittedBy']?></div>
                    <div class="descriptionTxt"><strong>For Approval by</strong></div>
                    <div class="spanTxt"><?=$var['new']['authoriser']?></div>
                    <? } else if (($var['action'] == 'reassign') || ($var['action'] == 'reassigned')) { ?>
                    <div class="descriptionTxt"><strong>Date Submitted</strong></div>
                    <div class="spanTxt"><?=$var['new']['dateCreated']?></div>
                    <div class="descriptionTxt"><strong>Submitted by</strong></div>
                    <div class="spanTxt"><?=$var['new']['submittedBy']?></div>
                    <div class="descriptionTxt"><strong>For Approval by</strong></div>
                    <div class="spanTxt">
                        <?=renderDropDownList ('authoriser', 'userID', 'name', $var['userList'], $var['new']['authoriser'])?>
                    </div>
                    <? } else { ?>
                    <div class="descriptionTxt"><strong>Date</strong></div>
                    <div class="originalTxt"><?=$var['new']['dateCreated']?></div>
                    <div class="modifiedTxt"><?=$var['new']['dateUpdated']?></div>

                    <div class="descriptionTxt"><strong>&nbsp;</strong></div>
                    <div class="originalTxt"><?=$var['new']['submittedBy']?></div>
                    <div class="modifiedTxt"><?=$var['new']['modifiedBy']?></div>
                    <? } ?>

                    <? if ($var['new']['filename']) { ?>
                    <div class="descriptionTxt"><strong>Attachment</strong></div>
                    <div class="spanTxt"><a
                        href="download.php?fileID=<?=encodeParameter($var['new']['downloadLink'])?>"><?=$var['new']['filename']?></a>
                    </div>
                    <? } ?>
                </div>
            </div>
            <div style="float:right;margin:10px;">
                <?php if(($var['action'] == 'verify') && ((isset($var['selectedAuthoriser'])) && ($var['selectedAuthoriser']))) : ?>
                <?=renderButton('btnApprove','Approve','onclick="return ajaxContainer(this.id,\'content\',\'?module=' . $var['module'] . '&command=' . $var['command'] . '&auditID=' . $var['auditID'] . '&action=approve\')"')?>
                <?=renderButton('btnReject','Reject','onclick="return ajaxContainer(this.id,\'content\',\'?module=' . $var['module'] . '&command=' . $var['command'] . '&auditID=' . $var['auditID'] . '&action=reject\')"')?>
                <? endif; ?>
                <?php if(($var['action'] == 'verify') && ($_SESSION['user_id'] == $var['new']['userCreated'])) : ?>
                <?=renderButton('btnReassign','Reassign','onclick="return ajaxContainer(this.id,\'content\',\'?module=' . $var['module'] . '&command=' . $var['command'] . '&auditID=' . $var['auditID'] . '&action=reassign\')"')?>
                <? endif; ?>
                <?php if(($var['action'] == 'reassign' || $var['action'] == 'reassigned') && ($_SESSION['user_id'] == $var['new']['userCreated'])) : ?>
                <?=renderButton('btnReassigned','Save','onclick="return ajaxContainer(this.id,\'content\',\'?module=' . $var['module'] . '&command=' . $var['command'] . '&auditID=' . $var['auditID'] . '&action=reassigned\')"')?>
                <? endif; ?>
            </div>
            <? if ($var['new']['downloadLink']) { ?>

            <? } ?>
        </td>
    </tr>
</table>
<? } else { ?>
<form action="?module=<?=$var['module']?>&command=<?=$var['command']?>&action=submit&sc=with-file-upload"
      enctype="multipart/form-data" id="form" method="post" name="form">
    <?=renderHidden('max_file_size',********)?>

    <? if ($var['companyID']) { ?>
    <? if ($var['paymentMethod'] != PAY_EFT) { ?>
    <?=renderHidden('bsbNumber',$var['bsbNumber'])?>
    <?=renderHidden('bankAccountNumber',$var['bankAccountNumber'])?>
    <?=renderHidden('bankAccountName',$var['bankAccountName'])?>
    <?=renderHidden('bankName',$var['bankName'])?>
    <? } ?>
    <? if ($var['paymentMethod'] != PAY_BPAY) { ?>
    <?=renderHidden('bpayBillerCode',$var['bpayBillerCode'])?>
    <? } ?>
    <?=renderHidden('debtor',$var['debtor'])?>
    <?=renderHidden('country',$var['country'])?>
    <? } ?>
    <table border="0" class="data-grid" cellpadding="0" cellspacing="0">
        <?=renderErrors($var['validationErrors']) ?>
        <? if (!$var['noAuthoriser']) { ?>
        <tr>
            <td class="title">Company</td>
            <td class="required">*</td>
            <td><?=renderSmartSearch('companyID','companyID','companyName',$var['companyList'],$var['companyID'],'content','?module=' . $var['module'] . '&command=' . $var['command'] . '&action=retrieve');?></td>
        </tr>
        <? if ($var['companyID']) { ?>
        <tr>
            <td class="title">Preferred Payment Method</td>
            <td class="required">*</td>
            <td><?=renderRadioGroup ('paymentMethod', $var['paymentMethodList'], $var['paymentMethod'], '', 'onClick="return ajaxContainer(this.id, \'content\',\'?module=' . $var['module'] . '&command=' . $var ['command'] . '\');"')?></td>
        </tr>
        <? if ($var['paymentMethod'] == PAY_EFT) { ?>
        <? if($var['debtor'] == 1 ): ?>
        <tr class="<?=alternateNextRow()?>">
            <td class="title">Direct Debit</td>
            <td class="required"></td>
            <td><?=renderRadioGroup ('directDebit', $var['yesNoOption'], $var['directDebit']);?></td>
        </tr>
        <? endif;?>

        <? if (displayBsbFromSession()) { ?>
        <tr>
            <td class="title"><?=bsbLabelFromSession()?></td>
            <td class="required">*</td>
            <td><?=renderTextBox('bsbNumber',$var['bsbNumber'],'size="15" maxlength="' . bsbLengthFromSession() . '"')?></td>
        </tr>
        <? } ?>

        <tr>
            <td class="title">Bank Account Number</td>
            <td class="required">*</td>
            <td>
                <?=renderTextBox('bankAccountNumber',$var['bankAccountNumber'],'size="15" maxlength="' . $_SESSION['country_default']['bank_account_length'] . '"')?>
            </td>
        </tr>
        <tr>
            <td class="title">Account Name</td>
            <td class="required">*</td>
            <td><?=renderTextBox('bankAccountName',$var['bankAccountName'],'size="30"')?></td>
        </tr>
        <tr>
            <td class="title">Bank Name</td>
            <td class="required">&nbsp;</td>
            <td><?=renderTextBox('bankName',$var['bankName'],'size="30"')?></td>
        </tr>
        <? } elseif ($var['paymentMethod'] == PAY_BPAY) { ?>
        <tr>
            <td class="title">BPAY Biller Code</td>
            <td class="required">*</td>
            <td><?=renderTextBox('bpayBillerCode',$var['bpayBillerCode'],'size="7" maxlength="10"')?></td>
        </tr>
        <? } elseif ($var['paymentMethod'] == PAY_CHQ) { ?>
        <tr>
            <td class="title">Cheque Clearance</td>
            <td class="required">*</td>
            <td><?=renderSimpleDropDownList('chequeDays',$var['dayList'],$var['chequeDays'])?></td>
        </tr>
        <? } ?>
        <tr>
            <td class="title">Reason</td>
            <td class="required">*</td>
            <td><?=renderTextBox('reason',$var['reason'],'size="100" maxlength="255"')?></td>
        </tr>
        <tr>
            <td class="title">Attachment</td>
            <td class="required">&nbsp;</td>
            <td><input type="file" name="file" id="file"/></td>
        </tr>
        <tr>
            <td class="title">Authoriser</td>
            <td class="required">*</td>
            <td><?=renderDropDownList ('authoriser', 'userID', 'name', $var['userList'], $var['authoriser'])?></td>
        </tr>

        <tr class="footer">
            <td colspan="3">
                <?=renderButton('btnSubmit','Submit for Approval &rarr;')?>
            </td>
        </tr>
        <? } ?>
        <? } ?>
    </table>
</form>
<? } ?>

<script src="<?= ASSET_DOMAIN ?>assets/file-uploader/jquery.fileuploader.min.js?<?= JS_VERSION ?>"
        type="text/javascript"></script>
<link rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/datatables.net-dt/2.1.7/css/dataTables.dataTables.min.css"
      integrity="sha512-JbyOZyqfBvhWNzVXZy2zUX9Hhp8+JGL15feGcRq1JnS1ZIxEdECKSqT+eLuZ8BvvzGHkxrBdu+EuJLdHk+kQ8g=="
      crossorigin="anonymous" referrerpolicy="no-referrer"/>
<link rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/datatables.net-buttons-dt/3.2.0/buttons.dataTables.min.css"
      integrity="sha512-/Ak8JpOHDvv+jy9sZjY7TpcoN+9P0Oc+BmdOm5PVKL8wgvXtQqP+PEH4ahmlHrzGIY3p04IcclFXkWmoBbBl1Q=="
      crossorigin="anonymous" referrerpolicy="no-referrer"/>
<style>
    .dialog-form-recovered input {
        margin-bottom: 5px;
        padding: 2px 3px;
        width: 209px;
    }

    .dialog-form-recovered table td {
        padding: 4px;
        width: 100px;
    }

    .dialog-form-recovered table.head th {
        padding: 4px;
        width: 100px;
    }

    .dialog-form-recovered .subHeader > th {
        width: 65px;
    }

    .ui-dialog-title {
        color: white;
    }

    .dialog-form-recovered input {
        border: 1px solid #ddd;
        float: right;
        font-size: 15px;
        margin-bottom: 12px;
        padding: 2px 2px 3px 8px;
        width: 50%;
    }

    .dialog-form-recovered table {
        /* Remove default list styling */
        list-style-type: none;
        padding: 0;
        margin: 0;
        float: left;
    }

    .dialog-form-recovered table#head {
        background: #acd7ec none repeat scroll 0 0;
        list-style-type: none;
        padding: 0;
        margin: 0;
        float: left;
    }

    .dialog-form-recovered {
        text-align: center;
    }

    .input-disabled {
        background-color: #EBEBE4;
    }

    .warning-text {
        color: red;
        font-weight: bold;
        text-align: left;
    }

    .hidden {
        display: none;
    }

    .line-note {
        width: 31px;
        height: 20px;
        text-align: center;
        font-size: 12px;
        background-color: #4caf50;
        color: #ffffff;
        border: 1px solid #4caf50;
        padding: 2px 8px 2px 10px;
        border-radius: 5px;
    }
</style>

<script type="text/javascript">
    $().ready(function () {
        $('a#recurringInvoice').click(function () {
            modalDialog('?module=ap&command=viewRecurringInvoice', {
                autoOpen: true,
                modal: true,
                title: 'Template Invoice',
                height: "auto",
                width: 960
            });
        });
        $('a#creditAnInvoice').click(function () {
            modalDialog('?module=ap&command=viewUnpaidInvoice&propertyID=<?=$var['propertyID']?>&creditorID=<?=$var['creditorID']?>&source=invoice', {
                autoOpen: true,
                modal: true,
                title: 'Select an Invoice',
                height: "auto",
                width: 960
            });
        });
        $('a#orderNumber').click(function () {

            const vw = Math.max(document.documentElement.clientWidth || 0, window.innerWidth || 0);

            if (vw <= 768) {
                modalDialog('?module=ap&command=viewOrderNumber&propertyID=<?=$var['propertyID']?>&creditorID=<?=$var['creditorID']?>&clientID=<?=$var['clientID']?>&source=invoice', {
                    autoOpen: true,
                    modal: true,
                    title: 'Work Orders for <?=$var['creditorID']?>',
                    height: "300",
                    width: 750
                });
            } else if (vw <= 834) {
                modalDialog('?module=ap&command=viewOrderNumber&propertyID=<?=$var['propertyID']?>&creditorID=<?=$var['creditorID']?>&clientID=<?=$var['clientID']?>&source=invoice', {
                    autoOpen: true,
                    modal: true,
                    title: 'Work Orders for <?=$var['creditorID']?>',
                    height: "300",
                    width: 800
                });
            } else if (vw <= 1024) {
                modalDialog('?module=ap&command=viewOrderNumber&propertyID=<?=$var['propertyID']?>&creditorID=<?=$var['creditorID']?>&clientID=<?=$var['clientID']?>&source=invoice', {
                    autoOpen: true,
                    modal: true,
                    title: 'Work Orders for <?=$var['creditorID']?>',
                    height: "300",
                    width: 1000
                });
            } else {
                modalDialog('?module=ap&command=viewOrderNumber&propertyID=<?=$var['propertyID']?>&creditorID=<?=$var['creditorID']?>&clientID=<?=$var['clientID']?>&source=invoice', {
                    autoOpen: true,
                    modal: true,
                    title: 'Work Orders for <?=$var['creditorID']?>',
                    height: "300",
                    width: 1115
                });
            }

        });

        <? if ($var['nextTab']) { ?>
        $('#<?=$var['nextTab']?>').focus();

        <? } elseif ($var['action'] == 'completeTransaction') { ?>
        $('#creditorID').focus();
        <? } ?>

        $('#taxRateID').change(function () {
            computeTax();
        });
        $('#grossAmount').keyup(function () {
            computeTax();
        });
        $('#grossAmount').blur(function () {
            grossAmount = accounting.toFixed(parseFloat($('#grossAmount').val()), 2);
            $('#grossAmount').val(grossAmount).attr('value', grossAmount);
        });

    });

    function computeTax() {
        var taxRate = parseFloat($('option:selected', $('#taxRateID')).attr('taxRate'));
        var grossAmt = parseFloat($('#grossAmount').val());

        taxRate = parseFloat(accounting.toFixed(taxRate, 2));
        grossAmt = parseFloat(accounting.toFixed(grossAmt, 2));

        var netAmount = grossAmt / (1 + (taxRate / 100));
        var taxAmount = grossAmt - netAmount;

        $('#taxAmount').val(taxAmount);
        $('#taxAmountString').text(toMoney2(taxAmount));

        $('#netAmount').val(netAmount);
        $('#netAmountString').text(toMoney2(netAmount));
    }

    var copyDate = function () {
        var date = $('#transactionDate').val();
        $('#fromDate').val(date);
        $('#toDate').val(date);
        $('#dueDate').val(date);
    }

    var truncate = function (obj) {
        if (obj.value) $('#txtDescription').html(obj.value.substr(0, 55));
    }


</script>


<div class="content-with-vo-notes">

    <?= renderMessage($var['statusMessage'], 'class="infoBox"'); ?>
    <?= renderDownloadLink($var['downloadLink']); ?>

    <h1>Invoices & Credits</h1>
    <b>This section allows you to create a new invoice or credit (<a href="#" id="recurringInvoice"
                                                                     title="Use Invoice Template">Use Invoice
            Template</a>)</b>
    <br/>Credits are used for entering credit notes from suppliers or crediting invoices that have been entered
    incorrectly or no longer requires payment.
    <br/><br/>

    <!--<form id="uploadForm" enctype="multipart/form-data" action="?sc=with-file-upload&module=ap&command=invoice&action=upload" method="post">-->
    <?= renderHidden('appendFiles', $var['appendFiles']) ?>
    <?= renderHidden('invoiceLinesJson', $var['invoiceLinesJson']) ?>
    <?= renderHidden('_templateID', $var['_templateID']); ?>
    <?= renderHidden('batchNumber', $var['batchNumber']); ?>
    <?= renderHidden('documentBatchNumber', $var['documentBatchNumber']); ?>
    <?= renderHidden('toBatchNumber', $var['toBatchNumber']); ?>
    <?= renderHidden('toLineNumber', $var['toLineNumber']); ?>
    <?= renderHidden('_invoiceNumber', $var['_invoiceNumber']); ?>
    <?= renderHidden('propertyTaxID', $var['propertyTaxID']); ?>
    <?= renderHidden('UnitID', $var['unitID']); ?>
    <?= renderHidden('propertyIsLedger', $var['propertyIsLedger']); ?>
    <?php /* [LYN] FROM OPENING BALANCES */ ?>
    <?php if (isset($var['obID'])): ?>
        <?= renderHidden('obID', $var['obID']); ?>
    <?php endif; ?>
    <?php /* [LYN] FROM OPENING BALANCES */ ?>

    <?php if (isset($var['obID'])) : ?>
        <div class="infoBox">
            <h3>
                Accounts Payable out by <strong><font
                        color="red"><?= toMoney($var['apDifferenceAmount']) ?></font></strong>
                <br/>
                View Unpaid Creditors <a href="#" title="Transactions"
                                         onClick="return modalDialog('?module=generalLedger&command=openingBalancesModal&obID=<?= $var['obID'] ?>&type=AP',{modal: true, width:963, height:'auto', position: { my: 'center', at: 'center', of: window }, title: 'Unpaid Creditors'})">here</a>
            </h3>
        </div>
    <?php endif; ?>

	<? if (count($var['validationErrors'] ?? []) > 0) {?>
        <table class="data-grid" cellspacing="0" cellpadding="3" border="0">
            <?= renderErrors($var['validationErrors']) ?>
        </table>
    <? } ?>
    <table id="validationTable" cellspacing="0" cellpadding="3" border="0">

    </table>

    <div id="tenantSelector">
		<?	if (sizeof($var['invoiceLines'] ?? []) == 0)
            echo $var['UserControl:TenantSelector'];
        else {
            echo renderHidden('creditorID', $var['creditorID']);
            //echo '<h3>Supplier : ' . dbGetCompanyDescription($var['creditorID']) ."</h3>";
            ?>
            <table class="data-grid" cellspacing="0" cellpadding="3" border="0">
                <tr class="row">
                    <td class="title">Supplier</td>
                    <td><h3><?= ($var['creditorID'] . ' - ' . dbGetCompanyDescription($var['creditorID'])) ?></h3></td>
                </tr>
            </table>
        <? } ?>
    </div>

    <? if ($var['step'] == 2) { ?>
        <? if ($var['previousInvoices']) { ?>
            <table class="data-grid" cellspacing="0" cellpadding="3" border="0">
                <tr class="subTitle">
                    <td colspan="15">Previous Invoices</td>
                </tr>
                <tr class="fieldDescription">
                    <td>Property</td>
                    <td>Invoice Date</td>
                    <td>Account</td>
                    <td>Description</td>
                    <td>From-To</td>
                    <td>Due Date</td>
                    <td align="right">Amount</td>
                    <td align="right">Net Amount</td>
                    <td align="right">Tax Code</td>
                    <td align="right">Tax Amount</td>
                </tr>
                <? foreach ($var['previousInvoices'] as $inv) { ?>
                    <tr class="<?= alternateNextRow() ?>">
                        <td><?= $inv['propertyID'] ?></td>
                        <td><?= $inv['transactionDate'] ?></td>
                        <td><?= $inv['accountID'] ?></td>
                        <td><?= $inv['description'] ?></td>
                        <td><?= $inv['fromDate'] ?> to <?= $inv['toDate'] ?></td>
                        <td><?= $inv['dueDate'] ?></td>
                        <td align="right"><?= toMoney($inv['transactionAmount']) ?></td>
                        <td align="right"><?= toMoney($inv['netAmount']) ?></td>
                        <td align="right"><?= $inv['taxID'] ?></td>
                        <td align="right"><?= toMoney($inv['taxAmount']) ?></td>
                    </tr>
                <? } ?>
            </table>
        <? } ?>

        <table id="previousInvoicesTable" class="data-grid hidden" cellspacing="0" cellpadding="3" border="0">
            <tr class="subTitle">
                <td colspan="15">Previous Invoices</td>
            </tr>
            <tr class="fieldDescription">
                <td>Property</td>
                <td>Invoice Date</td>
                <td>Account</td>
                <td>Description</td>
                <td>From-To</td>
                <td>Due Date</td>
                <td align="right">Amount</td>
                <td align="right">Net Amount</td>
                <td align="right">Tax Code</td>
                <td align="right">Tax Amount</td>
            </tr>
            <tbody id="previousInvoicesTableContent">
            <tr class="<?= alternateNextRow() ?>">
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td align="right"></td>
                <td align="right"></td>
                <td align="right"></td>
                <td align="right"></td>
            </tr>
            </tbody>

        </table>

        <table class="data-grid" cellspacing="0" cellpadding="3" border="0">
            <tr class="subTitle">
                <td colspan="3">Transaction Details</td>
            </tr>
            <tr class="<?= alternateNextRow() ?>">
                <td colspan="3" style="padding-left: 0px;">
                    <table class="data-grid" cellspacing="0" cellpadding="3" border="0" style="vertical-align: top">
                        <tr class="<?= alternateNextRow() ?>" style="vertical-align:top">
                            <td style="padding-left: 0px; width: 35%">
                                <table class="data-grid" cellspacing="0" cellpadding="3" border="0"
                                       style="width: 100%;">
                                    <? if ($var['supplierAgent'] == 1) { ?>
                                        <tr class="<?= alternateNextRow() ?>">
                                            <td class="title">Create agent invoice</td>
                                            <td class="required">*</td>
                                            <td><?= renderCheckBox('createAndAttachAgentInvoice', '', 'Yes', $var['createAndAttachAgentInvoice']); ?></td>
                                        </tr>

                                        <tr class="<?= alternateNextRow() ?> showGSTOnInvoice"
                                            title="Checking this will create an agent invoice with tax for <?= TAXFREE ?> line items.">
                                            <td class="title">Show <?= $_SESSION['country_default']['tax_label'] ?> on
                                                Invoice
                                            </td>
                                            <td class="required"></td>
                                            <td><?= renderCheckBox('showGSTOnInvoice', '', '1', $var['showGSTOnInvoice']); ?></td>
                                        </tr>

                                    <? } ?>
                                    <tr class="<?= alternateNextRow() ?>">
                                        <td class="title"><?= (($var['transactionType'] == TYPE_INVOICE) ? 'Invoice' : 'Credit Note') ?>
                                            #
                                        </td>
                                        <td class="required">*</td>
                                        <!--<td><?= renderTextBox('invoiceNumber', $var['invoiceNumber'], 'maxlength="20" onChange="return ajaxContainer(this.id, \'content\', \'?action=checkInvoice&module=ap&command=invoice&nextTab=orderNumber\')" size="30"') ?> <?= $var['invoiceNumberUnique'] ?></td>-->
                                        <td><?= renderTextBox('invoiceNumber', $var['invoiceNumber'], 'maxlength="20" onChange="checkIfExisted(this,\'invoice\')" size="30"') ?> <?= $var['invoiceNumberUnique'] ?></td>
                                    </tr>
                                    <tr class="<?= alternateNextRow() ?>">
                                        <td class="title">Work Order Number</td>
                                        <? if ($var['orderNumberRequire'] == 1) { ?>
                                            <td class="required">*</td>
                                        <? } else { ?>
                                            <td class="required"></td>
                                        <? } ?>
                                        <td><?= renderTextBox('orderNumber', $var['orderNumber'], ' onChange="return ajaxContainerV2(this)" size="30" maxlength="30"') ?>
                                            <!--										<a href="#" id="orderNumber" title="View Order Number"><img class="icon" src="<?= ASSET_DOMAIN ?>assets/images/icons/popup.png"></a>-->
                                            <?
                                            renderPopUpWorkOrder($var['creditorID'], $var['orderNumberList']);
                                            ?>
                                        </td>
                                    </tr>
                                    <tr class="<?= alternateNextRow() ?>">
                                        <td class="title"><?= (($var['transactionType'] == TYPE_INVOICE) ? 'Invoice' : 'Credit') ?>
                                            Date
                                        </td>
                                        <td class="required">*</td>
                                        <td>
                                            <?= renderSmartDateFutureDatedWarn('futureDatedSpan', 'transactionDate', $var['transactionDate'], ' onBlur="checkFutureDate(this,\'futureDatedSpan\')" '); ?>
                                            <a onclick="return copyDate(); return false;"><img class="icon"
                                                                                               src="<?= ASSET_DOMAIN ?>assets/images/icons/arrow_down.png"></a>
                                            <span id="futureDatedSpan" class="Draft"></span>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                            <td style="padding-left: 0px; width: 65%">
                                <table class="data-grid" cellspacing="0" cellpadding="3" border="0"
                                       style="width: 100%; vertical-align: top;">
                                    <? if ($var['supplierAgent'] == 1) { ?>
                                        <tr class="<?= alternateNextRow() ?>">
                                            <td class="title">
                                                <div class="EftDetails" style="display: none">Bank Details on Invoice
                                                    PDF :
                                                </div>
                                            </td>
                                            <td class="required"></td>
                                            <td class="EftDetails"><?= renderSimpleDropDownList('eftDetail', $var['eftDetailsList'], $var['eftDetail']); ?></td>
                                            <td class="EftDetails2">&nbsp;</td>
                                        </tr>
                                    <? } ?>
                                    <tr class="<?= alternateNextRow() ?>">
                                        <td class="title">Payment Method:</td>
                                        <td class="required"></td>
                                        <td><? echo $var['paymentMethodNameView'] ?></td>
                                    </tr>
                                    <? if ($var['paymentMethodView'] == '1') { ?>
                                        <? if (displayBsbFromSession()) { ?>
                                            <tr class="<?= alternateNextRow() ?>">
                                                <td class="title"><?=bsbLabelFromSession()?>:</td>
                                                <td class="required"></td>
                                                <td><? echo formatWithDelimiter($var['bsbNumberView']) ?></td>
                                            </tr>
                                        <? } ?>
                                        <tr class="<?= alternateNextRow() ?>">
                                            <td class="title">Account:</td>
                                            <td class="required"></td>
                                            <td><? echo $var['bankAccountNumberView'] ?></td>
                                        </tr>

                                        <? if (!displayBsbFromSession()) { ?>
                                            <tr class="<?= alternateNextRow() ?>">
                                                <td class="title">&nbsp;</td>
                                                <td class="required"></td>
                                                <td>&nbsp;</td>
                                            </tr>
                                        <? } ?>
                                    <? } elseif ($var['paymentMethodView'] == '2') { ?>

                                        <tr class="<?= alternateNextRow() ?>">
                                            <td class="title">Biller Code:</td>
                                            <td class="required"></td>
                                            <td><? echo $var['bpayBillerCodeView'] ?></td>
                                        </tr>
                                        <tr class="<?= alternateNextRow() ?>">
                                            <td class="title"></td>
                                            <td class="required"></td>
                                            <td>&nbsp;</td>
                                        </tr>
                                    <? } else { ?>
                                        <tr class="<?= alternateNextRow() ?>">
                                            <td class="title"></td>
                                            <td class="required"></td>
                                            <td>&nbsp;</td>
                                        </tr>
                                        <tr class="<?= alternateNextRow() ?>">
                                            <td class="title"></td>
                                            <td class="required"></td>
                                            <td>&nbsp;</td>
                                        </tr>
                                    <? } ?>
                                </table>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>

            <tr class="highlight">
                <td class="title">Transaction Type</td>
                <td class="required">*</td>
                <td>
                    <?= renderRadioGroup('transactionType', $var['transactionTypeList'], $var['transactionType'], '', ' onClick="return ajaxContainer(this.id, \'content\',\'?action=updateTypes&module=ap&command=invoice\')"') ?>
                    &nbsp; (<a href="#" id="creditAnInvoice" title="View Unpaid Invoices">Credit an Invoice</a>)
                </td>
            </tr>
            <tr class="row">
                <td class="title">Select a Property</td>
                <td class="required">*</td>
                <td><?= renderSmartSearch('propertyID', 'propertyID', 'propertyName', $var['propertyList'], $var['propertyID'], 'content', '?command=invoice&module=ap&action=changePrimary&selected=true&nextTab=accountIDList') ?>

                    <span style="position:relative; top:2px; padding-right:1px; padding-left:10px;">
					<?php
                    if ($var['propertyID']) {
                        renderPopUpPropertyVONotes($var['propertyID'], $var['voPropertyNotes'], $var['voPropertyNotesCount']);
                    }
                    ?>
				</span>

                    <span style="position:relative; top:2px; padding-right:1px; padding-left:5px;">
					<?php
                    if ($var['propertyID']) {
                        renderPopUpSupplierReconciliation($var['creditorID'], $var['propertyID'], $var['transactionType'], $var['suppReconciliation'], $var['suppReconciliationCount'], $var['propertyIsLedger']);
                    }
                    ?>
				</span>

                    <span style="padding: 0 10px;">
					<?php
                    if ($var['propertyID'] and $var['propertyIsLedger']) {
                        ?>
                        <span class='title' style='padding-right: +15px'>Select a Sub-Ledger </span>
                        <span style="color: crimson;">*</span> &nbsp;&nbsp;&nbsp;
                        <?= renderDropDownList('leaseID', 'leaseID', 'leaseCodeName', $var['leaseList'], $var['leaseID']) ?>
                        <?php
                    } else if (!empty($var['propertyID']) and !empty($var['transactionDate']) and !$var['propertyIsLedger']) {
                        echo "<span class='title' style='padding-right: +15px'>Select a Unit</span>";
                        renderDropDownList("unitIDMain", "unitID", "descriptionPretty", dbGetUnitsForPropertyAsAt($var['propertyID'], $var['transactionDate']), $var['unitIDMain'], '', false, '');
                    }
                    ?>
				</span>
                </td>
            </tr>
            <? if ($var['_invoiceNumber']) { ?>
                <tr class="subHeader">
                    <td colspan="3">Crediting invoice #<?= $var['_invoiceNumber'] ?>
                        (<b>id:</b> <?= $var['toBatchNumber'] ?>/<?= $var['toLineNumber'] ?>)
                    </td>
                </tr>
            <? } ?>
            <tr class="<?= alternateNextRow() ?>">
                <td class="title">Account</td>
                <td class="required">*</td>
                <td>
                    <? if ($var['_invoiceNumber']) { ?>
                        <?= renderHidden('accountID', $var['accountID']); ?><?= $var['accountID'] ?>
                    <? } else { ?>
                        <?
                        $params['container'] = 'content';
                        $params['url'] = '?action=updateAccounts&module=ap&command=invoice&nextTab=description';
                        $params['keyed'] = true;
                        $params['keyField'] = 'accountID';
                        $params['valueField'] = 'accountName';
                        $params['groupField'] = 'accountGroup';
                        $params['groupDescriptions'] = $var['accountGroupList'];
                        $params['textAttributes'] = 'required = "true"';
                        renderGroupedSmartSearchByCodeByName('accountID', $var['accountList'], $var['accountID'], $params);
                        ?>
                    <? } ?>
                    <? if ($var['accountGroupType'] != null) { ?>
                        <span style="margin-left:15px;">AR Transaction &nbsp</span>
                        <? //=renderCheckBox ('apRecovered', 'AP Recovered', 1, $var['apRecovered'],'')?>
                        <?= renderHidden('jsonAr', $var['jsonAr']) ?>
                        <?= renderButton('arRecovered', 'Link to existing AR Transaction', '') ?>
                        <?= renderButton('arRecoveredUpdate', 'Link to existing AR Transaction', 'style="display:none;"') ?>
                        <!-- <span id='span_arRecovered' style="margin-left:15px;">Amount &nbsp -->
                        <? //=renderTextBox ('apRecoveredAmount', $var['apRecoveredAmount'],'size="45" style="margin-left:10px;"')?>
                        <!-- <span> -->
                        <!-- <a href="#" id='addAR' style="margin-left:10px; visibility:hidden;">Update AP</a> -->
                    <? }//else{?>
                    <!-- <input id="aRecovered" name="arRecovered" disabled value="0" type="checkbox"> -->
                    <!-- <label for="arRecovered">AP Recovered</label> -->
                    <? //} ?>
                </td>
            </tr>

            <tr class="<?= alternateNextRow() ?>">
                <td class="title">Description</td>
                <td class="required">*</td>
                <td><?= renderTextBox('description', $var['description'], 'size="70" maxlength="55" onKeyUp="return truncate(this);"') ?>
                    Displayed in reports as - <span id="txtDescription"
                                                    class="crimson"><?= substr($var['description'], 0, 55) ?></span>
                </td>
            </tr>
            <? if ($var['paymentMethod'] == 2) { ?>
                <tr class="row">
                    <td class="title">BPAY Reference</td>
                    <td class="required">*</td>
                    <td>
                        <?
                        $jscript_for_bpay = "smartSearch(this, 'bpayReferenceList');
						if ($('#invoiceNumber').val() && ($('#bpayReference').val() && $('#bpayReference').val() != $('#bpayReferenceList').val())) {
							if (confirm ('The CRN you entered is different to the CRN previously entered to this supplier from this property. Would you like to continue and enter another CRN?')) {
								return ajaxCall (this.id, 'content', '?command=invoice&module=ap&action=changeReference', serializeContainer ('bpayReferenceContainer'));
							}
						}";
                        renderTextBox('bpayReference', $var['bpayReference'], ' size="18" maxlength="20" onChange="' . $jscript_for_bpay . '"');
                        echo '&nbsp;';
                        renderKeyedDropDownList('bpayReferenceList', 'bpayReference', 'bpayReference', $var['bpayList'], $var['bpayReference'], ' style="min-width: 150px;" onChange="smartUpdate (this, \'bpayReference\');return ajaxCall (this.id, \'content\', \'?command=invoice&module=ap&action=changeReference\', serializeContainer (\'bpayReferenceContainer\'));"', false, true, false, true);
                        echo '&nbsp;';
                        renderButton('bpayReferenceButton', ' &rarr; ', ' onClick="return ajaxCall(this.id, \'content\', \'?command=invoice&module=ap&action=changeReference\', serializeContainer(\'bpayReferenceContainer\'))"');
                        ?>

                        <?
                        if ($var['bpayReference']) {
                            renderButton('bpayReferenceRemoveButton', '&times', ' onClick="return ajaxCall(this.id, \'content\', \'?command=invoice&module=ap&action=removeBpayReference\', serializeContainer(\'bpayReferenceContainer\'))"');
                        }
                        ?>
                    </td>
                </tr>
            <? } ?>
            <tr class="<?= alternateNextRow() ?>">
                <td class="title">From Date</td>
                <td class="required">*</td>
                <td>
                    <? if ($var['_invoiceNumber']) { ?>
                        <?= renderHidden('fromDate', $var['fromDate']); ?><?= $var['fromDate'] ?>
                    <? } else { ?>
                        <?= renderSmartDate('fromDate', $var['fromDate'], 'onBlur=" if (!this.value) smartFill(\'transactionDate\',this)"') ?>
                    <? } ?>
                </td>
            </tr>
            <tr class="<?= alternateNextRow() ?>">
                <td class="title">To Date</td>
                <td class="required">*</td>
                <td>
                    <? if ($var['_invoiceNumber']) { ?>
                        <?= renderHidden('toDate', $var['toDate']); ?><?= $var['toDate'] ?>
                    <? } else { ?>
                        <?= renderSmartDate('toDate', $var['toDate'], 'onBlur=" if (!this.value) smartFill (\'transactionDate\',this)"') ?>
                    <? } ?>
                </td>
            </tr>
            <tr class="<?= alternateNextRow() ?>">
                <td class="title">Due Date</td>
                <td class="required">*</td>
                <td>
                    <? if ($var['_invoiceNumber']) { ?>
                        <?= renderHidden('dueDate', $var['dueDate']); ?><?= $var['dueDate'] ?>
                    <? } else { ?>
                        <?= renderSmartDate('dueDate', $var['dueDate'], 'onBlur=" if (!this.value)  smartFill(\'transactionDate\',this)"') ?>
                    <? } ?>
                </td>
            </tr>

            <tr class="<?= alternateNextRow() ?>">
                <td class="title">Tax Rate</td>
                <td class="required">*</td>
                <td>
                    <? if ($var['_invoiceNumber']) { ?>
                        <?= renderHidden('taxRateID', $var['taxRateID']); ?><?= $var['taxRateID'] ?>
                    <? } else { ?>
                        <?= renderDropDownList("taxRateID", "taxCode", "taxDescription", $var['taxRateList'], $var['taxRateID'], '', false, array('taxRate')) ?>
                        <span class="light">(owner's tax status is <?= $var['propertyTaxID'] ?>)</span>
                    <? } ?>
                </td>
            </tr>
            <tr class="<?= alternateNextRow() ?>">
                <td class="title">Gross Amount</td>
                <td class="required">*</td>
                <td>
                    <? if ($var['_invoiceNumber']) { ?>
                        <? renderHidden('grossAmount', $var['grossAmount']); ?><?= toMoney($var['grossAmount']) ?>
                    <? } else { ?>
                        <?= renderTextBox('grossAmount', $var['grossAmount']) ?>
                    <? } ?>

                    <? if ($var['estimatedAmount']) { ?><?= toMoney($var['estimatedAmount']) ?><? renderHidden('estimatedAmount', $var['estimatedAmount']); ?> (estimate)<? } ?>
                </td>
            </tr>
            <tr class="<?= alternateNextRow() ?>">
                <td class="title">Tax Amount</td>
                <td class="required">&nbsp;</td>
                <td><?= renderHidden('taxAmount', $var['taxAmount']) ?> <span
                        id="taxAmountString"><?= toMoney($var['taxAmount']) ?></span></td>
            </tr>

            <tr class="highlight">
                <td class="title">Net Amount</td>
                <td class="required">&nbsp;</td>
                <td><?= renderHidden('netAmount', $var['netAmount']) ?> <span
                        id="netAmountString"><?= toMoney($var['netAmount']) ?></span></td>
            </tr>

            <tr class="<?= alternateNextRow() ?> interimInvoiceRows">
                <td class="title">Invoice Note:</td>
                <td class="required">&nbsp;</td>
                <td>
                <textarea maxlength="250" name="invoiceNote" id="invoiceNote" rows="4"
                          cols="70"><?php echo $var['invoiceNote'] ?></textarea>
                    <span id="charWarning" class="hidden warning-text" role="alert">You have reached the 250 character
                        limit.</span>
                </td>
            </tr>

            <tr class="footer">
                <!--<td>Hold Invoice &nbsp;<?= renderCheckBox('holdInvoice', '', true, $var['holdInvoice']) ?></td>-->
                <td colspan="3">
                    <? if (!$var['_invoiceNumber']) { ?>
                        <?= renderButton('btnRecurring', 'Save As Template &rarr;', 'onClick="return ajaxContainer(this.id, \'content\',\'?action=saveRecurring&module=ap&command=invoice\')"'); ?>
                    <? } else { ?>
                        <? renderButton('btnCancel', 'Cancel', 'onClick="return ajaxContainer(this.id, \'content\',\'?action=changePrimary&module=ap&command=invoice\')"'); ?>
                    <? } ?>
                    &nbsp;
                    <? if ($var['taxRateID'] != $var['propertyTaxID']) { ?>
                        <?= renderButton('btnAdd', 'Process &rarr;'); ?>
                        <? //=renderButton ('btnAdd','Process &rarr;','onClick="return ajaxConfirmContainer (this.id, \'The property and selected tax rates do not match - are you sure you wish to proceed?\',\'content\',\'?action=addLine2&module=ap&command=invoice\');"');?>
                    <? } else { ?>
                        <?= renderButton('btnAdd', 'Process &rarr;'); ?>
                        <? //=renderButton ('btnAdd','Process &rarr;','onClick="return ajaxContainer (this.id, \'content\', \'?action=addLine2&module=ap&command=invoice\');"');?>
                    <? } ?>
                </td>
            </tr>
            <tr id="file-rw" style="visibility: hidden;">
                <td colspan="9">

                    <input type="file" name="invoiceFile">
                    <br>
                    <div id='divCheckBox' style="float: left;visibility: hidden;">
                        <? renderCheckBox('attachAR', '', true, $var['attachAR'], ''); ?>&nbsp;
                        <!--<?= renderCheckBox('attachAR', '', true, $var['attachAR'], 'onClick="return ajaxContainer (this.id, \'content\', \'?action=attachToRelatedInvoice&module=ap&command=invoice\');"') ?>&nbsp;-->
                        Attach to related AR Invoice &nbsp;
                    </div>
                    <div id='divCheckBox2' style="float: left;visibility: hidden;">
                        <? renderCheckBox('attachOwnerR', '', true, $var['attachOwnerR'], ''); ?>&nbsp;<!-- original -->
                        <? //renderCheckBox('attachOwnerR','',true,$var['defPropertyAttachToOwnerReport'],''); ?>&nbsp;
                        <!-- updated -->
                        <!--<?= renderCheckBox('attachOwnerR', '', true, $var['attachOwnerR'], 'onClick="return ajaxContainer (this.id, \'content\', \'?action=attachToOwnerReport&module=ap&command=invoice\');"') ?>&nbsp;-->
                        Attach to Owner Reports &nbsp;
                    </div>

                </td>
            </tr>


        </table>
        <br>
        <? if ($var['batchNumbers']) { ?>


            <? //if ($var['supplierInvoices']) { ?>
            <? //foreach ($var['supplierInvoices'] as $s) { ?>

            <? //} ?>
            <? //} ?>

        <? } ?>


        <table id='invoiceLine' class="data-grid" cellspacing="0" cellpadding="3" border="0">

        </table>
<? //if (sizeof($var['invoiceLines'] ?? []) > 0) { ?>
<!--<? //if (sizeof($var['invoiceLines'] ?? []) > 0) { ?>-->
        <!--<table id = 'invoiceLine1' class="data-grid" cellspacing="0" cellpadding="3" border="0">-->
        <!--<tr class="fieldDescription">-->
        <!--<td>Line Number</td>-->
        <!--<td>Transaction Date</td>-->
        <!--<td>Property</td>-->
        <!--<td>Account</td>-->
        <!--<td>Description</td>-->
        <!--<td class="right">Net Amount</td>-->
        <!--<td class="right">Tax Amount</td>-->
        <!--<td class="right">Gross Amount</td>-->
        <!--<td width="1%">&nbsp;</td>-->
        <!--</tr>-->
        <!--<? //foreach ($var['invoiceLines'] as $line) { ?>-->
        <!--<tr class="<? //=alternateNextRow()?>">-->
        <!--<td id="lineNumber"><? //=$line['lineNumber']?></td>-->
        <!--<td id="transactionDate"><? //=$line['transactionDate']?></td>-->
        <!--<td id="propertyID"><? //=$line['propertyID']?></td>-->
        <!--<td id="accountID"><strong><? //=$line['accountID']?></strong> <?= $line['accountName'] ?></td>-->
        <!--<td id="description"><? //=$line['description']?></td>-->
        <!--<td id="netAmount" class="right"><? //=toMoney($line['netAmount'])?></td>-->
        <!--<td id="taxAmount" class="right"><? //=toMoney($line['taxAmount'])?></td>-->
        <!--<td id="transactionAmount" class="right"><? //=toMoney($line['transactionAmount'])?></td>-->
        <!--<td width="1%"><img src="<?= ASSET_DOMAIN ?>assets/images/icons/delete.png" onClick="return ajaxContainer (this.id, 'content','?command=invoice&module=ap&action=delete&batchNumber=<?= $line['batchNumber'] ?>&lineNumber=<?= $line['lineNumber'] ?>');" /></td>-->
        <!--</tr>-->
        <!--<? // } ?>-->
        <!--<tr class="highlight">-->
        <!--<td colspan="5">&nbsp;</td>-->
        <!--<td class="right"><? //=toMoney ($var['total']['netAmount'])?></td>-->
        <!--<td class="right"><? //=toMoney ($var['total']['taxAmount'])?></td>-->
        <!--<td class="right"><? //=toMoney ($var['total']['transactionAmount'])?></td>-->
        <!--<td width="1%">&nbsp;</td>-->
        <!--</tr>-->
        <!--<tr class="footer"><td colspan="10"><? renderButton('btnPay', 'Pay Now &rarr;', 'onClick="return ajaxContainer (this.id, \'content\',\'?action=payNow&module=ap&command=invoice\')"'); ?> <? renderButton('btnAdd', 'On-charge Tenants &rarr;', 'onClick="return ajaxContainer (this.id, \'content\',\'?action=onCharge&module=ap&command=invoice\')"'); ?>-->
        <!--<?php if (isset($var['obID'])) : ?>-->
            <!--<? renderButton('btnClose', 'Complete', 'onClick="return ajaxContainer(this.id, \'content\',\'?action=openingBalancesApReceiptsComplete&module=ap&command=invoice\')"'); ?>-->
            <!--<?php else : ?>-->
            <!--<? renderButton('btnClose', 'Complete', 'onClick="return ajaxCall(this.id, \'content\',\'?action=completeTransaction&module=ap&command=invoice\')"'); ?>-->
            <!--<?php endif; ?>-->
        <!--</td></tr>-->
        <!--</table>-->
        <? //} ?>


    <? } ?>


    <div id="dialog-form-recovered" class="dialog-form-recovered" title="Unlinked AR Transactions" style="display:none">
        <? if (count($var['arRecoverableTransactionList'] ?? []) != 0) { ?>
            <fieldset>
                <input type="text" id="search" placeholder="Filter AR">
                <table id="table" class="data-grid" style="background: #b1daef; border-collapse: collapse;">
                    <thead>
                    <tr class="subHeader">
                        <th>Select</th>
                        <th align="left">Lease Code</th>
                        <th align="left">Transaction Date</th>
                        <th align="left">Due Date</th>
                        <th align="left">Account</th>
                        <th align="left" style="width: 12%;">Description</th>
                        <th align="left">From Date</th>
                        <th align="left">To Date</th>
                        <th align="right">Net Amount</th>
                        <th align="right">Tax Amount</th>
                        <th align="right">Gross Amount</th>
                        <th align="right">AP Invoice No</th>
                        <th>Supplier</th>

                    </tr>
                    </thead>
                    <tbody>
                    <? foreach ($var['arRecoverableTransactionList'] as $key => $v) { ?>

                        <tr style="background: #fff;">
                            <td>
                                <input type="hidden" name="batch" class="batch_<? print_r($v['batch'] . $v['line']); ?>"
                                       value="<? print_r($v['batch']); ?>">
                                <input type="hidden" name="line" class="line_<? print_r($v['batch'] . $v['line']); ?>"
                                       value="<? print_r($v['line']) ?>">
                                <input id="arTransaction" data-batch="<? print_r($v['batch']) ?>"
                                       data-line="<? print_r($v['line']) ?>"
                                       data-amount="<? print_r($v['artr_net_amt']) ?>" class="check"
                                       name="arTransaction" value="" type="checkbox">
                                <? //=renderCheckBox ('arTransaction', '', false, $v['batch'], 'class="check"')?>
                            </td>
                            <td align="left"><? print_r($v['leaseCode']); ?></td>
                            <td align="left"><? print_r($v['trans_date']); ?></td>
                            <td align="left"><? print_r($v['due_date']); ?></td>
                            <td align="left"><? print_r($v['account']); ?></td>
                            <td align="left"><? print_r($v['description']); ?></td>
                            <td align="left"><? print_r($v['date_from']); ?></td>
                            <td align="left"><? print_r($v['date_to']); ?></td>
                            <td align="right"><?= toMoney($v['artr_net_amt']); ?></td>
                            <td align="right"><?= toMoney($v['artr_tax_amt']); ?></td>
                            <td align="right"><?= toMoney($v['trans_amt']); ?></td>
                            <td align="right"><? print_r($v['apRecoveredInvoiceNo']); ?></td>
                            <td><? print_r($v['apRecoveredSupplier']); ?></td>

                        </tr>

                    <? } ?>
                    <tbody>
                </table>
                Select :&nbsp;&nbsp;&nbsp;&nbsp;<a href="javascript:;" onClick="selectAll()"
                                                   style="color:#1E90FF;">All</a>&nbsp;&nbsp;&nbsp;<a
                    style="color:#1E90FF;" href="javascript:;" onClick="selectNone()">None</a>

            </fieldset>
        <? } else { ?>

            <span style="color:red;font-size:16px;text-align:center;visibility: hidden" id="noDR">No AR Direct Recovered Invoice</span>
        <? } ?>
    </div>

    <div id="dialog-form-linked-ar" title="Linked AR Transactions" style="display:none">
	<? if(count($var['arRecoverableTransactionList'] ?? [])!=0){?>
            <fieldset>
                <table id="table" class="data-grid" style="background: #b1daef; border-collapse: collapse;">
                    <thead>
                    <tr class="subHeader">
                        <th>Select</th>
                        <th align="left">Lease Code</th>
                        <th align="left">Transaction Date</th>
                        <th align="left">Due Date</th>
                        <th align="left">Account</th>
                        <th align="left" style="width: 12%;">Description</th>
                        <th align="left">From Date</th>
                        <th align="left">To Date</th>
                        <th align="right">Net Amount</th>
                        <th align="right">Tax Amount</th>
                        <th align="right">Gross Amount</th>
                        <th align="right">AR Invoice No</th>
                        <th>Supplier</th>

                    </tr>
                    </thead>
                    <tbody>
                    <? foreach ($var['arRecoverableTransactionList'] as $key => $v) { ?>

                        <tr style="background: #fff;">
                            <td>
                                <input id="arTransactions" data-batch="<? print_r($v['batch']) ?>"
                                       data-line="<? print_r($v['line']) ?>"
                                       data-amount="<? print_r($v['artr_net_amt']) ?>" class="check"
                                       name="arTransaction" value="" type="checkbox">
                            </td>
                            <td align="left"><? print_r($v['leaseCode']); ?></td>
                            <td align="left"><? print_r($v['trans_date']); ?></td>
                            <td align="left"><? print_r($v['due_date']); ?></td>
                            <td align="left"><? print_r($v['account']); ?></td>
                            <td align="left"><? print_r($v['description']); ?></td>
                            <td align="left"><? print_r($v['date_from']); ?></td>
                            <td align="left"><? print_r($v['date_to']); ?></td>
                            <td align="right"><?= toMoney($v['artr_net_amt']); ?></td>
                            <td align="right"><?= toMoney($v['artr_tax_amt']); ?></td>
                            <td align="right"><?= toMoney($v['trans_amt']); ?></td>
                            <td align="right"><? print_r($v['artr_gst_inv_no']); ?></td>
                            <td><? print_r($v['apRecoveredSupplier']); ?></td>

                        </tr>

                    <? } ?>
                    <tbody>
                </table>

            </fieldset>
        <? } else { ?>

            <span style="color:red;font-size:16px;text-align:center;visibility: hidden" id="noDR">No AR Direct Recovered Invoice</span>
        <? } ?>
    </div>

    <div id="dialog-vo-notes" class="modal-custom vo-notes-dialog">
        <!-- Modal content -->
        <div class="modal-content">
            <div class="modal-header">
                <h4><?= ucwords(strtolower($_SESSION['country_default']['variable_outgoings'])); ?> Notes for <span
                        class="notes-owner"></span></h4>
                <span class="close">&times;</span>
            </div>
            <div class="modal-body">
                <div class="column">
                    <table id="vo-notes-table" class="data-grid row-border hover order-column dataTable no-footer">
                        <thead>
                        <th data-orderable="false">&nbsp;</th>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <div id="dialog-supp-recon" class="modal-custom supp-recon-dialog">
        <!-- Modal content -->
        <div class="modal-content">
            <div class="modal-header">
                <h4>Supplier Reconciliation (<span class="supplier-code"></span> - <span class="property-code"></span>)
                </h4>
                <span class="close">&times;</span>
            </div>
            <div class="modal-body">
                <div class="column">
                    <table id="supp-recon-table" class="data-grid row-border hover dataTable no-footer">
                    </table>
                </div>
            </div>
        </div>
    </div>

    <div>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/datatables.net/2.1.7/dataTables.min.js"
                integrity="sha512-uQwfH1NYeXU6Whr3PrKxExRgtnfVkcs30HKaAQtReHSVwQv040Spq22cZo1MaQZshLDTuIncxqWJfIVXyr/GSQ=="
                crossorigin="anonymous" referrerpolicy="no-referrer"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/datatables.net-buttons/3.2.0/js/dataTables.buttons.min.js"
                integrity="sha512-/iOthDtoEAAT8XBHzVM0DDacmcGS/3C2QRrGW5Q10S3W8RpeEbK65/WBdjeJtmzVcg1dAwnDceqCuP92HV4Kyw=="
                crossorigin="anonymous" referrerpolicy="no-referrer"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"
                integrity="sha512-XMVd28F1oH/O71fzwBnV7HucLxVwtxf26XV8P4wPk26EDxuGZ91N8bsOttmnomcCD3CS5ZMRL50H0GgOHvegtg=="
                crossorigin="anonymous" referrerpolicy="no-referrer"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/datatables.net-buttons/3.2.0/js/buttons.html5.min.js"
                integrity="sha512-OrwOX7sUH8/wOI5I8G+Xy0Q5lIM+BT05blMElksG0FPlWDfjrAOkt5TH0PzTnblD0kJgIlC4KvaRTfPtXsNlGg=="
                crossorigin="anonymous" referrerpolicy="no-referrer"></script>

        <script type="text/javascript" language="javascript">

            //******for jquery UI Modal******

            var myFocus = function () {
                ajaxContainer(null, 'content', '?module=ap&command=invoice');
            };

            $('#createAndAttachAgentInvoice').click(function () {
                checkCreateAgentInvoice();
            });

            $(function () {
                /**for ap recoverable*****/
                $('#noDR').css('visibility', 'visible');
                var dialog, form, dialog_linked_ar;
                dialog = $("#dialog-form-recovered").dialog({
                    autoOpen: false,
                    height: 400,
                    width: 1100,
                    modal: true,
                    buttons: {

                        "Link Transaction": function () {
                            var numberOfChecked = $('#dialog-form-recovered input[type=checkbox]:checked').length;
                            $('input[name=jsonAr]').val('');
                            $('#apRecoveredAmount').val('');
                            if (numberOfChecked > 0) {

                                var jsonObj = [];
                                var arrAmount = 0.00;
                                var stringAmount = '';
                                $('#dialog-form-recovered input[type=checkbox]').each(function () {

                                    if (this.checked) {
                                        var item = {}
                                        item ["batch"] = $(this).data('batch');
                                        item ["line"] = $(this).data('line');
                                        jsonObj.push(item);
                                        arrAmount = arrAmount + parseFloat($(this).data('amount'));

                                    }
                                });

                                $('#dialog-form-recovered input[type=checkbox]').prop('checked', false);

                                $('input[name=jsonAr]').val(JSON.stringify(jsonObj));
                                //$('#grossAmount').val(arrAmount.toFixed(2));
                                dialog.dialog("close");

                            } else {

                                //$('#grossAmount').val('0.00');
                                $('input[name=jsonAr]').val('');
                                //$('#divCheckBox').css('display','none');
                                dialog.dialog("close");

                            }

                            //myFocus();
                        },
                        "Close": function () {
                            $('#dialog-form-recovered input[type=checkbox]').prop('checked', false);
                            dialog.dialog("close");
                        }
                    },
                    open: function () {


                    },
                    close: function () {

                    }
                });

                dialog_linked_ar = $("#dialog-form-linked-ar").dialog({
                    autoOpen: false,
                    height: 400,
                    width: 1100,
                    modal: true,
                    buttons: {

                        "Close": function () {
                            dialog_linked_ar.dialog("close");
                        }
                    }

                });

                $('.ui-dialog div#dialog-form-recovered').each(function (index) {
                    if (index > 0) {
                        $("#dialog-form-recovered").dialog('destroy').remove();
                    }
                });
                $('#arRecovered').on("click", function (e) {
                    e.preventDefault();

                    var data = $('input[name=jsonAr]').val();

                    if (!data || data == '') {
                        var obj = jQuery.parseJSON(null);
                    } else {
                        var obj = jQuery.parseJSON(data);
                    }

                    if (obj) {
                        $.each(obj, function (idx, obje) {
                            if (idx >= 0) {
                                if (obje.batch != null) {
                                    $('#dialog-form-recovered input[type=checkbox]').each(function () {
                                        if ($(this).data('batch') == obje.batch && $(this).data('line') == obje.line) {
                                            $(this).prop('checked', true);
                                        }
                                    });
                                }
                            }
                        });

                    }
                    dialog.dialog("open");

                });

                $('.ui-dialog div#dialog-form-linked-ar').each(function (index) {
                    if (index > 0) {
                        $("#dialog-form-linked-ar").dialog('destroy').remove();
                    }
                });
                $('table').on("click", "a#linkedAR", function () {

                    var data_ = $(this).data('json_ar');
                    if (data_) {
                        var $rows = $('#dialog-form-linked-ar #table tbody tr');
                        $rows.hide();
                        $.each(data_, function (idx, obje) {
                            if (idx >= 0) {
                                if (obje.batch != null) {
                                    $('#dialog-form-linked-ar input[type=checkbox]').each(function () {

                                        if ($(this).data('batch') == obje.batch && $(this).data('line') == obje.line) {
                                            $(this).closest("tr").show();
                                            $(this).prop('checked', true);
                                            $(this).prop('disabled', true);
                                        }
                                    });
                                }
                            }
                        });

                    }
                    dialog_linked_ar.dialog("open");

                });


                /***for ar transaction checkbox search***/
                var $rows = $('#table tbody tr:not(:first-child)');
                $('#dialog-form-recovered input#search').keyup(function () {

                    var val = $.trim($(this).val()).replace(/ +/g, ' ').toLowerCase();
                    $rows.show().filter(function () {
                        var text = $(this).text().replace(/\s+/g, ' ').toLowerCase();
                        return !~text.indexOf(val);
                    }).hide();
                });


                /***************end********************/


                    //******for AP Transaction Process******
                var invoiceLinesJson = [];
                var appendedFiles = JSON.parse($('#appendFiles').val());

                var html = function () {
                    var jsonInput = $('#invoiceLinesJson').val();
                    var data = (jsonInput != '') ? JSON.parse(jsonInput) : [];

                    if (data.length > 0) {
                        var tr = '<tr class="fieldDescription"><td style="width:6%;">Hold Invoice</td><td>Invoice Note</td><td>Line Number</td><td>Transaction Date</td><td>Property</td><td>Account</td><td>From Date</td><td>To Date</td><td>Description</td><td>Unit</td><td>Sub-Ledger</td><td class="right">Net Amount</td><td class="right">Tax Amount</td><td class="right">Gross Amount</td><td width="1%">&nbsp;</td></tr>';
                        var net = 0;
                        var tax = 0;
                        var trans = 0;

                        $.each(data.reverse(), function (key, value) {
                            value.netAmount = parseFloat(value.netAmount).toFixed(2);
                            value.taxAmount = parseFloat(value.taxAmount).toFixed(2);
                            value.transactionAmount = parseFloat(value.transactionAmount).toFixed(2);

                            var checked = '';
                            var voNotesBtn = '';
                            var suppReconBtn = '';
                            var invoiceLineNote = '<td></td>';

                            if (value.holdInvoice == 'Y') {
                                checked = 'checked'
                            }
                            var linkedAr = (value.jsonAr) ? 'Linked AR' : '';

                            if (value?.invoiceNote) {
                                invoiceLineNote = '<td id="invoiceNote"><span class="line-note" title="' + escapeAllSpecialChars(value.invoiceNote) + '"><i class="fa fa-file-text"></i></span></td>';
                            }

                            tr = tr + '<tr>' +
                                '<td id="holdInvoice"><input id="holdInvoice" ' + checked + ' class="hold-ap-check" data-toggle="target-table" data-batch="' + value.batchNumber + '" data-line="' + key + '" name="holdInvoice" value="1" type="checkbox"><a href="#" id="linkedAR"  data-json_ar=' + value.jsonAr + ' style="float:right;margin-right:15px;">' + linkedAr + '</a></td>' +
                                invoiceLineNote +
                                '<td id="lineNumber">' + parseInt(key + 1) + '</td>' +
                                '<td id="transactionDate">' + value.transactionDate + '</td>' +
                                '<td id="propertyID">' + value.propertyID + '</td>' +
                                '<td id="accountID"><strong>' + value.accountID + '</strong></td>' +
                                '<td id="fromDate">' + value.fromDate + '</td>' +
                                '<td id="toDate">' + value.toDate + '</td>' +
                                '<td id="description">' + value.description + '</td>' +
                                '<td id="unitID">' + ((value.unitID == null) ? 'N/A' : value.unitID) +
                                '<td id="leaseID">' + ((value.leaseID == null) ? 'N/A' : value.leaseID) + '</td></td><td id="netAmount" class="right">' + value.netAmount + '</td><td id="taxAmount" class="right">' + value.taxAmount + '</td><td id="transactionAmount" class="right">' + value.transactionAmount + '</td><td width="1%"><a data-batch="' + value.batchNumber + '" data-line="' + key + '"  href="#" id="removeLine"><img src="<?=ASSET_DOMAIN?>assets/images/icons/delete.png" /></a></td></tr>';

                            net = net + parseFloat(value.netAmount);
                            tax = tax + parseFloat(value.taxAmount);
                            trans = trans + parseFloat(value.transactionAmount);
                        });

                        tr = tr + '<tr class="highlight"><td colspan="11">&nbsp;</td><td class="right">' + toMoney2(net) + '</td><td class="right">' + toMoney2(tax) + '</td> <td class="right">' + toMoney2(trans) + '</td> <td width="1%">&nbsp;</td> </tr>';
                        tr = tr + '<tr class="footer"><td colspan="15">' +
                            '<button name="btnPay" id="btnPay" class="ui-button ui-widget ui-state-default ui-corner-all ui-button-text-only" role="button" aria-disabled="false"><span class="ui-button-text">Pay Now →</span></button>&nbsp;' +
                            <? if ($_SESSION['readOnlyAccess'] != 1) { ?>
                            '<button name="btnCharge" id="btnCharge"  class="ui-button ui-widget ui-state-default ui-corner-all ui-button-text-only" role="button" aria-disabled="false"><span class="ui-button-text">On-charge Tenants →</span></button>&nbsp;' +
                            <? } ?>
                            '<button name="btnClose" id="btnClose"  class="ui-button ui-widget ui-state-default ui-corner-all ui-button-text-only" role="button" aria-disabled="false"><span class="ui-button-text">Complete</span></button>' +
                            '</td></tr>';

                        $('#invoiceLine').html(tr);
                        $('#file-rw').css('visibility', 'visible');
                    }

                    const $noteTextArea = $('#invoiceNote');
                    const $warning = $('#charWarning');
                    const maxLength = parseInt($noteTextArea.prop('maxLength'), 10);

                    function handleTextareaInput(event) {
                        const currentLength = $noteTextArea.val().length;
                        const isValueAtMaxLength = currentLength >= maxLength;

                        if (isValueAtMaxLength) {
                            event.preventDefault();
                        }

                        $warning.toggleClass('hidden', !isValueAtMaxLength);
                    }

                    $noteTextArea.on('input', handleTextareaInput);

                    if (appendedFiles.length > 0) {
                        $('#divCheckBox').css('visibility', 'visible');
                        $('#divCheckBox2').css('visibility', 'visible');
                        $('#file-rw').css('visibility', 'visible');
                    }

                    $('.voNotesBtn').on('click', function () {
                        var modal = $('#dialog-vo-notes');
                        var property = $(this).data('property');

                        modal.find('.notes-owner').text(property);
                        modal.find('#vo-notes-table tbody').html($(this).data('notes'));
                        modal.css('display', 'block');

                        if ($('#vo-notes-table').DataTable() != null) {
                            $('#vo-notes-table').DataTable().destroy();
                        }

                        $('#vo-notes-table').DataTable({
                            "aLengthMenu": [[10, 25, 50, 75, -1], [10, 25, 50, 75, "All"]],
                            sdom: "tlp",
                            "ordering": false,
                            "orderable": false,
                            "searching": false,
                            buttons: [],
                            "aoColumnDefs": [
                                {"bSortable": false, "aTargets": [0]},
                                {"bSearchable": false, "aTargets": [0]}
                            ],
                            "drawCallback": function (settings) {
                                $('#vo-notes-table thead').remove();
                            },
                            "oLanguage": {
                                "sEmptyTable": "No <?= ucwords(strtolower($_SESSION['country_default']['variable_outgoings'])) ?> notes available for this Property"
                            }
                        });
                    });
                }

                var addLine2 = function () {
                    //ajaxContainer(null, 'content','?action=addLine&module=ap&command=invoice');
                    if (typeof window.tinyMCE != "undefined") window.tinyMCE.triggerSave();
                    $.ajax('?action=addLine2&module=ap&command=invoice', {

                        success: function (data) {
                            $('#loading').css('display', 'none');
                            $('#file-uploader').css('display', 'block');
                            try {
                                $('#validationTable').html("");
                                if (JSON.parse(data).transaction) {
                                    invoiceLinesJson.push(data);

                                    var jsonString = [];
                                    if (invoiceLinesJson.length > 0) {
                                        var obj = JSON.parse(data);
                                        $('#batchNumber').val(obj.batch);
                                        $('#documentBatchNumber').val(obj.documentBatch);
                                        jsonString.push(obj.transaction);

                                        var jsonInput = $('#invoiceLinesJson').val();
                                        var dt = (jsonInput != '') ? JSON.parse(jsonInput) : [];

                                        if (dt.length > 0) {
                                            $.each(dt, function (key, value) {
                                                jsonString.push(value);
                                            });
                                        }

                                        $('input[name=invoiceLinesJson]').val(JSON.stringify(jsonString));
                                        html();
                                    }
                                    $('td textarea[name="invoiceNote"]').val('');
                                } else {
                                    var list = '<tbody><tr class="warning"><td colspan="10"><a id="error_list" name="error_list"></a><ul>';
                                    $.each(JSON.parse(data), function (key, value) {
                                        list = list + '<li>' + value + '</li>';
                                    });
                                    list = list + '</ul></td></tr></tbody>';
                                    $('#validationTable').html(list);
                                    $('#validationTable').addClass('data-grid');
                                }
                                $('input[name=jsonAr]').val('');// reset ar linked after process
                            } catch (e) {
                                var list = '<tbody><tr class="warning"><td colspan="10"><a id="error_list" name="error_list"></a><ul>';
                                $('input[name=invoiceLinesJson]').val(null);
                                $.each(JSON.parse(data), function (key, value) {
                                    list = list + '<li>' + value + '</li>';
                                });
                                list = list + '</ul></td></tr></tbody>';
                                $('#validationTable').html(list);
                                $('#validationTable').addClass('data-grid');
                            }
                        },
                        error: function (jqXHR, textStatus, errorThrown) { /*AWWW... JSON parse error*/
                        },
                        asynchronous: true,
                        evalScripts: true,
                        type: 'post',
                        data: $.param(serializeContainer('content'))
                    });

                };

                var x = "<?php echo $var['taxRateID']; ?>";
                var y = "<?php echo $var['propertyTaxID']; ?>";
                var obID = $('#obID').val();
                $('#btnAdd').on("click", function (e) {

                    $('button[name="btnAdd"]').prop('disabled', true);
                    e.preventDefault();
                    //ajaxContainer(null, 'content','?action=addLine&module=ap&command=invoice');
                    if (x != y) {
                        modalConfirm('The property and selected tax rates do not match - are you sure you wish to proceed?', function () {
                            $('#loading').css('display', 'block');
                            addLine2();
                        }, null);
                    } else {
                        $('#loading').css('display', 'block');
                        addLine2();
                    }
                    setTimeout(function () {
                        $('button[name="btnAdd"]').prop('disabled', false);
                    }, 1000);

                });

                $('table').on("click", "input#holdInvoice", function () {
                    var check = 'N';
                    var id = '#invoiceLinesJson';
                    if ($(this).is(':checked')) {
                        check = 'Y';
                    }
                    var jsonInput = $(id).val();
                    var data = (jsonInput !== '') ? JSON.parse(jsonInput) : [];
                    data.reverse()[$(this).data('line')]['holdInvoice'] = check;
                    data.reverse();
                    $(id).val(JSON.stringify(data));
                    //return ajaxContainer(null,'content','?command=invoice&module=ap&action=holdInvoice&batchNumber='+$(this).data('batch')+'&isChecked='+check+'&lineNumber='+$(this).data('line')+'');
                });
                $('table').on("click", "a#removeLine", function () {

                    return ajaxContainer(null, 'content', '?command=invoice&module=ap&action=delete1&batchNumber=' + $(this).data('batch') + '&lineNumber=' + $(this).data('line') + '');

                });
                $('table').on("click", "button#btnPay", function () {

                    ajaxContainer(null, 'content', '?action=payNow&module=ap&command=invoice');
                    //$('#uploadForm').submit();
                });
                $('table').on("click", "button#btnCharge", function () {

                    ajaxContainer(null, 'content', '?action=onCharge&module=ap&command=invoice');
                    //$('#uploadForm').submit();
                });
                $('table').on("click", "button#btnClose", function () {
                    $('button[name="btnClose"]').prop('disabled', true);
                    $('#loading').css('display', 'block');
                    if (typeof obID !== 'undefined') {
                        ajaxContainer(null, 'content', '?action=openingBalancesApReceiptsComplete&module=ap&command=invoice');
                    } else {
                        $.ajax('?action=completeTransaction1&module=ap&command=invoice', {
                            success: function (data) {
                                if (data) {
                                    $('#loading').css('display', 'none');
                                    ajaxCall(null, 'content', '?action=completeTransaction&module=ap&command=invoice');
                                }

                            },
                            error: function (jqXHR, textStatus, errorThrown) { /*AWWW... JSON parse error*/
                            },
                            asynchronous: true,
                            evalScripts: true,
                            type: 'post',
                            data: $.param(serializeContainer('content'))
                        });
                    }

                    setTimeout(function () {
                        $('button[name="btnAdd"]').prop('disabled', false);
                    }, 1000);
                });
                html();
                /***************end********************/

                /***enable fileuploader plugin****/

                $('input[name="invoiceFile"]').fileuploader({
                    addMore: true,
                    files: appendedFiles,
                    maxSize: 5,
                    fileMaxSize: 5,
                    extensions: ['pdf'],
                    captions: {
                        feedback: function (options) {
                            return ' 	Attach an  Invoice/Credit ' + (options.limit == 1 ? 'file' : 'files') + ' to upload';
                        },
                    },
                    upload: {
                        url: '?module=ap&command=invoice&action=uploadFile&sc=with-file-upload',
                        data: null,
                        type: 'POST',
                        enctype: 'multipart/form-data',
                        start: true,
                        synchron: true,
                        beforeSend: function (item) {
                            //var input = $('#attachAR');

                            if ($('#attachAR').prop("checked") == true) {
                                item.upload.data.attach_ar = 1;
                            } else {
                                item.upload.data.attach_ar = 0;
                            }
                            if ($('#attachOwnerR').prop("checked") == true) {
                                item.upload.data.attachOwnerR = 1;
                            } else {
                                item.upload.data.attachOwnerR = 0;
                            }
                            item.upload.data.invoiceNumber = $('#invoiceNumber').val();
                            item.upload.data.propertyID = $('#propertyID').val();
                            item.upload.data.batchNumber = $('#batchNumber').val();
                            item.upload.data.transactionDate = $('#transactionDate').val();
                            item.upload.data.creditorID = $('#creditorID').val();
                            item.upload.data.documentBatchNumber = $('#documentBatchNumber').val();
                            item.upload.data.fileName = item.file.name;
                            $('button[name="btnClose"]').prop('disabled', true);
                            // set the POST field
                            //if(input.length)
                            //    item.upload.data.attach_ar = input.val();
                            // console.log(item.upload.data.attach_ar);
                            // reset input value
                            // input.val("");

                        },
                        onSuccess: function (result, item) {
                            var data = JSON.parse(result),
                                nameWasChanged = false;
                            // get the new file name

                            if (data.isSuccess && data.files[0]) {
                                nameWasChanged = item.name != data.files[0].name;
                                var ret = data.files[0].name.split("_");
                                var len = (ret.length - 1);
                                var newName = ret.splice(0, len);
                                var attachInAr = '';
                                if (data.attachAR == 1) {
                                    attachInAr = 'Attach in related AR  --  ';
                                }
                                if (data.attachOwnerR == 1) {
                                    attachInAr = 'Attach to owner report  --  ';
                                }
                                item.name = attachInAr + newName.join('_').replace(".pdf", "");
                                item.data = {documentID: data.documentID, fileName: data.files[0].name};
                                $('#divCheckBox').css('visibility', 'visible');
                                $('#divCheckBox2').css('visibility', 'visible');
                            }
                            // make HTML changes
                            if (nameWasChanged)
                                item.html.find('.column-title div').animate({opacity: 0}, 400);
                            item.html.find('.column-actions').append('<a class="fileuploader-action fileuploader-action-remove fileuploader-action-success" title="Remove"><i></i></a>');
                            setTimeout(function () {
                                item.html.find('.column-title div').attr('title', item.name).text(item.name).animate({opacity: 1}, 400);
                                item.html.find('.progress-bar2').fadeOut(400);
                            }, 400);
                            $('button[name="btnClose"]').prop('disabled', false);
                        },
                        onError: function (item) {
                            var progressBar = item.html.find('.progress-bar2');
                            // make HTML changes
                            if (progressBar.length > 0) {
                                progressBar.find('span').html(0 + "%");
                                progressBar.find('.fileuploader-progressbar .bar').width(0 + "%");
                                item.html.find('.progress-bar2').fadeOut(400);
                            }

                            item.upload.status != 'cancelled' && item.html.find('.fileuploader-action-retry').length == 0 ? item.html.find('.column-actions').prepend(
                                '<a class="fileuploader-action fileuploader-action-retry" title="Retry"><i></i></a>'
                            ) : null;
                        },
                        onProgress: function (data, item) {
                            var progressBar = item.html.find('.progress-bar2');

                            // make HTML changes
                            if (progressBar.length > 0) {
                                progressBar.show();
                                progressBar.find('span').html(data.percentage + "%");
                                progressBar.find('.fileuploader-progressbar .bar').width(data.percentage + "%");
                            }
                        },
                        onComplete: null,
                    },
                    onRemove: function (item) {
                        // send POST request

                        $.post('?module=ap&command=invoice&action=removeFile', {
                            documentID: item.data.documentID,
                            fileName: item.data.fileName

                        }, function (data) {
                            if (data <= 0) {
                                $('#divCheckBox').css('visibility', 'hidden');
                                $('#divCheckBox2').css('visibility', 'hidden');
                            }
                        });

                    }

                });
            });

            $("#accountID").keydown(function (e) {
                if (e.which == 9)
                    return ajaxContainer(this.id, 'content', '?module=ap&command=invoice&nextTab=description');
            });

            var selectNone = function () {
                $('#dialog-form-recovered input[type=checkbox]:checked').each(function () {
                    $(this).removeAttr('checked');
                });
            }

            var selectAll = function () {
                $('#dialog-form-recovered input[type=checkbox]').each(function () {
                    $(this).attr('checked', 'checked');
                });
            }

            function closeIt() {
                var jsonInput = $('#invoiceLinesJson').val();
                var data = (jsonInput != '') ? JSON.parse(jsonInput) : [];
                if (data.length > 0) {
                    return "Any string value here forces a dialog box to \n" +
                        "appear before closing the window.";
                }

            }

            function ajaxContainerV2(content) {
                var val = $(content).val();
                if (val.length >= 4) {
                    ajaxContainer(null, 'content', '?action=checkOrder&module=ap&command=invoice&nextTab=transactionDate');
                }
            }

            function checkFutureDate(date, spanMsgId) {
                var getDate = $(date).val();
                getDate = getDate.split('/');
                var datepicker = new Date(getDate[1] + '/' + getDate[0] + '/' + getDate[2]);
                datepicker.setHours(0, 0, 0, 0);
                var now = new Date();
                now.setHours(0, 0, 0, 0);
                if (datepicker > now) {
                    $("#" + spanMsgId).html("Please note: Your transaction date is in the future.");
                } else {
                    $("#" + spanMsgId).html("");
                }
            }

            function checkIfExisted(content, type) {
                var value = $(content).val();
                var creditorID = $("#creditorID").val();
                var formData = new FormData();
                formData.append('type', type);
                formData.append('invoiceNumber', value);
                formData.append('creditorID', creditorID);
                $('#validationTable').html("");
                $("#previousInvoicesTable").addClass('hidden');
                $("#previousInvoicesTableContent").html('');
                $('#loading').css('display', 'block');
                $.ajax({
                    type: "POST",
                    processData: false,
                    contentType: false,
                    data: formData,
                    url: "index.php?command=checkIfExisted&module=ap",
                    success: function (result) {
                        try {
                            var data = JSON.parse(result);
                            if (data.errorMsg) {
                                var list = '<tbody><tr class="warning"><td colspan="10"><a id="error_list" name="error_list"></a><ul>';
                                $.each(data.errorMsg, function (key, value) {
                                    list = list + '<li>' + value + '</li>';
                                });
                                list = list + '</ul></td></tr></tbody>';
                                $('#validationTable').html(list);
                                $('#validationTable').addClass('data-grid');
                            }
                            if (data.existedInvoices) {
                                var content = "";
                                $.each(data.existedInvoices, function (key, value) {
                                    content = content + "<tr>" +
                                        "<td>" + value['propertyID'] + "</td>" +
                                        "<td>" + value['transactionDate'] + "</td>" +
                                        "<td>" + value['accountID'] + "</td>" +
                                        "<td>" + value['description'] + "</td>" +
                                        "<td>" + value['fromDate'] + " to " + value['toDate'] + "</td>" +
                                        "<td>" + value['dueDate'] + "</td>" +
                                        "<td align='right'>" + value['transactionAmount'] + "</td>" +
                                        "<td align='right'>" + value['netAmount'] + "</td>" +
                                        "<td align='right'>" + value['taxID'] + "</td>" +
                                        "<td align='right'>" + value['taxAmount'] + "</td>" +
                                        "</tr>";
                                });
                                $("#previousInvoicesTableContent").html(content);
                            }
                            $("#previousInvoicesTable").removeClass('hidden');
                        } catch (e) {
                            //console.log(e);
                        }
                        $('#loading').css('display', 'none');
                    },
                });
            }

            function checkCreateAgentInvoice() {
                if ($('#createAndAttachAgentInvoice').prop("checked") == true) {
                    $('#invoiceNumber').val('');
                    $('#invoiceNumber').attr('readonly', 'readonly');
                    //$('#invoiceNumber').attr('style','background-color:#EBEBE4 !important');
                    $('#invoiceNumber').addClass('input-disabled');

                    $('.EftDetails').show();
                    $('.EftDetails2').hide();

                    hideShowGSTOnInvoice();
                } else if ($('#createAndAttachAgentInvoice').prop("checked") == false) {
                    $('#invoiceNumber').removeAttr('readonly');
                    //$('#invoiceNumber').attr('style','background-color:white !important');
                    $('#invoiceNumber').removeClass('input-disabled');

                    $('.EftDetails').hide();
                    $('.EftDetails2').show();

                    hideShowGSTOnInvoice();
                }
            }

            function hideShowGSTOnInvoice() {
                if ($('#createAndAttachAgentInvoice').prop("checked") == true) {
                    $('.showGSTOnInvoice').show();
                } else {
                    $('.showGSTOnInvoice').hide();
                }
            }

            function escapeAllSpecialChars(str) {
                return str.replace(/[^\w\s]/g, function (char) {
                    return '&#' + char.charCodeAt(0) + ';';
                });
            }

            $("#dialog-vo-notes .close").click(function () {
                var modal = $("#dialog-vo-notes");
                modal.css("display", "none");

                if ($('#vo-notes-table').DataTable() != null) {
                    $('#vo-notes-table').DataTable().destroy();
                }

                modal.find('#vo-notes-table tbody').html();
            });

            $(".sr-acct-code").on('click', function () {
                var modal = $(this).data('modal');
                var accountCode = $(this).data('code');

                $('#' + modal).css("display", "none");

                $('#accountID').val(accountCode);

                return ajaxContainer('accountID', 'content', '?action=updateAccounts&module=ap&command=invoice&nextTab=description');
            });

            checkCreateAgentInvoice();

            window.onbeforeunload = closeIt;

        </script>

<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="content-type" content="text/html; charset=utf-8" /><!-- ISO-8859-1 -->
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=11">
    <title>Cirrus8: Online Commercial Property Management Solution - <?=$var['Master::Section']?></title>
    <link rel="icon" type="image/vnd.microsoft.icon" href="favicon.ico" />
    <!-- NEW FORMAT remove JS_VERSION for js & css libraries  -->

    <link rel="stylesheet" href="<?=FA_URL_CSS?>">

    <link type="text/css" href="<?=ASSET_DOMAIN?>assets/css/app.css?<?=CSS_VERSION?>" rel="stylesheet" />
    <link rel="stylesheet" href="<?=ASSET_DOMAIN?>assets/css/core.css?<?=JS_VERSION?>" type="text/css" />
    <link rel="stylesheet" type="text/css" href="<?=ASSET_DOMAIN?>assets/css/lightview.min.css?<?=JS_VERSION?>" />
    <link type="text/css" href="<?=ASSET_DOMAIN?>assets/css/stylesheet.css?<?=CSS_VERSION?>" rel="stylesheet" />
    <link type="text/css" href="<?=ASSET_DOMAIN?>assets/chosen_v1.6.2/chosen.min.css" rel="stylesheet" />
    <link type="text/css" href="<?=ASSET_DOMAIN?>assets/css/chat.min.css" rel="stylesheet" />
    <link type="text/css" href="<?=ASSET_DOMAIN?>assets/css/screen.css?<?=CSS_VERSION?>" rel="stylesheet" />
    <link type="text/css" href="<?=ASSET_DOMAIN?>assets/css/fg.menu.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jqueryui/1.13.2/themes/base/jquery-ui.min.css" integrity="sha512-ELV+xyi8IhEApPS/pSj66+Jiw+sOT1Mqkzlh8ExXihe4zfqbWkxPRi8wptXIO9g73FSlhmquFlUOuMSoXz5IRw==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <link type="text/css" href="<?=ASSET_DOMAIN?>assets/semantic-ui/components/menu.min.css" rel="stylesheet" />
    <link type="text/css" href="<?=ASSET_DOMAIN?>assets/css/master_layout.css?<?=CSS_VERSION?>" rel="stylesheet" />
    <link type="text/css" href="<?=ASSET_DOMAIN?>assets/semantic-ui/components/sidebar.min.css" rel="stylesheet" />
    <link type="text/css" href="<?=ASSET_DOMAIN?>assets/semantic-ui/components/icon.min.css" rel="stylesheet" />
    <link href="https://fonts.googleapis.com/css?family=Roboto:400,700" rel="stylesheet">
    <link href="<?=ASSET_DOMAIN?>assets/file-uploader/jquery.fileuploader.min.css" media="all" rel="stylesheet">
    <link type="text/css" rel="stylesheet" media="all" href="<?=ASSET_DOMAIN?>assets/css/dark.css?<?=CSS_VERSION?>" />
    <link type="text/css" media="all" href="assets/css/vo-notes.css?<?=CSS_VERSION?>" rel="stylesheet" type="text/css"/>
    <link type="text/css" href="<?=ASSET_DOMAIN?>assets/multiselect-master/css/jquery.uix.multiselect.css?<?=CSS_VERSION?>" rel="stylesheet"/>
    <link type="text/css" href="<?=ASSET_DOMAIN?>assets/semantic-ui/semantic.css?<?=CSS_VERSION?>" rel="stylesheet" />

    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.1/jquery.min.js" integrity="sha512-v2CJ7UaYy4JwqLDIrZUI/4hqeoQieOmAZNXBeQyjo21dadnwR+8ZaIJVT8EE2iyI61OV8e6M8PP2/4hpQINQ/g==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <script type="text/javascript" src="<?=ASSET_DOMAIN?>assets/js/jquery-migrate.js"></script>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/jqueryui/1.13.2/jquery-ui.min.js" integrity="sha512-57oZ/vW8ANMjR/KQ6Be9v/+/h6bq9/l3f0Oc7vn6qMqyhvPd1cvKBRWWpzu0QoneImqr2SkmO4MSqU+RpHom3Q==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <script type="text/javascript" src="<?=ASSET_DOMAIN?>assets/js/fg.menu.min.js"></script>
    <script type="text/javascript" src="<?=ASSET_DOMAIN?>assets/js/validation.js?<?=JS_VERSION?>"></script>

    <script type="text/javascript" src="<?=ASSET_DOMAIN?>assets/js/addon/periodicalupdater.min.js?<?=JS_VERSION?>"></script>
    <script type="text/javascript" src="<?=ASSET_DOMAIN?>assets/js/date.js?<?=JS_VERSION?>"></script> <!-- ui above need to run date.js .datepicker-->
    <script type="text/javascript" src="<?=ASSET_DOMAIN?>assets/js/addon/jquery.serializeall.min.js?<?=JS_VERSION?>"></script>

    <script type="text/javascript" src="<?=ASSET_DOMAIN?>assets/js/core.js?<?=JS_VERSION?>"></script>
    <script type="text/javascript" src="<?=ASSET_DOMAIN?>assets/js/custom.js?<?=JS_VERSION?>"></script>
    <script type="text/javascript" src="<?=ASSET_DOMAIN?>assets/js/loader.js?<?=JS_VERSION?>"></script>
    <script type="text/javascript" src="<?=ASSET_DOMAIN?>assets/js/dropdown.js?<?=JS_VERSION?>"></script>
    <script type="text/javascript" src="<?=ASSET_DOMAIN?>assets/js/smart_date.min.js?<?=JS_VERSION?>"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.30.1/moment.min.js" integrity="sha512-QoJS4DOhdmG8kbbHkxmB/rtPdN62cGWXAdAFWWJPvUFF1/zxcPSdAnn4HhYZSIlVoLVEJ0LesfNlusgm2bPfnA==" crossorigin="anonymous"></script>

    <script type="text/javascript" src="<?=ASSET_DOMAIN?>assets/js/accounting.min.js?<?=JS_VERSION?>"></script>
    <script type="text/javascript" src="<?=ASSET_DOMAIN?>assets/chosen_v1.6.2/chosen.jquery.min.js"></script>
    <script type="text/javascript" src="<?=ASSET_DOMAIN?>assets/multiselect-master/js/jquery.uix.multiselect.js"></script>
    <script type="text/javascript" src="<?=ASSET_DOMAIN?>assets/semantic-ui/components/sidebar.min.js?<?=JS_VERSION?>"></script>

    <script type="text/javascript" src="<?=ASSET_DOMAIN?>assets/js/mobile-optimisation.js?<?=JS_VERSION?>"></script>


    <meta name="csrf-token" content="<?=$_SESSION['CSRF-X-CODE']?>">
    <script>$.ajaxSetup({headers: { 'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content') }});</script>
    <?=renderAnnounceKitWidgetData()?>
    <?=Bugsnag::render()?>
</head>

<body ng-app="CirrusApp">
    <script type="text/javascript">
        if (localStorage.hasOwnProperty('dark_mode_setting') && localStorage.getItem('dark_mode_setting') == 'false') {
            $('body').removeClass('c8-dark');
        } else if (localStorage.hasOwnProperty('dark_mode') && localStorage.getItem('dark_mode') == 'true') {
            $('body').addClass('c8-dark');
            $('#darkMode').html('<i class="fas fa-sun"></i> Disable Dark Mode');
        }
    </script>
<div id="goTopBtn" onclick="topFunction()">
    <img src="<?=ASSET_DOMAIN?>assets/images/icons/arrow-left-01-512.png" id="goTopBtnImgLeft" class="goTopBtnImgLeft"/>
    <img src="<?=ASSET_DOMAIN?>assets/images/icons/arrow-up-01-512.png" class="goTopBtnImg"/>
</div>
<div id="ngLoader-UI" class="" style="display: none;opacity: 0.9;left:0px;top:0px;color: white;background: white;position: fixed;width: 100%;height: 100%;z-index: 99999 !important;">
    <div style="left: 1em;">
        <div style="padding-top: 15%;text-align: center; color:#000;">
            <img src="<?=ASSET_DOMAIN?>assets/images/Cirrus-8-cyan-circle-outline-hires.png" style="width: 250px;" alt="cirrus8" id="logo" >
            <br>
            <div class="ui active inline loader-ui"></div>
            <input id="ngLoaderCount" type="hidden" value="0"/>
        </div>
    </div>
</div>
<div id="ngHttpErrorHandlingModal" class="ui basic modal" style="display: none;margin-top: 10% !important;top: 10% !important;">
    <div class="ui icon header">
        <img class="cirrus8ErrorLogo" src="<?=ASSET_DOMAIN?>assets/images/Cirrus-8-cyan-circle-outline-hires.png" style="width: 100px;" alt="cirrus8" id="logo" >
        <h1 class="MainTitleError" style="color:#00BAF2 !important;">
            An error has occurred
        </h1>
        <span id="ngHttpErrorHandlingErrMsgTitle"></span><br/>
        Please contact cirrus8 support at <span style="color:#00BAF2;font-weight: bolder;"><EMAIL></span>
    </div>
    <div class="content hidden">
        <div id="ngHttpErrorHandlingAccordion" class="ui inverted accordion">
            <div class="title">
                <i class="dropdown icon"></i>
                Error Details
            </div>
            <div class="content active" style="color: white !important;">
                <strong class="red">STATUS: </strong><strong id="ngHttpErrorHandlingErrMsgStatus"></strong><br/>
                <strong class="red">INFO&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;: </strong><strong id="ngHttpErrorHandlingErrMsgInfo"></strong><br/>
                <strong class="red">URL&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;: </strong><strong id="ngHttpErrorHandlingErrMsgUrl"></strong><br/>
                <strong class="red">LINE&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;: </strong><strong id="ngHttpErrorHandlingErrMsgLine"></strong><br/>
                <strong class="red">MSG&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;: </strong><strong id="ngHttpErrorHandlingErrMsgMsg"></strong><br/>
            </div>

        </div>
    </div>
    <div class="actions centered">
        <div class="ui red basic cancel inverted button">
            <i class="remove icon"></i>
            Close
        </div>
        <div class="ui green ok inverted button">
            <i class="checkmark icon"></i>
            Refresh
        </div>
    </div>
</div>

<?php
			$page = $_GET['module'] . '-' . $_GET['command'];

			if(($page != 'generalLedger-journals') && ($page != 'generalLedger-journalEntry')) {
				unsetCommandSpecificSessions();
			}
		?>

<div id="main_container">


    <?=$var['preload']?>
    <a name="top" id="top"></a>
    <div id="child-help" style="display: none;">
        <ul>
            <li id="b"><a href="#" onclick="window.open('<?= $var['HELPLINKS']['navbar_pm_help'] ?>', '_blank')"><?=ucwords(strtolower($_SESSION['country_default']['property_manager']))?> Help</a></li>
            <li><a href="#" onclick="window.open('<?= $var['HELPLINKS']['navbar_ta_help'] ?>', '_blank')"><?=ucwords(strtolower($_SESSION['country_default']['trust_accountant']));?> Help</a></li>
            <li><a href="mailto:<EMAIL>">Support</a></li>
            <li><a href="index.php?command=termsOfUse">Terms of Use</a></li>

        </ul>
    </div>

    <div id="header">

        <div id="menuIconShowSideBarMenu" class="ui secondary menu menu-left">
            <a class="item menu-side-bar-toggle">
                <i id="menuIconShowSideBar" class="large bars icon" style="padding: 0.9em;margin-right: 0px;"></i>
            </a>

            <a id="side-nav-trigger" class="item menu-side-bar-toggle-tablet open-side-nav">
                <i class="large bars icon" style="padding: 0.9em;margin-right: 0px;"></i>
            </a>

            <a id="main-menu-nav-trigger" class="item mobile-menu-toggle open-side-nav">
                <i class="large bars icon" style="padding: 0.9em;margin-right: 0px;"></i>
            </a>

            <?if($_SESSION['user_sub_type'] == 'AP_1'){?>
            <a class="item" href="index.php?command=invoice&module=ap"><img src="<?=ASSET_DOMAIN?>assets/images/cirrus8_banner<? if (DEBUG) { ?>_dev<? } ?>.png" alt="cirrus8" id="logo" style="height: 50px; width: auto;" /></a>
            <?}else if($_SESSION['user_sub_type'] == 'PM_PLUS'){?>
            <a class="item" href="index.php?command=home&module=properties"><img src="<?=ASSET_DOMAIN?>assets/images/cirrus8_banner<? if (DEBUG) { ?>_dev<? } ?>.png" alt="cirrus8" id="logo" style="height: 50px; width: auto;" /></a>
            <?}else{ ?>
            <a class="item" href="index.php?command=home"><img src="<?=ASSET_DOMAIN?>assets/images/cirrus8_banner<? if (DEBUG) { ?>_dev<? } ?>.png" alt="cirrus8" id="logo" style="height: 50px; width: auto;" /></a>
            <? } ?>

            <div id="menuDropDown" class="item" style="padding:0px !important; margin:0px !important;">
                <strong class="item page-logged-in-label">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Logged in as <?=$_SESSION['first_name']?></strong>&nbsp;<a style="display:inline-block;margin-top:2px;margin-right:4px;" href="index.php?module=home&command=reauth&source=login&action=login&type=reauth" title="Refresh user session"><i class="fa fa-refresh"></i></a>
                <span class="item"><?=buildSwitcher('userGroupList')?></span>
                <strong class="item">on</strong>
                <span class="item"><?=buildSwitcher('databaseGroupList2')?></span>
            </div>

            <div id="menuIconShowSideBarMenu2" class="item right page-top-header-menu">
                <? if (buildC8MenuV2NonModuleLinks('module=home&command=home')) { ?>
                    <span class="item"><select id="searchBoxInput" style="width:235px;height: 20px;" multiple="multiple"></select></span>

                    <span class="item"><a accesskey="J" href="index.php?command=home&module=home"><span style="font-weight:bold">Dashboard</span></a></span>
                <? } ?>

                <? if (buildC8MenuV2NonModuleLinks('module=administration&command=home')) { ?>
                    <span class="item"><a href=".?command=home&module=administration"><span>Administration</span></a></span>
                <? } ?>

                <span class="item">
                    <?php
                        if(isset($var['useDbCirrusFM']) && $var['useDbCirrusFM'] != ''){
                            $ref = '?ref='.base64_encode(json_encode(['page'=>'dashboard','client'=>$var['useDbCirrusFM'],'source'=>'c8']));
                            echo '<a accesskey="W" href="'.CFM_USE_CLIENT_URL.$ref.'" target="worktracc"><span>cirrusFM</span></a>';
                        }
                    ?>
                </span>
                <span class="item"><a id = "logout"><span><strong>Logout</strong></span></a></span>
                <span class="item"><a class="fg-button fg-button-icon-right" href="#" id="menu-help">Help<span class="ui-icon ui-icon-triangle-1-s"></span></a></span>

                <span class="item">
                    <?=renderAnnounceKitWidget()?>
                </span>
            </div>

            <div id="userMobileMenu" class="item right">
                <span class="item search-box-container"><select id="searchBoxInputMobile" style="width:230px;height: 20px;" multiple="multiple"></select></span>

                <span class="item announcekit-mobile-container">
                    <?=renderAnnounceKitWidget('3aXXYA', 'announcekit-widget-mobile')?>
                </span>

                <a id="user-menu-trigger" class="item user-menu-toggle open-user-menu" style="font-size:20px">
                    <i class="fa fa-cog" aria-hidden="true"></i>
                </a>
            </div>
        </div>




    </div>

    <div id="frame">
        <div id="c8MenuV2">
            <nav role="select">
                <?= buildC8MenuV2($var['Master::CurrentTab'] ) ?>
            </nav>
        </div>

        <div id="userMenu" class="no-borders">
            <div id="userMenuContent" class="ui left inline vertical menu no-borders side-user-nav-mobile user-menu-content-master">
                <strong class="usermenu-item usermenu-logged-in-label">Logged in as <?=$_SESSION['first_name']?> &nbsp;<a href="index.php?module=home&command=reauth&source=login&action=login&type=reauth" title="Refresh user session"><i class="fa fa-refresh"></i></a></strong>
                <span class="usermenu-item"><?=buildSwitcher('userGroupList')?></span>
                <strong class="usermenu-item usermenu-database-label">on</strong>
                <span class="usermenu-item"><?=buildSwitcher('databaseGroupList')?></span>

                <div class="usermenu-links">

                    <? if (buildC8MenuV2NonModuleLinks('module=home&command=home')) { ?>
                        <span class="usermenu-item">
                            <a accesskey="J" href="index.php?command=home&module=home">
                                <span style="font-weight:bold">Dashboard</span>
                            </a>
                        </span>
                    <? } ?>

                    <? if (buildC8MenuV2NonModuleLinks('module=administration&command=home')) { ?>
                        <span class="usermenu-item"><a href=".?command=home&module=administration"><span>Administration</span></a></span>
                    <? } ?>

                    <?php
                        if(isset($var['useDbCirrusFM']) && $var['useDbCirrusFM'] != ''){
                            $ref = '?ref='.base64_encode(json_encode(['page'=>'dashboard','client'=>$var['useDbCirrusFM'],'source'=>'c8']));
                            echo '<span class="usermenu-item"><a accesskey="W" href="'.CFM_USE_CLIENT_URL.$ref.'" target="worktracc"><span>cirrusFM</span></a></span>';
                        }
                    ?>

                    <span class="usermenu-item">
                        <div class="helplist">
                            <select id="help-dropdown" class="chzn-help" placeholder="Help" data-placeholder="Help">
                                <option>Help</option>
                                <option value="property-mngr"><?=ucwords(strtolower($_SESSION['country_default']['property_manager']))?> Help</option>
                                <option value="trust-acct"><?=ucwords(strtolower($_SESSION['country_default']['trust_accountant']));?> Help</option>
                                <option value="support">Support</option>
                                <option value="terms">Terms of Use</option>
                            </select>
                        </div>
                    </span>
                </div>

                <span class="usermenu-item"><a id="user-logout"><span><strong>Logout</strong></span></a></span>

            </div>
        </div>


        <div class="clear"></div>
        <div id="container">
            <div class="context mainDiv">
                <div id="submenuV2" class="no-borders">
                    <div id="menuSidebarContent" class="ui left inline vertical menu no-borders">
                        <?=buildC8SideBarV2 ($var['Master::Section'], $var['command'])?>
                    </div>
                    <div id="mobileMenuSidebarContent" class="ui left inline vertical menu no-borders">
                        <?=buildC8MobileMenu($var['Master::CurrentTab'], $var['Master::Section'], $var['command'])?>
                    </div>
                </div>
                <div id="content" class="pusher content-ml-sm">
                    <?=(($var['Master::ControlContainer']) ? '<div id="' .$var['Master::ControlContainer'] . '">' . $var['Master::Body'] . '</div>' : $var['Master::Body'])?>
                </div>
            </div>
        </div>

        <div id="c8Footer">
            <div class="container">
                <a href="#" id="darkMode"></a>
            </div>
        </div>
    </div>
    <div id="iframeSetup" style="display:none"></div>
</div>
</div>




<?=$var['scripts']?>
<script type="text/javascript">
    if ($('body').hasClass('c8-dark')) {
        $('#darkMode').html('<i class="fas fa-sun"></i> Disable Dark Mode');
    } else {
        $('#darkMode').html('<i class="fas fa-moon"></i> Enable Dark Mode');
    }

    if (localStorage.hasOwnProperty('dark_mode_setting') && localStorage.getItem('dark_mode_setting') == 'false') {
        $('#darkMode').addClass('hidden');
    } else {
        $('#darkMode').removeClass('hidden');
    }

    $('#darkMode').on('click', function (event) {
        event.preventDefault();

        if ($('body').hasClass("c8-dark")) {
            $('body').removeClass('c8-dark');
            $(this).html('<i class="fas fa-moon"></i> Enable Dark Mode');
            localStorage.setItem('dark_mode', false);
        } else {
            $('body').addClass('c8-dark');
            $(this).html('<i class="fas fa-sun"></i> Disable Dark Mode');
            localStorage.setItem('dark_mode', true);
        }
    });

    $(document).ready(function(){
        $("#logout, #user-logout").click(function() {
            $('<iframe style="display:none">', {
                   src: '<?php echo my_c8 . "logout"?>',
                   id:  'logoutFrame',
                   frameborder: 0,
                   scrolling: 'no'
               }).appendTo('#iframeSetup').ready(function(){
                    localStorage.setItem('Authentication', "");
                    sessionStorage.setItem('sso_key','');
                    window.location = '<?php echo logout_url ?>';
               });
        });

        $("#menuIconShowSideBar").click(function(){
            $("#content").toggleClass("marginLeft");
            $("#submenuV2").toggleClass("hidden");
        });

        $("#ngHttpErrorHandlingAccordion").accordion();

        if ($('#menuSidebarContent>.sidebar').html()=='') {
            $('#content').attr('style', 'margin-left: 0px !important;');
            $("#menuSidebarContent").addClass("hidden");
            $("#menuIconShowSideBar").addClass("hidden");
        }

        if ($('#menuSidebarContent>#c8SideMenuV2').html()=='') {
            $('#content').attr('style', 'margin-left: 0px !important;');
            $("#menuSidebarContent").addClass("hidden");
            $("#menuIconShowSideBar").addClass("hidden");
            $("#menuIconShowSideBar").css('display','none');
            $("#menuIconShowSideBarMenu").css('padding-top','5px');
        }

        $(".chzn-select").chosen({'inherit_select_classes' : true});

        $(".chzn-select").promise().done(function() {
            $(".user-role-select").closest('.side-by-side').find('.chosen-container').css({'width':'100%', 'min-width':'180px', 'max-width':'200px'});
            $(".database-select-2").closest('.side-by-side').find('.chosen-container').css({'width':'100%', 'min-width':'180px', 'max-width':'275px'});
        });

        $('#help-dropdown').chosen({
            'inherit_select_classes' : true,
            'disable_search' : true,
            'placeholder_text_single' : 'Help'
        });

        $('#chat-button').menu({
            content: $('#chat-menu').html(),
            callerOnState:''
        });
        $('#menu-help').menu({
            content: $('#child-help').html(),
            callerOnState:''
        });
        $('#menu-help-mobile').menu({
            content: $('#child-help').html(),
            callerOnState:''
        });
        $("div.dblist").find("span").css( "color", "red" );

        $('#help-dropdown').on('change', function(evt, params) {
            let helpOption = $(this).val();

            switch (helpOption) {
                case 'property-mngr':
                    var windowSize = "width=" + window.innerWidth + ",height=" + window.innerHeight + ",scrollbars=no";
					window.open('<?= $var['HELPLINKS']['navbar_pm_help'] ?>', 'popup', windowSize);
                    break;
                case 'trust-acct':
                    var windowSize = "width=" + window.innerWidth + ",height=" + window.innerHeight + ",scrollbars=no";
					window.open('<?= $var['HELPLINKS']['navbar_ta_help'] ?>', 'popup', windowSize);
                    break;
                case 'support':
                    window.location.href = "mailto:<EMAIL>";
                    break;
                case 'terms':
                    window.location.href = "index.php?command=termsOfUse";
                    break;
            }
        });

        ajaxCallback();

        fixChosenZeroWidth();
    });
</script>

<!-- SEARCH -->
<link type="text/css" href="<?=ASSET_DOMAIN?>assets/css/select2.min.css?<?=CSS_VERSION?>" rel="stylesheet" />
<script type="text/javascript" src="<?=ASSET_DOMAIN?>assets/js/select2.min.js?<?=JS_VERSION?>"></script>
<script type="text/javascript" src="<?=ASSET_DOMAIN?>assets/js/search.js?<?=JS_VERSION?>"></script>
<link rel="stylesheet" href="<?=ASSET_DOMAIN?>assets/css/search.css?<?=CSS_VERSION?>" type="text/css" />

<script>

    document.addEventListener('scroll', function (event) {
        var scrollPos = $('#main_container').scrollTop();
        scrollFunction(scrollPos);
    }, true);

    function scrollFunction(scrollPos) {
        if (scrollPos > 20) {
            $("#goTopBtn").addClass("goTopBtn-active");
        } else {
            $("#goTopBtn").removeClass("goTopBtn-active");
        }
    }
    function topFunction() {
        $('#main_container').scrollTop(0);
    }
    $( "#goTopBtn" ).hover(function() {
        document.getElementById("goTopBtnImgLeft").style.display = "none";
    }, function() {
        document.getElementById("goTopBtnImgLeft").style.display = "block";
    });

    countryDefaults.currencySymbol = '<?php echo $_SESSION['country_default']['currency_symbol']; ?>';

</script>
<script type="text/javascript" src="<?=ASSET_DOMAIN?>assets/js/sso.js?<?=JS_VERSION?>"></script>
<!-- SEARCH -->
</body>
</html>

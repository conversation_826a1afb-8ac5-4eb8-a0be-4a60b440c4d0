<!doctype html>
<html class="no-js" lang="en">
<head>
    <title>cirrus8: Online Commercial Property Management Solution - <?=$var['Master::Section']?></title>
    <link href="favicon.ico" rel="shortcut icon" type="image/x-icon"/>
    <link href="favicon.ico" rel="icon" type="image/x-icon"/>
    <link rel="stylesheet" href="<?=FA_URL_CSS?>">
    <link rel="stylesheet" href="assets/css/core.css?<?=CSS_VERSION?>" type="text/css"/>
    <link type="text/css" href="assets/chosen_v1.6.2/chosen.css?<?=CSS_VERSION?>" rel="stylesheet"/>
    <link type="text/css" rel="stylesheet" media="all" href="assets/css/screen.css?<?=CSS_VERSION?>"/>
    <link type="text/css" rel="stylesheet" media="all" href="assets/css/fg.menu.css?<?=CSS_VERSION?>"/>
    <link type="text/css" href="assets/css/custom-theme/jquery-ui.css?<?=CSS_VERSION?>" rel="stylesheet"/>
    <link type="text/css" href="<?=ASSET_DOMAIN?>assets/semantic-ui/components/menu.min.css" rel="stylesheet" />
    <link type="text/css" href="<?=ASSET_DOMAIN?>assets/semantic-ui/components/icon.min.css" rel="stylesheet" />
    <link type="text/css" href="assets/sidenav/sidenav.css" rel="stylesheet" />

		<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js" integrity="sha512-894YE6QWD5I59HgZOGReFYm4dnWc1Qt5NtvYSaNcOP+u1T9qYdvdihz0PPSiiqn/+/3e7Jo4EaG7TubfWGUrMQ==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>

            <script type="text/javascript" src="assets/js/jquery-ui-min.js?<?=JS_VERSION?>"></script>

    <script type="text/javascript" src="assets/js/fg.menu.js?<?=JS_VERSION?>"></script>
    <script type="text/javascript" src="assets/js/addon/periodicalupdater.js?<?=JS_VERSION?>"></script>

    <script type="text/javascript" src="assets/js/FusionCharts.js?<?=JS_VERSION?>"></script>
    <script type="text/javascript" src="assets/js/FusionCharts.jqueryplugin.js?<?=JS_VERSION?>"></script>
    <script type="text/javascript" src="assets/chosen_v1.6.2/chosen.jquery.min.js"></script>
    <script type="text/javascript" src="assets/js/core.js?<?=JS_VERSION?>"></script>
    <script type="text/javascript" src="assets/js/custom.js?<?=JS_VERSION?>"></script>
    <script type="text/javascript" src="assets/js/loader.js?<?=JS_VERSION?>"></script>
    <script type="text/javascript" src="assets/js/smart_date.js?<?=JS_VERSION?>"></script>
    <script type="text/javascript" src="assets/sidenav/sidenav.js?<?=JS_VERSION?>"></script>

    <style>
        .LogoutButton {
            background-color:#00baf2;
            -moz-border-radius:4px;
            -webkit-border-radius:4px;
            border-radius:4px;
            border:1px solid #fff;
            display:inline-block;
            cursor:pointer;
            color:#ffffff;
            font-size:14px;
            font-weight: normal;
            padding:20px 30px;
            text-decoration:none;
        }
        .LogoutButton:hover {
            background-color: rgba(52,137,161, 0.3);
        }
        .LogoutButton:active {
            position:relative;
            top:1px;
        }
        .subtitle {
            background-color: #00BAF2 !important;
        }
        #container{
            position:relative;
        }
    </style>
    <meta name="csrf-token" content="<?=$_SESSION['CSRF-X-CODE']?>">
    <script>$.ajaxSetup({headers: { 'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content') }});</script>
    <?=renderAnnounceKitWidgetData()?>
    <?=Bugsnag::render()?>
</head>

<body>

<div id="ngLoader-UI" class=""
     style="display: none;opacity: 0.9;left:0px;top:0px;color: white;background: white;position: absolute;width: 100%;height: 100%;z-index: 99999 !important;">
    <div style="left: 1em;">
        <div style="padding-top: 15%;text-align: center; color:#000;">
            <img src="assets/images/Cirrus-8-cyan-circle-outline-hires.png" style="width: 250px;" alt="cirrus8"
                 id="logo">
            <br>
            <div class="ui active inline loader-ui"></div>
            <input id="ngLoaderCount" type="hidden" value="0"/>
        </div>
    </div>
</div>
<div id="main_container">
<div id="modaloverlay"></div>

<?=$var['preload']?><a name="top" id="top"></a>
<div id="child-help hidden" style="display: none;">
    <ul>
        <li><a href="index.php?command=help&amp;module=home">Support</a></li>
    </ul>
</div>


    <div id="header">
        <div id="menuIconShowSideBarMenu" class="ui secondary menu menu-left">
            <a class="item" onclick="ShowHideSideBar()" >
                <i id="menuIconShowSideBar" class="large bars icon" style="padding: 0.9em;margin-right: 0px;"></i>
            </a>
            <? if($_SESSION['user_sub_type'] == 'AP_1'){?>
            <a class="item" href="index.php?command=invoice&module=ap"><img src="assets/images/cirrus8_banner<? if (DEBUG) { ?>_dev<? } ?>.png" alt="cirrus8" id="logo" style="height: 50px; width: auto;" /></a>
            <?}else{ ?>
            <a class="item" href="index.php?command=home"><img src="assets/images/cirrus8_banner<? if (DEBUG) { ?>_dev<? } ?>.png" alt="cirrus8" id="logo" style="height: 50px; width: auto;" /></a>
            <? } ?>
            <? if($_SESSION['user_sub_type'] != 'O'){?>
            <strong class="item">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Logged in as <?=$_SESSION['first_name']?></strong>
            <?} ?>
            <span class="item"><?=buildSwitcher('userGroupList')?></span>
            <strong class="item">on</strong>
            <span class="item"><?=buildSwitcher('databaseGroupList')?></span>
            <div id="menuIconShowSideBarMenu2" class="item right">
                <a class="item hidden" accesskey="J" href="index.php?command=home&amp;module=home">
                    <img src="assets/images/dashboard-home<? if (DEBUG) { ?>_dev<? } ?>.png" alt="home" id="home"
                         style="height: 35px;"/>
                </a>

                <a class="item" href="<?= MY_ACCOUNT_URL ?>">
                    <img src="assets/images/change_password<? if (DEBUG) { ?>_dev<? } ?>.png" alt="password"
                         id="password" style="height: 38px; width: 38px;"/>
                </a>
                <span class="item"><a class="LogoutButton" style="cursor: pointer;" id = "logout"><span><strong>Logout</strong></span></a></span>
                <span class="item hidden"><a class="fg-button fg-button-icon-right" href="#" id="menu-help">Help<span class="ui-icon ui-icon-triangle-1-s"></span></a></span>

                <span class="item">
                    <?=renderAnnounceKitWidget()?>
                </span>
            </div>


        </div>
    </div>


<div id="frame">
    <div class="clear"></div>
    <div id="container">
        <div id="mySidenav" class="sidenav">
            <img class="sidenav-logo-img" alt="<?=$var['logoAlt']?>" src="<?=$var['logoURL']?>" title="<?=$var['logoAlt']?>" />
            <?=ownerNavMenu( $var['Master::Section'], $var['command'] )?>
        </div>
        <div class="context mainDiv">
            <div id="content"><?=(($var['Master::ControlContainer']) ? '<div id="' .$var['Master::ControlContainer'] . '">
            ' . $var['Master::Body'] . '

            </div>
        </div>
        ' : $var['Master::Body'])?>
    </div>
</div>
</div>
<div class="clear"></div>

<div id="iframeSetup" style="display:none"></div>
<?=$var['scripts']?>
<script type="text/javascript">
    $(document).ready(function () {
        document.getElementById("mySidenav").style.width = "250px";
        $("#logout").click(function () {
            $('<iframe style="display:none">', {
                src: '<?php echo my_c8 . "logout"?>',
                id: 'logoutFrame',
                frameborder: 0,
                scrolling: 'no'
            }).appendTo('#iframeSetup').ready(function () {
                localStorage.setItem('Authentication', "");
                    sessionStorage.setItem('sso_key','');
                window.location = '<?php echo logout_url ?>';
            });
        });

        $(".chzn-select").chosen();
        $('#menu-help').menu({
            content: $('#child-help').html(),
            callerOnState: ''
        });

        ajaxCallback();
    });
</script>
<!-- SEARCH -->
<link href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.3/css/select2.min.css" rel="stylesheet"/>
<script src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.3/js/select2.min.js"></script>
<script type="text/javascript" src="assets/js/search.js?<?=JS_VERSION?>"></script>
<link rel="stylesheet" href="assets/css/search.css?<?=CSS_VERSION?>" type="text/css"/>
<script type="text/javascript" src="assets/js/sso.js?<?=JS_VERSION?>"></script>
<script type="text/javascript" src="assets/sidenav/js/adminlte.min.js?<?=JS_VERSION?>"></script>
<!-- SEARCH -->

</body>
</html>
<?php

use C8Utils\Env;

require_once('vendor/autoload.php');

$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();
$dotenv->required(['DB_HOST', 'DB_USERNAME', 'DB_PASSWORD', 'DB_DATABASE']);

bcscale(2);
date_default_timezone_set('Australia/Perth');
define('TODAY', date('d/m/Y'));

define('JS_VERSION', '20250605');
define('CSS_VERSION', '20250605');

define('ENVIRONMENT', Env::get('APP_ENV', 'local'));
define('DEBUG', Env::get('DEBUG', false));
define('DEBUG_SQL', Env::get('DEBUG_SQL', false));
define('DEBUG_SQL_THRESHOLD', Env::get('DEBUG_SQL_THRESHOLD', 8));
define('CLEANSQL', Env::get('CLEANSQL', false));
define('MAINTENANCE', Env::get('MAINTENANCE', false));
define('APP_DOMAIN', Env::get('APP_DOMAIN', 'cirrus8.com.au'));

if (DEBUG) {
    ini_set('display_errors', Env::get('DISPLAY_ERRORS', false));
    error_reporting(E_ALL);
}

define('GL_ACTIVE', true);
define('CHAT_ACTIVE', false);

define('assets', __DIR__ . '/assets/');
define('curl_error', __DIR__ . '/assets/errorlog.txt');


define('LOGO_WIDTH', 200);
define('LOGO_HEIGHT', 40);
define('LOGO_HEIGHTV2', 70);
define('LOGO_HEIGHT_INVOICE', 80);
define('LOGO_WIDTH_INVOICE', 200);

define('LINEBREAK', "\n\n");

if (isset($_SESSION['user_sub_type']) and $_SESSION['user_sub_type'] == "AP_1") {
    define('DEFAULT_COMMAND', 'invoice');
    define('DEFAULT_MODULE', 'ap');
} else {
    define('DEFAULT_COMMAND', 'home');
    define('DEFAULT_MODULE', 'home');
}

define('SYSTEMMAIL_ADDRESS', Env::get('SYSTEMMAIL_ADDRESS', '<EMAIL>'));
define('SYSTEMMAIL_NAME', Env::get('SYSTEMMAIL_NAME', 'cirrus8'));

define('ADMINMAIL_ADDRESS', Env::get('ADMINMAIL_ADDRESS', '<EMAIL>; <EMAIL>; <EMAIL>; <EMAIL>; <EMAIL>'));
define('ADMINMAIL_CC_ADDRESS', Env::get('ADMINMAIL_CC_ADDRESS', '<EMAIL>'));
define('ADMINMAIL_NAME', Env::get('ADMINMAIL_NAME', 'cirrus8 Administrator'));

define('SYSTEMMAIL_METHOD', Env::get('SYSTEMMAIL_METHOD', 'smtp'));
define('SYSTEMMAIL_SERVER', Env::get('SYSTEMMAIL_SERVER', 'localhost'));

define('SYSTEMDSN', 'Server='.Env::get('DB_HOST', 'localhost').';UID='.Env::get('DB_USERNAME', 'sa').';PWD='.Env::get('DB_PASSWORD').';Database='.Env::get('DB_DATABASE').';TrustServerCertificate=true');
define('SYSTEMDB', Env::get('DB_DATABASE'));
define('COREDSN', 'Server='.Env::get('DB_HOST', 'localhost').';UID='.Env::get('DB_USERNAME', 'sa').';PWD='.Env::get('DB_PASSWORD', '').';Database=');

define('PDFLIB_LICENSE', Env::get('PDFLIB_LICENSE'));

define('FINALDATE', '31/12/2999');
define('STARTDATE', '01/01/1900');

define('PDF_FONT_SETTINGS', "subsetting=true kerning=true");
define('PDF_TEXT_LEFT_ALIGNMENT', 'left');
define('PDF_TEXT_RIGHT_ALIGNMENT', 'right');
define('PDF_TEXT_CENTER_ALIGNMENT', 'center');

/* tax codes */

define('TAXFREE', 'GSTFREE');
define('TAXABLE', 'TAXABLE');

define('HTTPHOST', Env::get('APP_HTTPHOST', 'http://localhost'));
define('ICALURL', Env::get('APP_ICALURL', 'http://localhost'));
define('SSO_APP_CODE', 'cirrus8');
define('SUPER_USER_CODES', array('implementation','support','developer'));

define('SUPER_SUPPORT', array(
    'admin-query-databases',
    'admin-scheduler',
    'admin-completed-tasks',
    'admin-manage-pages',
    'admin-manage-page-subcategories',
    'admin-manage-page-categories'
));

define('SUPER_IMPLEMENTATION', array(
    'admin-import-data',
    'admin-report-configuration',
));

define('SUPER_DEVELOPER', array(
    'admin-query-databases',
));

define('SSO_ENABLED', Env::get('SSO_ENABLED', true));
define('authenticator', Env::get('AUTHENTICATOR', 'local')); // sso, local
define('ASSET_DOMAIN', Env::get('ASSET_DOMAIN', 'http://localhost'));
define('UPLOADED_ASSET_DOMAIN', Env::get('UPLOADED_ASSET_DOMAIN', 'http://localhost'));
define('sso', Env::get('SSO_TOKEN', ''));
define('sso_check', Env::get('SSO_CHECK', ''));
define('sso_get', Env::get('SSO_GET', ''));
define('logout_url', Env::get('LOGOUT_URL', ''));
define('my_c8_api', Env::get('MY_C8_API'));
define('c8_api', Env::get('C8_API_URL'));
define('SSO_LOGIN', Env::get('SSO_LOGIN'));
define('CFM_USE_CLIENT_URL', Env::get('CFM_USE_CLIENT_URL'));
define('MY_ACCOUNT_URL', Env::get('MY_ACCOUNT_URL'));
define('fm_url', Env::get('FM_URL'));
define('HELPJUICE_KEY', Env::get('HELPJUICE_KEY'));
define('HELPJUICE_JWT_URL', Env::get('HELPJUICE_JWT_URL'));
define('C8_SECURE_LOGIN', Env::get('C8_SECURE_LOGIN'));
define('POSTMARK_AWS_S3_BUCKET', Env::get('POSTMARK_AWS_S3_BUCKET'));
define('ADMIN_URL', Env::get('ADMIN_URL'));

define('FM_DB', Env::get('FM_DB'));
define('my_c8_api_changepass', 'https://my-c8.cirrus8.com.au/#/forgotPassword');

define('BASEPATH', __DIR__ . '/..');
define('CALPATH', Env::get('CAL_PATH', './'));
define('NPMSPATH', Env::get('NPMSPATH', BASEPATH . '/newpms'));
define('NPMSURL', Env::get('NPMSURL', HTTPHOST . '/newpms'));
define('SYSTEMURL', Env::get('SYSTEMURL', HTTPHOST . '/'));
define('SYSTEMPATH', Env::get('SYSTEMPATH', BASEPATH . '/'));
define('CACHEPATH', Env::get('CACHEPATH', BASEPATH . '/framework/cache'));
define('cacert', Env::get('cacert', '/assets/cacert.pem'));

define('REPORTPATH', Env::get('REPORTPATH', BASEPATH . '/reports'));
define('PROPERTY_IMAGE_CREATE_PATH', Env::get('PROPERTY_IMAGE_CREATE_PATH', SYSTEMPATH . '/assets/images'));
define('PDFPATH', Env::get('PDFPATH', BASEPATH . '/resources/pdf'));
define('STARTBATCH', Env::get('STARTBATCH', 1000000));


define('SYSTEMLOGFILE', BASEPATH . '\errors.html');
define('SYSTEMERRORFILE', SYSTEMPATH . '\error_log.html');
define('SYSTEMSQLERRORFILE', SYSTEMPATH . '\sql_error_log.html');
define('SYSTEMDATAFILE', SYSTEMPATH . '\data_log.html');
define('SYSTEMSQLFILE', SYSTEMPATH . '\sql_log.html');
define('SYSTEMSCHEDULERFILE', SYSTEMPATH . '\scheduler_log.html');

define('FILEBROWSER_BASE', BASEPATH . '\fileshare');
define('UPLOAD_BASE', BASEPATH . '\uploads');

define('CAL_STANDARD', 1);
define('CAL_GL', 2);
define('CAL_RETAIL', 3);

define('DOC_INV_ENTRY_AP', 1);
define('DOC_INV_ENTRY_AR', 2);
define('DOC_LEASE', 10);
define('DOC_PROP', 15);
define('DOC_OWNER', 16);
define('DOC_INV_TENANT', 20);
define('DOC_INV_SUPPLIER', 21);
define('DOC_INV_AGENT', 22);
define('DOC_COMPANY_BANK', 23);
define('DOC_COMPANY', 24);
define('DOC_LEASE_INSPECTION', 11);
define('DOC_PROPERTY_INSPECTION', 12);
define('DOC_DIRECT_DEPOSIT_BANK', 26);
define('DOC_TRUST_ACCOUNT', 25);

define('IMG_PROP_COVER_PAGE', 1);

include('lib/functions.php');
include('lib/traccFunctions.php'); // common functions that manipulate data from the system
include('lib/database.php');
include('lib/drivers/mssql.php');
include('lib/forms.php');
include('lib/menu.php');

include('lib/AnnounceKitWidget.php');

include('lib/FileUploader/class.fileuploader.php');
include('lib/help_links.php');

//-- common and shared functions

include_once 'commands/shared/backcharge.functions.php';

include_once 'data/shared/dbInterface.GeneralLedger.php';
include_once 'commands/shared/gl.functions.php';

include('lib/classes/LinkedList.php');
include('lib/classes/strXML.php');
include('lib/classes/Context.php');
include('lib/classes/Template.php');
include('lib/classes/MasterPage.php');
include('lib/classes/DashboardPage.php');
include('lib/classes/PrintPage.php');
include('lib/classes/BlankPage.php');
include('lib/classes/LoginPage.php');
include('lib/classes/EmailTemplate.php');
include('lib/classes/UserControl.php');
include('lib/classes/Zip.php');
include('lib/classes/FileBrowser.php');
include('lib/classes/DirectEntry.php');
include('lib/classes/BPAYEntry.php');
include('lib/classes/DirectEntryGB.php');
include('lib/dbInterface.php');
include('lib/dbWrapper.php');
include('lib/PDFwrapper.php');
include('lib/PDFReport.php');
include('lib/XLSReport.php');
include('lib/PDFObjects.php');
include('lib/classes/Queue.php');
include 'data/shared/dbInterface.php';
include 'data/shared/dbInterface_email_centralisation.php';
include 'data/shared/dbInterface.Portfolio.php';


//-- TRACC SPECIFIC DOCUMENT TEMPLATES
include('lib/documents/PDFCheque.php');
include('lib/documents/RemittanceAdviceCheque.php');
include('lib/documents/PDFCheque_JLL.php');
include('lib/documents/PDFCheque_GW.php');
include('lib/documents/PDFCheque_BR.php');
include('lib/documents/PDFDiaryReminder.php');
include('lib/documents/PDFCheque_RN.php');
include('lib/documents/PDFCheque_RH.php');
include('lib/documents/PDFCheque_BC.php');
include('lib/documents/PDFCheque_PC.php');
include('lib/documents/PDFCheque_KF.php');
include('lib/documents/PDFCheque_CBA.php');
include('lib/documents/PDFCheque_RW.php');
include('lib/documents/PDFCheque_SJ.php');
include('lib/documents/PDFCheque_BW.php');


include('lib/classes/ABNLookup.php');

// 2020-10-26 AI IMPROVEMENTS
include('lib/classes/AutomatedInvoice.php');
include 'data/shared/dbInterface.AutomatedInvoice.php';

// set to the user defined error handler
$old_error_handler = set_error_handler("myErrorHandlerV2");

$stateList['WA'] = 'Western Australia';
$stateList['VIC'] = 'Victoria';
$stateList['NSW'] = 'New South Wales';
$stateList['QLD'] = 'Queensland';
$stateList['SA'] = 'South Australia';
$stateList['NT'] = 'Northern Territory';
$stateList['ACT'] = 'Australian Capital Territory';
$stateList['TAS'] = 'Tasmania';
$stateList['MALAYSIA'] = 'Malaysia';
$stateList['NZ'] = 'New Zealand';
$stateList['SNG'] = 'Singapore';
$stateList['ENG'] = 'England';
$stateList['SCO'] = 'Scotland';
$stateList['WAL'] = 'Wales';

$numberWordList[0] = 'ZERO';
$numberWordList[1] = 'ONE';
$numberWordList[2] = 'TWO';
$numberWordList[3] = 'THREE';
$numberWordList[4] = 'FOUR';
$numberWordList[5] = 'FIVE';
$numberWordList[6] = 'SIX';
$numberWordList[7] = 'SEVEN';
$numberWordList[8] = 'EIGHT';
$numberWordList[9] = 'NINE';

$yesNo[1] = 'Yes';
$yesNo[0] = 'No';

$taskDescription[0] = 'Draft - You may continue to edit this form.';
$taskDescription[1] = 'Finalised - This form has been submitted for processing.';
$taskDescription[2] = 'Rejected - There are errors in your submission, please review the email to identify what needs addressing.';
$taskDescription[3] = 'Approved';

define('TYPE_CREDIT', 'CRE');
define('TYPE_INVOICE', 'INV');
define('TYPE_ADJUSTMENT', 'ADJ');
define('TYPE_CASH', 'CSH');
define('TYPE_REVERSAL', 'REV');
define('TYPE_PAYMENT', 'PAY');
define('TYPE_DIRECTDEPOSIT', 'DIR');
define('TYPE_CHEQUE', 'CHQ');
define('TYPE_BPAY', 'BPA');
define('TYPE_EPOS', 'EPO');
define('TYPE_EFTPOS', 'EPO');
define('TYPE_DIRECTDEBIT', 'DDR');
define('TYPE_CANCELLATION', 'CAN');
define('TYPE_JOURNAL', 'JNL');

//-- defines the type of period calculation to apply when handling date comparisons
define('PERIOD_MONTH', 'M');
define('PERIOD_WEEK', 'W');
define('PERIOD_YEAR', 'Y');
define('PERIOD_6MONTH', 'S');
define('PERIOD_QUARTER', 'Q');
define('PERIOD_QUARTER_DAYS', 'U');
define('PERIOD_FORTNIGHT', 'F');
define('PERIOD_4WEEK', 'K');

define('DATA_LIVE', 1);
define('DATA_TEMP', 2);

define('FILETYPE_PDF', 'pdf');
define('FILETYPE_XLS', 'xlsx');
define('FILETYPE_ABA', 'aba');
define('FILETYPE_SCREEN', 'screen');
define('FILETYPE_DOC', 'docx');
define('FILETYPE_EMAIL', 'email');
define('FILETYPE_CSV', 'csv'); // 2013-09-16: Added. [Morph]

define('TRACC_LOGO', SYSTEMPATH . "/assets/clientLogos/cirrus8_logo.png");
define('BPAY_LOGO', SYSTEMPATH . "/assets/images/BPAY_2012_PORT_BLK.png");
define('BPAY', SYSTEMPATH . "/assets/images/BPAY.png");

if (!defined('INCOME')) {
    define('INCOME', 'I');
}
if (!defined('EXPENDITURE')) {
    define('EXPENDITURE', 'E');
}
if (!defined('BALANCESHEET')) {
    define('BALANCESHEET', 'B');
}

if (!defined('OCCUPIED')) {
    define('OCCUPIED', 'O');
}
if (!defined('VACANT')) {
    define('VACANT', 'V');
}

if (!defined('DEBTOR')) {
    define('DEBTOR', 'D');
}
if (!defined('SUPPLIER')) {
    define('SUPPLIER', 'S');
}
if (!defined('OWNER')) {
    define('OWNER', 'O');
}

define('USER_TRACC', 'A');
define('USER_CLIENT', 'C');
define('USER_OWNER', 'O');
define('USER_TENANT', 'T');
define('USER_LEASE', 'L');
define('USER_LEASEADMIN', 'N');
define('USER_STRATA', 'S');
define('USER_AI', 'R');
define('USER_CUSTOM_U1', 'CU1');
define('USER_PM_PLUS', 'PM_PLUS');
define('USER_EXECUTIVEMANAGER', 'EM');

define('PM_PLUS_SUB_TYPE', 18);

define('JOURNAL_REVERSAL', 'R');
define('JOURNAL_PREPAYMENT', 'P');

define('BASIS_CASH', 2);
define('BASIS_ACCRUALS', 1);

define('GL_SOURCE_AP', 'AP');
define('GL_SOURCE_AR', 'AR');
define('GL_SOURCE_JOURNAL', 'GJ');
define('GL_SOURCE_AP_ALLOC', 'AP_ALLOC');
define('GL_SOURCE_AR_ALLOC', 'AR_ALLOC');

//-- payment run types
define('PAYMENT_SUPPLIER', 1);
define('PAYMENT_OWNER', 2);
define('PAYMENT_MONTH_END', 3);

//-- payment run types
define('PARTIAL_FULL', 1);
define('PARTIAL_BALANCE', 2);
define('PARTIAL_PART', 3);

define('METHOD_ACCRUALS', 1);
define('METHOD_CASH', 2);

define('PAY_EFT', 1);
define('PAY_BPAY', 2);
define('PAY_CHQ', 3);

define('RETAIL_CATEGORY', 1);
define('RETAIL_SUBCATEGORY', 2);
define('RETAIL_FINECATEGORY', 3);
define('RETAIL_LEASE', 4);

//-- scheduler flags
define('IS_TASK', 'isScheduled');
define('DOC_MASTER', 'pdf');
define('TASK_COMPLETE', 'taskComplete');
//-- sets a threshold for how many attempts should be made to complete a task - each 'run' of the scheduled task processor increments the task by one : once a task passed the threshold of allowed fails, it won't be run
define('TASK_THRESHOLD', 1);

define('TASKTYPE_OWNER_REPORT', 2);
define('TASKTYPE_DIARY_REPORT', 3);
define('TASKTYPE_DEBTORS_REPORT', 4);
define('TASKTYPE_DIRECT_DEBIT', 5);
define('TASKTYPE_TENANCY_SCHEDULE', 6);
define('TASKTYPE_TRIAL_BALANCE', 7);
define('TASKTYPE_PROPERTY_REPORT', 8); // 2012-09-18: Added for the new version of propertyReport [Morph]
define('TASKTYPE_PROPERTY_LIST_REPORT', 9); // 2012-09-18: Added for the new version of propertyListReport [Morph]
define('TASKTYPE_COMPANY_REPORT', 10); // 2012-09-19: Added for the new version of companyReport [Morph]
define('TASKTYPE_TENANT_ACTIVITY', 13); // 2012-09-21: Added for the new version of tenantActivity [Morph]
define('TASKTYPE_AREA_SUMMARY', 14); // 2012-10-12: Added for the new version of areaSummary [Morph]
define('TASKTYPE_SUPPLIER_RECONCILIATION', 15); // 2012-10-29: Added for the new version of Supplier Reconciliation [Morph]
define('TASKTYPE_CASH_BOOK', 16); // 2012-11-19: Added for the new version of Cash Book [Morph]
define('TASKTYPE_PAYMENT_ENQUIRY', 17); // 2013-01-07: Added for the new version of Payment Enquiry [Morph]
define('TASKTYPE_TENANT_CONTACTS_REPORT', 18); // 2013-01-14: Added for the new version of tenantContactsReport [Morph]
define('TASKTYPE_VO_RECONCILIATION', 19); // 2013-03-11 [Morph]
define('TASKTYPE_GL', 8);
define('TASKTYPE_PROFIT_LOSS', 9);
define('TASKTYPE_EXCEL_REPORT', 21);
define('TASKTYPE_ABSTRACT_REPORT', 8); //2016-10-06: Added for the new version of Lease Abstract Report [Arjean]
define('TASKTYPE_BALANCE_SHEET', 22); //2016-10-19: Added by Lyn
define('TASKTYPE_BANK_BALANCE', 16); // 2017-04-06: Added for the Bank Balance [Morph]
define('TASKTYPE_INVOICE', 16);
define('TASKTYPE_TEST', 0);
define('TASKTYPE_KEYS_REPORT', 10);
define('TASKTYPE_INSURANCE_REPORT', 10);
define('TASKTYPE_RECURRING', 1);
define('TASKTYPE_MANAGEMENTAGREEMENT', 20);
define('TASKTYPE_GENERATE_INVOICE', 25);
define('TASKTYPE_BANK_GUARANTEE_REPORT', 120);

define('THRESHOLD_BANKGUARANTEEREPORT', 999999);
define('THRESHOLD_GENERATETAXINVOICE', 2000);
define('THRESHOLD_OWNERREPORT', 1); //-- the number of properties to process before it defaults to a scheduled task
define('THRESHOLD_DEBTORSREPORT', 5000);
define('THRESHOLD_TENANCYSCHEDULE', 9999);
define('THRESHOLD_DIRECTDEBIT', 99999);
define('THRESHOLD_TRIALBALANCE', 9999);
define('THRESHOLD_MANAGEMENTAGREEMENT', 9999);
define('THRESHOLD_PROPERTYREPORT', 9999); // 2012-09-18: Added for the new version of propertyReport [Morph]
define('THRESHOLD_PROPERTYLISTREPORT', 9999); // 2012-09-18: Added for the new version of propertyListReport [Morph]
define('THRESHOLD_COMPANYREPORT', 9999); // 2012-09-19: Added for the new version of companyReport [Morph]
define('THRESHOLD_TENANTACTIVITY', 23000); // 2012-09-21: Framework version of tenantActivity [Morph]
define('THRESHOLD_AREASUMMARY', 9999); // 2012-10-12: Framework version of areaSummary [Morph]
define('THRESHOLD_SUPPLIERRECONCILIATION', 26000); // 2012-10-29: Framework version of Supplier Reconciliation [Morph]
define('THRESHOLD_CASHBOOK', 9999); // 2012-10-29: Framework version of Cash Book [Morph]
define('THRESHOLD_PAYMENTENQUIRY', 9999); // 2013-01-07: Framework version of Payment Enquiry [Morph]
define('THRESHOLD_TENANTCONTACTSREPORT', 9999); // 2013-01-14: Framework version Tenant Contacts Report [Morph]
define('THRESHOLD_VORECONCILIATION', 23000); // 2013-03-11 [Morph]
define('THRESHOLD_EXCELREPORT', 9999); // 2013-03-11 [Morph]
define('THRESHOLD_ABSTRACTREPORT', 9999); //2016-10-06: Added for the new version of Lease Abstract Report [Arjean]
define('THRESHOLD_BALANCESHEET', 9999); //2016-10-19: Added by Lyn
define('THRESHOLD_BANKBALANCE', 9999); // 2017-04-06: Added for Bank Balance Report [Morph]
define('THRESHOLD_INVOICE', 9999);
define('THRESHOLD_CREDITORSREPORT', 9999);
define('THRESHOLD_GL', 3);
define('THRESHOLD_KEYSREPORT', 9999);
define('THRESHOLD_INSURANCEREPORT', 9999);

/** For Property Reports: Set default reportType for Jasper Reports */
define('REPORT_TYPE_JASPER', 23); //varies from the max reportType ID used + 1 in reports table

define('DOC_TAXINVOICE', 'TaxInvoice');
define('DOC_TRUSTACCOUNTSTATEMENT', 'TrustAccountStatement');
define('DOC_OWNERSTATEMENT', 'OwnerStatement');
define('DOC_AGEDDEBTORS', 'AgedDebtorsReport');
define('DOC_DIRECTDEBIT', 'DirectDebit');
define('DOC_DIRECTDEPOSIT', 'DirectDeposit');
define('DOC_BANKDEPOSITSLIP', 'BankDepositSlip');
define('DOC_TENANCYSCHEDULE', 'TenancySchedule');
define('DOC_TRIALBALANCE', 'TrialBalance');
define('DOC_UNPAIDDEBTORS', 'UnpaidDebtors');
define('DOC_PROPERTYREPORT', 'PropertyReport'); // 2012-09-18: Added for the new version of propertyReport [Morph]
define('DOC_PROPERTYLISTREPORT', 'PropertyListReport'); // 2012-09-18: Added for the new version of propertyListReport [Morph]
define('DOC_COMPANYREPORT', 'CompanyReport'); // 2012-09-18: Added for the new version of companyReport [Morph]
define('DOC_AREASUMMARY', 'AreaSummary'); // 2012-09-21: Added for the new version of areaSummary [Morph]
define('DOC_PROPERTYOCCUPANCYSTATUS', 'PropertyOccupancyStatus');
define('DOC_CHARTOFACCOUNTS', 'ChartOfAccounts'); // 2012-09-21: Added for the new version of chartOfAccounts [Morph]
define('DOC_TENANTACTIVITY', 'TenantActivity'); // 2012-09-21: Added for the new version of tenantActivity [Morph]
define('DOC_SUPPLIERRECONCILIATION', 'SupplierReconciliation'); // 2012-10-29: Added for the new version of Supplier Reconciliation [Morph]
define('DOC_CASHBOOK', 'CashBook'); // 2012-10-29: Added for the new version of Cash Book [Morph]
define('DOC_PAYMENTENQUIRY', 'PaymentEnquiry'); // 2013-01-07: Added for the new version of Payment Enquiry [Morph]
define('DOC_TENANTCONTACTSREPORT', 'TenantContactsReport'); // 2013-01-14: Added for the new version of Tenant Contacts Report [Morph]
define('DOC_VORECONCILIATION', 'VOReconciliation'); // 2013-03-11 [Morph]
define('DOC_GENERALLETTER', 'GeneralLetter'); // 2013-08-23 [Morph]
define('DOC_RENTREVIEWLETTER', 'RentReviewLetter'); // 2013-10-02 [Morph]
define('DOC_MANAGEMENTAGREEMENT', 'ManagementAgreement');
define('DOC_EXCELREPORT', 'ExcelReport');
define('DOC_ABSTRACTREPORT', 'AbstractReport'); //2016-10-06: Added for the new version of Lease Abstract Report [Arjean]
define('DOC_XEROCSV', 'XeroCSV'); //2016-10-19: Added by Lyn
define('DOC_BALANCESHEET', 'BalanceSheet'); //2016-10-19: Added by Lyn
define('DOC_BANKBALANCE', 'BankBalance'); //2017-04-14 [Morph]
define('DOC_INVOICE', 'InvoiceActivity'); //
define('DOC_GL', 'GeneralLedger');
define('DOC_BANKSTATEMENT', 'BankStatement');
define('DOC_AGEDCREDITORS', 'AgedCreditorsReport');
define('DOC_KEYS', 'KeyStatus');
define('DOC_AGENCYFEES', 'AgencyFees');
define('DOC_INSURANCE', 'InsuranceReport');
define('DOC_RECOVERABLE', 'RecoverableCostReport');
define('DOC_DIRECTDOWNLOAD', 'DirectDownload');

define('CHARGE_RENT', 'R');
define('CHARGE_PARKING', 'P');
define('CHARGE_CLEANING', 'C');
define('CHARGE_SINKING', 'F');
define('CHARGE_STRATA', 'S');
define('CHARGE_PROMOTIONS', 'M');
define('CHARGE_OTHER', 'O');
define('CHARGE_OUTGOINGS', 'V');
define('MISCELLANEOUS', 'MISC');

$systemLocation[USER_TRACC] = '/administrator';
$systemLocation[USER_CLIENT] = '/client';
$systemLocation[USER_TENANT] = '/tenant';
$systemLocation[USER_OWNER] = '/owner';
$systemLocation[USER_LEASE] = '/leaseManager';
$systemLocation[USER_LEASEADMIN] = '/leaseAdmin';
$systemLocation[USER_STRATA] = '/strataManager';
$systemLocation[USER_AI] = '/aiOperator';
$systemLocation[USER_CUSTOM_U1] = '/customU1';
$systemLocation[USER_PM_PLUS] = '/pmPlus';
$systemLocation[USER_EXECUTIVEMANAGER] = '/executiveManager';

$taskClass[0] = 'draft';
$taskClass[1] = 'finalised';
$taskClass[2] = 'rejected';
$taskClass[3] = 'approved';

$clientDirectory = str_replace(' ', '', $_SESSION['database']);
$client_directory = str_replace(' ', '', $_SESSION['database']); // code refactor
$pathPrefix =REPORTPATH . "/";
$path_prefix =REPORTPATH . "/"; // code refactor

$_SESSION['pathPrefix'] = $pathPrefix;
$_SESSION['clientDirectory'] = $clientDirectory;
$globalDownloadLink = "" . $clientDirectory;

$module = '';

$chartAttributes['caption'] = '';
$chartAttributes['palette'] = '2';
$chartAttributes['showBorder']=0;
$chartAttributes['showShadow'] = 0;

$chartAttributes['baseFont'] = 'tahoma';
$chartAttributes['baseFontColor'] = '3333333';

$chartAttributes['legendBorderAlpha'] = 0;
$chartAttributes['legendShadow'] = 0;
$chartAttributes['legendBgAlpha'] = 0;
$chartAttributes['yAxisName'] = '';
$chartAttributes['pieSliceDepth'] = '30';
$chartAttributes['formatNumberScale'] = 0;
$chartAttributes['canvasbgColor'] = 'FAFAFA';
$chartAttributes['canvasbgAlpha'] = 0;
$chartAttributes['showCanvasBg'] = 0;
$chartAttributes['canvasBaseDepth'] = 1;
$chartAttributes['canvasBaseColor'] = 'FCFCFC';
$chartAttributes['bgAlpha'] = 0;
$chartAttributes['bgColor'] = 'FAFAFA';
$chartAttributes['showbg'] = 0;
$chartAttributes['numberPrefix'] = '';
$chartAttributes['labelDisplay'] = 'WRAP';
$chartAttributes['rotateValues'] = 0;
$chartAttributes['showValues'] = 1;
$chartAttributes['placeValuesInside'] = 1;
$chartAttributes['canvasPadding'] = 0;
$chartAttributes['decimals'] = 2;
$chartAttributes['numberSuffix'] = ' ';

$chartAttributes['use3DLighting'] = 0;
$chartAttributes['divLineAlpha'] = 50;
$chartAttributes['maxColWidth'] = 40;

$chartAttributes['zeroPlaneColor'] = 'CCCCCC';
$chartAttributes['zeroPlaneAlpha'] = 80;
$chartAttributes['zeroPlaneShowBorder'] = 0;
$chartAttributes['showZeroPlane'] = 0;
$chartAttributes['zeroPlaneBorderColor'] =  '';

$chartAttributes['toolTipBorderColor']='CCCCCC';
$chartAttributes['toolTipBgColor']='ECECEC';
$chartAttributes['paletteColors'] = 'FF9E00';
define('GOOGLE_API_KEY', 'AIzaSyCUkQZP8wDmijbsiHKXUrg_eQmHoavmv14');
// For Latitude and Longitude
define('GOOGLE_API_KEY_LOCATION', 'AIzaSyDJlUnkgbaNn5qK5bS66TW9S2tnyvzprnE');

//for MBL Integration
require_once('lib/besimple_soap_0.2.6.0_require/vendor/autoload.php'); //for MBL upload

define('MBL_API_TEST', 'https://bfs-webservices-test.macquarie.com.au/bfs/b2bsit2/ws/MRBDirectUploads:v1?WSDL');
define('MBL_WS_TEST', 'https://bfs-webservices-test.macquarie.com.au/bfs/b2bsit2/ws/MRBDirectUploads:v1/MRBDirectUploads_v1_Port');
define('MBL_API_PROD', 'https://bfs-webservices-prod.macquarie.com.au/bfs/b2b/ws/MRBDirectUploads:v1?WSDL');
define('MBL_WS_PROD', 'https://bfs-webservices-prod.macquarie.com.au/bfs/b2b/ws/MRBDirectUploads:v1/MRBDirectUploads_v1_Port');
define('MBL_DD', 'https://www.macquarie.com.au/ab3directdownload/download?');
define('FILE_B', 'brf');
define('FILE_T', 'txn');
define('FILE_P', 'pay');
define('FILE_A', 'acc');

// GOOGLE LOGIN
define('GOOGLECLIENTID', Env::get('GOOGLE_CLIENT_ID'));
define('GOOGLECLIENTSECRET', Env::get('GOOGLE_CLIENT_SECRET'));
// AZURE LOGIN
$azureAccts = [
    "cirrus8" => [
        'clientID' =>"b074635f-7e58-428e-a3c2-26e5d95516c4",
        'clientKey'=>"om28CjRnBr2rQJcfTEc0c3+Lf62eWdyQcJD0u5d4eM0="
    ],
    "realmark" => [
        'clientID' =>"a9199e39-054a-4e37-aef6-e7f182add0fa",
        'clientKey'=>"PTmTzbITZQaLvahN8LyeYm97NTJlr0lCpxraX2c3/m4="
    ],
];

define('AZUREACCOUNTS', serialize($azureAccts));

define('ABN_GUID', Env::get('ABN_GUID'));

define('BIGLOGOCLIENT', array("Beller_Com","Beller_Com_Test","Aston_Com_Sales","Aston_Com_Sales_test","C8demo","Ross_Scarfone","Ross_Scarfone_test"));
define('API_PAYMENT_UPDATE', '*********************************/caaps-api/api/payment-updates');

define('AWS_S3_ENABLED', Env::get('AWS_S3_ENABLED', false));
define('AWS_S3_KEY', Env::get('AWS_S3_KEY'));
define('AWS_S3_SECRET', Env::get('AWS_S3_SECRET'));
define('AWS_S3_REGION', Env::get('AWS_S3_REGION'));
define('AWS_S3_BUCKET', Env::get('AWS_S3_BUCKET'));
define('AWS_S3_URL', 'http://s3.'.AWS_S3_REGION.'.amazonaws.com/'.AWS_S3_BUCKET.'/');
define('AWS_S3_BUCKET_MGMT', Env::get('AWS_S3_BUCKET_MGMT'));

include_once('lib/S3FileHandler.php');
include('lib/classes/UserPermission.php');
include('lib/classes/GLLookup.php');

define('maxClearanceDays', 8);
define('POSTMARK_TOKEN', Env::get('POSTMARK_TOKEN'));
define('POSTMARK_TIMEOUT', Env::get('POSTMARK_TIMEOUT', 60));

include('lib/postmark.php');
define('C8_FILE_URL', Env::get('FM_URL') . 'download/');

include_once('lib/S3FileHandler.php');
include_once('lib/Cirrus8FileHandler.php');
include_once('lib/SimpleHTMLDOM.php');

define('LOCAL_SSL_ENABLED', Env::get('LOCAL_SSL_ENABLED', true));

// Enable and configure sessions
ini_set('session.use_cookies', 1);
ini_set('session.use_only_cookies', 1);
ini_set('session.cookie_httponly', true);
ini_set('session.cookie_secure', Env::get('LOCAL_SSL_ENABLED', true));
ini_set('session.save_path', '/tmp');

define('CLIENT_ID', '3');
define('CLIENT_SECRET', 'SO4OU7ps1QinoSXW8WINTXYvPIVHyUOoNys7Oufc');

// ASSURO CONFIG - LIVE
define('ASSURO_URI', $_ENV['ASSURO_URI']);
define('ASSURO_CLIENT_KEY', $_ENV['ASSURO_CLIENT_KEY']);
define('ASSURO_SECRET_KEY', $_ENV['ASSURO_SECRET_KEY']);
define('ASSURO_CHANNEL_PARTNER_ID', $_ENV['ASSURO_CHANNEL_PARTNER_ID']);

// COUNTRY DEFAULTS
include('lib/classes/CountryDefaults.php');

define('CDF_COUNTRY_CODE', 'AU');
define('CDF_JSON_FILE', 'D:\www\c8-api\resources\json\country_default_settings.json');
define('CDF_BUSINESS_LABEL', 'ABN');
define('CDF_BUSINESS_LENGTH', '11');
define('CDF_BANK_ACCOUNT_LENGTH', '9');
define('CDF_POST_CODE_LENGTH', '4');
define('CDF_DISPLAY_STATE', true);
define('CDF_DISPLAY_BSB', true);
define('CDF_CURRENCY_SYMBOL', '$');
define('CDF_POST_CODE_FORMAT', '9999');
define('CDF_TAX_LABEL', 'GST');


// SMS
define('SMS_USERNAME', $_ENV['SMS_USERNAME']);
define('SMS_PASSWORD', $_ENV['SMS_PASSWORD']);
define('SMS_URL', $_ENV['SMS_URL']);
define('SMS_SERVICE', $_ENV['SMS_SERVICE']);
define('SMS2_URL', $_ENV['SMS2_URL']);
define('SMS2_TOKEN', $_ENV['SMS2_TOKEN']);

include_once('lib/SMSService.php');

define('TINYMCE_LICENSE_CODE', '2amsnfd7l46rwssxgp7ipikz40s8jvjpmf5qhoi5iwjbf31d');
define('OPENAI_LICENSE_CODE', '***************************************************');
define('AMCHART_LICENSE_CODE', 'AM5C-5226-4753-0153-4244');

//JASPERCONFIG
define('JASPER_SERVER', $_ENV['JASPER_SERVER']);
define('JASPER_USER', $_ENV['JASPER_USER']);
define('JASPER_PASS', $_ENV['JASPER_PASS']);

//FONTAWESOME URL
define('FA_URL', 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.6.0/js/all.min.js');
define('FA_URL_CSS', 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.6.0/css/all.min.css');

define('EVOLVE_TOKEN_URL','https://api-invoices.evolveautomation.com.au/auth/token');
define('EVOLVE_MARK_AS_PAID','https://api-invoices.evolveautomation.com.au/invoices/markAsPaid');
define('EVOLVE_USER', array(
        'Hallmarc_test' => '<EMAIL>',
        'Hallmarc' => '<EMAIL>'
    )
);
define('EVOLVE_PASS',array(
        'Hallmarc_test' => 'LmS*HdhY2V+HSw',
        'Hallmarc' => 'sS3-P/BgSrM--{'
    )
);
define('EVOLVE_GRANT_TYPE','password');

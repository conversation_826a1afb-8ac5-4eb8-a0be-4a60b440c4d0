<?php

// -- include and invoke the session handler
use <PERSON>8<PERSON><PERSON>s\RequestHandler;

include SYSTEMPATH . '/config.php';
include SYSTEMPATH . '/lib/classes/Session.php';
$sess = new Session();

// -- invoke the database handler and create a new connection to the system database
$dbh = new MSSQL(SYSTEMDSN);
define('CLIENTDB', $sess->get('currentDB'));

$request = new RequestHandler();
$request->handleRequest();

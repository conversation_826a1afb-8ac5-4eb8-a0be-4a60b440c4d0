<?php

//error_reporting(E_ALL ^ E_NOTICE);

  session_start();

  if (!function_exists("ob_get_clean")) {
    function ob_get_clean() {
        $ob_contents = ob_get_contents();
        ob_end_clean();
        return $ob_contents;
    }
}


include('config.php');
$dbh = new MSSQL(SYSTEMDSN);




        echo '<link rel="stylesheet" href="'.ASSET_DOMAIN.'assets/css/data.css" type="text/css" />
              <b>patching tables in npms</b><br /><br />
              <ul>';

        $dbMap = array('nscv2','gwv2','fps','lancom','lsv2','vsa','pprop','rn');

        $sql = "SELECT * FROM user_list";
        $userList = $dbh->executeSet($sql);
        $userList = mapByKey($userList,'user_name');
        //echo print_array($userList,true);

        $sql = "SELECT * FROM database_list";
        $databaseList = $dbh->executeSet($sql);
        $databaseList = mapByKey($databaseList,'database_name');
        //echo print_array($databaseList,true);

        $sql = "SELECT * FROM user_database_access";
        $userDatabaseList = $dbh->executeSet($sql);
        $userDatabaseList = mapbyKey($userDatabaseList,'user_name');
        //echo print_array($userDatabaseList,true);

      foreach ($userList as $username => $user) {
            echo '<b>Database access for ' . $username . '</b><br />';
            foreach ($dbMap as $db) {
                if ($userDatabaseList[$username][$db] == 1) {
                    echo 'mapping ' .  $db . ' => ' . $databaseList[$db]['database_id'] . '<br />';
                    $sql = "INSERT INTO user_dbaccess (user_id,db_id) VALUES('{$user['user_id']}','{$databaseList[$db]['database_id']}')";
                    $dbh->executeNonQuery($sql);
                }

            }
        }

        echo '</ul><br/><b>table patching complete</b>';





?>
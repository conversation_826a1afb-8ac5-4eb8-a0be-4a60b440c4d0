<?php

declare(strict_types=1);

namespace C8Utils;

use Error;
use Session;
use Exception;

class RequestHandler
{
    private string $defaultModule = 'home';
    private string $defaultCommand = 'home';

    public function handleRequest()
    {
        if (MAINTENANCE) {
            Session::getInstance()->destroy();
            echo 'Under Maintenance';
            return;
        }

        $this->validateRequest();

        try {
            $this->dispatchRequest();

        } catch (Exception $e) {
            $this->handleError($e);
        }
    }

    private function validateRequest()
    {
        if ($_GET['command'] == 'noAccess') {
            executeCommand('noAccess');
            exit();
        }

        if ($_GET['command'] == 'home'
            && $_GET['source'] == 'login'
            && $_GET['type'] == 'baseauth'
            && isset($_GET['token'])) {
            executeCommand('baseauth');
            return;
        }
        $userID = Session::getInstance()->get('userID');

        if (!$userID) {
            header('Location: '.SSO_LOGIN);
            return;
        }

        $verifyLogin = dbGetLoginStats($userID);

        if (!$verifyLogin) {
            return;
        }

        pingUser($userID, Session::getInstance()->get('groupID'), HTTPHOST . $_SERVER['REQUEST_URI']);
        setcookie('login', Session::getInstance()->get('un'), time() + 60 * 60 * 24 * 30, '/', Env::get('APP_DOMAIN'), true);
        setcookie('uid', $userID, time() + 60 * 60 * 24 * 30, '/', Env::get('APP_DOMAIN'), true);
        $hour_diff = dbGetHourDiffSSOLog(Session::getInstance()->get("un"), Session::getInstance()->get("sso_key"));

        if ($hour_diff > 3) {
            Session::getInstance()->destroy();
            header("Location: ".logout_url);
            exit();
        }

        dbUpdateSSOLastActive(Session::getInstance()->get("un"), Session::getInstance()->get("sso_key"));
    }

    private function dispatchRequest()
    {
        $command = $this->queryStringValue('command', $this->getDefaultCommand());
        $module = $this->queryStringValue('module', $this->getDefaultModule());

        if (!Session::getInstance()->loggedIn()) {
            $command = 'login';
            $module = 'home';
        }
        $id = Session::getInstance()->get('userID');
        $reged = Session::getInstance()->get('registered');
        $sid = session_id();

        if (ENVIRONMENT !== 'LIVE') {
            executeCommand($command, $module);
        }

        try {
            executeCommand($command, $module);
        } catch (Error $e) {
            myErrorHandlerV2(1, $e->getMessage(), $e->getFile(), $e->getLine());
        }
    }

    private function queryStringValue(string $key, string $default = null): string
    {
        if (isset($_GET[$key])) {
            return htmlspecialchars($_GET[$key]);
        }
        return htmlspecialchars($default);
    }

    private function isUserType(): bool
    {
        return isset($_SESSION['user_sub_type']) and $_SESSION['user_sub_type'] == "AP_1";
    }

    private function getDefaultModule(): string
    {
        return $this->isUserType() ? 'ap' : $this->defaultModule;
    }

    private function getDefaultCommand(): string
    {
        return $this->isUserType() ? 'invoice' : $this->defaultCommand;
    }

    private function handleError(Exception $e)
    {
        $msg = get_class($e) . PHP_EOL ;
        $msg .= 'code : '.$e->getCode() . PHP_EOL ;
        $msg .= 'file : '.$e->getFile() . 'line : '.$e->getLine() . PHP_EOL ;
        $msg .= 'mess : '.$e->getMessage() . PHP_EOL ;
        $msg .= 'detl : '.$e . PHP_EOL ;
        $msg .= 'PDFLIB';
        echo $msg;
    }
}

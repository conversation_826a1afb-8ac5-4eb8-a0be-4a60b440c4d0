<?php

declare(strict_types=1);

namespace C8Utils;

use Exception;
use Predis\Client;

class RedisClient
{
    private Client $redis;
    private static ?RedisClient $instance = null;

    public function __construct()
    {
        $host = Env::get('REDIS_HOST', '127.0.0.1');
        $port = Env::get('REDIS_PORT', '6379');

        try {
            $this->redis = new Client([
            'scheme' => 'tcp',
            'host' => $host,
            'port' => $port,
        ]);
        } catch (Exception $e) {
            throw new Exception('Failed to connect to Redis: ' . $e->getMessage());
        }
    }

    public static function getRedis(): Client
    {
        return self::getInstance()->redis;
    }

    public static function getInstance(): self
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

}

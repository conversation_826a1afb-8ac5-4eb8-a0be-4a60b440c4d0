<?php

namespace C8Utils;

class Env
{
    /**
     * Get the value of an environment variable.
     *
     * @param  string  $name  the env key to get
     * @param  mixed  $default  the fallback default if not defined
     * @return bool|int|null|string
     */
    public static function get(string $name, $default = null)
    {
        $value = $_ENV[$name] ?? $default;

        return self::convert($value);
    }

    /**
     * Converts the type of values like "true", "false", "null" or "123".
     *
     * @return bool|int|string|null
     */
    public static function convert(string $value)
    {
        switch (strtolower($value)) {
            case 'true':
                return true;

            case 'false':
                return false;

            case 'null':
                return;
        }

        if (ctype_digit($value)) {
            return (int) $value;
        }

        if (! empty($value)) {
            return self::stripQuotes($value);
        }

        return $value;
    }

    /**
     * Strip quotes.
     *
     * @param  string  $value  the value to strip quotes from
     * @return string the stripped string
     */
    private static function stripQuotes(string $value): string
    {
        if (
            ($value[0] === '"' && substr($value, -1) === '"')
            || ($value[0] === "'" && substr($value, -1) === "'")
        ) {
            return substr($value, 1, -1);
        }

        return $value;
    }
}

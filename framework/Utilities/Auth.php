<?php

namespace C8Utils;

use Session;

class Auth
{

    public static function validateLogin(string $userID): void
    {
        $verifyLogin = dbGetLoginStats($userID);
        $authKeySaved = $verifyLogin['authKey'];
        $authKeyCurrent = self::createAuthKey($userID);

        if (! Session::getInstance()->getClientDB()) {
            // ADD THIS SINCE THERE IS A TIME THAT CURRENT DATABASE SESSION BECAME NULL
            self::logout();
        }

        if (! $verifyLogin && $userID) {
            self::logout('logout');
        }

        if ($authKeyCurrent != $authKeySaved) {
            self::logout();
        }
        $sess = Session::getInstance();
        pingUser($userID, $sess->get('groupID'), HTTPHOST . $_SERVER['REQUEST_URI']);
        setcookie('login', $sess->get('un'), time() + 60 * 60 * 24 * 30, '/', APP_DOMAIN, true);
        setcookie('uid', $userID, time() + 60 * 60 * 24 * 30, '/', APP_DOMAIN, true);
    }

    private static function createAuthKey(string $userID): string
    {
        $sess = Session::getInstance();

        return md5($userID . session_id() . $_SERVER['HTTP_USER_AGENT'] . $sess->get('_timestamp'));
    }

    private static function logout(string $action = 'timeout'): void
    {
        global $sess, $context;
        $sess->destroy();
        $context['action'] = $action;
        executeCommand('logout');
        exit();
    }

}

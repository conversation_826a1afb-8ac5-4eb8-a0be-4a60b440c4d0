<?

$filename = 'd:\www\tracc_development\newpms\modules\ar\tax_invoice_statements_step_3.php';
$distinct = true;
$file = file_get_contents($filename);

$pattern = '/$pdf->[^(]*/';

preg_match_all ($pattern ,$file ,$matches);
$matches = $matches[0];

if ($distinct)
{
    foreach ($matches as $match) {
        $matchList[$match] = $match;
    }
} else $matchList = $matches;

echo implode('<br/>',$matchList);



?>
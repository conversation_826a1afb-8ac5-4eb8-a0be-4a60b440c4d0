<?

function numberToLetter($number)
{
    $index = 65;

    $n = $number - 1;

    $first = floor($n / 26);
    if ($first) {
        $output .= chr($index + $first - 1);
    }
    $output .= chr($index + ($n % 26));
    return $output;
}

function read_all_files($root = '.')
{
    $files = array('files' => array(), 'dirs' => array());
    $directories = array();
    $last_letter = $root[strlen($root) - 1];
    $root = ($last_letter == '\\' || $last_letter == '/') ? $root : $root . DIRECTORY_SEPARATOR;

    $directories[] = $root;

    while (sizeof($directories ?? [])) {
        $dir = array_pop($directories);
        if ($handle = opendir($dir)) {
            while (false !== ($file = readdir($handle))) {
                if ($file == '.' || $file == '..') {
                    continue;
                }
                $file = $dir . $file;
                if (is_dir($file)) {
                    $directory_path = $file . DIRECTORY_SEPARATOR;
                    array_push($directories, $directory_path);
                    $files['dirs'][] = $directory_path;
                } elseif (is_file($file)) {
                    $files['files'][] = $file;
                }
            }
            closedir($handle);
        }
    }

    return $files;
}

function parseFileCells($file)
{
    $f = file_get_contents($file);
    $regex = '/Cells\(([\d]+),([\d]+)\)/';
    $matches = array();

    preg_match_all($regex, $f, $matches);
    if (count($matches[0])) {
        $s = sizeof($matches[0] ?? []);
        for ($i = 0; $i < $s; $i++) {
            $cell = numberToLetter($matches[2][$i]) . $matches[2][$i];
            $search = $matches[0][$i];
            //echo $search;
            $replace = "->getActiveSheet()->SetCellValue(cellReference('{$cell}',";
            $f = str_ireplace($search, $replace, $f);
        }
        file_put_contents($file, $f);
    }
    //  echo $file . '<br/>';
    /*   if (count($matches[0])) {
           //print_r($matches);
           $m = $matches[1];
           $fontList = array();
           echo "{$file}<br/><br/>";
           foreach ($m as $_m) {
               $a = explode(',',$_m);
               $font = trim($a[0],' "');
               if ($font{0} != '$') {
               if (!isset($fontList[$font])) {
                   $fontList[$font] = $font;
                   $search = '"' . $font . '"';
                   $replace = '$_fonts["' . $font . '"]';
                   $f = str_ireplace($search, $replace, $f);
               }
               $size = trim($a[1],' "');

               //echo $font . ' ' . $size . '<br/>';
           }
           $f = str_ireplace(', "host"', '', $f);
           $f = str_ireplace(',"host"', '', $f);
           $f = str_ireplace('set_font','setfont',$f);
           }

           file_put_contents($file, $f);

           print_r($fontList);
       }*/
}


function parseFileColor($file)
{
    $f = file_get_contents($file);
    $regex = '/setcolor([^;]*);/';
    $matches = array();
    preg_match_all($regex, $f, $matches);
    if (count($matches[0])) {
        //print_r($matches);
        $m = $matches[0];
        $i = 0;
        $search = array();
        $replace = array();
        foreach ($m as $_m) {
            $a = explode(',', $_m);
            if (count($a) == 5) {
                $search[$i] = $_m;
                $_s = substr($_m, 0, -1);
                $_s .= ', 0)';
                $replace[$i] = $_s;
                $i++;
            }
        }

        if (count($search) > 0) {
            echo "{$file}<br/><br/>";
        }
        $f = str_ireplace($search, $replace, $f);
        file_put_contents($file, $f);
    }
}


function parseFileBoxes($file)
{
    $f = file_get_contents($file);
    $regex = '/show_boxed([^;]*);/';
    $matches = array();
    preg_match_all($regex, $f, $matches);
    if (count($matches[0])) {
        //print_r($matches);
        $m = $matches[1];
        $i = 0;
        $search = array();
        $replace = array();
        foreach ($m as $_m) {
            $a = explode(',', $_m);
            //   echo count($a);
            //    if (count($a) == 5) {
            $search[$i] = $_m;
            $_s = substr($_m, 0, -1);
            $_s .= ', "")';
            $replace[$i] = $_s;
            $i++;
            //  }
        }

        if (count($search) > 0) {
            echo "{$file}<br/><br/>";
        }
        $f = str_ireplace($search, $replace, $f);
        file_put_contents($file, $f);
    }
}


$path = $_SERVER["DOCUMENT_ROOT"] . '\newpms';
$files = read_all_files($path);
$files = $files['files'];

if ($files) {
    foreach ($files as $file) {
        $ext = end(explode('.', $file));
        if ($ext == 'php') {
            parseFileCells($file);
        }
    }
}


?>
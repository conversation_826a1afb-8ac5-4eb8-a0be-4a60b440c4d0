<?php

require_once SYSTEMPATH . '/lib/fileuploader/class.fileuploader.php';
include SYSTEMPATH . '/config.php';

/**
 *TransferJournalService singleton class.
 */
class TransferJournalService
{
    /**
     * Hold the class instance.
     */
    protected static ?TransferJournalService $instance = null;

    final private function __construct() {}

    /**
     * @getInstance object is created from within the class itself.
     */
    public static function getInstance(): ?TransferJournalService
    {
        if (! self::$instance instanceof \TransferJournalService) {
            self::$instance = new TransferJournalService();
        }

        return self::$instance;
    }

    /**
     * Update transaction data for document reference.
     *
     * @param  array  $documents  list of transaction data.
     */
    public function updateAttachmentToDocumentTables(array $documents, int $batchNumber, string $type): void
    {
        $data = [];
        foreach ($documents as $document) {
            $data['batchNumber'] = $batchNumber;
            $data['documentID'] = $document['document_id'];
            $data['type'] = $type;
            dbUpdateDocumentInvoiceBatchNumber($data);
        }

    }

    /**
     * Insert transaction data for document reference.
     *
     * @param  array  $transaction  list of transaction data.
     */
    public function addAttachmentToDocumentTable(array $transaction): int
    {
        global $clientDirectory;
        $documentTitle = explode('.pdf', $transaction['file_name']);
        $doc['documentTitle'] = implode('', $documentTitle);
        $doc['documentType'] = DOC_INV_SUPPLIER;
        $doc['createdBy'] = $_SESSION['un'];
        $doc['primaryID'] = $transaction['propertyID'];
        $doc['secondaryID'] = $transaction['creditorID'];
        $doc['attachAR'] = $transaction['attachAR'];
        $doc['attachOwnerR'] = $transaction['attachOwnerReportFile'];
        $doc['documentApTag'] = 1;
        $doc['filename'] = "{$clientDirectory}/pdf/SupplierInvoices/{$transaction['file_name']}";
        $doc['documentDescription'] = null;

        return dbAddDocument($doc);
    }

    /**
     * Insert transaction data for document invoice reference.
     *
     * @param  array  $transaction  list of transaction data.
     */
    public function addAttachmentToDocumentInvoiceTable(array $transaction): void
    {
        $invoiceDoc['propertyID'] = $transaction['propertyID'];
        $invoiceDoc['batchNumber'] = $transaction['batchNumber'];
        $invoiceDoc['lineNumber'] = $transaction['lineNumber'];
        $invoiceDoc['attachAR'] = $transaction['attachAR'];
        $invoiceDoc['attachOwnerR'] = $transaction['attachOwnerReportFile'];
        $invoiceDoc['documentID'] = $transaction['lastID'];
        $invoiceDoc['transactionType'] = $transaction['transactionType'];
        $invoiceDoc['leaseID'] = null;
        $invoiceDoc['transferJournalTemporaryID'] = $transaction['transferJournalTemporaryID'] ?? null;
        dbInsertLinkedInvoiceDocument($invoiceDoc);
    }

    /**
     * Upload document attachment.
     *
     * @param  array  $files  input files.
     */
    public function uploadAttachment(array $files): array
    {
        global $pathPrefix, $clientDirectory;
        $uploadDirectory = "{$pathPrefix}{$clientDirectory}/pdf/SupplierInvoices/";
        $attachments = [];
        if ($files === []) {
            return [];
        }

        foreach ($files['name'] as $key => $file) {
            $ext = pathinfo(basename($file, PATHINFO_FILENAME))['extension'];
            $baseName = basename($file);
            $fileName =  $this->createFileName($baseName);
            $filePath = "{$uploadDirectory}/{$fileName}.{$ext}";

            if (! file_exists("{$uploadDirectory}")) {
                mkdir("{$uploadDirectory}", FILE_PERMISSION, true);
            }

            // tmp_name stores the temporary file path.
            move_uploaded_file($files['tmp_name'][$key], $filePath);
            $attachments[$key]['extension'] = $ext;
            $attachments[$key]['file'] = $filePath;
            $attachments[$key]['file_name'] = $fileName;
            $attachments[$key]['content_type'] = mime_content_type($filePath);
        }

        return $attachments;
    }

    /**
     * Duplicate a copy of uploaded document.
     *
     * @param  string  $origin  file path to copy.
     * @param  string  $filename  filename for new copy of document.
     */
    public function duplicateAttachment(string $origin = '', string $filename = ''): void
    {
        global $pathPrefix, $clientDirectory;
        $duplicateDestination = "{$pathPrefix}{$clientDirectory}/pdf/SupplierInvoices/{$filename}";
        copy($origin, $duplicateDestination);
    }

    /**
     * Delete document attachment.
     *
     * @param  string  $filename  file path.
     */
    public function deleteFile(string $filename): void
    {
        global $pathPrefix;
        $filenamePath = "{$pathPrefix}/{$filename}";
        unlink($filenamePath);

    }

    /**
     * List down document attachments.
     *
     * @param  int  $journalID  temporary journal id.
     */
    public function listOfAttachments(int $journalID): array
    {
        $attachedDocuments = dbSelectDocumentInvoiceByTemporaryJournalID($journalID);
        $attachToARFile = null;
        $attachToOwnerReportFile = null;
        if (count($attachedDocuments) === 0) {
            return ['attachedDocumentsList' => [], 'attachToARFile' => $attachToARFile, 'attachToOwnerReportFile' => $attachToOwnerReportFile];
        }

        foreach ($attachedDocuments as $d) {
            $attachToARFile = $d['attachAR'];
            $attachToOwnerReportFile = $d['attachOwnerR'];
        }

        return [
            'attachedDocumentsList' => $attachedDocuments,
            'attachToARFile' => $attachToARFile,
            'attachToOwnerReportFile' => $attachToOwnerReportFile,
        ];

    }

    /**
     * Create file name.
     *
     * @param  string  $baseName  an attached filename.
     */
    private function createFileName(string $baseName): string
    {
        return substr($baseName, 0, 15) . '_' . mt_rand(0, 10000) . time();
    }
}

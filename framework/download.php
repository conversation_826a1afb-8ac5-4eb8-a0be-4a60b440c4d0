<?php
//-- include and invoke the session handler
include 'lib/classes/Session.php';
$sess = new Session ();
ob_start();

  if (!function_exists("ob_get_clean")) {
    function ob_get_clean() {
        $ob_contents = ob_get_contents();
        ob_end_clean();
        return $ob_contents;
    }
}

include('config.php');

date_default_timezone_set ('Australia/Perth');
define ('TODAY', date ('d/m/Y'));

$referrer = 'https://' . $_SERVER['HTTP_HOST'];

if ($referrer == HTTPHOST)
{
    $sess->set ('referrer', HTTPHOST . $_SERVER['SCRIPT_NAME'] . '?' . $_SERVER['QUERY_STRING']);
}
else $sess->drop ('referrer');

$sess->drop ('queries');

$clientDB = $sess->get ('currentDB');
$clientDSN = COREDSN . $sess->get ('currentDB');
//-- invoke the database handler and create a new connection to the system database

$dbh = new MSSQL (SYSTEMDSN);

if(SSO_ENABLED){
    if ($_GET['command'] == 'noAccess'){
        executeCommand ('noAccess');
        exit();
    }
    elseif ($_GET['command'] == 'home' && $_GET['action'] == 'login' && $_GET['type'] == 'baseauth' && isset($_GET['token'])){
        executeCommand ('baseauth');
    }

    else{
        if (!$sess->get ('userID')){
            header('Location: '.SSO_LOGIN);
        }
        else{
            if($sess->get ('userID')){
                $userID = $sess->get ('userID');
                if ($userID) { // just check if logged in regardless of timestamp
                    // download content --- start

                    global $pathPrefix;

                    if ($_REQUEST['fileID']) {
                        $filename = $_REQUEST['fileID'];
                        $filename = decodeParameter($filename);
                        $filePath = $pathPrefix . $filename;

                        $hasAccess = false;

                        $dbNameAccess = mapParameters(dbGetDatabasePermissions($userID),'description_no_space','databaseName');
                        $filenameParts = explode('/',str_replace("\\","/", $filename));

                        if (substr($filename,0,15) == 'administration/')
                            $hasAccess = true;
                        else
                        {
                            for ($i=0; $i <= 3; $i++) { // db name is in index 0 or sometimes index 2. This will check for the db name up to index 3
                                $clientDBDL = ($dbNameAccess[$filenameParts[$i]]);
                                if ($clientDBDL) break;
                            }

                            if ($clientDBDL AND $_SESSION['user_type'] != 'O')
                                $hasAccess = true;
                            elseif($clientDBDL AND $_SESSION['user_type'] == 'O'){
                                $invProperty = '';
                                $fileNamePartsEnd = $filenameParts[array_key_last($filenameParts)];

                                if (substr($fileNamePartsEnd,0,12) == 'tax_invoice_'){
                                    $parts = explode ('_', $fileNamePartsEnd);
                                    $invProperty = $parts[4];
                                    $hasAccess = (bool)checkOwnerPropertyForDownload($filename, $invProperty);
                                }

                                if (!$hasAccess) {
                                    $file_folder = strtoupper($filenameParts[2]);
                                    $hasAccess = checkOwnerFileAccess($file_folder, $filename);
                                }
                            }
                        }
                        if (file_exists($filePath) AND $hasAccess) {

                            $extension = substr($filePath, strrpos($filePath,'.')+1);

                            // required for IE, otherwise Content-disposition is ignored
                            ini_set('zlib.output_compression','Off');

                            switch($extension)
                            {
                                case "pdf": $ctype="application/pdf"; break;
                                case "exe": $ctype="application/octet-stream"; break;
                                case "zip": $ctype="application/zip"; break;
                                case "doc": $ctype="application/msword"; break;
                                case "xls": $ctype="application/vnd.ms-excel"; break;
                                case "xlsx": $ctype="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"; break;
                                case "ppt": $ctype="application/vnd.ms-powerpoint"; break;
                                case "pptx": $ctype="application/vnd.openxmlformats-officedocument.presentationml.presentation"; break;
                                case "gif": $ctype="image/gif"; break;
                                case "png": $ctype="image/png"; break;
                                case "jpeg": $ctype="image/jpeg"; break;
                                case "jpg": $ctype="image/jpg"; break;
                                case "aba" : $ctype="text/plain"; break;
                                case "txn" : $ctype="application/mbldeft-transaction"; break;
                                case "brf" : $ctype="application/mbldeft-bpay"; break;
                                case "pay" : $ctype="application/mbldeft-payment"; break;
                                case "acc" : $ctype="application/mbldeft-acc"; break;
                                default: $ctype="application/force-download";
                            }

                            header("Pragma: public"); // required
                            header("Expires: 0");
                            header("Cache-Control: must-revalidate, post-check=0, pre-check=0");
                            header("Cache-Control: private",false); // required for certain browsers
                            header("Content-Type: $ctype");
                            header("Content-Transfer-Encoding: none");
                            // change, added quotes to allow spaces in filenames
                            header('Content-Disposition: attachment; filename="'.basename(!$_REQUEST['fileName'] ? $filePath : $_REQUEST['fileName']).'";');

                            header("Content-Length: ".filesize($filePath));
                            ob_end_clean();
                            readfile($filePath);
                            exit();

                        } else {

                            exit();
                            $host  = $_SERVER['HTTP_HOST'];
                            $uri   = rtrim(dirname($_SERVER['PHP_SELF']), '/\\');
                            $extra = 'index.php';
                            header("Location: http://$host$uri/$extra?command=error");
                            exit();

                            echo 'File cannot be found - please contact Cirrus8 Support';
                        }
                    }
                    // -----------------------
                }
                else {
                    if (!SSO_ENABLED && $userID){
                        $sess->destroy ();
                        header("Location: ".logout_url);
                        exit ();
                    }
                }
            }
        }
    }
}
else{ //SSO_ENABLED should be true even on local
     executeCommand ('login');
        exit ();
}


function checkOwnerFileAccess($file_folder, $filename){
    switch ($file_folder) {
        case 'MANAGEMENTAGREEMENT':
            return checkOwnerManagementAgreementForDownload($filename);
        case 'PROPERTYINSURANCE':
            return checkOwnerPropertyInsuranceForDownload($filename);
        case 'LEASEINSURANCE':
            return checkOwnerLeaseInsuranceForDownload($filename);
        case 'LEASEGUARANTEE':
            return checkOwnerLeaseGuaranteeForDownload($filename);
        default:
            return (bool)checkOwnerPropertyForDownload($filename);
    }
}
?>
{"name": "cirrus8/framework", "license": "proprietary", "type": "project", "description": "Cirrus8 is a web-based property management system.", "require": {"php": "^7.4", "ext-bcmath": "*", "ext-curl": "*", "ext-gd": "*", "ext-intl": "*", "ext-json": "*", "ext-mbstring": "*", "ext-pdo": "*", "ext-sqlsrv": "*", "ext-zip": "*", "ext-zlib": "*", "ext-pdflib": "*", "ext-soap": "*", "bugsnag/bugsnag": "^3.0", "clue/ndjson-react": "^1.3.0", "ezyang/htmlpurifier": "^4.18.0", "guzzlehttp/guzzle": "^7.9.3", "jaspersoft/rest-client": "^2.0.0", "kigkonsult/icalcreator": "^2.39.2", "nuovo/spreadsheet-reader": "^0.5.11", "phpmailer/phpmailer": "^5.2.6", "phpoffice/phpspreadsheet": "^1.29.11", "predis/predis": "^3.0", "ralouphie/getallheaders": "^3.0.3", "rmccue/requests": "^2.0", "setasign/fpdi": "^2.6.3", "tecnickcom/tcpdf": "^6.6", "tpyo/amazon-s3-php-class": "^0.5.1", "vlucas/phpdotenv": "^5.6.2", "wildbit/postmark-php": "^4.0.5"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.73"}, "autoload": {"psr-4": {"C8Utils\\": "Utilities/"}}, "scripts": {"lint": ["Composer\\Config::disableProcessTimeout", "PHP_CS_FIXER_IGNORE_ENV=1 vendor/bin/php-cs-fixer fix --allow-risky=yes"], "check-style": "PHP_CS_FIXER_IGNORE_ENV=1 vendor/bin/php-cs-fixer check --allow-risky=yes"}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "platform": {"php": "7.4"}}}
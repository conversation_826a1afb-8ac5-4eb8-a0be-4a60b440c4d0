<?php

define('AUTH_PASS', 1);
define('api_key', '5LSIG93LGLSD8G2GLKF1301LFK');
define('AUTH_FAIL', 0);
define('4', 2);
define('AUTH_DUPLICATE', 3);
define('LICENSE_EXPIRED', 4);
define('AUTH_FIRST_TIME', 5);
define('AUTH_NEW_TERMS', 6);

function baseauth(&$context)
{
    global $sess;
    $view = new UserControl(null, '');
    $view->bindAttributesFrom($context);
    $view->bindAttributesFrom($_REQUEST);

    $token = $view->items['token'];
    $auth_keys = getAuthkeys($token);

    if (! isset($auth_keys['app_key']) || ! isset($auth_keys['token'])) {
        header('Location: ' . SYSTEMURL . '/index.php?module=home&command=noAccess&error=Unauthorized token.');
        exit();
    }

    $app_key = $auth_keys['app_key'];
    $app_token = $auth_keys['token'];
    $data = baseauthInit($app_key);
    if (! $data || ! isset($data['email'])) {
        header('Location: ' . SYSTEMURL . '/index.php?module=home&command=noAccess&error=Invalid login key.');
        exit();
    }
    //
    $email = $data['email']; // MUST USE EMAIL
    $super_enabled = true;
    $user = dbGetUserByEmail($email);
    $authResult = AUTH_FAIL;
    $userID = null;
    //
    if (! empty($user)) {
        $authResult = AUTH_PASS;
        // $userID = $authCheck['userID'];
        $userID = $user['userID'];
    }
    if ($authResult == AUTH_PASS) {
        // CHECK IF HAS CIRRUS8 PORTAL ACCESS
        $portals = dbGetUserPortals($email);
        if (count($portals ?? []) == 0 || (count($portals ?? []) > 0 && ! in_array('cirrus8', $portals))) {
            header('Location: ' . SYSTEMURL . '/index.php?module=home&command=noAccess');
            exit();
        }

        $sesC8 = apiConnect($app_token);
        $sess->clear();
        $sess->set('registered', true);
        dbClearCurrentLogin($userID);
        dbResetUserAttempt($userID);
        $userDetails = dbGetUserDetails($user['user_name'], true);
        // $superAccess = dbCheckUserSuperAccess($userID);
        $superAccess = true;

        if (! $super_enabled) {
            $userDetails['super'] = 0;
        } elseif (! $superAccess) {
            $userDetails['super'] = 0;
        } else {
            if (isset($userDetails['super']) && $userDetails['super'] == 1) {
                $super_user_access_codes = dbGetUserSuperAccess($userID);

                foreach ($super_user_access_codes as $sucs) {
                    if (in_array($sucs['access_code'], SUPER_USER_CODES)) {
                        $userDetails['super_' . $sucs['access_code']] = 1;
                    }
                }
            }
        }

        $sess->bindAttributesFrom($userDetails);
        $dbList = dbGetDatabaseList();
        // $sess->set ('dbList', $dbList);
        $sess->set('userID', $userID);


        $permissions = mapParameters(dbGetUserPermissions($sess->get('user_id'), SSO_ENABLED), 'subGroupID', 'subGroupName');
        $access_dbs = dbGetDatabasePermissions($sess->get('user_id'));

        // CHECK FOR DATABASES THAT USER HAS ACCESS TO BASED ON ACCESS START DATE AND EXPIRE DATE
        $today = time();
        foreach ($access_dbs as $adb_key => $db) {
            //
            // if($db['access_start_date'] != "" ){
            //   $s_date = explode('/', $db['access_start_date']);
            //   $s_date_str = strtotime($s_date[2]."-".$s_date[1]."-".$s_date[0]);
            //   if($today < $s_date_str ){
            //     unset($access_dbs[$adb_key]);
            //   }
            // }

            if ($db['access_expire_date'] != '') {
                $e_date = explode('/', $db['access_expire_date']);
                $e_date_str = strtotime($e_date[2] . '-' . $e_date[1] . '-' . $e_date[0]);
                if ($today >= $e_date_str) {
                    unset($access_dbs[$adb_key]);
                }
            } else {
                unset($access_dbs[$adb_key]);
            }
            //
        }

        $databases = mapParameters($access_dbs, 'databaseID', 'description');

        // CHECK FOR LOCK ROLES, REMOVE CLIENTS THAT REQUIRED ROLES ARE NOT FOUND IN IT
        $required_role_ids = dbGetCirrus8RequiredRoleIDs();
        $role_locks_raw = dbGetUserLockRolesDetailed($userID);

        // FILTER LOCKS REMOVE ROLES THAT ARE NOT IN CIRRUS8 PORTAL
        $role_locks = [];
        foreach ($role_locks_raw as $lck) {
            if (in_array($lck['sub_group_id'], $required_role_ids)) {
                $role_locks[] = $lck;
            }
        }

        foreach ($databases as $db_id => $db) {
            $db_locks = [];
            if (count($role_locks ?? []) > 0) {
                foreach ($role_locks as $lck) {
                    if ($db_id == $lck['database_id']) {
                        $db_locks[] = ['id' => $lck['sub_group_id'], 'code' => trim($lck['subGroupCode']), 'name' => $lck['subGroupName']];
                    }
                }
            }
            if (count($db_locks ?? []) > 0) {
                $found = false;
                foreach ($db_locks as $lckey => $dblck) {
                    if (in_array($dblck['id'], $required_role_ids)) {
                        $found = true;
                        break;
                    }
                }
            } else {
                $found = true;
            }

            if (! $found) {
                unset($databases[$db_id]);
            }
        }

        // SET DB
        if (! isset($databases[$sess->get('default_db')])) {
            reset($databases);
            $use_db_id = key($databases);
        } else {
            $use_db_id = $sess->get('default_db');
        }

        // SET ROLE
        $curr_role = trim($userDetails['user_sub_type']);
        $found = false;
        $ctr = 1;
        foreach ($role_locks as $rl) {
            if ($use_db_id == $rl['database_id']) {
                $subGroupCode = trim($rl['subGroupCode']);
                if ($curr_role == $subGroupCode) {
                    $found = true;
                }
                if ($ctr == 1) {
                    $first_role = $subGroupCode;
                }
            }
            if ($found) {
                break;
            }
        }
        if (! $found) {
            $curr_role = $first_role;
        }

        if (isset($view->items['re']) && (int) $view->items['re'] == 1) {
            // $app_data = switchApp($app_key,$email,trim($userDetails['user_sub_type']),$use_db_id);
            $app_data = switchApp($app_key, $email, $curr_role, $use_db_id);
            if (isset($app_data['app_key'])) {
                $app_key = $app_data['app_key'];
            } else {
                $view->items['notice'] = clientSideRedirect('?command=home&module=home');
            }
        }

        $sess->set('currentDB', $dbList[$use_db_id]['database_name']);
        $_SESSION['currentDB'] = $dbList[$use_db_id]['database_name'];
        $_SESSION['database'] = $dbList[$use_db_id]['description'];
        $_SESSION['development'] = $dbList[$use_db_id]['development'];
        $sess->set('database', $dbList[$use_db_id]['description']);
        $sess->set('databaseID', $use_db_id);

        $sess->set('userGroupList', $permissions);
        $sess->set('databaseGroupList', $databases);
        $currentTime = time();
        $sess->set('_timestamp', $currentTime);

        $sess->set('country_code', $dbList[$use_db_id]['country_code']);
        $cdf_content = json_decode(file_get_contents(CDF_JSON_FILE), true);
        $sess->set('country_default', $cdf_content[$dbList[$use_db_id]['country_code']]);

        // $timezone = dbGetTimezone($dbList[$use_db_id]['timezone']);
        // $sess->set ('clientTimezone', $timezone['timezone_info']);

        // REGISTER SESSION
        $userID = $userDetails['user_id'];
        $authKey = md5($userID . session_id() . $_SERVER['HTTP_USER_AGENT'] . $currentTime);
        dbInsertLoginStats($userID, $_SERVER['HTTP_USER_AGENT'], $_SERVER['REMOTE_ADDR'], $_SERVER['REMOTE_HOST'], session_id(), $authKey, $currentTime);

        // SET CIRRUS8 SESSION
        $sess->set('portfolio', dbGetPortfolio($userID, $use_db_id));
        $sess->set('sso_key', $app_key);
        // $sess->set ('in_key', $in_token);
        // $sess->set ('sso_key', $token);
        $sess->set('un', $sess->get('user_name'));

        $sess->set('clientID', $sess->get('default_db'));
        $_SESSION['clientID'] = $sess->get('default_db');
        $sess->set('old_client_id', $sess->get('default_db'));

        $sess->set('syskey', $ses['token']);
        $sess->set('sysmsgs', $ses['message']);
        $sess->set('syserrors', $ses['errors']);

        // bind c8 api token
        // $sess->set('sysApi', $sesC8['access_token']);

        $userType = dbGetUserType($curr_role);
        if (! $userType) {
            $context['last_error'] = 'Could not fetch the users details';
            executeCommand('error');
            exit();
        } else {
            $sess->bindAttributesFrom($userType);
        }

        // must use app_token
        $sess->set('sysApi', $app_token);
        $sess->set('super_enabled', $super_enabled);

        // for country defaults
        // $countryCode = dbGetClientCountryCode($dbList[$use_db_id]['database_name']);
        // $countryCode = CDF_COUNTRY_CODE;
        // cdf_setCountryDefaultsSession($sess, $countryCode);
        $redirectTo = 'Location: ' . SYSTEMURL . '/index.php';
        header($redirectTo);
        exit();
    } else {
        $redirectTo = 'Location: ' . SYSTEMURL . '/index.php?module=home&command=noAccess&error=authorization failed.';
        header($redirectTo);
        exit();
    }
}
function getAuthObj($token)
{
    $cacert = SYSTEMPATH . cacert;
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, sso . $token);
    curl_setopt($ch, CURLOPT_HEADER, 0);
    curl_setopt($ch, CURLOPT_CAINFO, $cacert);
    curl_setopt($ch, CURLOPT_VERBOSE, true);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    if (LOCAL_SSL_ENABLED) {
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
    }
    $ses = json_decode(curl_exec($ch), true);
    curl_close($ch);

    return $ses;
}

function apiConnect($token)
{
    $cacert = SYSTEMPATH . cacert;
    $parms = [
        'app_key' => $token,
    ];
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, c8_api . 'api/sso/connect');
    curl_setopt($ch, CURLOPT_HEADER, 0);
    curl_setopt($ch, CURLOPT_CAINFO, $cacert);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($parms));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    if (LOCAL_SSL_ENABLED) {
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
    }
    $sesC8 = json_decode(curl_exec($ch), true);
    curl_close($ch);

    return $sesC8;
}

function getAuthkeys($token)
{
    $cacert = SYSTEMPATH . cacert;
    $parms = [
        'token' => $token,
    ];

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, c8_api . 'sso/session/OneLogin');
    curl_setopt($ch, CURLOPT_HEADER, 0);
    curl_setopt($ch, CURLOPT_CAINFO, $cacert);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($parms));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    if (LOCAL_SSL_ENABLED) {
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
    }

    $auth_keys = json_decode(curl_exec($ch), true);
    curl_close($ch);

    return $auth_keys;
}

function baseauthInit($token)
{
    $cacert = SYSTEMPATH . cacert;
    $parms = [
        'app_key' => $token,
        'app_code' => 'cirrus8',
    ];
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, c8_api . 'api/sso/baseauth/init');
    curl_setopt($ch, CURLOPT_HEADER, 0);
    curl_setopt($ch, CURLOPT_CAINFO, $cacert);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($parms));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    if (LOCAL_SSL_ENABLED) {
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
    }

    $sesC8 = json_decode(curl_exec($ch), true);
    curl_close($ch);

    return $sesC8;
}

function switchApp($token, $user, $user_sub_type, $database_id)
{
    $cacert = SYSTEMPATH . cacert;
    $params = [
        'user' => $user,
        'app_key' => $token,
        'app_code' => SSO_APP_CODE,
        'app_role' => $user_sub_type,
        'client_id' => $database_id,
        'switch_client' => $database_id,
    ];

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, c8_api . 'api/sso/client/switch');
    curl_setopt($ch, CURLOPT_HEADER, 0);
    curl_setopt($ch, CURLOPT_CAINFO, $cacert);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($params));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    if (LOCAL_SSL_ENABLED) {
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
    }

    $data = json_decode(curl_exec($ch), true);
    curl_close($ch);

    return $data;
}


function myC8Connect($user)
{
    $cacert = SYSTEMPATH . cacert;
    $params = ['email' => $user['user_name'], 'password' => $user['password']];
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, my_c8_api . 'setsession');
    curl_setopt($ch, CURLOPT_HEADER, 0);
    curl_setopt($ch, CURLOPT_CAINFO, $cacert);
    curl_setopt($ch, CURLOPT_VERBOSE, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($params));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    if (LOCAL_SSL_ENABLED) {
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
    }
    $ses = json_decode(curl_exec($ch), true);
    curl_close($ch);
    $parms = [
        'client_id' => '2',
        'client_secret' => 'qUXY5WRIHlLpTTE4y5SCB80rNeujgxmFXo7GOUsx',
        'grant_type' => 'password',
        'scope' => '*',
        'username' => $user['user_name'],
        'password' => $user['password'],
    ];
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, c8_api . 'oauth/token');
    curl_setopt($ch, CURLOPT_HEADER, 0);
    curl_setopt($ch, CURLOPT_CAINFO, $cacert);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($parms));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    if (LOCAL_SSL_ENABLED) {
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
    }
    $sesC8 = json_decode(curl_exec($ch), true);
    curl_close($ch);

    return $sesC8;
}

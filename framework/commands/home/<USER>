<?php

session_start();
define('AUTH_PASS', 1);
define('api_key', '5LSIG93LGLSD8G2GLKF1301LFK');
define('AUTH_FAIL', 0);
define('4', 2);
define('AUTH_DUPLICATE', 3);
define('LICENSE_EXPIRED', 4);
define('AUTH_FIRST_TIME', 5);
define('AUTH_NEW_TERMS', 6);

function login(&$context)
{
    if (true) {
        $view = new UserControl(null, '/login.html');
    } else {
        $view = new LoginPage('views/login.html');
        $view->setSection('login');
    }

    if (authenticator === 'sso') {
        header('Location: ' . SSO_LOGIN);
    }

    $view->bindAttributesFrom($context);
    $view->bindAttributesFrom($_REQUEST);
    $view->items['success'] = false;

    $slogan = ['Commercial Property Management Made Easy',
        'Constant Evolution with latest technology',
        'Cash and accruals accounting',
        'Integration with other accounting systems'];

    $view->items['slogan'] = $slogan[array_rand($slogan, 1)];

    // updates
    $updates = dbGetSystemUpdates();
    $view->items['updates'] = $updates;

    // check if IE
    $view->items['browser_ie'] = ae_detect_ie() ? 1 : 0;

    // -- if you have selected to kick a user - clear all current connections and masquerade a login attempt
    if ($view->items['action'] == 'kick') {
        // $passwordIDHash = $view->items['passwordIDHash'];
        $userID = $view->items['userID'];
        dbClearCurrentLogin($userID);
        $userDetails = dbGetLoginForUser($userID);
        $view->bindAttributesFrom($userDetails);
        $view->items['action'] = 'login';
        $view->items['twoFASuccess'] = true;
        // $view->items['password'] = $passwordIDHash;
    }

    if (isset($view->items['type']) && ! $sess->get('userID') && $view->items['type'] == 'baseauth' && authenticator === 'sso') {
        executeCommand('baseauth');
        exit();
    }

    // else{
    // 	header('Location: '.SSO_LOGIN);
    // }

    if ($view->items['action'] == 'login') {

        // FoR 2FA SUCCESS IF FOUND LOGIN
        if (isset($sess->items['doLoginUser'])) {
            $doLoginUser = dbGetUserByEmail($sess->items['doLoginUser']);
            if (isset($sess->items['login_with'])) {
                $view->items['login_with'] = $sess->items['login_with'];
            }

            $view->items['username'] = $doLoginUser['user_name'];
            $view->items['password'] = $doLoginUser['password'];
            if (isset($doLoginUser['user_name'])) {
                $view->items['twoFASuccess'] = true;
            }
        }

        if (ae_detect_ie() && ! dbCheckLogin($view->items['username'], $view->items['password'], true)) {
            $view->items['notice'] = 'LOGIN';
        }

        if ((! isValid($view->items['username'], TEXT_LOOSE, false)) || empty($view->items['password'])) {
            $view->items['notice'] = 'You have not supplied a valid username or password - Please check that they are      correct and try again.';
        } else {
            $authResult = AUTH_FAIL;
            // -- check their username and password
            if (preg_match("/^([a-zA-Z0-9])+([a-zA-Z0-9\._-])*@([a-zA-Z0-9_-])+([a-zA-Z0-9\._-]+)+$/", $view->items['username'])) {
                $authCheck = dbCheckLogin($view->items['username'], $view->items['password'], true);
                $parms = [
                    'email' => $view->items['username'],
                    'password' => $view->items['password'],
                ];
                $cacert = SYSTEMPATH . cacert;
                // sets the session on the worktrack
                /*
                $ch = curl_init();
                curl_setopt($ch, CURLOPT_URL, my_c8_api . 'setsession');
                curl_setopt($ch, CURLOPT_HEADER, 0);
                curl_setopt ($ch, CURLOPT_CAINFO, $cacert);
                curl_setopt($ch, CURLOPT_VERBOSE, true);

                //  $verbose = fopen('php://temp', 'w+');
                //  curl_setopt($ch, CURLOPT_STDERR, $verbose);

                curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($parms));
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                $ses = json_decode( curl_exec($ch), TRUE);
                curl_close($ch);
                */

                //  rewind($verbose);
                //  $verboseLog = stream_get_contents($verbose);

                //	echo "Verbose information:\n<pre>", htmlspecialchars($verboseLog), "</pre>\n";

                $parms = [
                    'client_id' => '2',
                    'client_secret' => 'qUXY5WRIHlLpTTE4y5SCB80rNeujgxmFXo7GOUsx',
                    'grant_type' => 'password',
                    'scope' => '*',
                    'username' => $view->items['username'],
                    'password' => $view->items['password'],
                ];
                $ch = curl_init();
                curl_setopt($ch, CURLOPT_URL, c8_api . 'oauth/token');
                curl_setopt($ch, CURLOPT_HEADER, 0);
                curl_setopt($ch, CURLOPT_CAINFO, $cacert);
                curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($parms));
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                $sesC8 = json_decode(curl_exec($ch), true);
                curl_close($ch);

                $authCheck = dbCheckLogin($view->items['username'], $view->items['password'], true);
            } else {
                $authCheck = dbCheckLogin($view->items['username'], $view->items['password'], true);
            }

            // if ($authCheck && $authCheck['attempt_count'] >= 3) $authResult = AUTH_FAIL;
            // elseif ($authCheck) $authResult = AUTH_PASS;
            // else $authResult = AUTH_FAIL;
            // if ($authCheck && $authCheck['attempt_count'] >= 3) $authResult = AUTH_FAIL;
            $authResult = $authCheck ? AUTH_PASS : AUTH_FAIL;

            // -- if their username and password check out - check their license and whether they are already logged in

            if ($authCheck) {
                $licenseEndDays = $authCheck['licenseEndDays'];
                if ($licenseEndDays <= 0) {
                    $authResult = LICENSE_EXPIRED;
                }

                $userID = $authCheck['userID'];

                $databases = mapParameters(dbGetDatabasePermissions($userID), 'databaseID', 'description');

                // if user is not connected to any client accounts
                if (! $databases) {
                    executeCommand('errorAccessRedirect', 'home');
                    exit();

                }

                $loggedCount = dbCheckLogins($userID);
                if ($loggedCount) {
                    $authResult = AUTH_DUPLICATE;
                }

                /** For task # 4175338
                if ($userID && empty ($authCheck['termsAgreed']) && empty ($authCheck['termsVersion']))
                {
                    $ses['user_id'] = $userID;
                    $ses['user_name'] = $authCheck['user_name'];
                    $ses['first_name'] = $authCheck['first_name'];
                    $ses['last_name'] = $authCheck['last_name'];
                    $validationErrors = validatePassword ($view->items['password'], $view->items['newPassword1'], $view->items['newPassword2'], $ses);
                    if ($view->items['termsAgreed'] != 'yes') $validationErrors[] = 'As this is the first time you will be using the system. You need to read and agree cirrus8\'s Terms and Conditions before you can login.';
                    if (noErrors ($validationErrors))
                    {
                        updatePassword ($userID, $view->items['newPassword1']);
                        $view->items['statusMessage'] = 'Your password has now been successfully changed.';
                    }
                    else $authResult = AUTH_FIRST_TIME;
                }
                elseif ($userID && $view->items['termsAgreed'] != 'yes' && empty ($authCheck['termsAgreed']) && empty ($authCheck['termsVersion'])) $authResult = AUTH_FIRST_TIME;
                elseif ($userID && $view->items['termsAgreed'] != 'yes' && (empty ($authCheck['termsAgreed']) || empty ($authCheck['termsVersion']))) $authResult = AUTH_NEW_TERMS;
                 **/
                if ($userID && $view->items['termsAgreed'] != 'yes' && empty($authCheck['termsAgreed']) && empty($authCheck['termsVersion'])) {
                    $authResult = AUTH_FIRST_TIME;
                } elseif ($userID && $view->items['termsAgreed'] != 'yes' && (empty($authCheck['termsAgreed']) || empty($authCheck['termsVersion']))) {
                    $authResult = AUTH_NEW_TERMS;
                }
            }

            if ($authResult == AUTH_PASS || $authResult == AUTH_DUPLICATE) {
                // # GET USER MY_C8 NEEDED DETAILS
                /*
                $ch = curl_init();
                curl_setopt($ch, CURLOPT_URL, c8_api."api/userC8" );
                 curl_setopt($ch, CURLOPT_POST, 1);
                 $request_headers = array();
                 $request_headers[] = 'Accept: application/json';
                 $request_headers[] = 'Authorization: Bearer '. $sesC8['access_token'];
                 curl_setopt($ch, CURLOPT_HTTPHEADER, $request_headers);
                 curl_setopt($ch, CURLOPT_POSTFIELDS,"email=".$view->items['username']);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                $result = curl_exec($ch);
                curl_close($ch);
                $c8Arr = json_decode($result, true);

                ## CHECK IF 2FA Enabled
                if(!isset($view->items['twoFASuccess'])){
                    if(isset($c8Arr['c8Details']['two_fa']) && $c8Arr['c8Details']['two_fa'] != ""){
                        $sess->clear ();
                        $sess->set('twoFaEmail', $view->items['username']);
                        $sess->set('twoFaUserID', $userID);
                        $sess->set('login_with', $view->items['login_with']);
                        $sess->set('sysApi', $sesC8['access_token']);
                        header("Location: ".SYSTEMURL."/index.php?module=home&command=twoFA");
                        exit();
                    }
                }
                */
            }

            switch ($authResult) {
                case AUTH_FIRST_TIME:
                    /** For task # 4175338
                    $view->items['notice'] = '<ul><li>' . implode('</li><li>', $validationErrors) . '</li></ul>';
                     **/
                    $context['notice'] = "As this is the first time you will be using the system. You need to read and agree to cirrus8's Terms and Conditions before you can login.";
                    $context['firstTime'] = 'yes';
                    $context['username'] = $view->items['username'];
                    if (isset($view->items['login_with'])) {
                        $context['login_with'] = $view->items['login_with'];
                    }

                    $context['userID'] = $userID;
                    unset($view->items['action']);
                    unset($_REQUEST['action']);
                    executeCommand('termsOfUse');
                    exit();
                case AUTH_NEW_TERMS:
                    $context['notice'] = "cirrus8's Terms and Conditions have been recently modified. Please check the terms and conditions then click on accept to continue with the login.";
                    $context['modifiedTerms'] = 'yes';
                    $context['username'] = $view->items['username'];
                    if (isset($view->items['login_with'])) {
                        $context['login_with'] = $view->items['login_with'];
                    }

                    $context['userID'] = $userID;
                    unset($view->items['action']);
                    unset($_REQUEST['action']);
                    executeCommand('termsOfUse');
                    exit();
                case AUTH_FAIL:
                    // if (!$authCheck['attempt_count']) $authCheck['attempt_count'] = dbGetAttemptCount ($view->items['username']);
                    // if ($authCheck['attempt_count'] >= 3) $view->items['notice'] = 'Your user account has been locked due to several failed login attempts. Contact <EMAIL> to unlock your account.';
                    // else
                    $view->items['notice'] = 'Login failed - please check your details and try again.';
                    // dbFailedAttemptCount ($view->items['username']);
                    break;
                case LICENSE_EXPIRED:
                    $view->items['notice'] = 'Your licence has expired. Please renew your licence to continue to use the system.';
                    break;
                case AUTH_DUPLICATE:
                    if (isset($view->items['login_with'])) {
                        $context['login_with'] = $view->items['login_with'];
                    }

                    $context['userID'] = $userID;
                    // $context['passwordIDHash'] = $view->items['password'];
                    executeCommand('previousLogin');
                    exit();
                case AUTH_PASS:
                    $sess->clear();
                    $sess->set('registered', true);
                    dbClearCurrentLogin($userID);
                    dbResetUserAttempt($userID);

                    // -- need to register in the database that a user has logged in!
                    $userDetails = dbGetUserDetails($view->items['username'], true);

                    if (! $userDetails) {
                        $userDetails = dbGetUserDetails($view->items['username'], true);
                    }

                    if ($userDetails) {
                        $sess->bindAttributesFrom($userDetails);
                    }

                    $dbList = dbGetDatabaseList();
                    if (! $dbList) {
                        $context['last_error'] = 'Could not fetch the database list';
                        executeCommand('error');
                        exit();
                    }

                    // #############################################################################################
                    // # LOGIN CHECK
                    // # CHECK IF MANDATORY LOGIN
                    if (isset($userDetails['must_login_with']) && $userDetails['must_login_with'] != '') {
                        if (isset($view->items['login_with']) && $view->items['login_with'] == $userDetails['must_login_with']) {
                            $sess->set('login_with', $view->items['login_with']);
                        } else {
                            $login_with = $userDetails['must_login_with'];
                            if ($login_with == 'azure') {
                                $login_with = 'Office 365';
                            }

                            $error_msg = urlencode('Your administrator requires you to sign in with ' . ucwords($login_with));
                            $sess->clear();
                            $context['error_msg'] = $error_msg;
                            header('Location: ' . SYSTEMURL . '/index.php?module=home&command=login&error_msg=' . $error_msg);
                            exit();
                        }
                    }

                    // #############################################################################################

                    // $sess->set ('dbList', $dbList);
                    $sess->set('userID', $userID);


                    $sess->set('currentDB', $dbList[$sess->get('default_db')]['database_name']);
                    $_SESSION['currentDB'] = $dbList[$_SESSION['default_db']]['database_name'];
                    $_SESSION['database'] = $dbList[$sess->get('default_db')]['description'];
                    $_SESSION['development'] = $dbList[$sess->get('default_db')]['development'];
                    //	$_SESSION['pw'] = $view->items['password'];
                    $sess->set('database', $dbList[$sess->get('default_db')]['description']);
                    $sess->set('databaseID', $sess->get('default_db'));
                    // $timezone = dbGetTimezone($dbList[$sess->get ('default_db')]['timezone']);
                    // $sess->set ('clientTimezone', $timezone['timezone_info']);

                    $permissions = mapParameters(dbGetUserPermissions($sess->get('user_id'), true), 'subGroupID', 'subGroupName');
                    $databases = mapParameters(dbGetDatabasePermissions($sess->get('user_id')), 'databaseID', 'description');

                    $sess->set('userGroupList', $permissions);
                    $sess->set('databaseGroupList', $databases);
                    $currentTime = time();
                    $sess->set('_timestamp', $currentTime);

                    // -- register the login and create a key for use to compare sessions later
                    $userID = $userDetails['user_id'];
                    $authKey = md5($userID . session_id() . $_SERVER['HTTP_USER_AGENT'] . $currentTime);
                    dbInsertLoginStats($userID, $_SERVER['HTTP_USER_AGENT'], $_SERVER['REMOTE_ADDR'], $_SERVER['REMOTE_HOST'], session_id(), $authKey, $currentTime);

                    $sess->set('portfolio', dbGetPortfolio($userID, $sess->get('default_db')));

                    // -- legacy fields required by newpms
                    $sess->set('un', $sess->get('user_name'));
                    $sess->set('clientID', $sess->get('default_db'));
                    $sess->set('old_client_id', $sess->get('default_db'));

                    // for country defaults
                    // $countryCode = dbGetDefaultCountry();
                    // $countryCode = CDF_COUNTRY_CODE;
                    // cdf_setCountryDefaultsSession($sess, $countryCode);

                    // for token series
                    $sess->set('syskey', $ses['token']);
                    $sess->set('sysmsgs', $ses['message']);
                    $sess->set('syserrors', $ses['errors']);

                    // api c8-api token
                    $sess->set('sysApi', $sesC8['access_token']);

                    // # CHECK IF USER NEEDS TO CHANGE HIS PASSWORD
                    if ((! isset($view->items['login_with']) || $view->items['login_with'] == '')  && isset($ses['errors']) && count($ses['errors']) > 0 && in_array(1, $ses['errors'])) {
                        $sess->set('req_pwd_ch_user', $view->items['username']);
                        $sess->set('req_pwd_ch_user_type', $userDetails['user_sub_type']);
                        if (isset($view->items['termsAgreed']) && $view->items['termsAgreed'] == 'Y') {
                            $sess->set('update_terms_agree', $view->items['termsAgreed']);
                        }

                        header('Location: ' . SYSTEMURL . '/index.php?module=home&command=changePassword');
                        exit();
                    } else {
                        $ses['message'] = '';
                        $ses['errors'] = [];
                    }

                    $userType = dbGetUserType($userDetails['user_sub_type']);
                    if (! $userType) {
                        $context['last_error'] = 'Could not fetch the users details';
                        executeCommand('error');
                        exit();
                    } else {
                        $sess->bindAttributesFrom($userType);
                    }

                    $sess->bindAttributesFrom($authCheck);

                    if ($userID && $view->items['termsAgreed'] == 'yes' && (empty($authCheck['termsAgreed']) || empty($authCheck['termsVersion']))) {
                        dbAgreeToTerms($userID);
                    }

                    $refresh =  $sess->get('referrer');
                    if (! $refresh) {
                        $refresh = '?module=home&command=home';
                    }

                    if ($userDetails['user_sub_type'] == 'AP_1') {
                        $view->items['notice'] = clientSideRedirect('?module=ap&command=invoice&source=login');
                    } else {
                        $view->items['notice'] = clientSideRedirect('?module=home&command=home&source=login');
                    }

                    break;
            }
        }
    }

    $view->items['last_error'] = $context['last_error'];
    $view->render();
}

<?php

use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Fill;

/**
 * @param  $context  array Mandatory. Referenced array containing data needed to continue.
 *
 * <AUTHOR> Reyes
 *
 * @since 2012-11-16
 *
 * @modified 2012-12-07: Added display on screen option. [Morph]
 **/
function cashBookProcess($context)
{
    global $clientDirectory, $pathPrefix;

    if (! isPostback()) {
        $view = new MasterPage(userViews(), '/accountingReports/cashBookProcess.html');
        $view->setSection($context['module']);
    } else {
        $view = new UserControl(userViews(), '/accountingReports/cashBookProcess.html');
    }

    $view->bindAttributesFrom($_REQUEST);

    $clientCountryCode = $_SESSION['country_code'];

    $_report['reportName'] = 'Cash Book';
    $reportType = $view->items['reportType'];
    if ($reportType == 'bank') {
        $dataID = $view->items['bankID'];
        $bankID = $view->items['bankID'];
        foreach ($context['bankList'] as $v) {
            if ($v['bankID'] == $bankID) {
                $bankName = $v['bankAccountName'];
                break;
            }
        }
    } elseif ($_SESSION['user_type'] != USER_OWNER) {
        $reportType = 'property';
        foreach ($context['propertyGroupedList'] as $v) {
            $propertyID = $v['propertyID'];
            $propertyList[$propertyID]['name'] = $v['propertyName'];
        }

        if ($context['propertyID'] && is_array($context['propertyID'])) {
            $dataID = $context['propertyID'];
            $propertyID = $context['propertyID'];
            $propertyCount = count($context['propertyID']);
        }
    } else {
        $reportType = 'property';
        $propertyID = $context['propertyID'];
        $dataID = $context['propertyID'];
        $propertyList[$propertyID]['name'] = getPropertyName($context['propertyID']);
    }

    $propertyCount = count($context['propertyID']);

    if ($context['leaseID'] && is_array($context['leaseID'])) {
        $leaseID = $context['leaseID'];
    }

    if ($context['supplierID'] && is_array($context['supplierID'])) {
        $supplierID = $context['supplierID'];
    }

    $transactionOption = $view->items['transactionOption'];
    $openBalance['transactionDate'] = 'Opening Balance';
    $propNetChange['transactionDate'] = 'Property Net Change';
    $closedBalance['transactionDate'] = 'Closing Balance';
    $fromDate = $view->items['fromDate'];
    $toDate = $view->items['toDate'];
    $allDates = $view->items['allDates'];
    $pageBreak = $context['pageBreak'] == 'Yes';

    if ($view->items['accountOption'] == 'multipleCodes') {
        $accountID = deserializeParameters($view->items['accountID']);
    } elseif ($view->items['accountOption'] == 'accountRange' && $view->items['accountIDRange1'] && $view->items['accountIDRange2']) {
        $accountID = getValidAccountCodesFromRange($view->items['accountIDRange1'], $view->items['accountIDRange2']);
    }

    $reportResult = dbGetCashBook(
        $reportType,
        $dataID,
        $transactionOption,
        $allDates,
        $accountID,
        $fromDate,
        $toDate,
        $leaseID,
        $supplierID,
        $view->items['sortOption']
    );
    $count = count($reportResult ?? []);

    if ($reportType != 'bank') {
        if ($view->items['showZero'] == 'Yes') {
            foreach (deserializeParameters($view->items['propertyID']) as $propertyID) {
                $properties[$propertyID] = [];
            }
        } elseif ($transactionOption != 'receiptsOnly' && $transactionOption != 'paymentsOnly' && ! $leaseID && ! $supplierID) {
            // if opening or closing != 0 show property and "All Transactions" is selected
            $OBResult = dbGetCashBookOpeningBalance($reportType, $dataID, $accountID, $fromDate);
            foreach ($OBResult as $v) {
                if ($v['amount'] != 0 && $v['propertyID']) {
                    $properties[$v['propertyID']] = [];
                }
            }
        }

        $count += count($properties ?? []);
    } elseif (! $leaseID && ! $supplierID) {
        $properties['bank'] = [];
        $count += 1;
    }

    if ($context[IS_TASK]) {
        $view->bindAttributesFrom($context);
        extract($context);
    } else {
        $view->bindAttributesFrom($context);
        $view->bindAttributesFrom($_REQUEST);
        $queue = new Queue(TASKTYPE_CASH_BOOK);
        if ($propertyCount > THRESHOLD_CASHBOOK) {
            $queue->add(
                $_SESSION['clientID'],
                $_SESSION['un'],
                'command=cashBookProcess&module=accountingReports',
                $_REQUEST
            );
        }
    }

    if ($view->items['individualReport'] == 'lease' && ! $view->items['leaseID'] || $view->items['individualReport'] == 'supplier' && ! $view->items['supplierID']) {
        $view->items['sortOption'] = 'sortAccountID';
    }

    if ($count && ($context[IS_TASK] || $propertyCount <= THRESHOLD_CASHBOOK)) {
        $format = $view->items['format'];

        if (! $context[DOC_MASTER] && $format != FILETYPE_SCREEN) {
            $logoFile = dbGetClientLogo();
            $logoPath = "assets/clientLogos/{$logoFile}";
            $_filePath = "{$pathPrefix}{$clientDirectory}/{$format}/" . DOC_CASHBOOK . '/';
            $_downloadPath = "{$clientDirectory}/{$format}/" . DOC_CASHBOOK;
            $file = DOC_CASHBOOK . '_' . date('Ymd') . ".{$format}";
            $filePath = $_filePath . $file;
            $downloadPath = "{$_downloadPath}/{$file}";
        }

        if ($transactionOption != 'receiptsOnly' && $transactionOption != 'paymentsOnly') {
            $OBResult = dbGetCashBookOpeningBalance($reportType, $dataID, $accountID, $fromDate);
            foreach ($OBResult as $v) {
                $property = ($reportType == 'bank') ? 'bank' : $v['propertyID'];

                $closingBalance[$property]['balance'] = $openingBalance[$property]['balance'] = $v['amount'];
                $closingBalance[$property]['netAmountAll'] = $openingBalance[$property]['netAmountAll'] = $v['net'];
                $closingBalance[$property]['gstAmountAll'] = $openingBalance[$property]['gstAmountAll'] = $v['gst'];
                $closingBalance[$property]['totalAmountAll'] = $openingBalance[$property]['totalAmountAll'] = $v['amount'];
            }
        }

        $accountTotalNet = 0;
        $accountTotalGST = 0;
        $accountTotalAmt = 0;
        $k = 0;
        $incomeTotalNet = 0;
        $incomeTotalGST = 0;
        $incomeTotalAmt = 0;
        $expenseTotalNet = 0;
        $expenseTotalGST = 0;
        $expenseTotalAmt = 0;
        $lastAccountCode = '';
        $lastProperty = '';
        $lastTransType = '';

        if ($view->items['sortOption'] != 'sortAccountID') {
            foreach ($reportResult as $v) {
                $k++;

                $property = ($reportType == 'property') ? $v['propertyID'] : 'bank';
                $properties[$property][$k]['transactionDate'] = $v['transactionDate'];
                $properties[$property][$k]['propertyID'] = $v['propertyID'];
                $properties[$property][$k]['description'] = $v['description'];
                if (fileFormatIsXLS($format)) {
                    $properties[$property][$k]['chequeNumber'] = $v['chequeNumber'];
                    $properties[$property][$k]['createDate'] = $v['createDate'];
                    $properties[$property][$k]['bankDate'] = $v['bankDate'];
                } elseif ($view->items['dateSort'] == '') {
                    $properties[$property][$k]['chequeNumber'] = $v['chequeNumber'];
                } elseif ($view->items['dateSort'] == 'ProcDate') {
                    $properties[$property][$k]['chequeNumber'] = $v['createDate'];
                } elseif ($view->items['dateSort'] == '@Bank') {
                    $properties[$property][$k]['chequeNumber'] = $v['bankDate'];
                }

                $properties[$property][$k]['accountID'] = $v['accountID'];
                $properties[$property][$k]['fromDate'] = $v['fromDate'];
                $properties[$property][$k]['toDate'] = $v['toDate'];
                $properties[$property][$k]['leaseSupplierID'] = $v['leaseSupplierID'];
                $properties[$property][$k]['leaseSupplierName'] = $v['leaseSupplierName'];

                if (fileFormatIsXLS($format) && getDisplayBsbFromSession()) {
                    $properties[$property][$k]['suppBsbBillerCode'] = ! empty($v['suppBPayBillerCode']) ?
                        $v['suppBPayBillerCode'] : formatWithDelimiter($v['suppBSB']);
                }

                if (fileFormatIsXLS($format)) {
                    $properties[$property][$k]['suppAcctNoCrn'] = $v['suppAcctNoCrn'];
                }

                $properties[$property][$k]['unitID'] = $v['unitID'];
                $properties[$property][$k]['unitDescription'] = $v['unitDescription'];
                $properties[$property][$k]['receiptNumber2'] = $v['receiptNumber2'];

                if ($v['transactionType'] == 'receipt') {
                    $properties[$property][$k]['unitCode'] = $v['unitCode'];
                    if (fileFormatIsXLS($format)) {
                        $properties[$property][$k]['netAmountAll'] = $v['netAmount'];
                        $properties[$property][$k]['gstAmountAll'] = $v['gstAmount'];
                        $properties[$property][$k]['totalAmountAll'] = $v['totalAmount'];
                    } else {
                        $properties[$property][$k]['netAmountAll'] = toDecimal($v['netAmount'], 2);
                        $properties[$property][$k]['gstAmountAll'] = toDecimal($v['gstAmount'], 2);
                        $properties[$property][$k]['totalAmountAll'] = toDecimal($v['totalAmount'], 2);
                    }

                    $closingBalance[$property]['netAmountAll'] += $v['netAmount'];
                    $closingBalance[$property]['gstAmountAll'] += $v['gstAmount'];
                    $closingBalance[$property]['totalAmountAll'] += $v['totalAmount'];
                } else {
                    if (fileFormatIsXLS($format)) {
                        $properties[$property][$k]['netAmountAll'] = -$v['netAmount'];
                        $properties[$property][$k]['gstAmountAll'] = -$v['gstAmount'];
                        $properties[$property][$k]['totalAmountAll'] = -$v['totalAmount'];
                    } else {
                        $properties[$property][$k]['netAmountAll'] = toDecimal(-$v['netAmount'], 2);
                        $properties[$property][$k]['gstAmountAll'] = toDecimal(-$v['gstAmount'], 2);
                        $properties[$property][$k]['totalAmountAll'] = toDecimal(-$v['totalAmount'], 2);
                    }

                    $closingBalance[$property]['netAmountAll'] -= $v['netAmount'];
                    $closingBalance[$property]['gstAmountAll'] -= $v['gstAmount'];
                    $closingBalance[$property]['totalAmountAll'] -= $v['totalAmount'];
                }


                if ($v['transactionType'] == 'receipt') {
                    $properties[$property][$k]['leaseID'] = $v['leaseID'];
                    $properties[$property][$k]['leaseName'] = $v['leaseName'];
                    $properties[$property][$k]['supplierID'] = $v['supplierID'];
                    $properties[$property][$k]['supplierName'] = $v['supplierName'];

                    if (fileFormatIsXLS($format)) {
                        $properties[$property][$k]['chequeNumber'] = dbGetChequeNumber(
                            $v['transactionType'],
                            ['debtorID' => $v['debtorID'], 'fromDate' => $fromDate, 'toDate' => $toDate]
                        );
                        $properties[$property][$k]['createDate'] = $v['createDate'];
                        $properties[$property][$k]['bankDate'] = $v['bankDate'];
                    } elseif ($view->items['dateSort'] == '') {
                        $properties[$property][$k]['chequeNumber'] = dbGetChequeNumber(
                            $v['transactionType'],
                            ['debtorID' => $v['debtorID'], 'fromDate' => $fromDate, 'toDate' => $toDate]
                        );
                    } elseif ($view->items['dateSort'] == 'ProcDate') {
                        $properties[$property][$k]['chequeNumber'] = $v['createDate'];
                    } elseif ($view->items['dateSort'] == '@Bank') {
                        $properties[$property][$k]['chequeNumber'] = $v['bankDate'];
                    }

                    $properties[$property][$k]['ReceiptPaymentNumber'] = dbGetChequeNumber(
                        $v['transactionType'],
                        [
                            'debtorID' => $v['debtorID'],
                            'transactionDate' => $v['transactionDate'],
                            'propertyID' => $v['propertyID'],
                            'batchNumber' => $v['paymentsExtraLine'],
                        ]
                    );

                    $properties[$property][$k]['ChequeEFTNumber'] = $v['paymentReference'];
                    //                $properties[$property][$k]['ChequeEFTNumber'] = dbGetChequeEFTNumber($v['transactionType'], array('propertyID' => $v['propertyID'],'debtorID' => $v['debtorID'], 'fromDate' => $fromDate, 'toDate' => $toDate));
                    if ($properties[$property][$k]['ChequeEFTNumber'] == 'DIRECT DEPOSIT') {
                        $properties[$property][$k]['ChequeEFTNumber'] = 'EFT';
                    }


                    $properties[$property][$k]['receiptNumber'] = $v['receiptNumber2'] ? $v['receiptNumber2'] : $v['receiptNumber'];
                    if (fileFormatIsXLS($format)) {
                        $properties[$property][$k]['receiptNetAmount'] = $v['netAmount'];
                        $properties[$property][$k]['receiptGSTAmount'] = $v['gstAmount'];
                        $properties[$property][$k]['receiptTotalAmount'] = $v['totalAmount'];
                    } else {
                        $properties[$property][$k]['receiptNetAmount'] = toDecimal($v['netAmount'], 2);
                        $properties[$property][$k]['receiptGSTAmount'] = toDecimal($v['gstAmount'], 2);
                        $properties[$property][$k]['receiptTotalAmount'] = toDecimal($v['totalAmount'], 2);
                    }

                    $accountTotalNet += $v['netAmount'];
                    $accountTotalGST += $v['gstAmount'];
                    $accountTotalAmt += $v['totalAmount'];

                    $incomeTotalNet += $v['netAmount'];
                    $incomeTotalGST += $v['gstAmount'];
                    $incomeTotalAmt += $v['totalAmount'];

                    $closingBalance[$property]['receiptNetAmount'] += $v['netAmount'];
                    $closingBalance[$property]['receiptGSTAmount'] += $v['gstAmount'];
                    $closingBalance[$property]['receiptTotalAmount'] = bcadd(
                        $closingBalance[$property]['receiptTotalAmount'],
                        $v['totalAmount'],
                        2
                    );
                    if ($properties[$property][$previousKey]['transactionDate']) {
                        $properties[$property][$previousKey]['subTotal'] = ($v['receiptNumber'] != $previousReceipt && $properties[$property][$previousKey] && $reportResult[$previousKey]['receiptNumber']) ? (($format == FILETYPE_XLS) ? $subTotal : toDecimal(
                            $subTotal,
                            2
                        )) : '';
                    }

                    $subTotal = $v['receiptNumber'] == $previousReceipt ? bcadd($subTotal, -$v['totalAmount'], 2) : -$v['totalAmount'];

                    $previousKey = $k;
                    $previousReceipt = $v['receiptNumber'];
                    if ($subTotal && $properties[$property][$previousKey] && $reportResult[$previousKey]['receiptNumber']) {
                        $properties[$property][$previousKey]['subTotal'] = (fileFormatIsXLS(
                            $format
                        )) ? $subTotal : toDecimal(
                            $subTotal,
                            2
                        );
                    }

                    $closingBalance[$property]['balance'] = bcadd(
                        $closingBalance[$property]['balance'],
                        $v['totalAmount'],
                        2
                    );
                    $properties[$property][$k]['balance'] = (fileFormatIsXLS(
                        $format
                    )) ? $closingBalance[$property]['balance'] : toDecimal(
                        $closingBalance[$property]['balance'],
                        2
                    );
                } else {
                    $properties[$property][$k]['supplierID'] = $v['supplierID'];
                    $properties[$property][$k]['supplierName'] = $v['supplierName'];
                    $properties[$property][$k]['receiptNumber'] = $v['receiptNumber'];
                    $properties[$property][$k]['receiptNumber'] = dbGetChequeNumber(
                        $v['transactionType'],
                        ['batchNumber' => $v['paymentsExtraLine'], 'lineNumber' => $v['sequenceNumber']]
                    );
                    $properties[$property][$k]['ReceiptPaymentNumber'] = dbGetChequeNumber(
                        $v['transactionType'],
                        ['batchNumber' => $v['paymentsExtraLine'], 'lineNumber' => $v['sequenceNumber']]
                    );

                    $properties[$property][$k]['ChequeEFTNumber'] = '';

                    $properties[$property][$k]['ChequeEFTNumber'] = dbGetChequeEFTNumber(
                        $v['transactionType'],
                        ['batchNumber' => $v['paymentsExtraLine']]
                    );
                    if ($properties[$property][$k]['ChequeEFTNumber'] == 'X') {
                        $properties[$property][$k]['ChequeEFTNumber'] = 'EFT';
                    }

                    if ($properties[$property][$k]['ChequeEFTNumber'] == 'C') {
                        $properties[$property][$k]['ChequeEFTNumber'] = 'CHQ';
                    }

                    if ($properties[$property][$k]['ChequeEFTNumber'] == 'Y') {
                        $properties[$property][$k]['ChequeEFTNumber'] = 'BPAY';
                    }

                    if (fileFormatIsXLS($format)) {
                        $properties[$property][$k]['paymentsNetAmount'] = -$v['netAmount'];
                        $properties[$property][$k]['paymentsGSTAmount'] = -$v['gstAmount'];
                        $properties[$property][$k]['paymentsTotalAmount'] = -$v['totalAmount'];
                    } else {
                        $properties[$property][$k]['paymentsNetAmount'] = toDecimal(-$v['netAmount'], 2);
                        $properties[$property][$k]['paymentsGSTAmount'] = toDecimal(-$v['gstAmount'], 2);
                        $properties[$property][$k]['paymentsTotalAmount'] = toDecimal(-$v['totalAmount'], 2);
                    }

                    $accountTotalNet += $v['netAmount'];
                    $accountTotalGST += $v['gstAmount'];
                    $accountTotalAmt += $v['totalAmount'];

                    $expenseTotalNet += $v['netAmount'];
                    $expenseTotalGST += $v['gstAmount'];
                    $expenseTotalAmt += $v['totalAmount'];

                    $closingBalance[$property]['paymentsNetAmount'] -= $v['netAmount'];
                    $closingBalance[$property]['paymentsGSTAmount'] -= $v['gstAmount'];
                    $closingBalance[$property]['paymentsTotalAmount'] = bcsub(
                        $closingBalance[$property]['paymentsTotalAmount'],
                        $v['totalAmount'],
                        2
                    );


                    if ($properties[$property][$previousKey]['transactionDate']) {
                        $properties[$property][$previousKey]['subTotal'] = ($v['chequeNumber'] != $previousCheque && $properties[$property][$previousKey] && $reportResult[$previousKey]['chequeNumber']) ? ((fileFormatIsXLS(
                            $format
                        )) ? $subTotal : toDecimal(
                            $subTotal,
                            2
                        )) : '';
                    }

                    $subTotal = $v['chequeNumber'] == $previousCheque ? bcadd($subTotal, -$v['totalAmount'], 2) : -$v['totalAmount'];

                    $previousKey = $k;
                    $previousCheque = $v['chequeNumber'];
                    if ($subTotal && $properties[$property][$previousKey] && $reportResult[$previousKey]['chequeNumber']) {
                        $properties[$property][$previousKey]['subTotal'] = (fileFormatIsXLS(
                            $format
                        )) ? $subTotal : toDecimal(
                            $subTotal,
                            2
                        );
                    }

                    $closingBalance[$property]['balance'] = bcadd(
                        $closingBalance[$property]['balance'],
                        $v['totalAmount'],
                        2
                    );
                    $properties[$property][$k]['balance'] = (fileFormatIsXLS(
                        $format
                    )) ? $closingBalance[$property]['balance'] : toDecimal(
                        $closingBalance[$property]['balance'],
                        2
                    );
                }

                $lastProperty = $property;
                $lastAccountCode = $v['accountID'];
                $lastTransType = $v['transactionType'];
            }

            // display last account code
            if ($lastAccountCode != '' && $view->items['sortOption'] == 'sortAccountID') {
                $k++;
                // display account code total if sorted by account code
                $properties[$lastProperty][$k]['bold'] = false;
                $properties[$lastProperty][$k]['width']['transactionDate'] = 300;
                $properties[$lastProperty][$k]['bgcolor'] = [0.9, 0.9, 0.9];
                //          $properties[$lastProperty][$k]['accountID'] = $lastAccountCode;
                $properties[$lastProperty][$k]['leaseName'] = '';
                $properties[$lastProperty][$k]['supplierName'] = '';
                $properties[$lastProperty][$k]['transactionDate'] = "Account Total for {$lastAccountCode} " . dbGetAccountName(
                    $lastAccountCode
                );

                // used by excel
                $properties[$lastProperty][$k]['headerStyle'] =
                    [
                        'fill' => [
                            'type' => Fill::FILL_SOLID,
                            'color' => ['rgb' => 'e1e1e1'],
                        ],
                        'font' => ['color' => ['rgb' => '000000']],
                        'alignment' => [
                            'vertical' => Alignment::VERTICAL_CENTER,
                            'horizontal' => Alignment::HORIZONTAL_LEFT,
                        ],
                    ];

                if ($lastTransType == 'receipt') {
                    $properties[$lastProperty][$k]['receiptNetAmount'] = fileFormatIsXLS(
                        $format
                    ) ? $accountTotalNet : toDecimal(
                        $accountTotalNet,
                        2
                    );
                    $properties[$lastProperty][$k]['receiptGSTAmount'] = fileFormatIsXLS(
                        $format
                    ) ? $accountTotalGST : toDecimal(
                        $accountTotalGST,
                        2
                    );
                    $properties[$lastProperty][$k]['receiptTotalAmount'] = fileFormatIsXLS(
                        $format
                    ) ? $accountTotalAmt : toDecimal(
                        $accountTotalAmt,
                        2
                    );

                    $properties[$lastProperty][$k]['netAmountAll'] = fileFormatIsXLS(
                        $format
                    ) ? $accountTotalNet : toDecimal(
                        $accountTotalNet,
                        2
                    );
                    $properties[$lastProperty][$k]['gstAmountAll'] = fileFormatIsXLS(
                        $format
                    ) ? $accountTotalGST : toDecimal(
                        $accountTotalGST,
                        2
                    );
                    $properties[$lastProperty][$k]['totalAmountAll'] = fileFormatIsXLS(
                        $format
                    ) ? $accountTotalAmt : toDecimal(
                        $accountTotalAmt,
                        2
                    );
                } else {
                    $properties[$lastProperty][$k]['paymentsNetAmount'] = fileFormatIsXLS(
                        $format
                    ) ? -$accountTotalNet : toDecimal(
                        -$accountTotalNet,
                        2
                    );
                    $properties[$lastProperty][$k]['paymentsGSTAmount'] = fileFormatIsXLS(
                        $format
                    ) ? -$accountTotalGST : toDecimal(
                        -$accountTotalGST,
                        2
                    );
                    $properties[$lastProperty][$k]['paymentsTotalAmount'] = fileFormatIsXLS(
                        $format
                    ) ? -$accountTotalAmt : toDecimal(
                        -$accountTotalAmt,
                        2
                    );

                    $properties[$lastProperty][$k]['netAmountAll'] = fileFormatIsXLS(
                        $format
                    ) ? -$accountTotalNet : toDecimal(
                        -$accountTotalNet,
                        2
                    );
                    $properties[$lastProperty][$k]['gstAmountAll'] = fileFormatIsXLS(
                        $format
                    ) ? -$accountTotalGST : toDecimal(
                        -$accountTotalGST,
                        2
                    );
                    $properties[$lastProperty][$k]['totalAmountAll'] = fileFormatIsXLS(
                        $format
                    ) ? -$accountTotalAmt : toDecimal(
                        -$accountTotalAmt,
                        2
                    );
                }

                // income / expense total
                //            if ($lastTransType != '' AND $lastTransType != $v['transactionType'])
                //            {
                $k++;

                $properties[$lastProperty][$k]['bold'] = true;
                $properties[$lastProperty][$k]['bgcolor'] = [220 / 255, 242 / 255, 255 / 255];

                if ($format != FILETYPE_SCREEN) {
                    $properties[$lastProperty][$k]['leaseName'] =
                    $properties[$lastProperty][$k]['supplierName'] =
                    $properties[$lastProperty][$k]['transactionDate'] = $lastTransType == 'receipt' ? 'Income Total' : 'Expense Total';
                    $properties[$lastProperty][$k]['width']['transactionDate'] = 300;
                } else {
                    $properties[$lastProperty][$k]['transactionDate'] = $lastTransType == 'receipt' ? 'Income Total' : 'Expense Total';
                }

                // used by excel
                $properties[$lastProperty][$k]['headerStyle'] =
                [
                    'fill' => [
                        'type' => Fill::FILL_SOLID,
                        'color' => ['rgb' => 'dcf2ff'],
                    ],
                    'font' => ['bold' => true, 'color' => ['rgb' => '000000']],
                    'alignment' => [
                        'vertical' => Alignment::VERTICAL_CENTER,
                        'horizontal' => Alignment::HORIZONTAL_LEFT,
                    ],
                ];


                if ($lastTransType == 'receipt') {
                    $properties[$lastProperty][$k]['receiptNetAmount'] = fileFormatIsXLS(
                        $format
                    ) ? $incomeTotalNet : toDecimal(
                        $incomeTotalNet,
                        2
                    );
                    $properties[$lastProperty][$k]['receiptGSTAmount'] = fileFormatIsXLS(
                        $format
                    ) ? $incomeTotalGST : toDecimal(
                        $incomeTotalGST,
                        2
                    );
                    $properties[$lastProperty][$k]['receiptTotalAmount'] = fileFormatIsXLS(
                        $format
                    ) ? $incomeTotalAmt : toDecimal(
                        $incomeTotalAmt,
                        2
                    );

                    $properties[$lastProperty][$k]['netAmountAll'] = fileFormatIsXLS(
                        $format
                    ) ? $incomeTotalNet : toDecimal(
                        $incomeTotalNet,
                        2
                    );
                    $properties[$lastProperty][$k]['gstAmountAll'] = fileFormatIsXLS(
                        $format
                    ) ? $incomeTotalGST : toDecimal(
                        $incomeTotalGST,
                        2
                    );
                    $properties[$lastProperty][$k]['totalAmountAll'] = fileFormatIsXLS(
                        $format
                    ) ? $incomeTotalAmt : toDecimal(
                        $incomeTotalAmt,
                        2
                    );
                } else {
                    $properties[$lastProperty][$k]['paymentsNetAmount'] = fileFormatIsXLS(
                        $format
                    ) ? -$expenseTotalNet : toDecimal(
                        -$expenseTotalNet,
                        2
                    );
                    $properties[$lastProperty][$k]['paymentsGSTAmount'] = fileFormatIsXLS(
                        $format
                    ) ? -$expenseTotalGST : toDecimal(
                        -$expenseTotalGST,
                        2
                    );
                    $properties[$lastProperty][$k]['paymentsTotalAmount'] = fileFormatIsXLS(
                        $format
                    ) ? -$expenseTotalAmt : toDecimal(
                        -$expenseTotalAmt,
                        2
                    );

                    $properties[$lastProperty][$k]['netAmountAll'] = fileFormatIsXLS(
                        $format
                    ) ? -$expenseTotalNet : toDecimal(
                        -$expenseTotalNet,
                        2
                    );
                    $properties[$lastProperty][$k]['gstAmountAll'] = fileFormatIsXLS(
                        $format
                    ) ? -$expenseTotalGST : toDecimal(
                        -$expenseTotalGST,
                        2
                    );
                    $properties[$lastProperty][$k]['totalAmountAll'] = fileFormatIsXLS(
                        $format
                    ) ? -$expenseTotalAmt : toDecimal(
                        -$expenseTotalAmt,
                        2
                    );
                }

                //            }
            }
        } elseif ($view->items['sortOption'] == 'sortAccountID') {
            foreach ($reportResult as $v) {
                $propOrBank = ($reportType == 'property') ? $v['propertyID'] : 'bank';
                if ($lastAccountCode != '' && $lastAccountCode != $v['accountID'] && $view->items['sortOption'] == 'sortAccountID' || $lastProperty != $propOrBank && $lastProperty != '') {
                    $k++;
                    // display account code total if sorted by account code
                    $properties[$lastProperty][$k]['bold'] = false;
                    $properties[$lastProperty][$k]['width']['transactionDate'] = 300;
                    $properties[$lastProperty][$k]['bgcolor'] = [0.9, 0.9, 0.9];
                    //                $properties[$lastProperty][$k]['accountID'] = $lastAccountCode;
                    $properties[$lastProperty][$k]['leaseName'] = '';
                    $properties[$lastProperty][$k]['supplierName'] = '';
                    $properties[$lastProperty][$k]['transactionDate'] = "Account Total for {$lastAccountCode} " . dbGetAccountName(
                        $lastAccountCode
                    );

                    // used by excel
                    $properties[$lastProperty][$k]['headerStyle'] =
                    [
                        'fill' => [
                            'type' => Fill::FILL_SOLID,
                            'color' => ['rgb' => 'e1e1e1'],
                        ],
                        'font' => ['color' => ['rgb' => '000000']],
                        'alignment' => [
                            'vertical' => Alignment::VERTICAL_CENTER,
                            'horizontal' => Alignment::HORIZONTAL_LEFT,
                        ],
                    ];

                    if ($lastTransType == 'receipt') {
                        $properties[$lastProperty][$k]['receiptNetAmount'] = fileFormatIsXLS(
                            $format
                        ) ? $accountTotalNet : toDecimal(
                            $accountTotalNet,
                            2
                        );
                        $properties[$lastProperty][$k]['receiptGSTAmount'] = fileFormatIsXLS(
                            $format
                        ) ? $accountTotalGST : toDecimal(
                            $accountTotalGST,
                            2
                        );
                        $properties[$lastProperty][$k]['receiptTotalAmount'] = fileFormatIsXLS(
                            $format
                        ) ? $accountTotalAmt : toDecimal(
                            $accountTotalAmt,
                            2
                        );

                        $properties[$lastProperty][$k]['netAmountAll'] = fileFormatIsXLS(
                            $format
                        ) ? $accountTotalNet : toDecimal(
                            $accountTotalNet,
                            2
                        );
                        $properties[$lastProperty][$k]['gstAmountAll'] = fileFormatIsXLS(
                            $format
                        ) ? $accountTotalGST : toDecimal(
                            $accountTotalGST,
                            2
                        );
                        $properties[$lastProperty][$k]['totalAmountAll'] = fileFormatIsXLS(
                            $format
                        ) ? $accountTotalAmt : toDecimal(
                            $accountTotalAmt,
                            2
                        );
                    } else {
                        $properties[$lastProperty][$k]['paymentsNetAmount'] = fileFormatIsXLS(
                            $format
                        ) ? -$accountTotalNet : toDecimal(
                            -$accountTotalNet,
                            2
                        );
                        $properties[$lastProperty][$k]['paymentsGSTAmount'] = fileFormatIsXLS(
                            $format
                        ) ? -$accountTotalGST : toDecimal(
                            -$accountTotalGST,
                            2
                        );
                        $properties[$lastProperty][$k]['paymentsTotalAmount'] = fileFormatIsXLS(
                            $format
                        ) ? -$accountTotalAmt : toDecimal(
                            -$accountTotalAmt,
                            2
                        );

                        $properties[$lastProperty][$k]['netAmountAll'] = fileFormatIsXLS(
                            $format
                        ) ? -$accountTotalNet : toDecimal(
                            -$accountTotalNet,
                            2
                        );
                        $properties[$lastProperty][$k]['gstAmountAll'] = fileFormatIsXLS(
                            $format
                        ) ? -$accountTotalGST : toDecimal(
                            -$accountTotalGST,
                            2
                        );
                        $properties[$lastProperty][$k]['totalAmountAll'] = fileFormatIsXLS(
                            $format
                        ) ? -$accountTotalAmt : toDecimal(
                            -$accountTotalAmt,
                            2
                        );
                    }

                    // income / expense total
                    if ($lastTransType != '' && $lastTransType != $v['transactionType'] || $lastProperty != $propOrBank && $lastProperty != '') {
                        $k++;

                        $properties[$lastProperty][$k]['bold'] = true;
                        $properties[$lastProperty][$k]['bgcolor'] = [220 / 255, 242 / 255, 255 / 255];

                        if ($format != FILETYPE_SCREEN) {
                            $properties[$lastProperty][$k]['leaseName'] = '';
                            $properties[$lastProperty][$k]['supplierName'] = '';
                            $properties[$lastProperty][$k]['transactionDate'] = $lastTransType == 'receipt' ? 'Income Total' : 'Expense Total';
                            $properties[$lastProperty][$k]['width']['transactionDate'] = 300;
                        } else {
                            $properties[$lastProperty][$k]['transactionDate'] = $lastTransType == 'receipt' ? 'Income Total' : 'Expense Total';
                        }

                        // used by excel
                        $properties[$lastProperty][$k]['headerStyle'] =
                        [
                            'fill' => [
                                'type' => Fill::FILL_SOLID,
                                'color' => ['rgb' => 'dcf2ff'],
                            ],
                            'font' => ['bold' => true, 'color' => ['rgb' => '000000']],
                            'alignment' => [
                                'vertical' => Alignment::VERTICAL_CENTER,
                                'horizontal' => Alignment::HORIZONTAL_LEFT,
                            ],
                        ];


                        if ($lastTransType == 'receipt') {
                            $properties[$lastProperty][$k]['receiptNetAmount'] = fileFormatIsXLS(
                                $format
                            ) ? $incomeTotalNet : toDecimal(
                                $incomeTotalNet,
                                2
                            );
                            $properties[$lastProperty][$k]['receiptGSTAmount'] = fileFormatIsXLS(
                                $format
                            ) ? $incomeTotalGST : toDecimal(
                                $incomeTotalGST,
                                2
                            );
                            $properties[$lastProperty][$k]['receiptTotalAmount'] = fileFormatIsXLS(
                                $format
                            ) ? $incomeTotalAmt : toDecimal(
                                $incomeTotalAmt,
                                2
                            );

                            $properties[$lastProperty][$k]['netAmountAll'] = fileFormatIsXLS(
                                $format
                            ) ? $incomeTotalNet : toDecimal(
                                $incomeTotalNet,
                                2
                            );
                            $properties[$lastProperty][$k]['gstAmountAll'] = fileFormatIsXLS(
                                $format
                            ) ? $incomeTotalGST : toDecimal(
                                $incomeTotalGST,
                                2
                            );
                            $properties[$lastProperty][$k]['totalAmountAll'] = fileFormatIsXLS(
                                $format
                            ) ? $incomeTotalAmt : toDecimal(
                                $incomeTotalAmt,
                                2
                            );
                        } else {
                            $properties[$lastProperty][$k]['paymentsNetAmount'] = fileFormatIsXLS(
                                $format
                            ) ? -$expenseTotalNet : toDecimal(
                                -$expenseTotalNet,
                                2
                            );
                            $properties[$lastProperty][$k]['paymentsGSTAmount'] = fileFormatIsXLS(
                                $format
                            ) ? -$expenseTotalGST : toDecimal(
                                -$expenseTotalGST,
                                2
                            );
                            $properties[$lastProperty][$k]['paymentsTotalAmount'] = fileFormatIsXLS(
                                $format
                            ) ? -$expenseTotalAmt : toDecimal(
                                -$expenseTotalAmt,
                                2
                            );

                            $properties[$lastProperty][$k]['netAmountAll'] = fileFormatIsXLS(
                                $format
                            ) ? -$expenseTotalNet : toDecimal(
                                -$expenseTotalNet,
                                2
                            );
                            $properties[$lastProperty][$k]['gstAmountAll'] = fileFormatIsXLS(
                                $format
                            ) ? -$expenseTotalGST : toDecimal(
                                -$expenseTotalGST,
                                2
                            );
                            $properties[$lastProperty][$k]['totalAmountAll'] = fileFormatIsXLS(
                                $format
                            ) ? -$expenseTotalAmt : toDecimal(
                                -$expenseTotalAmt,
                                2
                            );
                        }
                    }

                    $k++;
                    $properties[$lastProperty][$k]['leaseName'] =
                    $properties[$lastProperty][$k]['supplierName'] =
                    $properties[$lastProperty][$k]['transactionDate'] = $format == FILETYPE_SCREEN ? '&nbsp' : '';
                    $accountTotalNet = 0;
                    $accountTotalGST = 0;
                    $accountTotalAmt = 0;
                    if ($lastProperty != $propOrBank && $lastProperty != '') {
                        $incomeTotalNet = 0;
                        $incomeTotalGST = 0;
                        $incomeTotalAmt = 0;
                        $expenseTotalNet = 0;
                        $expenseTotalGST = 0;
                        $expenseTotalAmt = 0;
                    }
                }

                $k++;
                $property = ($reportType == 'property') ? $v['propertyID'] : 'bank';
                $properties[$property][$k]['transactionDate'] = $v['transactionDate'];
                $properties[$property][$k]['propertyID'] = $v['propertyID'];
                $properties[$property][$k]['description'] = $v['description'];
                $properties[$property][$k]['chequeNumber'] = $v['chequeNumber'];
                $properties[$property][$k]['accountID'] = $v['accountID'];
                $properties[$property][$k]['fromDate'] = $v['fromDate'];
                $properties[$property][$k]['toDate'] = $v['toDate'];
                $properties[$property][$k]['leaseSupplierID'] = $v['leaseSupplierID'];
                $properties[$property][$k]['leaseSupplierName'] = $v['leaseSupplierName'];

                if ($v['transactionType'] == 'receipt') {
                    $properties[$property][$k]['unitCode'] = $v['unitCode'];
                    if (fileFormatIsXLS($format)) {
                        $properties[$property][$k]['netAmountAll'] = $v['netAmount'];
                        $properties[$property][$k]['gstAmountAll'] = $v['gstAmount'];
                        $properties[$property][$k]['totalAmountAll'] = $v['totalAmount'];
                    } else {
                        $properties[$property][$k]['netAmountAll'] = toDecimal($v['netAmount'], 2);
                        $properties[$property][$k]['gstAmountAll'] = toDecimal($v['gstAmount'], 2);
                        $properties[$property][$k]['totalAmountAll'] = toDecimal($v['totalAmount'], 2);
                    }

                    $closingBalance[$property]['netAmountAll'] += $v['netAmount'];
                    $closingBalance[$property]['gstAmountAll'] += $v['gstAmount'];
                    $closingBalance[$property]['totalAmountAll'] += $v['totalAmount'];
                } else {
                    if (fileFormatIsXLS($format)) {
                        $properties[$property][$k]['netAmountAll'] = -$v['netAmount'];
                        $properties[$property][$k]['gstAmountAll'] = -$v['gstAmount'];
                        $properties[$property][$k]['totalAmountAll'] = -$v['totalAmount'];
                    } else {
                        $properties[$property][$k]['netAmountAll'] = toDecimal(-$v['netAmount'], 2);
                        $properties[$property][$k]['gstAmountAll'] = toDecimal(-$v['gstAmount'], 2);
                        $properties[$property][$k]['totalAmountAll'] = toDecimal(-$v['totalAmount'], 2);
                    }

                    $closingBalance[$property]['netAmountAll'] -= $v['netAmount'];
                    $closingBalance[$property]['gstAmountAll'] -= $v['gstAmount'];
                    $closingBalance[$property]['totalAmountAll'] -= $v['totalAmount'];
                }

                if ($v['transactionType'] == 'receipt') {
                    $properties[$property][$k]['leaseID'] = $v['leaseID'];
                    $properties[$property][$k]['leaseName'] = $v['leaseName'];
                    $properties[$property][$k]['supplierID'] = $v['supplierID'];
                    $properties[$property][$k]['supplierName'] = $v['supplierName'];

                    if (fileFormatIsXLS($format) && getDisplayBsbFromSession()) {
                        $properties[$property][$k]['suppBsbBillerCode'] = ! empty($v['suppBPayBillerCode']) ?
                            $v['suppBPayBillerCode'] : formatWithDelimiter($v['suppBSB']);
                    }

                    if (fileFormatIsXLS($format)) {
                        $properties[$property][$k]['suppAcctNoCrn'] = $v['suppAcctNoCrn'];
                    }

                    $properties[$property][$k]['chequeNumber'] = dbGetChequeNumber(
                        $v['transactionType'],
                        ['debtorID' => $v['debtorID'], 'fromDate' => $fromDate, 'toDate' => $toDate]
                    );
                    $properties[$property][$k]['ReceiptPaymentNumber'] = dbGetChequeNumber(
                        $v['transactionType'],
                        [
                            'debtorID' => $v['debtorID'],
                            'transactionDate' => $v['transactionDate'],
                            'propertyID' => $v['propertyID'],
                            'batchNumber' => $v['paymentsExtraLine'],
                        ]
                    );

                    $properties[$property][$k]['ChequeEFTNumber'] = $v['paymentReference'];
                    if ($properties[$property][$k]['ChequeEFTNumber'] == 'DIRECT DEPOSIT') {
                        $properties[$property][$k]['ChequeEFTNumber'] = 'EFT';
                    }


                    $properties[$property][$k]['receiptNumber'] = $v['receiptNumber'];
                    if (fileFormatIsXLS($format)) {
                        $properties[$property][$k]['receiptNetAmount'] = $v['netAmount'];
                        $properties[$property][$k]['receiptGSTAmount'] = $v['gstAmount'];
                        $properties[$property][$k]['receiptTotalAmount'] = $v['totalAmount'];
                    } else {
                        $properties[$property][$k]['receiptNetAmount'] = toDecimal($v['netAmount'], 2);
                        $properties[$property][$k]['receiptGSTAmount'] = toDecimal($v['gstAmount'], 2);
                        $properties[$property][$k]['receiptTotalAmount'] = toDecimal($v['totalAmount'], 2);
                    }

                    $accountTotalNet += $v['netAmount'];
                    $accountTotalGST += $v['gstAmount'];
                    $accountTotalAmt += $v['totalAmount'];

                    $incomeTotalNet += $v['netAmount'];
                    $incomeTotalGST += $v['gstAmount'];
                    $incomeTotalAmt += $v['totalAmount'];

                    $closingBalance[$property]['receiptNetAmount'] += $v['netAmount'];
                    $closingBalance[$property]['receiptGSTAmount'] += $v['gstAmount'];
                    $closingBalance[$property]['receiptTotalAmount'] = bcadd(
                        $closingBalance[$property]['receiptTotalAmount'],
                        $v['totalAmount'],
                        2
                    );
                    if ($properties[$property][$previousKey]['transactionDate']) {
                        $properties[$property][$previousKey]['subTotal'] = ($v['receiptNumber'] != $previousReceipt && $properties[$property][$previousKey] && $reportResult[$previousKey]['receiptNumber']) ? ((fileFormatIsXLS(
                            $format
                        )) ? $subTotal : toDecimal(
                            $subTotal,
                            2
                        )) : '';
                    }

                    $subTotal = $v['receiptNumber'] == $previousReceipt ? bcadd($subTotal, -$v['totalAmount'], 2) : -$v['totalAmount'];

                    $previousKey = $k;
                    $previousReceipt = $v['receiptNumber'];
                    if ($subTotal && $properties[$property][$previousKey] && $reportResult[$previousKey]['receiptNumber']) {
                        $properties[$property][$previousKey]['subTotal'] = (fileFormatIsXLS(
                            $format
                        )) ? $subTotal : toDecimal(
                            $subTotal,
                            2
                        );
                    }

                    $closingBalance[$property]['balance'] = bcadd(
                        $closingBalance[$property]['balance'],
                        $v['totalAmount'],
                        2
                    );
                    $properties[$property][$k]['balance'] = (fileFormatIsXLS(
                        $format
                    )) ? $closingBalance[$property]['balance'] : toDecimal(
                        $closingBalance[$property]['balance'],
                        2
                    );
                } else {
                    $properties[$property][$k]['supplierID'] = $v['supplierID'];
                    $properties[$property][$k]['supplierName'] = $v['supplierName'];

                    if (fileFormatIsXLS($format) && getDisplayBsbFromSession()) {
                        $properties[$property][$k]['suppBsbBillerCode'] = ! empty($v['suppBPayBillerCode']) ?
                            $v['suppBPayBillerCode'] : formatWithDelimiter($v['suppBSB']);
                    }

                    if (fileFormatIsXLS($format)) {
                        $properties[$property][$k]['suppAcctNoCrn'] = $v['suppAcctNoCrn'];
                    }

                    $properties[$property][$k]['receiptNumber'] = $v['receiptNumber'];
                    $properties[$property][$k]['receiptNumber'] = dbGetChequeNumber(
                        $v['transactionType'],
                        ['batchNumber' => $v['paymentsExtraLine'], 'lineNumber' => $v['sequenceNumber']]
                    );
                    $properties[$property][$k]['ReceiptPaymentNumber'] = dbGetChequeNumber(
                        $v['transactionType'],
                        ['batchNumber' => $v['paymentsExtraLine'], 'lineNumber' => $v['sequenceNumber']]
                    );

                    $properties[$property][$k]['ChequeEFTNumber'] = '';

                    $properties[$property][$k]['ChequeEFTNumber'] = dbGetChequeEFTNumber(
                        $v['transactionType'],
                        ['batchNumber' => $v['paymentsExtraLine']]
                    );
                    if ($properties[$property][$k]['ChequeEFTNumber'] == 'X') {
                        $properties[$property][$k]['ChequeEFTNumber'] = 'EFT';
                    }

                    if ($properties[$property][$k]['ChequeEFTNumber'] == 'C') {
                        $properties[$property][$k]['ChequeEFTNumber'] = 'CHQ';
                    }

                    if ($properties[$property][$k]['ChequeEFTNumber'] == 'Y') {
                        $properties[$property][$k]['ChequeEFTNumber'] = 'BPAY';
                    }

                    if (fileFormatIsXLS($format)) {
                        $properties[$property][$k]['paymentsNetAmount'] = -$v['netAmount'];
                        $properties[$property][$k]['paymentsGSTAmount'] = -$v['gstAmount'];
                        $properties[$property][$k]['paymentsTotalAmount'] = -$v['totalAmount'];
                    } else {
                        $properties[$property][$k]['paymentsNetAmount'] = toDecimal(-$v['netAmount'], 2);
                        $properties[$property][$k]['paymentsGSTAmount'] = toDecimal(-$v['gstAmount'], 2);
                        $properties[$property][$k]['paymentsTotalAmount'] = toDecimal(-$v['totalAmount'], 2);
                    }

                    $accountTotalNet += $v['netAmount'];
                    $accountTotalGST += $v['gstAmount'];
                    $accountTotalAmt += $v['totalAmount'];

                    $expenseTotalNet += $v['netAmount'];
                    $expenseTotalGST += $v['gstAmount'];
                    $expenseTotalAmt += $v['totalAmount'];

                    $closingBalance[$property]['paymentsNetAmount'] -= $v['netAmount'];
                    $closingBalance[$property]['paymentsGSTAmount'] -= $v['gstAmount'];
                    $closingBalance[$property]['paymentsTotalAmount'] = bcsub(
                        $closingBalance[$property]['paymentsTotalAmount'],
                        $v['totalAmount'],
                        2
                    );


                    if ($properties[$property][$previousKey]['transactionDate']) {
                        $properties[$property][$previousKey]['subTotal'] = ($v['chequeNumber'] != $previousCheque && $properties[$property][$previousKey] && $reportResult[$previousKey]['chequeNumber']) ? ((fileFormatIsXLS(
                            $format
                        )) ? $subTotal : toDecimal(
                            $subTotal,
                            2
                        )) : '';
                    }

                    $subTotal = $v['chequeNumber'] == $previousCheque ? bcadd($subTotal, -$v['totalAmount'], 2) : -$v['totalAmount'];

                    $previousKey = $k;
                    $previousCheque = $v['chequeNumber'];
                    if ($subTotal && $properties[$property][$previousKey] && $reportResult[$previousKey]['chequeNumber']) {
                        $properties[$property][$previousKey]['subTotal'] = (fileFormatIsXLS(
                            $format
                        )) ? $subTotal : toDecimal(
                            $subTotal,
                            2
                        );
                    }

                    $closingBalance[$property]['balance'] = bcadd(
                        $closingBalance[$property]['balance'],
                        $v['totalAmount'],
                        2
                    );
                    $properties[$property][$k]['balance'] = (fileFormatIsXLS(
                        $format
                    )) ? $closingBalance[$property]['balance'] : toDecimal(
                        $closingBalance[$property]['balance'],
                        2
                    );
                }

                $lastProperty = $property;
                $lastAccountCode = $v['accountID'];
                $lastTransType = $v['transactionType'];
            }

            if ($lastAccountCode) {
                $k++;
                // display account code total if sorted by account code
                $properties[$lastProperty][$k]['bold'] = false;
                $properties[$lastProperty][$k]['width']['transactionDate'] = 300;
                $properties[$lastProperty][$k]['bgcolor'] = [0.9, 0.9, 0.9];
                //                $properties[$lastProperty][$k]['accountID'] = $lastAccountCode;
                $properties[$lastProperty][$k]['leaseName'] = '';
                $properties[$lastProperty][$k]['supplierName'] = '';
                $properties[$lastProperty][$k]['transactionDate'] = "Account Total for {$lastAccountCode} " . dbGetAccountName(
                    $lastAccountCode
                );
            }

            if ($lastProperty) {
                // used by excel
                $properties[$lastProperty][$k]['headerStyle'] =
                [
                    'fill' => [
                        'type' => Fill::FILL_SOLID,
                        'color' => ['rgb' => 'e1e1e1'],
                    ],
                    'font' => ['color' => ['rgb' => '000000']],
                    'alignment' => [
                        'vertical' => Alignment::VERTICAL_CENTER,
                        'horizontal' => Alignment::HORIZONTAL_LEFT,
                    ],
                ];

                if ($lastTransType == 'receipt') {
                    $properties[$lastProperty][$k]['receiptNetAmount'] = fileFormatIsXLS(
                        $format
                    ) ? $accountTotalNet : toDecimal(
                        $accountTotalNet,
                        2
                    );
                    $properties[$lastProperty][$k]['receiptGSTAmount'] = fileFormatIsXLS(
                        $format
                    ) ? $accountTotalGST : toDecimal(
                        $accountTotalGST,
                        2
                    );
                    $properties[$lastProperty][$k]['receiptTotalAmount'] = fileFormatIsXLS(
                        $format
                    ) ? $accountTotalAmt : toDecimal(
                        $accountTotalAmt,
                        2
                    );

                    $properties[$lastProperty][$k]['netAmountAll'] = fileFormatIsXLS(
                        $format
                    ) ? $accountTotalNet : toDecimal(
                        $accountTotalNet,
                        2
                    );
                    $properties[$lastProperty][$k]['gstAmountAll'] = fileFormatIsXLS(
                        $format
                    ) ? $accountTotalGST : toDecimal(
                        $accountTotalGST,
                        2
                    );
                    $properties[$lastProperty][$k]['totalAmountAll'] = fileFormatIsXLS(
                        $format
                    ) ? $accountTotalAmt : toDecimal(
                        $accountTotalAmt,
                        2
                    );
                } else {
                    $properties[$lastProperty][$k]['paymentsNetAmount'] = fileFormatIsXLS(
                        $format
                    ) ? -$accountTotalNet : toDecimal(
                        -$accountTotalNet,
                        2
                    );
                    $properties[$lastProperty][$k]['paymentsGSTAmount'] = fileFormatIsXLS(
                        $format
                    ) ? -$accountTotalGST : toDecimal(
                        -$accountTotalGST,
                        2
                    );
                    $properties[$lastProperty][$k]['paymentsTotalAmount'] = fileFormatIsXLS(
                        $format
                    ) ? -$accountTotalAmt : toDecimal(
                        -$accountTotalAmt,
                        2
                    );

                    $properties[$lastProperty][$k]['netAmountAll'] = fileFormatIsXLS(
                        $format
                    ) ? -$accountTotalNet : toDecimal(
                        -$accountTotalNet,
                        2
                    );
                    $properties[$lastProperty][$k]['gstAmountAll'] = fileFormatIsXLS(
                        $format
                    ) ? -$accountTotalGST : toDecimal(
                        -$accountTotalGST,
                        2
                    );
                    $properties[$lastProperty][$k]['totalAmountAll'] = fileFormatIsXLS(
                        $format
                    ) ? -$accountTotalAmt : toDecimal(
                        -$accountTotalAmt,
                        2
                    );
                }

                // income / expense total
                //                if ($lastTransType != '' AND $lastTransType != $v['transactionType'] OR ($lastProperty != $v['propertyID'] AND $lastProperty != ''))
                //                {
                $k++;

                $properties[$lastProperty][$k]['bold'] = true;
                $properties[$lastProperty][$k]['bgcolor'] = [220 / 255, 242 / 255, 255 / 255];

                if ($format != FILETYPE_SCREEN) {
                    $properties[$lastProperty][$k]['leaseName'] = '';
                    $properties[$lastProperty][$k]['supplierName'] = '';
                    $properties[$lastProperty][$k]['suppBsbBillerCode'] = '';
                    $properties[$lastProperty][$k]['suppAcctNoCrn'] = '';
                    $properties[$lastProperty][$k]['transactionDate'] = $lastTransType == 'receipt' ? 'Income Total' : 'Expense Total';
                    $properties[$lastProperty][$k]['width']['transactionDate'] = 300;
                } else {
                    $properties[$lastProperty][$k]['transactionDate'] = $lastTransType == 'receipt' ? 'Income Total' : 'Expense Total';
                }

                // used by excel
                $properties[$lastProperty][$k]['headerStyle'] =
                [
                    'fill' => [
                        'type' => Fill::FILL_SOLID,
                        'color' => ['rgb' => 'dcf2ff'],
                    ],
                    'font' => ['bold' => true, 'color' => ['rgb' => '000000']],
                    'alignment' => [
                        'vertical' => Alignment::VERTICAL_CENTER,
                        'horizontal' => Alignment::HORIZONTAL_LEFT,
                    ],
                ];


                if ($lastTransType == 'receipt') {
                    $properties[$lastProperty][$k]['receiptNetAmount'] = fileFormatIsXLS(
                        $format
                    ) ? $incomeTotalNet : toDecimal(
                        $incomeTotalNet,
                        2
                    );
                    $properties[$lastProperty][$k]['receiptGSTAmount'] = fileFormatIsXLS(
                        $format
                    ) ? $incomeTotalGST : toDecimal(
                        $incomeTotalGST,
                        2
                    );
                    $properties[$lastProperty][$k]['receiptTotalAmount'] = fileFormatIsXLS(
                        $format
                    ) ? $incomeTotalAmt : toDecimal(
                        $incomeTotalAmt,
                        2
                    );

                    $properties[$lastProperty][$k]['netAmountAll'] = fileFormatIsXLS(
                        $format
                    ) ? $incomeTotalNet : toDecimal(
                        $incomeTotalNet,
                        2
                    );
                    $properties[$lastProperty][$k]['gstAmountAll'] = fileFormatIsXLS(
                        $format
                    ) ? $incomeTotalGST : toDecimal(
                        $incomeTotalGST,
                        2
                    );
                    $properties[$lastProperty][$k]['totalAmountAll'] = fileFormatIsXLS(
                        $format
                    ) ? $incomeTotalAmt : toDecimal(
                        $incomeTotalAmt,
                        2
                    );
                } else {
                    $properties[$lastProperty][$k]['paymentsNetAmount'] = fileFormatIsXLS(
                        $format
                    ) ? -$expenseTotalNet : toDecimal(
                        -$expenseTotalNet,
                        2
                    );
                    $properties[$lastProperty][$k]['paymentsGSTAmount'] = fileFormatIsXLS(
                        $format
                    ) ? -$expenseTotalGST : toDecimal(
                        -$expenseTotalGST,
                        2
                    );
                    $properties[$lastProperty][$k]['paymentsTotalAmount'] = fileFormatIsXLS(
                        $format
                    ) ? -$expenseTotalAmt : toDecimal(
                        -$expenseTotalAmt,
                        2
                    );

                    $properties[$lastProperty][$k]['netAmountAll'] = fileFormatIsXLS(
                        $format
                    ) ? -$expenseTotalNet : toDecimal(
                        -$expenseTotalNet,
                        2
                    );
                    $properties[$lastProperty][$k]['gstAmountAll'] = fileFormatIsXLS(
                        $format
                    ) ? -$expenseTotalGST : toDecimal(
                        -$expenseTotalGST,
                        2
                    );
                    $properties[$lastProperty][$k]['totalAmountAll'] = fileFormatIsXLS(
                        $format
                    ) ? -$expenseTotalAmt : toDecimal(
                        -$expenseTotalAmt,
                        2
                    );
                }
            }
        }

        if (fileFormatIsXLS($format)) {
            if ($properties[$property][$previousKey] && $reportResult[$previousKey]['receiptNumber']) {
                $properties[$property][$previousKey]['subTotal'] = $subTotal;
            }

            if ($properties[$property][$previousKey] && $reportResult[$previousKey]['chequeNumber']) {
                $properties[$property][$previousKey]['subTotal'] = $subTotal;
            }
        } else {
            if ($properties[$property][$previousKey] && $reportResult[$previousKey]['receiptNumber']) {
                $properties[$property][$previousKey]['subTotal'] = toDecimal($subTotal, 2);
            }

            if ($properties[$property][$previousKey] && $reportResult[$previousKey]['chequeNumber']) {
                $properties[$property][$previousKey]['subTotal'] = toDecimal($subTotal, 2);
            }
        }

        switch ($format) {
            case FILETYPE_PDF:
                $report = ($context[DOC_MASTER]) ? new PDFDataReport(
                    $context[DOC_MASTER],
                    $logoPath,
                    A4_LANDSCAPE
                ) : new PDFDataReport($filePath, $logoPath, A4_LANDSCAPE);
                $report->multiLine = true;
                $report->printRowLines = true;
                $report->printColumnLines = false;
                $report->printBorders = false;
                $report->cache = false;
                $header = new ReportHeader('Cash Book Report');
                $header->subText = $allDates != 'Yes' && $toDate && $fromDate ? "{$fromDate} to {$toDate}" : 'all dates';

                $header->xPos = $report->hMargin;
                $header->yPos = $report->pageHeight - $report->vMargin;
                $report->attachObject('header', $header);
                $footer = new TraccFooter(null, $_report['reportName'], $report->pageSize);
                $report->attachObject('footer', $footer);
                break;
            case FILETYPE_XLS:
                $header = new ReportHeader('Cash Book Report');
                $numberFormat = '_(* #,##0.00_);_(* (#,##0.00);_(* "-"??_);_(@_)';
                //                $numberFormat = '_(* #,##0.00_);_(* (#,##0.00);_(@_)';
                $report = new XLSDataReport($filePath, 'Cash Book Report');
                $report->enableFormatting = true;
                break;
        }

        if ($format != FILETYPE_SCREEN) {
            switch ($transactionOption) {
                case 'paymentsOnly':
                    $report->addColumn('transactionDate', 'Date', 40, 'left');
                    if ($reportType == 'bank') {
                        $report->addColumn('propertyID', 'Property', 48, 'left');
                    }

                    if (fileFormatIsXLS($format)) {
                        $report->addColumn('supplierID', 'Lease/Supplier', 50, 'left');
                        $report->addColumn('chequeNumber', 'AP Invoice#', 70, 'left');
                        $report->addColumn('createDate', 'Processing Date', 70, 'left');
                        $report->addColumn('bankDate', '@Bank', 70, 'left');
                    } elseif ($view->items['dateSort']) {
                        $report->addColumn(
                            'chequeNumber',
                            $view->items['dateSort'] == '@Bank' ? '@Bank' : 'Proc. Date',
                            70,
                            'left'
                        );
                        $report->addColumn('supplierID', 'Lease/Supplier', 50, 'left');
                    } else {
                        $report->addColumn('supplierID', 'Lease/Supplier', 50, 'left');
                        $report->addColumn('chequeNumber', 'AP Invoice#', 70, 'left');
                    }

                    $report->addColumn('supplierName', 'Tenant/Payee', 70, 'left');
                    $report->addColumn('description', 'Description', 80 + ($reportType != 'bank' ? 85 : 0), 'left');
                    $report->addColumn('accountID', 'Acct', 20, 'left', '@');
                    $report->addColumn('ChequeEFTNumber', "CHQ/\nEFT#", 50, 'right');
                    $report->addColumn('receiptNumber', 'Payment #', 40, 'right');
                    if (fileFormatIsXLS($format) && getDisplayBsbFromSession()) {
                        $report->addColumn(
                            'suppBsbBillerCode',
                            'Payee ' . $_SESSION['country_default']['bsb_label'] . '/Biller Code',
                            60,
                            'right',
                            '@'
                        );
                    }

                    if (fileFormatIsXLS($format)) {
                        $report->addColumn('suppAcctNoCrn', 'Account number/CRN', 60, 'right', '@');
                    }

                    $report->addColumn('fromDate', 'From', 40, 'left');
                    $report->addColumn('toDate', 'To', 40, 'left');
                    $report->addColumn('paymentsNetAmount', 'Net', 50, 'right', $numberFormat);
                    $report->addColumn(
                        'paymentsGSTAmount',
                        $_SESSION['country_default']['tax_label'],
                        50,
                        'right',
                        $numberFormat
                    );
                    $report->addColumn('paymentsTotalAmount', 'Total', 50, 'right', $numberFormat);
                    if ($view->items['showSubTotal'] == 'Yes') {
                        $report->addColumn('subTotal', 'Sub-Total', 60, 'right', $numberFormat);
                    }

                    break;
                case 'receiptsOnly':
                    $report->addColumn('transactionDate', 'Date', 40, 'left');
                    if ($reportType == 'bank') {
                        $report->addColumn('propertyID', 'Property', 48, 'left');
                    }


                    if (fileFormatIsXLS($format)) {
                        $report->addColumn('leaseID', 'Lease/Supplier', 50, 'left');
                        $report->addColumn('chequeNumber', 'AP Invoice#', 70, 'left');
                        $report->addColumn('createDate', 'Processing Date', 70, 'left');
                        $report->addColumn('bankDate', '@Bank', 70, 'left');
                        $report->addColumn('unitCode', 'Unit Code', 40, 'left');
                    } elseif ($view->items['dateSort']) {
                        $report->addColumn(
                            'chequeNumber',
                            $view->items['dateSort'] == '@Bank' ? '@Bank' : 'Proc. Date',
                            70,
                            'left'
                        );
                        $report->addColumn('leaseID', 'Lease/Supplier', 50, 'left');
                    } else {
                        $report->addColumn('leaseID', 'Lease/Supplier', 50, 'left');
                        $report->addColumn('chequeNumber', 'AP Invoice#', 70, 'left');
                    }

                    $report->addColumn('leaseName', 'Tenant/Payee', 70, 'left');
                    $report->addColumn('description', 'Description', 80 + ($reportType != 'bank' ? 85 : 0), 'left');
                    $report->addColumn('accountID', 'Acct', 20, 'left', '@');
                    $report->addColumn('ChequeEFTNumber', "CHQ/\nEFT#", 40, 'right');
                    $report->addColumn('receiptNumber', 'Receipt #', 55, 'right');

                    // ---NOTE: commented displaying of BSB-Biller Code and Account number-CRN if transaction type selected is receipts only - since it just displays blank data for both columns
                    // if($format == FILETYPE_XLS){
                    //    $report->addColumn('suppBsbBillerCode', "Payee BSB/Biller Code", 60, 'right', '@');
                    //    $report->addColumn('suppAcctNoCrn', "Account number/CRN", 60, 'right', '@');
                    // }

                    $report->addColumn('fromDate', 'From', 40, 'left');
                    $report->addColumn('toDate', 'To', 40, 'left');
                    $report->addColumn('receiptNetAmount', 'Net', 50, 'right', $numberFormat);
                    $report->addColumn(
                        'receiptGSTAmount',
                        $_SESSION['country_default']['tax_label'],
                        50,
                        'right',
                        $numberFormat
                    );
                    $report->addColumn('receiptTotalAmount', 'Total', 50, 'right', $numberFormat);
                    if ($view->items['showSubTotal'] == 'Yes') {
                        $report->addColumn('subTotal', 'Sub-Total', 60, 'right', $numberFormat);
                    }

                    break;
                case 'allTransactions':
                default:
                    if ($view->items['sortOption'] == 'sortAccountID') {
                        $report->addColumn('transactionDate', 'Date', 40, 'left');
                        if ($reportType == 'bank') {
                            $report->addColumn('propertyID', 'Property', 48, 'left');
                        }

                        if ($view->items['showCreateDate']) {
                            $report->addColumn(
                                'chequeNumber',
                                fileFormatIsXLS($format) ? 'Processing Date' : 'Proc. Date',
                                45,
                                'left'
                            );
                            $report->addColumn('leaseSupplierID', 'Lease/Supplier', 50, 'left');
                        } else {
                            $report->addColumn('leaseSupplierID', 'Lease/Supplier', 50, 'left');
                            $report->addColumn('chequeNumber', 'AP Invoice#', 40, 'left');
                        }

                        $report->addColumn('leaseSupplierName', 'Tenant/Payee', 55 + 45, 'left');
                        $report->addColumn(
                            'description',
                            'Description',
                            55 + 45 + ($reportType != 'bank' ? 50 : 0),
                            'left'
                        );

                        if (fileFormatIsXLS($format)) {
                            $report->addColumn('unitCode', 'Unit Code', 40, 'left');
                        }

                        $report->addColumn('accountID', 'Acct', 20, 'left', '@');
                        $report->addColumn('ChequeEFTNumber', "CHQ/\nEFT#", 40, 'right');
                        $report->addColumn('receiptNumber', "Receipt/\nPayment #", 60, 'right');
                        if (fileFormatIsXLS($format) && getDisplayBsbFromSession()) {
                            $report->addColumn(
                                'suppBsbBillerCode',
                                'Payee ' . getBsbLabelFromSession() . '/Biller Code',
                                60,
                                'right',
                                '@'
                            );
                        }

                        if (fileFormatIsXLS($format)) {
                            $report->addColumn('suppAcctNoCrn', 'Account number/CRN', 60, 'right', '@');
                        }

                        $report->addColumn('fromDate', 'From', 40, 'left');
                        $report->addColumn('toDate', 'To', 40, 'left');
                        $report->addColumn('netAmountAll', 'Net', 50, 'right', $numberFormat);
                        $report->addColumn(
                            'gstAmountAll',
                            $_SESSION['country_default']['tax_label'],
                            50,
                            'right',
                            $numberFormat
                        );
                        $report->addColumn('totalAmountAll', 'Total', 50, 'right', $numberFormat);
                        // if ($view->items['showSubTotal'] == 'Yes') $report->addColumn ('subTotal', 'Sub-Total', 50, 'right', $numberFormat);
                    } else {
                        $report->addColumn('transactionDate', 'Date', 36, 'left');
                        if ($reportType == 'bank') {
                            $report->addColumn('propertyID', 'Property', 48, 'left');
                        }

                        if (fileFormatIsXLS($format)) {
                            $report->addColumn('leaseSupplierID', 'Lease/Supplier', 50, 'left');
                            $report->addColumn('chequeNumber', 'AP Invoice#', 70, 'left');
                            $report->addColumn('createDate', 'Processing Date', 70, 'left');
                            $report->addColumn('bankDate', '@Bank', 70, 'left');
                        } elseif ($view->items['dateSort']) {
                            $report->addColumn(
                                'chequeNumber',
                                $view->items['dateSort'] == '@Bank' ? '@Bank' : 'Proc. Date',
                                44,
                                'left'
                            );
                            $report->addColumn(
                                'leaseSupplierID',
                                'Lease/Supplier',
                                ($view->items['individualReport'] ? 100 : 50),
                                'left'
                            );
                        } else {
                            $report->addColumn(
                                'leaseSupplierID',
                                'Lease/Supplier',
                                ($view->items['individualReport'] ? 100 : 50),
                                'left'
                            );
                            $report->addColumn('chequeNumber', 'AP Invoice#', 40, 'left');
                        }

                        $report->addColumn('leaseSupplierName', 'Tenant/Payee', 75, 'left');
                        $report->addColumn('description', 'Description', 50 + ($reportType != 'bank' ? 50 : 0), 'left');

                        // Added by DK
                        if (fileFormatIsXLS($format)) {
                            $report->addColumn('fromDate', 'From', 40, 'left');
                            $report->addColumn('toDate', 'To', 40, 'left');
                            $report->addColumn('unitCode', 'Unit Code', 40, 'left');
                            $report->addColumn('unitDescription', 'Unit', 40, 'left');
                        }

                        $report->addColumn('accountID', 'Acct', 20, 'left', '@');
                        $report->addColumn('ChequeEFTNumber', "CHQ/\nEFT#", 40, 'right');
                        $report->addColumn('receiptNumber', "Receipt/\nPayment #", 40, 'right');

                        if (fileFormatIsXLS($format) && getDisplayBsbFromSession()) {
                            $report->addColumn(
                                'suppBsbBillerCode',
                                'Payee ' . getBsbLabelFromSession() . '/Biller Code',
                                60,
                                'right',
                                '@'
                            );
                        }

                        if (fileFormatIsXLS($format)) {
                            $report->addColumn('suppAcctNoCrn', 'Account number/CRN', 60, 'right', '@');
                        }

                        $report->addColumn(
                            'receiptNetAmount',
                            'Net',
                            (fileFormatIsXLS($format) ? 60 : 47),
                            'right',
                            $numberFormat
                        );
                        $report->addColumn(
                            'receiptGSTAmount',
                            $_SESSION['country_default']['tax_label'],
                            (fileFormatIsXLS($format) ? 60 : 47),
                            'right',
                            $numberFormat
                        );
                        $report->addColumn(
                            'receiptTotalAmount',
                            'Received',
                            (fileFormatIsXLS($format) ? 60 : 47),
                            'right',
                            $numberFormat
                        );


                        $report->addColumn('paymentsNetAmount', 'Net', 47, 'right', $numberFormat);
                        $report->addColumn(
                            'paymentsGSTAmount',
                            $_SESSION['country_default']['tax_label'],
                            47,
                            'right',
                            $numberFormat
                        );
                        $report->addColumn('paymentsTotalAmount', 'Paid', 47, 'right', $numberFormat);
                        // if ($view->items['showSubTotal'] == 'Yes') $report->addColumn ('subTotal', 'Sub-Total', 40, 'right');
                        if (! $view->items['individualReport']) {
                            $report->addColumn('balance', 'Balance', 50, 'right', $numberFormat);
                        }
                    }

                    break;
            }

            $report->addSubHeaderItem('title', 0, 200, 'left');

            if (! $pageBreak) {
                $header->subTitle = ($bankID) ? "{$bankName} ({$bankID})" : 'All Properties';
                $report->preparePage();
                $report->renderHeader();
            }
        } else { // set print to screen headers here
            $tableHeaders = [];
            $colspanAdjustment = 0;

            $tableHeaders[] = 'Transaction Date';
            if ($view->items['reportType'] == 'bank') {
                $tableHeaders[] = 'Property';
            }

            if ($view->items['dateSort'] == 'ProcDate') {
                $tableHeaders[] = 'Processing Date';
                $tableHeaders[] = 'Lease / Supplier';
            } elseif ($view->items['dateSort'] == '@Bank') {
                $tableHeaders[] = '@Bank';
                $tableHeaders[] = 'Lease / Supplier';
            } else {
                $tableHeaders[] = 'Lease / Supplier';
                $tableHeaders[] = 'AP Invoice#';
            }

            $tableHeaders[] = 'Tenant / Payee';
            $tableHeaders[] = 'Description';
            $tableHeaders[] = 'Account Code';
            $tableHeaders[] = 'Cheque / EFT #';
            $tableHeaders[] = 'Receipt / Payment #';
            $tableHeaders[] = 'From';
            $tableHeaders[] = 'To';
            if ($view->items['transactionOption'] == 'paymentsOnly') {
                $tableHeaders[] = 'Net';
                $tableHeaders[] = $_SESSION['country_default']['tax_label'];
                $tableHeaders[] = 'Total';
            } elseif ($view->items['transactionOption'] == 'receiptsOnly') {
                $tableHeaders[] = 'Net';
                $tableHeaders[] = $_SESSION['country_default']['tax_label'];
                $tableHeaders[] = 'Total';
                if ($view->items['showSubTotal'] == 'Yes') {
                    $tableHeaders[] = 'Sub-Total';
                }
            } elseif ($view->items['sortOption'] == 'sortAccountID') {
                $tableHeaders[] = 'Net';
                $tableHeaders[] = $_SESSION['country_default']['tax_label'];
                $tableHeaders[] = 'Total';
            } else {
                $tableHeaders[] = 'Net';
                $tableHeaders[] = $_SESSION['country_default']['tax_label'];
                $tableHeaders[] = 'Received';
                $tableHeaders[] = 'Net';
                $tableHeaders[] = $_SESSION['country_default']['tax_label'];
                $tableHeaders[] = 'Paid';
                if (! $view->items['individualReport']) {
                    $tableHeaders[] = 'Balance';
                }

                $colspanAdjustment = 3;
            }

            $view->items['tableHeaders'] = $tableHeaders;
            $view->items['headerCount'] = count($tableHeaders ?? []);
        }

        if ($reportType == 'bank' && count(
            $properties['bank']
        ) == 0 && $closingBalance[$property]['netAmountAll'] == 0 && $openingBalance[$property]['netAmountAll'] == 0 && $closingBalance[$property]['gstAmountAll'] == 0 && $openingBalance[$property]['gstAmountAll'] == 0 && $closingBalance[$property]['totalAmountAll'] == 0 && $openingBalance[$property]['totalAmountAll'] == 0) {
            $view->items['statusMessage'] = 'No Cash Book report to print.';
            $view->render();

            return false;
        }

        if ($properties && is_array($properties)) {
            ksort($properties);
            if ($format == FILETYPE_SCREEN) {
                $view->items['properties'] = $properties;
                $view->items['propertyList'] = $propertyList;
                $view->items['bankName'] = $bankName;
                $view->items['openingBalance'] = $openingBalance;
                $view->items['closingBalance'] = $closingBalance;
            } else {
                foreach ($properties as $k => $property) {
                    $propertyName = $propertyList[$k]['name'];

                    //                    $propertyBankTotal = $propertyList[$k]['name'] ? "$propertyName ($k) Total" : "$bankName ($bankID) Total";
                    $ownerCode = dbGetPrimaryOwner($k);
                    $ownerName = dbGetOwnerName($k);

                    $header->subText = $allDates != 'Yes' && $toDate && $fromDate ? "{$fromDate} to {$toDate}" : 'all dates';

                    if ($pageBreak) {
                        if ($k == 'allProperties' || ($bankID && empty($propertyList[$k]['name']))) {
                            $header->subTitle = "{$bankName} ({$bankID})";
                        } elseif ($bankID) {
                            $header->subTitle = "{$bankName} ({$bankID}) - {$propertyName} ({$k})";
                        } else {
                            $header->subTitle = "{$propertyName} ({$k})";
                            $header->subText1 = "{$ownerName} ({$ownerCode})";
                        }


                        if (fileFormatIsXLS($format)) {
                            $report->renderHeaderDetails($header->subTitle . '     ' . $header->subText);
                        }

                        $report->preparePage();
                        $report->renderHeader();
                    }

                    // Opening Balance
                    if ($transactionOption != 'receiptsOnly' && $transactionOption != 'paymentsOnly') {
                        $openBalance['balance'] = ($openingBalance[$k]['balance']) ? ((fileFormatIsXLS(
                            $format
                        )) ? $openingBalance[$k]['balance'] : toDecimal(
                            $openingBalance[$k]['balance'],
                            2
                        )) : '0.00';
                        $openBalance['netAmountAll'] = ($openingBalance[$k]['netAmountAll']) ? ((fileFormatIsXLS(
                            $format
                        )) ? $openingBalance[$k]['netAmountAll'] : toDecimal(
                            $openingBalance[$k]['netAmountAll'],
                            2
                        )) : '0.00';
                        $openBalance['gstAmountAll'] = ($openingBalance[$k]['gstAmountAll']) ? ((fileFormatIsXLS(
                            $format
                        )) ? $openingBalance[$k]['gstAmountAll'] : toDecimal(
                            $openingBalance[$k]['gstAmountAll'],
                            2
                        )) : '0.00';
                        $openBalance['totalAmountAll'] = ($openingBalance[$k]['totalAmountAll']) ? ((fileFormatIsXLS(
                            $format
                        )) ? $openingBalance[$k]['totalAmountAll'] : toDecimal(
                            $openingBalance[$k]['totalAmountAll'],
                            2
                        )) : '0.00';

                        if ($view->items['sortOption'] != 'sortAccountID') {
                            //                        if ($view->items['sortOption'] != 'sortAccountID' AND $view->items['accountOption'] == 'allAccountCodes') {
                            if ($format == FILETYPE_PDF) {
                                $openBalance['width']['transactionDate'] = 300;
                                $report->renderSubTotal($openBalance);
                            } else {
                                //                                $report->renderLine(array('transactionDate'=>$header->subTitle,'leaseSupplierID'=>$header->subText));
                                $report->renderLine($openBalance);
                            }

                            //
                        }
                    }

                    $report->renderData_custom($property);

                    //                    if ($format == FILETYPE_XLS) {
                    $closedBalance['receiptNetAmount'] = $closingBalance[$k]['receiptNetAmount'];
                    $closedBalance['receiptGSTAmount'] = $closingBalance[$k]['receiptGSTAmount'];
                    $closedBalance['receiptTotalAmount'] = $closingBalance[$k]['receiptTotalAmount'];
                    $closedBalance['paymentsNetAmount'] = $closingBalance[$k]['paymentsNetAmount'];
                    $closedBalance['paymentsGSTAmount'] = $closingBalance[$k]['paymentsGSTAmount'];
                    $closedBalance['paymentsTotalAmount'] = $closingBalance[$k]['paymentsTotalAmount'];
                    $closedBalance['balance'] = $closingBalance[$k]['balance'];

                    $openBalance['netAmountAll'] = $openingBalance[$k]['netAmountAll'];
                    $openBalance['gstAmountAll'] = $openingBalance[$k]['gstAmountAll'];
                    $openBalance['totalAmountAll'] = $openingBalance[$k]['totalAmountAll'];

                    $propNetChange['netAmountAll'] = ($closingBalance[$k]['receiptNetAmount']) + (-$closingBalance[$k]['paymentsNetAmount']);
                    $propNetChange['gstAmountAll'] = ($closingBalance[$k]['receiptGSTAmount']) + (-$closingBalance[$k]['paymentsGSTAmount']);
                    $propNetChange['totalAmountAll'] = ($closingBalance[$k]['receiptTotalAmount']) + (-$closingBalance[$k]['paymentsTotalAmount']);

                    $closedBalance['netAmountAll'] = $openingBalance[$k]['netAmountAll'] + ($closingBalance[$k]['receiptNetAmount']) + (-$closingBalance[$k]['paymentsNetAmount']);
                    $closedBalance['gstAmountAll'] = $openingBalance[$k]['gstAmountAll'] + ($closingBalance[$k]['receiptGSTAmount']) + (-$closingBalance[$k]['paymentsGSTAmount']);
                    $closedBalance['totalAmountAll'] = $openingBalance[$k]['totalAmountAll'] + ($closingBalance[$k]['receiptTotalAmount']) + (-$closingBalance[$k]['paymentsTotalAmount']);
                    //                    } else {
                    //
                    //                        $netAmountAll = ($openingBalance[$k]['netAmountAll'] + $closingBalance[$k]['receiptNetAmount']) + (-$closingBalance[$k]['paymentsNetAmount']);
                    //                        $gstAmountAll = ($openingBalance[$k]['gstAmountAll'] + $closingBalance[$k]['receiptGSTAmount']) + (-$closingBalance[$k]['paymentsGSTAmount']);
                    //                        $totalAmountAll = ($openingBalance[$k]['totalAmountAll'] + $closingBalance[$k]['receiptTotalAmount']) + (-$closingBalance[$k]['paymentsTotalAmount']);
                    //
                    //                        $openBalance['netAmountAll'] = toDecimal($openingBalance[$k]['netAmountAll'],2);
                    //                        $openBalance['gstAmountAll'] = toDecimal($openingBalance[$k]['gstAmountAll'],2);
                    //                        $openBalance['totalAmountAll'] = toDecimal($openingBalance[$k]['totalAmountAll'],2);
                    //
                    //						$propNetChange['netAmountAll'] = toDecimal($netAmountAll, 2);
                    //                        $propNetChange['gstAmountAll'] = toDecimal($gstAmountAll, 2);
                    //                        $propNetChange['totalAmountAll'] = toDecimal($totalAmountAll, 2);
                    //
                    //                        $closedBalance['netAmountAll'] = toDecimal($netAmountAll + $openingBalance[$k]['netAmountAll'],2);
                    //                        $closedBalance['gstAmountAll'] = toDecimal($gstAmountAll + $openingBalance[$k]['gstAmountAll'],2);
                    //                        $closedBalance['totalAmountAll'] = toDecimal($totalAmountAll + $openingBalance[$k]['totalAmountAll'],2);
                    //
                    //                        $closedBalance['receiptNetAmount'] = toDecimal($closingBalance[$k]['receiptNetAmount'], 2);
                    //                        $closedBalance['receiptGSTAmount'] = toDecimal($closingBalance[$k]['receiptGSTAmount'], 2);
                    //                        $closedBalance['receiptTotalAmount'] = toDecimal($closingBalance[$k]['receiptTotalAmount'], 2);
                    //                        $closedBalance['paymentsNetAmount'] = toDecimal(-$closingBalance[$k]['paymentsNetAmount'], 2);
                    //                        $closedBalance['paymentsGSTAmount'] = toDecimal(-$closingBalance[$k]['paymentsGSTAmount'], 2);
                    //                        $closedBalance['paymentsTotalAmount'] = toDecimal(-$closingBalance[$k]['paymentsTotalAmount'], 2);
                    //                        $closedBalance['balance'] = toDecimal($closingBalance[$k]['balance'], 2);
                    //                    }

                    if ($format == FILETYPE_PDF) {
                        $openBalance['netAmountAll'] = toDecimal($openBalance['netAmountAll'], 2);
                        $openBalance['gstAmountAll'] = toDecimal($openBalance['gstAmountAll'], 2);
                        $openBalance['totalAmountAll'] = toDecimal($openBalance['totalAmountAll'], 2);

                        $closedBalance['netAmountAll'] = toDecimal($closedBalance['netAmountAll'], 2);
                        $closedBalance['gstAmountAll'] = toDecimal($closedBalance['gstAmountAll'], 2);
                        $closedBalance['totalAmountAll'] = toDecimal($closedBalance['totalAmountAll'], 2);

                        $closedBalance['receiptNetAmount'] = toDecimal($closedBalance['receiptNetAmount'], 2);
                        $closedBalance['receiptGSTAmount'] = toDecimal($closedBalance['receiptGSTAmount'], 2);
                        $closedBalance['receiptTotalAmount'] = toDecimal($closedBalance['receiptTotalAmount'], 2);
                        $closedBalance['paymentsNetAmount'] = toDecimal($closedBalance['paymentsNetAmount'], 2);
                        $closedBalance['paymentsGSTAmount'] = toDecimal($closedBalance['paymentsGSTAmount'], 2);
                        $closedBalance['paymentsTotalAmount'] = toDecimal($closedBalance['paymentsTotalAmount'], 2);
                        $closedBalance['balance'] = toDecimal($closedBalance['balance'], 2);

                        if ($view->items['sortOption'] == 'sortAccountID' && $transactionOption != 'receiptsOnly' && $transactionOption != 'paymentsOnly') {
                            $openBalance['width']['transactionDate'] = 300;
                            $propNetChange['width']['transactionDate'] = 300;
                            $closedBalance['width']['transactionDate'] = 300;
                            if ($view->items['sortOption'] == 'sortAccountID') { //                          if ($view->items['sortOption'] == 'sortAccountID' AND $view->items['accountOption'] == 'allAccountCodes')
                                $report->renderSubTotal($openBalance);
                                if ($propNetChange['netAmountAll'] != 0 || $propNetChange['gstAmountAll'] != 0 || $propNetChange['totalAmountAll'] != 0) {
                                    $propNetChange['netAmountAll'] = toDecimal($propNetChange['netAmountAll'], 2);
                                    $propNetChange['gstAmountAll'] = toDecimal($propNetChange['gstAmountAll'], 2);
                                    $propNetChange['totalAmountAll'] = toDecimal($propNetChange['totalAmountAll'], 2);
                                    $report->renderSubTotal($propNetChange);
                                }
                            }

                            //                            if ($view->items['accountOption'] != 'allAccountCodes')
                            //                                $closedBalance['transactionDate'] = $propertyBankTotal;
                            $report->renderSubTotal($closedBalance);
                        } elseif ($view->items['sortOption'] != 'sortAccountID') {
                            $closedBalance['width']['transactionDate'] = 300;
                            if ($transactionOption == 'receiptsOnly') {
                                $closedBalance['transactionDate'] = 'Total Receipts';
                            } elseif ($transactionOption == 'paymentsOnly') {
                                $closedBalance['transactionDate'] = 'Total Payments';
                            }

                            //                            else if ($view->items['accountOption'] != 'allAccountCodes')
                            //                                $closedBalance['transactionDate'] = $propertyBankTotal;
                            $report->renderSubTotal($closedBalance);
                        }
                    } else {
                        $openBalance['netAmountAll'] += 0;
                        $openBalance['gstAmountAll'] += 0;
                        $openBalance['totalAmountAll'] += 0;

                        $propNetChange['netAmountAll'] += 0;
                        $propNetChange['gstAmountAll'] += 0;
                        $propNetChange['totalAmountAll'] += 0;

                        $closedBalance['netAmountAll'] += 0;
                        $closedBalance['gstAmountAll'] += 0;
                        $closedBalance['totalAmountAll'] += 0;

                        $closedBalance['receiptNetAmount'] += 0;
                        $closedBalance['receiptGSTAmount'] += 0;
                        $closedBalance['receiptTotalAmount'] += 0;
                        $closedBalance['paymentsNetAmount'] += 0;
                        $closedBalance['paymentsGSTAmount'] += 0;
                        $closedBalance['paymentsTotalAmount'] += 0;
                        $closedBalance['balance'] += 0;

                        if ($transactionOption != 'receiptsOnly' && $transactionOption != 'paymentsOnly') {
                            if ($view->items['sortOption'] == 'sortAccountID') {
                                $report->renderLine($openBalance);

                                if ($propNetChange['netAmountAll'] != 0 || $propNetChange['gstAmountAll'] != 0 || $propNetChange['totalAmountAll'] != 0) {
                                    $report->renderLine($propNetChange);
                                }
                            }

                            if (! $view->items['individualReport']) {
                                $closedBalance['headerStyle'] =
                                [
                                    'fill' => [
                                        'type' => Fill::FILL_SOLID,
                                        'color' => ['rgb' => 'dcf2ff'],
                                    ],
                                    'font' => ['bold' => true, 'color' => ['rgb' => '000000']],
                                    'alignment' => [
                                        'vertical' => Alignment::VERTICAL_CENTER,
                                        'horizontal' => Alignment::HORIZONTAL_LEFT,
                                    ],
                                ];
                                //                                $closedBalance['transactionDate'] = $propertyBankTotal;
                            }

                            $report->renderLine_custom($closedBalance);
                        } elseif ($view->items['sortOption'] != 'sortAccountID') {
                            if ($transactionOption == 'receiptsOnly') {
                                $closedBalance['transactionDate'] = 'Total Receipts';
                            } elseif ($transactionOption == 'paymentsOnly') {
                                $closedBalance['transactionDate'] = 'Total Payments';
                            } elseif ($view->items['accountOption'] != 'allAccountCodes') {
                                $closedBalance['headerStyle'] =
                                [
                                    'fill' => [
                                        'type' => Fill::FILL_SOLID,
                                        'color' => ['rgb' => 'dcf2ff'],
                                    ],
                                    'font' => ['bold' => true, 'color' => ['rgb' => '000000']],
                                    'alignment' => [
                                        'vertical' => Alignment::VERTICAL_CENTER,
                                        'horizontal' => Alignment::HORIZONTAL_LEFT,
                                    ],
                                ];
                                //                                $closedBalance['transactionDate'] = $propertyBankTotal;
                            }

                            $report->renderLine_custom($closedBalance);
                        }

                        $xx['leaseName'] =
                        $xx['supplierName'] =
                        $xx['leaseSupplierName'] = '';
                        $report->renderLine($xx);
                    }

                    if ($pageBreak) {
                        $report->clean();
                        $report->endPage();
                    }
                }
            }
        }

        if ($format != FILETYPE_SCREEN) {
            if (! $pageBreak) {
                $report->clean();
                $report->endPage();
            }

            $report->close();
        }
    } else {
        $view->items['statusMessage'] = 'No Cash Book report to print.';
    }

    if (! $context[DOC_MASTER]) {
        if ($context[IS_TASK]) {
            $email = new EmailTemplate('views/emails/basicEmail.html', SYSTEMURL);
            $attachment = [];
            $attachment[0]['file'] = REPORTPATH . '/' . $pdfDownloadLink;
            $attachment[0]['content_type'] = 'application/pdf';
            sendMail(
                $_SESSION['email'],
                $_SESSION['first_name'] . ' ' . $_SESSION['last_name'],
                $email->toString(),
                'Report',
                $attachment
            );
            logData('Emailed report to ' . $_SESSION['email']);
            $context[TASK_COMPLETE] = true;
            $view->items['statusMessage'] = 'Due to its complexity, this report has been scheduled and will be emailed to you on completion.';
        } else {
            $view->items['downloadPath'] = $downloadPath;
            if ($_SESSION['user_type'] == USER_OWNER) {
                $_SESSION['downloadFile'] = $downloadPath;
            }
        }

        $view->render();
    }


}

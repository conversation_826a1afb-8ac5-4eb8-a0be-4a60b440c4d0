<?php

function managementAgreement(&$context)
{
    global $stateList;

    $userpermission = new userpermission();
    $result = $userpermission->userHasPageAccess(__FUNCTION__);

    if (! isPostback()) {
        $view = new MasterPage(userViews(), '/managementReports/managementAgreement.html');
        $view->setSection($context['module']);
    } else {
        $view = new UserControl(userViews(), '/managementReports/managementAgreement.html');
    }

    $view->bindAttributesFrom($_REQUEST);

    $view->items['propertyManagerList'] = dbGetPropertyManagers();
    $view->items['property'] = deserializeParameters($view->items['property']);
    $view->items['propertyList'] = dbGetPropertyByCriteria(null, null, $view->items['propertyManager'], null, 1);


    $reports = dbGetReports(TASKTYPE_MANAGEMENTAGREEMENT);
    $reportList = mapParameters($reports, 'reportID', 'reportDescription');
    $view->items['reportList'] = $reportList;
    if (! isset($view->items['reportID'])) {
        $view->items['reportID'] = $reports[0]['reportID'];
    }

    $view->items['methodList'] =
    [
        1 => 'Current',
        0 => 'All Historic',
    ];
    $view->items['formatList'] =
    [
        FILETYPE_PDF => 'PDF',
        FILETYPE_XLS => 'Excel Spreadsheet',
        FILETYPE_SCREEN => 'Print to Screen',
    ];
    $view->items['sortBy'] =
    [
        'sortPropertyCode' => 'Property Code',
        'sortExpiryDate' => 'Expiry Date',
    ];

    if (! isset($view->items['toDate'])) {
        $view->items['toDate'] = TODAY;
    }
    if (! isset($view->items['format'])) {
        $view->items['format'] = FILETYPE_PDF;
    }
    if (! isset($view->items['method'])) {
        $view->items['method'] = 1;
    }
    if (! array_key_exists($view->items['sortOption'], $view->items['sortBy'])) {
        $view->items['sortOption'] = 'sortPropertyCode';
    }

    switch ($view->items['action']) {
        case 'finalise':
            $validationErrors =  [];
            $properties = deserializeParameters($view->items['property']);
            if (empty($view->items['property'])) {
                $validationErrors[] = 'You need to select at least one Property';
            }

            if (noErrors($validationErrors)) {
                if ($view->items['format'] == FILETYPE_SCREEN) {
                    $view->render();
                }
                $context = $view->items;
                executeCommand('managementAgreementProcess', 'managementReports');
            }
            break;
    }

    if ($view->items['format'] != FILETYPE_SCREEN || empty($view->items['action']) || $validationErrors) {
        $view->items['validationErrors'] = $validationErrors;
        $view->render();
    }
}

<?php

/**
 * @param  $context  array Mandatory. Referenced array containing data needed to continue.
 *
 * <AUTHOR>
 **/
function recoverableReportProcess($context)
{
    global $clientDirectory, $pathPrefix;

    if (! isPostback()) {
        $view = new MasterPage(userViews(), '/managementReports/recoverableReportProcess.html');
        $view->setSection($context['module']);
    } else {
        $view = new UserControl(userViews(), '/managementReports/recoverableReportProcess.html');
    }
    $view->bindAttributesFrom($_REQUEST);

    $_report['reportName'] = 'Recoverable Costs Report';
    $reportType = $view->items['reportType'];
    $recoveredTitle = 'Unrecovered Transactions';
    if ($reportType == 'recovered') {
        $recoveredTitle = 'Recovered Transactions';
    }
    $view->items['report_type'] = $reportType;
    /*if ($reportType == 'bank')
    {
        $dataID = $bankID = $view->items['bankID'];
        foreach ($context['bankList'] as $v)
        {
            if ($v['bankID'] == $bankID)
            {
                $bankName = $v['bankAccountName'];
                break;
            }
        }
    }
    else
    {*/
    foreach ($context['propertyGroupedList'] as $v) {
        $propertyID = $v['propertyID'];
        $propertyList[$propertyID]['name'] = $v['propertyName'];
    }
    if ($context['propertyID'] and is_array($context['propertyID'])) {
        $dataID = $propertyID = $context['propertyID'];
        $propertyCount = count($context['propertyID']);
    }
    // }
    $propertyCount = count($context['propertyID']);

    $openBalance['leaseSupplierName'] = 'Opening Balance';
    $closedBalance['leaseSupplierName'] = 'Closing Balance';
    $fromDate = $view->items['fromDate'];
    $toDate = $view->items['toDate'];
    $allDates = $view->items['allDates'];
    $pageBreak = ($context['pageBreak'] == 'Yes') ? true : false;
    $showReceiptNo = ($context['showReceiptNo'] == 'Yes') ? true : false;

    if ($view->items['accountOption'] == 'multipleCodes') {
        $accountID = deserializeParameters($view->items['accountID']);
    } elseif ($view->items['accountOption'] == 'accountRange' and $view->items['accountIDRange1'] and $view->items['accountIDRange2']) {
        //        $accountID = range ($view->items['accountIDRange1'], $view->items['accountIDRange2']);
        //        foreach ($accountID as $k => $v) $accountID[$k] = str_pad ($v, 4, '0', STR_PAD_LEFT);

        $accountID = getValidAccountCodesFromRange($view->items['accountIDRange1'], $view->items['accountIDRange2']);
    } else {
        $chartAccountListDr = dbGetChartOfAccountByRecoverable(true);
        $chartAccountListArr = [];
        foreach ($chartAccountListDr as $k => $v) {
            array_push($chartAccountListArr, $v['chartAccountCode']);
        }

        // $accountID = range ($view->items['accountIDRange1'], $view->items['accountIDRange2']);
        foreach ($chartAccountListArr as $k => $v) {
            $chartAccountListArr[$k] = str_pad($v, 4, '0', STR_PAD_LEFT);
        }
        // $chartAccountListArr = "'" . implode ("','", $chartAccountListArr) . "'";
        $accountID = $chartAccountListArr;
    }

    $reportResult = dbGetApRecoverable(
        $reportType,
        $dataID,
        $allDates,
        $accountID,
        $fromDate,
        $toDate,
        $view->items['sortOption']
    ); // dbGetCashBook ($reportType, $dataID, /*$transactionOption,*/ $allDates, $accountID, $fromDate, $toDate, $leaseID, $supplierID, $view->items['sortOption']);
    $count = count($reportResult);

    /*if ($view->items['showZero'] == 'Yes')
    {
        foreach (deserializeParameters ($view->items['propertyID']) as $propertyID) $properties[$propertyID] = array ();
        $count = sizeof($properties);
    }*/

    if ($context[IS_TASK]) {
        $view->bindAttributesFrom($context);
        extract($context);
    } else {
        $view->bindAttributesFrom($context);
        $view->bindAttributesFrom($_REQUEST);
        $queue = new Queue(TASKTYPE_CASH_BOOK);
        if ($propertyCount > THRESHOLD_CASHBOOK) {
            $queue->add(
                $_SESSION['clientID'],
                $_SESSION['un'],
                'command=recoverableReportProcess&module=ap',
                $_REQUEST
            );
        }
    }

    if ($count and ($context[IS_TASK] || $propertyCount <= THRESHOLD_CASHBOOK)) {
        $format = $view->items['format'];

        if (! $context[DOC_MASTER]) {
            $logoFile = dbGetClientLogo();
            $logoPath = "assets/clientLogos/{$logoFile}";
            $_filePath = "{$pathPrefix}{$clientDirectory}/{$format}/" . DOC_RECOVERABLE . '/';
            $_downloadPath = "{$clientDirectory}/{$format}/" . DOC_RECOVERABLE;
            $file = DOC_RECOVERABLE . '_' . date('Ymd') . ".{$format}";
            $filePath = $_filePath . $file;
            $downloadPath = "{$_downloadPath}/{$file}";
        }

        foreach ($reportResult as $k => $v) {
            $transaction[$k]['transactionDate'] = $v['transactionDate'];
            $transaction[$k]['invoiceNo'] = $v['invoice_no'];
            $transaction[$k]['property'] = $v['property'];
            $transaction[$k]['supplierID'] = $v['supplierID'];
            $transaction[$k]['supplierName'] = $v['supplierName'];
            $transaction[$k]['accountID'] = $v['accountID'];
            $transaction[$k]['description'] = $v['description'];
            $transaction[$k]['fromDate'] = $v['fromDate'];
            $transaction[$k]['toDate'] = $v['toDate'];
            $transaction[$k]['dueDate'] = $v['dueDate'];
            $transaction[$k]['taxAmount'] = toDecimal($v['taxAmount'], 2);
            $transaction[$k]['netAmount'] = toDecimal($v['netAmount'], 2);
            $transaction[$k]['transactionAmount'] = toDecimal($v['transactionAmount'], 2);
            $transaction[$k]['recoveredBy'] = $v['recovered_by'];
            if ($v['recovered'] == 1) {
                $transaction[$k]['recovered'] = 'Recovered';
            } else {
                $transaction[$k]['recovered'] = 'Unrecovered';
            }


            switch ($format) {
                case FILETYPE_PDF:
                    $report = ($context[DOC_MASTER]) ? new PDFDataReport(
                        $context[DOC_MASTER],
                        $logoPath,
                        A4_LANDSCAPE
                    ) : new PDFDataReport($filePath, $logoPath, A4_LANDSCAPE);
                    $report->multiLine = true;
                    $report->printRowLines = true;
                    $report->printColumnLines = false;
                    $report->printBorders = false;
                    $report->cache = false;
                    $header = new ReportHeader('Recoverable Costs Reports');
                    $header->subTitle = $recoveredTitle;
                    if ($allDates != 'Yes' and $toDate and $fromDate) {
                        $header->subText = "$fromDate to $toDate";
                    } else {
                        $header->subText = 'all dates';
                    }
                    $header->xPos = $report->hMargin;
                    $header->yPos = $report->pageHeight - $report->vMargin;
                    $report->attachObject('header', $header);
                    $footer = new TraccFooter(null, $_report['reportName'], $report->pageSize);
                    $report->attachObject('footer', $footer);
                    break;
                case FILETYPE_XLS:
                    $numberFormat = '_(* #,##0.00_);_(* (#,##0.00);_(* "-"??_);_(@_)';
                    $report = new XLSDataReport($filePath, 'Recoverable Costs Report');
                    $report->enableFormatting = true;
                    break;
            }

            if ($format != FILETYPE_SCREEN) {
                $report->addColumn('transactionDate', 'Transaction Date', 60, 'left');
                $report->addColumn('invoiceNo', 'Invoice#', 50, 'left');
                $report->addColumn('property', 'Property', 50, 'left');
                $report->addColumn('supplierID', 'Supplier', 50, 'left');
                $report->addColumn('supplierName', 'Supplier Name', 60, 'left');
                $report->addColumn('accountID', 'Account Code', 35, 'left');
                $report->addColumn('description', 'Description', 80, 'left');
                $report->addColumn('fromDate', 'From Date', 40, 'left');
                $report->addColumn('toDate', 'To Date', 40, 'left');
                $report->addColumn('dueDate', 'Due Date', 40, 'left');
                $report->addColumn('taxAmount', 'Tax Amount', 40, 'right');
                $report->addColumn('netAmount', 'Net Amount', 40, 'right');
                $report->addColumn('transactionAmount', 'Gross Amount', 60, 'right', $numberFormat);
                if ($format == FILETYPE_XLS) {
                    $report->addColumn('recovered', 'Status', 50, 'left');
                }
                if ($reportType == 'recovered') {
                    $report->addColumn('recoveredBy', 'Recovered By', 50, 'left');
                }
                $report->addSubHeaderItem('title', 0, 200, 'left');

                if (! $pageBreak) {
                    $header->subTitle = $recoveredTitle; // ($bankID) ? "$bankName ($bankID)" : 'All Properties';
                    $report->preparePage();
                    $report->renderHeader();
                }
            }

            if ($transaction and is_array($transaction)) {
                if ($format == FILETYPE_SCREEN) {
                    $view->items['transaction'] = $transaction;
                } else {
                    if ($pageBreak) {
                        // $header->subTitle = "$propertyName ($k)";
                        $header->subTitle = $recoveredTitle;
                        $report->preparePage();
                        $report->renderHeader();
                    }

                    $report->renderData($transaction);


                    if ($pageBreak) {
                        $report->clean();
                        $report->endPage();
                    }
                }
            }

            if ($format != FILETYPE_SCREEN) {
                if (! $pageBreak) {
                    $report->clean();
                    $report->endPage();
                }

                $report->close();
            }
        }
    } else {
        $view->items['statusMessage'] = 'No report to print.';
    }

    if (! $context[DOC_MASTER]) {
        if ($context[IS_TASK]) {
            $email = new EmailTemplate('views/emails/basicEmail.html', SYSTEMURL);
            $attachment = [];
            $attachment[0]['file'] = REPORTPATH . '/' . $pdfDownloadLink;
            $attachment[0]['content_type'] = 'application/pdf';
            sendMail(
                $_SESSION['email'],
                $_SESSION['first_name'] . ' ' . $_SESSION['last_name'],
                $email->toString(),
                'Report',
                $attachment
            );
            logData('Emailed report to ' . $_SESSION['email']);
            $context[TASK_COMPLETE] = true;
            $view->items['statusMessage'] = 'Due to its complexity, this report has been scheduled and will be emailed to you on completion.';
        } else {
            $view->items['downloadPath'] = $downloadPath;
        }
        $view->render();
    }
}

<?php

/**
 * Created by PhpStorm.
 * User: adacanay
 * Date: 7/12/2017
 * Time: 10:17 AM
 */
unset($properties_);
unset($fundOpeningAmount);
unset($fundReceiptsAmount);
unset($fundPaymentsAmount);
unset($ownerRemittance);
unset($main);
// Opening Balance
foreach (dbGetOpeningTrialBalanceFund('All', $propertyID, $periodFrom) as $v) {

    $properties_[$v['propertyCode']]['propertyFund'] = 'All';
    $v['amount'] *= -1;
    $properties_[$v['propertyCode']]['openingBalance'] += $v['amount'];
    $properties_[$v['propertyCode']]['closingBalance'] += $v['amount'];
    //    $total['openingBalance'] += $v['amount'];
    //    $total['closingBalance'] += $v['amount'];

    switch ($v['accountGroup']) {
        case 'INC.OWN':
        case 'EXP.OWN':
        case 'EXP.DIS':
        case 'LIA.CUR':
        case 'EQU.RET':
            $properties_[$v['propertyCode']]['ownerAmount'] += $v['amount'];
            $properties_[$v['propertyCode']]['openingOwnerAmount'] += $v['amount'];
            // $total['ownerAmount'] += $v['amount'];
            break;
        case 'INC.OUT':
        case 'EXP.OUT':
            $properties_[$v['propertyCode']]['outgoingAmount'] += $v['amount'];
            $properties_[$v['propertyCode']]['openingOutGoingAmount'] += $v['amount'];
            // $total['outgoingAmount'] += $v['amount'];
            break;
        case 'INC.REC':
        case 'EXP.REC':
            $properties_[$v['propertyCode']]['recoverableAmount'] += $v['amount'];
            $properties_[$v['propertyCode']]['openingRecoverableAmount'] += $v['amount'];
            // $total['recoverableAmount'] += $v['amount'];
            break;
        default:
            if ($v['pmca_type'] == 'B') {
                $properties_[$v['propertyCode']]['ownerAmount'] += $v['amount'];
                $properties_[$v['propertyCode']]['openingOwnerAmount'] += $v['amount'];
            }

            break;

    }


}

// Balance
$balances = dbGetBankBalance([$propertyID]);
foreach ($balances as $v) {
    $properties_[$v['propertyID']]['propertyFund'] = 'All';
    $period = dbGetPeriodYear($v['propertyID'], $periodFrom, $periodTo);

    foreach ($period as $p) {

        if ($p['year'] == $v['year'] && $p['period'] == $v['period']) {

            switch ($v['accountGroup']) {
                case 'INC.OWN':
                    // case 'LIA.CUR':
                case 'INC.OUT':
                case 'INC.REC':
                    $properties_[$v['propertyID']]['receiptAmount'] += $v['amount'];
                    $properties_[$v['propertyID']]['closingBalance'] += $v['amount'];
                    //                    if ($context['format'] != FILETYPE_XLS)
                    //                    {
                    //                        $total['receiptAmount'] += $v['amount'];
                    //                        $total['closingBalance'] += $v['amount'];
                    //
                    //                    }
                    break;
                case 'EXP.OWN':
                    // case 'EXP.DIS':
                case 'EXP.OUT':
                case 'EXP.REC':
                    //  case 'EQU.RET':
                    $properties_[$v['propertyID']]['paymentAmount'] += $v['amount'];
                    $properties_[$v['propertyID']]['closingBalance'] += $v['amount'];
                    //                    if ($context['format'] != FILETYPE_XLS)
                    //                    {
                    //                        $total['paymentAmount'] += $v['amount'];
                    //                        $total['closingBalance'] += $v['amount'];
                    //                    }
                    break;
                case 'ASS.CUR':
                case 'ASS.BAN':
                    break;
            }



            switch ($v['accountGroup']) {
                case 'INC.OWN':
                case 'EXP.OWN':
                case 'EXP.DIS':
                case 'LIA.CUR':
                case 'EQU.RET':
                    $properties_[$v['propertyID']]['ownerAmount'] += $v['amount'];
                    break;
                case 'INC.OUT':
                case 'EXP.OUT':
                    $properties_[$v['propertyID']]['outgoingAmount'] += $v['amount'];
                    break;
                case 'INC.REC':
                case 'EXP.REC':
                    $properties_[$v['propertyID']]['recoverableAmount'] += $v['amount'];
                    break;
            }

            switch ($v['accountGroup']) {
                case 'EXP.DIS':
                    $properties_[$v['propertyID']]['ownerRemittanceAmount'] += $v['amount'];
                    $properties_[$v['propertyID']]['mainPaymentOwnerAmount'] += $v['amount'];
                    break;
                case 'INC.OWN':
                    $properties_[$v['propertyID']]['receiptOwnerAmount'] += $v['amount'];
                    break;
                case 'INC.OUT':
                    $properties_[$v['propertyID']]['receiptOutGoingAmount'] += $v['amount'];
                    break;
                case 'INC.REC':
                    $properties_[$v['propertyID']]['receiptRecoverableAmount'] += $v['amount'];
                    break;
                case 'EXP.OWN':
                    $properties_[$v['propertyID']]['paymentOwnerAmount'] += $v['amount'];
                    break;
                case 'EXP.OUT':
                    $properties_[$v['propertyID']]['paymentOutGoingAmount'] += $v['amount'];
                    break;
                case 'EXP.REC':
                    $properties_[$v['propertyID']]['paymentRecoverableAmount'] += $v['amount'];
                    break;
                default:
                    if ($v['pmca_type'] == 'B') {
                        $properties_[$v['propertyID']]['ownerRemittanceAmount'] += $v['amount'];
                        $properties_[$v['propertyID']]['mainPaymentOwnerAmount'] += $v['amount'];
                    }

                    break;
            }

        }


    }

}

// ***********get the gst value per property add to receipt and payment added by arjay*******
$gst = gstAccount();
foreach ($properties_ as $k => $val) {
    $gstInc = gstAmount($k, $gst['gstOutputTax'], $periodFrom, $periodTo);
    $gstExp = gstAmount($k, $gst['gstInputTax'], $periodFrom, $periodTo);
    $val['receiptAmount'] += $gstInc;
    $val['paymentAmount'] += $gstExp;
    $val['receiptOwnerAmount'] += $gstInc;
    $val['paymentOwnerAmount'] += $gstExp;
    //    $total['paymentAmount']=$total['paymentAmount']+$gstExp;
    //    $total['receiptAmount']=$total['receiptAmount']+$gstInc;
    $properties_[$k] = $val;
}

// ***********get the partition fund per property added by arjay*******
$properties_1 = [];

foreach ($properties_ as $k => $v) {

    $prop = $k;
    $fundID = dbGetTrialBalanceFund($prop, $periodFrom, $periodTo);
    $v['propertyFund'] = 'Main Fund';

    foreach ($fundID as $p) {
        $p['propertyFund'] = $p['partition'];
        $fundName = dbGetFundNameByPartitionAndProperty($prop, $p['partition']);
        $p['fundName'] = $fundName['fundName'];
        $p['ownerName'] = $fundName['fundName'];
        $p['fundPropertyCode'] = $prop;
        $p['propertyCode'] = $p['partition'];

        $v['openingOwnerAmount'] -= $p['openingOwnerAmount'];
        $v['openingOutGoingAmount'] -= $p['openingOutGoingAmount'];
        $v['openingRecoverableAmount'] -= $p['openingRecoverableAmount'];

        $p['closingBalance'] = ($p['openingBalance'] + $p['receiptAmount']) + $p['paymentAmount'];

        $v['receiptAmount'] -= $p['receiptAmount'];
        $v['paymentAmount'] -= $p['paymentAmount'];
        $v['openingBalance'] -= $p['openingBalance'];
        $v['closingBalance'] = ($v['openingBalance'] + $v['receiptAmount']) + $v['paymentAmount'];


        $v['receiptOwnerAmount'] -= $p['incomeOwnerAmount'];
        $v['receiptOutGoingAmount'] -= $p['incomeOutGoingAmount'];
        $v['receiptRecoverableAmount'] -= $p['incomeRecoverableAmount'];
        $v['paymentOwnerAmount'] -= $p['expenseOwnerAmount'];
        $v['paymentOutGoingAmount'] -= $p['expenseOutGoingAmount'];
        $v['paymentRecoverableAmount'] -= $p['expenseRecoverableAmount'];

        $properties_[$p['partition'] . '-' . $k] = $p;

    }

    $properties_[$k] = $v;

}

if (count($properties_ ?? []) > 1) {
    $array = [[null, 'Total', 'Main Property', '   Owners Fund', '   Outgoings Fund', '   Recoverables Fund']];
} else {
    $array = [[null, 'Total', 'Owners Fund', 'Outgoings Fund', 'Recoverables Fund']];
}


$i = 0;
foreach ($properties_ as $v) {

    if ($i > 0) {
        $array[0][] = $v['fundName'];
        $fundPartition[] = $v['fundName'];
        $fundOpeningAmount[$v['fundName']] = (isset($v['openingBalance'])) ? $v['openingBalance'] : 0;
        $fundReceiptsAmount[$v['fundName']] = (isset($v['receiptAmount'])) ? $v['receiptAmount'] : 0;
        $fundPaymentsAmount[$v['fundName']] = (isset($v['paymentAmount'])) ? $v['paymentAmount'] : 0;
    } else {

        $main = [
            'mainOpening'                   => (isset($v['openingBalance'])) ? $v['openingBalance'] : 0,
            'mainOpeningOwnerAmount'        => (isset($v['openingOwnerAmount'])) ? $v['openingOwnerAmount'] : 0,
            'mainOpeningOutGoingAmount'     => (isset($v['openingOutGoingAmount'])) ? $v['openingOutGoingAmount'] : 0,
            'mainOpeningRecoverableAmount'  => (isset($v['openingRecoverableAmount'])) ? $v['openingRecoverableAmount'] : 0,
        ];
        $receipts = [
            'mainReceiptAmount'         => (isset($v['receiptAmount'])) ? $v['receiptAmount'] : 0,
            'mainReceiptOwnerAmount'    => (isset($v['receiptOwnerAmount'])) ? $v['receiptOwnerAmount'] : 0,
            'mainReceiptOutGoingAmount' => (isset($v['receiptOutGoingAmount'])) ? $v['receiptOutGoingAmount'] : 0,
            'mainReceiptRecoverableAmount' => (isset($v['receiptRecoverableAmount'])) ? $v['receiptRecoverableAmount'] : 0,
        ];
        $payments = [
            'mainPaymentAmount'             => (isset($v['paymentAmount'])) ? $v['paymentAmount'] : 0,
            'mainPaymentOwnerAmount'        => (isset($v['paymentOwnerAmount'])) ? $v['paymentOwnerAmount'] : 0,
            'mainPaymentOutGoingAmount'     => (isset($v['paymentOutGoingAmount'])) ? $v['paymentOutGoingAmount'] : 0,
            'mainPaymentRecoverableAmount' => (isset($v['paymentRecoverableAmount'])) ? $v['paymentRecoverableAmount'] : 0,
        ];
        $ownerRemittance = [
            'mainRemittanceAmount'             => (isset($v['ownerRemittanceAmount'])) ? $v['ownerRemittanceAmount'] : 0,
            'mainRemittanceOwnerAmount'        => (isset($v['ownerRemittanceAmount'])) ? $v['ownerRemittanceAmount'] : 0,
            'mainRemittanceOutGoingAmount'     => 0,
            'mainRemittanceRecoverableAmount' => 0,
        ];
    }

    $i++;
}

$array[0][] = 'Total';

if (count($properties_ ?? []) > 1) {
    $openingArray = ['Opening|Balance', $main['mainOpening'], $main['mainOpening'], $main['mainOpeningOwnerAmount'], $main['mainOpeningOutGoingAmount'], $main['mainOpeningRecoverableAmount']];
    $receiptArray = ['Operating|Receipts ', $receipts['mainReceiptAmount'], $receipts['mainReceiptAmount'], $receipts['mainReceiptOwnerAmount'], $receipts['mainReceiptOutGoingAmount'], $receipts['mainReceiptRecoverableAmount']];
    $payments = ['Operating    |Payments    ', $payments['mainPaymentAmount'], $payments['mainPaymentAmount'], $payments['mainPaymentOwnerAmount'], $payments['mainPaymentOutGoingAmount'], $payments['mainPaymentRecoverableAmount']];
    $remittance = ['Non-Operating     |Receipts/(Payments)', $ownerRemittance['mainRemittanceAmount'], $ownerRemittance['mainRemittanceAmount'], $ownerRemittance['mainRemittanceOwnerAmount'], $ownerRemittance['mainRemittanceOutGoingAmount'], $ownerRemittance['mainRemittanceRecoverableAmount']];
    if ($fundOpeningAmount) {
        foreach ($fundOpeningAmount as $o) {
            $openingArray[] = $o;
            $openingArray[1] += $o;
            $remittance[] += 0.00;
        }
    }

    if ($fundReceiptsAmount) {
        foreach ($fundReceiptsAmount as $o) {
            $receiptArray[] = $o;
            $receiptArray[1] += $o;
        }
    }

    if ($fundPaymentsAmount) {
        foreach ($fundPaymentsAmount as $o) {
            $payments[] = $o;
            $payments[1] += $o;
        }
    }

    $openingArray[] = $openingArray[1];
    $remittance[] = $remittance[1];
    $receiptArray[] = $receiptArray[1];
    $payments[] = $payments[1];
} else {
    $openingArray = ['Opening|Balance', $main['mainOpening'], $main['mainOpeningOwnerAmount'], $main['mainOpeningOutGoingAmount'], $main['mainOpeningRecoverableAmount']];
    $receiptArray = ['Operating|Receipts ', $receipts['mainReceiptAmount'], $receipts['mainReceiptOwnerAmount'], $receipts['mainReceiptOutGoingAmount'], $receipts['mainReceiptRecoverableAmount']];
    $payments = ['Operating    |Payments    ', $payments['mainPaymentAmount'], $payments['mainPaymentOwnerAmount'], $payments['mainPaymentOutGoingAmount'], $payments['mainPaymentRecoverableAmount']];
    $remittance = ['Non-Operating     |Receipts/(Payments)',  $ownerRemittance['mainRemittanceAmount'], $ownerRemittance['mainRemittanceOwnerAmount'], $ownerRemittance['mainRemittanceOutGoingAmount'], $ownerRemittance['mainRemittanceRecoverableAmount']];
    $openingArray[] = $openingArray[1];
    $remittance[] = $remittance[1];
    $receiptArray[] = $receiptArray[1];
    $payments[] = $payments[1];
}

$array[] = $openingArray;
$array[] = $receiptArray;
$array[] = $payments;
$array[] = $remittance;

// ***********************end fund partition***************************
$pdf->begin_page_ext(595, 842, '');
$page_header = 'Property Financial Report Fund Summary ';
$page++;

$pdf->setFontExt($_fonts['Helvetica-Bold'], 9);


$pdf->showBoxed("{$page_header}", 100, 690, 400, 20, 'center', '');
$pdf->setFontExt($_fonts['Helvetica'], 8);
$pdf->show_xy("Period From: {$periodFrom} To: {$periodTo}", 230, 685);
$pdf->show_xy('Owner:', 25, 750);
$pdf->continue_text('Property:');
$pdf->continue_text('Report for:');
$pdf->show_xy($client, 85, 750);
$pdf->continue_text($propertyName . " [{$propertyID}]");
$pdf->continue_text($periodDescription);

// top line
$pdf->setlinewidth(0.5);
$pdf->moveto(25, 675);
$pdf->lineto(570, 675);
$pdf->stroke();

$pdf->moveto(25, 643);
$pdf->lineto(570, 643);
$pdf->stroke();

$pdf->setFontExt($_fonts['Helvetica'], 9);
$pad = 0;
unset($totalArray);
foreach ($array as $key => $data) {
    $lineColor = -75;
    foreach (array_keys($data) as $k) {
        if ($k != 1) {
            $lineColor += 25;
        }
    }
}

$pdf->setColorExt('fill', 'rgb', 0.88, 0.88, 0.88, 0);
$pdf->rect(25, 598 - $lineColor, 545, 20);
$pdf->fill();
$pdf->setColorExt('fill', 'rgb', 0, 0, 0, 0);
$xAxis = 27;
foreach ($array as $key => $data) {
    $yAxis = 570;

    if ($key == 0) {
        $pdf->setFontExt($_fonts['Helvetica-Bold'], 9);
    } else {
        $pdf->setFontExt($_fonts['Helvetica'], 9);
    }

    $line = -50;
    foreach ($data as $k => $val) {

        if ($k != 1) {
            $line += 25;
            if ($key == 0) {
                $value = $val;
            } else {
                $value = (is_numeric($val)) ? formatting($val, 2) : $val;
            }

            if ($k == 0) {
                $value = str_replace('|', "\n", $value);
            }


            if (count($data ?? []) - 1 == $k || $k == 0 || $key == 0) {
                $pdf->setFontExt($_fonts['Helvetica-Bold'], 9);
            } else {
                $pdf->setFontExt($_fonts['Helvetica'], 9);
            }

            $pdf->showBoxed($value, $xAxis, $yAxis, ($key == 0 ? 95 : 90), 95, ($key == 0 ? 'left' : ($k == 0 ? 'right' : 'right')), '');
            if ($key != 0) {
                if ($k != 0) {
                    $totalArray[$k] = $value == null ? $totalArray[$k] + 0 : $totalArray[$k] + $val;
                } else {
                    $totalArray[$k] = "Closing   \nBalance  ";
                }

                if ($key == 4) {
                    if ($k != 0) {
                        $pdf->setFontExt($_fonts['Helvetica-Bold'], 9);
                    }

                    $totalArray[$k] = (is_numeric($totalArray[$k])) ? formatting($totalArray[$k], 2) : $totalArray[$k];
                    $pdf->showBoxed($k == 0 ? $totalArray[$k] : trim($totalArray[$k]), ($xAxis) + 90, $yAxis, 90, 95, 'right', ''); // --> for total pdf view
                    $pdf->setFontExt($_fonts['Helvetica'], 9);
                }
            }

            $yAxis -= 25;
        }

    }

    $xAxis += 90;
}


// total line
$pdf->moveto(25, 643 - $line);
$pdf->lineto(570, 643 - $line);
$pdf->stroke();

// bottom
$pdf->moveto(25, 623 - $line);
$pdf->lineto(570, 623 - $line);
$pdf->stroke();

// right side bar
$pdf->moveto(570, 675);
$pdf->lineto(570, 623 - $line);
$pdf->stroke();

// left side bar
$pdf->moveto(25, 675);
$pdf->lineto(25, 623 - $line);
$pdf->stroke();

$pdf->setFontExt($_fonts['Helvetica'], 8);
$pdf->showBoxed("Page {$page}", 520 + 9, 2, 75, 30, 'left', '');

$traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'fundSummary', A4_PORTRAIT);
$traccFooter->prerender($pdf);

if ($logo) {
    generateLogo();
}

$pdf->end_page_ext('');

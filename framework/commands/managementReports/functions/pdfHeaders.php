<?php




function getDbLogoDetails()
{

    //	logData('logo!');
    //	if(is_array($_SESSION['dbList']))
    //	{
    //		$return = Array();
    //		$dbList = $_SESSION['dbList'];
    //		$currentDbName = $_SESSION['currentDB'];
    //		foreach ($dbList as $id=> $row)
    //		{
    //			if ($dbList[$id]['database_name'] == $currentDbName)
    //			{
    $return['DbId'] = dbGetClientID();
    $return['logoName'] =  dbGetClientLogo();

    //			}
    //		}
    //		//$old_client_id = $_SESSION['old_client_id'];
    //		logData($return);
    return $return;
    //	}
    //	else
    //	{
    //		return false;
    //	}
}



// echo "<hr /><b>DB NAME : $currentDbName -- DB ID: $currentDbId -- OLD ID : $old_client_id  -  LOGO URL : $currentLogoName </b><hr />";


function getLogoCoordinatesPortrait($currentDbId)
{

    // defaults
    // $xCoord = 405; $yCoord = 765; $scale = 0.2;
    $imageType = 'jpeg';

    $imageParameters = [];

    if ($currentDbId == '567') {
        $xCoord = 450;
        $yCoord = 798;
        $scale = 0.5;
    }

    if ($currentDbId == '999') {
        $xCoord = 410;
        $yCoord = 780;
        $scale = 0.15;
    }

    if ($currentDbId == '998') {
        $xCoord = 450;
        $yCoord = 783;
        $scale = 0.5;
    }

    if ($currentDbId == '996') {
        $xCoord = 450;
        $yCoord = 798;
        $scale = 0.5;
    }

    if ($currentDbId == '1') {
        $xCoord = 450;
        $yCoord = 798;
        $scale = 0.5;
    }

    if ($currentDbId == '790') {
        $xCoord = 450;
        $yCoord = 798;
        $scale = 0.5;
    }

    if ($currentDbId == '777') {
        $xCoord = 450;
        $yCoord = 798;
        $scale = 0.5;
    }

    if ($currentDbId == '452') {
        $xCoord = 450;
        $yCoord = 769;
        $scale = 0.5;
    }

    if ($currentDbId == '111') {
        $xCoord = 430;
        $yCoord = 759;
        $scale = 0.9;
    }

    if ($currentDbId == '789') {
        $xCoord = 450;
        $yCoord = 779;
        $scale = 0.5;
    }

    if ($currentDbId == '333') {
        $xCoord = 450;
        $yCoord = 769;
        $scale = 0.5;
    }

    if ($currentDbId == '212') {
        $xCoord = 450;
        $yCoord = 769;
        $scale = 0.5;
    }

    if ($currentDbId == '555') {
        $xCoord = 450;
        $yCoord = 769;
        $scale = 0.5;
    }

    $imageParameters['xCoord'] = $xCoord;
    $imageParameters['yCoord'] = $yCoord;
    $imageParameters['scale'] = $scale;
    $imageParameters['imageType'] = $imageType;

    return $imageParameters;

}

function getLogoCoordinatesLandscape($currentDbId)
{

    // defaults
    // $xCoord = 405; $yCoord = 765; $scale = 0.2;
    $imageType = 'jpeg';

    $imageParameters = [];

    // note these copied from portrait function and may be inaccurate make sure you check these individualy when adding a client

    if ($currentDbId == '567') {
        $xCoord = 798;
        $yCoord = 450;
        $scale = 0.5;
    }

    if ($currentDbId == '999') {
        $xCoord = 775;
        $yCoord = 410;
        $scale = 0.15;
    }

    if ($currentDbId == '998') {
        $xCoord = 783;
        $yCoord = 450;
        $scale = 0.5;
    }

    if ($currentDbId == '996') {
        $xCoord = 798;
        $yCoord = 450;
        $scale = 0.5;
    }

    if ($currentDbId == '1') {
        $xCoord = 798;
        $yCoord = 450;
        $scale = 0.5;
    }

    if ($currentDbId == '790') {
        $xCoord = 798;
        $yCoord = 450;
        $scale = 0.5;
    }

    if ($currentDbId == '777') {
        $xCoord = 798;
        $yCoord = 450;
        $scale = 0.5;
    }

    if ($currentDbId == '452') {
        $xCoord = 769;
        $yCoord = 450;
        $scale = 0.5;
    }

    if ($currentDbId == '111') {
        $xCoord = 769;
        $yCoord = 450;
        $scale = 0.5;
    }

    if ($currentDbId == '789') {
        $xCoord = 779;
        $yCoord = 450;
        $scale = 0.5;
    }

    if ($currentDbId == '333') {
        $xCoord = 769;
        $yCoord = 450;
        $scale = 0.5;
    }

    if ($currentDbId == '212') {
        $xCoord = 769;
        $yCoord = 450;
        $scale = 0.5;
    }

    if ($currentDbId == '555') {
        $xCoord = 769;
        $yCoord = 450;
        $scale = 0.5;
    }

    $imageParameters['xCoord'] = $xCoord;
    $imageParameters['yCoord'] = $yCoord;
    $imageParameters['scale'] = $scale;
    $imageParameters['imageType'] = $imageType;

    return $imageParameters;

}

function generateLogo($page = 'portrait', $heightOverwrite = null, $LOGO_WIDTH = null, $LOGO_HEIGHT = null)
{
    global $pdf;

    $pageWidth = 595;
    $pageHeight = 842;
    if ($page == 'landscape') {
        $pageWidth = 842;
        $pageHeight = 595;
    }

    // echo "103 <br />";
    if ($dbLogoDetails = getDbLogoDetails()) {
        // echo "106 <br />";
        $logoName = $dbLogoDetails['logoName'];
        $logoFile = realpath("assets/clientLogos/{$logoName}");
        if (! file_exists($logoFile)) {
            return;
        }


        $maxWidth = $LOGO_WIDTH ? $LOGO_WIDTH : LOGO_WIDTH;
        $maxHeight = $LOGO_HEIGHT ? $LOGO_HEIGHT : LOGO_HEIGHT;
        [$imageWidth, $imageHeight] = getimagesize($logoFile);

        $horizontalScaling = ($imageWidth > $maxWidth) ? $maxWidth / $imageWidth : 1;
        $verticalScaling = ($imageHeight > $maxHeight) ? $maxHeight / $imageHeight : 1;
        $imageScale = ($horizontalScaling < $verticalScaling) ? $horizontalScaling : $verticalScaling;

        $imageScale = round($imageScale, 2);

        $vMargin = 25;
        $hMargin = 25;

        $hPos = $pageWidth - ($imageScale * $imageWidth) - $hMargin;
        $vPos = $pageHeight - ($imageScale * $imageHeight) - $vMargin;

        $pdfimage = $pdf->load_image('auto', $logoFile, '');
        $pdf->fit_image($pdfimage, $heightOverwrite ? $heightOverwrite : $hPos, $vPos, 'boxsize {' . "{$maxWidth} {$maxHeight}" . '} fitmethod meet');

        //	     $maxWidth = LOGO_WIDTH;
        //            $maxHeight = LOGO_HEIGHT;
        //            list($imageWidth, $imageHeight) = getimagesize($logoFile);
        //
        //            $horizontalScaling = ($imageWidth > $maxWidth) ? $maxWidth/$imageWidth : 1;
        //            $verticalScaling = ($imageHeight > $maxHeight) ? $maxHeight/$imageHeight : 1;
        //
        //
        //            $imageScale = ($horizontalScaling < $verticalScaling) ? $horizontalScaling : $verticalScaling;
        //
        //
        //	    //$imageScale = round($imageScale,2);
        //
        //            $vMargin = 25;
        //            $hMargin = 25;
        //
        //	    $hPos = $pageWidth-($imageScale*$imageWidth)-$hMargin;
        //	    $vPos = $pageHeight-($imageScale*$imageHeight)-$vMargin;
        //
        //            $pdfimage = $pdf->load_image("auto", $logoFile,"");
        //
        //	   // echo (int) $pdfimage;
        //
        //            $pdf->fit_image($pdfimage, $hPos,$vPos, "scale {$imageScale}");
        //            $pdf->close_image ($pdfimage);
    }
}

function short_header($header_name)
{
    global $pdf;
    global $dbh;
    global $logo;
    global $periodFrom;
    global $periodTo;
    global $description;
    global $periodDescription; // added this in 2 Feb 09
    global $propertyName;
    global $client;
    global $client_street;
    global $client_city;
    global $client_state;
    global $client_postcode;
    global $_fonts;

    // Zern Coordinates for logo
    global $xCoord_zern;
    global $yCoord_zern;
    global $scale_zern;

    global $currentDbName;
    global $line;
    global $page;

    if ($page == 0) {
        $page = 1;
    }

    $pdf->setFontExt($_fonts['Helvetica-Bold'], 9);
    $pdf->showBoxed(ucwords(strtolower($header_name)), 100, 690, 400, 20, 'center', '');
    $pdf->setFontExt($_fonts['Helvetica'], 9);
    // $pdf->show_xy(substr("$client",0,50), 105, 750);
    $keywords = explode("\n", $client_street);
    foreach ($keywords as $street_line) {
        //	$pdf->continue_text($street_line);
    }

    //	$pdf->continue_text("$client_city $client_state $client_postcode");
    $pdf->setFontExt($_fonts['Helvetica'], 8);
    $pdf->show_xy("Period From: {$periodFrom} To: {$periodTo}", 230, 685);




    $text1 = "Month\n" . $_SESSION['country_default']['currency_symbol'];
    $text2 = "YTD\n" . $_SESSION['country_default']['currency_symbol'];
    $pdf->setFontExt($_fonts['Helvetica-Bold'], 9);
    $pdf->showBoxed($text1, 330, 640, 35, 30, 'center', '');
    $pdf->showBoxed($text2, 480, 640, 35, 30, 'center', '');
    $pdf->setFontExt($_fonts['Helvetica'], 8);

    $officeSQL = 'SELECT * FROM pmol_office_loc';
    $dbh->executeSingle($officeSQL);

    /*
            $officeSQL_result = mssql_query($officeSQL);
            $office_name = $officeSQL_result["pmol_name"];
            $office_street = $officeSQL_result["pmol_street"];
            $office_city = $officeSQL_result["pmol_city"];
            $office_state = $officeSQL_result["pmol_state"];
            $office_postcode = $officeSQL_result["pmol_postcode"];
            $office_phone = $officeSQL_result["pmol_phone"];
            $office_fax = $officeSQL_result["pmol_fax"];
            $office_notes = $officeSQL_result["pmol_notes"];
    */




    //	$pdf->setFontExt($_fonts["Helvetica"], 9);
    //	$pdf->show_xy("Property $propertyName", 340,750);
    //	$pdf->continue_text("Month of: $periodDescription");//changed from $description 2 Feb 09

    //    $line = $line - 40;


    if ($logo) {
        generateLogo();
    }

}



function page_headerLS($cont, $propertyId = null)
{

    global $pdf;
    global $propertyName;
    global $line;
    global $periodDescription;
    global $filenameDescription;
    global $client;
    global $page;
    global $periodFrom;
    global $periodTo;
    global $date;
    global $logo;
    global $reporttype;
    global $_fonts;

    // Zern Coordinates for logo
    global $xCoord_zern; // echo "form short_header >> $xCoord_zern";
    global $yCoord_zern;
    global $scale_zern;

    $reporttype = $_REQUEST['reporttype'];

    $page_name = ($reporttype == '8') ? 'Receipts And ' . ucwords(strtolower($_SESSION['country_default']['strata'])) . ' Owner Charges' : 'Tenant Charges & Receipts';
    if ($cont == 'cont') {
        $page_name .= ' (...)';
    }

    // $page_name = "CASH RECEIPTS & TENANT CHARGES";

    $page++;

    $line -= 40;
    $pdf->setFontExt($_fonts['Helvetica-Bold'], 9);
    $pdf->showBoxed("{$page_name}", 100, 690 - $line, 400, 20, 'center', '');
    $pdf->setFontExt($_fonts['Helvetica'], 8);

    $pdf->show_xy('Owner: ', 25, 750 - $line);
    $pdf->continue_text('Property: ');
    $pdf->continue_text('Report for: ');

    $pdf->show_xy("{$client}", 85, 750 - $line);
    $propertyId ? $pdf->continue_text("{$propertyName} [{$propertyId}]") : $pdf->continue_text("{$propertyName}");
    $pdf->continue_text("{$periodDescription}");

    $pdf->showBoxed("Period From: {$periodFrom} To: {$periodTo}", 100, 675 - $line, 400, 20, 'center', '');
    // $pdf->show_xy("Period From: $periodFrom_display To: $periodTo_display", 220, 675-$line);


    // #################LOGO#############################
    if ($logo) {
        generateLogo();
    }

    // #####################LOGO###############################
    $line += 40;

    $pdf->setlinewidth(0.5);
    // top header line
    $pdf->moveto(25, 715 - $line);
    $pdf->lineto(570, 715 - $line);
    $pdf->stroke();



    $pdf->setFontExt($_fonts['Helvetica-Bold'], 8);

    $text1 = ($reporttype == 8) ? ucwords(strtolower($_SESSION['country_default']['strata'])) . ' Owner Name' : 'Tenant Name';
    $text3 = "Opening\nBalance\n\n" . $_SESSION['country_default']['currency_symbol'];
    $text5 = "\n\nNet\n" . $_SESSION['country_default']['currency_symbol'];
    $text6 = "\n\n" . $_SESSION['country_default']['tax_label'] . "\n" . $_SESSION['country_default']['currency_symbol'];
    $text8 = "\n\nNet\n" . $_SESSION['country_default']['currency_symbol'];
    $text9 = "\n\n" . $_SESSION['country_default']['tax_label'] . "\n" . $_SESSION['country_default']['currency_symbol'];
    $text10 = "Closing\nBalance\n\n" . $_SESSION['country_default']['currency_symbol'];
    $text11 = 'Billed';
    $text12 = 'Received';


    $pdf->showBoxed($text1, 50, 685 - $line, 175, 30, 'left', '');
    $pdf->showBoxed($text3, 235, 675 - $line, 75, 40, 'center', '');
    // $pdf->showBoxed ($text4, 325, 675, 75, 40, "center", "");
    $pdf->showBoxed($text5, 295, 675 - $line, 75, 40, 'center', '');
    $pdf->showBoxed($text6, 340, 675 - $line, 75, 40, 'center', '');
    // $pdf->showBoxed ($text7, 550, 675, 75, 40, "center", "");
    $pdf->showBoxed($text8, 395, 675 - $line, 75, 40, 'center', '');
    $pdf->showBoxed($text9, 445, 675 - $line, 75, 40, 'center', '');
    $pdf->showBoxed($text10, 500, 675 - $line, 75, 40, 'center', '');
    $pdf->showBoxed($text11, 315, 675 - $line, 75, 40, 'center', '');
    $pdf->showBoxed($text12, 415, 675 - $line, 75, 40, 'center', '');
    $pdf->setFontExt($_fonts['Helvetica'], 9);

    // top line
    $pdf->moveto(25, 675 - $line);
    $pdf->lineto(570, 675 - $line);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    // top middle line
    $pdf->moveto(300, 700 - $line);
    $pdf->lineto(500, 700 - $line);
    $pdf->stroke();
    $pdf->setlinewidth(1);


    $pdf->setFontExt($_fonts['Helvetica'], 7);
    // $pdf->showBoxed ("Printed on $date", 30+9, 5, 275, 30, "left", "");
    $pdf->showBoxed("Page {$page}", 470 + 9, 5, 75, 30, 'right', '');

    $line = 20;
}

function page_DetailedCashSummary($cont, $propertyId = null, $moveUp = 650)
{
    global $reportDescription;
    global $pdf;
    global $propertyName;
    global $line;
    global $periodDescription;
    global $filenameDescription;
    global $client;
    global $page;
    global $periodFrom;
    global $periodTo;
    global $date;
    global $logo;
    global $reporttype;
    global $_fonts;

    // Zern Coordinates for logo
    global $xCoord_zern; // echo "form short_header >> $xCoord_zern";
    global $yCoord_zern;
    global $scale_zern;

    $page_name =  $reportDescription;
    if ($cont == 'cont') {
        $page_name .= ' (...)';
    }

    // $page_name = "CASH RECEIPTS & TENANT CHARGES";

    $page++;

    $line -= 40;
    $copyLine = $line - 15;
    $pdf->setFontExt($_fonts['Helvetica-Bold'], 9);
    $pdf->showBoxed("{$page_name}", 25, 730 - $copyLine, 545, 20, 'center', '');
    $pdf->setFontExt($_fonts['Helvetica-Bold'], 8);

    $pdf->show_xy('Property: ', 27, 720 - $copyLine);
    $pdf->continue_text('Report Period: ');

    $propertyId ? $pdf->show_xy("{$propertyName} [{$propertyId}]", 85, 720 - $copyLine) : $pdf->show_xy("{$propertyName}", 85, 720 - $copyLine);
    $pdf->continue_text("{$periodFrom} - {$periodTo}");

    $prop_result = getAgentOwnerCodes($propertyId);
    $owner_code = $prop_result['owner_code'];
    $ownerDetails = getCompAddress($owner_code);

    $owner_name = $ownerDetails['company_name'];
    $owner_street = $ownerDetails['street'];
    $owner_city = $ownerDetails['city'];
    $owner_state = $ownerDetails['state'];
    $owner_postcode = $ownerDetails['postcode'];
    $owner_streets = explode("\n", $owner_street);

    $pdf->showBoxed($owner_name, 27, 690 - $copyLine, 590, 10, 'left', '');
    $copyLine += 10;
    foreach ($owner_streets as $ostreet) {
        $pdf->showBoxed($ostreet, 27, 690 - $copyLine, 590, 10, 'left', '');
        $copyLine += 10;
    }

    $pdf->showBoxed("{$owner_city}, {$owner_state} {$owner_postcode}", 27, 690 - $copyLine, 590, 10, 'left', '');


    // #################LOGO#############################
    if ($logo) {
        generateLogo('portrait', 25);
    }

    $agentData = dbGetAgentDetails();
    $agentDetailsNew = new agentDetails($propertyId, true, 600, $moveUp);
    $agentDetailsNew->bindAttributesFrom($agentData);
    $agentDetailsNew->render($pdf);

    // #####################LOGO###############################
    $line += 60;

    $pdf->setlinewidth(0.5);
    // top header line
    $pdf->moveto(25, 715 - $line);
    $pdf->lineto(570, 715 - $line);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    // top header line
    $pdf->moveto(25, 730 - $line);
    $pdf->lineto(570, 730 - $line);
    $pdf->stroke();

    $pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
    $pdf->showBoxed('TENANT CHARGES & RECEIPTS', 25, 708 - $line, 545, 20, 'center', '');

    $pdf->setFontExt($_fonts['Helvetica-Bold'], 8);

    $text1 = ($reporttype == 8) ? ucwords(strtolower($_SESSION['country_default']['strata'])) . ' Owner Name' : 'Tenant Name';
    $text3 = "Opening\nBalance\n\n" . $_SESSION['country_default']['currency_symbol'];
    $text5 = "\n\nNet\n" . $_SESSION['country_default']['currency_symbol'];
    $text6 = "\n\n" . $_SESSION['country_default']['tax_label'] . "\n" . $_SESSION['country_default']['currency_symbol'];
    $text8 = "\n\nNet\n" . $_SESSION['country_default']['currency_symbol'];
    $text9 = "\n\n" . $_SESSION['country_default']['tax_label'] . "\n" . $_SESSION['country_default']['currency_symbol'];
    $text10 = "Closing\nBalance\n\n" . $_SESSION['country_default']['currency_symbol'];
    $text11 = 'Billed';
    $text12 = 'Received';
    $text13 = "\n\nTotal\n" . $_SESSION['country_default']['currency_symbol'];

    $pdf->showBoxed($text1, 50, 685 - $line, 175, 30, 'left', '');
    $pdf->showBoxed($text3, 220, 675 - $line, 50, 40, 'center', '');
    $pdf->showBoxed($text5, 270, 675 - $line, 50, 40, 'center', '');
    $pdf->showBoxed($text6, 320, 675 - $line, 50, 40, 'center', '');
    $pdf->showBoxed($text8, 370, 675 - $line, 50, 40, 'center', '');
    $pdf->showBoxed($text9, 420, 675 - $line, 50, 40, 'center', '');
    $pdf->showBoxed($text13, 470, 675 - $line, 50, 40, 'center', '');
    $pdf->showBoxed($text10, 520, 675 - $line, 50, 40, 'center', '');
    $pdf->showBoxed($text11, 270, 675 - $line, 100, 40, 'center', '');
    $pdf->showBoxed($text12, 370, 675 - $line, 150, 40, 'center', '');
    $pdf->setFontExt($_fonts['Helvetica'], 9);

    // top line
    $pdf->moveto(25, 675 - $line);
    $pdf->lineto(570, 675 - $line);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    // top middle line
    $pdf->moveto(270, 700 - $line);
    $pdf->lineto(520, 700 - $line);
    $pdf->stroke();
    $pdf->setlinewidth(1);


    $pdf->setFontExt($_fonts['Helvetica'], 7);
    // $pdf->showBoxed ("Printed on $date", 30+9, 5, 275, 30, "left", "");
    $pdf->showBoxed("Page {$page}", 470 + 9, 5, 75, 30, 'right', '');

    $line = 20;
}

function page_headerSummary($cont, $propertyId = null, $moveUp = 650)
{
    global $reportDescription;
    global $pdf;
    global $propertyName;
    global $line;
    global $periodDescription;
    global $filenameDescription;
    global $client;
    global $page;
    global $periodFrom;
    global $periodTo;
    global $date;
    global $logo;
    global $reporttype;
    global $_fonts;

    // Zern Coordinates for logo
    global $xCoord_zern; // echo "form short_header >> $xCoord_zern";
    global $yCoord_zern;
    global $scale_zern;

    $page_name =  $reportDescription;
    if ($cont == 'cont') {
        $page_name .= ' (...)';
    }

    // $page_name = "CASH RECEIPTS & TENANT CHARGES";

    $page++;

    $line -= 40;
    $pdf->setFontExt($_fonts['Helvetica-Bold'], 9);
    $pdf->showBoxed("{$page_name}", 25, 730 - $line, 545, 20, 'center', '');
    $pdf->setFontExt($_fonts['Helvetica-Bold'], 8);

    $pdf->show_xy('Owner: ', 27, 720 - $line);
    $pdf->continue_text('Property: ');
    $pdf->continue_text('Report Period: ');

    $pdf->show_xy("{$client}", 85, 720 - $line);
    $propertyId ? $pdf->continue_text("{$propertyName} [{$propertyId}]") : $pdf->continue_text("{$propertyName}");
    $pdf->continue_text("{$periodFrom} - {$periodTo}");


    // #################LOGO#############################
    if ($logo) {
        generateLogo('portrait', 25);
    }

    $agentData = dbGetAgentDetails();
    $agentDetailsNew = new agentDetails($propertyId, true, 600, $moveUp);
    $agentDetailsNew->bindAttributesFrom($agentData);
    $agentDetailsNew->render($pdf);

    // #####################LOGO###############################
    $line += 60;

    $pdf->setlinewidth(0.5);
    // top header line
    $pdf->moveto(25, 715 - $line);
    $pdf->lineto(570, 715 - $line);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    // top header line
    $pdf->moveto(25, 730 - $line);
    $pdf->lineto(570, 730 - $line);
    $pdf->stroke();

    $pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
    $pdf->showBoxed('TENANT CHARGES & RECEIPTS', 25, 708 - $line, 545, 20, 'center', '');

    $pdf->setFontExt($_fonts['Helvetica-Bold'], 8);

    $text1 = ($reporttype == 8) ? ucwords(strtolower($_SESSION['country_default']['strata'])) . ' Owner Name' : 'Tenant Name';
    $text3 = "Opening\nBalance\n\n" . $_SESSION['country_default']['currency_symbol'];
    $text5 = "\n\nNet\n" . $_SESSION['country_default']['currency_symbol'];
    $text6 = "\n\n" . $_SESSION['country_default']['tax_label'] . "\n" . $_SESSION['country_default']['currency_symbol'];
    $text8 = "\n\nNet\n" . $_SESSION['country_default']['currency_symbol'];
    $text9 = "\n\n" . $_SESSION['country_default']['tax_label'] . "\n" . $_SESSION['country_default']['currency_symbol'];
    $text10 = "Closing\nBalance\n\n" . $_SESSION['country_default']['currency_symbol'];
    $text11 = 'Billed';
    $text12 = 'Received';
    $text13 = "\n\nTotal\n" . $_SESSION['country_default']['currency_symbol'];

    $pdf->showBoxed($text1, 50, 685 - $line, 175, 30, 'left', '');
    $pdf->showBoxed($text3, 220, 675 - $line, 50, 40, 'center', '');
    $pdf->showBoxed($text5, 270, 675 - $line, 50, 40, 'center', '');
    $pdf->showBoxed($text6, 320, 675 - $line, 50, 40, 'center', '');
    $pdf->showBoxed($text8, 370, 675 - $line, 50, 40, 'center', '');
    $pdf->showBoxed($text9, 420, 675 - $line, 50, 40, 'center', '');
    $pdf->showBoxed($text13, 470, 675 - $line, 50, 40, 'center', '');
    $pdf->showBoxed($text10, 520, 675 - $line, 50, 40, 'center', '');
    $pdf->showBoxed($text11, 270, 675 - $line, 100, 40, 'center', '');
    $pdf->showBoxed($text12, 370, 675 - $line, 150, 40, 'center', '');
    $pdf->setFontExt($_fonts['Helvetica'], 9);

    // top line
    $pdf->moveto(25, 675 - $line);
    $pdf->lineto(570, 675 - $line);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    // top middle line
    $pdf->moveto(270, 700 - $line);
    $pdf->lineto(520, 700 - $line);
    $pdf->stroke();
    $pdf->setlinewidth(1);


    $pdf->setFontExt($_fonts['Helvetica'], 7);
    // $pdf->showBoxed ("Printed on $date", 30+9, 5, 275, 30, "left", "");
    $pdf->showBoxed("Page {$page}", 470 + 9, 5, 75, 30, 'right', '');

    $line = 20;
}

function page_payment($cont, $propertyId = null, $moveUp = 650)
{
    global $reportDescription;
    global $pdf;
    global $propertyName;
    global $line;
    global $periodDescription;
    global $filenameDescription;
    global $client;
    global $page;
    global $periodFrom;
    global $periodTo;
    global $date;
    global $logo;
    global $reporttype;
    global $_fonts;

    // Zern Coordinates for logo
    global $xCoord_zern; // echo "form short_header >> $xCoord_zern";
    global $yCoord_zern;
    global $scale_zern;

    $traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'cirrus8CashSummary', A4_PORTRAIT);
    $traccFooter->prerender($pdf);

    $pdf->end_page_ext('');

    $pdf->begin_page_ext(595, 842, '');

    $page_name =  $reportDescription;
    if ($cont == 'cont') {
        $page_name .= ' (...)';
    }

    // $page_name = "CASH RECEIPTS & TENANT CHARGES";

    $page++;

    $line = -285;
    $pdf->setFontExt($_fonts['Helvetica-Bold'], 9);
    $pdf->showBoxed("{$page_name}", 25, 445 - $line, 545, 20, 'center', '');
    $pdf->setFontExt($_fonts['Helvetica-Bold'], 8);

    $pdf->show_xy('Owner: ', 27, 435 - $line);
    $pdf->continue_text('Property: ');
    $pdf->continue_text('Report Period: ');

    $pdf->show_xy("{$client}", 85, 435 - $line);
    $propertyId ? $pdf->continue_text("{$propertyName} [{$propertyId}]") : $pdf->continue_text("{$propertyName}");
    $pdf->continue_text("{$periodFrom} - {$periodTo}");


    // #################LOGO#############################
    if ($logo) {
        generateLogo('portrait', 25);
    }



    $agentData = dbGetAgentDetails();
    $agentDetailsNew = new agentDetails($propertyId, true, 600, $moveUp);
    $agentDetailsNew->bindAttributesFrom($agentData);
    $agentDetailsNew->render($pdf);





    // #####################LOGO###############################
    $line += 40;


    $pdf->setFontExt($_fonts['Helvetica'], 7);
    // $pdf->showBoxed ("Printed on $date", 30+9, 5, 275, 30, "left", "");
    $pdf->showBoxed("Page {$page}", 470 + 9, 5, 75, 30, 'right', '');

}



// NOTE these page headers below not used currently

function short_headerLS($header_name)
{
    global $pdf;
    global $periodFrom_display;
    global $periodTo_display;
    global $description;
    global $propertyName;
    global $client;
    global $client_street;
    global $client_city;
    global $client_state;
    global $client_postcode;
    global $line;
    global $logo;
    global $_fonts;

    // Zern Coordinates for logo
    global $xCoord_zern; // echo "form short_header >> $xCoord_zern";
    global $yCoord_zern;
    global $scale_zern;

    $pdf->setFontExt($_fonts['Helvetica'], 12);
    $pdf->showBoxed("{$header_name}", 100, 720 - $line, 400, 20, 'center', '');
    $pdf->setFontExt($_fonts['Helvetica'], 10);

    $pdf->show_xy('Owner: ', 155, 800 - $line);
    $pdf->continue_text('Property: ');
    $pdf->continue_text('Report for: ');

    $pdf->show_xy("{$client}", 215, 800 - $line);
    $pdf->continue_text("{$propertyName}");
    $pdf->continue_text("{$description}");

    $pdf->showBoxed("Periods From: {$periodFrom_display} To: {$periodTo_display}", 100, 705 - $line, 400, 20, 'center', '');
    // $pdf->show_xy("Period From: $periodFrom_display To: $periodTo_display", 220, 710-$line);



    if ($_SESSION['clientID'] == '567' && $logo == '1') {

        $pdfimage = $pdf->load_image('auto', 'gwlogo.jpg');
        $pdf->fit_image($pdfimage, 450, 769, 0.5);

        $pdf->close_image($pdfimage);

    }




    if ($_SESSION['clientID'] == '1' && $logo == '1') {

        $pdfimage = $pdf->load_image('auto', 'nsclogo.jpg');
        $pdf->fit_image($pdfimage, 450, 769, 0.5);

        $pdf->close_image($pdfimage);

    }

    // new client FESA added 13 Dec 06

    if ($_SESSION['clientID'] == '999' && $logo == '1') {


        $pdfimage = $pdf->load_image('auto', 'FESA_gov_logo_web.jpg');
        $pdf->fit_image($pdfimage, 450, 769, 0.5);

        $pdf->close_image($pdfimage);

    }

    // end new client

    // New Client DENI added 11 Jan 07

    if ($_SESSION['clientID'] == '998' && $logo == '1') {


        $pdfimage = $pdf->load_image('auto', 'DENI_logo.jpg');
        $pdf->fit_image($pdfimage, 440, 783, 0.5);

        $pdf->close_image($pdfimage);

    }

    // end new client

    // New Client  ZERN Added on 8 -June- 2007

    if ($_SESSION['clientID'] == '996' && $logo == '1') {


        $pdfimage = $pdf->load_image('auto', 'zernike_logo.jpg');
        $pdf->fit_image($pdfimage, $xCoord_zern, $yCoord_zern, $scale_zern);

        $pdf->close_image($pdfimage);

    }

    // end new client

    if ($_SESSION['clientID'] == '790' && $logo == '1') {

        $pdfimage = $pdf->load_image('auto', 'AM_BW.jpg');
        $pdf->fit_image($pdfimage, 450, 769, 0.5);

        $pdf->close_image($pdfimage);

    }

    if ($_SESSION['clientID'] == '777' && $logo == '1') {

        $pdfimage = $pdf->load_image('auto', 'pprop.jpg');
        $pdf->fit_image($pdfimage, 450, 769, 0.5);

        $pdf->close_image($pdfimage);

    }


    if ($_SESSION['clientID'] == '452' && $logo == '1') {

        $pdfimage = $pdf->load_image('auto', 'fps.jpg');
        $pdf->fit_image($pdfimage, 450, 769, 0.5);

        $pdf->close_image($pdfimage);

    }


    if ($_SESSION['clientID'] == '789' && $logo == '1') {

        $pdfimage = $pdf->load_image('auto', 'lslogo.jpg');
        $pdf->fit_image($pdfimage, 450, 769, 0.5);

        $pdf->close_image($pdfimage);

    }

    if ($_SESSION['clientID'] == '333' && $logo == '1') {

        $pdfimage = $pdf->load_image('auto', 'lang.jpg');
        $pdf->fit_image($pdfimage, 450, 769, 0.5);

        $pdf->close_image($pdfimage);

    }

    if ($_SESSION['clientID'] == '212' && $logo == '1') {

        $pdfimage = $pdf->load_image('auto', 'vsa.jpg');
        $pdf->fit_image($pdfimage, 450, 769, 0.5);

        $pdf->close_image($pdfimage);

    }

    $text1 = "Period\n$";
    $text2 = 'YTD  $';

    $pdf->showBoxed($text1, 290, 625 - $line, 125, 30, 'center', '');
    $pdf->showBoxed($text2, 480, 625 - $line, 25, 30, 'center', '');



}

function generatePropertyImage($page = 'portrait', $xAxis = 75, $yLandscapeAxis = 480)
{
    global $pdf;
    global $pathPrefix;
    // global $clientDirectory;
    global $propertyID;
    //    $pageWidth = 595; $pageHeight = 842;
    //    if($page == 'landscape') { $pageWidth = 842; $pageHeight = 595; }
    $imageDetails = dbGetImage(IMG_PROP_COVER_PAGE, $propertyID);
    $logoFile = null;
    if ($imageDetails) {
        $logoFile = SYSTEMPATH . "/assets/images/property-image/{$imageDetails['img_filename']}"; // "{$pathPrefix}{$imageDetails["img_filename"]}";
    }

    if (! file_exists($logoFile)) {
        $logoFile = SYSTEMPATH . '/assets/images/icons/default-property-image.jpg';
    }

    if (! file_exists($logoFile)) {
        return;
    }

    $x = $xAxis;
    $y = 580;
    $yoffset = 490;
    if ($page != 'portrait') {
        $maxWidth = 700;
        $maxHeight = 350;
    } else {
        $maxWidth = 450;
        $maxHeight = 350;
        $x = $xAxis;
        $y = $yLandscapeAxis;
        $yoffset = 250;
    }

    $pdfimage = $pdf->load_image('auto', $logoFile, '');
    $pdf->fit_image($pdfimage, $x, $y -= $yoffset, 'boxsize {' . "{$maxWidth} {$maxHeight}" . '} position={center} fitmethod=meet');
}

function generateManagementReportImage($page, $x, $y, $maxWidth, $maxHeight, $file, $fitmethod = 'meet', $position = 'center')
{
    global $pdf;
    if (! file_exists($file)) {
        return;
    }

    $yoffset = 0;
    $pdfimage = $pdf->load_image('auto', $file, '');
    $pdf->fit_image($pdfimage, $x, $y -= $yoffset, 'boxsize {' . "{$maxWidth} {$maxHeight}" . "} position={{$position}} fitmethod={$fitmethod}");
}

<?php

// #########################Functions below are for the medium report only ##############################################

function getBudgetExpCurrent($propertyID, $periodFrom, $periodTo, $sub_group)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $sql = "
		SELECT
			COALESCE(SUM(pmep_b_c_amt), 0) AS amount
		FROM
			pmep_b_exp_per
		WHERE
			pmep_prop = ?
			AND pmep_per IN
			(
				SELECT
					pmcp_period
				FROM
					pmcp_prop_cal
				WHERE
					pmcp_start_dt >= CONVERT(datetime, ?, 103)
					AND pmcp_end_dt <= CONVERT(datetime, ?, 103)
					AND pmcp_prop = ?
			)
			AND pmep_year IN
			(
				SELECT
					pmcp_year
				FROM
					pmcp_prop_cal
				WHERE
					pmcp_start_dt >= CONVERT(datetime, ?, 103)
					AND pmcp_end_dt <= CONVERT(datetime, ?, 103)
					AND pmcp_prop = ?
			)
			AND pmep_exp_acc IN
			(
				SELECT
					pmcg_acc
				FROM
					pmcg_chart_grp
				WHERE
					pmcg_grp = 'TRACC2'
					AND pmcg_subgrp = ?
			)";
    $params = [$propertyID, $periodFrom, $periodTo, $propertyID, $periodFrom, $periodTo, $propertyID, $sub_group];

    return $dbh->executeScalar($sql, $params);
}

function getBudgetIncCurrent($propertyID, $periodFrom, $periodTo, $sub_group)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $sql = "
		SELECT
			COALESCE(SUM(pmrp_b_c_amt), 0) AS amount
		FROM
			pmrp_b_rev_per
		WHERE
			pmrp_prop = ?
			AND pmrp_per IN
			(
				SELECT
					pmcp_period
				FROM
					pmcp_prop_cal
				WHERE
					pmcp_start_dt >= CONVERT(datetime, ?, 103)
					AND pmcp_end_dt <= CONVERT(datetime, ?, 103)
					AND pmcp_prop = ?
			)
			AND pmrp_year IN
			(
				SELECT
					pmcp_year
				FROM
					pmcp_prop_cal
				WHERE
					pmcp_start_dt >= CONVERT(datetime, ?, 103)
					AND pmcp_end_dt <= CONVERT(datetime, ?, 103)
					AND pmcp_prop = ?
			)
			AND pmrp_acc IN
			(
				SELECT
					pmcg_acc
				FROM
					pmcg_chart_grp
				WHERE
					pmcg_grp = 'TRACC2'
					AND pmcg_subgrp = ?
			)";
    $params = [$propertyID, $periodFrom, $periodTo, $propertyID, $periodFrom, $periodTo, $propertyID, $sub_group];

    return $dbh->executeScalar($sql, $params);
}

function getBudgetIncYTD($propertyID, $periodFrom, $currentPeriod, $periodTo, $sub_group)
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);

    $sql = "SELECT COALESCE(SUM(pmrp_b_c_amt), 0) AS amount
		FROM pmrp_b_rev_per
		WHERE (pmrp_prop = ?)
		AND ((pmrp_per >= 1)
		AND (pmrp_year IN (SELECT pmcp_year
									FROM pmcp_prop_cal
									WHERE pmcp_start_dt = CONVERT(datetime, ?, 103) AND pmcp_prop = ?)))
		AND ((pmrp_per <= ?)
				  AND (pmrp_year IN (SELECT pmcp_year
									FROM pmcp_prop_cal
									WHERE pmcp_end_dt = CONVERT(datetime, ?, 103) AND pmcp_prop = ?)))
				AND (pmrp_acc IN (SELECT pmcg_acc
									FROM pmcg_chart_grp
									WHERE (pmcg_grp = 'TRACC2')
									AND (pmcg_subgrp = ?)))";
    $params = [$propertyID, $periodFrom, $propertyID, $currentPeriod, $periodTo, $propertyID, $sub_group];

    return $dbh->executeScalar($sql, $params);
    // return 0;
}

function getBudgetExpYTD($propertyID, $currentPeriod, $periodTo, $sub_group)
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);

    $sql = "SELECT COALESCE(SUM(pmep_b_c_amt), 0) AS amount
		FROM pmep_b_exp_per
		WHERE (pmep_prop = ?)
		AND (pmep_per >= 1)
		AND (pmep_per <= ?)
				  AND (pmep_year IN (SELECT pmcp_year
									FROM pmcp_prop_cal
									WHERE pmcp_end_dt = CONVERT(datetime, ?, 103) AND pmcp_prop = ?))
				AND (pmep_exp_acc IN (SELECT pmcg_acc
									FROM pmcg_chart_grp
									WHERE (pmcg_grp = 'TRACC2')
									AND (pmcg_subgrp = ?)))";
    $params = [$propertyID, $currentPeriod, $periodTo, $propertyID, $sub_group];

    return $dbh->executeScalar($sql, $params);
}

function getYearBudgetExp($propertyID, $periodTo, $sub_group)
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);

    $sql = "SELECT COALESCE(SUM(pmep_b_c_amt), 0) AS amount
		FROM pmep_b_exp_per
		WHERE (pmep_prop = ?)
		AND (pmep_per BETWEEN 1 AND 12)AND (pmep_year IN (SELECT pmcp_year
									FROM pmcp_prop_cal
									WHERE pmcp_end_dt = CONVERT(datetime, ?, 103) AND pmcp_prop = ?))
				AND (pmep_exp_acc IN (SELECT pmcg_acc
									FROM pmcg_chart_grp
									WHERE (pmcg_grp = 'TRACC2')
									AND (pmcg_subgrp = ?)))";

    $params = [$propertyID, $periodTo, $propertyID, $sub_group];

    return $dbh->executeScalar($sql, $params);
}

function getYearBudgetInc($propertyID, $periodTo, $sub_group)
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);

    $sql = "SELECT COALESCE(SUM(pmrp_b_c_amt), 0) AS amount
			FROM pmrp_b_rev_per
			WHERE (pmrp_prop = ?)
			AND (pmrp_per BETWEEN 1 AND 12)AND (pmrp_year IN (SELECT pmcp_year
									FROM pmcp_prop_cal
									WHERE pmcp_end_dt = CONVERT(datetime, ?, 103) AND pmcp_prop = ?))
				AND (pmrp_acc IN (SELECT pmcg_acc
									FROM pmcg_chart_grp
									WHERE (pmcg_grp = 'TRACC2')
									AND (pmcg_subgrp = ?)))";

    $params = [$propertyID, $periodTo, $propertyID, $sub_group];

    return $dbh->executeScalar($sql, $params);
}

// ###################################Functions ABOVE are for the medium report only###################################################

// below are for all

function getPaymentsPerAccount($propertyID, $startDate, $endDate, $ignoreOwnersAccount = false)
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);

    $ignoreSQL = ($ignoreOwnersAccount) ? ' AND pmca_code != ' . dbGetParam('OWNREACC', 'ACCTCODE') : '';
    $paymentQuery = "
			SELECT pmca_code, pmca_name, COALESCE(SUM(pmxc_alloc_amt), 0) AS pmxc_alloc_amt, COALESCE(SUM(pmxc_tax_amt), 0) as pmxc_tax_amt
			FROM  pmxc_ap_alloc, pmca_chart
			WHERE (pmxc_acc = pmca_code)AND (pmxc_prop = ?) 			
			AND (pmxc_f_type = 'PAY') 
			AND (pmxc_alloc_dt BETWEEN CONVERT(datetime, ?, 103) AND CONVERT(datetime, ?, 103))
			{$ignoreSQL}
						GROUP BY pmca_code, pmca_name
						ORDER BY pmca_code"; // echo "$paymentQuery <br />";

    $params = [$propertyID, $startDate, $endDate];

    return $dbh->executeSet($paymentQuery, false, true, $params);
}

function getPaymentsToOwners($propertyID, $startDate, $endDate)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $paymentQuery = "
		SELECT
			COALESCE(SUM(pmxc_alloc_amt),0) AS AMT
		FROM 
			pmxc_ap_alloc
		WHERE
			pmxc_prop = ? 
			AND pmxc_alloc_dt BETWEEN CONVERT(datetime, ?, 103) AND CONVERT(datetime, ?, 103)
			AND pmxc_acc IN
			(
				SELECT
					pmcg_acc
				FROM
					pmcg_chart_grp
				WHERE pmcg_grp = 'TRACC3' and pmcg_subgrp IN ('EXPOWNREMI','BSPMTREMI')
			)
			AND pmxc_f_type = 'PAY'";
    $params = [$propertyID, $startDate, $endDate];

    return $dbh->executeScalar($paymentQuery, $params);
}

function getSimpleSubledgerPaymentsToOwners($propertyID, $leaseID, $startDate, $endDate)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $paymentQuery = "
		SELECT
			COALESCE(SUM(pmxc_alloc_amt),0) AS AMT
		FROM 
			pmxc_ap_alloc
		WHERE
			pmxc_prop = ? 
			AND pmxc_lease = ?
			AND pmxc_alloc_dt BETWEEN CONVERT(datetime, ?, 103) AND CONVERT(datetime, ?, 103)
			AND pmxc_acc IN
			(
				SELECT
					pmcg_acc
				FROM
					pmcg_chart_grp
				WHERE pmcg_grp = 'TRACC3' and pmcg_subgrp IN ('EXPOWNREMI','BSPMTREMI')
			)
			AND pmxc_f_type = 'PAY'";
    $params = [$propertyID, $leaseID, $startDate, $endDate];

    return $dbh->executeScalar($paymentQuery, $params);
}

function getPaymentsToOwnersBSonly($propertyID, $startDate, $endDate)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $paymentQuery = "
		SELECT
			COALESCE(SUM(pmxc_alloc_amt), 0) AS AMT
		FROM 
			pmxc_ap_alloc
		WHERE
			pmxc_prop = ? 
			AND pmxc_alloc_dt BETWEEN CONVERT(datetime, ?, 103) AND CONVERT(datetime, ?, 103)
			AND pmxc_acc IN
			(
				SELECT
					pmcg_acc
				FROM
					pmcg_chart_grp
				WHERE pmcg_grp = 'TRACC3' and pmcg_subgrp IN ('BSPMTREMI')
			)
			AND pmxc_f_type = 'PAY'";
    $params = [$propertyID, $startDate, $endDate];

    return $dbh->executeScalar($paymentQuery, $params);
}

function getCreditorDetails($propertyID, $periodFrom, $periodTo, $companyCode = null)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $params = [];
    $query = '
		SELECT
			pmxc_f_batch,
			pmxc_f_line,
			pmxc_t_batch,
			pmxc_t_line,
			COALESCE(SUM(pmxc_alloc_amt), 0) as pmxc_alloc_amt,
			pmxc_s_creditor as pmxc_s_creditor,
			MAX(CONVERT(char(10), pmxc_alloc_dt, 103)) as pmxc_alloc_dt,
			pmco_name as pmco_name,
			ap_transaction.description as description			
		FROM
			pmxc_ap_alloc,
			pmco_company,
			ap_transaction
		WHERE
			pmxc_prop = ' . addSQLParam($params, $propertyID) . '
			AND pmxc_alloc_dt BETWEEN CONVERT(datetime, ' . addSQLParam($params, $periodFrom) . ', 103) 
			AND CONVERT(datetime, ' . addSQLParam($params, $periodTo) . ", 103)
			AND pmxc_acc IN
			(
				SELECT
					pmcg_acc
				FROM
					pmcg_chart_grp
				WHERE pmcg_grp = 'TRACC3' and pmcg_subgrp IN ('EXPOWNREMI','BSPMTREMI')
				
				)
			AND pmxc_f_type = 'PAY'
			AND pmco_code = pmxc_s_creditor
			AND ap_transaction.batch_nr = pmxc_ap_alloc.pmxc_t_batch
			AND ap_transaction.batch_line_nr = pmxc_ap_alloc.pmxc_t_line " .
        ($companyCode ? ' AND pmco_code = ' . addSQLParam($params, $companyCode) : '') . '
		GROUP BY pmxc_f_batch,pmxc_f_line, pmxc_t_batch, pmxc_t_line, pmxc_s_creditor, pmco_name, pmco_name, ap_transaction.description
		HAVING ROUND(COALESCE(SUM(pmxc_alloc_amt), 0),2) != 0';

    return $dbh->executeSet($query, false, true, $params);
}


function getSimpleSubledgerCreditorDetails($propertyID, $leaseID, $periodFrom, $periodTo)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $query = "
		SELECT
			pmxc_f_batch,
			pmxc_f_line,
			pmxc_t_batch,
			pmxc_t_line,
			COALESCE(SUM(pmxc_alloc_amt), 0) as pmxc_alloc_amt,
			pmxc_s_creditor as pmxc_s_creditor,
			MAX(CONVERT(char(10), pmxc_alloc_dt, 103)) as pmxc_alloc_dt,
			pmco_name as pmco_name,
			ap_transaction.description as description			
		FROM
			pmxc_ap_alloc,
			pmco_company,
			ap_transaction
		WHERE
			pmxc_prop = '{$propertyID}'
			AND pmxc_lease = '{$leaseID}'
			AND pmxc_alloc_dt BETWEEN CONVERT(datetime, '{$periodFrom}', 103) AND CONVERT(datetime, '{$periodTo}', 103)
			AND pmxc_acc IN
			(
				SELECT
					pmcg_acc
				FROM
					pmcg_chart_grp
				WHERE pmcg_grp = 'TRACC3' and pmcg_subgrp IN ('EXPOWNREMI','BSPMTREMI')
				
				)
			AND pmxc_f_type = 'PAY'
			AND pmco_code = pmxc_s_creditor
			AND ap_transaction.batch_nr = pmxc_ap_alloc.pmxc_t_batch
			AND ap_transaction.batch_line_nr = pmxc_ap_alloc.pmxc_t_line
		GROUP BY pmxc_f_batch,pmxc_f_line, pmxc_t_batch, pmxc_t_line, pmxc_s_creditor, pmco_name, pmco_name, ap_transaction.description
		HAVING ROUND(COALESCE(SUM(pmxc_alloc_amt), 0),2) != 0";

    return $dbh->executeSet($query, false, true, [$propertyID, $leaseID, $periodFrom, $periodTo]);
}

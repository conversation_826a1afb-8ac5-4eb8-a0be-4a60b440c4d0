<?php

// ############################################### DETAIL CASH EXPENSES FOR CHOSEN PERIOD #######################
// ##########################PER TRANSACTION IN A GROUP - EXPOWN, EXPVO, EXPDR excluding EXPOPT, EXPGST##########

// changed the below function 16 jul 09 so that it does not look at table pmcg_chart_grp
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;

function expenses_detail_subaccountsWOunit($propertyID, $periodFrom, $periodTo, $acctSubGroup)
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);
    $sql = "SELECT  pmca_chart.pmca_code as 'account_code',
                        pmca_chart.pmca_name 'account_name',
                        pmco_company.pmco_name 'supplier_name',
                      ap_transaction.ref_1 'invoice_number',
                      ap_transaction.description 'description',
                      CONVERT(char(13),pmxc_ap_alloc.pmxc_alloc_dt, 103) as 'payment_date',
                      CONVERT(char(13), ap_transaction.spare_date_1, 103) as 'from_date',
                      CONVERT(char(13), dbo.ap_transaction.spare_date_2, 103) as 'to_date',
                      ((-1) * pmxc_ap_alloc.pmxc_alloc_amt) as 'gross_amount',
                      pmxc_ap_alloc.pmxc_tax_amt as 'tax_amount',
                      ((-1) * (pmxc_ap_alloc.pmxc_alloc_amt + pmxc_ap_alloc.pmxc_tax_amt)) as net_amount,
                      '' AS unitDescription
                    FROM
                        dbo.pmxc_ap_alloc
                        INNER JOIN
                        dbo.pmca_chart ON dbo.pmxc_ap_alloc.pmxc_acc = dbo.pmca_chart.pmca_code
                        INNER JOIN
                        dbo.ap_transaction

                        INNER JOIN
                        dbo.pmco_company ON dbo.ap_transaction.supplier_code = dbo.pmco_company.pmco_code ON
                        dbo.pmxc_ap_alloc.pmxc_t_batch = dbo.ap_transaction.batch_nr
                        AND dbo.pmxc_ap_alloc.pmxc_t_line = dbo.ap_transaction.batch_line_nr,
                        dbo.pmrcg_chart_grp pmrcg_chart_grp

                    WHERE pmxc_ap_alloc.pmxc_acc = pmca_chart.pmca_code
                        AND (pmrcg_chart_grp.pmrcg_grp='TRACC3')
                        AND (pmrcg_chart_grp.pmrcg_subgrp=?)
                        AND (pmca_chart.pmca_code>= CAST(pmrcg_acc AS INT)
                        And pmca_chart.pmca_code<= CAST(pmrcg_acc_to AS INT) )
                        AND (pmxc_ap_alloc.pmxc_prop=?)
                        AND (pmxc_ap_alloc.pmxc_f_type='PAY')
                        AND (pmxc_alloc_dt BETWEEN convert(datetime,?,103) AND convert(datetime,?,103))
                    ORDER BY 'account_code', pmxc_ap_alloc.pmxc_alloc_dt, ap_transaction.spare_date_1 ,'supplier_name'";
    $params = [$acctSubGroup, $propertyID, $periodFrom, $periodTo];

    return $dbh->executeSet($sql, false, true, $params);
}

// this is a special case for owner expenditure because it excludes EXPOWNGST and includes EXPTRANOWN
function getAcctSubGroupOwnerExpWOUnit()
{

    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);

    $sql = "SELECT DISTINCT (pmas_subgrp) as subgroup,
                    pmas_acc_subgrp.pmas_desc
                FROM pmas_acc_subgrp pmas_acc_subgrp
		WHERE
		(
			(pmas_acc_subgrp.pmas_grp = 'TRACC3')
			  AND (pmas_acc_subgrp.pmas_subgrp LIKE 'EXPOWN%')
			  AND (pmas_acc_subgrp.pmas_subgrp <> 'EXPOWNGST')OR (pmas_acc_subgrp.pmas_subgrp = 'EXPTRANOWN')
		)
		GROUP BY pmas_subgrp, pmas_desc
                ORDER BY pmas_subgrp";

    return $dbh->executeSet($sql);

}

// changed this one 16 jul 09 so as not to look at pmcg_chart_grp which is not being changed in new system
function getAcctSubGroupDRVOWOUnit($acctSubGroup)
{

    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);

    $sql = "SELECT DISTINCT pmas_acc_subgrp.pmas_subgrp as subgroup,
            pmas_acc_subgrp.pmas_desc
        FROM pmas_acc_subgrp pmas_acc_subgrp
        WHERE (pmas_acc_subgrp.pmas_grp='TRACC3')
        AND (pmas_acc_subgrp.pmas_subgrp Like '{$acctSubGroup}')
        GROUP BY pmas_subgrp, pmas_desc";

    return $dbh->executeSet($sql);

}

function newPageReportWOUnit($startNewPage = false)
{

    global $pdf;
    global $currentYCoord;
    global $currentXcoord;
    global $pageXCoord;
    global $pageYCoord;
    global $lineHeight;
    global $page;
    global $date;
    global $client;
    global $propertyName;
    global $periodDescription;
    global $periodFrom;
    global $periodTo;
    global $logo;
    global $_fonts;
    global $propertyID;
    global $lineExist;

    if ($startNewPage) {
        $pdf->begin_page_ext($pageXCoord, $pageYCoord, '');
        $page_header = 'Detailed Expenditure';
    } else {
        $page_header = 'Detailed Expenditure (...)';
        $pdf->end_page_ext('');
        $page++;

        // STAR NEW PAGE
        $pdf->begin_page_ext($pageXCoord, $pageYCoord, '');
    }

    $pdf->setFontExt($_fonts['Helvetica-Bold'], 9);
    $pdf->showBoxed("{$page_header}", 271, 540, 300, 30, 'center', '');
    $pdf->setFontExt($_fonts['Helvetica'], 8);
    $pdf->show_xy('Owner:', 22, 540);
    $pdf->continue_text('Property:');
    $pdf->continue_text('Report For:');
    $pdf->show_xy($client, 70, 540);
    $pdf->continue_text($propertyName . " [{$propertyID}]");
    $pdf->continue_text($periodDescription);
    $pdf->showBoxed("Period From: {$periodFrom} To: {$periodTo}", 271, 529, 300, 30, 'center', '');

    if ($logo) {
        generateLogo('landscape');
    }

    $pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
    $text2 = 'Account Name';
    $text4 = 'Payee';
    $text5 = 'Description';
    $text6 = "From\nDate";
    $text7 = "To\nDate";
    $text8 = "Net \n" . $_SESSION['country_default']['currency_symbol'];
    $text9 = $_SESSION['country_default']['tax_label'] . " \n" . $_SESSION['country_default']['currency_symbol'];
    $text10 = "Gross \n" . $_SESSION['country_default']['currency_symbol'];
    $text11 = "Payment\nDate";
    $text12 = 'Invoice #';

    $pdf->showBoxed($text2, 22, 480, 100, 30, 'left', '');
    $pdf->showBoxed($text4, 170, 480, 75, 30, 'left', '');
    $pdf->showBoxed($text12, 270, 480, 75, 30, 'left', '');
    $pdf->showBoxed($text5, 350, 480, 375, 30, 'left', '');
    $pdf->showBoxed($text11, 515, 480, 45, 30, 'center', '');
    $pdf->showBoxed($text6, 555, 480, 45, 30, 'center', '');
    $pdf->showBoxed($text7, 605, 480, 45, 30, 'center', '');
    $pdf->showBoxed($text8, 650, 480, 55, 30, 'center', '');
    $pdf->showBoxed($text9, 720, 480, 35, 30, 'center', '');
    $pdf->showBoxed($text10, 765, 480, 55, 30, 'center', '');
    $pdf->setlinewidth(0.5);

    $pdf->moveto(18, 515);
    $pdf->lineto(824, 515);
    $pdf->stroke();

    $pdf->moveto(18, 490);
    $pdf->lineto(824, 490);
    $pdf->stroke();

    $pdf->setFontExt($_fonts['Helvetica'], 8);
    $pdf->showBoxed("Page {$page}", 750, 0, 75, 30, 'right', '');

    // insert tracc header
    $traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'detailedExpenditure', A4_LANDSCAPE);
    $traccFooter->prerender($pdf);

    // #######################################################################################

    $currentYCoord = 460;
    $currentXcoord = 40;
    $lineHeight = 12;
    $lineExist = false;

}

function printGroupPDFWOUnit($acctSubGroupOwnerExp, $groupName, $printGrandTotal = false)
{
    global $pdf;
    global $currentYCoord;
    global $currentXcoord;
    global $lineHeight;
    global $page;

    global $grandTotalNet;
    global $grandTotalTax;
    global $grandTotalGross;

    global $groupTotalNet;
    $groupTotalNet = 0;
    global $groupTotalTax;
    $groupTotalTax = 0;
    global $groupTotalGross;
    $groupTotalGross = 0;

    global $amtWidth;
    global $groupNameWidth;

    global $propertyID;
    global $periodFrom;
    global $periodTo;
    global $_fonts;
    global $lineExist;

    global $totalNet;
    global $totalTax;
    global $totalGross;

    $printOwnerTotal = true;
    if ($groupName == 'OWNER REMITTANCE/S') {
        $printOwnerTotal = false;
    }

    $supplierWidth = 25;
    $groupNameWidth = 275;
    $supplierWidth = 275;
    global $amtWidth;

    // ###################################### TEST #########################################################

    $print_header = true;
    foreach ($acctSubGroupOwnerExp as $thisGroup) {

        $group_net_sum = 0;
        $group_tax_sum = 0;
        $group_gross_sum = 0;
        $subGroupCode = $thisGroup['subgroup'];
        $subGroupName = $thisGroup['pmas_desc'];
        $subAccountsOwner = expenses_detail_subaccountsWOunit($propertyID, $periodFrom, $periodTo, $subGroupCode);

        if (count($subAccountsOwner ?? []) > 0) {
            if ($print_header && $groupName) {
                $print_header = false;
                if ($currentYCoord >= 40) {
                    if ($currentYCoord <= 45 && $lineExist == false) {
                        $lineExist = true;
                        $pdf->setlinewidth(0.5);
                        $pdf->moveto(18, $currentYCoord);
                        $pdf->lineto(18, 515);
                        $pdf->stroke();
                        $pdf->moveto(824, $currentYCoord);
                        $pdf->lineto(824, 515);
                        $pdf->stroke();
                        $pdf->moveto(18, $currentYCoord);
                        $pdf->lineto(824, $currentYCoord);
                        $pdf->stroke();
                    }

                    $pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
                    $pdf->showBoxed(ucwords(strtolower($groupName)), 20, $currentYCoord, $groupNameWidth, $lineHeight + 10, 'left', '');
                    $currentYCoord = newLine($currentYCoord, 15);
                } else {
                    if ($lineExist == false) {
                        $lineExist = true;
                        $pdf->setlinewidth(0.5);

                        $pdf->moveto(18, $currentYCoord + 45);
                        $pdf->lineto(18, 515);
                        $pdf->stroke();

                        $pdf->moveto(824, $currentYCoord + 45);
                        $pdf->lineto(824, 515);
                        $pdf->stroke();
                    }

                    newPageReportWOUnit();
                    $pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
                    $pdf->showBoxed(ucwords(strtolower($groupName)), 20, $currentYCoord, $groupNameWidth, $lineHeight + 10, 'left', '');
                    $currentYCoord = newLine($currentYCoord);
                }
            }

            // initialise sub account rows
            $numRows = count($subAccountsOwner ?? []);
            $rowCount = 0;

            if ($currentYCoord >= 40) {
                if (ucwords(strtolower($subGroupName)) === 'Owners Remittance/s') {
                    $currentYCoord = newLine($currentYCoord, -20);
                }

                if (ucwords(strtolower($subGroupName)) === 'Owners Remittance/s' && $currentYCoord <= 45 && $lineExist == false) {
                    $lineExist = true;
                    $pdf->setlinewidth(0.5);
                    $pdf->moveto(18, $currentYCoord);
                    $pdf->lineto(18, 515);
                    $pdf->stroke();
                    $pdf->moveto(824, $currentYCoord);
                    $pdf->lineto(824, 515);
                    $pdf->stroke();
                    $pdf->moveto(18, $currentYCoord);
                    $pdf->lineto(824, $currentYCoord);
                    $pdf->stroke();
                }

                $pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
                $pdf->showBoxed(ucwords(strtolower($subGroupName)), 22, $currentYCoord, $supplierWidth, $lineHeight, 'left', ''); // echo " line 290 ycoord - $currentYCoord<br />";
                $currentYCoord = newLine($currentYCoord, 15);
            } elseif ($currentYCoord < 40) {
                $add = 40;
                if (in_array($currentYCoord, [5, 30, -10, 10, 25])) {
                    $add = 45;
                }

                if ($lineExist == false) {
                    $lineExist = true;
                    $pdf->setlinewidth(0.5);

                    $pdf->moveto(18, $currentYCoord + $add);
                    $pdf->lineto(18, 515);
                    $pdf->stroke();

                    $pdf->moveto(824, $currentYCoord + $add);
                    $pdf->lineto(824, 515);
                    $pdf->stroke();
                }

                newPageReportWOUnit();
                $pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
                $pdf->showBoxed(ucwords(strtolower($subGroupName)), 22, $currentYCoord, $supplierWidth, $lineHeight, 'left', '');
                $currentYCoord = newLine($currentYCoord, 15);
            }

            foreach ($subAccountsOwner as $thisRow) {
                $pdf->setFontExt($_fonts['Helvetica'], 7);
                $accCode = $thisRow['account_code']; // "Account Code";
                $accName = $thisRow['account_name']; // "Account Name";
                $payee = $thisRow['supplier_name']; // "Payee";
                $invoice = $thisRow['invoice_number']; // "Invoice No.";
                $desc = (strlen($thisRow['description']) > 50) ? substr($thisRow['description'], 0, 50) . '...' : $thisRow['description']; // "Description";
                $payDate = $thisRow['payment_date']; // "Payment\nDate";
                $fromDate = $thisRow['from_date']; // "From\nDate";
                $toDate = $thisRow['to_date']; // "To\nDate";
                $unitDescription = (strlen($thisRow['unitDescription']) > 23) ? substr($thisRow['unitDescription'], 0, 15) . '...' : $thisRow['unitDescription'];
                $net = $thisRow['net_amount']; // "Net Amount $";
                $gst = $thisRow['tax_amount']; // "GST $";
                $gross = $thisRow['gross_amount']; // "Gross Amount $";

                $group_net_sum += $net;
                $group_tax_sum += $gst;
                $group_gross_sum += $gross;

                if ($currentYCoord <= 30) {
                    if ($lineExist == false) {
                        $lineExist = true;
                        $pdf->setlinewidth(0.5);

                        if ($currentYCoord >= 25) {
                            $pdf->moveto(18, 35);
                            $pdf->lineto(18, 515);
                            $pdf->stroke();

                            $pdf->moveto(824, 35);
                            $pdf->lineto(824, 515);
                            $pdf->stroke();

                            $pdf->moveto(18, 35);
                            $pdf->lineto(824, 35);
                            $pdf->stroke();
                        } else {
                            echo $currentYCoord;
                            $pdf->moveto(18, $currentYCoord + 55);
                            $pdf->lineto(18, 515);
                            $pdf->stroke();

                            $pdf->moveto(824, $currentYCoord + 55);
                            $pdf->lineto(824, 515);
                            $pdf->stroke();
                        }
                    }

                    newPageReportWOUnit();
                    $currentYCoord = newLine($currentYCoord, -15);
                }

                $pdf->setFontExt($_fonts['Helvetica'], 7);
                $pdf->showBoxed($accCode, 22, $currentYCoord, 35, $lineHeight, 'left', '');
                $pdf->showBoxed($accName, 50, $currentYCoord, 100, $lineHeight, 'left', '');
                $pdf->showBoxed($payee, 170, $currentYCoord, 100, $lineHeight, 'left', '');
                $pdf->showBoxed($invoice, 270, $currentYCoord, 100, $lineHeight, 'left', '');
                $pdf->showBoxed(mb_convert_encoding($desc, 'UTF-8', 'UTF-8'), 350, $currentYCoord, 250, $lineHeight, 'left', '');
                $pdf->showBoxed($payDate, 515, $currentYCoord, 45, $lineHeight, 'left', '');
                $pdf->showBoxed($fromDate, 555, $currentYCoord, 45, $lineHeight, 'left', '');
                $pdf->showBoxed($toDate, 605, $currentYCoord, 45, $lineHeight, 'left', '');
                $pdf->showBoxed(formatting($net), 650, $currentYCoord, 55, $lineHeight, 'right', '');
                $pdf->showBoxed(formatting($gst), 715, $currentYCoord, 40, $lineHeight, 'right', '');
                $pdf->showBoxed(formatting($gross), 765, $currentYCoord, 55, $lineHeight, 'right', '');

                $rowCount++;
                $currentYCoord = newLine($currentYCoord);

            } // end for each sub account

            $groupTotalNet += $group_net_sum;
            $groupTotalTax += $group_tax_sum;
            $groupTotalGross += $group_gross_sum;

            $currentYCoord = newLine($currentYCoord, 15);

            if ($currentYCoord <= 30) {
                if ($lineExist == false) {
                    $lineExist = true;
                    $pdf->setlinewidth(0.5);

                    $pdf->moveto(18, 35);
                    $pdf->lineto(18, 515);
                    $pdf->stroke();

                    $pdf->moveto(824, 35);
                    $pdf->lineto(824, 515);
                    $pdf->stroke();

                    $pdf->moveto(18, 35);
                    $pdf->lineto(824, 35);
                    $pdf->stroke();
                }

                newPageReportWOUnit();
                $currentYCoord = newLine($currentYCoord);
            }

            $pdf->setColorExt('fill', 'rgb', 0.88, 0.88, 0.88, 0);
            $pdf->rect(19, $currentYCoord, 804, 20);
            $pdf->fill();
            $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);

            $pdf->setlinewidth(0.5);
            $pdf->moveto(18, $currentYCoord + $lineHeight + 8);
            $pdf->lineto(824, $currentYCoord + $lineHeight + 8);
            $pdf->stroke();

            if ($currentYCoord <= 55 && $lineExist == false) {
                $lineExist = true;
                $pdf->setlinewidth(0.5);
                $pdf->moveto(18, $currentYCoord);
                $pdf->lineto(18, 515);
                $pdf->stroke();
                $pdf->moveto(824, $currentYCoord);
                $pdf->lineto(824, 515);
                $pdf->stroke();
            }

            $pdf->setFontExt($_fonts['Helvetica-Bold'], 7);
            $pdf->showBoxed('Total ' . ucwords(strtolower($subGroupName)), 22, $currentYCoord - 1, $groupNameWidth, $lineHeight, 'left', '');
            $pdf->showBoxed(formatting($group_net_sum), 650, $currentYCoord - 1, 55, $lineHeight, 'right', '');
            $pdf->showBoxed(formatting($group_tax_sum), 715, $currentYCoord - 1, 40, $lineHeight, 'right', '');
            $pdf->showBoxed(formatting($group_gross_sum), 765, $currentYCoord - 1, 55, $lineHeight, 'right', '');

            $pdf->moveto(18, $currentYCoord);
            $pdf->lineto(824, $currentYCoord);
            $pdf->stroke();

            $currentYCoord = newLine($currentYCoord, 20);

            $totalNet += $group_net_sum;
            $totalTax += $group_tax_sum;
            $totalGross += $group_gross_sum;

        } // end if count > 0

    } // end for each acctSubGroupExp

    if ($printOwnerTotal && ($groupTotalNet > 0 || $groupTotalTax > 0 || $groupTotalGross > 0 || ! $groupName)) {
        $currentYCoord = newLine($currentYCoord, 15);

        if ($currentYCoord <= 30) {
            if ($lineExist == false) {
                $lineExist = true;
                $pdf->setlinewidth(0.5);

                if ($groupName == '') {
                    $pdf->moveto(18, $currentYCoord + ($currentYCoord == 30 ? 35 : 60));
                    $pdf->lineto(18, 515);
                    $pdf->stroke();

                    $pdf->moveto(824, $currentYCoord + ($currentYCoord == 30 ? 35 : 60));
                    $pdf->lineto(824, 515);
                    $pdf->stroke();
                } else {
                    $pdf->moveto(18, $currentYCoord + 35);
                    $pdf->lineto(18, 515);
                    $pdf->stroke();

                    $pdf->moveto(824, $currentYCoord + 35);
                    $pdf->lineto(824, 515);
                    $pdf->stroke();

                    $pdf->moveto(18, $currentYCoord + 35);
                    $pdf->lineto(824, $currentYCoord + 35);
                    $pdf->stroke();
                }
            }

            newPageReportWOUnit();
            $currentYCoord = newLine($currentYCoord);
        }

        $pdf->setColorExt('fill', 'rgb', 0.88, 0.88, 0.88, 0);
        $pdf->rect(19, $currentYCoord, 804, 20);
        $pdf->fill();
        $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);

        $pdf->setlinewidth(0.5);
        $pdf->moveto(18, $currentYCoord + $lineHeight + 8);
        $pdf->lineto(824, $currentYCoord + $lineHeight + 8);
        $pdf->stroke();

        if ($currentYCoord <= 55 && $lineExist == false) {
            $lineExist = true;
            $pdf->setlinewidth(0.5);
            $pdf->moveto(18, $currentYCoord);
            $pdf->lineto(18, 515);
            $pdf->stroke();
            $pdf->moveto(824, $currentYCoord);
            $pdf->lineto(824, 515);
            $pdf->stroke();
        }

        $pdf->setFontExt($_fonts['Helvetica-Bold'], 7);
        $pdf->showBoxed('Total ' . ucwords(strtolower(($groupName ? $groupName : 'Expenditure'))), 22, $currentYCoord, $groupNameWidth, $lineHeight, 'left', '');
        $pdf->setFontExt($_fonts['Helvetica-Bold'], 7);
        $pdf->showBoxed($groupName ? formatting($groupTotalNet) : formatting($totalNet), 650, $currentYCoord, 55, $lineHeight, 'right', '');
        $pdf->showBoxed($groupName ? formatting($groupTotalTax) : formatting($totalTax), 715, $currentYCoord, 40, $lineHeight, 'right', '');
        $pdf->showBoxed($groupName ? formatting($groupTotalGross) : formatting($totalGross), 765, $currentYCoord, 55, $lineHeight, 'right', '');

        $pdf->moveto(18, $currentYCoord);
        $pdf->lineto(824, $currentYCoord);
        $pdf->stroke();

        $currentYCoord = newLine($currentYCoord, 25);
    }

    // Add up the grand totals
    $grandTotalNet += $groupTotalNet;
    $grandTotalTax += $groupTotalTax;
    $grandTotalGross += $groupTotalGross;

    if ($printGrandTotal && ($grandTotalNet > 0 || $grandTotalTax > 0 || $grandTotalGross > 0)) {

        $currentYCoord = newLine($currentYCoord, 15);

        if ($currentYCoord <= 30) {
            if ($lineExist == false) {
                $lineExist = true;
                $pdf->setlinewidth(0.5);

                $pdf->moveto(18, $currentYCoord + 40);
                $pdf->lineto(18, 515);
                $pdf->stroke();

                $pdf->moveto(824, $currentYCoord + 40);
                $pdf->lineto(824, 515);
                $pdf->stroke();
            }

            newPageReportWOUnit();
            $currentYCoord = newLine($currentYCoord);
        }

        $pdf->setColorExt('fill', 'rgb', 0.88, 0.88, 0.88, 0);
        $pdf->rect(19, $currentYCoord, 804, 20);
        $pdf->fill();
        $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);

        $pdf->setlinewidth(0.5);
        $pdf->moveto(18, $currentYCoord + $lineHeight + 8);
        $pdf->lineto(824, $currentYCoord + $lineHeight + 8);
        $pdf->stroke();

        $pdf->setFontExt($_fonts['Helvetica-Bold'], 7);
        $pdf->showBoxed('Total Operating Expenditure', 22, $currentYCoord, $groupNameWidth, $lineHeight, 'left', '');
        $pdf->setFontExt($_fonts['Helvetica-Bold'], 7);
        $pdf->showBoxed(formatting($grandTotalNet), 650, $currentYCoord, 55, $lineHeight, 'right', '');
        $pdf->showBoxed(formatting($grandTotalTax), 720, $currentYCoord, 35, $lineHeight, 'right', '');
        $pdf->showBoxed(formatting($grandTotalGross), 765, $currentYCoord, 55, $lineHeight, 'right', '');

        $pdf->moveto(18, $currentYCoord);
        $pdf->lineto(824, $currentYCoord);
        $pdf->stroke();

        $currentYCoord = newLine($currentYCoord, 45);
    }
}

function newPageExcelWOUnit(&$excel)
{
    global $client;
    global $propertyName;
    global $periodDescription;
    global $periodFrom;
    global $periodTo;
    global $propertyID;

    $excel->setActiveSheetIndex(0); // Activate it
    $excel->getActiveSheet()->setTitle('Detailed Expenditure');

    $excel->getActiveSheet()->getStyle('A1:J1')->getFont()->setBold(true);
    $excel->getActiveSheet()->SetCellValue(cellReference(1, 1), 'Detailed Expenditure');

    $excel->getActiveSheet()->SetCellValue(cellReference(2, 1), 'Period From:');
    $excel->getActiveSheet()->SetCellValue(cellReference(2, 2), "{$periodFrom} To: {$periodTo}");

    $excel->getActiveSheet()->SetCellValue(cellReference(4, 1), 'Owner:');
    $excel->getActiveSheet()->SetCellValue(cellReference(4, 2), "{$client}");
    $excel->getActiveSheet()->SetCellValue(cellReference(5, 1), 'Property:');
    $excel->getActiveSheet()->SetCellValue(cellReference(5, 2), $propertyName . " [{$propertyID}]");
    $excel->getActiveSheet()->SetCellValue(cellReference(6, 1), 'Report For:');
    $excel->getActiveSheet()->SetCellValue(cellReference(6, 2), $periodDescription);

    $excel->getActiveSheet()->getStyle('A9:J9')->getFont()->setBold(true);
    $excel->getActiveSheet()->SetCellValue(cellReference(9, 1), 'Account Name');
    $excel->getActiveSheet()->SetCellValue(cellReference(9, 2), 'Payee');
    $excel->getActiveSheet()->SetCellValue(cellReference(9, 3), 'Invoice #');
    $excel->getActiveSheet()->SetCellValue(cellReference(9, 4), 'Description');
    $excel->getActiveSheet()->SetCellValue(cellReference(9, 5), 'Payment Date');
    $excel->getActiveSheet()->SetCellValue(cellReference(9, 6), 'From Date');
    $excel->getActiveSheet()->SetCellValue(cellReference(9, 7), 'To Date');
    $excel->getActiveSheet()->SetCellValue(cellReference(9, 8), 'Net ' . $_SESSION['country_default']['currency_symbol']);
    $excel->getActiveSheet()->SetCellValue(cellReference(9, 9), $_SESSION['country_default']['tax_label'] . ' ' . $_SESSION['country_default']['currency_symbol']);
    $excel->getActiveSheet()->SetCellValue(cellReference(9, 10), 'Gross ' . $_SESSION['country_default']['currency_symbol']);

    foreach (range('A', $excel->getActiveSheet()->getHighestDataColumn()) as $col) {
        $excel
            ->getActiveSheet()
            ->getColumnDimension($col)
            ->setAutoSize(true);
    }
}

function printGroupExcelWOUnit($acctSubGroupOwnerExp, $groupName, $printGrandTotal, &$excel, &$excelRow)
{
    global $grandTotalNet;
    global $grandTotalTax;
    global $grandTotalGross;

    global $groupTotalNet;
    $groupTotalNet = 0;
    global $groupTotalTax;
    $groupTotalTax = 0;
    global $groupTotalGross;
    $groupTotalGross = 0;

    global $groupNameWidth;

    global $propertyID;
    global $periodFrom;
    global $periodTo;

    global $totalNet;
    global $totalTax;
    global $totalGross;

    $shadedColor = [
        'fill' => ['type' => Fill::FILL_SOLID,
            'color' => ['rgb' => 'cacaca']],
        'borders' => [
            'top' => [
                'style' => Border::BORDER_THIN,
                'color' => ['rgb' => '000000'],
            ],
            'bottom' => [
                'style' => Border::BORDER_THIN,
                'color' => ['rgb' => '000000'],
            ],
        ]];
    $printOwnerTotal = true;
    if ($groupName == 'OWNER REMITTANCE/S') {
        $printOwnerTotal = false;
    }

    $groupNameWidth = 275;
    $print_header = true;
    foreach ($acctSubGroupOwnerExp as $thisGroup) {
        $group_net_sum = 0;
        $group_tax_sum = 0;
        $group_gross_sum = 0;

        $subGroupCode = $thisGroup['subgroup'];
        $subGroupName = $thisGroup['pmas_desc'];

        $subAccountsOwner = expenses_detail_subaccountsWOunit($propertyID, $periodFrom, $periodTo, $subGroupCode);

        if (count($subAccountsOwner ?? []) > 0) {
            if ($print_header && $groupName) {
                $print_header = false;

                $excel->getActiveSheet()->getStyle('A' . $excelRow . ':J' . $excelRow)->getFont()->setSize(11);
                $excel->getActiveSheet()->getStyle('A' . $excelRow . ':J' . $excelRow)->getFont()->setBold(true);
                $excel->getActiveSheet()->SetCellValue(cellReference($excelRow, 1), ucwords(strtolower($groupName)));
                $excelRow++;
            }

            $excel->getActiveSheet()->getStyle('A' . $excelRow . ':J' . $excelRow)->getFont()->setSize(11);
            $excel->getActiveSheet()->getStyle('A' . $excelRow . ':J' . $excelRow)->getFont()->setBold(true);
            $excel->getActiveSheet()->SetCellValue(cellReference($excelRow, 1), ucwords(strtolower($subGroupName)));
            $excelRow++;

            foreach ($subAccountsOwner as $thisRow) {
                $accCode = $thisRow['account_code']; // "Account Code";
                $accName = $thisRow['account_name']; // "Account Name";
                $payee = $thisRow['supplier_name']; // "Payee";
                $invoice = $thisRow['invoice_number']; // "Invoice No.";
                $desc = $thisRow['description'];
                $payDate = $thisRow['payment_date']; // "Payment\nDate";
                $fromDate = $thisRow['from_date']; // "From\nDate";
                $toDate = $thisRow['to_date']; // "To\nDate";
                $unitDescription = $thisRow['unitDescription'];
                $net = $thisRow['net_amount']; // "Net Amount $";
                $gst = $thisRow['tax_amount']; // "GST $";
                $gross = $thisRow['gross_amount']; // "Gross Amount $";

                $group_net_sum += $net;
                $group_tax_sum += $gst;
                $group_gross_sum += $gross;

                $excel->getActiveSheet()->getStyle('A' . $excelRow . ':J' . $excelRow)->getFont()->setSize(9);
                $excel->getActiveSheet()->SetCellValue(cellReference($excelRow, 1), $accCode . '       ' . $accName);
                $excel->getActiveSheet()->SetCellValue(cellReference($excelRow, 2), $payee);
                $excel->getActiveSheet()->SetCellValue(cellReference($excelRow, 3), $invoice);
                $excel->getActiveSheet()->SetCellValue(cellReference($excelRow, 4), $desc);
                $excel->getActiveSheet()->SetCellValue(cellReference($excelRow, 5), $payDate);
                $excel->getActiveSheet()->SetCellValue(cellReference($excelRow, 6), $fromDate);
                $excel->getActiveSheet()->SetCellValue(cellReference($excelRow, 7), $toDate);
                $excel->getActiveSheet()->SetCellValue(cellReference($excelRow, 8), trim($net));
                $excel->getActiveSheet()->SetCellValue(cellReference($excelRow, 9), trim($gst));
                $excel->getActiveSheet()->SetCellValue(cellReference($excelRow, 10), trim($gross));
                $excel->getActiveSheet()->getStyle('H' . $excelRow . ':J' . $excelRow)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);
                $excel->getActiveSheet()->getStyle('H' . $excelRow . ':J' . $excelRow)->getNumberFormat()->setFormatCode('#,##0.00');
                $excel->getActiveSheet()->getStyle('B' . $excelRow)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_LEFT);
                $excel->getActiveSheet()->getStyle('C' . $excelRow)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_LEFT);
                $excelRow++;

            } // end for each sub account

            $groupTotalNet += $group_net_sum;
            $groupTotalTax += $group_tax_sum;
            $groupTotalGross += $group_gross_sum;

            $excel->getActiveSheet()->getStyle('A' . $excelRow . ':J' . $excelRow)->applyFromArray($shadedColor);
            $excel->getActiveSheet()->getStyle('A' . $excelRow . ':J' . $excelRow)->getFont()->setSize(9);
            $excel->getActiveSheet()->getStyle('A' . $excelRow . ':J' . $excelRow)->getFont()->setBold(true);
            $excel->getActiveSheet()->SetCellValue(cellReference($excelRow, 1), 'Total ' . ucwords(strtolower($subGroupName)));
            $excel->getActiveSheet()->SetCellValue(cellReference($excelRow, 8), round($group_net_sum, 2));
            $excel->getActiveSheet()->SetCellValue(cellReference($excelRow, 9), round($group_tax_sum, 2));
            $excel->getActiveSheet()->SetCellValue(cellReference($excelRow, 10), round($group_gross_sum, 2));
            $excel->getActiveSheet()->getStyle('H' . $excelRow . ':J' . $excelRow)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);
            $excel->getActiveSheet()->getStyle('H' . $excelRow . ':J' . $excelRow)->getNumberFormat()->setFormatCode('#,##0.00');
            $excelRow++;

            $totalNet += $group_net_sum;
            $totalTax += $group_tax_sum;
            $totalGross += $group_gross_sum;

        } // end if count > 0

    } // end for each acctSubGroupExp

    if ($printOwnerTotal && ($groupTotalNet > 0 || $groupTotalTax > 0 || $groupTotalGross > 0 || ! $groupName)) {
        $excel->getActiveSheet()->getStyle('A' . $excelRow . ':J' . $excelRow)->applyFromArray($shadedColor);
        $excel->getActiveSheet()->getStyle('A' . $excelRow . ':J' . $excelRow)->getFont()->setSize(9);
        $excel->getActiveSheet()->getStyle('A' . $excelRow . ':J' . $excelRow)->getFont()->setBold(true);
        $excel->getActiveSheet()->SetCellValue(cellReference($excelRow, 1), 'Total ' . ucwords(strtolower(($groupName ? $groupName : 'Expenditure'))));
        $excel->getActiveSheet()->SetCellValue(cellReference($excelRow, 8), $groupName ? round($groupTotalNet, 2) : round($totalNet, 2));
        $excel->getActiveSheet()->SetCellValue(cellReference($excelRow, 9), $groupName ? round($groupTotalTax, 2) : round($totalTax, 2));
        $excel->getActiveSheet()->SetCellValue(cellReference($excelRow, 10), $groupName ? round($groupTotalGross, 2) : round($totalGross, 2));
        $excel->getActiveSheet()->getStyle('H' . $excelRow . ':J' . $excelRow)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);
        $excel->getActiveSheet()->getStyle('H' . $excelRow . ':J' . $excelRow)->getNumberFormat()->setFormatCode('#,##0.00');
        $excelRow++;
    }

    // Add up the grand totals
    $grandTotalNet += $groupTotalNet;
    $grandTotalTax += $groupTotalTax;
    $grandTotalGross += $groupTotalGross;

    if ($printGrandTotal && ($grandTotalNet > 0 || $grandTotalTax > 0 || $grandTotalGross > 0)) {
        $excel->getActiveSheet()->getStyle('A' . $excelRow . ':J' . $excelRow)->applyFromArray($shadedColor);
        $excel->getActiveSheet()->getStyle('A' . $excelRow . ':J' . $excelRow)->getFont()->setSize(9);
        $excel->getActiveSheet()->getStyle('A' . $excelRow . ':J' . $excelRow)->getFont()->setBold(true);
        $excel->getActiveSheet()->SetCellValue(cellReference($excelRow, 1), 'Total Operating Expenditure');
        $excel->getActiveSheet()->SetCellValue(cellReference($excelRow, 8), round($grandTotalNet, 2));
        $excel->getActiveSheet()->SetCellValue(cellReference($excelRow, 9), round($grandTotalTax, 2));
        $excel->getActiveSheet()->SetCellValue(cellReference($excelRow, 10), round($grandTotalGross, 2));
        $excel->getActiveSheet()->getStyle('H' . $excelRow . ':J' . $excelRow)->getNumberFormat()->setFormatCode('#,##0.00');
        $excel->getActiveSheet()->getStyle('H' . $excelRow . ':J' . $excelRow)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);
        $excelRow++;
    }
}

<?php



$pdf->setFontExt($_fonts['Helvetica-Bold'], 9);


$pdf->showBoxed("{$page_header}", 271, 540, 300, 30, 'center', '');
$pdf->setFontExt($_fonts['Helvetica'], 8);
$pdf->show_xy('Owner:', 22, 540);
$pdf->continue_text('Property:');
$pdf->continue_text('Report For:');
$pdf->show_xy($client, 70, 540);
$pdf->continue_text($propertyName . " [{$propertyID}]");
$pdf->continue_text($periodDescription);
$pdf->showBoxed("Period From: {$periodFrom} To: {$periodTo}", 271, 529, 300, 30, 'center', '');


// include "logo3.php";

// top header line
$pdf->setlinewidth(0.5);
$pdf->moveto(18, 515);
$pdf->lineto(824, 515);
$pdf->stroke();

// 2nd header line
$pdf->moveto(215, 470);
$pdf->lineto(824, 470);
$pdf->stroke();

// first header line
$pdf->moveto(215, 497);
$pdf->lineto(745, 497);
$pdf->stroke();


// $pdf->setColorExt("both", "rgb", 0,0,0, 0);
$pdf->setFontExt($_fonts['Helvetica-Bold'], 9);
$text1 = '';
$text2 = '';
$text3 = '';
$text4 = '';
$text5 = 'Current Month';
$text6 = '';
$text7 = '';
$text8 = 'Year to Date';
$text9 = "Budget\nYear";
$text10 = '';


$pdf->showBoxed($text1, 22, 480, 75, 30, 'left', '');
$pdf->showBoxed($text3, 250, 480, 75, 30, 'center', '');
$pdf->showBoxed($text4, 325, 480, 75, 30, 'center', '');
$pdf->showBoxed($text5, 210, 480, 270, 30, 'center', '');
$pdf->showBoxed($text6, 475, 480, 75, 30, 'center', '');
$pdf->showBoxed($text7, 550, 480, 75, 30, 'center', '');
$pdf->showBoxed($text8, 480, 480, 270, 30, 'center', '');
$pdf->showBoxed($text9, 765, 480, 55, 30, 'center', '');
$pdf->showBoxed($text10, 765, 480, 75, 30, 'center', '');

$text1 = "Actual\n" . $_SESSION['country_default']['currency_symbol'];
$text2 = "Budget\n" . $_SESSION['country_default']['currency_symbol'];
$text3 = "Variance\n" . $_SESSION['country_default']['currency_symbol'];
$text4 = "Var\n%";
$text5 = "Actual\n" . $_SESSION['country_default']['currency_symbol'];
$text6 = "Budget\n" . $_SESSION['country_default']['currency_symbol'];
$text7 = "Variance\n" . $_SESSION['country_default']['currency_symbol'];
$text8 = "Var\n%";
// $text9 = "$";
$text9 = $_SESSION['country_default']['currency_symbol'];
$text10 = '';


$pdf->showBoxed($text1, 232, 466, 35, 30, 'center', '');
$pdf->showBoxed($text2, 302, 466, 35, 30, 'center', '');
$pdf->showBoxed($text3, 362, 466, 45, 30, 'center', '');
$pdf->showBoxed($text4, 432, 466, 25, 30, 'center', '');
$pdf->showBoxed($text5, 515, 466, 35, 30, 'center', '');
$pdf->showBoxed($text6, 585, 466, 35, 30, 'center', '');
$pdf->showBoxed($text7, 650, 466, 45, 30, 'center', '');
$pdf->showBoxed($text8, 712, 466, 25, 30, 'center', '');
$pdf->showBoxed($text9, 765, 460, 55, 30, 'center', '');
$pdf->showBoxed($text10, 765, 466, 35, 30, 'center', '');

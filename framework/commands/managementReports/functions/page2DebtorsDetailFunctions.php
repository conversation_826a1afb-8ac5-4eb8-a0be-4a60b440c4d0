<?php

function getCashSummaryDetailed($propertyID, $periodFrom, $periodTo)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $params = [];

    $sql = "SELECT * from (
		SELECT pmle_t_name as tenant_name, case COALESCE(pmpu_desc,'') when '' then pmle_description else pmpu_desc end as unit_desc,pmca_name as account_name,x.*,
    
        COALESCE((SELECT SUM(pmxd_alloc_amt-pmxd_tax_amt)
        FROM pmxd_ar_alloc
        WHERE pmxd_alloc_dt >= CONVERT(datetime, " . addSQLParam($params, $periodFrom) . ', 103)
        AND pmxd_alloc_dt <= CONVERT(datetime, ' . addSQLParam($params, $periodTo) . ', 103)
        AND pmxd_t_batch = x.batchNumber
        AND pmxd_t_line = x.batchLineNumber), CONVERT(money, 0.00))
        +
        COALESCE((SELECT SUM((pmxd_alloc_amt-pmxd_tax_amt)*-1) 
        FROM pmxd_ar_alloc
        WHERE pmxd_alloc_dt >= CONVERT(datetime, ' . addSQLParam($params, $periodFrom) . ', 103)
        AND pmxd_alloc_dt <= CONVERT(datetime, ' . addSQLParam($params, $periodTo) . ', 103)
        AND pmxd_f_batch = x.batchNumber
        AND pmxd_f_line = x.batchLineNumber)
        , CONVERT(money, 0.00)) AS receivedNet,
    
        COALESCE((SELECT SUM(pmxd_tax_amt)
        FROM pmxd_ar_alloc
        WHERE pmxd_alloc_dt >= CONVERT(datetime, ' . addSQLParam($params, $periodFrom) . ', 103)
        AND pmxd_alloc_dt <= CONVERT(datetime, ' . addSQLParam($params, $periodTo) . ', 103)
        AND pmxd_t_batch = x.batchNumber
        AND pmxd_t_line = x.batchLineNumber), CONVERT(money, 0.00))
        +
        COALESCE((SELECT SUM((pmxd_tax_amt)*-1) 
        FROM pmxd_ar_alloc
        WHERE pmxd_alloc_dt >= CONVERT(datetime, ' . addSQLParam($params, $periodFrom) . ', 103)
        AND pmxd_alloc_dt <= CONVERT(datetime, ' . addSQLParam($params, $periodTo) . ', 103)
        AND pmxd_f_batch = x.batchNumber
        AND pmxd_f_line = x.batchLineNumber)
        , CONVERT(money, 0.00)) AS receivedTax
    
    
    
    FROM
    (SELECT 
        ref_5 as unit_code,
        batch_number AS batchNumber,
        batch_line_number AS batchLineNumber,
        debtor_code AS debtorID,
        property_code AS propertyID,
        account_code AS accountID,
        lease_code AS leaseID,
        description,
        from_date AS fromDate,
        to_date AS toDate,
        transaction_date AS transactionDate,
        due_date AS dueDate,
        invoice_number AS invoiceNumber,
          
        COALESCE((SELECT SUM(pmxd_alloc_amt)
        FROM pmxd_ar_alloc
        WHERE pmxd_alloc_dt < CONVERT(datetime, ' . addSQLParam($params, $periodFrom) . ', 103)
        AND pmxd_t_batch = batch_number
        AND pmxd_t_line = batch_line_number), CONVERT(money, 0.00))
        +
        COALESCE((SELECT SUM(pmxd_alloc_amt*-1) 
        FROM pmxd_ar_alloc
        WHERE pmxd_alloc_dt < CONVERT(datetime, ' . addSQLParam($params, $periodFrom) . ', 103)
        AND pmxd_f_batch = batch_number
        AND pmxd_f_line = batch_line_number)
        , CONVERT(money, 0.00))
        +
        COALESCE(trans_amt, CONVERT(money, 0.00))
        AS openingBalance,
          
        CONVERT(money, 0.00) AS billedNetAmount,
        CONVERT(money, 0.00) AS billedTaxAmount
    
      FROM (
        SELECT
        ref_5,
        batch_nr AS batch_number,
        batch_line_nr AS batch_line_number,
        debtor_code AS debtor_code,
        ref_2 AS property_code,
        ref_3 AS account_code,
        ref_4 AS lease_code,
        description,
        spare_date_1 AS from_date,
        spare_date_2 AS to_date,
        trans_date AS transaction_date,
        due_date,
        artr_gst_inv_no AS invoice_number,
        
        COALESCE((SELECT SUM(pmxd_alloc_amt)
        FROM pmxd_ar_alloc
        WHERE pmxd_alloc_dt < CONVERT(datetime, ' . addSQLParam($params, $periodFrom) . ', 103)
        AND pmxd_t_batch = batch_nr
        AND pmxd_t_line = batch_line_nr), CONVERT(money, 0.00))
        +
        COALESCE((SELECT SUM(pmxd_alloc_amt*-1) 
        FROM pmxd_ar_alloc
        WHERE pmxd_alloc_dt < CONVERT(datetime, ' . addSQLParam($params, $periodFrom) . ', 103)
        AND pmxd_f_batch = batch_nr
        AND pmxd_f_line = batch_line_nr)
        , CONVERT(money, 0.00)) AS openingBalance,
    
        artr_net_amt AS billedNetAmount,
        artr_tax_amt AS billedTaxAmount,
        trans_amt,
        artr_tax_amt,
    
        trans_amt
        +
        COALESCE((SELECT SUM(pmxd_alloc_amt)
        FROM pmxd_ar_alloc
        WHERE pmxd_alloc_dt < CONVERT(datetime, ' . addSQLParam($params, $periodFrom) . ', 103)
        AND pmxd_t_batch = batch_nr
        AND pmxd_t_line = batch_line_nr), CONVERT(money, 0.00))
        +
        COALESCE((SELECT SUM(pmxd_alloc_amt*-1) 
        FROM pmxd_ar_alloc
        WHERE pmxd_alloc_dt < CONVERT(datetime, ' . addSQLParam($params, $periodFrom) . ", 103)
        AND pmxd_f_batch = batch_nr
        AND pmxd_f_line = batch_line_nr)
        , CONVERT(money, 0.00)) AS balanceAsAt
        
        
        FROM ar_transaction
    
    
        WHERE (trans_type IN ('INV', 'CRE')) 
        AND ((ref_2 = " . addSQLParam($params, $propertyID) . '))  
        AND trans_date < CONVERT(datetime, ' . addSQLParam($params, $periodFrom) . ', 103)
      ) prior_ar_transaction
      WHERE balanceAsAt != 0
    
    UNION
    
    SELECT
        ref_5 as unit_code,
        batch_nr AS batchNumber,
        batch_line_nr AS batchLineNumber,
        debtor_code AS debtorID,
        ref_2 AS propertyID,
        ref_3 AS accountID,
        ref_4 AS leaseID,
        description,
        spare_date_1 AS fromDate,
        spare_date_2 AS toDate,
        trans_date AS transactionDate,
        due_date AS dueDate,
        artr_gst_inv_no AS invoiceNumber,
        
        COALESCE((SELECT SUM(pmxd_alloc_amt)
        FROM pmxd_ar_alloc
        WHERE pmxd_alloc_dt < CONVERT(datetime, ' . addSQLParam($params, $periodFrom) . ', 103)
        AND pmxd_t_batch = batch_nr
        AND pmxd_t_line = batch_line_nr), CONVERT(money, 0.00))
        +
        COALESCE((SELECT SUM(pmxd_alloc_amt*-1) 
        FROM pmxd_ar_alloc
        WHERE pmxd_alloc_dt < CONVERT(datetime, ' . addSQLParam($params, $periodFrom) . ', 103)
        AND pmxd_f_batch = batch_nr
        AND pmxd_f_line = batch_line_nr)
        , CONVERT(money, 0.00)) AS openingBalance,
        
        CASE WHEN (trans_date >= CONVERT(datetime, ' . addSQLParam($params, $periodFrom) . ', 103)
                AND trans_date <= CONVERT(datetime, ' . addSQLParam($params, $periodTo) . ', 103))
            THEN artr_net_amt 
            ELSE 0
            END AS billedNetAmount,
    
        CASE WHEN (trans_date >= CONVERT(datetime, ' . addSQLParam($params, $periodFrom) . ', 103)
                AND trans_date <= CONVERT(datetime, ' . addSQLParam($params, $periodTo) . ", 103))
            THEN artr_tax_amt  
            ELSE 0
            END AS billedTaxAmount
    
    FROM ar_transaction a
    
    
    WHERE (trans_type IN ('INV', 'CRE')) 
    AND ((ref_2 = " . addSQLParam($params, $propertyID) . '))  
    AND trans_date >= CONVERT(datetime, ' . addSQLParam($params, $periodFrom) . ', 103)
    
    ) x
    
    Left join pmle_lease on pmle_lease = leaseID and pmle_prop = propertyID
            Left join pmpu_p_unit on unit_code = pmpu_unit and pmpu_prop = propertyID
            Left join pmca_chart on pmca_code = accountID
    
    WHERE (transactionDate <= CONVERT(datetime, ' . addSQLParam($params, $periodTo) . ', 103)
    OR COALESCE((SELECT SUM(pmxd_alloc_amt)
        FROM pmxd_ar_alloc
        WHERE pmxd_alloc_dt >= CONVERT(datetime, ' . addSQLParam($params, $periodFrom) . ', 103)
        AND pmxd_alloc_dt <= CONVERT(datetime, ' . addSQLParam($params, $periodTo) . ', 103)
        AND pmxd_t_batch = x.batchNumber
        AND pmxd_t_line = x.batchLineNumber), CONVERT(money, 0.00))
        +
        COALESCE((SELECT SUM((pmxd_alloc_amt)*-1) 
        FROM pmxd_ar_alloc
        WHERE pmxd_alloc_dt >= CONVERT(datetime, ' . addSQLParam($params, $periodFrom) . ', 103)
        AND pmxd_alloc_dt <= CONVERT(datetime, ' . addSQLParam($params, $periodTo) . ", 103)
        AND pmxd_f_batch = x.batchNumber
        AND pmxd_f_line = x.batchLineNumber)
        , CONVERT(money, 0.00)) != 0
    )
            
            UNION ALL
    
            SELECT 'Vacant' AS tenant_name,
                    pmpu_desc, '',
                    pmpu_unit,0,0,'','','','','','','','','',0,0,0,0,0,0
                FROM pmpu_p_unit, 
                    pmpf_p_floor
                WHERE pmpu_prop = " . addSQLParam($params, $propertyID) . ' 
                    AND (pmpu_unit   IN 
                        (SELECT pmpu_unit 
                        FROM pmpf_p_floor, 
                            pmpu_p_unit, 
                            pmua_unit_area 
                         WHERE pmpf_prop = ' . addSQLParam($params, $propertyID) . " 
                            and pmua_status = 'V'
                            AND pmpu_prop = pmpf_prop 
                            AND pmpf_floor = pmpu_floor 
                            AND pmua_prop = pmpf_prop 
                            AND pmua_unit = pmpu_unit 
                            AND (pmua_to_dt>= CONVERT(datetime, " . addSQLParam($params, $periodFrom) . ', 103))
                            AND (pmua_from_dt<= CONVERT(datetime, ' . addSQLParam($params, $periodTo) . ", 103))))
                            AND pmpu_prop = pmpf_prop 
                            AND pmpf_floor = pmpu_floor
                ) t
    ORDER BY unit_code, case tenant_name when 'Vacant' then 1 else 0 end , account_name, transactionDate";

    $result = $dbh->executeSet($sql, false, true, $params);
    $detailed = [];
    foreach ($result as $value) {
        $detailed[$value['tenant_name'] . $value['unit_desc']][] = $value;
    }

    return $detailed;
}

/**
 * @modified 2008-10-13: amended thiscode 13 Oct 08 to look at pmua_unit_area rather than pmlu_l_unit
 **/
function getUnitDetails($propertyID, $periodFrom, $periodTo)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $params = [];
    // INTO $intotable removed temp table creation
    $unitsSQL = 'SELECT DISTINCT pmua_lease,pmpf_prop, 
			pmpf_floor, 
			pmpf_seq, 
			pmpu_unit, 
			pmpu_desc, 

			tenant_name, 
			status, 
			debtor_code
		FROM pmpf_p_floor, 
			pmpu_p_unit, 
			pmua_unit_area, 
			crp_lease
		WHERE pmpf_prop = ' . addSQLParam($params, $propertyID) . " 
			AND pmpu_prop = pmpf_prop 
			AND pmpf_floor = pmpu_floor 
			AND pmua_prop = pmpf_prop 
			AND pmua_unit = pmpu_unit 
			AND lease_code = pmua_lease
						AND property_code = pmua_prop
		UNION ALL

		SELECT lease_code, property_code, 
			'zzzzz', 
			'99999', 
			'nounit', 
			 LEASE_DESCRIPTION as lease_id, 

			tenant_name, 
			status,
			debtor_code
		FROM crp_lease
		WHERE property_code = " . addSQLParam($params, $propertyID) . ' 
			AND lease_code NOT IN (SELECT pmua_lease
						 FROM pmpf_p_floor, 
						pmpu_p_unit, 
						pmua_unit_area, 
						crp_lease
						 WHERE pmpf_prop = ' . addSQLParam($params, $propertyID) . " 
						AND pmpu_prop = pmpf_prop 
						AND pmpf_floor = pmpu_floor 
						AND pmua_prop = pmpf_prop 
						AND pmua_unit = pmpu_unit 
						AND lease_code = pmua_lease 
						AND property_code = pmua_prop)
			UNION ALL
			SELECT 'VACANT',pmpu_prop, 
				pmpu_floor as pmpf_floor, 
				pmpf_seq, 
				pmpu_unit, 
				pmpu_desc, 

				'VACANT', 
				'L',
				'debtor_code'
			FROM pmpu_p_unit, 
				pmpf_p_floor
			WHERE pmpu_prop = " . addSQLParam($params, $propertyID) . ' 
				AND (pmpu_unit NOT IN 
					(SELECT pmpu_unit 
					FROM pmpf_p_floor, 
						pmpu_p_unit, 
						pmua_unit_area, 
						crp_lease
					 WHERE pmpf_prop = ' . addSQLParam($params, $propertyID) . ' 
						AND pmpu_prop = pmpf_prop 
						AND pmpf_floor = pmpu_floor 
						AND pmua_prop = pmpf_prop 
						AND pmua_unit = pmpu_unit 
						AND lease_code = pmua_lease 
						AND property_code = pmua_prop
						AND (pmua_to_dt>= CONVERT(datetime, ' . addSQLParam($params, $periodTo) . ', 103))
						AND (pmua_from_dt<= CONVERT(datetime, ' . addSQLParam($params, $periodFrom) . ', 103))))
						AND pmpu_prop = pmpf_prop 
						AND pmpf_floor = pmpu_floor


						ORDER BY pmpf_seq ASC, pmpu_unit ASC';

    return $dbh->executeSet($unitsSQL, false, true, $params);
    // note added in two lines above with the date ranges to check for the units from and to dates because units that were vacant but previously occupied were not coming up
}

function getStopChargeDate($propertyID, $unit_code, $tenantCode)
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);
    $params = [];

    $chargeDateSQL = 'SELECT CONVERT(char(10), MAX(pmua_to_dt), 101) as pmlu_occ_to_dt
		FROM pmua_unit_area
				WHERE pmua_prop = ' . addSQLParam($params, $propertyID) . '
				AND pmua_unit = ' . addSQLParam($params, $unit_code) . '
				AND pmua_lease = ' . addSQLParam($params, $tenantCode);


    return $dbh->executeScalar($chargeDateSQL, $params);
}

function getBilledAccCode($tenantCode, $propertyID, $periodTo)
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);
    $params = [];

    $query = 'SELECT DISTINCT ref_3 AS acc
		FROM  ar_transaction
		WHERE (ref_4 = ' . addSQLParam($params, $tenantCode) . ') 
			AND (ref_2 = ' . addSQLParam($params, $propertyID) . ") 
			AND (trans_type <> 'CSH') 
			AND (trans_type <> 'REV') 
			AND (trans_date <= CONVERT(datetime, " . addSQLParam($params, $periodTo) . ", 103))
			AND (ref_3 <> 'NULL')
			ORDER BY ref_3";

    return $dbh->executeSet($query, false, true, $params);
}

function getBilledGross($propertyID, $periodTo)
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);
    $params = [];

    $query = 'SELECT COALESCE(SUM(trans_amt), 0)
		FROM ar_transaction
		WHERE (ref_2 = ' . addSQLParam($params, $propertyID) . ") 
			AND (trans_type <> 'CSH') 
			AND (trans_type <> 'REV') 
			AND (trans_date <= CONVERT(datetime, " . addSQLParam($params, $periodTo) . ", 103))
			AND (ref_3 <> 'NULL')";

    return $dbh->executeScalar($query, $params);
}

function getPaidAccCode($tenantCode, $propertyID, $periodTo)
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);
    $params = [];
    $paidSQL = "SELECT DISTINCT pmxd_acc AS acc
				FROM pmxd_ar_alloc 
				WHERE (pmxd_f_type <> 'REV')
				AND (pmxd_f_type <> 'ADJ') 
				AND (pmxd_f_type <> 'CRE') 
				AND (pmxd_lease = " . addSQLParam($params, $tenantCode) . ') 
				AND (pmxd_prop = ' . addSQLParam($params, $propertyID) . ") 
				AND (pmxd_acc <> '') 
				AND (pmxd_alloc_dt <= CONVERT(datetime, " . addSQLParam($params, $periodTo) . ', 103)) 
				ORDER BY PMXD_ACC';

    return $dbh->executeSet($paidSQL, false, true, $params);
}

function getReceivedGross($propertyID, $periodTo)
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);
    $params = [];
    $paidSQL = "SELECT COALESCE(SUM(pmxd_alloc_amt), 0)
				FROM pmxd_ar_alloc 
				WHERE (pmxd_f_type = 'CSH')
				AND (pmxd_prop = " . addSQLParam($params, $propertyID) . ") 
				AND (pmxd_acc <> '') 
				AND (pmxd_alloc_dt <= CONVERT(datetime, " . addSQLParam($params, $periodTo) . ', 103))';

    return $dbh->executeScalar($paidSQL, $params);
}

function getUnallocatedCashGross($propertyID, $periodTo)
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);
    $params = [];
    $unAllocCash = 'SELECT COALESCE(SUM(pmuc_amt), 0) 
					FROM pmuc_unall_csh 
					WHERE (pmuc_prop = ' . addSQLParam($params, $propertyID) . ') 					
					AND (pmuc_rcpt_dt <= CONVERT(datetime, ' . addSQLParam($params, $periodTo) . ', 103))';

    // echo $unAllocCash;
    return $dbh->executeScalar($unAllocCash, $params);
}

function getUnallocatedCashAccCode($tenantCode, $propertyID, $periodTo)
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);
    $params = [];
    $unAllocCash = 'SELECT DISTINCT pmuc_acc AS acc 
					FROM pmuc_unall_csh 
					WHERE (pmuc_prop = ' . addSQLParam($params, $propertyID) . ') 
					AND (pmuc_lease = ' . addSQLParam($params, $tenantCode) . ') 
					AND (pmuc_rcpt_dt <= CONVERT(datetime, ' . addSQLParam($params, $periodTo) . ', 103))';

    return $dbh->executeSet($unAllocCash, false, true, $params);
}

function getAccCodes($resultSet)
{
    $codes = [];
    if (count($resultSet ?? []) > 0) {
        $i = 0;
        foreach ($resultSet as $thisRow) {
            $acc = $thisRow['acc'];
            if ($acc !== '') {
                $codes[$i] = $acc;
            }

            $i++;
        } // end for each
    } // end if

    return $codes;
}

function getAccountName($account)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $params = [];
    $accSQL = '
		SELECT
			pmca_name
		FROM
			pmca_chart 
		WHERE
			pmca_code = ' . addSQLParam($params, $account);

    return $dbh->executeScalar($accSQL, $params);
}

/**
 * @param  string  $propertyID  Mandatory.
 * @param  string  $leaseID  Mandatory.
 * @param  string  $accountCode  Mandatory.
 *
 * <AUTHOR> Reyes
 *
 * @since 2015-09-10
 **/
function getAccountChargeType($propertyID, $leaseID, $accountCode)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $params = [];

    $sql = '
		SELECT
			pmlc_chg_type
		FROM
			pmlc_l_charge
		WHERE
			pmlc_prop=' . addSQLParam($params, $propertyID) . '
			AND pmlc_lease=' . addSQLParam($params, $leaseID) . '
			AND pmlc_acc=' . addSQLParam($params, $accountCode);

    return $dbh->executeScalar($sql, $params);
}

function getBilled($account, $propertyID, $tenantCode, $periodFrom, $periodTo, $past = true)
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);
    $params = [];
    $query = '
			SELECT COALESCE(SUM(trans_amt), 0) as amount 
			FROM ar_transaction 
			WHERE (ref_2 = ' . addSQLParam($params, $propertyID) . ') ' .
        ($tenantCode != '#' ? ' AND (ref_4 = ' . addSQLParam($params, $tenantCode) . ') ' : '') . "
			AND (trans_type <> 'CSH') 
			AND (trans_type <> 'REV') " .
        ($past ? ' AND (trans_date < CONVERT(datetime, ' . addSQLParam($params, $periodFrom) . ', 103)) '
            : ' AND (trans_date BETWEEN CONVERT(datetime, ' . addSQLParam($params, $periodFrom) . ', 103) 
                                AND CONVERT(datetime, ' . addSQLParam(
                $params,
                $periodTo
            ) . ", 103)) AND (artr_gst_code <>'GST FREE') AND (artr_gst_code <>'GSTFREE') ") . '
			AND ref_3 = ' . addSQLParam($params, $account);

    return $dbh->executeScalar($query, $params);
}

function getPaid($account, $propertyID, $tenantCode, $periodFrom)
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);
    $params = [];


    $tenantSubQuery = $tenantCode == '#' ? '' : ' AND (pmxd_lease = ' . addSQLParam($params, $tenantCode) . ')';

    $paidSQL = "
				SELECT COALESCE(SUM(pmxd_alloc_amt), 0) amount 
				FROM pmxd_ar_alloc WHERE (pmxd_f_type <> 'REV') 
				{$tenantSubQuery}
				AND (pmxd_f_type = 'CSH') 
				AND (pmxd_prop = " . addSQLParam($params, $propertyID) . ') 				
				AND (pmxd_alloc_dt < CONVERT(datetime, ' . addSQLParam($params, $periodFrom) . ', 103)) 
				AND (pmxd_acc=' . addSQLParam($params, $account) . ')
				';

    return $dbh->executeScalar($paidSQL, $params);
}

function getUnallocCash($account, $propertyID, $tenantCode, $periodFrom, $periodTo, $past = true)
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);
    $params = [];

    $unAllocCash = '
					SELECT COALESCE(SUM(pmuc_amt), 0) as grossAmount 
					FROM pmuc_unall_csh 
					WHERE (pmuc_acc = ' . addSQLParam($params, $account) . ') 
					AND (pmuc_prop = ' . addSQLParam($params, $propertyID) . ') ' .
        ($tenantCode == '#' ? '' : ' AND (pmuc_lease = ' . addSQLParam($params, $tenantCode) . ') ') .
        ($past ? ' AND (pmuc_rcpt_dt < CONVERT(datetime, ' . addSQLParam($params, $periodFrom) . ', 103))' :
            ' AND (pmuc_rcpt_dt BETWEEN CONVERT(datetime, ' . addSQLParam($params, $periodFrom) . ', 103) 
                        AND CONVERT(datetime, ' . addSQLParam($params, $periodTo) . ', 103)) ');

    return $dbh->executeScalar($unAllocCash, $params);
}

function getUnallocCashPre($account, $propertyID, $tenantCode, $periodFrom, $periodTo, $past = true)
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);
    $params = [];

    $unAllocCash = '
					SELECT COALESCE(SUM(pmuc_amt), 0) as grossAmount ,
						COALESCE(SUM(pmuc_tax_amt), 0) as GST,
						COALESCE(SUM(pmuc_net_amt), 0) as netAmount					
					FROM pmuc_unall_csh 
					WHERE (pmuc_acc = ' . addSQLParam($params, $account) . ') 
					AND (pmuc_prop = ' . addSQLParam($params, $propertyID) . ")
					AND (pmuc_gst_code = 'GSTFREE') " .
        ($tenantCode == '#' ? '' : ' AND (pmuc_lease = ' . addSQLParam($params, $tenantCode) . ') ') .
        ($past ? ' AND (pmuc_rcpt_dt < CONVERT(datetime, ' . addSQLParam($params, $periodFrom) . ', 103))' :
            ' AND (pmuc_rcpt_dt BETWEEN CONVERT(datetime, ' . addSQLParam($params, $periodFrom) . ', 103) 
                                    AND CONVERT(datetime, ' . addSQLParam($params, $periodTo) . ', 103)) ');

    // echo $unAllocCash;
    return $dbh->executeSingle($unAllocCash, $params);
}

function getUnallocCashPreTotal($account, $propertyID, $tenantCode, $periodFrom, $periodTo, $past = true)
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);
    $params = [];

    $unAllocCash = '
					SELECT COALESCE(SUM(pmuc_amt), 0) as grossAmount ,
						COALESCE(SUM(pmuc_tax_amt), 0) as GST,
						COALESCE(SUM(pmuc_net_amt), 0) as netAmount					
					FROM pmuc_unall_csh 
					WHERE (pmuc_acc = ' . addSQLParam($params, $account) . ') 
					AND (pmuc_prop = ' . addSQLParam($params, $propertyID) . ') ' .
        ($tenantCode == '#' ? '' : ' AND (pmuc_lease = ' . addSQLParam($params, $tenantCode) . ') ') .
        ($past ? ' AND (pmuc_rcpt_dt < CONVERT(datetime, ' . addSQLParam($params, $periodFrom) . ', 103))' :
            ' AND (pmuc_rcpt_dt BETWEEN CONVERT(datetime, ' . addSQLParam($params, $periodFrom) . ', 103) 
                                    AND CONVERT(datetime, ' . addSQLParam($params, $periodTo) . ', 103)) ');

    // echo $unAllocCash;
    return $dbh->executeSingle($unAllocCash, $params);
}

function getUnallocCashTaxable($propertyID, $tenantCode, $periodFrom, $periodTo, $account, $past = true)
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);
    $params = [];

    $unAllocCash = '
					SELECT COALESCE(SUM(pmuc_amt), 0) as grossAmount ,
						COALESCE(SUM(pmuc_tax_amt), 0) as GST,
						COALESCE(SUM(pmuc_net_amt), 0) as netAmount					
					FROM pmuc_unall_csh 
					WHERE (pmuc_acc = ' . addSQLParam($params, $account) . ') 
					AND (pmuc_prop = ' . addSQLParam($params, $propertyID) . ")
					AND (pmuc_gst_code = 'TAXABLE') " .
        ($tenantCode == '#' ? '' : ' AND (pmuc_lease = ' . addSQLParam($params, $tenantCode) . ') ') .
        ($past ? ' AND (pmuc_rcpt_dt < CONVERT(datetime, ' . addSQLParam($params, $periodFrom) . ', 103))' :
            ' AND (pmuc_rcpt_dt BETWEEN CONVERT(datetime, ' . addSQLParam($params, $periodFrom) . ', 103)
                        AND CONVERT(datetime, ' . addSQLParam($params, $periodTo) . ', 103)) ');
    // echo $unAllocCash;
    $result = $dbh->executeSingle($unAllocCash, $params);

    return $result;
}

function getBilledThisMonthAll($propertyID, $tenantCode, $periodFrom, $periodTo, $account, $gstFree = true)
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);
    $params = [];

    if ($gstFree) {
        $subQuery = " WHERE (artr_gst_code In ('GSTFREE','GST FREE'))";
    } else {
        $subQuery = " WHERE (artr_gst_code Not In ('GSTFREE','GST FREE')) ";
    }

    // Billed This Month (with and without gst)
    $billedSQL = "
						SELECT COALESCE(SUM(trans_amt), 0) AS amount 
						FROM ar_transaction 
						{$subQuery} 
						AND (ref_2 = " . addSQLParam($params, $propertyID) . ') ' .
        ($tenantCode == '#' ? '' : ' AND (ref_4 = ' . addSQLParam($params, $tenantCode) . ') ') . "
						AND (trans_type <> 'REV') 
						AND (trans_type <> 'CSH') 
						AND (trans_date BETWEEN CONVERT(datetime, " . addSQLParam(
            $params,
            $periodFrom
        ) . ', 103) AND CONVERT(datetime, ' . addSQLParam($params, $periodTo) . ', 103)) 
						AND (ref_3 = ' . addSQLParam($params, $account) . ')
						';

    return $dbh->executeScalar($billedSQL, $params);
}

// //total billed for accruals BAS report
function getBilledThisMonthTotal($propertyID, $periodFrom, $periodTo, $gstFree = true)
{
    if ($gstFree) {
        $subQuery = " WHERE (artr_gst_code In ('GSTFREE','GST FREE'))";
    } else {
        $subQuery = " WHERE (artr_gst_code Not In ('GSTFREE','GST FREE')) ";
    }

    // note special case for where tenant code is not needed like in the debtors by account page in the long report
    // a '#' if used as a flag to say no tenant code needed, do not use false or null as flags.

    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);
    $params = [];

    // Billed This Month (with and without gst)
    $billedSQL = "
						SELECT COALESCE(SUM(trans_amt), 0) AS amount,
												COALESCE(SUM(artr_tax_amt), 0) AS gst
						FROM ar_transaction 
						{$subQuery} 
						AND (ref_2 = " . addSQLParam($params, $propertyID) . ") 						
						AND (trans_type <> 'REV') 
						AND (trans_type <> 'CSH') 
						AND (trans_date BETWEEN CONVERT(datetime, " . addSQLParam(
        $params,
        $periodFrom
    ) . ', 103) AND CONVERT(datetime, ' . addSQLParam($params, $periodTo) . ', 103))';

    return $dbh->executeSingle($billedSQL, $params);
}

function getBilledGST($propertyID, $tenantCode, $periodFrom, $periodTo, $account)
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);
    $params = [];
    // Billed GST
    $gstSQL = "
				SELECT COALESCE(SUM(artr_tax_amt), 0) AS amount
				FROM ar_transaction 
				WHERE (artr_gst_code = 'TAXABLE')
				AND (ref_2 = " . addSQLParam($params, $propertyID) . ') ' .
        ($tenantCode == '#' ? '' : ' AND (ref_4 = ' . addSQLParam($params, $tenantCode) . ') ') . "
				AND (trans_type <> 'REV') 
				AND (trans_type <> 'CSH') 
				AND (trans_date BETWEEN CONVERT(datetime, " . addSQLParam(
            $params,
            $periodFrom
        ) . ', 103) AND CONVERT(datetime, ' . addSQLParam($params, $periodTo) . ', 103)) 
				AND (ref_3 = ' . addSQLParam($params, $account) . ')
				';

    return $dbh->executeScalar($gstSQL, $params);
}

function getRecievedThisMonthAll($propertyID, $tenantCode, $periodFrom, $periodTo, $account, $gstFree = true)
{
    $subQuery = $gstFree ? ' AND (pmxd_tax_amt = 0) ' : ' AND (pmxd_tax_amt <> 0) ';

    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);
    $params = [];

    // Received This Month GST FREE
    $receivedSQL = "
							SELECT COALESCE(SUM(pmxd_alloc_amt - pmxd_tax_amt), 0) amount 
							FROM pmxd_ar_alloc 
							WHERE (pmxd_f_type <> 'REV') 
							{$subQuery} 
							AND (pmxd_f_type = 'CSH') 
							AND (pmxd_prop = " . addSQLParam($params, $propertyID) . ') ' .
        ($tenantCode == '#' ? '' : ' AND (pmxd_lease = ' . addSQLParam($params, $tenantCode) . ') ') . '
							AND (pmxd_alloc_dt BETWEEN CONVERT(datetime, ' . addSQLParam(
            $params,
            $periodFrom
        ) . ', 103) AND CONVERT(datetime, ' . addSQLParam($params, $periodTo) . ', 103)) 
							AND (pmxd_acc=' . addSQLParam(
            $params,
            $account
        ) . ')'; // AND (pmxd_acc<>NULL) taken out 24 Jun 09

    return $dbh->executeScalar($receivedSQL, $params);
}// could change above to where pmxd_f_type ='CSH'


function getUnallocThisMonthAll($propertyID, $tenantCode, $periodFrom, $periodTo, $account)
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);
    $params = [];
    $sql = 'SELECT SUM (pmuc_tax_amt) AS amount
			FROM pmuc_unall_csh
			WHERE (pmuc_prop = ' . addSQLParam($params, $propertyID) . ')
			AND (pmuc_lease = ' . addSQLParam($params, $tenantCode) . ') 
			AND (pmuc_acc=' . addSQLParam($params, $account) . ')
			AND (pmuc_rcpt_dt BETWEEN convert(datetime,' . addSQLParam($params, $periodFrom) . ',103) 
			AND convert(datetime,' . addSQLParam($params, $periodTo) . ',103))';

    return $dbh->executeScalar($sql, $params);
}


function getGSTReceived($propertyID, $tenantCode, $periodFrom, $periodTo, $account)
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);
    $params = [];
    // GST Received
    $paidGSTSQL = "
					SELECT COALESCE(SUM(pmxd_tax_amt), 0) amount 
					FROM pmxd_ar_alloc 
					WHERE (pmxd_f_type = 'CSH')
					AND (pmxd_prop = " . addSQLParam($params, $propertyID) . ') ' .
        ($tenantCode == '#' ? '' : ' AND (pmxd_lease = ' . addSQLParam($params, $tenantCode) . ') ') . '
					AND (pmxd_alloc_dt BETWEEN CONVERT(datetime, ' . addSQLParam($params, $periodFrom) . ', 103) 
					AND CONVERT(datetime, ' . addSQLParam($params, $periodTo) . ', 103)) 
					AND (pmxd_acc=' . addSQLParam($params, $account) . ')';

    return $dbh->executeScalar($paidGSTSQL, $params); // took out on 12/12/11

}

// function getGSTReceivedPre($propertyID, $tenantCode, $periodFrom, $periodTo, $account) {
//
//    // note special case for where tenant code is not needed like in the debtors by account page in the long report
//    // a '#' if used as a flag to say no tenant code needed, do not use false or null as flags.
//    $tenantSubQuery = " AND (pmxd_lease = '{$tenantCode}') ";
//    if ($tenantCode == '#') {
//        $tenantSubQuery = "";
//    }
//
//
//    global $dbh;
//    global $clientDB;
//    $dbh->selectDatabase($clientDB);
//    //GST Received
//    $paidGSTSQL = "
//					SELECT SUM(pmxd_tax_amt) amount
//					FROM pmxd_ar_alloc
//					WHERE (pmxd_f_type = 'CSH')
//					AND (pmxd_prop = '{$propertyID}')
//					$tenantSubQuery
//
//					AND (pmxd_alloc_dt BETWEEN CONVERT(datetime, '{$periodFrom}', 103) AND CONVERT(datetime, '{$periodTo}', 103))
//					AND (pmxd_acc='{$account}')";
//
//    return $dbh->executeScalar($paidGSTSQL);//took out on 12/12/11
//
// }

// 13 July 2016 changes to account for GSt on unallocated cash
/*
$sql = "SELECT SUM (pmuc_tax_amt) AS amount, SUM (pmuc_net_amt) AS net_amount
            FROM pmuc_unall_csh
            WHERE (pmuc_prop = '{$propertyID}')
            AND (pmuc_rcpt_dt BETWEEN convert(datetime,'{$periodFrom}',103) AND convert(datetime,'{$periodTo}',103))
            AND (pmuc_acc = '{$account}')
            AND pmuc_lease = '$leaseCode'
            AND pmuc_gst_code = 'GSTFREE'";

    $unallocatedCashResult = $dbh->executeSingle($sql);
    $unallocatedCashTax = $unallocatedCashResult['amount'];
    $unallocatedCash = $unallocatedCashResult['net_amount'];

    $result['tax_amount'] = (($result1['tax_amount'] + $unallocatedCashTax));

    $net_amount = (($result1['gross_amount'] - $result1['tax_amount']) + ($unallocatedCash)); // $result1['gross_amount'];

    $result['net_amount'] = $net_amount*(-1);
    */

function getUnitDescription($propertyID, $unit_code)
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);
    $params = [];

    $checkunitoldSQL = 'SELECT pmpu_desc 
						FROM pmpu_p_unit 
						WHERE pmpu_prop = ' . addSQLParam($params, $propertyID) . ' 
						AND pmpu_unit = ' . addSQLParam($params, $unit_code);


    return $dbh->executeScalar($checkunitoldSQL, $params);
}

function checkLatestVacatedDate($propertyID, $unit_code)
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);
    $params = [];

    $latestVacDateSQL = '
						SELECT CONVERT(char(10), MAX(pmua_to_dt) , 101) as pmua_to_dt
						FROM pmua_unit_area
						WHERE pmua_prop = ' . addSQLParam($params, $propertyID) . '
						AND pmua_unit = ' . addSQLParam($params, $unit_code) . '
						';

    return $dbh->executeScalar($latestVacDateSQL, $params);
}

function checkOtherUnits($propertyID, $unit_code, $tenantCode, $periodTo)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $params = [];
    $checkunitSQL2 = '
					SELECT
						*
					FROM
						pmua_unit_area
					WHERE
						pmua_prop = ' . addSQLParam($params, $propertyID) . ' 
					AND pmua_unit = ' . addSQLParam($params, $unit_code) . '
					AND pmua_lease = ' . addSQLParam($params, $tenantCode) . '
					AND (CONVERT(datetime, ' . addSQLParam($params, $periodTo) . ', 103) BETWEEN pmua_from_dt AND pmua_to_dt)
	';

    return $dbh->executeSet($checkunitSQL2, false, true, $params);
}

function checkUnitVacated($propertyID, $unit_code, $periodTo)
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);
    $params = [];

    $checkunitSQL = '
					SELECT
						*
					FROM
						pmua_unit_area
					WHERE
						pmua_prop = ' . addSQLParam($params, $propertyID) . ' 
						AND pmua_unit = ' . addSQLParam($params, $unit_code) . "
					AND pmua_status = 'V'
					AND (CONVERT(datetime, " . addSQLParam($params, $periodTo) . ', 103) BETWEEN pmua_from_dt AND pmua_to_dt)
					';

    return $dbh->executeSet($checkunitSQL, false, true, $params);
}

// ######################## PDF FUNCTIONS #########################

function pdfHeader()
{
    global $line;
    global $pdf;
    global $startline;
    global $tenantcodeset;

    if ($line >= 400) {
        $pdf->setlinewidth(0.5);
        $pdf->moveto(245, 715 - $startline);
        $pdf->lineto(245, 40);
        $pdf->stroke();

        $pdf->moveto(300, 715 - $startline);
        $pdf->lineto(300, 40);
        $pdf->stroke();

        $pdf->moveto(500, 715 - $startline);
        $pdf->lineto(500, 40);
        $pdf->stroke();

        $pdf->moveto(400, 715 - $startline);
        $pdf->lineto(400, 40);
        $pdf->stroke();
        $pdf->moveto(555, 715 - $startline);
        $pdf->lineto(555, 40);
        $pdf->stroke();
        $pdf->moveto(40, 40); // tonkaja levaja
        $pdf->lineto(40, 715 - $startline);
        $pdf->stroke();
        $pdf->moveto(40, 40); // tonkaja levaja
        $pdf->lineto(555, 40);
        $pdf->stroke();
        $line = 40;
        $startline = $line;
        $pdf->end_page_ext('');
        $pdf->begin_page_ext(595, 842, '');
        page_headerLS('cont');

        $line = -140;

        if ($tenantcodeset == true) {
            $pdf->setFontExt($_fonts['Helvetica'], 9);
            $pdf->showBoxed("{$tenantname} (CONT'D)", 50, 460 - $line, 370, 20, 'left', '');
        }
    }
}

if (! function_exists('dbTotalIncomePerGroup')) {
    function dbTotalIncomePerGroup($prop_code, $periodFrom, $periodTo, $acctSubGroup)
    {
        global $dbh;
        global $clientDB;
        $dbh->selectDatabase($clientDB);
        $params = [];

        $sql = "SELECT COALESCE(SUM(pmxd_alloc_amt), 0) as gross_amount, 
				SUM (pmxd_tax_amt) as tax_amount
		FROM pmxd_ar_alloc
		WHERE fund IS NULL AND (pmxd_acc IN (SELECT pmcg_acc
                            		FROM pmcg_chart_grp
                            		WHERE (pmcg_grp = 'TRACC2')
                            		AND pmcg_subgrp = '{$acctSubGroup}'))
									AND (pmxd_f_type = 'CSH') 									
									AND (pmxd_prop = '{$prop_code}')
									AND (pmxd_ar_alloc.pmxd_alloc_dt>=convert(datetime," . addSQLParam(
            $params,
            $periodFrom
        ) . ',103))
									AND (pmxd_ar_alloc.pmxd_alloc_dt<=convert(datetime,' . addSQLParam(
            $params,
            $periodTo
        ) . ',103)) '; // echo "$sql <br />";

        $result1 = $dbh->executeSingle($sql, $params);

        $params = [];
        $sql = "SELECT COALESCE(SUM(pmuc_net_amt), 0)  AS amount, COALESCE(SUM(pmuc_tax_amt), 0) AS tax
				FROM pmuc_unall_csh
				WHERE (pmuc_prop = '{$prop_code}')
						AND (pmuc_acc IN (SELECT pmcg_acc
										FROM pmcg_chart_grp
										WHERE (pmcg_grp = 'TRACC2')
										AND (pmcg_subgrp = '{$acctSubGroup}')))		
						AND (pmuc_rcpt_dt BETWEEN convert(datetime," . addSQLParam($params, $periodFrom) . ',103) 
						AND convert(datetime,' . addSQLParam($params, $periodTo) . ',103))';


        $result2 = $dbh->executeSingle($sql, $params);
        $unallcNetAmt = $result2['amount'];


        $result['net_amount'] = ((-1) * (($result1['gross_amount'] - $result1['tax_amount']) + $unallcNetAmt));
        $result['gross_amount'] = ((-1) * ($result1['gross_amount'] + $result2['amount'] + $result2['tax']));

        return $result;
    }
}

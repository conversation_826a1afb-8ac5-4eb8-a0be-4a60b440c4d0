<?php

/**
 * Created by PhpStorm.
 * User: adacanay
 * Date: 8/1/2017
 * Time: 12:23 PM
 */
// New method, dependent with CashPaymentsSummaryFund include report file
if (! function_exists('acctName')) {
    function acctName($code, $group_code)
    {
        global $clientDB, $dbh;
        $dbh->selectDatabase($clientDB);
        $isCodeInGroupSQL = 'SELECT pmca_code FROM pmca_chart WHERE pmca_code = ? AND pmca_gl_account_group2 = ?';

        return $dbh->executeScalar($isCodeInGroupSQL, [$code, $group_code]);
    }
}

// transferred here from framework/commands/managementReports/functions/propertyCashReceiptsSummaryFund.php
if (! function_exists('isCodeInGroup')) {
    function isCodeInGroup($code, $group_code)
    {
        global $clientDB, $dbh;
        $dbh->selectDatabase($clientDB);
        $isCodeInGroupSQL = 'SELECT pmca_code FROM pmca_chart WHERE pmca_code = ? AND pmca_gl_account_group2 = ?';

        return $dbh->executeScalar($isCodeInGroupSQL, [$code, $group_code]);
    }
}


$fund = getPropertyFund($propertyID);
$type = [
    'INC.OWN' => 'INCOWN',
    'INC.REC' => 'INCDR',
    'INC.OUT' => 'INCOUT',
    'EXP.OWN' => 'EXPOWN',
    'EXP.REC' => 'EXPDR',
    'EXP.OUT' => 'EXPOUT',
];
$typeTitle = [
    'INC.OWN' => 'Owner Receipts',
    'INC.REC' => 'Recoverable Receipts',
    'INC.OUT' => ucwords(strtolower($_SESSION['country_default']['variable_outgoings'])) . ' Receipts',
    'EXP.OWN' => 'Cash Expenditure',
    'EXP.REC' => 'Recoverable Expenditure',
    'EXP.OUT' => ucwords(strtolower($_SESSION['country_default']['variable_outgoings'])) . ' Expenditure',
];

/** START OF PROPERTY FINANCIAL REPORT RECEIPTS */
$data = [];

foreach ($fund as $v) {

    $incGroup = dbGetPropertyFundAccountGroup($propertyID, 'INC');

    unset($fundAccountsArray);
    $fundAccountsArray = [];

    foreach ($incGroup as $vv) {
        $fundAccounts = dbGetPropertyFundByAccountsGroupWithName($propertyID, $vv, $v['fund']);

        $totalActualMonth = 0;
        $totalBudgetMonth = 0;
        $totalVarianceMonth = 0;
        $totalVarMonth = 0;
        $totalActualYear = 0;
        $totalBudgetYear = 0;
        $totalVarianceYear = 0;
        $totalVarYear = 0;
        $totalBudgetYear = 0;

        foreach ($fundAccounts as $tmp) {
            $fundAccountsArray[] = [
                'code'          => $tmp['code'],
                'account_name'  => $tmp['accountName'],
            ];
        }

        foreach ($fundAccountsArray as $fa) {

            $is_code_in_group = (isCodeInGroup($fa['code'], $vv) == $fa['code']);

            if ($is_code_in_group) {
                $ownerIncome                = dbTotalIncomePerGroupFund($propertyID, $periodFrom, $periodTo, $fa['code']);
                $ownerIncomeA               = dbTotalIncomePerGroupFund($propertyID, $startFinancialYear, $periodTo, $fa['code']);
                $ownerincome_display_rec    = $ownerIncome['net_amount'];
                $ownerincomeA_display       = $ownerIncomeA['net_amount'];
                $budgetINCOWN_display       = budgetReceipt($propertyID, $pmcp_period, $pmcp_year, $fa['code']);
                $budgetINCOWN_display_rec   = $budgetINCOWN_display;
                $incOWNVar_display          = $ownerincome_display_rec - $budgetINCOWN_display;
                $incOWNVar_display_rec      = $incOWNVar_display;

                if ($budgetINCOWN_display == 0) {
                    if ($incOWNVar_display == 0) {
                        $incOWNVP = 0;
                    } elseif ($incOWNVar_display < 0) {
                        $incOWNVP = -100;
                    } else {
                        $incOWNVP = 100;
                    }
                } elseif ($incOWNVar_display == 0) {
                    $incOWNVP = 0;
                } else {
                    $incOWNVP = $incOWNVar_display / $budgetINCOWN_display * 100;
                }

                $incOWNVP_display       = $incOWNVP;
                $incOWNVP_display_rec   = $incOWNVP_display;
                $budgetINCOWN           = getYearBudgetIncFund($propertyID, $periodTo, $fa['code']);
                $budgetINCVOY_display   = ($budgetINCOWN);

                if ($incOWNVP_display != 0) {

                    $totalActualMonth   += $ownerincome_display_rec;
                    $totalBudgetMonth   += $budgetINCOWN_display_rec;
                    $totalVarianceMonth += $incOWNVar_display_rec;
                    $totalVarMonth      += $incOWNVP_display_rec;
                    $totalActualYear    += $ownerincomeA_display;
                    $totalBudgetYear    += $budgetINCOWN_display;
                    $totalVarianceYear  += $incOWNVar_display;
                    $totalVarYear       += $incOWNVP_display;
                    $totalBudgetYear2   += $budgetINCVOY_display;

                    $data[$v['fundName']][$vv]['data'][$fa['code']] = [
                        'account_name'              => $fa['code'] . ' - ' . $fa['account_name'],
                        'ownerincome_display_rec'   => $ownerincome_display_rec,
                        'budgetINCOWN_display_rec'  => $budgetINCOWN_display_rec,
                        'incOWNVar_display_rec'     => $incOWNVar_display_rec,
                        'incOWNVP_display_rec'      => $incOWNVP_display_rec,
                        'ownerincomeA_display'      => $ownerincomeA_display,
                        'budgetINCOWN_display'      => $budgetINCOWN_display,
                        'incOWNVar_display'         => $incOWNVar_display,
                        'incOWNVP_display'          => $incOWNVP_display,
                        'budgetINCVOY_display'      => $budgetINCVOY_display,
                    ];

                    $data[$v['fundName']][$vv]['sub_total'] = [
                        'totalActualMonth'  => $totalActualMonth,
                        'totalBudgetMonth'  => $totalBudgetMonth,
                        'totalVarianceMonth' => $totalVarianceMonth,
                        'totalVarMonth'     => $totalVarMonth,
                        'totalActualYear'   => $totalActualYear,
                        'totalBudgetYear'   => $totalBudgetYear,
                        'totalVarianceYear' => $totalVarianceYear,
                        'totalVarYear'      => $totalVarYear,
                        'totalBudgetYear2'  => $totalBudgetYear2,
                    ];
                }
            }
        }

    }
}

// Compute for Total
foreach ($data as $key => $value) {
    $totalActualMonthAll    = 0;
    $totalBudgetMonthAll    = 0;
    $totalVarianceMonthAll  = 0;
    $totalVarMonthAll       = 0;
    $totalActualYearAll     = 0;
    $totalBudgetYearAll     = 0;
    $totalVarianceYearAll   = 0;
    $totalVarYearAll        = 0;
    $totalBudgetYearAll2    = 0;

    foreach ($value as $value1) {
        $totalActualMonthAll    += $value1['sub_total']['totalActualMonth'];
        $totalBudgetMonthAll    += $value1['sub_total']['totalBudgetMonth'];
        $totalVarianceMonthAll  += $value1['sub_total']['totalVarianceMonth'];
        $totalVarMonthAll       += $value1['sub_total']['totalVarMonth'];
        $totalActualYearAll     += $value1['sub_total']['totalActualYear'];
        $totalBudgetYearAll     += $value1['sub_total']['totalBudgetYear'];
        $totalVarianceYearAll   += $value1['sub_total']['totalVarianceYear'];
        $totalVarYearAll        += $value1['sub_total']['totalVarYear'];
        $totalBudgetYearAll2    += $value1['sub_total']['totalBudgetYear2'];
    }

    $data[$key]['total'] = [
        'totalActualMonthAll'   => $totalActualMonthAll,
        'totalBudgetMonthAll'   => $totalBudgetMonthAll,
        'totalVarianceMonthAll' => $totalVarianceMonthAll,
        'totalVarMonthAll'      => $totalVarMonthAll,
        'totalActualYearAll'    => $totalActualYearAll,
        'totalBudgetYearAll'    => $totalBudgetYearAll,
        'totalVarianceYearAll'  => $totalVarianceYearAll,
        'totalVarYearAll'       => $totalVarYearAll,
        'totalBudgetYearAll2'   => $totalBudgetYearAll2,
    ];
}

// Clean data transfer data array value into another key if owner display and variance display is equals to zero
foreach ($data as $key => $value) {
    foreach ($value as $key1 => $value1) {
        foreach ($value1['data'] as $key2 => $value2) {
            if ($value2['ownerincome_display_rec'] == 0) {
                $data[$key][$key1]['deleted_data'][$key2] = $value2;
                unset($data[$key][$key1]['data'][$key2]);
            }
        }
    }
}

// Printing of PDF
$gray_x_axis = 215;
$gray_y_axis = 445;
$gray_width  = 612;
$gray_height = 20;
$move        = 0;
foreach ($data as $key => $value) {
    $lineV      = 0;
    $starting   = 365;
    $page++;
    $pdf->begin_page_ext(842, 595, '');
    $page_header = 'Property Financial Report';

    // Bypass
    $v['fundName'] = $key;

    include __DIR__ . '/fundReportHeader.php';
    if ($logo) {
        generateLogo('landscape');
    }

    $pdf->setFontExt($_fonts['Helvetica-Bold'], 8);

    foreach ($value as $key1 => $value1) {
        if ($key1 != 'total' && count($value1['data']) != 0) {
            // Group Title
            $pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
            $pdf->showBoxed($typeTitle[$key1], 22, $starting - $lineV, 275, 95, 'left', '');
            $lineV += 12;

            foreach ($value1['data'] as $value2) {
                $pdf->setFontExt($_fonts['Helvetica'], 8);
                $pdf->showBoxed($value2['account_name'], 22, $starting - $lineV, 275, 95, 'left', '');
                $variance = $value2['ownerincomeA_display'] - $value2['budgetINCOWN_display'];
                $pdf->showBoxed(formatting($value2['ownerincome_display_rec'], 2), 205, $starting - $lineV, 75, 95, 'right', '');
                $pdf->showBoxed(formatting($value2['budgetINCOWN_display_rec'], 2), 272, $starting - $lineV, 75, 95, 'right', '');
                $pdf->showBoxed(formatting($value2['incOWNVar_display_rec'], 2), 330, $starting - $lineV, 75, 95, 'right', '');
                $pdf->showBoxed(formatting($value2['incOWNVP_display_rec'], 2), 385, $starting - $lineV, 75, 95, 'right', '');
                $pdf->showBoxed(formatting($value2['ownerincomeA_display'], 2), 475, $starting - $lineV, 75, 95, 'right', '');
                $pdf->showBoxed(formatting($value2['budgetINCOWN_display'], 2), 550, $starting - $lineV, 75, 95, 'right', '');
                $pdf->showBoxed(formatting($variance, 2), 620, $starting - $lineV, 75, 95, 'right', '');
                $pdf->showBoxed(calculateVariancePercentage($value2['ownerincomeA_display'], $value2['budgetINCOWN_display']), 665, $starting - $lineV, 75, 95, 'right', '');
                $pdf->showBoxed(formatting($value2['budgetINCVOY_display'], 2), 745, $starting - $lineV, 75, 95, 'right', '');

                $lineV += 12;
            }

            $lineV += 12;

            // Sub-total
            $pdf->setColorExt('fill', 'rgb', 0.88, 0.88, 0.88, 0);
            $pdf->rect($gray_x_axis, $gray_y_axis - $lineV, $gray_width - 3, $gray_height);
            $pdf->fill();
            $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
            $pdf->setlinewidth(0.5);
            $pdf->moveto(215, $gray_y_axis - $lineV);
            $pdf->lineto(824, $gray_y_axis - $lineV);
            $pdf->stroke();
            $pdf->moveto(215, ($gray_y_axis + 20) - $lineV);
            $pdf->lineto(824, ($gray_y_axis + 20) - $lineV);
            $pdf->stroke();

            $pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
            $pdf->showBoxed(formatting($value1['sub_total']['totalActualMonth'], 2), 205, $starting - $lineV, 75, 95, 'right', '');
            $pdf->showBoxed(formatting($value1['sub_total']['totalBudgetMonth'], 2), 272, $starting - $lineV, 75, 95, 'right', '');
            $pdf->showBoxed(formatting($value1['sub_total']['totalVarianceMonth'], 2), 330, $starting - $lineV, 75, 95, 'right', '');
            $pdf->showBoxed(formatting($value1['sub_total']['totalVarMonth'], 2), 385, $starting - $lineV, 75, 95, 'right', '');
            $pdf->showBoxed(formatting($value1['sub_total']['totalActualYear'], 2), 475, $starting - $lineV, 75, 95, 'right', '');
            $pdf->showBoxed(formatting($value1['sub_total']['totalBudgetYear'], 2), 550, $starting - $lineV, 75, 95, 'right', '');
            $pdf->showBoxed(formatting($value1['sub_total']['totalActualYear'] - $value1['sub_total']['totalBudgetYear'], 2), 620, $starting - $lineV, 75, 95, 'right', '');
            $pdf->showBoxed(calculateVariancePercentage($value1['sub_total']['totalActualYear'], $value1['sub_total']['totalBudgetYear']), 665, $starting - $lineV, 75, 95, 'right', '');
            $pdf->showBoxed(formatting($value1['sub_total']['totalBudgetYear2'], 2), 745, $starting - $lineV, 75, 95, 'right', '');

            $lineV += 12;
        }
    }

    // Total Gray Rectangle Box + Title
    $move = $gray_y_axis - ($lineV + 24);
    $pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
    $pdf->showBoxed('Net Cash Excluding ' . $_SESSION['country_default']['tax_label'], 22, $move - 12, 275, 30, 'left', '');
    $pdf->setColorExt('fill', 'rgb', 0.88, 0.88, 0.88, 0);
    $pdf->rect(215, $move, $gray_width - 3, $gray_height);
    $pdf->fill();
    $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
    // $pdf->setlinewidth(0.3);
    // $pdf->moveto(215, $move);
    // $pdf->lineto(824, $move);
    // $pdf->stroke();
    $pdf->moveto(215, ($move + 20));
    $pdf->lineto(824, ($move + 20));
    $pdf->stroke();
    $pdf->setlinewidth(0.5);
    $pdf->moveto(18, $move);
    $pdf->lineto(824, $move);
    $pdf->stroke();

    // Total Figures
    $pdf->showBoxed(formatting($value['total']['totalActualMonthAll'], 2), 205, $move - 12, 75, 30, 'right', '');
    $pdf->showBoxed(formatting($value['total']['totalBudgetMonthAll'], 2), 272, $move - 12, 75, 30, 'right', '');
    $pdf->showBoxed(formatting($value['total']['totalVarianceMonthAll'], 2), 330, $move - 12, 75, 30, 'right', '');
    $pdf->showBoxed(formatting($value['total']['totalVarMonthAll'], 2), 385, $move - 12, 75, 30, 'right', '');
    $pdf->showBoxed(formatting($value['total']['totalActualYearAll'], 2), 475, $move - 12, 75, 30, 'right', '');
    $pdf->showBoxed(formatting($value['total']['totalBudgetYearAll'], 2), 550, $move - 12, 75, 30, 'right', '');
    $pdf->showBoxed(formatting($value['total']['totalActualYearAll'] - $value['total']['totalBudgetYearAll'], 2), 620, $move - 12, 75, 30, 'right', '');
    $pdf->showBoxed(calculateVariancePercentage($value['total']['totalActualYearAll'], $value['total']['totalBudgetYearAll']), 665, $move - 12, 75, 30, 'right', '');
    $pdf->showBoxed(formatting($value['total']['totalBudgetYearAll2'], 2), 745, $move - 12, 75, 30, 'right', '');

    // Inner Vertical Lines
    $pdf->setlinewidth(0.5);
    $pdf->moveto(215, 505);
    $pdf->lineto(215, $gray_y_axis - ($lineV + 24));
    $pdf->stroke();
    $pdf->moveto(470, 505);
    $pdf->lineto(470, $gray_y_axis - ($lineV + 24));
    $pdf->stroke();
    $pdf->moveto(745, 505);
    $pdf->lineto(745, $gray_y_axis - ($lineV + 24));
    $pdf->stroke();
    $pdf->moveto(18, $gray_y_axis - ($lineV + 24));
    $pdf->lineto(18, 505);
    $pdf->stroke();
    $pdf->moveto(824, $gray_y_axis - ($lineV + 24));
    $pdf->lineto(824, 505);
    $pdf->stroke();

    // New Page Fallback
    // if ($lineV>$pageLimit) _newPage($pdf,$lineV, $page);

    // Footer Section
    $pdf->setFontExt($_fonts['Helvetica'], 8);
    // $pdf->showBoxed ("Printed on $date", 22, 10, 275, 30, "left", "");
    $pdf->showBoxed("Page {$page}", 750, 0, 75, 30, 'right', '');
    $pdf->setFontExt($_fonts['Helvetica'], 8);
    $traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'propertyFinancialReport', A4_LANDSCAPE);
    $traccFooter->prerender($pdf);
    $pdf->end_page_ext('');
}

/** END OF PROPERTY FINANCIAL REPORT RECEIPTS */

/** START OF PROPERTY FINANCIAL REPORT PAYMENT */
unset($data);
$data = [];

foreach ($fund as $v) {

    $expGroup = dbGetPropertyFundAccountGroup($propertyID, 'EXP');

    unset($fundAccountsArray);
    $fundAccountsArray = [];

    foreach ($expGroup as $vv) {
        $fundAccounts = dbGetPropertyFundByAccountsGroupWithName($propertyID, $vv, $v['fund']);

        $totalActualMonth = 0;
        $totalBudgetMonth = 0;
        $totalVarianceMonth = 0;
        $totalVarMonth = 0;
        $totalActualYear = 0;
        $totalBudgetYear = 0;
        $totalVarianceYear = 0;
        $totalVarYear = 0;
        $totalBudgetYear = 0;

        $totalActualMonthExp = 0;
        $totalBudgetMonthExp = 0;
        $totalVarianceMonthExp = 0;
        $totalVarMonthExp = 0;
        $totalActualYearExp = 0;
        $totalBudgetYearExp = 0;
        $totalVarianceYearExp = 0;
        $totalVarYearExp = 0;
        $totalBudgetYear2Exp = 0;

        foreach ($fundAccounts as $tmp) {
            $fundAccountsArray[] = [
                'code'          => $tmp['code'],
                'account_name'  => $tmp['accountName'],
            ];
        }

        foreach ($fundAccountsArray as $fa) {

            $is_code_in_group = (isCodeInGroup($fa['code'], $vv) == $fa['code']);

            if ($is_code_in_group) {
                $ownerIncome                = totalExpensesPerGroupFund($propertyID, $periodFrom, $periodTo, $fa['code']);
                $ownerIncomeA               = totalExpensesPerGroupFund($propertyID, $startFinancialYear, $periodTo, $fa['code']);
                $ownerincome_display_rec    = $ownerIncome['net_amount'];
                $ownerincomeA_display       = $ownerIncomeA['net_amount'];
                $budgetINCOWN_display       = budgetReceipt($propertyID, $pmcp_period, $pmcp_year, $fa['code']);
                $budgetINCOWN_display_rec   = $budgetINCOWN_display;
                $incOWNVar_display          = $ownerincome_display_rec - $budgetINCOWN_display;
                $incOWNVar_display_rec      = $incOWNVar_display;

                if ($budgetINCOWN_display == 0) {
                    if ($incOWNVar_display == 0) {
                        $incOWNVP = 0;
                    } elseif ($incOWNVar_display < 0) {
                        $incOWNVP = -100;
                    } else {
                        $incOWNVP = 100;
                    }
                } elseif ($incOWNVar_display == 0) {
                    $incOWNVP = 0;
                } else {
                    $incOWNVP = $incOWNVar_display / $budgetINCOWN_display * 100;
                }

                $incOWNVP_display       = $incOWNVP;
                $incOWNVP_display_rec   = $incOWNVP_display;
                $budgetINCOWN           = getBudgetExpYTDFund($propertyID, $toPeriod, $periodTo, $fa['code']);
                $budgetINCVOY_display   = formatting($budgetINCOWN, 2);
                // file_put_contents('C:\xampp\htdocs\logs.txt', $fa['code']."=>".$budgetINCOWN_display.PHP_EOL, FILE_APPEND | LOCK_EX);
                if ($incOWNVP_display != 0) {

                    $totalActualMonthExp   += $ownerincome_display_rec;
                    $totalBudgetMonthExp   += $budgetINCOWN_display_rec;
                    $totalVarianceMonthExp += $incOWNVar_display_rec;
                    $totalVarMonthExp      += $incOWNVP_display_rec;
                    $totalActualYearExp    += $ownerincomeA_display;
                    $totalBudgetYearExp    += $budgetINCOWN_display;
                    $totalVarianceYearExp  += $incOWNVar_display;
                    $totalVarYearExp       += $incOWNVP_display;
                    $totalBudgetYear2Exp   += $budgetINCVOY_display;

                    $data[$v['fundName']][$vv]['data'][$fa['code']] = [
                        'account_name'              => $fa['account_name'],
                        'ownerincome_display_rec'   => $ownerincome_display_rec,
                        'budgetINCOWN_display_rec'  => $budgetINCOWN_display_rec,
                        'incOWNVar_display_rec'     => $incOWNVar_display_rec,
                        'incOWNVP_display_rec'      => $incOWNVP_display_rec,
                        'ownerincomeA_display'      => $ownerincomeA_display,
                        'budgetINCOWN_display'      => $budgetINCOWN_display,
                        'incOWNVar_display'         => $incOWNVar_display,
                        'incOWNVP_display'          => $incOWNVP_display,
                        'budgetINCVOY_display'      => $budgetINCVOY_display,
                    ];

                    $data[$v['fundName']][$vv]['sub_total'] = [
                        'totalActualMonthExp'  => $totalActualMonthExp,
                        'totalBudgetMonthExp'  => $totalBudgetMonthExp,
                        'totalVarianceMonthExp' => $totalVarianceMonth,
                        'totalVarMonthExp'     => $totalVarMonthExp,
                        'totalActualYearExp'   => $totalActualYearExp,
                        'totalBudgetYearExp'   => $totalBudgetYearExp,
                        'totalVarianceYearExp' => $totalVarianceYearExp,
                        'totalVarYearExp'      => $totalVarYearExp,
                        'totalBudgetYear2Exp'  => $totalBudgetYear2Exp,
                    ];
                }
            }
        }

    }
}

// Compute for Total
foreach ($data as $key => $value) {
    $totalActualMonthAll    = 0;
    $totalBudgetMonthAll    = 0;
    $totalVarianceMonthAll  = 0;
    $totalVarMonthAll       = 0;
    $totalActualYearAll     = 0;
    $totalBudgetYearAll     = 0;
    $totalVarianceYearAll   = 0;
    $totalVarYearAll        = 0;
    $totalBudgetYearAll2    = 0;

    foreach ($value as $value1) {
        $totalActualMonthAll    += $value1['sub_total']['totalActualMonthExp'];
        $totalBudgetMonthAll    += $value1['sub_total']['totalBudgetMonthExp'];
        $totalVarMonthAll       += $value1['sub_total']['totalVarMonthExp'];
        $totalActualYearAll     += $value1['sub_total']['totalActualYearExp'];
        $totalBudgetYearAll     += $value1['sub_total']['totalBudgetYearExp'];
        $totalVarianceYearAll   += $value1['sub_total']['totalVarianceYearExp'];
        $totalVarYearAll        += $value1['sub_total']['totalVarYearExp'];
        $totalBudgetYearAll2    += $value1['sub_total']['totalBudgetYear2Exp'];
    }

    $data[$key]['total'] = [
        'totalActualMonthAll'   => $totalActualMonthAll,
        'totalBudgetMonthAll'   => $totalBudgetMonthAll,
        'totalVarianceMonthAll' => $totalVarianceMonthAll,
        'totalVarMonthAll'      => $totalVarMonthAll,
        'totalActualYearAll'    => $totalActualYearAll,
        'totalBudgetYearAll'    => $totalBudgetYearAll,
        'totalVarianceYearAll'  => $totalVarianceYearAll,
        'totalVarYearAll'       => $totalVarYearAll,
        'totalBudgetYearAll2'   => $totalBudgetYearAll2,
    ];
}

// Clean data transfer data array value into another key if owner display and variance display is equals to zero
foreach ($data as $key => $value) {
    foreach ($value as $key1 => $value1) {
        foreach ($value1['data'] as $key2 => $value2) {
            if ($value2['ownerincome_display_rec'] == 0) {
                $data[$key][$key1]['deleted_data'][$key2] = $value2;
                unset($data[$key][$key1]['data'][$key2]);
            }
        }
    }
}

// Printing of PDF
$gray_x_axis = 215;
$gray_y_axis = 445;
$gray_width  = 612;
$gray_height = 20;
$move        = 0;
foreach ($data as $key => $value) {
    $lineV      = 0;
    $starting   = 365;
    $page++;
    $pdf->begin_page_ext(842, 595, '');
    $page_header = 'Property Financial Report';

    // Bypass
    $v['fundName'] = $key;

    include __DIR__ . '/fundReportHeader.php';
    if ($logo) {
        generateLogo('landscape');
    }

    $pdf->setFontExt($_fonts['Helvetica-Bold'], 8);

    foreach ($value as $key1 => $value1) {
        if ($key1 != 'total' && count($value1['data']) != 0) {
            // Group Title
            $pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
            $pdf->showBoxed($typeTitle[$key1], 22, $starting - $lineV, 275, 95, 'left', '');
            $lineV += 12;

            foreach ($value1['data'] as $value2) {
                $pdf->setFontExt($_fonts['Helvetica'], 8);
                $pdf->showBoxed($value2['account_name'], 22, $starting - $lineV, 275, 95, 'left', '');
                $pdf->showBoxed(formatting($value2['ownerincome_display_rec'], 2), 205, $starting - $lineV, 75, 95, 'right', '');
                $pdf->showBoxed(formatting($value2['budgetINCOWN_display_rec'], 2), 272, $starting - $lineV, 75, 95, 'right', '');
                $pdf->showBoxed(formatting($value2['incOWNVar_display_rec'], 2), 330, $starting - $lineV, 75, 95, 'right', '');
                $pdf->showBoxed(formatting($value2['incOWNVP_display_rec'], 2), 385, $starting - $lineV, 75, 95, 'right', '');
                $pdf->showBoxed(formatting($value2['ownerincomeA_display'], 2), 475, $starting - $lineV, 75, 95, 'right', '');
                $pdf->showBoxed(formatting($value2['budgetINCOWN_display'], 2), 550, $starting - $lineV, 75, 95, 'right', '');
                $pdf->showBoxed(formatting($value2['incOWNVar_display'], 2), 620, $starting - $lineV, 75, 95, 'right', '');
                $pdf->showBoxed(formatting($value2['incOWNVP_display'], 2), 665, $starting - $lineV, 75, 95, 'right', '');
                $pdf->showBoxed(formatting($value2['budgetINCVOY_display'], 2), 745, $starting - $lineV, 75, 95, 'right', '');

                $lineV += 12;
            }

            $lineV += 12;

            // Sub-total
            $pdf->setColorExt('fill', 'rgb', 0.88, 0.88, 0.88, 0);
            $pdf->rect($gray_x_axis, $gray_y_axis - $lineV, $gray_width - 3, $gray_height);
            $pdf->fill();
            $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
            $pdf->setlinewidth(0.5);
            $pdf->moveto(215, $gray_y_axis - $lineV);
            $pdf->lineto(824, $gray_y_axis - $lineV);
            $pdf->stroke();
            $pdf->moveto(215, ($gray_y_axis + 20) - $lineV);
            $pdf->lineto(824, ($gray_y_axis + 20) - $lineV);
            $pdf->stroke();

            $pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
            $pdf->showBoxed(formatting($value1['sub_total']['totalActualMonthExp'], 2), 205, $starting - $lineV, 75, 95, 'right', '');
            $pdf->showBoxed(formatting($value1['sub_total']['totalBudgetMonthExp'], 2), 272, $starting - $lineV, 75, 95, 'right', '');
            $pdf->showBoxed(formatting($value1['sub_total']['totalVarianceMonthExp'], 2), 330, $starting - $lineV, 75, 95, 'right', '');
            $pdf->showBoxed(formatting($value1['sub_total']['totalVarMonthExp'], 2), 385, $starting - $lineV, 75, 95, 'right', '');
            $pdf->showBoxed(formatting($value1['sub_total']['totalActualYearExp'], 2), 475, $starting - $lineV, 75, 95, 'right', '');
            $pdf->showBoxed(formatting($value1['sub_total']['totalBudgetYearExp'], 2), 550, $starting - $lineV, 75, 95, 'right', '');
            $pdf->showBoxed(formatting($value1['sub_total']['totalVarianceYearExp'], 2), 620, $starting - $lineV, 75, 95, 'right', '');
            $pdf->showBoxed(formatting($value1['sub_total']['totalVarYearExp'], 2), 665, $starting - $lineV, 75, 95, 'right', '');
            $pdf->showBoxed(formatting($value1['sub_total']['totalBudgetYear2Exp'], 2), 745, $starting - $lineV, 75, 95, 'right', '');

            $lineV += 12;
        }
    }

    // Total Gray Rectangle Box + Title
    $move = $gray_y_axis - ($lineV + 24);
    $pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
    $pdf->showBoxed('Net Cash Excluding ' . $_SESSION['country_default']['tax_label'], 22, $move - 12, 275, 30, 'left', '');
    $pdf->setColorExt('fill', 'rgb', 0.88, 0.88, 0.88, 0);
    $pdf->rect(215, $move, $gray_width - 3, $gray_height);
    $pdf->fill();
    $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
    // $pdf->setlinewidth(0.3);
    // $pdf->moveto(215, $move);
    // $pdf->lineto(824, $move);
    // $pdf->stroke();
    $pdf->moveto(215, ($move + 20));
    $pdf->lineto(824, ($move + 20));
    $pdf->stroke();
    $pdf->setlinewidth(0.5);
    $pdf->moveto(18, $move);
    $pdf->lineto(824, $move);
    $pdf->stroke();

    // Total Figures
    $pdf->showBoxed(formatting($value['total']['totalActualMonthAll'], 2), 205, $move - 12, 75, 30, 'right', '');
    $pdf->showBoxed(formatting($value['total']['totalBudgetMonthAll'], 2), 272, $move - 12, 75, 30, 'right', '');
    $pdf->showBoxed(formatting($value['total']['totalVarianceMonthAll'], 2), 330, $move - 12, 75, 30, 'right', '');
    $pdf->showBoxed(formatting($value['total']['totalVarMonthAll'], 2), 385, $move - 12, 75, 30, 'right', '');
    $pdf->showBoxed(formatting($value['total']['totalActualYearAll'], 2), 475, $move - 12, 75, 30, 'right', '');
    $pdf->showBoxed(formatting($value['total']['totalBudgetYearAll'], 2), 550, $move - 12, 75, 30, 'right', '');
    $pdf->showBoxed(formatting($value['total']['totalVarianceYearAll'], 2), 620, $move - 12, 75, 30, 'right', '');
    $pdf->showBoxed(formatting($value['total']['totalVarYearAll'], 2), 665, $move - 12, 75, 30, 'right', '');
    $pdf->showBoxed(formatting($value['total']['totalBudgetYearAll2'], 2), 745, $move - 12, 75, 30, 'right', '');

    // Inner Vertical Lines
    $pdf->setlinewidth(0.5);
    $pdf->moveto(215, 505);
    $pdf->lineto(215, $gray_y_axis - ($lineV + 24));
    $pdf->stroke();
    $pdf->moveto(470, 505);
    $pdf->lineto(470, $gray_y_axis - ($lineV + 24));
    $pdf->stroke();
    $pdf->moveto(745, 505);
    $pdf->lineto(745, $gray_y_axis - ($lineV + 24));
    $pdf->stroke();
    $pdf->moveto(18, $gray_y_axis - ($lineV + 24));
    $pdf->lineto(18, 505);
    $pdf->stroke();
    $pdf->moveto(824, $gray_y_axis - ($lineV + 24));
    $pdf->lineto(824, 505);
    $pdf->stroke();

    // New Page Fallback
    // if ($lineV>$pageLimit) _newPage($pdf,$lineV, $page);

    // Footer Section
    $pdf->setFontExt($_fonts['Helvetica'], 8);
    // $pdf->showBoxed ("Printed on $date", 22, 10, 275, 30, "left", "");
    $pdf->showBoxed("Page {$page}", 750, 0, 75, 30, 'right', '');
    $pdf->setFontExt($_fonts['Helvetica'], 8);
    $traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'propertyFinancialReport', A4_LANDSCAPE);
    $traccFooter->prerender($pdf);
    $pdf->end_page_ext('');
}

/** END OF PROPERTY FINANCIAL REPORT PAYMENT */

/** START OF CASH RECEIPT SUMMARY */
$data = [];

foreach ($fund as $v) {
    $incGroup = dbGetPropertyFundAccountGroup($propertyID, 'INC');

    unset($fundAccountsArray);
    $fundAccountsArray = [];

    foreach ($incGroup as $vv) {
        $fundAccounts = dbGetPropertyFundByAccountsGroupWithName($propertyID, $vv, $v['fund']);

        $owner_displayTotal = 0;
        $budgetINCOWN_displayTotal = 0;
        $variance_displayTotal = 0;
        $varianceP_displayTotal = 0;
        $ownerA_displayTotal = 0;
        $budgetINCOWNY_displayTotal = 0;
        $varianceY_displayTotal = 0;
        $variancePY_displayTotal = 0;
        $budgetINCOWNYEAR_displayTotal = 0;

        foreach ($fundAccounts as $fa) {
            $is_code_in_group = (isCodeInGroup($fa['code'], $vv) == $fa['code']);

            if ($is_code_in_group) {
                $ownerinc_result = dbOwnerIncome($fa['code'], $propertyID, $periodFrom, $periodTo);
                $ownerUC_income = dbOwnerUCIncome($fa['code'], $propertyID, $periodFrom, $periodTo);
                $owner_income = $ownerinc_result['amount'];
                $owner_tax_income = $ownerinc_result['tax_amount'];
                // owner receipts actual Income Per Period
                $owner_income = $owner_income - $owner_tax_income + $ownerUC_income;
                $ownerPOS_income = $owner_income * (-1);
                $owner_display = $ownerPOS_income;
                // owner receipts actual Income Per Year
                $ownerincA_result = dbOwnerIncome($fa['code'], $propertyID, $startFinancialYear, $periodTo);
                $ownerincUC_result = dbOwnerUCIncome($fa['code'], $propertyID, $startFinancialYear, $periodTo);
                $openBalOWN_income = dbOpeningBalance($propertyID, $startFinancialYear, $periodTo, $account);
                $ownerUC_incomeA = (float) $ownerincUC_result;
                $ownerA_income = (float) $ownerincA_result['amount'];
                $ownerA_tax_income = (float) $ownerincA_result['tax_amount'];
                $ownerA_income = $ownerA_income - $ownerA_tax_income + $ownerUC_incomeA - $openBalOWN_income;
                $ownerAPOS_income = $ownerA_income * (-1);
                $ownerA_display = $ownerAPOS_income;

                // owner receipts budget Per Period
                $budgetINCOWN = dbBudget($fa['code'], $propertyID, $pmcp_period, $pmcp_year);
                $budgetINCOWN_display = $budgetINCOWN;
                // owner receipts budget Per Year
                $budgetINCOWNY = dbBudgetYear($propertyID, $toPeriod, $pmcp_year, $fa['code']);
                $budgetINCOWNY_display = $budgetINCOWNY;
                $budgetINCOWNYEAR = dbBudgetWholeYear($propertyID, $pmcp_year, $fa['code']);
                $budgetINCOWNYEAR_display = $budgetINCOWNYEAR;
                // variance period
                $variance = $ownerPOS_income - $budgetINCOWN;
                $variance_display = $variance;
                if ($budgetINCOWN == 0) {
                    if ($ownerPOS_income == 0) {
                        $varianceP = 0;
                    } else {
                        $varianceP = 100;
                        if ($variance < 0) {
                            $varianceP = -100;
                        }
                    }
                } else {
                    $varianceP = $variance / $budgetINCOWN * 100;
                }

                $varianceP_display = $varianceP;
                // variance year
                $varianceY = $ownerAPOS_income - $budgetINCOWNY;
                $varianceY_display = $varianceY;
                // variance percent year
                $varianceY = $ownerAPOS_income - $budgetINCOWNY;
                $varianceY_display = $varianceY;
                if ($budgetINCOWNY == 0) {
                    $variancePY = 100;
                    if ($varianceY < 0) {
                        $variancePY = -100;
                    }
                } else {
                    $variancePY = $varianceY / $budgetINCOWNY * 100;
                }

                $variancePY_display = $variancePY;
                $ownerA_display = $ownerAPOS_income;

                if ($owner_display != 0) {
                    $owner_displayTotal             += $owner_display;
                    $budgetINCOWN_displayTotal      += $budgetINCOWN_display;
                    $variance_displayTotal          += $variance_display;
                    $varianceP_displayTotal         += $varianceP_display;
                    $ownerA_displayTotal            += $ownerA_display;
                    $budgetINCOWNY_displayTotal     += $budgetINCOWNY_display;
                    $varianceY_displayTotal         += $varianceY_display;
                    $variancePY_displayTotal        += $variancePY_display;
                    $budgetINCOWNYEAR_displayTotal  += $budgetINCOWNYEAR_display;
                }

                $data[$v['fundName']][$vv]['data'][$fa['code']] = [
                    'account_name'          => $fa['code'] . ' - ' . $fa['accountName'],
                    'owner_display'         => $owner_display,
                    'budgetINCOWN_display'  => $budgetINCOWN_display,
                    'variance_display'      => $variance_display,
                    'varianceP_display'     => $varianceP_display,
                    'ownerA_display'        => $ownerA_display,
                    'budgetINCOWNY_display' => $budgetINCOWNY_display,
                    'varianceY_display'     => $varianceY_display,
                    'variancePY_display'    => $variancePY_display,
                    '$budgetINCOWNYEAR_display' => $budgetINCOWNYEAR_display,
                ];
                $data[$v['fundName']][$vv]['sub_total'] = [
                    'owner_displayTotal' 		=> $owner_displayTotal,
                    'budgetINCOWN_displayTotal' => $budgetINCOWN_displayTotal,
                    'variance_displayTotal' 	=> $variance_displayTotal,
                    'varianceP_displayTotal' 	=> $varianceP_displayTotal,
                    'ownerA_displayTotal' 		=> $ownerA_displayTotal,
                    'budgetINCOWNY_displayTotal' => $budgetINCOWNY_displayTotal,
                    'varianceY_displayTotal' 	=> $varianceY_displayTotal,
                    'variancePY_displayTotal' 	=> $variancePY_displayTotal,
                    'budgetINCOWNYEAR_displayTotal' => $budgetINCOWNYEAR_displayTotal,
                ];
            }
        }
    }
}

// Compute for Total
foreach ($data as $key => $value) {
    $owner_displayTotalAll = 0;
    $budgetINCOWN_displayTotalAll = 0;
    $variance_displayTotalAll = 0;
    $varianceP_displayTotalAll = 0;
    $ownerA_displayTotalAll = 0;
    $budgetINCOWNY_displayTotalAll = 0;
    $varianceY_displayTotalAll = 0;
    $variancePY_displayTotalAll = 0;
    $budgetINCOWNYEAR_displayTotalAll = 0;

    foreach ($value as $value1) {
        $owner_displayTotalAll          += $value1['sub_total']['owner_displayTotal'];
        $budgetINCOWN_displayTotalAll   += $value1['sub_total']['budgetINCOWN_displayTotal'];
        $variance_displayTotalAll       += $value1['sub_total']['variance_displayTotal'];
        $varianceP_displayTotalAll      += $value1['sub_total']['varianceP_displayTotal'];
        $ownerA_displayTotalAll         += $value1['sub_total']['ownerA_displayTotal'];
        $budgetINCOWNY_displayTotalAll  += $value1['sub_total']['budgetINCOWNY_displayTotal'];
        $varianceY_displayTotalAll      += $value1['sub_total']['varianceY_displayTotal'];
        $variancePY_displayTotalAll     += $value1['sub_total']['variancePY_displayTotal'];
        $budgetINCOWNYEAR_displayTotalAll += $value1['sub_total']['budgetINCOWNYEAR_displayTotal'];
    }

    $data[$key]['total'] = [
        'owner_displayTotalAll'        => $owner_displayTotalAll,
        'budgetINCOWN_displayTotalAll' => $budgetINCOWN_displayTotalAll,
        'variance_displayTotalAll'     => $variance_displayTotalAll,
        'varianceP_displayTotalAll'    => $varianceP_displayTotalAll,
        'ownerA_displayTotalAll'       => $ownerA_displayTotalAll,
        'budgetINCOWNY_displayTotalAll' => $budgetINCOWNY_displayTotalAll,
        'varianceY_displayTotalAll'    => $varianceY_displayTotalAll,
        'variancePY_displayTotalAll'   => $variancePY_displayTotalAll,
        'budgetINCOWNYEAR_displayTotalAll' => $budgetINCOWNYEAR_displayTotalAll,
    ];
}


// Clean data transfer data array value into another key if owner display and variance display is equals to zero
foreach ($data as $key => $value) {
    foreach ($value as $key1 => $value1) {
        foreach ($value1['data'] as $key2 => $value2) {
            if ($value2['owner_display'] == 0 && $value2['variance_display'] == 0) {
                $data[$key][$key1]['deleted_data'][$key2] = $value2;
                unset($data[$key][$key1]['data'][$key2]);
            }
        }
    }
}

// Printing of PDF
$gray_x_axis = 215;
$gray_y_axis = 445;
$gray_width  = 612;
$gray_height = 20;
$move        = 0;
foreach ($data as $key => $value) {
    $lineV      = 0;
    $starting   = 365;
    $page++;
    $pdf->begin_page_ext(842, 595, '');
    $page_header = 'Cash Receipts Summary';

    // Bypass
    $v['fundName'] = $key;

    include __DIR__ . '/fundReportHeader.php';
    if ($logo) {
        generateLogo('landscape');
    }

    $pdf->setFontExt($_fonts['Helvetica-Bold'], 8);

    foreach ($value as $key1 => $value1) {
        if ($key1 != 'total' && count($value1['data']) != 0) {
            // Group Title
            $pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
            $pdf->showBoxed($typeTitle[$key1], 22, $starting - $lineV, 275, 95, 'left', '');
            $lineV += 12;

            foreach ($value1['data'] as $value2) {
                $pdf->setFontExt($_fonts['Helvetica'], 8);
                $pdf->showBoxed($value2['account_name'], 22, $starting - $lineV, 275, 95, 'left', '');
                $pdf->showBoxed(formatting($value2['owner_display'], 2), 205, $starting - $lineV, 75, 95, 'right', '');
                $pdf->showBoxed(formatting($value2['budgetINCOWN_display'], 2), 272, $starting - $lineV, 75, 95, 'right', '');
                $pdf->showBoxed(formatting($value2['variance_display'], 2), 330, $starting - $lineV, 75, 95, 'right', '');
                $pdf->showBoxed(formatting($value2['varianceP_display'], 2), 385, $starting - $lineV, 75, 95, 'right', '');
                $pdf->showBoxed(formatting($value2['ownerA_display'], 2), 475, $starting - $lineV, 75, 95, 'right', '');
                $pdf->showBoxed(formatting($value2['budgetINCOWNY_display'], 2), 550, $starting - $lineV, 75, 95, 'right', '');
                $pdf->showBoxed(formatting($value2['varianceY_display'], 2), 620, $starting - $lineV, 75, 95, 'right', '');
                $pdf->showBoxed(formatting($value2['variancePY_display'], 2), 665, $starting - $lineV, 75, 95, 'right', '');
                $pdf->showBoxed(formatting($value2['$budgetINCOWNYEAR_display'], 2), 745, $starting - $lineV, 75, 95, 'right', '');

                $lineV += 12;
            }

            $lineV += 12;

            // Sub-total
            $pdf->setColorExt('fill', 'rgb', 0.88, 0.88, 0.88, 0);
            $pdf->rect($gray_x_axis, $gray_y_axis - $lineV, $gray_width - 3, $gray_height);
            $pdf->fill();
            $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
            $pdf->setlinewidth(0.5);
            $pdf->moveto(215, $gray_y_axis - $lineV);
            $pdf->lineto(824, $gray_y_axis - $lineV);
            $pdf->stroke();
            $pdf->moveto(215, ($gray_y_axis + 20) - $lineV);
            $pdf->lineto(824, ($gray_y_axis + 20) - $lineV);
            $pdf->stroke();

            $pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
            $pdf->showBoxed(formatting($value1['sub_total']['owner_displayTotal'], 2), 205, $starting - $lineV, 75, 95, 'right', '');
            $pdf->showBoxed(formatting($value1['sub_total']['budgetINCOWN_displayTotal'], 2), 272, $starting - $lineV, 75, 95, 'right', '');
            $pdf->showBoxed(formatting($value1['sub_total']['variance_displayTotal'], 2), 330, $starting - $lineV, 75, 95, 'right', '');
            $pdf->showBoxed(formatting($value1['sub_total']['varianceP_displayTotal'], 2), 385, $starting - $lineV, 75, 95, 'right', '');
            $pdf->showBoxed(formatting($value1['sub_total']['ownerA_displayTotal'], 2), 475, $starting - $lineV, 75, 95, 'right', '');
            $pdf->showBoxed(formatting($value1['sub_total']['budgetINCOWNY_displayTotal'], 2), 550, $starting - $lineV, 75, 95, 'right', '');
            $pdf->showBoxed(formatting($value1['sub_total']['varianceY_displayTotal'], 2), 620, $starting - $lineV, 75, 95, 'right', '');
            $pdf->showBoxed(formatting($value1['sub_total']['variancePY_displayTotal'], 2), 665, $starting - $lineV, 75, 95, 'right', '');
            $pdf->showBoxed(formatting($value1['sub_total']['budgetINCOWNYEAR_displayTotal'], 2), 745, $starting - $lineV, 75, 95, 'right', '');

            $lineV += 12;
        }
    }



    // Total Gray Rectangle Box + Title
    $move = $gray_y_axis - ($lineV + 24);
    $pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
    $pdf->showBoxed('Total Cash Receipts', 22, $move - 12, 275, 30, 'left', '');
    $pdf->setColorExt('fill', 'rgb', 0.88, 0.88, 0.88, 0);
    $pdf->rect(215, $move, $gray_width - 3, $gray_height);
    $pdf->fill();
    $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
    // $pdf->setlinewidth(0.3);
    // $pdf->moveto(215, $move);
    // $pdf->lineto(824, $move);
    // $pdf->stroke();
    $pdf->moveto(215, ($move + 20));
    $pdf->lineto(824, ($move + 20));
    $pdf->stroke();
    $pdf->setlinewidth(0.5);
    $pdf->moveto(18, $move);
    $pdf->lineto(824, $move);
    $pdf->stroke();

    // Total Figures
    $pdf->showBoxed(formatting($value['total']['owner_displayTotalAll'], 2), 205, $move - 12, 75, 30, 'right', '');
    $pdf->showBoxed(formatting($value['total']['budgetINCOWN_displayTotalAll'], 2), 272, $move - 12, 75, 30, 'right', '');
    $pdf->showBoxed(formatting($value['total']['variance_displayTotalAll'], 2), 330, $move - 12, 75, 30, 'right', '');
    $pdf->showBoxed(formatting($value['total']['varianceP_displayTotalAll'], 2), 385, $move - 12, 75, 30, 'right', '');
    $pdf->showBoxed(formatting($value['total']['ownerA_displayTotalAll'], 2), 475, $move - 12, 75, 30, 'right', '');
    $pdf->showBoxed(formatting($value['total']['budgetINCOWNY_displayTotalAll'], 2), 550, $move - 12, 75, 30, 'right', '');
    $pdf->showBoxed(formatting($value['total']['varianceY_displayTotalAll'], 2), 620, $move - 12, 75, 30, 'right', '');
    $pdf->showBoxed(calculateVariancePercentage($value['total']['ownerA_displayTotalAll'], $value['total']['budgetINCOWNY_displayTotalAll']), 665, $move - 12, 75, 30, 'right', '');
    $pdf->showBoxed(formatting($value['total']['budgetINCOWNYEAR_displayTotalAll'], 2), 745, $move - 12, 75, 30, 'right', '');

    // Inner Vertical Lines
    $pdf->setlinewidth(0.5);
    $pdf->moveto(215, 505);
    $pdf->lineto(215, $gray_y_axis - ($lineV + 24));
    $pdf->stroke();
    $pdf->moveto(470, 505);
    $pdf->lineto(470, $gray_y_axis - ($lineV + 24));
    $pdf->stroke();
    $pdf->moveto(745, 505);
    $pdf->lineto(745, $gray_y_axis - ($lineV + 24));
    $pdf->stroke();
    $pdf->moveto(18, $gray_y_axis - ($lineV + 24));
    $pdf->lineto(18, 505);
    $pdf->stroke();
    $pdf->moveto(824, $gray_y_axis - ($lineV + 24));
    $pdf->lineto(824, 505);
    $pdf->stroke();

    // New Page Fallback
    if ($lineV > $pageLimit) {
        _newPage($pdf, $lineV, $page);
    }

    // Footer Section
    $pdf->setFontExt($_fonts['Helvetica'], 8);
    // $pdf->showBoxed ("Printed on $date", 22, 10, 275, 30, "left", "");
    $pdf->showBoxed("Page {$page}", 750, 0, 75, 30, 'right', '');
    $pdf->setFontExt($_fonts['Helvetica'], 8);
    $traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'cashReceiptsAndPayments', A4_LANDSCAPE);
    $traccFooter->prerender($pdf);
    $pdf->end_page_ext('');
}

/** END OF CASH RECEIPT SUMMARY */

/** START OF CASH PAYMENT SUMMARY */
$dataE = [];

foreach ($fund as $v) {
    $incGroup = dbGetPropertyFundAccountGroup($propertyID, 'EXP');

    unset($fundAccountsArray);
    $fundAccountsArray = [];

    foreach ($incGroup as $vvE) {
        $fundAccounts = dbGetPropertyFundByAccountsGroupWithName($propertyID, $vvE, $v['fund']);

        $owner_displayTotal = 0;
        $budgetINCOWN_displayTotal = 0;
        $variance_displayTotal = 0;
        $varianceP_displayTotal = 0;
        $ownerA_displayTotal = 0;
        $budgetINCOWNY_displayTotal = 0;
        $varianceY_displayTotal = 0;
        $variancePY_displayTotal = 0;
        $budgetINCOWNYEAR_displayTotal = 0;

        foreach ($fundAccounts as $fa) {
            $is_code_in_group = (isCodeInGroup($fa['code'], $vvE) == $fa['code']);

            if ($is_code_in_group) {
                $ownerinc_result = dbOwnerExpense($fa['code'], $propertyID, $periodFrom, $periodTo);
                $ownerUC_income = dbOwnerUCExpense($fa['code'], $propertyID, $periodFrom, $periodTo);
                $owner_income = $ownerinc_result['amount'];
                $owner_tax_income = $ownerinc_result['tax_amount'];
                // owner receipts actual Income Per Period
                $owner_income = $owner_income - $owner_tax_income + $ownerUC_income;
                $ownerPOS_income = $owner_income;
                $owner_display = $ownerPOS_income;
                // owner receipts actual Income Per Year
                $ownerincA_result = dbOwnerExpense($fa['code'], $propertyID, $startFinancialYear, $periodTo);
                $ownerincUC_result = dbOwnerUCExpense($fa['code'], $propertyID, $startFinancialYear, $periodTo);
                $openBalOWN_income = dbOpeningBalanceExp($propertyID, $startFinancialYear, $periodTo, $account);
                $ownerUC_incomeA = (float) $ownerincUC_result;
                $ownerA_income = (float) $ownerincA_result['amount'];
                $ownerA_tax_income = (float) $ownerincA_result['tax_amount'];
                $ownerA_income = $ownerA_income - $ownerA_tax_income + $ownerUC_incomeA - $openBalOWN_income;
                $ownerAPOS_income = $ownerA_income;
                $ownerA_display = $ownerAPOS_income;

                // owner receipts budget Per Period
                $budgetINCOWN = dbBudgetExp($fa['code'], $propertyID, $pmcp_period, $pmcp_year);
                $budgetINCOWN_display = $budgetINCOWN;
                // owner receipts budget Per Year
                $budgetINCOWNY = dbBudgetExpYear($propertyID, $toPeriod, $pmcp_year, $fa['code']);
                $budgetINCOWNY_display = $budgetINCOWNY;
                $budgetINCOWNYEAR = dbBudgetExpWholeYear($propertyID, $pmcp_year, $fa['code']);
                $budgetINCOWNYEAR_display = $budgetINCOWNYEAR;
                // variance period
                $variance = $ownerPOS_income - $budgetINCOWN;
                $variance_display = $variance;
                if ($budgetINCOWN == 0) {
                    if ($ownerPOS_income == 0) {
                        $varianceP = 0;
                    } else {
                        $varianceP = 100;
                        if ($variance < 0) {
                            $varianceP = -100;
                        }
                    }
                } else {
                    $varianceP = $variance / $budgetINCOWN * 100;
                }

                $varianceP_display = $varianceP;
                // variance year
                $varianceY = $ownerAPOS_income - $budgetINCOWNY;
                $varianceY_display = $varianceY;
                // variance percent year
                $varianceY = $ownerAPOS_income - $budgetINCOWNY;
                $varianceY_display = $varianceY;
                if ($budgetINCOWNY == 0) {
                    $variancePY = 100;
                    if ($varianceY < 0) {
                        $variancePY = -100;
                    }
                } else {
                    $variancePY = $varianceY / $budgetINCOWNY * 100;
                }

                $variancePY_display = $variancePY;
                $ownerA_display = $ownerAPOS_income;

                if ($owner_display != 0) {
                    $owner_displayTotal             += $owner_display;
                    $budgetINCOWN_displayTotal      += $budgetINCOWN_display;
                    $variance_displayTotal          += $variance_display;
                    $varianceP_displayTotal         += $varianceP_display;
                    $ownerA_displayTotal            += $ownerA_display;
                    $budgetINCOWNY_displayTotal     += $budgetINCOWNY_display;
                    $varianceY_displayTotal         += $varianceY_display;
                    $variancePY_displayTotal        += $variancePY_display;
                    $budgetINCOWNYEAR_displayTotal  += $budgetINCOWNYEAR_display;
                }

                $dataE[$v['fundName']][$vvE]['data'][$fa['code']] = [
                    'account_name' => $fa['code'] . ' - ' . $fa['accountName'],
                    'owner_display' => $owner_display,
                    'budgetINCOWN_display' => $budgetINCOWN_display,
                    'variance_display' => $variance_display,
                    'varianceP_display' => $varianceP_display,
                    'ownerA_display' => $ownerA_display,
                    'budgetINCOWNY_display' => $budgetINCOWNY_display,
                    'varianceY_display' => $varianceY_display,
                    'variancePY_display' => $variancePY_display,
                    '$budgetINCOWNYEAR_display' => $budgetINCOWNYEAR_display,
                ];
                $dataE[$v['fundName']][$vvE]['sub_total'] = [
                    'owner_displayTotal' => $owner_displayTotal,
                    'budgetINCOWN_displayTotal' => $budgetINCOWN_displayTotal,
                    'variance_displayTotal' => $variance_displayTotal,
                    'varianceP_displayTotal' => $varianceP_displayTotal,
                    'ownerA_displayTotal' => $ownerA_displayTotal,
                    'budgetINCOWNY_displayTotal' => $budgetINCOWNY_displayTotal,
                    'varianceY_displayTotal' => $varianceY_displayTotal,
                    'variancePY_displayTotal' => $variancePY_displayTotal,
                    'budgetINCOWNYEAR_displayTotal' => $budgetINCOWNYEAR_displayTotal,
                ];
            }
        }
    }
}

// Compute for Total
foreach ($dataE as $key => $value) {
    $owner_displayTotalAll = 0;
    $budgetINCOWN_displayTotalAll = 0;
    $variance_displayTotalAll = 0;
    $varianceP_displayTotalAll = 0;
    $ownerA_displayTotalAll = 0;
    $budgetINCOWNY_displayTotalAll = 0;
    $varianceY_displayTotalAll = 0;
    $variancePY_displayTotalAll = 0;
    $budgetINCOWNYEAR_displayTotalAll = 0;

    foreach ($value as $value1) {
        $owner_displayTotalAll          += $value1['sub_total']['owner_displayTotal'];
        $budgetINCOWN_displayTotalAll   += $value1['sub_total']['budgetINCOWN_displayTotal'];
        $variance_displayTotalAll       += $value1['sub_total']['variance_displayTotal'];
        $varianceP_displayTotalAll      += $value1['sub_total']['varianceP_displayTotal'];
        $ownerA_displayTotalAll         += $value1['sub_total']['ownerA_displayTotal'];
        $budgetINCOWNY_displayTotalAll  += $value1['sub_total']['budgetINCOWNY_displayTotal'];
        $varianceY_displayTotalAll      += $value1['sub_total']['varianceY_displayTotal'];
        $variancePY_displayTotalAll     += $value1['sub_total']['variancePY_displayTotal'];
        $budgetINCOWNYEAR_displayTotalAll += $value1['sub_total']['budgetINCOWNYEAR_displayTotal'];
    }

    $dataE[$key]['total'] = [
        'owner_displayTotalAll'        => $owner_displayTotalAll,
        'budgetINCOWN_displayTotalAll' => $budgetINCOWN_displayTotalAll,
        'variance_displayTotalAll'     => $variance_displayTotalAll,
        'varianceP_displayTotalAll'    => $varianceP_displayTotalAll,
        'ownerA_displayTotalAll'       => $ownerA_displayTotalAll,
        'budgetINCOWNY_displayTotalAll' => $budgetINCOWNY_displayTotalAll,
        'varianceY_displayTotalAll'    => $varianceY_displayTotalAll,
        'variancePY_displayTotalAll'   => $variancePY_displayTotalAll,
        'budgetINCOWNYEAR_displayTotalAll' => $budgetINCOWNYEAR_displayTotalAll,
    ];
}

// Clean data transfer data array value into another key if owner display and variance display is equals to zero
foreach ($dataE as $key => $value) {
    foreach ($value as $key1 => $value1) {
        foreach ($value1['data'] as $key2 => $value2) {
            if ($value2['owner_display'] == 0 && $value2['variance_display'] == 0) {
                $dataE[$key][$key1]['deleted_data'][$key2] = $value2;
                unset($dataE[$key][$key1]['data'][$key2]);
            }
        }
    }
}

// Printing of PDF
$gray_x_axis = 215;
$gray_y_axis = 445;
$gray_width  = 612;
$gray_height = 20;
$move        = 0;
foreach ($dataE as $key => $value) {
    $lineE      = 0;
    $starting   = 365;
    $page++;
    $pdf->begin_page_ext(842, 595, '');
    $page_header = 'Cash Payments Summary';

    // Bypass
    $v['fundName'] = $key;

    include __DIR__ . '/fundReportHeader.php';
    if ($logo) {
        generateLogo('landscape');
    }

    $pdf->setFontExt($_fonts['Helvetica-Bold'], 8);

    foreach ($value as $key1 => $value1) {
        if ($key1 != 'total' && count($value1['data']) != 0) {
            // Group Title
            $pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
            $pdf->showBoxed($typeTitle[$key1], 22, $starting - $lineE, 275, 95, 'left', '');
            $lineE += 12;

            foreach ($value1['data'] as $value2) {
                $pdf->setFontExt($_fonts['Helvetica'], 8);
                $pdf->showBoxed($value2['account_name'], 22, $starting - $lineE, 275, 95, 'left', '');
                $pdf->showBoxed(formatting($value2['owner_display'], 2), 205, $starting - $lineE, 75, 95, 'right', '');
                $pdf->showBoxed(formatting($value2['budgetINCOWN_display'], 2), 272, $starting - $lineE, 75, 95, 'right', '');
                $pdf->showBoxed(formatting($value2['variance_display'], 2), 330, $starting - $lineE, 75, 95, 'right', '');
                $pdf->showBoxed(formatting($value2['varianceP_display'], 2), 385, $starting - $lineE, 75, 95, 'right', '');
                $pdf->showBoxed(formatting($value2['ownerA_display'], 2), 475, $starting - $lineE, 75, 95, 'right', '');
                $pdf->showBoxed(formatting($value2['budgetINCOWNY_display'], 2), 550, $starting - $lineE, 75, 95, 'right', '');
                $pdf->showBoxed(formatting($value2['varianceY_display'], 2), 620, $starting - $lineE, 75, 95, 'right', '');
                $pdf->showBoxed(formatting($value2['variancePY_display'], 2), 665, $starting - $lineE, 75, 95, 'right', '');
                $pdf->showBoxed(formatting($value2['budgetINCOWNYEAR_display'], 2), 745, $starting - $lineE, 75, 95, 'right', '');

                $lineE += 12;
            }

            $lineE += 12;

            // Sub-total
            $pdf->setColorExt('fill', 'rgb', 0.88, 0.88, 0.88, 0);
            $pdf->rect($gray_x_axis, $gray_y_axis - $lineE, $gray_width - 3, $gray_height);
            $pdf->fill();
            $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
            $pdf->setlinewidth(0.5);
            $pdf->moveto(215, $gray_y_axis - $lineE);
            $pdf->lineto(824, $gray_y_axis - $lineE);
            $pdf->stroke();
            $pdf->moveto(215, ($gray_y_axis + 20) - $lineE);
            $pdf->lineto(824, ($gray_y_axis + 20) - $lineE);
            $pdf->stroke();

            $pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
            $pdf->showBoxed(formatting($value1['sub_total']['owner_displayTotal'], 2), 205, $starting - $lineE, 75, 95, 'right', '');
            $pdf->showBoxed(formatting($value1['sub_total']['budgetINCOWN_displayTotal'], 2), 272, $starting - $lineE, 75, 95, 'right', '');
            $pdf->showBoxed(formatting($value1['sub_total']['variance_displayTotal'], 2), 330, $starting - $lineE, 75, 95, 'right', '');
            $pdf->showBoxed(formatting($value1['sub_total']['varianceP_displayTotal'], 2), 385, $starting - $lineE, 75, 95, 'right', '');
            $pdf->showBoxed(formatting($value1['sub_total']['ownerA_displayTotal'], 2), 475, $starting - $lineE, 75, 95, 'right', '');
            $pdf->showBoxed(formatting($value1['sub_total']['budgetINCOWNY_displayTotal'], 2), 550, $starting - $lineE, 75, 95, 'right', '');
            $pdf->showBoxed(formatting($value1['sub_total']['varianceY_displayTotal'], 2), 620, $starting - $lineE, 75, 95, 'right', '');
            $pdf->showBoxed(formatting($value1['sub_total']['variancePY_displayTotal'], 2), 665, $starting - $lineE, 75, 95, 'right', '');
            $pdf->showBoxed(formatting($value1['sub_total']['budgetINCOWNYEAR_displayTotal'], 2), 745, $starting - $lineE, 75, 95, 'right', '');

            $lineE += 12;
        }
    }

    // Total Gray Rectangle Box + Title
    $move = $gray_y_axis - ($lineE + 24);
    $pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
    $pdf->showBoxed('Total Cash Payments', 22, $move - 12, 275, 30, 'left', '');
    $pdf->setColorExt('fill', 'rgb', 0.88, 0.88, 0.88, 0);
    $pdf->rect(215, $move, $gray_width - 3, $gray_height);
    $pdf->fill();
    $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
    // $pdf->setlinewidth(0.3);
    // $pdf->moveto(215, $move);
    // $pdf->lineto(824, $move);
    // $pdf->stroke();
    $pdf->moveto(215, ($move + 20));
    $pdf->lineto(824, ($move + 20));
    $pdf->stroke();
    $pdf->setlinewidth(0.5);
    $pdf->moveto(18, $move);
    $pdf->lineto(824, $move);
    $pdf->stroke();

    // Total Figures
    $pdf->showBoxed(formatting($value['total']['owner_displayTotalAll'], 2), 205, $move - 12, 75, 30, 'right', '');
    $pdf->showBoxed(formatting($value['total']['budgetINCOWN_displayTotalAll'], 2), 272, $move - 12, 75, 30, 'right', '');
    $pdf->showBoxed(formatting($value['total']['variance_displayTotalAll'], 2), 330, $move - 12, 75, 30, 'right', '');
    $pdf->showBoxed(formatting($value['total']['varianceP_displayTotalAll'], 2), 385, $move - 12, 75, 30, 'right', '');
    $pdf->showBoxed(formatting($value['total']['ownerA_displayTotalAll'], 2), 475, $move - 12, 75, 30, 'right', '');
    $pdf->showBoxed(formatting($value['total']['budgetINCOWNY_displayTotalAll'], 2), 550, $move - 12, 75, 30, 'right', '');
    $pdf->showBoxed(formatting($value['total']['varianceY_displayTotalAll'], 2), 620, $move - 12, 75, 30, 'right', '');
    $pdf->showBoxed(formatting($value['total']['variancePY_displayTotalAll'], 2), 665, $move - 12, 75, 30, 'right', '');
    $pdf->showBoxed(formatting($value['total']['budgetINCOWNYEAR_displayTotalAll'], 2), 745, $move - 12, 75, 30, 'right', '');

    // Inner Vertical Lines
    $pdf->setlinewidth(0.5);
    $pdf->moveto(215, 505);
    $pdf->lineto(215, $gray_y_axis - ($lineE + 24));
    $pdf->stroke();
    $pdf->moveto(470, 505);
    $pdf->lineto(470, $gray_y_axis - ($lineE + 24));
    $pdf->stroke();
    $pdf->moveto(745, 505);
    $pdf->lineto(745, $gray_y_axis - ($lineE + 24));
    $pdf->stroke();
    $pdf->moveto(18, $gray_y_axis - ($lineE + 24));
    $pdf->lineto(18, 505);
    $pdf->stroke();
    $pdf->moveto(824, $gray_y_axis - ($lineE + 24));
    $pdf->lineto(824, 505);
    $pdf->stroke();

    // New Page Fallback
    if ($lineE > $pageLimit) {
        _newPage($pdf, $lineE, $page);
    }

    // Footer Section
    $pdf->setFontExt($_fonts['Helvetica'], 8);
    // $pdf->showBoxed ("Printed on $date", 22, 10, 275, 30, "left", "");
    $pdf->showBoxed("Page {$page}", 750, 0, 75, 30, 'right', '');
    $pdf->setFontExt($_fonts['Helvetica'], 8);
    $traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'cashReceiptsAndPayments', A4_LANDSCAPE);
    $traccFooter->prerender($pdf);
    $pdf->end_page_ext('');
}

/** END OF CASH PAYMENT SUMMARY */

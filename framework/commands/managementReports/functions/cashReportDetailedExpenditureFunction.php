<?php


function newPageDE($startNewPage = false, $propertyID = null)
{
    global $pdf;
    global $currentYCoord;
    global $currentXcoord;
    global $pageXCoord;
    global $pageYCoord;
    global $lineHeight;
    global $page;
    global $date;
    global $client;
    global $propertyName;
    global $periodDescription;
    global $periodFrom;
    global $periodTo;
    global $logo;
    global $_fonts;

    if ($startNewPage) {
        $pdf->begin_page_ext($pageXCoord, $pageYCoord, '');
        $page_header = 'Payments';
    } else {
        $page_header =  "Payments (Cont'd)";
        $pdf->end_page_ext('');
        $page++;


        // STAR NEW PAGE
        $pdf->begin_page_ext($pageXCoord, $pageYCoord, '');

    }
    // include "includes/newdetailedExpenditureIncludeLS.php";
    // ###################################################################################


    $pdf->setFontExt($_fonts['Helvetica-Bold'], 9);
    $pdf->showBoxed("{$page_header}", 100, 690, 400, 20, 'center', '');
    $pdf->setFontExt($_fonts['Helvetica'], 8);

    $pdf->show_xy('Owner: ', 25, 750);
    $pdf->continue_text('Property: ');
    $pdf->continue_text('Report for: ');

    $pdf->show_xy("{$client}", 85, 750);
    $propertyID ? $pdf->continue_text("{$propertyName} [{$propertyID}]") : $pdf->continue_text("{$propertyName}");
    $pdf->continue_text("{$periodDescription}");

    $pdf->showBoxed("Period From: {$periodFrom} To: {$periodTo}", 100, 675, 400, 20, 'center', '');
    // $pdf->show_xy("Period From: $periodFrom_display To: $periodTo_display", 220, 685-$_pdfline);


    // ###############INSERT LOGO HERE ############################################

    if ($logo) {
        generateLogo();
    }

    // ###############INSERT LOGO HERE ############################################

    // $pdf->setColorExt("both", "rgb", 0,0,0, 0);
    $pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
    // $text1 = "Owner Code";
    // $text2 = "Account Code";
    $text3 = 'Supplier';
    $text4 = 'Description';
    $text6 = "From\nDate";
    $text7 = "To\nDate";
    $text8 = "Net\n" . $_SESSION['country_default']['currency_symbol'];
    $text9 = $_SESSION['country_default']['tax_label'] . "\n" . $_SESSION['country_default']['currency_symbol'];
    $text10 = "Total\n" . $_SESSION['country_default']['currency_symbol'];
    // $text11 = "Payment\nDate";

    // $pdf->set_parameter ("underline", "true");
    // $pdf->showBoxed ($text1, 22, 480, 35, 30, "left", "");
    // $pdf->showBoxed ($text2, 25, 480, 35, 30, "left", "");
    $pdf->showBoxed($text3, 30, 645, 75, 30, 'left', '');
    $pdf->showBoxed($text4, 164, 645, 75, 30, 'left', '');
    // $pdf->showBoxed ($text5, 310, 480, 375, 30, "left", "");
    // $pdf->showBoxed ($text11, 515, 480, 55, 30, "center", "");
    $pdf->showBoxed($text6, 240, 645, 75, 30, 'center', '');
    $pdf->showBoxed($text7, 300, 645, 75, 30, 'center', '');
    $pdf->showBoxed($text8, 360, 645, 70, 30, 'center', '');
    $pdf->showBoxed($text9, 430, 645, 70, 30, 'center', '');
    $pdf->showBoxed($text10, 500, 645, 70, 30, 'center', '');
    // $pdf->set_parameter ("underline", "false");

    $pdf->setlinewidth(0.5);
    // top header
    $pdf->moveto(25, 675);
    $pdf->lineto(570, 675);
    $pdf->stroke();

    // header line(no need for update)
    $pdf->moveto(25, 650);
    $pdf->lineto(570, 650);
    $pdf->stroke();
    /*
        $pdf->setlinewidth(0.5);
    //bottom line
        $pdf->moveto(40, 40+100);
        $pdf->lineto(555, 40+100);
        $pdf->stroke();
    //left side bar line
        $pdf->moveto(40, 40+100);
        $pdf->lineto(40, 675);
        $pdf->stroke();

    //net left line
        $pdf->moveto(120+225, 40+100);
        $pdf->lineto(120+225, 675);
        $pdf->stroke();
    //net right line
        $pdf->moveto(190+225, 40+100);
        $pdf->lineto(190+225, 675);
        $pdf->stroke();

        //gst right line
        $pdf->moveto(260+225, 40+100);
        $pdf->lineto(260+225, 675);
        $pdf->stroke();

    //right side bar line
        $pdf->moveto(555, 40+100);
        $pdf->lineto(555, 675);
        $pdf->stroke();
    */
    // footer

    $pdf->setFontExt($_fonts['Helvetica'], 8);
    // $pdf->showBoxed ("Printed on $date", 30+9, 2, 275, 30, "left", "");
    $pdf->showBoxed("Page {$page}", 520 + 9, 2, 75, 30, 'left', '');

    // insert tracc header
    $traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'cirrus8CashExpenditure', A4_PORTRAIT);
    $traccFooter->prerender($pdf);


    // #######################################################################################

    $currentYCoord = 620;
    $currentXcoord = 50;
    $lineHeight = 12;


}


function printGroupDE($groupArray, $groupName, $printGrandTotal = false)
{


    global $pdf;
    global $currentYCoord;
    global $currentXcoord;
    global $lineHeight;
    global $page;
    global $grandTotalNet;
    global $grandTotalTax;
    global $grandTotalGross;
    global $amtWidth;
    global $groupNameWidth;
    global $_fonts;

    global $propertyID;

    $group_net_sum = 0;
    $group_tax_sum = 0;
    $group_gross_sum = 0;

    $groupNameWidth = 275;
    $supplierWidth = 275;
    $descriptionWidth = 76;
    $amtWidth = 275;


    $numRows = count($groupArray ?? []);
    $rowCount = 0;

    // if(count($groupArray == 0)) { $groupName.= " (none)"; }

    if ($currentYCoord >= 40) {

        $pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
        $pdf->showBoxed($groupName, 30, $currentYCoord, $groupNameWidth, $lineHeight + 10, 'left', '');
        //		$currentYCoord = newLine($currentYCoord);
    }

    foreach ($groupArray as $expense) {

        if ($currentYCoord <= 30) {
            $pdf->setlinewidth(0.5);
            // bottom line
            $pdf->moveto(25, 40 + $currentYCoord - 35);
            $pdf->lineto(570, 40 + $currentYCoord - 35);
            $pdf->stroke();
            // left side bar line
            $pdf->moveto(25, 40 + $currentYCoord - 35);
            $pdf->lineto(25, 675);
            $pdf->stroke();

            $pdf->moveto(240, 40 + $currentYCoord - 35);
            $pdf->lineto(240, 675);
            $pdf->stroke();

            $pdf->moveto(300, 40 + $currentYCoord - 35);
            $pdf->lineto(300, 675);
            $pdf->stroke();

            // net left line
            $pdf->moveto(360, 40 + $currentYCoord - 35);
            $pdf->lineto(360, 675);
            $pdf->stroke();
            // net right line
            $pdf->moveto(430, 40 + $currentYCoord - 35);
            $pdf->lineto(430, 675);
            $pdf->stroke();

            // gst right line
            $pdf->moveto(500, 40 + $currentYCoord - 35);
            $pdf->lineto(500, 675);
            $pdf->stroke();

            // right side bar line
            $pdf->moveto(570, 40 + $currentYCoord - 35);
            $pdf->lineto(570, 675);
            $pdf->stroke();

            newPageDE(false, $propertyID);
            $pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
            $pdf->showBoxed($groupName, 30, $currentYCoord, $groupNameWidth, $lineHeight + 10, 'left', '');
            $currentYCoord = newLine($currentYCoord);
        }


        $group_net_sum += ($expense['net_amount'] * 1);
        $group_tax_sum += ($expense['tax_amount'] * 1);
        $group_gross_sum += ($expense['gross_amount'] * 1);

        $supplierName = limit_name($expense['supplier_name'], 28);
        $description = limit_name($expense['description'], 48);
        $net = formatting($expense['net_amount']);
        $tax = formatting($expense['tax_amount']);
        $gross = formatting($expense['gross_amount']);


        $pdf->setFontExt($_fonts['Helvetica'], 8);
        $pdf->showBoxed($supplierName, 30, $currentYCoord, $supplierWidth, $lineHeight, 'left', '');
        $pdf->showBoxed($description, 162, $currentYCoord, $descriptionWidth, $lineHeight, 'left', '');

        $pdf->setFontExt($_fonts['Helvetica'], 8);
        $pdf->showBoxed($expense['from_date'], 240, $currentYCoord, 60, $lineHeight, 'center', '');
        $pdf->showBoxed($expense['to_date'], 300, $currentYCoord, 60, $lineHeight, 'center', '');
        $pdf->showBoxed($net, 363, $currentYCoord, 60, $lineHeight, 'right', '');
        $pdf->showBoxed($tax, 433, $currentYCoord, 60, $lineHeight, 'right', '');
        $pdf->showBoxed($gross, 503, $currentYCoord, 60, $lineHeight, 'right', '');



        $rowCount++;


        if ($rowCount == $numRows) {

            $currentYCoord = newLine($currentYCoord, 15);

            $pdf->setColorExt('fill', 'rgb', 0.88, 0.88, 0.88, 0);
            $pdf->rect(240, $currentYCoord, 330, 16);
            $pdf->fill();
            $pdf->setColorExt('fill', 'rgb', 0, 0, 0, 0);

            $pdf->setlinewidth(0.5);
            $pdf->moveto(240, $currentYCoord + $lineHeight + 5);
            $pdf->lineto(570, $currentYCoord + $lineHeight + 5);
            $pdf->stroke();


            $pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
            $pdf->showBoxed("Total {$groupName}", 30, $currentYCoord, $groupNameWidth, $lineHeight + 4, 'left', '');
            $pdf->showBoxed(formatting($group_net_sum), 363, $currentYCoord, 60, $lineHeight + 4, 'right', '');
            $pdf->showBoxed(formatting($group_tax_sum), 433, $currentYCoord, 60, $lineHeight + 4, 'right', '');
            $pdf->showBoxed(formatting($group_gross_sum), 503, $currentYCoord, 60, $lineHeight + 4, 'right', '');


            $pdf->moveto(240, $currentYCoord);
            $pdf->lineto(570, $currentYCoord);
            $pdf->stroke();



            $currentYCoord = newLine($currentYCoord, 15);


        }

        $currentYCoord = newLine($currentYCoord);


    }

    $grandTotalNet += $group_net_sum;
    $grandTotalTax += $group_tax_sum;
    $grandTotalGross += $group_gross_sum;

    /*if($printGrandTotal)
    {
        printGrandTotal();
    }*/



}

function printGrandTotalDE()
{
    global $grandTotalNet;
    global $grandTotalTax;
    global $grandTotalGross;

    global $pdf;
    global $currentYCoord;
    global $currentXcoord;
    global $lineHeight;
    global $page;

    global $amtWidth;
    global $groupNameWidth;

    global $grandTotalNet;
    global $grandTotalTax;
    global $grandTotalGross;

    global $_fonts;

    $currentYCoord = newLine($currentYCoord, 15);

    $pdf->setlinewidth(0.5);
    // top total
    $pdf->moveto(240, $currentYCoord + $lineHeight + 5);
    $pdf->lineto(570, $currentYCoord + $lineHeight + 5);
    $pdf->stroke();


    $groupNameWidth = 275;
    $amtWidth = 275;

    $pdf->setColorExt('fill', 'rgb', 0.88, 0.88, 0.88, 0);
    $pdf->rect(240, $currentYCoord, 330, 16);
    $pdf->fill();
    $pdf->setColorExt('fill', 'rgb', 0, 0, 0, 0);

    $pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
    $pdf->showBoxed('Total Cash Payments', 30, $currentYCoord, $groupNameWidth, $lineHeight + 4, 'left', '');
    $pdf->showBoxed(formatting($grandTotalNet), 363, $currentYCoord, 60, $lineHeight + 4, 'right', '');
    $pdf->showBoxed(formatting($grandTotalTax), 433, $currentYCoord, 60, $lineHeight + 4, 'right', '');
    $pdf->showBoxed(formatting($grandTotalGross), 503, $currentYCoord, 60, $lineHeight + 4, 'right', '');



    $pdf->setlinewidth(0.5);
    // bottom line
    $pdf->moveto(25, 40 + $currentYCoord - 40);
    $pdf->lineto(570, 40 + $currentYCoord - 40);
    $pdf->stroke();
    // left side bar line
    $pdf->moveto(25, 40 + $currentYCoord - 40);
    $pdf->lineto(25, 675);
    $pdf->stroke();

    $pdf->moveto(240, 40 + $currentYCoord - 40);
    $pdf->lineto(240, 675);
    $pdf->stroke();

    $pdf->moveto(300, 40 + $currentYCoord - 40);
    $pdf->lineto(300, 675);
    $pdf->stroke();

    // net left line
    $pdf->moveto(360, 40 + $currentYCoord - 40);
    $pdf->lineto(360, 675);
    $pdf->stroke();
    // net right line
    $pdf->moveto(430, 40 + $currentYCoord - 40);
    $pdf->lineto(430, 675);
    $pdf->stroke();

    // gst right line
    $pdf->moveto(500, 40 + $currentYCoord - 40);
    $pdf->lineto(500, 675);
    $pdf->stroke();

    // right side bar line
    $pdf->moveto(570, 40 + $currentYCoord - 40);
    $pdf->lineto(570, 675);
    $pdf->stroke();

    $currentYCoord = newLine($currentYCoord, 15);
    // $page++;

    $pdf->end_page_ext('');
}

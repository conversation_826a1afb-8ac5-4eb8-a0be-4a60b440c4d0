<?php

function get_prepayments($propertyID, $leaseID, $dueDate)
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);
    $prepaidSQL = "SELECT CONVERT(char(10), pmxd_alloc_dt, 103) AS pmxd_alloc_dt,*
					FROM[PMXD_AR_ALLOC] E
					WHERE PMXD_ALLOC_DT <= CONVERT(datetime, '$dueDate', 103)
						AND PMXD_PROP = '$propertyID'
						AND PMXD_LEASE = '$leaseID'
						AND NOT EXISTS
								(SELECT *
									FROM [AR_TRANSACTION] R
									WHERE((BATCH_NR = E.[PMXD_T_BATCH] AND BATCH_LINE_NR = E.[PMXD_T_LINE]))
									AND TRANS_DATE <= CONVERT(datetime, '$dueDate', 103))";

    return $dbh->executeSet($prepaidSQL);
}

function fetchOutstandingAmounts($debtorID, $propertyID, $leaseID, $dueDate, $showZero = 'No', $receiptsToDate = '')
{
    global $clientDirectory;
    $records = [];

    $outstanding = dbGetInvoiceChargesUnpaidDebtors($propertyID, $leaseID, $debtorID, $dueDate, $receiptsToDate);

    if ($outstanding) {
        foreach ($outstanding as $row) {
            // -- offset is all transactions allocated against the reference transaction minus the effect of any adjustments [INV<->CRE / CRE<->INV] (to prevent a double count)
            $offset = bcadd($row['totalAllocated'], $row['totalReallocated'], 2);
            $row['unallocated'] = round($row['unallocated'] * 1, 2);
            $row['amount'] = round($row['amount'], 2);
            $unpaidAmount = bcsub($row['amount'], $offset, 2);
            $unpaidTaxAmount = round($row['taxAmount'] - $row['totalAllocatedTax'], 2);
            $unpaidNetAmount = round($row['netAmount'] - $row['totalAllocatedNet'], 2);

            if ((($showZero == 'No' && $unpaidAmount != 0 and ! (($unpaidTaxAmount == 0 and $unpaidNetAmount == 0) || ($unpaidTaxAmount == -0.01 and $unpaidNetAmount == 0.01))) || $showZero == 'Yes') && $row['transactionType'] != TYPE_CASH) {
                $row['unpaidAmount'] = bcsub($row['amount'], $offset, 2);
                $row['unpaidTaxAmount'] = $unpaidTaxAmount;
                $row['unpaidNetAmount'] = $unpaidNetAmount;

                if ($row['transactionType'] == 'INV' or $row['transactionType'] == 'CRE') {
                    [$day_, $month_, $year_] = explode('/', $row['dueDate']);
                    // $filename = 'tax_invoice_'. '20140701' . '_'. '9646'. '_'. 'ALBANY'. '_'. 'MAST';
                    $filename = 'tax_invoice_' . $year_ . $month_ . $day_ . '_' . $row['invoiceNumber'] . '_' . $propertyID . '_' . $leaseID;
                    $file = "../reports/{$clientDirectory}/pdf/TaxInvoice/$filename.pdf";
                    // echo $file .'<br>';
                    if (file_exists($file)) {
                        $row['filePath'] = $file;
                    } else {
                        [$day_, $month_, $year_] = explode('/', $row['transactionDate']);
                        // $filename = 'tax_invoice_'. '20140701' . '_'. '9646'. '_'. 'ALBANY'. '_'. 'MAST';
                        $filename = 'tax_invoice_' . $year_ . $month_ . $day_ . '_' . $row['invoiceNumber'] . '_' . $propertyID . '_' . $leaseID;
                        $file = "../reports/{$clientDirectory}/pdf/TaxInvoice/$filename.pdf";
                        // echo $file .'<br>';
                        if (file_exists($file)) {
                            $row['filePath'] = $file;
                        } else {
                            [$day_, $month_, $year_] = explode('/', $row['toDate']);
                            $the_date = date('d/m/Y', strtotime("$month_/$day_/$year_"));
                            [$day_, $month_, $year_] = explode('/', $the_date);
                            // $filename = 'tax_invoice_'. '20140701' . '_'. '9646'. '_'. 'ALBANY'. '_'. 'MAST';
                            $filename = 'tax_invoice_' . $year_ . $month_ . $day_ . '_' . $row['invoiceNumber'] . '_' . $propertyID . '_' . $leaseID;
                            $file = "../reports/{$clientDirectory}/pdf/TaxInvoice/$filename.pdf";
                            // echo $file .'<br>';
                            if (file_exists($file)) {
                                $row['filePath'] = $file;
                            }
                        }
                    }
                }

                $records[] = $row;
            } else {
                if (($row['transactionType'] == TYPE_CASH) && ($row['unallocated'] != 0)) {
                    $unallocated = dbGetUnallocated(
                        $propertyID,
                        $leaseID,
                        $debtorID,
                        $row['batchNumber'],
                        $row['batchLineNumber'],
                        $dueDate
                    );
                    if ($unallocated) {
                        foreach ($unallocated as $unallocatedItem) {
                            if (($showZero == 'No' && $unallocatedItem['amount'] != 0 and ! (($unpaidTaxAmount == 0 and $unpaidNetAmount == 0) || ($unpaidTaxAmount == -0.01 and $unpaidNetAmount == 0.01))) || $showZero == 'Yes') {
                                $unallocatedItem['debtorID'] = $row['debtorID'];
                                $unallocatedItem['debtorName'] = $row['debtorName'];
                                $unallocatedItem['transactionAmount'] = $unallocatedItem['amount'];
                                $unallocatedItem['batchNumber'] = $row['batchNumber'];
                                $unallocatedItem['batchLineNumber'] = $row['batchLineNumber'];
                                $unallocatedItem['transactionDate'] = $row['transactionDate'];
                                $unallocatedItem['dueDate'] = $row['dueDate'];
                                $unallocatedItem['invoiceNumber'] = $row['invoiceNumber'];
                                $unallocatedItem['transactionType'] = TYPE_CASH;
                                $unallocatedItem['unallocated'] = $row['unallocated'];

                                $unallocatedItem['unpaidAmount'] = $unallocatedItem['amount'];

                                $unpaidTaxAmount = round($unallocatedItem['taxAmount'] - $row['totalAllocatedTax'], 2);
                                $unpaidNetAmount = round($unallocatedItem['netAmount'] - $row['totalAllocatedNet'], 2);

                                $unallocatedItem['unpaidTaxAmount'] = $unpaidTaxAmount;
                                $unallocatedItem['unpaidNetAmount'] = $unpaidNetAmount;

                                $unallocatedItem['unitID'] = $row['unitID'];
                                $unallocatedItem['unitDescription'] = $row['unitDescription'];

                                $records[] = $unallocatedItem;
                            }
                        }
                    }
                }
            }
        }
    }

    // get prepayments

    $unpaid_total_net = 0;
    $unpaid_total_gst = 0;
    $unpaid_total_gross = 0;
    $prepaid_result = get_prepayments($propertyID, $leaseID, $dueDate);

    $prepayments_ = [];
    foreach ($prepaid_result as $row) {
        $a = [];
        $unpaid_debtors_code = $row['pmxd_s_debtor'];
        $transaction_date = $row['pmxd_alloc_dt'];
        $unpaid_account_code = $row['pmxd_acc'];
        $unpaid_amount = $row['pmxd_alloc_amt'];
        $unpaid_tax_amt = $row['pmxd_tax_amt'];
        $unpaid_description = 'Prepayment';
        $unpaid_date_from = '';
        $unpaid_date_to = '';


        $unpaid_net_amount = bcsub($unpaid_amount, $unpaid_tax_amt, 2);

        if ($unpaid_amount != 0) {
            $unpaid_total_net = bcadd($unpaid_total_net, $unpaid_net_amount, 2);
            $unpaid_total_gst = bcadd($unpaid_total_gst, $unpaid_tax_amt, 2);
            $unpaid_total_gross = bcadd($unpaid_total_gross, $unpaid_amount, 2);
        }

        // if ($unpaid_amount == '0')// && $unpaid_tax_amt_display == "0.00" && $unpaid_net_amount_display == "0.00" )
        if ($unpaid_amount == 0 and (($unpaid_tax_amt == 0 and $unpaid_net_amount == 0) || ($unpaid_tax_amt == -0.01 and $unpaid_net_amount == 0.01) || ($unpaid_tax_amt == 0.01 and $unpaid_net_amount == -0.01))) {
        } else {
            // $html .=  '<tr class="' . alternateNextRow() . '">' .
            // "<td>$unpaid_account_code</td>
            // <td>$unpaid_debtors_name</td>
            // <td>$unpaid_description</td>
            // <td>$transaction_date</td>
            // <td>$unpaid_date_from</td>
            // <td align=right>$unpaid_date_to</td>
            // <td align=right>$unpaid_net_amount_display</td>
            // <td align=right>$unpaid_tax_amt_display</td>
            // <td align=right>$unpaid_gross_amount_display</td>" .
            // "</tr>";
            $a['transactionDate'] = $transaction_date;
            // $a['dueDate'] = $transaction_date;
            $a['invoiceDate'] = $transaction_date;
            $a['accountID'] = $unpaid_account_code;
            $a['description'] = $unpaid_description;
            $a['transactionType'] = '';
            $a['invoiceNumber'] = '';
            $a['fromDate'] = $unpaid_date_from;
            $a['toDate'] = $unpaid_date_to;
            $a['dueDate'] = '';
            $a['amount'] = $unpaid_amount;
            $a['unpaidNetAmount'] = $unpaid_net_amount;
            $a['unpaidTaxAmount'] = $unpaid_tax_amt;
            $a['unpaidAmount'] = $unpaid_amount;

            $prepayments_[] = $a;
        } // end of else - after - if ($unpaid_gross_amount_display == '0.00')

        // $i++;
        // endwhile; // end of while ($i < $prepaid_number) :
        // endif;
    }// end of - foreach ($prepaid_result as $row)

    if ($unpaid_total_gross != 0) {
        $records = array_merge($records, $prepayments_);
    }

    return $records;
}

function getPropGroupData($propGroupIDs, $reportFromDate, $reportToDate, $number_format)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $propGroupID = "('" . implode("','", array_values($propGroupIDs)) . "')";
    $account_array = [
        'tf.toOtherPortfolio' => [10030, 10034, 10039, 10060, 10061, 10062, 10063, 10064, 10065, 10066, 10067, 10068, 10069, 10070],
        'inc.income' => [
            1003,
            1001,
            1034,
            1403,
            3501,
            3503,
            3511,
            3562,
            3603,
            3551,
            3625,
            1401,
            1530,
            3502,
            3622,
        ],
        'expense.compensationRentFee' => [1034],
        'expense.expenseGMFee' => [
            6595,
            6603,
            6614,
            6617,
            6622,
            6627,
            6643,
            6644,
            6636,
            6653,
            5595,
            5603,
            5614,
            5617,
            5643,
            5644,
            5636,
            5653,
            4595,
            4603,
            4614,
            4617,
            4622,
            4627,
            4628,
            4643,
            4644,
            4636,
            4653,
        ],
        'expense.expenseEFee' => [6615, 6625, 5615, 5625, 4615, 4625],
        'expense.expensePFee' => [6629, 6635, 6531, 6604, 5629, 5635, 5531, 5604, 4629, 4635, 4531, 4604],
        'expense.expenseGFee' => [
            6581,
            6551,
            6552,
            6553,
            6554,
            6555,
            6556,
            6557,
            5581,
            5551,
            5552,
            5553,
            5554,
            5555,
            5556,
            5557,
            4581,
            4582,
            4551,
            4552,
            4553,
            4554,
            4555,
            4556,
            4557,
        ],
        'expense.expenseSFee' => [5651, 6651, 4651, 4684],
        'expense.expenseCRFee' => [6501, 5501, 4501],
        'expense.expenseSROFee' => [6502, 5502, 4502],
        'expense.expenseWRFee' => [6503, 6511, 6512, 5503, 5511, 5512, 4503, 4511, 4512],
        'expense.expenseStrataFee' => [
            6572,
            6661,
            6662,
            6201,
            6202,
            6204,
            6562,
            6561,
            6571,
            6705,
            5572,
            5661,
            5662,
            5201,
            5202,
            5204,
            5561,
            5591,
            5571,
            4572,
            4661,
            4662,
            4201,
            4202,
            4204,
            4560,
            4561,
            4591,
            4571,
            4300,
        ],
        'expense.expenseIFee' => [4682],
        'expense.expenseMFFee' => [6671, 6672, 6695, 5671, 5672, 5695, 4671, 4672, 4695],
        'expense.expenseAgencyFee' => [6708, 5708, 4708],
        'expense.expenseOthersMCFee' => [
            6513,
            6521,
            6522,
            6523,
            6524,
            6563,
            6564,
            6101,
            6000,
            6681,
            6683,
            6687,
            6688,
            6689,
            6692,
            6693,
            6694,
            6701,
            6702,
            6703,
            6704,
            6707,
            6726,
            6732,
            5513,
            5521,
            5522,
            5523,
            5524,
            5562,
            5563,
            5304,
            5673,
            5681,
            5687,
            5688,
            5689,
            5692,
            5693,
            5694,
            5701,
            5702,
            5703,
            5704,
            5707,
            5726,
            5732,
            4513,
            4521,
            4522,
            4523,
            4524,
            4562,
            4563,
            4101,
            4305,
            4670,
            4673,
            4680,
            4681,
            4683,
            4687,
            4688,
            4689,
            4692,
            4693,
            4694,
            4700,
            4701,
            4702,
            4703,
            4704,
            4705,
            4707,
            4726,
            4732,
            5622,
            5627,
        ],
        'expense.expenseMCForProperty' => [5622, 5627],
    ];

    $transferFund = '(' . implode(',', array_values($account_array['tf.toOtherPortfolio'])) . ')';
    $gstInc = '(' . implode(',', array_values($account_array['inc.income'])) . ')';
    $accountsCompensationRentFee = '(' . implode(
        ',',
        array_values($account_array['expense.compensationRentFee'])
    ) . ')';
    $accountsExpenseGMFee = '(' . implode(',', array_values($account_array['expense.expenseGMFee'])) . ')';
    $accountsExpenseEFee = '(' . implode(',', array_values($account_array['expense.expenseEFee'])) . ')';
    $accountsExpensePFee = '(' . implode(',', array_values($account_array['expense.expensePFee'])) . ')';
    $accountsExpenseGFee = '(' . implode(',', array_values($account_array['expense.expenseGFee'])) . ')';
    $accountsExpenseSFee = '(' . implode(',', array_values($account_array['expense.expenseSFee'])) . ')';
    $accountsExpenseCRFee = '(' . implode(',', array_values($account_array['expense.expenseCRFee'])) . ')';
    $accountsExpenseSROFee = '(' . implode(',', array_values($account_array['expense.expenseSROFee'])) . ')';
    $accountsExpenseWRFee = '(' . implode(',', array_values($account_array['expense.expenseWRFee'])) . ')';
    $accountExpenseStrataFee = '(' . implode(',', array_values($account_array['expense.expenseStrataFee'])) . ')';
    $accountExpenseIFee = '(' . implode(',', array_values($account_array['expense.expenseIFee'])) . ')';
    $accountExpenseMFFee = '(' . implode(',', array_values($account_array['expense.expenseMFFee'])) . ')';
    $accountExpenseAgencyFee = '(' . implode(',', array_values($account_array['expense.expenseAgencyFee'])) . ')';
    $accountExpenseOthersMCFee = '(' . implode(',', array_values($account_array['expense.expenseOthersMCFee'])) . ')';
    $expenseMCForProperty = '(' . implode(',', array_values($account_array['expense.expenseMCForProperty'])) . ')';
    $expenseAcc = [];
    foreach ($account_array as $key => $values) {
        if ($key != 'inc.income') {
            array_push($expenseAcc, implode(',', array_values($values)));
        }
    }

    $gstExp = '(' . implode(',', array_values($expenseAcc)) . ')';
    $fromPeriod = $dbh->executeSingle(
        "select pmcm_period from pmcm_mast_cal where pmcm_code = 'Financial' AND CONVERT(datetime, '{$reportFromDate}', 103) between pmcm_start_dt and pmcm_end_dt"
    )['pmcm_period'];
    $toPeriod = $dbh->executeSingle(
        "select pmcm_period from pmcm_mast_cal where pmcm_code = 'Financial' AND CONVERT(datetime, '{$reportToDate}', 103) between pmcm_start_dt and pmcm_end_dt"
    )['pmcm_period'];
    $year = $dbh->executeSingle(
        "select  DISTINCT pmcm_year from  pmcm_mast_cal where pmcm_code = 'Financial'  AND pmcm_start_dt >= CONVERT(datetime, '{$reportFromDate}', 103) AND pmcm_end_dt <= CONVERT(datetime, '{$reportToDate}', 103)"
    )['pmcm_year'];

    $params = [];
    $paramsFinance = [];

    // SQL Proces Finance Report Tab
    $sqlFinance = "
        select
        finance.orderby,
		finance.description as accountDescription,
        finance.type as accountType,
        pp.pmpr_prop_group as propertyGroup,
        gc.group_name propertyGroupName,
        COALESCE(SUM(finance.jul), 0) as Jul,
        COALESCE(SUM(finance.aug), 0) as Aug,
        COALESCE(SUM(finance.sep), 0) as Sep,
        COALESCE(SUM(finance.oct), 0) as Oct,
        COALESCE(SUM(finance.nov), 0) as Nov,
        COALESCE(SUM(finance.dec), 0) as Dec,
        COALESCE(SUM(finance.jan), 0) as Jan,
        COALESCE(SUM(finance.feb), 0) as Feb,
        COALESCE(SUM(finance.mar), 0) as Mar,
        COALESCE(SUM(finance.apr), 0) as Apr,
        COALESCE(SUM(finance.may), 0) as May,
        COALESCE(SUM(finance.jun), 0) as Jun
        from
        (
        SELECT
        'income' as type,
        'Revenue Account Adjustment' as description,
		 1 as orderby,
        inc.pmxd_prop as property,
        SUM(CASE WHEN pmxd_period = 1 THEN pmxd_alloc_amt-pmxd_tax_amt ELSE 0 END)*-1  as jul
        ,SUM(CASE WHEN pmxd_period = 2 THEN pmxd_alloc_amt-pmxd_tax_amt ELSE 0 END)*-1  as aug
        ,SUM(CASE WHEN pmxd_period = 3 THEN pmxd_alloc_amt-pmxd_tax_amt ELSE 0 END)*-1  as sep
        ,SUM(CASE WHEN pmxd_period = 4 THEN pmxd_alloc_amt-pmxd_tax_amt ELSE 0 END)*-1  as oct
        ,SUM(CASE WHEN pmxd_period = 5 THEN pmxd_alloc_amt-pmxd_tax_amt ELSE 0 END)*-1  as nov
        ,SUM(CASE WHEN pmxd_period = 6 THEN pmxd_alloc_amt-pmxd_tax_amt ELSE 0 END)*-1  as dec
        ,SUM(CASE WHEN pmxd_period = 7 THEN pmxd_alloc_amt-pmxd_tax_amt ELSE 0 END)*-1  as jan
        ,SUM(CASE WHEN pmxd_period = 8 THEN pmxd_alloc_amt-pmxd_tax_amt ELSE 0 END)*-1  as feb
        ,SUM(CASE WHEN pmxd_period = 9 THEN pmxd_alloc_amt-pmxd_tax_amt ELSE 0 END)*-1  as mar
        ,SUM(CASE WHEN pmxd_period = 10 THEN pmxd_alloc_amt-pmxd_tax_amt ELSE 0 END)*-1  as apr
        ,SUM(CASE WHEN pmxd_period = 11 THEN pmxd_alloc_amt-pmxd_tax_amt ELSE 0 END)*-1  as may
        ,SUM(CASE WHEN pmxd_period = 12 THEN pmxd_alloc_amt-pmxd_tax_amt ELSE 0 END)*-1  as jun
        FROM pmxd_ar_alloc inc
        WHERE
        inc.pmxd_f_type = 'CSH' AND
        inc.pmxd_acc IN {$gstInc} AND
        inc.pmxd_year = " . addSQLParam($paramsFinance, $year) . "
        GROUP BY inc.pmxd_prop
        UNION ALL
        SELECT
        'expenseWRFee' as type,
        'To Recognise Property Water Rates for properties' as description,
		 2 as orderby,
        expense.pmxc_prop as property,
        SUM(CASE WHEN pmxc_period = 1 THEN pmxc_alloc_amt+pmxc_tax_amt ELSE 0 END)*-1  as jul
        ,SUM(CASE WHEN pmxc_period = 2 THEN pmxc_alloc_amt+pmxc_tax_amt ELSE 0 END)*-1  as aug
        ,SUM(CASE WHEN pmxc_period = 3 THEN pmxc_alloc_amt+pmxc_tax_amt ELSE 0 END)*-1  as sep
        ,SUM(CASE WHEN pmxc_period = 4 THEN pmxc_alloc_amt+pmxc_tax_amt ELSE 0 END)*-1  as oct
        ,SUM(CASE WHEN pmxc_period = 5 THEN pmxc_alloc_amt+pmxc_tax_amt ELSE 0 END)*-1  as nov
        ,SUM(CASE WHEN pmxc_period = 6 THEN pmxc_alloc_amt+pmxc_tax_amt ELSE 0 END)*-1  as dec
        ,SUM(CASE WHEN pmxc_period = 7 THEN pmxc_alloc_amt+pmxc_tax_amt ELSE 0 END)*-1  as jan
        ,SUM(CASE WHEN pmxc_period = 8 THEN pmxc_alloc_amt+pmxc_tax_amt ELSE 0 END)*-1  as feb
        ,SUM(CASE WHEN pmxc_period = 9 THEN pmxc_alloc_amt+pmxc_tax_amt ELSE 0 END)*-1  as mar
        ,SUM(CASE WHEN pmxc_period = 10 THEN pmxc_alloc_amt+pmxc_tax_amt ELSE 0 END)*-1  as apr
        ,SUM(CASE WHEN pmxc_period = 11 THEN pmxc_alloc_amt+pmxc_tax_amt ELSE 0 END)*-1  as may
        ,SUM(CASE WHEN pmxc_period = 12 THEN pmxc_alloc_amt+pmxc_tax_amt ELSE 0 END)*-1  as jun
        FROM pmxc_ap_alloc expense
        WHERE
        pmxc_acc IN {$accountsExpenseWRFee}
        AND expense.pmxc_year =  " . addSQLParam($paramsFinance, $year) . "
        AND expense.pmxc_f_type = 'PAY'
        GROUP BY expense.pmxc_prop
        UNION ALL
        SELECT
        'expenseCRFee' as type,
        'To Recognise Property Council Rates for properties' as description,
		 3 as orderby,
        expense.pmxc_prop as property,
        SUM(CASE WHEN pmxc_period = 1 THEN pmxc_alloc_amt+pmxc_tax_amt ELSE 0 END)*-1  as jul
        ,SUM(CASE WHEN pmxc_period = 2 THEN pmxc_alloc_amt+pmxc_tax_amt ELSE 0 END)*-1  as aug
        ,SUM(CASE WHEN pmxc_period = 3 THEN pmxc_alloc_amt+pmxc_tax_amt ELSE 0 END)*-1  as sep
        ,SUM(CASE WHEN pmxc_period = 4 THEN pmxc_alloc_amt+pmxc_tax_amt ELSE 0 END)*-1  as oct
        ,SUM(CASE WHEN pmxc_period = 5 THEN pmxc_alloc_amt+pmxc_tax_amt ELSE 0 END)*-1  as nov
        ,SUM(CASE WHEN pmxc_period = 6 THEN pmxc_alloc_amt+pmxc_tax_amt ELSE 0 END)*-1  as dec
        ,SUM(CASE WHEN pmxc_period = 7 THEN pmxc_alloc_amt+pmxc_tax_amt ELSE 0 END)*-1  as jan
        ,SUM(CASE WHEN pmxc_period = 8 THEN pmxc_alloc_amt+pmxc_tax_amt ELSE 0 END)*-1  as feb
        ,SUM(CASE WHEN pmxc_period = 9 THEN pmxc_alloc_amt+pmxc_tax_amt ELSE 0 END)*-1  as mar
        ,SUM(CASE WHEN pmxc_period = 10 THEN pmxc_alloc_amt+pmxc_tax_amt ELSE 0 END)*-1  as apr
        ,SUM(CASE WHEN pmxc_period = 11 THEN pmxc_alloc_amt+pmxc_tax_amt ELSE 0 END)*-1  as may
        ,SUM(CASE WHEN pmxc_period = 12 THEN pmxc_alloc_amt+pmxc_tax_amt ELSE 0 END)*-1  as jun
        FROM pmxc_ap_alloc expense
        WHERE
        pmxc_acc IN {$accountsExpenseCRFee}
        AND expense.pmxc_year =  " . addSQLParam($paramsFinance, $year) . "
        AND expense.pmxc_f_type = 'PAY'
        GROUP BY expense.pmxc_prop
        UNION ALL
        SELECT
        'maintenanceFee' as type,
        'To Recognise Property Maintenance Costs for properties' as description,
		 4 as orderby,
        expense.pmxc_prop as property,
        SUM(CASE WHEN pmxc_period = 1 THEN pmxc_alloc_amt+pmxc_tax_amt ELSE 0 END)*-1  as jul
        ,SUM(CASE WHEN pmxc_period = 2 THEN pmxc_alloc_amt+pmxc_tax_amt ELSE 0 END)*-1  as aug
        ,SUM(CASE WHEN pmxc_period = 3 THEN pmxc_alloc_amt+pmxc_tax_amt ELSE 0 END)*-1  as sep
        ,SUM(CASE WHEN pmxc_period = 4 THEN pmxc_alloc_amt+pmxc_tax_amt ELSE 0 END)*-1  as oct
        ,SUM(CASE WHEN pmxc_period = 5 THEN pmxc_alloc_amt+pmxc_tax_amt ELSE 0 END)*-1  as nov
        ,SUM(CASE WHEN pmxc_period = 6 THEN pmxc_alloc_amt+pmxc_tax_amt ELSE 0 END)*-1  as dec
        ,SUM(CASE WHEN pmxc_period = 7 THEN pmxc_alloc_amt+pmxc_tax_amt ELSE 0 END)*-1  as jan
        ,SUM(CASE WHEN pmxc_period = 8 THEN pmxc_alloc_amt+pmxc_tax_amt ELSE 0 END)*-1  as feb
        ,SUM(CASE WHEN pmxc_period = 9 THEN pmxc_alloc_amt+pmxc_tax_amt ELSE 0 END)*-1  as mar
        ,SUM(CASE WHEN pmxc_period = 10 THEN pmxc_alloc_amt+pmxc_tax_amt ELSE 0 END)*-1  as apr
        ,SUM(CASE WHEN pmxc_period = 11 THEN pmxc_alloc_amt+pmxc_tax_amt ELSE 0 END)*-1  as may
        ,SUM(CASE WHEN pmxc_period = 12 THEN pmxc_alloc_amt+pmxc_tax_amt ELSE 0 END)*-1  as jun
        FROM pmxc_ap_alloc expense
        WHERE
        pmxc_acc NOT IN (" .
        str_replace(')', ',', str_replace('(', '', $accountsExpenseCRFee)) .
        str_replace(')', '', str_replace('(', '', $accountsExpenseWRFee)) .
        ')
        AND expense.pmxc_year =  ' . addSQLParam($paramsFinance, $year) . "
        AND expense.pmxc_f_type = 'PAY'
        GROUP BY expense.pmxc_prop
        UNION ALL
        SELECT
        'expenseTaxAmount' as type,
        'GST Payables' as description,
		 5 as orderby,
        expense.pmxc_prop as property,
        SUM(CASE WHEN pmxc_period = 1 THEN pmxc_tax_amt ELSE 0 END)  as jul
        ,SUM(CASE WHEN pmxc_period = 2 THEN pmxc_tax_amt ELSE 0 END)  as aug
        ,SUM(CASE WHEN pmxc_period = 3 THEN pmxc_tax_amt ELSE 0 END)  as sep
        ,SUM(CASE WHEN pmxc_period = 4 THEN pmxc_tax_amt ELSE 0 END)  as oct
        ,SUM(CASE WHEN pmxc_period = 5 THEN pmxc_tax_amt ELSE 0 END)  as nov
        ,SUM(CASE WHEN pmxc_period = 6 THEN pmxc_tax_amt ELSE 0 END)  as dec
        ,SUM(CASE WHEN pmxc_period = 7 THEN pmxc_tax_amt ELSE 0 END)  as jan
        ,SUM(CASE WHEN pmxc_period = 8 THEN pmxc_tax_amt ELSE 0 END)  as feb
        ,SUM(CASE WHEN pmxc_period = 9 THEN pmxc_tax_amt ELSE 0 END)  as mar
        ,SUM(CASE WHEN pmxc_period = 10 THEN pmxc_tax_amt ELSE 0 END)  as apr
        ,SUM(CASE WHEN pmxc_period = 11 THEN pmxc_tax_amt ELSE 0 END)  as may
        ,SUM(CASE WHEN pmxc_period = 12 THEN pmxc_tax_amt ELSE 0 END)  as jun
        FROM pmxc_ap_alloc expense
        WHERE
        expense.pmxc_acc
        NOT IN (" .
        str_replace(')', ',', str_replace('(', '', $accountsExpenseCRFee)) .
        str_replace(')', '', str_replace('(', '', $accountsExpenseWRFee)) .
        ')
        AND expense.pmxc_year =  ' . addSQLParam($paramsFinance, $year) . "
        GROUP BY expense.pmxc_prop
        UNION ALL
        SELECT
        'incomeTaxAmount' as type,
        'GST Recoveries' as description,
		 6 as orderby,
        inc.pmxd_prop as property,
        SUM(CASE WHEN pmxd_period = 1 THEN pmxd_tax_amt ELSE 0 END)*-1  as jul
        ,SUM(CASE WHEN pmxd_period = 2 THEN pmxd_tax_amt ELSE 0 END)*-1  as aug
        ,SUM(CASE WHEN pmxd_period = 3 THEN pmxd_tax_amt ELSE 0 END)*-1  as sep
        ,SUM(CASE WHEN pmxd_period = 4 THEN pmxd_tax_amt ELSE 0 END)*-1  as oct
        ,SUM(CASE WHEN pmxd_period = 5 THEN pmxd_tax_amt ELSE 0 END)*-1  as nov
        ,SUM(CASE WHEN pmxd_period = 6 THEN pmxd_tax_amt ELSE 0 END)*-1  as dec
        ,SUM(CASE WHEN pmxd_period = 7 THEN pmxd_tax_amt ELSE 0 END)*-1  as jan
        ,SUM(CASE WHEN pmxd_period = 8 THEN pmxd_tax_amt ELSE 0 END)*-1  as feb
        ,SUM(CASE WHEN pmxd_period = 9 THEN pmxd_tax_amt ELSE 0 END)*-1  as mar
        ,SUM(CASE WHEN pmxd_period = 10 THEN pmxd_tax_amt ELSE 0 END)*-1  as apr
        ,SUM(CASE WHEN pmxd_period = 11 THEN pmxd_tax_amt ELSE 0 END)*-1  as may
        ,SUM(CASE WHEN pmxd_period = 12 THEN pmxd_tax_amt ELSE 0 END)*-1  as jun
        FROM pmxd_ar_alloc inc
        WHERE
        pmxd_f_type = 'CSH'
        AND inc.pmxd_year =  " . addSQLParam($paramsFinance, $year) . "
        GROUP BY inc.pmxd_prop
        UNION ALL
        SELECT
        'transferFund' as type,
        'Transfer of funds to other portfolio' as description,
		 7 as orderby,
        inc.pmxd_prop as property,
        SUM(CASE WHEN pmxd_period = 1 THEN pmxd_alloc_amt-pmxd_tax_amt ELSE 0 END)*-1  as jul
        ,SUM(CASE WHEN pmxd_period = 2 THEN pmxd_alloc_amt-pmxd_tax_amt ELSE 0 END)*-1  as aug
        ,SUM(CASE WHEN pmxd_period = 3 THEN pmxd_alloc_amt-pmxd_tax_amt ELSE 0 END)*-1  as sep
        ,SUM(CASE WHEN pmxd_period = 4 THEN pmxd_alloc_amt-pmxd_tax_amt ELSE 0 END)*-1  as oct
        ,SUM(CASE WHEN pmxd_period = 5 THEN pmxd_alloc_amt-pmxd_tax_amt ELSE 0 END)*-1  as nov
        ,SUM(CASE WHEN pmxd_period = 6 THEN pmxd_alloc_amt-pmxd_tax_amt ELSE 0 END)*-1  as dec
        ,SUM(CASE WHEN pmxd_period = 7 THEN pmxd_alloc_amt-pmxd_tax_amt ELSE 0 END)*-1  as jan
        ,SUM(CASE WHEN pmxd_period = 8 THEN pmxd_alloc_amt-pmxd_tax_amt ELSE 0 END)*-1  as feb
        ,SUM(CASE WHEN pmxd_period = 9 THEN pmxd_alloc_amt-pmxd_tax_amt ELSE 0 END)*-1  as mar
        ,SUM(CASE WHEN pmxd_period = 10 THEN pmxd_alloc_amt-pmxd_tax_amt ELSE 0 END)*-1  as apr
        ,SUM(CASE WHEN pmxd_period = 11 THEN pmxd_alloc_amt-pmxd_tax_amt ELSE 0 END)*-1  as may
        ,SUM(CASE WHEN pmxd_period = 12 THEN pmxd_alloc_amt-pmxd_tax_amt ELSE 0 END)*-1  as jun
        FROM pmxd_ar_alloc inc
        WHERE
        inc.pmxd_f_type = 'CSH' AND
        inc.pmxd_acc IN {$transferFund} AND
        inc.pmxd_year = " . addSQLParam($paramsFinance, $year) . "
        GROUP BY inc.pmxd_prop
        )
        as finance
        left join (
                select
                    pmpr_prop,
                    pmpr_prop_group,
                    pmpr_delete
                from
                    pmpr_property
                ) pp on finance.property = pp.pmpr_prop
        left join (
                select DISTINCT(GROUP_CODE) as group_code, pmzz_desc as group_name
                    from crp_property, pmzz_param
                    where pmzz_par_type = 'PROPGROUP'
                    and pmzz_code = group_code
                ) gc on gc.group_code = pp.pmpr_prop_group
        WHERE
        pp.pmpr_prop_group IN {$propGroupID} AND pp.pmpr_delete != 1
        GROUP BY  finance.type,
        pp.pmpr_prop_group,
        gc.group_name,
        finance.orderby,
		finance.description
        ORDER BY gc.group_name, finance.orderby
    ";

    $resultFinance = $dbh->executeSet($sqlFinance, false, true, $paramsFinance);

    $return = [];

    $sqlCalendar = "select CONVERT(varchar(10), pmcm_start_dt, 101) as monthYear from pmcm_mast_cal
    where pmcm_code = 'Financial' AND pmcm_year = (select DISTINCT pmcm_year from pmcm_mast_cal where pmcm_code = 'Financial' AND CONVERT(datetime, '{$reportToDate}', 103) BETWEEN pmcm_start_dt AND pmcm_end_dt)";
    $resultCalendar = $dbh->executeSet($sqlCalendar);

    $sqlGroup = "select DISTINCT(GROUP_CODE) as group_code, pmzz_desc as group_name from crp_property, pmzz_param where pmzz_par_type = 'PROPGROUP' and pmzz_code = group_code AND group_code IN {$propGroupID}";
    $resultGroup = $dbh->executeSet($sqlGroup);
    $financeColumn = [];
    foreach ($resultCalendar as $val) {
        $dateTimeObj = new DateTime($val['monthYear']);
        $fiscalDate = $dateTimeObj->format('M-y');
        $fiscalDateData = $dateTimeObj->format('M');
        $financeColumn[] = [$fiscalDateData, $fiscalDate, 'right', $number_format];
    }

    $totalIncome = [];
    $totalExpense = [];
    $totalRevenueGross = [];
    $totalFundTransfer = [];
    $financialData = [];
    // initial default value per account type
    $accountType = [
        ['accountType' => 'income', 'accountDescription' => 'Revenue Account Adjustment'],
        ['accountType' => 'expenseWRFee', 'accountDescription' => 'To Recognise Property Water Rates for properties'],
        ['accountType' => 'expenseCRFee', 'accountDescription' => 'To Recognise Property Council Rates for properties'],
        [
            'accountType' => 'maintenanceFee',
            'accountDescription' => 'To Recognise Property Maintenance Costs for properties',
        ],
        ['accountType' => 'expenseTaxAmount', 'accountDescription' => 'GST Payables'],
        ['accountType' => 'incomeTaxAmount', 'accountDescription' => 'GST Recoveries'],
        ['accountType' => 'transferFund', 'accountDescription' => 'Transfer of funds to other portfolio'],
    ];


    foreach ($resultGroup as $grp) {
        foreach ($accountType as $key => $type) {
            // Additional Columns for Finance report sheet
            $additionalColumns = propertyPIMSParams($grp['group_code'] . '-' . ($key + 1), $dbh);
            [
                $type['ent'],
                $type['account'],
                $type['cc'],
                $type['initiative'],
                $type['authority'],
                $type['property'],
                $type['output'],
                $type['identifier'],
                $type['relatedParty'],
                $type['currency']
            ] = explode(',', $additionalColumns['parameterDescription']);

            $type['orderby'] = $key + 1;
            $type['propertyGroup'] = $grp['group_code'];
            $type['propertyGroupName'] = $grp['group_name'];
            $type['Jul'] = 0;
            $type['Aug'] = 0;
            $type['Sep'] = 0;
            $type['Oct'] = 0;
            $type['Nov'] = 0;
            $type['Dec'] = 0;
            $type['Jan'] = 0;
            $type['Feb'] = 0;
            $type['Mar'] = 0;
            $type['Apr'] = 0;
            $type['May'] = 0;
            $type['Jun'] = 0;
            $financialData[] = $type;
        }
    }
    foreach ($resultFinance as $key => $row) {
        $expense = ['expenseCRFee', 'maintenanceFee', 'expenseWRFee'];
        foreach ($financeColumn as $val) {
            $totalIncome[$val[0]] += $row['accountType'] === 'income' ? $row[$val[0]] : 0;
            $totalExpense[$val[0]] += in_array($row['accountType'], $expense) ? $row[$val[0]] : 0;
            $totalRevenueGross[$val[0]] += ($row['accountType'] === 'income' ? $row[$val[0]] : 0) - (in_array(
                $row['accountType'],
                $expense
            ) ? $row[$val[0]] : 0);
            $totalFundTransfer[$val[0]] += $row['accountType'] === 'transferFund' ? $row[$val[0]] : 0;
        }

        foreach ($financialData as $k => $default) {
            if ($default['accountType'] === $row['accountType'] && $default['propertyGroup'] === $row['propertyGroup']) {
                $financialData[$k]['orderby'] = $row['orderby'];
                $financialData[$k]['Jul'] = $row['Jul'];
                $financialData[$k]['Aug'] = $row['Aug'];
                $financialData[$k]['Sep'] = $row['Sep'];
                $financialData[$k]['Oct'] = $row['Oct'];
                $financialData[$k]['Nov'] = $row['Nov'];
                $financialData[$k]['Dec'] = $row['Dec'];
                $financialData[$k]['Jan'] = $row['Jan'];
                $financialData[$k]['Feb'] = $row['Feb'];
                $financialData[$k]['Mar'] = $row['Mar'];
                $financialData[$k]['Apr'] = $row['Apr'];
                $financialData[$k]['May'] = $row['May'];
                $financialData[$k]['Jun'] = $row['Jun'];
            }
        }
    }

    $totalIncome['propertyGroupName'] = 'TOTAL FUNDS BANKED (NETT)';
    $totalExpense['propertyGroupName'] = 'TOTAL EXPENSES';
    $totalRevenueGross['propertyGroupName'] = 'TOTAL REVENUE (GROSS)';
    $totalFundTransfer['propertyGroupName'] = 'TOTAL TRANSFERRED TO OTHER PORTFOLIOS';
    $totalIncome['accountDescription'] = '';
    $totalExpense['accountDescription'] = '';
    $totalRevenueGross['accountDescription'] = '';
    $totalFundTransfer['accountDescription'] = '';
    $financialData[] = $totalIncome;
    $financialData[] = $totalExpense;
    $financialData[] = $totalRevenueGross;
    $financialData[] = $totalFundTransfer;
    // SQL Process for Project Level Tab
    $sql = "
        select
        CONCAT(RTRIM(prop.pmpr_street), ', ',RTRIM(prop.pmpr_city), ', ', RTRIM(prop.pmpr_state), ' ', RTRIM(prop.pmpr_postcode)) as address,
        prop.pmpr_prop_group,
        gc.group_name,
        prop.pmpr_prop,
        unit.pmpu_unit,
        prop.pmpr_postcode as postCode,
        prop.pmpr_city as suburb,
        SUM(
        CASE WHEN trial_balance_total.accountID IN {$gstInc} THEN trial_balance_total.total ELSE 0 END
        )*-1 income,
        SUM(
        CASE WHEN trial_balance_total.accountID IN {$accountsCompensationRentFee} THEN trial_balance_total.total ELSE 0 END
        ) compensationRentFee,
        SUM(
        CASE WHEN trial_balance_total.accountID IN {$accountsExpenseGMFee} THEN trial_balance_total.total ELSE 0 END
        ) expenseGMFee,
        SUM(
        CASE WHEN trial_balance_total.accountID IN {$accountsExpenseEFee} THEN trial_balance_total.total ELSE 0 END
        ) expenseEFee,
        SUM(
        CASE WHEN trial_balance_total.accountID IN {$accountsExpensePFee} THEN trial_balance_total.total ELSE 0 END
        ) expensePFee,
        SUM(
        CASE WHEN trial_balance_total.accountID IN {$accountsExpenseGFee} THEN trial_balance_total.total ELSE 0 END
        ) expenseGFee,
        SUM(
        CASE WHEN trial_balance_total.accountID IN {$accountsExpenseSFee} THEN trial_balance_total.total ELSE 0 END
        ) expenseSFee,
        SUM(
        CASE WHEN trial_balance_total.accountID IN {$accountsExpenseCRFee} THEN trial_balance_total.total ELSE 0 END
        ) expenseCRFee,
        SUM(
        CASE WHEN trial_balance_total.accountID IN {$accountsExpenseSROFee} THEN trial_balance_total.total ELSE 0 END
        ) expenseSROFee,
        SUM(
        CASE WHEN trial_balance_total.accountID IN {$accountsExpenseWRFee} THEN trial_balance_total.total ELSE 0 END
        ) expenseWRFee,
        SUM(
        CASE WHEN trial_balance_total.accountID IN {$accountExpenseStrataFee} THEN trial_balance_total.total ELSE 0 END
        ) expenseStrataFee,
        SUM(
        CASE WHEN trial_balance_total.accountID IN {$accountExpenseIFee} THEN trial_balance_total.total ELSE 0 END
        ) expenseIFee,
        SUM(
        CASE WHEN trial_balance_total.accountID IN {$accountExpenseMFFee} THEN trial_balance_total.total ELSE 0 END
        ) expenseMFFee,
        SUM(
        CASE WHEN trial_balance_total.accountID IN {$accountExpenseOthersMCFee} THEN trial_balance_total.total ELSE 0 END
        ) expenseOthersMCFee,
        gst_expense.tax_total as expenseTaxAmount,
        gst_totals.tax_total as gstPaidReceived,
        payments_to_owner.amount as ownersFee,
        COALESCE(tenant_name, '') as leaseName,
        arrears.arrears_amount as arrearsBalance,
        lease_start_date as unitLeaseStartDate,
        lease_expiry_date as leaseExpireDate,
        lease_status,
        vacate_date,
        total_days_vacant,
        fyIncome2018.year_income income2017,
        fyExpense2018.year_expense expense2017,
        fyIncome2018.year_income - fyExpense2018.year_expense balance201718,
        fyIncome2019.year_income income2018,
        fyExpense2019.year_expense expense2018,
        fyIncome2019.year_income - fyExpense2019.year_expense balance201819,
        fyIncome2020.year_income income2019,
        fyExpense2020.year_expense expense2019,
        fyIncome2020.year_income - fyExpense2020.year_expense balance201920,
        fyIncome2021.year_income income2020,
        fyExpense2021.year_expense expense2020,
        fyIncome2021.year_income - fyExpense2021.year_expense balance202021,
        fyIncome2022.year_income income2021,
        fyExpense2022.year_expense expense2021,
        fyIncome2022.year_income - fyExpense2022.year_expense balance202122,
        fyIncome2023.year_income income2022,
        fyExpense2023.year_expense expense2022,
        fyIncome2023.year_income - fyExpense2023.year_expense balance202223,
        fyIncome2024.year_income income2023,
        fyExpense2024.year_expense expense2023,
        fyIncome2024.year_income - fyExpense2024.year_expense balance202324,
        fyIncome2025.year_income income2024,
        fyExpense2025.year_expense expense2024,
        fyIncome2025.year_income - fyExpense2025.year_expense balance202425,
        fyIncome2026.year_income income2025,
        fyExpense2026.year_expense expense2025,
        fyIncome2026.year_income - fyExpense2026.year_expense balance202526,
        fyIncome2027.year_income income2026,
        fyExpense2027.year_expense expense2026,
        fyIncome2027.year_income - fyExpense2027.year_expense balance202627
        FROM
        pmpr_property prop
        JOIN pmpu_p_unit unit ON unit.pmpu_prop = prop.pmpr_prop
        LEFT JOIN (
        SELECT
            propertyID,
            accountID,
            COALESCE(SUM(balanceCash), 0) total
        FROM
            gl_trial_balance
        WHERE
            period >= " . addSQLParam($params, $fromPeriod) . '
            AND period <= ' . addSQLParam($params, $toPeriod) . '
            AND year = ' . addSQLParam($params, $year) . '
        GROUP BY
            propertyID,
            accountID
        ) as trial_balance_total ON trial_balance_total.propertyID = prop.pmpr_prop
        LEFT JOIN (
        SELECT
                pmxc_prop as property,
                COALESCE(SUM(pmxc_tax_amt), 0) as tax_total
            FROM
                pmxc_ap_alloc
            WHERE
                pmxc_period >= ' . addSQLParam($params, $fromPeriod) . '
                AND pmxc_period <= ' . addSQLParam($params, $toPeriod) . '
                AND pmxc_year = ' . addSQLParam($params, $year) . "
                AND pmxc_f_type = 'PAY'
            GROUP BY
                pmxc_prop
        ) as gst_expense  ON gst_expense.property = prop.pmpr_prop
        LEFT JOIN (
        SELECT
            property,
            COALESCE(SUM(tax_total), 0) as tax_total
        FROM
            (
            SELECT
                pmxc_prop as property,
                COALESCE(SUM(pmxc_tax_amt), 0) as tax_total
            FROM
                pmxc_ap_alloc
            WHERE
                pmxc_period >= " . addSQLParam($params, $fromPeriod) . '
                AND pmxc_period <= ' . addSQLParam($params, $toPeriod) . '
                AND pmxc_year = ' . addSQLParam($params, $year) . "
                AND pmxc_f_type = 'PAY'
            GROUP BY
                pmxc_prop
            UNION ALL
            SELECT
                pmxd_prop as property,
                COALESCE(SUM(pmxd_tax_amt), 0) as tax_total
            FROM
                pmxd_ar_alloc
            WHERE
                pmxd_period >= " . addSQLParam($params, $fromPeriod) . '
                AND pmxd_period <= ' . addSQLParam($params, $toPeriod) . '
                AND pmxd_year = ' . addSQLParam($params, $year) . "
                AND pmxd_f_type = 'CSH'
            GROUP BY
                pmxd_prop
            ) x
        GROUP BY
            property
        ) as gst_totals ON gst_totals.property = prop.pmpr_prop
        LEFT JOIN (
        SELECT
            pmxc_prop,
            COALESCE(SUM(pmxc_alloc_amt), 0) as amount,
            COALESCE(SUM(pmxc_tax_amt), 0) as tax_amount
        FROM
            pmxc_ap_alloc
        WHERE
            (
            pmxc_acc IN (
                SELECT
                pmcg_acc
                FROM
                pmcg_chart_grp
                WHERE
                (pmcg_grp = 'TRACC3')
                AND (pmcg_subgrp = 'BSPMTREMI')
            )
            )
            AND (
            pmxc_period >= " . addSQLParam($params, $fromPeriod) . '
            AND pmxc_period <= ' . addSQLParam($params, $toPeriod) . '
            AND pmxc_year = ' . addSQLParam($params, $year) . "
            )
            AND (pmxc_f_type = 'PAY')
        GROUP BY
            pmxc_prop
        ) as payments_to_owner ON payments_to_owner.pmxc_prop = prop.pmpr_prop
        LEFT JOIN (
        SELECT
            ROW_NUMBER() OVER (
            PARTITION BY pmua_prop,
            pmua_unit
            ORDER BY
                pmua_to_dt DESC
            ) AS rn,
            pmua_prop,
            pmua_unit,
            pmua_from_dt,
            pmua_to_dt,
            pmle_lease,
            pmle_name as tenant_name,
            pmle_com_dt as lease_start_date,
            pmle_exp_dt as lease_expiry_date,
            CASE WHEN pmle_exp_dt >= CONVERT(datetime, " . addSQLParam(
        $params,
        $reportToDate
    ) . ", 103) THEN 'Leased' WHEN pmle_exp_dt < CONVERT(datetime, " . addSQLParam($params, $reportToDate) . ", 103) THEN 'Overholding' ELSE 'Vacant' END as lease_status,
            CASE WHEN pmua_status = 'V' THEN DATEADD(DAY,-1, pmua_from_dt) WHEN pmua_to_dt = '2999-12-31' THEN NULL ELSE NULL END as vacate_date,
            CASE WHEN pmua_status = 'V' THEN DATEDIFF(
            DAY,
            DATEADD(DAY,-1, pmua_from_dt),
            CONVERT(datetime, " . addSQLParam($params, $reportToDate) . ", 103)
            ) WHEN pmua_to_dt = '2999-12-31' THEN NULL ELSE NULL END as total_days_vacant
        FROM
            pmua_unit_area
            LEFT JOIN pmle_lease ON pmua_prop = pmle_prop
            AND pmua_lease = pmle_lease
        ) as lease ON lease.pmua_prop = prop.pmpr_prop
        AND lease.pmua_unit = unit.pmpu_unit
        AND lease.rn = 1
        LEFT JOIN (
        SELECT
            PropertyCode,
            LeaseCode,
            COALESCE(SUM(OutstandingGross), 0) as arrears_amount
        FROM
            FN_get_outstanding_AR_active_properties_all_leases (
            CONVERT(datetime, " . addSQLParam($params, $reportToDate) . ', 103),
            CONVERT(datetime, ' . addSQLParam($params, $reportToDate) . ", 103)
            )
        GROUP BY
            PropertyCode,
            LeaseCode
        ) as arrears ON arrears.PropertyCode = prop.pmpr_prop
        AND arrears.LeaseCode = lease.pmle_lease
        LEFT JOIN (
        SELECT
            propertyID,
            COALESCE(SUM(balanceCash), 0)*-1 as year_income
        FROM
            gl_trial_balance
        WHERE
            accountID IN {$gstInc}
            AND year = 2018
        GROUP by
            propertyID
        ) as fyIncome2018 ON fyIncome2018.propertyID = prop.pmpr_prop
        LEFT JOIN (
        SELECT
            propertyID,
            COALESCE(SUM(balanceCash), 0) as year_expense
        FROM
            gl_trial_balance
        WHERE
            accountID IN {$gstExp}
            AND year = 2018
        GROUP by
            propertyID
        ) as fyExpense2018 ON fyExpense2018.propertyID = prop.pmpr_prop
        LEFT JOIN (
        SELECT
            propertyID,
            COALESCE(SUM(balanceCash), 0)*-1 as year_income
        FROM
            gl_trial_balance
        WHERE
            accountID IN {$gstInc}
            AND year = 2019
        GROUP by
            propertyID
        ) as fyIncome2019 ON fyIncome2019.propertyID = prop.pmpr_prop
        LEFT JOIN (
        SELECT
            propertyID,
            COALESCE(SUM(balanceCash), 0) as year_expense
        FROM
            gl_trial_balance
        WHERE
            accountID IN {$gstExp}
            AND year = 2019
        GROUP by
            propertyID
        ) as fyExpense2019 ON fyExpense2019.propertyID = prop.pmpr_prop
        LEFT JOIN (
        SELECT
            propertyID,
            COALESCE(SUM(balanceCash), 0)*-1 as year_income
        FROM
            gl_trial_balance
        WHERE
            accountID IN {$gstInc}
            AND year = 2020
        GROUP by
            propertyID
        ) as fyIncome2020 ON fyIncome2020.propertyID = prop.pmpr_prop
        LEFT JOIN (
        SELECT
            propertyID,
            COALESCE(SUM(balanceCash), 0) as year_expense
        FROM
            gl_trial_balance
        WHERE
            accountID IN {$gstExp}
            AND year = 2020
        GROUP by
            propertyID
        ) as fyExpense2020 ON fyExpense2020.propertyID = prop.pmpr_prop
        LEFT JOIN (
        SELECT
            propertyID,
            COALESCE(SUM(balanceCash), 0)*-1 as year_income
        FROM
            gl_trial_balance
        WHERE
            accountID IN {$gstInc}
            AND year = 2021
        GROUP by
            propertyID
        ) as fyIncome2021 ON fyIncome2021.propertyID = prop.pmpr_prop
        LEFT JOIN (
        SELECT
            propertyID,
            COALESCE(SUM(balanceCash), 0) as year_expense
        FROM
            gl_trial_balance
        WHERE
            accountID IN {$gstExp}
            AND year = 2021
        GROUP by
            propertyID
        ) as fyExpense2021 ON fyExpense2021.propertyID = prop.pmpr_prop
        LEFT JOIN (
        SELECT
            propertyID,
            COALESCE(SUM(balanceCash), 0)*-1 as year_income
        FROM
            gl_trial_balance
        WHERE
            accountID IN {$gstInc}
            AND year = 2022
        GROUP by
            propertyID
        ) as fyIncome2022 ON fyIncome2022.propertyID = prop.pmpr_prop
        LEFT JOIN (
        SELECT
            propertyID,
            COALESCE(SUM(balanceCash), 0) as year_expense
        FROM
            gl_trial_balance
        WHERE
            accountID IN {$gstExp}
            AND year = 2022
        GROUP by
            propertyID
        ) as fyExpense2022 ON fyExpense2022.propertyID = prop.pmpr_prop
        LEFT JOIN (
        SELECT
            propertyID,
            COALESCE(SUM(balanceCash), 0)*-1 as year_income
        FROM
            gl_trial_balance
        WHERE
            accountID IN {$gstInc}
            AND year = 2023
        GROUP by
            propertyID
        ) as fyIncome2023 ON fyIncome2023.propertyID = prop.pmpr_prop
        LEFT JOIN (
        SELECT
            propertyID,
            COALESCE(SUM(balanceCash), 0) as year_expense
        FROM
            gl_trial_balance
        WHERE
            accountID IN {$gstExp}
            AND year = 2023
        GROUP by
            propertyID
        ) as fyExpense2023 ON fyExpense2023.propertyID = prop.pmpr_prop
        LEFT JOIN (
        SELECT
            propertyID,
            COALESCE(SUM(balanceCash), 0)*-1 as year_income
        FROM
            gl_trial_balance
        WHERE
            accountID IN {$gstInc}
            AND year = 2024
        GROUP by
            propertyID
        ) as fyIncome2024 ON fyIncome2024.propertyID = prop.pmpr_prop
        LEFT JOIN (
        SELECT
            propertyID,
            COALESCE(SUM(balanceCash), 0) as year_expense
        FROM
            gl_trial_balance
        WHERE
            accountID IN {$gstExp}
            AND year = 2024
        GROUP by
            propertyID
        ) as fyExpense2024 ON fyExpense2024.propertyID = prop.pmpr_prop
        LEFT JOIN (
        SELECT
            propertyID,
            COALESCE(SUM(balanceCash), 0)*-1 as year_income
        FROM
            gl_trial_balance
        WHERE
            accountID IN {$gstInc}
            AND year = 2025
        GROUP by
            propertyID
        ) as fyIncome2025 ON fyIncome2025.propertyID = prop.pmpr_prop
        LEFT JOIN (
        SELECT
            propertyID,
            COALESCE(SUM(balanceCash), 0) as year_expense
        FROM
            gl_trial_balance
        WHERE
            accountID IN {$gstExp}
            AND year = 2025
        GROUP by
            propertyID
        ) as fyExpense2025 ON fyExpense2025.propertyID = prop.pmpr_prop
        LEFT JOIN (
        SELECT
            propertyID,
            COALESCE(SUM(balanceCash), 0)*-1 as year_income
        FROM
            gl_trial_balance
        WHERE
            accountID IN {$gstInc}
            AND year = 2026
        GROUP by
            propertyID
        ) as fyIncome2026 ON fyIncome2026.propertyID = prop.pmpr_prop
        LEFT JOIN (
        SELECT
            propertyID,
            COALESCE(SUM(balanceCash), 0) as year_expense
        FROM
            gl_trial_balance
        WHERE
            accountID IN {$gstExp}
            AND year = 2026
        GROUP by
            propertyID
        ) as fyExpense2026 ON fyExpense2026.propertyID = prop.pmpr_prop
        LEFT JOIN (
        SELECT
            propertyID,
            COALESCE(SUM(balanceCash), 0)*-1 as year_income
        FROM
            gl_trial_balance
        WHERE
            accountID IN {$gstInc}
            AND year = 2027
        GROUP by
            propertyID
        ) as fyIncome2027 ON fyIncome2027.propertyID = prop.pmpr_prop
        LEFT JOIN (
        SELECT
            propertyID,
            COALESCE(SUM(balanceCash), 0) as year_expense
        FROM
            gl_trial_balance
        WHERE
            accountID IN {$gstExp}
            AND year = 2027
        GROUP by
            propertyID
        ) as fyExpense2027 ON fyExpense2027.propertyID = prop.pmpr_prop
        LEFT JOIN (
            select DISTINCT(GROUP_CODE) as group_code, pmzz_desc as group_name
                from crp_property, pmzz_param
                where pmzz_par_type = 'PROPGROUP'
                and pmzz_code = group_code
        ) gc on gc.group_code = prop.pmpr_prop_group
        WHERE prop.pmpr_prop_group IN {$propGroupID} AND prop.pmpr_delete !=1
        GROUP BY
        prop.pmpr_prop_group,
        gc.group_name,
        prop.pmpr_prop,
        prop.pmpr_street,
        unit.pmpu_unit,
        prop.pmpr_city,
        prop.pmpr_postcode,
        prop.pmpr_state,
        gst_expense.tax_total,
        gst_totals.tax_total,
        payments_to_owner.amount,
        tenant_name,
        arrears.arrears_amount,
        lease_start_date,
        lease_expiry_date,
        lease_status,
        vacate_date,
        total_days_vacant,
        fyIncome2018.year_income,
        fyExpense2018.year_expense,
        fyIncome2019.year_income,
        fyExpense2019.year_expense,
        fyIncome2020.year_income,
        fyExpense2020.year_expense,
        fyIncome2021.year_income,
        fyExpense2021.year_expense,
        fyIncome2022.year_income,
        fyExpense2022.year_expense,
        fyIncome2023.year_income,
        fyExpense2023.year_expense,
        fyIncome2024.year_income,
        fyExpense2024.year_expense,
        fyIncome2025.year_income,
        fyExpense2025.year_expense,
        fyIncome2026.year_income,
        fyExpense2026.year_expense,
        fyIncome2027.year_income,
        fyExpense2027.year_expense
        ORDER BY
        gc.group_name,
        prop.pmpr_prop
    ";
    $result = $dbh->executeSet($sql, false, true, $params);
    foreach ($result as $k => $line) {
        $key = array_search($line['pmpr_prop'], array_map(function ($data) {
            return $data['pmpr_prop'];
        }, $result));
        if ($key !== false && $k != $key) {
            $line['income'] = 0;
            $line['compensationRentFee'] = 0;
            $line['expenseGMFee'] = 0;
            $line['expenseEFee'] = 0;
            $line['expensePFee'] = 0;
            $line['expenseGFee'] = 0;
            $line['expenseSFee'] = 0;
            $line['expenseCRFee'] = 0;
            $line['expenseSROFee'] = 0;
            $line['expenseWRFee'] = 0;
            $line['expenseStrataFee'] = 0;
            $line['expenseIFee'] = 0;
            $line['expenseMFFee'] = 0;
            $line['expenseOthersMCFee'] = 0;
            $line['expenseTaxAmount'] = 0;
            $line['gstPaidReceived'] = 0;
            $line['ownersFee'] = 0;
            $line['income2017'] = 0;
            $line['expense2018'] = 0;
            $line['balance201819'] = 0;
            $line['income2017'] = 0;
            $line['expense2017'] = 0;
            $line['balance201718'] = 0;
            $line['income2018'] = 0;
            $line['expense2018'] = 0;
            $line['balance201819'] = 0;
            $line['income2019'] = 0;
            $line['expense2019'] = 0;
            $line['balance201920'] = 0;
            $line['income2020'] = 0;
            $line['expense2020'] = 0;
            $line['balance202021'] = 0;
            $line['income2021'] = 0;
            $line['expense2021'] = 0;
            $line['balance202122'] = 0;
            $line['income2022'] = 0;
            $line['expense2022'] = 0;
            $line['balance202223'] = 0;
            $line['income2023'] = 0;
            $line['expense2023'] = 0;
            $line['balance202324'] = 0;
            $line['income2024'] = 0;
            $line['expense2024'] = 0;
            $line['balance202425'] = 0;
            $line['income2025'] = 0;
            $line['expense2025'] = 0;
            $line['balance202526'] = 0;
            $line['income2026'] = 0;
            $line['expense2026'] = 0;
            $line['balance202627'] = 0;
            $line['sumAllFYBalance'] = 0;
            $line['propertyCost'] = 0;
            $line['costNeutralityBalance'] = 0;
        }

        $line['totalMonthlyExpense'] = $line['compensationRentFee'] + $line['expenseGMFee'] + $line['expenseEFee'] + $line['expensePFee'] + $line['expenseGFee'] +
            $line['expenseSFee'] + $line['expenseCRFee'] + $line['expenseSROFee'] + $line['expenseWRFee'] + $line['expenseStrataFee'] +
            $line['expenseIFee'] + $line['expenseMFFee'] + $line['expenseOthersMCFee'];
        $line['totalMonthlyExpensesGross'] = $line['totalMonthlyExpense'] + $line['expenseTaxAmount'];
        $notes = propertyPIMSNotes($line, $dbh);
        $line['sumAllFYBalance'] = $line['balance202627'] + $line['balance202526'] + $line['balance202425'] + $line['balance202324'] + $line['balance202223'] + $line['balance202122'] +
            $line['balance202021'] + $line['balance201920'] + $line['balance201819'] + $line['balance201718'];
        $line['alliancePartner'] = $notes['PIMS Alliance Partner'];
        $line['propertyNo'] = $notes['PIMS Property No.'];
        $line['pimsStage'] = $notes['PIMS Stage'];
        $line['propertyId'] = $notes['PIMS Property Id'];
        $line['propertyType'] = $notes['PIMS Property Type'];
        $line['propertyCost'] = $notes['PIMS Property Cost'];
        $line['project'] = $notes['PIMS Project'];
        $line['costNeutralityBalance'] = $line['propertyCost'] - $line['sumAllFYBalance'];
        $return[] = $line;
    }

    return [
        'projectLevelData' => $return,
        'financeLevelData' => $financialData,
        'financeColumn' => $financeColumn,
    ];
}

function propertyPIMSNotes($params, $dbh)
{
    $sql = "
        select
            MAX(CASE WHEN pmpn_header = 'PIMS Project' THEN pmpn_note ELSE '' END) as 'PIMS Project',
            MAX(CASE WHEN pmpn_header = 'PIMS Alliance Partner' THEN pmpn_note ELSE '' END) as 'PIMS Alliance Partner',
            MAX(CASE WHEN pmpn_header = 'PIMS Property Cost' THEN pmpn_note ELSE '' END) as 'PIMS Property Cost',
            MAX(CASE WHEN pmpn_header = 'PIMS Stage' THEN pmpn_note ELSE '' END) as 'PIMS Stage',
            MAX(CASE WHEN pmpn_header = 'PIMS Property ID' THEN pmpn_note ELSE '' END) as 'PIMS Property Id',
            MAX(CASE WHEN pmpn_header = 'PIMS Property No' THEN pmpn_note ELSE '' END) as 'PIMS Property No.',
            MAX(CASE WHEN pmpn_header = 'PIMS Property Type' THEN pmpn_note ELSE '' END) as 'PIMS Property Type'
        from
            pmpn_p_note
        where
            pmpn_prop = '{$params['pmpr_prop']}' AND pmpn_header in ('PIMS Alliance Partner','PIMS Property Cost','PIMS Stage','PIMS Property ID', 'PIMS Property No', 'PIMS Property Type', 'PIMS Project')
    ";

    return $dbh->executeSingle($sql);
}

function propertyPIMSParams($propertyGroup, $dbh)
{
    $params = [];
    $sql = "
    SELECT
			pmzz_code as parameterID,
			pmzz_desc as parameterDescription
		FROM
			pmzz_param
		WHERE
			pmzz_par_type = 'PIMSGROUP' AND pmzz_code = " . addSQLParam($params, $propertyGroup) . '
			ORDER BY pmzz_code
    ';

    return $dbh->executeSingle($sql, $params);
}

function fiscalIncomeExpense($params, $gstIncAcc, $gstExpAcc)
{
    global $dbh;
    $sql = "
        SELECT
            sum((CASE WHEN gl.year = 2018 THEN gl.balanceCash ELSE 0 END)) AS income2017,
            sum((CASE WHEN gl.year = 2019 THEN gl.balanceCash ELSE 0 END)) AS income2018,
            sum((CASE WHEN gl.year = 2020 THEN gl.balanceCash ELSE 0 END)) AS income2019,
            sum((CASE WHEN gl.year = 2021 THEN gl.balanceCash ELSE 0 END)) AS income2020,
            sum((CASE WHEN gl.year = 2022 THEN gl.balanceCash ELSE 0 END)) AS income2021,
            sum((CASE WHEN gl.year = 2023 THEN gl.balanceCash ELSE 0 END)) AS income2022,
            sum((CASE WHEN gl.year = 2024 THEN gl.balanceCash ELSE 0 END)) AS income2023,
            sum((CASE WHEN gl.year = 2025 THEN gl.balanceCash ELSE 0 END)) AS income2024,
            sum((CASE WHEN gl.year = 2026 THEN gl.balanceCash ELSE 0 END)) AS income2025,
            sum((CASE WHEN gl.year = 2027 THEN gl.balanceCash ELSE 0 END)) AS income2026
        FROM dot_property_group fiscalIncome
        LEFT JOIN (select year, period, accountID, propertyID, (balanceCash* -1) as balanceCash from gl_trial_balance
            where year >= 2017 AND year <= 2026
            AND accountID IN {$gstIncAcc}
           ) as gl on gl.propertyID = fiscalIncome.pmpr_prop
        WHERE fiscalIncome.pmle_lease = '{$params['pmle_lease']}'
            AND fiscalIncome.pmpr_prop = '{$params['pmpr_prop']}'
            AND fiscalIncome.pmpr_prop_group = '{$params['pmpr_prop_group']}'
        UNION ALL
        SELECT
            sum((CASE WHEN gle.year = 2018 THEN gle.balanceCash ELSE 0 END))*-1 AS expense2017,
            sum((CASE WHEN gle.year = 2019 THEN gle.balanceCash ELSE 0 END))*-1 AS expense2018,
            sum((CASE WHEN gle.year = 2020 THEN gle.balanceCash ELSE 0 END))*-1 AS expense2019,
            sum((CASE WHEN gle.year = 2021 THEN gle.balanceCash ELSE 0 END))*-1 AS expense2020,
            sum((CASE WHEN gle.year = 2022 THEN gle.balanceCash ELSE 0 END))*-1 AS expense2021,
            sum((CASE WHEN gle.year = 2023 THEN gle.balanceCash ELSE 0 END))*-1 AS expense2022,
            sum((CASE WHEN gle.year = 2024 THEN gle.balanceCash ELSE 0 END))*-1 AS expense2023,
            sum((CASE WHEN gle.year = 2025 THEN gle.balanceCash ELSE 0 END))*-1 AS expense2024,
            sum((CASE WHEN gle.year = 2026 THEN gle.balanceCash ELSE 0 END))*-1 AS expense2025,
            sum((CASE WHEN gle.year = 2027 THEN gle.balanceCash ELSE 0 END))*-1 AS expense2026
        FROM dot_property_group fiscalExpense
        LEFT JOIN (select year, period, accountID, propertyID, (balanceCash* -1) as balanceCash from gl_trial_balance
        where year >= 2017 AND year <= 2026
        AND accountID IN {$gstExpAcc}
       ) as gle on gle.propertyID = fiscalExpense.pmpr_prop
        WHERE fiscalExpense.pmle_lease = '{$params['pmle_lease']}'
            AND fiscalExpense.pmpr_prop = '{$params['pmpr_prop']}'
            AND fiscalExpense.pmpr_prop_group = '{$params['pmpr_prop_group']}'
        ";

    return $dbh->executeSet($sql);
}

function propertyGroupDotReportProcess(&$context)
{
    global $pathPrefix, $clientDirectory;

    if (! isPostback()) {
        $view = new MasterPage(userViews(), '/managementReports/propertyGroupScheduleProcess.html');
        $view->setSection($context['module']);
    } else {
        $view = new UserControl(userViews(), '/managementReports/propertyGroupScheduleProcess.html');
    }

    $view->bindAttributesFrom($_REQUEST);


    global $clientDirectory, $pathPrefix;

    [$d, $m, $y] = explode('/', $view->items['startDate']);
    $title = "$d.$m.$y";
    $filename = "$title" . '_dot_project_and_finance' . '.xlsx';
    $_filePath = "{$pathPrefix}{$clientDirectory}/xlsx/PropertyGroupSchedule/";
    checkDirPath($_filePath);
    $_downloadPath = "{$clientDirectory}/xlsx/PropertyGroupSchedule";
    $downloadLink = $_downloadPath . '/' . $filename; // for download link (used to display dl link on page)
    $filePath = $_filePath . $filename; // for attachment (can used for email attachment also)


    if (isset($view->items['propGroupID'])) {
        $view->items['propGroupID'] = deserializeParameters($view->items['propGroupID']);
    }
    $number_format = '_(* #,##0.00_);_(* (#,##0.00);_(* "-"??_);_(@_)';

    $columns = [
        ['group_name', 'PIMS Project', 'left'],
        ['propertyNo', 'PIMS Property No', 'left'],
        ['pimsStage', 'PIMS Stage', 'left'],
        ['alliancePartner', 'Alliance Partner', 'left'],
        ['address', 'Property Address', 'left'],
        ['suburb', 'Property ' . ucwords(strtolower($_SESSION['country_default']['suburb'])), 'left'],
        ['postCode', 'Property Postcode', 'left'],
        ['propertyId', 'PIMS Property ID', 'left'],
        ['propertyType', 'Property Type', 'left'],
        ['income', 'Total Monthly Income', 'right', $number_format],
        ['compensationRentFee', 'Rent Fee/Compensation Waiver', 'right', $number_format],
        ['expenseGMFee', 'Monthly Expenses - General Repairs/Maintenance', 'right', $number_format],
        ['expenseEFee', 'Monthly Expenses Electrical', 'right', $number_format],
        ['expensePFee', 'Monthly Expenses Plumbing', 'right', $number_format],
        ['expenseGFee', 'Monthly Expenses Gardening/Cleaning', 'right', $number_format],
        ['expenseSFee', 'Monthly Expenses Security', 'right', $number_format],
        ['expenseCRFee', 'Monthly Expenses Council Rates', 'right', $number_format],
        ['expenseSROFee', 'SRO Land Tax', 'right', $number_format],
        ['expenseWRFee', 'Monthly Expenses Water rates', 'right', $number_format],
        [
            'expenseStrataFee',
            'Owners Corporation/' . ucwords(strtolower($_SESSION['country_default']['strata'])) . ' Fees',
            'right',
            $number_format,
        ],
        ['expenseIFee', 'Inspection Fees', 'right', $number_format],
        ['expenseMFFee', 'Management Fees', 'right', $number_format],
        ['expenseOthersMCFee', 'Other Miscellanious Charges & Fees', 'right', $number_format],
        ['totalMonthlyExpense', 'Total Monthly Expenses', 'right', $number_format],
        ['gstPaidReceived', 'GST Paid/Received', 'right', $number_format],
        ['totalMonthlyExpensesGross', 'Total Monthly Expenses (Gross)', 'right', $number_format],
        ['ownersFee', 'Total Paid to DTP', 'right', $number_format],
        ['leaseName', 'Tenant Name', 'left'],
        ['arrearsBalance', 'Current Arrears Balance', 'right', $number_format],
        ['unitLeaseStartDate', 'Lease Start Date', 'left'],
        ['leaseExpireDate', 'Lease Expiry Date', 'left'],
        ['lease_status', 'Lease Status', 'left'],
        ['vacate_date', 'Vacate Date', 'left'],
        ['vacantReason', 'Reason property is vacant', 'left'],
        ['total_days_vacant', 'Total Days Vacant', 'right'],
        ['income2017', '2017/2018 FY Income', 'left', $number_format],
        ['expense2017', '2017/2018 FY Expense', 'left', $number_format],
        ['balance201718', '2017/2018 FY Balance', 'left', $number_format],

        ['income2018', '2018/2019 FY Income', 'left', $number_format],
        ['expense2018', '2018/2019 FY Expense', 'left', $number_format],
        ['balance201819', '2018/2019 FY Balance', 'left', $number_format],

        ['income2019', '2019/2020 FY Income', 'left', $number_format],
        ['expense2019', '2019/2020 FY Expense', 'left', $number_format],
        ['balance201920', '2019/2020 FY Balance', 'left', $number_format],

        ['income2020', '2020/2021 FY Income', 'left', $number_format],
        ['expense2020', '2020/2021 FY Expense', 'left', $number_format],
        ['balance202021', '2020/2021 FY Balance', 'left', $number_format],

        ['income2021', '2021/2022 FY Income', 'left', $number_format],
        ['expense2021', '2021/2022 FY Expense', 'left', $number_format],
        ['balance202122', '2021/2022 FY Balance', 'left', $number_format],

        ['income2022', '2022/2023 FY Income', 'left', $number_format],
        ['expense2022', '2022/2023 FY Expense', 'left', $number_format],
        ['balance202223', '2022/2023 FY Balance', 'left', $number_format],

        ['income2023', '2023/2024 FY Income', 'left', $number_format],
        ['expense2023', '2023/2024 FY Expense', 'left', $number_format],
        ['balance202324', '2023/2024 FY Balance', 'left', $number_format],

        ['income2024', '2024/2025 FY Income', 'left', $number_format],
        ['expense2024', '2024/2025 FY Expense', 'left', $number_format],
        ['balance202425', '2024/2025 FY Balance', 'left', $number_format],

        ['income2025', '2025/2026 FY Income', 'left', $number_format],
        ['expense2025', '2025/2026 FY Expense', 'left', $number_format],
        ['balance202526', '2025/2026 FY Balance', 'left', $number_format],

        ['income2026', '2026/2027 FY Income', 'left', $number_format],
        ['expense2026', '2026/2027 FY Expense', 'left', $number_format],
        ['balance202627', '2026/2027 FY Balance', 'left', $number_format],

        ['sumAllFYBalance', 'FY Balance', 'left', $number_format],
        ['propertyCost', 'PIMS Property Cost', 'left', $number_format],
        ['costNeutralityBalance', 'Cost Neutrality Balance', 'left', $number_format],

    ];

    $columnsFinance = [
        ['propertyGroupName', 'Property Group', 'left'],
        ['ent', 'Ent', 'left'],
        ['account', 'Account', 'left'],
        ['cc', 'CC', 'left'],
        ['initiative', 'Initiative', 'left'],
        ['authority', 'Authority', 'left'],
        ['property', 'Property', 'left'],
        ['output', 'Output', 'left'],
        ['identifier', 'Identifier', 'left'],
        ['relatedParty', 'Related Party', 'left'],
        ['currency', 'Currency', 'left'],
        ['accountDescription', 'Description', 'left'],
    ];

    $sheets = ['PIMS @ Project Level', 'Finance Team'];
    $index = 0;
    $propGroupData = getPropGroupData(
        $view->items['propGroupID'],
        $view->items['startDate'],
        $view->items['endDate'],
        $number_format
    );
    $columnsFinance = array_merge($columnsFinance, $propGroupData['financeColumn']);

    foreach ($sheets as $key => $propGroupID) {
        $header1 = $propGroupID . ' - ' . dbgetParam('PROPGROUP', $propGroupID);
        $header2 = 'MANAGER : ' . dbgetParam('PORTMGR', $propGroupID);
        $header = $header1 . "\n" . $header2;
        $columns = $key == 1 ? $columnsFinance : $columns;
        $propertyGroupData = $key == 1 ? $propGroupData['financeLevelData'] : $propGroupData['projectLevelData'];
        if (! isset($report)) {
            checkDirPath($filePath, true);
            $report = new XLSDataReport($filePath, $propGroupID);
            $report->enableFormatting = true;

            foreach ($columns as $column) {
                $report->addColumn($column[0], $column[1], 200, $column[2], $column[3]);
            }

            $report->renderHeaderDetails($header);
            $report->renderHeader();
            $report->freezePanes(0, 5);
            $report->renderData($propertyGroupData);
        } else {
            $report->setSheetDetails($index, $propGroupID);
            $report->line = 1;
            $report->columns = [];

            foreach ($columns as $column) {
                $report->addColumn($column[0], $column[1], 200, $column[2], $column[3]);
            }

            $report->renderHeaderDetails($header);
            $report->renderHeader();
            $report->freezePanes(0, 5);
            $report->renderData($propertyGroupData);
        }
        $index++;
    }

    $report->clean();
    $report->endPage();
    $report->close();

    $view->items['downloadPath'] = $downloadLink;
    $view->render();
}

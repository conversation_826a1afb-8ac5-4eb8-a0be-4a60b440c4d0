<?php

function glJournalReportProcess($context)
{
    global $clientDirectory, $dbh, $pathPrefix, $sess;

    if (! isPostback()) {
        $view = new MasterPage(userViews(), '/managementReports/glJournalReportProcess.html');
        $view->setSection($context['module']);
    } else {
        $view = new UserControl(userViews(), '/managementReports/glJournalReportProcess.html', '');
    }
    $view->setCurrentTab('managementReports');

    $view->bindAttributesFrom($_REQUEST);

    $reportName = 'GL Journal Report';
    $format = $view->items['format'];
    $startDate = $view->items['startDate'];
    $endDate = $view->items['endDate'];
    $allDates = $view->items['allDates'];

    $logoFile = dbGetClientLogo();
    $logoPath = "assets/clientLogos/{$logoFile}";
    $_filePath = "{$pathPrefix}{$clientDirectory}/{$format}/GLJournalReport/";
    $_downloadPath = "{$clientDirectory}/{$format}/GLJournalReport";
    $file = 'GLJournalReport' . '_' . date('Ymd') . ".{$format}";
    $filePath = $_filePath . $file;
    $downloadPath = "{$_downloadPath}/{$file}";

    $result = getGLJournals($view->items['propertyID'], $view->items['accountID'], $view->items['propertyAccountingBasis'], $view->items['startDate'], $view->items['endDate']);

    if (count($result ?? []) == 0) {
        $view->items['statusMessage'] = 'No GL Journal report to print.';
    } else {

        $header = new ReportHeader($reportName);
        $header->subTitle = $view->items['propertyID'] . ' - ' . dbGetPropertyName($view->items['propertyID']);
        $header->subTitle2 = $view->items['accountID'] . ' - ' . dbGetAccountName($view->items['accountID']);
        $header->subText = 'Basis : ' . ($view->items['propertyAccountingBasis'] == 'A' ? 'Accruals' : 'Cash');

        if ($allDates != 'Yes' && $endDate && $startDate) {
            $header->subText1 = "$startDate to $endDate";
        } else {
            $header->subText1 = 'all dates';
        }

        switch ($format) {
            case FILETYPE_PDF:
                $report = new PDFDataReport($filePath, $logoPath, A4_LANDSCAPE);
                $report->multiLine = true;
                $report->printRowLines = true;
                $report->printColumnLines = false;
                $report->printBorders = false;
                $report->cache = false;
                $header->xPos = $report->hMargin;
                $header->yPos = $report->pageHeight - $report->vMargin;
                $report->attachObject('header', $header);
                $footer = new TraccFooter(null, $reportName, $report->pageSize);
                $report->attachObject('footer', $footer);
                $numberFormat = 2;
                break;
            case FILETYPE_XLS:
                $numberFormat = '_(* #,##0.00_);_(* (#,##0.00);_(* "-"??_);_(@_)';
                $report = new XLSDataReport($filePath, $reportName);
                $report->enableFormatting = true;
                if ($format == FILETYPE_XLS) {
                    $report->renderHeaderDetails($header->subTitle . "\n" .
                        $header->subTitle2 . "\n" .
                        $header->subText . "\n" .
                        $header->subText1);
                }
                break;
        }

        if ($format != FILETYPE_SCREEN) {

            $report->addColumn('journalID', 'Journal #', 50, 'left', '@');
            $report->addColumn('companyID', 'Company Code', 50, 'left', '@');
            $report->addColumn('pmco_name', 'Company Name', 130, 'left', '@');
            $report->addColumn('accountID', 'Account Code', 50, 'left', '@');
            $report->addColumn('account_name', 'Account Name', 130, 'left', '@');
            $report->addColumn('description', 'Description', 130, 'left', '@');
            $report->addColumn('transactionDate', 'Transaction Date', 50, 'left', '@');
            $report->addColumn('fromDate', 'From Date', 50, 'left', '@');
            $report->addColumn('toDate', 'To Date', 50, 'left', '@');
            $report->addColumn('amount', 'Amount', 75, 'right', $numberFormat);
            $report->preparePage();
            $report->renderHeader();

            $totalAmount = 0;
            foreach ($result as $row) {
                $report->renderLine_custom($row);
                $totalAmount += $row['amount'];
            }

            $total['journalID'] = 'Total';
            $total['width']['journalID'] = 300;
            $total['amount'] = $totalAmount;

            if ($format == FILETYPE_PDF) {
                $report->renderSubTotal($total);
            } else {
                $report->renderLine($total);
            }

            $report->clean();
            $report->close();
            $view->items['downloadPath'] = $downloadPath;

        } else { // set print to screen variables
            $tableHeaders = [];

            $tableHeaders[] = 'Journal number';
            $tableHeaders[] = 'Company code';
            $tableHeaders[] = 'Company name';
            $tableHeaders[] = 'Account code';
            $tableHeaders[] = 'Account name';
            $tableHeaders[] = 'Description';
            $tableHeaders[] = 'Transaction Date';
            $tableHeaders[] = 'From Date';
            $tableHeaders[] = 'To Date';
            $tableHeaders[] = 'Amount';

            $view->items['tableHeaders'] = $tableHeaders;
            $view->items['result'] = $result;
        }

    }

    $view->render();
}

<?php

// include_once 'data/client/dbInterface.php';
function tenancySchedule(&$context)
{
    global $stateList;

    if (! isPostback()) {
        $view = new MasterPage(userViews(), '/managementReports/tenancySchedule.html');
        $view->setSection($context['module']);
    } else {
        $view = new UserControl(userViews(), '/managementReports/tenancySchedule.html');
    }

    $view->bindAttributesFrom($_REQUEST);

    switch ($view->items['action']) {
        case 'finalise':
            $validationErrors =  [];
            if (! isValid($view->items['reportID'], TEXT_INT, false)) {
                $validationErrors[] = 'You have not selected a report';
            }
            switch ($view->items['method']) {
                case 'businessUnit':
                    if (! isValid($view->items['division'], TEXT_LOOSE, false)) {
                        $validationErrors[] = 'You have not selected a business unit';
                    }
                    break;
                case 'state':
                    if (! isValid($view->items['state'], TEXT_LOOSE, false)) {
                        $validationErrors[] = 'You have not selected a state';
                    }
                    break;
            }

            if (noErrors($validationErrors)) {
                $context = $view->items;
                executeCommand('tenancyScheduleProcess', 'managementReports');
            }
            break;
    }

    $view->items['propertyManagerList'] = dbGetPropertyManagers();
    $view->items['property'] = deserializeParameters($view->items['property']);
    $view->items['propertyList'] = dbGetPropertyByCriteria(null, null, $view->items['propertyManager']);

    $reports = dbGetReports(TASKTYPE_TENANCY_SCHEDULE);
    $reportList = mapParameters($reports, 'reportID', 'reportDescription');
    $view->items['reportList'] = $reportList;
    if (! isset($view->items['reportID'])) {
        $view->items['reportID'] = $reports[0]['reportID'];
    }

    if (! isset($view->items['toDate'])) {
        $view->items['toDate'] = TODAY;
    }
    if (! isset($view->items['method'])) {
        $view->items['method'] = 'property';
    }

    $view->items['divisionList'] = mapParameters(dbGetParams('DIVISION'));
    $view->items['stateList'] = $stateList;

    /*$view->items['methodList'] = array (
        'propertyManager' => 'Property Manager',
        'property' => 'Property',
        'businessUnit' => 'Business Unit',
        'state' => 'State');*/
    $view->items['methodList'] =  [
        'propertyManager' => ucwords(strtolower($_SESSION['country_default']['property_manager'])),
        'property' => 'Property'];
    $view->items['formatList'] =
    [
        FILETYPE_PDF => 'PDF',
        FILETYPE_XLS => 'Excel Spreadsheet',
        FILETYPE_SCREEN => 'Print to Screen',
    ];
    if (! isset($view->items['format'])) {
        $view->items['format'] = FILETYPE_PDF;
    }

    if ($view->items['format'] != FILETYPE_SCREEN || empty($view->items['action']) || $validationErrors) {
        $view->items['validationErrors'] = $validationErrors;
        $view->render();
    }
}

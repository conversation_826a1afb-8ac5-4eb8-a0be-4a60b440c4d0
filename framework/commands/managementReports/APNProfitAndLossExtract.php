<?php

function getDataForProfitAndLoss($property, $period, $year)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $params = [];

    $sql = '
            SELECT 
                propID as bldgID, 
                propName as buildingName, 
                pmoc_o_chart.pmoc_owner_acc as clientAccountCode, 
                pmoc_o_chart.pmoc_desc as clientAccountName, 
                CONCAT(' . addSQLParam($params, $year) . ',REPLACE(STR(' . addSQLParam($params, $period) . ",2),' ','0')) as period,
                property_accounts.accountID, 
                property_accounts.accountName  as acctName,
                actual,
                actualYTD,
                actualBudget.budgetAccruals as budget,
                budgetYTD.budgetAccrualsYTD as budgetYTD,
                budgetAnnual.budgetAnnual as budgetAnnual
            FROM 
                (SELECT prop.pmpr_prop as propID, prop.pmpr_name as propName, accountID, accountName, prop.pmpr_owner as ownerID
                    FROM 
                    (SELECT
                            a_.pmca_code AS accountID,
                            a_.pmca_name AS accountName,
                            a_.pmca_gl_account_group1,
                            a_.pmca_gl_account_group2
                        FROM pmca_chart a_
                        WHERE a_.pmca_code NOT IN ('10001','10002','10003','10004','10000','10005','10006','10007','10008','10009','10010')
                        ) as income_accounts
                    CROSS JOIN pmpr_property prop
                    WHERE prop.pmpr_prop = " . addSQLParam($params, $property) . '
                ) as property_accounts
            LEFT JOIN pmoc_o_chart ON pmoc_o_chart.pmoc_owner = property_accounts.ownerID AND pmoc_o_chart.pmoc_acc = property_accounts.accountID
            LEFT JOIN 
                (SELECT propertyID as glPropertyID, accountID as glAccountID, balanceAccruals as actual
                    FROM gl_trial_balance
                    WHERE propertyID = ' . addSQLParam($params, $property) . '
                    AND period = ' . addSQLParam($params, $period) . '
                    AND year = ' . addSQLParam($params, $year) . '
                    ) as a 
                ON a.glPropertyID = property_accounts.propID
                AND a.glAccountID = property_accounts.accountID
            LEFT JOIN 
                (SELECT propertyID as glPropertyID, accountID as glAccountID, COALESCE(SUM(balanceAccruals), 0) as actualYTD
                    FROM gl_trial_balance
                    WHERE propertyID = ' . addSQLParam($params, $property) . '
                    AND period <= ' . addSQLParam($params, $period) . '
                    AND year = ' . addSQLParam($params, $year) . '
                    GROUP BY gl_trial_balance.propertyID, gl_trial_balance.accountID
                    ) as a_ytd 
                ON a_ytd.glAccountID = property_accounts.accountID
                AND a_ytd.glPropertyID = property_accounts.propID
            LEFT JOIN
                (SELECT
                    pmep_prop as propertyID,
                    pmep_exp_acc as accountID,
                    pmep_b_a_amt as budgetAccruals 
                    FROM pmep_b_exp_per
                    WHERE
                    pmep_prop = ' . addSQLParam($params, $property) . '
                    AND pmep_per = ' . addSQLParam($params, $period) . '
                    AND pmep_year = ' . addSQLParam($params, $year) . '
                    UNION
                    SELECT
                    pmrp_prop as propertyID,
                    pmrp_acc as accountID,
                    pmrp_b_a_amt as budgetAccruals 
                    FROM pmrp_b_rev_per
                    WHERE
                    pmrp_prop = ' . addSQLParam($params, $property) . '
                    AND pmrp_per = ' . addSQLParam($params, $period) . '
                    AND pmrp_year = ' . addSQLParam($params, $year) . ') as actualBudget
                ON actualBudget.propertyID = property_accounts.propID
                AND actualBudget.accountID = property_accounts.accountID
            LEFT JOIN 
                (SELECT
                    pmep_prop as propertyID,
                    pmep_exp_acc as accountID,
                    COALESCE(SUM(pmep_b_a_amt), 0) as budgetAccrualsYTD 
                FROM pmep_b_exp_per
                WHERE
                    pmep_prop = ' . addSQLParam($params, $property) . '
                    AND pmep_per <= ' . addSQLParam($params, $period) . '
                    AND pmep_year = ' . addSQLParam($params, $year) . '
                GROUP BY pmep_b_exp_per.pmep_prop, pmep_b_exp_per.pmep_exp_acc
                UNION
                SELECT
                    pmrp_prop as propertyID,
                    pmrp_acc as accountID,
                    COALESCE(SUM(pmrp_b_a_amt), 0) as budgetAccrualsYTD
                FROM pmrp_b_rev_per
                WHERE
                    pmrp_prop = ' . addSQLParam($params, $property) . '
                    AND pmrp_per <= ' . addSQLParam($params, $period) . '
                    AND pmrp_year = ' . addSQLParam($params, $year) . '
                GROUP BY pmrp_b_rev_per.pmrp_prop, pmrp_b_rev_per.pmrp_acc) as budgetYTD
                ON budgetYTD.propertyID = property_accounts.propID
                AND budgetYTD.accountID = property_accounts.accountID
            LEFT JOIN 
                (SELECT
                    pmep_prop as propertyID,
                    pmep_exp_acc as accountID,
                    COALESCE(SUM(pmep_b_a_amt), 0) as budgetAnnual
                FROM pmep_b_exp_per
                WHERE
                    pmep_prop = ' . addSQLParam($params, $property) . '
                    AND pmep_year = ' . addSQLParam($params, $year) . '
                GROUP BY pmep_b_exp_per.pmep_prop, pmep_b_exp_per.pmep_exp_acc
                UNION
                SELECT
                    pmrp_prop as propertyID,
                    pmrp_acc as accountID,
                    COALESCE(SUM(pmrp_b_a_amt), 0) as budgetAnnual
                FROM pmrp_b_rev_per
                WHERE
                    pmrp_prop = ' . addSQLParam($params, $property) . '
                    AND pmrp_year = ' . addSQLParam($params, $year) . '
                GROUP BY pmrp_b_rev_per.pmrp_prop, pmrp_b_rev_per.pmrp_acc) as budgetAnnual
                ON budgetAnnual.propertyID = property_accounts.propID
                AND budgetAnnual.accountID = property_accounts.accountID
            WHERE actual != 0 OR actualYTD != 0 OR actualBudget.budgetAccruals != 0 OR budgetYTD.budgetAccrualsYTD != 0 OR budgetAnnual.budgetAnnual != 0
            ORDER BY propID, pmoc_o_chart.pmoc_owner_acc
            ';

    return $dbh->executeSet($sql, false, true, $params);
}

// ======================================================================================================

global $clientDB, $clientDirectory, $dbh, $pathPrefix, $sess;

if ($view->items['property']) {
    $properties = deserializeParameters($view->items['property']);
} else {
    $properties = [$propertyID];
}

$period = $view->items['period'];
$year = $view->items['year'];
$prefix = dbGetParam('OACCOUNTPR', 'PREFIX'); // "AN";


$data = [];

foreach ($properties as $property) {
    // retrieve data from GL trial_balance and budget tables
    $ar_transactions = getDataForProfitAndLoss($property, $period, $year);
    foreach ($ar_transactions as $tran) {
        $variance = variance($tran['actual'], $tran['budget']);
        $variancePercent = varianceDecimal($variance, $tran['budget']);

        $varianceYTD = variance($tran['actualYTD'], $tran['budgetYTD']);
        $variancePercentYTD = varianceDecimal($varianceYTD, $tran['budgetYTD']);

        $data[] = [
            'bldgID' => $tran['bldgID'],
            'buildingName' => $tran['buildingName'],
            'ownerCode' => $prefix . $tran['clientAccountCode'], // owner code
            'ownerName' => $tran['clientAccountName'], // owner description
            'accountID' => $tran['accountID'],
            'acctName' => $tran['acctName'],
            'period' => $tran['period'],
            'actual' => $tran['actual'] + 0,
            'budget' => $tran['budget'] + 0,
            'variance' => $variance,
            'variancePercent' => $variancePercent,
            'actualYTD' => $tran['actualYTD'] + 0,
            'budgetYTD' => $tran['budgetYTD'] + 0,
            'YTDVariance' => $varianceYTD,
            'YTDVariancePercent' => $variancePercentYTD,
            'annual' => $tran['budgetAnnual'] + 0,
        ];
    }
}

// ----------------------------------------------------------

// DOC_FLATFILES

$title = $s['subReportName'];
$filename = str_replace(' ', '_', $title) . '_' . date('dmYHis') . '.csv';
$_filePath = "{$pathPrefix}{$clientDirectory}/csv/" . DOC_FLATFILES . '/';
checkDirPath($_filePath);
$_downloadPath = "{$clientDirectory}/csv/" . DOC_FLATFILES;
$downloadLink = $_downloadPath . '/' . $filename; // for download link (used to display dl link on page)
$filePath = $_filePath . $filename; // for attachment (can used for email attachment also)

$fp = fopen($filePath, 'w');

$header = [
    'PROPERTY ID',
    'PROPERTY NAME',
    strtoupper($_SESSION['country_default']['trust_account']) . ' CODE',
    strtoupper($_SESSION['country_default']['trust_account']) . ' NAME',
    'ACCOUNT CODE',
    'ACCOUNT NAME',
    'PERIOD',
    'ACTUAL',
    'BUDGET',
    'VARIANCE',
    '% VARIANCE',
    'ACTUAL YTD',
    'BUDGET YTD',
    'YTD VARIANCE',
    'YTD % VARIANCE',
    'ANNUAL BUDGET AMOUNT',
];

fwrite($fp, implode(',', $header) . PHP_EOL);
foreach ($data as $fields) {
    //    fwrite($fp, implode(',',$fields) . PHP_EOL);
    fputcsv($fp, $fields);
}
fclose($fp);

// for csvReportProcess
$attachments[] = [
    'title' => $title,
    'downloadPath' => $downloadLink,
    'filepath' => $filePath,
    'content_type' => 'text/csv',
];

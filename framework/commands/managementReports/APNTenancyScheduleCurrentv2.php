<?php

function getAPNTenancyScheduleCurrentv2($properties, $period, $year)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);

    $period_date = dbGetPeriodDate($properties[0], $period, $year);
    $last_period_date = $period_date['endDate'];
    if (is_array($properties) and count($properties ?? []) > 0) {
        $properties = implode("','", $properties);
    }
    $carBaysLicAccount = dbGetParam('CARBAYACC ', 'LICENSED');
    $carBaysCasAccount = dbGetParam('CARBAYACC ', 'CASUAL');
    $params = [];
    $sql = "SET ANSI_NULLS, QUOTED_IDENTIFIER, CONCAT_NULL_YIELDS_NULL, ANSI_WARNINGS, ANSI_PADDING ON; 
            SELECT d.pmpr_prop as propertyCode, d.pmpr_name as propertyName, d.pmpr_street AS propertyAddress, d.pmpr_city AS propertyCity, 
                    d.pmpr_state AS propertyState, d.pmpr_postcode AS propertyPostCode, portmgr.pmzz_desc as propertyManagerName,
                    unitArea.totalUnitArea as bldgArea, (d.pmpr_p_lease + d.pmpr_p_lic + d.pmpr_p_cas) as bldgCarParks, e.pmle_lease as leaseCode, 
                    e.pmle_debtor as tenantCode, e.pmle_name as leaseName,
                     
                    leaseTypes.pmzz_desc as leaseType,
                    tenantTypes.pmzz_desc as tenantType,
                    divisions.pmzz_desc as division,
                    retailCategories.categoryName as retailCategory,
                    retailSubCategories.subCategoryName as retailSubCategory,
                    retailFineCategories.fineCategoryName as retailFineCategory, 
                    
                    f.pmpu_unit as unitCode, f.pmpu_desc as unitDesc, g.pmua_area as unitArea, 
                    e.pmle_com_dt as leaseCommence, e.pmle_exp_dt as leaseExpiry, e.pmle_com_dt as rentCommence,  b.pmlc_chg_type as chargeType, 
                    CONCAT(
                        CASE WHEN e.pmle_t_year > 0
                        THEN CONCAT(e.pmle_t_year, 'y ')
                        ELSE ''
                        END,
                        CASE WHEN e.pmle_t_mth > 0
                        THEN CONCAT(e.pmle_t_mth, 'm ')
                        ELSE ''
                        END,
                        CASE WHEN e.pmle_t_day > 0
                        THEN CONCAT(e.pmle_t_day, 'd ')
                        ELSE ''
                        END) as leaseTerm, e.pmle_option as leaseOptions, e.pmle_status as leaseStatus, b.pmlc_acc as inccat, carBays.carBaysLic as carBaysLic, carBays.carBaysCas as carBaysCas, 
                    propertyTypes.pmzz_code as propertyType, propertyTypes.pmzz_desc as propertyDesc, leaseNote = 
                                STUFF((
                                  SELECT '\n' + notes.pmln_note
                                  FROM pmln_l_note notes
                                  WHERE notes.pmln_prop = d.pmpr_prop
                                    AND notes.pmln_lease = e.pmle_lease
                                    ORDER BY notes.pmln_seq
                                  FOR XML PATH(''), TYPE).value('.', 'NVARCHAR(MAX)'), 1, 1, '')
                    , c.pmla_amt as chargeAmount, c.pmla_start_dt as chargeDate, a.pmrr_rev_dt as reviewDate, tenant.pmco_name as tenantName
            FROM pmpr_property d
            JOIN pmpu_p_unit f 
                ON d.pmpr_prop = f.pmpu_prop
                AND f.pmpu_tenancy = 0
            JOIN pmua_unit_area g ON (g.pmua_prop=f.pmpu_prop 
                AND g.pmua_unit=f.pmpu_unit
                AND CONVERT(datetime, " . addSQLParam($params, $last_period_date) . ', 103) BETWEEN pmua_from_dt AND pmua_to_dt)
            LEFT JOIN pmle_lease e ON e.pmle_prop = d.pmpr_prop
                AND e.pmle_lease = g.pmua_lease
            LEFT JOIN pmlc_l_charge b
                ON b.pmlc_prop = d.pmpr_prop
                AND b.pmlc_lease = e.pmle_lease
                AND b.pmlc_start_dt <= CONVERT(datetime,  ' . addSQLParam($params, $last_period_date) . ', 103)
                AND b.pmlc_stop_dt >= CONVERT(datetime,  ' . addSQLParam($params, $last_period_date) . ', 103)
                AND b.pmlc_unit = f.pmpu_unit
            LEFT JOIN pmla_l_c_amt c
                ON c.pmla_serial = b.pmlc_serial
                AND c.pmla_prop = d.pmpr_prop
                AND c.pmla_lease = e.pmle_lease
                AND c.pmla_unit = f.pmpu_unit
                AND c.pmla_start_dt <= CONVERT(datetime,  ' . addSQLParam($params, $last_period_date) . ', 103)
                AND c.pmla_end_dt >= CONVERT(datetime,  ' . addSQLParam($params, $last_period_date) . ", 103)
            LEFT JOIN (SELECT * FROM pmzz_param
                    WHERE pmzz_par_type = 'PROPERTY') as propertyTypes
                ON propertyTypes.pmzz_code = d.pmpr_prop_type
            LEFT JOIN (SELECT * FROM pmzz_param
                    WHERE pmzz_par_type = 'PORTMGR') as portmgr 
                ON portmgr.pmzz_code = d.pmpr_portfolio
            LEFT JOIN (SELECT
                        pmua_prop propertyCode,
                        COUNT(pmpu_unit) totalUnit,
                        COALESCE(SUM(pmua_area), 0) totalUnitArea
                    FROM 
                        pmpu_p_unit 
                    JOIN 
                        pmua_unit_area ON (pmua_prop=pmpu_prop AND pmua_unit=pmpu_unit)
                    WHERE pmua_status IN ('O','V')
                        AND CONVERT(datetime, CONVERT(varchar, GETDATE(), 103), 103) BETWEEN pmua_from_dt AND pmua_to_dt
                        AND pmpu_prop IN ('" . $properties . "')
                    GROUP BY
                        pmua_prop) as unitArea
                ON unitArea.propertyCode = d.pmpr_prop
            LEFT JOIN (SELECT pmlc.pmlc_prop as propID, pmlc.pmlc_lease as leaseID, pmlc_unit as unitID, SUM(CASE WHEN pmlc_acc = '$carBaysLicAccount' THEN pmla_parking ELSE 0 END) as carBaysLic,
                    SUM(CASE WHEN pmlc_acc = '$carBaysCasAccount' THEN pmla_parking ELSE 0 END) as carBaysCas FROM pmlc_l_charge pmlc
                    JOIN  pmla_l_c_amt  pmla ON pmla.pmla_prop = pmlc.pmlc_prop
                    AND pmla.pmla_lease = pmlc.pmlc_lease
                    AND pmla.pmla_unit = pmlc.pmlc_unit
                    AND pmla.pmla_serial = pmlc.pmlc_serial
					WHERE pmlc.pmlc_start_dt <= CONVERT(datetime,  " . addSQLParam($params, $last_period_date) . ', 103)
					AND pmlc.pmlc_stop_dt >= CONVERT(datetime,  ' . addSQLParam($params, $last_period_date) . ', 103)
					AND pmla.pmla_start_dt <= CONVERT(datetime,  ' . addSQLParam($params, $last_period_date) . ', 103)
					AND pmla.pmla_end_dt >= CONVERT(datetime,  ' . addSQLParam($params, $last_period_date) . ", 103)
					AND pmlc.pmlc_prop IN ('" . $properties . "')
                    GROUP BY pmlc.pmlc_prop, pmlc.pmlc_lease, pmlc_unit
                    HAVING COALESCE(SUM(pmla_parking), 0) != 0) as carBays
                ON carBays.propID = d.pmpr_prop
                AND carBays.leaseID = e.pmle_lease
                AND carBays.unitID = f.pmpu_unit
            LEFT JOIN (SELECT * FROM 
                    (select pmrr_prop, pmrr_lease, pmrr_rev_dt,
                        ROW_NUMBER() OVER (PARTITION BY pmrr_lease, pmrr_prop ORDER BY pmrr_rev_dt) AS rn
                     from pmrr_l_rent_rev
                    WHERE pmrr_prop IN ('" . $properties . "')
                    AND pmrr_closed = 0
                    ) as pmrr_l_rent_rev
                    WHERE rn = 1) a
                ON a.pmrr_prop = d.pmpr_prop
                AND a.pmrr_lease = e.pmle_lease
            LEFT JOIN pmco_company tenant
                ON tenant.pmco_code = e.pmle_debtor
                
            LEFT JOIN (SELECT * FROM pmzz_param
				WHERE pmzz_par_type = 'TENANT') as tenantTypes
			ON tenantTypes.pmzz_code = e.pmle_ten_type
            LEFT JOIN (SELECT * FROM pmzz_param
                    WHERE pmzz_par_type = 'DIVISION') as divisions
                ON divisions.pmzz_code = e.pmle_division
            LEFT JOIN (SELECT * FROM pmzz_param
                    WHERE pmzz_par_type = 'LEASE') as leaseTypes
                ON leaseTypes.pmzz_code = e.pmle_l_type
            LEFT JOIN npms.dbo.retail_category as retailCategories
                ON retailCategories.categoryID = pmle_retail_cat
            LEFT JOIN npms.dbo.retail_sub_category as retailSubCategories
                ON retailSubCategories.categoryID = pmle_retail_cat
                AND retailSubCategories.subCategoryID = pmle_retail_sub_cat
            LEFT JOIN npms.dbo.retail_fine_category as retailFineCategories
                ON retailFineCategories.subCategoryID = pmle_retail_sub_cat
                AND retailFineCategories.fineCategoryID = pmle_retail_fine_cat
			    
            WHERE pmpr_prop IN ('" . $properties . "')
            ORDER BY d.pmpr_prop, f.pmpu_unit, e.pmle_lease, b.pmlc_acc;
    
            SET ANSI_WARNINGS OFF;";
    //    pre_print_r($sql);

    return $dbh->executeSet($sql, false, true, $params);
}

// ======================================================================================================

global $clientDB, $clientDirectory, $dbh, $pathPrefix, $sess;

if ($view->items['property']) {
    $properties = deserializeParameters($view->items['property']);
} else {
    $properties = [$propertyID];
}

$period = $view->items['period'];
$year = $view->items['year'];


$data = [];

// get ar_transactions and budget
$tenancyScheds = getAPNTenancyScheduleCurrentv2($properties, $period, $year);


$tsCols = dbGetParams('TSCOL', true, true, ' ORDER BY CAST(pmzz_desc as INT)');

$colCount = 29;
$index = 0;

$numberFormat = '_(* #,##0.00_);_(* (#,##0.00);_(* "-"??_);_(@_)';
$numberFormatNoDecimal = '#,##0';
$numberFormatCols = [
    //    'BASE RENT',
    'BUILDING AREA',
    'AREA NLA',
];
$numberFormatNoDecimalCols = [
    'BUILDING CAR PARKS',
    'CAR BAYS LICENSED',
    'CAR BAYS CASUAL',
];

$prevPropUnitLeaseID = '';
foreach ($tenancyScheds as $tran) {
    $index = $tran['propertyCode'] . '~' . $tran['unitCode'] . '~' . $tran['leaseCode'];

    if ($prevPropUnitLeaseID != $tran['propertyCode'] . '~' . $tran['unitCode'] . '~' . $tran['leaseCode']) {
        $data[$index] = [
            'PROPERTY ID' => $tran['propertyCode'],
            //        'CLIENT CODE' => $tran['propertyCode'],
            'PROPERTY NAME' => $tran['propertyName'],
            'ADDRESS' => $tran['propertyAddress'],
            'CITY' => $tran['propertyCity'],
            'STATE' => $tran['propertyState'],
            'POSTCODE' => $tran['propertyPostCode'],
            'MGR NAME' => $tran['propertyManagerName'],
            'BUILDING AREA' => $tran['bldgArea'],
            'BUILDING CAR PARKS' => $tran['bldgCarParks'],
            'LEASE ID' => $tran['leaseCode'] ? $tran['propertyCode'] . '-' . $tran['leaseCode'] : '',
            'TENANT CODE' => $tran['tenantCode'],
            'TENANT NAME' => $tran['tenantName'],

            'LEASE TYPE' => $tran['leaseType'],
            'TENANT TYPE' => $tran['tenantType'],
            'DIVISION' => $tran['division'],
            'RETAIL CATEGORY' => $tran['retailCategory'],
            'RETAIL SUBCATEGORY' => $tran['retailSubCategory'],
            'RETAIL FINE CATEGORY' => $tran['retailFineCategory'],

            'AREA CODE' => $tran['unitCode'],
            'AREA DESCRIPTION' => $tran['unitDesc'],
            'AREA NLA' => $tran['unitArea'],
            // 'AREA TYPE' => '',
            'LEASE COMMENCE' => $tran['leaseCommence'],
            'LEASE EXPIRY' => $tran['leaseExpiry'],
            'RENT COMMENCEMENT' => $tran['rentCommence'],
            //            'BASE RENT' => $tran['baseRent'],
            'LEASE TERM' => $tran['leaseTerm'],
            'LEASE OPTIONS' => $tran['leaseOptions'],
            'OCCUPANCY STATUS' => $tran['leaseStatus'],
            'REVIEW DATE' => $tran['reviewDate'],
            // 'ACCOUNT CODE' => $tran['inccat'], //previously INCCAT
            // 'REVIEW TYPE' => $tran['reviewType'],
            // 'REVIEW DESC' => $tran['reviewDesc'],
            // 'LEASE ID' => $tran['leaseCode'],
            'CAR BAYS LICENSED' => $tran['carBaysLic'] + 0,
            'CAR BAYS CASUAL' => $tran['carBaysCas'] + 0,
            // 'CLASS ID' => '',
            'PROPERTY TYPE' => $tran['propertyType'],
            'PROPERTY TYPE DESC' => $tran['propertyDesc'],
            'TS NOTE' => $tran['leaseNote'],
        ];

        // assign columns per on account on pmzz par type TSCOL
        foreach ($tsCols as $key => $tSCol) {
            $data[$index][$tSCol . '_charge'] = '';
            $data[$index][$tSCol . '_date'] = '';
            $data[$index][$tSCol . '_amount'] = '';
        }

        $colNo = array_search($tran['inccat'], $tsCols);
        $colNo = $tsCols[$tran['inccat']];

        if ($colNo > 0) {
            $data[$index][$colNo . '_charge'] = $tran['inccat'];
            $data[$index][$colNo . '_date'] = $tran['chargeDate'];
            $data[$index][$colNo . '_amount'] += $tran['chargeAmount'];
        }
    } else {
        $colNo = array_search($tran['inccat'], $tsCols);
        $colNo = $tsCols[$tran['inccat']];

        if ($colNo > 0) {
            $data[$index][$colNo . '_charge'] = $tran['inccat'];
            $data[$index][$colNo . '_date'] = $tran['chargeDate'];
            $data[$index][$colNo . '_amount'] += $tran['chargeAmount'];
        }
    }

    $prevPropUnitLeaseID = $index;
}

// ----------------------------------------------------------

// pre_print_r($data);

$title = $s['subReportName'];
$filename = str_replace(' ', '_', $title) . '_' . date('dmYHis') . '.xlsx';
$_filePath = "{$pathPrefix}{$clientDirectory}/xlsx/" . DOC_FLATFILES . '/';
checkDirPath($_filePath);
$_downloadPath = "{$clientDirectory}/xlsx/" . DOC_FLATFILES;
$downloadLink = $_downloadPath . '/' . $filename; // for download link (used to display dl link on page)
$filePath = $_filePath . $filename; // for attachment (can used for email attachment also)

$xls = new XLSDataReport($filePath, $title);
$xls->enableFormatting = true;
$data = array_values($data);

if (! empty($data)) {
    foreach (array_keys($data[0]) as $col) {
        if (strpos($col, '_charge') === false and strpos($col, '_amount') === false) {
            if (in_array($col, $numberFormatCols)) {
                $xls->addColumn($col, $col, 50, null, $numberFormat);
            } else {
                if (in_array($col, $numberFormatNoDecimalCols)) {
                    $xls->addColumn($col, $col, 50, null, $numberFormatNoDecimal);
                } else {
                    if ($col == 'TS_ NOTE') {
                        $xls->addColumn($col, $col, 50, 'wrap');
                    } else {
                        $xls->addColumn($col, $col, 50, null, '@');
                    }
                }
            }
        } else {
            if (strpos($col, '_charge') !== false) {
                $xls->addColumn($col, 'CHARGE', 50, null, '@');
            } else {
                if (strpos($col, '_date') !== false) {
                    $xls->addColumn($col, 'DATE', 50, null);
                } else {
                    if (strpos($col, '_amount') !== false) {
                        $xls->addColumn($col, 'AMOUNT', 50, null, $numberFormat);
                    }
                }
            }
        }
    }

    $xls->preparePage();
    $xls->renderHeader();
    $xls->renderData($data);
    $xls->close();

    $attachments[] = [
        'title' => $title,
        'downloadPath' => $downloadLink,
        'filepath' => $filePath,
        'content_type' => 'application/xlsx',
    ];
}

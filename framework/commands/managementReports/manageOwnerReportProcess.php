<?php

set_time_limit(0);
ini_set('MAX_EXECUTION_TIME', '-1');

if (! function_exists('closePDF')) {
    function closePDF()
    {
        global $pdf;
        $pdf->end_document('');
        $pdf->delete();
    }
}

function manageOwnerReportProcess(&$context)
{
    global $dbh, $sess;
    $jasperInclude = [];
    $zipCounter = 0;

    include_once 'lib/reportLib/reportincludesAll.php';
    include_once 'lib/ReportLib/commonReportFunctions.php';
    include_once 'functions/ownerReportProcessFunctions.php';
    include_once 'functions/page1PropertyReportFunctions.php';
    include_once 'functions/page2DebtorsDetailFunctions.php';
    include_once 'functions/pdfHeaders.php';


    $view = new UserControl(userViews(), '', '');

    global $periodFrom;
    global $periodTo;
    global $reportingPeriodFrom;
    global $reportingPeriodTo;
    global $periodDescription;
    global $reportDescription;
    global $reportingTotal;
    global $reportingCurrPage;
    global $reportingTotalPages;
    global $filenameDescription;
    global $propertyName;
    global $client;
    global $client_street;
    global $client_city;
    global $client_state;
    global $client_postcode;
    global $line;
    global $startline;
    global $date;
    global $page;
    global $pdf;
    global $_fonts;
    global $propertyID;
    global $propertyGroup;
    global $clientDirectory;
    global $pathPrefix;


    try {
        $view->bindAttributesFrom($_REQUEST);

        // ## NOTE: Uncomment this section only for testing purposes - START
        //         $view->items['reporttype'] = "1";  //Report ID which sub report belongs to
        //        $view->items['description'] = "OWNERS STATEMENT";
        //        $view->items['reportPeriod'] = "January 2021";
        //        $view->items['filename_description'] = "Owner_reports_";
        //        $view->items['logo'] = 1;
        //        $view->items['sub_report'] = "1"; //sub report ID
        //        $view->items['property'] = "CHUR0001"; //property ID
        //        $view->items['periodFrom'] = "01/07/2021";
        //        $view->items['periodTo'] = "31/07/2021";
        // THIS PART WILL BE DELETED ONCE COMMITED

        $propertyCount = 0;
        if ($view->items['property']) {
            $propertyCount = count(deserializeParameters($view->items['property']));
        }

        if ($propertyCount) {
            if (isset($view->items['reporttype'])) {
                $reportType = $view->items['reporttype'];
            }

            $report = dbGetReport_management($reportType);

            $title = $report['description'];
            $orientation = 2; // $report['reportOrientation']; update this if needed
            $_page = getPageSize($orientation);


            $date = TODAY;
            $logo = $view->items['logo'];
            $logoFlag = $logo;
            logData($view->items['logo'] . '<<');
            $periodFrom = $view->items['periodFrom'];
            $periodTo = $view->items['periodTo'];

            $reportingPeriodFrom = $view->items['reportingPeriodFrom'];
            $reportingPeriodTo = $view->items['reportingPeriodTo'];


            $logoFile = dbGetClientLogo();
            $logoPath = "assets/clientLogos/{$logoFile}";

            logData($logoPath);

            $properties = deserializeParameters($view->items['property']);

            $periodDescription = date('M Y', strtotime(convertDate($view->items['periodTo'])));
            $reportDescription = $view->items['description'];
            $filenameDescription = $view->items['filename_description'];
            $PDFtitle = 'Owners Reports';
            $filename_description = preg_replace("/([^\w\d])/", '_', $view->items['filename_description']);
            if (strlen($filename_description) > 0) {
                $fileName = $filename_description . '_';
            } else {
                $fileName = 'OwnersReports_';
            }

            $fileName .= $properties[0] . '_';


            $_filePath = "{$pathPrefix}{$clientDirectory}/pdf/" . DOC_OWNERSTATEMENT . '/';
            $_downloadPath = "{$clientDirectory}/pdf/" . DOC_OWNERSTATEMENT;
            $_resourcePath = "{$pathPrefix}{$clientDirectory}/pdf/" . DOC_OWNERSTATEMENT . '/_resources';

            $pdfDownloadLink = initializePDF($PDFtitle, $fileName, $_filePath, $_downloadPath, '', false);


            $vars = get_defined_vars();
            logData(print_array($vars, true));

            define('A', 1);
            define('C', 2);

            $reportingTotal = 0;
            $reportingCurrPage = 1;
            $reportingTotalPages = count($properties ?? []);
            //
            foreach ($properties as $ownerReportIndex => $propertyID) {
                $page = 0;

                $propertyName = getPropName($propertyID);

                // Residential Simple Report
                //		                if ($report['reportPeriod'] == 'D')
                //		                {
                //
                //		                    $periodFrom = $view->items['reportingPeriodFrom'];
                //		                    $periodTo = $view->items['reportingPeriodTo'];
                //
                //
                //		                    $previousPeriodTo = oneDayBefore($periodFrom);
                //		                    $periodFrom_display = toDisplayDate($periodFrom);
                //		                    $periodTo_display = toDisplayDate($periodTo);
                //		                    $startFinancialYear = $periodFrom;
                //		                    $endFinancialYear = $periodTo;
                //		                    $reportingPeriodToPY = oneDayBefore($periodFrom);
                //		                    $financialPeriodToPY = oneDayBefore($periodFrom);
                //
                //		                    $calendar = dbGetPeriod($propertyID, $periodFrom);
                //		                    $calendar['startDate'] = $periodFrom;
                //		                    $calendar['endDate'] = $periodTo;
                //
                //		                }
                //		                else {
                $previousPeriodTo = oneDayBefore($periodFrom);
                $periodFrom_display = toDisplayDate($periodFrom);
                $periodTo_display = toDisplayDate($periodTo);
                $calendar = dbGetPeriod($propertyID, $periodFrom);


                $currentPeriod = $calendar['period'];

                $calendarTo = dbGetPeriod($propertyID, $periodTo); // added by Raymond 28/1/11

                $toPeriod = $calendarTo['period']; // added by Raymond 28/1/11
                $currentYear = $calendar['year'];
                [$previousPeriod, $previousYear] = periodBefore($currentPeriod, $currentYear);

                $previousFinancialYear = $currentYear - 1;
                $financialYearPeriod = dbGetPeriodDate($propertyID, 1, $currentYear);

                $startFinancialYear = $financialYearPeriod['startDate'];
                $endFinancialYear = $financialYearPeriod['endDate'];

                $dateParts = explode('/', $endFinancialYear);
                $startReportingYear = "01/{$dateParts[DATE_MONTH]}/{$dateParts[DATE_YEAR]}";

                $reportingPeriodToPY = oneDayBefore($startReportingYear);
                $financialPeriodToPY = oneDayBefore($startFinancialYear);
                //  }
                $timestamp = time();

                // ################### GET THE OWNERS DETAILS FOR PRINTING AT THE TOP###################

                $owner = owner_details($propertyID);
                $client = $owner['pmco_name'];
                $client_street = $owner['pmco_street'];
                $client_city = $owner['pmco_city'];
                $client_state = $owner['pmco_state'];
                $client_postcode = $owner['pmco_postcode'];

                $view->items['processed'] = true;
                /**	NEW REPORT PROCESSING CODE **/
                $includes = dbGetSubReports_management($reportType);

                // -- tracc cash options
                $rentPaidTo = $view->items['rentPaidTo'];
                // -- accruals options
                $ownerAccountCodes = false;	// -- this relates to whether there are owners account codes or not
                $description = 'Description'; // -- used in trial balance excel sheet i think used for account name


                $subReports = explode(',', $view->items['sub_report']);
                $ownerName = dbGetOwnerName($propertyID);

                // -- create the header object that gets used on each page
                $header = new ReportHeader($propertyName, 'Owner: ' . $ownerName, $title . ' - ' . $periodDescription);
                $header->xPos = 20;
                $header->yPos = $_page->height - 25;

                $footer = new TraccFooter($logoPath, 'OwnerStatement', $orientation);

                foreach ($includes as $s) {
                    $sub_report_check = 0;
                    if ($view->items['is_merged'] == '1') {
                        $sub_report_check = dbCheckSubreportInclusion($propertyID, $currentPeriod, $currentYear, $reportType, $s['sub_report_id']);
                    }

                    if (($view->items['is_merged'] != '1' && in_array($s['sub_report_id'], $subReports)) || ($view->items['is_merged'] == '1' && $sub_report_check)) {
                        if ($s['sub_report_file_path']) {
                            include $s['sub_report_file_path'];
                        }
                    }
                }

                $reportingCurrPage++;
            }// END FOR EACH
            closePDF();


            // echo $pdfDownloadLink; //ORIGINAL
            echo urlencode(base64_encode('../reports/' . $pdfDownloadLink)); // Updated
            // echo urlencode(base64_encode($pdfDownloadLink)); //uncomment for encoding
        }
    } catch (PDFlibException $e) {
        $msg = 'PDFLIB' . PHP_EOL;
        $msg .= 'code : ' . $e->getCode() . PHP_EOL;
        $msg .= 'file : ' . $e->getFile() . 'line : ' . $e->getLine() . PHP_EOL;
        $msg .= 'mess : ' . $e->getMessage() . PHP_EOL;
        $msg .= 'detl : ' . $e . PHP_EOL;
        $msg .= 'PDFLIB';
        pre_print_r($msg);
        //		sendMail('<EMAIL>', 'Andrew Erkins', $msg, 'Scheduler Failed');
    } catch (Exception $e) {
        $msg = 'Exception' . PHP_EOL;
        $msg .= 'code : ' . $e->getCode() . PHP_EOL;
        $msg .= 'file : ' . $e->getFile() . 'line : ' . $e->getLine() . PHP_EOL;
        $msg .= 'mess : ' . $e->getMessage() . PHP_EOL;
        $msg .= 'detl : ' . $e . PHP_EOL;
        $msg .= 'Exception';
        sendMail('<EMAIL>', 'Andrew Erkins', $msg, 'Scheduler Failed');
    }
}

<?php

global $pathPrefix, $clientDirectory, $dbh, $clientDB;

$reportName = $s['subReportName'];
$reportAccountID = '10009';

[$day, $month, $year] = explode('/', TODAY);
$filename = $reportName . '_' . $propertyID . "_{$year}{$month}{$day}.pdf";

$subReportFile[$s['subReportID']] = "{$pathPrefix}{$clientDirectory}/pdf/OwnerStatement/{$filename}";
if (! file_exists("{$pathPrefix}{$clientDirectory}/pdf/OwnerStatement")) {
    mkdir("{$pathPrefix}{$clientDirectory}/pdf/OwnerStatement", FILE_PERMISSION, true);
}

$logoFile = dbGetClientLogo();
$logoPath = "assets/clientLogos/{$logoFile}";

$report = new PDFDataReport($subReportFile[$s['subReportID']], $logoPath, A4_LANDSCAPE);
$report->multiLine = true;
$report->printRowLines = true;
$report->printColumnLines = false;
$report->printBorders = false;
$report->cache = false;

$subText = 'Basis : Accruals';
$subText1 = 'From : ' . $periodFrom . ' To : ' . $periodTo;

$fromYearPeriod = dbGetPeriod($propertyID, $periodFrom);
$toYearPeriod = dbGetPeriod($propertyID, $periodTo);

$sqlPeriod = ' AND (CAST(gl_transaction.year as decimal) + CAST(gl_transaction.period as decimal)/100) >= ' . ($fromYearPeriod['year'] + ($fromYearPeriod['period'] / 100));
$sqlPeriod .= ' AND (CAST(gl_transaction.year as decimal) + CAST(gl_transaction.period as decimal)/100) <= ' . ($toYearPeriod['year'] + ($toYearPeriod['period'] / 100));

$header = new ReportHeader($reportName, $propertyID . ' - ' . dbGetPropertyName($propertyID), $subText, $subText1);
$header->xPos = $report->hMargin;
$header->yPos = $report->pageHeight - $report->vMargin;
$report->attachObject('header', $header);
$footer = new TraccFooter(null, $reportName, $report->pageSize);
$report->attachObject('footer', $footer);

$report->addColumn('Journal number', 'Journal number', 50, 'left', '@');
$report->addColumn('Company code', 'Company code', 50, 'left', '@');
$report->addColumn('Company name', 'Company name', 130, 'left', '@');
$report->addColumn('Account code', 'Account code', 50, 'left', '@');
$report->addColumn('Account name', 'Account name', 130, 'left', '@');
$report->addColumn('Description', 'Description', 130, 'left', '@');
$report->addColumn('Transaction Date', 'Transaction Date', 50, 'left', '@');
$report->addColumn('From Date', 'From Date', 50, 'left', '@');
$report->addColumn('To Date', 'To Date', 50, 'left', '@');
$report->addColumn('Amount', 'Amount', 75, 'right', REPORT_DECIMAL);
$report->preparePage();
$report->renderHeader();

// ------------------ get data
$dbh->selectDatabase($clientDB);

$sql = "SELECT gl_journal.journalid      AS 'Journal number',
           gl_journal_entry.companyid        AS 'Company code',
           pmco_company.pmco_name            AS 'Company name',
           gl_journal_entry.accountid        AS 'Account code',
           pmca_chart.pmca_name              AS 'Account name',
           gl_journal_entry.description      AS 'Description',
           gl_journal.transactiondate        AS 'Transaction Date',
           gl_journal_entry.fromdate         AS 'From Date',
           gl_journal_entry.todate           AS 'To Date',
           (gl_journal_entry.netamount * -1) AS 'Amount'
    FROM   gl_journal,
           gl_journal_entry,
           gl_transaction,
           pmca_chart,
           pmco_company
    WHERE  gl_journal_entry.journalid = gl_journal.journalid
    AND    gl_journal_entry.companyid = pmco_company.pmco_code
    AND    gl_journal_entry.accountid = pmca_chart.pmca_code
    AND    gl_transaction.batch_id = gl_journal.journalid
    AND    ((gl_transaction.property_id=?)
    AND    (gl_transaction.account_id=?)
    {$sqlPeriod}
    AND    (gl_journal_entry.accountid<>?)
    AND    (gl_journal.parentid IS NULL))
    ORDER BY gl_transaction.year, gl_transaction.period";

// pre_print_r($sql);
$rep_data = $dbh->executeSet($sql, false, true, [$propertyID, $reportAccountID, $reportAccountID]);


// ---------------------


$total = 0;
foreach ($rep_data as $row) {
    // $report->renderLineFill($row);
    $report->renderLine_custom($row);
    $total += $row['Amount'];
}

$subTotal['Journal number'] = 'Total';
$subTotal['Amount'] = $total;

$report->renderSubTotal($subTotal);

$report->clean();
$report->close();

if (file_exists($subReportFile[$s['subReportID']])) {
    // $pdf is the main PDF where this report will be attached.
    $pdi = new ImportedPage($pdf, $subReportFile[$s['subReportID']], A4_LANDSCAPE);
    $i = 1;
    while ($pdi->loadPage($i)) {
        $page++;
        $pdi->preparePage();
        $pdi->render();
        $pdi->endPage();
        $i++;
    }

    $pdi->close();
}

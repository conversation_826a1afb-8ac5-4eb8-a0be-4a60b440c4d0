<?php

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

$ownerincome = 0;
$voinc = 0;
$recinc = 0;
$ownerincomeA = 0;
$voincA = 0;
$recincA = 0;
$subGroupArray = [];
$subGroupChecker = [];
$excel = new Spreadsheet();

$excel->setActiveSheetIndex(0); // Activate it
$excel->getActiveSheet()->setTitle('Operating Cash Receipts Summary');

$excel->getActiveSheet()->getStyle('A1:J1')->getFont()->setBold(true);
$excel->getActiveSheet()->SetCellValue(cellReference(1, 1), 'Operating Cash Receipts Summary');

$excel->getActiveSheet()->SetCellValue(cellReference(2, 1), 'Period From:');
$excel->getActiveSheet()->SetCellValue(cellReference(2, 2), "$periodFrom To: $periodTo");

$excel->getActiveSheet()->SetCellValue(cellReference(4, 1), 'Owner:');
$excel->getActiveSheet()->SetCellValue(cellReference(4, 2), "$client");
$excel->getActiveSheet()->SetCellValue(cellReference(5, 1), 'Property:');
$excel->getActiveSheet()->SetCellValue(cellReference(5, 2), $propertyName . " [$propertyID]");
$excel->getActiveSheet()->SetCellValue(cellReference(6, 1), 'Report For:');
$excel->getActiveSheet()->SetCellValue(cellReference(6, 2), $periodDescription);

$excel->getActiveSheet()->getStyle('A9:J9')->getFont()->setBold(true);
$excel->getActiveSheet()->SetCellValue(cellReference(9, 2), 'Current Month');
$excel->getActiveSheet()->SetCellValue(cellReference(9, 6), 'Year to Date');
$excel->getActiveSheet()->SetCellValue(
    cellReference(9, 10),
    'Budget Year ' . $_SESSION['country_default']['currency_symbol']
);
$excel->getActiveSheet()->getStyle('A10:J10')->getFont()->setBold(true);
$excel->getActiveSheet()->SetCellValue(
    cellReference(10, 2),
    'Actual ' . $_SESSION['country_default']['currency_symbol']
);
$excel->getActiveSheet()->SetCellValue(
    cellReference(10, 3),
    'Budget ' . $_SESSION['country_default']['currency_symbol']
);
$excel->getActiveSheet()->SetCellValue(
    cellReference(10, 4),
    'Variance ' . $_SESSION['country_default']['currency_symbol']
);
$excel->getActiveSheet()->SetCellValue(cellReference(10, 5), 'Var %');
$excel->getActiveSheet()->SetCellValue(
    cellReference(10, 6),
    'Actual ' . $_SESSION['country_default']['currency_symbol']
);
$excel->getActiveSheet()->SetCellValue(
    cellReference(10, 7),
    'Budget ' . $_SESSION['country_default']['currency_symbol']
);
$excel->getActiveSheet()->SetCellValue(
    cellReference(10, 8),
    'Variance ' . $_SESSION['country_default']['currency_symbol']
);
$excel->getActiveSheet()->SetCellValue(cellReference(10, 9), 'Var %');

foreach (range('A', $excel->getActiveSheet()->getHighestDataColumn()) as $col) {
    $excel
        ->getActiveSheet()
        ->getColumnDimension($col)
        ->setAutoSize(true);
}

$clientDirectory = str_replace(' ', '', $_SESSION['database']);
$pathPrefix = REPORTPATH . '/';
$path = $pathPrefix;
$timestamp = date('dmYHis');
$filename_description = preg_replace("/([^\w\d])/", '_', $view->items['filename_description']);
if (strlen($filename_description) > 0) {
    $fileName = $filename_description . '_';
} else {
    $fileName = 'OwnersReports_';
}

if (! file_exists("{$path}{$clientDirectory}/xlsx/" . DOC_OWNERSTATEMENT . '/')) {
    mkdir("{$path}{$clientDirectory}/xlsx/" . DOC_OWNERSTATEMENT . '/', FILE_PERMISSION, true);
}
$xlsDownloadPath = "{$path}{$clientDirectory}/xlsx/" . DOC_OWNERSTATEMENT . '/' . $fileName . $propertyID . '_' . $timestamp . '.xlsx';


global $dbh;
$dbh->selectDatabase($clientDB);
$budgetINCVOTotal_display = $budgetINCVOYTotal_display = 0;

$excel->getActiveSheet()->getStyle('A11:J11')->getFont()->setBold(true);
$excel->getActiveSheet()->SetCellValue(cellReference(11, 1), 'Owner Receipts');

// --------------------------------------------------------------------------
// OWNER RECEIPTS
//

$budgetINCOWNTotal = 0;
$budgetINCOWNYTotal = 0;
$budgetINCOWNYEARTotal = 0;
$budgetINCRECTotal = 0;
$budgetINCRECYTotal = 0;
$budgetINCRECYEARTotal = 0;
$budgetINCVOTotal = 0;
$budgetINCVOYTotal = 0;
$budgetINCVOYEARTotal = 0;

$_totalAllIncome_display = 0;
$_totalAllIncomeA_display = 0;
$_totalAllBudgetIncome_display = 0;
$_totalAllBudgetIncomeA_display = 0;
$_totalAllBudgetIncomeYEAR_display = 0;
$_totalAllVarianceRECEIPTS_display = 0;
$_totalAllVP_display1 = 0;
$_totalAllVarianceY_display = 0;
$_totalAllVPY_display = 0;

// ACCOUNTS FOR INDIV CALCULATIONS
$accSQL = "SELECT DISTINCT pmxd_acc
		FROM pmxd_ar_alloc
		WHERE fund IS NULL AND (pmxd_prop = ?)
		AND (pmxd_alloc_dt <= CONVERT(datetime, ?, 103))
		AND (pmxd_f_type = 'CSH')
				AND (pmxd_acc IN
						  (SELECT pmcg_acc
							FROM pmcg_chart_grp
							WHERE (pmcg_grp = 'TRACC2')
							AND pmcg_subgrp = 'INCOWN'))
			 UNION ALL
			SELECT DISTINCT pmuc_acc
						FROM pmuc_unall_csh
					wHERE (pmuc_prop = ?)
			AND (pmuc_rcpt_dt <= CONVERT(datetime, ?, 103))
			AND (pmuc_acc <> '')
			AND (pmuc_acc IN(SELECT pmcg_acc
				FROM pmcg_chart_grp
				WHERE (pmcg_grp = 'TRACC2')
				AND pmcg_subgrp = 'INCOWN'))";
$acc = $dbh->executeScalars($accSQL, [$propertyID, $periodTo, $propertyID, $periodTo]);

$unAllocCash = "SELECT DISTINCT pmuc_acc
		FROM pmuc_unall_csh
		WHERE (pmuc_prop = ?)
		AND (pmuc_rcpt_dt < CONVERT(datetime, ?, 103))
		AND (pmuc_acc IN
						  (SELECT pmcg_acc
							FROM pmcg_chart_grp
							WHERE (pmcg_grp = 'TRACC2') AND pmcg_subgrp = 'INCOWN'))";
$cash = $dbh->executeScalars($unAllocCash, [$propertyID, $periodTo]);

$increcownSQL = "SELECT pmcg_acc
		FROM pmcg_chart_grp
		WHERE (pmcg_grp = 'TRACC3')
		AND (pmcg_subgrp = 'INCRECOWN')";
$increcown = $dbh->executeScalars($increcownSQL, []);

$opBAL_accSQL = "SELECT DISTINCT pmpb_acc as account
		FROM pmpb_p_bal
		WHERE (pmpb_prop = ?)
		AND (pmpb_date BETWEEN CONVERT(datetime, ?, 103) AND CONVERT(datetime, ?, 103))
		AND (pmpb_acc IN (
		                  SELECT a.pmca_code from pmca_chart a
                            JOIN pmcg_chart_grp b ON b.pmcg_acc = a.pmca_code AND pmcg_grp = 'TRACC2' AND b.pmcg_subgrp = 'INCOWN'
                            JOIN pmas_acc_subgrp c ON c.pmas_subgrp = b.pmcg_subgrp
                            AND c.pmas_class = a.pmca_type
                            AND c.pmas_grp = 'BSHEET'
						  )
			)
        AND (pmpb_acc NOT IN (SELECT pmcg_acc
                  FROM pmcg_chart_grp
                  WHERE (pmcg_grp = 'TRACC2')
                  AND (pmcg_subgrp = 'EXPOPT')))
	    AND (pmpb_acc <> '0701')
	    AND (pmpb_acc <> '0702')";
$openBAL = $dbh->executeScalars($opBAL_accSQL, [$propertyID, $startFinancialYear, $periodTo]);

// Retrieve periods
$periodsSQL = "
	SELECT
		pmcp_period, pmcp_year
	FROM
		pmcp_prop_cal
	WHERE
		pmcp_start_dt BETWEEN CONVERT(datetime, ?, 103) AND CONVERT(datetime, ?, 103)
		AND pmcp_end_dt BETWEEN CONVERT(datetime, ?, 103) AND CONVERT(datetime, ?, 103)
		AND pmcp_prop = '$propertyID'";
$periods = $dbh->executeSet($periodsSQL, false, true, [$periodFrom, $periodTo, $periodFrom, $periodTo]);
$pmcp_period = $pmcp_year = null;
foreach ($periods as $v) {
    $pmcp_period[] = $v['pmcp_period'];
    $pmcp_year[] = $v['pmcp_year'];
}
$pmcp_period = array_unique($pmcp_period);
$pmcp_year = array_unique($pmcp_year);

// //////////////////////////////////////////////////////////////
// ///////////////////This is where the ammount is set and made
// //////////////////////////////////////////////////////////////
$paramsbudgetACCSQL = [];
$budgetACCSQL = '
	SELECT DISTINCT
		pmrp_acc as account
	FROM
		pmrp_b_rev_per
	WHERE
		pmrp_prop = ' . addSQLParam($paramsbudgetACCSQL, $propertyID) . '
		AND pmrp_per IN (' . addSQLParam($paramsbudgetACCSQL, $pmcp_period) . ')
		AND pmrp_year IN (' . addSQLParam($paramsbudgetACCSQL, $pmcp_year) . ")
		AND pmrp_acc IN (SELECT pmcg_acc FROM pmcg_chart_grp WHERE pmcg_grp = 'TRACC2' AND pmcg_subgrp = 'INCOWN')";
// echo "First BUDGET:" . $budgetACCSQL;

$budget = $dbh->executeScalars($budgetACCSQL, $paramsbudgetACCSQL);

$tax = 0;
$merged = array_unique(array_merge($acc, $cash, $budget, $openBAL, $increcown));
asort($merged);

$limit = 36;

$budgetINCOWNTotal_display = formatting(0);
$budgetINCOWNYTotal_display = formatting(0);
$xlsLine = 12;
foreach ($merged as $key => $account) {
    // Get Account Name
    $accSQL = 'SELECT pmca_name FROM pmca_chart WHERE pmca_code = ?';
    $account_name = $dbh->executeScalar($accSQL, [$account]);

    $account_name = (strlen($account_name) < $limit) ? $account_name : substr($account_name, 0, $limit) . ' ...';

    // OWNER RECEIPTS
    $ownerincSQL = "SELECT COALESCE(SUM(pmxd_alloc_amt), 0) amount, SUM (pmxd_tax_amt) as tax_amount
		FROM pmxd_ar_alloc
		WHERE (pmxd_acc = ?)
						AND (pmxd_f_type = 'CSH')
			AND (pmxd_prop = ?)
			AND (pmxd_alloc_dt BETWEEN CONVERT(datetime, ?, 103) AND CONVERT(datetime, ?, 103))";
    $ownerinc_result = $dbh->executeSingle($ownerincSQL, [$account, $propertyID, $periodFrom, $periodTo]);
    $unallcashOISQL = 'SELECT SUM (pmuc_net_amt) AS amount
			FROM pmuc_unall_csh
			WHERE (pmuc_prop = ?)
			AND (pmuc_acc = ?)
						AND (pmuc_rcpt_dt BETWEEN CONVERT(datetime, ?, 103) AND CONVERT(datetime, ?, 103))';
    $ownerUC_income = $dbh->executeScalar($unallcashOISQL, [$propertyID, $account, $periodFrom, $periodTo]);

    $paidGSTFreeSQL = "SELECT COALESCE(SUM(pmxd_alloc_amt), 0) amount
			FROM pmxd_ar_alloc
			WHERE (pmxd_f_type = 'CSH')
			AND (pmxd_tax_amt = 0)
			AND (pmxd_prop = ?)
			AND (pmxd_alloc_dt BETWEEN CONVERT(datetime, ?, 103) AND CONVERT(datetime, ?, 103))
			AND (pmxd_acc=?)";

    $paidgstfree = $dbh->executeScalar($paidGSTFreeSQL, [$propertyID, $periodFrom, $periodTo, $account]);

    $owner_income = $ownerinc_result['amount'];
    $owner_tax_income = $ownerinc_result['tax_amount'];

    $owner_income = $owner_income - $owner_tax_income + $ownerUC_income;

    $ownerincome = $ownerincome + $owner_income;

    $ownerPOS_income = $owner_income * (-1);
    $owner_display = formatting($ownerPOS_income, 2);
    $tax = $tax + $owner_tax_income;

    // YTD1

    $ownerincASQL = "SELECT COALESCE(SUM(pmxd_alloc_amt), 0) AS amount, COALESCE(SUM(pmxd_tax_amt), 0) as tax_amount
		FROM pmxd_ar_alloc
		WHERE (pmxd_acc = ?)
						AND (pmxd_f_type = 'CSH')
			AND (pmxd_prop = ?)
			AND (pmxd_alloc_dt BETWEEN CONVERT(datetime, ?, 103) AND CONVERT(datetime, ?, 103))";
    $ownerincA_result = $dbh->executeSingle(
        $ownerincASQL,
        [$account, $propertyID, $startFinancialYear, $periodTo]
    );


    $unallcashOISQL = 'SELECT COALESCE(SUM(pmuc_net_amt), 0)  AS amount, COALESCE(SUM(pmuc_tax_amt), 0) AS tax
			FROM pmuc_unall_csh
			WHERE (pmuc_prop = ?)
			AND (pmuc_acc = ?) AND
			(pmuc_rcpt_dt BETWEEN CONVERT(datetime, ?, 103) AND CONVERT(datetime, ?, 103))';

    $ownerincUC_result = $dbh->executeScalar(
        $unallcashOISQL,
        [$propertyID, $account, $startFinancialYear, $periodTo]
    );

    $paidGSTFreeASQL = "SELECT COALESCE(SUM(pmxd_alloc_amt), 0) amount
			FROM pmxd_ar_alloc
			WHERE (pmxd_tax_amt = 0)
			AND (pmxd_f_type = 'CSH')
			AND (pmxd_prop = ?)
			AND (pmxd_acc<>NULL)
			AND (pmxd_alloc_dt BETWEEN CONVERT(datetime, ?, 103) AND CONVERT(datetime, ?, 103))
			AND (pmxd_acc=?)";

    $paidGSTFreeA_result = $dbh->executeScalar(
        $paidGSTFreeASQL,
        [$propertyID, $startFinancialYear, $periodTo, $account]
    );


    // OPENING BALANCE FOR EACH ACCOUNT
    $openBalOWNSQL = 'SELECT COALESCE(SUM(pmpb_bal), 0) as amount
		FROM pmpb_p_bal
		WHERE (pmpb_prop = ?)
		AND (pmpb_date BETWEEN CONVERT(datetime, ?, 103) AND CONVERT(datetime, ?, 103))
		AND (pmpb_acc = ?)';
    $openBalOWN_result = $dbh->executeScalar(
        $openBalOWNSQL,
        [$propertyID, $startFinancialYear, $periodTo, $account]
    );


    // -----------OPENING BALANCE FIGURES------------------------
    $openBalOWN_income = $openBalOWN_result;
    // ----------------------------------------------------------

    // -----------BUDGET FIGURES ---------------------------------
    $paramsbudgetINCOWNSQL = [];
    $budgetINCOWNSQL = '
	SELECT
		COALESCE(SUM(pmrp_b_c_amt), 0) as amount
	FROM
		pmrp_b_rev_per
	WHERE
		pmrp_prop = ' . addSQLParam($paramsbudgetINCOWNSQL, $propertyID) . '
		AND pmrp_per IN ( ' . addSQLParam($paramsbudgetINCOWNSQL, $pmcp_period) . ')
		AND pmrp_year IN ( ' . addSQLParam($paramsbudgetINCOWNSQL, $pmcp_year) . ')
		AND pmrp_acc =  ' . addSQLParam($paramsbudgetINCOWNSQL, $account) . ' ';
    // echo "Second BUDGET:" . $budgetINCOWNSQL;

    $budgetINCOWN = $dbh->executeScalar($budgetINCOWNSQL, $paramsbudgetINCOWNSQL);

    $budgetINCOWNTotal = $budgetINCOWNTotal + $budgetINCOWN;
    $budgetINCOWNTotal_display = formatting($budgetINCOWNTotal);
    $budgetINCOWN_display = formatting($budgetINCOWN);

    $paramsbudgetINCOWNYSQL = [];
    $budgetINCOWNYSQL = 'SELECT COALESCE(SUM(pmrp_b_c_amt), 0) AS amount
		FROM pmrp_b_rev_per
		WHERE (pmrp_prop = ' . addSQLParam($paramsbudgetINCOWNYSQL, $propertyID) . ')
		AND (pmrp_per >= 1)
		AND (pmrp_per <= ' . addSQLParam($paramsbudgetINCOWNYSQL, $toPeriod) . ')
				  AND pmrp_year IN (' . addSQLParam($paramsbudgetINCOWNYSQL, $pmcp_year) . ')
				AND (pmrp_acc =' . addSQLParam($paramsbudgetINCOWNYSQL, $account) . ')';
    // echo "Third BUDGET:" . $budgetINCOWNYSQL;

    $budgetINCOWNY_result = $dbh->executeScalar($budgetINCOWNYSQL, $paramsbudgetINCOWNYSQL);
    $budgetINCOWNY = $budgetINCOWNY_result;
    $budgetINCOWNY_display = formatting($budgetINCOWNY, 2);
    $budgetINCOWNYTotal = $budgetINCOWNYTotal + $budgetINCOWNY;
    $budgetINCOWNYTotal_display = formatting($budgetINCOWNYTotal);

    $paramsbudgetINCOWNYEARSQL = [];
    $budgetINCOWNYEARSQL = 'SELECT COALESCE(SUM(pmrp_b_c_amt), 0) AS amount
		FROM pmrp_b_rev_per
		WHERE (pmrp_prop = ' . addSQLParam($paramsbudgetINCOWNYEARSQL, $propertyID) . ')
		AND (pmrp_per BETWEEN 1 AND 12)AND (pmrp_year IN (' . addSQLParam($paramsbudgetINCOWNYEARSQL, $pmcp_year) . '))
				AND (pmrp_acc = ' . addSQLParam($paramsbudgetINCOWNYEARSQL, $account) . ')';
    // echo "Fourth BUDGET:" . $budgetINCOWNYEARSQL;

    $budgetINCOWNYEAR = (float) $dbh->executeScalar($budgetINCOWNYEARSQL, $paramsbudgetINCOWNYEARSQL);
    $budgetINCOWNYEAR_display = formatting($budgetINCOWNYEAR, 2);
    $budgetINCOWNYEARTotal = $budgetINCOWNYEARTotal + $budgetINCOWNYEAR;

    $ownerUC_incomeA = (float) $ownerincUC_result;
    $ownerA_income = (float) $ownerincA_result['amount'];

    $ownerA_tax_income = (float) $ownerincA_result['tax_amount'];

    $ownerA_income = $ownerA_income - $ownerA_tax_income + $ownerUC_incomeA - $openBalOWN_income;

    $ownerAPOS_income = $ownerA_income * (-1);

    $ownerA_display = formatting($ownerAPOS_income, 2);

    $variance = $ownerPOS_income - $budgetINCOWN;
    $variance_display = formatting($variance);
    if ($budgetINCOWN == 0) {
        if ($ownerPOS_income == 0) {
            $varianceP = 0;
        } else {
            $varianceP = 100;
            if ($variance < 0) {
                $varianceP = -100;
            }
        }
    } else {
        $varianceP = $variance / $budgetINCOWN * 100;
    }
    $varianceP_display = formatting($varianceP);


    $varianceY = $ownerAPOS_income - $budgetINCOWNY;
    $varianceY_display = formatting($varianceY);
    if ($budgetINCOWNY == 0) {
        $variancePY = 100;
        if ($varianceY < 0) {
            $variancePY = -100;
        }
    } else {
        $variancePY = $varianceY / $budgetINCOWNY * 100;
    }
    $variancePY_display = formatting($variancePY);

    if ($owner_income == 0 && $ownerA_income == 0 && $budgetINCOWN == 0 && $budgetINCOWNY == 0 && $budgetINCOWNYEAR_display == 0) {
        // DON NOT DISPLAY LINE
    } else {
        $excel->getActiveSheet()->getStyle("B{$xlsLine}:J{$xlsLine}")->getAlignment()->setHorizontal(
            Alignment::HORIZONTAL_RIGHT
        );
        $excel->getActiveSheet()->getStyle("A{$xlsLine}:J{$xlsLine}")->getFont()->setBold(false);
        $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 1), $account . '     ' . $account_name);
        $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 2), $owner_display);
        $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 3), $budgetINCOWN_display);
        $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 7), $budgetINCOWNY_display);
        $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 10), $budgetINCOWNYEAR_display);
        $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 4), $variance_display);
        $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 5), $varianceP_display);
        $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 8), $varianceY_display);
        $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 9), $variancePY_display);
        $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 6), $ownerA_display);

        $xlsLine = $xlsLine + 1;
    }
}


$ownergst = "SELECT COALESCE(SUM(pmxd_tax_amt), 0) AS amount
		FROM pmxd_ar_alloc
		WHERE (pmxd_f_type = 'CSH')
		AND (pmxd_prop = ?)
		AND (pmxd_acc IN
					  (SELECT pmcg_acc
					   FROM pmcg_chart_grp
					   WHERE (pmcg_grp = 'TRACC2')
					   AND (pmcg_subgrp = 'INCVO')
					   OR (pmcg_subgrp = 'INCDR')
					   OR (pmcg_subgrp = 'INCOWN')))
				AND (pmxd_alloc_dt BETWEEN CONVERT(datetime, ?, 103) AND CONVERT(datetime, ?, 103))";


$ownergst_result = $dbh->executeScalar($ownergst, [$propertyID, $periodFrom, $periodTo]);
$gst = $ownergst_result;

$unAllocCash = "SELECT COALESCE(SUM(pmuc_amt), 0) AS amount
		FROM pmuc_unall_csh
		WHERE (pmuc_prop = ?)
		AND (pmuc_rcpt_dt BETWEEN CONVERT(datetime, ?, 103) AND CONVERT(datetime, ?, 103))
		AND (pmuc_acc = '0701')";
$cash_result = $dbh->executeScalar($unAllocCash, [$propertyID, $periodFrom, $periodTo]);
$gst_cash = $cash_result;

$gst = ($gst - $gst_cash) * (-1);
$gst_display = formatting($gst, 2);
$ownergstA = "SELECT COALESCE(SUM(pmxd_tax_amt), 0) AS amount
		FROM pmxd_ar_alloc
		WHERE (pmxd_f_type = 'CSH')
		AND (pmxd_prop = ?)
		AND (pmxd_acc IN
						  (SELECT pmcg_acc
							FROM pmcg_chart_grp
							WHERE (pmcg_grp = 'TRACC2')
							AND (pmcg_subgrp = 'INCVO')
							OR (pmcg_subgrp = 'INCDR')
							OR (pmcg_subgrp = 'INCOWN')))
				AND (pmxd_alloc_dt BETWEEN CONVERT(datetime, ?, 103) AND CONVERT(datetime, ?, 103))";
$ownergstA_result = $dbh->executeScalar($ownergstA, [$propertyID, $startFinancialYear, $periodTo]);
$unAllocCashA = "SELECT COALESCE(SUM(pmuc_amt), 0) AS amount
		FROM pmuc_unall_csh
		WHERE (pmuc_prop = ?)
		AND (pmuc_rcpt_dt BETWEEN CONVERT(datetime, ?, 103) AND CONVERT(datetime, ?, 103))
		AND (pmuc_acc = '0701')";
$cashA_result = $dbh->executeScalar($unAllocCashA, [$propertyID, $startFinancialYear, $periodTo]);
$gstA_cash = $cashA_result;

$gstA = $ownergstA_result;


$gstA = ($gstA + $gstA_cash) * (-1);
$gstA_display = formatting($gstA, 2);


// $ownerincome = $ownerincome - $gst;
$ownerincomePOS = $ownerincome * (-1);

$ownerincomeA = dbTotalIncomePerGroup($propertyID, $startFinancialYear, $periodTo, 'INCOWN');
$own_takeon = take_on_balance_INCOWN($propertyID, 'INCOWN', $startFinancialYear, $periodTo);
$ownerincomeAPOS = bcadd($ownerincomeA['net_amount'], $own_takeon['amount'], 2);

// $ownerincomeAPOS = $ownerincomeA * (-1);
$total_oi = formatting($ownerincomePOS, 2);
$total_oiA = formatting($ownerincomeAPOS, 2);

$varianceT = $ownerincomePOS - $budgetINCOWNTotal;
$varianceT_display = formatting($varianceT);
if ($budgetINCOWNTotal == 0) {
    $varianceTP = 100;
    if ($varianceT < 0) {
        $varianceTP = -100;
    }
} else {
    $varianceTP = $varianceT / $budgetINCOWNTotal * 100;
}
$varianceTP_display = formatting($varianceTP);

$varianceTY = $ownerincomeAPOS - $budgetINCOWNYTotal;
$varianceTY_display = formatting($varianceTY);
if ($budgetINCOWNYTotal == 0) {
    $varianceTPY = 100;
    if ($varianceTY < 0) {
        $varianceTPY = -100;
    }

    if ($ownerincomeAPOS == 0) {
        $varianceTPY = 0;
    }
} else {
    $varianceTPY = $varianceTY / $budgetINCOWNYTotal * 100;
}
$varianceTPY_display = formatting($varianceTPY);

$budgetINCOWNYEARTotal_display = formatting($budgetINCOWNYEARTotal);

if ($total_oi == '0.00' && $total_oiA == '0.00' && $budgetINCOWNTotal_display == '0.00' && $budgetINCOWNYTotal_display == '0.00' && $budgetINCOWNYEARTotal_display == '0.00') {
    $xlsLine = $xlsLine + 1;
} else {
    $excel->getActiveSheet()->getStyle("B{$xlsLine}:J{$xlsLine}")->getAlignment()->setHorizontal(
        Alignment::HORIZONTAL_RIGHT
    );
    $excel->getActiveSheet()->getStyle("B{$xlsLine}:J{$xlsLine}")->getFont()->setBold(true);
    $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 2), $total_oi);
    $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 3), $budgetINCOWNTotal_display);
    $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 6), $total_oiA);
    $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 7), $budgetINCOWNYTotal_display);
    $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 4), $varianceT_display);
    $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 5), $varianceTP_display);
    $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 8), $varianceTY_display);
    $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 9), $varianceTPY_display);
    $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 10), $budgetINCOWNYEARTotal_display);
    $xlsLine = $xlsLine + 1;


    if (strpos($total_oi, ')')) {
        $total_oi = str_replace(')', '', $total_oi);
        $total_oi = str_replace('(', '-', $total_oi);
    } else {

    }
    if (strpos($total_oiA, ')')) {
        $total_oiA = str_replace(')', '', $total_oiA);
        $total_oiA = str_replace('(', '-', $total_oiA);
    } else {

    }
    if (strpos($budgetINCOWNTotal_display, ')')) {
        $budgetINCOWNTotal_display = str_replace(')', '', $budgetINCOWNTotal_display);
        $budgetINCOWNTotal_display = str_replace('(', '-', $budgetINCOWNTotal_display);
    } else {

    }
    if (strpos($budgetINCOWNYTotal_display, ')')) {
        $budgetINCOWNYTotal_display = str_replace(')', '', $budgetINCOWNYTotal_display);
        $budgetINCOWNYTotal_display = str_replace('(', '-', $budgetINCOWNYTotal_display);
    } else {

    }
    if (strpos($budgetINCOWNYEARTotal_display, ')')) {
        $budgetINCOWNYEARTotal_display = str_replace(')', '', $budgetINCOWNYEARTotal_display);
        $budgetINCOWNYEARTotal_display = str_replace('(', '-', $budgetINCOWNYEARTotal_display);
    } else {

    }
    if (strpos($varianceT_display, ')')) {
        $varianceT_display = str_replace(')', '', $varianceT_display);
        $varianceT_display = str_replace('(', '-', $varianceT_display);
    } else {

    }
    if (strpos($varianceTP_display, ')')) {
        $varianceTP_display = str_replace(')', '', $varianceTP_display);
        $varianceTP_display = str_replace('(', '-', $varianceTP_display);
    } else {

    }
    if (strpos($varianceTY_display, ')')) {
        $varianceTY_display = str_replace(')', '', $varianceTY_display);
        $varianceTY_display = str_replace('(', '-', $varianceTY_display);
    } else {

    }
    if (strpos($varianceTPY_display, ')')) {
        $varianceTPY_display = str_replace(')', '', $varianceTPY_display);
        $varianceTPY_display = str_replace('(', '-', $varianceTPY_display);
    } else {

    }
    $_totalAllIncome_display = $_totalAllIncome_display + floatval(str_replace(',', '', $total_oi));
    $_totalAllIncomeA_display = $_totalAllIncomeA_display + floatval(str_replace(',', '', $total_oiA));
    $_totalAllBudgetIncome_display = $_totalAllBudgetIncome_display + floatval(
        str_replace(',', '', $budgetINCOWNTotal_display)
    );
    $_totalAllBudgetIncomeA_display = $_totalAllBudgetIncomeA_display + floatval(
        str_replace(',', '', $budgetINCOWNYTotal_display)
    );
    $_totalAllBudgetIncomeYEAR_display = $_totalAllBudgetIncomeYEAR_display + floatval(
        str_replace(',', '', $budgetINCOWNYEARTotal_display)
    );
    $_totalAllVarianceRECEIPTS_display = $_totalAllVarianceRECEIPTS_display + floatval(
        str_replace(',', '', $varianceT_display)
    );
    //    $_totalAllVP_display1=$_totalAllVP_display1+floatval(str_replace(',','',$varianceTP_display));
    $_totalAllVarianceY_display = $_totalAllVarianceY_display + floatval(str_replace(',', '', $varianceTY_display));
    //    $_totalAllVPY_display=$_totalAllVPY_display+floatval(str_replace(',','',$varianceTPY_display));

}

// --------------------------------------------------------------------------
// RECOVERABLE RECEIPTS
//
// ACCOUNTS FOR INDIV CALCULATIONS
$accSQL = "SELECT DISTINCT pmxd_acc
		FROM pmxd_ar_alloc
		WHERE  fund IS NULL AND (pmxd_prop = ?)
		AND (pmxd_alloc_dt <= CONVERT(datetime, ?, 103))
		AND (pmxd_f_type = 'CSH')
				AND (pmxd_acc IN
						  (SELECT pmcg_acc
							FROM pmcg_chart_grp
							WHERE (pmcg_grp = 'TRACC2')
							AND pmcg_subgrp = 'INCDR'))
			UNION ALL
			SELECT DISTINCT pmuc_acc
						FROM pmuc_unall_csh
					wHERE (pmuc_prop = ?)
			AND (pmuc_rcpt_dt < CONVERT(datetime, ?, 103))
			AND (pmuc_acc <> '')
			AND (pmuc_acc IN(SELECT pmcg_acc
				FROM pmcg_chart_grp
				WHERE (pmcg_grp = 'TRACC2')
				AND pmcg_subgrp = 'INCDR'))";

$accSQL_result = $dbh->executeScalars($accSQL, [$propertyID, $periodTo, $propertyID, $periodTo]);


$opBAL_accSQL = "SELECT DISTINCT pmpb_acc as account
		FROM pmpb_p_bal
		WHERE (pmpb_prop = ?)
		AND (pmpb_date BETWEEN CONVERT(datetime, ?, 103) AND CONVERT(datetime, ?, 103))
		AND (pmpb_acc IN (SELECT pmcg_acc
									FROM pmcg_chart_grp
									WHERE (pmcg_grp = 'TRACC2')
									AND (pmcg_subgrp = 'INCDR')))";
$opBALacc_result = $dbh->executeScalars($opBAL_accSQL, [$propertyID, $startFinancialYear, $periodTo]);


$paramsbudgetACCSQL = [];
$budgetACCSQL = 'SELECT DISTINCT pmrp_acc AS account
FROM         pmrp_b_rev_per
WHERE     (pmrp_prop = ' . addSQLParam($paramsbudgetACCSQL, $propertyID) . ')
    AND pmrp_per IN (' . addSQLParam($paramsbudgetACCSQL, $pmcp_period) . ')
    AND pmrp_year IN (' . addSQLParam($paramsbudgetACCSQL, $pmcp_year) . ")
    AND (pmrp_acc IN
						  (SELECT pmcg_acc
							FROM pmcg_chart_grp
							WHERE (pmcg_grp = 'TRACC2') AND pmcg_subgrp = 'INCDR'))";
// echo "5th BUDGET:" . $budgetACCSQL;
$budget_result = $dbh->executeScalars($budgetACCSQL, $paramsbudgetACCSQL);


$acc = [];
$acc = array_merge($accSQL_result, $opBALacc_result, $budget_result);
$merged = array_unique($acc);


asort($merged);

$excel->getActiveSheet()->getStyle("A{$xlsLine}:J{$xlsLine}")->getFont()->setBold(true);
$excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 1), 'Recoverables Receipts');
$xlsLine = $xlsLine + 1;

foreach ($merged as $account) {
    // Get Account Name
    $accSQL = 'SELECT pmca_name FROM pmca_chart WHERE pmca_code = ?';
    $acc_result = $dbh->executeScalar($accSQL, [$account]);
    $account_name = $acc_result;
    $account_name = (strlen($account_name) < $limit) ? $account_name : substr($account_name, 0, $limit) . ' ...';


    // OWNER RECEIPTS
    $recSQL = "SELECT COALESCE(SUM(pmxd_alloc_amt), 0) amount, SUM (pmxd_tax_amt) as tax_amount
		FROM pmxd_ar_alloc
		WHERE (pmxd_acc = ?)
						AND (pmxd_f_type = 'CSH')
			AND (pmxd_prop = ?)
			AND (pmxd_alloc_dt BETWEEN CONVERT(datetime, ?, 103) AND CONVERT(datetime, ?, 103))";
    $rec_result = $dbh->executeSingle($recSQL, [$account, $propertyID, $periodFrom, $periodTo]);

    $unallcashRECSQL = 'SELECT SUM (pmuc_net_amt) AS amount
			FROM pmuc_unall_csh
			WHERE (pmuc_prop = ?)
			AND (pmuc_acc = ?)
						AND (pmuc_rcpt_dt BETWEEN CONVERT(datetime, ?, 103) AND CONVERT(datetime, ?, 103))';
    $recUC_result = $dbh->executeScalar($unallcashRECSQL, [$propertyID, $account, $periodFrom, $periodTo]);

    $rec_income = $rec_result['amount'];
    $rec_tax_income = $rec_result['tax_amount'];
    $recUC_income = $recUC_result;

    $rec_income = $rec_income + $recUC_income - $rec_tax_income;
    $recinc = $recinc + $rec_income;
    $recPOS_income = $rec_income * (-1);
    $rec_display = formatting($recPOS_income, 2);

    // YTD2

    $recASQL = "SELECT COALESCE(SUM(pmxd_alloc_amt), 0) AS amount, COALESCE(SUM(pmxd_tax_amt), 0) as tax_amount
		FROM pmxd_ar_alloc
		WHERE (pmxd_acc = ?)
						AND (pmxd_f_type = 'CSH')
			AND (pmxd_prop = ?)
			AND (pmxd_alloc_dt BETWEEN CONVERT(datetime, ?, 103) AND CONVERT(datetime, ?, 103))";
    $recA_result = $dbh->executeSingle($recASQL, [$account, $propertyID, $startFinancialYear, $periodTo]);


    // OPENING BALANCE FOR EACH ACCOUNT
    $openBalOWNSQL = 'SELECT COALESCE(SUM(pmpb_bal), 0) as amount
		FROM pmpb_p_bal
		WHERE (pmpb_prop = ?)
		AND (pmpb_date BETWEEN CONVERT(datetime, ?, 103) AND CONVERT(datetime, ?, 103))
		AND (pmpb_acc = ?)';
    $openBalOWN_result = $dbh->executeScalar(
        $openBalOWNSQL,
        [$propertyID, $startFinancialYear, $periodTo, $account]
    );

    // -----------OPENING BALANCE FIGURES------------------------
    $openBalDR_income = $openBalOWN_result;
    // ----------------------------------------------------------

    // -----------BUDGET FIGURES ---------------------------------
    $paramsbudgetINCRECSQL = [];
    $budgetINCRECSQL = 'SELECT COALESCE(SUM(pmrp_b_c_amt), 0) AS amount
		FROM pmrp_b_rev_per
		WHERE (pmrp_prop = ' . addSQLParam($paramsbudgetINCRECSQL, $propertyID) . ')
		AND (pmrp_per IN (' . addSQLParam($paramsbudgetINCRECSQL, $pmcp_period) . '))
				AND (pmrp_year IN (' . addSQLParam($paramsbudgetINCRECSQL, $pmcp_year) . '))
				AND (pmrp_acc = ' . addSQLParam($paramsbudgetINCRECSQL, $account) . ')';
    // echo "6th BUDGET:" . $budgetINCRECSQL;

    $budgetINCREC_result = $dbh->executeScalar($budgetINCRECSQL, $paramsbudgetINCRECSQL);
    $budgetINCREC = $budgetINCREC_result;
    $budgetINCRECTotal = $budgetINCRECTotal + $budgetINCREC;
    $budgetINCRECTotal_display = formatting($budgetINCRECTotal);
    $budgetINCREC_display = formatting($budgetINCREC);

    $paramsbudgetINCRECYSQL = [];
    $budgetINCRECYSQL = 'SELECT COALESCE(SUM(pmrp_b_c_amt), 0) AS amount
		FROM pmrp_b_rev_per
		WHERE (pmrp_prop = ' . addSQLParam($paramsbudgetINCRECYSQL, $propertyID) . ')
		AND (pmrp_per >= 1)
		AND (pmrp_per <= ' . addSQLParam($paramsbudgetINCRECYSQL, $toPeriod) . ')
		AND (pmrp_year IN (' . addSQLParam($paramsbudgetINCRECYSQL, $pmcp_year) . '))
				AND (pmrp_acc =' . addSQLParam($paramsbudgetINCRECYSQL, $account) . ')';
    // echo "7th BUDGET:" . $budgetINCRECYSQL;

    $budgetINCRECY_result = $dbh->executeScalar($budgetINCRECYSQL, $paramsbudgetINCRECYSQL);
    $budgetINCRECY = $budgetINCRECY_result;
    $budgetINCRECY_display = formatting($budgetINCRECY, 2);
    $budgetINCRECYTotal = $budgetINCRECYTotal + $budgetINCRECY;
    $budgetINCRECYTotal_display = formatting($budgetINCRECYTotal);

    $paramsbudgetINCRECYEARSQL = [];
    $budgetINCRECYEARSQL = 'SELECT COALESCE(SUM(pmrp_b_c_amt), 0) AS amount
		FROM pmrp_b_rev_per
		WHERE (pmrp_prop = ' . addSQLParam($paramsbudgetINCRECYEARSQL, $propertyID) . ')
		AND (pmrp_per BETWEEN 1 AND 12)AND (pmrp_year IN (' . addSQLParam($paramsbudgetINCRECYEARSQL, $pmcp_year) . '))
				AND (pmrp_acc = ' . addSQLParam($paramsbudgetINCRECYEARSQL, $account) . ')';

    $budgetINCRECYEAR_result = $dbh->executeScalar($budgetINCRECYEARSQL, $paramsbudgetINCRECYEARSQL);
    $budgetINCRECYEAR = $budgetINCRECYEAR_result;
    $budgetINCRECYEAR_display = formatting($budgetINCRECYEAR, 2);
    $budgetINCRECYEARTotal = $budgetINCRECYEARTotal + $budgetINCRECYEAR;

    $paramsunallcashRECSQL = [];
    $unallcashRECSQL = 'SELECT COALESCE(SUM(pmuc_net_amt), 0) as amount
			FROM pmuc_unall_csh
			WHERE (pmuc_prop = ' . addSQLParam($paramsunallcashRECSQL, $propertyID) . ')
			AND (pmuc_acc = ' . addSQLParam($paramsunallcashRECSQL, $account) . ')
			AND (pmuc_period >= 1)
						AND (pmuc_period <= ' . addSQLParam($paramsunallcashRECSQL, $toPeriod) . ')
				AND (pmuc_year IN (' . addSQLParam($paramsunallcashRECSQL, $pmcp_year) . '))';

    $recUC_result = $dbh->executeScalar($unallcashRECSQL, $paramsunallcashRECSQL);
    $recUC_income = $recUC_result;

    $recA_income = $recA_result['amount'];
    $recA_tax_income = $recA_result['tax_amount'];
    $recA_income = $recA_income + $recUC_income - $recA_tax_income - $openBalDR_income;
    $recAPOS_income = $recA_income * (-1);
    $recA_display = formatting($recAPOS_income, 2);

    $variance = $recPOS_income - $budgetINCREC;
    $variance_display = formatting($variance);
    if ($budgetINCREC == 0) {
        $varianceP = 100;
        if ($variance < 0) {
            $varianceP = -100;
        }
        if ($recPOS_income == 0) {
            $varianceP = 0;
        }
    } else {
        $varianceP = $variance / $budgetINCREC * 100;
    }
    $varianceP_display = formatting($varianceP);


    $varianceY = $recAPOS_income - $budgetINCRECY;
    $varianceY_display = formatting($varianceY);
    if ($budgetINCRECY == 0) {
        $variancePY = 100;
        if ($varianceY < 0) {
            $variancePY = -100;
        }
    } else {
        $variancePY = $varianceY / $budgetINCRECY * 100;
    }
    $variancePY_display = formatting($variancePY);

    if ($rec_income == 0 && $recA_income == 0 && $budgetINCREC == 0 && $budgetINCRECY == 0 && $budgetINCRECYEAR == 0) {
        // DON NOT DISPLAY LINE
    } else {
        $excel->getActiveSheet()->getStyle("B{$xlsLine}:J{$xlsLine}")->getAlignment()->setHorizontal(
            Alignment::HORIZONTAL_RIGHT
        );
        $excel->getActiveSheet()->getStyle("A{$xlsLine}:J{$xlsLine}")->getFont()->setBold(false);
        $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 1), $account . ' ' . $account_name);
        $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 2), $rec_display);
        $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 3), $budgetINCREC_display);
        $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 4), $variance_display);
        $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 5), $varianceP_display);
        $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 6), $recA_display);
        $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 7), $budgetINCRECY_display);
        $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 8), $varianceY_display);
        $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 9), $variancePY_display);
        $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 10), $budgetINCRECYEAR_display);
        $xlsLine = $xlsLine + 1;
    }
}


$recincPOS = $recinc * (-1);
$recincA = dbTotalIncomePerGroup($propertyID, $startFinancialYear, $periodTo, 'INCDR');
$recincomeA = $recincA['net_amount'];

$rec_takeon = take_on_balance($propertyID, 'INCDR', $startFinancialYear, $periodTo);
$recincAPOS = @($recincomeA + $rec_takeon['amount']);

// $recincAPOS = $recincA * (-1);
$total_rec = formatting($recincPOS, 2);
$total_recA = formatting($recincAPOS, 2);


$varianceT = $recincPOS - $budgetINCRECTotal;
$varianceT_display = formatting($varianceT);
if ($budgetINCRECTotal == 0) {
    $varianceTP = 100;
    if ($varianceT < 0) {
        $varianceTP = -100;
    }
    if ($recincPOS == 0) {
        $varianceTP = 0;
    }
} else {
    $varianceTP = $varianceT / $budgetINCRECTotal * 100;
}
$varianceTP_display = formatting($varianceTP);

$varianceTY = $recincAPOS - $budgetINCRECYTotal;
$varianceTY_display = formatting($varianceTY);
if ($budgetINCRECYTotal == 0) {
    $varianceTPY = 100;
    if ($varianceTY < 0) {
        $varianceTPY = -100;
    }
    if ($recincAPOS == 0) {
        $varianceTPY = 0;
    }
} else {
    $varianceTPY = $varianceTY / $budgetINCRECYTotal * 100;
}
$varianceTPY_display = formatting($varianceTPY);

$budgetINCRECYEARTotal_display = formatting($budgetINCRECYEARTotal);
$budgetINCRECYTotal_display = formatting($budgetINCRECYTotal);
$budgetINCRECTotal_display = formatting($budgetINCRECTotal);

if ($budgetINCRECTotal_display == ' ') {
    $budgetINCRECTotal_display = '0.00';
}
if ($budgetINCRECYTotal_display == ' ') {
    $budgetINCRECYTotal_display = '0.00';
}
if ($budgetINCRECYEARTotal_display == ' ') {
    $budgetINCRECYEARTotal_display = '0.00';
}


if ($total_rec == '0.00' && $total_recA == '0.00' && $budgetINCRECTotal_display == '0.00' && $budgetINCRECYTotal_display == '0.00' && $budgetINCRECYEARTotal_display == '0.00') {
    $xlsLine = $xlsLine + 1;
} else {
    $excel->getActiveSheet()->getStyle("B{$xlsLine}:J{$xlsLine}")->getAlignment()->setHorizontal(
        Alignment::HORIZONTAL_RIGHT
    );
    $excel->getActiveSheet()->getStyle("A{$xlsLine}:J{$xlsLine}")->getFont()->setBold(true);
    $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 2), $total_rec);
    $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 3), $budgetINCRECTotal_display);
    $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 4), $varianceT_display);
    $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 5), $varianceTP_display);
    $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 6), $total_recA);
    $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 7), $budgetINCRECYTotal_display);
    $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 8), $varianceTY_display);
    $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 9), $varianceTPY_display);
    $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 10), $budgetINCRECYEARTotal_display);
    $xlsLine = $xlsLine + 1;


    if (strpos($total_rec, ')')) {
        $total_rec = str_replace(')', '', $total_rec);
        $total_rec = str_replace('(', '-', $total_rec);
    } else {

    }
    if (strpos($total_recA, ')')) {
        $total_recA = str_replace(')', '', $total_recA);
        $total_recA = str_replace('(', '-', $total_recA);
    } else {

    }
    if (strpos($budgetINCRECTotal_display, ')')) {
        $budgetINCRECTotal_display = str_replace(')', '', $budgetINCRECTotal_display);
        $budgetINCRECTotal_display = str_replace('(', '-', $budgetINCRECTotal_display);
    } else {

    }
    if (strpos($budgetINCRECYTotal_display, ')')) {
        $budgetINCRECYTotal_display = str_replace(')', '', $budgetINCRECYTotal_display);
        $budgetINCRECYTotal_display = str_replace('(', '-', $budgetINCRECYTotal_display);
    } else {

    }
    if (strpos($budgetINCRECYEARTotal_display, ')')) {
        $budgetINCRECYEARTotal_display = str_replace(')', '', $budgetINCRECYEARTotal_display);
        $budgetINCRECYEARTotal_display = str_replace('(', '-', $budgetINCRECYEARTotal_display);
    } else {

    }
    if (strpos($varianceT_display, ')')) {
        $varianceT_display = str_replace(')', '', $varianceT_display);
        $varianceT_display = str_replace('(', '-', $varianceT_display);
    } else {

    }
    if (strpos($varianceTP_display, ')')) {
        $varianceTP_display = str_replace(')', '', $varianceTP_display);
        $varianceTP_display = str_replace('(', '-', $varianceTP_display);
    } else {

    }
    if (strpos($varianceTY_display, ')')) {
        $varianceTY_display = str_replace(')', '', $varianceTY_display);
        $varianceTY_display = str_replace('(', '-', $varianceTY_display);
    } else {

    }
    if (strpos($varianceTPY_display, ')')) {
        $varianceTPY_display = str_replace(')', '', $varianceTPY_display);
        $varianceTPY_display = str_replace('(', '-', $varianceTPY_display);
    } else {

    }

    $_totalAllIncome_display = $_totalAllIncome_display + floatval(str_replace(',', '', $total_rec));
    $_totalAllIncomeA_display = $_totalAllIncomeA_display + floatval(str_replace(',', '', $total_recA));
    $_totalAllBudgetIncome_display = $_totalAllBudgetIncome_display + floatval(
        str_replace(',', '', $budgetINCRECTotal_display)
    );
    $_totalAllBudgetIncomeA_display = $_totalAllBudgetIncomeA_display + floatval(
        str_replace(',', '', $budgetINCRECYTotal_display)
    );
    $_totalAllBudgetIncomeYEAR_display = $_totalAllBudgetIncomeYEAR_display + floatval(
        str_replace(',', '', $budgetINCRECYEARTotal_display)
    );
    $_totalAllVarianceRECEIPTS_display = $_totalAllVarianceRECEIPTS_display + floatval(
        str_replace(',', '', $varianceT_display)
    );
    //    $_totalAllVP_display1=$_totalAllVP_display1+floatval(str_replace(',','',$varianceTP_display));
    $_totalAllVarianceY_display = $_totalAllVarianceY_display + floatval(str_replace(',', '', $varianceTY_display));
    //    $_totalAllVPY_display=$_totalAllVPY_display+floatval(str_replace(',','',$varianceTPY_display));

}


// --------------------------------------------------------------------------
// VARIABLE OUTGOINGS RECEIPTS
//
// ACCOUNTS FOR INDIV CALCULATIONS
$accSQL = "SELECT DISTINCT pmxd_acc
		FROM pmxd_ar_alloc
		WHERE fund IS NULL AND (pmxd_prop = ?)
		AND (pmxd_alloc_dt <= CONVERT(datetime, ?, 103))
		AND (pmxd_f_type = 'CSH')
				AND (pmxd_acc IN
						  (SELECT pmcg_acc
							FROM pmcg_chart_grp
							WHERE (pmcg_grp = 'TRACC2')
							AND pmcg_subgrp = 'INCVO'))
			UNION ALL
			SELECT DISTINCT pmuc_acc
						FROM pmuc_unall_csh
					wHERE (pmuc_prop = ?)
			AND (pmuc_rcpt_dt < CONVERT(datetime, ?, 103))
			AND (pmuc_acc <> '')
			AND (pmuc_acc IN(SELECT pmcg_acc
				FROM pmcg_chart_grp
				WHERE (pmcg_grp = 'TRACC2')
				AND pmcg_subgrp = 'INCVO'))";

$accSQL_result = $dbh->executeScalars($accSQL, [$propertyID, $periodTo, $propertyID, $periodTo]);


$opBAL_accSQL = "SELECT DISTINCT pmpb_acc as account
		FROM pmpb_p_bal
		WHERE (pmpb_prop = ?)
		AND (pmpb_date BETWEEN CONVERT(datetime, ?, 103) AND CONVERT(datetime, ?, 103))
		AND (pmpb_acc IN (SELECT pmcg_acc
									FROM pmcg_chart_grp
									WHERE (pmcg_grp = 'TRACC2')
									AND (pmcg_subgrp = 'INCVO')))";
$opBALacc_result = $dbh->executeScalars($opBAL_accSQL, [$propertyID, $startFinancialYear, $periodTo]);


$paramsbudgetACCSQL = [];
$budgetACCSQL = 'SELECT DISTINCT pmrp_acc AS account
FROM         pmrp_b_rev_per
WHERE     (pmrp_prop = ' . addSQLParam($paramsbudgetACCSQL, $propertyID) . ')
    AND (pmrp_per IN (' . addSQLParam($paramsbudgetACCSQL, $pmcp_period) . '))
    AND (pmrp_year IN (' . addSQLParam($paramsbudgetACCSQL, $pmcp_year) . "))
    AND (pmrp_acc IN
						  (SELECT pmcg_acc
							FROM pmcg_chart_grp
							WHERE (pmcg_grp = 'TRACC2') AND pmcg_subgrp = 'INCVO'))";
// echo "8th BUDGET:" . $budgetACCSQL;
$budget_result = $dbh->executeScalars($budgetACCSQL, $paramsbudgetACCSQL);


$acc = [];
$acc = array_merge($accSQL_result, $opBALacc_result, $budget_result);
$merged = array_unique($acc);

asort($merged);

$excel->getActiveSheet()->getStyle("A{$xlsLine}:J{$xlsLine}")->getFont()->setBold(true);
$excel->getActiveSheet()->SetCellValue(
    cellReference($xlsLine, 1),
    ucwords(strtolower($_SESSION['country_default']['variable_outgoings'])) . ' Receipts'
);
$xlsLine = $xlsLine + 1;

foreach ($merged as $account) {
    // Get Account Name
    $accSQL = 'SELECT pmca_name FROM pmca_chart WHERE pmca_code = ?';
    $acc_result = $dbh->executeScalar($accSQL, [$account]);
    $account_name = $acc_result;
    $account_name = (strlen($account_name) < $limit) ? $account_name : substr($account_name, 0, $limit) . ' ...';


    // OPENING BALANCE FOR EACH ACCOUNT
    $openBalOWNSQL = 'SELECT COALESCE(SUM(pmpb_bal), 0) as amount
		FROM pmpb_p_bal
		WHERE (pmpb_prop = ?)
		AND (pmpb_date BETWEEN CONVERT(datetime, ?, 103) AND CONVERT(datetime, ?, 103))
		AND (pmpb_acc = ?)';
    $openBalOWN_result = $dbh->executeScalar(
        $openBalOWNSQL,
        [$propertyID, $startFinancialYear, $periodTo, $account]
    );

    $paramsbudgetINCVOSQL = [];
    $budgetINCVOSQL = 'SELECT COALESCE(SUM(pmrp_b_c_amt), 0) AS amount
		FROM pmrp_b_rev_per
		WHERE (pmrp_prop = ' . addSQLParam($paramsbudgetINCVOSQL, $propertyID) . ')
		AND (pmrp_per IN (' . addSQLParam($paramsbudgetINCVOSQL, $pmcp_period) . '))
				AND (pmrp_year IN (' . addSQLParam($paramsbudgetINCVOSQL, $pmcp_year) . '))
				AND (pmrp_acc = ' . addSQLParam($paramsbudgetINCVOSQL, $account) . ')';
    // echo "9th BUDGET:" . $budgetINCVOSQL;

    $budgetINCVO_result = $dbh->executeScalar($budgetINCVOSQL, $paramsbudgetINCVOSQL);
    $budgetINCVO = $budgetINCVO_result;
    $budgetINCVOTotal = $budgetINCVOTotal + $budgetINCVO;
    $budgetINCVOTotal_display = formatting($budgetINCVOTotal);
    $budgetINCVO_display = formatting($budgetINCVO);

    $paramsbudgetINCVOYSQL = [];
    $budgetINCVOYSQL = 'SELECT COALESCE(SUM(pmrp_b_c_amt), 0) AS amount
		FROM pmrp_b_rev_per
		WHERE (pmrp_prop = ' . addSQLParam($paramsbudgetINCVOYSQL, $propertyID) . ')
		AND (pmrp_per >= 1)
		AND (pmrp_per <= ' . addSQLParam($paramsbudgetINCVOYSQL, $toPeriod) . ')
				  AND (pmrp_year IN (' . addSQLParam($paramsbudgetINCVOYSQL, $pmcp_year) . '))
				AND (pmrp_acc =' . addSQLParam($paramsbudgetINCVOYSQL, $account) . ')';
    // echo "10th BUDGET:" . $budgetINCVOYSQL;

    $budgetINCVOY_result = $dbh->executeScalar($budgetINCVOYSQL, $paramsbudgetINCVOYSQL);
    $budgetINCVOY = $budgetINCVOY_result;
    $budgetINCVOY_display = formatting($budgetINCVOY, 2);
    $budgetINCVOYTotal = $budgetINCVOYTotal + $budgetINCVOY;
    $budgetINCVOYTotal_display = formatting($budgetINCVOYTotal);

    $paramsbudgetINCVOYEARSQL = [];
    $budgetINCVOYEARSQL = 'SELECT COALESCE(SUM(pmrp_b_c_amt), 0) AS amount
		FROM pmrp_b_rev_per
		WHERE (pmrp_prop = ' . addSQLParam($paramsbudgetINCVOYEARSQL, $propertyID) . ')
		AND (pmrp_per BETWEEN 1 AND 12)AND (pmrp_year IN (' . addSQLParam($paramsbudgetINCVOYEARSQL, $pmcp_year) . '))
				AND (pmrp_acc = ' . addSQLParam($paramsbudgetINCVOYEARSQL, $account) . ')';
    // echo "11th BUDGET:" . $budgetINCVOYEARSQL;

    $budgetINCVOYEAR_result = $dbh->executeScalar($budgetINCVOYEARSQL, $paramsbudgetINCVOYEARSQL);
    $budgetINCVOYEAR = $budgetINCVOYEAR_result;
    $budgetINCVOYEAR_display = formatting($budgetINCVOYEAR, 2);
    $budgetINCVOYEARTotal = $budgetINCVOYEARTotal + $budgetINCVOYEAR;

    // -----------OPENING BALANCE FIGURES------------------------
    $openBalVO_income = $openBalOWN_result;
    // ----------------------------------------------------------
    // VO RECEIPTS
    $voSQL = "SELECT COALESCE(SUM(pmxd_alloc_amt), 0) amount, SUM (pmxd_tax_amt) as tax_amount
		FROM pmxd_ar_alloc
		WHERE (pmxd_acc = ?)
					   AND (pmxd_f_type = 'CSH')
			AND (pmxd_prop = ?)
			AND (pmxd_alloc_dt BETWEEN CONVERT(datetime, ?, 103) AND CONVERT(datetime, ?, 103))";

    $vo_result = $dbh->executeSingle($voSQL, [$account, $propertyID, $periodFrom, $periodTo]);

    $unallcashVOSQL = 'SELECT SUM (pmuc_net_amt) AS amount
			FROM pmuc_unall_csh
			WHERE (pmuc_prop = ?)
			AND (pmuc_acc = ?)
						AND (pmuc_rcpt_dt BETWEEN CONVERT(datetime, ?, 103) AND CONVERT(datetime, ?, 103))';

    $voUC_result = $dbh->executeScalar($unallcashVOSQL, [$account, $propertyID, $periodFrom, $periodTo]);
    $vo_income = $vo_result['amount'];
    $vo_tax_income = $vo_result['tax_amount'];
    $voUC_income = $voUC_result;

    $vo_income = $vo_income + $voUC_income - $vo_tax_income;
    $voinc = $voinc + $vo_income;
    $voPOS_income = $vo_income * (-1);
    $vo_display = formatting($voPOS_income, 2);

    // YTD3

    $voASQL = "SELECT COALESCE(SUM(pmxd_alloc_amt), 0) AS amount, COALESCE(SUM(pmxd_tax_amt), 0) as tax_amount
		FROM pmxd_ar_alloc
		WHERE (pmxd_acc = ?)
						AND (pmxd_f_type = 'CSH')
			AND (pmxd_prop = ?)
			AND (pmxd_alloc_dt BETWEEN CONVERT(datetime, ?, 103) AND CONVERT(datetime, ?, 103))";
    $voA_result = $dbh->executeSingle($voASQL, [$account, $propertyID, $startFinancialYear, $periodTo]);

    $paramsunallcashVOSQL = [];
    $unallcashVOSQL = 'SELECT COALESCE(SUM(pmuc_net_amt), 0) as amount
			FROM pmuc_unall_csh
			WHERE (pmuc_prop = ' . addSQLParam($paramsunallcashVOSQL, $propertyID) . ')
			AND (pmuc_acc = ' . addSQLParam($paramsunallcashVOSQL, $account) . ')
			AND (pmuc_period >= 1)
						AND (pmuc_period <= ' . addSQLParam($paramsunallcashVOSQL, $toPeriod) . ')
				AND (pmuc_year IN (' . addSQLParam($paramsunallcashVOSQL, $pmcp_year) . '))';

    $voUC_result = $dbh->executeScalar($unallcashVOSQL, $paramsunallcashVOSQL);
    $voUC_income = $voUC_result;
    $voA_income = $voA_result['amount'];
    $voA_tax_income = $voA_result['tax_amount'];
    $voA_income = $voA_income + $voUC_income - $voA_tax_income - $openBalVO_income;
    $voAPOS_income = $voA_income * (-1);
    $voA_display = formatting($voAPOS_income, 2);


    $variance = $voPOS_income - $budgetINCVO;
    $variance_display = formatting($variance);
    if ($budgetINCVO == 0) {
        $varianceP = 100;
        if ($variance < 0) {
            $varianceP = -100;
        }
        if ($voPOS_income == 0) {
            $varianceP = 0;
        }
    } else {
        $varianceP = $variance / $budgetINCVO * 100;
    }
    $varianceP_display = formatting($varianceP);


    $varianceY = $voAPOS_income - $budgetINCVOY;
    $varianceY_display = formatting($varianceY);
    if ($budgetINCVOY == 0) {
        $variancePY = 100;
        if ($varianceY < 0) {
            $variancePY = -100;
        }
        if ($voAPOS_income == 0) {
            $variancePY = 0;
        }
    } else {
        $variancePY = $varianceY / $budgetINCVOY * 100;
    }
    $variancePY_display = formatting($variancePY);

    if ($vo_income == 0 && $voA_income == 0 && $budgetINCVO == 0 && $budgetINCVOYEAR == 0) { // if ($vo_income == 0 && $voA_income == 0 && $budgetINCVO == 0)	//change above line on 14 Oct 08 RMB
        // DON NOT DISPLAY LINE
    } else {
        $excel->getActiveSheet()->getStyle("B{$xlsLine}:J{$xlsLine}")->getAlignment()->setHorizontal(
            Alignment::HORIZONTAL_RIGHT
        );
        $excel->getActiveSheet()->getStyle("A{$xlsLine}:J{$xlsLine}")->getFont()->setBold(false);
        $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 1), $account . ' ' . $account_name);
        $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 2), $vo_display);
        $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 3), $budgetINCVO_display);
        $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 4), $variance_display);
        $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 5), $varianceP_display);
        $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 6), $voA_display);
        $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 7), $budgetINCVOY_display);
        $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 8), $varianceY_display);
        $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 9), $variancePY_display);
        $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 10), $budgetINCVOYEAR_display);
        $xlsLine = $xlsLine + 1;
    }
}

$voincPOS = $voinc * (-1);

// $voincAPOS = $voincA * (-1);

$voincA = dbTotalIncomePerGroup($propertyID, $startFinancialYear, $periodTo, 'INCVO');
$voincomeA = $voincA['net_amount'];
$vo_takeon = take_on_balance($propertyID, 'INCVO', $startFinancialYear, $periodTo);
$voincAPOS = $voincomeA + $vo_takeon['amount'];


$total_vo = formatting($voincPOS, 2);
$total_voA = formatting($voincAPOS, 2);

$varianceT = $voincPOS - $budgetINCVOTotal;
$varianceT_display = formatting($varianceT);
if ($budgetINCVOTotal == 0) {
    $varianceTP = 100;
    if ($varianceT < 0) {
        $varianceTP = -100;
    }
    if ($voincPOS == 0) {
        $varianceTP = 0;
    }
} else {
    $varianceTP = $varianceT / $budgetINCVOTotal * 100;
}
$varianceTP_display = formatting($varianceTP);

$varianceTY = $voincAPOS - $budgetINCVOYTotal;
$varianceTY_display = formatting($varianceTY);
if ($budgetINCVOYTotal == 0) {
    $varianceTPY = 100;
    if ($varianceTY < 0) {
        $varianceTPY = -100;
    }
    if ($voincAPOS == 0) {
        $varianceTPY = 0;
    }
} else {
    $varianceTPY = $varianceTY / $budgetINCVOYTotal * 100;
}
$varianceTPY_display = formatting($varianceTPY);

$budgetINCVOYEARTotal_display = formatting($budgetINCVOYEARTotal);


if ($total_vo == '0.00' && $total_voA == '0.00' && $budgetINCVOTotal_display == '0.00' && $budgetINCVOYTotal_display == '0.00' && $budgetINCVOYEARTotal_display == '0.00') {
    $xlsLine = $xlsLine + 1;
} else {
    $excel->getActiveSheet()->getStyle("B{$xlsLine}:J{$xlsLine}")->getAlignment()->setHorizontal(
        Alignment::HORIZONTAL_RIGHT
    );
    $excel->getActiveSheet()->getStyle("A{$xlsLine}:J{$xlsLine}")->getFont()->setBold(true);
    $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 2), $total_vo);
    $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 3), $budgetINCVOTotal_display);
    $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 4), $varianceT_display);
    $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 5), $varianceTP_display);
    $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 6), $total_voA);
    $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 7), $budgetINCVOYTotal_display);
    $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 8), $varianceTY_display);
    $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 9), $varianceTPY_display);
    $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 10), $budgetINCVOYEARTotal_display);
    $xlsLine = $xlsLine + 1;


    if (strpos($total_vo, ')')) {
        $total_vo = str_replace(')', '', $total_vo);
        $total_vo = str_replace('(', '-', $total_vo);
    } else {

    }
    if (strpos($total_voA, ')')) {
        $total_voA = str_replace(')', '', $total_voA);
        $total_voA = str_replace('(', '-', $total_voA);
    } else {

    }
    if (strpos($budgetINCVOTotal_display, ')')) {
        $budgetINCVOTotal_display = str_replace(')', '', $budgetINCVOTotal_display);
        $budgetINCVOTotal_display = str_replace('(', '-', $budgetINCVOTotal_display);
    } else {

    }
    if (strpos($budgetINCVOYTotal_display, ')')) {
        $budgetINCVOYTotal_display = str_replace(')', '', $budgetINCVOYTotal_display);
        $budgetINCVOYTotal_display = str_replace('(', '-', $budgetINCVOYTotal_display);
    } else {

    }
    if (strpos($budgetINCVOYEARTotal_display, ')')) {
        $budgetINCVOYEARTotal_display = str_replace(')', '', $budgetINCVOYEARTotal_display);
        $budgetINCVOYEARTotal_display = str_replace('(', '-', $budgetINCVOYEARTotal_display);
    } else {

    }
    if (strpos($varianceT_display, ')')) {
        $varianceT_display = str_replace(')', '', $varianceT_display);
        $varianceT_display = str_replace('(', '-', $varianceT_display);
    } else {

    }
    if (strpos($varianceTP_display, ')')) {
        $varianceTP_display = str_replace(')', '', $varianceTP_display);
        $varianceTP_display = str_replace('(', '-', $varianceTP_display);
    } else {

    }
    if (strpos($varianceTY_display, ')')) {
        $varianceTY_display = str_replace(')', '', $varianceTY_display);
        $varianceTY_display = str_replace('(', '-', $varianceTY_display);
    } else {

    }
    if (strpos($varianceTPY_display, ')')) {
        $varianceTPY_display = str_replace(')', '', $varianceTPY_display);
        $varianceTPY_display = str_replace('(', '-', $varianceTPY_display);
    } else {

    }


    $_totalAllIncome_display = $_totalAllIncome_display + floatval(str_replace(',', '', $total_vo));
    $_totalAllIncomeA_display = $_totalAllIncomeA_display + floatval(str_replace(',', '', $total_voA));
    $_totalAllBudgetIncome_display = $_totalAllBudgetIncome_display + floatval(
        str_replace(',', '', $budgetINCVOTotal_display)
    );
    $_totalAllBudgetIncomeA_display = $_totalAllBudgetIncomeA_display + floatval(
        str_replace(',', '', $budgetINCVOYTotal_display)
    );
    $_totalAllBudgetIncomeYEAR_display = $_totalAllBudgetIncomeYEAR_display + floatval(
        str_replace(',', '', $budgetINCVOYEARTotal_display)
    );
    $_totalAllVarianceRECEIPTS_display = $_totalAllVarianceRECEIPTS_display + floatval(
        str_replace(',', '', $varianceT_display)
    );

    //    $_totalAllVP_display1=$_totalAllVP_display1+floatval(str_replace(',','',$varianceTP_display));
    $_totalAllVarianceY_display = $_totalAllVarianceY_display + floatval(str_replace(',', '', $varianceTY_display));
    //    $_totalAllVPY_display=$_totalAllVPY_display+floatval(str_replace(',','',$varianceTPY_display));


    //    if ($_totalAllBudgetIncome_display != 0)
    //        $_totalAllVP_display1 = round(($_totalAllVarianceRECEIPTS_display/$_totalAllBudgetIncome_display) * 100 ,2);

    //    if ($_totalAllBudgetIncomeA_display != 0)
    //        $_totalAllVPY_display = round(($_totalAllVarianceY_display/$_totalAllBudgetIncomeA_display) * 100 ,2);

}

// var_dump($_totalAllVarianceY_display, $varianceTY_display);


// --------------------------------------------------------------------------------------
// TOTAL CASH RECEIPTS
//


// $_totalAllVP_display1 = (floatval(str_replace(',','',$_totalAllVarianceRECEIPTS_display)) / floatval(str_replace(',','',$_totalAllBudgetIncome_display)))*100;
// $_totalAllVPY_display = (floatval(str_replace(',','',$_totalAllVarianceY_display)) / floatval(str_replace(',','',$_totalAllBudgetIncomeA_display)))*100;

if ($_totalAllBudgetIncome_display == 0) {
    $_totalAllVP_display1 = 100;
    if ($_totalAllVarianceRECEIPTS_display < 0) {
        $_totalAllVP_display1 = -100;
    }
    if ($_totalAllIncome_display == 0) {
        $_totalAllVP_display1 = 0;
    }
} else {
    $_totalAllVP_display1 = $_totalAllVarianceRECEIPTS_display / $_totalAllBudgetIncome_display * 100;
}

if ($_totalAllBudgetIncomeA_display == 0) {
    $_totalAllVPY_display = 100;
    if ($_totalAllVarianceY_display < 0) {
        $_totalAllVPY_display = -100;
    }
    if ($_totalAllIncomeA_display == 0) {
        $_totalAllVPY_display = 0;
    }
} else {
    $_totalAllVPY_display = $_totalAllVarianceY_display / $_totalAllBudgetIncomeA_display * 100;
}

$xlsLine = $xlsLine + 1;
$excel->getActiveSheet()->getStyle("B{$xlsLine}:J{$xlsLine}")->getAlignment()->setHorizontal(
    Alignment::HORIZONTAL_RIGHT
);
$excel->getActiveSheet()->getStyle("A{$xlsLine}:J{$xlsLine}")->getFont()->setBold(true);
$excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 1), 'Total Operating Cash Receipts');
$excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 2), formatting($_totalAllIncome_display));
$excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 3), formatting($_totalAllBudgetIncome_display));
$excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 4), formatting($_totalAllVarianceRECEIPTS_display));
$excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 5), formatting($_totalAllVP_display1));
$excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 6), formatting($_totalAllIncomeA_display));
$excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 7), formatting($_totalAllBudgetIncomeA_display));
$excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 8), formatting($_totalAllVarianceY_display));
$excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 9), formatting($_totalAllVPY_display));
$excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 10), formatting($_totalAllBudgetIncomeYEAR_display));
$xlsLine = $xlsLine + 1;

$totalAllBudgetIncome = $budgetINCOWNTotal + $budgetINCRECTotal + $budgetINCVOTotal;
$totalAllBudgetIncome_display = formatting($totalAllBudgetIncome, 2);


// BUDGET FIGURES
$paramsbudgetINCOWNSQL = [];
$budgetINCOWNSQL = 'SELECT COALESCE(SUM(pmrp_b_c_amt), 0) AS amount
		FROM pmrp_b_rev_per
		WHERE (pmrp_prop = ' . addSQLParam($paramsbudgetINCOWNSQL, $propertyID) . ')
		AND (pmrp_per IN (' . addSQLParam($paramsbudgetINCOWNSQL, $pmcp_period) . '))
				AND (pmrp_year IN (' . addSQLParam($paramsbudgetINCOWNSQL, $pmcp_year) . "))
				AND (pmrp_acc IN (SELECT pmcg_acc
									FROM pmcg_chart_grp
									WHERE (pmcg_grp = 'TRACC2')
									AND (pmcg_subgrp = 'INCOWN')))";
// echo "12th BUDGET:" . $budgetINCOWNSQL;

$budgetINCOWN_result = $dbh->executeScalar($budgetINCOWNSQL, $paramsbudgetINCOWNSQL);
$budgetINCOWN = $budgetINCOWN_result;
$budgetINCOWN = $budgetINCOWN;
$budgetINCOWN_display = formatting($budgetINCOWN, 2);

$paramsbudgetINCDRSQL = [];
$budgetINCDRSQL = 'SELECT COALESCE(SUM(pmrp_b_c_amt), 0) AS amount
		FROM pmrp_b_rev_per
		WHERE (pmrp_prop = ' . addSQLParam($paramsbudgetINCDRSQL, $propertyID) . ')
		AND (pmrp_per IN (' . addSQLParam($paramsbudgetINCDRSQL, $pmcp_period) . '))
				AND (pmrp_year IN (' . addSQLParam($paramsbudgetINCDRSQL, $pmcp_year) . "))
				AND (pmrp_acc IN (SELECT pmcg_acc
									FROM pmcg_chart_grp
									WHERE (pmcg_grp = 'TRACC2')
									AND (pmcg_subgrp = 'INCDR')))";
// echo "13th BUDGET:" . $budgetINCDRSQL;

$budgetINCDR_result = $dbh->executeScalar($budgetINCDRSQL, $paramsbudgetINCDRSQL);
$budgetINCDR = $budgetINCDR_result;
$budgetINCDR_display = formatting($budgetINCDR, 2);

$paramsbudgetINCVOSQL = [];
$budgetINCVOSQL = 'SELECT COALESCE(SUM(pmrp_b_c_amt), 0) AS amount
		FROM pmrp_b_rev_per
		WHERE (pmrp_prop = ' . addSQLParam($paramsbudgetINCVOSQL, $propertyID) . ')
		AND (pmrp_per IN (' . addSQLParam($paramsbudgetINCVOSQL, $pmcp_period) . '))
				AND (pmrp_year IN (' . addSQLParam($paramsbudgetINCVOSQL, $pmcp_year) . "))
				AND (pmrp_acc IN (SELECT pmcg_acc
									FROM pmcg_chart_grp
									WHERE (pmcg_grp = 'TRACC2')
									AND (pmcg_subgrp = 'INCVO')))";
// echo "14th BUDGET:" . $budgetINCVOSQL;

$budgetINCVO_result = $dbh->executeScalar($budgetINCVOSQL, $paramsbudgetINCVOSQL);
$budgetINCVO = $budgetINCVO_result;
$budgetINCVO_display = formatting($budgetINCVO, 2);

// VARIANCE
$incOWNVar = $ownerincomePOS - $budgetINCOWN;
$incDRVar = $recincPOS - $budgetINCDR;
$voincVar = $voincPOS - $budgetINCVO;

// DISPLAY VARIANCE
$incOWNVar_display = formatting($incOWNVar, 2);
$incDRVar_display = formatting($incDRVar, 2);
$voincVar_display = formatting($voincVar, 2);

// VAR %

if ($budgetINCOWN == 0) {
    if ($incOWNVar == 0) {
        $incOWNVP = 0;
    } else {
        if ($incOWNVar < 0) {
            $incOWNVP = -100;
        } else {
            $incOWNVP = 100;
        }
    }
} else {
    if ($incOWNVar == 0) {
        $incOWNVP = 0;
    } else {
        $incOWNVP = $incOWNVar / $budgetINCOWN * 100;
    }
}

if ($budgetINCDR == 0) {
    if ($incDRVar == 0) {
        $incDRVP = 0;
    } else {
        if ($incDRVar < 0) {
            $incDRVP = -100;
        } else {
            $incDRVP = 100;
        }
    }
} else {
    if ($incDRVar == 0) {
        $incDRVP = 0;
    } else {
        $incDRVP = $incDRVar / $budgetINCDR * 100;
    }
}


if ($budgetINCVO == 0) {
    if ($voincVar == 0) {
        $incVOVP = 0;
    } else {
        if ($voincVar < 0) {
            $incVOVP = -100;
        } else {
            $incVOVP = 100;
        }
    }
} else {
    if ($voincVar == 0) {
        $incVOVP = 0;
    } else {
        $incVOVP = $voincVar / $budgetINCVO * 100;
    }
}


// DISPLAY VAR %
$incOWNVP_display = formatting($incOWNVP, 0);
$incDRVP_display = formatting($incDRVP, 0);
$incVOVP_display = formatting($incVOVP, 0);

// TOTALS DISPLAY

$totalAllBudgetIncome = $budgetINCOWNTotal + $budgetINCRECTotal + $budgetINCVOTotal;
// $totalAllBudgetIncome = $budgetINCOWN + $budgetINCDR + $budgetINCVO;
// $totalAllBudgetIncome1 = $totalAllBudgetIncome;
$totalAllBudgetIncome_display = formatting($totalAllBudgetIncome, 2);
$totalAllVariance = $totalAllIncomePOS - $totalAllBudgetIncome;
$totalAllVarianceIncome = $totalAllVariance;
$totalAllVariance_display = formatting($totalAllVariance, 2);


if ($totalAllBudgetIncome == 0) {
    if ($totalAllVariance == 0) {
        $totalAllVP = 0;
    } else {
        if ($totalAllVariance < 0) {
            $totalAllVP = -100;
        } else {
            $totalAllVP = 100;
        }
    }
} else {
    if ($totalAllVariance == 0) {
        $totalAllVP = 0;
    } else {
        $totalAllVP = $totalAllVariance / $totalAllBudgetIncome * 100;
    }
}


$totalAllVP_display = formatting($totalAllVP, 0);

$excel->createSheet(1);
$excel->setActiveSheetIndex(1); // Activate it
$excel->getActiveSheet()->setTitle('Operating Cash Payments Summary');

$excel->getActiveSheet()->getStyle('A1:J1')->getFont()->setBold(true);
$excel->getActiveSheet()->SetCellValue(cellReference(1, 1), 'Operating Cash Payments Summary');

$excel->getActiveSheet()->SetCellValue(cellReference(2, 1), 'Period From:');
$excel->getActiveSheet()->SetCellValue(cellReference(2, 2), "$periodFrom To: $periodTo");

$excel->getActiveSheet()->SetCellValue(cellReference(4, 1), 'Owner:');
$excel->getActiveSheet()->SetCellValue(cellReference(4, 2), "$client");
$excel->getActiveSheet()->SetCellValue(cellReference(5, 1), 'Property:');
$excel->getActiveSheet()->SetCellValue(cellReference(5, 2), $propertyName . " [$propertyID]");
$excel->getActiveSheet()->SetCellValue(cellReference(6, 1), 'Report For:');
$excel->getActiveSheet()->SetCellValue(cellReference(6, 2), $periodDescription);

$excel->getActiveSheet()->getStyle('A9:J9')->getFont()->setBold(true);
$excel->getActiveSheet()->SetCellValue(cellReference(9, 2), 'Current Month');
$excel->getActiveSheet()->SetCellValue(cellReference(9, 6), 'Year to Date');
$excel->getActiveSheet()->SetCellValue(
    cellReference(9, 10),
    'Budget Year ' . $_SESSION['country_default']['currency_symbol']
);
$excel->getActiveSheet()->getStyle('A10:J10')->getFont()->setBold(true);
$excel->getActiveSheet()->SetCellValue(
    cellReference(10, 2),
    'Actual ' . $_SESSION['country_default']['currency_symbol']
);
$excel->getActiveSheet()->SetCellValue(
    cellReference(10, 3),
    'Budget ' . $_SESSION['country_default']['currency_symbol']
);
$excel->getActiveSheet()->SetCellValue(
    cellReference(10, 4),
    'Variance ' . $_SESSION['country_default']['currency_symbol']
);
$excel->getActiveSheet()->SetCellValue(cellReference(10, 5), 'Var %');
$excel->getActiveSheet()->SetCellValue(
    cellReference(10, 6),
    'Actual ' . $_SESSION['country_default']['currency_symbol']
);
$excel->getActiveSheet()->SetCellValue(
    cellReference(10, 7),
    'Budget ' . $_SESSION['country_default']['currency_symbol']
);
$excel->getActiveSheet()->SetCellValue(
    cellReference(10, 8),
    'Variance ' . $_SESSION['country_default']['currency_symbol']
);
$excel->getActiveSheet()->SetCellValue(cellReference(10, 9), 'Var %');

foreach (range('A', $excel->getActiveSheet()->getHighestDataColumn()) as $col) {
    $excel
        ->getActiveSheet()
        ->getColumnDimension($col)
        ->setAutoSize(true);
}


$cashReceiptsPages = $page;

// --------------------------------------------------------------------------
// OWNER PAYMENTS
//
// ACCOUNTS FOR INDIV CALCULATIONS
$accSQL = "SELECT DISTINCT pmxc_acc
		FROM pmxc_ap_alloc
		WHERE fund IS NULL AND (pmxc_prop = ?)
		AND (pmxc_alloc_dt <= CONVERT(datetime, ?, 103))
		AND (pmxc_f_type = 'PAY')
				AND (pmxc_acc IN
						  (SELECT pmcg_acc
							FROM pmcg_chart_grp
							WHERE (pmcg_grp = 'TRACC2')
							AND pmcg_subgrp = 'EXPOWN'))";

$accSQL_result = $dbh->executeScalars($accSQL, [$propertyID, $periodTo]);


$unAllocCash = "SELECT DISTINCT pmuc_acc
		FROM pmuc_unall_csh
		WHERE (pmuc_prop = ?)
		AND (pmuc_rcpt_dt < CONVERT(datetime, ?, 103))
		AND (pmuc_acc IN
						  (SELECT pmcg_acc
							FROM pmcg_chart_grp
							WHERE (pmcg_grp = 'TRACC2')
							AND pmcg_subgrp = 'EXPOWN'))";
$cash_result = $dbh->executeScalars($unAllocCash, [$propertyID, $periodTo]);


$paramsbudgetACCSQL = [];
$budgetACCSQL = 'SELECT DISTINCT pmep_exp_acc AS account
FROM         pmep_b_exp_per
WHERE     (pmep_prop = ' . addSQLParam($paramsbudgetACCSQL, $propertyID) . ')
    AND (pmep_per IN (' . addSQLParam($paramsbudgetACCSQL, $pmcp_period) . '))
    AND (pmep_year IN (' . addSQLParam($paramsbudgetACCSQL, $pmcp_year) . "))
    AND (pmep_exp_acc IN
						  (SELECT pmcg_acc
							FROM pmcg_chart_grp
							WHERE (pmcg_grp = 'TRACC2') AND pmcg_subgrp = 'EXPOWN'))";
// echo "15th BUDGET:" . $budgetACCSQL;
$budget_result = $dbh->executeScalars($budgetACCSQL, $paramsbudgetACCSQL);

$opBAL_accSQL = "SELECT DISTINCT pmpb_acc as account
		FROM pmpb_p_bal
		WHERE (pmpb_prop = ?)
		AND (pmpb_date BETWEEN CONVERT(datetime, ?, 103) AND CONVERT(datetime, ?, 103))
		AND (pmpb_acc IN (SELECT pmcg_acc
									FROM pmcg_chart_grp
									WHERE (pmcg_grp = 'TRACC2')
									AND (pmcg_subgrp = 'EXPOWN')))
								  AND (pmpb_acc <> '0703')";
$opBALacc_result = $dbh->executeScalars($opBAL_accSQL, [$propertyID, $startFinancialYear, $periodTo]);


$ownerexp_display = '0.00';
$acc = [];
$acc = array_merge($accSQL_result, $cash_result, $opBALacc_result, $budget_result);

$merged = array_unique($acc);
asort($merged);

$xlsLine = 11;
$excel->getActiveSheet()->getStyle("A{$xlsLine}:J{$xlsLine}")->getFont()->setBold(true);
$excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 1), 'Owner Expenditure');
$xlsLine = $xlsLine + 1;


$budgetEXPOWNTotal = 0;
$budgetEXPOWNYTotal = 0;
$budgetEXPOWNYEARTotal = 0;

$subGroup = '(' . implode('),(', $merged) . ')';
if ($subGroup != '()') {
    $subGroupQuery = "SELECT  accountCode,pmrcg_subgrp , pmas_desc as description  FROM (VALUES {$subGroup}  )
    AS accounts(accountCode)
	join pmrcg_chart_grp on  accountCode between pmrcg_acc and pmrcg_acc_to and pmrcg_chart_grp.pmrcg_grp='TRACC3'
	join pmas_acc_subgrp on pmrcg_chart_grp.pmrcg_grp = pmas_acc_subgrp.pmas_grp and pmas_subgrp = pmrcg_subgrp
	order by pmas_desc,accountCode";
    $subGroupRun = $dbh->executeSet($subGroupQuery);
    $subGroupArray = [];
    $subGroupChecker = [];
    foreach ($subGroupRun as $v) {
        $subGroupArray[$v['accountCode']] = ucwords(strtolower($v['description']));
    }
}
$subGroupVo_display = [];
$subGroupBudgetEXPVO_display = [];
$subGroupVoA_display = [];
$subGroupBudgetEXPVOY_display = [];
$subGroupBudgetEXPVOYEAR_display = [];
foreach ($subGroupArray as $key => $account) {
    $account = $key;
    // Get Account Name
    $accSQL = 'SELECT pmca_name FROM pmca_chart WHERE pmca_code = ?';
    $acc_result = $dbh->executeScalar($accSQL, [$account]);
    $account_name = $acc_result;
    $account_name = (strlen($account_name) < $limit) ? $account_name : substr($account_name, 0, $limit) . ' ...';


    // OWNER PAYMENTS
    $ownerexpSQL = "SELECT COALESCE(SUM(pmxc_alloc_amt), 0) amount, SUM (pmxc_tax_amt) as tax_amount
		FROM pmxc_ap_alloc
		WHERE (pmxc_acc = ?)
						 AND (pmxc_f_type = 'PAY')
			AND (pmxc_prop = ?)
			AND (pmxc_alloc_dt BETWEEN CONVERT(datetime, ?, 103) AND CONVERT(datetime, ?, 103))";

    // $unallcashOISQL = "SELECT SUM (pmuc_amt) AS amount
    //		FROM pmuc_unall_csh
    //	WHERE (pmuc_prop = '$propertyID')
    // AND (pmuc_acc = '$account')
    //          AND (pmuc_rcpt_dt BETWEEN CONVERT(datetime, '{$periodFrom}', 103) AND CONVERT(datetime, '{$periodTo}', 103))";


    $ownerexp_result = $dbh->executeSingle($ownerexpSQL, [$account, $propertyID, $periodFrom, $periodTo]);

    $unallcashOISQL = 'SELECT SUM (pmuc_net_amt) AS amount
			FROM pmuc_unall_csh
			WHERE (pmuc_prop = ?)
			AND (pmuc_acc = ?)
						AND (pmuc_rcpt_dt BETWEEN CONVERT(datetime, ?, 103) AND CONVERT(datetime, ?, 103))';
    $ownerexpUC_result = $dbh->executeScalar($unallcashOISQL, [$propertyID, $account, $periodFrom, $periodTo]);
    $owner_expenses = $ownerexp_result['amount'];
    $owner_expenses_tax = $ownerexp_result['tax_amount'];

    $owner_expenses = $owner_expenses + $owner_expenses_tax;
    $ownerPOS_expenses = $owner_expenses * (-1);
    $owner_display = formatting($ownerPOS_expenses, 2);
    $ownerexp_display = $ownerexp_display + $ownerPOS_expenses;

    // YTD4

    $ownerexpASQL = "SELECT COALESCE(SUM(pmxc_alloc_amt), 0) AS amount, COALESCE(SUM(pmxc_tax_amt), 0) as tax_amount
		FROM pmxc_ap_alloc
		WHERE (pmxc_acc = ?)
						AND (pmxc_f_type = 'PAY')
						AND (pmxc_prop = ?)
			AND (pmxc_alloc_dt BETWEEN CONVERT(datetime, ?, 103) AND CONVERT(datetime, ?, 103))";
    $ownerexpA_result = $dbh->executeSingle(
        $ownerexpASQL,
        [$account, $propertyID, $startFinancialYear, $periodTo]
    );

    // AND (pmxc_alloc_dt BETWEEN CONVERT(datetime, '{$startFinancialYear}', 103) AND CONVERT(datetime, '{$periodTo}', 103))";

    // THIS QUERY EFFECTS THE CURRENT MONTH AREA FOR OWNER EXPENDITURE


    $paramsunallcashOISQL = [];
    $unallcashOISQL = 'SELECT COALESCE(SUM(pmuc_net_amt), 0) as amount
			FROM pmuc_unall_csh
			WHERE (pmuc_prop = ' . addSQLParam($paramsunallcashOISQL, $propertyID) . ')
			AND (pmuc_acc = ' . addSQLParam($paramsunallcashOISQL, $account) . ')
			AND (pmuc_period >= 1)
						AND (pmuc_period <= ' . addSQLParam($paramsunallcashOISQL, $toPeriod) . ')
				AND (pmuc_year IN (' . addSQLParam($paramsunallcashOISQL, $pmcp_year) . '))';

    $ownerexpUC_result = $dbh->executeScalar($unallcashOISQL, $paramsunallcashOISQL);
    $ownerUC_expenses = $ownerexpUC_result;


    // OPENING BALANCE FOR EACH ACCOUNT
    $openBalOWNSQL = 'SELECT COALESCE(SUM(pmpb_bal), 0) as amount
		FROM pmpb_p_bal
		WHERE (pmpb_prop = ?)
		AND (pmpb_date BETWEEN CONVERT(datetime, ?, 103) AND CONVERT(datetime, ?, 103))
		AND (pmpb_acc = ?)';
    $openBalOWN_result = $dbh->executeScalar(
        $openBalOWNSQL,
        [$propertyID, $startFinancialYear, $periodTo, $account]
    );

    // -----------OPENING BALANCE FIGURES------------------------
    $openBalOWN_expenses = $openBalOWN_result;
    // ----------------------------------------------------------

    // -----------BUDGET FIGURES ---------------------------------
    $paramsbudgetEXPOWNSQL = [];
    $budgetEXPOWNSQL = 'SELECT COALESCE(SUM(pmep_b_c_amt), 0) AS amount
		FROM pmep_b_exp_per
		WHERE (pmep_prop = ' . addSQLParam($paramsbudgetEXPOWNSQL, $propertyID) . ')
		AND (pmep_per IN (' . addSQLParam($paramsbudgetEXPOWNSQL, $pmcp_period) . '))
				AND (pmep_year IN (' . addSQLParam($paramsbudgetEXPOWNSQL, $pmcp_year) . '))
				AND (pmep_exp_acc = ' . addSQLParam($paramsbudgetEXPOWNSQL, $account) . ')';
    // echo "16th BUDGET:" . $budgetEXPOWNSQL;

    $budgetEXPOWN_result = $dbh->executeScalar($budgetEXPOWNSQL, $paramsbudgetEXPOWNSQL);
    $budgetEXPOWN = $budgetEXPOWN_result;
    $budgetEXPOWNTotal = $budgetEXPOWNTotal + $budgetEXPOWN;
    $budgetEXPOWNTotal_display = formatting($budgetEXPOWNTotal);
    $budgetEXPOWN_display = formatting($budgetEXPOWN);

    $paramsbudgetEXPOWNYSQL = [];
    $budgetEXPOWNYSQL = 'SELECT COALESCE(SUM(pmep_b_c_amt), 0) AS amount
		FROM pmep_b_exp_per
		WHERE (pmep_prop = ' . addSQLParam($paramsbudgetEXPOWNYSQL, $propertyID) . ')
		AND (pmep_per >= 1)
		AND (pmep_per <= ' . addSQLParam($paramsbudgetEXPOWNYSQL, $toPeriod) . ')
				  AND (pmep_year IN (' . addSQLParam($paramsbudgetEXPOWNYSQL, $pmcp_year) . '))
				AND (pmep_exp_acc =' . addSQLParam($paramsbudgetEXPOWNYSQL, $account) . ')';
    // echo "17th BUDGET:" . $budgetEXPOWNYSQL;

    $budgetEXPOWNY_result = $dbh->executeScalar($budgetEXPOWNYSQL, $paramsbudgetEXPOWNYSQL);
    $budgetEXPOWNY = $budgetEXPOWNY_result;
    $budgetEXPOWNY_display = formatting($budgetEXPOWNY, 2);
    $budgetEXPOWNYTotal = $budgetEXPOWNYTotal + $budgetEXPOWNY;
    $budgetEXPOWNYTotal_display = formatting($budgetEXPOWNYTotal);

    $paramsbudgetEXPOWNYEARSQL = [];
    $budgetEXPOWNYEARSQL = 'SELECT COALESCE(SUM(pmep_b_c_amt), 0) AS amount
		FROM pmep_b_exp_per
		WHERE (pmep_prop = ' . addSQLParam($paramsbudgetEXPOWNYEARSQL, $propertyID) . ')
		AND (pmep_per BETWEEN 1 AND 12)AND (pmep_year IN (' . addSQLParam($paramsbudgetEXPOWNYEARSQL, $pmcp_year) . '))
				AND (pmep_exp_acc = ' . addSQLParam($paramsbudgetEXPOWNYEARSQL, $account) . ')';
    // echo "18th BUDGET:" . $budgetEXPOWNYEARSQL;

    $budgetEXPOWNYEAR_result = $dbh->executeScalar($budgetEXPOWNYEARSQL, $paramsbudgetEXPOWNYEARSQL);
    $budgetEXPOWNYEAR = $budgetEXPOWNYEAR_result;
    $budgetEXPOWNYEAR_display = formatting($budgetEXPOWNYEAR, 2);
    $budgetEXPOWNYEARTotal = $budgetEXPOWNYEARTotal + $budgetEXPOWNYEAR;

    // OPENING BALANCE FOR EACH ACCOUNT
    $openBalOWNSQL = 'SELECT COALESCE(SUM(pmpb_bal), 0) as amount
		FROM pmpb_p_bal
		WHERE (pmpb_prop = ?)
		AND (pmpb_date BETWEEN CONVERT(datetime, ?, 103) AND CONVERT(datetime, ?, 103))
		AND (pmpb_acc = ?)';
    $openBalOWN_result = $dbh->executeScalar(
        $openBalOWNSQL,
        [$propertyID, $startFinancialYear, $periodTo, $account]
    );

    // -----------OPENING BALANCE FIGURES------------------------
    $openBalOWN_exp = $openBalOWN_result;
    // ----------------------------------------------------------


    $ownerA_expenses = $ownerexpA_result['amount'];
    $ownerA_expenses_tax = $ownerexpA_result['tax_amount'];
    $ownerA_expenses = $ownerA_expenses + $ownerA_expenses_tax - $openBalOWN_exp;
    $ownerAPOS_expenses = $ownerA_expenses * (-1);
    $ownerA_display = formatting($ownerAPOS_expenses, 2);

    $variance = $budgetEXPOWN - $ownerPOS_expenses;
    $variance_display = formatting($variance);


    if ($budgetEXPOWN == 0) {
        if ($ownerPOS_expenses == 0) {
            $varianceP = 0;
        } else {
            $varianceP = -100;
        }
    } else {
        $varianceP = $variance / $budgetEXPOWN * 100;
    }
    $varianceP_display = formatting($varianceP);


    $varianceY = $budgetEXPOWNY - $ownerAPOS_expenses;
    $varianceY_display = formatting($varianceY);
    if ($budgetEXPOWNY == 0) {
        if ($ownerAPOS_expenses == 0) {
            $variancePY = 0;
        } else {
            $variancePY = -100;
        }
    } else {
        $variancePY = $varianceY / $budgetEXPOWNY * 100;
    }
    $variancePY_display = formatting($variancePY);
    if ($owner_display == '0.00' && $ownerA_display == '0.00' && $budgetEXPOWN_display == '0.00' && $budgetEXPOWNY_display == '0.00' && $budgetEXPOWNYEAR_display == '0.00') {
        // DO NOT DISPLAY LINE
    } else {
        if (! in_array(
            $subGroupArray[$account],
            $subGroupChecker
        ) && isset($subGroupArray[$account]) && $subGroupArray[$account]) {
            if (! empty($subGroupChecker)) {
                $varianceC = array_sum($subGroupBudgetEXPVO_display) - array_sum($subGroupVo_display);
                $varianceCP = array_sum($subGroupBudgetEXPVO_display) ? $varianceC / array_sum(
                    $subGroupBudgetEXPVO_display
                ) * 100 : (array_sum($subGroupVo_display) != 0 ? -100 : 0);
                $varianceY = array_sum($subGroupBudgetEXPVOY_display) - array_sum($subGroupVoA_display);
                $varianceYP = array_sum($subGroupBudgetEXPVOY_display) ? $varianceY / array_sum(
                    $subGroupBudgetEXPVOY_display
                ) * 100 : (array_sum($subGroupVoA_display) != 0 ? -100 : 0);

                $excel->getActiveSheet()->getStyle("B{$xlsLine}:J{$xlsLine}")->getAlignment()->setHorizontal(
                    Alignment::HORIZONTAL_RIGHT
                );
                $excel->getActiveSheet()->getStyle("A{$xlsLine}:J{$xlsLine}")->getFont()->setBold(true);
                $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 1), 'Sub Total');
                $excel->getActiveSheet()->SetCellValue(
                    cellReference($xlsLine, 2),
                    formatting(array_sum($subGroupVo_display))
                );
                $excel->getActiveSheet()->SetCellValue(
                    cellReference($xlsLine, 3),
                    formatting(array_sum($subGroupBudgetEXPVO_display))
                );
                $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 4), formatting(($varianceC)));
                $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 5), formatting(($varianceCP)));
                $excel->getActiveSheet()->SetCellValue(
                    cellReference($xlsLine, 6),
                    formatting(array_sum($subGroupVoA_display))
                );
                $excel->getActiveSheet()->SetCellValue(
                    cellReference($xlsLine, 7),
                    formatting(array_sum($subGroupBudgetEXPVOY_display))
                );
                $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 8), formatting(($varianceY)));
                $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 9), formatting(($varianceYP)));
                $excel->getActiveSheet()->SetCellValue(
                    cellReference($xlsLine, 10),
                    formatting(array_sum($subGroupBudgetEXPVOYEAR_display))
                );
                $xlsLine = $xlsLine + 1;

                $subGroupVo_display = [];
                $subGroupBudgetEXPVO_display = [];
                $subGroupVoA_display = [];
                $subGroupBudgetEXPVOY_display = [];
                $subGroupBudgetEXPVOYEAR_display = [];
            }

            $excel->getActiveSheet()->getStyle("A{$xlsLine}:J{$xlsLine}")->getFont()->setBold(true);
            $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 1), $subGroupArray[$account]);
            $xlsLine = $xlsLine + 1;

            $subGroupChecker[] = $subGroupArray[$account];
        }

        $subGroupVo_display[] = $ownerPOS_expenses;
        $subGroupBudgetEXPVO_display[] = $budgetEXPOWN;
        $subGroupVoA_display[] = $ownerAPOS_expenses;
        $subGroupBudgetEXPVOY_display[] = $budgetEXPOWNY;
        $subGroupBudgetEXPVOYEAR_display[] = $budgetEXPOWNYEAR;

        $excel->getActiveSheet()->getStyle("B{$xlsLine}:J{$xlsLine}")->getAlignment()->setHorizontal(
            Alignment::HORIZONTAL_RIGHT
        );
        $excel->getActiveSheet()->getStyle("A{$xlsLine}:J{$xlsLine}")->getFont()->setBold(false);
        $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 1), $account . ' ' . $account_name);
        $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 2), $owner_display);
        $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 3), $budgetEXPOWN_display);
        $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 4), $variance_display);
        $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 5), $varianceP_display);
        $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 6), $ownerA_display);
        $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 7), $budgetEXPOWNY_display);
        $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 8), $varianceY_display);
        $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 9), $variancePY_display);
        $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 10), $budgetEXPOWNYEAR_display);
        $xlsLine = $xlsLine + 1;
    }
}

if (! empty($subGroupChecker)) {
    $varianceC = array_sum($subGroupBudgetEXPVO_display) - array_sum($subGroupVo_display);
    $varianceCP = array_sum($subGroupBudgetEXPVO_display) ? $varianceC / array_sum(
        $subGroupBudgetEXPVO_display
    ) * 100 : (array_sum($subGroupVo_display) != 0 ? -100 : 0);
    $varianceY = array_sum($subGroupBudgetEXPVOY_display) - array_sum($subGroupVoA_display);
    $varianceYP = array_sum($subGroupBudgetEXPVOY_display) ? $varianceY / array_sum(
        $subGroupBudgetEXPVOY_display
    ) * 100 : (array_sum($subGroupVoA_display) != 0 ? -100 : 0);

    $excel->getActiveSheet()->getStyle("B{$xlsLine}:J{$xlsLine}")->getAlignment()->setHorizontal(
        Alignment::HORIZONTAL_RIGHT
    );
    $excel->getActiveSheet()->getStyle("A{$xlsLine}:J{$xlsLine}")->getFont()->setBold(true);
    $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 1), 'Sub Total');
    $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 2), formatting(array_sum($subGroupVo_display)));
    $excel->getActiveSheet()->SetCellValue(
        cellReference($xlsLine, 3),
        formatting(array_sum($subGroupBudgetEXPVO_display))
    );
    $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 4), formatting(($varianceC)));
    $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 5), formatting(($varianceCP)));
    $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 6), formatting(array_sum($subGroupVoA_display)));
    $excel->getActiveSheet()->SetCellValue(
        cellReference($xlsLine, 7),
        formatting(array_sum($subGroupBudgetEXPVOY_display))
    );
    $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 8), formatting(($varianceY)));
    $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 9), formatting(($varianceYP)));
    $excel->getActiveSheet()->SetCellValue(
        cellReference($xlsLine, 10),
        formatting(array_sum($subGroupBudgetEXPVOYEAR_display))
    );
    $xlsLine = $xlsLine + 1;

    $subGroupVo_display = [];
    $subGroupBudgetEXPVO_display = [];
    $subGroupVoA_display = [];
    $subGroupBudgetEXPVOY_display = [];
    $subGroupBudgetEXPVOYEAR_display = [];
}


$gstSQL = "SELECT COALESCE(SUM(pmxc_tax_amt), 0) amount
		FROM pmxc_ap_alloc
		WHERE (pmxc_f_type = 'PAY')
		AND (pmxc_prop = ?)
		AND (pmxc_acc<>NULL)
		AND (pmxc_alloc_dt BETWEEN CONVERT(datetime, ?, 103) AND CONVERT(datetime, ?, 103))
		AND (pmxc_acc IN (SELECT pmcg_acc
									FROM pmcg_chart_grp
									WHERE (pmcg_grp = 'TRACC2')
									AND pmcg_subgrp = 'EXPDR' OR pmcg_subgrp = 'EXPOWN' OR pmcg_subgrp = 'EXPVO'))";
$gst_result = $dbh->executeScalar($gstSQL, [$propertyID, $periodFrom, $periodTo]);
$gst = $gst_result;
$gst_display = formatting($gst, 2);


// OPENING BALANCE FOR EACH ACCOUNT
$ownerexp_takeon = take_on_balance($propertyID, 'EXPOWN', $startFinancialYear, $periodTo);
$voexp_takeon = take_on_balance($propertyID, 'EXPVO', $startFinancialYear, $periodTo);
$recexp_takeon = take_on_balance($propertyID, 'EXPDR', $startFinancialYear, $periodTo);


/*
$openBalOWNSQL = "SELECT SUM(pmpb_bal) as amount
        FROM pmpb_p_bal
        WHERE (pmpb_prop = '$propertyID')
        AND (pmpb_date BETWEEN CONVERT(datetime, '{$startFinancialYear}', 103) AND CONVERT(datetime, '{$periodTo}', 103))
        AND (pmpb_acc IN (SELECT pmcg_acc
                                    FROM pmcg_chart_grp
                                    WHERE (pmcg_grp = 'TRACC2')
                                    AND pmcg_subgrp = 'EXPDR' OR pmcg_subgrp = 'EXPOWN' OR pmcg_subgrp = 'EXPVO'))";
$openBalOWN_result = $dbh->executeScalar($openBalOWNSQL);
*/

// -----------OPENING BALANCE FIGURES------------------------
$openBalGST_exp = $openBalOWN_result;
// ----------------------------------------------------------


$gstASQL = "SELECT COALESCE(SUM(pmxc_tax_amt), 0) amount
		FROM pmxc_ap_alloc
		WHERE (pmxc_f_type = 'PAY')
		AND (pmxc_prop = ?)
		AND (pmxc_acc<>NULL)
		AND (pmxc_alloc_dt BETWEEN CONVERT(datetime, ?, 103) AND CONVERT(datetime, ?, 103))
		AND (pmxc_acc IN (SELECT pmcg_acc
									FROM pmcg_chart_grp
									WHERE (pmcg_grp = 'TRACC2')
									AND pmcg_subgrp = 'EXPDR' OR pmcg_subgrp = 'EXPOWN' OR pmcg_subgrp = 'EXPVO'))";
$gstA_result = $dbh->executeScalar($gstASQL, [$propertyID, $startFinancialYear, $periodTo]);
$gstA = $gstA_result;
$gstA = $gstA + $openBalGST_exp;
$gstA_display = formatting($gstA, 2);


$ownerexpYear = totalexpenses($propertyID, $startFinancialYear, $periodTo, 'EXPOWN');
$ownerexpAPOS = $ownerexpYear['net_amount'] + $ownerexp_takeon['amount'];

// Formatting
$ownerexpA_display = formatting($ownerexpAPOS, 2);

if ($ownerexp_display == '0.00' && $ownerexpA_display == '0.00' && $budgetEXPOWNTotal == 0 && $budgetEXPOWNYTotal == 0 && $budgetEXPOWNYEARTotal == 0) {
} else {
    $budgetEXPOWNTotal_display = formatting($budgetEXPOWNTotal, 2);
    $budgetEXPOWNYTotal_display = formatting($budgetEXPOWNYTotal, 2);
    $budgetEXPOWNYEARTotal_display = formatting($budgetEXPOWNYEARTotal, 2);
    $expOWNVarTotal = $budgetEXPOWNTotal - $ownerexpPOS;
    $expOWNVarYTotal = $budgetEXPOWNYTotal - $ownerexpAPOS;
    $expOWNVarTotal_display = formatting($expOWNVarTotal, 2);
    $expOWNVarYTotal_display = formatting($expOWNVarYTotal, 2);
    if ($budgetEXPOWNTotal == 0) {
        if ($ownerexpPOS == 0) {
            $variancePTotal = 0;
        } else {
            $variancePTotal = -100;
        }
    } else {
        $variancePTotal = $expOWNVarTotal / $budgetEXPOWNTotal * 100;
    }
    $variancePTotal_display = formatting($variancePTotal);

    if ($budgetEXPOWNYTotal == 0) {
        if ($ownerexpAPOS == 0) {
            $variancePYTotal = 0;
        } else {
            $variancePYTotal = -100;
        }
    } else {
        $variancePYTotal = $expOWNVarYTotal / $budgetEXPOWNYTotal * 100;
    }
    $variancePYTotal_display = formatting($variancePYTotal);

    $xlsLine = $xlsLine + 1;
    $excel->getActiveSheet()->getStyle("B{$xlsLine}:J{$xlsLine}")->getAlignment()->setHorizontal(
        Alignment::HORIZONTAL_RIGHT
    );
    $excel->getActiveSheet()->getStyle("A{$xlsLine}:J{$xlsLine}")->getFont()->setBold(true);
    $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 1), 'Total Owner Expenditure');
    $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 2), formatting($ownerexp_display));
    $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 3), $budgetEXPOWNTotal_display);
    $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 4), $expOWNVarTotal_display);
    $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 8), $expOWNVarYTotal_display);
    $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 6), $ownerexpA_display);
    $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 7), $budgetEXPOWNYTotal_display);
    $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 5), $variancePTotal_display);
    $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 9), $variancePYTotal_display);
    $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 10), $budgetEXPOWNYEARTotal_display);
    $xlsLine = $xlsLine + 1;
}


// --------------------------------------------------------------------------
// RECOVERABLE PAYMENTS
//
// ACCOUNTS FOR INDIV CALCULATIONS
$accSQL = "SELECT DISTINCT pmxc_acc
		FROM pmxc_ap_alloc
		WHERE  fund IS NULL AND (pmxc_prop = ?)
		AND (pmxc_alloc_dt <= CONVERT(datetime, ?, 103))
		AND (pmxc_f_type = 'PAY')
		AND (pmxc_acc IN
						  (SELECT pmcg_acc
							FROM pmcg_chart_grp
							WHERE (pmcg_grp = 'TRACC2')
							AND pmcg_subgrp = 'EXPDR'))";

$accSQL_result = $dbh->executeScalars($accSQL, [$propertyID, $periodTo]);

$paramsbudgetACCSQL = [];
$budgetACCSQL = 'SELECT DISTINCT pmep_exp_acc AS account
FROM         pmep_b_exp_per
WHERE     (pmep_prop = ' . addSQLParam($paramsbudgetACCSQL, $propertyID) . ')
    AND (pmep_per IN (' . addSQLParam($paramsbudgetACCSQL, $pmcp_period) . '))
    AND (pmep_year IN (' . addSQLParam($paramsbudgetACCSQL, $pmcp_year) . "))
    AND (pmep_exp_acc IN
						  (SELECT pmcg_acc
							FROM pmcg_chart_grp
							WHERE (pmcg_grp = 'TRACC2') AND pmcg_subgrp = 'EXPDR'))";
// echo "19th BUDGET:" . $budgetACCSQL;
$budget_result = $dbh->executeScalars($budgetACCSQL, $paramsbudgetACCSQL);

$opBAL_accSQL = "SELECT DISTINCT pmpb_acc as account
		FROM pmpb_p_bal
		WHERE (pmpb_prop = ?)
		AND (pmpb_date BETWEEN CONVERT(datetime, ?, 103) AND CONVERT(datetime, ?, 103))
		AND (pmpb_acc IN (SELECT pmcg_acc
									FROM pmcg_chart_grp
									WHERE (pmcg_grp = 'TRACC2')
									AND (pmcg_subgrp = 'EXPDR')))";
$opBALacc_result = $dbh->executeScalars($opBAL_accSQL, [$propertyID, $startFinancialYear, $periodTo]);


$acc = [];
$acc = array_merge($accSQL_result, $opBALacc_result, $budget_result);

$merged = array_unique($acc);
asort($merged);


$budgetEXPDRTotal = 0;
$budgetEXPDRYTotal = 0;
$budgetEXPDRYEARTotal = 0;

$excel->getActiveSheet()->getStyle("A{$xlsLine}:J{$xlsLine}")->getFont()->setBold(true);
$excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 1), 'Direct Recoverable Expenditure');
$xlsLine = $xlsLine + 1;


// /////////////////////////account code assigned and account name identified
// /////////////////////////////////////////////////////////////////////////////

$subGroupVo_display = [];
$subGroupBudgetEXPVO_display = [];
$subGroupVoA_display = [];
$subGroupBudgetEXPVOY_display = [];
$subGroupBudgetEXPVOYEAR_display = [];
foreach ($merged as $key => $account) {
    // Get Account Name
    $accSQL = 'SELECT pmca_name FROM pmca_chart WHERE pmca_code = ?';
    $acc_result = $dbh->executeScalar($accSQL, [$account]);
    $account_name = $acc_result;
    $account_name = (strlen($account_name) < $limit) ? $account_name : substr($account_name, 0, $limit) . ' ...';


    // RECOVERABLE EXPENDITURE
    $recSQL = "SELECT COALESCE(SUM(pmxc_alloc_amt), 0) amount, SUM (pmxc_tax_amt) as tax_amount
		FROM pmxc_ap_alloc
		WHERE (pmxc_acc = ?)
				   AND (pmxc_f_type = 'PAY')
					AND (pmxc_prop = ?)
			AND (pmxc_alloc_dt BETWEEN CONVERT(datetime, ?, 103) AND CONVERT(datetime, ?, 103))";


    $rec_result = $dbh->executeSingle($recSQL, [$account, $propertyID, $periodFrom, $periodTo]);

    $rec_income = $rec_result['amount'];
    $rec_tax_income = $rec_result['tax_amount'];


    $rec_income = $rec_income + $rec_tax_income;
    $recPOS_income = $rec_income * (-1);
    $rec_display = formatting($recPOS_income, 2);

    // YTD5

    $recASQL = "SELECT COALESCE(SUM(pmxc_alloc_amt), 0) AS amount, COALESCE(SUM(pmxc_tax_amt), 0) as tax_amount
		FROM pmxc_ap_alloc
		WHERE (pmxc_acc = ?)
				   AND (pmxc_f_type = 'PAY')
					AND (pmxc_prop = ?)
			AND (pmxc_alloc_dt BETWEEN CONVERT(datetime, ?, 103) AND CONVERT(datetime, ?, 103))";
    $recA_result = $dbh->executeSingle($recASQL, [$account, $propertyID, $startFinancialYear, $periodTo]);

    $paramsunallcashRECSQL = [];
    $unallcashRECSQL = 'SELECT COALESCE(SUM(pmuc_net_amt), 0) as amount
			FROM pmuc_unall_csh
			WHERE (pmuc_prop = ' . addSQLParam($paramsunallcashRECSQL, $propertyID) . ')
			AND (pmuc_acc = ' . addSQLParam($paramsunallcashRECSQL, $account) . ')
			AND (pmuc_period >= 1)
						AND (pmuc_period <= ' . addSQLParam($paramsunallcashRECSQL, $toPeriod) . ')
				AND (pmuc_year IN (' . addSQLParam($paramsunallcashRECSQL, $pmcp_year) . '))';

    $recUC_result = $dbh->executeScalar($unallcashRECSQL, $paramsunallcashRECSQL);
    $recUC_income = $recUC_result;

    $recA_income = $recA_result['amount'];
    $recA_tax_income = $recA_result['tax_amount'];
    $recA_income = $recA_income + $recA_tax_income;


    // -----------BUDGET FIGURES ---------------------------------
    $paramsbudgetEXPDRSQL = [];
    $budgetEXPDRSQL = 'SELECT COALESCE(SUM(pmep_b_c_amt), 0) AS amount
		FROM pmep_b_exp_per
		WHERE (pmep_prop =  ' . addSQLParam($paramsbudgetEXPDRSQL, $propertyID) . ')
		AND (pmep_per IN ( ' . addSQLParam($paramsbudgetEXPDRSQL, $pmcp_period) . '))
				AND (pmep_year IN ( ' . addSQLParam($paramsbudgetEXPDRSQL, $pmcp_year) . '))
				AND (pmep_exp_acc =  ' . addSQLParam($paramsbudgetEXPDRSQL, $account) . ')';
    // echo "20th BUDGET:" . $budgetEXPDRSQL;

    $budgetEXPDR_result = $dbh->executeScalar($budgetEXPDRSQL, $paramsbudgetEXPDRSQL);
    $budgetEXPDR = $budgetEXPDR_result;
    $budgetEXPDRTotal = $budgetEXPDRTotal + $budgetEXPDR;
    $budgetEXPDR_display = formatting($budgetEXPDR);


    $paramsbudgetEXPDRYSQL = [];
    $budgetEXPDRYSQL = 'SELECT COALESCE(SUM(pmep_b_c_amt), 0) AS amount
		FROM pmep_b_exp_per
		WHERE (pmep_prop = ' . addSQLParam($paramsbudgetEXPDRYSQL, $propertyID) . ')
		AND (pmep_per >= 1)
		AND (pmep_per <= ' . addSQLParam($paramsbudgetEXPDRYSQL, $toPeriod) . ')
				  AND (pmep_year IN (' . addSQLParam($paramsbudgetEXPDRYSQL, $pmcp_year) . '))
				AND (pmep_exp_acc =' . addSQLParam($paramsbudgetEXPDRYSQL, $account) . ')';
    // echo "21st BUDGET:" . $budgetEXPDRYSQL;

    $budgetEXPDRY_result = $dbh->executeScalar($budgetEXPDRYSQL, $paramsbudgetEXPDRYSQL);
    $budgetEXPDRY = $budgetEXPDRY_result;
    $budgetEXPDRY_display = formatting($budgetEXPDRY, 2);
    $budgetEXPDRYTotal = $budgetEXPDRYTotal + $budgetEXPDRY;

    $paramsbudgetEXPDRYEARSQL = [];
    $budgetEXPDRYEARSQL = 'SELECT COALESCE(SUM(pmep_b_c_amt), 0) AS amount
		FROM pmep_b_exp_per
		WHERE (pmep_prop = ' . addSQLParam($paramsbudgetEXPDRYEARSQL, $propertyID) . ')
		AND (pmep_per BETWEEN 1 AND 12)AND (pmep_year IN (' . addSQLParam($paramsbudgetEXPDRYEARSQL, $pmcp_year) . '))
				AND (pmep_exp_acc = ' . addSQLParam($paramsbudgetEXPDRYEARSQL, $account) . ')';
    // echo "22nd BUDGET:" . $budgetEXPDRYEARSQL;

    $budgetEXPDRYEAR_result = $dbh->executeScalar($budgetEXPDRYEARSQL, $paramsbudgetEXPDRYEARSQL);
    $budgetEXPDRYEAR = $budgetEXPDRYEAR_result;
    $budgetEXPDRYEAR_display = formatting($budgetEXPDRYEAR, 2);
    $budgetEXPDRYEARTotal = $budgetEXPDRYEARTotal + $budgetEXPDRYEAR;


    // OPENING BALANCE FOR EACH ACCOUNT
    $openBalOWNSQL = 'SELECT COALESCE(SUM(pmpb_bal), 0) as amount
		FROM pmpb_p_bal
		WHERE (pmpb_prop = ?)
		AND (pmpb_date BETWEEN CONVERT(datetime, ?, 103) AND CONVERT(datetime, ?, 103))
		AND (pmpb_acc = ?)';
    $openBalOWN_result = $dbh->executeScalar(
        $openBalOWNSQL,
        [$propertyID, $startFinancialYear, $periodTo, $account]
    );

    // -----------OPENING BALANCE FIGURES------------------------
    $openBalOWN_exp = $openBalOWN_result;
    // ----------------------------------------------------------


    $ownerA_income = $ownerincA_result['amount'];
    $ownerA_income_tax = $ownerincA_result['tax_amount'];
    $ownerA_income = $ownerA_income + $ownerA_income_tax - $openBalOWN_exp;
    $ownerAPOS_income = $ownerA_income * (-1);
    $ownerA_display = formatting($ownerAPOS_income, 2);

    $recA_income = $recA_income - $openBalOWN_exp;
    $recAPOS_income = $recA_income * (-1);
    $recA_display = formatting($recAPOS_income, 2);

    $variance = $budgetEXPDR - $recPOS_income;
    $variance_display = formatting($variance);
    if ($budgetEXPDR == 0) {
        if ($recPOS_income == 0) {
            $varianceP = 0;
        } else {
            $varianceP = -100;
        }
    } else {
        $varianceP = $variance / $budgetEXPDR * 100;
    }
    $varianceP_display = formatting($varianceP);


    $varianceY = $budgetEXPDRY - $recAPOS_income;
    $varianceY_display = formatting($varianceY);
    if ($budgetEXPDRY == 0) {
        if ($recAPOS_income == 0) {
            $variancePY = 0;
        } else {
            $variancePY = -100;
        }
    } else {
        $variancePY = $varianceY / $budgetEXPDRY * 100;
    }
    $variancePY_display = formatting($variancePY);
    if ($rec_display == '0.00' && $recA_income == '0.00' && $budgetEXPDR_display == '0.00' && $budgetEXPDRY_display == '0.00' && $budgetEXPDRYEAR_display == '0.00') {
        // DON NOT DISPLAY LINE
    } else {
        $subGroupVo_display[] = $recPOS_income;
        $subGroupBudgetEXPVO_display[] = $budgetEXPDRY;
        $subGroupVoA_display[] = $recAPOS_income;
        $subGroupBudgetEXPVOY_display[] = $budgetEXPDRYEAR;
        $subGroupBudgetEXPVOYEAR_display[] = $budgetEXPDRYEAR;

        $excel->getActiveSheet()->getStyle("B{$xlsLine}:J{$xlsLine}")->getAlignment()->setHorizontal(
            Alignment::HORIZONTAL_RIGHT
        );
        $excel->getActiveSheet()->getStyle("A{$xlsLine}:J{$xlsLine}")->getFont()->setBold(false);
        $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 1), $account . ' ' . $account_name);
        $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 2), $rec_display);
        $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 3), $budgetEXPDR_display);
        $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 4), $variance_display);
        $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 5), $varianceP_display);
        $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 6), $recA_display);
        $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 7), $budgetEXPDRY_display);
        $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 8), $varianceY_display);
        $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 9), $variancePY_display);
        $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 10), $budgetEXPDRYEAR_display);
        $xlsLine = $xlsLine + 1;
    }
}


$budgetEXPDRTotal_display = formatting($budgetEXPDRTotal);
$budgetEXPDRYEARTotal_display = formatting($budgetEXPDRYEARTotal, 2);
$budgetEXPDRYTotal_display = formatting($budgetEXPDRYTotal);


$recexpYear = totalexpenses($propertyID, $startFinancialYear, $periodTo, 'EXPDR');
$recoverableA_exp = $recexpYear['net_amount'] + $recexp_takeon['amount'];


$recexp = totalexpenses($propertyID, $periodFrom, $periodTo, 'EXPDR');
$recoverable_exp = $recexp['net_amount'];


// $recincAPOS = $recexpY;
$total_rec = formatting($recoverable_exp, 2);
$total_recA = formatting($recoverableA_exp, 2);

$varianceTotal = $budgetEXPDRTotal - $recoverable_exp;
$varianceTotal_display = formatting($varianceTotal);

$varianceYTotal = $budgetEXPDRYTotal - $recoverableA_exp;
$varianceYTotal_display = formatting($varianceYTotal);

if ($budgetEXPDRTotal == 0) {
    if ($recoverable_exp == 0) {
        $variancePTotal = 0;
    } else {
        $variancePTotal = -100;
    }
} else {
    $variancePTotal = $varianceTotal / $budgetEXPDRTotal * 100;
}
$variancePTotal_display = formatting($variancePTotal);

if ($budgetEXPDRYTotal == 0) {
    if ($recoverableA_exp == 0) {
        $variancePYTotal = 0;
    } else {
        $variancePYTotal = -100;
    }
} else {
    $variancePYTotal = $varianceYTotal / $budgetEXPDRYTotal * 100;
}
$variancePYTotal_display = formatting($variancePYTotal);

if ($total_rec == '0.00' &&
    $total_recA == '0.00' &&
    $budgetEXPDRTotal_display == '0.00' &&
    $budgetEXPDRYTotal_display == '0.00' &&
    $budgetEXPDRYEARTotal_display == '0.00' &&
    $varianceTotal_display == '0.00' &&
    $variancePTotal_display == '0.00' &&
    $varianceYTotal_display == '0.00' &&
    $variancePYTotal_display == '0.00') {
    $xlsLine = $xlsLine + 1;
} else {
    $xlsLine = $xlsLine + 1;
    $excel->getActiveSheet()->getStyle("B{$xlsLine}:J{$xlsLine}")->getAlignment()->setHorizontal(
        Alignment::HORIZONTAL_RIGHT
    );
    $excel->getActiveSheet()->getStyle("A{$xlsLine}:J{$xlsLine}")->getFont()->setBold(true);
    $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 1), 'Total Direct Recoverable Expenditure');
    $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 2), $total_rec);
    $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 3), $budgetEXPDRTotal_display);
    $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 4), $varianceTotal_display);
    $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 5), $variancePTotal_display);
    $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 6), $total_recA);
    $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 7), $budgetEXPDRYTotal_display);
    $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 9), $variancePYTotal_display);
    $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 8), $varianceYTotal_display);
    $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 10), $budgetEXPDRYEARTotal_display);
    $xlsLine = $xlsLine + 1;
}

// --------------------------------------------------------------------------
// VARIABLE OUTGOINGS PAYMENTS
//
// ACCOUNTS FOR INDIV CALCULATIONS
$accSQL = "SELECT DISTINCT pmxc_acc
		FROM pmxc_ap_alloc
		WHERE  fund IS NULL AND (pmxc_prop = ?)
		AND (pmxc_alloc_dt BETWEEN CONVERT(datetime, ?, 103) AND CONVERT(datetime, ?, 103))
		AND (pmxc_f_type = 'PAY')
		AND (pmxc_acc IN
						  (SELECT pmcg_acc
							FROM pmcg_chart_grp
							WHERE (pmcg_grp = 'TRACC2')
							AND pmcg_subgrp = 'EXPVO'))";

$accSQL_result = $dbh->executeScalars($accSQL, [$propertyID, $startFinancialYear, $periodTo]);


$paramsbudgetACCSQL = [];
$budgetACCSQL = 'SELECT DISTINCT pmep_exp_acc AS account
FROM         pmep_b_exp_per
WHERE     (pmep_prop = ' . addSQLParam($paramsbudgetACCSQL, $propertyID) . ')
    AND (pmep_per IN (' . addSQLParam($paramsbudgetACCSQL, $pmcp_period) . '))
    AND (pmep_year IN (' . addSQLParam($paramsbudgetACCSQL, $pmcp_year) . "))
    AND (pmep_exp_acc IN
						  (SELECT pmcg_acc
							FROM pmcg_chart_grp
							WHERE (pmcg_grp = 'TRACC2') AND pmcg_subgrp = 'EXPVO'))";
// echo "23rd BUDGET:" . $budgetACCSQL;
$budget_result = $dbh->executeScalars($budgetACCSQL, $paramsbudgetACCSQL);


$opBAL_accSQL = "SELECT DISTINCT pmpb_acc as account
		FROM pmpb_p_bal
		WHERE (pmpb_prop = ?)
		AND (pmpb_date BETWEEN CONVERT(datetime, ?, 103) AND CONVERT(datetime, ?, 103))
		AND (pmpb_acc IN (SELECT pmcg_acc
									FROM pmcg_chart_grp
									WHERE (pmcg_grp = 'TRACC2')
									AND (pmcg_subgrp = 'EXPVO')))";
$opBALacc_result = $dbh->executeScalars($opBAL_accSQL, [$propertyID, $startFinancialYear, $periodTo]);


$acc = [];
$acc = array_merge($accSQL_result, $opBALacc_result, $budget_result);

$merged = array_unique($acc);


asort($merged);


$budgetEXPVOTotal = 0;
$budgetEXPVOYTotal = 0;
$budgetEXPVOYEARTotal = 0;

$subGroup = '(' . implode('),(', $merged) . ')';
if ($subGroup != '()') {
    $subGroupQuery = "SELECT  accountCode,pmrcg_subgrp , pmas_desc as description  FROM (VALUES {$subGroup}  )
    AS accounts(accountCode)
	join pmrcg_chart_grp on  accountCode between pmrcg_acc and pmrcg_acc_to and pmrcg_chart_grp.pmrcg_grp='TRACC3'
	join pmas_acc_subgrp on pmrcg_chart_grp.pmrcg_grp = pmas_acc_subgrp.pmas_grp and pmas_subgrp = pmrcg_subgrp
	order by pmas_desc,accountCode";
    $subGroupRun = $dbh->executeSet($subGroupQuery);
    $subGroupArray = [];
    $subGroupChecker = [];
    foreach ($subGroupRun as $v) {
        $subGroupArray[$v['accountCode']] = ucwords(strtolower($v['description']));
    }
}
$vt = 0;
$show_var = true;
$subGroupVo_display = [];
$subGroupBudgetEXPVO_display = [];
$subGroupVoA_display = [];
$subGroupBudgetEXPVOY_display = [];
$subGroupBudgetEXPVOYEAR_display = [];
foreach ($subGroupArray as $key => $account) {
    $account = $key;
    // Get Account Name
    $accSQL = 'SELECT pmca_name FROM pmca_chart WHERE pmca_code = ?';
    $acc_result = $dbh->executeScalar($accSQL, [$account]);
    $account_name = $acc_result;
    $account_name = (strlen($account_name) < $limit) ? $account_name : substr($account_name, 0, $limit) . ' ...';

    // VO PAYMENTS
    $voSQL = "SELECT COALESCE(SUM(pmxc_alloc_amt), 0) amount, SUM (pmxc_tax_amt) as tax_amount
		FROM pmxc_ap_alloc
		WHERE (pmxc_acc = ?)
				AND (pmxc_f_type = 'PAY')
					AND (pmxc_prop = ?)
			AND (pmxc_alloc_dt BETWEEN CONVERT(datetime, ?, 103) AND CONVERT(datetime, ?, 103))";

    $vo_result = $dbh->executeSingle($voSQL, [$account, $propertyID, $periodFrom, $periodTo]);
    $vo_exp = $vo_result['amount'];
    $vo_tax_exp = $vo_result['tax_amount'];

    $vo_exp = $vo_exp + $vo_tax_exp;
    $vt = $vt + $vo_exp;
    $voPOS_exp = $vo_exp * (-1);
    $vo_display = formatting($voPOS_exp, 2);

    // YTD6

    $voASQL = "SELECT COALESCE(SUM(pmxc_alloc_amt), 0) AS amount, COALESCE(SUM(pmxc_tax_amt), 0) as tax_amount
		FROM pmxc_ap_alloc
		WHERE (pmxc_acc = ?)
				AND (pmxc_f_type = 'PAY')
					AND (pmxc_prop = ?)
			AND (pmxc_alloc_dt BETWEEN CONVERT(datetime, ?, 103) AND CONVERT(datetime, ?, 103))";


    $voA_result = $dbh->executeSingle($voASQL, [$account, $propertyID, $startFinancialYear, $periodTo]);

    $paramsunallcashRECSQL = [];
    $unallcashRECSQL = 'SELECT COALESCE(SUM(pmuc_net_amt), 0) as amount
			FROM pmuc_unall_csh
			WHERE (pmuc_prop = ' . addSQLParam($paramsunallcashRECSQL, $propertyID) . ')
			AND (pmuc_acc = ' . addSQLParam($paramsunallcashRECSQL, $account) . ')
			AND (pmuc_period >= 1)
						AND (pmuc_period <= ' . addSQLParam($paramsunallcashRECSQL, $toPeriod) . ')
				AND (pmuc_year IN (' . addSQLParam($paramsunallcashRECSQL, $pmcp_year) . '))';

    $recUC_result = $dbh->executeScalar($unallcashRECSQL, $paramsunallcashRECSQL);
    $recUC_income = $recUC_result;

    // -----------BUDGET FIGURES ---------------------------------
    $paramsbudgetEXPVOSQL = [];
    $budgetEXPVOSQL = 'SELECT COALESCE(SUM(pmep_b_c_amt), 0) AS amount
		FROM pmep_b_exp_per
		WHERE (pmep_prop = ' . addSQLParam($paramsbudgetEXPVOSQL, $propertyID) . ')
		AND (pmep_per IN (' . addSQLParam($paramsbudgetEXPVOSQL, $pmcp_period) . '))
				AND (pmep_year IN (' . addSQLParam($paramsbudgetEXPVOSQL, $pmcp_year) . '))
				AND (pmep_exp_acc = ' . addSQLParam($paramsbudgetEXPVOSQL, $account) . ')';
    // echo "24th BUDGET:" . $budgetEXPVOSQL;

    $budgetEXPVO_result = $dbh->executeScalar($budgetEXPVOSQL, $paramsbudgetEXPVOSQL);
    $budgetEXPVO = $budgetEXPVO_result;
    $budgetEXPVOTotal = $budgetEXPVOTotal + $budgetEXPVO;
    $budgetEXPVOTotal_display = formatting($budgetEXPVOTotal);

    $budgetEXPVO_display = formatting($budgetEXPVO);

    $paramsbudgetEXPVOYSQL = [];
    $budgetEXPVOYSQL = 'SELECT COALESCE(SUM(pmep_b_c_amt), 0) AS amount
		FROM pmep_b_exp_per
		WHERE (pmep_prop = ' . addSQLParam($paramsbudgetEXPVOYSQL, $propertyID) . ')
		AND (pmep_per >= 1)
		AND (pmep_per <= ' . addSQLParam($paramsbudgetEXPVOYSQL, $toPeriod) . ')
				  AND (pmep_year IN (' . addSQLParam($paramsbudgetEXPVOYSQL, $pmcp_year) . '))
				AND (pmep_exp_acc =' . addSQLParam($paramsbudgetEXPVOYSQL, $account) . ')';
    // echo "25th BUDGET:" . $budgetEXPVOYSQL;

    $budgetEXPVOY_result = $dbh->executeScalar($budgetEXPVOYSQL, $paramsbudgetEXPVOYSQL);
    $budgetEXPVOY = $budgetEXPVOY_result;
    $budgetEXPVOY_display = formatting($budgetEXPVOY, 2);
    $budgetEXPVOYTotal = $budgetEXPVOYTotal + $budgetEXPVOY;

    $budgetEXPVOYTotal_display = formatting($budgetEXPVOYTotal);

    $paramsbudgetEXPVOYEARSQL = [];
    $budgetEXPVOYEARSQL = 'SELECT COALESCE(SUM(pmep_b_c_amt), 0) AS amount
		FROM pmep_b_exp_per
		WHERE (pmep_prop = ' . addSQLParam($paramsbudgetEXPVOYEARSQL, $propertyID) . ')
		AND (pmep_per BETWEEN 1 AND 12)AND (pmep_year IN (' . addSQLParam($paramsbudgetEXPVOYEARSQL, $pmcp_year) . '))
				AND (pmep_exp_acc = ' . addSQLParam($paramsbudgetEXPVOYEARSQL, $account) . ')';
    // echo "26th BUDGET:" . $budgetEXPVOYEARSQL;

    $budgetEXPVOYEAR_result = $dbh->executeScalar($budgetEXPVOYEARSQL, $paramsbudgetEXPVOYEARSQL);
    $budgetEXPVOYEAR = $budgetEXPVOYEAR_result;
    $budgetEXPVOYEAR_display = formatting($budgetEXPVOYEAR, 2);
    $budgetEXPVOYEARTotal = $budgetEXPVOYEARTotal + $budgetEXPVOYEAR;
    $budgetEXPVOYEARTotal_display = formatting($budgetEXPVOYEARTotal, 2);

    // OPENING BALANCE FOR EACH ACCOUNT
    $openBalVOSQL = 'SELECT COALESCE(SUM(pmpb_bal), 0) as amount
		FROM pmpb_p_bal
		WHERE (pmpb_prop = ?)
		AND (pmpb_date BETWEEN CONVERT(datetime, ?, 103) AND CONVERT(datetime, ?, 103))
		AND (pmpb_acc = ?)';


    $openBalVO_result = $dbh->executeScalar(
        $openBalVOSQL,
        [$propertyID, $startFinancialYear, $periodTo, $account]
    );

    // -----------OPENING BALANCE FIGURES------------------------
    $openBalVO_exp = $openBalVO_result;
    // ----------------------------------------------------------

    $voA_exp = $voA_result['amount'];
    $voA_tax_income = $voA_result['tax_amount'];
    $voA_exp = $voA_exp + $voA_tax_income - $openBalVO_exp;
    $voAPOS_exp = $voA_exp * (-1);
    $voA_display = formatting($voAPOS_exp, 2);


    $variance = $budgetEXPVO - $voPOS_exp;
    $variance_display = formatting($variance);
    if ($budgetEXPVO == 0) {
        if ($voPOS_exp == 0) {
            $varianceP = 0;
        } else {
            $varianceP = -100;
        }
    } else {
        $varianceP = $variance / $budgetEXPVO * 100;
    }
    $varianceP_display = formatting($varianceP);


    $varianceY = $budgetEXPVOY - $voAPOS_exp;
    $varianceY_display = formatting($varianceY);
    if ($budgetEXPVOY == 0) {
        if ($voAPOS_exp == 0) {
            $variancePY = 0;
        } else {
            $variancePY = -100;
        }
    } else {
        $variancePY = $varianceY / $budgetEXPVOY * 100;
    }
    $variancePY_display = formatting($variancePY);


    if ($vo_display == '0.00' && $voA_display == '0.00' && $budgetEXPVO_display == '0.00' && $budgetEXPVOY_display == '0.00' && $budgetEXPVOYEAR_display == '0.00') {
        // DON NOT DISPLAY LINE

    } else {
        if (($show_var)) {
            $show_var = false;

            $excel->getActiveSheet()->getStyle("A{$xlsLine}:J{$xlsLine}")->getFont()->setBold(true);
            $excel->getActiveSheet()->SetCellValue(
                cellReference($xlsLine, 1),
                ucwords(strtolower($_SESSION['country_default']['variable_outgoings'])) . ' Expenditure'
            );
            $xlsLine = $xlsLine + 1;
        }


        if (! in_array(
            $subGroupArray[$account],
            $subGroupChecker
        ) && isset($subGroupArray[$account]) && $subGroupArray[$account]) {
            if (! empty($subGroupChecker)) {
                $varianceC = array_sum($subGroupBudgetEXPVO_display) - array_sum($subGroupVo_display);
                $varianceCP = array_sum($subGroupBudgetEXPVO_display) ? $varianceC / array_sum(
                    $subGroupBudgetEXPVO_display
                ) * 100 : (array_sum($subGroupVo_display) != 0 ? -100 : 0);
                $varianceY = array_sum($subGroupBudgetEXPVOY_display) - array_sum($subGroupVoA_display);
                $varianceYP = array_sum($subGroupBudgetEXPVOY_display) ? $varianceY / array_sum(
                    $subGroupBudgetEXPVOY_display
                ) * 100 : (array_sum($subGroupVoA_display) != 0 ? -100 : 0);

                $excel->getActiveSheet()->getStyle("B{$xlsLine}:J{$xlsLine}")->getAlignment()->setHorizontal(
                    Alignment::HORIZONTAL_RIGHT
                );
                $excel->getActiveSheet()->getStyle("A{$xlsLine}:J{$xlsLine}")->getFont()->setBold(true);
                $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 1), 'Sub Total');
                $excel->getActiveSheet()->SetCellValue(
                    cellReference($xlsLine, 2),
                    formatting(array_sum($subGroupVo_display))
                );
                $excel->getActiveSheet()->SetCellValue(
                    cellReference($xlsLine, 3),
                    formatting(array_sum($subGroupBudgetEXPVO_display))
                );
                $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 4), formatting(($varianceC)));
                $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 5), formatting(($varianceCP)));
                $excel->getActiveSheet()->SetCellValue(
                    cellReference($xlsLine, 6),
                    formatting(array_sum($subGroupVoA_display))
                );
                $excel->getActiveSheet()->SetCellValue(
                    cellReference($xlsLine, 7),
                    formatting(array_sum($subGroupBudgetEXPVOY_display))
                );
                $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 8), formatting(($varianceY)));
                $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 9), formatting(($varianceYP)));
                $excel->getActiveSheet()->SetCellValue(
                    cellReference($xlsLine, 10),
                    formatting(array_sum($subGroupBudgetEXPVOYEAR_display))
                );
                $xlsLine = $xlsLine + 1;

                $subGroupVo_display = [];
                $subGroupBudgetEXPVO_display = [];
                $subGroupVoA_display = [];
                $subGroupBudgetEXPVOY_display = [];
                $subGroupBudgetEXPVOYEAR_display = [];
            }

            $excel->getActiveSheet()->getStyle("A{$xlsLine}:J{$xlsLine}")->getFont()->setBold(true);
            $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 1), $subGroupArray[$account]);
            $xlsLine = $xlsLine + 1;
            $subGroupChecker[] = $subGroupArray[$account];
        }

        $subGroupVo_display[] = $voPOS_exp;
        $subGroupBudgetEXPVO_display[] = $budgetEXPVO;
        $subGroupVoA_display[] = $voAPOS_exp;
        $subGroupBudgetEXPVOY_display[] = $budgetEXPVOY;
        $subGroupBudgetEXPVOYEAR_display[] = $budgetEXPVOYEAR;

        $excel->getActiveSheet()->getStyle("B{$xlsLine}:J{$xlsLine}")->getAlignment()->setHorizontal(
            Alignment::HORIZONTAL_RIGHT
        );
        $excel->getActiveSheet()->getStyle("A{$xlsLine}:J{$xlsLine}")->getFont()->setBold(false);
        $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 1), $account . ' ' . $account_name);
        $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 2), $vo_display);
        $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 3), $budgetEXPVO_display);
        $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 4), $variance_display);
        $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 5), $varianceP_display);
        $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 6), $voA_display);
        $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 7), $budgetEXPVOY_display);
        $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 8), $varianceY_display);
        $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 9), $variancePY_display);
        $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 10), $budgetEXPVOYEAR_display);
        $xlsLine = $xlsLine + 1;
    }
}

if (! empty($subGroupChecker)) {
    $varianceC = array_sum($subGroupBudgetEXPVO_display) - array_sum($subGroupVo_display);
    $varianceCP = array_sum($subGroupBudgetEXPVO_display) ? $varianceC / array_sum(
        $subGroupBudgetEXPVO_display
    ) * 100 : (array_sum($subGroupVo_display) != 0 ? -100 : 0);
    $varianceY = array_sum($subGroupBudgetEXPVOY_display) - array_sum($subGroupVoA_display);
    $varianceYP = array_sum($subGroupBudgetEXPVOY_display) ? $varianceY / array_sum(
        $subGroupBudgetEXPVOY_display
    ) * 100 : (array_sum($subGroupVoA_display) != 0 ? -100 : 0);

    $excel->getActiveSheet()->getStyle("B{$xlsLine}:J{$xlsLine}")->getAlignment()->setHorizontal(
        Alignment::HORIZONTAL_RIGHT
    );
    $excel->getActiveSheet()->getStyle("A{$xlsLine}:J{$xlsLine}")->getFont()->setBold(true);
    $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 1), 'Sub Total');
    $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 2), formatting(array_sum($subGroupVo_display)));
    $excel->getActiveSheet()->SetCellValue(
        cellReference($xlsLine, 3),
        formatting(array_sum($subGroupBudgetEXPVO_display))
    );
    $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 4), formatting(($varianceC)));
    $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 5), formatting(($varianceCP)));
    $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 6), formatting(array_sum($subGroupVoA_display)));
    $excel->getActiveSheet()->SetCellValue(
        cellReference($xlsLine, 7),
        formatting(array_sum($subGroupBudgetEXPVOY_display))
    );
    $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 8), formatting(($varianceY)));
    $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 9), formatting(($varianceYP)));
    $excel->getActiveSheet()->SetCellValue(
        cellReference($xlsLine, 10),
        formatting(array_sum($subGroupBudgetEXPVOYEAR_display))
    );
    $xlsLine = $xlsLine + 1;
    $subGroupVo_display = [];
    $subGroupBudgetEXPVO_display = [];
    $subGroupVoA_display = [];
    $subGroupBudgetEXPVOY_display = [];
    $subGroupBudgetEXPVOYEAR_display = [];
}

$voexpM = totalexpenses($propertyID, $periodFrom, $periodTo, 'EXPVO');
$voexpTotal = $voexpM['net_amount'];

$voexpYear = totalexpenses($propertyID, $startFinancialYear, $periodTo, 'EXPVO');
$voexpATotal = $voexpYear['net_amount'] + $voexp_takeon['amount'];


$total_vo = formatting($voexpTotal, 2);
$total_voA = formatting($voexpATotal, 2);


$total_variance = $budgetEXPVOTotal - $voexpTotal;
$total_variance_display = formatting($total_variance);
if ($budgetEXPVOTotal == 0) {
    if ($voexpTotal == 0) {
        $total_varianceP = 0;
    } else {
        $total_varianceP = -100;
    }
} else {
    $total_varianceP = $total_variance / $budgetEXPVOTotal * 100;
}
$total_varianceP_display = formatting($total_varianceP);

$total_varianceY = $budgetEXPVOYTotal - $voexpATotal;
$total_varianceY_display = formatting($total_varianceY);
if ($budgetEXPVOYTotal == 0) {
    if ($voexpATotal == 0) {
        $total_variancePY = 0;
    } else {
        $total_variancePY = -100;
    }
} else {
    $total_variancePY = $total_varianceY / $budgetEXPVOYTotal * 100;
}
$total_variancePY_display = formatting($total_variancePY);

$budgetEXPVOYTotal_display = formatting($budgetEXPVOYTotal);
$budgetEXPVOTotal_display = formatting($budgetEXPVOTotal);


if ($total_vo == '0.00' &&
    $total_voA == '0.00' &&
    ($budgetEXPVOTotal_display == '0.00' || $budgetEXPVOTotal_display == ' ') &&
    ($budgetEXPVOYTotal_display == '0.00' || $budgetEXPVOYTotal_display == ' ')) {
    $xlsLine = $xlsLine + 1;
} else {
    $xlsLine = $xlsLine + 1;
    $excel->getActiveSheet()->getStyle("B{$xlsLine}:J{$xlsLine}")->getAlignment()->setHorizontal(
        Alignment::HORIZONTAL_RIGHT
    );
    $excel->getActiveSheet()->getStyle("A{$xlsLine}:J{$xlsLine}")->getFont()->setBold(true);
    $excel->getActiveSheet()->SetCellValue(
        cellReference($xlsLine, 1),
        'Total ' . ucwords(strtolower($_SESSION['country_default']['variable_outgoings'])) . ' Expenditure'
    );
    $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 2), $total_vo);
    $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 3), $budgetEXPVOTotal_display);
    $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 4), $total_variance_display);
    $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 5), $total_varianceP_display);
    $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 6), $total_voA);
    $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 7), $budgetEXPVOYTotal_display);
    $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 8), $total_varianceY_display);
    $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 9), $total_variancePY_display);
    $excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 10), $budgetEXPVOYEARTotal_display);
    $xlsLine = $xlsLine + 1;
}


// --------------------------------------------------------------------------------------
// TOTAL CASH RECEIPTS
//


$totalActual = $voexpTotal + $recoverable_exp + $ownerexp_display;
$totalActualY = $voexpATotal + $recoverableA_exp + $ownerexpAPOS;
$totalAllBudgetExpM = $budgetEXPOWNTotal + $budgetEXPDRTotal + $budgetEXPVOTotal;
$totalAllBudgetExpY = $budgetEXPOWNYTotal + $budgetEXPDRYTotal + $budgetEXPVOYTotal;
$totalAllBudgetExpYTD = $budgetEXPOWNYEARTotal + $budgetEXPDRYEARTotal + $budgetEXPVOYEARTotal;
$totalAllVarianceExp = $totalAllBudgetExpM - $totalActual;
$totalAllVarianceExpY = $totalAllBudgetExpY - $totalActualY;

if ($totalAllBudgetExpM == 0) {
    if ($totalActual == 0) {
        $totalGrand_varianceP = 0;
    } else {
        $totalGrand_varianceP = -100;
    }
} else {
    $totalGrand_varianceP = $totalAllVarianceExp / $totalAllBudgetExpM * 100;
}

if ($totalAllBudgetExpY == 0) {
    if ($totalActual == 0) {
        $totalGrand_variancePY = 0;
    } else {
        $totalGrand_variancePY = -100;
    }
} else {
    $totalGrand_variancePY = $totalAllVarianceExpY / $totalAllBudgetExpY * 100;
}


$totalAllExp_display = formatting($totalActual, 2);
$totalAllExpA_display = formatting($totalActualY, 2);
$totalAllBudgetExpM_display = formatting($totalAllBudgetExpM, 2);
$totalAllBudgetExpY_display = formatting($totalAllBudgetExpY, 2);
$totalAllBudgetExpYTD_display = formatting($totalAllBudgetExpYTD, 2);
$totalAllVarianceExp_display = formatting($totalAllVarianceExp, 2);
$totalAllVarianceExpY_display = formatting($totalAllVarianceExpY, 2);
$totalAllVPExp_display = formatting($totalGrand_varianceP);
$totalAllVPExpY_display = formatting($totalGrand_variancePY);

$xlsLine = $xlsLine + 1;
$excel->getActiveSheet()->getStyle("B{$xlsLine}:J{$xlsLine}")->getAlignment()->setHorizontal(
    Alignment::HORIZONTAL_RIGHT
);
$excel->getActiveSheet()->getStyle("A{$xlsLine}:J{$xlsLine}")->getFont()->setBold(true);
$excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 1), 'Total Operating Cash Payments');
$excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 2), $totalAllExp_display);
$excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 3), $totalAllBudgetExpM_display);
$excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 4), $totalAllVarianceExp_display);
$excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 5), $totalAllVPExp_display);
$excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 6), $totalAllExpA_display);
$excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 7), $totalAllBudgetExpY_display);
$excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 8), $totalAllVarianceExpY_display);
$excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 9), $totalAllVPExpY_display);
$excel->getActiveSheet()->SetCellValue(cellReference($xlsLine, 10), $totalAllBudgetExpYTD_display);


$cashPaymentsPages = $startCashPaymentsPages;

$_excel = new Xlsx($excel);
$_excel->save($xlsDownloadPath);
$clientDirectory = str_replace(' ', '', $_SESSION['database']);
$xlsDownloadLink = "{$clientDirectory}/xlsx/" . DOC_OWNERSTATEMENT . '/' . $fileName . $propertyID . '_' . $timestamp . '.xlsx';
$expenditureExcelAttachments[] = [$xlsDownloadPath, $fileName . $propertyID . '_' . $timestamp . '.xlsx'];
renderDownloadLink($xlsDownloadLink);

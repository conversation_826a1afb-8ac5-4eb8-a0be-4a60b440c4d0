<?php

function getTransactionsAndBudgets($property, $period, $year)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $params = [];

    $sql = 'SELECT propID as bldgID, propName as buildingName, unitID as suiteID, unitName as description, accountID, accountName  as acctName,  
             CONCAT(' . addSQLParam($params, $year) . ',REPLACE(STR(' . addSQLParam($params, $period) . ",2),' ','0')) as period, a.actual, b.pmrp_b_a_amt as budget,
			 a_ytd.actualYTD, b_ytd.budgetYTD, b_annual.budgetAnnual
            FROM 
                (SELECT prop.pmpr_prop as propID, prop.pmpr_name as propName, unit.pmpu_unit as unitID, unit.pmpu_desc as unitName, accountID, accountName, prop.pmpr_owner as ownerID
                  FROM 
                    (SELECT
                            a_.pmca_code AS accountID,
                            a_.pmca_name AS accountName,
                            a_.pmca_gl_account_group1,
                            a_.pmca_gl_account_group2
                        FROM pmca_chart a_
                        JOIN pmcg_chart_grp c 
                            ON a_.pmca_code = c.pmcg_acc 
                            AND ( ( (pmcg_grp = 'TRACC1' AND pmcg_subgrp = 'INCOME')  OR (pmcg_grp = 'TRACC2' AND pmcg_subgrp = 'BSREC')))
                        WHERE a_.pmca_code NOT IN ('10001','10002','10003','10004','10000','10005','10006','10007','10008','10009','10010')
                        ) as income_accounts
                    CROSS JOIN pmpr_property prop
                    JOIN pmpu_p_unit unit
                        ON unit.pmpu_prop = prop.pmpr_prop
                    WHERE prop.pmpr_prop = " . addSQLParam($params, $property) . '
                ) as property_income_accounts
            LEFT JOIN 
                (SELECT ref_2, ref_3, ref_5, COALESCE(SUM(trans_amt), 0) as actual
                    FROM ar_transaction
                    WHERE ref_2 = ' . addSQLParam($params, $property) . '
                    AND artr_period = ' . addSQLParam($params, $period) . '
                    AND artr_year = ' . addSQLParam($params, $year) . "
                    AND trans_type IN ('INV', 'CRE')
                    GROUP BY ref_2, ref_3, ref_5) as a 
                ON a.ref_2 = property_income_accounts.propID
                AND a.ref_3 = property_income_accounts.accountID
                AND a.ref_5 = property_income_accounts.unitID
            LEFT JOIN pmrp_b_rev_per b
                ON b.pmrp_prop = property_income_accounts.propID
                AND b.pmrp_unit = property_income_accounts.unitID
                AND b.pmrp_acc = property_income_accounts.accountID
                AND b.pmrp_per = " . addSQLParam($params, $period) . '
                AND b.pmrp_year = ' . addSQLParam($params, $year) . '
            LEFT JOIN 
                (SELECT ref_2, ref_3, ref_5, COALESCE(SUM(trans_amt), 0) as actualYTD
                    FROM ar_transaction
                    WHERE ref_2 = ' . addSQLParam($params, $property) . '
                    AND artr_period <= ' . addSQLParam($params, $period) . '
                    AND artr_year = ' . addSQLParam($params, $year) . "
                    AND trans_type IN ('INV', 'CRE')
                    GROUP BY ref_2, ref_3, ref_5) as a_ytd 
                ON a_ytd.ref_3 = property_income_accounts.accountID
                AND a_ytd.ref_2 = property_income_accounts.propID
                AND a_ytd.ref_5 = property_income_accounts.unitID
            LEFT JOIN 
                (SELECT pmrp_prop, pmrp_year, pmrp_unit, pmrp_acc, COALESCE(SUM(pmrp_b_a_amt), 0) as budgetYTD
                    FROM pmrp_b_rev_per
                    WHERE pmrp_prop = " . addSQLParam($params, $property) . '
                    AND pmrp_per <= ' . addSQLParam($params, $period) . '
                    AND pmrp_year = ' . addSQLParam($params, $year) . '
                    GROUP BY pmrp_prop, pmrp_year, pmrp_unit, pmrp_acc) as b_ytd
                ON b_ytd.pmrp_prop = property_income_accounts.propID
                AND b_ytd.pmrp_unit = property_income_accounts.unitID
            LEFT JOIN 
                (SELECT pmrp_prop, pmrp_year, pmrp_unit, pmrp_acc, COALESCE(SUM(pmrp_b_a_amt), 0) as budgetAnnual
                    FROM pmrp_b_rev_per
                    WHERE pmrp_prop = ' . addSQLParam($params, $property) . '
                    AND pmrp_year = ' . addSQLParam($params, $year) . '
                    GROUP BY pmrp_prop, pmrp_year, pmrp_unit, pmrp_acc) as b_annual
                ON b_annual.pmrp_prop = property_income_accounts.propID
                AND b_annual.pmrp_unit = property_income_accounts.unitID
            WHERE a.actual != 0 OR b.pmrp_b_a_amt != 0 OR a_ytd.actualYTD != 0 OR b_ytd.budgetYTD != 0 OR b_annual.budgetAnnual != 0
            GROUP BY propID , propName , unitID , unitName, accountID, accountName,a.actual, b.pmrp_b_a_amt,
                 a_ytd.actualYTD, b_ytd.budgetYTD, b_annual.budgetAnnual
            ORDER BY propID , propName , unitID , unitName, accountID';

    return $dbh->executeSet($sql, false, true, $params);
}

// ======================================================================================================

global $clientDB, $clientDirectory, $dbh, $pathPrefix, $sess;

if ($view->items['property']) {
    $properties = deserializeParameters($view->items['property']);
} else {
    $properties = [$propertyID];
}

$period = $view->items['period'];
$year = $view->items['year'];


$data = [];

foreach ($properties as $property) {
    // get ar_transactions and budget
    $ar_transactions = getTransactionsAndBudgets($property, $period, $year);
    foreach ($ar_transactions as $tran) {
        $variance = variance($tran['actual'], $tran['budget']);
        $variancePercent = varianceDecimal($variance, $tran['budget']);

        $varianceYTD = variance($tran['actualYTD'], $tran['budgetYTD']);
        $variancePercentYTD = varianceDecimal($varianceYTD, $tran['budgetYTD']);

        $data[] = [
            'bldgID' => $tran['bldgID'],
            'buildingName' => $tran['buildingName'],
            'suiteID' => $tran['suiteID'],
            'description' => $tran['description'],
            'period' => $tran['period'],
            'accountID' => $tran['accountID'],
            'acctName' => $tran['acctName'],
            'actual' => $tran['actual'] + 0,
            'budget' => $tran['budget'] + 0,
            'variance' => $variance,
            'variancePercent' => $variancePercent,
            'actualYTD' => $tran['actualYTD'] + 0,
            'budgetYTD' => $tran['budgetYTD'] + 0,
            'YTDVariance' => $varianceYTD,
            'YTDVariancePercent' => $variancePercentYTD,
            'annual' => $tran['budgetAnnual'] + 0,
        ];
    }
}

// ----------------------------------------------------------

// DOC_FLATFILES

$title = $s['subReportName'];
$filename = str_replace(' ', '_', $title) . '_' . date('dmYHis') . '.csv';
$_filePath = "{$pathPrefix}{$clientDirectory}/csv/" . DOC_FLATFILES . '/';
checkDirPath($_filePath);
$_downloadPath = "{$clientDirectory}/csv/" . DOC_FLATFILES;
$downloadLink = $_downloadPath . '/' . $filename; // for download link (used to display dl link on page)
$filePath = $_filePath . $filename; // for attachment (can used for email attachment also)

$fp = fopen($filePath, 'w');
$header = [
    'PROPERTY ID',
    'PROPERTY NAME',
    'SUITE ID',
    'DESCRIPTION',
    'PERIOD',
    'ACCOUNT CODE',
    'ACCOUNT NAME',
    'ACTUAL',
    'BUDGET',
    'VARIANCE',
    '% VARIANCE',
    'ACTUAL YTD',
    'BUDGET YTD',
    'YTD VARIANCE',
    'YTD % VARIANCE',
    'ANNUAL',
];

fwrite($fp, implode(',', $header) . PHP_EOL);
foreach ($data as $fields) {
    //    fwrite($fp, implode(',',$fields) . PHP_EOL);
    fputcsv($fp, $fields);
}
fclose($fp);

// for csvReportProcess
$attachments[] = [
    'title' => $title,
    'downloadPath' => $downloadLink,
    'filepath' => $filePath,
    'content_type' => 'text/csv',
];

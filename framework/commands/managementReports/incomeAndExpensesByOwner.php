<?php

include_once 'Residential/residentialReportFunctions.php';

global $_fonts, $reportDescription;
global $periodDescription; // added this in 2 Feb 09
global $propertyName;
global $clientDB;

$page++;
$totalGross_receipts = 0;
$totalGST_receipts = 0;
$totalNet_receipts = 0;
$totalNet_payments = 0;
$totalUCGST_receipts = 0;
$totalUCGross_receipts = 0;
$totalUCNet_receipts = 0;

$totalGST_payments = 0;
$totalGross_payments = 0;
$mychar  = "\n";

$prop_result = getAgentOwnerCodes($propertyID);
$agent_code = $prop_result['agent_code'];
$owner_code = $prop_result['owner_code'];

$agentDetails = getCompAddress($agent_code);
$ownerDetails = getCompAddress($owner_code);

$agent_name = $agentDetails['company_name'];
$agent_street = $agentDetails['street'];
$agent_city = $agentDetails['city'];
$agent_state = $agentDetails['state'];
$agent_postcode = $agentDetails['postcide'];
$agent_streets = explode("\n", $agent_street);

$owner_name = $ownerShareData['companyName'];
$yourShare = ($ownerShareData['ownerSharePercent']);

$line = 0;

$pdf->begin_page_ext(595, 842, '');
$pdf->setlinewidth(0.5);

// generate logo
if ($logo) {
    $logoObj = new ClientLogo($logoPath);
    $logoObj->preRender($pdf);
}

$bigLogoClient = BIGLOGOCLIENT;
$agentData = dbGetAgentDetails();
$agentDetailsNew = new agentDetails($propertyID, true, null, (in_array($clientDB, $bigLogoClient) ? 620 : 650));
$agentDetailsNew->bindAttributesFrom($agentData);
$agentDetailsNew->render($pdf);

$page_header = 'RESIDENTIAL PROPERTY REPORT';

$pdf->setFontExt($_fonts['Helvetica'], 8);

// ############# LOGO #############################
// include_once 'logo2.php';
// ###############################################


$pdf->show_xy('Owner: ', 40, 750 - $line);
$pdf->continue_text('Property: ');
$pdf->continue_text('Report for: ');
$pdf->continue_text('Your Share: ');

$pdf->show_xy("$owner_name", 105, 750 - $line);
$pdf->continue_text("$propertyName [$propertyID]");
$pdf->continue_text("$periodDescription");
$pdf->continue_text(formatting($yourShare) . '%');

$pdf->setFontExt($_fonts['Helvetica'], 9);

$line = $line + 10;
$line = $line + 10;
$pdf->setFontExt($_fonts['Helvetica'], 9);

$line = $line + 60;




$pdf->setFontExt($_fonts['Helvetica-Bold'], 9);
$pdf->showBoxed('Owners Statement', 100, 670, 400, 20, 'center', '');
$line = $line + 10;
// $pdf->showBoxed ("$propertyName", 180, 760-$line, 590, 10, "left", "");
// $line = $line + 10;
$pdf->setFontExt($_fonts['Helvetica'], 8);
$pdf->show_xy("Period From: $periodFrom To: $periodTo", 230, 665);
$line = $line + 40;

// /////////////////////

$moveRight = 20;
$moveLeft = 20;
$pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
$pdf->showBoxed('Income', 40 + $moveRight, 760 - $line, 590, 20, 'left', '');
$pdf->showBoxed('Total', 425 - $moveLeft, 760 - $line, 100, 20, 'center', '');
$pdf->showBoxed('Your Share', 450 - $moveLeft, 760 - $line, 100, 20, 'right', '');

$line = $line + 15;
$pdf->setlinewidth(0.5);
$receipts = getSimpleReportReceipts2($propertyID, $periodFrom, $periodTo, false, 'd.pmca_code,pmle_t_name');
if (empty($receipts)) {
    $receipts = getSimpleReportReceipts2($propertyID, $periodFrom, $periodTo, true, 'd.pmca_code,pmle_t_name');
}

$totalReceiptTax = 0;
$incomeData = [];
$totalIncome = 0;
$incomeTax = 0;
if ($receipts) {
    foreach ($receipts as $k => $thisRow) {
        $incomeData[$thisRow['account_name']][limit_name(character_special($thisRow['leaseName']), 38)][] = $thisRow;
    }

    foreach ($incomeData as $account => $dataLease) {
        if ($line > 715) {
            $pdf->setFontExt($_fonts['Helvetica'], 8);
            $pdf->showBoxed("Page $page", 479, 7, 75, 30, 'right', '');

            $traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'onePageOwnerReport', A4_PORTRAIT);
            $traccFooter->prerender($pdf);

            $pdf->end_page_ext('');
            $page++;

            $pdf->begin_page_ext(595, 842, '');
            $pdf->setlinewidth(0.5);
            if ($logo) {
                $logoObj = new ClientLogo($logoPath);
                $logoObj->preRender($pdf);
            }
            $line = 10;

            $pdf->setFontExt($_fonts['Helvetica-Bold'], 9);
            $pdf->showBoxed('Owners Statement (...)', 100, 670, 400, 20, 'center', '');
            $line = $line + 10;

            $pdf->setFontExt($_fonts['Helvetica'], 8);
            $pdf->show_xy("Period From: $periodFrom To: $periodTo", 230, 665);
            $line = 120;

            $pdf->show_xy('Owner: ', 40, 750);
            $pdf->continue_text('Property: ');
            $pdf->continue_text('Report for: ');
            $pdf->continue_text('Your Share: ');

            $pdf->show_xy("$owner_name", 105, 750);
            $pdf->continue_text("$propertyName [$propertyID]");
            $pdf->continue_text("$periodDescription");
            $pdf->continue_text(formatting($yourShare) . '%');


            $pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
            $pdf->showBoxed('Income', 40 + $moveRight, 760 - $line, 590, 20, 'left', '');
            $pdf->showBoxed('Total', 425 - $moveLeft, 760 - $line, 100, 20, 'center', '');
            $pdf->showBoxed('Your Share', 450 - $moveLeft, 760 - $line, 100, 20, 'right', '');

            $line = $line + 15;
        }

        $pdf->setFontExt($_fonts['Helvetica'], 8);
        $pdf->showBoxed($account, 40 + $moveRight, 760 - $line, 590, 20, 'left', '');
        //		$line = $line + 10;

        $subTotal = 0;
        $subTotalShare = 0;

        foreach ($dataLease as $lease => $data) {

            $net = 0;
            foreach ($data as $key => $v) {
                $net += $v['net'];
                $totalIncome += $v['gross'];
                $incomeTax += $v['gst'];
            }

            $subTotal += $net;
            $subTotalShare += $net * ($yourShare / 100);
        }

        $pdf->setFontExt($_fonts['Helvetica'], 8);

        $pdf->showBoxed(formatting($subTotal), 390 - $moveLeft, 760 - $line, 100, 20, 'right', '');
        $pdf->showBoxed(formatting($subTotalShare), 450 - $moveLeft, 760 - $line, 100, 20, 'right', '');

        $line = $line + 10;
    }

}

if ($subTotal) {

    $pdf->setFontExt($_fonts['Helvetica'], 8);
    $pdf->showBoxed($_SESSION['country_default']['tax_label'] . ' on Income', 40 + $moveRight, 760 - $line, 590, 20, 'left', '');
    $pdf->showBoxed(formatting($incomeTax), 390 - $moveLeft, 760 - $line, 100, 20, 'right', '');
    $pdf->showBoxed(formatting($incomeTax * ($yourShare / 100)), 450 - $moveLeft, 760 - $line, 100, 20, 'right', '');

    $line = $line + 10;
}


$pdf->setFontExt($_fonts['Helvetica-Bold'], 8);

$totalIncomeDisplay = formatting($totalIncome);
$totalIncomeShareDisplay = formatting($totalIncome * ($yourShare / 100));
$pdf->showBoxed('Total Income (Gross)', 40 + $moveRight, 756 - $line, 590, 20, 'left', '');
$pdf->showBoxed("$totalIncomeDisplay", 390 - $moveLeft, 756 - $line, 100, 20, 'right', '');
$pdf->showBoxed("$totalIncomeShareDisplay", 450 - $moveLeft, 756 - $line, 100, 20, 'right', '');
$pdf->setFontExt($_fonts['Helvetica'], 8);

$line = $line + 12;


$pdf->moveto(440 - $moveLeft, 789 - $line);
$pdf->lineto(490 - $moveLeft, 789 - $line);
$pdf->stroke();

$pdf->moveto(500 - $moveLeft, 789 - $line);
$pdf->lineto(550 - $moveLeft, 789 - $line);
$pdf->stroke();


$line = $line + 10;



if ($line > 715) { // 715
    $pdf->setFontExt($_fonts['Helvetica'], 8);
    $pdf->showBoxed("Page $page", 479, 7, 75, 30, 'right', '');

    $traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'onePageOwnerReport', A4_PORTRAIT);
    $traccFooter->prerender($pdf);

    $pdf->end_page_ext('');
    $page++;

    $pdf->begin_page_ext(595, 842, '');
    $pdf->setlinewidth(0.5);
    if ($logo) {
        $logoObj = new ClientLogo($logoPath);
        $logoObj->preRender($pdf);
    }
    $line = 10;

    $pdf->setFontExt($_fonts['Helvetica-Bold'], 9);
    $pdf->showBoxed('Owners Statement (...)', 100, 670, 400, 20, 'center', '');
    $line = $line + 10;
    //	$pdf->showBoxed ("$propertyName", 180, 760-$line, 590, 20, "left", "");
    //	$line = $line + 10;
    $pdf->setFontExt($_fonts['Helvetica'], 8);
    $pdf->show_xy("Period From: $periodFrom To: $periodTo", 230, 665);
    $line = 120;

    $pdf->show_xy('Owner: ', 40, 750);
    $pdf->continue_text('Property: ');
    $pdf->continue_text('Report for: ');
    $pdf->continue_text('Your Share: ');

    $pdf->show_xy("$owner_name", 105, 750);
    $pdf->continue_text("$propertyName [$propertyID]");
    $pdf->continue_text("$periodDescription");
    $pdf->continue_text(formatting($yourShare) . '%');
}



$line = $line + 10;
$pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
$pdf->showBoxed('Expenses', 40 + $moveRight, 760 - $line, 590, 20, 'left', '');




$line = $line + 25;
$expenseData = [];
$expenseDataGST = [];
$batchnr_result = getPayBatchNrs($propertyID, $periodFrom, $periodTo);
$batchByAccountGroup =  [];

foreach ($batchnr_result as $k => $v) {
    $accountGroup = ucwords(strtolower($v['accountGroup']));
    $batchByAccountGroup[$accountGroup][$k] = $v;
}

$i = 0;
$totalPaymentsTax = 0;

foreach ($batchByAccountGroup as $k => $v) {
    // $line = $line - 10;
    // $pdf->setFontExt ($_fonts['Helvetica-Bold'], 8);
    // $pdf->showBoxed ($k, 20, 760 - $line, 590, 20, 'left', '');
    // $line = $line + 20;

    foreach ($v as $thisLine) {
        $batch = $thisLine['pmxc_t_batch'];
        $linen = $thisLine['pmxc_t_line'];
        // $alloc_dt = $thisLine['pmxc_alloc_dt'];
        $alloc_dt = $thisLine['paymentDate'];
        $alloc_amt = $thisLine['pmxc_alloc_amt'];
        $alloc_tax = $thisLine['pmxc_tax_amt'];

        $i++;

        $details_result = getPaymentByAccountDetails($batch, $linen, $propertyID);

        $tdescription   = $details_result['description'];

        $date_from  = $details_result['spare_date_1'];
        $date_to   = $details_result['spare_date_2'];

        $trans_date     = $alloc_dt;
        $aptr_tax_amt  = $alloc_tax;

        $supplier_code = $details_result['creditor_code'];

        $payee_name =  $details_result['creditor_name'];
        $payee_name = limit_name($payee_name, 35);

        $invoice = $details_result['ref_1'];
        $net_amt = $alloc_amt * (-1) - $aptr_tax_amt;

        $date_from = dateFormattingPrintShort($date_from);
        $date_to = dateFormattingPrintShort($date_to);

        $net_amt_display = formatting($net_amt);
        $aptr_tax_amt_display  = formatting($aptr_tax_amt);
        $gross_amt = $alloc_amt * (-1);
        $gross_amt_display = formatting($gross_amt);

        $totalPaymentsTax += $aptr_tax_amt;

        $totalNet_payments = $totalNet_payments + $net_amt;
        $totalGST_payments = $totalGST_payments + $aptr_tax_amt;
        $totalGross_payments = $totalGross_payments + $gross_amt;

        $invoice = substr($invoice, 0, 10);
        $tdescription = limit_name(character_special($tdescription), 38); // 38

        $expenseData[$details_result['pmca_name']] += $net_amt;
        $expenseDataGST[$details_result['pmca_name']] += $aptr_tax_amt;

    }
}

ksort($expenseData);
foreach ($expenseData as $name => $value) {

    $pdf->setFontExt($_fonts['Helvetica'], 8);
    $pdf->showBoxed($name, 40 + $moveRight, 780 - $line, 150, 10, 'left', '');
    $pdf->showBoxed(formatting($value), 390 - $moveLeft, 760 - $line, 100, 30, 'right', '');
    $pdf->showBoxed(formatting($value * ($yourShare / 100)), 450 - $moveLeft, 760 - $line, 100, 30, 'right', '');


    $line = $line + 10;

    if ($line > 715) { // 715
        $pdf->setFontExt($_fonts['Helvetica'], 8);
        $pdf->showBoxed("Page $page", 479, 7, 75, 30, 'right', '');


        $traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'onePageOwnerReport', A4_PORTRAIT);
        $traccFooter->prerender($pdf);

        $pdf->end_page_ext('');
        $page++;

        $pdf->begin_page_ext(595, 842, '');
        $pdf->setlinewidth(0.5);
        if ($logo) {
            $logoObj = new ClientLogo($logoPath);
            $logoObj->preRender($pdf);
        }
        $line = 10;

        $pdf->setFontExt($_fonts['Helvetica-Bold'], 9);
        $pdf->showBoxed('Owners Statement (...)', 100, 670, 400, 20, 'center', '');
        $line = $line + 10;

        $pdf->setFontExt($_fonts['Helvetica'], 8);
        $pdf->show_xy("Period From: $periodFrom To: $periodTo", 230, 665);
        $line = 120;

        $pdf->show_xy('Owner: ', 40, 750);
        $pdf->continue_text('Property: ');
        $pdf->continue_text('Report for: ');
        $pdf->continue_text('Your Share: ');

        $pdf->show_xy("$owner_name", 105, 750);
        $pdf->continue_text("$propertyName [$propertyID]");
        $pdf->continue_text("$periodDescription");
        $pdf->continue_text(formatting($yourShare) . '%');

        $pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
        $pdf->showBoxed('Expenses', 40 + $moveRight, 760 - $line, 590, 20, 'left', '');

        $line = $line + 35;
    }

}


if ($totalGross_payments) {
    $taxTotalExpense = array_sum($expenseDataGST);
    $pdf->setFontExt($_fonts['Helvetica'], 8);
    $pdf->showBoxed($_SESSION['country_default']['tax_label'] . ' on Expenses', 40 + $moveRight, 780 - $line, 150, 10, 'left', '');
    $pdf->showBoxed(formatting($taxTotalExpense), 390 - $moveLeft, 760 - $line, 100, 30, 'right', '');
    $pdf->showBoxed(formatting($taxTotalExpense * ($yourShare / 100)), 450 - $moveLeft, 760 - $line, 100, 30, 'right', '');

    $line = $line + 15;
}

if ($line > 715) {// 600
    $pdf->setFontExt($_fonts['Helvetica'], 8);
    $pdf->showBoxed("Page $page", 479, 7, 75, 30, 'right', '');

    $traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'onePageOwnerReport', A4_PORTRAIT);
    $traccFooter->prerender($pdf);

    $pdf->end_page_ext('');
    $page++;

    $pdf->begin_page_ext(595, 842, '');
    $pdf->setlinewidth(0.5);
    if ($logo) {
        $logoObj = new ClientLogo($logoPath);
        $logoObj->preRender($pdf);
    }
    $line = 10;

    $pdf->setFontExt($_fonts['Helvetica-Bold'], 9);
    $pdf->showBoxed('Owners Statement (...)', 100, 670, 400, 20, 'center', '');
    $line = $line + 10;
    //	$pdf->showBoxed ("$propertyName", 180, 760-$line, 590, 20, "left", "");
    //	$line = $line + 10;
    $pdf->setFontExt($_fonts['Helvetica'], 8);
    $pdf->show_xy("Period From: $periodFrom To: $periodTo", 230, 665);
    $line = 120;

    $pdf->show_xy('Owner: ', 40, 750);
    $pdf->continue_text('Property: ');
    $pdf->continue_text('Report for: ');
    $pdf->continue_text('Your Share: ');

    $pdf->show_xy("$owner_name", 105, 750);
    $pdf->continue_text("$propertyName [$propertyID]");
    $pdf->continue_text("$periodDescription");
    $pdf->continue_text(formatting($yourShare) . '%');
}




$pdf->moveto(440 - $moveLeft, 791 - $line);
$pdf->lineto(490 - $moveLeft, 791 - $line);
$pdf->stroke();

$pdf->moveto(500 - $moveLeft, 791 - $line);
$pdf->lineto(550 - $moveLeft, 791 - $line);
$pdf->stroke();

$pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
$totalGross_payments_display = formatting($totalGross_payments);
$totalGrossShare_payments_display = formatting($totalGross_payments * ($yourShare / 100));
$pdf->showBoxed('Total Expenses (Gross)', 40 + $moveRight, 760 - $line, 590, 30, 'left', '');
$pdf->showBoxed("$totalGross_payments_display", 390 - $moveLeft, 760 - $line, 100, 31, 'right', '');
$pdf->showBoxed("$totalGrossShare_payments_display", 450 - $moveLeft, 760 - $line, 100, 31, 'right', '');


$line = $line + 20;

$cashmov = $totalIncome - $totalGross_payments;
$cashmov_display = formatting($cashmov);
$cashmovShare_display = formatting($cashmov * ($yourShare / 100));
$pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
$pdf->showBoxed('Net Balance', 40 + $moveRight, 760 - $line, 590, 30, 'left', '');
$pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
$pdf->showBoxed("$cashmov_display", 390 - $moveLeft, 760 - $line, 100, 30, 'right', '');
$pdf->showBoxed("$cashmovShare_display", 450 - $moveLeft, 760 - $line, 100, 30, 'right', '');

// $pdf->moveto(440-$moveLeft, 791-$line);
// 		$pdf->lineto(490-$moveLeft, 791-$line);
// 		$pdf->stroke();

// 		$pdf->moveto(500-$moveLeft, 791-$line);
// 		$pdf->lineto(550-$moveLeft, 791-$line);
// $pdf->stroke ();

// $line = $line + 4;

// $pdf->moveto(500-$moveLeft, 781-$line);
// $pdf->lineto(550-$moveLeft, 781-$line);
// $pdf->stroke ();

// $line = $line + 3;

// $pdf->moveto(500-$moveLeft, 781-$line);
// $pdf->lineto(550-$moveLeft, 781-$line);
// $pdf->stroke ();


// OPENING BALANCE
// ####################################################################################
// ################## CASH RECONCILIATION #############################################
// ####################################################################################

$receipts = total_cash_receipts($propertyID, $startFinancialYear, $previousPeriodTo);
$payments = total_cash_payments($propertyID, $startFinancialYear, $previousPeriodTo);

$receiptsA = total_cash_receipts($propertyID, STARTDATE, $financialPeriodToPY);
$paymentsA = total_cash_payments($propertyID, STARTDATE, $financialPeriodToPY);


$diffY = $receipts - $payments;
$diffA = $receiptsA - $paymentsA;
$diff = $diffA + $diffY;

$diff_display = formatting($diff);


$openingbalance = $diff;
$openingbalance_display = formatting($openingbalance);


$subtotal_balance = $cashmov + $openingbalance;

if ($line > 715) {// 715
    $pdf->setFontExt($_fonts['Helvetica'], 8);
    $pdf->showBoxed("Page $page", 479, 7, 75, 30, 'right', '');

    $traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'onePageOwnerReport', A4_PORTRAIT);
    $traccFooter->prerender($pdf);


    $pdf->end_page_ext('');
    $page++;

    $pdf->begin_page_ext(595, 842, '');
    $pdf->setlinewidth(0.5);
    if ($logo) {
        $logoObj = new ClientLogo($logoPath);
        $logoObj->preRender($pdf);
    }
    $line = 10;

    $pdf->setFontExt($_fonts['Helvetica-Bold'], 9);
    $pdf->showBoxed('Owners Statement (...)', 100, 670, 400, 20, 'center', '');
    $line = $line + 10;
    //	$pdf->showBoxed ("$propertyName", 180, 760-$line, 590, 20, "left", "");
    //	$line = $line + 10;
    $pdf->setFontExt($_fonts['Helvetica'], 8);
    $pdf->show_xy("Period From: $periodFrom To: $periodTo", 230, 665);
    $line = 120;

    $pdf->show_xy('Owner: ', 40, 750);
    $pdf->continue_text('Property: ');
    $pdf->continue_text('Report for: ');
    $pdf->continue_text('Your Share: ');

    $pdf->show_xy("$owner_name", 105, 750);
    $pdf->continue_text("$propertyName [$propertyID]");
    $pdf->continue_text("$periodDescription");
    $pdf->continue_text(formatting($yourShare) . '%');
}

$line = $line + 10;


$openingSharebalance_display = formatting($openingbalance * ($yourShare / 100));
$pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
$pdf->showBoxed('Opening Cash balance', 40 + $moveRight, 757 - $line, 590, 20, 'left', '');
$pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
$pdf->showBoxed("$openingbalance_display", 390 - $moveLeft, 757 - $line, 100, 22, 'right', '');
$pdf->showBoxed("$openingSharebalance_display", 450 - $moveLeft, 757 - $line, 100, 22, 'right', '');

$line = $line + 20;

if ($line > 715) {// 715
    $pdf->setFontExt($_fonts['Helvetica'], 8);
    $pdf->showBoxed("Page $page", 479, 7, 75, 30, 'right', '');

    $traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'onePageOwnerReport', A4_PORTRAIT);
    $traccFooter->prerender($pdf);


    $pdf->end_page_ext('');
    $page++;

    $pdf->begin_page_ext(595, 842, '');
    $pdf->setlinewidth(0.5);
    if ($logo) {
        $logoObj = new ClientLogo($logoPath);
        $logoObj->preRender($pdf);
    }
    $line = 10;

    $pdf->setFontExt($_fonts['Helvetica-Bold'], 9);
    $pdf->showBoxed('Owners Statement (...)', 100, 670, 400, 20, 'center', '');
    $line = $line + 10;
    //	$pdf->showBoxed ("$propertyName", 180, 760-$line, 590, 20, "left", "");
    //	$line = $line + 10;
    $pdf->setFontExt($_fonts['Helvetica'], 8);
    $pdf->show_xy("Period From: $periodFrom To: $periodTo", 230, 665);
    $line = 120;

    $pdf->show_xy('Owner: ', 40, 750);
    $pdf->continue_text('Property: ');
    $pdf->continue_text('Report for: ');
    $pdf->continue_text('Your Share: ');

    $pdf->show_xy("$owner_name", 105, 750);
    $pdf->continue_text("$propertyName [$propertyID]");
    $pdf->continue_text("$periodDescription");
    $pdf->continue_text(formatting($yourShare) . '%');
}

$beforeDistribution = $openingbalance + $cashmov;
$beforeDistributionShare_display = formatting($beforeDistribution * ($yourShare / 100));
$pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
$pdf->showBoxed('Funds Before Distributions', 40 + $moveRight, 757 - $line, 590, 20, 'left', '');
$pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
$pdf->showBoxed(formatting($beforeDistribution), 390 - $moveLeft, 757 - $line, 100, 22, 'right', '');
$pdf->showBoxed("$beforeDistributionShare_display", 450 - $moveLeft, 757 - $line, 100, 22, 'right', '');

$line = $line + 20;

if ($line > 715) {// 715
    $pdf->setFontExt($_fonts['Helvetica'], 8);
    $pdf->showBoxed("Page $page", 479, 7, 75, 30, 'right', '');

    $traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'onePageOwnerReport', A4_PORTRAIT);
    $traccFooter->prerender($pdf);


    $pdf->end_page_ext('');
    $page++;

    $pdf->begin_page_ext(595, 842, '');
    $pdf->setlinewidth(0.5);
    if ($logo) {
        $logoObj = new ClientLogo($logoPath);
        $logoObj->preRender($pdf);
    }
    $line = 10;

    $pdf->setFontExt($_fonts['Helvetica-Bold'], 9);
    $pdf->showBoxed('Owners Statement (...)', 100, 670, 400, 20, 'center', '');
    $line = $line + 10;
    $pdf->setFontExt($_fonts['Helvetica'], 8);
    $pdf->show_xy("Period From: $periodFrom To: $periodTo", 230, 665);
    $line = 120;

    $pdf->show_xy('Owner: ', 40, 750);
    $pdf->continue_text('Property: ');
    $pdf->continue_text('Report for: ');
    $pdf->continue_text('Your Share: ');

    $pdf->show_xy("$owner_name", 105, 750);
    $pdf->continue_text("$propertyName [$propertyID]");
    $pdf->continue_text("$periodDescription");
    $pdf->continue_text(formatting($yourShare) . '%');
}



// $pdf->moveto(500-$moveLeft, 781-$line);
// 		$pdf->lineto(550-$moveLeft, 781-$line);
// $pdf->stroke ();

// $pdf->setFontExt($_fonts["Helvetica-Bold"], 8);
// $pdf->showBoxed ("Your Share", 	 40+$moveRight, 758-$line, 590, 20, "left", '');
// $pdf->setFontExt($_fonts["Helvetica-Bold"], 8);
// $pdf->showBoxed (formatting( ($openingbalance* ($yourShare/100)) + ($cashmov* ($yourShare/100)) ),  450-$moveLeft, 758-$line, 100, 22, "right", '');



// $line = $line + 20;

if ($line > 715) {// 715
    $pdf->setFontExt($_fonts['Helvetica'], 8);
    $pdf->showBoxed("Page $page", 479, 7, 75, 30, 'right', '');

    $traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'onePageOwnerReport', A4_PORTRAIT);
    $traccFooter->prerender($pdf);


    $pdf->end_page_ext('');
    $page++;

    $pdf->begin_page_ext(595, 842, '');
    $pdf->setlinewidth(0.5);
    if ($logo) {
        $logoObj = new ClientLogo($logoPath);
        $logoObj->preRender($pdf);
    }
    $line = 10;

    $pdf->setFontExt($_fonts['Helvetica-Bold'], 9);
    $pdf->showBoxed('Owners Statement (...)', 100, 670, 400, 20, 'center', '');
    $line = $line + 10;
    $pdf->setFontExt($_fonts['Helvetica'], 8);
    $pdf->show_xy("Period From: $periodFrom To: $periodTo", 230, 665);
    $line = 120;

    $pdf->show_xy('Owner: ', 40, 750);
    $pdf->continue_text('Property: ');
    $pdf->continue_text('Report for: ');
    $pdf->continue_text('Your Share: ');

    $pdf->show_xy("$owner_name", 105, 750);
    $pdf->continue_text("$propertyName [$propertyID]");
    $pdf->continue_text("$periodDescription");
    $pdf->continue_text(formatting($yourShare) . '%');
}


// $pdf->moveto(500-$moveLeft, 781-$line);
// $pdf->lineto(550-$moveLeft, 781-$line);
// $pdf->stroke ();

// OWNER REMITTANCES

$creditorResult = getCreditorDetails($propertyID, $periodFrom, $periodTo);
$remmitanceShare = 0;
$remittancesTotal = 0;
foreach ($creditorResult as $key => $thisRow) {
    if ($ownerShareData['companyID'] == $thisRow['pmxc_s_creditor']) {
        $remmitanceShare += $thisRow['pmxc_alloc_amt'];
    }

    $remittancesTotal += $thisRow['pmxc_alloc_amt'];
}

$pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
$pdf->showBoxed('Funds transferred to your account', 40 + $moveRight, 758 - $line, 590, 20, 'left', '');
$pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
$pdf->showBoxed(formatting($remmitanceShare * -1), 450 - $moveLeft, 758 - $line, 100, 22, 'right', '');


$pdf->setFontExt($_fonts['Helvetica'], 8);
$pdf->showBoxed("Page $page", 479, 7, 75, 30, 'right', '');

$traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'onePageOwnerReport', A4_PORTRAIT);
$traccFooter->prerender($pdf);

$line = $line + 10;

// $pdf->moveto(500-$moveLeft, 791-$line);
// $pdf->lineto(550-$moveLeft, 791-$line);
// $pdf->stroke();

// $line = $line + 20;

if ($line > 715) {// 715
    $pdf->setFontExt($_fonts['Helvetica'], 8);
    $pdf->showBoxed("Page $page", 479, 7, 75, 30, 'right', '');

    $traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'onePageOwnerReport', A4_PORTRAIT);
    $traccFooter->prerender($pdf);


    $pdf->end_page_ext('');
    $page++;

    $pdf->begin_page_ext(595, 842, '');
    $pdf->setlinewidth(0.5);
    if ($logo) {
        $logoObj = new ClientLogo($logoPath);
        $logoObj->preRender($pdf);
    }
    $line = 10;

    $pdf->setFontExt($_fonts['Helvetica-Bold'], 9);
    $pdf->showBoxed('Owners Statement (...)', 100, 670, 400, 20, 'center', '');
    $line = $line + 10;
    //	$pdf->showBoxed ("$propertyName", 180, 760-$line, 590, 20, "left", "");
    //	$line = $line + 10;
    $pdf->setFontExt($_fonts['Helvetica'], 8);
    $pdf->show_xy("Period From: $periodFrom To: $periodTo", 230, 665);
    $line = 120;

    $pdf->show_xy('Owner: ', 40, 750);
    $pdf->continue_text('Property: ');
    $pdf->continue_text('Report for: ');
    $pdf->continue_text('Your Share: ');

    $pdf->show_xy("$owner_name", 105, 750);
    $pdf->continue_text("$propertyName [$propertyID]");
    $pdf->continue_text("$periodDescription");
    $pdf->continue_text(formatting($yourShare) . '%');
}


$line = $line + 10;


$pdf->moveto(500 - $moveLeft, 781 - $line);
$pdf->lineto(550 - $moveLeft, 781 - $line);

$pdf->moveto(455 - $moveLeft, 781 - $line);
$pdf->lineto(495 - $moveLeft, 781 - $line);
$pdf->stroke();

// CLOSING BALANCE
// echo "<Br>" . $ownerShareData['companyID'] . "-" .$subtotal_balance . " ---" . ($remmitanceShare*-1);
$closing_cashbalance = $subtotal_balance - ($remittancesTotal * -1);

$closing_cashbalance_display = formatting($closing_cashbalance);
$closingShare_cashbalance_display = formatting(($subtotal_balance * ($yourShare / 100)) - ($remmitanceShare * -1));
$pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
$pdf->showBoxed('Closing Cash balance', 40 + $moveRight, 757 - $line, 590, 20, 'left', '');
$pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
$pdf->showBoxed("$closing_cashbalance_display", 390 - $moveLeft, 757 - $line, 100, 22, 'right', '');
$pdf->showBoxed("$closingShare_cashbalance_display", 450 - $moveLeft, 757 - $line, 100, 22, 'right', '');

$line = $line + 14;

$pdf->moveto(455 - $moveLeft, 781 - $line);
$pdf->lineto(495 - $moveLeft, 781 - $line);
$pdf->stroke();

$pdf->moveto(500 - $moveLeft, 781 - $line);
$pdf->lineto(550 - $moveLeft, 781 - $line);
$pdf->stroke();

$line = $line + 3;

$pdf->moveto(455 - $moveLeft, 781 - $line);
$pdf->lineto(495 - $moveLeft, 781 - $line);
$pdf->stroke();

$pdf->moveto(500 - $moveLeft, 781 - $line);
$pdf->lineto(550 - $moveLeft, 781 - $line);
$pdf->stroke();



$pdf->end_page_ext('');

<?php

function getAPNTenancySchedule($properties, $period, $year)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);

    // AND c.pmla_id = a.pmrr_chargeAmountID
    $period_date = dbGetPeriodDate($properties[0], $period, $year);
    $last_period_date = $period_date['endDate'];

    $carBaysLicAccount = dbGetParam('CARBAYACC ', 'LICENSED');
    $carBaysCasAccount = dbGetParam('CARBAYACC ', 'CASUAL');
    if (is_array($properties) and count($properties ?? []) > 0) {
        $properties = implode("','", $properties);
    }

    //    $sql = "SET ANSI_NULLS, QUOTED_IDENTIFIER, CONCAT_NULL_YIELDS_NULL, ANSI_WARNINGS, ANSI_PADDING ON;
    //            SELECT d.pmpr_prop as propertyCode, d.pmpr_name as propertyName, e.pmle_lease as leaseCode, e.pmle_debtor as tenantCode, tenant.pmco_name as tenantName,
    //                tenantTypes.pmzz_desc as tenantType, tenantCategories.pmzz_desc as tenantCategory, f.pmpu_unit as unitCode, f.pmpu_desc as unitDesc, g.pmua_area as unitArea,
    //                e.pmle_com_dt as leaseCommence, e.pmle_exp_dt as leaseExpiry, e.pmle_com_dt as rentCommence,
    //                (CASE WHEN b.pmlc_chg_type = 'R' THEN c.pmla_amt ELSE 0 END) as baseRent,
    //                (CASE WHEN pmlc_acc = '$carBaysLicAccount' THEN pmla_parking ELSE 0 END) as carBaysLic,
    //                (CASE WHEN pmlc_acc = '$carBaysCasAccount' THEN pmla_parking ELSE 0 END) as carBaysCas,
    //                propertyTypes.pmzz_code as propertyType,
    //                b.pmlc_chg_type as chargeType, a.pmrr_rev_dt as reviewDate, a.pmrr_type as reviewID, rentReviewTypes.pmzz_desc as reviewDesc, a.pmrr_closed as reviewClosed,
    //                b.pmlc_acc as accountCode, accounts.pmca_name as accountDescription, c.pmla_amt as amount,
    //                (CASE WHEN b.pmlc_stop_dt = '2999-12-31' THEN '' ELSE CONVERT(char(10), pmlc_stop_dt, 103) END) as chargeStopDate
    //            FROM pmpr_property d
    //            JOIN pmpu_p_unit f
    //                ON d.pmpr_prop = f.pmpu_prop
    //                AND f.pmpu_tenancy = 0
    //            JOIN pmua_unit_area g ON (g.pmua_prop=f.pmpu_prop
    //                AND g.pmua_unit=f.pmpu_unit
    //                AND CONVERT(datetime,  '".$last_period_date."', 103) BETWEEN pmua_from_dt AND pmua_to_dt)
    //			LEFT JOIN pmrr_l_rent_rev a
    //                ON a.pmrr_prop = d.pmpr_prop
    //                AND a.pmrr_lease = g.pmua_lease
    //            LEFT JOIN pmle_lease e
    //				ON e.pmle_prop = d.pmpr_prop
    //                AND e.pmle_lease = g.pmua_lease
    //            LEFT JOIN pmlc_l_charge b
    //                ON b.pmlc_prop = d.pmpr_prop
    //                AND b.pmlc_lease = e.pmle_lease
    //                AND b.pmlc_serial = a.pmrr_charge
    //                AND b.pmlc_unit = f.pmpu_unit
    //            LEFT JOIN pmla_l_c_amt c
    //                ON c.pmla_serial = b.pmlc_serial
    //                AND c.pmla_prop = d.pmpr_prop
    //                AND c.pmla_lease = e.pmle_lease
    //                AND c.pmla_unit = f.pmpu_unit
    //                AND c.pmla_start_dt <= a.pmrr_rev_dt
    //				AND c.pmla_end_dt >= a.pmrr_rev_dt
    //            LEFT JOIN (SELECT * FROM pmzz_param
    //                    WHERE pmzz_par_type = 'PROPERTY') as propertyTypes
    //                ON propertyTypes.pmzz_code = d.pmpr_prop_type
    //            LEFT JOIN (SELECT * FROM pmzz_param
    //                    WHERE pmzz_par_type = 'PORTMGR') as portmgr
    //                ON portmgr.pmzz_code = d.pmpr_portfolio
    //            LEFT JOIN (SELECT
    //                        pmua_prop propertyCode,
    //                        COUNT(pmpu_unit) totalUnit,
    //                        SUM(pmpu_area) totalUnitArea
    //                    FROM
    //                        pmpu_p_unit
    //                    JOIN
    //                        pmua_unit_area ON (pmua_prop=pmpu_prop AND pmua_unit=pmpu_unit)
    //                    WHERE pmua_status IN ('O','V')
    //                        AND CONVERT(datetime, CONVERT(varchar, GETDATE(), 103), 103) BETWEEN pmua_from_dt AND pmua_to_dt
    //                        AND pmpu_prop IN ('". implode("','", $properties) ."')
    //                    GROUP BY
    //                        pmua_prop) as unitArea
    //                ON unitArea.propertyCode = d.pmpr_prop
    //            LEFT JOIN pmco_company tenant
    //                ON tenant.pmco_code = e.pmle_debtor
    //			LEFT JOIN (SELECT * FROM pmzz_param
    //                    WHERE pmzz_par_type = 'RENTREVIEW') as rentReviewTypes
    //                ON rentReviewTypes.pmzz_code = a.pmrr_type
    //			LEFT JOIN pmca_chart as accounts
    //				ON accounts.pmca_code = b.pmlc_acc
    //			LEFT JOIN (SELECT * FROM pmzz_param
    //                    WHERE pmzz_par_type = 'TENANT') as tenantTypes
    //                ON tenantTypes.pmzz_code = e.pmle_ten_type
    //             LEFT JOIN (SELECT * FROM pmzz_param
    //                    WHERE pmzz_par_type = 'DIVISION') as tenantCategories
    //                ON tenantCategories.pmzz_code = e.pmle_division
    //            WHERE pmpr_prop IN ('". implode("','", $properties) ."')
    //            ORDER BY d.pmpr_prop, f.pmpu_unit, e.pmle_lease, b.pmlc_acc";
    $params = [];
    $sql = " SELECT d.pmpr_prop as propertyCode, d.pmpr_name as propertyName, e.pmle_lease as leaseCode, e.pmle_debtor as tenantCode, tenant.pmco_name as tenantName, 
                tenantTypes.pmzz_desc as tenantType, tenantCategories.pmzz_desc as tenantCategory, f.pmpu_unit as unitCode, f.pmpu_desc as unitDesc, g.pmua_area as unitArea, 
                e.pmle_com_dt as leaseCommence, e.pmle_exp_dt as leaseExpiry, e.pmle_com_dt as rentCommence, 
                (CASE WHEN b.pmlc_chg_type = 'R' THEN c.pmla_amt ELSE 0 END) as baseRent,
                (CASE WHEN pmlc_acc = '$carBaysLicAccount' THEN pmla_parking ELSE 0 END) as carBaysLic, 
                (CASE WHEN pmlc_acc = '$carBaysCasAccount' THEN pmla_parking ELSE 0 END) as carBaysCas, 
                propertyTypes.pmzz_code as propertyType, 
                b.pmlc_chg_type as chargeType, a.pmrr_rev_dt as reviewDate, a.pmrr_type as reviewID, rentReviewTypes.pmzz_desc as reviewDesc, a.pmrr_closed as reviewClosed, 
                b.pmlc_acc as accountCode, accounts.pmca_name as accountDescription, c.pmla_amt as amount, 
                b.pmlc_stop_dt as chargeStopDate,
                a.pmrr_charge as serial
            FROM (SELECT 
					(CASE WHEN propLease.prop IS NOT NULL THEN propLease.prop ELSE propUnit.prop END) as propertyID,
					(CASE WHEN propLease.unit IS NOT NULL THEN propLease.unit ELSE propUnit.unit END) as unitID,
					(CASE WHEN propLease.lease IS NOT NULL THEN propLease.lease ELSE propUnit.lease END) as leaseID
					FROM 
						(SELECT pmle_prop as prop, cc.pmua_unit as unit, pmle_lease as lease
						FROM pmpr_property aa
						JOIN pmle_lease bb ON bb.pmle_prop = aa.pmpr_prop
						LEFT JOIN pmua_unit_area cc ON cc.pmua_prop = aa.pmpr_prop
						AND cc.pmua_lease = bb.pmle_lease
						WHERE pmpr_prop IN ('" . $properties . "')) as propLease
					 FULL OUTER JOIN
						(SELECT pmpu_prop as prop, pmpu_unit as unit, NULL as lease 
						FROM pmpr_property 
						JOIN pmpu_p_unit ON pmpr_prop = pmpu_prop
						WHERE pmpr_prop IN ('" . $properties . "')) as propUnit
					ON propLease.prop = propUnit.prop
					AND propLease.unit = propUnit.unit) as propUnitLease
			LEFT JOIN pmpr_property d
				ON d.pmpr_prop = propUnitLease.propertyID
            LEFT JOIN pmpu_p_unit f 
                ON f.pmpu_prop = propUnitLease.propertyID
				AND f.pmpu_unit =  propUnitLease.unitID
                AND f.pmpu_tenancy = 0
			LEFT JOIN pmle_lease e 
				ON e.pmle_prop = propUnitLease.propertyID
                AND e.pmle_lease = propUnitLease.leaseID
			LEFT JOIN pmrr_l_rent_rev a
                ON a.pmrr_prop = propUnitLease.propertyID
                AND a.pmrr_lease = propUnitLease.leaseID
            LEFT JOIN pmua_unit_area g ON (g.pmua_prop=f.pmpu_prop 
                AND g.pmua_unit = propUnitLease.unitID
                AND a.pmrr_rev_dt BETWEEN pmua_from_dt AND pmua_to_dt)
            LEFT JOIN pmlc_l_charge b
                ON b.pmlc_prop = d.pmpr_prop
                AND b.pmlc_lease = e.pmle_lease
                AND b.pmlc_serial = a.pmrr_charge
                AND b.pmlc_unit = f.pmpu_unit
            LEFT JOIN pmla_l_c_amt c
                ON c.pmla_serial = b.pmlc_serial
                AND c.pmla_prop = d.pmpr_prop
                AND c.pmla_lease = e.pmle_lease
                AND c.pmla_unit = f.pmpu_unit
                AND c.pmla_start_dt <= a.pmrr_rev_dt
				AND c.pmla_end_dt >= a.pmrr_rev_dt
            LEFT JOIN (SELECT * FROM pmzz_param
                    WHERE pmzz_par_type = 'PROPERTY') as propertyTypes
                ON propertyTypes.pmzz_code = d.pmpr_prop_type
            LEFT JOIN (SELECT * FROM pmzz_param
                    WHERE pmzz_par_type = 'PORTMGR') as portmgr 
                ON portmgr.pmzz_code = d.pmpr_portfolio
            LEFT JOIN pmco_company tenant
                ON tenant.pmco_code = e.pmle_debtor
			LEFT JOIN (SELECT * FROM pmzz_param
                    WHERE pmzz_par_type = 'RENTREVIEW') as rentReviewTypes
                ON rentReviewTypes.pmzz_code = a.pmrr_type
			LEFT JOIN pmca_chart as accounts
				ON accounts.pmca_code = b.pmlc_acc
			LEFT JOIN (SELECT * FROM pmzz_param
                    WHERE pmzz_par_type = 'TENANT') as tenantTypes
                ON tenantTypes.pmzz_code = e.pmle_ten_type
             LEFT JOIN (SELECT * FROM pmzz_param
                    WHERE pmzz_par_type = 'DIVISION') as tenantCategories
                ON tenantCategories.pmzz_code = e.pmle_division
            WHERE pmpr_prop IN ('" . $properties . "')
            ORDER BY d.pmpr_prop, f.pmpu_unit, e.pmle_lease, b.pmlc_acc";

    //    pre_print_r($sql);
    return $dbh->executeSet($sql, false, true, $params);
}

// ======================================================================================================

global $clientDB, $clientDirectory, $dbh, $pathPrefix, $sess;

if ($view->items['property']) {
    $properties = deserializeParameters($view->items['property']);
} else {
    $properties = [$propertyID];
}

$period = $view->items['period'];
$year = $view->items['year'];


$data = [];

// get ar_transactions and budget
$tenancyScheds = getAPNTenancySchedule($properties, $period, $year);

$prevPropID = '';
$prevLeaseID = '';
$prevUnitID = '';
$prevRevDate = '';

$tsCols = dbGetParams('TSCOL', true, true, ' ORDER BY CAST(pmzz_desc as INT)');

$colCount = 34;
$index = 0;

$numberFormat = '_(* #,##0.00_);_(* (#,##0.00);_(* "-"??_);_(@_)';
$numberFormatNoDecimal = '#,##0';
$numberFormatCols = [
    'BASE RENT',
    'BUILDING AREA',
    'AREA NLA',
    'AMOUNT',
    'UNITS',
];
$numberFormatNoDecimalCols = [
    'CAR BAYS LICENSED',
    'CAR BAYS CASUAL',
];

$currentPropLeaseUnit = $prevPropLeaseUnit = $nextPropLeaseUnit = '';

foreach ($tenancyScheds as $key => $tran) {
    $variance = variance($tran['actual'], $tran['budget']);
    $variancePercent = varianceDecimal($variance, $tran['budget']);

    $varianceYTD = variance($tran['actualYTD'], $tran['budgetYTD']);
    $variancePercentYTD = varianceDecimal($varianceYTD, $tran['budgetYTD']);

    $units = $tran['unitArea'];

    if ($tran['leaseCode']) {
        if ($tran['chargeType'] != 'R') {
            $units = $tran['carBaysLic'] + $tran['carBaysCas'];
        }
    }

    $currentPropLeaseUnit = $tran['propertyCode'] . '~' . $tran['leaseCode'] . '~' . $tran['unitCode'] . '~' . $tran['serial'];
    $prevPropLeaseUnit = $tenancyScheds[$key - 1]['propertyCode'] . '~' . $tenancyScheds[$key - 1]['leaseCode'] . '~' . $tenancyScheds[$key - 1]['unitCode'] . '~' . $tenancyScheds[$key - 1]['serial'];
    $nextPropLeaseUnit = $tenancyScheds[$key + 1]['propertyCode'] . '~' . $tenancyScheds[$key + 1]['leaseCode'] . '~' . $tenancyScheds[$key + 1]['unitCode'] . '~' . $tenancyScheds[$key + 1]['serial'];

    if ($currentPropLeaseUnit != $prevPropLeaseUnit) {
        $amount = '';
        if ($tran['propertyCode'] != '' and $tran['leaseCode'] != '' and $tran['unitCode'] != '' and $tran['serial'] != '' and $tran['leaseCommence'] != '') {
            $chargeDetails = dbGetChargeByPropertyLeaseUnitChargeID($tran['propertyCode'], $tran['leaseCode'], $tran['unitCode'], $tran['serial'], $tran['leaseCommence']);
            $amount = $chargeDetails['amount'];
        }

        $data[] = [
            'PROPERTY ID' => $tran['propertyCode'],
            'PROPERTY NAME' => $tran['propertyName'],
            'LEASE ID' => $tran['leaseCode'] ? $tran['propertyCode'] . '-' . $tran['leaseCode'] : '',
            'TENANT CODE' => $tran['tenantCode'],
            'TENANT NAME' => $tran['tenantName'],
            'TENANT TYPE' => $tran['tenantType'],
            'TENANT CATEGORY' => $tran['tenantCategory'],
            'AREA CODE' => $tran['unitCode'],
            'AREA DESCRIPTION' => $tran['unitDesc'],
            //        'AREA NLA' => $tran['unitArea'],
            'LEASE COMMENCE' => $tran['leaseCommence'],
            'LEASE EXPIRY' => $tran['leaseExpiry'],
            //        'RENT COMMENCEMENT' => $tran['rentCommence'],
            //        'BASE RENT' => $tran['baseRent'],
            //        'CAR BAYS LICENSED' => $tran['carBaysLic'],
            //        'CAR BAYS CASUAL' => $tran['carBaysCas'],
            //        'PROPERTY TYPE' => $tran['propertyType'],
            //        'PROPERTY TYPE DESC' => $tran['propertyDesc'],
            //        'TS NOTE' => $tran['leaseNote'],
            'UNITS' => $units,
            'REVIEW DATE' => $tran['leaseCommence'],
            'CHARGE STOP DATE' => $tran['chargeStopDate'],
            'REVIEW ID' => 'COMMENCEMENT',
            'REVIEW DESCRIPTION' => 'Commencement',
            'STATUS' => '',
            'ACCOUNT CODE' => $tran['accountCode'],
            'ACCOUNT DESCRIPTION' => $tran['accountDescription'],
            'AMOUNT' => $amount,
        ];
    }

    $data[] = [
        'PROPERTY ID' => $tran['propertyCode'],
        'PROPERTY NAME' => $tran['propertyName'],
        'LEASE ID' => $tran['leaseCode'] ? $tran['propertyCode'] . '-' . $tran['leaseCode'] : '',
        'TENANT CODE' => $tran['tenantCode'],
        'TENANT NAME' => $tran['tenantName'],
        'TENANT TYPE' => $tran['tenantType'],
        'TENANT CATEGORY' => $tran['tenantCategory'],
        'AREA CODE' => $tran['unitCode'],
        'AREA DESCRIPTION' => $tran['unitDesc'],
        //        'AREA NLA' => $tran['unitArea'],
        'LEASE COMMENCE' => $tran['leaseCommence'],
        'LEASE EXPIRY' => $tran['leaseExpiry'],
        //        'RENT COMMENCEMENT' => $tran['rentCommence'],
        //        'BASE RENT' => $tran['baseRent'],
        //        'CAR BAYS LICENSED' => $tran['carBaysLic'],
        //        'CAR BAYS CASUAL' => $tran['carBaysCas'],
        //        'PROPERTY TYPE' => $tran['propertyType'],
        //        'PROPERTY TYPE DESC' => $tran['propertyDesc'],
        //        'TS NOTE' => $tran['leaseNote'],
        'UNITS' => $units,
        'REVIEW DATE' => $tran['reviewDate'],
        'CHARGE STOP DATE' => $tran['chargeStopDate'],
        'REVIEW ID' => $tran['reviewID'],
        'REVIEW DESCRIPTION' => $tran['reviewDesc'],
        'STATUS' =>  $tran['leaseCode'] ? $tran['reviewClosed'] ? 'Closed' : 'Open' : '',
        'ACCOUNT CODE' => $tran['accountCode'],
        'ACCOUNT DESCRIPTION' => $tran['accountDescription'],
        'AMOUNT' => $tran['amount'],
    ];

    if ($currentPropLeaseUnit != $nextPropLeaseUnit) {
        $amount = '';
        if ($tran['propertyCode'] != '' and $tran['leaseCode'] != '' and $tran['unitCode'] != '' and $tran['serial'] != '' and $tran['leaseExpiry'] != '') {
            $chargeDetails = dbGetChargeByPropertyLeaseUnitChargeID($tran['propertyCode'], $tran['leaseCode'], $tran['unitCode'], $tran['serial'], $tran['leaseExpiry']);
            $amount = $chargeDetails['amount'];
        }
        $data[] = [
            'PROPERTY ID' => $tran['propertyCode'],
            'PROPERTY NAME' => $tran['propertyName'],
            'LEASE ID' => $tran['leaseCode'] ? $tran['propertyCode'] . '-' . $tran['leaseCode'] : '',
            'TENANT CODE' => $tran['tenantCode'],
            'TENANT NAME' => $tran['tenantName'],
            'TENANT TYPE' => $tran['tenantType'],
            'TENANT CATEGORY' => $tran['tenantCategory'],
            'AREA CODE' => $tran['unitCode'],
            'AREA DESCRIPTION' => $tran['unitDesc'],
            //        'AREA NLA' => $tran['unitArea'],
            'LEASE COMMENCE' => $tran['leaseCommence'],
            'LEASE EXPIRY' => $tran['leaseExpiry'],
            //        'RENT COMMENCEMENT' => $tran['rentCommence'],
            //        'BASE RENT' => $tran['baseRent'],
            //        'CAR BAYS LICENSED' => $tran['carBaysLic'],
            //        'CAR BAYS CASUAL' => $tran['carBaysCas'],
            //        'PROPERTY TYPE' => $tran['propertyType'],
            //        'PROPERTY TYPE DESC' => $tran['propertyDesc'],
            //        'TS NOTE' => $tran['leaseNote'],
            'UNITS' => $units,
            'REVIEW DATE' => $tran['leaseExpiry'],
            'CHARGE STOP DATE' => $tran['chargeStopDate'],
            'REVIEW ID' => 'EXPIRY',
            'REVIEW DESCRIPTION' => 'Expiry',
            'STATUS' => '',
            'ACCOUNT CODE' => $tran['accountCode'],
            'ACCOUNT DESCRIPTION' => $tran['accountDescription'],
            'AMOUNT' => $amount,
        ];
    }
}

// ----------------------------------------------------------

$title = $s['subReportName'];
$filename = str_replace(' ', '_', $title) . '_' . date('dmYHis') . '.xlsx';
$_filePath = "{$pathPrefix}{$clientDirectory}/xlsx/" . DOC_FLATFILES . '/';
checkDirPath($_filePath);
$_downloadPath = "{$clientDirectory}/xlsx/" . DOC_FLATFILES;
$downloadLink = $_downloadPath . '/' . $filename; // for download link (used to display dl link on page)
$filePath = $_filePath . $filename; // for attachment (can used for email attachment also)

$xls = new XLSDataReport($filePath, $title);
$xls->enableFormatting = true;

if (! empty($data)) {

    foreach (array_keys($data[0]) as $col) {
        if (strpos($col, '_charge') === false and strpos($col, '_amount') === false) {
            if (in_array($col, $numberFormatCols)) {
                $xls->addColumn($col, $col, 50, null, $numberFormat);
            } elseif (in_array($col, $numberFormatNoDecimalCols)) {
                $xls->addColumn($col, $col, 50, null, $numberFormatNoDecimal);
            } elseif ($col == 'TS_ NOTE') {
                $xls->addColumn($col, $col, 50, 'wrap');
            } else {
                $xls->addColumn($col, $col, 50, null, '@');
            }

        }
    }

    $xls->preparePage();
    $xls->renderHeader();
    $xls->renderData($data);
    $xls->close();

    $attachments[] = [
        'title' => $title,
        'downloadPath' => $downloadLink,
        'filepath' => $filePath,
        'content_type' => 'application/xlsx'];
}

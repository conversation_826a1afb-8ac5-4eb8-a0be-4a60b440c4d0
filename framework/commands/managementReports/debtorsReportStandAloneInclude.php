<?php

include_once 'functions/page2DebtorsDetailFunctions.php';

// initialise variables
// $line = $line + 500;
$line = 40;
$startline = $line;
$pdf->begin_page_ext(595, 842, '');
page_headerLS('new');
$line = -140;



$vacant_units = [];
$tempunits = [];
$t = 0;
$tenantcod = [];
$nepokazyvat = 0;
$bftotalC = 0;
$gstfreetotalC = 0;
$billedThistotalC = 0;
$gsttotalC = 0;
$paidgsttotalC = 0;
$paidThisMonthtotalC = 0;
$paidgstfreetotalC = 0;
$closing_balancetotalC = 0;
$unallocThisMonth = 0;

$intotable = "temp_$timestamp";

$units_result = getUnitDetails($propertyID, $periodFrom, $periodTo);
$units_number = count($units_result ?? []);


foreach ($units_result as $key => $thisRow) {


    $noprint = 0;
    $unit_code = $thisRow['pmpu_unit'];
    $unitdesc = $thisRow['pmpu_desc'];
    $tenantCode = $thisRow['pmlu_lease'];

    $tenantnameFull = $thisRow['tenant_name'];
    $tenantname = limit_name($tenantnameFull, 40);

    // echo "<br />NOW WORKING ON UNIT:$unit_code tenantNAME:<b> $tenantnameFull ($tenantname)</b>UNIT DESCRIPTION:<b>$unitdesc</b><br />";
    $status = $thisRow['status'];



    foreach ($tempunits as $unit) {
        if ($unit == $tenantCode) {
            $noprint++;
        }
    }

    $tempunits[$t] = $tenantCode;

    // Get Charge Dates

    $stopChargeDateRaw =  getStopChargeDate($propertyID, $unit_code, $tenantCode);
    $stop_charge_date = toNormalDate($stopChargeDateRaw);

    // echo "<br />Stop Charge date: $stopChargeDateRaw- $stop_charge_date<hr />";

    [$now_day, $now_month, $now_year] = explode('/', $periodTo);
    $nownumber = $now_year . $now_month . $now_day;

    if (strlen($stop_charge_date) > 0) {
        [$stop_cd_day, $stop_cd_month, $stop_cd_year] = explode('/', $stop_charge_date);
        $stopnumber = $stop_cd_year . $stop_cd_month . $stop_cd_day;
        // echo "<br />NOW: $nownumber- STOP NUMBER:$stopnumber<hr />";
    } else {
        $stopnumber = 0;
    }




    if ($status == 'L' && $nownumber >= $stopnumber) {
        $vacated = "(VACATED - $stop_charge_date)";
        // echo"this unit is $vacated<br />";
    } else {
        $vacated = '';
        // echo "this unit has NOT BEEN vacated<br />";
    }

    $notempty = false;
    $tenantcodeset = false;


    // initialise the totals
    $bftotal = 0;
    $gstfreetotal = 0;
    $billedThistotal = 0;
    $gsttotal = 0;
    $paidgsttotal = 0;
    $paidThisMonthtotal = 0;
    $paidgstfreetotal = 0;
    $closing_balancetotal = 0;


    // initialise the other values
    $bf = 0;
    $gstfree = 0;
    $billedThis = 0;
    $gst = 0;
    $paidgstfree = 0;
    $paidThisMonth = 0;
    $paidgst = 0;
    $closing_balance = 0;

    $cash = [];
    $paid = [];
    $billed = [];
    $acc = [];

    // ####################################################################

    $result = getBilledAccCode($tenantCode, $propertyID, $periodTo);
    $billedCodes = getAccCodes($result);

    $paid_result = getPaidAccCode($tenantCode, $propertyID, $periodTo);
    $paidCodes = getAccCodes($paid_result);

    $cash_result = getUnallocatedCashAccCode($tenantCode, $propertyID, $periodTo);
    $unallocCashCodes = getAccCodes($cash_result);

    $merged = array_merge($paidCodes, $billedCodes, $unallocCashCodes);
    $merged = array_unique($merged);
    asort($merged);

    // echo "<hr /> MERGED CODES :";  echo "<br />";

    $mynum = 0;
    $z = 0;
    $my = [];

    // ###################################################################

    /*if ($merged == array())
    {
        $merged[0] = "*********";
        echo "<hr /> MERGED LINE 162<br />";

    }*/



    // ########################################################################################################
    // ########################################################################################################
    // ####################################	  START		 ####################################################
    // #################################### START ACCOUNTS ####################################################
    // ########################################################################################################
    // ########################################################################################################

    foreach ($merged as $account) {
        $account_name = getAccountName($account);

        // set past flag to true to get a total of all previous periods
        $billed =  getBilled($account, $propertyID, $tenantCode, $periodFrom, $periodTo, true);
        // set past flag to false to get a total of current period
        $billedThis = getBilled($account, $propertyID, $tenantCode, $periodFrom, $periodTo, false);

        $paid =  getPaid($account, $propertyID, $tenantCode, $periodFrom);


        // ##########UNALLOCATED CASH#############################################################

        // set past flag to true to get a total of all previous periods
        $cash =  getUnallocCash($account, $propertyID, $tenantCode, $periodFrom, $periodTo, $past = true);
        // set past flag to false to get a total of current period
        $cashThis =  getUnallocCash($account, $propertyID, $tenantCode, $periodFrom, $periodTo, false);

        // cashThis and cash2 are the same
        $cash2 =  $cashThis;

        // ########################################################################################

        // billed this month GST free set flag to true
        $gstfree = getBilledThisMonthAll($propertyID, $tenantCode, $periodFrom, $periodTo, $account, true);
        // Note Billed this month with GST (flag false) not used currently

        // GST Only
        $gst = getBilledGST($propertyID, $tenantCode, $periodFrom, $periodTo, $account);

        // ########################################################################################

        $paidgstfree = getRecievedThisMonthAll($propertyID, $tenantCode, $periodFrom, $periodTo, $account, true);

        $paidThisMonth = getRecievedThisMonthAll($propertyID, $tenantCode, $periodFrom, $periodTo, $account, false);

        $unallocThisMonth = getUnallocThisMonthAll($propertyID, $tenantCode, $periodFrom, $periodTo, $account);
        // echo "<b><hr />$unallocThisMonth<vr /></b>";

        $paidgst = getGSTReceived($propertyID, $tenantCode, $periodFrom, $periodTo, $account);


        // echo "<hr /><b>(account_name)$account_name - (billed)$billed - (billedThis)$billedThis - (paid)$paid - (cash)$cash - (cashThis)$cashThis - (gstfree)$gstfree - (gst)$gst - (paidgstfree)$paidgstfree - (paidThisMonth)$paidThisMonth - (paidgst)$paidgst</b><hr />";

        // #########################END CALCULATIONS#############################################

        // brought forward
        $bf = $billed + $paid + $cash;
        $balance = formatting($bf, 2);
        $bf_display = $account;
        // echo "<br />THIS >>> $billedThis = (billedThis)$billedThis - (gst)$gst + (gstfree)$gstfree";
        $billedThis = $billedThis - $gst + $gstfree;
        $paidgstfree = $paidgstfree + $cash2;


        $paidThisMonth = $paidThisMonth + $paidgstfree - $unallocThisMonth;

        $gstfree_display = formatting($gstfree, 2);
        $billed_display = formatting($billedThis, 2);
        $gst_display = formatting($gst, 2);

        $paidgstfree = $paidgstfree * (-1);
        $paidThisMonth = $paidThisMonth * (-1);
        $paidgst = ($paidgst + $unallocThisMonth) * (-1);

        $paidgstfree_display = formatting($paidgstfree, 2);
        $paidThisMonth_display = formatting($paidThisMonth, 2);
        $paidgst_display = formatting($paidgst, 2);
        $closing_balance = $bf  + $billedThis + $gst - $paidThisMonth - $paidgst; // - $paidgstfree;
        $closing_balance_display = formatting($closing_balance, 2);

        // TOTALS

        $bftotal = $bftotal + $bf;
        $gstfreetotal = $gstfreetotal + $gstfree;
        $billedThistotal = $billedThistotal + $billedThis;
        $gsttotal = $gsttotal + $gst;
        $paidgsttotal = $paidgsttotal + $paidgst;
        $paidThisMonthtotal = $paidThisMonthtotal + $paidThisMonth;
        $paidgstfreetotal = $paidgstfreetotal + $paidgstfree;
        $closing_balancetotal = $closing_balancetotal + $closing_balance;

        // TOTALS COLLECTIONS

        $bftotalC = $bftotalC + $bf;
        $gstfreetotalC = $gstfreetotalC + $gstfree;
        $billedThistotalC = $billedThistotalC + $billedThis;
        $gsttotalC = $gsttotalC + $gst;
        $paidgsttotalC = $paidgsttotalC + $paidgst;
        $paidThisMonthtotalC = $paidThisMonthtotalC + $paidThisMonth;
        $paidgstfreetotalC = $paidgstfreetotalC + $paidgstfree;
        $closing_balancetotalC = $closing_balancetotalC + $closing_balance;


        // ########################################PDF #######################################

        if ($line >= 400) {
            $pdf->setlinewidth(0.5);
            $pdf->moveto(245, 715 - $startline);
            $pdf->lineto(245, 40);
            $pdf->stroke();

            $pdf->moveto(300, 715 - $startline);
            $pdf->lineto(300, 40);
            $pdf->stroke();

            $pdf->moveto(500, 715 - $startline);
            $pdf->lineto(500, 40);
            $pdf->stroke();

            $pdf->moveto(400, 715 - $startline);
            $pdf->lineto(400, 40);
            $pdf->stroke();
            $pdf->moveto(555, 715 - $startline);
            $pdf->lineto(555, 40);
            $pdf->stroke();
            $pdf->moveto(40, 40);
            $pdf->lineto(40, 715 - $startline);
            $pdf->stroke();

            $pdf->moveto(40, 40);
            $pdf->lineto(555, 40);
            $pdf->stroke();

            // insert tracc header
            $traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'owner_report', A4_PORTRAIT);
            $traccFooter->prerender($pdf);

            $line = 40;
            $startline = $line;
            $pdf->end_page_ext('');

            $pdf->begin_page_ext(595, 842, '');
            page_headerLS('cont');

            $line = -140;


            if ($tenantcodeset == true) {
                $pdf->setFontExt($_fonts['Helvetica'], 9);
                $pdf->showBoxed("$tenantname (...)", 50, 460 - $line, 370, 20, 'left', '');
            }
        }

        // ##########################################################################################################
        // ##########################################################################################################
        // ##########################################################################################################
        // ##########################################################################################################
        // ##########################################################################################################
        // ################################ ACCOUNTS WITH BALANCES GREATER THEN ZERO#################################
        // ##########################################################################################################
        // ##########################################################################################################
        // ##########################################################################################################
        // ##########################################################################################################


        if (
            ! (
                (
                    $balance == '0.00' &&
                    $billed_display == '0.00' &&
                    $gst_display == '0.00' &&
                    $paidThisMonth_display == '0.00' &&
                    $paidgst_display == '0.00' &&
                    $closing_balance_display == '0.00'
                )
            )
        ) {

            $unitcod_with_bal = $unit_code;
            $notempty = true;

            // echo "<hr /><b>LINE >>> $line</b><hr />";
            if (! $tenantcodeset) {
                // echo "<br /> LINE >>> $line ($account_name) <hr />";
                $pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
                $pdf->showBoxed("$unitdesc $vacated", 50, 470 - $line, 370, 20, 'left', '');
                $pdf->showBoxed("$tenantname", 50, 460 - $line, 470, 20, 'left', '');
                $tenantcodeset = true;
            }

            $pdf->setFontExt($_fonts['Helvetica'], 9);
            // $pdf->showBoxed ($bf_display, 22, 450-$line, 70, 20, "left", "");
            $pdf->showBoxed(ucfirst(strtolower($account_name)), 50, 450 - $line, 270, 20, 'left', '');
            $pdf->save();
            $pdf->setColorExt('fill', 'rgb', 1, 1, 1, 0);
            $pdf->rect(250, 450 - $line, 70, 18);
            $pdf->fill();
            $pdf->restore();
            $pdf->showBoxed($balance, 230, 450 - $line, 70, 20, 'right', '');
            // $pdf->showBoxed ($gstfree_display, 227, 450-$line, 170, 20, "right", "");
            $pdf->showBoxed($billed_display, 185, 450 - $line, 170, 20, 'right', '');
            $pdf->showBoxed($gst_display, 330, 450 - $line, 70, 20, 'right', '');
            // $pdf->showBoxed ($paidgstfree_display, 450, 450-$line, 170, 20, "right", "");
            $pdf->showBoxed($paidThisMonth_display, 385, 450 - $line, 70, 20, 'right', '');
            $pdf->showBoxed($paidgst_display, 430, 450 - $line, 70, 20, 'right', '');
            $pdf->showBoxed($closing_balance_display, 480, 450 - $line, 75, 20, 'right', '');

            $line = $line + 10;
        }

    } // end FOR EACH account


    // #################################### END ACCOUNTS ####################################################


    $bftotal_display = formatting($bftotal, 2);
    $gstfreetotal_display = formatting($gstfreetotal, 2);
    $billedThistotal_display = formatting($billedThistotal, 2);
    $gsttotal_display = formatting($gsttotal, 2);
    $paidgsttotal_display = formatting($paidgsttotal, 2);
    $paidThisMonthtotal_display = formatting($paidThisMonthtotal, 2);
    $paidgstfreetotal_display = formatting($paidgstfreetotal, 2);
    $closing_balancetotal_display = formatting($closing_balancetotal, 2);

    $unit_av_desc = getUnitDescription($propertyID, $unit_code);

    // ##########################################################################################################
    // ##########################################################################################################
    // ##########################################################################################################
    // ##########################################################################################################
    // ##########################################################################################################
    // ################################ ACCOUNTS WITH ALL BALANCES EQUAL TO ZERO#################################
    // ##########################################################################################################
    // ##########################################################################################################
    // ##########################################################################################################
    // ##########################################################################################################


    /*
            ||
                (
                    $bftotal_display == "0.00" &&
                    $billedThistotal_display == "0.00" &&
                    $gsttotal_display == "0.00" &&
                    $paidThisMonthtotal_display == "0.00" &&
                    $paidgsttotal_display == "0.00" &&
                    $closing_balancetotal_display == "0.00" &&
                    $notempty == false
                )

    */
    if (
        (
            (
                $bftotal_display == '0.00' &&
                $billedThistotal_display == '0.00' &&
                $gsttotal_display == '0.00' &&
                $paidThisMonthtotal_display == '0.00' &&
                $paidgsttotal_display == '0.00' &&
                $closing_balancetotal_display == '0.00' &&
                $notempty == false
            )
        )
    ) {
        // ##################################################################################################
        // ##################################################################################################
        // ################################ CHECK IF UNIT IS VACATED FOR THHE CURRENT PERIOD#################
        // ##################################################################################################

        $checkunit_result = checkUnitVacated($propertyID, $unit_code, $periodTo);
        $checkunit_number = count($checkunit_result ?? []);

        if ($checkunit_number > 0) {
            $unitcod_withOut_bal = $unit_code;

            // ###############check if this is the latest vacated date for this unit#######################
            // ###############check if this is the latest vacated date for this unit#######################
            // ###############check if this is the latest vacated date for this unit#######################

            $latestVacDate_result = checkLatestVacatedDate($propertyID, $unit_code);
            if (count($latestVacDate_result ?? []) > 0) {
                $row = $latestVacDate_result[0];
                $maxdate = $row['pmlu_occ_to_dt'];
                // $latestVacDate_number = count($latestVacDate_result);
            } else {
                $maxdate = '';
            }


            // echo "<br><br> MAX DATE: $maxdate and stop charge date = $stop_charge_date<br />";

            if ($maxdate == $stop_charge_date) {

                $unit_codetaken = $unit_code;

                $pdf->setFontExt($_fonts['Helvetica-Bold'], 9);
                $pdf->showBoxed("$unitdesc $vacated", 50, 470 - $line, 370, 20, 'left', '');
                // $pdf->showBoxed ("$tenantname", 50, 460-$line, 470, 20, "left", "");
                $pdf->showBoxed('Vacant', 50, 460 - $line, 470, 20, 'left', '');



                $line = $line - 7;
                $pdf->setColorExt('both', 'rgb', 0.8, 0.8, 0.8, 0);
                $pdf->rect(245, 445 - $line, 310, 15);
                $pdf->fill();
                $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);


                $pdf->setlinewidth(0.5);
                $pdf->moveto(245, 460 - $line);
                $pdf->lineto(555, 460 - $line);
                $pdf->stroke();
                $pdf->setFontExt($_fonts['Helvetica'], 9);
                // $pdf->showBoxed ('TOTAL', 22, 438-$line, 170, 20, "left", "");
                $pdf->showBoxed($bftotal_display, 230, 438 - $line, 70, 20, 'right', '');
                // $pdf->showBoxed ($gstfreetotal_display, 227, 438-$line, 170, 20, "right", "");
                $pdf->showBoxed($billedThistotal_display, 185, 438 - $line, 170, 20, 'right', '');
                $pdf->showBoxed($gsttotal_display, 330, 438 - $line, 70, 20, 'right', '');
                // $pdf->showBoxed ($paidgstfreetotal_display, 450, 438-$line, 170, 20, "right", "");
                $pdf->showBoxed($paidThisMonthtotal_display, 385, 438 - $line, 70, 20, 'right', '');
                $pdf->showBoxed($paidgsttotal_display, 430, 438 - $line, 70, 20, 'right', '');
                $pdf->showBoxed($closing_balancetotal_display, 480, 438 - $line, 75, 20, 'right', '');


                $pdf->moveto(245, 445 - $line);
                $pdf->lineto(555, 445 - $line);
                $pdf->stroke();
                $line = $line + 45;
            }

        } elseif ($unitcod_with_bal != $unit_code && $unitcod_withOut_bal = $unit_code) {
            // ###############this section deals with any left over units not taken care by above code###########
            // #########################in this case units with a ZERO balance but are STILL occupied###########

            $checkunit_result2 = checkOtherUnits($propertyID, $unit_code, $tenantCode, $periodTo);
            $checkunit_number2 = count($checkunit_result2 ?? []);

            if ($checkunit_number2 > 0) {

                $ucto = $unit_code;

                $pdf->setFontExt($_fonts['Helvetica-Bold'], 9);

                $pdf->showBoxed("$unitdesc", 50, 470 - $line, 370, 20, 'left', '');
                $pdf->showBoxed("$tenantname", 50, 460 - $line, 470, 20, 'left', '');

                $line = $line - 7;
                $pdf->setColorExt('both', 'rgb', 0.8, 0.8, 0.8, 0);
                $pdf->rect(245, 445 - $line, 310, 15);
                $pdf->fill();
                $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);


                $pdf->setlinewidth(0.5);
                $pdf->moveto(245, 460 - $line);
                $pdf->lineto(555, 460 - $line);
                $pdf->stroke();
                $pdf->setFontExt($_fonts['Helvetica'], 9);
                // $pdf->showBoxed ('TOTAL', 22, 438-$line, 170, 20, "left", "");
                $pdf->showBoxed($bftotal_display, 230, 438 - $line, 70, 20, 'right', '');
                // $pdf->showBoxed ($gstfreetotal_display, 227, 438-$line, 170, 20, "right", "");
                $pdf->showBoxed($billedThistotal_display, 185, 438 - $line, 170, 20, 'right', '');
                $pdf->showBoxed($gsttotal_display, 330, 438 - $line, 70, 20, 'right', '');
                // $pdf->showBoxed ($paidgstfreetotal_display, 450, 438-$line, 170, 20, "right", "");
                $pdf->showBoxed($paidThisMonthtotal_display, 385, 438 - $line, 70, 20, 'right', '');
                $pdf->showBoxed($paidgsttotal_display, 430, 438 - $line, 70, 20, 'right', '');
                $pdf->showBoxed($closing_balancetotal_display, 480, 438 - $line, 75, 20, 'right', '');


                $pdf->moveto(245, 445 - $line);
                $pdf->lineto(555, 445 - $line);
                $pdf->stroke();
                $line = $line + 45;
            }
        }
    } else {
        $line = $line - 7;
        $pdf->setColorExt('both', 'rgb', 0.8, 0.8, 0.8, 0);
        $pdf->rect(245, 445 - $line, 310, 15);
        $pdf->fill();
        $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);


        $pdf->setlinewidth(0.5);
        $pdf->moveto(245, 460 - $line);
        $pdf->lineto(555, 460 - $line);
        $pdf->stroke();
        $pdf->setFontExt($_fonts['Helvetica'], 9);
        // $pdf->showBoxed ('TOTAL', 22, 438-$line, 170, 20, "left", "");
        $pdf->showBoxed($bftotal_display, 230, 438 - $line, 70, 20, 'right', '');
        // $pdf->showBoxed ($gstfreetotal_display, 227, 438-$line, 170, 20, "right", "");
        $pdf->showBoxed($billedThistotal_display, 185, 438 - $line, 170, 20, 'right', '');
        $pdf->showBoxed($gsttotal_display, 330, 438 - $line, 70, 20, 'right', '');
        // $pdf->showBoxed ($paidgstfreetotal_display, 450, 438-$line, 170, 20, "right", "");
        $pdf->showBoxed($paidThisMonthtotal_display, 385, 438 - $line, 70, 20, 'right', '');
        $pdf->showBoxed($paidgsttotal_display, 430, 438 - $line, 70, 20, 'right', '');
        $pdf->showBoxed($closing_balancetotal_display, 480, 438 - $line, 75, 20, 'right', '');


        $pdf->moveto(245, 445 - $line);
        $pdf->lineto(555, 445 - $line);
        $pdf->stroke();
        $line = $line + 45;

    } // end else


    $t++;

} // end for each






// #############################################TOTALS COLLECTIONS DISPLAY FIGURES###################################
// #############################################TOTALS COLLECTIONS DISPLAY FIGURES###################################
// #############################################TOTALS COLLECTIONS DISPLAY FIGURES###################################
// #############################################TOTALS COLLECTIONS DISPLAY FIGURES###################################
// #############################################TOTALS COLLECTIONS DISPLAY FIGURES###################################
// #############################################TOTALS COLLECTIONS DISPLAY FIGURES###################################




$bftotalC_display = formatting($bftotalC, 2);
$gstfreetotalC_display = formatting($gstfreetotalC, 2);
$billedThistotalC_display = formatting($billedThistotalC, 2);
$gsttotalC_display = formatting($gsttotalC, 2);
$paidgsttotalC_display = formatting($paidgsttotalC, 2);
$paidThisMonthtotalC_display = formatting($paidThisMonthtotalC, 2);
$paidgstfreetotalC_display = formatting($paidgstfreetotalC, 2);
$closing_balancetotalC_display = formatting($closing_balancetotalC, 2);


if ($line >= 410) {


    // $line = $line + 5;
    $pdf->setlinewidth(0.5);
    $pdf->moveto(245, 715 - $startline);
    $pdf->lineto(245, 40);
    $pdf->stroke();

    $pdf->moveto(300, 715 - $startline);
    $pdf->lineto(300, 40);
    $pdf->stroke();

    $pdf->moveto(500, 715 - $startline);
    $pdf->lineto(500, 40);
    $pdf->stroke();

    $pdf->moveto(400, 715 - $startline);
    $pdf->lineto(400, 40);
    $pdf->stroke();
    $pdf->moveto(555, 715 - $startline);
    $pdf->lineto(555, 40);
    $pdf->stroke();
    $pdf->moveto(40, 40);      // tonkaja levaja
    $pdf->lineto(40, 715 - $startline);
    $pdf->stroke();
    $pdf->moveto(40, 40);      // tonkaja levaja
    $pdf->lineto(555, 40);
    $pdf->stroke();

    $line = 40;
    $startline = $line;

    $traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'owner_report', A4_PORTRAIT);
    $traccFooter->prerender($pdf);

    $pdf->end_page_ext('');

    $pdf->begin_page_ext(595, 842, '');
    page_headerLS('cont');

    $line = -140;


}



$line = $line - 30;
$pdf->setColorExt('both', 'rgb', 0.8, 0.8, 0.8, 0);
$pdf->rect(245, 430 - $line, 310, 20);
$pdf->fill();
$pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
$pdf->setlinewidth(0.5);

$pdf->moveto(245, 450 - $line);
$pdf->lineto(555, 450 - $line);
$pdf->stroke();

$pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
$pdf->showBoxed('TOTAL', 50, 425 - $line, 170, 20, 'left', '');
$pdf->showBoxed($bftotalC_display, 230, 425 - $line, 70, 20, 'right', '');
// $pdf->showBoxed ($gstfreetotalC_display, 227, 428-$line, 170, 20, "right", "");
$pdf->showBoxed($billedThistotalC_display, 185, 425 - $line, 170, 20, 'right', '');
$pdf->showBoxed($gsttotalC_display, 330, 426 - $line, 70, 20, 'right', '');
// $pdf->showBoxed ($paidgstfreetotalC_display, 450, 428-$line, 170, 20, "right", "");
$pdf->showBoxed($paidThisMonthtotalC_display, 385, 425 - $line, 70, 20, 'right', '');
$pdf->showBoxed($paidgsttotalC_display, 430, 425 - $line, 70, 20, 'right', '');
$pdf->showBoxed($closing_balancetotalC_display, 480, 425 - $line, 75, 20, 'right', '');

$pdf->setlinewidth(1);
$pdf->moveto(40, 430 - $line);
$pdf->lineto(555, 430 - $line);
$pdf->stroke();

$pdf->setlinewidth(0.5);

$pdf->moveto(245, 715 - $startline);
$pdf->lineto(245, 430 - $line);
$pdf->stroke();

$pdf->moveto(300, 715 - $startline);
$pdf->lineto(300, 430 - $line);
$pdf->stroke();

$pdf->moveto(500, 715 - $startline);
$pdf->lineto(500, 430 - $line);
$pdf->stroke();

$pdf->moveto(400, 715 - $startline);
$pdf->lineto(400, 430 - $line);
$pdf->stroke();

$pdf->moveto(555, 715 - $startline);
$pdf->lineto(555, 430 - $line);
$pdf->stroke();

$pdf->moveto(40, 430 - $line);      // tonkaja levaja
$pdf->lineto(40, 715 - $startline);
$pdf->stroke();

$traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'owner_report', A4_PORTRAIT);
$traccFooter->prerender($pdf);

$pdf->end_page_ext('');

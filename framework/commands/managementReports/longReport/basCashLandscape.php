<?php

include_once SYSTEMPATH . '/commands/managementReports/functions/page2DebtorsDetailFunctions.php';
$dbh->selectDatabase($clientDB);
$owner_remittance = "SELECT pmzz_desc
FROM pmzz_param
WHERE pmzz_par_type='OWNREACC'
";
$owner_remittance_acc = $dbh->executeSingle($owner_remittance);

$bas_basis = 'CASH';
$basis = 'C';
// /////////////////////////////// TOTAL RECEIPTS /////////////////////////////////////////////////////////
$tenantreceipts = "
		SELECT
			COALESCE(SUM(pmxd_alloc_amt), 0) gross_amount,
			COALESCE(SUM(pmxd_tax_amt), 0) gst_amount
		FROM
			pmxd_ar_alloc
		WHERE
			pmxd_f_type = 'CSH'
			AND pmxd_prop = ?
			AND pmxd_acc IS NOT NULL
			AND pmxd_alloc_dt BETWEEN CONVERT(datetime, ?, 103)
			AND CONVERT(datetime, ?, 103)
			AND pmxd_acc IN
			(
				SELECT
					pmcg_acc
				FROM
					pmcg_chart_grp
				WHERE (pmcg_grp = 'TRACC1')
                AND (pmcg_subgrp <> 'BSHEET'))";
$tenantreceipts_result = $dbh->executeSingle($tenantreceipts, [$propertyID, $periodFrom, $periodTo]);
$tenrec = $tenantreceipts_result['gross_amount'];
$gstrec = $tenantreceipts_result['gst_amount'];
$unalloc = "
		SELECT
			COALESCE(SUM(pmuc_amt), 0) AS 'gross_amount',
			COALESCE(SUM(pmuc_tax_amt), 0) AS 'tax_amount',
			COALESCE(SUM(pmuc_net_amt), 0) AS 'net_amount'
		FROM
			pmuc_unall_csh
		WHERE
			pmuc_prop=?
			AND pmuc_rcpt_dt BETWEEN CONVERT(datetime, ?, 103)
			AND CONVERT(datetime, ?, 103)
			AND pmuc_acc IN
			(
				SELECT
					pmcg_acc
				FROM
					pmcg_chart_grp
				WHERE (pmcg_grp = 'TRACC1')
                AND (pmcg_subgrp <> 'BSHEET'))";
$unalloc_result = $dbh->executeSingle($unalloc, [$propertyID, $periodFrom, $periodTo]);
$unallrec = $unalloc_result['gross_amount'];
$unallgst = $unalloc_result['tax_amount'];
$total_receipts = ($tenrec + $unallrec) * (-1);
$bas_gst_received = ($gstrec + $unallgst) * (-1);
$bas_tenant_receipts = formatting($total_receipts);
// TAXABLE non-operating receipts
$noneop_receipts_tax = dbTotalIncomePerGroup($propertyID, $periodFrom, $periodTo, 'BSREC', TAXABLE);
$bas_non_tenant_receipts = $noneop_receipts_tax['gross_amount'];
$bas_non_tenant_receipts_display = formatting($bas_non_tenant_receipts);
$total_receipts += $bas_non_tenant_receipts;
$bas_gst_received += $noneop_receipts_tax['gross_amount'] - $noneop_receipts_tax['net_amount'];
$G1 = formatting($total_receipts);
$gstfreetotal = 0;
// ///////////////////////////////////// GST FREE RECEIPTS ///////////////////////////////////////////////////////
$gstfreereceipts = "
		SELECT
			COALESCE(SUM(pmxd_alloc_amt), 0) amount
		FROM
			pmxd_ar_alloc
		WHERE
			pmxd_f_type = 'CSH'
			AND pmxd_prop = ?
			AND pmxd_acc IS NOT NULL
			AND pmxd_tax_amt = 0
			AND pmxd_alloc_dt BETWEEN CONVERT(datetime, ?, 103)
			AND CONVERT(datetime, ?, 103)
			AND pmxd_acc IN
			(
				SELECT
					pmcg_acc
				FROM
					pmcg_chart_grp
				WHERE (pmcg_grp = 'TRACC1')
                AND (pmcg_subgrp <> 'BSHEET'))";
$gstfreerec = $dbh->executeScalar($gstfreereceipts, [$propertyID, $periodFrom, $periodTo]);
$gstfreeunalloc = "
		SELECT
			COALESCE(SUM(pmuc_amt), 0) AS 'gross_amount'
		FROM
			pmuc_unall_csh
		WHERE
			(pmuc_prop=?)
			AND (pmuc_tax_amt= 0)
			AND (pmuc_rcpt_dt BETWEEN CONVERT(datetime, ?, 103)
			AND CONVERT(datetime, ?, 103))
			AND pmuc_acc IN
			(
				SELECT
					pmcg_acc
				FROM
					pmcg_chart_grp
				WHERE (pmcg_grp = 'TRACC1')
                AND (pmcg_subgrp <> 'BSHEET'))";
$gstfreeunallrec = $dbh->executeScalar($gstfreeunalloc, [$propertyID, $periodFrom, $periodTo]);
$gstfreetotal = ($gstfreerec + $gstfreeunallrec) * (-1);
// GSTFREE non-operating receipts
//    $noneop_receipts_tax = dbTotalIncomePerGroup($propertyID, $periodFrom, $periodTo, 'BSREC', GSTFREE);
//    $gstfreetotal += $noneop_receipts_tax['net_amount'];
$bas_gstfree_supplies_display = formatting($gstfreetotal);
$total_taxable = $total_receipts - $gstfreetotal;
$bas_gst_on_taxable = $total_taxable / 11;
$bas_gst_on_taxable_display = formatting($bas_gst_on_taxable);
$bas_total_taxable_display = formatting($total_taxable);
// ///////////////////////// ACQUISITIONS SIDE /////////////////////////////////////////////
// CAPITAL ACQUISITIONS INCLGST
$capacqGSTSQL = "
		SELECT
			COALESCE(SUM(pmxc_alloc_amt), 0) as amount
		FROM
			pmxc_ap_alloc
		WHERE
			pmxc_prop = ?
			AND (pmxc_f_type = 'PAY')
			AND (pmxc_alloc_dt BETWEEN CONVERT(datetime, ?, 103) AND CONVERT(datetime, ?, 103))
			AND pmxc_acc IN
			(
				SELECT
					pmcg_acc
				FROM
					pmcg_chart_grp
				WHERE (pmcg_grp = 'TRACC3')
                                AND pmxc_acc <> ?
				AND (pmcg_subgrp IN ('EXPOWNCAPI', 'BSPMTCAPI')))";
$capacqGST = $dbh->executeScalar(
    $capacqGSTSQL,
    [$propertyID, $periodFrom, $periodTo, $owner_remittance_acc['pmzz_desc']]
);
$capacqGST *= -1;
$capacqGST_display = formatting($capacqGST);
// OTHER ACQUISITIONS INCLGST
$otheracqGSTSQL = "
		SELECT
			COALESCE(SUM(pmxc_alloc_amt), 0) as amount
		FROM
			pmxc_ap_alloc
		WHERE
			pmxc_prop=?
			AND (pmxc_f_type = 'PAY')
			AND (pmxc_alloc_dt BETWEEN CONVERT(datetime, ?, 103) AND CONVERT(datetime, ?, 103))
			AND pmxc_acc IN
			(
				SELECT
					pmcg_acc
				FROM
					pmcg_chart_grp
				WHERE
					(pmcg_grp = 'TRACC3')
					AND (pmcg_subgrp <>'EXPOWNGST')
                                          AND pmxc_acc <> ?
					AND (pmcg_subgrp <>'EXPOWNCAPI')
					AND (pmcg_subgrp <>'BSPMTCAPI')
					AND (pmcg_subgrp <>'BSPMTFUND')
					AND (pmcg_subgrp <>'BSPMTOTH')
					AND (pmcg_subgrp <>'BSPMTREMI')
					AND (pmcg_subgrp <>'BSRECFUND')
					AND (pmcg_subgrp <> 'EXPOPTREMI'))";
$otheracqGST = $dbh->executeScalar(
    $otheracqGSTSQL,
    [$propertyID, $periodFrom, $periodTo, $owner_remittance_acc['pmzz_desc']]
);
$otheracqGST *= -1;
$bas_other_acquisitions = $otheracqGST;
$bas_other_acquisitions_display = formatting($bas_other_acquisitions);
$bas_total_acquisitions = $bas_other_acquisitions + $capacqGST;
$bas_total_acquisitions_display = formatting($bas_total_acquisitions);
$acNoGSTSQL = "
		SELECT
			COALESCE(SUM(pmxc_alloc_amt), 0) as amount
		FROM
			pmxc_ap_alloc
		WHERE
			pmxc_tax_amt = '0'
			AND pmxc_f_type = 'PAY'
			AND pmxc_prop = ?
			AND (pmxc_alloc_dt BETWEEN CONVERT(datetime, ?, 103) AND CONVERT(datetime, ?, 103))
			AND pmxc_acc IN
			(
				SELECT
					pmcg_acc
				FROM
					pmcg_chart_grp
				WHERE
					(pmcg_grp = 'TRACC3')
					AND (pmcg_subgrp <>'EXPOWNGST')
                    AND pmxc_acc <> ?
					AND (pmcg_subgrp <>'BSPMTOTH')
					AND (pmcg_subgrp <>'BSPMTREMI')
					AND (pmcg_subgrp <>'BSPMTFUND')
					AND (pmcg_subgrp <>'BSRECFUND')
					AND (pmcg_subgrp <> 'EXPOPTREMI'))";
//	pre_print_r($acNoGSTSQL);
$acNoGST = $dbh->executeScalar(
    $acNoGSTSQL,
    [$propertyID, $periodFrom, $periodTo, $owner_remittance_acc['pmzz_desc']]
);
$acNoGST *= -1;
$bas_acquisition_no_GST_display = formatting($acNoGST);
$bas_creditable = $bas_total_acquisitions - $acNoGST;
// Creditable acquisitions equals total aqusitions less gst free acquisitions
$bas_creditable_display = formatting($bas_creditable);
$bas_gst_on_creditable = $bas_creditable / 11;
$bas_amount_payable = $bas_gst_on_taxable - $bas_gst_on_creditable;
$bas_amount_payable_display = formatting($bas_amount_payable);
$bas_gst_on_creditable_display = formatting($bas_gst_on_creditable);
// /////////////////////////// TOTAL GST PAID FOR ERROR CHECKING ////////////////////////////////
$gstpaid = "
		SELECT
			COALESCE(SUM(pmxc_tax_amt), 0) as gst_amount
		FROM
			pmxc_ap_alloc
		WHERE
			pmxc_f_type = 'PAY'
			AND pmxc_prop = ?
			AND (pmxc_alloc_dt BETWEEN CONVERT(datetime, ?, 103) AND CONVERT(datetime, ?, 103))";
$gst_total = $dbh->executeScalar($gstpaid, [$propertyID, $periodFrom, $periodTo]);
$bas_gst_paid = $gst_total * (-1);
// ////////////////////////// SUMMARY AT THE BOTTOM /////////////////////////////////////////
if ($bas_gst_on_taxable < 0) {
    $bas_gst_on_taxable *= -1;
}

if ($bas_gst_received < 0) {
    $bas_gst_received *= -1;
}

if ($bas_gst_on_creditable < 0) {
    $bas_gst_on_creditable *= -1;
}

if ($bas_gst_paid < 0) {
    $bas_gst_paid *= -1;
}

$error_diff_s = $bas_gst_on_taxable - $bas_gst_received;
$error_diff_a = $bas_gst_on_creditable - $bas_gst_paid;
if ($error_diff_s < 0) {
    $error_diff_s *= -1;
}

// ////////////////////////////// START OF PDF ////////////////////////////////////////////////
$page++;
$pdf->begin_page_ext(842, 595, '');
$pdf->setFontExt($_fonts['Helvetica-Bold'], 9);
// //////////////////////////////////////////////////////////////////////////////////////////////////
$error_diff_a_display = formatting($error_diff_a);
$error_diff_s_display = formatting($error_diff_s);
if ($error_diff_s > 1) {
    $pdf->show_xy("difference of {$error_diff_s_display}", 340, 160);
}

if ($error_diff_a < 0) {
    $error_diff_a *= -1;
}

if ($error_diff_a > 1) {
    $pdf->show_xy("difference of {$error_diff_a_display}", 340, 145);
}

// /////////////////////////////// PDF CONTINUED //////////////////////////////////////////////////////////////////
$pdf->setColorExt('fill', 'rgb', 0.88, 0.88, 0.88, 0);
$pdf->rect(255, 202, 75, 15);
$pdf->fill();
$pdf->rect(255, 337, 75, 14);
$pdf->fill();
$pdf->rect(255, 397, 75, 14);
$pdf->fill();
$pdf->rect(255, 416, 75, 16);
$pdf->fill();
$pdf->rect(700, 292, 75, 15);
$pdf->fill();
$pdf->rect(700, 372, 75, 15);
$pdf->fill();
$pdf->rect(700, 442, 75, 15);
$pdf->fill();
$pdf->setColorExt('fill', 'rgb', 0, 0, 0, 0);
$pdf->setlinewidth(0.5);
// make the border of the rectangle a bit wider
// $pdf->rect(22, 480, 795, 80); //draw the rectangle
// $pdf->rect(22, 200, 345, 270); //draw the rectangle
// $pdf->rect(367, 200, 450, 270); //draw the rectangle
// $pdf->rect(30, 445, 330, 15); //draw the rectangle
// $pdf->rect(375, 445, 432, 15); //draw the rectangle
// $pdf->moveto(255, 426);
// $pdf->lineto(330, 426);
// $pdf->stroke();
// $pdf->rect(255, 397, 75, 45); //draw the rectangle
$pdf->rect(255, 341 + 75, 75, 45);
// draw the rectangle
$pdf->rect(255, 322 + 75, 75, 14);
// draw the rectangle
$pdf->rect(255, 302 + 75, 75, 14);
// draw the rectangle
$pdf->rect(255, 282 + 75, 75, 14);
// draw the rectangle
$pdf->rect(255, 262 + 75, 75, 14);
// draw the rectangle
// G rights
// $pdf->rect(335, 397+10, 25, 14); //draw the rectangle
//
// $pdf->rect(335, 341+10, 25, 45); //draw the rectangle
// $pdf->rect(335, 357+10, 25, 14); //draw the rectangle
$pdf->rect(255, 357 + 75, 75, 14);
// draw the rectangle
$pdf->rect(255, 397 + 75, 75, 14);
// draw the rectangle
// $pdf->rect(335, 322+10, 25, 14); //draw the rectangle
// $pdf->rect(335, 302+10, 25, 14); //draw the rectangle
// $pdf->rect(335, 282+10, 25, 14); //draw the rectangle
// $pdf->rect(335, 262+10, 25, 14); //draw the rectangle
// G LEFT
// $pdf->rect(205, 342+10, 45, 14); //draw the rectangle
// $pdf->rect(205, 322+10, 45, 14); //draw the rectangle
// $pdf->rect(205, 282+10, 45, 14); //draw the rectangle
// $pdf->rect(205, 262+10, 45, 14); //draw the rectangle
//
// //G ON ACQUISITIONSIDE
// $pdf->rect(782, 427-20, 25, 15); //draw the rectangle
// $pdf->rect(782, 412-20, 25, 15); //draw the rectangle
// $pdf->rect(782, 397-20, 25, 15); //draw the rectangle
//
// $pdf->rect(782, 372-20, 25, 15); //draw the rectangle
// $pdf->rect(782, 357-20, 25, 15); //draw the rectangle
// $pdf->rect(782, 342-20, 25, 15); //draw the rectangle
// $pdf->rect(782, 327-20, 25, 15); //draw the rectangle
//
//
// $pdf->rect(782, 307-20, 25, 14); //draw the rectangle
// $pdf->rect(782, 287-20, 25, 14); //draw the rectangle
// $pdf->rect(782, 267-20, 25, 14); //draw the rectangle
// $pdf->rect(782, 247-20, 25, 14); //draw the rectangle
// ACQUISITIONS RECTANGLES
$pdf->rect(700, 397 + 45, 75, 45);
// draw the rectangle
$pdf->rect(700, 412 + 45, 75, 15);
// draw the rectangle
$pdf->rect(700, 342 + 45, 75, 45);
// draw the rectangle
$pdf->rect(700, 327 + 45, 75, 15);
// draw the rectangle
$pdf->rect(700, 357 + 45, 75, 15);
// draw the rectangle
$pdf->rect(700, 307 + 45, 75, 15);
// draw the rectangle
$pdf->rect(700, 287 + 45, 75, 15);
// draw the rectangle
$pdf->rect(700, 267 + 45, 75, 15);
// draw the rectangle
$pdf->rect(700, 247 + 45, 75, 15);
// draw the rectangle
//
// $pdf->rect(618, 397-20, 75, 15); //draw the rectangle
// $pdf->rect(602, 327-20, 91, 15); //draw the rectangle
// $pdf->rect(618, 307-20, 75, 15); //draw the rectangle
// $pdf->rect(618, 267-20, 75, 15); //draw the rectangle
// $pdf->rect(618, 247-20, 75, 15); //draw the rectangle
// summary RECTS
$pdf->rect(255, 142 + 80, 75, 15);
// draw the rectangle
$pdf->rect(255, 157 + 80, 75, 15);
// draw the rectangle
$pdf->rect(255, 122 + 80, 75, 15);
// draw the rectangle
$pdf->stroke();
// stroke the path with the current color and line width
$pdf->showBoxed('Business Activity Statement Report', 271, 540, 300, 30, 'center', '');
$pdf->setFontExt($_fonts['Helvetica'], 8);
$pdf->show_xy('Owner:', 22, 540);
$pdf->continue_text('Property:');
$pdf->continue_text('Report For:');
$pdf->show_xy($client, 70, 540);
$pdf->continue_text($propertyName . " [{$propertyID}]");
$pdf->continue_text($periodDescription);
// $pdf->showBoxed("$propertyName", 171, 510,500,30,"center", "");
$bas_date = date('M Y');
// $pdf->showBoxed("$periodFrom_display - $periodTo_display", 271, 495,300,30,"center", "");
$pdf->setFontExt($_fonts['Helvetica'], 8);
$pdf->showBoxed("Period From: {$periodFrom} To: {$periodTo}", 271, 529, 300, 30, 'center', '');
// include "logobas.php";
$pdf->showBoxed('Basis: ' . ucwords(strtolower($bas_basis)), 271, 519, 300, 30, 'center', '');
$pdf->setFontExt($_fonts['Helvetica'], 9);
if ($logo) {
    generateLogo('landscape');
}

// added 2 Feb 09
// $pdf->setColorExt("both", "rgb", 0,0,0, 0);
$pdf->setFontExt($_fonts['Helvetica-Bold'], 9);
$pdf->show_xy($_SESSION['country_default']['tax_label'] . ' amounts you owe the Tax Office from Sales', 35, 500);
$pdf->show_xy(
    $_SESSION['country_default']['tax_label'] . ' amounts the Tax Office owes you from purchases',
    405,
    500
);
// $pdf->show_xy("Supplies (Property Income)", 145, 450);
// $pdf->show_xy("Acquisitions (Property Expenditure)", 530, 450);
// $pdf->show_xy("Tenant Receipts (GST incl)", 35, 430);
// $pdf->show_xy("Non Tenant Receipts (GST incl)", 35, 415);
$yLine = 75;
$pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
$pdf->show_xy('Receipts (Incl. any ' . $_SESSION['country_default']['tax_label'] . ')', 35, 400 + $yLine);
$pdf->show_xy('Less', 215, 375 + $yLine);
$pdf->setFontExt($_fonts['Helvetica'], 8);
$pdf->show_xy($_SESSION['country_default']['tax_label'] . ' Free Supplies', 35, 375 + $yLine);
$pdf->show_xy('Input Taxed Supplies', 35, 360 + $yLine);
$pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
$pdf->show_xy('Total ' . $_SESSION['country_default']['tax_label'] . ' Free Supplies', 35, 345 + $yLine);
$pdf->show_xy('Total Taxable Supplies', 35, 325 + $yLine);
$pdf->show_xy('Adjustments', 35, 305 + $yLine);
$pdf->show_xy('Adjusted Taxable Supplies', 35, 285 + $yLine);
$pdf->show_xy($_SESSION['country_default']['tax_label'] . ' on Taxable Supplies', 35, 265 + $yLine);
// $pdf->showBoxed("$bas_tenant_receipts", 255,410,75,30,"right", "");
// $pdf->showBoxed($bas_non_tenant_receipts_display, 255,395,75,30,"right", "");
$pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
$pdf->showBoxed($G1, 253, 380 + $yLine, 75, 30, 'right', '');
$pdf->showBoxed('G1', 280, 380 + $yLine, 75, 30, 'right', '');
$pdf->showBoxed('G3', 280, 355 + $yLine, 75, 30, 'right', '');
$pdf->showBoxed('G4', 280, 340 + $yLine, 75, 30, 'right', '');
$pdf->showBoxed('G5', 280, 325 + $yLine, 75, 30, 'right', '');
$pdf->showBoxed('G6', 280, 305 + $yLine, 75, 30, 'right', '');
$pdf->showBoxed('G7', 280, 285 + $yLine, 75, 30, 'right', '');
$pdf->showBoxed('G8', 280, 265 + $yLine, 75, 30, 'right', '');
$pdf->showBoxed('G9', 280, 245 + $yLine, 75, 30, 'right', '');
$pdf->showBoxed('G10', 730, 410 + 45, 75, 30, 'right', '');
$pdf->showBoxed('G11', 730, 395 + 45, 75, 30, 'right', '');
$pdf->showBoxed('G12', 730, 380 + 45, 75, 30, 'right', '');
$pdf->showBoxed('G13', 730, 355 + 45, 75, 30, 'right', '');
$pdf->showBoxed('G14', 730, 340 + 45, 75, 30, 'right', '');
$pdf->showBoxed('G15', 730, 325 + 45, 75, 30, 'right', '');
$pdf->showBoxed('G16', 730, 310 + 45, 75, 30, 'right', '');
$pdf->showBoxed('G17', 730, 290 + 45, 75, 30, 'right', '');
$pdf->showBoxed('G18', 730, 270 + 45, 75, 30, 'right', '');
$pdf->showBoxed('G19', 730, 250 + 45, 75, 30, 'right', '');
$pdf->showBoxed('G20', 730, 230 + 45, 75, 30, 'right', '');
$pdf->showBoxed('G3 + G4', 205, 325 + $yLine, 45, 30, 'center', '');
$pdf->showBoxed('G1 - G5', 205, 305 + $yLine, 45, 30, 'center', '');
$pdf->showBoxed('G6 + G7', 205, 265 + $yLine, 45, 30, 'center', '');
$pdf->showBoxed('G8/11', 205, 245 + $yLine, 45, 30, 'center', '');
// G ACQ
$pdf->showBoxed('G10 + G11', 605, 380 + 45, 100, 30, 'center', '');
$pdf->showBoxed('G13 + G14 + G15', 597, 310 + 45, 100, 30, 'center', '');
$pdf->showBoxed('G12 - G16', 605, 290 + 45, 100, 30, 'center', '');
$pdf->showBoxed('G17 - G18', 605, 250 + 45, 100, 30, 'center', '');
$pdf->showBoxed('G19/11', 605, 230 + 45, 100, 30, 'center', '');
// //////////////////////////// DISPLAYING THE RECEIPTS FIGURES IN THE PDF /////////////////////////////
$pdf->setFontExt($_fonts['Helvetica'], 8);
$pdf->showBoxed("{$bas_gstfree_supplies_display}", 253, 355 + $yLine, 75, 30, 'right', '');
$pdf->showBoxed('0.00', 253, 340 + $yLine, 75, 30, 'right', '');
$pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
$pdf->showBoxed("{$bas_gstfree_supplies_display}", 253, 325 + $yLine, 75, 30, 'right', '');
$pdf->showBoxed("{$bas_total_taxable_display}", 253, 305 + $yLine, 75, 30, 'right', '');
// $pdf->setFontExt($_fonts["Helvetica"], 9);
$pdf->showBoxed('0.00', 253, 285 + $yLine, 75, 30, 'right', '');
$pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
$pdf->showBoxed("{$bas_total_taxable_display}", 253, 265 + $yLine, 75, 30, 'right', '');
$pdf->showBoxed("{$bas_gst_on_taxable_display}", 253, 245 + $yLine, 75, 30, 'right', '');
// $pdf->setFontExt($_fonts["Helvetica"], 9);
// $pdf->showBoxed ("Printed on $date", 40, 5, 275, 30, "left", "");
// $pdf->showBoxed ("Page $page", 740, 5, 275, 30, "left", "");
// ACQUISITIONS SIDE
$pdf->setFontExt($_fonts['Helvetica'], 8);
$pdf->show_xy('Capital Acquisitions (' . $_SESSION['country_default']['tax_label'] . ' Incl)', 405, 430 + 45);
$pdf->show_xy('Other Acquisitions(' . $_SESSION['country_default']['tax_label'] . ' Incl)', 405, 415 + 45);
$pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
$pdf->show_xy('Total Acquisitions', 405, 400 + 45);
$pdf->show_xy('Less', 640, 375 + 45);
$pdf->setFontExt($_fonts['Helvetica'], 8);
$pdf->show_xy('Acquisitions for Input Taxed Supplies', 405, 375 + 45);
$pdf->show_xy('Acquisitions With No ' . $_SESSION['country_default']['tax_label'] . ' in Price', 405, 360 + 45);
$pdf->show_xy('Non Deductable Acquisitions', 405, 345 + 45);
$pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
$pdf->show_xy('Total Non Creditable Acquisitions', 405, 330 + 45);
$pdf->show_xy('Creditable Acquisitions', 405, 310 + 45);
$pdf->show_xy('Adjustments', 405, 290 + 45);
$pdf->show_xy('Adjusted Creditable Acquisitions', 405, 270 + 45);
$pdf->show_xy($_SESSION['country_default']['tax_label'] . ' on Creditable Acquisitions', 405, 250 + 45);
// SUMMARY
$pdf->show_xy('Summary', 35, 260);
$pdf->setFontExt($_fonts['Helvetica'], 8);
$pdf->show_xy('Total ' . $_SESSION['country_default']['tax_label'] . ' on Taxable Supplies', 35, 240);
$pdf->show_xy('Total ' . $_SESSION['country_default']['tax_label'] . ' on Creditable Acquisitions', 35, 225);
$pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
$pdf->show_xy('Amount Payable to/(Due from) ATO', 35, 205);
$pdf->setFontExt($_fonts['Helvetica'], 6);
$disclaimerSQL = '
            SELECT b.pmco_name as pmco_name
            FROM pmpr_property a
            LEFT JOIN pmco_company b
            ON a.pmpr_agent = b.pmco_code
            WHERE pmpr_prop = ?
            ';
$disclaimer_result = $dbh->executeSingle($disclaimerSQL, [$propertyID]);
$AgentCompanyName = $disclaimer_result['pmco_name'];
$disclaimerTitle = 'DISCLAIMER';
$disclaimerLine1 = 'The purpose of this report is to provide only those details of transactions for the property that have ';
$disclaimerLine2 = 'been processed by ' . $AgentCompanyName . ' as Managing Agent for the owner for the specified period. ';
$disclaimerLine3 = $AgentCompanyName . ' is in no way purporting any taxation advice with respect to the ' . $_SESSION['country_default']['tax_label'] . ' liabilities of the owner. ';
$disclaimerLine4 = 'It is strongly recommended that the owner seek separate, professional advice regarding any ' . $_SESSION['country_default']['tax_label'] . ' issues relating to this property.';
$allDisc = $disclaimerLine1 . $disclaimerLine2 . $disclaimerLine3 . $disclaimerLine4;
$count = substr_count($allDisc, ' ') / 4;
$disclaimerLine1 = '';
$disclaimerLine2 = '';
$disclaimerLine3 = '';
$disclaimerLine4 = '';
foreach (explode(' ', $allDisc) as $index => $rs) {
    if ($index <= $count) {
        $disclaimerLine1 .= $rs . ' ';
    } elseif ($index < $count * 2) {
        $disclaimerLine2 .= $rs . ' ';
    } elseif ($index < ($count * 3) + 4) {
        $disclaimerLine3 .= $rs . ' ';
    } else {
        $disclaimerLine4 .= $rs . ' ';
    }
}

$pdf->show_xy($disclaimerTitle, 22, 90 - 40);
$pdf->show_xy($disclaimerLine1, 22, 83 - 40);
$pdf->show_xy($disclaimerLine2, 22, 77 - 40);
$pdf->show_xy($disclaimerLine3, 22, 71 - 40);
$pdf->show_xy($disclaimerLine4, 22, 65 - 40);
// $pdf->show_xy($disclaimerLine5, 65, 59);
$pdf->setFontExt($_fonts['Helvetica'], 8);
$pdf->showBoxed("{$capacqGST_display}", 698, 410 + 45, 75, 30, 'right', '');
$pdf->showBoxed("{$bas_other_acquisitions_display}", 698, 395 + 45, 75, 30, 'right', '');
// $pdf->showBoxed("$bas_total_acquisitions_display", 698,395,75,30,"right", "");
// print "bas_other_acquisitions_display $bas_other_acquisitions_display<p>";
$pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
$pdf->showBoxed("{$bas_total_acquisitions_display}", 698, 380 + 45, 75, 30, 'right', '');
// Total acquisitions
// print "bas_total_acquisitions_display $bas_total_acquisitions_display<p>";
$pdf->setFontExt($_fonts['Helvetica'], 8);
$pdf->showBoxed('0.00', 698, 355 + 45, 75, 30, 'right', '');
$pdf->showBoxed(
    "{$bas_acquisition_no_GST_display}",
    698,
    340 + 45,
    75,
    30,
    'right',
    ''
);
// Acquisitions with no GST in Price
// print "$bas_acquisition_no_GST_display $bas_acquisition_no_GST_display";
$pdf->showBoxed('0.00', 698, 325 + 45, 75, 30, 'right', '');
$pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
$pdf->showBoxed("{$bas_acquisition_no_GST_display}", 698, 310 + 45, 75, 30, 'right', '');
$pdf->showBoxed("{$bas_creditable_display}", 698, 290 + 45, 75, 30, 'right', '');
$pdf->showBoxed('0.00', 698, 270 + 45, 75, 30, 'right', '');
$pdf->showBoxed("{$bas_creditable_display}", 698, 250 + 45, 75, 30, 'right', '');
$pdf->showBoxed("{$bas_gst_on_creditable_display}", 698, 230 + 45, 75, 30, 'right', '');
$pdf->setFontExt($_fonts['Helvetica'], 8);
$pdf->showBoxed("{$bas_gst_on_taxable_display}", 253, 139 + 80, 75, 30, 'right', '');
$pdf->showBoxed("{$bas_gst_on_creditable_display}", 253, 124 + 80, 75, 30, 'right', '');
$pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
$pdf->showBoxed("{$bas_amount_payable_display}", 253, 104 + 80, 75, 30, 'right', '');
// GST
// footer
$pdf->setFontExt($_fonts['Helvetica'], 8);
// $pdf->showBoxed ("Printed on $date", 40, 5, 275, 30, "left", "");
// $pdf->showBoxed ("Page $page", 740, 5, 275, 30, "left", "");
$pdf->showBoxed("Page {$page}", 750, 0, 75, 30, 'right', '');
$pdf->setlinewidth(0.5);
$pdf->moveto(18, 515);
$pdf->lineto(824, 515);
$pdf->stroke();
$pdf->moveto(18, 190);
$pdf->lineto(824, 190);
$pdf->stroke();
//
// left side bar line
$pdf->moveto(18, 515);
$pdf->lineto(18, 190);
$pdf->stroke();
// right side bar line
$pdf->moveto(824, 515);
$pdf->lineto(824, 190);
$pdf->stroke();
// first headers
$pdf->moveto(18, 493);
$pdf->lineto(824, 493);
$pdf->stroke();
// divider
$pdf->moveto(380, 515);
$pdf->lineto(380, 280);
$pdf->stroke();
// summary bottom
$pdf->moveto(18, 280);
$pdf->lineto(824, 280);
$pdf->stroke();
// insert tracc footer
$traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'BAS', A4_LANDSCAPE);
$traccFooter->prerender($pdf);
$basReportPages = $page;
$pdf->end_page_ext(''); // end of loop - if ($basis == "C")

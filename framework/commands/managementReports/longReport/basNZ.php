<?php

include_once SYSTEMPATH . '/commands/managementReports/functions/page2DebtorsDetailFunctions.php';
include_once SYSTEMPATH . '/commands/managementReports/Residential/residentialReportFunctions.php';
$basisSQL = 'SELECT pmpr_or_basis, pmpr_or_gstper FROM pmpr_property WHERE pmpr_prop = ?';
$dbh->selectDatabase($clientDB);
$basis_result = $dbh->executeSingle($basisSQL, [$propertyID]);
$basis = $basis_result['pmpr_or_basis'];

$owner_remittance = "SELECT pmzz_desc
FROM pmzz_param
WHERE pmzz_par_type='OWNREACC'
";
$owner_remittance_acc = $dbh->executeSingle($owner_remittance);

if ($basis == 'C') {
    $bas_basis = 'CASH';
}

if ($basis == 'A') {
    $bas_basis = 'ACCRUALS';
}

$params = [$propertyID, $periodFrom, $periodTo];
// $basis = "A";
if ($basis == 'C') {
    // /////////////////////////////// TOTAL RECEIPTS /////////////////////////////////////////////////////////
    $tenantreceipts = "
		SELECT
			COALESCE(SUM(pmxd_alloc_amt), 0) gross_amount,
			COALESCE(SUM(pmxd_tax_amt), 0) gst_amount
		FROM
			pmxd_ar_alloc
		WHERE
			pmxd_f_type = 'CSH'
			AND pmxd_prop = ?
			AND pmxd_acc IS NOT NULL
			AND pmxd_alloc_dt BETWEEN CONVERT(datetime, ?, 103)
			AND CONVERT(datetime, ?, 103)
			AND pmxd_acc IN
			(
				SELECT
					pmcg_acc
				FROM
					pmcg_chart_grp
				WHERE (pmcg_grp = 'TRACC1')
                AND (pmcg_subgrp <> 'BSHEET'))";

    $tenantreceipts_result = $dbh->executeSingle($tenantreceipts, $params);
    $tenrec = $tenantreceipts_result['gross_amount'];
    $gstrec = $tenantreceipts_result['gst_amount'];

    $unalloc = "
		SELECT
			COALESCE(SUM(pmuc_amt), 0) AS 'gross_amount',
			COALESCE(SUM(pmuc_tax_amt), 0) AS 'tax_amount',
			COALESCE(SUM(pmuc_net_amt), 0) AS 'net_amount'
		FROM
			pmuc_unall_csh
		WHERE
			pmuc_prop=?
			AND pmuc_rcpt_dt BETWEEN CONVERT(datetime, ?, 103)
			AND CONVERT(datetime, ?, 103)
			AND pmuc_acc IN
			(
				SELECT
					pmcg_acc
				FROM
					pmcg_chart_grp
				WHERE (pmcg_grp = 'TRACC1')
                AND (pmcg_subgrp <> 'BSHEET'))";

    $unalloc_result = $dbh->executeSingle($unalloc, $params);
    $unallrec = $unalloc_result['gross_amount'];
    $unallgst = $unalloc_result['tax_amount'];

    $total_receipts = ($tenrec + $unallrec) * (-1);
    $bas_gst_received = ($gstrec + $unallgst) * (-1);
    $bas_tenant_receipts = formatting($total_receipts);


    // TAXABLE non-operating receipts
    $noneop_receipts_tax = dbTotalIncomePerGroup($propertyID, $periodFrom, $periodTo, 'BSREC', TAXABLE);

    $bas_non_tenant_receipts = $noneop_receipts_tax['gross_amount'];
    $bas_non_tenant_receipts_display = formatting($bas_non_tenant_receipts);

    $total_receipts += $bas_non_tenant_receipts;
    $bas_gst_received += $noneop_receipts_tax['gross_amount'] - $noneop_receipts_tax['net_amount'];

    $G1 = formatting($total_receipts);
    $gstfreetotal = 0;
    // ///////////////////////////////////// GST FREE RECEIPTS ///////////////////////////////////////////////////////
    $gstfreereceipts = "
		SELECT
			COALESCE(SUM(pmxd_alloc_amt), 0) amount
		FROM
			pmxd_ar_alloc
		WHERE
			pmxd_f_type = 'CSH'
			AND pmxd_prop = ?
			AND pmxd_acc IS NOT NULL
			AND pmxd_tax_amt = 0
			AND pmxd_alloc_dt BETWEEN CONVERT(datetime, ?, 103)
			AND CONVERT(datetime, ?, 103)
			AND pmxd_acc IN
			(
				SELECT
					pmcg_acc
				FROM
					pmcg_chart_grp
				WHERE (pmcg_grp = 'TRACC1')
                AND (pmcg_subgrp <> 'BSHEET'))";

    $gstfreerec = $dbh->executeScalar($gstfreereceipts, $params);

    $gstfreeunalloc = "
		SELECT
			COALESCE(SUM(pmuc_amt), 0) AS 'gross_amount'
		FROM
			pmuc_unall_csh
		WHERE
			(pmuc_prop=?)
			AND (pmuc_tax_amt= 0)
			AND (pmuc_rcpt_dt BETWEEN CONVERT(datetime, ?, 103)
			AND CONVERT(datetime, ?, 103))
			AND pmuc_acc IN
			(
				SELECT
					pmcg_acc
				FROM
					pmcg_chart_grp
				WHERE (pmcg_grp = 'TRACC1')
                AND (pmcg_subgrp <> 'BSHEET'))";

    $gstfreeunallrec = $dbh->executeScalar($gstfreeunalloc, $params);

    $gstfreetotal = ($gstfreerec + $gstfreeunallrec) * (-1);

    // GSTFREE non-operating receipts
    //    $noneop_receipts_tax = dbTotalIncomePerGroup($propertyID, $periodFrom, $periodTo, 'BSREC', GSTFREE);
    //    $gstfreetotal += $noneop_receipts_tax['net_amount'];


    $bas_gstfree_supplies_display = formatting($gstfreetotal);

    $total_taxable = $total_receipts - $gstfreetotal;

    $bas_gst_on_taxable = $total_taxable / 11;
    $bas_gst_on_taxable_display = formatting($bas_gst_on_taxable);
    $bas_total_taxable_display = formatting($total_taxable);

    // ///////////////////////// ACQUISITIONS SIDE /////////////////////////////////////////////
    // CAPITAL ACQUISITIONS INCLGST
    $capacqGSTSQL = "
		SELECT
			COALESCE(SUM(pmxc_alloc_amt), 0) as amount
		FROM
			pmxc_ap_alloc
		WHERE
			pmxc_prop = ?
			AND (pmxc_f_type = 'PAY')
			and pmxc_tax_amt <> 0
			AND (pmxc_alloc_dt BETWEEN CONVERT(datetime, ?, 103) AND CONVERT(datetime, ?, 103))
			AND pmxc_acc IN
			(
				SELECT
					pmcg_acc
				FROM
					pmcg_chart_grp
				WHERE (pmcg_grp = 'TRACC3')
                                AND pmxc_acc <> ?
				AND (pmcg_subgrp IN ('EXPOWNCAPI', 'BSPMTCAPI')))";

    $capacqGST = $dbh->executeScalar(
        $capacqGSTSQL,
        [$propertyID, $periodFrom, $periodTo, $owner_remittance_acc['pmzz_desc']]
    );
    $capacqGST *= -1;
    $capacqGST_display = formatting($capacqGST);

    // OTHER ACQUISITIONS INCLGST
    $otheracqGSTSQL = "
		SELECT
			COALESCE(SUM(pmxc_alloc_amt), 0) as amount
		FROM
			pmxc_ap_alloc
		WHERE
			pmxc_prop=?
			AND (pmxc_f_type = 'PAY')
			AND (pmxc_alloc_dt BETWEEN CONVERT(datetime, ?, 103) AND CONVERT(datetime, ?, 103))
			and pmxc_tax_amt <> 0
			AND pmxc_acc IN
			(
				SELECT
					pmcg_acc
				FROM
					pmcg_chart_grp
				WHERE
					(pmcg_grp = 'TRACC3')
					AND (pmcg_subgrp <>'EXPOWNGST')
                                          AND pmxc_acc <> ?
					AND (pmcg_subgrp <>'EXPOWNCAPI')
					AND (pmcg_subgrp <>'BSPMTCAPI')
					AND (pmcg_subgrp <>'BSPMTFUND')
					AND (pmcg_subgrp <>'BSPMTOTH')
					AND (pmcg_subgrp <>'BSPMTREMI')
					AND (pmcg_subgrp <>'BSRECFUND')
					AND (pmcg_subgrp <> 'EXPOPTREMI'))";

    $otheracqGST = $dbh->executeScalar(
        $otheracqGSTSQL,
        [$propertyID, $periodFrom, $periodTo, $owner_remittance_acc['pmzz_desc']]
    );
    $otheracqGST *= -1;

    $bas_other_acquisitions = $otheracqGST;
    $bas_other_acquisitions_display = formatting($bas_other_acquisitions);

    $bas_total_acquisitions = $bas_other_acquisitions + $capacqGST;
    $bas_total_acquisitions_display = formatting($bas_total_acquisitions);

    $acNoGSTSQL = "
		SELECT
			COALESCE(SUM(pmxc_alloc_amt), 0) as amount
		FROM
			pmxc_ap_alloc
		WHERE
			pmxc_tax_amt = '0'
			AND pmxc_f_type = 'PAY'
			AND pmxc_prop = ?
			AND (pmxc_alloc_dt BETWEEN CONVERT(datetime, ?, 103) AND CONVERT(datetime, ?, 103))
			AND pmxc_acc IN
			(
				SELECT
					pmcg_acc
				FROM
					pmcg_chart_grp
				WHERE
					(pmcg_grp = 'TRACC3')
					AND (pmcg_subgrp <>'EXPOWNGST')
                    AND pmxc_acc <> ?
					AND (pmcg_subgrp <>'BSPMTOTH')
					AND (pmcg_subgrp <>'BSPMTREMI')
					AND (pmcg_subgrp <>'BSPMTFUND')
					AND (pmcg_subgrp <>'BSRECFUND')
					AND (pmcg_subgrp <> 'EXPOPTREMI'))";
    //	pre_print_r($acNoGSTSQL);
    $acNoGST = $dbh->executeScalar(
        $acNoGSTSQL,
        [$propertyID, $periodFrom, $periodTo, $owner_remittance_acc['pmzz_desc']]
    );
    $acNoGST *= -1;
    $bas_acquisition_no_GST_display = formatting($acNoGST);

    $bas_creditable = $bas_total_acquisitions - $acNoGST; // Creditable acquisitions equals total aqusitions less gst free acquisitions
    $bas_creditable_display = formatting($bas_creditable);
    $bas_gst_on_creditable = $bas_creditable / 11;
    $bas_amount_payable = $bas_gst_on_taxable - $bas_gst_on_creditable;
    $bas_amount_payable_display = formatting($bas_amount_payable);
    $bas_gst_on_creditable_display = formatting($bas_gst_on_creditable);

    // /////////////////////////// TOTAL GST PAID FOR ERROR CHECKING ////////////////////////////////
    $gstpaid = "
		SELECT
			COALESCE(SUM(pmxc_tax_amt), 0) as gst_amount
		FROM
			pmxc_ap_alloc
		WHERE
			pmxc_f_type = 'PAY'
			AND pmxc_prop = ?
			AND (pmxc_alloc_dt BETWEEN CONVERT(datetime, ?, 103) AND CONVERT(datetime, ?, 103))";
    $gst_total = $dbh->executeScalar($gstpaid, $params);
    $bas_gst_paid = $gst_total * (-1);

    // ////////////////////////// SUMMARY AT THE BOTTOM /////////////////////////////////////////

    if ($bas_gst_on_taxable < 0) {
        $bas_gst_on_taxable *= -1;
    }

    if ($bas_gst_received < 0) {
        $bas_gst_received *= -1;
    }

    if ($bas_gst_on_creditable < 0) {
        $bas_gst_on_creditable *= -1;
    }

    if ($bas_gst_paid < 0) {
        $bas_gst_paid *= -1;
    }


    // ////////////////////////////// START OF PDF ////////////////////////////////////////////////
    $page++;


    $pdf->begin_page_ext(595, 842, '');


    $pdf->setFontExt($_fonts['Helvetica-Bold'], 9);
    // //////////////////////////////////////////////////////////////////////////////////////////////////


    // /////////////////////////////// PDF CONTINUED //////////////////////////////////////////////////////////////////


    $pdf->setlinewidth(1); // make the border of the rectangle a bit wider

    $pdf->setColorExt('fill', 'rgb', 0, 0, 0, 0);

    $pdf->rect(395, 397 + 240, 75, 14); // draw the rectangle
    $pdf->rect(395, 377 + 240, 75, 14); // draw the rectangle
    $pdf->rect(395, 357 + 240, 75, 14); // draw the rectangle
    $pdf->rect(395, 337 + 240, 75, 14); // draw the rectangle
    $pdf->rect(395, 317 + 240, 75, 14); // draw the rectangle
    $pdf->rect(395, 297 + 240, 75, 14); // draw the rectangle

    // ACQUISITIONS RECTANGLES
    $pdf->rect(395, 412 + 20, 75, 15); // draw the rectangle
    $pdf->rect(395, 357 + 20, 75, 15); // draw the rectangle
    $pdf->rect(395, 337 + 20, 75, 15); // draw the rectangle
    $pdf->rect(395, 317 + 20, 75, 15); // draw the rectangle

    // summary RECTS
    $pdf->rect(395, 157 + 55, 75, 15); // draw the rectangle


    $pdf->stroke(); // stroke the path with the current color and line width
    $pdf->showBoxed('Business Activity Statement Report', 271 - 130, 510 + 185, 300, 30, 'center', '');
    $pdf->setFontExt($_fonts['Helvetica'], 8);
    // $pdf->showBoxed("$propertyName", 171-130, 510+185,500,30,"center", "");
    $bas_date = date('M Y');
    // $pdf->showBoxed("$periodFrom_display - $periodTo_display", 271, 495,300,30,"center", "");
    $pdf->showBoxed("Period From: {$periodFrom} To: {$periodTo}", 271 - 130, 495 + 185, 300, 30, 'center', '');
    // include "logobas.php";

    $pdf->showBoxed('Basis: ' . ucwords(strtolower($bas_basis)), 271 - 130, 480 + 185, 300, 30, 'center', '');


    $pdf->show_xy('Owner: ', 25, 750);
    $pdf->continue_text('Property: ');
    $pdf->continue_text('Report for: ');

    $pdf->show_xy("{$client}", 85, 750);
    $pdf->continue_text("{$propertyName} [{$propertyID}]");
    $pdf->continue_text("{$periodDescription}");

    if ($logo) {
        generateLogo();
    }// added 2 Feb 09

    // $pdf->setColorExt("both", "rgb", 0,0,0, 0);
    $pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
    $pdf->show_xy('Goods and Serives Tax on your Sales and Income', 100, 435 + 183 + 40);
    $pdf->show_xy('Goods and services tax on your purchases and expenses', 100, 430 + 40);

    $yLine = 240;
    $pdf->setFontExt($_fonts['Helvetica'], 8);
    $pdf->show_xy(
        'Total Sales and Income for the period (incl. ' . $_SESSION['country_default']['tax_label'] . ')',
        100,
        400 + $yLine
    );
    $pdf->show_xy('Zero Rated supplies included in Box 5', 100, 380 + $yLine);
    $pdf->show_xy('Subtract Box 6 from Box 5', 100, 360 + $yLine);
    $pdf->show_xy(
        'Divide Box 7 to calculate ' . $_SESSION['country_default']['tax_label'] . ' Component',
        100,
        340 + $yLine
    );
    $pdf->show_xy('Adjustment from Calculation sheets', 100, 320 + $yLine);
    $pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
    $pdf->show_xy('Total ' . $_SESSION['country_default']['tax_label'] . ' Collected', 100, 300 + $yLine);

    $pdf->setFontExt($_fonts['Helvetica'], 8);
    $pdf->showBoxed('(5)', 420, 380 + $yLine, 75, 30, 'right', '');
    $pdf->showBoxed('(6)', 420, 360 + $yLine, 75, 30, 'right', '');
    $pdf->showBoxed('(7)', 420, 340 + $yLine, 75, 30, 'right', '');
    $pdf->showBoxed('(8)', 420, 320 + $yLine, 75, 30, 'right', '');
    $pdf->showBoxed('(9)', 420, 300 + $yLine, 75, 30, 'right', '');
    $pdf->showBoxed('(10)', 420, 280 + $yLine, 75, 30, 'right', '');
    $pdf->showBoxed('(11)', 425, 395 + 20, 75, 30, 'right', '');
    $pdf->showBoxed('(12)', 425, 340 + 20, 75, 30, 'right', '');
    $pdf->showBoxed('(13)', 425, 320 + 20, 75, 30, 'right', '');
    $pdf->showBoxed('(14)', 425, 300 + 20, 75, 30, 'right', '');

    $pdf->showBoxed(
        'Total purchases and expenses (incl. ' . $_SESSION['country_default']['tax_label'] . ') for which tax invoices requirements have been met, excluding any imported goods',
        100,
        400 + 20,
        160,
        24,
        'left',
        ''
    );
    $pdf->show_xy(
        'Divide Box 11 to calculate ' . $_SESSION['country_default']['tax_label'] . ' Component',
        100,
        360 + 20
    );
    $pdf->show_xy('Adjustments', 100, 340 + 20);
    $pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
    $pdf->show_xy('Total ' . $_SESSION['country_default']['tax_label'] . ' Credit', 100, 320 + 20);


    $pdf->show_xy($_SESSION['country_default']['tax_label'] . ' to pay/(Due to you)', 100, 162 + 55);

    $seven = ($total_receipts - $gstfreetotal);
    $tenth = $bas_gst_received;
    $pdf->setFontExt($_fonts['Helvetica'], 8);
    $pdf->showBoxed($G1, 392, 380 + $yLine, 75, 30, 'right', '');
    $pdf->showBoxed("{$bas_gstfree_supplies_display}", 392, 360 + $yLine, 75, 30, 'right', '');
    $pdf->showBoxed(formatting($seven), 392, 340 + $yLine, 75, 30, 'right', '');
    $pdf->showBoxed(formatting($seven * 3 / 23), 392, 320 + $yLine, 75, 30, 'right', '');
    $pdf->showBoxed(formatting($tenth - ($seven * 3 / 23)), 392, 300 + $yLine, 75, 30, 'right', '');
    $pdf->showBoxed(formatting($tenth), 392, 280 + $yLine, 75, 30, 'right', '');


    $pdf->setFontExt($_fonts['Helvetica'], 6);
    $disclaimerSQL = '
            SELECT b.pmco_name as pmco_name
            FROM pmpr_property a
            LEFT JOIN pmco_company b
            ON a.pmpr_agent = b.pmco_code
            WHERE pmpr_prop = ?
            ';
    $disclaimer_result = $dbh->executeSingle($disclaimerSQL, [$propertyID]);
    $AgentCompanyName = $disclaimer_result['pmco_name'];

    $disclaimerTitle = 'DISCLAIMER';
    $disclaimerLine1 = 'The purpose of this report is to provide only those details of transactions for the property that have ';
    $disclaimerLine2 = 'been processed by ' . $AgentCompanyName . ' as Managing Agent for the owner for the specified period.';
    $disclaimerLine3 = $AgentCompanyName . ' is in no way purporting any taxation advice with respect to the ' . $_SESSION['country_default']['tax_label'] . ' liabilities of the owner. ';
    $disclaimerLine4 = 'It is strongly recommended that the owner seek separate, professional advice regarding any ' . $_SESSION['country_default']['tax_label'] . ' issues relating to this property.';

    $allDisc = $disclaimerLine1 . $disclaimerLine2 . $disclaimerLine3 . $disclaimerLine4;
    $count = substr_count($allDisc, ' ') / 4;
    $disclaimerLine1 = '';
    $disclaimerLine2 = '';
    $disclaimerLine3 = '';
    $disclaimerLine4 = '';
    foreach (explode(' ', $allDisc) as $index => $rs) {
        if ($index <= $count) {
            $disclaimerLine1 .= $rs . ' ';
        } elseif ($index < $count * 2) {
            $disclaimerLine2 .= $rs . ' ';
        } elseif ($index < ($count * 3) + 4) {
            $disclaimerLine3 .= $rs . ' ';
        } else {
            $disclaimerLine4 .= $rs . ' ';
        }
    }

    $pdf->show_xy($disclaimerTitle, 65, 90);
    $pdf->show_xy($disclaimerLine1, 65, 83);
    $pdf->show_xy($disclaimerLine2, 65, 77);
    $pdf->show_xy($disclaimerLine3, 65, 71);
    $pdf->show_xy($disclaimerLine4, 65, 65);

    $pdf->setFontExt($_fonts['Helvetica'], 8);
    $pdf->showBoxed("{$bas_total_acquisitions_display}", 392, 395 + 20, 75, 30, 'right', ''); // Total acquisitions

    // START PURCHASE AND EXPENSES
    $batchnr_result = getPayBatchNrs($propertyID, $periodFrom, $periodTo);
    $purchaseGstTotal = 0;

    foreach ($batchnr_result as $thisLine) {
        $purchaseGstTotal += $thisLine['pmxc_tax_amt'];
    }

    // END PURCHASE AND EXPENSES

    $fourteen = $purchaseGstTotal;
    $twelve = ($bas_total_acquisitions * 3) / 23;
    $pdf->showBoxed(formatting($twelve), 392, 340 + 20, 75, 30, 'right', ''); // Divide Box 11 to calculate GST Component
    $pdf->showBoxed(formatting($fourteen - $twelve), 392, 320 + 20, 75, 30, 'right', '');
    $pdf->showBoxed(formatting($fourteen), 392, 300 + 20, 75, 30, 'right', '');
    $dueToYou = formatting($tenth - $fourteen);
    $pdf->showBoxed("{$dueToYou}", 392, 154 + 55, 75, 15, 'right', '');

    // footer
    $pdf->setFontExt($_fonts['Helvetica'], 8);
    $pdf->showBoxed("Page {$page}", 470, 2, 75, 30, 'right', '');

    $pdf->setlinewidth(0.5);
    $pdf->moveto(65, 635 + 40);
    $pdf->lineto(530, 635 + 40);
    $pdf->stroke();
    // middle line
    $pdf->moveto(65, 445 + 40);
    $pdf->lineto(530, 445 + 40);
    $pdf->stroke();
    // bottom line
    $pdf->moveto(65, 210 + 40);
    $pdf->lineto(530, 210 + 40);
    $pdf->stroke();

    // left side bar line
    $pdf->moveto(65, 210 - 50);
    $pdf->lineto(65, 635 + 40);
    $pdf->stroke();

    // right side bar line
    $pdf->moveto(530, 210 - 50);
    $pdf->lineto(530, 635 + 40);
    $pdf->stroke();

    // first headers
    $pdf->moveto(65, 614 + 40);
    $pdf->lineto(530, 614 + 40);
    $pdf->stroke();


    // second headers
    $pdf->moveto(65, 425 + 40);
    $pdf->lineto(530, 425 + 40);
    $pdf->stroke();

    // summary bottom
    $pdf->moveto(65, 160);
    $pdf->lineto(530, 160);
    $pdf->stroke();

    // insert tracc footer
    $traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'GST', A4_PORTRAIT);
    $traccFooter->prerender($pdf);

    $basReportPages = $page;

    $pdf->end_page_ext('');


    // CASH DETAIL PART
    $page++;

    $pdf->begin_page_ext(595, 842, '');
    if ($logo) {
        $logoObj = new ClientLogo($logoPath);
        $logoObj->preRender($pdf);
    }

    $line = 80;

    $pdf->setFontExt($_fonts['Helvetica-Bold'], 9);
    $pdf->showBoxed('Business Activity Statement Report', 271 - 130, 510 + 185, 300, 30, 'center', '');
    $pdf->setFontExt($_fonts['Helvetica'], 8);
    $pdf->showBoxed("Period From: {$periodFrom} To: {$periodTo}", 271 - 130, 495 + 185, 300, 30, 'center', '');
    $pdf->showBoxed('Basis: ' . ucwords(strtolower($bas_basis)), 271 - 130, 480 + 185, 300, 30, 'center', '');

    $line += 40;

    $pdf->show_xy('Owner: ', 25, 750);
    $pdf->continue_text('Property: ');
    $pdf->continue_text('Report for: ');

    $pdf->show_xy("{$client}", 85, 750);
    $pdf->continue_text("{$propertyName} [{$propertyID}]");
    $pdf->continue_text("{$periodDescription}");

    $pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
    $pdf->showBoxed('Date', 20, 760 - $line, 70, 20, 'left', '');
    $pdf->showBoxed('Invoice Number', 90, 760 - $line, 100, 20, 'left', '');
    $pdf->showBoxed('Account', 190, 760 - $line, 70, 20, 'left', '');
    $pdf->showBoxed('Details', 260, 760 - $line, 150, 20, 'left', '');
    $pdf->showBoxed('Gross Amount', 410, 760 - $line, 80, 20, 'right', '');
    $pdf->showBoxed($_SESSION['country_default']['tax_label'] . ' Amount', 490, 760 - $line, 80, 20, 'right', '');

    $line += 10;

    $receipts = getOnepageReport($propertyID, $periodFrom, $periodTo, false);
    if (empty($receipts)) {
        $receipts = getOnepageReport($propertyID, $periodFrom, $periodTo, true);
    }

    $pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
    $pdf->showBoxed('Total sales and income', 20, 760 - $line, 590, 10, 'left', '');
    $line += 10;
    $grossTotal = 0;
    $gstTotal = 0;
    $taxFreeData = [];
    foreach ($receipts as $v) {
        if ($line > 715) {
            $pdf->setFontExt($_fonts['Helvetica'], 8);
            $pdf->showBoxed("Page {$page}", 479, 7, 75, 30, 'right', '');

            $traccFooter = new TraccFooter(
                'assets/clientLogos/tracc_logo_footer.jpg',
                'onePageOwnerReport',
                A4_PORTRAIT
            );
            $traccFooter->prerender($pdf);

            $pdf->end_page_ext('');
            $page++;

            $pdf->begin_page_ext(595, 842, '');
            if ($logo) {
                $logoObj = new ClientLogo($logoPath);
                $logoObj->preRender($pdf);
            }

            $line = 10;

            $pdf->setFontExt($_fonts['Helvetica-Bold'], 9);
            $pdf->showBoxed('Business Activity Statement Report', 271 - 130, 510 + 185, 300, 30, 'center', '');
            $pdf->setFontExt($_fonts['Helvetica'], 8);
            $pdf->showBoxed("Period From: {$periodFrom} To: {$periodTo}", 271 - 130, 495 + 185, 300, 30, 'center', '');
            $pdf->showBoxed('Basis: ' . ucwords(strtolower($bas_basis)), 271 - 130, 480 + 185, 300, 30, 'center', '');

            $line = 120;

            $pdf->show_xy('Owner: ', 25, 750);
            $pdf->continue_text('Property: ');
            $pdf->continue_text('Report for: ');

            $pdf->show_xy("{$client}", 85, 750);
            $pdf->continue_text("{$propertyName} [{$propertyID}]");
            $pdf->continue_text("{$periodDescription}");


            $pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
            $pdf->showBoxed('Date', 20, 760 - $line, 70, 20, 'left', '');
            $pdf->showBoxed('Invoice Number', 90, 760 - $line, 100, 20, 'left', '');
            $pdf->showBoxed('Account', 190, 760 - $line, 70, 20, 'left', '');
            $pdf->showBoxed('Details', 260, 760 - $line, 150, 20, 'left', '');
            $pdf->showBoxed('Gross Amount', 410, 760 - $line, 80, 20, 'right', '');
            $pdf->showBoxed(
                $_SESSION['country_default']['tax_label'] . ' Amount',
                490,
                760 - $line,
                80,
                20,
                'right',
                ''
            );

            $line += 10;

            $receipts = getOnepageReport($propertyID, $periodFrom, $periodTo, false);
            if (empty($receipts)) {
                $receipts = getOnepageReport($propertyID, $periodFrom, $periodTo, true);
            }

            $pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
            $pdf->showBoxed('Total sales and income', 20, 760 - $line, 590, 10, 'left', '');

            $line += 10;
        }

        if ($v['gst'] != 0) {
            $pdf->setFontExt($_fonts['Helvetica'], 8);
            $pdf->showBoxed($v['transaction_date'], 20, 760 - $line, 70, 10, 'left', '');
            $pdf->showBoxed($v['invoice_no'], 90, 760 - $line, 100, 10, 'left', '');
            $pdf->showBoxed($v['account_code'], 190, 760 - $line, 70, 10, 'left', '');
            $pdf->showBoxed($v['description'], 260, 760 - $line, 150, 10, 'left', '');
            $pdf->showBoxed(formatting($v['gross']), 410, 760 - $line, 80, 10, 'right', '');
            $pdf->showBoxed(formatting($v['gst']), 490, 760 - $line, 80, 10, 'right', '');

            $grossTotal += $v['gross'];
            $gstTotal += $v['gst'];

            $line += 10;
        } else {
            $taxFreeData[] = $v;
        }
    }

    $pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
    $pdf->showBoxed('Total sales and income', 190, 760 - $line, 220, 10, 'left', '');
    $pdf->showBoxed(formatting($grossTotal), 410, 760 - $line, 80, 10, 'right', '');
    $pdf->showBoxed(formatting($gstTotal), 490, 760 - $line, 80, 10, 'right', '');
    $line += 20;


    if ($line > 715) { // 715
        $pdf->setFontExt($_fonts['Helvetica'], 8);
        $pdf->showBoxed("Page {$page}", 479, 7, 75, 30, 'right', '');

        $traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'onePageOwnerReport', A4_PORTRAIT);
        $traccFooter->prerender($pdf);

        $pdf->end_page_ext('');
        $page++;

        $pdf->begin_page_ext(595, 842, '');
        if ($logo) {
            $logoObj = new ClientLogo($logoPath);
            $logoObj->preRender($pdf);
        }

        $line = 10;

        $pdf->setFontExt($_fonts['Helvetica-Bold'], 9);
        $pdf->showBoxed('Business Activity Statement Report', 271 - 130, 510 + 185, 300, 30, 'center', '');
        $pdf->setFontExt($_fonts['Helvetica'], 8);
        $pdf->showBoxed("Period From: {$periodFrom} To: {$periodTo}", 271 - 130, 495 + 185, 300, 30, 'center', '');
        $pdf->showBoxed('Basis: ' . ucwords(strtolower($bas_basis)), 271 - 130, 480 + 185, 300, 30, 'center', '');

        $line = 120;

        $pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
        $pdf->showBoxed('Date', 20, 760 - $line, 70, 20, 'left', '');
        $pdf->showBoxed('Invoice Number', 90, 760 - $line, 100, 20, 'left', '');
        $pdf->showBoxed('Account', 190, 760 - $line, 70, 20, 'left', '');
        $pdf->showBoxed('Details', 260, 760 - $line, 150, 20, 'left', '');
        $pdf->showBoxed('Gross Amount', 410, 760 - $line, 80, 20, 'right', '');
        $pdf->showBoxed($_SESSION['country_default']['tax_label'] . ' Amount', 490, 760 - $line, 80, 20, 'right', '');
    }

    // ############################################### PAYMENTS ##########################################################


    $totalPaymentsTax = 0;

    $line += 10;
    $pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
    $pdf->showBoxed('Total taxable purchases and expenses', 20, 760 - $line, 590, 20, 'left', '');
    $grossTotal = 0;
    $gstTotal = 0;

    foreach ($batchnr_result as $thisLine) {
        $batch = $thisLine['pmxc_t_batch'];
        $linen = $thisLine['pmxc_t_line'];
        $alloc_dt = $thisLine['paymentDate'];
        $alloc_amt = $thisLine['pmxc_alloc_amt'] * -1;
        $alloc_tax = $thisLine['pmxc_tax_amt'];

        $details_result = getPaymentAccountDetails($batch, $linen, $propertyID);

        if ($line > 715) {
            $pdf->setFontExt($_fonts['Helvetica'], 8);
            $pdf->showBoxed("Page {$page}", 479, 7, 75, 30, 'right', '');

            $traccFooter = new TraccFooter(
                'assets/clientLogos/tracc_logo_footer.jpg',
                'onePageOwnerReport',
                A4_PORTRAIT
            );
            $traccFooter->prerender($pdf);

            $pdf->end_page_ext('');
            $page++;

            $pdf->begin_page_ext(595, 842, '');
            if ($logo) {
                $logoObj = new ClientLogo($logoPath);
                $logoObj->preRender($pdf);
            }

            $line = 10;

            $pdf->setFontExt($_fonts['Helvetica-Bold'], 9);
            $pdf->showBoxed('Business Activity Statement Report', 271 - 130, 510 + 185, 300, 30, 'center', '');
            $pdf->setFontExt($_fonts['Helvetica'], 8);
            $pdf->showBoxed("Period From: {$periodFrom} To: {$periodTo}", 271 - 130, 495 + 185, 300, 30, 'center', '');
            $pdf->showBoxed('Basis: ' . ucwords(strtolower($bas_basis)), 271 - 130, 480 + 185, 300, 30, 'center', '');

            $line = 120;

            $pdf->show_xy('Owner: ', 25, 750);
            $pdf->continue_text('Property: ');
            $pdf->continue_text('Report for: ');

            $pdf->show_xy("{$client}", 85, 750);
            $pdf->continue_text("{$propertyName} [{$propertyID}]");
            $pdf->continue_text("{$periodDescription}");


            $pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
            $pdf->showBoxed('Date', 20, 760 - $line, 70, 20, 'left', '');
            $pdf->showBoxed('Invoice Number', 90, 760 - $line, 100, 20, 'left', '');
            $pdf->showBoxed('Account', 190, 760 - $line, 70, 20, 'left', '');
            $pdf->showBoxed('Details', 260, 760 - $line, 150, 20, 'left', '');
            $pdf->showBoxed('Gross Amount', 410, 760 - $line, 80, 20, 'right', '');
            $pdf->showBoxed('GST Amount', 490, 760 - $line, 80, 20, 'right', '');

            $line += 10;

            $receipts = getOnepageReport($propertyID, $periodFrom, $periodTo, false);
            if (empty($receipts)) {
                $receipts = getOnepageReport($propertyID, $periodFrom, $periodTo, true);
            }

            $pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
            $pdf->showBoxed('Total taxable purchases and expenses', 20, 760 - $line, 590, 10, 'left', '');

            $line += 10;
        }

        if ($alloc_tax != 0) {
            $pdf->setFontExt($_fonts['Helvetica'], 8);
            $pdf->showBoxed($details_result['trans_date'], 20, 760 - $line, 70, 10, 'left', '');
            $pdf->showBoxed($details_result['ref_1'], 90, 760 - $line, 100, 10, 'left', '');
            $pdf->showBoxed($details_result['ref_3'], 190, 760 - $line, 70, 10, 'left', '');
            $pdf->showBoxed($details_result['description'], 260, 760 - $line, 150, 10, 'left', '');
            $pdf->showBoxed(formatting($alloc_amt), 410, 760 - $line, 80, 10, 'right', '');
            $pdf->showBoxed(formatting($alloc_tax), 490, 760 - $line, 80, 10, 'right', '');

            $grossTotal += $alloc_amt;
            $gstTotal += $alloc_tax;

            $line += 10;
        } else {
            $v['transaction_date'] = $details_result['trans_date'];
            $v['invoice_no'] = $details_result['ref_1'];
            $v['account_code'] = $details_result['ref_3'];
            $v['description'] = $details_result['description'];
            $v['gross'] = $alloc_amt;
            $taxFreeData[] = $v;
        }
    }

    $pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
    $pdf->showBoxed('Total taxable purchases and expenses', 190, 760 - $line, 220, 10, 'left', '');
    $pdf->showBoxed(formatting($grossTotal), 410, 760 - $line, 80, 10, 'right', '');
    $pdf->showBoxed(formatting($gstTotal), 490, 760 - $line, 80, 10, 'right', '');
    $line += 20;

    $pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
    $pdf->showBoxed(
        'Items not included in ' . $_SESSION['country_default']['tax_label'] . ' Return',
        20,
        760 - $line,
        590,
        10,
        'left',
        ''
    );
    $line += 10;
    $grossTotal = 0;
    $gstTotal = 0;

    foreach ($taxFreeData as $v) {
        if ($line > 715) {
            $pdf->setFontExt($_fonts['Helvetica'], 8);
            $pdf->showBoxed("Page {$page}", 479, 7, 75, 30, 'right', '');

            $traccFooter = new TraccFooter(
                'assets/clientLogos/tracc_logo_footer.jpg',
                'onePageOwnerReport',
                A4_PORTRAIT
            );
            $traccFooter->prerender($pdf);

            $pdf->end_page_ext('');
            $page++;

            $pdf->begin_page_ext(595, 842, '');
            if ($logo) {
                $logoObj = new ClientLogo($logoPath);
                $logoObj->preRender($pdf);
            }

            $line = 10;

            $pdf->setFontExt($_fonts['Helvetica-Bold'], 9);
            $pdf->showBoxed('Business Activity Statement Report', 271 - 130, 510 + 185, 300, 30, 'center', '');
            $pdf->setFontExt($_fonts['Helvetica'], 8);
            $pdf->showBoxed("Period From: {$periodFrom} To: {$periodTo}", 271 - 130, 495 + 185, 300, 30, 'center', '');
            $pdf->showBoxed('Basis: ' . ucwords(strtolower($bas_basis)), 271 - 130, 480 + 185, 300, 30, 'center', '');
            $line = 120;

            $pdf->show_xy('Owner: ', 25, 750);
            $pdf->continue_text('Property: ');
            $pdf->continue_text('Report for: ');

            $pdf->show_xy("{$client}", 85, 750);
            $pdf->continue_text("{$propertyName} [{$propertyID}]");
            $pdf->continue_text("{$periodDescription}");


            $pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
            $pdf->showBoxed('Date', 20, 760 - $line, 70, 20, 'left', '');
            $pdf->showBoxed('Invoice Number', 90, 760 - $line, 100, 20, 'left', '');
            $pdf->showBoxed('Account', 190, 760 - $line, 70, 20, 'left', '');
            $pdf->showBoxed('Details', 260, 760 - $line, 150, 20, 'left', '');
            $pdf->showBoxed('Gross Amount', 410, 760 - $line, 80, 20, 'right', '');
            $pdf->showBoxed(
                $_SESSION['country_default']['tax_label'] . ' Amount',
                490,
                760 - $line,
                80,
                20,
                'right',
                ''
            );

            $line += 10;

            $pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
            $pdf->showBoxed(
                'Items not included in ' . $_SESSION['country_default']['tax_label'] . ' Return',
                20,
                760 - $line,
                590,
                10,
                'left',
                ''
            );
            $line += 10;
        }

        $pdf->setFontExt($_fonts['Helvetica'], 8);
        $pdf->showBoxed($v['transaction_date'], 20, 760 - $line, 70, 10, 'left', '');
        $pdf->showBoxed($v['invoice_no'], 90, 760 - $line, 100, 10, 'left', '');
        $pdf->showBoxed($v['account_code'], 190, 760 - $line, 70, 10, 'left', '');
        $pdf->showBoxed($v['description'], 260, 760 - $line, 150, 10, 'left', '');
        $pdf->showBoxed(formatting($v['gross']), 410, 760 - $line, 80, 10, 'right', '');

        $grossTotal += $v['gross'];

        $line += 10;
    }

    $pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
    $pdf->showBoxed(
        'Items not included in ' . $_SESSION['country_default']['tax_label'] . ' Return',
        190,
        760 - $line,
        220,
        10,
        'left',
        ''
    );
    $pdf->showBoxed(formatting($grossTotal), 410, 760 - $line, 80, 10, 'right', '');
    $line += 20;


    // insert tracc footer
    $traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'GST', A4_PORTRAIT);
    $traccFooter->prerender($pdf);

    $basReportPages = $page;
    $pdf->end_page_ext('');
    // CASH DETAIL ENDING PART


}// end of loop - if ($basis == "C")

// /////////////////////////////////////START OF BAS ON ACCRUALS BASIS//////////////////////////////////////////////////////////////////////////////////////////////
if ($basis == 'A') {
    $billedSQL = getBilledThisMonthTotal($propertyID, $periodFrom, $periodTo, false);
    $billedThisTaxable = ($billedSQL['amount']);
    $billedThisGST = ($billedSQL['gst']);
    $bas_total_taxable_accruals_display = formatting($billedThisTaxable);
    $billedSQLFree = getBilledThisMonthTotal($propertyID, $periodFrom, $periodTo, true);
    $billedThisGSTfree = ($billedSQLFree['amount']);
    $billedThisGSTFree = ($billedSQLFree['gst']);
    $bas_gstfree_supplies_accruals_display = formatting($billedThisGSTfree);

    // ////// Exclude Balance Sheet Account Receipts         //////////////////////////////////////
    $BS_Rec_TOTAL_SQL = "
		SELECT COALESCE(SUM( trans_amt ), 0) AS amount,
		COALESCE(SUM(artr_tax_amt), 0) AS gst
          FROM   AR_TRANSACTION
          WHERE  ( ref_2 = ? )
            AND ( trans_type <> 'REV' )
            AND ( trans_type <> 'CSH' )
            AND ( trans_date BETWEEN Convert( datetime, ?, 103 ) AND Convert( datetime, ?, 103 ) )
            AND ref_3 IN
                (
                    SELECT
                        pmcg_acc
                    FROM
                        pmcg_chart_grp
                    WHERE (pmcg_grp = 'TRACC1')
                    AND (pmcg_subgrp = 'BSHEET'))";
    $BS_Rec_amount_total = $dbh->executeSingle($BS_Rec_TOTAL_SQL, $params);

    $BS_Rec_NO_GST_SQL = "
		SELECT COALESCE(SUM( trans_amt ), 0)    AS amount
          FROM   AR_TRANSACTION
          WHERE  ( ref_2 = ? )
            AND ( trans_type <> 'REV' )
            AND ( trans_type <> 'CSH' )
            AND ( trans_date BETWEEN Convert( datetime, ?, 103 ) AND Convert( datetime, ?, 103 ) )
            AND (artr_gst_code In ('GSTFREE','GST FREE', 'BASEXCLUDED'))
            AND ref_3 IN
                (
                    SELECT
                        pmcg_acc
                    FROM
                        pmcg_chart_grp
                    WHERE (pmcg_grp = 'TRACC1')
                    AND (pmcg_subgrp = 'BSHEET'))";
    $BS_Rec_nogst_amount = $dbh->executeScalar($BS_Rec_NO_GST_SQL, $params);
    $BS_Rec_ant_total = ($BS_Rec_amount_total['amount']);
    $BS_Rec_gst_total = ($BS_Rec_amount_total['gst']);
    // echo $BS_Rec_ant_total;
    $bas_gstfree_supplies_accruals_display = formatting($billedThisGSTfree - $BS_Rec_nogst_amount);
    $billedTotal = $billedThisTaxable + $billedThisGSTfree - ($BS_Rec_ant_total);
    $gstOUTPUTonIncome = $billedThisGST + $billedThisGSTFree - ($BS_Rec_gst_total);
    $bas_tenant_income_display = formatting($billedTotal);
    $bas_gst_on_taxable = $billedThisTaxable / 11;
    $bas_gst_on_taxable_accruals_display = formatting($bas_gst_on_taxable);

    // CAPITAL ACQUISITIONS INCLGST
    $capacqGSTSQL = "
		SELECT
			COALESCE(SUM(trans_amt), 0) as amount
		FROM
			ap_transaction
		WHERE
			ref_2 = ?
			AND [TRANS_TYPE] <> 'ADJ'
			AND [TRANS_TYPE] <> 'CAN'
			AND [TRANS_TYPE] <> 'PAY'
			AND (aptr_period >= ?)
			AND (aptr_period <= ?)
			AND (aptr_year = ?)
			AND ref_3 <> ?
			AND ref_3 IN (
				SELECT
					pmcg_acc
				FROM
					pmcg_chart_grp
				WHERE (pmcg_grp = 'TRACC3')
				AND (pmcg_subgrp IN ('EXPOWNCAPI', 'BSPMTCAPI')))";
    $capacqGST = $dbh->executeScalar(
        $capacqGSTSQL,
        [$propertyID, $currentPeriod, $toPeriod, $currentYear, $owner_remittance_acc['pmzz_desc']]
    );
    $capacqGST_display = formatting($capacqGST);
    $params = [$propertyID, $owner_remittance_acc['pmzz_desc']];
    // TOTAL ACQUISITIONS
    $totalacqGSTSQL = "
		SELECT
			COALESCE(SUM(trans_amt), 0) as amount,
			COALESCE(SUM(aptr_tax_amt), 0) as gst
		FROM
			ap_transaction
		WHERE
			ref_2 = ?
			AND [TRANS_TYPE] <> 'ADJ'
			AND [TRANS_TYPE] <> 'CAN'
			AND [TRANS_TYPE] <> 'PAY'
			AND ref_3 <> ?
			AND aptr_tax_amt <> 0
			AND ref_3 IN
				(SELECT pmcg_acc
				FROM pmcg_chart_grp
				WHERE
					(pmcg_grp = 'TRACC3')
					AND (pmcg_subgrp <>'EXPOWNGST')
					AND (pmcg_subgrp <>'BSPMTFUND')
					AND (pmcg_subgrp <>'BSPMTOTH')
					AND (pmcg_subgrp <>'BSPMTREMI')
					AND (pmcg_subgrp <>'BSRECFUND')
					AND (pmcg_subgrp <> 'EXPOPTREMI'))";

    $totalacqGSTSQL .= $currentYear == $calendarTo['year'] ? '
			AND (aptr_period >= ' . addSQLParam($params, $currentPeriod) . ')
			AND (aptr_period <= ' . addSQLParam($params, $toPeriod) . ')
			AND (aptr_year = ' . addSQLParam($params, $currentYear) . ')' : 'and ((aptr_period >= ' . addSQLParam(
        $params,
        $currentPeriod
    ) . ' and ' . addSQLParam($params, $previousYear) . ' = aptr_year) or (aptr_period <= ' . addSQLParam(
        $params,
        $toPeriod
    ) . ' and ' . addSQLParam($params, $calendarTo['year']) . ' = aptr_year))';

    $totalacqGST_result = $dbh->executeSingle($totalacqGSTSQL, $params);
    $totalacqGST = $totalacqGST_result['amount'];
    $gstpaidAccrual = $totalacqGST_result['gst'];

    $totalacqGST_display = formatting($totalacqGST);

    $bas_other_acquisitions_accruals = $totalacqGST - $capacqGST;
    $bas_other_acquisitions_accruals_display = formatting($bas_other_acquisitions_accruals);
    $params = [$propertyID, $owner_remittance_acc['pmzz_desc']];
    $acNoGSTSQL = "
		SELECT
			COALESCE(SUM(trans_amt), 0) as amount FROM ap_transaction
			  WHERE ref_2 = ?
			  AND [TRANS_TYPE] <> 'ADJ'
			  AND [TRANS_TYPE] <> 'CAN'
			  AND [TRANS_TYPE] <> 'PAY'
			  AND [APTR_TAX_AMT] = '0'
			  AND ref_3 <> ?
			  AND ref_3 IN
						  (SELECT pmcg_acc
							FROM pmcg_chart_grp
							WHERE
					(pmcg_grp = 'TRACC3')
					AND (pmcg_subgrp <>'EXPOWNGST')
					AND (pmcg_subgrp <>'BSPMTFUND')
					AND (pmcg_subgrp <>'BSPMTOTH')
					AND (pmcg_subgrp <>'BSPMTREMI')
					AND (pmcg_subgrp <>'BSRECFUND')
					AND (pmcg_subgrp <>'EXPOPTREMI'))";

    $acNoGSTSQL .= $currentYear == $calendarTo['year'] ? '
			AND (aptr_period >= ' . addSQLParam($params, $currentPeriod) . ')
			AND (aptr_period <= ' . addSQLParam($params, $toPeriod) . ')
			AND (aptr_year = ' . addSQLParam($params, $currentYear) . ')' : 'and ((aptr_period >= ' . addSQLParam(
        $params,
        $currentPeriod
    ) . ' and ' . addSQLParam($params, $previousYear) . ' = aptr_year) or (aptr_period <= ' . addSQLParam(
        $params,
        $toPeriod
    ) . ' and ' . addSQLParam($params, $calendarTo['year']) . ' = aptr_year))';
    $acNoGST = $dbh->executeScalar($acNoGSTSQL, $params);
    $acNoGST += $bas_acquisition_noGST_owner_remit;
    $bas_acquisition_no_GST_display = formatting($acNoGST);

    $bas_creditable = $totalacqGST - $acNoGST;
    $bas_creditable_display = formatting($bas_creditable);
    $bas_gst_on_creditable = $bas_creditable / 11;
    $bas_amount_payable = $bas_gst_on_taxable - $bas_gst_on_creditable;
    $bas_amount_payable_display = formatting($bas_amount_payable);
    $bas_gst_on_creditable_display = formatting($bas_gst_on_creditable);


    // //////////////////////////////////START OF PDF CODE/////////////////////////////////////////////////////////
    $page++;

    $pdf->begin_page_ext(595, 842, '');
    $pdf->setFontExt($_fonts['Helvetica-Bold'], 9);

    $pdf->setlinewidth(1);


    $pdf->setColorExt('fill', 'rgb', 0, 0, 0, 0);

    $pdf->rect(395, 397 + 240, 75, 14); // draw the rectangle
    $pdf->rect(395, 377 + 240, 75, 14); // draw the rectangle
    $pdf->rect(395, 357 + 240, 75, 14); // draw the rectangle
    $pdf->rect(395, 337 + 240, 75, 14); // draw the rectangle
    $pdf->rect(395, 317 + 240, 75, 14); // draw the rectangle
    $pdf->rect(395, 297 + 240, 75, 14); // draw the rectangle

    // ACQUISITIONS RECTANGLES
    $pdf->rect(395, 412 + 20, 75, 15); // draw the rectangle
    $pdf->rect(395, 357 + 20, 75, 15); // draw the rectangle
    $pdf->rect(395, 337 + 20, 75, 15); // draw the rectangle
    $pdf->rect(395, 317 + 20, 75, 15); // draw the rectangle

    // summary RECTS
    $pdf->rect(395, 157 + 55, 75, 15); // draw the rectangle

    $pdf->stroke(); // stroke the path with the current color and line width
    $pdf->showBoxed('Business Activity Statement Report', 271 - 130, 510 + 185, 300, 30, 'center', '');
    $pdf->setFontExt($_fonts['Helvetica'], 8);
    // $pdf->showBoxed("$propertyName", 171-130, 510+185,500,30,"center", "");
    $bas_date = date('M Y');
    // $pdf->showBoxed("$periodFrom_display - $periodTo_display", 271, 495,300,30,"center", "");
    $pdf->showBoxed("Period From: {$periodFrom} To: {$periodTo}", 271 - 130, 495 + 185, 300, 30, 'center', '');

    if ($logo) {
        generateLogo();
    }

    $pdf->showBoxed('Basis: ' . ucwords(strtolower($bas_basis)), 271 - 130, 480 + 185, 300, 30, 'center', '');

    $pdf->show_xy('Owner: ', 25, 750);
    $pdf->continue_text('Property: ');
    $pdf->continue_text('Report for: ');

    $pdf->show_xy("{$client}", 85, 750);
    $pdf->continue_text("{$propertyName} [{$propertyID}]");
    $pdf->continue_text("{$periodDescription}");


    // $pdf->setColorExt("both", "rgb", 0,0,0, 0);
    $pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
    $pdf->show_xy('Goods and Serives Tax on your Sales and Income', 100, 435 + 183 + 40);
    $pdf->show_xy('Goods and services tax on your purchases and expenses', 100, 430 + 40);

    $yLine = 240;
    $pdf->setFontExt($_fonts['Helvetica'], 8);
    $pdf->show_xy(
        'Total Sales and Income for the period (incl. ' . $_SESSION['country_default']['tax_label'] . ')',
        100,
        400 + $yLine
    );
    $pdf->show_xy('Zero Rated supplies included in Box 5', 100, 380 + $yLine);
    $pdf->show_xy('Subtract Box 6 from Box 5', 100, 360 + $yLine);
    $pdf->show_xy(
        'Divide Box 7 to calculate ' . $_SESSION['country_default']['tax_label'] . ' Component',
        100,
        340 + $yLine
    );
    $pdf->show_xy('Adjustment from Calculation sheets', 100, 320 + $yLine);
    $pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
    $pdf->show_xy('Total ' . $_SESSION['country_default']['tax_label'] . ' Collected', 100, 300 + $yLine);

    $pdf->setFontExt($_fonts['Helvetica'], 8);
    $pdf->showBoxed('(5)', 420, 380 + $yLine, 75, 30, 'right', '');
    $pdf->showBoxed('(6)', 420, 360 + $yLine, 75, 30, 'right', '');
    $pdf->showBoxed('(7)', 420, 340 + $yLine, 75, 30, 'right', '');
    $pdf->showBoxed('(8)', 420, 320 + $yLine, 75, 30, 'right', '');
    $pdf->showBoxed('(9)', 420, 300 + $yLine, 75, 30, 'right', '');
    $pdf->showBoxed('(10)', 420, 280 + $yLine, 75, 30, 'right', '');
    $pdf->showBoxed('(11)', 425, 395 + 20, 75, 30, 'right', '');
    $pdf->showBoxed('(12)', 425, 340 + 20, 75, 30, 'right', '');
    $pdf->showBoxed('(13)', 425, 320 + 20, 75, 30, 'right', '');
    $pdf->showBoxed('(14)', 425, 300 + 20, 75, 30, 'right', '');

    $pdf->showBoxed(
        'Total purchases and expenses (incl. ' . $_SESSION['country_default']['tax_label'] . ') for which tax invoices requirements have been met, excluding any imported goods',
        100,
        400 + 20,
        160,
        24,
        'left',
        ''
    );
    $pdf->show_xy(
        'Divide Box 11 to calculate ' . $_SESSION['country_default']['tax_label'] . ' Component',
        100,
        360 + 20
    );
    $pdf->show_xy('Adjustments', 100, 340 + 20);
    $pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
    $pdf->show_xy('Total ' . $_SESSION['country_default']['tax_label'] . ' Credit', 100, 320 + 20);


    $pdf->show_xy($_SESSION['country_default']['tax_label'] . ' to pay/(Due to you)', 100, 162 + 55);


    $seven = ($billedTotal - ($billedThisGSTfree - $BS_Rec_nogst_amount));

    // START PURCHASE AMOUNT
    $period = dbGetPeriodYear($propertyID, $view->items['periodFrom'], $view->items['periodTo']);
    $yearPeriod = [];
    foreach ($period as $date) {
        $id = $date['year'];
        $yearPeriod[$id][] = $date['period'];
    }

    $invoice = dbGetInvoiceActivity($propertyID, $yearPeriod);
    $datas = [];
    $taxFreeData = [];
    foreach ($invoice as $row) {
        if ($row['gstAmount'] != 0) {
            if (in_array($row['transactionType'], ['ar', 'arbs'])) {
                $datas['Total sales and income'][] = $row;
            } else {
                $datas['Total taxable purchases and expenses'][] = $row;
            }
        } else {
            $taxFreeData[] = $row;
        }
    }

    $purchaseGstTotal = 0;
    foreach ($datas as $IE => $data) {
        foreach ($data as $v) {
            if ($IE === 'Total taxable purchases and expenses') {
                $purchaseGstTotal += $v['gstAmount'];
            }
        }
    }

    // END PURCHASE AMOUNT
    $tenth = $billedSQL['gst'];
    $pdf->setFontExt($_fonts['Helvetica'], 8);
    $pdf->showBoxed($bas_tenant_income_display, 392, 380 + $yLine, 75, 30, 'right', '');
    $pdf->showBoxed("{$bas_gstfree_supplies_accruals_display}", 392, 360 + $yLine, 75, 30, 'right', '');
    $pdf->showBoxed(formatting($seven), 392, 340 + $yLine, 75, 30, 'right', '');
    $pdf->showBoxed(formatting($seven * 3 / 23), 392, 320 + $yLine, 75, 30, 'right', '');
    $pdf->showBoxed(formatting($billedSQL['gst'] - ($seven * 3 / 23)), 392, 300 + $yLine, 75, 30, 'right', '');
    $pdf->showBoxed(formatting($billedSQL['gst']), 392, 280 + $yLine, 75, 30, 'right', '');


    $pdf->setFontExt($_fonts['Helvetica'], 6);
    $disclaimerSQL = '
        SELECT b.pmco_name as pmco_name
        FROM pmpr_property a
        LEFT JOIN pmco_company b
        ON a.pmpr_agent = b.pmco_code
        WHERE pmpr_prop = ?
        ';
    $disclaimer_result = $dbh->executeSingle($disclaimerSQL, [$propertyID]);
    $AgentCompanyName = $disclaimer_result['pmco_name'];

    $disclaimerTitle = 'DISCLAIMER';
    $disclaimerLine1 = 'The purpose of this report is to provide only those details of transactions for the property that have ';
    $disclaimerLine2 = 'been processed by ' . $AgentCompanyName . ' as Managing Agent for the owner for the specified period.';
    $disclaimerLine3 = $AgentCompanyName . ' is in no way purporting any taxation advice with respect to the ' . $_SESSION['country_default']['tax_label'] . ' liabilities of the owner. ';
    $disclaimerLine4 = 'It is strongly recommended that the owner seek separate, professional advice regarding any ' . $_SESSION['country_default']['tax_label'] . ' issues relating to this property.';
    // $disclaimerLine5 = 'any GST issues relating to this property.';

    $allDisc = $disclaimerLine1 . $disclaimerLine2 . $disclaimerLine3 . $disclaimerLine4;
    $count = substr_count($allDisc, ' ') / 4;
    $disclaimerLine1 = '';
    $disclaimerLine2 = '';
    $disclaimerLine3 = '';
    $disclaimerLine4 = '';
    foreach (explode(' ', $allDisc) as $index => $rs) {
        if ($index <= $count) {
            $disclaimerLine1 .= $rs . ' ';
        } elseif ($index < $count * 2) {
            $disclaimerLine2 .= $rs . ' ';
        } elseif ($index < ($count * 3) + 4) {
            $disclaimerLine3 .= $rs . ' ';
        } else {
            $disclaimerLine4 .= $rs . ' ';
        }
    }

    $pdf->show_xy($disclaimerTitle, 65, 90);
    $pdf->show_xy($disclaimerLine1, 65, 83);
    $pdf->show_xy($disclaimerLine2, 65, 77);
    $pdf->show_xy($disclaimerLine3, 65, 71);
    $pdf->show_xy($disclaimerLine4, 65, 65);

    $twelve = ($totalacqGST * 3) / 23;
    $fourteen = $purchaseGstTotal;

    $pdf->setFontExt($_fonts['Helvetica'], 8);
    $pdf->showBoxed("{$totalacqGST_display}", 392, 395 + 20, 75, 30, 'right', '');
    $pdf->showBoxed(formatting($twelve), 392, 340 + 20, 75, 30, 'right', '');
    $pdf->showBoxed(formatting($fourteen - $twelve), 392, 320 + 20, 75, 30, 'right', '');
    $pdf->showBoxed(formatting($fourteen), 392, 300 + 20, 75, 30, 'right', '');

    $dueToYou = formatting($tenth - $fourteen);
    $pdf->showBoxed("{$dueToYou}", 392, 154 + 55, 75, 15, 'right', '');

    // footer
    $pdf->setFontExt($_fonts['Helvetica'], 8);
    $pdf->showBoxed("Page {$page}", 470, 5, 75, 30, 'left', '');

    $pdf->setlinewidth(0.5);
    $pdf->moveto(65, 635 + 40);
    $pdf->lineto(530, 635 + 40);
    $pdf->stroke();
    // middle line
    $pdf->moveto(65, 445 + 40);
    $pdf->lineto(530, 445 + 40);
    $pdf->stroke();
    // bottom line
    $pdf->moveto(65, 210 + 40);
    $pdf->lineto(530, 210 + 40);
    $pdf->stroke();

    // left side bar line
    $pdf->moveto(65, 210 - 50);
    $pdf->lineto(65, 635 + 40);
    $pdf->stroke();

    // right side bar line
    $pdf->moveto(530, 210 - 50);
    $pdf->lineto(530, 635 + 40);
    $pdf->stroke();

    // first headers
    $pdf->moveto(65, 614 + 40);
    $pdf->lineto(530, 614 + 40);
    $pdf->stroke();


    // second headers
    $pdf->moveto(65, 425 + 40);
    $pdf->lineto(530, 425 + 40);
    $pdf->stroke();

    // summary bottom
    $pdf->moveto(65, 160);
    $pdf->lineto(530, 160);
    $pdf->stroke();

    // insert tracc footer
    $traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'GST', A4_PORTRAIT);
    $traccFooter->prerender($pdf);

    $basReportPages = $page;
    $pdf->end_page_ext('');

    // ACCRUAL DETAIL PART

    $page++;

    $pdf->begin_page_ext(595, 842, '');
    if ($logo) {
        $logoObj = new ClientLogo($logoPath);
        $logoObj->preRender($pdf);
    }

    $line = 80;

    $pdf->setFontExt($_fonts['Helvetica-Bold'], 9);
    $pdf->showBoxed('Business Activity Statement Report', 271 - 130, 510 + 185, 300, 30, 'center', '');
    $pdf->setFontExt($_fonts['Helvetica'], 8);
    $pdf->showBoxed("Period From: {$periodFrom} To: {$periodTo}", 271 - 130, 495 + 185, 300, 30, 'center', '');
    $pdf->showBoxed('Basis: ' . ucwords(strtolower($bas_basis)), 271 - 130, 480 + 185, 300, 30, 'center', '');
    $line += 40;

    $pdf->show_xy('Owner: ', 25, 750);
    $pdf->continue_text('Property: ');
    $pdf->continue_text('Report for: ');

    $pdf->show_xy("{$client}", 85, 750);
    $pdf->continue_text("{$propertyName} [{$propertyID}]");
    $pdf->continue_text("{$periodDescription}");

    $pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
    $pdf->showBoxed('Date', 20, 760 - $line, 70, 20, 'left', '');
    $pdf->showBoxed('Invoice Number', 90, 760 - $line, 100, 20, 'left', '');
    $pdf->showBoxed('Account', 190, 760 - $line, 70, 20, 'left', '');
    $pdf->showBoxed('Details', 260, 760 - $line, 150, 20, 'left', '');
    $pdf->showBoxed('Gross Amount', 410, 760 - $line, 80, 20, 'right', '');
    $pdf->showBoxed($_SESSION['country_default']['tax_label'] . ' Amount', 490, 760 - $line, 80, 20, 'right', '');

    $line += 10;


    foreach ($datas as $IE => $data) {
        $pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
        $pdf->showBoxed($IE, 20, 760 - $line, 590, 10, 'left', '');
        $line += 10;
        $grossTotal = 0;
        $gstTotal = 0;

        foreach ($data as $v) {
            if ($line > 715) {
                $pdf->setFontExt($_fonts['Helvetica'], 8);
                $pdf->showBoxed("Page {$page}", 479, 7, 75, 30, 'right', '');

                $traccFooter = new TraccFooter(
                    'assets/clientLogos/tracc_logo_footer.jpg',
                    'onePageOwnerReport',
                    A4_PORTRAIT
                );
                $traccFooter->prerender($pdf);

                $pdf->end_page_ext('');
                $page++;

                $pdf->begin_page_ext(595, 842, '');
                if ($logo) {
                    $logoObj = new ClientLogo($logoPath);
                    $logoObj->preRender($pdf);
                }

                $line = 10;

                $pdf->setFontExt($_fonts['Helvetica-Bold'], 9);
                $pdf->showBoxed('Business Activity Statement Report', 271 - 130, 510 + 185, 300, 30, 'center', '');
                $pdf->setFontExt($_fonts['Helvetica'], 8);
                $pdf->showBoxed("Period From: {$periodFrom} To: {$periodTo}", 271 - 130, 495 + 185, 300, 30, 'center', '');
                $pdf->showBoxed(
                    'Basis: ' . ucwords(strtolower($bas_basis)),
                    271 - 130,
                    480 + 185,
                    300,
                    30,
                    'center',
                    ''
                );
                $line = 120;

                $pdf->show_xy('Owner: ', 25, 750);
                $pdf->continue_text('Property: ');
                $pdf->continue_text('Report for: ');

                $pdf->show_xy("{$client}", 85, 750);
                $pdf->continue_text("{$propertyName} [{$propertyID}]");
                $pdf->continue_text("{$periodDescription}");


                $pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
                $pdf->showBoxed('Date', 20, 760 - $line, 70, 20, 'left', '');
                $pdf->showBoxed('Invoice Number', 90, 760 - $line, 100, 20, 'left', '');
                $pdf->showBoxed('Account', 190, 760 - $line, 70, 20, 'left', '');
                $pdf->showBoxed('Details', 260, 760 - $line, 150, 20, 'left', '');
                $pdf->showBoxed('Gross Amount', 410, 760 - $line, 80, 20, 'right', '');
                $pdf->showBoxed(
                    $_SESSION['country_default']['tax_label'] . ' Amount',
                    490,
                    760 - $line,
                    80,
                    20,
                    'right',
                    ''
                );

                $line += 10;

                $pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
                $pdf->showBoxed($IE, 20, 760 - $line, 590, 10, 'left', '');
                $line += 10;
            }

            $pdf->setFontExt($_fonts['Helvetica'], 8);
            $pdf->showBoxed($v['transactionDate'], 20, 760 - $line, 70, 10, 'left', '');
            $pdf->showBoxed($v['invoiceNumber'], 90, 760 - $line, 100, 10, 'left', '');
            $pdf->showBoxed($v['accountID'], 190, 760 - $line, 70, 10, 'left', '');
            $pdf->showBoxed($v['description'], 260, 760 - $line, 150, 10, 'left', '');
            $pdf->showBoxed(formatting($v['grossAmount']), 410, 760 - $line, 80, 10, 'right', '');
            $pdf->showBoxed(formatting($v['gstAmount']), 490, 760 - $line, 80, 10, 'right', '');

            $grossTotal += $v['grossAmount'];
            $gstTotal += $v['gstAmount'];

            $line += 10;
        }

        $pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
        $pdf->showBoxed($IE, 190, 760 - $line, 220, 10, 'left', '');
        $pdf->showBoxed(formatting($grossTotal), 410, 760 - $line, 80, 10, 'right', '');
        $pdf->showBoxed(formatting($gstTotal), 490, 760 - $line, 80, 10, 'right', '');
        $line += 20;
    }


    $pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
    $pdf->showBoxed(
        'Items not included in ' . $_SESSION['country_default']['tax_label'] . ' Return',
        20,
        760 - $line,
        590,
        10,
        'left',
        ''
    );
    $line += 10;
    $grossTotal = 0;
    $gstTotal = 0;

    foreach ($taxFreeData as $v) {
        if ($line > 715) {
            $pdf->setFontExt($_fonts['Helvetica'], 8);
            $pdf->showBoxed("Page {$page}", 479, 7, 75, 30, 'right', '');

            $traccFooter = new TraccFooter(
                'assets/clientLogos/tracc_logo_footer.jpg',
                'onePageOwnerReport',
                A4_PORTRAIT
            );
            $traccFooter->prerender($pdf);

            $pdf->end_page_ext('');
            $page++;

            $pdf->begin_page_ext(595, 842, '');
            if ($logo) {
                $logoObj = new ClientLogo($logoPath);
                $logoObj->preRender($pdf);
            }

            $line = 10;

            $pdf->setFontExt($_fonts['Helvetica-Bold'], 9);
            $pdf->showBoxed('Business Activity Statement Report', 271 - 130, 510 + 185, 300, 30, 'center', '');
            $pdf->setFontExt($_fonts['Helvetica'], 8);
            $pdf->showBoxed("Period From: {$periodFrom} To: {$periodTo}", 271 - 130, 495 + 185, 300, 30, 'center', '');
            $pdf->showBoxed('Basis: ' . ucwords(strtolower($bas_basis)), 271 - 130, 480 + 185, 300, 30, 'center', '');
            $line = 120;

            $pdf->show_xy('Owner: ', 25, 750);
            $pdf->continue_text('Property: ');
            $pdf->continue_text('Report for: ');

            $pdf->show_xy("{$client}", 85, 750);
            $pdf->continue_text("{$propertyName} [{$propertyID}]");
            $pdf->continue_text("{$periodDescription}");


            $pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
            $pdf->showBoxed('Date', 20, 760 - $line, 70, 20, 'left', '');
            $pdf->showBoxed('Invoice Number', 90, 760 - $line, 100, 20, 'left', '');
            $pdf->showBoxed('Account', 190, 760 - $line, 70, 20, 'left', '');
            $pdf->showBoxed('Details', 260, 760 - $line, 150, 20, 'left', '');
            $pdf->showBoxed('Gross Amount', 410, 760 - $line, 80, 20, 'right', '');
            $pdf->showBoxed(
                $_SESSION['country_default']['tax_label'] . ' Amount',
                490,
                760 - $line,
                80,
                20,
                'right',
                ''
            );

            $line += 10;

            $pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
            $pdf->showBoxed(
                'Items not included in ' . $_SESSION['country_default']['tax_label'] . ' Return',
                20,
                760 - $line,
                590,
                10,
                'left',
                ''
            );
            $line += 10;
        }

        $pdf->setFontExt($_fonts['Helvetica'], 8);
        $pdf->showBoxed($v['transactionDate'], 20, 760 - $line, 70, 10, 'left', '');
        $pdf->showBoxed($v['invoiceNumber'], 90, 760 - $line, 100, 10, 'left', '');
        $pdf->showBoxed($v['accountID'], 190, 760 - $line, 70, 10, 'left', '');
        $pdf->showBoxed($v['description'], 260, 760 - $line, 150, 10, 'left', '');
        $pdf->showBoxed(formatting($v['grossAmount']), 410, 760 - $line, 80, 10, 'right', '');
        //			$pdf->showBoxed (formatting($v['gstAmount']), 490, 760 - $line, 80, 10, 'right', '');

        $grossTotal += $v['grossAmount'];
        $gstTotal += $v['gstAmount'];

        $line += 10;
    }

    $pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
    $pdf->showBoxed(
        'Items not included in ' . $_SESSION['country_default']['tax_label'] . ' Return',
        190,
        760 - $line,
        220,
        10,
        'left',
        ''
    );
    $pdf->showBoxed(formatting($grossTotal), 410, 760 - $line, 80, 10, 'right', '');
    //		$pdf->showBoxed (formatting($gstTotal), 490, 760 - $line, 80, 10, 'right', '');
    $line += 20;

    // insert tracc footer
    $traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'GST', A4_PORTRAIT);
    $traccFooter->prerender($pdf);

    $basReportPages = $page;
    $pdf->end_page_ext('');
    // ACCRUAL DETAIL ENDING PART
}

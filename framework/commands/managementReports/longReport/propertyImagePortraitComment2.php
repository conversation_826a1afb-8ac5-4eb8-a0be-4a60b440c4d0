<?php

/**
 * Created by PhpStorm.
 * User: adacanay
 * Date: 8/28/2019
 * Time: 8:38 AM
 */
global $dbh;
global $periodDescription;
global $propertyName;
$pdf->set_option('errorpolicy=return');
$pdf->set_option('stringformat=utf8');
$pdf->begin_page_ext(595, 842, '');

// header
$page++;

$page_header = 'Owners Statement';

// #############################################UNPAID CREDITORS INCLUDE ########################################
// include "owner/unpaidCreditorsInclude.php";

// $agentData = dbGetAgentDetails ();
// $agentDetailsNew = new agentDetails ($propertyID);
// $agentDetailsNew->bindAttributesFrom ($agentData);
// $agentDetailsNew->preRender($pdf);

$agentData = dbGetAgentDetails();
$agentDetailsNew = new agentDetails($propertyID, true);
$agentDetailsNew->bindAttributesFrom($agentData);
$agentDetailsNew->renderPortrait($pdf);

$agentName = $agentData['agentName'];
$agentStreet = $agentData['agentAddress'];
$agentCity = $agentData['agentCity'];
$agentState = $agentData['agentState'];
$agentPostCode = $agentData['agentPostCode'];
$agentPhone = $agentData['agentPhone'];
$agentABN = $agentData['agentABN'];
$portfolioManager = $agentDetailsNew->portfolioManager;
$portfolioEmail = $agentDetailsNew->portfolioEmail;


$pdf->setFontExt($_fonts['Helvetica-Bold'], 12);
$pdf->showBoxed("{$page_header}", 100, 690, 400, 20, 'center', '');
$pdf->setFontExt($_fonts['Helvetica'], 9);
$pdf->show_xy('Owner:', 22, 760);
$pdf->continue_text('Property:');
$pdf->continue_text('Report for:');
$pdf->show_xy($client, 85, 760);
$pdf->continue_text("{$propertyName} [{$propertyID}]");
// $pdf->continue_text($periodTo);
$pdf->continue_text($periodDescription);
$pdf->showBoxed("Period From: {$periodFrom} To: {$periodTo}", 100, 670, 400, 20, 'center', '');

if ($logo) {
    generateLogo('portrait');
}

// $agentInfo = "$agentName
// \n$agentStreet
// \n$agentCity, $agentState $agentPostCode
// \nProperty Manager - $portfolioManager
// \nEmail - $portfolioEmail
// \nABN $agentABN";
// $pdf->setFontExt ($_fonts["Helvetica-Bold"], 9);
// $pdf->showBoxed ($agentName, 273, 740, 300, 30, 'right', '');
// $pdf->showBoxed ("ABN $agentABN", 273, 730, 300, 30, 'right', '');
// $pdf->showBoxed ($agentStreet, 273, 710, 300, 30, 'right', '');
//
// $pdf->showBoxed ("$agentCity, $agentState $agentPostCode", 273, 700, 300, 30, 'right', '');

// $pdf->showBoxed ("Prepared by - $portfolioManager", 513, 440, 300, 30, 'right', '');
$pdf->showBoxed("Prepared by - {$portfolioManager} ({$portfolioEmail})", 160, 120, 300, 30, 'center', '');
// $pdf->showBoxed ("Email - $portfolioEmail", 513, 430, 300, 30, 'right', '');

$management_upload = dbGetSubReportUploadImage($propertyID, $s['sub_report_id'], $calendar['period'], $calendar['year']);
$management_comments = dbGetSubReportComment($propertyID, $s['sub_report_id'], $calendar['period'], $calendar['year']);
management_report($pdf, $_fonts, $management_comments[1], 22, 170, 240, 50, 'left');

// COMMENT 1
$pdf->moveto(20, 170);
$pdf->lineto(20, 220);
$pdf->stroke();

$pdf->moveto(260, 170);
$pdf->lineto(260, 220);
$pdf->stroke();

$pdf->moveto(20, 220);
$pdf->lineto(260, 220);
$pdf->stroke();

$pdf->moveto(20, 170);
$pdf->lineto(260, 170);
$pdf->stroke();

// COMMENT 2
management_report($pdf, $_fonts, $management_comments[2], 292, 170, 240, 50, 'left');

$pdf->moveto(290, 170);
$pdf->lineto(290, 220);
$pdf->stroke();

$pdf->moveto(530, 170);
$pdf->lineto(530, 220);
$pdf->stroke();

$pdf->moveto(290, 220);
$pdf->lineto(530, 220);
$pdf->stroke();

$pdf->moveto(290, 170);
$pdf->lineto(530, 170);
$pdf->stroke();




$pdf->setFontExt($_fonts['Helvetica'], 8);
$pdf->showBoxed("Page {$page}", 480, 2, 75, 30, 'right', '');

if ($logo) {
    generatePropertyImage('portrait');
}

if (isset($management_upload[1])) {
    generateManagementReportImage('portrait', 10, 550, 200, 200, $management_upload[1]);
}

if (isset($management_upload[2])) {
    generateManagementReportImage('portrait', 400, 550, 100, 200, $management_upload[2]);
}

// #############################################################################################################

$line = -15;

// insert tracc footer
$traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'Report Cover', A4_PORTRAIT);
$traccFooter->prerender($pdf);
$unpaidCreditorsPages = $page;
$pdf->end_page_ext('');



$management_data = dbGetSubReportData($propertyID, $s['sub_report_id'], $calendar['period'], $calendar['year']);
$management_data_column = dbGetSubReportDataColumn($s['sub_report_id']);
$pdf->begin_page_ext(595, 842, '');
$pdf->setFontExt($_fonts['Helvetica'], 8);
$pdf->showBoxed($management_data_column[1][0]['label'], 51, 780, 99, 20, $management_data_column[1][0]['alignment'], '');
$pdf->showBoxed($management_data_column[1][1]['label'], 151, 780, 99, 20, $management_data_column[1][1]['alignment'], '');
$pdf->showBoxed($management_data_column[1][2]['label'], 251, 780, 99, 20, $management_data_column[1][2]['alignment'], '');
$pdf->showBoxed($management_data_column[1][3]['label'], 351, 780, 99, 20, $management_data_column[1][3]['alignment'], '');
// 1ST COLUMN
$pdf->moveto(50, 780);
$pdf->lineto(50, 800);
$pdf->stroke();

$pdf->moveto(150, 780);
$pdf->lineto(150, 800);
$pdf->stroke();

$pdf->moveto(50, 800);
$pdf->lineto(150, 800);
$pdf->stroke();

$pdf->moveto(50, 780);
$pdf->lineto(150, 780);
$pdf->stroke();

// 2ND COLUMN
$pdf->moveto(150, 780);
$pdf->lineto(150, 800);
$pdf->stroke();

$pdf->moveto(250, 780);
$pdf->lineto(250, 800);
$pdf->stroke();

$pdf->moveto(150, 800);
$pdf->lineto(250, 800);
$pdf->stroke();

$pdf->moveto(150, 780);
$pdf->lineto(250, 780);
$pdf->stroke();

// 3RD COLUMN
$pdf->moveto(250, 780);
$pdf->lineto(250, 800);
$pdf->stroke();

$pdf->moveto(350, 780);
$pdf->lineto(350, 800);
$pdf->stroke();

$pdf->moveto(250, 800);
$pdf->lineto(350, 800);
$pdf->stroke();

$pdf->moveto(250, 780);
$pdf->lineto(350, 780);
$pdf->stroke();

// 4TH COLUMN
$pdf->moveto(350, 780);
$pdf->lineto(350, 800);
$pdf->stroke();

$pdf->moveto(450, 780);
$pdf->lineto(450, 800);
$pdf->stroke();

$pdf->moveto(350, 800);
$pdf->lineto(450, 800);
$pdf->stroke();

$pdf->moveto(350, 780);
$pdf->lineto(450, 780);
$pdf->stroke();

$line = 20;
foreach ($management_data as $row) {

    if ($row['table_id'] == 1) {
        $pdf->showBoxed($row['column_1'], 51, 780 - $line, 99, 20, 'left', '');
        $pdf->showBoxed($row['column_2'], 151, 780 - $line, 99, 20, 'left', '');
        $pdf->showBoxed($row['column_3'], 251, 780 - $line, 99, 20, 'left', '');
        $pdf->showBoxed($row['column_4'], 351, 780 - $line, 99, 20, 'left', '');

        // 1ST COLUMN
        $pdf->moveto(50, 780 - $line);
        $pdf->lineto(50, 800 - $line);
        $pdf->stroke();

        $pdf->moveto(150, 780 - $line);
        $pdf->lineto(150, 800 - $line);
        $pdf->stroke();

        $pdf->moveto(50, 800 - $line);
        $pdf->lineto(150, 800 - $line);
        $pdf->stroke();

        $pdf->moveto(50, 780 - $line);
        $pdf->lineto(150, 780 - $line);
        $pdf->stroke();

        // 2ND COLUMN
        $pdf->moveto(150, 780 - $line);
        $pdf->lineto(150, 800 - $line);
        $pdf->stroke();

        $pdf->moveto(250, 780 - $line);
        $pdf->lineto(250, 800 - $line);
        $pdf->stroke();

        $pdf->moveto(150, 800 - $line);
        $pdf->lineto(250, 800 - $line);
        $pdf->stroke();

        $pdf->moveto(150, 780 - $line);
        $pdf->lineto(250, 780 - $line);
        $pdf->stroke();

        // 3RD COLUMN
        $pdf->moveto(250, 780 - $line);
        $pdf->lineto(250, 800 - $line);
        $pdf->stroke();

        $pdf->moveto(350, 780 - $line);
        $pdf->lineto(350, 800 - $line);
        $pdf->stroke();

        $pdf->moveto(250, 800 - $line);
        $pdf->lineto(350, 800 - $line);
        $pdf->stroke();

        $pdf->moveto(250, 780 - $line);
        $pdf->lineto(350, 780 - $line);
        $pdf->stroke();

        // 4TH COLUMN
        $pdf->moveto(350, 780 - $line);
        $pdf->lineto(350, 800 - $line);
        $pdf->stroke();

        $pdf->moveto(450, 780 - $line);
        $pdf->lineto(450, 800 - $line);
        $pdf->stroke();

        $pdf->moveto(350, 800 - $line);
        $pdf->lineto(450, 800 - $line);
        $pdf->stroke();

        $pdf->moveto(350, 780 - $line);
        $pdf->lineto(450, 780 - $line);
        $pdf->stroke();

        $line += 20;
    }
}


// 2ND TABLE
$line += 40;
$pdf->showBoxed($management_data_column[1][0]['label'], 51, 780 - $line, 99, 20, $management_data_column[1][0]['alignment'], '');
$pdf->showBoxed($management_data_column[1][1]['label'], 151, 780 - $line, 99, 20, $management_data_column[1][1]['alignment'], '');
$pdf->showBoxed($management_data_column[1][2]['label'], 251, 780 - $line, 99, 20, $management_data_column[1][2]['alignment'], '');

// 1ST COLUMN
$pdf->moveto(50, 780 - $line);
$pdf->lineto(50, 800 - $line);
$pdf->stroke();

$pdf->moveto(150, 780 - $line);
$pdf->lineto(150, 800 - $line);
$pdf->stroke();

$pdf->moveto(50, 800 - $line);
$pdf->lineto(150, 800 - $line);
$pdf->stroke();

$pdf->moveto(50, 780 - $line);
$pdf->lineto(150, 780 - $line);
$pdf->stroke();

// 2ND COLUMN
$pdf->moveto(150, 780 - $line);
$pdf->lineto(150, 800 - $line);
$pdf->stroke();

$pdf->moveto(250, 780 - $line);
$pdf->lineto(250, 800 - $line);
$pdf->stroke();

$pdf->moveto(150, 800 - $line);
$pdf->lineto(250, 800 - $line);
$pdf->stroke();

$pdf->moveto(150, 780 - $line);
$pdf->lineto(250, 780 - $line);
$pdf->stroke();

// 3RD COLUMN
$pdf->moveto(250, 780 - $line);
$pdf->lineto(250, 800 - $line);
$pdf->stroke();

$pdf->moveto(350, 780 - $line);
$pdf->lineto(350, 800 - $line);
$pdf->stroke();

$pdf->moveto(250, 800 - $line);
$pdf->lineto(350, 800 - $line);
$pdf->stroke();

$pdf->moveto(250, 780 - $line);
$pdf->lineto(350, 780 - $line);
$pdf->stroke();


$line += 20;
foreach ($management_data as $row) {

    if ($row['table_id'] == 2) {
        $pdf->showBoxed($row['column_1'], 51, 780 - $line, 99, 20, 'left', '');
        $pdf->showBoxed($row['column_2'], 151, 780 - $line, 99, 20, 'left', '');
        $pdf->showBoxed($row['column_3'], 251, 780 - $line, 99, 20, 'left', '');

        // 1ST COLUMN
        $pdf->moveto(50, 780 - $line);
        $pdf->lineto(50, 800 - $line);
        $pdf->stroke();

        $pdf->moveto(150, 780 - $line);
        $pdf->lineto(150, 800 - $line);
        $pdf->stroke();

        $pdf->moveto(50, 800 - $line);
        $pdf->lineto(150, 800 - $line);
        $pdf->stroke();

        $pdf->moveto(50, 780 - $line);
        $pdf->lineto(150, 780 - $line);
        $pdf->stroke();

        // 2ND COLUMN
        $pdf->moveto(150, 780 - $line);
        $pdf->lineto(150, 800 - $line);
        $pdf->stroke();

        $pdf->moveto(250, 780 - $line);
        $pdf->lineto(250, 800 - $line);
        $pdf->stroke();

        $pdf->moveto(150, 800 - $line);
        $pdf->lineto(250, 800 - $line);
        $pdf->stroke();

        $pdf->moveto(150, 780 - $line);
        $pdf->lineto(250, 780 - $line);
        $pdf->stroke();

        // 3RD COLUMN
        $pdf->moveto(250, 780 - $line);
        $pdf->lineto(250, 800 - $line);
        $pdf->stroke();

        $pdf->moveto(350, 780 - $line);
        $pdf->lineto(350, 800 - $line);
        $pdf->stroke();

        $pdf->moveto(250, 800 - $line);
        $pdf->lineto(350, 800 - $line);
        $pdf->stroke();

        $pdf->moveto(250, 780 - $line);
        $pdf->lineto(350, 780 - $line);
        $pdf->stroke();


        $line += 20;
    }
}

// insert tracc footer
$traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'Report Cover', A4_PORTRAIT);
$traccFooter->prerender($pdf);
$unpaidCreditorsPages = $page;
$pdf->end_page_ext('');
foreach ($management_upload as $file) {
    unlink($file);
}

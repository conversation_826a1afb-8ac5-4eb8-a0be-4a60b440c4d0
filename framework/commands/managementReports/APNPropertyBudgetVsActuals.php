<?php

function getDataForBudgetAndActuals($property)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $params = [];
    $sql = "
            SELECT 
                RTRIM(all_trans.propID) as propID, 
                RTRIM(pmpr_property.pmpr_name) as propName, 
                RTRIM(pmoc_o_chart.pmoc_owner_acc) as clientAccountCode, 
                RTRIM(pmoc_o_chart.pmoc_desc) as clientAccountName, 
                RTRIM(all_trans.accountID) as accountID, 
                RTRIM(accounts.pmca_name) as acctName, 
                RTRIM(all_trans.leaseID) as leaseID, 
                RTRIM(pmle_lease.pmle_debtor) as debtorCode, 
                RTRIM(pmco_company.pmco_name) as tenantName, 
                RTRIM(all_trans.unitID) as suiteID, 
                all_trans.periodyear as period,
                all_trans.year as year,
                all_trans.amount as amount,
                all_trans.src as src
            FROM 
            (
                SELECT
                    gl_transaction.property_id as propID, 
                    gl_transaction.lease_id as leaseID, 
                    units.pmua_unit as unitID, 
                    gl_transaction.account_id as accountID,
                    CONCAT(gl_transaction.year,REPLACE(STR(gl_transaction.period,2),' ','0')) as periodyear,
                    gl_transaction.year as year,
                    COALESCE(SUM(gl_transaction.transaction_amount), 0) as amount,
                    'ACT' as src
                FROM gl_transaction
                LEFT JOIN pmua_unit_area units
                    ON gl_transaction.property_id = units.pmua_prop
                    AND gl_transaction.lease_id = units.pmua_lease
                    AND CONVERT(datetime, gl_transaction.transaction_date, 103) BETWEEN pmua_from_dt AND pmua_to_dt
                    AND units.pmua_status = 'O'
                WHERE gl_transaction.property_id = " . addSQLParam($params, $property) . "
                  AND gl_transaction.account_id != ''
                  AND gl_transaction.method = 1
                GROUP BY gl_transaction.property_id, gl_transaction.account_id, gl_transaction.lease_id, units.pmua_unit, gl_transaction.year, gl_transaction.period
                UNION
                SELECT propID, leaseID, unitID, accountID, periodyear, year, amount, 'BUD' as src
                FROM 
                (
                    SELECT
                        pmep_prop as propID,
                        '' as leaseID,
                        pmep_exp_acc as accountID,
                        '' as unitID,
                        CONCAT(pmep_year,REPLACE(STR(pmep_per,2),' ','0')) as periodyear,
                        pmep_year as year,
                        COALESCE(SUM(pmep_b_a_amt), 0) as amount 
                    FROM pmep_b_exp_per
                    WHERE
                      pmep_prop = " . addSQLParam($params, $property) . "
                    GROUP BY pmep_prop, pmep_exp_acc, pmep_per, pmep_year
                    UNION
                    SELECT
                        pmrp_prop as propID,
                        pmrp_lease as leaseID,
                        pmrp_acc as accountID,
                        pmrp_unit as unitID,
                        CONCAT(pmrp_year,REPLACE(STR(pmrp_per,2),' ','0')) as periodyear,
                        pmrp_year as year,
                        COALESCE(SUM(pmrp_b_a_amt), 0)*-1 as amount
                    FROM pmrp_b_rev_per
                    WHERE
                      pmrp_prop = '{$property}'
                    GROUP BY pmrp_prop, pmrp_lease, pmrp_acc, pmrp_per, pmrp_year, pmrp_unit
                ) as budgets
                WHERE amount != 0 AND amount IS NOT NULL
                UNION ALL
                SELECT propID, leaseID, unitID, accountID, periodyear, year, amount, 'FCST' as src
                FROM 
                (
                    SELECT
                        pmep_prop as propID,
                        '' as leaseID,
                        pmep_exp_acc as accountID,
                        '' as unitID,
                        CONCAT(pmep_year,REPLACE(STR(pmep_per,2),' ','0')) as periodyear,
                        pmep_year as year,
                        COALESCE(SUM(pmep_f_a_amt), 0) as amount 
                    FROM pmep_b_exp_per
                    WHERE
                        pmep_prop = " . addSQLParam($params, $property) . "
                    GROUP BY pmep_prop, pmep_exp_acc, pmep_per, pmep_year
                    UNION
                    SELECT
                        pmrp_prop as propID,
                        pmrp_lease as leaseID,
                        pmrp_acc as accountID,
                        pmrp_unit as unitID,
                        CONCAT(pmrp_year,REPLACE(STR(pmrp_per,2),' ','0')) as periodyear,
                        pmrp_year as year,
                        COALESCE(SUM(pmrp_f_a_amt), 0)*-1 as amount
                    FROM pmrp_b_rev_per
                    WHERE
                        pmrp_prop = " . addSQLParam($params, $property) . '
                    GROUP BY pmrp_prop, pmrp_lease, pmrp_acc, pmrp_per, pmrp_year, pmrp_unit
                    ) as forecasts
                WHERE amount != 0 AND amount IS NOT NULL
            ) as all_trans
            LEFT JOIN pmca_chart AS accounts ON accounts.pmca_code = all_trans.accountID
            LEFT JOIN pmpr_property ON all_trans.propID = pmpr_property.pmpr_prop
            LEFT JOIN pmle_lease
              ON all_trans.propID = pmle_lease.pmle_prop
              AND all_trans.leaseID = pmle_lease.pmle_lease
            LEFT JOIN pmco_company
              ON pmle_lease.pmle_debtor = pmco_company.pmco_code
            LEFT JOIN pmoc_o_chart ON pmoc_o_chart.pmoc_owner = pmpr_property.pmpr_owner AND pmoc_o_chart.pmoc_acc = all_trans.accountID
            ORDER BY period, propID, leaseID, accountID, unitID, src
            ';

    // pre_print_r($sql);

    return $dbh->executeSet($sql, false, true, $params);
}

// ======================================================================================================

global $clientDB, $clientDirectory, $dbh, $pathPrefix, $sess;

if ($view->items['property']) {
    $properties = deserializeParameters($view->items['property']);
} else {
    $properties = [$propertyID];
}

$period = $view->items['period'];
$year = $view->items['year'];
$prefix = dbGetParam('OACCOUNTPR', 'PREFIX'); // "AN";

$data = [];

foreach ($properties as $property) {
    // retrieve data from GL trial_balance and budget tables

    // //$ar_transactions = getDataForBudgetAndActuals($property, $period, $year);

    $all_transactions = getDataForBudgetAndActuals($property);
    foreach ($all_transactions as $tran) {
        $variance = variance($tran['actual'], $tran['budget']);
        $variancePercent = varianceDecimal($variance, $tran['budget']);

        $varianceYTD = variance($tran['actualYTD'], $tran['budgetYTD']);
        $variancePercentYTD = varianceDecimal($varianceYTD, $tran['budgetYTD']);

        $data[] = [
            'bldgID' => $tran['propID'],
            'buildingName' => $tran['propName'],
            'ownerCode' => $prefix . $tran['clientAccountCode'], // owner code
            'ownerName' => $tran['clientAccountName'], // owner description
            'accountID' => $tran['accountID'],
            'acctName' => $tran['acctName'],
            'suiteID' => $tran['suiteID'],
            'leaseID' => $tran['leaseID'] ? $tran['propID'] . '-' . $tran['leaseID'] : '',
            'tenantName' => $tran['tenantName'],
            'period' => $tran['period'],
            'netChange' => $tran['amount'] + 0,
            'src' => $tran['src'] . ($tran['src'] != 'ACT' ? substr($tran['year'], 2, 2) : ''),
        ];
    }
}

// ----------------------------------------------------------

// DOC_FLATFILES

$title = $s['subReportName'];
$filename = str_replace(' ', '_', $title) . '_' . date('dmYHis') . '.csv';
$_filePath = "{$pathPrefix}{$clientDirectory}/csv/" . DOC_FLATFILES . '/';
checkDirPath($_filePath);
$_downloadPath = "{$clientDirectory}/csv/" . DOC_FLATFILES;
$downloadLink = $_downloadPath . '/' . $filename; // for download link (used to display dl link on page)
$filePath = $_filePath . $filename; // for attachment (can used for email attachment also)

$fp = fopen($filePath, 'w');

$header = [
    'PROPERTY ID',
    'PROPERTY NAME',
    strtoupper($_SESSION['country_default']['trust_account']) . ' CODE',
    strtoupper($_SESSION['country_default']['trust_account']) . ' NAME',
    'ACCOUNT CODE',
    'ACCOUNT NAME',
    'SUITE ID',
    'LEASE ID',
    'TENANT NAME',
    'PERIOD',
    'NET CHANGE',
    'VERSION', // if ACT or BUD
];

fwrite($fp, implode(',', $header) . PHP_EOL);
foreach ($data as $fields) {
    //    fwrite($fp, implode(',',$fields) . PHP_EOL);
    fputcsv($fp, $fields);
}
fclose($fp);

// for csvReportProcess
$attachments[] = [
    'title' => $title,
    'downloadPath' => $downloadLink,
    'filepath' => $filePath,
    'content_type' => 'text/csv',
];

<?php

function newCommentaryPage(&$pdf, &$page, $dateToFormat, $propertyName, $propertyCity)
{
    $_fonts['Calibri'] = $pdf->load_font('Calibri', 'host', '');
    $_fonts['Calibri-Bold'] = $pdf->load_font('Calibri Bold', 'host', '');

    $maxWidth = 595;
    $maxHeight = 200;

    if ($page == 1) {
        $file = realpath('assets/images/agora/footer1.jpg');
        $pdfimage = $pdf->load_image('auto', $file, '');
        $pdf->fit_image($pdfimage, 0, -60, 'boxsize {' . "{$maxWidth} {$maxHeight}" . '} position={center} fitmethod=meet');
    } else {
        $file = realpath('assets/images/agora/footer2.jpg');
        $pdfimage = $pdf->load_image('auto', $file, '');
        $pdf->fit_image($pdfimage, 0, -87, 'boxsize {' . "{$maxWidth} {$maxHeight}" . '} position={center} fitmethod=meet');
    }

    $pdf->end_page_ext('');

    $pdf->begin_page_ext(595, 842, '');

    $page += 1;

    $file = realpath('assets/images/agora/letterhead2.jpg');
    $pdfimage = $pdf->load_image('auto', $file, '');
    $maxWidth = 595;
    $maxHeight = 200;
    $pdf->fit_image($pdfimage, 0, 700, 'boxsize {' . "{$maxWidth} {$maxHeight}" . '} position={center} fitmethod=meet');

    switch (date('S', strtotime($dateToFormat))) {
        case 'st': $superScript = "\u{02E2}" . "\u{1D57}";
            break;
        case 'nd': $superScript = "\u{207F}" . "\u{1D48}";
            break;
        case 'rd': $superScript = "\u{02B3}" . "\u{1D48}";
            break;
        case 'th': $superScript = "\u{1D57}" . "\u{02B0}";
            break;
    }

    $pdf->setFontExt($_fonts['Calibri-Bold'], 9);
    $pdf->setColorExt('both', 'rgb', 0.07, 0.13, 0.26, 0);
    $pdf->showBoxed(date('d', strtotime($dateToFormat)) . $superScript . date(' F Y', strtotime($dateToFormat)), 40, 808, 500, 10, 'right', '');

    $pdf->showBoxed($page, 40, 793, 500, 10, 'right', '');
    $pdf->setColorExt('both', 'rgb', 0.95, 0.44, 0.17, 0);
    $pdf->showBoxed('|', 40, 793, 495, 10, 'right', '');
    $pdf->setColorExt('both', 'rgb', 0.07, 0.13, 0.26, 0);
    $pdf->showBoxed('Page', 40, 793, 490, 10, 'right', '');

    $pdf->showBoxed(ucwords(strtolower($propertyName . ', ' . $propertyCity)), 40, 778, 500, 10, 'right', '');
    //    $pdf->showBoxed (, 40, 763, 500 ,10,"right", "");
}

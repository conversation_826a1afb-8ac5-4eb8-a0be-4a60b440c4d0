<?php

if (! function_exists('commentLineThreeDisplay')) {
    function commentLineThreeDisplay(&$pdf, $value, $xAxis, $yAxis, $width, $height, $alignment, $charCount)
    {

        if (strlen($value) > $charCount * 2) {
            $yAxis -= 2;
        } elseif (strlen($value) > $charCount) {
            $yAxis -= 4;
        } else {
            $yAxis -= 11;
        }

        $pdf->showBoxed($value, $xAxis, $yAxis, $width, $height, $alignment, '');

    }
}

if (! function_exists('new_page_custom')) {
    function new_page_custom(&$pdf)
    {
        global $_fonts, $reportDescription;
        global $propertyID;
        global $client,$periodTo;
        global $propertyName;

        $pdf->begin_page_ext(595, 842, '');

        generateLogo();

        $prop_result = getAgentOwnerCodes($propertyID);
        $agent_code = $prop_result['agent_code'];
        $owner_code = $prop_result['owner_code'];

        $agentDetails = getCompAddress($agent_code);
        $ownerDetails = getCompAddress($owner_code);

        // generate logo
        if ($logo) {
            generateLogo();
        }

        $agentData = dbGetAgentDetails();
        $agentDetailsNew = new agentDetails($propertyID, true);
        $agentDetailsNew->noManager = true;
        $agentDetailsNew->bindAttributesFrom($agentData);
        $agentDetailsNew->render($pdf);

        $pdf->setFontExt($_fonts['Helvetica'], 8);
        $pdf->show_xy('Owner: ', 25, 750);
        $pdf->continue_text('Property: ');
        $pdf->continue_text('Report for: ');

        $pdf->show_xy("{$client}", 85, 750);
        $pdf->continue_text("{$propertyName} [{$propertyID}]");
        $pdf->continue_text(date('M Y', strtotime(convertDate($periodTo))));
    }
}

require_once SYSTEMPATH . '/commands/managementReports/Residential/residentialReportFunctions.php';
require_once SYSTEMPATH . '/commands/managementReports/propertyReport/C8Demo/summaryFunction.php';

global $_fonts, $reportDescription;
global $clientDB;

$page++;

$totals  = [];
$totals1 = [];

new_page_custom($pdf);
$typeTitle = [
    'INC.OWN' => 'Owner Income',
    'INC.REC' => 'Directly Recoverable Income',
    'INC.OUT' => ucwords(strtolower($_SESSION['country_default']['variable_outgoings'])) . ' Income',
    'EXP.OWN' => 'Owner Expenses',
    'EXP.REC' => 'Directly Recoverable Expenses',
    'EXP.OUT' => ucwords(strtolower($_SESSION['country_default']['variable_outgoings'])) . ' Expenses',
];


$pdf->setFontExt($_fonts['Helvetica-Bold'], 9);
$pdf->showBoxed('INCOME ANALYSIS', 100, 690, 400, 20, 'center', '');

$line = 670;

$pdf->setColorExt('fill', 'rgb', 0.85, 0.88, 0.95, 0);
$pdf->rect(25, $line, 545, 15);
$pdf->fill();

$pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
$pdf->setFontExt($_fonts['Helvetica-Bold'], 7);


// ////////////////////Income ‐ Budget Variances (current month)////////////

$pdf->setColorExt('fill', 'rgb', 0.85, 0.88, 0.95, 0);
$pdf->rect(25, $line, 545, 15);
$pdf->fill();

$pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
$pdf->setFontExt($_fonts['Helvetica-Bold'], 7);

$pdf->showBoxed('Budget Variances (current month)', 25, $line - 4, 545, 15, 'center', '');

// $line -= 20;
$originalLine = $line;

$line -= 24;

$pdf->setFontExt($_fonts['Helvetica-Bold'], 7);
$pdf->showBoxed('Description', 27, $line - 9, 103, 24, 'left', '');
$pdf->showBoxed('Actual ($)', 130, $line - 9, 70, 24, 'right', '');
$pdf->showBoxed('Budget ($)', 200, $line - 9, 70, 24, 'right', '');
$pdf->showBoxed('Variance ($)', 270, $line - 9, 70, 24, 'right', '');
$pdf->showBoxed('Variance (%)', 340, $line - 9, 70, 24, 'right', '');
$pdf->showBoxed('Comment', 412, $line - 9, 158, 24, 'left', '');

$management_data_sequence = dbGetSubReportDataConfig($s['sub_report_id']);
$management_data = dbGetSubReportDataWithAccounts($propertyID, $s['sub_report_id'], $calendar['period'], $calendar['year']);

$subHeader = '';
$parity = 1;
if (isset($management_data_sequence[1])) {
    foreach ($management_data as $row) {
        if ($row['table_id'] == $management_data_sequence[1]) {

            $line -= 24;
            if ($line < 50) {

                $line += 24;

                $traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'customLongReport', A4_PORTRAIT);
                $traccFooter->prerender($pdf);

                $pdf->end_page_ext('');


                new_page_custom($pdf);

                $pdf->setFontExt($_fonts['Helvetica-Bold'], 9);
                $pdf->showBoxed('INCOME ANALYSIS', 100, 690, 400, 20, 'center', '');

                $line = 670;

                $pdf->setColorExt('fill', 'rgb', 0.85, 0.88, 0.95, 0);
                $pdf->rect(25, $line, 545, 15);
                $pdf->fill();

                $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
                $pdf->setFontExt($_fonts['Helvetica-Bold'], 7);
                $pdf->showBoxed('Budget Variances (current month)', 25, $line - 4, 545, 15, 'center', '');

                // $line -= 20;
                $originalLine = $line;

                // $pdf->setlinewidth(0.5);
                // $pdf->moveto(25, $line);
                // $pdf->lineto(570, $line);
                // $pdf->stroke();

                $line -= 24;

                $pdf->setFontExt($_fonts['Helvetica-Bold'], 7);
                $pdf->showBoxed('Description', 27, $line - 9, 103, 24, 'left', '');
                $pdf->showBoxed('Actual ($)', 130, $line - 9, 70, 24, 'right', '');
                $pdf->showBoxed('Budget ($)', 200, $line - 9, 70, 24, 'right', '');
                $pdf->showBoxed('Variance ($)', 270, $line - 9, 70, 24, 'right', '');
                $pdf->showBoxed('Variance (%)', 340, $line - 9, 70, 24, 'right', '');
                $pdf->showBoxed('Comment', 412, $line - 9, 158, 24, 'left', '');

                $line -= 24;
                $parity = 1;

                if ($parity === 1) {
                    $pdf->setColorExt('fill', 'rgb', 0.91, 0.94, 0.97, 1);
                } else {
                    $pdf->setColorExt('fill', 'rgb', 1, 1, 1, 1);
                }

                $line -= 4;
                $pdf->rect(25, $line - 4, 545, 28);
                $pdf->fill();

                $subHeader = trim($row['pmca_gl_account_group2']);
                $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
                $pdf->setFontExt($_fonts['Helvetica-Bold'], 7);
                $pdf->showBoxed($typeTitle[$subHeader], 27, $line - 9, 200, 24, 'left', '');

                $line -= 24;
                $parity *= -1;
            }


            if ($subHeader !== trim($row['pmca_gl_account_group2'])) {

                if ($parity == 1) {
                    $pdf->setColorExt('fill', 'rgb', 0.91, 0.94, 0.97, 1);
                } else {
                    $pdf->setColorExt('fill', 'rgb', 1, 1, 1, 1);
                }

                $line -= 4;
                $pdf->rect(25, $line - 4, 545, 28);
                $pdf->fill();

                $subHeader = trim($row['pmca_gl_account_group2']);
                $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
                $pdf->setFontExt($_fonts['Helvetica-Bold'], 7);
                $pdf->showBoxed($typeTitle[$subHeader], 27, $line - 9, 200, 24, 'left', '');

                $line -= 24;
                $parity *= -1;
            }


            if ($line < 50) {

                $line += 24;

                $traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'customLongReport', A4_PORTRAIT);
                $traccFooter->prerender($pdf);

                $pdf->end_page_ext('');


                new_page_custom($pdf);

                $pdf->setFontExt($_fonts['Helvetica-Bold'], 9);
                $pdf->showBoxed('INCOME ANALYSIS', 100, 690, 400, 20, 'center', '');

                $line = 670;

                $pdf->setColorExt('fill', 'rgb', 0.85, 0.88, 0.95, 0);
                $pdf->rect(25, $line, 545, 15);
                $pdf->fill();

                $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
                $pdf->setFontExt($_fonts['Helvetica-Bold'], 7);
                $pdf->showBoxed('Budget Variances (current month)', 25, $line - 4, 545, 15, 'center', '');

                // $line -= 20;
                $originalLine = $line;

                // $pdf->setlinewidth(0.5);
                // $pdf->moveto(25, $line);
                // $pdf->lineto(570, $line);
                // $pdf->stroke();

                $line -= 24;

                $pdf->setFontExt($_fonts['Helvetica-Bold'], 7);
                $pdf->showBoxed('Description', 27, $line - 9, 103, 24, 'left', '');
                $pdf->showBoxed('Actual ($)', 130, $line - 9, 70, 24, 'right', '');
                $pdf->showBoxed('Budget ($)', 200, $line - 9, 70, 24, 'right', '');
                $pdf->showBoxed('Variance ($)', 270, $line - 9, 70, 24, 'right', '');
                $pdf->showBoxed('Variance (%)', 340, $line - 9, 70, 24, 'right', '');
                $pdf->showBoxed('Comment', 412, $line - 9, 158, 24, 'left', '');

                $line -= 24;
                $parity = 1;

                if ($parity === 1) {
                    $pdf->setColorExt('fill', 'rgb', 0.91, 0.94, 0.97, 1);
                } else {
                    $pdf->setColorExt('fill', 'rgb', 1, 1, 1, 1);
                }

                $line -= 4;
                $pdf->rect(25, $line - 4, 545, 28);
                $pdf->fill();
                $pdf->fill();

                $subHeader = trim($row['pmca_gl_account_group2']);
                $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
                $pdf->setFontExt($_fonts['Helvetica-Bold'], 7);
                $pdf->showBoxed($typeTitle[$subHeader], 27, $line - 9, 200, 24, 'left', '');

                $line -= 24;
                $parity *= -1;
            }



            if ($parity == 1) {
                $pdf->setColorExt('fill', 'rgb', 0.91, 0.94, 0.97, 1);
            } else {
                $pdf->setColorExt('fill', 'rgb', 1, 1, 1, 1);
            }

            $line -= 4;
            $pdf->rect(25, $line - 4, 545, 28);
            $pdf->fill();

            $parity *= -1;
            $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);

            $pdf->setFontExt($_fonts['Helvetica'], 7);
            commentLineThreeDisplay($pdf, trim($row['column_2']), 27, $line, 103, 24, 'left', 32);
            $pdf->showBoxed(toMoney($row['column_3'], ''), 130, $line - 9, 68, 24, 'right', '');
            $pdf->showBoxed(toMoney($row['column_4'], ''), 200, $line - 9, 68, 24, 'right', '');
            $pdf->showBoxed(toMoney($row['column_5'], ''), 270, $line - 9, 68, 24, 'right', '');
            $pdf->showBoxed(toMoney($row['column_6'], '') . '', 340, $line - 9, 68, 24, 'right', '');
            commentLineThreeDisplay($pdf, $row['column_0'], 412, $line, 158, 24, 'left', 49);
        }
    }
}


$line -= 30;
// ////////////////////Income ‐ Budget Variances (YTD)////////////

$pdf->setColorExt('fill', 'rgb', 0.85, 0.88, 0.95, 0);
$pdf->rect(25, $line, 545, 15);
$pdf->fill();

$pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
$pdf->setFontExt($_fonts['Helvetica-Bold'], 7);

$pdf->showBoxed('Budget Variances (YTD)', 25, $line - 4, 545, 15, 'center', '');

// $line -= 20;
$originalLine = $line;


$line -= 24;

$pdf->setFontExt($_fonts['Helvetica-Bold'], 7);
$pdf->showBoxed('Description', 27, $line - 9, 103, 24, 'left', '');
$pdf->showBoxed('Actual ($)', 130, $line - 9, 70, 24, 'right', '');
$pdf->showBoxed('Budget ($)', 200, $line - 9, 70, 24, 'right', '');
$pdf->showBoxed('Variance ($)', 270, $line - 9, 70, 24, 'right', '');
$pdf->showBoxed('Variance (%)', 340, $line - 9, 70, 24, 'right', '');
$pdf->showBoxed('Comment', 412, $line - 9, 158, 24, 'left', '');

$subHeader = '';
$parity = 1;
if (isset($management_data_sequence[2])) {
    foreach ($management_data as $row) {
        if ($row['table_id'] == $management_data_sequence[2]) {

            $line -= 24;

            if ($line < 50) {

                $line += 24;

                $traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'customLongReport', A4_PORTRAIT);
                $traccFooter->prerender($pdf);

                $pdf->end_page_ext('');


                new_page_custom($pdf);

                $pdf->setFontExt($_fonts['Helvetica-Bold'], 9);
                $pdf->showBoxed('INCOME ANALYSIS', 100, 690, 400, 20, 'center', '');

                $line = 670;

                $pdf->setColorExt('fill', 'rgb', 0.85, 0.88, 0.95, 0);
                $pdf->rect(25, $line, 545, 15);
                $pdf->fill();

                $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
                $pdf->setFontExt($_fonts['Helvetica-Bold'], 7);
                $pdf->showBoxed('Budget Variances (YTD)', 25, $line - 4, 545, 15, 'center', '');

                // $line -= 20;
                $originalLine = $line;

                // $pdf->setlinewidth(0.5);
                // $pdf->moveto(25, $line);
                // $pdf->lineto(570, $line);
                // $pdf->stroke();

                $line -= 24;

                $pdf->setFontExt($_fonts['Helvetica-Bold'], 7);
                $pdf->showBoxed('Description', 27, $line - 9, 103, 24, 'left', '');
                $pdf->showBoxed('Actual ($)', 130, $line - 9, 70, 24, 'right', '');
                $pdf->showBoxed('Budget ($)', 200, $line - 9, 70, 24, 'right', '');
                $pdf->showBoxed('Variance ($)', 270, $line - 9, 70, 24, 'right', '');
                $pdf->showBoxed('Variance (%)', 340, $line - 9, 70, 24, 'right', '');
                $pdf->showBoxed('Comment', 412, $line - 9, 158, 24, 'left', '');

                $line -= 24;
                $parity = 1;

                if ($parity === 1) {
                    $pdf->setColorExt('fill', 'rgb', 0.91, 0.94, 0.97, 1);
                } else {
                    $pdf->setColorExt('fill', 'rgb', 1, 1, 1, 1);
                }

                $line -= 4;
                $pdf->rect(25, $line - 4, 545, 28);
                $pdf->fill();

                $subHeader = trim($row['pmca_gl_account_group2']);
                $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
                $pdf->setFontExt($_fonts['Helvetica-Bold'], 7);
                $pdf->showBoxed($typeTitle[$subHeader], 27, $line - 9, 200, 24, 'left', '');

                $line -= 24;
                $parity *= -1;
            }

            if ($subHeader !== trim($row['pmca_gl_account_group2'])) {

                if ($parity == 1) {
                    $pdf->setColorExt('fill', 'rgb', 0.91, 0.94, 0.97, 1);
                } else {
                    $pdf->setColorExt('fill', 'rgb', 1, 1, 1, 1);
                }

                $line -= 4;
                $pdf->rect(25, $line - 4, 545, 28);
                $pdf->fill();

                $subHeader = trim($row['pmca_gl_account_group2']);
                $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
                $pdf->setFontExt($_fonts['Helvetica-Bold'], 7);
                $pdf->showBoxed($typeTitle[$subHeader], 27, $line - 9, 200, 24, 'left', '');

                $line -= 24;
                $parity *= -1;
            }


            if ($line < 50) {

                $line += 24;

                $traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'customLongReport', A4_PORTRAIT);
                $traccFooter->prerender($pdf);

                $pdf->end_page_ext('');


                new_page_custom($pdf);

                $pdf->setFontExt($_fonts['Helvetica-Bold'], 9);
                $pdf->showBoxed('INCOME ANALYSIS', 100, 690, 400, 20, 'center', '');

                $line = 670;

                $pdf->setColorExt('fill', 'rgb', 0.85, 0.88, 0.95, 0);
                $pdf->rect(25, $line, 545, 15);
                $pdf->fill();

                $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
                $pdf->setFontExt($_fonts['Helvetica-Bold'], 7);
                $pdf->showBoxed('Budget Variances (YTD)', 25, $line - 4, 545, 15, 'center', '');

                // $line -= 20;
                $originalLine = $line;

                // $pdf->setlinewidth(0.5);
                // $pdf->moveto(25, $line);
                // $pdf->lineto(570, $line);
                // $pdf->stroke();

                $line -= 24;

                $pdf->setFontExt($_fonts['Helvetica-Bold'], 7);
                $pdf->showBoxed('Description', 27, $line - 9, 103, 24, 'left', '');
                $pdf->showBoxed('Actual ($)', 130, $line - 9, 70, 24, 'right', '');
                $pdf->showBoxed('Budget ($)', 200, $line - 9, 70, 24, 'right', '');
                $pdf->showBoxed('Variance ($)', 270, $line - 9, 70, 24, 'right', '');
                $pdf->showBoxed('Variance (%)', 340, $line - 9, 70, 24, 'right', '');
                $pdf->showBoxed('Comment', 412, $line - 9, 158, 24, 'left', '');

                $line -= 24;
                $parity = 1;

                if ($parity === 1) {
                    $pdf->setColorExt('fill', 'rgb', 0.91, 0.94, 0.97, 1);
                } else {
                    $pdf->setColorExt('fill', 'rgb', 1, 1, 1, 1);
                }

                $line -= 4;
                $pdf->rect(25, $line - 4, 545, 28);
                $pdf->fill();

                $subHeader = trim($row['pmca_gl_account_group2']);
                $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
                $pdf->setFontExt($_fonts['Helvetica-Bold'], 7);
                $pdf->showBoxed($typeTitle[$subHeader], 27, $line - 9, 200, 24, 'left', '');

                $line -= 24;
                $parity *= -1;
            }

            if ($parity == 1) {
                $pdf->setColorExt('fill', 'rgb', 0.91, 0.94, 0.97, 1);
            } else {
                $pdf->setColorExt('fill', 'rgb', 1, 1, 1, 1);
            }

            $line -= 4;
            $pdf->rect(25, $line - 4, 545, 28);
            $pdf->fill();

            $parity *= -1;
            $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);

            $pdf->setFontExt($_fonts['Helvetica'], 7);
            commentLineThreeDisplay($pdf, trim($row['column_2']), 27, $line, 103, 24, 'left', 32);
            $pdf->showBoxed(toMoney($row['column_3'], ''), 130, $line - 9, 68, 24, 'right', '');
            $pdf->showBoxed(toMoney($row['column_4'], ''), 200, $line - 9, 68, 24, 'right', '');
            $pdf->showBoxed(toMoney($row['column_5'], ''), 270, $line - 9, 68, 24, 'right', '');
            $pdf->showBoxed(toMoney($row['column_6'], '') . '', 340, $line - 9, 68, 24, 'right', '');
            commentLineThreeDisplay($pdf, $row['column_0'], 412, $line, 158, 24, 'left', 49);

        }
    }
}

$management_comments = dbGetSubReportComment($propertyID, $s['sub_report_id'], $calendar['period'], $calendar['year']);

$line -= 25;

// $pdf->setColorExt("both", "rgb", 0, 0, 0, 0);
// $pdf->setFontExt ($_fonts['Helvetica'], 7 );
// $pdf->showBoxed ($management_comments[1]['comment'], 25, $line, 545, 7 , "left", "");

if (trim($management_comments[1]['comment']) !== '' && trim($management_comments[1]['comment']) !== '0') {
    $generalComment = explode("\n", $management_comments[1]['comment']);
    foreach ($generalComment as $key => $com) {

        if ($line < 50) {
            $pdf->setFontExt($_fonts['Helvetica'], 8);
            $pdf->showBoxed("Page {$page}", 479, 7, 75, 30, 'right', '');
            $traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'customLongReport', A4_PORTRAIT);
            $traccFooter->footerAlignment = 'left';
            $traccFooter->prerender($pdf);
            $page++;

            $pdf->end_page_ext('');

            new_page_custom($pdf);

            $pdf->setFontExt($_fonts['Helvetica-Bold'], 9);
            $pdf->showBoxed('INCOME ANALYSIS', 100, 690, 400, 20, 'center', '');

            $line = 670;
        }


        $comment['comment'] = $com;
        $comment['bold'] = $management_comments[1]['bold'];
        $comment['size'] = $management_comments[1]['size'];
        $comment['bullet'] = $management_comments[1]['bullet'];
        $comment['listed'] = $management_comments[1]['listed'];

        $commentCount = ceil(strlen($com) / 115);
        $height = ($comment['size'] * ($commentCount + 1));
        management_report($pdf, $_fonts, $comment, 27, $line - $height, 540, $height, 'left', $key);
        $line -= $height;
    }
}

$line -= 30;

$traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'customLongReport', A4_PORTRAIT);
$traccFooter->prerender($pdf);

$pdf->end_page_ext('');

new_page_custom($pdf);

$pdf->setFontExt($_fonts['Helvetica-Bold'], 9);
$pdf->showBoxed('EXPENSE ANALYSIS', 100, 690, 400, 20, 'center', '');

$line = 670;


// ////////////////////Expenses ‐ Top 5 Budget Variances (current month)////////////

$pdf->setColorExt('fill', 'rgb', 0.85, 0.88, 0.95, 0);
$pdf->rect(25, $line, 545, 15);
$pdf->fill();

$pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
$pdf->setFontExt($_fonts['Helvetica-Bold'], 7);
$pdf->showBoxed('Budget Variances (current month)', 25, $line - 4, 545, 15, 'center', '');

// $line -= 20;
$originalLine = $line;

$line -= 24;

$pdf->setFontExt($_fonts['Helvetica-Bold'], 7);
$pdf->showBoxed('Description', 27, $line - 9, 103, 24, 'left', '');
$pdf->showBoxed('Actual ($)', 130, $line - 9, 70, 24, 'right', '');
$pdf->showBoxed('Budget ($)', 200, $line - 9, 70, 24, 'right', '');
$pdf->showBoxed('Variance ($)', 270, $line - 9, 70, 24, 'right', '');
$pdf->showBoxed('Variance (%)', 340, $line - 9, 70, 24, 'right', '');
$pdf->showBoxed('Comment', 412, $line - 9, 158, 24, 'left', '');

$subHeader = '';
$parity = 1;
if (isset($management_data_sequence[3])) {
    foreach ($management_data as $row) {
        if ($row['table_id'] == $management_data_sequence[3]) {

            $line -= 24;

            if ($line < 50) {

                $line += 24;

                $traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'customLongReport', A4_PORTRAIT);
                $traccFooter->prerender($pdf);

                $pdf->end_page_ext('');


                new_page_custom($pdf);

                $pdf->setFontExt($_fonts['Helvetica-Bold'], 9);
                $pdf->showBoxed('EXPENSE ANALYSIS', 100, 690, 400, 20, 'center', '');

                $line = 670;

                $pdf->setColorExt('fill', 'rgb', 0.85, 0.88, 0.95, 0);
                $pdf->rect(25, $line, 545, 15);
                $pdf->fill();

                $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
                $pdf->setFontExt($_fonts['Helvetica-Bold'], 7);
                $pdf->showBoxed('Budget Variances (current month)', 25, $line - 4, 545, 15, 'center', '');

                // $line -= 20;
                $originalLine = $line;

                // $pdf->setlinewidth(0.5);
                // $pdf->moveto(25, $line);
                // $pdf->lineto(570, $line);
                // $pdf->stroke();

                $line -= 24;

                $pdf->setFontExt($_fonts['Helvetica-Bold'], 7);
                $pdf->showBoxed('Description', 27, $line - 9, 103, 24, 'left', '');
                $pdf->showBoxed('Actual ($)', 130, $line - 9, 70, 24, 'right', '');
                $pdf->showBoxed('Budget ($)', 200, $line - 9, 70, 24, 'right', '');
                $pdf->showBoxed('Variance ($)', 270, $line - 9, 70, 24, 'right', '');
                $pdf->showBoxed('Variance (%)', 340, $line - 9, 70, 24, 'right', '');
                $pdf->showBoxed('Comment', 412, $line - 9, 158, 24, 'left', '');

                $line -= 24;
                $parity = 1;

                if ($parity === 1) {
                    $pdf->setColorExt('fill', 'rgb', 0.91, 0.94, 0.97, 1);
                } else {
                    $pdf->setColorExt('fill', 'rgb', 1, 1, 1, 1);
                }

                $line -= 4;
                $pdf->rect(25, $line - 4, 545, 28);
                $pdf->fill();

                $subHeader = trim($row['pmca_gl_account_group2']);
                $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
                $pdf->setFontExt($_fonts['Helvetica-Bold'], 7);
                $pdf->showBoxed($typeTitle[$subHeader], 27, $line - 9, 200, 24, 'left', '');

                $line -= 24;
                $parity *= -1;
            }

            if ($subHeader !== trim($row['pmca_gl_account_group2'])) {

                if ($parity == 1) {
                    $pdf->setColorExt('fill', 'rgb', 0.91, 0.94, 0.97, 1);
                } else {
                    $pdf->setColorExt('fill', 'rgb', 1, 1, 1, 1);
                }

                $line -= 4;
                $pdf->rect(25, $line - 4, 545, 28);
                $pdf->fill();

                $subHeader = trim($row['pmca_gl_account_group2']);
                $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
                $pdf->setFontExt($_fonts['Helvetica-Bold'], 7);
                $pdf->showBoxed($typeTitle[$subHeader], 27, $line - 9, 200, 24, 'left', '');

                $line -= 24;
                $parity *= -1;
            }


            if ($line < 50) {

                $line += 24;

                $traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'customLongReport', A4_PORTRAIT);
                $traccFooter->prerender($pdf);

                $pdf->end_page_ext('');


                new_page_custom($pdf);

                $pdf->setFontExt($_fonts['Helvetica-Bold'], 9);
                $pdf->showBoxed('EXPENSE ANALYSIS', 100, 690, 400, 20, 'center', '');

                $line = 670;

                $pdf->setColorExt('fill', 'rgb', 0.85, 0.88, 0.95, 0);
                $pdf->rect(25, $line, 545, 15);
                $pdf->fill();

                $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
                $pdf->setFontExt($_fonts['Helvetica-Bold'], 7);
                $pdf->showBoxed('Budget Variances (current month)', 25, $line - 4, 545, 15, 'center', '');

                // $line -= 20;
                $originalLine = $line;

                // $pdf->setlinewidth(0.5);
                // $pdf->moveto(25, $line);
                // $pdf->lineto(570, $line);
                // $pdf->stroke();

                $line -= 24;

                $pdf->setFontExt($_fonts['Helvetica-Bold'], 7);
                $pdf->showBoxed('Description', 27, $line - 9, 103, 24, 'left', '');
                $pdf->showBoxed('Actual ($)', 130, $line - 9, 70, 24, 'right', '');
                $pdf->showBoxed('Budget ($)', 200, $line - 9, 70, 24, 'right', '');
                $pdf->showBoxed('Variance ($)', 270, $line - 9, 70, 24, 'right', '');
                $pdf->showBoxed('Variance (%)', 340, $line - 9, 70, 24, 'right', '');
                $pdf->showBoxed('Comment', 412, $line - 9, 158, 24, 'left', '');

                $line -= 24;
                $parity = 1;

                if ($parity === 1) {
                    $pdf->setColorExt('fill', 'rgb', 0.91, 0.94, 0.97, 1);
                } else {
                    $pdf->setColorExt('fill', 'rgb', 1, 1, 1, 1);
                }

                $line -= 4;
                $pdf->rect(25, $line - 4, 545, 28);
                $pdf->fill();

                $subHeader = trim($row['pmca_gl_account_group2']);
                $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
                $pdf->setFontExt($_fonts['Helvetica-Bold'], 7);
                $pdf->showBoxed($typeTitle[$subHeader], 27, $line - 9, 200, 24, 'left', '');

                $line -= 24;
                $parity *= -1;
            }

            if ($parity == 1) {
                $pdf->setColorExt('fill', 'rgb', 0.91, 0.94, 0.97, 1);
            } else {
                $pdf->setColorExt('fill', 'rgb', 1, 1, 1, 1);
            }

            $line -= 4;
            $pdf->rect(25, $line - 4, 545, 28);
            $pdf->fill();

            $parity *= -1;
            $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);

            $pdf->setFontExt($_fonts['Helvetica'], 7);
            commentLineThreeDisplay($pdf, trim($row['column_2']), 27, $line, 103, 24, 'left', 32);
            $pdf->showBoxed(toMoney($row['column_3'], ''), 130, $line - 9, 68, 24, 'right', '');
            $pdf->showBoxed(toMoney($row['column_4'], ''), 200, $line - 9, 68, 24, 'right', '');
            $pdf->showBoxed(toMoney($row['column_5'], ''), 270, $line - 9, 68, 24, 'right', '');
            $pdf->showBoxed(toMoney($row['column_6'], '') . '', 340, $line - 9, 68, 24, 'right', '');
            commentLineThreeDisplay($pdf, $row['column_0'], 412, $line, 158, 24, 'left', 49);

        }
    }
}


$line -= 30;
// ////////////////////Expenses ‐ Top 5 Budget Variances (YTD)////////////

$pdf->setColorExt('fill', 'rgb', 0.85, 0.88, 0.95, 0);
$pdf->rect(25, $line, 545, 15);
$pdf->fill();

$pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
$pdf->setFontExt($_fonts['Helvetica-Bold'], 7);
$pdf->showBoxed('Budget Variances (YTD)', 25, $line - 4, 545, 15, 'center', '');

// $line -= 20;
$originalLine = $line;


$line -= 24;

$pdf->setFontExt($_fonts['Helvetica-Bold'], 7);
$pdf->showBoxed('Description', 27, $line - 9, 103, 24, 'left', '');
$pdf->showBoxed('Actual ($)', 130, $line - 9, 70, 24, 'right', '');
$pdf->showBoxed('Budget ($)', 200, $line - 9, 70, 24, 'right', '');
$pdf->showBoxed('Variance ($)', 270, $line - 9, 70, 24, 'right', '');
$pdf->showBoxed('Variance (%)', 340, $line - 9, 70, 24, 'right', '');
$pdf->showBoxed('Comment', 412, $line - 9, 158, 24, 'left', '');

$subHeader = '';
$parity = 1;
if (isset($management_data_sequence[4])) {
    foreach ($management_data as $row) {
        if ($row['table_id'] == $management_data_sequence[4]) {

            $line -= 24;

            if ($line < 50) {

                $line += 24;

                $traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'customLongReport', A4_PORTRAIT);
                $traccFooter->prerender($pdf);

                $pdf->end_page_ext('');


                new_page_custom($pdf);

                $pdf->setFontExt($_fonts['Helvetica-Bold'], 9);
                $pdf->showBoxed('EXPENSE ANALYSIS', 100, 690, 400, 20, 'center', '');

                $line = 670;

                $pdf->setColorExt('fill', 'rgb', 0.85, 0.88, 0.95, 0);
                $pdf->rect(25, $line, 545, 15);
                $pdf->fill();

                $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
                $pdf->setFontExt($_fonts['Helvetica-Bold'], 7);
                $pdf->showBoxed('Budget Variances (YTD)', 25, $line - 4, 545, 15, 'center', '');

                // $line -= 20;
                $originalLine = $line;

                // $pdf->setlinewidth(0.5);
                // $pdf->moveto(25, $line);
                // $pdf->lineto(570, $line);
                // $pdf->stroke();

                $line -= 24;

                $pdf->setFontExt($_fonts['Helvetica-Bold'], 7);
                $pdf->showBoxed('Description', 27, $line - 9, 103, 24, 'left', '');
                $pdf->showBoxed('Actual ($)', 130, $line - 9, 70, 24, 'right', '');
                $pdf->showBoxed('Budget ($)', 200, $line - 9, 70, 24, 'right', '');
                $pdf->showBoxed('Variance ($)', 270, $line - 9, 70, 24, 'right', '');
                $pdf->showBoxed('Variance (%)', 340, $line - 9, 70, 24, 'right', '');
                $pdf->showBoxed('Comment', 412, $line - 9, 158, 24, 'left', '');

                $line -= 24;
                $parity = 1;

                if ($parity === 1) {
                    $pdf->setColorExt('fill', 'rgb', 0.91, 0.94, 0.97, 1);
                } else {
                    $pdf->setColorExt('fill', 'rgb', 1, 1, 1, 1);
                }

                $line -= 4;
                $pdf->rect(25, $line - 4, 545, 28);
                $pdf->fill();

                $subHeader = trim($row['pmca_gl_account_group2']);
                $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
                $pdf->setFontExt($_fonts['Helvetica-Bold'], 7);
                $pdf->showBoxed($typeTitle[$subHeader], 27, $line - 9, 200, 24, 'left', '');

                $line -= 24;
                $parity *= -1;
            }

            if ($subHeader !== trim($row['pmca_gl_account_group2'])) {

                if ($parity == 1) {
                    $pdf->setColorExt('fill', 'rgb', 0.91, 0.94, 0.97, 1);
                } else {
                    $pdf->setColorExt('fill', 'rgb', 1, 1, 1, 1);
                }

                $line -= 4;
                $pdf->rect(25, $line - 4, 545, 28);
                $pdf->fill();

                $subHeader = trim($row['pmca_gl_account_group2']);
                $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
                $pdf->setFontExt($_fonts['Helvetica-Bold'], 7);
                $pdf->showBoxed($typeTitle[$subHeader], 27, $line - 9, 200, 24, 'left', '');

                $line -= 24;
                $parity *= -1;
            }


            if ($line < 50) {

                $line += 24;

                $traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'customLongReport', A4_PORTRAIT);
                $traccFooter->prerender($pdf);

                $pdf->end_page_ext('');


                new_page_custom($pdf);

                $pdf->setFontExt($_fonts['Helvetica-Bold'], 9);
                $pdf->showBoxed('EXPENSE ANALYSIS', 100, 690, 400, 20, 'center', '');

                $line = 670;

                $pdf->setColorExt('fill', 'rgb', 0.85, 0.88, 0.95, 0);
                $pdf->rect(25, $line, 545, 15);
                $pdf->fill();

                $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
                $pdf->setFontExt($_fonts['Helvetica-Bold'], 7);
                $pdf->showBoxed('Budget Variances (YTD)', 25, $line - 4, 545, 15, 'center', '');

                // $line -= 20;
                $originalLine = $line;

                // $pdf->setlinewidth(0.5);
                // $pdf->moveto(25, $line);
                // $pdf->lineto(570, $line);
                // $pdf->stroke();

                $line -= 24;

                $pdf->setFontExt($_fonts['Helvetica-Bold'], 7);
                $pdf->showBoxed('Description', 27, $line - 9, 103, 24, 'left', '');
                $pdf->showBoxed('Actual ($)', 130, $line - 9, 70, 24, 'right', '');
                $pdf->showBoxed('Budget ($)', 200, $line - 9, 70, 24, 'right', '');
                $pdf->showBoxed('Variance ($)', 270, $line - 9, 70, 24, 'right', '');
                $pdf->showBoxed('Variance (%)', 340, $line - 9, 70, 24, 'right', '');
                $pdf->showBoxed('Comment', 412, $line - 9, 158, 24, 'left', '');

                $line -= 24;
                $parity = 1;

                if ($parity === 1) {
                    $pdf->setColorExt('fill', 'rgb', 0.91, 0.94, 0.97, 1);
                } else {
                    $pdf->setColorExt('fill', 'rgb', 1, 1, 1, 1);
                }

                $line -= 4;
                $pdf->rect(25, $line - 4, 545, 28);
                $pdf->fill();

                $subHeader = trim($row['pmca_gl_account_group2']);
                $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
                $pdf->setFontExt($_fonts['Helvetica-Bold'], 7);
                $pdf->showBoxed($typeTitle[$subHeader], 27, $line - 9, 200, 24, 'left', '');

                $line -= 24;
                $parity *= -1;
            }

            if ($parity == 1) {
                $pdf->setColorExt('fill', 'rgb', 0.91, 0.94, 0.97, 1);
            } else {
                $pdf->setColorExt('fill', 'rgb', 1, 1, 1, 1);
            }

            $line -= 4;
            $pdf->rect(25, $line - 4, 545, 28);
            $pdf->fill();

            $parity *= -1;
            $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);

            $pdf->setFontExt($_fonts['Helvetica'], 7);
            commentLineThreeDisplay($pdf, trim($row['column_2']), 27, $line, 103, 24, 'left', 32);
            $pdf->showBoxed(toMoney($row['column_3'], ''), 130, $line - 9, 68, 24, 'right', '');
            $pdf->showBoxed(toMoney($row['column_4'], ''), 200, $line - 9, 68, 24, 'right', '');
            $pdf->showBoxed(toMoney($row['column_5'], ''), 270, $line - 9, 68, 24, 'right', '');
            $pdf->showBoxed(toMoney($row['column_6'], '') . '', 340, $line - 9, 68, 24, 'right', '');
            commentLineThreeDisplay($pdf, $row['column_0'], 412, $line, 158, 24, 'left', 49);

            // $pdf->setlinewidth(0.5);
            // $pdf->moveto(25, $line);
            // $pdf->lineto(570, $line);
            // $pdf->stroke();

        }
    }
}




$line -= 25;

// $pdf->setColorExt("both", "rgb", 0, 0, 0, 0);
// $pdf->setFontExt ($_fonts['Helvetica'], 7 );
// $pdf->showBoxed ($management_comments[2]['comment'], 25, $line, 545, 7 , "left", "");

if (trim($management_comments[2]['comment']) !== '' && trim($management_comments[2]['comment']) !== '0') {
    $generalComment = explode("\n", $management_comments[2]['comment']);
    foreach ($generalComment as $key => $com) {

        if ($line < 50) {
            $pdf->setFontExt($_fonts['Helvetica'], 8);
            $pdf->showBoxed("Page {$page}", 479, 7, 75, 30, 'right', '');
            $traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'customLongReport', A4_PORTRAIT);
            $traccFooter->footerAlignment = 'left';
            $traccFooter->prerender($pdf);
            $page++;

            $pdf->end_page_ext('');

            new_page_custom($pdf);

            $pdf->setFontExt($_fonts['Helvetica-Bold'], 9);
            $pdf->showBoxed('EXPENSE ANALYSIS', 100, 690, 400, 20, 'center', '');

            $line = 670;
        }


        $comment['comment'] = $com;
        $comment['bold'] = $management_comments[2]['bold'];
        $comment['size'] = $management_comments[2]['size'];
        $comment['bullet'] = $management_comments[2]['bullet'];
        $comment['listed'] = $management_comments[2]['listed'];

        $commentCount = ceil(strlen($com) / 115);
        $height = ($comment['size'] * ($commentCount + 1));
        management_report($pdf, $_fonts, $comment, 27, $line - $height, 540, $height, 'left', $key);
        $line -= $height;
    }
}

$line -= 30;

$traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'customLongReport', A4_PORTRAIT);
$traccFooter->prerender($pdf);

$pdf->end_page_ext('');

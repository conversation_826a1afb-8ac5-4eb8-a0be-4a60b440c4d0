<?php

use PhpOffice\PhpPresentation\Style\Color;
use PhpOffice\PhpPresentation\Style\Fill;

$monthYear = date('M-y', strtotime(convertDate($periodFrom)));
$monthYear2 = date('F Y', strtotime(convertDate($periodFrom)));
// SET ALL THE VARIABLES HERE
$cal = dbGetCalendarForProperty($propertyID);
$calendarName = $cal['calendarName'];
$calendar = dbGetMasterCalendarPeriodForDate($calendarName, $periodTo);
$calendarYear = dbGetCalendarPeriods($propertyID, $calendar['year']);

function insertLeaseEquityTemplate(&$currentSlide, $text)
{
    $shape = $currentSlide->createRichTextShape()->setHeight(80)->setWidth(940)->setOffsetX(10)->setOffsetY(10);
    $shape->getFill()->setFillType(Fill::FILL_SOLID)->setRotation(90)->setStartColor(new Color('E9E8E8'))->setEndColor(
        new Color('E9E8E8')
    );

    $shape = $currentSlide->createRichTextShape()->setHeight(610)->setWidth(940)->setOffsetX(10)->setOffsetY(100);
    $shape->getFill()->setFillType(Fill::FILL_SOLID)->setRotation(90)->setStartColor(new Color('E9E8E8'))->setEndColor(
        new Color('E9E8E8')
    );

    $shape = $currentSlide->createDrawingShape();
    $shape->setPath('assets/images/lq.jpg')
        ->setHeight(55)
        ->setOffsetX(750)
        ->setOffsetY(25);

    // Create a shape (text)
    $shape = $currentSlide->createRichTextShape()
        ->setHeight(55)
        ->setWidth(600)
        ->setOffsetX(40)
        ->setOffsetY(30);
    $shape->getActiveParagraph()->getAlignment();
    $textRun = $shape->createTextRun($text);
    $textRun->getFont()->setBold(false)->setSize(17)->setName('Panton-Bold')->setColor(new Color('FF000000'));
}

function renderLogoPage(&$pdf)
{
    $file = realpath('assets/images/lq.jpg');
    $pdfimage = $pdf->load_image('auto', $file, '');
    $maxWidth = 200;
    $maxHeight = 40;
    $pdf->fit_image($pdfimage, 650, 520, 'boxsize {' . "{$maxWidth} {$maxHeight}" . '} position={center} fitmethod=meet');
}

function renderNewVacancyReportPage(&$pdf, $line, $contentPageNo)
{
    $_fonts['Panton-Light'] = $pdf->load_font('Panton-Light', 'host', '');
    $_fonts['Panton-Bold'] = $pdf->load_font('Panton-SemiBold', 'host', '');

    $pdf->setColorExt('stroke', 'rgb', 1, 1, 1, 1);
    // vertical lines
    $pdf->setlinewidth(0.5);
    $pdf->moveto(817, 460);
    $pdf->lineto(817, $line);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(25, 460);
    $pdf->lineto(25, $line);
    $pdf->stroke();

    // middle lines
    $pdf->setlinewidth(0.5);
    $pdf->moveto(80, 460);
    $pdf->lineto(80, $line);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(170, 460);
    $pdf->lineto(170, $line);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(270, 460);
    $pdf->lineto(270, $line);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(360, 460);
    $pdf->lineto(360, $line);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(450, 460);
    $pdf->lineto(450, $line);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(540, 460);
    $pdf->lineto(540, $line);
    $pdf->stroke();

    // $traccFooter = new TraccFooter("assets/clientLogos/tracc_logo_footer.jpg", 'simpleOwnersReport', A4_LANDSCAPE);
    // $traccFooter->prerender($pdf);
    $pdf->end_page_ext('');

    // New Page

    $pdf->begin_page_ext(842, 595, '');
    $pdf->setColorExt('fill', 'rgb', 0.91, 0.91, 0.91, 0);
    $pdf->rect(20, 505, 802, 70);
    $pdf->fill();

    $pdf->setColorExt('fill', 'rgb', 0.91, 0.91, 0.91, 0);
    $pdf->rect(20, 20, 802, 475);
    $pdf->fill();

    renderLogoPage($pdf);

    $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
    $pdf->setFontExt($_fonts['Panton-Bold'], 18);
    $pdf->showBoxed($contentPageNo . ' VACANCY REPORT', 50, 535, 400, 18, 'left', '');


    $pdf->setColorExt('fill', 'rgb', 0.22, 0.59, 0.86, 1);
    $pdf->rect(25, 430, 792, 30);
    $pdf->fill();

    $pdf->setColorExt('fill', 'rgb', 1, 1, 1, 1);
    $pdf->rect(25, 427, 792, 3);
    $pdf->fill();


    // above lines
    $pdf->setColorExt('stroke', 'rgb', 1, 1, 1, 0);
    $pdf->setlinewidth(0.5);
    $pdf->moveto(25, 460);
    $pdf->lineto(817, 460);
    $pdf->stroke();

    $pdf->setFontExt($_fonts['Panton-Bold'], 12);
    $pdf->setColorExt('both', 'rgb', 1, 1, 1, 0);
    $pdf->showBoxed('Shop No.', 25, 437, 55, 12, 'center', '');
    $pdf->showBoxed('Area', 80, 437, 90, 12, 'center', '');
    $pdf->showBoxed('Previous Tenant', 170, 437, 100, 12, 'center', '');
    $pdf->showBoxed('Vacant Since', 270, 437, 90, 12, 'center', '');
    $pdf->showBoxed('Forecast Lease Date', 360, 435, 90, 24, 'center', '');
    $pdf->showBoxed('Budget Rent', 450, 437, 100, 12, 'center', '');
    $pdf->showBoxed('Comment', 540, 437, 90, 12, 'right', '');
}

function renderNewSalesReportPage(&$pdf, $line, $contentPageNo)
{
    $_fonts['Panton-Light'] = $pdf->load_font('Panton-Light', 'host', '');
    $_fonts['Panton-Bold'] = $pdf->load_font('Panton-SemiBold', 'host', '');

    $pdf->setColorExt('stroke', 'rgb', 1, 1, 1, 1);

    // ENDING LINES
    $pdf->setlinewidth(0.5);
    $pdf->moveto(817, 430);
    $pdf->lineto(817, $line);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(25, 430);
    $pdf->lineto(25, $line);
    $pdf->stroke();

    // middle vertical lines

    $pdf->setlinewidth(0.5);
    $pdf->moveto(170, 430);
    $pdf->lineto(170, $line);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(240, 430);
    $pdf->lineto(240, $line);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(320, 430);
    $pdf->lineto(320, $line);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(400, 430);
    $pdf->lineto(400, $line);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(480, 430);
    $pdf->lineto(480, $line);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(560, 430);
    $pdf->lineto(560, $line);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(640, 430);
    $pdf->lineto(640, $line);
    $pdf->stroke();

    // $traccFooter = new TraccFooter("assets/clientLogos/tracc_logo_footer.jpg", 'simpleOwnersReport', A4_LANDSCAPE);
    // $traccFooter->prerender($pdf);
    $pdf->end_page_ext('');

    // New Page

    $pdf->begin_page_ext(842, 595, '');
    $pdf->setColorExt('fill', 'rgb', 0.91, 0.91, 0.91, 0);
    $pdf->rect(20, 505, 802, 70);
    $pdf->fill();

    $pdf->setColorExt('fill', 'rgb', 0.91, 0.91, 0.91, 0);
    $pdf->rect(20, 20, 802, 475);
    $pdf->fill();

    renderLogoPage($pdf);

    $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
    $pdf->setFontExt($_fonts['Panton-Bold'], 18);
    $pdf->showBoxed(floor($contentPageNo) . '.0 CENTRE PERFORMANCE', 50, 535, 400, 18, 'left', '');


    $pdf->setColorExt('fill', 'rgb', 0.22, 0.59, 0.86, 1);
    $pdf->rect(25, 400, 792, 30);
    $pdf->fill();

    // above lines
    $pdf->setColorExt('stroke', 'rgb', 1, 1, 1, 0);
    $pdf->setlinewidth(0.5);
    $pdf->moveto(25, 400);
    $pdf->lineto(817, 400);
    $pdf->stroke();

    $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
    $pdf->setFontExt($_fonts['Panton-Bold'], 16);
    $pdf->showBoxed($contentPageNo . ' Sales and MAT Summary', 25, 437, 400, 18, 'left', '');

    $date = date('Y-m-1', strtotime(convertDate($periodTo)));
    $prevDate = date('Y-m-d', strtotime($date . '-1 day'));

    // DATA START
    $pdf->setFontExt($_fonts['Panton-Bold'], 12);
    $pdf->setColorExt('both', 'rgb', 1, 1, 1, 0);
    $pdf->showBoxed('Shop No.', 25, 407, 145, 12, 'center', '');
    $pdf->showBoxed(date('M-y', strtotime($prevDate)) . "\n(000’s)", 170, 407 - 4, 70, 24, 'center', '');
    $pdf->showBoxed(date('M-y', strtotime($prevDate . '-1 year')) . "\n(000’s)", 240, 407 - 4, 80, 24, 'center', '');
    $pdf->showBoxed('% Var', 320, 407, 80, 12, 'center', '');
    $pdf->showBoxed('MAT ' . date('Y', strtotime($prevDate)) . "\n(000’s)", 400, 407 - 4, 80, 24, 'center', '');
    $pdf->showBoxed(
        'MAT ' . date('Y', strtotime($prevDate . '-1 year')) . "\n(000’s)",
        480,
        407 - 4,
        80,
        24,
        'center',
        ''
    );
    $pdf->showBoxed('% Var ', 560, 407, 80, 12, 'center', '');
    $pdf->showBoxed('Comments', 642, 407, 175, 12, 'left', '');

    $pdf->setColorExt('fill', 'rgb', 1, 1, 1, 1);
    $pdf->rect(25, 397, 792, 3);
    $pdf->fill();

    $pdf->setFontExt($_fonts['Panton-Light'], 11);
}

function renderNewFirstBankGuaranteeReportPage(&$pdf, $line, $contentPageNo)
{
    $_fonts['Panton-Light'] = $pdf->load_font('Panton-Light', 'host', '');
    $_fonts['Panton-Bold'] = $pdf->load_font('Panton-SemiBold', 'host', '');

    $pdf->setColorExt('stroke', 'rgb', 1, 1, 1, 1);
    // vertical lines
    $pdf->setlinewidth(0.5);
    $pdf->moveto(817, 460);
    $pdf->lineto(817, $line);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(25, 460);
    $pdf->lineto(25, $line);
    $pdf->stroke();

    // middle lines

    // $pdf->setlinewidth(0.5);
    // $pdf->moveto(170, 460);
    // $pdf->lineto(170, $line);
    // $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(270, 460);
    $pdf->lineto(270, $line);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(360, 460);
    $pdf->lineto(360, $line);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(450, 460);
    $pdf->lineto(450, $line);
    $pdf->stroke();

    // $traccFooter = new TraccFooter("assets/clientLogos/tracc_logo_footer.jpg", 'simpleOwnersReport', A4_LANDSCAPE);
    // $traccFooter->prerender($pdf);
    $pdf->end_page_ext('');

    // New Page

    $pdf->begin_page_ext(842, 595, '');
    $pdf->setColorExt('fill', 'rgb', 0.91, 0.91, 0.91, 0);
    $pdf->rect(20, 505, 802, 70);
    $pdf->fill();

    $pdf->setColorExt('fill', 'rgb', 0.91, 0.91, 0.91, 0);
    $pdf->rect(20, 20, 802, 475);
    $pdf->fill();

    renderLogoPage($pdf);

    $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
    $pdf->setFontExt($_fonts['Panton-Bold'], 18);
    $pdf->showBoxed($contentPageNo . ' BANK GUARANTEE AND SECURITY DEPOSITS', 50, 535, 400, 18, 'left', '');


    $pdf->setColorExt('fill', 'rgb', 0.22, 0.59, 0.86, 1);
    $pdf->rect(25, 430, 792, 30);
    $pdf->fill();

    $pdf->setColorExt('fill', 'rgb', 1, 1, 1, 1);
    $pdf->rect(25, 427, 792, 3);
    $pdf->fill();


    // above lines
    $pdf->setColorExt('stroke', 'rgb', 1, 1, 1, 0);
    $pdf->setlinewidth(0.5);
    $pdf->moveto(25, 460);
    $pdf->lineto(817, 460);
    $pdf->stroke();

    $pdf->setFontExt($_fonts['Panton-Bold'], 12);
    $pdf->setColorExt('both', 'rgb', 1, 1, 1, 0);
    $pdf->showBoxed('Shop #/Tenant', 25, 437, 145, 12, 'center', '');
    // $pdf->showBoxed ('Amount due', 170, 437, 100 ,12,"center", "");
    $pdf->showBoxed('Amount held', 270, 437, 90, 12, 'center', '');
    $pdf->showBoxed('Expiry Date', 360, 437, 90, 12, 'center', '');
    $pdf->showBoxed('Comment', 452, 437, 362, 12, 'left', '');
}

function renderNewBankGuaranteeReportPage(&$pdf, $line, $startLine, $contentPageNo)
{
    $_fonts['Panton-Light'] = $pdf->load_font('Panton-Light', 'host', '');
    $_fonts['Panton-Bold'] = $pdf->load_font('Panton-SemiBold', 'host', '');

    $pdf->setColorExt('stroke', 'rgb', 1, 1, 1, 1);
    // vertical lines
    $pdf->setlinewidth(0.5);
    $pdf->moveto(817, $startLine);
    $pdf->lineto(817, $line);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(25, $startLine);
    $pdf->lineto(25, $line);
    $pdf->stroke();

    // middle lines

    // $pdf->setlinewidth(0.5);
    // $pdf->moveto(170, $startLine);
    // $pdf->lineto(170, $line);
    // $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(270, $startLine);
    $pdf->lineto(270, $line);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(360, $startLine);
    $pdf->lineto(360, $line);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(450, $startLine);
    $pdf->lineto(450, $line);
    $pdf->stroke();


    // $traccFooter = new TraccFooter("assets/clientLogos/tracc_logo_footer.jpg", 'simpleOwnersReport', A4_LANDSCAPE);
    // $traccFooter->prerender($pdf);
    $pdf->end_page_ext('');

    // New Page

    $pdf->begin_page_ext(842, 595, '');
    $pdf->setColorExt('fill', 'rgb', 0.91, 0.91, 0.91, 0);
    $pdf->rect(20, 505, 802, 70);
    $pdf->fill();

    $pdf->setColorExt('fill', 'rgb', 0.91, 0.91, 0.91, 0);
    $pdf->rect(20, 20, 802, 475);
    $pdf->fill();

    renderLogoPage($pdf);

    $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
    $pdf->setFontExt($_fonts['Panton-Bold'], 18);
    $pdf->showBoxed($contentPageNo . ' BANK GUARANTEE AND SECURITY DEPOSITS', 50, 535, 400, 18, 'left', '');


    $pdf->setColorExt('fill', 'rgb', 0.22, 0.59, 0.86, 1);
    $pdf->rect(25, 430, 792, 30);
    $pdf->fill();

    $pdf->setColorExt('fill', 'rgb', 1, 1, 1, 1);
    $pdf->rect(25, 427, 792, 3);
    $pdf->fill();


    // above lines
    $pdf->setColorExt('stroke', 'rgb', 1, 1, 1, 0);
    $pdf->setlinewidth(0.5);
    $pdf->moveto(25, 460);
    $pdf->lineto(817, 460);
    $pdf->stroke();

    $pdf->setFontExt($_fonts['Panton-Bold'], 12);
    $pdf->setColorExt('both', 'rgb', 1, 1, 1, 0);
    $pdf->showBoxed('Shop #/Tenant', 25, 437, 145, 12, 'center', '');
    $pdf->showBoxed('Amount due', 270, 437, 100, 12, 'center', '');
    $pdf->showBoxed('Amount held', 360, 437, 90, 12, 'center', '');
    //   $pdf->showBoxed ('Expiry Date', 360, 437, 90 ,12,"center", "");
    $pdf->showBoxed('Comment', 452, 437, 362, 12, 'left', '');
}

function renderNewInsuranceReportPage(&$pdf, $line, $contentPageNo)
{
    $_fonts['Panton-Light'] = $pdf->load_font('Panton-Light', 'host', '');
    $_fonts['Panton-Bold'] = $pdf->load_font('Panton-SemiBold', 'host', '');

    $origLines = 415;

    $pdf->setColorExt('stroke', 'rgb', 1, 1, 1, 1);
    // vertical lines
    $pdf->setlinewidth(0.5);
    $pdf->moveto(817, $origLines + 23);
    $pdf->lineto(817, $line);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(25, $origLines + 23);
    $pdf->lineto(25, $line);
    $pdf->stroke();

    // middle lines
    $pdf->setlinewidth(0.5);
    $pdf->moveto(170, $origLines + 23);
    $pdf->lineto(170, $line);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(400, $origLines + 23);
    $pdf->lineto(400, $line);
    $pdf->stroke();


    $pdf->setlinewidth(0.5);
    $pdf->moveto(560, $origLines + 23);
    $pdf->lineto(560, $line);
    $pdf->stroke();


    // $traccFooter = new TraccFooter("assets/clientLogos/tracc_logo_footer.jpg", 'simpleOwnersReport', A4_LANDSCAPE);
    // $traccFooter->prerender($pdf);
    $pdf->end_page_ext('');

    // New Page

    $pdf->begin_page_ext(842, 595, '');
    $pdf->setColorExt('fill', 'rgb', 0.91, 0.91, 0.91, 0);
    $pdf->rect(20, 505, 802, 70);
    $pdf->fill();

    $pdf->setColorExt('fill', 'rgb', 0.91, 0.91, 0.91, 0);
    $pdf->rect(20, 20, 802, 475);
    $pdf->fill();

    renderLogoPage($pdf);

    $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
    $pdf->setFontExt($_fonts['Panton-Bold'], 18);
    $pdf->showBoxed(floor($contentPageNo) . '.0 OPERATIONS AND RISK', 50, 535, 400, 18, 'left', '');


    $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
    $pdf->setFontExt($_fonts['Panton-Bold'], 18);
    $pdf->showBoxed($contentPageNo . ' Public Liability Insurance Claims', 50, 455, 400, 18, 'left', '');

    $line = $origLines;

    $pdf->setColorExt('fill', 'rgb', 0.22, 0.59, 0.86, 1);
    $pdf->rect(25, $line - 7, 792, 30);
    $pdf->fill();

    $pdf->setColorExt('fill', 'rgb', 1, 1, 1, 1);
    $pdf->rect(25, $line - 10, 792, 3);
    $pdf->fill();


    // above lines
    $pdf->setColorExt('stroke', 'rgb', 1, 1, 1, 0);
    $pdf->setlinewidth(0.5);
    $pdf->moveto(25, $line + 23);
    $pdf->lineto(817, $line + 23);
    $pdf->stroke();


    // DATA START
    $pdf->setFontExt($_fonts['Panton-Bold'], 12);
    $pdf->setColorExt('both', 'rgb', 1, 1, 1, 0);
    $pdf->showBoxed('Incident Claimant', 27, $line, 145, 12, 'left', '');
    $pdf->showBoxed('Particulars', 172, $line, 228, 12, 'left', '');
    $pdf->showBoxed('Amount Claimed', 402, $line, 158, 12, 'left', '');
    $pdf->showBoxed('Status', 562, $line, 253, 12, 'left', '');
}

function renderNewIncidentReportPage(&$pdf, $line, $origLines2, $contentPageNo)
{
    $_fonts['Panton-Light'] = $pdf->load_font('Panton-Light', 'host', '');
    $_fonts['Panton-Bold'] = $pdf->load_font('Panton-SemiBold', 'host', '');

    $origLines = 415;

    $pdf->setColorExt('stroke', 'rgb', 1, 1, 1, 1);
    // vertical lines
    $pdf->setlinewidth(0.5);
    $pdf->moveto(817, $origLines2 + 23);
    $pdf->lineto(817, $line);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(25, $origLines2 + 23);
    $pdf->lineto(25, $line);
    $pdf->stroke();

    // middle lines
    $pdf->setlinewidth(0.5);
    $pdf->moveto(170, $origLines2 + 23);
    $pdf->lineto(170, $line);
    $pdf->stroke();


    // $traccFooter = new TraccFooter("assets/clientLogos/tracc_logo_footer.jpg", 'simpleOwnersReport', A4_LANDSCAPE);
    // $traccFooter->prerender($pdf);
    $pdf->end_page_ext('');

    // New Page

    $pdf->begin_page_ext(842, 595, '');
    $pdf->setColorExt('fill', 'rgb', 0.91, 0.91, 0.91, 0);
    $pdf->rect(20, 505, 802, 70);
    $pdf->fill();

    $pdf->setColorExt('fill', 'rgb', 0.91, 0.91, 0.91, 0);
    $pdf->rect(20, 20, 802, 475);
    $pdf->fill();

    renderLogoPage($pdf);

    $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
    $pdf->setFontExt($_fonts['Panton-Bold'], 18);
    $pdf->showBoxed(floor($contentPageNo) . '.0 OPERATIONS AND RISK', 50, 535, 400, 18, 'left', '');


    $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
    $pdf->setFontExt($_fonts['Panton-Bold'], 18);
    $pdf->showBoxed(((float) $contentPageNo + 0.1) . ' Security Incidents this Month', 50, 455, 400, 18, 'left', '');

    $line = $origLines;

    $pdf->setColorExt('fill', 'rgb', 0.22, 0.59, 0.86, 1);
    $pdf->rect(25, $line - 7, 792, 30);
    $pdf->fill();

    $pdf->setColorExt('fill', 'rgb', 1, 1, 1, 1);
    $pdf->rect(25, $line - 10, 792, 3);
    $pdf->fill();


    // above lines
    $pdf->setColorExt('stroke', 'rgb', 1, 1, 1, 0);
    $pdf->setlinewidth(0.5);
    $pdf->moveto(25, $line + 23);
    $pdf->lineto(817, $line + 23);
    $pdf->stroke();


    // DATA START
    $pdf->setFontExt($_fonts['Panton-Bold'], 12);
    $pdf->setColorExt('both', 'rgb', 1, 1, 1, 0);
    $pdf->showBoxed('Date', 27, $line, 145, 12, 'left', '');
    $pdf->showBoxed('Particulars', 172, $line, 639, 12, 'left', '');
}

function renderNewVirtualVacancyReportPage(&$pdf, $line, $contentPageNo)
{
    $_fonts['Panton-Light'] = $pdf->load_font('Panton-Light', 'host', '');
    $_fonts['Panton-Bold'] = $pdf->load_font('Panton-SemiBold', 'host', '');

    $pdf->setColorExt('stroke', 'rgb', 1, 1, 1, 1);
    // vertical lines
    $pdf->setlinewidth(0.5);
    $pdf->moveto(817, 460);
    $pdf->lineto(817, $line);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(25, 460);
    $pdf->lineto(25, $line);
    $pdf->stroke();

    // middle lines
    $pdf->setlinewidth(0.5);
    $pdf->moveto(80, 460);
    $pdf->lineto(80, $line);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(170, 460);
    $pdf->lineto(170, $line);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(240, 460);
    $pdf->lineto(240, $line);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(320, 460);
    $pdf->lineto(320, $line);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(400, 460);
    $pdf->lineto(400, $line);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(480, 460);
    $pdf->lineto(480, $line);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(560, 460);
    $pdf->lineto(560, $line);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(620, 460);
    $pdf->lineto(620, $line);
    $pdf->stroke();

    // $traccFooter = new TraccFooter("assets/clientLogos/tracc_logo_footer.jpg", 'simpleOwnersReport', A4_LANDSCAPE);
    // $traccFooter->prerender($pdf);
    $pdf->end_page_ext('');

    // New Page

    $pdf->begin_page_ext(842, 595, '');
    $pdf->setColorExt('fill', 'rgb', 0.91, 0.91, 0.91, 0);
    $pdf->rect(20, 505, 802, 70);
    $pdf->fill();

    $pdf->setColorExt('fill', 'rgb', 0.91, 0.91, 0.91, 0);
    $pdf->rect(20, 20, 802, 475);
    $pdf->fill();

    renderLogoPage($pdf);

    $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
    $pdf->setFontExt($_fonts['Panton-Bold'], 18);
    $pdf->showBoxed($contentPageNo . ' VIRTUAL VACANCIES', 50, 535, 400, 18, 'left', '');


    $pdf->setColorExt('fill', 'rgb', 0.22, 0.59, 0.86, 1);
    $pdf->rect(25, 430, 792, 30);
    $pdf->fill();

    $pdf->setColorExt('fill', 'rgb', 1, 1, 1, 1);
    $pdf->rect(25, 427, 792, 3);
    $pdf->fill();


    // above lines
    $pdf->setColorExt('stroke', 'rgb', 1, 1, 1, 0);
    $pdf->setlinewidth(0.5);
    $pdf->moveto(25, 460);
    $pdf->lineto(817, 460);
    $pdf->stroke();

    $pdf->setFontExt($_fonts['Panton-Bold'], 12);
    $pdf->setColorExt('both', 'rgb', 1, 1, 1, 0);
    $pdf->showBoxed('Shop No.', 25, 437, 55, 12, 'center', '');
    $pdf->showBoxed('Tenant Name', 80, 437, 90, 12, 'center', '');
    $pdf->showBoxed('Expiry Date', 170, 437, 70, 12, 'center', '');
    $pdf->showBoxed('Total Arrears', 240, 437, 80, 12, 'center', '');
    $pdf->showBoxed('Bank Guarantee', 320, 435, 80, 24, 'center', '');
    $pdf->showBoxed('MAT Sales', 400, 437, 80, 12, 'center', '');
    $pdf->showBoxed('Gross Rent PA', 480, 437, 80, 12, 'center', '');
    $pdf->showBoxed('Occ Cost %', 560, 435, 60, 24, 'center', '');
    $pdf->showBoxed('Comments/Action', 622, 437, 195, 12, 'left', '');
}

function renderNewDetailedArrearsReportPage(&$pdf, $line, $contentPageNo)
{
    $_fonts['Panton-Light'] = $pdf->load_font('Panton-Light', 'host', '');
    $_fonts['Panton-Bold'] = $pdf->load_font('Panton-SemiBold', 'host', '');

    $pdf->setColorExt('stroke', 'rgb', 1, 1, 1, 1);
    // vertical lines
    $pdf->setlinewidth(0.5);
    $pdf->moveto(817, 460);
    $pdf->lineto(817, $line);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(25, 460);
    $pdf->lineto(25, $line);
    $pdf->stroke();

    // middle lines


    $pdf->setlinewidth(0.5);
    $pdf->moveto(170, 460);
    $pdf->lineto(170, $line);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(250, 460);
    $pdf->lineto(250, $line);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(330, 460);
    $pdf->lineto(330, $line);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(410, 460);
    $pdf->lineto(410, $line);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(490, 460);
    $pdf->lineto(490, $line);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(570, 460);
    $pdf->lineto(570, $line);
    $pdf->stroke();

    // $pdf->setlinewidth(0.5);
    // $pdf->moveto(650, 460);
    // $pdf->lineto(650, $line);
    // $pdf->stroke();

    // $traccFooter = new TraccFooter("assets/clientLogos/tracc_logo_footer.jpg", 'simpleOwnersReport', A4_LANDSCAPE);
    // $traccFooter->prerender($pdf);
    $pdf->end_page_ext('');

    // New Page

    $pdf->begin_page_ext(842, 595, '');
    $pdf->setColorExt('fill', 'rgb', 0.91, 0.91, 0.91, 0);
    $pdf->rect(20, 505, 802, 70);
    $pdf->fill();

    $pdf->setColorExt('fill', 'rgb', 0.91, 0.91, 0.91, 0);
    $pdf->rect(20, 20, 802, 475);
    $pdf->fill();

    renderLogoPage($pdf);

    $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
    $pdf->setFontExt($_fonts['Panton-Bold'], 18);
    $pdf->showBoxed($contentPageNo . ' DETAILED ARREARS REPORT', 50, 535, 400, 18, 'left', '');


    $pdf->setColorExt('fill', 'rgb', 0.22, 0.59, 0.86, 1);
    $pdf->rect(25, 430, 792, 30);
    $pdf->fill();

    $pdf->setColorExt('fill', 'rgb', 1, 1, 1, 1);
    $pdf->rect(25, 427, 792, 3);
    $pdf->fill();


    // above lines
    $pdf->setColorExt('stroke', 'rgb', 1, 1, 1, 0);
    $pdf->setlinewidth(0.5);
    $pdf->moveto(25, 460);
    $pdf->lineto(817, 460);
    $pdf->stroke();

    $pdf->setFontExt($_fonts['Panton-Bold'], 12);
    $pdf->setColorExt('both', 'rgb', 1, 1, 1, 0);
    $pdf->showBoxed('Tenant', 25, 437, 145, 12, 'center', '');
    $pdf->showBoxed('Current', 170, 437, 80, 12, 'center', '');
    $pdf->showBoxed('30 days', 250, 437, 80, 12, 'center', '');
    $pdf->showBoxed('60 days', 330, 437, 80, 12, 'center', '');
    $pdf->showBoxed('90+ days', 410, 437, 80, 12, 'center', '');
    $pdf->showBoxed('Total Arrears', 490, 437, 80, 12, 'center', '');
    // $pdf->showBoxed ('Since Paid', 570, 437, 80 ,12,"center", "");
    $pdf->showBoxed('Comments/Action', 572, 437, 250, 12, 'left', '');
}

function renderNewCommentaryPage(&$pdf, $line, $startLine, $header = 1)
{
    $_fonts['Panton-Light'] = $pdf->load_font('Panton-Light', 'host', '');
    $_fonts['Panton-Bold'] = $pdf->load_font('Panton-SemiBold', 'host', '');

    $pdf->setColorExt('stroke', 'rgb', 1, 1, 1, 1);
    // vertical lines
    $pdf->setlinewidth(0.5);
    $pdf->moveto(817, $startLine);
    $pdf->lineto(817, $line);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(25, $startLine);
    $pdf->lineto(25, $line);
    $pdf->stroke();

    // middle lines

    $pdf->setlinewidth(0.5);
    $pdf->moveto(170, $startLine);
    $pdf->lineto(170, $line);
    $pdf->stroke();


    $pdf->setlinewidth(0.5);
    $pdf->moveto(320, $startLine);
    $pdf->lineto(320, $line);
    $pdf->stroke();

    // $traccFooter = new TraccFooter("assets/clientLogos/tracc_logo_footer.jpg", 'simpleOwnersReport', A4_LANDSCAPE);
    // $traccFooter->prerender($pdf);
    $pdf->end_page_ext('');

    // New Page

    $pdf->begin_page_ext(842, 595, '');
    $pdf->setColorExt('fill', 'rgb', 0.91, 0.91, 0.91, 0);
    $pdf->rect(20, 505, 802, 70);
    $pdf->fill();

    $pdf->setColorExt('fill', 'rgb', 0.91, 0.91, 0.91, 0);
    $pdf->rect(20, 20, 802, 475);
    $pdf->fill();

    renderLogoPage($pdf);

    $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
    $pdf->setFontExt($_fonts['Panton-Bold'], 18);
    $pdf->showBoxed('FINANCIAL PERFORMANCE', 50, 535, 400, 18, 'left', '');


    $line = 437;
    $startLine = $line - 7;

    $pdf->setColorExt('fill', 'rgb', 0.22, 0.59, 0.86, 1);
    $pdf->rect(25, $line - 7, 792, 30);
    $pdf->fill();

    $pdf->setColorExt('fill', 'rgb', 1, 1, 1, 1);
    $pdf->rect(25, $line - 10, 792, 3);
    $pdf->fill();


    // above lines
    $pdf->setColorExt('stroke', 'rgb', 1, 1, 1, 0);
    $pdf->setlinewidth(0.5);
    $pdf->moveto(25, $startLine);
    $pdf->lineto(817, $startLine);
    $pdf->stroke();

    // DATA START
    switch ($header) {
        case '2':
            $headerText = 'DIRECT RECOVERIES';
            break;
        case '3':
            $headerText = 'EXPENSES';
            break;
        default:
            $headerText = 'INCOME';
            break;
    }

    $pdf->setFontExt($_fonts['Panton-Bold'], 12);
    $pdf->setColorExt('both', 'rgb', 1, 1, 1, 0);
    $pdf->showBoxed($headerText, 25, $line, 686, 12, 'center', '');
    $line -= 10;
}

function renderNewRentReviewAchievedPage(&$pdf, $line, $contentPageNo)
{
    $_fonts['Panton-Light'] = $pdf->load_font('Panton-Light', 'host', '');
    $_fonts['Panton-Bold'] = $pdf->load_font('Panton-SemiBold', 'host', '');

    $pdf->setColorExt('stroke', 'rgb', 1, 1, 1, 1);
    // vertical lines
    $pdf->setlinewidth(0.5);
    $pdf->moveto(817, 400);
    $pdf->lineto(817, $line);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(25, 400);
    $pdf->lineto(25, $line);
    $pdf->stroke();

    // middle vertical lines
    $pdf->setlinewidth(0.5);
    $pdf->moveto(80, 400);
    $pdf->lineto(80, $line);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(170, 400);
    $pdf->lineto(170, $line);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(240, 400);
    $pdf->lineto(240, $line);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(320, 400);
    $pdf->lineto(320, $line);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(400, 400);
    $pdf->lineto(400, $line);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(480, 400);
    $pdf->lineto(480, $line);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(560, 400);
    $pdf->lineto(560, $line);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(640, 400);
    $pdf->lineto(640, $line);
    $pdf->stroke();

    // $traccFooter = new TraccFooter("assets/clientLogos/tracc_logo_footer.jpg", 'simpleOwnersReport', A4_LANDSCAPE);
    // $traccFooter->prerender($pdf);
    $pdf->end_page_ext('');

    // New Page
    $pdf->begin_page_ext(842, 595, '');
    $pdf->setColorExt('fill', 'rgb', 0.91, 0.91, 0.91, 0);
    $pdf->rect(20, 505, 802, 70);
    $pdf->fill();

    $pdf->setColorExt('fill', 'rgb', 0.91, 0.91, 0.91, 0);
    $pdf->rect(20, 20, 802, 475);
    $pdf->fill();

    renderLogoPage($pdf);

    $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
    $pdf->setFontExt($_fonts['Panton-Bold'], 18);
    $pdf->showBoxed($contentPageNo . ' RENT REVIEWS', 50, 535, 400, 18, 'left', '');

    $pdf->setColorExt('fill', 'rgb', 0.22, 0.59, 0.86, 1);
    $pdf->rect(25, 430, 792, 30);
    $pdf->fill();

    // above lines
    $pdf->setColorExt('stroke', 'rgb', 1, 1, 1, 0);
    $pdf->setlinewidth(0.5);
    $pdf->moveto(25, 460);
    $pdf->lineto(817, 460);
    $pdf->stroke();

    // middle vertical lines
    $pdf->setlinewidth(0.5);
    $pdf->moveto(80, 460);
    $pdf->lineto(80, 430);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(170, 460);
    $pdf->lineto(170, 430);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(240, 460);
    $pdf->lineto(240, 430);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(320, 460);
    $pdf->lineto(320, 430);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(400, 460);
    $pdf->lineto(400, 430);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(480, 460);
    $pdf->lineto(480, 430);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(560, 460);
    $pdf->lineto(560, 430);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(640, 460);
    $pdf->lineto(640, 430);
    $pdf->stroke();

    $pdf->setFontExt($_fonts['Panton-Bold'], 12);
    $pdf->setColorExt('both', 'rgb', 1, 1, 1, 0);
    $pdf->showBoxed('Shop No.', 25, 437, 55, 12, 'center', '');
    $pdf->showBoxed('Tenant Name', 80, 437, 90, 12, 'center', '');
    $pdf->showBoxed('Area', 170, 437, 70, 12, 'center', '');
    $pdf->showBoxed('Review Date', 240, 437, 80, 12, 'center', '');
    $pdf->showBoxed('Review Type', 320, 437, 80, 12, 'center', '');
    $pdf->showBoxed('Lease Expiry', 400, 437, 80, 12, 'center', '');
    $pdf->showBoxed('Current Rent', 480, 437, 80, 12, 'center', '');
    $pdf->showBoxed('New Rent', 560, 437, 80, 12, 'center', '');
    $pdf->showBoxed('Comments', 642, 437, 175, 12, 'left', '');

    // achieved
    $pdf->setColorExt('fill', 'rgb', 0.22, 0.59, 0.86, 1);
    $pdf->rect(25, 400, 792, 30);
    $pdf->fill();

    $pdf->setColorExt('fill', 'rgb', 1, 1, 1, 1);
    $pdf->rect(25, 427, 792, 3);
    $pdf->fill();

    $pdf->showBoxed('Achieved', 29, 407, 100, 12, 'left', '');
}

function renderNewRentReviewAchievedOutstanding(&$pdf, $line, &$outstandingLine, $contentPageNo)
{
    $_fonts['Panton-Light'] = $pdf->load_font('Panton-Light', 'host', '');
    $_fonts['Panton-Bold'] = $pdf->load_font('Panton-SemiBold', 'host', '');

    $pdf->setColorExt('stroke', 'rgb', 1, 1, 1, 1);
    // vertical lines
    $pdf->setlinewidth(0.5);
    $pdf->moveto(817, $outstandingLine);
    $pdf->lineto(817, $line);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(25, $outstandingLine);
    $pdf->lineto(25, $line);
    $pdf->stroke();

    // middle vertical lines
    $pdf->setlinewidth(0.5);
    $pdf->moveto(80, $outstandingLine);
    $pdf->lineto(80, $line);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(170, $outstandingLine);
    $pdf->lineto(170, $line);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(240, $outstandingLine);
    $pdf->lineto(240, $line);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(320, $outstandingLine);
    $pdf->lineto(320, $line);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(400, $outstandingLine);
    $pdf->lineto(400, $line);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(480, $outstandingLine);
    $pdf->lineto(480, $line);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(560, $outstandingLine);
    $pdf->lineto(560, $line);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(640, $outstandingLine);
    $pdf->lineto(640, $line);
    $pdf->stroke();

    // $traccFooter = new TraccFooter("assets/clientLogos/tracc_logo_footer.jpg", 'simpleOwnersReport', A4_LANDSCAPE);
    // $traccFooter->prerender($pdf);
    $pdf->end_page_ext('');

    // New Page
    $pdf->begin_page_ext(842, 595, '');
    $pdf->setColorExt('fill', 'rgb', 0.91, 0.91, 0.91, 0);
    $pdf->rect(20, 505, 802, 70);
    $pdf->fill();

    $pdf->setColorExt('fill', 'rgb', 0.91, 0.91, 0.91, 0);
    $pdf->rect(20, 20, 802, 475);
    $pdf->fill();

    renderLogoPage($pdf);

    $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
    $pdf->setFontExt($_fonts['Panton-Bold'], 18);
    $pdf->showBoxed($contentPageNo . ' RENT REVIEWS', 50, 535, 400, 18, 'left', '');

    $pdf->setColorExt('fill', 'rgb', 0.22, 0.59, 0.86, 1);
    $pdf->rect(25, 430, 792, 30);
    $pdf->fill();

    // above lines
    $pdf->setColorExt('stroke', 'rgb', 1, 1, 1, 0);
    $pdf->setlinewidth(0.5);
    $pdf->moveto(25, 460);
    $pdf->lineto(817, 460);
    $pdf->stroke();

    // middle vertical lines
    $pdf->setlinewidth(0.5);
    $pdf->moveto(80, 460);
    $pdf->lineto(80, 430);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(170, 460);
    $pdf->lineto(170, 430);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(240, 460);
    $pdf->lineto(240, 430);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(320, 460);
    $pdf->lineto(320, 430);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(400, 460);
    $pdf->lineto(400, 430);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(480, 460);
    $pdf->lineto(480, 430);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(560, 460);
    $pdf->lineto(560, 430);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(640, 460);
    $pdf->lineto(640, 430);
    $pdf->stroke();

    $pdf->setFontExt($_fonts['Panton-Bold'], 12);
    $pdf->setColorExt('both', 'rgb', 1, 1, 1, 0);
    $pdf->showBoxed('Shop No.', 25, 437, 55, 12, 'center', '');
    $pdf->showBoxed('Tenant Name', 80, 437, 90, 12, 'center', '');
    $pdf->showBoxed('Area', 170, 437, 70, 12, 'center', '');
    $pdf->showBoxed('Review Date', 240, 437, 80, 12, 'center', '');
    $pdf->showBoxed('Review Type', 320, 437, 80, 12, 'center', '');
    $pdf->showBoxed('Lease Expiry', 400, 437, 80, 12, 'center', '');
    $pdf->showBoxed('Current Rent', 480, 437, 80, 12, 'center', '');
    $pdf->showBoxed('New Rent', 560, 437, 80, 12, 'center', '');
    $pdf->showBoxed('Comments', 642, 437, 175, 12, 'left', '');

    // achieved
    $pdf->setColorExt('fill', 'rgb', 0.22, 0.59, 0.86, 1);
    $pdf->rect(25, 400, 792, 30);
    $pdf->fill();

    $pdf->setColorExt('fill', 'rgb', 1, 1, 1, 1);
    $pdf->rect(25, 427, 792, 3);
    $pdf->fill();

    $pdf->showBoxed('Outstanding', 29, 407, 100, 12, 'left', '');
}

function renderNewHoldoverPage(&$pdf, $line, $contentPageNo)
{
    $_fonts['Panton-Light'] = $pdf->load_font('Panton-Light', 'host', '');
    $_fonts['Panton-Bold'] = $pdf->load_font('Panton-SemiBold', 'host', '');

    $pdf->setColorExt('stroke', 'rgb', 1, 1, 1, 1);
    // vertical lines
    $pdf->setlinewidth(0.5);
    $pdf->moveto(817, 400);
    $pdf->lineto(817, $line);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(25, 400);
    $pdf->lineto(25, $line);
    $pdf->stroke();

    // middle vertical lines
    $pdf->setlinewidth(0.5);
    $pdf->moveto(80, 400);
    $pdf->lineto(80, $line);
    $pdf->stroke();


    $pdf->setlinewidth(0.5);
    $pdf->moveto(240, 400);
    $pdf->lineto(240, $line);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(320, 400);
    $pdf->lineto(320, $line);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(440, 400);
    $pdf->lineto(440, $line);
    $pdf->stroke();


    $pdf->setlinewidth(0.5);
    $pdf->moveto(560, 400);
    $pdf->lineto(560, $line);
    $pdf->stroke();


    // $traccFooter = new TraccFooter("assets/clientLogos/tracc_logo_footer.jpg", 'simpleOwnersReport', A4_LANDSCAPE);
    // $traccFooter->prerender($pdf);
    $pdf->end_page_ext('');

    // New Page
    $pdf->begin_page_ext(842, 595, '');
    $pdf->setColorExt('fill', 'rgb', 0.91, 0.91, 0.91, 0);
    $pdf->rect(20, 505, 802, 70);
    $pdf->fill();

    $pdf->setColorExt('fill', 'rgb', 0.91, 0.91, 0.91, 0);
    $pdf->rect(20, 20, 802, 475);
    $pdf->fill();

    renderLogoPage($pdf);

    $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
    $pdf->setFontExt($_fonts['Panton-Bold'], 18);
    $pdf->showBoxed($contentPageNo . ' HOLDOVER AND 12 MONTH EXPIRY REPORT', 50, 535, 400, 18, 'left', '');

    $pdf->setColorExt('fill', 'rgb', 0.22, 0.59, 0.86, 1);
    $pdf->rect(25, 430, 792, 30);
    $pdf->fill();

    // above lines
    $pdf->setColorExt('stroke', 'rgb', 1, 1, 1, 0);
    $pdf->setlinewidth(0.5);
    $pdf->moveto(25, 460);
    $pdf->lineto(817, 460);
    $pdf->stroke();

    // middle vertical lines
    $pdf->setlinewidth(0.5);
    $pdf->moveto(80, 460);
    $pdf->lineto(80, 430);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(240, 460);
    $pdf->lineto(240, 430);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(320, 460);
    $pdf->lineto(320, 430);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(440, 460);
    $pdf->lineto(440, 430);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(560, 460);
    $pdf->lineto(560, 430);
    $pdf->stroke();

    $pdf->setFontExt($_fonts['Panton-Bold'], 12);
    $pdf->setColorExt('both', 'rgb', 1, 1, 1, 0);
    $pdf->showBoxed('Shop No.', 25, 437, 55, 12, 'center', '');
    $pdf->showBoxed('Tenant Name', 80, 437, 150, 12, 'center', '');
    $pdf->showBoxed('Expiry Date', 240, 437, 80, 12, 'center', '');
    $pdf->showBoxed('Current Rent', 320, 437, 120, 12, 'center', '');
    $pdf->showBoxed('New Rent', 440, 437, 120, 12, 'center', '');
    $pdf->showBoxed('Comments', 562, 437, 175, 12, 'left', '');

    // achieved
    $pdf->setColorExt('fill', 'rgb', 0.22, 0.59, 0.86, 1);
    $pdf->rect(25, 400, 792, 30);
    $pdf->fill();

    $pdf->setColorExt('fill', 'rgb', 1, 1, 1, 1);
    $pdf->rect(25, 427, 792, 3);
    $pdf->fill();

    $pdf->showBoxed('Holdovers', 29, 407, 100, 12, 'left', '');
}

function renderNewHoldoverExpiryPage(&$pdf, $line, &$outstandingLine, $contentPageNo)
{
    $_fonts['Panton-Light'] = $pdf->load_font('Panton-Light', 'host', '');
    $_fonts['Panton-Bold'] = $pdf->load_font('Panton-SemiBold', 'host', '');

    $pdf->setColorExt('stroke', 'rgb', 1, 1, 1, 1);
    // vertical lines
    $pdf->setlinewidth(0.5);
    $pdf->moveto(817, $outstandingLine);
    $pdf->lineto(817, $line);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(25, $outstandingLine);
    $pdf->lineto(25, $line);
    $pdf->stroke();

    // middle vertical lines
    $pdf->setlinewidth(0.5);
    $pdf->moveto(80, $outstandingLine);
    $pdf->lineto(80, $line);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(240, $outstandingLine);
    $pdf->lineto(240, $line);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(320, $outstandingLine);
    $pdf->lineto(320, $line);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(440, $outstandingLine);
    $pdf->lineto(440, $line);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(560, $outstandingLine);
    $pdf->lineto(560, $line);
    $pdf->stroke();

    // $traccFooter = new TraccFooter("assets/clientLogos/tracc_logo_footer.jpg", 'simpleOwnersReport', A4_LANDSCAPE);
    // $traccFooter->prerender($pdf);
    $pdf->end_page_ext('');

    // New Page
    $pdf->begin_page_ext(842, 595, '');
    $pdf->setColorExt('fill', 'rgb', 0.91, 0.91, 0.91, 0);
    $pdf->rect(20, 505, 802, 70);
    $pdf->fill();

    $pdf->setColorExt('fill', 'rgb', 0.91, 0.91, 0.91, 0);
    $pdf->rect(20, 20, 802, 475);
    $pdf->fill();

    renderLogoPage($pdf);

    $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
    $pdf->setFontExt($_fonts['Panton-Bold'], 18);
    $pdf->showBoxed($contentPageNo . ' HOLDOVER AND 12 MONTH EXPIRY REPORT', 50, 535, 400, 18, 'left', '');

    $pdf->setColorExt('fill', 'rgb', 0.22, 0.59, 0.86, 1);
    $pdf->rect(25, 430, 792, 30);
    $pdf->fill();

    // above lines
    $pdf->setColorExt('stroke', 'rgb', 1, 1, 1, 0);
    $pdf->setlinewidth(0.5);
    $pdf->moveto(25, 460);
    $pdf->lineto(817, 460);
    $pdf->stroke();

    // middle vertical lines
    $pdf->setlinewidth(0.5);
    $pdf->moveto(80, 460);
    $pdf->lineto(80, 430);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(240, 460);
    $pdf->lineto(240, 430);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(320, 460);
    $pdf->lineto(320, 430);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(440, 460);
    $pdf->lineto(440, 430);
    $pdf->stroke();


    $pdf->setlinewidth(0.5);
    $pdf->moveto(560, 460);
    $pdf->lineto(560, 430);
    $pdf->stroke();


    $pdf->setFontExt($_fonts['Panton-Bold'], 12);
    $pdf->setColorExt('both', 'rgb', 1, 1, 1, 0);
    $pdf->showBoxed('Shop No.', 25, 437, 55, 12, 'center', '');
    $pdf->showBoxed('Tenant Name', 80, 437, 150, 12, 'center', '');
    $pdf->showBoxed('Expiry Date', 240, 437, 80, 12, 'center', '');
    $pdf->showBoxed('Current Rent', 320, 437, 120, 12, 'center', '');
    $pdf->showBoxed('New Rent', 440, 437, 120, 12, 'center', '');
    $pdf->showBoxed('Comments', 562, 437, 175, 12, 'left', '');

    // achieved
    $pdf->setColorExt('fill', 'rgb', 0.22, 0.59, 0.86, 1);
    $pdf->rect(25, 400, 792, 30);
    $pdf->fill();

    $pdf->setColorExt('fill', 'rgb', 1, 1, 1, 1);
    $pdf->rect(25, 427, 792, 3);
    $pdf->fill();

    $pdf->showBoxed('Expiries', 29, 407, 100, 12, 'left', '');
}

function renderUpdatedCommentaryPage(&$pdf, $line, &$outstandingLine, $subHeader, $contentPageNo)
{
    $_fonts['Panton-Light'] = $pdf->load_font('Panton-Light', 'host', '');
    $_fonts['Panton-Bold'] = $pdf->load_font('Panton-SemiBold', 'host', '');

    $pdf->setColorExt('stroke', 'rgb', 1, 1, 1, 1);
    // vertical lines
    $pdf->setlinewidth(0.5);
    $pdf->moveto(817, $outstandingLine);
    $pdf->lineto(817, $line);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(25, $outstandingLine);
    $pdf->lineto(25, $line);
    $pdf->stroke();

    // middle vertical lines
    $pdf->setlinewidth(0.5);
    $pdf->moveto(190, $outstandingLine);
    $pdf->lineto(190, $line);
    $pdf->stroke();


    $pdf->setlinewidth(0.5);
    $pdf->moveto(310, $outstandingLine);
    $pdf->lineto(310, $line);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(430, $outstandingLine);
    $pdf->lineto(430, $line);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(550, $outstandingLine);
    $pdf->lineto(550, $line);
    $pdf->stroke();

    $pdf->end_page_ext('');

    // New Page
    $pdf->begin_page_ext(842, 595, '');
    $pdf->setColorExt('fill', 'rgb', 0.91, 0.91, 0.91, 0);
    $pdf->rect(20, 505, 802, 70);
    $pdf->fill();

    $pdf->setColorExt('fill', 'rgb', 0.91, 0.91, 0.91, 0);
    $pdf->rect(20, 20, 802, 475);
    $pdf->fill();

    renderLogoPage($pdf);

    $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
    $pdf->setFontExt($_fonts['Panton-Bold'], 18);
    $pdf->showBoxed($contentPageNo . ' FINANCIAL PERFORMANCE', 50, 535, 400, 18, 'left', '');

    $pdf->setColorExt('fill', 'rgb', 0.22, 0.59, 0.86, 1);
    $pdf->rect(25, 430, 792, 30);
    $pdf->fill();

    // above lines
    $pdf->setColorExt('stroke', 'rgb', 1, 1, 1, 0);
    $pdf->setlinewidth(0.5);
    $pdf->moveto(25, 460);
    $pdf->lineto(817, 460);
    $pdf->stroke();

    // middle vertical lines
    $pdf->setlinewidth(0.5);
    $pdf->moveto(190, 460);
    $pdf->lineto(190, 430);
    $pdf->stroke();


    $pdf->setlinewidth(0.5);
    $pdf->moveto(310, 460);
    $pdf->lineto(310, 430);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(430, 460);
    $pdf->lineto(430, 430);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(550, 460);
    $pdf->lineto(550, 430);
    $pdf->stroke();

    $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
    $pdf->setFontExt($_fonts['Panton-Bold'], 16);
    $pdf->showBoxed(
        'Commentary on major variances ( > 5% or ' . $_SESSION['country_default']['currency_symbol'] . '2,000.00)',
        25,
        467,
        400,
        18,
        'left',
        ''
    );


    $pdf->setFontExt($_fonts['Panton-Bold'], 12);
    $pdf->setColorExt('both', 'rgb', 1, 1, 1, 0);
    $pdf->showBoxed('Accounts', 27, 437, 165, 12, 'left', '');
    $pdf->showBoxed('Actual', 190, 437, 120, 12, 'center', '');
    $pdf->showBoxed('Budget', 310, 437, 120, 12, 'center', '');
    $pdf->showBoxed(
        'Variance(' . $_SESSION['country_default']['currency_symbol'] . ')',
        430,
        437,
        120,
        12,
        'center',
        ''
    );
    $pdf->showBoxed('Comments', 562, 437, 175, 12, 'left', '');

    // achieved
    $pdf->setColorExt('fill', 'rgb', 0.22, 0.59, 0.86, 1);
    $pdf->rect(25, 400, 792, 30);
    $pdf->fill();

    $pdf->setColorExt('fill', 'rgb', 1, 1, 1, 1);
    $pdf->rect(25, 427, 792, 3);
    $pdf->fill();

    $pdf->showBoxed($subHeader, 29, 407, 200, 12, 'left', '');
}


function commentDisplay(&$pdf, $value, $xAxis, $yAxis, $width, $height, $alignment, $charCount)
{
    $yAxis = strlen($value) > $charCount ? $yAxis + 1 : $yAxis - 4;

    $pdf->showBoxed($value, $xAxis, $yAxis, $width, $height, $alignment, '');
}

function dbGetCasualLeasingActual($account, $propertyID, $year, $period)
{
    global $clientDB, $dbh, $context;
    $dbh->selectDatabase($clientDB);
    if (is_array($propertyID) && count($propertyID ?? []) > 0) {
        $propertyID = implode("','", $propertyID);
    }

    $params = [];
    $sql = "SELECT
            a.pmca_code AS accountID,
            a.pmca_name AS accountName,
            COALESCE(SUM(b.balanceCash), 0) * -1 AS balanceCash,
            COALESCE(SUM(b.balanceAccruals), 0) * -1 AS balanceAccruals  
            FROM
            pmca_chart a,
            gl_trial_balance b

            WHERE
             b.accountID = a.pmca_code AND b.propertyID IN ('{$propertyID}') AND b.year=" . addSQLParam(
        $params,
        $year
    ) . ' AND b.period <= ' . addSQLParam($params, $period) . '
             AND accountID in (' . addSQLParam($params, $account) . ')
            GROUP BY
            a.pmca_code,
            a.pmca_name 
            ORDER BY
            a.pmca_code';

    return $dbh->executeSingle($sql, $params);
}

function dbGetCasualLeasingBudget($account, $propertyID, $year, $period)
{
    global $clientDB, $dbh, $context;
    $dbh->selectDatabase($clientDB);
    if (is_array($propertyID) && count($propertyID ?? []) > 0) {
        $propertyID = implode("','", $propertyID);
    }

    $params = [];
    $sql = "SELECT DISTINCT
                    pmrp_acc accountID,
                    pmca_type AS accountType,
                    pmca_name AS accountName,

                    COALESCE(SUM(pmrp_b_c_amt), 0) AS budgetCash,
                    COALESCE(SUM(pmrp_b_a_amt), 0) AS budgetAccruals
                FROM
                    pmrp_b_rev_per,
                    pmcg_chart_grp,
                    pmca_chart
                WHERE
                    pmrp_prop  IN ('{$propertyID}')
                    AND pmcg_acc = pmca_code
                    AND pmcg_acc in (" . addSQLParam($params, $account) . ')
                    AND pmrp_acc = pmca_code
                    AND pmrp_year=' . addSQLParam($params, $year) . "
                    AND pmrp_per BETWEEN '1' AND " . addSQLParam($params, $period) . "
                GROUP BY pmrp_acc, pmca_type, pmca_name, pmcg_subgrp 
            UNION ALL
                SELECT DISTINCT
                    pmep_exp_acc accountID,
                    pmca_type AS accountType,
                    pmca_name AS accountName,

                    COALESCE(SUM(pmep_b_c_amt), 0) AS budgetCash,
                    COALESCE(SUM(pmep_b_a_amt), 0) AS budgetAccruals
                FROM
                    pmep_b_exp_per,
                    pmcg_chart_grp,
                    pmca_chart
                WHERE
                    pmep_prop IN ('" . $propertyID . "')
                    AND pmcg_acc = pmca_code
                    AND pmcg_acc in (" . addSQLParam($params, $account) . ')
                    AND pmep_exp_acc = pmca_code
                    AND pmep_year=' . addSQLParam($params, $year) . "
                    AND pmep_per BETWEEN '1' AND " . addSQLParam($params, $period) . '
                GROUP BY pmep_exp_acc, pmca_type, pmca_name, pmcg_subgrp            
            ORDER BY
                pmrp_acc ASC';

    return $dbh->executeSingle($sql, $params);
}

function dbGetFinancialActualIncome(
    $propertyID,
    $group,
    $dateFrom,
    $dateTo,
    $year,
    $promo = false,
    $column = 'balanceCash'
) {
    global $clientDB, $dbh, $context;
    $dbh->selectDatabase($clientDB);
    $params = [];
    $queryGroup_params = [];

    if ($promo) {
        $queryGroup = ' and pmca_code in (205100,205101,205102,205105,205109)';
    } else {
        $queryGroup = ' AND pmca_gl_account_group2 = ' . addSQLParam(
            $queryGroup_params,
            $group
        ) . ' and pmca_code not in (205100,205101,205102,205105,205109)';
    }

    $sql = "SELECT
            COALESCE(SUM({$column}), 0) * -1 AS amount
            FROM 
            gl_trial_balance b
            inner join [pmca_chart] a on b.accountID = a.pmca_code
            WHERE b.propertyID = " . addSQLParam($params, $propertyID) . '  and b.year = ' . addSQLParam(
        $params,
        $year
    ) . '
            and b.period between ' . addSQLParam($params, $dateFrom) . ' and ' . addSQLParam($params, $dateTo) . " 
            {$queryGroup} " . addSQLParam($params, $queryGroup_params, false);

    return $dbh->executeSingle($sql, $params);
}

function dbGetFinancialActualIncomeAccounts(
    $propertyID,
    $group,
    $dateFrom,
    $dateTo,
    $year,
    $promo = false,
    $column = 'balanceCash'
) {
    global $clientDB, $dbh, $context;
    $dbh->selectDatabase($clientDB);
    $params = [];
    $queryGroup_params = [];

    if ($promo) {
        $queryGroup = ' and pmca_code in (205100,205101,205102,205105,205109)';
    } else {
        $queryGroup = ' AND pmca_gl_account_group2 = ' . addSQLParam(
            $queryGroup_params,
            $group
        ) . ' and pmca_code not in (205100,205101,205102,205105,205109)';
    }

    $sql = "SELECT
        COALESCE(SUM({$column}), 0) * -1 AS amount,pmca_name as description
        FROM 
        gl_trial_balance b
        inner join [pmca_chart] a on b.accountID = a.pmca_code
        WHERE b.propertyID = " . addSQLParam($params, $propertyID) . '  and b.year = ' . addSQLParam($params, $year) . '
        and b.period between ' . addSQLParam($params, $dateFrom) . ' and ' . addSQLParam($params, $dateTo) . " 
        {$queryGroup} " . addSQLParam($params, $queryGroup_params, false) . '
        group by pmca_code,pmca_name
        ';

    return $dbh->executeSet($sql, false, true, $params);
}

function dbGetFinancialBudgetIncome(
    $propertyID,
    $group,
    $year,
    $periodFrom,
    $periodTo,
    $promo = false,
    $column = 'pmrp_b_c_amt'
) {
    global $clientDB, $dbh, $context;
    $dbh->selectDatabase($clientDB);
    $params = [];
    $queryGroup_params = [];

    if ($promo) {
        $queryGroup = ' and pmca_code in (205100,205101,205102,205105,205109)';
    } else {
        $queryGroup = ' AND gl_account_group = ' . addSQLParam(
            $queryGroup_params,
            $group
        ) . ' and pmca_code not in (205100,205101,205102,205105,205109)';
    }

    $sql = "SELECT  COALESCE(SUM({$column}), 0) AS amount 
        from [pmrp_b_rev_per] inner join [pmca_chart] on [pmrp_b_rev_per].[pmrp_acc] = [pmca_chart].[pmca_code] 
        INNER JOIN gl_account_groups B ON pmca_gl_account_group2 = B.gl_account_group
        where [pmrp_b_rev_per].[pmrp_prop] = " . addSQLParam(
        $params,
        $propertyID
    ) . ' and [pmrp_b_rev_per].[pmrp_year] = ' . addSQLParam($params, $year) . " and {$column} is not null 
        And pmrp_per between " . addSQLParam($params, $periodFrom) . ' and ' . addSQLParam($params, $periodTo) . "
        {$queryGroup} " . addSQLParam($params, $queryGroup_params, false);

    return $dbh->executeSingle($sql, $params);
}

function dbGetFinancialBudgetIncomeAccounts(
    $propertyID,
    $group,
    $year,
    $periodFrom,
    $periodTo,
    $promo = false,
    $column = 'pmrp_b_c_amt'
) {
    global $clientDB, $dbh, $context;
    $dbh->selectDatabase($clientDB);
    $params = [];
    $queryGroup_params = [];

    if ($promo) {
        $queryGroup = ' and pmca_code in (205100,205101,205102,205105,205109)';
    } else {
        $queryGroup = ' AND gl_account_group = ' . addSQLParam(
            $queryGroup_params,
            $group
        ) . ' and pmca_code not in (205100,205101,205102,205105,205109)';
    }

    $sql = "SELECT  COALESCE(SUM({$column}), 0) AS amount ,pmca_name as description
        from [pmrp_b_rev_per] inner join [pmca_chart] on [pmrp_b_rev_per].[pmrp_acc] = [pmca_chart].[pmca_code] 
        INNER JOIN gl_account_groups B ON pmca_gl_account_group2 = B.gl_account_group
        where [pmrp_b_rev_per].[pmrp_prop] = " . addSQLParam(
        $params,
        $propertyID
    ) . ' and [pmrp_b_rev_per].[pmrp_year] = ' . addSQLParam($params, $year) . " and {$column} is not null 
        And pmrp_per between " . addSQLParam($params, $periodFrom) . ' and ' . addSQLParam($params, $periodTo) . "
        {$queryGroup} " . addSQLParam($params, $queryGroup_params, false) . '
        group by pmca_code , pmca_name';

    return $dbh->executeSet($sql, false, true, $params);
}

function dbGetFinancialBudgetExpense($propertyID, $group, $year, $periodFrom, $periodTo, $column = 'pmep_b_c_amt')
{
    global $clientDB, $dbh, $context;
    $dbh->selectDatabase($clientDB);
    $params = [];

    $sql = "SELECT COALESCE(SUM({$column}), 0) as amount  
        from pmep_b_exp_per 
        inner join [pmca_chart] on pmep_b_exp_per.[pmep_exp_acc] = [pmca_chart].[pmca_code] 
        INNER JOIN gl_account_groups B ON pmca_gl_account_group2 = B.gl_account_group
        LEFT JOIN pmrcg_chart_grp on [pmep_exp_acc] between pmrcg_acc and pmrcg_acc_to and pmrcg_grp = 'TRACC3' 
        LEFT JOIN pmas_acc_subgrp ON pmas_grp = pmrcg_grp AND pmas_subgrp = pmrcg_subgrp 
        where pmep_prop = " . addSQLParam($params, $propertyID) . ' and pmep_year = ' . addSQLParam($params, $year) . '
        and pmep_per between ' . addSQLParam($params, $periodFrom) . ' and ' . addSQLParam($params, $periodTo) . '
        AND gl_account_group = ' . addSQLParam($params, $group);

    return $dbh->executeSingle($sql, $params);
}

function dbGetFinancialActualExpense($propertyID, $group, $periodFrom, $periodTo, $year, $column = 'balanceCash')
{
    global $clientDB, $dbh, $context;
    $dbh->selectDatabase($clientDB);
    $params = [];

    $sql = "SELECT
            COALESCE(SUM({$column}), 0) AS amount
            FROM 
            gl_trial_balance b
            inner join [pmca_chart] a on b.accountID = a.pmca_code
            JOIN pmrcg_chart_grp on accountID between pmrcg_acc and pmrcg_acc_to and pmrcg_grp = 'TRACC3'
            WHERE b.propertyID = " . addSQLParam($params, $propertyID) . '  and b.year = ' . addSQLParam(
        $params,
        $year
    ) . '
            and b.period between ' . addSQLParam($params, $periodFrom) . '  and ' . addSQLParam($params, $periodTo) . '  
            and pmca_gl_account_group2 = ' . addSQLParam($params, $group);

    return $dbh->executeSingle($sql, $params);
}

function dbGetFinancialBudgetExpenseSubGroup(
    $propertyID,
    $group,
    $year,
    $periodFrom,
    $periodTo,
    $column = 'pmep_b_c_amt'
) {
    global $clientDB, $dbh, $context;
    $dbh->selectDatabase($clientDB);
    $params = [];

    $sql = "SELECT COALESCE(SUM({$column}), 0) as amount  , pmrcg_desc as description
        from pmep_b_exp_per 
        inner join [pmca_chart] on pmep_b_exp_per.[pmep_exp_acc] = [pmca_chart].[pmca_code] 
        INNER JOIN gl_account_groups B ON pmca_gl_account_group2 = B.gl_account_group
        LEFT JOIN pmrcg_chart_grp on [pmep_exp_acc] between pmrcg_acc and pmrcg_acc_to and pmrcg_grp = 'TRACC3' 
        LEFT JOIN pmas_acc_subgrp ON pmas_grp = pmrcg_grp AND pmas_subgrp = pmrcg_subgrp 
        where pmep_prop = " . addSQLParam($params, $propertyID) . ' and pmep_year = ' . addSQLParam($params, $year) . '
        and pmep_per between ' . addSQLParam($params, $periodFrom) . ' and ' . addSQLParam($params, $periodTo) . '
        AND gl_account_group = ' . addSQLParam($params, $group) . '
        group by pmrcg_desc';

    return $dbh->executeSet($sql, false, true, $params);
}

function dbGetFinancialActualExpenseSubGroup(
    $propertyID,
    $group,
    $periodFrom,
    $periodTo,
    $year,
    $column = 'balanceCash'
) {
    global $clientDB, $dbh, $context;
    $dbh->selectDatabase($clientDB);
    $params = [];

    $sql = "SELECT
            COALESCE(SUM({$column}), 0) AS amount,pmrcg_desc as description
            FROM 
            gl_trial_balance b
            inner join [pmca_chart] a on b.accountID = a.pmca_code
            JOIN pmrcg_chart_grp on accountID between pmrcg_acc and pmrcg_acc_to and pmrcg_grp = 'TRACC3'
            WHERE b.propertyID = " . addSQLParam($params, $propertyID) . "  and b.year = {$year}
            and b.period between " . addSQLParam($params, $periodFrom) . '  and ' . addSQLParam($params, $periodTo) . '  
            and pmca_gl_account_group2 = ' . addSQLParam($params, $group) . ' group by pmrcg_desc';

    return $dbh->executeSet($sql, false, true, $params);
}

function dbGetFinancialBudgetExpenseAccounts(
    $propertyID,
    $group,
    $year,
    $periodFrom,
    $periodTo,
    $column = 'pmep_b_c_amt'
) {
    global $clientDB, $dbh, $context;
    $dbh->selectDatabase($clientDB);
    $params = [];

    $sql = "SELECT COALESCE(SUM({$column}), 0) as amount  , pmrcg_desc as description , pmca_name as account_name
        from pmep_b_exp_per 
        inner join [pmca_chart] on pmep_b_exp_per.[pmep_exp_acc] = [pmca_chart].[pmca_code] 
        INNER JOIN gl_account_groups B ON pmca_gl_account_group2 = B.gl_account_group
        LEFT JOIN pmrcg_chart_grp on [pmep_exp_acc] between pmrcg_acc and pmrcg_acc_to and pmrcg_grp = 'TRACC3' 
        LEFT JOIN pmas_acc_subgrp ON pmas_grp = pmrcg_grp AND pmas_subgrp = pmrcg_subgrp 
        where pmep_prop = " . addSQLParam($params, $propertyID) . ' and pmep_year = ' . addSQLParam($params, $year) . '
        and pmep_per between ' . addSQLParam($params, $periodFrom) . ' and ' . addSQLParam($params, $periodTo) . '
        AND gl_account_group = ' . addSQLParam($params, $group) . '
        group by pmrcg_desc,pmca_name';

    return $dbh->executeSet($sql, false, true, $params);
}

function dbGetFinancialActualExpenseAccounts(
    $propertyID,
    $group,
    $periodFrom,
    $periodTo,
    $year,
    $column = 'balanceCash'
) {
    global $clientDB, $dbh, $context;
    $dbh->selectDatabase($clientDB);
    $params = [];

    //    $queryGroup = " AND gl_account_group = '{$group}'";

    $sql = "SELECT
            COALESCE(SUM({$column}), 0) AS amount, pmrcg_desc as description, pmca_name as account_name
            FROM 
            gl_trial_balance b
            inner join [pmca_chart] a on b.accountID = a.pmca_code
            JOIN pmrcg_chart_grp on accountID between pmrcg_acc and pmrcg_acc_to and pmrcg_grp = 'TRACC3'
            WHERE b.propertyID = " . addSQLParam($params, $propertyID) . '  and b.year = ' . addSQLParam(
        $params,
        $year
    ) . '
            and b.period between ' . addSQLParam($params, $periodFrom) . '  and ' . addSQLParam($params, $periodTo) . ' 
            and pmca_gl_account_group2 = ' . addSQLParam($params, $group) . ' group by pmrcg_desc,pmca_name';

    return $dbh->executeSet($sql, false, true, $params);
}

function dbGetDigitalPerformance($year, $previousYear, $property)
{
    global $clientDB, $dbh, $context;
    $dbh->selectDatabase($clientDB);
    $params = [];

    $sql = 'SELECT COALESCE(SUM(pmdc_count), 0) as total,YEAR( pmdc_date ) as year, MONTH(pmdc_date) as month ,pmdc_door as website
        from pmdc_website_count
        where pmdc_prop = ' . addSQLParam($params, $property) . "
        and pmdc_date between '{$previousYear}-01-01' and '{$year}-12-31'
        group by pmdc_door,YEAR( pmdc_date ), MONTH(pmdc_date) ";

    return $dbh->executeSet($sql, false, true, $params);
}

function renderNewMajorWorksReportPage(&$pdf, $line, $contentPageNo)
{
    $_fonts['Panton-Light'] = $pdf->load_font('Panton-Light', 'host', '');
    $_fonts['Panton-Bold'] = $pdf->load_font('Panton-SemiBold', 'host', '');

    $pdf->setColorExt('stroke', 'rgb', 1, 1, 1, 1);
    // vertical lines
    $pdf->setlinewidth(0.5);
    $pdf->moveto(817, 460);
    $pdf->lineto(817, $line);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(25, 460);
    $pdf->lineto(25, $line);
    $pdf->stroke();

    // middle lines

    $pdf->setlinewidth(0.5);
    $pdf->moveto(240, 460);
    $pdf->lineto(240, $line);
    $pdf->stroke();


    // $traccFooter = new TraccFooter("assets/clientLogos/tracc_logo_footer.jpg", 'simpleOwnersReport', A4_LANDSCAPE);
    // $traccFooter->prerender($pdf);
    $pdf->end_page_ext('');

    // New Page

    $pdf->begin_page_ext(842, 595, '');
    $pdf->setColorExt('fill', 'rgb', 0.91, 0.91, 0.91, 0);
    $pdf->rect(20, 505, 802, 70);
    $pdf->fill();

    $pdf->setColorExt('fill', 'rgb', 0.91, 0.91, 0.91, 0);
    $pdf->rect(20, 20, 802, 475);
    $pdf->fill();

    renderLogoPage($pdf);

    $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
    $pdf->setFontExt($_fonts['Panton-Bold'], 18);
    $pdf->showBoxed($contentPageNo . ' MAJOR WORKS AND CONTRACTS', 50, 535, 400, 18, 'left', '');


    $pdf->setColorExt('fill', 'rgb', 0.22, 0.59, 0.86, 1);
    $pdf->rect(25, 430, 792, 30);
    $pdf->fill();

    $pdf->setColorExt('fill', 'rgb', 1, 1, 1, 1);
    $pdf->rect(25, 427, 792, 3);
    $pdf->fill();


    // above lines
    $pdf->setColorExt('stroke', 'rgb', 1, 1, 1, 0);
    $pdf->setlinewidth(0.5);
    $pdf->moveto(25, 460);
    $pdf->lineto(817, 460);
    $pdf->stroke();

    $pdf->setFontExt($_fonts['Panton-Bold'], 12);
    $pdf->setColorExt('both', 'rgb', 1, 1, 1, 0);
    $pdf->showBoxed('Area', 27, 437, 213, 12, 'left', '');
    $pdf->showBoxed('Comments', 242, 437, 575, 12, 'left', '');
}

function renderNewCompetitorReviewReportPage(&$pdf, $line, $contentPageNo)
{
    $_fonts['Panton-Light'] = $pdf->load_font('Panton-Light', 'host', '');
    $_fonts['Panton-Bold'] = $pdf->load_font('Panton-SemiBold', 'host', '');

    $pdf->setColorExt('stroke', 'rgb', 1, 1, 1, 1);
    // vertical lines
    $pdf->setlinewidth(0.5);
    $pdf->moveto(817, 460);
    $pdf->lineto(817, $line);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(25, 460);
    $pdf->lineto(25, $line);
    $pdf->stroke();

    // middle lines

    $pdf->setlinewidth(0.5);
    $pdf->moveto(240, 460);
    $pdf->lineto(240, $line);
    $pdf->stroke();


    // $traccFooter = new TraccFooter("assets/clientLogos/tracc_logo_footer.jpg", 'simpleOwnersReport', A4_LANDSCAPE);
    // $traccFooter->prerender($pdf);
    $pdf->end_page_ext('');

    // New Page

    $pdf->begin_page_ext(842, 595, '');
    $pdf->setColorExt('fill', 'rgb', 0.91, 0.91, 0.91, 0);
    $pdf->rect(20, 505, 802, 70);
    $pdf->fill();

    $pdf->setColorExt('fill', 'rgb', 0.91, 0.91, 0.91, 0);
    $pdf->rect(20, 20, 802, 475);
    $pdf->fill();

    renderLogoPage($pdf);

    $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
    $pdf->setFontExt($_fonts['Panton-Bold'], 18);
    $pdf->showBoxed($contentPageNo . ' COMPETITOR REVIEW', 50, 535, 400, 18, 'left', '');


    $pdf->setColorExt('fill', 'rgb', 0.22, 0.59, 0.86, 1);
    $pdf->rect(25, 430, 792, 30);
    $pdf->fill();

    $pdf->setColorExt('fill', 'rgb', 1, 1, 1, 1);
    $pdf->rect(25, 427, 792, 3);
    $pdf->fill();


    // above lines
    $pdf->setColorExt('stroke', 'rgb', 1, 1, 1, 0);
    $pdf->setlinewidth(0.5);
    $pdf->moveto(25, 460);
    $pdf->lineto(817, 460);
    $pdf->stroke();

    $pdf->setFontExt($_fonts['Panton-Bold'], 12);
    $pdf->setColorExt('both', 'rgb', 1, 1, 1, 0);
    $pdf->showBoxed('Competitor', 27, 437, 213, 12, 'left', '');
    $pdf->showBoxed('Status & comments', 242, 437, 575, 12, 'left', '');
}

function dbGetVirtualVacancyData($property, $sub_report, $period, $year, $periodFrom, $periodTo)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $params = [];
    $prevYear = $year - 1;
    $prevYearDate = date('d/m/Y', strtotime(convertDate($periodTo) . '-1 year'));
    $sql = 'SELECT  
    pmua_unit as column_1 , 
    pmle_t_name as column_2,
    CONVERT(char(10), pmle_exp_dt , 103)  as column_3  ,
    (SELECT   COALESCE(SUM(OutstandingGross), 0) from FN_get_outstanding_AR_per_property_all_leases(CONVERT(datetime, ' . addSQLParam(
        $params,
        $periodTo
    ) . ', 103),CONVERT(datetime, ' . addSQLParam($params, $periodTo) . ', 103),' . addSQLParam(
        $params,
        $property
    ) . ') where LeaseCode = pmle_lease) as column_4,
    (SELECT   COALESCE(SUM(pmgu_amount), 0) from pmgu_l_guarantee where pmgu_lease = pmle_lease and pmgu_prop = ' . addSQLParam(
        $params,
        $property
    ) . ') as column_5,
    (SELECT COALESCE(SUM(pmlr_act_amt), 0) AS actual FROM pmlr_l_retail OUTER APPLY (Select top 1 * from pmua_unit_area where pmua_prop = pmlr_prop AND pmua_lease = pmlr_lease and pmlr_turnover_date >= pmua_from_dt order by pmua_to_dt desc) pmua_unit_area WHERE pmlr_prop = ' . addSQLParam(
        $params,
        $property
    ) . ' AND ((pmlr_per > ' . addSQLParam($params, $period) . ' AND pmlr_year=' . addSQLParam(
        $params,
        $prevYear
    ) . ') OR (pmlr_per <= ' . addSQLParam($params, $period) . ' AND pmlr_year=' . addSQLParam(
        $params,
        $year
    ) . ')) AND pmua_to_dt > CONVERT(datetime, ' . addSQLParam($params, $prevYearDate) . ', 103) and pmlr_lease = pmle_lease ) as column_6,
    (SELECT COALESCE(SUM(pmut_pct_rent), 0) AS percentageRent FROM pmut_unit_tp
        OUTER APPLY (select top 1 * from pmua_unit_area where pmua_prop = pmut_prop AND pmua_lease = pmut_lease 
        AND ( ((CONVERT(datetime, ' . addSQLParam(
        $params,
        $periodTo
    ) . ', 103) BETWEEN pmua_from_dt AND pmua_to_dt) or (CONVERT(datetime, ' . addSQLParam($params, $periodFrom) . ', 103) BETWEEN pmua_from_dt AND pmua_to_dt) ) 
        or (( pmua_from_dt BETWEEN CONVERT(datetime, ' . addSQLParam(
        $params,
        $periodFrom
    ) . ', 103) AND CONVERT(datetime, ' . addSQLParam(
        $params,
        $periodTo
    ) . ', 103) ) or ( pmua_to_dt BETWEEN CONVERT(datetime, ' . addSQLParam(
        $params,
        $periodFrom
    ) . ', 103) AND CONVERT(datetime, ' . addSQLParam($params, $periodTo) . ', 103) ) ) ) order by pmua_to_dt desc) pmua_unit_area 
        WHERE   pmua_lease = pmut_lease AND pmua_prop = pmut_prop AND pmut_from_amt = 0 AND pmut_prop = ' . addSQLParam(
        $params,
        $property
    ) . ' and pmut_lease = pmle_lease) as percentageRent,
    (SELECT COALESCE(SUM(pmla_amt), 0) FROM pmla_l_c_amt, pmlc_l_charge WHERE pmla_lease = pmlc_lease AND pmla_prop = pmlc_prop AND pmla_serial = pmlc_serial AND (CONVERT(datetime, ' . addSQLParam(
        $params,
        $periodTo
    ) . ', 103) BETWEEN pmla_start_dt AND pmla_end_dt) AND pmla_prop=' . addSQLParam($params, $property) . " AND pmlc_lease=pmle_lease AND pmlc_acc In (SELECT pmca_code FROM pmca_chart WHERE pmca_ret_out = 1)) as outgoings,
    (SELECT COALESCE(SUM(pmla_amt), 0) FROM pmla_l_c_amt, pmlc_l_charge WHERE pmla_lease = pmlc_lease  AND pmla_prop = pmlc_prop AND pmla_serial = pmlc_serial AND pmla_lease=pmle_lease AND pmlc_chg_type='R' AND (CONVERT(datetime, " . addSQLParam(
        $params,
        $periodTo
    ) . ', 103) BETWEEN pmla_start_dt AND pmla_end_dt) AND pmla_prop=' . addSQLParam($params, $property) . ") as baseRent,
    column_0
    from mgmt_sub_report_table_data
        join  pmua_unit_area area on   area.pmua_prop = property_code and pmua_status = 'O' and pmua_unit = column_1 
        join pmle_lease on pmle_lease = pmua_lease and pmle_prop = property_code
    where sub_report_id = " . addSQLParam($params, $sub_report) . ' and cal_year=' . addSQLParam(
        $params,
        $year
    ) . ' and cal_period=' . addSQLParam($params, $period) . ' and property_code = ' . addSQLParam(
        $params,
        $property
    ) . '
';

    return $dbh->executeSet($sql, false, true, $params);
}

function dbGetBankGuaranteeData($property, $sub_report, $period, $year, $periodFrom, $periodTo)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $params = [];
    date('d/m/Y', strtotime(convertDate($periodTo) . '-1 year'));
    $sql = "SELECT  
    concat(RTRIM(LTRIM(area.pmua_unit)),' - ' ,rtrim(pmle_t_name)) as column_1,
    column_2,
    pmgu_amount as column_3,
    CONVERT(char(10), pmgu_expire_date, 103) as column_4,
    column_0
    from mgmt_sub_report_table_data
    Join pmua_unit_area area on area.pmua_prop = " . addSQLParam($params, $property) . ' and area.pmua_unit = column_1
    Join pmle_lease on pmle_lease = area.pmua_lease and pmle_prop = area.pmua_prop 
    LEFT JOIN pmgu_l_guarantee on pmle_lease = pmgu_lease and pmle_prop = pmgu_prop
    where sub_report_id = ' . addSQLParam($params, $sub_report) . ' and property_code = ' . addSQLParam(
        $params,
        $property
    ) . ' and cal_year=' . addSQLParam($params, $year) . ' and cal_period=' . addSQLParam(
        $params,
        $period
    ) . '  and CONVERT(datetime, ' . addSQLParam($params, $periodTo) . ', 103) between area.pmua_from_dt and area.pmua_to_dt
';

    return $dbh->executeSet($sql, false, true, $params);
}

function renderNewCapitalExpenditureReportPage(&$pdf, $line, $contentPageNo)
{
    $_fonts['Panton-Light'] = $pdf->load_font('Panton-Light', 'host', '');
    $_fonts['Panton-Bold'] = $pdf->load_font('Panton-SemiBold', 'host', '');

    $pdf->setColorExt('stroke', 'rgb', 1, 1, 1, 1);
    // vertical lines
    $pdf->setlinewidth(0.5);
    $pdf->moveto(817, 460);
    $pdf->lineto(817, $line);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(25, 460);
    $pdf->lineto(25, $line);
    $pdf->stroke();

    // middle lines

    $pdf->setlinewidth(0.5);
    $pdf->moveto(170, 460);
    $pdf->lineto(170, $line);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(270, 460);
    $pdf->lineto(270, $line);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(360, 460);
    $pdf->lineto(360, $line);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(450, 460);
    $pdf->lineto(450, $line);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(540, 460);
    $pdf->lineto(540, $line);
    $pdf->stroke();


    // $traccFooter = new TraccFooter("assets/clientLogos/tracc_logo_footer.jpg", 'simpleOwnersReport', A4_LANDSCAPE);
    // $traccFooter->prerender($pdf);
    $pdf->end_page_ext('');

    // New Page

    $pdf->begin_page_ext(842, 595, '');
    $pdf->setColorExt('fill', 'rgb', 0.91, 0.91, 0.91, 0);
    $pdf->rect(20, 505, 802, 70);
    $pdf->fill();

    $pdf->setColorExt('fill', 'rgb', 0.91, 0.91, 0.91, 0);
    $pdf->rect(20, 20, 802, 475);
    $pdf->fill();

    renderLogoPage($pdf);

    $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
    $pdf->setFontExt($_fonts['Panton-Bold'], 18);
    $pdf->showBoxed($contentPageNo . ' CAPITAL EXPENDITURE', 50, 535, 400, 18, 'left', '');


    $pdf->setColorExt('fill', 'rgb', 0.22, 0.59, 0.86, 1);
    $pdf->rect(25, 430, 792, 30);
    $pdf->fill();

    $pdf->setColorExt('fill', 'rgb', 1, 1, 1, 1);
    $pdf->rect(25, 427, 792, 3);
    $pdf->fill();


    // above lines
    $pdf->setColorExt('stroke', 'rgb', 1, 1, 1, 0);
    $pdf->setlinewidth(0.5);
    $pdf->moveto(25, 460);
    $pdf->lineto(817, 460);
    $pdf->stroke();

    $pdf->setFontExt($_fonts['Panton-Bold'], 12);
    $pdf->setColorExt('both', 'rgb', 1, 1, 1, 0);
    $pdf->showBoxed('Project', 25, 437, 145, 12, 'center', '');
    $pdf->showBoxed(
        "Total Budget\n(" . $_SESSION['country_default']['currency_symbol'] . '000)',
        170,
        435,
        100,
        24,
        'center',
        ''
    );
    $pdf->showBoxed(
        "Approved\n(" . $_SESSION['country_default']['currency_symbol'] . '000)',
        270,
        435,
        90,
        24,
        'center',
        ''
    );
    $pdf->showBoxed('Start date', 360, 437, 90, 12, 'center', '');
    $pdf->showBoxed('Complete date', 450, 437, 90, 12, 'center', '');
    $pdf->showBoxed('Comment', 542, 437, 272, 12, 'left', '');
}

function dbGetMarketingFundActualIncome($propertyList, $year, $dateFrom, $dateTo, $account = null)
{
    global $clientDB, $dbh, $context;
    $dbh->selectDatabase($clientDB);
    if (is_array($propertyList) && count($propertyList ?? []) > 0) {
        $propertyList = implode("','", $propertyList);
    }

    $params = [];
    $queryGroup_params = [];

    if ($account) {
        $queryGroup = ' and pmca_code in (' . addSQLParam($queryGroup_params, $account) . ')';
    } else {
        $queryGroup = ' AND pmca_code not in (205135,205100)';
    }

    $sql = "SELECT
            COALESCE(SUM(balanceCash), 0) * -1 AS amount
            FROM 
            gl_trial_balance b
            inner join [pmca_chart] a on b.accountID = a.pmca_code
            WHERE b.propertyID IN ('{$propertyList}')  and b.year = " . addSQLParam($params, $year) . "
            and pmca_type = 'I'
            and b.period between " . addSQLParam($params, $dateFrom) . ' and ' . addSQLParam($params, $dateTo) . " 
            {$queryGroup} " . addSQLParam($params, $queryGroup_params, false);

    // echo $sql . "------------";
    return $dbh->executeSingle($sql, $params);
}

function dbGetMarketingFundBudgetIncome($propertyList, $year, $periodFrom, $periodTo, $account = null)
{
    global $clientDB, $dbh, $context;
    $dbh->selectDatabase($clientDB);
    if (is_array($propertyList)) {
        $propertyList = implode("','", $propertyList);
    }

    $params = [];
    $queryGroup_params = [];

    if ($account) {
        $queryGroup = ' and pmca_code in (' . addSQLParam($queryGroup_params, $account) . ')';
    } else {
        $queryGroup = ' AND  pmca_code not in (205135,205100)';
    }

    $sql = "SELECT  COALESCE(SUM(pmrp_b_rev_per.pmrp_b_c_amt), 0) AS amount 
        from [pmrp_b_rev_per] inner join [pmca_chart] on [pmrp_b_rev_per].[pmrp_acc] = [pmca_chart].[pmca_code] 
        INNER JOIN gl_account_groups B ON pmca_gl_account_group2 = B.gl_account_group
        where [pmrp_b_rev_per].[pmrp_prop] in ('{$propertyList}') and [pmrp_b_rev_per].[pmrp_year] = " . addSQLParam(
        $params,
        $year
    ) . ' and [pmrp_b_rev_per].[pmrp_b_c_amt] is not null 
        And pmrp_per between ' . addSQLParam($params, $periodFrom) . ' and ' . addSQLParam($params, $periodTo) . "
        {$queryGroup} " . addSQLParam($params, $queryGroup_params, false);

    return $dbh->executeSingle($sql, $params);
}

function dbGetMarketingFundActualExpense($propertyList, $year, $periodFrom, $periodTo, $account = null)
{
    global $clientDB, $dbh, $context;
    $dbh->selectDatabase($clientDB);
    if (is_array($propertyList) && count($propertyList ?? []) > 0) {
        $propertyList = implode("','", $propertyList);
    }

    $params = [];
    $queryGroup_params = [];

    if ($account) {
        $queryGroup = ' and pmca_code in (' . addSQLParam($queryGroup_params, $account) . ')';
    } else {
        $queryGroup = ' AND  pmca_code not in (420245,420250,420255,420305,420310,420315,420320,420322,420325,420330,420332,420335,420336,420340,420345,420346,420352,420355,421100,421105,421110,421200,421250,430352,420100,420110,420120,420130,420140,420150,420160,420170,420205,420210,420215,420220,420225,420230,420235,420240,420326)';
    }

    $sql = "SELECT
            COALESCE(SUM(balanceCash), 0) AS amount
            FROM 
            gl_trial_balance b
            inner join [pmca_chart] a on b.accountID = a.pmca_code
            JOIN pmrcg_chart_grp on accountID between pmrcg_acc and pmrcg_acc_to and pmrcg_grp = 'TRACC3'
            WHERE b.propertyID in ('{$propertyList}') and b.year = " . addSQLParam($params, $year) . "
            and pmca_type = 'E'
            and b.period between " . addSQLParam($params, $periodFrom) . '  and ' . addSQLParam($params, $periodTo) . "  
            {$queryGroup} " . addSQLParam($params, $queryGroup_params, false);

    return $dbh->executeSingle($sql, $params);
}

function dbGetMarketingFundBudgetExpense($propertyList, $year, $periodFrom, $periodTo, $account = null)
{
    global $clientDB, $dbh, $context;
    $dbh->selectDatabase($clientDB);
    if (is_array($propertyList) && count($propertyList ?? []) > 0) {
        $propertyList = implode("','", $propertyList);
    }

    $params = [];
    $queryGroup_params = [];

    if ($account) {
        $queryGroup = ' and pmca_code in (' . addSQLParam($queryGroup_params, $account) . ')';
    } else {
        $queryGroup = ' AND pmca_code not in (420245,420250,420255,420305,420310,420315,420320,420322,420325,420330,420332,420335,420336,420340,420345,420346,420352,420355,421100,421105,421110,421200,421250,430352,420100,420110,420120,420130,420140,420150,420160,420170,420205,420210,420215,420220,420225,420230,420235,420240,420326)';
    }

    $sql = "SELECT COALESCE(SUM(pmep_b_c_amt), 0) as amount  
        from pmep_b_exp_per 
        inner join [pmca_chart] on pmep_b_exp_per.[pmep_exp_acc] = [pmca_chart].[pmca_code] 
        INNER JOIN gl_account_groups B ON pmca_gl_account_group2 = B.gl_account_group
        LEFT JOIN pmrcg_chart_grp on [pmep_exp_acc] between pmrcg_acc and pmrcg_acc_to and pmrcg_grp = 'TRACC3' 
        LEFT JOIN pmas_acc_subgrp ON pmas_grp = pmrcg_grp AND pmas_subgrp = pmrcg_subgrp 
        where pmep_prop  in ('{$propertyList}') and pmep_year = " . addSQLParam($params, $year) . '
        and pmep_per between ' . addSQLParam($params, $periodFrom) . ' and ' . addSQLParam($params, $periodTo) . "
        {$queryGroup} " . addSQLParam($params, $queryGroup_params, false);

    return $dbh->executeSingle($sql, $params);
}

function renderNewFinancialSummarySubGroupPage(&$pdf, $line, $contentPageNo)
{
    $_fonts['Panton-Light'] = $pdf->load_font('Panton-Light', 'host', '');
    $_fonts['Panton-Bold'] = $pdf->load_font('Panton-SemiBold', 'host', '');

    $pdf->setColorExt('stroke', 'rgb', 1, 1, 1, 1);
    // ENDING LINES
    $pdf->setlinewidth(0.5);
    $pdf->moveto(817, 430);
    $pdf->lineto(817, $line);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(25, 430);
    $pdf->lineto(25, $line);
    $pdf->stroke();

    // middle vertical lines

    $pdf->setlinewidth(0.5);
    $pdf->moveto(139, 430);
    $pdf->lineto(139, $line);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(252, 430);
    $pdf->lineto(252, $line);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(365, 430);
    $pdf->lineto(365, $line);
    $pdf->stroke();


    $pdf->setlinewidth(0.5);
    $pdf->moveto(478, 430);
    $pdf->lineto(478, $line);
    $pdf->stroke();


    $pdf->setlinewidth(0.5);
    $pdf->moveto(591, 430);
    $pdf->lineto(591, $line);
    $pdf->stroke();


    $pdf->setlinewidth(0.5);
    $pdf->moveto(704, 430);
    $pdf->lineto(704, $line);
    $pdf->stroke();

    // $traccFooter = new TraccFooter("assets/clientLogos/tracc_logo_footer.jpg", 'simpleOwnersReport', A4_LANDSCAPE);
    // $traccFooter->prerender($pdf);
    $pdf->end_page_ext('');

    // New Page

    $pdf->begin_page_ext(842, 595, '');
    $pdf->setColorExt('fill', 'rgb', 0.91, 0.91, 0.91, 0);
    $pdf->rect(20, 505, 802, 70);
    $pdf->fill();

    $pdf->setColorExt('fill', 'rgb', 0.91, 0.91, 0.91, 0);
    $pdf->rect(20, 20, 802, 475);
    $pdf->fill();

    renderLogoPage($pdf);

    $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
    $pdf->setFontExt($_fonts['Panton-Bold'], 18);
    $pdf->showBoxed($contentPageNo . ' FINANCIAL PERFORMANCE', 50, 535, 400, 18, 'left', '');


    $pdf->setColorExt('fill', 'rgb', 0.22, 0.59, 0.86, 1);
    $pdf->rect(25, 400, 792, 30);
    $pdf->fill();

    // above lines
    $pdf->setColorExt('stroke', 'rgb', 1, 1, 1, 0);
    $pdf->setlinewidth(0.5);
    $pdf->moveto(25, 400);
    $pdf->lineto(817, 400);
    $pdf->stroke();

    $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
    $pdf->setFontExt($_fonts['Panton-Bold'], 16);
    $pdf->showBoxed(((float) $contentPageNo + 0.1) . ' Financial Summary', 25, 437, 400, 18, 'left', '');

    // DATA START
    $pdf->setFontExt($_fonts['Panton-Bold'], 12);
    $pdf->setColorExt('both', 'rgb', 1, 1, 1, 0);
    $pdf->showBoxed('', 25, 407, 114, 12, 'center', '');
    $pdf->showBoxed('Month Actual', 139, 407, 114, 12, 'center', '');
    $pdf->showBoxed('Month Budget', 252, 407, 113, 12, 'center', '');
    $pdf->showBoxed('Variance', 365, 407, 113, 12, 'center', '');
    $pdf->showBoxed('YTD Actual', 478, 407, 113, 12, 'center', '');
    $pdf->showBoxed('YTD Budget', 591, 407, 113, 12, 'center', '');
    $pdf->showBoxed('Variance', 704, 407, 113, 12, 'center', '');
}

function contentPage($property, $period, $year, $managementReport, $returnFileNo = null)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $params = [];

    $sql = 'SELECT mgmt_sub_report_name as description,sub_report_file_path from mgmt_sub_report as sr
        full join mgmt_sub_report_selection_data as sd on  sr.sub_report_id = sd.sub_report_id
        and sr.mgmt_report_id = sd.mgmt_report_id and property_code = ' . addSQLParam($params, $property) . ' 
        and cal_period = ' . addSQLParam($params, $period) . '  and cal_year = ' . addSQLParam($params, $year) . '
        where sr.mgmt_report_id = ' . addSQLParam($params, $managementReport) . ' and (default_status = 1 or is_selected = 1)
        order by sub_report_sequence';

    $data = $dbh->executeSet($sql, false, true, $params);
    $files = [];
    foreach ($data as $key => $value) {
        if (! isset($files[$value['sub_report_file_path']])) {
            $files[$value['sub_report_file_path']] = $value['description'];
        }
    }

    $contentArray[1] = ['header' => '1.0 Centre Summary', 'subReport' => ['1.1 Key Highlights and Issues']];
    $HeaderCount = 2;

    if ($managementReport == 1) {
        $group2 = [
            'propertyReport/Lease_Equity/vacancyReport.php',
            'propertyReport/Lease_Equity/virtualVacanciesReport.php',
            'propertyReport/Lease_Equity/holdoverExpiryReport.php',
            'propertyReport/Lease_Equity/floorPlanReport.php',
            'propertyReport/Lease_Equity/rentReviewReport.php',
            'propertyReport/Lease_Equity/1Image1comment.php',
            'propertyReport/Lease_Equity/2Images2comments.php',
            'propertyReport/Lease_Equity/casualLeasing.php',
            'propertyReport/Lease_Equity/casualHighlights.php',
        ];
    } else {
        $group2 = [
            'propertyReport/Lease_Equity/vacancyReport.php',
            'propertyReport/Lease_Equity/virtualVacanciesReport.php',
            'propertyReport/Lease_Equity/holdoverExpiryReport.php',
            'propertyReport/Lease_Equity/floorPlanReport.php',
            'propertyReport/Lease_Equity/rentReviewReport.php',
            'propertyReport/Lease_Equity/1Image1comment.php',
            'propertyReport/Lease_Equity/2Images2comments.php',
            'propertyReport/Lease_Equity/casualAccrualLeasing.php',
            'propertyReport/Lease_Equity/casualHighlights.php',
        ];
    }

    $subCount = 1;
    $subReportFile = [];
    foreach ($group2 as $key => $value) {
        if (isset($files[$value])) {
            if ($returnFileNo && $returnFileNo == 'propertyReport/Lease_Equity/2Images2comments.php'
                && isset($files['propertyReport/Lease_Equity/1Image1comment.php']) && $returnFileNo == $value) {
                return $HeaderCount . '.' . ($subCount - 1);
            }

            if ($returnFileNo && $returnFileNo == $value) {
                return $HeaderCount . '.' . $subCount;
            }

            if ($value === 'propertyReport/Lease_Equity/2Images2comments.php' && isset($files['propertyReport/Lease_Equity/1Image1comment.php'])) {
                continue;
            }

            $subReportFile[] = $HeaderCount . '.' . $subCount . ' ' . $files[$value];
            $subCount++;
        }
    }

    if (! empty($subReportFile)) {
        $contentArray[$HeaderCount] = [
            'header' => $HeaderCount . '.0 Leasing and Tenancy',
            'subReport' => $subReportFile,
        ];
        $HeaderCount++;
    }


    if ($managementReport == 1) {
        $group3 = [
            'propertyReport/Lease_Equity/financialSummary.php',
            'propertyReport/Lease_Equity/varianceCommentary.php',
            'propertyReport/Lease_Equity/financialSummarySubGroup.php',
            'propertyReport/Lease_Equity/financialSummaryAccounts.php',
            'propertyReport/Lease_Equity/riskAndOpportunities.php',
            'propertyReport/Lease_Equity/detailedArrearsReport.php',
            'propertyReport/Lease_Equity/capitalExpenditure.php',
            'propertyReport/Lease_equity/bankGuarantee.php',
        ];
    } else {
        $group3 = [
            'propertyReport/Lease_Equity/financialSummaryAccrual.php',
            'propertyReport/Lease_Equity/varianceCommentary.php',
            'propertyReport/Lease_Equity/financialSummarySubGroupAccrual.php',
            'propertyReport/Lease_Equity/financialSummaryAccountsAccrual.php',
            'propertyReport/Lease_Equity/riskAndOpportunities.php',
            'propertyReport/Lease_Equity/detailedArrearsReport.php',
            'propertyReport/Lease_Equity/capitalExpenditure.php',
            'propertyReport/Lease_equity/bankGuarantee.php',
        ];
    }

    $subCount = 1;
    $subReportFile = [];
    foreach ($group3 as $value) {
        if (isset($files[$value])) {
            if ($returnFileNo && in_array(
                $returnFileNo,
                [
                    'propertyReport/Lease_Equity/financialSummary.php',
                    'propertyReport/Lease_Equity/varianceCommentary.php',
                    'propertyReport/Lease_Equity/financialSummarySubGroup.php',
                    'propertyReport/Lease_Equity/financialSummaryAccounts.php',
                    'propertyReport/Lease_Equity/financialSummaryAccrual.php',
                    'propertyReport/Lease_Equity/financialSummarySubGroupAccrual.php',
                    'propertyReport/Lease_Equity/financialSummaryAccountsAccrual.php',
                ]
            )) {
                return $HeaderCount . '.' . 0;
            }

            if ($returnFileNo && $returnFileNo == $value) {
                return $HeaderCount . '.' . $subCount;
            }

            if ($value === 'propertyReport/Lease_Equity/varianceCommentary.php') {
                continue;
            }

            if ($value === 'propertyReport/Lease_Equity/financialSummarySubGroup.php' && isset($files['propertyReport/Lease_Equity/financialSummary.php'])) {
                continue;
            }

            if ($value === 'propertyReport/Lease_Equity/financialSummaryAccounts.php' && (isset($files['propertyReport/Lease_Equity/financialSummary.php']) || isset($files['propertyReport/Lease_Equity/financialSummarySubGroup.php']))) {
                continue;
            }

            if ($value === 'propertyReport/Lease_Equity/financialSummarySubGroupAccrual.php' && isset($files['propertyReport/Lease_Equity/financialSummaryAccrual.php'])) {
                continue;
            }

            if ($value === 'propertyReport/Lease_Equity/financialSummaryAccountsAccrual.php' && (isset($files['propertyReport/Lease_Equity/financialSummaryAccrual.php']) || isset($files['propertyReport/Lease_Equity/financialSummarySubGroupAccrual.php']))) {
                continue;
            }

            $subReportFile[] = $HeaderCount . '.' . $subCount . ' ' . $files[$value];
            $subCount++;
        }
    }

    if (! empty($subReportFile)) {
        $contentArray[$HeaderCount] = [
            'header' => $HeaderCount . '.0 Financial Performance',
            'subReport' => $subReportFile,
        ];
        $HeaderCount++;
    }

    $group4 = [
        'propertyReport/Lease_Equity/salesSummary.php',
        'propertyReport/Lease_Equity/digitalWebsite.php',
        'propertyReport/Lease_Equity/marketingFundPerformance.php',
        'propertyReport/Lease_Equity/1Image.php',
        'propertyReport/Lease_Equity/2Images.php',
        'propertyReport/Lease_Equity/competitorReview.php',
    ];


    $subCount = 1;
    $subReportFile = [];
    foreach ($group4 as $value) {
        if (isset($files[$value])) {
            if ($returnFileNo && $returnFileNo == 'propertyReport/Lease_Equity/2Images.php'
                && isset($files['propertyReport/Lease_Equity/1Image.php']) && $returnFileNo == $value) {
                return $HeaderCount . '.' . ($subCount - 1);
            }

            if ($returnFileNo && $returnFileNo == $value) {
                return $HeaderCount . '.' . $subCount;
            }

            if ($value === 'propertyReport/Lease_Equity/2Images.php' && isset($files['propertyReport/Lease_Equity/1Image.php'])) {
                continue;
            }

            $subReportFile[] = $HeaderCount . '.' . $subCount . ' ' . $files[$value];
            $subCount++;
        }
    }

    if (! empty($subReportFile)) {
        $contentArray[$HeaderCount] = [
            'header' => $HeaderCount . '.0 Centre Performance',
            'subReport' => $subReportFile,
        ];
        $HeaderCount++;
    }


    $group5 = [
        'propertyReport/Lease_Equity/operationAndRisk.php',
        'propertyReport/Lease_Equity/majorWorksAndContracts.php',
    ];


    $subCount = 1;
    $subReportFile = [];
    foreach ($group5 as $value) {
        if (isset($files[$value])) {
            if ($returnFileNo && $returnFileNo == $value) {
                return $HeaderCount . '.' . $subCount;
            }

            if ($value === 'propertyReport/Lease_Equity/operationAndRisk.php') {
                $subReportFile[] = $HeaderCount . '.' . $subCount . ' ' . 'Public Liability Insurance Claims';
                $subCount++;
                $subReportFile[] = $HeaderCount . '.' . $subCount . ' ' . 'Security Incidents';
                $subCount++;
            } else {
                $subReportFile[] = $HeaderCount . '.' . $subCount . ' ' . $files[$value];
            }
        }
    }

    if (! empty($subReportFile)) {
        $contentArray[$HeaderCount] = [
            'header' => $HeaderCount . '.0 Operations and Risk',
            'subReport' => $subReportFile,
        ];
        $HeaderCount++;
    }

    $contentArray[$HeaderCount] = [
        'header' => 'APPENDICES',
        'subReport' => [],
    ];

    if ($returnFileNo) {
        return '';
    }

    return $contentArray;
}

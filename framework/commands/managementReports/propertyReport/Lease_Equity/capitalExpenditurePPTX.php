<?php

include_once __DIR__ . '/powerPointFunction.php';
include_once __DIR__ . '/LeaseEquityFunction.php';

$contentPageNo = contentPage($propertyID, $calendar['period'], $calendar['year'], $reportType, 'propertyReport/Lease_Equity/capitalExpenditure.php');
insertLeaseEquityTemplate($currentSlide, $contentPageNo . ' CAPITAL EXPENDITURE ');

$fontWhite = ['fontSize' => 9, 'fontName' => 'Panton-Bold', 'fontBold' => true, 'fontColor' => 'FFFFFFFF'];
$fontBlack = ['fontSize' => 8, 'fontName' => 'Panton-Light', 'fontBold' => false, 'fontColor' => 'FF000000'];

// CREATE TABLE
$shape = $currentSlide->createTableShape(6);
$shape->setOffsetX(20);
$shape->setOffsetY(120);

// header row
$lineNo = 120;
$row = insertTableRow($shape, 30, 'FF3996DB');
insertTableCell($row, 'Project', 200, $fontWhite, 'ctr', 'ctr');
insertTableCell($row, "Total Budget\n(" . $_SESSION['country_default']['currency_symbol'] . '000)', 95, $fontWhite, 'ctr', 'ctr');
insertTableCell($row, "Approved\n(" . $_SESSION['country_default']['currency_symbol'] . '000)', 95, $fontWhite, 'ctr', 'ctr');
insertTableCell($row, 'Start date', 95, $fontWhite, 'ctr', 'ctr');
insertTableCell($row, 'Complete date', 95, $fontWhite, 'ctr', 'ctr');
insertTableCell($row, 'Comment', 340, $fontWhite, 'ctr', 'ctr');

$parity = 1;
$management_data_sequence = dbGetSubReportDataConfig($s['sub_report_id']);
$management_data = dbGetSubReportData($propertyID, $s['sub_report_id'], $calendar['period'], $calendar['year']);

if (isset($management_data_sequence[1])) {
    foreach ($management_data as $data) {
        if ($data['table_id'] == $management_data_sequence[1]) {

            if ($lineNo > 650) {

                // Create slide
                $currentSlide = $objPHPPowerPoint->createSlide();
                insertLeaseEquityTemplate($currentSlide, $contentPageNo . ' CAPITAL EXPENDITURE ');

                // CREATE TABLE
                $shape = $currentSlide->createTableShape(6);
                $shape->setOffsetX(20);
                $shape->setOffsetY(120);

                // header row
                $lineNo = 120;
                $row = insertTableRow($shape, 30, 'FF3996DB');
                insertTableCell($row, 'Project', 200, $fontWhite, 'ctr', 'ctr');
                insertTableCell($row, "Total Budget\n(" . $_SESSION['country_default']['currency_symbol'] . '000)', 95, $fontWhite, 'ctr', 'ctr');
                insertTableCell($row, "Approved\n(" . $_SESSION['country_default']['currency_symbol'] . '000)', 95, $fontWhite, 'ctr', 'ctr');
                insertTableCell($row, 'Start date', 95, $fontWhite, 'ctr', 'ctr');
                insertTableCell($row, 'Complete date', 95, $fontWhite, 'ctr', 'ctr');
                insertTableCell($row, 'Comment', 340, $fontWhite, 'ctr', 'ctr');

            }


            // add row
            $lineNo += 30;
            $row = insertTableRow($shape, 30, ($parity == 1 ? 'FFCEDDF1' : 'FFE8EFF8'));
            insertTableCell($row, $data['column_1'], 0, $fontBlack, 'l', 'ctr', 0, 2);
            insertTableCell($row, toMoney($data['column_2']), 0, $fontBlack, 'r', 'ctr', 2);
            insertTableCell($row, toMoney($data['column_3']), 0, $fontBlack, 'r', 'ctr', 2);
            insertTableCell($row, ($data['column_4']), 0, $fontBlack, 'ctr', 'ctr');
            insertTableCell($row, ($data['column_5']), 0, $fontBlack, 'ctr', 'ctr');
            insertTableCell($row, $data['column_0'], 0, $fontBlack, 'l', 'ctr', 0, 2);

            $parity *= -1;
        }
    }
}

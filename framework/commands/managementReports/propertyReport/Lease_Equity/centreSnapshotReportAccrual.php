<?php

include_once __DIR__ . '/fontFunction.php';
include_once __DIR__ . '/LeaseEquityFunction.php';

global $_fonts, $reportDescription;
global $clientDB;

// 1.0  Centre snapshot
$page++;
$pdf->begin_page_ext(842, 595, '');
$pdf->setColorExt('fill', 'rgb', 0.91, 0.91, 0.91, 0);
$pdf->rect(20, 505, 802, 70);
$pdf->fill();

$pdf->setColorExt('fill', 'rgb', 0.91, 0.91, 0.91, 0);
$pdf->rect(20, 20, 802, 475);
$pdf->fill();

renderLogoPage($pdf);

$pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
$pdf->setFontExt($_fonts['Panton-Bold'], 18);
$pdf->showBoxed('1.0 CENTRE SNAPSHOT', 50, 535, 400, 18, 'left', '');


// NET INCOME
$pdf->setColorExt('fill', 'rgb', 1, 1, 1, 1);
$pdf->rect(40, 410, 762, 75);
$pdf->fill();

$pdf->setColorExt('fill', 'rgb', 0.23, 0.45, 0.77, 0);
$pdf->rect(40, 410, 60, 75);
$pdf->fill();

$pdf->setColorExt('fill', 'rgb', 0.71, 0.78, 0.91, 0);
$pdf->rect(220, 470, 288, 15);
$pdf->fill();

$pdf->setColorExt('fill', 'rgb', 0.23, 0.45, 0.77, 0);
$pdf->rect(508, 470, 294, 15);
$pdf->fill();

$pdf->setlinewidth(0.5);
$pdf->moveto(40, 485);
$pdf->lineto(802, 485);
$pdf->stroke();

$pdf->setlinewidth(0.5);
$pdf->moveto(40, 410);
$pdf->lineto(802, 410);
$pdf->stroke();

$pdf->setlinewidth(0.5);
$pdf->moveto(802, 410);
$pdf->lineto(802, 485);
$pdf->stroke();

$pdf->setlinewidth(0.5);
$pdf->moveto(40, 410);
$pdf->lineto(40, 485);
$pdf->stroke();

// horizontal bar
$pdf->setlinewidth(0.5);
$pdf->moveto(100, 470);
$pdf->lineto(802, 470);
$pdf->stroke();

$pdf->setlinewidth(0.5);
$pdf->moveto(100, 455);
$pdf->lineto(802, 455);
$pdf->stroke();

$pdf->setlinewidth(0.5);
$pdf->moveto(100, 440);
$pdf->lineto(802, 440);
$pdf->stroke();

$pdf->setlinewidth(0.5);
$pdf->moveto(100, 425);
$pdf->lineto(802, 425);
$pdf->stroke();

// SET ALL THE VARIABLES HERE
$cal = dbGetCalendarForProperty($propertyID);
$calendarName = $cal['calendarName'];
$calendar = dbGetMasterCalendarPeriodForDate($calendarName, $periodTo);
$calendarYear = dbGetCalendarPeriods($propertyID, $calendar['year']);

// show label
$pdf->setColorExt('both', 'rgb', 1, 1, 1, 0);
$pdf->setFontExt($_fonts['Panton-Bold'], 13);
$pdf->showBoxed('NET INCOME', 40, 435, 60, 28, 'center', '');
$pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
$pdf->setFontExt($_fonts['Panton-Bold'], 11);
$pdf->showBoxed("Month '000's", 220, 473, 290, 12, 'center', '');
$pdf->setColorExt('both', 'rgb', 1, 1, 1, 1);
$pdf->setFontExt($_fonts['Panton-Bold'], 11);
$pdf->showBoxed("YTD '000's", 510, 473, 290, 12, 'center', '');
$pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
$pdf->setFontExt($_fonts['Panton-Bold'], 11);
$pdf->showBoxed('Actual', 223, 458, 68, 12, 'right', '');
$pdf->showBoxed('Budget', 295, 458, 68, 12, 'right', '');
$pdf->showBoxed('Var $', 367, 458, 68, 12, 'right', '');
$pdf->showBoxed('Var %', 439, 458, 68, 12, 'right', '');
$pdf->showBoxed('Actual', 511, 458, 68, 12, 'right', '');
$pdf->showBoxed('Budget', 583, 458, 68, 12, 'right', '');
$pdf->showBoxed('Var $', 655, 458, 68, 12, 'right', '');
$pdf->showBoxed('Var %', 727, 458, 68, 12, 'right', '');
$pdf->showBoxed('Income', 102, 444 - 1, 68, 12, 'left', '');
$pdf->showBoxed('Expense', 102, 429 - 1, 68, 12, 'left', '');
$pdf->showBoxed('Net Income', 102, 414 - 1, 68, 12, 'left', '');

$currentIncomeBudget = dbGetNetIncomeBudget($propertyID, $calendar['period'], $calendar['period'], $calendar['year'], 'pmrp_b_a_amt');
$YTDIncomeBudget = dbGetNetIncomeBudget($propertyID, 1, $calendar['period'], $calendar['year'], 'pmrp_b_a_amt');
$currentIncomeActual = dbGetNetIncomeActual($propertyID, $calendar['period'], $calendar['period'], $calendar['year'], 'balanceAccruals');
$YTDIncomeActual = dbGetNetIncomeActual($propertyID, 1, $calendar['period'], $calendar['year'], 'balanceAccruals');
$currentExpenseBudget = dbGetNetExpenseBudget($propertyID, $calendar['period'], $calendar['period'], $calendar['year'], 'pmep_b_a_amt');
$YTDExpenseBudget = dbGetNetExpenseBudget($propertyID, 1, $calendar['period'], $calendar['year'], 'pmep_b_a_amt');
$currentExpenseActual = dbGetNetExpenseActual($propertyID, $calendar['period'], $calendar['period'], $calendar['year'], 'balanceAccruals');
$YTDExpenseActual = dbGetNetExpenseActual($propertyID, 1, $calendar['period'], $calendar['year'], 'balanceAccruals');
$netIncomeCurrentActual = $currentIncomeActual - $currentExpenseActual;
$netIncomeCurrentBudget = $currentIncomeBudget - $currentExpenseBudget;
$netIncomeYTDActual = $YTDIncomeActual - $YTDExpenseActual;
$netIncomeYTDBudget = $YTDIncomeBudget - $YTDExpenseBudget;
$incomeCurrentDollar = $currentIncomeActual - $currentIncomeBudget;
$incomeYTDDollar = $YTDIncomeActual - $YTDIncomeBudget;
$incomeCurrentPercent = calculateVariancePercentage($currentIncomeActual, $currentIncomeBudget);
$incomeYTDPercent = calculateVariancePercentage($YTDIncomeActual, $YTDIncomeBudget);
$expenseCurrentDollar = $currentExpenseBudget - $currentExpenseActual;
$expenseYTDDollar =  $YTDExpenseBudget - $YTDExpenseActual;
$expenseCurrentPercent = variancePercentage(round(thousandDivision($expenseCurrentDollar), 1), round(thousandDivision($currentExpenseBudget), 1));
$expenseYTDPercent = variancePercentage(round(thousandDivision($expenseYTDDollar), 1), round(thousandDivision($YTDExpenseBudget), 1));
$netCurrentDollar = $netIncomeCurrentActual - $netIncomeCurrentBudget;
$netYTDDollar = $netIncomeYTDActual - $netIncomeYTDBudget;
$netCurrentPercent = calculateVariancePercentage($netIncomeCurrentActual, $netIncomeCurrentBudget);
$netYTDPercent = calculateVariancePercentage($netIncomeYTDActual, $netIncomeYTDBudget);

$pdf->setFontExt($_fonts['Panton-Light'], 11);
$pdf->showBoxed(toMoney(thousandDivision($currentIncomeActual), '$', 1), 223, 444 - 1, 68, 12, 'right', '');
$pdf->showBoxed(toMoney(thousandDivision($currentIncomeBudget), '$', 1), 295, 444 - 1, 68, 12, 'right', '');
$pdf->showBoxed(toMoney(thousandDivision($incomeCurrentDollar), '$', 1), 367, 444 - 1, 68, 12, 'right', '');
$pdf->showBoxed(toMoney($incomeCurrentPercent, '', 1) . ' %', 439, 444 - 1, 68, 12, 'right', '');
$pdf->showBoxed(toMoney(thousandDivision($YTDIncomeActual), '$', 1), 509, 444 - 1, 70, 12, 'right', '');
$pdf->showBoxed(toMoney(thousandDivision($YTDIncomeBudget), '$', 1), 580, 444 - 1, 70, 12, 'right', '');
$pdf->showBoxed(toMoney(thousandDivision($incomeYTDDollar), '$', 1), 655, 444 - 1, 68, 12, 'right', '');
$pdf->showBoxed(toMoney($incomeYTDPercent, '', 1) . ' %', 727, 444, 68, 12, 'right', '');

$pdf->showBoxed(toMoney(thousandDivision($currentExpenseActual), '$', 1), 223, 429 - 1, 68, 12, 'right', '');
$pdf->showBoxed(toMoney(thousandDivision($currentExpenseBudget), '$', 1), 295, 429 - 1, 68, 12, 'right', '');
$pdf->showBoxed(toMoney(thousandDivision($expenseCurrentDollar), '$', 1), 367, 429 - 1, 68, 12, 'right', '');
$pdf->showBoxed(toMoney($expenseCurrentPercent, '', 1) . ' %', 439, 429, 68, 12, 'right', '');
$pdf->showBoxed(toMoney(thousandDivision($YTDExpenseActual), '$', 1), 509, 429 - 1, 70, 12, 'right', '');
$pdf->showBoxed(toMoney(thousandDivision($YTDExpenseBudget), '$', 1), 580, 429 - 1, 70, 12, 'right', '');
$pdf->showBoxed(toMoney(thousandDivision($expenseYTDDollar), '$', 1), 655, 429 - 1, 68, 12, 'right', '');
$pdf->showBoxed(toMoney($expenseYTDPercent, '', 1) . ' %', 727, 429 - 1, 68, 12, 'right', '');

$pdf->showBoxed(toMoney(thousandDivision($netIncomeCurrentActual), '$', 1), 223, 414 - 1, 68, 12, 'right', '');
$pdf->showBoxed(toMoney(thousandDivision($netIncomeCurrentBudget), '$', 1), 295, 414 - 1, 68, 12, 'right', '');
$pdf->showBoxed(toMoney(thousandDivision($netCurrentDollar), '$', 1), 367, 414 - 1, 68, 12, 'right', '');
$pdf->showBoxed(toMoney($netCurrentPercent, '', 1) . ' %', 439, 414 - 1, 68, 12, 'right', '');
$pdf->showBoxed(toMoney(thousandDivision($netIncomeYTDActual), '$', 1), 509, 414 - 1, 70, 12, 'right', '');
$pdf->showBoxed(toMoney(thousandDivision($netIncomeYTDBudget), '$', 1), 580, 414 - 1, 70, 12, 'right', '');
$pdf->showBoxed(toMoney(thousandDivision($netYTDDollar), '$', 1), 655, 414 - 1, 68, 12, 'right', '');
$pdf->showBoxed(toMoney($netYTDPercent, '', 1) . ' %', 727, 414 - 1, 68, 12, 'right', '');

// vertical line
$pdf->setlinewidth(0.5);
$pdf->moveto(100, 410);
$pdf->lineto(100, 485);
$pdf->stroke();

$pdf->setlinewidth(0.5);
$pdf->moveto(220, 410);
$pdf->lineto(220, 485);
$pdf->stroke();

$pdf->setlinewidth(0.5);
$pdf->moveto(292, 410);
$pdf->lineto(292, 470);
$pdf->stroke();

$pdf->setlinewidth(0.5);
$pdf->moveto(364, 410);
$pdf->lineto(364, 470);
$pdf->stroke();

$pdf->setlinewidth(0.5);
$pdf->moveto(436, 410);
$pdf->lineto(436, 470);
$pdf->stroke();

$pdf->setlinewidth(0.5);
$pdf->moveto(508, 410);
$pdf->lineto(508, 485);
$pdf->stroke();

$pdf->setlinewidth(0.5);
$pdf->moveto(580, 410);
$pdf->lineto(580, 470);
$pdf->stroke();

$pdf->setlinewidth(0.5);
$pdf->moveto(652, 410);
$pdf->lineto(652, 470);
$pdf->stroke();

$pdf->setlinewidth(0.5);
$pdf->moveto(724, 410);
$pdf->lineto(724, 470);
$pdf->stroke();


// ARREARS
$pdf->setColorExt('fill', 'rgb', 1, 1, 1, 1);
$pdf->rect(40, 330, 612, 60);
$pdf->fill();

$pdf->setColorExt('fill', 'rgb', 0.93, 0.49, 0.19, 0);
$pdf->rect(40, 330, 60, 60);
$pdf->fill();

$pdf->setColorExt('fill', 'rgb', 0.97, 0.8, 0.68, 0);
$pdf->rect(100, 375, 552, 15);
$pdf->fill();

$pdf->setlinewidth(0.5);
$pdf->moveto(40, 390);
$pdf->lineto(652, 390);
$pdf->stroke();

$pdf->setlinewidth(0.5);
$pdf->moveto(40, 330);
$pdf->lineto(652, 330);
$pdf->stroke();

$pdf->setlinewidth(0.5);
$pdf->moveto(652, 330);
$pdf->lineto(652, 390);
$pdf->stroke();

$pdf->setlinewidth(0.5);
$pdf->moveto(40, 330);
$pdf->lineto(40, 390);
$pdf->stroke();

// horizontal bar
$pdf->setlinewidth(0.5);
$pdf->moveto(100, 375);
$pdf->lineto(652, 375);
$pdf->stroke();


$pdf->setlinewidth(0.5);
$pdf->moveto(100, 360);
$pdf->lineto(652, 360);
$pdf->stroke();

$pdf->setlinewidth(0.5);
$pdf->moveto(100, 345);
$pdf->lineto(652, 345);
$pdf->stroke();

// show label
$pdf->setColorExt('both', 'rgb', 1, 1, 1, 0);
$pdf->setFontExt($_fonts['Panton-Bold'], 13);
$pdf->showBoxed('ARREARS', 40, 355, 62, 14, 'center', '');

$pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
$pdf->setFontExt($_fonts['Panton-Bold'], 11);
$pdf->showBoxed('Monthly billings', 102, 378, 120, 12, 'center', '');
$pdf->showBoxed('Total Arrear', 220, 378, 68, 12, 'center', '');
$pdf->showBoxed('Current', 292, 378, 68, 12, 'center', '');
$pdf->showBoxed('30 days', 364, 378, 68, 12, 'center', '');
$pdf->showBoxed('60 days', 436, 378, 68, 12, 'center', '');
$pdf->showBoxed('90 days', 508, 378, 68, 12, 'center', '');
$pdf->showBoxed('120+ days', 580, 378, 68, 12, 'center', '');

// arrears
$arrears = dbGetArrearsAged($propertyID, $periodTo);
// var_dump($arrears);
// $current = convertDate($periodTo);
// $arrearsCur=[];
// $arrearsCur30=[];
// $arrearsCur60= [];
// $arrearsCur90= [];
// $arrearsCur120= [];
// foreach ($arrears as $key=>$row){
//     $dueDate =  convertDate($row['dueDate']);
//     if (!$dueDate || strtotime($dueDate) > strtotime($current . "-1 months")) {
//         $arrearsCur[] = $row['total'];
//     }
//     else if(strtotime($dueDate) > strtotime($current . "-2 months")){
//         $arrearsCur30[] = $row['total'];
//     }
//     else if(strtotime($dueDate) > strtotime($current . "-3 months")){
//         $arrearsCur60[] = $row['total'];
//     }
//     else if(strtotime($dueDate) > strtotime($current . "-4 months")){
//         $arrearsCur90[] = $row['total'];
//     }
//     else{
//         $arrearsCur120[] = $row['total'];
//     }
// }

$monthlyBilling = dbGetMonthlyBilling($propertyID, $periodFrom, $periodTo);
$currentArrears = $arrears['currentArrears'];
$day30Arrears = $arrears['thirtyArrears'];
$day60Arrears = $arrears['sixtyArrears'];
$day90Arrears = $arrears['ninetyPlusArrears'];
$day120Arrears = $arrears['oneTwentyPlusArrears'];
$totalArrears = $currentArrears + $day30Arrears + $day60Arrears + $day90Arrears + $day120Arrears;
$totalArrearsPercent = $monthlyBilling ? ($totalArrears / $monthlyBilling) * 100 : 0;
$currentArrearsPercent = $monthlyBilling ? ($currentArrears / $monthlyBilling) * 100 : 0;
$day30ArrearsPercent = $monthlyBilling ? ($day30Arrears / $monthlyBilling) * 100 : 0;
$day60ArrearsPercent = $monthlyBilling ? ($day60Arrears / $monthlyBilling) * 100 : 0;
$day90ArrearsPercent = $monthlyBilling ? ($day90Arrears / $monthlyBilling) * 100 : 0;
$day120ArrearsPercent = $monthlyBilling ? ($day120Arrears / $monthlyBilling) * 100 : 0;


$pdf->setFontExt($_fonts['Panton-Light'], 11);
$pdf->showBoxed(toMoney(thousandDivision($monthlyBilling), '$', 1), 102, 364 - 1, 116, 12, 'right', '');
$pdf->showBoxed(toMoney(thousandDivision($totalArrears), '$', 1), 220, 364 - 1, 68, 12, 'right', '');
$pdf->showBoxed(toMoney($totalArrearsPercent, '', 1) . ' %', 220, 349, 68, 12, 'right', '');
$pdf->setFontExt($_fonts['Panton-Bold'], 11);
$pdf->showBoxed('Doubtful Debt provision', 222, 332, 150, 12, 'left', '');

$pdf->setFontExt($_fonts['Panton-Light'], 11);
$pdf->showBoxed(toMoney(thousandDivision($currentArrears), '$', 1), 292, 364 - 1, 68, 12, 'right', '');
$pdf->showBoxed(toMoney($currentArrearsPercent, '', 1) . ' %', 292, 349 - 1, 68, 12, 'right', '');

$arrearComments = dbGetSubReportComment($propertyID, $s['sub_report_id'], $calendar['period'], $calendar['year']);

$pdf->showBoxed(toMoney(thousandDivision($day30Arrears), '$', 1), 364, 364 - 1, 68, 12, 'right', '');
$pdf->showBoxed(toMoney($day30ArrearsPercent, '', 1) . ' %', 364, 349 - 1, 68, 12, 'right', '');
$pdf->showBoxed($arrearComments[1]['comment'], 364, 332 - 1, 68, 12, 'right', '');

$pdf->showBoxed(toMoney(thousandDivision($day60Arrears), '$', 1), 436, 364, 68, 12, 'right', '');
$pdf->showBoxed(toMoney($day60ArrearsPercent, '', 1) . ' %', 436, 349 - 1, 68, 12, 'right', '');
$pdf->setFontExt($_fonts['Panton-Bold'], 11);
$pdf->showBoxed('Amount not collectable', 438, 332, 150, 12, 'left', '');
$pdf->setFontExt($_fonts['Panton-Light'], 11);
$pdf->showBoxed(toMoney(thousandDivision($day90Arrears), '$', 1), 508, 364 - 1, 68, 12, 'right', '');
$pdf->showBoxed(toMoney($day90ArrearsPercent, '', 1) . ' %', 508, 349 - 1, 68, 12, 'right', '');

$pdf->showBoxed(toMoney(thousandDivision($day120Arrears), '$', 1), 580, 364 - 1, 68, 12, 'right', '');
$pdf->showBoxed(toMoney($day120ArrearsPercent, '', 1) . ' %', 580, 349 - 1, 68, 12, 'right', '');
$pdf->showBoxed($arrearComments[2]['comment'], 580, 332 - 1, 68, 12, 'right', '');

// vertical line
$pdf->setlinewidth(0.5);
$pdf->moveto(100, 330);
$pdf->lineto(100, 390);
$pdf->stroke();

$pdf->setlinewidth(0.5);
$pdf->moveto(220, 330);
$pdf->lineto(220, 390);
$pdf->stroke();

$pdf->setlinewidth(0.5);
$pdf->moveto(292, 345);
$pdf->lineto(292, 390);
$pdf->stroke();

$pdf->setlinewidth(0.5);
$pdf->moveto(364, 330);
$pdf->lineto(364, 390);
$pdf->stroke();

$pdf->setlinewidth(0.5);
$pdf->moveto(436, 330);
$pdf->lineto(436, 390);
$pdf->stroke();

$pdf->setlinewidth(0.5);
$pdf->moveto(508, 345);
$pdf->lineto(508, 390);
$pdf->stroke();

$pdf->setlinewidth(0.5);
$pdf->moveto(580, 330);
$pdf->lineto(580, 390);
$pdf->stroke();


// VACANCY
$pdf->setColorExt('fill', 'rgb', 1, 1, 1, 1);
$pdf->rect(40, 235, 252, 75);
$pdf->fill();

$pdf->setColorExt('fill', 'rgb', 0.44, 0.68, 0.28, 0);
$pdf->rect(40, 235, 60, 75);
$pdf->fill();

$pdf->setlinewidth(0.5);
$pdf->moveto(40, 235);
$pdf->lineto(292, 235);
$pdf->stroke();

$pdf->setlinewidth(0.5);
$pdf->moveto(40, 310);
$pdf->lineto(292, 310);
$pdf->stroke();

$pdf->setlinewidth(0.5);
$pdf->moveto(292, 310);
$pdf->lineto(292, 235);
$pdf->stroke();

$pdf->setlinewidth(0.5);
$pdf->moveto(40, 310);
$pdf->lineto(40, 235);
$pdf->stroke();

// horizontal bar
$pdf->setlinewidth(0.5);
$pdf->moveto(100, 295);
$pdf->lineto(292, 295);
$pdf->stroke();

$pdf->setlinewidth(0.5);
$pdf->moveto(100, 280);
$pdf->lineto(292, 280);
$pdf->stroke();

$pdf->setlinewidth(0.5);
$pdf->moveto(100, 265);
$pdf->lineto(292, 265);
$pdf->stroke();

$pdf->setlinewidth(0.5);
$pdf->moveto(100, 250);
$pdf->lineto(292, 250);
$pdf->stroke();

// show boxed
$pdf->setColorExt('both', 'rgb', 1, 1, 1, 0);
$pdf->setFontExt($_fonts['Panton-Bold'], 13);
$pdf->showBoxed('VACANCY', 40, 265, 62, 14, 'center', '');

$pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
$pdf->setFontExt($_fonts['Panton-Bold'], 11);
$pdf->showBoxed('Number of Vacancy', 102, 298, 120, 12, 'left', '');
$pdf->showBoxed('Vacant Area GLA', 102, 283, 120, 12, 'left', '');
$pdf->showBoxed('Centre GLA', 102, 268, 120, 12, 'left', '');
$pdf->showBoxed('Leased GLA %', 102, 253, 120, 12, 'left', '');
$pdf->showBoxed('Vacant GLA %', 102, 238, 120, 12, 'left', '');

$numberOfVacancy = dbGetNumberOfVacancy($propertyID, $periodTo);
$vacantArea = dbGetTotalArea($propertyID, $periodTo, 1);
$centreGLA = dbGetTotalArea($propertyID, $periodTo, 2);
$vacantGLA = ($vacantArea / $centreGLA) * 100;
$leasedGLA = 100 - $vacantGLA;

$pdf->setFontExt($_fonts['Panton-Light'], 11);
$pdf->showBoxed(round($numberOfVacancy), 220, 298 - 1, 68, 12, 'right', '');
$pdf->showBoxed(round($vacantArea), 220, 283 - 1, 68, 12, 'right', '');
$pdf->showBoxed(round($centreGLA), 220, 268 - 1, 68, 12, 'right', '');
$pdf->showBoxed(toMoney($leasedGLA, '', 2) . ' %', 220, 253 - 1, 68, 12, 'right', '');
$pdf->showBoxed(toMoney($vacantGLA, '', 2) . ' %', 220, 238 - 1, 68, 12, 'right', '');

// vertical line
$pdf->setlinewidth(0.5);
$pdf->moveto(100, 235);
$pdf->lineto(100, 310);
$pdf->stroke();


$pdf->setlinewidth(0.5);
$pdf->moveto(220, 235);
$pdf->lineto(220, 310);
$pdf->stroke();


$trafficLine = 215;

// SALES
if ($calendar['period'] == 1) {
    $calendarSales['year'] = $calendar['year'] - 1;
    $calendarSales['period'] = 12;
} else {
    $calendarSales['period'] = $calendar['period'] - 1;
    $calendarSales['year'] = $calendar['year'];
}

$sales = dbgetSalesMatSummary($propertyID, $calendarSales['period'], $calendarSales['year'], $periodTo);
$monthYear = date('M-y', strtotime(convertDate($periodFrom) . '- 1 day'));
$previousYear = date('M-y', strtotime(convertDate($periodFrom) . '-1 year') - 86400);

if (! empty($sales)) {
    $pdf->setColorExt('fill', 'rgb', 1, 1, 1, 1);
    $pdf->rect(40, (215 - (15 * (3 + count($sales)))), 612, (15 * (3 + count($sales))));
    $pdf->fill();

    $pdf->setColorExt('fill', 'rgb', 0.36, 0.61, 0.84, 0);
    $pdf->rect(40, (215 - (15 * (3 + count($sales)))), 60, (15 * (3 + count($sales))));
    $pdf->fill();

    $pdf->setColorExt('fill', 'rgb', 0.74, 0.84, 0.93, 0);
    $pdf->rect(220, 200, 216, 15);
    $pdf->fill();

    $pdf->setColorExt('fill', 'rgb', 0.36, 0.61, 0.84, 0);
    $pdf->rect(436, 200, 216, 15);
    $pdf->fill();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(40, 215);
    $pdf->lineto(652, 215);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(40, (215 - (15 * (3 + count($sales)))));
    $pdf->lineto(652, (215 - (15 * (3 + count($sales)))));
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(652, (215 - (15 * (3 + count($sales)))));
    $pdf->lineto(652, 215);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(40, (215 - (15 * (3 + count($sales)))));
    $pdf->lineto(40, 215);
    $pdf->stroke();

    // horizontal bar
    $pdf->setlinewidth(0.5);
    $pdf->moveto(100, 200);
    $pdf->lineto(652, 200);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(100, 185);
    $pdf->lineto(652, 185);
    $pdf->stroke();

    $horizontalLine = 170;
    foreach ($sales as $row) {
        $pdf->setlinewidth(0.5);
        $pdf->moveto(100, $horizontalLine);
        $pdf->lineto(652, $horizontalLine);
        $pdf->stroke();
        $horizontalLine -= 15;
    }

    // vertical line
    $pdf->setlinewidth(0.5);
    $pdf->moveto(100, (215 - (15 * (3 + count($sales)))));
    $pdf->lineto(100, 215);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(220, (215 - (15 * (3 + count($sales)))));
    $pdf->lineto(220, 215);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(292, (215 - (15 * (3 + count($sales)))));
    $pdf->lineto(292, 200);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(364, (215 - (15 * (3 + count($sales)))));
    $pdf->lineto(364, 200);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(436, (215 - (15 * (3 + count($sales)))));
    $pdf->lineto(436, 215);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(508, (215 - (15 * (3 + count($sales)))));
    $pdf->lineto(508, 200);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(580, (215 - (15 * (3 + count($sales)))));
    $pdf->lineto(580, 200);
    $pdf->stroke();


    // show box
    $pdf->setColorExt('both', 'rgb', 1, 1, 1, 0);
    $pdf->setFontExt($_fonts['Panton-Bold'], 13);
    $pdf->showBoxed('SALES', 40, (215 - ((15 * (3 + count($sales))) / 2)), 62, 15, 'center', '');

    $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
    $pdf->setFontExt($_fonts['Panton-Bold'], 11);
    $pdf->showBoxed("Month '000's", 220, 202, 189, 12, 'center', '');
    $pdf->setColorExt('both', 'rgb', 1, 1, 1, 0);
    $pdf->showBoxed('MAT TY', 436, 202, 189, 12, 'center', '');
    $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
    $pdf->showBoxed($monthYear, 220, 187, 68, 12, 'center', '');
    $pdf->showBoxed($previousYear, 292, 187, 68, 12, 'center', '');
    $pdf->showBoxed('% Var', 364, 187, 68, 12, 'center', '');
    $pdf->showBoxed($monthYear, 436, 187, 68, 12, 'center', '');
    $pdf->showBoxed($previousYear, 508, 187, 68, 12, 'center', '');
    $pdf->showBoxed('% Var', 580, 187, 68, 12, 'center', '');

    $horizontalLine = 172;
    $MonthTY = 0;
    $MatTY = 0;
    $MonthLY = 0;
    $MatLY = 0;
    foreach ($sales as $row) {
        $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        $pdf->setFontExt($_fonts['Panton-Bold'], 11);
        $pdf->showBoxed($row['categoryName'], 102, $horizontalLine, 120, 12, 'left', '');

        $pdf->setFontExt($_fonts['Panton-Light'], 11);
        $monthlyMajorVariance = calculateVariancePercentage($row['MonthTY'], $row['MonthLY']);
        $YTDMajorVariance = calculateVariancePercentage($row['MatTY'], $row['MatLY']);
        $pdf->showBoxed(toMoney(thousandDivision($row['MonthTY']), '$', 1), 220, $horizontalLine, 68, 12, 'right', '');
        $pdf->showBoxed(toMoney(thousandDivision($row['MonthLY']), '$', 1), 292, $horizontalLine, 68, 12, 'right', '');
        $pdf->showBoxed(toMoney($monthlyMajorVariance, '', 1) . ' %', 364, $horizontalLine, 68, 12, 'right', '');
        $pdf->showBoxed(toMoney(thousandDivision($row['MatTY']), '$', 1), 436, $horizontalLine, 68, 12, 'right', '');
        $pdf->showBoxed(toMoney(thousandDivision($row['MatLY']), '$', 1), 508, $horizontalLine, 68, 12, 'right', '');
        $pdf->showBoxed(toMoney($YTDMajorVariance, '', 1) . ' %', 580, $horizontalLine, 68, 12, 'right', '');

        $horizontalLine -= 15;
        $MonthTY += $row['MonthTY'];
        $MonthLY += $row['MonthLY'];
        $MatTY += $row['MatTY'];
        $MatLY += $row['MatLY'];
    }

    $pdf->setFontExt($_fonts['Panton-Bold'], 11);
    $pdf->showBoxed('Total', 102, $horizontalLine, 120, 12, 'left', '');
    $monthlyMajorVariance = calculateVariancePercentage($MonthTY, $MonthLY);
    $YTDMajorVariance = calculateVariancePercentage($MatTY, $MatLY);
    $pdf->showBoxed(toMoney(thousandDivision($MonthTY), '$', 1), 220, $horizontalLine, 68, 12, 'right', '');
    $pdf->showBoxed(toMoney(thousandDivision($MonthLY), '$', 1), 292, $horizontalLine, 68, 12, 'right', '');
    $pdf->showBoxed(toMoney($monthlyMajorVariance, '', 1) . ' %', 364, $horizontalLine, 68, 12, 'right', '');
    $pdf->showBoxed(toMoney(thousandDivision($MatTY), '$', 1), 436, $horizontalLine, 68, 12, 'right', '');
    $pdf->showBoxed(toMoney(thousandDivision($MatLY), '$', 1), 508, $horizontalLine, 68, 12, 'right', '');
    $pdf->showBoxed(toMoney($YTDMajorVariance, '', 1) . ' %', 580, $horizontalLine, 68, 12, 'right', '');

    $trafficLine = $horizontalLine - 15;
}

// TRAFFIC
$currentStartMonth = date('01/m/Y', strtotime(convertDate($periodFrom) . '-1 day'));
$currentStartMonth2 = convertDate($currentStartMonth);
$currentEndMonth = date('t/m/Y', strtotime(convertDate($periodFrom) . '-1 day'));
$currentStartYear = date('01/m/Y', strtotime($currentStartMonth2 . '-11 months'));
$previousStartMonth = date('01/m/Y', strtotime($currentStartMonth2 . '-12 months'));
$previousStartMonth2 = date('m/01/Y', strtotime($currentStartMonth2 . '-12 months'));
$previousEndMonth = date('t/m/Y', strtotime($currentStartMonth2 . '-12 months'));
$previousStartYear = date('01/m/Y', strtotime($previousStartMonth2 . '-11 months'));

$currentMonthTraffic = dbGetDoorsCount($propertyID, $currentStartMonth, $currentEndMonth);
$previousMonthTraffic = dbGetDoorsCount($propertyID, $previousStartMonth, $previousEndMonth);
$monthlyVariance = calculateVariancePercentage(thousandDivision($currentMonthTraffic), thousandDivision($previousMonthTraffic));

$currentYearTraffic = dbGetDoorsCount($propertyID, $currentStartYear, $currentEndMonth);
$previousYearTraffic = dbGetDoorsCount($propertyID, $previousStartYear, $previousEndMonth);
$yearlyVariance = calculateVariancePercentage(($currentYearTraffic), ($previousYearTraffic));

$currentMonthVisit = ($currentMonthTraffic ? ($MonthTY) / $currentMonthTraffic : 0);
$previousMonthVisit = $previousMonthTraffic ? (($MonthLY) / $previousMonthTraffic) : 0;
$visitMonthVariance =  calculateVariancePercentage(($currentMonthVisit), ($previousMonthVisit));

$currentMatVisit = ($MatTY) / $currentYearTraffic;
$previousMatVisit = ($MatLY) / $previousYearTraffic;
$visitMatVariance =  calculateVariancePercentage(($currentMatVisit), ($previousMatVisit));

if ($currentYearTraffic != 0 || $previousYearTraffic != 0) {
    $pdf->setColorExt('fill', 'rgb', 1, 1, 1, 1);
    $pdf->rect(40, $trafficLine - 60, 612, 60);
    $pdf->fill();

    $pdf->setColorExt('fill', 'rgb', 0.65, 0.65, 0.65, 0);
    $pdf->rect(40, $trafficLine - 60, 60, 60);
    $pdf->fill();

    $pdf->setColorExt('fill', 'rgb', 0.86, 0.86, 0.86, 0);
    $pdf->rect(220, $trafficLine - 15, 216, 15);
    $pdf->fill();

    $pdf->setColorExt('fill', 'rgb', 0.65, 0.65, 0.65, 0);
    $pdf->rect(436, $trafficLine - 15, 216, 15);
    $pdf->fill();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(40, $trafficLine);
    $pdf->lineto(652, $trafficLine);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(40, $trafficLine - 60);
    $pdf->lineto(652, $trafficLine - 60);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(652, $trafficLine - 60);
    $pdf->lineto(652, $trafficLine);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(40, $trafficLine - 60);
    $pdf->lineto(40, $trafficLine);
    $pdf->stroke();

    // horizontal bar
    $pdf->setlinewidth(0.5);
    $pdf->moveto(100, $trafficLine - 15);
    $pdf->lineto(652, $trafficLine - 15);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(100, $trafficLine - 30);
    $pdf->lineto(652, $trafficLine - 30);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(100, $trafficLine - 45);
    $pdf->lineto(652, $trafficLine - 45);
    $pdf->stroke();

    // show box
    $pdf->setColorExt('both', 'rgb', 1, 1, 1, 0);
    $pdf->setFontExt($_fonts['Panton-Bold'], 13);
    $pdf->showBoxed('TRAFFIC', 40, $trafficLine - 35, 62, 14, 'center', '');

    $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
    $pdf->setFontExt($_fonts['Panton-Bold'], 11);
    $pdf->showBoxed('Traffic', 102, $trafficLine - 42, 120, 12, 'left', '');
    $pdf->showBoxed('Spend / Visit', 102, $trafficLine - 57, 120, 12, 'left', '');
    $pdf->showBoxed("Month '000's", 220, $trafficLine - 12, 189, 12, 'center', '');
    $pdf->setColorExt('both', 'rgb', 1, 1, 1, 0);
    $pdf->showBoxed('MAT TY', 436, $trafficLine - 12, 189, 12, 'center', '');
    $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
    $pdf->showBoxed($monthYear, 220, $trafficLine - 27, 68, 12, 'center', '');
    $pdf->showBoxed($previousYear, 292, $trafficLine - 27, 68, 12, 'center', '');
    $pdf->showBoxed('% Var', 364, $trafficLine - 27, 68, 12, 'center', '');
    $pdf->showBoxed($monthYear, 436, $trafficLine - 27, 68, 12, 'center', '');
    $pdf->showBoxed($previousYear, 508, $trafficLine - 27, 68, 12, 'center', '');
    $pdf->showBoxed('% Var', 580, $trafficLine - 27, 68, 12, 'center', '');



    $pdf->setFontExt($_fonts['Panton-Light'], 11);
    $pdf->showBoxed(toMoney(thousandDivision($currentMonthTraffic), '', 1), 220, $trafficLine - 42, 68, 12, 'right', '');
    $pdf->showBoxed(toMoney(thousandDivision($previousMonthTraffic), '', 1), 292, $trafficLine - 42, 68, 12, 'right', '');
    $pdf->showBoxed(toMoney($monthlyVariance, '', 1) . ' %', 364, $trafficLine - 42, 68, 12, 'right', '');

    $pdf->showBoxed(toMoney(($currentMonthVisit), '', 1), 220, $trafficLine - 57, 68, 12, 'right', '');
    $pdf->showBoxed(toMoney(($previousMonthVisit), '', 1), 292, $trafficLine - 57, 68, 12, 'right', '');
    $pdf->showBoxed(toMoney($visitMonthVariance, '', 1) . ' %', 364, $trafficLine - 57, 68, 12, 'right', '');

    $pdf->showBoxed(toMoney(thousandDivision($currentYearTraffic), '', 1), 436, $trafficLine - 42, 68, 12, 'right', '');
    $pdf->showBoxed(toMoney($currentMatVisit, '$', 1), 436, $trafficLine - 57, 68, 12, 'right', '');

    $pdf->showBoxed(toMoney(thousandDivision($previousYearTraffic), '', 1), 508, $trafficLine - 42, 68, 12, 'right', '');
    $pdf->showBoxed(toMoney($previousMatVisit, '$', 1), 508, $trafficLine - 57, 68, 12, 'right', '');

    $pdf->showBoxed(toMoney($yearlyVariance, '', 1) . ' %', 580, $trafficLine - 42, 68, 12, 'right', '');
    $pdf->showBoxed(toMoney($visitMatVariance, '', 1) . ' %', 580, $trafficLine - 57, 68, 12, 'right', '');

    // vertical line
    $pdf->setlinewidth(0.5);
    $pdf->moveto(100, $trafficLine - 60);
    $pdf->lineto(100, $trafficLine);
    $pdf->stroke();


    $pdf->setlinewidth(0.5);
    $pdf->moveto(220, $trafficLine - 60);
    $pdf->lineto(220, $trafficLine);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(292, $trafficLine - 60);
    $pdf->lineto(292, $trafficLine - 15);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(364, $trafficLine - 60);
    $pdf->lineto(364, $trafficLine - 15);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(436, $trafficLine - 60);
    $pdf->lineto(436, $trafficLine);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(508, $trafficLine - 60);
    $pdf->lineto(508, $trafficLine - 15);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(580, $trafficLine - 60);
    $pdf->lineto(580, $trafficLine - 15);
    $pdf->stroke();
}

// $traccFooter = new TraccFooter("assets/clientLogos/tracc_logo_footer.jpg", 'simpleOwnersReport', A4_LANDSCAPE);
// $traccFooter->prerender($pdf);
$pdf->end_page_ext('');

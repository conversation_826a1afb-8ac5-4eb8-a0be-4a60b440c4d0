<?php

include_once __DIR__ . '/fontFunction.php';
include_once __DIR__ . '/LeaseEquityFunction.php';

$page++;
$pdf->begin_page_ext(842, 595, '');
$pdf->setColorExt('fill', 'rgb', 0.91, 0.91, 0.91, 0);
$pdf->rect(20, 505, 802, 70);
$pdf->fill();

$pdf->setColorExt('fill', 'rgb', 0.91, 0.91, 0.91, 0);
$pdf->rect(20, 20, 802, 475);
$pdf->fill();

renderLogoPage($pdf);

$pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
$pdf->setFontExt($_fonts['Panton-Bold'], 18);
$contentPageNo = contentPage($propertyID, $calendar['period'], $calendar['year'], $reportType, 'propertyReport/Lease_Equity/financialSummaryAccountsAccrual.php');
$pdf->showBoxed($contentPageNo . ' FINANCIAL PERFORMANCE', 50, 535, 400, 18, 'left', '');


$pdf->setColorExt('fill', 'rgb', 0.22, 0.59, 0.86, 1);
$pdf->rect(25, 400, 792, 30);
$pdf->fill();

// above lines
$pdf->setColorExt('stroke', 'rgb', 1, 1, 1, 0);
$pdf->setlinewidth(0.5);
$pdf->moveto(25, 400);
$pdf->lineto(817, 400);
$pdf->stroke();

$pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
$pdf->setFontExt($_fonts['Panton-Bold'], 16);
$pdf->showBoxed(((float) $contentPageNo + 0.1) . ' Financial Summary', 25, 437, 400, 18, 'left', '');

// DATA START
$pdf->setFontExt($_fonts['Panton-Bold'], 12);
$pdf->setColorExt('both', 'rgb', 1, 1, 1, 0);
$pdf->showBoxed('', 25, 407, 114, 12, 'center', '');
$pdf->showBoxed('Month Actual', 139, 407, 114, 12, 'center', '');
$pdf->showBoxed('Month Budget', 252, 407, 113, 12, 'center', '');
$pdf->showBoxed('Variance', 365, 407, 113, 12, 'center', '');
$pdf->showBoxed('YTD Actual', 478, 407, 113, 12, 'center', '');
$pdf->showBoxed('YTD Budget', 591, 407, 113, 12, 'center', '');
$pdf->showBoxed('Variance', 704, 407, 113, 12, 'center', '');

$pdf->setColorExt('fill', 'rgb', 1, 1, 1, 1);
$pdf->rect(25, 397, 792, 3);
$pdf->fill();

$pdf->setFontExt($_fonts['Panton-Light'], 11);
$Comments = dbGetSubReportComment($propertyID, $s['sub_report_id'], $calendar['period'], $calendar['year']);
$line = 373;
// SET ALL THE VARIABLES HERE
$cal = dbGetCalendarForProperty($propertyID);
$calendarName = $cal['calendarName'];
$calendar = dbGetMasterCalendarPeriodForDate($calendarName, $periodTo);
$calendarYear = dbGetCalendarPeriods($propertyID, $calendar['year']);

$rentalActual = dbGetFinancialActualIncome($propertyID, 'INC.OWN', $calendar['period'], $calendar['period'], $calendar['year'], false, 'balanceAccruals');
$rentalBudget = dbGetFinancialBudgetIncome($propertyID, 'INC.OWN', $calendar['year'], $calendar['period'], $calendar['period'], false, 'pmrp_b_a_amt');
$rentalActualYTD = dbGetFinancialActualIncome($propertyID, 'INC.OWN', 1, $calendar['period'], $calendar['year'], false, 'balanceAccruals');
$rentalBudgetYTD = dbGetFinancialBudgetIncome($propertyID, 'INC.OWN', $calendar['year'], 1, $calendar['period'], false, 'pmrp_b_a_amt');

if ($rentalActual['amount'] || $rentalBudget['amount'] || $rentalActualYTD['amount'] || $rentalBudgetYTD['amount']) {
    $pdf->setColorExt('fill', 'rgb', 0.81, 0.87, 0.95, 1);
    $pdf->rect(25, $line, 792, 24);
    $pdf->fill();
    $pdf->setlinewidth(0.5);
    $pdf->moveto(25, $line);
    $pdf->lineto(817, $line);
    $pdf->stroke();

    $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
    $pdf->showBoxed('Rental Income', 27, $line - 4, 113, 24, 'left', '');
    $pdf->showBoxed(toMoney($rentalActual['amount']), 139, $line - 4, 112, 24, 'right', '');
    $pdf->showBoxed(toMoney($rentalBudget['amount']), 252, $line - 4, 111, 24, 'right', '');
    $pdf->showBoxed(toMoney($rentalActual['amount'] - $rentalBudget['amount']), 365, $line - 4, 111, 24, 'right', '');
    $pdf->showBoxed(toMoney($rentalActualYTD['amount']), 478, $line - 4, 111, 24, 'right', '');
    $pdf->showBoxed(toMoney($rentalBudgetYTD['amount']), 591, $line - 4, 111, 24, 'right', '');
    $pdf->showBoxed(toMoney($rentalActualYTD['amount'] - $rentalBudgetYTD['amount']), 704, $line - 4, 111, 24, 'right', '');

    $rentalActualAcc = dbGetFinancialActualIncomeAccounts($propertyID, 'INC.OWN', $calendar['period'], $calendar['period'], $calendar['year'], false, 'balanceAccruals');
    $rentalBudgetAcc = dbGetFinancialBudgetIncomeAccounts($propertyID, 'INC.OWN', $calendar['year'], $calendar['period'], $calendar['period'], false, 'pmrp_b_a_amt');
    $rentalActualYTDAcc = dbGetFinancialActualIncomeAccounts($propertyID, 'INC.OWN', 1, $calendar['period'], $calendar['year'], false, 'balanceAccruals');
    $rentalBudgetYTDAcc = dbGetFinancialBudgetIncomeAccounts($propertyID, 'INC.OWN', $calendar['year'], 1, $calendar['period'], false, 'pmrp_b_a_amt');

    $subGroups = [];
    foreach ($rentalActualAcc as $rs) {
        $subGroups[ucfirst(strtolower($rs['description']))]['actual'] = $rs['amount'];
    }

    foreach ($rentalActualYTDAcc as $rs) {
        $subGroups[ucfirst(strtolower($rs['description']))]['actuaYTD'] = $rs['amount'];
    }

    foreach ($rentalBudgetAcc as $rs) {
        $subGroups[ucfirst(strtolower($rs['description']))]['budget'] = $rs['amount'];
    }

    foreach ($rentalBudgetYTDAcc as $rs) {
        $subGroups[ucfirst(strtolower($rs['description']))]['budgetYTD'] = $rs['amount'];
    }

    foreach ($subGroups as $subGroup => $amount) {

        if ($amount['amount'] != 0 || $amount['budget'] != 0 || $amount['actuaYTD'] != 0 || $amount['budgetYTD'] != 0) {
            if ($line < 50) {
                renderNewFinancialSummarySubGroupPage($pdf, $line, $contentPageNo);
                $line = 397;
            }

            $pdf->setFontExt($_fonts['Panton-Light'], 9);
            $line -= 24;
            $pdf->setColorExt('fill', 'rgb', 0.91, 0.94, 0.97, 1);
            $pdf->rect(25, $line, 792, 24);
            $pdf->fill();
            $pdf->setlinewidth(0.5);
            $pdf->moveto(25, $line);
            $pdf->lineto(817, $line);
            $pdf->stroke();

            $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
            $pdf->showBoxed('   ' . $subGroup, 27, $line - 4, 113, 24, 'left', '');
            $pdf->showBoxed(toMoney($amount['actual']), 139, $line - 4, 112, 24, 'right', '');
            $pdf->showBoxed(toMoney($amount['budget']), 252, $line - 4, 111, 24, 'right', '');
            $pdf->showBoxed(toMoney($amount['actual'] - $amount['budget']), 365, $line - 4, 111, 24, 'right', '');
            $pdf->showBoxed(toMoney($amount['actuaYTD']), 478, $line - 4, 111, 24, 'right', '');
            $pdf->showBoxed(toMoney($amount['budgetYTD']), 591, $line - 4, 111, 24, 'right', '');
            $pdf->showBoxed(toMoney($amount['actuaYTD'] - $amount['budgetYTD']), 704, $line - 4, 111, 24, 'right', '');
        }

    }
}

$outgoingActual = dbGetFinancialActualIncome($propertyID, 'INC.OUT', $calendar['period'], $calendar['period'], $calendar['year'], false, 'balanceAccruals');
$outgoingBudget = dbGetFinancialBudgetIncome($propertyID, 'INC.OUT', $calendar['year'], $calendar['period'], $calendar['period'], false, 'pmrp_b_a_amt');
$outgoingActualYTD = dbGetFinancialActualIncome($propertyID, 'INC.OUT', 1, $calendar['period'], $calendar['year'], false, 'balanceAccruals');
$outgoingBudgetYTD = dbGetFinancialBudgetIncome($propertyID, 'INC.OUT', $calendar['year'], 1, $calendar['period'], false, 'pmrp_b_a_amt');

if ($outgoingActual['amount'] || $outgoingBudget['amount'] || $outgoingActualYTD['amount'] || $outgoingBudgetYTD['amount']) {

    $pdf->setFontExt($_fonts['Panton-Light'], 11);
    $line -= 24;
    $pdf->setColorExt('fill', 'rgb', 0.81, 0.87, 0.95, 1);
    $pdf->rect(25, $line, 792, 24);
    $pdf->fill();
    $pdf->setlinewidth(0.5);
    $pdf->moveto(25, $line);
    $pdf->lineto(817, $line);
    $pdf->stroke();

    $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
    $pdf->showBoxed('Outgoings Income', 27, $line - 4, 113, 24, 'left', '');
    $pdf->showBoxed(toMoney($outgoingActual['amount']), 139, $line - 4, 112, 24, 'right', '');
    $pdf->showBoxed(toMoney($outgoingBudget['amount']), 252, $line - 4, 111, 24, 'right', '');
    $pdf->showBoxed(toMoney($outgoingActual['amount'] - $outgoingBudget['amount']), 365, $line - 4, 111, 24, 'right', '');
    $pdf->showBoxed(toMoney($outgoingActualYTD['amount']), 478, $line - 4, 111, 24, 'right', '');
    $pdf->showBoxed(toMoney($outgoingBudgetYTD['amount']), 591, $line - 4, 111, 24, 'right', '');
    $pdf->showBoxed(toMoney($outgoingActualYTD['amount'] - $outgoingBudgetYTD['amount']), 704, $line - 4, 111, 24, 'right', '');

    $outgoingActualAcc = dbGetFinancialActualIncomeAccounts($propertyID, 'INC.OUT', $calendar['period'], $calendar['period'], $calendar['year'], false, 'balanceAccruals');
    $outgoingBudgetAcc = dbGetFinancialBudgetIncomeAccounts($propertyID, 'INC.OUT', $calendar['year'], $calendar['period'], $calendar['period'], false, 'pmrp_b_a_amt');
    $outgoingActualYTDAcc = dbGetFinancialActualIncomeAccounts($propertyID, 'INC.OUT', 1, $calendar['period'], $calendar['year'], false, 'balanceAccruals');
    $outgoingBudgetYTDAcc = dbGetFinancialBudgetIncomeAccounts($propertyID, 'INC.OUT', $calendar['year'], 1, $calendar['period'], false, 'pmrp_b_a_amt');

    $subGroups = [];
    foreach ($outgoingActualAcc as $rs) {
        $subGroups[ucfirst(strtolower($rs['description']))]['actual'] = $rs['amount'];
    }

    foreach ($outgoingActualYTDAcc as $rs) {
        $subGroups[ucfirst(strtolower($rs['description']))]['actuaYTD'] = $rs['amount'];
    }

    foreach ($outgoingBudgetAcc as $rs) {
        $subGroups[ucfirst(strtolower($rs['description']))]['budget'] = $rs['amount'];
    }

    foreach ($outgoingBudgetYTDAcc as $rs) {
        $subGroups[ucfirst(strtolower($rs['description']))]['budgetYTD'] = $rs['amount'];
    }

    foreach ($subGroups as $subGroup => $amount) {

        if ($amount['amount'] != 0 || $amount['budget'] != 0 || $amount['actuaYTD'] != 0 || $amount['budgetYTD'] != 0) {
            if ($line < 50) {
                renderNewFinancialSummarySubGroupPage($pdf, $line, $contentPageNo);
                $line = 397;
            }

            $pdf->setFontExt($_fonts['Panton-Light'], 9);
            $line -= 24;
            $pdf->setColorExt('fill', 'rgb', 0.91, 0.94, 0.97, 1);
            $pdf->rect(25, $line, 792, 24);
            $pdf->fill();
            $pdf->setlinewidth(0.5);
            $pdf->moveto(25, $line);
            $pdf->lineto(817, $line);
            $pdf->stroke();

            $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
            $pdf->showBoxed('   ' . $subGroup, 27, $line - 4, 113, 24, 'left', '');
            $pdf->showBoxed(toMoney($amount['actual']), 139, $line - 4, 112, 24, 'right', '');
            $pdf->showBoxed(toMoney($amount['budget']), 252, $line - 4, 111, 24, 'right', '');
            $pdf->showBoxed(toMoney($amount['actual'] - $amount['budget']), 365, $line - 4, 111, 24, 'right', '');
            $pdf->showBoxed(toMoney($amount['actuaYTD']), 478, $line - 4, 111, 24, 'right', '');
            $pdf->showBoxed(toMoney($amount['budgetYTD']), 591, $line - 4, 111, 24, 'right', '');
            $pdf->showBoxed(toMoney($amount['actuaYTD'] - $amount['budgetYTD']), 704, $line - 4, 111, 24, 'right', '');
        }

    }
}

$promoActual = dbGetFinancialActualIncome($propertyID, '', $calendar['period'], $calendar['period'], $calendar['year'], true, 'balanceAccruals');
$promoBudget = dbGetFinancialBudgetIncome($propertyID, '', $calendar['year'], $calendar['period'], $calendar['period'], true, 'pmrp_b_a_amt');
$promoActualYTD = dbGetFinancialActualIncome($propertyID, '', 1, $calendar['period'], $calendar['year'], true, 'balanceAccruals');
$promoBudgetYTD = dbGetFinancialBudgetIncome($propertyID, '', $calendar['year'], 1, $calendar['period'], true, 'pmrp_b_a_amt');

if ($promoActual['amount'] || $promoBudget['amount'] || $promoActualYTD['amount'] || $promoBudgetYTD['amount']) {
    $pdf->setFontExt($_fonts['Panton-Light'], 11);

    $line -= 24;
    $pdf->setColorExt('fill', 'rgb', 0.81, 0.87, 0.95, 1);
    $pdf->rect(25, $line, 792, 24);
    $pdf->fill();
    $pdf->setlinewidth(0.5);
    $pdf->moveto(25, $line);
    $pdf->lineto(817, $line);
    $pdf->stroke();

    $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
    $pdf->showBoxed('Promo. Income', 27, $line - 4, 113, 24, 'left', '');
    $pdf->showBoxed(toMoney($promoActual['amount']), 139, $line - 4, 112, 24, 'right', '');
    $pdf->showBoxed(toMoney($promoBudgetYTD['amount']), 252, $line - 4, 111, 24, 'right', '');
    $pdf->showBoxed(toMoney($promoActual['amount'] - $promoBudgetYTD['amount']), 365, $line - 4, 111, 24, 'right', '');
    $pdf->showBoxed(toMoney($promoActualYTD['amount']), 478, $line - 4, 111, 24, 'right', '');
    $pdf->showBoxed(toMoney($promoBudgetYTD['amount']), 591, $line - 4, 111, 24, 'right', '');
    $pdf->showBoxed(toMoney($promoActualYTD['amount'] - $promoBudgetYTD['amount']), 704, $line - 4, 111, 24, 'right', '');

    $promoActualAcc = dbGetFinancialActualIncomeAccounts($propertyID, '', $calendar['period'], $calendar['period'], $calendar['year'], true, 'balanceAccruals');
    $promoBudgetAcc = dbGetFinancialBudgetIncomeAccounts($propertyID, '', $calendar['year'], $calendar['period'], $calendar['period'], true, 'pmrp_b_a_amt');
    $promoActualYTDAcc = dbGetFinancialActualIncomeAccounts($propertyID, '', 1, $calendar['period'], $calendar['year'], true, 'balanceAccruals');
    $promoBudgetYTDAcc = dbGetFinancialBudgetIncomeAccounts($propertyID, '', $calendar['year'], 1, $calendar['period'], true, 'pmrp_b_a_amt');

    $subGroups = [];
    foreach ($promoActualAcc as $rs) {
        $subGroups[ucfirst(strtolower($rs['description']))]['actual'] = $rs['amount'];
    }

    foreach ($promoActualYTDAcc as $rs) {
        $subGroups[ucfirst(strtolower($rs['description']))]['actuaYTD'] = $rs['amount'];
    }

    foreach ($promoBudgetAcc as $rs) {
        $subGroups[ucfirst(strtolower($rs['description']))]['budget'] = $rs['amount'];
    }

    foreach ($promoBudgetYTDAcc as $rs) {
        $subGroups[ucfirst(strtolower($rs['description']))]['budgetYTD'] = $rs['amount'];
    }

    foreach ($subGroups as $subGroup => $amount) {

        if ($amount['amount'] != 0 || $amount['budget'] != 0 || $amount['actuaYTD'] != 0 || $amount['budgetYTD'] != 0) {

            if ($line < 50) {
                renderNewFinancialSummarySubGroupPage($pdf, $line, $contentPageNo);
                $line = 397;
            }

            $pdf->setFontExt($_fonts['Panton-Light'], 9);
            $line -= 24;
            $pdf->setColorExt('fill', 'rgb', 0.91, 0.94, 0.97, 1);
            $pdf->rect(25, $line, 792, 24);
            $pdf->fill();
            $pdf->setlinewidth(0.5);
            $pdf->moveto(25, $line);
            $pdf->lineto(817, $line);
            $pdf->stroke();

            $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
            $pdf->showBoxed('   ' . $subGroup, 27, $line - 4, 113, 24, 'left', '');
            $pdf->showBoxed(toMoney($amount['actual']), 139, $line - 4, 112, 24, 'right', '');
            $pdf->showBoxed(toMoney($amount['budget']), 252, $line - 4, 111, 24, 'right', '');
            $pdf->showBoxed(toMoney($amount['actual'] - $amount['budget']), 365, $line - 4, 111, 24, 'right', '');
            $pdf->showBoxed(toMoney($amount['actuaYTD']), 478, $line - 4, 111, 24, 'right', '');
            $pdf->showBoxed(toMoney($amount['budgetYTD']), 591, $line - 4, 111, 24, 'right', '');
            $pdf->showBoxed(toMoney($amount['actuaYTD'] - $amount['budgetYTD']), 704, $line - 4, 111, 24, 'right', '');
        }

    }
}

$otherActual = dbGetFinancialActualIncome($propertyID, 'INC.REC', $calendar['period'], $calendar['period'], $calendar['year'], false, 'balanceAccruals');
$otherBudget = dbGetFinancialBudgetIncome($propertyID, 'INC.REC', $calendar['year'], $calendar['period'], $calendar['period'], false, 'pmrp_b_a_amt');
$otherActualYTD = dbGetFinancialActualIncome($propertyID, 'INC.REC', 1, $calendar['period'], $calendar['year'], false, 'balanceAccruals');
$otherBudgetYTD = dbGetFinancialBudgetIncome($propertyID, 'INC.REC', $calendar['year'], 1, $calendar['period'], false, 'pmrp_b_a_amt');

if ($otherActual['amount'] || $otherBudget['amount'] || $otherActualYTD['amount'] || $otherBudgetYTD['amount']) {
    $pdf->setFontExt($_fonts['Panton-Light'], 11);

    $line -= 24;
    $pdf->setColorExt('fill', 'rgb', 0.81, 0.87, 0.95, 1);
    $pdf->rect(25, $line, 792, 24);
    $pdf->fill();
    $pdf->setlinewidth(0.5);
    $pdf->moveto(25, $line);
    $pdf->lineto(817, $line);
    $pdf->stroke();

    $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
    $pdf->showBoxed('Other Income', 27, $line - 4, 113, 24, 'left', '');
    $pdf->showBoxed(toMoney($otherActual['amount']), 139, $line - 4, 112, 24, 'right', '');
    $pdf->showBoxed(toMoney($otherBudget['amount']), 252, $line - 4, 111, 24, 'right', '');
    $pdf->showBoxed(toMoney($otherActual['amount'] - $otherBudget['amount']), 365, $line - 4, 111, 24, 'right', '');
    $pdf->showBoxed(toMoney($otherActualYTD['amount']), 478, $line - 4, 111, 24, 'right', '');
    $pdf->showBoxed(toMoney($otherBudgetYTD['amount']), 591, $line - 4, 111, 24, 'right', '');
    $pdf->showBoxed(toMoney($otherActualYTD['amount'] - $otherBudgetYTD['amount']), 704, $line - 4, 111, 24, 'right', '');

    $otherActualAcc = dbGetFinancialActualIncomeAccounts($propertyID, 'INC.REC', $calendar['period'], $calendar['period'], $calendar['year'], false, 'balanceAccruals');
    $otherBudgetAcc = dbGetFinancialBudgetIncomeAccounts($propertyID, 'INC.REC', $calendar['year'], $calendar['period'], $calendar['period'], false, 'pmrp_b_a_amt');
    $otherActualYTDAcc = dbGetFinancialActualIncomeAccounts($propertyID, 'INC.REC', 1, $calendar['period'], $calendar['year'], false, 'balanceAccruals');
    $otherBudgetYTDAcc = dbGetFinancialBudgetIncomeAccounts($propertyID, 'INC.REC', $calendar['year'], 1, $calendar['period'], false, 'pmrp_b_a_amt');

    $subGroups = [];
    foreach ($otherActualAcc as $rs) {
        $subGroups[ucfirst(strtolower($rs['description']))]['actual'] = $rs['amount'];
    }

    foreach ($otherActualYTDAcc as $rs) {
        $subGroups[ucfirst(strtolower($rs['description']))]['actuaYTD'] = $rs['amount'];
    }

    foreach ($otherBudgetAcc as $rs) {
        $subGroups[ucfirst(strtolower($rs['description']))]['budget'] = $rs['amount'];
    }

    foreach ($otherBudgetYTDAcc as $rs) {
        $subGroups[ucfirst(strtolower($rs['description']))]['budgetYTD'] = $rs['amount'];
    }

    foreach ($subGroups as $subGroup => $amount) {

        if ($amount['amount'] != 0 || $amount['budget'] != 0 || $amount['actuaYTD'] != 0 || $amount['budgetYTD'] != 0) {

            if ($line < 50) {
                renderNewFinancialSummarySubGroupPage($pdf, $line, $contentPageNo);
                $line = 397;
            }

            $pdf->setFontExt($_fonts['Panton-Light'], 9);
            $line -= 24;
            $pdf->setColorExt('fill', 'rgb', 0.91, 0.94, 0.97, 1);
            $pdf->rect(25, $line, 792, 24);
            $pdf->fill();
            $pdf->setlinewidth(0.5);
            $pdf->moveto(25, $line);
            $pdf->lineto(817, $line);
            $pdf->stroke();

            $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
            $pdf->showBoxed('   ' . $subGroup, 27, $line - 4, 113, 24, 'left', '');
            $pdf->showBoxed(toMoney($amount['actual']), 139, $line - 4, 112, 24, 'right', '');
            $pdf->showBoxed(toMoney($amount['budget']), 252, $line - 4, 111, 24, 'right', '');
            $pdf->showBoxed(toMoney($amount['actual'] - $amount['budget']), 365, $line - 4, 111, 24, 'right', '');
            $pdf->showBoxed(toMoney($amount['actuaYTD']), 478, $line - 4, 111, 24, 'right', '');
            $pdf->showBoxed(toMoney($amount['budgetYTD']), 591, $line - 4, 111, 24, 'right', '');
            $pdf->showBoxed(toMoney($amount['actuaYTD'] - $amount['budgetYTD']), 704, $line - 4, 111, 24, 'right', '');
        }

    }
}

$TotalActual = $otherActual['amount'] + $promoActual['amount'] + $outgoingActual['amount'] + $rentalActual['amount'];
$TotalBudget = $otherBudget['amount'] + $promoBudget['amount'] + $outgoingBudget['amount'] + $rentalBudget['amount'];
$TotalActualYTD = $otherActualYTD['amount'] + $promoActualYTD['amount'] + $outgoingActualYTD['amount'] + $rentalActualYTD['amount'];
$TotalBudgetYTD = $otherBudgetYTD['amount'] + $promoBudgetYTD['amount'] + $outgoingBudgetYTD['amount'] + $rentalBudgetYTD['amount'];

if ($TotalActual  || $TotalBudget  || $TotalActualYTD || $TotalBudgetYTD) {
    $pdf->setFontExt($_fonts['Panton-Light'], 11);

    $line -= 24;
    $pdf->setColorExt('fill', 'rgb', 0.91, 0.94, 0.97, 1);
    $pdf->rect(25, $line, 792, 24);
    $pdf->fill();
    $pdf->setlinewidth(0.5);
    $pdf->moveto(25, $line);
    $pdf->lineto(817, $line);
    $pdf->stroke();

    $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
    $pdf->setFontExt($_fonts['Panton-Bold'], 11);
    $pdf->showBoxed('TOTAL INCOME', 27, $line - 4, 113, 24, 'left', '');

    $pdf->showBoxed(toMoney($TotalActual), 139, $line - 4, 112, 24, 'right', '');
    $pdf->showBoxed(toMoney($TotalBudget), 252, $line - 4, 111, 24, 'right', '');
    $pdf->showBoxed(toMoney($TotalActual - $TotalBudget), 365, $line - 4, 111, 24, 'right', '');
    $pdf->showBoxed(toMoney($TotalActualYTD), 478, $line - 4, 111, 24, 'right', '');
    $pdf->showBoxed(toMoney($TotalBudgetYTD), 591, $line - 4, 111, 24, 'right', '');
    $pdf->showBoxed(toMoney($TotalActualYTD - $TotalBudgetYTD), 704, $line - 4, 111, 24, 'right', '');
    $pdf->setFontExt($_fonts['Panton-Light'], 11);
    $recExpensesActual = dbGetFinancialActualExpense($propertyID, 'EXP.OUT', $calendar['period'], $calendar['period'], $calendar['year'], 'balanceAccruals');
    $recExpensesBudget = dbGetFinancialBudgetExpense($propertyID, 'EXP.OUT', $calendar['year'], $calendar['period'], $calendar['period'], 'pmep_b_a_amt');
    $recExpensesActualYTD = dbGetFinancialActualExpense($propertyID, 'EXP.OUT', 1, $calendar['period'], $calendar['year'], 'balanceAccruals');
    $recExpensesBudgetYTD = dbGetFinancialBudgetExpense($propertyID, 'EXP.OUT', $calendar['year'], 1, $calendar['period'], 'pmep_b_a_amt');

    $line -= 24;
    $pdf->setColorExt('fill', 'rgb', 0.81, 0.87, 0.95, 1);
    $pdf->rect(25, $line, 792, 24);
    $pdf->fill();
    $pdf->setlinewidth(0.5);
    $pdf->moveto(25, $line);
    $pdf->lineto(817, $line);
    $pdf->stroke();

    $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
    $pdf->showBoxed('Recoverable Expenses', 27, $line - 4, 113, 24, 'left', '');
    $pdf->showBoxed(toMoney($recExpensesActual['amount']), 139, $line - 4, 112, 24, 'right', '');
    $pdf->showBoxed(toMoney($recExpensesBudget['amount']), 252, $line - 4, 111, 24, 'right', '');
    $pdf->showBoxed(toMoney($recExpensesBudget['amount'] - $recExpensesActual['amount']), 365, $line - 4, 111, 24, 'right', '');
    $pdf->showBoxed(toMoney($recExpensesActualYTD['amount']), 478, $line - 4, 111, 24, 'right', '');
    $pdf->showBoxed(toMoney($recExpensesBudgetYTD['amount']), 591, $line - 4, 111, 24, 'right', '');
    $pdf->showBoxed(toMoney($recExpensesBudgetYTD['amount'] - $recExpensesActualYTD['amount']), 704, $line - 4, 111, 24, 'right', '');

    $recExpensesActualSubGroup = dbGetFinancialActualExpenseAccounts($propertyID, 'EXP.OUT', $calendar['period'], $calendar['period'], $calendar['year'], 'balanceAccruals');
    $recExpensesBudgetSubGroup = dbGetFinancialBudgetExpenseAccounts($propertyID, 'EXP.OUT', $calendar['year'], $calendar['period'], $calendar['period'], 'pmep_b_a_amt');
    $recExpensesActualYTDSubGroup = dbGetFinancialActualExpenseAccounts($propertyID, 'EXP.OUT', 1, $calendar['period'], $calendar['year'], 'balanceAccruals');
    $recExpensesBudgetYTDSubGroup = dbGetFinancialBudgetExpenseAccounts($propertyID, 'EXP.OUT', $calendar['year'], 1, $calendar['period'], 'pmep_b_a_amt');

    $subGroups = [];
    foreach ($recExpensesActualSubGroup as $rs) {
        $subGroups[ucfirst(strtolower($rs['description']))][ucfirst(strtolower($rs['account_name']))]['actual'] = $rs['amount'];
    }

    foreach ($recExpensesActualYTDSubGroup as $rs) {
        $subGroups[ucfirst(strtolower($rs['description']))][ucfirst(strtolower($rs['account_name']))]['actuaYTD'] = $rs['amount'];
    }

    foreach ($recExpensesBudgetSubGroup as $rs) {
        $subGroups[ucfirst(strtolower($rs['description']))][ucfirst(strtolower($rs['account_name']))]['budget'] = $rs['amount'];
    }

    foreach ($recExpensesBudgetYTDSubGroup as $rs) {
        $subGroups[ucfirst(strtolower($rs['description']))][ucfirst(strtolower($rs['account_name']))]['budgetYTD'] = $rs['amount'];
    }

    foreach ($subGroups as $subGroup => $accounts) {

        $subAmount = [];
        foreach ($accounts as $name => $amount) {

            if ($amount['amount'] != 0 || $amount['budget'] != 0 || $amount['actuaYTD'] != 0 || $amount['budgetYTD'] != 0) {

                if ($line < 50) {
                    renderNewFinancialSummarySubGroupPage($pdf, $line, $contentPageNo);
                    $line = 397;
                }

                $pdf->setFontExt($_fonts['Panton-Light'], 9);
                $line -= 24;
                $pdf->setColorExt('fill', 'rgb', 0.91, 0.94, 0.97, 1);
                $pdf->rect(25, $line, 792, 24);
                $pdf->fill();
                $pdf->setlinewidth(0.5);
                $pdf->moveto(25, $line);
                $pdf->lineto(817, $line);
                $pdf->stroke();

                $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
                $pdf->showBoxed('   ' . $name, 27, $line - 4, 113, 24, 'left', '');
                $pdf->showBoxed(toMoney($amount['actual']), 139, $line - 4, 112, 24, 'right', '');
                $pdf->showBoxed(toMoney($amount['budget']), 252, $line - 4, 111, 24, 'right', '');
                $pdf->showBoxed(toMoney($amount['budget'] - $amount['actual']), 365, $line - 4, 111, 24, 'right', '');
                $pdf->showBoxed(toMoney($amount['actuaYTD']), 478, $line - 4, 111, 24, 'right', '');
                $pdf->showBoxed(toMoney($amount['budgetYTD']), 591, $line - 4, 111, 24, 'right', '');
                $pdf->showBoxed(toMoney($amount['budgetYTD'] - $amount['actuaYTD']), 704, $line - 4, 111, 24, 'right', '');

                $subAmount['actual'] += $amount['actual'];
                $subAmount['budget'] += $amount['budget'];
                $subAmount['actuaYTD'] += $amount['actuaYTD'];
                $subAmount['budgetYTD'] += $amount['budgetYTD'];

            }
        }

        if ($subAmount['actual'] != 0 || $subAmount['budget'] != 0 || $subAmount['actuaYTD'] != 0 || $subAmount['budgetYTD'] != 0) {
            if ($line < 50) {
                renderNewFinancialSummarySubGroupPage($pdf, $line, $contentPageNo);
                $line = 397;
            }

            $pdf->setFontExt($_fonts['Panton-Bold'], 9);
            $line -= 24;
            $pdf->setColorExt('fill', 'rgb', 0.91, 0.94, 0.97, 1);
            $pdf->rect(25, $line, 792, 24);
            $pdf->fill();
            $pdf->setlinewidth(0.5);
            $pdf->moveto(25, $line);
            $pdf->lineto(817, $line);
            $pdf->stroke();

            $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
            $pdf->showBoxed('   ' . $subGroup, 27, $line - 4, 113, 24, 'left', '');
            $pdf->showBoxed(toMoney($subAmount['actual']), 139, $line - 4, 112, 24, 'right', '');
            $pdf->showBoxed(toMoney($subAmount['budget']), 252, $line - 4, 111, 24, 'right', '');
            $pdf->showBoxed(toMoney($subAmount['budget'] - $subAmount['actual']), 365, $line - 4, 111, 24, 'right', '');
            $pdf->showBoxed(toMoney($subAmount['actuaYTD']), 478, $line - 4, 111, 24, 'right', '');
            $pdf->showBoxed(toMoney($subAmount['budgetYTD']), 591, $line - 4, 111, 24, 'right', '');
            $pdf->showBoxed(toMoney($subAmount['budgetYTD'] - $subAmount['actuaYTD']), 704, $line - 4, 111, 24, 'right', '');
        }

    }
}

$directExpensesActual = dbGetFinancialActualExpense($propertyID, 'EXP.REC', $calendar['period'], $calendar['period'], $calendar['year'], 'balanceAccruals');
$directExpensesBudget = dbGetFinancialBudgetExpense($propertyID, 'EXP.REC', $calendar['year'], $calendar['period'], $calendar['period'], 'pmep_b_a_amt');
$directExpensesActualYTD = dbGetFinancialActualExpense($propertyID, 'EXP.REC', 1, $calendar['period'], $calendar['year'], 'balanceAccruals');
$directExpensesBudgetYTD = dbGetFinancialBudgetExpense($propertyID, 'EXP.REC', $calendar['year'], 1, $calendar['period'], 'pmep_b_a_amt');

if ($directExpensesActual['amount'] || $directExpensesBudget['amount'] || $directExpensesActualYTD['amount'] || $directExpensesBudgetYTD['amount']) {
    $pdf->setFontExt($_fonts['Panton-Light'], 11);

    $line -= 24;
    $pdf->setColorExt('fill', 'rgb', 0.81, 0.87, 0.95, 1);
    $pdf->rect(25, $line, 792, 24);
    $pdf->fill();
    $pdf->setlinewidth(0.5);
    $pdf->moveto(25, $line);
    $pdf->lineto(817, $line);
    $pdf->stroke();

    $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
    $pdf->showBoxed('Direct Recoveries', 27, $line - 4, 113, 24, 'left', '');
    $pdf->showBoxed(toMoney($directExpensesActual['amount']), 139, $line - 4, 112, 24, 'right', '');
    $pdf->showBoxed(toMoney($directExpensesBudget['amount']), 252, $line - 4, 111, 24, 'right', '');
    $pdf->showBoxed(toMoney($directExpensesBudget['amount'] - $directExpensesActual['amount']), 365, $line - 4, 111, 24, 'right', '');
    $pdf->showBoxed(toMoney($directExpensesActualYTD['amount']), 478, $line - 4, 111, 24, 'right', '');
    $pdf->showBoxed(toMoney($directExpensesBudgetYTD['amount']), 591, $line - 4, 111, 24, 'right', '');
    $pdf->showBoxed(toMoney($directExpensesBudgetYTD['amount'] - $directExpensesActualYTD['amount']), 704, $line - 4, 111, 24, 'right', '');

    $directExpensesActualAccounts = dbGetFinancialActualExpenseAccounts($propertyID, 'EXP.REC', $calendar['period'], $calendar['period'], $calendar['year'], 'balanceAccruals');
    $directExpensesBudgetAccounts = dbGetFinancialBudgetExpenseAccounts($propertyID, 'EXP.REC', $calendar['year'], $calendar['period'], $calendar['period'], 'pmep_b_a_amt');
    $directExpensesActualYTDAccounts = dbGetFinancialActualExpenseAccounts($propertyID, 'EXP.REC', 1, $calendar['period'], $calendar['year'], 'balanceAccruals');
    $directExpensesBudgetYTDAccounts = dbGetFinancialBudgetExpenseAccounts($propertyID, 'EXP.REC', $calendar['year'], 1, $calendar['period'], 'pmep_b_a_amt');

    $subGroups = [];
    foreach ($directExpensesActualAccounts as $rs) {
        $subGroups[ucfirst(strtolower($rs['description']))][ucfirst(strtolower($rs['account_name']))]['actual'] = $rs['amount'];
    }

    foreach ($directExpensesActualYTDAccounts as $rs) {
        $subGroups[ucfirst(strtolower($rs['description']))][ucfirst(strtolower($rs['account_name']))]['actuaYTD'] = $rs['amount'];
    }

    foreach ($directExpensesBudgetAccounts as $rs) {
        $subGroups[ucfirst(strtolower($rs['description']))][ucfirst(strtolower($rs['account_name']))]['budget'] = $rs['amount'];
    }

    foreach ($directExpensesBudgetYTDAccounts as $rs) {
        $subGroups[ucfirst(strtolower($rs['description']))][ucfirst(strtolower($rs['account_name']))]['budgetYTD'] = $rs['amount'];
    }

    foreach ($subGroups as $subGroup => $accounts) {

        $subAmount = [];
        foreach ($accounts as $name => $amount) {

            if ($amount['amount'] != 0 || $amount['budget'] != 0 || $amount['actuaYTD'] != 0 || $amount['budgetYTD'] != 0) {

                if ($line < 50) {
                    renderNewFinancialSummarySubGroupPage($pdf, $line, $contentPageNo);
                    $line = 397;
                }

                $pdf->setFontExt($_fonts['Panton-Light'], 9);
                $line -= 24;
                $pdf->setColorExt('fill', 'rgb', 0.91, 0.94, 0.97, 1);
                $pdf->rect(25, $line, 792, 24);
                $pdf->fill();
                $pdf->setlinewidth(0.5);
                $pdf->moveto(25, $line);
                $pdf->lineto(817, $line);
                $pdf->stroke();

                $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
                $pdf->showBoxed('   ' . $name, 27, $line - 4, 113, 24, 'left', '');
                $pdf->showBoxed(toMoney($amount['actual']), 139, $line - 4, 112, 24, 'right', '');
                $pdf->showBoxed(toMoney($amount['budget']), 252, $line - 4, 111, 24, 'right', '');
                $pdf->showBoxed(toMoney($amount['budget'] - $amount['actual']), 365, $line - 4, 111, 24, 'right', '');
                $pdf->showBoxed(toMoney($amount['actuaYTD']), 478, $line - 4, 111, 24, 'right', '');
                $pdf->showBoxed(toMoney($amount['budgetYTD']), 591, $line - 4, 111, 24, 'right', '');
                $pdf->showBoxed(toMoney($amount['budgetYTD'] - $amount['actuaYTD']), 704, $line - 4, 111, 24, 'right', '');

                $subAmount['actual'] += $amount['actual'];
                $subAmount['budget'] += $amount['budget'];
                $subAmount['actuaYTD'] += $amount['actuaYTD'];
                $subAmount['budgetYTD'] += $amount['budgetYTD'];
            }
        }

        if ($subAmount['actual'] != 0 || $subAmount['budget'] != 0 || $subAmount['actuaYTD'] != 0 || $subAmount['budgetYTD'] != 0) {
            if ($line < 50) {
                renderNewFinancialSummarySubGroupPage($pdf, $line, $contentPageNo);
                $line = 397;
            }

            $pdf->setFontExt($_fonts['Panton-Bold'], 9);
            $line -= 24;
            $pdf->setColorExt('fill', 'rgb', 0.91, 0.94, 0.97, 1);
            $pdf->rect(25, $line, 792, 24);
            $pdf->fill();
            $pdf->setlinewidth(0.5);
            $pdf->moveto(25, $line);
            $pdf->lineto(817, $line);
            $pdf->stroke();

            $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
            $pdf->showBoxed('   ' . $subGroup, 27, $line - 4, 113, 24, 'left', '');
            $pdf->showBoxed(toMoney($subAmount['actual']), 139, $line - 4, 112, 24, 'right', '');
            $pdf->showBoxed(toMoney($subAmount['budget']), 252, $line - 4, 111, 24, 'right', '');
            $pdf->showBoxed(toMoney($subAmount['budget'] - $subAmount['actual']), 365, $line - 4, 111, 24, 'right', '');
            $pdf->showBoxed(toMoney($subAmount['actuaYTD']), 478, $line - 4, 111, 24, 'right', '');
            $pdf->showBoxed(toMoney($subAmount['budgetYTD']), 591, $line - 4, 111, 24, 'right', '');
            $pdf->showBoxed(toMoney($subAmount['budgetYTD'] - $subAmount['actuaYTD']), 704, $line - 4, 111, 24, 'right', '');
        }

    }
}

$nonRecExpensesActual = dbGetFinancialActualExpense($propertyID, 'EXP.OWN', $calendar['period'], $calendar['period'], $calendar['year'], 'balanceAccruals');
$nonRecExpensesBudget = dbGetFinancialBudgetExpense($propertyID, 'EXP.OWN', $calendar['year'], $calendar['period'], $calendar['period'], 'pmep_b_a_amt');
$nonRecExpensesActualYTD = dbGetFinancialActualExpense($propertyID, 'EXP.OWN', 1, $calendar['period'], $calendar['year'], 'balanceAccruals');
$nonRecExpensesBudgetYTD = dbGetFinancialBudgetExpense($propertyID, 'EXP.OWN', $calendar['year'], 1, $calendar['period'], 'pmep_b_a_amt');

if ($nonRecExpensesActual['amount'] || $nonRecExpensesBudget['amount'] || $nonRecExpensesActualYTD['amount'] || $nonRecExpensesBudgetYTD['amount']) {
    $pdf->setFontExt($_fonts['Panton-Light'], 11);

    $line -= 24;
    $pdf->setColorExt('fill', 'rgb', 0.81, 0.87, 0.95, 1);
    $pdf->rect(25, $line, 792, 24);
    $pdf->fill();
    $pdf->setlinewidth(0.5);
    $pdf->moveto(25, $line);
    $pdf->lineto(817, $line);
    $pdf->stroke();

    $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
    $pdf->showBoxed('Non-Recoverable Expenses', 27, $line, 113, 24, 'left', '');
    $pdf->showBoxed(toMoney($nonRecExpensesActual['amount']), 139, $line - 4, 112, 24, 'right', '');
    $pdf->showBoxed(toMoney($nonRecExpensesBudget['amount']), 252, $line - 4, 111, 24, 'right', '');
    $pdf->showBoxed(toMoney($nonRecExpensesBudget['amount'] - $nonRecExpensesActual['amount']), 365, $line - 4, 111, 24, 'right', '');
    $pdf->showBoxed(toMoney($nonRecExpensesActualYTD['amount']), 478, $line - 4, 111, 24, 'right', '');
    $pdf->showBoxed(toMoney($nonRecExpensesBudgetYTD['amount']), 591, $line - 4, 111, 24, 'right', '');
    $pdf->showBoxed(toMoney($nonRecExpensesBudgetYTD['amount'] - $nonRecExpensesActualYTD['amount']), 704, $line - 4, 111, 24, 'right', '');

    $nonRecExpensesActualAccounts = dbGetFinancialActualExpenseAccounts($propertyID, 'EXP.OWN', $calendar['period'], $calendar['period'], $calendar['year'], 'balanceAccruals');
    $nonRecExpensesBudgetAccounts = dbGetFinancialBudgetExpenseAccounts($propertyID, 'EXP.OWN', $calendar['year'], $calendar['period'], $calendar['period'], 'pmep_b_a_amt');
    $nonRecExpensesActualYTDAccounts = dbGetFinancialActualExpenseAccounts($propertyID, 'EXP.OWN', 1, $calendar['period'], $calendar['year'], 'balanceAccruals');
    $nonRecExpensesBudgetYTDAccounts = dbGetFinancialBudgetExpenseAccounts($propertyID, 'EXP.OWN', $calendar['year'], 1, $calendar['period'], 'pmep_b_a_amt');

    $subGroups = [];
    foreach ($nonRecExpensesActualAccounts as $rs) {
        $subGroups[ucfirst(strtolower($rs['description']))][ucfirst(strtolower($rs['account_name']))]['actual'] = $rs['amount'];
    }

    foreach ($nonRecExpensesActualYTDAccounts as $rs) {
        $subGroups[ucfirst(strtolower($rs['description']))][ucfirst(strtolower($rs['account_name']))]['actuaYTD'] = $rs['amount'];
    }

    foreach ($nonRecExpensesBudgetAccounts as $rs) {
        $subGroups[ucfirst(strtolower($rs['description']))][ucfirst(strtolower($rs['account_name']))]['budget'] = $rs['amount'];
    }

    foreach ($nonRecExpensesBudgetYTDAccounts as $rs) {
        $subGroups[ucfirst(strtolower($rs['description']))][ucfirst(strtolower($rs['account_name']))]['budgetYTD'] = $rs['amount'];
    }

    foreach ($subGroups as $subGroup => $accounts) {

        $subAmount = [];
        foreach ($accounts as $name => $amount) {

            if ($amount['amount'] != 0 || $amount['budget'] != 0 || $amount['actuaYTD'] != 0 || $amount['budgetYTD'] != 0) {

                if ($line < 50) {
                    renderNewFinancialSummarySubGroupPage($pdf, $line, $contentPageNo);
                    $line = 397;
                }

                $pdf->setFontExt($_fonts['Panton-Light'], 9);
                $line -= 24;
                $pdf->setColorExt('fill', 'rgb', 0.91, 0.94, 0.97, 1);
                $pdf->rect(25, $line, 792, 24);
                $pdf->fill();
                $pdf->setlinewidth(0.5);
                $pdf->moveto(25, $line);
                $pdf->lineto(817, $line);
                $pdf->stroke();

                $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
                $pdf->showBoxed('   ' . $name, 27, $line - 4, 113, 24, 'left', '');
                $pdf->showBoxed(toMoney($amount['actual']), 139, $line - 4, 112, 24, 'right', '');
                $pdf->showBoxed(toMoney($amount['budget']), 252, $line - 4, 111, 24, 'right', '');
                $pdf->showBoxed(toMoney($amount['budget'] - $amount['actual']), 365, $line - 4, 111, 24, 'right', '');
                $pdf->showBoxed(toMoney($amount['actuaYTD']), 478, $line - 4, 111, 24, 'right', '');
                $pdf->showBoxed(toMoney($amount['budgetYTD']), 591, $line - 4, 111, 24, 'right', '');
                $pdf->showBoxed(toMoney($amount['budgetYTD'] - $amount['actuaYTD']), 704, $line - 4, 111, 24, 'right', '');

                $subAmount['actual'] += $amount['actual'];
                $subAmount['budget'] += $amount['budget'];
                $subAmount['actuaYTD'] += $amount['actuaYTD'];
                $subAmount['budgetYTD'] += $amount['budgetYTD'];
            }
        }

        if ($subAmount['actual'] != 0 || $subAmount['budget'] != 0 || $subAmount['actuaYTD'] != 0 || $subAmount['budgetYTD'] != 0) {
            if ($line < 50) {
                renderNewFinancialSummarySubGroupPage($pdf, $line, $contentPageNo);
                $line = 397;
            }

            $pdf->setFontExt($_fonts['Panton-Bold'], 9);
            $line -= 24;
            $pdf->setColorExt('fill', 'rgb', 0.91, 0.94, 0.97, 1);
            $pdf->rect(25, $line, 792, 24);
            $pdf->fill();
            $pdf->setlinewidth(0.5);
            $pdf->moveto(25, $line);
            $pdf->lineto(817, $line);
            $pdf->stroke();

            $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
            $pdf->showBoxed('   ' . $subGroup, 27, $line - 4, 113, 24, 'left', '');
            $pdf->showBoxed(toMoney($subAmount['actual']), 139, $line - 4, 112, 24, 'right', '');
            $pdf->showBoxed(toMoney($subAmount['budget']), 252, $line - 4, 111, 24, 'right', '');
            $pdf->showBoxed(toMoney($subAmount['budget'] - $subAmount['actual']), 365, $line - 4, 111, 24, 'right', '');
            $pdf->showBoxed(toMoney($subAmount['actuaYTD']), 478, $line - 4, 111, 24, 'right', '');
            $pdf->showBoxed(toMoney($subAmount['budgetYTD']), 591, $line - 4, 111, 24, 'right', '');
            $pdf->showBoxed(toMoney($subAmount['budgetYTD'] - $subAmount['actuaYTD']), 704, $line - 4, 111, 24, 'right', '');
        }

    }
}

$TotalActualExpense = $recExpensesActual['amount'] + $directExpensesActual['amount'] + $nonRecExpensesActual['amount'];
$TotalBudgetExpense = $recExpensesBudget['amount'] + $directExpensesBudget['amount'] + $nonRecExpensesBudget['amount'];
$TotalActualYTDExpense = $recExpensesActualYTD['amount'] + $directExpensesActualYTD['amount'] + $nonRecExpensesActualYTD['amount'];
$TotalBudgetYTDExpense = $recExpensesBudgetYTD['amount'] + $directExpensesBudgetYTD['amount'] + $nonRecExpensesBudgetYTD['amount'];

if ($TotalActualExpense  || $TotalBudgetExpense || $TotalActualYTDExpense  || $TotalBudgetYTDExpense) {

    $pdf->setFontExt($_fonts['Panton-Light'], 11);

    if ($line < 50) {
        renderNewFinancialSummarySubGroupPage($pdf, $line, $contentPageNo);
        $line = 397;
    }

    $line -= 24;
    $pdf->setColorExt('fill', 'rgb', 0.91, 0.94, 0.97, 1);
    $pdf->rect(25, $line, 792, 24);
    $pdf->fill();
    $pdf->setlinewidth(0.5);
    $pdf->moveto(25, $line);
    $pdf->lineto(817, $line);
    $pdf->stroke();

    $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
    $pdf->setFontExt($_fonts['Panton-Bold'], 11);
    $pdf->showBoxed('TOTAL EXPENSES', 27, $line - 4, 113, 24, 'left', '');

    $pdf->showBoxed(toMoney($TotalActualExpense), 139, $line - 4, 112, 24, 'right', '');
    $pdf->showBoxed(toMoney($TotalBudgetExpense), 252, $line - 4, 111, 24, 'right', '');
    $pdf->showBoxed(toMoney($TotalBudgetExpense - $TotalActualExpense), 365, $line - 4, 111, 24, 'right', '');
    $pdf->showBoxed(toMoney($TotalActualYTDExpense), 478, $line - 4, 111, 24, 'right', '');
    $pdf->showBoxed(toMoney($TotalBudgetYTDExpense), 591, $line - 4, 111, 24, 'right', '');
    $pdf->showBoxed(toMoney($TotalBudgetYTDExpense - $TotalActualYTDExpense), 704, $line - 4, 111, 24, 'right', '');
    $pdf->setFontExt($_fonts['Panton-Light'], 11);
    if ($line < 50) {
        renderNewFinancialSummarySubGroupPage($pdf, $line, $contentPageNo);
        $line = 397;
    }
}

$line -= 24;
$pdf->setColorExt('fill', 'rgb', 0.91, 0.94, 0.97, 1);
$pdf->rect(25, $line, 792, 24);
$pdf->fill();
$pdf->setlinewidth(0.5);
$pdf->moveto(25, $line);
$pdf->lineto(817, $line);
$pdf->stroke();


$NetActual = $TotalActual - $TotalActualExpense;
$NetBudget = $TotalBudget - $TotalBudgetExpense;
$NetActualYTD = $TotalActualYTD - $TotalActualYTDExpense;
$NetBudgetYTD = $TotalBudgetYTD - $TotalBudgetYTDExpense;
$pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
$pdf->setFontExt($_fonts['Panton-Bold'], 11);
$pdf->showBoxed('NET INCOME', 27, $line - 4, 113, 24, 'left', '');

$pdf->showBoxed(toMoney($NetActual), 139, $line - 4, 112, 24, 'right', '');
$pdf->showBoxed(toMoney($NetBudget), 252, $line - 4, 111, 24, 'right', '');
$pdf->showBoxed(toMoney($NetActual - $NetBudget), 365, $line - 4, 111, 24, 'right', '');
$pdf->showBoxed(toMoney($NetActualYTD), 478, $line - 4, 111, 24, 'right', '');
$pdf->showBoxed(toMoney($NetBudgetYTD), 591, $line - 4, 111, 24, 'right', '');
$pdf->showBoxed(toMoney($NetActualYTD - $NetBudgetYTD), 704, $line - 4, 111, 24, 'right', '');
$pdf->setFontExt($_fonts['Panton-Light'], 11);

// ENDING LINES
$pdf->setlinewidth(0.5);
$pdf->moveto(817, 430);
$pdf->lineto(817, $line);
$pdf->stroke();

$pdf->setlinewidth(0.5);
$pdf->moveto(25, 430);
$pdf->lineto(25, $line);
$pdf->stroke();

// middle vertical lines

$pdf->setlinewidth(0.5);
$pdf->moveto(139, 430);
$pdf->lineto(139, $line);
$pdf->stroke();

$pdf->setlinewidth(0.5);
$pdf->moveto(252, 430);
$pdf->lineto(252, $line);
$pdf->stroke();

$pdf->setlinewidth(0.5);
$pdf->moveto(365, 430);
$pdf->lineto(365, $line);
$pdf->stroke();


$pdf->setlinewidth(0.5);
$pdf->moveto(478, 430);
$pdf->lineto(478, $line);
$pdf->stroke();


$pdf->setlinewidth(0.5);
$pdf->moveto(591, 430);
$pdf->lineto(591, $line);
$pdf->stroke();


$pdf->setlinewidth(0.5);
$pdf->moveto(704, 430);
$pdf->lineto(704, $line);
$pdf->stroke();


// $traccFooter = new TraccFooter("assets/clientLogos/tracc_logo_footer.jpg", 'simpleOwnersReport', A4_LANDSCAPE);
// $traccFooter->prerender($pdf);
$pdf->end_page_ext('');

<?php

include_once 'functions/cashReportDetailedExpenditureFunction.php';

global $grandTotalNet;
global $grandTotalTax;
global $grandTotalGross;

$grandTotalNet = 0;
$grandTotalTax = 0;
$grandTotalGross = 0;

$ownerGroupName = 'Owner Payments';
$DRGroupName = 'Recoverable Payments';
$VOGroupName = ucwords(strtolower($_SESSION['country_default']['variable_outgoings'])) . ' Payments';
$NPGroupName = 'Non-Operating Payments';

// ########################### PDF GENERATION FUNCTIONS #####################################

$page++;

global $pageXCoord;
$pageXCoord = 595;
global $pageYCoord;
$pageYCoord = 842;
global $currentYCoord;
$currentYCoord = 620;
global $currentXcoord;
$currentXcoord = 50;
global $lineHeight;
$lineHeight = 12;

// ########################################################################################
// #########  EXPENSE DETAILS PER TRACC2 GROUPS - EXPOWN,EXPVO & EXPDR ####################
// ########################################################################################

// ##########################OWNER	EXPENSES#############################################
newPageDE(true, $propertyID);
$expenseListOwner = expenses_detail_subgroup($propertyID, $periodFrom, $periodTo, 'EXPOWN');
if ($expenseListOwner) {
    printGroupDE($expenseListOwner, $ownerGroupName);
}

// ###############################DIRECTLY RECOVERABLE	EXPENSES#########################

$expenseListDR = expenses_detail_subgroup($propertyID, $periodFrom, $periodTo, 'EXPDR');
if ($expenseListDR) {
    printGroupDE($expenseListDR, $DRGroupName);
}

// ############################VARIABLE OUTGOINGS	EXPENSES#################################
$expenseListVO = expenses_detail_subgroup($propertyID, $periodFrom, $periodTo, 'EXPVO');
if ($expenseListVO) {
    printGroupDE($expenseListVO, $VOGroupName);
}

//	printGrandTotal();

// ############################ NON-OPERATING EXPENSES excluding owner remittances #################################
$expenseListNP = expenses_detail_subgroup($propertyID, $periodFrom, $periodTo, 'BSPMT', true);
if ($expenseListNP) {
    printGroupDE($expenseListNP, $NPGroupName);
}

printGrandTotalDE();

<?php

include_once 'functions/page1PropertyReportFunctions.php';

// error_reporting(0);
// include('functions/fundSummary.php');

// $fundCoverPage = new fundSummaryCoverPage ($pdf, $_coverPage, $orientation, $logoPath);
// $fundCoverPage->attachObject ('header', $header);
// $fundCoverPage->preparePage ();
// $fundCoverPage->addPage ($array);
// $fundCoverPage->render ();
// $fundCoverPage->endPage ();

// color computation

$negVar = dbGetParam('OWNREPCOLO', 'NEGVAR'); // '255,0,0';
$posVar = dbGetParam('OWNREPCOLO', 'POSVAR'); // '63,153,0';

$negVarColor = explode(',', $negVar);
$posVarColor = explode(',', $posVar);

foreach ($negVarColor as $key => $item) {
    $negVarColor[$key] = round($item / 255, 2);
}

foreach ($posVarColor as $key => $item) {
    $posVarColor[$key] = round($item / 255, 2);
}


$pdf->begin_page_ext(842, 595, '');

$page_header = 'Property Financial Report';
$page++;

include 'functions/mediumReportHeader.php';

if ($logo) {
    generateLogo('landscape');
}

$text1 = 'Cash Receipts';

$pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
$pdf->showBoxed($text1, 22, 425, 275, 30, 'left', '');


// CASH RECEIPTS FIGURES
// ACTUAL
// ---------------------------------------------------------------------------
// This Period Actual
//

$ownerincome = 0;
$voinc = 0;
$recinc = 0;

$ownerincomeA = 0;
$voincA = 0;
$recincA = 0;

$withFund = false;
// #########################OWNER INCOME CURRENT PERIOD#####################################


$ownerinc_result = dbTotalIncomePerGroup($propertyID, $periodFrom, $periodTo, 'INCOWN');
$owner_income = $ownerinc_result['net_amount'];
$ownerincomePOS = $owner_income;


$recinc = dbTotalIncomePerGroup($propertyID, $periodFrom, $periodTo, 'INCDR');
$recincPOS = $recinc['net_amount'];

$voinc = dbTotalIncomePerGroup($propertyID, $periodFrom, $periodTo, 'INCVO');
$voincPOS = $voinc['net_amount'];

$ownerincome = $ownerincome + $owner_income;
// $ownerincomePOS = $ownerincome*(-1);

$totalAllIncomePOS = $ownerincomePOS + $voincPOS + $recincPOS;
$totalAllIncome1 = $totalAllIncomePOS;

// DISPLAY FIGURES
$ownerincome_display = formatting($ownerincomePOS, 2);
$ownerincome_display_rec = $ownerincome_display;
$voinc_display = formatting($voincPOS, 2);
// print "voincPOS $voincPOS<p>";
$voinc_display_rec = $voinc_display;


// echo "$totalAllIncomePOS <br />";


$recinc_display = formatting($recincPOS, 2);
$recinc_display_rec = $recinc_display;
// print "<font color=blue>recinc_display_rec = $recinc_display</font><p>";
$totalAllIncome_display = formatting($totalAllIncomePOS, 2);
$totalAllIncome_display_rec = $totalAllIncome_display;

// ##############################  PERIODS  ##########################################
// Retrieve periods
$periodsSQL = '
	SELECT
		pmcp_period, pmcp_year
	FROM
		pmcp_prop_cal
	WHERE
		pmcp_start_dt BETWEEN CONVERT(datetime, ?, 103) AND CONVERT(datetime, ?, 103)
		AND pmcp_end_dt BETWEEN CONVERT(datetime, ?, 103) AND CONVERT(datetime, ?, 103)
		AND pmcp_prop = ?';
$periods = $dbh->executeSet(
    $periodsSQL,
    false,
    true,
    [$periodFrom, $periodTo, $periodFrom, $periodTo, $propertyID]
);
$pmcp_period = $pmcp_year = null;
foreach ($periods as $v) {
    $pmcp_period[] = $v['pmcp_period'];
    $pmcp_year[] = $v['pmcp_year'];
}
$pmcp_period = array_unique($pmcp_period);
$pmcp_year = array_unique($pmcp_year);

// ##############################BUDGET INCOME Current Period##########################################
// -----------BUDGET FIGURES ---------------------------------
$parambudgetINCOWNSQL = [];
$budgetINCOWNSQL = '
	SELECT
		COALESCE(SUM(pmrp_b_c_amt), 0) as amount
	FROM
		pmrp_b_rev_per
	WHERE
		pmrp_prop = ' . addSQLParam($parambudgetINCOWNSQL, $propertyID) . '
		AND pmrp_per IN (' . addSQLParam($parambudgetINCOWNSQL, $pmcp_period) . ') 
		AND pmrp_year IN (' . addSQLParam($parambudgetINCOWNSQL, $pmcp_year) . ") 
		AND pmrp_acc IN (SELECT pmcg_acc FROM pmcg_chart_grp WHERE pmcg_grp = 'TRACC2' AND pmcg_subgrp = 'INCOWN')";
// echo "Second BUDGET:" . $budgetINCOWNSQL;

$budgetINCOWN = $dbh->executeScalar($budgetINCOWNSQL, $parambudgetINCOWNSQL);

$budgetINCOWN_display = formatting($budgetINCOWN, 2);
$budgetINCOWN_display_rec = $budgetINCOWN_display;
$paramsbudgetINCRECSQL = [];
$budgetINCRECSQL = 'SELECT COALESCE(SUM(pmrp_b_c_amt), 0) AS amount
		FROM pmrp_b_rev_per
		WHERE (pmrp_prop = ' . addSQLParam($paramsbudgetINCRECSQL, $propertyID) . ') 
		AND (pmrp_per IN (' . addSQLParam($paramsbudgetINCRECSQL, $pmcp_period) . ')) 
				AND (pmrp_year IN (' . addSQLParam($paramsbudgetINCRECSQL, $pmcp_year) . ")) 
				AND pmrp_acc IN (SELECT pmcg_acc FROM pmcg_chart_grp WHERE pmcg_grp = 'TRACC2' AND pmcg_subgrp = 'INCDR')";
// echo "6th BUDGET:" . $budgetINCRECSQL;
$budgetINCDR = $dbh->executeScalar($budgetINCRECSQL, $paramsbudgetINCRECSQL);

$budgetINCDR_display = formatting($budgetINCDR, 2);
$budgetINCDR_display_rec = $budgetINCDR_display;
$paramsbudgetINCVOSQL = [];
$budgetINCVOSQL = 'SELECT COALESCE(SUM(pmrp_b_c_amt), 0) AS amount
		FROM pmrp_b_rev_per
		WHERE (pmrp_prop = ' . addSQLParam($paramsbudgetINCVOSQL, $propertyID) . ') 
		AND (pmrp_per IN (' . addSQLParam($paramsbudgetINCVOSQL, $pmcp_period) . ')) 
				AND (pmrp_year IN (' . addSQLParam($paramsbudgetINCVOSQL, $pmcp_year) . ")) 
				AND pmrp_acc IN (SELECT pmcg_acc FROM pmcg_chart_grp WHERE pmcg_grp = 'TRACC2' AND pmcg_subgrp = 'INCVO')";
// echo "9th BUDGET:" . $budgetINCVOSQL;

$budgetINCVO = $dbh->executeScalar($budgetINCVOSQL, $paramsbudgetINCVOSQL);

$budgetINCVO_display = formatting($budgetINCVO, 2);
$budgetINCVO_display_rec = $budgetINCVO_display;


// ############################Variance of actual to budget ####################################

// VARIANCE
$incOWNVar = $ownerincomePOS - $budgetINCOWN;
$incDRVar = $recincPOS - $budgetINCDR;
$voincVar = $voincPOS - $budgetINCVO;

// DISPLAY VARIANCE
$incOWNVar_display = formatting($incOWNVar, 2);
$incOWNVar_display_rec = $incOWNVar_display;
$incDRVar_display = formatting($incDRVar, 2);
$incDRVar_display_rec = $incDRVar_display;
$voincVar_display = formatting($voincVar, 2);
$voincVar_display_rec = $voincVar_display;

// VAR %

if ($budgetINCOWN == 0) {
    if ($incOWNVar == 0) {
        $incOWNVP = 0;
    } else {
        if ($incOWNVar < 0) {
            $incOWNVP = -100;
        } else {
            $incOWNVP = 100;
        }
    }
} else {
    if ($incOWNVar == 0) {
        $incOWNVP = 0;
    } else {
        $incOWNVP = $incOWNVar / $budgetINCOWN * 100;
    }
}

if ($budgetINCDR == 0) {
    if ($incDRVar == 0) {
        $incDRVP = 0;
    } else {
        if ($incDRVar < 0) {
            $incDRVP = -100;
        } else {
            $incDRVP = 100;
        }
    }
} else {
    if ($incDRVar == 0) {
        $incDRVP = 0;
    } else {
        $incDRVP = $incDRVar / $budgetINCDR * 100;
    }
}


if ($budgetINCVO == 0) {
    if ($voincVar == 0) {
        $incVOVP = 0;
    } else {
        if ($voincVar < 0) {
            $incVOVP = -100;
        } else {
            $incVOVP = 100;
        }
    }
} else {
    if ($voincVar == 0) {
        $incVOVP = 0;
    } else {
        $incVOVP = $voincVar / $budgetINCVO * 100;
    }
}

// #####################################VARIANCE CURRENT PERIOD ########################################################

// DISPLAY VAR %
if ($incOWNVP <= 1000 and $incOWNVP >= -1000) {
    $incOWNVP_display = formatting($incOWNVP, 0);
} else {
    if ($incOWNVP < -1000) {
        $incOWNVP_display = '<(1,000.00)';
    } else {
        if ($incOWNVP > 1000) {
            $incOWNVP_display = '>1,000.00';
        }
    }
}

if ($incDRVP <= 1000 and $incDRVP >= -1000) {
    $incDRVP_display = formatting($incDRVP, 0);
} else {
    if ($incDRVP < -1000) {
        $incDRVP_display = '<(1,000.00)';
    } else {
        if ($incDRVP > 1000) {
            $incDRVP_display = '>1,000.00';
        }
    }
}

if ($incVOVP <= 1000 and $incVOVP >= -1000) {
    $incVOVP_display = formatting($incVOVP, 0);
} else {
    if ($incVOVP < -1000) {
        $incVOVP_display = '<(1,000.00)';
    } else {
        if ($incVOVP > 1000) {
            $incVOVP_display = '>1,000.00';
        }
    }
}

$incOWNVP_display_rec = $incOWNVP_display;
$incDRVP_display_rec = $incDRVP_display;
$incVOVP_display_rec = $incVOVP_display;


// TOTALS DISPLAY
$totalAllBudgetIncome = $budgetINCOWN + $budgetINCDR + $budgetINCVO;
$totalAllBudgetIncome1 = $totalAllBudgetIncome;
$totalAllBudgetIncome_display = formatting($totalAllBudgetIncome, 2);
$totalAllBudgetIncome_display_rec = $totalAllBudgetIncome_display;
$totalAllVariance = $totalAllIncomePOS - $totalAllBudgetIncome;
$totalAllVarianceIncome = $totalAllVariance;
$totalAllVariance_display = formatting($totalAllVariance, 2);
$totalAllVariance_display_rec = $totalAllVariance_display;


if ($totalAllBudgetIncome == 0) {
    if ($totalAllVariance == 0) {
        $totalAllVP = 0;
    } else {
        if ($totalAllVariance < 0) {
            $totalAllVP = -100;
        } else {
            $totalAllVP = 100;
        }
    }
} else {
    if ($totalAllVariance == 0) {
        $totalAllVP = 0;
    } else {
        $totalAllVP = $totalAllVariance / $totalAllBudgetIncome * 100;
    }
}


// $totalAllVP_display = formatting($totalAllVP,0);
if ($totalAllVP <= 1000 and $totalAllVP >= -1000) {
    $totalAllVP_display = formatting($totalAllVP, 0);
} else {
    if ($totalAllVP < -1000) {
        $totalAllVP_display = '<(1,000.00)';
    } else {
        if ($totalAllVP > 1000) {
            $totalAllVP_display = '>1,000.00';
        }
    }
}

$totalAllVP_display_rec = $totalAllVP_display;
$totalAllVP_display1 = $totalAllVP_display;

// $pdf->show_xy("Actual: $budgetINCOWN_display", 22, 500);
// BUDGET FIGURES DISPLAY

$totalAllVarianceRECEIPTS_display = $totalAllVariance_display;

// #####################################ACTUAL INCOME YTD ########################################################

$ownerincome = 0;
$voinc = 0;
$recinc = 0;
$ownerincomeA = 0;
$voincA = 0;
$recincA = 0;

$ownerincomeA = dbTotalIncomePerGroup($propertyID, $startFinancialYear, $periodTo, 'INCOWN');
// $own_takeon = take_on_balance($propertyID,'INCOWN', $startFinancialYear,$periodTo);
$own_takeon = take_on_balance_INCOWN($propertyID, 'INCOWN', $startFinancialYear, $periodTo);

$ownerincomeAPOS = $ownerincomeA['net_amount'] + $own_takeon['amount'];


$voincA = dbTotalIncomePerGroup($propertyID, $startFinancialYear, $periodTo, 'INCVO');
$voincomeA = $voincA['net_amount'];
$vo_takeon = take_on_balance($propertyID, 'INCVO', $startFinancialYear, $periodTo);
$voincAPOS = $voincomeA + $vo_takeon['amount'];
$recincA = dbTotalIncomePerGroup($propertyID, $startFinancialYear, $periodTo, 'INCDR');
$recincomeA = $recincA['net_amount'];
$rec_takeon = take_on_balance($propertyID, 'INCDR', $startFinancialYear, $periodTo);
$recincAPOS = @($recincomeA + $rec_takeon['amount']);

$totalAllIncomeAPOS = $ownerincomeAPOS + $voincAPOS + $recincAPOS;

// Formatting /
$ownerincomeA_display = formatting($ownerincomeAPOS, 2);
$voincA_display = formatting($voincAPOS, 2);
$recincA_display = formatting($recincAPOS, 2);
$totalAllIncomeA_display = formatting($totalAllIncomeAPOS, 2);


// ################################################################################

// ################################################################################
// ################################################################################
// ###########################BUDGET INCOME YTD####################################
// ################################################################################

$budgetINCOWN = getBudgetIncYTD($propertyID, $periodFrom, $toPeriod, $periodTo, 'INCOWN');
// echo $budgetINCOWN;
$budgetINCOWN_display = formatting($budgetINCOWN, 2);

$budgetINCDR = getBudgetIncYTD($propertyID, $periodFrom, $toPeriod, $periodTo, 'INCDR');
$budgetINCDR_display = formatting($budgetINCDR, 2);

$budgetINCVO = getBudgetIncYTD($propertyID, $periodFrom, $toPeriod, $periodTo, 'INCVO');
$budgetINCVO_display = formatting($budgetINCVO, 2);


// echo "$budgetINCOWN  - $budgetINCDR  - $budgetINCVO  <br />";

// ###############################VARIANCE OF INCOME YTD ######################################

// VARIANCE
$incOWNVar = $ownerincomeAPOS - $budgetINCOWN;
$incDRVar = $recincAPOS - $budgetINCDR;
$voincVar = $voincAPOS - $budgetINCVO;

// DISPLAY VARIANCE
$incOWNVar_display = formatting($incOWNVar, 2);
$incDRVar_display = formatting($incDRVar, 2);
$voincVar_display = formatting($voincVar, 2);

// VAR %

if ($budgetINCOWN == 0) {
    if ($incOWNVar == 0) {
        $incOWNVP = 0;
    } else {
        if ($incOWNVar < 0) {
            $incOWNVP = -100;
        } else {
            $incOWNVP = 100;
        }
    }
} else {
    if ($incOWNVar == 0) {
        $incOWNVP = 0;
    } else {
        $incOWNVP = $incOWNVar / $budgetINCOWN * 100;
    }
}

if ($budgetINCDR == 0) {
    if ($incDRVar == 0) {
        $incDRVP = 0;
    } else {
        if ($incDRVar < 0) {
            $incDRVP = -100;
        } else {
            $incDRVP = 100;
        }
    }
} else {
    if ($incDRVar == 0) {
        $incDRVP = 0;
    } else {
        $incDRVP = $incDRVar / $budgetINCDR * 100;
    }
}


if ($budgetINCVO == 0) {
    if ($voincVar == 0) {
        $incVOVP = 0;
    } else {
        if ($voincVar < 0) {
            $incVOVP = -100;
        } else {
            $incVOVP = 100;
        }
    }
} else {
    if ($voincVar == 0) {
        $incVOVP = 0;
    } else {
        $incVOVP = $voincVar / $budgetINCVO * 100;
    }
}


// DISPLAY VAR %
// $incOWNVP_display = formatting($incOWNVP,0);
// $incDRVP_display = formatting($incDRVP,0);
// $incVOVP_display = formatting($incVOVP,0);

if ($incOWNVP <= 1000 and $incOWNVP >= -1000) {
    $incOWNVP_display = formatting($incOWNVP, 0);
} else {
    if ($incOWNVP < -1000) {
        $incOWNVP_display = '<(1,000.00)';
    } else {
        if ($incOWNVP > 1000) {
            $incOWNVP_display = '>1,000.00';
        }
    }
}

if ($incDRVP <= 1000 and $incDRVP >= -1000) {
    $incDRVP_display = formatting($incDRVP, 0);
} else {
    if ($incDRVP < -1000) {
        $incDRVP_display = '<(1,000.00)';
    } else {
        if ($incDRVP > 1000) {
            $incDRVP_display = '>1,000.00';
        }
    }
}

if ($incVOVP <= 1000 and $incVOVP >= -1000) {
    $incVOVP_display = formatting($incVOVP, 0);
} else {
    if ($incVOVP < -1000) {
        $incVOVP_display = '<(1,000.00)';
    } else {
        if ($incVOVP > 1000) {
            $incVOVP_display = '>1,000.00';
        }
    }
}


// TOTALS DISPLAY
$totalAllBudgetIncome = $budgetINCOWN + $budgetINCDR + $budgetINCVO;
$totalAllBudgetIncomeA = $totalAllBudgetIncome;
$totalAllBudgetIncomeA_display = formatting($totalAllBudgetIncome, 2);
$totalAllVariance = $totalAllIncomePOS - $totalAllBudgetIncome;
$totalAllVarianceY = $totalAllIncomeAPOS - $totalAllBudgetIncomeA;
$totalAllVarianceIncomeY = $totalAllVariance;
$totalAllVariance_display = formatting($totalAllVariance, 2);

$totalAllVarianceY_display = formatting($totalAllVarianceY, 2);


if ($totalAllBudgetIncomeA == 0) {
    if ($totalAllVarianceY == 0) {
        $totalAllVPY = 0;
    } else {
        if ($totalAllVarianceY < 0) {
            $totalAllVPY = -100;
        } else {
            $totalAllVPY = 100;
        }
    }
} else {
    if ($totalAllVarianceY == 0) {
        $totalAllVPY = 0;
    } else {
        $totalAllVPY = $totalAllVarianceY / $totalAllBudgetIncomeA * 100;
    }
}


// $totalAllVPY_display = formatting($totalAllVPY,0);
if ($totalAllVPY <= 1000 and $totalAllVPY >= -1000) {
    $totalAllVPY_display = formatting($totalAllVPY, 0);
} else {
    if ($totalAllVPY < -1000) {
        $totalAllVPY_display = '<(1,000.00)';
    } else {
        if ($totalAllVPY > 1000) {
            $totalAllVPY_display = '>1,000.00';
        }
    }
}


// #########################YEAR BUDGET INCOME FIGURES ########################################################


$budgetINCOWN = getYearBudgetInc($propertyID, $periodTo, 'INCOWN');
$budgetINCOWNY_display = formatting($budgetINCOWN, 2);

$budgetINCDR = getYearBudgetInc($propertyID, $periodTo, 'INCDR');
$budgetINCDRY_display = formatting($budgetINCDR, 2);

$budgetINCVO = getYearBudgetInc($propertyID, $periodTo, 'INCVO');
$budgetINCVOY_display = formatting($budgetINCVO, 2);


$totalAllBudgetIncome = $budgetINCOWN + $budgetINCDR + $budgetINCVO;
$totalAllBudgetIncomeYTD = $totalAllBudgetIncome;
$totalAllBudgetIncomeY_display = formatting($totalAllBudgetIncome, 2);
$totalAllBudgetIncomeYEAR_display = $totalAllBudgetIncomeY_display;


// echo "$totalAllBudgetIncome = $budgetINCOWN + $budgetINCDR + $budgetINCVO <br />";

$line = 0;
$templine = 0; // to reduce '$line' after the third line if any lines were skipped
$text2 = 'Owner Receipts';
$text3 = 'Recoverable Receipts';
$text4 = ucwords(strtolower($_SESSION['country_default']['variable_outgoings'])) . ' Receipts';
$pdf->setFontExt($_fonts['Helvetica'], 8);


if ($ownerincome_display_rec == '0.00' &&
    $ownerincomeA_display == '0.00' &&
    $budgetINCOWN_display_rec == '0.00' &&
    $budgetINCOWN_display == '0.00' &&
    $budgetINCOWNY_display == '0.00') {
    $templine++;
} else {
    $pdf->showBoxed($text2, 22, 410 - $line, 275, 30, 'left', '');
    $pdf->showBoxed($ownerincome_display_rec, 202, 410 - $line, 75, 30, 'right', '');
    $pdf->showBoxed($ownerincomeA_display, 475, 410 - $line, 75, 30, 'right', '');
    $pdf->showBoxed($budgetINCOWN_display_rec, 272, 410 - $line, 75, 30, 'right', '');
    $pdf->showBoxed($budgetINCOWN_display, 550, 410 - $line, 75, 30, 'right', '');

    if (strpos($incOWNVar_display_rec, '(') !== false) {
        $pdf->setColorExt('both', 'rgb', $negVarColor[0], $negVarColor[1], $negVarColor[2], 0);
    } else {
        $pdf->setColorExt('both', 'rgb', $posVarColor[0], $posVarColor[1], $posVarColor[2], 0);
    }
    $pdf->showBoxed($incOWNVar_display_rec, 330, 410 - $line, 75, 30, 'right', '');

    if (strpos($incOWNVar_display, '(') !== false) {
        $pdf->setColorExt('both', 'rgb', $negVarColor[0], $negVarColor[1], $negVarColor[2], 0);
    } else {
        $pdf->setColorExt('both', 'rgb', $posVarColor[0], $posVarColor[1], $posVarColor[2], 0);
    }
    $pdf->showBoxed($incOWNVar_display, 620, 410 - $line, 75, 30, 'right', '');

    if (strpos($incOWNVP_display_rec, '(') !== false) {
        $pdf->setColorExt('both', 'rgb', $negVarColor[0], $negVarColor[1], $negVarColor[2], 0);
    } else {
        $pdf->setColorExt('both', 'rgb', $posVarColor[0], $posVarColor[1], $posVarColor[2], 0);
    }
    $pdf->showBoxed($incOWNVP_display_rec, 392, 410 - $line, 75, 30, 'right', '');

    if (strpos($incOWNVP_display, '(') !== false) {
        $pdf->setColorExt('both', 'rgb', $negVarColor[0], $negVarColor[1], $negVarColor[2], 0);
    } else {
        $pdf->setColorExt('both', 'rgb', $posVarColor[0], $posVarColor[1], $posVarColor[2], 0);
    }
    $pdf->showBoxed($incOWNVP_display, 667, 410 - $line, 75, 30, 'right', '');

    if (strpos($budgetINCOWNY_display, '(') !== false) {
        $pdf->setColorExt('both', 'rgb', $negVarColor[0], $negVarColor[1], $negVarColor[2], 0);
    } else {
        $pdf->setColorExt('both', 'rgb', $posVarColor[0], $posVarColor[1], $posVarColor[2], 0);
    }
    $pdf->showBoxed($budgetINCOWNY_display, 746, 410 - $line, 75, 30, 'right', '');

    $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);

    $line = $line + 15;
}

if (
    $recinc_display_rec == '0.00' &&
    $recincA_display == '0.00' &&
    $budgetINCDR_display_rec == '0.00' &&
    $budgetINCDR_display == '0.00' &&
    $budgetINCDRY_display == '0.00') {
    $templine++;
} else {
    $pdf->showBoxed($text3, 22, 410 - $line, 275, 30, 'left', '');
    $pdf->showBoxed($recinc_display_rec, 202, 410 - $line, 75, 30, 'right', '');
    // print "<font color=blue>recinc_display_rec $recinc_display_rec</font><p>";
    $pdf->showBoxed($recincA_display, 475, 410 - $line, 75, 30, 'right', '');
    $pdf->showBoxed($budgetINCDR_display_rec, 272, 410 - $line, 75, 30, 'right', '');
    $pdf->showBoxed($budgetINCDR_display, 550, 410 - $line, 75, 30, 'right', '');

    if (strpos($incDRVar_display_rec, '(') !== false) {
        $pdf->setColorExt('both', 'rgb', $negVarColor[0], $negVarColor[1], $negVarColor[2], 0);
    } else {
        $pdf->setColorExt('both', 'rgb', $posVarColor[0], $posVarColor[1], $posVarColor[2], 0);
    }
    $pdf->showBoxed($incDRVar_display_rec, 330, 410 - $line, 75, 30, 'right', '');

    if (strpos($incDRVar_display, '(') !== false) {
        $pdf->setColorExt('both', 'rgb', $negVarColor[0], $negVarColor[1], $negVarColor[2], 0);
    } else {
        $pdf->setColorExt('both', 'rgb', $posVarColor[0], $posVarColor[1], $posVarColor[2], 0);
    }
    $pdf->showBoxed($incDRVar_display, 620, 410 - $line, 75, 30, 'right', '');

    if (strpos($incDRVP_display_rec, '(') !== false) {
        $pdf->setColorExt('both', 'rgb', $negVarColor[0], $negVarColor[1], $negVarColor[2], 0);
    } else {
        $pdf->setColorExt('both', 'rgb', $posVarColor[0], $posVarColor[1], $posVarColor[2], 0);
    }
    $pdf->showBoxed($incDRVP_display_rec, 392, 410 - $line, 75, 30, 'right', '');

    if (strpos($incDRVP_display, '(') !== false) {
        $pdf->setColorExt('both', 'rgb', $negVarColor[0], $negVarColor[1], $negVarColor[2], 0);
    } else {
        $pdf->setColorExt('both', 'rgb', $posVarColor[0], $posVarColor[1], $posVarColor[2], 0);
    }
    $pdf->showBoxed($incDRVP_display, 667, 410 - $line, 75, 30, 'right', '');

    if (strpos($budgetINCDRY_display, '(') !== false) {
        $pdf->setColorExt('both', 'rgb', $negVarColor[0], $negVarColor[1], $negVarColor[2], 0);
    } else {
        $pdf->setColorExt('both', 'rgb', $posVarColor[0], $posVarColor[1], $posVarColor[2], 0);
    }
    $pdf->showBoxed($budgetINCDRY_display, 746, 410 - $line, 75, 30, 'right', '');

    $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
    $line = $line + 15;
}


if (
    $voinc_display_rec == '0.00' &&
    $voincA_display == '0.00' &&
    $budgetINCVO_display_rec == '0.00' &&
    $budgetINCVO_display == '0.00' &&
    $budgetINCVOY_display == '0.00') {
    $templine++;
} else {
    $pdf->showBoxed($text4, 22, 410 - $line, 275, 30, 'left', '');
    $pdf->showBoxed($voinc_display_rec, 202, 410 - $line, 75, 30, 'right', '');
    // print "voinc_display_rec $voinc_display_rec<p>";
    $pdf->showBoxed($voincA_display, 475, 410 - $line, 75, 30, 'right', '');
    $pdf->showBoxed($budgetINCVO_display_rec, 272, 410 - $line, 75, 30, 'right', '');
    $pdf->showBoxed($budgetINCVO_display, 550, 410 - $line, 75, 30, 'right', '');

    if (strpos($voincVar_display_rec, '(') !== false) {
        $pdf->setColorExt('both', 'rgb', $negVarColor[0], $negVarColor[1], $negVarColor[2], 0);
    } else {
        $pdf->setColorExt('both', 'rgb', $posVarColor[0], $posVarColor[1], $posVarColor[2], 0);
    }
    $pdf->showBoxed($voincVar_display_rec, 330, 410 - $line, 75, 30, 'right', '');

    if (strpos($voincVar_display, '(') !== false) {
        $pdf->setColorExt('both', 'rgb', $negVarColor[0], $negVarColor[1], $negVarColor[2], 0);
    } else {
        $pdf->setColorExt('both', 'rgb', $posVarColor[0], $posVarColor[1], $posVarColor[2], 0);
    }
    $pdf->showBoxed($voincVar_display, 620, 410 - $line, 75, 30, 'right', '');

    if (strpos($incVOVP_display_rec, '(') !== false) {
        $pdf->setColorExt('both', 'rgb', $negVarColor[0], $negVarColor[1], $negVarColor[2], 0);
    } else {
        $pdf->setColorExt('both', 'rgb', $posVarColor[0], $posVarColor[1], $posVarColor[2], 0);
    }
    $pdf->showBoxed($incVOVP_display_rec, 392, 410 - $line, 75, 30, 'right', '');

    if (strpos($incVOVP_display, '(') !== false) {
        $pdf->setColorExt('both', 'rgb', $negVarColor[0], $negVarColor[1], $negVarColor[2], 0);
    } else {
        $pdf->setColorExt('both', 'rgb', $posVarColor[0], $posVarColor[1], $posVarColor[2], 0);
    }
    $pdf->showBoxed($incVOVP_display, 667, 410 - $line, 75, 30, 'right', '');

    if (strpos($budgetINCVOY_display, '(') !== false) {
        $pdf->setColorExt('both', 'rgb', $negVarColor[0], $negVarColor[1], $negVarColor[2], 0);
    } else {
        $pdf->setColorExt('both', 'rgb', $posVarColor[0], $posVarColor[1], $posVarColor[2], 0);
    }
    $pdf->showBoxed($budgetINCVOY_display, 746, 410 - $line, 75, 30, 'right', '');

    $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
    $line = $line + 15;
}


$pdf->setColorExt('fill', 'rgb', 0.88, 0.88, 0.88, 0);
$pdf->rect(215, 420 - $line, 609, 20);
$pdf->fill();
$pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
$pdf->setlinewidth(0.5);

$pdf->moveto(215, 440 - $line);
$pdf->lineto(824, 440 - $line);
$pdf->stroke();
$pdf->moveto(215, 420 - $line);
$pdf->lineto(824, 420 - $line);
$pdf->stroke();


// TOTALS
$pdf->showBoxed($totalAllIncome_display_rec, 202, 405 - $line, 75, 30, 'right', '');
$pdf->showBoxed($totalAllBudgetIncome_display_rec, 272, 405 - $line, 75, 30, 'right', '');
$pdf->showBoxed($totalAllVariance_display_rec, 330, 405 - $line, 75, 30, 'right', '');
$pdf->showBoxed($totalAllVP_display_rec, 392, 405 - $line, 75, 30, 'right', '');


// TOTALS
$pdf->showBoxed($totalAllIncomeA_display, 475, 405 - $line, 75, 30, 'right', '');
$pdf->showBoxed($totalAllBudgetIncomeA_display, 550, 405 - $line, 75, 30, 'right', '');
$pdf->showBoxed($totalAllVarianceY_display, 620, 405 - $line, 75, 30, 'right', '');
$pdf->showBoxed($totalAllVPY_display, 667, 405 - $line, 75, 30, 'right', '');


$pdf->showBoxed($totalAllBudgetIncomeY_display, 746, 405 - $line, 75, 30, 'right', '');


$line = $line - (40);

// ########################################################################################################
// ########################################################################################################
// ########################################################################################################
// ########################################################################################################
// ###################################CASH PAYMENTS		  ###############################################
// ########################################################################################################
// ########################################################################################################
// ########################################################################################################

$line = $line - 10;


$text1 = 'Cash Payments';

$pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
$pdf->showBoxed($text1, 22, 320 - $line, 275, 30, 'left', '');

// CASH PAYMENTS FIGURES
// ACTUAL
// ---------------------------------------------------------------------------
// This Period Actual
//

$ownerexp = 0;
$voexp = 0;
$recexp = 0;
$ownerexpA = 0;
$voexpA = 0;
$recexpA = 0;


// Current month
$ownerexpM = totalexpensesAll($propertyID, $periodFrom, $periodTo, 'EXPOWN');
$ownerexpPOS = $ownerexpM['net_amount'];


$voexpM = totalexpensesAll($propertyID, $periodFrom, $periodTo, 'EXPVO');
$voexpPOS = $voexpM['net_amount'];

$recexpM = totalexpensesAll($propertyID, $periodFrom, $periodTo, 'EXPDR');
$recexpPOS = $recexpM['net_amount'];

$totalAllExpPOS = $ownerexpPOS + $voexpPOS + $recexpPOS;
$net = $totalAllIncomePOS - $totalAllExpPOS;


// Formatting
$ownerexp_display = formatting($ownerexpPOS, 2);
$ownerexp_display_pmt = $ownerexp_display;
$ownerexpenditure = $ownerexp_display;

$voexp_display = formatting($voexpPOS, 2);
$voexp_display_pmt = $voexp_display;

$recexp_display = formatting($recexpPOS, 2);
$recexp_display_pmt = $recexp_display;

$totalAllExp_display = formatting($totalAllExpPOS, 2);
$totalAllExp_display_pmt = $totalAllExp_display;

$net_display = formatting($net, 2);
$net_displayMonth = $net_display;


$totalAllExp1 = $totalAllExpPOS;


// #############################BUDGET EXPENSES CURRENT PERIOD #####################################################
$paramsbudgetEXPOWNSQL = [];
$budgetEXPOWNSQL = 'SELECT COALESCE(SUM(pmep_b_c_amt), 0) AS amount
		FROM pmep_b_exp_per
		WHERE (pmep_prop = ' . addSQLParam($paramsbudgetEXPOWNSQL, $propertyID) . ') 
		AND (pmep_per IN (' . addSQLParam($paramsbudgetEXPOWNSQL, $pmcp_period) . ')) 
				AND (pmep_year IN (' . addSQLParam($paramsbudgetEXPOWNSQL, $pmcp_year) . ")) 
				AND (pmep_exp_acc IN
						  (SELECT pmcg_acc
							FROM pmcg_chart_grp
							WHERE (pmcg_grp = 'TRACC2') AND pmcg_subgrp = 'EXPOWN'))";

// echo "16th BUDGET:" . $budgetEXPOWNSQL;

$budgetEXPOWN = $dbh->executeScalar($budgetEXPOWNSQL, $paramsbudgetEXPOWNSQL);

$budgetEXPOWN = $budgetEXPOWN;
$budgetEXPOWN_display = formatting($budgetEXPOWN, 2);
$budgetEXPOWN_display_pmt = $budgetEXPOWN_display;
$paramsbudgetEXPDRSQL = [];
$budgetEXPDRSQL = 'SELECT COALESCE(SUM(pmep_b_c_amt), 0) AS amount
		FROM pmep_b_exp_per
		WHERE (pmep_prop = ' . addSQLParam($paramsbudgetEXPDRSQL, $propertyID) . ') 
		AND (pmep_per IN (' . addSQLParam($paramsbudgetEXPDRSQL, $pmcp_period) . ')) 
				AND (pmep_year IN (' . addSQLParam($paramsbudgetEXPDRSQL, $pmcp_year) . "))
								AND (pmep_exp_acc IN
						  (SELECT pmcg_acc
							FROM pmcg_chart_grp
							WHERE (pmcg_grp = 'TRACC2') AND pmcg_subgrp = 'EXPDR'))";
// echo "20th BUDGET:" . $budgetEXPDRSQL;

$budgetEXPDR = $dbh->executeScalar($budgetEXPDRSQL, $paramsbudgetEXPDRSQL);

$budgetEXPDR_display = formatting($budgetEXPDR, 2);
$budgetEXPDR_display_pmt = $budgetEXPDR_display;

$paramsbudgetEXPVOSQL = [];
$budgetEXPVOSQL = 'SELECT COALESCE(SUM(pmep_b_c_amt), 0) AS amount
		FROM pmep_b_exp_per
		WHERE (pmep_prop = ' . addSQLParam($paramsbudgetEXPVOSQL, $propertyID) . ') 
		AND (pmep_per IN (' . addSQLParam($paramsbudgetEXPVOSQL, $pmcp_period) . ')) 
				AND (pmep_year IN (' . addSQLParam($paramsbudgetEXPVOSQL, $pmcp_year) . "))
							   AND (pmep_exp_acc IN
						  (SELECT pmcg_acc
							FROM pmcg_chart_grp
							WHERE (pmcg_grp = 'TRACC2') AND pmcg_subgrp = 'EXPVO'))";
// echo "24th BUDGET:" . $budgetEXPVOSQL;

$budgetEXPVO = $dbh->executeScalar($budgetEXPVOSQL, $paramsbudgetEXPVOSQL);

$budgetEXPVO_display = formatting($budgetEXPVO, 2);
$budgetEXPVO_display_pmt = $budgetEXPVO_display;


// VARIANCE
$expOWNVar = $budgetEXPOWN - $ownerexpPOS;
$expDRVar = $budgetEXPDR - $recexpPOS;
$voexpVar = $budgetEXPVO - $voexpPOS;

// DISPLAY VARIANCE
$expOWNVar_display = formatting($expOWNVar, 2);
$expOWNVar_display_pmt = $expOWNVar_display;
$expDRVar_display_pmt = formatting($expDRVar, 2);
// $expDRVar_display_pmt = $expDRVar_display;
$voexpVar_display = formatting($voexpVar, 2);
$voexpVar_display_pmt = $voexpVar_display;

// VAR %
if ($budgetEXPOWN == 0) {
    if ($expOWNVar == 0) {
        $expOWNVP = 0;
    } else {
        if ($expOWNVar < 0) {
            $expOWNVP = '-100';
        } else {
            $expOWNVP = 100;
        }
    }
} else {
    $expOWNVP = $expOWNVar / $budgetEXPOWN * 100;
}

if ($budgetEXPDR == 0) {
    if ($expDRVar == 0) {
        $expDRVP = 0;
    } else {
        if ($expDRVar < 0) {
            $expDRVP = '-100';
        } else {
            $expDRVP = 100;
        }
    }
} else {
    $expDRVP = $expDRVar / $budgetEXPDR * 100;
}


if ($budgetEXPVO == 0) {
    if ($voexpVar == 0) {
        $expVOVP = 0;
    } else {
        if ($voexpVar < 0) {
            $expVOVP = '-100';
        } else {
            $expVOVP = 100;
        }
    }
} else {
    $expVOVP = $voexpVar / $budgetEXPVO * 100;
}

// DISPLAY VAR %
$expOWNVP_display = formatting($expOWNVP, 0);
$expDRVP_display = formatting($expDRVP, 0);
$expVOVP_display = formatting($expVOVP, 0);

if ($expOWNVP <= 1000 and $expOWNVP >= -1000) {
    $expOWNVP_display = formatting($expOWNVP, 0);
} else {
    if ($expOWNVP < -1000) {
        $expOWNVP_display = '<(1,000.00)';
    } else {
        if ($expOWNVP > 1000) {
            $expOWNVP_display = '>1,000.00';
        }
    }
}

if ($expDRVP <= 1000 and $expDRVP >= -1000) {
    $expDRVP_display = formatting($expDRVP, 0);
} else {
    if ($expDRVP < -1000) {
        $expDRVP_display = '<(1,000.00)';
    } else {
        if ($expDRVP > 1000) {
            $expDRVP_display = '>1,000.00';
        }
    }
}

if ($expVOVP <= 1000 and $expVOVP >= -1000) {
    $expVOVP_display = formatting($expVOVP, 0);
} else {
    if ($expVOVP < -1000) {
        $expVOVP_display = '<(1,000.00)';
    } else {
        if ($expVOVP > 1000) {
            $expVOVP_display = '>1,000.00';
        }
    }
}

$expOWNVP_display_pmt = $expOWNVP_display;
$expDRVP_display_pmt = $expDRVP_display;
$expVOVP_display_pmt = $expVOVP_display;

// TOTALS DISPLAY
$totalAllBudgetExp = $budgetEXPOWN + $budgetEXPDR + $budgetEXPVO;
$totalAllBudgetExp1 = $totalAllBudgetExp;
$totalAllBudgetExp_display = formatting($totalAllBudgetExp, 2);
$totalAllBudgetExpM_display = $totalAllBudgetExp_display;
$totalAllBudgetExp_display_pmt = $totalAllBudgetExp_display;
$totalAllVariance = $totalAllBudgetExp - $totalAllExpPOS;
$totalAllVarianceExp = $totalAllVariance;
$totalAllVariance_display = formatting($totalAllVariance, 2);
$totalAllVariance_display_pmt = $totalAllVariance_display;


if ($totalAllBudgetExp == 0) {
    if ($totalAllVariance == 0) {
        $totalAllVP = 0;
    } else {
        if ($totalAllVariance < 0) {
            $totalAllVP = '-100';
        } else {
            $totalAllVP = 100;
        }
    }
} else {
    $totalAllVP = $totalAllVariance / $totalAllBudgetExp * 100;
}


$totalAllVP_display = formatting($totalAllVP, 0);

if ($totalAllVP <= 1000 and $totalAllVP >= -1000) {
    $totalAllVP_display = formatting($totalAllVP, 0);
} else {
    if ($totalAllVP < -1000) {
        $totalAllVP_display = '<(1,000.00)';
    } else {
        if ($totalAllVP > 1000) {
            $totalAllVP_display = '>1,000.00';
        }
    }
}

$totalAllVP_display_pmt = $totalAllVP_display;
$totalAllVPExp_display = $totalAllVP_display;

// $pdf->show_xy("Actual: $budgetINCOWN_display", 22, 500);
// BUDGET FIGURES DISPLAY


// ////////////////////////////////////////////////////////////////////////////////////////////////////
// ////////////////////////////////////////////////////////////////////////////////////////////////////
// ////////////////////////////////////////////////////////////////////////////////////////////////////
// //////	YEAR TO DATE ACTUAL
// //////
// //////


$ownerexp = 0;
$voexp = 0;
$recexp = 0;
$ownerexpA = 0;
$voexpA = 0;
$recexpA = 0;


// ---------------------------------------------------------------------------
// Year To Date Actual
//


$ownerexpYear = totalexpensesAll($propertyID, $startFinancialYear, $periodTo, 'EXPOWN');
$ownerexp_takeon = take_on_balance($propertyID, 'EXPOWN', $startFinancialYear, $periodTo);
$ownerexpAPOS = $ownerexpYear['net_amount'] + $ownerexp_takeon['amount'];
// $ownerexpY = $ownerexpYr + $ownerexp_takeon['amount'];

$voexpYear = totalexpensesAll($propertyID, $startFinancialYear, $periodTo, 'EXPVO');
$voexp_takeon = take_on_balance($propertyID, 'EXPVO', $startFinancialYear, $periodTo);
$voexpAPOS = $voexpYear['net_amount'] + $voexp_takeon['amount'];
// $voexpY = $voexpYr + $voexp_takeon['amount'];

$recexpYear = totalexpensesAll($propertyID, $startFinancialYear, $periodTo, 'EXPDR');
$recexp_takeon = take_on_balance($propertyID, 'EXPDR', $startFinancialYear, $periodTo);
$recexpYr = $recexpYear['net_amount'] + $recexp_takeon['amount'];
// $recexpAPOS = $recexpYr + $recexp_takeon['amount'];

$totalAllExpAPOS = $ownerexpAPOS + $voexpAPOS + $recexpYr;
$netY = $totalAllIncomeAPOS - $totalAllExpAPOS;

// Formatting
$ownerexpA_display = formatting($ownerexpAPOS, 2);
$voexpA_display = formatting($voexpAPOS, 2);
$recexpA_display = formatting($recexpYr, 2);

$totalAllExpA_display = formatting($totalAllExpAPOS, 2);
// $net_display = formatting($netY,2);


// echo "$net_display <br />";
// $recoverableA_exp = $recexpAPOS;


// //////////////////////////////////////////////////
//	BUDGET YTD
//


$budgetEXPOWN = getBudgetExpYTD($propertyID, $toPeriod, $periodTo, 'EXPOWN');
$budgetEXPOWN_display = formatting($budgetEXPOWN, 2);

$budgetEXPDR = getBudgetExpYTD($propertyID, $toPeriod, $periodTo, 'EXPDR');
$budgetEXPDR_display = formatting($budgetEXPDR, 2);

$budgetEXPVO = getBudgetExpYTD($propertyID, $toPeriod, $periodTo, 'EXPVO');
$budgetEXPVO_display = formatting($budgetEXPVO, 2);


// VARIANCE
$expOWNVar = $budgetEXPOWN - $ownerexpAPOS;
$expDRVar = $budgetEXPDR - $recexpYr;
$voexpVar = $budgetEXPVO - $voexpAPOS;

// DISPLAY VARIANCE
$expOWNVar_display = formatting($expOWNVar, 2);
$expDRVar_display = formatting($expDRVar, 2);
$voexpVar_display = formatting($voexpVar, 2);

// VAR %

if ($budgetEXPOWN == 0) {
    if ($expOWNVar == 0) {
        $expOWNVP = 0;
    } else {
        if ($expOWNVar < 0) {
            $expOWNVP = '-100';
        } else {
            $expOWNVP = 100;
        }
    }
} else {
    $expOWNVP = $expOWNVar / $budgetEXPOWN * 100;
}


if ($budgetEXPDR == 0) {
    if ($expDRVar == 0) {
        $expDRVP = 0;
    } else {
        if ($expDRVar < 0) {
            $expDRVP = '-100';
        } else {
            $expDRVP = 100;
        }
    }
} else {
    $expDRVP = $expDRVar / $budgetEXPDR * 100;
}


if ($budgetEXPVO == 0) {
    if ($voexpVar == 0) {
        $expVOVP = 0;
    } else {
        if ($voexpVar < 0) {
            $expVOVP = '-100';
        } else {
            $expVOVP = 100;
        }
    }
} else {
    $expVOVP = $voexpVar / $budgetEXPVO * 100;
}


// DISPLAY VAR %
$expOWNVP_display = formatting($expOWNVP, 0);
$expDRVP_display = formatting($expDRVP, 0);
$expVOVP_display = formatting($expVOVP, 0);

if ($expOWNVP <= 1000 and $expOWNVP >= -1000) {
    $expOWNVP_display = formatting($expOWNVP, 0);
} else {
    if ($expOWNVP < -1000) {
        $expOWNVP_display = '<(1,000.00)';
    } else {
        if ($expOWNVP > 1000) {
            $expOWNVP_display = '>1,000.00';
        }
    }
}

if ($expDRVP <= 1000 and $expDRVP >= -1000) {
    $expDRVP_display = formatting($expDRVP, 0);
} else {
    if ($expDRVP < -1000) {
        $expDRVP_display = '<(1,000.00)';
    } else {
        if ($expDRVP > 1000) {
            $expDRVP_display = '>1,000.00';
        }
    }
}

if ($expVOVP <= 1000 and $expVOVP >= -1000) {
    $expVOVP_display = formatting($expVOVP, 0);
} else {
    if ($expVOVP < -1000) {
        $expVOVP_display = '<(1,000.00)';
    } else {
        if ($expVOVP > 1000) {
            $expVOVP_display = '>1,000.00';
        }
    }
}


// TOTALS DISPLAY
$totalAllBudgetExp = $budgetEXPOWN + $budgetEXPDR + $budgetEXPVO;
$totalAllBudgetExpA = $totalAllBudgetExp;
$totalAllBudgetExp_display = formatting($totalAllBudgetExp, 2);


// ##############################YEAR BUDGET EXPENSE FIGURES #######################################################


$budgetEXPOWN = getYearBudgetExp($propertyID, $periodTo, 'EXPOWN');
$budgetEXPOWNY_display = formatting($budgetEXPOWN, 2);

$budgetEXPDR = getYearBudgetExp($propertyID, $periodTo, 'EXPDR');
$budgetEXPDRY_display = formatting($budgetEXPDR, 2);

$budgetEXPVO = getYearBudgetExp($propertyID, $periodTo, 'EXPVO');
$budgetEXPVOY_display = formatting($budgetEXPVO, 2);


$totalAllBudgetExp = $budgetEXPOWN + $budgetEXPDR + $budgetEXPVO;
$totalAllBudgetExpY_display = formatting($totalAllBudgetExp, 2);
$totalAllBudgetExpYTD = $totalAllBudgetExp;
$totalAllBudgetExpYTD_display = formatting($totalAllBudgetExpYTD, 2);

$totalAllVariance = $totalAllBudgetExpA - $totalAllExpAPOS;
$totalAllVarianceExpY = $totalAllVariance;
$totalAllVariance_display = formatting($totalAllVariance, 2);


if ($totalAllBudgetExpA == 0) {
    if ($totalAllVariance == 0) {
        $totalAllVP = 0;
    } else {
        if ($totalAllVariance < 0) {
            $totalAllVP = '-100';
        } else {
            $totalAllVP = 100;
        }
    }
} else {
    $totalAllVP = $totalAllVariance / $totalAllBudgetExpA * 100;
}


$totalAllVP_display = formatting($totalAllVP, 0);
$totalAllVPExpY_display = $totalAllVP_display;


$pdf->setFontExt($_fonts['Helvetica'], 8);
$text2 = 'Owner Expenditure';
$text3 = 'Recoverable Expenditure';
$text4 = ucwords(strtolower($_SESSION['country_default']['variable_outgoings'])) . ' Expenditure';


if ($ownerexp_display_pmt == '0.00' &&
    $budgetEXPOWN_display_pmt == '0.00' &&
    $ownerexpA_display == '0.00' &&
    $budgetEXPOWN_display == '0.00' &&
    $budgetEXPOWNY_display == '0.00') {
    $line = $line - 15;
} else {
    $pdf->showBoxed($text2, 22, 305 - $line, 275, 30, 'left', '');
    $pdf->showBoxed($ownerexp_display_pmt, 202, 305 - $line, 75, 30, 'right', '');
    $pdf->showBoxed($ownerexpA_display, 475, 305 - $line, 75, 30, 'right', '');
    $pdf->showBoxed($budgetEXPOWN_display_pmt, 272, 305 - $line, 75, 30, 'right', '');
    $pdf->showBoxed($budgetEXPOWN_display, 550, 305 - $line, 75, 30, 'right', '');

    if (strpos($expOWNVar_display_pmt, '(') !== false) {
        $pdf->setColorExt('both', 'rgb', $negVarColor[0], $negVarColor[1], $negVarColor[2], 0);
    } else {
        $pdf->setColorExt('both', 'rgb', $posVarColor[0], $posVarColor[1], $posVarColor[2], 0);
    }
    $pdf->showBoxed($expOWNVar_display_pmt, 330, 305 - $line, 75, 30, 'right', '');

    if (strpos($expOWNVar_display, '(') !== false) {
        $pdf->setColorExt('both', 'rgb', $negVarColor[0], $negVarColor[1], $negVarColor[2], 0);
    } else {
        $pdf->setColorExt('both', 'rgb', $posVarColor[0], $posVarColor[1], $posVarColor[2], 0);
    }
    $pdf->showBoxed($expOWNVar_display, 620, 305 - $line, 75, 30, 'right', '');

    if (strpos($expOWNVP_display_pmt, '(') !== false) {
        $pdf->setColorExt('both', 'rgb', $negVarColor[0], $negVarColor[1], $negVarColor[2], 0);
    } else {
        $pdf->setColorExt('both', 'rgb', $posVarColor[0], $posVarColor[1], $posVarColor[2], 0);
    }
    $pdf->showBoxed($expOWNVP_display_pmt, 392, 305 - $line, 75, 30, 'right', '');

    if (strpos($expOWNVP_display, '(') !== false) {
        $pdf->setColorExt('both', 'rgb', $negVarColor[0], $negVarColor[1], $negVarColor[2], 0);
    } else {
        $pdf->setColorExt('both', 'rgb', $posVarColor[0], $posVarColor[1], $posVarColor[2], 0);
    }
    $pdf->showBoxed($expOWNVP_display, 667, 305 - $line, 75, 30, 'right', '');

    if (strpos($budgetEXPOWNY_display, '(') !== false) {
        $pdf->setColorExt('both', 'rgb', $negVarColor[0], $negVarColor[1], $negVarColor[2], 0);
    } else {
        $pdf->setColorExt('both', 'rgb', $posVarColor[0], $posVarColor[1], $posVarColor[2], 0);
    }
    $pdf->showBoxed($budgetEXPOWNY_display, 746, 305 - $line, 75, 30, 'right', '');

    $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
}

if ($recexp_display_pmt == '0.00' &&
    $budgetEXPDR_display_pmt == '0.00' &&
    $recexpA_display == '0.00' &&
    $budgetEXPDR_display == '0.00' &&
    $budgetEXPDRY_display == '0.00') {
    $line = $line - 15;
} else {
    $pdf->showBoxed($text3, 22, 290 - $line, 275, 30, 'left', '');
    $pdf->showBoxed($recexp_display_pmt, 202, 290 - $line, 75, 30, 'right', '');
    $pdf->showBoxed($recexpA_display, 475, 290 - $line, 75, 30, 'right', '');
    $pdf->showBoxed($budgetEXPDR_display_pmt, 272, 290 - $line, 75, 30, 'right', '');
    $pdf->showBoxed($budgetEXPDR_display, 550, 290 - $line, 75, 30, 'right', '');

    if (strpos($expDRVar_display_pmt, '(') !== false) {
        $pdf->setColorExt('both', 'rgb', $negVarColor[0], $negVarColor[1], $negVarColor[2], 0);
    } else {
        $pdf->setColorExt('both', 'rgb', $posVarColor[0], $posVarColor[1], $posVarColor[2], 0);
    }
    $pdf->showBoxed($expDRVar_display_pmt, 330, 290 - $line, 75, 30, 'right', '');

    if (strpos($expDRVar_display, '(') !== false) {
        $pdf->setColorExt('both', 'rgb', $negVarColor[0], $negVarColor[1], $negVarColor[2], 0);
    } else {
        $pdf->setColorExt('both', 'rgb', $posVarColor[0], $posVarColor[1], $posVarColor[2], 0);
    }
    $pdf->showBoxed($expDRVar_display, 620, 290 - $line, 75, 30, 'right', '');

    if (strpos($expDRVP_display_pmt, '(') !== false) {
        $pdf->setColorExt('both', 'rgb', $negVarColor[0], $negVarColor[1], $negVarColor[2], 0);
    } else {
        $pdf->setColorExt('both', 'rgb', $posVarColor[0], $posVarColor[1], $posVarColor[2], 0);
    }
    $pdf->showBoxed($expDRVP_display_pmt, 392, 290 - $line, 75, 30, 'right', '');

    if (strpos($expDRVP_display, '(') !== false) {
        $pdf->setColorExt('both', 'rgb', $negVarColor[0], $negVarColor[1], $negVarColor[2], 0);
    } else {
        $pdf->setColorExt('both', 'rgb', $posVarColor[0], $posVarColor[1], $posVarColor[2], 0);
    }
    $pdf->showBoxed($expDRVP_display, 667, 290 - $line, 75, 30, 'right', '');

    if (strpos($budgetEXPDRY_display, '(') !== false) {
        $pdf->setColorExt('both', 'rgb', $negVarColor[0], $negVarColor[1], $negVarColor[2], 0);
    } else {
        $pdf->setColorExt('both', 'rgb', $posVarColor[0], $posVarColor[1], $posVarColor[2], 0);
    }
    $pdf->showBoxed($budgetEXPDRY_display, 746, 290 - $line, 75, 30, 'right', '');

    $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
}

if ($voexp_display_pmt == '0.00' &&
    $budgetEXPVO_display_pmt == '0.00' &&
    $voexpA_display == '0.00' &&
    $budgetEXPVO_display == '0.00' &&
    $budgetEXPVOY_display == '0.00') {
    $line = $line - 15;
} else {
    $pdf->showBoxed($text4, 22, 275 - $line, 275, 30, 'left', '');
    $pdf->showBoxed($voexp_display_pmt, 202, 275 - $line, 75, 30, 'right', '');
    $pdf->showBoxed($voexpA_display, 475, 275 - $line, 75, 30, 'right', '');
    $pdf->showBoxed($budgetEXPVO_display_pmt, 272, 275 - $line, 75, 30, 'right', '');
    $pdf->showBoxed($budgetEXPVO_display, 550, 275 - $line, 75, 30, 'right', '');

    if (strpos($voexpVar_display_pmt, '(') !== false) {
        $pdf->setColorExt('both', 'rgb', $negVarColor[0], $negVarColor[1], $negVarColor[2], 0);
    } else {
        $pdf->setColorExt('both', 'rgb', $posVarColor[0], $posVarColor[1], $posVarColor[2], 0);
    }
    $pdf->showBoxed($voexpVar_display_pmt, 330, 275 - $line, 75, 30, 'right', '');

    if (strpos($voexpVar_display, '(') !== false) {
        $pdf->setColorExt('both', 'rgb', $negVarColor[0], $negVarColor[1], $negVarColor[2], 0);
    } else {
        $pdf->setColorExt('both', 'rgb', $posVarColor[0], $posVarColor[1], $posVarColor[2], 0);
    }
    $pdf->showBoxed($voexpVar_display, 620, 275 - $line, 75, 30, 'right', '');

    if (strpos($expVOVP_display_pmt, '(') !== false) {
        $pdf->setColorExt('both', 'rgb', $negVarColor[0], $negVarColor[1], $negVarColor[2], 0);
    } else {
        $pdf->setColorExt('both', 'rgb', $posVarColor[0], $posVarColor[1], $posVarColor[2], 0);
    }
    $pdf->showBoxed($expVOVP_display_pmt, 392, 275 - $line, 75, 30, 'right', '');

    if (strpos($expVOVP_display, '(') !== false) {
        $pdf->setColorExt('both', 'rgb', $negVarColor[0], $negVarColor[1], $negVarColor[2], 0);
    } else {
        $pdf->setColorExt('both', 'rgb', $posVarColor[0], $posVarColor[1], $posVarColor[2], 0);
    }
    $pdf->showBoxed($expVOVP_display, 667, 275 - $line, 75, 30, 'right', '');

    if (strpos($budgetEXPVOY_display, '(') !== false) {
        $pdf->setColorExt('both', 'rgb', $negVarColor[0], $negVarColor[1], $negVarColor[2], 0);
    } else {
        $pdf->setColorExt('both', 'rgb', $posVarColor[0], $posVarColor[1], $posVarColor[2], 0);
    }
    $pdf->showBoxed($budgetEXPVOY_display, 746, 275 - $line, 75, 30, 'right', '');
}


// TOTALS


$pdf->setColorExt('fill', 'rgb', 0.88, 0.88, 0.88, 0);
$pdf->rect(215, 270 - $line, 609, 20);
$pdf->fill();
$pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
$pdf->setlinewidth(0.5);
$pdf->moveto(215, 290 - $line);
$pdf->lineto(824, 290 - $line);
$pdf->stroke();
$pdf->moveto(215, 270 - $line);
$pdf->lineto(824, 270 - $line);
$pdf->stroke();


$pdf->showBoxed($totalAllExp_display_pmt, 202, 255 - $line, 75, 30, 'right', '');
$pdf->showBoxed($totalAllBudgetExp_display_pmt, 272, 255 - $line, 75, 30, 'right', '');
$pdf->showBoxed($totalAllVariance_display_pmt, 330, 255 - $line, 75, 30, 'right', '');
$pdf->showBoxed($totalAllVP_display_pmt, 392, 255 - $line, 75, 30, 'right', '');


$totalAllBudgetExpY_display = $totalAllBudgetExp_display;

// TOTALS
$pdf->showBoxed($totalAllExpA_display, 475, 255 - $line, 75, 30, 'right', '');
$pdf->showBoxed($totalAllBudgetExp_display, 550, 255 - $line, 75, 30, 'right', '');
$pdf->showBoxed($totalAllVariance_display, 620, 255 - $line, 75, 30, 'right', '');
$pdf->showBoxed($totalAllVP_display, 667, 255 - $line, 75, 30, 'right', '');


$pdf->showBoxed($totalAllBudgetExpYTD_display, 746, 255 - $line, 75, 30, 'right', '');


// NET CASH INFLOWS/(OUTFLOWS)
$net = $totalAllIncome1 - $totalAllExp1;
$netBudget = $totalAllBudgetIncome1 - $totalAllBudgetExp1;
$netVar = $net - $netBudget;


if ($netBudget == 0) {
    if ($netVar == 0) {
        $netVarP = 0;
    } else {
        if ($netVar < 0) {
            $netVarP = '-100';
        } else {
            $netVarP = 100;
        }
    }
} else {
    $netVarP = $netVar / $netBudget * 100;
}


// DISPLAY FIGURES
// $net_display = formatting($net);
$netBudget_display = formatting($netBudget);
$netVar_display = formatting($netVar);
$netVarP_display = formatting($netVarP);


$text1 = 'Net Cash Excluding ' . $_SESSION['country_default']['tax_label'];
$pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
$pdf->showBoxed($text1, 22, 225 - $line, 275, 30, 'left', '');


$pdf->setColorExt('fill', 'rgb', 0.88, 0.88, 0.88, 0);
$pdf->rect(215, 240 - $line, 609, 20);
$pdf->fill();
$pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
$pdf->setlinewidth(0.5);
$pdf->moveto(215, 260 - $line);
$pdf->lineto(824, 260 - $line);
$pdf->stroke();

$pdf->moveto(18, 240 - $line);
$pdf->lineto(824, 240 - $line);
$pdf->stroke();

$pdf->moveto(215, 515);
$pdf->lineto(215, 240 - $line);
$pdf->stroke();
$pdf->moveto(470, 515);
$pdf->lineto(470, 240 - $line);
$pdf->stroke();
$pdf->moveto(745, 515);
$pdf->lineto(745, 240 - $line);
$pdf->stroke();

$pdf->moveto(18, 240 - $line);
$pdf->lineto(18, 515);
$pdf->stroke();
// was 110 not 240
$pdf->moveto(824, 240 - $line);
$pdf->lineto(824, 515);
$pdf->stroke();


$pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
$pdf->showBoxed($net_display, 202, 225 - $line, 75, 30, 'right', '');
$pdf->showBoxed($netBudget_display, 272, 225 - $line, 75, 30, 'right', '');
$pdf->showBoxed($netVar_display, 330, 225 - $line, 75, 30, 'right', '');
$netVarP_display = $netVarP < -1000 ? '<(1,000.00)' : $netVarP_display;
$netVarP_display = $netVarP > 1000 ? '>1,000.00' : $netVarP_display;
$pdf->showBoxed($netVarP_display, 392, 225 - $line, 75, 30, 'right', '');


// NET CASH INFLOWS/(OUTFLOWS) YTD
$netA = $totalAllIncomeAPOS - $totalAllExpAPOS;
$netBudgetA = $totalAllBudgetIncomeA - $totalAllBudgetExpA;
$netVarA = $netA - $netBudgetA;


if ($netBudgetA == 0) {
    if ($netVarA == 0) {
        $netVarPA = 0;
    } else {
        if ($netVarA < 0) {
            $netVarPA = '-100';
        } else {
            $netVarPA = 100;
        }
    }
} else {
    $netVarPA = $netVarA / $netBudgetA * 100;
}


// DISPLAY FIGURES
$netA_display = formatting($netA);
$netBudgetA_display = formatting($netBudgetA);
$netVarA_display = formatting($netVarA);
$netVarPA_display = formatting($netVarPA);


$pdf->showBoxed($netA_display, 475, 225 - $line, 75, 30, 'right', '');
$pdf->showBoxed($netBudgetA_display, 550, 225 - $line, 75, 30, 'right', '');
$pdf->showBoxed($netVarA_display, 620, 225 - $line, 75, 30, 'right', '');
$netVarPA_display = $netVarPA < -1000 ? '<(1,000.00)' : $netVarPA_display;
$netVarPA_display = $netVarPA > 1000 ? '>1,000.00' : $netVarPA_display;
$pdf->showBoxed($netVarPA_display, 667, 225 - $line, 75, 30, 'right', '');

// NET BUDGET YEAR
$netB = $totalAllBudgetIncomeYTD - $totalAllBudgetExpYTD;

$netB_display = formatting($netB);


$pdf->showBoxed($netB_display, 746, 225 - $line, 75, 30, 'right', '');


$pdf->setFontExt($_fonts['Helvetica'], 8);
// ########################################################################
// ###################### GST RECEIVED AND PAID ###########################
// ########################################################################

$tax_income = totalincome($propertyID, $periodFrom, $periodTo);
$tax_incM = $tax_income['tax_amount'];
$gst = $tax_incM * (-1);
$gst_display = formatting($gst, 2); // echo "$gst_display <br />";

$tax_Aincome = totalincome($propertyID, $startFinancialYear, $periodTo);
$gstA = $tax_Aincome['tax_amount'] * (-1);


$gst_takeon = take_on_balanceGST($propertyID, $startFinancialYear, $periodTo);

// problem here multiplying 0 by -1	- should be done in the function
// $gstAOPENBAL = $gst_takeon['amount'];
$gstAOPENBAL = $gst_takeon;
$gstA = $gstA + $gstAOPENBAL;
$gstA_display = formatting($gstA, 2);

// echo "echo $gstA_display <br />";


// ################################################################################
// ###############################GST PAID ########################################
// ################################################################################


$ownerexpMFund = totalexpensesFund($propertyID, $periodFrom, $periodTo, 'EXPOWN');
$voexpMFund = totalexpensesFund($propertyID, $periodFrom, $periodTo, 'EXPVO');
$recexpMFund = totalexpensesFund($propertyID, $periodFrom, $periodTo, 'EXPDR');
$ownerexpYearFund = totalexpensesFund($propertyID, $startFinancialYear, $periodTo, 'EXPOWN');
$voexpYearFund = totalexpensesFund($propertyID, $startFinancialYear, $periodTo, 'EXPVO');
$recexpYearFund = totalexpensesFund($propertyID, $startFinancialYear, $periodTo, 'EXPDR');
$noneop_pay_duringM = totalexpensesAll($propertyID, $periodFrom, $periodTo, 'BSPMT'); // trim($query_result["amount"]);
$noneop_pay_duringY = totalexpensesAll($propertyID, $startFinancialYear, $periodTo, 'BSPMT');

// GST paid
$ownerexp_GST = $ownerexpM['tax_amount']; // +$ownerexpMFund['tax_amount'];
$voexp_GST = $voexpM['tax_amount']; // +$voexpMFund['tax_amount'];
$recexp_GST = $recexpM['tax_amount']; // +$recexpMFund['tax_amount'];
$ownerexpYr_GST = $ownerexpYear['tax_amount']; // +$ownerexpYearFund['tax_amount'];
$voexpYr_GST = $voexpYear['tax_amount']; // +$voexpYearFund['tax_amount'];
$recexpYr_GST = $recexpYear['tax_amount']; // +$recexpYearFund['tax_amount'];

$gstpaid = -$ownerexp_GST - $voexp_GST - $recexp_GST - +$noneop_pay_duringM['tax_amount'];
$gstpaid_display = formatting($gstpaid, 2);

// echo "$gstpaid_display <br />";


// ####################################################################################
// #########  GST YTD NOTE: NEED TO INCLUDE GROUP  pmcg_subgrp = 'EXPGST' #############
// ####################################################################################

$openBalGST_pd = take_on_balanceGST_paid($propertyID, $startFinancialYear, $periodTo);
// $openBalGST_exp = $openBalGST_pd ['amount'];
$openBalGST_exp = $openBalGST_pd;
$gstpaidA = @(-$ownerexpYr_GST - $voexpYr_GST - $recexpYr_GST - $openBalGST_exp - +$noneop_pay_duringY['tax_amount']);
$gstpaidA_display = formatting($gstpaidA, 2);

// ##GST paid to owner
$remitgstMnt = GST_remitted($propertyID, 'EXPGST', $periodFrom, $periodTo);
$remitgstM = $remitgstMnt * (-1);
$remitgst_display = formatting($remitgstM, 2);

$remitgstYear = GST_remitted($propertyID, 'EXPGST', $startFinancialYear, $periodTo);
$remitgstYr = $remitgstYear * (-1);
$remitgstA_display = formatting($remitgstYr, 2);

// echo "  $remitgst_display - $remitgstA_display <br />";


$gst_subtotal = $gst + $gstpaid + $remitgstM;
$gst_subtotalA = $gstA + $gstpaidA + $remitgstYr;

$gst_subtotal_display = $gst_subtotal; // * (-1);
$gst_subtotalA_display = $gst_subtotalA; // * (-1);
$gst_subtotal_display = formatting($gst_subtotal_display, 2);
$gst_subtotalA_display = formatting($gst_subtotalA_display, 2);

$bas_gst_received = $gst;
$bas_gst_paid = $gstpaid;


$line = -12;
$pdf->setFontExt($_fonts['Helvetica'], 8);
if ($gst_display == '0.00' && $gstA_display == '0.00') {
    $line = $line + 10;
} else {
    $pdf->showBoxed(
        $_SESSION['country_default']['tax_label'] . ' Received on Receipts',
        22,
        215 + $line,
        175,
        30,
        'left',
        ''
    );
    $pdf->showBoxed($gst_display, 202, 215 + $line, 75, 30, 'right', '');
    $pdf->showBoxed($gstA_display, 475, 215 + $line, 75, 30, 'right', '');
}
if ($gstpaid_display == '0.00' && $gstpaidA_display == '0.00') {
    $line = $line + 10;
} else {
    $pdf->showBoxed(
        'Less: ' . $_SESSION['country_default']['tax_label'] . ' Paid on Payments',
        22,
        200 + $line,
        275,
        30,
        'left',
        ''
    );
    $pdf->showBoxed($gstpaid_display, 202, 200 + $line, 75, 30, 'right', '');
    $pdf->showBoxed($gstpaidA_display, 475, 200 + $line, 75, 30, 'right', '');
}
if ($remitgst_display == '0.00' && $remitgstA_display == '0.00') {
    $line = $line + 10;
} else {
    $pdf->showBoxed(
        'Less: ' . $_SESSION['country_default']['tax_label'] . ' Remitted to Owner/ATO',
        22,
        185 + $line,
        275,
        30,
        'left',
        ''
    );
    $pdf->showBoxed($remitgst_display, 202, 185 + $line, 75, 30, 'right', '');
    $pdf->showBoxed($remitgstA_display, 475, 185 + $line, 75, 30, 'right', '');
}

// year
// if all GST is zero don't show totals
if ($gst_display == '0.00' && $gstA_display == '0.00' && $gstpaid_display == '0.00' && $gstpaidA_display == '0.00' && $remitgst_display == '0.00') {
} else {
    $pdf->showBoxed($gst_subtotal_display, 202, 172 + $line, 75, 30, 'right', '');
    $pdf->showBoxed($gst_subtotalA_display, 475, 172 + $line, 75, 30, 'right', '');
}


$pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
$pdf->showBoxed('Net Cash Operating Inflows/(Outflows)', 22, 152 + $line, 275, 30, 'left', '');
$pdf->setFontExt($_fonts['Helvetica'], 8);

$netcash = $net + $gst_subtotal;
$netcashA = $netA + $gst_subtotalA;
$netcash_display = formatting($netcash);
$netcashA_display = formatting($netcashA);


$pdf->showBoxed($netcash_display, 202, 152 + $line, 75, 30, 'right', '');
$pdf->showBoxed($netcashA_display, 475, 152 + $line, 75, 30, 'right', '');

// don't show GST lines if GST is not being shown
if ($gst_display == '0.00' && $gstA_display == '0.00' && $gstpaid_display == '0.00' && $gstpaidA_display == '0.00' && $remitgst_display == '0.00') {
} else {
    $pdf->moveto(215, 188 + $line);
    $pdf->lineto(276, 188 + $line);
    $pdf->stroke();
    $pdf->moveto(215, 202 + $line);
    $pdf->lineto(276, 202 + $line);
    $pdf->stroke();
    $pdf->moveto(490, 188 + $line);
    $pdf->lineto(550, 188 + $line);
    $pdf->stroke();
    $pdf->moveto(490, 202 + $line);
    $pdf->lineto(550, 202 + $line);
    $pdf->stroke();
}

// $pdf->moveto(220, 179-$line);
// $pdf->lineto(280, 179-$line);
// $pdf->stroke();

$pdf->moveto(215, 169 + $line);
$pdf->lineto(276, 169 + $line);
$pdf->stroke();
$pdf->moveto(215, 167 + $line);
$pdf->lineto(276, 167 + $line);
$pdf->stroke();
// $pdf->moveto(500, 179-$line);
// $pdf->lineto(560, 179-$line);
// $pdf->stroke();
$pdf->moveto(490, 169 + $line);
$pdf->lineto(550, 169 + $line);
$pdf->stroke();
$pdf->moveto(490, 167 + $line);
$pdf->lineto(550, 167 + $line);
$pdf->stroke();


// ////////////////////////////////////////////////////////////////////////////////////////////////////////
// ////////////////////////////////////////////////////////////////////////////////////////////////////////
// ////////
// ////////   CASH RECONCILIATION
// ////////
$net_displayMonth = $net_display;
$net_display = $netA_display;

$line = 65;
$pdf->setlinewidth(0.5);
$pdf->moveto(18, 215 - $line);
$pdf->lineto(827, 215 - $line);
$pdf->stroke();

$pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
$pdf->showBoxed('Cash Reconciliation', 22, 180 - $line, 275, 30, 'left', '');
$pdf->setFontExt($_fonts['Helvetica'], 8);
// echo $netcash_display;
$pdf->showBoxed($netcash_display, 202, 145 - $line, 75, 30, 'right', '');
$pdf->showBoxed($netcashA_display, 475, 145 - $line, 75, 30, 'right', '');

$pdf->moveto(215, 163 - $line);
$pdf->lineto(276, 163 - $line);
$pdf->stroke();

$pdf->moveto(490, 163 - $line);
$pdf->lineto(550, 163 - $line);
$pdf->stroke();


$pdf->showBoxed('Cash balance at beginning of period/year', 22, 160 - $line, 275, 30, 'left', '');
$pdf->showBoxed('Net Cash Operating Inflows/(Outflows)', 22, 145 - $line, 275, 30, 'left', '');


/*$pdf->setlinewidth (2);
$pdf->moveto(18, 110-$line);
$pdf->lineto(827, 110-$line);
$pdf->stroke();
  */
$pdf->setFontExt($_fonts['Helvetica'], 8);
// $pdf->showBoxed ("Printed on $date", 22, 10, 275, 30, "left", "");
$pdf->showBoxed("Page {$page}", 750, 0, 75, 30, 'right', '');
$pdf->setFontExt($_fonts['Helvetica'], 8);

// ---------------------------------------------------------------------------------------------------------------
// ---------------------------------------------------------------------------------------------------------------
//			DATE BEFORE


// determine the day before the start of the reporting period
// $datebefore = date_before_report($propertyID,$periodFrom);
// changed above to refer to $periodFrom 20 Mar 2009
// $datebefore = date_before_report($propertyID,$periodTo);


// obtain the day before the begining of the year
$dateBeforeStartYr = date_before_start_FY($propertyID, $periodTo);
if ($dateBeforeStartYr) {
    $dateBeforeStart = $dateBeforeStartYr['date'];
} else {
    $dateBeforeStart = null;
}

// #####################################################################################
// ######  NET CASH INFLOWS/(OUTFLOWS)  NET CASH INFLOWS/(OUTFLOWS)#####################
// #####################################################################################

$total_netcash = $net + $gst_subtotal;
$total_netcashA = $netY + $gst_subtotalA;

// echo "$total_netcashA = $netY + $gst_subtotalA <br />";


$total_netcash_display = formatting($total_netcash, 2);
$total_netcashA_display = formatting($total_netcashA, 2);


// ####################################################################################
// ################## CASH RECONCILIATION #############################################
// ####################################################################################


$receipts = total_cash_receipts($propertyID, $startFinancialYear, $previousPeriodTo);
$payments = total_cash_payments($propertyID, $startFinancialYear, $previousPeriodTo);

$receiptsA = total_cash_receipts($propertyID, STARTDATE, $dateBeforeStart); // echo "receiptsA - $receiptsA";
$paymentsA = total_cash_payments($propertyID, STARTDATE, $dateBeforeStart);


// $receiptsAFund = total_cash_receipts_fund($propertyID,STARTDATE,$financialPeriodToPY); // added by arjay 2017-27-07
// $receiptsFund = total_cash_receipts_fund($propertyID,$startFinancialYear,$previousPeriodTo); // added by arjay 2017-27-07
// $paymentsAFund = total_cash_payments_fund($propertyID,STARTDATE,$financialPeriodToPY); // added by arjay 2017-01-08
// $paymentsFund = total_cash_payments_fund($propertyID,$startFinancialYear,$previousPeriodTo); // added by arjay 2017-01-08
//
// $receiptsA = total_cash_receipts($propertyID,STARTDATE,$financialPeriodToPY)-$receiptsAFund; //echo "receiptsA - $receiptsA";
// $paymentsA = total_cash_payments($propertyID,STARTDATE,$financialPeriodToPY)-$paymentsAFund;
//
// $receipts =total_cash_receipts($propertyID,$startFinancialYear,$previousPeriodTo)-$receiptsFund;////////////receipts current year up to start of reporting period ::: was $datebefore
// $payments = total_cash_payments($propertyID,$startFinancialYear,$previousPeriodTo)-$paymentsFund;

// echo $receipts . ' ' . $payments . '<br/>';
// echo $receiptsA . ' ' . $paymentsA . '<br/>';


$diffY = $receipts - $payments;
$diffA = $receiptsA - $paymentsA;

// add the below section in 19 Mar 09 to take into account when reporting multiple periods starting from beginning of financial year
if ($previousPeriodTo == $dateBeforeStart) {
    $diff = $diffA;
} else {
    $diff = $diffA + $diffY;
}

$diff_display = formatting($diff);
$diffA_display = formatting($diffA);
// NOTE need also to do it to $dateBeforeStart   - day before start of fin year - above is from beginning of the year to day before reporting period

$subtotal = $diff + $total_netcash;
$subtotalA = $diffA + $total_netcashA;


// echo "subtotal($subtotal) - subtotalA($subtotalA)<br />";

$subtotal_display = formatting($subtotal);
$subtotalA_display = formatting($subtotalA);

// #####################################################################################
// -----------------------------OWNER REMITTANCES--------------------------------------
// #####################################################################################

// $category = "'EXPOWNREMI', 'BSPMTREMI'";
$category = ['EXPOWNREMI', 'BSPMTREMI'];

// Current month
$pmts_duringM_displayM = totalExpensesPerSubgroupAll($propertyID, $periodFrom, $periodTo, $category);
$pmts_duringMnt_display = $pmts_duringM_displayM['gross_amount'];
$pmts_duringM_display = formatting($pmts_duringMnt_display);

// Year to date includes take on balances
$openPMTS = take_on_balance_own_remit($propertyID, 'EXPOPT', $startFinancialYear, $periodTo);
$pmts_duringY_displayY = totalExpensesPerSubgroupAll($propertyID, $startFinancialYear, $periodTo, $category);
$pmts_duringYear_display = $pmts_duringY_displayY['gross_amount'];
$pmts_duringYr_display = $pmts_duringYear_display - $openPMTS;
$pmts_duringY_display = formatting($pmts_duringYr_display);

// Each owners remittance
$owner_remittances = expenses_detail($propertyID, $periodFrom, $periodTo, 'EXPOPT');
$count_number = count($owner_remittances ?? []);

$ownerRemittanceList = [];
foreach ($owner_remittances as $key => $each_owner_remit) {
    // this is in an array with possibly multiple lines
    $ownerRemittanceList[$key]['name'] = @substr($owner_remittances['name'], 0, 15);
    $ownerRemittanceList[$key]['amount'] = @$owner_remittances['amount'];
    // ////////NOTE WE NEED TO PUT IN SOME PDF FORMATTING IN THIS LOOP
}

$lineOffset = 0;
foreach ($ownerRemittanceList as $ownerRemittanceItem) {
    echo $ownerRemittanceItem['amount'];
    $lineOffset += 10;
}

// #####################################################################################
// -----------------------------CASH BALANCE AT THE END OF THE YEAR--------------------
// #####################################################################################


// #####################################################################################
// ###################################END###############################################
// ##############################CALCULATIONS###########################################
// #####################################################################################

$pmts_duringM = getPaymentsToOwners($propertyID, $periodFrom, $periodTo); // trim($query_result["amount"]);
$pmts_duringY = getPaymentsToOwners($propertyID, $startFinancialYear, $periodTo);
$pmts_duringMBS = getPaymentsToOwnersBSonly($propertyID, $periodFrom, $periodTo);
$pmts_duringYBS = getPaymentsToOwnersBSonly($propertyID, $startFinancialYear, $periodTo);

$pmts_duringY = $pmts_duringY - $openPMTS;

$pmts_duringM_display = $pmts_duringM; // * (-1);
$pmts_duringY_display = $pmts_duringY; // * (-1);
$pmts_duringM_display = formatting($pmts_duringM_display);
$pmts_duringY_display = formatting($pmts_duringY_display);

$pdf->showBoxed($diff_display, 202, 160 - $line, 75, 30, 'right', '');
$pdf->showBoxed($diffA_display, 475, 160 - $line, 75, 30, 'right', '');

$pdf->showBoxed($subtotal_display, 202, 130 - $line, 75, 30, 'right', '');
$pdf->showBoxed($subtotalA_display, 475, 130 - $line, 75, 30, 'right', '');

// $pdf->showBoxed ($pmts_duringM_display, 202, 88-$line, 75, 30, "right", "");
// $pdf->showBoxed ($pmts_duringY_display, 475, 88-$line, 75, 30, "right", "");


$noneop_rec_duringM = dbTotalIncomePerGroup(
    $propertyID,
    $periodFrom,
    $periodTo,
    'BSREC'
); // trim($query_result["amount"]);
$noneop_rec_duringY = dbTotalIncomePerGroup($propertyID, $startFinancialYear, $periodTo, 'BSREC');
// $noneop_rec_takeon = take_on_balance_own_remit($propertyID,'BSREC',$startFinancialYear, $periodTo);
$noneop_rec_takeon = $noneop_pay_takeon = 0;

$noneop_rec_takeon = take_on_balance($propertyID, 'BSREC', $startFinancialYear, $periodTo, 'I');
$noneop_rec_takeon = $noneop_rec_takeon['amount'];
$noneop_pay_takeon = take_on_balance($propertyID, 'BSPMT', $startFinancialYear, $periodTo, 'E');
$noneop_pay_takeon = $noneop_pay_takeon['amount'];


$noneop_pay_duringMnt_display = $noneop_pay_duringM['gross_amount'] + $noneop_pay_duringM['tax_amount'] - $pmts_duringMBS; // * (-1);
$noneop_pay_duringYer_display = $noneop_pay_duringY['gross_amount'] + $noneop_pay_duringY['tax_amount'] - $pmts_duringYBS - $noneop_pay_takeon; // * (-1);
$noneop_rec_duringMnt_display = $noneop_rec_duringM['net_amount']; // + $noneop_rec_duringM['tax_amount'];// * (-1);
$noneop_rec_duringYer_display = $noneop_rec_duringY['net_amount'] + $noneop_rec_takeon; // + $noneop_rec_duringY['tax_amount'];// * (-1);


$noneop_pay_duringM_display = formatting($noneop_pay_duringMnt_display);
$noneop_pay_duringY_display = formatting($noneop_pay_duringYer_display);
$noneop_rec_duringM_display = formatting($noneop_rec_duringMnt_display);
$noneop_rec_duringY_display = formatting($noneop_rec_duringYer_display);

if ($noneop_rec_duringMnt_display != 0 or $noneop_rec_duringYer_display != 0) {
    $pdf->showBoxed('Non-operating Receipts', 22, 118 - $line, 275, 30, 'left', '');
    $pdf->showBoxed($noneop_rec_duringM_display, 202, 118 - $line, 75, 30, 'right', '');
    $pdf->showBoxed($noneop_rec_duringY_display, 475, 118 - $line, 75, 30, 'right', '');
} else {
    $line = $line - 15;
}

if ($noneop_pay_duringMnt_display != 0 or $noneop_pay_duringYer_display != 0) {
    $pdf->showBoxed('Non-operating Payments', 22, 103 - $line, 275, 30, 'left', '');
    $pdf->showBoxed($noneop_pay_duringM_display, 202, 103 - $line, 75, 30, 'right', '');
    $pdf->showBoxed($noneop_pay_duringY_display, 475, 103 - $line, 75, 30, 'right', '');
} else {
    $line = $line - 15;
}


// Moved by: DK
$pdf->showBoxed('Payments to owner/s during period/year', 22, 88 - $line, 275, 30, 'left', '');
$pdf->showBoxed($pmts_duringM_display, 202, 88 - $line, 75, 30, 'right', '');
$pdf->showBoxed($pmts_duringY_display, 475, 88 - $line, 75, 30, 'right', '');
$pdf->moveto(215, 90 - $line);
$pdf->lineto(276, 90 - $line);
$pdf->stroke();
$pdf->moveto(215, 92 - $line);
$pdf->lineto(276, 92 - $line);
$pdf->stroke();
$pdf->moveto(490, 90 - $line);
$pdf->lineto(550, 90 - $line);
$pdf->stroke();
$pdf->moveto(490, 92 - $line);
$pdf->lineto(550, 92 - $line);
$pdf->stroke();
$pdf->moveto(215, 107 - $line);
$pdf->lineto(276, 107 - $line);
$pdf->stroke();
$pdf->moveto(490, 107 - $line);
$pdf->lineto(550, 107 - $line);
$pdf->stroke();


$cb = ($subtotal + $pmts_duringMnt_display + $noneop_rec_duringMnt_display + $noneop_pay_duringMnt_display);
$cbA = $pmts_duringYr_display + $subtotalA + $noneop_pay_duringYer_display + $noneop_rec_duringYer_display;
$cb_display = formatting($cb);
$cbA_display = formatting($cbA);

$pdf->showBoxed('Cash balance at end of period/year', 22, 73 - $line, 275, 30, 'left', '');
$pdf->showBoxed($cb_display, 202, 73 - $line, 75, 30, 'right', '');
$pdf->showBoxed($cbA_display, 475, 73 - $line, 75, 30, 'right', '');

// insert tracc footer
$traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'propertyFinancialReport', A4_LANDSCAPE);
$traccFooter->prerender($pdf);

$pdf->end_page_ext('');

// include_once('functions/propertyFinancialReportFund.php');

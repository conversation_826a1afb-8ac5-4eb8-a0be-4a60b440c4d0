<?php

function array_orderby()
{
    $args = func_get_args();
    $data = array_shift($args);
    foreach ($args as $n => $field) {
        if (is_string($field)) {
            $tmp =  [];
            foreach ($data as $key => $row) {
                $tmp[$key] = $row[$field];
            }
            $args[$n] = $tmp;
        }
    }
    $args[] = &$data;
    call_user_func_array('array_multisort', $args);

    return array_pop($args);
}

// used to sort by expiry date
function compareByExpirydate($a, $b)
{
    // $t1 = strtotime($a['agreementTo']);
    // $t2 = strtotime($b['agreementTo']);
    // return $t1 - $t2;

    $t1 = str_replace('/', '-', $a['agreementTo']);
    $t2 = str_replace('/', '-', $b['agreementTo']);

    $datetime1 = new DateTime($t1);
    $datetime2 = new DateTime($t2);

    if ($datetime1 == $datetime2) {
        return strcmp($a['propertyID'], $b['propertyID']);
    } else {
        return $datetime1 > $datetime2;
    }

}

// used to sort by expiry date
function compareByPropertyID($a, $b)
{
    return strcmp($a['propertyID'], $b['propertyID']);
}

function managementAgreementProcess($context)
{
    global $clientDirectory, $pathPrefix, $sess;
    $threshold = 3;

    if (! isPostback()) {
        $view = new MasterPage(userViews(), '/managementReports/managementAgreementProcess.html');
        $view->setSection($context['module']);
    } else {
        $view = new UserControl(userViews(), '/managementReports/managementAgreementProcess.html');
    }

    $view->bindAttributesFrom($_REQUEST);
    $validationErrors =  [];

    if ($context[IS_TASK]) {
        $view->bindAttributesFrom($context);
        extract($context);
    } else {
        $view->bindAttributesFrom($_REQUEST);
        $propertyCount = 0;
        if ($view->items['property']) {
            $propertyCount = count(deserializeParameters($view->items['property']));
        }
        $view->items['propertyCount'] = $propertyCount;
        $queue = new Queue(TASKTYPE_MANAGEMENTAGREEMENT);
        if ($propertyCount > THRESHOLD_MANAGEMENTAGREEMENT) {
            $queue->add($_SESSION['clientID'], $_SESSION['un'], 'command=managementAgreementProcess&module=mangementReports', $_REQUEST);
        }
    }


    if (($context[IS_TASK]) || ($propertyCount <= THRESHOLD_MANAGEMENTAGREEMENT)) {
        /* parameters you can pass
        - chargeType: S/D
        - includeVacant : true/false
        */
        global $totals, $propertyID, $date, $totalArea, $area, $reviewTypes, $periodSize, $property,  $occupiedArea, $vacantArea, $totalArea, $format, $propertyAreas, $divisionTypes;

        $propertyAreas =  [];

        $properties = deserializeParameters($view->items['property']);

        $date = $view->items['toDate'];
        $format = $view->items['format'];
        // ** HANDLE PROCESSING LOGIC

        $_report = dbGetReport($view->items['reportID']);
        // -- if there are parameters provided, extract them to the globals
        if (! empty($_report['reportParameters'])) {
            $parameters = (array) json_decode($_report['reportParameters']);
            extract($parameters);
        }

        $columns = dbGetReportColumns($view->items['reportID']);
        foreach ($columns as $c) {
            $columnList[] = $c['columnKey'];
        }

        $data = $data2 =  [];

        // -- IF METHOD INVOLVES PROPERTY
        if (! isEmptyArray($properties)) {
            foreach ($properties as $propertyID) {
                $agreements = dbGetAgreementsForPropertyAsAt($propertyID, $date, $view->items['method']);

                $property = dbGetPropertyDetails($propertyID);


                $row = [];
                if ($agreements) {
                    foreach ($agreements as $a) {
                        $row['propertyID'] = $propertyID;
                        $row['propertyName'] = $property['propertyName'];
                        $row['agreementDescription'] = $a['agreementDescription'];
                        $row['agreementFrom'] = $a['agreementFrom'];
                        $row['agreementTo'] = $a['agreementTo'];
                        $data[] = $row;
                    }
                } else {
                    $row['propertyID'] = $propertyID;
                    $row['propertyName'] = $property['propertyName'];
                    $row['agreementDescription'] = 'There are currently no active management agreements';
                    $row['agreementFrom'] = '';
                    $row['agreementTo'] = '';
                    $data2[] = $row;
                }
            }

            //	sort by property code
            if ($view->items['sortOption'] == 'sortPropertyCode') {
                $data = array_merge($data, $data2);
                usort($data, 'compareByPropertyID');
            }

            // sorting by expiry date
            elseif ($view->items['sortOption'] == 'sortExpiryDate') {
                usort($data, 'compareByExpirydate');
                usort($data2, 'compareByPropertyID');
                $data = array_merge($data, $data2);
            }
        }

        $logoFile = dbGetClientLogo();
        $logoPath = "assets/clientLogos/{$logoFile}";

        $_filePath =  "{$pathPrefix}{$clientDirectory}/{$format}/" . DOC_MANAGEMENTAGREEMENT . '/';
        $_downloadPath =  "{$clientDirectory}/{$format}/" . DOC_MANAGEMENTAGREEMENT;
        $file =  'ManagementAgreementReport_' . date('Ymd') . ".{$format}";
        $filePath = $_filePath . $file;
        $downloadPath = "{$_downloadPath}/{$file}";

        switch ($format) {
            case FILETYPE_PDF:
                $report = new PDFDataMultiLineReport($filePath, $logoPath, A4_LANDSCAPE);
                $report->multiLine = true;
                $report->printRowLines = true;
                $report->printColumnLines = false;
                switch ($view->items['method']) {
                    case 0: $subtitle = 'all (including historic)';
                        break;
                    case 1: $subtitle = 'current';
                        break;
                    default: $subtitle = ''; // ($view->items['merge']) ? "by Properties" : $property['propertyName'] . '(' . $propertyID  .')';
                }
                $prepared = 'as at ' . $date;
                $header = new ReportHeader($_report['reportName'], $subtitle, $prepared);
                $header->xPos = $report->hMargin;
                $header->yPos = $report->pageHeight - $report->vMargin;
                $report->attachObject('header', $header);
                $footer = new TraccFooter(null, $_report['reportName'], $report->pageSize);
                $report->attachObject('footer', $footer);
                break;
            case FILETYPE_XLS:
                // $subtitle = ' for ' . $property['propertyName'] . '(' . $propertyID  .') as at ' . $date;
                $report = new XLSDataReport($filePath, $_report['reportName']);
                $report->enableFormatting = true;
                break;
        }

        if ($format != FILETYPE_SCREEN) {
            foreach ($columns as $c) {
                if ($format == FILETYPE_XLS) {
                    $c['width'] = (int) $c['width'] / 5;
                } // -- needed to downgrade the widths used from the PDF settings
                $report->addColumn($c['columnKey'], $c['title'], $c['width'], $c['alignment']);
                $key[$c['columnKey']] = $c['subtitle'];
            }

            $report->preparePage();

            if ($format == FILETYPE_PDF) {
                $report->addKeyHeaders($key);
            } else {
                $report->renderHeader();
                // $report->renderLine ($key);
            }

            $report->renderData($data);
            // if ($format == FILETYPE_PDF) $report->renderSubTotal ($totals);
            // else $report->renderLine ($totals);
            $report->clean();
            $report->endPage();
            $report->close();
        } else {
            $view->items = $context;
            $view->items['columns'] = $columns;
            $view->items['data'] = $data;
        }

        // -- if it s a scheduled task - attach the report and email to the requester
        if ($context[IS_TASK]) {
            $email = new EmailTemplate('views/emails/basicEmail.html', SYSTEMURL);
            $attachment =  [];
            $attachment[0]['file'] = REPORTPATH . '/' . $pdfDownloadLink;
            $attachment[0]['content_type'] = 'application/pdf';
            sendMail($_SESSION['email'], $_SESSION['first_name'] . ' ' . $_SESSION['last_name'], $email->toString(), 'Report', $attachment);
            logData('Emailed report to ' . $_SESSION['email']);
            $context[TASK_COMPLETE] = true;
        }
    }

    if ($propertyCount > THRESHOLD_MANAGEMENTAGREEMENT) {
        $view->items['statusMessage'] = 'Due to its complexity, this report has been scheduled and will be emailed to you on completion!';
        $view->render();
    } else {
        $view->items['downloadPath'] = $downloadPath;
        $view->render();
    }
}

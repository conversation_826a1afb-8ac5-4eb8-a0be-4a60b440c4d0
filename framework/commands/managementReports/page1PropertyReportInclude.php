<?php

include_once 'functions/page1PropertyReportFunctions.php';

global $reportDescription;
global $periodDescription; // added this in 2 Feb 09
global $propertyName;
global $client;
// ##########################################PDF GENERATION##############################################################################
$page++;
$pdf->begin_page_ext(595, 842, '');

short_header($reportDescription);

// $line=60;
$line = -10;

$pdf->show_xy('Owner: ', 25, 750);
$pdf->continue_text('Property: ');
$pdf->continue_text('Report for: ');

$pdf->show_xy("$client", 85, 750);
$pdf->continue_text("$propertyName [$propertyID]");
$pdf->continue_text("$periodDescription");
// ///////////////////////////////////////// AGENT////////////////////////////////////////   /////////////////
$agentData = dbGetAgentDetails();
$agentDetailsNew = new agentDetails($propertyID, true);
$agentDetailsNew->bindAttributesFrom($agentData);
$agentDetailsNew->render($pdf);
// $line = 300;

// ///////////////////////////////////////// CASH RECEIPTS/////////////////////////////////////////////////////////

$pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
$pdf->showBoxed('Receipts', 30, 630 + $line, 175, 30, 'left', '');
$pdf->setFontExt($_fonts['Helvetica'], 9);
// PART1

// var_dump($income);
// var_dump($incomeYTD);
// dotFlush();
if ($income->owner || $incomeYTD->owner) {
    $pdf->setFontExt($_fonts['Helvetica'], 8);
    $pdf->showBoxed('Owner Receipts', 30, 620 + $line, 175, 30, 'left', '');
    $pdf->showBoxed(toDecimal($income->owner), 290, 620 + $line, 75, 30, 'right', '');
    $pdf->showBoxed(toDecimal($incomeYTD->owner), 440, 620 + $line, 75, 30, 'right', '');
} else {
    $line = 0;
}


if ($income->recoverable || $incomeYTD->recoverable) {
    $pdf->showBoxed('Recoverable Receipts', 30, 610 + $line, 175, 30, 'left', '');
    $pdf->showBoxed(toDecimal($income->recoverable), 290, 610 + $line, 75, 30, 'right', '');
    $pdf->showBoxed(toDecimal($incomeYTD->recoverable), 440, 610 + $line, 75, 30, 'right', '');
} else {
    $line = $line + 10;
}

if ($income->variableOutgoing || $incomeYTD->variableOutgoing) {
    $pdf->showBoxed(ucwords(strtolower($_SESSION['country_default']['variable_outgoings'])) . ' Receipts', 30, 600 + $line, 295, 30, 'left', '');
    $pdf->showBoxed(toDecimal($income->variableOutgoing), 290, 600 + $line, 75, 30, 'right', '');
    $pdf->showBoxed(toDecimal($incomeYTD->variableOutgoing), 440, 600 + $line, 75, 30, 'right', '');
} else {
    $line = $line + 10;
}

$pdf->setColorExt('fill', 'rgb', 0.88, 0.88, 0.88, 0);
$pdf->rect(265, 595 + $line, 305, 16);
$pdf->fill();
$pdf->setColorExt('fill', 'rgb', 0, 0, 0, 0);

$pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
$pdf->showBoxed(toDecimal($income->total), 290, 580 + $line, 75, 30, 'right', '');
$pdf->showBoxed(toDecimal($incomeYTD->total), 440, 580 + $line, 75, 30, 'right', '');

$pdf->setlinewidth(0.5);
$pdf->moveto(265, 595 + $line);
$pdf->lineto(570, 595 + $line);
$pdf->stroke();

$pdf->moveto(265, 611 + $line);
$pdf->lineto(570, 611 + $line);
$pdf->stroke();

// ///////////////////////////////////////////////// PDF EXPENDITURE SECTION //////////////////////////////////


$pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
$pdf->showBoxed('Payments', 30, 560 + $line, 175, 30, 'left', '');
$pdf->setFontExt($_fonts['Helvetica'], 8);

if ($expenditure->owner || $expenditureYTD->owner) {
    $pdf->showBoxed('Owner Payments', 30, 550 + $line, 275, 30, 'left', '');
    $pdf->showBoxed(toDecimal($expenditure->owner), 290, 550 + $line, 75, 30, 'right', '');
    $pdf->showBoxed(toDecimal($expenditureYTD->owner), 440, 550 + $line, 75, 30, 'right', '');
} else {
    $line = $line + 10;
}

if ($expenditure->recoverable || $expenditureYTD->recoverable) {
    $pdf->showBoxed('Recoverable Payments', 30, 540 + $line, 275, 30, 'left', '');
    $pdf->showBoxed(toDecimal($expenditure->recoverable), 290, 540 + $line, 75, 30, 'right', '');
    $pdf->showBoxed(toDecimal($expenditureYTD->recoverable), 440, 540 + $line, 75, 30, 'right', '');
} else {
    $line = $line + 10;
}

if ($expenditure->variableOutgoing || $expenditureYTD->variableOutgoing) {
    $pdf->showBoxed(ucwords(strtolower($_SESSION['country_default']['variable_outgoings'])) . ' Payments', 30, 530 + $line, 275, 30, 'left', '');
    $pdf->showBoxed(toDecimal($expenditure->variableOutgoing), 290, 530 + $line, 75, 30, 'right', '');
    $pdf->showBoxed(toDecimal($expenditureYTD->variableOutgoing), 440, 530 + $line, 75, 30, 'right', '');
} else {
    $line = $line + 10;
}

// dotFlush();
$pdf->setlinewidth(0.5);


$pdf->setColorExt('fill', 'rgb', 0.88, 0.88, 0.88, 0);
$pdf->rect(265, 525 + $line, 305, 16);
$pdf->fill();
$pdf->setColorExt('fill', 'rgb', 0, 0, 0, 0);

// ///////////////////////////////TOTAL ALL EXPENDITURE YEAR TO DATE////////////////////////////////////////////
$pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
$pdf->showBoxed(toDecimal($expenditure->total), 290, 510 + $line, 75, 30, 'right', '');
$pdf->showBoxed(toDecimal($expenditureYTD->total), 440, 510 + $line, 75, 30, 'right', '');



$pdf->moveto(265, 525 + $line);
$pdf->lineto(570, 525 + $line);
$pdf->stroke();

$pdf->moveto(265, 541 + $line);
$pdf->lineto(570, 541 + $line);
$pdf->stroke();


$pdf->setColorExt('fill', 'rgb', 0.88, 0.88, 0.88, 0);
$pdf->rect(265, 503 + $line, 305, 16);
$pdf->fill();
$pdf->setColorExt('fill', 'rgb', 0, 0, 0, 0);


$pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
$pdf->showBoxed('Net Cash excluding ' . $_SESSION['country_default']['tax_label'], 30, 488 + $line, 275, 30, 'left', '');
$pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
$pdf->showBoxed(toDecimal($net), 290, 488 + $line, 75, 30, 'right', '');
$pdf->showBoxed(toDecimal($netYTD), 440, 488 + $line, 75, 30, 'right', '');


$pdf->moveto(265, 503 + $line);
$pdf->lineto(570, 503 + $line);
$pdf->stroke();

$pdf->moveto(265, 519 + $line);
$pdf->lineto(570, 519 + $line);
$pdf->stroke();


// ///////////////////// GST PDF SECTION//////////////////////////////////////


$pdf->setFontExt($_fonts['Helvetica'], 8);
if ($tax  || $taxYTD) {
    $pdf->showBoxed($_SESSION['country_default']['tax_label'] . ' Received on Receipts', 30, 460 + $line, 175, 30, 'left', '');
    $pdf->showBoxed(toDecimal($tax), 290, 460 + $line, 75, 30, 'right', '');
    $pdf->showBoxed(toDecimal($taxYTD), 440, 460 + $line, 75, 30, 'right', '');
} else {
    $line = $line + 10;
}

if ($taxPaid || $taxPaidYTD) {
    $pdf->showBoxed($_SESSION['country_default']['tax_label'] . ' Paid on Payments', 30, 450 + $line, 275, 30, 'left', '');
    $pdf->showBoxed(toDecimal($taxPaid), 290, 450 + $line, 75, 30, 'right', '');
    $pdf->showBoxed(toDecimal($taxPaidYTD), 440, 450 + $line, 75, 30, 'right', '');
} else {
    $line = $line + 10;
}

if ($remittedTax || $remittedTaxYTD) {
    $pdf->showBoxed($_SESSION['country_default']['tax_label'] . ' Remitted to Owner/ATO', 30, 440 + $line, 275, 30, 'left', '');
    $pdf->showBoxed(toDecimal($remittedTax), 290, 440 + $line, 75, 30, 'right', '');
    $pdf->showBoxed(toDecimal($remittedTaxYTD), 440, 440 + $line, 75, 30, 'right', '');
}

// //////////////////////////////////////GST /// YEAR/////////////////////////////////////////////////////


$pdf->setColorExt('fill', 'rgb', 0.88, 0.88, 0.88, 0);
$pdf->rect(265, 440 + $line, 305, 16);
$pdf->fill();
$pdf->setColorExt('fill', 'rgb', 0, 0, 0, 0);

$pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
$pdf->showBoxed(toDecimal($taxSubTotal), 290, 425 + $line, 75, 30, 'right', '');
$pdf->showBoxed(toDecimal($taxSubTotalYTD), 440, 425 + $line, 75, 30, 'right', '');



$pdf->moveto(265, 440 + $line);
$pdf->lineto(570, 440 + $line);
$pdf->stroke();

$pdf->moveto(265, 456 + $line);
$pdf->lineto(570, 456 + $line);
$pdf->stroke();

// //////////////////////////////////// NET CASH INFLOWS/(OUTFLOWS) ////////////////////////////////////
$pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
$pdf->showBoxed('Net Cash Inflows/(Outflows)', 30, 398 + $line, 275, 30, 'left', '');
$pdf->setFontExt($_fonts['Helvetica'], 8);


// //////////////////////////////ANOTHER PDF SECTION//////////////////////////////////////////////////////


$pdf->setColorExt('fill', 'rgb', 0.88, 0.88, 0.88, 0);
$pdf->rect(265, 413 + $line, 305, 16);
$pdf->fill();
$pdf->setColorExt('fill', 'rgb', 0, 0, 0, 0);

$pdf->moveto(265, 413 + $line);
$pdf->lineto(570, 413 + $line);
$pdf->stroke();

$pdf->moveto(265, 429 + $line);
$pdf->lineto(570, 429 + $line);
$pdf->stroke();


$pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
$pdf->showBoxed(toDecimal($totalNet), 290, 398 + $line, 75, 30, 'right', '');
$pdf->showBoxed(toDecimal($totalNetYTD), 440, 398 + $line, 75, 30, 'right', '');


// -------------------------------------------------------------------------------------
// ///////////////////////////CASH RECONCILIATION////////////////////////////////////
// ------------------------------------------------------------------------------------

//	if ($line == 0)
//	{
//	$line=60;
//	}
//	elseif ($line <=10)
//	{
//	$line = 50;
//	}
//	elseif ($line <=20 && $line >= 11)
//	{
//	$line = 40;
//	}
//	elseif ($line <=30 && $line >= 21)
//	{
//	$line = 40;
//	}
//	elseif ($line <=40 && $line >= 31)
//	{
//	$line = 20;
//	}
//	elseif ($line <=50 && $line >= 41)
//	{
//	$line = 10;
//	}
//	elseif ($line <=60 && $line >= 51)
//	{
//	$line = 0;
//	}
//	elseif ($line <=60 && $line >= 61)
//	{
//	$line = 0;
//	}
//	elseif ($line <=70 && $line >= 61)
//	{
//	$line = -10;
//	}
//	elseif ($line <=80 && $line >= 71)
//	{
//	$line = -20;
//	}
//	elseif ($line <=90 && $line >= 81)
//	{
//	$line = -30;
//	}



$pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
$pdf->showBoxed('Cash Reconciliation', 30, 378 + $line, 275, 30, 'left', ''); // 428-$line
$pdf->setFontExt($_fonts['Helvetica'], 8);
$pdf->showBoxed('Opening Cash Balance', 30, 358 + $line, 275, 30, 'left', '');
$pdf->showBoxed('Net Cash Inflows/(Outflows)', 30, 338 + $line, 275, 30, 'left', '');
$pdf->showBoxed(toDecimal($totalNet), 290, 338 + $line, 75, 30, 'right', '');
$pdf->showBoxed(toDecimal($totalNetYTD), 440, 338 + $line, 75, 30, 'right', '');


$pdf->moveto(265, 353 + $line);
$pdf->lineto(570, 353 + $line);
$pdf->stroke();


$pdf->setFontExt($_fonts['Helvetica'], 8);
// $pdf->showBoxed ("Printed on $date", 30+9, 7, 275, 30, "left" ,"");
$pdf->showBoxed("Page $page", 470 + 9, 7, 75, 30, 'right', '');



// //////////////////////////////// ANOTHER PDF SECTION///////////////////////////////////////


$pdf->setFontExt($_fonts['Helvetica'], 8);


if ($line == 50) {

} elseif ($line == '-30') {

} else {

}

$pdf->showBoxed(toDecimal($balance), 290, 358 + $line, 75, 30, 'right', '');
$pdf->showBoxed(toDecimal($balanceYTD), 440, 358 + $line, 75, 30, 'right', '');

$pdf->showBoxed(toDecimal($subTotal), 290, 323 + $line, 75, 30, 'right', '');
$pdf->showBoxed(toDecimal($subTotalYTD), 440, 323 + $line, 75, 30, 'right', '');

//
//
// ########################PAYMENTS TO OWNERS ################################

if ($noneop_rec_month != 0 or $noneop_rec_year != 0) {
    $pdf->showBoxed('Non-operating Receipts', 30, 313 + $line, 275, 30, 'left', '');
    $pdf->showBoxed(toDecimal($noneop_rec_month), 290, 313 + $line, 75, 30, 'right', '');
    $pdf->showBoxed(toDecimal($noneop_rec_year), 440, 313 + $line, 75, 30, 'right', '');
    $line = $line - 10;
}
// #######################PAYMENTS TO OWNERS ################################

if ($noneop_pay_month != 0 or $noneop_pay_year != 0) {
    $pdf->showBoxed('Non-operating Payments', 30, 313 + $line, 275, 30, 'left', '');
    $pdf->showBoxed(toDecimal($noneop_pay_month), 290, 313 + $line, 75, 30, 'right', '');
    $pdf->showBoxed(toDecimal($noneop_pay_year), 440, 313 + $line, 75, 30, 'right', '');
    $line = $line - 10;
}

// ########################PAYMENTS TO OWNERS ################################

$pdf->showBoxed('Payments to owner/s', 30, 313 + $line, 275, 30, 'left', '');
$ownerLine = 303 + $line;

$pmts_duringM = getPaymentsToOwners($propertyID, $periodFrom, $periodTo); // trim(MSSQL_RESULT($query_result,0,"amount"));
$creditorResult = getCreditorDetails($propertyID, $periodFrom, $periodTo);	// $query_result = MSSQL_QUERY($query);

if (count($creditorResult ?? []) < 16) {
    //    $count = 0; for testing line count
    foreach ($creditorResult as $key => $thisRow) {
        //        $count ++; for testing line count
        $ind_pmts_duringY 	= $thisRow['pmxc_alloc_amt'];
        $ind_pmxc_s_creditor 	= $thisRow['pmxc_s_creditor'];
        $cred_name 		= $thisRow['pmco_name'];
        $ind_pmxc_alloc_dt 	= $thisRow['pmxc_alloc_dt'];
        $cred_name 		= substr($cred_name, 0, 15);

        $pdf->showBoxed($cred_name, 30, 303 + $line, 120, 30, 'left', '');
        $pdf->showBoxed($ind_pmxc_alloc_dt, 75, 303 + $line, 120, 30, 'right', '');
        $pdf->showBoxed(toDecimal($ind_pmts_duringY), 140, 303 + $line, 120, 30, 'right', '');
        $ownerLine = 303 + $line;
        $line = $line - 10;
        //		if ($count >= 15) for testing line count
        //		    break; for testing line count
    }
} else {
    $line = $line - 10;
}
$pmts_duringY = $ownerPaymentsYTD;

//	$line = $line + 10;

$pdf->showBoxed(toDecimal($pmts_duringM), 290, $ownerLine, 75, 30, 'right', '');
$pdf->showBoxed(toDecimal($pmts_duringY), 440, $ownerLine, 75, 30, 'right', '');
//
//
//
//	//----------------------------------------------------------------------------------------------------------
//	///////////////////////////CASH BALANCE AT THE END OF YEAR FIGURES//////////////////////////////////////////
//	//----------------------------------------------------------------------------------------------------------
//
$line = $line - 20;
$pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
$pdf->showBoxed('Closing Cash Balance', 30, 303 + $line, 275, 30, 'left', '');

$pdf->setColorExt('fill', 'rgb', 0.88, 0.88, 0.88, 0);
$pdf->rect(265, 317 + $line, 305, 16);
$pdf->fill();
$pdf->setColorExt('fill', 'rgb', 0, 0, 0, 0);
$pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
$pdf->showBoxed(toDecimal($cashBalance), 290, 303 + $line, 75, 30, 'right', '');
$pdf->showBoxed(toDecimal($cashBalanceYTD), 440, 303 + $line, 75, 30, 'right', '');

$pdf->setlinewidth(0.5);
// bottom line
$pdf->moveto(25, 317 + $line);
$pdf->lineto(570, 317 + $line);
$pdf->stroke();


$pdf->setlinewidth(0.5);
// left side bar
$pdf->moveto(25, 317 + $line);
$pdf->lineto(25, 675);
$pdf->stroke();
// mtd right
$pdf->moveto(265, 317 + $line);
$pdf->lineto(265, 675);
$pdf->stroke();
// mtd right
$pdf->moveto(417, 317 + $line);
$pdf->lineto(417, 675);
$pdf->stroke();
// right side bar
$pdf->moveto(570, 317 + $line);
$pdf->lineto(570, 675);
$pdf->stroke();

// top bar

$pdf->setlinewidth(0.5);
$pdf->moveto(25, 675);
$pdf->lineto(570, 675);
$pdf->stroke();

// total lines

$pdf->moveto(265, 333 + $line);
$pdf->lineto(570, 333 + $line);
$pdf->stroke();
//
//	$pdf->moveto(265, 363-$line);
//	$pdf->lineto(570, 363-$line);
//	$pdf->stroke();

$traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'cirrus8CashOwnerReport', A4_PORTRAIT);
$traccFooter->prerender($pdf);

$pdf->end_page_ext('');

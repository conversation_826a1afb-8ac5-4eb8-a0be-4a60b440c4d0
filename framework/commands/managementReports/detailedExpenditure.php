<?php

function checkPeriods($properties)
{
    $i = 0;
    $_match = [];
    foreach ($properties as $propertyID => $date) {
        if ($i == 0) {
            $_check = $date;
        } else {
            if ($date !== $_check) {
                $_match[] = $propertyID;
            }
        }
        $i++;
    }

    return $_match;
}


define('DS_ALL', 1);
define('DS_PORTFOLIO', 2);
define('DS_GROUP', 3);
define('DS_TYPE', 4);
define('DS_REPORT', 5);

function detailedExpenditure(&$context)
{
    global $monthName;

    include 'functions/ownerReportFunctions.php';

    if (! isPostback()) {
        $view = new MasterPage(userViews(), '/managementReports/detailedExpenditure.html');
        $view->setSection($context['module']);
    } else {
        $view = new UserControl(userViews(), '/managementReports/detailedExpenditure.html', '');
    }

    $view->bindAttributesFrom($_REQUEST);

    // ## IF OWNER ACCESS PLUS ###
    // ## CHECK HAS PERMISSION ###
    if ($_SESSION['user_type'] == USER_OWNER) {
        if ($_SESSION['user_sub_type'] == 'OWA') {
            ownerRedirect('');
        } else {
            ownerRedirect($view->items['command']);
        }
    }
    // ##          END         ###

    $reportTypeList = dbGetReports(1);
    $reportTypeList = mapParameters($reportTypeList, 'reportID', 'reportName');
    $reportKeys = array_keys($reportTypeList);
    $view->items['reportTypeList'] = $reportTypeList;

    $view->items['selectList'] = [
        DS_ALL => 'All Properties',
        DS_PORTFOLIO => ucwords(strtolower($_SESSION['country_default']['portfolio_manager'])),
        DS_GROUP => 'Property Group',
        DS_TYPE => 'Property Type',
        DS_REPORT => 'Report Type',
    ];
    if (! isset($view->items['datasource'])) {
        $view->items['datasource'] = DS_ALL;
    }

    if (! isset($view->items['reporttype'])) {
        $view->items['reporttype'] = $reportKeys[0];
    }

    if (isset($view->items['reporttype'])) {
        $report = dbGetReport($view->items['reporttype']);
        $includes = dbGetSubReports($view->items['reporttype']);
        $view->items['subReports'] = $includes;

        if ($view->items['action'] == 'changeType') {
            $view->items['sub-report'] = 1;
            foreach ($includes as $i) {
                $view->items['sr_' . $i['subReportID']] = 1;
            }
        }
    }


    if ((isset($view->items['action'])) && (! $view->items['property'])) {
        $validationErrors[] = 'Please select a property';
    }


    if ($_SESSION['user_type'] != USER_OWNER) {
        $view->items['property'] = deserializeParameters($view->items['property']);
        $propList = getPropList($view->items['inactiveprop'], $view->items['datasource'], $view->items['filter']);
        $properties = $view->items['property'][0];
    } else {
        $model = new dbOwner();
        $model->userID = intval($_SESSION['user_id']);

        $propertyOwnerList = $model->getOwnerPropertyList();
        $view->items['propertyOwnerList'] = $propertyOwnerList;
        $properties = $view->items['property'];
    }

    if (! isEmptyArray($view->items['property'])) {
        $view->items['showDates'] = true;
    }

    // -- if a building has been selected
    if ($view->items['property']) {
        $allPeriods = getAllPeriodsFromTo($properties);
        $view->items['allPeriods'] = $allPeriods;

        // -- check that the calendars match for all selected properties
        if (in_array($view->items['action'], ['getDates', 'changeType'])) {
            foreach ($properties as $property) {
                $_p = getPropPeriod($property);
                $_periodFrom[$property] = $_p['periodFrom'];
                $_periodTo[$property] = $_p['periodTo'];
                $_startFY[$property] = $_p['startFY'];
            }
            $periodFrom = checkPeriods($_periodFrom);
            $periodTo = checkPeriods($_periodTo);
            $startFY = checkPeriods($_startFY);

            if (count($periodFrom ?? []) > 0) {
                $validationErrors[] = 'Period from dates dont match for the following properties - ' . implode(
                    ', ',
                    $periodFrom
                );
            }
            if (count($periodTo ?? []) > 0) {
                $validationErrors[] = 'Period to dates dont match for the following properties - ' . implode(
                    ', ',
                    $periodTo
                );
            }
            if (count($startFY ?? []) > 0) {
                $validationErrors[] = 'Start of financial year dates dont match for the following properties - ' . implode(
                    ', ',
                    $startFY
                );
            }

            $view->items['finalDateCheck'] = $finalDateCheck;
            $view->items['checkList'] = $_checkList;

            // get period of first property which is used as a reference for all properties selected after it
            $_p = getPropPeriod($properties[0]);

            $view->items['periodFrom'] = $_p['periodFrom'];
            $view->items['periodTo'] = $_p['periodTo'];
            $view->items['startFY'] = $_p['startFY'];

            [$d, $m, $y] = explode('/', $view->items['periodTo']);
            if (! isset($view->items['reportingPeriodFrom'])) {
                $view->items['reportingPeriodFrom'] = "1/$m/$y";
            }
            if (! isset($view->items['reportingPeriodTo'])) {
                $view->items['reportingPeriodTo'] = onedayBefore(oneMonthAfter($view->items['reportingPeriodFrom']));
            }
        }
    }

    // -- if the period dates have been changed, adjust the report description
    if (in_array($view->items['action'], ['getDates', 'changeDates'])) {
        if (noErrors($validationErrors)) {
            [$d, $m, $y] = explode('/', $view->items['periodTo']);
            $view->items['reportPeriod'] = $monthName[(int) $m] . ' ' . $y;
            $period = dbGetPeriod($view->items['property'][0], $view->items['periodTo']);
            $view->items['year'] = $period['year'];
            $view->items['period'] = $period['period'];
        }
    }


    $propGroupList = get_prop_groups();
    $propTypeList = get_prop_types();
    $portfolioList = getPortfolioList();
    $view->items['formatList'] =
    [
        FILETYPE_PDF => 'PDF',
        FILETYPE_XLS => 'Excel Spreadsheet',
    ];

    if (! isset($view->items['format'])) {
        $view->items['format'] = FILETYPE_PDF;
    }
    if (isset($view->items['rtype'])) {
        $view->items['value'] = $view->items['rtype'];
    }
    if (isset($view->items['ptype'])) {
        $view->items['value'] = $view->items['ptype'];
    }
    if (! isset($view->items['logo'])) {
        $view->items['logo'] = true;
    }
    if (! isset($view->items['filename_description'])) {
        $view->items['filename_description'] = 'Owner_reports_';
    }

    $view->items['last_error'] = $context['last_error'];

    $view->items['propList'] = $propList;
    $view->items['propGroupList'] = $propGroupList;
    $view->items['propTypeList'] = $propTypeList;
    $view->items['reportTypeList'] = $reportTypeList;
    $view->items['portfolioList'] = $portfolioList;

    switch ($view->items['action']) {
        case 'submit':
            if (! $view->items['periodFrom'] && $view->items['property']) {
                $validationErrors[] = 'Please select a report period from date';
            }
            if (! $view->items['periodTo'] && $view->items['property']) {
                $validationErrors[] = 'Please select a report period to date';
            }

            if ((isset($view->items['action'])) && ($view->items['action'] == 'submit') && (toDateStamp(
                $view->items['periodTo']
            ) < toDateStamp($view->items['periodFrom']))) {
                $validationErrors[] = 'Your to date cannot be before your from date (reporting period).';
            }

            if ($view->items['property'] && $view->items['periodFrom']) {
                foreach ($view->items['property'] as $ownerReportIndex => $propertyID) {
                    $calendar = dbGetPeriod($propertyID, $view->items['periodFrom']);
                    if (empty($calendar)) {
                        $validationErrors[] = 'There is no calendar period for this property.';
                    }
                }
            }

            if (noErrors($validationErrors)) {
                $context = $view->items;
                executeCommand('ownerReportProcess', 'managementReports');
            }
            break;
    }

    $view->items['validationErrors'] = $validationErrors;

    $view->render();
}


// ###########################SQL QUERY GENERATION ###########################################

function getPropList($inactivePropFlag, $dataSource, $val)
{
    $params = [];
    // default sub queries
    $inactiveprop1 = ' WHERE deleted_flag <> 1 ';

    if ($inactivePropFlag == 'true') {
        $inactiveprop1 = ' WHERE 1=1';
    }

    $subQuery = "$inactiveprop1 ORDER BY property_code";
    $reportTypeFlag = false;

    if ($dataSource !== DS_ALL) {
        if ($dataSource == DS_REPORT) {
            $reportTypeFlag = true;
            $inactiveprop2 = " WHERE pmpr_delete <> '1'";
            if ($inactivePropFlag == 'true') {
                $inactiveprop2 = ' WHERE 1=1';
            }
            $subQ = '';

            if ($val !== 'all') {
                $subQ = ' AND pmpr_own_rep = ' . addSQLParam($params, $val) . ' ';
            } // else { $subQuery = "1=1"; }
            $subQueryReport = " $inactiveprop2 $subQ ORDER BY pmpr_prop";
        } else {
            if ($dataSource == DS_TYPE) {
                $subQuery = "$inactiveprop1 AND property_type = " . addSQLParam(
                    $params,
                    $val
                ) . ' ORDER BY property_code';
            } else {
                if ($dataSource == DS_GROUP) {
                    $subQuery = "$inactiveprop1 AND group_code = " . addSQLParam(
                        $params,
                        $val
                    ) . ' ORDER BY property_code';
                } else {
                    if ($dataSource == DS_PORTFOLIO) {
                        $subQuery = "$inactiveprop1 AND portfolio = " . addSQLParam(
                            $params,
                            $val
                        ) . ' ORDER BY property_code';
                    }
                }
            }
        }
    }

    if ($reportTypeFlag) {
        $propertySQL = "	SELECT (pmpr_prop) as property_code, (pmpr_name) as property_name
							FROM pmpr_property
							$subQueryReport";
    } else {
        $propertySQL = "	SELECT property_code as property_code, property_name as property_name
							FROM crp_property $subQuery";
    }

    $propList = [];
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);
    $propList = $dbh->executeSet($propertySQL, false, true, $params); // mssql_query($propertySQL);

    return $propList;
}

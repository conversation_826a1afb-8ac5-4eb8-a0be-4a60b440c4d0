<?php

/**
 * Process information gathered in order to generate an area summary.
 *
 * @param  $context  array Mandatory. Referenced array containing data needed to continue.
 *
 * <AUTHOR> Reyes
 *
 * @since 2012-09-21
 *
 * @modified 2012-12-08: Added format option. [Morph]
 **/
function areaSummaryProcess($context)
{
    global $clientDirectory, $pathPrefix;

    if (! isPostback()) {
        $view = new MasterPage(userViews(), '/managementReports/areaSummaryProcess.html');
        $view->setSection($context['module']);
    } else {
        $view = new UserControl(userViews(), '/managementReports/areaSummaryProcess.html');
    }

    $view->bindAttributesFrom($_REQUEST);

    $buildingTotalOnly = ($view->items['buildingTotalOnly'] == 'Yes') ? true : false;
    $date = $view->items['toDate'];
    $vacantAreaOnly = $context['vacantAreaOnly'];

    if ($view->items['method'] == 'portfolioManager') {
        $propertyManager = $view->items['portfolioManager'];
        $propertyManagerName = dbGetParam('PORTMGR', $propertyManager);
        $subtitle = "for $propertyManagerName ($propertyManager)";
        if ($buildingTotalOnly) {
            $headerTitle = 'Property Occupancy Status Report (Buildings Total)';
            $reportResult = dbGetAreaBuildingReport($date, $propertyManager, $vacantAreaOnly);
        } else {
            $headerTitle = 'Property Occupancy Status Report';
            $reportResult = dbGetAreaSummaryReport('portfolioManager', $date, $propertyManager, $vacantAreaOnly);
        }
    } elseif ($view->items['method'] == 'property') {
        $headerTitle = 'Property Occupancy Status Report (Properties)';
        $propertyCode = $view->items['propertyID'];
        $propertyName = dbGetPropertyDetails($propertyCode);
        $propertyName = $propertyName['propertyName'];
        $subtitle = "for $propertyName ($propertyCode)";
        $reportResult = dbGetAreaSummaryReport('property', $date, $propertyCode, $vacantAreaOnly);
    } else {
        if ($buildingTotalOnly) {
            $headerTitle = 'Property Occupancy Status Report (Buildings Total)';
            $subtitle = '';
            $reportResult = dbGetAreaBuildingReport($date, '', $vacantAreaOnly);
        } else {
            $headerTitle = 'Property Occupancy Status Report';
            $subtitle = '';
            $reportResult = dbGetAreaSummaryReport('all', $date, '', $vacantAreaOnly);
        }
    }

    $count = 1; // 2012-10-15: Placeholder if the scheduler is to be implemented for this report someday. [Morph]

    if ($context[IS_TASK]) {
        $view->bindAttributesFrom($context);
        extract($context);
    } else {
        $view->bindAttributesFrom($context);
        $view->bindAttributesFrom($_REQUEST);
        $queue = new Queue(TASKTYPE_AREA_SUMMARY);
        if ($count > THRESHOLD_AREASUMMARY) {
            $queue->add($_SESSION['clientID'], $_SESSION['un'], 'command=areaSummaryProcess&module=managementReports', $_REQUEST);
        }
    }

    $format = $view->items['format'];

    if (! $context[DOC_MASTER]) {
        $logoFile = dbGetClientLogo();
        $logoPath = "assets/clientLogos/{$logoFile}";
        $_filePath =  "{$pathPrefix}{$clientDirectory}/{$format}/" . DOC_PROPERTYOCCUPANCYSTATUS . '/';
        $_downloadPath =  "{$clientDirectory}/{$format}/" . DOC_PROPERTYOCCUPANCYSTATUS;
        $file =  DOC_PROPERTYOCCUPANCYSTATUS . '_' . date('Ymd') . ".{$format}";
        $filePath = $_filePath . $file;
        $downloadPath = "{$_downloadPath}/{$file}";
    }

    if ($context[IS_TASK] || $count <= THRESHOLD_AREASUMMARY) {
        switch ($format) {
            case FILETYPE_PDF:
                $report = ($context[DOC_MASTER]) ? new PDFDataReport($context[DOC_MASTER], $logoPath, A4_PORTRAIT) : new PDFDataReport($filePath, $logoPath, A4_PORTRAIT);
                $report->multiLine = true;
                $report->printRowLines = true;
                $report->printColumnLines = false;
                $report->printBorders = false;
                $report->cache = false;
                $prepared = 'as at ' . $date;
                $header = new ReportHeader($headerTitle, $subtitle, $prepared);
                $header->xPos = $report->hMargin;
                $header->yPos = $report->pageHeight - $report->vMargin;
                $report->attachObject('header', $header);
                $footer = new TraccFooter(null, $_report['reportName'], $report->pageSize);
                $report->attachObject('footer', $footer);
                break;
            case FILETYPE_XLS:
                $report = new XLSDataReport($filePath, $headerTitle);
                break;
        }

        if ($format != FILETYPE_SCREEN) {
            if ($buildingTotalOnly) {
                $report->addColumn('propertyName', 'Property', 200, 'left');
                $report->addColumn('propertyTotalArea', 'Total Area (' . html_entity_decode($_SESSION['country_default']['area_unit'], ENT_COMPAT) . ')', 60, 'right');
                $report->addColumn('propertyAreaLet', 'Area Let (' . html_entity_decode($_SESSION['country_default']['area_unit'], ENT_COMPAT) . ')', 60, 'right');
                $report->addColumn('propertyAreaLetPercent', 'Area Let (%)', 60, 'right');
                $report->addColumn('propertyAreaVacant', 'Area Vacant (' . html_entity_decode($_SESSION['country_default']['area_unit'], ENT_COMPAT) . ')', 60, 'right');
                $report->addColumn('propertyAreaVacantPercent', 'Area Vacant (%)', 60, 'right');
            } else {
                $report->addColumn('propertyCode', 'Property', 60, 'left');
                $report->addColumn('propertyUnit', 'Unit', 60, 'left');
                $report->addColumn('propertyDescription', 'Description', 75, 'left');
                $report->addColumn('tenantName', 'Tenant', 130, 'left');
                $report->addColumn('propertyArea', 'Area (' . html_entity_decode($_SESSION['country_default']['area_unit'], ENT_COMPAT) . ')', 80, 'right');
                $report->addColumn('vacantSince', 'From Date', 80, 'right');
            }
        }

        if ($view->items['method'] != 'property') {
            $properties = [];
            if ($buildingTotalOnly) {
                if ($format != FILETYPE_SCREEN) {
                    $report->preparePage();
                    $report->renderHeader();
                }

                // (a) Data Segregation
                //                pre_print_r($reportResult);
                foreach ($reportResult as $k => $v) {
                    //				    pre_print_r($v);
                    $property = $v['propertyID'];
                    $properties[$property]['propertyName'] = $v['propertyID'] . ' - ' . $v['propertyName'];
                    $properties[$property]['propertyTotalArea'] += $v['propertyArea'];
                    $totalResult['propertyTotalArea'] += $v['propertyArea'];
                    if ($v['propertyStatus'] == 'V' or $v['propertyStatus'] == 'I') {
                        $properties[$property]['propertyAreaVacant'] += $v['propertyArea'];
                        $totalResult['propertyAreaVacant'] += $v['propertyArea'];
                    } else {
                        $properties[$property]['propertyAreaLet'] += $v['propertyArea'];
                        $totalResult['propertyAreaLet'] += $v['propertyArea'];
                    }
                    //					if($property == 'Unit 1, 91 Aberdeen Street') pre_print_r($properties);
                }
                $reportResult =  [];
                // (b) Data Format
                //                pre_print_r($properties);
                foreach ($properties as $k => $v) {
                    $reportResult[$i]['propertyName'] = $v['propertyName'];
                    if ($v['propertyAreaVacant']) {
                        $reportResult[$i]['propertyAreaVacantPercent'] = toDecimal($v['propertyAreaVacant'] / $v['propertyTotalArea'] * 100) . '%';
                    }
                    if ($v['propertyAreaLet']) {
                        $reportResult[$i]['propertyAreaLetPercent'] = toDecimal($v['propertyAreaLet'] / $v['propertyTotalArea'] * 100) . '%';
                    }
                    $reportResult[$i]['propertyTotalArea'] = toDecimal($v['propertyTotalArea']);
                    $reportResult[$i]['propertyAreaVacant'] = toDecimal($v['propertyAreaVacant']);
                    $reportResult[$i]['propertyAreaLet'] = toDecimal($v['propertyAreaLet']);
                    $i++;
                }

                if ($format != FILETYPE_SCREEN) {
                    $report->renderData($reportResult);
                }

                $totalResult['propertyAreaLetPercent'] = toDecimal($totalResult['propertyAreaLet'] / $totalResult['propertyTotalArea'] * 100) . '%';
                $totalResult['propertyAreaVacantPercent'] = toDecimal($totalResult['propertyAreaVacant'] / $totalResult['propertyTotalArea'] * 100) . '%';
                $totalResult['propertyTotalArea'] = toDecimal($totalResult['propertyTotalArea']);
                $totalResult['propertyAreaLet'] = toDecimal($totalResult['propertyAreaLet']);
                $totalResult['propertyAreaVacant'] = toDecimal($totalResult['propertyAreaVacant']);

                if ($format == FILETYPE_PDF) {
                    $report->renderSubTotal($totalResult);

                    $report->clean();
                    $report->endPage();
                }
            } else {
                foreach ($reportResult as $k => $v) {
                    $property = $v['propertyCode'];
                    $properties[$property][$k]['propertyCode'] = $property;
                    $properties[$property][$k]['propertyUnit'] = $v['propertyUnit'];
                    $properties[$property][$k]['propertyDescription'] = $v['propertyDescription'];
                    $properties[$property][$k]['vacantSince'] = $v['vacantSince'];
                    if ($v['propertyStatus'] == 'V' or $v['propertyStatus'] == 'I' or empty($v['tenantName'])) {
                        $properties[$property][$k]['tenantName'] = 'Vacant';
                        $properties[$property]['vacantTotal'] += $v['propertyArea'];
                    } else {
                        $properties[$property][$k]['tenantName'] = $v['tenantName'];
                        $properties[$property]['occupiedTotal'] += $v['propertyArea'];
                    }
                    $properties[$property][$k]['propertyArea'] = $v['propertyArea'];
                    $properties[$property]['total'] += $v['propertyArea'];
                }

                foreach ($properties as $property => $v) {
                    if ($format != FILETYPE_SCREEN) {
                        $report->preparePage();
                        $report->renderHeader();

                        foreach ($v as $propertyDetails) {
                            if ($propertyDetails['propertyCode'] == $property) {
                                $propertyDetails['propertyArea'] = toDecimal($propertyDetails['propertyArea']);
                                $report->renderLine($propertyDetails);
                            }
                        }

                        $totalOccupiedResult = ['tenantName' => '0%'];
                        $totalVacantResult = ['tenantName' => '0%'];

                        $totalResult['propertyArea'] = $v['total'];

                        $totalOccupiedResult['propertyCode'] = 'Occupied Area';

                        if ($v['occupiedTotal'] and $totalResult['propertyArea']) {
                            $totalOccupiedResult['tenantName'] = toDecimal(($v['occupiedTotal'] / $totalResult['propertyArea']) * 100) . '%';
                        }
                        $totalOccupiedResult['propertyArea'] = toDecimal($v['occupiedTotal']);
                        if ($format == FILETYPE_PDF) {
                            $report->renderSubTotal($totalOccupiedResult);
                        } else {
                            $report->renderLine($totalOccupiedResult);
                        }

                        $totalVacantResult['propertyCode'] = 'Vacant Area';
                        if ($v['vacantTotal'] and $totalResult['propertyArea']) {
                            $totalVacantResult['tenantName'] = toDecimal(($v['vacantTotal'] / $totalResult['propertyArea']) * 100) . '%';
                        }
                        $totalVacantResult['propertyArea'] = toDecimal($v['vacantTotal']);
                        if ($format == FILETYPE_PDF) {
                            $report->renderSubTotal($totalVacantResult);
                        } else {
                            $report->renderLine($totalVacantResult);
                        }

                        $totalResult['propertyCode'] = 'Total Area';
                        $totalResult['propertyArea'] = toDecimal($totalResult['propertyArea']);
                        if ($format == FILETYPE_PDF) {
                            $report->renderSubTotal($totalResult);
                        } else {
                            $report->renderLine($totalResult);
                        }

                        $report->clean();
                        $report->endPage();
                    }
                }
            }
        } else {
            if ($format != FILETYPE_SCREEN) {
                $report->preparePage();
                $report->renderHeader();
            }

            foreach ($reportResult as $k => $v) {
                if ($v['propertyStatus'] == 'V' or $v['propertyStatus'] == 'I' or empty($v['tenantName'])) {
                    $reportResult[$k]['tenantName'] = 'Vacant';
                    $totalVacantResult['propertyArea'] += $v['propertyArea'];
                } else {
                    $totalOccupiedResult['propertyArea'] += $v['propertyArea'];
                }
                $reportResult[$k]['propertyArea'] = toDecimal($v['propertyArea']);
                $totalResult['propertyArea'] += $v['propertyArea'];
            }

            if ($format != FILETYPE_SCREEN) {
                $report->renderData($reportResult);
            }
            $totalOccupiedResult['propertyCode'] = 'Occupied Area';
            $totalOccupiedResult['tenantName'] = toDecimal(($totalOccupiedResult['propertyArea'] / $totalResult['propertyArea']) * 100) . '%';
            $totalOccupiedResult['propertyArea'] = toDecimal($totalOccupiedResult['propertyArea']);
            if ($format == FILETYPE_PDF) {
                $report->renderSubTotal($totalOccupiedResult);
            }

            $totalVacantResult['propertyCode'] = 'Vacant Area';
            $totalVacantResult['tenantName'] = toDecimal(($totalVacantResult['propertyArea'] / $totalResult['propertyArea']) * 100) . '%';
            $totalVacantResult['propertyArea'] = toDecimal($totalVacantResult['propertyArea']);
            if ($format  == FILETYPE_PDF) {
                $report->renderSubTotal($totalVacantResult);
            }

            $totalResult['propertyCode'] = 'Total Area';
            $totalResult['propertyArea'] = toDecimal($totalResult['propertyArea']);

            if ($format  == FILETYPE_PDF) {
                $report->renderSubTotal($totalResult);

                $report->clean();
                $report->endPage();
            }
        }

        if ($format != FILETYPE_SCREEN) {
            $report->close();
        } else {
            $view->items['reportResult'] = $reportResult;
            $view->items['totalResult'] = $totalResult;
        }
    }

    if (! $context[DOC_MASTER]) {
        if ($context[IS_TASK]) {
            $email = new EmailTemplate('views/emails/basicEmail.html', SYSTEMURL);
            $attachment =  [];
            $attachment[0]['file'] = REPORTPATH . '/' . $pdfDownloadLink;
            $attachment[0]['content_type'] = 'application/pdf';
            sendMail($_SESSION['email'], $_SESSION['first_name'] . ' ' . $_SESSION['last_name'], $email->toString(), 'Report', $attachment);
            logData('Emailed report to ' . $_SESSION['email']);
            $context[TASK_COMPLETE] = true;
            $view->items['statusMessage'] = 'Due to its complexity, this report has been scheduled and will be emailed to you on completion.';
        } else {
            $view->items['downloadPath'] = $downloadPath;
        }
        $view->render();
    }
}

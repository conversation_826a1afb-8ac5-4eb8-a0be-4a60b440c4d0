<?php

include 'lib/ngForm.php';

function ownerTenancyReport(&$context)
{

    if (! isPostback()) {
        $view = new MasterPage(userViews(), '/managementReports/ownerTenancyReport.html');
        $view->setSection($context['module']);
    } else {
        $view = new UserControl(userViews(), '/managementReports/ownerTenancyReport.html');
    }

    // -- bind the postback and set some standard variables
    $view->bindAttributesFrom($_REQUEST);

    // ## IF OWNER ACCESS PLUS ###
    // ## CHECK HAS PERMISSION ###
    if ($_SESSION['user_type'] == USER_OWNER) {
        if ($_SESSION['user_sub_type'] == 'OWA') {
            ownerRedirect('');
        } else {
            ownerRedirect($view->items['command']);
        }
    }
    // ##          END         ###

    $view->items['onScreenHeaders'] = [
        ['label' => '#', 'align' => 'center'],
        ['label' => 'Report Name', 'align' => 'center'],
        ['label' => 'Link', 'align' => 'center'],
        ['label' => 'Dates Enabled?', 'align' => 'center'],
        ['label' => 'Account Enabled?', 'align' => 'center'],
        ['label' => 'Is Consolidated?', 'align' => 'center'],
        ['label' => 'Is Quick Report?', 'align' => 'center'],
        ['label' => 'Display Logo?', 'align' => 'center'],
        ['label' => 'Category', 'align' => 'center'],
        ['label' => 'Sub Category', 'align' => 'center'],
        ['label' => 'Actions', 'align' => 'center'],
    ];

    $view->items['typeList'] =
    [
        1 => 'Cash Budget',
        2 => 'Cash Forecast',
        3 => 'Accruals Budget',
        4 => 'Accruals Forecast',
    ];
    if (empty($view->items['dataType'])) {
        $view->items['dataType'] = 3;
    }

    if (! isset($view->items['toDate'])) {
        $view->items['toDate'] = TODAY;
    }
    if (isUserType(USER_LEASE) || isUserType(USER_LEASEADMIN)) {
        if (! isset($view->items['method'])) {
            $view->items['method'] = 'division';
        }
    } else {
        if (! isset($view->items['method'])) {
            $view->items['method'] = 'property';
        }
    }

    $view->items['propertyManagerList'] = dbGetPropertyManagers();
    $view->items['property'] = deserializeParameters($view->items['property']);

    if (in_array($view->items['method'], ['property', 'propertyManager'])) {
        switch ($_SESSION['user_type']) {
            case USER_OWNER:
                $model  = new dbOwner();
                $model->userID = intval($_SESSION['user_id']);
                $propAssigned = $model->getPropertyList($view->items['propertyManager']);
                $propCode = '';
                $ownerCode = '';
                foreach ($propAssigned as $propValue) {
                    if ($propCode == '') {
                        $propCode = $propValue['propertyID'];
                    } else {
                        $propCode .= ',' . $propValue['propertyID'] . '';
                    }

                    if ($ownerCode == '') {
                        $ownerCode = $propValue['ownerCode'];
                    } else {
                        $ownerCode .= ',' . $propValue['ownerCode'] . '';
                    }

                }
                $view->items['propertyList'] = $propCode;
                $view->items['propertyOwnerCode'] = $ownerCode;
                break;
            default:
                $view->items['propertyList'] = dbGetPropertyByCriteria(null, null, $view->items['propertyManager']);
                break;
        }
    }


    $view->items['last_error'] = $context['last_error'];
    $view->items['statusMessage'] = $context['statusMessage'];
    $validationErrors =  [];
    $details = extractFields($view->items, ['contactDetailID', 'detail', 'detailCode']);
    $view->render();
}

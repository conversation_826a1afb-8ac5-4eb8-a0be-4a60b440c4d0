<?php

$period = dbGetPeriod($propertyID, $view->items['periodTo']);
$year = $period['year'];

$logoFile = dbGetClientLogo();
$logoPath = "assets/clientLogos/{$logoFile}";
$format = 'pdf';

$_filePath = "{$pathPrefix}{$clientDirectory}/{$format}/" . DOC_OWNERSTATEMENT . '/';
$_downloadPath = "{$clientDirectory}/{$format}/" . DOC_OWNERSTATEMENT;
$file = 'DetailedCashBudgetReport_' . $propertyID . '_' . date('dmYHis') . rand() . '.pdf';
$filePath = $_filePath . $file;
$downloadPath = "{$_downloadPath}/{$file}";
// $context['MultixlsDownloadPath'] = array($filePath, $file);

$calendar = dbGetPropertyCalendar($propertyID, $year);
$reportDetail = new PDFDataReport($filePath, $logoPath, A4_LANDSCAPE);
$reportDetail->pageCount = $page;
$reportDetail->renderPageNumberFontSize = 8;
$reportDetail->doctype = 'DetailedCashBudget';

$reportDetail->addColumn('Unit', 'Unit', 30, 'left');
$reportDetail->addColumn('Description', 'Account', 120, 'left');
$ending = '';
foreach ($calendar as $cal) {
    $reportDetail->addColumn($cal['period'], date('M Y', strtotime($cal['date'])), 45, 'right');
    if ($cal['period'] == 12) {
        $ending = date('t F Y', strtotime($cal['date']));
    }
}

$reportDetail->addColumn('ytd', 'Total', 45, 'right');

$reportDetail->multiLine = true;
$reportDetail->printRowLines = true;
$reportDetail->printColumnLines = false;
$reportDetail->printBorders = false;
$reportDetail->cache = false;

$header = new ReportHeader('Profit and Loss', $subtitle, $prepared);
$header->xPos = $reportDetail->hMargin;
$header->yPos = $reportDetail->pageHeight - $reportDetail->vMargin;
$reportDetail->attachObject('header', $header);

$area = dbGetFloorsUnitArea($propertyID);
$floorArea = isset($area[0]) ? $area[0]['floorArea'] : 0;
$reportDetail->objects['header']->mainTitle = 'Detailed Cash Budget Report';
$reportDetail->objects['header']->subTitle =  'for year ending ' . $ending . ', Area: ' . $floorArea;
$reportDetail->objects['header']->subText = "for {$propertyName} ({$propertyID})";
$reportDetail->addSubHeaderItem('title', 0, 200, 'left');

$reportDetail->preparePage();

$reportDetail->setSubHeaderValue('title', 'Income');
$reportDetail->renderSubHeader();

$getCash = dbGetCashBudgetIncome($propertyID, $year);
$leaseArray = [];
foreach ($getCash as $lease) {
    $leaseArray[$lease['leaseCode']]['leaseCode'] = $lease['leaseCode'];
    $leaseArray[$lease['leaseCode']]['leaseName'] = $lease['leaseName'];
    $leaseArray[$lease['leaseCode']]['leaseUnit'] = $lease['unitCode'];
    $leaseArray[$lease['leaseCode']]['leaseDescription'] = $lease['unitDesc'];
    $leaseArray[$lease['leaseCode']]['leaseExpiry'] = $lease['leaseExpDate'];
    $leaseArray[$lease['leaseCode']]['leaseAccount'][$lease['accountID']][$lease['period']] = $lease;
}

$incomeTotal = [];
foreach ($leaseArray as $row) {

    $leaseData = [];
    $rentReview = dbGetLastOpenRentReview($propertyID, $row['leaseCode']);
    $leaseData['3'] = 'Review';
    $leaseData['4'] = $rentReview['rentReviewDate'] ? $rentReview['rentReviewDate'] : '-';
    $leaseData['5'] = 'Expiry';
    $leaseData['6'] = $row['leaseExpiry'] ? date('d/m/Y', strtotime($row['leaseExpiry'])) : '';
    $leaseData['Unit'] = $row['leaseCode'];
    $leaseData['Description'] = $row['leaseName'];
    $reportDetail->renderTenantWithReviews($leaseData);

    $unit['Unit'] = $row['leaseUnit'];
    $unit['Description'] = $row['leaseDescription'];
    $reportDetail->renderLine($unit);

    $subLeaseTotal = [];
    foreach ($row['leaseAccount'] as $account) {
        $subLeaseTotal[1][] = $account[1]['cashBudget'];
        $subLeaseTotal[2][] = $account[2]['cashBudget'];
        $subLeaseTotal[3][] = $account[3]['cashBudget'];
        $subLeaseTotal[4][] = $account[4]['cashBudget'];
        $subLeaseTotal[5][] = $account[5]['cashBudget'];
        $subLeaseTotal[6][] = $account[6]['cashBudget'];
        $subLeaseTotal[7][] = $account[7]['cashBudget'];
        $subLeaseTotal[8][] = $account[8]['cashBudget'];
        $subLeaseTotal[9][] = $account[9]['cashBudget'];
        $subLeaseTotal[10][] = $account[10]['cashBudget'];
        $subLeaseTotal[11][] = $account[11]['cashBudget'];
        $subLeaseTotal[12][] = $account[12]['cashBudget'];
        $subLeaseTotal['ytd'][] = $account[1]['cashBudget']  + $account[2]['cashBudget'] + $account[3]['cashBudget'] + $account[4]['cashBudget'] + $account[5]['cashBudget']
            + $account[6]['cashBudget'] + $account[7]['cashBudget'] + $account[8]['cashBudget'] + $account[9]['cashBudget'] + $account[10]['cashBudget'] + $account[11]['cashBudget'] + $account[12]['cashBudget'];

        $incomeTotal[1][] = $account[1]['cashBudget'];
        $incomeTotal[2][] = $account[2]['cashBudget'];
        $incomeTotal[3][] = $account[3]['cashBudget'];
        $incomeTotal[4][] = $account[4]['cashBudget'];
        $incomeTotal[5][] = $account[5]['cashBudget'];
        $incomeTotal[6][] = $account[6]['cashBudget'];
        $incomeTotal[7][] = $account[7]['cashBudget'];
        $incomeTotal[8][] = $account[8]['cashBudget'];
        $incomeTotal[9][] = $account[9]['cashBudget'];
        $incomeTotal[10][] = $account[10]['cashBudget'];
        $incomeTotal[11][] = $account[11]['cashBudget'];
        $incomeTotal[12][] = $account[12]['cashBudget'];
        $incomeTotal['ytd'][] = $account[1]['cashBudget']  + $account[2]['cashBudget'] + $account[3]['cashBudget'] + $account[4]['cashBudget'] + $account[5]['cashBudget']
            + $account[6]['cashBudget'] + $account[7]['cashBudget'] + $account[8]['cashBudget'] + $account[9]['cashBudget'] + $account[10]['cashBudget'] + $account[11]['cashBudget'] + $account[12]['cashBudget'];


        $renderAccount['Unit'] = $account[1]['accountID'];
        $renderAccount['Description'] = $account[1]['accountName'];
        $renderAccount['1'] = toMoney($account[1]['cashBudget']);
        $renderAccount['2'] = toMoney($account[2]['cashBudget']);
        $renderAccount['3'] = toMoney($account[3]['cashBudget']);
        $renderAccount['4'] = toMoney($account[4]['cashBudget']);
        $renderAccount['5'] = toMoney($account[5]['cashBudget']);
        $renderAccount['6'] = toMoney($account[6]['cashBudget']);
        $renderAccount['7'] = toMoney($account[7]['cashBudget']);
        $renderAccount['8'] = toMoney($account[8]['cashBudget']);
        $renderAccount['9'] = toMoney($account[9]['cashBudget']);
        $renderAccount['10'] = toMoney($account[10]['cashBudget']);
        $renderAccount['11'] = toMoney($account[11]['cashBudget']);
        $renderAccount['12'] = toMoney($account[12]['cashBudget']);
        $renderAccount['ytd'] = toMoney($account[1]['cashBudget']  + $account[2]['cashBudget'] + $account[3]['cashBudget'] + $account[4]['cashBudget'] + $account[5]['cashBudget']
            + $account[6]['cashBudget'] + $account[7]['cashBudget'] + $account[8]['cashBudget'] + $account[9]['cashBudget'] + $account[10]['cashBudget'] + $account[11]['cashBudget'] + $account[12]['cashBudget']);

        $reportDetail->renderLine($renderAccount);
    }

    $subTotal['Description'] = 'Lease Sub Total';
    $subTotal['1'] = toMoney(array_sum($subLeaseTotal[1]));
    $subTotal['2'] = toMoney(array_sum($subLeaseTotal[2]));
    $subTotal['3'] = toMoney(array_sum($subLeaseTotal[3]));
    $subTotal['4'] = toMoney(array_sum($subLeaseTotal[4]));
    $subTotal['5'] = toMoney(array_sum($subLeaseTotal[5]));
    $subTotal['6'] = toMoney(array_sum($subLeaseTotal[6]));
    $subTotal['7'] = toMoney(array_sum($subLeaseTotal[7]));
    $subTotal['8'] = toMoney(array_sum($subLeaseTotal[8]));
    $subTotal['9'] = toMoney(array_sum($subLeaseTotal[9]));
    $subTotal['10'] = toMoney(array_sum($subLeaseTotal[10]));
    $subTotal['11'] = toMoney(array_sum($subLeaseTotal[11]));
    $subTotal['12'] = toMoney(array_sum($subLeaseTotal[12]));
    $subTotal['ytd'] = toMoney(array_sum($subLeaseTotal['ytd']));
    $reportDetail->renderSubTotal($subTotal);

    $reportDetail->renderLine([]);
}

$renderIncomeTotal['Description'] = 'Income Total';
$renderIncomeTotal['1'] = toMoney(array_sum($incomeTotal[1]));
$renderIncomeTotal['2'] = toMoney(array_sum($incomeTotal[2]));
$renderIncomeTotal['3'] = toMoney(array_sum($incomeTotal[3]));
$renderIncomeTotal['4'] = toMoney(array_sum($incomeTotal[4]));
$renderIncomeTotal['5'] = toMoney(array_sum($incomeTotal[5]));
$renderIncomeTotal['6'] = toMoney(array_sum($incomeTotal[6]));
$renderIncomeTotal['7'] = toMoney(array_sum($incomeTotal[7]));
$renderIncomeTotal['8'] = toMoney(array_sum($incomeTotal[8]));
$renderIncomeTotal['9'] = toMoney(array_sum($incomeTotal[9]));
$renderIncomeTotal['10'] = toMoney(array_sum($incomeTotal[10]));
$renderIncomeTotal['11'] = toMoney(array_sum($incomeTotal[11]));
$renderIncomeTotal['12'] = toMoney(array_sum($incomeTotal[12]));
$renderIncomeTotal['ytd'] = toMoney(array_sum($incomeTotal['ytd']));
$reportDetail->renderSubTotal($renderIncomeTotal);

$incomeAreaTotal['Description'] = 'per ' . html_entity_decode($_SESSION['country_default']['area_unit'], ENT_COMPAT);
$incomeAreaTotal['1'] = toMoney((array_sum($incomeTotal[1]) / $floorArea), '');
$incomeAreaTotal['2'] = toMoney((array_sum($incomeTotal[2]) / $floorArea), '');
$incomeAreaTotal['3'] = toMoney((array_sum($incomeTotal[3]) / $floorArea), '');
$incomeAreaTotal['4'] = toMoney((array_sum($incomeTotal[4]) / $floorArea), '');
$incomeAreaTotal['5'] = toMoney((array_sum($incomeTotal[5]) / $floorArea), '');
$incomeAreaTotal['6'] = toMoney((array_sum($incomeTotal[6]) / $floorArea), '');
$incomeAreaTotal['7'] = toMoney((array_sum($incomeTotal[7]) / $floorArea), '');
$incomeAreaTotal['8'] = toMoney((array_sum($incomeTotal[8]) / $floorArea), '');
$incomeAreaTotal['9'] = toMoney((array_sum($incomeTotal[9]) / $floorArea), '');
$incomeAreaTotal['10'] = toMoney((array_sum($incomeTotal[10]) / $floorArea), '');
$incomeAreaTotal['11'] = toMoney((array_sum($incomeTotal[11]) / $floorArea), '');
$incomeAreaTotal['12'] = toMoney((array_sum($incomeTotal[12]) / $floorArea), '');
$incomeAreaTotal['ytd'] = toMoney((array_sum($incomeTotal['ytd']) / $floorArea), '');
$reportDetail->renderSubTotal($incomeAreaTotal);


$reportDetail->renderLine([]);
$reportDetail->setSubHeaderValue('title', 'Expenses');
$reportDetail->renderSubHeader();

$getExpenseCash = dbGetCashBudgetExpenses($propertyID, $year);
$accountArray = [];
foreach ($getExpenseCash as $accounts) {
    $accountArray[$accounts['accountID']][$accounts['period']] = $accounts;
}

$expenseTotal = [];
foreach ($accountArray as $account) {

    $expenseTotal[1][] = $account[1]['cashBudget'];
    $expenseTotal[2][] = $account[2]['cashBudget'];
    $expenseTotal[3][] = $account[3]['cashBudget'];
    $expenseTotal[4][] = $account[4]['cashBudget'];
    $expenseTotal[5][] = $account[5]['cashBudget'];
    $expenseTotal[6][] = $account[6]['cashBudget'];
    $expenseTotal[7][] = $account[7]['cashBudget'];
    $expenseTotal[8][] = $account[8]['cashBudget'];
    $expenseTotal[9][] = $account[9]['cashBudget'];
    $expenseTotal[10][] = $account[10]['cashBudget'];
    $expenseTotal[11][] = $account[11]['cashBudget'];
    $expenseTotal[12][] = $account[12]['cashBudget'];
    $expenseTotal['ytd'][] = $account[1]['cashBudget']  + $account[2]['cashBudget'] + $account[3]['cashBudget'] + $account[4]['cashBudget'] + $account[5]['cashBudget']
        + $account[6]['cashBudget'] + $account[7]['cashBudget'] + $account[8]['cashBudget'] + $account[9]['cashBudget'] + $account[10]['cashBudget'] + $account[11]['cashBudget'] + $account[12]['cashBudget'];


    $renderAccount['Unit'] = $account[1]['accountID'];
    $renderAccount['Description'] = $account[1]['accountName'];
    $renderAccount['1'] = toMoney($account[1]['cashBudget']);
    $renderAccount['2'] = toMoney($account[2]['cashBudget']);
    $renderAccount['3'] = toMoney($account[3]['cashBudget']);
    $renderAccount['4'] = toMoney($account[4]['cashBudget']);
    $renderAccount['5'] = toMoney($account[5]['cashBudget']);
    $renderAccount['6'] = toMoney($account[6]['cashBudget']);
    $renderAccount['7'] = toMoney($account[7]['cashBudget']);
    $renderAccount['8'] = toMoney($account[8]['cashBudget']);
    $renderAccount['9'] = toMoney($account[9]['cashBudget']);
    $renderAccount['10'] = toMoney($account[10]['cashBudget']);
    $renderAccount['11'] = toMoney($account[11]['cashBudget']);
    $renderAccount['12'] = toMoney($account[12]['cashBudget']);
    $renderAccount['ytd'] = toMoney($account[1]['cashBudget']  + $account[2]['cashBudget'] + $account[3]['cashBudget'] + $account[4]['cashBudget'] + $account[5]['cashBudget']
        + $account[6]['cashBudget'] + $account[7]['cashBudget'] + $account[8]['cashBudget'] + $account[9]['cashBudget'] + $account[10]['cashBudget'] + $account[11]['cashBudget'] + $account[12]['cashBudget']);

    $reportDetail->renderLine($renderAccount);
}

$renderExpenseTotal['Description'] = 'Expense Total';
$renderExpenseTotal['1'] = toMoney(array_sum($expenseTotal[1]));
$renderExpenseTotal['2'] = toMoney(array_sum($expenseTotal[2]));
$renderExpenseTotal['3'] = toMoney(array_sum($expenseTotal[3]));
$renderExpenseTotal['4'] = toMoney(array_sum($expenseTotal[4]));
$renderExpenseTotal['5'] = toMoney(array_sum($expenseTotal[5]));
$renderExpenseTotal['6'] = toMoney(array_sum($expenseTotal[6]));
$renderExpenseTotal['7'] = toMoney(array_sum($expenseTotal[7]));
$renderExpenseTotal['8'] = toMoney(array_sum($expenseTotal[8]));
$renderExpenseTotal['9'] = toMoney(array_sum($expenseTotal[9]));
$renderExpenseTotal['10'] = toMoney(array_sum($expenseTotal[10]));
$renderExpenseTotal['11'] = toMoney(array_sum($expenseTotal[11]));
$renderExpenseTotal['12'] = toMoney(array_sum($expenseTotal[12]));
$renderExpenseTotal['ytd'] = toMoney(array_sum($expenseTotal['ytd']));
$reportDetail->renderSubTotal($renderExpenseTotal);

$expenseAreaTotal['Description'] = 'per ' . html_entity_decode($_SESSION['country_default']['area_unit'], ENT_COMPAT);
$expenseAreaTotal['1'] = toMoney((array_sum($expenseTotal[1]) / $floorArea), '');
$expenseAreaTotal['2'] = toMoney((array_sum($expenseTotal[2]) / $floorArea), '');
$expenseAreaTotal['3'] = toMoney((array_sum($expenseTotal[3]) / $floorArea), '');
$expenseAreaTotal['4'] = toMoney((array_sum($expenseTotal[4]) / $floorArea), '');
$expenseAreaTotal['5'] = toMoney((array_sum($expenseTotal[5]) / $floorArea), '');
$expenseAreaTotal['6'] = toMoney((array_sum($expenseTotal[6]) / $floorArea), '');
$expenseAreaTotal['7'] = toMoney((array_sum($expenseTotal[7]) / $floorArea), '');
$expenseAreaTotal['8'] = toMoney((array_sum($expenseTotal[8]) / $floorArea), '');
$expenseAreaTotal['9'] = toMoney((array_sum($expenseTotal[9]) / $floorArea), '');
$expenseAreaTotal['10'] = toMoney((array_sum($expenseTotal[10]) / $floorArea), '');
$expenseAreaTotal['11'] = toMoney((array_sum($expenseTotal[11]) / $floorArea), '');
$expenseAreaTotal['12'] = toMoney((array_sum($expenseTotal[12]) / $floorArea), '');
$expenseAreaTotal['ytd'] = toMoney((array_sum($expenseTotal['ytd']) / $floorArea), '');
$reportDetail->renderSubTotal($expenseAreaTotal);

$reportDetail->renderLine([]);

$netIncome[1] = array_sum($incomeTotal[1]) - array_sum($expenseTotal[1]);
$netIncome[2] = array_sum($incomeTotal[2]) - array_sum($expenseTotal[2]);
$netIncome[3] = array_sum($incomeTotal[3]) - array_sum($expenseTotal[3]);
$netIncome[4] = array_sum($incomeTotal[4]) - array_sum($expenseTotal[4]);
$netIncome[5] = array_sum($incomeTotal[5]) - array_sum($expenseTotal[5]);
$netIncome[6] = array_sum($incomeTotal[6]) - array_sum($expenseTotal[6]);
$netIncome[7] = array_sum($incomeTotal[7]) - array_sum($expenseTotal[7]);
$netIncome[8] = array_sum($incomeTotal[8]) - array_sum($expenseTotal[8]);
$netIncome[9] = array_sum($incomeTotal[9]) - array_sum($expenseTotal[9]);
$netIncome[10] = array_sum($incomeTotal[10]) - array_sum($expenseTotal[10]);
$netIncome[11] = array_sum($incomeTotal[11]) - array_sum($expenseTotal[11]);
$netIncome[12] = array_sum($incomeTotal[12]) - array_sum($expenseTotal[12]);
$netIncome['ytd'] = array_sum($incomeTotal['ytd']) - array_sum($expenseTotal['ytd']);


$renderNetTotal['Description'] = 'Net Income';
$renderNetTotal['1'] = toMoney(($netIncome[1]));
$renderNetTotal['2'] = toMoney(($netIncome[2]));
$renderNetTotal['3'] = toMoney(($netIncome[3]));
$renderNetTotal['4'] = toMoney(($netIncome[4]));
$renderNetTotal['5'] = toMoney(($netIncome[5]));
$renderNetTotal['6'] = toMoney(($netIncome[6]));
$renderNetTotal['7'] = toMoney(($netIncome[7]));
$renderNetTotal['8'] = toMoney(($netIncome[8]));
$renderNetTotal['9'] = toMoney(($netIncome[9]));
$renderNetTotal['10'] = toMoney(($netIncome[10]));
$renderNetTotal['11'] = toMoney(($netIncome[11]));
$renderNetTotal['12'] = toMoney(($netIncome[12]));
$renderNetTotal['ytd'] = toMoney(($netIncome['ytd']));
$reportDetail->renderSubTotal($renderNetTotal);

$netAreaTotal['Description'] = 'per ' . html_entity_decode($_SESSION['country_default']['area_unit'], ENT_COMPAT);
$netAreaTotal['1'] = toMoney((($netIncome[1]) / $floorArea), '');
$netAreaTotal['2'] = toMoney((($netIncome[2]) / $floorArea), '');
$netAreaTotal['3'] = toMoney((($netIncome[3]) / $floorArea), '');
$netAreaTotal['4'] = toMoney((($netIncome[4]) / $floorArea), '');
$netAreaTotal['5'] = toMoney((($netIncome[5]) / $floorArea), '');
$netAreaTotal['6'] = toMoney((($netIncome[6]) / $floorArea), '');
$netAreaTotal['7'] = toMoney((($netIncome[7]) / $floorArea), '');
$netAreaTotal['8'] = toMoney((($netIncome[8]) / $floorArea), '');
$netAreaTotal['9'] = toMoney((($netIncome[9]) / $floorArea), '');
$netAreaTotal['10'] = toMoney((($netIncome[10]) / $floorArea), '');
$netAreaTotal['11'] = toMoney((($netIncome[11]) / $floorArea), '');
$netAreaTotal['12'] = toMoney((($netIncome[12]) / $floorArea), '');
$netAreaTotal['ytd'] = toMoney((($netIncome['ytd']) / $floorArea), '');
$reportDetail->renderSubTotal($netAreaTotal);

$reportDetail->clean();
$reportDetail->endPage();
$reportDetail->close();

// MERGE PDF FILE TO OWNER REPORT
$pdi = new ImportedPage($pdf, $filePath, 2);
$i = 1;
while ($pdi->loadPage($i)) {
    $page++;
    $pdi->preparePage();
    $pdi->render();
    $pdi->endPage();
    $i++;
}

$pdi->close();

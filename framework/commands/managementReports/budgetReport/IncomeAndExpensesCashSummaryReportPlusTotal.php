<?php

include_once __DIR__ . '/budgetExpenditureFunctions.php';

$period = dbGetPeriod($propertyID, $view->items['periodTo']);
$year = $period['year'];

$logoFile = dbGetClientLogo();
$logoPath = "assets/clientLogos/{$logoFile}";
$format = 'pdf';

$_filePath = "{$pathPrefix}{$clientDirectory}/{$format}/" . DOC_OWNERSTATEMENT . '/';
$_downloadPath = "{$clientDirectory}/{$format}/" . DOC_OWNERSTATEMENT;
$file = 'IncomeAndExpensesSummaryReport_' . $propertyID . '_' . date('dmYHis') . rand() . '.pdf';
$filePath = $_filePath . $file;
$downloadPath = "{$_downloadPath}/{$file}";
// $context['MultixlsDownloadPath'] = array($filePath, $file);

// $calendar = dbGetPropertyCalendar($propertyID,$year);
$reportDetail = new PDFDataReport($filePath, $logoPath, A4_LANDSCAPE);
$reportDetail->pageCount = $page;
$reportDetail->renderPageNumberFontSize = 8;
$reportDetail->doctype = 'IncomeAndExpensesCashSummary';

$reportDetail->addColumn('Unit', '', 30, 'left');
$reportDetail->addColumn('Description', '', 120, 'left');
$reportDetail->addColumn('CActual', '', 45, 'right');
$reportDetail->addColumn('CBudget', '', 45, 'right');
$reportDetail->addColumn('CVariance$', 'Current', 55, 'right');
$reportDetail->addColumn('CVariance%', '', 45, 'right');
$reportDetail->addColumn('YActual', '', 45, 'right');
$reportDetail->addColumn('YBudget', '', 45, 'right');
$reportDetail->addColumn('YVariance$', 'YTD', 55, 'right');
$reportDetail->addColumn('YVariance%', '', 45, 'right');
$reportDetail->addColumn('FActual', '', 45, 'right');
$reportDetail->addColumn('FBudget', '', 45, 'right');
$reportDetail->addColumn('FVariance$', 'Full Year', 55, 'right');
$reportDetail->addColumn('FVariance%', '', 45, 'right');

$reportDetail->columns['CVariance$']->alignment2 = 'left';
$reportDetail->columns['YVariance$']->alignment2 = 'left';
$reportDetail->columns['FVariance$']->alignment2 = 'left';

$reportDetail->multiLine = true;
$reportDetail->printRowLines = true;
$reportDetail->printColumnLines = false;
$reportDetail->printBorders = false;
$reportDetail->cache = false;

$header = new ReportHeader('Profit and Loss', $subtitle, $prepared);
$header->xPos = $reportDetail->hMargin;
$header->yPos = $reportDetail->pageHeight - $reportDetail->vMargin;
$reportDetail->attachObject('header', $header);

$reportDetail->objects['header']->mainTitle = 'Income And Expenses Summary Report - Cash';
$reportDetail->objects['header']->subTitle =  "for the period of {$periodFrom} to {$periodTo}";
$reportDetail->objects['header']->subText = "for {$propertyName} ({$propertyID})";
$reportDetail->addSubHeaderItem('title', 0, 200, 'left');

$subheader['Unit'] = 'Account';
$subheader['Description'] = '';
$subheader['CActual'] = 'Actual';
$subheader['CBudget'] = 'Budget';
$subheader['CVariance$'] = 'Var ($)';
$subheader['CVariance%'] = 'Var (%)';
$subheader['YActual'] = 'Actual';
$subheader['YBudget'] = 'Budget';
$subheader['YVariance$'] = 'Var ($)';
$subheader['YVariance%'] = 'Var (%)';
$subheader['FActual'] = 'Forecast';
$subheader['FBudget'] = 'Budget';
$subheader['FVariance$'] = 'Var ($)';
$subheader['FVariance%'] = 'Var (%)';
$reportDetail->addKeyHeaders($subheader, 2);

$reportDetail->preparePage();

// $reportDetail->renderSubTotal($subheader,0.7);

$reportDetail->setSubHeaderValue('title', 'Property Income');
$reportDetail->renderSubHeader();

$leaseArray = [];
$charge = [
    'O' => 'OTHERS',
    'P' => 'CAR PARK INCOME',
    'V' => 'OUTGOINGS',
    'R' => 'RENTAL INCOME',
];

$tenantcode = '#';
$group_array = ['INCOWN', 'INCVO', 'INCDR'];
foreach ($group_array as $key => $group) {

    $sublines = 0;
    // $merged = getAccountCodes_statement($tenantcode, $propertyID, $periodTo, $group);

    if ($group === 'INCOWN') {
        $group_name = 'Rental Income';
    }

    if ($group === 'INCDR') {
        $group_name = 'Directly Recoverable Income';
    }

    if ($group === 'INCVO') {
        $group_name = 'Outgoings Income';
    }

    if ($group === 'BSHEET') {
        $group_name = 'Non-Operating Income';
    }

    $currentActual = dbGetIncomeStatementActual($propertyID, $year, $group, $currentPeriod, $toPeriod);
    foreach ($currentActual as $account) {

        $leaseArray[$group][$account['account']]['currentActual'] = $account['amount'];
        $leaseArray[$group][$account['account']]['accountID'] = $account['account'];
        $leaseArray[$group][$account['account']]['accountName'] = $account['account_name'];
        $leaseArray[$group][$account['account']]['chargeType'] = $group_name;
    }

    $ytdActual = dbGetIncomeStatementActual($propertyID, $year, $group, 1, $toPeriod);
    foreach ($ytdActual as $account) {

        $leaseArray[$group][$account['account']]['fullForecast'] = $account['amount'];
        $leaseArray[$group][$account['account']]['ytdActual'] = $account['amount'];
        $leaseArray[$group][$account['account']]['accountID'] = $account['account'];
        $leaseArray[$group][$account['account']]['accountName'] = $account['account_name'];
        $leaseArray[$group][$account['account']]['chargeType'] = $group_name;
    }


    // foreach ($merged as $account) {

    //     $account_name = getAccountName($account);
    //     $paid = getPaid($account, $propertyID, $tenantcode, $periodFrom);

    //     $paidgstfree = getRecievedThisMonthAll($propertyID, $tenantcode, $periodFrom, $periodTo, $account);//GST free amounts received - needs to exclude unallocated amounts with GST
    //     $unallocatedAmountForGST = getUnallocCashTaxable($propertyID, $tenantcode, $periodFrom, $periodTo, $account, false);
    //     $paidThisMonthPre = getRecievedThisMonthAll($propertyID, $tenantcode, $periodFrom, $periodTo, $account, false);

    //     $paidThisMonth = 	($paidThisMonthPre + $unallocatedAmountForGST['netAmount'] + $paidgstfree) * -1;

    //     $leaseArray[$group][$account]['currentActual'] = $paidThisMonth;
    //     $leaseArray[$group][$account]['accountID'] = $account;
    //     $leaseArray[$group][$account]['accountName'] = $account_name;
    //     $leaseArray[$group][$account]['chargeType'] = $group_name;

    //     $paidgstfree = getRecievedThisMonthAll($propertyID, $tenantcode, $startFinancialYear , $periodTo, $account);//GST free amounts received - needs to exclude unallocated amounts with GST
    //     $unallocatedAmountForGST = getUnallocCashTaxable($propertyID, $tenantcode, $startFinancialYear , $periodTo, $account, false);
    //     $paidThisYTDPre = getRecievedThisMonthAll($propertyID, $tenantcode, $startFinancialYear , $periodTo, $account, false);

    //     $paidThisYTD = 	($paidThisYTDPre + $unallocatedAmountForGST['netAmount'] + $paidgstfree) * -1;
    //     $leaseArray[$group][$account]['ytdActual'] = $paidThisYTD;
    //     $leaseArray[$group][$account]['fullForecast'] = $paidThisYTD;

    // }

    // if(!empty($merged)){

    $getCash = dbGetIncomeStatementBudget($propertyID, $year, $group);
    foreach ($getCash as $lease) {
        $leaseArray[$group][$lease['accountID']]['fullBudget'] = $lease['cashBudget'];
        $leaseArray[$group][$lease['accountID']]['accountID'] = $lease['accountID'];
        $leaseArray[$group][$lease['accountID']]['accountName'] = $lease['accountName'];
        $leaseArray[$group][$lease['accountID']]['chargeType'] = $group_name;
    }

    $getCash = dbGetIncomeStatementBudget($propertyID, $year, $group, $toPeriod);
    foreach ($getCash as $lease) {
        $leaseArray[$group][$lease['accountID']]['ytdBudget'] = $lease['cashBudget'];
        $leaseArray[$group][$lease['accountID']]['accountID'] = $lease['accountID'];
        $leaseArray[$group][$lease['accountID']]['accountName'] = $lease['accountName'];
        $leaseArray[$group][$lease['accountID']]['chargeType'] = $group_name;
    }

    $getCash = dbGetIncomeStatementBudget($propertyID, $year, $group, $toPeriod, $currentPeriod);
    foreach ($getCash as $lease) {
        $leaseArray[$group][$lease['accountID']]['currentBudget'] = $lease['cashBudget'];
        $leaseArray[$group][$lease['accountID']]['accountID'] = $lease['accountID'];
        $leaseArray[$group][$lease['accountID']]['accountName'] = $lease['accountName'];
        $leaseArray[$group][$lease['accountID']]['chargeType'] = $group_name;
    }

    $getCash = dbGetIncomeStatementForecast($propertyID, $year, $group, $toPeriod);
    foreach ($getCash as $lease) {
        $leaseArray[$group][$lease['accountID']]['fullForecast'] = $lease['cashForecast'] + ($leaseArray[$group][$lease['accountID']]['fullForecast'] ?? 0);
        $leaseArray[$group][$lease['accountID']]['accountID'] = $lease['accountID'];
        $leaseArray[$group][$lease['accountID']]['accountName'] = $lease['accountName'];
        $leaseArray[$group][$lease['accountID']]['chargeType'] = $group_name;
    }

    // }


}

$incomeTotal = [];
foreach ($leaseArray as $row) {

    $subLeaseTotal = [];
    ksort($row);
    foreach ($row as $account) {
        if ($account['fullBudget'] || $account['ytdActual'] || $account['fullForecast']) {

            // $account['fullForecast'] = $account['fullForecast'] + $account['ytdActual'];

            $varianceCurrent = $account['currentActual'] - $account['currentBudget'];
            $varianceYTD = $account['ytdActual'] - $account['ytdBudget'];
            $varianceFull = $account['fullForecast'] - $account['fullBudget'];
            $varianceCurrentP = variancePercentageReturnZero($varianceCurrent, $account['currentBudget']);
            $varianceYTDP = variancePercentageReturnZero($varianceYTD, $account['ytdBudget']);
            $varianceFullP = variancePercentageReturnZero($varianceFull, $account['fullBudget']);

            $subLeaseTotal['CBudget'][] = $account['currentBudget'];
            $subLeaseTotal['YBudget'][] = $account['ytdBudget'];
            $subLeaseTotal['FBudget'][] = $account['fullBudget'];
            $subLeaseTotal['CActual'][] = $account['currentActual'];
            $subLeaseTotal['YActual'][] = $account['ytdActual'];
            $subLeaseTotal['FActual'][] = $account['fullForecast'];
            $subLeaseTotal['CVariance$'][] = $varianceCurrent;
            $subLeaseTotal['YVariance$'][] = $varianceYTD;
            $subLeaseTotal['FVariance$'][] = $varianceFull;

            $incomeTotal['CBudget'][] = $account['currentBudget'];
            $incomeTotal['YBudget'][] = $account['ytdBudget'];
            $incomeTotal['FBudget'][] = $account['fullBudget'];
            $incomeTotal['CActual'][] = $account['currentActual'];
            $incomeTotal['YActual'][] = $account['ytdActual'];
            $incomeTotal['FActual'][] = $account['fullForecast'];
            $incomeTotal['CVariance$'][] = $varianceCurrent;
            $incomeTotal['YVariance$'][] = $varianceYTD;
            $incomeTotal['FVariance$'][] = $varianceFull;


            $renderAccount['Unit'] = $account['accountID'];
            $renderAccount['Description'] = $account['accountName'];
            $renderAccount['CBudget'] = toMoney($account['currentBudget']);
            $renderAccount['YBudget'] = toMoney($account['ytdBudget']);
            $renderAccount['FBudget'] = toMoney($account['fullBudget']);
            $renderAccount['CActual'] = toMoney($account['currentActual']);
            $renderAccount['YActual'] = toMoney($account['ytdActual']);
            $renderAccount['FActual'] = toMoney($account['fullForecast']);
            $renderAccount['CVariance$'] = toDecimal($varianceCurrent);
            $renderAccount['CVariance%'] = toDecimal($varianceCurrentP);
            $renderAccount['YVariance$'] = toDecimal($varianceYTD);
            $renderAccount['YVariance%'] = toDecimal($varianceYTDP);
            $renderAccount['FVariance$'] = toDecimal($varianceFull);
            $renderAccount['FVariance%'] = toDecimal($varianceFullP);

            $reportDetail->renderLine($renderAccount, true);
        }
    }

    if (! empty($subLeaseTotal['YActual']) || ! empty($subLeaseTotal['FBudget']) || ! empty($subLeaseTotal['FActual'])) {

        $varianceCurrentP = variancePercentageReturnZero(array_sum($subLeaseTotal['CVariance$']), array_sum($subLeaseTotal['CBudget']));
        $varianceYTDP = variancePercentageReturnZero(array_sum($subLeaseTotal['YVariance$']), array_sum($subLeaseTotal['YBudget']));
        $varianceFullP = variancePercentageReturnZero(array_sum($subLeaseTotal['FVariance$']), array_sum($subLeaseTotal['FBudget']));

        $subTotal['Description'] = $account['chargeType'];
        $subTotal['CBudget'] = toMoney(array_sum($subLeaseTotal['CBudget']));
        $subTotal['YBudget'] = toMoney(array_sum($subLeaseTotal['YBudget']));
        $subTotal['FBudget'] = toMoney(array_sum($subLeaseTotal['FBudget']));
        $subTotal['CActual'] = toMoney(array_sum($subLeaseTotal['CActual']));
        $subTotal['YActual'] = toMoney(array_sum($subLeaseTotal['YActual']));
        $subTotal['FActual'] = toMoney(array_sum($subLeaseTotal['FActual']));
        $subTotal['CVariance$'] = toDecimal(array_sum($subLeaseTotal['CVariance$']));
        $subTotal['CVariance%'] = toDecimal($varianceCurrentP);
        $subTotal['YVariance$'] = toDecimal(array_sum($subLeaseTotal['YVariance$']));
        $subTotal['YVariance%'] = toDecimal($varianceYTDP);
        $subTotal['FVariance$'] = toDecimal(array_sum($subLeaseTotal['FVariance$']));
        $subTotal['FVariance%'] = toDecimal($varianceFullP);
        $reportDetail->renderSubTotal($subTotal);

        $reportDetail->renderLine([]);
    }
}

$varianceCurrentP = variancePercentageReturnZero(array_sum($incomeTotal['CVariance$']), array_sum($incomeTotal['CBudget']));
$varianceYTDP = variancePercentageReturnZero(array_sum($incomeTotal['YVariance$']), array_sum($incomeTotal['YBudget']));
$varianceFullP = variancePercentageReturnZero(array_sum($incomeTotal['FVariance$']), array_sum($incomeTotal['FBudget']));

$renderIncomeTotal['Description'] = 'Total Property Income';
$renderIncomeTotal['CBudget'] = toMoney(array_sum($incomeTotal['CBudget']));
$renderIncomeTotal['YBudget'] = toMoney(array_sum($incomeTotal['YBudget']));
$renderIncomeTotal['FBudget'] = toMoney(array_sum($incomeTotal['FBudget']));
$renderIncomeTotal['CActual'] = toMoney(array_sum($incomeTotal['CActual']));
$renderIncomeTotal['YActual'] = toMoney(array_sum($incomeTotal['YActual']));
$renderIncomeTotal['FActual'] = toMoney(array_sum($incomeTotal['FActual']));
$renderIncomeTotal['CVariance$'] = toDecimal(array_sum($incomeTotal['CVariance$']));
$renderIncomeTotal['CVariance%'] = toDecimal($varianceCurrentP);
$renderIncomeTotal['YVariance$'] = toDecimal(array_sum($incomeTotal['YVariance$']));
$renderIncomeTotal['YVariance%'] = toDecimal($varianceYTDP);
$renderIncomeTotal['FVariance$'] = toDecimal(array_sum($incomeTotal['FVariance$']));
$renderIncomeTotal['FVariance%'] = toDecimal($varianceFullP);

$reportDetail->renderSubTotal($renderIncomeTotal);

$reportDetail->_updateLineOffset(900);

$reportDetail->renderLine([]);

$ag = dbGetAccountGroups('MAJOR', EXPENDITURE);
$expenditureAccountGroupsUnorder = mapParameters($ag, 'accountGroup', 'groupDescription');

$sortOrder = ['EXPVO', 'EXPDR', 'EXPOWN', 'BSPMT'];
$expenditureAccountGroups = [];
$accountArray = [];
foreach ($sortOrder as $key) {
    $expenditureAccountGroups[$key] = $expenditureAccountGroupsUnorder[$key];
}

foreach ($expenditureAccountGroups as $groupCode => $groupName) {

    switch ($groupCode) {
        case 'EXPOWN': $a = getAcctSubGroupOwnerExp_statement();
            break;
        case 'EXPDR': $a = getAcctSubGroupDRVO_statement('EXPDR%');
            break;
        case 'EXPVO': $a = getAcctSubGroupDRVO_statement('EXPVO%');
            break;
        case 'BSPMT': $a = getAcctSubGroupDRVO_statement('BSPMTCAPI%');
            break;
    }

    foreach ($a as $thisGroup) {
        $subGroupCode = trim($thisGroup['subgroup']);
        $subGroupName = $thisGroup['pmas_desc'];

        $subAccountsOwner = dbGetExpensesStatementActual($propertyID, $year, $subGroupCode, 1, $toPeriod);
        foreach ($subAccountsOwner as $accounts) {
            $accountArray[$groupCode][$thisGroup['pmas_seq']][$subGroupName][$accounts['account_code']]['fullForecast'] = $accounts['net_amount'];
            $accountArray[$groupCode][$thisGroup['pmas_seq']][$subGroupName][$accounts['account_code']]['ytdActual'] = $accounts['net_amount'];
            $accountArray[$groupCode][$thisGroup['pmas_seq']][$subGroupName][$accounts['account_code']]['accountID'] = $accounts['account_code'];
            $accountArray[$groupCode][$thisGroup['pmas_seq']][$subGroupName][$accounts['account_code']]['accountName'] = $accounts['account_name'];
            $accountArray[$groupCode][$thisGroup['pmas_seq']][$subGroupName][$accounts['account_code']]['subCategory'] = $subGroupName;
        }

        $subAccountsOwner = dbGetExpensesStatementActual($propertyID, $year, $subGroupCode, $currentPeriod, $toPeriod);
        foreach ($subAccountsOwner as $accounts) {
            $accountArray[$groupCode][$thisGroup['pmas_seq']][$subGroupName][$accounts['account_code']]['currentActual'] = $accounts['net_amount'];
            $accountArray[$groupCode][$thisGroup['pmas_seq']][$subGroupName][$accounts['account_code']]['accountID'] = $accounts['account_code'];
            $accountArray[$groupCode][$thisGroup['pmas_seq']][$subGroupName][$accounts['account_code']]['accountName'] = $accounts['account_name'];
            $accountArray[$groupCode][$thisGroup['pmas_seq']][$subGroupName][$accounts['account_code']]['subCategory'] = $subGroupName;
        }
    }

    //  if ($groupCode != "EXPOWN") {
    $getExpenseCash = dbGetExpenseStatementBudget($propertyID, $year, $groupCode);
    foreach ($getExpenseCash as $accounts) {
        $accountArray[$groupCode][$accounts['pmas_seq']][($accounts['pmas_desc'])][$accounts['accountID']]['fullBudget'] = $accounts['cashBudget'];
        $accountArray[$groupCode][$accounts['pmas_seq']][($accounts['pmas_desc'])][$accounts['accountID']]['accountID'] = $accounts['accountID'];
        $accountArray[$groupCode][$accounts['pmas_seq']][($accounts['pmas_desc'])][$accounts['accountID']]['accountName'] = $accounts['accountName'];
        $accountArray[$groupCode][$accounts['pmas_seq']][($accounts['pmas_desc'])][$accounts['accountID']]['subCategory'] = $accounts['pmas_desc'];
    }

    $getExpenseCash = dbGetExpenseStatementBudget($propertyID, $year, $groupCode, $toPeriod);
    foreach ($getExpenseCash as $accounts) {
        $accountArray[$groupCode][$accounts['pmas_seq']][$accounts['pmas_desc']][$accounts['accountID']]['ytdBudget'] = $accounts['cashBudget'];
        $accountArray[$groupCode][$accounts['pmas_seq']][$accounts['pmas_desc']][$accounts['accountID']]['accountID'] = $accounts['accountID'];
        $accountArray[$groupCode][$accounts['pmas_seq']][$accounts['pmas_desc']][$accounts['accountID']]['accountName'] = $accounts['accountName'];
        $accountArray[$groupCode][$accounts['pmas_seq']][$accounts['pmas_desc']][$accounts['accountID']]['subCategory'] = $accounts['pmas_desc'];
    }

    $getExpenseCash = dbGetExpenseStatementBudget($propertyID, $year, $groupCode, $toPeriod, $currentPeriod);
    foreach ($getExpenseCash as $accounts) {
        $accountArray[$groupCode][$accounts['pmas_seq']][$accounts['pmas_desc']][$accounts['accountID']]['currentBudget'] = $accounts['cashBudget'];
        $accountArray[$groupCode][$accounts['pmas_seq']][$accounts['pmas_desc']][$accounts['accountID']]['accountID'] = $accounts['accountID'];
        $accountArray[$groupCode][$accounts['pmas_seq']][$accounts['pmas_desc']][$accounts['accountID']]['accountName'] = $accounts['accountName'];
        $accountArray[$groupCode][$accounts['pmas_seq']][$accounts['pmas_desc']][$accounts['accountID']]['subCategory'] = $accounts['pmas_desc'];
    }

    $getExpenseCash = dbGetExpensesStatementForecast($propertyID, $year, $groupCode, $toPeriod);
    foreach ($getExpenseCash as $accounts) {
        $accountArray[$groupCode][$accounts['pmas_seq']][$accounts['pmas_desc']][$accounts['accountID']]['fullForecast'] = $accounts['cashForecast'] + ($accountArray[$groupCode][$accounts['pmas_seq']][$accounts['pmas_desc']][$accounts['accountID']]['fullForecast'] ?? 0);
        $accountArray[$groupCode][$accounts['pmas_seq']][$accounts['pmas_desc']][$accounts['accountID']]['accountID'] = $accounts['accountID'];
        $accountArray[$groupCode][$accounts['pmas_seq']][$accounts['pmas_desc']][$accounts['accountID']]['accountName'] = $accounts['accountName'];
        $accountArray[$groupCode][$accounts['pmas_seq']][$accounts['pmas_desc']][$accounts['accountID']]['subCategory'] = $accounts['pmas_desc'];
    }

    // }
}

$expenseTotal = [];
$expenseNonRecoTotal = [];
foreach ($expenditureAccountGroups as $groupCode => $groupName) {

    switch ($groupCode) {
        case 'EXPOWN':$groupName = 'Owner Expenses';
            break;
        case 'EXPDR': $groupName = 'Direct Recoverable Expenses';
            break;
        case 'EXPVO': $groupName = 'Property Expense';
            break;
        case 'BSPMT': $groupName = 'Capital Expenditures Item';
            break;
    }

    if (! empty($accountArray[$groupCode])) {
        $reportDetail->setSubHeaderValue('title', $groupName);
        $reportDetail->renderSubHeader();

        $subGroupTotal = [];

        ksort($accountArray[$groupCode]);
        foreach ($accountArray[$groupCode] as $row2) {
            foreach ($row2 as $row) {
                $subLeaseTotal = [];
                ksort($row);
                foreach ($row as $account) {

                    //   $account['fullForecast'] = $account['fullForecast'] + $account['ytdActual'];

                    $varianceCurrent = $account['currentBudget'] - $account['currentActual'];
                    $varianceYTD = $account['ytdBudget'] - $account['ytdActual'];
                    $varianceFull = $account['fullBudget'] - $account['fullForecast'];
                    $varianceCurrentP = variancePercentageReturnZero($varianceCurrent, $account['currentBudget']);
                    $varianceYTDP = variancePercentageReturnZero($varianceYTD, $account['ytdBudget']);
                    $varianceFullP = variancePercentageReturnZero($varianceFull, $account['fullBudget']);

                    $subGroupTotal['CBudget'][] = $account['currentBudget'];
                    $subGroupTotal['YBudget'][] = $account['ytdBudget'];
                    $subGroupTotal['FBudget'][] = $account['fullBudget'];
                    $subGroupTotal['CActual'][] = $account['currentActual'];
                    $subGroupTotal['YActual'][] = $account['ytdActual'];
                    $subGroupTotal['FActual'][] = $account['fullForecast'];
                    $subGroupTotal['CVariance$'][] = $varianceCurrent;
                    $subGroupTotal['YVariance$'][] = $varianceYTD;
                    $subGroupTotal['FVariance$'][] = $varianceFull;

                    $subLeaseTotal['CBudget'][] = $account['currentBudget'];
                    $subLeaseTotal['YBudget'][] = $account['ytdBudget'];
                    $subLeaseTotal['FBudget'][] = $account['fullBudget'];
                    $subLeaseTotal['CActual'][] = $account['currentActual'];
                    $subLeaseTotal['YActual'][] = $account['ytdActual'];
                    $subLeaseTotal['FActual'][] = $account['fullForecast'];
                    $subLeaseTotal['CVariance$'][] = $varianceCurrent;
                    $subLeaseTotal['YVariance$'][] = $varianceYTD;
                    $subLeaseTotal['FVariance$'][] = $varianceFull;


                    $expenseTotal['CBudget'][] = $account['currentBudget'];
                    $expenseTotal['YBudget'][] = $account['ytdBudget'];
                    $expenseTotal['FBudget'][] = $account['fullBudget'];
                    $expenseTotal['CActual'][] = $account['currentActual'];
                    $expenseTotal['YActual'][] = $account['ytdActual'];
                    $expenseTotal['FActual'][] = $account['fullForecast'];
                    $expenseTotal['CVariance$'][] = $varianceCurrent;
                    $expenseTotal['YVariance$'][] = $varianceYTD;
                    $expenseTotal['FVariance$'][] = $varianceFull;

                    $expenseNonRecoTotal['CBudget'][] = $account['currentBudget'];
                    $expenseNonRecoTotal['YBudget'][] = $account['ytdBudget'];
                    $expenseNonRecoTotal['FBudget'][] = $account['fullBudget'];
                    $expenseNonRecoTotal['CActual'][] = $account['currentActual'];
                    $expenseNonRecoTotal['YActual'][] = $account['ytdActual'];
                    $expenseNonRecoTotal['FActual'][] = $account['fullForecast'];
                    $expenseNonRecoTotal['CVariance$'][] = $varianceCurrent;
                    $expenseNonRecoTotal['YVariance$'][] = $varianceYTD;
                    $expenseNonRecoTotal['FVariance$'][] = $varianceFull;

                    $renderAccount['Unit'] = $account['accountID'];
                    $renderAccount['Description'] = $account['accountName'];
                    $renderAccount['CBudget'] = toMoney($account['currentBudget']);
                    $renderAccount['YBudget'] = toMoney($account['ytdBudget']);
                    $renderAccount['FBudget'] = toMoney($account['fullBudget']);
                    $renderAccount['CActual'] = toMoney($account['currentActual']);
                    $renderAccount['YActual'] = toMoney($account['ytdActual']);
                    $renderAccount['FActual'] = toMoney($account['fullForecast']);
                    $renderAccount['CVariance$'] = toDecimal($varianceCurrent);
                    $renderAccount['CVariance%'] = toDecimal($varianceCurrentP);
                    $renderAccount['YVariance$'] = toDecimal($varianceYTD);
                    $renderAccount['YVariance%'] = toDecimal($varianceYTDP);
                    $renderAccount['FVariance$'] = toDecimal($varianceFull);
                    $renderAccount['FVariance%'] = toDecimal($varianceFullP);

                    $reportDetail->renderLine($renderAccount, true);
                }
            }

            if ($groupCode === 'EXPVO') {
                $varianceCurrentP = variancePercentageReturnZero(array_sum($subLeaseTotal['CVariance$']), array_sum($subLeaseTotal['CBudget']));
                $varianceYTDP = variancePercentageReturnZero(array_sum($subLeaseTotal['YVariance$']), array_sum($subLeaseTotal['YBudget']));
                $varianceFullP = variancePercentageReturnZero(array_sum($subLeaseTotal['FVariance$']), array_sum($subLeaseTotal['FBudget']));

                $subTotal['Description'] = $account['subCategory'];
                $subTotal['CBudget'] = toMoney(array_sum($subLeaseTotal['CBudget']));
                $subTotal['YBudget'] = toMoney(array_sum($subLeaseTotal['YBudget']));
                $subTotal['FBudget'] = toMoney(array_sum($subLeaseTotal['FBudget']));
                $subTotal['CActual'] = toMoney(array_sum($subLeaseTotal['CActual']));
                $subTotal['YActual'] = toMoney(array_sum($subLeaseTotal['YActual']));
                $subTotal['FActual'] = toMoney(array_sum($subLeaseTotal['FActual']));

                $subTotal['CVariance$'] = toDecimal(array_sum($subLeaseTotal['CVariance$']));
                $subTotal['CVariance%'] = toDecimal($varianceCurrentP);
                $subTotal['YVariance$'] = toDecimal(array_sum($subLeaseTotal['YVariance$']));
                $subTotal['YVariance%'] = toDecimal($varianceYTDP);
                $subTotal['FVariance$'] = toDecimal(array_sum($subLeaseTotal['FVariance$']));
                $subTotal['FVariance%'] = toDecimal($varianceFullP);

                $reportDetail->renderSubTotal($subTotal);

                $reportDetail->renderLine([]);
            }
        }

        $varianceCurrentP = variancePercentageReturnZero(array_sum($subGroupTotal['CVariance$']), array_sum($subGroupTotal['CBudget']));
        $varianceYTDP = variancePercentageReturnZero(array_sum($subGroupTotal['YVariance$']), array_sum($subGroupTotal['YBudget']));
        $varianceFullP = variancePercentageReturnZero(array_sum($subGroupTotal['FVariance$']), array_sum($subGroupTotal['FBudget']));

        $subGroupTotal['Description'] = 'Total ' . ($groupName !== 'Owner Expenses' ? $groupName : 'Owner Expenditure');
        $subGroupTotal['CBudget'] = toMoney(array_sum($subGroupTotal['CBudget']));
        $subGroupTotal['YBudget'] = toMoney(array_sum($subGroupTotal['YBudget']));
        $subGroupTotal['FBudget'] = toMoney(array_sum($subGroupTotal['FBudget']));
        $subGroupTotal['CActual'] = toMoney(array_sum($subGroupTotal['CActual']));
        $subGroupTotal['YActual'] = toMoney(array_sum($subGroupTotal['YActual']));
        $subGroupTotal['FActual'] = toMoney(array_sum($subGroupTotal['FActual']));

        $subGroupTotal['CVariance$'] = toDecimal(array_sum($subGroupTotal['CVariance$']));
        $subGroupTotal['CVariance%'] = toDecimal($varianceCurrentP);
        $subGroupTotal['YVariance$'] = toDecimal(array_sum($subGroupTotal['YVariance$']));
        $subGroupTotal['YVariance%'] = toDecimal($varianceYTDP);
        $subGroupTotal['FVariance$'] = toDecimal(array_sum($subGroupTotal['FVariance$']));
        $subGroupTotal['FVariance%'] = toDecimal($varianceFullP);
        $reportDetail->renderSubTotal($subGroupTotal);

        $reportDetail->renderLine([]);

        if ($groupName === 'Direct Recoverable Expenses') {

            $varianceCurrentPExp = variancePercentageReturnZero(array_sum($expenseTotal['CVariance$']), array_sum($expenseTotal['CBudget']));
            $varianceYTDPExp = variancePercentageReturnZero(array_sum($expenseTotal['YVariance$']), array_sum($expenseTotal['YBudget']));
            $varianceFullPExp = variancePercentageReturnZero(array_sum($expenseTotal['FVariance$']), array_sum($expenseTotal['FBudget']));

            $renderExpenseTotal['Description'] = 'Total Recoverable Expenditure';
            $renderExpenseTotal['CBudget'] = toMoney(array_sum($expenseTotal['CBudget']));
            $renderExpenseTotal['YBudget'] = toMoney(array_sum($expenseTotal['YBudget']));
            $renderExpenseTotal['FBudget'] = toMoney(array_sum($expenseTotal['FBudget']));
            $renderExpenseTotal['CActual'] = toMoney(array_sum($expenseTotal['CActual']));
            $renderExpenseTotal['YActual'] = toMoney(array_sum($expenseTotal['YActual']));
            $renderExpenseTotal['FActual'] = toMoney(array_sum($expenseTotal['FActual']));
            $renderExpenseTotal['CVariance$'] = toDecimal(array_sum($expenseTotal['CVariance$']));
            $renderExpenseTotal['CVariance%'] = toDecimal($varianceCurrentPExp);
            $renderExpenseTotal['YVariance$'] = toDecimal(array_sum($expenseTotal['YVariance$']));
            $renderExpenseTotal['YVariance%'] = toDecimal($varianceYTDPExp);
            $renderExpenseTotal['FVariance$'] = toDecimal(array_sum($expenseTotal['FVariance$']));
            $renderExpenseTotal['FVariance%'] = toDecimal($varianceFullPExp);
            $reportDetail->renderSubTotal($renderExpenseTotal);

            $reportDetail->renderLine([]);

            $varianceCNOI = (array_sum($incomeTotal['CActual']) - array_sum($expenseTotal['CActual'])) - (array_sum($incomeTotal['CBudget']) - array_sum($expenseTotal['CBudget']));
            $varianceYNOI = (array_sum($incomeTotal['YActual']) - array_sum($expenseTotal['YActual'])) - (array_sum($incomeTotal['YBudget']) - array_sum($expenseTotal['YBudget']));
            $varianceFNOI = (array_sum($incomeTotal['FActual']) - array_sum($expenseTotal['FActual'])) - (array_sum($incomeTotal['FBudget']) - array_sum($expenseTotal['FBudget']));


            $varianceCurrentP = variancePercentageReturnZero($varianceCNOI, array_sum($incomeTotal['CBudget']) - array_sum($expenseTotal['CBudget']));
            $varianceYTDP = variancePercentageReturnZero($varianceYNOI, array_sum($incomeTotal['YBudget']) - array_sum($expenseTotal['YBudget']));
            $varianceFullP = variancePercentageReturnZero($varianceFNOI, array_sum($incomeTotal['FBudget']) - array_sum($expenseTotal['FBudget']));

            $renderIncomeTotal['Description'] = 'Net Operating Income';
            $renderIncomeTotal['CBudget'] = toMoney(array_sum($incomeTotal['CBudget']) - array_sum($expenseTotal['CBudget']));
            $renderIncomeTotal['YBudget'] = toMoney(array_sum($incomeTotal['YBudget']) - array_sum($expenseTotal['YBudget']));
            $renderIncomeTotal['FBudget'] = toMoney(array_sum($incomeTotal['FBudget']) - array_sum($expenseTotal['FBudget']));
            $renderIncomeTotal['CActual'] = toMoney(array_sum($incomeTotal['CActual']) - array_sum($expenseTotal['CActual']));
            $renderIncomeTotal['YActual'] = toMoney(array_sum($incomeTotal['YActual']) - array_sum($expenseTotal['YActual']));
            $renderIncomeTotal['FActual'] = toMoney(array_sum($incomeTotal['FActual']) - array_sum($expenseTotal['FActual']));
            $renderIncomeTotal['CVariance$'] = toDecimal($varianceCNOI);
            $renderIncomeTotal['CVariance%'] = toDecimal($varianceCurrentP);
            $renderIncomeTotal['YVariance$'] = toDecimal($varianceYNOI);
            $renderIncomeTotal['YVariance%'] = toDecimal($varianceYTDP);
            $renderIncomeTotal['FVariance$'] = toDecimal($varianceFNOI);
            $renderIncomeTotal['FVariance%'] = toDecimal($varianceFullP);

            $reportDetail->renderSubTotal($renderIncomeTotal);

            $reportDetail->renderLine([]);

            $expenseNonRecoTotal = [];

        }

        if ($groupName === 'Capital Expenditures Item') {

            $varianceCurrentPExp = variancePercentageReturnZero(array_sum($expenseNonRecoTotal['CVariance$']), array_sum($expenseNonRecoTotal['CBudget']));
            $varianceYTDPExp = variancePercentageReturnZero(array_sum($expenseNonRecoTotal['YVariance$']), array_sum($expenseNonRecoTotal['YBudget']));
            $varianceFullPExp = variancePercentageReturnZero(array_sum($expenseNonRecoTotal['FVariance$']), array_sum($expenseNonRecoTotal['FBudget']));

            $renderexpenseNonRecoTotal['Description'] = 'Total Non Recoverable Expenses';
            $renderexpenseNonRecoTotal['CBudget'] = toMoney(array_sum($expenseNonRecoTotal['CBudget']));
            $renderexpenseNonRecoTotal['YBudget'] = toMoney(array_sum($expenseNonRecoTotal['YBudget']));
            $renderexpenseNonRecoTotal['FBudget'] = toMoney(array_sum($expenseNonRecoTotal['FBudget']));
            $renderexpenseNonRecoTotal['CActual'] = toMoney(array_sum($expenseNonRecoTotal['CActual']));
            $renderexpenseNonRecoTotal['YActual'] = toMoney(array_sum($expenseNonRecoTotal['YActual']));
            $renderexpenseNonRecoTotal['FActual'] = toMoney(array_sum($expenseNonRecoTotal['FActual']));
            $renderexpenseNonRecoTotal['CVariance$'] = toDecimal(array_sum($expenseNonRecoTotal['CVariance$']));
            $renderexpenseNonRecoTotal['CVariance%'] = toDecimal($varianceCurrentPExp);
            $renderexpenseNonRecoTotal['YVariance$'] = toDecimal(array_sum($expenseNonRecoTotal['YVariance$']));
            $renderexpenseNonRecoTotal['YVariance%'] = toDecimal($varianceYTDPExp);
            $renderexpenseNonRecoTotal['FVariance$'] = toDecimal(array_sum($expenseNonRecoTotal['FVariance$']));
            $renderexpenseNonRecoTotal['FVariance%'] = toDecimal($varianceFullPExp);
            $reportDetail->renderSubTotal($renderexpenseNonRecoTotal);

            $reportDetail->renderLine([]);
        }

    }
}

$varianceCurrentP = variancePercentageReturnZero(array_sum($expenseTotal['CVariance$']), array_sum($expenseTotal['CBudget']));
$varianceYTDP = variancePercentageReturnZero(array_sum($expenseTotal['YVariance$']), array_sum($expenseTotal['YBudget']));
$varianceFullP = variancePercentageReturnZero(array_sum($expenseTotal['FVariance$']), array_sum($expenseTotal['FBudget']));

$renderExpenseTotal['Description'] = 'Total Expenditure';
$renderExpenseTotal['CBudget'] = toMoney(array_sum($expenseTotal['CBudget']));
$renderExpenseTotal['YBudget'] = toMoney(array_sum($expenseTotal['YBudget']));
$renderExpenseTotal['FBudget'] = toMoney(array_sum($expenseTotal['FBudget']));
$renderExpenseTotal['CActual'] = toMoney(array_sum($expenseTotal['CActual']));
$renderExpenseTotal['YActual'] = toMoney(array_sum($expenseTotal['YActual']));
$renderExpenseTotal['FActual'] = toMoney(array_sum($expenseTotal['FActual']));
$renderExpenseTotal['CVariance$'] = toDecimal(array_sum($expenseTotal['CVariance$']));
$renderExpenseTotal['CVariance%'] = toDecimal($varianceCurrentP);
$renderExpenseTotal['YVariance$'] = toDecimal(array_sum($expenseTotal['YVariance$']));
$renderExpenseTotal['YVariance%'] = toDecimal($varianceYTDP);
$renderExpenseTotal['FVariance$'] = toDecimal(array_sum($expenseTotal['FVariance$']));
$renderExpenseTotal['FVariance%'] = toDecimal($varianceFullP);
$reportDetail->renderSubTotal($renderExpenseTotal);


$reportDetail->renderLine([]);


$netIncome['CBudget'] = array_sum($incomeTotal['CBudget']) - array_sum($expenseTotal['CBudget']);
$netIncome['YBudget'] = array_sum($incomeTotal['YBudget']) - array_sum($expenseTotal['YBudget']);
$netIncome['FBudget'] = array_sum($incomeTotal['FBudget']) - array_sum($expenseTotal['FBudget']);
$netIncome['CActual'] = array_sum($incomeTotal['CActual']) - array_sum($expenseTotal['CActual']);
$netIncome['YActual'] = array_sum($incomeTotal['YActual']) - array_sum($expenseTotal['YActual']);
$netIncome['FActual'] = array_sum($incomeTotal['FActual']) - array_sum($expenseTotal['FActual']);

$varianceCurrent = ($netIncome['CActual']) - ($netIncome['CBudget']);
$varianceYTD = ($netIncome['YActual']) - ($netIncome['YBudget']);
$varianceFull = ($netIncome['FActual']) - ($netIncome['FBudget']);
$varianceCurrentP = variancePercentageReturnZero($varianceCurrent, ($netIncome['CBudget']));
$varianceYTDP = variancePercentageReturnZero($varianceYTD, ($netIncome['YBudget']));
$varianceFullP = variancePercentageReturnZero($varianceFull, ($netIncome['FBudget']));

$renderNetTotal['Description'] = 'Net Income Excl. ' . $_SESSION['country_default']['tax_label'];
$renderNetTotal['CBudget'] = toMoney(($netIncome['CBudget']));
$renderNetTotal['YBudget'] = toMoney(($netIncome['YBudget']));
$renderNetTotal['FBudget'] = toMoney(($netIncome['FBudget']));
$renderNetTotal['CActual'] = toMoney(($netIncome['CActual']));
$renderNetTotal['YActual'] = toMoney(($netIncome['YActual']));
$renderNetTotal['FActual'] = toMoney(($netIncome['FActual']));
$renderNetTotal['CVariance$'] = toDecimal($varianceCurrent);
$renderNetTotal['CVariance%'] = toDecimal($varianceCurrentP);
$renderNetTotal['YVariance$'] = toDecimal($varianceYTD);
$renderNetTotal['YVariance%'] = toDecimal($varianceYTDP);
$renderNetTotal['FVariance$'] = toDecimal($varianceFull);
$renderNetTotal['FVariance%'] = toDecimal($varianceFullP);
$reportDetail->renderSubTotal($renderNetTotal);
$reportDetail->renderLine([]);
$reportDetail->renderLine([]);

$tax = gstAccount();
$incomeTaxCode = $tax['gstOutputTax'];
$expenseTaxCode = $tax['gstInputTax'];

$currentIncomeTax = dbGetTaxFromTrialBalance($propertyID, $year, $currentPeriod, $toPeriod, $incomeTaxCode, 'balanceCash');
$ytdIncomeTax = dbGetTaxFromTrialBalance($propertyID, $year, 1, $toPeriod, $incomeTaxCode, 'balanceCash');
$gst = $currentIncomeTax['amount'] * (-1);
$gstA = $ytdIncomeTax['amount'] * (-1);

$currentExpensesTax = dbGetTaxFromTrialBalance($propertyID, $year, $currentPeriod, $toPeriod, $expenseTaxCode, 'balanceCash');
$ytdExpensesTax = dbGetTaxFromTrialBalance($propertyID, $year, 1, $toPeriod, $expenseTaxCode, 'balanceCash');
$gstpaid = $currentExpensesTax['amount'] * (-1);
$gstpaidA = $ytdExpensesTax['amount'] * (-1);

$gstReceipts['Unit'] = '';
$gstReceipts['Description'] = $_SESSION['country_default']['tax_label'] . ' Receipts';
$gstReceipts['CBudget'] = '';
$gstReceipts['YBudget'] = '';
$gstReceipts['FBudget'] = '';
$gstReceipts['CActual'] = toMoney($gst);
$gstReceipts['YActual'] = toMoney($gstA);
$gstReceipts['FActual'] = '';
$gstReceipts['CVariance$'] = '';
$gstReceipts['CVariance%'] = '';
$gstReceipts['YVariance$'] = '';
$gstReceipts['YVariance%'] = '';
$gstReceipts['FVariance$'] = '';
$gstReceipts['FVariance%'] = '';
$reportDetail->renderLine($gstReceipts);

$gstpaid *= -1;
$gstpaidA *= -1;

$gstPay['Unit'] = '';
$gstPay['Description'] = $_SESSION['country_default']['tax_label'] . ' Payments';
$gstPay['CBudget'] = '';
$gstPay['YBudget'] = '';
$gstPay['FBudget'] = '';
$gstPay['CActual'] = toMoney($gstpaid);
$gstPay['YActual'] = toMoney($gstpaidA);
$gstPay['FActual'] = '';
$gstPay['CVariance$'] = '';
$gstPay['CVariance%'] = '';
$gstPay['YVariance$'] = '';
$gstPay['YVariance%'] = '';
$gstPay['FVariance$'] = '';
$gstPay['FVariance%'] = '';
$reportDetail->renderLine($gstPay);

$gstNet['Unit'] = '';
$gstNet['Description'] = 'Net ' . $_SESSION['country_default']['tax_label'];
$gstNet['CBudget'] = '';
$gstNet['YBudget'] = '';
$gstNet['FBudget'] = '';
$gstNet['CActual'] = toMoney($gst - $gstpaid);
$gstNet['YActual'] = toMoney($gstA - $gstpaidA);
$gstNet['FActual'] = '';
$gstNet['CVariance$'] = '';
$gstNet['CVariance%'] = '';
$gstNet['YVariance$'] = '';
$gstNet['YVariance%'] = '';
$gstNet['FVariance$'] = '';
$gstNet['FVariance%'] = '';
$reportDetail->renderLine($gstNet);

$renderIncGst['Unit'] = '';
$renderIncGst['Description'] = 'Net Income Incl. ' . $_SESSION['country_default']['tax_label'];
$renderIncGst['CBudget'] = '';
$renderIncGst['YBudget'] = '';
$renderIncGst['FBudget'] = '';
$renderIncGst['CActual'] = toMoney($netIncome['CActual'] + ($gst - $gstpaid));
$renderIncGst['YActual'] = toMoney($netIncome['YActual'] + ($gstA - $gstpaidA));
$renderIncGst['FActual'] = '';
$renderIncGst['CVariance$'] = '';
$renderIncGst['CVariance%'] = '';
$renderIncGst['YVariance$'] = '';
$renderIncGst['YVariance%'] = '';
$renderIncGst['FVariance$'] = '';
$renderIncGst['FVariance%'] = '';
$reportDetail->renderSubTotal($renderIncGst);

$reportDetail->clean();
$reportDetail->endPage();
$reportDetail->close();

// MERGE PDF FILE TO OWNER REPORT
$pdi = new ImportedPage($pdf, $filePath, 2);
$i = 1;
while ($pdi->loadPage($i)) {
    $page++;
    $pdi->preparePage();
    $pdi->render();
    $pdi->endPage();
    $i++;
}

$pdi->close();

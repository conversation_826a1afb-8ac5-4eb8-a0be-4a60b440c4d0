<?php

use PhpOffice\PhpSpreadsheet\Style\Alignment;

function budgetReportProcess(&$context)
{
    global $clientDirectory, $pathPrefix, $sess;

    if (! isPostback()) {
        $view = new MasterPage(userViews(), '/managementReports/budgetReportProcess.html');
        $view->setSection($context['module']);
    } else {
        $view = new UserControl(userViews(), '/managementReports/budgetReportProcess.html');
    }

    $view->bindAttributesFrom($_REQUEST);
    if (isset($view->items['from_api'])) {
        $view->bindAttributesFrom($context);
    }

    $properties = deserializeParameters($view->items['propertyID']);
    $propertyCount = count($properties);

    if ($context[IS_TASK]) {
        $view->bindAttributesFrom($context);
        extract($context);
    } else {
        $queue = new Queue(TASKTYPE_EXCEL_REPORT);
        if ($propertyCount > THRESHOLD_OWNERREPORT) {
            $queue->add(
                $_SESSION['clientID'],
                $_SESSION['un'],
                'module=' . $view->items['module'] . '&command=' . $view->items['command'],
                $view->items
            );
        }
    }

    if (($context[IS_TASK]) || ($propertyCount <= THRESHOLD_EXCELREPORT)) {
        // Path
        $year = $view->items['year'];
        $yearRange = ($year - 1) . '/' . substr($year, 2, 2);
        $toDate = '01/06/' . $view->items['year'];
        $format = $context['format'];
        if (count($properties) > 1) {
            $fileName = 'BudgetReport_Multiple_' . $year . '_' . date('dmYHis') . ".{$format}";
        } else {
            $fileName = 'BudgetReport_' . $properties[0] . '_' . $year . '_' . date('dmYHis') . ".{$format}";
        }

        $_filePath = $pathPrefix . $clientDirectory . "/{$format}/BudgetReport/";
        $_downloadPath = $clientDirectory . "/{$format}/BudgetReport/";
        checkDirPath($_filePath);
        $filePath = $_filePath . $fileName;
        if (file_exists($filePath)) {
            @unlink($filePath);
        }

        $downloadPath = $_downloadPath . $fileName;
        $logoFile = dbGetClientLogo();
        $logoPath = "assets/clientLogos/{$logoFile}";

        // Style
        $numberFormat = '_(* #,##0.00_);_(* (#,##0.00);_(* "-"??_);_(@_)';
        $numberFormatStyle =
        [
            'alignment' => ['horizontal' => 'right'],
            'numberformat' => ['code' => $numberFormat],
        ];
        $borderSubTotalStyle =
        [
            'borders' => [
                'top' => [
                    'style' => 'thin',
                    'color' => ['rgb' => 'a6a6a6'],
                ],
                'bottom' => [
                    'style' => 'thick',
                    'color' => ['rgb' => 'a6a6a6'],
                ],
            ],
        ];

        $xls = new XLSDataReport($filePath, 'Budget Report');
        $xls->enableFormatting = true;
        $xls->useFormula = true;
        switch ($view->items['colorScheme']) {
            case 'Medium Blue':
                $xls->totalFillColor = 'e8eaf1';
                $xls->fillColor = 'e8eaf1';
                $xls->baseColor = '406ec7';
                break;
            case 'Indigo':
                $xls->totalFillColor = 'eae7f0';
                $xls->fillColor = 'eae7f0';
                $xls->baseColor = '5a3ec1';
                break;
            case 'Magenta':
                $xls->totalFillColor = 'f1e8f1';
                $xls->fillColor = 'f1e8f1';
                $xls->baseColor = 'c241ca';
                break;
            case 'Pinkish Red':
                $xls->totalFillColor = 'f2e9ec';
                $xls->fillColor = 'f2e9ec';
                $xls->baseColor = 'e94b82';
                break;
            case 'Red-Orange':
                $xls->totalFillColor = 'f3ecea';
                $xls->fillColor = 'f3ecea';
                $xls->baseColor = 'fe6f52';
                break;
            case 'Orange':
                $xls->totalFillColor = 'f3efea';
                $xls->fillColor = 'f3efea';
                $xls->baseColor = 'fea252';
                break;
            case 'Yellow-Orange':
                $xls->fillColor = 'fec452';
                $xls->baseColor = '000000';
                $xls->totalFillColor = 'ffe7b8';
                break;
            case 'Yellow':
                $xls->totalFillColor = 'feea52';
                $xls->fillColor = 'feea52';
                $xls->baseColor = '000000';
                break;
            case 'Greenish-Yellow':
                $xls->totalFillColor = 'f1f2e9';
                $xls->fillColor = 'f1f2e9';
                $xls->baseColor = 'd8ee4c';
                break;
            case 'Yellow-Green':
                $xls->totalFillColor = 'ebf1e8';
                $xls->fillColor = 'ebf1e8';
                $xls->baseColor = '7fd143';
                break;
            case 'Blue-Green':
                $xls->totalFillColor = 'e8f1ed';
                $xls->fillColor = 'e8f1ed';
                $xls->baseColor = '42cd85';
                break;
            case 'Navy Blue':
                $xls->fillColor = '366092';
                $xls->baseColor = 'ffffff';
                $xls->totalFillColor = 'c0cfe3';
                break;
            case 'Bluish Cyan':
            default:
                $xls->totalFillColor = 'e8eef1';
                $xls->fillColor = 'e8eef1';
                $xls->baseColor = '43a5d1';
                break;
        }

        $xls->changeColorScheme($xls->baseColor, $xls->fillColor, $xls->totalFillColor);
        $styleHeaderLeftAlign = array_merge(
            $xls->styleHeader,
            [
                'alignment' => [
                    'vertical' => Alignment::VERTICAL_CENTER,
                    'horizontal' => Alignment::HORIZONTAL_LEFT,
                ],
            ]
        );
        $styleHeaderCenterAlign = array_merge(
            $xls->styleHeader,
            [
                'alignment' => [
                    'vertical' => Alignment::VERTICAL_CENTER,
                    'horizontal' => Alignment::HORIZONTAL_CENTER,
                ],
            ]
        );

        $useFormula = $view->items['useFormula'] != 'No';
        $sheetIndex = 0;

        foreach ($properties as $propertyID) {
            $periodFormula = [];
            $periodTotals = [];
            $dataSet = [];
            $expvo = [];
            $expenseAccounts = [];
            $count = 0;
            $countIncome = 0;
            $countExpense = 0;
            $periods = dbGetCalendarPeriods($propertyID, $year);


            if ($view->items['worksheet1'] == 'Yes') {
                $incomeAccounts = dbBudgetIncomeAccountsWitoutComment($propertyID, $year);
            } else {
                $incomeAccounts = dbBudgetIncomeAccounts($propertyID, $year);
            }

            $countIncome = count($incomeAccounts);

            $ag = dbGetAccountGroups('MAJOR', EXPENDITURE);
            $expenditureAccountGroups = mapParameters($ag, 'accountGroup', 'groupDescription');
            foreach ($expenditureAccountGroups as $code => $description) {
                $expAcc = dbBudgetExpensesAccounts($propertyID, $year, $code);
                if ($view->items['worksheet1'] == 'Yes') {
                    $expAcc = dbBudgetExpensesAccountsWithoutComment($propertyID, $year, $code);
                } else {
                    $expAcc = dbBudgetExpensesAccounts($propertyID, $year, $code);
                }


                $countExpense += count($expAcc);
                if ($expAcc) {
                    $expenseAccounts[$code] = $expAcc;
                }
            }

            if ($countIncome || $countExpense) {
                $propertyLookUp = dbGetPropertyLookup($propertyID);
                $propertyName = $propertyLookUp['propertyName'];
                // Expenses Budget
                if ($view->items['worksheet0'] == 'Yes') {
                    $reportType = $view->items['reportType'];
                    $sheetName = 'Expenditure Budget';

                    $xls->setSheetDetails($sheetIndex, $sheetName, $sheetIndex !== 0);
                    $xls->printer();
                    $xls->line = 1;
                    $xls->columns = [];
                    $dataSet = [];

                    $xls->addColumn('colHeader1', '', 30, 'left', '@', $styleHeaderLeftAlign);
                    $xls->addColumn('colHeader2', '', 30, 'left', '@', $styleHeaderLeftAlign);
                    $xls->addColumn('colHeader3', '', 30, 'right', '@', $styleHeaderLeftAlign);
                    $xls->addColumn('colHeader4', '', 30, 'right', '@', $styleHeaderLeftAlign);
                    $xls->addColumn(
                        'colHeader5',
                        ((int) $year - 1) . ' vs ' . $year . "\nBudget",
                        30,
                        'right',
                        '@',
                        $styleHeaderCenterAlign
                    );
                    $xls->addColumn(
                        'colHeader6',
                        ((int) $year - 1) . ' vs ' . $year . "\nBudget",
                        30,
                        'right',
                        '@',
                        $styleHeaderCenterAlign
                    );
                    $xls->addColumn(
                        'colHeader7',
                        $year . " budget vs \n" . ((int) $year - 1) . ' actual',
                        30,
                        'right',
                        '@',
                        $styleHeaderCenterAlign
                    );
                    $xls->addColumn(
                        'colHeader8',
                        $year . " budget vs \n" . ((int) $year - 1) . ' actual',
                        30,
                        'right',
                        '@',
                        $styleHeaderCenterAlign
                    );
                    $xls->addColumn('colHeader9', '', 23, 'right', $numberFormat, $styleHeaderCenterAlign);
                    $xls->addColumn('colHeader10', '', 23, 'right', $numberFormat, $styleHeaderCenterAlign);
                    $xls->addColumn('colHeader11', '', 30, 'left', $numberFormat, $styleHeaderCenterAlign);

                    $header = "Property: {$propertyName} ({$propertyID})  Year: {$yearRange}\n";
                    $xls->renderHeaderDetails($header);
                    $xls->renderHeader();
                    $xls->columns = [];
                    $dataSet = [];
                    $xls->addColumn('accountCode', 'Account Code', 30, 'left', '@', $styleHeaderLeftAlign);
                    $xls->addColumn('accountName', 'Account Name', 30, 'left', '@', $styleHeaderLeftAlign);
                    $xls->addColumn(
                        'lastYearBudget',
                        'Last Year Budget',
                        30,
                        'right',
                        $numberFormat,
                        $styleHeaderLeftAlign
                    );
                    $xls->addColumn(
                        'actualBudget',
                        'Actual (Estimate)',
                        30,
                        'right',
                        $numberFormat,
                        $styleHeaderLeftAlign
                    );
                    $xls->addColumn(
                        'varLastYearVSCurrentYearVal',
                        'Variance ' . $_SESSION['country_default']['currency_symbol'],
                        30,
                        'right',
                        $numberFormat,
                        $styleHeaderCenterAlign
                    );
                    $xls->addColumn(
                        'varLastYearVSCurrentYearPer',
                        'Variance %',
                        30,
                        'right',
                        $numberFormat,
                        $styleHeaderCenterAlign
                    );
                    $xls->addColumn(
                        'varCurrentYearVSLastYearActualVal',
                        'Variance ' . $_SESSION['country_default']['currency_symbol'],
                        30,
                        'right',
                        $numberFormat,
                        $styleHeaderCenterAlign
                    );
                    $xls->addColumn(
                        'varCurrentYearVSLastYearActualPer',
                        'Variance %',
                        30,
                        'right',
                        $numberFormat,
                        $styleHeaderCenterAlign
                    );
                    $xls->addColumn('currentBudget', 'Budget', 23, 'right', $numberFormat, $styleHeaderCenterAlign);
                    $xls->addColumn(
                        'ratePerM2',
                        'Rate per ' . html_entity_decode($_SESSION['country_default']['area_unit'], ENT_COMPAT),
                        23,
                        'right',
                        $numberFormat,
                        $styleHeaderCenterAlign
                    );
                    $xls->addColumn('comment', 'Comment', 30, 'wrap', '@', $styleHeaderCenterAlign);
                    $xls->setColumnHeight(4, 'auto');
                    $xls->setColumnWidth('E', 20);
                    $xls->setColumnWidth('F', 20);
                    $xls->setColumnWidth('G', 20);
                    $xls->setColumnWidth('H', 20);
                    $xls->setCellTextWrap('E4');
                    $xls->setCellTextWrap('F4');
                    $xls->setCellTextWrap('G4');
                    $xls->setCellTextWrap('H4');
                    $xls->addSubHeaderItem('title', 1, 40, null);
                    //                    $header = "Property: $propertyName ($propertyID) \nLease: $leaseName ($leaseCode) \nUnit: $unitName ($unitCode) \nArea: $unitArea SQM \nYear: $yearRange \nBasis: " . $view->items['reportType'];
                    $xls->renderHeader();
                    $xls->freezePanes(0, $xls->line);

                    $total = 0;
                    $overAllTotalLastYear = 0;
                    $overAllTotalActual = 0;
                    $overAllTotalCurrent = 0;
                    $lettableAreaBudget = (float) dbGetLettableAreaBudget($propertyID, $toDate);
                    $leaseVOData = dbGetExpedentureBudget($propertyID, $year, (int) $year - 1, $reportType);
                    $countLeaseEXPVO = count($leaseVOData);
                    $ratePerM2Total = 0;
                    $OVERALLTOTALSqm = 0;
                    $row_count = 5;
                    foreach ($leaseVOData as $dataLeaseVal) {
                        $row_count++;
                        $row = [];
                        $TOTALSqm = $dataLeaseVal['unit_area'];
                        if ($OVERALLTOTALSqm == 0) {
                            $OVERALLTOTALSqm = $dataLeaseVal['unit_area_total'];
                        }

                        if ($TOTALSqm == 0) {
                            $TOTALSqm = $lettableAreaBudget;
                        }

                        $row['accountCode'] = $dataLeaseVal['account_code'];
                        $row['accountName'] = $dataLeaseVal['account_name'];
                        $row['actualBudget'] = (round($dataLeaseVal['last_year_actual'], 2));
                        if ($reportType == '1') {
                            $row['lastYearBudget'] = (round($dataLeaseVal['year_budget_cash_value_last_year'], 2));
                            $row['currentBudget'] = (round($dataLeaseVal['year_budget_cash_value'], 2));
                        } elseif ($reportType == '3') {
                            $row['lastYearBudget'] = (round($dataLeaseVal['year_budget_accruals_value_last_year'], 2));
                            $row['currentBudget'] = (round($dataLeaseVal['year_budget_accruals_value'], 2));
                        } elseif ($reportType == '2') {
                            $row['lastYearBudget'] = (round(
                                $dataLeaseVal['year_budget_cash_forecast_value_last_year'],
                                2
                            ));
                            $row['currentBudget'] = (round($dataLeaseVal['year_budget_cash_forecast_value'], 2));
                        } elseif ($reportType == '4') {
                            $row['lastYearBudget'] = (round(
                                $dataLeaseVal['year_budget_accruals_forecast_value_last_year'],
                                2
                            ));
                            $row['currentBudget'] = (round($dataLeaseVal['year_budget_accruals_forecast_value'], 2));
                        }

                        if ($useFormula) {
                            $last_year_budget_cell = 'C' . $row_count;
                            $actual_budget_cell = 'D' . $row_count;
                            $variance_amount_budget_cell = 'E' . $row_count;
                            $variance_per_budget_cell = 'F' . $row_count;
                            $variance_actual_amount_budget_cell = 'G' . $row_count;
                            $variance_actual_per_budget_cell = 'H' . $row_count;
                            $current_budget_cell = 'I' . $row_count;
                            $rate_per_m2_budget_cell = 'J' . $row_count;

                            $varLastYearVSCurrentYearVal = $current_budget_cell . '-' . $last_year_budget_cell;
                            if ($row['lastYearBudget'] <= 0) {
                                $varLastYearVSCurrentYearPer = 0;
                            } else {
                                $varLastYearVSCurrentYearPer = '((' . $varLastYearVSCurrentYearVal . ')/' . $last_year_budget_cell . ')*100';
                            }

                            $row['varLastYearVSCurrentYearVal'] = '=' . $varLastYearVSCurrentYearVal;
                            $row['varLastYearVSCurrentYearPer'] = '=' . $varLastYearVSCurrentYearPer;

                            $varCurrentYearVSLastYearActualVal = $current_budget_cell . '-' . $actual_budget_cell;
                            if ($row['actualBudget'] <= 0) {
                                $varCurrentYearVSLastYearActualPer = 0;
                            } else {
                                $varCurrentYearVSLastYearActualPer = '((' . $varCurrentYearVSLastYearActualVal . ')/(' . $actual_budget_cell . ')) * 100';
                            }

                            $row['varCurrentYearVSLastYearActualVal'] = '=' . $varCurrentYearVSLastYearActualVal;
                            $row['varCurrentYearVSLastYearActualPer'] = '=' . $varCurrentYearVSLastYearActualPer;
                            $ratePerM2 = 0;
                            if ($TOTALSqm > 0) {
                                $ratePerM2 = '=(' . $current_budget_cell . '/' . $TOTALSqm . ')';
                                $ratePerM2Total = $ratePerM2;
                            }
                        } else {
                            $varLastYearVSCurrentYearVal = $row['currentBudget'] - $row['lastYearBudget'];
                            if ($row['lastYearBudget'] <= 0) {
                                $varLastYearVSCurrentYearPer = 0;
                            } else {
                                $varLastYearVSCurrentYearPer = (round(
                                    ($varLastYearVSCurrentYearVal / $row['lastYearBudget']) * 100,
                                    2
                                ));
                            }

                            $row['varLastYearVSCurrentYearVal'] = (round($varLastYearVSCurrentYearVal, 2));
                            $row['varLastYearVSCurrentYearPer'] = (round($varLastYearVSCurrentYearPer, 2));

                            $varCurrentYearVSLastYearActualVal = $row['currentBudget'] - $row['actualBudget'];
                            if ($row['actualBudget'] <= 0) {
                                $varCurrentYearVSLastYearActualPer = 0;
                            } else {
                                $varCurrentYearVSLastYearActualPer = ($varCurrentYearVSLastYearActualVal / $row['actualBudget']) * 100;
                            }

                            $row['varCurrentYearVSLastYearActualVal'] = (round($varCurrentYearVSLastYearActualVal, 2));
                            $row['varCurrentYearVSLastYearActualPer'] = (round($varCurrentYearVSLastYearActualPer, 2));
                            $ratePerM2 = 0;
                            if ($TOTALSqm > 0) {
                                $ratePerM2 = round($row['currentBudget'] / (float) $TOTALSqm, 2);
                                $ratePerM2Total += $ratePerM2;
                            }
                        }

                        $row['ratePerM2'] = $ratePerM2;
                        $row['comment'] = $dataLeaseVal['comment'];

                        $overAllTotalLastYear += $row['lastYearBudget'];
                        $overAllTotalActual += $row['actualBudget'];
                        $overAllTotalCurrent += $row['currentBudget'];
                        $dataSet[] = $row;
                    }

                    $xls->setName(
                        $propertyID . 'EXPVOOverAll',
                        cellReference($xls->line, 3) . ':' . cellReference(($xls->line + $countLeaseEXPVO - 1), 3)
                    );
                    if ($countLeaseEXPVO > 0) {
                        if ($useFormula) {
                            $expensesTotal['lastYearBudget'] = '=SUM(' . cellReference(
                                $xls->line,
                                3
                            ) . ':' . cellReference(($xls->line + $countLeaseEXPVO - 1), 3) . ')';
                            $expensesTotal['actualBudget'] = '=SUM(' . cellReference(
                                $xls->line,
                                4
                            ) . ':' . cellReference(($xls->line + $countLeaseEXPVO - 1), 4) . ')';
                            $expensesTotal['varLastYearVSCurrentYearVal'] = '=SUM(' . cellReference(
                                $xls->line,
                                5
                            ) . ':' . cellReference(($xls->line + $countLeaseEXPVO - 1), 5) . ')';
                            if ($overAllTotalLastYear > 0) {
                                $expensesTotal['varLastYearVSCurrentYearPer'] = '=(SUM(' . cellReference(
                                    $xls->line,
                                    5
                                ) . ':' . cellReference(
                                    ($xls->line + $countLeaseEXPVO - 1),
                                    5
                                ) . ')/' . 'SUM(' . cellReference($xls->line, 3) . ':' . cellReference(
                                    ($xls->line + $countLeaseEXPVO - 1),
                                    3
                                ) . '))*100';
                            } else {
                                $expensesTotal['varLastYearVSCurrentYearPer'] = '0';
                            }

                            $expensesTotal['varCurrentYearVSLastYearActualVal'] = '=SUM(' . cellReference(
                                $xls->line,
                                7
                            ) . ':' . cellReference(($xls->line + $countLeaseEXPVO - 1), 7) . ')';
                            if ($overAllTotalActual > 0) {
                                $expensesTotal['varCurrentYearVSLastYearActualPer'] = '=(SUM(' . cellReference(
                                    $xls->line,
                                    7
                                ) . ':' . cellReference(
                                    ($xls->line + $countLeaseEXPVO - 1),
                                    7
                                ) . ')/' . 'SUM(' . cellReference($xls->line, 4) . ':' . cellReference(
                                    ($xls->line + $countLeaseEXPVO - 1),
                                    4
                                ) . '))*100';
                            } else {
                                $expensesTotal['varCurrentYearVSLastYearActualPer'] = '0';
                            }

                            $expensesTotal['currentBudget'] = '=SUM(' . cellReference(
                                $xls->line,
                                9
                            ) . ':' . cellReference(($xls->line + $countLeaseEXPVO - 1), 9) . ')';
                            $expensesTotal['ratePerM2'] = '=SUM(' . cellReference($xls->line, 10) . ':' . cellReference(
                                ($xls->line + $countLeaseEXPVO - 1),
                                10
                            ) . ')';
                        } else {
                            $expensesTotal['lastYearBudget'] = $overAllTotalLastYear;
                            $expensesTotal['actualBudget'] = $overAllTotalActual;
                            $expensesTotal['varLastYearVSCurrentYearVal'] = (float) $overAllTotalCurrent - (float) $overAllTotalLastYear;
                            if ($overAllTotalLastYear > 0) {
                                $expensesTotal['varLastYearVSCurrentYearPer'] = (((float) $overAllTotalCurrent - $overAllTotalLastYear) / $overAllTotalLastYear) * 100;
                            } else {
                                $expensesTotal['varLastYearVSCurrentYearPer'] = '0';
                            }

                            $expensesTotal['varCurrentYearVSLastYearActualVal'] = (float) $overAllTotalCurrent - (float) $overAllTotalActual;
                            if ($overAllTotalActual > 0) {
                                $expensesTotal['varCurrentYearVSLastYearActualPer'] = (((float) $overAllTotalCurrent - $overAllTotalActual) / $overAllTotalActual) * 100;
                            } else {
                                $expensesTotal['varCurrentYearVSLastYearActualPer'] = '0';
                            }

                            $expensesTotal['currentBudget'] = $overAllTotalCurrent;
                            $expensesTotal['ratePerM2'] = $ratePerM2Total;
                        }
                    } else {
                        $expensesTotal['lastYearBudget'] = '0.00';
                        $expensesTotal['actualBudget'] = '0.00';
                        $expensesTotal['varLastYearVSCurrentYearVal'] = '0.00';
                        $expensesTotal['varLastYearVSCurrentYearPer'] = '0.00';
                        $expensesTotal['varCurrentYearVSLastYearActualVal'] = '0.00';
                        $expensesTotal['varCurrentYearVSLastYearActualPer'] = '0.00';
                        $expensesTotal['currentBudget'] = '0.00';
                        $expensesTotal['ratePerM2'] = '0.00';
                    }


                    $xls->renderData($dataSet);
                    $xls->renderTotal($expensesTotal, 'Total');
                    //                    $xls->renderData ($dataSet);

                    //                    $xls->renderTotal ($propertyTotal,"Sub Total");

                    $sheetIndex++;
                }

                $xls->columns = [];
                $dataSet = [];
                if ($view->items['worksheet1'] == 'Yes') {
                    $xls->setSheetDetails(
                        $sheetIndex,
                        $propertyID . ' monthly budget',
                        $sheetIndex !== 0
                    );
                    $xls->printer();
                    $xls->line = 1;
                    $xls->columns = [];
                    $periodIncomeTotal = [];
                    $periodExpenseTotal = [];
                }


                foreach ($periods as $v) {
                    $periodMonths[$v['calendarPeriod']] = date('F', toTimestamp($v['calendarEndDate']));
                }

                $periodCount = count($periodMonths);

                if ($view->items['worksheet1'] == 'Yes') {
                    $xls->addColumn('accountID', 'Account', 10, 'left', '@', $styleHeaderLeftAlign);
                    $xls->addColumn('accountName', 'Account Name', 30, 'left', '@', $styleHeaderLeftAlign);
                    foreach ($periodMonths as $x => $y) {
                        $xls->addColumn("period{$x}", $y, 23, 'right', $numberFormat);
                    }

                    $xls->addColumn('total', 'Total', 15, 'right', $numberFormat);
                    $xls->addColumn('comment', 'Comments', 15, 'left', '@', $styleHeaderLeftAlign);
                    $xls->addColumn('user', 'User', 15, 'left', '@', $styleHeaderLeftAlign);
                    $xls->addColumn('dateEntered', 'Date Entered', 15, 'left', '@', $styleHeaderLeftAlign);
                    $xls->addSubHeaderItem('title', 1, 40, null);
                }

                $header = "Property: {$propertyName} ({$propertyID})  Year: {$yearRange}\n";
                if ($view->items['worksheet1'] == 'Yes') {
                    $xls->renderHeaderDetails($header);
                    $xls->renderHeader();
                    $xls->freezePanes(0, $xls->line);
                }

                // ## Income ###
                if ($view->items['worksheet1'] == 'Yes') {
                    $xls->line++;
                    $xls->renderHeaderDetails('Income');
                }

                if ($countIncome !== 0) {
                    $periodTotals = [];
                    foreach ($incomeAccounts as $a) {
                        $row = [];
                        //						var_dump($view->items['reportType']);
                        $ia = dbBudgetIncomeAmounts($propertyID, $year, $a['accountID'], $view->items['reportType']);
                        $row['accountID'] = $a['accountID'];
                        $row['accountName'] = $a['accountName'];
                        $row['accountType'] = 'Income';
                        if ($view->items['worksheet1'] == 'Yes') {
                            $getComment = dbBudgetIncomeAccountsGetComment($propertyID, $year, $a['accountID'], 'MISC');
                            $comment = '';
                            foreach ($getComment as $aCom) {
                                $comment = $comment . $aCom['comment'] . ' ';
                            }

                            $row['comment'] = $comment;
                        } else {
                            $row['comment'] = $a['comment'];
                        }

                        $userEmail = dbGetUserByID($a['commentUser']);
                        $row['user'] = ($userEmail) ? $userEmail['email'] : '';
                        $row['dateEntered'] = $a['commentDate'];
                        if ($view->items['worksheet1'] == 'Yes') {
                            $xls->setName(
                                $propertyID . 'IncomeAccount' . $a['accountID'],
                                'C' . ($xls->line + $count) . ':N' . ($xls->line + $count)
                            );
                        }

                        $total = 0;

                        foreach ($ia as $i) {
                            if (! $useFormula) {
                                $periodTotals['period' . $i['period']] += $i['amount'];
                            }

                            //                            var_dump($periodTotals['period' . $i['period']]);
                            $row['period' . $i['period']] += $i['amount'];
                            $total += $i['amount'] * 1;
                        }

                        if (! $useFormula) {
                            $row['total'] = $total;
                            $periodTotals['total'] += $total;
                        } else {
                            // $row['total'] = '=SUM(' . $propertyID . 'IncomeAccount' . $a['accountID'] . ')';
                            $row['total'] = '=SUM(C' . ($xls->line + $count) . ':N' . ($xls->line + $count) . ')';
                        }

                        $dataSet[] = $row;
                        $count++;
                    }

                    if ($useFormula) {
                        foreach ($periodMonths as $x => $y) {
                            if ($view->items['worksheet1'] == 'Yes') {
                                $xls->setName(
                                    $propertyID . "OwnerIncome{$y}",
                                    cellReference($xls->line + 1, $x + 2) . ':' . cellReference(
                                        $xls->line + $countIncome,
                                        $x + 2
                                    )
                                );
                            }

                            // $periodTotals["period$x"] = "=SUM(" . $propertyID . "OwnerIncome$y)";
                            $periodTotals["period{$x}"] = '=SUM(' . cellReference(
                                $xls->line,
                                $x + 2
                            ) . ':' . cellReference($xls->line + $countIncome - 1, $x + 2) . ')';
                            // $periodFormula["period$x"] = $propertyID . "OwnerIncome$y";
                            $periodFormula["period{$x}"] = cellReference($xls->line, $x + 2) . ':' . cellReference(
                                $xls->line + $countIncome - 1,
                                $x + 2
                            );
                        }

                        // $periodTotals['total'] = '=SUM(' . $propertyID . 'OwnerIncomeTotal)';
                        $periodTotals['total'] = '=SUM(C' . ($xls->line + $countIncome) . ':N' . ($xls->line + $countIncome) . ')';
                        // $periodFormula['total'] = $propertyID . 'OwnerIncomeTotal';
                        $periodFormula['total'] = 'C' . ($xls->line + $countIncome) . ':N' . ($xls->line + $countIncome);
                    }
                } else {
                    foreach ($periodMonths as $x => $y) {
                        if ($view->items['worksheet1'] == 'Yes') {
                            $xls->setName(
                                $propertyID . "OwnerIncome{$y}",
                                cellReference($xls->line + 1, $x + 2) . ':' . cellReference(
                                    $xls->line,
                                    $x + 2
                                )
                            );
                        }

                        $periodTotals["period{$x}"] = 0.00;
                        $periodFormula["period{$x}"] = 0.00;
                    }

                    $periodTotals['total'] = 0.00;
                    $periodFormula['total'] = 0.00;
                }

                if ($view->items['worksheet1'] == 'Yes') {
                    $xls->setName(
                        $propertyID . 'OwnerIncomeTotal',
                        'C' . ($xls->line + $countIncome) . ':N' . ($xls->line + $countIncome)
                    );
                    if ($dataSet !== []) {
                        $xls->renderData($dataSet);
                    }

                    $xls->renderTotal($periodTotals);
                    $sheetIndex++;
                }

                $periodIncomeTotal = ($useFormula) ? $periodFormula : $periodTotals;

                // ## Expense ###
                foreach ($expenditureAccountGroups as $code => $description) {
                    if ($expenseAccounts[$code]) {
                        $countExpense = count($expenseAccounts[$code]);
                        if ($view->items['worksheet1'] == 'Yes') {
                            $xls->renderHeaderDetails("\n{$description}");
                        }

                        $dataSet = [];
                        $periodTotals = [];
                        $periodFormula = [];
                        $count = 0;
                        foreach ($expenseAccounts[$code] as $a) {
                            $row = [];
                            $ia = dbBudgetExpensesAmounts(
                                $propertyID,
                                $year,
                                $a['accountID'],
                                $view->items['reportType']
                            );
                            $row['accountID'] = $a['accountID'];
                            $accountIDs[$a['accountID']] = $row['accountName'] = $a['accountName'];
                            $row['accountType'] = 'Expenses';

                            if ($view->items['worksheet1'] == 'Yes') {
                                $getComment = dbBudgetExpensesAccountsGetComment($propertyID, $year, $a['accountID']);
                                $comment = '';
                                $commentUser = '';
                                $commentDate = '';
                                foreach ($getComment as $aCom) {
                                    $comment = $comment . $aCom['comment'] . ' ';
                                    $commentDate = $aCom['commentDate'] . ' ';
                                    $userEmail = dbGetUserByID($aCom['commentUser']);
                                    $accountIDCommentUser[$a['accountID']] = $userEmail ? $row['user'] = $userEmail['email'] : $row['user'] = '';
                                }

                                $accountIDComments[$a['accountID']] = $row['comment'] = $comment;
                                $accountIDCommentDate[$a['accountID']] = $row['dateEntered'] = $commentDate;
                            } else {
                                $accountIDComments[$a['accountID']] = $row['comment'] = $a['comment'];
                                $userEmail = dbGetUserByID($a['commentUser']);
                                $accountIDCommentUser[$a['accountID']] = $userEmail ? $row['user'] = $userEmail['email'] : $row['user'] = '';

                                $accountIDCommentDate[$a['accountID']] = $row['dateEntered'] = $a['commentDate'];
                            }


                            $total = 0;
                            if ($view->items['worksheet1'] == 'Yes') {
                                $xls->setName(
                                    $propertyID . $code . $a['accountID'],
                                    'C' . ($xls->line + $count) . ':N' . ($xls->line + $count)
                                );
                            }

                            foreach ($ia as $i) {
                                if (! $useFormula) {
                                    $periodTotals['period' . $i['period']] += $i['amount'];
                                }

                                $row['period' . $i['period']] = $i['amount'];
                                $total += $i['amount'] * 1;
                            }

                            if (! $useFormula) {
                                $row['total'] = $total;
                                $periodTotals['total'] += $total;
                            } else {
                                // $row['total'] = "=SUM({$propertyID}$code".$a['accountID'].')';
                                $row['total'] = '=SUM(C' . ($xls->line + $count) . ':N' . ($xls->line + $count) . ')';
                            }

                            $dataSet[] = $row;

                            // if ($code == 'EXPVO') $expvo[$a['accountID']][$propertyID] = $total;
                            if ($code == 'EXPVO') {
                                $expvo[$a['accountID']] = $total;
                            }

                            $count++;
                        }

                        if ($useFormula) {
                            foreach ($periodMonths as $x => $y) {
                                if ($view->items['worksheet1'] == 'Yes') {
                                    $xls->setName(
                                        $propertyID . $code . 'Total' . $y,
                                        cellReference($xls->line, $x + 2) . ':' . cellReference(
                                            $xls->line + $countExpense - 1,
                                            $x + 2
                                        )
                                    );
                                }

                                // $periodTotals["period$x"] = "=SUM({$propertyID}{$code}Total$y)";
                                $periodTotals["period{$x}"] = '=SUM(' . cellReference(
                                    $xls->line,
                                    $x + 2
                                ) . ':' . cellReference($xls->line + $countExpense - 1, $x + 2) . ')';
                                // $periodFormula["period$x"] = $propertyID . $code . "Total$y";
                                $periodFormula["period{$x}"] = cellReference($xls->line, $x + 2) . ':' . cellReference(
                                    $xls->line + $countExpense - 1,
                                    $x + 2
                                );
                            }

                            // $periodTotals['total'] = "=SUM({$propertyID}{$code}Total)";
                            $periodTotals['total'] = '=SUM(' . cellReference(
                                $xls->line + $countExpense,
                                3
                            ) . ':' . cellReference($xls->line + $countExpense, (2 + $periodCount)) . ')';
                            // $periodFormula['total'] = $propertyID . $code . 'Total';
                            $periodFormula['total'] = cellReference(
                                $xls->line + $countExpense,
                                3
                            ) . ':' . cellReference($xls->line + $countExpense, (2 + $periodCount));
                        }

                        if ($view->items['worksheet1'] == 'Yes') {
                            $xls->setName(
                                $propertyID . $code . 'Total',
                                cellReference($xls->line + $countExpense, 3) . ':' . cellReference(
                                    $xls->line + $countExpense,
                                    (2 + $periodCount)
                                )
                            );

                            $xls->renderData($dataSet);
                            $xls->renderTotal($periodTotals);
                        }

                        $periodExpenseTotal[] = ($useFormula) ? $periodFormula : $periodTotals;
                    }
                }

                if ($view->items['worksheet1'] == 'Yes') {
                    $xls->line++;
                }

                $periodTotals = [];
                $periodExpenseOverallTotal = [];

                if ($periodIncomeTotal !== []) {
                    if ($useFormula) {
                        foreach ($periodIncomeTotal as $k => $v) {
                            $periodIncomeOverallTotal[$k] = 'SUM(' . $v . ')';
                        }
                    } else {
                        $periodIncomeOverallTotal = $periodIncomeTotal;
                    }
                }

                if ($periodExpenseTotal !== []) {
                    foreach ($periodExpenseTotal as $k => $v) {
                        foreach ($v as $x => $y) {
                            $periodExpenseOverallTotal[$x][$k] = $useFormula ? 'SUM(' . $y . ')' : $y;
                        }
                    }

                    foreach ($periodExpenseOverallTotal as $k => $v) {
                        $periodExpenseOverallTotal[$k] = $useFormula ? 'SUM(' . implode(',', $v) . ')' : array_sum($v);
                    }
                }

                if (! empty($periodIncomeOverallTotal) && ! empty($periodExpenseOverallTotal)) {
                    foreach (array_keys($periodIncomeOverallTotal) as $k) {
                        if ($useFormula) {
                            $periodTotals[$k] = '=' . $periodIncomeOverallTotal[$k] . '-' . $periodExpenseOverallTotal[$k];
                        } else {
                            $periodTotals[$k] = $periodIncomeOverallTotal[$k] - $periodExpenseOverallTotal[$k];
                        }
                    }
                } elseif (! empty($periodIncomeOverallTotal)) {
                    foreach (array_keys($periodIncomeOverallTotal) as $k) {
                        $periodTotals[$k] = $useFormula ? '=' . $periodIncomeOverallTotal[$k] : $periodIncomeOverallTotal[$k];
                    }
                } elseif (! empty($periodExpenseOverallTotal)) {
                    foreach (array_keys($periodExpenseOverallTotal) as $k) {
                        $periodTotals[$k] = $useFormula ? '=' . $periodExpenseOverallTotal[$k] : $periodExpenseOverallTotal[$k];
                    }
                }

                if ($view->items['worksheet1'] == 'Yes') {
                    $xls->renderTotal($periodTotals, 'Net surplus/(deficit)');

                    $xls->setColumnWidth('A', 19);
                    $xls->setColumnWidth('O', 13);
                    $xls->setColumnWidth('P', 75);
                    $xls->setColumnWidth('Q', 25);
                    $xls->setColumnWidth('R', 12);
                }

                // per Lease (start)
                if ($view->items['worksheet3'] == 'Yes') {
                    $incomeAccountsPerLease = dbBudgetIncomeAccountsWitoutComment($propertyID, $year, true);
                    $countIncomePerLease = count($incomeAccountsPerLease);
                    //					if ($view->items['worksheet1'] == 'Yes') $sheetIndex++;
                    $xls->setSheetDetails($sheetIndex, $propertyID . ' per lease', $sheetIndex !== 0);
                    $xls->printer();
                    $xls->line = 1;
                    $xls->columns = [];
                    $periodIncomeTotal = [];
                    $dataSet = [];
                    $count = 0;
                }

                $propertyLookUp = dbGetPropertyLookup($propertyID);
                $propertyName = $propertyLookUp['propertyName'];

                foreach ($periods as $v) {
                    $periodMonths[$v['calendarPeriod']] = date('F', toTimestamp($v['calendarEndDate']));
                }

                $periodCount = count($periodMonths);

                if ($view->items['worksheet3'] == 'Yes') {
                    $xls->addColumn('accountID', 'Account', 10, 'left', '@', $styleHeaderLeftAlign);
                    $xls->addColumn('accountName', 'Account Name', 30, 'left', '@', $styleHeaderLeftAlign);
                    $xls->addColumn('unitCode', 'Unit Code', 30, 'left', '@', $styleHeaderLeftAlign);
                    $xls->addColumn('leaseCode', 'Lease Code', 30, 'left', '@', $styleHeaderLeftAlign);
                    $xls->addColumn('leaseName', 'Lease Name', 30, 'left', '@', $styleHeaderLeftAlign);
                    foreach ($periodMonths as $x => $y) {
                        $xls->addColumn("period{$x}", $y, 23, 'right', $numberFormat);
                    }

                    $xls->addColumn('total', 'Total', 15, 'right', $numberFormat);
                    $xls->addColumn('comment', 'Comments', 15, 'left', '@', $styleHeaderLeftAlign);
                    $xls->addColumn('user', 'User', 15, 'left', '@', $styleHeaderLeftAlign);
                    $xls->addColumn('dateEntered', 'Date Entered', 15, 'left', '@', $styleHeaderLeftAlign);
                    $xls->addSubHeaderItem('title', 1, 40, null);
                }

                $header = "Property: {$propertyName} ({$propertyID})  Year: {$yearRange}\n";
                if ($view->items['worksheet3'] == 'Yes') {
                    $xls->renderHeaderDetails($header);
                    $xls->renderHeader();
                    $xls->freezePanes(0, $xls->line);
                }

                // ## Income ###
                if ($countIncomePerLease !== 0) {
                    $periodTotals = [];
                    foreach ($incomeAccountsPerLease as $a) {
                        $row = [];
                        $ia = dbBudgetIncomeAmounts(
                            $propertyID,
                            $year,
                            $a['accountID'],
                            $view->items['reportType'],
                            $a['leaseCode'],
                            $a['unitCode']
                        );
                        $row['accountID'] = $a['accountID'];
                        $row['accountName'] = $a['accountName'];
                        $row['unitCode'] = $a['unitCode'];
                        $row['leaseCode'] = $a['leaseCode'];
                        $row['leaseName'] = $a['leaseName'];
                        $row['accountType'] = 'Income';

                        if ($view->items['worksheet3'] == 'Yes') {
                            $getComment = dbBudgetIncomeAccountsGetComment(
                                $propertyID,
                                $year,
                                $a['accountID'],
                                $a['leaseCode']
                            );
                            $comment = '';
                            foreach ($getComment as $aCom) {
                                $comment = $comment . $aCom['comment'] . ' ';
                            }

                            $row['comment'] = $comment;
                        } else {
                            $row['comment'] = $a['comment'];
                        }

                        $userEmail = dbGetUserByID($a['commentUser']);
                        $row['user'] = ($userEmail) ? $userEmail['email'] : '';
                        $row['dateEntered'] = $a['commentDate'];
                        if ($view->items['worksheet3'] == 'Yes') {
                            $xls->setName(
                                $propertyID . 'IncomeAccount' . $a['accountID'],
                                'F' . ($xls->line + $count) . ':Q' . ($xls->line + $count)
                            );
                        }

                        $total = 0;

                        foreach ($ia as $i) {
                            if (! $useFormula) {
                                $periodTotals['period' . $i['period']] += $i['amount'];
                            }

                            $row['period' . $i['period']] += $i['amount'];
                            $total += $i['amount'] * 1;
                        }

                        if (! $useFormula) {
                            $row['total'] = $total;
                            $periodTotals['total'] += $total;
                        } else {
                            $row['total'] = '=SUM(F' . ($xls->line + $count) . ':Q' . ($xls->line + $count) . ')';
                        }

                        $dataSet[] = $row;
                        $count++;
                    }

                    if ($useFormula) {
                        foreach ($periodMonths as $x => $y) {
                            if ($view->items['worksheet3'] == 'Yes') {
                                $xls->setName(
                                    $propertyID . "OwnerIncome{$y}",
                                    cellReference($xls->line + 1, $x + 5) . ':' . cellReference(
                                        $xls->line + $countIncomePerLease,
                                        $x + 5
                                    )
                                );
                            }

                            $periodTotals["period{$x}"] = '=SUM(' . cellReference(
                                $xls->line,
                                $x + 5
                            ) . ':' . cellReference($xls->line + $countIncomePerLease - 1, $x + 5) . ')';
                            $periodFormula["period{$x}"] = cellReference($xls->line, $x + 5) . ':' . cellReference(
                                $xls->line + $countIncomePerLease - 1,
                                $x + 5
                            );
                        }

                        $periodTotals['total'] = '=SUM(F' . ($xls->line + $countIncomePerLease) . ':Q' . ($xls->line + $countIncomePerLease) . ')';
                        $periodFormula['total'] = 'F' . ($xls->line + $countIncomePerLease) . ':Q' . ($xls->line + $countIncomePerLease);
                    }
                } else {
                    foreach ($periodMonths as $x => $y) {
                        if ($view->items['worksheet3'] == 'Yes') {
                            $xls->setName(
                                $propertyID . "OwnerIncome{$y}",
                                cellReference($xls->line + 1, $x + 2) . ':' . cellReference(
                                    $xls->line,
                                    $x + 2
                                )
                            );
                        }

                        $periodTotals["period{$x}"] = 0.00;
                        $periodFormula["period{$x}"] = 0.00;
                    }

                    $periodTotals['total'] = 0.00;
                    $periodFormula['total'] = 0.00;
                }

                if ($view->items['worksheet3'] == 'Yes') {
                    $xls->setName(
                        $propertyID . 'OwnerIncomeTotal',
                        'F' . ($xls->line + $countIncomePerLease) . ':Q' . ($xls->line + $countIncomePerLease)
                    );
                    if ($dataSet !== []) {
                        $xls->renderData($dataSet);
                    }

                    $xls->renderTotal($periodTotals);
                }

                $periodIncomeTotal = ($useFormula) ? $periodFormula : $periodTotals;

                $periodTotals = [];

                if ($periodIncomeTotal !== []) {
                    if ($useFormula) {
                        foreach ($periodIncomeTotal as $k => $v) {
                            $periodIncomeOverallTotal[$k] = 'SUM(' . $v . ')';
                        }
                    } else {
                        $periodIncomeOverallTotal = $periodIncomeTotal;
                    }
                }

                if (! empty($periodIncomeOverallTotal)) {
                    foreach (array_keys($periodIncomeOverallTotal) as $k) {
                        $periodTotals[$k] = $useFormula ? '=' . $periodIncomeOverallTotal[$k] : $periodIncomeOverallTotal[$k];
                    }
                }

                if ($view->items['worksheet3'] == 'Yes') {
                    $xls->setColumnWidth('A', 19);
                    $xls->setColumnWidth('Q', 13);
                    $xls->setColumnWidth('R', 75);
                    $xls->setColumnWidth('S', 25);
                    $xls->setColumnWidth('T', 12);
                }

                // per Lease (end)

                // VO Summary
                if ($view->items['worksheet2'] == 'Yes') {
                    $reportType = $view->items['reportType'];
                    //					if ($view->items['worksheet1'] == 'Yes') $sheetIndex++;
                    $xls->setSheetDetails(
                        $sheetIndex,
                        $propertyID . ' Operating Expenditure',
                        $sheetIndex !== 0
                    );
                    $xls->printer();
                    $xls->line = 1;
                    $xls->columns = [];
                    $dataSet = [];

                    $xls->addColumn('accountName', 'Account Name', 30, 'left', '@', $styleHeaderLeftAlign);
                    //                    $xls->addColumn ('totalForProperty', '$', 23, 'right', $numberFormat, $styleHeaderCenterAlign);
                    $xls->addColumn(
                        $propertyID,
                        $_SESSION['country_default']['currency_symbol'],
                        23,
                        'right',
                        $numberFormat,
                        $styleHeaderCenterAlign
                    );
                    //                    $xls->addColumn ('monthlyAmount', 'Tenant Portion (pcm)', 23, 'right', $numberFormat, $styleHeaderCenterAlign);
                    //                    $xls->addColumn ('monthlyAmountRatePerSqm', 'Tenant Rate (per SQM)', 23, 'right', $numberFormat, $styleHeaderCenterAlign);
                    $xls->addSubHeaderItem('title', 1, 40, null);
                    $header = "Property: {$propertyName} ({$propertyID}) \nYear: {$yearRange} \n";
                    $xls->renderHeaderDetails($header);

                    $leaseVOData = dbGetExpLeaseBreakdownVOProperty($propertyID, $year, $reportType);
                    $countLeaseEXPVO = count($leaseVOData);

                    $row = [];
                    $total = 0;
                    $overAllTotal0 = 0;
                    $overAllTotal1 = 0;
                    $overAllTotal2 = 0;
                    $overAllTotal3 = 0;

                    $propertyTotal = [];
                    if ($countLeaseEXPVO > 0) {
                        $xls->renderHeaderDetails(
                            ucwords(strtolower($_SESSION['country_default']['variable_outgoings'])) . ' Expenditure'
                        );
                        foreach ($leaseVOData as $dataLeaseVal) {
                            $unitArea = $dataLeaseVal['unitArea'];
                            $row['accountName'] = $dataLeaseVal['pmca_name'];
                            //                            $row['totalForProperty'] = dbGetExpLeaseBreakdownSumAccount($propertyID,$dataLeaseVal['account_code'],$year,$reportType);

                            $row[$propertyID] = $dataLeaseVal['custom_value'];
                            //                            $row['monthlyAmount'] = $dataLeaseVal['custom_value'] / 12;
                            //                            if($unitArea>0){
                            //                                $row['monthlyAmountRatePerSqm'] = $dataLeaseVal['custom_value'] / $unitArea;
                            //                            }
                            $total += $dataLeaseVal['custom_value'];
                            $dataSet[] = $row;
                            //                            $overAllTotal0 = $overAllTotal0 + (float) $row['totalForProperty'];
                            $overAllTotal1 += (float) $row[$propertyID];
                            //                            $overAllTotal2 = $overAllTotal2 + (float) $row['monthlyAmount'];
                            //                            $overAllTotal3 = $overAllTotal3 + (float) $row['monthlyAmountRatePerSqm'];
                            if (! $useFormula) {
                                $propertyTotal[$propertyID] += (float) $dataLeaseVal['custom_value'];
                            }

                            if (! $useFormula) {
                                $propertyTotal['monthlyAmount'] += (float) $dataLeaseVal['custom_value'] / 12;
                            }

                            if (! $useFormula) {
                                $propertyTotal['totalForProperty'] += (float) $row['totalForProperty'];
                            }

                            if (! $useFormula) {
                                $propertyTotal['monthlyAmountRatePerSqm'] += (float) $row['monthlyAmountRatePerSqm'];
                            }
                        }

                        $xls->renderHeader();
                        //                        $xls->freezePanes (0, $xls->line);
                        $voLine1 = $xls->line;
                        $xls->setName(
                            $propertyID . 'EXPVOOverAll',
                            cellReference($xls->line, 2) . ':' . cellReference(($xls->line + $countLeaseEXPVO - 1), 2)
                        );
                        if ($useFormula) {
                            //                            $propertyTotal['totalForProperty'] = '=SUM(' . cellReference ($xls->line, 2) . ':' . cellReference (($xls->line + $countLeaseEXPVO - 1), 2) . ')';
                            $propertyTotal[$propertyID] = '=SUM(' . cellReference($xls->line, 2) . ':' . cellReference(
                                ($xls->line + $countLeaseEXPVO - 1),
                                2
                            ) . ')';
                            //                            $propertyTotal['monthlyAmount'] = '=SUM(' . cellReference ($xls->line, 4) . ':' . cellReference (($xls->line + $countLeaseEXPVO - 1), 4) . ')';
                            //                            $propertyTotal['monthlyAmountRatePerSqm'] = '=SUM(' . cellReference ($xls->line, 5) . ':' . cellReference (($xls->line + $countLeaseEXPVO - 1), 5) . ')';
                        }

                        $xls->renderData($dataSet);
                        $xls->renderTotal($propertyTotal, 'Sub Total');
                    }

                    $row = [];
                    $dataSet = [];
                    $leaseVOData = dbGetExpLeaseBreakdownRECProperty($propertyID, $year, $reportType);
                    $countLeaseEXPVO = count($leaseVOData);
                    $propertyTotal = [];
                    if ($countLeaseEXPVO > 0) {
                        $xls->line++;
                        $xls->renderHeaderDetails('Directly Recoverable Expenditure');
                        foreach ($leaseVOData as $dataLeaseVal) {
                            $unitArea = $dataLeaseVal['unitArea'];
                            $row['accountName'] = $dataLeaseVal['pmca_name'];
                            //                            $row['totalForProperty'] = dbGetExpLeaseBreakdownSumAccount($propertyID,$dataLeaseVal['account_code'],$year,$reportType);

                            $row[$propertyID] = $dataLeaseVal['custom_value'];
                            //                                $row['monthlyAmount'] = $dataLeaseVal['custom_value'] / 12;
                            //                            if($unitArea>0){
                            //                                $row['monthlyAmountRatePerSqm'] = $dataLeaseVal['custom_value'] / $unitArea;
                            //                            }
                            $total += $dataLeaseVal['custom_value'];
                            $dataSet[] = $row;
                            //                            $overAllTotal0 = $overAllTotal0 + (float) $row['totalForProperty'];
                            $overAllTotal1 += (float) $row[$propertyID];
                            //                                $overAllTotal2 = $overAllTotal2 + (float) $row['monthlyAmount'];
                            //                            $overAllTotal3 = $overAllTotal3 + (float) $row['monthlyAmountRatePerSqm'];
                            if (! $useFormula) {
                                $propertyTotal[$propertyID] += (float) $dataLeaseVal['custom_value'];
                            }

                            //                                if (!$useFormula) $propertyTotal['monthlyAmount'] += (float) $dataLeaseVal['custom_value'] / 12;
                            //                            if (!$useFormula) $propertyTotal['totalForProperty'] += (float) $row['totalForProperty'];
                            //                            if (!$useFormula) $propertyTotal['monthlyAmountRatePerSqm'] += (float) $row['monthlyAmountRatePerSqm'];
                        }

                        $xls->columns = [];

                        $xls->addColumn('accountName', 'Account Name', 30, 'left', '@', $styleHeaderLeftAlign);
                        //                        $xls->addColumn ('totalForProperty', 'Annual Property Budget (pa)', 23, 'right', $numberFormat, $styleHeaderCenterAlign);
                        $xls->addColumn(
                            $propertyID,
                            $_SESSION['country_default']['currency_symbol'],
                            23,
                            'right',
                            $numberFormat,
                            $styleHeaderCenterAlign
                        );
                        //                        $xls->addColumn ('monthlyAmount', ' ', 23, 'right', $numberFormat, $styleHeaderCenterAlign);
                        //                        $xls->addColumn ('monthlyAmountRatePerSqm', 'Tenant Rate (per SQM)', 23, 'right', $numberFormat, $styleHeaderCenterAlign);

                        $xls->renderHeader();
                        //                        $xls->freezePanes (0, $xls->line);
                        $voLine1 = $xls->line;
                        $xls->setName(
                            $propertyID . 'EXPVOOverAll',
                            cellReference($xls->line, 2) . ':' . cellReference(($xls->line + $countLeaseEXPVO - 1), 2)
                        );

                        if ($useFormula) {
                            $propertyTotal[$propertyID] = '=SUM(' . cellReference($xls->line, 2) . ':' . cellReference(
                                ($xls->line + $countLeaseEXPVO - 1),
                                2
                            ) . ')';
                        }

                        $xls->renderData($dataSet);
                        $xls->renderTotal($propertyTotal, 'Sub Total');
                    }

                    $overAllTotal[$propertyID] = $overAllTotal1;
                    $xls->renderTotal($overAllTotal, 'Total');

                    // Lettable Area
                    //                    $lettableArea = (float) dbGetLettableArea ($propertyID, $toDate);
                    $leaseVOData = dbGetExpedentureBudget($propertyID, $year, (int) $year - 1, $reportType);
                    $total_unit_area = 0;
                    foreach ($leaseVOData as $row_data) {
                        if ($row_data['unit_area_total']) {
                            $total_unit_area = $row_data['unit_area_total'];
                        }
                    }

                    if ($total_unit_area) {
                        $xls->line += 2;
                        $voLine2 = $xls->line;
                        $xls->setName($propertyID . 'LettableArea', cellReference($xls->line, 2));
                        $line =
                        [
                            'accountName' => 'Total lettable area',
                            $propertyID => $total_unit_area,
                        ];
                        $xls->renderCustomLine($line, $numberFormatStyle);
                        $xls->line++;

                        $line =
                        [
                            'accountName' => '="Outgoing Expenditure Budget for ' . $yearRange . ' per ' . html_entity_decode(
                                $_SESSION['country_default']['area_unit'],
                                ENT_COMPAT
                            ) . '"',
                            $propertyID => [
                                'value' => round(($total_unit_area != 0 ? ($total / $total_unit_area) : 0), 2),
                                'style' => array_merge($numberFormatStyle, $borderSubTotalStyle),
                            ],
                        ];
                        $xls->renderCustomLine($line, $numberFormatStyle);
                    }
                }

                // VO details per lease
                if ($view->items['worksheet4'] == 'Yes') {
                    // $year
                    $reportType = $view->items['reportType'];
                    $tenant_detail = $view->items['tenant_detail'];
                    $getLeaseBreakdown = dbGetExpLeaseBreakdownDistinct($propertyID, $year, $reportType);
                    foreach ($getLeaseBreakdown as $data) {
                        $leaseCode = $data['lease_code'];
                        $leaseName = $data['lease_name'];

                        $unitCode = $data['unit_code'];
                        $unitName = $data['unit_name'];
                        $unitArea = $data['unit_area'];
                        $tm_unit_code = $unitCode;
                        $tm_unit_name = str_replace('/', '', $tm_unit_code);
                        $sheetName = $tm_unit_name . ' - ' . $data['lease_code'];
                        if ($leaseCode == 'MISC') {
                            $leaseName = 'Miscellaneous';
                            $sheetName = $propertyID . ' - ' . $data['lease_code'];
                        }

                        $xls->setSheetDetails($sheetIndex, $sheetName, $sheetIndex !== 0);
                        $xls->printer();
                        $xls->line = 1;
                        $xls->columns = [];
                        $dataSet = [];

                        $xls->addColumn('accountName', 'Account Name', 30, 'left', '@', $styleHeaderLeftAlign);
                        $xls->addColumn(
                            'totalForProperty',
                            'Annual Property Budget (pa)',
                            23,
                            'right',
                            $numberFormat,
                            $styleHeaderCenterAlign
                        );
                        $xls->addColumn(
                            $propertyID,
                            'Tenant Portion (pa)',
                            23,
                            'right',
                            $numberFormat,
                            $styleHeaderCenterAlign
                        );
                        $xls->addColumn(
                            'monthlyAmount',
                            'Tenant Portion (pcm)',
                            23,
                            'right',
                            $numberFormat,
                            $styleHeaderCenterAlign
                        );

                        switch ($tenant_detail) {
                            case 'show_sqm':
                                $xls->addColumn(
                                    'monthlyAmountRatePerSqm',
                                    'Tenant Rate (per ' . html_entity_decode(
                                        $_SESSION['country_default']['area_unit'],
                                        ENT_COMPAT
                                    ) . ')',
                                    23,
                                    'right',
                                    $numberFormat,
                                    $styleHeaderCenterAlign
                                );
                                break;
                            case 'show_percentage':
                                $xls->addColumn(
                                    'tenantPercentage',
                                    'Tenant %',
                                    23,
                                    'right',
                                    $numberFormat,
                                    $styleHeaderCenterAlign
                                );
                                break;
                            case 'show_both':
                                $xls->addColumn(
                                    'monthlyAmountRatePerSqm',
                                    'Tenant Rate (per ' . html_entity_decode(
                                        $_SESSION['country_default']['area_unit'],
                                        ENT_COMPAT
                                    ) . ')',
                                    23,
                                    'right',
                                    $numberFormat,
                                    $styleHeaderCenterAlign
                                );
                                $xls->addColumn(
                                    'tenantPercentage',
                                    'Tenant Rate %',
                                    23,
                                    'right',
                                    $numberFormat,
                                    $styleHeaderCenterAlign
                                );
                                break;
                        }

                        $xls->addSubHeaderItem('title', 1, 40, null);
                        $header = "Property: {$propertyName} ({$propertyID}) \nLease: {$leaseName} ({$leaseCode}) \nUnit: {$unitName} ({$unitCode}) \nArea: {$unitArea} " . html_entity_decode(
                            $_SESSION['country_default']['area_unit'],
                            ENT_COMPAT
                        ) . " \nYear: {$yearRange} \n";
                        $xls->renderHeaderDetails($header);

                        $leaseVOData = dbGetExpLeaseBreakdownVO($propertyID, $leaseCode, $year, $reportType, $unitCode);
                        $countLeaseEXPVO = count($leaseVOData);

                        $row = [];
                        $total = 0;
                        $overAllTotal0 = 0;
                        $overAllTotal1 = 0;
                        $overAllTotal2 = 0;
                        $overAllTotal3 = 0;
                        $propertyTotal = [];
                        if ($countLeaseEXPVO > 0) {
                            $xls->renderHeaderDetails(
                                ucwords(strtolower($_SESSION['country_default']['variable_outgoings'])) . ' Expenditure'
                            );
                            $lc_row_line_no = $xls->line;
                            foreach ($leaseVOData as $dataLeaseVal) {
                                $lc_row_line_no += 1;
                                $row['accountName'] = $dataLeaseVal['pmca_name'];
                                $row['totalForProperty'] = dbGetExpLeaseBreakdownSumAccount(
                                    $propertyID,
                                    $dataLeaseVal['account_code'],
                                    $year,
                                    $reportType
                                );

                                $row[$propertyID] = $dataLeaseVal['custom_value'];
                                $row['monthlyAmount'] = $dataLeaseVal['custom_value'] / 12;
                                if ($unitArea > 0) {
                                    $row['monthlyAmountRatePerSqm'] = $dataLeaseVal['custom_value'] / $unitArea;
                                    $row['tenantPercentage'] = '=SUM(' . cellReference(
                                        $lc_row_line_no,
                                        3
                                    ) . '/IF(' . cellReference($lc_row_line_no, 2) . '=0,1,' . cellReference(
                                        $lc_row_line_no,
                                        2
                                    ) . '))*100';
                                }

                                $total += $dataLeaseVal['custom_value'];
                                $dataSet[] = $row;
                                $overAllTotal0 += (float) $row['totalForProperty'];
                                $overAllTotal1 += (float) $row[$propertyID];
                                $overAllTotal2 += (float) $row['monthlyAmount'];
                                $overAllTotal3 += (float) $row['monthlyAmountRatePerSqm'];
                                if (! $useFormula) {
                                    $propertyTotal[$propertyID] += (float) $dataLeaseVal['custom_value'];
                                }

                                if (! $useFormula) {
                                    $propertyTotal['monthlyAmount'] += (float) $dataLeaseVal['custom_value'] / 12;
                                }

                                if (! $useFormula) {
                                    $propertyTotal['totalForProperty'] += (float) $row['totalForProperty'];
                                }

                                if (! $useFormula) {
                                    $propertyTotal['monthlyAmountRatePerSqm'] += (float) $row['monthlyAmountRatePerSqm'];
                                }
                            }

                            $xls->renderHeader();
                            //                            $xls->freezePanes (0, $xls->line);
                            $voLine1 = $xls->line;
                            $xls->setName(
                                $propertyID . 'EXPVOOverAll',
                                cellReference($xls->line, 3) . ':' . cellReference(
                                    ($xls->line + $countLeaseEXPVO - 1),
                                    3
                                )
                            );
                            if ($useFormula) {
                                $propertyTotal['totalForProperty'] = '=SUM(' . cellReference(
                                    $xls->line,
                                    2
                                ) . ':' . cellReference(($xls->line + $countLeaseEXPVO - 1), 2) . ')';
                                $propertyTotal[$propertyID] = '=SUM(' . cellReference(
                                    $xls->line,
                                    3
                                ) . ':' . cellReference(($xls->line + $countLeaseEXPVO - 1), 3) . ')';
                                $propertyTotal['monthlyAmount'] = '=SUM(' . cellReference(
                                    $xls->line,
                                    4
                                ) . ':' . cellReference(($xls->line + $countLeaseEXPVO - 1), 4) . ')';
                                $propertyTotal['monthlyAmountRatePerSqm'] = '=SUM(' . cellReference(
                                    $xls->line,
                                    5
                                ) . ':' . cellReference(($xls->line + $countLeaseEXPVO - 1), 5) . ')';
                            }

                            //                            $propertyTotal['tenantPercentage'] = "=SUM(".cellReference (($xls->line + $countLeaseEXPVO), 3)."/IF(".cellReference (($xls->line + $countLeaseEXPVO), 2)."=0,1,".cellReference (($xls->line + $countLeaseEXPVO), 2)."))*100";
                            $xls->renderData($dataSet);
                            $xls->renderTotal($propertyTotal, 'Sub Total');
                        }

                        $row = [];
                        $dataSet = [];
                        $leaseVOData = dbGetExpLeaseBreakdownREC(
                            $propertyID,
                            $leaseCode,
                            $year,
                            $reportType,
                            $unitCode
                        );
                        $countLeaseEXPVO = count($leaseVOData);
                        $propertyTotal = [];
                        if ($countLeaseEXPVO > 0) {
                            $xls->line++;
                            $xls->renderHeaderDetails('Directly Recoverable Expenditure');
                            $lc_row_line_no = $xls->line;

                            foreach ($leaseVOData as $dataLeaseVal) {
                                $lc_row_line_no += 1;
                                $row['accountName'] = $dataLeaseVal['pmca_name'];
                                $row['totalForProperty'] = dbGetExpLeaseBreakdownSumAccount(
                                    $propertyID,
                                    $dataLeaseVal['account_code'],
                                    $year,
                                    $reportType
                                );

                                $row[$propertyID] = $dataLeaseVal['custom_value'];
                                //                                $row['monthlyAmount'] = $dataLeaseVal['custom_value'] / 12;
                                if ($unitArea > 0) {
                                    $row['monthlyAmountRatePerSqm'] = $dataLeaseVal['custom_value'] / $unitArea;
                                    $row['tenantPercentage'] = '=SUM(' . cellReference(
                                        $lc_row_line_no,
                                        3
                                    ) . '/IF(' . cellReference($lc_row_line_no, 2) . '=0,1,' . cellReference(
                                        $lc_row_line_no,
                                        2
                                    ) . '))*100';
                                }

                                $total += $dataLeaseVal['custom_value'];
                                $dataSet[] = $row;
                                $overAllTotal0 += (float) $row['totalForProperty'];
                                $overAllTotal1 += (float) $row[$propertyID];
                                //                                $overAllTotal2 = $overAllTotal2 + (float) $row['monthlyAmount'];
                                $overAllTotal3 += (float) $row['monthlyAmountRatePerSqm'];
                                if (! $useFormula) {
                                    $propertyTotal[$propertyID] += (float) $dataLeaseVal['custom_value'];
                                }

                                //                                if (!$useFormula) $propertyTotal['monthlyAmount'] += (float) $dataLeaseVal['custom_value'] / 12;
                                if (! $useFormula) {
                                    $propertyTotal['totalForProperty'] += (float) $row['totalForProperty'];
                                }

                                if (! $useFormula) {
                                    $propertyTotal['monthlyAmountRatePerSqm'] += (float) $row['monthlyAmountRatePerSqm'];
                                }
                            }

                            $xls->columns = [];

                            $xls->addColumn('accountName', 'Account Name', 30, 'left', '@', $styleHeaderLeftAlign);
                            $xls->addColumn(
                                'totalForProperty',
                                'Annual Property Budget (pa)',
                                23,
                                'right',
                                $numberFormat,
                                $styleHeaderCenterAlign
                            );
                            $xls->addColumn(
                                $propertyID,
                                'Tenant Portion (pa)',
                                23,
                                'right',
                                $numberFormat,
                                $styleHeaderCenterAlign
                            );
                            $xls->addColumn('monthlyAmount', ' ', 23, 'right', $numberFormat, $styleHeaderCenterAlign);
                            switch ($tenant_detail) {
                                case 'show_sqm':
                                    $xls->addColumn(
                                        'monthlyAmountRatePerSqm',
                                        'Tenant Rate (per ' . html_entity_decode(
                                            $_SESSION['country_default']['area_unit'],
                                            ENT_COMPAT
                                        ) . ')',
                                        23,
                                        'right',
                                        $numberFormat,
                                        $styleHeaderCenterAlign
                                    );
                                    break;
                                case 'show_percentage':
                                    $xls->addColumn(
                                        'tenantPercentage',
                                        'Tenant %',
                                        23,
                                        'right',
                                        $numberFormat,
                                        $styleHeaderCenterAlign
                                    );
                                    break;
                                case 'show_both':
                                    $xls->addColumn(
                                        'monthlyAmountRatePerSqm',
                                        'Tenant Rate (per ' . html_entity_decode(
                                            $_SESSION['country_default']['area_unit'],
                                            ENT_COMPAT
                                        ) . ')',
                                        23,
                                        'right',
                                        $numberFormat,
                                        $styleHeaderCenterAlign
                                    );
                                    $xls->addColumn(
                                        'tenantPercentage',
                                        'Tenant Rate %',
                                        23,
                                        'right',
                                        $numberFormat,
                                        $styleHeaderCenterAlign
                                    );
                                    break;
                            }

                            $xls->renderHeader();
                            //                            $xls->freezePanes (0, $xls->line);
                            $voLine1 = $xls->line;
                            $xls->setName(
                                $propertyID . 'EXPVOOverAll',
                                cellReference($xls->line, 3) . ':' . cellReference(
                                    ($xls->line + $countLeaseEXPVO - 1),
                                    3
                                )
                            );

                            if ($useFormula) {
                                $propertyTotal['totalForProperty'] = '=SUM(' . cellReference(
                                    $xls->line,
                                    2
                                ) . ':' . cellReference(($xls->line + $countLeaseEXPVO - 1), 2) . ')';
                                $propertyTotal[$propertyID] = '=SUM(' . cellReference(
                                    $xls->line,
                                    3
                                ) . ':' . cellReference(($xls->line + $countLeaseEXPVO - 1), 3) . ')';
                                //                                $propertyTotal['monthlyAmount'] = '=SUM(' . cellReference ($xls->line, 4) . ':' . cellReference (($xls->line + $countLeaseEXPVO - 1), 4) . ')';
                                $propertyTotal['monthlyAmountRatePerSqm'] = '=SUM(' . cellReference(
                                    $xls->line,
                                    5
                                ) . ':' . cellReference(($xls->line + $countLeaseEXPVO - 1), 5) . ')';
                            }

                            //                            $propertyTotal['tenantPercentage'] = "=SUM(".cellReference (($xls->line + $countLeaseEXPVO), 3)."/IF(".cellReference (($xls->line + $countLeaseEXPVO), 2)."=0,1,".cellReference (($xls->line + $countLeaseEXPVO), 2)."))*100";

                            $xls->renderData($dataSet);
                            $xls->renderTotal($propertyTotal, 'Sub Total');
                        }

                        $overAllTotal['totalForProperty'] = $overAllTotal0;
                        $overAllTotal[$propertyID] = $overAllTotal1;
                        $overAllTotal['monthlyAmountRatePerSqm'] = $overAllTotal3;
                        $overAllTotal['monthlyAmount'] = $overAllTotal2;
                        //                        $overAllTotal['tenantPercentage'] = "=SUM(".cellReference (($xls->line), 3)."/IF(".cellReference (($xls->line), 2)."=0,1,".cellReference (($xls->line ), 2)."))*100";
                        $xls->renderTotal($overAllTotal, 'Total');

                        $sheetIndex++;
                    }
                }


                $sheetIndex++;
            }
        }

        $xls->close(false, $format);
        if ($sheetIndex === 0) {
            unlink($filePath);
        } else {
            // -- if it s a scheduled task - attach the report and email to the requester
            if ($context[IS_TASK]) {
                $email = new EmailTemplate('views/emails/basicEmail.html', SYSTEMURL);
                $attachment = [];
                $attachment[0]['file'] = $filePath;
                $attachment[0]['content_type'] = "application/{$format}";
                sendMail(
                    $_SESSION['email'],
                    $_SESSION['first_name'] . ' ' . $_SESSION['last_name'],
                    $email->toString(),
                    'Budget Report',
                    $attachment
                );
                logData('Emailed budget report to ' . $_SESSION['email']);
                $context[TASK_COMPLETE] = true;
            }

            if ($propertyCount > THRESHOLD_EXCELREPORT) {
                $view->items['statusMessage'] = 'Due to its complexity, this report has been scheduled and will be emailed to you upon completion.';
            } else {
                $view->items['downloadPath'] = $downloadPath;
            }
        }
    }

    if (isset($view->items['from_api'])) {
        return $view->items['downloadPath'];
    }

    $view->render();

}

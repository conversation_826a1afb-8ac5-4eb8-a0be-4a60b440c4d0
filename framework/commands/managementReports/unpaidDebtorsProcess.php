<?php

use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Fill;

function dbGetCurrentUnitArrearsReport($propertyID, $leaseID)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);

    $sql = 'SELECT u.pmua_unit AS unitID,
			u.pmua_lease AS leaseID,
			p.pmpu_desc AS unitDescription,
			u.pmua_area AS unitArea,
			u.pmua_status AS unitStatus,
			u.pmua_id AS unitAreaID,
			CONVERT(char(10), u.pmua_from_dt, 103) AS leaseFromDate,
			CONVERT(char(10), u.pmua_to_dt,103) AS leaseToDate,
			p.pmpu_bedroom AS unitBedroom,
			p.pmpu_bathroom AS unitBathroom,
			p.pmpu_carpark AS unitCarPark
		FROM pmua_unit_area u, pmpu_p_unit p
		WHERE u.pmua_prop=?
			AND p.pmpu_unit = u.pmua_unit
			AND p.pmpu_prop = u.pmua_prop
			AND u.pmua_lease=?
		ORDER BY pmua_from_dt DESC';

    return $dbh->executeSingle($sql, [$propertyID, $leaseID]);
}

function get_prepayments($propertyID, $leaseID, $dueDate)
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);
    $prepaidSQL =  'SELECT CONVERT(char(10), pmxd_alloc_dt, 103) AS pmxd_alloc_dt,*
					FROM[PMXD_AR_ALLOC] E
					WHERE PMXD_ALLOC_DT <= CONVERT(datetime, ?, 103)
						AND PMXD_PROP = ?
						AND PMXD_LEASE = ?
						AND NOT EXISTS
								(SELECT *
									FROM [AR_TRANSACTION] R
									WHERE((BATCH_NR = E.[PMXD_T_BATCH] AND BATCH_LINE_NR = E.[PMXD_T_LINE]))
									AND TRANS_DATE <= CONVERT(datetime, ?, 103))';

    return $dbh->executeSet($prepaidSQL, false, true, [$dueDate, $propertyID, $leaseID, $dueDate]);
}

function fetchOutstandingAmountsArrears($debtorID, $propertyID, $leaseID, $dueDate, $showZero = 'No', $receiptsToDate = '')
{
    global $clientDirectory;
    $records =  [];

    // $outstanding = dbGetInvoiceCharges ($propertyID, $leaseID, $debtorID, $dueDate, $batchNumber);
    $outstanding = dbGetInvoiceChargesUnpaidDebtors($propertyID, $leaseID, $debtorID, $dueDate, $receiptsToDate);

    if ($outstanding) {
        foreach ($outstanding as $row) {
            // -- offset is all transactions allocated against the reference transaction minus the effect of any adjustments [INV<->CRE / CRE<->INV] (to prevent a double count)
            $offset = bcadd($row['totalAllocated'], $row['totalReallocated'], 2);
            $row['unallocated'] = round($row['unallocated'] * 1, 2);
            $row['amount'] = round($row['amount'], 2);
            $unpaidAmount = bcsub($row['amount'], $offset, 2);
            $unpaidTaxAmount = round($row['taxAmount'] - $row['totalAllocatedTax'], 2);
            $unpaidNetAmount = round($row['netAmount'] - $row['totalAllocatedNet'], 2);

            if ((($showZero == 'No' && $unpaidAmount != 0 and ! (($unpaidTaxAmount == 0 and $unpaidNetAmount == 0) || ($unpaidTaxAmount == -0.01 and $unpaidNetAmount == 0.01))) || $showZero == 'Yes') && $row['transactionType'] != TYPE_CASH) {
                $row['unpaidAmount'] = bcsub($row['amount'], $offset, 2);
                $row['unpaidTaxAmount'] = $unpaidTaxAmount;
                $row['unpaidNetAmount'] = $unpaidNetAmount;

                if ($row['transactionType'] == 'INV' or $row['transactionType'] == 'CRE') {
                    [$day_, $month_, $year_] = explode('/', $row['dueDate']);
                    // $filename = 'tax_invoice_'. '20140701' . '_'. '9646'. '_'. 'ALBANY'. '_'. 'MAST';
                    $filename = 'tax_invoice_' . $year_ . $month_ . $day_ . '_' . $row['invoiceNumber'] . '_' . $propertyID . '_' . $leaseID;
                    $file = "../reports/{$clientDirectory}/pdf/TaxInvoice/$filename.pdf";
                    // echo $file .'<br>';
                    if (file_exists($file)) {
                        $row['filePath'] = $file;
                    } else {
                        [$day_, $month_, $year_] = explode('/', $row['transactionDate']);
                        // $filename = 'tax_invoice_'. '20140701' . '_'. '9646'. '_'. 'ALBANY'. '_'. 'MAST';
                        $filename = 'tax_invoice_' . $year_ . $month_ . $day_ . '_' . $row['invoiceNumber'] . '_' . $propertyID . '_' . $leaseID;
                        $file = "../reports/{$clientDirectory}/pdf/TaxInvoice/$filename.pdf";
                        // echo $file .'<br>';
                        if (file_exists($file)) {
                            $row['filePath'] = $file;
                        } else {
                            [$day_, $month_, $year_] = explode('/', $row['toDate']);
                            $the_date = date('d/m/Y', strtotime("$month_/$day_/$year_"));
                            [$day_, $month_, $year_] = explode('/', $the_date);
                            // $filename = 'tax_invoice_'. '20140701' . '_'. '9646'. '_'. 'ALBANY'. '_'. 'MAST';
                            $filename = 'tax_invoice_' . $year_ . $month_ . $day_ . '_' . $row['invoiceNumber'] . '_' . $propertyID . '_' . $leaseID;
                            $file = "../reports/{$clientDirectory}/pdf/TaxInvoice/$filename.pdf";
                            // echo $file .'<br>';
                            if (file_exists($file)) {
                                $row['filePath'] = $file;
                            }
                        }
                    }
                }

                $records[] = $row;
            } elseif (($row['transactionType'] == TYPE_CASH) && ($row['unallocated'] != 0)) {
                $unallocated = dbGetUnallocated($propertyID, $leaseID, $debtorID, $row['batchNumber'], $row['batchLineNumber'], $dueDate);
                if ($unallocated) {
                    foreach ($unallocated as $unallocatedItem) {
                        if (($showZero == 'No' && $unallocatedItem['amount'] != 0 and ! (($unpaidTaxAmount == 0 and $unpaidNetAmount == 0) || ($unpaidTaxAmount == -0.01 and $unpaidNetAmount == 0.01))) || $showZero == 'Yes') {
                            $unallocatedItem['debtorID'] = $row['debtorID'];
                            $unallocatedItem['debtorName'] = $row['debtorName'];
                            $unallocatedItem['transactionAmount'] = $unallocatedItem['amount'];
                            $unallocatedItem['batchNumber'] = $row['batchNumber'];
                            $unallocatedItem['batchLineNumber'] = $row['batchLineNumber'];
                            $unallocatedItem['transactionDate'] = $row['transactionDate'];
                            $unallocatedItem['dueDate'] = $row['dueDate'];
                            $unallocatedItem['invoiceNumber'] = $row['invoiceNumber'];
                            $unallocatedItem['transactionType'] = TYPE_CASH;
                            $unallocatedItem['unallocated'] = $row['unallocated'];

                            $unallocatedItem['unpaidAmount'] = $unallocatedItem['amount'];

                            $unpaidTaxAmount = round($unallocatedItem['taxAmount'] - $row['totalAllocatedTax'], 2);
                            $unpaidNetAmount = round($unallocatedItem['netAmount'] - $row['totalAllocatedNet'], 2);

                            $unallocatedItem['unpaidTaxAmount'] = $unpaidTaxAmount;
                            $unallocatedItem['unpaidNetAmount'] = $unpaidNetAmount;

                            $unallocatedItem['unitID'] = $row['unitID'];
                            $unallocatedItem['unitDescription'] = $row['unitDescription'];

                            $records[] = $unallocatedItem;
                        }
                    }
                }
            }
        }
    }

    // get prepayments

    $unpaid_total_net = 0;
    $unpaid_total_gst = 0;
    $unpaid_total_gross = 0;
    $prepaid_result = get_prepayments($propertyID, $leaseID, $dueDate);

    $prepayments_ = [];
    foreach ($prepaid_result as $row) {
        $a = [];
        $unpaid_debtors_code 	= $row['pmxd_s_debtor'];
        $transaction_date 	= $row['pmxd_alloc_dt'];
        $unpaid_account_code 	= $row['pmxd_acc'];
        $unpaid_amount 		= $row['pmxd_alloc_amt'];
        $unpaid_tax_amt 	= $row['pmxd_tax_amt'];
        $unpaid_description 	= 'Prepayment';
        $unpaid_date_from 	= '';
        $unpaid_date_to 	= '';


        $unpaid_net_amount = bcsub($unpaid_amount, $unpaid_tax_amt, 2);

        if ($unpaid_amount != 0) {
            $unpaid_total_net = bcadd($unpaid_total_net, $unpaid_net_amount, 2);
            $unpaid_total_gst = bcadd($unpaid_total_gst, $unpaid_tax_amt, 2);
            $unpaid_total_gross = bcadd($unpaid_total_gross, $unpaid_amount, 2);
        }

        // if ($unpaid_amount == '0')// && $unpaid_tax_amt_display == "0.00" && $unpaid_net_amount_display == "0.00" )
        if ($unpaid_amount == 0 and (($unpaid_tax_amt == 0 and $unpaid_net_amount == 0) || ($unpaid_tax_amt == -0.01 and $unpaid_net_amount == 0.01) || ($unpaid_tax_amt == 0.01 and $unpaid_net_amount == -0.01))) {
        } else {
            // $html .=  '<tr class="' . alternateNextRow() . '">' .
            // "<td>$unpaid_account_code</td>
            // <td>$unpaid_debtors_name</td>
            // <td>$unpaid_description</td>
            // <td>$transaction_date</td>
            // <td>$unpaid_date_from</td>
            // <td align=right>$unpaid_date_to</td>
            // <td align=right>$unpaid_net_amount_display</td>
            // <td align=right>$unpaid_tax_amt_display</td>
            // <td align=right>$unpaid_gross_amount_display</td>" .
            // "</tr>";
            $a['transactionDate'] = $transaction_date;
            // $a['dueDate'] = $transaction_date;
            $a['invoiceDate'] = $transaction_date;
            $a['accountID'] = $unpaid_account_code;
            $a['description'] = $unpaid_description;
            $a['transactionType'] = '';
            $a['invoiceNumber'] = '';
            $a['fromDate'] = $unpaid_date_from;
            $a['toDate'] = $unpaid_date_to;
            $a['dueDate'] = '';
            $a['amount'] = $unpaid_amount;
            $a['unpaidNetAmount'] = $unpaid_net_amount;
            $a['unpaidTaxAmount'] = $unpaid_tax_amt;
            $a['unpaidAmount'] = $unpaid_amount;

            $prepayments_[] = $a;
        } // end of else - after - if ($unpaid_gross_amount_display == '0.00')

        // $i++;
        // endwhile; // end of while ($i < $prepaid_number) :
        // endif;
    }// end of - foreach ($prepaid_result as $row)

    if ($unpaid_total_gross != 0) {
        $records = array_merge($records, $prepayments_);
    }

    return $records;
}

function itemExists($array, $key, $value)
{
    $i = 0;
    $max = count($array);
    while ($i < $max) {
        if ($array[$i][$key] == $value) {
            return true;
        }
        $i++;
    }

    return false;
}

function unpaidDebtorsProcess(&$context)
{
    global $pathPrefix, $clientDirectory;
    $newAmounts =  [];
    $oldAmounts =  [];

    if (! isPostback()) {
        $view = new MasterPage(userViews(), '/accountingReports/unpaidDebtorsProcess.html');
        $view->setSection($context['module']);
    } else {
        $view = new UserControl(userViews(), '/accountingReports/unpaidDebtorsProcess.html');
    }

    $validationErrors =  [];

    if ($context[IS_TASK]) {
        $view->bindAttributesFrom($context);
        extract($context);
    } else {
        $view->bindAttributesFrom($_REQUEST);
        $propertyCount = 0;
        if ($view->items['property']) {
            $propertyCount = count(deserializeParameters($view->items['property']));
        }
        $view->items['propertyCount'] = $propertyCount;
        $view->items['command'] = 'unpaidDebtorsProcess';
        $view->items['module'] = 'accountingReports';
        $queue = new Queue(TASKTYPE_DEBTORS_REPORT);
        if ($propertyCount > THRESHOLD_DEBTORSREPORT) {
            $queue->add($_SESSION['clientID'], $_SESSION['un'], 'command=unpaidDebtorsProcess&module=accountingReports', $view->items);
        }
    }

    if (($context[IS_TASK]) || ($propertyCount <= THRESHOLD_DEBTORSREPORT)) {
        $format = $view->items['format'];
        if ($view->items['format2']) {
            $format = $view->items['format2'];
        }
        // $numberFormat = ($format == FILETYPE_XLS) ? false : true;

        // # HANDLE PROCESSING LOGIC ##
        $properties = deserializeParameters($view->items['property']);
        $view->items['tenantIDs'] = deserializeParameters($view->items['tenantID']);

        if (! $properties and $view->items['tenantIDs']) {
            // get properties of tenants
            $properties = getDistinctPropertyFromLease($view->items['tenantIDs']);
        }


        $accounts = deserializeParameters($view->items['accounts']);
        $portfolio = mapParameters(dbGetParams('PORTMGR'));
        $startDate = STARTDATE;
        $endDate = $view->items['toDate'];
        // $endDate = $view->items['toDate'];
        $dates = buildDatesForReport(oneDayAfter($endDate), $view->items['frequency']);

        if (! $context['forOwnerReport'] || ($context['forOwnerReport'] && $context['logo'])) {
            $logoFile = dbGetClientLogo();
            $logoPath = "assets/clientLogos/{$logoFile}";
        }

        $_filePath = "{$pathPrefix}{$clientDirectory}/{$format}/" . DOC_UNPAIDDEBTORS . '/';
        $_downloadPath = "{$clientDirectory}/{$format}/" . DOC_UNPAIDDEBTORS;

        $file = 'arrearsReport_' . date('YmdHis') . rand(0, 1000) . ".{$format}";
        $filePath = $_filePath . $file;
        $downloadPath = "{$_downloadPath}/{$file}";

        $numberFormat = '_(* #,##0.00_);_(* (#,##0.00);_(* "-"??_);_(@_)';

        if ($format == FILETYPE_PDF) {
            $report = new PDFDataMultiLineReport($filePath, $logoPath, A4_LANDSCAPE);
            $report->multiLine = true;
        } else {
            $xlsDownloadPath = $filePath;
            checkDirPath($xlsDownloadPath, true);
            $context['MultixlsDownloadPath'] = [$xlsDownloadPath, $file];

            $report = new XLSDataReport($filePath, 'Arrears Report');
            $report->enableFormatting = true;
        }

        $criteria =  [];
        switch ($view->items['accountMethod']) {
            case 'accountCode':$criteria[] = 'By Account Codes (' . implode(',', $accounts) . ')';
                break;
            case 'allAccounts':$criteria[] = 'All Accounts';
                break;
            default:
                if ($view->items['showArrearsAged'] != 'Yes') {
                    $criteria[] =  'By Property';
                } else {
                    $criteria[] =  ($view->items['showArrearsWithDetails'] == 'Yes') ? 'Aged with details, By Property' : 'Aged without details, By Property';
                }
                break;
        }
        if ($view->items['propertyManager']) {
            $criteria[] = ' for ' . $portfolio[$view->items['propertyManager']];
        }
        $criteria = 'Criteria - ' . implode(', ', $criteria);
        $header = new ReportHeader('Arrears Report', 'as of ' . $view->items['toDate'], $criteria);
        $header->xPos = $report->hMargin;
        $header->yPos = $report->pageHeight - $report->vMargin;
        if ($format == FILETYPE_PDF) {
            $report->attachObject('header', $header);
            $footer = new TraccFooter(null, 'arrears report', $report->pageSize);
            $report->attachObject('footer', $footer);
        }

        $data =  [];
        $data_ =  [];
        $count = 0;
        $grand_total = [];
        foreach ($properties as $propertyID) {
            $printProperty = true;

            $a = [];
            $totalPerProperty = [
                'total' => 0,
                'current' => 0,
                'period1' => 0,
                'period2' => 0,
                'period3' => 0,
                'period4' => 0,
            ];
            $tenantList = dbTenantDetailList($propertyID, $view->items['tenantIDs']);
            // $data[$propertyID]['name'] = $propertyID . ' - '.getPropertyName($propertyID);
            foreach ($tenantList as $l) {
                // -- note: the receipts section is only using the outstanding records section, so the new records section isnt required anymore

                if ($view->items['badDebtProvision'] == 'Yes' && $l['badDebtProvision']) {
                    continue;
                }

                $tenant_total = [];
                $records = fetchOutstandingAmountsArrears(null, $l['propertyID'], $l['leaseID'], $view->items['toDate'], 'No', $view->items['receiptsToDate']);
                foreach ($records as $id => $item) {
                    if ($item['transactionType'] == 'CRE') {
                        $records[$id]['stamp'] = toDateStamp($item['transactionDate']);
                    } else {
                        $records[$id]['stamp'] = ($item['dueDate']) ? toDateStamp($item['dueDate']) : toDateStamp($item['transactionDate']);
                    }
                }


                // added by arjay*************
                if ($view->items['showArrearsAged'] == 'Yes') {

                    $perAccount = [];
                    $perTenant = [
                        'leaseID' => $l['leaseID'],
                        'leaseName' => $l['leaseName'],
                        'total' => 0,
                        'current' => 0,
                        'period1' => 0,
                        'period2' => 0,
                        'period3' => 0,
                        'period4' => 0,
                    ];

                    $accountId = [];
                    foreach ($records as $id => $item) {

                        if (! isset($perAccount['tenantName'])) {
                            $perAccount['tenantName'] = '(' . $l['leaseID'] . ') - ' . $l['leaseName'];
                            $perTenant['tenantName'] = '(' . $l['leaseID'] . ') - ' . $l['leaseName'];
                        }


                        if ($item['unitID']) {
                            $perAccount['tenantName'] = '(' . $l['leaseID'] . ') - ' . $l['leaseName'] . ' (' . $item['unitID'] . ': ' . $item['unitDescription'] . ')';
                            $perTenant['tenantName'] = '(' . $l['leaseID'] . ') - ' . $l['leaseName'] . ' (' . $item['unitID'] . ': ' . $item['unitDescription'] . ')';
                        } else { // still check for current unit - possible if all transactions for the lease are CRE
                            $currUnit = dbGetCurrentUnitArrearsReport($l['propertyID'], $l['leaseID']);
                            if ($currUnit['unitID']) {
                                $perAccount['tenantName'] = '(' . $l['leaseID'] . ') - ' . $l['leaseName'] . ' (' . $currUnit['unitID'] . ': ' . $currUnit['unitDescription'] . ')';
                                $perTenant['tenantName'] = '(' . $l['leaseID'] . ') - ' . $l['leaseName'] . ' (' . $currUnit['unitID'] . ': ' . $currUnit['unitDescription'] . ')';
                            }
                        }

                        if ($view->items['includeArrearsNotes'] == 'Yes') {
                            $leaseNotes = dbGetArrearsLeaseNotes($l['propertyID'], $l['leaseID']);
                            $perAccount['leaseNotes'] = $leaseNotes;
                        }

                        if ($view->items['showleaseGurantee'] == 'Yes') {
                            $leaseGurantee = dbGetLeaseGuaranteeTop($l['propertyID'], $l['leaseID']);
                            $perAccount['leaseGurantee'] = $leaseGurantee;
                            $perTenant['leaseGurantee'] = $leaseGurantee;
                        }

                        if (! in_array($item['accountID'], $accountId)) {
                            $_t = ageTransactionsForReport([$item], $dates);

                            $current = (isset($_t[0]['total'])) ? $_t[0]['total'] : 0;
                            $period1 = (isset($_t[1]['total'])) ? $_t[1]['total'] : 0;
                            $period2 = (isset($_t[2]['total'])) ? $_t[2]['total'] : 0;
                            $period3 = (isset($_t[3]['total'])) ? $_t[3]['total'] : 0;
                            $period4 = (isset($_t[4]['total'])) ? $_t[4]['total'] : 0;
                            $perAccount['lease'] = $l['leaseID'];
                            $perAccount['leaseData'][$item['accountID']] = [
                                'accountID' => $item['accountID'],
                                'description' => $item['description'],
                                'accountName' => $item['accountName'],
                                'total' => ($current + $period1 + $period2 + $period3 + $period4),
                                'current' => $current,
                                'period1' => $period1,
                                'period2' => $period2,
                                'period3' => $period3,
                                'period4' => $period4,
                            ];

                            $perTenant['total'] = $perTenant['total'] + ($current + $period1 + $period2 + $period3 + $period4);
                            $perTenant['current'] = $perTenant['current'] + $current;
                            $perTenant['period1'] = $perTenant['period1'] + $period1;
                            $perTenant['period2'] = $perTenant['period2'] + $period2;
                            $perTenant['period3'] = $perTenant['period3'] + $period3;
                            $perTenant['period4'] = $perTenant['period4'] + $period4;
                            array_push($accountId, $item['accountID']);
                        } else {

                            $_t = ageTransactionsForReport([$item], $dates);

                            $current = (isset($_t[0]['total'])) ? $_t[0]['total'] : 0;
                            $period1 = (isset($_t[1]['total'])) ? $_t[1]['total'] : 0;
                            $period2 = (isset($_t[2]['total'])) ? $_t[2]['total'] : 0;
                            $period3 = (isset($_t[3]['total'])) ? $_t[3]['total'] : 0;
                            $period4 = (isset($_t[4]['total'])) ? $_t[4]['total'] : 0;
                            $perAccount['leaseData'][$item['accountID']]['total'] = $perAccount['leaseData'][$item['accountID']]['total'] + $current + $period1 + $period2 + $period3 + $period4;
                            $perAccount['leaseData'][$item['accountID']]['current'] =  $perAccount['leaseData'][$item['accountID']]['current'] + $current;
                            $perAccount['leaseData'][$item['accountID']]['period1'] =  $perAccount['leaseData'][$item['accountID']]['period1'] + $period1;
                            $perAccount['leaseData'][$item['accountID']]['period2'] =  $perAccount['leaseData'][$item['accountID']]['period2'] + $period2;
                            $perAccount['leaseData'][$item['accountID']]['period3'] =  $perAccount['leaseData'][$item['accountID']]['period3'] + $period3;
                            $perAccount['leaseData'][$item['accountID']]['period4'] =  $perAccount['leaseData'][$item['accountID']]['period4'] + $period4;
                            $perTenant['total'] = $perTenant['total'] + ($current + $period1 + $period2 + $period3 + $period4);
                            $perTenant['current'] = $perTenant['current'] + $current;
                            $perTenant['period1'] = $perTenant['period1'] + $period1;
                            $perTenant['period2'] = $perTenant['period2'] + $period2;
                            $perTenant['period3'] = $perTenant['period3'] + $period3;
                            $perTenant['period4'] = $perTenant['period4'] + $period4;

                        }

                    }


                    $perAccount['data'] = $perTenant;

                    $data_[$propertyID]['name'] = $propertyID . ' - ' . getPropertyName($propertyID);
                    if ($view->items['showCredit'] == 'Yes') {
                        $totalPerProperty['total'] = $totalPerProperty['total'] + $perTenant['total'];
                        $totalPerProperty['current'] = $totalPerProperty['current'] + $perTenant['current'];
                        $totalPerProperty['period1'] = $totalPerProperty['period1'] + $perTenant['period1'];
                        $totalPerProperty['period2'] = $totalPerProperty['period2'] + $perTenant['period2'];
                        $totalPerProperty['period3'] = $totalPerProperty['period3'] + $perTenant['period3'];
                        $totalPerProperty['period4'] = $totalPerProperty['period4'] + $perTenant['period4'];
                        $data_[$propertyID]['total'] = $totalPerProperty;
                        $data_[$propertyID]['data'][] = ($view->items['showArrearsWithDetails'] == 'Yes') ? $perAccount : $perTenant;

                    } else {
                        if ($perAccount['data']['total'] >= 0) {
                            $totalPerProperty['total'] = $totalPerProperty['total'] + $perTenant['total'];
                            $totalPerProperty['current'] = $totalPerProperty['current'] + $perTenant['current'];
                            $totalPerProperty['period1'] = $totalPerProperty['period1'] + $perTenant['period1'];
                            $totalPerProperty['period2'] = $totalPerProperty['period2'] + $perTenant['period2'];
                            $totalPerProperty['period3'] = $totalPerProperty['period3'] + $perTenant['period3'];
                            $totalPerProperty['period4'] = $totalPerProperty['period4'] + $perTenant['period4'];
                            $data_[$propertyID]['total'] = $totalPerProperty;
                            $data_[$propertyID]['data'][] = ($view->items['showArrearsWithDetails'] == 'Yes') ? $perAccount : $perTenant;
                        }
                    }
                }


                // *********end added by arjay*********

                if (count($records ?? []) > 0) {
                    if (! isset($data[$propertyID]['name'])) {

                        $data[$propertyID]['name'] = $propertyID . ' - ' . getPropertyName($propertyID);
                        $data[$propertyID]['amount'] =
                        $data[$propertyID]['unpaidNetAmount'] =
                        $data[$propertyID]['unpaidTaxAmount'] =
                        $data[$propertyID]['unpaidAmount'] = 0;
                        $printProperty = false;
                    }

                    if ($view->items['includeArrearsNotes'] == 'Yes' and $view->items['showArrearsAged'] != 'Yes') {
                        $leaseNotes = dbGetArrearsLeaseNotes($l['propertyID'], $l['leaseID']);

                        $data[$propertyID]['lease'][$l['leaseID']] = ['propertyID' => $l['propertyID'], 'leaseID' => $l['leaseID'],
                            'leaseName' => '(' . $l['leaseID'] . ') - ' . $l['leaseName'],
                            'leaseNotes' => $leaseNotes,
                            'data' => []];
                    }

                } else {
                    continue;
                }

                foreach ($records as $r) {

                    $a = [];
                    $a['tenantID'] = $r['debtorID'];
                    $a['leaseID'] = $l['leaseID'];
                    $a['tenantName'] = '(' . $l['leaseID'] . ') - ' . $l['leaseName'] . ($r['unitID'] ? ' (' . $r['unitID'] . ': ' . $r['unitDescription'] . ')' : ' - ' . $l['description']);
                    $data[$propertyID]['lease'][$l['leaseID']]['leaseName'] = '(' . $l['leaseID'] . ') - ' . $l['leaseName'] . ($r['unitID'] ? ' (' . $r['unitID'] . ': ' . $r['unitDescription'] . ')' : ' - ' . $l['description']);
                    $a['accountID'] = $r['accountID'];
                    $a['propertyID'] = $r['propertyID'];
                    $a['description'] = $r['description'];
                    $a['transactionType'] = $r['transactionType'];
                    $a['invoiceNumber'] = $r['invoiceNumber'];
                    $a['transactionDate'] = $r['transactionDate'];
                    $a['dueDate'] = $r['dueDate'];

                    $a['amount'] = $r['amount'];
                    $a['unpaidAmount'] = $r['unpaidAmount'];
                    $a['unpaidNetAmount'] = $r['unpaidNetAmount'];
                    $a['unpaidTaxAmount'] = $r['unpaidTaxAmount'];

                    $a['amount_unformatted'] = $r['amount'];
                    $a['unpaidAmount_unformatted'] = $r['unpaidAmount'];
                    $a['unpaidNetAmount_unformatted'] = $r['unpaidNetAmount'];
                    $a['unpaidTaxAmount_unformatted'] = $r['unpaidTaxAmount'];

                    $a['fromDate'] = $r['fromDate'];
                    $a['toDate'] = $r['toDate'];
                    $a['invoiceDate'] = $r['invoiceDate'];
                    $a['taxCode'] = $r['taxCode'];
                    $a['filePath'] = $r['filePath'];



                    $data[$propertyID]['lease'][$l['leaseID']]['data'][] = $a;

                    $tenant_total[0] += $r['amount'];
                    $tenant_total[1] += $r['unpaidAmount'];
                    $tenant_total[2] += $r['unpaidNetAmount'];
                    $tenant_total[3] += $r['unpaidTaxAmount'];

                }
                if (count($records ?? []) > 0) {
                    $totalRow['transactionDate'] = '(' . $l['leaseID'] . ') - ' . $l['leaseName'] . ' (' . $r['unitID'] . ': ' . $r['unitDescription'] . ')' . '  Total : ';
                    $totalRow['amount'] = ($tenant_total[0]);
                    $totalRow['unpaidAmount'] = ($tenant_total[1]);
                    $totalRow['unpaidNetAmount'] = ($tenant_total[2]);
                    $totalRow['unpaidTaxAmount'] = ($tenant_total[3]);

                    $data[$propertyID]['lease'][$l['leaseID']]['totalRow'] = $totalRow;

                    if ($view->items['showCredit'] == 'No' and $totalRow['unpaidAmount'] < 0) {
                        unset($data[$propertyID]['lease'][$l['leaseID']]);
                    } else {
                        $data[$propertyID]['amount'] += $totalRow['amount'];
                        $data[$propertyID]['unpaidAmount'] += $totalRow['unpaidAmount'];
                        $data[$propertyID]['unpaidNetAmount'] += $totalRow['unpaidNetAmount'];
                        $data[$propertyID]['unpaidTaxAmount'] += $totalRow['unpaidTaxAmount'];

                        $grand_total['transactionDate'] = 'Grand Total';
                        $grand_total['amount'] += $totalRow['amount'];
                        $grand_total['unpaidAmount'] += $totalRow['unpaidAmount'];
                        $grand_total['unpaidNetAmount'] += $totalRow['unpaidNetAmount'];
                        $grand_total['unpaidTaxAmount'] += $totalRow['unpaidTaxAmount'];
                    }

                }

                if (count($data[$propertyID]['lease']) == 0) {
                    unset($data[$propertyID]);
                }
            }

            if ($view->items['showArrearsAged'] == 'Yes' and $data_[$propertyID]['total']['total'] == 0) {
                unset($data_[$propertyID]);
            }
            $count++;
        }
        $view->items['data_'] = $data_;
        $view->items['data'] = $data;
        $view->items['properties'] = $properties;
        // }


        if ($format != FILETYPE_SCREEN) {
            $topHeaderStyle = [
                'fill' => [
                    'type' => Fill::FILL_SOLID,
                    'color' => ['rgb' => '004c7a'],
                ],
                'font' =>  ['bold' => true, 'color' =>  ['rgb' => 'ffffff']],
                'alignment' =>  ['vertical' => Alignment::VERTICAL_CENTER,  'horizontal' => Alignment::HORIZONTAL_LEFT],
            ];

            $topHeaderStyleRight = [
                'fill' => [
                    'type' => Fill::FILL_SOLID,
                    'color' => ['rgb' => '004c7a'],
                ],
                'font' =>  ['bold' => true, 'color' =>  ['rgb' => 'ffffff']],
                'alignment' =>  ['vertical' => Alignment::VERTICAL_CENTER,  'horizontal' => Alignment::HORIZONTAL_RIGHT],
            ];

            if ($view->items['showArrearsAged'] == 'Yes') {
                if ($view->items['showArrearsWithDetails'] == 'Yes') {
                    $report->addColumn('accountID', 'Account', 50, 'left', '@', $topHeaderStyle);
                    if ($view->items['showleaseGurantee'] == 'Yes') {
                        $report->addColumn('description', 'Description', 217, 'left', null, $topHeaderStyle);
                        $report->addColumn('leaseGuarantee1', 'Lease', 50, 'right', null, $topHeaderStyleRight);
                        $report->addColumn('leaseGuarantee2', 'Guarantee', 50, 'left', '#,##0.00_);(#,##0.00)', $topHeaderStyle);
                    } else {
                        $report->addColumn('description', 'Description', 317, 'left', null, $topHeaderStyle);
                    }
                    $report->addColumn('total', 'Total ($)', 65, 'right', '#,##0.00_);(#,##0.00)', $topHeaderStyle);
                    $report->addColumn('current', 'Current ($)', 65, 'right', '#,##0.00_);(#,##0.00)', $topHeaderStyle);

                } else {
                    if ($view->items['showleaseGurantee'] == 'Yes') {
                        if ($format == FILETYPE_PDF) {
                            $report->addColumn('tenantName', 'Tenant', 285, 'left', null, $topHeaderStyle);
                        } else {
                            $report->addColumn('tenantName', 'Tenant', 217, 'left', null, $topHeaderStyle);
                        }
                        $report->addColumn('leaseGuarantee1', 'Lease', 50, 'right', null, $topHeaderStyleRight);
                        $report->addColumn('leaseGuarantee2', 'Guarantee', 50, 'left', '#,##0.00_);(#,##0.00)', $topHeaderStyle);
                    } else {
                        $report->addColumn('tenantName', 'Tenant', 317, 'left', null, $topHeaderStyle);
                    }
                    $report->addColumn('total', 'Total ($)', 65, 'right', '#,##0.00_);(#,##0.00)', $topHeaderStyle);
                    $report->addColumn('current', 'Current ($)', 65, 'right', '#,##0.00_);(#,##0.00)', $topHeaderStyle);
                }

                switch ($view->items['frequency']) {
                    case 'M':
                        $report->addColumn('period1', '1 Month ($)', 65, 'right', '#,##0.00_);(#,##0.00)', $topHeaderStyle);
                        $report->addColumn('period2', '2 Months ($)', 65, 'right', '#,##0.00_);(#,##0.00)', $topHeaderStyle);
                        $report->addColumn('period3', '3 Months ($)', 65, 'right', '#,##0.00_);(#,##0.00)', $topHeaderStyle);
                        $report->addColumn('period4', '> 3 Months ($)', 65, 'right', '#,##0.00_);(#,##0.00)', $topHeaderStyle);
                        break;
                    case 'F':
                        $report->addColumn('period1', '7 days ($)', 65, 'right', '#,##0.00_);(#,##0.00)', $topHeaderStyle);
                        $report->addColumn('period2', '14 days ($)', 65, 'right', '#,##0.00_);(#,##0.00)', $topHeaderStyle);
                        $report->addColumn('period3', '21 days ($)', 65, 'right', '#,##0.00_);(#,##0.00)', $topHeaderStyle);
                        $report->addColumn('period4', '> 21 days ($)', 65, 'right', '#,##0.00_);(#,##0.00)', $topHeaderStyle);
                        break;
                    case 'D':
                        $report->addColumn('period1', '1 day ($)', 65, 'right', '#,##0.00_);(#,##0.00)', $topHeaderStyle);
                        $report->addColumn('period2', '2 days ($)', 65, 'right', '#,##0.00_);(#,##0.00)', $topHeaderStyle);
                        $report->addColumn('period3', '3 days ($)', 65, 'right', '#,##0.00_);(#,##0.00)', $topHeaderStyle);
                        $report->addColumn('period4', '> 3 days ($)', 65, 'right', '#,##0.00_);(#,##0.00)', $topHeaderStyle);
                        break;
                }
                $report->addSubHeaderItem('property', 0, 200, 'left');

            } else {

                if ($view->items['showArrearsWithDetails'] == 'Yes') {
                    $report->addColumn('transactionDate', 'Date', 40, 'left', null, $topHeaderStyle);
                    $report->addColumn('dueDate', 'Due Date', 40, 'center', null, $topHeaderStyle);
                    $report->addColumn('accountID', 'Account', 30, 'left', '@', $topHeaderStyle);
                    $report->addColumn('description', 'Description', 270, 'left', null, $topHeaderStyle);
                    $report->addColumn('transactionType', 'Type', 30, 'left', null, $topHeaderStyle);
                    $report->addColumn('invoiceNumber', 'Inv No', 30, 'left', null, $topHeaderStyle);
                    $report->addColumn('fromDate', 'From Date', 40, 'center', null, $topHeaderStyle);
                    $report->addColumn('toDate', 'To Date', 40, 'center', null, $topHeaderStyle);
                } else {
                    $report->addColumn('transactionDate', 'Description', 520, 'left', null, $topHeaderStyle);
                }

                $report->addColumn('amount', 'Original (' . $_SESSION['country_default']['currency_symbol'] . ')', 60, 'right', '#,##0.00_);(#,##0.00)', $topHeaderStyle);
                $report->addColumn('unpaidNetAmount', 'Net (' . $_SESSION['country_default']['currency_symbol'] . ')', 60, 'right', '#,##0.00_);(#,##0.00)', $topHeaderStyle);
                $report->addColumn('unpaidTaxAmount', 'Tax (' . $_SESSION['country_default']['currency_symbol'] . ')', 60, 'right', '#,##0.00_);(#,##0.00)', $topHeaderStyle);
                $report->addColumn('unpaidAmount', 'Gross (' . $_SESSION['country_default']['currency_symbol'] . ')', 60, 'right', '#,##0.00_);(#,##0.00)', $topHeaderStyle);
                $report->addSubHeaderItem('property', 0, 200, 'left');

            }
            $report->preparePage();

            $rep_data =  [];
            $count = 0;
            if ($view->items['showArrearsAged'] == 'Yes') {
                foreach ($data_ as $property) {
                    $prop['accountID'] = $property['name'];
                    $prop['tenantName'] = $property['name'];
                    $prop['leaseGuarantee1'] = 'Exp. Date';
                    $prop['leaseGuarantee2'] = 'Amount (' . $_SESSION['country_default']['currency_symbol'] . ')';
                    $prop['total'] = $property['total']['total'];
                    $prop['current'] = $property['total']['current'];
                    $prop['period1'] = $property['total']['period1'];
                    $prop['period2'] = $property['total']['period2'];
                    $prop['period3'] = $property['total']['period3'];
                    $prop['period4'] = $property['total']['period4'];
                    if ($format == FILETYPE_PDF) {
                        $prop['total'] =  toMoney($property['total']['total'], '');
                        $prop['current'] =  toMoney($property['total']['current'], '');
                        $prop['period1'] =  toMoney($property['total']['period1'], '');
                        $prop['period2'] =  toMoney($property['total']['period2'], '');
                        $prop['period3'] =  toMoney($property['total']['period3'], '');
                        $prop['period4'] =  toMoney($property['total']['period4'], '');
                    }

                    $grand_total['total'] += $property['total']['total'];
                    $grand_total['current'] += $property['total']['current'];
                    $grand_total['period1'] += $property['total']['period1'];
                    $grand_total['period2'] += $property['total']['period2'];
                    $grand_total['period3'] += $property['total']['period3'];
                    $grand_total['period4'] += $property['total']['period4'];

                    $prop['alignment']['leaseGuarantee1'] = 'center';
                    $prop['alignment']['leaseGuarantee2'] = 'right';
                    $prop['bold'] = true;
                    $prop['width']['accountID'] = 487;
                    $prop['width']['description'] = 487;
                    $prop['width']['tenantName'] = 487;
                    $prop['bgcolor'] = [142 / 255, 212 / 255, 255 / 255];
                    $prop['headerStyle'] =
                    [
                        'fill' => [
                            'type' => Fill::FILL_SOLID,
                            'color' => ['rgb' => '8ed4ff'],
                        ],
                        'font' =>  ['bold' => true, 'color' =>  ['rgb' => '000000']],
                        'alignment' =>  ['vertical' => Alignment::VERTICAL_CENTER,  'horizontal' => Alignment::HORIZONTAL_LEFT],
                    ];
                    $rep_data[] = $prop;
                    foreach ($property['data'] as $l) {
                        if (isset($l['leaseGurantee'])) {
                            $_lease['alignment']['leaseGuarantee1'] = 'center';
                            $_lease['alignment']['leaseGuarantee2'] = 'right';
                            $_lease['leaseGuarantee1'] = $l['leaseGurantee']['pmgu_expire_date'];
                            $_lease['leaseGuarantee2'] = $l['leaseGurantee']['pmgu_amount'];

                            if ($format == FILETYPE_PDF && $l['leaseGurantee']['pmgu_amount']) {
                                $_lease['leaseGuarantee2'] = toMoney($_lease['leaseGuarantee2']);
                            }
                        }

                        $_lease['accountID'] = $l['tenantName'];
                        $_lease['tenantName'] = $l['tenantName'];
                        $_lease['leaseID'] =  ($view->items['showArrearsWithDetails'] == 'Yes') ? $l['lease'] : $l['leaseID'];
                        $_lease['bold'] = true;
                        $_lease['width']['accountID'] = 400;
                        $_lease['width']['description'] = 400;
                        $_lease['width']['tenantName'] = 400;
                        $_lease['total'] =  ($view->items['showArrearsWithDetails'] == 'Yes') ? $l['data']['total'] : $l['total'];
                        $_lease['current'] =  ($view->items['showArrearsWithDetails'] == 'Yes') ? $l['data']['current'] : $l['current'];
                        $_lease['period1'] =  ($view->items['showArrearsWithDetails'] == 'Yes') ? $l['data']['period1'] : $l['period1'];
                        $_lease['period2'] = ($view->items['showArrearsWithDetails'] == 'Yes') ? $l['data']['period2'] : $l['period2'];
                        $_lease['period3'] = ($view->items['showArrearsWithDetails'] == 'Yes') ? $l['data']['period3'] : $l['period3'];
                        $_lease['period4'] =  ($view->items['showArrearsWithDetails'] == 'Yes') ? $l['data']['period4'] : $l['period4'];
                        if ($format == FILETYPE_PDF) {
                            $_lease['total'] =  ($view->items['showArrearsWithDetails'] == 'Yes') ? toMoney($l['data']['total'], '') : toMoney($l['total'], '');
                            $_lease['current'] =  ($view->items['showArrearsWithDetails'] == 'Yes') ? toMoney($l['data']['current'], '') : toMoney($l['current'], '');
                            $_lease['period1'] =  ($view->items['showArrearsWithDetails'] == 'Yes') ? toMoney($l['data']['period1'], '') : toMoney($l['period1'], '');
                            $_lease['period2'] =  ($view->items['showArrearsWithDetails'] == 'Yes') ? toMoney($l['data']['period2'], '') : toMoney($l['period2'], '');
                            $_lease['period3'] =  ($view->items['showArrearsWithDetails'] == 'Yes') ? toMoney($l['data']['period3'], '') : toMoney($l['period3'], '');
                            $_lease['period4'] =  ($view->items['showArrearsWithDetails'] == 'Yes') ? toMoney($l['data']['period4'], '') : toMoney($l['period4'], '');
                        }


                        // bg color for PDF
                        $_lease['bgcolor'] = [220 / 255, 242 / 255, 255 / 255];

                        // header style for excel
                        $_lease['headerStyle'] =
                        [
                            'fill' => [
                                'type' => Fill::FILL_SOLID,
                                'color' => ['rgb' => 'dcf2ff'],
                            ],
                            'font' =>  ['bold' => true, 'color' =>  ['rgb' => '000000']],
                            'alignment' =>  ['vertical' => Alignment::VERTICAL_CENTER,  'horizontal' => Alignment::HORIZONTAL_LEFT],
                        ];
                        if (isset($l['tenantName'])) {
                            $rep_data[] = $_lease;

                            if (! empty($l['leaseNotes'])) {
                                foreach ($l['leaseNotes'] as $note) {
                                    $leaseNote['transactionDate'] = $l['leaseName'];

                                    $leaseNote['leaseID'] = $l['leaseID'];
                                    $leaseNote['bold'] = false;
                                    $leaseNote['multiLine'] = true;
                                    $leaseNote['width']['accountID'] = 487;

                                    // bg color for PDF
                                    $leaseNote['bgcolor'] = [1, 1, 1];

                                    // header style for excel
                                    $leaseNote['headerStyle'] =
                                    [
                                        'fill' => [
                                            'color' => ['rgb' => 'e1e1e1'],
                                        ],
                                        'font' =>  ['bold' => true, 'color' =>  ['rgb' => '000000']],
                                        'alignment' =>  ['vertical' => Alignment::VERTICAL_CENTER,  'horizontal' => Alignment::HORIZONTAL_LEFT],
                                    ];

                                    $leaseNote['accountID'] = str_replace('ARREARS:', 'ARREARS      ' . $note['noteTimestamp'] . ' by ' . $note['noteUser'], $note['noteDescription']);
                                    $rep_data[] = $leaseNote;
                                }
                            }

                            if ($view->items['showArrearsWithDetails'] == 'Yes') {
                                foreach ($l['leaseData'] as $y) {

                                    if ($format == FILETYPE_PDF) {
                                        $y['total'] = toMoney($y['total'], '');
                                        $y['current'] = toMoney($y['current'], '');
                                        $y['period1'] = toMoney($y['period1'], '');
                                        $y['period2'] = toMoney($y['period2'], '');
                                        $y['period3'] = toMoney($y['period3'], '');
                                        $y['period4'] = toMoney($y['period4'], '');
                                    }

                                    $y['bold'] = false;
                                    $rep_data[] = $y;
                                }
                            }
                        }
                    }
                    //                    $totalRow['accountID'] = $l['tenantName'] .' total';
                    //                    $totalRow['tenantName'] = $l['tenantName'] .' total';
                    //                    $totalRow['amount'] = $l['totalRow']['amount'];
                    //                    $totalRow['unpaidNetAmount'] = $l['totalRow']['unpaidNetAmount'];
                    //                    $totalRow['unpaidTaxAmount'] = $l['totalRow']['unpaidTaxAmount'];
                    //                    $totalRow['unpaidAmount'] = $l['totalRow']['unpaidAmount'];
                    //
                    //
                }
            } else {

                foreach ($data as $property) {
                    $prop['transactionDate'] = $property['name'];
                    $prop['amount'] = $property['amount'];
                    $prop['unpaidNetAmount'] = $property['unpaidNetAmount'];
                    $prop['unpaidTaxAmount'] = $property['unpaidTaxAmount'];
                    $prop['unpaidAmount'] = $property['unpaidAmount'];

                    if ($format == FILETYPE_PDF) {
                        $prop['amount'] = toMoney($property['amount'], '');
                        $prop['unpaidNetAmount'] = toMoney($property['unpaidNetAmount'], '');
                        $prop['unpaidTaxAmount'] = toMoney($property['unpaidTaxAmount'], '');
                        $prop['unpaidAmount'] = toMoney($property['unpaidAmount'], '');
                    }


                    $prop['bold'] = true;
                    $prop['width']['transactionDate'] = 487;
                    $prop['bgcolor'] = [142 / 255, 212 / 255, 255 / 255];
                    $prop['headerStyle'] =
                    [
                        'fill' => [
                            'type' => Fill::FILL_SOLID,
                            'color' => ['rgb' => '8ed4ff'],
                        ],
                        'font' =>  ['bold' => true, 'color' =>  ['rgb' => '000000']],
                        'alignment' =>  ['vertical' => Alignment::VERTICAL_CENTER,  'horizontal' => Alignment::HORIZONTAL_LEFT],
                    ];
                    $rep_data[] = $prop;

                    foreach ($property['lease'] as $l) {
                        $_lease['transactionDate'] = $l['leaseName'];

                        $_lease['leaseID'] = $l['leaseID'];
                        $_lease['bold'] = true;
                        $_lease['width']['transactionDate'] = 487;
                        if ($view->items['showArrearsWithDetails'] == 'No') {
                            $_lease['amount'] = $l['totalRow']['amount'];
                            $_lease['unpaidNetAmount'] = $l['totalRow']['unpaidNetAmount'];
                            $_lease['unpaidTaxAmount'] = $l['totalRow']['unpaidTaxAmount'];
                            $_lease['unpaidAmount'] = $l['totalRow']['unpaidAmount'];

                            if ($format == FILETYPE_PDF) {
                                $_lease['amount'] = toDecimal($_lease['amount']);
                                $_lease['unpaidNetAmount'] = toDecimal($_lease['unpaidNetAmount']);
                                $_lease['unpaidTaxAmount'] = toDecimal($_lease['unpaidTaxAmount']);
                                $_lease['unpaidAmount'] = toDecimal($_lease['unpaidAmount']);
                            }
                        }
                        // bg color for PDF
                        $_lease['bgcolor'] = [220 / 255, 242 / 255, 255 / 255];

                        // header style for excel
                        $_lease['headerStyle'] =
                            [
                                'fill' => [
                                    'type' => Fill::FILL_SOLID,
                                    'color' => ['rgb' => 'dcf2ff'],
                                ],
                                'font' =>  ['bold' => true, 'color' =>  ['rgb' => '000000']],
                                'alignment' =>  ['vertical' => Alignment::VERTICAL_CENTER,  'horizontal' => Alignment::HORIZONTAL_LEFT],
                            ];
                        $rep_data[] = $_lease;

                        if (! empty($l['leaseNotes'])) {
                            $_lease['transactionDate'] = $l['leaseName'];

                            $_lease['leaseID'] = $l['leaseID'];
                            $_lease['bold'] = false;
                            $_lease['multiLine'] = true;
                            $_lease['width']['transactionDate'] = 487;

                            // bg color for PDF
                            $_lease['bgcolor'] = [1, 1, 1];

                            // header style for excel
                            $_lease['headerStyle'] =
                            [
                                'fill' => [
                                    'color' => ['rgb' => 'e1e1e1'],
                                ],
                                'font' =>  ['bold' => true, 'color' =>  ['rgb' => '000000']],
                                'alignment' =>  ['vertical' => Alignment::VERTICAL_CENTER,  'horizontal' => Alignment::HORIZONTAL_LEFT],
                            ];

                            foreach ($l['leaseNotes'] as $note) {
                                $_lease['transactionDate'] = str_replace('ARREARS:', 'ARREARS      ' . $note['noteTimestamp'] . ' by ' . $note['noteUser'], $note['noteDescription']);
                                $rep_data[] = $_lease;
                            }
                        }
                        if ($view->items['showArrearsWithDetails'] == 'Yes') {
                            foreach ($l['data'] as $y) {

                                if ($format == FILETYPE_PDF) {
                                    $y['amount'] = toMoney($y['amount'], '');
                                    $y['unpaidAmount'] = toMoney($y['unpaidAmount'], '');
                                    $y['unpaidNetAmount'] = toMoney($y['unpaidNetAmount'], '');
                                    $y['unpaidTaxAmount'] = toMoney($y['unpaidTaxAmount'], '');
                                }

                                $y['bold'] = false;
                                $rep_data[] = $y;
                            }

                            $totalRow['transactionDate'] = $l['leaseName'] . ' total';
                            $totalRow['amount'] = $l['totalRow']['amount'];
                            $totalRow['unpaidNetAmount'] = $l['totalRow']['unpaidNetAmount'];
                            $totalRow['unpaidTaxAmount'] = $l['totalRow']['unpaidTaxAmount'];
                            $totalRow['unpaidAmount'] = $l['totalRow']['unpaidAmount'];

                            if ($format == FILETYPE_PDF) {
                                $totalRow['amount'] = toDecimal($totalRow['amount']);
                                $totalRow['unpaidNetAmount'] = toDecimal($totalRow['unpaidNetAmount']);
                                $totalRow['unpaidTaxAmount'] = toDecimal($totalRow['unpaidTaxAmount']);
                                $totalRow['unpaidAmount'] = toDecimal($totalRow['unpaidAmount']);
                            }

                            $totalRow['bold'] = true;
                            $totalRow['width']['transactionDate'] = 487;
                            $totalRow['bgcolor'] = [0.9, 0.9, 0.9];

                            // header style for excel
                            $totalRow['headerStyle'] =
                            [
                                'fill' => [
                                    'type' => Fill::FILL_SOLID,
                                    'color' => ['rgb' => 'e1e1e1'],
                                ],
                                'font' =>  ['bold' => true, 'color' =>  ['rgb' => '000000']],
                                'alignment' =>  ['vertical' => Alignment::VERTICAL_CENTER,  'horizontal' => Alignment::HORIZONTAL_LEFT],
                            ];

                            $rep_data[] = $totalRow;
                            $rep_data[] = ['transactionDate' => ''];
                        }
                    }
                }

            }
            $report->renderHeader();
            // $report->renderData ($data);

            $leaseTotal = [];
            $leaseCount = 0;

            foreach ($rep_data as $row) {
                // $report->renderLineFill($row);
                $report->renderLine_custom($row);
            }


            $grand_total['width']['accountID'] = 487;
            $grand_total['width']['transactionDate'] = 487;
            if ($format == FILETYPE_PDF) {
                if ($view->items['showArrearsAged'] != 'Yes') {
                    $grand_total['amount'] = toMoney($grand_total['amount'], '');
                    $grand_total['unpaidNetAmount'] = toMoney($grand_total['unpaidNetAmount'], '');
                    $grand_total['unpaidTaxAmount'] = toMoney($grand_total['unpaidTaxAmount'], '');
                    $grand_total['unpaidAmount'] = toMoney($grand_total['unpaidAmount'], '');
                } else {
                    $grand_total['accountID'] = $grand_total['transactionDate'];
                    $grand_total['total'] = toMoney($grand_total['total'], '');
                    $grand_total['current'] = toMoney($grand_total['current'], '');
                    $grand_total['period1'] = toMoney($grand_total['period1'], '');
                    $grand_total['period2'] = toMoney($grand_total['period2'], '');
                    $grand_total['period3'] = toMoney($grand_total['period3'], '');
                    $grand_total['period4'] = toMoney($grand_total['period4'], '');
                }

                $report->renderSubTotal($grand_total, 1);
            } else {
                $report->renderTotal($grand_total, 'Grand Total');
            }

            $report->clean();
            $report->close();
        }

        if ($context['forOwnerReport']) {
            $context['ownerReportFile'] = $filePath;
        }

        // -- if it s a scheduled task - attach the report and email to the requester
        if ($context[IS_TASK] && ! $context['forOwnerReport']) {
            $email = new EmailTemplate('views/emails/basicEmail.html', SYSTEMURL);
            $attachment =  [];
            $attachment[0]['file'] = $filePath;
            $attachment[0]['content_type'] = 'application/pdf';
            sendMail($_SESSION['email'], $_SESSION['first_name'] . ' ' . $_SESSION['last_name'], $email->toString(), 'Arrears Report', $attachment);
            logData('Emailed arrears report report to ' . $_SESSION['email']);
            $context[TASK_COMPLETE] = true;
        }
    }

    if (! $context['forOwnerReport'] || ($context['forOwnerReport'] && $format == 'xlsx')) {
        if ($propertyCount > THRESHOLD_DEBTORSREPORT) {
            $view->items['statusMessage'] = 'Due to its complexity, this report has been scheduled and will be emailed to you on completion!';
            $view->render();
        } else {
            $view->items['downloadPath'] = $downloadPath;

            if ($context['forOwnerReport']) {
                renderDownloadLink($downloadPath);
            } else {
                $view->render();
            }
        }
    }
}

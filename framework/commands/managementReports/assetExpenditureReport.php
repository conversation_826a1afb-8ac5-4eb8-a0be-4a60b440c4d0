<?php

function assetExpenditureReport(&$context)
{
    global $clientDirectory, $sess, $pathPrefix;
    if (isUserType(USER_TRACC)) {
        $view = new MasterPage(userViews(), '/managementReports/assetExpenditureReport.html', 'views_vuejs');
    } else {
        $view = new MasterPage(userViews(), '/managementReports/assetExpenditureReport.html', 'views_vuejs');
    }
    $view->setSection($context['module']);

    $view->bindAttributesFrom($_REQUEST);
    $view->items['url'] = c8_api . 'administrator/management-reports/asset-expenditure-report';

    $view->render();
}

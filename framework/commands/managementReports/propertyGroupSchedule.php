<?php

function propertyGroupSchedule(&$context)
{
    global $clientDirectory, $pathPrefix;

    if (! isPostback()) {
        $view = new MasterPage(userViews(), '/managementReports/propertyGroupSchedule.html');
        $view->setSection($context['module']);
    } else {
        $view = new UserControl(userViews(), '/managementReports/propertyGroupSchedule.html');
    }

    $view->bindAttributesFrom($_REQUEST);
    $view->items['validationErrors'] = [];

    $view->items['report_type_list'] = ['default' => 'Property Group Schedule', 'dot' => 'PIMS Project Level'];
    if (! isset($view->items['report_type'])) {
        $view->items['report_type'] = 'dot';
    }

    $view->items['propGroupList'] = get_prop_groups();
    if (! isset($view->items['reportingDate'])) {
        $view->items['reportingDate'] = TODAY;
    }
    // -- get the master calendar entries for that calendar
    $calendarName = 'Financial';
    $dateList = dbGetMasterCalendarDates($calendarName);
    $view->items['dateList'] = array_reverse($dateList);
    $view->items['dateFromList'] = array_reverse($dateList);
    if (! isset($view->items['startDate'])) {
        $view->items['startDate'] = date('01/m/Y', strtotime('last month'));
    }
    if (! isset($view->items['endDate'])) {
        $view->items['endDate'] =  date('t/m/Y', strtotime('last month'));
    }

    if ($view->items['startDate']) {
        $fromDateList = array_reverse(dbGetMasterCalendarFromDates($calendarName, $view->items['startDate']));
        $view->items['dateFromList'] = $fromDateList;
        if ($view->items['action'] == 'selectDate') {
            $view->items['endDate'] = $fromDateList[count($fromDateList ?? []) - 1]['endDate'];
        }
    }

    if (isset($view->items['propGroupID'])) {
        $view->items['propGroupID'] = deserializeParameters($view->items['propGroupID']);
    }
    switch ($view->items['action']) {
        case 'finalise':
            $command = 'propertyGroupScheduleProcess';
            if ($view->items['report_type'] === 'default') {
                if (! isValid($view->items['reportingDate'], TEXT_SMARTDATE, false)) {
                    $validationErrors[] = 'Please enter a reporting date.';
                }

            } else {

                if (! isValid($view->items['startDate'], TEXT_SMARTDATE, false)) {
                    $validationErrors[] = 'Please enter a report from date.';
                }

                if (! isValid($view->items['endDate'], TEXT_SMARTDATE, false)) {
                    $validationErrors[] = 'Please enter a report to date.';
                }

                $command = 'propertyGroupDotReportProcess';
            }

            if (empty($view->items['propGroupID'])) {
                $validationErrors[] = 'You must choose a property group.';
            }

            if (noErrors($validationErrors)) {
                executeCommand($command, 'managementReports');
            }
            break;
    }
    $view->items['validationErrors'] = $validationErrors;
    $view->render();
}

<?php

function executiveReportProcess(&$context)
{

    global $pathPrefix, $clientDirectory;

    if (! isPostback()) {
        $view = new MasterPage(userViews(), '/managementReports/executiveReportProcess.html');
        $view->setSection($context['module']);
    } else {
        $view = new UserControl(userViews(), '/managementReports/executiveReportProcess.html');
    }

    $validationErrors =  [];

    if ($context[IS_TASK]) {
        $view->bindAttributesFrom($context);
        extract($context);
    } else {
        $view->bindAttributesFrom($_REQUEST);

    }

    $selectedReports = [];
    $selectedReport = false;

    $year = $view->items['year'];
    $period = $view->items['period'];
    $view->items['linked_id'] = deserializeParameters($view->items['linked_id']);
    $calendarPeriod = dbGetMasterCalendarForPeriod('FINANCIAL', $period, $year);
    $currentFirstPeriod = convertDate($calendarPeriod['endDate']);
    $db = dbGetLinkedDatabase();
    if (count($db ?? []) == 1) {
        $view->items['linked_id'] = [$db[0]['database_name']];
    }

    if ($view->items['reportsCSS']) {
        if (! $view->items['subReport']) {
            $view->items['subReport'] = '4';
        }
        $view->items['dataMgtFees'] = getFeesData($db, $currentFirstPeriod, $view->items['subReport'], $view->items['linked_id'], $year);
        $view->items['subReports'] =
        [
            '4' => 'Properties',
            '1' => 'Active Occupied Units',
            '2' => 'Number Of Owners',
            '3' => 'Management Fees By Primary Owner',
            '5' => 'Settlements by Sales Agent',
            '6' => 'Property Changes for period',
            '7' => 'Management Fees By Company Group',
        ];
    } else {
        if ($view->items['subReport']) {
            $selectedReport = true;
        }
        if ($view->items['properties'] == 'properties') {
            if (! $selectedReport) {
                $view->items['subReport'] = '4';
            }
            $selectedReports[4] = 'Properties';
        }

        if ($view->items['activeOccupiedUnits'] == 'activeOccupiedUnits') {
            if (! $selectedReport) {
                $view->items['subReport'] = '1';
            }
            $selectedReports[1] = 'Active Occupied Units';
        }

        if ($view->items['numberOfOwners'] == 'numberOfOwners') {
            if (! $selectedReport) {
                $view->items['subReport'] = '2';
            }
            $selectedReports[2] = 'Number Of Owners';
        }

        if ($view->items['mngtFeesPrimaryOwner'] == 'mngtFeesPrimaryOwner') {
            if (! $selectedReport) {
                $view->items['subReport'] = '3';
            }
            $selectedReports[3] = 'Management Fees By Primary Owner';
        }

        if ($view->items['settlementByAgent'] == 'settlementByAgent') {
            if (! $selectedReport) {
                $view->items['subReport'] = '5';
            }
            $selectedReports[5] = 'Settlements by Sales Agent';
        }

        if ($view->items['propertyChangesForPeriod'] == 'propertyChangesForPeriod') {
            if (! $selectedReport) {
                $view->items['subReport'] = '6';
            }
            $selectedReports[6] = 'Property Changes for period';
        }

        if ($view->items['mngtFeesCompanyGroup'] == 'mngtFeesCompanyGroup') {
            if (! $selectedReport) {
                $view->items['subReport'] = '7';
            }
            $selectedReports[7] = 'Management Fees By Company Group';
        }

        $view->items['subReports'] = $selectedReports;
        $view->items['dataMgtFees'] = getFeesData($db, $currentFirstPeriod, $view->items['subReport'], $view->items['linked_id'], $year);
    }


    $format = $view->items['format'];
    $logoFile = dbGetClientLogo();
    $logoPath = "/assets/clientLogos/{$logoFile}";

    $_filePath = "{$pathPrefix}{$clientDirectory}/{$format}/" . DOC_AGENCYFEES . '/';
    $_downloadPath = "{$clientDirectory}/{$format}/" . DOC_AGENCYFEES;

    $file = 'executiveReport_' . date('YmdHis') . ".{$format}";
    $filePath = $_filePath . $file;
    $downloadPath = "{$_downloadPath}/{$file}";
    $indexSheet = 0;
    if ($view->items['format'] != FILETYPE_SCREEN) {
        if ($view->items['format'] != FILETYPE_PDF) {
            $report = new XLSDataReport($filePath, 'Executive Report');
            $report->enableFormatting = true;
        } else {
            $report = ($context[DOC_MASTER]) ? new PDFDataReport($context[DOC_MASTER], $logoPath, A4_LANDSCAPE) : new PDFDataReport($filePath, $logoPath, A4_LANDSCAPE);
        }
        $report->multiLine = true;
        $report->printRowLines = true;
        $report->printColumnLines = false;
        $report->printBorders = false;
        $report->cache = false;
        $header = new ReportHeader('Executive Report');
        $header->xPos = $report->hMargin;
        $header->yPos = $report->pageHeight - $report->vMargin;
        if ($format == FILETYPE_PDF) {
            $report->attachObject('header', $header);
            $footer = new TraccFooter(null, 'Executive Report', $report->pageSize);
            $report->attachObject('footer', $footer);
        }

        $year = $view->items['year'];
        $period = $view->items['period'];
        $calendarPeriod = dbGetMasterCalendarForPeriod('FINANCIAL', $period, $year);
        $currentFirstPeriod = convertDate($calendarPeriod['startDate']);
        $db = dbGetLinkedDatabase();

        // Properties
        if ($view->items['reportsCSS'] || $view->items['properties']) {
            if ($view->items['format'] == FILETYPE_XLS) {
                $report->setSheetDetails($indexSheet, 'Properties');
                $report->line = 1;
                $indexSheet++;
            }
            $header->subTitle = 'Properties ( ' . date('M Y', strtotime($currentFirstPeriod . ' -11 month')) . ' - ' . date('M Y', strtotime($currentFirstPeriod)) . ' ) ';
            $report->resetColumns();
            $report->addColumn('manager', 'Portfolio', 160, 'left', null);

            for ($x = 0; $x <= 11; $x++) {
                $month = date('M', strtotime($currentFirstPeriod . "-{$x} months"));
                $monthYear = date('Y', strtotime($currentFirstPeriod . "-{$x} months"));
                $report->addColumn('month' . $x, $month . ' ' . $monthYear, 45, 'right', '#,##0_);(#,##0)');
            }
            $report->preparePage();
            $report->renderHeader();

            $data = getFeesData($db, $currentFirstPeriod, 4, $view->items['linked_id']);
            $total = [];
            foreach ($data as $manager => $row) {
                $report->renderLine_custom($row);
                for ($x = 0; $x <= 11; $x++) {
                    $total["month{$x}"][] = $row["month{$x}"];
                }
            }

            $row['total'] = 'Total';
            for ($x = 0; $x <= 11; $x++) {
                $row['month' . $x] = isset($total["month{$x}"]) ? array_sum($total["month{$x}"]) : 0;
            }
            $row['manager'] = 'Total';
            if ($view->items['format'] != FILETYPE_PDF) {
                $report->renderTotal($row);
            } else {
                $report->renderSubTotal($row);
            }

            $report->clean();
            $report->endPage();
        }

        // Active Occupied Units
        if ($view->items['reportsCSS'] || $view->items['activeOccupiedUnits']) {
            if ($view->items['format'] == FILETYPE_XLS) {
                $report->setSheetDetails($indexSheet, 'Active Occupied Units');
                $report->line = 1;
                $indexSheet++;
            }

            $header->subTitle = 'Active Occupied Units ( ' . date('M Y', strtotime($currentFirstPeriod . ' -11 month')) . ' - ' . date('M Y', strtotime($currentFirstPeriod)) . ' ) ';
            $report->resetColumns();
            $report->addColumn('manager', 'Portfolio', 160, 'left', null);

            for ($x = 0; $x <= 11; $x++) {
                $month = date('M', strtotime($currentFirstPeriod . "-{$x} months"));
                $monthYear = date('Y', strtotime($currentFirstPeriod . "-{$x} months"));
                $report->addColumn('month' . $x, $month . ' ' . $monthYear, 45, 'right', '#,##0_);(#,##0)');
            }
            $report->preparePage();
            $report->renderHeader();

            $data = getFeesData($db, $currentFirstPeriod, 1, $view->items['linked_id']);
            $total = [];
            foreach ($data as $manager => $row) {
                $report->renderLine_custom($row);
                for ($x = 0; $x <= 11; $x++) {
                    $total["month{$x}"][] = $row["month{$x}"];
                }
            }

            $row['total'] = 'Total';
            for ($x = 0; $x <= 11; $x++) {
                $row['month' . $x] = isset($total["month{$x}"]) ? array_sum($total["month{$x}"]) : 0;
            }
            $row['manager'] = 'Total';
            if ($view->items['format'] != FILETYPE_PDF) {
                $report->renderTotal($row);
            } else {
                $report->renderSubTotal($row);
            }

            $report->clean();
            $report->endPage();
        }

        // Number Of Owners
        if ($view->items['reportsCSS'] || $view->items['numberOfOwners']) {
            if ($view->items['format'] == FILETYPE_XLS) {
                $report->setSheetDetails($indexSheet, 'Number Of Owners');
                $report->line = 1;
                $indexSheet++;
            }

            $header->subTitle = 'Number Of Owners ( ' . date('M Y', strtotime($currentFirstPeriod . ' -11 month')) . ' - ' . date('M Y', strtotime($currentFirstPeriod)) . ' ) ';
            $report->resetColumns();
            $report->addColumn('manager', 'Portfolio', 160, 'left', null);

            for ($x = 0; $x <= 11; $x++) {
                $month = date('M', strtotime($currentFirstPeriod . "-{$x} months"));
                $monthYear = date('Y', strtotime($currentFirstPeriod . "-{$x} months"));
                $report->addColumn('month' . $x, $month . ' ' . $monthYear, 45, 'right', '#,##0_);(#,##0)');
            }
            $report->preparePage();
            $report->renderHeader();

            $data = getFeesData($db, $currentFirstPeriod, 2, $view->items['linked_id']);
            $total = [];
            foreach ($data as $manager => $row) {
                $report->renderLine_custom($row);
            }

            $ownerTotal = dbGetCountOwnersTotal($db, $currentFirstPeriod, $view->items['linked_id']);
            foreach ($ownerTotal as $manager => $row) {
                for ($x = 0; $x <= 11; $x++) {
                    $total["month{$x}"][] = $row["month{$x}"];
                }
            }

            $row['total'] = 'Total';
            for ($x = 0; $x <= 11; $x++) {
                $row['month' . $x] = isset($total["month{$x}"]) ? array_sum($total["month{$x}"]) : 0;
            }

            $view->items['ownerTotal'] = $row;

            if ($view->items['format'] != FILETYPE_PDF) {
                $report->renderTotal($row);
            } else {
                $report->renderSubTotal($row);
            }


            $report->clean();
            $report->endPage();
        }

        // MANAGEMENT FEES BY
        if ($view->items['reportsCSS'] || $view->items['mngtFeesPrimaryOwner']) {
            if ($view->items['format'] == FILETYPE_XLS) {
                $report->setSheetDetails($indexSheet, 'Mgt Fees By Primary Owner');
                $report->line = 1;
                $indexSheet++;
            }
            $header->subTitle = 'Management Fees By Primary Owner - ' . $view->items['year'];
            $report->resetColumns();
            $report->addColumn('label', 'Primary Owner', 160, 'left', null);
            $report->addColumn('julyAmount', 'July', 45, 'right', '#,##0.00_);(#,##0.00)');
            $report->addColumn('augustAmount', 'August', 45, 'right', '#,##0.00_);(#,##0.00)');
            $report->addColumn('septemberAmount', 'September', 45, 'right', '#,##0.00_);(#,##0.00)');
            $report->addColumn('octoberAmount', 'October', 45, 'right', '#,##0.00_);(#,##0.00)');
            $report->addColumn('novemberAmount', 'November', 45, 'right', '#,##0.00_);(#,##0.00)');
            $report->addColumn('decemberAmount', 'December', 45, 'right', '#,##0.00_);(#,##0.00)');
            $report->addColumn('januaryAmount', 'January', 45, 'right', '#,##0.00_);(#,##0.00)');
            $report->addColumn('februaryAmount', 'February', 45, 'right', '#,##0.00_);(#,##0.00)');
            $report->addColumn('marchAmount', 'March', 45, 'right', '#,##0.00_);(#,##0.00)');
            $report->addColumn('aprilAmount', 'April', 45, 'right', '#,##0.00_);(#,##0.00)');
            $report->addColumn('mayAmount', 'May', 45, 'right', '#,##0.00_);(#,##0.00)');
            $report->addColumn('juneAmount', 'June', 45, 'right', '#,##0.00_);(#,##0.00)');
            $report->addColumn('totalAmount', 'YTD', 45, 'right', '#,##0.00_);(#,##0.00)');
            $report->preparePage();
            $report->renderHeader();

            $feesData = getFeesData($db, $currentFirstPeriod, 3, $view->items['linked_id'], $year);
            $feesDataTotal = [];
            foreach ($feesData as $row) {
                $feesDataTotal['julyAmount'] += $row['julyAmount'];
                $feesDataTotal['augustAmount'] += $row['augustAmount'];
                $feesDataTotal['septemberAmount'] += $row['septemberAmount'];
                $feesDataTotal['octoberAmount'] += $row['octoberAmount'];
                $feesDataTotal['novemberAmount'] += $row['novemberAmount'];
                $feesDataTotal['decemberAmount'] += $row['decemberAmount'];
                $feesDataTotal['januaryAmount'] += $row['januaryAmount'];
                $feesDataTotal['februaryAmount'] += $row['februaryAmount'];
                $feesDataTotal['marchAmount'] += $row['marchAmount'];
                $feesDataTotal['aprilAmount'] += $row['aprilAmount'];
                $feesDataTotal['mayAmount'] += $row['mayAmount'];
                $feesDataTotal['juneAmount'] += $row['juneAmount'];
                $feesDataTotal['totalAmount'] += $row['totalAmount'];
                if ($view->items['format'] == FILETYPE_PDF) {
                    $row['julyAmount'] = toMoney($row['julyAmount'], '');
                    $row['augustAmount'] = toMoney($row['augustAmount'], '');
                    $row['septemberAmount'] = toMoney($row['septemberAmount'], '');
                    $row['octoberAmount'] = toMoney($row['octoberAmount'], '');
                    $row['novemberAmount'] = toMoney($row['novemberAmount'], '');
                    $row['decemberAmount'] = toMoney($row['decemberAmount'], '');
                    $row['januaryAmount'] = toMoney($row['januaryAmount'], '');
                    $row['februaryAmount'] = toMoney($row['februaryAmount'], '');
                    $row['marchAmount'] = toMoney($row['marchAmount'], '');
                    $row['aprilAmount'] = toMoney($row['aprilAmount'], '');
                    $row['mayAmount'] = toMoney($row['mayAmount'], '');
                    $row['juneAmount'] = toMoney($row['juneAmount'], '');
                    $row['totalAmount'] = toMoney($row['totalAmount'], '');
                }
                $report->renderLine_custom($row);
            }
            if ($view->items['format'] == FILETYPE_PDF) {
                $feesDataTotal['label'] = 'Total';
                $feesDataTotal['julyAmount'] = toMoney($feesDataTotal['julyAmount'], '');
                $feesDataTotal['augustAmount'] = toMoney($feesDataTotal['augustAmount'], '');
                $feesDataTotal['septemberAmount'] = toMoney($feesDataTotal['septemberAmount'], '');
                $feesDataTotal['octoberAmount'] = toMoney($feesDataTotal['octoberAmount'], '');
                $feesDataTotal['novemberAmount'] = toMoney($feesDataTotal['novemberAmount'], '');
                $feesDataTotal['decemberAmount'] = toMoney($feesDataTotal['decemberAmount'], '');
                $feesDataTotal['januaryAmount'] = toMoney($feesDataTotal['januaryAmount'], '');
                $feesDataTotal['februaryAmount'] = toMoney($feesDataTotal['februaryAmount'], '');
                $feesDataTotal['marchAmount'] = toMoney($feesDataTotal['marchAmount'], '');
                $feesDataTotal['aprilAmount'] = toMoney($feesDataTotal['aprilAmount'], '');
                $feesDataTotal['mayAmount'] = toMoney($feesDataTotal['mayAmount'], '');
                $feesDataTotal['juneAmount'] = toMoney($feesDataTotal['juneAmount'], '');
                $feesDataTotal['totalAmount'] = toMoney($feesDataTotal['totalAmount'], '');
                $report->renderSubTotal($feesDataTotal);
            } else {
                $report->renderTotal($feesDataTotal);
            }
            $report->clean();
            $report->endPage();
        }

        // MANAGEMETN FEES BY COMPANY GROUP
        if ($view->items['reportsCSS'] || $view->items['mngtFeesCompanyGroup']) {
            if ($view->items['format'] == FILETYPE_XLS) {
                $report->setSheetDetails($indexSheet, 'Mgt Fees By Company Group');
                $report->line = 1;
                $indexSheet++;
            }
            $header->subTitle = 'Management Fees By Company Group - ' . $view->items['year'];
            $report->resetColumns();
            $report->addColumn('label', 'Company Group', 160, 'left', null);
            $report->addColumn('julyAmount', 'July', 45, 'right', '#,##0.00_);(#,##0.00)');
            $report->addColumn('augustAmount', 'August', 45, 'right', '#,##0.00_);(#,##0.00)');
            $report->addColumn('septemberAmount', 'September', 45, 'right', '#,##0.00_);(#,##0.00)');
            $report->addColumn('octoberAmount', 'October', 45, 'right', '#,##0.00_);(#,##0.00)');
            $report->addColumn('novemberAmount', 'November', 45, 'right', '#,##0.00_);(#,##0.00)');
            $report->addColumn('decemberAmount', 'December', 45, 'right', '#,##0.00_);(#,##0.00)');
            $report->addColumn('januaryAmount', 'January', 45, 'right', '#,##0.00_);(#,##0.00)');
            $report->addColumn('februaryAmount', 'February', 45, 'right', '#,##0.00_);(#,##0.00)');
            $report->addColumn('marchAmount', 'March', 45, 'right', '#,##0.00_);(#,##0.00)');
            $report->addColumn('aprilAmount', 'April', 45, 'right', '#,##0.00_);(#,##0.00)');
            $report->addColumn('mayAmount', 'May', 45, 'right', '#,##0.00_);(#,##0.00)');
            $report->addColumn('juneAmount', 'June', 45, 'right', '#,##0.00_);(#,##0.00)');
            $report->addColumn('totalAmount', 'YTD', 45, 'right', '#,##0.00_);(#,##0.00)');
            $report->preparePage();
            $report->renderHeader();

            $feesData = getFeesData($db, $currentFirstPeriod, 7, $view->items['linked_id'], $year);
            $feesDataTotal = [];
            $isOwnerGroup = false;
            foreach ($feesData as $row) {
                $feesDataTotal['julyAmount'] += $row['julyAmount'];
                $feesDataTotal['augustAmount'] += $row['augustAmount'];
                $feesDataTotal['septemberAmount'] += $row['septemberAmount'];
                $feesDataTotal['octoberAmount'] += $row['octoberAmount'];
                $feesDataTotal['novemberAmount'] += $row['novemberAmount'];
                $feesDataTotal['decemberAmount'] += $row['decemberAmount'];
                $feesDataTotal['januaryAmount'] += $row['januaryAmount'];
                $feesDataTotal['februaryAmount'] += $row['februaryAmount'];
                $feesDataTotal['marchAmount'] += $row['marchAmount'];
                $feesDataTotal['aprilAmount'] += $row['aprilAmount'];
                $feesDataTotal['mayAmount'] += $row['mayAmount'];
                $feesDataTotal['juneAmount'] += $row['juneAmount'];
                $feesDataTotal['totalAmount'] += $row['totalAmount'];
                if ($view->items['format'] == FILETYPE_PDF) {
                    $row['julyAmount'] = toMoney($row['julyAmount'], '');
                    $row['augustAmount'] = toMoney($row['augustAmount'], '');
                    $row['septemberAmount'] = toMoney($row['septemberAmount'], '');
                    $row['octoberAmount'] = toMoney($row['octoberAmount'], '');
                    $row['novemberAmount'] = toMoney($row['novemberAmount'], '');
                    $row['decemberAmount'] = toMoney($row['decemberAmount'], '');
                    $row['januaryAmount'] = toMoney($row['januaryAmount'], '');
                    $row['februaryAmount'] = toMoney($row['februaryAmount'], '');
                    $row['marchAmount'] = toMoney($row['marchAmount'], '');
                    $row['aprilAmount'] = toMoney($row['aprilAmount'], '');
                    $row['mayAmount'] = toMoney($row['mayAmount'], '');
                    $row['juneAmount'] = toMoney($row['juneAmount'], '');
                    $row['totalAmount'] = toMoney($row['totalAmount'], '');
                }
                $report->renderLine_custom($row);

                if (! $isOwnerGroup && $row['isCompanyGroup'] == 1) {
                    $isOwnerGroup = true;
                }
            }
            if ($view->items['format'] == FILETYPE_PDF) {
                $feesDataTotal['label'] = 'Total';
                $feesDataTotal['julyAmount'] = toMoney($feesDataTotal['julyAmount'], '');
                $feesDataTotal['augustAmount'] = toMoney($feesDataTotal['augustAmount'], '');
                $feesDataTotal['septemberAmount'] = toMoney($feesDataTotal['septemberAmount'], '');
                $feesDataTotal['octoberAmount'] = toMoney($feesDataTotal['octoberAmount'], '');
                $feesDataTotal['novemberAmount'] = toMoney($feesDataTotal['novemberAmount'], '');
                $feesDataTotal['decemberAmount'] = toMoney($feesDataTotal['decemberAmount'], '');
                $feesDataTotal['januaryAmount'] = toMoney($feesDataTotal['januaryAmount'], '');
                $feesDataTotal['februaryAmount'] = toMoney($feesDataTotal['februaryAmount'], '');
                $feesDataTotal['marchAmount'] = toMoney($feesDataTotal['marchAmount'], '');
                $feesDataTotal['aprilAmount'] = toMoney($feesDataTotal['aprilAmount'], '');
                $feesDataTotal['mayAmount'] = toMoney($feesDataTotal['mayAmount'], '');
                $feesDataTotal['juneAmount'] = toMoney($feesDataTotal['juneAmount'], '');
                $feesDataTotal['totalAmount'] = toMoney($feesDataTotal['totalAmount'], '');
                $report->renderSubTotal($feesDataTotal);
            } else {
                $report->renderTotal($feesDataTotal);
            }

            if ($isOwnerGroup) {
                $z['label'] = '* Owner Group';
                $z['italic'] = true;
                $z['headerStyle'] =
                [
                    'font' => ['italic' => true],
                ];
                $report->renderLine_custom($z);
            }

            $report->clean();
            $report->endPage();
        }

        // Settlement Report
        if ($view->items['reportsCSS'] || $view->items['settlementByAgent']) {
            if ($view->items['format'] == FILETYPE_XLS) {
                $report->setSheetDetails($indexSheet, 'Settlements by Sales Agent');
                $report->line = 1;
                $indexSheet++;
            }

            $header->subTitle = 'Settlements by Sales Agent - ' . date('M Y', strtotime($currentFirstPeriod));
            $report->resetColumns();
            $report->addColumn('label', 'Portfolio', 200, 'left', null);
            $report->addColumn('askingPrice', 'Asking Price', 100, 'right', '#,##0.00_);(#,##0.00)');
            $report->addColumn('salesPrice', 'Sales Price', 100, 'right', '#,##0.00_);(#,##0.00)');
            $report->addColumn('settlementDate', 'Settlement Date', 100, 'right', null);
            $report->addColumn('commissionFees', 'Commission / Fees', 100, 'right', '#,##0.00_);(#,##0.00)');

            $report->preparePage();
            $report->renderHeader();

            $data = getFeesData($db, $currentFirstPeriod, 5, $view->items['linked_id']);
            $total = [];

            $report->addSubHeaderItem('title', 0, 200, 'left');
            $agent = '';
            $grandArray = [];
            $subArray = [];
            foreach ($data as $row) {

                if ($agent != $row['pmzz_desc']) {

                    if ($agent) {
                        $subArray['label'] = 'Sub-Total';
                        if ($view->items['format'] == FILETYPE_PDF) {
                            $subArray['askingPrice'] = toMoney(array_sum($subArray['askingPrice']));
                            $subArray['salesPrice'] = toMoney(array_sum($subArray['salesPrice']));
                            $subArray['commissionFees'] = toMoney(array_sum($subArray['commissionFees']));
                            $report->renderSubTotal($subArray);
                        } else {
                            $subArray['askingPrice'] = (array_sum($subArray['askingPrice']));
                            $subArray['salesPrice'] = (array_sum($subArray['salesPrice']));
                            $subArray['commissionFees'] = (array_sum($subArray['commissionFees']));
                            $report->renderTotal($subArray, 'Sub-Total');
                        }
                        $subArray = [];
                    }

                    $report->setSubHeaderValue('title', $row['pmzz_desc']);
                    $report->renderSubHeader();
                    $agent = $row['pmzz_desc'];
                }

                $subArray['askingPrice'][] = ($row['askingPrice']);
                $subArray['salesPrice'][] = ($row['salesPrice']);
                $subArray['commissionFees'][] = ($row['commissionFees']);

                $grandArray['askingPrice'][] = ($row['askingPrice']);
                $grandArray['salesPrice'][] = ($row['salesPrice']);
                $grandArray['commissionFees'][] = ($row['commissionFees']);

                if ($view->items['format'] == FILETYPE_PDF) {
                    $row['askingPrice'] = toMoney($row['askingPrice']);
                    $row['salesPrice'] = toMoney($row['salesPrice']);
                    $row['commissionFees'] = toMoney($row['commissionFees']);
                }


                $report->renderLine($row);

            }

            $subArray['label'] = 'Sub-Total';
            if ($view->items['format'] == FILETYPE_PDF) {
                $subArray['askingPrice'] = toMoney(array_sum($subArray['askingPrice']));
                $subArray['salesPrice'] = toMoney(array_sum($subArray['salesPrice']));
                $subArray['commissionFees'] = toMoney(array_sum($subArray['commissionFees']));
                $report->renderSubTotal($subArray);
            } else {
                $subArray['askingPrice'] = (array_sum($subArray['askingPrice']));
                $subArray['salesPrice'] = (array_sum($subArray['salesPrice']));
                $subArray['commissionFees'] = (array_sum($subArray['commissionFees']));
                $report->renderTotal($subArray, 'Sub-Total');
            }


            $grandArray['label'] = 'Agency Total';
            if ($view->items['format'] == FILETYPE_PDF) {
                $grandArray['askingPrice'] = toMoney(array_sum($grandArray['askingPrice']));
                $grandArray['salesPrice'] = toMoney(array_sum($grandArray['salesPrice']));
                $grandArray['commissionFees'] = toMoney(array_sum($grandArray['commissionFees']));
                $report->renderSubTotal($grandArray);
            } else {
                $grandArray['askingPrice'] = (array_sum($grandArray['askingPrice']));
                $grandArray['salesPrice'] = (array_sum($grandArray['salesPrice']));
                $grandArray['commissionFees'] = (array_sum($grandArray['commissionFees']));
                $report->renderTotal($grandArray, 'Agency Total');
            }

            $report->clean();
            $report->endPage();
        }

        // Property Change Report
        if ($view->items['reportsCSS'] || $view->items['propertyChangesForPeriod']) {
            if ($view->items['format'] == FILETYPE_XLS) {
                $report->setSheetDetails($indexSheet, 'Property Changes for period');
                $report->line = 1;
                $indexSheet++;
            }

            $header->subTitle = 'Property Changes for period - ' . date('M Y', strtotime($currentFirstPeriod));
            $report->resetColumns();
            $report->addColumn('manager', ucwords(strtolower($_SESSION['country_default']['portfolio_manager'])), 200, 'left', null);
            $report->addColumn('current_year', 'Properties at Start of Reporting Period', 150, 'right', '#,##0_);(#,##0)');
            $report->addColumn('property_gain', 'Properties gained', 150, 'right', '#,##0_);(#,##0)');
            $report->addColumn('property_loses', 'Properties lost', 150, 'right', '#,##0_);(#,##0)');
            $report->addColumn('total', 'Total Properties', 150, 'right', '#,##0_);(#,##0)');

            $report->preparePage();
            $report->renderHeader();

            $data = getFeesData($db, $currentFirstPeriod, 6, $view->items['linked_id']);
            $total = [];

            $report->addSubHeaderItem('title', 0, 200, 'left');
            $subArray = [];
            foreach ($data as $row) {



                $row['total'] = $row['current_year'] + $row['property_gain'] - $row['property_loses'];
                $grandArray['current_year'][] = ($row['current_year']);
                $grandArray['property_gain'][] = ($row['property_gain']);
                $grandArray['property_loses'][] = ($row['property_loses']);
                $grandArray['total'][] = ($row['total']);
                $report->renderLine($row);

            }

            $grandArray['label'] = 'Agency Total';
            $grandArray['current_year'] = (array_sum($grandArray['current_year']));
            $grandArray['property_gain'] = (array_sum($grandArray['property_gain']));
            $grandArray['property_loses'] = (array_sum($grandArray['property_loses']));
            $grandArray['total'] = (array_sum($grandArray['total']));
            if ($view->items['format'] == FILETYPE_PDF) {
                $report->renderSubTotal($grandArray);
            } else {
                $report->renderTotal($grandArray, 'Agency Total');
            }

            $report->clean();
            $report->endPage();
        }


        if ($view->items['format'] == FILETYPE_XLS) {
            $report->deleteSheetByName('Executive Report');
        }
        $report->clean();
        $report->close();
    } elseif ($view->items['numberOfOwners']) {

        $ownerTotal = dbGetCountOwnersTotal($db, $currentFirstPeriod, $view->items['linked_id']);
        foreach ($ownerTotal as $manager => $row) {
            for ($x = 0; $x <= 11; $x++) {
                $total["month{$x}"][] = $row["month{$x}"];
            }
        }

        $row['total'] = 'Total';
        for ($x = 0; $x <= 11; $x++) {
            $row['month' . $x] = isset($total["month{$x}"]) ? array_sum($total["month{$x}"]) : 0;
        }

        $view->items['ownerTotal'] = $row;
    }
    $view->items['data'] = $data;
    $view->items['currentFirstPeriod'] = $currentFirstPeriod;
    $view->items['downloadPath'] = $downloadPath;
    $view->render();
}

function getFeesData($db, $currentFirstPeriod, $subReport, $linked = null, $year = null)
{

    switch ($subReport) {
        case '4':
            return dbGetCountPropertiesTotal($db, $currentFirstPeriod, $linked);
            break;
        case '1':
            return dbGetCountActiveUnitsTotal($db, $currentFirstPeriod, $linked);
            break;
        case '2':
            return dbGetCountNumberOfOwnersTotal($db, $currentFirstPeriod, $linked);
            break;
        case '3':
            return dbGetMngtFeesPrimaryOwner($db, $year, $linked);
        case '5':
            return dbGetSettlementByAgent($db, $currentFirstPeriod, $linked);
        case '6':
            return dbGetPropertyChangePeriod($db, $currentFirstPeriod, $linked);
            break;
        case '7':
            return dbGetMngtFeesCompanyGroup($db, $year, $linked);
            break;
    }
}

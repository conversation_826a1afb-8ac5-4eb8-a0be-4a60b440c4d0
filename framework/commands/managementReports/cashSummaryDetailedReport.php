<?php

include_once 'functions/page2DebtorsDetailFunctions.php';
include_once 'Residential/residentialReportFunctions.php';
include_once 'functions/page3DetailedExpenditureFunctions.php';

// initialise variables
$line = 40;
$startline = $line;
$pdf->begin_page_ext(595, 842, '');
page_headerSummary('new', $propertyID);
$line = -120;

$vacant_units =  [];
$tempunits =  [];
$t = 0;
$tenantcod =  [];
$nepokazyvat = 0;
$bftotalC = 0;
$gstfreetotalC = 0;
$billedThistotalC = 0;
$gsttotalC = 0;
$paidgsttotalC = 0;
$paidThisMonthtotalC = 0;
$paidgstfreetotalC = 0;
$closing_balancetotalC = 0;
$unallocThisMonth = 0;

$intotable = "temp_$timestamp";

// $units_result = getUnitDetails ($propertyID);
$units_result = getUnitDetails($propertyID, $periodFrom, $periodTo);
$units_number = count($units_result);

$rentPaidToCheck = false;
if (isset($view->items['rentPaidTo'])) {
    $rentPaidToCheck = true;
}

foreach ($units_result as $key => $thisRow) {
    $noprint = 0;
    $unit_code = $thisRow['pmpu_unit'];
    $unitdesc = $thisRow['pmpu_desc'];
    $tenantCode = $thisRow['pmua_lease'];
    $tenantnameFull = $thisRow['tenant_name'];
    $tenantname = limit_name($tenantnameFull, 50);
    $debtor_code = $thisRow['debtor_code'];
    $status = $thisRow['status'];

    $noprint = in_array($tenantCode, $tempunits);
    $tempunits[] = $tenantCode;

    // Get Charge Dates

    $stopChargeDateRaw =  getStopChargeDate($propertyID, $unit_code, $tenantCode);
    $stop_charge_date = toNormalDate($stopChargeDateRaw);

    [$now_day, $now_month, $now_year] = explode('/', $periodTo);
    $nownumber = $now_year . $now_month . $now_day;

    if (strlen($stop_charge_date) > 0) {
        [$stop_cd_day, $stop_cd_month, $stop_cd_year] = explode('/', $stop_charge_date);
        $stopnumber = $stop_cd_year . $stop_cd_month . $stop_cd_day;
    } else {
        $stopnumber = 0;
    }

    if ($status == 'L' && $nownumber >= $stopnumber) { // made this change on 9 Apr 09 specifically for Tony)
        $vacated = ($stop_charge_date) ? "(VACATED - $stop_charge_date)" : '';
    } else {
        $vacated = '';
    }

    $notempty = false;
    $tenantcodeset = false;

    // initialise the totals
    $bftotal = 0;
    $gstfreetotal = 0;
    $billedThistotal = 0;
    $gsttotal = 0;
    $paidgsttotal  = 0;
    $paidThisMonthtotal = 0;
    $paidgstfreetotal = 0;
    $closing_balancetotal = 0;

    // initialise the other values
    $bf = 0;
    $gstfree = 0;
    $billedThis = 0;
    $gst = 0;
    $paidgstfree = 0;
    $paidThisMonth = 0;
    $paidgst = 0;
    $closing_balance = 0;

    $cash =  [];
    $paid =  [];
    $billed =  [];
    $acc =  [];

    // ####################################################################

    $result = getBilledAccCode($tenantCode, $propertyID, $periodTo);
    $billedCodes = getAccCodes($result);

    $paid_result = getPaidAccCode($tenantCode, $propertyID, $periodTo);
    $paidCodes = getAccCodes($paid_result);

    $cash_result = getUnallocatedCashAccCode($tenantCode, $propertyID, $periodTo);
    $unallocCashCodes = getAccCodes($cash_result);

    $merged = array_merge($paidCodes, $billedCodes, $unallocCashCodes);
    $merged = array_unique($merged);
    asort($merged);
    if ($rentPaidToCheck && count($merged) == 0) {
        $merged[] = '0101';
    }

    $mynum = 0;
    $z = 0;
    $my =  [];

    // ###################################################################

    // ########################################################################################################
    // ########################################################################################################
    // ####################################	  START		 ####################################################
    // #################################### START ACCOUNTS ####################################################
    // ########################################################################################################
    // ########################################################################################################

    foreach ($merged as $account) {
        $baseRentPaidToFlag = false;
        $account_name = getAccountName($account);

        // if ($rentPaidToCheck && strtoupper ($account_name) == 'BASE RENT') $baseRentPaidToFlag = true;
        if ($rentPaidToCheck && getAccountChargeType($propertyID, $tenantCode, $account) == CHARGE_RENT) {
            $baseRentPaidToFlag = true;
        }

        // set past flag to true to get a total of all previous periods
        $billed =  getBilled($account, $propertyID, $tenantCode, $periodFrom, $periodTo, true);
        // set past flag to false to get a total of current period
        $billedThis = getBilled($account, $propertyID, $tenantCode, $periodFrom, $periodTo, false);

        $paid =  getPaid($account, $propertyID, $tenantCode, $periodFrom);

        // ##########UNALLOCATED CASH#############################################################

        // set past flag to true to get a total of all previous periods
        $cash =  getUnallocCash($account, $propertyID, $tenantCode, $periodFrom, $periodTo, $past = true);
        // set past flag to false to get a total of current period
        $cashThis =  getUnallocCash($account, $propertyID, $tenantCode, $periodFrom, $periodTo, false);

        // cashThis and cash2 are the same
        $cash2 =  $cashThis;

        // ########################################################################################

        // billed this month GST free set flag to true
        $gstfree = getBilledThisMonthAll($propertyID, $tenantCode, $periodFrom, $periodTo, $account, true);
        // Note Billed this month with GST (flag false) not used currently

        // GST Only
        $gst = getBilledGST($propertyID, $tenantCode, $periodFrom, $periodTo, $account);

        // ########################################################################################

        $paidgstfree = getRecievedThisMonthAll($propertyID, $tenantCode, $periodFrom, $periodTo, $account, true);

        $paidThisMonth = getRecievedThisMonthAll($propertyID, $tenantCode, $periodFrom, $periodTo, $account, false);

        $unallocThisMonth = getUnallocThisMonthAll($propertyID, $tenantCode, $periodFrom, $periodTo, $account);

        $paidgst = getGSTReceived($propertyID, $tenantCode, $periodFrom, $periodTo, $account);

        // #########################END CALCULATIONS#############################################

        // brought forward
        $bf 			= $billed + $paid + $cash;
        $balance 		= formatting($bf, 2);
        $bf_display 		= $account;
        // echo "<br />THIS >>> $billedThis = (billedThis)$billedThis - (gst)$gst + (gstfree)$gstfree";
        $billedThis 		= $billedThis - $gst + $gstfree;
        $paidgstfree 		= $paidgstfree + $cash2;


        $paidThisMonth 		= $paidThisMonth + $paidgstfree - $unallocThisMonth;

        $gstfree_display 	= formatting($gstfree, 2);
        $billed_display 	= formatting($billedThis, 2);
        $gst_display 		= formatting($gst, 2);


        $paidgstfree 		= $paidgstfree * (-1);
        $paidThisMonth 		= $paidThisMonth * (-1);
        // print_r($tenantCode);print_r($paidgst);
        $paidgst 		= ($paidgst + $unallocThisMonth) * (-1);

        $paidgstfree_display 	= formatting($paidgstfree, 2);
        $paidThisMonth_display 	= formatting($paidThisMonth, 2);
        $paidgst_display 	= formatting($paidgst, 2);
        $totalPaid_display = formatting($paidgst + $paidThisMonth, 2);
        $closing_balance 	= $bf  + $billedThis + $gst - $paidThisMonth - $paidgst; // - $paidgstfree;
        $closing_balance_display = trim(formatting($closing_balance, 2));

        // TOTALS

        $bftotal 		= $bftotal + $bf;
        $gstfreetotal 		= $gstfreetotal + $gstfree;
        $billedThistotal	= $billedThistotal + $billedThis;
        $gsttotal 		= $gsttotal + $gst;
        $paidgsttotal 		= $paidgsttotal + $paidgst;
        $paidThisMonthtotal 	= $paidThisMonthtotal + $paidThisMonth;
        $paidgstfreetotal 	= $paidgstfreetotal + $paidgstfree;
        $closing_balancetotal 	= $closing_balancetotal + $closing_balance;

        // TOTALS COLLECTIONS

        $bftotalC = $bftotalC + $bf;
        $gstfreetotalC = $gstfreetotalC + $gstfree;
        $billedThistotalC = $billedThistotalC + $billedThis;
        $gsttotalC = $gsttotalC + $gst;
        $paidgsttotalC = $paidgsttotalC + $paidgst;
        $paidThisMonthtotalC = $paidThisMonthtotalC + $paidThisMonth;
        $paidgstfreetotalC = $paidgstfreetotalC + $paidgstfree;
        $closing_balancetotalC = $closing_balancetotalC + $closing_balance;


        // ########################################PDF #######################################

        if ($line >= 410) {
            $pdf->setlinewidth(0.5);
            $pdf->moveto(220, 695 - $startline);
            $pdf->lineto(220, 40);
            $pdf->stroke();

            $pdf->moveto(270, 695 - $startline);
            $pdf->lineto(270, 40);
            $pdf->stroke();

            $pdf->moveto(520, 695 - $startline);
            $pdf->lineto(520, 40);
            $pdf->stroke();

            $pdf->moveto(370, 695 - $startline);
            $pdf->lineto(370, 40);
            $pdf->stroke();
            $pdf->moveto(570, 710 - $startline);
            $pdf->lineto(570, 40);
            $pdf->stroke();
            $pdf->moveto(25, 40);
            $pdf->lineto(25, 710 - $startline);
            $pdf->stroke();

            $pdf->moveto(25, 40);
            $pdf->lineto(570, 40);
            $pdf->stroke();

            // insert tracc header
            $traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'cirrus8CashSummary', A4_PORTRAIT);
            $traccFooter->prerender($pdf);

            $line = 40;
            $startline = $line;
            $pdf->end_page_ext('');

            $pdf->begin_page_ext(595, 842, '');
            page_headerSummary('cont', $propertyID);

            $line = -120;


            if ($tenantcodeset == true) {
                $pdf->setFontExt($_fonts['Helvetica'], 7);
                $pdf->showBoxed("$tenantname (...)", 30, 460 - $line, 370, 20, 'left', '');
            }
        }

        // ##########################################################################################################
        // ##########################################################################################################
        // ##########################################################################################################
        // ##########################################################################################################
        // ##########################################################################################################
        // ################################ ACCOUNTS WITH BALANCES GREATER THEN ZERO#################################
        // ##########################################################################################################
        // ##########################################################################################################
        // ##########################################################################################################
        // ##########################################################################################################

        $_balanceCheck = ($balance == '0.00' && $billed_display == '0.00' && $gst_display == '0.00' && $paidThisMonth_display == '0.00' && $paidgst_display == '0.00' && $closing_balance_display == '0.00');

        // -- note that this is a hack to allow base rent to pass through - dependant on base rent being coded 0101. Better solution would be to flag an account code in the system as 'base rent' then do a lookup

        if ((! $_balanceCheck) || ($rentPaidToCheck && ($account == '0101') && ($status == 'C'))) {
            $unitcod_with_bal = $unit_code;
            $_hasBalance = true;
            $notempty = true;

            if (! $tenantcodeset) {
                $pdf->setFontExt($_fonts['Helvetica-Bold'], 7);
                $pdf->showBoxed("$unitdesc $vacated", 30, 470 - $line, 370, 20, 'left', '');
                $pdf->showBoxed("$tenantname", 30, 460 - $line, 470, 20, 'left', '');
                $tenantcodeset = true;
            }

            if ($baseRentPaidToFlag) {
                $baseRentPaidTo = '';
                $baseRentPaidTo = dbGetPaidTo($propertyID, $tenantCode, $debtor_code, $periodTo);
                if ($baseRentPaidTo) {
                    $baseRentPaidTo = " (paid to $baseRentPaidTo)";
                } else {
                    $baseRentPaidTo = ' (paid to N/A)';
                }
            }

            $pdf->setFontExt($_fonts['Helvetica'], 7);
            // $pdf->showBoxed ($bf_display, 22, 450-$line, 70, 20, "left", '');
            $accName = ucfirst(strtolower($account_name));
            if ($baseRentPaidToFlag) {
                $accNameStr = $accName . $baseRentPaidTo;
            } else {
                $accNameStr = $accName;
            }

            $pdf->showBoxed($accNameStr, 30, 450 - $line, 270, 20, 'left', '');
            $pdf->save();
            $pdf->setColorExt('fill', 'rgb', 1, 1, 1, 0);
            $pdf->rect(250, 450 - $line, 70, 18);
            $pdf->fill();
            $pdf->restore();
            $pdf->showBoxed($balance, 220, 450 - $line, 48, 20, 'right', '');
            $pdf->showBoxed($billed_display, 270, 450 - $line, 48, 20, 'right', '');
            $pdf->showBoxed($gst_display, 320, 450 - $line, 48, 20, 'right', '');
            $pdf->showBoxed($paidThisMonth_display, 370, 450 - $line, 48, 20, 'right', '');
            $pdf->showBoxed($paidgst_display, 420, 450 - $line, 48, 20, 'right', '');
            $pdf->showBoxed($totalPaid_display, 470, 450 - $line, 48, 20, 'right', '');
            $pdf->showBoxed($closing_balance_display, 520, 450 - $line, 48, 20, 'right', '');

            $line = $line + 10;
        } else {
            $_hasBalance = false;
        }

    } // end FOR EACH account

    if ($line >= 410) {
        $pdf->setlinewidth(0.5);
        $pdf->moveto(220, 695 - $startline);
        $pdf->lineto(220, 40);
        $pdf->stroke();

        $pdf->moveto(270, 695 - $startline);
        $pdf->lineto(270, 40);
        $pdf->stroke();

        $pdf->moveto(520, 695 - $startline);
        $pdf->lineto(520, 40);
        $pdf->stroke();

        $pdf->moveto(370, 695 - $startline);
        $pdf->lineto(370, 40);
        $pdf->stroke();
        $pdf->moveto(570, 710 - $startline);
        $pdf->lineto(570, 40);
        $pdf->stroke();
        $pdf->moveto(25, 40);
        $pdf->lineto(25, 710 - $startline);
        $pdf->stroke();

        $pdf->moveto(25, 40);
        $pdf->lineto(570, 40);
        $pdf->stroke();

        // insert tracc header
        $traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'cirrus8CashSummary', A4_PORTRAIT);
        $traccFooter->prerender($pdf);

        $line = 40;
        $startline = $line;
        $pdf->end_page_ext('');

        $pdf->begin_page_ext(595, 842, '');
        page_headerSummary('cont', $propertyID);

        $line = -120;


        if ($tenantcodeset == true) {
            $pdf->setFontExt($_fonts['Helvetica'], 7);
            $pdf->showBoxed("$tenantname (...)", 30, 460 - $line, 370, 20, 'left', '');
        }
    }

    // #################################### END ACCOUNTS ####################################################

    $bftotal_display = formatting($bftotal, 2);
    $gstfreetotal_display = formatting($gstfreetotal, 2);
    $billedThistotal_display = formatting($billedThistotal, 2);
    $gsttotal_display = formatting($gsttotal, 2);
    $paidgsttotal_display = formatting($paidgsttotal, 2);
    $paidThisMonthtotal_display = formatting($paidThisMonthtotal, 2);
    $paidtotal_display = formatting($paidgsttotal + $paidThisMonthtotal, 2);
    $paidgstfreetotal_display = formatting($paidgstfreetotal, 2);
    $closing_balancetotal_display = trim(formatting($closing_balancetotal, 2));

    $unit_av_desc = getUnitDescription($propertyID, $unit_code);

    // ##### ACCOUNTS WITH ALL BALANCES EQUAL TO ZERO #####
    /*
        ||
            (
                $bftotal_display == '0.00' &&
                $billedThistotal_display == '0.00' &&
                $gsttotal_display == '0.00' &&
                $paidThisMonthtotal_display == '0.00' &&
                $paidgsttotal_display == '0.00' &&
                $closing_balancetotal_display == '0.00' &&
                $notempty == false
            )
    */
    if (
        (
            (
                $bftotal_display == '0.00' &&
                $billedThistotal_display == '0.00' &&
                $gsttotal_display == '0.00' &&
                $paidThisMonthtotal_display == '0.00' &&
                $paidgsttotal_display == '0.00' &&
                $closing_balancetotal_display == '0.00' &&
                $notempty == false
            )
        )
    ) {
        // ##################################################################################################
        // ##################################################################################################
        // ################################ CHECK IF UNIT IS VACATED FOR THHE CURRENT PERIOD#################
        // ##################################################################################################

        $checkunit_result = checkUnitVacated($propertyID, $unit_code, $periodTo);
        $checkunit_number = count($checkunit_result ?? []);

        if ($checkunit_number > 0) {
            $unitcod_withOut_bal = $unit_code;

            // ###############check if this is the latest vacated date for this unit#######################
            // ###############check if this is the latest vacated date for this unit#######################
            // ###############check if this is the latest vacated date for this unit#######################

            $latestVacDate_result = checkLatestVacatedDate($propertyID, $unit_code);
            if (count($latestVacDate_result) > 0) {
                $row = $latestVacDate_result[0];
                $maxdate = $row['pmua_to_dt'];
                // $latestVacDate_number = count ($latestVacDate_result);
            } else {
                $maxdate = '';
            }

            // echo "<br><br> MAX DATE: $maxdate and stop charge date = $stop_charge_date<br />";

            if (($maxdate == $stop_charge_date) || ($tenantCode == 'VACANT' && $view->items['zeroReceipting'])) {// Andrew add latter section about Vacant on 28 Nov 08 when Vacant units were not appearing on the owners statement

                $unit_codetaken = $unit_code;
                // $vacated = '';	//-- cleari
                $pdf->setFontExt($_fonts['Helvetica-Bold'], 7);
                $pdf->showBoxed("$unitdesc $vacated", 30, 470 - $line, 370, 20, 'left', '');
                // $pdf->showBoxed ("$tenantname", 50, 460-$line, 470, 20, "left", '');
                $pdf->showBoxed('Vacant', 30, 460 - $line, 470, 20, 'left', '');

                $line = $line - 7;
                $pdf->setColorExt('fill', 'rgb', 0.88, 0.88, 0.88, 0);
                $pdf->rect(220, 445 - $line, 350, 15);
                $pdf->fill();
                $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);


                $pdf->setlinewidth(0.5);
                $pdf->moveto(220, 460 - $line);
                $pdf->lineto(570, 460 - $line);
                $pdf->stroke();
                $pdf->setFontExt($_fonts['Helvetica-Bold'], 7);
                // $pdf->showBoxed ('TOTAL', 22, 438-$line, 170, 20, "left", '');
                $pdf->showBoxed($bftotal_display, 220, 438 - $line, 48, 20, 'right', '');
                $pdf->showBoxed($billedThistotal_display, 270, 438 - $line, 48, 20, 'right', '');
                $pdf->showBoxed($gsttotal_display, 320, 438 - $line, 48, 20, 'right', '');
                $pdf->showBoxed($paidThisMonthtotal_display, 370, 438 - $line, 48, 20, 'right', '');
                $pdf->showBoxed($paidgsttotal_display, 420, 438 - $line, 48, 20, 'right', '');
                $pdf->showBoxed($paidtotal_display, 470, 438 - $line, 48, 20, 'right', '');
                $pdf->showBoxed($closing_balancetotal_display, 520, 438 - $line, 48, 20, 'right', '');

                $pdf->moveto(220, 445 - $line);
                $pdf->lineto(570, 445 - $line);
                $pdf->stroke();
                $line = $line + 45;
            }

        } elseif ($unitcod_withOut_bal != $unit_code) {
            // $unitcod_with_bal == $unit_code &&

            // else if ($unitcod_with_bal == $unit_code && $unitcod_withOut_bal != $unit_code)
            // ###############this section deals with any left over units not taken care by above code###########
            // #########################in this case units with a ZERO balance but are STILL occupied###########

            $checkunit_result2 = checkOtherUnits($propertyID, $unit_code, $tenantCode, $periodTo);
            $checkunit_number2 = count($checkunit_result2 ?? []);

            if ($checkunit_result2) {

                $ucto = $unit_code;

                $pdf->setFontExt($_fonts['Helvetica-Bold'], 7);

                $pdf->showBoxed("$unitdesc", 30, 470 - $line, 370, 20, 'left', '');
                $pdf->showBoxed("$tenantname", 30, 460 - $line, 470, 20, 'left', '');

                $line = $line - 7;
                $pdf->setColorExt('fill', 'rgb', 0.88, 0.88, 0.88, 0);
                $pdf->rect(220, 445 - $line, 350, 15);
                $pdf->fill();
                $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);


                $pdf->setlinewidth(0.5);
                $pdf->moveto(220, 460 - $line);
                $pdf->lineto(570, 460 - $line);
                $pdf->stroke();
                $pdf->setFontExt($_fonts['Helvetica-Bold'], 7);
                // $pdf->showBoxed ('TOTAL', 22, 438-$line, 170, 20, "left", '');
                $pdf->showBoxed($bftotal_display, 220, 438 - $line, 48, 20, 'right', '');
                $pdf->showBoxed($billedThistotal_display, 270, 438 - $line, 48, 20, 'right', '');
                // $pdf->showBoxed ($gstfreetotal_display, 227, 438-$line, 170, 20, "right", '');
                $pdf->showBoxed($gsttotal_display, 320, 438 - $line, 48, 20, 'right', '');
                $pdf->showBoxed($paidThisMonthtotal_display, 370, 438 - $line, 48, 20, 'right', '');
                // $pdf->showBoxed ($paidgstfreetotal_display, 450, 438-$line, 170, 20, "right", '');
                $pdf->showBoxed($paidgsttotal_display, 420, 438 - $line, 48, 20, 'right', '');
                $pdf->showBoxed($paidtotal_display, 470, 438 - $line, 48, 20, 'right', '');
                $pdf->showBoxed($closing_balancetotal_display, 520, 438 - $line, 48, 20, 'right', '');


                $pdf->moveto(220, 445 - $line);
                $pdf->lineto(570, 445 - $line);
                $pdf->stroke();
                $line = $line + 45;
            }
        }
    } else {
        $line = $line - 7;
        $pdf->setColorExt('fill', 'rgb', 0.88, 0.88, 0.88, 0);
        $pdf->rect(220, 445 - $line, 350, 15);
        $pdf->fill();
        $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);


        $pdf->setlinewidth(0.5);
        $pdf->moveto(220, 460 - $line);
        $pdf->lineto(570, 460 - $line);
        $pdf->stroke();
        $pdf->setFontExt($_fonts['Helvetica-Bold'], 7);
        // $pdf->showBoxed ('TOTAL', 22, 438-$line, 170, 20, "left", '');
        $pdf->showBoxed($bftotal_display, 220, 438 - $line, 48, 20, 'right', '');
        $pdf->showBoxed($billedThistotal_display, 270, 438 - $line, 48, 20, 'right', '');
        // $pdf->showBoxed ($gstfreetotal_display, 227, 438-$line, 170, 20, "right", '');
        $pdf->showBoxed($gsttotal_display, 320, 438 - $line, 48, 20, 'right', '');
        $pdf->showBoxed($paidThisMonthtotal_display, 370, 438 - $line, 48, 20, 'right', '');
        // $pdf->showBoxed ($paidgstfreetotal_display, 450, 438-$line, 170, 20, "right", '');
        $pdf->showBoxed($paidgsttotal_display, 420, 438 - $line, 48, 20, 'right', '');
        $pdf->showBoxed($paidtotal_display, 470, 438 - $line, 48, 20, 'right', '');
        $pdf->showBoxed($closing_balancetotal_display, 520, 438 - $line, 48, 20, 'right', '');


        $pdf->moveto(220, 445 - $line);
        $pdf->lineto(570, 445 - $line);
        $pdf->stroke();
        $line = $line + 45;

    } // end else


    $t++;

} // end for each

// #############################################TOTALS COLLECTIONS DISPLAY FIGURES###################################
// #############################################TOTALS COLLECTIONS DISPLAY FIGURES###################################
// #############################################TOTALS COLLECTIONS DISPLAY FIGURES###################################
// #############################################TOTALS COLLECTIONS DISPLAY FIGURES###################################
// #############################################TOTALS COLLECTIONS DISPLAY FIGURES###################################
// #############################################TOTALS COLLECTIONS DISPLAY FIGURES###################################




$bftotalC_display = formatting($bftotalC, 2);
$gstfreetotalC_display = formatting($gstfreetotalC, 2);
$billedThistotalC_display = formatting($billedThistotalC, 2);
$gsttotalC_display = formatting($gsttotalC, 2);
$paidgsttotalC_display = formatting($paidgsttotalC, 2);
$paidThisMonthtotalC_display = formatting($paidThisMonthtotalC, 2);
$paidtotalC_display = formatting($paidgsttotalC + $paidThisMonthtotalC, 2);
$paidgstfreetotalC_display = formatting($paidgstfreetotalC, 2);
$closing_balancetotalC_display = formatting($closing_balancetotalC, 2);


if ($line >= 410) {


    // $line = $line + 5;
    $pdf->setlinewidth(0.5);
    $pdf->moveto(220, 695 - $startline);
    $pdf->lineto(220, 40);
    $pdf->stroke();

    $pdf->moveto(270, 695 - $startline);
    $pdf->lineto(270, 40);
    $pdf->stroke();

    $pdf->moveto(520, 695 - $startline);
    $pdf->lineto(520, 40);
    $pdf->stroke();

    $pdf->moveto(370, 695 - $startline);
    $pdf->lineto(370, 40);
    $pdf->stroke();
    $pdf->moveto(570, 710 - $startline);
    $pdf->lineto(570, 40);
    $pdf->stroke();
    $pdf->moveto(25, 40);      // tonkaja levaja
    $pdf->lineto(25, 710 - $startline);
    $pdf->stroke();
    $pdf->moveto(25, 40);      // tonkaja levaja
    $pdf->lineto(570, 40);
    $pdf->stroke();

    $line = 40;
    $startline = $line;

    $traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'cirrus8CashSummary', A4_PORTRAIT);
    $traccFooter->prerender($pdf);

    $pdf->end_page_ext('');

    $pdf->begin_page_ext(595, 842, '');
    page_headerSummary('cont', $propertyID);

    $line = -120;


}



$line = $line - 30;
$pdf->setColorExt('fill', 'rgb', 0.88, 0.88, 0.88, 0);
$pdf->rect(220, 430 - $line, 350, 16);
$pdf->fill();
$pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
$pdf->setlinewidth(0.5);
// total line
$pdf->moveto(220, 446 - $line);
$pdf->lineto(570, 446 - $line);
$pdf->stroke();

$pdf->setFontExt($_fonts['Helvetica-Bold'], 7);
$pdf->showBoxed('Total', 30, 423 - $line, 170, 20, 'left', '');
$pdf->showBoxed($bftotalC_display, 220, 423 - $line, 48, 20, 'right', '');
$pdf->showBoxed($billedThistotalC_display, 270, 423 - $line, 48, 20, 'right', '');
$pdf->showBoxed($gsttotalC_display, 320, 423 - $line, 48, 20, 'right', '');
$pdf->showBoxed($paidThisMonthtotalC_display, 370, 423 - $line, 48, 20, 'right', '');
$pdf->showBoxed($paidgsttotalC_display, 420, 423 - $line, 48, 20, 'right', '');
$pdf->showBoxed($paidtotalC_display, 470, 423 - $line, 48, 20, 'right', '');
$pdf->showBoxed($closing_balancetotalC_display, 520, 423 - $line, 48, 20, 'right', '');
$grossReceipts =  $paidThisMonthtotalC + $paidgsttotalC;
$pdf->setlinewidth(0.5);
// bottom line
$pdf->moveto(25, 430 - $line);
$pdf->lineto(570, 430 - $line);
$pdf->stroke();

$pdf->setlinewidth(0.5);
// tenant and opening
$pdf->moveto(220, 695 - $startline);
$pdf->lineto(220, 430 - $line);
$pdf->stroke();
// opening and bill
$pdf->moveto(270, 695 - $startline);
$pdf->lineto(270, 430 - $line);
$pdf->stroke();
// received and closing
$pdf->moveto(520, 695 - $startline);
$pdf->lineto(520, 430 - $line);
$pdf->stroke();
// bill and received
$pdf->moveto(370, 695 - $startline);
$pdf->lineto(370, 430 - $line);
$pdf->stroke();
// right side bar
$pdf->moveto(570, 710 - $startline);
$pdf->lineto(570, 430 - $line);
$pdf->stroke();

// left side bar
$pdf->moveto(25, 430 - $line);      // tonkaja levaja
$pdf->lineto(25, 710 - $startline);
$pdf->stroke();

$pdf->moveto(25, 405 - $line);
$pdf->lineto(570, 405 - $line);
$pdf->stroke();

$pdf->moveto(25, 405 - $line);
$pdf->lineto(25, 388 - $line);
$pdf->stroke();

$pdf->moveto(570, 405 - $line);
$pdf->lineto(570, 388 - $line);
$pdf->stroke();

$startline = $line = $line + 10;
$pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
$pdf->showBoxed('PAYMENTS', 25, 393 - $line, 545, 20, 'center', '');

$startline =  $line = $line + 30;
$pdf->setFontExt($_fonts['Helvetica-Bold'], 8);

$text3 = 'Supplier';
$text4 = 'Description';
$text5 = '';
$text8 = "Net\n" . $_SESSION['country_default']['currency_symbol'];
$text9 = $_SESSION['country_default']['tax_label'] . "\n" . $_SESSION['country_default']['currency_symbol'];
$text10 = "Total\n" . $_SESSION['country_default']['currency_symbol'];

$pdf->showBoxed($text3, 30, 400 - $line, 75, 30, 'left', '');
$pdf->showBoxed($text4, 164, 400 - $line, 75, 30, 'left', '');
$pdf->showBoxed($text8, 355, 400 - $line, 45, 30, 'center', '');
$pdf->showBoxed($text9, 430, 400 - $line, 45, 30, 'center', '');
$pdf->showBoxed($text10, 505, 400 - $line, 55, 30, 'center', '');

$pdf->setlinewidth(0.5);

$pdf->moveto(25, 430 - $line);
$pdf->lineto(570, 430 - $line);
$pdf->stroke();

$pdf->moveto(25, 405 - $line);
$pdf->lineto(570, 405 - $line);
$pdf->stroke();

$ownerGroupName = 'Owner Payments';
$DRGroupName = 'Recoverable Payments';
$VOGroupName = ucwords(strtolower($_SESSION['country_default']['variable_outgoings'])) . ' Payments';
$NPGroupName = 'Non-Operating Payments';

global $grandTotalNet;
global $grandTotalTax;
global $grandTotalGross;
global $newPage;

$grandTotalNet = 0;
$grandTotalTax  = 0;
$grandTotalGross = 0;

$newPage = 0;
$line = $line + 10;
$expenseListOwner = expenses_detail_subgroup($propertyID, $periodFrom, $periodTo, 'EXPOWN');
if ($expenseListOwner) {
    printGroupDetailed($expenseListOwner, $ownerGroupName, false, $line, $startline);
}

$expenseListDR = expenses_detail_subgroup($propertyID, $periodFrom, $periodTo, 'EXPDR');
if ($expenseListDR) {
    printGroupDetailed($expenseListDR, $DRGroupName, false, $line, $startline);
}

// ###########################VARIABLE OUTGOINGS	EXPENSES#################################
$expenseListVO = expenses_detail_subgroup($propertyID, $periodFrom, $periodTo, 'EXPVO');
if ($expenseListVO) {
    printGroupDetailed($expenseListVO, $VOGroupName, false, $line, $startline);
}

$expenseListNP = expenses_detail_subgroup($propertyID, $periodFrom, $periodTo, 'BSPMT', true);
if ($expenseListNP) {
    printGroup($expenseListNP, $NPGroupName);
}

$totalGross_payments = printGrandTotalDetailed($line, $startline);

$line = $line + 50;

if ($line >= 370) {
    page_payment('cont', $propertyID);
}

$cashmov = $grossReceipts - $totalGross_payments;
$pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
$pdf->showBoxed('Cash Movement For The Period', 27, 390 - $line, 130, 30, 'left', '');
$pdf->setFontExt($_fonts['Helvetica-Bold'], 7);
$pdf->showBoxed(formatting($cashmov), 470, 390 - $line, 48, 30, 'right', '');

$line = $line + 20;

if ($line >= 370) {
    page_payment('cont', $propertyID);
}

$receipts = total_cash_receipts($propertyID, $startFinancialYear, $previousPeriodTo);
$payments = total_cash_payments($propertyID, $startFinancialYear, $previousPeriodTo);

$receiptsA = total_cash_receipts($propertyID, STARTDATE, $financialPeriodToPY);
$paymentsA = total_cash_payments($propertyID, STARTDATE, $financialPeriodToPY);


$diffY = $receipts - $payments;
$diffA = $receiptsA - $paymentsA;
$diff = $diffA + $diffY;

$diff_display = formatting($diff);
$openingbalance = $diff;

$pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
$pdf->showBoxed('Opening Cash Balance', 27, 390 - $line, 130, 30, 'left', '');
$pdf->setFontExt($_fonts['Helvetica-Bold'], 7);
$pdf->showBoxed($diff_display, 470, 390 - $line, 48, 30, 'right', '');

$line = $line + 20;
// OWNER REMITTANCES

$total_pmts_duringM = getPaymentsToOwners($propertyID, $periodFrom, $periodTo); // trim($query_result["amount"]);
$creditorResult = getCreditorDetails($propertyID, $periodFrom, $periodTo);	// $query_result = mssql_query($query);

foreach ($creditorResult as $key => $thisRow) {

    $ind_pmts_duringY_display = formatting($thisRow['pmxc_alloc_amt']);
    $ind_pmxc_s_creditor_display = $thisRow['pmxc_s_creditor'];
    $cred_name = $thisRow['pmco_name'];
    $cred_name = ellipsitize($cred_name, 38);
    $ind_pmxc_alloc_dt =  $thisRow['pmxc_alloc_dt'];
    $tdescription = $thisRow['description'];

    if ($line >= 370) {
        page_payment('cont', $propertyID);
    }

    $pdf->setFontExt($_fonts['Helvetica'], 7);
    $pdf->showBoxed("$cred_name", 27, 390 - $line + 20, 95, 10, 'left', '');
    $pdf->showBoxed("$tdescription", 122, 390 - $line, 150, 30, 'left', '');
    $pdf->showBoxed("$ind_pmxc_alloc_dt", 320, 390 - $line, 48, 30, 'right', '');
    $pdf->setFontExt($_fonts['Helvetica-Bold'], 7);
    $pdf->showBoxed("$ind_pmts_duringY_display", 470, 390 - $line, 48, 30, 'right', '');
    $line = $line + 10;

}

$ownerpayments = -($total_pmts_duringM);
$ownerpayments_display = formatting($ownerpayments);

$subtotal_balance = $cashmov + $openingbalance;
// CLOSING BALANCE
$closing_cashbalance = $subtotal_balance - $ownerpayments;
$line = $line + 10;

if ($line >= 370) {
    page_payment('cont', $propertyID);
}


$pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
$pdf->showBoxed('Closing Cash Balance', 27, 390 - $line, 130, 30, 'left', '');
$pdf->setFontExt($_fonts['Helvetica-Bold'], 7);
$pdf->showBoxed(formatting($closing_cashbalance), 470, 390 - $line, 48, 30, 'right', '');

$traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'cirrus8CashSummary', A4_PORTRAIT);
$traccFooter->prerender($pdf);

$pdf->end_page_ext('');

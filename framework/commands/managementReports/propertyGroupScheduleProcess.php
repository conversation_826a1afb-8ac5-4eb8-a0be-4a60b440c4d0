<?php

function get_prepayments($propertyID, $leaseID, $dueDate)
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);
    $params = [];
    $prepaidSQL =  'SELECT CONVERT(char(10), pmxd_alloc_dt, 103) AS pmxd_alloc_dt,*
					FROM[PMXD_AR_ALLOC] E
					WHERE PMXD_ALLOC_DT <= CONVERT(datetime, ' . addSQLParam($params, $dueDate) . ', 103) 
						AND PMXD_PROP = ' . addSQLParam($params, $propertyID) . ' 
						AND PMXD_LEASE = ' . addSQLParam($params, $leaseID) . '
						AND NOT EXISTS
								(SELECT *
									FROM [AR_TRANSACTION] R
									WHERE((BATCH_NR = E.[PMXD_T_BATCH] AND BATCH_LINE_NR = E.[PMXD_T_LINE]))
									AND TRANS_DATE <= CONVERT(datetime, ' . addSQLParam($params, $dueDate) . ', 103))';

    return $dbh->executeSet($prepaidSQL, false, true, $params);
}

function fetchOutstandingAmounts($debtorID, $propertyID, $leaseID, $dueDate, $showZero = 'No', $receiptsToDate = '')
{
    global $clientDirectory;
    $records =  [];

    $outstanding = dbGetInvoiceChargesUnpaidDebtors($propertyID, $leaseID, $debtorID, $dueDate, $receiptsToDate);

    if ($outstanding) {
        foreach ($outstanding as $row) {
            // -- offset is all transactions allocated against the reference transaction minus the effect of any adjustments [INV<->CRE / CRE<->INV] (to prevent a double count)
            $offset = bcadd($row['totalAllocated'], $row['totalReallocated'], 2);
            $row['unallocated'] = round($row['unallocated'] * 1, 2);
            $row['amount'] = round($row['amount'], 2);
            $unpaidAmount = bcsub($row['amount'], $offset, 2);
            $unpaidTaxAmount = round($row['taxAmount'] - $row['totalAllocatedTax'], 2);
            $unpaidNetAmount = round($row['netAmount'] - $row['totalAllocatedNet'], 2);

            if ((($showZero == 'No' && $unpaidAmount != 0 and ! (($unpaidTaxAmount == 0 and $unpaidNetAmount == 0) || ($unpaidTaxAmount == -0.01 and $unpaidNetAmount == 0.01))) || $showZero == 'Yes') && $row['transactionType'] != TYPE_CASH) {
                $row['unpaidAmount'] = bcsub($row['amount'], $offset, 2);
                $row['unpaidTaxAmount'] = $unpaidTaxAmount;
                $row['unpaidNetAmount'] = $unpaidNetAmount;

                if ($row['transactionType'] == 'INV' or $row['transactionType'] == 'CRE') {
                    [$day_, $month_, $year_] = explode('/', $row['dueDate']);
                    // $filename = 'tax_invoice_'. '20140701' . '_'. '9646'. '_'. 'ALBANY'. '_'. 'MAST';
                    $filename = 'tax_invoice_' . $year_ . $month_ . $day_ . '_' . $row['invoiceNumber'] . '_' . $propertyID . '_' . $leaseID;
                    $file = "../reports/{$clientDirectory}/pdf/TaxInvoice/$filename.pdf";
                    // echo $file .'<br>';
                    if (file_exists($file)) {
                        $row['filePath'] = $file;
                    } else {
                        [$day_, $month_, $year_] = explode('/', $row['transactionDate']);
                        // $filename = 'tax_invoice_'. '20140701' . '_'. '9646'. '_'. 'ALBANY'. '_'. 'MAST';
                        $filename = 'tax_invoice_' . $year_ . $month_ . $day_ . '_' . $row['invoiceNumber'] . '_' . $propertyID . '_' . $leaseID;
                        $file = "../reports/{$clientDirectory}/pdf/TaxInvoice/$filename.pdf";
                        // echo $file .'<br>';
                        if (file_exists($file)) {
                            $row['filePath'] = $file;
                        } else {
                            [$day_, $month_, $year_] = explode('/', $row['toDate']);
                            $the_date = date('d/m/Y', strtotime("$month_/$day_/$year_"));
                            [$day_, $month_, $year_] = explode('/', $the_date);
                            // $filename = 'tax_invoice_'. '20140701' . '_'. '9646'. '_'. 'ALBANY'. '_'. 'MAST';
                            $filename = 'tax_invoice_' . $year_ . $month_ . $day_ . '_' . $row['invoiceNumber'] . '_' . $propertyID . '_' . $leaseID;
                            $file = "../reports/{$clientDirectory}/pdf/TaxInvoice/$filename.pdf";
                            // echo $file .'<br>';
                            if (file_exists($file)) {
                                $row['filePath'] = $file;
                            }
                        }
                    }
                }

                $records[] = $row;
            } elseif (($row['transactionType'] == TYPE_CASH) && ($row['unallocated'] != 0)) {
                $unallocated = dbGetUnallocated($propertyID, $leaseID, $debtorID, $row['batchNumber'], $row['batchLineNumber'], $dueDate);
                if ($unallocated) {
                    foreach ($unallocated as $unallocatedItem) {
                        if (($showZero == 'No' && $unallocatedItem['amount'] != 0 and ! (($unpaidTaxAmount == 0 and $unpaidNetAmount == 0) || ($unpaidTaxAmount == -0.01 and $unpaidNetAmount == 0.01))) || $showZero == 'Yes') {
                            $unallocatedItem['debtorID'] = $row['debtorID'];
                            $unallocatedItem['debtorName'] = $row['debtorName'];
                            $unallocatedItem['transactionAmount'] = $unallocatedItem['amount'];
                            $unallocatedItem['batchNumber'] = $row['batchNumber'];
                            $unallocatedItem['batchLineNumber'] = $row['batchLineNumber'];
                            $unallocatedItem['transactionDate'] = $row['transactionDate'];
                            $unallocatedItem['dueDate'] = $row['dueDate'];
                            $unallocatedItem['invoiceNumber'] = $row['invoiceNumber'];
                            $unallocatedItem['transactionType'] = TYPE_CASH;
                            $unallocatedItem['unallocated'] = $row['unallocated'];

                            $unallocatedItem['unpaidAmount'] = $unallocatedItem['amount'];

                            $unpaidTaxAmount = round($unallocatedItem['taxAmount'] - $row['totalAllocatedTax'], 2);
                            $unpaidNetAmount = round($unallocatedItem['netAmount'] - $row['totalAllocatedNet'], 2);

                            $unallocatedItem['unpaidTaxAmount'] = $unpaidTaxAmount;
                            $unallocatedItem['unpaidNetAmount'] = $unpaidNetAmount;

                            $unallocatedItem['unitID'] = $row['unitID'];
                            $unallocatedItem['unitDescription'] = $row['unitDescription'];

                            $records[] = $unallocatedItem;
                        }
                    }
                }
            }
        }
    }

    // get prepayments

    $unpaid_total_net = 0;
    $unpaid_total_gst = 0;
    $unpaid_total_gross = 0;
    $prepaid_result = get_prepayments($propertyID, $leaseID, $dueDate);

    $prepayments_ = [];
    foreach ($prepaid_result as $row) {
        $a = [];
        $unpaid_debtors_code 	= $row['pmxd_s_debtor'];
        $transaction_date 	= $row['pmxd_alloc_dt'];
        $unpaid_account_code 	= $row['pmxd_acc'];
        $unpaid_amount 		= $row['pmxd_alloc_amt'];
        $unpaid_tax_amt 	= $row['pmxd_tax_amt'];
        $unpaid_description 	= 'Prepayment';
        $unpaid_date_from 	= '';
        $unpaid_date_to 	= '';


        $unpaid_net_amount = bcsub($unpaid_amount, $unpaid_tax_amt, 2);

        if ($unpaid_amount != 0) {
            $unpaid_total_net = bcadd($unpaid_total_net, $unpaid_net_amount, 2);
            $unpaid_total_gst = bcadd($unpaid_total_gst, $unpaid_tax_amt, 2);
            $unpaid_total_gross = bcadd($unpaid_total_gross, $unpaid_amount, 2);
        }

        // if ($unpaid_amount == '0')// && $unpaid_tax_amt_display == "0.00" && $unpaid_net_amount_display == "0.00" )
        if ($unpaid_amount == 0 and (($unpaid_tax_amt == 0 and $unpaid_net_amount == 0) || ($unpaid_tax_amt == -0.01 and $unpaid_net_amount == 0.01) || ($unpaid_tax_amt == 0.01 and $unpaid_net_amount == -0.01))) {
        } else {
            // $html .=  '<tr class="' . alternateNextRow() . '">' .
            // "<td>$unpaid_account_code</td>
            // <td>$unpaid_debtors_name</td>
            // <td>$unpaid_description</td>
            // <td>$transaction_date</td>
            // <td>$unpaid_date_from</td>
            // <td align=right>$unpaid_date_to</td>
            // <td align=right>$unpaid_net_amount_display</td>
            // <td align=right>$unpaid_tax_amt_display</td>
            // <td align=right>$unpaid_gross_amount_display</td>" .
            // "</tr>";
            $a['transactionDate'] = $transaction_date;
            // $a['dueDate'] = $transaction_date;
            $a['invoiceDate'] = $transaction_date;
            $a['accountID'] = $unpaid_account_code;
            $a['description'] = $unpaid_description;
            $a['transactionType'] = '';
            $a['invoiceNumber'] = '';
            $a['fromDate'] = $unpaid_date_from;
            $a['toDate'] = $unpaid_date_to;
            $a['dueDate'] = '';
            $a['amount'] = $unpaid_amount;
            $a['unpaidNetAmount'] = $unpaid_net_amount;
            $a['unpaidTaxAmount'] = $unpaid_tax_amt;
            $a['unpaidAmount'] = $unpaid_amount;

            $prepayments_[] = $a;
        } // end of else - after - if ($unpaid_gross_amount_display == '0.00')

        // $i++;
        // endwhile; // end of while ($i < $prepaid_number) :
        // endif;
    }// end of - foreach ($prepaid_result as $row)

    if ($unpaid_total_gross != 0) {
        $records = array_merge($records, $prepayments_);
    }

    return $records;
}

function getPropGroupData($propGroupID, $reportDate)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $params = [];
    $sql = "
        SELECT 
            pmpr_prop
            ,pmle_lease
            ,CASE WHEN pmle_lease IS NOT NULL THEN CONCAT(RTRIM(pmle_street), ', ',RTRIM(pmle_city), ', ', RTRIM(pmle_state), ' ', RTRIM(pmle_postcode))
				ELSE CONCAT(RTRIM(pmpr_street), ', ',RTRIM(pmpr_city), ', ', RTRIM(pmpr_state), ' ', RTRIM(pmpr_postcode)) END as address
            ,pmle_name as leaseName
            ,CASE 
                WHEN pmle_lease IS NULL OR pmle_lease = '' 
                    THEN vacant_lease.pmua_to_dt 
                ELSE NULL END as lastOccupationDate
            ,currentRent
            ,nextRentReviewDate
            ,nextRentAmount
            ,pmle_com_dt as leaseStart
            ,pmle_exp_dt as leaseExpiry
            ,current_lease.pmua_to_dt
            ,CASE WHEN pmle_lease IS NOT NULL THEN 0 ELSE 1 END as orderBy
        FROM pmpr_property property
        
        JOIN pmpu_p_unit unit
            ON unit.pmpu_prop = property.pmpr_prop
        LEFT JOIN (
            SELECT pmua_prop, pmua_unit, pmua_from_dt, pmua_to_dt, pmua_status, pmua_lease,
                row_number() over(partition by pmua_prop,pmua_unit order by pmua_to_dt desc) as rn,
                c.pmla_amt as currentRent
            FROM pmua_unit_area a
            LEFT JOIN pmlc_l_charge b
                ON b.pmlc_prop = a.pmua_prop
                AND b.pmlc_lease = a.pmua_lease
                AND b.pmlc_unit = a.pmua_unit
            LEFT JOIN pmla_l_c_amt c
                            ON c.pmla_serial = b.pmlc_serial
                            AND c.pmla_prop = b.pmlc_prop
                            AND c.pmla_lease = b.pmlc_lease
                            AND c.pmla_start_dt = b.pmlc_start_dt
                            AND c.pmla_unit = b.pmlc_unit
        
            WHERE pmua_from_dt <= CONVERT(datetime, " . addSQLParam($params, $reportDate) . ', 103)
            AND pmua_to_dt >= CONVERT(datetime, ' . addSQLParam($params, $reportDate) . ', 103)
            ) current_lease
            ON current_lease.pmua_prop = property.pmpr_prop
            AND current_lease.pmua_unit = unit.pmpu_unit
            AND current_lease.rn = 1
        
        LEFT JOIN pmle_lease lease
            ON pmle_prop = pmpr_prop
            AND pmle_lease = current_lease.pmua_lease
        
        LEFT JOIN (
            SELECT pmua_prop, pmua_unit, pmua_from_dt, pmua_to_dt, pmua_status, pmua_lease,
                row_number() over(partition by pmua_prop,pmua_unit order by pmua_to_dt desc) as rn
            FROM pmua_unit_area
            WHERE pmua_to_dt < CONVERT(datetime, ' . addSQLParam($params, $reportDate) . ', 103)
            ) vacant_lease
            ON vacant_lease.pmua_prop = property.pmpr_prop
            AND vacant_lease.pmua_unit = unit.pmpu_unit
            AND vacant_lease.rn = 1
        
        LEFT JOIN (
            SELECT 
                pmrr_prop,
                pmrr_lease,
                pmrr_type,
                pmrr_closed,
                CAST(pmrr_rev_dt as DATE) as nextRentReviewDate,
                pmla_amt as nextRentAmount,
                ROW_NUMBER() OVER (PARTITION BY pmrr_prop, pmrr_lease ORDER BY pmrr_rev_dt ASC) AS rn
            FROM pmrr_l_rent_rev
            LEFT JOIN pmla_l_c_amt
                ON pmla_prop = pmrr_prop
                AND pmla_lease = pmrr_lease
                AND pmrr_chargeAmountID = pmla_id
            WHERE pmrr_closed = 0 ) nextRentReview
            ON nextRentReview.pmrr_prop = lease.pmle_prop
            AND nextRentReview.pmrr_lease = lease.pmle_lease
            AND nextRentReview.rn = 1
            
            WHERE pmpr_prop_group = ' . addSQLParam($params, $propGroupID) . '
            AND property.pmpr_delete != 1
            ORDER BY orderBy, pmle_prop;';

    //    pre_print_r($sql);
    $result =  $dbh->executeSet($sql, false, true, $params);

    $return = [];

    foreach ($result as $line) {
        $overholding = '';
        $arrearsTotal = 0;

        if ($line['leaseName']) {
            $overholding = toDateStamp($line['leaseExpiry']) < toDateStamp($reportDate) ? 'Y' : 'N';
            $records = fetchOutstandingAmounts(null, $line['pmpr_prop'], $line['leaseID'], $reportDate, 'No', null, 1);

            foreach ($records as $record) {
                $arrearsTotal += $record['unpaidNetAmount'];
            }
        } else {
            $line['leaseName'] = 'Vacant';
        }

        $return[] = [
            'address' => $line['address'],
            'leaseName' => $line['leaseName'],
            'lastDayOfOccupation' => $line['lastOccupationDate'],
            'currentRent' => $line['currentRent'],
            'proposedRent' => '',
            'nextIncDate' => $line['nextRentReviewDate'],
            'nextIncAmt' => $line['nextRentAmount'],
            'leaseStart' => $line['leaseStart'],
            'leaseExpiry' => $line['leaseExpiry'],
            'overholding' => $overholding,
            'vacateDate' => $line['pmua_to_dt'] != '31/12/2999' ? $line['pmua_to_dt'] : '',
            'arrears' => $arrearsTotal,
            'propertyStatus' => '',
        ];
    }

    return $return;
}

function propertyGroupScheduleProcess(&$context)
{
    global $pathPrefix, $clientDirectory;

    if (! isPostback()) {
        $view = new MasterPage(userViews(), '/managementReports/propertyGroupScheduleProcess.html');
        $view->setSection($context['module']);
    } else {
        $view = new UserControl(userViews(), '/managementReports/propertyGroupScheduleProcess.html');
    }

    $view->bindAttributesFrom($_REQUEST);


    global $clientDirectory, $pathPrefix;

    [$d, $m, $y] = explode('/', $view->items['reportingDate']);
    $title = "$d.$m.$y";
    $filename = "$d.$m.$y" . '.xlsx';
    $_filePath = "{$pathPrefix}{$clientDirectory}/xlsx/PropertyGroupSchedule/";
    checkDirPath($_filePath);
    $_downloadPath = "{$clientDirectory}/xlsx/PropertyGroupSchedule";
    $downloadLink = $_downloadPath . '/' . $filename; // for download link (used to display dl link on page)
    $filePath = $_filePath . $filename; // for attachment (can used for email attachment also)


    if (isset($view->items['propGroupID'])) {
        $view->items['propGroupID'] = deserializeParameters($view->items['propGroupID']);
    }

    $amount_format = '_("$"* #,##0.00_);_("$"* \(#,##0.00\);_("$"* "-"??_);_(@_)';
    $number_format = '_(* #,##0.00_);_(* (#,##0.00);_(* "-"??_);_(@_)';

    $columns = [
        ['address', 'Address', 'left'],
        ['leaseName', 'Lease Name', 'left'],
        ['lastOccupationDate', 'Last Day of Occupation', 'left'],
        ['currentRent', 'Current Rent', 'right', $number_format],
        ['proposedRent', 'Proposed Rent', 'right', $number_format],
        ['nextIncDate', 'Next Inc. Date', 'left'],
        ['nextIncAmt', 'Next Inc. Amt', 'right', $number_format],
        ['leaseStart', 'Lease Start', 'left'],
        ['leaseExpiry', 'Lease Expiry', 'left'],
        ['overholding', 'Overholding', 'left'],
        ['vacateDate', 'Vacate Date', 'left'],
        ['arrears', 'Arrears', 'left', $number_format],
        ['propertyStatus', 'Property Status', 'left'],
    ];

    $vacantProperties = [];

    $index = 0;
    foreach ($view->items['propGroupID'] as $propGroupID) {
        $propGroupData = getPropGroupData($propGroupID, $view->items['reportingDate']);
        $header1 = $propGroupID . ' - ' . dbgetParam('PROPGROUP', $propGroupID);
        $header2 = 'MANAGER : ' . dbgetParam('PORTMGR', $propGroupID);
        $header = $header1 . "\n" . $header2;

        if (! isset($report)) {
            checkDirPath($filePath, true);
            $report = new XLSDataReport($filePath, $propGroupID);
            $report->enableFormatting = true;

            foreach ($columns as $column) {
                $report->addColumn($column[0], $column[1], 200, $column[2], $column[3]);
            }

            $report->renderHeaderDetails($header);
            $report->renderHeader();
            $report->freezePanes(0, 5);
            $report->renderData($propGroupData);
        } else {
            $report->setSheetDetails($index, $propGroupID);
            $report->line = 1;
            $report->columns = [];

            foreach ($columns as $column) {
                $report->addColumn($column[0], $column[1], 200, $column[2], $column[3]);
            }

            $report->renderHeaderDetails($header);
            $report->renderHeader();
            $report->freezePanes(0, 5);
            $report->renderData($propGroupData);
        }
        $index++;

    }

    $report->clean();
    $report->endPage();
    $report->close();

    $view->items['downloadPath'] = $downloadLink;
    $view->render();
}

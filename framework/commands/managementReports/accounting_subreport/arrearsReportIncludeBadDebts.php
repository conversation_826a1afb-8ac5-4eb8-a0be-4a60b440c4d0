<?php

/**
 * Created by PhpStorm.
 * User: esantiago
 * Date: 3/15/2019
 * Time: 2:53 PM
 */

// set module and command to use
$repModule = 'managementReports';
$repCommand = 'unpaidDebtorsProcess';

// reset report file to be attached
$context['ownerReportFile'] = null;

// report option
// 1 = portrait
// 2 = landscape
// $orientation = default from report table
$repOrientation = 2;

if ($repModule and $repCommand) {
    $originalViewItems = $view->items;
    $originalContext = $context;

    $view->items['method'] = 'property';
    $view->items['property'] = $propertyID;
    $lease = dbLeaseList($propertyID, 0, false, false, true);
    $leaseList = [];
    foreach ($lease as $row) {
        $leaseList[] = $row['leaseID'];
    }
    $view->items['tenantID'] = implode('::', $leaseList);
    $view->items['receiptsToDate'] = $view->items['periodTo'];
    $view->items['toDate'] = $view->items['periodTo'];
    $view->items[IS_TASK] = true;

    // Adjust parameters
    $view->items['showCurrentOnly'] = 'No';
    $view->items['showCredit'] = 'No';
    $view->items['badDebtProvision'] = 'No';
    $view->items['showArrearsWithDetails'] = 'Yes';
    $view->items['showArrearsAged'] = 'No';
    $view->items['frequency'] = 'M';
    $view->items['showleaseGurantee'] = 'No';
    $view->items['includeArrearsNotes'] = 'No';
    $view->items['format2'] = 'pdf';

    // used on the command file to alter some processes
    $view->items['forOwnerReport'] = 1;


    $context = $view->items;
    executeCommand($repCommand, $repModule);

    if (file_exists($context['ownerReportFile'])) {
        // $pdf is the main PDF where this report will be attached.
        $pdi = new ImportedPage($pdf, $context['ownerReportFile'], $repOrientation);
        $i = 1;
        while ($pdi->loadPage($i)) {
            $page++;
            $pdi->preparePage();
            $pdi->render();
            $pdi->endPage();
            $i++;
        }
        $pdi->close();
    }

    $context = $originalContext;
    $view->items = $originalViewItems;
}

// reset report file to be attached
$context['ownerReportFile'] = null;

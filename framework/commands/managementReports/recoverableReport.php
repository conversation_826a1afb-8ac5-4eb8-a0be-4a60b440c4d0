<?php

/**
 * Created by PhpStorm.
 * User: adacanay
 * Date: 1/5/2017
 * Time: 10:11 AM
 */
function recoverableReport(&$context)
{
    $userpermission = new userpermission();
    $result = $userpermission->userHasPageAccess(__FUNCTION__);

    // Page Template
    if (! isPostback()) {
        $view = new MasterPage(userViews(), '/managementReports/recoverableReport.html');
        $view->setSection($context['module']);
    } else {
        $view = new UserControl(userViews(), '/managementReports/recoverableReport.html');
    }
    $view->bindAttributesFrom($_REQUEST);

    // Array Call-In
    $view->items['reportTypeList'] =
    [
        'recovered' => 'Recovered',
        'unrecovered' => 'UnRecovered',
    ];
    if (! isset($view->items['reportType'])) {
        $view->items['reportType'] = 'recovered';
    }

    $view->items['transactionOptionList'] =
    [
        'allTransactions' => 'All Transactions',
        'paymentsOnly' => 'Payments Only',
        'receiptsOnly' => 'Receipts Only',
    ];
    $view->items['accountOptionList'] =
    [
        'allAccountCodes' => 'All Account Codes',
        'accountRange' => 'Account Range',
        'multipleCodes' => 'Multiple Codes',
    ];
    $view->items['formatList'] =
    [
        FILETYPE_PDF => 'PDF',
        FILETYPE_XLS => 'Excel Spreadsheet',
        FILETYPE_SCREEN => 'Print to Screen',
    ];

    $view->items['sortBy'] =
    [
        'sortDate' => 'Date',
        'sortAccountID' => 'Account',
        'sortLease' => 'Supplier',
    ];

    $view->items['propertyGroupedList'] = dbPropertyGroupList();

    // Filter Value
    if ($view->items['propertyID']) {
        $view->items['propertyID'] = deserializeParameters($view->items['propertyID']);
    }
    if (is_array($view->items['accountID']) and $view->items['accountID']) {
        $accountID = deserializeParameters($view->items['accountID']);
    }



    $view->items['accountChartList'] = dbGetChartOfAccountByRecoverable(true);

    // Default Values
    $month = date('m');
    $year = date('Y');
    if (empty($view->items['fromDate'])) {
        $view->items['fromDate'] = date("01/$month/$year");
    }
    if (empty($view->items['toDate'])) {
        nextMonth($month, $year);
        $month = str_pad($month, 2, '0', STR_PAD_LEFT);
        $view->items['toDate'] = date("01/$month/$year");
    }

    if (! array_key_exists($view->items['accountOption'], $view->items['accountOptionList'])) {
        $view->items['accountOption'] = 'allAccountCodes';
    }
    if (! array_key_exists($view->items['format'], $view->items['formatList'])) {
        $view->items['format'] = FILETYPE_PDF;
    }
    if (! array_key_exists($view->items['sortOption'], $view->items['sortBy'])) {
        $view->items['sortOption'] = 'sortDate';
    }

    // $view->items['pageBreak'] = $view->items['separateProperties'];


    // Action
    switch ($view->items['action']) {
        case 'finalise':
            // Validation
            if (empty($view->items['reportType'])) {
                $validationErrors[] = 'You need to select a report type, either Bank Account or Property.';
            }
            if (empty($view->items['propertyID'])) {
                $validationErrors[] = 'You need to select at least one Property';
            }
            if ($view->items['fromDate'] and ! isValid($view->items['fromDate'], TEXT_SMARTDATE, false)) {
                $validationErrors[] = 'Your reporting period is invalid (from date).';
            }
            if ($view->items['toDate'] and ! isValid($view->items['toDate'], TEXT_SMARTDATE, false)) {
                $validationErrors[] = 'Your reporting period is invalid (to date).';
            }
            if (toDateStamp($view->items['toDate']) < toDateStamp($view->items['fromDate'])) {
                $validationErrors[] = 'Your to date cannot be before your from date (reporting period).';
            }
            if ($view->items['accountOption'] == 'accountRange' and (empty($view->items['accountIDRange1']) or empty($view->items['accountIDRange2']))) {
                $validationErrors[] = 'You need to select an account range.';
            } elseif ($view->items['accountIDRange1'] and $view->items['accountIDRange2'] and $view->items['accountIDRange1'] > $view->items['accountIDRange2']) {
                $validationErrors[] = 'From Account Range must be less than To Account Range.';
            } elseif ($view->items['accountOption'] == 'multipleCodes' and empty($view->items['accountID'])) {
                $validationErrors[] = 'You need to select at least one account type.';
            }
            // No Errors
            if (noErrors($validationErrors)) {
                // Additional Filter
                if ($view->items['reportType'] != 'unrecovered') {
                    $view->items['reportType'] = 'recovered';
                }
                // Ready to send to process
                if ($view->items['format'] == FILETYPE_SCREEN) {
                    $view->render();
                }
                $context = $view->items;

                executeCommand('recoverableReportProcess', 'managementReports');
            }
            break;
    }

    if ($view->items['format'] != FILETYPE_SCREEN || empty($view->items['action']) || $validationErrors) {
        // Post Feed
        $view->items['validationErrors'] = $validationErrors;

        // Display
        $view->render();
    }
}

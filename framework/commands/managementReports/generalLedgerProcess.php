<?php

function calculateBalances($propertyID, $basis, $periodFrom, $yearFrom, $periodTo, $yearTo, $accounts, &$balances, $suffix, $ownerID)
{
    switch ($basis) {
        case BASIS_CASH:
            $node = 'Cash';
            break;
        case BASIS_ACCRUALS:
            $node = 'Accruals';

            break;
    }
    if ($ownerID) {
        $generalLedger = dbGetLedgerForPropertyOwner($propertyID, $basis, $periodFrom, $yearFrom, $periodTo, $yearTo, $accounts, $ownerID);
    } else {
        $generalLedger = dbGetLedgerForProperty($propertyID, $basis, $periodFrom, $yearFrom, $periodTo, $yearTo, $accounts);
    }

    /*	$lastPeriod = periodBefore ($periodFrom, $yearFrom);
        $lastYear = $yearTo - 1;*/

    $_balanceNode = 'opening' . $node;
    $output = [];

    foreach ($generalLedger as $t) {
        if (! isset($balances[$t['accountID']])) {
            $o = dbGetOpeningTrialBalanceByAccount($propertyID, $t['accountID'], $periodFrom, $yearFrom, $t['accountType'] != 'B');
            $balances[$t['accountID']] = $o['balance' . $node];
        }
        $row = $t;
        if ($t['transactionAmount'] != 0) {
            if ($t['transactionAmount'] < 0) {
                $row['credit' . $suffix] = abs($t['transactionAmount']);
            } else {
                $row['debit' . $suffix] = abs($t['transactionAmount']);
            }
            $row['current' . $suffix] = $t['transactionAmount'];
        }
        $row['balance'] = bcadd($balances[$t['accountID']], $t['transactionAmount']);
        // -- it's a running balance so update the balance
        $balances[$t['accountID']] = $row['balance'];

        $output[$t['accountID']][] = $row;
    }

    return $output;
}


function generalLedgerProcess($context)
{
    $threshold = 3;
    global $sess, $clientDirectory, $pathPrefix, $propertyID, $pdf;

    $view = new UserControl(userViews(), '/managementReports/generalLedgerProcess.html');

    $view->bindAttributesFrom($context);
    $view->bindAttributesFrom($_REQUEST);
    $validationErrors = [];

    if ($context[IS_TASK]) {
        $view->bindAttributesFrom($context);
        extract($context);
    } else {
        $view->bindAttributesFrom($context);
        $view->bindAttributesFrom($_REQUEST);
        $count = 1;
        $view->items['propertyCount'] = $count;
        $queue = new Queue(TASKTYPE_GL);
        if ($count > THRESHOLD_GL) {
            $queue->add($_SESSION['clientID'], $_SESSION['un'], 'command=generalLedgerProcess&module=mangementReports', $_REQUEST);
        }
    }

    if (($context[IS_TASK]) || ($count <= THRESHOLD_GL)) {
        $propertyID = $view->items['property'];

        $startDate = dbGetMasterCalendarPeriodForDate($view->items['calendarName'], $view->items['startDate']);
        $endDate = dbGetMasterCalendarPeriodForDate($view->items['calendarName'], $view->items['endDate']);

        $date = $view->items['endDate'];
        $format = $view->items['format'];
        $priorYear = true;

        switch ($view->items['basis']) {
            case BASIS_CASH:
                $node = 'Cash';
                $basis = 'Cash Basis';
                break;
            case BASIS_ACCRUALS:
                $node = 'Accruals';
                $basis = 'Accruals Basis';
                break;
        }

        $lastPeriod = periodBefore($startDate['period'], $startDate['year']);
        $lastYear = $endDate['year'] - 1;
        $totals = [];

        $accountList = dbGetAccountList();
        $accountLookup = mapParameters($accountList, 'accountID', 'accountName');

        $accountGroups = [
            INCOME => 'Income',
            EXPENDITURE => 'Expenditure',
            BALANCESHEET => 'Balance Sheet'];


        $accounts = explode('::', $view->items['accounts']);
        $balances = [];
        $totals = [];
        $subHeader = [];

        $data = calculateBalances($propertyID, $view->items['basis'], $startDate['period'], $startDate['year'], $endDate['period'], $endDate['year'], $accounts, $balances, null, $view->items['ownerID']);
        // pre_print_r($data);
        foreach ($data as $account => $list) {
            $accountType = $list[0]['accountType'];
            $openingBalance = dbGetOpeningTrialBalanceByAccount($propertyID, $account, $startDate['period'], $startDate['year'], ($accountType != 'B'));

            $subHeader[$account]['accountCode'] = $view->items['ownerID'] ? $list[0]['ownerAccountCode'] : $accountLookup[$account];
            $subHeader[$account]['accountName'] = $view->items['ownerID'] ? $list[0]['ownerAccountDesc'] : $accountLookup[$account];
            $subHeader[$account]['openingBalance'] = bcmul(1, $openingBalance['balance' . $node], 2);

            total($list, $totals[$account], ['credit', 'debit', 'creditPY', 'debitPY', 'current', 'currentPY']);
            $totals[$account]['balance'] = $balances[$account];
        }

        if (! $context[DOC_MASTER]) {
            $logoFile = dbGetClientLogo();
            $logoPath = "assets/clientLogos/{$logoFile}";

            $_filePath = "{$pathPrefix}{$clientDirectory}/{$format}/" . DOC_GL . '/';
            $_downloadPath = "{$clientDirectory}/{$format}/" . DOC_GL;
            if ($format == 'FILE_EXPORT') {
                $file = 'GeneralLedger_' . date('dmYHis') . '.xlsx';
            } else {
                $file = 'GeneralLedger_' . date('dmYHis') . ".{$format}";
            }
            $filePath = $_filePath . $file;
            $downloadPath = "{$_downloadPath}/{$file}";
        }

        switch ($format) {
            case FILETYPE_PDF:
                $report = ($context[DOC_MASTER]) ? new PDFDataReport($context[DOC_MASTER], $logoPath, A4_PORTRAIT) : new PDFDataReport($filePath, $logoPath, A4_PORTRAIT);
                $report->multiLine = true;
                $report->printRowLines = true;
                $report->printColumnLines = false;
                $report->printBorders = false;
                $report->cache = false;

                $report->resetColumns();
                $report->addColumn('transactionDate', 'Date', 36, 'left');
                $report->addColumn('source', '', 12, 'left');
                $report->addColumn('companyID', 'Company', 35, 'left');
                $report->addColumn('description', 'Description', 150, 'left');
                $report->addColumn('transactionType', 'Type', 20, 'left');
                $report->addColumn('fromDate', 'From', 36, 'left');
                $report->addColumn('toDate', 'To', 36, 'left');
                $report->addColumn('debit', 'Debit', 50, 'right', REPORT_DECIMAL);
                $report->addColumn('credit', 'Credit', 50, 'right', REPORT_DECIMAL);
                $report->addColumn('balance', 'Balance', 60, 'right', REPORT_DECIMAL);
                $report->addSubHeaderItem('accountName', 0, 125, 'left');
                $report->addSubHeaderItem('openingBalance', 452.5, 80, 'right');

                $subtitle = 'for ' . $property['propertyName'] . '(' . $propertyID . ')';
                $prepared = 'for period ' . $view->items['startDate'] . ' to ' . $view->items['endDate'] . ' (' . $basis . ')';
                $header = new ReportHeader('General Ledger', $subtitle, $prepared);
                $header->xPos = $report->hMargin;
                $header->yPos = $report->pageHeight - $report->vMargin;
                $report->attachObject('header', $header);
                $footer = new TraccFooter(null, $_report['reportName'], $report->pageSize);
                $report->attachObject('footer', $footer);
                $report->preparePage();
                foreach ($data as $account => $accountData) {

                    $report->setSubHeaderValue('accountName', ($view->items['ownerID'] ? $subHeader[$account]['accountCode'] : $account) . ' - ' . $subHeader[$account]['accountName']);
                    $report->setSubHeaderValue('openingBalance', toDecimal($subHeader[$account]['openingBalance']));
                    $report->renderSubHeader(1, 0.7, [0, 0.3, 0.5]);

                    foreach ($accountData as $line) {
                        if (checkValuesFor($line, ['credit', 'debit'])) {
                            $report->renderLine($line);
                        }
                    }
                    $report->renderSubTotal($totals[$account]);
                }


                $report->clean();
                $report->endPage();

                break;
            case FILETYPE_XLS:
                $report = new XLSDataReport($filePath, 'General Ledger');
                $report->addColumn('transactionDate', 'Date', 36, 'left');
                $report->addColumn('source', '', 12, 'left');
                $report->addColumn('companyID', 'Company', 35, 'left');
                $report->addColumn('description', 'Description', 150, 'left');
                $report->addColumn('transactionType', 'Type', 20, 'left');
                $report->addColumn('fromDate', 'From', 36, 'left');
                $report->addColumn('toDate', 'To', 36, 'left');
                $report->addColumn('debit', 'Debit', 50, 'right', REPORT_DECIMAL);
                $report->addColumn('credit', 'Credit', 50, 'right', REPORT_DECIMAL);
                $report->addColumn('balance', 'Balance', 60, 'right', REPORT_DECIMAL);

                $report->renderHeader();
                if ($key) {
                    $report->renderLine($key);
                }

                foreach ($data as $account => $accountData) {
                    $subHeaderArray = ['transactionDate' => ($view->items['ownerID'] ? $subHeader[$account]['accountCode'] : $account) . ' - ' . $subHeader[$account]['accountName'],
                        'source' => '',
                        'companyID' => '',
                        'description' => '',
                        'transactionType' => '',
                        'fromDate' => '',
                        'toDate' => '',
                        'debit' => '',
                        'credit' => '',
                        'balance' => $subHeader[$account]['openingBalance']];
                    $report->renderLine($subHeaderArray);
                    foreach ($accountData as $line) {
                        if (checkValuesFor($line, ['credit', 'debit'])) {
                            $report->renderLine($line);
                        }
                    }
                    $report->renderLine($totals[$account]);
                }
                break;

            case 'FILE_EXPORT':
                $amountFormat = '_(* #,##0.00_);_(* (#,##0.00);_(* "-"??_);_(@_)';
                $report = new XLSDataReport($filePath, 'General Ledger');
                $report->enableFormatting = true;
                $report->addColumn('transactionDate', 'Date', 36, 'left');
                $report->addColumn('source', 'Source', 12, 'left');
                $report->addColumn('accountID', 'Account Code', 36, 'left', '@');
                $report->addColumn('accountName', 'Account Name', 36, 'left');
                if ($view->items['ownerAccountCode'] == 'Yes') {
                    $report->addColumn('ownerAccountCode', 'Owner Account Code', 36, 'left', '@');
                    $report->addColumn('ownerAccountDesc', 'Owner Account Name', 36, 'left');
                }
                $report->addColumn('companyID', 'Company', 35, 'left');
                $report->addColumn('description', 'Description', 150, 'left');
                $report->addColumn('transactionType', 'Type', 20, 'left');
                $report->addColumn('fromDate', 'From', 36, 'left');
                $report->addColumn('toDate', 'To', 36, 'left');
                $report->addColumn('transactionAmount', 'Amount', 60, 'right', $amountFormat);

                $report->renderHeader();
                foreach ($data as $account => $accountData) {
                    foreach ($accountData as $line) {
                        $report->renderLine($line);
                    }
                }
                break;
            case FILETYPE_SCREEN:
                $view->items['basisList'] = [BASIS_CASH => 'Cash', BASIS_ACCRUALS => 'Accruals'];
                $view->items['data'] = $data;
                $view->items['accountGroups'] = $accountGroups;
                $view->items['subHeader'] = $subHeader;
                $view->items['totals'] = $totals;


                $context[DOC_MASTER] = null;
                break;
        }

        if ($format != FILETYPE_SCREEN) {
            $report->close();
        }

        // -- if it s a scheduled task and not part of a bigger report - attach the report and email to the requester
        if (($context[IS_TASK]) && (! $context[DOC_MASTER])) {
            $email = new EmailTemplate('views/emails/basicEmail.html', SYSTEMURL);
            $attachment = [];
            $attachment[0]['file'] = REPORTPATH . '/' . $pdfDownloadLink;
            $attachment[0]['content_type'] = 'application/pdf';
            sendMail($_SESSION['email'], $_SESSION['first_name'] . ' ' . $_SESSION['last_name'], $email->toString(), 'Report', $attachment);
            logData('Emailed report to ' . $_SESSION['email']);
            $context[TASK_COMPLETE] = true;
        }
    }

    // -- if the document doesnt form part of a bigger report
    if (! $context[DOC_MASTER]) {
        if ($count > THRESHOLD_GL) {
            $view->items['statusMessage'] = 'Due to its complexity, this report has been scheduled and will be emailed to you on completion!';
            $view->render();
        } else {
            $view->items['downloadPath'] = $downloadPath;
            $view->render();
        }
    }
}

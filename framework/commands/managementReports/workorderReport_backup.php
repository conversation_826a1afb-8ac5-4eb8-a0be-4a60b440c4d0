<?php

include 'lib/ngForm.php';
include 'lib/htmler.php';

function workorderReport(&$context)
{
    global $pathPrefix, $clientDirectory, $sess;

    if (! isPostback()) {
        $view = new MasterPage(userViews(), '/managementReports/workorderReport.html');
        $view->setSection($context['module']);
    } else {
        $view = new UserControl(userViews(), '/managementReports/workorderReport.html');
    }
    $view->bindAttributesFrom($_REQUEST);

    // PROPERTY Options
    // $propertiesList = array(array("label"=>"Select a Property","value"=>""));
    foreach (dbPropertyList(true, null, 1) as $opt) {
        $propertiesList[] = ['label' => $opt['propertyID'] . ' - ' . $opt['propertyName'], 'value' => $opt['propertyID'], 'bank' => $opt['bank']];
    }
    $view->items['propertiesList'] = htmlspecialchars(json_encode($propertiesList), ENT_QUOTES, 'UTF-8');
    //

    $params = [
        'client_id'     => $sess->get('databaseID'),
        'client_db'     => $sess->get('currentDB'),
        'trans_type'    => 'workorder',
        'user'          => $sess->get('user_name'),
    ];
    $view->items['params'] = htmlspecialchars(json_encode($params), ENT_QUOTES, 'UTF-8');

    $view->render();
}

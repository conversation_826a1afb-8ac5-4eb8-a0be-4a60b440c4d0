<?php


function costPerPortfolio(&$context)
{
    global $clientDB;
    $userpermission = new userpermission();
    $result = $userpermission->userHasPageAccess(__FUNCTION__);

    if (! isPostback()) {
        $view = new MasterPage(userViews(), '/managementReports/costPerPortfolio.html');
        $view->setSection($context['module']);
    } else {
        $view = new UserControl(userViews(), '/managementReports/costPerPortfolio.html');
    }


    // -- bind the postback and set some standard variables
    $view->bindAttributesFrom($_REQUEST);
    if ($context['last_error']) {
        $view->items['last_error'] = $context['last_error'];
    }
    if ($context['statusMessage']) {
        $view->items['statusMessage'] = $context['statusMessage'];
    }
    $validationErrors = [];


    // -- grab the relevant action and process as required
    switch ($view->items['action']) {
        case 'changeClient':
            $view->items['parameterID'] = null;
            break;
        case 'save':
            if (! $view->items['year']) {
                $validationErrors[] = 'You have not provided a valid financial year';
            }

            if (! $view->items['fteCompany']) {
                $validationErrors[] = 'You have not provided Total Number of FTEs in Company';
            } elseif (! isValid($view->items['fteCompany'], TEXT_FLOAT, true)) {
                $validationErrors[] = 'You have not provided a valid Total Number of FTEs in Company';
            }
            // else if( ( $view->items['fteCompany'] < 1)) $validationErrors[] = 'You have not provided a valid Total Number of FTEs in Company';


            if (! isValid($view->items['companyRent'], TEXT_FLOAT, true)) {
                $validationErrors[] = 'You have not provided a valid Rent Amount';
            }
            if (! isValid($view->items['companyInsurance'], TEXT_FLOAT, true)) {
                $validationErrors[] = 'You have not provided a valid Insurance Amount';
            }
            if (! isValid($view->items['companyUtilities'], TEXT_FLOAT, true)) {
                $validationErrors[] = 'You have not provided a valid Utilities Amount';
            }
            if (! isValid($view->items['companySubscription'], TEXT_FLOAT, true)) {
                $validationErrors[] = 'You have not provided a valid Subscription Amount';
            }
            if (! isValid($view->items['companyGovernment'], TEXT_FLOAT, true)) {
                $validationErrors[] = 'You have not provided a valid Government Fee';
            }
            if (! isValid($view->items['companyParking'], TEXT_FLOAT, true)) {
                $validationErrors[] = 'You have not provided a valid Car Parking Amount';
            }
            if (! isValid($view->items['companyOthers'], TEXT_FLOAT, true)) {
                $validationErrors[] = 'You have not provided a valid Company Others Amount';
            }

            if (! isValid($view->items['trustSalary'], TEXT_FLOAT, true)) {
                $validationErrors[] = 'You have not provided a valid Salary Amount';
            }
            if (! isValid($view->items['trustSuperAnnuation'], TEXT_FLOAT, true)) {
                $validationErrors[] = 'You have not provided a valid Superannuation Amount';
            }
            if (! isValid($view->items['trustBonuses'], TEXT_FLOAT, true)) {
                $validationErrors[] = 'You have not provided a valid Bonuses Amount';
            }
            if (! isValid($view->items['trustOthers'], TEXT_FLOAT, true)) {
                $validationErrors[] = 'You have not provided a valid ' . ucwords(strtolower($_SESSION['country_default']['trust_account'])) . 'ing Cost Others Amount';
            }

            if (! $view->items['trustFTE']) {
                $validationErrors[] = 'You have not provided FTEs for ' . ucwords(strtolower($_SESSION['country_default']['trust_account'])) . 'ing Cost';
            } elseif (! isValid($view->items['trustFTE'], TEXT_FLOAT, true)) {
                $validationErrors[] = 'You have not provided a valid FTEs for ' . ucwords(strtolower($_SESSION['country_default']['trust_account'])) . 'ing Cost';
            } elseif (($view->items['trustFTE'] < 1)) {
                $validationErrors[] = 'You have not provided a valid FTEs for ' . ucwords(strtolower($_SESSION['country_default']['trust_account'])) . 'ing Cost';
            }

            if (! isValid($view->items['trustIT'], TEXT_FLOAT, true)) {
                $validationErrors[] = 'You have not provided a valid IT Amount';
            }
            if (! isValid($view->items['trustClerical'], TEXT_FLOAT, true)) {
                $validationErrors[] = 'You have not provided a valid Clerical Support Amount';
            }
            if (! isValid($view->items['trustMisc'], TEXT_FLOAT, true)) {
                $validationErrors[] = 'You have not provided a valid Other Misc. Amount';
            }






            if (noErrors($validationErrors)) {
                $data['year'] = $view->items['year'];
                $data['fteCompany'] = $view->items['fteCompany'];
                $data['companyRent'] = $view->items['companyRent'] ? $view->items['companyRent'] : 0;
                $data['companyInsurance'] = $view->items['companyInsurance'] ? $view->items['companyInsurance'] : 0;
                $data['companyUtilities'] = $view->items['companyUtilities'] ? $view->items['companyUtilities'] : 0;
                $data['companySubscription'] = $view->items['companySubscription'] ? $view->items['companySubscription'] : 0;
                $data['companyGovernment'] = $view->items['companyGovernment'] ? $view->items['companyGovernment'] : 0;
                $data['companyParking'] = $view->items['companyParking'] ? $view->items['companyParking'] : 0;
                $data['companyOthers'] = $view->items['companyOthers'] ? $view->items['companyOthers'] : 0;
                $data['companyTotal'] = $view->items['companyTotal'] ? $view->items['companyTotal'] : 0;
                $data['companyTotalCost'] = $view->items['companyTotalCost'] ? $view->items['companyTotalCost'] : 0;
                $data['trustSalary'] = $view->items['trustSalary'] ? $view->items['trustSalary'] : 0;
                $data['trustSuperAnnuation'] = $view->items['trustSuperAnnuation'] ? $view->items['trustSuperAnnuation'] : 0;
                $data['trustBonuses'] = $view->items['trustBonuses'] ? $view->items['trustBonuses'] : 0;
                $data['trustOthers'] = $view->items['trustOthers'] ? $view->items['trustOthers'] : 0;
                $data['trustTotal'] = $view->items['trustTotal'] ? $view->items['trustFTE'] : 0;
                $data['trustFTE'] = $view->items['trustFTE'] ? $view->items['trustFTE'] : 0;
                $data['trustCost'] = $view->items['trustCost'] ? $view->items['trustCost'] : 0;
                $data['trustTotalCost'] = $view->items['trustTotalCost'] ? $view->items['trustTotalCost'] : 0;
                $data['trustIT'] = $view->items['trustIT'] ? $view->items['trustIT'] : 0;
                $data['trustClerical'] = $view->items['trustClerical'] ? $view->items['trustClerical'] : 0;
                $data['trustMisc'] = $view->items['trustMisc'] ? $view->items['trustMisc'] : 0;
                $data['trustOverallTotal'] = $view->items['trustOverallTotal'] ? $view->items['trustOverallTotal'] : 0;

                dbUpdateCostCompany($data, $view->items['linked_id']);

                dbInsertCostCompany($data, $view->items['linked_id']);
                $view->items['statusMessage'] = 'Cost has been successfully updated.';

            }
            break;
        case 'changeYear':
            $view->items['fteCompany'] = '';
            $view->items['companyRent'] = '';
            $view->items['companyInsurance'] = '';
            $view->items['companyUtilities'] = '';
            $view->items['companySubscription'] = '';
            $view->items['companyGovernment'] = '';
            $view->items['companyParking'] = '';
            $view->items['companyOthers'] = '';
            $view->items['companyTotal'] = '';
            $view->items['companyTotalCost'] = '';
            $view->items['trustSalary'] = '';
            $view->items['trustSuperAnnuation'] = '';
            $view->items['trustBonuses'] = '';
            $view->items['trustOthers'] = '';
            $view->items['trustTotal'] = '';
            $view->items['trustFTE'] = '';
            $view->items['trustCost'] = '';
            $view->items['trustTotalCost'] = '';
            $view->items['trustIT'] = '';
            $view->items['trustClerical'] = '';
            $view->items['trustMisc'] = '';
            $view->items['trustOverallTotal'] = '';
            break;
    }

    $view->items['LinkedDatabaseListMode'] =  dbGetLinkedDatabase();
    if (count($view->items['LinkedDatabaseListMode']) == 1) {
        $view->items['linked_id'] = $clientDB;
        $view->items['LinkedDatabaseListMode'] = [];
    }
    $view->items['portfolioListMode'] =  (dbGetParams('PORTMGR', true, false, '', $view->items['linked_id']));

    if (! isset($view->items['year'])) {
        $view->items['year'] = date('Y');
    }

    $year = $view->items['year'];
    $range = 3;
    for ($i = $year - $range; ($i - $year) < $range; $i++) {
        $yearList[$i] = $i;
    }
    $view->items['yearList'] = $yearList;


    if ($view->items['year']) {
        $rec = dbGetCostCompany($view->items['year'], $view->items['linked_id']);
        $view->bindAttributesFrom($rec);
    }


    // -- add the validation errors to the view and render
    $view->items['validationErrors'] = $validationErrors;
    $view->render();


}

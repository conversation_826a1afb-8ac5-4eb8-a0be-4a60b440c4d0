<?php

function calculateBalances($properties, $basis, $periodFrom, $yearFrom, $periodTo, $yearTo, $accounts, &$balances, $suffix, $modalType)
{
    switch ($basis) {
        case BASIS_CASH:
            $node = 'Cash';
            break;
        case BASIS_ACCRUALS:
            $node = 'Accruals';
            break;
    }

    switch ($modalType) {
        case 1:
        case 4:
            $generalLedger = dbGetLedgerForProperties($properties, $basis, $periodFrom, $yearFrom, $periodTo ? $periodTo : $periodFrom, $yearTo, $accounts);

            $_balanceNode = 'opening' . $node;
            $output = [];

            foreach ($generalLedger as $t) {
                if (! isset($balances[$t['accountID']])) {
                    $o = dbGetOpeningTrialBalanceByAccount($t['propertyID'], $t['accountID'], $periodFrom, $yearFrom);
                    $balances[$t['accountID']] = $o['balance' . $node];
                }
                $row = $t;
                if ($t['transactionAmount'] != 0) {
                    if ($t['transactionAmount'] < 0) {
                        $row['credit' . $suffix] = abs($t['transactionAmount']);
                    } else {
                        $row['debit' . $suffix] = abs($t['transactionAmount']);
                    }
                    $row['current' . $suffix] = $t['transactionAmount'];
                }
                $row['balance'] = bcadd($balances[$t['accountID']], $t['transactionAmount']);
                $balances[$t['accountID']] = $row['balance'];

                $output[$t['accountID']][] = $row;
            }
            break;
        case 2:
            $output = dbGetBalancePerAccountCodePerPeriod($properties, $yearTo, null, $periodTo ? $periodTo : $periodFrom, $accounts, $basis, 1);
            break;
        case 3:
            $output = dbGetBalancePerAccountCodePerPeriod($properties, $yearTo, $periodFrom, $periodTo ? $periodTo : $periodFrom, $accounts, $basis, 2);
            break;
    }

    return $output;
}

function generalLedgerModal($context)
{
    global $clientDirectory, $pathPrefix, $pdf, $sess;

    $view = new UserControl(userViews(), '/managementReports/generalLedgerModal.html');

    $view->bindAttributesFrom($_REQUEST);

    $properties = explode('::', $view->items['property']);
    switch ($view->items['basis']) {
        case BASIS_CASH:
            $node = 'Cash';
            $basis = 'Cash Basis';
            break;
        case BASIS_ACCRUALS:
            $node = 'Accruals';
            $basis = 'Accruals Basis';
            break;
    }

    $totals =  [];

    $accountList = dbGetAccountList();
    $accountLookup = mapParameters($accountList, 'accountID', 'accountName');
    $accountTypeLookup = mapParameters($accountList, 'accountID', 'accountType');

    $accountGroups =
    [
        INCOME => 'Income',
        EXPENDITURE => 'Expenditure',
        BALANCESHEET => 'Balance Sheet',
    ];

    $modalType = $view->items['modalType'];
    $propFundID = $view->items['propFundID'] ?? '';

    if ($modalType == 4) {
        $specialAccounts = [];
        if (isset($view->items['OwnerRemittanceAndBalanceSheet'])) {
            $specialAccounts = getOwnerRemittanceAndBalanceSheetAccounts();
        }

        $accountGroup2Accounts = dbGetClosingTrialBalanceBreakdownForPeriod($properties, $view->items['period'], $view->items['year'], $view->items['accountGroup2'], $specialAccounts);
        $accountGroup2AccountsArray =  [];
        if ($accountGroup2Accounts) {
            foreach ($accountGroup2Accounts as $accountGroup2Account) {
                $accountGroup2AccountsArray[] = $accountGroup2Account['accountCode'];
            }
            $accounts = $accountGroup2AccountsArray;
        } else {
            $accounts =  [];
        }


        if (count($properties ?? []) == 1 and $propFundID == '') {
            $excludedAccts = dbGetPropertyFundAccounts($properties[0]);
            $accounts = array_diff($accounts, $excludedAccts);
        } elseif (count($properties ?? []) == 1 and $propFundID != '') {
            $includedAccts = dbGetPropertyFundAccounts($properties[0]);
            $accounts = array_intersect($accounts, $includedAccts);
        }
    } else {
        $accounts = explode('::', $view->items['accounts']);
    }
    $balances = [];
    $totals =  [];
    $subHeader = [];
    $receivablesCount = 0;

    $data = ($modalType == 3)
        ? calculateBalances($properties, $view->items['basis'], $view->items['period'], $view->items['year'], $view->items['periodTo'], $view->items['year'], $accounts, $balances, null, $modalType)
        : calculateBalances($properties, $view->items['basis'], $view->items['period'], $view->items['year'], $view->items['periodTo2'] ? $view->items['periodTo2'] : $view->items['periodTo'], $view->items['year'], $accounts, $balances, null, $modalType);


    switch ($modalType) {
        case 1:
        case 4:
            foreach ($data as $account => $list) {
                $openingBalance = dbGetOpeningBalancesPerAccount($properties, $view->items['year'], $account, $view->items['period'], $accountTypeLookup[$account]);
                $subHeader[$account]['accountName'] = $accountLookup[$account];
                $subHeader[$account]['openingBalance'] = bcmul(1, $openingBalance['balance' . $node], 2);

                total($list, $totals[$account], ['credit', 'debit', 'creditPY', 'debitPY', 'current', 'currentPY']);
                $totals[$account]['balance'] = $balances[$account];
            }

            // Count AR entries (for the Unit column display)
            foreach ($data as $value) {
                foreach ($value as $val) {
                    if (array_key_exists('source', $val) && $val['source'] == 'AR') {
                        if (isset($view->items['unit'])) {
                            if (isset($view->items['OwnerRemittanceAndBalanceSheet'])) {
                            } else {
                                $receivablesCount++;
                            }
                        }
                    }
                }
            }
            break;
        case 2:
        case 3:
            $ytdTotal = 0;
            foreach ($data as $ytdAmount) {
                $subHeader['ytd']['accountCode'] = $ytdAmount['accountID'];
                $subHeader['ytd']['accountName'] = $ytdAmount['accountName'];
                $ytdTotal += $ytdAmount['balance'];
            }
            $totals['ytd']['balance'] = $ytdTotal;
            break;
    }

    $view->items['basisList'] =
    [
        BASIS_CASH => 'Cash',
        BASIS_ACCRUALS => 'Accruals',
    ];

    $view->items['data'] = $data;
    $view->items['accountGroups'] = $accountGroups;
    $view->items['subHeader'] = $subHeader;
    $view->items['totals'] = $totals;
    $view->items['receivablesCount'] = $receivablesCount;

    $view->render();
}

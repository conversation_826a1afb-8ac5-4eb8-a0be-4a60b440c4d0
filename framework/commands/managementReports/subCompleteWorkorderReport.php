<?php

$property_id = $propertyID;

$include_logo = false;
if (isset($view->items['logo']) && (int) $view->items['logo'] == 1) {
    $include_logo = true;
}

$d = explode('/', $periodFrom);
$from_date = $d[2] . '-' . $d[1] . '-' . $d[0];
$d = explode('/', $periodTo);
$to_date = $d[2] . '-' . $d[1] . '-' . $d[0];

// SET THE NEEDED PARAMS
$params = [
    'client_id'     => $_SESSION['databaseID'],
    'client_db'     => $_SESSION['currentDB'],
    'currentDB'     => $_SESSION['currentDB'],
    'trans_type'    => 'workorder',
    'user'          => $_SESSION['user_name'],
    'un'            => $_SESSION['user_name'],
    'user_type'     => $_SESSION['user_type'],
    'owner_template' => 1,
    'use_client_logo' => ($include_logo ? 1 : 0),
    'has_logo'        => ($include_logo ? 1 : 0),
];
$params['filters'] = json_encode([
    'property_code'     => [$property_id],
    'target_date_enabled' => 0,
    'service_id'          => null,
    'created_at_enabled'  => 1,
    'owner_workorder_type' => 'complete',
    'ordered_at'          => ['from' => $from_date, 'to' => $to_date, 'only_to_date' => 1],
]);
$params['options'] = json_encode([
    'format'        => 'pdf',
    'group_by'      => null,
    'sort_by'       => 'complete_date',
    'sort_type'     => 'asc',
]);

// USE cURL Request
$url = c8_api . 'fm/reports/generate';
$headers = [
    'Accept: application/json',
    'Authorization: Bearer ' . $_SESSION['sysApi'],
];

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_POST, 1);
curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
curl_setopt($ch, CURLOPT_POSTFIELDS, $params);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
$result = curl_exec($ch);
curl_close($ch);
$pdf_data = json_decode(base64_decode($result), true);

if (! empty($pdf_data) && isset($pdf_data['file']['data'])) {
    $content = base64_decode($pdf_data['file']['data']);

    $timestamp = date('dmYHis');
    $ext = '.pdf';
    $name = 'workorder_report_';

    // SET DIRECTORY
    if (! file_exists("{$pathPrefix}{$clientDirectory}/pdf")) {
        mkdir("{$pathPrefix}{$clientDirectory}/pdf", FILE_PERMISSION);
    }
    if (! file_exists("{$pathPrefix}{$clientDirectory}/pdf/Workorders")) {
        mkdir("{$pathPrefix}{$clientDirectory}/pdf/Workorders", FILE_PERMISSION);
    }

    // $directory = "{$pathPrefix}{$clientDirectory}/pdf/" . DOC_OWNERSTATEMENT. "/workorderReport/";
    $directory = "{$pathPrefix}{$clientDirectory}/pdf/Workorders/workorderReport/";
    if (! file_exists($directory)) {
        mkdir($directory, FILE_PERMISSION);
    }
    // put contents on file
    $file = $directory . $name . $timestamp . $ext;
    file_put_contents($file, $content);
    //
    if (file_exists($file) && filesize($file) > 1052 && isset($pdf)) {
        // $pdf is the main PDF where this report will be attached.
        $pdi = new ImportedPage($pdf, $file, 2);
        $i = 1;
        while ($pdi->loadPage($i)) {
            $page++;
            $pdi->preparePage();
            $pdi->render();
            $pdi->endPage();
            $i++;
        }
        $pdi->close();
    }
    $jasperInclude[] = $file;
}

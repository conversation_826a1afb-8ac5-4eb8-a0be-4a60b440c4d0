<?php

function calculateBalances($properties, $accountGroup, $node, $periodFrom, $yearFrom, $periodTo, $yearTo, $suffix, $actualYear, $owner)
{
    $retainedEarnings = dbGetGlLinkedAccountsColumnValue('retainedEarningsStartFY');
    $retainedEarningsCode = $retainedEarnings['retrievedColumn'];

    $retainedEarningsCurrent = dbGetGlLinkedAccountsColumnValue('retainedEarningsCurrentFY');
    $retainedEarningsCurrentCode = $retainedEarningsCurrent['retrievedColumn'];

    if ($owner) {
        $trialBalance = dbGetTrialBalanceForPropertiesByAccountGroupOwner($properties, $accountGroup, $periodFrom, $yearFrom, $periodTo, $yearTo, $retainedEarningsCode, $owner);
    } else {
        $trialBalance = dbGetTrialBalanceForPropertiesByAccountGroup($properties, $accountGroup, $periodFrom, $yearFrom, $periodTo, $yearTo, $retainedEarningsCode);
    }


    $_amountNode = 'balance' . $node;
    $_balanceNode = 'opening' . $node;
    $output = [];
    foreach ($trialBalance as $t) {
        $row = [];
        $row['ownerAccountCode'] = $t['ownerAccountCode'];
        $row['ownerAccountDesc'] = $t['ownerAccountDesc'];
        $row['propertyID'] = $t['propertyID'];
        $row['accountID'] = $t['accountID'];
        $row['description'] = $t['accountName'];
        $row['accountGroup2'] = $t['accountGroup2'];
        $row[$_amountNode] = $t[$_amountNode];
        if ($row[$_amountNode] != 0) {
            if ($row[$_amountNode] < 0) {
                $row['credit' . $suffix] = abs($t[$_amountNode]);
            } else {
                $row['debit' . $suffix] = abs($t[$_amountNode]);
            }
            $row['current' . $suffix] = $t[$_amountNode];
        }

        if (($accountGroup == 'B') && (in_array($t['accountID'], [$retainedEarningsCode, $retainedEarningsCurrentCode]))) {
            $row['openingBalance' . $suffix] = $t[$_balanceNode];
        } else {
            $openingBalances = dbGetOpeningBalancesPerAccount($properties, $actualYear, $t['accountID'], $periodFrom, $accountGroup);
            $row['openingBalance' . $suffix] = $openingBalances[$_amountNode];
        }

        $_ytd = $t[$_amountNode] + $row['openingBalance' . $suffix];
        if ($_ytd == 0) {
            $row['ytdCredit' . $suffix] = 0.00;
            $row['ytdDebit' . $suffix] = 0.00;
        } elseif ($_ytd < 0) {
            $row['ytdCredit' . $suffix] = abs($_ytd);
        } else {
            $row['ytdDebit' . $suffix] = abs($_ytd);
        }
        $row['ytd' . $suffix] = $_ytd;
        $output[] = $row;
    }

    return $output;
}


function trialBalanceProcess(&$context)
{
    $threshold = 3;
    global $sess, $clientDirectory, $pathPrefix, $propertyID, $pdf;

    $formData = $context;
    $validationErrors = [];

    // -- need to expand the definitions

    // if its a task
    // if its a normal run
    // if its an inclusion run (like management reports)


    if ($context[IS_TASK]) {
        extract($context);
    } else {
        $count = 1;
        $formData['propertyCount'] = $count;
        $queue = new Queue(TASKTYPE_TRIAL_BALANCE);
        if ($count > THRESHOLD_TRIALBALANCE) {
            $queue->add($_SESSION['clientID'], $_SESSION['un'], 'command=trialBalanceProcess&module=mangementReports', $_REQUEST);
        }
    }

    if (($context[IS_TASK]) || ($count <= THRESHOLD_TRIALBALANCE)) {
        // -- either a single property, or multiple properties may be passed
        // -- similarly, check to see whether a PDF has been passed in $context

        if ($formData['property']) {
            $properties = $formData['property'];
        } else {
            $properties = [$propertyID];
        }

        if (! is_array($properties)) {
            $properties = [$properties];
        }


        if ($properties) {
            foreach ($properties as $propertyID) {
                // $propertyID = $view->items['property'];
                $cal = dbGetCalendarForProperty($propertyID);
                $calendarName = $cal['calendarName'];
                $_calendar[$cal['calendarName']][] = $propertyID;
                $formData['calendarName'] = $calendarName;
            }
        }

        $actualStartDate = $formData['startDate'];
        $actualEndDate = $formData['endDate'];

        $startDate = dbGetMasterCalendarPeriodForDate($formData['calendarName'], $formData['startDate']);
        $endDate = dbGetMasterCalendarPeriodForDate($formData['calendarName'], $formData['endDate']);

        $date = $formData['endDate'];
        $format = $formData['format'];
        // $format = FILETYPE_PDF;
        $priorYear = true;

        // ** HANDLE PROCESSING LOGIC
        switch ($formData['basis']) {
            case BASIS_CASH:
                $node = 'Cash';
                $basis = 'Cash Basis';
                break;
            case BASIS_ACCRUALS:
                $node = 'Accruals';
                $basis = 'Accruals Basis';
                break;
        }

        $lastPeriod = periodBefore($startDate['period'], $startDate['year']);
        $lastYear = $endDate['year'] - 1;
        $totals = [];

        $accountGroups = [
            INCOME => 'Income',
            EXPENDITURE => 'Expenditure',
            BALANCESHEET => 'Balance Sheet'];

        $totals = [];
        $subtotals = [];
        $data = [];
        foreach ($accountGroups as $accountType => $accountGroupName) {
            $balances = calculateBalances($properties, $accountType, $node, $startDate['period'], $startDate['year'], $endDate['period'], $endDate['year'], null, $endDate['year'], $formData['ownerID']);
            if ($balances) {
                foreach ($balances as $b) {
                    $data[$accountType][$b['accountID']] = $b;
                }
            }

            total($data[$accountType], $totals, ['credit', 'debit', 'ytdCredit', 'ytdDebit', 'openingBalance', 'creditPY', 'debitPY', 'ytdCreditPY', 'ytdDebitPY', 'openingBalancePY', 'ytd', 'ytdPY', 'current', 'currentPY']);
            total($data[$accountType], $subtotals[$accountType], ['credit', 'debit', 'ytdCredit', 'ytdDebit', 'openingBalance', 'creditPY', 'debitPY', 'ytdCreditPY', 'ytdDebitPY', 'openingBalancePY', 'ytd', 'ytdPY', 'current', 'currentPY']);
            $subtotals[$accountType]['description'] = 'Total ' . $accountGroupName;
            $subtotals[$accountType]['ownerAccountDesc'] = 'Total ' . $accountGroupName;
            if (is_array($data[$accountType])) {
                ksort($data[$accountType]);
            }
        }

        $plTotal = [];
        sum($subtotals[INCOME], $plTotal, ['credit', 'debit', 'ytdCredit', 'ytdDebit', 'openingBalance', 'creditPY', 'debitPY', 'ytdCreditPY', 'ytdDebitPY', 'openingBalancePY', 'ytd', 'ytdPY', 'current', 'currentPY']);
        sum($subtotals[EXPENDITURE], $plTotal, ['credit', 'debit', 'ytdCredit', 'ytdDebit', 'openingBalance', 'creditPY', 'debitPY', 'ytdCreditPY', 'ytdDebitPY', 'openingBalancePY', 'ytd', 'ytdPY', 'current', 'currentPY']);
        $totals['description'] = 'Total';
        $totals['ownerAccountDesc'] = 'Total';

        $formData['accountGroups'] = $accountGroups;
        $formData['data'] = $data;
        $formData['subtotals'] = $subtotals;
        $formData['totals'] = $totals;

        if (! $context[DOC_MASTER]) {
            if (! $context['forOwnerReport'] || ($context['forOwnerReport'] && $context['logo'])) {
                $logoFile = dbGetClientLogo();
                $logoPath = "assets/clientLogos/{$logoFile}";
            }

            $_filePath = "{$pathPrefix}{$clientDirectory}/{$format}/" . DOC_TRIALBALANCE . '/';
            $_downloadPath = "{$clientDirectory}/{$format}/" . DOC_TRIALBALANCE;
            if (count($properties ?? []) < 2) {
                $file = 'TrialBalance_' . $properties[0] . '_' . date('dmYHis') . ".{$format}";
            } else {
                $file = 'TrialBalance_Multiple_' . date('dmYHis') . ".{$format}";
            }
            $filePath = $_filePath . $file;
            $downloadPath = "{$_downloadPath}/{$file}";
        }


        $includes = dbGetSubReports($formData['reportID'], true);

        foreach ($includes as $i) {
            include $i['subReportFile'];
        }

        // -- if it s a scheduled task and not part of a bigger report - attach the report and email to the requester
        if (($context[IS_TASK]) && (! $context[DOC_MASTER]) && ! $context['forOwnerReport']) {
            $email = new EmailTemplate('views/emails/basicEmail.html', SYSTEMURL);
            $attachment = [];
            $attachment[0]['file'] = $filePath;
            $attachment[0]['content_type'] = 'application/pdf';
            sendMail($_SESSION['email'], $_SESSION['first_name'] . ' ' . $_SESSION['last_name'], $email->toString(), 'Report', $attachment);
            logData('Emailed report to ' . $_SESSION['email']);
            $context[TASK_COMPLETE] = true;
        }
    }

    if ($context['forOwnerReport']) {
        $context['ownerReportFile'] = $filePath;
    }

    // -- if the document doesnt form part of a bigger report
    if (! $context[DOC_MASTER]) {
        if ($count > THRESHOLD_TRIALBALANCE) {
            $formData['statusMessage'] = 'Due to its complexity, this report has been scheduled and will be emailed to you on completion!';

            return $formData;
        }

        $formData['downloadPath'] = $downloadPath;
        if ($_SESSION['user_type'] == USER_OWNER) {
            $_SESSION['downloadFile'] = $downloadPath;
        }

        return $formData;
    }
}

<?php

function balanceSheetProcess(&$context)
{
    global $sess, $clientDirectory, $pathPrefix, $propertyID, $pdf;

    $formData = $context;
    $validationErrors =  [];

    if ($context[IS_TASK]) {
        extract($context);
    } else {
        $count = 1;
        $formData['propertyCount'] = $count;
        $queue = new Queue(TASKTYPE_BALANCE_SHEET);
        if ($count > THRESHOLD_BALANCESHEET) {
            $queue->add($_SESSION['clientID'], $_SESSION['un'], 'command=balanceSheetProcess&module=mangementReports', $_REQUEST);
        }
    }

    if (($context[IS_TASK]) || ($count <= THRESHOLD_BALANCESHEET)) {
        if ($formData['property']) {
            $properties = $formData['property'];
        } else {
            $properties =  [$propertyID];
        }

        $chosenBalanceDate = $formData['balanceDate'];
        $balanceDate = dbGetMasterCalendarPeriodForDate($formData['calendarName'], $formData['balanceDate']);
        $balanceDatePeriod = $balanceDate['period'];
        $balanceDateYear = $balanceDate['year'];

        $format = $formData['format'];

        switch ($formData['basis']) {
            case BASIS_CASH:
                $node = 'Cash';
                $basis = 'Cash Basis';
                break;
            case BASIS_ACCRUALS:
                $node = 'Accruals';
                $basis = 'Accruals Basis';
                break;
        }

        $compareTo = $formData['compareTo'];
        $comparePeriods = $formData['comparePeriods'];

        $data = [];
        $dateHeaders = [];
        $subTotal = [];
        $accountGroup1Total = [];
        $netAssetTotal = [];

        $accountGroups1 =  ['ASS' => 'Assets',
            'LIA' => 'Liabilities',
            'EQU' => 'Equity'];
        $accountGroupReference = mapParameters(dbGetAllGLAccountGroups(), 'accountGroup', 'accountDescription');
        $retainedEarnings = dbGetGlLinkedAccountsColumnValue('retainedEarningsStartFY');
        $retainedEarningsCode = $retainedEarnings['retrievedColumn'];

        foreach ($accountGroups1 as $accountGroupsCode => $accountGroupsName) {

            $accountGroups2 = dbGetBalanceSheetDistinctAccountGroup2($properties, $accountGroupsCode);
            foreach ($accountGroups2 as $accountGroups2Code) {
                if ($formData['ownerID']) {
                    $mainBalance = dbGetOwnerBalanceSheetAmounts($properties, $balanceDateYear, $balanceDatePeriod, $accountGroupsCode, $accountGroups2Code['accountGroup2'], $retainedEarningsCode, $formData['ownerID']);
                } else {
                    $mainBalance = dbGetBalanceSheetAmounts($properties, $balanceDateYear, $balanceDatePeriod, $accountGroupsCode, $accountGroups2Code['accountGroup2'], $retainedEarningsCode);
                }
                $dateHeader = dbGetCalendarDetailsPerPeriodYear($balanceDateYear, $balanceDatePeriod, 106, $properties[0]);
                $dateHeaders[0] = $dateHeader['endDate'];
                if ($mainBalance) {
                    foreach ($mainBalance as $a) {
                        if ($accountGroupsCode != 'ASS') {
                            $balanceAmount = ($a['balance' . $node] * -1);
                        } else {
                            $balanceAmount = $a['balance' . $node];
                        }

                        $data[$accountGroupsCode][$accountGroups2Code['accountGroup2']][$a['accountCode']][0]['balance'] = $balanceAmount;
                        $data[$accountGroupsCode][$accountGroups2Code['accountGroup2']][$a['accountCode']][0]['accountName'] = $formData['ownerID'] ? $a['ownerAccountDesc'] : $a['accountName'];
                        $data[$accountGroupsCode][$accountGroups2Code['accountGroup2']][$a['accountCode']][0]['accountCode'] = $formData['ownerID'] ? $a['ownerAccountCode'] : $a['accountCode'];
                        if (! $subTotal[$accountGroupsCode][$accountGroups2Code['accountGroup2']][0]['subtotal']) {
                            $subTotal[$accountGroupsCode][$accountGroups2Code['accountGroup2']][0]['subtotal'] = $balanceAmount;
                        } else {
                            $subTotal[$accountGroupsCode][$accountGroups2Code['accountGroup2']][0]['subtotal'] = ($subTotal[$accountGroupsCode][$accountGroups2Code['accountGroup2']][0]['subtotal'] + $balanceAmount);
                        }
                        if (! $accountGroup1Total[$accountGroupsCode][0]['subtotal']) {
                            $accountGroup1Total[$accountGroupsCode][0]['subtotal'] = $balanceAmount;
                        } else {
                            $accountGroup1Total[$accountGroupsCode][0]['subtotal'] = ($accountGroup1Total[$accountGroupsCode][0]['subtotal'] + $balanceAmount);
                        }

                        $figureChecking = floatval($balanceAmount);
                        if (! empty($figureChecking)) {
                            $data[$accountGroupsCode][$accountGroups2Code['accountGroup2']][$a['accountCode']]['display'] = true;
                        } else {
                            $data[$accountGroupsCode][$accountGroups2Code['accountGroup2']][$a['accountCode']]['display'] = false;
                        }
                    }
                }

                if ($compareTo != 4) {
                    if ($comparePeriods > 0) {
                        $year = $balanceDateYear;
                        $date = $chosenBalanceDate;
                        for ($i = 1; $i <= $comparePeriods; $i++) {
                            switch ($compareTo) {
                                case 0:
                                    $period = $balanceDatePeriod;
                                    $year = ($year - 1);
                                    break;
                                case 1:
                                    $period = 12;
                                    $year = ($year - 1);
                                    break;
                                case 2:
                                    $dateQuery = dbComputeDates('month', -3, $date);
                                    $date = $dateQuery['computedDate'];

                                    $newdate = dbGetMasterCalendarPeriodForDate($formData['calendarName'], $date);
                                    $period = $newdate['period'];
                                    $year = $newdate['year'];
                                    break;
                                case 3:
                                    $dateQuery = dbComputeDates('month', -1, $date);
                                    $date = $dateQuery['computedDate'];

                                    $newdate = dbGetMasterCalendarPeriodForDate($formData['calendarName'], $date);
                                    $period = $newdate['period'];
                                    $year = $newdate['year'];
                                    break;
                            }
                            $comparisons = dbGetBalanceSheetAmounts($properties, $year, $period, $accountGroupsCode, $accountGroups2Code['accountGroup2'], $retainedEarningsCode);
                            $dateHeader = dbGetCalendarDetailsPerPeriodYear($year, $period, 106, $properties[0]);
                            $dateHeaders[$i] = $dateHeader['endDate'];
                            if ($comparisons) {
                                foreach ($comparisons as $a) {
                                    if ($accountGroupsCode != 'ASS') {
                                        $balanceAmount = ($a['balance' . $node] * -1);
                                    } else {
                                        $balanceAmount = $a['balance' . $node];
                                    }

                                    $data[$accountGroupsCode][$accountGroups2Code['accountGroup2']][$a['accountCode']][$i]['balance'] = $balanceAmount;
                                    $data[$accountGroupsCode][$accountGroups2Code['accountGroup2']][$a['accountCode']][$i]['accountName'] = $a['accountName'];
                                    $data[$accountGroupsCode][$accountGroups2Code['accountGroup2']][$a['accountCode']][$i]['accountCode'] = $a['accountCode'];
                                    if (! $subTotal[$accountGroupsCode][$accountGroups2Code['accountGroup2']][$i]['subtotal']) {
                                        $subTotal[$accountGroupsCode][$accountGroups2Code['accountGroup2']][$i]['subtotal'] = $balanceAmount;
                                    } else {
                                        $subTotal[$accountGroupsCode][$accountGroups2Code['accountGroup2']][$i]['subtotal'] = ($subTotal[$accountGroupsCode][$accountGroups2Code['accountGroup2']][$i]['subtotal'] + $balanceAmount);
                                    }
                                    if (! $accountGroup1Total[$accountGroupsCode][$i]['subtotal']) {
                                        $accountGroup1Total[$accountGroupsCode][$i]['subtotal'] = $balanceAmount;
                                    } else {
                                        $accountGroup1Total[$accountGroupsCode][$i]['subtotal'] = ($accountGroup1Total[$accountGroupsCode][$i]['subtotal'] + $balanceAmount);
                                    }

                                    if (! $data[$accountGroupsCode][$accountGroups2Code['accountGroup2']][$a['accountCode']]['display']) {
                                        $figureChecking = floatval($balanceAmount);
                                        if (! empty($figureChecking)) {
                                            $data[$accountGroupsCode][$accountGroups2Code['accountGroup2']][$a['accountCode']]['display'] = true;
                                        } else {
                                            $data[$accountGroupsCode][$accountGroups2Code['accountGroup2']][$a['accountCode']]['display'] = false;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }

        }

        $formData['data'] = $data;
        $formData['accountGroupReference'] = $accountGroupReference;
        $formData['dateHeaders'] = $dateHeaders;
        $formData['subTotal'] = $subTotal;
        $formData['accountGroup1Total'] = $accountGroup1Total;
        for ($i = 0; $i < count($dateHeaders ?? []); $i++) {
            $netAssetTotal[$i]['netAsset'] = ($accountGroup1Total['ASS'][$i]['subtotal'] - $accountGroup1Total['LIA'][$i]['subtotal']);
        }
        $formData['netAssetTotal'] = $netAssetTotal;

        if (! $context[DOC_MASTER]) {
            if (! $context['forOwnerReport'] || ($context['forOwnerReport'] && $context['logo'])) {
                $logoFile = dbGetClientLogo();
                $logoPath = "assets/clientLogos/{$logoFile}";
            }

            $_filePath =  "{$pathPrefix}{$clientDirectory}/{$format}/" . DOC_BALANCESHEET . '/';

            $_downloadPath =  "{$clientDirectory}/{$format}/" . DOC_BALANCESHEET;
            if (count($properties ?? []) < 2) {
                $file =  'BalanceSheet_' . $properties[0] . '_' . date('dmYHis') . ".{$format}";
            } else {
                $file =  'BalanceSheet_Multiple_' . date('dmYHis') . ".{$format}";
            }
            $filePath = $_filePath . $file;
            $downloadPath = "{$_downloadPath}/{$file}";
        }


        $includes = dbGetSubReports($formData['reportID'], true);

        if ($includes == '') {
            foreach ($includes as $i) {
                include $i['subReportFile'];
            }
        } else {
            include 'gl/balanceSheet.php';
        }


        if (($context[IS_TASK]) && (! $context[DOC_MASTER])  && ! $context['forOwnerReport']) {
            $email = new EmailTemplate('views/emails/basicEmail.html', SYSTEMURL);
            $attachment =  [];
            $attachment[0]['file'] = REPORTPATH . '/' . $pdfDownloadLink;
            $attachment[0]['content_type'] = 'application/pdf';
            sendMail($_SESSION['email'], $_SESSION['first_name'] . ' ' . $_SESSION['last_name'], $email->toString(), 'Report', $attachment);
            logData('Emailed report to ' . $_SESSION['email']);
            $context[TASK_COMPLETE] = true;
        }
    }

    if ($context['forOwnerReport']) {
        $context['ownerReportFile'] = $filePath;
    }

    if (! $context[DOC_MASTER]) {
        if ($count > THRESHOLD_BALANCESHEET) {
            $formData['statusMessage'] = 'Due to its complexity, this report has been scheduled and will be emailed to you on completion!';

            return $formData;
        } else {
            $formData['downloadPath'] = $downloadPath;
            if ($_SESSION['user_type'] == USER_OWNER) {
                $_SESSION['downloadFile'] = $downloadPath;
            }

            return $formData;
        }
    }
}

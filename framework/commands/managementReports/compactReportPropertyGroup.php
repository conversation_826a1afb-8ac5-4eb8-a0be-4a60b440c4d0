<?php

include_once 'Residential/residentialReportFunctions.php';
global $_fonts, $reportDescription;
global $clientDB;

if ($report['reportPeriod'] == 'D') {
    $periodFrom = $view->items['reportingPeriodFrom'];
    $periodTo = $view->items['reportingPeriodTo'];
}

if ($view->items['paymentDate'] != '') {
    $periodTo =  $view->items['paymentDate'];
    $periodFrom = dbGetNextDayOfPreviousOwnerPaymentDateMultipleProperties($properties, $periodTo);
}

$page++;
$line = 0;
$pdf->begin_page_ext(595, 842, '');
// REPORT HEADER
compactReportPropertyGroupHead($pdf, $line, $propertyGroup, $periodDescription, $logo, $logoPath);
// REPORT TITLE
compactReportPropertyGroupTitle($pdf, $line, $reportDescription, $periodFrom, $periodTo);
// REPORT DATA
$data = ['receipts' => [], 'payments' => [], 'opening_bal' => 0, 'remittances' => [], 'total_pmts_during' => 0];
$rent_paid_to = null;
if (isset($view->items['rentPaidTo'])) {
    $rent_paid_to = $view->items['rentPaidTo'];
}

compactReportPropertyGroupData($data, $properties, $periodFrom, $periodTo, $report['reportPeriod'], $view->items['datasource'], $view->items['filterProperty'], $rent_paid_to);

// PLOT THE PDF
// ###############################################################
// RECEIPTS
// SET DEFAULT STYLES AND COLUMNS
$columns = [
    ['field' => 'description', 'label' => 'Receipts:', 'width' => 20, 'length' => 450, 'align' => 'left', 'format' => 'text'],
    ['field' => 'from', 'label' => 'From', 'width' => 245, 'length' => 90, 'align' => 'left', 'format' => 'text'],
    ['field' => 'to', 'label' => 'To', 'width' => 295, 'length' => 90, 'align' => 'left', 'format' => 'text'],
    ['field' => 'invoice_no', 'label' => 'Invoice #', 'width' => 345, 'length' => 450, 'align' => 'left', 'format' => 'text'],
    ['field' => 'net', 'label' => 'Net ', 'width' => 350, 'length' => 90, 'align' => 'right', 'format' => 'num'],
    ['field' => 'gst', 'label' => $_SESSION['country_default']['tax_label'] . ' ', 'width' => 410, 'length' => 90, 'align' => 'right', 'format' => 'num'],
    ['field' => 'gross', 'label' => 'Gross ', 'width' => 480, 'length' => 90, 'align' => 'right', 'format' => 'num'],
];
$font_size = 8;

$receipts_total = ['net' => 0, 'gst' => 0, 'gross' => 0];
// $pdf->setFontExt($_fonts['Helvetica-Bold'], $font_size + 2);
// $pdf->showBoxed('Portfolio Receipts:', 20, 760 - $line, 590, 20, 'left', '');
foreach ($data['receipts'] as $tenant_code => $tenant) {

    extract(checkIfAddHeader($line, $_fonts, $page, $pdf, $logo, $logoPath, $periodFrom, $periodTo));
    $pdf->setFontExt($_fonts['Helvetica'], $font_size);

    // TENANT TITLE
    $line += 20;
    $pdf->setFontExt($_fonts['Helvetica-Bold'], $font_size);
    $pdf->showBoxed($tenant['description'] . ' - ' . $tenant['name'], 20, 760 - $line, 590, 20, 'left', '');

    // CHECK IF VACANT
    if (strtolower($tenant['name']) == 'vacant') {
        $line = $line + 12;
    }
    if (! $tenant['leaseID']) {
        $line = $line + 18;

        continue;
    }

    // CHARGES LIST
    if (isset($tenant['charges']) && count($tenant['charges']) > 0) {
        $pdf->setFontExt($_fonts['Helvetica'], $font_size);
        $line = $line + 12;
        $ctr = 1;
        $adjustX = 50;
        $charges_total = 0;
        foreach ($tenant['charges'] as $row) {
            if ($ctr == 1) {
                $pdf->setFontExt($_fonts['Helvetica-Bold'], $font_size);
                $pdf->showBoxed('Recurring Charges:', 20, 760 - $line, 590, 20, 'left', '');
            }

            $pdf->setFontExt($_fonts['Helvetica'], $font_size);
            $pdf->showBoxed($row['label'], 60 + $adjustX, 760 - $line, 590, 20, 'left', '');
            $pdf->showBoxed(formattingWithSpace($row['value']), 150 + $adjustX, 760 - $line, 90, 20, 'right', '');
            $pdf->showBoxed($row['charge_frequency'], 250 + $adjustX, 760 - $line, 40, 20, 'left', '');

            if ($row['paid_to']) {
                $pdf->showBoxed('Fully Paid To:', 300 + $adjustX, 760 - $line, 590, 20, 'left', '');
                $pdf->showBoxed($row['paid_to'], 355 + $adjustX, 760 - $line, 590, 20, 'left', '');
            }
            $charges_total += $row['value'];

            $line = $line + 10;
            $ctr++;

            extract(checkIfAddHeader($line, $_fonts, $page, $pdf, $logo, $logoPath, $periodFrom, $periodTo));
            $pdf->setFontExt($_fonts['Helvetica'], $font_size);
        }
        if (count($tenant['charges']) > 1) {
            $pdf->moveto(190 + $adjustX, 760 - $line + 18);
            $pdf->lineto(190 + $adjustX + 50, 760 - $line + 18);
            $pdf->stroke();
            $pdf->moveto(190 + $adjustX, 760 - $line + 18 - 14);
            $pdf->lineto(190 + $adjustX + 50, 760 - $line + 18 - 14);
            $pdf->stroke();
            $pdf->setFontExt($_fonts['Helvetica-Bold'], $font_size);
            $pdf->showBoxed(formattingWithSpace($charges_total), 150 + $adjustX, 760 - $line - 4, 90, 20, 'right', '');
            $pdf->setFontExt($_fonts['Helvetica'], $font_size);
            $line = $line + 10;
        }
    }
    // RECEIPTS LIST
    // COLUMN HEADERS
    $line += 12;
    $pdf->setFontExt($_fonts['Helvetica-Bold'], $font_size);
    foreach ($columns as $col) {
        $pdf->showBoxed($col['label'], $col['width'], 760 - $line, $col['length'], 20, $col['align'], '');
    }
    $line += 10;

    extract(checkIfAddHeader($line, $_fonts, $page, $pdf, $logo, $logoPath, $periodFrom, $periodTo));
    $pdf->setFontExt($_fonts['Helvetica'], $font_size);

    // RECEIPT ROWS
    $pdf->setFontExt($_fonts['Helvetica'], $font_size);
    if (isset($tenant['receipts']) && count($tenant['receipts']) > 0) {
        $totals = ['net' => 0, 'gst' => 0, 'gross' => 0];
        foreach ($tenant['receipts'] as $row) {
            // PLOT VALUES TO COLUMNS
            foreach ($columns as $col) {
                $value = $row[$col['field']] ?? ($col['format'] == 'num' ? 0 : '');
                if (($value != '' || $value == 0) && $col['format'] == 'num') {
                    $value = formattingWithSpace($value);
                }
                $pdf->showBoxed($value, $col['width'], 760 - $line, $col['length'], 20, $col['align'], '');
                // add to total receipts per tenant
                if (isset($totals[$col['field']])) {
                    $totals[$col['field']] += (float) $row[$col['field']];
                }
                // add to total overall
                if (isset($receipts_total[$col['field']])) {
                    $receipts_total[$col['field']] += (float) $row[$col['field']];
                }
            }
            //
            $line += 10;

            extract(checkIfAddHeader($line, $_fonts, $page, $pdf, $logo, $logoPath, $periodFrom, $periodTo));
            $pdf->setFontExt($_fonts['Helvetica'], $font_size);
        }
        $pdf->setFontExt($_fonts['Helvetica-Bold'], $font_size);
        $line = $line + 5;
        foreach ($columns as $col) {
            if (isset($totals[$col['field']])) {
                $pdf->showBoxed(formattingWithSpace($totals[$col['field']]), $col['width'], 760 - $line, $col['length'], 20, $col['align'], '');
            } elseif ($col['field'] == 'to') {
                $pdf->showBoxed('Total Receipts', $col['width'], 760 - $line, $col['length'], 20, $col['align'], '');
            }
        }
        $pdf->moveto(400, 781 - $line);
        $pdf->lineto(570, 781 - $line);
        $pdf->stroke();
    } else {
        $pdf->showBoxed('None', 20, 760 - $line, 190, 20, 'left', '');
        $line = $line + 15;
    }
    // ARREARS LIST
    $line += 12;
    $pdf->setFontExt($_fonts['Helvetica-Bold'], $font_size);
    $ctr = 1;
    foreach ($columns as $col) {
        $label = ' ';
        if ($ctr == 1) {
            $label = 'Arrears:';
        }
        $pdf->showBoxed($label, $col['width'], 760 - $line, $col['length'], 20, $col['align'], '');
        $ctr++;
    }
    $line += 10;

    extract(checkIfAddHeader($line, $_fonts, $page, $pdf, $logo, $logoPath, $periodFrom, $periodTo));
    $pdf->setFontExt($_fonts['Helvetica'], $font_size);

    if (isset($tenant['arrears']) && count($tenant['arrears']) > 0) {
        $totals = ['net' => 0, 'gst' => 0, 'gross' => 0];
        foreach ($tenant['arrears'] as $row) {
            // PLOT VALUES TO COLUMNS
            foreach ($columns as $col) {
                $value = $row[$col['field']] ?? ($col['format'] == 'num' ? 0 : '');
                if (($value != '' || $value == 0) && $col['format'] == 'num') {
                    $value = formattingWithSpace($value);
                }
                $pdf->showBoxed($value, $col['width'], 760 - $line, $col['length'], 20, $col['align'], '');
                // add to total arrears per tenant
                if (isset($totals[$col['field']])) {
                    $totals[$col['field']] += (float) $row[$col['field']];
                }
            }
            //
            $line += 10;

            extract(checkIfAddHeader($line, $_fonts, $page, $pdf, $logo, $logoPath, $periodFrom, $periodTo));
            $pdf->setFontExt($_fonts['Helvetica'], $font_size);
        }
        $pdf->setFontExt($_fonts['Helvetica-Bold'], $font_size);
        $line = $line + 5;
        foreach ($columns as $col) {
            if (isset($totals[$col['field']])) {
                $pdf->showBoxed(formattingWithSpace($totals[$col['field']]), $col['width'], 760 - $line, $col['length'], 20, $col['align'], '');
            } elseif ($col['field'] == 'to') {
                $pdf->showBoxed('Total Arrears', $col['width'], 760 - $line, $col['length'], 20, $col['align'], '');
            }
        }
        $pdf->moveto(400, 781 - $line);
        $pdf->lineto(570, 781 - $line);
        $pdf->stroke();
    } else {
        $pdf->showBoxed('None', 20, 760 - $line, 190, 20, 'left', '');
        $line = $line + 15;
    }
    // DEPOSITS LIST
    $line += 12;
    $pdf->setFontExt($_fonts['Helvetica-Bold'], $font_size);
    $ctr = 1;
    foreach ($columns as $col) {
        $label = ' ';
        if ($ctr == 1) {
            $label = 'Deposits/Pre Payments:';
        }
        $pdf->showBoxed($label, $col['width'], 760 - $line, $col['length'], 20, $col['align'], '');
        $ctr++;
    }
    $line += 10;

    extract(checkIfAddHeader($line, $_fonts, $page, $pdf, $logo, $logoPath, $periodFrom, $periodTo));
    $pdf->setFontExt($_fonts['Helvetica'], $font_size);

    if (isset($tenant['deposits']) && count($tenant['deposits']) > 0) {
        $totals = ['net' => 0, 'gst' => 0, 'gross' => 0];
        foreach ($tenant['deposits'] as $row) {
            // PLOT VALUES TO COLUMNS
            foreach ($columns as $col) {
                $value = $row[$col['field']] ?? ($col['format'] == 'num' ? 0 : '');
                if (($value != '' || $value == 0) && $col['format'] == 'num') {
                    $value = formattingWithSpace($value);
                }
                $pdf->showBoxed($value, $col['width'], 760 - $line, $col['length'], 20, $col['align'], '');
                // add to total deposits per tenant
                if (isset($totals[$col['field']])) {
                    $totals[$col['field']] += (float) $row[$col['field']];
                }
            }
            //
            $line += 10;

            extract(checkIfAddHeader($line, $_fonts, $page, $pdf, $logo, $logoPath, $periodFrom, $periodTo));
            $pdf->setFontExt($_fonts['Helvetica'], $font_size);
        }
        // $pdf->setFontExt($_fonts['Helvetica-Bold'], $font_size);
        // $line = $line + 5;
        // foreach ($columns as $col) {
        //     if (isset($totals[$col['field']])) {
        //         $pdf->showBoxed(formattingWithSpace($totals[$col['field']]), $col['width'], 760 - $line, $col['length'], 20, $col['align'], '');
        //     } else if ($col['field'] == 'to') {
        //         $pdf->showBoxed('Total Deposits', $col['width'], 760 - $line, $col['length'], 20, $col['align'], '');
        //     }
        // }
        // $pdf->moveto(400, 781 - $line);
        // $pdf->lineto(570, 781 - $line);
        // $pdf->stroke();
    } else {
        $pdf->showBoxed('None', 20, 760 - $line, 190, 20, 'left', '');
        $line = $line + 15;
    }
}
$line += 20;
$pdf->setFontExt($_fonts['Helvetica-Bold'], $font_size);
foreach ($columns as $col) {
    if (isset($receipts_total[$col['field']])) {
        $pdf->showBoxed(formattingWithSpace($receipts_total[$col['field']]), $col['width'], 760 - $line, $col['length'], 20, $col['align'], '');
    } elseif ($col['field'] == 'to') {
        $pdf->showBoxed('Total Receipts (Portfolio)', $col['width'], 760 - $line, 230, 20, 'left', '');
    }
}
$gross_receipts = $receipts_total['gross'];
$pdf->moveto(400, 781 - $line);
$pdf->lineto(570, 781 - $line);
$pdf->stroke();
$pdf->moveto(400, 781 - $line - 13);
$pdf->lineto(570, 781 - $line - 13);
$pdf->stroke();
// ###############################################################
// PAYMENTS
extract(checkIfAddHeader($line, $_fonts, $page, $pdf, $logo, $logoPath, $periodFrom, $periodTo));
$pdf->setFontExt($_fonts['Helvetica'], $font_size);

$columns = [
    ['field' => 'payee_name', 'label' => 'Supplier', 'width' => 20, 'length' => 190, 'align' => 'left', 'format' => 'text'],
    ['field' => 'tdescription', 'label' => 'Description', 'width' => 130, 'length' => 190, 'align' => 'left', 'format' => 'text'],
    ['field' => 'date_from', 'label' => 'From', 'width' => 245, 'length' => 90, 'align' => 'left', 'format' => 'text'],
    ['field' => 'date_to', 'label' => 'To', 'width' => 295, 'length' => 90, 'align' => 'left', 'format' => 'text'],
    ['field' => 'invoice', 'label' => 'Invoice #', 'width' => 345, 'length' => 450, 'align' => 'left', 'format' => 'text'],
    ['field' => 'net', 'label' => 'Net ', 'width' => 350, 'length' => 90, 'align' => 'right', 'format' => 'num'],
    ['field' => 'gst', 'label' => $_SESSION['country_default']['tax_label'] . ' ', 'width' => 410, 'length' => 90, 'align' => 'right', 'format' => 'num'],
    ['field' => 'gross', 'label' => 'Gross ', 'width' => 480, 'length' => 90, 'align' => 'right', 'format' => 'num'],
];
$font_size = 8;

$line += 30;
$pdf->setFontExt($_fonts['Helvetica-Bold'], $font_size);
$pdf->showBoxed('Payments:', 20, 760 - $line, 590, 20, 'left', '');
$line += 12;

$gross_payments = 0;
if (isset($data['payments']) && count($data['payments']) > 0) {
    $pdf->setFontExt($_fonts['Helvetica-Bold'], $font_size);
    foreach ($columns as $col) {
        $pdf->showBoxed($col['label'], $col['width'], 760 - $line, $col['length'], 20, $col['align'], '');
    }
    $line += 10;

    extract(checkIfAddHeader($line, $_fonts, $page, $pdf, $logo, $logoPath, $periodFrom, $periodTo));
    $pdf->setFontExt($_fonts['Helvetica'], $font_size);

    $totals = ['net' => 0, 'gst' => 0, 'gross' => 0];

    uasort($data['payments'], function ($a, $b) {
        return [$a['payee_name'], $a['invoice']] <=> [$b['payee_name'], $b['invoice']];
    });

    foreach ($data['payments'] as $row) {
        // PLOT VALUES TO COLUMNS
        foreach ($columns as $col) {
            $value = $row[$col['field']] ?? ($col['format'] == 'num' ? 0 : '');
            if (($value != '' || $value == 0) && $col['format'] == 'num') {
                $value = formattingWithSpace($value);
            }
            $pdf->showBoxed($value, $col['width'], 760 - $line, $col['length'], 20, $col['align'], '');
            // add to total payments
            if (isset($totals[$col['field']])) {
                $totals[$col['field']] += (float) $row[$col['field']];
            }
        }
        //
        $line += 10;

        extract(checkIfAddHeader($line, $_fonts, $page, $pdf, $logo, $logoPath, $periodFrom, $periodTo));
        $pdf->setFontExt($_fonts['Helvetica'], $font_size);
    }
    $pdf->setFontExt($_fonts['Helvetica-Bold'], $font_size);
    $line = $line + 5;
    foreach ($columns as $col) {
        if (isset($totals[$col['field']])) {
            $pdf->showBoxed(formattingWithSpace($totals[$col['field']]), $col['width'], 760 - $line, $col['length'], 20, $col['align'], '');
        } elseif ($col['field'] == 'date_to') {
            $pdf->showBoxed('Total Payments (Portfolio)', $col['width'], 760 - $line, 230, 20, 'left', '');
        }
    }
    $pdf->moveto(400, 781 - $line);
    $pdf->lineto(570, 781 - $line);
    $pdf->stroke();
    $gross_payments = $totals['gross'];
} else {
    $pdf->showBoxed('None', 20, 760 - $line, 190, 20, 'left', '');
    $line = $line + 15;
}

$line = $line + 30;

$cash_movement = $gross_receipts - $gross_payments;
$pdf->setFontExt($_fonts['Helvetica-Bold'], $font_size);
$pdf->showBoxed('Cash Movement For The Period', 20, 760 - $line, 590, 30, 'left', '');
$pdf->setFontExt($_fonts['Helvetica-Bold'], $font_size);
$pdf->showBoxed(formattingWithSpace($cash_movement), 470, 760 - $line, 100, 30, 'right', '');

$pdf->moveto(503, 791 - $line);
$pdf->lineto(570, 791 - $line);
$pdf->stroke();
$pdf->moveto(503, 791 - $line - 13);
$pdf->lineto(570, 791 - $line - 13);
$pdf->stroke();

$line = $line + 10;
extract(checkIfAddHeader($line, $_fonts, $page, $pdf, $logo, $logoPath, $periodFrom, $periodTo));
// ##############################################################
// CASH RECONCILIATION
$pdf->setFontExt($_fonts['Helvetica-Bold'], $font_size);
$line += 15;
$pdf->showBoxed('Opening Cash Balance', 20, 760 - $line, 590, 30, 'left', '');
$pdf->setFontExt($_fonts['Helvetica-Bold'], $font_size);
$pdf->showBoxed(formattingWithSpace($data['opening_bal']), 470, 760 - $line, 100, 30, 'right', '');
$line += 10;

// CASH REMITTANCES
if (isset($data['remittances']) && count($data['remittances']) > 0) {
    foreach ($data['remittances'] as $row) {
        $pdf->setFontExt($_fonts['Helvetica'], $font_size);
        $pdf->showBoxed($row['cred_name'], 20, 760 - $line, 590, 20, 'left', '');
        //                $pdf->showBoxed($row['tdescription'], 245, 760 - $line, 590, 20, "left", "");
        $pdf->showBoxed($row['ind_pmxc_alloc_dt'], 350, 760 - $line, 90, 20, 'right', '');
        $pdf->setFontExt($_fonts['Helvetica-Bold'], $font_size);
        $pdf->showBoxed(formattingWithSpace($row['ind_pmts_during']), 480, 760 - $line, 90, 20, 'right', '');
        $line += 10;
        extract(checkIfAddHeader($line, $_fonts, $page, $pdf, $logo, $logoPath, $periodFrom, $periodTo));
        $pdf->setFontExt($_fonts['Helvetica'], $font_size);
    }
}
$subtotal_balance = $cash_movement + $data['opening_bal'];
$owner_payments = -($data['total_pmts_during']);

$line += 10;
$pdf->moveto(503, 783 - $line);
$pdf->lineto(570, 783 - $line);
$pdf->stroke();
$pdf->moveto(503, 783 - $line - 13);
$pdf->lineto(570, 783 - $line - 13);
$pdf->stroke();
// CLOSING BALANCE
$closing_bal = $subtotal_balance - $owner_payments;

$pdf->setFontExt($_fonts['Helvetica-Bold'], $font_size);
$pdf->showBoxed('Closing Cash Balance', 20, 760 - $line, 590, 20, 'left', '');
$pdf->setFontExt($_fonts['Helvetica-Bold'], $font_size);
$pdf->showBoxed(formattingWithSpace($closing_bal), 470, 760 - $line, 100, 22, 'right', '');

$line += 10;
// ###############################################################
$pdf->setFontExt($_fonts['Helvetica'], 7);
$pdf->showBoxed("Page $page", 495, 0, 75, 20, 'right', '');

$traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'compactReport', A4_PORTRAIT);
$traccFooter->prerender($pdf);

$line = $line + 10;
$pdf->end_page_ext('');
// ###############################################################

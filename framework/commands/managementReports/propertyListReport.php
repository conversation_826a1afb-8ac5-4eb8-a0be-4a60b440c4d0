<?php

/**
 * Generate a property list report.
 *
 * @param  $context  array Mandatory. Referenced array containing data needed to continue.
 *
 * <AUTHOR> Reyes
 *
 * @since 2012-09-18
 *
 * @modified 2012-12-07: Added display on screen option. [Morph]
 **/
function propertyListReport(&$context)
{
    $userpermission = new userpermission();
    $result = $userpermission->userHasPageAccess(__FUNCTION__);

    // Page Template
    if (! isPostback()) {
        $view = new MasterPage(userViews(), '/managementReports/propertyListReport.html');
        $view->setSection($context['module']);
    } else {
        $view = new UserControl(userViews(), '/managementReports/propertyListReport.html');
    }
    $view->bindAttributesFrom($_REQUEST);

    $view->setCurrentTab('properties');

    // Array Call-In
    $view->items['formatList'] =
    [
        FILETYPE_PDF => 'PDF',
        FILETYPE_XLS => 'Excel Spreadsheet',
        FILETYPE_SCREEN => 'Print to Screen',
    ];
    $view->items['statusList'] =
    [
        '0' => 'Active',
        '1' => 'Inactive',
    ];
    $view->items['propertyManagerList'] =  [0 =>  ['propertyManagerID' => 'ALL', 'propertyManagerName' => 'All', 'number_of_props' => 23]];
    $view->items['propertyManagerList'] = array_merge($view->items['propertyManagerList'], dbGetPropertyManagers());

    // Default Values
    if (! array_key_exists($view->items['format'], $view->items['formatList'])) {
        $view->items['format'] = FILETYPE_PDF;
    }
    if (! isset($view->items['status'])) {
        $view->items['status'] = '0';
    }

    // Action
    switch ($view->items['action']) {
        case 'finalise':
            if (empty($view->items['propertyManager'])) {
                $validationErrors[] = 'You need to select a ' . ucwords(strtolower($_SESSION['country_default']['property_manager'])) . '.';
            } else {
                $view->items['PropManagerList'] = dbGetPropertyManagers();
                foreach ($view->items['PropManagerList'] as $v) {
                    $view->items['PropManagerList'][$v['propertyManagerID']]['name'] = $v['propertyManagerName'];
                    $view->items['PropManagerList'][$v['propertyManagerID']]['count'] = $v['number_of_props'];
                    if ($view->items['propertyManager'] == 'ALL') {
                        $view->items['PropManagerList'][$view->items['propertyManager']]['count'] += $v['number_of_props'];
                    }
                }
                $view->items['propertyManagerName'] = ($view->items['propertyManager'] == 'ALL') ? 'All ' . ucwords(strtolower($_SESSION['country_default']['property_manager'])) . 's' : $view->items['PropManagerList'][$view->items['propertyManager']]['name'];
                $view->items['propertyCount'] = $view->items['PropManagerList'][$view->items['propertyManager']]['count'];
                if (! $view->items['propertyCount']) {
                    $validationErrors[] = 'No properties found for ' . $view->items['propertyManagerName'] . '.';
                }
            }
            if (noErrors($validationErrors)) {
                if ($view->items['format'] == FILETYPE_SCREEN) {
                    $view->render();
                }
                $context = $view->items;
                executeCommand('propertyListReportProcess', 'managementReports');
            }
            break;
    }

    if ($view->items['format'] != FILETYPE_SCREEN || empty($view->items['action']) || $validationErrors) {
        // Post Feed
        $view->items['validationErrors'] = $validationErrors;

        // Display
        $view->render();
    }
}

<?php

/**
 * Created by PhpStorm.
 * User: esantiago
 * Date: 3/15/2019
 * Time: 2:53 PM
 */

// set module and command to use
$repModule = 'retail';
$repCommand = 'salesReport';

// reset report file to be attached
$context['ownerReportFile'] = null;

// report option
// 1 = portrait
// 2 = landscape
// $orientation = default from report table
$repOrientation = 2;

if ($repModule and $repCommand) {
    $originalViewItems = $view->items;
    $originalContext = $context;

    $view->items['propertyID'] = $propertyID;
    $view->items['reportTo'] = date('t/m/Y', strtotime(convertDate($view->items['periodTo']) . '-31 days'));
    $view->items[IS_TASK] = true;
    // Adjust parameters
    $view->items['method'] = $view->items['reportOn'];
    $view->items['taxStatus'] = $view->items['reportAs'];
    $view->items['type'] = 'year';
    $view->items['format'] = 'pdf';
    $view->items['summary'] = $view->items['includeSummary'];
    $view->items['promotionCharges'] =  $view->items['includePromotionCharge'];
    $view->items['period'] = $view->items['numberOfYear'];
    $view->items['showVariance'] = $view->items['showVariance'];
    $view->items['trafficCount'] = $view->items['includeTrafficCount'];

    // used on the command file to alter some processes
    $view->items['forOwnerReport'] = 1;
    $view->items['action'] = 'process';

    $context = $view->items;
    executeCommand($repCommand, $repModule);

    if (file_exists($context['ownerReportFile'])) {
        // $pdf is the main PDF where this report will be attached.
        $pdi = new ImportedPage($pdf, $context['ownerReportFile'], $repOrientation);
        $i = 1;
        while ($pdi->loadPage($i)) {
            $page++;
            $pdi->preparePage();
            $pdi->render();
            $pdi->endPage();
            $i++;
        }
        $pdi->close();
    }

    $context = $originalContext;
    $view->items = $originalViewItems;
}

// reset report file to be attached
$context['ownerReportFile'] = null;

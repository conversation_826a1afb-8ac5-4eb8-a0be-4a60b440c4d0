<?php

use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Fill;

include_once 'lib/ReportLib/commonReportFunctions.php';
function bankGuaranteeReportProcess(&$context)
{
    global $pathPrefix, $clientDirectory;

    if (! isPostback()) {
        $view = new MasterPage(userViews(), '/managementReports/bankGuaranteeReportProcess.html');
        $view->setSection($context['module']);
    } else {
        $view = new UserControl(userViews(), '/managementReports/bankGuaranteeReportProcess.html');
    }

    if ($context[IS_TASK]) {
        $view->bindAttributesFrom($context);
        extract($context);
    } else {

        $view->bindAttributesFrom($_REQUEST);
        $bankGuaranteeData = dbGenerateBankGuarantee($view->items['propertyManager'], $view->items['reportType'], $view->items['bankGuaranteeOption'], $view->items['bankGuaranteeDate'], $view->items['currentTenantOnly']);
        if ($bankGuaranteeData) {
            $bankGuaranteeCount = count($bankGuaranteeData ?? []);
        }
        $view->items['bankGuaranteeCount'] = $bankGuaranteeCount;
        $view->items['command'] = 'bankGuaranteeReportProcess';
        $view->items['module'] = 'managementReports';
        $queue = new Queue(TASKTYPE_BANK_GUARANTEE_REPORT);
        if ($bankGuaranteeCount > THRESHOLD_BANKGUARANTEEREPORT) {
            $queue->add($_SESSION['clientID'], $_SESSION['un'], 'command=bankGuaranteeReportProcess&module=managementReports', $view->items);
        }
    }

    // ## GENERATE PDF REPORT
    if (($context[IS_TASK]) || ($bankGuaranteeCount <= THRESHOLD_BANKGUARANTEEREPORT)) {
        $format = $view->items['format'];

        // # HANDLE PROCESSING LOGIC ##
        $logoFile = $_SESSION['dbList'][$_SESSION['clientID']]['logo_url'];
        $logoPath = "/assets/clientLogos/{$logoFile}";

        $_filePath = "{$pathPrefix}{$clientDirectory}/{$format}/" . DOC_BANKGUARANTEE . '/';
        $_downloadPath = "{$clientDirectory}/{$format}/" . DOC_BANKGUARANTEE;

        $_filename = 'BondAndBankGuaranteeReport';
        $_header_title = 'Bond and Bank Guarantee Report';

        $logoFile = dbGetClientLogo();
        $logoPath = "assets/clientLogos/$logoFile";

        // Style
        $numberFormat = '_(* #,##0.00_);_(* (#,##0.00);_(* "-"??_);_(@_)';
        $numberFormatStyle =
        [
            'alignment' =>  ['horizontal' => 'right'],
            'numberformat' =>  ['code' => $numberFormat],
        ];

        $file = $_filename . date('YmdHis') . ".{$format}";
        $filePath = $_filePath . $file;
        $downloadPath = "{$_downloadPath}/{$file}";

        if ($format == FILETYPE_PDF) {
            $report = ($context[DOC_MASTER]) ? new PDFDataReport($context[DOC_MASTER], $logoPath, A4_LANDSCAPE) : new PDFDataReport($filePath, $logoPath, A4_LANDSCAPE);
            $report->multiLine = true;
        } else {
            $report = new XLSDataReport($filePath, $_header_title);
            $report->enableFormatting = true;
        }

        $subtitle = 'as of ' . $view->items['bankGuaranteeDate'];

        $header = new ReportHeader($_header_title, $subtitle, '');
        $header->xPos = $report->hMargin;
        $header->yPos = $report->pageHeight - $report->vMargin;
        if ($format == FILETYPE_PDF) {
            $report->attachObject('header', $header);
            $footer = new TraccFooter(null, $_header_title, $report->pageSize);
            $report->attachObject('footer', $footer);
        }

        if ($format != FILETYPE_SCREEN) {
            $subHeaderstyle = [
                'fill' => [
                    'type' => Fill::FILL_SOLID,
                    'color' => ['rgb' => '004c7a'],
                ],
                'font' =>  ['bold' => true, 'color' =>  ['rgb' => 'ffffff']],
                'alignment' =>  ['vertical' => Alignment::VERTICAL_CENTER,  'horizontal' => Alignment::HORIZONTAL_LEFT],
            ];

            $report->resetColumns();

            $report->addColumn('property', 'Property', 70, 'left');
            $report->addColumn('lease', 'Lease', 100, 'left');
            $report->addColumn('tenant_name', 'Tenant Name', 100, 'left');
            $report->addColumn('guarantor', 'Type', 80, 'left');
            $report->addColumn('held', 'Person', 70, 'left');
            $report->addColumn('bank_name', 'Bank', 80, 'left');
            $report->addColumn('details', 'Details', 100, 'left');
            $report->addColumn('exp_date', 'Exp. Date', 50, 'center');
            $report->addColumn('amount', 'Amount', 60, 'right', '#,##0.00_);(#,##0.00)');
            $report->addColumn('accrued_amount', 'Accrued Interest', 60, 'right', '#,##0.00_);(#,##0.00)');

            if ($format == FILETYPE_PDF) {
                $report->addSubHeaderItem('title', 0, 200, 'left', null, $subHeaderstyle);
            }
            $report->preparePage();
            $report->renderHeader();
            foreach ($bankGuaranteeData as $bank_value) {
                if ($bank_value) {
                    $bank_value['amount'] = $bank_value['amount'] ? toMoney($bank_value['amount'], '') : '';
                    $bank_value['accrued_amount'] = $bank_value['accrued_amount'] ? toMoney($bank_value['accrued_amount'], '') : '';

                    $report->renderLine($bank_value);
                }
            }
            $report->clean();
            $report->endPage();
        }
        $report->close();
    }

    if ($bankGuaranteeCount > THRESHOLD_BANKGUARANTEEREPORT) {
        $view->items['statusMessage'] = 'Due to its complexity, this report has been scheduled and will be emailed to you on completion!';
        $view->render();
    } else {
        $view->items['downloadPath'] = $downloadPath;
        $view->items['bankGuaranteeData'] = $bankGuaranteeData;
        $view->render();
    }
}

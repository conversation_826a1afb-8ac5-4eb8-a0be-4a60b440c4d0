<?php

global $clientDirectory, $pathPrefix, $sess;
function array_orderby()
{
    $args = func_get_args();
    $data = array_shift($args);
    foreach ($args as $n => $field) {
        if (is_string($field)) {
            $tmp =  [];
            foreach ($data as $key => $row) {
                $tmp[$key] = $row[$field];
            }
            $args[$n] = $tmp;
        }
    }
    $args[] = &$data;
    call_user_func_array('array_multisort', $args);

    return array_pop($args);
}

function buildTenancyDetails($unit, $columnList)
{
    global $totals1,$totals, $date, $totalArea, $area, $reviewTypes, $periodSize, $property,  $occupiedArea, $vacantArea, $totalArea, $format, $divisionTypes;

    $row =  [];

    $propertyID = $unit['propertyID'];
    $_area = getPropertyArea($propertyID, $date);

    $details = ($unit['leaseID']) ? dbGetLeaseTerms($propertyID, $unit['leaseID']) : null;
    $contacts = ($unit['leaseID']) ? dbGetTenantContacts($propertyID, $unit['leaseID']) : null;
    $property = dbGetPropertyLookup($propertyID);
    $row['propertyName'] = $property['propertyName'];
    $row['tenant'] = ($details) ? "{$unit['leaseID']} - {$details['leaseName']}\n{$unit['unitID']} - {$details['tenantName']}\n{$details['leaseDescription']}" : "{$unit['unitID']} - Vacant\n{$unit['description']}";
    $row['tenantUnit'] = ($details) ? "{$unit['leaseID']} - {$details['leaseName']}\n{$unit['unitID']} - {$details['unitDesc']}\n{$details['leaseDescription']}" : "{$unit['unitID']} - Vacant\n{$unit['description']}";
    $row['leaseIDName'] = "{$unit['leaseID']} - {$details['leaseName']}";
    $row['leaseUnit'] = "{$unit['unitID']}";
    $row['leaseName'] = $details['leaseName'];
    $row['tenantName'] = $details['tenantName'];
    $row['postcode'] = $details['tenantPostcode'];
    $row['suburb'] = $details['tenantSuburb'];
    $row['landlordName'] = $details['landlordName'];
    $row['tenantSuburb'] = $details['tenantSuburb'];
    $row['tenantState'] = $details['tenantState'];
    $row['leaseClass'] = $details['leaseClass'];

    $row['description'] = $unit['description'];
    $row['leaseType'] = $details['leaseType'];
    // $row['description'] = $unit['description'];
    $row['leaseLType'] = $details['leaseLType'];
    $row['state'] = $details['state'];
    $row['leaseID'] = $unit['leaseID'];

    $row['leaseCRN'] = $details['leaseCRN']; // Lease CRN
    $row['leaseStatusDesc'] =  $details['leaseStatusDescription']; // Lease Status description
    $row['bondDepositProp'] = $details['bondDepositProp']; // Bond/Deposit Property Code
    $row['bondDepositLease'] = $details['bondDepositLease']; // Bond/Deposit Lease Code
    $row['tenantType'] = $details['tenantType']; // Tenant Type
    $row['leaseDivision'] = $details['leaseDivision']; // Division
    $row['retailCategory'] = $details['retailCategory']; // Retail Category
    $row['retailSubCategory'] = $details['retailSubCategory']; // Retail Sub Category
    $row['retailFineCategory'] = $details['retailFineCategory']; // Retail Fine Category

    // -- lesee details (used for lease administration)
    $row['lessee'] = ($details) ? "{$details['leaseName']}\n{$details['leaseDescription']}\n{$details['landlordName']}" : "{$unit['unitID']} - Vacant\n{$unit['description']}";

    // -- tenant contacts (used for strata)
    $_contacts = '';
    if ($contacts) {

        $_contacts = '';
    }
    $contacts = dbGetLeaseContacts($propertyID, $unit['leaseID']);
    foreach ($contacts as &$c) {
        $contactDetails = dbGetLeaseContactDetails($propertyID, $unit['leaseID'], $c['contactID']);

        $_contacts .= "{$c['contactName']}\n{$c['contactRoleDesc']}\n\n";

        foreach ($contactDetails as $d) {
            $_contacts .= "{$d['detailCodeDesc']}\n{$d['detail']}\n\n";
        }
    }

    $row['contact'] = ($contacts) ? $_contacts : null;

    $areaPercentage =  $_area->totalArea > 0 ? (toDecimal(($unit['unitArea'] / $_area->totalArea) * 100)) : 0;

    $totals['areaOnly'] += (float) $unit['unitArea'];

    $totals1['area'] += (float) $unit['unitArea'];
    $totals1['area-vacant'] += ($unit['unitStatus'] == VACANT) ? (float) $unit['unitArea'] : 0;
    $unitArea = toDecimal($unit['unitArea']);
    $xlsunitArea = $unit['unitArea'];
    $row['area'] = "{$unitArea}\n{$areaPercentage}";
    if ($row[$type] = $format == FILETYPE_XLS) {
        $row['areaOnly'] = "{$xlsunitArea}";
    } else {
        $row['areaOnly'] = "{$unitArea}";
    }
    // -- calculate the period of the lease
    $dateString = '';
    if (! empty($details['commencementDate']) && ! empty($details['expiryDate'])) {
        $dates = dateDiff($details['commencementDate'], $details['expiryDate']);
        if ($dates[DATE_YEAR]) {
            $dateString .= $dates[DATE_YEAR] . 'Y ';
        }
        if ($dates[DATE_MONTH]) {
            $dateString .= $dates[DATE_MONTH] . 'M ';
        }
        if ($dates[DATE_DAY]) {
            $dateString .= $dates[DATE_DAY] . 'D ';
        }
    }

    $row['terms'] = "{$details['commencementDate']}\n{$details['expiryDate']}\n{$dateString}";
    $row['leaseterms'] = "{$details['commencementDate']}";
    $row['expiryDate'] = "{$details['expiryDate']}";
    $row['dateString'] = "{$dateString}";


    $row['options'] = "{$details['leaseOption']}";

    $area = (float) $unit['unitArea'];
    $charges = dbGetSumAllChargeTypes($propertyID, $unit['leaseID'], $unit['unitID'], $date);
    $total = 0;
    $totalMonthly = 0; // added by arjay
    $totalPerArea = 0; // added by arjay
    // -- calculate the rows for the various lease charges - there are some tweaks here for excel files to only have a single value (the total charge) to help with calculations
    if ($charges) {
        foreach ($charges as $type => $charge) {
            if ($type != CHARGE_PARKING) {
                if ($area != 0) {
                    $byArea = ($area) ? toMoney($charge / $area) : 0;
                    $perArea = ($area) ? $charge / $area : 0;
                }
                $perMonth = toMoney($charge / $periodSize);
                $_charge = toMoney($charge);
                $row[$type] = ($format == FILETYPE_PDF) ? "{$_charge}\n{$perMonth}\n{$byArea}" : $charge;
                $total += (float) $charge;

            } else {
                $bays =  dbGetParkingForDate($propertyID, $unit['leaseID'], $unit['unitID'], $date);
                $perMonth = toMoney($charge / $periodSize);
                $perArea = $bays;
                $_charge = toMoney($charge);
                $row[$type] = ($format == FILETYPE_PDF) ? "{$_charge}\n{$perMonth}\n{$bays}" : $charge;
                $total += (float) $charge;

            }
            $perMonth1 = $charge / $periodSize;
            $totalMonthly += (float) $perMonth1;
            $totalPerArea += (float) $perArea;
            $totals[$type] = bcadd($totals[$type], $charge, 2);
            $totals1[$type] = bcadd($totals1[$type], $charge, 2);
            $totals1[$type . '-M'] = bcadd($totals1[$type . '-M'], $perMonth1, 2);
            $totals1[$type . '-m2'] = bcadd($totals1[$type . '-m2'], $perArea, 2);
        }
    }
    $totals['total'] += (float) $total;
    $totals1['total'] += (float) $total;
    $totals1['total-M'] += (float) $totalMonthly;

    // echo $totals['total'].'<br>';
    // -- total of all charges
    if ($area != 0) {
        $byArea = ($area) ? toMoney($total / $area) : 0;
        $perArea = ($area) ? $total / $area : 0;
    }
    $totals1['total-m2'] += (float) $perArea;
    $perMonth = toMoney($total / $periodSize);
    $total = $total;
    $pdftotal = toMoney($total);
    if ($total) {
        $row['total'] = ($format == FILETYPE_PDF) ? "{$pdftotal}\n{$perMonth}\n{$byArea}" : $total;
    }

    // -- calculate WALE
    $firstDT = toTimestamp($date);
    $secondDT = toTimestamp($details['expiryDate']);
    $waleDateDiff = (($secondDT - $firstDT) / 3600) / 24;
    $wale = $waleDateDiff <= 0 ? 0.00 : $waleDateDiff * ($total / 365.25);
    $pdfWale = toMoney($wale);
    $row['wl'] = ($format == FILETYPE_PDF) ? (string) ($pdfWale) : $wale;
    $totals1['wl'] += (float) $wale;
    // -- calculated 'others'
    if ($charges) {
        $_others = 0;
        foreach ($charges as $type => $charge) {
            if (! in_array($type, $columnList)) {
                $_others += (float) $charge;
            }
        }
        $byArea = ($area) ? toMoney($_others / $area) : 0;
        $byArea1 = ($area) ? $_others / $area : 0;
        $perMonth = toMoney($_others / $periodSize);
        $others = toMoney($_others);
        $totals['others'] = bcadd($totals['others'], $_others, 2);
        $totals1['others'] = bcadd($totals1['others'], $_others, 2);
        $totals1['others-M'] = bcadd($totals1['others-M'], ($_others / $periodSize), 2);
        $totals1['others-m2'] = bcadd($totals1['others-m2'], $byArea1, 2);
        if ($_others) {
            $row['others'] = ($format == FILETYPE_PDF) ? "{$others}\n{$perMonth}\n{$byArea}" : $others;
        }
    }
    /** end **/

    // -- rent reviews
    $rentReviewNext = dbGetRentReviewsTenancyReport($propertyID, $unit['leaseID']);
    $rentReviews = dbGetRentReviews($propertyID, $unit['leaseID']);
    $_reviews = [];
    if ($rentReviews) {
        $rentDate = [];
        $rentType = [];
        foreach ($rentReviews as $k => $review) {
            if ($review['chargeType'] == CHARGE_RENT) {
                if ($k == 0) {
                    $rentDate[] = $review['reviewDate'];
                    $rentType[] = $reviewTypes[$review['reviewType']];
                } else {
                    $rentDate[] = $review['reviewDate'];
                    $rentType[] = $reviewTypes[$review['reviewType']];
                }
            }
        }
        $_reviews = ['rentData' => $rentDate, 'rentType' => $rentType];

    }

    // -- lease notes
    $leaseNotes = dbReportLeaseNotes($propertyID, $unit['leaseID']);
    $_notes = [];
    if ($leaseNotes) {
        foreach ($leaseNotes as $note) {
            $_notes[] = "{$note['note']}\n";
        }
    }

    $row['notes'] = $_notes;

    /***for renderline  rentreview and lease notes added by  **/
    $row['rr'] = $_reviews;

    $row['nt'] = $_notes;
    $rentReviewData = "{$rentReviewNext['reviewDate']}\n{$reviewTypes[$rentReviewNext['reviewType']]}\n";


    $notes = '';
    foreach ($_notes as $k => $v) {
        $notes .= $v;
    }

    /**********************end******************/
    return $row;
}

function getPropertyArea($propertyID, $date)
{
    global $propertyAreas;

    if (! isset($propertyAreas[$propertyID])) {
        $o = new stdClass();
        $o->occupiedArea = dbGetUnitAreaForPropertyByStatus($propertyID, $date, 'O');
        $o->vacantArea = dbGetUnitAreaForPropertyByStatus($propertyID, $date, 'O');
        $o->totalArea = dbGetUnitTotalAreaForPropertyByStatus($propertyID, $date, 'O');
        $propertyAreas[$propertyID] = $o;
    }

    return $propertyAreas[$propertyID];
}

function tenancyScheduleProcess(&$context)
{
    global $clientDirectory, $pathPrefix, $sess;
    $threshold = 3;

    if (! isPostback()) {
        $view = new MasterPage(userViews(), '/managementReports/tenancyScheduleProcess.html');
        $view->setSection($context['module']);
    } else {
        $view = new UserControl(userViews(), '/managementReports/tenancyScheduleProcess.html');
    }

    $view->bindAttributesFrom($_REQUEST);
    $view->bindAttributesFrom($context);
    $validationErrors =  [];

    if ($context[IS_TASK]) {
        $view->bindAttributesFrom($context);
        extract($context);
    } else {
        $view->bindAttributesFrom($_REQUEST);
        $propertyCount = 0;
        if ($view->items['property']) {
            $propertyCount = count(deserializeParameters($view->items['property']));
        }
        $view->items['propertyCount'] = $propertyCount;
        $queue = new Queue(TASKTYPE_TENANCY_SCHEDULE);
        if ($propertyCount > THRESHOLD_TENANCYSCHEDULE) {
            $queue->add($_SESSION['clientID'], $_SESSION['un'], 'command=tenancyScheduleProcess&module=mangementReports', $_REQUEST);
        }
    }

    /* parameters you can pass
    - chargeType: S/D
    - includeVacant : true/false
    */
    global $totalsEveryProperty, $totalsAllProperty,$totalsAllPropertyExcel,$totals1,$totals, $propertyID, $date, $totalArea, $area, $reviewTypes, $periodSize, $property,  $occupiedArea, $vacantArea, $totalArea, $format, $propertyAreas, $divisionTypes;

    $propertyAreas =  [];
    $columnList = [];
    $chosenProperties = [];

    $properties = deserializeParameters($view->items['property']);
    $date = $view->items['toDate'];
    $format = $view->items['format'];
    // ** HANDLE PROCESSING LOGICreview0

    $_report = dbGetReport($view->items['reportID']);
    // -- if there are parameters provided, extract them to the globals
    if (! empty($_report['reportParameters'])) {
        $parameters = (array) json_decode($_report['reportParameters']);
        extract($parameters);
    }

    $columns = dbGetReportColumns($view->items['reportID']);

    foreach ($columns as $col_key => $col_val) {
        if (strpos($columns[$col_key]['subtitle'], '$') !== false) {
            $columns[$col_key]['subtitle'] = str_replace('$', $_SESSION['country_default']['currency_symbol'], $columns[$col_key]['subtitle']);
        }

        if (strpos($columns[$col_key]['subtitle'], 'm2') !== false) {
            $columns[$col_key]['subtitle'] = str_replace('m2', html_entity_decode($_SESSION['country_default']['area_unit'], ENT_COMPAT), $columns[$col_key]['subtitle']);
        }
    }

    foreach ($columns as $c) {
        $columnList[] = $c['columnKey'];
    }

    $data =  [];

    $reviewTypes = mapParameters(dbGetParams('RENTREVIEW'));
    $divisionTypes = mapParameters(dbGetParams('DIVISION'));

    switch ($_report['reportPeriod']) {
        case PERIOD_MONTH: $periodSize = 12;
            break;
        case PERIOD_YEAR: $periodSize = 1;
            break;
        case PERIOD_QUARTER: $periodSize = 4;
            break;
        case PERIOD_FORTNIGHT: $periodSize = 26;
            break;
        case PERIOD_WEEK: $periodSize = 52;
            break;
        case PERIOD_4WEEK: $periodSize = 26;
            break;
    }

    // -- IF METHOD INVOLVES PROPERTY
    $count = 0;
    if (in_array($view->items['method'], ['property', 'propertyManager'])) {
        if (! isEmptyArray($properties)) {
            foreach ($properties as $propertyID) {
                $units = dbGetUnitsForPropertyAsAt($propertyID, $date, $chargeType);
                $property = dbGetPropertyLookup($propertyID);
                $chosenProperties[] = ['propertyName' => $property['propertyName'], 'propertyID' => $property['propertyID']];
                foreach ($units as $unit) {
                    $row = buildTenancyDetails($unit, $columnList);

                    if ($view->items['merge']) {
                        $data[] = $row;
                    } else {
                        $data[$unit['propertyID']][] = $row;
                    }
                }


                /**subtotal for every property added by Arjay May 05,2017**/
                if (! isset($totals1['areaOnly'])) {
                    $totals1['areaOnly'] = 0.00;
                }
                if (! isset($totals1['area'])) {
                    $totals1['area'] = 0.00;
                }
                if (! isset($totals1['area-vacant'])) {
                    $totals1['area-vacant'] = 0.00;
                }
                if (! isset($totals1['total'])) {
                    $totals1['total'] = 0.00;
                }
                if (! isset($totals1['total-M'])) {
                    $totals1['total-M'] = 0.00;
                }
                if (! isset($totals1['total-m2'])) {
                    $totals1['total-m2'] = 0.00;
                }
                if (! isset($totals1['P'])) {
                    $totals1['P'] = 0.00;
                }
                if (! isset($totals1['P-M'])) {
                    $totals1['P-M'] = 0.00;
                }
                if (! isset($totals1['P-m2'])) {
                    $totals1['P-m2'] = 0.00;
                }
                if (! isset($totals1['others'])) {
                    $totals1['others'] = 0.00;
                }
                if (! isset($totals1['others-M'])) {
                    $totals1['others-M'] = 0.00;
                }
                if (! isset($totals1['others-m2'])) {
                    $totals1['others-m2'] = 0.00;
                }
                if (! isset($totals1['R'])) {
                    $totals1['R'] = 0.00;
                }
                if (! isset($totals1['R-M'])) {
                    $totals1['R-M'] = 0.00;
                }
                if (! isset($totals1['R-m2'])) {
                    $totals1['R-m2'] = 0.00;
                }
                if (! isset($totals1['O'])) {
                    $totals1['O'] = 0.00;
                }
                if (! isset($totals1['O-M'])) {
                    $totals1['O-M'] = 0.00;
                }
                if (! isset($totals1['O-m2'])) {
                    $totals1['O-m2'] = 0.00;
                }
                if (! isset($totals1['V'])) {
                    $totals1['V'] = 0.00;
                }
                if (! isset($totals1['V-M'])) {
                    $totals1['V-M'] = 0.00;
                }
                if (! isset($totals1['V-m2'])) {
                    $totals1['V-m2'] = 0.00;
                }
                if (! isset($totals1['C'])) {
                    $totals1['C'] = 0.00;
                }
                if (! isset($totals1['C-M'])) {
                    $totals1['C-M'] = 0.00;
                }
                if (! isset($totals1['C-m2'])) {
                    $totals1['C-m2'] = 0.00;
                }
                if (! isset($totals1['S'])) {
                    $totals1['S'] = 0.00;
                }
                if (! isset($totals1['S-M'])) {
                    $totals1['S-M'] = 0.00;
                }
                if (! isset($totals1['S-m2'])) {
                    $totals1['S-m2'] = 0.00;
                }
                if (! isset($totals1['F'])) {
                    $totals1['F'] = 0.00;
                }
                if (! isset($totals1['F-M'])) {
                    $totals1['F-M'] = 0.00;
                }
                if (! isset($totals1['F-m2'])) {
                    $totals1['F-m2'] = 0.00;
                }
                if (! isset($totals1['M'])) {
                    $totals1['M'] = 0.00;
                }
                if (! isset($totals1['M-M'])) {
                    $totals1['M-M'] = 0.00;
                }
                if (! isset($totals1['M-m2'])) {
                    $totals1['M-m2'] = 0.00;
                }
                if (! isset($totals1['wl'])) {
                    $totals1['wl'] = 0.00;
                }
                $percentVacant = $totals1['area'] > 0 ? ($totals1['area-vacant'] / $totals1['area']) * 100 : 0;
                $totals1['P-m2'] = $totals1['area'] > 0 ? ($totals1['P'] / $totals1['area']) : 0;
                $totals1['others-m2'] = $totals1['area'] > 0 ? ($totals1['P'] / $totals1['area']) : 0;
                $totals1['R-m2'] = $totals1['area'] > 0 ? ($totals1['R'] / $totals1['area']) : 0;
                $totals1['V-m2'] = $totals1['area'] > 0 ? ($totals1['V'] / $totals1['area']) : 0;
                $totals1['C-m2'] = $totals1['area'] > 0 ? ($totals1['C'] / $totals1['area']) : 0;
                $totals1['S-m2'] = $totals1['area'] > 0 ? ($totals1['S'] / $totals1['area']) : 0;
                $totals1['F-m2'] = $totals1['area'] > 0 ? ($totals1['F'] / $totals1['area']) : 0;
                $totals1['M-m2'] = $totals1['area'] > 0 ? ($totals1['M'] / $totals1['area']) : 0;
                $totals1['areaOnly'] = $totals1['area'];
                // for sum of all properties
                if (count($totalsAllProperty ?? []) >= 0) {
                    foreach ($totals1 as $k => $d) {
                        $totalsAllProperty[$k] += $d;
                    }
                }

                if ($format == FILETYPE_PDF) {
                    $totals1['areaOnly'] = number_format($totals1['area'], 2) . ' ' . html_entity_decode($_SESSION['country_default']['area_unit'], ENT_COMPAT);
                    $totals1['area'] = number_format($totals1['area'], 2) . ' ' . html_entity_decode($_SESSION['country_default']['area_unit'], ENT_COMPAT) . "\n" . 'Vacant ' . number_format($percentVacant, 0) . '%';
                    $totals1['total'] = toMoney($totals1['total']) . "\n" . toMoney($totals1['total-M']) . "\n" . toMoney($totals1['total-m2']);
                    $totals1['P'] = toMoney($totals1['P']) . "\n" . toMoney($totals1['P-M']) . "\n" . toMoney($totals1['P-m2']);
                    $totals1['others'] = toMoney($totals1['others']) . "\n" . toMoney($totals1['others-M']) . "\n" . toMoney($totals1['others-m2']);
                    $totals1['R'] = toMoney($totals1['R']) . "\n" . toMoney($totals1['R-M']) . "\n" . toMoney($totals1['R-m2']);
                    $totals1['V'] = toMoney($totals1['V']) . "\n" . toMoney($totals1['V-M']) . "\n" . toMoney($totals1['V-m2']);
                    $totals1['C'] = toMoney($totals1['C']) . "\n" . toMoney($totals1['C-M']) . "\n" . toMoney($totals1['C-m2']);
                    $totals1['S'] = toMoney($totals1['S']) . "\n" . toMoney($totals1['S-M']) . "\n" . toMoney($totals1['S-m2']);
                    $totals1['F'] = toMoney($totals1['F']) . "\n" . toMoney($totals1['F-M']) . "\n" . toMoney($totals1['F-m2']);
                    $totals1['M'] = toMoney($totals1['M']) . "\n" . toMoney($totals1['M-M']) . "\n" . toMoney($totals1['M-m2']);
                    $totals1['wl'] = toMoney($totals1['wl']);
                    $totals1['tenant'] = 'Total Annual' . "\n" . 'Total Monthly' . "\n" . $_SESSION['country_default']['currency_symbol'] . '/' . html_entity_decode($_SESSION['country_default']['area_unit'], ENT_COMPAT);
                    $totals1['leaseIDName'] = 'Total Annual' . "\n" . 'Total Monthly' . "\n" . $_SESSION['country_default']['currency_symbol'] . '/' . html_entity_decode($_SESSION['country_default']['area_unit'], ENT_COMPAT);
                    $totalsEveryProperty[$propertyID] = $totals1;

                } else {
                    $percentVacant = $totals1['area'] > 0 ? (($totals1['area-vacant'] / $totals1['area']) * 100) : 0;
                    foreach ($totals1 as $k => $v) {
                        if (strpos($k, '-')) {
                            $r = explode('-', $k);
                            if (strpos($k, '2')) {
                                $totalsEveryProperty[$propertyID]['m2'][$r[0]] = $v;
                            } else {
                                if (strpos($k, 'vacant')) { // check if the key is for vacant area
                                    $totalsEveryProperty[$propertyID]['monthly'][$r[0]] = 'Vacant ' . number_format($percentVacant, 0) . '%';
                                } else {
                                    $totalsEveryProperty[$propertyID]['monthly'][$r[0]] = $v;
                                }
                            }
                        } else {

                            if (strtoupper($k) === 'AREA' || strtoupper($k) === 'AREAONLY') { // check if the key is for area

                                $totalsEveryProperty[$propertyID]['annual'][$k] = number_format($v, 2) . ' ' . html_entity_decode($_SESSION['country_default']['area_unit'], ENT_COMPAT);

                            } else {

                                $totalsEveryProperty[$propertyID]['annual'][$k] = $v;
                            }

                        }
                    }

                }

                if (is_array($totals1)) {
                    foreach ($totals1 as $id => $t) {
                        $totals1[$id] = 0;
                    }
                }

                /*****************************end***********************/

            }

            if ($format == FILETYPE_PDF) {
                /*****for sum of all properties PDF*******/

                $percentVacant = $totalsAllProperty['area'] > 0 ? (($totalsAllProperty['area-vacant'] / $totalsAllProperty['area']) * 100) : 0;
                $totalsAllPropertyROverArea = $totalsAllProperty['area'] > 0 ? ($totalsAllProperty['R'] / $totalsAllProperty['area']) : 0;
                $totalsAllProperty['areaOnly'] = number_format($totalsAllProperty['area'], 2) . ' ' . html_entity_decode($_SESSION['country_default']['area_unit'], ENT_COMPAT);
                $totalsAllProperty['area'] = number_format($totalsAllProperty['area'], 2) . ' ' . html_entity_decode($_SESSION['country_default']['area_unit'], ENT_COMPAT) . "\n" . 'Vacant ' . number_format($percentVacant, 0) . '%';
                $totalsAllProperty['total'] = toMoney($totalsAllProperty['total']) . "\n" . toMoney($totalsAllProperty['total-M']) . "\n" . toMoney($totalsAllProperty['total-m2']);
                $totalsAllProperty['P'] = toMoney($totalsAllProperty['P']) . "\n" . toMoney($totalsAllProperty['P-M']) . "\n" . toMoney($totalsAllProperty['P-m2']);
                $totalsAllProperty['others'] = toMoney($totalsAllProperty['others']) . "\n" . toMoney($totalsAllProperty['others-M']) . "\n" . toMoney($totalsAllProperty['others-m2']);
                $totalsAllProperty['R'] = toMoney($totalsAllProperty['R']) . "\n" . toMoney($totalsAllProperty['R-M']) . "\n" . toMoney($totalsAllPropertyROverArea);
                $totalsAllProperty['V'] = toMoney($totalsAllProperty['V']) . "\n" . toMoney($totalsAllProperty['V-M']) . "\n" . toMoney($totalsAllProperty['V-m2']);
                $totalsAllProperty['C'] = toMoney($totalsAllProperty['C']) . "\n" . toMoney($totalsAllProperty['C-M']) . "\n" . toMoney($totalsAllProperty['C-m2']);
                $totalsAllProperty['S'] = toMoney($totalsAllProperty['S']) . "\n" . toMoney($totalsAllProperty['S-M']) . "\n" . toMoney($totalsAllProperty['S-m2']);
                $totalsAllProperty['F'] = toMoney($totalsAllProperty['F']) . "\n" . toMoney($totalsAllProperty['F-M']) . "\n" . toMoney($totalsAllProperty['F-m2']);
                $totalsAllProperty['M'] = toMoney($totalsAllProperty['M']) . "\n" . toMoney($totalsAllProperty['M-M']) . "\n" . toMoney($totalsAllProperty['M-m2']);
                $totalsAllProperty['wl'] = toMoney($totalsAllProperty['wl']);
                $totalsAllProperty['tenant'] = 'Total Annual' . "\n" . 'Total Monthly' . "\n" . $_SESSION['country_default']['currency_symbol'] . '/' . html_entity_decode($_SESSION['country_default']['area_unit'], ENT_COMPAT);
                $totalsAllProperty['leaseIDName'] = 'Total Annual' . "\n" . 'Total Monthly' . "\n" . $_SESSION['country_default']['currency_symbol'] . '/' . html_entity_decode($_SESSION['country_default']['area_unit'], ENT_COMPAT);
                /*****for sum of all properties*******/

            } else {
                /*****for sum of all properties Excel*******/

                $percentVacant = $totalsAllProperty['area'] > 0 ? (($totalsAllProperty['area-vacant'] / $totalsAllProperty['area']) * 100) : 0;

                foreach ($totalsAllProperty as $k => $v) {

                    if (strpos($k, '-')) {
                        $r = explode('-', $k);
                        if (strpos($k, '2')) {
                            $totalsAllPropertyExcel['m2'][$r[0]] = $v;
                        } else {
                            if (strpos($k, 'vacant')) { // check if the key is for vacant area
                                $totalsAllPropertyExcel['monthly'][$r[0]] = 'Vacant ' . number_format($percentVacant, 0) . '%';
                            } else {
                                $totalsAllPropertyExcel['monthly'][$r[0]] = $v;
                            }
                        }
                    } else {
                        if (strtoupper($k) === 'AREA') { // check if the key is for area
                            $totalsAllPropertyExcel['annual'][$k] = number_format($v, 2) . ' ' . html_entity_decode($_SESSION['country_default']['area_unit'], ENT_COMPAT);
                        } elseif (strtoupper($k) === 'AREAONLY') {
                            $totalsAllPropertyExcel['annual'][$k] = number_format($v, 2) . ' ' . html_entity_decode($_SESSION['country_default']['area_unit'], ENT_COMPAT);
                        } else {
                            $totalsAllPropertyExcel['annual'][$k] = $v;
                        }

                    }
                }

                /*****for sum of all properties*******/
            }

            $count = count($data ?? []);
        }
    }

    if (($count && ($context[IS_TASK])) || ($propertyCount <= THRESHOLD_TENANCYSCHEDULE)) {
        $logoFile = dbGetClientLogo();
        $logoPath = "assets/clientLogos/{$logoFile}";

        $_filePath =  "{$pathPrefix}{$clientDirectory}/{$format}/" . DOC_TENANCYSCHEDULE . '/';
        $_downloadPath =  "{$clientDirectory}/{$format}/" . DOC_TENANCYSCHEDULE;
        $file =  'TenancySchedule_' . date('YmdHis') . ".{$format}";


        $filePath = $_filePath . $file;
        $downloadPath = "{$_downloadPath}/{$file}";

        $prepared = 'as at ' . $date;
        if ($view->items['merge']) {
            $subtitle = 'for Multiple Properties';
            switch ($format) {
                case FILETYPE_PDF:
                    $report = new PDFDataMultiLineReport($filePath, $logoPath, A4_LANDSCAPE);
                    $report->multiLine = true;
                    $report->printRowLines = true;
                    $report->printColumnLines = false;
                    $report->printBorders = false;
                    $report->cache = false;
                    $header = new ReportHeader($_report['reportName'], $subtitle, $prepared);
                    $header->xPos = $report->hMargin;
                    $header->yPos = $report->pageHeight - $report->vMargin;
                    $report->attachObject('header', $header);
                    $footer = new TraccFooter(null, $_report['reportName'], $report->pageSize);
                    $report->attachObject('footer', $footer);
                    break;
                case FILETYPE_XLS:
                    $report = new XLSDataReport($filePath, $_report['reportName']);
                    $report->enableFormatting = true;
                    break;
            }

            $chargesColumnKey = [CHARGE_RENT, CHARGE_PARKING, CHARGE_CLEANING, CHARGE_SINKING, CHARGE_STRATA,
                CHARGE_PROMOTIONS, CHARGE_OTHER, CHARGE_OUTGOINGS, 'total', 'others'];
            if ($format != FILETYPE_SCREEN) {
                foreach ($columns as $c) {

                    if ($format == FILETYPE_XLS) {
                        $c['width'] = (int) $c['width'] / 5; // -- needed to downgrade the widths used from the PDF settings

                        if ($c['columnKey'] == 'area' || $c['columnKey'] == 'areaOnly') {
                            $numberFormat = '_(* #,##0.00_);_(* (#,##0.00);_(* "-"??_);_(@_)';
                        } elseif ($c['columnKey'] == 'tenant') {
                            $numberFormat = '@';
                        } elseif ($c['columnKey'] == 'postcode' || $c['columnKey'] == 'leaseCRN') {
                            $numberFormat = '@';
                        } else {
                            $currency = $_SESSION['country_default']['currency_symbol'];
                            $numberFormat = '_("' . $currency . '"* #,##0.00_);_("' . $currency . '"* \(#,##0.00\);_("' . $currency . '"* "-"??_);_(@_)';
                        }
                    }

                    $c['alignment'] = (($c['alignment'] == null) || ($c['alignment'] == '')) ? 'left' : $c['alignment'];
                    $c['width'] = (($c['width'] == null) || ($c['width'] == '')) ? 150 : $c['width'];

                    $report->addColumn($c['columnKey'], $c['title'], $c['width'], $c['alignment'], $numberFormat);

                    if ((in_array($c['columnKey'], $chargesColumnKey)) && ($format == FILETYPE_XLS)) {
                        $multiLineSubtitle = $pieces = explode("\n", $c['subtitle']);
                        $key[$c['columnKey']] = $multiLineSubtitle[0];
                    } else {
                        $key[$c['columnKey']] = $c['subtitle'];
                    }
                }

                if ($format == FILETYPE_PDF) {
                    $report->addKeyHeaders($key);
                } else {
                    $report->renderHeader();
                    $report->renderLine($key);
                }
                $include = [
                    'includeLeaseNotes' => $view->items['includeLeaseNotes'],
                    'includeRentReviews' => $view->items['includeRentReviews'],
                    'merge' => $view->items['merge'],
                ];
                $report->preparePage();
                $report->renderDataNotes($data, $include);


                if ($format == FILETYPE_PDF) {

                    $report->renderLineSub($totalsAllProperty);
                } else {
                    // $report->renderLine ($totals1);
                    $totalsAllPropertyExcel['annual']['tenant'] = 'Total Annual';
                    $totalsAllPropertyExcel['monthly']['tenant'] = 'Total Monthly';
                    $totalsAllPropertyExcel['m2']['tenant'] = $_SESSION['country_default']['currency_symbol'] . '/' . html_entity_decode($_SESSION['country_default']['area_unit'], ENT_COMPAT);
                    $totalsAllPropertyExcel['m2']['R'] = toMoney(($totalsAllProperty['R'] / $totalsAllProperty['area']));
                    $totalsAllPropertyExcel['annual']['leaseIDName'] = 'Total Annual';
                    $totalsAllPropertyExcel['monthly']['leaseIDName'] = 'Total Monthly';
                    $totalsAllPropertyExcel['m2']['leaseIDName'] = $_SESSION['country_default']['currency_symbol'] . '/' . html_entity_decode($_SESSION['country_default']['area_unit'], ENT_COMPAT);
                    $report->renderLine_bold($totalsAllPropertyExcel['annual']);
                    $report->renderLine_bold($totalsAllPropertyExcel['monthly']);
                    $report->renderLine_bold($totalsAllPropertyExcel['m2']);
                }
                $report->clean();
                $report->close();
            } else {
                $view->items = $context;
                $view->items['columns'] = $columns;
                $view->items['data'] = $data;
            }
        } else {
            switch ($format) {
                case FILETYPE_PDF:
                    $report = new PDFDataMultiLineReport($filePath, $logoPath, A4_LANDSCAPE);
                    $report->multiLine = true;
                    $report->printRowLines = true;
                    $report->printColumnLines = false;
                    $report->printBorders = false;
                    $report->cache = false;
                    $header = new ReportHeader($_report['reportName'], $subtitle, $prepared);
                    $header->xPos = $report->hMargin;
                    $header->yPos = $report->pageHeight - $report->vMargin;
                    $report->attachObject('header', $header);
                    $footer = new TraccFooter(null, $_report['reportName'], $report->pageSize);
                    $report->attachObject('footer', $footer);
                    break;
                case FILETYPE_XLS:
                    $report = new XLSDataReport($filePath, $_report['reportName']);
                    $report->enableFormatting = true;
                    break;
            }

            $chargesColumnKey = [CHARGE_RENT, CHARGE_PARKING, CHARGE_CLEANING, CHARGE_SINKING, CHARGE_STRATA,
                CHARGE_PROMOTIONS, CHARGE_OTHER, CHARGE_OUTGOINGS, 'total', 'others'];
            if ($format != FILETYPE_SCREEN) {
                foreach ($columns as $c) {

                    if ($format == FILETYPE_XLS) {
                        $c['width'] = (int) $c['width'] / 5; // -- needed to downgrade the widths used from the PDF settings

                        if ($c['columnKey'] == 'area' || $c['columnKey'] == 'areaOnly') {
                            $numberFormat = '_(* #,##0.00_);_(* (#,##0.00);_(* "-"??_);_(@_)';
                        } elseif ($c['columnKey'] == 'tenant') {
                            $numberFormat = '@';
                        } elseif ($c['columnKey'] == 'postcode' || $c['columnKey'] == 'leaseCRN') {
                            $numberFormat = '@';
                        } else {
                            $currency = $_SESSION['country_default']['currency_symbol'];
                            $numberFormat = '_("' . $currency . '"* #,##0.00_);_("' . $currency . '"* \(#,##0.00\);_("' . $currency . '"* "-"??_);_(@_)';
                        }
                        $c['alignment'] = 'wrap';
                    }

                    $c['alignment'] = (($c['alignment'] == null) || ($c['alignment'] == '')) ? 'left' : $c['alignment'];
                    $c['width'] = (($c['width'] == null) || ($c['width'] == '')) ? 150 : $c['width'];

                    $report->addColumn($c['columnKey'], $c['title'], $c['width'], $c['alignment'], $numberFormat);

                    if ((in_array($c['columnKey'], $chargesColumnKey)) && ($format == FILETYPE_XLS)) {
                        $multiLineSubtitle = $pieces = explode("\n", $c['subtitle']);
                        $key[$c['columnKey']] = $multiLineSubtitle[0];
                    } else {
                        $key[$c['columnKey']] = $c['subtitle'];
                    }
                }

                if ($format == FILETYPE_PDF) {
                    $report->addKeyHeaders($key);
                } else {
                    $report->renderHeader();
                    $report->renderLine($key);
                }

                $i = 1;

                foreach ($data as $k => $d) {

                    if ($format == FILETYPE_PDF) {
                        $subtitle2 = $subtitle . 'for ' . $k . ' - ' . $data[$k][0]['propertyName'];
                        $header = new ReportHeader($_report['reportName'], $subtitle2, $prepared);
                        $header->xPos = $report->hMargin;
                        $header->yPos = $report->pageHeight - $report->vMargin;
                        $report->attachObject('header', $header);
                        $footer = new TraccFooter(null, $_report['reportName'], $report->pageSize);
                        $report->attachObject('footer', $footer);
                    }

                    if ($format == FILETYPE_XLS) {
                        $xlsPropertyDivider = 'PROPERTY : ' . $k . '-' . $data[$k][0]['propertyName'];
                        $report->renderLine([key($key) => $xlsPropertyDivider]);
                    }
                    $include = [
                        'includeLeaseNotes' => $view->items['includeLeaseNotes'],
                        'includeRentReviews' => $view->items['includeRentReviews'],
                    ];

                    $report->preparePage();
                    $report->renderDataNotes($data[$k], $include);
                    /**subtotal for every property added by Arjay May 05,2017**/

                    if ($format == FILETYPE_PDF) {
                        $report->renderLineSub($totalsEveryProperty[$k]);
                    } else {
                        $totalsEveryProperty[$k]['annual']['tenant'] = 'Total Annual';
                        $totalsEveryProperty[$k]['annual']['leaseIDName'] = 'Total Annual';
                        $totalsEveryProperty[$k]['monthly']['tenant'] = 'Total Monthly';
                        $totalsEveryProperty[$k]['monthly']['leaseIDName'] = 'Total Monthly';
                        $totalsEveryProperty[$k]['m2']['tenant'] = $_SESSION['country_default']['currency_symbol'] . '/' . html_entity_decode($_SESSION['country_default']['area_unit'], ENT_COMPAT);
                        $totalsEveryProperty[$k]['m2']['leaseIDName'] = $_SESSION['country_default']['currency_symbol'] . '/' . html_entity_decode($_SESSION['country_default']['area_unit'], ENT_COMPAT);
                        $report->renderLine_bold($totalsEveryProperty[$k]['annual']);
                        $report->renderLine_bold($totalsEveryProperty[$k]['monthly']);
                        $report->renderLine_bold($totalsEveryProperty[$k]['m2']);
                    }
                    /******************************end*************************/

                    $report->clean();
                    $report->endPage();
                    $i++;
                }
                $report->close();
            } else {
                $view->items = $context;
                $view->items['columns'] = $columns;
                $view->items['data'] = $data;
            }
        }

        // -- if it s a scheduled task - attach the report and email to the requester
        if ($context[IS_TASK]) {
            $email = new EmailTemplate('views/emails/basicEmail.html', SYSTEMURL);
            $attachment =  [];
            $attachment[0]['file'] = REPORTPATH . '/' . $pdfDownloadLink;
            $attachment[0]['content_type'] = 'application/pdf';
            sendMail($_SESSION['email'], $_SESSION['first_name'] . ' ' . $_SESSION['last_name'], $email->toString(), 'Report', $attachment);
            logData('Emailed report to ' . $_SESSION['email']);
            $context[TASK_COMPLETE] = true;
        }
    } else {
        $view->items['statusMessage'] = 'No Leased Property Schedule to print.';
    }

    if ($propertyCount > THRESHOLD_TENANCYSCHEDULE) {
        $view->items['statusMessage'] = 'Due to its complexity, this report has been scheduled and will be emailed to you on completion!';
        $view->render();
    } else {
        $view->items['downloadPath'] = $downloadPath;
        $view->render();
    }
}

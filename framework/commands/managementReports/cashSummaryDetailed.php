<?php

include_once 'functions/page2DebtorsDetailFunctions.php';
include_once 'Residential/residentialReportFunctions.php';
// initialise variables
$line = 40;
$startline = $line;
$pdf->begin_page_ext(595, 842, '');
page_DetailedCashSummary('new', $propertyID, 700);
$line = -120;

$vacant_units =  [];
$tempunits =  [];
$t = 0;
$tenantcod =  [];
$nepokazyvat = 0;
$bftotalC = 0;
$gstfreetotalC = 0;
$billedThistotalC = 0;
$gsttotalC = 0;
$paidgsttotalC = 0;
$paidThisMonthtotalC = 0;
$paidgstfreetotalC = 0;
$closing_balancetotalC = 0;
$unallocThisMonth = 0;

$intotable = "temp_$timestamp";


$detailedUnit = getCashSummaryDetailed($propertyID, $periodFrom, $periodTo);
$openingTotal = 0;
$billedNetTotal = 0;
$billedTaxTotal = 0;
$receivedNetTotal = 0;
$receivedTaxTotal = 0;

foreach ($detailedUnit as $key => $unit) {

    $openingSubTotal = 0;
    $billedNetSubTotal = 0;
    $billedTaxSubTotal = 0;
    $receivedNetSubTotal = 0;
    $receivedTaxSubTotal = 0;

    foreach ($unit as $row) {
        $receivedNet = $row['receivedNet'] * -1;
        $receivedTax = $row['receivedTax'] * -1;

        $openingSubTotal += $row['openingBalance'];
        $billedNetSubTotal += $row['billedNetAmount'];
        $billedTaxSubTotal += $row['billedTaxAmount'];
        $receivedNetSubTotal += $receivedNet;
        $receivedTaxSubTotal += $receivedTax;
    }

    if ((! $view->items['zeroReceipting'] && $row['tenant_name'] == 'Vacant')) {
        unset($detailedUnit[$key]);
    }

}

foreach ($detailedUnit as $unit) {

    if ($line >= 410) {
        $pdf->setlinewidth(0.5);
        $pdf->moveto(220, 695 - $startline);
        $pdf->lineto(220, 40);
        $pdf->stroke();

        $pdf->moveto(270, 695 - $startline);
        $pdf->lineto(270, 40);
        $pdf->stroke();

        $pdf->moveto(520, 695 - $startline);
        $pdf->lineto(520, 40);
        $pdf->stroke();

        $pdf->moveto(370, 695 - $startline);
        $pdf->lineto(370, 40);
        $pdf->stroke();
        $pdf->moveto(570, 710 - $startline);
        $pdf->lineto(570, 40);
        $pdf->stroke();
        $pdf->moveto(25, 40);
        $pdf->lineto(25, 710 - $startline);
        $pdf->stroke();

        $pdf->moveto(25, 40);
        $pdf->lineto(570, 40);
        $pdf->stroke();

        // insert tracc header
        $traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'cirrus8CashSummary', A4_PORTRAIT);
        $traccFooter->prerender($pdf);

        $line = 40;
        $startline = $line;
        $pdf->end_page_ext('');

        $pdf->begin_page_ext(595, 842, '');
        page_DetailedCashSummary('cont', $propertyID, 700);

        $line = -120;

    }

    $pdf->setFontExt($_fonts['Helvetica-Bold'], 7);
    $pdf->showBoxed($unit[0]['unit_desc'], 30, 470 - $line, 370, 20, 'left', '');
    $pdf->showBoxed($unit[0]['tenant_name'], 30, 460 - $line, 470, 20, 'left', '');
    $openingSubTotal = 0;
    $billedNetSubTotal = 0;
    $billedTaxSubTotal = 0;
    $receivedNetSubTotal = 0;
    $receivedTaxSubTotal = 0;

    if ($unit[0]['tenant_name'] != 'Vacant') {
        foreach ($unit as $row) {

            if ($row['openingBalance'] == 0  && $row['billedNetAmount'] == 0  && $row['billedTaxAmount'] == 0  && $row['receivedNet'] == 0  && $row['receivedTax'] == 0) {
                continue;
            }

            if ($line >= 410) {
                $pdf->setlinewidth(0.5);
                $pdf->moveto(220, 695 - $startline);
                $pdf->lineto(220, 40);
                $pdf->stroke();

                $pdf->moveto(270, 695 - $startline);
                $pdf->lineto(270, 40);
                $pdf->stroke();

                $pdf->moveto(520, 695 - $startline);
                $pdf->lineto(520, 40);
                $pdf->stroke();

                $pdf->moveto(370, 695 - $startline);
                $pdf->lineto(370, 40);
                $pdf->stroke();
                $pdf->moveto(570, 710 - $startline);
                $pdf->lineto(570, 40);
                $pdf->stroke();
                $pdf->moveto(25, 40);
                $pdf->lineto(25, 710 - $startline);
                $pdf->stroke();

                $pdf->moveto(25, 40);
                $pdf->lineto(570, 40);
                $pdf->stroke();

                // insert tracc header
                $traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'cirrus8CashSummary', A4_PORTRAIT);
                $traccFooter->prerender($pdf);

                $line = 40;
                $startline = $line;
                $pdf->end_page_ext('');

                $pdf->begin_page_ext(595, 842, '');
                page_DetailedCashSummary('cont', $propertyID, 700);

                $line = -120;

                $pdf->setFontExt($_fonts['Helvetica-Bold'], 7);
                $pdf->showBoxed($unit[0]['tenant_name'] . ' (...)', 30, 460 - $line, 370, 20, 'left', '');
            }

            $receivedNet = $row['receivedNet'] * -1;
            $receivedTax = $row['receivedTax'] * -1;
            $closing_balance = number_format(($row['openingBalance'] + $row['billedNetAmount'] + $row['billedTaxAmount']) - ($receivedNet + $receivedTax), 2, '.', '');
            $pdf->setFontExt($_fonts['Helvetica'], 7);
            $pdf->showBoxed($row['account_name'] . ' ' . $row['fromDate'] . ' - ' . $row['toDate'], 30, 450 - $line, 270, 20, 'left', '');
            $pdf->save();
            $pdf->setColorExt('fill', 'rgb', 1, 1, 1, 0);
            $pdf->rect(250, 450 - $line, 70, 18);
            $pdf->fill();
            $pdf->restore();
            $pdf->showBoxed(formatting($row['openingBalance'], 2), 220, 450 - $line, 48, 20, 'right', '');
            $pdf->showBoxed(formatting($row['billedNetAmount'], 2), 270, 450 - $line, 48, 20, 'right', '');
            $pdf->showBoxed(formatting($row['billedTaxAmount'], 2), 320, 450 - $line, 48, 20, 'right', '');
            $pdf->showBoxed(formatting($receivedNet, 2), 370, 450 - $line, 48, 20, 'right', '');
            $pdf->showBoxed(formatting($receivedTax, 2), 420, 450 - $line, 48, 20, 'right', '');
            $pdf->showBoxed(formatting($receivedNet + $receivedTax, 2), 470, 450 - $line, 48, 20, 'right', '');
            $pdf->showBoxed(formatting($closing_balance, 2), 520, 450 - $line, 48, 20, 'right', '');

            $openingSubTotal += $row['openingBalance'];
            $billedNetSubTotal += $row['billedNetAmount'];
            $billedTaxSubTotal += $row['billedTaxAmount'];
            $receivedNetSubTotal += $receivedNet;
            $receivedTaxSubTotal += $receivedTax;

            $openingTotal += $row['openingBalance'];
            $billedNetTotal += $row['billedNetAmount'];
            $billedTaxTotal += $row['billedTaxAmount'];
            $receivedNetTotal += $receivedNet;
            $receivedTaxTotal += $receivedTax;
            $line = $line + 10;
        }
    }

    if ($line >= 410) {
        $pdf->setlinewidth(0.5);
        $pdf->moveto(220, 695 - $startline);
        $pdf->lineto(220, 40);
        $pdf->stroke();

        $pdf->moveto(270, 695 - $startline);
        $pdf->lineto(270, 40);
        $pdf->stroke();

        $pdf->moveto(520, 695 - $startline);
        $pdf->lineto(520, 40);
        $pdf->stroke();

        $pdf->moveto(370, 695 - $startline);
        $pdf->lineto(370, 40);
        $pdf->stroke();
        $pdf->moveto(570, 710 - $startline);
        $pdf->lineto(570, 40);
        $pdf->stroke();
        $pdf->moveto(25, 40);
        $pdf->lineto(25, 710 - $startline);
        $pdf->stroke();

        $pdf->moveto(25, 40);
        $pdf->lineto(570, 40);
        $pdf->stroke();

        // insert tracc header
        $traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'cirrus8CashSummary', A4_PORTRAIT);
        $traccFooter->prerender($pdf);

        $line = 40;
        $startline = $line;
        $pdf->end_page_ext('');

        $pdf->begin_page_ext(595, 842, '');
        page_DetailedCashSummary('cont', $propertyID, 700);

        $line = -120;

    }

    $line = $line - 7;
    $pdf->setColorExt('fill', 'rgb', 0.88, 0.88, 0.88, 0);
    $pdf->rect(220, 445 - $line, 350, 15);
    $pdf->fill();
    $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);


    $closing_sub_total =  ($openingSubTotal + $billedNetSubTotal + $billedTaxSubTotal) - $receivedNetSubTotal - $receivedTaxSubTotal;
    $pdf->setlinewidth(0.5);
    $pdf->moveto(220, 460 - $line);
    $pdf->lineto(570, 460 - $line);
    $pdf->stroke();
    $pdf->setFontExt($_fonts['Helvetica-Bold'], 7);
    $pdf->showBoxed(formatting($openingSubTotal, 2), 220, 438 - $line, 48, 20, 'right', '');
    $pdf->showBoxed(formatting($billedNetSubTotal, 2), 270, 438 - $line, 48, 20, 'right', '');
    $pdf->showBoxed(formatting($billedTaxSubTotal, 2), 320, 438 - $line, 48, 20, 'right', '');
    $pdf->showBoxed(formatting($receivedNetSubTotal, 2), 370, 438 - $line, 48, 20, 'right', '');
    $pdf->showBoxed(formatting($receivedTaxSubTotal, 2), 420, 438 - $line, 48, 20, 'right', '');
    $pdf->showBoxed(formatting($receivedNetSubTotal + $receivedTaxSubTotal, 2), 470, 438 - $line, 48, 20, 'right', '');
    $pdf->showBoxed(formatting($closing_sub_total, 2), 520, 438 - $line, 48, 20, 'right', '');


    $pdf->moveto(220, 445 - $line);
    $pdf->lineto(570, 445 - $line);
    $pdf->stroke();
    $line = $line + 45;
}


if ($line >= 410) {
    $pdf->setlinewidth(0.5);
    $pdf->moveto(220, 695 - $startline);
    $pdf->lineto(220, 40);
    $pdf->stroke();

    $pdf->moveto(270, 695 - $startline);
    $pdf->lineto(270, 40);
    $pdf->stroke();

    $pdf->moveto(520, 695 - $startline);
    $pdf->lineto(520, 40);
    $pdf->stroke();

    $pdf->moveto(370, 695 - $startline);
    $pdf->lineto(370, 40);
    $pdf->stroke();
    $pdf->moveto(570, 710 - $startline);
    $pdf->lineto(570, 40);
    $pdf->stroke();
    $pdf->moveto(25, 40);
    $pdf->lineto(25, 710 - $startline);
    $pdf->stroke();

    $pdf->moveto(25, 40);
    $pdf->lineto(570, 40);
    $pdf->stroke();

    // insert tracc header
    $traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'cirrus8CashSummary', A4_PORTRAIT);
    $traccFooter->prerender($pdf);

    $line = 40;
    $startline = $line;
    $pdf->end_page_ext('');

    $pdf->begin_page_ext(595, 842, '');
    page_DetailedCashSummary('cont', $propertyID, 700);

    $line = -120;

}


$line = $line - 30;
$pdf->setColorExt('fill', 'rgb', 0.88, 0.88, 0.88, 0);
$pdf->rect(220, 430 - $line, 350, 16);
$pdf->fill();
$pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
$pdf->setlinewidth(0.5);
// total line
$pdf->moveto(220, 446 - $line);
$pdf->lineto(570, 446 - $line);
$pdf->stroke();

$grossReceipts =  $receivedNetTotal + $receivedTaxTotal;
$closing_total =  ($openingTotal + $billedNetTotal + $billedTaxTotal) - $receivedNetTotal - $receivedTaxTotal;
$pdf->setFontExt($_fonts['Helvetica-Bold'], 7);
$pdf->showBoxed('Total', 30, 423 - $line, 170, 20, 'left', '');
$pdf->showBoxed(formatting($openingTotal, 2), 220, 423 - $line, 48, 20, 'right', '');
$pdf->showBoxed(formatting($billedNetTotal, 2), 270, 423 - $line, 48, 20, 'right', '');
$pdf->showBoxed(formatting($billedTaxTotal, 2), 320, 423 - $line, 48, 20, 'right', '');
$pdf->showBoxed(formatting($receivedNetTotal, 2), 370, 423 - $line, 48, 20, 'right', '');
$pdf->showBoxed(formatting($receivedTaxTotal, 2), 420, 423 - $line, 48, 20, 'right', '');
$pdf->showBoxed(formatting($grossReceipts, 2), 470, 423 - $line, 48, 20, 'right', '');
$pdf->showBoxed(formatting($closing_total, 2), 520, 423 - $line, 48, 20, 'right', '');

$pdf->setlinewidth(0.5);
// bottom line
$pdf->moveto(25, 430 - $line);
$pdf->lineto(570, 430 - $line);
$pdf->stroke();

$pdf->setlinewidth(0.5);
// tenant and opening
$pdf->moveto(220, 695 - $startline);
$pdf->lineto(220, 430 - $line);
$pdf->stroke();
// opening and bill
$pdf->moveto(270, 695 - $startline);
$pdf->lineto(270, 430 - $line);
$pdf->stroke();
// received and closing
$pdf->moveto(520, 695 - $startline);
$pdf->lineto(520, 430 - $line);
$pdf->stroke();
// bill and received
$pdf->moveto(370, 695 - $startline);
$pdf->lineto(370, 430 - $line);
$pdf->stroke();
// right side bar
$pdf->moveto(570, 710 - $startline);
$pdf->lineto(570, 430 - $line);
$pdf->stroke();

// left side bar
$pdf->moveto(25, 430 - $line);      // tonkaja levaja
$pdf->lineto(25, 710 - $startline);
$pdf->stroke();

$line = $line + 10;
if ($line <= 345) {
    $pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
    $pdf->showBoxed('PAYMENTS', 20, 400 - $line, 550, 30, 'center', '');
    $line = $line + 11;

    $pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
    $pdf->showBoxed('Supplier', 27, 400 - $line, 95, 30, 'left', '');
    $pdf->showBoxed('Description', 122, 400 - $line, 98, 30, 'left', ''); // 170
    $pdf->showBoxed('Invoice #', 222, 400 - $line, 48, 30, 'center', ''); // 90
    $pdf->showBoxed('From', 270, 400 - $line, 48, 30, 'center', '');
    $pdf->showBoxed('To', 320, 400 - $line, 48, 30, 'center', '');
    $pdf->showBoxed('Net', 370, 400 - $line, 48, 30, 'center', '');
    $pdf->showBoxed('GST', 420, 400 - $line, 48, 30, 'center', '');
    $pdf->showBoxed('Total', 470, 400 - $line, 48, 30, 'center', '');

    $line = $line + 1;
}
$batchnr_result = getPayBatchNrs($propertyID, $periodFrom, $periodTo);

$totalNet_payments = $totalGST_payments = $totalGross_payments = 0;

foreach ($batchnr_result as $k => $thisLine) {
    $batch = $thisLine['pmxc_t_batch'];
    $linen = $thisLine['pmxc_t_line'];
    // $alloc_dt = $thisLine['pmxc_alloc_dt'];
    $alloc_dt = $thisLine['paymentDate'];
    $alloc_amt = $thisLine['pmxc_alloc_amt'];
    $alloc_tax = $thisLine['pmxc_tax_amt'];

    $details_result = getPaymentAccountDetails($batch, $linen, $propertyID);

    $tdescription   = $details_result['description'];

    $date_from  = $details_result['spare_date_1'];
    $date_to   = $details_result['spare_date_2'];

    $trans_date     = $alloc_dt;
    $aptr_tax_amt  = $alloc_tax;

    $supplier_code = $details_result['creditor_code'];

    $payee_name =  $details_result['creditor_name'];
    $payee_name = limit_name($payee_name, 35);

    $invoice = $details_result['ref_1'];
    $net_amt = $alloc_amt * (-1) - $aptr_tax_amt;

    //        $date_from = dateFormattingPrintShort($date_from);
    //        $date_to = dateFormattingPrintShort($date_to);

    $net_amt_display = formatting($net_amt);
    $aptr_tax_amt_display  = formatting($aptr_tax_amt);
    $gross_amt = $alloc_amt * (-1);
    $gross_amt_display = formatting($gross_amt);

    $totalNet_payments = $totalNet_payments + $net_amt;
    $totalGST_payments = $totalGST_payments + $aptr_tax_amt;
    $totalGross_payments = $totalGross_payments + $gross_amt;

    $invoice = substr($invoice, 0, 10);
    $tdescription = limit_name(character_special($tdescription), 38); // 38

    if ($line >= 370) {
        page_payment('cont', $propertyID, 700);

        $pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
        $pdf->showBoxed('PAYMENTS', 20, 400 - $line, 550, 30, 'center', '');
        $line = $line + 11;

        $pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
        $pdf->showBoxed('Supplier', 27, 400 - $line, 95, 30, 'left', '');
        $pdf->showBoxed('Description', 122, 400 - $line, 98, 30, 'left', ''); // 170
        $pdf->showBoxed('Invoice #', 222, 400 - $line, 48, 30, 'left', ''); // 90
        $pdf->showBoxed('From', 270, 400 - $line, 48, 30, 'center', '');
        $pdf->showBoxed('To', 320, 400 - $line, 48, 30, 'center', '');
        $pdf->showBoxed('Net', 370, 400 - $line, 48, 30, 'center', '');
        $pdf->showBoxed('GST', 420, 400 - $line, 48, 30, 'center', '');
        $pdf->showBoxed('Total', 470, 400 - $line, 48, 30, 'center', '');

        $line = $line + 1;
    }


    $pdf->setFontExt($_fonts['Helvetica'], 7);
    $pdf->showBoxed("$payee_name", 27, 390 - $line + 20, 95, 10, 'left', '');
    $pdf->showBoxed("$tdescription", 122, 390 - $line + 20, 98, 10, 'left', ''); // 170
    $pdf->showBoxed("$invoice", 222, 390 - $line, 48, 30, 'left', ''); // 90
    $pdf->showBoxed("$date_from", 270, 390 - $line, 48, 30, 'right', '');
    $pdf->showBoxed("$date_to", 320, 390 - $line, 48, 30, 'right', '');
    $pdf->showBoxed("$net_amt_display", 370, 390 - $line, 48, 30, 'right', '');
    $pdf->showBoxed("$aptr_tax_amt_display", 420, 390 - $line, 48, 30, 'right', '');
    $pdf->showBoxed("$gross_amt_display", 470, 390 - $line, 48, 30, 'right', '');


    $line = $line + 10;

}

$line = $line + 10;
if ($line >= 370) {
    page_payment('cont', $propertyID, 700);
}

$pdf->setColorExt('fill', 'rgb', 0.88, 0.88, 0.88, 0);
$pdf->rect(370, 408 - $line, 150, 15);
$pdf->fill();
$pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);

$pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
$pdf->showBoxed('Total Payments', 27, 390 - $line, 95, 30, 'left', '');
$pdf->setFontExt($_fonts['Helvetica-Bold'], 7);
$pdf->showBoxed(formatting($totalNet_payments), 370, 390 - $line, 48, 30, 'right', '');
$pdf->showBoxed(formatting($totalGST_payments), 420, 390 - $line, 48, 30, 'right', '');
$pdf->showBoxed(formatting($totalGross_payments), 470, 390 - $line, 48, 30, 'right', '');



$line = $line + 20;

if ($line >= 370) {
    page_payment('cont', $propertyID, 700);
}

$cashmov = $grossReceipts - $totalGross_payments;
$pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
$pdf->showBoxed('Cash Movement For The Period', 27, 390 - $line, 130, 30, 'left', '');
$pdf->setFontExt($_fonts['Helvetica-Bold'], 7);
$pdf->showBoxed(formatting($cashmov), 470, 390 - $line, 48, 30, 'right', '');

$line = $line + 20;

if ($line >= 370) {
    page_payment('cont', $propertyID, 700);
}

$receipts = total_cash_receipts($propertyID, $startFinancialYear, $previousPeriodTo);
$payments = total_cash_payments($propertyID, $startFinancialYear, $previousPeriodTo);

$receiptsA = total_cash_receipts($propertyID, STARTDATE, $financialPeriodToPY);
$paymentsA = total_cash_payments($propertyID, STARTDATE, $financialPeriodToPY);


$diffY = $receipts - $payments;
$diffA = $receiptsA - $paymentsA;
$diff = $diffA + $diffY;

$diff_display = formatting($diff);
$openingbalance = $diff;

$pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
$pdf->showBoxed('Opening Cash Balance', 27, 390 - $line, 130, 30, 'left', '');
$pdf->setFontExt($_fonts['Helvetica-Bold'], 7);
$pdf->showBoxed($diff_display, 470, 390 - $line, 48, 30, 'right', '');

$line = $line + 20;
// OWNER REMITTANCES

$total_pmts_duringM = getPaymentsToOwners($propertyID, $periodFrom, $periodTo); // trim($query_result["amount"]);
$creditorResult = getCreditorDetails($propertyID, $periodFrom, $periodTo);	// $query_result = mssql_query($query);

foreach ($creditorResult as $key => $thisRow) {

    $ind_pmts_duringY_display = formatting($thisRow['pmxc_alloc_amt']);
    $ind_pmxc_s_creditor_display = $thisRow['pmxc_s_creditor'];
    $cred_name = $thisRow['pmco_name'];
    $cred_name = ellipsitize($cred_name, 38);
    $ind_pmxc_alloc_dt =  $thisRow['pmxc_alloc_dt'];
    $tdescription = $thisRow['description'];

    if ($line >= 370) {
        page_payment('cont', $propertyID, 700);
    }

    $pdf->setFontExt($_fonts['Helvetica'], 7);
    $pdf->showBoxed("$cred_name", 27, 390 - $line + 20, 95, 10, 'left', '');
    $pdf->showBoxed("$tdescription", 122, 390 - $line, 150, 30, 'left', '');
    $pdf->showBoxed("$ind_pmxc_alloc_dt", 320, 390 - $line, 48, 30, 'right', '');
    $pdf->setFontExt($_fonts['Helvetica-Bold'], 7);
    $pdf->showBoxed("$ind_pmts_duringY_display", 470, 390 - $line, 48, 30, 'right', '');
    $line = $line + 10;

}

$ownerpayments = -($total_pmts_duringM);
$ownerpayments_display = formatting($ownerpayments);

$subtotal_balance = $cashmov + $openingbalance;
// CLOSING BALANCE
$closing_cashbalance = $subtotal_balance - $ownerpayments;
$line = $line + 10;

if ($line >= 370) {
    page_payment('cont', $propertyID, 700);
}


$pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
$pdf->showBoxed('Closing Cash Balance', 27, 390 - $line, 130, 30, 'left', '');
$pdf->setFontExt($_fonts['Helvetica-Bold'], 7);
$pdf->showBoxed(formatting($closing_cashbalance), 470, 390 - $line, 48, 30, 'right', '');

$traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'cirrus8CashSummary', A4_PORTRAIT);
$traccFooter->prerender($pdf);

$pdf->end_page_ext('');

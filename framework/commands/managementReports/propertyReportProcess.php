<?php

/**
 * Process information gathered in order to generate a property report.
 *
 * @param  $context  array Mandatory. Referenced array containing data needed to continue.
 *
 * <AUTHOR> <PERSON><PERSON><PERSON> Reyes
 *
 * @modified 2012-09-17 Part of my first npms conversion. This is were the dizzy starts... [Morph]
 * @modified 2012-12-07: Added display on screen option. [Morph]
 *
 * TODO:
 * 2012-09-18 - Excel on pend...
 * 2012-09-18 - SQL statements are currently in here. Will move it out to dbInterfaces later.. hopefully...
 **/
function formatting_num($number)
{
    if ($number == 0) {
        return $number = '';
    } else {
        return number_format($number, 2);
    }
}

function propertyReportProcess($context)
{
    global $clientDirectory, $pathPrefix, $clientDB, $filePath;
    global $dbh; // 2012-09-18: temporary
    $dbh->selectDatabase($clientDB);

    include_once SYSTEMPATH . '/lib/reportLib/reportincludesAll.php';


    if (! isPostback()) {
        $view = new MasterPage(userViews(), '/managementReports/propertyReportProcess.html');
        $view->setSection($context['module']);
    } else {
        $view = new UserControl(userViews(), '/managementReports/propertyReportProcess.html');
    }

    $view->bindAttributesFrom($_REQUEST);
    $validationErrors = [];

    $properties = ($view->items['property']) ? deserializeParameters($view->items['property']) : null;
    [$t_day, $t_month, $t_year] = explode('/', $view->items['toDate']);
    $asAtDate = ($t_day && $t_month && $t_year) ? "{$t_day}/{$t_month}/{$t_year}" : null;

    $format = 'pdf';
    $_filePath = "{$pathPrefix}{$clientDirectory}/{$format}/" . DOC_PROPERTYREPORT . '/';
    $_downloadPath = "{$clientDirectory}/{$format}/" . DOC_PROPERTYREPORT;
    check_dirpath($_filePath);
    $file = DOC_PROPERTYREPORT . '_' . date('Ymd') . ".{$format}";
    $filePath = $_filePath . $file;
    $downloadPath = "{$_downloadPath}/{$file}";
    $countryCode = dbGetDefaultCountry();
    // Report Type
    switch ($view->items['reportType']) {
        case 1:
            include __DIR__ . '/propertyReport/propertyReport.php';
            break;
        case 2:
            include __DIR__ . '/propertyReport/propertyAbstractReport.php';
            break;
        case 0:
            $pdf = new PDFlibExt();
            $pdf->set_option('license=' . PDFLIB_LICENSE);
            $pdf->set_option('stringformat=utf8');

            $pdf->begin_document($filePath, '');
            $pdf->set_info('Author', 'cirrusBooks | cirrus8.com.au - Online Commercial Property Management Solution');
            $pdf->set_info('Title', 'Property Report');


            $_fonts['Helvetica'] = $pdf->load_font('Helvetica', 'host', '');
            $_fonts['Helvetica-Bold'] = $pdf->load_font('Helvetica-Bold', 'host', '');

            foreach ($properties as $prop_code) {
                $total_ownership = 0;
                $page = 1;

                $dbh->selectDatabase($clientDB);
                $propnameSQL = '
                    SELECT
                        pmco_gst_code,
                        pmpr_name,
                        pmpr_owner,
                        pmpr_portfolio,
                        pmpr_street,
                        pmpr_city,
                        pmpr_state,
                        pmpr_postcode,
                        pmpr_prop_group,
                        pmpr_prop_type,
                        pmpr_agent,
                        pmpr_c_supp,
                        pmpr_mgmt_gst,
                        pmpr_p_lease,
                        pmpr_p_lic,
                        pmpr_p_cas,
                        pmpr_or_basis,
                        pmpr_or_gstper,
                        prmf_method AS pmpr_c_type,
                        prmf_percentage AS pmpr_c_pct,
                        prmf_account AS pmpr_c_acc,
                        prmf_amount AS pmpr_c_val
                    FROM
                        pmpr_property,
                        pmco_company
                        ,prmf_fees
                    WHERE
                        pmpr_prop = ?
                        AND pmco_code = pmpr_owner
                        AND pmpr_prop = prmf_prop
                        and CONVERT(datetime, ?, 103) BETWEEN prmf_start_period and prmf_end_period';

                $propname_result = $dbh->executeSet($propnameSQL, false, true, [$prop_code, $asAtDate]);
                foreach ($propname_result as $row) {
                    // while ($row = mssql_fetch_array($propname_result))
                    // {
                    $prop_name = $row['pmpr_name']; // $propname_result["pmpr_name"];
                    $prop_owner = $row['pmpr_owner']; // $propname_result["pmpr_owner"];
                    $prop_owner = trim($prop_owner);
                    $prop_portfolio = $row['pmpr_portfolio']; // $propname_result["pmpr_portfolio"];
                    $prop_street = $row['pmpr_street']; // $propname_result["pmpr_street"];
                    $prop_city = $row['pmpr_city']; // $propname_result["pmpr_city"];
                    $prop_state = $row['pmpr_state']; // $propname_result["pmpr_state"];
                    $prop_postcode = $row['pmpr_postcode']; // $propname_result["pmpr_postcode"];
                    $prop_group = $row['pmpr_prop_group']; // $propname_result["pmpr_prop_group"];
                    $prop_type = $row['pmpr_prop_type']; // $propname_result["pmpr_prop_type"];
                    $prop_agent = $row['pmpr_agent']; // $propname_result["pmpr_agent"];
                    // $prop_gst = $propname_result["pmpr_gst_code"];

                    // MAN FEES AREA
                    $pmpr_c_pct = $row['pmpr_c_pct']; // $propname_result["pmpr_c_pct"];
                    $prop_c_type = $row['pmpr_c_type']; // $propname_result["pmpr_c_type"];
                    $prop_c_val = $row['pmpr_c_val']; // $propname_result["pmpr_c_val"];
                    $prop_c_pct = $row['pmpr_c_pct']; // $propname_result["pmpr_c_pct"];
                    $prop_c_acc = $row['pmpr_c_acc']; // $propname_result["pmpr_c_acc"];
                    $prop_c_supp = $row['pmpr_c_supp']; // $propname_result["pmpr_c_supp"];
                    $prop_mgmt_gst = $row['pmpr_mgmt_gst']; // $propname_result["pmpr_mgmt_gst"];

                    // PARKING
                    $prop_p_lease = $row['pmpr_p_lease']; // $propname_result["pmpr_p_lease"];
                    $prop_p_lic = $row['pmpr_p_lic']; // $propname_result["pmpr_p_lic"];
                    $prop_p_cas = $row['pmpr_p_cas']; // $propname_result["pmpr_p_cas"];

                    // GST
                    $prop_basis = $row['pmpr_or_basis']; // $propname_result["pmpr_or_basis"];
                    $prop_gst_per = $row['pmpr_or_gstper']; // $propname_result["pmpr_or_gstper"];
                }

                $prop_gst_per_display = '';
                if ($prop_gst_per == 'Q') {
                    $prop_gst_per_display = '/ QUARTERLY';
                }

                if ($prop_gst_per == 'M') {
                    $prop_gst_per_display = '/ MONTHLY';
                }

                $prop_basis_display = '';
                if ($prop_basis == 'C') {
                    $prop_basis_display = '/ CASH';
                }

                if ($prop_basis == 'A') {
                    $prop_basis_display = '/ ACCRUALS';
                }

                $pm_name = "SELECT pmzz_desc
                        FROM pmzz_param
                        WHERE (pmzz_par_type = 'PORTMGR')
                        AND (pmzz_code = ?)";
                $pm = $dbh->executeScalar($pm_name, [$prop_portfolio]);
                // $pm = $pm_name_result["pmzz_desc"];

                $clientSQL = 'SELECT pmco_name FROM pmco_company WHERE pmco_code = ?';
                $owner = $dbh->executeScalar($clientSQL, [$prop_owner]);
                // $owner = $client_result['pmco_name'];
                // $owner = trim ($owner);
                $mainowner = $owner;

                // list ($prop_code, $prop_name) = explode ('||', $properties);

                $line = -60;

                $contactSQL = 'SELECT * FROM pmpt_p_contact WHERE (pmpt_prop = ?)';
                $contact_result = $dbh->executeSet($contactSQL, false, true, [$prop_code]);
                $contact_number = count($contact_result ?? []);

                $pdf->begin_page_ext(842, 595, '');
                $pdf->setFontExt($_fonts['Helvetica-Bold'], 12);
                $pdf->showBoxed("PROPERTY REPORT: {$prop_name}", 22, 485 - $line, 820, 20, 'center', '');
                $pdf->setFontExt($_fonts['Helvetica-Bold'], 10);

                $pdf->showBoxed("Owner: {$owner} ({$prop_owner})", 22, 470 - $line, 820, 20, 'center', '');

                $line += 5;
                $pdf->showBoxed(
                    'Property ' . $_SESSION['country_default']['tax_label'] . ":  {$prop_gst} {$prop_basis_display} {$prop_gst_per_display}",
                    20,
                    455 - $line,
                    580,
                    20,
                    'left',
                    ''
                );
                $pdf->showBoxed(
                    ucwords(strtolower($_SESSION['country_default']['property_manager'])) . ": {$pm}",
                    535,
                    455 - $line,
                    280,
                    20,
                    'right',
                    ''
                );
                // PROPERTY CONTACTS
                $pdf->setlinewidth(0.5);
                $pdf->setColorExt('fill', 'rgb', 0.9, 0.9, 0.9, 0);
                $pdf->rect(20, 446 - $line, 370, 13); // draw the rectangle
                $pdf->fill();
                $pdf->rect(20, 446 - $line, 370, 13); // draw the rectangle
                $pdf->stroke();
                $pdf->setColorExt('fill', 'rgb', 0, 0, 0, 0);
                $pdf->showBoxed('Property Contacts', 160, 440 - $line, 100, 20, 'center', '');
                $pdf->setFontExt($_fonts['Helvetica-Bold'], 7);

                foreach ($contact_result as $row) {
                    $c_serial = $row['pmpt_serial'];
                    $c_name = $row['pmpt_name'];
                    $c_role = '';
                    $role_name = "
                        SELECT
                            pmzz_desc
                        FROM
                            pmpw_p_role,
                            pmzz_param
                        WHERE (pmzz_code = pmpw_type) AND (pmzz_par_type = 'ROLETYPE') AND pmpw_prop = ? AND pmpw_serial = ?";
                    $c_role = $dbh->executeScalar($role_name, [$prop_code, $c_serial]);
                    // while($thisRow = mssql_fetch_array($role_name_result)) { $c_role =  $thisRow['pmzz_desc']; }


                    $pdf->setFontExt($_fonts['Helvetica-Bold'], 7);
                    $pdf->showBoxed("{$c_role}", 46, 420 - $line - $subline, 600, 20, 'left', '');
                    $pdf->setFontExt($_fonts['Helvetica'], 7);
                    $pdf->showBoxed("{$c_name}", 180, 420 - $line - $subline, 600, 20, 'left', '');
                    $subline += 10;
                    $phone = "SELECT * FROM pmpj_p_phone
                        WHERE
                        (pmpj_prop = ?)
                        AND pmpj_c_serial = ?
                        AND (pmpj_ph_code <> '' )";
                    $phone_result = $dbh->executeSet($phone, false, true, [$prop_code, $c_serial]);

                    /*$phone_number = sizeof($phone_result);
                    $j=0;
                    if ($phone_number == 0) :	//Andrew to check
                    elseif ($phone_number > 0) :
                    while ($j < $phone_number) :*/
                    foreach ($phone_result as $row) {
                        $phone_code = $row['pmpj_ph_code'];
                        $phone_no = $row['pmpj_phone_no'];

                        $phone_name = "SELECT pmzz_desc
                            FROM pmzz_param
                            WHERE (pmzz_par_type = 'PHONETYPE')
                            AND (pmzz_code = '{$phone_code}')";
                        $phone_code_name = $dbh->executeScalar($phone_name);

                        // $phone_name_number = sizeof($phone_name_result);
                        // $phone_code_name = $phone_name_result["pmzz_desc"];
                        $pdf->setFontExt($_fonts['Helvetica-Bold'], 7);
                        $pdf->showBoxed("   {$phone_code_name} ", 40, 420 - $line - $subline, 600, 20, 'left', '');
                        $pdf->setFontExt($_fonts['Helvetica'], 7);
                        $pdf->showBoxed("   {$phone_no} ", 180, 420 - $line - $subline, 600, 20, 'left', '');
                        $subline += 10;
                    }// end of foreach($phone_result as $row)
                }// end of foreach($contact_result as $row)


                $line = $line + $subline + 20;


                $floorSQL = 'SELECT *
                    FROM PMPF_P_FLOOR
                    WHERE (PMPF_PROP = ?)
                    ORDER BY PMPF_SEQ';
                $floor_result = $dbh->executeSet($floorSQL, false, true, [$prop_code]);
                $floor_number = count($floor_result ?? []);

                // LEVELS & PARKING
                $pdf->setlinewidth(0.5);
                $pdf->setColorExt('fill', 'rgb', 0.9, 0.9, 0.9, 0);
                $pdf->rect(20, 446 - $line, 370, 13); // draw the rectangle
                $pdf->fill();
                $pdf->rect(20, 446 - $line, 370, 13); // draw the rectangle
                $pdf->stroke();
                $pdf->setColorExt('fill', 'rgb', 0, 0, 0, 0);
                $pdf->setFontExt($_fonts['Helvetica-Bold'], 10);
                $pdf->showBoxed('Parking', 160, 440 - $line, 100, 20, 'center', '');
                $pdf->setFontExt($_fonts['Helvetica'], 7);


                $pdf->showBoxed('Lease', 20, 420 - $line, 600, 20, 'left', '');
                $pdf->showBoxed('Licensed', 60, 420 - $line, 600, 20, 'left', '');
                $pdf->showBoxed('Casual', 100, 420 - $line, 600, 20, 'left', '');
                $line += 10;
                $pdf->showBoxed("{$prop_p_lease}", 20, 420 - $line, 600, 20, 'left', '');
                $pdf->showBoxed("{$prop_p_lic}", 60, 420 - $line, 600, 20, 'left', '');
                $pdf->showBoxed("{$prop_p_cas}", 100, 420 - $line, 600, 20, 'left', '');

                $subline = 0;

                // UNITS
                $pdf->setlinewidth(0.5);
                $pdf->setColorExt('fill', 'rgb', 0.9, 0.9, 0.9, 0);
                $pdf->rect(20, 407 - $line, 370, 13); // draw the rectangle
                $pdf->fill();
                $pdf->rect(20, 407 - $line, 370, 13); // draw the rectangle
                $pdf->stroke();
                $pdf->setColorExt('fill', 'rgb', 0, 0, 0, 0);
                $pdf->setFontExt($_fonts['Helvetica-Bold'], 10);
                $pdf->showBoxed('Units', 160, 400 - $line, 100, 20, 'center', '');
                $pdf->setFontExt($_fonts['Helvetica-Bold'], 7);
                $pdf->showBoxed('UNIT', 20, 385 - $line, 600, 20, 'left', '');
                $pdf->showBoxed('DESCRIPTION', 60, 385 - $line, 600, 20, 'left', '');
                $pdf->showBoxed('FLOOR', 265, 385 - $line, 600, 20, 'left', '');
                $pdf->showBoxed('SEQ', 320, 385 - $line, 600, 20, 'left', '');
                $pdf->showBoxed('AREA', 360, 385 - $line, 600, 20, 'left', '');


                $line += 10;
                $pdf->setFontExt($_fonts['Helvetica'], 7);

                $total_area = 0;

                if ($asAtDate) {
                    $unitQuery = "
                        SELECT
                            pmua_unit_area.pmua_prop, pmpf_p_floor.pmpf_floor, pmpf_p_floor.pmpf_seq, pmua_unit_area.pmua_unit, pmpu_p_unit.pmpu_desc, pmua_unit_area.pmua_status, pmua_unit_area.pmua_area as area, pmua_unit_area.pmua_lease as lease
                        FROM
                            pmpf_p_floor,  pmpu_p_unit,  pmua_unit_area
                        WHERE
                            pmua_unit_area.pmua_prop = pmpu_p_unit.pmpu_prop
                            AND pmua_unit_area.pmua_unit = pmpu_p_unit.pmpu_unit
                            AND pmpu_p_unit.pmpu_prop = pmpf_p_floor.pmpf_prop
                            AND pmpu_p_unit.pmpu_floor = pmpf_p_floor.pmpf_floor
                            AND ((pmua_unit_area.pmua_prop=?)
                            AND (CONVERT(datetime, ?, 103) BETWEEN pmua_unit_area.pmua_from_dt AND pmua_unit_area.pmua_to_dt)
                            AND (pmua_unit_area.pmua_status<>'I'))
                            ORDER BY pmua_unit_area.pmua_unit";
                    $units_result = $dbh->executeSet($unitQuery, false, true, [$prop_code, $asAtDate]);
                    $units_number = count($units_result ?? []);
                    $headset = 0;
                    $roffset = 0;
                    $subheadset = 0;
                    $line += 15;
                    $subsubline = -100;

                    if ($units_result) {
                        foreach ($units_result as $row) {
                            $unit_code = $row['pmua_unit'];
                            $unit_desc = $row['pmpu_desc'];

                            $unit_floor = $row['pmpf_floor'];
                            $unit_area = $row['area'];
                            $unit_lease = $row['lease'];
                            // $unit_active 	= $row['pmpu_active'];
                            $unit_vacant = $row['pmua_status'];
                            $unit_seq = $row['pmpf_seq'];
                            $total_area += $unit_area;
                            $unit_area_display = formatting_num($unit_area);

                            $pmpr_c_pct = formatting_num($pmpr_c_pct);

                            if ($unit_vacant !== 'I') {
                                $unit_vacant = ($unit_vacant == 'V') ? 'vacant' : $unit_lease;

                                if ($line > 370 && $roffset == 0) {
                                    if ($headset == 0) {
                                        $roffset = 400;
                                        $line = -94;
                                        $pdf->setlinewidth(0.5);
                                        $pdf->setColorExt('fill', 'rgb', 0.9, 0.9, 0.9, 0);
                                        $pdf->rect(420, 407 - $line, 397, 13); // draw the rectangle
                                        $pdf->fill();
                                        $pdf->rect(420, 407 - $line, 397, 13); // draw the rectangle
                                        $pdf->stroke();
                                        $pdf->setColorExt('fill', 'rgb', 0, 0, 0, 0);
                                        $pdf->setFontExt($_fonts['Helvetica-Bold'], 10);
                                        $pdf->showBoxed("Units (CONT'D)", 580, 400 - $line, 100, 20, 'center', '');
                                        $pdf->setFontExt($_fonts['Helvetica-Bold'], 7);
                                        $pdf->showBoxed('UNIT', 20 + $roffset, 385 - $line, 600, 20, 'left', '');
                                        $pdf->showBoxed('DESCRIPTION', 60 + $roffset, 385 - $line, 600, 20, 'left', '');
                                        $pdf->showBoxed('FLOOR', 265 + $roffset, 385 - $line, 600, 20, 'left', '');
                                        $pdf->showBoxed('SEQ', 320 + $roffset, 385 - $line, 600, 20, 'left', '');
                                        $pdf->showBoxed('AREA', 360 + $roffset, 385 - $line, 600, 20, 'left', '');
                                        $line += 15;
                                        $pdf->setFontExt($_fonts['Helvetica'], 7);
                                        $headset = 1;
                                    }// end of - if ($headset == 0)

                                    $pdf->setFontExt($_fonts['Helvetica'], 7);
                                    $line += 10;
                                    $pdf->showBoxed($unit_code, 20 + $roffset, 400 - $line, 600, 20, 'left', '');
                                    $pdf->showBoxed($unit_desc, 60 + $roffset, 400 - $line, 600, 20, 'left', '');
                                    $pdf->showBoxed($unit_vacant, 210 + $roffset, 400 - $line, 600, 20, 'left', '');
                                    $pdf->showBoxed($unit_floor, 265 + $roffset, 400 - $line, 600, 20, 'left', '');
                                    $pdf->showBoxed($unit_seq, 320 + $roffset, 400 - $line, 600, 20, 'left', '');
                                    $pdf->showBoxed(
                                        $unit_area_display,
                                        320 + $roffset,
                                        400 - $line,
                                        60,
                                        20,
                                        'right',
                                        ''
                                    );
                                }// end of - if($line > 370 && $roffset == 0)

                                elseif ($line > 370 && $roffset == 400) {
                                    $roffset = 0;
                                    $pdf->setFontExt($_fonts['Helvetica'], 8);
                                    // $pdf->showBoxed("Printed on $date", 22, 5, 275, 30, "left", "");
                                    $pdf->showBoxed("Page {$page}", 540, 5, 275, 30, 'right', '');

                                    $pdf->end_page_ext('');

                                    $page++;
                                    $line = -60;
                                    $pdf->begin_page_ext(842, 595, '');
                                    $pdf->setFontExt($_fonts['Helvetica-Bold'], 12);
                                    $pdf->showBoxed("{$prop_name}", 22, 485 - $line, 820, 20, 'center', '');
                                    $pdf->setFontExt($_fonts['Helvetica-Bold'], 10);
                                    $pdf->showBoxed("Owner: {$owner}", 22, 470 - $line, 820, 20, 'center', '');
                                    // $pdf->showBoxed("GROSS / NET", 535, 470-$line, 280, 20, "right", "");
                                    $line += 5;
                                    $pdf->showBoxed(
                                        ucwords(strtolower($_SESSION['country_default']['property_manager'])) . ": {$pm}",
                                        535,
                                        455 - $line,
                                        280,
                                        20,
                                        'right',
                                        ''
                                    );
                                    $line -= 39;

                                    $pdf->setlinewidth(0.5);
                                    $pdf->setColorExt('fill', 'rgb', 0.9, 0.9, 0.9, 0);
                                    $pdf->rect(20, 407 - $line, 370, 13); // draw the rectangle
                                    $pdf->fill();
                                    $pdf->rect(20, 407 - $line, 370, 13); // draw the rectangle
                                    $pdf->stroke();
                                    $pdf->setColorExt('fill', 'rgb', 0, 0, 0, 0);
                                    $pdf->setFontExt($_fonts['Helvetica-Bold'], 10);
                                    $pdf->showBoxed("Units (CONT'D)", 160, 400 - $line, 100, 20, 'center', '');
                                    $pdf->setFontExt($_fonts['Helvetica-Bold'], 7);
                                    $line -= 1;
                                    $pdf->showBoxed('UNIT', 20, 385 - $line, 600, 20, 'left', '');
                                    $pdf->showBoxed('DESCRIPTION', 60, 385 - $line, 600, 20, 'left', '');
                                    $pdf->showBoxed('FLOOR', 265, 385 - $line, 600, 20, 'left', '');
                                    $pdf->showBoxed('SEQ', 320, 385 - $line, 600, 20, 'left', '');
                                    $pdf->showBoxed('AREA', 360, 385 - $line, 600, 20, 'left', '');

                                    $line += 25;
                                    $pdf->setFontExt($_fonts['Helvetica'], 7);
                                    $pdf->showBoxed($unit_code, 20, 400 - $line, 600, 20, 'left', '');
                                    $pdf->showBoxed($unit_desc, 60, 400 - $line, 600, 20, 'left', '');
                                    $pdf->showBoxed($unit_vacant, 210, 400 - $line, 600, 20, 'left', '');
                                    $pdf->showBoxed($unit_floor, 265, 400 - $line, 600, 20, 'left', '');
                                    $pdf->showBoxed($unit_seq, 320, 400 - $line, 600, 20, 'left', '');
                                    $pdf->showBoxed($unit_area_display, 320, 400 - $line, 60, 20, 'right', '');
                                }// end of elseif ($line > 370 && $roffset == 400)
                                else {
                                    $pdf->setFontExt($_fonts['Helvetica'], 7);
                                    $pdf->showBoxed($unit_code, 20 + $roffset, 400 - $line, 600, 20, 'left', '');
                                    $pdf->showBoxed($unit_desc, 60 + $roffset, 400 - $line, 600, 20, 'left', '');
                                    $pdf->showBoxed($unit_vacant, 210 + $roffset, 400 - $line, 600, 20, 'left', '');
                                    $pdf->showBoxed($unit_floor, 265 + $roffset, 400 - $line, 600, 20, 'left', '');
                                    $pdf->showBoxed($unit_seq, 320 + $roffset, 400 - $line, 600, 20, 'left', '');
                                    $pdf->showBoxed(
                                        $unit_area_display,
                                        320 + $roffset,
                                        400 - $line,
                                        60,
                                        20,
                                        'right',
                                        ''
                                    );
                                }

                                $subline += 10;
                                $line += 10;
                            }// end of - if($unit_vacant !== "I")
                        }// end of foreach($units_result as $row)

                        $pdf->setFontExt($_fonts['Helvetica-Bold'], 7);
                        $total_area_display = formatting_num($total_area);
                        $pdf->showBoxed(
                            "Total area: {$total_area_display}",
                            282 + $roffset,
                            400 - $line,
                            100,
                            20,
                            'right',
                            ''
                        );
                        $pdf->setFontExt($_fonts['Helvetica'], 7);
                    }// end of - if ($units_result)
                }

                if ($roffset == 400) {
                    $line += 50; // -55;
                } else {
                    $line = -55;
                }

                // MANAGEMENT FEES
                $pdf->setlinewidth(0.5);
                $pdf->setColorExt('fill', 'rgb', 0.9, 0.9, 0.9, 0);
                $pdf->rect(420, 446 - $line, 397, 13); // draw the rectangle
                $pdf->fill();
                $pdf->rect(420, 446 - $line, 397, 13); // draw the rectangle
                $pdf->stroke();
                $pdf->setColorExt('fill', 'rgb', 0, 0, 0, 0);
                $pdf->setFontExt($_fonts['Helvetica-Bold'], 10);
                $pdf->showBoxed('Fees', 580, 440 - $line, 100, 20, 'center', '');


                $csupSQL = 'SELECT pmco_name FROM pmco_company WHERE pmco_code = ?';
                $csup = $dbh->executeScalar($csupSQL, [$prop_c_supp]);
                // $csup = @$csup_result["pmco_name"];


                switch ($prop_c_type) {
                    case 1:
                        $prop_c_type_name = 'Nil';
                        break;
                    case 2:
                        $prop_c_type_name = 'Fixed';
                        break;
                    case 3:
                        $prop_c_type_name = "Percent Total: {$pmpr_c_pct}%";
                        break;
                    case 4:
                        $prop_c_type_name = 'Account Percent';
                        break;
                }

                $line -= 5;
                $pdf->setFontExt($_fonts['Helvetica-Bold'], 7);
                $pdf->showBoxed('MANAGEMENT FEES', 420, 420 - $line, 600, 20, 'left', '');
                $pdf->showBoxed('ACCOUNT', 600, 420 - $line, 600, 20, 'left', '');
                $pdf->showBoxed('TYPE', 650, 420 - $line, 600, 20, 'left', '');
                $pdf->showBoxed($_SESSION['country_default']['tax_label'], 745, 420 - $line, 600, 20, 'left', '');
                if ($prop_c_type == 2) {
                    $pdf->showBoxed(
                        'AMOUNT (' . $_SESSION['country_default']['currency_symbol'] . ')',
                        690,
                        420 - $line,
                        600,
                        20,
                        'left',
                        ''
                    );
                }

                $line += 10;
                $pdf->setFontExt($_fonts['Helvetica'], 7);
                $pdf->showBoxed("{$prop_c_acc}", 600, 420 - $line, 600, 20, 'left', '');
                $pdf->showBoxed("{$prop_c_type_name}", 650, 420 - $line, 600, 20, 'left', '');
                $pdf->showBoxed("{$prop_mgmt_gst}", 745, 420 - $line, 600, 20, 'left', '');

                if ($prop_c_type == 2) {
                    $prop_c_val_display = formatting_num($prop_c_val);
                    $pdf->showBoxed("{$prop_c_val_display}", 650, 420 - $line, 80, 20, 'right', '');
                }


                $line += 10;
                if ($prop_c_lim == '') {
                    $prop_c_lim = '0.00';
                }

                if ($prop_c_mmin == '') {
                    $prop_c_mmin = '0.00';
                }

                if ($prop_c_mmax == '') {
                    $prop_c_mmax = '0.00';
                }

                $line -= 10;

                if ($prop_c_type == 4) {
                    $manfeeSQL = 'SELECT pmrcf_acc, pmrcf_acc_to, CAST(pmrcf_pct as numeric(15,2)) as pmrcf_pct FROM pmrcf_p_fee_acc
                        JOIN prmf_fees ON pmrcf_prop = prmf_prop AND pmrcf_prmf_id = prmf_id
                        WHERE pmrcf_prop = ?
                        and CONVERT(datetime, ?, 103) BETWEEN prmf_start_period and prmf_end_period';
                    $manfee_result = $dbh->executeSet($manfeeSQL, false, true, [$prop_code, $asAtDate]);

                    if ($manfee_result) {
                        $pdf->setFontExt($_fonts['Helvetica-Bold'], 7);
                        $pdf->showBoxed('FROM ACC', 650, 410 - $line, 600, 20, 'left', '');
                        $pdf->showBoxed('TO ACC', 700, 410 - $line, 600, 20, 'left', '');
                        $pdf->showBoxed(' %', 750, 410 - $line, 600, 20, 'left', '');
                        $pdf->setFontExt($_fonts['Helvetica'], 7);
                        $line += 10;

                        foreach ($manfee_result as $row) {
                            $from_acc = $row['pmrcf_acc'];
                            $to_acc = $row['pmrcf_acc_to'];
                            $manfee_pct = $row['pmrcf_pct'];

                            $pdf->showBoxed("{$from_acc}", 650, 410 - $line, 600, 20, 'left', '');
                            $pdf->showBoxed("{$to_acc}", 700, 410 - $line, 600, 20, 'left', '');
                            $pdf->showBoxed("{$manfee_pct}", 750, 410 - $line, 600, 20, 'left', '');

                            $line += 10;
                        }
                    }

                    // end of - if ($manfee_result)
                    // endif;
                }// end of - if($prop_c_type == 4)

                // -- sundries

                $sql = 'SELECT * FROM pmps_p_sundry WHERE pmps_prop=?';
                $sundries = $dbh->executeSet($sql, false, true, [$prop_code]);
                $line += 10;

                $pdf->setFontExt($_fonts['Helvetica-Bold'], 7);
                $pdf->showBoxed('SUNDRY FEES', 420, 420 - $line, 600, 20, 'left', '');
                $pdf->showBoxed('ACCOUNT', 600, 420 - $line, 600, 20, 'left', '');
                $pdf->showBoxed('TYPE', 650, 420 - $line, 600, 20, 'left', '');
                $pdf->showBoxed($_SESSION['country_default']['tax_label'], 745, 420 - $line, 600, 20, 'left', '');
                $pdf->showBoxed(
                    'AMOUNT (' . $_SESSION['country_default']['currency_symbol'] . ')',
                    690,
                    420 - $line,
                    600,
                    20,
                    'left',
                    ''
                );

                $line += 10;
                $pdf->setFontExt($_fonts['Helvetica'], 7);
                foreach ($sundries as $row) {
                    $pdf->showBoxed($row['pmps_desc'], 420, 420 - $line, 600, 20, 'left', '');
                    $pdf->showBoxed('monthly', 650, 420 - $line, 600, 20, 'left', '');
                    $pdf->showBoxed($row['pmps_acc'], 600, 420 - $line, 600, 20, 'left', '');
                    $pdf->showBoxed($row['pmps_value'], 650, 420 - $line, 80, 20, 'right', '');
                    $pdf->showBoxed($row['pmps_gst_code'], 745, 420 - $line, 600, 20, 'left', '');
                    $line += 10;
                }// end of foreach($sundries as $row)


                $roffset = 400;
                // CALENDAR
                $line += 44;
                $pdf->setlinewidth(0.5);
                $pdf->setColorExt('fill', 'rgb', 0.9, 0.9, 0.9, 0);
                $pdf->rect(20 + $roffset, 446 - $line, 397, 13); // draw the rectangle
                $pdf->fill();
                $pdf->rect(20 + $roffset, 446 - $line, 397, 13); // draw the rectangle
                $pdf->stroke();
                $pdf->setColorExt('fill', 'rgb', 0, 0, 0, 0);
                $pdf->setFontExt($_fonts['Helvetica-Bold'], 10);
                $pdf->showBoxed('Property Calendar', 180 + $roffset, 440 - $line, 100, 20, 'center', '');
                $pdf->setFontExt($_fonts['Helvetica-Bold'], 7);
                $pdf->showBoxed('PERIOD', 20 + $roffset, 420 - $line, 600, 20, 'left', '');
                $pdf->showBoxed('START DATE', 65 + $roffset, 420 - $line, 600, 20, 'left', '');
                $pdf->showBoxed('END DATE', 143 + $roffset, 420 - $line, 600, 20, 'left', '');

                $line += 10;

                $yearSQL = 'SELECT TOP 1 pmcp_year
                                FROM pmcp_prop_cal
                                WHERE pmcp_closed = 1
                                AND pmcp_prop = ?
                                ORDER BY pmcp_end_dt DESC'; // echo $yearSQL."<<< YEAR SQL 1 <br />";
                $curperyear = $dbh->executeScalar($yearSQL, [$prop_code]); // Andrew to check

                $calendar_number = 0;
                if (isset($curperyear)) {
                    $calendarSQL = 'SELECT pmcp_period,CONVERT(char(10), pmcp_start_dt, 103) AS pmcp_start_dt,CONVERT(char(10), pmcp_end_dt, 103) AS pmcp_end_dt, pmcp_closed
                                FROM pmcp_prop_cal
                                WHERE PMCP_PROP =?
                                AND PMCP_YEAR = ?
                                ORDER BY PMCP_PERIOD';
                    $calendar_result = $dbh->executeSet($calendarSQL, false, true, [$prop_code, $curperyear]);
                    $calendar_number = count($calendar_result ?? []);
                }

                if ($calendar_result) {
                    foreach ($calendar_result as $row) {
                        $start_date = $row['pmcp_start_dt'];
                        $end_date = $row['pmcp_end_dt'];
                        $period = $row['pmcp_period'];
                        $period_closed = $row['pmcp_closed'];
                        $period_closed = ($period_closed == 1) ? 'closed' : '';

                        if ($line > 390) {
                            $pdf->setFontExt($_fonts['Helvetica'], 8);
                            $pdf->showBoxed("Page {$page}", 540, 5, 275, 30, 'right', '');
                            $pdf->end_page_ext('');
                            $page++;
                            $line = -60;
                            $roffset = 0;
                            $pdf->begin_page_ext(842, 595, '');
                            // $pdf->setFontExt ($_fonts["Helvetica-Bold"], 12);
                            $pdf->setlinewidth(0.5);
                            $pdf->setColorExt('fill', 'rgb', 0.9, 0.9, 0.9, 0);
                            $pdf->rect(20, 446 - $line, 397, 13); // draw the rectangle
                            $pdf->fill();
                            $pdf->rect(20, 446 - $line, 397, 13); // draw the rectangle
                            $pdf->stroke();
                            $pdf->setColorExt('fill', 'rgb', 0, 0, 0, 0);
                            $pdf->setFontExt($_fonts['Helvetica-Bold'], 10);
                            $pdf->showBoxed('Property Calendar', 180, 440 - $line, 100, 20, 'center', '');
                            $pdf->setFontExt($_fonts['Helvetica-Bold'], 7);
                            $pdf->showBoxed('PERIOD', 20, 420 - $line, 600, 20, 'left', '');
                            $pdf->showBoxed('START DATE', 65, 420 - $line, 600, 20, 'left', '');
                            $pdf->showBoxed('END DATE', 143, 420 - $line, 600, 20, 'left', '');
                            $line += 10;
                        }// end of - if($line > 390)

                        $pdf->setFontExt($_fonts['Helvetica'], 7);
                        $pdf->showBoxed("{$period}", 20 + $roffset, 420 - $line, 600, 20, 'left', '');
                        $pdf->showBoxed("{$start_date}", 45 + $roffset, 420 - $line, 60, 20, 'right', '');
                        $pdf->showBoxed("{$end_date}", 120 + $roffset, 420 - $line, 60, 20, 'right', '');
                        $pdf->showBoxed("{$period_closed}", 160 + $roffset, 420 - $line, 60, 20, 'right', '');
                        $line += 10;
                    }// end of - foreach($calendar_result as $row)
                }// end of - if ($calendar_result)


                // PROPERTY DIARY
                $line += 30;
                $pdf->setlinewidth(0.5);
                $pdf->setColorExt('fill', 'rgb', 0.9, 0.9, 0.9, 0);
                $pdf->rect(20 + $roffset, 446 - $line, 397, 13); // draw the rectangle
                $pdf->fill();
                $pdf->rect(20 + $roffset, 446 - $line, 397, 13); // draw the rectangle
                $pdf->stroke();
                $pdf->setColorExt('fill', 'rgb', 0, 0, 0, 0);
                $pdf->setFontExt($_fonts['Helvetica-Bold'], 10);
                $pdf->showBoxed('Property Diary', 180 + $roffset, 440 - $line, 100, 20, 'center', '');
                $pdf->setFontExt($_fonts['Helvetica-Bold'], 7);
                $pdf->showBoxed('DATE', 20 + $roffset, 420 - $line, 600, 20, 'left', '');
                $pdf->showBoxed('TYPE', 65 + $roffset, 420 - $line, 600, 20, 'left', '');
                $pdf->showBoxed('NARRATION', 200 + $roffset, 420 - $line, 600, 20, 'left', '');
                $line += 10;

                $diarySQL = "SELECT CONVERT(char(10),pmdy_date, 103) AS pmdy_date, pmdy_text, pmzz_desc FROM pmdy_diary, pmzz_param WHERE (pmdy_prop = ?) AND (pmdy_lease is NULL) AND pmzz_par_type = 'DIARYTYPE' AND pmzz_code = pmdy_par_code AND pmdy_comp_stamp is NULL";
                $diary_result = $dbh->executeSet($diarySQL, false, true, [$prop_code]);

                foreach ($diary_result as $row) {
                    $diary_date = $row['pmdy_date'];
                    $diary_type = $row['pmzz_desc'];
                    $diary_text = $row['pmdy_text'];
                    $test1 = strlen($diary_type);
                    $diary_type = trim($diary_type);
                    $test2 = strlen($diary_type);

                    $pdf->setFontExt($_fonts['Helvetica'], 6);
                    $pdf->showBoxed("{$diary_date}", -5 + $roffset, 420 - $line, 60, 20, 'right', '');
                    $pdf->showBoxed("{$diary_type}", 65 + $roffset, 420 - $line, 90, 20, 'left', '');
                    $pdf->showBoxed("{$diary_text}", 200 + $roffset, 420 - $line, 600, 20, 'left', '');
                    $line += 10;
                }// end of - foreach($diary_result as $row)

                // PROPERTY NOTES
                $line += 30;
                $pdf->setlinewidth(0.5);
                $pdf->setColorExt('fill', 'rgb', 0.9, 0.9, 0.9, 0);
                $pdf->rect(20 + $roffset, 446 - $line, 397, 13); // draw the rectangle
                $pdf->fill();
                $pdf->rect(20 + $roffset, 446 - $line, 397, 13); // draw the rectangle
                $pdf->stroke();
                $pdf->setColorExt('fill', 'rgb', 0, 0, 0, 0);
                $pdf->setFontExt($_fonts['Helvetica-Bold'], 10);
                $pdf->showBoxed('Property Notes', 180 + $roffset, 440 - $line, 100, 20, 'center', '');
                $pdf->setFontExt($_fonts['Helvetica-Bold'], 7);

                $pdf->setFontExt($_fonts['Helvetica'], 7);

                $character = "/\n/";

                $noteSQL = 'SELECT CONVERT(char(10),pmpn_stamp, 103) AS pmpn_stamp, convert(text,pmpn_note)as pmpn_note FROM pmpn_p_note WHERE (pmpn_prop = ?)';
                $note_result = $dbh->executeSet($noteSQL, false, true, [$prop_code]);

                foreach ($note_result as $row) {
                    $note_date = $row['pmpn_stamp'];
                    $note_text = $row['pmpn_note'];

                    $pdf->setFontExt($_fonts['Helvetica'], 7);
                    $pdf->showBoxed("{$note_date}", -5 + $roffset, 420 - $line, 60, 20, 'right', '');


                    $note_text = preg_replace($character, ' ', $note_text);
                    $notes_text = explode(' ', $note_text);

                    $notes_display = '';
                    foreach ($notes_text as $ntext) {
                        if ($notes_display === '') {
                            $notes_display .= "{$ntext}";
                        } elseif ((strlen($notes_display) + strlen($ntext) + 1) > 85) {
                            $pdf->showBoxed("{$notes_display}", 60 + $roffset, 420 - $line, 600, 20, 'left', '');
                            $line += 10;
                            $notes_display = "{$ntext}";
                        } else {
                            $notes_display .= " {$ntext}";
                        }
                    }

                    $pdf->showBoxed("{$notes_display}", 60 + $roffset, 420 - $line, 600, 20, 'left', '');

                    $line += 10;

                    // endwhile;
                    // endif;
                }// end of foreach($note_result as $row)


                // OWNERSHIP
                $headset = 0;
                $line += 30;
                $pdf->setlinewidth(0.5);
                $pdf->setColorExt('fill', 'rgb', 0.9, 0.9, 0.9, 0);
                $pdf->rect(20 + $roffset, 446 - $line, 397, 13); // draw the rectangle
                $pdf->fill();
                $pdf->rect(20 + $roffset, 446 - $line, 397, 13); // draw the rectangle
                $pdf->stroke();
                $pdf->setColorExt('fill', 'rgb', 0, 0, 0, 0);
                $pdf->setFontExt($_fonts['Helvetica-Bold'], 10);
                $pdf->showBoxed('Ownership', 180 + $roffset, 440 - $line, 100, 20, 'center', '');
                $pdf->setFontExt($_fonts['Helvetica-Bold'], 7);

                $pdf->setFontExt($_fonts['Helvetica'], 7);

                // SELECT ALL PROPERTY OWNERS
                $owners = '
                    SELECT
                        company_name,
                        pmos_owner,
                        pmos_pct
                    FROM
                        pmos_o_share
                    LEFT JOIN
                        crp_company ON (pmos_owner=company_code)
                    WHERE
                        pmos_prop=?';
                $owners_result = $dbh->executeSet($owners, false, true, [$prop_code]);

                foreach ($owners_result as $row) {
                    $owner_code = $row['pmos_owner'];
                    $pct = $row['pmos_pct'];
                    $total_ownership += $pct;
                    $owner_name = $row['company_name'];

                    $pct_display = formatting_num($pct) . '%';

                    if ($line > 370 && $roffset == 0) {
                        $roffset = 400;
                        $line = -50;
                        $pdf->setlinewidth(0.5);
                        $pdf->setColorExt('fill', 'rgb', 0.9, 0.9, 0.9, 0);
                        $pdf->rect(20 + $roffset, 446 - $line, 397, 13); // draw the rectangle
                        $pdf->fill();
                        $pdf->rect(20 + $roffset, 446 - $line, 397, 13); // draw the rectangle
                        $pdf->stroke();
                        $pdf->setColorExt('fill', 'rgb', 0, 0, 0, 0);
                        $pdf->setFontExt($_fonts['Helvetica-Bold'], 10);
                        $pdf->showBoxed("Ownership (CONT'D)", 180 + $roffset, 440 - $line, 140, 20, 'center', '');
                        $line += 10;
                        $pdf->setFontExt($_fonts['Helvetica'], 7);
                    } elseif ($line > 370 && $roffset == 400) {
                        $pdf->setFontExt($_fonts['Helvetica'], 8);
                        $pdf->showBoxed("Page {$page}", 540, 5, 275, 30, 'right', '');

                        $pdf->end_page_ext('');

                        $page++;
                        $line = -60;
                        $pdf->begin_page_ext(842, 595, '');
                        $pdf->setFontExt($_fonts['Helvetica-Bold'], 12);
                        $pdf->showBoxed("PROPERTY REPORT: {$prop_name}", 22, 485 - $line, 820, 20, 'center', '');
                        $pdf->setFontExt($_fonts['Helvetica-Bold'], 10);
                        $pdf->showBoxed("Owner: {$mainowner}", 22, 470 - $line, 820, 20, 'center', '');
                        $line += 5;
                        $pdf->showBoxed(
                            ucwords(strtolower($_SESSION['country_default']['property_manager'])) . ": {$pm}",
                            535,
                            455 - $line,
                            280,
                            20,
                            'right',
                            ''
                        );
                        $line -= 39;


                        $roffset = 0;
                        $line = -50;
                        $pdf->setlinewidth(0.5);
                        $pdf->setColorExt('fill', 'rgb', 0.9, 0.9, 0.9, 0);
                        $pdf->rect(20, $line - 446, 397, 13); // draw the rectangle
                        $pdf->fill();
                        $pdf->rect(20, $line - 446, 397, 13); // draw the rectangle
                        $pdf->stroke();
                        $pdf->setColorExt('fill', 'rgb', 0, 0, 0, 0);
                        $pdf->setFontExt($_fonts['Helvetica-Bold'], 10);
                        $pdf->showBoxed("Ownership (CONT'D)", 180, 440 - $line, 120, 20, 'center', '');
                        $pdf->setFontExt($_fonts['Helvetica'], 7);
                    }

                    $pdf->setFontExt($_fonts['Helvetica'], 7);
                    $pdf->showBoxed("{$owner_name} ({$owner_code})", 20 + $roffset, 420 - $line, 600, 20, 'left', '');
                    $pdf->showBoxed($pct_display, 155 + $roffset, 420 - $line, 100, 20, 'right', '');
                    $line += 10;

                    // endwhile;
                    // endif;
                }// end of foreach($owners_result as $row)


                // trying to put in a total for shares
                $pdf->setFontExt($_fonts['Helvetica-Bold'], 7);
                $total_ownership_display = formatting_num($total_ownership) . '%';
                $pdf->showBoxed(
                    "Total ownership: {$total_ownership_display}",
                    155 + $roffset,
                    420 - $line,
                    100,
                    20,
                    'right',
                    ''
                );
                $pdf->setFontExt($_fonts['Helvetica'], 7);


                // OWNER DETAILS
                $line += 30;
                $pdf->setlinewidth(0.5);
                $pdf->setColorExt('fill', 'rgb', 0.9, 0.9, 0.9, 0);
                $pdf->rect(20 + $roffset, 446 - $line, 397, 13); // draw the rectangle
                $pdf->fill();
                $pdf->rect(20 + $roffset, 446 - $line, 397, 13); // draw the rectangle
                $pdf->stroke();
                $pdf->setColorExt('fill', 'rgb', 0, 0, 0, 0);
                $pdf->setFontExt($_fonts['Helvetica-Bold'], 10);
                $pdf->showBoxed('Owner Details', 180 + $roffset, 440 - $line, 100, 20, 'center', '');
                $pdf->setFontExt($_fonts['Helvetica-Bold'], 7);
                $pdf->setFontExt($_fonts['Helvetica'], 7);

                $ownerdetailsSQL = 'SELECT pmos_owner, pmco_name, pmos_pct, pmco_street,
                    pmco_city, pmco_state, pmco_country, pmco_postcode, pmco_gst_no,
                    pmco_gst_code, pmco_dir_bank, pmco_b_bsb, pmco_b_acc_no, pmco_b_acc_name, pmco_b_name, pmco_email
                FROM pmos_o_share, pmco_company
                WHERE pmos_owner = pmco_code AND pmos_prop = ?';
                $ownerdetails_result = $dbh->executeSet($ownerdetailsSQL, false, true, [$prop_code]);

                foreach ($ownerdetails_result as $row) {
                    // Andrew to check dont know where to end this statement

                    $owner_code = $row['pmos_owner'];
                    $owner_name = $row['pmco_name'];
                    $owner_pct = $row['pmos_pct'];
                    $owner_street = $row['pmco_street'];
                    $owner_city = $row['pmco_city'];
                    $owner_state = $row['pmco_state'];
                    $owner_country = $row['pmco_country'];
                    $owner_postcode = $row['pmco_postcode'];
                    $owner_abn = $row['pmco_gst_no'];
                    $owner_gst_code = $row['pmco_gst_code'];
                    $owner_dir_bank = $row['pmco_dir_bank'];
                    $owner_bsb = formatWithDelimiter($row['pmco_b_bsb']);
                    $owner_acc_no = $row['pmco_b_acc_no'];
                    $owner_acc_name = $row['pmco_b_acc_name'];
                    $owner_bankname = $row['pmco_b_name'];
                    $owner_email = $row['pmco_email'];

                    if ($line > 370 && $roffset == 0) {
                        $pdf->setFontExt($_fonts['Helvetica'], 7);
                        $pdf->showBoxed("Page {$page}", 540, 5, 275, 30, 'right', '');
                        $pdf->end_page_ext('');
                        $page++;
                        $line = -60;
                        $pdf->begin_page_ext(842, 595, '');
                        $pdf->setlinewidth(0.5);
                        $pdf->setColorExt('fill', 'rgb', 0.9, 0.9, 0.9, 0);
                        $pdf->rect(20, $line - 446, 397, 13);
                        $pdf->fill();
                        $pdf->rect(20, $line - 446, 397, 13);
                        $pdf->stroke();
                        $pdf->setColorExt('fill', 'rgb', 0, 0, 0, 0);
                        $pdf->setFontExt($_fonts['Helvetica-Bold'], 10);
                        $pdf->showBoxed("Owner Details (CONT'D)", 180, $line - 440, 140, 20, 'center', '');
                        $line += 10;
                    } elseif ($line > 370 && $roffset == 400) {
                        $pdf->setFontExt($_fonts['Helvetica'], 8);
                        $pdf->showBoxed("Page {$page}", 540, 5, 275, 30, 'right', '');
                        $pdf->end_page_ext('');
                        $page++;
                        $line = -60;
                        $pdf->begin_page_ext(842, 595, '');
                        $pdf->setFontExt($_fonts['Helvetica-Bold'], 12);
                        $pdf->showBoxed("PROPERTY REPORT: {$prop_name}", 22, 485 - $line, 820, 20, 'center', '');
                        $pdf->setFontExt($_fonts['Helvetica-Bold'], 10);
                        $pdf->showBoxed("Owner: {$mainowner} ({$prop_owner})", 22, 470 - $line, 820, 20, 'center', '');
                        $line += 5;
                        $pdf->showBoxed(
                            ucwords(strtolower($_SESSION['country_default']['property_manager'])) . ": {$pm}",
                            535,
                            $line - 455,
                            280,
                            20,
                            'right',
                            ''
                        );
                        $line = -50;
                        $pdf->setlinewidth(0.5);
                        $pdf->setColorExt('fill', 'rgb', 0.9, 0.9, 0.9, 0);
                        $pdf->rect(20 + $roffset, 450 - $line, 397, 13); // draw the rectangle
                        $pdf->fill();
                        $pdf->rect(20 + $roffset, 450 - $line, 397, 13); // draw the rectangle
                        $pdf->stroke();
                        $pdf->setColorExt('fill', 'rgb', 0, 0, 0, 0);
                        $pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
                        $pdf->showBoxed("Owner Details (CONT'D)", 180 + $roffset, 440 - $line, 100, 20, 'center', '');
                    }

                    // # OWNER DETAILS Address and ABN GST ##

                    $prefix = ($owner_abn && $owner_abn != '') ? $_SESSION['country_default']['business_prefix'] : '';

                    $pdf->setFontExt($_fonts['Helvetica'], 7);
                    $pdf->showBoxed("{$owner_name} ({$owner_code})", 20 + $roffset, 420 - $line, 600, 20, 'left', '');
                    $pdf->showBoxed('Address:', 135 + $roffset, 427 - $line, 100, 20, 'right', '');
                    $pdf->showBoxed(
                        $_SESSION['country_default']['business_label'] . ': ' . $prefix . $owner_abn,
                        20 + $roffset,
                        410 - $line,
                        600,
                        20,
                        'left',
                        ''
                    );
                    $pdf->showBoxed(
                        $_SESSION['country_default']['tax_label'] . ": {$owner_gst_code}",
                        120 + $roffset,
                        410 - $line,
                        600,
                        20,
                        'left',
                        ''
                    );
                    $pdf->showBoxed("Email: {$owner_email}", 208 + $roffset, 410 - $line, 600, 20, 'left', '');

                    $owner_streets = explode($character, $owner_street);

                    foreach ($owner_streets as $street) {
                        $owner_name = trim($owner_name);
                        $street = trim($street);
                        $owner_city = trim($owner_city);
                        $owner_state = trim($owner_state);
                        $owner_postcode = trim($owner_postcode);

                        $pdf->showBoxed(
                            "{$street} {$owner_city} {$owner_state} {$owner_postcode}",
                            240 + $roffset,
                            427 - $line,
                            600,
                            20,
                            'left',
                            ''
                        );
                        $line += 10;
                    }

                    if ($owner_dir_bank == 1) {
                        $pdf->showBoxed(
                            "Bank Account No:   {$owner_acc_no}",
                            20 + $roffset,
                            410 - $line,
                            600,
                            20,
                            'left',
                            ''
                        );
                        if (getDisplayBsbFromSession()) {
                            $pdf->showBoxed(
                                getBsbLabelFromSession() . " :   {$owner_bsb}",
                                120 + $roffset,
                                410 - $line,
                                600,
                                20,
                                'left',
                                ''
                            );
                        }

                        $pdf->showBoxed("Acc Name: {$owner_acc_name}", 20 + $roffset, 400 - $line, 600, 20, 'left', '');
                        $pdf->showBoxed("Bank Name: {$owner_bankname}", 20 + $roffset, 390 - $line, 600, 20, 'left', '');
                        $line += 20;
                    }

                    $dbh->selectDatabase($clientDB);

                    $contactSQL = 'SELECT *
                        FROM pmct_c_contact
                        WHERE(pmct_company = ?)';
                    $contact_result = $dbh->executeSet($contactSQL, false, true, [$owner_code]);
                    // $contact_number = sizeof($contact_result);

                    $k = 0;
                    $subline = 0;

                    foreach ($contact_result as $row) {
                        $c_serial = $row['pmct_serial'];
                        $c_name = $row['pmct_name'];

                        // initialise role name
                        $c_role = '';
                        $role_name = "SELECT pmzz_desc
                            FROM  pmcw_c_role, pmzz_param
                            WHERE (pmzz_par_type = 'ROLETYPE')
                            AND (pmzz_code = pmcw_type) AND pmcw_company = ? AND pmcw_serial = ?";
                        $c_role = $dbh->executeScalar($role_name, [$owner_code, $c_serial]);

                        $line += 10;

                        $pdf->setFontExt($_fonts['Helvetica'], 6);
                        $pdf->showBoxed("{$c_role}:", 165 + $roffset, 420 - $line - $subline, 110, 20, 'right', '');
                        $pdf->showBoxed("{$c_name}", 280 + $roffset, 420 - $line - $subline, 600, 20, 'left', '');

                        // $pdf->setFontExt($_fonts["Helvetica"], 7);
                        $pdf->showBoxed("{$c_name}", 80 + $roffset, 420 - $line - $subline, 600, 20, 'right', '');
                        $phone = "SELECT * FROM pmcj_c_phone
                            WHERE
                            (pmcj_company = ?)
                            AND pmcj_c_serial = ?
                            AND (pmcj_ph_code <> '' )";
                        $phone_result = $dbh->executeSet($phone, false, true, [$owner_code, $c_serial]);
                        foreach ($phone_result as $row) {
                            /*$phone_number = sizeof($phone_result);
                            $j=0;
                            if ($phone_number == 0) ://andrew to check
                            elseif ($phone_number > 0) :
                            while ($j < $phone_number) :*/

                            $phone_code = $row['pmcj_ph_code'];
                            $phone_no = $row['pmcj_phone_no'];

                            $phone_code_name = '';
                            $phone_name = "SELECT pmzz_desc
                                FROM pmzz_param
                                WHERE (pmzz_par_type = 'PHONETYPE')
                                AND (pmzz_code = ?)";
                            $phone_code_name = $dbh->executeScalar($phone_name, [$phone_code]);
                            /*$phone_name_number = sizeof($phone_name_result);
                            while($thisRow = mssql_fetch_array($phone_name_result))
                            { $phone_code_name = $thisRow['pmzz_desc'];  $phone_code_name = trim($phone_code_name);}*/

                            $pdf->setFontExt($_fonts['Helvetica'], 7);
                            $pdf->showBoxed(
                                "{$phone_code_name}:",
                                155 + $roffset,
                                410 - $line - $subline,
                                100,
                                20,
                                'right',
                                ''
                            );
                            $pdf->setFontExt($_fonts['Helvetica'], 7);
                            $pdf->showBoxed("{$phone_no}", 260 + $roffset, 410 - $line - $subline, 600, 20, 'left', '');
                            $subline += 10;
                            // $j++;
                            // endwhile;
                        }// end of - foreach($phone_result as $row)

                        // endif;
                        $line += 15;
                        $k++;
                        // endwhile;
                    }// end of foreach($contact_result as $row) {

                    // endif;

                    $line += 20;
                }

                // }//end of - if($prop_c_type == 4)v

                $pdf->setFontExt($_fonts['Helvetica'], 8);
                // $pdf->showBoxed("Printed on $date", 22, 30, 275, 10, "left", "");
                $pdf->showBoxed(
                    "Created and authorised by: ___________________ Date: _______________   Page {$page}",
                    420,
                    30,
                    375,
                    10,
                    'right',
                    ''
                );

                $traccFooter = new TraccFooter(
                    'assets/clientLogos/tracc_logo_footer.jpg',
                    'Property_Report',
                    A4_LANDSCAPE
                );
                $traccFooter->prerender($pdf);

                $pdf->end_page_ext('');
            }   // end of - foreach ($properties as $prop_code)

            $pdf->end_document('');
            $pdf->delete();
            break;
    }

    $view->items['downloadPath'] = $downloadPath;
    $view->render();
}

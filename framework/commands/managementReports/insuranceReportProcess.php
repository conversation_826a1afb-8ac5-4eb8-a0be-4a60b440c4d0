<?php

use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Fill;

include_once 'lib/ReportLib/commonReportFunctions.php';
function insuranceReportProcess(&$context)
{
    global $pathPrefix, $clientDirectory;

    if (! isPostback()) {
        $view = new MasterPage(userViews(), '/managementReports/insuranceReportProcess.html');
        $view->setSection($context['module']);
    } else {
        $view = new UserControl(userViews(), '/managementReports/insuranceReportProcess.html');
    }

    if ($context[IS_TASK]) {
        $view->bindAttributesFrom($context);
        extract($context);
    } else {

        $view->bindAttributesFrom($_REQUEST);
        $insuranceData = dbGenerateInsurance($view->items['propertyManager'], $view->items['reportType'], $view->items['insuranceOption'], $view->items['insuranceDate'], $view->items['policyType']);
        if ($insuranceData) {
            $insuranceCount = count($insuranceData ?? []);
        }
        $view->items['insuranceCount'] = $insuranceCount;
        $view->items['command'] = 'insuranceReportProcess';
        $view->items['module'] = 'managementReports';
        $queue = new Queue(TASKTYPE_INSURANCE_REPORT);
        if ($insuranceCount > THRESHOLD_INSURANCEREPORT) {
            $queue->add($_SESSION['clientID'], $_SESSION['un'], 'command=insuranceReportProcess&module=managementReports', $view->items);
        }
    }

    // ## GENERATE PDF REPORT
    if (($context[IS_TASK]) || ($insuranceCount <= THRESHOLD_INSURANCEREPORT)) {
        $format = $view->items['format'];

        // # HANDLE PROCESSING LOGIC ##
        $logoFile = dbGetClientLogo();
        $logoPath = "/assets/clientLogos/{$logoFile}";

        $_filePath = "{$pathPrefix}{$clientDirectory}/{$format}/" . DOC_INSURANCE . '/';
        $_downloadPath = "{$clientDirectory}/{$format}/" . DOC_INSURANCE;
        if ($view->items['reportType'] == 'P') {
            $_filename = 'PropertyInsuranceReport';
            $_header_title = 'Property Insurance Report';
        } else {
            $_filename = 'TenantInsuranceReport';
            $_header_title = 'Tenant Insurance Report';
        }

        $logoFile = dbGetClientLogo();
        $logoPath = "assets/clientLogos/$logoFile";

        // Style
        $numberFormat = '_(* #,##0.00_);_(* (#,##0.00);_(* "-"??_);_(@_)';
        $numberFormatStyle =
        [
            'alignment' =>  ['horizontal' => 'right'],
            'numberformat' =>  ['code' => $numberFormat],
        ];

        $file = $_filename . date('YmdHis') . ".{$format}";
        $filePath = $_filePath . $file;
        $downloadPath = "{$_downloadPath}/{$file}";

        if ($format == FILETYPE_PDF) {
            //            $report = new PDFDataMultiLineReport ($filePath, $logoPath, A4_LANDSCAPE);
            $report = ($context[DOC_MASTER]) ? new PDFDataReport($context[DOC_MASTER], $logoPath, A4_LANDSCAPE) : new PDFDataReport($filePath, $logoPath, A4_LANDSCAPE);
            $report->multiLine = true;
        } else {
            $report = new XLSDataReport($filePath, $_header_title);
            $report->enableFormatting = true;
        }

        $subtitle = 'as of ' . $view->items['insuranceDate'];

        $header = new ReportHeader($_header_title, $subtitle, '');
        $header->xPos = $report->hMargin;
        $header->yPos = $report->pageHeight - $report->vMargin;
        if ($format == FILETYPE_PDF) {
            $report->attachObject('header', $header);
            $footer = new TraccFooter(null, $_header_title, $report->pageSize);
            $report->attachObject('footer', $footer);
        }

        if ($format != FILETYPE_SCREEN) {
            $subHeaderstyle = [
                'fill' => [
                    'type' => Fill::FILL_SOLID,
                    'color' => ['rgb' => '004c7a'],
                ],
                'font' =>  ['bold' => true, 'color' =>  ['rgb' => 'ffffff']],
                'alignment' =>  ['vertical' => Alignment::VERTICAL_CENTER,  'horizontal' => Alignment::HORIZONTAL_LEFT],
            ];

            $report->resetColumns();

            if ($view->items['reportType'] == 'P') {
                $report->addColumn('property_code', 'Property Code', 70, 'left');
                $report->addColumn('property_name', 'Property Name', 220, 'left');
            } else {
                $report->addColumn('property_code', 'Property Code', 50, 'left');
                $report->addColumn('lease_code', 'Lease Code', 50, 'left');
                $report->addColumn('lease_name', 'Lease Name', 190, 'left');
            }

            $report->addColumn('insurance_type', 'Type', 100, 'left');
            $report->addColumn('insurer', 'Insurer', 100, 'left');
            if ($view->items['reportType'] == 'P') {
                $report->addColumn('premium_amount', 'Premium Amount', 60, 'right', '#,##0.00_);(#,##0.00)');
            } else {
                $report->addColumn('indemnity_limit', 'Indemnity Limit', 60, 'right', '#,##0.00_);(#,##0.00)');
            }
            $report->addColumn('policy_no', 'Policy No.', 70, 'left');
            $report->addColumn('start_date', 'Start Date', 50, 'center');
            $report->addColumn('exp_date', 'Exp. Date', 50, 'center');
            $report->addColumn('paid_date', 'Paid Date', 50, 'center');
            if ($format == FILETYPE_XLS) {
                $report->addColumn('note', 'Note', 200, 'left');
            }
            if ($format == FILETYPE_PDF) {
                $report->addSubHeaderItem('title', 0, 200, 'left', null, $subHeaderstyle);
            }
            $report->preparePage();
            $report->renderHeader();
            foreach ($insuranceData as $insuranceKey => $insuranceValue) {
                if ($insuranceValue) {
                    $insuranceValue['premium_amount'] = $insuranceValue['premium_amount'] ? toMoney($insuranceValue['premium_amount'], '') : '';
                    $insuranceValue['indemnity_limit'] = $insuranceValue['indemnity_limit'] ? toMoney($insuranceValue['indemnity_limit'], '') : '';

                    $report->renderLine($insuranceValue);
                }
            }
            $report->clean();
            $report->endPage();
        }
        $report->close();
    }

    if ($insuranceCount > THRESHOLD_INSURANCEREPORT) {
        $view->items['statusMessage'] = 'Due to its complexity, this report has been scheduled and will be emailed to you on completion!';
        $view->render();
    } else {
        $view->items['downloadPath'] = $downloadPath;
        $view->items['insuranceData'] = $insuranceData;
        $view->render();
    }
}

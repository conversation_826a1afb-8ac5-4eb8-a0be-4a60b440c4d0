<?php

function bankGuaranteeReport(&$context)
{
    global $clientDirectory, $pathPrefix;

    $userpermission = new userpermission();
    $result = $userpermission->userHasPageAccess(__FUNCTION__);

    if (! isPostback()) {
        $view = new MasterPage(userViews(), '/managementReports/bankGuaranteeReport.html');
        $view->setSection($context['module']);
    } else {
        $view = new UserControl(userViews(), '/managementReports/bankGuaranteeReport.html');
    }

    $view->bindAttributesFrom($_REQUEST);

    // Default Values
    if (empty($view->items['bankGuaranteeDate'])) {
        $view->items['bankGuaranteeDate'] =  date('d/m/Y');
    }

    $view->items['yesNoOption'] =
    [
        'Yes' => 'Yes',
        'No' => 'No',
    ];

    $view->items['formatList'] =
    [
        FILETYPE_PDF => 'PDF',
        FILETYPE_XLS => 'Excel Spreadsheet',
        FILETYPE_SCREEN => 'Print to Screen',
    ];

    $view->items['bankGuaranteeOptionList'] = ['' => 'All', 'C' => 'Current', 'E' => 'Expired'];

    if (! isset($view->items['format'])) {
        $view->items['format'] = FILETYPE_SCREEN;
    }
    if (! isset($view->items['reportType'])) {
        $view->items['reportType'] = 'T';
    }
    if (! isset($view->items['bankGuaranteeOption'])) {
        $view->items['bankGuaranteeOption'] = '';
    }
    if (! isset($view->items['currentTenantOnly'])) {
        $view->items['currentTenantOnly'] = 'Yes';
    }

    if (! isset($view->items['propertyManager'])) {
        $view->items['propertyManager'] = 'ALL';
    }
    $view->items['propertyManagerList'] =  [0 =>  ['propertyManagerID' => 'ALL', 'propertyManagerName' => 'All', 'number_of_props' => 23]];
    $view->items['propertyManagerList'] = array_merge($view->items['propertyManagerList'], dbGetPropertyManagers());
    //    if($view->items['propertyManager'] == 'ALL'){
    //        $view->items['propertyList'] = dbPropertyGroupList ('', 'Property Manager', true, 1);
    //    }else{
    //        $view->items['propertyList'] = dbPropertyGroupList ($view->items['propertyManager'], 'Property Manager', false, 1);
    //    }

    if ($view->items['propertyManager']) {
        $view->items['propertyManager'] = $view->items['propertyManager'];
    }


    switch ($view->items['action']) {
        case 'generate':
            if ($view->items['format'] == FILETYPE_SCREEN) {
                $view->render();
            }
            $context = $view->items;
            executeCommand('bankGuaranteeReportProcess', 'managementReports');
            break;
    }


    if ($view->items['format'] != FILETYPE_SCREEN || empty($view->items['action'])  || $validationErrors) {
        // Post Feed
        $view->items['validationErrors'] = $validationErrors;


        // Display
        $view->render();
    }

}

<?php

if (! function_exists('invoice_activity_header')) {
    function invoice_activity_header($cont, $propertyID)
    {

        global $pdf;
        global $propertyName;
        global $line;
        global $description;
        global $periodDescription;
        global $client;
        global $page;
        global $periodFrom;
        global $periodTo;
        global $date;
        global $logo;
        // Landscape Paper
        global $xCoord_zern_landscape;
        global $yCoord_zern_landscape;
        global $scale_zern_landscape;
        global  $_fonts;

        if ($cont == 'cont') {
            $page_name = 'BAS Supporting Documentation (...)';
        } else {
            $page_name = 'BAS Supporting Documentation';
        }
        $page++;

        $pdf->setFontExt($_fonts['Helvetica-Bold'], 9);


        $pdf->showBoxed("$page_name", 271, 540, 300, 30, 'center', '');

        $pdf->setFontExt($_fonts['Helvetica'], 8);
        $pdf->show_xy('Owner:', 22, 540);
        $pdf->continue_text('Property:');
        $pdf->continue_text('Report For:');
        $pdf->show_xy($client, 70, 540);
        $pdf->continue_text($propertyName . " [$propertyID]");
        $pdf->continue_text($periodDescription);


        $pdf->setlinewidth(0.5);
        $pdf->moveto(18, 515);
        $pdf->lineto(830, 515);
        $pdf->stroke();

        $marginVertical = 5;
        $pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
        $pdf->showBoxed('Date', 22, 485 - $marginVertical, 40, 30, 'left', '');
        $pdf->showBoxed('Lease Code', 62, 475 - $marginVertical, 50, 40, 'left', '');
        $pdf->showBoxed('Lease Name', 112, 475 - $marginVertical, 70, 40, 'left', '');
        $pdf->showBoxed('Invoice #', 182, 475 - $marginVertical, 50, 40, 'left', '');
        //    $pdf->showBoxed ('PO #', 232, 475, 40, 40, "left", "");
        $pdf->showBoxed('Due Date', 234, 475 - $marginVertical, 60, 40, 'left', '');
        $pdf->showBoxed('From Date', 284, 475 - $marginVertical, 60, 40, 'left', '');
        $pdf->showBoxed('To Date', 344, 475 - $marginVertical, 60, 40, 'left', '');
        $pdf->showBoxed('Account Code', 404, 475 - $marginVertical, 60, 40, 'left', '');
        $pdf->showBoxed('Description', 464, 475 - $marginVertical, 110, 40, 'left', '');
        $pdf->showBoxed('Type', 574, 475 - $marginVertical, 30, 40, 'left', '');
        $pdf->showBoxed('Period', 604, 475 - $marginVertical, 30, 40, 'left', '');
        $pdf->showBoxed('Year', 634, 475 - $marginVertical, 18, 40, 'left', '');
        $pdf->showBoxed('Net', 652, 475 - $marginVertical, 60, 40, 'right', '');
        $pdf->showBoxed($_SESSION['country_default']['tax_label'], 712, 475 - $marginVertical, 55, 40, 'right', '');
        $pdf->showBoxed('Gross', 767, 475 - $marginVertical, 60, 40, 'right', '');

        $pdf->setFontExt($_fonts['Helvetica'], 8);
        $pdf->showBoxed("Period From: $periodFrom To: $periodTo", 271, 529, 300, 30, 'center', '');

        if ($logo) {
            generateLogo('landscape');
        }

        $pdf->setlinewidth(0.5);
        $pdf->moveto(18, 495);
        $pdf->lineto(830, 495);
        $pdf->stroke();


        $pdf->setFontExt($_fonts['Helvetica'], 8);
        $pdf->showBoxed("Page {$page}", 750, 0, 75, 30, 'right', '');

        $line = 490;
    }
}

$pdf->begin_page_ext(842, 595, '');
invoice_activity_header('new', $propertyID);
$line = 490;


$period = dbGetPeriodYear($propertyID, $view->items['periodFrom'], $view->items['periodTo']);
$yearPeriod = [];
foreach ($period as $data) {
    $id = $data['year'];
    $yearPeriod[$id][] = $data['period'];
}


$invoice = dbGetInvoiceActivity($propertyID, $yearPeriod);
$totals = [];
$lastAccount = null;
$acctTotalGross = $acctTotalNet = $acctTotalGST = 0;
$lastTransactionType = null;
$transTotalGross = $transTotalNet = $transTotalGST = 0;
$propGrossIncome = $propNetIncome = $propGSTIncome = 0;
$propGrossExpense = $propIncomeExpense = $propGSTExpense = 0;
$data = [];
foreach ($invoice as $key => $row) {
    if ($key == 0 && $row['transactionType'] == 'ar') {
        $typeIncome['transactionDate'] = 'Operating Income';
        $typeIncome['bold'] = true;
        $typeIncome['width']['transactionDate'] = 100;
        $typeIncome['bgcolor'] = [0.8, 0.85, 1];
        $typeIncome['header'] = 'income';
        $data[$propertyID][] = $typeIncome;
    } elseif ($key == 0 && $row['transactionType'] == 'ap') {
        $varType['transactionDate'] = 'Operating Expense';
        $varType['bold'] = true;
        $varType['header'] = 'expense';
        $varType['width']['transactionDate'] = 100;
        $varType['bgcolor'] = [0.8, 0.85, 1];
        $data[$row['propertyID']][] = $varType;
    } elseif ($key == 0 && $row['transactionType'] == 'arbs') {
        $varType['transactionDate'] = 'Non-Operating Income';
        $varType['bold'] = true;
        $varType['header'] = 'incomeBS';
        $varType['width']['transactionDate'] = 100;
        $varType['bgcolor'] = [0.8, 0.85, 1];
        $data[$row['propertyID']][] = $varType;
    } elseif ($key == 0 && $row['transactionType'] == 'apbs') {
        $varType['transactionDate'] = 'Non-Operating Expense';
        $varType['bold'] = true;
        $varType['header'] = 'expenseBS';
        $varType['width']['transactionDate'] = 100;
        $varType['bgcolor'] = [0.8, 0.85, 1];
        $data[$row['propertyID']][] = $varType;
    }


    if (! in_array($row['propertyID'], $prop)) {
        array_push($prop, $row['propertyID']);
    }

    if ($lastAccount !== null and $lastAccount != $row['accountID']) {
        // add total
        $acctTotal['invoiceNumber'] = "Account Total for $lastAccount " . dbGetAccountName($lastAccount);
        if ($format == FILETYPE_SCREEN) {
            $acct['transactionDate'] = '&nbsp;';
        } else {
            $acct['transactionDate'] = '';
        }

        $acctTotal['gross'] = ($acctTotalGross);
        $acctTotal['net'] = ($acctTotalNet);
        $acctTotal['gst'] = ($acctTotalGST);

        $acctTotal['bold'] = false;
        $acctTotal['width']['invoiceNumber'] = 300;
        $acctTotal['bgcolor'] = [0.9, 0.9, 0.9];

        $data[$row['propertyID']][] = $acctTotal;
        if ($lastTransactionType !== null and $lastTransactionType != $row['transactionType']) {
        } else {
            $data[$row['propertyID']][] = $acct;
        }
        $acctTotalGross = $acctTotalNet = $acctTotalGST = 0;

    }

    if ($lastTransactionType !== null and $lastTransactionType != $row['transactionType']) {
        switch ($lastTransactionType) {
            case 'ar':
                $transType['invoiceNumber'] = 'Operating Income Total';
                break;
            case 'ap':
                $transType['invoiceNumber'] = 'Operating Expenses Total';
                break;
            case 'arbs':
                $transType['invoiceNumber'] = 'Non-Operating Income Total';
                break;
            default:
                $transType['invoiceNumber'] = 'Non-Operating Expenses Total';
                break;

        }

        if ($format == FILETYPE_SCREEN) {
            $expense['transactionDate'] = '&nbsp;';
        } else {
            $expense['transactionDate'] = '';
        }

        $transType['gross'] = ($transTotalGross);
        $transType['net'] = ($transTotalNet);
        $transType['gst'] = ($transTotalGST);

        $transType['bold'] = true;
        $transType['width']['invoiceNumber'] = 200;
        $transType['bgcolor'] = [220 / 255, 242 / 255, 255 / 255];
        $transType['transacTotal'] = 'transacTotal';


        $data[$row['propertyID']][] = $transType;
        $data[$row['propertyID']][] = $expense;

        if ($row['transactionType'] == 'ap') {
            $varType['transactionDate'] = 'Operating Expense';
            $varType['bold'] = true;
            $varType['header'] = 'expense';
            $varType['width']['transactionDate'] = 100;
            $varType['bgcolor'] = [0.8, 0.85, 1];
            $data[$row['propertyID']][] = $varType;
        } elseif ($row['transactionType'] == 'arbs') {
            $varType['transactionDate'] = 'Non-Operating Income';
            $varType['bold'] = true;
            $varType['header'] = 'incomeBS';
            $varType['width']['transactionDate'] = 100;
            $varType['bgcolor'] = [0.8, 0.85, 1];
            $data[$row['propertyID']][] = $varType;
        } elseif ($row['transactionType'] == 'apbs') {
            $varType['transactionDate'] = 'Non-Operating Expense';
            $varType['bold'] = true;
            $varType['header'] = 'expenseBS';
            $varType['width']['transactionDate'] = 100;
            $varType['bgcolor'] = [0.8, 0.85, 1];
            $data[$row['propertyID']][] = $varType;
        }

        if (substr($lastTransactionType, 0, 2) == 'ar') {
            $propGrossIncome += $transTotalGross;
            $propNetIncome += $transTotalNet;
            $propGSTIncome += $transTotalGST;

        } else {
            $propGrossIncome -= $transTotalGross;
            $propNetIncome -= $transTotalNet;
            $propGSTIncome -= $transTotalGST;
        }

        $transTotalGross = $transTotalNet = $transTotalGST = 0;
    }

    if ($row['purchaseNumber'] == 0) {
        $row['purchaseNumber'] = '';
    } else {
        $row['purchaseNumber'] =  $row['purchaseNumber'];
    }
    $row['gross'] = ($row['grossAmount']);
    $row['net'] = ($row['netAmount']);
    $row['gst'] = ($row['gstAmount']);

    $acctTotalGross += $row['grossAmount'];
    $acctTotalNet += $row['netAmount'];
    $acctTotalGST += $row['gstAmount'];

    $transTotalGross += $row['grossAmount'];
    $transTotalNet += $row['netAmount'];
    $transTotalGST += $row['gstAmount'];

    $data[$row['propertyID']][] = $row;
    $lastAccount = $row['accountID'];
    $lastTransactionType = $row['transactionType'];

    $count = count($invoice ?? []);

}

if ($lastAccount !== null) {
    $acctTotal['invoiceNumber'] = "Account Total for $lastAccount " . dbGetAccountName($lastAccount);
    //  $acct['transactionDate'] = '';
    $acctTotal['gross'] = ($acctTotalGross);
    $acctTotal['net'] = ($acctTotalNet);
    $acctTotal['gst'] = ($acctTotalGST);

    $acctTotal['bold'] = false;
    $acctTotal['width']['invoiceNumber'] = 300;
    $acctTotal['bgcolor'] = [0.9, 0.9, 0.9];

    $data[$propertyID][] = $acctTotal;
    $acctTotalGross = $acctTotalNet = $acctTotalGST = 0;
}

if ($lastTransactionType !== null) {
    switch ($lastTransactionType) {
        case 'ar':
            $transType['invoiceNumber'] = 'Operating Income Total';
            break;
        case 'ap':
            $transType['invoiceNumber'] = 'Operating Expenses Total';
            break;
        case 'arbs':
            $transType['invoiceNumber'] = 'Non-Operating Income Total';
            break;
        default:
            $transType['invoiceNumber'] = 'Non-Operating Expenses Total';
            break;

    }
    $transType['gross'] = ($transTotalGross);
    $transType['net'] = ($transTotalNet);
    $transType['gst'] = ($transTotalGST);

    $transType['bold'] = true;
    $transType['width']['invoiceNumber'] = 200;
    $transType['bgcolor'] = [220 / 255, 242 / 255, 255 / 255];
    $transType['transacTotal'] = 'transacTotal';
    $data[$row['propertyID']][] = $transType;
    $propGrossExpense = $transTotalGross;
    $propNetExpense = $transTotalNet;
    $propGSTExpense = $transTotalGST;
    $transTotalGross = $transTotalNet = $transTotalGST = 0;
}

$totals['invoiceNumber'] = 'Property Total';
$totals['bold'] = false;
$totals['width']['invoiceNumber'] = 300;
$totals['bgcolor'] = [0.9, 0.9, 0.9];
$totals['gross'] = ($propGrossIncome - $propGrossExpense);
$totals['net'] = ($propNetIncome - $propNetExpense);
$totals['gst'] = ($propGSTIncome - $propGSTExpense);
$data[$propertyID][] = $totals;
unset($totals);


foreach ($data[$propertyID] as $row) {
    $line -= 10;
    if ($line <= 70) {
        $pdf->setlinewidth(0.5);

        $pdf->moveto(18, $line);
        $pdf->lineto(830, $line);
        $pdf->stroke();

        // first line
        $pdf->moveto(18, $line);
        $pdf->lineto(18, 515);
        $pdf->stroke();

        // LAST LINE
        $pdf->moveto(830, 515);
        $pdf->lineto(830, $line);
        $pdf->stroke();



        $traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'BASSupport', A4_LANDSCAPE);
        $traccFooter->prerender($pdf);


        $pdf->end_page_ext('');
        $pdf->begin_page_ext(842, 595, '');
        invoice_activity_header('cont', $propertyID);

        $line = 480;
    }

    if (strpos($row['invoiceNumber'], 'Total') !== false) {

        if (strpos($row['invoiceNumber'], 'Account') !== false) {
            $pdf->setFontExt($_fonts['Helvetica'], 7);
        } else {
            $pdf->setFontExt($_fonts['Helvetica-Bold'], 7);
        }
        $line -= 5;
        $pdf->save();

        $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        $pdf->setlinewidth(0.5);
        $pdf->moveto(18, $line);
        $pdf->lineto(830, $line);
        $pdf->stroke();

        $pdf->setlinewidth(0.5);
        $pdf->moveto(18, $line + 12);
        $pdf->lineto(830, $line + 12);
        $pdf->stroke();

        $pdf->setColorExt('fill', 'rgb', 0.88, 0.88, 0.88, 0);
        $pdf->rect(18, $line, 812, 12);
        $pdf->fill();



        $pdf->restore();

        $pdf->showBoxed($row['invoiceNumber'], 182, $line, 200, 10, 'left', '');
        $pdf->showBoxed(toMoney($row['net'], ''), 652, $line, 60, 10, 'right', '');
        $pdf->showBoxed(toMoney($row['gst'], ''), 712, $line, 55, 10, 'right', '');
        $pdf->showBoxed(toMoney($row['gross'], ''), 767, $line, 60, 10, 'right', '');

    } elseif (strpos($row['transactionDate'], 'Operating') !== false) {

        $pdf->setFontExt($_fonts['Helvetica-Bold'], 7);

        $pdf->save();

        $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        $pdf->setlinewidth(0.5);
        $pdf->moveto(18, $line);
        $pdf->lineto(830, $line);
        $pdf->stroke();

        $pdf->setlinewidth(0.5);
        $pdf->moveto(18, $line + 12);
        $pdf->lineto(830, $line + 12);
        $pdf->stroke();

        $pdf->setColorExt('fill', 'rgb', 0.88, 0.88, 0.88, 0);
        $pdf->rect(18, $line, 812, 12);
        $pdf->fill();



        $pdf->restore();

        $pdf->showBoxed($row['transactionDate'], 22, $line, 200, 10, 'left', '');
        $line -= 5;
    } else {
        if ($row['net'] == 0 && $row['gst'] == 0 && $row['gross'] == 0) {
            continue;
        }
        $pdf->setFontExt($_fonts['Helvetica'], 7);
        $pdf->showBoxed($row['transactionDate'], 22, $line, 40, 10, 'left', '');
        $pdf->showBoxed($row['leaseID'], 62, $line, 50, 10, 'left', '');
        $pdf->showBoxed($row['leaseName'], 112, $line, 70, 10, 'left', '');
        $pdf->showBoxed($row['invoiceNumber'], 182, $line, 50, 10, 'left', '');
        // $pdf->showBoxed ($row['purchaseNumber'], 232, $line, 40, 10, "left", "");
        $pdf->showBoxed($row['dueDate'], 234, $line, 60, 10, 'left', '');
        $pdf->showBoxed($row['fromDate'], 284, $line, 60, 10, 'left', '');
        $pdf->showBoxed($row['toDate'], 344, $line, 60, 10, 'left', '');
        $pdf->showBoxed($row['accountID'], 404, $line, 60, 10, 'left', '');
        $pdf->showBoxed($row['description'], 464, $line, 110, 10, 'left', '');
        $pdf->showBoxed($row['transType'], 574, $line, 30, 10, 'left', '');
        $pdf->showBoxed($row['period'], 604, $line, 30, 10, 'left', '');
        $pdf->showBoxed($row['year'], 634, $line, 18, 10, 'left', '');
        $pdf->showBoxed(toMoney($row['net'], ''), 652, $line, 60, 10, 'right', '');
        $pdf->showBoxed(toMoney($row['gst'], ''), 712, $line, 55, 10, 'right', '');
        $pdf->showBoxed(toMoney($row['gross'], ''), 767, $line, 60, 10, 'right', '');
    }
}

$pdf->setlinewidth(0.5);

$pdf->moveto(18, $line);
$pdf->lineto(830, $line);
$pdf->stroke();

// first line
$pdf->moveto(18, $line);
$pdf->lineto(18, 515);
$pdf->stroke();

// LAST LINE
$pdf->moveto(830, 515);
$pdf->lineto(830, $line);
$pdf->stroke();

$traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'BASSupport', A4_LANDSCAPE);
$traccFooter->prerender($pdf);

$pdf->end_page_ext('');

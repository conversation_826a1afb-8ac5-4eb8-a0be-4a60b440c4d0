<?php

function withAccountCategoriesComputations($dataArray, $type)
{
    $actualTotal = 0;
    $YTDTotal = 0;
    $PYTotal = 0;
    $budgetTotal = 0;
    $budgetYTDTotal = 0;
    $PYYTDTotal = 0;
    $PYYearTotal = 0;
    $actualPYTotal = 0;
    $varianceTotal = 0;
    $varianceAccountCategories = 0;
    $varianceYTDTotal = 0;
    $varianceYTDAccountCategories = 0;
    $variancePYYTDTotal = 0;
    $variancePYYTDAccountCategories = 0;
    $variancePYTotal = 0;
    $variancePYAccountCategories = 0;
    $actualTotal_sub = [];
    $actualTotalPrior_sub = [];
    $actualTotalY_sub = [];
    $actualTotalPriorY_sub = [];
    foreach ($dataArray as $y => $i) {
        $budgetAccountCategories = 0;
        $budgetYTDAccountCategories = 0;
        $varianceAccountCategories = 0;
        $varianceYTDAccountCategories = 0;
        $variancePYYTDAccountCategories = 0;
        $variancePercentageAccountCategories = 0;
        $variancePercentageYTDAccountCategories = 0;
        $variancePercentagePYYTDAccountCategories = 0;
        $variancePYAccountCategories = 0;
        $variancePercentagePYAccountCategories = 0;
        $accountCategoryLabel = $y;
        foreach ($i as $x => $z) {
            $variance = 0;
            $varianceYTD = 0;
            $variancePYYTD = 0;
            $variancePY = 0;
            $actual = $z[0] ?? 0;
            $budget = $z['budget'] ?? 0;
            $ytd = $z['ytd'] ?? 0;
            $priorYTD = $z['priorYTD'] ?? 0;
            $budgetYTD = $z['budgetYTD'] ?? 0;
            $priorYear = $z['priorYear'] ?? 0;
            $actualPY = $z['actualPY'] ?? 0;


            $variance =  $type == EXPENDITURE ? variance($budget, $actual) : variance($actual, $budget);
            $varianceYTD =   $type == EXPENDITURE ? variance($budgetYTD, $ytd) : variance($ytd, $budgetYTD);
            $variancePYYTD =  variance($ytd, $priorYTD);
            $variancePY =    variance($actual, $actualPY);

            /* COMPUTATION PER INCOME ACCOUNT */
            $dataArray[$accountCategoryLabel][$x]['variance'] = $variance;
            $dataArray[$accountCategoryLabel][$x]['variancePercentage'] = variancePercentage($variance, $budget);

            $dataArray[$accountCategoryLabel][$x]['varianceYTD'] = $varianceYTD;
            $dataArray[$accountCategoryLabel][$x]['variancePercentageYTD'] = variancePercentage($varianceYTD, $budgetYTD);


            $dataArray[$accountCategoryLabel][$x]['variancePYYTD'] = $variancePYYTD;
            $dataArray[$accountCategoryLabel][$x]['variancePercentagePYYTD'] = variancePercentage($variancePYYTD, $ytd);

            $dataArray[$accountCategoryLabel][$x]['variancePY'] = $variancePY;
            $dataArray[$accountCategoryLabel][$x]['variancePercentagePY'] = variancePercentage($variancePY, $actual);
            /* END */

            /* COMPUTATION OF SUB-TOTAL PER ACCOUNT CATEGORY */
            $actualTotal += $actual;
            $actualTotal_sub[$y] += $actual;
            $actualTotalPrior_sub[$y] += $actualPY;
            $actualTotalY_sub[$y] += $ytd;
            $actualTotalPriorY_sub[$y] += $priorYTD;
            $budgetTotal += $budget;
            $YTDTotal += $ytd;
            $budgetYTDTotal += $budgetYTD;
            $budgetYTDAccountCategories += $budgetYTD;
            $PYTotal += $priorYear;
            $PYYearTotal += $priorYTD;
            $actualPYTotal += $actualPY;

            $varianceTotal += $variance;
            $varianceAccountCategories += $variance;

            $budgetAccountCategories += $budget;

            $varianceYTDTotal += $varianceYTD;
            $varianceYTDAccountCategories += $varianceYTD;


            $variancePYYTDTotal += $variancePYYTD;
            $variancePYYTDAccountCategories += $variancePYYTD;

            $variancePYTotal += $variancePY;
            $variancePYAccountCategories += $variancePY;

            $variancePercentageAccountCategories += variancePercentage($variance, $budget);
            $variancePercentageYTDAccountCategories += variancePercentage($varianceYTD, $budgetYTD);
            $variancePercentagePYYTDAccountCategories += variancePercentage($variancePYYTD, $ytd);
            $variancePercentagePYAccountCategories += variancePercentage($variancePY, $actual);
            /* END */
        }
        $accountCategoriesSubTotals[$y]['description'] = 'Sub-total';
        $accountCategoriesSubTotals[$y]['variance'] = $varianceAccountCategories;
        $accountCategoriesSubTotals[$y]['variancePercentage'] = variancePercentage($varianceAccountCategories, $budgetAccountCategories);

        $accountCategoriesSubTotals[$y]['varianceYTD'] = $varianceYTDAccountCategories;
        $accountCategoriesSubTotals[$y]['variancePercentageYTD'] = variancePercentage($varianceYTDAccountCategories, $budgetYTDAccountCategories);

        $variancePYYTD = $actualTotalY_sub[$y] - $actualTotalPriorY_sub[$y];
        $accountCategoriesSubTotals[$y]['variancePYYTD'] = $variancePYYTD;
        $accountCategoriesSubTotals[$y]['variancePercentagePYYTD'] = variancePercentage($variancePYYTD, $actualTotalY_sub[$y]);


        $variangePY = $actualTotal_sub[$y] - $actualTotalPrior_sub[$y];
        $accountCategoriesSubTotals[$y]['variancePY'] = $variangePY;
        $accountCategoriesSubTotals[$y]['variancePercentagePY'] = variancePercentage($variangePY, $actualTotal_sub[$y]);

    }
    $subtotals['variance'] =   $type == EXPENDITURE ? variance($budgetTotal, $actualTotal) : variance($actualTotal, $budgetTotal);
    $subtotals['variancePercentage'] = variancePercentage($varianceTotal, $budgetTotal);

    $subtotals['varianceYTD'] =  $type == EXPENDITURE ? variance($budgetYTDTotal, $YTDTotal) : variance($YTDTotal, $budgetYTDTotal);
    $subtotals['variancePercentageYTD'] = variancePercentage($varianceYTDTotal, $budgetYTDTotal);

    $subtotals['variancePYYTD'] =   variance($YTDTotal, $PYYearTotal);
    $subtotals['variancePercentagePYYTD'] = variancePercentage($variancePYYTDTotal, $YTDTotal);

    $subtotals['variancePY'] =  variance($actualTotal, $PYTotal);
    $subtotals['variancePercentagePY'] = variancePercentage($variancePYTotal, $actualTotal);

    return ['data' => $dataArray, 'subTotalAccountCategory' => $accountCategoriesSubTotals, 'subTotals' => $subtotals];
}

function withoutAccountCategoriesComputations($dataArray, $type)
{
    foreach ($dataArray as $x => $z) {
        $variance = 0;
        $varianceYTD = 0;
        $variancePYYTD = 0;
        $actual = $z[0] ?? 0;
        $budget = $z['budget'] ?? 0;
        $ytd = $z['ytd'] ?? 0;
        $priorYTD = $z['priorYTD'] ?? 0;
        $budgetYTD = $z['budgetYTD'] ?? 0;
        $priorYear = $z['priorYear'] ?? 0;
        $actualPY = $z['actualPY'] ?? 0;

        $variance =   $type == EXPENDITURE ? variance($budget, $actual) : variance($actual, $budget);
        $varianceYTD =   $type == EXPENDITURE ? variance($budgetYTD, $ytd) : variance($ytd, $budgetYTD);
        $variancePYYTD =  variance($ytd, $priorYTD);
        $variancePY =  variance($actual, $actualPY);

        /* COMPUTATION PER INCOME ACCOUNT */
        $dataArray[$x]['variance'] = $variance;
        $dataArray[$x]['variancePercentage'] = variancePercentage($variance, $budget);
        $dataArray[$x]['varianceYTD'] = $varianceYTD;
        $dataArray[$x]['variancePercentageYTD'] = variancePercentage($varianceYTD, $budgetYTD);
        $dataArray[$x]['variancePYYTD'] = $variancePYYTD;
        $dataArray[$x]['variancePercentagePYYTD'] = variancePercentage($variancePYYTD, $ytd);
        $dataArray[$x]['variancePY'] = $variancePY;
        $dataArray[$x]['variancePercentagePY'] = variancePercentage($variancePY, $actual);
        /* END */

    }

    return ['data' => $dataArray];
}
function profitLossProcess(&$context)
{
    $threshold = 3;
    global $sess, $clientDirectory, $pathPrefix, $propertyID, $pdf, $shortMonthName;

    if (! isPostback()) {
        $view = new MasterPage(userViews(), '/managementReports/profitLossProcess.html');
        $view->setSection($context['module']);
    } else {
        $view = new UserControl(userViews(), '/managementReports/profitLossProcess.html');
    }

    $view->bindAttributesFrom($_REQUEST);
    $validationErrors =  [];

    if ($context[IS_TASK]) {
        $view->bindAttributesFrom($context);
        extract($context);
    } else {
        $view->bindAttributesFrom($context);
        $view->bindAttributesFrom($_REQUEST);
        $count = 1;
        $view->items['propertyCount'] = $count;
        $queue = new Queue(TASKTYPE_TRIAL_BALANCE);
        if ($count > THRESHOLD_TRIALBALANCE) {
            $queue->add($_SESSION['clientID'], $_SESSION['un'], 'command=profitLossProcess&module=mangementReports', $_REQUEST);
        }
    }

    if (($context[IS_TASK]) || ($count <= THRESHOLD_TRIALBALANCE)) {
        if ($view->items['properties']) {
            $view->items['property'] = $view->items['properties'];
        }
        if ($view->items['property']) {
            $properties = deserializeParameters($view->items['property']);
        } else {
            $properties = [$propertyID];
        }

        $startDate = dbGetMasterCalendarPeriodForDate($view->items['calendarName'], $view->items['startDate']);
        $endDate = dbGetMasterCalendarPeriodForDate($view->items['calendarName'], $view->items['endDate']);

        $showAccounts = (int) $view->items['showAccounts'];
        $showAccountCategories = (int) $view->items['showCategories'];

        $date = $view->items['endDate'];
        $format = $view->items['format'];
        $type = $view->items['type'];
        $priorYear = true;

        switch ($view->items['basis']) {
            case BASIS_CASH:
                $node = 'Cash';
                $basis = 'Cash Basis';
                break;
            case BASIS_ACCRUALS:
                $node = 'Accruals';
                $basis = 'Accruals Basis';
                break;
        }

        $view->items['year'] = $endDate['year'];

        $lastPeriod = periodBefore($startDate['period'], $startDate['year']);
        $lastYear = $endDate['year'] - 1;
        $totals = [];

        $accountGroups = [
            INCOME => 'Income',
            EXPENDITURE => 'Expenditure',
        ];

        if ($view->items['basis'] == BASIS_CASH && $view->items['showBankBalance'] == 1) {
            $accountGroups = [
                INCOME => 'Income',
                EXPENDITURE => 'Expenditure',
                BALANCESHEET => 'Balance Sheet',
            ];
        }

        $accountCategories = mapParameters(dbGetAccountGroup(false), 'accountGroup', 'accountDescription');

        $startYear = $year;

        $data = [];
        $totals = [];
        $subtotals = [];
        $expDisData = [];
        $netProfitTotal = [];
        $accounts = [];
        $periods = [];
        $accountCategoriesSubTotals = [];

        $numberOfPeriod = $view->items['numberOfPeriod'] ?? 3;
        switch ($view->items['type']) {
            case 8:case 9:
                $_periods = 1;
                break;
            case 1:
                $_periods = ($numberOfPeriod + 1);
                break;
            case 7:
            case 6:
                $_periods = 4;
                break;
            case 2:
            case 5:
                $_periods = 12;
                break;
            case 4:
            case 3:
                $_periods = 1;
                $periodTo = $endDate['period'];
                break;
        }

        $periodLookup = [];

        $linkedAccounts = dbGetLinkedAccounts();
        foreach ($accountGroups as $accountType => $accountGroupName) {
            $period = ($view->items['type'] == 7 || $view->items['type'] == 6 || $view->items['type'] == 2 || $view->items['type'] == 5) ? 12 : $startDate['period'];

            if ($view->items['type'] == 1 || $view->items['type'] == 8 || $view->items['type'] == 9) {
                $period = $endDate['period'];
            }

            if ($view->items['type'] <= 2 || $view->items['type'] == 5  || $view->items['type'] == 8 || $view->items['type'] == 9) {
                $periodTo = null;
            }
            $year = $endDate['year'];

            $subtotals[$accountType]['description'] = 'Total ' . $accountGroupName;
            $subtotals['isExpDis']['description'] = 'Total Distributions';
            if ($view->items['basis'] == BASIS_CASH && $view->items['showBankBalance'] == 1) {
                $subtotals['isExpDis']['description'] = 'Total Balance Sheet Movements';
            }

            $factor = ($accountType == 'I') ? -1 : 1;

            $ytd = dbGetTrialBalanceYTDByAccountGroup($properties, $accountType, $endDate['period'], $year, $view->items['type'], $view->items['forecastPeriodFrom'], $view->items['ownerID']);
            foreach ($ytd as $y) {
                $isExpDis = dbCheckAccountIdExpDis($y['accountID']);
                if ($view->items['basis'] == BASIS_CASH && $view->items['showBankBalance'] == 1) {
                    $isExpDis = dbCheckAccountIdExpDisOrBS($y['accountID']);

                    if ($accountType == BALANCESHEET && in_array($y['accountID'], $linkedAccounts)) {
                        continue;
                    }
                }

                if ($showAccountCategories) {
                    $accountCategory = dbGetAccountGroup($y['accountID']);
                    if ($isExpDis) {
                        $expDisData[$accountType][$accountCategory['accountGroup']][$y['accountID']]['ownerAccountCode'] = $y['ownerAccountCode'];
                        $expDisData[$accountType][$accountCategory['accountGroup']][$y['accountID']]['ownerAccountDesc'] = $y['ownerAccountDesc'];
                        $expDisData[$accountType][$accountCategory['accountGroup']][$y['accountID']]['accountGroupName'] = $accountCategory['accountDescription'];
                        $expDisData[$accountType][$accountCategory['accountGroup']][$y['accountID']]['accountID'] = $y['accountID'];
                        $expDisData[$accountType][$accountCategory['accountGroup']][$y['accountID']]['ytd'] = bcmul($factor, $y['balance' . $node], 2);
                        $expDisData[$accountType][$accountCategory['accountGroup']][$y['accountID']]['description'] = $y['accountName'];
                        $subtotals['isExpDis']['ytd'] += $expDisData[$accountType][$accountCategory['accountGroup']][$y['accountID']]['ytd'];
                        $accountCategoriesSubTotals[$accountCategory['accountGroup']]['ytd'] += $expDisData[$accountType][$accountCategory['accountGroup']][$y['accountID']]['ytd'];
                    } else {
                        $data[$accountType][$accountCategory['accountGroup']][$y['accountID']]['ownerAccountDesc'] = $y['ownerAccountDesc'];
                        $data[$accountType][$accountCategory['accountGroup']][$y['accountID']]['ownerAccountCode'] = $y['ownerAccountCode'];
                        $data[$accountType][$accountCategory['accountGroup']][$y['accountID']]['accountGroupName'] = $accountCategory['accountDescription'];
                        $data[$accountType][$accountCategory['accountGroup']][$y['accountID']]['accountID'] = $y['accountID'];
                        $data[$accountType][$accountCategory['accountGroup']][$y['accountID']]['ytd'] = bcmul($factor, $y['balance' . $node], 2);
                        $data[$accountType][$accountCategory['accountGroup']][$y['accountID']]['description'] = $y['accountName'];
                        $subtotals[$accountType]['ytd'] += $data[$accountType][$accountCategory['accountGroup']][$y['accountID']]['ytd'];
                        $accountCategoriesSubTotals[$accountCategory['accountGroup']]['ytd'] += $data[$accountType][$accountCategory['accountGroup']][$y['accountID']]['ytd'];
                    }
                } elseif ($isExpDis) {
                    $expDisData[$accountType][$y['accountID']]['ownerAccountCode'] = $y['ownerAccountCode'];
                    $expDisData[$accountType][$y['accountID']]['ownerAccountDesc'] = $y['ownerAccountDesc'];
                    $expDisData[$accountType][$y['accountID']]['accountID'] = $y['accountID'];
                    $expDisData[$accountType][$y['accountID']]['ytd'] = bcmul($factor, $y['balance' . $node], 2);
                    $expDisData[$accountType][$y['accountID']]['description'] = $y['accountName'];
                    $subtotals['isExpDis']['ytd'] += $expDisData[$accountType][$y['accountID']]['ytd'];
                } else {
                    $data[$accountType][$y['accountID']]['ownerAccountCode'] = $y['ownerAccountCode'];
                    $data[$accountType][$y['accountID']]['ownerAccountDesc'] = $y['ownerAccountDesc'];
                    $data[$accountType][$y['accountID']]['accountID'] = $y['accountID'];
                    $data[$accountType][$y['accountID']]['ytd'] = bcmul($factor, $y['balance' . $node], 2);
                    $data[$accountType][$y['accountID']]['description'] = $y['accountName'];
                    $subtotals[$accountType]['ytd'] += $data[$accountType][$y['accountID']]['ytd'];
                }
            }

            if ($view->items['type'] != 1) {
                for ($i = 0; $i < $_periods; $i++) {
                    if ($view->items['type'] == 6 || $view->items['type'] == 7) {
                        $periodTo = $period - 2;
                        $oldPeriod = $period;
                        $period = $periodTo;
                        $periodTo = $oldPeriod;
                    }
                    if ($view->items['type'] != 5) {
                        $balances = dbGetTrialBalanceForPeriodByAccountGroup($properties, $accountType, $period, $year, $periodTo, $view->items['ownerID']);
                    } else {
                        if ($period < $view->items['forecastPeriodFrom']) {
                            $balances = dbGetTrialBalanceForPeriodByAccountGroup($properties, $accountType, $period, $year, $periodTo, $view->items['ownerID']);
                        } else {
                            $balances = dbGetForecastForPeriodByAccountGroup($properties, $accountType, $period, $year, $periodTo, $view->items['ownerID']);
                        }
                    }


                    if (! $periods[$i]) {
                        $date = dbGetMasterCalendarForPeriod($view->items['calendarName'], $period, $year);
                        [$_d, $_m, $_y] = explode('/', $date['endDate']);
                        $periods[$i] = $shortMonthName[(int) $_m] . ' ' . $_y . ($period < $view->items['forecastPeriodFrom'] || $view->items['type'] != 5 ? '' : ' (F)');
                        $periodLookup[$i]['period'] = $period;
                        $periodLookup[$i]['year'] = $year;
                    }

                    if ($balances) {
                        foreach ($balances as $b) {
                            $isExpDis = dbCheckAccountIdExpDis($b['accountID']);
                            if ($view->items['basis'] == BASIS_CASH && $view->items['showBankBalance'] == 1) {
                                $isExpDis = dbCheckAccountIdExpDisOrBS($b['accountID']);

                                if ($accountType == BALANCESHEET && in_array($b['accountID'], $linkedAccounts)) {
                                    continue;
                                }
                            }

                            if ($showAccountCategories) {
                                $accountCategory = dbGetAccountGroup($b['accountID']);
                                if ($isExpDis) {
                                    $expDisData[$accountType][$accountCategory['accountGroup']][$b['accountID']]['ownerAccountDesc'] = $b['ownerAccountDesc'];
                                    $expDisData[$accountType][$accountCategory['accountGroup']][$b['accountID']]['ownerAccountCode'] = $b['ownerAccountCode'];
                                    $expDisData[$accountType][$accountCategory['accountGroup']][$b['accountID']]['accountGroupName'] = $accountCategory['accountDescription'];
                                    $expDisData[$accountType][$accountCategory['accountGroup']][$b['accountID']]['accountID'] = $b['accountID'];
                                    $expDisData[$accountType][$accountCategory['accountGroup']][$b['accountID']]['description'] = $b['accountName'];
                                    $expDisData[$accountType][$accountCategory['accountGroup']][$b['accountID']][$i] = bcmul($factor, $b['balance' . $node], 2);
                                    $subtotals['isExpDis'][$i] += $expDisData[$accountType][$accountCategory['accountGroup']][$b['accountID']][$i];
                                    $accountCategoriesSubTotals[$accountCategory['accountGroup']][$i] += $expDisData[$accountType][$accountCategory['accountGroup']][$b['accountID']][$i];
                                } else {

                                    $data[$accountType][$accountCategory['accountGroup']][$b['accountID']]['ownerAccountDesc'] = $b['ownerAccountDesc'];
                                    $data[$accountType][$accountCategory['accountGroup']][$b['accountID']]['ownerAccountCode'] = $b['ownerAccountCode'];
                                    $data[$accountType][$accountCategory['accountGroup']][$b['accountID']][$i] = bcmul($factor, $b['balance' . $node], 2);
                                    $data[$accountType][$accountCategory['accountGroup']][$b['accountID']]['accountGroupName'] = $accountCategory['accountDescription'];
                                    $data[$accountType][$accountCategory['accountGroup']][$b['accountID']]['accountID'] = $b['accountID'];
                                    $data[$accountType][$accountCategory['accountGroup']][$b['accountID']]['description'] = $b['accountName'];
                                    $subtotals[$accountType][$i] += $data[$accountType][$accountCategory['accountGroup']][$b['accountID']][$i];
                                    $accountCategoriesSubTotals[$accountCategory['accountGroup']][$i] += $data[$accountType][$accountCategory['accountGroup']][$b['accountID']][$i];

                                    $accountCategoriesSubTotals[$accountCategory['accountGroup']]['quarterVariance'][$i] += $data[$accountType][$accountCategory['accountGroup']][$b['accountID']][$i];
                                }
                            } elseif ($isExpDis) {
                                $expDisData[$accountType][$b['accountID']]['ownerAccountDesc'] = $b['ownerAccountDesc'];
                                $expDisData[$accountType][$b['accountID']]['ownerAccountCode'] = $b['ownerAccountCode'];
                                $expDisData[$accountType][$b['accountID']]['accountID'] = $b['accountID'];
                                $expDisData[$accountType][$b['accountID']]['description'] = $b['accountName'];
                                $expDisData[$accountType][$b['accountID']][$i] = bcmul($factor, $b['balance' . $node], 2);
                                $subtotals['isExpDis'][$i] += $expDisData[$accountType][$b['accountID']][$i];
                            } else {
                                $data[$accountType][$b['accountID']]['ownerAccountDesc'] = $b['ownerAccountDesc'];
                                $data[$accountType][$b['accountID']]['ownerAccountCode'] = $b['ownerAccountCode'];
                                $data[$accountType][$b['accountID']]['accountID'] = $b['accountID'];
                                $data[$accountType][$b['accountID']]['description'] = $b['accountName'];
                                $data[$accountType][$b['accountID']][$i] = bcmul($factor, $b['balance' . $node], 2);
                                $subtotals[$accountType][$i] += $data[$accountType][$b['accountID']][$i];
                            }
                        }
                    }
                    if ($view->items['type'] == 7 && ! isset($quarterRun)) {

                        $_budget = dbGetBudgetForPeriod($properties, $endDate['year'], $period, $periodTo, 'TRACC2', $view->items['ownerID']);
                        foreach ($_budget as $b) {
                            $budget = $b['budget' . $node];
                            $isExpDis = dbCheckAccountIdExpDis($b['accountID']);
                            if ($view->items['basis'] == BASIS_CASH && $view->items['showBankBalance'] == 1) {
                                $isExpDis = dbCheckAccountIdExpDisOrBS($b['accountID']);

                                if ($accountType == BALANCESHEET && in_array($b['accountID'], $linkedAccounts)) {
                                    continue;
                                }
                            }

                            if ($showAccountCategories) {
                                $accountCategory = dbGetAccountGroup($b['accountID']);
                                if (! $isExpDis) {
                                    $data[$b['accountType']][$accountCategory['accountGroup']][$b['accountID']]['quarterBudget'][$i] = $b['budget' . $node];
                                }
                            } else {
                                if ($isExpDis) {
                                    $expDisData[$b['accountType']][$b['accountID']]['quarterBudget'][$i] = $b['budget' . $node];
                                } else {
                                    $data[$b['accountType']][$b['accountID']]['quarterBudget'][$i] = $b['budget' . $node];
                                }
                            }
                        }
                    }


                    [$period, $year] = periodBefore($period, $year);
                }

            }

            if ($view->items['type'] == 1) {
                for ($i = 0; $i < $_periods; $i++) {
                    [$period, $year] = periodBefore($period, $year);
                }

                [$period, $year] = periodAfter($period, $year);

                for ($i =  $_periods; $i > 0; $i--) {
                    if ($view->items['type'] == 6 || $view->items['type'] == 7) {
                        $periodTo = $period - 2;
                        $oldPeriod = $period;
                        $period = $periodTo;
                        $periodTo = $oldPeriod;
                    }
                    if ($view->items['type'] != 5) {
                        $balances = dbGetTrialBalanceForPeriodByAccountGroup($properties, $accountType, $period, $year, $periodTo, $view->items['ownerID']);
                    } else {
                        if ($period < $view->items['forecastPeriodFrom']) {
                            $balances = dbGetTrialBalanceForPeriodByAccountGroup($properties, $accountType, $period, $year, $periodTo, $view->items['ownerID']);
                        } else {
                            $balances = dbGetForecastForPeriodByAccountGroup($properties, $accountType, $period, $year, $periodTo, $view->items['ownerID']);
                        }
                    }


                    if (! $periods[$i]) {
                        $date = dbGetMasterCalendarForPeriod($view->items['calendarName'], $period, $year);
                        [$_d, $_m, $_y] = explode('/', $date['endDate']);
                        $periods[$i] = $shortMonthName[(int) $_m] . ' ' . $_y . ($period < $view->items['forecastPeriodFrom'] || $view->items['type'] != 5 ? '' : ' (F)');
                        $periodLookup[$i]['period'] = $period;
                        $periodLookup[$i]['year'] = $year;
                    }

                    if ($balances) {
                        foreach ($balances as $b) {
                            $isExpDis = dbCheckAccountIdExpDis($b['accountID']);
                            if ($view->items['basis'] == BASIS_CASH && $view->items['showBankBalance'] == 1) {
                                $isExpDis = dbCheckAccountIdExpDisOrBS($b['accountID']);

                                if ($accountType == BALANCESHEET && in_array($b['accountID'], $linkedAccounts)) {
                                    continue;
                                }
                            }

                            if ($showAccountCategories) {
                                $accountCategory = dbGetAccountGroup($b['accountID']);
                                if ($isExpDis) {
                                    $expDisData[$accountType][$accountCategory['accountGroup']][$b['accountID']]['ownerAccountDesc'] = $b['ownerAccountDesc'];
                                    $expDisData[$accountType][$accountCategory['accountGroup']][$b['accountID']]['ownerAccountCode'] = $b['ownerAccountCode'];
                                    $expDisData[$accountType][$accountCategory['accountGroup']][$b['accountID']]['accountGroupName'] = $accountCategory['accountDescription'];
                                    $expDisData[$accountType][$accountCategory['accountGroup']][$b['accountID']]['accountID'] = $b['accountID'];
                                    $expDisData[$accountType][$accountCategory['accountGroup']][$b['accountID']]['description'] = $b['accountName'];
                                    $expDisData[$accountType][$accountCategory['accountGroup']][$b['accountID']][$i] = bcmul($factor, $b['balance' . $node], 2);
                                    $subtotals['isExpDis'][$i] += $expDisData[$accountType][$accountCategory['accountGroup']][$b['accountID']][$i];
                                    $accountCategoriesSubTotals[$accountCategory['accountGroup']][$i] += $expDisData[$accountType][$accountCategory['accountGroup']][$b['accountID']][$i];
                                } else {

                                    $data[$accountType][$accountCategory['accountGroup']][$b['accountID']]['ownerAccountDesc'] = $b['ownerAccountDesc'];
                                    $data[$accountType][$accountCategory['accountGroup']][$b['accountID']]['ownerAccountCode'] = $b['ownerAccountCode'];
                                    $data[$accountType][$accountCategory['accountGroup']][$b['accountID']][$i] = bcmul($factor, $b['balance' . $node], 2);
                                    $data[$accountType][$accountCategory['accountGroup']][$b['accountID']]['accountGroupName'] = $accountCategory['accountDescription'];
                                    $data[$accountType][$accountCategory['accountGroup']][$b['accountID']]['accountID'] = $b['accountID'];
                                    $data[$accountType][$accountCategory['accountGroup']][$b['accountID']]['description'] = $b['accountName'];
                                    $subtotals[$accountType][$i] += $data[$accountType][$accountCategory['accountGroup']][$b['accountID']][$i];
                                    $accountCategoriesSubTotals[$accountCategory['accountGroup']][$i] += $data[$accountType][$accountCategory['accountGroup']][$b['accountID']][$i];

                                    $accountCategoriesSubTotals[$accountCategory['accountGroup']]['quarterVariance'][$i] += $data[$accountType][$accountCategory['accountGroup']][$b['accountID']][$i];
                                }
                            } else {
                                if ($isExpDis) {
                                    $expDisData[$accountType][$b['accountID']]['ownerAccountDesc'] = $b['ownerAccountDesc'];
                                    $expDisData[$accountType][$b['accountID']]['ownerAccountCode'] = $b['ownerAccountCode'];
                                    $expDisData[$accountType][$b['accountID']]['accountID'] = $b['accountID'];
                                    $expDisData[$accountType][$b['accountID']]['description'] = $b['accountName'];
                                    $expDisData[$accountType][$b['accountID']][$i] = bcmul($factor, $b['balance' . $node], 2);
                                    $subtotals['isExpDis'][$i] += $expDisData[$accountType][$b['accountID']][$i];
                                } else {
                                    $data[$accountType][$b['accountID']]['ownerAccountDesc'] = $b['ownerAccountDesc'];
                                    $data[$accountType][$b['accountID']]['ownerAccountCode'] = $b['ownerAccountCode'];
                                    $data[$accountType][$b['accountID']]['accountID'] = $b['accountID'];
                                    $data[$accountType][$b['accountID']]['description'] = $b['accountName'];
                                    $data[$accountType][$b['accountID']][$i] = bcmul($factor, $b['balance' . $node], 2);
                                    $subtotals[$accountType][$i] += $data[$accountType][$b['accountID']][$i];
                                }
                            }
                        }
                    }
                    if ($view->items['type'] == 7 && ! isset($quarterRun)) {

                        $_budget = dbGetBudgetForPeriod($properties, $endDate['year'], $period, $periodTo, 'TRACC2', $view->items['ownerID']);
                        foreach ($_budget as $b) {
                            $budget = $b['budget' . $node];
                            $isExpDis = dbCheckAccountIdExpDis($b['accountID']);
                            if ($view->items['basis'] == BASIS_CASH && $view->items['showBankBalance'] == 1) {
                                $isExpDis = dbCheckAccountIdExpDisOrBS($b['accountID']);

                                if ($accountType == BALANCESHEET && in_array($b['accountID'], $linkedAccounts)) {
                                    continue;
                                }
                            }

                            if ($showAccountCategories) {
                                $accountCategory = dbGetAccountGroup($b['accountID']);
                                if (! $isExpDis) {
                                    $data[$b['accountType']][$accountCategory['accountGroup']][$b['accountID']]['quarterBudget'][$i] = $b['budget' . $node];
                                }
                            } else {
                                if ($isExpDis) {
                                    $expDisData[$b['accountType']][$b['accountID']]['quarterBudget'][$i] = $b['budget' . $node];
                                } else {
                                    $data[$b['accountType']][$b['accountID']]['quarterBudget'][$i] = $b['budget' . $node];
                                }
                            }
                        }
                    }


                    [$period, $year] = periodAfter($period, $year);
                }

            }
            $quarterRun = true;
        }

        if ($view->items['type'] == 7) {
            $period =   12;
            for ($i = 0; $i < $_periods; $i++) {

                $periodTo = $period - 2;
                $oldPeriod = $period;
                $period = $periodTo;
                $periodTo = $oldPeriod;


                $_budget = dbGetBudgetForPeriod($properties, $endDate['year'], $period, $periodTo, 'TRACC2', $view->items['ownerID']);
                foreach ($_budget as $b) {
                    $budget = $b['budget' . $node];
                    $isExpDis = dbCheckAccountIdExpDis($b['accountID']);
                    if ($view->items['basis'] == BASIS_CASH && $view->items['showBankBalance'] == 1) {
                        $isExpDis = dbCheckAccountIdExpDisOrBS($b['accountID']);

                        if ($accountType == BALANCESHEET && in_array($b['accountID'], $linkedAccounts)) {
                            continue;
                        }
                    }

                    switch ($period) {
                        case 10:$indexQuarter = 3;
                            break;
                        case 7:$indexQuarter = 2;
                            break;
                        case 4:$indexQuarter = 1;
                            break;
                        case 1:$indexQuarter = 0;
                            break;
                    }


                    if ($showAccountCategories) {
                        $accountCategory = dbGetAccountGroup($b['accountID']);
                        if (isset($data[$b['accountType']][$accountCategory['accountGroup']][$b['accountID']]['description'])) {
                            if ($isExpDis) {
                                $subtotals['isExpDis']['quarterBudget'][$i] += $b['budget' . $node];
                                $accountCategoriesSubTotals[$accountCategory['accountGroup']]['quarterBudget'][$indexQuarter] += $b['budget' . $node];
                            } else {
                                $subtotals[$b['accountType']]['quarterBudget'][$i] += $b['budget' . $node];
                                $accountCategoriesSubTotals[$accountCategory['accountGroup']]['quarterBudget'][$indexQuarter] += $b['budget' . $node];
                            }
                        }
                    } else {
                        if (isset($data[$b['accountType']][$b['accountID']]['description'])) {
                            if ($isExpDis) {
                                $subtotals['isExpDis']['quarterBudget'][$i] += $b['budget' . $node];
                            } else {
                                $subtotals[$b['accountType']]['quarterBudget'][$i] += $b['budget' . $node];
                            }
                        }
                    }

                }

                [$period, $year] = periodBefore($period, $year);
            }

        }

        if ($view->items['type'] == 7) {
            $_budgetYTD = dbGetBudgetForYTD($properties, $endDate['year'], $endDate['period'], 'TRACC2', $view->items['ownerID']);
            foreach ($_budgetYTD as $b) {
                $budget = $b['budget' . $node];


                $isExpDis = dbCheckAccountIdExpDis($b['accountID']);
                if ($view->items['basis'] == BASIS_CASH && $view->items['showBankBalance'] == 1) {
                    $isExpDis = dbCheckAccountIdExpDisOrBS($b['accountID']);

                    if ($accountType == BALANCESHEET && in_array($b['accountID'], $linkedAccounts)) {
                        continue;
                    }
                }

                if ($showAccountCategories) {
                    $accountCategory = dbGetAccountGroup($b['accountID']);
                    if (isset($data[$b['accountType']][$accountCategory['accountGroup']][$b['accountID']]['description'])) {
                        if ($isExpDis) {
                            $actual = $expDisData[$b['accountType']][$accountCategory['accountGroup']][$b['accountID']]['ytd'];
                            $variance = $b['accountType'] == EXPENDITURE ? bcsub($budget, $actual, 2) : bcsub($actual, $budget, 2);

                            $expDisData[$b['accountType']][$accountCategory['accountGroup']][$b['accountID']]['ownerAccountCode'] = $b['ownerAccountCode'];
                            $expDisData[$b['accountType']][$accountCategory['accountGroup']][$b['accountID']]['ownerAccountDesc'] = $b['ownerAccountDesc'];
                            $expDisData[$b['accountType']][$accountCategory['accountGroup']][$b['accountID']]['accountGroupName'] = $accountCategory['accountDescription'];
                            $expDisData[$b['accountType']][$accountCategory['accountGroup']][$b['accountID']]['budgetYTD'] = $b['budget' . $node];
                            $expDisData[$b['accountType']][$accountCategory['accountGroup']][$b['accountID']]['description'] = $b['accountName'];
                            $expDisData[$b['accountType']][$accountCategory['accountGroup']][$b['accountID']]['accountID'] = $b['accountID'];
                            $subtotals['isExpDis']['budgetYTD'] += $budget;
                            $accountCategoriesSubTotals[$accountCategory['accountGroup']]['budgetYTD'] += $budget;

                            $expDisData[$b['accountType']][$accountCategory['accountGroup']][$b['accountID']]['varianceYTD'] = $variance;
                            $expDisData[$b['accountType']][$accountCategory['accountGroup']][$b['accountID']]['variancePercentageYTD'] = variancePercentage($variance, $budget);
                        } else {
                            $actual = $data[$b['accountType']][$accountCategory['accountGroup']][$b['accountID']]['ytd'];
                            $variance = bcsub($actual, $budget, 2);

                            $data[$b['accountType']][$accountCategory['accountGroup']][$b['accountID']]['ownerAccountCode'] = $b['ownerAccountCode'];
                            $data[$b['accountType']][$accountCategory['accountGroup']][$b['accountID']]['ownerAccountDesc'] = $b['ownerAccountDesc'];
                            $data[$b['accountType']][$accountCategory['accountGroup']][$b['accountID']]['accountGroupName'] = $accountCategory['accountDescription'];
                            $data[$b['accountType']][$accountCategory['accountGroup']][$b['accountID']]['budgetYTD'] = $b['budget' . $node];
                            $data[$b['accountType']][$accountCategory['accountGroup']][$b['accountID']]['description'] = $b['accountName'];
                            $data[$b['accountType']][$accountCategory['accountGroup']][$b['accountID']]['accountID'] = $b['accountID'];
                            $subtotals[$b['accountType']]['budgetYTD'] += $budget;
                            $accountCategoriesSubTotals[$accountCategory['accountGroup']]['budgetYTD'] += $budget;

                            $data[$b['accountType']][$accountCategory['accountGroup']][$b['accountID']]['varianceYTD'] = $variance;
                            $data[$b['accountType']][$accountCategory['accountGroup']][$b['accountID']]['variancePercentageYTD'] = variancePercentage($variance, $budget);
                        }
                    }
                } else {
                    if (isset($data[$b['accountType']][$b['accountID']]['description'])) {
                        if ($isExpDis) {
                            $actual = $expDisData[$b['accountType']][$b['accountID']]['ytd'];
                            $variance = $b['accountType'] == EXPENDITURE ? bcsub($budget, $actual, 2) : bcsub($actual, $budget, 2);

                            $expDisData[$b['accountType']][$b['accountID']]['ownerAccountCode'] = $b['ownerAccountCode'];
                            $expDisData[$b['accountType']][$b['accountID']]['ownerAccountDesc'] = $b['ownerAccountDesc'];
                            $expDisData[$b['accountType']][$b['accountID']]['budgetYTD'] = $b['budget' . $node];
                            $expDisData[$b['accountType']][$b['accountID']]['description'] = $b['accountName'];
                            $expDisData[$b['accountType']][$b['accountID']]['accountID'] = $b['accountID'];
                            $subtotals['isExpDis']['budgetYTD'] += $budget;

                            $expDisData[$b['accountType']][$b['accountID']]['varianceYTD'] = $variance;
                            $expDisData[$b['accountType']][$b['accountID']]['variancePercentageYTD'] = variancePercentage($variance, $budget);
                        } else {
                            $actual = $data[$b['accountType']][$b['accountID']]['ytd'];
                            $variance = bcsub($actual, $budget, 2);

                            $data[$b['accountType']][$b['accountID']]['ownerAccountCode'] = $b['ownerAccountCode'];
                            $data[$b['accountType']][$b['accountID']]['ownerAccountDesc'] = $b['ownerAccountDesc'];
                            $data[$b['accountType']][$b['accountID']]['budgetYTD'] = $b['budget' . $node];
                            $data[$b['accountType']][$b['accountID']]['description'] = $b['accountName'];
                            $data[$b['accountType']][$b['accountID']]['accountID'] = $b['accountID'];
                            $subtotals[$b['accountType']]['budgetYTD'] += $budget;

                            $data[$b['accountType']][$b['accountID']]['varianceYTD'] = $variance;
                            $data[$b['accountType']][$b['accountID']]['variancePercentageYTD'] = variancePercentage($variance, $budget);
                        }
                    }
                }

            }
        }

        /* prior Year */
        if ($view->items['type'] == 4) {
            foreach ($accountGroups as $accountType => $accountGroupName) {
                $factor = ($accountType == 'I') ? -1 : 1;

                $ytd = dbGetTrialBalanceYTDByAccountGroup($properties, $accountType, $endDate['period'], $lastYear, $view->items['type'], $periodTo, $view->items['ownerID']);
                foreach ($ytd as $y) {
                    $priorYear = bcmul($factor, $y['balance' . $node], 2) ?? 0;

                    $isExpDis = dbCheckAccountIdExpDis($y['accountID']);
                    if ($view->items['basis'] == BASIS_CASH && $view->items['showBankBalance'] == 1) {
                        $isExpDis = dbCheckAccountIdExpDisOrBS($y['accountID']);

                        if ($accountType == BALANCESHEET && in_array($y['accountID'], $linkedAccounts)) {
                            continue;
                        }
                    }

                    if ($showAccountCategories) {
                        $accountCategory = dbGetAccountGroup($y['accountID']);
                        if ($isExpDis) {
                            $actual = $expDisData[$accountType][$accountCategory['accountGroup']][$y['accountID']]['ytd'];
                            $variance = bcsub($actual, $priorYear, 2);

                            $expDisData[$accountType][$accountCategory['accountGroup']][$y['accountID']]['ownerAccountCode'] = $y['ownerAccountCode'];
                            $expDisData[$accountType][$accountCategory['accountGroup']][$y['accountID']]['ownerAccountDesc'] = $y['ownerAccountDesc'];
                            $expDisData[$accountType][$accountCategory['accountGroup']][$y['accountID']]['accountGroupName'] = $accountCategory['accountDescription'];
                            $expDisData[$accountType][$accountCategory['accountGroup']][$y['accountID']]['accountID'] = $y['accountID'];
                            $expDisData[$accountType][$accountCategory['accountGroup']][$y['accountID']]['priorYTD'] = bcmul($factor, $y['balance' . $node], 2);
                            $expDisData[$accountType][$accountCategory['accountGroup']][$y['accountID']]['description'] = $y['accountName'];
                            $expDisData[$accountType][$accountCategory['accountGroup']][$y['accountID']]['priorYear'] = $priorYear;
                            $subtotals['isExpDis']['priorYTD'] += $priorYear;
                            $accountCategoriesSubTotals[$accountCategory['accountGroup']]['priorYTD'] += $priorYear;

                            $expDisData[$accountType][$accountCategory['accountGroup']][$y['accountID']]['variancePYYTD'] = $variance;
                            $expDisData[$accountType][$accountCategory['accountGroup']][$y['accountID']]['variancePercentagePYYTD'] = variancePercentage($variance, $actual);
                        } else {
                            $actual = $data[$accountType][$accountCategory['accountGroup']][$y['accountID']]['ytd'];
                            $variance = bcsub($actual, $priorYear, 2);

                            $data[$accountType][$accountCategory['accountGroup']][$y['accountID']]['ownerAccountCode'] = $y['ownerAccountCode'];
                            $data[$accountType][$accountCategory['accountGroup']][$y['accountID']]['ownerAccountDesc'] = $y['ownerAccountDesc'];
                            $data[$accountType][$accountCategory['accountGroup']][$y['accountID']]['accountGroupName'] = $accountCategory['accountDescription'];
                            $data[$accountType][$accountCategory['accountGroup']][$y['accountID']]['accountID'] = $y['accountID'];
                            $data[$accountType][$accountCategory['accountGroup']][$y['accountID']]['priorYTD'] = bcmul($factor, $y['balance' . $node], 2);
                            $data[$accountType][$accountCategory['accountGroup']][$y['accountID']]['description'] = $y['accountName'];
                            $data[$accountType][$accountCategory['accountGroup']][$y['accountID']]['priorYear'] = $priorYear;
                            $subtotals[$accountType]['priorYTD'] += $priorYear;
                            $accountCategoriesSubTotals[$accountCategory['accountGroup']]['priorYTD'] += $priorYear;

                            $data[$accountType][$accountCategory['accountGroup']][$y['accountID']]['variancePYYTD'] = $variance;
                            $data[$accountType][$accountCategory['accountGroup']][$y['accountID']]['variancePercentagePYYTD'] = variancePercentage($variance, $actual);
                        }
                    } else {
                        if ($isExpDis) {
                            $actual = $expDisData[$accountType][$y['accountID']]['ytd'];
                            $variance = bcsub($actual, $priorYear, 2);

                            $expDisData[$accountType][$y['accountID']]['ownerAccountDesc'] = $y['ownerAccountDesc'];
                            $expDisData[$accountType][$y['accountID']]['ownerAccountCode'] = $y['ownerAccountCode'];
                            $expDisData[$accountType][$y['accountID']]['accountID'] = $y['accountID'];
                            $expDisData[$accountType][$y['accountID']]['priorYTD'] = bcmul($factor, $y['balance' . $node], 2);
                            $expDisData[$accountType][$y['accountID']]['description'] = $y['accountName'];
                            $expDisData[$accountType][$y['accountID']]['priorYear'] = $priorYear;
                            $subtotals['isExpDis']['priorYTD'] += $priorYear;

                            $expDisData[$accountType][$y['accountID']]['variancePYYTD'] = $variance;
                            $expDisData[$accountType][$y['accountID']]['variancePercentagePYYTD'] = variancePercentage($variance, $actual);
                        } else {
                            $actual = $data[$accountType][$y['accountID']]['ytd'];
                            $variance = bcsub($actual, $priorYear, 2);

                            $data[$accountType][$y['accountID']]['ownerAccountDesc'] = $y['ownerAccountDesc'];
                            $data[$accountType][$y['accountID']]['ownerAccountCode'] = $y['ownerAccountCode'];
                            $data[$accountType][$y['accountID']]['accountID'] = $y['accountID'];
                            $data[$accountType][$y['accountID']]['priorYTD'] = bcmul($factor, $y['balance' . $node], 2);
                            $data[$accountType][$y['accountID']]['description'] = $y['accountName'];
                            $data[$accountType][$y['accountID']]['priorYear'] = $priorYear;
                            $subtotals[$accountType]['priorYTD'] += $priorYear;

                            $data[$accountType][$y['accountID']]['variancePYYTD'] = $variance;
                            $data[$accountType][$y['accountID']]['variancePercentagePYYTD'] = variancePercentage($variance, $actual);
                        }
                    }
                }


                $balancesPY = dbGetTrialBalanceForPeriodByAccountGroup($properties, $accountType, $endDate['period'], $lastYear, $periodTo, $view->items['ownerID']);
                if ($balancesPY) {
                    foreach ($balancesPY as $b) {
                        $priorYear = bcmul($factor, $b['balance' . $node], 2) ?? 0;
                        $isExpDis = dbCheckAccountIdExpDis($b['accountID']);
                        if ($view->items['basis'] == BASIS_CASH && $view->items['showBankBalance'] == 1) {
                            $isExpDis = dbCheckAccountIdExpDisOrBS($b['accountID']);

                            if ($accountType == BALANCESHEET && in_array($b['accountID'], $linkedAccounts)) {
                                continue;
                            }
                        }

                        if ($showAccountCategories) {
                            $accountCategory = dbGetAccountGroup($b['accountID']);
                            if ($isExpDis) {
                                $actual = $data[$accountType][$accountCategory['accountGroup']][$b['accountID']][0];
                                $variance = bcsub($actual, $priorYear, 2);

                                $expDisData[$accountType][$accountCategory['accountGroup']][$b['accountID']]['ownerAccountDesc'] = $b['ownerAccountDesc'];
                                $expDisData[$accountType][$accountCategory['accountGroup']][$b['accountID']]['ownerAccountCode'] = $b['ownerAccountCode'];
                                $expDisData[$accountType][$accountCategory['accountGroup']][$b['accountID']]['accountGroupName'] = $accountCategory['accountDescription'];
                                $expDisData[$accountType][$accountCategory['accountGroup']][$b['accountID']]['accountID'] = $b['accountID'];
                                $expDisData[$accountType][$accountCategory['accountGroup']][$b['accountID']]['description'] = $b['accountName'];
                                $expDisData[$accountType][$accountCategory['accountGroup']][$b['accountID']]['actualPY'] = bcmul($factor, $b['balance' . $node], 2);
                                $expDisData[$accountType][$accountCategory['accountGroup']][$b['accountID']]['priorYear'] = $priorYear;
                                $subtotals['isExpDis']['actualPY'] += $priorYear;
                                $accountCategoriesSubTotals[$accountCategory['accountGroup']]['actualPY'] += $priorYear;

                                $expDisData[$accountType][$accountCategory['accountGroup']][$b['accountID']]['variancePY'] = $variance;
                                $expDisData[$accountType][$accountCategory['accountGroup']][$b['accountID']]['variancePercentagePY'] = variancePercentage($variance, $actual);
                            } else {
                                $actual = $data[$accountType][$accountCategory['accountGroup']][$b['accountID']][0];
                                $variance = bcsub($actual, $priorYear, 2);

                                $data[$accountType][$accountCategory['accountGroup']][$b['accountID']]['ownerAccountDesc'] = $b['ownerAccountDesc'];
                                $data[$accountType][$accountCategory['accountGroup']][$b['accountID']]['ownerAccountCode'] = $b['ownerAccountCode'];
                                $data[$accountType][$accountCategory['accountGroup']][$b['accountID']]['accountGroupName'] = $accountCategory['accountDescription'];
                                $data[$accountType][$accountCategory['accountGroup']][$b['accountID']]['accountID'] = $b['accountID'];
                                $data[$accountType][$accountCategory['accountGroup']][$b['accountID']]['description'] = $b['accountName'];
                                $data[$accountType][$accountCategory['accountGroup']][$b['accountID']]['actualPY'] = bcmul($factor, $b['balance' . $node], 2);
                                $data[$accountType][$accountCategory['accountGroup']][$b['accountID']]['priorYear'] = $priorYear;
                                $subtotals[$accountType]['actualPY'] += $priorYear;
                                $accountCategoriesSubTotals[$accountCategory['accountGroup']]['actualPY'] += $priorYear;

                                $data[$accountType][$accountCategory['accountGroup']][$b['accountID']]['variancePY'] = $variance;
                                $data[$accountType][$accountCategory['accountGroup']][$b['accountID']]['variancePercentagePY'] = variancePercentage($variance, $actual);
                            }
                        } else {
                            if ($isExpDis) {
                                $actual = $expDisData[$accountType][$b['accountID']][0];
                                $variance = bcsub($actual, $priorYear, 2);

                                $expDisData[$accountType][$b['accountID']]['ownerAccountDesc'] = $b['ownerAccountDesc'];
                                $expDisData[$accountType][$b['accountID']]['ownerAccountCode'] = $b['ownerAccountCode'];
                                $expDisData[$accountType][$b['accountID']]['accountID'] = $b['accountID'];
                                $expDisData[$accountType][$b['accountID']]['description'] = $b['accountName'];
                                $expDisData[$accountType][$b['accountID']]['actualPY'] = bcmul($factor, $b['balance' . $node], 2);
                                $expDisData[$accountType][$b['accountID']]['priorYear'] = $priorYear;
                                $subtotals['isExpDis']['actualPY'] += $priorYear;

                                $expDisData[$accountType][$b['accountID']]['variancePY'] = $variance;
                                $expDisData[$accountType][$b['accountID']]['variancePercentagePY'] = variancePercentage($variance, $actual);
                            } else {
                                $actual = $data[$accountType][$b['accountID']][0];
                                $variance = bcsub($actual, $priorYear, 2);

                                $data[$accountType][$b['accountID']]['ownerAccountDesc'] = $b['ownerAccountDesc'];
                                $data[$accountType][$b['accountID']]['ownerAccountCode'] = $b['ownerAccountCode'];
                                $data[$accountType][$b['accountID']]['accountID'] = $b['accountID'];
                                $data[$accountType][$b['accountID']]['description'] = $b['accountName'];
                                $data[$accountType][$b['accountID']]['actualPY'] = bcmul($factor, $b['balance' . $node], 2);
                                $data[$accountType][$b['accountID']]['priorYear'] = $priorYear;
                                $subtotals[$accountType]['actualPY'] += $priorYear;

                                $data[$accountType][$b['accountID']]['variancePY'] = $variance;
                                $data[$accountType][$b['accountID']]['variancePercentagePY'] = variancePercentage($variance, $actual);
                            }
                        }
                    }
                }
            }
        }

        if ($view->items['type'] == 3) {
            $_budget = dbGetBudgetForPeriod($properties, $endDate['year'], $startDate['period'], $periodTo, 'TRACC2', $view->items['ownerID']);
            $budgets = [];
            foreach ($_budget as $b) {
                $budget = $b['budget' . $node];
                $isExpDis = dbCheckAccountIdExpDis($b['accountID']);
                if ($view->items['basis'] == BASIS_CASH && $view->items['showBankBalance'] == 1) {
                    $isExpDis = dbCheckAccountIdExpDisOrBS($b['accountID']);

                    if ($accountType == BALANCESHEET && in_array($b['accountID'], $linkedAccounts)) {
                        continue;
                    }
                }

                if ($showAccountCategories) {

                    $accountCategory = dbGetAccountGroup($b['accountID']);
                    if ($isExpDis) {
                        $actual = $expDisData[$b['accountType']][$accountCategory['accountGroup']][$b['accountID']][0];
                        $variance = $b['accountType'] == EXPENDITURE ? bcsub($budget, $actual, 2) : bcsub($actual, $budget, 2);

                        $expDisData[$b['accountType']][$accountCategory['accountGroup']][$b['accountID']]['ownerAccountCode'] = $b['ownerAccountCode'];
                        $expDisData[$b['accountType']][$accountCategory['accountGroup']][$b['accountID']]['ownerAccountDesc'] = $b['ownerAccountDesc'];
                        $expDisData[$b['accountType']][$accountCategory['accountGroup']][$b['accountID']]['accountGroupName'] = $accountCategory['accountDescription'];
                        $expDisData[$b['accountType']][$accountCategory['accountGroup']][$b['accountID']]['budget'] = $b['budget' . $node];
                        $expDisData[$b['accountType']][$accountCategory['accountGroup']][$b['accountID']]['description'] = $b['accountName'];
                        $expDisData[$b['accountType']][$accountCategory['accountGroup']][$b['accountID']]['accountID'] = $b['accountID'];
                        $subtotals['isExpDis']['budget'] += $budget;
                        $accountCategoriesSubTotals[$accountCategory['accountGroup']]['budget'] += $budget;

                        $expDisData[$b['accountType']][$accountCategory['accountGroup']][$b['accountID']]['variance'] = $variance;
                        $expDisData[$b['accountType']][$accountCategory['accountGroup']][$b['accountID']]['variancePercentage'] = variancePercentage($variance, $budget);
                    } else {
                        $actual = $data[$b['accountType']][$accountCategory['accountGroup']][$b['accountID']][0];
                        $variance = $b['accountType'] == EXPENDITURE ? bcsub($budget, $actual, 2) : bcsub($actual, $budget, 2);

                        $data[$b['accountType']][$accountCategory['accountGroup']][$b['accountID']]['ownerAccountCode'] = $b['ownerAccountCode'];
                        $data[$b['accountType']][$accountCategory['accountGroup']][$b['accountID']]['ownerAccountDesc'] = $b['ownerAccountDesc'];
                        $data[$b['accountType']][$accountCategory['accountGroup']][$b['accountID']]['accountGroupName'] = $accountCategory['accountDescription'];
                        $data[$b['accountType']][$accountCategory['accountGroup']][$b['accountID']]['budget'] = $b['budget' . $node];
                        $data[$b['accountType']][$accountCategory['accountGroup']][$b['accountID']]['description'] = $b['accountName'];
                        $data[$b['accountType']][$accountCategory['accountGroup']][$b['accountID']]['accountID'] = $b['accountID'];
                        $subtotals[$b['accountType']]['budget'] += $budget;
                        $accountCategoriesSubTotals[$accountCategory['accountGroup']]['budget'] += $budget;

                        $data[$b['accountType']][$accountCategory['accountGroup']][$b['accountID']]['variance'] = $variance;
                        $data[$b['accountType']][$accountCategory['accountGroup']][$b['accountID']]['variancePercentage'] = variancePercentage($variance, $budget);
                    }
                } elseif ($isExpDis) {
                    $actual = $expDisData[$b['accountType']][$b['accountID']][0];
                    $variance = $b['accountType'] == EXPENDITURE ? bcsub($budget, $actual, 2) : bcsub($actual, $budget, 2);

                    $expDisData[$b['accountType']][$b['accountID']]['ownerAccountCode'] = $b['ownerAccountCode'];
                    $expDisData[$b['accountType']][$b['accountID']]['ownerAccountDesc'] = $b['ownerAccountDesc'];
                    $expDisData[$b['accountType']][$b['accountID']]['budget'] = $b['budget' . $node];
                    $expDisData[$b['accountType']][$b['accountID']]['description'] = $b['accountName'];
                    $expDisData[$b['accountType']][$b['accountID']]['accountID'] = $b['accountID'];
                    $subtotals['isExpDis']['budget'] += $budget;

                    $expDisData[$b['accountType']][$b['accountID']]['variance'] = $variance;
                    $expDisData[$b['accountType']][$b['accountID']]['variancePercentage'] = variancePercentage($variance, $budget);
                } else {
                    $actual = $data[$b['accountType']][$b['accountID']][0];
                    $variance = $b['accountType'] == EXPENDITURE ? bcsub($budget, $actual, 2) : bcsub($actual, $budget, 2);

                    $data[$b['accountType']][$b['accountID']]['ownerAccountCode'] = $b['ownerAccountCode'];
                    $data[$b['accountType']][$b['accountID']]['ownerAccountDesc'] = $b['ownerAccountDesc'];
                    $data[$b['accountType']][$b['accountID']]['budget'] = $b['budget' . $node];
                    $data[$b['accountType']][$b['accountID']]['description'] = $b['accountName'];
                    $data[$b['accountType']][$b['accountID']]['accountID'] = $b['accountID'];
                    $subtotals[$b['accountType']]['budget'] += $budget;

                    $data[$b['accountType']][$b['accountID']]['variance'] = $variance;
                    $data[$b['accountType']][$b['accountID']]['variancePercentage'] = variancePercentage($variance, $budget);
                }
            }

            $_budgetYTD = dbGetBudgetForYTD($properties, $endDate['year'], $endDate['period'], 'TRACC2', $view->items['ownerID']);
            foreach ($_budgetYTD as $b) {
                $budget = $b['budget' . $node];
                $isExpDis = dbCheckAccountIdExpDis($b['accountID']);
                if ($view->items['basis'] == BASIS_CASH && $view->items['showBankBalance'] == 1) {
                    $isExpDis = dbCheckAccountIdExpDisOrBS($b['accountID']);

                    if ($accountType == BALANCESHEET && in_array($b['accountID'], $linkedAccounts)) {
                        continue;
                    }
                }

                if ($showAccountCategories) {
                    $accountCategory = dbGetAccountGroup($b['accountID']);
                    if ($isExpDis) {
                        $actual = $expDisData[$b['accountType']][$accountCategory['accountGroup']][$b['accountID']]['ytd'];
                        $variance = bcsub($budget, $actual, 2);

                        $expDisData[$b['accountType']][$accountCategory['accountGroup']][$b['accountID']]['ownerAccountCode'] = $b['ownerAccountCode'];
                        $expDisData[$b['accountType']][$accountCategory['accountGroup']][$b['accountID']]['ownerAccountDesc'] = $b['ownerAccountDesc'];
                        $expDisData[$b['accountType']][$accountCategory['accountGroup']][$b['accountID']]['accountGroupName'] = $accountCategory['accountDescription'];
                        $expDisData[$b['accountType']][$accountCategory['accountGroup']][$b['accountID']]['budgetYTD'] = $b['budget' . $node];
                        $expDisData[$b['accountType']][$accountCategory['accountGroup']][$b['accountID']]['description'] = $b['accountName'];
                        $expDisData[$b['accountType']][$accountCategory['accountGroup']][$b['accountID']]['accountID'] = $b['accountID'];
                        $subtotals['isExpDis']['budgetYTD'] += $budget;
                        $accountCategoriesSubTotals[$accountCategory['accountGroup']]['budgetYTD'] += $budget;

                        $expDisData[$b['accountType']][$accountCategory['accountGroup']][$b['accountID']]['varianceYTD'] = $variance;
                        $expDisData[$b['accountType']][$accountCategory['accountGroup']][$b['accountID']]['variancePercentageYTD'] = variancePercentage($variance, $budget);
                    } else {
                        $actual = $data[$b['accountType']][$accountCategory['accountGroup']][$b['accountID']]['ytd'];
                        $variance = bcsub($actual, $budget, 2);

                        $data[$b['accountType']][$accountCategory['accountGroup']][$b['accountID']]['ownerAccountCode'] = $b['ownerAccountCode'];
                        $data[$b['accountType']][$accountCategory['accountGroup']][$b['accountID']]['ownerAccountDesc'] = $b['ownerAccountDesc'];
                        $data[$b['accountType']][$accountCategory['accountGroup']][$b['accountID']]['accountGroupName'] = $accountCategory['accountDescription'];
                        $data[$b['accountType']][$accountCategory['accountGroup']][$b['accountID']]['budgetYTD'] = $b['budget' . $node];
                        $data[$b['accountType']][$accountCategory['accountGroup']][$b['accountID']]['description'] = $b['accountName'];
                        $data[$b['accountType']][$accountCategory['accountGroup']][$b['accountID']]['accountID'] = $b['accountID'];
                        $subtotals[$b['accountType']]['budgetYTD'] += $budget;
                        $accountCategoriesSubTotals[$accountCategory['accountGroup']]['budgetYTD'] += $budget;

                        $data[$b['accountType']][$accountCategory['accountGroup']][$b['accountID']]['varianceYTD'] = $variance;
                        $data[$b['accountType']][$accountCategory['accountGroup']][$b['accountID']]['variancePercentageYTD'] = variancePercentage($variance, $budget);
                    }
                } else {
                    if ($isExpDis) {
                        $actual = $expDisData[$b['accountType']][$b['accountID']]['ytd'];
                        $variance = bcsub($budget, $actual, 2);

                        $expDisData[$b['accountType']][$b['accountID']]['ownerAccountCode'] = $b['ownerAccountCode'];
                        $expDisData[$b['accountType']][$b['accountID']]['ownerAccountDesc'] = $b['ownerAccountDesc'];
                        $expDisData[$b['accountType']][$b['accountID']]['budgetYTD'] = $b['budget' . $node];
                        $expDisData[$b['accountType']][$b['accountID']]['description'] = $b['accountName'];
                        $expDisData[$b['accountType']][$b['accountID']]['accountID'] = $b['accountID'];
                        $subtotals['isExpDis']['budgetYTD'] += $budget;

                        $expDisData[$b['accountType']][$b['accountID']]['varianceYTD'] = $variance;
                        $expDisData[$b['accountType']][$b['accountID']]['variancePercentageYTD'] = variancePercentage($variance, $budget);
                    } else {
                        $actual = $data[$b['accountType']][$b['accountID']]['ytd'];
                        $variance = bcsub($actual, $budget, 2);

                        $data[$b['accountType']][$b['accountID']]['ownerAccountCode'] = $b['ownerAccountCode'];
                        $data[$b['accountType']][$b['accountID']]['ownerAccountDesc'] = $b['ownerAccountDesc'];
                        $data[$b['accountType']][$b['accountID']]['budgetYTD'] = $b['budget' . $node];
                        $data[$b['accountType']][$b['accountID']]['description'] = $b['accountName'];
                        $data[$b['accountType']][$b['accountID']]['accountID'] = $b['accountID'];
                        $subtotals[$b['accountType']]['budgetYTD'] += $budget;

                        $data[$b['accountType']][$b['accountID']]['varianceYTD'] = $variance;
                        $data[$b['accountType']][$b['accountID']]['variancePercentageYTD'] = variancePercentage($variance, $budget);
                    }
                }
            }
        }

        if ($showAccountCategories) {
            if (is_array($data[INCOME])) {
                $computations = withAccountCategoriesComputations($data[INCOME], INCOME);
                $data[INCOME] = $computations['data'];
                $subtotals[INCOME]['variance'] = $computations['subTotals']['variance'];
                $subtotals[INCOME]['variancePercentage'] = $computations['subTotals']['variancePercentage'];
                $subtotals[INCOME]['varianceYTD'] = $computations['subTotals']['varianceYTD'];
                $subtotals[INCOME]['variancePercentageYTD'] = $computations['subTotals']['variancePercentageYTD'];
                $subtotals[INCOME]['variancePYYTD'] = $computations['subTotals']['variancePYYTD'];
                $subtotals[INCOME]['variancePercentagePYYTD'] = $computations['subTotals']['variancePercentagePYYTD'];
                $subtotals[INCOME]['variancePY'] = $computations['subTotals']['variancePY'];
                $subtotals[INCOME]['variancePercentagePY'] = $computations['subTotals']['variancePercentagePY'];

                $accountCategoriesSubTotalsComputations = $computations['subTotalAccountCategory'];
                foreach ($data[INCOME] as $y => $x) {
                    $accountCategoriesSubTotals[$y]['description'] = 'Sub-total';
                    $accountCategoriesSubTotals[$y]['variance'] = $accountCategoriesSubTotalsComputations[$y]['variance'];
                    $accountCategoriesSubTotals[$y]['varianceYTD'] = $accountCategoriesSubTotalsComputations[$y]['varianceYTD'];
                    $accountCategoriesSubTotals[$y]['variancePercentage'] = $accountCategoriesSubTotalsComputations[$y]['variancePercentage'];
                    $accountCategoriesSubTotals[$y]['variancePercentageYTD'] = $accountCategoriesSubTotalsComputations[$y]['variancePercentageYTD'];
                    $accountCategoriesSubTotals[$y]['variancePYYTD'] = $accountCategoriesSubTotalsComputations[$y]['variancePYYTD'];
                    $accountCategoriesSubTotals[$y]['variancePercentagePYYTD'] = $accountCategoriesSubTotalsComputations[$y]['variancePercentagePYYTD'];
                    $accountCategoriesSubTotals[$y]['variancePY'] = $accountCategoriesSubTotalsComputations[$y]['variancePY'];
                    $accountCategoriesSubTotals[$y]['variancePercentagePY'] = $accountCategoriesSubTotalsComputations[$y]['variancePercentagePY'];
                }
            }

            if (is_array($data[EXPENDITURE])) {

                $expenditureComputations = withAccountCategoriesComputations($data[EXPENDITURE], EXPENDITURE);
                $data[EXPENDITURE] = $expenditureComputations['data'];
                $subtotals[EXPENDITURE]['variance'] = $expenditureComputations['subTotals']['variance'];
                $subtotals[EXPENDITURE]['variancePercentage'] = $expenditureComputations['subTotals']['variancePercentage'];
                $subtotals[EXPENDITURE]['varianceYTD'] = $expenditureComputations['subTotals']['varianceYTD'];
                $subtotals[EXPENDITURE]['variancePercentageYTD'] = $expenditureComputations['subTotals']['variancePercentageYTD'];
                $subtotals[EXPENDITURE]['variancePYYTD'] = $expenditureComputations['subTotals']['variancePYYTD'];
                $subtotals[EXPENDITURE]['variancePercentagePYYTD'] = $expenditureComputations['subTotals']['variancePercentagePYYTD'];
                $subtotals[EXPENDITURE]['variancePY'] = $expenditureComputations['subTotals']['variancePY'];
                $subtotals[EXPENDITURE]['variancePercentagePY'] = $expenditureComputations['subTotals']['variancePercentagePY'];

                $accountCategoriesSubTotalsComputations = $expenditureComputations['subTotalAccountCategory'];
                foreach ($data[EXPENDITURE] as $y => $x) {
                    $accountCategoriesSubTotals[$y]['description'] = 'Sub-total';
                    $accountCategoriesSubTotals[$y]['variance'] = $accountCategoriesSubTotalsComputations[$y]['variance'];
                    $accountCategoriesSubTotals[$y]['varianceYTD'] = $accountCategoriesSubTotalsComputations[$y]['varianceYTD'];
                    $accountCategoriesSubTotals[$y]['variancePercentage'] = $accountCategoriesSubTotalsComputations[$y]['variancePercentage'];
                    $accountCategoriesSubTotals[$y]['variancePercentageYTD'] = $accountCategoriesSubTotalsComputations[$y]['variancePercentageYTD'];
                    $accountCategoriesSubTotals[$y]['variancePYYTD'] = $accountCategoriesSubTotalsComputations[$y]['variancePYYTD'];
                    $accountCategoriesSubTotals[$y]['variancePercentagePYYTD'] = $accountCategoriesSubTotalsComputations[$y]['variancePercentagePYYTD'];
                    $accountCategoriesSubTotals[$y]['variancePY'] = $accountCategoriesSubTotalsComputations[$y]['variancePY'];
                    $accountCategoriesSubTotals[$y]['variancePercentagePY'] = $accountCategoriesSubTotalsComputations[$y]['variancePercentagePY'];
                }
            }

            if (is_array($expDisData[INCOME])) {
                $expDisIncomeComputations = withAccountCategoriesComputations($expDisData[INCOME], INCOME);
                $expDisData[INCOME] = $expDisIncomeComputations['data'];
                $subtotals['isExpDis']['variance'] = $expDisIncomeComputations['subTotals']['variance'];
                $subtotals['isExpDis']['variancePercentage'] = $expDisIncomeComputations['subTotals']['variancePercentage'];
                $subtotals['isExpDis']['varianceYTD'] = $expDisIncomeComputations['subTotals']['varianceYTD'];
                $subtotals['isExpDis']['variancePercentageYTD'] = $expDisIncomeComputations['subTotals']['variancePercentageYTD'];
                $subtotals['isExpDis']['variancePYYTD'] = $expDisIncomeComputations['subTotals']['variancePYYTD'];
                $subtotals['isExpDis']['variancePercentagePYYTD'] = $expDisIncomeComputations['subTotals']['variancePercentagePYYTD'];
                $subtotals['isExpDis']['variancePY'] = $expDisIncomeComputations['subTotals']['variancePY'];
                $subtotals['isExpDis']['variancePercentagePY'] = $expDisIncomeComputations['subTotals']['variancePercentagePY'];

                $accountCategoriesSubTotalsComputations = $expDisIncomeComputations['subTotalAccountCategory'];
                foreach ($expDisData[INCOME] as $y => $x) {
                    $accountCategoriesSubTotals[$y]['description'] = 'Sub-total';
                    $accountCategoriesSubTotals[$y]['variance'] = $accountCategoriesSubTotalsComputations[$y]['variance'];
                    $accountCategoriesSubTotals[$y]['varianceYTD'] = $accountCategoriesSubTotalsComputations[$y]['varianceYTD'];
                    $accountCategoriesSubTotals[$y]['variancePercentage'] = $accountCategoriesSubTotalsComputations[$y]['variancePercentage'];
                    $accountCategoriesSubTotals[$y]['variancePercentageYTD'] = $accountCategoriesSubTotalsComputations[$y]['variancePercentageYTD'];
                    $accountCategoriesSubTotals[$y]['variancePYYTD'] = $accountCategoriesSubTotalsComputations[$y]['variancePYYTD'];
                    $accountCategoriesSubTotals[$y]['variancePercentagePYYTD'] = $accountCategoriesSubTotalsComputations[$y]['variancePercentagePYYTD'];
                    $accountCategoriesSubTotals[$y]['variancePY'] = $accountCategoriesSubTotalsComputations[$y]['variancePY'];
                    $accountCategoriesSubTotals[$y]['variancePercentagePY'] = $accountCategoriesSubTotalsComputations[$y]['variancePercentagePY'];
                }
            }

            if (is_array($expDisData[EXPENDITURE])) {
                $expDisExpenditureComputations = withAccountCategoriesComputations($expDisData[EXPENDITURE], BALANCESHEET);
                $expDisData[EXPENDITURE] = $expDisExpenditureComputations['data'];
                $subtotals['isExpDis']['variance'] = $expDisExpenditureComputations['subTotals']['variance'];
                $subtotals['isExpDis']['variancePercentage'] = $expDisExpenditureComputations['subTotals']['variancePercentage'];
                $subtotals['isExpDis']['varianceYTD'] = $expDisExpenditureComputations['subTotals']['varianceYTD'];
                $subtotals['isExpDis']['variancePercentageYTD'] = $expDisExpenditureComputations['subTotals']['variancePercentageYTD'];
                $subtotals['isExpDis']['variancePYYTD'] = $expDisExpenditureComputations['subTotals']['variancePYYTD'];
                $subtotals['isExpDis']['variancePercentagePYYTD'] = $expDisExpenditureComputations['subTotals']['variancePercentagePYYTD'];
                $subtotals['isExpDis']['variancePY'] = $expDisExpenditureComputations['subTotals']['variancePY'];
                $subtotals['isExpDis']['variancePercentagePY'] = $expDisExpenditureComputations['subTotals']['variancePercentagePY'];

                $accountCategoriesSubTotalsComputations = $expDisExpenditureComputations['subTotalAccountCategory'];
                foreach ($expDisData[EXPENDITURE] as $y => $x) {
                    $accountCategoriesSubTotals[$y]['description'] = 'Sub-total';
                    $accountCategoriesSubTotals[$y]['variance'] = $accountCategoriesSubTotalsComputations[$y]['variance'];
                    $accountCategoriesSubTotals[$y]['varianceYTD'] = $accountCategoriesSubTotalsComputations[$y]['varianceYTD'];
                    $accountCategoriesSubTotals[$y]['variancePercentage'] = $accountCategoriesSubTotalsComputations[$y]['variancePercentage'];
                    $accountCategoriesSubTotals[$y]['variancePercentageYTD'] = $accountCategoriesSubTotalsComputations[$y]['variancePercentageYTD'];
                    $accountCategoriesSubTotals[$y]['variancePYYTD'] = $accountCategoriesSubTotalsComputations[$y]['variancePYYTD'];
                    $accountCategoriesSubTotals[$y]['variancePercentagePYYTD'] = $accountCategoriesSubTotalsComputations[$y]['variancePercentagePYYTD'];
                    $accountCategoriesSubTotals[$y]['variancePY'] = $accountCategoriesSubTotalsComputations[$y]['variancePY'];
                    $accountCategoriesSubTotals[$y]['variancePercentagePY'] = $accountCategoriesSubTotalsComputations[$y]['variancePercentagePY'];
                }
            }
        } else {
            if (is_array($data[INCOME])) {
                $computations = withoutAccountCategoriesComputations($data[INCOME], INCOME);
                $data[INCOME] = $computations['data'];

                $subtotals[INCOME]['variance'] = variance($subtotals[INCOME][0], $subtotals[INCOME]['budget']);
                $subtotals[INCOME]['variancePercentage'] = variancePercentage($subtotals[INCOME]['variance'], $subtotals[INCOME]['budget']);
                $subtotals[INCOME]['varianceYTD'] = variance($subtotals[INCOME]['ytd'], $subtotals[INCOME]['budgetYTD']);
                $subtotals[INCOME]['variancePercentageYTD'] = variancePercentage($subtotals[INCOME]['varianceYTD'], $subtotals[INCOME]['budgetYTD']);
                $subtotals[INCOME]['variancePYYTD'] = variance($subtotals[INCOME]['ytd'], $subtotals[INCOME]['priorYear']);
                $subtotals[INCOME]['variancePercentagePYYTD'] = variancePercentage($subtotals[INCOME]['variancePYYTD'], $subtotals[INCOME]['ytd']);
                $subtotals[INCOME]['variancePY'] = variance($subtotals[INCOME][0], $subtotals[INCOME]['actualPY']);
                $subtotals[INCOME]['variancePercentagePY'] = variancePercentage($subtotals[INCOME]['variancePY'], $subtotals[INCOME][0]);
            }

            if (is_array($data[EXPENDITURE])) {
                $expenditureComputations = withoutAccountCategoriesComputations($data[EXPENDITURE], EXPENDITURE);
                $data[EXPENDITURE] = $expenditureComputations['data'];

                $subtotals[EXPENDITURE]['variance'] = variance($subtotals[EXPENDITURE]['budget'], $subtotals[EXPENDITURE][0]);
                $subtotals[EXPENDITURE]['variancePercentage'] = variancePercentage($subtotals[EXPENDITURE]['variance'], $subtotals[EXPENDITURE]['budget']);
                $subtotals[EXPENDITURE]['varianceYTD'] = variance($subtotals[EXPENDITURE]['budgetYTD'], $subtotals[EXPENDITURE]['ytd']);
                $subtotals[EXPENDITURE]['variancePercentageYTD'] = variancePercentage($subtotals[EXPENDITURE]['varianceYTD'], $subtotals[EXPENDITURE]['budgetYTD']);
                $subtotals[EXPENDITURE]['variancePYYTD'] = variance($subtotals[EXPENDITURE]['ytd'], $subtotals[EXPENDITURE]['priorYear']);
                $subtotals[EXPENDITURE]['variancePercentagePYYTD'] = variancePercentage($subtotals[EXPENDITURE]['variancePYYTD'], $subtotals[EXPENDITURE]['ytd']);
                $subtotals[EXPENDITURE]['variancePY'] = variance($subtotals[EXPENDITURE][0], $subtotals[EXPENDITURE]['actualPY']);
                $subtotals[EXPENDITURE]['variancePercentagePY'] = variancePercentage($subtotals[EXPENDITURE]['variancePY'], $subtotals[EXPENDITURE][0]);

            }

            if (is_array($expDisData[INCOME])) {
                $expDisIncomeComputations = withoutAccountCategoriesComputations($expDisData[INCOME], INCOME);
                $expDisData[INCOME] = $expDisIncomeComputations['data'];

                $subtotals['isExpDis']['variance'] = variance($subtotals['isExpDis'][0], $subtotals['isExpDis']['budget']);
                $subtotals['isExpDis']['variancePercentage'] = variancePercentage($subtotals['isExpDis']['variance'], $subtotals['isExpDis']['budget']);
                $subtotals['isExpDis']['varianceYTD'] = variance($subtotals['isExpDis']['ytd'], $subtotals['isExpDis']['budgetYTD']);
                $subtotals['isExpDis']['variancePercentageYTD'] = variancePercentage($subtotals['isExpDis']['varianceYTD'], $subtotals['isExpDis']['budgetYTD']);
                $subtotals['isExpDis']['variancePYYTD'] = variance($subtotals['isExpDis']['ytd'], $subtotals['isExpDis']['priorYear']);
                $subtotals['isExpDis']['variancePercentagePYYTD'] = variancePercentage($subtotals['isExpDis']['variancePYYTD'], $subtotals['isExpDis']['ytd']);
                $subtotals['isExpDis']['variancePY'] = variance($subtotals['isExpDis'][0], $subtotals['isExpDis']['actualPY']);
                $subtotals['isExpDis']['variancePercentagePY'] = variancePercentage($subtotals['isExpDis']['variancePY'], $subtotals['isExpDis'][0]);
            }

            if (is_array($expDisData[EXPENDITURE])) {
                $expDisExpenditureComputations = withoutAccountCategoriesComputations($expDisData[EXPENDITURE], BALANCESHEET);
                $expDisData[EXPENDITURE] = $expDisExpenditureComputations['data'];

                $subtotals['isExpDis']['variance'] = variance($subtotals['isExpDis'][0], $subtotals['isExpDis']['budget']);
                $subtotals['isExpDis']['variancePercentage'] = variancePercentage($subtotals['isExpDis']['variance'], $subtotals['isExpDis']['budget']);
                $subtotals['isExpDis']['varianceYTD'] = variance($subtotals['isExpDis']['ytd'], $subtotals['isExpDis']['budgetYTD']);
                $subtotals['isExpDis']['variancePercentageYTD'] = variancePercentage($subtotals['isExpDis']['varianceYTD'], $subtotals['isExpDis']['budgetYTD']);
                $subtotals['isExpDis']['variancePYYTD'] = variance($subtotals['isExpDis']['ytd'], $subtotals['isExpDis']['priorYTD']);
                $subtotals['isExpDis']['variancePercentagePYYTD'] = variancePercentage($subtotals['isExpDis']['variancePYYTD'], $subtotals['isExpDis']['ytd']);
                $subtotals['isExpDis']['variancePY'] = variance($subtotals['isExpDis'][0], $subtotals['isExpDis']['actualPY']);
                $subtotals['isExpDis']['variancePercentagePY'] = variancePercentage($subtotals['isExpDis']['variancePY'], $subtotals['isExpDis'][0]);
            }
        }


        if ($showAccountCategories) {
            if (! $showAccounts) {
                function cmp($a, $b)
                {
                    return strcmp($a['description'], $b['description']);
                }
                if (isset($data[INCOME])) {
                    foreach ($data[INCOME] as $y => $i) {
                        usort($data[INCOME][$y], 'cmp');
                    }
                }
                if (isset($data[EXPENDITURE])) {
                    foreach ($data[EXPENDITURE] as $y => $i) {
                        usort($data[EXPENDITURE][$y], 'cmp');
                    }
                }
                if (isset($expDisData[INCOME])) {
                    foreach ($expDisData[INCOME] as $y => $i) {
                        usort($expDisData[INCOME][$y], 'cmp');
                    }
                }
                if (isset($expDisData[EXPENDITURE])) {
                    foreach ($expDisData[EXPENDITURE] as $y => $i) {
                        usort($expDisData[EXPENDITURE][$y], 'cmp');
                    }
                }
            } else {
                if (is_array($data[INCOME])) {
                    foreach ($data[INCOME] as $y => $i) {
                        ksort($data[INCOME][$y]);
                    }
                }
                if (is_array($data[EXPENDITURE])) {
                    foreach ($data[EXPENDITURE] as $y => $i) {
                        ksort($data[EXPENDITURE][$y]);
                    }
                }
                if (is_array($expDisData[INCOME])) {
                    foreach ($expDisData[INCOME] as $y => $i) {
                        ksort($expDisData[INCOME][$y]);
                    }
                }
                if (is_array($expDisData[EXPENDITURE])) {
                    foreach ($expDisData[EXPENDITURE] as $y => $i) {
                        ksort($expDisData[EXPENDITURE][$y]);
                    }
                }
            }
        } else {
            if (! $showAccounts) {
                function cmp($a, $b)
                {
                    return strcmp($a['description'], $b['description']);
                }
                if (isset($data[INCOME])) {
                    usort($data[INCOME], 'cmp');
                }
                if (isset($data[EXPENDITURE])) {
                    usort($data[EXPENDITURE], 'cmp');
                }
                if (isset($expDisData[INCOME])) {
                    usort($expDisData[INCOME], 'cmp');
                }
                if (isset($expDisData[EXPENDITURE])) {
                    usort($expDisData[EXPENDITURE], 'cmp');
                }
            } else {
                if (is_array($data[INCOME])) {
                    ksort($data[INCOME]);
                }
                if (is_array($data[EXPENDITURE])) {
                    ksort($data[EXPENDITURE]);
                }
                if (is_array($expDisData[INCOME])) {
                    ksort($expDisData[INCOME]);
                }
                if (is_array($expDisData[EXPENDITURE])) {
                    ksort($expDisData[EXPENDITURE]);
                }
            }
        }

        if (isset($subtotals[EXPENDITURE])) {
            // var_dump($subtotals[EXPENDITURE]);
            $subtotals[EXPENDITURE]['variancePY'] = $subtotals[EXPENDITURE][0] - $subtotals[EXPENDITURE]['actualPY'];
            $subtotals[EXPENDITURE]['variancePYYTD'] = $subtotals[EXPENDITURE]['ytd'] - $subtotals[EXPENDITURE]['priorYTD'];
            $subtotals[EXPENDITURE]['variancePercentagePYYTD'] = variancePercentage($subtotals[EXPENDITURE]['variancePYYTD'], $subtotals[EXPENDITURE]['ytd']);
        }

        if (isset($subtotals[INCOME])) {
            $subtotals[INCOME]['variancePY'] = $subtotals[INCOME][0] - $subtotals[INCOME]['actualPY'];
            $subtotals[INCOME]['variancePYYTD'] = $subtotals[INCOME]['ytd'] - $subtotals[INCOME]['priorYTD'];
            $subtotals[INCOME]['variancePercentagePYYTD'] = variancePercentage($subtotals[INCOME]['variancePYYTD'], $subtotals[INCOME]['ytd']);
        }

        $plTotal = $subtotals[INCOME];
        $netProfitTotal = $subtotals[INCOME];
        foreach ($subtotals[EXPENDITURE] as $a => $e) {
            if ($a !== 'variance' && $a !== 'varianceYTD') {
                $plTotal[$a] = number_format((float) $plTotal[$a] - (float) $e, 2, '.', '');
                $netProfitTotal[$a] = number_format((float) $netProfitTotal[$a] - (float) $e, 2, '.', '');
            } else {
                $plTotal[$a] = bcadd($plTotal[$a], $e, 2);
                $netProfitTotal[$a] = bcadd($netProfitTotal[$a], $e, 2);
            }
        }

        foreach ($subtotals['isExpDis'] as $a => $e) {
            if (is_numeric($e)) {
                $plTotal[$a] = bcsub($plTotal[$a], $e, 2);
            }
        }

        $plTotal['variance'] = $plTotal[0] - $plTotal['budget'];
        $plTotal['varianceYTD'] = $plTotal['ytd'] - $plTotal['budgetYTD'];
        $plTotal['variancePY'] = $plTotal[0] - $plTotal['actualPY'];
        $plTotal['variancePYYTD'] = $plTotal['ytd'] - $plTotal['priorYTD'];
        $plTotal['variancePercentage'] = variancePercentage($plTotal['variance'], $plTotal['budget']);
        $plTotal['variancePercentageYTD'] = variancePercentage($plTotal['varianceYTD'], $plTotal['budgetYTD']);
        $plTotal['variancePercentagePYYTD'] = variancePercentage($plTotal['variancePYYTD'], $plTotal['ytd']);
        $plTotal['variancePercentagePY'] = variancePercentage($plTotal['variancePY'], $plTotal[0]);

        $netProfitTotal['variance'] = $netProfitTotal[0] - $netProfitTotal['budget'];
        $netProfitTotal['varianceYTD'] = $netProfitTotal['ytd'] - $netProfitTotal['budgetYTD'];
        $netProfitTotal['variancePY'] = $netProfitTotal[0] - $netProfitTotal['actualPY'];
        $netProfitTotal['variancePYYTD'] = $netProfitTotal['ytd'] - $netProfitTotal['priorYTD'];
        $netProfitTotal['variancePercentage'] = variancePercentage($netProfitTotal['variance'], $netProfitTotal['budget']);
        $netProfitTotal['variancePercentageYTD'] = variancePercentage($netProfitTotal['varianceYTD'], $netProfitTotal['budgetYTD']);
        $netProfitTotal['variancePercentagePYYTD'] = variancePercentage($netProfitTotal['variancePYYTD'], $netProfitTotal['ytd']);
        $netProfitTotal['variancePercentagePY'] = variancePercentage($netProfitTotal['variancePY'], $netProfitTotal[0]);

        $plTotal['description'] = 'Net profit after distributions';
        $netProfitTotal['description'] = 'Net profit before distributions';

        // cashflow

        if ($view->items['type'] <= 2 || $view->items['type'] == 5) {
            $periodTo = null;
        }
        $year = $endDate['year'];

        //        $otherRemittanceAndBSMovements = array();
        if ($view->items['basis'] == BASIS_CASH && $view->items['showBankBalance'] == 1) {
            $otherAccounts = []; // getOwnerRemittanceAndBalanceSheetAccounts('EXP.DIS');

            $plTotal['description'] = 'Net Cash';
            $netProfitTotal['description'] = 'Net cash before distributions & ' . $_SESSION['country_default']['tax_label'];
            $linkedAccounts = dbGetLinkedAccounts();

            $gstReceived['description'] = $_SESSION['country_default']['tax_label'] . ' Received';
            $gstReceivedYtd = dbGetTrialBalanceYTDByAccount($properties, $linkedAccounts['gstOutputTax'], $endDate['period'], $endDate['year']);
            $gstReceived['ytd'] = $gstReceivedYtd['balanceCash'] * -1;
            $gstReceived['variancePercentageYTD'] = 100;
            $gstReceived['varianceYTD'] = $gstReceived['ytd'];

            $gstPaid['description'] = $_SESSION['country_default']['tax_label'] . ' Paid';
            $gstPaidYtd = dbGetTrialBalanceYTDByAccount($properties, $linkedAccounts['gstInputTax'], $endDate['period'], $endDate['year']);
            $gstPaid['ytd'] = $gstPaidYtd['balanceCash'] * -1;
            $gstPaid['variancePercentageYTD'] = 100;
            $gstPaid['varianceYTD'] = $gstPaid['ytd'];

            $gstSubTotal['description'] = 'Net ' . $_SESSION['country_default']['tax_label'] . ' Received / Paid';
            $gstSubTotal['ytd'] = $gstReceived['ytd'] + $gstPaid['ytd'];
            $gstSubTotal['variancePercentageYTD'] = 100;
            $gstSubTotal['varianceYTD'] = $gstSubTotal['ytd'];

            $netProfitTotal2 = $netProfitTotal;
            $netProfitTotal2['description'] = 'Net cash before distributions';
            $netProfitTotal2['ytd'] = $netProfitTotal['ytd'] + $gstSubTotal['ytd'];

            $otherRemittanceAndBSMovements['description'] = 'Balance Sheet Movements';
            $otherRemittanceAndBSMovementsYtd = dbGetTrialBalanceYTDByAccounts($properties, $otherAccounts, $endDate['period'], $endDate['year']);
            $otherRemittanceAndBSMovements['ytd'] = $otherRemittanceAndBSMovementsYtd['balanceCash'] * -1;

            $gstPaid['description'] = $_SESSION['country_default']['tax_label'] . ' Paid';
            $gstPaidYtd = dbGetTrialBalanceYTDByAccount($properties, $linkedAccounts['gstInputTax'], $endDate['period'], $endDate['year']);
            $gstPaid['ytd'] = $gstPaidYtd['balanceCash'] * -1;

            $plTotal['ytd'] = $netProfitTotal2['ytd'] - $subtotals['isExpDis']['ytd'];

            $cashOpening['description'] = 'Opening Cash Balance';
            $cashOpeningYtd = dbGetOpeningTrialBalanceByAccount($properties, $linkedAccounts['bank'], 1, $year);

            $cashOpening['ytd'] = $cashOpeningYtd['balanceCash'];


            $cashClosing['description'] = 'Closing Cash Balance';
            $cashClosing['ytd'] = $cashOpening['ytd'] + $plTotal['ytd'] + $otherRemittanceAndBSMovements['ytd'];

            // get amounts per period
            // reset periods
            $period = ($view->items['type'] == 7 || $view->items['type'] == 6 || $view->items['type'] == 2 || $view->items['type'] == 5) ? 12 : $startDate['period'];

            if ($view->items['type'] == 1 || $view->items['type'] == 8 || $view->items['type'] == 9) {
                $period = $endDate['period'];
            }

            if ($view->items['type'] <= 2 || $view->items['type'] == 5) {
                $periodTo = null;
            }
            $year = $endDate['year'];

            if ($view->items['type'] == 1) {

                for ($i = 1; $i <= $_periods; $i++) {
                    $gstReceivedYtd = dbGetTrialBalanceForPeriodByAccount($properties, $linkedAccounts['gstOutputTax'], $period, $year, $periodTo);
                    $gstReceived[$i] = $gstReceivedYtd['balanceCash'] * -1;
                    $gstReceived['variance' . $i] = $gstReceivedYtd['balanceCash'] * -1;

                    $gstPaidYtd = dbGetTrialBalanceForPeriodByAccount($properties, $linkedAccounts['gstInputTax'], $period, $year, $periodTo);
                    $gstPaid[$i] = $gstPaidYtd['balanceCash'] * -1;
                    $gstPaid['variance' . $i] = $gstPaidYtd['balanceCash'] * -1;

                    $otherRemittanceAndBSMovementsP = dbGetTrialBalanceForPeriodByAccounts($properties, $otherAccounts, $period, $year, $periodTo);
                    $otherRemittanceAndBSMovements[$i] = $otherRemittanceAndBSMovementsP['balanceCash'] * -1;

                    $gstSubTotal[$i] = ($gstReceived[$i] + $gstPaid[$i]);
                    $gstSubTotal['variance' . $i] = $gstSubTotal[$i];

                    $netProfitTotal2[$i] = $netProfitTotal[$i] + $gstSubTotal[$i];

                    $plTotal[$i] = $netProfitTotal2[$i] - $subtotals['isExpDis'][$i];

                    $cashOpeningYtd = dbGetOpeningTrialBalanceByAccount($properties, $linkedAccounts['bank'], $period, $year);
                    $cashOpening[$i] = $cashOpeningYtd['balanceCash'];

                    $cashClosing[$i] = $cashOpening[$i] + $plTotal[$i] + $otherRemittanceAndBSMovements[$i];

                    [$period, $year] = periodBefore($period, $year);
                }

            } else {

                for ($i = 0; $i < $_periods; $i++) {
                    if ($view->items['type'] == 6 || $view->items['type'] == 7) {
                        $periodTo = $period - 2;
                        $oldPeriod = $period;
                        $period = $periodTo;
                        $periodTo = $oldPeriod;
                    }

                    $gstReceivedYtd = dbGetTrialBalanceForPeriodByAccount($properties, $linkedAccounts['gstOutputTax'], $period, $year, $periodTo);
                    $gstReceived[$i] = $gstReceivedYtd['balanceCash'] * -1;
                    $gstReceived['variance' . $i] = $gstReceivedYtd['balanceCash'] * -1;

                    $gstPaidYtd = dbGetTrialBalanceForPeriodByAccount($properties, $linkedAccounts['gstInputTax'], $period, $year, $periodTo);
                    $gstPaid[$i] = $gstPaidYtd['balanceCash'] * -1;
                    $gstPaid['variance' . $i] = $gstPaidYtd['balanceCash'] * -1;

                    $otherRemittanceAndBSMovementsP = dbGetTrialBalanceForPeriodByAccounts($properties, $otherAccounts, $period, $year, $periodTo);
                    $otherRemittanceAndBSMovements[$i] = $otherRemittanceAndBSMovementsP['balanceCash'] * -1;

                    $gstSubTotal[$i] = ($gstReceived[$i] + $gstPaid[$i]);
                    $gstSubTotal['variance' . $i] = $gstSubTotal[$i];

                    $netProfitTotal2[$i] = $netProfitTotal[$i] + $gstSubTotal[$i];

                    $plTotal[$i] = $netProfitTotal2[$i] - $subtotals['isExpDis'][$i];

                    $cashOpeningYtd = dbGetOpeningTrialBalanceByAccount($properties, $linkedAccounts['bank'], $period, $year);
                    $cashOpening[$i] = $cashOpeningYtd['balanceCash'];

                    $cashClosing[$i] = $cashOpening[$i] + $plTotal[$i] + $otherRemittanceAndBSMovements[$i];

                    [$period, $year] = periodBefore($period, $year);
                }

            }
            // end of amounts per period

            // get amounts for budget
            if ($_budgetYTD && count($_budgetYTD) > 0) {
                $gstReceivedBudgetYtdIndex = array_search($linkedAccounts['gstOutpuTax'], array_column($_budgetYTD, 'accountID'), true);
                $gstPaidBudgetYtdIndex = array_search($linkedAccounts['gstInputTax'], array_column($_budgetYTD, 'accountID'), true);
            }

            if ($gstReceivedBudgetYtdIndex !== false) {
                $gstReceived['budgetYTD'] = $_budgetYTD[$gstReceivedBudgetYtdIndex]['budgetCash'];
                $gstReceived['varianceYTD'] = variance($gstReceived[0], $gstReceived['budgetYTD']);
                $gstReceived['variancePercentageYTD'] = variancePercentage($gstReceived['varianceYTD'], $gstReceived['budgetYTD']);
            }
            if ($_budget && count($_budget) > 0) {
                $gstReceivedBudgetIndex = array_search($linkedAccounts['gstOutpuTax'], array_column($_budget, 'accountID'), true);
                $gstPaidBudgetIndex = array_search($linkedAccounts['gstInputTax'], array_column($_budget, 'accountID'), true);
            }

            if ($gstReceivedBudgetIndex !== false) {
                $gstReceived['budget'] = $_budget[$gstReceivedBudgetIndex]['budgetCash'];
                $gstReceived['variance'] = variance($gstReceived[0], $gstReceived['budget']);
                $gstReceived['variancePercentage'] = variancePercentage($gstReceived['variance'], $gstReceived['budget']);
            }

            if ($gstPaidBudgetYtdIndex !== false) {
                $gstPaid['budgetYTD'] = $_budgetYTD[$gstPaidBudgetYtdIndex]['budgetCash'];
                $gstPaid['varianceYTD'] = variance($gstPaid[0], $gstPaid['budgetYTD']);
                $gstPaid['variancePercentageYTD'] = variancePercentage($gstPaid['varianceYTD'], $gstPaid['budgetYTD']);
            }

            if ($gstPaidBudgetIndex !== false) {
                $gstPaid['budget'] = $_budget[$gstPaidBudgetIndex]['budgetCash'];
                $gstPaid['variance'] = variance($gstPaid[0], $gstPaid['budget']);
                $gstPaid['variancePercentage'] = variancePercentage($gstPaid['variance'], $gstPaid['budget']);
            }

            // end of amounts for budget

            // get amounts for prior year
            $period = ($view->items['type'] == 2 || $view->items['type'] == 5) ? 12 : $startDate['period'];

            if ($view->items['type'] == 1 || $view->items['type'] == 8 || $view->items['type'] == 9) {
                $period = $endDate['period'];
            }

            if ($view->items['type'] <= 2 || $view->items['type'] == 5) {
                $periodTo = null;
            }
            $year = $endDate['year'];

            $gstReceivedActualPY = dbGetTrialBalanceForPeriodByAccount($properties, $linkedAccounts['gstOutputTax'], $period, $year - 1, $periodTo);
            $gstReceivedYtdPY = dbGetTrialBalanceYTDByAccount($properties, $linkedAccounts['gstOutputTax'], $endDate['period'], $endDate['year'] - 1);
            $gstReceived['actualPY'] = $gstReceivedActualPY['balanceCash'] * -1;
            $gstReceived['priorYTD'] = $gstReceivedYtdPY['balanceCash'] * -1;
            $gstReceived['variancePY'] = variance($gstReceived[0], $gstReceived['actualPY']);
            $gstReceived['variancePercentagePY'] = variancePercentage($gstReceived['variancePY'], $gstReceived[0]);
            $gstReceived['variancePYYTD'] = variance($gstReceived['ytd'], $gstReceived['priorYTD']);
            $gstReceived['variancePercentagePYYTD'] = variancePercentage($gstReceived['variancePYYTD'], $gstReceived['ytd']);
            $gstReceived['variancePercentage'] = 100;
            $gstReceived['variance'] = $gstReceived[0];

            $gstPaidActualPY = dbGetTrialBalanceForPeriodByAccount($properties, $linkedAccounts['gstInputTax'], $period, $year - 1, $periodTo);
            $gstPaidYtdPY = dbGetTrialBalanceYTDByAccount($properties, $linkedAccounts['gstInputTax'], $endDate['period'], $endDate['year'] - 1);
            $gstPaid['actualPY'] = $gstPaidActualPY['balanceCash'] * -1;
            $gstPaid['priorYTD'] = $gstPaidYtdPY['balanceCash'] * -1;
            $gstPaid['variancePY'] = variance($gstPaid[0], $gstPaid['actualPY']);
            $gstPaid['variancePercentagePY'] = variancePercentage($gstPaid['variancePY'], $gstPaid[0]);
            $gstPaid['variancePYYTD'] = variance($gstPaid['ytd'], $gstPaid['priorYTD']);
            $gstPaid['variancePercentagePYYTD'] = variancePercentage($gstPaid['variancePYYTD'], $gstPaid['ytd']);
            $gstPaid['variancePercentage'] = 100;
            $gstPaid['variance'] = $gstPaid[0];

            $gstSubTotal['actualPY'] = $gstReceived['actualPY'] + $gstPaid['actualPY'];
            $gstSubTotal['priorYTD'] = $gstReceived['priorYTD'] + $gstPaid['priorYTD'];
            $gstSubTotal['variancePY'] = variance($gstSubTotal[0], $gstSubTotal['actualPY']);
            $gstSubTotal['variancePercentagePY'] = variancePercentage($gstSubTotal['variancePY'], $gstSubTotal[0]);
            $gstSubTotal['variancePYYTD'] = variance($gstSubTotal['ytd'], $gstSubTotal['priorYTD']);
            $gstSubTotal['variancePercentagePYYTD'] = variancePercentage($gstSubTotal['variancePYYTD'], $gstSubTotal['ytd']);
            $gstSubTotal['variancePercentage'] = 100;
            $gstSubTotal['variance'] = $gstSubTotal[0];

            $netProfitTotal2['actualPY'] = $netProfitTotal['actualPY'] + $gstSubTotal['actualPY'];
            $netProfitTotal2['priorYTD'] = $netProfitTotal['priorYTD'] + $gstSubTotal['priorYTD'];
            $netProfitTotal2['variancePY'] = variance($netProfitTotal2[0], $netProfitTotal2['actualPY']);
            $netProfitTotal2['variancePercentagePY'] = variancePercentage($netProfitTotal2['variancePY'], $netProfitTotal2[0]);
            $netProfitTotal2['variancePYYTD'] = variance($netProfitTotal2['ytd'], $netProfitTotal2['priorYTD']);
            $netProfitTotal2['variancePercentagePYYTD'] = variancePercentage($netProfitTotal2['variancePYYTD'], $netProfitTotal2['ytd']);

            $otherRemittanceAndBSMovementsActualPY = dbGetTrialBalanceForPeriodByAccounts($properties, $otherAccounts, $period, $year - 1, $periodTo);
            $otherRemittanceAndBSMovementsYtdPY = dbGetTrialBalanceYTDByAccounts($properties, $otherAccounts, $endDate['period'], $endDate['year'] - 1);
            $otherRemittanceAndBSMovements['actualPY'] = $otherRemittanceAndBSMovementsActualPY['balanceCash'] * -1;
            $otherRemittanceAndBSMovements['priorYTD'] = $otherRemittanceAndBSMovementsYtdPY['balanceCash'] * -1;
            $otherRemittanceAndBSMovements['variancePY'] = variance($otherRemittanceAndBSMovements[0], $otherRemittanceAndBSMovements['actualPY']);
            $otherRemittanceAndBSMovements['variancePercentagePY'] = variancePercentage($otherRemittanceAndBSMovements['variancePY'], $otherRemittanceAndBSMovements[0]);
            $otherRemittanceAndBSMovements['variancePYYTD'] = variance($otherRemittanceAndBSMovements['ytd'], $otherRemittanceAndBSMovements['priorYTD']);
            $otherRemittanceAndBSMovements['variancePercentagePYYTD'] = variancePercentage($otherRemittanceAndBSMovements['variancePYYTD'], $otherRemittanceAndBSMovements['ytd']);

            $plTotal['actualPY'] = $netProfitTotal2['actualPY'] - $subtotals['isExpDis']['actualPY'];
            $plTotal['priorYTD'] = $netProfitTotal2['priorYTD'] - $subtotals['isExpDis']['priorYTD'];
            $plTotal['variancePY'] = variance($plTotal[0], $plTotal['actualPY']);
            $plTotal['variancePercentagePY'] = variancePercentage($plTotal['variancePY'], $plTotal[0]);
            $plTotal['variancePYYTD'] = variance($plTotal['ytd'], $plTotal['priorYTD']);
            $plTotal['variancePercentagePYYTD'] = variancePercentage($plTotal['variancePYYTD'], $plTotal['ytd']);

            $cashOpeningActualPY = dbGetOpeningTrialBalanceByAccount($properties, $linkedAccounts['bank'], $period, $year - 1);
            $cashOpeningYtdPY = dbGetOpeningTrialBalanceByAccount($properties, $linkedAccounts['bank'], 1, $year - 1);
            $cashOpening['actualPY'] = $cashOpeningActualPY['balanceCash'];
            $cashOpening['priorYTD'] = $cashOpeningYtdPY['balanceCash'];
            $cashOpening['variancePY'] = variance($cashOpening[0], $cashOpening['actualPY']);
            $cashOpening['variancePercentagePY'] = variancePercentage($cashOpening['variancePY'], $cashOpening[0]);
            $cashOpening['variancePYYTD'] = variance($cashOpening['ytd'], $cashOpening['priorYTD']);
            $cashOpening['variancePercentagePYYTD'] = variancePercentage($cashOpening['variancePYYTD'], $cashOpening['ytd']);

            $cashClosing['actualPY'] = $cashOpening['actualPY'] + $plTotal['actualPY'];
            $cashClosing['priorYTD'] = $cashOpening['priorYTD'] + $plTotal['priorYTD'];
            $cashClosing['variancePY'] = variance($cashClosing[0], $cashClosing['actualPY']);
            $cashClosing['variancePercentagePY'] = variancePercentage($cashClosing['variancePY'], $cashClosing[0]);
            $cashClosing['variancePYYTD'] = variance($cashClosing['ytd'], $cashClosing['priorYTD']);
            $cashClosing['variancePercentagePYYTD'] = variancePercentage($cashClosing['variancePYYTD'], $cashClosing['ytd']);

            // end of amounts for prior year

        }

        if ($format != FILETYPE_SCREEN) {
            if (! $context[DOC_MASTER]) {
                if (! $context['forOwnerReport'] || ($context['forOwnerReport'] && $context['logo'])) {
                    $logoFile = dbGetClientLogo();
                    $logoPath = "assets/clientLogos/{$logoFile}";
                }

                $_filePath =  "{$pathPrefix}{$clientDirectory}/{$format}/" . DOC_TRIALBALANCE . '/';
                $_downloadPath =  "{$clientDirectory}/{$format}/" . DOC_TRIALBALANCE;
                if (count($properties ?? []) < 2) {
                    $file = 'ProfitLoss_' . $properties[0] . '_' . date('dmYHis') . rand() . ".{$format}";
                } else {
                    $file = 'ProfitLoss_Multiple_' . date('dmYHis') . ".{$format}";
                }
                $filePath = $_filePath . $file;
                $downloadPath = "{$_downloadPath}/{$file}";

                $context['MultixlsDownloadPath'] = [$filePath, $file];
            }

            $includes = dbGetSubReports($view->items['reportID'], true);

            foreach ($includes as $i) {
                include $i['subReportFile'];
            }

            // -- if it s a scheduled task and not part of a bigger report - attach the report and email to the requester
            if (($context[IS_TASK]) && (! $context[DOC_MASTER]) && ! $context['forOwnerReport']) {
                $email = new EmailTemplate('views/emails/basicEmail.html', SYSTEMURL);
                $attachment =  [];
                $attachment[0]['file'] = $filePath;
                $attachment[0]['content_type'] = 'application/pdf';
                sendMail($_SESSION['email'], $_SESSION['first_name'] . ' ' . $_SESSION['last_name'], $email->toString(), 'Report', $attachment);
                logData('Emailed report to ' . $_SESSION['email']);
                $context[TASK_COMPLETE] = true;
            }
        }
    }

    if ($context['forOwnerReport']) {
        if ($format == FILETYPE_XLS) {
            renderDownloadLink($downloadPath);
        } else {
            $context['ownerReportFile'] = $filePath;
        }

        return false;
    }


    if ($format != FILETYPE_SCREEN) {
        // -- if the document doesnt form part of a bigger report
        if (! $context[DOC_MASTER]) {
            if ($count > THRESHOLD_TRIALBALANCE) {
                $view->items['statusMessage'] = 'Due to its complexity, this report has been scheduled and will be emailed to you on completion!';
                $view->render();
            } else {
                $view->items['downloadPath'] = $downloadPath;
                if ($_SESSION['user_type'] == USER_OWNER) {
                    $_SESSION['downloadFile'] = $downloadPath;
                }
                $view->render();
            }
        }

    } else {
        //  var_dump($subtotals['isExpDis']);
        $view->items['properties'] = $properties;
        $view->items['typeList'] =  [1 => 'Selected and Previous 3 Months',  2 => 'Current financial year', 3 => 'Actual vs Budget', 4 => 'Actual vs Prior Year'];
        if ($type == 6 || $type == 7 || $type == 2 || $type == 5) {
            $view->items['periods'] = array_reverse($periods, true);
        } else {
            $view->items['periods'] = $periods;
        }
        $view->items['periodLookup'] = $periodLookup;
        $view->items['data'] = $data;
        $view->items['subtotals'] = $subtotals;
        $view->items['total'] = $plTotal;

        $view->items['gstPaid'] = $gstPaid;
        $view->items['linkedAccounts'] = $linkedAccounts;
        $view->items['gstReceived'] = $gstReceived;
        $view->items['gstSubTotal'] = $gstSubTotal;
        $view->items['cashOpening'] = $cashOpening;
        $view->items['cashClosing'] = $cashClosing;
        $view->items['netProfitTotal2'] = $netProfitTotal2;

        $view->items['accountGroups'] = $accountGroups;
        $view->items['accountCategories'] = $accountCategories;
        $view->items['expDisAccounts'] = $expDisData;
        $view->items['netProfitTotal'] = $netProfitTotal;
        $view->items['otherRemittanceAndBSMovements'] = $otherRemittanceAndBSMovements;

        $view->items['lastYear'] = $lastYear;
        $view->items['periodFrom'] = $startDate['period'];
        $view->items['periodTo'] = $periodTo;
        $view->items['accountCategoriesSubTotals'] = $accountCategoriesSubTotals;
        $view->render();

    }
}

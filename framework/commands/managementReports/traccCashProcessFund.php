<?php

global $_fonts, $pdf;

/* INCOME - Current */

$income = new StdClass();

$o = dbTotalIncomePerGroupExFund($propertyID, $periodFrom, $periodTo, 'INCOWN');
$income->owner = $o['net_amount'];

$o = dbTotalIncomePerGroupExFund($propertyID, $periodFrom, $periodTo, 'INCVO');
$income->variableOutgoing = $o['net_amount'];

$o = dbTotalIncomePerGroupExFund($propertyID, $periodFrom, $periodTo, 'INCDR');
$income->recoverable = $o['net_amount'];

$income->total = $income->owner + $income->variableOutgoing + $income->recoverable;

/* INCOME - YTD */

$incomeYTD = new StdClass();

$o = dbTotalIncomePerGroupExFund($propertyID, $startFinancialYear, $periodTo, 'INCOWN');
$t = take_on_balance($propertyID, 'INCOWN', $startFinancialYear, $periodTo);
$incomeYTD->owner = (float) bcadd($o['net_amount'], $t['amount'], 2);

$o = dbTotalIncomePerGroupExFund($propertyID, $startFinancialYear, $periodTo, 'INCVO');
$t = take_on_balance($propertyID, 'INCVO', $startFinancialYear, $periodTo);
$incomeYTD->variableOutgoing = (float) bcadd($o['net_amount'], $t['amount'], 2);

$o = dbTotalIncomePerGroupExFund($propertyID, $startFinancialYear, $periodTo, 'INCDR');
$t = take_on_balance($propertyID, 'INCDR', $startFinancialYear, $periodTo);
$incomeYTD->recoverable = (float) bcadd($o['net_amount'], $t['amount'], 2);

$incomeYTD->total = $incomeYTD->owner + $incomeYTD->variableOutgoing + $incomeYTD->recoverable;

/* EXPENDITURE - Current */

$expenditure = new StdClass();
$_tax = new stdClass();

$o = dbTotalExpensesPerGroupExFund($propertyID, $periodFrom, $periodTo, 'EXPOWN');
$expenditure->owner = $o['net_amount'];
$_tax->owner = $o['tax_amount'];

$o = dbTotalExpensesPerGroupExFund($propertyID, $periodFrom, $periodTo, 'EXPVO');
$expenditure->variableOutgoing = $o['net_amount'];
$_tax->variableOutgoing = $o['tax_amount'];

$o = dbTotalExpensesPerGroupExFund($propertyID, $periodFrom, $periodTo, 'EXPDR');
$expenditure->recoverable = $o['net_amount'];
$_tax->recoverable = $o['tax_amount'];

$o = dbTotalExpensesPerGroupExFund($propertyID, $periodFrom, $periodTo, 'BSPMT');
// $expenditure->noneOp = $o['net_amount'];
$_tax->noneOp = $o['tax_amount'];

$expenditure->total = $expenditure->owner + $expenditure->variableOutgoing + $expenditure->recoverable;

/* EXPENDITURE - YTD */

$expenditureYTD = new StdClass();
$_taxYTD = new StdClass();

$o = dbTotalExpensesPerGroupExFund($propertyID, $startFinancialYear, $periodTo, 'EXPOWN');
$t = take_on_balance($propertyID, 'EXPOWN', $startFinancialYear, $periodTo);
$expenditureYTD->owner = (float) bcadd($o['net_amount'], $t['amount'], 2);
$_taxYTD->owner = $o['tax_amount'];

$o = dbTotalExpensesPerGroupExFund($propertyID, $startFinancialYear, $periodTo, 'EXPVO');
$t = take_on_balance($propertyID, 'EXPVO', $startFinancialYear, $periodTo);
$expenditureYTD->variableOutgoing = (float) bcadd($o['net_amount'], $t['amount'], 2);
$_taxYTD->variableOutgoing = $o['tax_amount'];

$o = dbTotalExpensesPerGroupExFund($propertyID, $startFinancialYear, $periodTo, 'EXPDR');
$t = take_on_balance($propertyID, 'EXPDR', $startFinancialYear, $periodTo);
$expenditureYTD->recoverable = (float) bcadd($o['net_amount'], $t['amount'], 2);
$_taxYTD->recoverable = $o['tax_amount'];

$o = dbTotalExpensesPerGroupExFund($propertyID, $startFinancialYear, $periodTo, 'BSPMT');
// $t = take_on_balance ($propertyID, 'BSPMT', $startFinancialYear, $periodTo);
// $expenditureYTD->noneOp = (float) bcadd ($o['net_amount'], $t['amount'], 2);
$_taxYTD->noneOp = $o['tax_amount'];

$expenditureYTD->total = $expenditureYTD->owner + $expenditureYTD->variableOutgoing + $expenditureYTD->recoverable;

/* NET - current and YTD */
$net = round($income->total - $expenditure->total, 2);
$netYTD = bcsub($incomeYTD->total, $expenditureYTD->total, 2);

// ########################################################################
// ###################### GST RECEIVED AND PAID ###########################
// ########################################################################

// GST received

$o = dbTotalIncomeExFund($propertyID, $periodFrom, $periodTo);
$tax = bcmul(-1, $o['tax_amount'], 2);

$o = dbTotalIncomeExFund($propertyID, $startFinancialYear, $periodTo);
$_o = bcmul(-1, $o['tax_amount'], 2);
$t = take_on_balanceGST($propertyID, $startFinancialYear, $periodTo);

$_t = $t;

$taxYTD = (float) bcadd($_o, $_t, 2);

$taxPaid = (float) bcmul(-1, ($_tax->owner + $_tax->variableOutgoing + $_tax->recoverable + $_tax->noneOp), 2);

// ####################################################################################
// ########## GST YTD NOTE: NEED TO INCLUDE GROUP pmcg_subgrp = 'EXPGST' ##############
// ####################################################################################

$o = take_on_balanceGST_paid($propertyID, $startFinancialYear, $periodTo);
$taxPaidYTD = (float) bcmul(-1, ($_taxYTD->owner + $_taxYTD->variableOutgoing + $_taxYTD->recoverable + $_taxYTD->noneOp + $o), 2);

// ## GST paid to owner
$o = GST_remitted($propertyID, 'EXPGST', $periodFrom, $periodTo);
$remittedTax = (float) bcmul(-1, $o, 2);

$o = GST_remitted($propertyID, 'EXPGST', $startFinancialYear, $periodTo);
$remittedTaxYTD = (float) bcmul(-1, $o, 2);

// #GST subtotals
$taxSubTotal = $tax + $taxPaid + $remittedTax;
$taxSubTotalYTD = $taxYTD + $taxPaidYTD + $remittedTaxYTD;

$taxSubTotal = round($taxSubTotal, 2);
$taxSubTotalYTD = round($taxSubTotalYTD, 2);

// #####################################################################################
// ###### NET CASH INFLOWS/(OUTFLOWS) NET CASH INFLOWS/(OUTFLOWS) #####################
// #####################################################################################

$totalNet = bcadd($net, $taxSubTotal, 2);
$totalNetYTD = bcadd($netYTD, $taxSubTotalYTD, 2);

// ####################################################################################
// ################## CASH RECONCILIATION #############################################
// ####################################################################################

$receipts = total_cash_receiptsExFund($propertyID, $startFinancialYear, $previousPeriodTo); // receipts this financial year to beginning of current month //$datebefore
$payments = total_cash_paymentsExFund($propertyID, $startFinancialYear, $previousPeriodTo); // payments this financial year to beginning of current month //$datebefore
$_balance = bcsub($receipts, $payments, 2); // net receipts current financial year to beginning of current month

$receiptsYTD = total_cash_receiptsExFund($propertyID, STARTDATE, $financialPeriodToPY);
$paymentsYTD = total_cash_paymentsExFund($propertyID, STARTDATE, $financialPeriodToPY); // all payments up to the end of the prior financial year //$dateBeforeStart
$balanceYTD = bcsub($receiptsYTD, $paymentsYTD, 2); // net receipts to end of prior financial year

$balance = bcadd($_balance, $balanceYTD, 2); // net receipts to beginning of current month

// NOTE need also to do it to $dateBeforeStart - day before start of fin year - above is from beginning of the year to day before reporting period
$subTotal = bcadd($balance, $totalNet, 2);
$subTotalYTD = bcadd($balanceYTD, $totalNetYTD, 2);




// #####################################################################################
// ----------------------NONE OPERATING RECEIPTS/PAYMENTS------------------------------
// #####################################################################################

$pmts_duringMBS = getPaymentsToOwnersBSonly($propertyID, $periodFrom, $periodTo);
$pmts_duringYBS = getPaymentsToOwnersBSonly($propertyID, $startFinancialYear, $periodTo);

$noneop_rec_duringM = dbTotalIncomePerGroupExFund($propertyID, $periodFrom, $periodTo, 'BSREC'); // trim($query_result["amount"]);
$noneop_rec_duringY = dbTotalIncomePerGroupExFund($propertyID, $startFinancialYear, $periodTo, 'BSREC');

$noneop_pay_duringM = totalexpenses($propertyID, $periodFrom, $periodTo, 'BSPMT'); // trim($query_result["amount"]);
$noneop_pay_duringY = totalexpenses($propertyID, $startFinancialYear, $periodTo, 'BSPMT');

$noneop_pay_month = $noneop_pay_duringM['gross_amount'] + $noneop_pay_duringM['tax_amount'] - $pmts_duringMBS; // * (-1);
$noneop_pay_year = $noneop_pay_duringY['gross_amount'] + $noneop_pay_duringY['tax_amount'] - $pmts_duringYBS; // * (-1);
$noneop_rec_month = $noneop_rec_duringM['net_amount']; // + $noneop_rec_duringM['tax_amount'];// * (-1);
$noneop_rec_year = $noneop_rec_duringY['net_amount']; // + $noneop_rec_duringY['tax_amount'];// * (-1);


// #####################################################################################
// -----------------------------OWNER REMITTANCES--------------------------------------
// #####################################################################################

// Current month
// $o = dbTotalExpensesPerGroupExFund ($propertyID, $periodFrom, $periodTo, 'EXPOPT');
// $ownerPayments = $o['gross_amount'];

$ownerPayments = getPaymentsToOwners($propertyID, $periodFrom, $periodTo);
$ownerPaymentsYTD = getPaymentsToOwners($propertyID, $startFinancialYear, $periodTo);

// Year to date includes take on balances
$t = take_on_balance_own_remit($propertyID, 'EXPOPT', $startFinancialYear, $periodTo);
// $o = dbTotalExpensesPerGroupExFund ($propertyID, $startFinancialYear, $periodTo, 'EXPOPT');
// $ownerPaymentsYTD = (float) bcsub ($o['gross_amount'], $t, 2);
$ownerPaymentsYTD = $ownerPaymentsYTD - $t;

// Each owners remittance
$owner_remittances = expenses_detail($propertyID, $periodFrom, $periodTo, 'EXPOPT');

$ownerRemittanceList =  [];
foreach ($owner_remittances as $key => $each_owner_remit) {
    // this is in an array with possibly multiple lines
    $ownerRemittanceList[$key]['name'] = @substr($owner_remittances['name'], 0, 15);
    $ownerRemittanceList[$key]['amount'] = @$owner_remittances['amount'];
    // //////// NOTE WE NEED TO PUT IN SOME PDF FORMATTING IN THIS LOOP
}

$lineOffset = 0;
foreach ($ownerRemittanceList as $ownerRemittanceItem) {
    $lineOffset += 10;
}

// #####################################################################################
// -----------------------------CASH BALANCE AT THE END OF THE YEAR--------------------
// #####################################################################################

// $cashBalance = (float) bcadd ($subTotal, $ownerPayments, 2);
$cashBalance = round($subTotal + $ownerPayments + $noneop_pay_month + $noneop_rec_month, 2);
// $cashBalanceYTD = (float) bcadd ($subTotalYTD, $ownerPaymentsYTD, 2);
$cashBalanceYTD = round($subTotalYTD + $ownerPaymentsYTD + $noneop_pay_year + $noneop_rec_year, 2);

<?php

set_time_limit(0);
if (! function_exists('closePDF')) {
    function closePDF()
    {
        global $pdf;
        $pdf->end_document('');
        $pdf->delete();
    }
}


function slInitializePDF($title, $fileName, $path, $downloadLink, $propName)
{
    global $pdf, $_fonts;
    $openFileSuccess = false;

    // prepare the file
    //    $timestamp = date('dmYHis');
    $ext = '.pdf';
    $fullFileName = $fileName . $ext;

    // Full logical Path for pdf creation
    $fullPath = $path . $fullFileName;
    $pdfDownloadLink = "$downloadLink/$fullFileName";

    $pdf = new PDFlibExt();
    $pdf->set_option('license=' . PDFLIB_LICENSE);
    $pdf->set_option('stringformat=utf8');
    $pdf->set_option('SearchPath=' . realpath(BASEPATH . '/framework/'));
    $openFileSccess = $pdf->begin_document($fullPath, '');

    if ($openFileSccess) {
        $pdf->set_info('Author', 'cirrus8 Pty Ltd | cirrus8.com.au - Online Commercial Property Management Solution');
        $pdf->set_info('Title', $title);

        $_fonts['Helvetica'] = $pdf->load_font('Helvetica', 'host', '');
        $_fonts['Helvetica-Bold'] = $pdf->load_font('Helvetica-Bold', 'host', '');
        $_fonts['Helvetica-Oblique'] = $pdf->load_font('Helvetica-Oblique', 'host', '');
    } else {
        return false;
    }

    return $pdfDownloadLink;
}


function generateSubLedgerReportProcess(&$context)
{
    $threshold = 3;
    global $dbh, $sess;

    include_once 'lib/reportLib/reportincludesAll.php';
    include_once 'lib/ReportLib/commonReportFunctions.php';
    include_once 'functions/page1PropertyReportFunctions.php';
    include_once 'functions/page2DebtorsDetailFunctions.php';
    include_once 'functions/pdfHeaders.php';
    require_once 'lib/phpdocx/classes/CreateDocx.php';

    if (! isPostback()) {
        $view = new MasterPage(userViews(), '/managementReports/generateSubLedgerReportProcess.html');
        //        $view->setSection ($context['module']);
    } else {
        $view = new UserControl(userViews(), '/managementReports/generateSubLedgerReportProcess.html', '');
    }

    try {
        if ($context[IS_TASK]) {
            $view->bindAttributesFrom($context);
            extract($context, EXTR_OVERWRITE);
        } else {
            $view->bindAttributesFrom($_REQUEST);
            $propertyCount = 0;

            $forceGenerate = false;
            if (isset($view->items['forceGenerate'])) {
                $forceGenerate = (bool) $view->items['forceGenerate'];
            }

            if ($view->items['property']) {
                $propertyCount = count(deserializeParameters($view->items['property']));
            }
            $queue = new Queue(TASKTYPE_OWNER_REPORT);
            //            if (!$forceGenerate && $propertyCount > THRESHOLD_OWNERREPORT)
            //            {
            //                $_REQUEST['command'] = 'ownerReportProcess';
            //                $queue->add ($_SESSION['clientID'], $_SESSION['un'], 'module=managementReports&command=ownerReportProcess', $_REQUEST);
            //            }
        }
        if (($context[IS_TASK]) || $forceGenerate || ($propertyCount <= THRESHOLD_OWNERREPORT)) {
            if (isset($view->items['reporttype'])) {
                $reportType = $view->items['reporttype'];
            }

            $report = dbGetReport($reportType);

            $title = $report['reportDescription'];
            $orientation = $report['reportOrientation'];
            $_page = getPageSize($orientation);

            $showAccCodesFlag = false;
            if (isset($view->items['show_acc_codes'])) {
                $showAccCodesFlag = true;
            }

            global $periodFrom;
            global $periodTo;
            global $reportingPeriodFrom;
            global $reportingPeriodTo;
            global $periodDescription;
            global $reportDescription;
            global $reportingTotal;
            global $reportingCurrPage;
            global $reportingTotalPages;
            global $filenameDescription;
            global $propertyName;
            global $client;
            global $client_street;
            global $client_city;
            global $client_state;
            global $client_postcode;
            global $line;
            global $startline;
            global $date;
            global $page;
            global $pdf;
            global $_fonts;
            global $propertyID;
            global $propertyGroup;
            global $clientDirectory;
            global $filePrefix;
            global $pathPrefix;
            global $logo;
            global $clientDB;
            global $leaseID;
            global $openingbalance;

            $openingbalance = 0;
            $leaseID = $view->items['leaseID'];

            $date = TODAY;
            $logo = $view->items['logo'];
            $logoFlag = $logo;
            //            logData($view->items['logo'] . '<<');
            $periodFrom = $view->items['periodFrom'];
            $periodTo = $view->items['periodTo'];
            $sub_ledger_ps = $view->items['sub_ledger_ps'];

            $reportingPeriodFrom = $view->items['reportingPeriodFrom'];
            $reportingPeriodTo = $view->items['reportingPeriodTo'];

            $combinedReport = (isset($view->items['combinedReport']) ? (bool) $view->items['combinedReport'] : false);
            //			$logoFile = dbGetClientLogo();
            $logoFile = dbGetClientLogo();
            $logoPath = "assets/clientLogos/{$logoFile}";

            //            logData ($logoPath);

            $dtTo = str_replace('/', '-', $periodTo);

            $dateReport = DateTime::createFromFormat('d-m-Y', $dtTo);
            $dateObj = DateTime::createFromFormat('!m', $dateReport->format('m'));
            $monthName = $dateObj->format('F');
            $dtYear = $dateReport->format('Y'); // year

            $periodDescription = $monthName . ' ' . $dtYear;
            $reportDescription = $view->items['description'];
            $filenameDescription = $view->items['filename_description'];
            $PDFtitle = 'Owners Reports';
            $filename_description = preg_replace("/([^\w\d])/", '_', $view->items['filename_description']);
            if (strlen($filename_description) > 0) {
                $fileName = $filename_description;
            } else {
                $fileName = 'OwnersReports_';
            }

            $properties = deserializeParameters($view->items['property']);
            $_filePath = "{$pathPrefix}{$clientDirectory}/pdf/" . DOC_TRUSTACCOUNTSTATEMENT . '/';
            $_downloadPath = "{$clientDirectory}/pdf/" . DOC_TRUSTACCOUNTSTATEMENT;
            if (! file_exists($_filePath)) {
                mkdir($_filePath, FILE_PERMISSION, true);
            }
            if ($view->items['datasource'] == DS_GROUP) {
                $propertyGroup = $view->items['filter'];
            }

            $_coverPage = 0;
            define('A', 1);
            define('C', 2);

            //
            // #########################################
            // GENERATE REPORT
            if (! $combinedReport) { // IF BY PROPERTY
                // SET OVERALL TOTALS
                $reportingTotal = 0;
                $reportingCurrPage = 1;
                $reportingTotalPages = count($properties ?? []);
                //

                foreach ($properties as $ownerReportIndex => $propertyID) {
                    $_coverPage++;
                    $page = 0;
                    $subledgerOpeningBalance = dbGetSubLedgerOpeningBalance($propertyID, $leaseID, $periodFrom);
                    if ($subledgerOpeningBalance['totalAmount']) {
                        $openingbalance = $subledgerOpeningBalance['totalAmount'];
                    }


                    $prop = dbGetPropertyDetails($propertyID);
                    $propertyName = $prop['propertyName'];
                    $propertyAddress = $prop['propertyDescription'];
                    $salesReference = $prop['sales_ref'];
                    $agentSLName = dbGetParam('AGENT', $prop['agent']);
                    $commission = dbGetCommission($propertyID);
                    $settlementDate = $commission['settlement_date'];
                    $salesPrice = $commission['sales_price'];
                    $leaseDetails = dbGetLeaseDetails($propertyID, $leaseID);
                    // #####################################################################################
                    // ################### GET THE OWNERS DETAILS FOR PRINTING AT THE TOP###################

                    //                    $owner = owner_details ($propertyID);
                    $owner = dbGetTenantCompanyDetails($view->items['companyID']);
                    $client = $owner['companyID'] . ' - ' . $owner['companyName'];
                    $client_street = $owner['companyAddress'];
                    $client_city = $owner['companyCity'];
                    $client_state = $owner['companyState'];
                    $client_postcode = $owner['companyPostCode'];
                    $slDownloadLink = slInitializePDF($PDFtitle, $fileName, $_filePath, $_downloadPath, $propertyName);

                    $view->items['processed'] = true;
                    /**    NEW REPORT PROCESSING CODE **/
                    // -- extract the selected pages from the report
                    $ownerName = dbGetOwnerName($propertyID);

                    // -- create the header object that gets used on each page
                    $header = new ReportHeader(
                        $propertyName,
                        'Owner: ' . $ownerName,
                        $title . ' - ' . $periodDescription
                    );
                    $header->xPos = 20;
                    $header->yPos = $_page->height - 25;
                    // -- process the subreport pages
                    $report_type = $view->items['report_type'];
                    if ($report_type == 0) {
                        include 'residential/simple_subledger.php';
                    } elseif ($report_type == 1) {
                        include 'residential/seller_statement.php';
                    } elseif ($report_type == 2) {
                        include 'residential/purchaser_statement.php';
                    }
                    $_coverPage += $page;
                    /** END OF NEW CODE **/
                    // UPDATE REPORTING PAGE
                    $reportingCurrPage++;
                    closePDF();
                }// END FOR EACH
            }
            echo encodeParameter($slDownloadLink);
            exit();
            // ###########################################
        } // -- end threshold check

    } catch (PDFlibException $e) {
        exit($e);
    } catch (Exception $e) {
        exit($e);
    }
}

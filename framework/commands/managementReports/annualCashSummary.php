<?php

if (! function_exists('newAnnualPage')) {
    function newAnnualPage($startDate, $endDate)
    {
        global $pdf;
        global $propertyID;
        global $client;
        global $propertyName;
        global $client;
        global $logo;
        global $_fonts;

        $pdf->begin_page_ext(842, 595, '');

        $page_header = 'Annual Cash Summary';
        $page++;
        $pdf->setFontExt($_fonts['Helvetica-Bold'], 9);

        $pdf->showBoxed("$page_header", 271, 540, 300, 30, 'center', '');
        $pdf->setFontExt($_fonts['Helvetica'], 8);
        $pdf->show_xy('Owner:', 22, 540);
        $pdf->continue_text('Property:');
        $pdf->continue_text('Report For:');
        $pdf->show_xy($client, 70, 540);
        $pdf->continue_text($propertyName . " [$propertyID]");
        $pdf->continue_text(date('M Y', strtotime(convertDate($startDate['startDate']) . '11 month')));
        $pdf->showBoxed('Period From: ' . $startDate['startDate'] . ' To: ' . $endDate['endDate'] . '', 271, 529, 300, 30, 'center', '');

        if ($logo) {
            generateLogo('landscape');
        }

        // top header line
        $pdf->setlinewidth(0.5);
        $pdf->moveto(18, 515);
        $pdf->lineto(824, 515);
        $pdf->stroke();

        $pdf->setlinewidth(0.5);
        $pdf->moveto(18, 495);
        $pdf->lineto(824, 495);
        $pdf->stroke();

        $pdf->setFontExt($_fonts['Helvetica-Bold'], 9);
        // $pdf->showBoxed ($text1, 22, 490, 75, 30, "left", "");
        $pdf->showBoxed(date('M Y', strtotime(convertDate($startDate['startDate']))), 170, 490, 50, 20, 'right', '');
        $pdf->showBoxed(date('M Y', strtotime(convertDate($startDate['startDate']) . '1 month')), 220, 490, 50, 20, 'right', '');
        $pdf->showBoxed(date('M Y', strtotime(convertDate($startDate['startDate']) . '2 month')), 270, 490, 50, 20, 'right', '');
        $pdf->showBoxed(date('M Y', strtotime(convertDate($startDate['startDate']) . '3 month')), 320, 490, 50, 20, 'right', '');
        $pdf->showBoxed(date('M Y', strtotime(convertDate($startDate['startDate']) . '4 month')), 370, 490, 50, 20, 'right', '');
        $pdf->showBoxed(date('M Y', strtotime(convertDate($startDate['startDate']) . '5 month')), 420, 490, 50, 20, 'right', '');
        $pdf->showBoxed(date('M Y', strtotime(convertDate($startDate['startDate']) . '6 month')), 470, 490, 50, 20, 'right', '');
        $pdf->showBoxed(date('M Y', strtotime(convertDate($startDate['startDate']) . '7 month')), 520, 490, 50, 20, 'right', '');
        $pdf->showBoxed(date('M Y', strtotime(convertDate($startDate['startDate']) . '8 month')), 570, 490, 50, 20, 'right', '');
        $pdf->showBoxed(date('M Y', strtotime(convertDate($startDate['startDate']) . '9 month')), 620, 490, 50, 20, 'right', '');
        $pdf->showBoxed(date('M Y', strtotime(convertDate($startDate['startDate']) . '10 month')), 670, 490, 50, 20, 'right', '');
        $pdf->showBoxed(date('M Y', strtotime(convertDate($startDate['startDate']) . '11 month')), 720, 490, 50, 20, 'right', '');
        $pdf->showBoxed('YTD', 770, 490, 52, 20, 'right', '');
    }
}

if (! function_exists('withAccountCategoriesComputations')) {
    function withAccountCategoriesComputations($dataArray, $type)
    {
        $actualTotal = 0;
        $YTDTotal = 0;
        $PYTotal = 0;
        $budgetTotal = 0;
        $budgetYTDTotal = 0;
        $PYYTDTotal = 0;
        $PYYearTotal = 0;
        $actualPYTotal = 0;
        $varianceTotal = 0;
        $varianceAccountCategories = 0;
        $varianceYTDTotal = 0;
        $varianceYTDAccountCategories = 0;
        $variancePYYTDTotal = 0;
        $variancePYYTDAccountCategories = 0;
        $variancePYTotal = 0;
        $variancePYAccountCategories = 0;
        $actualTotal_sub = [];
        $actualTotalPrior_sub = [];
        $actualTotalY_sub = [];
        $actualTotalPriorY_sub = [];
        foreach ($dataArray as $y => $i) {
            $budgetAccountCategories = 0;
            $budgetYTDAccountCategories = 0;
            $varianceAccountCategories = 0;
            $varianceYTDAccountCategories = 0;
            $variancePYYTDAccountCategories = 0;
            $variancePercentageAccountCategories = 0;
            $variancePercentageYTDAccountCategories = 0;
            $variancePercentagePYYTDAccountCategories = 0;
            $variancePYAccountCategories = 0;
            $variancePercentagePYAccountCategories = 0;
            $accountCategoryLabel = $y;
            foreach ($i as $x => $z) {
                $variance = 0;
                $varianceYTD = 0;
                $variancePYYTD = 0;
                $variancePY = 0;
                $actual = ! isset($z[0]) ? 0 : $z[0];
                $budget = ! isset($z['budget']) ? 0 : $z['budget'];
                $ytd = ! isset($z['ytd']) ? 0 : $z['ytd'];
                $priorYTD = ! isset($z['priorYTD']) ? 0 : $z['priorYTD'];
                $budgetYTD = ! isset($z['budgetYTD']) ? 0 : $z['budgetYTD'];
                $priorYear = ! isset($z['priorYear']) ? 0 : $z['priorYear'];
                $actualPY = ! isset($z['actualPY']) ? 0 : $z['actualPY'];


                $variance =  $type == EXPENDITURE ? variance($budget, $actual) : variance($actual, $budget);
                $varianceYTD =   $type == EXPENDITURE ? variance($budgetYTD, $ytd) : variance($ytd, $budgetYTD);
                $variancePYYTD =  variance($ytd, $priorYTD);
                $variancePY =    variance($actual, $actualPY);

                /* COMPUTATION PER INCOME ACCOUNT */
                $dataArray[$accountCategoryLabel][$x]['variance'] = $variance;
                $dataArray[$accountCategoryLabel][$x]['variancePercentage'] = variancePercentage($variance, $budget);

                $dataArray[$accountCategoryLabel][$x]['varianceYTD'] = $varianceYTD;
                $dataArray[$accountCategoryLabel][$x]['variancePercentageYTD'] = variancePercentage($varianceYTD, $budgetYTD);


                $dataArray[$accountCategoryLabel][$x]['variancePYYTD'] = $variancePYYTD;
                $dataArray[$accountCategoryLabel][$x]['variancePercentagePYYTD'] = variancePercentage($variancePYYTD, $ytd);

                $dataArray[$accountCategoryLabel][$x]['variancePY'] = $variancePY;
                $dataArray[$accountCategoryLabel][$x]['variancePercentagePY'] = variancePercentage($variancePY, $actual);
                /* END */

                /* COMPUTATION OF SUB-TOTAL PER ACCOUNT CATEGORY */
                $actualTotal += $actual;
                $actualTotal_sub[$y] += $actual;
                $actualTotalPrior_sub[$y] += $actualPY;
                $actualTotalY_sub[$y] += $ytd;
                $actualTotalPriorY_sub[$y] += $priorYTD;
                $budgetTotal += $budget;
                $YTDTotal += $ytd;
                $budgetYTDTotal += $budgetYTD;
                $budgetYTDAccountCategories += $budgetYTD;
                $PYTotal += $priorYear;
                $PYYearTotal += $priorYTD;
                $actualPYTotal += $actualPY;

                $varianceTotal += $variance;
                $varianceAccountCategories += $variance;

                $budgetAccountCategories += $budget;

                $varianceYTDTotal += $varianceYTD;
                $varianceYTDAccountCategories += $varianceYTD;


                $variancePYYTDTotal += $variancePYYTD;
                $variancePYYTDAccountCategories += $variancePYYTD;

                $variancePYTotal += $variancePY;
                $variancePYAccountCategories += $variancePY;

                $variancePercentageAccountCategories += variancePercentage($variance, $budget);
                $variancePercentageYTDAccountCategories += variancePercentage($varianceYTD, $budgetYTD);
                $variancePercentagePYYTDAccountCategories += variancePercentage($variancePYYTD, $ytd);
                $variancePercentagePYAccountCategories += variancePercentage($variancePY, $actual);
                /* END */
            }
            $accountCategoriesSubTotals[$y]['description'] = 'Sub-total';
            $accountCategoriesSubTotals[$y]['variance'] = $varianceAccountCategories;
            $accountCategoriesSubTotals[$y]['variancePercentage'] = variancePercentage($varianceAccountCategories, $budgetAccountCategories);

            $accountCategoriesSubTotals[$y]['varianceYTD'] = $varianceYTDAccountCategories;
            $accountCategoriesSubTotals[$y]['variancePercentageYTD'] = variancePercentage($varianceYTDAccountCategories, $budgetYTDAccountCategories);

            $variancePYYTD = $actualTotalY_sub[$y] - $actualTotalPriorY_sub[$y];
            $accountCategoriesSubTotals[$y]['variancePYYTD'] = $variancePYYTD;
            $accountCategoriesSubTotals[$y]['variancePercentagePYYTD'] = variancePercentage($variancePYYTD, $actualTotalY_sub[$y]);


            $variangePY = $actualTotal_sub[$y] - $actualTotalPrior_sub[$y];
            $accountCategoriesSubTotals[$y]['variancePY'] = $variangePY;
            $accountCategoriesSubTotals[$y]['variancePercentagePY'] = variancePercentage($variangePY, $actualTotal_sub[$y]);

        }
        $subtotals['variance'] =   $type == EXPENDITURE ? variance($budgetTotal, $actualTotal) : variance($actualTotal, $budgetTotal);
        $subtotals['variancePercentage'] = variancePercentage($varianceTotal, $budgetTotal);

        $subtotals['varianceYTD'] =  $type == EXPENDITURE ? variance($budgetYTDTotal, $YTDTotal) : variance($YTDTotal, $budgetYTDTotal);
        $subtotals['variancePercentageYTD'] = variancePercentage($varianceYTDTotal, $budgetYTDTotal);

        $subtotals['variancePYYTD'] =   variance($YTDTotal, $PYYearTotal);
        $subtotals['variancePercentagePYYTD'] = variancePercentage($variancePYYTDTotal, $YTDTotal);

        $subtotals['variancePY'] =  variance($actualTotal, $PYTotal);
        $subtotals['variancePercentagePY'] = variancePercentage($variancePYTotal, $actualTotal);

        return ['data' => $dataArray, 'subTotalAccountCategory' => $accountCategoriesSubTotals, 'subTotals' => $subtotals];
    }
}

if (! function_exists('withoutAccountCategoriesComputations')) {
    function withoutAccountCategoriesComputations($dataArray, $type)
    {
        foreach ($dataArray as $x => $z) {
            $variance = 0;
            $varianceYTD = 0;
            $variancePYYTD = 0;
            $actual = ! isset($z[0]) ? 0 : $z[0];
            $budget = ! isset($z['budget']) ? 0 : $z['budget'];
            $ytd = ! isset($z['ytd']) ? 0 : $z['ytd'];
            $priorYTD = ! isset($z['priorYTD']) ? 0 : $z['priorYTD'];
            $budgetYTD = ! isset($z['budgetYTD']) ? 0 : $z['budgetYTD'];
            $priorYear = ! isset($z['priorYear']) ? 0 : $z['priorYear'];
            $actualPY = ! isset($z['actualPY']) ? 0 : $z['actualPY'];

            $variance =   $type == EXPENDITURE ? variance($budget, $actual) : variance($actual, $budget);
            $varianceYTD =   $type == EXPENDITURE ? variance($budgetYTD, $ytd) : variance($ytd, $budgetYTD);
            $variancePYYTD =  variance($ytd, $priorYTD);
            $variancePY =  variance($actual, $actualPY);

            /* COMPUTATION PER INCOME ACCOUNT */
            $dataArray[$x]['variance'] = $variance;
            $dataArray[$x]['variancePercentage'] = variancePercentage($variance, $budget);
            $dataArray[$x]['varianceYTD'] = $varianceYTD;
            $dataArray[$x]['variancePercentageYTD'] = variancePercentage($varianceYTD, $budgetYTD);
            $dataArray[$x]['variancePYYTD'] = $variancePYYTD;
            $dataArray[$x]['variancePercentagePYYTD'] = variancePercentage($variancePYYTD, $ytd);
            $dataArray[$x]['variancePY'] = $variancePY;
            $dataArray[$x]['variancePercentagePY'] = variancePercentage($variancePY, $actual);
            /* END */

        }

        return ['data' => $dataArray];
    }
}

$pdf->begin_page_ext(842, 595, '');

$page_header = 'Annual Cash Summary';
$page++;

$pdf->setFontExt($_fonts['Helvetica-Bold'], 9);

$cal = dbGetMasterCalendarForYear('Financial', $calendarTo['year']);
$startDate = $cal[0];
$endDate = $cal[1];

$pdf->showBoxed("$page_header", 271, 540, 300, 30, 'center', '');
$pdf->setFontExt($_fonts['Helvetica'], 8);
$pdf->show_xy('Owner:', 22, 540);
$pdf->continue_text('Property:');
$pdf->continue_text('Report For:');
$pdf->show_xy($client, 70, 540);
$pdf->continue_text($propertyName . " [$propertyID]");
$pdf->continue_text(date('M Y', strtotime(convertDate($startDate['startDate']) . '11 month')));
$pdf->showBoxed('Period From: ' . $startDate['startDate'] . ' To: ' . $endDate['endDate'] . '', 271, 529, 300, 30, 'center', '');

if ($logo) {
    generateLogo('landscape');
}

// top header line
$pdf->setlinewidth(0.5);
$pdf->moveto(18, 515);
$pdf->lineto(824, 515);
$pdf->stroke();

$pdf->setlinewidth(0.5);
$pdf->moveto(18, 495);
$pdf->lineto(824, 495);
$pdf->stroke();

$pdf->setFontExt($_fonts['Helvetica-Bold'], 9);
// $pdf->showBoxed ($text1, 22, 490, 75, 30, "left", "");
$pdf->showBoxed(date('M Y', strtotime(convertDate($startDate['startDate']))), 170, 490, 50, 20, 'right', '');
$pdf->showBoxed(date('M Y', strtotime(convertDate($startDate['startDate']) . '1 month')), 220, 490, 50, 20, 'right', '');
$pdf->showBoxed(date('M Y', strtotime(convertDate($startDate['startDate']) . '2 month')), 270, 490, 50, 20, 'right', '');
$pdf->showBoxed(date('M Y', strtotime(convertDate($startDate['startDate']) . '3 month')), 320, 490, 50, 20, 'right', '');
$pdf->showBoxed(date('M Y', strtotime(convertDate($startDate['startDate']) . '4 month')), 370, 490, 50, 20, 'right', '');
$pdf->showBoxed(date('M Y', strtotime(convertDate($startDate['startDate']) . '5 month')), 420, 490, 50, 20, 'right', '');
$pdf->showBoxed(date('M Y', strtotime(convertDate($startDate['startDate']) . '6 month')), 470, 490, 50, 20, 'right', '');
$pdf->showBoxed(date('M Y', strtotime(convertDate($startDate['startDate']) . '7 month')), 520, 490, 50, 20, 'right', '');
$pdf->showBoxed(date('M Y', strtotime(convertDate($startDate['startDate']) . '8 month')), 570, 490, 50, 20, 'right', '');
$pdf->showBoxed(date('M Y', strtotime(convertDate($startDate['startDate']) . '9 month')), 620, 490, 50, 20, 'right', '');
$pdf->showBoxed(date('M Y', strtotime(convertDate($startDate['startDate']) . '10 month')), 670, 490, 50, 20, 'right', '');
$pdf->showBoxed(date('M Y', strtotime(convertDate($startDate['startDate']) . '11 month')), 720, 490, 50, 20, 'right', '');
$pdf->showBoxed('YTD', 770, 490, 52, 20, 'right', '');

$accountGroups = [
    INCOME => 'Income',
    EXPENDITURE => 'Expenditure',
    BALANCESHEET => 'Balance Sheet',
];


$year = $endDate['year'];

$accountCategories = mapParameters(dbGetAccountGroup(false), 'accountGroup', 'accountDescription');
$startYear = $year;

$data = [];
$totals = [];
$subtotals = [];
$expDisData = [];
$netProfitTotal = [];
$accounts = [];
$periods = [];
$accountCategoriesSubTotals = [];
$node = 'Cash';
$basis = 'Cash Basis';

$_periods = 12;
$properties = [$propertyID];

$linkedAccounts = dbGetLinkedAccounts();
foreach ($accountGroups as $accountType => $accountGroupName) {
    $period = 12;
    $per_to = null;

    $subtotals[$accountType]['description'] = 'Total ' . $accountGroupName;
    $subtotals['isExpDis']['description'] = 'Total Balance Sheet Movements';

    $factor = ($accountType == 'I') ? -1 : 1;

    $ytd = dbGetTrialBalanceYTDByAccountGroup([$propertyID], $accountType, $endDate['period'], $year, 2);
    foreach ($ytd as $y) {
        $isExpDis = dbCheckAccountIdExpDisOrBS($y['accountID']);
        if ($accountType == BALANCESHEET and in_array($y['accountID'], $linkedAccounts)) {
            continue;
        }

        $accountCategory = dbGetAccountGroup($y['accountID']);
        if ($isExpDis) {
            $expDisData[$accountType][$accountCategory['accountGroup']][$y['accountID']]['ownerAccountCode'] = $y['ownerAccountCode'];
            $expDisData[$accountType][$accountCategory['accountGroup']][$y['accountID']]['ownerAccountDesc'] = $y['ownerAccountDesc'];
            $expDisData[$accountType][$accountCategory['accountGroup']][$y['accountID']]['accountGroupName'] = $accountCategory['accountDescription'];
            $expDisData[$accountType][$accountCategory['accountGroup']][$y['accountID']]['accountID'] = $y['accountID'];
            $expDisData[$accountType][$accountCategory['accountGroup']][$y['accountID']]['ytd'] = bcmul($factor, $y['balance' . $node], 2);
            $expDisData[$accountType][$accountCategory['accountGroup']][$y['accountID']]['description'] = $y['accountName'];
            $subtotals['isExpDis']['ytd'] += $expDisData[$accountType][$accountCategory['accountGroup']][$y['accountID']]['ytd'];
            $accountCategoriesSubTotals[$accountCategory['accountGroup']]['ytd'] += $expDisData[$accountType][$accountCategory['accountGroup']][$y['accountID']]['ytd'];
        } else {
            $data[$accountType][$accountCategory['accountGroup']][$y['accountID']]['ownerAccountDesc'] = $y['ownerAccountDesc'];
            $data[$accountType][$accountCategory['accountGroup']][$y['accountID']]['ownerAccountCode'] = $y['ownerAccountCode'];
            $data[$accountType][$accountCategory['accountGroup']][$y['accountID']]['accountGroupName'] = $accountCategory['accountDescription'];
            $data[$accountType][$accountCategory['accountGroup']][$y['accountID']]['accountID'] = $y['accountID'];
            $data[$accountType][$accountCategory['accountGroup']][$y['accountID']]['ytd'] = bcmul($factor, $y['balance' . $node], 2);
            $data[$accountType][$accountCategory['accountGroup']][$y['accountID']]['description'] = $y['accountName'];
            $subtotals[$accountType]['ytd'] += $data[$accountType][$accountCategory['accountGroup']][$y['accountID']]['ytd'];
            $accountCategoriesSubTotals[$accountCategory['accountGroup']]['ytd'] += $data[$accountType][$accountCategory['accountGroup']][$y['accountID']]['ytd'];
        }
    }

    for ($i = 0; $i < $_periods; $i++) {

        $balances = dbGetTrialBalanceForPeriodByAccountGroup([$propertyID], $accountType, $period, $year, $per_to);

        if ($balances) {
            foreach ($balances as $b) {
                $isExpDis = dbCheckAccountIdExpDisOrBS($b['accountID']);
                if ($accountType == BALANCESHEET and in_array($b['accountID'], $linkedAccounts)) {
                    continue;
                }

                $accountCategory = dbGetAccountGroup($b['accountID']);
                if ($isExpDis) {
                    $expDisData[$accountType][$accountCategory['accountGroup']][$b['accountID']]['ownerAccountDesc'] = $b['ownerAccountDesc'];
                    $expDisData[$accountType][$accountCategory['accountGroup']][$b['accountID']]['ownerAccountCode'] = $b['ownerAccountCode'];
                    $expDisData[$accountType][$accountCategory['accountGroup']][$b['accountID']]['accountGroupName'] = $accountCategory['accountDescription'];
                    $expDisData[$accountType][$accountCategory['accountGroup']][$b['accountID']]['accountID'] = $b['accountID'];
                    $expDisData[$accountType][$accountCategory['accountGroup']][$b['accountID']]['description'] = $b['accountName'];
                    $expDisData[$accountType][$accountCategory['accountGroup']][$b['accountID']][$i] = bcmul($factor, $b['balance' . $node], 2);
                    $subtotals['isExpDis'][$i] += $expDisData[$accountType][$accountCategory['accountGroup']][$b['accountID']][$i];
                    $accountCategoriesSubTotals[$accountCategory['accountGroup']][$i] += $expDisData[$accountType][$accountCategory['accountGroup']][$b['accountID']][$i];
                } else {

                    $data[$accountType][$accountCategory['accountGroup']][$b['accountID']]['ownerAccountDesc'] = $b['ownerAccountDesc'];
                    $data[$accountType][$accountCategory['accountGroup']][$b['accountID']]['ownerAccountCode'] = $b['ownerAccountCode'];
                    $data[$accountType][$accountCategory['accountGroup']][$b['accountID']][$i] = bcmul($factor, $b['balance' . $node], 2);
                    $data[$accountType][$accountCategory['accountGroup']][$b['accountID']]['accountGroupName'] = $accountCategory['accountDescription'];
                    $data[$accountType][$accountCategory['accountGroup']][$b['accountID']]['accountID'] = $b['accountID'];
                    $data[$accountType][$accountCategory['accountGroup']][$b['accountID']]['description'] = $b['accountName'];
                    $subtotals[$accountType][$i] += $data[$accountType][$accountCategory['accountGroup']][$b['accountID']][$i];
                    $accountCategoriesSubTotals[$accountCategory['accountGroup']][$i] += $data[$accountType][$accountCategory['accountGroup']][$b['accountID']][$i];

                    $accountCategoriesSubTotals[$accountCategory['accountGroup']]['quarterVariance'][$i] += $data[$accountType][$accountCategory['accountGroup']][$b['accountID']][$i];
                }
            }
        }

        $period -= 1;
    }
}


if (is_array($data[INCOME])) {
    $computations = withAccountCategoriesComputations($data[INCOME], INCOME);
    $data[INCOME] = $computations['data'];
    $subtotals[INCOME]['variance'] = $computations['subTotals']['variance'];
    $subtotals[INCOME]['variancePercentage'] = $computations['subTotals']['variancePercentage'];
    $subtotals[INCOME]['varianceYTD'] = $computations['subTotals']['varianceYTD'];
    $subtotals[INCOME]['variancePercentageYTD'] = $computations['subTotals']['variancePercentageYTD'];
    $subtotals[INCOME]['variancePYYTD'] = $computations['subTotals']['variancePYYTD'];
    $subtotals[INCOME]['variancePercentagePYYTD'] = $computations['subTotals']['variancePercentagePYYTD'];
    $subtotals[INCOME]['variancePY'] = $computations['subTotals']['variancePY'];
    $subtotals[INCOME]['variancePercentagePY'] = $computations['subTotals']['variancePercentagePY'];

    $accountCategoriesSubTotalsComputations = $computations['subTotalAccountCategory'];
    foreach ($data[INCOME] as $y => $x) {
        $accountCategoriesSubTotals[$y]['description'] = 'Sub-total';
        $accountCategoriesSubTotals[$y]['variance'] = $accountCategoriesSubTotalsComputations[$y]['variance'];
        $accountCategoriesSubTotals[$y]['varianceYTD'] = $accountCategoriesSubTotalsComputations[$y]['varianceYTD'];
        $accountCategoriesSubTotals[$y]['variancePercentage'] = $accountCategoriesSubTotalsComputations[$y]['variancePercentage'];
        $accountCategoriesSubTotals[$y]['variancePercentageYTD'] = $accountCategoriesSubTotalsComputations[$y]['variancePercentageYTD'];
        $accountCategoriesSubTotals[$y]['variancePYYTD'] = $accountCategoriesSubTotalsComputations[$y]['variancePYYTD'];
        $accountCategoriesSubTotals[$y]['variancePercentagePYYTD'] = $accountCategoriesSubTotalsComputations[$y]['variancePercentagePYYTD'];
        $accountCategoriesSubTotals[$y]['variancePY'] = $accountCategoriesSubTotalsComputations[$y]['variancePY'];
        $accountCategoriesSubTotals[$y]['variancePercentagePY'] = $accountCategoriesSubTotalsComputations[$y]['variancePercentagePY'];
    }
}

if (is_array($data[EXPENDITURE])) {

    $expenditureComputations = withAccountCategoriesComputations($data[EXPENDITURE], EXPENDITURE);
    $data[EXPENDITURE] = $expenditureComputations['data'];
    $subtotals[EXPENDITURE]['variance'] = $expenditureComputations['subTotals']['variance'];
    $subtotals[EXPENDITURE]['variancePercentage'] = $expenditureComputations['subTotals']['variancePercentage'];
    $subtotals[EXPENDITURE]['varianceYTD'] = $expenditureComputations['subTotals']['varianceYTD'];
    $subtotals[EXPENDITURE]['variancePercentageYTD'] = $expenditureComputations['subTotals']['variancePercentageYTD'];
    $subtotals[EXPENDITURE]['variancePYYTD'] = $expenditureComputations['subTotals']['variancePYYTD'];
    $subtotals[EXPENDITURE]['variancePercentagePYYTD'] = $expenditureComputations['subTotals']['variancePercentagePYYTD'];
    $subtotals[EXPENDITURE]['variancePY'] = $expenditureComputations['subTotals']['variancePY'];
    $subtotals[EXPENDITURE]['variancePercentagePY'] = $expenditureComputations['subTotals']['variancePercentagePY'];

    $accountCategoriesSubTotalsComputations = $expenditureComputations['subTotalAccountCategory'];
    foreach ($data[EXPENDITURE] as $y => $x) {
        $accountCategoriesSubTotals[$y]['description'] = 'Sub-total';
        $accountCategoriesSubTotals[$y]['variance'] = $accountCategoriesSubTotalsComputations[$y]['variance'];
        $accountCategoriesSubTotals[$y]['varianceYTD'] = $accountCategoriesSubTotalsComputations[$y]['varianceYTD'];
        $accountCategoriesSubTotals[$y]['variancePercentage'] = $accountCategoriesSubTotalsComputations[$y]['variancePercentage'];
        $accountCategoriesSubTotals[$y]['variancePercentageYTD'] = $accountCategoriesSubTotalsComputations[$y]['variancePercentageYTD'];
        $accountCategoriesSubTotals[$y]['variancePYYTD'] = $accountCategoriesSubTotalsComputations[$y]['variancePYYTD'];
        $accountCategoriesSubTotals[$y]['variancePercentagePYYTD'] = $accountCategoriesSubTotalsComputations[$y]['variancePercentagePYYTD'];
        $accountCategoriesSubTotals[$y]['variancePY'] = $accountCategoriesSubTotalsComputations[$y]['variancePY'];
        $accountCategoriesSubTotals[$y]['variancePercentagePY'] = $accountCategoriesSubTotalsComputations[$y]['variancePercentagePY'];
    }
}

if (is_array($expDisData[INCOME])) {
    $expDisIncomeComputations = withAccountCategoriesComputations($expDisData[INCOME], INCOME);
    $expDisData[INCOME] = $expDisIncomeComputations['data'];
    $subtotals['isExpDis']['variance'] = $expDisIncomeComputations['subTotals']['variance'];
    $subtotals['isExpDis']['variancePercentage'] = $expDisIncomeComputations['subTotals']['variancePercentage'];
    $subtotals['isExpDis']['varianceYTD'] = $expDisIncomeComputations['subTotals']['varianceYTD'];
    $subtotals['isExpDis']['variancePercentageYTD'] = $expDisIncomeComputations['subTotals']['variancePercentageYTD'];
    $subtotals['isExpDis']['variancePYYTD'] = $expDisIncomeComputations['subTotals']['variancePYYTD'];
    $subtotals['isExpDis']['variancePercentagePYYTD'] = $expDisIncomeComputations['subTotals']['variancePercentagePYYTD'];
    $subtotals['isExpDis']['variancePY'] = $expDisIncomeComputations['subTotals']['variancePY'];
    $subtotals['isExpDis']['variancePercentagePY'] = $expDisIncomeComputations['subTotals']['variancePercentagePY'];

    $accountCategoriesSubTotalsComputations = $expDisIncomeComputations['subTotalAccountCategory'];
    foreach ($expDisData[INCOME] as $y => $x) {
        $accountCategoriesSubTotals[$y]['description'] = 'Sub-total';
        $accountCategoriesSubTotals[$y]['variance'] = $accountCategoriesSubTotalsComputations[$y]['variance'];
        $accountCategoriesSubTotals[$y]['varianceYTD'] = $accountCategoriesSubTotalsComputations[$y]['varianceYTD'];
        $accountCategoriesSubTotals[$y]['variancePercentage'] = $accountCategoriesSubTotalsComputations[$y]['variancePercentage'];
        $accountCategoriesSubTotals[$y]['variancePercentageYTD'] = $accountCategoriesSubTotalsComputations[$y]['variancePercentageYTD'];
        $accountCategoriesSubTotals[$y]['variancePYYTD'] = $accountCategoriesSubTotalsComputations[$y]['variancePYYTD'];
        $accountCategoriesSubTotals[$y]['variancePercentagePYYTD'] = $accountCategoriesSubTotalsComputations[$y]['variancePercentagePYYTD'];
        $accountCategoriesSubTotals[$y]['variancePY'] = $accountCategoriesSubTotalsComputations[$y]['variancePY'];
        $accountCategoriesSubTotals[$y]['variancePercentagePY'] = $accountCategoriesSubTotalsComputations[$y]['variancePercentagePY'];
    }
}

if (is_array($expDisData[EXPENDITURE])) {
    $expDisExpenditureComputations = withAccountCategoriesComputations($expDisData[EXPENDITURE], BALANCESHEET);
    $expDisData[EXPENDITURE] = $expDisExpenditureComputations['data'];
    $subtotals['isExpDis']['variance'] = $expDisExpenditureComputations['subTotals']['variance'];
    $subtotals['isExpDis']['variancePercentage'] = $expDisExpenditureComputations['subTotals']['variancePercentage'];
    $subtotals['isExpDis']['varianceYTD'] = $expDisExpenditureComputations['subTotals']['varianceYTD'];
    $subtotals['isExpDis']['variancePercentageYTD'] = $expDisExpenditureComputations['subTotals']['variancePercentageYTD'];
    $subtotals['isExpDis']['variancePYYTD'] = $expDisExpenditureComputations['subTotals']['variancePYYTD'];
    $subtotals['isExpDis']['variancePercentagePYYTD'] = $expDisExpenditureComputations['subTotals']['variancePercentagePYYTD'];
    $subtotals['isExpDis']['variancePY'] = $expDisExpenditureComputations['subTotals']['variancePY'];
    $subtotals['isExpDis']['variancePercentagePY'] = $expDisExpenditureComputations['subTotals']['variancePercentagePY'];

    $accountCategoriesSubTotalsComputations = $expDisExpenditureComputations['subTotalAccountCategory'];
    foreach ($expDisData[EXPENDITURE] as $y => $x) {
        $accountCategoriesSubTotals[$y]['description'] = 'Sub-total';
        $accountCategoriesSubTotals[$y]['variance'] = $accountCategoriesSubTotalsComputations[$y]['variance'];
        $accountCategoriesSubTotals[$y]['varianceYTD'] = $accountCategoriesSubTotalsComputations[$y]['varianceYTD'];
        $accountCategoriesSubTotals[$y]['variancePercentage'] = $accountCategoriesSubTotalsComputations[$y]['variancePercentage'];
        $accountCategoriesSubTotals[$y]['variancePercentageYTD'] = $accountCategoriesSubTotalsComputations[$y]['variancePercentageYTD'];
        $accountCategoriesSubTotals[$y]['variancePYYTD'] = $accountCategoriesSubTotalsComputations[$y]['variancePYYTD'];
        $accountCategoriesSubTotals[$y]['variancePercentagePYYTD'] = $accountCategoriesSubTotalsComputations[$y]['variancePercentagePYYTD'];
        $accountCategoriesSubTotals[$y]['variancePY'] = $accountCategoriesSubTotalsComputations[$y]['variancePY'];
        $accountCategoriesSubTotals[$y]['variancePercentagePY'] = $accountCategoriesSubTotalsComputations[$y]['variancePercentagePY'];
    }
}

if (is_array($data[INCOME])) {
    foreach ($data[INCOME] as $y => $i) {
        ksort($data[INCOME][$y]);
    }
}
if (is_array($data[EXPENDITURE])) {
    foreach ($data[EXPENDITURE] as $y => $i) {
        ksort($data[EXPENDITURE][$y]);
    }
}
if (is_array($expDisData[INCOME])) {
    foreach ($expDisData[INCOME] as $y => $i) {
        ksort($expDisData[INCOME][$y]);
    }
}
if (is_array($expDisData[EXPENDITURE])) {
    foreach ($expDisData[EXPENDITURE] as $y => $i) {
        ksort($expDisData[EXPENDITURE][$y]);
    }
}

if (isset($subtotals[EXPENDITURE])) {
    // var_dump($subtotals[EXPENDITURE]);
    $subtotals[EXPENDITURE]['variancePY'] = $subtotals[EXPENDITURE][0] - $subtotals[EXPENDITURE]['actualPY'];
    $subtotals[EXPENDITURE]['variancePYYTD'] = $subtotals[EXPENDITURE]['ytd'] - $subtotals[EXPENDITURE]['priorYTD'];
    $subtotals[EXPENDITURE]['variancePercentagePYYTD'] = variancePercentage($subtotals[EXPENDITURE]['variancePYYTD'], $subtotals[EXPENDITURE]['ytd']);
}

if (isset($subtotals[INCOME])) {
    $subtotals[INCOME]['variancePY'] = $subtotals[INCOME][0] - $subtotals[INCOME]['actualPY'];
    $subtotals[INCOME]['variancePYYTD'] = $subtotals[INCOME]['ytd'] - $subtotals[INCOME]['priorYTD'];
    $subtotals[INCOME]['variancePercentagePYYTD'] = variancePercentage($subtotals[INCOME]['variancePYYTD'], $subtotals[INCOME]['ytd']);
}


$plTotal = $subtotals[INCOME];
$netProfitTotal = $subtotals[INCOME];
foreach ($subtotals[EXPENDITURE] as $a => $e) {
    if ($a !== 'variance' and $a !== 'varianceYTD') {
        $plTotal[$a] = number_format((float) $plTotal[$a] - (float) $e, 2, '.', '');
        $netProfitTotal[$a] = number_format((float) $netProfitTotal[$a] - (float) $e, 2, '.', '');
    } else {
        $plTotal[$a] = bcadd($plTotal[$a], $e, 2);
        $netProfitTotal[$a] = bcadd($netProfitTotal[$a], $e, 2);
    }
}

foreach ($subtotals['isExpDis'] as $a => $e) {
    $plTotal[$a] = bcsub($plTotal[$a], $e, 2);
}

$plTotal['variance'] = $plTotal[0] - $plTotal['budget'];
$plTotal['varianceYTD'] = $plTotal['ytd'] - $plTotal['budgetYTD'];
$plTotal['variancePY'] = $plTotal[0] - $plTotal['actualPY'];
$plTotal['variancePYYTD'] = $plTotal['ytd'] - $plTotal['priorYTD'];
$plTotal['variancePercentage'] = variancePercentage($plTotal['variance'], $plTotal['budget']);
$plTotal['variancePercentageYTD'] = variancePercentage($plTotal['varianceYTD'], $plTotal['budgetYTD']);
$plTotal['variancePercentagePYYTD'] = variancePercentage($plTotal['variancePYYTD'], $plTotal['ytd']);
$plTotal['variancePercentagePY'] = variancePercentage($plTotal['variancePY'], $plTotal[0]);

$netProfitTotal['variance'] = $netProfitTotal[0] - $netProfitTotal['budget'];
$netProfitTotal['varianceYTD'] = $netProfitTotal['ytd'] - $netProfitTotal['budgetYTD'];
$netProfitTotal['variancePY'] = $netProfitTotal[0] - $netProfitTotal['actualPY'];
$netProfitTotal['variancePYYTD'] = $netProfitTotal['ytd'] - $netProfitTotal['priorYTD'];
$netProfitTotal['variancePercentage'] = variancePercentage($netProfitTotal['variance'], $netProfitTotal['budget']);
$netProfitTotal['variancePercentageYTD'] = variancePercentage($netProfitTotal['varianceYTD'], $netProfitTotal['budgetYTD']);
$netProfitTotal['variancePercentagePYYTD'] = variancePercentage($netProfitTotal['variancePYYTD'], $netProfitTotal['ytd']);
$netProfitTotal['variancePercentagePY'] = variancePercentage($netProfitTotal['variancePY'], $netProfitTotal[0]);

$plTotal['description'] = 'Net profit after distributions';
$netProfitTotal['description'] = 'Net profit before distributions';

$year = $endDate['year'];

$otherAccounts = []; // getOwnerRemittanceAndBalanceSheetAccounts('EXP.DIS');

$plTotal['description'] = 'Net Cash';
$netProfitTotal['description'] = 'Net cash before distributions & GST';
//            $otherRemittanceAndBSMovements['description'] = 'Owner Remittance and Other Balance Sheet Movements';

$linkedAccounts = dbGetLinkedAccounts();

$gstReceived['description'] = 'GST Received';
$gstReceivedYtd = dbGetTrialBalanceYTDByAccount($properties, $linkedAccounts['gstOutputTax'], $endDate['period'], $endDate['year']);
$gstReceived['ytd'] = $gstReceivedYtd['balanceCash'] * -1;
$gstReceived['variancePercentageYTD'] = 100;
$gstReceived['varianceYTD'] = $gstReceived['ytd'];

$gstPaid['description'] = 'GST Paid';
$gstPaidYtd = dbGetTrialBalanceYTDByAccount($properties, $linkedAccounts['gstInputTax'], $endDate['period'], $endDate['year']);
$gstPaid['ytd'] = $gstPaidYtd['balanceCash'] * -1;
$gstPaid['variancePercentageYTD'] = 100;
$gstPaid['varianceYTD'] = $gstPaid['ytd'];

$gstSubTotal['description'] = 'Net GST Received / Paid';
$gstSubTotal['ytd'] = $gstReceived['ytd'] + $gstPaid['ytd'];
$gstSubTotal['variancePercentageYTD'] = 100;
$gstSubTotal['varianceYTD'] = $gstSubTotal['ytd'];

$netProfitTotal2 = $netProfitTotal;
$netProfitTotal2['description'] = 'Net cash before distributions';
$netProfitTotal2['ytd'] = $netProfitTotal['ytd'] + $gstSubTotal['ytd'];

$otherRemittanceAndBSMovements['description'] = 'Balance Sheet Movements';
$otherRemittanceAndBSMovementsYtd = dbGetTrialBalanceYTDByAccounts($properties, $otherAccounts, $endDate['period'], $endDate['year']);
$otherRemittanceAndBSMovements['ytd'] = $otherRemittanceAndBSMovementsYtd['balanceCash'] * -1;

$gstPaid['description'] = 'GST Paid';
$gstPaidYtd = dbGetTrialBalanceYTDByAccount($properties, $linkedAccounts['gstInputTax'], $endDate['period'], $endDate['year']);
$gstPaid['ytd'] = $gstPaidYtd['balanceCash'] * -1;

$plTotal['ytd'] = $netProfitTotal2['ytd'] - $subtotals['isExpDis']['ytd'];

$cashOpening['description'] = 'Opening Cash Balance';
$cashOpeningYtd = dbGetOpeningTrialBalanceByAccount($properties, $linkedAccounts['bank'], 1, $year);

$cashOpening['ytd'] = $cashOpeningYtd['balanceCash'];


$cashClosing['description'] = 'Closing Cash Balance';
$cashClosing['ytd'] = $cashOpening['ytd'] + $plTotal['ytd'] + $otherRemittanceAndBSMovements['ytd'];

// get amounts per period
// reset periods
$period = 12;
$per_to = null;
$year = $endDate['year'];

for ($i = 0; $i < $_periods; $i++) {

    $gstReceivedYtd = dbGetTrialBalanceForPeriodByAccount($properties, $linkedAccounts['gstOutputTax'], $period, $year, $per_to);
    $gstReceived[$i] = $gstReceivedYtd['balanceCash'] * -1;
    $gstReceived['variance' . $i] = $gstReceivedYtd['balanceCash'] * -1;

    $gstPaidYtd = dbGetTrialBalanceForPeriodByAccount($properties, $linkedAccounts['gstInputTax'], $period, $year, $per_to);
    $gstPaid[$i] = $gstPaidYtd['balanceCash'] * -1;
    $gstPaid['variance' . $i] = $gstPaidYtd['balanceCash'] * -1;

    $otherRemittanceAndBSMovementsP = dbGetTrialBalanceForPeriodByAccounts($properties, $otherAccounts, $period, $year, $per_to);
    $otherRemittanceAndBSMovements[$i] = $otherRemittanceAndBSMovementsP['balanceCash'] * -1;

    $gstSubTotal[$i] = ($gstReceived[$i] + $gstPaid[$i]);
    $gstSubTotal['variance' . $i] = $gstSubTotal[$i];

    $netProfitTotal2[$i] = $netProfitTotal[$i] + $gstSubTotal[$i];

    $plTotal[$i] = $netProfitTotal2[$i] - $subtotals['isExpDis'][$i];

    $cashOpeningYtd = dbGetOpeningTrialBalanceByAccount($properties, $linkedAccounts['bank'], $period, $year);
    $cashOpening[$i] = $cashOpeningYtd['balanceCash'];

    $cashClosing[$i] = $cashOpening[$i] + $plTotal[$i] + $otherRemittanceAndBSMovements[$i];

    [$period, $year] = periodBefore($period, $year);
}

// get amounts for budget
$gstReceivedBudgetYtdIndex = array_search($linkedAccounts['gstOutpuTax'], array_column($_budgetYTD, 'accountID'));

if ($gstReceivedBudgetYtdIndex !== false) {
    $gstReceived['budgetYTD'] = $_budgetYTD[$gstReceivedBudgetYtdIndex]['budgetCash'];
    $gstReceived['varianceYTD'] = variance($gstReceived[0], $gstReceived['budgetYTD']);
    $gstReceived['variancePercentageYTD'] = variancePercentage($gstReceived['varianceYTD'], $gstReceived['budgetYTD']);
}

$gstReceivedBudgetIndex = array_search($linkedAccounts['gstOutpuTax'], array_column($_budget, 'accountID'));

if ($gstReceivedBudgetIndex !== false) {
    $gstReceived['budget'] = $_budget[$gstReceivedBudgetIndex]['budgetCash'];
    $gstReceived['variance'] = variance($gstReceived[0], $gstReceived['budget']);
    $gstReceived['variancePercentage'] = variancePercentage($gstReceived['variance'], $gstReceived['budget']);
}

$gstPaidBudgetYtdIndex = array_search($linkedAccounts['gstInputTax'], array_column($_budgetYTD, 'accountID'));

if ($gstPaidBudgetYtdIndex !== false) {
    $gstPaid['budgetYTD'] = $_budgetYTD[$gstPaidBudgetYtdIndex]['budgetCash'];
    $gstPaid['varianceYTD'] = variance($gstPaid[0], $gstPaid['budgetYTD']);
    $gstPaid['variancePercentageYTD'] = variancePercentage($gstPaid['varianceYTD'], $gstPaid['budgetYTD']);
}

$gstPaidBudgetIndex = array_search($linkedAccounts['gstInputTax'], array_column($_budget, 'accountID'));

if ($gstPaidBudgetIndex !== false) {
    $gstPaid['budget'] = $_budget[$gstPaidBudgetIndex]['budgetCash'];
    $gstPaid['variance'] = variance($gstPaid[0], $gstPaid['budget']);
    $gstPaid['variancePercentage'] = variancePercentage($gstPaid['variance'], $gstPaid['budget']);
}

// end of amounts for budget

// get amounts for prior year
$period = 12;
$per_to = null;
$year = $endDate['year'];

$gstReceivedActualPY = dbGetTrialBalanceForPeriodByAccount($properties, $linkedAccounts['gstOutputTax'], $period, $year - 1, $per_to);
$gstReceivedYtdPY = dbGetTrialBalanceYTDByAccount($properties, $linkedAccounts['gstOutputTax'], $endDate['period'], $endDate['year'] - 1);
$gstReceived['actualPY'] = $gstReceivedActualPY['balanceCash'] * -1;
$gstReceived['priorYTD'] = $gstReceivedYtdPY['balanceCash'] * -1;
$gstReceived['variancePY'] = variance($gstReceived[0], $gstReceived['actualPY']);
$gstReceived['variancePercentagePY'] = variancePercentage($gstReceived['variancePY'], $gstReceived[0]);
$gstReceived['variancePYYTD'] = variance($gstReceived['ytd'], $gstReceived['priorYTD']);
$gstReceived['variancePercentagePYYTD'] = variancePercentage($gstReceived['variancePYYTD'], $gstReceived['ytd']);
$gstReceived['variancePercentage'] = 100;
$gstReceived['variance'] = $gstReceived[0];

$gstPaidActualPY = dbGetTrialBalanceForPeriodByAccount($properties, $linkedAccounts['gstInputTax'], $period, $year - 1, $per_to);
$gstPaidYtdPY = dbGetTrialBalanceYTDByAccount($properties, $linkedAccounts['gstInputTax'], $endDate['period'], $endDate['year'] - 1);
$gstPaid['actualPY'] = $gstPaidActualPY['balanceCash'] * -1;
$gstPaid['priorYTD'] = $gstPaidYtdPY['balanceCash'] * -1;
$gstPaid['variancePY'] = variance($gstPaid[0], $gstPaid['actualPY']);
$gstPaid['variancePercentagePY'] = variancePercentage($gstPaid['variancePY'], $gstPaid[0]);
$gstPaid['variancePYYTD'] = variance($gstPaid['ytd'], $gstPaid['priorYTD']);
$gstPaid['variancePercentagePYYTD'] = variancePercentage($gstPaid['variancePYYTD'], $gstPaid['ytd']);
$gstPaid['variancePercentage'] = 100;
$gstPaid['variance'] = $gstPaid[0];

$gstSubTotal['actualPY'] = $gstReceived['actualPY'] + $gstPaid['actualPY'];
$gstSubTotal['priorYTD'] = $gstReceived['priorYTD'] + $gstPaid['priorYTD'];
$gstSubTotal['variancePY'] = variance($gstSubTotal[0], $gstSubTotal['actualPY']);
$gstSubTotal['variancePercentagePY'] = variancePercentage($gstSubTotal['variancePY'], $gstSubTotal[0]);
$gstSubTotal['variancePYYTD'] = variance($gstSubTotal['ytd'], $gstSubTotal['priorYTD']);
$gstSubTotal['variancePercentagePYYTD'] = variancePercentage($gstSubTotal['variancePYYTD'], $gstSubTotal['ytd']);
$gstSubTotal['variancePercentage'] = 100;
$gstSubTotal['variance'] = $gstSubTotal[0];

$netProfitTotal2['actualPY'] = $netProfitTotal['actualPY'] + $gstSubTotal['actualPY'];
$netProfitTotal2['priorYTD'] = $netProfitTotal['priorYTD'] + $gstSubTotal['priorYTD'];
$netProfitTotal2['variancePY'] = variance($netProfitTotal2[0], $netProfitTotal2['actualPY']);
$netProfitTotal2['variancePercentagePY'] = variancePercentage($netProfitTotal2['variancePY'], $netProfitTotal2[0]);
$netProfitTotal2['variancePYYTD'] = variance($netProfitTotal2['ytd'], $netProfitTotal2['priorYTD']);
$netProfitTotal2['variancePercentagePYYTD'] = variancePercentage($netProfitTotal2['variancePYYTD'], $netProfitTotal2['ytd']);

$otherRemittanceAndBSMovementsActualPY = dbGetTrialBalanceForPeriodByAccounts($properties, $otherAccounts, $period, $year - 1, $per_to);
$otherRemittanceAndBSMovementsYtdPY = dbGetTrialBalanceYTDByAccounts($properties, $otherAccounts, $endDate['period'], $endDate['year'] - 1);
$otherRemittanceAndBSMovements['actualPY'] = $otherRemittanceAndBSMovementsActualPY['balanceCash'] * -1;
$otherRemittanceAndBSMovements['priorYTD'] = $otherRemittanceAndBSMovementsYtdPY['balanceCash'] * -1;
$otherRemittanceAndBSMovements['variancePY'] = variance($otherRemittanceAndBSMovements[0], $otherRemittanceAndBSMovements['actualPY']);
$otherRemittanceAndBSMovements['variancePercentagePY'] = variancePercentage($otherRemittanceAndBSMovements['variancePY'], $otherRemittanceAndBSMovements[0]);
$otherRemittanceAndBSMovements['variancePYYTD'] = variance($otherRemittanceAndBSMovements['ytd'], $otherRemittanceAndBSMovements['priorYTD']);
$otherRemittanceAndBSMovements['variancePercentagePYYTD'] = variancePercentage($otherRemittanceAndBSMovements['variancePYYTD'], $otherRemittanceAndBSMovements['ytd']);

$plTotal['actualPY'] = $netProfitTotal2['actualPY'] - $subtotals['isExpDis']['actualPY'];
$plTotal['priorYTD'] = $netProfitTotal2['priorYTD'] - $subtotals['isExpDis']['priorYTD'];
$plTotal['variancePY'] = variance($plTotal[0], $plTotal['actualPY']);
$plTotal['variancePercentagePY'] = variancePercentage($plTotal['variancePY'], $plTotal[0]);
$plTotal['variancePYYTD'] = variance($plTotal['ytd'], $plTotal['priorYTD']);
$plTotal['variancePercentagePYYTD'] = variancePercentage($plTotal['variancePYYTD'], $plTotal['ytd']);

$cashOpeningActualPY = dbGetOpeningTrialBalanceByAccount($properties, $linkedAccounts['bank'], $period, $year - 1);
$cashOpeningYtdPY = dbGetOpeningTrialBalanceByAccount($properties, $linkedAccounts['bank'], 1, $year - 1);
$cashOpening['actualPY'] = $cashOpeningActualPY['balanceCash'];
$cashOpening['priorYTD'] = $cashOpeningYtdPY['balanceCash'];
$cashOpening['variancePY'] = variance($cashOpening[0], $cashOpening['actualPY']);
$cashOpening['variancePercentagePY'] = variancePercentage($cashOpening['variancePY'], $cashOpening[0]);
$cashOpening['variancePYYTD'] = variance($cashOpening['ytd'], $cashOpening['priorYTD']);
$cashOpening['variancePercentagePYYTD'] = variancePercentage($cashOpening['variancePYYTD'], $cashOpening['ytd']);

$cashClosing['actualPY'] = $cashOpening['actualPY'] + $plTotal['actualPY'];
$cashClosing['priorYTD'] = $cashOpening['priorYTD'] + $plTotal['priorYTD'];
$cashClosing['variancePY'] = variance($cashClosing[0], $cashClosing['actualPY']);
$cashClosing['variancePercentagePY'] = variancePercentage($cashClosing['variancePY'], $cashClosing[0]);
$cashClosing['variancePYYTD'] = variance($cashClosing['ytd'], $cashClosing['priorYTD']);
$cashClosing['variancePercentagePYYTD'] = variancePercentage($cashClosing['variancePYYTD'], $cashClosing['ytd']);


$line = 478;
foreach ($accountGroups as $acc => $row) {
    if ($line < 50) {
        $pdf->setlinewidth(0.5);
        $pdf->moveto(18, 515);
        $pdf->lineto(18, $line);
        $pdf->stroke();

        $pdf->setlinewidth(0.5);
        $pdf->moveto(824, 515);
        $pdf->lineto(824, $line);
        $pdf->stroke();

        $pdf->setlinewidth(0.5);
        $pdf->moveto(18, $line);
        $pdf->lineto(824, $line);
        $pdf->stroke();

        // insert tracc footer
        $traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'propertyFinancialReport', A4_LANDSCAPE);
        $traccFooter->prerender($pdf);

        $pdf->end_page_ext('');

        newAnnualPage($startDate, $endDate);

        $line = 478;

    }

    $pdf->setColorExt('fill', 'rgb', 0.88, 0.88, 0.88, 0);
    $pdf->rect(18, $line + 2, 806, 15);
    $pdf->fill();
    $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);

    $pdf->setlinewidth(0.5);
    $pdf->moveto(18, $line + 2);
    $pdf->lineto(824, $line + 2);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(18, $line + 17);
    $pdf->lineto(824, $line + 17);
    $pdf->stroke();

    $pdf->setFontExt($_fonts['Helvetica-Bold'], 9);
    $pdf->showBoxed($row, 22, $line, 100, 15, 'left', '');
    $line -= 15;

    foreach ($data[$acc] as $index => $row2) {
        $count = 0;
        foreach ($row2 as $key => $rowData) {

            if ($line < 50) {

                $pdf->setlinewidth(0.5);
                $pdf->moveto(18, 515);
                $pdf->lineto(18, $line);
                $pdf->stroke();

                $pdf->setlinewidth(0.5);
                $pdf->moveto(824, 515);
                $pdf->lineto(824, $line);
                $pdf->stroke();

                $pdf->setlinewidth(0.5);
                $pdf->moveto(18, $line);
                $pdf->lineto(824, $line);
                $pdf->stroke();

                // insert tracc footer
                $traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'propertyFinancialReport', A4_LANDSCAPE);
                $traccFooter->prerender($pdf);

                $pdf->end_page_ext('');

                newAnnualPage($startDate, $endDate);

                $line = 478;

            }

            if ($count == 0) {
                $pdf->setColorExt('fill', 'rgb', 0.88, 0.88, 0.88, 0);
                $pdf->rect(18, $line + 2, 806, 15);
                $pdf->fill();
                $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);

                $pdf->setlinewidth(0.5);
                $pdf->moveto(18, $line + 2);
                $pdf->lineto(824, $line + 2);
                $pdf->stroke();

                $pdf->setlinewidth(0.5);
                $pdf->moveto(18, $line + 17);
                $pdf->lineto(824, $line + 17);
                $pdf->stroke();

                $pdf->setFontExt($_fonts['Helvetica-Bold'], 9);
                $pdf->showBoxed($rowData['accountGroupName'], 22, $line, 200, 15, 'left', '');
                $line -= 15;
                $count = 1;
            }


            $pdf->setFontExt($_fonts['Helvetica'], 8);
            $pdf->showBoxed($rowData['accountID'], 50, $line, 30, 15, 'left', '');
            $pdf->showBoxed($rowData['description'], 80, $line, 120, 15, 'left', '');
            $pdf->showBoxed(toMoney($rowData['11']), 170, $line, 50, 15, 'right', '');
            $pdf->showBoxed(toMoney($rowData['10']), 220, $line, 50, 15, 'right', '');
            $pdf->showBoxed(toMoney($rowData['9']), 270, $line, 50, 15, 'right', '');
            $pdf->showBoxed(toMoney($rowData['8']), 320, $line, 50, 15, 'right', '');
            $pdf->showBoxed(toMoney($rowData['7']), 370, $line, 50, 15, 'right', '');
            $pdf->showBoxed(toMoney($rowData['6']), 420, $line, 50, 15, 'right', '');
            $pdf->showBoxed(toMoney($rowData['5']), 470, $line, 50, 15, 'right', '');
            $pdf->showBoxed(toMoney($rowData['4']), 520, $line, 50, 15, 'right', '');
            $pdf->showBoxed(toMoney($rowData['3']), 570, $line, 50, 15, 'right', '');
            $pdf->showBoxed(toMoney($rowData['2']), 620, $line, 50, 15, 'right', '');
            $pdf->showBoxed(toMoney($rowData['1']), 670, $line, 50, 15, 'right', '');
            $pdf->showBoxed(toMoney($rowData['0']), 720, $line, 50, 15, 'right', '');
            $pdf->showBoxed(toMoney($rowData['ytd']), 770, $line, 52, 15, 'right', '');

            $line -= 15;
        }

        $pdf->setColorExt('fill', 'rgb', 0.88, 0.88, 0.88, 0);
        $pdf->rect(18, $line + 2, 806, 15);
        $pdf->fill();
        $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);

        $pdf->setlinewidth(0.5);
        $pdf->moveto(18, $line + 2);
        $pdf->lineto(824, $line + 2);
        $pdf->stroke();

        $pdf->setlinewidth(0.5);
        $pdf->moveto(18, $line + 17);
        $pdf->lineto(824, $line + 17);
        $pdf->stroke();

        $pdf->setFontExt($_fonts['Helvetica-Bold'], 9);
        $pdf->showBoxed('Sub-total', 80, $line, 100, 15, 'left', '');
        $pdf->showBoxed(toMoney($accountCategoriesSubTotals[$index][11]), 170, $line, 50, 15, 'right', '');
        $pdf->showBoxed(toMoney($accountCategoriesSubTotals[$index][10]), 220, $line, 50, 15, 'right', '');
        $pdf->showBoxed(toMoney($accountCategoriesSubTotals[$index][9]), 270, $line, 50, 15, 'right', '');
        $pdf->showBoxed(toMoney($accountCategoriesSubTotals[$index][8]), 320, $line, 50, 15, 'right', '');
        $pdf->showBoxed(toMoney($accountCategoriesSubTotals[$index][7]), 370, $line, 50, 15, 'right', '');
        $pdf->showBoxed(toMoney($accountCategoriesSubTotals[$index][6]), 420, $line, 50, 15, 'right', '');
        $pdf->showBoxed(toMoney($accountCategoriesSubTotals[$index][5]), 470, $line, 50, 15, 'right', '');
        $pdf->showBoxed(toMoney($accountCategoriesSubTotals[$index][4]), 520, $line, 50, 15, 'right', '');
        $pdf->showBoxed(toMoney($accountCategoriesSubTotals[$index][3]), 570, $line, 50, 15, 'right', '');
        $pdf->showBoxed(toMoney($accountCategoriesSubTotals[$index][2]), 620, $line, 50, 15, 'right', '');
        $pdf->showBoxed(toMoney($accountCategoriesSubTotals[$index][1]), 670, $line, 50, 15, 'right', '');
        $pdf->showBoxed(toMoney($accountCategoriesSubTotals[$index][0]), 720, $line, 50, 15, 'right', '');
        $pdf->showBoxed(toMoney($accountCategoriesSubTotals[$index]['ytd']), 770, $line, 52, 15, 'right', '');
        $line -= 15;
    }

    if ($line < 50) {
        $pdf->setlinewidth(0.5);
        $pdf->moveto(18, 515);
        $pdf->lineto(18, $line);
        $pdf->stroke();

        $pdf->setlinewidth(0.5);
        $pdf->moveto(824, 515);
        $pdf->lineto(824, $line);
        $pdf->stroke();

        $pdf->setlinewidth(0.5);
        $pdf->moveto(18, $line);
        $pdf->lineto(824, $line);
        $pdf->stroke();

        // insert tracc footer
        $traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'propertyFinancialReport', A4_LANDSCAPE);
        $traccFooter->prerender($pdf);

        $pdf->end_page_ext('');

        newAnnualPage($startDate, $endDate);

        $line = 478;

    }

    $pdf->setColorExt('fill', 'rgb', 0.88, 0.88, 0.88, 0);
    $pdf->rect(18, $line + 2, 806, 15);
    $pdf->fill();
    $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);

    $pdf->setlinewidth(0.5);
    $pdf->moveto(18, $line + 2);
    $pdf->lineto(824, $line + 2);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(18, $line + 17);
    $pdf->lineto(824, $line + 17);
    $pdf->stroke();

    $pdf->setFontExt($_fonts['Helvetica-Bold'], 9);
    $pdf->showBoxed($subtotals[$acc]['description'], 22, $line, 100, 15, 'left', '');
    $pdf->showBoxed(toMoney($subtotals[$acc][11]), 170, $line, 50, 15, 'right', '');
    $pdf->showBoxed(toMoney($subtotals[$acc][10]), 220, $line, 50, 15, 'right', '');
    $pdf->showBoxed(toMoney($subtotals[$acc][9]), 270, $line, 50, 15, 'right', '');
    $pdf->showBoxed(toMoney($subtotals[$acc][8]), 320, $line, 50, 15, 'right', '');
    $pdf->showBoxed(toMoney($subtotals[$acc][7]), 370, $line, 50, 15, 'right', '');
    $pdf->showBoxed(toMoney($subtotals[$acc][6]), 420, $line, 50, 15, 'right', '');
    $pdf->showBoxed(toMoney($subtotals[$acc][5]), 470, $line, 50, 15, 'right', '');
    $pdf->showBoxed(toMoney($subtotals[$acc][4]), 520, $line, 50, 15, 'right', '');
    $pdf->showBoxed(toMoney($subtotals[$acc][3]), 570, $line, 50, 15, 'right', '');
    $pdf->showBoxed(toMoney($subtotals[$acc][2]), 620, $line, 50, 15, 'right', '');
    $pdf->showBoxed(toMoney($subtotals[$acc][1]), 670, $line, 50, 15, 'right', '');
    $pdf->showBoxed(toMoney($subtotals[$acc][0]), 720, $line, 50, 15, 'right', '');
    $pdf->showBoxed(toMoney($subtotals[$acc]['ytd']), 770, $line, 52, 15, 'right', '');
    $line -= 30;
}

$pdf->setColorExt('fill', 'rgb', 0.88, 0.88, 0.88, 0);
$pdf->rect(18, $line + 2, 806, 15);
$pdf->fill();
$pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);

$pdf->setlinewidth(0.5);
$pdf->moveto(18, $line + 2);
$pdf->lineto(824, $line + 2);
$pdf->stroke();

$pdf->setlinewidth(0.5);
$pdf->moveto(18, $line + 17);
$pdf->lineto(824, $line + 17);
$pdf->stroke();

$pdf->setFontExt($_fonts['Helvetica-Bold'], 9);
$pdf->showBoxed($netProfitTotal['description'], 22, $line, 200, 15, 'left', '');
$pdf->showBoxed(toMoney($netProfitTotal[11]), 170, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($netProfitTotal[10]), 220, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($netProfitTotal[9]), 270, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($netProfitTotal[8]), 320, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($netProfitTotal[7]), 370, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($netProfitTotal[6]), 420, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($netProfitTotal[5]), 470, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($netProfitTotal[4]), 520, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($netProfitTotal[3]), 570, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($netProfitTotal[2]), 620, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($netProfitTotal[1]), 670, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($netProfitTotal[0]), 720, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($netProfitTotal['ytd']), 770, $line, 52, 15, 'right', '');
$line -= 15;

if ($line < 50) {
    $pdf->setlinewidth(0.5);
    $pdf->moveto(18, 515);
    $pdf->lineto(18, $line);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(824, 515);
    $pdf->lineto(824, $line);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(18, $line);
    $pdf->lineto(824, $line);
    $pdf->stroke();

    // insert tracc footer
    $traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'propertyFinancialReport', A4_LANDSCAPE);
    $traccFooter->prerender($pdf);

    $pdf->end_page_ext('');

    newAnnualPage($startDate, $endDate);

    $line = 478;

}

$pdf->setFontExt($_fonts['Helvetica'], 8);
$pdf->showBoxed($gstReceived['description'], 80, $line, 200, 15, 'left', '');
$pdf->showBoxed(toMoney($gstReceived[11]), 170, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($gstReceived[10]), 220, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($gstReceived[9]), 270, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($gstReceived[8]), 320, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($gstReceived[7]), 370, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($gstReceived[6]), 420, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($gstReceived[5]), 470, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($gstReceived[4]), 520, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($gstReceived[3]), 570, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($gstReceived[2]), 620, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($gstReceived[1]), 670, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($gstReceived[0]), 720, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($gstReceived['ytd']), 770, $line, 52, 15, 'right', '');
$line -= 15;

if ($line < 50) {
    $pdf->setlinewidth(0.5);
    $pdf->moveto(18, 515);
    $pdf->lineto(18, $line);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(824, 515);
    $pdf->lineto(824, $line);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(18, $line);
    $pdf->lineto(824, $line);
    $pdf->stroke();

    // insert tracc footer
    $traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'propertyFinancialReport', A4_LANDSCAPE);
    $traccFooter->prerender($pdf);

    $pdf->end_page_ext('');

    newAnnualPage($startDate, $endDate);

    $line = 478;

}

$pdf->setFontExt($_fonts['Helvetica'], 8);
$pdf->showBoxed($gstPaid['description'], 80, $line, 200, 15, 'left', '');
$pdf->showBoxed(toMoney($gstPaid[11]), 170, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($gstPaid[10]), 220, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($gstPaid[9]), 270, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($gstPaid[8]), 320, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($gstPaid[7]), 370, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($gstPaid[6]), 420, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($gstPaid[5]), 470, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($gstPaid[4]), 520, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($gstPaid[3]), 570, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($gstPaid[2]), 620, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($gstPaid[1]), 670, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($gstPaid[0]), 720, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($gstPaid['ytd']), 770, $line, 52, 15, 'right', '');
$line -= 15;

if ($line < 50) {
    $pdf->setlinewidth(0.5);
    $pdf->moveto(18, 515);
    $pdf->lineto(18, $line);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(824, 515);
    $pdf->lineto(824, $line);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(18, $line);
    $pdf->lineto(824, $line);
    $pdf->stroke();

    // insert tracc footer
    $traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'propertyFinancialReport', A4_LANDSCAPE);
    $traccFooter->prerender($pdf);

    $pdf->end_page_ext('');

    newAnnualPage($startDate, $endDate);

    $line = 478;

}

$pdf->setColorExt('fill', 'rgb', 0.88, 0.88, 0.88, 0);
$pdf->rect(18, $line + 2, 806, 15);
$pdf->fill();
$pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);

$pdf->setlinewidth(0.5);
$pdf->moveto(18, $line + 2);
$pdf->lineto(824, $line + 2);
$pdf->stroke();

$pdf->setlinewidth(0.5);
$pdf->moveto(18, $line + 17);
$pdf->lineto(824, $line + 17);
$pdf->stroke();

$pdf->setFontExt($_fonts['Helvetica-Bold'], 9);
$pdf->showBoxed($gstSubTotal['description'], 22, $line, 200, 15, 'left', '');
$pdf->showBoxed(toMoney($gstSubTotal[11]), 170, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($gstSubTotal[10]), 220, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($gstSubTotal[9]), 270, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($gstSubTotal[8]), 320, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($gstSubTotal[7]), 370, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($gstSubTotal[6]), 420, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($gstSubTotal[5]), 470, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($gstSubTotal[4]), 520, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($gstSubTotal[3]), 570, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($gstSubTotal[2]), 620, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($gstSubTotal[1]), 670, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($gstSubTotal[0]), 720, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($gstSubTotal['ytd']), 770, $line, 52, 15, 'right', '');
$line -= 15;

if ($line < 50) {
    $pdf->setlinewidth(0.5);
    $pdf->moveto(18, 515);
    $pdf->lineto(18, $line);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(824, 515);
    $pdf->lineto(824, $line);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(18, $line);
    $pdf->lineto(824, $line);
    $pdf->stroke();

    // insert tracc footer
    $traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'propertyFinancialReport', A4_LANDSCAPE);
    $traccFooter->prerender($pdf);

    $pdf->end_page_ext('');

    newAnnualPage($startDate, $endDate);

    $line = 478;

}

$pdf->setColorExt('fill', 'rgb', 0.88, 0.88, 0.88, 0);
$pdf->rect(18, $line + 2, 806, 15);
$pdf->fill();
$pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);

$pdf->setlinewidth(0.5);
$pdf->moveto(18, $line + 2);
$pdf->lineto(824, $line + 2);
$pdf->stroke();

$pdf->setlinewidth(0.5);
$pdf->moveto(18, $line + 17);
$pdf->lineto(824, $line + 17);
$pdf->stroke();

$pdf->setFontExt($_fonts['Helvetica-Bold'], 9);
$pdf->showBoxed($netProfitTotal2['description'], 22, $line, 200, 15, 'left', '');
$pdf->showBoxed(toMoney($netProfitTotal2[11]), 170, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($netProfitTotal2[10]), 220, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($netProfitTotal2[9]), 270, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($netProfitTotal2[8]), 320, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($netProfitTotal2[7]), 370, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($netProfitTotal2[6]), 420, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($netProfitTotal2[5]), 470, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($netProfitTotal2[4]), 520, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($netProfitTotal2[3]), 570, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($netProfitTotal2[2]), 620, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($netProfitTotal2[1]), 670, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($netProfitTotal2[0]), 720, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($netProfitTotal2['ytd']), 770, $line, 52, 15, 'right', '');
$line -= 15;

if ($line < 50) {
    $pdf->setlinewidth(0.5);
    $pdf->moveto(18, 515);
    $pdf->lineto(18, $line);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(824, 515);
    $pdf->lineto(824, $line);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(18, $line);
    $pdf->lineto(824, $line);
    $pdf->stroke();

    // insert tracc footer
    $traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'propertyFinancialReport', A4_LANDSCAPE);
    $traccFooter->prerender($pdf);

    $pdf->end_page_ext('');

    newAnnualPage($startDate, $endDate);

    $line = 478;

}

$pdf->setColorExt('fill', 'rgb', 0.88, 0.88, 0.88, 0);
$pdf->rect(18, $line + 2, 806, 15);
$pdf->fill();
$pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);

$pdf->setlinewidth(0.5);
$pdf->moveto(18, $line + 2);
$pdf->lineto(824, $line + 2);
$pdf->stroke();

$pdf->setlinewidth(0.5);
$pdf->moveto(18, $line + 17);
$pdf->lineto(824, $line + 17);
$pdf->stroke();

$pdf->setFontExt($_fonts['Helvetica-Bold'], 9);
$pdf->showBoxed($otherRemittanceAndBSMovements['description'], 22, $line, 200, 15, 'left', '');
// $pdf->showBoxed (toMoney( $otherRemittanceAndBSMovements[11]), 170, $line, 50, 15, "right", "");
// $pdf->showBoxed (toMoney( $otherRemittanceAndBSMovements[10]), 220, $line, 50, 15, "right", "");
// $pdf->showBoxed (toMoney( $otherRemittanceAndBSMovements[9]), 270, $line, 50, 15, "right", "");
// $pdf->showBoxed (toMoney( $otherRemittanceAndBSMovements[8]), 320, $line, 50, 15, "right", "");
// $pdf->showBoxed (toMoney( $otherRemittanceAndBSMovements[7]), 370, $line, 50, 15, "right", "");
// $pdf->showBoxed (toMoney( $otherRemittanceAndBSMovements[6]), 420, $line, 50, 15, "right", "");
// $pdf->showBoxed (toMoney( $otherRemittanceAndBSMovements[5]), 470, $line, 50, 15, "right", "");
// $pdf->showBoxed (toMoney( $otherRemittanceAndBSMovements[4]), 520, $line, 50, 15, "right", "");
// $pdf->showBoxed (toMoney( $otherRemittanceAndBSMovements[3]), 570, $line, 50, 15, "right", "");
// $pdf->showBoxed (toMoney( $otherRemittanceAndBSMovements[2]), 620, $line, 50, 15, "right", "");
// $pdf->showBoxed (toMoney( $otherRemittanceAndBSMovements[1]), 670, $line, 50, 15, "right", "");
// $pdf->showBoxed (toMoney( $otherRemittanceAndBSMovements[0]), 720, $line, 50, 15, "right", "");
// $pdf->showBoxed (toMoney( $otherRemittanceAndBSMovements['ytd']), 770, $line, 52, 15, "right", "");
$distributionAccount = [];
foreach ($accountGroups as $accountType => $accountGroupName) {
    if (is_array($expDisData[$accountType])) {
        foreach ($expDisData[$accountType] as $lines => $lineRow) {
            foreach ($lineRow as $x => $z) {

                $line -= 15;

                if ($line < 50) {
                    $pdf->setlinewidth(0.5);
                    $pdf->moveto(18, 515);
                    $pdf->lineto(18, $line);
                    $pdf->stroke();

                    $pdf->setlinewidth(0.5);
                    $pdf->moveto(824, 515);
                    $pdf->lineto(824, $line);
                    $pdf->stroke();

                    $pdf->setlinewidth(0.5);
                    $pdf->moveto(18, $line);
                    $pdf->lineto(824, $line);
                    $pdf->stroke();

                    // insert tracc footer
                    $traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'propertyFinancialReport', A4_LANDSCAPE);
                    $traccFooter->prerender($pdf);

                    $pdf->end_page_ext('');

                    newAnnualPage($startDate, $endDate);

                    $line = 478;

                }


                if ($z['accountID'] == '2423') {
                    $z['description'] = $z['description'] . '(Note: Not Profit & Loss account)';
                }

                $distributionAccount[] = $z['accountID'];
                $pdf->setFontExt($_fonts['Helvetica'], 8);
                $pdf->showBoxed($z['accountID'], 50, $line, 200, 15, 'left', '');
                $pdf->showBoxed($z['description'], 80, $line, 200, 15, 'left', '');
                $pdf->showBoxed(toMoney($z[11]), 170, $line, 50, 15, 'right', '');
                $pdf->showBoxed(toMoney($z[10]), 220, $line, 50, 15, 'right', '');
                $pdf->showBoxed(toMoney($z[9]), 270, $line, 50, 15, 'right', '');
                $pdf->showBoxed(toMoney($z[8]), 320, $line, 50, 15, 'right', '');
                $pdf->showBoxed(toMoney($z[7]), 370, $line, 50, 15, 'right', '');
                $pdf->showBoxed(toMoney($z[6]), 420, $line, 50, 15, 'right', '');
                $pdf->showBoxed(toMoney($z[5]), 470, $line, 50, 15, 'right', '');
                $pdf->showBoxed(toMoney($z[4]), 520, $line, 50, 15, 'right', '');
                $pdf->showBoxed(toMoney($z[3]), 570, $line, 50, 15, 'right', '');
                $pdf->showBoxed(toMoney($z[2]), 620, $line, 50, 15, 'right', '');
                $pdf->showBoxed(toMoney($z[1]), 670, $line, 50, 15, 'right', '');
                $pdf->showBoxed(toMoney($z[0]), 720, $line, 50, 15, 'right', '');
                $pdf->showBoxed(toMoney($z['ytd']), 770, $line, 52, 15, 'right', '');

            }
        }
    }
}
$line -= 15;

if ($line < 50) {
    $pdf->setlinewidth(0.5);
    $pdf->moveto(18, 515);
    $pdf->lineto(18, $line);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(824, 515);
    $pdf->lineto(824, $line);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(18, $line);
    $pdf->lineto(824, $line);
    $pdf->stroke();

    // insert tracc footer
    $traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'propertyFinancialReport', A4_LANDSCAPE);
    $traccFooter->prerender($pdf);

    $pdf->end_page_ext('');

    newAnnualPage($startDate, $endDate);

    $line = 478;

}

$pdf->setColorExt('fill', 'rgb', 0.88, 0.88, 0.88, 0);
$pdf->rect(18, $line + 2, 806, 15);
$pdf->fill();
$pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);

$pdf->setlinewidth(0.5);
$pdf->moveto(18, $line + 2);
$pdf->lineto(824, $line + 2);
$pdf->stroke();

$pdf->setlinewidth(0.5);
$pdf->moveto(18, $line + 17);
$pdf->lineto(824, $line + 17);
$pdf->stroke();

$pdf->setFontExt($_fonts['Helvetica-Bold'], 9);
$pdf->showBoxed($subtotals['isExpDis']['description'], 22, $line, 200, 15, 'left', '');
$pdf->showBoxed(toMoney($subtotals['isExpDis'][11]), 170, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($subtotals['isExpDis'][10]), 220, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($subtotals['isExpDis'][9]), 270, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($subtotals['isExpDis'][8]), 320, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($subtotals['isExpDis'][7]), 370, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($subtotals['isExpDis'][6]), 420, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($subtotals['isExpDis'][5]), 470, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($subtotals['isExpDis'][4]), 520, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($subtotals['isExpDis'][3]), 570, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($subtotals['isExpDis'][2]), 620, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($subtotals['isExpDis'][1]), 670, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($subtotals['isExpDis'][0]), 720, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($subtotals['isExpDis']['ytd']), 770, $line, 52, 15, 'right', '');
$line -= 15;

if ($line < 50) {
    $pdf->setlinewidth(0.5);
    $pdf->moveto(18, 515);
    $pdf->lineto(18, $line);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(824, 515);
    $pdf->lineto(824, $line);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(18, $line);
    $pdf->lineto(824, $line);
    $pdf->stroke();

    // insert tracc footer
    $traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'propertyFinancialReport', A4_LANDSCAPE);
    $traccFooter->prerender($pdf);

    $pdf->end_page_ext('');

    newAnnualPage($startDate, $endDate);

    $line = 478;

}

$pdf->setColorExt('fill', 'rgb', 0.88, 0.88, 0.88, 0);
$pdf->rect(18, $line + 2, 806, 15);
$pdf->fill();
$pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);

$pdf->setlinewidth(0.5);
$pdf->moveto(18, $line + 2);
$pdf->lineto(824, $line + 2);
$pdf->stroke();

$pdf->setlinewidth(0.5);
$pdf->moveto(18, $line + 17);
$pdf->lineto(824, $line + 17);
$pdf->stroke();

$pdf->setFontExt($_fonts['Helvetica-Bold'], 9);
$pdf->showBoxed($plTotal['description'], 22, $line, 200, 15, 'left', '');
$pdf->showBoxed(toMoney($plTotal[11]), 170, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($plTotal[10]), 220, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($plTotal[9]), 270, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($plTotal[8]), 320, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($plTotal[7]), 370, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($plTotal[6]), 420, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($plTotal[5]), 470, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($plTotal[4]), 520, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($plTotal[3]), 570, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($plTotal[2]), 620, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($plTotal[1]), 670, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($plTotal[0]), 720, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($plTotal['ytd']), 770, $line, 52, 15, 'right', '');
$line -= 15;

if ($line < 50) {
    $pdf->setlinewidth(0.5);
    $pdf->moveto(18, 515);
    $pdf->lineto(18, $line);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(824, 515);
    $pdf->lineto(824, $line);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(18, $line);
    $pdf->lineto(824, $line);
    $pdf->stroke();

    // insert tracc footer
    $traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'propertyFinancialReport', A4_LANDSCAPE);
    $traccFooter->prerender($pdf);

    $pdf->end_page_ext('');

    newAnnualPage($startDate, $endDate);

    $line = 478;

}

$pdf->setFontExt($_fonts['Helvetica'], 8);
$pdf->showBoxed($cashOpening['description'], 22, $line, 200, 15, 'left', '');
$pdf->showBoxed(toMoney($cashOpening[11]), 170, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($cashOpening[10]), 220, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($cashOpening[9]), 270, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($cashOpening[8]), 320, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($cashOpening[7]), 370, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($cashOpening[6]), 420, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($cashOpening[5]), 470, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($cashOpening[4]), 520, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($cashOpening[3]), 570, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($cashOpening[2]), 620, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($cashOpening[1]), 670, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($cashOpening[0]), 720, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($cashOpening['ytd']), 770, $line, 52, 15, 'right', '');
$line -= 15;


if ($line < 50) {
    $pdf->setlinewidth(0.5);
    $pdf->moveto(18, 515);
    $pdf->lineto(18, $line);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(824, 515);
    $pdf->lineto(824, $line);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(18, $line);
    $pdf->lineto(824, $line);
    $pdf->stroke();

    // insert tracc footer
    $traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'propertyFinancialReport', A4_LANDSCAPE);
    $traccFooter->prerender($pdf);

    $pdf->end_page_ext('');

    newAnnualPage($startDate, $endDate);

    $line = 478;

}

$pdf->setColorExt('fill', 'rgb', 0.88, 0.88, 0.88, 0);
$pdf->rect(18, $line + 2, 806, 15);
$pdf->fill();
$pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);

$pdf->setlinewidth(0.5);
$pdf->moveto(18, $line + 2);
$pdf->lineto(824, $line + 2);
$pdf->stroke();

$pdf->setlinewidth(0.5);
$pdf->moveto(18, $line + 17);
$pdf->lineto(824, $line + 17);
$pdf->stroke();

$pdf->setFontExt($_fonts['Helvetica-Bold'], 9);
$pdf->showBoxed($cashClosing['description'], 22, $line, 200, 15, 'left', '');
$pdf->showBoxed(toMoney($cashClosing[11]), 170, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($cashClosing[10]), 220, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($cashClosing[9]), 270, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($cashClosing[8]), 320, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($cashClosing[7]), 370, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($cashClosing[6]), 420, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($cashClosing[5]), 470, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($cashClosing[4]), 520, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($cashClosing[3]), 570, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($cashClosing[2]), 620, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($cashClosing[1]), 670, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($cashClosing[0]), 720, $line, 50, 15, 'right', '');
$pdf->showBoxed(toMoney($cashClosing['ytd']), 770, $line, 52, 15, 'right', '');
$line -= 30;

$distRow = [];
if ($subtotals['isExpDis']['ytd'] != 0) {
    for ($period = 1; $period <= 12; $period++) {
        $distData = dbGetOwnerDistributions($propertyID, $period, $period, $year, $distributionAccount);
        foreach ($distData as $row) {
            $distRow[$row['company_id']]['description'] =  substr($row['company_id'], 0, 43);
            $distRow[$row['company_id']][$period] =  $row['total'];
        }
    }

    $distData = dbGetOwnerDistributions($propertyID, 1, 12, $year, $distributionAccount);
    foreach ($distData as $row) {
        $distRow[$row['company_id']]['description'] =  substr($row['company_id'], 0, 43);
        $distRow[$row['company_id']]['ytd'] =  $row['total'];
    }


    //    $line -= 15;

    if ($line < 50) {
        $pdf->setlinewidth(0.5);
        $pdf->moveto(18, 515);
        $pdf->lineto(18, $line);
        $pdf->stroke();

        $pdf->setlinewidth(0.5);
        $pdf->moveto(824, 515);
        $pdf->lineto(824, $line);
        $pdf->stroke();

        $pdf->setlinewidth(0.5);
        $pdf->moveto(18, $line);
        $pdf->lineto(824, $line);
        $pdf->stroke();

        // insert tracc footer
        $traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'propertyFinancialReport', A4_LANDSCAPE);
        $traccFooter->prerender($pdf);

        $pdf->end_page_ext('');

        newAnnualPage($startDate, $endDate);

        $line = 478;

    }

    $pdf->setColorExt('fill', 'rgb', 0.88, 0.88, 0.88, 0);
    $pdf->rect(18, $line + 2, 806, 15);
    $pdf->fill();
    $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);

    $pdf->setlinewidth(0.5);
    $pdf->moveto(18, $line + 2);
    $pdf->lineto(824, $line + 2);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(18, $line + 17);
    $pdf->lineto(824, $line + 17);
    $pdf->stroke();

    $pdf->setFontExt($_fonts['Helvetica-Bold'], 9);
    $pdf->showBoxed('Owner Distributions Details', 22, $line, 200, 15, 'left', '');
    $line -= 15;

    $distSub = [];
    foreach ($distRow as $dat) {

        if ($line < 50) {
            $pdf->setlinewidth(0.5);
            $pdf->moveto(18, 515);
            $pdf->lineto(18, $line);
            $pdf->stroke();

            $pdf->setlinewidth(0.5);
            $pdf->moveto(824, 515);
            $pdf->lineto(824, $line);
            $pdf->stroke();

            $pdf->setlinewidth(0.5);
            $pdf->moveto(18, $line);
            $pdf->lineto(824, $line);
            $pdf->stroke();

            // insert tracc footer
            $traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'propertyFinancialReport', A4_LANDSCAPE);
            $traccFooter->prerender($pdf);

            $pdf->end_page_ext('');

            newAnnualPage($startDate, $endDate);

            $line = 478;

        }

        for ($x = 1; $x <= 12; $x++) {
            $distSub[$x] += $dat[$x];
        }

        $pdf->setFontExt($_fonts['Helvetica'], 8);
        $pdf->showBoxed($dat['description'], 22, $line, 200, 15, 'left', '');
        $pdf->showBoxed(toMoney($dat[1]), 170, $line, 50, 15, 'right', '');
        $pdf->showBoxed(toMoney($dat[2]), 220, $line, 50, 15, 'right', '');
        $pdf->showBoxed(toMoney($dat[3]), 270, $line, 50, 15, 'right', '');
        $pdf->showBoxed(toMoney($dat[4]), 320, $line, 50, 15, 'right', '');
        $pdf->showBoxed(toMoney($dat[5]), 370, $line, 50, 15, 'right', '');
        $pdf->showBoxed(toMoney($dat[6]), 420, $line, 50, 15, 'right', '');
        $pdf->showBoxed(toMoney($dat[7]), 470, $line, 50, 15, 'right', '');
        $pdf->showBoxed(toMoney($dat[8]), 520, $line, 50, 15, 'right', '');
        $pdf->showBoxed(toMoney($dat[9]), 570, $line, 50, 15, 'right', '');
        $pdf->showBoxed(toMoney($dat[10]), 620, $line, 50, 15, 'right', '');
        $pdf->showBoxed(toMoney($dat[11]), 670, $line, 50, 15, 'right', '');
        $pdf->showBoxed(toMoney($dat[12]), 720, $line, 50, 15, 'right', '');
        $pdf->showBoxed(toMoney($dat['ytd']), 770, $line, 52, 15, 'right', '');
        $line -= 15;

        $distSub['ytd'] += $dat['ytd'];

    }

    if ($line < 50) {
        $pdf->setlinewidth(0.5);
        $pdf->moveto(18, 515);
        $pdf->lineto(18, $line);
        $pdf->stroke();

        $pdf->setlinewidth(0.5);
        $pdf->moveto(824, 515);
        $pdf->lineto(824, $line);
        $pdf->stroke();

        $pdf->setlinewidth(0.5);
        $pdf->moveto(18, $line);
        $pdf->lineto(824, $line);
        $pdf->stroke();

        // insert tracc footer
        $traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'propertyFinancialReport', A4_LANDSCAPE);
        $traccFooter->prerender($pdf);

        $pdf->end_page_ext('');

        newAnnualPage($startDate, $endDate);

        $line = 478;

    }

    $pdf->setColorExt('fill', 'rgb', 0.88, 0.88, 0.88, 0);
    $pdf->rect(18, $line + 2, 806, 15);
    $pdf->fill();
    $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);

    $pdf->setlinewidth(0.5);
    $pdf->moveto(18, $line + 2);
    $pdf->lineto(824, $line + 2);
    $pdf->stroke();

    $pdf->setlinewidth(0.5);
    $pdf->moveto(18, $line + 17);
    $pdf->lineto(824, $line + 17);
    $pdf->stroke();

    $pdf->setFontExt($_fonts['Helvetica-Bold'], 9);
    $pdf->showBoxed('Total Owner Distributions', 22, $line, 200, 15, 'left', '');
    $pdf->showBoxed(toMoney($distSub[1]), 170, $line, 50, 15, 'right', '');
    $pdf->showBoxed(toMoney($distSub[2]), 220, $line, 50, 15, 'right', '');
    $pdf->showBoxed(toMoney($distSub[3]), 270, $line, 50, 15, 'right', '');
    $pdf->showBoxed(toMoney($distSub[4]), 320, $line, 50, 15, 'right', '');
    $pdf->showBoxed(toMoney($distSub[5]), 370, $line, 50, 15, 'right', '');
    $pdf->showBoxed(toMoney($distSub[6]), 420, $line, 50, 15, 'right', '');
    $pdf->showBoxed(toMoney($distSub[7]), 470, $line, 50, 15, 'right', '');
    $pdf->showBoxed(toMoney($distSub[8]), 520, $line, 50, 15, 'right', '');
    $pdf->showBoxed(toMoney($distSub[9]), 570, $line, 50, 15, 'right', '');
    $pdf->showBoxed(toMoney($distSub[10]), 620, $line, 50, 15, 'right', '');
    $pdf->showBoxed(toMoney($distSub[11]), 670, $line, 50, 15, 'right', '');
    $pdf->showBoxed(toMoney($distSub[12]), 720, $line, 50, 15, 'right', '');
    $pdf->showBoxed(toMoney($distSub['ytd']), 770, $line, 52, 15, 'right', '');
    $line -= 15;
}

$pdf->setlinewidth(0.5);
$pdf->moveto(18, 515);
$pdf->lineto(18, $line);
$pdf->stroke();

$pdf->setlinewidth(0.5);
$pdf->moveto(824, 515);
$pdf->lineto(824, $line);
$pdf->stroke();

$pdf->setlinewidth(0.5);
$pdf->moveto(18, $line);
$pdf->lineto(824, $line);
$pdf->stroke();

// insert tracc footer
$traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'propertyFinancialReport', A4_LANDSCAPE);
$traccFooter->prerender($pdf);

$pdf->end_page_ext('');

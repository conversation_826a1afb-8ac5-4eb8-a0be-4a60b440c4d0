<?php

/**
 * Process information gathered in order to generate a property list report.
 *
 * @param  $context  array Mandatory. Referenced array containing data needed to continue.
 *
 * <AUTHOR> Reyes
 *
 * @since 2012-09-18
 *
 * @modified 2012-12-07: Added display on screen option. [Morph]
 **/
function propertyListReportProcess($context)
{
    global $clientDirectory, $pathPrefix;

    if (! isPostback()) {
        $view = new MasterPage(userViews(), '/managementReports/propertyListReportProcess.html');
        $view->setSection($context['module']);
    } else {
        $view = new UserControl(userViews(), '/managementReports/propertyListReportProcess.html');
    }

    $view->bindAttributesFrom($_REQUEST);

    $properties = dbGetPropertyList($view->items['propertyManager'], $view->items['status']);

    if ($context[IS_TASK]) {
        $view->bindAttributesFrom($context);
        extract($context);
    } else {
        $view->bindAttributesFrom($context);
        $view->bindAttributesFrom($_REQUEST);

        $queue = new Queue(TASKTYPE_PROPERTY_LIST_REPORT);
        if ($view->items['propertyCount'] > THRESHOLD_PROPERTYLISTREPORT) {
            $queue->add($_SESSION['clientID'], $_SESSION['un'], 'command=propertyListReportProcess&module=managementReports', $_REQUEST);
        }
    }

    if ($context[IS_TASK] || $view->items['propertyCount'] <= THRESHOLD_PROPERTYLISTREPORT) {
        $format = $view->items['format'];

        if (! $context[DOC_MASTER]) {
            $logoFile = dbGetClientLogo();
            $logoPath = "assets/clientLogos/{$logoFile}";
            $_filePath =  "{$pathPrefix}{$clientDirectory}/{$format}/" . DOC_PROPERTYLISTREPORT . '/';
            $_downloadPath =  "{$clientDirectory}/{$format}/" . DOC_PROPERTYLISTREPORT;
            $file =  DOC_PROPERTYLISTREPORT . '_' . date('Ymd') . ".{$format}";
            $filePath = $_filePath . $file;
            $downloadPath = "{$_downloadPath}/{$file}";
        }

        foreach ($properties as $k => $v) {
            if ($v['propertyStreet'] and $v['propertyStreet'] != '.') {
                $properties[$k]['propertyAddress'] = $v['propertyStreet'] . ', ' . $v['propertyCity'] . ' ' . $v['propertyState'] . ' ' . $v['propertyPostalCode'];
            } else {
                $properties[$k]['propertyAddress'] = $v['propertyCity'] . ' ' . $v['propertyState'] . ' ' . $v['propertyPostalCode'];
            }
            $portfolioOwner = $v['propertyPortfolioOwner'];
            if (isset($view->items['PropManagerList'][$portfolioOwner]['name'])) {
                $properties[$k]['propertyPortfolio'] = $view->items['PropManagerList'][$portfolioOwner]['name'] . " ({$portfolioOwner})";
            }
            if (! $view->items['status']) {
                unset($properties[$k]['propertyChargeDate']);
            }
        }

        if ($format != FILETYPE_SCREEN) {
            $subtitle = 'for ' . $view->items['propertyManagerName'] . ' (' . $view->items['propertyManager'] . ')';
            $prepared = 'as at ' . date('d M Y');
        }

        switch ($format) {
            case FILETYPE_PDF:
                $report = ($context[DOC_MASTER]) ? new PDFDataReport($context[DOC_MASTER], $logoPath, A4_LANDSCAPE) : new PDFDataReport($filePath, $logoPath, A4_LANDSCAPE);
                $report->multiLine = true;
                $report->printRowLines = true;
                $report->printColumnLines = false;
                $report->printBorders = false;
                $report->cache = false;

                $header = ($view->items['status']) ? new ReportHeader('Inactive Property List Report', $subtitle, $prepared) : new ReportHeader('Active Property List Report', $subtitle, $prepared);
                $header->xPos = $report->hMargin;
                $header->yPos = $report->pageHeight - $report->vMargin;
                $report->attachObject('header', $header);
                $footer = new TraccFooter(null, $_report['reportName'], $report->pageSize);
                $report->attachObject('footer', $footer);
                break;
            case FILETYPE_XLS:
                $report = new XLSDataReport($filePath, (($view->items['status']) ? 'Inactive' : 'Active') . ' Property List Report');
                $report->enableFormatting = true;
                $report->renderHeaderDetails("$subtitle\n$prepared");
                break;
        }

        if ($format != FILETYPE_SCREEN) {
            $adjustX = 0;
            if ($view->items['status']) {
                $adjustX = 22;
            }

            $report->addColumn('propertyCode', 'Property', 60, 'left');
            $report->addColumn('propertyName', 'Property Name', 130 - $adjustX, 'left');
            $report->addColumn(($format == FILETYPE_PDF) ? 'propertyStreet' : 'propertyAddress', 'Address', 130 - $adjustX, 'left');
            $report->addColumn('propertyCity', 'Property ' . ucwords(strtolower($_SESSION['country_default']['suburb'])), 130 - $adjustX, 'left');
            $report->addColumn('propertyOwner', 'Owner', 60, 'left');
            $report->addColumn('propertyOwnerName', 'Owner Name', 130 - $adjustX, 'left');
            $report->addColumn('propertyPortfolio', 'Portfolio', 130 - $adjustX, 'left');
            if ($view->items['status']) {
                $report->addColumn('propertyChargeDate', 'Last Charge Date', 50, 'center');
                $report->addColumn('propertyInactiveDate', 'Inactive Date', 50, 'center');
            }
            $report->preparePage();
            $report->renderHeader();
            $report->renderData($properties);
            $report->clean();
            $report->endPage();

            $report->close();
        } else {
            $view->items['properties'] = $properties;
        }
    }

    if (! $context[DOC_MASTER]) {
        if ($context[IS_TASK]) {
            $email = new EmailTemplate('views/emails/basicEmail.html', SYSTEMURL);
            $attachment =  [];
            $attachment[0]['file'] = REPORTPATH . '/' . $pdfDownloadLink;
            $attachment[0]['content_type'] = 'application/pdf';
            sendMail($_SESSION['email'], $_SESSION['first_name'] . ' ' . $_SESSION['last_name'], $email->toString(), 'Report', $attachment);
            logData('Emailed report to ' . $_SESSION['email']);
            $context[TASK_COMPLETE] = true;
            $view->items['statusMessage'] = 'Due to its complexity, this report has been scheduled and will be emailed to you on completion.';
        } else {
            $view->items['downloadPath'] = $downloadPath;
        }
        $view->render();
    }
}

<?php

/**
 * Process information gathered in order to generate a door count report.
 *
 * @param  $context  array Mandatory. Referenced array containing data needed to continue.
 *
 * <AUTHOR> <PERSON>
 *
 * @since 2015-07-31
 **/
function doorCountReportProcess($context)
{
    global $clientDirectory, $monthName, $pathPrefix;

    if (! isPostback()) {
        $view = new MasterPage(userViews(), '/managementReports/doorCountReportProcess.html');
        $view->setSection($context['module']);
    } else {
        $view = new UserControl(userViews(), '/managementReports/doorCountReportProcess.html');
    }

    if ($context[IS_TASK]) {
        $view->bindAttributesFrom($context);
        extract($context);
    } else {
        $view->bindAttributesFrom($_REQUEST);
        $view->bindAttributesFrom($context);
    }

    $dataCount = dbCountDoorCount($view->items['propertyID'], '01/01/' . $view->items['yearCurrent'], '31/12/' . $view->items['yearCurrent']) + dbCountDoorCount($view->items['propertyID'], '01/01/' . $view->items['yearPrevious'], '31/12/' . $view->items['yearPrevious']);

    $queue = new Queue(10);
    if ($dataCount > THRESHOLD_EXCELREPORT) {
        $queue->add($_SESSION['clientID'], $_SESSION['un'], 'command=' . $view->items['command'] . '&module=' . $view->items['module'], $_REQUEST);
    }

    if (($context[IS_TASK] || $dataCount <= THRESHOLD_EXCELREPORT) && $dataCount) {
        // $dateFormat = 'd/mm/yyyy;@';
        $dateFormat = 'mmmm dd (dddd)';
        $format = $view->items['format'];
        $numberFormat = '#,##0_);[Red](#,##0);-_)';
        $percentageFormat = '#,##0.00%;[Red](#,##0.00%);-_)';
        $useFormula = ($view->items['useFormula'] == 'No' || $format != FILETYPE_XLS) ? false : true;

        if (! $context[DOC_MASTER]) {
            $logoFile = dbGetClientLogo();
            $logoPath = "assets/clientLogos/$logoFile";
            $file =  'DoorCountReport_' . date('Ymd') . '.' . $format;
            $filePath = "{$pathPrefix}{$clientDirectory}/$format/DoorCountReport/$file";
            $downloadPath = "$clientDirectory/$format/DoorCountReport/$file";
        }

        switch ($format) {
            case FILETYPE_PDF:
                $report = ($context[DOC_MASTER]) ? new PDFDataReport($context[DOC_MASTER], $logoPath, A4_LANDSCAPE) : new PDFDataReport($filePath, $logoPath, A4_LANDSCAPE);
                $report->multiLine = true;
                $report->printRowLines = true;
                $report->printColumnLines = false;
                $report->printBorders = false;
                $report->cache = false;

                $prepared = 'as at ' . date('d M Y');
                $header = new ReportHeader('Door Count Report', '', $prepared);
                $header->xPos = $report->hMargin;
                $header->yPos = $report->pageHeight - $report->vMargin;
                $report->attachObject('header', $header);
                $footer = new TraccFooter(null, 'DoorCountReport', $report->pageSize);
                if ($format == FILETYPE_PDF) {
                    $report->attachObject('footer', $footer);
                }
                break;
            case FILETYPE_XLS:
                $report = new XLSDataReport($filePath, 'Door Count Report');
                $report->enableFormatting = true;
                switch ($view->items['colorScheme']) {
                    case 'Medium Blue':
                        $report->totalFillColor = $report->fillColor = 'e8eaf1';
                        $report->baseColor = '406ec7';
                        break;
                    case 'Indigo':
                        $report->totalFillColor = $report->fillColor = 'eae7f0';
                        $report->baseColor = '5a3ec1';
                        break;
                    case 'Magenta':
                        $report->totalFillColor = $report->fillColor = 'f1e8f1';
                        $report->baseColor = 'c241ca';
                        break;
                    case 'Pinkish Red':
                        $report->totalFillColor = $report->fillColor = 'f2e9ec';
                        $report->baseColor = 'e94b82';
                        break;
                    case 'Red-Orange':
                        $report->totalFillColor = $report->fillColor = 'f3ecea';
                        $report->baseColor = 'fe6f52';
                        break;
                    case 'Orange':
                        $report->totalFillColor = $report->fillColor = 'f3efea';
                        $report->baseColor = 'fea252';
                        break;
                    case 'Yellow-Orange':
                        $report->fillColor = 'fec452';
                        $report->baseColor = '000000';
                        $report->totalFillColor = 'ffe7b8';
                        break;
                    case 'Yellow':
                        $report->totalFillColor = $report->fillColor = 'feea52';
                        $report->baseColor = '000000';
                        break;
                    case 'Greenish-Yellow':
                        $report->totalFillColor = $report->fillColor = 'f1f2e9';
                        $report->baseColor = 'd8ee4c';
                        break;
                    case 'Yellow-Green':
                        $report->totalFillColor = $report->fillColor = 'ebf1e8';
                        $report->baseColor = '7fd143';
                        break;
                    case 'Blue-Green':
                        $report->totalFillColor = $report->fillColor = 'e8f1ed';
                        $report->baseColor = '42cd85';
                        break;
                    case 'Navy Blue':
                        $report->fillColor = '366092';
                        $report->baseColor = 'ffffff';
                        $report->totalFillColor = 'c0cfe3';
                        break;
                    case 'Bluish Cyan':
                    default:
                        $report->totalFillColor = $report->fillColor = 'e8eef1';
                        $report->baseColor = '43a5d1';
                        break;
                }
                $report->changeColorScheme($report->baseColor, $report->fillColor, $report->totalFillColor);
                break;
        }

        if (date('L', strtotime($view->items['yearPrevious'] . '-01-01'))) {
            $dateRange = new DatePeriod(new DateTime($view->items['yearPrevious'] . '-01-01'), new DateInterval('P1D'), new DateTime($view->items['yearPrevious'] + 1 . '-01-01'));
        } else {
            $dateRange = new DatePeriod(new DateTime($view->items['yearCurrent'] . '-01-01'), new DateInterval('P1D'), new DateTime($view->items['yearCurrent'] + 1 . '-01-01'));
        }

        $sheetIndex = 0;
        $dataDailyRender =  [];
        foreach ($view->items['propertyID'] as $propertyID) {
            $dataCurrent = dbGetDoorCount($propertyID, '01/01/' . $view->items['yearCurrent'], '31/12/' . $view->items['yearCurrent'], 'No');
            $dataPrevious = dbGetDoorCount($propertyID, '01/01/' . $view->items['yearPrevious'], '31/12/' . $view->items['yearPrevious'], 'No');
            foreach ($dateRange as $date) {
                $dataDailyRender[$date->format('md')]['date'] = $date->format('F d (l)');
            }
            ksort($dataDailyRender);

            if (count($dataCurrent) || count($dataPrevious)) {
                // Daily
                $report->setSheetDetails($sheetIndex, "$propertyID-Daily", ($sheetIndex === 0) ? false : true);
                $report->printer();

                $dataMonthlyRender = $doors = $doorsCurrent = $doorsPrevious =  [];
                foreach ($dataCurrent as $v) {
                    $date = date('md', totimestamp($v['date']));
                    $dateMonth = date('F', totimestamp($v['date']));
                    $doors[$v['doorID']] = $v['doorID'];
                    $dataDailyRender[$date][$v['doorID'] . $view->items['yearCurrent']] = $v['doorCount'];
                    if (! $useFormula) {
                        $doorsCurrent[$v['doorID']] += $v['doorCount'];
                        $dataMonthlyRender[$dateMonth][$v['doorID'] . $view->items['yearCurrent']] += $v['doorCount'];
                        $dataDailyRender[$date]['total' . $view->items['yearCurrent']] += $v['doorCount'];
                        $dataMonthlyRender[$dateMonth]['total' . $view->items['yearCurrent']] += $v['doorCount'];
                    }
                }
                foreach ($dataPrevious as $v) {
                    $date = date('md', totimestamp($v['date']));
                    $dateMonth = date('F', totimestamp($v['date']));
                    $doors[$v['doorID']] = $v['doorID'];
                    $dataDailyRender[$date][$v['doorID'] . $view->items['yearPrevious']] = $v['doorCount'];
                    if (! $useFormula) {
                        $doorsPrevious[$v['doorID']] += $v['doorCount'];
                        $dataMonthlyRender[$dateMonth][$v['doorID'] . $view->items['yearPrevious']] += $v['doorCount'];
                        $dataDailyRender[$date]['total' . $view->items['yearPrevious']] += $v['doorCount'];
                        $dataMonthlyRender[$dateMonth]['total' . $view->items['yearPrevious']] += $v['doorCount'];
                    }
                }

                $report->addColumn('date', '', 123, 'right', $dateFormat);
                $total = $line =  [];
                $columnPosition = 2;
                foreach ($doors as $k => $v) {
                    $report->addColumn($k . $view->items['yearPrevious'], $view->items['yearPrevious'], 60, 'right', $numberFormat);
                    $report->addColumn($k . $view->items['yearCurrent'], $view->items['yearCurrent'], 60, 'right', $numberFormat);
                    $report->addColumn($k . 'Var', 'Var %', 60, 'right', $percentageFormat);

                    if (! $useFormula) {
                        $total[$k . $view->items['yearPrevious']] = $doorsPrevious[$k];
                        $total['total' . $view->items['yearPrevious']] += $total[$k . $view->items['yearPrevious']];
                        $total[$k . $view->items['yearCurrent']] = $doorsCurrent[$k];
                        $total['total' . $view->items['yearCurrent']] += $total[$k . $view->items['yearCurrent']];
                    }

                    $linePosition = 3;
                    foreach ($dateRange as $date) {
                        $myDate = $date->format('md');
                        if (! $dataDailyRender[$myDate][$k . $view->items['yearPrevious']]) {
                            $dataDailyRender[$myDate][$k . $view->items['yearPrevious']] = 0;
                        }
                        if (! $dataDailyRender[$myDate][$k . $view->items['yearCurrent']]) {
                            $dataDailyRender[$myDate][$k . $view->items['yearCurrent']] = 0;
                        }
                        if (! $useFormula) {
                            $dataDailyRender[$myDate][$k . 'Var'] = ($dataDailyRender[$myDate][$k . $view->items['yearPrevious']] && $dataDailyRender[$myDate][$k . $view->items['yearCurrent']]) ? ($dataDailyRender[$myDate][$k . $view->items['yearCurrent']] / $dataDailyRender[$myDate][$k . $view->items['yearPrevious']]) - 1 : 0;
                        } else {
                            $dataDailyRender[$myDate][$k . 'Var'] = '=IF(OR(' . cellReference($linePosition, $columnPosition + 1) . '=0,' . cellReference($linePosition, $columnPosition) . '=0),0,(' . cellReference($linePosition, $columnPosition) . '/' . cellReference($linePosition, $columnPosition + 1) . ')-1)';
                        }
                        $linePosition++;
                    }

                    if (! $useFormula) {
                        $total[$k . 'Var']  = ($total[$k . $view->items['yearPrevious']] && $total[$k . $view->items['yearCurrent']]) ? ($total[$k . $view->items['yearCurrent']] / $total[$k . $view->items['yearPrevious']]) - 1 : 0;
                        $total['totalVar']  = ($total['total' . $view->items['yearPrevious']] && $total['total' . $view->items['yearCurrent']]) ? ($total['total' . $view->items['yearCurrent']] / $total['total' . $view->items['yearPrevious']]) - 1 : 0;
                    }

                    $line[$k . $view->items['yearPrevious']] =
                    [
                        'value' => $k,
                        'mergecells' => $columnPosition + 2,
                    ];
                    $columnPosition += 3;
                }
                $report->addColumn('total' . $view->items['yearPrevious'], $view->items['yearPrevious'], 60, 'right', $numberFormat);
                $report->addColumn('total' . $view->items['yearCurrent'], $view->items['yearCurrent'], 60, 'right', $numberFormat);
                $report->addColumn('totalVar', 'Var %', 60, 'right', $percentageFormat);
                $line['total' . $view->items['yearPrevious']] =
                [
                    'value' => 'Total',
                    'mergecells' => $columnPosition + 2,
                ];
                $report->preparePage();

                $report->renderCustomLine($line, array_merge($report->styleHeader, $report->styleCenter));
                $report->renderHeader();

                $linePosition = 3;
                $prevMonth = '';
                foreach ($dateRange as $date) {
                    $myDate = $date->format('md');
                    if (! $useFormula) {
                        if (! $dataDailyRender[$myDate]['total' . $view->items['yearPrevious']]) {
                            $dataDailyRender[$myDate]['total' . $view->items['yearPrevious']] = 0;
                        }
                        if (! $dataDailyRender[$myDate]['total' . $view->items['yearCurrent']]) {
                            $dataDailyRender[$myDate]['total' . $view->items['yearCurrent']] = 0;
                        }
                        $dataDailyRender[$myDate]['totalVar'] = ($dataDailyRender[$myDate]['total' . $view->items['yearPrevious']] && $dataDailyRender[$myDate]['total' . $view->items['yearCurrent']]) ? ($dataDailyRender[$myDate]['total' . $view->items['yearCurrent']] / $dataDailyRender[$myDate]['total' . $view->items['yearPrevious']]) - 1 : 0;
                    } else {
                        $columnPosition = 2;
                        $cellRefPrevious = $cellRefCurrent =  [];
                        foreach ($doors as $k => $v) {
                            $cellRefPrevious[] = cellReference($linePosition, $columnPosition);
                            $cellRefCurrent[] = cellReference($linePosition, $columnPosition + 1);
                            $columnPosition += 3;
                        }
                        $dataDailyRender[$myDate]['total' . $view->items['yearPrevious']] = '=SUM(' . implode(',', $cellRefPrevious) . ')';
                        $dataDailyRender[$myDate]['total' . $view->items['yearCurrent']] = '=SUM(' . implode(',', $cellRefCurrent) . ')';
                        $dataDailyRender[$myDate]['totalVar'] = '=IF(OR(' . cellReference($linePosition, $columnPosition + 1) . '=0,' . cellReference($linePosition, $columnPosition) . '=0),0,(' . cellReference($linePosition, $columnPosition) . '/' . cellReference($linePosition, $columnPosition + 1) . ')-1)';
                        if ($prevMonth != $date->format('F')) {
                            $linePositionMonth[$date->format('F')]['start'] = $linePosition;
                        } else {
                            $linePositionMonth[$date->format('F')]['end'] = $linePosition;
                        }
                        $prevMonth = $date->format('F');
                    }
                    $linePosition++;
                }
                foreach ($dataDailyRender as $v) {
                    $report->renderLine($v);
                }

                if ($useFormula) {
                    $columnPosition = 2;
                    $cellRefPrevious = $cellRefCurrent =  [];
                    foreach ($doors as $k => $v) {
                        $total[$k . $view->items['yearPrevious']] = '=SUM(' . cellReference(3, $columnPosition) . ':' . cellReference($linePosition - 1, $columnPosition) . ')';
                        $total[$k . $view->items['yearCurrent']] = '=SUM(' . cellReference(3, $columnPosition + 1) . ':' . cellReference($linePosition - 1, $columnPosition + 1) . ')';
                        $total[$k . 'Var']  = '=IF(OR(' . cellReference($linePosition, $columnPosition + 1) . '=0,' . cellReference($linePosition, $columnPosition) . '=0),0,(' . cellReference($linePosition, $columnPosition) . '/' . cellReference($linePosition, $columnPosition + 1) . ')-1)';
                        $cellRefPrevious[] = cellReference($linePosition, $columnPosition);
                        $cellRefCurrent[] = cellReference($linePosition, $columnPosition + 1);
                        $columnPosition += 3;
                    }
                    $total['total' . $view->items['yearPrevious']] = '=SUM(' . implode(',', $cellRefPrevious) . ')';
                    $total['total' . $view->items['yearCurrent']] = '=SUM(' . implode(',', $cellRefCurrent) . ')';
                    $total['totalVar'] = '=IF(OR(' . cellReference($linePosition, $columnPosition + 1) . '=0,' . cellReference($linePosition, $columnPosition) . '=0),0,(' . cellReference($linePosition, $columnPosition) . '/' . cellReference($linePosition, $columnPosition + 1) . ')-1)';
                }
                if ($format == FILETYPE_XLS) {
                    $report->renderTotal($total);
                } else {
                    $report->renderSubTotal($total);
                }

                $columnPosition = 2;
                foreach ($doors as $k => $v) {
                    $report->setColumnWidth(numberToLetter($columnPosition), 11);
                    $report->setColumnWidth(numberToLetter($columnPosition + 1), 11);
                    $report->setColumnWidth(numberToLetter($columnPosition + 2), 11);
                    $columnPosition += 3;
                }
                $report->setColumnWidth(numberToLetter($columnPosition), 11);
                $report->setColumnWidth(numberToLetter($columnPosition + 1), 11);
                $report->setColumnWidth(numberToLetter($columnPosition + 2), 11);

                if ($useFormula) {
                    foreach ($linePositionMonth as $month => $linePosition) {
                        $columnPosition = 2;
                        foreach ($doors as $k => $v) {
                            $report->setName($month . $k . $view->items['yearPrevious'], cellReference($linePosition['start'], $columnPosition) . ':' . cellReference($linePosition['end'], $columnPosition));
                            $report->setName($month . $k . $view->items['yearCurrent'], cellReference($linePosition['start'], $columnPosition + 1) . ':' . cellReference($linePosition['end'], $columnPosition + 1));
                            $columnPosition += 3;
                        }
                    }
                }

                $sheetIndex++;

                // Monthly
                $report->setSheetDetails($sheetIndex, "$propertyID-Monthly", true);
                $report->line = 1;
                if ($useFormula) {
                    $total =  [];
                }

                $columnPosition = 2;
                foreach ($doors as $k => $v) {
                    $linePosition = 3;
                    foreach ($monthName as $month) {
                        if (! $useFormula) {
                            if (! $dataMonthlyRender[$month][$k . $view->items['yearPrevious']]) {
                                $dataMonthlyRender[$month][$k . $view->items['yearPrevious']] = 0;
                            }
                            if (! $dataMonthlyRender[$month][$k . $view->items['yearCurrent']]) {
                                $dataMonthlyRender[$month][$k . $view->items['yearCurrent']] = 0;
                            }
                            $dataMonthlyRender[$month][$k . 'Var'] = ($dataMonthlyRender[$month][$k . $view->items['yearPrevious']] && $dataMonthlyRender[$month][$k . $view->items['yearCurrent']]) ? ($dataMonthlyRender[$month][$k . $view->items['yearCurrent']] / $dataMonthlyRender[$month][$k . $view->items['yearPrevious']]) - 1 : 0;
                        } else {
                            $dataMonthlyRender[$month][$k . $view->items['yearPrevious']] = '=SUM(' . $month . $k . $view->items['yearPrevious'] . ')';
                            $dataMonthlyRender[$month][$k . $view->items['yearCurrent']] = '=SUM(' . $month . $k . $view->items['yearCurrent'] . ')';
                            $dataMonthlyRender[$month][$k . 'Var'] = '=IF(OR(' . cellReference($linePosition, $columnPosition + 1) . '=0,' . cellReference($linePosition, $columnPosition) . '=0),0,(' . cellReference($linePosition, $columnPosition) . '/' . cellReference($linePosition, $columnPosition + 1) . ')-1)';
                        }
                        $linePosition++;
                    }
                    $columnPosition += 3;
                }
                $report->renderCustomLine($line, array_merge($report->styleHeader, $report->styleCenter));
                $report->renderHeader();

                $linePosition = 3;
                foreach ($monthName as $month) {
                    $dataMonthlyRender[$month]['date'] = $month;
                    if (! $useFormula) {
                        if (! $dataMonthlyRender[$month]['total' . $view->items['yearPrevious']]) {
                            $dataMonthlyRender[$month]['total' . $view->items['yearPrevious']] = 0;
                        }
                        if (! $dataMonthlyRender[$month]['total' . $view->items['yearCurrent']]) {
                            $dataMonthlyRender[$month]['total' . $view->items['yearCurrent']] = 0;
                        }
                        $dataMonthlyRender[$month]['totalVar'] = ($dataMonthlyRender[$month]['total' . $view->items['yearPrevious']] && $dataMonthlyRender[$month]['total' . $view->items['yearCurrent']]) ? ($dataMonthlyRender[$month]['total' . $view->items['yearCurrent']] / $dataMonthlyRender[$month]['total' . $view->items['yearPrevious']]) - 1 : 0;
                    } else {
                        $columnPosition = 2;
                        $cellRefPrevious = $cellRefCurrent =  [];
                        foreach ($doors as $k => $v) {
                            $cellRefPrevious[] = cellReference($linePosition, $columnPosition);
                            $cellRefCurrent[] = cellReference($linePosition, $columnPosition + 1);
                            $columnPosition += 3;
                        }
                        $dataMonthlyRender[$month]['total' . $view->items['yearPrevious']] = '=SUM(' . implode(',', $cellRefPrevious) . ')';
                        $dataMonthlyRender[$month]['total' . $view->items['yearCurrent']] = '=SUM(' . implode(',', $cellRefCurrent) . ')';
                        $dataMonthlyRender[$month]['totalVar'] = '=IF(OR(' . cellReference($linePosition, $columnPosition + 1) . '=0,' . cellReference($linePosition, $columnPosition) . '=0),0,(' . cellReference($linePosition, $columnPosition) . '/' . cellReference($linePosition, $columnPosition + 1) . ')-1)';
                        $linePosition++;
                    }
                }
                foreach ($dataMonthlyRender as $v) {
                    $report->renderLine($v);
                }

                if ($useFormula) {
                    $columnPosition = 2;
                    $cellRefPrevious = $cellRefCurrent =  [];
                    foreach ($doors as $k => $v) {
                        $total[$k . $view->items['yearPrevious']] = '=SUM(' . cellReference(3, $columnPosition) . ':' . cellReference($linePosition - 1, $columnPosition) . ')';
                        $total[$k . $view->items['yearCurrent']] = '=SUM(' . cellReference(3, $columnPosition + 1) . ':' . cellReference($linePosition - 1, $columnPosition + 1) . ')';
                        $total[$k . 'Var']  = '=IF(OR(' . cellReference($linePosition, $columnPosition + 1) . '=0,' . cellReference($linePosition, $columnPosition) . '=0),0,(' . cellReference($linePosition, $columnPosition) . '/' . cellReference($linePosition, $columnPosition + 1) . ')-1)';
                        $cellRefPrevious[] = cellReference($linePosition, $columnPosition);
                        $cellRefCurrent[] = cellReference($linePosition, $columnPosition + 1);
                        $columnPosition += 3;
                    }
                    $total['total' . $view->items['yearPrevious']] = '=SUM(' . implode(',', $cellRefPrevious) . ')';
                    $total['total' . $view->items['yearCurrent']] = '=SUM(' . implode(',', $cellRefCurrent) . ')';
                    $total['totalVar'] = '=IF(OR(' . cellReference($linePosition, $columnPosition + 1) . '=0,' . cellReference($linePosition, $columnPosition) . '=0),0,(' . cellReference($linePosition, $columnPosition + 1) . '/' . cellReference($linePosition, $columnPosition) . ')-1)';
                }
                if ($format == FILETYPE_XLS) {
                    $report->renderTotal($total);
                } else {
                    $report->renderSubTotal($total);
                }

                $columnPosition = 2;
                foreach ($doors as $k => $v) {
                    $report->setColumnWidth(numberToLetter($columnPosition), 11);
                    $report->setColumnWidth(numberToLetter($columnPosition + 1), 11);
                    $report->setColumnWidth(numberToLetter($columnPosition + 2), 11);
                    $columnPosition += 3;
                }
                $report->setColumnWidth(numberToLetter($columnPosition), 11);
                $report->setColumnWidth(numberToLetter($columnPosition + 1), 11);
                $report->setColumnWidth(numberToLetter($columnPosition + 2), 11);

                $sheetIndex++;
            }
        }

        if ($format != FILETYPE_SCREEN) {
            $report->clean();
            $report->endPage();
            $report->close();
        }
    } elseif (! $dataCount) {
        $view->items['statusMessage'] = 'Nothing to report.';
    }

    if (! $context[DOC_MASTER]) {
        if ($context[IS_TASK]) {
            $email = new EmailTemplate('views/emails/basicEmail.html', SYSTEMURL);
            $attachment =  [];
            $attachment[0]['file'] = REPORTPATH . '/' . $pdfDownloadLink;
            $attachment[0]['content_type'] = 'application/pdf';
            sendMail($_SESSION['email'], $_SESSION['first_name'] . ' ' . $_SESSION['last_name'], $email->toString(), 'Report', $attachment);
            logData('Emailed report to ' . $_SESSION['email']);
            $context[TASK_COMPLETE] = true;
            $view->items['statusMessage'] = 'Due to its complexity, this report has been scheduled and will be emailed to you on completion.';
        } else {
            $view->items['downloadPath'] = $downloadPath;
        }
        $view->render();
    }
}

<?php

/**
 * Created by PhpStorm.
 * User: esantiago
 * Date: 3/15/2019
 * Time: 2:53 PM
 */

// set module and command to use
$repModule = 'managementReports';
$repCommand = 'invoiceActivityProcess';

// reset report file to be attached
$context['ownerReportFile'] = null;

// report option
// 1 = portrait
// 2 = landscape
// $orientation = default from report table
$repOrientation = 2;

if ($repModule and $repCommand) {
    $originalViewItems = $view->items;
    $originalContext = $context;

    // Adjust parameters
    $view->items['propertyID'] = ($propertyID);
    $view->items['format'] = FILETYPE_PDF;

    $context = $view->items;

    executeCommand($repCommand, $repModule);

    if (file_exists($context['ownerReportFile'])) {
        // $pdf is the main PDF where this report will be attached.
        $pdi = new ImportedPage($pdf, $context['ownerReportFile'], $repOrientation);
        $i = 1;
        while ($pdi->loadPage($i)) {
            $page++;
            $pdi->preparePage();
            $pdi->render();
            $pdi->endPage();
            $i++;
        }
        $pdi->close();
    }

    $context = $originalContext;
    $view->items = $originalViewItems;
}

// reset report file to be attached
$context['ownerReportFile'] = null;

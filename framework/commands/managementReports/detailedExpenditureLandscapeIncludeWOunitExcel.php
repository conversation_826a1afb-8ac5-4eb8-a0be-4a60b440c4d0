<?php

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

include_once 'functions/detailedExpenditureLandsapeFunctionsWOunit.php';

global $grandTotalNet;
global $grandTotalTax;
global $grandTotalGross;
global $totalNet;
global $totalTax;
global $totalGross;

global $lineExist;

$grandTotalNet = 0;
$grandTotalTax = 0;
$grandTotalGross = 0;

$totalNet = 0;
$totalTax = 0;
$totalGross = 0;

$ag = dbGetAccountGroups('MAJOR', EXPENDITURE);
$expenditureAccountGroups = mapParameters($ag, 'accountGroup', 'groupDescription');

// $ownerGroupName = "OWNER EXPENDITURE";
// $DRGroupName = "RECOVERABLE EXPENDITURE";
// $VOGroupName = "VARIABLE OUTGOINGS EXPENDITURE";
// $VOGroupName = "COMMON ARE MAINATENANCE EXPENDITURE";
//	$OwnerRemmitGroupName = "OWNER REMITTANCE/S";

// ########################### PDF GENERATION FUNCTIONS #####################################

$page++;

global $pageXCoord;
$pageXCoord = 842;
global $pageYCoord;
$pageYCoord = 595;
global $currentYCoord;
$currentYCoord = 620;
global $currentXcoord;
$currentXcoord = 50;
global $lineHeight;
$lineHeight = 12;
global $amtWidth;
$amtWidth = 60;

global $client;
global $propertyName;
global $periodDescription;
global $periodFrom;
global $periodTo;
global $propertyID;

// ########################################################################################
// #########  EXPENSE DETAILS PER TRACC3 GROUPS - EXPOWN,EXPVO & EXPDR ####################
// ########################################################################################

// ##########################OWNER	EXPENSES#############################################
$excel = new Spreadsheet();

$excel->setActiveSheetIndex(0); // Activate it
$excel->getActiveSheet()->setTitle('Detailed Expenditure');

$excel->getActiveSheet()->getStyle('A1:J1')->getFont()->setBold(true);
$excel->getActiveSheet()->SetCellValue(cellReference(1, 1), 'Detailed Expenditure');

$excel->getActiveSheet()->SetCellValue(cellReference(2, 1), 'Period From:');
$excel->getActiveSheet()->SetCellValue(cellReference(2, 2), "$periodFrom To: $periodTo");

$excel->getActiveSheet()->SetCellValue(cellReference(4, 1), 'Owner:');
$excel->getActiveSheet()->SetCellValue(cellReference(4, 2), "$client");
$excel->getActiveSheet()->SetCellValue(cellReference(5, 1), 'Property:');
$excel->getActiveSheet()->SetCellValue(cellReference(5, 2), $propertyName . " [$propertyID]");
$excel->getActiveSheet()->SetCellValue(cellReference(6, 1), 'Report For:');
$excel->getActiveSheet()->SetCellValue(cellReference(6, 2), $periodDescription);

$excel->getActiveSheet()->getStyle('A9:J9')->getFont()->setBold(true);
$excel->getActiveSheet()->SetCellValue(cellReference(9, 1), 'Account Name');
$excel->getActiveSheet()->SetCellValue(cellReference(9, 2), 'Payee');
$excel->getActiveSheet()->SetCellValue(cellReference(9, 3), 'Invoice #');
$excel->getActiveSheet()->SetCellValue(cellReference(9, 4), 'Description');
$excel->getActiveSheet()->SetCellValue(cellReference(9, 5), 'Payment Date');
$excel->getActiveSheet()->SetCellValue(cellReference(9, 6), 'From Date');
$excel->getActiveSheet()->SetCellValue(cellReference(9, 7), 'To Date');
// $excel->getActiveSheet()->SetCellValue(cellReference(9,8),"Net $");
$excel->getActiveSheet()->SetCellValue(cellReference(9, 8), 'Net ' . $_SESSION['country_default']['currency_symbol']);
// $excel->getActiveSheet()->SetCellValue(cellReference(9,9),"GST $");
$excel->getActiveSheet()->SetCellValue(cellReference(9, 9), $_SESSION['country_default']['tax_label'] . ' ' . $_SESSION['country_default']['currency_symbol']);
// $excel->getActiveSheet()->SetCellValue(cellReference(9,10),"Gross $");
$excel->getActiveSheet()->SetCellValue(cellReference(9, 10), 'Gross ' . $_SESSION['country_default']['currency_symbol']);

foreach (range('A', $excel->getActiveSheet()->getHighestDataColumn()) as $col) {
    $excel
        ->getActiveSheet()
        ->getColumnDimension($col)
        ->setAutoSize(true);
}

$clientDirectory = str_replace(' ', '', $_SESSION['database']);
$pathPrefix = REPORTPATH . '/';
$path = $pathPrefix;
$timestamp = date('dmYHis');
$filename_description = preg_replace("/([^\w\d])/", '_', $view->items['filename_description']);
if (strlen($filename_description) > 0) {
    $fileName = $filename_description . '_';
} else {
    $fileName = 'OwnersReports_';
}

$xlsDownloadPath = "{$path}{$clientDirectory}/xlsx/" . DOC_OWNERSTATEMENT . '/' . $fileName . $propertyID . '_' . $timestamp . '.xlsx';

checkDirPath($xlsDownloadPath, true);

$cashPaymentsDetailPages = $page;

$count = 0;
$excelRow = 10;
foreach ($expenditureAccountGroups as $groupCode => $groupName) {

    //            pre_print_r($groupName.'----test');
    switch ($groupCode) {
        case 'EXPOWN': $a = getAcctSubGroupOwnerExpWOUnit();
            break;
        case 'EXPDR': $a = getAcctSubGroupDRVOWOUnit('EXPDR%');
            break;
        case 'EXPVO': $a = getAcctSubGroupDRVOWOUnit('EXPVO%');
            break;
        case 'BSPMT': $a = getAcctSubGroupDRVOWOUnit('BSPMT%');
            break;
    }
    $count++;
    printGroupExcelWOUnit($a, $groupName, ($count == 3), $excel, $excelRow);
}
// ##################################FINAL OWNER EXENDITURE ###########################################



$accSubGroupOwnerRemmit = [];
$accSubGroupOwnerRemmit[0] = ['subgroup' => 'EXPOPTREMI', 'pmas_desc' => 'OWNERS REMITTANCE/S'];
$accSubGroupOwnerRemmit[1] = ['subgroup' => 'EXPOWNGST', 'pmas_desc' => 'GST PAYMENTS'];


printGroupExcelWOUnit($accSubGroupOwnerRemmit, $OwnerRemmitGroupName, false, $excel, $excelRow);


// ###############################DIRECTLY RECOVERABLE	EXPENSES#########################

// ############################VARIABLE OUTGOINGS	EXPENSES#################################

$_excel = new Xlsx($excel);
$_excel->save($xlsDownloadPath);
$clientDirectory = str_replace(' ', '', $_SESSION['database']);
$xlsDownloadLink = "{$clientDirectory}/xlsx/" . DOC_OWNERSTATEMENT . '/' . $fileName . $propertyID . '_' . $timestamp . '.xlsx';
$expenditureExcelAttachments[] = [$xlsDownloadPath, $fileName . $propertyID . '_' . $timestamp . '.xlsx'];
renderDownloadLink($xlsDownloadLink);

<?php

if (! function_exists('cashbook_header')) {
    function cashbook_header($cont, $propertyID)
    {

        global $pdf;
        global $propertyName;
        global $line;
        global $description;
        global $periodDescription;
        global $client;
        global $page;
        global $periodFrom;
        global $periodTo;
        global $date;
        global $logo;
        // Landscape Paper
        global $xCoord_zern_landscape;
        global $yCoord_zern_landscape;
        global $scale_zern_landscape;
        global  $_fonts;

        if ($cont == 'cont') {
            $page_name = 'BAS Supporting Documentation (...)';
        } else {
            $page_name = 'BAS Supporting Documentation';
        }
        $page++;

        $pdf->setFontExt($_fonts['Helvetica-Bold'], 9);


        $pdf->showBoxed("$page_name", 271, 540, 300, 30, 'center', '');

        $pdf->setFontExt($_fonts['Helvetica'], 8);
        $pdf->show_xy('Owner:', 22, 540);
        $pdf->continue_text('Property:');
        $pdf->continue_text('Report For:');
        $pdf->show_xy($client, 70, 540);
        $pdf->continue_text($propertyName . " [$propertyID]");
        $pdf->continue_text($periodDescription);


        $pdf->setlinewidth(0.5);
        $pdf->moveto(18, 515);
        $pdf->lineto(830, 515);
        $pdf->stroke();

        $marginVertical = 10;
        $pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
        $pdf->showBoxed('Date', 22, 485 - $marginVertical, 40, 30, 'left', '');
        $pdf->showBoxed('Lease/Supplier', 62, 475 - 6, 50, 40, 'left', '');
        $pdf->showBoxed('AP Invoice#', 112, 475 - $marginVertical, 70, 40, 'left', '');
        $pdf->showBoxed('Tenant/Payee', 182, 475 - $marginVertical, 130, 40, 'left', '');
        $pdf->showBoxed('Description', 312, 475 - $marginVertical, 155, 40, 'left', '');
        $pdf->showBoxed('Account', 467, 475 - $marginVertical, 40, 40, 'left', '');
        // $pdf->showBoxed ('CHQ/EFT#', 497, 475, 35, 40, "left", "");
        // $pdf->showBoxed ('Receipt/Payment #', 532, 475, 35, 40, "left", "");
        $pdf->showBoxed('From', 507, 475 - $marginVertical, 40, 40, 'center', '');
        $pdf->showBoxed('To', 547, 475 - $marginVertical, 40, 40, 'center', '');
        $pdf->showBoxed('Net', 587, 475 - $marginVertical, 80, 40, 'right', '');
        $pdf->showBoxed($_SESSION['country_default']['tax_label'], 667, 475 - $marginVertical, 80, 40, 'right', '');
        $pdf->showBoxed('Total', 747, 475 - $marginVertical, 80, 40, 'right', '');



        $pdf->setFontExt($_fonts['Helvetica'], 8);
        $pdf->showBoxed("Period From: $periodFrom To: $periodTo", 271, 529, 300, 30, 'center', '');

        if ($logo) {
            generateLogo('landscape');
        }

        $pdf->setlinewidth(0.5);
        $pdf->moveto(18, 485);
        $pdf->lineto(830, 485);
        $pdf->stroke();


        $pdf->setFontExt($_fonts['Helvetica'], 8);
        $pdf->showBoxed("Page {$page}", 750, 0, 75, 30, 'right', '');

        $line = 480;
    }
}
$properties = [];
$pdf->begin_page_ext(842, 595, '');
cashbook_header('new', $propertyID);
$line = 480;

$cbReportType = 'property';
$dataID = $propertyID;


$transactionOption = 'allTransactions';
$openBalance['transactionDate'] = 'Opening Balance';
$propNetChange['transactionDate'] = 'Property Total';
$closedBalance['transactionDate'] = 'Closing Balance';
$fromDate = $view->items['periodFrom'];
$toDate = $view->items['periodTo'];
$allDates = 'No';
$pageBreak = ($context['pageBreak'] == 'Yes') ? true : false;
$showReceiptNo = ($context['showReceiptNo'] == 'Yes') ? true : false;

$reportResult = dbGetCashBook($cbReportType, $dataID, $transactionOption, $allDates, $accountID, $fromDate, $toDate, $leaseID, $supplierID, 'sortAccountID');
$count = count($reportResult ?? []);

if ($transactionOption != 'receiptsOnly' && $transactionOption != 'paymentsOnly') {
    $OBResult = dbGetCashBookOpeningBalance($cbReportType, $dataID, $accountID, $fromDate);
    foreach ($OBResult as $k => $v) {
        $property = ($cbReportType == 'bank') ? 'bank' : $v['propertyID'];

        $closingBalance[$property]['balance'] = $openingBalance[$property]['balance'] = $v['amount'];
        $closingBalance[$property]['netAmountAll'] = $openingBalance[$property]['netAmountAll'] = $v['net'];
        $closingBalance[$property]['gstAmountAll'] = $openingBalance[$property]['gstAmountAll'] = $v['gst'];
        $closingBalance[$property]['totalAmountAll'] = $openingBalance[$property]['totalAmountAll'] = $v['amount'];
    }
}


$accountTotalNet = $accountTotalGST = $accountTotalAmt = $k = 0;
$incomeTotalNet = $incomeTotalGST = $incomeTotalAmt = 0;
$expenseTotalNet = $expenseTotalGST = $expenseTotalAmt = 0;
$lastAccountCode = $lastProperty = $lastTransType = '';

foreach ($reportResult as $v) {

    $propOrBank = ($cbReportType == 'property') ? $v['propertyID'] : 'bank';
    if ($lastAccountCode != '' and $lastAccountCode != $v['accountID']
                    or ($lastProperty != $propOrBank and $lastProperty != '')) {
        $k++;
        $properties[$lastProperty][$k]['bold'] = false;
        $properties[$lastProperty][$k]['width']['transactionDate'] = 300;
        $properties[$lastProperty][$k]['bgcolor'] = [0.9, 0.9, 0.9];
        $properties[$lastProperty][$k]['leaseName'] = '';
        $properties[$lastProperty][$k]['supplierName'] = '';
        $properties[$lastProperty][$k]['transactionDate'] = "Account Total for $lastAccountCode " . dbGetAccountName($lastAccountCode);
        if ($lastTransType == 'receipt') {
            $properties[$lastProperty][$k]['receiptNetAmount'] = $format == FILETYPE_XLS ? $accountTotalNet : toDecimal($accountTotalNet, 2);
            $properties[$lastProperty][$k]['receiptGSTAmount'] = $format == FILETYPE_XLS ? $accountTotalGST : toDecimal($accountTotalGST, 2);
            $properties[$lastProperty][$k]['receiptTotalAmount'] = $format == FILETYPE_XLS ? $accountTotalAmt : toDecimal($accountTotalAmt, 2);

            $properties[$lastProperty][$k]['netAmountAll'] = $format == FILETYPE_XLS ? $accountTotalNet : toDecimal($accountTotalNet, 2);
            $properties[$lastProperty][$k]['gstAmountAll'] = $format == FILETYPE_XLS ? $accountTotalGST : toDecimal($accountTotalGST, 2);
            $properties[$lastProperty][$k]['totalAmountAll'] = $format == FILETYPE_XLS ? $accountTotalAmt : toDecimal($accountTotalAmt, 2);
        } else {
            $properties[$lastProperty][$k]['paymentsNetAmount'] = $format == FILETYPE_XLS ? -$accountTotalNet : toDecimal(-$accountTotalNet, 2);
            $properties[$lastProperty][$k]['paymentsGSTAmount'] = $format == FILETYPE_XLS ? -$accountTotalGST : toDecimal(-$accountTotalGST, 2);
            $properties[$lastProperty][$k]['paymentsTotalAmount'] = $format == FILETYPE_XLS ? -$accountTotalAmt : toDecimal(-$accountTotalAmt, 2);

            $properties[$lastProperty][$k]['netAmountAll'] = $format == FILETYPE_XLS ? -$accountTotalNet : toDecimal(-$accountTotalNet, 2);
            $properties[$lastProperty][$k]['gstAmountAll'] = $format == FILETYPE_XLS ? -$accountTotalGST : toDecimal(-$accountTotalGST, 2);
            $properties[$lastProperty][$k]['totalAmountAll'] = $format == FILETYPE_XLS ? -$accountTotalAmt : toDecimal(-$accountTotalAmt, 2);

        }

        // income / expense total
        if ($lastTransType != '' and $lastTransType != $v['transactionType'] or ($lastProperty != $propOrBank and $lastProperty != '')) {
            $k++;

            $properties[$lastProperty][$k]['bold'] = true;
            $properties[$lastProperty][$k]['bgcolor'] = [220 / 255, 242 / 255, 255 / 255];

            if ($format != FILETYPE_SCREEN) {
                $properties[$lastProperty][$k]['leaseName'] = '';
                $properties[$lastProperty][$k]['supplierName'] = '';
                $properties[$lastProperty][$k]['transactionDate'] = $lastTransType == 'receipt' ? 'Income Total' : 'Expense Total';
                $properties[$lastProperty][$k]['width']['transactionDate'] = 300;
            } else {
                $properties[$lastProperty][$k]['transactionDate'] = $lastTransType == 'receipt' ? 'Income Total' : 'Expense Total';
            }

            if ($lastTransType == 'receipt') {
                $properties[$lastProperty][$k]['receiptNetAmount'] = $format == FILETYPE_XLS ? $incomeTotalNet : toDecimal($incomeTotalNet, 2);
                $properties[$lastProperty][$k]['receiptGSTAmount'] = $format == FILETYPE_XLS ? $incomeTotalGST : toDecimal($incomeTotalGST, 2);
                $properties[$lastProperty][$k]['receiptTotalAmount'] = $format == FILETYPE_XLS ? $incomeTotalAmt : toDecimal($incomeTotalAmt, 2);

                $properties[$lastProperty][$k]['netAmountAll'] = $format == FILETYPE_XLS ? $incomeTotalNet : toDecimal($incomeTotalNet, 2);
                $properties[$lastProperty][$k]['gstAmountAll'] = $format == FILETYPE_XLS ? $incomeTotalGST : toDecimal($incomeTotalGST, 2);
                $properties[$lastProperty][$k]['totalAmountAll'] = $format == FILETYPE_XLS ? $incomeTotalAmt : toDecimal($incomeTotalAmt, 2);
            } else {
                $properties[$lastProperty][$k]['paymentsNetAmount'] = $format == FILETYPE_XLS ? -$expenseTotalNet : toDecimal(-$expenseTotalNet, 2);
                $properties[$lastProperty][$k]['paymentsGSTAmount'] = $format == FILETYPE_XLS ? -$expenseTotalGST : toDecimal(-$expenseTotalGST, 2);
                $properties[$lastProperty][$k]['paymentsTotalAmount'] = $format == FILETYPE_XLS ? -$expenseTotalAmt : toDecimal(-$expenseTotalAmt, 2);

                $properties[$lastProperty][$k]['netAmountAll'] = $format == FILETYPE_XLS ? -$expenseTotalNet : toDecimal(-$expenseTotalNet, 2);
                $properties[$lastProperty][$k]['gstAmountAll'] = $format == FILETYPE_XLS ? -$expenseTotalGST : toDecimal(-$expenseTotalGST, 2);
                $properties[$lastProperty][$k]['totalAmountAll'] = $format == FILETYPE_XLS ? -$expenseTotalAmt : toDecimal(-$expenseTotalAmt, 2);

            }

        }

        $k++;
        $properties[$lastProperty][$k]['leaseName'] =
        $properties[$lastProperty][$k]['supplierName'] =
        $properties[$lastProperty][$k]['transactionDate'] = $format == FILETYPE_SCREEN ? '&nbsp' : '';

        $accountTotalNet = $accountTotalGST = $accountTotalAmt = 0;
        if ($lastProperty != $propOrBank and $lastProperty != '') {
            $incomeTotalNet = $incomeTotalGST = $incomeTotalAmt = $expenseTotalNet = $expenseTotalGST = $expenseTotalAmt = 0;
        }
    }
    $k++;
    $property = ($cbReportType == 'property') ? $v['propertyID'] : 'bank';
    $properties[$property][$k]['transactionDate'] = $v['transactionDate'];
    $properties[$property][$k]['propertyID'] = $v['propertyID'];
    $properties[$property][$k]['description'] = $v['description'];
    $properties[$property][$k]['chequeNumber'] = $v['chequeNumber'];
    $properties[$property][$k]['accountID'] = $v['accountID'];
    $properties[$property][$k]['fromDate'] = $v['fromDate'];
    $properties[$property][$k]['toDate'] = $v['toDate'];
    $properties[$property][$k]['leaseSupplierID'] = $v['leaseSupplierID'];
    $properties[$property][$k]['leaseSupplierName'] = $v['leaseSupplierName'];

    if ($v['transactionType'] == 'receipt') {
        $properties[$property][$k]['unitCode'] = $v['unitCode'];
        if ($format == FILETYPE_XLS) {
            $properties[$property][$k]['netAmountAll'] = $v['netAmount'];
            $properties[$property][$k]['gstAmountAll'] = $v['gstAmount'];
            $properties[$property][$k]['totalAmountAll'] = $v['totalAmount'];
        } else {
            $properties[$property][$k]['netAmountAll'] = toDecimal($v['netAmount'], 2);
            $properties[$property][$k]['gstAmountAll'] = toDecimal($v['gstAmount'], 2);
            $properties[$property][$k]['totalAmountAll'] = toDecimal($v['totalAmount'], 2);
        }
        $closingBalance[$property]['netAmountAll'] += $v['netAmount'];
        $closingBalance[$property]['gstAmountAll'] += $v['gstAmount'];
        $closingBalance[$property]['totalAmountAll'] += $v['totalAmount'];
    } else {
        if ($format == FILETYPE_XLS) {
            $properties[$property][$k]['netAmountAll'] = -$v['netAmount'];
            $properties[$property][$k]['gstAmountAll'] = -$v['gstAmount'];
            $properties[$property][$k]['totalAmountAll'] = -$v['totalAmount'];
        } else {
            $properties[$property][$k]['netAmountAll'] = toDecimal(-$v['netAmount'], 2);
            $properties[$property][$k]['gstAmountAll'] = toDecimal(-$v['gstAmount'], 2);
            $properties[$property][$k]['totalAmountAll'] = toDecimal(-$v['totalAmount'], 2);
        }
        $closingBalance[$property]['netAmountAll'] -= $v['netAmount'];
        $closingBalance[$property]['gstAmountAll'] -= $v['gstAmount'];
        $closingBalance[$property]['totalAmountAll'] -= $v['totalAmount'];
    }

    if ($v['transactionType'] == 'receipt') {
        $properties[$property][$k]['leaseID'] = $v['leaseID'];
        $properties[$property][$k]['leaseName'] = $v['leaseName'];
        $properties[$property][$k]['supplierID'] = $v['supplierID'];
        $properties[$property][$k]['supplierName'] = $v['supplierName'];

        $properties[$property][$k]['chequeNumber'] = dbGetChequeNumber($v['transactionType'], ['debtorID' => $v['debtorID'], 'fromDate' => $fromDate, 'toDate' => $toDate]);
        $properties[$property][$k]['ReceiptPaymentNumber'] = dbGetChequeNumber($v['transactionType'], ['debtorID' => $v['debtorID'], 'transactionDate' => $v['transactionDate'], 'propertyID' => $v['propertyID'], 'batchNumber' => $v['paymentsExtraLine']]);

        $properties[$property][$k]['ChequeEFTNumber'] = $v['paymentReference'];
        if ($properties[$property][$k]['ChequeEFTNumber'] == 'DIRECT DEPOSIT') {
            $properties[$property][$k]['ChequeEFTNumber'] = 'EFT';
        }

        $properties[$property][$k]['receiptNumber'] = $v['receiptNumber'];
        if ($format == FILETYPE_XLS) {
            $properties[$property][$k]['receiptNetAmount'] = $v['netAmount'];
            $properties[$property][$k]['receiptGSTAmount'] = $v['gstAmount'];
            $properties[$property][$k]['receiptTotalAmount'] = $v['totalAmount'];
        } else {
            $properties[$property][$k]['receiptNetAmount'] = toDecimal($v['netAmount'], 2);
            $properties[$property][$k]['receiptGSTAmount'] = toDecimal($v['gstAmount'], 2);
            $properties[$property][$k]['receiptTotalAmount'] = toDecimal($v['totalAmount'], 2);
        }
        $accountTotalNet += $v['netAmount'];
        $accountTotalGST += $v['gstAmount'];
        $accountTotalAmt += $v['totalAmount'];

        $incomeTotalNet += $v['netAmount'];
        $incomeTotalGST += $v['gstAmount'];
        $incomeTotalAmt += $v['totalAmount'];

        $closingBalance[$property]['receiptNetAmount'] += $v['netAmount'];
        $closingBalance[$property]['receiptGSTAmount'] += $v['gstAmount'];
        $closingBalance[$property]['receiptTotalAmount'] = bcadd($closingBalance[$property]['receiptTotalAmount'], $v['totalAmount'], 2);
        if ($properties[$property][$previousKey]['transactionDate']) {
            $properties[$property][$previousKey]['subTotal'] = ($v['receiptNumber'] != $previousReceipt && $properties[$property][$previousKey] && $reportResult[$previousKey]['receiptNumber']) ? (($format == FILETYPE_XLS) ? $subTotal : toDecimal($subTotal, 2)) : '';
        }
        if ($v['receiptNumber'] == $previousReceipt) {
            $subTotal = bcadd($subTotal, -$v['totalAmount'], 2);
        } else {
            $subTotal = -$v['totalAmount'];
        }
        $previousKey = $k;
        $previousReceipt = $v['receiptNumber'];
        if ($subTotal && $properties[$property][$previousKey] && $reportResult[$previousKey]['receiptNumber']) {
            $properties[$property][$previousKey]['subTotal'] = ($format == FILETYPE_XLS) ? $subTotal : toDecimal($subTotal, 2);
        }

        $closingBalance[$property]['balance'] = bcadd($closingBalance[$property]['balance'], $v['totalAmount'], 2);
        $properties[$property][$k]['balance'] = ($format == FILETYPE_XLS) ? $closingBalance[$property]['balance'] : toDecimal($closingBalance[$property]['balance'], 2);

    } else {

        $properties[$property][$k]['supplierID'] = $v['supplierID'];
        $properties[$property][$k]['supplierName'] = $v['supplierName'];
        $properties[$property][$k]['receiptNumber'] = $v['receiptNumber'];
        $properties[$property][$k]['receiptNumber'] = dbGetChequeNumber($v['transactionType'], ['batchNumber' => $v['paymentsExtraLine'], 'lineNumber' => $v['sequenceNumber']]);
        $properties[$property][$k]['ReceiptPaymentNumber'] = dbGetChequeNumber($v['transactionType'], ['batchNumber' => $v['paymentsExtraLine'], 'lineNumber' => $v['sequenceNumber']]);

        $properties[$property][$k]['ChequeEFTNumber'] = '';

        $properties[$property][$k]['ChequeEFTNumber'] = dbGetChequeEFTNumber($v['transactionType'], ['batchNumber' => $v['paymentsExtraLine']]);
        if ($properties[$property][$k]['ChequeEFTNumber'] == 'X') {
            $properties[$property][$k]['ChequeEFTNumber'] = 'EFT';
        }
        if ($properties[$property][$k]['ChequeEFTNumber'] == 'C') {
            $properties[$property][$k]['ChequeEFTNumber'] = 'CHQ';
        }
        if ($properties[$property][$k]['ChequeEFTNumber'] == 'Y') {
            $properties[$property][$k]['ChequeEFTNumber'] = 'BPAY';
        }
        if ($format == FILETYPE_XLS) {
            $properties[$property][$k]['paymentsNetAmount'] = -$v['netAmount'];
            $properties[$property][$k]['paymentsGSTAmount'] = -$v['gstAmount'];
            $properties[$property][$k]['paymentsTotalAmount'] = -$v['totalAmount'];
        } else {
            $properties[$property][$k]['paymentsNetAmount'] = toDecimal(-$v['netAmount'], 2);
            $properties[$property][$k]['paymentsGSTAmount'] = toDecimal(-$v['gstAmount'], 2);
            $properties[$property][$k]['paymentsTotalAmount'] = toDecimal(-$v['totalAmount'], 2);
        }

        $accountTotalNet += $v['netAmount'];
        $accountTotalGST += $v['gstAmount'];
        $accountTotalAmt += $v['totalAmount'];

        $expenseTotalNet += $v['netAmount'];
        $expenseTotalGST += $v['gstAmount'];
        $expenseTotalAmt += $v['totalAmount'];

        $closingBalance[$property]['paymentsNetAmount'] -= $v['netAmount'];
        $closingBalance[$property]['paymentsGSTAmount'] -= $v['gstAmount'];
        $closingBalance[$property]['paymentsTotalAmount'] = bcsub($closingBalance[$property]['paymentsTotalAmount'], $v['totalAmount'], 2);

        if ($properties[$property][$previousKey]['transactionDate']) {
            $properties[$property][$previousKey]['subTotal'] = ($v['chequeNumber'] != $previousCheque && $properties[$property][$previousKey] && $reportResult[$previousKey]['chequeNumber']) ? (($format == FILETYPE_XLS) ? $subTotal : toDecimal($subTotal, 2)) : '';
        }
        if ($v['chequeNumber'] == $previousCheque) {
            $subTotal = bcadd($subTotal, -$v['totalAmount'], 2);
        } else {
            $subTotal = -$v['totalAmount'];
        }
        $previousKey = $k;
        $previousCheque = $v['chequeNumber'];
        if ($subTotal && $properties[$property][$previousKey] && $reportResult[$previousKey]['chequeNumber']) {
            $properties[$property][$previousKey]['subTotal'] = ($format == FILETYPE_XLS) ? $subTotal : toDecimal($subTotal, 2);
        }

        $closingBalance[$property]['balance'] = bcadd($closingBalance[$property]['balance'], $v['totalAmount'], 2);
        $properties[$property][$k]['balance'] = ($format == FILETYPE_XLS) ? $closingBalance[$property]['balance'] : toDecimal($closingBalance[$property]['balance'], 2);
    }

    $lastProperty = $property;
    $lastAccountCode = $v['accountID'];
    $lastTransType = $v['transactionType'];
}



$k++;
$properties[$lastProperty][$k]['bold'] = false;
$properties[$lastProperty][$k]['width']['transactionDate'] = 300;
$properties[$lastProperty][$k]['bgcolor'] = [0.9, 0.9, 0.9];
$properties[$lastProperty][$k]['leaseName'] = '';
$properties[$lastProperty][$k]['supplierName'] = '';
$properties[$lastProperty][$k]['transactionDate'] = "Account Total for $lastAccountCode " . dbGetAccountName($lastAccountCode);

if ($lastTransType == 'receipt') {
    $properties[$lastProperty][$k]['receiptNetAmount'] = $format == FILETYPE_XLS ? $accountTotalNet : toDecimal($accountTotalNet, 2);
    $properties[$lastProperty][$k]['receiptGSTAmount'] = $format == FILETYPE_XLS ? $accountTotalGST : toDecimal($accountTotalGST, 2);
    $properties[$lastProperty][$k]['receiptTotalAmount'] = $format == FILETYPE_XLS ? $accountTotalAmt : toDecimal($accountTotalAmt, 2);

    $properties[$lastProperty][$k]['netAmountAll'] = $format == FILETYPE_XLS ? $accountTotalNet : toDecimal($accountTotalNet, 2);
    $properties[$lastProperty][$k]['gstAmountAll'] = $format == FILETYPE_XLS ? $accountTotalGST : toDecimal($accountTotalGST, 2);
    $properties[$lastProperty][$k]['totalAmountAll'] = $format == FILETYPE_XLS ? $accountTotalAmt : toDecimal($accountTotalAmt, 2);
} else {
    $properties[$lastProperty][$k]['paymentsNetAmount'] = $format == FILETYPE_XLS ? -$accountTotalNet : toDecimal(-$accountTotalNet, 2);
    $properties[$lastProperty][$k]['paymentsGSTAmount'] = $format == FILETYPE_XLS ? -$accountTotalGST : toDecimal(-$accountTotalGST, 2);
    $properties[$lastProperty][$k]['paymentsTotalAmount'] = $format == FILETYPE_XLS ? -$accountTotalAmt : toDecimal(-$accountTotalAmt, 2);

    $properties[$lastProperty][$k]['netAmountAll'] = $format == FILETYPE_XLS ? -$accountTotalNet : toDecimal(-$accountTotalNet, 2);
    $properties[$lastProperty][$k]['gstAmountAll'] = $format == FILETYPE_XLS ? -$accountTotalGST : toDecimal(-$accountTotalGST, 2);
    $properties[$lastProperty][$k]['totalAmountAll'] = $format == FILETYPE_XLS ? -$accountTotalAmt : toDecimal(-$accountTotalAmt, 2);

}

$k++;

$properties[$lastProperty][$k]['bold'] = true;
$properties[$lastProperty][$k]['bgcolor'] = [220 / 255, 242 / 255, 255 / 255];

if ($format != FILETYPE_SCREEN) {
    $properties[$lastProperty][$k]['leaseName'] = '';
    $properties[$lastProperty][$k]['supplierName'] = '';
    $properties[$lastProperty][$k]['transactionDate'] = $lastTransType == 'receipt' ? 'Income Total' : 'Expense Total';
    $properties[$lastProperty][$k]['width']['transactionDate'] = 300;
} else {
    $properties[$lastProperty][$k]['transactionDate'] = $lastTransType == 'receipt' ? 'Income Total' : 'Expense Total';
}



if ($lastTransType == 'receipt') {
    $properties[$lastProperty][$k]['receiptNetAmount'] = $format == FILETYPE_XLS ? $incomeTotalNet : toDecimal($incomeTotalNet, 2);
    $properties[$lastProperty][$k]['receiptGSTAmount'] = $format == FILETYPE_XLS ? $incomeTotalGST : toDecimal($incomeTotalGST, 2);
    $properties[$lastProperty][$k]['receiptTotalAmount'] = $format == FILETYPE_XLS ? $incomeTotalAmt : toDecimal($incomeTotalAmt, 2);

    $properties[$lastProperty][$k]['netAmountAll'] = $format == FILETYPE_XLS ? $incomeTotalNet : toDecimal($incomeTotalNet, 2);
    $properties[$lastProperty][$k]['gstAmountAll'] = $format == FILETYPE_XLS ? $incomeTotalGST : toDecimal($incomeTotalGST, 2);
    $properties[$lastProperty][$k]['totalAmountAll'] = $format == FILETYPE_XLS ? $incomeTotalAmt : toDecimal($incomeTotalAmt, 2);
} else {
    $properties[$lastProperty][$k]['paymentsNetAmount'] = $format == FILETYPE_XLS ? -$expenseTotalNet : toDecimal(-$expenseTotalNet, 2);
    $properties[$lastProperty][$k]['paymentsGSTAmount'] = $format == FILETYPE_XLS ? -$expenseTotalGST : toDecimal(-$expenseTotalGST, 2);
    $properties[$lastProperty][$k]['paymentsTotalAmount'] = $format == FILETYPE_XLS ? -$expenseTotalAmt : toDecimal(-$expenseTotalAmt, 2);

    $properties[$lastProperty][$k]['netAmountAll'] = $format == FILETYPE_XLS ? -$expenseTotalNet : toDecimal(-$expenseTotalNet, 2);
    $properties[$lastProperty][$k]['gstAmountAll'] = $format == FILETYPE_XLS ? -$expenseTotalGST : toDecimal(-$expenseTotalGST, 2);
    $properties[$lastProperty][$k]['totalAmountAll'] = $format == FILETYPE_XLS ? -$expenseTotalAmt : toDecimal(-$expenseTotalAmt, 2);

}


if ($format == FILETYPE_XLS) {
    if ($properties[$property][$previousKey] && $reportResult[$previousKey]['receiptNumber']) {
        $properties[$property][$previousKey]['subTotal'] = $subTotal;
    }
    if ($properties[$property][$previousKey] && $reportResult[$previousKey]['chequeNumber']) {
        $properties[$property][$previousKey]['subTotal'] = $subTotal;
    }
} else {
    if ($properties[$property][$previousKey] && $reportResult[$previousKey]['receiptNumber']) {
        $properties[$property][$previousKey]['subTotal'] = toDecimal($subTotal, 2);
    }
    if ($properties[$property][$previousKey] && $reportResult[$previousKey]['chequeNumber']) {
        $properties[$property][$previousKey]['subTotal'] = toDecimal($subTotal, 2);
    }
}



foreach ($properties as $k => $property) {
    //  $propertyName = $propertyList[$k]['name'];
    $ownerCode = dbGetPrimaryOwner($k);
    $ownerName = dbGetOwnerName($k);

    if ($allDates != 'Yes' && $toDate && $fromDate) {
        $header->subText = "$fromDate to $toDate";
    } else {
        $header->subText = 'all dates';
    }

    // Opening Balance
    if ($transactionOption != 'receiptsOnly' && $transactionOption != 'paymentsOnly') {
        $openBalance['balance'] = ($openingBalance[$k]['balance']) ? (($format == FILETYPE_XLS) ? $openingBalance[$k]['balance'] : toDecimal($openingBalance[$k]['balance'], 2)) : '0.00';
        $openBalance['netAmountAll'] = ($openingBalance[$k]['netAmountAll']) ? (($format == FILETYPE_XLS) ? $openingBalance[$k]['netAmountAll'] : toDecimal($openingBalance[$k]['netAmountAll'], 2)) : '0.00';
        $openBalance['gstAmountAll'] = ($openingBalance[$k]['gstAmountAll']) ? (($format == FILETYPE_XLS) ? $openingBalance[$k]['gstAmountAll'] : toDecimal($openingBalance[$k]['gstAmountAll'], 2)) : '0.00';
        $openBalance['totalAmountAll'] = ($openingBalance[$k]['totalAmountAll']) ? (($format == FILETYPE_XLS) ? $openingBalance[$k]['totalAmountAll'] : toDecimal($openingBalance[$k]['totalAmountAll'], 2)) : '0.00';

    }


    foreach ($property as $row) {

        $line -= 10;
        if ($line <= 70) {
            $pdf->setlinewidth(0.5);

            $pdf->moveto(18, $line);
            $pdf->lineto(830, $line);
            $pdf->stroke();

            // first line
            $pdf->moveto(18, $line);
            $pdf->lineto(18, 515);
            $pdf->stroke();

            // LAST LINE
            $pdf->moveto(830, 515);
            $pdf->lineto(830, $line);
            $pdf->stroke();



            $traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'BASSupport', A4_LANDSCAPE);
            $traccFooter->prerender($pdf);


            $pdf->end_page_ext('');
            $pdf->begin_page_ext(842, 595, '');
            cashbook_header('cont', $propertyID);

            $line = 470;
        }


        if (strpos($row['transactionDate'], 'Account Total for') !== false || strpos($row['transactionDate'], 'Income Total') !== false || strpos($row['transactionDate'], 'Expense Total') !== false) {

            if (strpos($row['transactionDate'], 'Account') !== false) {
                $pdf->setFontExt($_fonts['Helvetica'], 7);
            } else {
                $pdf->setFontExt($_fonts['Helvetica-Bold'], 7);
            }
            $line -= 5;
            $pdf->save();

            $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
            $pdf->setlinewidth(0.5);
            $pdf->moveto(18, $line);
            $pdf->lineto(830, $line);
            $pdf->stroke();

            $pdf->setlinewidth(0.5);
            $pdf->moveto(18, $line + 12);
            $pdf->lineto(830, $line + 12);
            $pdf->stroke();

            $pdf->setColorExt('fill', 'rgb', 0.88, 0.88, 0.88, 0);
            $pdf->rect(18, $line, 812, 12);
            $pdf->fill();



            $pdf->restore();

            $pdf->showBoxed($row['transactionDate'], 22, $line, 200, 10, 'left', '');
            $pdf->showBoxed($row['netAmountAll'], 587, $line, 80, 10, 'right', '');
            $pdf->showBoxed($row['gstAmountAll'], 667, $line, 80, 10, 'right', '');
            $pdf->showBoxed($row['totalAmountAll'], 747, $line, 80, 10, 'right', '');



        } else {
            $pdf->setFontExt($_fonts['Helvetica'], 7);
            $pdf->showBoxed($row['transactionDate'], 22, $line, 40, 10, 'left', '');
            $pdf->showBoxed($row['leaseSupplierID'], 62, $line, 60, 10, 'left', '');
            $pdf->showBoxed($row['chequeNumber'], 112, $line, 70, 10, 'left', '');
            $pdf->showBoxed($row['leaseSupplierName'], 182, $line, 140, 10, 'left', '');
            $pdf->showBoxed($row['description'], 317, $line, 152, 10, 'left', '');
            $pdf->showBoxed($row['accountID'], 467, $line, 40, 10, 'left', '');
            // $pdf->showBoxed ($row['ChequeEFTNumber'], 497, $line, 35, 10, "left", "");
            // $pdf->showBoxed ($row['receiptNumber'], 532, $line, 35, 10, "left", "");
            $pdf->showBoxed($row['fromDate'], 507, $line, 40, 10, 'center', '');
            $pdf->showBoxed($row['toDate'], 547, $line, 40, 10, 'center', '');
            $pdf->showBoxed($row['netAmountAll'], 587, $line, 80, 10, 'right', '');
            $pdf->showBoxed($row['gstAmountAll'], 667, $line, 80, 10, 'right', '');
            $pdf->showBoxed($row['totalAmountAll'], 747, $line, 80, 10, 'right', '');
        }
    }

    $closedBalance['receiptNetAmount'] = $closingBalance[$k]['receiptNetAmount'];
    $closedBalance['receiptGSTAmount'] = $closingBalance[$k]['receiptGSTAmount'];
    $closedBalance['receiptTotalAmount'] = $closingBalance[$k]['receiptTotalAmount'];
    $closedBalance['paymentsNetAmount'] = $closingBalance[$k]['paymentsNetAmount'];
    $closedBalance['paymentsGSTAmount'] = $closingBalance[$k]['paymentsGSTAmount'];
    $closedBalance['paymentsTotalAmount'] = $closingBalance[$k]['paymentsTotalAmount'];
    $closedBalance['balance'] = $closingBalance[$k]['balance'];

    $openBalance['netAmountAll'] = $openingBalance[$k]['netAmountAll'];
    $openBalance['gstAmountAll'] = $openingBalance[$k]['gstAmountAll'];
    $openBalance['totalAmountAll'] = $openingBalance[$k]['totalAmountAll'];

    $propNetChange['netAmountAll'] = ($closingBalance[$k]['receiptNetAmount']) + (-$closingBalance[$k]['paymentsNetAmount']);
    $propNetChange['gstAmountAll'] = ($closingBalance[$k]['receiptGSTAmount']) + (-$closingBalance[$k]['paymentsGSTAmount']);
    $propNetChange['totalAmountAll'] = ($closingBalance[$k]['receiptTotalAmount']) + (-$closingBalance[$k]['paymentsTotalAmount']);

    $closedBalance['netAmountAll'] = $openingBalance[$k]['netAmountAll'] + ($closingBalance[$k]['receiptNetAmount']) + (-$closingBalance[$k]['paymentsNetAmount']);
    $closedBalance['gstAmountAll'] = $openingBalance[$k]['gstAmountAll'] + ($closingBalance[$k]['receiptGSTAmount']) + (-$closingBalance[$k]['paymentsGSTAmount']);
    $closedBalance['totalAmountAll'] = $openingBalance[$k]['totalAmountAll'] + ($closingBalance[$k]['receiptTotalAmount']) + (-$closingBalance[$k]['paymentsTotalAmount']);


    $openBalance['netAmountAll'] = toDecimal($openBalance['netAmountAll'], 2);
    $openBalance['gstAmountAll'] = toDecimal($openBalance['gstAmountAll'], 2);
    $openBalance['totalAmountAll'] = toDecimal($openBalance['totalAmountAll'], 2);

    $propNetChange['netAmountAll'] = toDecimal($propNetChange['netAmountAll'], 2);
    $propNetChange['gstAmountAll'] = toDecimal($propNetChange['gstAmountAll'], 2);
    $propNetChange['totalAmountAll'] = toDecimal($propNetChange['totalAmountAll'], 2);

    $closedBalance['netAmountAll'] = toDecimal($closedBalance['netAmountAll'], 2);
    $closedBalance['gstAmountAll'] = toDecimal($closedBalance['gstAmountAll'], 2);
    $closedBalance['totalAmountAll'] = toDecimal($closedBalance['totalAmountAll'], 2);

    $closedBalance['receiptNetAmount'] = toDecimal($closedBalance['receiptNetAmount'], 2);
    $closedBalance['receiptGSTAmount'] = toDecimal($closedBalance['receiptGSTAmount'], 2);
    $closedBalance['receiptTotalAmount'] = toDecimal($closedBalance['receiptTotalAmount'], 2);
    $closedBalance['paymentsNetAmount'] = toDecimal($closedBalance['paymentsNetAmount'], 2);
    $closedBalance['paymentsGSTAmount'] = toDecimal($closedBalance['paymentsGSTAmount'], 2);
    $closedBalance['paymentsTotalAmount'] = toDecimal($closedBalance['paymentsTotalAmount'], 2);
    $closedBalance['balance'] = toDecimal($closedBalance['balance'], 2);


    if ($transactionOption != 'receiptsOnly' and $transactionOption != 'paymentsOnly' && is_array($property)) {
        $openBalance['width']['transactionDate'] = 300;
        $propNetChange['width']['transactionDate'] = 300;
        $closedBalance['width']['transactionDate'] = 300;

        // $pdf->setFontExt($_fonts["Helvetica-Bold"], 7);
        // $line -= 15;
        // $pdf->save();
        // $pdf->setColorExt("both", "rgb", 0, 0, 0, 0);
        // $pdf->setlinewidth(0.5);
        // $pdf->moveto(18, $line);
        // $pdf->lineto(830, $line);
        // $pdf->stroke();
        // $pdf->setlinewidth(0.5);
        // $pdf->moveto(18, $line+12);
        // $pdf->lineto(830, $line+12);
        // $pdf->stroke();
        // $pdf->setColorExt("fill", "rgb", 0.88, 0.88, 0.88, 0);
        // $pdf->rect(18,$line,812,12);
        // $pdf->fill();
        // $pdf->restore();

        // $pdf->showBoxed ($openBalance['transactionDate'], 22, $line, 200, 10, "left", "");
        // $pdf->showBoxed ($openBalance['netAmountAll'], 647, $line, 60, 10, "right", "");
        // $pdf->showBoxed ($openBalance['gstAmountAll'], 707, $line, 60, 10, "right", "");
        // $pdf->showBoxed ($openBalance['totalAmountAll'], 767, $line, 60, 10, "right", "");

        if ($line <= 70) {
            $pdf->setlinewidth(0.5);

            $pdf->moveto(18, $line);
            $pdf->lineto(830, $line);
            $pdf->stroke();

            // first line
            $pdf->moveto(18, $line);
            $pdf->lineto(18, 515);
            $pdf->stroke();

            // LAST LINE
            $pdf->moveto(830, 515);
            $pdf->lineto(830, $line);
            $pdf->stroke();



            $traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'BASSupport', A4_LANDSCAPE);
            $traccFooter->prerender($pdf);


            $pdf->end_page_ext('');
            $pdf->begin_page_ext(842, 595, '');
            cashbook_header('cont', $propertyID);

            $line = 470;
        }

        $line -= 15;
        $pdf->save();
        $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        $pdf->setlinewidth(0.5);
        $pdf->moveto(18, $line);
        $pdf->lineto(830, $line);
        $pdf->stroke();
        $pdf->setlinewidth(0.5);
        $pdf->moveto(18, $line + 12);
        $pdf->lineto(830, $line + 12);
        $pdf->stroke();
        $pdf->setColorExt('fill', 'rgb', 0.88, 0.88, 0.88, 0);
        $pdf->rect(18, $line, 812, 12);
        $pdf->fill();
        $pdf->restore();

        $pdf->showBoxed($propNetChange['transactionDate'], 22, $line, 200, 10, 'left', '');
        $pdf->showBoxed($propNetChange['netAmountAll'], 587, $line, 80, 10, 'right', '');
        $pdf->showBoxed($propNetChange['gstAmountAll'], 667, $line, 80, 10, 'right', '');
        $pdf->showBoxed($propNetChange['totalAmountAll'], 747, $line, 80, 10, 'right', '');

        // if ($line <= 70)
        // {
        //     $pdf->setlinewidth(0.5);

        //     $pdf->moveto(18, $line);
        //     $pdf->lineto(830, $line);
        //     $pdf->stroke();

        //     //first line
        //     $pdf->moveto(18, $line);
        //     $pdf->lineto(18, 515);
        //     $pdf->stroke();

        //     //LAST LINE
        //     $pdf->moveto(830, 515);
        //     $pdf->lineto(830, $line);
        //     $pdf->stroke();



        //     $traccFooter = new TraccFooter("assets/clientLogos/tracc_logo_footer.jpg",'BASSupport', A4_LANDSCAPE);
        //     $traccFooter->prerender($pdf);


        //     $pdf->end_page_ext("");
        //     $pdf->begin_page_ext(842,595,"");
        //     cashbook_header("cont" , $propertyID);

        //     $line = 470;
        // }

        // $line -= 15;
        // $pdf->save();
        // $pdf->setColorExt("both", "rgb", 0, 0, 0, 0);
        // $pdf->setlinewidth(0.5);
        // $pdf->moveto(18, $line);
        // $pdf->lineto(830, $line);
        // $pdf->stroke();
        // $pdf->setlinewidth(0.5);
        // $pdf->moveto(18, $line+12);
        // $pdf->lineto(830, $line+12);
        // $pdf->stroke();
        // $pdf->setColorExt("fill", "rgb", 0.88, 0.88, 0.88, 0);
        // $pdf->rect(18,$line,812,12);
        // $pdf->fill();
        // $pdf->restore();

        // $pdf->showBoxed ($closedBalance['transactionDate'], 22, $line, 200, 10, "left", "");
        // $pdf->showBoxed ($closedBalance['netAmountAll'], 647, $line, 60, 10, "right", "");
        // $pdf->showBoxed ($closedBalance['gstAmountAll'], 707, $line, 60, 10, "right", "");
        // $pdf->showBoxed ($closedBalance['totalAmountAll'], 767, $line, 60, 10, "right", "");

        // if ($line <= 70)
        // {
        //     $pdf->setlinewidth(0.5);

        //     $pdf->moveto(18, $line);
        //     $pdf->lineto(830, $line);
        //     $pdf->stroke();

        //     //first line
        //     $pdf->moveto(18, $line);
        //     $pdf->lineto(18, 515);
        //     $pdf->stroke();

        //     //LAST LINE
        //     $pdf->moveto(830, 515);
        //     $pdf->lineto(830, $line);
        //     $pdf->stroke();



        //     $traccFooter = new TraccFooter("assets/clientLogos/tracc_logo_footer.jpg",'BASSupport', A4_LANDSCAPE);
        //     $traccFooter->prerender($pdf);


        //     $pdf->end_page_ext("");
        //     $pdf->begin_page_ext(842,595,"");
        //     cashbook_header("cont" , $propertyID);

        //     $line = 470;
        // }

        // $report->renderSubTotal($openBalance);
        // $report->renderSubTotal($propNetChange);
        // $report->renderSubTotal($closedBalance);

    }


}




$pdf->setlinewidth(0.5);

$pdf->moveto(18, $line);
$pdf->lineto(830, $line);
$pdf->stroke();

// first line
$pdf->moveto(18, $line);
$pdf->lineto(18, 515);
$pdf->stroke();

// LAST LINE
$pdf->moveto(830, 515);
$pdf->lineto(830, $line);
$pdf->stroke();

$traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'BASSupport', A4_LANDSCAPE);
$traccFooter->prerender($pdf);

$pdf->end_page_ext('');

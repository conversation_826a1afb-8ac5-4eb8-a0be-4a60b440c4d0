<?php

/**
 * Generate a door count report.
 *
 * @param  $context  array Mandatory. Referenced array containing data needed to continue.
 *
 * <AUTHOR> Reyes
 *
 * @since 2015-07-31
 **/
function websiteCountReport(&$context)
{
    $userpermission = new userpermission();
    $result = $userpermission->userHasPageAccess(__FUNCTION__);

    // Page Template
    if (! isPostback()) {
        $view = new MasterPage(userViews(), '/managementReports/websiteCountReport.html');
        $view->setSection($context['module']);
    } else {
        $view = new UserControl(userViews(), '/managementReports/websiteCountReport.html');
    }
    $view->bindAttributesFrom($_REQUEST);

    // Array Call-In
    $view->items['formatList'] =
    [
        FILETYPE_PDF => 'PDF',
        FILETYPE_XLS => 'Excel Spreadsheet',
        // FILETYPE_DOC => 'Word Document',
        FILETYPE_SCREEN => 'Print to Screen',
    ];
    $view->items['yesNoOption'] =
    [
        'Yes' => 'Yes',
        'No' => 'No',
    ];
    $view->items['colorSchemes'] =
    [
        'Bluish Cyan' => 'Bluish Cyan',
        'Medium Blue' => 'Medium Blue',
        'Indigo' => 'Indigo',
        'Magenta' => 'Magenta',
        'Pinkish Red' => 'Pinkish Red',
        'Red-Orange' => 'Red-Orange',
        'Orange' => 'Orange',
        'Yellow-Orange' => 'Yellow-Orange',
        'Yellow' => 'Yellow',
        'Greenish-Yellow' => 'Greenish-Yellow',
        'Yellow-Green' => 'Yellow-Green',
        'Blue-Green' => 'Blue-Green',
        'Navy Blue' => 'Navy Blue',
    ];

    $view->items['propertyGroupedList'] = dbPropertyGroupList(($view->items['method'] == 'property') ? null : $view->items['propertyManager'], ucwords(strtolower($_SESSION['country_default']['property_manager'])), true, 1);
    // Default Values
    if (empty($view->items['fromDate']) || empty($view->items['toDate'])) {
        if (empty($view->items['yearCurrent'])) {
            $view->items['yearCurrent'] = date('Y');
        }
        if (empty($view->items['yearPrevious']) && $view->items['yearCurrent']) {
            $view->items['yearPrevious'] = $view->items['yearCurrent'] - 1;
        }
    } else {
        $fromDateYear = explode('/', $view->items['fromDate']);
        $toDateYear = explode('/', $view->items['toDate']);
        $view->items['yearPrevious'] = $fromDateYear[2];
        $view->items['yearCurrent'] = $toDateYear[2];
    }
    if (! array_key_exists($view->items['format'], $view->items['formatList'])) {
        $view->items['format'] = FILETYPE_XLS;
    }
    if ($view->items['useFormula'] != 'No') {
        $view->items['useFormula'] = 'Yes';
    }
    if (! $view->items['colorScheme']) {
        $view->items['colorScheme'] = 'Bluish Cyan';
    }

    if ($view->items['selectAll'] == 'true') {
        $view->items['propertyID'] = [];
        foreach ($view->items['propertyGroupedList'] as $k => $v) {
            $view->items['propertyID'][$k] = $v['propertyID'];
        }
    } elseif ($view->items['propertyID']) {
        $view->items['propertyID'] = deserializeParameters($view->items['propertyID']);
    }
    if (isset($view->items['propertyID'])) {
        $view->items['yearList'] = dbGetWebsiteYears($view->items['propertyID']);
    }
    // $view->items['fromDate'] = '01/01/' . $view->items['yearCurrent'];
    // $view->items['toDate'] = '31/12/' . $view->items['yearCurrent'];

    // Action
    switch ($view->items['action']) {
        case 'generate':
            $yearList = mapParameters($view->items['yearList'], 'year', 'year');

            if (empty($view->items['propertyID'])) {
                $validationErrors[] = 'You need to select at least one property.';
            }
            if (empty($view->items['fromDate']) || empty($view->items['toDate'])) {
                if ($view->items['yearCurrent'] < $view->items['yearPrevious']) {
                    $validationErrors[] = 'Previous year cannot be greater than the reporting year.';
                }
                if (! array_key_exists($view->items['yearCurrent'], $yearList)) {
                    $validationErrors[] = 'Reporting year is invalid.';
                }
                if (! array_key_exists($view->items['yearPrevious'], $yearList)) {
                    $validationErrors[] = 'Previous year is invalid.';
                }
            } else {
                if ($view->items['fromDate'] and ! isValid($view->items['fromDate'], TEXT_SMARTDATE, false)) {
                    $validationErrors[] = 'Your reporting period is invalid (from date).';
                }
                if ($view->items['toDate'] and ! isValid($view->items['toDate'], TEXT_SMARTDATE, false)) {
                    $validationErrors[] = 'Your reporting period is invalid (to date).';
                }
                if (toDateStamp($view->items['toDate']) < toDateStamp($view->items['fromDate'])) {
                    $validationErrors[] = 'Your to date cannot be before your from date (reporting period).';
                }
            }

            if (empty($view->items['fromDate']) || empty($view->items['toDate'])) {
                $view->items['fromDate'] = '01/01/' . $view->items['yearCurrent'];
                $view->items['toDate'] = '31/12/' . $view->items['yearCurrent'];
            }

            if (noErrors($validationErrors)) {
                if ($view->items['format'] == FILETYPE_SCREEN) {
                    $view->render();
                }
                $context = $view->items;
                executeCommand('websiteCountReportProcess', 'managementReports');
            }
            break;
    }

    if ($view->items['format'] != FILETYPE_SCREEN || empty($view->items['action']) || $validationErrors) {
        // Post Feed
        $view->items['validationErrors'] = $validationErrors;

        // Display
        $view->render();
    }
}

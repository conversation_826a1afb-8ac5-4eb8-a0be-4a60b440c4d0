<?php

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

include_once 'functions/detailedExpenditureLandsapeFunctionsWOunit.php';

global $grandTotalNet;
global $grandTotalTax;
global $grandTotalGross;
global $totalNet;
global $totalTax;
global $totalGross;

global $lineExist;

$grandTotalNet = 0;
$grandTotalTax = 0;
$grandTotalGross = 0;

$totalNet = 0;
$totalTax = 0;
$totalGross = 0;

$ag = dbGetAccountGroups('MAJOR', EXPENDITURE);
$expenditureAccountGroups = mapParameters($ag, 'accountGroup', 'groupDescription');

// ########################### PDF GENERATION FUNCTIONS #####################################

$page++;

global $pageXCoord;
$pageXCoord = 842;
global $pageYCoord;
$pageYCoord = 595;
global $currentYCoord;
$currentYCoord = 620;
global $currentXcoord;
$currentXcoord = 50;
global $lineHeight;
$lineHeight = 12;
global $amtWidth;
$amtWidth = 60;

// ########################################################################################
// #########  EXPENSE DETAILS PER TRACC3 GROUPS - EXPOWN,EXPVO & EXPDR ####################
// ########################################################################################

// ##########################OWNER	EXPENSES#############################################
if ($view->items['format'] == FILETYPE_XLS) {
    $excel = new Spreadsheet();
    newPageExcelWOUnit($excel);

    $clientDirectory = str_replace(' ', '', $_SESSION['database']);
    $pathPrefix = REPORTPATH . '/';
    $path = $pathPrefix;
    $timestamp = date('dmYHis');
    $filename_description = preg_replace("/([^\w\d])/", '_', $view->items['filename_description']);
    if (strlen($filename_description) > 0) {
        $fileName = $filename_description . '_';
    } else {
        $fileName = 'OwnersReports_';
    }

    $xlsDownloadPath = "{$path}{$clientDirectory}/xlsx/" . DOC_OWNERSTATEMENT . '/' . $fileName . $timestamp . '.xlsx';
    checkDirPath($xlsDownloadPath, true);
} else {

    newPageReportWOUnit(true);
}
$cashPaymentsDetailPages = $page;

$count = 0;
$excelRow = 10;
foreach ($expenditureAccountGroups as $groupCode => $groupName) {
    switch ($groupCode) {
        case 'EXPOWN':
            $a = getAcctSubGroupOwnerExpWOUnit();
            break;
        case 'EXPDR':
            $a = getAcctSubGroupDRVOWOUnit('EXPDR%');
            break;
        case 'EXPVO':
            $a = getAcctSubGroupDRVOWOUnit('EXPVO%');
            break;
        case 'BSPMT':
            $a = getAcctSubGroupDRVOWOUnit('BSPMT%');
            break;
    }
    $count++;
    if ($view->items['format'] == FILETYPE_XLS) {
        printGroupExcelWOUnit($a, $groupName, ($count == 3), $excel, $excelRow);
    } else {
        printGroupPDFWOUnit($a, $groupName, ($count == 3));
    }

}

// ##################################FINAL OWNER EXENDITURE ###########################################

$accSubGroupOwnerRemmit = [];
$accSubGroupOwnerRemmit[0] = ['subgroup' => 'EXPOPTREMI', 'pmas_desc' => 'OWNERS REMITTANCE/S'];
$accSubGroupOwnerRemmit[1] = ['subgroup' => 'EXPOWNGST', 'pmas_desc' => 'GST PAYMENTS'];

if ($view->items['format'] == FILETYPE_XLS) {
    printGroupExcelWOUnit($accSubGroupOwnerRemmit, $OwnerRemmitGroupName, false, $excel, $excelRow);
} else {
    printGroupPDFWOUnit($accSubGroupOwnerRemmit, $OwnerRemmitGroupName, false);

    if (! $lineExist) {
        $lineExist = true;
        $pdf->setlinewidth(0.5);
        $pdf->moveto(18, $currentYCoord + 25);
        $pdf->lineto(18, 515);
        $pdf->stroke();

        $pdf->moveto(824, $currentYCoord + 25);
        $pdf->lineto(824, 515);
        $pdf->stroke();
    }

    $pdf->end_page_ext('');
}

if ($view->items['format'] == FILETYPE_XLS) {
    // excel
    $_excel = new Xlsx($excel);
    $_excel->save($xlsDownloadPath);
    $clientDirectory = str_replace(' ', '', $_SESSION['database']);
    $xlsDownloadLink = "{$clientDirectory}/xlsx/" . DOC_OWNERSTATEMENT . '/' . $fileName . $timestamp . '.xlsx';
    $expenditureExcelAttachments[] = [$xlsDownloadPath, $fileName . $timestamp . '.xlsx'];
    renderDownloadLink($xlsDownloadLink);
}

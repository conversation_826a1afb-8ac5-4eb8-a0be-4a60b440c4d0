<?php

/**
 * Process information gathered in order to generate a company report.
 *
 * @param  $context  array Mandatory. Referenced array containing data needed to continue.
 *
 * <AUTHOR> Reyes
 *
 * @since 2012-09-19
 *
 * @modified 2012-12-07: Added display on screen option. [Morph]
 **/
function companyReportProcess($context)
{
    global $clientDirectory, $pathPrefix;

    if (! isPostback()) {
        $view = new MasterPage(userViews(), '/managementReports/companyReportProcess.html');
        $view->setSection($context['module']);
    } else {
        $view = new UserControl(userViews(), '/managementReports/companyReportProcess.html');
    }

    $view->bindAttributesFrom($_REQUEST);

    $companies = deserializeParameters($view->items['companyID']);
    $companyCount = count($companies);

    if ($context[IS_TASK]) {
        $view->bindAttributesFrom($context);
        $view->bindAttributesFrom($_REQUEST);
        extract($context);
    } else {
        $view->bindAttributesFrom($context);
        $view->bindAttributesFrom($_REQUEST);

        $queue = new Queue(TASKTYPE_COMPANY_REPORT);
        if ($companyCount > THRESHOLD_COMPANYREPORT) {
            $queue->add($_SESSION['clientID'], $_SESSION['un'], 'command=companyReportProcess&module=managementReports', $_REQUEST);
        }
    }

    if ($context[IS_TASK] || $companyCount <= THRESHOLD_COMPANYREPORT) {
        $format = $view->items['format'];

        define('PAY_EFT', 1);
        define('PAY_BPAY', 2);
        define('PAY_CHQ', 3);

        $paymentMethodList[PAY_EFT] = 'EFT';
        if (cdf_isAU()) {
            $paymentMethodList[PAY_BPAY] = 'BPAY';
        }

        $paymentMethodList[PAY_CHQ] = 'Cheque';

        if (! $context[DOC_MASTER]) {
            $logoFile = dbGetClientLogo();
            $logoPath = "assets/clientLogos/{$logoFile}";
            $_filePath = "{$pathPrefix}{$clientDirectory}/{$format}/" . DOC_COMPANYREPORT . '/';
            $_downloadPath = "{$clientDirectory}/{$format}/" . DOC_COMPANYREPORT;
            $file = DOC_COMPANYREPORT . '_' . date('Ymd') . ".{$format}";
            $filePath = $_filePath . $file;
            $downloadPath = "{$_downloadPath}/{$file}";
        }

        switch ($format) {
            case FILETYPE_PDF:
                $report = ($context[DOC_MASTER]) ? new PDFDataMultiLineReport($context[DOC_MASTER], $logoPath, A4_LANDSCAPE) : new PDFDataMultiLineReport($filePath, $logoPath, A4_LANDSCAPE);
                $report->multiLine = true;
                $report->printRowLines = true;
                $report->printColumnLines = false;
                $report->printBorders = false;
                $report->enableFormatting = true;
                $report->cache = false;
                break;
            case FILETYPE_XLS:
                $report = new XLSDataReport($filePath, 'Company Report');
                $report->enableFormatting = true;
                break;
        }

        if ($format != FILETYPE_SCREEN) {
            $prepared = 'as at ' . date('d M Y');
            $header = new ReportHeader('Company Report', '', $prepared);
            $header->xPos = $report->hMargin;
            $header->yPos = $report->pageHeight - $report->vMargin;
            if ($format == FILETYPE_PDF) {
                $report->attachObject('header', $header);
            }

            $footer = new TraccFooter(null, $_report['reportName'], $report->pageSize);
            if ($format == FILETYPE_PDF) {
                $report->attachObject('footer', $footer);
            }

            $report->addColumn('companyID', 'Company Code', 55, 'left', '@');
            if ($format == FILETYPE_XLS) {
                $report->addColumn('supplierType', 'Supplier Type', 60, 'left', '@');
            }

            $report->addColumn('companyName', 'Company Name', 60, 'left', '@');
            $report->addColumn('address', 'Street', 60, 'left', '@');
            $report->addColumn('city', ucwords(strtolower($_SESSION['country_default']['suburb'])), 40, 'left', '@');
            if ($_SESSION['country_default']['display_state']) {
                $report->addColumn('state', 'State', 20, 'left', '@');
            }

            $report->addColumn('postCode', 'Post Code', 40, 'left');
            if (getDisplayBsbFromSession()) {
                $report->addColumn('bsbNumber', getBsbLabelFromSession(), 25, 'left', '@');
            }

            $report->addColumn('bankAccountNumber', 'Account Number', 60, 'left', '@');
            $report->addColumn('bankAccountName', 'Account Name', 50, 'left', '@');
            if (cdf_isAU()) {
                $report->addColumn('bpayBillerCode', 'Biller Code', 70, 'left', '@');
            }

            $report->addColumn('businessNumber', $_SESSION['country_default']['business_label'], 40, 'left', '@');
            $report->addColumn('taxCode', $_SESSION['country_default']['tax_label'] . ' Code', 35, 'left', '@');
            $report->addColumn('owner', 'Owner', 25, 'left');
            $report->addColumn('supplier', 'Supplier', 30, 'left');
            $report->addColumn('debtor', 'Debtor', 25, 'left');
            $report->addColumn('paymentMethod', 'Payment Method', 30, 'left');
            $report->addColumn('email', 'E-mail Address', 60, 'left');

            if ($format == FILETYPE_XLS) {
                $report->addColumn('cirrusfmYN', 'CirrusFM Enabled', 60, 'left');
            }

            $report->preparePage();
            $report->renderHeader();
        }

        $companyDetails = dbGetCompanyReport($companies, $view->items['includeOwner']);
        $details = array_map(function ($detail) {
            $items = [];

            foreach ($detail as $key => $value) {
                if ($key === 'businessNumber') {
                    $prefix = ($value && $value !== '') ? $_SESSION['country_default']['business_prefix'] : '';
                    $items[$key] = $prefix . $value;

                    continue;
                }

                if ($key === 'bsbNumber' && getDisplayBsbFromSession()) {
                    $items[$key] = formatWithDelimiter($value);

                    continue;
                }

                $items[$key] = $value;
            }

            return $items;
        }, $companyDetails);

        if ($format == FILETYPE_SCREEN) {
            $view->items['companyDetails'] = $details;
        } else {
            $report->renderData($details);
        }


        if ($format != FILETYPE_SCREEN) {
            $report->clean();
            $report->endPage();
            $report->close();
        }
    }

    if (! $context[DOC_MASTER]) {
        if ($context[IS_TASK]) {
            $email = new EmailTemplate('views/emails/basicEmail.html', SYSTEMURL);
            $attachment = [];
            $attachment[0]['file'] = REPORTPATH . '/' . $pdfDownloadLink;
            $attachment[0]['content_type'] = 'application/pdf';
            sendMail($_SESSION['email'], $_SESSION['first_name'] . ' ' . $_SESSION['last_name'], $email->toString(), 'Report', $attachment);
            logData('Emailed report to ' . $_SESSION['email']);
            $context[TASK_COMPLETE] = true;
            $view->items['statusMessage'] = 'Due to its complexity, this report has been scheduled and will be emailed to you on completion.';
        } else {
            $view->items['downloadPath'] = $downloadPath;
        }

        $view->render();
    }
}

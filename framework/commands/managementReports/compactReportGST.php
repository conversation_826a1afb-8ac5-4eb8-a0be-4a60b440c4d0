<?php

// SIMPLE OWNER STATEMENT REPORT
include_once 'Residential/residentialReportFunctions.php';
global $_fonts, $reportDescription;
global $clientDB;

$page++;
$totalGross_receipts = 0;
$totalGST_receipts = 0;
$totalNet_receipts = 0;
$totalNet_payments = 0;
$totalUCGST_receipts = 0;
$totalUCGross_receipts = 0;
$totalUCNet_receipts = 0;

$totalGST_payments = 0;
$totalGross_payments = 0;
$mychar = "\n";

$prop_result = getAgentOwnerCodes($propertyID);
$agent_code = $prop_result['agent_code'];
$owner_code = $prop_result['owner_code'];

$agentDetails = getCompAddress($agent_code);
$ownerDetails = getCompAddress($owner_code);

$agent_name = $agentDetails['company_name'];
$agent_street = $agentDetails['street'];
$agent_city = $agentDetails['city'];
$agent_state = $agentDetails['state'];
$agent_postcode = $agentDetails['postcide'];
$agent_streets = explode("\n", $agent_street);

$owner_name = $ownerDetails['company_name'];
$owner_street = $ownerDetails['street'];
$owner_city = $ownerDetails['city'];
$owner_state = $ownerDetails['state'];
$owner_postcode = $ownerDetails['postcode'];
$owner_streets = explode("\n", $owner_street);

$line = 0;
$pdf->begin_page_ext(595, 842, '');

if ($logo) {
    $logoObj = new ClientLogo($logoPath);
    $logoObj->preRender($pdf);
}

$bigLogoClient = BIGLOGOCLIENT;
$agentData = dbGetAgentDetails();
$agentDetailsNew = new agentDetails($propertyID, true, null, (in_array($clientDB, $bigLogoClient) ? 620 : 650));
$agentDetailsNew->bindAttributesFrom($agentData);
$agentDetailsNew->render($pdf);

$pdf->setFontExt($_fonts['Helvetica'], 10);

// ############# LOGO #############################
// include_once 'logo2.php';
// ###############################################


$pdf->show_xy('Owner: ', 25, 750 - $line);
$pdf->continue_text('Property: ');
$pdf->continue_text('Report for: ');

$pdf->show_xy("$client", 85, 750 - $line);
$pdf->continue_text("$propertyName [$propertyID]");
$pdf->continue_text("$periodDescription");

$line = $line + 10;
$line = $line + 10;
$pdf->setFontExt($_fonts['Helvetica'], 9);

$line = $line + 80;


$pdf->setFontExt($_fonts['Helvetica-Bold'], 10);
$pdf->showBoxed((($reportDescription)), 0, 760 - $line, 595, 20, 'center', '');
$line = $line + 15;
$pdf->showBoxed("Period From: $periodFrom To: $periodTo", 0, 760 - $line, 595, 22, 'center', '');
$line = $line + 30;


$pdf->setFontExt($_fonts['Helvetica-Bold'], 9);


// $pdf->showBoxed ('Income', 20, 760 - $line, 590, 20, 'left', '');
// $line = $line + 20;

$columns = [
    ['field' => 'receipt_desc', 'label' => 'Receipts:', 'width' => 20, 'length' => 450, 'align' => 'left', 'format' => 'text'],
    ['field' => 'invoice_no', 'label' => 'Invoice #', 'width' => 245, 'length' => 90, 'align' => 'left', 'format' => 'text'],
    ['field' => 'from', 'label' => 'From', 'width' => 295, 'length' => 90, 'align' => 'left', 'format' => 'text'],
    ['field' => 'to', 'label' => 'To', 'width' => 345, 'length' => 450, 'align' => 'left', 'format' => 'text'],
    ['field' => 'net', 'label' => 'Net ', 'width' => 350, 'length' => 90, 'align' => 'right', 'format' => 'num'],
    ['field' => 'gst', 'label' => $_SESSION['country_default']['tax_label'] . ' ', 'width' => 410, 'length' => 90, 'align' => 'right', 'format' => 'num'],
    ['field' => 'gross', 'label' => 'Gross ', 'width' => 480, 'length' => 90, 'align' => 'right', 'format' => 'num'],
];

$tenant_list = [];

if ($view->items['zeroReceipting']) {
    $propertyUnits = getCompactReportUnits($propertyID, $periodTo);

    foreach ($propertyUnits as $propertyUnit) {
        $tenant_list[$propertyUnit['propID'] . '~' . $propertyUnit['leaseID'] . '~' . $propertyUnit['unitID']] = [
            'floor' => $propertyUnit['floor'] ? $propertyUnit['floor'] : 'zzzzzzzzzz',
            'unitID' => $propertyUnit['unitID'] ? $propertyUnit['unitID'] : 'zzzzzzzzzz',
            'leaseID' => $propertyUnit['leaseID'],
            'name' => $propertyUnit['leaseName'],
            'description' => $propertyUnit['tenancyLocation'],
            'receipts' => [],
            'outstanding' => [],
            'charges' => [],
        ];
    }
}

// charges
$propCharges = getSimpleOwnerReportCharges($propertyID, $periodFrom, $periodTo);

$unitChargeFrequency[PERIOD_MONTH] = 'per month'; // 12
$unitChargeFrequency[PERIOD_YEAR] = 'per year'; // 1
$unitChargeFrequency[PERIOD_QUARTER] = 'per quarter'; // 4
$unitChargeFrequency[PERIOD_WEEK] = 'per week'; // 52
$unitChargeFrequency[PERIOD_FORTNIGHT] = 'per fortnight'; // 26
$unitChargeFrequency[PERIOD_4WEEK] = 'per 4 weeks'; // 13

$unitChargeDivisor[PERIOD_MONTH] = 12;
$unitChargeDivisor[PERIOD_YEAR] = 1;
$unitChargeDivisor[PERIOD_QUARTER] = 4;
$unitChargeDivisor[PERIOD_WEEK] = 52;
$unitChargeDivisor[PERIOD_FORTNIGHT] = 26;
$unitChargeDivisor[PERIOD_4WEEK] = 13;

foreach ($propCharges as $propCharge) {
    if ($propCharge['chargeTotal'] == 0) {
        continue;
    }

    if (! isset($tenant_list[$propCharge['propID'] . '~' . $propCharge['leaseID'] . '~' . $propCharge['unitID']])) {
        $tenant_list[$propCharge['propID'] . '~' . $propCharge['leaseID'] . '~' . $propCharge['unitID']] = [
            'floor' => $propCharge['floor'] ? $propCharge['floor'] : 'zzzzzzzzzz',
            'unitID' => $propCharge['unitID'] ? $propCharge['unitID'] : 'zzzzzzzzzz',
            'leaseID' => $propCharge['leaseID'],
            'name' => $propCharge['leaseName'],
            'description' => $propCharge['tenancyLocation'],
            'receipts' => [],
            'outstanding' => [],
            'deposits' => [],
            'charges' => [],
        ];
    }

    $tenant_list[$propCharge['propID'] . '~' . $propCharge['leaseID'] . '~' . $propCharge['unitID']]['charges'][] = [
        'forceGSTFree'  => $propCharge['forceGSTFree'],
        'label' => $propCharge['chargeDesc'],
        'value' => $unitChargeDivisor[$propCharge['chargeFrequency']] ? ($propCharge['chargeTotal'] / $unitChargeDivisor[$propCharge['chargeFrequency']]) : 0,
        'charge_frequency' => $unitChargeFrequency[$propCharge['chargeFrequency']],
        'paid_to' => (in_array($propCharge['chargeType'], ['R', 'V']) and $view->items['rentPaidTo']) ?
            getCompactReportPaidTo($propertyID, $propCharge['leaseID'], $propCharge['chargeAccount'], $propCharge['unitID']) : '',
    ];
}

// receipts
$propReceipts = getSimpleOwnerReportReceipts($propertyID, $periodFrom, $periodTo);

foreach ($propReceipts as $propReceipt) {
    if (! isset($tenant_list[$propReceipt['propID'] . '~' . $propReceipt['leaseID'] . '~' . $propReceipt['unitID']])) {
        $tenant_list[$propReceipt['propID'] . '~' . $propReceipt['leaseID'] . '~' . $propReceipt['unitID']] = [
            'floor' => $propReceipt['floor'] ? $propReceipt['floor'] : 'zzzzzzzzzz',
            'unitID' => $propReceipt['unitID'] ? $propReceipt['unitID'] : 'zzzzzzzzzz',
            'leaseID' => $propReceipt['leaseID'],
            'name' => $propReceipt['leaseName'],
            'description' => $propReceipt['tenancyLocation'],
            'receipts' => [],
            'outstanding' => [],
            'deposits' => [],
            'charges' => [],
        ];
    }

    $tenant_list[$propReceipt['propID'] . '~' . $propReceipt['leaseID'] . '~' . $propReceipt['unitID']]['receipts'][] = [
        'receipt_desc' => $propReceipt['description'],
        'from' => $propReceipt['fromDate'],
        'to' => $propReceipt['toDate'],
        'invoice_no' => $propReceipt['invoiceNo'] != '0' ? $propReceipt['invoiceNo'] : '',
        'net' => $propReceipt['net'],
        'gst' => $propReceipt['gst'],
        'gross' => $propReceipt['gross'],
    ];

}

// outstanding
$propOutstandings = getSimpleOwnerReportOutstanding($propertyID, $periodFrom, $periodTo);

foreach ($propOutstandings as $propOutstanding) {
    if (! isset($tenant_list[$propOutstanding['propID'] . '~' . $propOutstanding['leaseID'] . '~' . $propOutstanding['unitID']])) {
        $tenant_list[$propOutstanding['propID'] . '~' . $propOutstanding['leaseID'] . '~' . $propOutstanding['unitID']] = [
            'floor' => $propOutstanding['floor'] ? $propOutstanding['floor'] : 'zzzzzzzzzz',
            'unitID' => $propOutstanding['unitID'] ? $propOutstanding['unitID'] : 'zzzzzzzzzz',
            'leaseID' => $propOutstanding['leaseID'],
            'name' => $propOutstanding['leaseName'],
            'description' => $propOutstanding['tenancyLocation'],
            'receipts' => [],
            'outstanding' => [],
            'deposits' => [],
            'charges' => [],
        ];
    }

    if ($propOutstanding['outstanding_gross'] > 0) {
        $tenant_list[$propOutstanding['propID'] . '~' . $propOutstanding['leaseID'] . '~' . $propOutstanding['unitID']]['outstanding'][] = [
            'receipt_desc' => $propOutstanding['description'],
            'from' => $propOutstanding['fromDate'],
            'to' => $propOutstanding['toDate'],
            'invoice_no' => $propOutstanding['invoiceNo'] != '0' ? $propOutstanding['invoiceNo'] : '',
            'net' => $propOutstanding['outstanding_net'],
            'gst' => $propOutstanding['outstanding_tax'],
            'gross' => $propOutstanding['outstanding_gross'],
        ];
    } elseif ($propOutstanding['outstanding_gross'] < 0) {
        $tenant_list[$propOutstanding['propID'] . '~' . $propOutstanding['leaseID'] . '~' . $propOutstanding['unitID']]['deposits'][] = [
            'receipt_desc' => $propOutstanding['description'],
            'from' => $propOutstanding['fromDate'],
            'to' => $propOutstanding['toDate'],
            'invoice_no' => $propOutstanding['invoiceNo'] != '0' ? $propOutstanding['invoiceNo'] : '',
            'net' => $propOutstanding['outstanding_net'] * -1,
            'gst' => $propOutstanding['outstanding_tax'] * -1,
            'gross' => $propOutstanding['outstanding_gross'] * -1,
        ];
    }


}

$font_size = 8;
$overall = ['net' => 0, 'gst' => 0, 'gross' => 0];
$grand_overall_total = 0;

// sort tenant list
array_multisort(
    array_column($tenant_list, 'propID'),
    SORT_ASC,
    array_column($tenant_list, 'floor'),
    SORT_ASC,
    array_column($tenant_list, 'unitID'),
    SORT_ASC,
    array_column($tenant_list, 'leaseID'),
    SORT_ASC,
    $tenant_list
);

// function cmp($a, $b) {
//    if ($a['date'] == $b['date']) {
//        return 0;
//    }
//    return ($a['date'] < $b['date']) ? -1 : 1;
// }
//
// uasort($tenant_list, function($a, $b) {
//    return strcmp($a['floor'], $b['floor']);
// });

// pre_print_r($tenant_list);
$taxRates =  dbGetTaxRates();
$tax = mapParameters($taxRates, 'taxCode', 'taxRate');
foreach ($tax as $id => $value) {
    $tax[$id] = ($value / 100);
}

$taxProp = dbGetTaxStatus($propertyID);

foreach ($tenant_list as $tenant) {
    // ##########################################################################################################
    // HEADER

    if ($line >= 690) {
        $line = 740;
        extract(checkIfAddHeader($line, $_fonts, $page, $pdf, $logo, $logoPath, $periodFrom, $periodTo));
    }


    $pdf->setFontExt($_fonts['Helvetica-Bold'], $font_size);

    $pdf->showBoxed($tenant['description'] . ' - ' . $tenant['name'], 20, 760 - $line, 590, 20, 'left', '');

    if (strtolower($tenant['name']) == 'vacant') {
        $line = $line + 12;
    }

    // ##########################################################################################################

    if (! $tenant['leaseID']) {
        $line = $line + 18;

        continue;
    }

    $taxCode = $taxProp['taxCode'];
    $isResidential = dbGetResidential($propertyID, $tenant['leaseID']);
    if ($isResidential) {
        $taxCode = 'GSTFREE';
    }

    // CHARGES
    if (count($tenant['charges']) > 0) {
        $pdf->setFontExt($_fonts['Helvetica'], $font_size);
        $line = $line + 12;
        $ctr = 1;
        $adjustX = 50;
        $chargeTotal = 0;
        foreach ($tenant['charges'] as $ch) {

            if ($ch['forceGSTFree']) {
                $taxCode = 'GSTFREE';
            }

            if ($taxCode == 'TAXABLE') {
                // $ch['value'] = $ch['value'] + ($ch['value'] * ($tax['taxRate']/100));

                $gst = round($ch['value'] * $tax[$taxCode], 2);
                $ch['value'] = $ch['value'] + $gst;
            }

            if ($ctr == 1) {
                $pdf->setFontExt($_fonts['Helvetica-Bold'], $font_size);
                $pdf->showBoxed('Recurring Charges:', 20, 760 - $line, 590, 20, 'left', '');
            }
            $pdf->setFontExt($_fonts['Helvetica'], $font_size);
            $pdf->showBoxed($ch['label'], 60 + $adjustX, 760 - $line, 590, 20, 'left', '');
            $pdf->showBoxed(formattingWithSpace($ch['value']), 195 + $adjustX, 760 - $line, 90, 20, 'right', '');

            $pdf->showBoxed($ch['charge_frequency'], 295 + $adjustX, 760 - $line, 50, 20, 'left', '');

            if ($ch['paid_to']) {
                $pdf->showBoxed('Fully Paid To:', 355 + $adjustX, 760 - $line, 590, 20, 'left', '');
                $pdf->showBoxed($ch['paid_to'], 410 + $adjustX, 760 - $line, 590, 20, 'left', '');
            }
            $chargeTotal += round($ch['value'], 2);

            $line = $line + 10;
            $ctr++;

            extract(checkIfAddHeader($line, $_fonts, $page, $pdf, $logo, $logoPath, $periodFrom, $periodTo));
            $pdf->setFontExt($_fonts['Helvetica'], $font_size);
        }

        if (count($tenant['charges']) > 1) {
            $pdf->moveto(235 + $adjustX, 760 - $line + 18);
            $pdf->lineto(235 + $adjustX + 50, 760 - $line + 18);
            $pdf->stroke();
            $pdf->moveto(235 + $adjustX, 760 - $line + 18 - 14);
            $pdf->lineto(235 + $adjustX + 50, 760 - $line + 18 - 14);
            $pdf->stroke();
            $pdf->setFontExt($_fonts['Helvetica-Bold'], $font_size);
            $pdf->showBoxed(formattingWithSpace($chargeTotal), 195 + $adjustX, 760 - $line - 4, 90, 20, 'right', '');
            $pdf->setFontExt($_fonts['Helvetica'], $font_size);
            $line = $line + 10;
        }
    }
    // ##########################################################################################################
    // RECEIPTS
    $line = $line + 15;
    $pdf->setFontExt($_fonts['Helvetica-Bold'], $font_size);
    foreach ($columns as $col) {
        $pdf->showBoxed($col['label'], $col['width'], 760 - $line, $col['length'], 20, $col['align'], '');
    }
    $line = $line + 10;

    extract(checkIfAddHeader($line, $_fonts, $page, $pdf, $logo, $logoPath, $periodFrom, $periodTo));
    $pdf->setFontExt($_fonts['Helvetica'], $font_size);

    if (count($tenant['receipts']) > 0) {
        $totals = ['net' => 0, 'gst' => 0, 'gross' => 0];
        foreach ($tenant['receipts'] as $rec) {
            foreach ($columns as $col) {
                $value = $rec[$col['field']];
                if ($col['format'] == 'num') {
                    $value = formattingWithSpace($rec[$col['field']]);
                }
                $pdf->showBoxed($value, $col['width'], 760 - $line, $col['length'], 20, $col['align'], '');
                if (isset($totals[$col['field']])) {
                    $totals[$col['field']] += $rec[$col['field']];
                }
                if (isset($overall[$col['field']])) {
                    $overall[$col['field']] += $rec[$col['field']];
                }
            }
            $line = $line + 10;

            extract(checkIfAddHeader($line, $_fonts, $page, $pdf, $logo, $logoPath, $periodFrom, $periodTo));
            $pdf->setFontExt($_fonts['Helvetica'], $font_size);
        }

        //			if (count($tenant['receipts']) > 1) {
        $pdf->setFontExt($_fonts['Helvetica-Bold'], $font_size);
        $line = $line + 5;
        foreach ($columns as $col) {
            if (isset($totals[$col['field']])) {
                $pdf->showBoxed(formattingWithSpace($totals[$col['field']]), $col['width'], 760 - $line, $col['length'], 20, $col['align'], '');
            } elseif ($col['field'] == 'to') {
                $pdf->showBoxed('Total Receipts', $col['width'] - 20, 760 - $line, $col['length'], 20, $col['align'], '');
            }
        }
        $pdf->moveto(400, 781 - $line);
        $pdf->lineto(570, 781 - $line);
        $pdf->stroke();
        //                $pdf->moveto(400, 781-$line-13);
        //                $pdf->lineto(570, 781-$line-13);
        //                $pdf->stroke();
        //            }
    } else {
        $pdf->showBoxed('None', 20, 760 - $line, 190, 20, 'left', '');
        $line = $line + 15;
    }
    // ##########################################################################################################
    // ARREARS
    $line = $line + 7;
    $pdf->setFontExt($_fonts['Helvetica-Bold'], $font_size);
    $ctr = 1;
    foreach ($columns as $col) {
        $label = ' ';
        if ($ctr == 1) {
            $label = 'Unpaid Balance:';
        }
        $pdf->showBoxed($label, $col['width'], 760 - $line, $col['length'], 20, $col['align'], '');
        $ctr++;
    }
    $line = $line + 10;
    extract(checkIfAddHeader($line, $_fonts, $page, $pdf, $logo, $logoPath, $periodFrom, $periodTo));
    $pdf->setFontExt($_fonts['Helvetica'], $font_size);
    if (count($tenant['outstanding']) > 0) {
        $totals = ['net' => 0, 'gst' => 0, 'gross' => 0];
        foreach ($tenant['outstanding'] as $out) {
            foreach ($columns as $col) {
                $value = $out[$col['field']];
                if ($col['format'] == 'num') {
                    $value = formattingWithSpace($out[$col['field']]);
                }
                $pdf->showBoxed($value, $col['width'], 760 - $line, $col['length'], 20, $col['align'], '');
                if (isset($totals[$col['field']])) {
                    $totals[$col['field']] += $out[$col['field']];
                }
                //					if(isset($overall[$col['field']])){
                //						$overall[$col['field']] += $out[$col['field']];
                //					}
            }
            $line = $line + 10;

            extract(checkIfAddHeader($line, $_fonts, $page, $pdf, $logo, $logoPath, $periodFrom, $periodTo));
            $pdf->setFontExt($_fonts['Helvetica'], $font_size);
        }

        //			if (count($tenant['outstanding']) > 1)
        //            {
        $pdf->setFontExt($_fonts['Helvetica-Bold'], $font_size);
        $line = $line + 5;
        foreach ($columns as $col) {
            if (isset($totals[$col['field']])) {
                $pdf->showBoxed(formattingWithSpace($totals[$col['field']]), $col['width'], 760 - $line, $col['length'], 20, $col['align'], '');
            } elseif ($col['field'] == 'to') {
                $pdf->showBoxed('Total Unpaid', $col['width'] - 20, 760 - $line, $col['length'], 20, $col['align'], '');
            }
        }
        $pdf->moveto(400, 781 - $line);
        $pdf->lineto(570, 781 - $line);
        $pdf->stroke();
        //                $pdf->moveto(400, 781-$line-13);
        //                $pdf->lineto(570, 781-$line-13);
        //                $pdf->stroke();
        //            }
    } else {
        $pdf->showBoxed('None', 20, 760 - $line, 190, 20, 'left', '');
        $line = $line + 15;
    }
    $line = $line + 20;
    // ##########################################################################################################
    // DEPOSITS
    if (count($tenant['deposits']) > 0) {
        $line = $line + 7;
        $pdf->setFontExt($_fonts['Helvetica-Bold'], $font_size);
        $ctr = 1;
        foreach ($columns as $col) {
            $label = ' ';
            if ($ctr == 1) {
                $label = 'Deposits/Pre Payments:';
            }
            $pdf->showBoxed($label, $col['width'], 760 - $line, $col['length'], 20, $col['align'], '');
            $ctr++;
        }
        $line = $line + 10;
        extract(checkIfAddHeader($line, $_fonts, $page, $pdf, $logo, $logoPath, $periodFrom, $periodTo));
        $pdf->setFontExt($_fonts['Helvetica'], $font_size);
        //        if (count($tenant['deposits']) > 0) {
        //            $totals = ['net' => 0, 'gst' => 0, 'gross' => 0];
        foreach ($tenant['deposits'] as $out) {
            foreach ($columns as $col) {
                $value = $out[$col['field']];
                if ($col['format'] == 'num') {
                    $value = formattingWithSpace($out[$col['field']]);
                }
                $pdf->showBoxed($value, $col['width'], 760 - $line, $col['length'], 20, $col['align'], '');
                if (isset($totals[$col['field']])) {
                    $totals[$col['field']] += $out[$col['field']];
                }
                //					if(isset($overall[$col['field']])){
                //						$overall[$col['field']] += $out[$col['field']];
                //					}
            }
            $line = $line + 10;

            extract(checkIfAddHeader($line, $_fonts, $page, $pdf, $logo, $logoPath, $periodFrom, $periodTo));
            $pdf->setFontExt($_fonts['Helvetica'], $font_size);
        }
        //			$pdf->setFontExt ($_fonts['Helvetica-Bold'], $font_size);
        //			$line = $line + 5;
        //			foreach ($columns as $col) {
        //				if(isset($totals[$col['field']])){
        //					$pdf->showBoxed(formattingWithSpace($totals[$col['field']]), $col['width'], 760 - $line, $col['length'], 20, $col['align'], '');
        //				}
        //				else if($col['field'] == 'to'){
        //					$pdf->showBoxed('Tenant Balance', $col['width'], 760 - $line, $col['length'], 20, $col['align'], '');
        //				}
        //			}
        //			$pdf->moveto(400, 781-$line);
        //			$pdf->lineto(570, 781-$line);
        //			$pdf->stroke();
        //        } else {
        //            $pdf->showBoxed('None', 20, 760 - $line, 190, 20, 'left', '');
        //            $line = $line + 15;
        //        }
        $line = $line + 20;
    }
    // ##########################################################################################################
    extract(checkIfAddHeader($line, $_fonts, $page, $pdf, $logo, $logoPath, $periodFrom, $periodTo));
    $pdf->setFontExt($_fonts['Helvetica'], $font_size);
}

$line = $line + 5;

$pdf->moveto(20, 781 - $line);
$pdf->lineto(570, 781 - $line);
$pdf->stroke();

$line = $line + 15;
$pdf->setFontExt($_fonts['Helvetica-Bold'], $font_size);
$pdf->showBoxed('Monies Received; Period From: ' . $periodFrom . ' To: ' . $periodTo, 20, 760 - $line, 250, 20, 'left', '');
foreach ($columns as $col) {
    if (isset($overall[$col['field']])) {
        $pdf->showBoxed(formattingWithSpace($overall[$col['field']]), $col['width'], 760 - $line, $col['length'], 20, $col['align'], '');
    } elseif ($col['field'] == 'to') {
        $pdf->showBoxed('Total Receipts for property', $col['width'] - 100, 760 - $line, $col['length'] + 100, 20, 'right', '');
    }
}
$grossReceipts = $overall['gross'];
$pdf->moveto(400, 781 - $line);
$pdf->lineto(570, 781 - $line);
$pdf->stroke();
$pdf->moveto(400, 781 - $line - 13);
$pdf->lineto(570, 781 - $line - 13);
$pdf->stroke();

$line = $line + 40;
extract(checkIfAddHeader($line, $_fonts, $page, $pdf, $logo, $logoPath, $periodFrom, $periodTo));
// ##################################################################################################################
// ###################################################################################################################
// ###################################################################################################################
// ############################################### PAYMENTS ##########################################################
// ############################################### PAYMENTS ##########################################################
// ############################################### PAYMENTS ##########################################################
// ###################################################################################################################

$pdf->setFontExt($_fonts['Helvetica-Bold'], $font_size);
$pdf->showBoxed('Payments:', 20, 760 - $line, 590, 20, 'left', '');
$line = $line + 10;

// $pdf->setFontExt ($_fonts['Helvetica-Bold'], $font_size);
// $pdf->showBoxed ('Supplier', 20, 760 - $line, 590, 20, 'left', '');
// $pdf->showBoxed ("Description", 140, 760-$line, 590, 20, 'left', '');
// $pdf->showBoxed ("Invoice", 240, 760-$line, 75, 20, 'left', '');
// $pdf->showBoxed ("From", 320, 760-$line, 90, 20, 'left', '');
// $pdf->showBoxed ("To", 370, 760-$line, 90, 20, 'left', '');
// $pdf->showBoxed ("Net", 420, 760-$line, 50, 20, 'right', '');
// $pdf->showBoxed ("GST", 475, 760-$line, 40, 20, 'right', '');
// $pdf->showBoxed ("Total", 520, 760-$line, 50, 20, 'right', '');
//
$columns = [
    'supplier' => ['label' => 'Supplier', 'width' => 20, 'length' => 190, 'align' => 'left', 'format' => 'text'],
    'description' => ['label' => 'Description', 'width' => 110, 'length' => 190, 'align' => 'left', 'format' => 'text'],
    'invoice_no' => ['label' => 'Invoice #', 'width' => 245, 'length' => 90, 'align' => 'left', 'format' => 'text'],
    'from' => ['label' => 'From', 'width' => 295, 'length' => 90, 'align' => 'left', 'format' => 'text'],
    'to' => ['label' => 'To', 'width' => 345, 'length' => 450, 'align' => 'left', 'format' => 'text'],
    'net' => ['label' => 'Net ', 'width' => 350, 'length' => 90, 'align' => 'right', 'format' => 'num'],
    'gst' => ['label' => $_SESSION['country_default']['tax_label'] . ' ', 'width' => 410, 'length' => 90, 'align' => 'right', 'format' => 'num'],
    'gross' => ['label' => 'Gross ', 'width' => 480, 'length' => 90, 'align' => 'right', 'format' => 'num'],
];
$pdf->setFontExt($_fonts['Helvetica-Bold'], $font_size);
foreach ($columns as $col) {
    $pdf->showBoxed($col['label'], $col['width'], 760 - $line, $col['length'], 20, $col['align'], '');
}
$line = $line + 15;
extract(checkIfAddHeader($line, $_fonts, $page, $pdf, $logo, $logoPath, $periodFrom, $periodTo));
$pdf->setFontExt($_fonts['Helvetica'], $font_size);
//

$batchnr_result = getCompactReportPayments($propertyID, $periodFrom, $periodTo);
$batchByAccountGroup = [];

foreach ($batchnr_result as $k => $v) {
    $accountGroup = ucwords(strtolower($v['accountGroup']));
    $batchByAccountGroup[$accountGroup][$k] = $v;
}

$i = 0;
$totalPaymentsTax = 0;

foreach ($batchByAccountGroup as $k => $v) {
    $line = $line - 10;
    $pdf->setFontExt($_fonts['Helvetica-Bold'], $font_size);
    $pdf->showBoxed($k, 20, 760 - $line, 590, 20, 'left', '');
    $line = $line + 20;

    foreach ($v as $thisLine) {
        $batch = $thisLine['pmxc_t_batch'];
        $linen = $thisLine['pmxc_t_line'];
        $alloc_dt = $thisLine['paymentDate'];

        $inv_from_dt = $thisLine['from_date'];
        $inv_to_dt = $thisLine['to_date'];

        $alloc_amt = $thisLine['alloc_total'];
        $alloc_tax = $thisLine['alloc_tax'];

        $i++;

        $tdescription = $thisLine['description'];

        $date_from = $inv_from_dt;
        $date_to = $inv_to_dt;

        $aptr_tax_amt = $alloc_tax;

        $payee_name = $thisLine['creditor_name'];
        $payee_name = limit_name($payee_name, 32);

        $invoice = $thisLine['invoice_no'];
        $net_amt = $alloc_amt * (-1) - $aptr_tax_amt;

        $net_amt_display = formattingWithSpace($net_amt);
        $aptr_tax_amt_display = formattingWithSpace($aptr_tax_amt);
        $gross_amt = $alloc_amt * (-1);
        $gross_amt_display = formattingWithSpace($gross_amt);

        $totalPaymentsTax += $aptr_tax_amt;

        $totalNet_payments = $totalNet_payments + $net_amt;
        $totalGST_payments = $totalGST_payments + $aptr_tax_amt;
        $totalGross_payments = $totalGross_payments + $gross_amt;

        $invoice = substr($invoice, 0, 10);
        $tdescription = limit_name(character_special($tdescription), 38); // 38

        $pdf->setFontExt($_fonts['Helvetica'], $font_size);
        $pdf->showBoxed(ellipsitize($payee_name, 19), $columns['supplier']['width'], 760 - $line, $columns['supplier']['length'], 30, $columns['supplier']['align'], '');
        $pdf->showBoxed(ellipsitize($tdescription, 32), $columns['description']['width'], 760 - $line, $columns['description']['length'], 30, $columns['description']['align'], ''); // 170
        $pdf->showBoxed("$invoice", $columns['invoice_no']['width'], 760 - $line, $columns['invoice_no']['length'], 30, $columns['invoice_no']['align'], ''); // 90
        $pdf->showBoxed("$date_from", $columns['from']['width'], 760 - $line, $columns['from']['length'], 30, $columns['from']['align'], ''); // 90
        $pdf->showBoxed("$date_to", $columns['to']['width'], 760 - $line, $columns['to']['length'], 30, $columns['to']['align'], '');
        $pdf->showBoxed("$net_amt_display", $columns['net']['width'], 760 - $line, $columns['net']['length'], 30, $columns['net']['align'], '');
        $pdf->showBoxed("$aptr_tax_amt_display", $columns['gst']['width'], 760 - $line, $columns['gst']['length'], 30, $columns['gst']['align'], '');
        $pdf->showBoxed("$gross_amt_display", $columns['gross']['width'], 760 - $line, $columns['gross']['length'], 30, $columns['gross']['align'], '');


        $line = $line + 10;
        extract(checkIfAddHeader($line, $_fonts, $page, $pdf, $logo, $logoPath, $periodFrom, $periodTo));
    }
}

$line = $line + 5;
extract(checkIfAddHeader($line, $_fonts, $page, $pdf, $logo, $logoPath, $periodFrom, $periodTo));


// $pdf->moveto (420, 791-$line);
// $pdf->lineto (470, 791-$line);
// $pdf->stroke ();

// $pdf->moveto (475, 791-$line);
// $pdf->lineto (515, 791-$line);
// $pdf->stroke ();

// $pdf->moveto (520, 791-$line);
// $pdf->lineto (570, 791-$line);
// $pdf->stroke ();

$pdf->moveto(400, 791 - $line);
$pdf->lineto(570, 791 - $line);
$pdf->stroke();
$pdf->moveto(400, 791 - $line - 13);
$pdf->lineto(570, 791 - $line - 13);
$pdf->stroke();

$totalNet_payments_display = formattingWithSpace($totalNet_payments);
$totalGST_payments_display = formattingWithSpace($totalGST_payments);
$totalGross_payments_display = formattingWithSpace($totalGross_payments);

$pdf->setFontExt($_fonts['Helvetica-Bold'], $font_size);
$pdf->showBoxed('Payments; Period From: ' . $periodFrom . ' To: ' . $periodTo, $columns['supplier']['width'], 760 - $line, 250, 30, $columns['supplier']['align'], '');
$pdf->showBoxed('', $columns['description']['width'], 760 - $line, $columns['description']['length'], 30, $columns['description']['align'], ''); // 170
$pdf->showBoxed('', $columns['invoice_no']['width'], 760 - $line, $columns['invoice_no']['length'], 30, $columns['invoice_no']['align'], ''); // 90
$pdf->showBoxed('', $columns['from']['width'], 760 - $line, $columns['from']['length'], 30, $columns['from']['align'], ''); // 90
$pdf->showBoxed('Total Payments for property', $columns['to']['width'] - 100, 760 - $line, $columns['to']['length'] + 100, 30, 'right', '');
$pdf->showBoxed("$totalNet_payments_display", $columns['net']['width'], 760 - $line, $columns['net']['length'], 30, $columns['net']['align'], '');
$pdf->showBoxed("$totalGST_payments_display", $columns['gst']['width'], 760 - $line, $columns['gst']['length'], 30, $columns['gst']['align'], '');
$pdf->showBoxed("$totalGross_payments_display", $columns['gross']['width'], 760 - $line, $columns['gross']['length'], 30, $columns['gross']['align'], '');

// $pdf->showBoxed ("$totalNet_payments_display",  $columns['to']['width'], 760-$line, $columns['to']['length'], 31, $columns['to']['align'], "");
// $pdf->showBoxed ("$totalGST_payments_display",  $columns['net']['width'], 760-$line, $columns['net']['length'], 31, $columns['net']['align'], "");
// $pdf->showBoxed ("$totalGross_payments_display",  $columns['gross']['width'], 760-$line, $columns['gross']['length'], 31, $columns['gross']['align'], "");

$line = $line + 12;

// $pdf->moveto (420, 791 - $line);
// $pdf->lineto (470, 791 - $line);
// $pdf->stroke ();

// $pdf->moveto (475, 791 - $line);
// $pdf->lineto (515, 791 - $line);
// $pdf->stroke ();

// $pdf->moveto (520, 791 - $line);
// $pdf->lineto (570, 791 - $line);
// $pdf->stroke ();

$line = $line + 10;

$cashmov = $grossReceipts - $totalGross_payments;
$cashmov_display = formattingWithSpace($cashmov);
$pdf->setFontExt($_fonts['Helvetica-Bold'], $font_size);
$pdf->showBoxed('Net Balance; Monies Received - Payments:', 20, 760 - $line, 590, 30, 'left', '');
$pdf->setFontExt($_fonts['Helvetica-Bold'], $font_size);
$pdf->showBoxed("$cashmov_display", 470, 760 - $line, 100, 30, 'right', '');

$pdf->moveto(503, 791 - $line);
$pdf->lineto(570, 791 - $line);
$pdf->stroke();
$pdf->moveto(503, 791 - $line - 13);
$pdf->lineto(570, 791 - $line - 13);
$pdf->stroke();

$line = $line + 15;

$pdf->moveto(20, 781 - $line);
$pdf->lineto(570, 781 - $line);
$pdf->stroke();

$line = $line + 15;

extract(checkIfAddHeader($line, $_fonts, $page, $pdf, $logo, $logoPath, $periodFrom, $periodTo));

// OPENING BALANCE
// ####################################################################################
// ################## CASH RECONCILIATION #############################################
// ####################################################################################

$receipts = total_cash_receipts($propertyID, $startFinancialYear, $previousPeriodTo);
$payments = total_cash_payments($propertyID, $startFinancialYear, $previousPeriodTo);

$receiptsA = total_cash_receipts($propertyID, STARTDATE, $financialPeriodToPY);
$paymentsA = total_cash_payments($propertyID, STARTDATE, $financialPeriodToPY);


$diffY = $receipts - $payments;
$diffA = $receiptsA - $paymentsA;
$diff = $diffA + $diffY;

$diff_display = formattingWithSpace($diff);


$openingbalance = $diff;
$openingbalance_display = formattingWithSpace($openingbalance);
$pdf->setFontExt($_fonts['Helvetica-Bold'], $font_size);
$line = $line + 15;
$pdf->showBoxed('Opening Cash Balance: ' . $periodFrom, 20, 760 - $line, 590, 30, 'left', '');
$pdf->setFontExt($_fonts['Helvetica-Bold'], $font_size);
$pdf->showBoxed("$openingbalance_display", 470, 760 - $line, 100, 30, 'right', '');
$line = $line + 5;

$subtotal_balance = $cashmov + $openingbalance;

// OWNER REMITTANCES

$total_pmts_duringM = getPaymentsToOwners($propertyID, $periodFrom, $periodTo); // trim($query_result["amount"]);
$creditorResult = getCreditorDetails($propertyID, $periodFrom, $periodTo);    // $query_result = mssql_query($query);

foreach ($creditorResult as $key => $thisRow) {

    $ind_pmts_duringY_display = formattingWithSpace($thisRow['pmxc_alloc_amt']);
    $ind_pmxc_s_creditor_display = $thisRow['pmxc_s_creditor'];
    $cred_name = $thisRow['pmco_name'];
    $cred_name = limit_name($cred_name, 35);
    $ind_pmxc_alloc_dt = $thisRow['pmxc_alloc_dt'];
    $tdescription = $thisRow['description'];

    $pdf->setFontExt($_fonts['Helvetica'], $font_size);
    $pdf->showBoxed("$cred_name", 20, 760 - $line, 210, 20, 'left', '');
    $pdf->showBoxed("$tdescription", 240, 760 - $line, 150, 20, 'left', '');
    $pdf->showBoxed("$ind_pmxc_alloc_dt", 400, 760 - $line, 50, 20, 'right', '');
    $pdf->setFontExt($_fonts['Helvetica-Bold'], $font_size);
    $pdf->showBoxed("$ind_pmts_duringY_display", 480, 760 - $line, 90, 20, 'right', '');
    $line = $line + 10;
    extract(checkIfAddHeader($line, $_fonts, $page, $pdf, $logo, $logoPath, $periodFrom, $periodTo));
} // end foreach

$line = $line + 5;
$ownerpayments = -($total_pmts_duringM);
$ownerpayments_display = formattingWithSpace($ownerpayments);
// $pdf->setlinewidth (0.5);

$pdf->moveto(503, 783 - $line);
$pdf->lineto(570, 783 - $line);
$pdf->stroke();
$pdf->moveto(503, 783 - $line - 13);
$pdf->lineto(570, 783 - $line - 13);
$pdf->stroke();

// CLOSING BALANCE
$closing_cashbalance = $subtotal_balance - $ownerpayments;

$closing_cashbalance_display = formattingWithSpace($closing_cashbalance);
$pdf->setFontExt($_fonts['Helvetica-Bold'], $font_size);
$pdf->showBoxed('Closing Cash Balance: ' . $periodTo, 20, 760 - $line, 590, 20, 'left', '');
$pdf->setFontExt($_fonts['Helvetica-Bold'], $font_size);
$pdf->showBoxed("$closing_cashbalance_display", 470, 760 - $line, 100, 22, 'right', '');

// echo "LINE 730 --- 760- $line<br />";

$line = $line + 10;


$pdf->setFontExt($_fonts['Helvetica'], 7);

$pdf->setFontExt($_fonts['Helvetica'], 7);
$pdf->showBoxed("Page $page", 495, 0, 75, 20, 'right', '');

$traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'compactReport', A4_PORTRAIT);
$traccFooter->prerender($pdf);

$line = $line + 10;

// $pdf->moveto(520, 791-$line);
// $pdf->lineto(570, 791-$line);
// $pdf->stroke();
$calendarTo = dbGetPeriod($propertyID, $periodTo);
$unpaidInvoices =  dbGetUnpaidInvoicesToPeriod($propertyID, $calendarTo['period'], $calendarTo['year'], $periodTo);
$outstanding = [];
foreach ($unpaidInvoices as $invoice) {
    $offset = bcadd($invoice['totalAllocated'], $invoice['totalReallocated'], 2);
    $amount = bcsub($invoice['transactionAmount'], $offset, 2);

    $offsetTax = bcadd($invoice['totalTaxAllocated'], $invoice['totalTaxReallocated'], 2);
    $amountTax = bcsub($invoice['transactionTax'], $offsetTax, 2);

    $invoice['transactionAmount'] = $amount;
    $invoice['transactionTax'] = $amountTax;
    $invoice['transactionNet'] = $amount - $amountTax;
    if ($amount != 0) {
        $outstanding[] = $invoice;
        $outstandingSet[] = $invoice;
        $outstandingTotal += $amount;
    }
}




$line = 740;
extract(checkIfAddHeader($line, $_fonts, $page, $pdf, $logo, $logoPath, $periodFrom, $periodTo));
// ############################################### UNPAID ##########################################################

$pdf->setFontExt($_fonts['Helvetica-Bold'], $font_size);
$pdf->showBoxed('Unpaid Creditors as at ' . $periodTo, 20, 760 - $line, 590, 20, 'left', '');
$line = $line + 10;


$columns = [
    'supplier' => ['label' => 'Supplier', 'width' => 20, 'length' => 190, 'align' => 'left', 'format' => 'text'],
    'description' => ['label' => 'Description', 'width' => 110, 'length' => 190, 'align' => 'left', 'format' => 'text'],
    'invoice_no' => ['label' => 'Invoice #', 'width' => 245, 'length' => 90, 'align' => 'left', 'format' => 'text'],
    'from' => ['label' => 'From', 'width' => 295, 'length' => 90, 'align' => 'left', 'format' => 'text'],
    'to' => ['label' => 'To', 'width' => 345, 'length' => 450, 'align' => 'left', 'format' => 'text'],
    'net' => ['label' => 'Net ', 'width' => 350, 'length' => 90, 'align' => 'right', 'format' => 'num'],
    'gst' => ['label' => $_SESSION['country_default']['tax_label'] . ' ', 'width' => 410, 'length' => 90, 'align' => 'right', 'format' => 'num'],
    'gross' => ['label' => 'Gross ', 'width' => 480, 'length' => 90, 'align' => 'right', 'format' => 'num'],
];
$pdf->setFontExt($_fonts['Helvetica-Bold'], $font_size);
foreach ($columns as $col) {
    $pdf->showBoxed($col['label'], $col['width'], 760 - $line, $col['length'], 20, $col['align'], '');
}
$line = $line + 15;
extract(checkIfAddHeader($line, $_fonts, $page, $pdf, $logo, $logoPath, $periodFrom, $periodTo));
$pdf->setFontExt($_fonts['Helvetica'], $font_size);

$totalGross_payments = 0;
$totalNet_payments = 0;
$totalGST_payments = 0;
$line = $line + 10;
foreach ($outstanding as $k => $v) {
    $invoice = substr($v['reference'], 0, 10);
    $tdescription = limit_name(character_special($v['description']), 38); // 38

    $pdf->setFontExt($_fonts['Helvetica'], $font_size);
    $pdf->showBoxed(ellipsitize($v['creditorName'], 19), $columns['supplier']['width'], 760 - $line, $columns['supplier']['length'], 30, $columns['supplier']['align'], '');
    $pdf->showBoxed(ellipsitize($tdescription, 32), $columns['description']['width'], 760 - $line, $columns['description']['length'], 30, $columns['description']['align'], ''); // 170
    $pdf->showBoxed("$invoice", $columns['invoice_no']['width'], 760 - $line, $columns['invoice_no']['length'], 30, $columns['invoice_no']['align'], ''); // 90
    $pdf->showBoxed($v['fromDate'], $columns['from']['width'], 760 - $line, $columns['from']['length'], 30, $columns['from']['align'], ''); // 90
    $pdf->showBoxed($v['toDate'], $columns['to']['width'], 760 - $line, $columns['to']['length'], 30, $columns['to']['align'], '');
    $pdf->showBoxed(formattingWithSpace($v['transactionNet']), $columns['net']['width'], 760 - $line, $columns['net']['length'], 30, $columns['net']['align'], '');
    $pdf->showBoxed(formattingWithSpace($v['transactionTax']), $columns['gst']['width'], 760 - $line, $columns['gst']['length'], 30, $columns['gst']['align'], '');
    $pdf->showBoxed(formattingWithSpace($v['transactionAmount']), $columns['gross']['width'], 760 - $line, $columns['gross']['length'], 30, $columns['gross']['align'], '');

    $totalGross_payments += $v['transactionAmount'];
    $totalNet_payments += $v['transactionNet'];
    $totalGST_payments += $v['transactionTax'];


    $line = $line + 10;
    extract(checkIfAddHeader($line, $_fonts, $page, $pdf, $logo, $logoPath, $periodFrom, $periodTo));
}

$line = $line + 5;
extract(checkIfAddHeader($line, $_fonts, $page, $pdf, $logo, $logoPath, $periodFrom, $periodTo));



$pdf->moveto(400, 791 - $line);
$pdf->lineto(570, 791 - $line);
$pdf->stroke();
$pdf->moveto(400, 791 - $line - 13);
$pdf->lineto(570, 791 - $line - 13);
$pdf->stroke();

$totalNet_payments_display = formattingWithSpace($totalNet_payments);
$totalGST_payments_display = formattingWithSpace($totalGST_payments);
$totalGross_payments_display = formattingWithSpace($totalGross_payments);

$pdf->setFontExt($_fonts['Helvetica-Bold'], $font_size);
$pdf->showBoxed("$totalNet_payments_display", $columns['net']['width'], 760 - $line, $columns['net']['length'], 30, $columns['net']['align'], '');
$pdf->showBoxed("$totalGST_payments_display", $columns['gst']['width'], 760 - $line, $columns['gst']['length'], 30, $columns['gst']['align'], '');
$pdf->showBoxed("$totalGross_payments_display", $columns['gross']['width'], 760 - $line, $columns['gross']['length'], 30, $columns['gross']['align'], '');

$line = $line + 12;

$pdf->end_page_ext('');

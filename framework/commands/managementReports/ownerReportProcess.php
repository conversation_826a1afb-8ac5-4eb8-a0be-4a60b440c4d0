<?php

set_time_limit(0);
ini_set('MAX_EXECUTION_TIME', '-1');

if (! defined('DS_ALL')) {
    define('DS_ALL', 1);
}
if (! defined('DS_PORTFOLIO')) {
    define('DS_PORTFOLIO', 2);
}
if (! defined('DS_GROUP')) {
    define('DS_GROUP', 3);
}
if (! defined('DS_TYPE')) {
    define('DS_TYPE', 4);
}
if (! defined('DS_REPORT')) {
    define('DS_REPORT', 5);
}
if (! defined('DS_OWNER')) {
    define('DS_OWNER', 6);
}
if (! defined('DS_PAYDATE')) {
    define('DS_PAYDATE', 7);
}

function dbGetCompanyContactSalutation($ownerID)
{
    global $clientDB, $dbh;
    $params = [];
    $dbh->selectDatabase($clientDB);
    $sql = '
		  SELECT pmcj_phone_no , pmct_name
          FROM pmct_c_contact
          left join pmcj_c_phone ON pmct_c_contact.pmct_serial = pmcj_c_phone.pmcj_c_serial
          where pmct_company = ' . addSQLParam($params, $ownerID) . '
          and pmcj_company = ' . addSQLParam($params, $ownerID) . "
          and pmct_primary = 1
          and pmcj_ph_code = 'SALU'
          ";

    return $dbh->executeSingle($sql, $params);
}

function dateSplit($date)
{
    $year = substr($date, 0, 4);
    $month = substr($date, 4, 2);
    $day = substr($date, 6, 2);

    return $day . '/' . $month . '/' . $year;
}


function fetchAgentInvoices($type, $fromDate = null, $toDate = null, $propertyID = null, $leaseID = null, $invoiceNumber = null)
{
    global $pathPrefix, $clientDirectory;

    $path = "{$pathPrefix}{$clientDirectory}/pdf/AgentTaxInvoice/";

    if (! file_exists($path)) {
        mkdir($path, FILE_PERMISSION, true);
    }

    if ($handle = opendir($path)) {
        while (false !== ($file = readdir($handle))) {
            $fullPath = $path . $file;

            $pathInfo = @pathinfo($fullPath);

            $info['fileName'] = $file;
            $info['lastModified'] = date('d/m/Y', filemtime($fullPath));
            $info['created'] = date('d/m/Y', filectime($fullPath));
            $info['extension'] = $pathInfo['extension'];
            $info['downloadPath'] = "../reports/{$clientDirectory}/pdf/AgentTaxInvoice/{$file}";
            $info['file'] = substr($info['fileName'], 0, strrpos($info['fileName'], '.'));
            switch ($type) {
                case 1: // TINV_PDF_SINGLE
                    $parts = explode('_', $info['file']);

                    $ok = (count($parts ?? []) > 5);
                    // echo count($parts);
                    if ($ok) {
                        // $info['invoiceNumber'] = $parts[3];
                        $info['invoiceDate'] = dateSplit($parts[3]);
                        $info['propertyID'] = $parts[4];
                        // $info['leaseID'] = $parts[5];

                        if (($fromDate) && ($toDate)) {
                            $ok = ($ok) && (toDateStamp($info['invoiceDate']) >= toDateStamp($fromDate)) && (toDateStamp($info['invoiceDate']) <= toDateStamp($toDate));
                        }
                        if ($invoiceNumber) {
                            $ok = ($ok) && ($invoiceNumber == $info['invoiceNumber']);
                        }
                        if ($propertyID) {
                            $ok = ($ok) && ($propertyID == $info['propertyID']);
                        }
                        // if ($leaseID) $ok = ($ok) && ($leaseID == $info['leaseID']);
                    }
                    break;
                case 2: // TINV_PDF_MULTIPLE
                    $parts = explode('_', $info['file']);
                    $ok = (count($parts ?? []) == 4);
                    if ($ok) {
                        $info['invoiceDate'] = dateSplit($parts[2]);
                        $info['time'] = date('d/m/Y', $parts[3]);

                        if (($fromDate) && ($toDate)) {
                            $ok = ($ok) && (toDateStamp($info['invoiceDate']) >= toDateStamp($fromDate)) && (toDateStamp($info['invoiceDate']) <= toDateStamp($toDate));
                        }
                    }
                    break;
            }

            if ((strtolower($info['extension']) == 'pdf') && ($ok)) {
                $fileList[] = $info;
            }
        }
    }

    return $fileList;
}

if (! function_exists('closePDF')) {
    function closePDF($pdfCounter)
    {
        global $pdf;

        if ($pdfCounter == 0) {
            $pdf->begin_page_ext(595, 842, '');
            $pdf->end_page_ext('');
        }

        $pdf->end_document('');
    }
}

function ownerReportProcess(&$context)
{
    $threshold = 3;
    global $dbh, $sess;
    $jasperInclude = [];
    $zipCounter = 0;
    $pdfCounter = 0;

    include_once 'lib/reportLib/reportincludesAll.php';
    include_once 'lib/ReportLib/commonReportFunctions.php';
    include_once 'functions/ownerReportProcessFunctions.php';
    include_once 'functions/page1PropertyReportFunctions.php';
    include_once 'functions/page2DebtorsDetailFunctions.php';
    include_once 'functions/pdfHeaders.php';

    require_once 'lib/phpdocx/classes/CreateDocx.php';


    // Start Email Address Book
    $email_cen_sendmail_setting = dbGetEmailCenParamSetting('SENDMAIL');
    $email_cen_send = false;
    if ($email_cen_sendmail_setting) {
        if ($email_cen_sendmail_setting['parameterDescription'] == '1') {
            $email_cen_send = true;
        }
    }
    // End Email Address Book

    if (! isPostback()) {
        $view = new MasterPage(userViews(), '/managementReports/ownerReportProcess.html');
        $view->setSection($context['module']);
    } else {
        $view = new UserControl(userViews(), '/managementReports/ownerReportProcess.html', '');
    }

    try {
        if ($context[IS_TASK]) {
            $view->bindAttributesFrom($context);
            extract($context, EXTR_OVERWRITE);
        } else {
            $view->bindAttributesFrom($_REQUEST);
            $propertyCount = 0;

            $forceGenerate = false;
            if (isset($view->items['forceGenerate'])) {
                $forceGenerate = (bool) $view->items['forceGenerate'];
            }

            if ($view->items['property']) {
                $propertyCount = count(deserializeParameters($view->items['property']));
            }
            $queue = new Queue(TASKTYPE_OWNER_REPORT);
            if (! $forceGenerate && $propertyCount > THRESHOLD_OWNERREPORT) {
                $_REQUEST['command'] = 'ownerReportProcess';
                $queue->add($_SESSION['clientID'], $_SESSION['un'], 'module=managementReports&command=ownerReportProcess', $_REQUEST);
            }
        }
        if (($context[IS_TASK]) || $forceGenerate || ($propertyCount <= THRESHOLD_OWNERREPORT)) {
            if (isset($view->items['reporttype'])) {
                $reportType = $view->items['reporttype'];
            }

            $report = dbGetReport($reportType);
            $reportParams = json_decode($report['reportParameters'], true);
            foreach ($reportParams as $indexParam => $params) {
                $view->items[$indexParam] = $params;
            }

            $title = $report['reportDescription'];
            $orientation = $report['reportOrientation'];
            $_page = getPageSize($orientation);

            $showAccCodesFlag = false;
            if (isset($view->items['show_acc_codes'])) {
                $showAccCodesFlag = true;
            }

            global $periodFrom;
            global $periodTo;
            global $reportingPeriodFrom;
            global $reportingPeriodTo;
            global $periodDescription;
            global $reportDescription;
            global $reportingTotal;
            global $reportingCurrPage;
            global $reportingTotalPages;
            global $filenameDescription;
            global $propertyName;
            global $client;
            global $client_street;
            global $client_city;
            global $client_state;
            global $client_postcode;
            global $line;
            global $startline;
            global $date;
            global $page;
            global $pdf;
            global $_fonts;
            global $propertyID;
            global $propertyGroup;
            global $clientDirectory;
            global $filePrefix;
            global $pathPrefix;
            global $logo;
            global $clientDB;

            $date = TODAY;
            $logo = $view->items['logo'];
            $logoFlag = $logo;
            logData($view->items['logo'] . '<<');
            $periodFrom = $view->items['periodFrom'];
            $periodTo = $view->items['periodTo'];

            $reportingPeriodFrom = $view->items['reportingPeriodFrom'];
            $reportingPeriodTo = $view->items['reportingPeriodTo'];

            $combinedReport = (isset($view->items['combinedReport']) ? (bool) $view->items['combinedReport'] : false);

            $hasTax = $sess->items['hasTax'];
            $includeCoverPage = (bool) $view->items['includeCoverPage'];
            $includeNotes = (bool) $view->items['includeNotes'];
            $includeCoverLetter = $view->items['includeCoverLetter'];

            //			$logoFile = dbGetClientLogo();
            $logoFile = dbGetClientLogo();
            $logoPath = "assets/clientLogos/{$logoFile}";

            logData($logoPath);

            $periodDescription = $view->items['reportPeriod'];
            $reportDescription = $view->items['description'];
            $filenameDescription = $view->items['filename_description'];
            $PDFtitle = 'Owners Reports';
            $filename_description = preg_replace("/([^\w\d])/", '_', $view->items['filename_description']);
            if (strlen($filename_description) > 0) {
                $fileName = $filename_description . '_';
            } else {
                $fileName = 'OwnersReports_';
            }

            $individualPDF = (bool) $view->items['sep_pdfs'];
            if ($view->items['emailOwner']) {
                $individualPDF = true;
            }

            $properties = deserializeParameters($view->items['property']);

            if (count($properties) == 1) {
                $propertyName = getPropName($thisPropCode);
            }

            $_filePath = "{$pathPrefix}{$clientDirectory}/pdf/" . DOC_OWNERSTATEMENT . '/';
            $_downloadPath = "{$clientDirectory}/pdf/" . DOC_OWNERSTATEMENT;
            $_resourcePath = "{$pathPrefix}{$clientDirectory}/pdf/" . DOC_OWNERSTATEMENT . '/_resources';

            // check if format is declared
            if (! array_key_exists('format', $view->items)) {
                // check if the sub report select is single option only
                $_subReports = extractID($view->items, 'sr');
                //   if($_subReports && count($_subReports) == 1){
                // get sub report format
                $_subReportID = array_keys($_subReports);
                $view->items['format'] = 'xlsx';
                foreach ($_subReportID as $key) {
                    $get_subReport_dtls = dbGetSubReportDetails($key);
                    if ($get_subReport_dtls && $get_subReport_dtls['subReportFormat'] == '') {
                        $view->items['format'] = '';
                    }
                }
                //   }
            }

            if ($view->items['format'] != FILETYPE_XLS) {
                if (! $individualPDF && ! $includeCoverLetter) {
                    $pdfDownloadLink = initializePDF($PDFtitle, $fileName, $_filePath, $_downloadPath, $propertyName, false);
                }
            }

            if ($includeCoverLetter) {
                $coverTemplate = dbGetLetterTemplate($view->items['letterTemplate']);
                $coverTemplate['letterTemplateBody'];
            }

            if ($view->items['datasource'] == DS_GROUP) {
                $propertyGroup = $view->items['filter'];
            }

            $vars = get_defined_vars();
            logData(print_array($vars, true));

            $_coverPage = 0;
            $attachment = [];

            define('A', 1);
            define('C', 2);

            $propertyCombine = [];
            $accrualsExcelAttachments = [];
            $expenditureExcelAttachments = [];
            $ownerStatementExcelAttachments = [];

            $parameters = [];

            if (isset($view->items['send_from'])) {
                $addlEmailConfig = dbGetEmailConfigByValue('send_from', $view->items['send_from']);
                if ($addlEmailConfig) {
                    $parameters['sendFrom'] = $addlEmailConfig['send_from'];
                    $parameters['fromName'] = $addlEmailConfig['from_name'];
                    $parameters['replyTo'] = $addlEmailConfig['reply_to'];
                } else {
                    $clientEmailConfig = dbGetClientEmailConfig();

                    $parameters['sendFrom'] = $clientEmailConfig['fromEmail'];
                    $parameters['fromName'] = $clientEmailConfig['fromName'];
                    $parameters['replyTo'] = $clientEmailConfig['replyTo'];
                }
            }

            //
            // #########################################
            // GENERATE REPORT
            if (! $combinedReport) { // IF BY PROPERTY
                // SET OVERALL TOTALS
                $reportingTotal = 0;
                $reportingCurrPage = 1;
                $reportingTotalPages = count($properties ?? []);
                //
                $ownerShareFile = [];
                $ownerShareFilePM = [];
                $ownerShareFilePMEmail = [];
                $ownerShareCount = 0;
                foreach ($properties as $ownerReportIndex => $propertyID) {
                    if ($view->items['ownerSharesReport'] == 1) {
                        $ownersShares = dbGetPropertyOwnersDetailed($propertyID);
                    } else {
                        $ownersShares = ['companyID' => ''];
                    }
                    foreach ($ownersShares as $ownerKey => $ownerShareData) {
                        $MultixlsDownloadPath = [];
                        $_coverPage++;
                        $page = 0;

                        $propertyName = getPropName($propertyID);

                        // Residential Simple Report
                        if ($report['reportPeriod'] == 'D' or $view->items['paymentDate'] != '') {
                            if ($view->items['datasource'] == DS_PAYDATE or $view->items['paymentDate'] != '') {
                                $periodTo = ($view->items['paymentDate'] == '' ? $view->items['filterProperty'] : $view->items['paymentDate']);
                                $periodFrom = dbGetNextDayOfPreviousOwnerPaymentDate($propertyID, $periodTo);
                            } else {
                                $periodFrom = $view->items['reportingPeriodFrom'];
                                $periodTo = $view->items['reportingPeriodTo'];
                            }

                            $previousPeriodTo = oneDayBefore($periodFrom);
                            $periodFrom_display = toDisplayDate($periodFrom);
                            $periodTo_display = toDisplayDate($periodTo);
                            $startFinancialYear = $periodFrom;
                            $endFinancialYear = $periodTo;
                            $reportingPeriodToPY = oneDayBefore($periodFrom);
                            $financialPeriodToPY = oneDayBefore($periodFrom);

                            $calendar = dbGetPeriod($propertyID, $periodFrom);
                            $calendar['startDate'] = $periodFrom;
                            $calendar['endDate'] = $periodTo;

                        } else {
                            $previousPeriodTo = oneDayBefore($periodFrom);
                            $periodFrom_display = toDisplayDate($periodFrom);
                            $periodTo_display = toDisplayDate($periodTo);
                            $calendar = dbGetPeriod($propertyID, $periodFrom);


                            $currentPeriod = $calendar['period'];

                            $calendarTo = dbGetPeriod($propertyID, $periodTo); // added by Raymond 28/1/11

                            $toPeriod = $calendarTo['period']; // added by Raymond 28/1/11
                            $currentYear = $calendar['year'];
                            [$previousPeriod, $previousYear] = periodBefore($currentPeriod, $currentYear);

                            $previousFinancialYear = $currentYear - 1;
                            $financialYearPeriod = dbGetPeriodDate($propertyID, 1, $currentYear);

                            $startFinancialYear = $financialYearPeriod['startDate'];
                            $endFinancialYear = $financialYearPeriod['endDate'];

                            $dateParts = explode('/', $endFinancialYear);
                            $startReportingYear = "01/{$dateParts[DATE_MONTH]}/{$dateParts[DATE_YEAR]}";

                            $reportingPeriodToPY = oneDayBefore($startReportingYear);
                            $financialPeriodToPY = oneDayBefore($startFinancialYear);
                        }
                        $timestamp = time();

                        // #####################################################################################
                        // ################### GET THE OWNERS DETAILS FOR PRINTING AT THE TOP###################

                        $owner = owner_details($propertyID);
                        $client = $owner['pmco_name'];
                        $client_street = $owner['pmco_street'];
                        $client_city = $owner['pmco_city'];
                        $client_state = $owner['pmco_state'];
                        $client_postcode = $owner['pmco_postcode'];

                        if ($view->items['format'] != FILETYPE_XLS) {
                            if ($individualPDF || $includeCoverLetter) {
                                $ownerShareCompanyID = $ownerShareData['companyID'] ?? '';
                                $pdfDownloadLink = initializePDF($PDFtitle, $fileName . $propertyID . '_' . $ownerShareCompanyID . '_', $_filePath, $_downloadPath, $propertyName, false);
                            }
                        }

                        if ($view->items['ownerSharesReport'] == 1 && ($ownerShareCount == 0 || $individualPDF)) {


                            $ownerShareFile[$ownerShareCount]['file'] = REPORTPATH . '/' . $pdfDownloadLink;
                            $ownerShareFile[$ownerShareCount]['content_type'] = 'application/pdf';
                            $ownerShareFilePM[$propertyID][$ownerShareCount]['file'] = REPORTPATH . '/' . $pdfDownloadLink;
                            $ownerShareFilePM[$propertyID][$ownerShareCount]['content_type'] = 'application/pdf';
                            $ownerShareCount++;
                        }

                        $view->items['processed'] = true;
                        /**    NEW REPORT PROCESSING CODE **/
                        $includes = dbGetSubReports($reportType, true);

                        // -- tracc cash options
                        $rentPaidTo = $view->items['rentPaidTo'];
                        // -- accruals options
                        $ownerAccountCodes = false;    // -- this relates to whether there are owners account codes or not
                        $description = 'Description'; // -- used in trial balance excel sheet i think used for account name

                        // -- extract the selected pages from the report
                        $subReports = extractID($view->items, 'sr');
                        $ownerName = dbGetOwnerName($propertyID);

                        // -- create the header object that gets used on each page
                        $header = new ReportHeader($propertyName, 'Owner: ' . $ownerName, $title . ' - ' . $periodDescription);
                        $header->xPos = 20;
                        $header->yPos = $_page->height - 25;

                        $footer = new TraccFooter($logoPath, 'OwnerStatement', $orientation);

                        if ($includeCoverPage) {
                            $header = new ReportHeader('Property: ', $propertyName, 'Owner: ' . $ownerName, $title . ' - ' . $periodDescription);
                            $header->xPos = 20;
                            $header->yPos = $_page->height - 25;
                            $coverPage = new CoverPage($pdf, $_coverPage, $orientation, $logoPath);
                            $coverPage->attachObject('header', $header);
                            $coverPage->preparePage();

                        }
                        // -- process the subreport pages

                        $xlsDownloadPath = '';

                        foreach ($includes as $s) {
                            // -- if the page is a dependency, load it

                            if ($s['dependency']) {
                                include $s['subReportFile'];
                            }
                            // -- if the page has been selected for inclusion - run it

                            if ($s['is_management_report']) {
                                if ($subReports[$s['subReportID']]) {
                                    $includesMngmtReport = dbGetSubReports_management($s['subReportFile']);
                                    foreach ($includesMngmtReport as $mngmtSub) {
                                        $sub_report_check = dbCheckSubreportInclusion($propertyID, $currentPeriod, $currentYear, $s['subReportFile'], $mngmtSub['sub_report_id']);
                                        if ($sub_report_check) {
                                            $s['sub_report_id'] = $mngmtSub['sub_report_id'];
                                            if ($mngmtSub['sub_report_file_path']) {
                                                include $mngmtSub['sub_report_file_path'];
                                                $pdfCounter = $pdfCounter + 1;
                                            }
                                        }

                                    }
                                }
                            } elseif ($subReports[$s['subReportID']]) {
                                // if (DEBUG) echo 'building ' . $s['subReportName'] . '... <br />';
                                // -- get any notes for that subreport

                                $subReportParams = json_decode($s['subReportParameter'], true);
                                foreach ($subReportParams as $indexParam => $params) {
                                    $view->items[$indexParam] = $params;
                                }

                                $notes_ = [];

                                if ($includeNotes) {
                                    if ($currentPeriod and $currentYear) {
                                        $notes_ = dbGetSubReportNote($s['reportID'], $s['subReportID'], $propertyID, $currentPeriod, $currentYear);
                                    } else {
                                        $notes_ = dbGetSubReportNoteByDates($s['reportID'], $s['subReportID'], $propertyID, $periodFrom, $periodTo);
                                    }

                                    $notes_same_page = dbGetSubReportNote($s['reportID'], $s['subReportID'], $propertyID, $currentPeriod, $currentYear, 1);
                                }

                                foreach ($notes_ as $notes) {
                                    if ($notes['note'] && $includeNotes) {
                                        $key = md5($notes['reportID'] . $notes['subReportID'] . $notes['propertyID'] . $notes['period'] . $notes['year'] . $notes['report_date'] . '0');
                                        $_pdipath = "{$_resourcePath}/$key.pdf";

                                        if (file_exists($_pdipath)) {
                                            $pdi = new ImportedPage($pdf, $_pdipath, $orientation, $logoPath);
                                            $header->subTitle = $s['subReportName'];
                                            $header->subText = null;
                                            $pdi->attachObject('header', $header);
                                            $pdi->attachObject('footer', $footer);
                                            $i = 1;
                                            while ($pdi->loadPage($i)) {
                                                $page++;
                                                $pdi->preparePage();
                                                $pdi->render();
                                                $pdi->endPage();
                                                $i++;
                                            }
                                            $pdi->close();
                                        }
                                    }
                                }


                                if ($includeCoverPage && ($s['subReportFile'] || $includeNotes)) {
                                    $coverPage->addPage($s['subReportName'], $page + 1);
                                }

                                // if(!glob($s['subReportFile']))
                                if (! file_exists(SYSTEMPATH . '/commands/managementReports/' . $s['subReportFile'])) {
                                    $msgError = "require(): Failed opening required '" . $s['subReportFile'] . "'";
                                    myErrorHandlerV2(1, $msgError, '', '');
                                }

                                if ($s['subReportFile']) {
                                    include $s['subReportFile'];
                                    if ($s['subReportFormat'] != 'xlsx') {
                                        $pdfCounter = $pdfCounter + 1;
                                    }
                                }
                            }
                        }

                        if ($includeCoverPage) {
                            $coverPage->render();
                            $coverPage->endPage();
                        }

                        $_coverPage += $page;


                        $s3_attachments = [];

                        $has_zip = false;
                        //                        $startDate = $calendar['startDate'];
                        //                        $endDate = $calendar['endDate'];
                        $startDate = $periodFrom;
                        $endDate = $periodTo;

                        if ($view->items['emailOwner']) {
                            $zip_folder = "{$pathPrefix}{$clientDirectory}/ap_zip";
                            if (! file_exists($zip_folder)) {
                                mkdir($zip_folder, FILE_PERMISSION, true);
                            }

                            if (! $has_zip) {
                                $zip = new ZipArchive();
                                $filePath = "$zip_folder/" . $propertyID . '_attachments' . date('YmdHis') . ($zipCounter++) . '.zip';
                                // $dlPath = "{$clientDirectory}/ap_zip/".$propertyID."_attachments.zip";
                                if (file_exists($filePath)) {
                                    unlink($filePath);
                                }
                                if ($zip->open($filePath, ZipArchive::CREATE) !== true) {
                                    exit("cannot open <$filePath>\n");
                                }
                                $has_zip = true;
                            }

                            //                            $startDate = $calendar['startDate'];
                            //                            $endDate = $calendar['endDate'];
                            $startDate = $periodFrom;
                            $endDate = $periodTo;

                            // # GET PROPERTY DOCUMENTS WITHIN PERIOD
                            $property_docs = dbGetPropertyDocsForOwnerReport($propertyID, '', $startDate, $endDate);
                            if ($_SESSION['s3StorageEnabled']) {
                                $S3 = new S3FileHandler();
                            }
                            if (count($property_docs ?? []) > 0) {
                                $zip = new ZipArchive();
                                $filePath = "$zip_folder/" . $propertyID . '_attachments' . date('YmdHis') . ($zipCounter++) . '.zip';
                                if (file_exists($filePath)) {
                                    unlink($filePath);
                                }
                                if ($zip->open($filePath, ZipArchive::CREATE) !== true) {
                                    exit("cannot open <$filePath>\n");
                                }
                                foreach ($property_docs as $prop_doc) {
                                    if ($_SESSION['s3StorageEnabled'] && isset($prop_doc['on_s3']) && (bool) $prop_doc['on_s3'] && isset($prop_doc['documentID'])) {
                                        $file4zip = $S3->setTempAttachments([$prop_doc['documentID']], true);
                                        if (isset($file4zip['file']) && file_exists($file4zip['file'])) {
                                            $zip->addFile($file4zip['file'], $file4zip['name']);
                                            $s3_attachments[] = $file4zip;
                                        }
                                    } else {
                                        if (file_exists(REPORTPATH . '/' . $prop_doc['filename'])) {
                                            $zip->addFile(REPORTPATH . '/' . $prop_doc['filename'], str_replace("{$clientDirectory}/pdf/PropertyDocuments/", '', $prop_doc['filename']));
                                        }
                                    }
                                }
                                $has_zip = true;
                            }
                            //
                        }

                        if ($view->items['emailOwnerAttach']) {
                            $param = [
                                'startDate' => $startDate,
                                'endDate' => $endDate,
                                'period' => $currentPeriod,
                                'year' => $currentYear,
                                'type' => $view->items['cashNAccruals'],
                                'propertyID' => $propertyID,
                            ];
                            if ($view->items['cashNAccruals'] == 1) {
                                $getAttachment = dbGetOwnerBatchNumberAccruals($startDate, $endDate, $propertyID);
                            } else {
                                $getAttachment = getAPPaidByBatch($param);
                            }
                            $batchWithAttachment = dbGetInvoiceFileByBatchNumber($getAttachment, $propertyID);
                            if (count($batchWithAttachment ?? []) > 0) {
                                if (! $has_zip) {

                                    if (! $zip_folder) {
                                        $zip_folder = "{$pathPrefix}{$clientDirectory}/ap_zip";
                                        if (! file_exists($zip_folder)) {
                                            mkdir($zip_folder, FILE_PERMISSION, true);
                                        }
                                    }

                                    $zip = new ZipArchive();
                                    $filePath = "$zip_folder/" . $propertyID . '_attachments' . date('YmdHis') . ($zipCounter++) . '.zip';
                                    // $dlPath = "{$clientDirectory}/ap_zip/".$propertyID."_attachments.zip";
                                    if (file_exists($filePath)) {
                                        unlink($filePath);
                                    }
                                    if ($zip->open($filePath, ZipArchive::CREATE) !== true) {
                                        exit("cannot open <$filePath>\n");
                                    }
                                    $has_zip = true;
                                }

                                foreach ($getAttachment as $getAttachmentDataBatchNr) {
                                    $batch = $getAttachmentDataBatchNr;
                                    $getInvoiceFiles = dbGetInvoiceFileByBatchNumber($batch, $propertyID);
                                    foreach ($getInvoiceFiles as $getInvoiceFilesData) {
                                        /*
                                        if(AWS_S3_ENABLED && isset($getInvoiceFilesData['on_s3']) && (bool)$getInvoiceFilesData['on_s3'] && isset($getInvoiceFilesData['documentID']) ){
                                            $file4zip = $S3->setTempAttachments($getInvoiceFilesData['documentID'],true);
                                            if(isset($file4zip['file']) && file_exists($file4zip['file']) ){
                                                $zip->addFile($file4zip['file'], $file4zip['name']);
                                                $s3_attachments[] = $file4zip;
                                            }
                                        }
                                        else{
                                        */

                                        $attachmentFileName = $getInvoiceFilesData['filename'];
                                        if (file_exists(REPORTPATH . '/' . $attachmentFileName)) {
                                            $zip->addFile(REPORTPATH . '/' . $attachmentFileName, str_replace("{$clientDirectory}/pdf/SupplierInvoices/", '', $attachmentFileName));
                                        }
                                        // }
                                        // $attachmentCtr++;
                                    }
                                }
                            }


                        }

                        //
                        if ($has_zip) {
                            $zip->close();
                            if (file_exists($filePath)) {
                                $_attachment[1]['file'] = $filePath;
                                $_attachment[1]['content_type'] = 'application/zip';
                            }

                            //                				if ($view->items['publishToOwner'])
                            //                				{
                            //                                    $doc = array ();
                            //                					$doc['documentTitle'] = $view->items['description'] . ' for ' . $view->items['reportPeriod']. ' Invoices';
                            //                					$doc['documentDescription'] = 'Owner report invoices for property ' . $propertyID ;
                            //                					$doc['documentType'] = DOC_OWNER;
                            //                					$doc['createdBy'] = $_SESSION['un'];
                            //                					$doc['primaryID'] = $propertyID;
                            //                					$doc['secondaryID'] = '';
                            //                					$doc['filename'] = str_replace("{$pathPrefix}",'',$filePath);
                            //                					$doc['publishToOwner'] = 1;
                            //                                    $doc['period_from'] = date('Y-m-d h:i:s', strtotime( str_replace('/','-',$periodFrom)));
                            //                                    $doc['period_to'] = date('Y-m-d h:i:s', strtotime( str_replace('/','-',$periodTo)));
                            //                					$newDociID = dbAddDocument ($doc);
                            //                					#
                            //                					# COPY TO S3 AND DELETE IT
                            //                					#
                            //                					if($_SESSION['s3StorageEnabled'] && $newDocID && isset($doc['filename']) && file_exists($pathPrefix.$doc['filename'])){
                            //                						$S3 = new S3FileHandler();
                            //                						$copied = $S3->copyFile($newDocID,"Documents/".$propertyID."/PropertyDocuments",basename($doc['filename']),true);
                            //                					}
                            //                					#
                            //                					// $view->items['statusMessage2'][] = 'Owners Report can now be downloaded by the owners using their own interface.';
                            //                				}
                        }

                        // attach Agent Invoices
                        if ($view->items['agentInvoiceOwnerAttach']) {
                            $agentInvoiceFileList = fetchAgentInvoices(1, $periodFrom, $periodTo, $propertyID);
                            if (count($agentInvoiceFileList ?? []) > 0) {
                                $zip_folder = "{$pathPrefix}{$clientDirectory}/ap_agent_invoice_zip";
                                if (! file_exists($zip_folder)) {
                                    mkdir($zip_folder, FILE_PERMISSION, true);
                                }
                                $zip = new ZipArchive();
                                $filePath = "$zip_folder/" . $propertyID . '_attachments_AgentInvoices_' . date('YmdHis') . ($zipCounter++) . '.zip';
                                if (file_exists($filePath)) {
                                    unlink($filePath);
                                }
                                if ($zip->open($filePath, ZipArchive::CREATE) !== true) {
                                    exit("cannot open <$filePath>\n");
                                }
                                foreach ($agentInvoiceFileList as $agentInvoiceFile) {
                                    $attachmentFile = $agentInvoiceFile['downloadPath'];
                                    $attachmentFileName = $agentInvoiceFile['fileName'];
                                    if (file_exists($attachmentFile)) {
                                        $zip->addFile($attachmentFile, $attachmentFileName);
                                    }
                                }
                                /*
                                if(AWS_S3_ENABLED){
                                    $inv_docs = dbGetDocuments(DOC_INV_TENANT,$propertyID,'',0, null, $periodFrom,$periodTo);
                                    foreach ($inv_docs as $idc) {
                                       $file4zip = $S3->setTempAttachments($idc['documentID'],true);
                                       if(isset($file4zip['file']) && file_exists($file4zip['file']) ){
                                           $zip->addFile($file4zip['file'], $file4zip['name']);
                                           $s3_attachments[] = $file4zip;
                                       }
                                    }
                                }
                                */
                                $zip->close();
                                if (file_exists($filePath)) {
                                    $_attachment[2]['file'] = $filePath;
                                    $_attachment[2]['content_type'] = 'application/zip';
                                }
                            }
                        }


                        /** END OF NEW CODE **/
                        if ($includeCoverLetter && ! $individualPDF) {
                            closePDF($pdfCounter);
                        }

                        if ($individualPDF) {
                            $_coverPage = 0;
                            if ($view->items['format'] != FILETYPE_XLS) {
                                closePDF($pdfCounter);
                                $attachment[$ownerReportIndex]['file'] = REPORTPATH . '/' . $pdfDownloadLink;
                                $attachment[$ownerReportIndex]['content_type'] = 'application/pdf';
                                // echo $pdfDownloadLink . "<Br>";
                            }

                        }

                        // 2012-07-18: Added e-mail owner as an option as per task # 490420 [Morph]
                        // 2012-07-19: Moved the code to within the property loop and isolated the attachments so that owners wouldn't be sent the reports for other owners [Andrew]
                        if ($view->items['emailOwner']) {
                            $emailTemplate = [];
                            if ($view->items['emailTemplate']) {
                                $emailTemplate = dbGetEmailContentTemplate($view->items['emailTemplate']);
                                $body = $emailTemplate['email_body'];
                                $placeholder = ['%TenantCode%', '%TenantLocation%', '%TenantName%', '%SalutationName%', '%CompanyName%', '%PropertyCode%', '%PropertyName%', '%PropertyAddress%', '%ReportFromDate%', '%ReportEndDate%', '%SalutationName%', '%ReceiptNo%', '%PrimaryOwnerCompanySalutation%'];
                                foreach ($placeholder as $holder) {

                                    $folder = BASEPATH . '/framework/views/emails/' . $clientDB;
                                    if (! glob($folder)) {
                                        mkdir($folder);
                                    }

                                    $body = str_replace($holder, '<?=' . $holder, $body);
                                    $body = str_replace($holder, '$var' . $holder, $body);
                                    $body = str_replace($holder, "['" . str_replace('%', '', $holder) . "']" . $holder, $body);
                                    $body = str_replace($holder, '?>', $body);
                                }

                                file_put_contents($folder . '/email_layout1.html', $body);
                            }

                            $_attachment = [];
                            $_attachment[0]['file'] = REPORTPATH . '/' . $pdfDownloadLink;
                            $_attachment[0]['content_type'] = 'application/pdf';
                            if (! $view->items['emailTemplate']) {
                                $email = new EmailTemplate('views/emails/ownerReportEmail.html', SYSTEMURL);
                            } else {
                                $email = new EmailTemplate("views/emails/$clientDB/email_layout1.html", SYSTEMURL);
                            }

                            $emailAddresses = dbGetListOwnerEmail($propertyID);
                            $manager = dbGetManagerDetail($propertyID);
                            foreach ($emailAddresses as $key => $row) {
                                $emailAddresses[$key]['propertyManagerName'] = $manager['propertyManagerName'];
                                $emailAddresses[$key]['propertyManagerTitle'] = $manager['propertyManagerTitle'];
                                $emailAddresses[$key]['propertyManagerEmail'] = $manager['propertyManagerEmail'];
                                $emailAddresses[$key]['propertyManagerMobileNumber'] = $manager['propertyManagerMobileNumber'];
                            }

                            $salutationPrimaryOwner = dbGetSupplierContactSalutation($manager['primary_owner']);
                            $email->items['PrimaryOwnerCompanySalutation'] = $salutationPrimaryOwner;

                            if ($view->items['emailOption'] == 1) {
                                $emailAddresses[0]['emailAddress'] = $emailAddresses[0]['propertyManagerEmail'];
                                $temporaryEmail = $emailAddresses[0];
                                $emailAddresses = [];
                                $emailAddresses[] = $temporaryEmail;
                            }

                            $email->items['clientName'] = dbGetClientName();
                            $email->items['propertyCode'] = $propertyID;
                            $email->items['propertyName'] = $propertyName;
                            $email->items['PropertyCode'] = $propertyID;
                            $email->items['PropertyName'] = $propertyName;
                            $email->items['ReportFromDate'] = $periodFrom;
                            $email->items['ReportEndDate'] = $periodTo;
                            $prop = dbGetPropertyDetails($propertyID);
                            $email->items['PropertyAddress'] = $prop['propertyDescription'];
                            $email->items['SalutationName'] = dbGetPropertyContactSalutationPrimary($propertyID);

                            $subject = $view->items['emailTemplate'] ? $emailTemplate['email_subject'] : '';
                            $subject = str_replace('%PropertyCode%', $propertyID, $subject);
                            $subject = str_replace('%PropertyName%', $propertyName, $subject);
                            $subject = str_replace('%ReportFromDate%', $periodFrom, $subject);
                            $subject = str_replace('%ReportEndDate%', $periodTo, $subject);
                            $subject = str_replace('%PropertyAddress%', $prop['propertyDescription'], $subject);
                            $subject = str_replace('%SalutationName%', $email->items['SalutationName'], $subject);
                            $subject = str_replace('%PrimaryOwnerCompanySalutation%', $email->items['PrimaryOwnerCompanySalutation'], $subject);


                            $emailAddressCC = '';
                            if ($view->items['emailOwnerCCMan'] && $view->items['emailOption'] == 0) {
                                $getPrortfolioCode = dbGetPropertyPortfolioById($propertyID);
                                $emailAddressCC = dbGetParam('PORTMEMAIL', $getPrortfolioCode['pmpr_portfolio']);
                            }

                            $singleDocAttachment = '';
                            $has_zip = false;
                            //                            $startDate = $calendar['startDate'];
                            //                            $endDate = $calendar['endDate'];
                            $startDate = $periodFrom;
                            $endDate = $periodTo;

                            if ($view->items['emailOwner']) {
                                $zip_folder = "{$pathPrefix}{$clientDirectory}/ap_zip";
                                if (! file_exists($zip_folder)) {
                                    mkdir($zip_folder, FILE_PERMISSION, true);
                                }

                                if (! $has_zip) {
                                    $zip = new ZipArchive();
                                    $filePath = "$zip_folder/" . $propertyID . '_attachments' . date('YmdHis') . ($zipCounter++) . '.zip';
                                    // $dlPath = "{$clientDirectory}/ap_zip/".$propertyID."_attachments.zip";
                                    if (file_exists($filePath)) {
                                        unlink($filePath);
                                    }
                                    if ($zip->open($filePath, ZipArchive::CREATE) !== true) {
                                        exit("cannot open <$filePath>\n");
                                    }
                                    $has_zip = true;
                                }

                                // # GET PROPERTY DOCUMENTS WITHIN PERIOD
                                $property_docs = dbGetPropertyDocsForOwnerReport($propertyID, '', $startDate, $endDate);
                                if ($_SESSION['s3StorageEnabled']) {
                                    $S3 = new S3FileHandler();
                                }
                                if (count($property_docs ?? []) > 1) {
                                    $zip = new ZipArchive();
                                    $filePath = "$zip_folder/" . $propertyID . '_attachments' . date('YmdHis') . ($zipCounter++) . '.zip';
                                    if (file_exists($filePath)) {
                                        unlink($filePath);
                                    }
                                    if ($zip->open($filePath, ZipArchive::CREATE) !== true) {
                                        exit("cannot open <$filePath>\n");
                                    }
                                    foreach ($property_docs as $prop_doc) {
                                        if ($_SESSION['s3StorageEnabled'] && isset($prop_doc['on_s3']) && (bool) $prop_doc['on_s3'] && isset($prop_doc['documentID'])) {
                                            $file4zip = $S3->setTempAttachments([$prop_doc['documentID']], true);
                                            if (isset($file4zip['file']) && file_exists($file4zip['file'])) {
                                                $zip->addFile($file4zip['file'], $file4zip['name']);
                                                $s3_attachments[] = $file4zip;
                                            }
                                        } else {
                                            if (file_exists(REPORTPATH . '/' . $prop_doc['filename'])) {
                                                $zip->addFile(REPORTPATH . '/' . $prop_doc['filename'], str_replace("{$clientDirectory}/pdf/PropertyDocuments/", '', $prop_doc['filename']));
                                            }
                                        }
                                    }
                                    $has_zip = true;
                                } elseif (count($property_docs ?? []) == 1) {
                                    foreach ($property_docs as $prop_doc) {
                                        if ($_SESSION['s3StorageEnabled'] && isset($prop_doc['on_s3']) && (bool) $prop_doc['on_s3'] && isset($prop_doc['documentID'])) {
                                            $file4zip = $S3->setTempAttachments([$prop_doc['documentID']], true);
                                            if (isset($file4zip['file']) && file_exists($file4zip['file'])) {
                                                $zip->addFile($file4zip['file'], $file4zip['name']);
                                                $s3_attachments[] = $file4zip;
                                            }
                                            $has_zip = true;
                                        } else {
                                            if (file_exists(REPORTPATH . '/' . $prop_doc['filename'])) {
                                                $singleDocAttachment = REPORTPATH . '/' . $prop_doc['filename'];
                                            }
                                        }
                                    }
                                }
                                //
                            }

                            if ($view->items['emailOwnerAttach']) {
                                $param = [
                                    'startDate' => $startDate,
                                    'endDate' => $endDate,
                                    'period' => $currentPeriod,
                                    'year' => $currentYear,
                                    'type' => $view->items['cashNAccruals'],
                                    'propertyID' => $propertyID,
                                ];
                                if ($view->items['cashNAccruals'] == 1) {
                                    // use dbGetOwnerBatchNumberAccruals, not dbGetOwnerBatchNumberAccrualsByPeriod to get 2 or more periods
                                    $getAttachment = dbGetOwnerBatchNumberAccruals($startDate, $endDate, $propertyID);
                                } else {
                                    $getAttachment = getAPPaidByBatch($param);
                                }

                                $batchWithAttachment = dbGetInvoiceFileByBatchNumber($getAttachment, $propertyID);

                                if (count($batchWithAttachment ?? []) > 0) {
                                    if (! $has_zip) {
                                        $zip = new ZipArchive();
                                        $filePath = "$zip_folder/" . $propertyID . '_attachments' . date('YmdHis') . ($zipCounter++) . '.zip';
                                        // $dlPath = "{$clientDirectory}/ap_zip/".$propertyID."_attachments.zip";
                                        if (file_exists($filePath)) {
                                            unlink($filePath);
                                        }
                                        if ($zip->open($filePath, ZipArchive::CREATE) !== true) {
                                            exit("cannot open <$filePath>\n");
                                        }
                                        $has_zip = true;
                                    }
                                    foreach ($getAttachment as $getAttachmentDataBatchNr) {
                                        $batch = $getAttachmentDataBatchNr;
                                        $getInvoiceFiles = dbGetInvoiceFileByBatchNumber($batch, $propertyID);
                                        foreach ($getInvoiceFiles as $getInvoiceFilesData) {
                                            if ($_SESSION['s3StorageEnabled'] && isset($getInvoiceFilesData['on_s3']) && (bool) $getInvoiceFilesData['on_s3'] && isset($getInvoiceFilesData['documentID'])) {
                                                $file4zip = $S3->setTempAttachments($getInvoiceFilesData['documentID'], true);
                                                if (isset($file4zip['file']) && file_exists($file4zip['file'])) {
                                                    $zip->addFile($file4zip['file'], $file4zip['name']);
                                                    $s3_attachments[] = $file4zip;
                                                }
                                            } else {
                                                $attachmentFileName = $getInvoiceFilesData['filename'];
                                                if (file_exists(REPORTPATH . '/' . $attachmentFileName)) {
                                                    $zip->addFile(REPORTPATH . '/' . $attachmentFileName, str_replace("{$clientDirectory}/pdf/SupplierInvoices/", '', $attachmentFileName));
                                                }

                                            }
                                            // $attachmentCtr++;
                                        }
                                    }
                                    // $zip->close();
                                    // $_attachment[1]['file'] = $filePath;
                                    // $_attachment[1]['content_type'] = 'application/zip';
                                }
                            }

                            //
                            if ($has_zip) {
                                $zip->close();
                                if (file_exists($filePath)) {
                                    $_attachment[1]['file'] = $filePath;
                                    $_attachment[1]['content_type'] = 'application/zip';
                                }
                            }

                            // attach Agent Invoices
                            if ($view->items['agentInvoiceOwnerAttach']) {
                                $agentInvoiceFileList = fetchAgentInvoices(1, $periodFrom, $periodTo, $propertyID);
                                if (count($agentInvoiceFileList ?? []) > 0) {
                                    $zip_folder = "{$pathPrefix}{$clientDirectory}/ap_agent_invoice_zip";
                                    if (! file_exists($zip_folder)) {
                                        mkdir($zip_folder, FILE_PERMISSION, true);
                                    }
                                    $zip = new ZipArchive();
                                    $filePath = "$zip_folder/" . $propertyID . '_attachments_AgentInvoices_' . date('YmdHis') . ($zipCounter++) . '.zip';
                                    if (file_exists($filePath)) {
                                        unlink($filePath);
                                    }
                                    if ($zip->open($filePath, ZipArchive::CREATE) !== true) {
                                        exit("cannot open <$filePath>\n");
                                    }
                                    foreach ($agentInvoiceFileList as $agentInvoiceFile) {
                                        $attachmentFile = $agentInvoiceFile['downloadPath'];
                                        $attachmentFileName = $agentInvoiceFile['fileName'];
                                        if (file_exists($attachmentFile)) {
                                            $zip->addFile($attachmentFile, $attachmentFileName);
                                        }
                                    }
                                    $zip->close();
                                    if (file_exists($filePath)) {
                                        $_attachment[2]['file'] = $filePath;
                                        $_attachment[2]['content_type'] = 'application/zip';
                                    }
                                }
                            }

                            // attach accruals trial balance
                            if ($xlsDownloadPath != '') {
                                $_attachment[3]['file'] = $xlsDownloadPath;
                                $_attachment[3]['content_type'] = 'application/xlsx';
                            }

                            if (! empty($MultixlsDownloadPath)) {

                                $key = 10;
                                foreach ($MultixlsDownloadPath as $rec) {
                                    $key = $key + 1;
                                    $_attachment[$key]['file'] = $rec[0];
                                    $_attachment[$key]['content_type'] = 'application/xlsx';
                                }
                            }

                            //
                            if ($email_cen_send && $view->items['emailOption'] == 0 && $view->items['ownerSharesReport'] != 1) {
                                $ownerCombine = [];
                                $oldEmailAddress = $emailAddresses;
                                $emailAddresses = dbGetOwnersEmailsCentralised($propertyID, 'OWNRPT');
                                $manager = dbGetManagerDetail($propertyID);
                                foreach ($emailAddresses as $key => $row) {
                                    $emailAddresses[$key]['propertyManagerName'] = $manager['propertyManagerName'];
                                    $emailAddresses[$key]['propertyManagerTitle'] = $manager['propertyManagerTitle'];
                                    $emailAddresses[$key]['propertyManagerEmail'] = $manager['propertyManagerEmail'];
                                    $emailAddresses[$key]['propertyManagerMobileNumber'] = $manager['propertyManagerMobileNumber'];
                                }
                                $defaultEmailSend = '1';
                                foreach ($emailAddresses as $y => $aRowCentralisedEmailCheck) {
                                    $defaultEmail = $aRowCentralisedEmailCheck['defaultEmail'];
                                    if ($defaultEmail == '0') {
                                        $defaultEmailSend = $defaultEmail;
                                    }

                                    if (! trim($aRowCentralisedEmailCheck['emailAddress']) && trim($aRowCentralisedEmailCheck['companyCode'])) {
                                        $emailAddresses[$y]['noEmail'] = 1;
                                    }
                                    foreach ($oldEmailAddress as $x => $allOwner) {
                                        if ($allOwner['companyCode'] == $aRowCentralisedEmailCheck['companyCode']) {
                                            unset($oldEmailAddress[$x]);
                                        }
                                    }
                                }

                                foreach ($oldEmailAddress as $allOwner) {
                                    $allOwner['noEmail'] = 1;
                                    $emailAddresses[] = $allOwner;
                                }

                                $propertyContact = 0;
                                foreach ($emailAddresses as $xx => $aRowCentralisedEmail) {
                                    $emailAddressCentral = $aRowCentralisedEmail['emailAddress'];
                                    $defaultEmail = $aRowCentralisedEmail['defaultEmail'];
                                    if ($defaultEmailSend == $defaultEmail || $aRowCentralisedEmail['noEmail']) {

                                        $companyArray = [];
                                        if ($includeCoverLetter && $aRowCentralisedEmail['companyCode'] && ! in_array($aRowCentralisedEmail['companyCode'], $companyArray)) {
                                            $letter_filePath = $_filePath . '/' . time() . $xx . '_' . $aRowCentralisedEmail['pmos_prop'] . '_' . $aRowCentralisedEmail['companyCode'] . '.pdf';

                                            $salutation = '';
                                            $salutation_name = '';
                                            if (strpos($coverTemplate['letterTemplateBody'], '%salutation%') !== false || strpos($coverTemplate['letterTemplateBody'], '%contactName%') !== false || $coverTemplate['useDocxFile']) {
                                                $salutation_query = dbGetCompanyContactSalutation($aRowCentralisedEmail['companyCode']);
                                                $salutation = $salutation_query['pmcj_phone_no'];
                                                $salutation_name = $salutation_query['pmct_name'];
                                            }

                                            if ($coverTemplate['useDocxFile']) {

                                                $file = '../reports/' . $coverTemplate['letterFilename'];
                                                copy($file, $letter_filePath . '.docx');

                                                $replacer = replaceCoverLetterPlaceholderArray($aRowCentralisedEmail, $coverTemplate['letterTemplateBody'], $propertyName, $periodFrom, $periodTo, $salutation, $salutation_name);

                                                $docx = new CreateDocxFromTemplate($letter_filePath . '.docx');
                                                $docx->setTemplateSymbol('%');

                                                $docx->replaceVariableByText($replacer);
                                                $docx->modifyPageLayout('A4');
                                                $docx->createDocx($letter_filePath . '.docx');

                                                $docx = new CreateDocx();
                                                $docx->transformDocument($letter_filePath . '.docx', $letter_filePath, 'libreoffice', ['outdir' => 'C:\libreoffice\temp']);
                                                // $transform = new TransformDocAdvPDF($letter_filePath . ".docx");
                                                // $transform->transform($letter_filePath);

                                                while (! glob($letter_filePath)) {
                                                    sleep(5);
                                                }

                                                unlink($letter_filePath . '.docx');
                                            } else {
                                                $letter = new HTMLPage2($letter_filePath, PDF_PAGE_ORIENTATION, PDF_UNIT, PDF_PAGE_FORMAT);
                                                $letter->SetFont('helvetica', '', 8, '', true);
                                                $letter->setImageScale(1.53);
                                                $text = replaceCoverLetterPlaceholder($aRowCentralisedEmail, $coverTemplate['letterTemplateBody'], $propertyName, $periodFrom, $periodTo, $salutation, $salutation_name);
                                                $letter->render($text);
                                            }

                                            $fileArray = [];
                                            $fileArray[] = $letter_filePath;
                                            $fileArray[] = REPORTPATH . '/' . $pdfDownloadLink;

                                            $timestamp = date('dmYHis');
                                            $ext = '.pdf';
                                            $fullFileName = $_filePath . $fileName . $propertyID . '_' . $timestamp . '_' . $aRowCentralisedEmail['companyCode'] . $ext;
                                            mergePDF($fileArray, false, $PDFtitle, $fullFileName, '');
                                            $_attachment[0]['file'] = $fullFileName;
                                            $companyArray[$aRowCentralisedEmail['companyCode']] = $_attachment[0]['file'];

                                            unlink($letter_filePath);
                                            $ownerCombine[] = $fullFileName;
                                        } elseif (in_array($aRowCentralisedEmail['companyCode'], $companyArray)) {
                                            $_attachment[0]['file'] = $companyArray[$aRowCentralisedEmail['companyCode']];
                                        } else {
                                            $_attachment[0]['file'] = REPORTPATH . '/' . $pdfDownloadLink;
                                        }

                                        if (! $aRowCentralisedEmail['companyCode']) {
                                            $propertyContact = 1;
                                        }

                                        // attach single property document at the end
                                        if ($singleDocAttachment) {
                                            $singleDoc['file'] = $singleDocAttachment;
                                            $singleDoc['content_type'] = 'application/pdf';
                                            $_attachment[4] = $singleDoc;
                                        }

                                        if (isValid($emailAddressCentral, TEXT_EMAIL, false)) {
                                            $trailLetter = [
                                                'letterTemplateID' => dbGetLetterCategoryID('Owner Report'),
                                                'letterFormat' => 'pdf',
                                                'letterRecipient' => $emailAddressCentral . ($emailAddressCC ? ';' . $emailAddressCC : ''),
                                                'letterEmailSubject' => $subject ? $subject : 'Owner Report for [' . $propertyID . '] ' . $propertyName . ' (' . $email->items['clientName'] . ')',
                                                'letterEmailBody' => $email->toString(),
                                                'letterTemplateBody' => '',
                                                'propertyCode' => $propertyID,
                                                'leaseCode' => '',
                                                'userID' => $_SESSION['user_id'],
                                                'dl_path' => $pdfDownloadLink,

                                            ];
                                            if ($pdfDownloadLink) {
                                                $parameters['letter_history_id'] = dbInsertTrailLetter($trailLetter);
                                            }
                                            sendMail(
                                                $emailAddressCentral,
                                                '',
                                                $email->toString(),
                                                $subject ? $subject : 'Owner Report for [' . $propertyID . '] ' . $propertyName . ' (' . $email->items['clientName'] . ')',
                                                $_attachment,
                                                null,
                                                null,
                                                false,
                                                $emailAddressCC,
                                                true,
                                                true,
                                                $parameters
                                            );
                                        } elseif ($defaultEmail == 1 || $aRowCentralisedEmail['noEmail']) {
                                            $trailLetter = [
                                                'letterTemplateID' => dbGetLetterCategoryID('Owner Report'),
                                                'letterFormat' => 'pdf',
                                                'letterRecipient' => $_SESSION['email'] . ($emailAddressCC ? ';' . $emailAddressCC : ''),
                                                'letterEmailSubject' => $subject ? $subject : 'Owner Report for [' . $propertyID . '] ' . $propertyName . ' (' . $email->items['clientName'] . ')',
                                                'letterEmailBody' => $email->toString(),
                                                'letterTemplateBody' => '',
                                                'propertyCode' => $propertyID,
                                                'leaseCode' => '',
                                                'userID' => $_SESSION['user_id'],
                                                'dl_path' => $pdfDownloadLink,

                                            ];
                                            if ($pdfDownloadLink) {
                                                $parameters['letter_history_id'] = dbInsertTrailLetter($trailLetter);
                                            }
                                            sendMail(
                                                $_SESSION['email'],
                                                '',
                                                $email->toString(),
                                                $subject ? $subject : 'Owner Report for [' . $propertyID . '] ' . $propertyName . ' (' . $email->items['clientName'] . ')',
                                                $_attachment,
                                                null,
                                                null,
                                                false,
                                                $emailAddressCC,
                                                true,
                                                true,
                                                $parameters
                                            );
                                        }
                                    }
                                }

                                if ($includeCoverLetter) {
                                    if ($propertyContact == 0) {
                                        unlink(REPORTPATH . '/' . $pdfDownloadLink);
                                        mergePDF($ownerCombine, false, $PDFtitle, REPORTPATH . '/' . $pdfDownloadLink, '');
                                    } else {
                                        $letter_filePath = $_filePath . '/' . time() . $xx . '_' . $aRowCentralisedEmail['pmos_prop'] . '_' . $aRowCentralisedEmail['companyCode'] . '.pdf';
                                        mergePDF($ownerCombine, false, $PDFtitle, $letter_filePath, '');
                                    }

                                    // if (count($ownerCombine) == 1)
                                    // unlink($ownerCombine[0]);
                                }

                            } else {
                                if (! empty($emailAddresses)) {
                                    $ownerCombine = [];
                                    foreach ($emailAddresses as $xx => $emailAddress) {
                                        if ($includeCoverLetter) {
                                            $letter_filePath = $_filePath . '/' . time() . $xx . '_' . $emailAddress['pmos_prop'] . '_' . $emailAddress['pmos_owner'] . '.pdf';

                                            $salutation = '';
                                            $salutation_name = '';
                                            if (strpos($coverTemplate['letterTemplateBody'], '%salutation%') !== false || strpos($coverTemplate['letterTemplateBody'], '%contactName%') !== false || $coverTemplate['useDocxFile']) {
                                                $salutation_query = dbGetCompanyContactSalutation($emailAddress['pmos_owner']);
                                                $salutation = $salutation_query['pmcj_phone_no'];
                                                $salutation_name = $salutation_query['pmct_name'];
                                            }
                                            if ($coverTemplate['useDocxFile']) {
                                                $file = '../reports/' . $coverTemplate['letterFilename'];
                                                copy($file, $letter_filePath . '.docx');

                                                $replacer = replaceCoverLetterPlaceholderArray($emailAddress, $coverTemplate['letterTemplateBody'], $propertyName, $periodFrom, $periodTo, $salutation, $salutation_name);

                                                $docx = new CreateDocxFromTemplate($letter_filePath . '.docx');
                                                $docx->setTemplateSymbol('%');

                                                $docx->replaceVariableByText($replacer);
                                                $docx->modifyPageLayout('A4');
                                                $docx->createDocx($letter_filePath . '.docx');

                                                $docx = new CreateDocx();
                                                $docx->transformDocument($letter_filePath . '.docx', $letter_filePath, 'libreoffice', ['outdir' => 'C:\libreoffice\temp']);
                                                // $transform = new TransformDocAdvPDF($letter_filePath . ".docx");
                                                // $transform->transform($letter_filePath);

                                                while (! glob($letter_filePath)) {
                                                    sleep(5);
                                                }

                                                unlink($letter_filePath . '.docx');
                                            } else {
                                                $letter = new HTMLPage2($letter_filePath, PDF_PAGE_ORIENTATION, PDF_UNIT, PDF_PAGE_FORMAT);
                                                $letter->SetFont('helvetica', '', 8, '', true);
                                                $letter->setImageScale(1.53);
                                                $text = replaceCoverLetterPlaceholder($emailAddress, $coverTemplate['letterTemplateBody'], $propertyName, $periodFrom, $periodTo, $salutation, $salutation_name);
                                                $letter->render($text);
                                            }

                                            $fileArray = [];
                                            $fileArray[] = $letter_filePath;
                                            $fileArray[] = REPORTPATH . '/' . $pdfDownloadLink;

                                            $timestamp = date('dmYHis');
                                            $ext = '.pdf';
                                            $fullFileName = $_filePath . $fileName . $propertyID . '_' . $timestamp . '_' . $emailAddress['pmos_owner'] . $ext;
                                            mergePDF($fileArray, false, $PDFtitle, $fullFileName, '');
                                            $_attachment[0]['file'] = $fullFileName;

                                            unlink($letter_filePath);
                                            $ownerCombine[] = $fullFileName;
                                        }

                                        // attach single property document at the end
                                        if ($singleDocAttachment) {
                                            $singleDoc['file'] = $singleDocAttachment;
                                            $singleDoc['content_type'] = 'application/pdf';
                                            $_attachment[4] = $singleDoc;
                                        }

                                        if ((strpos($_attachment[0]['file'], $emailAddress['companyCode']) !== false && $view->items['ownerSharesReport'] == 1 && $view->items['emailOption'] == 0) || $view->items['ownerSharesReport'] != 1) {

                                            if ($emailAddress['emailAddress']) {
                                                $trailLetter = [
                                                    'letterTemplateID' => dbGetLetterCategoryID('Owner Report'),
                                                    'letterFormat' => 'pdf',
                                                    'letterRecipient' => $emailAddress['emailAddress'] . ($emailAddressCC ? ';' . $emailAddressCC : ''),
                                                    'letterEmailSubject' => $subject ? $subject : 'Owner Report for [' . $propertyID . '] ' . $propertyName . ' (' . $email->items['clientName'] . ')',
                                                    'letterEmailBody' => $email->toString(),
                                                    'letterTemplateBody' => '',
                                                    'propertyCode' => $propertyID,
                                                    'leaseCode' => '',
                                                    'userID' => $_SESSION['user_id'],
                                                    'dl_path' => $pdfDownloadLink,

                                                ];
                                                if ($pdfDownloadLink) {
                                                    $parameters['letter_history_id'] = dbInsertTrailLetter($trailLetter);
                                                }
                                                sendMail(
                                                    $emailAddress['emailAddress'],
                                                    '',
                                                    $email->toString(),
                                                    $subject ? $subject : 'Owner Report for [' . $propertyID . '] ' . $propertyName . ' (' . $email->items['clientName'] . ')',
                                                    $_attachment,
                                                    null,
                                                    null,
                                                    false,
                                                    $emailAddressCC,
                                                    true,
                                                    true,
                                                    $parameters
                                                );
                                            } else {
                                                $trailLetter = [
                                                    'letterTemplateID' => dbGetLetterCategoryID('Owner Report'),
                                                    'letterFormat' => 'pdf',
                                                    'letterRecipient' => $_SESSION['email'] . ($emailAddressCC ? ';' . $emailAddressCC : ''),
                                                    'letterEmailSubject' => $subject ? $subject : 'Owner Report for [' . $propertyID . '] ' . $propertyName . ' (' . $email->items['clientName'] . ')',
                                                    'letterEmailBody' => $email->toString(),
                                                    'letterTemplateBody' => '',
                                                    'propertyCode' => $propertyID,
                                                    'leaseCode' => '',
                                                    'userID' => $_SESSION['user_id'],
                                                    'dl_path' => $pdfDownloadLink,

                                                ];
                                                if ($pdfDownloadLink) {
                                                    $parameters['letter_history_id'] = dbInsertTrailLetter($trailLetter);
                                                }
                                                sendMail(
                                                    $_SESSION['email'],
                                                    '',
                                                    $email->toString(),
                                                    $subject ? $subject : 'Owner Report for [' . $propertyID . '] ' . $propertyName . ' (' . $email->items['clientName'] . ')',
                                                    $_attachment,
                                                    null,
                                                    null,
                                                    false,
                                                    $emailAddressCC,
                                                    true,
                                                    true,
                                                    $parameters
                                                );
                                            }
                                        } elseif ($view->items['ownerSharesReport'] == 1 && $view->items['emailOption'] == 1) {
                                            $ownerShareFilePMEmail[$propertyID]['email'] = $emailAddress['emailAddress'];
                                            $ownerShareFilePMEmail[$propertyID]['body'] = $email->toString();
                                            $ownerShareFilePMEmail[$propertyID]['subject'] = $subject ? $subject : 'Owner Report for [' . $propertyID . '] ' . $propertyName . ' (' . $email->items['clientName'] . ')';
                                        }
                                    }

                                    if ($includeCoverLetter) {
                                        unlink(REPORTPATH . '/' . $pdfDownloadLink);
                                        mergePDF($ownerCombine, false, $PDFtitle, REPORTPATH . '/' . $pdfDownloadLink, '');

                                        // if (count($ownerCombine) == 1)
                                        // unlink($ownerCombine[0]);
                                    }
                                } else {
                                    $view->items['statusMessage2'][] = "No e-mail address found for <strong>$propertyID</strong>.";
                                }

                            }

                            if ($includeCoverLetter && count($ownerCombine ?? []) > 0) {
                                if (glob($ownerCombine[0])) {
                                    unlink(REPORTPATH . '/' . $pdfDownloadLink);
                                    mergePDF($ownerCombine, false, $PDFtitle, REPORTPATH . '/' . $pdfDownloadLink, '');

                                    // if (count($ownerCombine) == 1)
                                    // unlink($ownerCombine[0]);
                                }
                            }
                        }
                        // else $view->items['statusMessage2'][] = "No e-mail address found for <strong>$propertyID</strong>.";

                        //	                    if(AWS_S3_ENABLED && count($s3_attachments) > 0){
                        //	                        $S3 = new S3FileHandler();
                        //	                        $file_paths = [];
                        //	                        foreach ($s3_attachments as $fp) {
                        //	                            $file_paths[] = $fp['file'];
                        //	                        }
                        //	                        $deleted = $S3->deleteTempAttachments($file_paths);
                        //	                    }

                        if ($view->items['publishToOwner']) {
                            $folder_name = date('Y', strtotime(str_replace('/', '-', $periodTo)));
                            $doc = [];
                            $doc['documentTitle'] = $view->items['description'] . ' for ' . $view->items['reportPeriod'];
                            $doc['documentDescription'] = 'Owner report for property ' . $propertyID;
                            $doc['documentType'] = DOC_OWNER;
                            $doc['createdBy'] = $_SESSION['un'];
                            $doc['primaryID'] = $propertyID;
                            $doc['secondaryID'] = '';
                            $doc['filename'] = $pdfDownloadLink;
                            $doc['publishToOwner'] = 1;
                            $doc['directory_id'] = generatePropertySystemFolderReturnId($folder_name, 'OWNER REPORTS', $propertyID);
                            $doc['period_from'] = date('Y-m-d h:i:s', strtotime(str_replace('/', '-', $periodFrom)));
                            $doc['period_to'] = date('Y-m-d h:i:s', strtotime(str_replace('/', '-', $periodTo)));
                            if (! $pdfDownloadLink) {
                                $doc['filename'] = '';
                            }

                            $newDocID = dbAddDocument($doc);
                            //
                            // COPY TO S3 AND DELETE IT
                            //
                            if ($_SESSION['s3StorageEnabled'] && $newDocID && isset($doc['filename']) && file_exists($pathPrefix . $doc['filename'])) {
                                $S3 = new S3FileHandler();
                                $copied = $S3->copyFile($newDocID, 'Documents/' . $propertyID . '/PropertyDocuments', basename($doc['filename']), true);
                            }
                            //
                            $view->items['statusMessage2'][] = 'Owners Report can now be downloaded by the owners using their own interface.';
                        }

                        if ($includeCoverLetter && ! $view->items['emailOwner']) {
                            $emailAddresses = dbGetListOwnerEmail($propertyID);
                            $manager = dbGetManagerDetail($propertyID);
                            foreach ($emailAddresses as $key => $row) {
                                $emailAddresses[$key]['propertyManagerName'] = $manager['propertyManagerName'];
                                $emailAddresses[$key]['propertyManagerTitle'] = $manager['propertyManagerTitle'];
                                $emailAddresses[$key]['propertyManagerEmail'] = $manager['propertyManagerEmail'];
                                $emailAddresses[$key]['propertyManagerMobileNumber'] = $manager['propertyManagerMobileNumber'];
                            }

                            $ownerCombine = [];
                            foreach ($emailAddresses as $xx => $emailAddress) {
                                $letter_filePath = $_filePath . '/' . time() . $xx . '_' . $emailAddress['pmos_prop'] . '_' . $emailAddress['pmos_owner'] . '.pdf';
                                $salutation = '';
                                $salutation_name = '';
                                if (strpos($coverTemplate['letterTemplateBody'], '%salutation%') !== false || strpos($coverTemplate['letterTemplateBody'], '%contactName%') !== false || $coverTemplate['useDocxFile']) {
                                    $salutation_query = dbGetCompanyContactSalutation($emailAddress['pmos_owner']);
                                    $salutation = $salutation_query['pmcj_phone_no'];
                                    $salutation_name = $salutation_query['pmct_name'];
                                }
                                if ($coverTemplate['useDocxFile']) {
                                    $file = '../reports/' . $coverTemplate['letterFilename'];
                                    copy($file, $letter_filePath . '.docx');

                                    $replacer = replaceCoverLetterPlaceholderArray($emailAddress, $coverTemplate['letterTemplateBody'], $propertyName, $periodFrom, $periodTo, $salutation, $salutation_name);

                                    $docx = new CreateDocxFromTemplate($letter_filePath . '.docx');
                                    $docx->setTemplateSymbol('%');

                                    $docx->replaceVariableByText($replacer);
                                    $docx->modifyPageLayout('A4');
                                    $docx->createDocx($letter_filePath . '.docx');

                                    $docx = new CreateDocx();
                                    $docx->transformDocument($letter_filePath . '.docx', $letter_filePath, 'libreoffice', ['outdir' => 'C:\libreoffice\temp']);
                                    //                            $transform = new TransformDocAdvPDF($letter_filePath . ".docx");
                                    //                          $transform->transform($letter_filePath);

                                    while (! glob($letter_filePath)) {
                                        sleep(5);
                                    }

                                    unlink($letter_filePath . '.docx');
                                } else {
                                    $letter = new HTMLPage2($letter_filePath, PDF_PAGE_ORIENTATION, PDF_UNIT, PDF_PAGE_FORMAT);
                                    $letter->SetFont('helvetica', '', 8, '', true);
                                    $letter->setImageScale(1.53);
                                    $text = replaceCoverLetterPlaceholder($emailAddress, $coverTemplate['letterTemplateBody'], $propertyName, $periodFrom, $periodTo, $salutation, $salutation_name);
                                    $letter->render($text);
                                }


                                $fileArray = [];
                                $fileArray[] = $letter_filePath;
                                $fileArray[] = REPORTPATH . '/' . $pdfDownloadLink;

                                $timestamp = date('dmYHis');
                                $ext = '.pdf';
                                $fullFileName = $_filePath . $fileName . $propertyID . '_' . $timestamp . $xx . '_' . $emailAddress['pmos_owner'] . $ext;
                                mergePDF($fileArray, false, $PDFtitle, $fullFileName, '');

                                unlink($letter_filePath);
                                $ownerCombine[] = $fullFileName;
                            }

                            if (count($ownerCombine ?? []) > 0) {
                                unlink(REPORTPATH . '/' . $pdfDownloadLink);
                                mergePDF($ownerCombine, false, $PDFtitle, REPORTPATH . '/' . $pdfDownloadLink, '');
                                $propertyCombine[] = REPORTPATH . '/' . $pdfDownloadLink;
                            }

                            foreach ($ownerCombine as $file) {
                                unlink($file);
                            }
                        }

                        // UPDATE REPORTING PAGE
                        $reportingCurrPage++;
                    }
                }// END FOR EACH
            } else {
                $page = 0;
                $subReports = extractID($view->items, 'sr');
                $includes = dbGetSubReports($reportType, true);

                foreach ($includes as $inc) {
                    // -- if the page has been selected for inclusion - run it
                    if ($subReports[$inc['subReportID']]) {
                        if ($inc['subReportFile']) {
                            include $inc['subReportFile'];
                        }
                    }
                }

                $s3_attachments = [];
                if ($view->items['withApInvoices'] || $view->items['withAgentInvoices']) {
                    $zip_file_name = 'owner_report_attachments' . date('YmdHis') . ($zipCounter++) . '.zip';
                    $zip_folder = "{$pathPrefix}{$clientDirectory}/ap_zip";
                    if (! file_exists($zip_folder)) {
                        mkdir($zip_folder, FILE_PERMISSION, true);
                    }

                    $zip_ap = new ZipArchive();

                    $filePath = "$zip_folder/" . $zip_file_name;

                    $downloadPath = "{$clientDirectory}/ap_zip/" . $zip_file_name;

                    if (file_exists($filePath)) {
                        unlink($filePath);
                    }

                    if ($zip_ap->open($filePath, ZipArchive::CREATE) !== true) {
                        exit("cannot open <$filePath>\n");
                    }
                    $zip_count = 0;
                    foreach ($properties as $ownerReportIndex => $propertyID) {
                        if ($report['reportPeriod'] == 'D' or $view->items['paymentDate'] != '') {
                            if ($view->items['datasource'] == DS_PAYDATE or $view->items['paymentDate'] != '') {
                                $periodTo = ($view->items['paymentDate'] == '' ? $view->items['filterProperty'] : $view->items['paymentDate']);
                                $periodFrom = dbGetNextDayOfPreviousOwnerPaymentDate($propertyID, $periodTo);
                            } else {
                                $periodFrom = $view->items['reportingPeriodFrom'];
                                $periodTo = $view->items['reportingPeriodTo'];
                            }

                            $previousPeriodTo = oneDayBefore($periodFrom);
                            $periodFrom_display = toDisplayDate($periodFrom);
                            $periodTo_display = toDisplayDate($periodTo);
                            $startFinancialYear = $periodFrom;
                            $endFinancialYear = $periodTo;
                            $reportingPeriodToPY = oneDayBefore($periodFrom);
                            $financialPeriodToPY = oneDayBefore($periodFrom);

                            $calendar = dbGetPeriod($propertyID, $periodFrom);
                            $calendar['startDate'] = $periodFrom;
                            $calendar['endDate'] = $periodTo;
                        } else {
                            $previousPeriodTo = oneDayBefore($periodFrom);
                            $periodFrom_display = toDisplayDate($periodFrom);
                            $periodTo_display = toDisplayDate($periodTo);
                            $calendar = dbGetPeriod($propertyID, $periodFrom);


                            $currentPeriod = $calendar['period'];

                            $calendarTo = dbGetPeriod($propertyID, $periodTo); // added by Raymond 28/1/11

                            $toPeriod = $calendarTo['period']; // added by Raymond 28/1/11
                            $currentYear = $calendar['year'];
                            [$previousPeriod, $previousYear] = periodBefore($currentPeriod, $currentYear);

                            $previousFinancialYear = $currentYear - 1;
                            $financialYearPeriod = dbGetPeriodDate($propertyID, 1, $currentYear);

                            $startFinancialYear = $financialYearPeriod['startDate'];
                            $endFinancialYear = $financialYearPeriod['endDate'];

                            $dateParts = explode('/', $endFinancialYear);
                            $startReportingYear = "01/{$dateParts[DATE_MONTH]}/{$dateParts[DATE_YEAR]}";

                            $reportingPeriodToPY = oneDayBefore($startReportingYear);
                            $financialPeriodToPY = oneDayBefore($startFinancialYear);
                        }
                        // ########################################################
                        // WITH APINVOICES
                        if ($view->items['withApInvoices']) {
                            // $startDate = $calendar['startDate'];
                            // $endDate = $calendar['endDate'];
                            $startDate = $periodFrom;
                            $endDate = $periodTo;

                            $param = [
                                'startDate' => $startDate,
                                'endDate' => $endDate,
                                'period' => $currentPeriod,
                                'year' => $currentYear,
                                'type' => $view->items['withApCashNAccruals'],
                                'propertyID' => $propertyID,
                            ];

                            if ($view->items['withApCashNAccruals'] == 1) {
                                $getAttachment = dbGetOwnerBatchNumberAccruals($startDate, $endDate, $propertyID);
                            } else {
                                $getAttachment = getAPPaidByBatch($param);
                            }
                            $batchWithAttachment = dbGetInvoiceFileByBatchNumber($getAttachment, $propertyID);

                            if (count($batchWithAttachment ?? []) > 0) {
                                foreach ($getAttachment as $getAttachmentDataBatchNr) {
                                    $batch = $getAttachmentDataBatchNr;
                                    $getInvoiceFiles = dbGetInvoiceFileByBatchNumber($batch, $propertyID);
                                    foreach ($getInvoiceFiles as $getInvoiceFilesData) {
                                        // if(AWS_S3_ENABLED && isset($getInvoiceFilesData['on_s3']) && (bool)$getInvoiceFilesData['on_s3'] && isset($getInvoiceFilesData['documentID']) ){
                                        // 	$file4zip = $S3->setTempAttachments($getInvoiceFilesData['documentID'],true);
                                        // 	if(isset($file4zip['file']) && file_exists($file4zip['file']) ){
                                        // 		$zip_ap->addFile($file4zip['file'], $file4zip['name']);
                                        // 		$s3_attachments[] = $file4zip;
                                        // 	}
                                        // }
                                        // else{
                                        $attachmentFileName = $getInvoiceFilesData['filename'];
                                        if (file_exists(REPORTPATH . '/' . $attachmentFileName)) {
                                            $zip_ap->addFile(REPORTPATH . '/' . $attachmentFileName, str_replace("{$clientDirectory}/pdf/SupplierInvoices/", '', $attachmentFileName));
                                            $zip_count++;
                                        }
                                        //			                                	}
                                    }
                                }
                            }
                        }
                        // ########################################################
                        // WITH AGENTINVOICES
                        if ($view->items['withAgentInvoices']) {
                            $agentInvoiceFileList = fetchAgentInvoices(1, $periodFrom, $periodTo, $propertyID);
                            if (count($agentInvoiceFileList ?? []) > 0) {
                                foreach ($agentInvoiceFileList as $agentInvoiceFile) {
                                    $attachmentFile = $agentInvoiceFile['downloadPath'];
                                    $attachmentFileName = $agentInvoiceFile['fileName'];
                                    if (file_exists($attachmentFile)) {
                                        $zip_ap->addFile($attachmentFile, $attachmentFileName);
                                        $zip_count++;
                                    }
                                }
                                //			                            if(AWS_S3_ENABLED){
                                //			                            	$inv_docs = dbGetDocuments(DOC_INV_TENANT,$propertyID,'',0, null, $periodFrom,$periodTo);
                                //			                            	foreach ($inv_docs as $idc) {
                                //			                            	   $file4zip = $S3->setTempAttachments($idc['documentID'],true);
                                //			                            	   if(isset($file4zip['file']) && file_exists($file4zip['file']) ){
                                //			                            	   	$zip_ap->addFile($file4zip['file'], $file4zip['name']);
                                //			                            	   	$s3_attachments[] = $file4zip;
                                //			                            	   }
                                //			                            	}
                                //			                            }
                            }
                        }
                        // ########################################################
                    }
                    if ($zip_count > 0) {
                        renderDownloadLink($downloadPath);
                    } else {
                        echo '<div class="infoBox">No zip file generated</div>';
                    }
                    $zip_ap->close();
                }
            }// END OF COMBINED REPORT IF
            // ###########################################
            //
            if (! $individualPDF) {
                if ($view->items['format'] == FILETYPE_XLS) {
                } else {
                    $attachment[0]['file'] = REPORTPATH . '/' . $pdfDownloadLink;
                    $attachment[0]['content_type'] = 'application/pdf';

                    if (! $includeCoverLetter) {
                        closePDF($pdfCounter);
                    } else {
                        $fullFileName = $fileName . $timestamp . $ext;
                        $fullPath = $_filePath . $fullFileName;
                        mergePDF($propertyCombine, false, $PDFtitle, $fullPath, '');
                        foreach ($propertyCombine as $file) {
                            unlink($file);
                        }

                        if ($context[IS_TASK]) {
                            $attachment[0]['file'] = $fullPath;
                        } else {
                            rename($fullPath, REPORTPATH . '/' . $pdfDownloadLink);
                        }
                    }
                }

                if (count($CombineMultixlsDownloadPath ?? []) > 0) {
                    $zip_folder = "{$pathPrefix}{$clientDirectory}/xlsx/owner_report";
                    if (! file_exists($zip_folder)) {
                        mkdir($zip_folder, FILE_PERMISSION, true);
                    }
                    $zip = new ZipArchive();
                    $filePath = "$zip_folder/owner_report" . date('YmdHis') . ($zipCounter++) . '.zip';
                    if (file_exists($filePath)) {
                        unlink($filePath);
                    }
                    if ($zip->open($filePath, ZipArchive::CREATE) !== true) {
                        exit("cannot open <$filePath>\n");
                    }
                    foreach ($CombineMultixlsDownloadPath as $fileOwner) {
                        $attachmentFile = $fileOwner[0];
                        $attachmentFileName = $fileOwner[1];
                        if (file_exists($attachmentFile)) {
                            $zip->addFile($attachmentFile, $attachmentFileName);
                        }
                    }
                    $zip->close();
                    if (file_exists($filePath)) {
                        $attachment[4]['file'] = $filePath;
                        $attachment[4]['content_type'] = 'application/zip';
                    }
                }


                if (count($accrualsExcelAttachments ?? []) > 0) {
                    $zip_folder = "{$pathPrefix}{$clientDirectory}/accruals_tb";
                    if (! file_exists($zip_folder)) {
                        mkdir($zip_folder, FILE_PERMISSION, true);
                    }
                    $zip = new ZipArchive();
                    $filePath = "$zip_folder/accruals_tb_" . date('YmdHis') . ($zipCounter++) . '.zip';
                    if (file_exists($filePath)) {
                        unlink($filePath);
                    }
                    if ($zip->open($filePath, ZipArchive::CREATE) !== true) {
                        exit("cannot open <$filePath>\n");
                    }
                    foreach ($accrualsExcelAttachments as $accrualsExcelAttachment) {
                        $attachmentFile = $accrualsExcelAttachment[0];
                        $attachmentFileName = $accrualsExcelAttachment[1];
                        if (file_exists($attachmentFile)) {
                            $zip->addFile($attachmentFile, $attachmentFileName);
                        }
                    }
                    $zip->close();
                    if (file_exists($filePath)) {
                        $attachment[1]['file'] = $filePath;
                        $attachment[1]['content_type'] = 'application/zip';
                    }
                }

                if (count($expenditureExcelAttachments ?? []) > 0) {
                    $zip_folder = "{$pathPrefix}{$clientDirectory}/xlsx/Detailed Expenditure";
                    if (! file_exists($zip_folder)) {
                        mkdir($zip_folder, FILE_PERMISSION, true);
                    }
                    $zip = new ZipArchive();
                    $filePath = "$zip_folder/detailed_expenditure" . date('YmdHis') . ($zipCounter++) . '.zip';
                    if (file_exists($filePath)) {
                        unlink($filePath);
                    }
                    if ($zip->open($filePath, ZipArchive::CREATE) !== true) {
                        exit("cannot open <$filePath>\n");
                    }
                    foreach ($expenditureExcelAttachments as $expendituresExcelAttachment) {
                        $attachmentFile = $expendituresExcelAttachment[0];
                        $attachmentFileName = $expendituresExcelAttachment[1];
                        if (file_exists($attachmentFile)) {
                            $zip->addFile($attachmentFile, $attachmentFileName);
                        }
                    }
                    $zip->close();
                    if (file_exists($filePath)) {
                        $attachment[1]['file'] = $filePath;
                        $attachment[1]['content_type'] = 'application/zip';
                    }
                }

                // FOR SUB REPORT OWNER STATEMENT EXCEL
                if (count($ownerStatementExcelAttachments ?? []) > 0) {
                    $zip_folder = "{$pathPrefix}{$clientDirectory}/xlsx/" . DOC_OWNERSTATEMENT;
                    if (! file_exists($zip_folder)) {
                        mkdir($zip_folder, FILE_PERMISSION, true);
                    }
                    $zip = new ZipArchive();
                    $filePath = "$zip_folder/owner_statement_excel" . date('YmdHis') . ($zipCounter++) . '.zip';
                    if (file_exists($filePath)) {
                        unlink($filePath);
                    }
                    if ($zip->open($filePath, ZipArchive::CREATE) !== true) {
                        exit("cannot open <$filePath>\n");
                    }
                    foreach ($ownerStatementExcelAttachments as $ownerStatementExcelAttachment) {
                        $attachmentFile = $ownerStatementExcelAttachment[0];
                        $attachmentFileName = $ownerStatementExcelAttachment[1];
                        if (file_exists($attachmentFile)) {
                            $zip->addFile($attachmentFile, $attachmentFileName);
                        }
                    }
                    $zip->close();
                    if (file_exists($filePath)) {
                        $attachment[1]['file'] = $filePath;
                        $attachment[1]['content_type'] = 'application/zip';
                    }
                }
            }
            // END INDIVIDUAL PDF
            // remove jasper report pdf that already merge in owners report
            foreach ($jasperInclude as $file) {
                if (file_exists($file)) {
                    unlink($file);
                }
            }

            if ($view->items['emailOwner'] && $view->items['isScheduled']) {

                // FOR SUB REPORT OWNER STATEMENT EXCEL
                if (count($ownerStatementExcelAttachments ?? []) > 0) {
                    $_count = count($attachment ?? []) + 1;
                    $zip_folder = "{$pathPrefix}{$clientDirectory}/xlsx/" . DOC_OWNERSTATEMENT;
                    if (! file_exists($zip_folder)) {
                        mkdir($zip_folder, FILE_PERMISSION, true);
                    }
                    $zip = new ZipArchive();
                    $filePath = "$zip_folder/owner_statement_excel" . date('YmdHis') . ($zipCounter++) . '.zip';
                    if (file_exists($filePath)) {
                        unlink($filePath);
                    }
                    if ($zip->open($filePath, ZipArchive::CREATE) !== true) {
                        exit("cannot open <$filePath>\n");
                    }
                    foreach ($ownerStatementExcelAttachments as $ownerStatementExcelAttachment) {
                        $attachmentFile = $ownerStatementExcelAttachment[0];
                        $attachmentFileName = $ownerStatementExcelAttachment[1];
                        if (file_exists($attachmentFile)) {
                            $zip->addFile($attachmentFile, $attachmentFileName);
                        }
                    }
                    $zip->close();
                    if (file_exists($filePath)) {
                        $attachment[$_count]['file'] = $filePath;
                        $attachment[$_count]['content_type'] = 'application/zip';
                    }
                }
            }
            // END JASPER

            if ($view->items['emailOwner'] && $view->items['isScheduled']) {
                // SENDING TO OWNER DETAILED EXPENDITURE
                if (count($expenditureExcelAttachments ?? []) > 0) {
                    $_count = count($attachment ?? []) + 1;
                    $zip_folder = "{$pathPrefix}{$clientDirectory}/xlsx/Detailed Expenditure";
                    if (! file_exists($zip_folder)) {
                        mkdir($zip_folder, FILE_PERMISSION, true);
                    }
                    $zip = new ZipArchive();
                    $filePath = "$zip_folder/detailed_expenditure" . date('YmdHis') . ($zipCounter++) . '.zip';
                    if (file_exists($filePath)) {
                        unlink($filePath);
                    }
                    if ($zip->open($filePath, ZipArchive::CREATE) !== true) {
                        exit("cannot open <$filePath>\n");
                    }
                    foreach ($expenditureExcelAttachments as $expendituresExcelAttachment) {
                        $attachmentFile = $expendituresExcelAttachment[0];
                        $attachmentFileName = $expendituresExcelAttachment[1];
                        if (file_exists($attachmentFile)) {
                            $zip->addFile($attachmentFile, $attachmentFileName);
                        }
                    }
                    $zip->close();
                    if (file_exists($filePath)) {
                        $attachment[$_count]['file'] = $filePath;
                        $attachment[$_count]['content_type'] = 'application/zip';
                    }
                }

                if (count($CombineMultixlsDownloadPath ?? []) > 0) {
                    $zip_folder = "{$pathPrefix}{$clientDirectory}/xlsx/owner_report";
                    if (! file_exists($zip_folder)) {
                        mkdir($zip_folder, FILE_PERMISSION, true);
                    }
                    $zip = new ZipArchive();
                    $filePath = "$zip_folder/owner_report" . date('YmdHis') . ($zipCounter++) . '.zip';
                    if (file_exists($filePath)) {
                        unlink($filePath);
                    }
                    if ($zip->open($filePath, ZipArchive::CREATE) !== true) {
                        exit("cannot open <$filePath>\n");
                    }
                    foreach ($CombineMultixlsDownloadPath as $fileOwner) {
                        $attachmentFile = $fileOwner[0];
                        $attachmentFileName = $fileOwner[1];
                        if (file_exists($attachmentFile)) {
                            $zip->addFile($attachmentFile, $attachmentFileName);
                        }
                    }
                    $zip->close();
                    if (file_exists($filePath)) {
                        $attachment[4]['file'] = $filePath;
                        $attachment[4]['content_type'] = 'application/zip';
                    }
                }
            }

            if ($view->items['emailOwner']) {
                if ($view->items['ownerSharesReport'] == 1 && $view->items['emailOption'] == 1) {
                    foreach ($ownerShareFilePMEmail as $key => $emailInfo) {
                        if ($emailInfo['email']) {
                            sendMail(
                                $emailInfo['email'],
                                '',
                                $emailInfo['body'],
                                $emailInfo['subject'],
                                $ownerShareFilePM[$key],
                                null,
                                null,
                                false,
                                $emailAddressCC,
                                true,
                                true,
                                $parameters
                            );
                        }
                    }

                }
            }

            // -- if it s a scheduled task - attach the report and email to the requester
            if ($context[IS_TASK]) {
                $email = new EmailTemplate('views/emails/basicEmail.html', SYSTEMURL);
                sendMail(
                    $_SESSION['email'],
                    $_SESSION['first_name'] . ' ' . $_SESSION['last_name'],
                    $email->toString(),
                    'Owner Report',
                    (! empty($ownerShareFile) ? $ownerShareFile : $attachment),
                    null,
                    null,
                    false,
                    false,
                    true,
                    true,
                    $parameters
                );
                logData('Emailed owner report to ' . $_SESSION['email']);
                $context[TASK_COMPLETE] = true;
            } else {
                $view->render();
            }

            if ($pdfDownloadLink) {
                renderDownloadLink($pdfDownloadLink);
            }
        } // -- end threshold check

        if (! $forceGenerate && $propertyCount > THRESHOLD_OWNERREPORT) {
            $view->items['statusMessage'] = 'Due to its complexity, this report has been scheduled and will be emailed to you on completion!';
            $view->render();
        }
    } catch (PDFlibException $e) {
        if ($e->getCode() == 2106) {
            $validationErrors[] = 'No Owners Report to print';
            $view->items['validationErrors'] = $validationErrors;
            $view->render();
        } else {


            if (ENVIRONMENT == 'LIVE') {
                myErrorHandlerV2(1, $e->getMessage(), $e->getFile(), $e->getLine());
            } else {
                $msg = 'PDFLIB' . PHP_EOL;
                $msg .= 'code : ' . $e->getCode() . PHP_EOL;
                $msg .= 'file : ' . $e->getFile() . 'line : ' . $e->getLine() . PHP_EOL;
                $msg .= 'mess : ' . $e->getMessage() . PHP_EOL;
                $msg .= 'detl : ' . $e . PHP_EOL;
                $msg .= 'PDFLIB';
                pre_print_r($msg);
            }

        }
    } catch (Exception $e) {
        $msg = 'Exception' . PHP_EOL;
        $msg .= 'code : ' . $e->getCode() . PHP_EOL;
        $msg .= 'file : ' . $e->getFile() . 'line : ' . $e->getLine() . PHP_EOL;
        $msg .= 'mess : ' . $e->getMessage() . PHP_EOL;
        $msg .= 'detl : ' . $e . PHP_EOL;
        $msg .= 'Exception';
        sendMail('<EMAIL>', 'Andrew Erkins', $msg, 'Scheduler Failed');
    }
}

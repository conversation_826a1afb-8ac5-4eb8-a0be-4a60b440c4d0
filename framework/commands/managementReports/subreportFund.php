<?php


include_once __DIR__ . '/functions/page1PropertyReportFunctions.php';
include_once __DIR__ . '/functions/dbPropertyFundInterface.php';
include __DIR__ . '/functions/fundSummary.php';


$pdf->begin_page_ext(842, 595, '');

$page_header = 'Property Financial Report';
$page++;

include __DIR__ . '/functions/mediumReportHeader.php';

if ($logo) {
    generateLogo('landscape');
}

$text1 = 'Cash Receipts';

$pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
$pdf->showBoxed($text1, 22, 425, 275, 30, 'left', '');


// CASH RECEIPTS FIGURES
// ACTUAL
// ---------------------------------------------------------------------------
// This Period Actual
//

$ownerincome = 0;
$voinc = 0;
$recinc = 0;

$ownerincomeA = 0;
$voincA = 0;
$recincA = 0;

$withFund = true;

// #########################OWNER INCOME CURRENT PERIOD#####################################


$ownerinc_result = dbTotalIncomePerGroupExFund($propertyID, $periodFrom, $periodTo, 'INCOWN');
$owner_income = $ownerinc_result['net_amount'];
$ownerincomePOS = $owner_income;


$recinc = dbTotalIncomePerGroupExFund($propertyID, $periodFrom, $periodTo, 'INCDR');
$recincPOS = $recinc['net_amount'];

$voinc = dbTotalIncomePerGroupExFund($propertyID, $periodFrom, $periodTo, 'INCVO');
$voincPOS = $voinc['net_amount'];

$ownerincome += $owner_income;
// $ownerincomePOS = $ownerincome*(-1);

$totalAllIncomePOS = $ownerincomePOS + $voincPOS + $recincPOS;
$totalAllIncome1 = $totalAllIncomePOS;

// DISPLAY FIGURES
$ownerincome_display = formatting($ownerincomePOS, 2);
$ownerincome_display_rec = $ownerincome_display;
$voinc_display = formatting($voincPOS, 2);
// print "voincPOS $voincPOS<p>";
$voinc_display_rec = $voinc_display;


// echo "$totalAllIncomePOS <br />";


$recinc_display = formatting($recincPOS, 2);
$recinc_display_rec = $recinc_display;
// print "<font color=blue>recinc_display_rec = $recinc_display</font><p>";
$totalAllIncome_display = formatting($totalAllIncomePOS, 2);
$totalAllIncome_display_rec = $totalAllIncome_display;

// ##############################  PERIODS  ##########################################
// Retrieve periods
$periodsSQL = '
	SELECT
		pmcp_period, pmcp_year
	FROM
		pmcp_prop_cal
	WHERE
		pmcp_start_dt BETWEEN CONVERT(datetime, ?, 103) AND CONVERT(datetime, ?, 103)
		AND pmcp_end_dt BETWEEN CONVERT(datetime, ?, 103) AND CONVERT(datetime, ?, 103)
		AND pmcp_prop = ?';
$dbh->selectDatabase($clientDB);
$periods = $dbh->executeSet(
    $periodsSQL,
    false,
    true,
    [$periodFrom, $periodTo, $periodFrom, $periodTo, $propertyID]
);
$pmcp_period = null;
$pmcp_year = null;
foreach ($periods as $v) {
    $pmcp_period[] = $v['pmcp_period'];
    $pmcp_year[] = $v['pmcp_year'];
}

$pmcp_period = array_unique($pmcp_period);
$pmcp_year = array_unique($pmcp_year);

// ##############################BUDGET INCOME Current Period##########################################
// -----------BUDGET FIGURES ---------------------------------
$params = [];
$budgetINCOWNSQL = '
	SELECT
		COALESCE(SUM(pmrp_b_c_amt), 0) as amount
	FROM
		pmrp_b_rev_per
	WHERE
		pmrp_prop = ' . addSQLParam($params, $propertyID) . '
		AND pmrp_per IN (' . addSQLParam($params, $pmcp_period) . ') 
		AND pmrp_year IN (' . addSQLParam($params, $pmcp_year) . ") 
		AND pmrp_acc IN (SELECT pmcg_acc FROM pmcg_chart_grp WHERE pmcg_grp = 'TRACC2' AND pmcg_subgrp = 'INCOWN')";
// echo "Second BUDGET:" . $budgetINCOWNSQL;
$dbh->selectDatabase($clientDB);
$budgetINCOWN = $dbh->executeScalar($budgetINCOWNSQL, $params);

$budgetINCOWN_display = formatting($budgetINCOWN, 2);
$budgetINCOWN_display_rec = $budgetINCOWN_display;
$params = [];
$budgetINCRECSQL = 'SELECT COALESCE(SUM(pmrp_b_c_amt), 0) AS amount
		FROM pmrp_b_rev_per
		WHERE (pmrp_prop = ' . addSQLParam($params, $propertyID) . ') 
		AND (pmrp_per IN (' . addSQLParam($params, $pmcp_period) . ')) 
				AND (pmrp_year IN (' . addSQLParam($params, $pmcp_year) . ")) 
				AND pmrp_acc IN (SELECT pmcg_acc FROM pmcg_chart_grp WHERE pmcg_grp = 'TRACC2' AND pmcg_subgrp = 'INCDR')";
// echo "6th BUDGET:" . $budgetINCRECSQL;
$dbh->selectDatabase($clientDB);
$budgetINCDR = $dbh->executeScalar($budgetINCRECSQL, $params);

$budgetINCDR_display = formatting($budgetINCDR, 2);
$budgetINCDR_display_rec = $budgetINCDR_display;
$params = [];
$budgetINCVOSQL = 'SELECT COALESCE(SUM(pmrp_b_c_amt), 0) AS amount
		FROM pmrp_b_rev_per
		WHERE (pmrp_prop = ' . addSQLParam($params, $propertyID) . ') 
		AND (pmrp_per IN (' . addSQLParam($params, $pmcp_period) . ')) 
				AND (pmrp_year IN (' . addSQLParam($params, $pmcp_year) . ")) 
				AND pmrp_acc IN (SELECT pmcg_acc FROM pmcg_chart_grp WHERE pmcg_grp = 'TRACC2' AND pmcg_subgrp = 'INCVO')";
// echo "9th BUDGET:" . $budgetINCVOSQL;
$dbh->selectDatabase($clientDB);
$budgetINCVO = $dbh->executeScalar($budgetINCVOSQL, $params);

$budgetINCVO_display = formatting($budgetINCVO, 2);
$budgetINCVO_display_rec = $budgetINCVO_display;


// ############################Variance of actual to budget ####################################

// VARIANCE
$incOWNVar = $ownerincomePOS - $budgetINCOWN;
$incDRVar = $recincPOS - $budgetINCDR;
$voincVar = $voincPOS - $budgetINCVO;

// DISPLAY VARIANCE
$incOWNVar_display = formatting($incOWNVar, 2);
$incOWNVar_display_rec = $incOWNVar_display;
$incDRVar_display = formatting($incDRVar, 2);
$incDRVar_display_rec = $incDRVar_display;
$voincVar_display = formatting($voincVar, 2);
$voincVar_display_rec = $voincVar_display;

// VAR %

if ($budgetINCOWN == 0) {
    if ($incOWNVar == 0) {
        $incOWNVP = 0;
    } elseif ($incOWNVar < 0) {
        $incOWNVP = -100;
    } else {
        $incOWNVP = 100;
    }
} elseif ($incOWNVar == 0) {
    $incOWNVP = 0;
} else {
    $incOWNVP = $incOWNVar / $budgetINCOWN * 100;
}

if ($budgetINCDR == 0) {
    if ($incDRVar == 0) {
        $incDRVP = 0;
    } elseif ($incDRVar < 0) {
        $incDRVP = -100;
    } else {
        $incDRVP = 100;
    }
} elseif ($incDRVar == 0) {
    $incDRVP = 0;
} else {
    $incDRVP = $incDRVar / $budgetINCDR * 100;
}


if ($budgetINCVO == 0) {
    if ($voincVar == 0) {
        $incVOVP = 0;
    } elseif ($voincVar < 0) {
        $incVOVP = -100;
    } else {
        $incVOVP = 100;
    }
} elseif ($voincVar == 0) {
    $incVOVP = 0;
} else {
    $incVOVP = $voincVar / $budgetINCVO * 100;
}

// #####################################VARIANCE CURRENT PERIOD ########################################################

// DISPLAY VAR %
if ($incOWNVP <= 1000 && $incOWNVP >= -1000) {
    $incOWNVP_display = formatting($incOWNVP, 0);
} elseif ($incOWNVP < -1000) {
    $incOWNVP_display = '<(1,000.00)';
} elseif ($incOWNVP > 1000) {
    $incOWNVP_display = '<(1,000.00)';
}

if ($incDRVP <= 1000 && $incDRVP >= -1000) {
    $incDRVP_display = formatting($incDRVP, 0);
} elseif ($incDRVP < -1000) {
    $incDRVP_display = '<(1,000.00)';
} elseif ($incDRVP > 1000) {
    $incDRVP_display = '<(1,000.00)';
}

if ($incVOVP <= 1000 && $incVOVP >= -1000) {
    $incVOVP_display = formatting($incVOVP, 0);
} elseif ($incVOVP < -1000) {
    $incVOVP_display = '<(1,000.00)';
} elseif ($incVOVP > 1000) {
    $incVOVP_display = '<(1,000.00)';
}

$incOWNVP_display_rec = $incOWNVP_display;
$incDRVP_display_rec = $incDRVP_display;
$incVOVP_display_rec = $incVOVP_display;


// TOTALS DISPLAY
$totalAllBudgetIncome = $budgetINCOWN + $budgetINCDR + $budgetINCVO;
$totalAllBudgetIncome1 = $totalAllBudgetIncome;
$totalAllBudgetIncome_display = formatting($totalAllBudgetIncome, 2);
$totalAllBudgetIncome_display_rec = $totalAllBudgetIncome_display;
$totalAllVariance = $totalAllIncomePOS - $totalAllBudgetIncome;
$totalAllVarianceIncome = $totalAllVariance;
$totalAllVariance_display = formatting($totalAllVariance, 2);
$totalAllVariance_display_rec = $totalAllVariance_display;


if ($totalAllBudgetIncome == 0) {
    if ($totalAllVariance == 0) {
        $totalAllVP = 0;
    } elseif ($totalAllVariance < 0) {
        $totalAllVP = -100;
    } else {
        $totalAllVP = 100;
    }
} elseif ($totalAllVariance == 0) {
    $totalAllVP = 0;
} else {
    $totalAllVP = $totalAllVariance / $totalAllBudgetIncome * 100;
}


// $totalAllVP_display = formatting($totalAllVP,0);
if ($totalAllVP <= 1000 && $totalAllVP >= -1000) {
    $totalAllVP_display = formatting($totalAllVP, 0);
} elseif ($totalAllVP < -1000) {
    $totalAllVP_display = '<(1,000.00)';
} elseif ($totalAllVP > 1000) {
    $totalAllVP_display = '<(1,000.00)';
}

$totalAllVP_display_rec = $totalAllVP_display;
$totalAllVP_display1 = $totalAllVP_display;

// $pdf->show_xy("Actual: $budgetINCOWN_display", 22, 500);
// BUDGET FIGURES DISPLAY

$totalAllVarianceRECEIPTS_display = $totalAllVariance_display;

// #####################################ACTUAL INCOME YTD ########################################################

$ownerincome = 0;
$voinc = 0;
$recinc = 0;
$ownerincomeA = 0;
$voincA = 0;
$recincA = 0;

$ownerincomeA = dbTotalIncomePerGroupExFund($propertyID, $startFinancialYear, $periodTo, 'INCOWN');
$own_takeon = take_on_balance($propertyID, 'INCOWN', $startFinancialYear, $periodTo);

$ownerincomeAPOS = $ownerincomeA['net_amount'] + $own_takeon['amount'];


$voincA = dbTotalIncomePerGroupExFund($propertyID, $startFinancialYear, $periodTo, 'INCVO');
$voincomeA = $voincA['net_amount'];
$vo_takeon = take_on_balance($propertyID, 'INCVO', $startFinancialYear, $periodTo);
$voincAPOS = $voincomeA + $vo_takeon['amount'];
$recincA = dbTotalIncomePerGroupExFund($propertyID, $startFinancialYear, $periodTo, 'INCDR');
$recincomeA = $recincA['net_amount'];
$rec_takeon = take_on_balance($propertyID, 'INCDR', $startFinancialYear, $periodTo);
$recincAPOS = @($recincomeA + $rec_takeon['amount']);

$totalAllIncomeAPOS = $ownerincomeAPOS + $voincAPOS + $recincAPOS;

// Formatting /
$ownerincomeA_display = formatting($ownerincomeAPOS, 2);
$voincA_display = formatting($voincAPOS, 2);
$recincA_display = formatting($recincAPOS, 2);
$totalAllIncomeA_display = formatting($totalAllIncomeAPOS, 2);


// ################################################################################

// ################################################################################
// ################################################################################
// ###########################BUDGET INCOME YTD####################################
// ################################################################################

$budgetINCOWN = getBudgetIncYTD($propertyID, $periodFrom, $toPeriod, $periodTo, 'INCOWN');
// echo $budgetINCOWN;
$budgetINCOWN_display = formatting($budgetINCOWN, 2);

$budgetINCDR = getBudgetIncYTD($propertyID, $periodFrom, $toPeriod, $periodTo, 'INCDR');
$budgetINCDR_display = formatting($budgetINCDR, 2);

$budgetINCVO = getBudgetIncYTD($propertyID, $periodFrom, $toPeriod, $periodTo, 'INCVO');
$budgetINCVO_display = formatting($budgetINCVO, 2);


// echo "$budgetINCOWN  - $budgetINCDR  - $budgetINCVO  <br />";

// ###############################VARIANCE OF INCOME YTD ######################################

// VARIANCE
$incOWNVar = $ownerincomeAPOS - $budgetINCOWN;
$incDRVar = $recincAPOS - $budgetINCDR;
$voincVar = $voincAPOS - $budgetINCVO;

// DISPLAY VARIANCE
$incOWNVar_display = formatting($incOWNVar, 2);
$incDRVar_display = formatting($incDRVar, 2);
$voincVar_display = formatting($voincVar, 2);

// VAR %

if ($budgetINCOWN == 0) {
    if ($incOWNVar == 0) {
        $incOWNVP = 0;
    } elseif ($incOWNVar < 0) {
        $incOWNVP = -100;
    } else {
        $incOWNVP = 100;
    }
} elseif ($incOWNVar == 0) {
    $incOWNVP = 0;
} else {
    $incOWNVP = $incOWNVar / $budgetINCOWN * 100;
}

if ($budgetINCDR == 0) {
    if ($incDRVar == 0) {
        $incDRVP = 0;
    } elseif ($incDRVar < 0) {
        $incDRVP = -100;
    } else {
        $incDRVP = 100;
    }
} elseif ($incDRVar == 0) {
    $incDRVP = 0;
} else {
    $incDRVP = $incDRVar / $budgetINCDR * 100;
}


if ($budgetINCVO == 0) {
    if ($voincVar == 0) {
        $incVOVP = 0;
    } elseif ($voincVar < 0) {
        $incVOVP = -100;
    } else {
        $incVOVP = 100;
    }
} elseif ($voincVar == 0) {
    $incVOVP = 0;
} else {
    $incVOVP = $voincVar / $budgetINCVO * 100;
}


// DISPLAY VAR %
// $incOWNVP_display = formatting($incOWNVP,0);
// $incDRVP_display = formatting($incDRVP,0);
// $incVOVP_display = formatting($incVOVP,0);

if ($incOWNVP <= 1000 && $incOWNVP >= -1000) {
    $incOWNVP_display = formatting($incOWNVP, 0);
} elseif ($incOWNVP < -1000) {
    $incOWNVP_display = '<(1,000.00)';
} elseif ($incOWNVP > 1000) {
    $incOWNVP_display = '<(1,000.00)';
}

if ($incDRVP <= 1000 && $incDRVP >= -1000) {
    $incDRVP_display = formatting($incDRVP, 0);
} elseif ($incDRVP < -1000) {
    $incDRVP_display = '<(1,000.00)';
} elseif ($incDRVP > 1000) {
    $incDRVP_display = '<(1,000.00)';
}

if ($incVOVP <= 1000 && $incVOVP >= -1000) {
    $incVOVP_display = formatting($incVOVP, 0);
} elseif ($incVOVP < -1000) {
    $incVOVP_display = '<(1,000.00)';
} elseif ($incVOVP > 1000) {
    $incVOVP_display = '<(1,000.00)';
}


// TOTALS DISPLAY
$totalAllBudgetIncome = $budgetINCOWN + $budgetINCDR + $budgetINCVO;
$totalAllBudgetIncomeA = $totalAllBudgetIncome;
$totalAllBudgetIncomeA_display = formatting($totalAllBudgetIncome, 2);
$totalAllVariance = $totalAllIncomePOS - $totalAllBudgetIncome;
$totalAllVarianceY = $totalAllIncomeAPOS - $totalAllBudgetIncomeA;
$totalAllVarianceIncomeY = $totalAllVariance;
$totalAllVariance_display = formatting($totalAllVariance, 2);

$totalAllVarianceY_display = formatting($totalAllVarianceY, 2);


if ($totalAllBudgetIncomeA == 0) {
    if ($totalAllVarianceY == 0) {
        $totalAllVPY = 0;
    } elseif ($totalAllVarianceY < 0) {
        $totalAllVPY = -100;
    } else {
        $totalAllVPY = 100;
    }
} elseif ($totalAllVarianceY == 0) {
    $totalAllVPY = 0;
} else {
    $totalAllVPY = $totalAllVarianceY / $totalAllBudgetIncomeA * 100;
}


// $totalAllVPY_display = formatting($totalAllVPY,0);
if ($totalAllVPY <= 1000 && $totalAllVPY >= -1000) {
    $totalAllVPY_display = formatting($totalAllVPY, 0);
} elseif ($totalAllVPY < -1000) {
    $totalAllVPY_display = '<(1,000.00)';
} elseif ($totalAllVPY > 1000) {
    $totalAllVPY_display = '<(1,000.00)';
}


// #########################YEAR BUDGET INCOME FIGURES ########################################################


$budgetINCOWN = getYearBudgetInc($propertyID, $periodTo, 'INCOWN');
$budgetINCOWNY_display = formatting($budgetINCOWN, 2);

$budgetINCDR = getYearBudgetInc($propertyID, $periodTo, 'INCDR');
$budgetINCDRY_display = formatting($budgetINCDR, 2);

$budgetINCVO = getYearBudgetInc($propertyID, $periodTo, 'INCVO');
$budgetINCVOY_display = formatting($budgetINCVO, 2);


$totalAllBudgetIncome = $budgetINCOWN + $budgetINCDR + $budgetINCVO;
$totalAllBudgetIncomeYTD = $totalAllBudgetIncome;
$totalAllBudgetIncomeY_display = formatting($totalAllBudgetIncome, 2);
$totalAllBudgetIncomeYEAR_display = $totalAllBudgetIncomeY_display;


// echo "$totalAllBudgetIncome = $budgetINCOWN + $budgetINCDR + $budgetINCVO <br />";

$line = 0;
$templine = 0; // to reduce '$line' after the third line if any lines were skipped
$text2 = 'Owner Receipts';
$text3 = 'Recoverable Receipts';
$text4 = ucwords(strtolower($_SESSION['country_default']['variable_outgoings'])) . ' Receipts';
$pdf->setFontExt($_fonts['Helvetica'], 8);


if ($ownerincome_display_rec == '0.00' &&
    $ownerincomeA_display == '0.00' &&
    $budgetINCOWN_display_rec == '0.00' &&
    $budgetINCOWN_display == '0.00' &&
    $budgetINCOWNY_display == '0.00') {
    $templine++;
} else {
    $pdf->showBoxed($text2, 22, 410, 275, 30, 'left', '');
    $pdf->showBoxed($ownerincome_display_rec, 202, 410, 75, 30, 'right', '');
    $pdf->showBoxed($ownerincomeA_display, 475, 410, 75, 30, 'right', '');
    $pdf->showBoxed($budgetINCOWN_display_rec, 272, 410, 75, 30, 'right', '');
    $pdf->showBoxed($budgetINCOWN_display, 550, 410, 75, 30, 'right', '');
    $pdf->showBoxed($incOWNVar_display_rec, 330, 410, 75, 30, 'right', '');
    $pdf->showBoxed($incOWNVar_display, 620, 410, 75, 30, 'right', '');
    $pdf->showBoxed($incOWNVP_display_rec, 385, 410, 75, 30, 'right', '');
    $pdf->showBoxed($incOWNVP_display, 665, 410, 75, 30, 'right', '');
    $pdf->showBoxed($budgetINCOWNY_display, 745, 410, 75, 30, 'right', '');
    $line += 15;
}

if (
    $recinc_display_rec == '0.00' &&
    $recincA_display == '0.00' &&
    $budgetINCDR_display_rec == '0.00' &&
    $budgetINCDR_display == '0.00' &&
    $budgetINCDRY_display == '0.00') {
    $templine++;
} else {
    $pdf->showBoxed($text3, 22, 410 - $line, 275, 30, 'left', '');
    $pdf->showBoxed($recinc_display_rec, 202, 410 - $line, 75, 30, 'right', '');
    // print "<font color=blue>recinc_display_rec $recinc_display_rec</font><p>";
    $pdf->showBoxed($recincA_display, 475, 410 - $line, 75, 30, 'right', '');
    $pdf->showBoxed($budgetINCDR_display_rec, 272, 410 - $line, 75, 30, 'right', '');
    $pdf->showBoxed($budgetINCDR_display, 550, 410 - $line, 75, 30, 'right', '');
    $pdf->showBoxed($incDRVar_display_rec, 330, 410 - $line, 75, 30, 'right', '');
    $pdf->showBoxed($incDRVar_display, 620, 410 - $line, 75, 30, 'right', '');
    $pdf->showBoxed($incDRVP_display_rec, 385, 410 - $line, 75, 30, 'right', '');
    $pdf->showBoxed($incDRVP_display, 665, 410 - $line, 75, 30, 'right', '');
    $pdf->showBoxed($budgetINCDRY_display, 745, 410 - $line, 75, 30, 'right', '');
    $line += 15;
}


if (
    $voinc_display_rec == '0.00' &&
    $voincA_display == '0.00' &&
    $budgetINCVO_display_rec == '0.00' &&
    $budgetINCVO_display == '0.00' &&
    $budgetINCVOY_display == '0.00') {
    $templine++;
} else {
    $pdf->showBoxed($text4, 22, 410 - $line, 275, 30, 'left', '');
    $pdf->showBoxed($voinc_display_rec, 202, 410 - $line, 75, 30, 'right', '');
    // print "voinc_display_rec $voinc_display_rec<p>";
    $pdf->showBoxed($voincA_display, 475, 410 - $line, 75, 30, 'right', '');
    $pdf->showBoxed($budgetINCVO_display_rec, 272, 410 - $line, 75, 30, 'right', '');
    $pdf->showBoxed($budgetINCVO_display, 550, 410 - $line, 75, 30, 'right', '');
    $pdf->showBoxed($voincVar_display_rec, 330, 410 - $line, 75, 30, 'right', '');
    $pdf->showBoxed($voincVar_display, 620, 410 - $line, 75, 30, 'right', '');
    $pdf->showBoxed($incVOVP_display_rec, 385, 410 - $line, 75, 30, 'right', '');
    $pdf->showBoxed($incVOVP_display, 665, 410 - $line, 75, 30, 'right', '');
    $pdf->showBoxed($budgetINCVOY_display, 745, 410 - $line, 75, 30, 'right', '');
    $line += 15;
}


$pdf->setColorExt('fill', 'rgb', 0.88, 0.88, 0.88, 0);
$pdf->rect(215, 420 - $line, 609, 20);
$pdf->fill();
$pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
$pdf->setlinewidth(0.5);
$pdf->moveto(215, 440 - $line);
$pdf->lineto(824, 440 - $line);
$pdf->stroke();
$pdf->moveto(215, 420 - $line);
$pdf->lineto(824, 420 - $line);
$pdf->stroke();


// TOTALS
$pdf->showBoxed($totalAllIncome_display_rec, 202, 405 - $line, 75, 30, 'right', '');
$pdf->showBoxed($totalAllBudgetIncome_display_rec, 272, 405 - $line, 75, 30, 'right', '');
$pdf->showBoxed($totalAllVariance_display_rec, 330, 405 - $line, 75, 30, 'right', '');
$pdf->showBoxed($totalAllVP_display_rec, 385, 405 - $line, 75, 30, 'right', '');


// TOTALS
$pdf->showBoxed($totalAllIncomeA_display, 475, 405 - $line, 75, 30, 'right', '');
$pdf->showBoxed($totalAllBudgetIncomeA_display, 550, 405 - $line, 75, 30, 'right', '');
$pdf->showBoxed($totalAllVarianceY_display, 620, 405 - $line, 75, 30, 'right', '');
$pdf->showBoxed($totalAllVPY_display, 665, 405 - $line, 75, 30, 'right', '');


$pdf->showBoxed($totalAllBudgetIncomeY_display, 745, 405 - $line, 75, 30, 'right', '');


$line -= 40;

// ########################################################################################################
// ########################################################################################################
// ########################################################################################################
// ########################################################################################################
// ###################################CASH PAYMENTS		  ###############################################
// ########################################################################################################
// ########################################################################################################
// ########################################################################################################

$line -= 10;


$text1 = 'Cash Payments';

$pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
$pdf->showBoxed($text1, 22, 320 - $line, 275, 30, 'left', '');

// CASH PAYMENTS FIGURES
// ACTUAL
// ---------------------------------------------------------------------------
// This Period Actual
//

$ownerexp = 0;
$voexp = 0;
$recexp = 0;
$ownerexpA = 0;
$voexpA = 0;
$recexpA = 0;


// Current month
$ownerexpM = totalexpenses($propertyID, $periodFrom, $periodTo, 'EXPOWN');
$ownerexpPOS = $ownerexpM['net_amount'];


$voexpM = totalexpenses($propertyID, $periodFrom, $periodTo, 'EXPVO');
$voexpPOS = $voexpM['net_amount'];

$recexpM = totalexpenses($propertyID, $periodFrom, $periodTo, 'EXPDR');
$recexpPOS = $recexpM['net_amount'];

$totalAllExpPOS = $ownerexpPOS + $voexpPOS + $recexpPOS;
$net = $totalAllIncomePOS - $totalAllExpPOS;


// Formatting
$ownerexp_display = formatting($ownerexpPOS, 2);
$ownerexp_display_pmt = $ownerexp_display;
$ownerexpenditure = $ownerexp_display;

$voexp_display = formatting($voexpPOS, 2);
$voexp_display_pmt = $voexp_display;

$recexp_display = formatting($recexpPOS, 2);
$recexp_display_pmt = $recexp_display;

$totalAllExp_display = formatting($totalAllExpPOS, 2);
$totalAllExp_display_pmt = $totalAllExp_display;

$net_display = formatting($net, 2);
$net_displayMonth = $net_display;


$totalAllExp1 = $totalAllExpPOS;


// #############################BUDGET EXPENSES CURRENT PERIOD #####################################################
$params = [];
$budgetEXPOWNSQL = 'SELECT COALESCE(SUM(pmep_b_c_amt), 0) AS amount
		FROM pmep_b_exp_per
		WHERE (pmep_prop = ' . addSQLParam($params, $propertyID) . ') 
		AND (pmep_per IN (' . addSQLParam($params, $pmcp_period) . ')) 
				AND (pmep_year IN (' . addSQLParam($params, $pmcp_year) . ")) 
				AND (pmep_exp_acc IN
						  (SELECT pmcg_acc
							FROM pmcg_chart_grp
							WHERE (pmcg_grp = 'TRACC2') AND pmcg_subgrp = 'EXPOWN'))";

// echo "16th BUDGET:" . $budgetEXPOWNSQL;
$dbh->selectDatabase($clientDB);
$budgetEXPOWN = $dbh->executeScalar($budgetEXPOWNSQL, $params);
$budgetEXPOWN_display = formatting($budgetEXPOWN, 2);
$budgetEXPOWN_display_pmt = $budgetEXPOWN_display;
$params = [];
$budgetEXPDRSQL = 'SELECT COALESCE(SUM(pmep_b_c_amt), 0) AS amount
		FROM pmep_b_exp_per
		WHERE (pmep_prop = ' . addSQLParam($params, $propertyID) . ') 
		AND (pmep_per IN (' . addSQLParam($params, $pmcp_period) . ')) 
				AND (pmep_year IN (' . addSQLParam($params, $pmcp_year) . "))
								AND (pmep_exp_acc IN
						  (SELECT pmcg_acc
							FROM pmcg_chart_grp
							WHERE (pmcg_grp = 'TRACC2') AND pmcg_subgrp = 'EXPDR'))";
// echo "20th BUDGET:" . $budgetEXPDRSQL;
$dbh->selectDatabase($clientDB);
$budgetEXPDR = $dbh->executeScalar($budgetEXPDRSQL, $params);

$budgetEXPDR_display = formatting($budgetEXPDR, 2);
$budgetEXPDR_display_pmt = $budgetEXPDR_display;

$params = [];
$budgetEXPVOSQL = 'SELECT COALESCE(SUM(pmep_b_c_amt), 0) AS amount
		FROM pmep_b_exp_per
		WHERE (pmep_prop = ' . addSQLParam($params, $propertyID) . ') 
		AND (pmep_per IN (' . addSQLParam($params, $pmcp_period) . ')) 
				AND (pmep_year IN (' . addSQLParam($params, $pmcp_year) . "))
							   AND (pmep_exp_acc IN
						  (SELECT pmcg_acc
							FROM pmcg_chart_grp
							WHERE (pmcg_grp = 'TRACC2') AND pmcg_subgrp = 'EXPVO'))";
// echo "24th BUDGET:" . $budgetEXPVOSQL;
$dbh->selectDatabase($clientDB);
$budgetEXPVO = $dbh->executeScalar($budgetEXPVOSQL, $params);

$budgetEXPVO_display = formatting($budgetEXPVO, 2);
$budgetEXPVO_display_pmt = $budgetEXPVO_display;


// VARIANCE
$expOWNVar = $budgetEXPOWN - $ownerexpPOS;
$expDRVar = $budgetEXPDR - $recexpPOS;
$voexpVar = $budgetEXPVO - $voexpPOS;

// DISPLAY VARIANCE
$expOWNVar_display = formatting($expOWNVar, 2);
$expOWNVar_display_pmt = $expOWNVar_display;
$expDRVar_display_pmt = formatting($expDRVar, 2);
// $expDRVar_display_pmt = $expDRVar_display;
$voexpVar_display = formatting($voexpVar, 2);
$voexpVar_display_pmt = $voexpVar_display;

// VAR %
if ($budgetEXPOWN == 0) {
    if ($expOWNVar == 0) {
        $expOWNVP = 0;
    } elseif ($expOWNVar < 0) {
        $expOWNVP = '-100';
    } else {
        $expOWNVP = 100;
    }
} else {
    $expOWNVP = $expOWNVar / $budgetEXPOWN * 100;
}

if ($budgetEXPDR == 0) {
    if ($expDRVar == 0) {
        $expDRVP = 0;
    } elseif ($expDRVar < 0) {
        $expDRVP = '-100';
    } else {
        $expDRVP = 100;
    }
} else {
    $expDRVP = $expDRVar / $budgetEXPDR * 100;
}


if ($budgetEXPVO == 0) {
    if ($voexpVar == 0) {
        $expVOVP = 0;
    } elseif ($voexpVar < 0) {
        $expVOVP = '-100';
    } else {
        $expVOVP = 100;
    }
} else {
    $expVOVP = $voexpVar / $budgetEXPVO * 100;
}

// DISPLAY VAR %
$expOWNVP_display = formatting($expOWNVP, 0);
$expDRVP_display = formatting($expDRVP, 0);
$expVOVP_display = formatting($expVOVP, 0);

if ($expOWNVP <= 1000 && $expOWNVP >= -1000) {
    $expOWNVP_display = formatting($expOWNVP, 0);
} elseif ($expOWNVP < -1000) {
    $expOWNVP_display = '<(1,000.00)';
} elseif ($expOWNVP > 1000) {
    $expOWNVP_display = '<(1,000.00)';
}

if ($expDRVP <= 1000 && $expDRVP >= -1000) {
    $expDRVP_display = formatting($expDRVP, 0);
} elseif ($expDRVP < -1000) {
    $expDRVP_display = '<(1,000.00)';
} elseif ($expDRVP > 1000) {
    $expDRVP_display = '<(1,000.00)';
}

if ($expVOVP <= 1000 && $expVOVP >= -1000) {
    $expVOVP_display = formatting($expVOVP, 0);
} elseif ($expVOVP < -1000) {
    $expVOVP_display = '<(1,000.00)';
} elseif ($expVOVP > 1000) {
    $expVOVP_display = '<(1,000.00)';
}

$expOWNVP_display_pmt = $expOWNVP_display;
$expDRVP_display_pmt = $expDRVP_display;
$expVOVP_display_pmt = $expVOVP_display;

// TOTALS DISPLAY
$totalAllBudgetExp = $budgetEXPOWN + $budgetEXPDR + $budgetEXPVO;
$totalAllBudgetExp1 = $totalAllBudgetExp;
$totalAllBudgetExp_display = formatting($totalAllBudgetExp, 2);
$totalAllBudgetExpM_display = $totalAllBudgetExp_display;
$totalAllBudgetExp_display_pmt = $totalAllBudgetExp_display;
$totalAllVariance = $totalAllBudgetExp - $totalAllExpPOS;
$totalAllVarianceExp = $totalAllVariance;
$totalAllVariance_display = formatting($totalAllVariance, 2);
$totalAllVariance_display_pmt = $totalAllVariance_display;


if ($totalAllBudgetExp == 0) {
    if ($totalAllVariance == 0) {
        $totalAllVP = 0;
    } elseif ($totalAllVariance < 0) {
        $totalAllVP = '-100';
    } else {
        $totalAllVP = 100;
    }
} else {
    $totalAllVP = $totalAllVariance / $totalAllBudgetExp * 100;
}


$totalAllVP_display = formatting($totalAllVP, 0);

if ($totalAllVP <= 1000 && $totalAllVP >= -1000) {
    $totalAllVP_display = formatting($totalAllVP, 0);
} elseif ($totalAllVP < -1000) {
    $totalAllVP_display = '<(1,000.00)';
} elseif ($totalAllVP > 1000) {
    $totalAllVP_display = '<(1,000.00)';
}

$totalAllVP_display_pmt = $totalAllVP_display;
$totalAllVPExp_display = $totalAllVP_display;

// $pdf->show_xy("Actual: $budgetINCOWN_display", 22, 500);
// BUDGET FIGURES DISPLAY


// ////////////////////////////////////////////////////////////////////////////////////////////////////
// ////////////////////////////////////////////////////////////////////////////////////////////////////
// ////////////////////////////////////////////////////////////////////////////////////////////////////
// //////	YEAR TO DATE ACTUAL
// //////
// //////


$ownerexp = 0;
$voexp = 0;
$recexp = 0;
$ownerexpA = 0;
$voexpA = 0;
$recexpA = 0;


// ---------------------------------------------------------------------------
// Year To Date Actual
//


$ownerexpYear = totalexpenses($propertyID, $startFinancialYear, $periodTo, 'EXPOWN');
$ownerexp_takeon = take_on_balance($propertyID, 'EXPOWN', $startFinancialYear, $periodTo);
$ownerexpAPOS = $ownerexpYear['net_amount'] + $ownerexp_takeon['amount'];
// $ownerexpY = $ownerexpYr + $ownerexp_takeon['amount'];

$voexpYear = totalexpenses($propertyID, $startFinancialYear, $periodTo, 'EXPVO');
$voexp_takeon = take_on_balance($propertyID, 'EXPVO', $startFinancialYear, $periodTo);
$voexpAPOS = $voexpYear['net_amount'] + $voexp_takeon['amount'];
// $voexpY = $voexpYr + $voexp_takeon['amount'];

$recexpYear = totalexpenses($propertyID, $startFinancialYear, $periodTo, 'EXPDR');
$recexp_takeon = take_on_balance($propertyID, 'EXPDR', $startFinancialYear, $periodTo);
$recexpYr = $recexpYear['net_amount'] + $recexp_takeon['amount'];
// $recexpAPOS = $recexpYr + $recexp_takeon['amount'];

$totalAllExpAPOS = $ownerexpAPOS + $voexpAPOS + $recexpYr;
$netY = $totalAllIncomeAPOS - $totalAllExpAPOS;

// Formatting
$ownerexpA_display = formatting($ownerexpAPOS, 2);
$voexpA_display = formatting($voexpAPOS, 2);
$recexpA_display = formatting($recexpYr, 2);

$totalAllExpA_display = formatting($totalAllExpAPOS, 2);
// $net_display = formatting($netY,2);


// echo "$net_display <br />";
// $recoverableA_exp = $recexpAPOS;


// //////////////////////////////////////////////////
//	BUDGET YTD
//


$budgetEXPOWN = getBudgetExpYTD($propertyID, $toPeriod, $periodTo, 'EXPOWN');
$budgetEXPOWN_display = formatting($budgetEXPOWN, 2);

$budgetEXPDR = getBudgetExpYTD($propertyID, $toPeriod, $periodTo, 'EXPDR');
$budgetEXPDR_display = formatting($budgetEXPDR, 2);

$budgetEXPVO = getBudgetExpYTD($propertyID, $toPeriod, $periodTo, 'EXPVO');
$budgetEXPVO_display = formatting($budgetEXPVO, 2);


// VARIANCE
$expOWNVar = $budgetEXPOWN - $ownerexpAPOS;
$expDRVar = $budgetEXPDR - $recexpYr;
$voexpVar = $budgetEXPVO - $voexpAPOS;

// DISPLAY VARIANCE
$expOWNVar_display = formatting($expOWNVar, 2);
$expDRVar_display = formatting($expDRVar, 2);
$voexpVar_display = formatting($voexpVar, 2);

// VAR %

if ($budgetEXPOWN == 0) {
    if ($expOWNVar == 0) {
        $expOWNVP = 0;
    } elseif ($expOWNVar < 0) {
        $expOWNVP = '-100';
    } else {
        $expOWNVP = 100;
    }
} else {
    $expOWNVP = $expOWNVar / $budgetEXPOWN * 100;
}


if ($budgetEXPDR == 0) {
    if ($expDRVar == 0) {
        $expDRVP = 0;
    } elseif ($expDRVar < 0) {
        $expDRVP = '-100';
    } else {
        $expDRVP = 100;
    }
} else {
    $expDRVP = $expDRVar / $budgetEXPDR * 100;
}


if ($budgetEXPVO == 0) {
    if ($voexpVar == 0) {
        $expVOVP = 0;
    } elseif ($voexpVar < 0) {
        $expVOVP = '-100';
    } else {
        $expVOVP = 100;
    }
} else {
    $expVOVP = $voexpVar / $budgetEXPVO * 100;
}


// DISPLAY VAR %
$expOWNVP_display = formatting($expOWNVP, 0);
$expDRVP_display = formatting($expDRVP, 0);
$expVOVP_display = formatting($expVOVP, 0);

if ($expOWNVP <= 1000 && $expOWNVP >= -1000) {
    $expOWNVP_display = formatting($expOWNVP, 0);
} elseif ($expOWNVP < -1000) {
    $expOWNVP_display = '<(1,000.00)';
} elseif ($expOWNVP > 1000) {
    $expOWNVP_display = '<(1,000.00)';
}

if ($expDRVP <= 1000 && $expDRVP >= -1000) {
    $expDRVP_display = formatting($expDRVP, 0);
} elseif ($expDRVP < -1000) {
    $expDRVP_display = '<(1,000.00)';
} elseif ($expDRVP > 1000) {
    $expDRVP_display = '<(1,000.00)';
}

if ($expVOVP <= 1000 && $expVOVP >= -1000) {
    $expVOVP_display = formatting($expVOVP, 0);
} elseif ($expVOVP < -1000) {
    $expVOVP_display = '<(1,000.00)';
} elseif ($expVOVP > 1000) {
    $expVOVP_display = '<(1,000.00)';
}


// TOTALS DISPLAY
$totalAllBudgetExp = $budgetEXPOWN + $budgetEXPDR + $budgetEXPVO;
$totalAllBudgetExpA = $totalAllBudgetExp;
$totalAllBudgetExp_display = formatting($totalAllBudgetExp, 2);


// ##############################YEAR BUDGET EXPENSE FIGURES #######################################################


$budgetEXPOWN = getYearBudgetExp($propertyID, $periodTo, 'EXPOWN');
$budgetEXPOWNY_display = formatting($budgetEXPOWN, 2);

$budgetEXPDR = getYearBudgetExp($propertyID, $periodTo, 'EXPDR');
$budgetEXPDRY_display = formatting($budgetEXPDR, 2);

$budgetEXPVO = getYearBudgetExp($propertyID, $periodTo, 'EXPVO');
$budgetEXPVOY_display = formatting($budgetEXPVO, 2);


$totalAllBudgetExp = $budgetEXPOWN + $budgetEXPDR + $budgetEXPVO;
$totalAllBudgetExpY_display = formatting($totalAllBudgetExp, 2);
$totalAllBudgetExpYTD = $totalAllBudgetExp;
$totalAllBudgetExpYTD_display = formatting($totalAllBudgetExpYTD, 2);

$totalAllVariance = $totalAllBudgetExpA - $totalAllExpAPOS;
$totalAllVarianceExpY = $totalAllVariance;
$totalAllVariance_display = formatting($totalAllVariance, 2);


if ($totalAllBudgetExpA == 0) {
    if ($totalAllVariance == 0) {
        $totalAllVP = 0;
    } elseif ($totalAllVariance < 0) {
        $totalAllVP = '-100';
    } else {
        $totalAllVP = 100;
    }
} else {
    $totalAllVP = $totalAllVariance / $totalAllBudgetExpA * 100;
}


$totalAllVP_display = formatting($totalAllVP, 0);
$totalAllVPExpY_display = $totalAllVP_display;


$pdf->setFontExt($_fonts['Helvetica'], 8);
$text2 = 'Owner Expenditure';
$text3 = 'Recoverable Expenditure';
$text4 = ucwords(strtolower($_SESSION['country_default']['variable_outgoings'])) . ' Expenditure';


if ($ownerexp_display_pmt == '0.00' &&
    $budgetEXPOWN_display_pmt == '0.00' &&
    $ownerexpA_display == '0.00' &&
    $budgetEXPOWN_display == '0.00' &&
    $budgetEXPOWNY_display == '0.00') {
    $line -= 15;
} else {
    $pdf->showBoxed($text2, 22, 305 - $line, 275, 30, 'left', '');
    $pdf->showBoxed($ownerexp_display_pmt, 202, 305 - $line, 75, 30, 'right', '');
    $pdf->showBoxed($budgetEXPOWN_display_pmt, 272, 305 - $line, 75, 30, 'right', '');
    $pdf->showBoxed($expOWNVar_display_pmt, 330, 305 - $line, 75, 30, 'right', '');
    $pdf->showBoxed($expOWNVP_display_pmt, 385, 305 - $line, 75, 30, 'right', '');

    $pdf->showBoxed($ownerexpA_display, 475, 305 - $line, 75, 30, 'right', '');
    $pdf->showBoxed($budgetEXPOWN_display, 550, 305 - $line, 75, 30, 'right', '');
    $pdf->showBoxed($expOWNVar_display, 620, 305 - $line, 75, 30, 'right', '');
    $pdf->showBoxed($expOWNVP_display, 665, 305 - $line, 75, 30, 'right', '');
    $pdf->showBoxed($budgetEXPOWNY_display, 745, 305 - $line, 75, 30, 'right', '');
}

if ($recexp_display_pmt == '0.00' &&
    $budgetEXPDR_display_pmt == '0.00' &&
    $recexpA_display == '0.00' &&
    $budgetEXPDR_display == '0.00' &&
    $budgetEXPDRY_display == '0.00') {
    $line -= 15;
} else {
    $pdf->showBoxed($text3, 22, 290 - $line, 275, 30, 'left', '');
    $pdf->showBoxed($recexp_display_pmt, 202, 290 - $line, 75, 30, 'right', '');
    $pdf->showBoxed($budgetEXPDR_display_pmt, 272, 290 - $line, 75, 30, 'right', '');
    $pdf->showBoxed($expDRVar_display_pmt, 330, 290 - $line, 75, 30, 'right', '');
    $pdf->showBoxed($expDRVP_display_pmt, 385, 290 - $line, 75, 30, 'right', '');

    $pdf->showBoxed($recexpA_display, 475, 290 - $line, 75, 30, 'right', '');
    $pdf->showBoxed($budgetEXPDR_display, 550, 290 - $line, 75, 30, 'right', '');
    $pdf->showBoxed($expDRVar_display, 620, 290 - $line, 75, 30, 'right', '');
    $pdf->showBoxed($expDRVP_display, 665, 290 - $line, 75, 30, 'right', '');
    $pdf->showBoxed($budgetEXPDRY_display, 745, 290 - $line, 75, 30, 'right', '');
}

if ($voexp_display_pmt == '0.00' &&
    $budgetEXPVO_display_pmt == '0.00' &&
    $voexpA_display == '0.00' &&
    $budgetEXPVO_display == '0.00' &&
    $budgetEXPVOY_display == '0.00') {
    $line -= 15;
} else {
    $pdf->showBoxed($text4, 22, 275 - $line, 275, 30, 'left', '');
    $pdf->showBoxed($voexp_display_pmt, 202, 275 - $line, 75, 30, 'right', '');
    $pdf->showBoxed($budgetEXPVO_display_pmt, 272, 275 - $line, 75, 30, 'right', '');
    $pdf->showBoxed($voexpVar_display_pmt, 330, 275 - $line, 75, 30, 'right', '');
    $pdf->showBoxed($expVOVP_display_pmt, 385, 275 - $line, 75, 30, 'right', '');

    $pdf->showBoxed($voexpA_display, 475, 275 - $line, 75, 30, 'right', '');
    $pdf->showBoxed($budgetEXPVO_display, 550, 275 - $line, 75, 30, 'right', '');
    $pdf->showBoxed($voexpVar_display, 620, 275 - $line, 75, 30, 'right', '');
    $pdf->showBoxed($expVOVP_display, 665, 275 - $line, 75, 30, 'right', '');
    $pdf->showBoxed($budgetEXPVOY_display, 745, 275 - $line, 75, 30, 'right', '');
}


// TOTALS


$pdf->setColorExt('fill', 'rgb', 0.88, 0.88, 0.88, 0);
$pdf->rect(215, 270 - $line, 609, 20);
$pdf->fill();
$pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
$pdf->setlinewidth(0.5);
$pdf->moveto(215, 290 - $line);
$pdf->lineto(824, 290 - $line);
$pdf->stroke();
$pdf->moveto(215, 270 - $line);
$pdf->lineto(824, 270 - $line);
$pdf->stroke();


$pdf->showBoxed($totalAllExp_display_pmt, 202, 255 - $line, 75, 30, 'right', '');
$pdf->showBoxed($totalAllBudgetExp_display_pmt, 272, 255 - $line, 75, 30, 'right', '');
$pdf->showBoxed($totalAllVariance_display_pmt, 330, 255 - $line, 75, 30, 'right', '');
$pdf->showBoxed($totalAllVP_display_pmt, 385, 255 - $line, 75, 30, 'right', '');


$totalAllBudgetExpY_display = $totalAllBudgetExp_display;

// TOTALS
$pdf->showBoxed($totalAllExpA_display, 475, 255 - $line, 75, 30, 'right', '');
$pdf->showBoxed($totalAllBudgetExp_display, 550, 255 - $line, 75, 30, 'right', '');
$pdf->showBoxed($totalAllVariance_display, 620, 255 - $line, 75, 30, 'right', '');
$pdf->showBoxed($totalAllVP_display, 665, 255 - $line, 75, 30, 'right', '');


$pdf->showBoxed($totalAllBudgetExpYTD_display, 745, 255 - $line, 75, 30, 'right', '');


// NET CASH INFLOWS/(OUTFLOWS)
$net = $totalAllIncome1 - $totalAllExp1;
$netBudget = $totalAllBudgetIncome1 - $totalAllBudgetExp1;
$netVar = $net - $netBudget;


if ($netBudget == 0) {
    if ($netVar == 0) {
        $netVarP = 0;
    } elseif ($netVar < 0) {
        $netVarP = '-100';
    } else {
        $netVarP = 100;
    }
} else {
    $netVarP = $netVar / $netBudget * 100;
}


// DISPLAY FIGURES
// $net_display = formatting($net);
$netBudget_display = formatting($netBudget);
$netVar_display = formatting($netVar);
$netVarP_display = formatting($netVarP);


$text1 = 'Net Cash Excluding ' . $_SESSION['country_default']['tax_label'];
$pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
$pdf->showBoxed($text1, 22, 225 - $line, 275, 30, 'left', '');


$pdf->setColorExt('fill', 'rgb', 0.88, 0.88, 0.88, 0);
$pdf->rect(215, 240 - $line, 609, 20);
$pdf->fill();
$pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
$pdf->setlinewidth(0.5);
$pdf->moveto(215, 260 - $line);
$pdf->lineto(824, 260 - $line);
$pdf->stroke();

$pdf->moveto(18, 240 - $line);
$pdf->lineto(824, 240 - $line);
$pdf->stroke();

$pdf->moveto(215, 515);
$pdf->lineto(215, 240 - $line);
$pdf->stroke();
$pdf->moveto(470, 515);
$pdf->lineto(470, 240 - $line);
$pdf->stroke();
$pdf->moveto(745, 515);
$pdf->lineto(745, 240 - $line);
$pdf->stroke();

$pdf->moveto(18, 240 - $line);
$pdf->lineto(18, 515);
$pdf->stroke();
// was 110 not 240
$pdf->moveto(824, 240 - $line);
$pdf->lineto(824, 515);
$pdf->stroke();


$pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
$pdf->showBoxed($net_display, 202, 225 - $line, 75, 30, 'right', '');
$pdf->showBoxed($netBudget_display, 272, 225 - $line, 75, 30, 'right', '');
$pdf->showBoxed($netVar_display, 330, 225 - $line, 75, 30, 'right', '');
$netVarP_display = $netVarP < -1000 ? '<(1,000.00)' : $netVarP_display;
$netVarP_display = $netVarP > 1000 ? '>1,000.00' : $netVarP_display;
$pdf->showBoxed($netVarP_display, 385, 225 - $line, 75, 30, 'right', '');


// NET CASH INFLOWS/(OUTFLOWS) YTD
$netA = $totalAllIncomeAPOS - $totalAllExpAPOS;
$netBudgetA = $totalAllBudgetIncomeA - $totalAllBudgetExpA;
$netVarA = $netA - $netBudgetA;


if ($netBudgetA == 0) {
    if ($netVarA == 0) {
        $netVarPA = 0;
    } elseif ($netVarA < 0) {
        $netVarPA = '-100';
    } else {
        $netVarPA = 100;
    }
} else {
    $netVarPA = $netVarA / $netBudgetA * 100;
}


// DISPLAY FIGURES
$netA_display = formatting($netA);
$netBudgetA_display = formatting($netBudgetA);
$netVarA_display = formatting($netVarA);
$netVarPA_display = formatting($netVarPA);


$pdf->showBoxed($netA_display, 475, 225 - $line, 75, 30, 'right', '');
$pdf->showBoxed($netBudgetA_display, 550, 225 - $line, 75, 30, 'right', '');
$pdf->showBoxed($netVarA_display, 620, 225 - $line, 75, 30, 'right', '');
$netVarPA_display = $netVarPA < -1000 ? '<(1,000.00)' : $netVarPA_display;
$netVarPA_display = $netVarPA > 1000 ? '>1,000.00' : $netVarPA_display;
$pdf->showBoxed($netVarPA_display, 665, 225 - $line, 75, 30, 'right', '');

// NET BUDGET YEAR
$netB = $totalAllBudgetIncomeYTD - $totalAllBudgetExpYTD;

$netB_display = formatting($netB);


$pdf->showBoxed($netB_display, 745, 225 - $line, 75, 30, 'right', '');


$pdf->setFontExt($_fonts['Helvetica'], 8);
// ########################################################################
// ###################### GST RECEIVED AND PAID ###########################
// ########################################################################

$tax_income = totalincome($propertyID, $periodFrom, $periodTo);
$tax_incM = $tax_income['tax_amount'];
$gst = $tax_incM * (-1);
$gst_display = formatting($gst, 2); // echo "$gst_display <br />";

$tax_Aincome = totalincome($propertyID, $startFinancialYear, $periodTo);
$gstA = $tax_Aincome['tax_amount'] * (-1);


$gst_takeon = take_on_balanceGST($propertyID, $startFinancialYear, $periodTo);

// problem here multiplying 0 by -1	- should be done in the function
// $gstAOPENBAL = $gst_takeon['amount'];
$gstAOPENBAL = $gst_takeon;
$gstA += $gstAOPENBAL;
$gstA_display = formatting($gstA, 2);

// echo "echo $gstA_display <br />";


// ################################################################################
// ###############################GST PAID ########################################
// ################################################################################


$ownerexpMFund = totalexpensesFund($propertyID, $periodFrom, $periodTo, 'EXPOWN');
$voexpMFund = totalexpensesFund($propertyID, $periodFrom, $periodTo, 'EXPVO');
$recexpMFund = totalexpensesFund($propertyID, $periodFrom, $periodTo, 'EXPDR');
$ownerexpYearFund = totalexpensesFund($propertyID, $startFinancialYear, $periodTo, 'EXPOWN');
$voexpYearFund = totalexpensesFund($propertyID, $startFinancialYear, $periodTo, 'EXPVO');
$recexpYearFund = totalexpensesFund($propertyID, $startFinancialYear, $periodTo, 'EXPDR');
$noneop_pay_duringM = totalexpenses($propertyID, $periodFrom, $periodTo, 'BSPMT'); // trim($query_result["amount"]);
$noneop_pay_duringY = totalexpenses($propertyID, $startFinancialYear, $periodTo, 'BSPMT');

// GST paid
$ownerexp_GST = $ownerexpM['tax_amount'] + $ownerexpMFund['tax_amount'];
$voexp_GST = $voexpM['tax_amount'] + $voexpMFund['tax_amount'];
$recexp_GST = $recexpM['tax_amount'] + $recexpMFund['tax_amount'];
$ownerexpYr_GST = $ownerexpYear['tax_amount'] + $ownerexpYearFund['tax_amount'];
$voexpYr_GST = $voexpYear['tax_amount'] + $voexpYearFund['tax_amount'];
$recexpYr_GST = $recexpYear['tax_amount'] + $recexpYearFund['tax_amount'];
$gstpaid = -$ownerexp_GST - $voexp_GST - $recexp_GST - +$noneop_pay_duringM['tax_amount'];
$gstpaid_display = formatting($gstpaid, 2);

// echo "$gstpaid_display <br />";


// ####################################################################################
// #########  GST YTD NOTE: NEED TO INCLUDE GROUP  pmcg_subgrp = 'EXPGST' #############
// ####################################################################################

$openBalGST_pd = take_on_balanceGST_paid($propertyID, $startFinancialYear, $periodTo);
// $openBalGST_exp = $openBalGST_pd ['amount'];
$openBalGST_exp = $openBalGST_pd;
$gstpaidA = @(-$ownerexpYr_GST - $voexpYr_GST - $recexpYr_GST - $openBalGST_exp - +$noneop_pay_duringY['tax_amount']);
$gstpaidA_display = formatting($gstpaidA, 2);

// ##GST paid to owner
$remitgstMnt = GST_remitted($propertyID, 'EXPGST', $periodFrom, $periodTo);
$remitgstM = $remitgstMnt * (-1);
$remitgst_display = formatting($remitgstM, 2);

$remitgstYear = GST_remitted($propertyID, 'EXPGST', $startFinancialYear, $periodTo);
$remitgstYr = $remitgstYear * (-1);
$remitgstA_display = formatting($remitgstYr, 2);

// echo "  $remitgst_display - $remitgstA_display <br />";


$gst_subtotal = $gst + $gstpaid + $remitgstM;
$gst_subtotalA = $gstA + $gstpaidA + $remitgstYr;

$gst_subtotal_display = $gst_subtotal; // * (-1);
$gst_subtotalA_display = $gst_subtotalA; // * (-1);
$gst_subtotal_display = formatting($gst_subtotal_display, 2);
$gst_subtotalA_display = formatting($gst_subtotalA_display, 2);

$bas_gst_received = $gst;
$bas_gst_paid = $gstpaid;


$line = -12;
$pdf->setFontExt($_fonts['Helvetica'], 8);
if ($gst_display == '0.00' && $gstA_display == '0.00') {
    $line += 10;
} else {
    $pdf->showBoxed(
        $_SESSION['country_default']['tax_label'] . ' Received on Receipts',
        22,
        215 + $line,
        175,
        30,
        'left',
        ''
    );
    $pdf->showBoxed($gst_display, 202, 215 + $line, 75, 30, 'right', '');
    $pdf->showBoxed($gstA_display, 475, 215 + $line, 75, 30, 'right', '');
}

if ($gstpaid_display == '0.00' && $gstpaidA_display == '0.00') {
    $line += 10;
} else {
    $pdf->showBoxed(
        'Less: ' . $_SESSION['country_default']['tax_label'] . ' Paid on Payments',
        22,
        200 + $line,
        275,
        30,
        'left',
        ''
    );
    $pdf->showBoxed($gstpaid_display, 202, 200 + $line, 75, 30, 'right', '');
    $pdf->showBoxed($gstpaidA_display, 475, 200 + $line, 75, 30, 'right', '');
}

if ($remitgst_display == '0.00' && $remitgstA_display == '0.00') {
    $line += 10;
} else {
    $pdf->showBoxed(
        'Less: ' . $_SESSION['country_default']['tax_label'] . ' Remitted to Owner/ATO',
        22,
        185 + $line,
        275,
        30,
        'left',
        ''
    );
    $pdf->showBoxed($remitgst_display, 202, 185 + $line, 75, 30, 'right', '');
    $pdf->showBoxed($remitgstA_display, 475, 185 + $line, 75, 30, 'right', '');
}

// year
// if all GST is zero don't show totals
if (! ($gst_display == '0.00' && $gstA_display == '0.00' && $gstpaid_display == '0.00' && $gstpaidA_display == '0.00' && $remitgst_display == '0.00')) {
    $pdf->showBoxed($gst_subtotal_display, 202, 172 + $line, 75, 30, 'right', '');
    $pdf->showBoxed($gst_subtotalA_display, 475, 172 + $line, 75, 30, 'right', '');
}


$pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
$pdf->showBoxed('Net Cash Operating Inflows/(Outflows)', 22, 152 + $line, 275, 30, 'left', '');
$pdf->setFontExt($_fonts['Helvetica'], 8);

$netcash = $net + $gst_subtotal;
$netcashA = $netA + $gst_subtotalA;
$netcash_display = formatting($netcash);
$netcashA_display = formatting($netcashA);


$pdf->showBoxed($netcash_display, 202, 152 + $line, 75, 30, 'right', '');
$pdf->showBoxed($netcashA_display, 475, 152 + $line, 75, 30, 'right', '');

// don't show GST lines if GST is not being shown
if (! ($gst_display == '0.00' && $gstA_display == '0.00' && $gstpaid_display == '0.00' && $gstpaidA_display == '0.00' && $remitgst_display == '0.00')) {
    $pdf->moveto(215, 188 + $line);
    $pdf->lineto(276, 188 + $line);
    $pdf->stroke();
    $pdf->moveto(215, 202 + $line);
    $pdf->lineto(276, 202 + $line);
    $pdf->stroke();
    $pdf->moveto(490, 188 + $line);
    $pdf->lineto(550, 188 + $line);
    $pdf->stroke();
    $pdf->moveto(490, 202 + $line);
    $pdf->lineto(550, 202 + $line);
    $pdf->stroke();
}

// $pdf->moveto(220, 179-$line);
// $pdf->lineto(280, 179-$line);
// $pdf->stroke();

$pdf->moveto(215, 169 + $line);
$pdf->lineto(276, 169 + $line);
$pdf->stroke();
$pdf->moveto(215, 167 + $line);
$pdf->lineto(276, 167 + $line);
$pdf->stroke();
// $pdf->moveto(500, 179-$line);
// $pdf->lineto(560, 179-$line);
// $pdf->stroke();
$pdf->moveto(490, 169 + $line);
$pdf->lineto(550, 169 + $line);
$pdf->stroke();
$pdf->moveto(490, 167 + $line);
$pdf->lineto(550, 167 + $line);
$pdf->stroke();


// ////////////////////////////////////////////////////////////////////////////////////////////////////////
// ////////////////////////////////////////////////////////////////////////////////////////////////////////
// ////////
// ////////   CASH RECONCILIATION
// ////////
$net_displayMonth = $net_display;
$net_display = $netA_display;

$line = 65;
$pdf->setlinewidth(0.5);
$pdf->moveto(18, 215 - $line);
$pdf->lineto(824, 215 - $line);
$pdf->stroke();

$pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
$pdf->showBoxed('Cash Reconciliation', 22, 180 - $line, 275, 30, 'left', '');
$pdf->setFontExt($_fonts['Helvetica'], 8);
// echo $netcash_display;
$pdf->showBoxed($netcash_display, 202, 145 - $line, 75, 30, 'right', '');
$pdf->showBoxed($netcashA_display, 475, 145 - $line, 75, 30, 'right', '');
$pdf->setlinewidth(0.5);
$pdf->moveto(215, 163 - $line);
$pdf->lineto(276, 163 - $line);
$pdf->stroke();

$pdf->moveto(490, 163 - $line);
$pdf->lineto(550, 163 - $line);
$pdf->stroke();


$pdf->showBoxed('Cash balance at beginning of period/year', 22, 160 - $line, 275, 30, 'left', '');
$pdf->showBoxed('Net Cash Operating Inflows/(Outflows)', 22, 145 - $line, 275, 30, 'left', '');


/*$pdf->setlinewidth (2);
$pdf->moveto(18, 110-$line);
$pdf->lineto(824, 110-$line);
$pdf->stroke();
  */
$pdf->setFontExt($_fonts['Helvetica'], 8);
// $pdf->showBoxed ("Printed on $date", 22, 10, 275, 30, "left", "");
$pdf->showBoxed("Page {$page}", 750, 0, 75, 30, 'right', '');
$pdf->setFontExt($_fonts['Helvetica'], 8);

// ---------------------------------------------------------------------------------------------------------------
// ---------------------------------------------------------------------------------------------------------------
//			DATE BEFORE


// determine the day before the start of the reporting period
// $datebefore = date_before_report($propertyID,$periodFrom);
// changed above to refer to $periodFrom 20 Mar 2009
// $datebefore = date_before_report($propertyID,$periodTo);


// obtain the day before the begining of the year
$dateBeforeStartYr = date_before_start_FY($propertyID, $periodTo);
$dateBeforeStart = $dateBeforeStartYr ? $dateBeforeStartYr['date'] : null;

// #####################################################################################
// ######  NET CASH INFLOWS/(OUTFLOWS)  NET CASH INFLOWS/(OUTFLOWS)#####################
// #####################################################################################

$total_netcash = $net + $gst_subtotal;
$total_netcashA = $netY + $gst_subtotalA;

// echo "$total_netcashA = $netY + $gst_subtotalA <br />";


$total_netcash_display = formatting($total_netcash, 2);
$total_netcashA_display = formatting($total_netcashA, 2);


// ####################################################################################
// ################## CASH RECONCILIATION #############################################
// ####################################################################################


// $receipts = total_cash_receipts($propertyID,$startFinancialYear,$previousPeriodTo);
// $payments = total_cash_payments($propertyID,$startFinancialYear,$previousPeriodTo);

// $receiptsA = total_cash_receipts($propertyID,STARTDATE,$dateBeforeStart); //echo "receiptsA - $receiptsA";
// $paymentsA = total_cash_payments($propertyID,STARTDATE,$dateBeforeStart);


// $receiptsAFund = total_cash_receipts_fund($propertyID,STARTDATE,$financialPeriodToPY); // added by arjay 2017-27-07
// $receiptsFund = total_cash_receipts_fund($propertyID,$startFinancialYear,$previousPeriodTo); // added by arjay 2017-27-07
// $paymentsAFund = total_cash_payments_fund($propertyID,STARTDATE,$financialPeriodToPY); // added by arjay 2017-01-08
// $paymentsFund = total_cash_payments_fund($propertyID,$startFinancialYear,$previousPeriodTo); // added by arjay 2017-01-08

$receiptsAFund = total_cash_receipts_fund(
    $propertyID,
    STARTDATE,
    $financialPeriodToPY,
    'net'
); // added by arjay 2017-27-07
$paymentsAFund = total_cash_payments_fund(
    $propertyID,
    STARTDATE,
    $financialPeriodToPY,
    'net'
); // added by arjay 2017-01-08

$receiptsFund = total_cash_receipts_fund(
    $propertyID,
    $startFinancialYear,
    $previousPeriodTo,
    'net'
); // added by arjay 2017-27-07
$paymentsFund = total_cash_payments_fund(
    $propertyID,
    $startFinancialYear,
    $previousPeriodTo,
    'net'
); // added by arjay 2017-01-08

$receiptsA = total_cash_receipts(
    $propertyID,
    STARTDATE,
    $financialPeriodToPY
) - $receiptsAFund; // echo "receiptsA - $receiptsA";
$paymentsA = total_cash_payments($propertyID, STARTDATE, $financialPeriodToPY) - $paymentsAFund;

$receipts = total_cash_receipts(
    $propertyID,
    $startFinancialYear,
    $previousPeriodTo
) - $receiptsFund; // //////////receipts current year up to start of reporting period ::: was $datebefore
$payments = total_cash_payments($propertyID, $startFinancialYear, $previousPeriodTo) - $paymentsFund;

// echo $receipts . ' ' . $payments . '<br/>';
// echo $receiptsA . ' ' . $paymentsA . '<br/>';


$diffY = $receipts - $payments;
$diffA = $receiptsA - $paymentsA;

// add the below section in 19 Mar 09 to take into account when reporting multiple periods starting from beginning of financial year
$diff = $previousPeriodTo == $dateBeforeStart ? $diffA : $diffA + $diffY;

$diff_display = formatting($diff);
$diffA_display = formatting($diffA);
// NOTE need also to do it to $dateBeforeStart   - day before start of fin year - above is from beginning of the year to day before reporting period

$subtotal = $diff + $total_netcash;
$subtotalA = $diffA + $total_netcashA;


// echo "subtotal($subtotal) - subtotalA($subtotalA)<br />";

$subtotal_display = formatting($subtotal);
$subtotalA_display = formatting($subtotalA);

// #####################################################################################
// -----------------------------OWNER REMITTANCES--------------------------------------
// #####################################################################################

// $category = "'EXPOWNREMI', 'BSPMTREMI'";
$category = ['EXPOWNREMI', 'BSPMTREMI'];

// Current month
$pmts_duringM_displayM = totalExpensesPerSubgroup($propertyID, $periodFrom, $periodTo, $category);
$pmts_duringMnt_display = $pmts_duringM_displayM['gross_amount'];
$pmts_duringM_display = formatting($pmts_duringMnt_display);

// Year to date includes take on balances
$openPMTS = take_on_balance_own_remit($propertyID, 'EXPOPT', $startFinancialYear, $periodTo);
$pmts_duringY_displayY = totalExpensesPerSubgroup($propertyID, $startFinancialYear, $periodTo, $category);
$pmts_duringYear_display = $pmts_duringY_displayY['gross_amount'];
$pmts_duringYr_display = $pmts_duringYear_display - $openPMTS;
$pmts_duringY_display = formatting($pmts_duringYr_display);

// Each owners remittance
$owner_remittances = expenses_detail($propertyID, $periodFrom, $periodTo, 'EXPOPT');
$count_number = count($owner_remittances ?? []);

$ownerRemittanceList = [];
foreach ($owner_remittances as $key => $each_owner_remit) {
    // this is in an array with possibly multiple lines
    $ownerRemittanceList[$key]['name'] = @substr($owner_remittances['name'], 0, 15);
    $ownerRemittanceList[$key]['amount'] = @$owner_remittances['amount'];
    // ////////NOTE WE NEED TO PUT IN SOME PDF FORMATTING IN THIS LOOP
}

$lineOffset = 0;
foreach ($ownerRemittanceList as $ownerRemittanceItem) {
    // echo $ownerRemittanceItem['amount'];
    $lineOffset += 10;
}

// #####################################################################################
// -----------------------------CASH BALANCE AT THE END OF THE YEAR--------------------
// #####################################################################################


// #####################################################################################
// ###################################END###############################################
// ##############################CALCULATIONS###########################################
// #####################################################################################

$pmts_duringM = getPaymentsToOwners($propertyID, $periodFrom, $periodTo); // trim($query_result["amount"]);
$pmts_duringY = getPaymentsToOwners($propertyID, $startFinancialYear, $periodTo);
$pmts_duringMBS = getPaymentsToOwnersBSonly($propertyID, $periodFrom, $periodTo);
$pmts_duringYBS = getPaymentsToOwnersBSonly($propertyID, $startFinancialYear, $periodTo);

$pmts_duringY -= $openPMTS;

$pmts_duringM_display = $pmts_duringM; // * (-1);
$pmts_duringY_display = $pmts_duringY; // * (-1);
$pmts_duringM_display = formatting($pmts_duringM_display);
$pmts_duringY_display = formatting($pmts_duringY_display);

$pdf->showBoxed($diff_display, 202, 160 - $line, 75, 30, 'right', '');
$pdf->showBoxed($diffA_display, 475, 160 - $line, 75, 30, 'right', '');

$pdf->showBoxed($subtotal_display, 202, 130 - $line, 75, 30, 'right', '');
$pdf->showBoxed($subtotalA_display, 475, 130 - $line, 75, 30, 'right', '');

// $pdf->showBoxed ($pmts_duringM_display, 202, 88-$line, 75, 30, "right", "");
// $pdf->showBoxed ($pmts_duringY_display, 475, 88-$line, 75, 30, "right", "");


$noneop_rec_duringM = dbTotalIncomePerGroupExFund(
    $propertyID,
    $periodFrom,
    $periodTo,
    'BSREC'
); // trim($query_result["amount"]);
$noneop_rec_duringY = dbTotalIncomePerGroupExFund($propertyID, $startFinancialYear, $periodTo, 'BSREC');

$noneop_pay_duringMnt_display = $noneop_pay_duringM['gross_amount'] + $noneop_pay_duringM['tax_amount'] - $pmts_duringMBS; // * (-1);
$noneop_pay_duringYer_display = $noneop_pay_duringY['gross_amount'] + $noneop_pay_duringY['tax_amount'] - $pmts_duringYBS; // * (-1);
$noneop_rec_duringMnt_display = $noneop_rec_duringM['net_amount']; // + $noneop_rec_duringM['tax_amount'];// * (-1);
$noneop_rec_duringYer_display = $noneop_rec_duringY['net_amount']; // + $noneop_rec_duringY['tax_amount'];// * (-1);

$noneop_pay_duringM_display = formatting($noneop_pay_duringMnt_display);
$noneop_pay_duringY_display = formatting($noneop_pay_duringYer_display);
$noneop_rec_duringM_display = formatting($noneop_rec_duringMnt_display);
$noneop_rec_duringY_display = formatting($noneop_rec_duringYer_display);

if ($noneop_rec_duringMnt_display != 0 || $noneop_rec_duringYer_display != 0) {
    $pdf->showBoxed('Non-operating Receipts', 22, 118 - $line, 275, 30, 'left', '');
    $pdf->showBoxed($noneop_rec_duringM_display, 202, 118 - $line, 75, 30, 'right', '');
    $pdf->showBoxed($noneop_rec_duringY_display, 475, 118 - $line, 75, 30, 'right', '');
} else {
    $line -= 15;
}

if ($noneop_pay_duringMnt_display != 0 || $noneop_pay_duringYer_display != 0) {
    $pdf->showBoxed('Non-operating Payments', 22, 103 - $line, 275, 30, 'left', '');
    $pdf->showBoxed($noneop_pay_duringM_display, 202, 103 - $line, 75, 30, 'right', '');
    $pdf->showBoxed($noneop_pay_duringY_display, 475, 103 - $line, 75, 30, 'right', '');
} else {
    $line -= 15;
}


// Moved by: DK
$pdf->showBoxed('Payments to owner/s during period/year', 22, 88 - $line, 275, 30, 'left', '');
$pdf->showBoxed($pmts_duringM_display, 202, 88 - $line, 75, 30, 'right', '');
$pdf->showBoxed($pmts_duringY_display, 475, 88 - $line, 75, 30, 'right', '');
$pdf->moveto(215, 90 - $line);
$pdf->lineto(276, 90 - $line);
$pdf->stroke();
$pdf->moveto(215, 92 - $line);
$pdf->lineto(276, 92 - $line);
$pdf->stroke();
$pdf->moveto(490, 90 - $line);
$pdf->lineto(550, 90 - $line);
$pdf->stroke();
$pdf->moveto(490, 92 - $line);
$pdf->lineto(550, 92 - $line);
$pdf->stroke();
$pdf->moveto(215, 107 - $line);
$pdf->lineto(276, 107 - $line);
$pdf->stroke();
$pdf->moveto(490, 107 - $line);
$pdf->lineto(550, 107 - $line);
$pdf->stroke();


$cb = ($subtotal + $pmts_duringMnt_display + $noneop_rec_duringMnt_display + $noneop_pay_duringMnt_display);
$cbA = $pmts_duringYr_display + $subtotalA + $noneop_pay_duringYer_display + $noneop_rec_duringYer_display;
$cb_display = formatting($cb);
$cbA_display = formatting($cbA);

$pdf->showBoxed('Cash balance at end of period/year', 22, 73 - $line, 275, 30, 'left', '');
$pdf->showBoxed($cb_display, 202, 73 - $line, 75, 30, 'right', '');
$pdf->showBoxed($cbA_display, 475, 73 - $line, 75, 30, 'right', '');


// insert tracc footer
$traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'propertyFinancialReport', A4_LANDSCAPE);
$traccFooter->prerender($pdf);

$pdf->end_page_ext('');


// **********************CASH RECEIPTS AND PAYMENT SUMMARY****************


/*** Page 2 - Cash Receipts And Payments Summary **/

// include ('med_page1_propertyReport.php');
include_once __DIR__ . '/functions/mediumReportFunctions.php';

global $dbh;

$pageLimit = 550;
$budgetINCVOTotal_display = 0;
$budgetINCVOYTotal_display = 0;

// if (isset ($view->items['page1']))	{
$page++;
// }

$pdf->begin_page_ext(842, 595, '');
$page_header = 'Cash Receipts Summary';

include __DIR__ . '/functions/mediumReportHeader.php';
if ($logo) {
    generateLogo('landscape');
}

/**
 * $ownerincome = 0;
 * $voinc = 0;
 * $recinc = 0;
 * $ownerincomeA = 0;
 * $voincA = 0;
 * $recincA = 0;
 **/
$text1 = 'Owner Receipts';

$pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
$pdf->showBoxed($text1, 22, 440, 275, 30, 'left', '');
$pdf->setFontExt($_fonts['Helvetica'], 8);

// --------------------------------------------------------------------------
// OWNER RECEIPTS
//

$budgetINCOWNTotal = 0;
$budgetINCOWNYTotal = 0;
$budgetINCOWNYEARTotal = 0;
$budgetINCRECTotal = 0;
$budgetINCRECYTotal = 0;
$budgetINCRECYEARTotal = 0;
$budgetINCVOTotal = 0;
$budgetINCVOYTotal = 0;
$budgetINCVOYEARTotal = 0;

$_totalAllIncome_display = 0;
$_totalAllIncomeA_display = 0;
$_totalAllBudgetIncome_display = 0;
$_totalAllBudgetIncomeA_display = 0;
$_totalAllBudgetIncomeYEAR_display = 0;
$_totalAllVarianceRECEIPTS_display = 0;
$_totalAllVP_display1 = 0;
$_totalAllVarianceY_display = 0;
$_totalAllVPY_display = 0;


// ACCOUNTS FOR INDIV CALCULATIONS
$accSQL = "SELECT DISTINCT pmxd_acc
		FROM pmxd_ar_alloc
		WHERE fund IS NULL AND (pmxd_prop = ?) 
		AND (pmxd_alloc_dt <= CONVERT(datetime, ?, 103)) 
		AND (pmxd_f_type = 'CSH') 		
				AND (pmxd_acc IN
						  (SELECT pmcg_acc
							FROM pmcg_chart_grp
							WHERE (pmcg_grp = 'TRACC2') 
							AND pmcg_subgrp = 'INCOWN'))
			 UNION ALL
			SELECT DISTINCT pmuc_acc
						FROM pmuc_unall_csh
					wHERE (pmuc_prop = ?) 
			AND (pmuc_rcpt_dt <= CONVERT(datetime, ?, 103))
			AND (pmuc_acc <> '')
			AND (pmuc_acc IN(SELECT pmcg_acc
				FROM pmcg_chart_grp
				WHERE (pmcg_grp = 'TRACC2') 
				AND pmcg_subgrp = 'INCOWN'))";
$dbh->selectDatabase($clientDB);
$acc = $dbh->executeScalars($accSQL, [$propertyID, $periodTo, $propertyID, $periodTo]);


$increcownSQL = "SELECT pmcg_acc
		FROM pmcg_chart_grp
		WHERE (pmcg_grp = 'TRACC3')
		AND (pmcg_subgrp = 'INCRECOWN')";


// Retrieve periods
$periodsSQL = '
	SELECT
		pmcp_period, pmcp_year
	FROM
		pmcp_prop_cal
	WHERE
		pmcp_start_dt BETWEEN CONVERT(datetime, ?, 103) AND CONVERT(datetime, ?, 103)
		AND pmcp_end_dt BETWEEN CONVERT(datetime, ?, 103) AND CONVERT(datetime, ?, 103)
		AND pmcp_prop = ?';
$dbh->selectDatabase($clientDB);
$periods = $dbh->executeSet(
    $periodsSQL,
    false,
    true,
    [$periodFrom, $periodTo, $periodFrom, $periodTo, $propertyID]
);
$pmcp_period = null;
$pmcp_year = null;
foreach ($periods as $v) {
    $pmcp_period[] = $v['pmcp_period'];
    $pmcp_year[] = $v['pmcp_year'];
}

$pmcp_period = array_unique($pmcp_period);
$pmcp_year = array_unique($pmcp_year);

// //////////////////////////////////////////////////////////////
// ///////////////////This is where the ammount is set and made
// //////////////////////////////////////////////////////////////


// echo "First BUDGET:" . $budgetACCSQL;


$opBAL_accSQL = "SELECT DISTINCT pmpb_acc as account
        FROM pmpb_p_bal
        WHERE (pmpb_prop = ?)
        AND (pmpb_date BETWEEN CONVERT(datetime, ?, 103) AND CONVERT(datetime, ?, 103))
        AND (pmpb_acc IN (SELECT pmcg_acc
                                    FROM pmcg_chart_grp
                                    WHERE (pmcg_grp = 'TRACC2')
                                    AND (pmcg_subgrp = 'INCOWN'))) AND (pmpb_acc NOT IN (SELECT pmcg_acc
                                    FROM pmcg_chart_grp
                                    WHERE (pmcg_grp = 'TRACC2')
                                    AND (pmcg_subgrp = 'EXPOPT')))
      AND (pmpb_acc <> '0701')
      AND (pmpb_acc <> '0702')";
$dbh->selectDatabase($clientDB);
$openBAL = $dbh->executeScalars($opBAL_accSQL, [$propertyID, $startFinancialYear, $periodTo]);
$params = [];
$budgetACCSQL = '
    SELECT DISTINCT
        pmrp_acc as account
    FROM
        pmrp_b_rev_per
    WHERE
        pmrp_prop = ' . addSQLParam($params, $propertyID) . '
        AND pmrp_per IN (' . addSQLParam($params, $pmcp_period) . ')
        AND pmrp_year IN (' . addSQLParam($params, $pmcp_year) . ")
        AND pmrp_acc IN (SELECT pmcg_acc FROM pmcg_chart_grp WHERE pmcg_grp = 'TRACC2' AND pmcg_subgrp = 'INCOWN')";
$dbh->selectDatabase($clientDB);
$budget = $dbh->executeScalars($budgetACCSQL, $params);

$unAllocCash = "SELECT DISTINCT pmuc_acc
        FROM pmuc_unall_csh
        WHERE (pmuc_prop = ?)
        AND (pmuc_rcpt_dt < CONVERT(datetime, ?, 103))
        AND (pmuc_acc IN
                          (SELECT pmcg_acc
                            FROM pmcg_chart_grp
                            WHERE (pmcg_grp = 'TRACC2') AND pmcg_subgrp = 'INCOWN'))";
$dbh->selectDatabase($clientDB);
$cash = $dbh->executeScalars($unAllocCash, [$propertyID, $periodTo]);
$dbh->selectDatabase($clientDB);
$increcown = $dbh->executeScalars($increcownSQL);

$tax = 0;
$merged = array_unique(array_merge($acc, $cash, $budget, $openBAL, $increcown));
asort($merged);

$limit = 40;
$line = 200;

$budgetINCOWNTotal_display = formatting(0);
$budgetINCOWNYTotal_display = formatting(0);
foreach ($merged as $account) {
    // Get Account Name
    $accSQL = 'SELECT pmca_name FROM pmca_chart WHERE pmca_code = ?';
    $dbh->selectDatabase($clientDB);
    $account_name = $dbh->executeScalar($accSQL, [$account]);

    $account_name = (strlen($account_name) < $limit) ? $account_name : substr($account_name, 0, $limit) . ' ...';

    // OWNER RECEIPTS


    $unallcashOISQL = 'SELECT SUM (pmuc_net_amt) AS amount
			FROM pmuc_unall_csh
			WHERE (pmuc_prop = ?)
			AND (pmuc_acc = ?)
						AND (pmuc_rcpt_dt BETWEEN CONVERT(datetime, ?, 103) AND CONVERT(datetime, ?, 103))';

    $paidGSTFreeSQL = "SELECT COALESCE(SUM(pmxd_alloc_amt), 0) amount 
			FROM pmxd_ar_alloc 
			WHERE (pmxd_f_type = 'CSH') 
			AND (pmxd_tax_amt = 0) 
			AND (pmxd_prop = ?) 
			AND (pmxd_alloc_dt BETWEEN CONVERT(datetime, ?, 103) AND CONVERT(datetime, ?, 103)) 
			AND (pmxd_acc=?)";
    $dbh->selectDatabase($clientDB);
    $paidgstfree = $dbh->executeScalar($paidGSTFreeSQL, [$propertyID, $periodFrom, $periodTo, $account]);

    $ownerincSQL = "SELECT COALESCE(SUM(pmxd_alloc_amt), 0) amount, SUM (pmxd_tax_amt) as tax_amount
        FROM pmxd_ar_alloc 
        WHERE (pmxd_acc = ?)
                        AND (pmxd_f_type = 'CSH')  
            AND (pmxd_prop = ?) 
            AND (pmxd_alloc_dt BETWEEN CONVERT(datetime, ?, 103) AND CONVERT(datetime, ?, 103))";
    $dbh->selectDatabase($clientDB);
    $ownerinc_result = $dbh->executeSingle($ownerincSQL, [$account, $propertyID, $periodFrom, $periodTo]);


    $owner_income = $ownerinc_result['amount'];
    $owner_tax_income = $ownerinc_result['tax_amount'];
    $dbh->selectDatabase($clientDB);
    $ownerUC_income = $dbh->executeScalar($unallcashOISQL, [$propertyID, $account, $periodFrom, $periodTo]);


    $owner_income = $owner_income - $owner_tax_income + $ownerUC_income;
    $ownerincome += $owner_income;

    $ownerPOS_income = $owner_income * (-1);
    $owner_display = formatting($ownerPOS_income, 2);
    $tax += $owner_tax_income;

    // YTD1

    $ownerincASQL = "SELECT COALESCE(SUM(pmxd_alloc_amt), 0) AS amount, COALESCE(SUM(pmxd_tax_amt), 0) as tax_amount
		FROM pmxd_ar_alloc 
		WHERE (pmxd_acc = ?)
						AND (pmxd_f_type = 'CSH') 
			AND (pmxd_prop = ?) 
			AND (pmxd_alloc_dt BETWEEN CONVERT(datetime, ?, 103) AND CONVERT(datetime, ?, 103))";
    $dbh->selectDatabase($clientDB);
    $ownerincA_result = $dbh->executeSingle(
        $ownerincASQL,
        [$account, $propertyID, $startFinancialYear, $periodTo]
    );


    $unallcashOISQL = 'SELECT COALESCE(SUM(pmuc_net_amt), 0)  AS amount, COALESCE(SUM(pmuc_tax_amt), 0) AS tax
			FROM pmuc_unall_csh
			WHERE (pmuc_prop = ?)
			AND (pmuc_acc = ?) AND 
			(pmuc_rcpt_dt BETWEEN CONVERT(datetime, ?, 103) AND CONVERT(datetime, ?, 103))';
    $dbh->selectDatabase($clientDB);
    $ownerincUC_result = $dbh->executeScalar(
        $unallcashOISQL,
        [$propertyID, $account, $startFinancialYear, $periodTo]
    );

    $paidGSTFreeASQL = "SELECT COALESCE(SUM(pmxd_alloc_amt), 0) amount 
			FROM pmxd_ar_alloc 
			WHERE (pmxd_tax_amt = 0) 
			AND (pmxd_f_type = 'CSH') 
			AND (pmxd_prop = ?) 
			AND (pmxd_acc<>NULL) 
			AND (pmxd_alloc_dt BETWEEN CONVERT(datetime, ?, 103) AND CONVERT(datetime, ?, 103)) 
			AND (pmxd_acc=?)";
    $dbh->selectDatabase($clientDB);
    $paidGSTFreeA_result = $dbh->executeScalar(
        $paidGSTFreeASQL,
        [$propertyID, $startFinancialYear, $periodTo, $account]
    );


    // OPENING BALANCE FOR EACH ACCOUNT
    $openBalOWNSQL = 'SELECT COALESCE(SUM(pmpb_bal), 0) as amount
		FROM pmpb_p_bal
		WHERE (pmpb_prop = ?) 
		AND (pmpb_date BETWEEN CONVERT(datetime, ?, 103) AND CONVERT(datetime, ?, 103))
		AND (pmpb_acc = ?)';
    $dbh->selectDatabase($clientDB);
    $openBalOWN_result = $dbh->executeScalar(
        $openBalOWNSQL,
        [$propertyID, $startFinancialYear, $periodTo, $account]
    );


    // -----------OPENING BALANCE FIGURES------------------------
    $openBalOWN_income = $openBalOWN_result;
    // ----------------------------------------------------------

    // -----------BUDGET FIGURES ---------------------------------
    $params = [];
    $budgetINCOWNSQL = '
	SELECT
		COALESCE(SUM(pmrp_b_c_amt), 0) as amount
	FROM
		pmrp_b_rev_per
	WHERE
		pmrp_prop = ' . addSQLParam($params, $propertyID) . '
		AND pmrp_per IN (' . addSQLParam($params, $pmcp_period) . ') 
		AND pmrp_year IN (' . addSQLParam($params, $pmcp_year) . ') 
		AND pmrp_acc = ' . addSQLParam($params, $account) . '';
    // echo "Second BUDGET:" . $budgetINCOWNSQL;
    $dbh->selectDatabase($clientDB);
    $budgetINCOWN = $dbh->executeScalar($budgetINCOWNSQL, $params);

    $budgetINCOWNTotal += $budgetINCOWN;
    $budgetINCOWNTotal_display = formatting($budgetINCOWNTotal);
    $budgetINCOWN_display = formatting($budgetINCOWN);
    $params = [];
    $budgetINCOWNYSQL = 'SELECT COALESCE(SUM(pmrp_b_c_amt), 0) AS amount
		FROM pmrp_b_rev_per
		WHERE (pmrp_prop = ' . addSQLParam($params, $propertyID) . ') 
		AND (pmrp_per >= 1) 
		AND (pmrp_per <= ' . addSQLParam($params, $toPeriod) . ')
				  AND pmrp_year IN (' . addSQLParam($params, $pmcp_year) . ')
				AND (pmrp_acc =' . addSQLParam($params, $account) . ')';
    // echo "Third BUDGET:" . $budgetINCOWNYSQL;
    $dbh->selectDatabase($clientDB);
    $budgetINCOWNY_result = $dbh->executeScalar($budgetINCOWNYSQL, $params);
    $budgetINCOWNY = $budgetINCOWNY_result;
    $budgetINCOWNY_display = formatting($budgetINCOWNY, 2);
    $budgetINCOWNYTotal += $budgetINCOWNY;
    $budgetINCOWNYTotal_display = formatting($budgetINCOWNYTotal);
    $params = [];
    $budgetINCOWNYEARSQL = 'SELECT COALESCE(SUM(pmrp_b_c_amt), 0) AS amount
		FROM pmrp_b_rev_per
		WHERE (pmrp_prop = ' . addSQLParam($params, $propertyID) . ') 
		AND (pmrp_per BETWEEN 1 AND 12)AND (pmrp_year IN (' . addSQLParam($params, $pmcp_year) . '))
				AND (pmrp_acc = ' . addSQLParam($params, $account) . ')';
    // echo "Fourth BUDGET:" . $budgetINCOWNYEARSQL;
    $dbh->selectDatabase($clientDB);
    $budgetINCOWNYEAR = (float) $dbh->executeScalar($budgetINCOWNYEARSQL, $params);
    $budgetINCOWNYEAR_display = formatting($budgetINCOWNYEAR, 2);
    $budgetINCOWNYEARTotal += $budgetINCOWNYEAR;

    $ownerUC_incomeA = (float) $ownerincUC_result;
    $ownerA_income = (float) $ownerincA_result['amount'];

    $ownerA_tax_income = (float) $ownerincA_result['tax_amount'];

    $ownerA_income = $ownerA_income - $ownerA_tax_income + $ownerUC_incomeA - $openBalOWN_income;

    $ownerAPOS_income = $ownerA_income * (-1);

    $ownerA_display = formatting($ownerAPOS_income, 2);

    $variance = $ownerPOS_income - $budgetINCOWN;
    $variance_display = formatting($variance);
    if ($budgetINCOWN == 0) {
        if ($ownerPOS_income == 0) {
            $varianceP = 0;
        } else {
            $varianceP = 100;
            if ($variance < 0) {
                $varianceP = -100;
            }
        }
    } else {
        $varianceP = $variance / $budgetINCOWN * 100;
    }

    $varianceP_display = formatting($varianceP);


    $varianceY = $ownerAPOS_income - $budgetINCOWNY;
    $varianceY_display = formatting($varianceY);
    if ($budgetINCOWNY == 0) {
        $variancePY = 100;
        if ($varianceY < 0) {
            $variancePY = -100;
        }
    } else {
        $variancePY = $varianceY / $budgetINCOWNY * 100;
    }

    $variancePY_display = formatting($variancePY);

    if ($owner_income == 0 && $ownerA_income == 0 && $budgetINCOWN == 0 && $budgetINCOWNY == 0 && $budgetINCOWNYEAR_display == 0) {
        // DON NOT DISPLAY LINE
    } else {
        $pdf->showBoxed($account, 22, 640 - $line, 400, 20, 'left', '');
        $pdf->showBoxed($account_name, 50, 640 - $line, 400, 20, 'left', '');
        $pdf->showBoxed($owner_display, 202, 640 - $line, 75, 20, 'right', '');
        $pdf->showBoxed($budgetINCOWN_display, 272, 640 - $line, 75, 20, 'right', '');
        $pdf->showBoxed($budgetINCOWNY_display, 550, 640 - $line, 75, 20, 'right', '');
        $pdf->showBoxed($budgetINCOWNYEAR_display, 745, 640 - $line, 75, 20, 'right', '');
        $pdf->showBoxed($variance_display, 330, 640 - $line, 75, 20, 'right', '');
        $pdf->showBoxed($varianceP_display, 385, 640 - $line, 75, 20, 'right', '');
        $pdf->showBoxed($varianceY_display, 620, 640 - $line, 75, 20, 'right', '');
        $pdf->showBoxed($variancePY_display, 665, 640 - $line, 75, 20, 'right', '');

        $pdf->showBoxed($ownerA_display, 475, 640 - $line, 75, 20, 'right', '');

        $line += 10;
        if ($line > $pageLimit) {
            _newPage($pdf, $line, $page);
        }
    }
}

$ownergst = "SELECT COALESCE(SUM(pmxd_tax_amt), 0) AS amount
		FROM pmxd_ar_alloc
		WHERE (pmxd_f_type = 'CSH') 
		AND (pmxd_prop = ?) 
		AND (pmxd_acc IN
					  (SELECT pmcg_acc
					   FROM pmcg_chart_grp
					   WHERE (pmcg_grp = 'TRACC2') 
					   AND (pmcg_subgrp = 'INCVO') 
					   OR (pmcg_subgrp = 'INCDR') 
					   OR (pmcg_subgrp = 'INCOWN'))) 
				AND (pmxd_alloc_dt BETWEEN CONVERT(datetime, ?, 103) AND CONVERT(datetime, ?, 103))";


$dbh->selectDatabase($clientDB);
$ownergst_result = $dbh->executeScalar($ownergst, [$propertyID, $periodFrom, $periodTo]);
$gst = $ownergst_result;

$unAllocCash = "SELECT COALESCE(SUM(pmuc_amt), 0) AS amount
		FROM pmuc_unall_csh 
		WHERE (pmuc_prop = ?) 
		AND (pmuc_rcpt_dt BETWEEN CONVERT(datetime, ?, 103) AND CONVERT(datetime, ?, 103))
		AND (pmuc_acc = '0701')";
$dbh->selectDatabase($clientDB);
$cash_result = $dbh->executeScalar($unAllocCash, [$propertyID, $periodFrom, $periodTo]);
$gst_cash = $cash_result;

$gst = ($gst - $gst_cash) * (-1);
$gst_display = formatting($gst, 2);
$ownergstA = "SELECT COALESCE(SUM(pmxd_tax_amt), 0) AS amount
		FROM pmxd_ar_alloc
		WHERE (pmxd_f_type = 'CSH') 
		AND (pmxd_prop = ?) 
		AND (pmxd_acc IN
						  (SELECT pmcg_acc
							FROM pmcg_chart_grp
							WHERE (pmcg_grp = 'TRACC2') 
							AND (pmcg_subgrp = 'INCVO') 
							OR (pmcg_subgrp = 'INCDR') 
							OR (pmcg_subgrp = 'INCOWN'))) 
				AND (pmxd_alloc_dt BETWEEN CONVERT(datetime, ?, 103) AND CONVERT(datetime, ?, 103))";

$unAllocCashA = "SELECT COALESCE(SUM(pmuc_amt), 0) AS amount
		FROM pmuc_unall_csh 
		WHERE (pmuc_prop = ?) 
		AND (pmuc_rcpt_dt BETWEEN CONVERT(datetime, ?, 103) AND CONVERT(datetime, ?, 103))
		AND (pmuc_acc = '0701')";
$dbh->selectDatabase($clientDB);
$cashA_result = $dbh->executeScalar($unAllocCashA, [$propertyID, $startFinancialYear, $periodTo]);
$gstA_cash = $cashA_result;
$dbh->selectDatabase($clientDB);
$ownergstA_result = $dbh->executeScalar($ownergstA, [$propertyID, $startFinancialYear, $periodTo]);
$gstA = $ownergstA_result;


$gstA = ($gstA + $gstA_cash) * (-1);
$gstA_display = formatting($gstA, 2);
$line -= 10;


// $ownerincome = $ownerincome - $gst;
$ownerincomePOS = $ownerincome * (-1);

$ownerincomeA = dbTotalIncomePerGroupExFund($propertyID, $startFinancialYear, $periodTo, 'INCOWN');
$ownerincomeAPOS = bcadd($ownerincomeA['net_amount'], $own_takeon['amount'], 2);

// $ownerincomeAPOS = $ownerincomeA * (-1);
$total_oi = formatting($ownerincomePOS, 2);
$total_oiA = formatting($ownerincomeAPOS, 2);

$varianceT = $ownerincomePOS - $budgetINCOWNTotal;
$varianceT_display = formatting($varianceT);
if ($budgetINCOWNTotal == 0) {
    $varianceTP = 100;
    if ($varianceT < 0) {
        $varianceTP = -100;
    }
} else {
    $varianceTP = $varianceT / $budgetINCOWNTotal * 100;
}

$varianceTP_display = formatting($varianceTP);

$varianceTY = $ownerincomeAPOS - $budgetINCOWNYTotal;
$varianceTY_display = formatting($varianceTY);
if ($budgetINCOWNYTotal == 0) {
    $varianceTPY = 100;
    if ($varianceTY < 0) {
        $varianceTPY = -100;
    }

    if ($ownerincomeAPOS == 0) {
        $varianceTPY = 0;
    }
} else {
    $varianceTPY = $varianceTY / $budgetINCOWNYTotal * 100;
}

$varianceTPY_display = formatting($varianceTPY);

$budgetINCOWNYEARTotal_display = formatting($budgetINCOWNYEARTotal);

if ($total_oi == '0.00' && $total_oiA == '0.00' && $budgetINCOWNTotal_display == '0.00' && $budgetINCOWNYTotal_display == '0.00' && $budgetINCOWNYEARTotal_display == '0.00') {
    $line -= 10;
    $pdf->setColorExt('both', 'rgb', 1, 1, 1, 0);
    $pdf->rect(20, 634 - $line, 170, 15);
    $pdf->fill();
    $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
    $line -= 20;
} else {
    $line += 2;
    $pdf->setColorExt('fill', 'rgb', 0.88, 0.88, 0.88, 0);
    $pdf->rect(215, 630 - $line, 609, 20);
    $pdf->fill();
    $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);


    $pdf->moveto(215, 650 - $line);
    $pdf->lineto(824, 650 - $line);
    $pdf->stroke();

    $pdf->showBoxed($total_oi, 202, 626 - $line, 75, 20, 'right', '');
    $pdf->showBoxed($budgetINCOWNTotal_display, 272, 626 - $line, 75, 20, 'right', '');
    $pdf->showBoxed($budgetINCOWNYTotal_display, 550, 626 - $line, 75, 20, 'right', '');
    $pdf->showBoxed($budgetINCOWNYEARTotal_display, 745, 626 - $line, 75, 20, 'right', '');
    $pdf->showBoxed($varianceT_display, 330, 626 - $line, 75, 20, 'right', '');
    $pdf->showBoxed($varianceTP_display, 385, 626 - $line, 75, 20, 'right', '');
    $pdf->showBoxed($varianceTY_display, 620, 626 - $line, 75, 20, 'right', '');
    $pdf->showBoxed($varianceTPY_display, 665, 626 - $line, 75, 20, 'right', '');
    $pdf->showBoxed($total_oiA, 475, 626 - $line, 75, 20, 'right', '');

    $pdf->moveto(215, 630 - $line);
    $pdf->lineto(824, 630 - $line);
    $pdf->stroke();
    if (strpos($total_oi, ')')) {
        $total_oi = str_replace(')', '', $total_oi);
        $total_oi = str_replace('(', '-', $total_oi);
    } else {

    }

    if (strpos($total_oiA, ')')) {
        $total_oiA = str_replace(')', '', $total_oiA);
        $total_oiA = str_replace('(', '-', $total_oiA);
    } else {

    }

    if (strpos($budgetINCOWNTotal_display, ')')) {
        $budgetINCOWNTotal_display = str_replace(')', '', $budgetINCOWNTotal_display);
        $budgetINCOWNTotal_display = str_replace('(', '-', $budgetINCOWNTotal_display);
    } else {

    }

    if (strpos($budgetINCOWNYTotal_display, ')')) {
        $budgetINCOWNYTotal_display = str_replace(')', '', $budgetINCOWNYTotal_display);
        $budgetINCOWNYTotal_display = str_replace('(', '-', $budgetINCOWNYTotal_display);
    } else {

    }

    if (strpos($budgetINCOWNYEARTotal_display, ')')) {
        $budgetINCOWNYEARTotal_display = str_replace(')', '', $budgetINCOWNYEARTotal_display);
        $budgetINCOWNYEARTotal_display = str_replace('(', '-', $budgetINCOWNYEARTotal_display);
    } else {

    }

    if (strpos($varianceT_display, ')')) {
        $varianceT_display = str_replace(')', '', $varianceT_display);
        $varianceT_display = str_replace('(', '-', $varianceT_display);
    } else {

    }

    if (strpos($varianceTP_display, ')')) {
        $varianceTP_display = str_replace(')', '', $varianceTP_display);
        $varianceTP_display = str_replace('(', '-', $varianceTP_display);
    } else {

    }

    if (strpos($varianceTY_display, ')')) {
        $varianceTY_display = str_replace(')', '', $varianceTY_display);
        $varianceTY_display = str_replace('(', '-', $varianceTY_display);
    } else {

    }

    if (strpos($varianceTPY_display, ')')) {
        $varianceTPY_display = str_replace(')', '', $varianceTPY_display);
        $varianceTPY_display = str_replace('(', '-', $varianceTPY_display);
    } else {

    }

    $_totalAllIncome_display += floatval(str_replace(',', '', $total_oi));
    $_totalAllIncomeA_display += floatval(str_replace(',', '', $total_oiA));
    $_totalAllBudgetIncome_display += floatval(
        str_replace(',', '', $budgetINCOWNTotal_display)
    );
    $_totalAllBudgetIncomeA_display += floatval(
        str_replace(',', '', $budgetINCOWNYTotal_display)
    );
    $_totalAllBudgetIncomeYEAR_display += floatval(
        str_replace(',', '', $budgetINCOWNYEARTotal_display)
    );
    $_totalAllVarianceRECEIPTS_display += floatval(
        str_replace(',', '', $varianceT_display)
    );
    $_totalAllVP_display1 += floatval(str_replace(',', '', $varianceTP_display));
    $_totalAllVarianceY_display += floatval(str_replace(',', '', $varianceTY_display));
    $_totalAllVPY_display += floatval(str_replace(',', '', $varianceTPY_display));
}


// --------------------------------------------------------------------------
// RECOVERABLE RECEIPTS
//
// ACCOUNTS FOR INDIV CALCULATIONS
$accSQL = "SELECT DISTINCT pmxd_acc
		FROM pmxd_ar_alloc
		WHERE fund IS NULL AND (pmxd_prop = ?) 
		AND (pmxd_alloc_dt <= CONVERT(datetime, ?, 103)) 
		AND (pmxd_f_type = 'CSH')  
				AND (pmxd_acc IN
						  (SELECT pmcg_acc
							FROM pmcg_chart_grp
							WHERE (pmcg_grp = 'TRACC2') 
							AND pmcg_subgrp = 'INCDR'))
			UNION ALL
			SELECT DISTINCT pmuc_acc
						FROM pmuc_unall_csh
					wHERE (pmuc_prop = ?) 
			AND (pmuc_rcpt_dt < CONVERT(datetime, ?, 103))
			AND (pmuc_acc <> '')
			AND (pmuc_acc IN(SELECT pmcg_acc
				FROM pmcg_chart_grp
				WHERE (pmcg_grp = 'TRACC2') 
				AND pmcg_subgrp = 'INCDR'))";
$dbh->selectDatabase($clientDB);
$accSQL_result = $dbh->executeScalars($accSQL, [$propertyID, $periodTo, $propertyID, $periodTo]);


$opBAL_accSQL = "SELECT DISTINCT pmpb_acc as account
		FROM pmpb_p_bal
		WHERE (pmpb_prop = ?)
		AND (pmpb_date BETWEEN CONVERT(datetime, ?, 103) AND CONVERT(datetime, ?, 103))
		AND (pmpb_acc IN (SELECT pmcg_acc
									FROM pmcg_chart_grp
									WHERE (pmcg_grp = 'TRACC2')
									AND (pmcg_subgrp = 'INCDR')))";
$dbh->selectDatabase($clientDB);
$opBALacc_result = $dbh->executeScalars($opBAL_accSQL, [$propertyID, $startFinancialYear, $periodTo]);


$params = [];
$budgetACCSQL = 'SELECT DISTINCT pmrp_acc AS account
FROM         pmrp_b_rev_per
WHERE     (pmrp_prop = ' . addSQLParam($params, $propertyID) . ') AND pmrp_per IN
						  (' . addSQLParam($params, $pmcp_period) . ') AND pmrp_year IN (' . addSQLParam(
    $params,
    $pmcp_year
) . ") AND (pmrp_acc IN
						  (SELECT pmcg_acc
							FROM pmcg_chart_grp
							WHERE (pmcg_grp = 'TRACC2') AND pmcg_subgrp = 'INCDR'))";
// echo "5th BUDGET:" . $budgetACCSQL;
$dbh->selectDatabase($clientDB);
$budget_result = $dbh->executeScalars($budgetACCSQL, $params);
$acc = array_merge($accSQL_result, $opBALacc_result, $budget_result);
$merged = array_unique($acc);

$fundAccounts = dbGetPropertyFundAccounts($propertyID);

$merged = array_diff($merged, $fundAccounts);


asort($merged);
$line -= 3;
$pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
$pdf->showBoxed('Recoverable Receipts', 22, 600 - $line, 400, 20, 'left', '');
$pdf->setFontExt($_fonts['Helvetica'], 8);

// remove fund accounts

foreach ($merged as $account) {
    // Get Account Name
    $accSQL = 'SELECT pmca_name FROM pmca_chart WHERE pmca_code = ?';
    $dbh->selectDatabase($clientDB);
    $acc_result = $dbh->executeScalar($accSQL, [$account]);
    $account_name = $acc_result;
    $account_name = (strlen($account_name) < $limit) ? $account_name : substr($account_name, 0, $limit) . ' ...';


    // OWNER RECEIPTS
    $recSQL = "SELECT COALESCE(SUM(pmxd_alloc_amt), 0) amount, SUM (pmxd_tax_amt) as tax_amount
		FROM pmxd_ar_alloc 
		WHERE (pmxd_acc = ?)
						AND (pmxd_f_type = 'CSH') 
			AND (pmxd_prop = ?) 
			AND (pmxd_alloc_dt BETWEEN CONVERT(datetime, ?, 103) AND CONVERT(datetime, ?, 103))";

    $unallcashRECSQL = 'SELECT SUM (pmuc_net_amt) AS amount
			FROM pmuc_unall_csh
			WHERE (pmuc_prop = ?)
			AND (pmuc_acc = ?)
						AND (pmuc_rcpt_dt BETWEEN CONVERT(datetime, ?, 103) AND CONVERT(datetime, ?, 103))';


    $dbh->selectDatabase($clientDB);
    $rec_result = $dbh->executeSingle($recSQL, [$account, $propertyID, $periodFrom, $periodTo]);
    $dbh->selectDatabase($clientDB);
    $recUC_result = $dbh->executeScalar($unallcashRECSQL, [$propertyID, $account, $periodFrom, $periodTo]);
    $rec_income = $rec_result['amount'];
    $rec_tax_income = $rec_result['tax_amount'];
    $recUC_income = $recUC_result;

    $rec_income = $rec_income + $recUC_income - $rec_tax_income;
    $recinc += $rec_income;
    $recPOS_income = $rec_income * (-1);
    $rec_display = formatting($recPOS_income, 2);

    // YTD2

    $recASQL = "SELECT COALESCE(SUM(pmxd_alloc_amt), 0) AS amount, COALESCE(SUM(pmxd_tax_amt), 0) as tax_amount
		FROM pmxd_ar_alloc 
		WHERE (pmxd_acc = ?)
						AND (pmxd_f_type = 'CSH') 
			AND (pmxd_prop = ?) 
			AND (pmxd_alloc_dt BETWEEN CONVERT(datetime, ?, 103) AND CONVERT(datetime, ?, 103))";
    $dbh->selectDatabase($clientDB);
    $recA_result = $dbh->executeSingle($recASQL, [$account, $propertyID, $startFinancialYear, $periodTo]);


    // OPENING BALANCE FOR EACH ACCOUNT
    $openBalOWNSQL = 'SELECT COALESCE(SUM(pmpb_bal), 0) as amount
		FROM pmpb_p_bal
		WHERE (pmpb_prop = ?) 
		AND (pmpb_date BETWEEN CONVERT(datetime, ?, 103) AND CONVERT(datetime, ?, 103))
		AND (pmpb_acc = ?)';
    $dbh->selectDatabase($clientDB);
    $openBalOWN_result = $dbh->executeScalar(
        $openBalOWNSQL,
        [$propertyID, $startFinancialYear, $periodTo, $account]
    );

    // -----------OPENING BALANCE FIGURES------------------------
    $openBalDR_income = $openBalOWN_result;
    // ----------------------------------------------------------

    // -----------BUDGET FIGURES ---------------------------------
    $params = [];
    $budgetINCRECSQL = 'SELECT COALESCE(SUM(pmrp_b_c_amt), 0) AS amount
		FROM pmrp_b_rev_per
		WHERE (pmrp_prop = ' . addSQLParam($params, $propertyID) . ') 
		AND (pmrp_per IN (' . addSQLParam($params, $pmcp_period) . ')) 
				AND (pmrp_year IN (' . addSQLParam($params, $pmcp_year) . ')) 
				AND (pmrp_acc = ' . addSQLParam($params, $account) . ')';
    // echo "6th BUDGET:" . $budgetINCRECSQL;
    $dbh->selectDatabase($clientDB);
    $budgetINCREC_result = $dbh->executeScalar($budgetINCRECSQL, $params);
    $budgetINCREC = $budgetINCREC_result;
    $budgetINCRECTotal += $budgetINCREC;
    $budgetINCRECTotal_display = formatting($budgetINCRECTotal);
    $budgetINCREC_display = formatting($budgetINCREC);

    $params = [];
    $budgetINCRECYSQL = 'SELECT COALESCE(SUM(pmrp_b_c_amt), 0) AS amount
		FROM pmrp_b_rev_per
		WHERE (pmrp_prop = ' . addSQLParam($params, $propertyID) . ') 
		AND (pmrp_per >= 1) 
		AND (pmrp_per <= ' . addSQLParam($params, $toPeriod) . ')
		AND (pmrp_year IN (' . addSQLParam($params, $pmcp_year) . '))
				AND (pmrp_acc =' . addSQLParam($params, $account) . ')';
    // echo "7th BUDGET:" . $budgetINCRECYSQL;
    $dbh->selectDatabase($clientDB);
    $budgetINCRECY_result = $dbh->executeScalar($budgetINCRECYSQL, $params);
    $budgetINCRECY = $budgetINCRECY_result;
    $budgetINCRECY_display = formatting($budgetINCRECY, 2);
    $budgetINCRECYTotal += $budgetINCRECY;
    $budgetINCRECYTotal_display = formatting($budgetINCRECYTotal);

    $params = [];
    $budgetINCRECYEARSQL = 'SELECT COALESCE(SUM(pmrp_b_c_amt), 0) AS amount
		FROM pmrp_b_rev_per
		WHERE (pmrp_prop = ' . addSQLParam($params, $propertyID) . ') 
		AND (pmrp_per BETWEEN 1 AND 12)AND (pmrp_year IN (' . addSQLParam($params, $pmcp_year) . '))
				AND (pmrp_acc = ' . addSQLParam($params, $account) . ')';
    $dbh->selectDatabase($clientDB);
    $budgetINCRECYEAR_result = $dbh->executeScalar($budgetINCRECYEARSQL, $params);
    $budgetINCRECYEAR = $budgetINCRECYEAR_result;
    $budgetINCRECYEAR_display = formatting($budgetINCRECYEAR, 2);
    $budgetINCRECYEARTotal += $budgetINCRECYEAR;
    $params = [];
    $unallcashRECSQL = 'SELECT COALESCE(SUM(pmuc_net_amt), 0) as amount
			FROM pmuc_unall_csh
			WHERE (pmuc_prop = ' . addSQLParam($params, $propertyID) . ')
			AND (pmuc_acc = ' . addSQLParam($params, $account) . ')
			AND (pmuc_period >= 1)
						AND (pmuc_period <= ' . addSQLParam($params, $toPeriod) . ')
				AND (pmuc_year IN (' . addSQLParam($params, $pmcp_year) . '))';
    $dbh->selectDatabase($clientDB);
    $recUC_result = $dbh->executeScalar($unallcashRECSQL, $params);
    $recUC_income = $recUC_result;

    $recA_income = $recA_result['amount'];
    $recA_tax_income = $recA_result['tax_amount'];
    $recA_income = $recA_income + $recUC_income - $recA_tax_income - $openBalDR_income;
    $recAPOS_income = $recA_income * (-1);
    $recA_display = formatting($recAPOS_income, 2);

    $variance = $recPOS_income - $budgetINCREC;
    $variance_display = formatting($variance);
    if ($budgetINCREC == 0) {
        $varianceP = 100;
        if ($variance < 0) {
            $varianceP = -100;
        }

        if ($recPOS_income == 0) {
            $varianceP = 0;
        }
    } else {
        $varianceP = $variance / $budgetINCREC * 100;
    }

    $varianceP_display = formatting($varianceP);


    $varianceY = $recAPOS_income - $budgetINCRECY;
    $varianceY_display = formatting($varianceY);
    if ($budgetINCRECY == 0) {
        $variancePY = 100;
        if ($varianceY < 0) {
            $variancePY = -100;
        }
    } else {
        $variancePY = $varianceY / $budgetINCRECY * 100;
    }

    $variancePY_display = formatting($variancePY);

    if ($rec_income == 0 && $recA_income == 0 && $budgetINCREC == 0 && $budgetINCRECY == 0 && $budgetINCRECYEAR == 0) {
        // DON NOT DISPLAY LINE
    } else {
        $pdf->showBoxed($account, 22, 590 - $line, 400, 20, 'left', '');
        $pdf->showBoxed($account_name, 50, 590 - $line, 400, 20, 'left', '');
        $pdf->showBoxed($rec_display, 202, 590 - $line, 75, 20, 'right', '');
        $pdf->showBoxed($recA_display, 475, 590 - $line, 75, 20, 'right', '');
        $pdf->showBoxed($budgetINCREC_display, 272, 590 - $line, 75, 20, 'right', '');
        $pdf->showBoxed($budgetINCRECY_display, 550, 590 - $line, 75, 20, 'right', '');
        $pdf->showBoxed($budgetINCRECYEAR_display, 745, 590 - $line, 75, 20, 'right', '');
        $pdf->showBoxed($variance_display, 330, 590 - $line, 75, 20, 'right', '');
        $pdf->showBoxed($varianceP_display, 385, 590 - $line, 75, 20, 'right', '');
        $pdf->showBoxed($varianceY_display, 620, 590 - $line, 75, 20, 'right', '');
        $pdf->showBoxed($variancePY_display, 665, 590 - $line, 75, 20, 'right', '');
        $line += 10;

        if ($line > $pageLimit) {
            _newPage($pdf, $line, $page);
        }
    }
}

$recincPOS = $recinc * (-1);
$recincA = dbTotalIncomePerGroupExFund($propertyID, $startFinancialYear, $periodTo, 'INCDR');
$recincomeA = $recincA['net_amount'];

$rec_takeon = take_on_balance($propertyID, 'INCDR', $startFinancialYear, $periodTo);
$recincAPOS = @($recincomeA + $rec_takeon['amount']);

// $recincAPOS = $recincA * (-1);
$total_rec = formatting($recincPOS, 2);
$total_recA = formatting($recincAPOS, 2);


$varianceT = $recincPOS - $budgetINCRECTotal;
$varianceT_display = formatting($varianceT);
if ($budgetINCRECTotal == 0) {
    $varianceTP = 100;
    if ($varianceT < 0) {
        $varianceTP = -100;
    }

    if ($recincPOS == 0) {
        $varianceTP = 0;
    }
} else {
    $varianceTP = $varianceT / $budgetINCRECTotal * 100;
}

$varianceTP_display = formatting($varianceTP);

$varianceTY = $recincAPOS - $budgetINCRECYTotal;
$varianceTY_display = formatting($varianceTY);
if ($budgetINCRECYTotal == 0) {
    $varianceTPY = 100;
    if ($varianceTY < 0) {
        $varianceTPY = -100;
    }

    if ($recincAPOS == 0) {
        $varianceTPY = 0;
    }
} else {
    $varianceTPY = $varianceTY / $budgetINCRECYTotal * 100;
}

$varianceTPY_display = formatting($varianceTPY);

$budgetINCRECYEARTotal_display = formatting($budgetINCRECYEARTotal);
$budgetINCRECYTotal_display = formatting($budgetINCRECYTotal);
$budgetINCRECTotal_display = formatting($budgetINCRECTotal);

if ($budgetINCRECTotal_display == '') {
    $budgetINCRECTotal_display = '0.00';
}

if ($budgetINCRECYTotal_display == '') {
    $budgetINCRECYTotal_display = '0.00';
}

if ($budgetINCRECYEARTotal_display == '') {
    $budgetINCRECYEARTotal_display = '0.00';
}


if ($total_rec == '0.00' && $total_recA == '0.00' && $budgetINCRECTotal_display == '0.00' && $budgetINCRECYTotal_display == '0.00' && $budgetINCRECYEARTotal_display == '0.00') {
    $line -= 10;
    $pdf->setColorExt('both', 'rgb', 1, 1, 1, 0);
    $pdf->rect(20, 595 - $line, 170, 14);
    $pdf->fill();
    $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
    $line -= 20;
} else {
    $line += 2;
    $pdf->setColorExt('fill', 'rgb', 0.88, 0.88, 0.88, 0);
    $pdf->rect(215, 590 - $line, 609, 20);
    $pdf->fill();
    $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);


    $pdf->moveto(215, 610 - $line);
    $pdf->lineto(824, 610 - $line);
    $pdf->stroke();


    $pdf->showBoxed($total_rec, 202, 585 - $line, 75, 20, 'right', '');
    $pdf->showBoxed($total_recA, 475, 585 - $line, 75, 20, 'right', '');
    $pdf->showBoxed($budgetINCRECTotal_display, 272, 585 - $line, 75, 20, 'right', '');
    $pdf->showBoxed($budgetINCRECYTotal_display, 550, 585 - $line, 75, 20, 'right', '');
    $pdf->showBoxed($budgetINCRECYEARTotal_display, 745, 585 - $line, 75, 20, 'right', '');
    $pdf->showBoxed($varianceT_display, 330, 585 - $line, 75, 20, 'right', '');
    $pdf->showBoxed($varianceTP_display, 385, 585 - $line, 75, 20, 'right', '');
    $pdf->showBoxed($varianceTY_display, 620, 585 - $line, 75, 20, 'right', '');
    $pdf->showBoxed($varianceTPY_display, 665, 585 - $line, 75, 20, 'right', '');

    $pdf->moveto(215, 590 - $line);
    $pdf->lineto(824, 590 - $line);
    $pdf->stroke();

    if (strpos($total_rec, ')')) {
        $total_rec = str_replace(')', '', $total_rec);
        $total_rec = str_replace('(', '-', $total_rec);
    } else {

    }

    if (strpos($total_recA, ')')) {
        $total_recA = str_replace(')', '', $total_recA);
        $total_recA = str_replace('(', '-', $total_recA);
    } else {

    }

    if (strpos($budgetINCRECTotal_display, ')')) {
        $budgetINCRECTotal_display = str_replace(')', '', $budgetINCRECTotal_display);
        $budgetINCRECTotal_display = str_replace('(', '-', $budgetINCRECTotal_display);
    } else {

    }

    if (strpos($budgetINCRECYTotal_display, ')')) {
        $budgetINCRECYTotal_display = str_replace(')', '', $budgetINCRECYTotal_display);
        $budgetINCRECYTotal_display = str_replace('(', '-', $budgetINCRECYTotal_display);
    } else {

    }

    if (strpos($budgetINCRECYEARTotal_display, ')')) {
        $budgetINCRECYEARTotal_display = str_replace(')', '', $budgetINCRECYEARTotal_display);
        $budgetINCRECYEARTotal_display = str_replace('(', '-', $budgetINCRECYEARTotal_display);
    } else {

    }

    if (strpos($varianceT_display, ')')) {
        $varianceT_display = str_replace(')', '', $varianceT_display);
        $varianceT_display = str_replace('(', '-', $varianceT_display);
    } else {

    }

    if (strpos($varianceTP_display, ')')) {
        $varianceTP_display = str_replace(')', '', $varianceTP_display);
        $varianceTP_display = str_replace('(', '-', $varianceTP_display);
    } else {

    }

    if (strpos($varianceTY_display, ')')) {
        $varianceTY_display = str_replace(')', '', $varianceTY_display);
        $varianceTY_display = str_replace('(', '-', $varianceTY_display);
    } else {

    }

    if (strpos($varianceTPY_display, ')')) {
        $varianceTPY_display = str_replace(')', '', $varianceTPY_display);
        $varianceTPY_display = str_replace('(', '-', $varianceTPY_display);
    } else {

    }

    $_totalAllIncome_display += floatval(str_replace(',', '', $total_rec));
    $_totalAllIncomeA_display += floatval(str_replace(',', '', $total_recA));
    $_totalAllBudgetIncome_display += floatval(
        str_replace(',', '', $budgetINCRECTotal_display)
    );
    $_totalAllBudgetIncomeA_display += floatval(
        str_replace(',', '', $budgetINCRECYTotal_display)
    );
    $_totalAllBudgetIncomeYEAR_display += floatval(
        str_replace(',', '', $budgetINCRECYEARTotal_display)
    );
    $_totalAllVarianceRECEIPTS_display += floatval(
        str_replace(',', '', $varianceT_display)
    );
    $_totalAllVP_display1 += floatval(str_replace(',', '', $varianceTP_display));
    $_totalAllVarianceY_display += floatval(str_replace(',', '', $varianceTY_display));
    $_totalAllVPY_display += floatval(str_replace(',', '', $varianceTPY_display));
}


// --------------------------------------------------------------------------
// VARIABLE OUTGOINGS RECEIPTS
//
// ACCOUNTS FOR INDIV CALCULATIONS
$line += 40;
$accSQL = "SELECT DISTINCT pmxd_acc
		FROM pmxd_ar_alloc
		WHERE fund IS NULL AND (pmxd_prop = ?) 
		AND (pmxd_alloc_dt <= CONVERT(datetime, ?, 103)) 
		AND (pmxd_f_type = 'CSH') 
				AND (pmxd_acc IN
						  (SELECT pmcg_acc
							FROM pmcg_chart_grp
							WHERE (pmcg_grp = 'TRACC2') 
							AND pmcg_subgrp = 'INCVO'))
			UNION ALL
			SELECT DISTINCT pmuc_acc
						FROM pmuc_unall_csh
					wHERE (pmuc_prop = ?) 
			AND (pmuc_rcpt_dt < CONVERT(datetime, ?, 103))
			AND (pmuc_acc <> '')
			AND (pmuc_acc IN(SELECT pmcg_acc
				FROM pmcg_chart_grp
				WHERE (pmcg_grp = 'TRACC2') 
				AND pmcg_subgrp = 'INCVO'))";
$dbh->selectDatabase($clientDB);
$accSQL_result = $dbh->executeScalars($accSQL, [$propertyID, $periodTo, $propertyID, $periodTo]);


$opBAL_accSQL = "SELECT DISTINCT pmpb_acc as account
		FROM pmpb_p_bal
		WHERE (pmpb_prop = ?)
		AND (pmpb_date BETWEEN CONVERT(datetime, ?, 103) AND CONVERT(datetime, ?, 103))
		AND (pmpb_acc IN (SELECT pmcg_acc
									FROM pmcg_chart_grp
									WHERE (pmcg_grp = 'TRACC2')
									AND (pmcg_subgrp = 'INCVO')))";
$dbh->selectDatabase($clientDB);
$opBALacc_result = $dbh->executeScalars($opBAL_accSQL, [$propertyID, $startFinancialYear, $periodTo]);


$params = [];
$budgetACCSQL = 'SELECT DISTINCT pmrp_acc AS account
FROM         pmrp_b_rev_per
WHERE     (pmrp_prop = ' . addSQLParam($params, $propertyID) . ') AND (pmrp_per IN
						  (' . addSQLParam($params, $pmcp_period) . ')) AND (pmrp_year IN
						  (' . addSQLParam($params, $pmcp_year) . ")) AND (pmrp_acc IN
						  (SELECT pmcg_acc
							FROM pmcg_chart_grp
							WHERE (pmcg_grp = 'TRACC2') AND pmcg_subgrp = 'INCVO'))";
// echo "8th BUDGET:" . $budgetACCSQL;
$dbh->selectDatabase($clientDB);
$budget_result = $dbh->executeScalars($budgetACCSQL, $params);
$acc = array_merge($accSQL_result, $opBALacc_result, $budget_result);
$merged = array_unique($acc);

asort($merged);

$line -= 3;
$pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
$pdf->showBoxed(
    ucwords(strtolower($_SESSION['country_default']['variable_outgoings'])) . ' Receipts',
    22,
    600 - $line,
    400,
    20,
    'left',
    ''
);
$pdf->setFontExt($_fonts['Helvetica'], 8);

foreach ($merged as $account) {
    // Get Account Name
    $accSQL = 'SELECT pmca_name FROM pmca_chart WHERE pmca_code = ?';
    $dbh->selectDatabase($clientDB);
    $acc_result = $dbh->executeScalar($accSQL, [$account]);
    $account_name = $acc_result;
    $account_name = (strlen($account_name) < $limit) ? $account_name : substr($account_name, 0, $limit) . ' ...';

    // VO RECEIPTS


    // OPENING BALANCE FOR EACH ACCOUNT
    $openBalOWNSQL = 'SELECT COALESCE(SUM(pmpb_bal), 0) as amount
		FROM pmpb_p_bal
		WHERE (pmpb_prop = ?) 
		AND (pmpb_date BETWEEN CONVERT(datetime, ?, 103) AND CONVERT(datetime, ?, 103))
		AND (pmpb_acc = ?)';
    $dbh->selectDatabase($clientDB);
    $openBalOWN_result = $dbh->executeScalar(
        $openBalOWNSQL,
        [$propertyID, $startFinancialYear, $periodTo, $account]
    );

    $params = [];
    $budgetINCVOSQL = 'SELECT COALESCE(SUM(pmrp_b_c_amt), 0) AS amount
		FROM pmrp_b_rev_per
		WHERE (pmrp_prop = ' . addSQLParam($params, $propertyID) . ') 
		AND (pmrp_per IN (' . addSQLParam($params, $pmcp_period) . ')) 
				AND (pmrp_year IN (' . addSQLParam($params, $pmcp_year) . ')) 
				AND (pmrp_acc = ' . addSQLParam($params, $account) . ')';
    // echo "9th BUDGET:" . $budgetINCVOSQL;
    $dbh->selectDatabase($clientDB);
    $budgetINCVO_result = $dbh->executeScalar($budgetINCVOSQL, $params);
    $budgetINCVO = $budgetINCVO_result;
    $budgetINCVOTotal += $budgetINCVO;
    $budgetINCVOTotal_display = formatting($budgetINCVOTotal);
    $budgetINCVO_display = formatting($budgetINCVO);

    $params = [];
    $budgetINCVOYSQL = 'SELECT COALESCE(SUM(pmrp_b_c_amt), 0) AS amount
		FROM pmrp_b_rev_per
		WHERE (pmrp_prop = ' . addSQLParam($params, $propertyID) . ') 
		AND (pmrp_per >= 1) 
		AND (pmrp_per <= ' . addSQLParam($params, $toPeriod) . ')
				  AND (pmrp_year IN (' . addSQLParam($params, $pmcp_year) . '))
				AND (pmrp_acc =' . addSQLParam($params, $account) . ')';
    // echo "10th BUDGET:" . $budgetINCVOYSQL;
    $dbh->selectDatabase($clientDB);
    $budgetINCVOY_result = $dbh->executeScalar($budgetINCVOYSQL, $params);
    $budgetINCVOY = $budgetINCVOY_result;
    $budgetINCVOY_display = formatting($budgetINCVOY, 2);
    $budgetINCVOYTotal += $budgetINCVOY;
    $budgetINCVOYTotal_display = formatting($budgetINCVOYTotal);

    $params = [];
    $budgetINCVOYEARSQL = 'SELECT COALESCE(SUM(pmrp_b_c_amt), 0) AS amount
		FROM pmrp_b_rev_per
		WHERE (pmrp_prop = ' . addSQLParam($params, $propertyID) . ') 
		AND (pmrp_per BETWEEN 1 AND 12)AND (pmrp_year IN (' . addSQLParam($params, $pmcp_year) . '))
				AND (pmrp_acc = ' . addSQLParam($params, $account) . ')';
    // echo "11th BUDGET:" . $budgetINCVOYEARSQL;
    $dbh->selectDatabase($clientDB);
    $budgetINCVOYEAR_result = $dbh->executeScalar($budgetINCVOYEARSQL, $params);
    $budgetINCVOYEAR = $budgetINCVOYEAR_result;
    $budgetINCVOYEAR_display = formatting($budgetINCVOYEAR, 2);
    $budgetINCVOYEARTotal += $budgetINCVOYEAR;

    // -----------OPENING BALANCE FIGURES------------------------
    $openBalVO_income = $openBalOWN_result;
    // ----------------------------------------------------------


    $voSQL = "SELECT COALESCE(SUM(pmxd_alloc_amt), 0) amount, SUM (pmxd_tax_amt) as tax_amount
        FROM pmxd_ar_alloc 
        WHERE (pmxd_acc = ?)
                       AND (pmxd_f_type = 'CSH') 
            AND (pmxd_prop = ?) 
            AND (pmxd_alloc_dt BETWEEN CONVERT(datetime, ?, 103) AND CONVERT(datetime, ?, 103))";
    $dbh->selectDatabase($clientDB);
    $vo_result = $dbh->executeSingle($voSQL, [$account, $propertyID, $periodFrom, $periodTo]);

    $unallcashVOSQL = 'SELECT SUM (pmuc_net_amt) AS amount
            FROM pmuc_unall_csh
            WHERE (pmuc_prop = ?)
            AND (pmuc_acc = ?)
                        AND (pmuc_rcpt_dt BETWEEN CONVERT(datetime, ?, 103) AND CONVERT(datetime, ?, 103))';
    $dbh->selectDatabase($clientDB);
    $voUC_result = $dbh->executeScalar($unallcashVOSQL, [$propertyID, $account, $periodFrom, $periodTo]);
    $vo_income = $vo_result['amount'];
    $vo_tax_income = $vo_result['tax_amount'];
    $voUC_income = $voUC_result;

    $vo_income = $vo_income + $voUC_income - $vo_tax_income;
    $voinc += $vo_income;
    $voPOS_income = $vo_income * (-1);
    $vo_display = formatting($voPOS_income, 2);

    // YTD3

    $voASQL = "SELECT COALESCE(SUM(pmxd_alloc_amt), 0) AS amount, COALESCE(SUM(pmxd_tax_amt), 0) as tax_amount
		FROM pmxd_ar_alloc 
		WHERE (pmxd_acc = ?)
						AND (pmxd_f_type = 'CSH') 
			AND (pmxd_prop = ?) 
			AND (pmxd_alloc_dt BETWEEN CONVERT(datetime, ?, 103) AND CONVERT(datetime, ?, 103))";
    $dbh->selectDatabase($clientDB);
    $voA_result = $dbh->executeSingle($voASQL, [$account, $propertyID, $startFinancialYear, $periodTo]);

    $params = [];
    $unallcashVOSQL = 'SELECT COALESCE(SUM(pmuc_net_amt), 0) as amount
			FROM pmuc_unall_csh
			WHERE (pmuc_prop = ' . addSQLParam($params, $propertyID) . ')
			AND (pmuc_acc = ' . addSQLParam($params, $account) . ')
			AND (pmuc_period >= 1)
						AND (pmuc_period <= ' . addSQLParam($params, $toPeriod) . ')
				AND (pmuc_year IN (' . addSQLParam($params, $pmcp_year) . '))';
    $dbh->selectDatabase($clientDB);
    $voUC_result = $dbh->executeScalar($unallcashVOSQL, $params);
    $voUC_income = $voUC_result;
    $voA_income = $voA_result['amount'];
    $voA_tax_income = $voA_result['tax_amount'];
    $voA_income = $voA_income + $voUC_income - $voA_tax_income - $openBalVO_income;
    $voAPOS_income = $voA_income * (-1);
    $voA_display = formatting($voAPOS_income, 2);


    $variance = $voPOS_income - $budgetINCVO;
    $variance_display = formatting($variance);
    if ($budgetINCVO == 0) {
        $varianceP = 100;
        if ($variance < 0) {
            $varianceP = -100;
        }

        if ($voPOS_income == 0) {
            $varianceP = 0;
        }
    } else {
        $varianceP = $variance / $budgetINCVO * 100;
    }

    $varianceP_display = formatting($varianceP);


    $varianceY = $voAPOS_income - $budgetINCVOY;
    $varianceY_display = formatting($varianceY);
    if ($budgetINCVOY == 0) {
        $variancePY = 100;
        if ($varianceY < 0) {
            $variancePY = -100;
        }

        if ($voAPOS_income == 0) {
            $variancePY = 0;
        }
    } else {
        $variancePY = $varianceY / $budgetINCVOY * 100;
    }

    $variancePY_display = formatting($variancePY);

    if ($vo_income == 0 && $voA_income == 0 && $budgetINCVO == 0 && $budgetINCVOYEAR == 0) { // if ($vo_income == 0 && $voA_income == 0 && $budgetINCVO == 0)	//change above line on 14 Oct 08 RMB
        // DON NOT DISPLAY LINE
    } else {
        $pdf->showBoxed($account, 22, 590 - $line, 400, 20, 'left', '');
        $pdf->showBoxed($account_name, 50, 590 - $line, 400, 20, 'left', '');
        $pdf->setColorExt('fill', 'rgb', 1, 1, 1, 0);
        $pdf->rect(212, 590 - $line, 70, 18);
        $pdf->fill();
        $pdf->setColorExt('fill', 'rgb', 0, 0, 0, 0);
        $pdf->showBoxed($vo_display, 202, 590 - $line, 75, 20, 'right', '');
        $pdf->showBoxed($voA_display, 475, 590 - $line, 75, 20, 'right', '');
        $pdf->showBoxed($budgetINCVO_display, 272, 590 - $line, 75, 20, 'right', '');
        $pdf->showBoxed($budgetINCVOY_display, 550, 590 - $line, 75, 20, 'right', '');
        $pdf->showBoxed($budgetINCVOYEAR_display, 745, 590 - $line, 75, 20, 'right', '');
        $pdf->showBoxed($variance_display, 330, 590 - $line, 75, 20, 'right', '');
        $pdf->showBoxed($varianceP_display, 385, 590 - $line, 75, 20, 'right', '');
        $pdf->showBoxed($varianceY_display, 620, 590 - $line, 75, 20, 'right', '');
        $pdf->showBoxed($variancePY_display, 665, 590 - $line, 75, 20, 'right', '');
        $line += 10;

        if ($line > $pageLimit) {
            _newPage($pdf, $line, $page);
        }
    }
}

$voincPOS = $voinc * (-1);

// $voincAPOS = $voincA * (-1);

$voincA = dbTotalIncomePerGroupExFund($propertyID, $startFinancialYear, $periodTo, 'INCVO');
$voincomeA = $voincA['net_amount'];
$vo_takeon = take_on_balance($propertyID, 'INCVO', $startFinancialYear, $periodTo);
$voincAPOS = $voincomeA + $vo_takeon['amount'];


$total_vo = formatting($voincPOS, 2);
$total_voA = formatting($voincAPOS, 2);

$varianceT = $voincPOS - $budgetINCVOTotal;
$varianceT_display = formatting($varianceT);
if ($budgetINCVOTotal == 0) {
    $varianceTP = 100;
    if ($varianceT < 0) {
        $varianceTP = -100;
    }

    if ($voincPOS == 0) {
        $varianceTP = 0;
    }
} else {
    $varianceTP = $varianceT / $budgetINCVOTotal * 100;
}

$varianceTP_display = formatting($varianceTP);

$varianceTY = $voincAPOS - $budgetINCVOYTotal;
$varianceTY_display = formatting($varianceTY);
if ($budgetINCVOYTotal == 0) {
    $varianceTPY = 100;
    if ($varianceTY < 0) {
        $varianceTPY = -100;
    }

    if ($voincAPOS == 0) {
        $varianceTPY = 0;
    }
} else {
    $varianceTPY = $varianceTY / $budgetINCVOYTotal * 100;
}

$varianceTPY_display = formatting($varianceTPY);

$budgetINCVOYEARTotal_display = formatting($budgetINCVOYEARTotal);


if ($total_vo == '0.00' && $total_voA == '0.00' && $budgetINCVOTotal_display == '0.00' && $budgetINCVOYTotal_display == '0.00' && $budgetINCVOYEARTotal_display == '0.00') {
    $line -= 10;
    $pdf->setColorExt('both', 'rgb', 1, 1, 1, 0);
    $pdf->rect(20, 595 - $line, 170, 14);
    $pdf->fill();
    $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
    $line -= 20;
} else {
    $line += 2;

    $pdf->setColorExt('fill', 'rgb', 0.88, 0.88, 0.88, 0);
    $pdf->rect(215, 590 - $line, 609, 20);
    $pdf->fill();
    $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);


    $pdf->showBoxed($total_vo, 202, 585 - $line, 75, 20, 'right', '');
    $pdf->showBoxed($total_voA, 475, 585 - $line, 75, 20, 'right', '');
    $pdf->showBoxed($budgetINCVOTotal_display, 272, 585 - $line, 75, 20, 'right', '');
    $pdf->showBoxed($budgetINCVOYTotal_display, 550, 585 - $line, 75, 20, 'right', '');
    $pdf->showBoxed($budgetINCVOYEARTotal_display, 745, 585 - $line, 75, 20, 'right', '');
    $pdf->showBoxed($varianceT_display, 330, 585 - $line, 75, 20, 'right', '');
    $pdf->showBoxed($varianceTP_display, 385, 585 - $line, 75, 20, 'right', '');
    $pdf->showBoxed($varianceTY_display, 620, 585 - $line, 75, 20, 'right', '');
    $pdf->showBoxed($varianceTPY_display, 665, 585 - $line, 75, 20, 'right', '');


    $pdf->moveto(215, 610 - $line);
    $pdf->lineto(824, 610 - $line);
    $pdf->stroke();

    $pdf->moveto(215, 590 - $line);
    $pdf->lineto(824, 590 - $line);
    $pdf->stroke();


    if (strpos($total_vo, ')')) {
        $total_vo = str_replace(')', '', $total_vo);
        $total_vo = str_replace('(', '-', $total_vo);
    } else {

    }

    if (strpos($total_voA, ')')) {
        $total_voA = str_replace(')', '', $total_voA);
        $total_voA = str_replace('(', '-', $total_voA);
    } else {

    }

    if (strpos($budgetINCVOTotal_display, ')')) {
        $budgetINCVOTotal_display = str_replace(')', '', $budgetINCVOTotal_display);
        $budgetINCVOTotal_display = str_replace('(', '-', $budgetINCVOTotal_display);
    } else {

    }

    if (strpos($budgetINCVOYTotal_display, ')')) {
        $budgetINCVOYTotal_display = str_replace(')', '', $budgetINCVOYTotal_display);
        $budgetINCVOYTotal_display = str_replace('(', '-', $budgetINCVOYTotal_display);
    } else {

    }

    if (strpos($budgetINCVOYEARTotal_display, ')')) {
        $budgetINCVOYEARTotal_display = str_replace(')', '', $budgetINCVOYEARTotal_display);
        $budgetINCVOYEARTotal_display = str_replace('(', '-', $budgetINCVOYEARTotal_display);
    } else {

    }

    if (strpos($varianceT_display, ')')) {
        $varianceT_display = str_replace(')', '', $varianceT_display);
        $varianceT_display = str_replace('(', '-', $varianceT_display);
    } else {

    }

    if (strpos($varianceTP_display, ')')) {
        $varianceTP_display = str_replace(')', '', $varianceTP_display);
        $varianceTP_display = str_replace('(', '-', $varianceTP_display);
    } else {

    }

    if (strpos($varianceTY_display, ')')) {
        $varianceTY_display = str_replace(')', '', $varianceTY_display);
        $varianceTY_display = str_replace('(', '-', $varianceTY_display);
    } else {

    }

    if (strpos($varianceTPY_display, ')')) {
        $varianceTPY_display = str_replace(')', '', $varianceTPY_display);
        $varianceTPY_display = str_replace('(', '-', $varianceTPY_display);
    } else {

    }


    $_totalAllIncome_display += floatval(str_replace(',', '', $total_vo));
    $_totalAllIncomeA_display += floatval(str_replace(',', '', $total_voA));
    $_totalAllBudgetIncome_display += floatval(
        str_replace(',', '', $budgetINCVOTotal_display)
    );
    $_totalAllBudgetIncomeA_display += floatval(
        str_replace(',', '', $budgetINCVOYTotal_display)
    );
    $_totalAllBudgetIncomeYEAR_display += floatval(
        str_replace(',', '', $budgetINCVOYEARTotal_display)
    );
    $_totalAllVarianceRECEIPTS_display += floatval(
        str_replace(',', '', $varianceT_display)
    );
    $_totalAllVP_display1 += floatval(str_replace(',', '', $varianceTP_display));
    $_totalAllVarianceY_display += floatval(str_replace(',', '', $varianceTY_display));
    $_totalAllVPY_display += floatval(str_replace(',', '', $varianceTPY_display));
}

// var_dump($_totalAllVarianceY_display, $varianceTY_display);


// --------------------------------------------------------------------------------------
// TOTAL CASH RECEIPTS
//


if ($line > 550) {
    $pdf->setFontExt($_fonts['Helvetica'], 8);
    // $pdf->showBoxed ("Printed on $date", 22, 5, 275, 30, "left", "");
    $pdf->showBoxed("Page {$page}", 750, 0, 75, 30, 'right', '');
    $page++;


    $pdf->setlinewidth(0.5);           // zhirnaja nizhnjaja
    $pdf->moveto(18, 590 - $line);
    $pdf->lineto(824, 590 - $line);
    $pdf->stroke();


    $pdf->moveto(18, 590 - $line);      // tonkaja levaja
    $pdf->lineto(18, 515);
    $pdf->stroke();

    $pdf->moveto(824, 590 - $line);     // tonkaja pravaja
    $pdf->lineto(824, 515);
    $pdf->stroke();


    $pdf->moveto(215, 515);           // pervaja vertikalnaja
    $pdf->lineto(215, 590 - $line);
    $pdf->stroke();

    $pdf->moveto(470, 515);           // vtoraja vertikalnaja
    $pdf->lineto(470, 590 - $line);
    $pdf->stroke();

    $pdf->moveto(745, 515);           // tretja vertikalnaja
    $pdf->lineto(745, 590 - $line);
    $pdf->stroke();


    $pdf->end_page_ext('');
    $pdf->begin_page_ext(842, 595, '');
    $page_header = 'Cash Receipts Summary (...)';


    include __DIR__ . '/functions/mediumReportHeader.php';


    $line = 120;
    $pdf->setFontExt($_fonts['Helvetica'], 8);
    // $pdf->showBoxed ("Printed on $date", 22, 5, 275, 30, "left", "");
    $pdf->showBoxed("Page {$page}", 750, 0, 75, 30, 'right', '');
}

$line += 10;
$pdf->setColorExt('fill', 'rgb', 0.88, 0.88, 0.88, 0);
$pdf->rect(215, 570 - $line, 609, 20);
$pdf->fill();
$pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);


$line += 20;
$pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
$pdf->showBoxed('Total Cash Receipts', 22, 585 - $line, 400, 20, 'left', '');

$pdf->showBoxed(formatting($_totalAllIncome_display), 202, 585 - $line, 75, 20, 'right', '');
$pdf->showBoxed(formatting($_totalAllIncomeA_display), 475, 585 - $line, 75, 20, 'right', '');

$totalAllBudgetIncome = $budgetINCOWNTotal + $budgetINCRECTotal + $budgetINCVOTotal;
$totalAllBudgetIncome_display = formatting($totalAllBudgetIncome, 2);


$pdf->showBoxed(formatting($_totalAllBudgetIncome_display), 272, 585 - $line, 75, 20, 'right', '');
$pdf->showBoxed(formatting($_totalAllBudgetIncomeA_display), 550, 585 - $line, 75, 20, 'right', '');
$pdf->showBoxed(formatting($_totalAllBudgetIncomeYEAR_display), 745, 585 - $line, 75, 20, 'right', '');
$pdf->showBoxed(formatting($_totalAllVarianceRECEIPTS_display), 330, 585 - $line, 75, 20, 'right', '');
$pdf->showBoxed(formatting($_totalAllVP_display1), 385, 585 - $line, 75, 20, 'right', '');
$pdf->showBoxed(formatting($_totalAllVarianceY_display), 620, 585 - $line, 75, 20, 'right', '');
$pdf->showBoxed(formatting($_totalAllVPY_display), 665, 585 - $line, 75, 20, 'right', '');


$pdf->setlinewidth(0.5);
$pdf->moveto(18, 590 - $line);
$pdf->lineto(824, 590 - $line);
$pdf->stroke();


$pdf->moveto(18, 590 - $line);
$pdf->lineto(18, 515);
$pdf->stroke();
$pdf->moveto(824, 590 - $line);
$pdf->lineto(824, 515);
$pdf->stroke();

$pdf->moveto(215, 610 - $line);
$pdf->lineto(824, 610 - $line);
$pdf->stroke();


$pdf->moveto(215, 515);
$pdf->lineto(215, 590 - $line);
$pdf->stroke();
$pdf->moveto(470, 515);
$pdf->lineto(470, 590 - $line);
$pdf->stroke();
$pdf->moveto(745, 515);
$pdf->lineto(745, 590 - $line);
$pdf->stroke();


// Footer
$pdf->setFontExt($_fonts['Helvetica'], 8);
// $pdf->showBoxed ("Printed on $date", 22, 5, 275, 30, "left", "");
$pdf->showBoxed("Page {$page}", 750, 0, 75, 30, 'right', '');

// insert tracc footer
$traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'cashReceiptsAndPayments', A4_LANDSCAPE);
$traccFooter->prerender($pdf);

// BUDGET FIGURES
$params = [];
$budgetINCOWNSQL = 'SELECT COALESCE(SUM(pmrp_b_c_amt), 0) AS amount
		FROM pmrp_b_rev_per
		WHERE (pmrp_prop = ' . addSQLParam($params, $propertyID) . ') 
		AND (pmrp_per IN (' . addSQLParam($params, $pmcp_period) . ')) 
				AND (pmrp_year IN (' . addSQLParam($params, $pmcp_year) . ")) 
				AND (pmrp_acc IN (SELECT pmcg_acc
									FROM pmcg_chart_grp
									WHERE (pmcg_grp = 'TRACC2') 
									AND (pmcg_subgrp = 'INCOWN')))";
// echo "12th BUDGET:" . $budgetINCOWNSQL;
$dbh->selectDatabase($clientDB);
$budgetINCOWN_result = $dbh->executeScalar($budgetINCOWNSQL, $params);
$budgetINCOWN = $budgetINCOWN_result;
$budgetINCOWN_display = formatting($budgetINCOWN, 2);

$params = [];
$budgetINCDRSQL = 'SELECT COALESCE(SUM(pmrp_b_c_amt), 0) AS amount
		FROM pmrp_b_rev_per
		WHERE (pmrp_prop = ' . addSQLParam($params, $propertyID) . ') 
		AND (pmrp_per IN (' . addSQLParam($params, $pmcp_period) . '))
				AND (pmrp_year IN (' . addSQLParam($params, $pmcp_year) . "))
				AND (pmrp_acc IN (SELECT pmcg_acc
									FROM pmcg_chart_grp
									WHERE (pmcg_grp = 'TRACC2') 
									AND (pmcg_subgrp = 'INCDR')))";
// echo "13th BUDGET:" . $budgetINCDRSQL;
$dbh->selectDatabase($clientDB);
$budgetINCDR_result = $dbh->executeScalar($budgetINCDRSQL, $params);
$budgetINCDR = $budgetINCDR_result;
$budgetINCDR_display = formatting($budgetINCDR, 2);

$params = [];
$budgetINCVOSQL = 'SELECT COALESCE(SUM(pmrp_b_c_amt), 0) AS amount
		FROM pmrp_b_rev_per
		WHERE (pmrp_prop = ' . addSQLParam($params, $propertyID) . ') 
		AND (pmrp_per IN (' . addSQLParam($params, $pmcp_period) . '))
				AND (pmrp_year IN (' . addSQLParam($params, $pmcp_year) . "))
				AND (pmrp_acc IN (SELECT pmcg_acc
									FROM pmcg_chart_grp
									WHERE (pmcg_grp = 'TRACC2') 
									AND (pmcg_subgrp = 'INCVO')))";
// echo "14th BUDGET:" . $budgetINCVOSQL;
$dbh->selectDatabase($clientDB);
$budgetINCVO_result = $dbh->executeScalar($budgetINCVOSQL, $params);
$budgetINCVO = $budgetINCVO_result;
$budgetINCVO_display = formatting($budgetINCVO, 2);

// VARIANCE
$incOWNVar = $ownerincomePOS - $budgetINCOWN;
$incDRVar = $recincPOS - $budgetINCDR;
$voincVar = $voincPOS - $budgetINCVO;

// DISPLAY VARIANCE
$incOWNVar_display = formatting($incOWNVar, 2);
$incDRVar_display = formatting($incDRVar, 2);
$voincVar_display = formatting($voincVar, 2);

// VAR %

if ($budgetINCOWN == 0) {
    if ($incOWNVar == 0) {
        $incOWNVP = 0;
    } elseif ($incOWNVar < 0) {
        $incOWNVP = -100;
    } else {
        $incOWNVP = 100;
    }
} elseif ($incOWNVar == 0) {
    $incOWNVP = 0;
} else {
    $incOWNVP = $incOWNVar / $budgetINCOWN * 100;
}

if ($budgetINCDR == 0) {
    if ($incDRVar == 0) {
        $incDRVP = 0;
    } elseif ($incDRVar < 0) {
        $incDRVP = -100;
    } else {
        $incDRVP = 100;
    }
} elseif ($incDRVar == 0) {
    $incDRVP = 0;
} else {
    $incDRVP = $incDRVar / $budgetINCDR * 100;
}


if ($budgetINCVO == 0) {
    if ($voincVar == 0) {
        $incVOVP = 0;
    } elseif ($voincVar < 0) {
        $incVOVP = -100;
    } else {
        $incVOVP = 100;
    }
} elseif ($voincVar == 0) {
    $incVOVP = 0;
} else {
    $incVOVP = $voincVar / $budgetINCVO * 100;
}


// DISPLAY VAR %
$incOWNVP_display = formatting($incOWNVP, 0);
$incDRVP_display = formatting($incDRVP, 0);
$incVOVP_display = formatting($incVOVP, 0);

// TOTALS DISPLAY

$totalAllBudgetIncome = $budgetINCOWNTotal + $budgetINCRECTotal + $budgetINCVOTotal;
// $totalAllBudgetIncome = $budgetINCOWN + $budgetINCDR + $budgetINCVO;
// $totalAllBudgetIncome1 = $totalAllBudgetIncome;
$totalAllBudgetIncome_display = formatting($totalAllBudgetIncome, 2);
$totalAllVariance = $totalAllIncomePOS - $totalAllBudgetIncome;
$totalAllVarianceIncome = $totalAllVariance;
$totalAllVariance_display = formatting($totalAllVariance, 2);


if ($totalAllBudgetIncome == 0) {
    if ($totalAllVariance == 0) {
        $totalAllVP = 0;
    } elseif ($totalAllVariance < 0) {
        $totalAllVP = -100;
    } else {
        $totalAllVP = 100;
    }
} elseif ($totalAllVariance == 0) {
    $totalAllVP = 0;
} else {
    $totalAllVP = $totalAllVariance / $totalAllBudgetIncome * 100;
}


$totalAllVP_display = formatting($totalAllVP, 0);


$pdf->end_page_ext('');


/*
if ($page == 2)
{
$cashReceiptsPages = "2";
}
else
{
$cashReceiptsPages = 2 - $page;
}
*/

$cashReceiptsPages = $page;

// PAGE 3 - CASH PAYMENTS SUMMARY
$page++;

$startCashPaymentsPages = $page;
$pdf->begin_page_ext(842, 595, '');
$page_header = 'Cash Payments Summary';
include __DIR__ . '/functions/mediumReportHeader.php';
if ($logo) {
    generateLogo('landscape');
}

$pdf->setFontExt($_fonts['Helvetica'], 8);
// $pdf->showBoxed ("Printed on $date", 22, 5, 275, 30, "left", "");
$pdf->showBoxed("Page {$page}", 750, 0, 75, 30, 'right', '');

// insert tracc footer
$traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'cashReceiptsAndPayments', A4_LANDSCAPE);
$traccFooter->prerender($pdf);

// $pdf->setFontExt($_fonts["Helvetica"], 9);


// --------------------------------------------------------------------------
// OWNER PAYMENTS
//
$line = 200;
// ACCOUNTS FOR INDIV CALCULATIONS
$accSQL = "SELECT DISTINCT pmxc_acc
		FROM pmxc_ap_alloc
		WHERE fund IS NULL AND (pmxc_prop = ?) 
		AND (pmxc_alloc_dt <= CONVERT(datetime, ?, 103)) 
		AND (pmxc_f_type = 'PAY') 	
				AND (pmxc_acc IN
						  (SELECT pmcg_acc
							FROM pmcg_chart_grp
							WHERE (pmcg_grp = 'TRACC2') 
							AND pmcg_subgrp = 'EXPOWN'))";
$dbh->selectDatabase($clientDB);
$accSQL_result = $dbh->executeScalars($accSQL, [$propertyID, $periodTo]);


$unAllocCash = "SELECT DISTINCT pmuc_acc 
		FROM pmuc_unall_csh 
		WHERE (pmuc_prop = ?) 
		AND (pmuc_rcpt_dt < CONVERT(datetime, ?, 103))
		AND (pmuc_acc IN
						  (SELECT pmcg_acc
							FROM pmcg_chart_grp
							WHERE (pmcg_grp = 'TRACC2') 
							AND pmcg_subgrp = 'EXPOWN'))";
$dbh->selectDatabase($clientDB);
$cash_result = $dbh->executeScalars($unAllocCash, [$propertyID, $periodTo]);


$params = [];
$budgetACCSQL = 'SELECT DISTINCT pmep_exp_acc AS account
FROM         pmep_b_exp_per
WHERE     (pmep_prop = ' . addSQLParam($params, $propertyID) . ') AND (pmep_per IN
						  (' . addSQLParam($params, $pmcp_period) . ')) AND (pmep_year IN
						  (' . addSQLParam($params, $pmcp_year) . ")) AND (pmep_exp_acc IN
						  (SELECT pmcg_acc
							FROM pmcg_chart_grp
							WHERE (pmcg_grp = 'TRACC2') AND pmcg_subgrp = 'EXPOWN'))";
// echo "15th BUDGET:" . $budgetACCSQL;
$dbh->selectDatabase($clientDB);
$budget_result = $dbh->executeScalars($budgetACCSQL, $params);

$opBAL_accSQL = "SELECT DISTINCT pmpb_acc as account
		FROM pmpb_p_bal
		WHERE (pmpb_prop = ?)
		AND (pmpb_date BETWEEN CONVERT(datetime, ?, 103) AND CONVERT(datetime, ?, 103))
		AND (pmpb_acc IN (SELECT pmcg_acc
									FROM pmcg_chart_grp
									WHERE (pmcg_grp = 'TRACC2')
									AND (pmcg_subgrp = 'EXPOWN')))
								  AND (pmpb_acc <> '0703')";
$dbh->selectDatabase($clientDB);
$opBALacc_result = $dbh->executeScalars($opBAL_accSQL, [$propertyID, $startFinancialYear, $periodTo]);


$ownerexp_display = '0.00';
$acc = array_merge($accSQL_result, $cash_result, $opBALacc_result, $budget_result);

$merged = array_unique($acc);
asort($merged);

$pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
$pdf->showBoxed('Owner Expenditure', 22, 650 - $line, 400, 20, 'left', '');
$pdf->setFontExt($_fonts['Helvetica'], 8);
// $line = 0;
$budgetEXPOWNTotal = 0;
$budgetEXPOWNYTotal = 0;
$budgetEXPOWNYEARTotal = 0;
foreach ($merged as $account) {
    // Get Account Name
    $accSQL = "SELECT pmca_name FROM pmca_chart WHERE pmca_code = '{$account}'";
    $dbh->selectDatabase($clientDB);
    $acc_result = $dbh->executeScalar($accSQL, [$account]);
    $account_name = $acc_result;
    $account_name = (strlen($account_name) < $limit) ? $account_name : substr($account_name, 0, $limit) . ' ...';


    // OWNER PAYMENTS
    $ownerexpSQL = "SELECT COALESCE(SUM(pmxc_alloc_amt), 0) amount, SUM (pmxc_tax_amt) as tax_amount
		FROM pmxc_ap_alloc 
		WHERE (pmxc_acc = ?)
						 AND (pmxc_f_type = 'PAY') 	
			AND (pmxc_prop = ?) 
			AND (pmxc_alloc_dt BETWEEN CONVERT(datetime, ?, 103) AND CONVERT(datetime, ?, 103))";

    // $unallcashOISQL = "SELECT SUM (pmuc_amt) AS amount
    //		FROM pmuc_unall_csh
    //	WHERE (pmuc_prop = '$propertyID')
    // AND (pmuc_acc = '$account')
    //          AND (pmuc_rcpt_dt BETWEEN CONVERT(datetime, '{$periodFrom}', 103) AND CONVERT(datetime, '{$periodTo}', 103))";


    $dbh->selectDatabase($clientDB);
    $ownerexp_result = $dbh->executeSingle($ownerexpSQL, [$account, $propertyID, $periodFrom, $periodTo]);
    // $ownerexpUC_result = $dbh->executeScalar($unallcashOISQL);
    $owner_expenses = $ownerexp_result['amount'];
    $owner_expenses_tax = $ownerexp_result['tax_amount'];

    $owner_expenses += $owner_expenses_tax;
    $ownerPOS_expenses = $owner_expenses * (-1);
    $ownerexp_display += $ownerPOS_expenses;
    $owner_display = formatting($ownerPOS_expenses, 2);

    // YTD4

    $ownerexpASQL = "SELECT COALESCE(SUM(pmxc_alloc_amt), 0) AS amount, COALESCE(SUM(pmxc_tax_amt), 0) as tax_amount
		FROM pmxc_ap_alloc 
		WHERE (pmxc_acc = ?)
						AND (pmxc_f_type = 'PAY') 	
						AND (pmxc_prop = ?) 
			AND (pmxc_alloc_dt BETWEEN CONVERT(datetime, ?, 103) AND CONVERT(datetime, ?, 103))";
    $dbh->selectDatabase($clientDB);
    $ownerexpA_result = $dbh->executeSingle(
        $ownerexpASQL,
        [$account, $propertyID, $startFinancialYear, $periodTo]
    );

    // AND (pmxc_alloc_dt BETWEEN CONVERT(datetime, '{$startFinancialYear}', 103) AND CONVERT(datetime, '{$periodTo}', 103))";

    // THIS QUERY EFFECTS THE CURRENT MONTH AREA FOR OWNER EXPENDITURE


    $params = [];
    $unallcashOISQL = 'SELECT COALESCE(SUM(pmuc_net_amt), 0) as amount
			FROM pmuc_unall_csh
			WHERE (pmuc_prop = ' . addSQLParam($params, $propertyID) . ')
			AND (pmuc_acc = ' . addSQLParam($params, $account) . ')
			AND (pmuc_period >= 1)
						AND (pmuc_period <= ' . addSQLParam($params, $toPeriod) . ')
				AND (pmuc_year IN (' . addSQLParam($params, $pmcp_year) . '))';
    $dbh->selectDatabase($clientDB);
    $ownerexpUC_result = $dbh->executeScalar($unallcashOISQL, $params);
    $ownerUC_expenses = $ownerexpUC_result;


    // OPENING BALANCE FOR EACH ACCOUNT
    $openBalOWNSQL = 'SELECT COALESCE(SUM(pmpb_bal), 0) as amount
		FROM pmpb_p_bal
		WHERE (pmpb_prop = ?) 
		AND (pmpb_date BETWEEN CONVERT(datetime, ?, 103) AND CONVERT(datetime,?, 103))
		AND (pmpb_acc = ?)';
    $dbh->selectDatabase($clientDB);
    $openBalOWN_result = $dbh->executeScalar(
        $openBalOWNSQL,
        [$propertyID, $startFinancialYear, $periodTo, $account]
    );

    // -----------OPENING BALANCE FIGURES------------------------
    $openBalOWN_expenses = $openBalOWN_result;
    // ----------------------------------------------------------

    // -----------BUDGET FIGURES ---------------------------------
    $params = [];
    $budgetEXPOWNSQL = 'SELECT COALESCE(SUM(pmep_b_c_amt), 0) AS amount
		FROM pmep_b_exp_per
		WHERE (pmep_prop = ' . addSQLParam($params, $propertyID) . ') 
		AND (pmep_per IN (' . addSQLParam($params, $pmcp_period) . ')) 
				AND (pmep_year IN (' . addSQLParam($params, $pmcp_year) . ')) 
				AND (pmep_exp_acc = ' . addSQLParam($params, $account) . ')';
    // echo "16th BUDGET:" . $budgetEXPOWNSQL;
    $dbh->selectDatabase($clientDB);
    $budgetEXPOWN_result = $dbh->executeScalar($budgetEXPOWNSQL, $params);
    $budgetEXPOWN = $budgetEXPOWN_result;
    $budgetEXPOWNTotal += $budgetEXPOWN;
    $budgetEXPOWNTotal_display = formatting($budgetEXPOWNTotal);
    $budgetEXPOWN_display = formatting($budgetEXPOWN);

    $params = [];
    $budgetEXPOWNYSQL = 'SELECT COALESCE(SUM(pmep_b_c_amt), 0) AS amount
		FROM pmep_b_exp_per
		WHERE (pmep_prop = ' . addSQLParam($params, $propertyID) . ') 
		AND (pmep_per >= 1) 
		AND (pmep_per <= ' . addSQLParam($params, $toPeriod) . ')
				  AND (pmep_year IN (' . addSQLParam($params, $pmcp_year) . '))
				AND (pmep_exp_acc =' . addSQLParam($params, $account) . ')';
    // echo "17th BUDGET:" . $budgetEXPOWNYSQL;
    $dbh->selectDatabase($clientDB);
    $budgetEXPOWNY_result = $dbh->executeScalar($budgetEXPOWNYSQL, $params);
    $budgetEXPOWNY = $budgetEXPOWNY_result;
    $budgetEXPOWNY_display = formatting($budgetEXPOWNY, 2);
    $budgetEXPOWNYTotal += $budgetEXPOWNY;
    $budgetEXPOWNYTotal_display = formatting($budgetEXPOWNYTotal);
    $params = [];
    $budgetEXPOWNYEARSQL = 'SELECT COALESCE(SUM(pmep_b_c_amt), 0) AS amount
		FROM pmep_b_exp_per
		WHERE (pmep_prop = ' . addSQLParam($params, $propertyID) . ') 
		AND (pmep_per BETWEEN 1 AND 12)AND (pmep_year IN (' . addSQLParam($params, $pmcp_year) . '))
				AND (pmep_exp_acc = ' . addSQLParam($params, $account) . ')';
    // echo "18th BUDGET:" . $budgetEXPOWNYEARSQL;
    $dbh->selectDatabase($clientDB);
    $budgetEXPOWNYEAR_result = $dbh->executeScalar($budgetEXPOWNYEARSQL, $params);
    $budgetEXPOWNYEAR = $budgetEXPOWNYEAR_result;
    $budgetEXPOWNYEAR_display = formatting($budgetEXPOWNYEAR, 2);
    $budgetEXPOWNYEARTotal += $budgetEXPOWNYEAR;

    // OPENING BALANCE FOR EACH ACCOUNT
    $openBalOWNSQL = 'SELECT COALESCE(SUM(pmpb_bal), 0) as amount
		FROM pmpb_p_bal
		WHERE (pmpb_prop = ?) 
		AND (pmpb_date BETWEEN CONVERT(datetime, ?, 103) AND CONVERT(datetime, ?, 103))
		AND (pmpb_acc = ?)';
    $dbh->selectDatabase($clientDB);
    $openBalOWN_result = $dbh->executeScalar(
        $openBalOWNSQL,
        [$propertyID, $startFinancialYear, $periodTo, $account]
    );

    // -----------OPENING BALANCE FIGURES------------------------
    $openBalOWN_exp = $openBalOWN_result;
    // ----------------------------------------------------------


    $ownerA_expenses = $ownerexpA_result['amount'];
    $ownerA_expenses_tax = $ownerexpA_result['tax_amount'];
    $ownerA_expenses = $ownerA_expenses + $ownerA_expenses_tax - $openBalOWN_exp;
    $ownerAPOS_expenses = $ownerA_expenses * (-1);
    $ownerA_display = formatting($ownerAPOS_expenses, 2);

    $variance = $budgetEXPOWN - $ownerPOS_expenses;
    $variance_display = formatting($variance);
    if ($budgetEXPOWN == 0) {
        $varianceP = $ownerPOS_expenses == 0 ? 0 : -100;
    } else {
        $varianceP = $variance / $budgetEXPOWN * 100;
    }

    $varianceP_display = formatting($varianceP);


    $varianceY = $budgetEXPOWNY - $ownerAPOS_expenses;
    $varianceY_display = formatting($varianceY);
    if ($budgetEXPOWNY == 0) {
        $variancePY = $ownerAPOS_expenses == 0 ? 0 : -100;
    } else {
        $variancePY = $varianceY / $budgetEXPOWNY * 100;
    }

    $variancePY_display = formatting($variancePY);
    if ($owner_display == '0.00' && $ownerA_display == '0.00' && $budgetEXPOWN_display == '0.00' && $budgetEXPOWNY_display == '0.00' && $budgetEXPOWNYEAR_display == '0.00') {
        // DO NOT DISPLAY LINE
    } else {
        if ($line > 600) {
            $page++;


            $pdf->setlinewidth(0.5);           // zhirnaja nizhnjaja
            $pdf->moveto(18, 650 - $line);
            $pdf->lineto(824, 650 - $line);
            $pdf->stroke();


            $pdf->moveto(18, 650 - $line);      // tonkaja levaja
            $pdf->lineto(18, 515);
            $pdf->stroke();

            $pdf->moveto(824, 650 - $line);     // tonkaja pravaja
            $pdf->lineto(824, 515);
            $pdf->stroke();


            $pdf->moveto(215, 515);           // pervaja vertikalnaja
            $pdf->lineto(215, 650 - $line);
            $pdf->stroke();

            $pdf->moveto(470, 515);           // vtoraja vertikalnaja
            $pdf->lineto(470, 650 - $line);
            $pdf->stroke();

            $pdf->moveto(745, 515);           // tretja vertikalnaja
            $pdf->lineto(745, 650 - $line);
            $pdf->stroke();

            $pdf->end_page_ext('');
            $pdf->begin_page_ext(842, 595, '');
            // PAGE 4 - CASH PAYMENTS ...


            $page_header = 'Cash Payments Summary (...)';
            include __DIR__ . '/functions/mediumReportHeader.php';
            if ($logo) {
                generateLogo('landscape');
            }

            $pdf->setFontExt($_fonts['Helvetica'], 8);
            // $pdf->showBoxed ("Printed on $date", 22, 5, 275, 30, "left", "");
            $pdf->showBoxed("Page {$page}", 750, 0, 75, 30, 'right', '');

            // insert tracc footer
            $traccFooter = new TraccFooter(
                'assets/clientLogos/tracc_logo_footer.jpg',
                'cashReceiptsAndPayments',
                A4_LANDSCAPE
            );
            $traccFooter->prerender($pdf);

            $line = 210;
            $pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
            $pdf->showBoxed('Owner Expenditure (...)', 22, 640 - $line, 400, 40, 'left', '');
            $line = 195;
            $pdf->setFontExt($_fonts['Helvetica'], 8);
        }

        $pdf->showBoxed($account, 22, 640 - $line, 400, 20, 'left', '');
        $pdf->showBoxed($account_name, 50, 640 - $line, 400, 20, 'left', '');
        $pdf->showBoxed($owner_display, 202, 640 - $line, 75, 20, 'right', '');
        $pdf->showBoxed($budgetEXPOWN_display, 262, 640 - $line, 75, 20, 'right', '');
        $pdf->showBoxed($variance_display, 330, 640 - $line, 75, 20, 'right', '');
        $pdf->showBoxed($varianceP_display, 385, 640 - $line, 75, 20, 'right', '');
        $pdf->showBoxed($ownerA_display, 475, 640 - $line, 75, 20, 'right', '');
        $pdf->showBoxed($budgetEXPOWNY_display, 550, 640 - $line, 75, 20, 'right', '');
        $pdf->showBoxed($varianceY_display, 620, 640 - $line, 75, 20, 'right', '');
        $pdf->showBoxed($variancePY_display, 665, 640 - $line, 75, 20, 'right', '');

        $pdf->showBoxed($budgetEXPOWNYEAR_display, 745, 640 - $line, 75, 20, 'right', '');

        $line += 10;
    }
}


$gstSQL = "SELECT COALESCE(SUM(pmxc_tax_amt), 0) amount 
		FROM pmxc_ap_alloc 
		WHERE (pmxc_f_type = 'PAY')  
		AND (pmxc_prop = ?) 
		AND (pmxc_acc<>NULL) 
		AND (pmxc_alloc_dt BETWEEN CONVERT(datetime, ?, 103) AND CONVERT(datetime, ?, 103))
		AND (pmxc_acc IN (SELECT pmcg_acc
									FROM pmcg_chart_grp
									WHERE (pmcg_grp = 'TRACC2') 
									AND pmcg_subgrp = 'EXPDR' OR pmcg_subgrp = 'EXPOWN' OR pmcg_subgrp = 'EXPVO'))";
$dbh->selectDatabase($clientDB);
$gst_result = $dbh->executeScalar($gstSQL, [$propertyID, $periodFrom, $periodTo]);
$gst = $gst_result;
$gst_display = formatting($gst, 2);


// OPENING BALANCE FOR EACH ACCOUNT
$ownerexp_takeon = take_on_balance($propertyID, 'EXPOWN', $startFinancialYear, $periodTo);
$voexp_takeon = take_on_balance($propertyID, 'EXPVO', $startFinancialYear, $periodTo);
$recexp_takeon = take_on_balance($propertyID, 'EXPDR', $startFinancialYear, $periodTo);


/*
$openBalOWNSQL = "SELECT SUM(pmpb_bal) as amount
        FROM pmpb_p_bal
        WHERE (pmpb_prop = '$propertyID')
        AND (pmpb_date BETWEEN CONVERT(datetime, '{$startFinancialYear}', 103) AND CONVERT(datetime, '{$periodTo}', 103))
        AND (pmpb_acc IN (SELECT pmcg_acc
                                    FROM pmcg_chart_grp
                                    WHERE (pmcg_grp = 'TRACC2')
                                    AND pmcg_subgrp = 'EXPDR' OR pmcg_subgrp = 'EXPOWN' OR pmcg_subgrp = 'EXPVO'))";
$openBalOWN_result = $dbh->executeScalar($openBalOWNSQL);
*/

// -----------OPENING BALANCE FIGURES------------------------
$openBalGST_exp = $openBalOWN_result;
// ----------------------------------------------------------


$gstASQL = "SELECT COALESCE(SUM(pmxc_tax_amt), 0) amount 
		FROM pmxc_ap_alloc 
		WHERE (pmxc_f_type = 'PAY')  
		AND (pmxc_prop = ?) 
		AND (pmxc_acc<>NULL) 
		AND (pmxc_alloc_dt BETWEEN CONVERT(datetime, ?, 103) AND CONVERT(datetime, ?, 103))
		AND (pmxc_acc IN (SELECT pmcg_acc
									FROM pmcg_chart_grp
									WHERE (pmcg_grp = 'TRACC2') 
									AND pmcg_subgrp = 'EXPDR' OR pmcg_subgrp = 'EXPOWN' OR pmcg_subgrp = 'EXPVO'))";
$dbh->selectDatabase($clientDB);
$gstA_result = $dbh->executeScalar($gstASQL, [$propertyID, $startFinancialYear, $periodTo]);
$gstA = $gstA_result;
$gstA += $openBalGST_exp;
$gstA_display = formatting($gstA, 2);
$line -= 10;


$ownerexpYear = totalexpenses($propertyID, $startFinancialYear, $periodTo, 'EXPOWN');
$ownerexpAPOS = $ownerexpYear['net_amount'] + $ownerexp_takeon['amount'];

// Formatting
$ownerexpA_display = formatting($ownerexpAPOS, 2);
$ownerexp_display = formatting($ownerexp_display, 2);
if ($ownerexp_display == '0.00' && $ownerexpA_display == '0.00' && $budgetEXPOWNTotal == 0 && $budgetEXPOWNYTotal == 0 && $budgetEXPOWNYEARTotal == 0) {
    $pdf->setColorExt('both', 'rgb', 1, 1, 1, 0);
    $pdf->rect(20, 647 - $line, 170, 15);
    $pdf->fill();
    $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);

    $line -= 40;
} else {
    $line += 2;
    $pdf->setColorExt('fill', 'rgb', 0.88, 0.88, 0.88, 0);
    $pdf->rect(215, 630 - $line, 609, 20);
    $pdf->fill();
    $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);


    $pdf->moveto(215, 650 - $line);
    $pdf->lineto(824, 650 - $line);
    $pdf->stroke();


    $budgetEXPOWNTotal_display = formatting($budgetEXPOWNTotal, 2);
    $budgetEXPOWNYTotal_display = formatting($budgetEXPOWNYTotal, 2);
    $budgetEXPOWNYEARTotal_display = formatting($budgetEXPOWNYEARTotal, 2);
    $expOWNVarTotal = $budgetEXPOWNTotal - $ownerexpPOS;
    $expOWNVarYTotal = $budgetEXPOWNYTotal - $ownerexpAPOS;
    $expOWNVarTotal_display = formatting($expOWNVarTotal, 2);
    $expOWNVarYTotal_display = formatting($expOWNVarYTotal, 2);
    if ($budgetEXPOWNTotal == 0) {
        $variancePTotal = $ownerexpPOS == 0 ? 0 : -100;
    } else {
        $variancePTotal = $expOWNVarTotal / $budgetEXPOWNTotal * 100;
    }

    $variancePTotal_display = formatting($variancePTotal);
    if ($budgetEXPOWNYTotal == 0) {
        $variancePYTotal = $ownerexpAPOS == 0 ? 0 : -100;
    } else {
        $variancePYTotal = $expOWNVarYTotal / $budgetEXPOWNYTotal * 100;
    }

    $variancePYTotal_display = formatting($variancePYTotal);

    $pdf->showBoxed($ownerexp_display, 202, 626 - $line, 75, 20, 'right', '');
    $pdf->showBoxed($ownerexpA_display, 475, 626 - $line, 75, 20, 'right', '');
    $pdf->showBoxed($budgetEXPOWNTotal_display, 262, 626 - $line, 75, 20, 'right', '');
    $pdf->showBoxed($budgetEXPOWNYTotal_display, 550, 626 - $line, 75, 20, 'right', '');
    $pdf->showBoxed($budgetEXPOWNYEARTotal_display, 745, 626 - $line, 75, 20, 'right', '');
    $pdf->showBoxed($expOWNVarTotal_display, 330, 626 - $line, 75, 20, 'right', '');
    $pdf->showBoxed($expOWNVarYTotal_display, 620, 626 - $line, 75, 20, 'right', '');
    $pdf->showBoxed($variancePTotal_display, 385, 626 - $line, 75, 20, 'right', '');
    $pdf->showBoxed($variancePYTotal_display, 665, 626 - $line, 75, 20, 'right', '');

    $pdf->moveto(215, 630 - $line);
    $pdf->lineto(824, 630 - $line);
    $pdf->stroke();
}

// --------------------------------------------------------------------------
// RECOVERABLE PAYMENTS
//
// ACCOUNTS FOR INDIV CALCULATIONS
$accSQL = "SELECT DISTINCT pmxc_acc
		FROM pmxc_ap_alloc
		WHERE  fund IS NULL AND (pmxc_prop = ?) 
		AND (pmxc_alloc_dt <= CONVERT(datetime, ?, 103))
		AND (pmxc_f_type = 'PAY') 	
		AND (pmxc_acc IN
						  (SELECT pmcg_acc
							FROM pmcg_chart_grp
							WHERE (pmcg_grp = 'TRACC2') 
							AND pmcg_subgrp = 'EXPDR'))";
$dbh->selectDatabase($clientDB);
$accSQL_result = $dbh->executeScalars($accSQL, [$propertyID, $periodTo]);

$params = [];
$budgetACCSQL = 'SELECT DISTINCT pmep_exp_acc AS account
FROM         pmep_b_exp_per
WHERE     (pmep_prop = ' . addSQLParam($params, $propertyID) . ') AND (pmep_per IN
						  (' . addSQLParam($params, $pmcp_period) . ')) AND (pmep_year IN
						  (' . addSQLParam($params, $pmcp_year) . ")) AND (pmep_exp_acc IN
						  (SELECT pmcg_acc
							FROM pmcg_chart_grp
							WHERE (pmcg_grp = 'TRACC2') AND pmcg_subgrp = 'EXPDR'))";
// echo "19th BUDGET:" . $budgetACCSQL;
$dbh->selectDatabase($clientDB);
$budget_result = $dbh->executeScalars($budgetACCSQL, $params);

$opBAL_accSQL = "SELECT DISTINCT pmpb_acc as account
		FROM pmpb_p_bal
		WHERE (pmpb_prop = ?)
		AND (pmpb_date BETWEEN CONVERT(datetime, ?, 103) AND CONVERT(datetime, ?, 103))
		AND (pmpb_acc IN (SELECT pmcg_acc
									FROM pmcg_chart_grp
									WHERE (pmcg_grp = 'TRACC2')
									AND (pmcg_subgrp = 'EXPDR')))";
$dbh->selectDatabase($clientDB);
$opBALacc_result = $dbh->executeScalars($opBAL_accSQL, [$propertyID, $startFinancialYear, $periodTo]);
$acc = array_merge($accSQL_result, $opBALacc_result, $budget_result);

$merged = array_unique($acc);
asort($merged);


$budgetEXPDRTotal = 0;
$budgetEXPDRYTotal = 0;
$budgetEXPDRYEARTotal = 0;

$pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
$pdf->showBoxed('Recoverable Expenditure', 22, 600 - $line, 400, 20, 'left', '');
$pdf->setFontExt($_fonts['Helvetica'], 8);


// /////////////////////////account code assigned and account name identified
// /////////////////////////////////////////////////////////////////////////////


foreach ($merged as $account) {
    // Get Account Name
    $accSQL = 'SELECT pmca_name FROM pmca_chart WHERE pmca_code = ?';
    $dbh->selectDatabase($clientDB);
    $acc_result = $dbh->executeScalar($accSQL, [$account]);
    $account_name = $acc_result;
    $account_name = (strlen($account_name) < $limit) ? $account_name : substr($account_name, 0, $limit) . ' ...';


    // RECOVERABLE EXPENDITURE
    $recSQL = "SELECT COALESCE(SUM(pmxc_alloc_amt), 0) amount, SUM (pmxc_tax_amt) as tax_amount
		FROM pmxc_ap_alloc 
		WHERE (pmxc_acc = ?)
				   AND (pmxc_f_type = 'PAY') 	
					AND (pmxc_prop = ?) 
			AND (pmxc_alloc_dt BETWEEN CONVERT(datetime, ?, 103) AND CONVERT(datetime, ?, 103))";

    $dbh->selectDatabase($clientDB);
    $rec_result = $dbh->executeSingle($recSQL, [$account, $propertyID, $periodFrom, $periodTo]);

    $rec_income = $rec_result['amount'];
    $rec_tax_income = $rec_result['tax_amount'];


    $rec_income += $rec_tax_income;
    $recPOS_income = $rec_income * (-1);
    $rec_display = formatting($recPOS_income, 2);

    // YTD5

    $recASQL = "SELECT COALESCE(SUM(pmxc_alloc_amt), 0) AS amount, COALESCE(SUM(pmxc_tax_amt), 0) as tax_amount
		FROM pmxc_ap_alloc 
		WHERE (pmxc_acc = ?)
				   AND (pmxc_f_type = 'PAY') 	
					AND (pmxc_prop = ?) 
			AND (pmxc_alloc_dt BETWEEN CONVERT(datetime, ?, 103) AND CONVERT(datetime, ?, 103))";
    $dbh->selectDatabase($clientDB);
    $recA_result = $dbh->executeSingle($recASQL, [$account, $propertyID, $startFinancialYear, $periodTo]);

    $params = [];
    $unallcashRECSQL = 'SELECT COALESCE(SUM(pmuc_net_amt), 0) as amount
			FROM pmuc_unall_csh
			WHERE (pmuc_prop = ' . addSQLParam($params, $propertyID) . ')
			AND (pmuc_acc = ' . addSQLParam($params, $account) . ')
			AND (pmuc_period >= 1)
						AND (pmuc_period <= ' . addSQLParam($params, $toPeriod) . ')
				AND (pmuc_year IN (' . addSQLParam($params, $pmcp_year) . '))';
    $dbh->selectDatabase($clientDB);
    $recUC_result = $dbh->executeScalar($unallcashRECSQL, $params);
    $recUC_income = $recUC_result;

    $recA_income = $recA_result['amount'];
    $recA_tax_income = $recA_result['tax_amount'];
    $recA_income += $recA_tax_income;


    // -----------BUDGET FIGURES ---------------------------------
    $params = [];
    $budgetEXPDRSQL = 'SELECT COALESCE(SUM(pmep_b_c_amt), 0) AS amount
		FROM pmep_b_exp_per
		WHERE (pmep_prop = ' . addSQLParam($params, $propertyID) . ') 
		AND (pmep_per IN (' . addSQLParam($params, $pmcp_period) . ')) 
				AND (pmep_year IN (' . addSQLParam($params, $pmcp_year) . ')) 
				AND (pmep_exp_acc = ' . addSQLParam($params, $account) . ')';
    // echo "20th BUDGET:" . $budgetEXPDRSQL;
    $dbh->selectDatabase($clientDB);
    $budgetEXPDR_result = $dbh->executeScalar($budgetEXPDRSQL, $params);
    $budgetEXPDR = $budgetEXPDR_result;
    $budgetEXPDRTotal += $budgetEXPDR;
    $budgetEXPDR_display = formatting($budgetEXPDR);

    $params = [];
    $budgetEXPDRYSQL = 'SELECT COALESCE(SUM(pmep_b_c_amt), 0) AS amount
		FROM pmep_b_exp_per
		WHERE (pmep_prop = ' . addSQLParam($params, $propertyID) . ') 
		AND (pmep_per >= 1) 
		AND (pmep_per <= ' . addSQLParam($params, $toPeriod) . ')
				  AND (pmep_year IN (' . addSQLParam($params, $pmcp_year) . '))
				AND (pmep_exp_acc =' . addSQLParam($params, $account) . ')';
    // echo "21st BUDGET:" . $budgetEXPDRYSQL;
    $dbh->selectDatabase($clientDB);
    $budgetEXPDRY_result = $dbh->executeScalar($budgetEXPDRYSQL, $params);
    $budgetEXPDRY = $budgetEXPDRY_result;
    $budgetEXPDRY_display = formatting($budgetEXPDRY, 2);
    $budgetEXPDRYTotal += $budgetEXPDRY;

    $params = [];
    $budgetEXPDRYEARSQL = 'SELECT COALESCE(SUM(pmep_b_c_amt), 0) AS amount
		FROM pmep_b_exp_per
		WHERE (pmep_prop = ' . addSQLParam($params, $propertyID) . ') 
		AND (pmep_per BETWEEN 1 AND 12)AND (pmep_year IN (' . addSQLParam($params, $pmcp_year) . '))
				AND (pmep_exp_acc = ' . addSQLParam($params, $account) . ')';
    // echo "22nd BUDGET:" . $budgetEXPDRYEARSQL;
    $dbh->selectDatabase($clientDB);
    $budgetEXPDRYEAR_result = $dbh->executeScalar($budgetEXPDRYEARSQL, $params);
    $budgetEXPDRYEAR = $budgetEXPDRYEAR_result;
    $budgetEXPDRYEAR_display = formatting($budgetEXPDRYEAR, 2);
    $budgetEXPDRYEARTotal += $budgetEXPDRYEAR;


    // OPENING BALANCE FOR EACH ACCOUNT
    $openBalOWNSQL = 'SELECT COALESCE(SUM(pmpb_bal), 0) as amount
		FROM pmpb_p_bal
		WHERE (pmpb_prop = ?) 
		AND (pmpb_date BETWEEN CONVERT(datetime, ?, 103) AND CONVERT(datetime, ?, 103))
		AND (pmpb_acc = ?)';
    $dbh->selectDatabase($clientDB);
    $openBalOWN_result = $dbh->executeScalar(
        $openBalOWNSQL,
        [$propertyID, $startFinancialYear, $periodTo, $account]
    );

    // -----------OPENING BALANCE FIGURES------------------------
    $openBalOWN_exp = $openBalOWN_result;
    // ----------------------------------------------------------


    $ownerA_income = $ownerincA_result['amount'];
    $ownerA_income_tax = $ownerincA_result['tax_amount'];
    $ownerA_income = $ownerA_income + $ownerA_income_tax - $openBalOWN_exp;
    $ownerAPOS_income = $ownerA_income * (-1);
    $ownerA_display = formatting($ownerAPOS_income, 2);

    $recA_income -= $openBalOWN_exp;
    $recAPOS_income = $recA_income * (-1);
    $recA_display = formatting($recAPOS_income, 2);

    $variance = $budgetEXPDR - $recPOS_income;
    $variance_display = formatting($variance);
    if ($budgetEXPDR == 0) {
        $varianceP = $recPOS_income == 0 ? 0 : -100;
    } else {
        $varianceP = $variance / $budgetEXPDR * 100;
    }

    $varianceP_display = formatting($varianceP);


    $varianceY = $budgetEXPDRY - $recAPOS_income;
    $varianceY_display = formatting($varianceY);
    if ($budgetEXPDRY == 0) {
        $variancePY = $recAPOS_income == 0 ? 0 : -100;
    } else {
        $variancePY = $varianceY / $budgetEXPDRY * 100;
    }

    $variancePY_display = formatting($variancePY);
    if ($rec_display == '0.00' && $recA_income == '0.00' && $budgetEXPDR_display == '0.00' && $budgetEXPDRY_display == '0.00' && $budgetEXPDRYEAR_display == '0.00') {
        // DON NOT DISPLAY LINE
    } else {
        if ($line >= 560) {
            $page++;


            $pdf->setlinewidth(0.5);           // zhirnaja nizhnjaja
            $pdf->moveto(18, 600 - $line);
            $pdf->lineto(824, 600 - $line);
            $pdf->stroke();


            $pdf->moveto(18, 600 - $line);      // tonkaja levaja
            $pdf->lineto(18, 515);
            $pdf->stroke();

            $pdf->moveto(824, 600 - $line);     // tonkaja pravaja
            $pdf->lineto(824, 515);
            $pdf->stroke();


            $pdf->moveto(215, 515);           // pervaja vertikalnaja
            $pdf->lineto(215, 600 - $line);
            $pdf->stroke();

            $pdf->moveto(470, 515);           // vtoraja vertikalnaja
            $pdf->lineto(470, 600 - $line);
            $pdf->stroke();

            $pdf->moveto(745, 515);           // tretja vertikalnaja
            $pdf->lineto(745, 600 - $line);
            $pdf->stroke();

            $pdf->end_page_ext('');
            $pdf->begin_page_ext(842, 595, '');
            // PAGE 4 - CASH PAYMENTS ...


            $page_header = 'Cash Payments Summary (...)';
            include __DIR__ . '/functions/mediumReportHeader.php';
            if ($logo) {
                generateLogo('landscape');
            }

            $pdf->setFontExt($_fonts['Helvetica'], 8);
            // $pdf->showBoxed ("Printed on $date", 22, 5, 275, 30, "left", "");
            $pdf->showBoxed("Page {$page}", 750, 0, 75, 30, 'right', '');

            // insert tracc footer
            $traccFooter = new TraccFooter(
                'assets/clientLogos/tracc_logo_footer.jpg',
                'cashReceiptsAndPayments',
                A4_LANDSCAPE
            );
            $traccFooter->prerender($pdf);

            $line = 160;
            $pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
            $pdf->showBoxed('Recoverable Expenditure (...)', 22, 590 - $line, 400, 40, 'left', '');
            $line = 145;
            $pdf->setFontExt($_fonts['Helvetica'], 8);
        }

        $pdf->showBoxed($account, 22, 590 - $line, 400, 20, 'left', '');
        $pdf->showBoxed($account_name, 50, 590 - $line, 400, 20, 'left', '');
        $pdf->showBoxed($rec_display, 202, 590 - $line, 75, 20, 'right', '');
        $pdf->showBoxed($recA_display, 475, 590 - $line, 75, 20, 'right', '');
        $pdf->showBoxed($budgetEXPDR_display, 262, 590 - $line, 75, 20, 'right', '');
        $pdf->showBoxed($budgetEXPDRY_display, 550, 590 - $line, 75, 20, 'right', '');
        $pdf->showBoxed($budgetEXPDRYEAR_display, 745, 590 - $line, 75, 20, 'right', '');
        $pdf->showBoxed($variance_display, 330, 590 - $line, 75, 20, 'right', '');
        $pdf->showBoxed($varianceY_display, 620, 590 - $line, 75, 20, 'right', '');
        $pdf->showBoxed($varianceP_display, 385, 590 - $line, 75, 20, 'right', '');
        $pdf->showBoxed($variancePY_display, 665, 590 - $line, 75, 20, 'right', '');

        $line += 10;
    }
}


$budgetEXPDRTotal_display = formatting($budgetEXPDRTotal);
$budgetEXPDRYEARTotal_display = formatting($budgetEXPDRYEARTotal, 2);
$budgetEXPDRYTotal_display = formatting($budgetEXPDRYTotal);


$recexpYear = totalexpenses($propertyID, $startFinancialYear, $periodTo, 'EXPDR');
$recoverableA_exp = $recexpYear['net_amount'] + $recexp_takeon['amount'];


$recexp = totalexpenses($propertyID, $periodFrom, $periodTo, 'EXPDR');
$recoverable_exp = $recexp['net_amount'];


// $recincAPOS = $recexpY;
$total_rec = formatting($recoverable_exp, 2);
$total_recA = formatting($recoverableA_exp, 2);

$varianceTotal = $budgetEXPDRTotal - $recoverable_exp;
$varianceTotal_display = formatting($varianceTotal);

$varianceYTotal = $budgetEXPDRYTotal - $recoverableA_exp;
$varianceYTotal_display = formatting($varianceYTotal);
if ($budgetEXPDRTotal == 0) {
    $variancePTotal = $recoverable_exp == 0 ? 0 : -100;
} else {
    $variancePTotal = $varianceTotal / $budgetEXPDRTotal * 100;
}

$variancePTotal_display = formatting($variancePTotal);
if ($budgetEXPDRYTotal == 0) {
    $variancePYTotal = $recoverableA_exp == 0 ? 0 : -100;
} else {
    $variancePYTotal = $varianceYTotal / $budgetEXPDRYTotal * 100;
}

$variancePYTotal_display = formatting($variancePYTotal);

if ($total_rec == '0.00' &&
    $total_recA == '0.00' &&
    $budgetEXPDRTotal_display == '0.00' &&
    $budgetEXPDRYTotal_display == '0.00' &&
    $budgetEXPDRYEARTotal_display == '0.00' &&
    $varianceTotal_display == '0.00' &&
    $variancePTotal_display == '0.00' &&
    $varianceYTotal_display == '0.00' &&
    $variancePYTotal_display == '0.00') {
    $line -= 20;
    $pdf->setColorExt('fill', 'rgb', 1, 1, 1, 0);
    $pdf->rect(20, 570 - $line, 170, 38);
    $pdf->fill();
    $line -= 20;
    $pdf->setColorExt('fill', 'rgb', 0, 0, 0, 0);
} else {
    $line += 12;
    $pdf->setColorExt('fill', 'rgb', 0.88, 0.88, 0.88, 0);
    $pdf->rect(215, 590 - $line, 609, 20);
    $pdf->fill();
    $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);


    $pdf->moveto(215, 610 - $line);
    $pdf->lineto(824, 610 - $line);
    $pdf->stroke();


    $pdf->showBoxed($total_rec, 202, 585 - $line, 75, 20, 'right', '');
    $pdf->showBoxed($total_recA, 475, 585 - $line, 75, 20, 'right', '');
    $pdf->showBoxed($budgetEXPDRTotal_display, 262, 585 - $line, 75, 20, 'right', '');
    $pdf->showBoxed($budgetEXPDRYTotal_display, 550, 585 - $line, 75, 20, 'right', '');
    $pdf->showBoxed($budgetEXPDRYEARTotal_display, 745, 585 - $line, 75, 20, 'right', '');
    $pdf->showBoxed($varianceTotal_display, 330, 585 - $line, 75, 20, 'right', '');
    $pdf->showBoxed($variancePTotal_display, 385, 585 - $line, 75, 20, 'right', '');
    $pdf->showBoxed($varianceYTotal_display, 620, 585 - $line, 75, 20, 'right', '');
    $pdf->showBoxed($variancePYTotal_display, 665, 585 - $line, 75, 20, 'right', '');

    // $pdf->showBoxed($total_rec, 212, 585-$line, 75,20, "right", "");
    // $pdf->showBoxed($total_recA, 485, 585-$line, 75,20, "right", "");


    $pdf->moveto(215, 590 - $line);
    $pdf->lineto(824, 590 - $line);
    $pdf->stroke();
}


// --------------------------------------------------------------------------
// VARIABLE OUTGOINGS PAYMENTS
//
// ACCOUNTS FOR INDIV CALCULATIONS
$line += 40;


$accSQL = "SELECT DISTINCT pmxc_acc
		FROM pmxc_ap_alloc
		WHERE  fund IS NULL AND (pmxc_prop = ?)
		AND (pmxc_alloc_dt BETWEEN CONVERT(datetime, ?, 103) AND CONVERT(datetime, ?, 103))
		AND (pmxc_f_type = 'PAY') 	
		AND (pmxc_acc IN
						  (SELECT pmcg_acc
							FROM pmcg_chart_grp
							WHERE (pmcg_grp = 'TRACC2')
							AND pmcg_subgrp = 'EXPVO'))";
$dbh->selectDatabase($clientDB);
$accSQL_result = $dbh->executeScalars($accSQL, [$propertyID, $startFinancialYear, $periodTo]);


$params = [];
$budgetACCSQL = 'SELECT DISTINCT pmep_exp_acc AS account
FROM         pmep_b_exp_per
WHERE     (pmep_prop = ' . addSQLParam($params, $propertyID) . ') AND (pmep_per IN
						  (' . addSQLParam($params, $pmcp_period) . ')) AND (pmep_year IN
						  (' . addSQLParam($params, $pmcp_year) . ")) AND (pmep_exp_acc IN
						  (SELECT pmcg_acc
							FROM pmcg_chart_grp
							WHERE (pmcg_grp = 'TRACC2') AND pmcg_subgrp = 'EXPVO'))";
// echo "23rd BUDGET:" . $budgetACCSQL;
$dbh->selectDatabase($clientDB);
$budget_result = $dbh->executeScalars($budgetACCSQL, $params);


$opBAL_accSQL = "SELECT DISTINCT pmpb_acc as account
		FROM pmpb_p_bal
		WHERE (pmpb_prop = ?)
		AND (pmpb_date BETWEEN CONVERT(datetime, ?, 103) AND CONVERT(datetime, ?, 103))
		AND (pmpb_acc IN (SELECT pmcg_acc
									FROM pmcg_chart_grp
									WHERE (pmcg_grp = 'TRACC2')
									AND (pmcg_subgrp = 'EXPVO')))";
$dbh->selectDatabase($clientDB);
$opBALacc_result = $dbh->executeScalars($opBAL_accSQL, [$propertyID, $startFinancialYear, $periodTo]);
$acc = array_merge($accSQL_result, $opBALacc_result, $budget_result);

$merged = array_unique($acc);


asort($merged);


$budgetEXPVOTotal = 0;
$budgetEXPVOYTotal = 0;
$budgetEXPVOYEARTotal = 0;

$vt = 0;

$show_var = true;
foreach ($merged as $account) {
    // Get Account Name
    $accSQL = 'SELECT pmca_name FROM pmca_chart WHERE pmca_code = ?';
    $dbh->selectDatabase($clientDB);
    $acc_result = $dbh->executeScalar($accSQL, [$account]);
    $account_name = $acc_result;
    $account_name = (strlen($account_name) < $limit) ? $account_name : substr($account_name, 0, $limit) . ' ...';

    // VO PAYMENTS
    $voSQL = "SELECT COALESCE(SUM(pmxc_alloc_amt), 0) amount, SUM (pmxc_tax_amt) as tax_amount
		FROM pmxc_ap_alloc 
		WHERE (pmxc_acc = ?)
				AND (pmxc_f_type = 'PAY') 	
					AND (pmxc_prop = ?) 
			AND (pmxc_alloc_dt BETWEEN CONVERT(datetime, ?, 103) AND CONVERT(datetime, ?, 103))";
    $dbh->selectDatabase($clientDB);
    $vo_result = $dbh->executeSingle($voSQL, [$account, $propertyID, $periodFrom, $periodTo]);
    $vo_exp = $vo_result['amount'];
    $vo_tax_exp = $vo_result['tax_amount'];

    $vo_exp += $vo_tax_exp;
    $vt += $vo_exp;
    $voPOS_exp = $vo_exp * (-1);
    $vo_display = formatting($voPOS_exp, 2);

    // YTD6

    $voASQL = "SELECT COALESCE(SUM(pmxc_alloc_amt), 0) AS amount, COALESCE(SUM(pmxc_tax_amt), 0) as tax_amount
		FROM pmxc_ap_alloc 
		WHERE (pmxc_acc = ?)
				AND (pmxc_f_type = 'PAY') 	
					AND (pmxc_prop = ?) 
			AND (pmxc_alloc_dt BETWEEN CONVERT(datetime, ?, 103) AND CONVERT(datetime, ?, 103))";

    $dbh->selectDatabase($clientDB);
    $voA_result = $dbh->executeSingle($voASQL, [$account, $propertyID, $startFinancialYear, $periodTo]);

    $params = [];
    $unallcashRECSQL = 'SELECT COALESCE(SUM(pmuc_net_amt), 0) as amount
			FROM pmuc_unall_csh
			WHERE (pmuc_prop = ' . addSQLParam($params, $propertyID) . ')
			AND (pmuc_acc = ' . addSQLParam($params, $account) . ')
			AND (pmuc_period >= 1)
						AND (pmuc_period <= ' . addSQLParam($params, $toPeriod) . ')
				AND (pmuc_year IN (' . addSQLParam($params, $pmcp_year) . '))';
    $dbh->selectDatabase($clientDB);
    $recUC_result = $dbh->executeScalar($unallcashRECSQL, $params);
    $recUC_income = $recUC_result;

    // -----------BUDGET FIGURES ---------------------------------
    $params = [];
    $budgetEXPVOSQL = 'SELECT COALESCE(SUM(pmep_b_c_amt), 0) AS amount
		FROM pmep_b_exp_per
		WHERE (pmep_prop = ' . addSQLParam($params, $propertyID) . ') 
		AND (pmep_per IN (' . addSQLParam($params, $pmcp_period) . ')) 
				AND (pmep_year IN (' . addSQLParam($params, $pmcp_year) . ')) 
				AND (pmep_exp_acc = ' . addSQLParam($params, $account) . ')';
    // echo "24th BUDGET:" . $budgetEXPVOSQL;
    $dbh->selectDatabase($clientDB);
    $budgetEXPVO_result = $dbh->executeScalar($budgetEXPVOSQL, $params);
    $budgetEXPVO = $budgetEXPVO_result;
    $budgetEXPVOTotal += $budgetEXPVO;
    $budgetEXPVOTotal_display = formatting($budgetEXPVOTotal);

    $budgetEXPVO_display = formatting($budgetEXPVO);

    $params = [];
    $budgetEXPVOYSQL = 'SELECT COALESCE(SUM(pmep_b_c_amt), 0) AS amount
		FROM pmep_b_exp_per
		WHERE (pmep_prop = ' . addSQLParam($params, $propertyID) . ') 
		AND (pmep_per >= 1) 
		AND (pmep_per <= ' . addSQLParam($params, $toPeriod) . ')
				  AND (pmep_year IN (' . addSQLParam($params, $pmcp_year) . "))
				AND (pmep_exp_acc ='{$account}')";
    // echo "25th BUDGET:" . $budgetEXPVOYSQL;
    $dbh->selectDatabase($clientDB);
    $budgetEXPVOY_result = $dbh->executeScalar($budgetEXPVOYSQL, $params);
    $budgetEXPVOY = $budgetEXPVOY_result;
    $budgetEXPVOY_display = formatting($budgetEXPVOY, 2);
    $budgetEXPVOYTotal += $budgetEXPVOY;

    $budgetEXPVOYTotal_display = formatting($budgetEXPVOYTotal);

    $params = [];
    $budgetEXPVOYEARSQL = 'SELECT COALESCE(SUM(pmep_b_c_amt), 0) AS amount
		FROM pmep_b_exp_per
		WHERE (pmep_prop = ' . addSQLParam($params, $propertyID) . ') 
		AND (pmep_per BETWEEN 1 AND 12)AND (pmep_year IN (' . addSQLParam($params, $pmcp_year) . '))
				AND (pmep_exp_acc = ' . addSQLParam($params, $account) . ')';
    // echo "26th BUDGET:" . $budgetEXPVOYEARSQL;
    $dbh->selectDatabase($clientDB);
    $budgetEXPVOYEAR_result = $dbh->executeScalar($budgetEXPVOYEARSQL, $params);
    $budgetEXPVOYEAR = $budgetEXPVOYEAR_result;
    $budgetEXPVOYEAR_display = formatting($budgetEXPVOYEAR, 2);
    $budgetEXPVOYEARTotal += $budgetEXPVOYEAR;
    $budgetEXPVOYEARTotal_display = formatting($budgetEXPVOYEARTotal, 2);

    // OPENING BALANCE FOR EACH ACCOUNT
    $openBalVOSQL = 'SELECT COALESCE(SUM(pmpb_bal), 0) as amount
		FROM pmpb_p_bal
		WHERE (pmpb_prop = ?) 
		AND (pmpb_date BETWEEN CONVERT(datetime, ?, 103) AND CONVERT(datetime, ?, 103))
		AND (pmpb_acc = ?)';

    $dbh->selectDatabase($clientDB);
    $openBalVO_result = $dbh->executeScalar(
        $openBalVOSQL,
        [$propertyID, $startFinancialYear, $periodTo, $account]
    );

    // -----------OPENING BALANCE FIGURES------------------------
    $openBalVO_exp = $openBalVO_result;
    // ----------------------------------------------------------

    $voA_exp = $voA_result['amount'];
    $voA_tax_income = $voA_result['tax_amount'];
    $voA_exp = $voA_exp + $voA_tax_income - $openBalVO_exp;
    $voAPOS_exp = $voA_exp * (-1);
    $voA_display = formatting($voAPOS_exp, 2);


    $variance = $budgetEXPVO - $voPOS_exp;
    $variance_display = formatting($variance);
    if ($budgetEXPVO == 0) {
        $varianceP = $voPOS_exp == 0 ? 0 : -100;
    } else {
        $varianceP = $variance / $budgetEXPVO * 100;
    }

    $varianceP_display = formatting($varianceP);


    $varianceY = $budgetEXPVOY - $voAPOS_exp;
    $varianceY_display = formatting($varianceY);
    if ($budgetEXPVOY == 0) {
        $variancePY = $voAPOS_exp == 0 ? 0 : -100;
    } else {
        $variancePY = $varianceY / $budgetEXPVOY * 100;
    }

    $variancePY_display = formatting($variancePY);


    if ($vo_display == '0.00' && $voA_display == '0.00' && $budgetEXPVO_display == '0.00' && $budgetEXPVOY_display == '0.00' && $budgetEXPVOYEAR_display == '0.00') {
        // DON NOT DISPLAY LINE

    } else {
        if (($show_var)) {
            $show_var = false;
            if ($line >= 560) {
                $page++;


                $line = 560;
                $pdf->setlinewidth(0.5);           // zhirnaja nizhnjaja
                $pdf->moveto(18, 600 - $line);
                $pdf->lineto(824, 600 - $line);
                $pdf->stroke();


                $pdf->moveto(18, 600 - $line);      // tonkaja levaja
                $pdf->lineto(18, 515);
                $pdf->stroke();

                $pdf->moveto(824, 600 - $line);     // tonkaja pravaja
                $pdf->lineto(824, 515);
                $pdf->stroke();


                $pdf->moveto(215, 515);           // pervaja vertikalnaja
                $pdf->lineto(215, 600 - $line);
                $pdf->stroke();

                $pdf->moveto(470, 515);           // vtoraja vertikalnaja
                $pdf->lineto(470, 600 - $line);
                $pdf->stroke();

                $pdf->moveto(745, 515);           // tretja vertikalnaja
                $pdf->lineto(745, 600 - $line);
                $pdf->stroke();

                $pdf->end_page_ext('');
                $pdf->begin_page_ext(842, 595, '');


                $page_header = 'Cash Payments Summary (...)';
                include __DIR__ . '/functions/mediumReportHeader.php';
                if ($logo) {
                    generateLogo('landscape');
                }

                $pdf->setFontExt($_fonts['Helvetica'], 8);
                $pdf->showBoxed("Page {$page}", 750, 0, 75, 30, 'right', '');

                $traccFooter = new TraccFooter(
                    'assets/clientLogos/tracc_logo_footer.jpg',
                    'cashReceiptsAndPayments',
                    A4_LANDSCAPE
                );
                $traccFooter->prerender($pdf);

                $line = 160;
                $pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
                $pdf->showBoxed(
                    ucwords(strtolower($_SESSION['country_default']['variable_outgoings'])) . ' Expenditure',
                    22,
                    590 - $line,
                    400,
                    40,
                    'left',
                    ''
                );

                $pdf->setFontExt($_fonts['Helvetica'], 8);
            } else {
                $pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
                $pdf->showBoxed(
                    ucwords(strtolower($_SESSION['country_default']['variable_outgoings'])) . ' Expenditure',
                    22,
                    600 - $line,
                    400,
                    20,
                    'left',
                    ''
                );
                $pdf->setFontExt($_fonts['Helvetica'], 8);
                $line -= 8;
            }
        }

        if (($line) >= 560) {
            $page++;


            $pdf->setlinewidth(0.5);           // zhirnaja nizhnjaja
            $pdf->moveto(18, 600 - $line);
            $pdf->lineto(824, 600 - $line);
            $pdf->stroke();


            $pdf->moveto(18, 600 - $line);      // tonkaja levaja
            $pdf->lineto(18, 515);
            $pdf->stroke();

            $pdf->moveto(824, 600 - $line);     // tonkaja pravaja
            $pdf->lineto(824, 515);
            $pdf->stroke();


            $pdf->moveto(215, 515);           // pervaja vertikalnaja
            $pdf->lineto(215, 600 - $line);
            $pdf->stroke();


            $pdf->moveto(470, 515);           // vtoraja vertikalnaja
            $pdf->lineto(470, 600 - $line);
            $pdf->stroke();

            $pdf->moveto(745, 515);           // tretja vertikalnaja
            $pdf->lineto(745, 600 - $line);
            $pdf->stroke();

            $pdf->end_page_ext('');
            $pdf->begin_page_ext(842, 595, '');
            // PAGE 4 - CASH PAYMENTS ...


            $page_header = 'Cash Payments Summary (...)';
            include __DIR__ . '/functions/mediumReportHeader.php';
            if ($logo) {
                generateLogo('landscape');
            }

            $pdf->setFontExt($_fonts['Helvetica'], 8);
            // $pdf->showBoxed ("Printed on $date", 22, 5, 275, 30, "left", "");
            $pdf->showBoxed("Page {$page}", 750, 0, 75, 30, 'right', '');

            // insert tracc footer
            $traccFooter = new TraccFooter(
                'assets/clientLogos/tracc_logo_footer.jpg',
                'cashReceiptsAndPayments',
                A4_LANDSCAPE
            );
            $traccFooter->prerender($pdf);

            $line = 160;
            $pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
            $pdf->showBoxed(
                ucwords(strtolower($_SESSION['country_default']['variable_outgoings'])) . ' Expenditure (...)',
                22,
                590 - $line,
                400,
                40,
                'left',
                ''
            );
            $line = 142;
            $pdf->setFontExt($_fonts['Helvetica'], 8);
        }

        $pdf->showBoxed($account, 22, 590 - $line, 400, 12, 'left', '');
        $pdf->showBoxed($account_name, 50, 590 - $line, 400, 12, 'left', '');
        $pdf->showBoxed($vo_display, 202, 590 - $line, 75, 12, 'right', '');
        $pdf->showBoxed($budgetEXPVO_display, 262, 590 - $line, 75, 12, 'right', '');
        $pdf->showBoxed($variance_display, 330, 590 - $line, 75, 12, 'right', '');
        $pdf->showBoxed($varianceP_display, 385, 590 - $line, 75, 12, 'right', '');
        $pdf->showBoxed($voA_display, 475, 590 - $line, 75, 12, 'right', '');
        $pdf->showBoxed($budgetEXPVOY_display, 550, 590 - $line, 75, 12, 'right', '');
        $pdf->showBoxed($varianceY_display, 620, 590 - $line, 75, 12, 'right', '');
        $pdf->showBoxed($variancePY_display, 665, 590 - $line, 75, 12, 'right', '');
        $pdf->showBoxed($budgetEXPVOYEAR_display, 745, 590 - $line, 75, 12, 'right', '');
        $line += 10;
    }
}

$voexpM = totalexpenses($propertyID, $periodFrom, $periodTo, 'EXPVO');
$voexpTotal = $voexpM['net_amount'];

$voexpYear = totalexpenses($propertyID, $startFinancialYear, $periodTo, 'EXPVO');
$voexpATotal = $voexpYear['net_amount'] + $voexp_takeon['amount'];


$total_vo = formatting($voexpTotal, 2);
$total_voA = formatting($voexpATotal, 2);


$total_variance = $budgetEXPVOTotal - $voexpTotal;
$total_variance_display = formatting($total_variance);
if ($budgetEXPVOTotal == 0) {
    $total_varianceP = $voexpTotal == 0 ? 0 : -100;
} else {
    $total_varianceP = $total_variance / $budgetEXPVOTotal * 100;
}

$total_varianceP_display = formatting($total_varianceP);

$total_varianceY = $budgetEXPVOYTotal - $voexpATotal;
$total_varianceY_display = formatting($total_varianceY);
if ($budgetEXPVOYTotal == 0) {
    $total_variancePY = $voexpATotal == 0 ? 0 : -100;
} else {
    $total_variancePY = $total_varianceY / $budgetEXPVOYTotal * 100;
}

$total_variancePY_display = formatting($total_variancePY);

$budgetEXPVOYTotal_display = formatting($budgetEXPVOYTotal);
$budgetEXPVOTotal_display = formatting($budgetEXPVOTotal);


if ($total_vo == '0.00' &&
    $total_voA == '0.00' &&
    ($budgetEXPVOTotal_display == '0.00' || $budgetEXPVOTotal_display == '') &&
    ($budgetEXPVOYTotal_display == '0.00' || $budgetEXPVOYTotal_display == '')) {
    $line -= 20;
    // $pdf->setColorExt("fill", "rgb", 1, 1, 1, 0);
    // $pdf->rect(20,570-$line,170,38);
    // $pdf->fill();
    $line -= 20;
    $pdf->setColorExt('fill', 'rgb', 0, 0, 0, 0);
} else {
    if ($line >= 560) {
        $page++;


        $pdf->setlinewidth(0.5);           // zhirnaja nizhnjaja
        $pdf->moveto(18, 600 - $line);
        $pdf->lineto(824, 600 - $line);
        $pdf->stroke();


        $pdf->moveto(18, 600 - $line);      // tonkaja levaja
        $pdf->lineto(18, 515);
        $pdf->stroke();

        $pdf->moveto(824, 600 - $line);     // tonkaja pravaja
        $pdf->lineto(824, 515);
        $pdf->stroke();


        $pdf->moveto(215, 515);           // pervaja vertikalnaja
        $pdf->lineto(215, 600 - $line);
        $pdf->stroke();

        $pdf->moveto(470, 515);           // vtoraja vertikalnaja
        $pdf->lineto(470, 600 - $line);
        $pdf->stroke();

        $pdf->moveto(745, 515);           // tretja vertikalnaja
        $pdf->lineto(745, 600 - $line);
        $pdf->stroke();

        $pdf->end_page_ext('');
        $pdf->begin_page_ext(842, 595, '');
        // PAGE 4 - CASH PAYMENTS ...


        $page_header = 'Cash Payments Summary (...)';
        include __DIR__ . '/functions/mediumReportHeader.php';
        if ($logo) {
            generateLogo('landscape');
        }

        $pdf->setFontExt($_fonts['Helvetica'], 8);
        // $pdf->showBoxed ("Printed on $date", 22, 5, 275, 30, "left", "");
        $pdf->showBoxed("Page {$page}", 750, 0, 75, 30, 'right', '');

        // insert tracc footer
        $traccFooter = new TraccFooter(
            'assets/clientLogos/tracc_logo_footer.jpg',
            'cashReceiptsAndPayments',
            A4_LANDSCAPE
        );
        $traccFooter->prerender($pdf);

        $line = 160;
        $pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
        $pdf->showBoxed(
            ucwords(strtolower($_SESSION['country_default']['variable_outgoings'])) . ' Expenditure (...)',
            22,
            590 - $line,
            400,
            40,
            'left',
            ''
        );
        $line = 145;
        $pdf->setFontExt($_fonts['Helvetica'], 8);
    }

    $line += 10; // was 2 17 dec 08
    $pdf->setColorExt('fill', 'rgb', 0.88, 0.88, 0.88, 0);
    $pdf->rect(215, 590 - $line, 609, 20);
    $pdf->fill();
    $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);


    $pdf->moveto(215, 610 - $line);
    $pdf->lineto(824, 610 - $line);
    $pdf->stroke();


    $pdf->showBoxed($total_vo, 202, 585 - $line, 75, 20, 'right', '');
    $pdf->showBoxed($total_voA, 475, 585 - $line, 75, 20, 'right', '');
    $pdf->showBoxed($total_variance_display, 330, 585 - $line, 75, 20, 'right', '');
    $pdf->showBoxed($total_varianceP_display, 385, 585 - $line, 75, 20, 'right', '');
    $pdf->showBoxed($total_varianceY_display, 620, 585 - $line, 75, 20, 'right', '');
    $pdf->showBoxed($total_variancePY_display, 665, 585 - $line, 75, 20, 'right', '');
    $pdf->showBoxed($budgetEXPVOTotal_display, 262, 585 - $line, 75, 20, 'right', '');
    $pdf->showBoxed($budgetEXPVOYTotal_display, 550, 585 - $line, 75, 20, 'right', '');
    $pdf->showBoxed($budgetEXPVOYEARTotal_display, 745, 585 - $line, 75, 20, 'right', '');

    $pdf->moveto(215, 590 - $line);
    $pdf->lineto(824, 590 - $line);
    $pdf->stroke();
}


// --------------------------------------------------------------------------------------
// TOTAL CASH RECEIPTS
//


$line += 30;
if ($line >= 550) {
    $line -= 15;
    $page++;


    $pdf->setlinewidth(0.5);           // zhirnaja nizhnjaja
    $pdf->moveto(18, 600 - $line);
    $pdf->lineto(824, 600 - $line);
    $pdf->stroke();


    $pdf->moveto(18, 600 - $line);      // tonkaja levaja
    $pdf->lineto(18, 515);
    $pdf->stroke();

    $pdf->moveto(824, 600 - $line);     // tonkaja pravaja
    $pdf->lineto(824, 515);
    $pdf->stroke();


    $pdf->moveto(215, 515);           // pervaja vertikalnaja
    $pdf->lineto(215, 600 - $line);
    $pdf->stroke();

    $pdf->moveto(470, 515);           // vtoraja vertikalnaja
    $pdf->lineto(470, 600 - $line);
    $pdf->stroke();

    $pdf->moveto(745, 515);           // tretja vertikalnaja
    $pdf->lineto(745, 600 - $line);
    $pdf->stroke();

    $pdf->end_page_ext('');
    $pdf->begin_page_ext(842, 595, '');
    // PAGE 4 - CASH PAYMENTS ...


    $page_header = 'Cash Payments Summary (...)';
    include __DIR__ . '/functions/mediumReportHeader.php';
    if ($logo) {
        generateLogo('landscape');
    }

    $pdf->setFontExt($_fonts['Helvetica'], 8);
    // $pdf->showBoxed ("Printed on $date", 22, 5, 275, 30, "left", "");
    $pdf->showBoxed("Page {$page}", 750, 0, 75, 30, 'right', '');

    // insert tracc footer
    $traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'cashReceiptsAndPayments', A4_LANDSCAPE);
    $traccFooter->prerender($pdf);

    $line = 160;
}

$line += 20;

$pdf->setColorExt('fill', 'rgb', 0.88, 0.88, 0.88, 0);
$pdf->rect(215, 590 - $line, 609, 20);
$pdf->fill();
$pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);


$pdf->moveto(215, 610 - $line);
$pdf->lineto(824, 610 - $line);
$pdf->stroke();

$hpos = 585;
$totalAllVarianceExp_display = formatting($totalAllVarianceExp, 2);
$totalAllVarianceExpY_display = formatting($totalAllVarianceExpY, 2);
$pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
$pdf->showBoxed('Total Cash Payments', 22, 585 - $line, 400, 20, 'left', '');
$pdf->showBoxed($totalAllExp_display, 202, 585 - $line, 75, 20, 'right', '');
$pdf->showBoxed($totalAllExpA_display, 475, 585 - $line, 75, 20, 'right', '');

$totalAllBudgetExpM = $budgetEXPOWNTotal + $budgetEXPDRTotal + $budgetEXPVOTotal;
$totalAllBudgetExpM_display = formatting($totalAllBudgetExpM, 2);

$pdf->showBoxed($totalAllBudgetExpM_display, 262, 585 - $line, 75, 20, 'right', '');
$pdf->showBoxed($totalAllBudgetExpY_display, 550, 585 - $line, 75, 20, 'right', '');
$pdf->showBoxed($totalAllVarianceExp_display, 333, 585 - $line, 75, 20, 'right', '');
$pdf->showBoxed($totalAllVarianceExpY_display, 620, 585 - $line, 75, 20, 'right', '');
$pdf->showBoxed($totalAllVPExp_display, 385, 585 - $line, 75, 20, 'right', '');
$pdf->showBoxed($totalAllVPExpY_display, 665, 585 - $line, 75, 20, 'right', '');
$pdf->showBoxed($totalAllBudgetExpYTD_display, 745, 585 - $line, 75, 20, 'right', '');


$pdf->setlinewidth(0.5);           // zhirnaja nizhnjaja
$pdf->moveto(18, 590 - $line);
$pdf->lineto(824, 590 - $line);
$pdf->stroke();


$pdf->moveto(18, 590 - $line);      // tonkaja levaja
$pdf->lineto(18, 515);
$pdf->stroke();

$pdf->moveto(824, 590 - $line);     // tonkaja pravaja
$pdf->lineto(824, 515);
$pdf->stroke();


$pdf->moveto(215, 515);           // pervaja vertikalnaja
$pdf->lineto(215, 590 - $line);
$pdf->stroke();

$pdf->moveto(470, 515);           // vtoraja vertikalnaja
$pdf->lineto(470, 590 - $line);
$pdf->stroke();

$pdf->moveto(745, 515);           // tretja vertikalnaja
$pdf->lineto(745, 590 - $line);
$pdf->stroke();

// Footer

// insert tracc footer
$traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'cashReceiptsAndPayments', A4_LANDSCAPE);
$traccFooter->prerender($pdf);

$pdf->end_page_ext('');


/*
if ($startCashPaymentsPages == $page)
{
$cashPaymentsPages = $page;
}
else
{
$cashPaymentsPages = $startCashPaymentsPages - $page;
}
*/

$line = 65;
include __DIR__ . '/functions/propertyFundReport.php';

$cashPaymentsPages = $startCashPaymentsPages;

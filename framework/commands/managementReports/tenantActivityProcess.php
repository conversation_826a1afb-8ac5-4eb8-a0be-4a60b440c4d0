<?php

use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Fill;

/**
 * Process information gathered in order to generate a tenant activity.
 *
 * @param  $context  array Mandatory. Referenced array containing data needed to continue.
 *
 * <AUTHOR> Reyes
 *
 * @since 2012-09-21
 *
 * @modified 2012-12-07: Added display on screen option. [Morph]
 **/
function tenantActivityProcess($context)
{
    global $clientDirectory, $pathPrefix;

    if (! isPostback()) {
        $view = new MasterPage(userViews(), '/managementReports/tenantActivityProcess.html');
        $view->setSection($context['module']);
    } else {
        $view = new UserControl(userViews(), '/managementReports/tenantActivityProcess.html');
    }
    $view->bindAttributesFrom($_REQUEST);

    $_report['reportName'] = 'Tenant Activity';
    if ($context['propertyID']) {
        $propertyCode = $context['propertyID'];
        $propertyLookUp = dbGetPropertyLookup($propertyCode);
        $propertyName = $propertyLookUp['propertyName'];
    }

    $transactionOption = $view->items['transactionOption'];
    $openBalance['description'] = 'Opening Balance';
    if ($transactionOption == 'chargesOnly') {
        $closedBalance['description'] = 'Total Charges';
    } elseif ($transactionOption == 'receiptsOnly') {
        $closedBalance['description'] = 'Total Receipts';
    } else {
        $closedBalance['description'] = 'Closing Balance';
    }
    $totalCharges['description'] = 'Total Charges';
    $totalReceipts['description'] = 'Total Receipts';

    if ($context['propertyLeaseID']) {
        $leaseProperties = [];
        foreach ($context['propertyLeaseID'] as $propLeaseID) {
            [$propertyID, $leaseID] = explode('~', $propLeaseID);

            if (! in_array($propertyID, $leaseProperties)) {
                $leaseProperties[] = $propertyID;
            }
            $view->items['tenantID'][] = $leaseID;
        }
        $tenantID = $view->items['tenantID'];
    } else {
        $tenantID = deserializeParameters($view->items['tenantID']);
    }

    if ($view->items['app_origin'] == 'lease_page') {
        $fromDate = $view->items['fromDate'];
        $toDate = $view->items['toDate'];
    } else {
        $fromDate = $context['fromDate'];
        $toDate = $context['toDate'];
    }

    if ($view->items['accountOption'] == 'multipleCodes') {
        $accountID = deserializeParameters($view->items['accountID']);
    } elseif ($view->items['accountOption'] == 'accountRange' && $view->items['accountIDRange1'] && $view->items['accountIDRange2']) {
        $accountID = getValidAccountCodesFromRange($view->items['accountIDRange1'], $view->items['accountIDRange2']);
    }

    if ($_SESSION['user_type'] == USER_OWNER) {
        $view->items['propertyManager'] = 'all';
        $view->items['showZeroReceipts'] = 'Yes';
        $view->items['showCreditAllocations'] = 'Yes';
    }

    $reportResult = dbGetTenantActivityReport(
        $propertyCode,
        $tenantID,
        $transactionOption,
        $view->items['allDates'],
        $accountID,
        $fromDate,
        $toDate,
        $view->items['propertyManager'],
        $view->items['sortOption'],
        $view->items['showZeroReceipts'],
        $view->items['showCreditAllocations']
    );

    $count = count($reportResult);
    // sort by transaction type $reportResult
    if ($view->items['sortOption'] == 'sortAccountID' && $transactionOption == 'allTransactions') {
        usort($reportResult, function ($a, $b) {
            $retval = $a['tenantID'] <=> $b['tenantID'];
            if (! $retval) {
                $retval = $a['accountCode'] <=> $b['accountCode'];
            }
            if (! $retval) {
                $retval = $a['tableName'] <=> $b['tableName'];
            }

            return $retval;
        });
        usort($reportResult, function ($a, $b) {
            $retval = $a['tenantID'] <=> $b['tenantID'];
            if (! $retval) {
                $retval = $a['tableName'] <=> $b['tableName'];
            }
            if (! $retval) {
                $retval = $a['accountCode'] <=> $b['accountCode'];
            }

            return $retval;
        });
        uasort($reportResult, function ($a, $b) {
            $retval = $a['tenantID'] <=> $b['tenantID'];
            if (! $retval) {
                $retval = $a['tableName2'] <=> $b['tableName2'];
            }
            if (! $retval) {
                $retval = $a['accountCode'] <=> $b['accountCode'];
            }
            if ($a['tenantID'] == $b['tenantID'] && $a['tableName2'] == $b['tableName2'] && $a['accountCode'] == $b['accountCode']) {
                $format = 'd/m/Y';
                $ascending = true;
                $zone = new DateTimeZone('UTC');
                $d1 = DateTime::createFromFormat($format, $a['dateSort'], $zone)->getTimestamp();
                $d2 = DateTime::createFromFormat($format, $b['dateSort'], $zone)->getTimestamp();

                return $ascending ? ($d1 - $d2) : ($d2 - $d1);
            }

            return $retval;
        });
        $reportResult = array_values($reportResult);
    }

    if ($context[IS_TASK]) {
        $view->bindAttributesFrom($context);
        extract($context);
    } else {
        $view->bindAttributesFrom($context);
        $view->bindAttributesFrom($_REQUEST);
        if ($count > THRESHOLD_TENANTACTIVITY) {
            $queue = new Queue(TASKTYPE_TENANT_ACTIVITY);
            // -- no real need to schedule this report, code can be cleaned up at a later date
            // $queue->add ($_SESSION['clientID'], $_SESSION['un'], 'command=tenantActivityProcess&module=managementReports', $_REQUEST);
        }
    }

    if ($count && ($context[IS_TASK] || $count <= THRESHOLD_TENANTACTIVITY)) {
        $format = $view->items['format'];
        $numberFormat = ($format == FILETYPE_XLS) ? false : true;

        if (! $context[DOC_MASTER]) {
            $logoFile = dbGetClientLogo();
            $logoPath = "assets/clientLogos/{$logoFile}";
            $_filePath = "{$pathPrefix}{$clientDirectory}/{$format}/" . DOC_TENANTACTIVITY . '/';
            $_downloadPath = "{$clientDirectory}/{$format}/" . DOC_TENANTACTIVITY;
            $file = DOC_TENANTACTIVITY . '_' . date('Ymd') . ".{$format}";
            $filePath = $_filePath . $file;
            $downloadPath = "{$_downloadPath}/{$file}";
        }

        if ($transactionOption != 'chargesOnly' && $transactionOption != 'receiptsOnly') {
            $OBResult = dbGetTenantOpeningBalance($propertyCode, $tenantID, $view->items['allDates'], $accountID, $fromDate);
            foreach ($OBResult as $k => $v) {
                $tenant = $v['tenantID'];
                $closingBalance[$tenant]['balance'] = $openingBalance[$tenant]['balance'] += $v['amt'];
            }
        }
        foreach ($reportResult as $k => $v) {
            $tenant = $v['tenantID'];
            $tenants[$tenant][$k]['transactionDate'] = $v['transactionDate'];

            if ($v['transactionType'] == 'CSH/REV') {
                if ($v['netAmount'] < 0) {
                    $tenants[$tenant][$k]['transactionType'] = 'REV';
                } else {
                    $tenants[$tenant][$k]['transactionType'] = 'CSH';
                }
            } else {
                $tenants[$tenant][$k]['transactionType'] = $v['transactionType'];
            }

            $tenants[$tenant][$k]['propertyID'] = $v['propertyID'];
            $tenants[$tenant][$k]['name'] = dbGetLeaseName($v['propertyID'], $tenant);
            $tenants[$tenant][$k]['accountCode'] = $v['accountCode'];
            $tenants[$tenant][$k]['tenantID'] = $v['tenantID'];
            $tenants[$tenant][$k]['description'] = $v['description'];
            if ($v['tableName'] == 'AR') {
                $tenants[$tenant][$k]['netAmount'] = toDecimal($v['netAmount'], 2, '0', $numberFormat);
                $tenants[$tenant][$k]['chargedGST'] = toDecimal($v['chargedGST'], 2, '0', $numberFormat);
                $tenants[$tenant][$k]['totalCharge'] = toDecimal($v['totalCharge'], 2, '0', $numberFormat);
                $closingBalance[$tenant]['netAmount'] += $v['netAmount'];
                $closingBalance[$tenant]['chargedGST'] += $v['chargedGST'];
                $closingBalance[$tenant]['totalCharge'] += $v['totalCharge'];
                $tenants[$tenant][$k]['balance'] = toDecimal($closingBalance[$tenant]['balance'] += $v['totalCharge'], 2, '0', $numberFormat);
            } else {
                $tenants[$tenant][$k]['netTotal'] = toDecimal($v['netAmount'], 2, '0', $numberFormat);
                $tenants[$tenant][$k]['receivedGST'] = toDecimal($v['chargedGST'], 2, '0', $numberFormat);
                $tenants[$tenant][$k]['totalAmount'] = toDecimal($v['totalCharge'], 2, '0', $numberFormat);
                $closingBalance[$tenant]['netTotal'] += $v['netAmount'];
                $closingBalance[$tenant]['receivedGST'] += $v['chargedGST'];
                $closingBalance[$tenant]['totalAmount'] += $v['totalCharge'];
                $tenants[$tenant][$k]['balance'] = toDecimal($closingBalance[$tenant]['balance'] -= $v['totalCharge'], 2, '0', $numberFormat);
            }
            $tenants[$tenant][$k]['fromDate'] = $v['fromDate'];
            $tenants[$tenant][$k]['toDate'] = $v['toDate'];
            $tenants[$tenant][$k]['invoiceNumber'] = $v['invoiceNumber'];
            $tenants[$tenant][$k]['receiptNumber'] = $v['receiptNumber'];

            if ($tenants[$tenant][$previousKey]['transactionDate']) {
                $tenants[$tenant][$previousKey]['totalReceipt'] = ($v['batchNo'] != $previousBatchNo && $tenants[$tenant][$previousKey] && $reportResult[$previousKey]['tableName'] == 'PMXD') ?
                    toDecimal($totalReceipt, 2, '0', $numberFormat) : '';
            }
            if ($v['batchNo'] == $previousBatchNo && $v['tenantID'] == $previousTenant) {
                $totalReceipt += $v['totalCharge'];
            } else {
                $totalReceipt = $v['totalCharge'];
            }

            $previousKey = $k;
            $previousTenant = $v['tenantID'];
            $previousBatchNo = $v['batchNo'];
            if ($totalReceipt && $tenants[$tenant][$previousKey] && $reportResult[$previousKey]['tableName'] == 'PMXD') {
                $tenants[$tenant][$previousKey]['totalReceipt'] = toDecimal($totalReceipt, 2, '0', $numberFormat);
            }
            if ($v['transactionType'] == 'CRE' || $v['transactionType'] == 'INV') {
                $tenants[$tenant][$k]['transactionType2'] = 'INV';
            } elseif ($v['transactionType'] == 'CSH' || $v['transactionType'] == 'REV' || $v['transactionType'] == 'CSH/REV') {
                $tenants[$tenant][$k]['transactionType2'] = 'CSH';
            }
        }
        if ($tenants[$tenant][$previousKey] && $reportResult[$previousKey]['tableName'] == 'PMXD') {
            $tenants[$tenant][$previousKey]['totalReceipt'] = toDecimal($totalReceipt, 2, '0', $numberFormat);
        }

        // sub total for each account
        if ($view->items['sortOption'] == 'sortAccountID') {
            $lastTenant = '';
            $key = 0;
            $tenants_ = [];
            $lastKey = null;
            foreach ($tenants as $kk => $vv) {
                $lastAccountCode = $lastAccountCodePropertyTenant = $lastPropertyTenant = $lastTransactionType = '';
                $netAmountTenant = $chargedGSTTenant = $totalChargeTenant = $netAmountClosing = $chargedGSTClosing = $totalChargeClosing = $balanceTenant = $balanceClosing = 0;
                $netTotalTenant = $receivedGSTTenant = $totalAmountTenant = $netTotalClosing = $receivedGSTClosing = $totalAmountClosing = 0;
                end($tenants[$kk]);
                foreach ($vv as $key_ => $v) {

                    if (($lastAccountCodePropertyTenant != '' && $lastAccountCodePropertyTenant != $v['accountCode'] . $v['propertyID'] . $v['tenantID'])) {
                        // display account code total if sorted by account code
                        $key++;
                        $arr = [];
                        $arr['width']['transactionDate'] = 300;
                        $arr['bold'] = true;
                        $arr['bgcolor'] = [220 / 255, 242 / 255, 255 / 255];

                        $arr['transactionDate'] = "Account Total for $lastAccountCode " . dbGetAccountName($lastAccountCode);
                        $arr['tenantID'] = $lastTenant;
                        $arr['netAmount'] = toDecimal($netAmountTenant, 2, '0', $numberFormat);
                        $arr['chargedGST'] = toDecimal($chargedGSTTenant, 2, '0', $numberFormat);
                        $arr['totalCharge'] = toDecimal($totalChargeTenant, 2, '0', $numberFormat);
                        if ($lastTransactionType == 'CSH') {
                            $arr['netTotal'] = toDecimal($netTotalTenant, 2, '0', $numberFormat);
                            $arr['receivedGST'] = toDecimal($receivedGSTTenant, 2, '0', $numberFormat);
                            $arr['totalAmount'] = toDecimal($totalAmountTenant, 2, '0', $numberFormat);
                        } else {
                            $arr['netTotal'] = toDecimal($netAmountTenant, 2, '0', $numberFormat);
                            $arr['receivedGST'] = toDecimal($chargedGSTTenant, 2, '0', $numberFormat);
                            $arr['totalAmount'] = toDecimal($totalChargeTenant, 2, '0', $numberFormat);
                        }
                        $arr['balance'] = toDecimal($balanceTenant, 2, '0', $numberFormat);
                        $arr['accountCode'] = null;
                        $arr['description'] = null;
                        $arr['transactionType'] = null;
                        $tenants_[$lastTenant][$key] = $arr;
                        if ($format != FILETYPE_SCREEN) {
                            $key = $key + 1;
                            $tenants_[$lastTenant][$key] = null;
                        } // add null value for pdf and excel
                        $netAmountTenant = $chargedGSTTenant = $totalChargeTenant = $netAmountClosing = $chargedGSTClosing = $totalChargeClosing = $balanceTenant = $balanceClosing = 0;
                        $netTotalTenant = $receivedGSTTenant = $totalAmountTenant = $netTotalClosing = $receivedGSTClosing = $totalAmountClosing = 0;

                        // used by excel
                        $tenants_[$lastTenant][$key]['headerStyle'] =
                            [
                                'fill' => [
                                    'type' => Fill::FILL_SOLID,
                                    'color' => ['rgb' => 'e1e1e1'],
                                ],
                                'font' => ['color' => ['rgb' => '000000']],
                                'alignment' => ['vertical' => Alignment::VERTICAL_CENTER, 'horizontal' => Alignment::HORIZONTAL_LEFT],
                            ];
                    }
                    $netAmountTenant = $netAmountTenant + floatval(str_replace(',', '', str_replace(')', '', str_replace('(', '-', $v['netAmount']))));
                    $chargedGSTTenant = $chargedGSTTenant + floatval(str_replace(',', '', str_replace(')', '', str_replace('(', '-', $v['chargedGST']))));
                    $totalChargeTenant = $totalChargeTenant + floatval(str_replace(',', '', str_replace(')', '', str_replace('(', '-', $v['totalCharge']))));
                    $netTotalTenant = $netTotalTenant + floatval(str_replace(',', '', str_replace(')', '', str_replace('(', '-', $v['netTotal']))));
                    $receivedGSTTenant = $receivedGSTTenant + floatval(str_replace(',', '', str_replace(')', '', str_replace('(', '-', $v['receivedGST']))));
                    $totalAmountTenant = $totalAmountTenant + floatval(str_replace(',', '', str_replace(')', '', str_replace('(', '-', $v['totalAmount']))));
                    $balanceTenant = $balanceTenant + floatval(str_replace(',', '', str_replace(')', '', str_replace('(', '-', $v['balance']))));

                    if ($lastKey + 1 == key($tenants[$kk])) {

                        $arr_['width']['transactionDate'] = 300;
                        $arr_['bold'] = true;
                        $arr_['bgcolor'] = [220 / 255, 242 / 255, 255 / 255];

                        $arr_['transactionDate'] = "Account Total for {$vv[$lastKey]['accountCode']} " . dbGetAccountName($vv[$lastKey]['accountCode']);
                        $arr_['tenantID'] = $lastTenant;
                        $arr_['netAmount'] = toDecimal($netAmountTenant, 2, '0', $numberFormat);
                        $arr_['chargedGST'] = toDecimal($chargedGSTTenant, 2, '0', $numberFormat);
                        $arr_['totalCharge'] = toDecimal($totalChargeTenant, 2, '0', $numberFormat);

                        if ($lastTransactionType == 'CSH') {
                            $arr_['netTotal'] = toDecimal($netTotalTenant, 2, '0', $numberFormat);
                            $arr_['receivedGST'] = toDecimal($receivedGSTTenant, 2, '0', $numberFormat);
                            $arr_['totalAmount'] = toDecimal($totalAmountTenant, 2, '0', $numberFormat);
                        } else {
                            $arr_['netTotal'] = toDecimal($netAmountTenant, 2, '0', $numberFormat);
                            $arr_['receivedGST'] = toDecimal($chargedGSTTenant, 2, '0', $numberFormat);
                            $arr_['totalAmount'] = toDecimal($totalChargeTenant, 2, '0', $numberFormat);
                        }

                        $arr_['balance'] = toDecimal($balanceTenant, 2, '0', $numberFormat);
                        $arr_['accountCode'] = null;
                        $arr_['description'] = null;
                        $arr_['transactionType'] = null;

                        $arr_total['width']['transactionDate'] = 300;
                        $arr_total['bold'] = true;
                        $arr_total['bgcolor'] = [0.9, 0.9, 0.9];
                        $arr_total['transactionDate'] = ($lastTransactionType == 'INV') ? 'Total Charges' : 'Total Receipts';
                        $arr_total['tenantID'] = $lastTenant;
                        if ($lastTransactionType == 'INV') {
                            $arr_total['netTotal'] = toDecimal($closingBalance[$kk]['netAmount'], 2, '0', $numberFormat);
                            $arr_total['receivedGST'] = toDecimal($closingBalance[$kk]['chargedGST'], 2, '0', $numberFormat);
                            $arr_total['totalAmount'] = toDecimal($closingBalance[$kk]['totalCharge'], 2, '0', $numberFormat);
                        } else {
                            $arr_total['netTotal'] = toDecimal($closingBalance[$kk]['netTotal'], 2, '0', $numberFormat);
                            $arr_total['receivedGST'] = toDecimal($closingBalance[$kk]['receivedGST'], 2, '0', $numberFormat);
                            $arr_total['totalAmount'] = toDecimal($closingBalance[$kk]['totalAmount'], 2, '0', $numberFormat);
                        }
                        $arr_total['accountCode'] = null;
                        $arr_total['description'] = null;
                        $arr_total['transactionType'] = null;
                        $netAmountTenant = $chargedGSTTenant = $totalChargeTenant = $netAmountClosing = $chargedGSTClosing = $totalChargeClosing = $balanceTenant = $balanceClosing = 0;
                        $netTotalTenant = $receivedGSTTenant = $totalAmountTenant = $netTotalClosing = $receivedGSTClosing = $totalAmountClosing = 0;
                    }

                    if ($lastPropertyTenant != '' && $lastPropertyTenant == $v['propertyID'] . $v['tenantID'] && $lastTransactionType != $v['transactionType2']) {
                        // display account code total if sorted by account code
                        if ($format == FILETYPE_SCREEN) {
                            $key++;
                            $_arr = [];
                            $_arr['width']['transactionDate'] = 300;
                            $_arr['bold'] = true;
                            $_arr['bgcolor'] = [220 / 255, 242 / 255, 255 / 255];

                            $_arr['transactionDate'] = "Account Total for $lastAccountCode " . dbGetAccountName($lastAccountCode);
                            $_arr['tenantID'] = $lastTenant;
                            $_arr['netAmount'] = toDecimal($netAmountTenant, 2, '0', $numberFormat);
                            $_arr['chargedGST'] = toDecimal($chargedGSTTenant, 2, '0', $numberFormat);
                            $_arr['totalCharge'] = toDecimal($totalChargeTenant, 2, '0', $numberFormat);
                            if ($lastTransactionType == 'CSH') {
                                $_arr['netTotal'] = toDecimal($netTotalTenant, 2, '0', $numberFormat);
                                $_arr['receivedGST'] = toDecimal($receivedGSTTenant, 2, '0', $numberFormat);
                                $_arr['totalAmount'] = toDecimal($totalAmountTenant, 2, '0', $numberFormat);
                            } else {
                                $_arr['netTotal'] = toDecimal($netAmountTenant, 2, '0', $numberFormat);
                                $_arr['receivedGST'] = toDecimal($chargedGSTTenant, 2, '0', $numberFormat);
                                $_arr['totalAmount'] = toDecimal($totalChargeTenant, 2, '0', $numberFormat);
                            }
                            $_arr['balance'] = toDecimal($balanceTenant, 2, '0', $numberFormat);
                            $_arr['accountCode'] = null;
                            $_arr['description'] = null;
                            $_arr['transactionType'] = null;
                            $tenants_[$lastTenant][$key] = $_arr;
                            if ($format != FILETYPE_SCREEN) {
                                $key = $key + 1;
                                $tenants_[$lastTenant][$key] = null;
                            } // add null value for pdf and excel

                            $netAmountTenant = $chargedGSTTenant = $totalChargeTenant = $netAmountClosing = $chargedGSTClosing = $totalChargeClosing = $balanceTenant = $balanceClosing = 0;

                            // used by excel
                            $tenants_[$lastTenant][$key]['headerStyle'] =
                                [
                                    'fill' => [
                                        'type' => Fill::FILL_SOLID,
                                        'color' => ['rgb' => 'e1e1e1'],
                                    ],
                                    'font' => ['color' => ['rgb' => '000000']],
                                    'alignment' => ['vertical' => Alignment::VERTICAL_CENTER, 'horizontal' => Alignment::HORIZONTAL_LEFT],
                                ];
                        }
                        $key = ($lastAccountCodePropertyTenant == $v['accountCode'] . $v['propertyID'] . $v['tenantID'] && $lastTransactionType != $v['transactionType2']) ? $key + 1 : $key;
                        $arr = [];
                        $arr['width']['transactionDate'] = 300;
                        $arr['bold'] = true;
                        $arr['bgcolor'] = [0.9, 0.9, 0.9];
                        $arr['transactionDate'] = ($lastTransactionType == 'INV') ? 'Total Charges' : 'Total Receipts';
                        $arr['tenantID'] = $lastTenant;
                        if ($lastTransactionType == 'INV') {
                            $arr['netTotal'] = toDecimal($closingBalance[$kk]['netAmount'], 2, '0', $numberFormat);
                            $arr['receivedGST'] = toDecimal($closingBalance[$kk]['chargedGST'], 2, '0', $numberFormat);
                            $arr['totalAmount'] = toDecimal($closingBalance[$kk]['totalCharge'], 2, '0', $numberFormat);
                        } else {
                            $arr['netTotal'] = toDecimal($closingBalance[$kk]['netTotal'], 2, '0', $numberFormat);
                            $arr['receivedGST'] = toDecimal($closingBalance[$kk]['receivedGST'], 2, '0', $numberFormat);
                            $arr['totalAmount'] = toDecimal($closingBalance[$kk]['totalAmount'], 2, '0', $numberFormat);
                        }
                        $arr['accountCode'] = null;
                        $arr['description'] = null;
                        $arr['transactionType'] = null;
                        if ($view->items['transactionOption'] == 'allTransactions') {
                            $tenants_[$lastTenant][$key] = $arr;
                        }
                        // used by excel
                        $tenants_[$lastTenant][$key]['headerStyle'] =
                            [
                                'fill' => [
                                    'type' => Fill::FILL_SOLID,
                                    'color' => ['rgb' => 'e1e1e1'],
                                ],
                                'font' => ['color' => ['rgb' => '000000']],
                                'alignment' => ['vertical' => Alignment::VERTICAL_CENTER, 'horizontal' => Alignment::HORIZONTAL_LEFT],
                            ];
                        if ($format != FILETYPE_SCREEN) {
                            $key = $key + 1;
                            $tenants_[$lastTenant][$key] = null;
                        } // add null value for pdf and excel
                    }
                    $lastTenant = $kk;
                    $lastKey = $key_;
                    $lastAccountCode = $v['accountCode'];
                    $lastPropertyTenant = $v['propertyID'] . $v['tenantID'];
                    $lastAccountCodePropertyTenant = $v['accountCode'] . $v['propertyID'] . $v['tenantID'];
                    $lastTransactionType = $v['transactionType2'];
                    $key++;
                    $tenants_[$kk][$key] = $v;
                }
                array_push($tenants_[$kk], $arr_);
                if ($view->items['transactionOption'] == 'allTransactions') {
                    array_push($tenants_[$kk], $arr_total);
                }
                if ($format != FILETYPE_SCREEN) {
                    array_push($tenants_[$kk], null);
                }// add null value for pdf and excel
            }
            // replace tenant variable with a new array values
            $tenants = $tenants_;
        }

        switch ($format) {
            case FILETYPE_PDF:
                $report = ($context[DOC_MASTER]) ? new PDFDataReport($context[DOC_MASTER], $logoPath, A4_LANDSCAPE) : new PDFDataReport($filePath, $logoPath, A4_LANDSCAPE);
                $report->multiLine = true;
                $report->printRowLines = true;
                $report->printColumnLines = false;
                $report->printBorders = false;
                $report->cache = false;
                $header = new ReportHeader('Tenant Activity Report');
                if ($view->items['allDates'] != 'Yes' && $toDate && $fromDate) {
                    $header->subText = "$fromDate to $toDate";
                } else {
                    $header->subText = 'all dates';
                }
                $header->xPos = $report->hMargin;
                $header->yPos = $report->pageHeight - $report->vMargin;
                $report->attachObject('header', $header);
                $footer = new TraccFooter(null, $_report['reportName'], $report->pageSize);
                $report->attachObject('footer', $footer);
                break;
            case FILETYPE_XLS:
                $report = new XLSDataReport($filePath, 'Tenant Activity Report');
                break;
        }

        if ($format != FILETYPE_SCREEN) {
            $report->addColumn('transactionDate', 'Date', 40, 'left');
            $report->addColumn('accountCode', 'Account', 40, 'left');
            $report->addColumn('description', 'Description', 95, 'left');
            $report->addColumn('transactionType', 'Type', 35, 'left');
            $report->addColumn('invoiceNumber', 'Invoice #', 45, 'left');
            $report->addColumn('receiptNumber', 'Receipt #', 45, 'left');
            $report->addColumn('fromDate', 'From', 40, 'left');
            $report->addColumn('toDate', 'To', 40, 'left');

            switch ($transactionOption) {
                case 'chargesOnly':
                    $report->addColumn('netAmount', 'Net', 50, 'right');
                    $report->addColumn('chargedGST', $_SESSION['country_default']['tax_label'], 45, 'right');
                    $report->addColumn('totalCharge', 'Total', 50, 'right');
                    break;
                case 'receiptsOnly':
                    $report->addColumn('netTotal', 'Net', 50, 'right');
                    $report->addColumn('receivedGST', $_SESSION['country_default']['tax_label'], 45, 'right');
                    $report->addColumn('totalAmount', 'Total', 50, 'right');
                    if ($view->items['showSubTotal'] == 'Yes') {
                        $report->addColumn('totalReceipt', 'Receipt Total', 50, 'right');
                    }
                    break;
                case 'allTransactions':
                default:
                    if ($view->items['sortOption'] == 'sortAccountID') {
                        $report->addColumn('netTotal', 'Net', 90, 'right');
                        $report->addColumn('receivedGST', $_SESSION['country_default']['tax_label'], 90, 'right');
                        $report->addColumn('totalAmount', 'Total Charged/Received', 98, 'right');
                        if ($view->items['showSubTotal'] == 'Yes') {
                            $report->addColumn('totalReceipt', 'Receipt Total', 49, 'right');
                        }
                    } else {
                        $report->addColumn('netAmount', 'Net', 40, 'right');
                        $report->addColumn('chargedGST', $_SESSION['country_default']['tax_label'], 35, 'right');
                        $report->addColumn('totalCharge', 'Total Charged', 48, 'right');
                        $report->addColumn('netTotal', 'Net', 40, 'right');
                        $report->addColumn('receivedGST', $_SESSION['country_default']['tax_label'], 35, 'right');
                        $report->addColumn('totalAmount', 'Total Received', 49, 'right');
                        if ($view->items['showSubTotal'] == 'Yes') {
                            $report->addColumn('totalReceipt', 'Receipt Total', 49, 'right');
                        }
                        $report->addColumn('balance', 'Balance', 60, 'right');
                    }
                    break;
            }

            $report->addSubHeaderItem('title', 0, 200, 'left');

            if ($view->items['pageBreak'] == 'No') {
                if ($propertyName && $propertyCode && $format == FILETYPE_PDF) {
                    $header->subTitle = "$propertyName ($propertyCode)";
                }
                $report->preparePage();
                $report->renderHeader();
                if ($format == FILETYPE_XLS) {
                    if ($propertyName && $propertyCode) {
                        $excelTitle['description'] = "$propertyName ($propertyCode)";
                    }
                    $report->renderLine($excelTitle);
                }
            }
        }

        if ($tenants && is_array($tenants)) {
            if ($format == FILETYPE_SCREEN) {

                $view->items['tenants'] = $tenants;
                $view->items['propertyName'] = $propertyName;
                $view->items['openingBalance'] = $openingBalance;
                $view->items['closingBalance'] = $closingBalance;
            } else {
                if ($view->items['sortOption'] != 'sortAccountID') {
                    $i = 0;
                }
                foreach ($tenants as $k => $tenant) {
                    if ($view->items['sortOption'] == 'sortAccountID') {
                        $i = 0;
                        $resetIndex = array_values($tenant);
                    }

                    $leaseName = dbGetLeaseName($propertyCode, $k);
                    if ($view->items['pageBreak'] == 'Yes') {
                        if ($view->items['sortOption'] != 'sortAccountID') {
                            $propertyCode = $tenant[$i]['propertyID'];
                        } else {
                            $propertyCode = $resetIndex[$i]['propertyID'];
                        }

                        $propertyLookUp = dbGetPropertyLookup($propertyCode);
                        $propertyName = $propertyLookUp['propertyName'];
                        if ($format == FILETYPE_PDF) {
                            $header->subTitle = "$propertyName ($propertyCode) - $leaseName ($k)";
                        }
                        $report->preparePage();

                        if ($format == FILETYPE_XLS) {
                            $excelTitle['description'] = "$propertyName ($propertyCode) - $leaseName ($k)";
                            $report->renderLine($excelTitle);
                            $report->renderHeader();
                        } else {
                            $report->renderHeader();
                        }
                    } else {
                        $report->setSubHeaderValue('title', "$leaseName ($k)");
                        if ($format == FILETYPE_XLS) {
                            $excelTitle['description'] = "$leaseName ($k)";
                            $report->renderLine($excelTitle);
                        } else {
                            $report->renderSubHeader();
                        }
                    }


                    if ($view->items['sortOption'] == 'sortAccountID') {
                        if ($tenant) {
                            $i = $i + count($tenant);
                            foreach ($tenant as $k_ => $val) {
                                if ($val['transactionType2'] == 'INV') {
                                    $tenant[$k_]['netTotal'] = $val['netAmount'];
                                    $tenant[$k_]['receivedGST'] = $val['chargedGST'];
                                    $tenant[$k_]['totalAmount'] = $val['totalCharge'];
                                }
                            }
                            $report->renderData_custom($tenant);
                        }
                        // Opening Balance
                        if ($transactionOption != 'chargesOnly' && $transactionOption != 'receiptsOnly') {
                            $openBalance['totalAmount'] = toDecimal($openingBalance[$k]['balance'], 2, '0', $numberFormat);
                            if ($format == FILETYPE_PDF) {
                                $report->renderSubTotal($openBalance);
                            } else {
                                $report->renderLine($openBalance);
                            }
                        }

                        // Total Receipts
                        $totalReceipts['netTotal'] = toDecimal($closingBalance[$k]['netTotal'], 2, '0', $numberFormat);
                        $totalReceipts['receivedGST'] = toDecimal($closingBalance[$k]['receivedGST'], 2, '0', $numberFormat);
                        $totalReceipts['totalAmount'] = toDecimal($closingBalance[$k]['totalAmount'], 2, '0', $numberFormat);

                        // Closing Balance
                        $closedBalance['totalAmount'] = toDecimal($closingBalance[$k]['balance'], 2, '0', $numberFormat);
                        if ($transactionOption == 'chargesOnly') {
                            $totalCharges['netAmount'] = toDecimal($closingBalance[$k]['netAmount'], 2, '0', $numberFormat);
                            $totalCharges['chargedGST'] = toDecimal($closingBalance[$k]['chargedGST'], 2, '0', $numberFormat);
                            $totalCharges['totalCharge'] = toDecimal($closingBalance[$k]['totalCharge'], 2, '0', $numberFormat);
                            if ($format == FILETYPE_PDF) {
                                $report->renderSubTotal($totalCharges);
                            } else {
                                $report->renderLine($totalCharges);
                            }
                        } elseif ($transactionOption == 'receiptsOnly') {
                            if ($format == FILETYPE_PDF) {
                                $report->renderSubTotal($totalReceipts);
                            } else {
                                $report->renderLine($totalReceipts);
                            }
                        } else {
                            $totalCharges['netTotal'] = toDecimal($closingBalance[$k]['netAmount'], 2, '0', $numberFormat);
                            $totalCharges['receivedGST'] = toDecimal($closingBalance[$k]['chargedGST'], 2, '0', $numberFormat);
                            $totalCharges['totalAmount'] = toDecimal($closingBalance[$k]['totalCharge'], 2, '0', $numberFormat);
                            if ($format == FILETYPE_PDF) {
                                $report->renderSubTotal($totalCharges);
                            } else {
                                $report->renderLine($totalCharges);
                            }

                            if ($format == FILETYPE_PDF) {
                                $report->renderSubTotal($totalReceipts);
                            } else {
                                $report->renderLine($totalReceipts);
                            }

                            if ($format == FILETYPE_PDF) {
                                $report->renderSubTotal($closedBalance);
                            } else {
                                $report->renderLine($closedBalance);
                            }
                        }

                    } else {
                        // Opening Balance
                        if ($transactionOption != 'chargesOnly' && $transactionOption != 'receiptsOnly') {
                            $openBalance['balance'] = toDecimal($openingBalance[$k]['balance'], 2, '0', $numberFormat);
                            if ($format == FILETYPE_PDF) {
                                $report->renderSubTotal($openBalance);
                            } else {
                                $report->renderLine($openBalance);
                            }
                        }
                        if ($tenant) {
                            $i = $i + count($tenant);
                            $report->renderData_custom($tenant);
                        }
                        // Closing Balance
                        $closedBalance['netAmount'] = toDecimal($closingBalance[$k]['netAmount'], 2, '0', $numberFormat);
                        $closedBalance['chargedGST'] = toDecimal($closingBalance[$k]['chargedGST'], 2, '0', $numberFormat);
                        $closedBalance['totalCharge'] = toDecimal($closingBalance[$k]['totalCharge'], 2, '0', $numberFormat);
                        $closedBalance['netTotal'] = toDecimal($closingBalance[$k]['netTotal'], 2, '0', $numberFormat);
                        $closedBalance['receivedGST'] = toDecimal($closingBalance[$k]['receivedGST'], 2, '0', $numberFormat);
                        $closedBalance['totalAmount'] = toDecimal($closingBalance[$k]['totalAmount'], 2, '0', $numberFormat);
                        $closedBalance['balance'] = toDecimal($closingBalance[$k]['balance'], 2, '0', $numberFormat);
                        if ($format == FILETYPE_PDF) {
                            $report->renderSubTotal($closedBalance);
                        } else {
                            $report->renderLine($closedBalance);
                        }
                    }

                    if ($view->items['pageBreak'] == 'Yes') {
                        $report->clean();
                        $report->endPage();
                    }
                }
            }
        }

        if ($format != FILETYPE_SCREEN) {
            if ($view->items['pageBreak'] == 'No') {
                $report->clean();
                $report->endPage();
            }

            $report->close();
        }
    } elseif (empty($count)) {
        $view->items['statusMessage'] = 'No Tenant Activity to print.';
    }

    if ($count > THRESHOLD_TENANTACTIVITY) {
        $view->items['statusMessage'] = 'Due to its complexity, this report has been scheduled and will be emailed to you on completion.';
    }

    if (! $context[DOC_MASTER]) {
        if ($context[IS_TASK]) {
            $email = new EmailTemplate('views/emails/basicEmail.html', SYSTEMURL);
            $attachment = [];
            $attachment[0]['file'] = REPORTPATH . '/' . $pdfDownloadLink;
            $attachment[0]['content_type'] = 'application/pdf';
            sendMail($_SESSION['email'], $_SESSION['first_name'] . ' ' . $_SESSION['last_name'], $email->toString(), 'Report', $attachment);
            logData('Emailed report to ' . $_SESSION['email']);
            $context[TASK_COMPLETE] = true;
        } else {
            $view->items['downloadPath'] = $downloadPath;
            if ($_SESSION['user_type'] == USER_OWNER) {
                $_SESSION['downloadFile'] = $downloadPath;
            }
        }
        $tenantUnitList = [];
        $propertyCode = $context['selectBy'] === 'crn' ? explode('~', $context['propertyLeaseID'][0])[0] : $propertyCode;
        $units = dbGetCurrentUnit($propertyCode);
        foreach ($tenantID as $id) {
            $unitList = getArrayFilteredReturnList($propertyCode, $view->items['tenantList'], 'propertyID');
            $unitCode = findArrayValueByKey($id, $unitList, 'leaseID', 'unitID');
            $unitLocDescription = getArrayFilteredReturnList($propertyCode, $view->items['tenantList'], 'propertyID');
            $unitLocDescription = findArrayValueByKey($id, $unitLocDescription, 'leaseID', 'description');
            $leaseName = findArrayValueByKey($id, $view->items['tenantList'], 'leaseID', 'leaseName');
            $unitDescription = $unitCode != '' ? findArrayValueByKey($id, $units, 'leaseID', 'unitDescription') : $unitLocDescription;
            $unitCode = ($unitCode != '' && $unitDescription != '') ? $unitCode . ': ' : $unitCode;
            $tenantUnitList[$id]['unit'] = ($unitCode === '' && $unitDescription === '') ? '' : "({$unitCode}{$unitDescription})";
            $tenantUnitList[$id]['leaseName'] = $leaseName;
        }
        $view->items['tenantUnitList'] = $tenantUnitList;
        $view->render();
    }
}

/**
 * Filtered array list based on key value.
 *
 * @param  mixed  $value  value of the key to check in list.
 * @param  array  $list  list of data to filter.
 * @param  string  $key  key to list to check the value of array list.
 * @param  string  $returnKey  key value to return.
 */
function findArrayValueByKey($value, $list, $key, $returnKey): string
{
    $filteredArray = getArrayFilteredReturnList($value, $list, $key);

    return $filteredArray[0][$returnKey] ?? '';
}

/**
 * Filtered array list based on key value. Return array list.
 *
 * @param  mixed  $value  value of the key to check in list.
 * @param  array  $list  array list to filter.
 * @param  string  $key  key to list to check the value of array list.
 */
function getArrayFilteredReturnList($value, $list, $key): array
{
    return array_values(array_filter($list, function ($subArray) use ($key, $value) {
        return isset($subArray[$key]) && $subArray[$key] == $value;
    }));
}

/**
 * Get the current unit list for the property by propertyID.
 *
 * @param  string  $propertyID  use to filter unit list by property.
 */
function dbGetCurrentUnit($propertyID): array
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);

    $sql = 'SELECT u.pmua_prop,
            u.pmua_unit AS unitID,
            u.pmua_lease AS leaseID,
            p.pmpu_desc AS unitDescription
		FROM pmua_unit_area u, pmpu_p_unit p
		WHERE u.pmua_prop=?
			AND p.pmpu_unit = u.pmua_unit
			AND p.pmpu_prop = u.pmua_prop
		ORDER BY pmua_from_dt DESC';

    return $dbh->executeSet($sql, false, true, [$propertyID]);
}

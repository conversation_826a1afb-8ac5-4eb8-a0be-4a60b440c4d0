<?php


include_once SYSTEMPATH . '/commands/managementReports/accruals/fs_header.php';


$pdf->begin_page_ext(842, 595, '');
$page++;
$page_header = 'Property Financial Summary';
// include "fs_header.php";
fsHeader($pdf, $page_header, $reportingPeriodFrom, $reportingPeriodTo);
if ($logo) {
    generateLogo('landscape');
}

$line = 10;
// print "PROPERTY FINANCIAL SUMMARY<BR><br><table bgcolor=silver cellspacing=1>";


$pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
$pdf->showBoxed('Accrual Summary', 22, 440 - $line, 275, 30, 'left', '');
$pdf->setFontExt($_fonts['Helvetica'], 8);

$accountListing = [
    INCOME => $incomeAccountGroups,
    EXPENDITURE => $expenditureAccountGroups,
];

$accountNames = [INCOME => 'Income', EXPENDITURE => 'Expenditure'];

foreach ($accountListing as $accountClass => $accountGroups) {

    foreach ($accountGroups as $accountGroupCode => $accountGroupName) {

        if ($subTotal[A][$accountGroupCode]['variancePercentage'] <= 1000 && $subTotal[A][$accountGroupCode]['variancePercentage'] >= -1000) {
            $subTotal_A_accountGroupCode_variancePercentage = toDecimal($subTotal[A][$accountGroupCode]['variancePercentage']);
        } elseif ($subTotal[A][$accountGroupCode]['variancePercentage'] > 1000) {
            $subTotal_A_accountGroupCode_variancePercentage = '>1000';
        } elseif ($subTotal[A][$accountGroupCode]['variancePercentage'] < -1000) {
            $subTotal_A_accountGroupCode_variancePercentage = '<(1000)';
        }

        if ($subTotal[A][$accountGroupCode]['variancePercentageYTD'] <= 1000 && $subTotal[A][$accountGroupCode]['variancePercentageYTD'] >= -1000) {
            $subTotal_A_accountGroupCode_variancePercentageYTD = toDecimal($subTotal[A][$accountGroupCode]['variancePercentageYTD']);
        } elseif ($subTotal[A][$accountGroupCode]['variancePercentageYTD'] > 1000) {
            $subTotal_A_accountGroupCode_variancePercentageYTD = '>1000';
        } elseif ($subTotal[A][$accountGroupCode]['variancePercentageYTD'] < -1000) {
            $subTotal_A_accountGroupCode_variancePercentageYTD = '<(1000)';
        }


        $pdf->showBoxed($accountGroupName, 22, 425 - $line, 275, 30, 'left', ''); // ////////////////	OWNER INCOME - ACCRUALS BASIS/////////////////////////////////////////
        $pdf->showBoxed(toDecimal($subTotal[A][$accountGroupCode]['actual']), 190, 425 - $line, 75, 30, 'right', ''); // /owner accrual income current period
        $pdf->showBoxed(toDecimal($subTotal[A][$accountGroupCode]['budget']), 250, 425 - $line, 75, 30, 'right', ''); // ///owner accrual budget income current period
        $pdf->showBoxed(toDecimal($subTotal[A][$accountGroupCode]['variance']), 310, 425 - $line, 75, 30, 'right', '');
        $pdf->showBoxed($subTotal_A_accountGroupCode_variancePercentage, 355, 425 - $line, 75, 30, 'right', '');
        $pdf->showBoxed(toDecimal($subTotal[A][$accountGroupCode]['actualYTD']), 440, 425 - $line, 75, 30, 'right', ''); // ///////owner accrual income YTD
        $pdf->showBoxed(toDecimal($subTotal[A][$accountGroupCode]['budgetYTD']), 500, 425 - $line, 75, 30, 'right', ''); // ///owner accrual budget income YTD
        $pdf->showBoxed(toDecimal($subTotal[A][$accountGroupCode]['varianceYTD']), 560, 425 - $line, 75, 30, 'right', '');
        $pdf->showBoxed($subTotal_A_accountGroupCode_variancePercentageYTD, 600, 425 - $line, 75, 30, 'right', '');
        $pdf->showBoxed(toDecimal($subTotal[A][$accountGroupCode]['budgetFullYear']), 675, 425 - $line, 75, 30, 'right', ''); // /////owner accrual budget income entire year
        $pdf->showBoxed(toDecimal($subTotal[A][$accountGroupCode]['forecast']), 745, 425 - $line, 75, 30, 'right', ''); // //////////////owner forecast income
        $line += 15;
    }

    $pdf->setColorExt('fill', 'rgb', 0.88, 0.88, 0.88, 0);
    $pdf->rect(200, 443 - $line, 624, 13);
    $pdf->fill();
    $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
    $pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
    $pdf->showBoxed('Total ' . $accountNames[$accountClass], 22, 425 - $line, 275, 30, 'left', ''); // //////////////////////TOTAL ACCRUAL INCOME////////////////////////////////////////////
    $pdf->setFontExt($_fonts['Helvetica'], 8);

    if ($total[A][$accountClass]['variancePercentage'] <= 1000 && $total[A][$accountClass]['variancePercentage'] >= -1000) {
        $total_A_accountClass_variancePercentage = toDecimal($total[A][$accountClass]['variancePercentage']);
    } elseif ($total[A][$accountClass]['variancePercentage'] > 1000) {
        $total_A_accountClass_variancePercentage = '>1000';
    } elseif ($total[A][$accountClass]['variancePercentage'] < -1000) {
        $total_A_accountClass_variancePercentage = '<(1000)';
    }

    if ($total[A][$accountClass]['variancePercentageYTD'] <= 1000 && $total[A][$accountClass]['variancePercentageYTD'] >= -1000) {
        $total_A_accountClass_variancePercentageYTD = toDecimal($total[A][$accountClass]['variancePercentageYTD']);
    } elseif ($total[A][$accountClass]['variancePercentageYTD'] > 1000) {
        $total_A_accountClass_variancePercentageYTD = '>1000';
    } elseif ($total[A][$accountClass]['variancePercentageYTD'] < -1000) {
        $total_A_accountClass_variancePercentageYTD = '<(1000)';
    }

    $pdf->showBoxed(toDecimal($total[A][$accountClass]['actual']), 190, 425 - $line, 75, 30, 'right', '');
    $pdf->showBoxed(toDecimal($total[A][$accountClass]['budget']), 250, 425 - $line, 75, 30, 'right', '');
    $pdf->showBoxed(toDecimal($total[A][$accountClass]['variance']), 310, 425 - $line, 75, 30, 'right', '');
    $pdf->showBoxed($total_A_accountClass_variancePercentage, 355, 425 - $line, 75, 30, 'right', '');
    $pdf->showBoxed(toDecimal($total[A][$accountClass]['actualYTD']), 440, 425 - $line, 75, 30, 'right', '');
    $pdf->showBoxed(toDecimal($total[A][$accountClass]['budgetYTD']), 500, 425 - $line, 75, 30, 'right', '');
    $pdf->showBoxed(toDecimal($total[A][$accountClass]['varianceYTD']), 560, 425 - $line, 75, 30, 'right', '');
    $pdf->showBoxed($total_A_accountClass_variancePercentageYTD, 600, 425 - $line, 75, 30, 'right', '');
    $pdf->showBoxed(toDecimal($total[A][$accountClass]['budgetFullYear']), 675, 425 - $line, 75, 30, 'right', '');
    $pdf->showBoxed(toDecimal($total[A][$accountClass]['budgetForecast']), 745, 425 - $line, 75, 30, 'right', '');

    $pdf->moveto(200, 443 - $line);
    $pdf->lineto(824, 443 - $line);
    $pdf->stroke();

    $pdf->moveto(200, 456 - $line);
    $pdf->lineto(824, 456 - $line);
    $pdf->stroke();
    $line += 30;
}



$pdf->setColorExt('fill', 'rgb', 0.88, 0.88, 0.88, 0);
$pdf->rect(200, 443 - $line, 624, 13);
$pdf->fill();
$pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
$pdf->setFontExt($_fonts['Helvetica-Bold'], 8);

if ($net[A]['variancePercentage'] <= 1000 && $net[A]['variancePercentage'] >= -1000) {
    $net_A_variancePercentage = toDecimal($net[A]['variancePercentage']);
} elseif ($net[A]['variancePercentage'] > 1000) {
    $net_A_variancePercentage = '>1000';
} elseif ($net[A]['variancePercentage'] < -1000) {
    $net_A_variancePercentage = '<(1000)';
}

if ($net[A]['variancePercentageYTD'] <= 1000 && $net[A]['variancePercentageYTD'] >= -1000) {
    $net_A_variancePercentageYTD = toDecimal($net[A]['variancePercentageYTD']);
} elseif ($net[A]['variancePercentageYTD'] > 1000) {
    $net_A_variancePercentageYTD = '>1000';
} elseif ($net[A]['variancePercentageYTD'] < -1000) {
    $net_A_variancePercentageYTD = '<(1000)';
}

$pdf->showBoxed('Net Income (Excluding ' . $_SESSION['country_default']['tax_label'] . ')', 22, 425 - $line, 275, 30, 'left', ''); // /////////////////NET INCOME ACCRUALS BASIS//////////////////////////

$pdf->showBoxed(toDecimal($net[A]['actual']), 190, 425 - $line, 75, 30, 'right', '');
$pdf->showBoxed(toDecimal($net[A]['budget']), 250, 425 - $line, 75, 30, 'right', '');
$pdf->showBoxed(toDecimal($net[A]['variance']), 310, 425 - $line, 75, 30, 'right', '');
$pdf->showBoxed($net_A_variancePercentage, 355, 425 - $line, 75, 30, 'right', '');
$pdf->showBoxed(toDecimal($net[A]['actualYTD']), 440, 425 - $line, 75, 30, 'right', '');
$pdf->showBoxed(toDecimal($net[A]['budgetYTD']), 500, 425 - $line, 75, 30, 'right', '');
$pdf->showBoxed(toDecimal($net[A]['varianceYTD']), 560, 425 - $line, 75, 30, 'right', '');
$pdf->showBoxed($net_A_variancePercentageYTD, 600, 425 - $line, 75, 30, 'right', '');
$pdf->showBoxed(toDecimal($net[A]['budgetFullYear']), 675, 425 - $line, 75, 30, 'right', '');
$pdf->showBoxed(toDecimal($net[A]['budgetForecast']), 745, 425 - $line, 75, 30, 'right', '');

$pdf->setFontExt($_fonts['Helvetica'], 8);
$pdf->moveto(200, 443 - $line);
$pdf->lineto(824, 443 - $line);
$pdf->stroke();

$pdf->moveto(200, 456 - $line);
$pdf->lineto(824, 456 - $line);
$pdf->stroke();

$line += 45;



$pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
$pdf->showBoxed('Cash Summary', 22, 440 - $line, 275, 30, 'left', ''); // ///////////////////////////////////////CASH SUMMARY ///////////////////////////////


foreach ($accountListing as $accountClass => $accountGroups) {

    foreach ($accountGroups as $accountGroupCode => $accountGroupName) {

        if ($subTotal[C][$accountGroupCode]['variancePercentage'] <= 1000 && $subTotal[C][$accountGroupCode]['variancePercentage'] >= -1000) {
            $subTotal_C_accountGroupCode_variancePercentage = toDecimal($subTotal[C][$accountGroupCode]['variancePercentage']);
        } elseif ($subTotal[C][$accountGroupCode]['variancePercentage'] > 1000) {
            $subTotal_C_accountGroupCode_variancePercentage = '>1000';
        } elseif ($subTotal[C][$accountGroupCode]['variancePercentage'] < -1000) {
            $subTotal_C_accountGroupCode_variancePercentage = '<(1000)';
        }

        if ($subTotal[C][$accountGroupCode]['variancePercentageYTD'] <= 1000 && $subTotal[C][$accountGroupCode]['variancePercentageYTD'] >= -1000) {
            $subTotal_C_accountGroupCode_variancePercentageYTD = toDecimal($subTotal[C][$accountGroupCode]['variancePercentageYTD']);
        } elseif ($subTotal[C][$accountGroupCode]['variancePercentageYTD'] > 1000) {
            $subTotal_C_accountGroupCode_variancePercentageYTD = '>1000';
        } elseif ($subTotal[C][$accountGroupCode]['variancePercentageYTD'] < -1000) {
            $subTotal_C_accountGroupCode_variancePercentageYTD = '<(1000)';
        }


        $pdf->setFontExt($_fonts['Helvetica'], 8);
        $pdf->showBoxed($accountGroupName, 22, 425 - $line, 275, 30, 'left', ''); // ///////////////////////////OWNER INCOME CASH BASIS//////////////////////////////////
        $pdf->showBoxed(toDecimal($subTotal[C][$accountGroupCode]['actual']), 190, 425 - $line, 75, 30, 'right', '');
        $pdf->showBoxed(toDecimal($subTotal[C][$accountGroupCode]['budget']), 250, 425 - $line, 75, 30, 'right', '');
        $pdf->showBoxed(toDecimal($subTotal[C][$accountGroupCode]['variance']), 310, 425 - $line, 75, 30, 'right', '');
        $pdf->showBoxed($subTotal_C_accountGroupCode_variancePercentage, 355, 425 - $line, 75, 30, 'right', '');
        $pdf->showBoxed(toDecimal($subTotal[C][$accountGroupCode]['actualYTD']), 440, 425 - $line, 75, 30, 'right', '');
        $pdf->showBoxed(toDecimal($subTotal[C][$accountGroupCode]['budgetYTD']), 500, 425 - $line, 75, 30, 'right', '');
        $pdf->showBoxed(toDecimal($subTotal[C][$accountGroupCode]['varianceYTD']), 560, 425 - $line, 75, 30, 'right', '');
        $pdf->showBoxed($subTotal_C_accountGroupCode_variancePercentageYTD, 600, 425 - $line, 75, 30, 'right', '');
        $pdf->showBoxed(toDecimal($subTotal[C][$accountGroupCode]['budgetFullYear']), 675, 425 - $line, 75, 30, 'right', '');
        $pdf->showBoxed(toDecimal($subTotal[C][$accountGroupCode]['budgetForecast']), 745, 425 - $line, 75, 30, 'right', '');
        $line += 15;
    }


    $pdf->setColorExt('fill', 'rgb', 0.88, 0.88, 0.88, 0);
    $pdf->rect(200, 443 - $line, 624, 13);
    $pdf->fill();
    $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
    $pdf->setFontExt($_fonts['Helvetica-Bold'], 8);

    if ($total[C][$accountClass]['variancePercentage'] <= 1000 && $total[C][$accountClass]['variancePercentage'] >= -1000) {
        $total_C_accountClass_variancePercentage = toDecimal($total[C][$accountClass]['variancePercentage']);
    } elseif ($subTotal[C][$accountGroupCode]['variancePercentage'] > 1000) {
        $total_C_accountClass_variancePercentage = '>1000';
    } elseif ($subTotal[C][$accountGroupCode]['variancePercentage'] < -1000) {
        $total_C_accountClass_variancePercentage = '<(1000)';
    }

    if ($total[C][$accountClass]['variancePercentageYTD'] <= 1000 && $total[C][$accountClass]['variancePercentageYTD'] >= -1000) {
        $total_C_accountClass_variancePercentageYTD = toDecimal($total[C][$accountClass]['variancePercentageYTD']);
    } elseif ($subTotal[C][$accountGroupCode]['variancePercentageYTD'] > 1000) {
        $total_C_accountClass_variancePercentageYTD = '>1000';
    } elseif ($subTotal[C][$accountGroupCode]['variancePercentageYTD'] < -1000) {
        $total_C_accountClass_variancePercentageYTD = '<(1000)';
    }

    $pdf->showBoxed('Total ' . $accountNames[$accountClass], 22, 425 - $line, 275, 30, 'left', ''); // //////////////////////////TOTAL INCOME CASH BASIS/////////////////////////////////
    $pdf->setFontExt($_fonts['Helvetica'], 8);
    $pdf->showBoxed(toDecimal($total[C][$accountClass]['actual']), 190, 425 - $line, 75, 30, 'right', '');
    $pdf->showBoxed(toDecimal($total[C][$accountClass]['budget']), 250, 425 - $line, 75, 30, 'right', '');
    $pdf->showBoxed(toDecimal($total[C][$accountClass]['variance']), 310, 425 - $line, 75, 30, 'right', '');
    $pdf->showBoxed($total_C_accountClass_variancePercentage, 355, 425 - $line, 75, 30, 'right', '');
    $pdf->showBoxed(toDecimal($total[C][$accountClass]['actualYTD']), 440, 425 - $line, 75, 30, 'right', '');
    $pdf->showBoxed(toDecimal($total[C][$accountClass]['budgetYTD']), 500, 425 - $line, 75, 30, 'right', '');
    $pdf->showBoxed(toDecimal($total[C][$accountClass]['varianceYTD']), 560, 425 - $line, 75, 30, 'right', '');
    $pdf->showBoxed($total_C_accountClass_variancePercentageYTD, 600, 425 - $line, 75, 30, 'right', '');
    $pdf->showBoxed(toDecimal($total[C][$accountClass]['budgetFullYear']), 675, 425 - $line, 75, 30, 'right', '');
    $pdf->showBoxed(toDecimal($total[C][$accountClass]['budgetForecast']), 745, 425 - $line, 75, 30, 'right', '');


    $pdf->moveto(200, 443 - $line);
    $pdf->lineto(824, 443 - $line);
    $pdf->stroke();

    $pdf->moveto(200, 456 - $line);
    $pdf->lineto(824, 456 - $line);
    $pdf->stroke();


    $line += 30;
}

$pdf->setColorExt('fill', 'rgb', 0.88, 0.88, 0.88, 0);
$pdf->rect(200, 443 - $line, 624, 13);
$pdf->fill();
$pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
$pdf->setFontExt($_fonts['Helvetica-Bold'], 8);

if ($net[C]['variancePercentage'] <= 1000 && $net[C]['variancePercentage'] >= -1000) {
    $net_C_variancePercentage = toDecimal($net[C]['variancePercentage']);
} elseif ($net[C]['variancePercentage'] > 1000) {
    $net_C_variancePercentage = '>1000';
} elseif ($net[C]['variancePercentage'] < -1000) {
    $net_C_variancePercentage = '<(1000)';
}

if ($net[C]['variancePercentageYTD'] <= 1000 && $net[C]['variancePercentageYTD'] >= -1000) {
    $net_C_variancePercentageYTD = toDecimal($net[C]['variancePercentageYTD']);
} elseif ($net[C]['variancePercentageYTD'] > 1000) {
    $net_C_variancePercentageYTD = '>1000';
} elseif ($net[C]['variancePercentageYTD'] < -1000) {
    $net_C_variancePercentageYTD = '<(1000)';
}


$pdf->showBoxed('Net Cash (Excluding ' . $_SESSION['country_default']['tax_label'] . ')', 22, 425 - $line, 275, 30, 'left', ''); // /////////////////////NET CASH ///////////////////////////////////////

$pdf->showBoxed(toDecimal($net[C]['actual']), 190, 425 - $line, 75, 30, 'right', '');
$pdf->showBoxed(toDecimal($net[C]['budget']), 250, 425 - $line, 75, 30, 'right', '');
$pdf->showBoxed(toDecimal($net[C]['variance']), 310, 425 - $line, 75, 30, 'right', '');
$pdf->showBoxed($net_C_variancePercentage, 355, 425 - $line, 75, 30, 'right', '');
$pdf->showBoxed(toDecimal($net[C]['actualYTD']), 440, 425 - $line, 75, 30, 'right', '');
$pdf->showBoxed(toDecimal($net[C]['budgetYTD']), 500, 425 - $line, 75, 30, 'right', '');
$pdf->showBoxed(toDecimal($net[C]['varianceYTD']), 560, 425 - $line, 75, 30, 'right', '');
$pdf->showBoxed($net_C_variancePercentageYTD, 600, 425 - $line, 75, 30, 'right', '');
$pdf->showBoxed(toDecimal($net[C]['budgetFullYear']), 675, 425 - $line, 75, 30, 'right', '');
$pdf->showBoxed(toDecimal($net[C]['budgetForecast']), 745, 425 - $line, 75, 30, 'right', '');

$pdf->setFontExt($_fonts['Helvetica'], 8);

$pdf->moveto(200, 456 - $line);
$pdf->lineto(824, 456 - $line);
$pdf->stroke();

$line += 8;

$pdf->setlinewidth(0.5);
$pdf->moveto(18, 515);
$pdf->lineto(18, 450 - $line);
$pdf->stroke();

$pdf->moveto(200, 515);
$pdf->lineto(200, 450 - $line);
$pdf->stroke();

$pdf->moveto(445, 515);
$pdf->lineto(445, 450 - $line);
$pdf->stroke();


$pdf->moveto(680, 515);
$pdf->lineto(680, 450 - $line);
$pdf->stroke();

$pdf->moveto(824, 515);
$pdf->lineto(824, 450 - $line);
$pdf->stroke();

$pdf->moveto(18, 450 - $line);
$pdf->lineto(824, 450 - $line);
$pdf->stroke();



$line += 15;

$pdf->setFontExt($_fonts['Helvetica'], 8);
// $pdf->showBoxed ("Printed on $date", 22, 5, 275, 30, "left", "");
$pdf->showBoxed('Page ' . $page, 745, 4, 75, 20, 'right', '');

$traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'accrualPropertyFinancialSummary2', A4_LANDSCAPE);
$traccFooter->prerender($pdf);

// $command = "TRUNCATE TABLE  TEMP2004";
// mssql_query($command);

// $command = "TRUNCATE TABLE  TEMP2004_EXP";
// mssql_query($command);

$pdf->end_page_ext('');

// echo print_array($subTotal, true);

<?php


use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

include_once SYSTEMPATH . '/commands/managementReports/accruals/tb_header.php';


// $command = "TRUNCATE TABLE  TEMP2004";
// mssql_query($command);


$accountOrder = $ownerAccountCodes ? ' pmoc_owner_acc, ACCOUNT ' : ' ACCOUNT ';

$line = 10;

if (! function_exists('newPageTrialBalance')) {
    function newPageTrialBalance($endPrevious = true)
    {
        global $line;
        global $pdf;
        global $date;
        global $page;
        global $client;
        global $propertyName;
        global $description;
        global $reportingPeriodFrom;
        global $reportingPeriodTo;
        global $periodFrom;
        global $periodTo;
        global $logo;
        global $_fonts;

        if ($endPrevious) {
            $pdf->setlinewidth(0.5);
            $pdf->moveto(18, 515);
            $pdf->lineto(18, 450 - $line);
            $pdf->stroke();


            $pdf->moveto(250, 515);
            $pdf->lineto(250, 450 - $line);
            $pdf->stroke();

            $pdf->moveto(445, 515);
            $pdf->lineto(445, 450 - $line);
            $pdf->stroke();


            $pdf->moveto(824, 515);
            $pdf->lineto(824, 450 - $line);
            $pdf->stroke();

            $pdf->moveto(18, 450 - $line);
            $pdf->lineto(824, 450 - $line);
            $pdf->stroke();


            $pdf->setFontExt($_fonts['Helvetica'], 8);
            $pdf->showBoxed("Page {$page}", 745, 5, 75, 30, 'right', '');


            $pdf->end_page_ext('');
        }

        $pdf->begin_page_ext(842, 595, '');
        $page++;


        $pdf->setFontExt($_fonts['Helvetica'], 8);
        $traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'trialBalance', A4_LANDSCAPE);
        $traccFooter->prerender($pdf);

        $page_header = 'Accruals Trial Balance' . ($endPrevious ? ' (...)' : '');
        $page_header2 = '';
        tbHeader($pdf, $page_header, $page_header2, $reportingPeriodFrom, $reportingPeriodTo);
        if ($logo) {
            generateLogo('landscape');
        }
    }
}

if (! function_exists('updateLine')) {
    function updateLine($amount)
    {
        global $line;

        $line += $amount;

        if ($line > 360) {
            newPageTrialBalance();
            $line = 10;
        }
    }
}

// /////////////////////////START EXCEL///////////////////////////////
if (! function_exists('initializeXLS')) {
    function initializeXLS($title, $fileName, $path, $downloadLink)
    {
        global $xls;
        global $fullPath;

        $returnArray = [];
        check_dirpath($path);

        // prepare the file
        $timestamp = date('dmYHis');
        $fullFileName = "{$fileName}_{$timestamp}.xlsx";

        // Full logical Path for xls creation
        $fullPath = "{$path}/{$fullFileName}";
        $xlsDownloadLink = "{$downloadLink}/{$fullFileName}";

        renderDownloadLink($xlsDownloadLink);
        $returnArray['xlsDownloadLink'] = $xlsDownloadLink;
        $returnArray['fullPath'] = $fullPath;

        return $returnArray;
    }
}

$path = $pathPrefix;
global $clientDirectory;

global $clientDB, $dbh;
$dbh->selectDatabase($clientDB);

$xlsDownloadPath = "{$path}{$clientDirectory}/xlsx/TrialBalance/TrialBalance_{$timestamp}.xlsx";
$xlsDownloadLink = "{$clientDirectory}/xlsx/TrialBalance/TrialBalance_{$timestamp}.xlsx";
checkDirPath($xlsDownloadPath, true);

// ///end of copied section


// /////////////////////////START EXCEL DOCUMENT///////////////////////////////

$excel = new Spreadsheet();


$excel->setActiveSheetIndex(0); // Activate it
$excel->getActiveSheet()->setTitle('Accruals Trial Balance');


// header
$excel->getActiveSheet()->SetCellValue(cellReference(1, 1), "{$clientDirectory}");

$excel->getActiveSheet()->SetCellValue(cellReference(2, 1), 'Accruals Trial Balance');


/*
$excel=new PHPExcel();





$excel->setActiveSheetIndex(0); #Activate it
$excel->getActiveSheet()->setTitle("Trial Balance";
*/
// header

$excel->getActiveSheet()->SetCellValue(cellReference(2, 1), "Client: {$client}");
$excel->getActiveSheet()->SetCellValue(cellReference(4, 1), "Property: {$propertyID} - {$propertyName}");
$excel->getActiveSheet()->SetCellValue(cellReference(5, 1), "Report Period: {$periodDescription}");
$excel->getActiveSheet()->SetCellValue(
    cellReference(7, 1),
    "Accruals Period From: {$reportingPeriodFrom} To: {$reportingPeriodTo}"
);
$excel->getActiveSheet()->SetCellValue(cellReference(8, 1), "Cash Period From: {$periodFrom} To: {$periodTo}");
$excel->getActiveSheet()->SetCellValue(cellReference(9, 1), 'Account Code');
$excel->getActiveSheet()->SetCellValue(cellReference(9, 2), 'Account Name');

$excel->getActiveSheet()->SetCellValue(cellReference(9, 3), 'Current Period Debit');
$excel->getActiveSheet()->SetCellValue(cellReference(9, 4), 'Current Period Credit');
$excel->getActiveSheet()->SetCellValue(cellReference(9, 5), 'YTD Opening Balance');
$excel->getActiveSheet()->SetCellValue(cellReference(9, 6), 'YTD Debit');
$excel->getActiveSheet()->SetCellValue(cellReference(9, 7), 'YTD Credit');
$excel->getActiveSheet()->SetCellValue(cellReference(9, 8), 'Closing Balance');

//


$excel->getActiveSheet()->SetCellValue(cellReference(10, 1), 'INCOME');

$x = 0;
$maxLines = 350;
// /////////////////////////////////////////////////
newPageTrialBalance(false);
/*
$pdf->begin_page_ext(842,595,"");
$page++;
$page_header = "ACCRUALS TRIAL BALANCE";
$page_header2 = "";
tbHeader($page_header,$page_header2,$reportingPeriodFrom,$reportingPeriodTo);
if($logo){ generateLogo('landscape');}
//include "tb_header.php";
//include_once "functions/tb_pagebreak.php";///took this out 7 Oct don't know where the function is
include_once "commands/managementReports/functions/tb_pagebreak.php";
$line = 10;
//print "ACCRUALS TRIAL BALANCE<BR><br><table bgcolor=silver cellspacing=1>";
*/

$total_debit_current = 0;
$total_credit_current = 0;
$total_debit_YTD = 0;
$total_credit_YTD = 0;
$total_openingbalance = 0;
$total_closingbalance = 0;


$sub_credit = 0;
$sub_creditYTD = 0;
$sub_tb_closingBalance = 0;
$sub_tb_openingBalance = 0;
$sub_debit = 0;
$sub_debitYTD = 0;
$sub_tb_closingBalanceEXP = 0;
$sub_tb_openingBalanceEXP = 0;
$sub_debitIncome = 0;
$sub_debitIncomeYTD = 0;
$sub_creditEXP = 0;

$sub_creditEXPYTD = 0;

if ($ownerAccountCodes) {
    $acccommandSQL = "SELECT DISTINCT(ACCOUNT) AS ACCOUNT, pmoc_owner_acc
				FROM TEMP2004
				LEFT JOIN  pmoc_o_chart ON pmoc_acc = ACCOUNT
				WHERE PROPERTY = ?
				AND TIMESTAMP = ?
				AND (pmoc_owner = ?)
				ORDER BY {$accountOrder}";
    $params = [$propertyID, $timestamp, $owner];
} else {
    $acccommandSQL = "SELECT DISTINCT(ACCOUNT) AS ACCOUNT
				FROM TEMP2004
				WHERE PROPERTY = ?
				AND TIMESTAMP = ?
				ORDER BY {$accountOrder}"; // echo $acccommandSQL;
    $params = [$propertyID, $timestamp];
}

$acc_temp2004 = $dbh->executeScalars($acccommandSQL, $params); // echo $acccommandSQL;


$takeoncodesSQL = "SELECT DISTINCT pmpb_p_bal.pmpb_acc AS ACCOUNT
                    FROM pmpb_p_bal INNER JOIN
                         pmca_chart ON pmpb_p_bal.pmpb_acc = pmca_chart.pmca_code
                    WHERE (pmpb_p_bal.pmpb_prop = ?) AND (pmpb_p_bal.pmpb_date BETWEEN CONVERT(datetime, ?, 103) AND CONVERT(datetime, ?, 103)) AND                      (pmca_chart.pmca_type = 'I')"; // echo $takeoncodesSQL;

$acc_takeon = $dbh->executeScalars($takeoncodesSQL, [$propertyID, $startFinancialYear, $periodTo]);


$acc2 = array_unique(array_merge($acc_temp2004, $acc_takeon), SORT_REGULAR);

$pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
$pdf->showBoxed('Income', 22, 435 - $line, 275, 30, 'left', '');

foreach ($acc2 as $account) {
    $commandSQL = 'SELECT ACTUAL_INCOME, TYPE
				FROM TEMP2004
				WHERE ACCOUNT = ?
				AND PROPERTY = ?
				AND TIMESTAMP = ?
				ORDER BY ACCOUNT';
    $result = $dbh->executeSet($commandSQL, false, true, [$account, $propertyID, $timestamp]);

    $creditYTD = 0;
    $credit = 0;
    $debitIncomeYTD = 0;
    $debitIncome = 0;
    // pre_print_r($result);
    if (empty($result)) {
        $result[] = ['ACTUAL_INCOME' => 0, 'TYPE' => 'YTD'];
    }

    foreach ($result as $row) {
        $type = trim($row['TYPE']);
        $amount = round($row['ACTUAL_INCOME'], 2);
        if ($type === 'YTD') {
            // /if type is YTD then  if it is negative income put in debit YTD column otherwise put in credit YTD column
            $amount += take_on_balance_accruals($propertyID, $account, $startFinancialYear, $periodTo);
            if ($amount < 0) {
                $creditYTD = 0;
                $debitIncomeYTD = $amount * (-1);
            } else {
                $creditYTD = $amount;
                $debitIncomeYTD = 0;
            }
        } elseif ($amount < 0) {
            // ///////////////else if type is CUR and amount is negative put in debit column else put in credit column
            $credit = 0;
            $debitIncome = $amount * (-1);
        } else {
            $credit = $amount;
            $debitIncome = 0;
        }
    }


    // $accountName                   = account_name();
    $accountName = toTitleCase(accountName($account));

    $tb_openingBalance = 0;
    $tb_closingBalance = bcsub(bcadd($tb_openingBalance, $debitIncomeYTD, 2), $creditYTD, 2);


    $sub_credit = bcadd($sub_credit, $credit, 2);
    $sub_creditYTD = bcadd($sub_creditYTD, $creditYTD, 2);
    $sub_debitIncome = bcadd($sub_debitIncome, $debitIncome, 2);
    $sub_debitIncomeYTD = bcadd($sub_debitIncomeYTD, $debitIncomeYTD, 2);

    $sub_tb_closingBalance = bcadd($sub_tb_closingBalance, $tb_closingBalance, 2);
    $sub_tb_openingBalance = bcadd($sub_tb_openingBalance, $tb_openingBalance, 2);


    if ($credit != 0 || $tb_openingBalance !== 0 || $tb_closingBalance != 0 || $debitIncome != 0 || $debitIncomeYTD != 0) {
        // /////////////////////////////////////////////start excel/////////////////////
        $excel->getActiveSheet()->SetCellValue(cellReference(11 + $x, 1), $account);
        $excel->getActiveSheet()->SetCellValue(cellReference(11 + $x, 2), $accountName);
        $excel->getActiveSheet()->SetCellValue(cellReference(11 + $x, 3), $debitIncome);
        $excel->getActiveSheet()->SetCellValue(cellReference(11 + $x, 4), $credit);
        $excel->getActiveSheet()->SetCellValue(cellReference(11 + $x, 5), $tb_openingBalance);
        $excel->getActiveSheet()->SetCellValue(cellReference(11 + $x, 6), $debitIncomeYTD);
        $excel->getActiveSheet()->SetCellValue(cellReference(11 + $x, 7), $creditYTD);
        $excel->getActiveSheet()->SetCellValue(cellReference(11 + $x, 8), $tb_closingBalance);
        $x++;
        // ///////////////////////////////////////////////////end excel//////////////////////////////////////


        $pdf->setFontExt($_fonts['Helvetica'], 7);
        $pdf->showBoxed(
            "{$account} {$accountName}",
            22,
            425 - $line,
            275,
            30,
            'left',
            ''
        ); // ////////////////////////account name
        $pdf->showBoxed(
            toDecimal($debitIncome),
            255,
            425 - $line,
            75,
            30,
            'right',
            ''
        ); // /////////////////////debit current month
        $pdf->showBoxed(
            toDecimal($credit),
            330,
            425 - $line,
            75,
            30,
            'right',
            ''
        ); // //////////////////////credit current month
        $pdf->showBoxed(
            toDecimal($tb_openingBalance),
            450,
            425 - $line,
            75,
            30,
            'right',
            ''
        ); // /////////////////////opening balance for the year
        $pdf->showBoxed(
            toDecimal($debitIncomeYTD),
            540,
            425 - $line,
            75,
            30,
            'right',
            ''
        ); // ///////////////////////debit YTD
        $pdf->showBoxed(
            toDecimal($creditYTD),
            620,
            425 - $line,
            75,
            30,
            'right',
            ''
        ); // ///////////////////////credit YTD
        $pdf->showBoxed(
            toDecimal($tb_closingBalance),
            710,
            425 - $line,
            75,
            30,
            'right',
            ''
        ); // ////////////////////////closing balance

        updateLine(10);
    }
}

$pdf->setColorExt('fill', 'rgb', 0.88, 0.88, 0.88, 0);
$pdf->rect(250, 443 - $line, 574, 13);
$pdf->fill();
$pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);

$pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
$pdf->showBoxed(
    'Total Income',
    22,
    425 - $line,
    275,
    30,
    'left',
    ''
); // ////////////////////////////TOTAL INCOME////////////
$pdf->setFontExt($_fonts['Helvetica'], 7);
$pdf->showBoxed(
    toDecimal($sub_debitIncome),
    255,
    425 - $line,
    75,
    30,
    'right',
    ''
); // /////////////////////debit current period
$pdf->showBoxed(
    toDecimal($sub_credit),
    330,
    425 - $line,
    75,
    30,
    'right',
    ''
); // /////////////////////credit current period
$pdf->showBoxed(
    toDecimal($sub_tb_openingBalance),
    450,
    425 - $line,
    75,
    30,
    'right',
    ''
); // ////////////////////opening balance
$pdf->showBoxed(toDecimal($sub_debitIncomeYTD), 540, 425 - $line, 75, 30, 'right', ''); // ////////////////////debit YTD
$pdf->showBoxed(toDecimal($sub_creditYTD), 620, 425 - $line, 75, 30, 'right', ''); // //////////////////////credit YTD
$pdf->showBoxed(
    toDecimal($sub_tb_closingBalance),
    710,
    425 - $line,
    75,
    30,
    'right',
    ''
); // ///////////////////////closing balance

$total_debit_current = bcadd($total_debit_current, $sub_debitIncome, 2);
$total_credit_current = bcadd($total_credit_current, $sub_credit, 2);
$total_debit_YTD = bcadd($total_debit_YTD, $sub_debitIncomeYTD, 2);
$total_credit_YTD = bcadd($total_credit_YTD, $sub_creditYTD, 2);
$total_openingbalance = bcadd($total_openingbalance, $sub_tb_openingBalance, 2);
$total_closingbalance = bcadd($total_closingbalance, $sub_tb_closingBalance, 2);

$pdf->moveto(250, 443 - $line);
$pdf->lineto(824, 443 - $line);
$pdf->stroke();

$pdf->moveto(250, 456 - $line);
$pdf->lineto(824, 456 - $line);
$pdf->stroke();
updateLine(35);
// /////////////////////////////EXCEL
$x++;
$x++;
$excel->getActiveSheet()->SetCellValue(cellReference(10 + $x, 1), 'EXPENDITURE');


$pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
$pdf->showBoxed('Expenditure', 22, 435 - $line, 275, 30, 'left', '');


// ///////////////////////////////////////////////get a list of expense account codes from temp table where account code is in TRACC3 group but excluding capital and GST expenditure
if ($ownerAccountCodes) {
    $acccommandSQL = "SELECT DISTINCT(ACCOUNT) AS ACCOUNT,
                                pmoc_owner_acc
			    FROM TEMP2004_EXP
				LEFT JOIN  pmoc_o_chart ON pmoc_acc = ACCOUNT
				WHERE PROPERTY = ?
				AND TIMESTAMP = ?
				AND ACCOUNT NOT IN
                                    (SELECT pmcg_acc
                                    FROM pmcg_chart_grp
                                        WHERE (pmcg_grp = 'TRACC3')
                                        AND (pmcg_subgrp = 'EXPOWNGST') OR
                                        (pmcg_subgrp = 'EXPOWNCAPI'))
                                        AND (pmoc_owner = ?)
				ORDER BY {$accountOrder}";
    $params = [$propertyID, $timestamp, $owner];
} else {
    $acccommandSQL = "SELECT DISTINCT(ACCOUNT) AS ACCOUNT
			    FROM TEMP2004_EXP
				WHERE PROPERTY = ?
				AND TIMESTAMP = ?
				AND ACCOUNT NOT IN
                                    (SELECT pmcg_acc
                                    FROM pmcg_chart_grp
                                        WHERE (pmcg_grp = 'TRACC3')
                                        AND (pmcg_subgrp = 'EXPOWNGST') OR
                                        (pmcg_subgrp = 'EXPOWNCAPI'))
				ORDER BY {$accountOrder}"; // echo $acccommandSQL;

    $params = [$propertyID, $timestamp];
}

$acc_temp2004 = $dbh->executeScalars($acccommandSQL, $params);

$takeoncodesSQL = "SELECT DISTINCT pmpb_p_bal.pmpb_acc AS ACCOUNT
                    FROM pmpb_p_bal INNER JOIN
                         pmca_chart ON pmpb_p_bal.pmpb_acc = pmca_chart.pmca_code
                    WHERE (pmpb_p_bal.pmpb_prop = ?) AND (pmpb_p_bal.pmpb_date BETWEEN CONVERT(datetime, ?, 103) AND CONVERT(datetime, ?, 103)) AND                      (pmca_chart.pmca_type = 'E')"; // echo $takeoncodesSQL;

$acc_takeon = $dbh->executeScalars($takeoncodesSQL, [$propertyID, $startFinancialYear, $periodTo]);

$acc2 = array_unique(array_merge($acc_temp2004, $acc_takeon), SORT_REGULAR);

foreach ($acc2 as $account) {
    $commandSQL = 'SELECT ACTUAL_EXP,
                    TYPE
		FROM TEMP2004_EXP
		    WHERE ACCOUNT = ?
		    AND PROPERTY = ?
		    AND TIMESTAMP = ?
		    ORDER BY ACCOUNT'; // echo $commandSQL;
    $result = $dbh->executeSet($commandSQL, false, true, [$account, $propertyID, $timestamp]);

    if (empty($result)) {
        $result[] = ['ACTUAL_INCOME' => 0, 'TYPE' => 'YTD'];
    }

    $debit = 0;
    $creditEXP = 0;
    $debitYTD = 0;
    $creditEXPYTD = 0;
    foreach ($result as $row) {
        $amount = round($row['ACTUAL_EXP'], 2);
        $type = trim($row['TYPE']);
        if ($type === 'YTD') {
            $amount += take_on_balance_accruals($propertyID, $account, $startFinancialYear, $periodTo);
            if ($amount < 0) {
                $creditEXPYTD = $amount * (-1);
                $debitYTD = 0;
            } else {
                $creditEXPYTD = 0;
                $debitYTD = $amount;
            }
        } elseif ($amount < 0) {
            $creditEXP = $amount * (-1);
            $debit = 0;
        } else {
            $creditEXP = 0;
            $debit = $amount;
        }
    }


    $accountName = toTitleCase(accountName($account));
    $tb_openingBalanceEXP = 0;

    $tb_closingBalanceEXP = bcsub(bcadd($tb_openingBalanceEXP, $debitYTD, 2), $creditEXPYTD, 2);


    $sub_tb_openingBalanceEXP = bcadd($sub_tb_openingBalanceEXP, $tb_openingBalanceEXP, 2);
    $sub_debit = bcadd($sub_debit, $debit, 2);
    $sub_debitYTD = bcadd($sub_debitYTD, $debitYTD, 2);
    $sub_creditEXP = bcadd($sub_creditEXP, $creditEXP, 2);
    $sub_creditEXPYTD = bcadd($sub_creditEXPYTD, $creditEXPYTD, 2);
    $sub_tb_closingBalanceEXP = bcadd($sub_tb_closingBalanceEXP, $tb_closingBalanceEXP, 2);


    if ($debit != 0 || $tb_openingBalanceEXP !== 0 || $debitYTD != 0 || $tb_closingBalanceEXP != 0 || $creditEXP != 0 || $creditEXPYTD != 0) {
        // ///////////////////////////////////////////////////start excel///////////////////////////////////////////////
        $excel->getActiveSheet()->SetCellValue(cellReference(11 + $x, 1), $account);
        $excel->getActiveSheet()->SetCellValue(cellReference(11 + $x, 2), $accountName);
        // $cell->value="$accountName";
        $excel->getActiveSheet()->SetCellValue(cellReference(11 + $x, 3), $debit);
        $excel->getActiveSheet()->SetCellValue(cellReference(11 + $x, 4), $creditEXP);
        $excel->getActiveSheet()->SetCellValue(cellReference(11 + $x, 5), $tb_openingBalanceEXP);
        $excel->getActiveSheet()->SetCellValue(cellReference(11 + $x, 6), $debitYTD);
        $excel->getActiveSheet()->SetCellValue(cellReference(11 + $x, 7), $creditEXPYTD);
        $excel->getActiveSheet()->SetCellValue(cellReference(11 + $x, 8), $tb_closingBalanceEXP);
        $x++;
        // //////////////////////////////////////////////////////////////finish excel///////////////////////////////////////////

        $pdf->setFontExt($_fonts['Helvetica'], 7);
        $pdf->showBoxed(
            "{$account} {$accountName}",
            22,
            425 - $line,
            275,
            30,
            'left',
            ''
        ); // //////////////////////////account name
        $pdf->showBoxed(
            toDecimal($debit),
            255,
            425 - $line,
            75,
            30,
            'right',
            ''
        ); // /////////////////////////debit current period
        $pdf->showBoxed(
            toDecimal($creditEXP),
            330,
            425 - $line,
            75,
            30,
            'right',
            ''
        ); // //////////////////////////credit current period
        $pdf->showBoxed(
            toDecimal($tb_openingBalanceEXP),
            450,
            425 - $line,
            75,
            30,
            'right',
            ''
        ); // //////////////////////////opening balance for the year
        $pdf->showBoxed(
            toDecimal($debitYTD),
            540,
            425 - $line,
            75,
            30,
            'right',
            ''
        ); // //////////////////////////debit YTD
        $pdf->showBoxed(
            toDecimal($creditEXPYTD),
            620,
            425 - $line,
            75,
            30,
            'right',
            ''
        ); // //////////////////////////credit YTD
        $pdf->showBoxed(
            toDecimal($tb_closingBalanceEXP),
            710,
            425 - $line,
            75,
            30,
            'right',
            ''
        ); // //////////////////////////closing balance

        updateLine(10);
    }
}

$pdf->setColorExt('fill', 'rgb', 0.88, 0.88, 0.88, 0);
$pdf->rect(250, 443 - $line, 577, 13);
$pdf->fill();
$pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);

$pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
$pdf->showBoxed(
    'Total Expenditure',
    22,
    425 - $line,
    275,
    30,
    'left',
    ''
); // ////////////////////TOTAL EXPENDITURE//////////////////////////////////
$pdf->setFontExt($_fonts['Helvetica'], 7);
$pdf->showBoxed(
    toDecimal($sub_debit),
    255,
    425 - $line,
    75,
    30,
    'right',
    ''
); // ///////////////////////debit current period
$pdf->showBoxed(
    toDecimal($sub_creditEXP),
    330,
    425 - $line,
    75,
    30,
    'right',
    ''
); // ///////////////////////credit current period
$pdf->showBoxed(
    toDecimal($sub_tb_openingBalanceEXP),
    450,
    425 - $line,
    75,
    30,
    'right',
    ''
); // //////////////////////opening balance beginning of year
$pdf->showBoxed(toDecimal($sub_debitYTD), 540, 425 - $line, 75, 30, 'right', ''); // /////////////////////////debit YTD
$pdf->showBoxed(
    toDecimal($sub_creditEXPYTD),
    620,
    425 - $line,
    75,
    30,
    'right',
    ''
); // /////////////////////////credit YTD
$pdf->showBoxed(
    toDecimal($sub_tb_closingBalanceEXP),
    710,
    425 - $line,
    75,
    30,
    'right',
    ''
); // ////////////////////////closing balance

$pdf->moveto(250, 443 - $line);
$pdf->lineto(824, 443 - $line);
$pdf->stroke();

$pdf->moveto(250, 456 - $line);
$pdf->lineto(824, 456 - $line);
$pdf->stroke();
updateLine(15);


// ////////////////////////////////////// BALANCE SHEET ITEMS///////////////////////////////////////////////////////////////////
//
//
//

$total_debit_current = bcadd($total_debit_current, $sub_debit, 2);
$total_credit_current = bcadd($total_credit_current, $sub_creditEXP, 2);
$total_debit_YTD = bcadd($total_debit_YTD, $sub_debitYTD, 2);
$total_credit_YTD = bcadd($total_credit_YTD, $sub_creditEXPYTD, 2);
$total_openingbalance = bcadd($total_openingbalance, $sub_tb_openingBalanceEXP, 2);
$total_closingbalance = bcadd($total_closingbalance, $sub_tb_closingBalanceEXP, 2);


// GST AND CAPITAL EXPENDITURE
$x++;
$x++;
$excel->getActiveSheet()->SetCellValue(cellReference(10 + $x, 1), 'BALANCE SHEET ITEMS');


$pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
$pdf->showBoxed('Balance Sheet Items', 22, 425 - $line, 275, 30, 'left', '');
updateLine(15);

// ///////////////////////////////////////////////////////obtaining a list of capital and GST account codes///////// as these transactions are shown in the balance sheet section of the trial balance
if ($ownerAccountCodes) {
    $acccommandSQL = "SELECT DISTINCT(ACCOUNT) AS ACCOUNT, pmoc_owner_acc
				FROM TEMP2004_EXP
				LEFT JOIN  pmoc_o_chart ON pmoc_acc = ACCOUNT
				WHERE PROPERTY = ?
				AND TIMESTAMP = ?
				AND ACCOUNT  IN
                          (SELECT pmcg_acc
                            FROM pmcg_chart_grp
                            WHERE (pmcg_grp = 'TRACC3')
                            AND (pmcg_subgrp = 'EXPOWNGST') OR
                             (pmcg_subgrp = 'EXPOWNCAPI'))
				AND (pmoc_owner = ?)
				ORDER BY {$accountOrder}";
    $params = [$propertyID, $timestamp, $owner];
} else {
    $acccommandSQL = "SELECT DISTINCT(ACCOUNT) AS ACCOUNT
				FROM TEMP2004_EXP
				WHERE PROPERTY = ?
				AND TIMESTAMP = ?
				AND ACCOUNT  IN
                          (SELECT pmcg_acc
                            FROM pmcg_chart_grp
                            WHERE (pmcg_grp = 'TRACC3')
                            AND (pmcg_subgrp = 'EXPOWNGST') OR
                             (pmcg_subgrp = 'EXPOWNCAPI'))
				ORDER BY {$accountOrder}";
    $params = [$propertyID, $timestamp];
}


$acc2 = $dbh->executeScalars($acccommandSQL, $params);

// //////////////////////////////////////////////////////extract current CUR and year to date YTD figures from each temp table for capital expenditure and GST account codes
foreach ($acc2 as $account) {
    $commandSQL = 'SELECT ACTUAL_EXP,
                    TYPE
		FROM TEMP2004_EXP
		    WHERE ACCOUNT = ?
		    AND PROPERTY = ?
		    AND TIMESTAMP = ?
		    ORDER BY ACCOUNT';
    $result = $dbh->executeSet($commandSQL, false, true, [$account, $propertyID, $timestamp]);

    $debitYTD = 0;
    $debit = 0;
    $creditEXPYTD = 0;
    $creditEXP = 0;

    foreach ($result as $row) {
        $amount = round($row['ACTUAL_EXP'], 2);
        $type = trim($row['TYPE']);
        // ///////////////////////////////////////////////////////////working out whether to put in the debit or credit column
        if ($type === 'YTD') {
            $amount += take_on_balance_accruals($propertyID, $account, $startFinancialYear, $periodTo);
            if ($amount < 0) {
                $creditEXPYTD = bcmul($amount, -1, 2);
                $debitYTD = 0;
            } else {
                $creditEXPYTD = 0;
                $debitYTD = $amount;
            }
        } elseif ($amount < 0) {
            $creditEXP = bcmul($amount, -1, 2);
            $debit = 0;
        } else {
            $creditEXP = 0;
            $debit = $amount;
        }
    }


    if ($debit == '0.00') {
        $debit = '0.00';
    }

    if ($debitYTD == '0.00') {
        $debitYTD = '0.00';
    }

    if ($creditEXP == '0.00') {
        $creditEXP = '0.00';
    }

    if ($creditEXPYTD == '0.00') {
        $creditEXPYTD = '0.00';
    }

    // $accountName = account_name();
    $accountName = toTitleCase(accountName($account));
    $tb_openingBalanceEXP = 0;

    $tb_closingBalanceEXP = bcsub(bcadd($tb_openingBalanceEXP, $debitYTD, 2), $creditEXPYTD, 2);


    $sub_debit = bcadd($sub_debit, $debit, 2);
    $sub_debitYTD = bcadd($sub_debitYTD, $debitYTD, 2);
    $sub_creditEXP = bcadd($sub_creditEXP, $creditEXP, 2);
    $sub_creditEXPYTD = bcadd($sub_creditEXPYTD, $creditEXPYTD, 2);

    $sub_tb_closingBalanceEXP = bcadd($sub_tb_closingBalanceEXP, $tb_closingBalanceEXP, 2);
    $sub_tb_openingBalanceEXP = bcadd($sub_tb_openingBalanceEXP, $tb_openingBalanceEXP, 2);

    // ////////////////////////////////////////////start excel/////////////////////////capital and GST expenditure
    $excel->getActiveSheet()->SetCellValue(cellReference(11 + $x, 2), $accountName);
    $excel->getActiveSheet()->SetCellValue(cellReference(11 + $x, 3), $debit);
    $excel->getActiveSheet()->SetCellValue(cellReference(11 + $x, 4), $creditEXP);
    $excel->getActiveSheet()->SetCellValue(cellReference(11 + $x, 5), $tb_openingBalanceEXP);
    $excel->getActiveSheet()->SetCellValue(cellReference(11 + $x, 6), $debitYTD);
    $excel->getActiveSheet()->SetCellValue(cellReference(11 + $x, 7), $creditEXPYTD);
    $excel->getActiveSheet()->SetCellValue(cellReference(11 + $x, 8), $tb_closingBalanceEXP);
    $x++;


    // //////////////////////////////////////////////////capital and GST expenditure account codes/////////////////////

    $pdf->setFontExt($_fonts['Helvetica'], 7);
    $pdf->showBoxed("{$accountName}", 22, 425 - $line, 275, 30, 'left', ''); // ///////////////account name
    $pdf->showBoxed(toDecimal($debit), 255, 425 - $line, 75, 30, 'right', ''); // //////////////debit current period
    $pdf->showBoxed(toDecimal($creditEXP), 330, 425 - $line, 75, 30, 'right', ''); // //////////////credit current period
    $pdf->showBoxed(
        toDecimal($tb_openingBalanceEXP),
        450,
        425 - $line,
        75,
        30,
        'right',
        ''
    ); // //////////////opening balance BOY
    $pdf->showBoxed(toDecimal($debitYTD), 540, 425 - $line, 75, 30, 'right', ''); // //////////////debit YTD
    $pdf->showBoxed(toDecimal($creditEXPYTD), 620, 425 - $line, 75, 30, 'right', ''); // //////////////credit YTD
    $pdf->showBoxed(
        toDecimal($tb_closingBalanceEXP),
        710,
        425 - $line,
        75,
        30,
        'right',
        ''
    ); // //////////////closing balance
    // $line = $line + 10;//added 18 may 09


    $total_debit_current = bcadd($total_debit_current, $debit, 2);
    $total_credit_current = bcadd($total_credit_current, $creditEXP, 2);
    $total_debit_YTD = bcadd($total_debit_YTD, $debitYTD, 2);
    $total_credit_YTD = bcadd($total_credit_YTD, $creditEXPYTD, 2);
    $total_openingbalance = bcadd($total_openingbalance, $tb_openingBalanceEXP, 2);
    $total_closingbalance = bcadd($total_closingbalance, $tb_closingBalanceEXP, 2);

    updateLine(10);
    //
} // end foreach

// INCOME ACCRUALS
$ia = round(
    (($ctaTotal[INCOME]['accruedReverse']) + ($ctaTotal[INCOME]['accrued'])),
    2
); // /////////////////////accrued income current period - difference between accrued income reversal and closing balance
// $ia = round(($totalAcrruedIncomeReverse + $totalAcrruedIncome),2);///////////////////////accrued income current period - difference between accrued income reversal and closing balance
if ($ia < 0) {
    $ia = bcmul($ia, -1, 2);
    $id_debit = 0;
    $ia_credit = $ia;
} else {
    $ia_debit = $ia;
    $ia_credit = 0;
}

$iaYTD = round(($totalAcrruedIncomePY - ($ctaTotal[INCOME]['accrued'])), 2); // //////////////////////accrued income YTD
// $iaYTD = round(($totalAcrruedIncomePY - $totalAcrruedIncome),2);////////////////////////accrued income YTD
if ($iaYTD < 0) {
    $iaYTD = bcmul($iaYTD, -1, 2);
    $ia_debitYTD = $iaYTD;
    $ia_creditYTD = 0;
} else {
    $ia_debitYTD = 0;
    $ia_creditYTD = $iaYTD;
}

$total_debit_current = bcadd($total_debit_current, $ia_debit, 2);
$total_credit_current = bcadd($total_credit_current, $ia_credit, 2);
$total_debit_YTD = bcadd($total_debit_YTD, $ia_debitYTD, 2);
$total_credit_YTD = bcadd($total_credit_YTD, $ia_creditYTD, 2);
$total_openingbalance = bcadd($total_openingbalance, $totalAcrruedIncomePY, 2);
$total_closingbalance = bcadd($total_closingbalance, $ctaTotal[INCOME]['accrued'], 2); // $totalAcrruedIncome;


// EXPENSE ACCRUALS
$ea = round(
    (($ctaTotal[EXPENDITURE]['accruedReverse']) + ($ctaTotal[EXPENDITURE]['accrued'])),
    2
); // ////////////////////accrued expense current period difference between current and prior period
// $ea = round(($grand_EXP_accruedExp_reverse + $grand_EXP_accruedExp),2);//////////////////////accrued expense current period difference between current and prior period

if ($ea < 0) {
    $ea = bcmul($ea, -1, 2);
    $ea_debit = $ea;
    $ea_credit = 0;
} else {
    $ea_debit = 0;
    $ea_credit = $ea;
}

$eaYTD = round(
    ($totalAccruedReverseYTD + ($ctaTotal[EXPENDITURE]['accrued'])),
    2
); // ///////////////////////accrued expense YTD
// $eaYTD = round(($totalAccruedReverseYTD + $grand_EXP_accruedExp),2);/////////////////////////accrued expense YTD

if ($eaYTD < 0) {
    $eaYTD = bcmul($eaYTD, -1, 2);
    $ea_creditYTD = 0;
    $ea_debitYTD = $eaYTD;
} else {
    $ea_creditYTD = $eaYTD;
    $ea_debitYTD = 0;
    // print "true";
}

$total_debit_current = bcadd($total_debit_current, $ea_debit, 2);
$total_credit_current = bcadd($total_credit_current, $ea_credit, 2);
$total_debit_YTD = bcadd($total_debit_YTD, $ea_debitYTD, 2);
$total_credit_YTD = bcadd($total_credit_YTD, $ea_creditYTD, 2);
$total_openingbalance = bcadd($total_openingbalance, $totalAccruedReverseYTD, 2);
$total_closingbalance = bcadd($total_closingbalance, ($ctaTotal[EXPENDITURE]['accrued']) * (-1), 2);

// PREPAID EXPENDITURE

$pe = round(
    (($ctaTotal[EXPENDITURE]['futureCostsReverse']) + ($ctaTotal[EXPENDITURE]['futureCosts'])),
    2
); // ///////////////////difference between future costs current and prior periods
// $pe = round(($grand_EXP_futureCosts_reverse + $grand_EXP_futureCosts),2);/////////////////////difference between future costs current and prior periods

if ($pe < 0) {
    $pe = bcmul($pe, -1, 2);

    $pe_debit = $pe;
    $pe_credit = 0;
} else {
    $pe_debit = 0;

    $pe_credit = $pe;
}

$peYTD = round(
    ($totalFutureCostsReverseYTD + ($ctaTotal[EXPENDITURE]['futureCosts'])),
    2
); // //////////////////////////////future period costs YTD
// $peYTD = round(($totalFutureCostsReverseYTD + $grand_EXP_futureCosts),2);////////////////////////////////future period costs YTD

if ($peYTD < 0) {
    $peYTD = bcmul($peYTD, -1, 2);

    $pe_debitYTD = $peYTD;
    $pe_creditYTD = 0;
} else {
    $pe_creditYTD = $peYTD;
    $pe_debitYTD = 0;
}

$total_debit_current = bcadd($total_debit_current, $pe_debit, 2);
$total_credit_current = bcadd($total_credit_current, $pe_credit, 2);
$total_openingbalance = bcadd($total_openingbalance, $totalFutureCostsReverseYTD, 2);
$total_debit_YTD = bcadd($total_debit_YTD, $pe_debitYTD, 2);
$total_credit_YTD = bcadd($total_credit_YTD, $pe_creditYTD, 2);
$total_closingbalance = bcadd($total_closingbalance, $ctaTotal[EXPENDITURE]['futureCosts'] * (-1), 2);
// $total_closingbalance       = $total_closingbalance + $grand_EXP_futureCosts*(-1);


// PREPAID INCOME
$pi = round(
    (($ctaTotal[INCOME]['chargedInAdvanceReverse']) + ($ctaTotal[INCOME]['chargedInAdvance'])),
    2
); // /////////////////////////charged in advance difference between current and prior periods
// $pi = round(($totalChargedInAdvanceReverse + $totalChargedInAdvance),2);///////////////////////////charged in advance difference between current and prior periods

if ($pi < 0) {
    $pi = bcmul($pi, -1, 2);
    $pi_debit = 0;
    $pi_credit = $pi;
} else {
    $pi_credit = 0;
    $pi_debit = $pi;
}

$piYTD = round(
    ($totalChargedInAdvancePY + ($ctaTotal[INCOME]['chargedInAdvance'])),
    2
); // ////////////////////////charged in advance YTD
// $piYTD = round(($totalChargedInAdvancePY + $totalChargedInAdvance),2);//////////////////////////charged in advance YTD
if ($piYTD < 0) {
    $piYTD = bcmul($piYTD, -1, 2);
    $pi_debitYTD = 0;
    $pi_creditYTD = $piYTD;
} else {
    $pi_debitYTD = $piYTD;
    $pi_creditYTD = 0;
}

$total_debit_current = bcadd($total_debit_current, $pi_debit, 2);
$total_credit_current = bcadd($total_credit_current, $pi_credit, 2);
$total_debit_YTD = bcadd($total_debit_YTD, $pi_debitYTD, 2);
$total_credit_YTD = bcadd($total_credit_YTD, $pi_creditYTD, 2);
$total_openingbalance = bcadd($total_openingbalance, $totalChargedInAdvancePY * (-1), 2);
$total_closingbalance = bcadd($total_closingbalance, $ctaTotal[INCOME]['chargedInAdvance'], 2); // $totalChargedInAdvance


// ///////////////////////////////////////////////////GST output tax/////////////////////////////////////could get this from temp table if it gets written to by account
$GSTsql = "SELECT COALESCE(SUM(artr_tax_amt), 0) as tax
		FROM  ar_transaction
			WHERE  (ref_2 = ?)
			AND (trans_type <> 'CSH')
			AND (trans_type <> 'REV')
			AND (trans_date BETWEEN CONVERT(datetime, ?, 103) AND CONVERT(datetime, ?, 103))
			AND (ref_3 <> 'NULL')";
$GSTresult = $dbh->executeSingle($GSTsql, [$propertyID, $periodFrom, $periodTo]);
$gstOUTPUTonIncome = round($GSTresult['tax'], 2);


if ($gstOUTPUTonIncome < 0) {
    $gstOUT_debit = bcmul($gstOUTPUTonIncome, -1, 2);
    $gstOUT_credit = 0;
} else {
    $gstOUT_debit = 0;
    $gstOUT_credit = $gstOUTPUTonIncome;
}


if ($gstOUT_debit == '0.00') {
    $gstOUT_debit = '0.00';
}


if ($gstOUT_credit == '0.00') {
    $gstOUT_credit = '0.00';
}

// /////////////////////////////////////////GST output tax YTD /////////total GST on accruals basis where year stamp is current year
$GSTquery = "SELECT COALESCE(SUM(artr_tax_amt), 0) as tax
		FROM  ar_transaction
			WHERE  (ref_2 = ?)
			AND (trans_type <> 'CSH')
			AND (trans_type <> 'REV')
			AND (trans_date BETWEEN CONVERT(datetime, ?, 103) AND CONVERT(datetime, ?, 103))
			AND (ref_3 <> 'NULL')";

$GSTresult = $dbh->executeSingle($GSTquery, [$propertyID, $startFinancialYear, $periodTo]);
$gstYTD = round($GSTresult['tax'], 2);

$gstYTD += take_on_balance_gst($propertyID, '10003', $startFinancialYear, $periodTo);
if ($gstYTD < 0) {
    $gstOUTYTDdebit = bcmul($gstYTD, -1, 2);
    $gstOUTYTDcredit = 0;
} else {
    $gstOUTYTDdebit = 0;
    $gstOUTYTDcredit = $gstYTD;
}


if ($gstOUTYTDdebit == '0.00') {
    $gstOUTYTDdebit = '0.00';
}


if ($gstOUTYTDcredit == '0.00') {
    $gstOUTYTDcredit = '0.00';
}

$gstOUTopening = 0;
$gstOUTclosing = bcsub(bcadd($gstOUTopening, $gstOUTYTDdebit, 2), $gstOUTYTDcredit, 2);


$total_debit_current = bcadd($total_debit_current, $gstOUT_debit, 2);
$total_credit_current = bcadd($total_credit_current, $gstOUT_credit, 2);
$total_openingbalance = bcadd($total_openingbalance, $gstOUTopening, 2);
$total_debit_YTD = bcadd($total_debit_YTD, $gstOUTYTDdebit, 2);
$total_credit_YTD = bcadd($total_credit_YTD, $gstOUTYTDcredit, 2);
$total_closingbalance = bcadd($total_closingbalance, $gstOUTclosing, 2);


// /////////////////////////////////////////////////////////////GST EXPENDITURE//////////////////////////////////////////

// ///////////section used for unpaid creditors
$credControl_opening = 0;
$credControl = 0;
$credControl_opening_Mnt = 0;

$tempExp = tempExpensesTB($propertyID, $timestamp, 'CUR');
$creditors_prior_net = $tempExp['creditors_reversal_net'];
$creditors_prior_gst = $tempExp['creditors_reversal_gst'];
$creditors_current_net = $tempExp['creditors_current_net'];
$creditors_current_gst = $tempExp['creditors_current_gst'];
$credControl_opening_Mnt = bcadd($creditors_prior_net, $creditors_prior_gst, 2);

// print_r ($creditors_prior_net);
// echo "gst";
// print_r ($creditors_prior_gst);
// $credControl = $creditors_current_net + $creditors_current_gst + $creditors_prior_net + $creditors_prior_gst;

$tempExp = tempExpensesTB($propertyID, $timestamp, 'YTD');
$creditors_priorYr_net = $tempExp['creditors_reversal_net'];
$creditors_priorYr_gst = $tempExp['creditors_reversal_gst'];
$credControl_opening = bcadd(
    $creditors_priorYr_net,
    $creditors_priorYr_gst,
    2
); // creditors control opening balance at begining of year


// //////////////////////////end of unpaid creditors section

$tb_gstpaid = bcmul($financial['gstPaid'], -1, 2); // /pulled this from property Financial summary
$diff_between_cur_and_prevperiod_unpaid_gst = bcadd($creditors_current_gst, $creditors_prior_gst, 2); // added
$gstpaidAccrual = round(($tb_gstpaid + $diff_between_cur_and_prevperiod_unpaid_gst), 2);

if ($gstpaidAccrual > 0) {
    $gstIN_debit = $gstpaidAccrual;
    $gstIN_credit = 0;
} else {
    $gstIN_debit = 0;
    $gstIN_credit = $gstpaidAccrual;
}

/*
$tb_gstpaid = $gstpaid;///pulled this from property Financial summary
$diff_between_cur_and_prevperiod_unpaid_gst = $creditors_current_gst - $creditors_prior_gst;//added
$gstpaid = round(($tb_gstpaid + $diff_between_cur_and_prevperiod_unpaid_gst),2);
$bas_gst_input = $gstpaid;

if($gstpaid < 0)
{
    $gstIN_debit =  $gstpaid * (-1);
    $gstIN_credit =0;
}
else
{
    $gstIN_debit = 0;
    $gstIN_credit = $gstpaid;
}
*/
/*
if ($gstIN_debit == "0.00")
{
    $gstIN_debit = "0.00";
}

if ($gstIN_credit == "0.00")
{
$gstIN_credit = "0.00";
}

*/
// print "<br><B>$gstpaidA = round(($tb_gstpaidA - $diff_between_cur_and_YTDunpaid_gst),2);</B><br>";
// $tb_gstpaidA = $gstpaidA;/
$tb_gstpaidA = $financial['gstPaidYTD']; // pulled this from property Financial summary

$diff_between_cur_and_YTDunpaid_gst = bcadd($creditors_current_gst, $creditors_priorYr_gst, 2);
$gstpaidA = round(($tb_gstpaidA - $diff_between_cur_and_YTDunpaid_gst), 2);

if ($gstpaidA < 0) {
    $gstINYTDdebit = bcmul($gstpaidA, -1, 2);
    $gstINYTDcredit = 0;
} else {
    $gstINYTDdebit = 0;
    $gstINYTDcredit = $gstpaidA;
}

if ($gstINYTDdebit == '0.00') {
    $gstINYTDdebit = '0.00';
}


if ($gstINYTDcredit == '0.00') {
    $gstINYTDcredit = '0.00';
}

$gstINopening = 0;
$gstINclosing = bcsub(bcadd($gstINopening, $gstINYTDdebit, 2), $gstINYTDcredit, 2);


$total_debit_current = bcadd($total_debit_current, $gstIN_debit, 2);
$total_credit_current = bcadd($total_credit_current, $gstIN_credit, 2);
$total_openingbalance = bcadd($total_openingbalance, $gstINopening, 2);
$total_debit_YTD = bcadd($total_debit_YTD, $gstINYTDdebit, 2);
$total_credit_YTD = bcadd($total_credit_YTD, $gstINYTDcredit, 2);
$total_closingbalance = bcadd($total_closingbalance, $gstINclosing, 2);


// /////////////////////// PAYMENTS TO THE OWNER/////////////////////////////////
// ///////////payments to owner calculated in propertyFinancialSummary page

$paymentsDuringM = $paymentsForPeriod; // from property financial summary
$paymentsDuringY = ($paymentsYTD); // /from property financial summary, excludes take on balances
// $paymentsDuringY = $paymentsDuringYear;///from property financial summary, excludes take on balances

if ($paymentsDuringM < 0) {
    $paymentsDebit = bcmul($paymentsDuringM, -1, 2);
    $paymentsCredit = 0;
} else {
    $paymentsDebit = 0;
    $paymentsCredit = $paymentsDuringM;
}

if ($paymentsDebit == 0) {
    $paymentsDebit = '0.00';
}

if ($paymentsCredit == 0) {
    $paymentsCredit = '0.00';
}

if ($paymentsDuringY < 0) {
    $pmtsYTDdebit = bcmul($paymentsDuringY, -1, 2);
    $pmtsYTDcredit = 0;
} else {
    $pmtsYTDdebit = 0;
    $pmtsYTDcredit = $paymentsDuringY;
}

if ($pmtsYTDdebit == 0) {
    $pmtsYTDdebit = '0.00';
}

if ($pmtsYTDcredit == 0) {
    $pmtsYTDcredit = '0.00';
}

$pmts_owner_opening = 0;
$pmts_owner_closing = bcsub(bcadd($pmts_owner_opening, $pmtsYTDdebit, 2), $pmtsYTDcredit, 2);


$total_debit_current = bcadd($total_debit_current, $paymentsDebit, 2);
$total_credit_current = bcadd($total_credit_current, $paymentsCredit, 2);
$total_debit_YTD = bcadd($total_debit_YTD, $pmtsYTDdebit, 2);
$total_credit_YTD = bcadd($total_credit_YTD, $pmtsYTDcredit, 2);
$total_openingbalance = bcadd($total_openingbalance, $pmts_owner_opening, 2);
$total_closingbalance = bcadd($total_closingbalance, $pmts_owner_closing, 2);

// //////////////////////////////////////////////////////////CALCULATE TRUST ACCOUNT BALANCES///////////////////////////
// /////////////////////////////////////current month debit or credit
$movement = bcadd($net[C]['net'], $paymentsForPeriod, 2);
// $movement = $netcash + $paymentsForPeriod;
$dc_pos = '';
if ($movement >= 0) {
    $dc_pos = '257';
    $tba_debit = $movement;
    $tba_credit = 0;
} else {
    $dc_pos = '369';
    $tba_credit = bcmul($movement, -1, 2);
    $tba_debit = 0;
}

// //////////////////////////////////////YTD debit or credit//////////////////////////////
$movementA = bcadd($net[C]['netYTD'], $paymentsYTD, 2);
// $movementA = $netcashA + $paymentsYTD ;

$dc_posA = '';
if ($movementA >= 0) {
    $dc_posA = '536';
    $tbaYTDdebit = $movementA;
    $tbaYTDcredit = 0;
} else {
    $dc_posA = '638';
    $tbaYTDcredit = bcmul($movementA, -1, 2);
    $tbaYTDdebit = 0;
}


$total_debit_current = bcadd($total_debit_current, $tba_debit, 2);
$total_credit_current = bcadd($total_credit_current, $tba_credit, 2);
$total_debit_YTD = bcadd($total_debit_YTD, $tbaYTDdebit, 2);
$total_credit_YTD = bcadd($total_credit_YTD, $tbaYTDcredit, 2);


$tba_closing = bcsub(bcadd($net[C]['balanceYTD'], $tbaYTDdebit, 2), $tbaYTDcredit, 2);
// $tba_closing = $diffA + $tbaYTDdebit - $tbaYTDcredit;


$total_openingbalance = bcadd($total_openingbalance, $net[C]['balanceYTD'], 2);
// $total_openingbalance = $total_openingbalance  + $diffA;
$total_closingbalance = bcadd($total_closingbalance, $tba_closing, 2);

// ///////////////////retained earnings opening balance
$pmzzcode = 'RETINC';
$pmzzcode .= $currentYear;

$retearnSQL = 'SELECT pmzz_desc as amount
		FROM pmzz_param
			WHERE pmzz_par_type = ?
			and pmzz_code = ?';
$retearn_result = $dbh->executeSingle($retearnSQL, [$pmzzcode, $propertyID]);
$retearn = trim($retearn_result['amount']);


$total_openingbalance = bcadd($total_openingbalance, $retearn, 2);
$total_closingbalance = bcadd($total_closingbalance, $retearn, 2);


// ///////DEBTORS CONTROL/////////////////////////////////////////////////////////
$debtors_closing = 0;
$debtorsYTDopening = 0;

$tempInc = tempIncomeTB($propertyID, $timestamp, 'CUR');
$debtors_prior_net = $tempInc['arrears_reversal_net'];
$debtors_prior_gst = $tempInc['arrears_reversal_gst'];
$debtors_current_net = $tempInc['arrears_current_net'];
$debtors_current_gst = $tempInc['arrears_current_gst'];
$debtors_closing = bcadd($debtors_current_net, $debtors_current_gst, 2);

$debtorsControl_movement = ($debtors_current_net + $debtors_current_gst + $debtors_prior_net + $debtors_prior_gst);

if ($debtorsControl_movement > 0) {
    $debtorsControl_debit = $debtorsControl_movement;

    $debtorsControl_credit = '0.00';
} else {
    $debtorsControl_credit = bcmul($debtorsControl_movement, -1, 2);

    $debtorsControl_debit = '0.00';
}


$tempIncYTD = tempIncomeTB($propertyID, $timestamp, 'YTD');
$debtors_priorYTDnet = $tempIncYTD['arrears_reversal_net'];
$debtors_priorYTDgst = $tempIncYTD['arrears_reversal_gst'];
$debtorsYTDopening = bcadd($debtors_priorYTDnet, $debtors_priorYTDgst, 2);

$debtorsControlYTD = bcsub($debtors_closing, $debtorsYTDopening, 2);

$debtorsControl_closing = $debtors_closing;


if ($debtorsControlYTD > 0) {
    $debtorsControl_creditYTD = '0.00';
    $debtorsControl_debitYTD = $debtorsControlYTD;
} else {
    $debtorsControl_creditYTD = bcmul($debtorsControlYTD, -1, 2);
    $debtorsControl_debitYTD = '0.00';
}


$total_debit_current = bcadd($total_debit_current, $debtorsControl_debit, 2);
$total_credit_current = bcadd($total_credit_current, $debtorsControl_credit, 2);
$total_debit_YTD = bcadd($total_debit_YTD, $debtorsControl_debitYTD, 2);
$total_credit_YTD = bcadd($total_credit_YTD, $debtorsControl_creditYTD, 2);

// $debtorsControl_closing = $grand_total_gross + $debtorsControl_debitYTD - $debtorsControl_creditYTD;
//

// $total_openingbalance = $total_openingbalance  + $grand_total_gross;
$total_openingbalance = bcadd($total_openingbalance, $debtorsYTDopening, 2);
$total_closingbalance = bcadd($total_closingbalance, $debtorsControl_closing, 2);

//  UNPAID CREDITORS

// see temp table extract above by GST expenditure

$credControl_closing = bcmul(bcadd($creditors_current_net, $creditors_current_gst, 2), -1, 2);

if ($credControl_closing <= $credControl_opening) {
    $credControl_creditYTD = bcsub($credControl_opening, $credControl_closing, 2);
    $credControl_debitYTD = 0;
} else {
    $credControl_debitYTD = bcmul(bcsub($credControl_opening, $credControl_closing, 2), -1, 2);
    $credControl_creditYTD = 0;
}

/*
if
    ($credControl_closing >= ($credControl_opening * (-1)))
{
    $credControl_creditYTD = $credControl_closing - $credControl_opening;
    $credControl_debitYTD = 0;
}
else
{
    $credControl_debitYTD	= ($credControl_opening * (-1)) + $credControl_closing;
    $credControl_creditYTD = 0;
}
*/

// ///////////////////////creditors control

// $credControl = $diff_between_cur_and_prevperiod_unpaid_creditors_gross;//////////////////////creditors control movement current period newly defined above
$credControl = bcmul(bcsub($credControl_closing, $credControl_opening_Mnt, 2), -1, 2);
if ($credControl > 0) {
    $credControl_credit = $credControl;
    $credControl_debit = 0;
} else {
    $credControl = bcmul($credControl, -1, 2);
    $credControl_debit = $credControl;
    $credControl_credit = 0;
}


//	$credControl_closing = $credControl_opening + $credControl_debitYTD - $credControl_creditYTD;


$total_debit_current = bcadd($total_debit_current, $credControl_debit, 2);
$total_credit_current = bcadd($total_credit_current, $credControl_credit, 2);
$total_debit_YTD = bcadd($total_debit_YTD, $credControl_debitYTD, 2);
$total_credit_YTD = bcadd($total_credit_YTD, $credControl_creditYTD, 2);
$total_openingbalance = bcadd($total_openingbalance, $credControl_opening, 2);
$total_closingbalance = bcadd($total_closingbalance, $credControl_closing, 2);

// GST REMITTED TO OWNER
/*
$ownerremitgst = "SELECT SUM(pmxc_alloc_amt) AS amount,
            SUM(pmxc_tax_amt) as tax
        FROM pmxc_ap_alloc
            WHERE (pmxc_f_type = 'PAY')
            AND (pmxc_prop = '$propertyID')
            AND (pmxc_acc IN
                  (SELECT pmcg_acc
                   FROM pmcg_chart_grp
                   WHERE (pmcg_grp = 'TRACC3')
                   AND (pmcg_subgrp = 'EXPOWNGST')))
                AND (pmxc_alloc_dt BETWEEN '$periodFrom' AND '$periodTo')";
$ownerremitgst_result = $dbh->executeSingle($ownerremitgst);
$remitgst = trim($ownerremitgst_result["amount"]);
$remittax = trim($ownerremitgst_result["tax"]);

$remitgst = $remitgst + $remittax;
*/
$remitgst = ($financial['gstRemitted']);
if ($remitgst < 0) {
    $gstremit_debit = bcmul($remitgst, -1, 2);
    $gstremit_credit = 0;
} else {
    $gstremit_credit = $remitgst;
    $gstremit_debit = 0;
}


//
// GST REMITTED TO OWNER YTD
/*
$ownerremitgstA = "SELECT SUM(pmxc_alloc_amt) AS amount,
        SUM(pmxc_tax_amt) as tax
        FROM pmxc_ap_alloc
            WHERE (pmxc_f_type = 'PAY')
            AND (pmxc_prop = '$propertyID')
            AND (pmxc_acc IN
                  (SELECT pmcg_acc
                   FROM pmcg_chart_grp
                   WHERE (pmcg_grp = 'TRACC3')
                   AND (pmcg_subgrp = 'EXPOWNGST')))
                AND (pmxc_alloc_dt BETWEEN CONVERT(datetime, '{$startFinancialYear}', 103) AND '$periodTo')";
$ownerremitgstA_result = $dbh->executeSingle($ownerremitgstA);
$remitgstA = trim($ownerremitgstA_result["amount"]);
$remittaxA = trim($ownerremitgstA_result["tax"]);

$remitgstA = $remitgstA + $remittaxA;*/

$remitgstA = ($financial['gstRemittedYTD']);

if ($remitgstA < 0) {
    $gstremit_debitYTD = bcmul($remitgstA, -1, 2);
    $gstremit_creditYTD = 0;
} else {
    $gstremit_creditYTD = $remitgstA;
    $gstremit_debitYTD = 0;
}


$total_debit_current = bcadd($total_debit_current, $gstremit_debit, 2);
$total_credit_current = bcadd($total_credit_current, $gstremit_credit, 2);
$total_debit_YTD = bcadd($total_debit_YTD, $gstremit_debitYTD, 2);
$total_credit_YTD = bcadd($total_credit_YTD, $gstremit_creditYTD, 2);

$gstremit_closing = bcsub($gstremit_debitYTD, $gstremit_creditYTD, 2);

$gstremit_opening = 0;

$total_openingbalance = bcadd($total_openingbalance, $gstremit_opening, 2);
$total_closingbalance = bcadd($total_closingbalance, $gstremit_closing, 2);


$pdf->setFontExt($_fonts['Helvetica'], 7);
$pdf->showBoxed('Accrued income', 22, 425 - $line, 275, 30, 'left', ''); // ///////////////////////accrued income
$pdf->showBoxed(
    toDecimal($ia_debit),
    255,
    425 - $line,
    75,
    30,
    'right',
    ''
); // ////////////////////////debit current period
$pdf->showBoxed(
    toDecimal($ia_credit),
    330,
    425 - $line,
    75,
    30,
    'right',
    ''
); // ////////////////////////credit current period
$pdf->showBoxed(
    toDecimal($totalAcrruedIncomePY),
    450,
    425 - $line,
    75,
    30,
    'right',
    ''
); // ///////////////////////opening balance BOY
$pdf->showBoxed(toDecimal($ia_debitYTD), 540, 425 - $line, 75, 30, 'right', ''); // ///////////////////////debit YTD
$pdf->showBoxed(toDecimal($ia_creditYTD), 620, 425 - $line, 75, 30, 'right', ''); // ///////////////////////credit YTD
$pdf->showBoxed(
    toDecimal($ctaTotal[INCOME]['accrued']),
    710,
    425 - $line,
    75,
    30,
    'right',
    ''
); // //////////////////////closing balance //($totalAcrruedIncome)
updateLine(10);

$pdf->setFontExt($_fonts['Helvetica'], 7);
$pdf->showBoxed(
    'Accrued expenditure',
    22,
    425 - $line,
    275,
    30,
    'left',
    ''
); // ///////////////////////accrued expenditure
$pdf->showBoxed(
    toDecimal($ea_debit),
    255,
    425 - $line,
    75,
    30,
    'right',
    ''
); // /////////////////////////debit current period
$pdf->showBoxed(
    toDecimal($ea_credit),
    330,
    425 - $line,
    75,
    30,
    'right',
    ''
); // ////////////////////////credit current period
$pdf->showBoxed(
    toDecimal($totalAccruedReverseYTD),
    450,
    425 - $line,
    75,
    30,
    'right',
    ''
); // ///////////////////////opening balance
$pdf->showBoxed(toDecimal($ea_debitYTD), 540, 425 - $line, 75, 30, 'right', ''); // //////////////////////debit YTD
$pdf->showBoxed(toDecimal($ea_creditYTD), 620, 425 - $line, 75, 30, 'right', ''); // //////////////////////credit YTD
$pdf->showBoxed(
    toDecimal(($ctaTotal[EXPENDITURE]['accrued']) * -1),
    710,
    425 - $line,
    75,
    30,
    'right',
    ''
); // ///////////////////////closing balance //$grand_EXP_accruedExp
updateLine(10);

$pdf->setFontExt($_fonts['Helvetica'], 7);
$pdf->showBoxed(
    'Future period costs',
    22,
    425 - $line,
    275,
    30,
    'left',
    ''
); // /////////future period costs///////////////////////////////
$pdf->showBoxed(toDecimal($pe_debit), 255, 425 - $line, 75, 30, 'right', ''); // ///////////////debit current period
$pdf->showBoxed(toDecimal($pe_credit), 330, 425 - $line, 75, 30, 'right', ''); // //////////////credit current period
$pdf->showBoxed(
    toDecimal($totalFutureCostsReverseYTD),
    450,
    425 - $line,
    75,
    30,
    'right',
    ''
); // ///////////////opening balance
$pdf->showBoxed(toDecimal($pe_debitYTD), 540, 425 - $line, 75, 30, 'right', ''); // ///////////////debit YTD
$pdf->showBoxed(toDecimal($pe_creditYTD), 620, 425 - $line, 75, 30, 'right', ''); // ///////////////credit YTD
$pdf->showBoxed(
    toDecimal(($ctaTotal[EXPENDITURE]['futureCosts']) * -1),
    710,
    425 - $line,
    75,
    30,
    'right',
    ''
); // ////////////////closing balance //$grand_EXP_futureCosts
updateLine(10);

$pdf->setFontExt($_fonts['Helvetica'], 7);
$pdf->showBoxed('Charged in advance', 22, 425 - $line, 275, 30, 'left', ''); // ////////////charged in advance
$pdf->showBoxed(toDecimal($pi_debit), 255, 425 - $line, 75, 30, 'right', ''); // ////////////////debit current period
$pdf->showBoxed(toDecimal($pi_credit), 330, 425 - $line, 75, 30, 'right', ''); // ////////////////credit current period
$pdf->showBoxed(
    toDecimal($totalChargedInAdvancePY * -1),
    450,
    425 - $line,
    75,
    30,
    'right',
    ''
); // ///////////////opening balance
$pdf->showBoxed(toDecimal($pi_debitYTD), 540, 425 - $line, 75, 30, 'right', ''); // ///////////////debit YTD
$pdf->showBoxed(toDecimal($pi_creditYTD), 620, 425 - $line, 75, 30, 'right', ''); // ///////////////credit YTD
$pdf->showBoxed(
    toDecimal($ctaTotal[INCOME]['chargedInAdvance']),
    710,
    425 - $line,
    75,
    30,
    'right',
    ''
); // //////////////closing balance//($totalChargedInAdvance)
updateLine(10);

$pdf->setFontExt($_fonts['Helvetica'], 7);
$pdf->showBoxed(
    $_SESSION['country_default']['tax_label'] . ' output tax (on income)',
    22,
    425 - $line,
    275,
    30,
    'left',
    ''
); // ///////////////////////////GST output tax on income
$pdf->showBoxed(
    toDecimal($gstOUT_debit),
    255,
    425 - $line,
    75,
    30,
    'right',
    ''
); // ////////////////////////debit current period
$pdf->showBoxed(
    toDecimal($gstOUT_credit),
    330,
    425 - $line,
    75,
    30,
    'right',
    ''
); // /////////////////////////credit current period
$pdf->showBoxed(
    toDecimal($gstOUTopening),
    450,
    425 - $line,
    75,
    30,
    'right',
    ''
); // /////////////////////////opening balance beginning of year
$pdf->showBoxed(toDecimal($gstOUTYTDdebit), 540, 425 - $line, 75, 30, 'right', ''); // //////////////////////////debit YTD
$pdf->showBoxed(
    toDecimal($gstOUTYTDcredit),
    620,
    425 - $line,
    75,
    30,
    'right',
    ''
); // ///////////////////////////credit YTD
$pdf->showBoxed(
    toDecimal($gstOUTclosing),
    710,
    425 - $line,
    75,
    30,
    'right',
    ''
); // ////////////////////////////closing balance
updateLine(10);

$pdf->setFontExt($_fonts['Helvetica'], 7);
$pdf->showBoxed(
    $_SESSION['country_default']['tax_label'] . ' input tax (on expenditure)',
    22,
    425 - $line,
    275,
    30,
    'left',
    ''
); // //////////////////GST ON EXPENDITURE
$pdf->showBoxed(toDecimal($gstIN_debit), 255, 425 - $line, 75, 30, 'right', ''); // /////////////////debit current period
$pdf->showBoxed(
    toDecimal($gstIN_credit),
    330,
    425 - $line,
    75,
    30,
    'right',
    ''
); // /////////////////credit current period
$pdf->showBoxed(
    toDecimal($gstINopening),
    450,
    425 - $line,
    75,
    30,
    'right',
    ''
); // ////////////////opening balance beginning of year
$pdf->showBoxed(toDecimal($gstINYTDdebit), 540, 425 - $line, 75, 30, 'right', ''); // /////////////////debit YTD
$pdf->showBoxed(toDecimal($gstINYTDcredit), 620, 425 - $line, 75, 30, 'right', ''); // /////////////////credit YTD
$pdf->showBoxed(toDecimal($gstINclosing), 710, 425 - $line, 75, 30, 'right', ''); // ///////////////////closing balance
updateLine(10);

$pdf->setFontExt($_fonts['Helvetica'], 7);
$pdf->showBoxed('Payments to the owner', 22, 425 - $line, 275, 30, 'left', ''); // /////////////////PAYMENTS TO OWNER
$pdf->showBoxed(
    toDecimal($paymentsDebit),
    255,
    425 - $line,
    75,
    30,
    'right',
    ''
); // /////////////////debit current period
$pdf->showBoxed(
    toDecimal($paymentsCredit),
    330,
    425 - $line,
    75,
    30,
    'right',
    ''
); // //////////////////credit current period
$pdf->showBoxed(
    toDecimal($pmts_owner_opening),
    450,
    425 - $line,
    75,
    30,
    'right',
    ''
); // ///////////////////opening balance at beginning of year
$pdf->showBoxed(toDecimal($pmtsYTDdebit), 540, 425 - $line, 75, 30, 'right', ''); // ///////////////////debit YTD
$pdf->showBoxed(toDecimal($pmtsYTDcredit), 620, 425 - $line, 75, 30, 'right', ''); // ////////////////////credit YTD
$pdf->showBoxed(
    toDecimal($pmts_owner_closing),
    710,
    425 - $line,
    75,
    30,
    'right',
    ''
); // ///////////////////////closing balance
updateLine(10);

$pdf->setFontExt($_fonts['Helvetica'], 7);
$pdf->showBoxed(
    ucwords(strtolower($_SESSION['country_default']['trust_bank_account'])),
    22,
    425 - $line,
    275,
    30,
    'left',
    ''
); // //////////////////////////////////	TRUST BANK ACCOUNT
$pdf->showBoxed(
    toDecimal($tba_debit),
    255,
    425 - $line,
    75,
    30,
    'right',
    ''
); // //////////////////////////////////debit current period
$pdf->showBoxed(
    toDecimal($tba_credit),
    330,
    425 - $line,
    75,
    30,
    'right',
    ''
); // //////////////////////////////////credit current period
$pdf->showBoxed(
    toDecimal($net[C]['balanceYTD']),
    450,
    425 - $line,
    75,
    30,
    'right',
    ''
); // ///////////////////////////////////opening balance beginning of year
$pdf->showBoxed(
    toDecimal($tbaYTDdebit),
    540,
    425 - $line,
    75,
    30,
    'right',
    ''
); // ////////////////////////////////////debit current YTD
$pdf->showBoxed(
    toDecimal($tbaYTDcredit),
    620,
    425 - $line,
    75,
    30,
    'right',
    ''
); // ///////////////////////////////////credit YTD
$pdf->showBoxed(
    toDecimal($tba_closing),
    710,
    425 - $line,
    75,
    30,
    'right',
    ''
); // ////////////////////////////////////closing balance
updateLine(10);

$pdf->setFontExt($_fonts['Helvetica'], 7);
$pdf->showBoxed(
    'Retained earnings*',
    22,
    425 - $line,
    275,
    30,
    'left',
    ''
); // ///////////////////////////RETAINED EARNINGS OPENING BALANCE///////////////////////
$pdf->showBoxed('0.00', 255, 425 - $line, 75, 30, 'right', ''); // //////////////////////////debit current period
$pdf->showBoxed('0.00', 330, 425 - $line, 75, 30, 'right', ''); // //////////////////////////credit current period
$pdf->showBoxed(
    toDecimal($retearn),
    450,
    425 - $line,
    75,
    30,
    'right',
    ''
); // /////////////////////////opening balance at beginning of year
$pdf->showBoxed(' 0.00', 540, 425 - $line, 75, 30, 'right', ''); // //////////////////////////debit YTD
$pdf->showBoxed(' 0.00', 620, 425 - $line, 75, 30, 'right', ''); // ///////////////////////////credit YTD
$pdf->showBoxed(toDecimal($retearn), 710, 425 - $line, 75, 30, 'right', ''); // //////////////////////////closing balance
updateLine(10);

$pdf->setFontExt($_fonts['Helvetica'], 7);
$pdf->showBoxed('Debtors control', 22, 425 - $line, 275, 30, 'left', ''); // /////////////////////DEBTORS CONTROL
$pdf->showBoxed(
    toDecimal($debtorsControl_debit),
    255,
    425 - $line,
    75,
    30,
    'right',
    ''
); // ///////////////////////debit current period
$pdf->showBoxed(
    toDecimal($debtorsControl_credit),
    330,
    425 - $line,
    75,
    30,
    'right',
    ''
); // ///////////////////////credit current period
$pdf->showBoxed(
    toDecimal($debtorsYTDopening),
    450,
    425 - $line,
    75,
    30,
    'right',
    ''
); // //////////////////////opening balance at begining of year //$grand_total_gross
$pdf->showBoxed(
    toDecimal($debtorsControl_debitYTD),
    540,
    425 - $line,
    75,
    30,
    'right',
    ''
); // //////////////////////debit YTD
$pdf->showBoxed(
    toDecimal($debtorsControl_creditYTD),
    620,
    425 - $line,
    75,
    30,
    'right',
    ''
); // //////////////////////credit YTD
$pdf->showBoxed(
    toDecimal($debtorsControl_closing),
    710,
    425 - $line,
    75,
    30,
    'right',
    ''
); // //////////////////////closing balance
updateLine(10);

$pdf->setFontExt($_fonts['Helvetica'], 7);
$pdf->showBoxed(
    'Creditors control',
    22,
    425 - $line,
    275,
    30,
    'left',
    ''
); // ////////////////////CREDITORS CONTROL/////////////////////////
$pdf->showBoxed(
    toDecimal($credControl_debit),
    255,
    425 - $line,
    75,
    30,
    'right',
    ''
); // /////////////////////debit current period
$pdf->showBoxed(
    toDecimal($credControl_credit),
    330,
    425 - $line,
    75,
    30,
    'right',
    ''
); // ////////////////////credit current period
$pdf->showBoxed(
    toDecimal($credControl_opening),
    450,
    425 - $line,
    75,
    30,
    'right',
    ''
); // /////////////////////opening balance at beginning of year
$pdf->showBoxed(
    toDecimal($credControl_debitYTD),
    540,
    425 - $line,
    75,
    30,
    'right',
    ''
); // /////////////////////debit YTD
$pdf->showBoxed(
    toDecimal($credControl_creditYTD),
    620,
    425 - $line,
    75,
    30,
    'right',
    ''
); // ///////////////////////credit YTD
$pdf->showBoxed(
    toDecimal($credControl_closing),
    710,
    425 - $line,
    75,
    30,
    'right',
    ''
); // /////////////////////closing balance
updateLine(10);

$pdf->setFontExt($_fonts['Helvetica'], 7);
$pdf->showBoxed(
    $_SESSION['country_default']['tax_label'] . ' remitted to owner/ATO',
    22,
    425 - $line,
    275,
    30,
    'left',
    ''
); // ////////////////////GST REMITTED TO OWNER/ATO//////////////
$pdf->showBoxed(
    toDecimal($gstremit_debit),
    255,
    425 - $line,
    75,
    30,
    'right',
    ''
); // ///////////////////debit current period
$pdf->showBoxed(
    toDecimal($gstremit_credit),
    330,
    425 - $line,
    75,
    30,
    'right',
    ''
); // ///////////////////credit current period
$pdf->showBoxed(
    toDecimal($gstremit_opening),
    450,
    425 - $line,
    75,
    30,
    'right',
    ''
); // ///////////////////opening balance at beginning of year
$pdf->showBoxed(toDecimal($gstremit_debitYTD), 540, 425 - $line, 75, 30, 'right', ''); // ///////////////////debit YTD
$pdf->showBoxed(toDecimal($gstremit_creditYTD), 620, 425 - $line, 75, 30, 'right', ''); // ///////////////////credit YTD
$pdf->showBoxed(
    toDecimal($gstremit_closing),
    710,
    425 - $line,
    75,
    30,
    'right',
    ''
); // ///////////////////closing balance
updateLine(10);

$pdf->setColorExt('fill', 'rgb', 0.88, 0.88, 0.88, 0);
$pdf->rect(250, 435 - $line, 574, 15);
$pdf->fill();
$pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
$pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
$pdf->showBoxed(
    '',
    22,
    419 - $line,
    275,
    30,
    'left',
    ''
); // //////////////////////////////TOTALS///////////////////////////
$pdf->showBoxed(
    toDecimal($total_debit_current),
    255,
    419 - $line,
    75,
    30,
    'right',
    ''
); // //////////////////////////debit current period
$pdf->showBoxed(
    toDecimal($total_credit_current),
    330,
    419 - $line,
    75,
    30,
    'right',
    ''
); // //////////////////////////credit current period
$pdf->showBoxed(
    toDecimal($total_openingbalance),
    450,
    419 - $line,
    75,
    30,
    'right',
    ''
); // /////////////////////////opening balance at beginning of year
$pdf->showBoxed(
    toDecimal($total_debit_YTD),
    540,
    419 - $line,
    75,
    30,
    'right',
    ''
); // ///////////////////////////debit YTD
$pdf->showBoxed(
    toDecimal($total_credit_YTD),
    620,
    419 - $line,
    75,
    30,
    'right',
    ''
); // ///////////////////////////credit YTD
$pdf->showBoxed(
    toDecimal($total_closingbalance),
    710,
    419 - $line,
    75,
    30,
    'right',
    ''
); // /////////////////////////closing balance


$pdf->moveto(250, 450 - $line);
$pdf->lineto(824, 450 - $line);
$pdf->stroke();


updateLine(15);
$pdf->setFontExt($_fonts['Helvetica'], 8);
$pdf->showBoxed(
    "* Please note that this represents retained income from when the management started and includes the following balance sheet accounts which are cleared at the end of each year:\n" . $_SESSION['country_default']['tax_label'] . ' OUPUT TAX, ' . $_SESSION['country_default']['tax_label'] . ' INPUT TAX, PAYMENTS TO THE OWNER, ' . $_SESSION['country_default']['tax_label'] . ' REMITTED TO OWNER/ATO, CAPITAL ITEMS.',
    22,
    420 - $line,
    775,
    30,
    'left',
    ''
);


// $line = $line - 5;

$pdf->setlinewidth(0.5);
$pdf->moveto(18, 515);
$pdf->lineto(18, 450 - $line);
$pdf->stroke();

$pdf->moveto(250, 515);
$pdf->lineto(250, 450 - $line);
$pdf->stroke();

$pdf->moveto(445, 515);
$pdf->lineto(445, 450 - $line);
$pdf->stroke();


$pdf->moveto(824, 515);
$pdf->lineto(824, 450 - $line);
$pdf->stroke();


$pdf->moveto(18, 450 - $line);
$pdf->lineto(824, 450 - $line);
$pdf->stroke();


$pdf->setFontExt($_fonts['Helvetica'], 8);
// $pdf->showBoxed ("Printed on $date", 22, 5, 275, 30, "left", "");
$pdf->showBoxed("Page {$page}", 745, 5, 75, 30, 'right', '');


$pdf->end_page_ext('');
$line = 10;


// TRIAL BALANCE
// /START OF EXTENDED EXCEL SECTION

// ////////////////////////////////////////////////////////excel Accrued Income//////////////////////////
$excel->getActiveSheet()->SetCellValue(cellReference(11 + $x, 2), 'Accrued income');
$excel->getActiveSheet()->SetCellValue(cellReference(11 + $x, 3), $ia_debit);
$excel->getActiveSheet()->SetCellValue(cellReference(11 + $x, 4), $ia_credit);
$excel->getActiveSheet()->SetCellValue(cellReference(11 + $x, 5), $totalAcrruedIncomePY);
$excel->getActiveSheet()->SetCellValue(cellReference(11 + $x, 6), $ia_debitYTD);
$excel->getActiveSheet()->SetCellValue(cellReference(11 + $x, 7), $ia_creditYTD);
$excel->getActiveSheet()->SetCellValue(cellReference(11 + $x, 8), $ctaTotal[INCOME]['accrued']); // ($totalAcrruedIncome);
$x++;

// ///////////////////////////////////////////////////excel accrued expenditure///////////////////
$excel->getActiveSheet()->SetCellValue(cellReference(11 + $x, 2), 'Accrued expenditure');
$excel->getActiveSheet()->SetCellValue(cellReference(11 + $x, 3), $ea_debit);
$excel->getActiveSheet()->SetCellValue(cellReference(11 + $x, 4), $ea_credit);
$excel->getActiveSheet()->SetCellValue(cellReference(11 + $x, 5), $totalAccruedReverseYTD);
$excel->getActiveSheet()->SetCellValue(cellReference(11 + $x, 6), $ea_debitYTD);
$excel->getActiveSheet()->SetCellValue(cellReference(11 + $x, 7), $ea_creditYTD);
$excel->getActiveSheet()->SetCellValue(
    cellReference(11 + $x, 8),
    $ctaTotal[EXPENDITURE]['accrued'] * (-1)
); // ($grand_EXP_accruedExp);
$x++;

// /////////////////////////////////////////////excel future period costs////////////////////////
$excel->getActiveSheet()->SetCellValue(cellReference(11 + $x, 2), 'Future period costs');
$excel->getActiveSheet()->SetCellValue(cellReference(11 + $x, 3), $pe_debit);
$excel->getActiveSheet()->SetCellValue(cellReference(11 + $x, 4), $pe_credit);
$excel->getActiveSheet()->SetCellValue(cellReference(11 + $x, 5), $totalFutureCostsReverseYTD);
$excel->getActiveSheet()->SetCellValue(cellReference(11 + $x, 6), $pe_debitYTD);
$excel->getActiveSheet()->SetCellValue(cellReference(11 + $x, 7), $pe_creditYTD);
$excel->getActiveSheet()->SetCellValue(cellReference(11 + $x, 8), $ctaTotal[EXPENDITURE]['futureCosts'] * (-1));
// $cell->value=$grand_EXP_futureCosts);
$x++;

// //////////////////////////////////////////////start excel CHARGED IN ADVANCE//////////////////////////////
$excel->getActiveSheet()->SetCellValue(cellReference(11 + $x, 2), 'Charged in advance');
$excel->getActiveSheet()->SetCellValue(cellReference(11 + $x, 3), $pi_debit);
$excel->getActiveSheet()->SetCellValue(cellReference(11 + $x, 4), $pi_credit);
$excel->getActiveSheet()->SetCellValue(cellReference(11 + $x, 5), $totalChargedInAdvancePY * (-1));
$excel->getActiveSheet()->SetCellValue(cellReference(11 + $x, 6), $pi_debitYTD);
$excel->getActiveSheet()->SetCellValue(cellReference(11 + $x, 7), $pi_creditYTD);
$excel->getActiveSheet()->SetCellValue(
    cellReference(11 + $x, 8),
    $ctaTotal[INCOME]['chargedInAdvance']
); // ($totalChargedInAdvance);
$x++;

// /////////////////////////////////////////////////////start excel////////////////////////GST output tax
$excel->getActiveSheet()->SetCellValue(
    cellReference(11 + $x, 2),
    $_SESSION['country_default']['tax_label'] . ' output tax (on income)'
);
$excel->getActiveSheet()->SetCellValue(cellReference(11 + $x, 3), $gstOUT_debit);
$excel->getActiveSheet()->SetCellValue(cellReference(11 + $x, 4), $gstOUT_credit);
$excel->getActiveSheet()->SetCellValue(cellReference(11 + $x, 5), $gstOUTopening);
$excel->getActiveSheet()->SetCellValue(cellReference(11 + $x, 6), $gstOUTYTDdebit);
$excel->getActiveSheet()->SetCellValue(cellReference(11 + $x, 7), $gstOUTYTDcredit);
$excel->getActiveSheet()->SetCellValue(cellReference(11 + $x, 8), $gstOUTclosing);
$x++;

// ///////////////////////////////////////////////////////EXCEL - PAYMENTS TO OWNER////////////////////////////////////////
$excel->getActiveSheet()->SetCellValue(cellReference(11 + $x, 2), 'Payments to the owner');
$excel->getActiveSheet()->SetCellValue(cellReference(11 + $x, 3), $paymentsDebit);
$excel->getActiveSheet()->SetCellValue(cellReference(11 + $x, 4), $paymentsCredit);
$excel->getActiveSheet()->SetCellValue(cellReference(11 + $x, 5), $pmts_owner_opening);
$excel->getActiveSheet()->SetCellValue(cellReference(11 + $x, 6), $pmtsYTDdebit);
$excel->getActiveSheet()->SetCellValue(cellReference(11 + $x, 7), $pmtsYTDcredit);
$excel->getActiveSheet()->SetCellValue(cellReference(11 + $x, 8), $pmts_owner_closing);
$x++;

// ///////////////////////////////////////////////////////////EXCEL - BANK ACCOUNT ////////////////////////////////////////////
$excel->getActiveSheet()->SetCellValue(
    cellReference(11 + $x, 2),
    ucwords(strtolower($_SESSION['country_default']['trust_bank_account']))
);
$excel->getActiveSheet()->SetCellValue(cellReference(11 + $x, 3), $tba_debit);
$excel->getActiveSheet()->SetCellValue(cellReference(11 + $x, 4), $tba_credit);
$excel->getActiveSheet()->SetCellValue(cellReference(11 + $x, 5), $net[C]['balanceYTD']);
$excel->getActiveSheet()->SetCellValue(cellReference(11 + $x, 6), $tbaYTDdebit);
$excel->getActiveSheet()->SetCellValue(cellReference(11 + $x, 7), $tbaYTDcredit);
$excel->getActiveSheet()->SetCellValue(cellReference(11 + $x, 8), $tba_closing);
$x++;

// ////////////////////////////////////////////////////////////EXCEL/////////////////////////////////////////GST on expenditure
$excel->getActiveSheet()->SetCellValue(
    cellReference(11 + $x, 2),
    $_SESSION['country_default']['tax_label'] . ' input tax (on expenditure)'
);
$excel->getActiveSheet()->SetCellValue(cellReference(11 + $x, 3), $gstIN_debit);
$excel->getActiveSheet()->SetCellValue(cellReference(11 + $x, 4), $gstIN_credit);
$excel->getActiveSheet()->SetCellValue(cellReference(11 + $x, 5), $gstINopening);
$excel->getActiveSheet()->SetCellValue(cellReference(11 + $x, 6), $gstINYTDdebit);
$excel->getActiveSheet()->SetCellValue(cellReference(11 + $x, 7), $gstINYTDcredit);
$excel->getActiveSheet()->SetCellValue(cellReference(11 + $x, 8), $gstINclosing);
$x++;

// ///////////////////////////////////////////////////////////////////////EXCEL////////////////////////////
$excel->getActiveSheet()->SetCellValue(cellReference(11 + $x, 2), 'Retained earnings*');
$excel->getActiveSheet()->SetCellValue(cellReference(11 + $x, 3), '0.00');
$excel->getActiveSheet()->SetCellValue(cellReference(11 + $x, 4), '0.00');
$excel->getActiveSheet()->SetCellValue(cellReference(11 + $x, 5), $retearn);
$excel->getActiveSheet()->SetCellValue(cellReference(11 + $x, 6), '0.00');
$excel->getActiveSheet()->SetCellValue(cellReference(11 + $x, 7), '0.00');
$excel->getActiveSheet()->SetCellValue(cellReference(11 + $x, 8), $retearn);
$x++;

// /////////////////////////////////////////////////////////////EXCEL//////////////////////////////////////////
$excel->getActiveSheet()->SetCellValue(cellReference(11 + $x, 2), 'Debtors control');
$excel->getActiveSheet()->SetCellValue(cellReference(11 + $x, 3), $debtorsControl_debit);
$excel->getActiveSheet()->SetCellValue(cellReference(11 + $x, 4), $debtorsControl_credit);
$excel->getActiveSheet()->SetCellValue(cellReference(11 + $x, 5), $debtorsYTDopening);
$excel->getActiveSheet()->SetCellValue(cellReference(11 + $x, 6), $debtorsControl_debitYTD);
$excel->getActiveSheet()->SetCellValue(cellReference(11 + $x, 7), $debtorsControl_creditYTD);
$excel->getActiveSheet()->SetCellValue(cellReference(11 + $x, 8), $debtorsControl_closing);
$x++;

// ////////////////////////////////////////////////////////////EXCEL///////////////////////////////////////
$excel->getActiveSheet()->SetCellValue(cellReference(11 + $x, 2), 'Creditors control');
$excel->getActiveSheet()->SetCellValue(cellReference(11 + $x, 3), $credControl_debit);
$excel->getActiveSheet()->SetCellValue(cellReference(11 + $x, 4), $credControl_credit);
$excel->getActiveSheet()->SetCellValue(cellReference(11 + $x, 5), $credControl_opening);
$excel->getActiveSheet()->SetCellValue(cellReference(11 + $x, 6), $credControl_debitYTD);
$excel->getActiveSheet()->SetCellValue(cellReference(11 + $x, 7), $credControl_creditYTD);
$excel->getActiveSheet()->SetCellValue(cellReference(11 + $x, 8), $credControl_closing);
$x++;

// ////////////////////////////////////////////////////////////EXCEL///////////////////////////////////////////
$excel->getActiveSheet()->SetCellValue(
    cellReference(11 + $x, 2),
    $_SESSION['country_default']['tax_label'] . ' remitted to owner/ATO'
);
$excel->getActiveSheet()->SetCellValue(cellReference(11 + $x, 3), $gstremit_debit);
$excel->getActiveSheet()->SetCellValue(cellReference(11 + $x, 4), $gstremit_credit);
$excel->getActiveSheet()->SetCellValue(cellReference(11 + $x, 5), $gstremit_opening);
$excel->getActiveSheet()->SetCellValue(cellReference(11 + $x, 6), $gstremit_debitYTD);
$excel->getActiveSheet()->SetCellValue(cellReference(11 + $x, 7), $gstremit_creditYTD);
$excel->getActiveSheet()->SetCellValue(cellReference(11 + $x, 8), $gstremit_closing);

// ////////////////////////////////////////////////////////////EXCEL - totals///////////////////////////////////////////
$excel->getActiveSheet()->SetCellValue(cellReference(13 + $x, 2), 'TOTALS');

$excel->getActiveSheet()->SetCellValue(cellReference(13 + $x, 3), $total_debit_current);

$excel->getActiveSheet()->SetCellValue(cellReference(13 + $x, 4), $total_credit_current);

$excel->getActiveSheet()->SetCellValue(cellReference(13 + $x, 5), $total_openingbalance);

$excel->getActiveSheet()->SetCellValue(cellReference(13 + $x, 6), $total_debit_YTD);

$excel->getActiveSheet()->SetCellValue(cellReference(13 + $x, 7), $total_credit_YTD);

$excel->getActiveSheet()->SetCellValue(cellReference(13 + $x, 8), $total_closingbalance);


$_excel = new Xlsx($excel);
$_excel->save($xlsDownloadPath);


$accrualsExcelAttachments[] = [$xlsDownloadPath, "{$propertyID} - {$propertyName}.xlsx"];

renderDownloadLink($xlsDownloadLink);

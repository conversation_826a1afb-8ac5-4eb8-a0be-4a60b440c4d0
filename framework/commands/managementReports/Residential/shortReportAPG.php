<?php

//
//
// SHORT PROPERTY REPORT - APG SPECIFIC
//
//

include_once __DIR__ . '/residentialReportFunctions.php';
// include_once("./data/administrator/ar/dbInterface.php");
// include_once ("./commands/ar/invoicePDF.php");

global $_fonts, $reportDescription;

$page++;
$totalGross_receipts = 0;
$totalGST_receipts = 0;
$totalNet_receipts = 0;
$totalNet_payments = 0;
$totalUCGST_receipts = 0;
$totalUCGross_receipts = 0;
$totalUCNet_receipts = 0;


$totalGST_payments = 0;
$totalGross_payments = 0;
$mychar  = "\n";

$prop_result = getAgentOwnerCodes($propertyID);
$agent_code = $prop_result['agent_code'];
$owner_code = $prop_result['owner_code'];

$agentDetails = getCompAddress($agent_code);
$ownerDetails = getCompAddress($owner_code);

$agent_name = $agentDetails['company_name'];
$agent_street = $agentDetails['street'];
$agent_city = $agentDetails['city'];
$agent_state = $agentDetails['state'];
$agent_postcode = $agentDetails['postcide'];
$agent_streets = explode("\n", $agent_street);

$owner_name = $ownerDetails['company_name'];
$owner_street = $ownerDetails['street'];
$owner_city = $ownerDetails['city'];
$owner_state = $ownerDetails['state'];
$owner_postcode = $ownerDetails['postcode'];
$owner_streets = explode("\n", $owner_street);

$line = -10;

$pdf->begin_page_ext(595, 842, '');

// generate logo
if ($logo) {
    $logoObj = new ClientLogo($logoPath);
    $logoObj->preRender($pdf);
}

$agentData = dbGetAgentDetails();
$agentDetailsNew = new agentDetails($propertyID);
$agentDetailsNew->bindAttributesFrom($agentData);
$agentDetailsNew->render($pdf);

$page_header = 'RESIDENTIAL PROPERTY REPORT';

$pdf->setFontExt($_fonts['Helvetica-Bold'], 9);

// #############LOGO #############################
// include "logo2.php";
// ###############################################


$pdf->setFontExt($_fonts['Helvetica'], 10);

$line += 10;
$line += 10;
$pdf->setFontExt($_fonts['Helvetica'], 10);

$line += 60;

$pdf->showBoxed($owner_name, 80, 760 - $line, 590, 10, 'left', '');
$line += 10;

foreach ($owner_streets as $ostreet) {
    $pdf->showBoxed($ostreet, 80, 760 - $line, 590, 10, 'left', '');
    $line += 10;
}


$pdf->showBoxed("{$owner_city}, {$owner_state} {$owner_postcode}", 80, 760 - $line, 590, 10, 'left', '');
$line += 70;


$pdf->setFontExt($_fonts['Helvetica-Bold'], 10);
$pdf->showBoxed($reportDescription, 180, 760 - $line, 590, 10, 'left', '');
$line += 10;
$pdf->showBoxed("{$propertyName}", 180, 760 - $line, 590, 10, 'left', '');
$line += 10;
$pdf->showBoxed("Period From: {$periodFrom} To: {$periodTo}", 180, 760 - $line, 590, 10, 'left', '');
$line += 40;

// /////////////////////



$pdf->setFontExt($_fonts['Helvetica-Bold'], 10);
$pdf->showBoxed('Receipts', 20, 760 - $line, 590, 20, 'left', '');
$line += 10;
$pdf->setFontExt($_fonts['Helvetica'], 9);
$pdf->showBoxed('Tenant', 20, 760 - $line, 590, 20, 'left', '');
$pdf->showBoxed('Description', 170, 760 - $line, 590, 20, 'left', '');
$pdf->showBoxed('From', 275, 760 - $line, 90, 20, 'right', '');
$pdf->showBoxed('To', 318, 760 - $line, 90, 20, 'right', '');
$pdf->showBoxed('Net', 360, 760 - $line, 100, 20, 'right', '');
$pdf->showBoxed($_SESSION['country_default']['tax_label'], 400, 760 - $line, 100, 20, 'right', '');
$pdf->showBoxed('Total', 460, 760 - $line, 100, 20, 'right', '');

$pdf->moveto(20, 768 - $line);
$pdf->lineto(565, 768 - $line);
$pdf->stroke();

$line += 20;


$receipts = getReceipts($propertyID, $periodFrom, $periodTo);
if (empty($receipts)) {
    $receipts = getReceipts($propertyID, $periodFrom, $periodTo, true);
}


$totalReceiptTax = 0;

if ($receipts) {
    foreach ($receipts as $thisRow) {
        $accDetails = [];
        $lease_code  = $thisRow['code'];
        $lease_nameRaw = $thisRow['lease_name'];
        $lease_name = limit_name($lease_nameRaw, 35);

        $pmxd_acc = $thisRow['pmxd_acc'];
        $gross = $thisRow['pmxd_alloc_amt'];
        $partial = $thisRow['pmxd_partial'];

        $partialText = null;
        if ($partial == PARTIAL_PART) {
            $partialText = '- partial';
        }

        if ($partial == PARTIAL_BALANCE) {
            $partialText = '- balance';
        }

        $pmxd_tax = $thisRow['pmxd_tax_amt'];
        $pmxd_tax *= -1;
        $gross *= -1;
        $t_batch = $thisRow['pmxd_t_batch'];
        $t_line =  $thisRow['pmxd_t_line'];

        $totalReceiptTax += $pmxd_tax;
        $netAmount = $gross - $pmxd_tax;

        $accDetails = ($t_batch && $t_line) ? getAccountDetails($propertyID, $pmxd_acc, $t_batch, $t_line) : [];

        $descriptionRaw = $accDetails['description'];
        $description = limit_name($descriptionRaw, 38) . ' ' . $partialText;
        $dtfrom = $accDetails['spare_date_1'];
        $dtto = $accDetails['spare_date_2'];

        $netAmount_display = formatting($netAmount);
        $GSTAmount_display = formatting($pmxd_tax);
        $grossAmount_display = formatting($gross);

        $totalNet_receipts += $netAmount;
        $totalGST_receipts += $pmxd_tax;
        $totalGross_receipts += $gross;

        if ($line > 750) {// 715

            $pdf->setFontExt($_fonts['Helvetica'], 7);
            $pdf->showBoxed("Page {$page}", 495, 0, 75, 20, 'right', '');

            $traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'shortOwnersReport', A4_PORTRAIT);
            $traccFooter->prerender($pdf);

            $pdf->end_page_ext('');
            $page++;

            $pdf->begin_page_ext(595, 842, '');
            if ($logo) {
                $logoObj = new ClientLogo($logoPath);
                $logoObj->preRender($pdf);
            }

            $line = 10;

            $pdf->setFontExt($_fonts['Helvetica-Bold'], 10);
            $pdf->showBoxed("OWNER STATEMENT (CONT'D)", 180, 760 - $line, 590, 20, 'left', '');
            $line += 10;
            $pdf->showBoxed("{$propertyName}", 180, 760 - $line, 590, 20, 'left', '');
            $line += 10;
            $pdf->showBoxed("Period From: {$periodFrom} To: {$periodTo}", 180, 760 - $line, 590, 20, 'left', '');
            $line += 40;

            $pdf->setFontExt($_fonts['Helvetica-Bold'], 10);
            $pdf->showBoxed("Receipts (CONT'D)", 20, 760 - $line, 590, 20, 'left', '');
            $line += 10;
            $pdf->setFontExt($_fonts['Helvetica'], 10);
            $pdf->showBoxed('Tenant', 20, 760 - $line, 590, 20, 'left', '');
            $pdf->showBoxed('Description', 170, 760 - $line, 590, 20, 'left', '');
            $pdf->showBoxed('From', 275, 760 - $line, 90, 20, 'right', '');
            $pdf->showBoxed('To', 318, 760 - $line, 90, 20, 'right', '');
            $pdf->showBoxed('Net', 360, 760 - $line, 100, 20, 'right', '');
            $pdf->showBoxed($_SESSION['country_default']['tax_label'], 400, 760 - $line, 100, 20, 'right', '');
            $pdf->showBoxed('Total', 460, 760 - $line, 100, 20, 'right', '');

            $pdf->moveto(20, 768 - $line);
            $pdf->lineto(565, 768 - $line);
            $pdf->stroke();

            $line += 20;
        }

        $pdf->setFontExt($_fonts['Helvetica'], 8);
        $pdf->showBoxed("{$lease_name}", 20, 760 - $line, 590, 20, 'left', '');
        $pdf->showBoxed("{$description}", 170, 760 - $line, 590, 20, 'left', '');
        $pdf->showBoxed("{$dtfrom}", 280, 760 - $line, 90, 20, 'right', '');
        $pdf->showBoxed("{$dtto}", 330, 760 - $line, 90, 20, 'right', '');
        $pdf->showBoxed("{$netAmount_display}", 370, 760 - $line, 100, 20, 'right', '');
        $pdf->showBoxed("{$GSTAmount_display}", 410, 760 - $line, 100, 20, 'right', '');
        $pdf->showBoxed("{$grossAmount_display}", 470, 760 - $line, 100, 20, 'right', '');
        $line += 10;

        $i++;
    }
}

// echo"totalRecieptTax - $totalRecieptTax <hr />";

// Note: instead of checking the lease codes from the receipts query, go and grab all unallocated transactions from pmuc_Ualloc_sch table for that property

$unallocatedReceipts = getAccUnallocAmtAll($propertyID, $periodFrom, $periodTo);

$totalReceiptUnallocTax = 0;
foreach ($unallocatedReceipts as $thisLine) {

    $lease_code = $thisLine['pmuc_lease'];
    $nameRaw = $thisLine['lease_name'];
    $name = limit_name($nameRaw, 35);

    $pmuc_descRaw = $thisLine['pmuc_desc'];
    $pmuc_desc = limit_name($pmuc_descRaw, 38);
    // tax will be used later in the tracc short report

    $date = $thisLine['date'];

    $pmuc_gross = $thisLine['gross'];
    $pmuc_gross *= -1;
    $pmuc_grossDisplay = formatting($pmuc_gross);

    $pmuc_tax = $thisLine['tax'];
    $pmuc_tax *= -1;
    $pmuc_taxDisplay = formatting($pmuc_tax);

    $pmuc_amt = $thisLine['net'];
    $pmuc_amt *= -1;
    $pmuc_amtDisplay = formatting($pmuc_amt);

    $totalUCGST_receipts += $pmuc_tax;
    $totalUCGross_receipts += $pmuc_gross;
    $totalUCNet_receipts += $pmuc_amt;

    if ($pmuc_amt != '0.00') {
        $pmuc_amt = formatting($pmuc_amt);
        $pdf->setFontExt($_fonts['Helvetica'], 8);
        $pdf->showBoxed("{$name}", 20, 760 - $line, 590, 20, 'left', '');
        $pdf->showBoxed("{$pmuc_desc}", 170, 760 - $line, 590, 20, 'left', '');
        $pdf->showBoxed("{$date}", 280, 760 - $line, 90, 20, 'right', '');
        $pdf->showBoxed("{$date}", 330, 760 - $line, 90, 20, 'right', '');
        $pdf->showBoxed("{$pmuc_amtDisplay}", 370, 760 - $line, 100, 20, 'right', '');
        $pdf->showBoxed("{$pmuc_taxDisplay}", 410, 760 - $line, 100, 20, 'right', '');
        $pdf->showBoxed("{$pmuc_grossDisplay}", 470, 760 - $line, 100, 20, 'right', '');
        $line += 10;
    }

    // }

    if ($line > 750) {// 715
        $pdf->setFontExt($_fonts['Helvetica'], 7);
        $pdf->showBoxed("Page {$page}", 495, 0, 75, 20, 'right', '');

        $traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'shortOwnersReport', A4_PORTRAIT);
        $traccFooter->prerender($pdf);

        $pdf->end_page_ext('');
        $page++;

        $pdf->begin_page_ext(595, 842, '');
        if ($logo) {
            $logoObj = new ClientLogo($logoPath);
            $logoObj->preRender($pdf);
        }

        $line = 10;

        $pdf->setFontExt($_fonts['Helvetica-Bold'], 10);
        $pdf->showBoxed("OWNER STATEMENT (CONT'D)", 180, 760 - $line, 590, 20, 'left', '');
        $line += 10;
        $pdf->showBoxed("{$propertyName}", 180, 760 - $line, 590, 20, 'left', '');
        $line += 10;
        $pdf->showBoxed("Period From: {$periodFrom} To: {$periodTo}", 180, 760 - $line, 590, 22, 'left', '');
        $line += 40;

    }

}

$pdf->setFontExt($_fonts['Helvetica-Bold'], 9);

$netReceipts = $totalNet_receipts + $totalUCNet_receipts;
$gstReceipts = $totalGST_receipts + $totalUCGST_receipts;
$grossReceipts = $totalGross_receipts + $totalUCGross_receipts;
$totalNet_receipts_display = formatting($netReceipts);
$totalGST_receipts_display = formatting($gstReceipts);
$totalGross_receipts_display = formatting($grossReceipts);

$pdf->showBoxed("{$totalNet_receipts_display}", 370, 760 - $line, 100, 21, 'right', '');
$pdf->showBoxed("{$totalGST_receipts_display}", 410, 760 - $line, 100, 21, 'right', '');
$pdf->showBoxed("{$totalGross_receipts_display}", 470, 760 - $line, 100, 21, 'right', '');
$pdf->setFontExt($_fonts['Helvetica'], 7);

$line += 10;


$pdf->moveto(420, 791 - $line);
$pdf->lineto(470, 791 - $line);
$pdf->stroke();

$pdf->moveto(475, 791 - $line);
$pdf->lineto(510, 791 - $line);
$pdf->stroke();

$pdf->moveto(520, 791 - $line);
$pdf->lineto(570, 791 - $line);
$pdf->stroke();

// echo "line 268   -  $line <hr />";

$line += 10;

$pdf->moveto(420, 791 - $line);
$pdf->lineto(470, 791 - $line);
$pdf->stroke();

$pdf->moveto(475, 791 - $line);
$pdf->lineto(510, 791 - $line);
$pdf->stroke();

$pdf->moveto(520, 791 - $line);
$pdf->lineto(570, 791 - $line);
$pdf->stroke();



if ($line > 750) {// 715
    $pdf->setFontExt($_fonts['Helvetica'], 7);
    $pdf->showBoxed("Page {$page}", 495, 0, 75, 20, 'right', '');

    $traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'shortOwnersReport', A4_PORTRAIT);
    $traccFooter->prerender($pdf);

    $pdf->end_page_ext('');
    $page++;

    $pdf->begin_page_ext(595, 842, '');
    if ($logo) {
        $logoObj = new ClientLogo($logoPath);
        $logoObj->preRender($pdf);
    }

    $line = 10;

    $pdf->setFontExt($_fonts['Helvetica-Bold'], 10);
    $pdf->showBoxed("OWNER STATEMENT (CONT'D)", 180, 760 - $line, 590, 20, 'left', '');
    $line += 10;
    $pdf->showBoxed("{$propertyName}", 180, 760 - $line, 590, 20, 'left', '');
    $line += 10;
    $pdf->showBoxed("Period From: {$periodFrom} To: {$periodTo}", 180, 760 - $line, 590, 22, 'left', '');
    $line += 40;

}

// ###################################################################################################################
// ###################################################################################################################
// ###############################################PAYMENTS ###########################################################
// ###############################################PAYMENTS ###########################################################
// ###############################################PAYMENTS ###########################################################
// ###################################################################################################################


$pdf->setFontExt($_fonts['Helvetica-Bold'], 10);
$pdf->showBoxed('Payments', 20, 760 - $line, 590, 20, 'left', '');
$line += 10;
$pdf->setFontExt($_fonts['Helvetica'], 10);
$pdf->showBoxed('Supplier', 20, 760 - $line, 590, 20, 'left', '');
$pdf->showBoxed('Description', 170, 760 - $line, 590, 20, 'left', '');
$pdf->showBoxed('Invoice', 275, 760 - $line, 90, 20, 'right', '');
$pdf->showBoxed('Paid', 318, 760 - $line, 90, 20, 'right', '');
$pdf->showBoxed('Net', 360, 760 - $line, 100, 20, 'right', '');
$pdf->showBoxed($_SESSION['country_default']['tax_label'], 410, 760 - $line, 100, 20, 'right', '');
$pdf->showBoxed('Total', 460, 760 - $line, 100, 20, 'right', '');

$pdf->moveto(20, 768 - $line);
$pdf->lineto(565, 768 - $line);
$pdf->stroke();

$line += 30;


$batchnr_result = getPayBatchNrs($propertyID, $periodFrom, $periodTo);




$i = 0;

$totalPaymentsTax = 0;

foreach ($batchnr_result as $thisLine) {

    $batch = $thisLine['pmxc_t_batch'];
    $linen = $thisLine['pmxc_t_line'];
    $alloc_dt = $thisLine['pmxc_alloc_dt'];
    $alloc_amt = $thisLine['pmxc_alloc_amt'];
    $alloc_tax = $thisLine['pmxc_tax_amt'];


    $i++;

    $details_result = getPaymentAccountDetails($batch, $linen, $propertyID);

    $tdescription   = $details_result['description'];

    $date_from  = $details_result['spare_date_1'];
    $date_to   = $details_result['spare_date_2'];

    $trans_date     = $alloc_dt;
    $aptr_tax_amt  = $alloc_tax;

    $supplier_code = $details_result['creditor_code'];

    $payee_name =  $details_result['creditor_name'];
    $payee_name = limit_name($payee_name, 35);

    $invoice = $details_result['ref_1'];
    $net_amt = $alloc_amt * (-1) - $aptr_tax_amt;

    $date_from = dateFormattingPrintShort($date_from);
    $date_to = dateFormattingPrintShort($date_to);

    $net_amt_display = formatting($net_amt);
    $aptr_tax_amt_display  = formatting($aptr_tax_amt);
    $gross_amt = $alloc_amt * (-1);
    $gross_amt_display = formatting($gross_amt);

    $totalPaymentsTax += $aptr_tax_amt;

    $totalNet_payments += $net_amt;
    $totalGST_payments += $aptr_tax_amt;
    $totalGross_payments += $gross_amt;


    $tdescription = limit_name(character_special($tdescription), 38); // 38


    $pdf->setFontExt($_fonts['Helvetica'], 8);
    $pdf->showBoxed("{$payee_name}", 20, 760 - $line, 150, 30, 'left', '');
    $pdf->showBoxed("{$tdescription}", 170, 760 - $line, 300, 30, 'left', ''); // 170
    $pdf->showBoxed("{$invoice}", 320, 760 - $line, 50, 30, 'right', ''); // 90
    $pdf->showBoxed("{$trans_date}", 330, 760 - $line, 90, 30, 'right', '');
    $pdf->showBoxed("{$net_amt_display}", 370, 760 - $line, 100, 30, 'right', '');
    $pdf->showBoxed("{$aptr_tax_amt_display}", 410, 760 - $line, 100, 30, 'right', '');
    $pdf->showBoxed("{$gross_amt_display}", 470, 760 - $line, 100, 30, 'right', '');


    $line += 10;

    if ($line > 750) {// 715

        $pdf->setFontExt($_fonts['Helvetica'], 7);
        $pdf->showBoxed("Page {$page}", 495, 0, 75, 20, 'right', '');

        $traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'shortOwnersReport', A4_PORTRAIT);
        $traccFooter->prerender($pdf);

        $pdf->end_page_ext('');
        $page++;

        $pdf->begin_page_ext(595, 842, '');
        if ($logo) {
            $logoObj = new ClientLogo($logoPath);
            $logoObj->preRender($pdf);
        }

        $line = 10;

        $pdf->setFontExt($_fonts['Helvetica-Bold'], 10);
        $pdf->showBoxed("OWNER STATEMENT (CONT'D)", 180, 760 - $line, 590, 20, 'left', '');
        $line += 10;
        $pdf->showBoxed("{$propertyName}", 180, 760 - $line, 590, 20, 'left', '');
        $line += 10;
        $pdf->showBoxed("Period From: {$periodFrom} To: {$periodTo}", 180, 760 - $line, 590, 22, 'left', '');
        $line += 40;

    }

}	// batch nr


// echo"totalPaymentsTax - $totalPaymentsTax <hr />";

$line += 5;


if ($line > 750) {// 600
    $pdf->setFontExt($_fonts['Helvetica'], 7);
    $pdf->showBoxed("Page {$page}", 495, 0, 75, 20, 'right', '');

    $traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'shortOwnersReport', A4_PORTRAIT);
    $traccFooter->prerender($pdf);

    $pdf->end_page_ext('');
    $page++;

    $pdf->begin_page_ext(595, 842, '');
    if ($logo) {
        $logoObj = new ClientLogo($logoPath);
        $logoObj->preRender($pdf);
    }

    $line = 10;

    $pdf->setFontExt($_fonts['Helvetica-Bold'], 10);
    $pdf->showBoxed("OWNER STATEMENT (CONT'D)", 180, 760 - $line, 590, 20, 'left', '');
    $line += 10;
    $pdf->showBoxed("{$propertyName}", 180, 760 - $line, 590, 20, 'left', '');
    $line += 10;
    $pdf->showBoxed("Period From: {$periodFrom} To: {$periodTo}", 180, 760 - $line, 590, 22, 'left', '');
    $line += 40;
}


// echo "LINE 489 --- 791- $line<br />";
$pdf->moveto(420, 791 - $line);
$pdf->lineto(470, 791 - $line);
$pdf->stroke();

$pdf->moveto(475, 791 - $line);
$pdf->lineto(510, 791 - $line);
$pdf->stroke();

$pdf->moveto(520, 791 - $line);
$pdf->lineto(570, 791 - $line);
$pdf->stroke();

$pdf->setFontExt($_fonts['Helvetica-Bold'], 9);
$totalNet_payments_display = formatting($totalNet_payments);
$totalGST_payments_display = formatting($totalGST_payments);
$totalGross_payments_display = formatting($totalGross_payments);
$pdf->showBoxed("{$totalNet_payments_display}", 370, 760 - $line, 100, 31, 'right', '');
$pdf->showBoxed("{$totalGST_payments_display}", 410, 760 - $line, 100, 31, 'right', '');
$pdf->showBoxed("{$totalGross_payments_display}", 470, 760 - $line, 100, 31, 'right', '');

$line += 12;

$pdf->moveto(420, 791 - $line);
$pdf->lineto(470, 791 - $line);
$pdf->stroke();

$pdf->moveto(475, 791 - $line);
$pdf->lineto(510, 791 - $line);
$pdf->stroke();

$pdf->moveto(520, 791 - $line);
$pdf->lineto(570, 791 - $line);
$pdf->stroke();

$line += 10;

$cashmov = $grossReceipts - $totalGross_payments;
$cashmov_display = formatting($cashmov);
$pdf->setFontExt($_fonts['Helvetica-Bold'], 9);
$pdf->showBoxed('Cash Movement For The Period', 20, 760 - $line, 590, 30, 'left', '');
$pdf->setFontExt($_fonts['Helvetica-Bold'], 9);
$pdf->showBoxed("{$cashmov_display}", 470, 760 - $line, 100, 30, 'right', '');

$pdf->moveto(520, 791 - $line);
$pdf->lineto(570, 791 - $line);
$pdf->stroke();

$line += 10;



if ($line > 750) {// 715
    $pdf->setFontExt($_fonts['Helvetica'], 7);
    $pdf->showBoxed("Page {$page}", 495, 0, 75, 20, 'right', '');

    $traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'shortOwnersReport', A4_PORTRAIT);
    $traccFooter->prerender($pdf);


    $pdf->end_page_ext('');
    $page++;

    $pdf->begin_page_ext(595, 842, '');
    if ($logo) {
        $logoObj = new ClientLogo($logoPath);
        $logoObj->preRender($pdf);
    }

    $line = 10;

    $pdf->setFontExt($_fonts['Helvetica-Bold'], 10);
    $pdf->showBoxed("OWNER STATEMENT (CONT'D)", 180, 760 - $line, 590, 20, 'left', '');
    $line += 10;
    $pdf->showBoxed("{$propertyName}", 180, 760 - $line, 590, 20, 'left', '');
    $line += 10;
    $pdf->showBoxed("Period From: {$periodFrom} To: {$periodTo}", 180, 760 - $line, 590, 22, 'left', '');
    $line += 40;

}


// OPENING BALANCE
// ####################################################################################
// ################## CASH RECONCILIATION #############################################
// ####################################################################################

$receipts = total_cash_receipts($propertyID, $startFinancialYear, $previousPeriodTo);
$payments = total_cash_payments($propertyID, $startFinancialYear, $previousPeriodTo);

$receiptsA = total_cash_receipts($propertyID, STARTDATE, $financialPeriodToPY);
$paymentsA = total_cash_payments($propertyID, STARTDATE, $financialPeriodToPY);


$diffY = $receipts - $payments;
$diffA = $receiptsA - $paymentsA;
$diff = $diffA + $diffY;

$diff_display = formatting($diff);


$openingbalance = $diff;
$openingbalance_display = formatting($openingbalance);
$pdf->setFontExt($_fonts['Helvetica-Bold'], 9);
$line += 10;
$pdf->showBoxed('Opening cash balance', 20, 760 - $line, 590, 30, 'left', '');
$pdf->setFontExt($_fonts['Helvetica-Bold'], 9);
$pdf->showBoxed("{$openingbalance_display}", 470, 760 - $line, 100, 30, 'right', '');


$subtotal_balance = $cashmov + $openingbalance;

$line += 10;


// OWNER REMITTANCES

$total_pmts_duringM = getPaymentsToOwners($propertyID, $periodFrom, $periodTo); // trim($query_result["amount"]);
$creditorResult = getCreditorDetails($propertyID, $periodFrom, $periodTo);	// $query_result = mssql_query($query);

foreach ($creditorResult as $thisRow) {

    $ind_pmts_duringY_display = formatting($thisRow['pmxc_alloc_amt']);
    $ind_pmxc_s_creditor_display = $thisRow['pmxc_s_creditor'];
    $cred_name = $thisRow['pmco_name'];
    $cred_name = limit_name($cred_name, 35);
    $ind_pmxc_alloc_dt =  $thisRow['pmxc_alloc_dt'];
    $tdescription = $thisRow['description'];

    $pdf->setFontExt($_fonts['Helvetica'], 9);

    $pdf->showBoxed("{$cred_name}", 20, 760 - $line, 590, 20, 'left', '');

    $pdf->showBoxed("{$tdescription}", 170, 760 - $line, 590, 20, 'left', '');
    $pdf->showBoxed("{$ind_pmxc_alloc_dt}", 330, 760 - $line, 90, 20, 'right', '');

    $pdf->setFontExt($_fonts['Helvetica-Bold'], 9);
    $pdf->showBoxed("{$ind_pmts_duringY_display}", 470, 760 - $line, 102, 20, 'right', '');
    $line += 10;



    if ($line > 750) {// 715
        $pdf->setFontExt($_fonts['Helvetica'], 7);
        $pdf->showBoxed("Page {$page}", 495, 0, 75, 20, 'right', '');

        $traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'shortOwnersReport', A4_PORTRAIT);
        $traccFooter->prerender($pdf);

        $pdf->end_page_ext('');
        $page++;

        $pdf->begin_page_ext(595, 842, '');
        if ($logo) {
            $logoObj = new ClientLogo($logoPath);
            $logoObj->preRender($pdf);
        }

        $line = 10;

        $pdf->setFontExt($_fonts['Helvetica-Bold'], 10);
        $pdf->showBoxed("OWNER STATEMENT (CONT'D)", 180, 760 - $line, 590, 20, 'left', '');
        $line += 10;
        $pdf->showBoxed("{$propertyName}", 180, 760 - $line, 590, 20, 'left', '');
        $line += 10;
        $pdf->showBoxed("Period From: {$periodFrom} To: {$periodTo}", 180, 760 - $line, 590, 22, 'left', '');
        $line += 40;
    }

} // end foreach


$line += 10;
$ownerpayments = -($total_pmts_duringM);
$ownerpayments_display = formatting($ownerpayments);
// $pdf->setlinewidth(0.5);

$pdf->moveto(520, 781 - $line);
$pdf->lineto(570, 781 - $line);
$pdf->stroke();

// CLOSING BALANCE
$closing_cashbalance = $subtotal_balance - $ownerpayments;

$closing_cashbalance_display = formatting($closing_cashbalance);
$pdf->setFontExt($_fonts['Helvetica-Bold'], 9);
$pdf->showBoxed('Closing cash balance', 20, 760 - $line, 590, 20, 'left', '');
$pdf->setFontExt($_fonts['Helvetica-Bold'], 9);
$pdf->showBoxed("{$closing_cashbalance_display}", 470, 760 - $line, 100, 22, 'right', '');

// echo "LINE 730 --- 760- $line<br />";

$line += 10;


$pdf->setFontExt($_fonts['Helvetica'], 7);

$pdf->setFontExt($_fonts['Helvetica'], 7);
$pdf->showBoxed("Page {$page}", 495, 0, 75, 20, 'right', '');

$traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'shortOwnersReport', A4_PORTRAIT);
$traccFooter->prerender($pdf);

$line += 10;

$pdf->moveto(520, 791 - $line);
$pdf->lineto(570, 791 - $line);
$pdf->stroke();



$pdf->end_page_ext('');

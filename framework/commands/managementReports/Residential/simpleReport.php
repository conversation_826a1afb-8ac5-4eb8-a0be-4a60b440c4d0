<?php

//
//
// SHORT PROPERTY REPORT - APG SPECIFIC
//
//

include_once __DIR__ . '/residentialReportFunctions.php';
// include_once ('./data/administrator/ar/dbInterface.php');
// include_once ('./commands/ar/invoicePDF.php');

global $_fonts, $reportDescription;
global $clientDB;

$page++;
$totalGross_receipts = 0;
$totalGST_receipts = 0;
$totalNet_receipts = 0;
$totalNet_payments = 0;
$totalUCGST_receipts = 0;
$totalUCGross_receipts = 0;
$totalUCNet_receipts = 0;

$totalGST_payments = 0;
$totalGross_payments = 0;
$mychar  = "\n";

$prop_result = getAgentOwnerCodes($propertyID);
$agent_code = $prop_result['agent_code'];
$owner_code = $prop_result['owner_code'];

$agentDetails = getCompAddress($agent_code);
$ownerDetails = getCompAddress($owner_code);

$agent_name = $agentDetails['company_name'];
$agent_street = $agentDetails['street'];
$agent_city = $agentDetails['city'];
$agent_state = $agentDetails['state'];
$agent_postcode = $agentDetails['postcide'];
$agent_streets = explode("\n", $agent_street);

$owner_name = $ownerDetails['company_name'];
$owner_street = $ownerDetails['street'];
$owner_city = $ownerDetails['city'];
$owner_state = $ownerDetails['state'];
$owner_postcode = $ownerDetails['postcode'];
$owner_streets = explode("\n", $owner_street);

$line = -10;

$pdf->begin_page_ext(595, 842, '');

// generate logo
if ($logo) {
    $logoObj = new ClientLogo($logoPath);
    $logoObj->preRender($pdf);
}

$bigLogoClient = BIGLOGOCLIENT;
$agentData = dbGetAgentDetails();
$agentDetailsNew = new agentDetails($propertyID, true, null, (in_array($clientDB, $bigLogoClient) ? 600 : 650));
$agentDetailsNew->bindAttributesFrom($agentData);
$agentDetailsNew->render($pdf);

$page_header = 'RESIDENTIAL PROPERTY REPORT';

$pdf->setFontExt($_fonts['Helvetica-Bold'], 9);

// ############# LOGO #############################
// include_once 'logo2.php';
// ###############################################


$pdf->setFontExt($_fonts['Helvetica'], 10);

$line += 10;
$line += 10;
$pdf->setFontExt($_fonts['Helvetica'], 10);

$line += 60;

$pdf->showBoxed($owner_name, 80, 760 - $line, 590, 10, 'left', '');
$line += 10;

foreach ($owner_streets as $ostreet) {
    $pdf->showBoxed($ostreet, 80, 760 - $line, 590, 10, 'left', '');
    $line += 10;
}


$pdf->showBoxed("{$owner_city}, {$owner_state} {$owner_postcode}", 80, 760 - $line, 590, 10, 'left', '');
$line += 70;


$pdf->setFontExt($_fonts['Helvetica-Bold'], 10);
$pdf->showBoxed(ucwords(strtolower($reportDescription)), 180, 760 - $line, 590, 10, 'left', '');
$line += 10;
$pdf->showBoxed("{$propertyName}", 180, 760 - $line, 590, 10, 'left', '');
$line += 10;
$pdf->showBoxed("Period From: {$periodFrom} To: {$periodTo}", 180, 760 - $line, 590, 10, 'left', '');
$line += 40;

// /////////////////////

$pdf->setFontExt($_fonts['Helvetica-Bold'], 10);
$pdf->showBoxed('Receipts', 20, 760 - $line, 590, 20, 'left', '');
$line += 10;
$pdf->setFontExt($_fonts['Helvetica-Bold'], 9);
$pdf->showBoxed('Tenant', 20, 760 - $line, 590, 20, 'left', '');
$pdf->showBoxed('Description', 170, 760 - $line, 590, 20, 'left', '');
$pdf->showBoxed('From', 320, 760 - $line, 90, 20, 'left', '');
$pdf->showBoxed('To', 380, 760 - $line, 90, 20, 'left', '');
$pdf->showBoxed('Net', 370, 760 - $line, 100, 20, 'right', '');
$pdf->showBoxed($_SESSION['country_default']['tax_label'], 410, 760 - $line, 100, 20, 'right', '');
$pdf->showBoxed('Total', 470, 760 - $line, 100, 20, 'right', '');

// $pdf->moveto (20, 768 - $line);
// $pdf->lineto (565, 768 - $line);
// $pdf->stroke ();

$line += 20;

$receipts = getSimpleReportReceipts2($propertyID, $periodFrom, $periodTo, false);
if (empty($receipts)) {
    $receipts = getSimpleReportReceipts2($propertyID, $periodFrom, $periodTo, true);
}

$totalReceiptTax = 0;

if ($receipts) {
    foreach ($receipts as $k => $thisRow) {
        $totalReceiptTax += $thisRow['gst'];

        $totalNet_receipts += $thisRow['net'];
        $totalGST_receipts += $thisRow['gst'];
        $totalGross_receipts += $thisRow['gross'];


        $simpleReportListLeases[$thisRow['code']] = $thisRow['code'];

        $reportReceipts[$k]['accountCode'] = $thisRow['code'];
        $reportReceipts[$k]['leaseName'] = $thisRow['leaseName'];
        $reportReceipts[$k]['description'] = $thisRow['description'];
        $reportReceipts[$k]['leaseDescription'] = $thisRow['lease_description'];
        $reportReceipts[$k]['fromDate'] = $thisRow['fromDate'];
        $reportReceipts[$k]['sortDate'] = toDateStamp($thisRow['fromDate']);
        $reportReceipts[$k]['toDate'] = $thisRow['toDate'];
        $reportReceipts[$k]['netAmount'] = formatting($thisRow['net']);
        $reportReceipts[$k]['gstAmount'] = formatting($thisRow['gst']);
        $reportReceipts[$k]['grossAmount'] = formatting($thisRow['gross']);
        //		}

        $previousLease = $thisRow['code'];
        $previousLeaseDescription = $thisRow['lease_description'];
        $previousDescription = $thisRow['description'];
        $previousGross = abs($thisRow['gross']);
        $previousTax = abs($thisRow['gst']);
        $previousNet = abs($thisRow['net']);
        //		$previousReversalDate = $thisRow['reversalDate'];
    }

    unset($receipts, $previousLeaseDescription);

    if ($view->items['zeroReceipting']) {
        $simpleReportLeases = dbLeaseList($propertyID, false);
        $simpleReportTempCount = count($reportReceipts) + 1;
        foreach ($simpleReportLeases as $v) {
            if (! $simpleReportListLeases[$v['leaseID']]) {
                $reportReceipts[$simpleReportTempCount]['accountCode'] = '';
                $reportReceipts[$simpleReportTempCount]['leaseName'] = $v['leaseName'];
                $reportReceipts[$simpleReportTempCount]['description'] = '';
                $reportReceipts[$simpleReportTempCount]['leaseDescription'] = $v['description'];
                $reportReceipts[$simpleReportTempCount]['fromDate'] = '';
                $reportReceipts[$simpleReportTempCount]['sortDate'] = toDateStamp($periodFrom);
                $reportReceipts[$simpleReportTempCount]['toDate'] = '';
                $reportReceipts[$simpleReportTempCount]['netAmount'] = formatting(0);
                $reportReceipts[$simpleReportTempCount]['gstAmount'] = formatting(0);
                $reportReceipts[$simpleReportTempCount]['grossAmount'] = formatting(0);
                $simpleReportTempCount++;
            }
        }
    }

    $reportReceipts = array_orderby($reportReceipts, 'leaseDescription', 'ASC', 'accountCode', 'ASC', 'sortDate', 'ASC');

    foreach ($reportReceipts as $v) {
        if ($line > 715) {
            $pdf->setFontExt($_fonts['Helvetica'], 7);
            $pdf->showBoxed("Page {$page}", 495, 0, 75, 20, 'right', '');

            $traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'simpleOwnersReport', A4_PORTRAIT);
            $traccFooter->prerender($pdf);

            $pdf->end_page_ext('');
            $page++;

            $pdf->begin_page_ext(595, 842, '');
            if ($logo) {
                $logoObj = new ClientLogo($logoPath);
                $logoObj->preRender($pdf);
            }

            $line = 10;

            $pdf->setFontExt($_fonts['Helvetica-Bold'], 10);
            $pdf->showBoxed('Owner Statement (...)', 180, 760 - $line, 590, 20, 'left', '');
            $line += 10;
            $pdf->showBoxed("{$propertyName}", 180, 760 - $line, 590, 20, 'left', '');
            $line += 10;
            $pdf->showBoxed("Period From: {$periodFrom} To: {$periodTo}", 180, 760 - $line, 590, 20, 'left', '');
            $line += 40;

            $pdf->setFontExt($_fonts['Helvetica-Bold'], 10);
            $pdf->showBoxed('Receipts (...)', 20, 760 - $line, 590, 20, 'left', '');
            $line += 10;
            $pdf->setFontExt($_fonts['Helvetica-Bold'], 9);
            $pdf->showBoxed('Tenant', 20, 760 - $line, 590, 20, 'left', '');
            $pdf->showBoxed('Description', 170, 760 - $line, 590, 20, 'left', '');
            $pdf->showBoxed('From', 320, 760 - $line, 90, 20, 'left', '');
            $pdf->showBoxed('To', 380, 760 - $line, 90, 20, 'left', '');
            $pdf->showBoxed('Net', 370, 760 - $line, 100, 20, 'right', '');
            $pdf->showBoxed($_SESSION['country_default']['tax_label'], 410, 760 - $line, 100, 20, 'right', '');
            $pdf->showBoxed('Total', 470, 760 - $line, 100, 20, 'right', '');

            //			$pdf->moveto (20, 768 - $line);
            //			$pdf->lineto (565, 768 - $line);
            //			$pdf->stroke ();

            $line += 20;
        }

        if ($previousLeaseDescription != $v['leaseDescription']) {
            if (! $previousLeaseDescription) {
                $line -= 5;
            }

            $pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
            $pdf->showBoxed($v['leaseDescription'], 20, 760 - $line, 590, 20, 'left', '');
            $line += 10;
        }

        $pdf->setFontExt($_fonts['Helvetica'], 8);
        $pdf->showBoxed($v['leaseName'], 20, 760 - $line + 10, 148, 10, 'left', '');
        $pdf->showBoxed(substr($v['description'], 0, 41), 170, 760 - $line, 590, 20, 'left', '');
        $pdf->showBoxed($v['fromDate'], 320, 760 - $line, 90, 20, 'left', '');
        $pdf->showBoxed($v['toDate'], 380, 760 - $line, 90, 20, 'left', '');
        $pdf->showBoxed($v['netAmount'], 370, 760 - $line, 100, 20, 'right', '');
        $pdf->showBoxed($v['gstAmount'], 410, 760 - $line, 100, 20, 'right', '');
        $pdf->showBoxed($v['grossAmount'], 470, 760 - $line, 100, 20, 'right', '');
        $line += 10;
        $i++;

        $previousLeaseDescription = $v['leaseDescription'];
    }

    unset($reportReceipts);
}

/**
// Note: instead of checking the lease codes from the receipts query, go and grab all unallocated transactions from pmuc_Ualloc_sch table for that property
$unallocatedReceipts = getAccUnallocAmtAll ($propertyID, $periodFrom, $periodTo);

$totalReceiptUnallocTax = 0;
foreach ($unallocatedReceipts as $thisLine)
{
        $lease_code = $thisLine['pmuc_lease'];
        $leaseDescription = $thisLine['lease_description'];
        $nameRaw = $thisLine['lease_name'];
        $name = limit_name ($nameRaw, 35);

        $pmuc_descRaw = $thisLine['pmuc_desc'];
        $pmuc_desc = limit_name($pmuc_descRaw,38);
        // tax will be used later in the tracc short report

        $date = $thisLine['date'];

        $pmuc_gross = $thisLine['gross'];
        $pmuc_gross = $pmuc_gross * (-1);
        $pmuc_grossDisplay = formatting ($pmuc_gross);

        $pmuc_tax = $thisLine['tax'];
        $pmuc_tax = $pmuc_tax * (-1);
        $pmuc_taxDisplay = formatting ($pmuc_tax);

        $pmuc_amt = $thisLine['net'];
        $pmuc_amt = $pmuc_amt * (-1);
        $pmuc_amtDisplay = formatting ($pmuc_amt);

        $totalUCGST_receipts += $pmuc_tax;
        $totalUCGross_receipts += $pmuc_gross;
        $totalUCNet_receipts += $pmuc_amt;

        if ($pmuc_amt != '0.00')
        {
            if ($previousLeaseDescription != $leaseDescription)
            {
                if (!$previousLeaseDescription) $line = $line - 5;
                $pdf->setFontExt ($_fonts['Helvetica-Bold'], 10);
                $pdf->showBoxed ($leaseDescription, 20, 760 - $line, 590, 20, "left", '');
                $line = $line + 10;
            }
            $pmuc_amt = formatting ($pmuc_amt);
            $pdf->setFontExt ($_fonts["Helvetica"], 8);
            $pdf->showBoxed ("$name", 	 20, 760-$line, 590, 20, "left", "");
            // $pdf->showBoxed ("$leaseDescription", 	 112, 760-$line, 590, 20, "left", "");
            $pdf->showBoxed ("$pmuc_desc", 170, 760-$line, 590, 20, "left", "");
            $pdf->showBoxed ("$date", 		 280, 760-$line, 90, 20, "right", "");
            $pdf->showBoxed ("$date", 		 330, 760-$line, 90, 20, "right", "");
            $pdf->showBoxed ("$pmuc_amtDisplay",  370, 760-$line, 100, 20, "right", "");
            $pdf->showBoxed ("$pmuc_taxDisplay",  410, 760-$line, 100, 20, "right", "");
            $pdf->showBoxed ("$pmuc_grossDisplay",  470, 760-$line, 100, 20, "right", "");
            $line = $line +10;
            $previousLeaseDescription = $leaseDescription;
        }
    //}

        if($line > 750)//715
        {
            $pdf->setFontExt($_fonts["Helvetica"], 7);
            $pdf->showBoxed ("Page $page", 495, 0, 75, 20, "right", "");

            $traccFooter = new TraccFooter("assets/clientLogos/tracc_logo_footer.jpg",'simpleOwnersReport', A4_PORTRAIT);
            $traccFooter->prerender($pdf);

            $pdf->end_page_ext("");
            $page++;

            $pdf->begin_page_ext(595,842,"");
            if($logo) { $logoObj = new ClientLogo($logoPath);	$logoObj->preRender($pdf); }
            $line = 10;

            $pdf->setFontExt($_fonts["Helvetica-Bold"], 10);
            $pdf->showBoxed ("OWNER STATEMENT (...)", 180, 760-$line, 590, 20, "left", "");
            $line = $line + 10;
            $pdf->showBoxed ("$propertyName", 180, 760-$line, 590, 20, "left", "");
            $line = $line + 10;
            $pdf->showBoxed ("Period From: $periodFrom To: $periodTo", 180, 760-$line, 590, 22, "left", "");
            $line = $line + 40;

        }

}
 **/
$pdf->setFontExt($_fonts['Helvetica-Bold'], 9);

$netReceipts = $totalNet_receipts + $totalUCNet_receipts;
$gstReceipts = $totalGST_receipts + $totalUCGST_receipts;
$grossReceipts = $totalGross_receipts + $totalUCGross_receipts;
$totalNet_receipts_display = formatting($netReceipts);
$totalGST_receipts_display = formatting($gstReceipts);
$totalGross_receipts_display = formatting($grossReceipts);


$pdf->showBoxed("{$totalNet_receipts_display}", 370, 755 - $line, 100, 21, 'right', '');
$pdf->showBoxed("{$totalGST_receipts_display}", 410, 755 - $line, 100, 21, 'right', '');
$pdf->showBoxed("{$totalGross_receipts_display}", 470, 755 - $line, 100, 21, 'right', '');
$pdf->setFontExt($_fonts['Helvetica'], 7);

$line += 15;


$pdf->moveto(420, 791 - $line);
$pdf->lineto(470, 791 - $line);
$pdf->stroke();

$pdf->moveto(475, 791 - $line);
$pdf->lineto(510, 791 - $line);
$pdf->stroke();

$pdf->moveto(520, 791 - $line);
$pdf->lineto(570, 791 - $line);
$pdf->stroke();
//
// //echo "line 268   -  $line <hr />";
//
$line += 13;

$pdf->moveto(420, 791 - $line);
$pdf->lineto(470, 791 - $line);
$pdf->stroke();

$pdf->moveto(475, 791 - $line);
$pdf->lineto(510, 791 - $line);
$pdf->stroke();

$pdf->moveto(520, 791 - $line);
$pdf->lineto(570, 791 - $line);
$pdf->stroke();



if ($line > 715) { // 715
    $pdf->setFontExt($_fonts['Helvetica'], 7);
    $pdf->showBoxed("Page {$page}", 495, 0, 75, 20, 'right', '');

    $traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'simpleOwnersReport', A4_PORTRAIT);
    $traccFooter->prerender($pdf);

    $pdf->end_page_ext('');
    $page++;

    $pdf->begin_page_ext(595, 842, '');
    if ($logo) {
        $logoObj = new ClientLogo($logoPath);
        $logoObj->preRender($pdf);
    }

    $line = 10;

    $pdf->setFontExt($_fonts['Helvetica-Bold'], 10);
    $pdf->showBoxed('Owner Statement (...)', 180, 760 - $line, 590, 20, 'left', '');
    $line += 10;
    $pdf->showBoxed("{$propertyName}", 180, 760 - $line, 590, 20, 'left', '');
    $line += 10;
    $pdf->showBoxed("Period From: {$periodFrom} To: {$periodTo}", 180, 760 - $line, 590, 22, 'left', '');
    $line += 40;

}

// ###################################################################################################################
// ###################################################################################################################
// ############################################### PAYMENTS ##########################################################
// ############################################### PAYMENTS ##########################################################
// ############################################### PAYMENTS ##########################################################
// ###################################################################################################################

$pdf->setFontExt($_fonts['Helvetica-Bold'], 10);
$pdf->showBoxed('Payments', 20, 760 - $line, 590, 20, 'left', '');
$line += 10;
$pdf->setFontExt($_fonts['Helvetica-Bold'], 9);
$pdf->showBoxed('Supplier', 20, 760 - $line, 590, 20, 'left', '');
$pdf->showBoxed('Description', 170, 760 - $line, 590, 20, 'left', '');
$pdf->showBoxed('Invoice', 320, 760 - $line, 90, 20, 'left', '');
$pdf->showBoxed('Paid', 380, 760 - $line, 90, 20, 'left', '');
$pdf->showBoxed('Net', 370, 760 - $line, 100, 20, 'right', '');
$pdf->showBoxed($_SESSION['country_default']['tax_label'], 410, 760 - $line, 100, 20, 'right', '');
$pdf->showBoxed('Total', 470, 760 - $line, 100, 20, 'right', '');

// $pdf->moveto (20, 768-$line);
// $pdf->lineto (565, 768-$line);
// $pdf->stroke ();

$line += 25;

$batchnr_result = getPayBatchNrs($propertyID, $periodFrom, $periodTo);
$batchByAccountGroup =  [];

foreach ($batchnr_result as $k => $v) {
    $accountGroup = ucwords(strtolower($v['accountGroup']));
    $batchByAccountGroup[$accountGroup][$k] = $v;
}

$i = 0;
$totalPaymentsTax = 0;

foreach ($batchByAccountGroup as $k => $v) {
    $line -= 10;
    $pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
    $pdf->showBoxed($k, 20, 760 - $line, 590, 20, 'left', '');
    $line += 20;

    foreach ($v as $thisLine) {
        $batch = $thisLine['pmxc_t_batch'];
        $linen = $thisLine['pmxc_t_line'];
        // $alloc_dt = $thisLine['pmxc_alloc_dt'];
        $alloc_dt = $thisLine['paymentDate'];
        $alloc_amt = $thisLine['pmxc_alloc_amt'];
        $alloc_tax = $thisLine['pmxc_tax_amt'];

        $i++;

        $details_result = getPaymentAccountDetails($batch, $linen, $propertyID);

        $tdescription   = $details_result['description'];

        $date_from  = $details_result['spare_date_1'];
        $date_to   = $details_result['spare_date_2'];

        $trans_date     = $alloc_dt;
        $aptr_tax_amt  = $alloc_tax;

        $supplier_code = $details_result['creditor_code'];

        $payee_name =  $details_result['creditor_name'];
        $payee_name = limit_name($payee_name, 35);

        $invoice = $details_result['ref_1'];
        $net_amt = $alloc_amt * (-1) - $aptr_tax_amt;

        $date_from = dateFormattingPrintShort($date_from);
        $date_to = dateFormattingPrintShort($date_to);

        $net_amt_display = formatting($net_amt);
        $aptr_tax_amt_display  = formatting($aptr_tax_amt);
        $gross_amt = $alloc_amt * (-1);
        $gross_amt_display = formatting($gross_amt);

        $totalPaymentsTax += $aptr_tax_amt;

        $totalNet_payments += $net_amt;
        $totalGST_payments += $aptr_tax_amt;
        $totalGross_payments += $gross_amt;

        $invoice = substr($invoice, 0, 10);
        $tdescription = limit_name(character_special($tdescription), 38); // 38

        $pdf->setFontExt($_fonts['Helvetica'], 8);
        $pdf->showBoxed("{$payee_name}", 20, 760 - $line + 20, 148, 10, 'left', '');
        $pdf->showBoxed("{$tdescription}", 170, 760 - $line, 300, 30, 'left', ''); // 170
        $pdf->showBoxed("{$invoice}", 320, 760 - $line, 90, 30, 'left', ''); // 90
        $pdf->showBoxed("{$trans_date}", 380, 760 - $line, 90, 30, 'left', '');
        $pdf->showBoxed("{$net_amt_display}", 370, 760 - $line, 100, 30, 'right', '');
        $pdf->showBoxed("{$aptr_tax_amt_display}", 410, 760 - $line, 100, 30, 'right', '');
        $pdf->showBoxed("{$gross_amt_display}", 470, 760 - $line, 100, 30, 'right', '');


        $line += 10;

        if ($line > 715) { // 715
            $pdf->setFontExt($_fonts['Helvetica'], 7);
            $pdf->showBoxed("Page {$page}", 495, 0, 75, 20, 'right', '');

            $traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'simpleOwnersReport', A4_PORTRAIT);
            $traccFooter->prerender($pdf);

            $pdf->end_page_ext('');
            $page++;

            $pdf->begin_page_ext(595, 842, '');
            if ($logo) {
                $logoObj = new ClientLogo($logoPath);
                $logoObj->preRender($pdf);
            }

            $line = 10;

            $pdf->setFontExt($_fonts['Helvetica-Bold'], 10);
            $pdf->showBoxed('Owner Statement (...)', 180, 760 - $line, 590, 20, 'left', '');
            $line += 10;
            $pdf->showBoxed("{$propertyName}", 180, 760 - $line, 590, 20, 'left', '');
            $line += 10;
            $pdf->showBoxed("Period From: {$periodFrom} To: {$periodTo}", 180, 760 - $line, 590, 22, 'left', '');
            $line += 40;

        }
    }
}

$line += 5;


if ($line > 715) {// 600
    $pdf->setFontExt($_fonts['Helvetica'], 7);
    $pdf->showBoxed("Page {$page}", 495, 0, 75, 20, 'right', '');

    $traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'simpleOwnersReport', A4_PORTRAIT);
    $traccFooter->prerender($pdf);

    $pdf->end_page_ext('');
    $page++;

    $pdf->begin_page_ext(595, 842, '');
    if ($logo) {
        $logoObj = new ClientLogo($logoPath);
        $logoObj->preRender($pdf);
    }

    $line = 10;

    $pdf->setFontExt($_fonts['Helvetica-Bold'], 10);
    $pdf->showBoxed('Owner Statement (...)', 180, 760 - $line, 590, 20, 'left', '');
    $line += 10;
    $pdf->showBoxed("{$propertyName}", 180, 760 - $line, 590, 20, 'left', '');
    $line += 10;
    $pdf->showBoxed("Period From: {$periodFrom} To: {$periodTo}", 180, 760 - $line, 590, 22, 'left', '');
    $line += 40;
}


$pdf->moveto(420, 791 - $line);
$pdf->lineto(470, 791 - $line);
$pdf->stroke();

$pdf->moveto(475, 791 - $line);
$pdf->lineto(510, 791 - $line);
$pdf->stroke();

$pdf->moveto(520, 791 - $line);
$pdf->lineto(570, 791 - $line);
$pdf->stroke();

$pdf->setFontExt($_fonts['Helvetica-Bold'], 9);
$totalNet_payments_display = formatting($totalNet_payments);
$totalGST_payments_display = formatting($totalGST_payments);
$totalGross_payments_display = formatting($totalGross_payments);
$pdf->showBoxed("{$totalNet_payments_display}", 370, 760 - $line, 100, 31, 'right', '');
$pdf->showBoxed("{$totalGST_payments_display}", 410, 760 - $line, 100, 31, 'right', '');
$pdf->showBoxed("{$totalGross_payments_display}", 470, 760 - $line, 100, 31, 'right', '');

$line += 12;

$pdf->moveto(420, 791 - $line);
$pdf->lineto(470, 791 - $line);
$pdf->stroke();

$pdf->moveto(475, 791 - $line);
$pdf->lineto(510, 791 - $line);
$pdf->stroke();

$pdf->moveto(520, 791 - $line);
$pdf->lineto(570, 791 - $line);
$pdf->stroke();

$line += 10;

$cashmov = $grossReceipts - $totalGross_payments;
$cashmov_display = formatting($cashmov);
$pdf->setFontExt($_fonts['Helvetica-Bold'], 9);
$pdf->showBoxed('Cash Movement For The Period', 20, 760 - $line, 590, 30, 'left', '');
$pdf->setFontExt($_fonts['Helvetica-Bold'], 9);
$pdf->showBoxed("{$cashmov_display}", 470, 760 - $line, 100, 30, 'right', '');

$pdf->moveto(520, 791 - $line);
$pdf->lineto(570, 791 - $line);
$pdf->stroke();

$line += 10;



if ($line > 715) {// 715
    $pdf->setFontExt($_fonts['Helvetica'], 7);
    $pdf->showBoxed("Page {$page}", 495, 0, 75, 20, 'right', '');

    $traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'simpleOwnersReport', A4_PORTRAIT);
    $traccFooter->prerender($pdf);


    $pdf->end_page_ext('');
    $page++;

    $pdf->begin_page_ext(595, 842, '');
    if ($logo) {
        $logoObj = new ClientLogo($logoPath);
        $logoObj->preRender($pdf);
    }

    $line = 10;

    $pdf->setFontExt($_fonts['Helvetica-Bold'], 10);
    $pdf->showBoxed('Owner Statement (...)', 180, 760 - $line, 590, 20, 'left', '');
    $line += 10;
    $pdf->showBoxed("{$propertyName}", 180, 760 - $line, 590, 20, 'left', '');
    $line += 10;
    $pdf->showBoxed("Period From: {$periodFrom} To: {$periodTo}", 180, 760 - $line, 590, 22, 'left', '');
    $line += 40;

}


// OPENING BALANCE
// ####################################################################################
// ################## CASH RECONCILIATION #############################################
// ####################################################################################

$receipts = total_cash_receipts($propertyID, $startFinancialYear, $previousPeriodTo);
$payments = total_cash_payments($propertyID, $startFinancialYear, $previousPeriodTo);

$receiptsA = total_cash_receipts($propertyID, STARTDATE, $financialPeriodToPY);
$paymentsA = total_cash_payments($propertyID, STARTDATE, $financialPeriodToPY);


$diffY = $receipts - $payments;
$diffA = $receiptsA - $paymentsA;
$diff = $diffA + $diffY;

$diff_display = formatting($diff);


$openingbalance = $diff;
$openingbalance_display = formatting($openingbalance);
$pdf->setFontExt($_fonts['Helvetica-Bold'], 9);
$line += 10;
$pdf->showBoxed('Opening cash balance', 20, 760 - $line, 590, 30, 'left', '');
$pdf->setFontExt($_fonts['Helvetica-Bold'], 9);
$pdf->showBoxed("{$openingbalance_display}", 470, 760 - $line, 100, 30, 'right', '');


$subtotal_balance = $cashmov + $openingbalance;

$line += 10;


// OWNER REMITTANCES

$total_pmts_duringM = getPaymentsToOwners($propertyID, $periodFrom, $periodTo); // trim($query_result["amount"]);
$creditorResult = getCreditorDetails($propertyID, $periodFrom, $periodTo);	// $query_result = mssql_query($query);

foreach ($creditorResult as $thisRow) {

    $ind_pmts_duringY_display = formatting($thisRow['pmxc_alloc_amt']);
    $ind_pmxc_s_creditor_display = $thisRow['pmxc_s_creditor'];
    $cred_name = $thisRow['pmco_name'];
    $cred_name = ellipsitize($cred_name, 38);
    $ind_pmxc_alloc_dt =  $thisRow['pmxc_alloc_dt'];
    $tdescription = $thisRow['description'];

    $pdf->setFontExt($_fonts['Helvetica'], 9);

    $pdf->showBoxed("{$cred_name}", 20, 760 - $line + 10, 148, 10, 'left', '');

    $pdf->showBoxed("{$tdescription}", 170, 760 - $line, 590, 20, 'left', '');
    $pdf->showBoxed("{$ind_pmxc_alloc_dt}", 330, 760 - $line, 90, 20, 'right', '');

    $pdf->setFontExt($_fonts['Helvetica-Bold'], 9);
    $pdf->showBoxed("{$ind_pmts_duringY_display}", 470, 760 - $line, 102, 20, 'right', '');
    $line += 10;



    if ($line > 715) {// 715
        $pdf->setFontExt($_fonts['Helvetica'], 7);
        $pdf->showBoxed("Page {$page}", 495, 0, 75, 20, 'right', '');

        $traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'simpleOwnersReport', A4_PORTRAIT);
        $traccFooter->prerender($pdf);

        $pdf->end_page_ext('');
        $page++;

        $pdf->begin_page_ext(595, 842, '');
        if ($logo) {
            $logoObj = new ClientLogo($logoPath);
            $logoObj->preRender($pdf);
        }

        $line = 10;

        $pdf->setFontExt($_fonts['Helvetica-Bold'], 10);
        $pdf->showBoxed('Owner Statement (...)', 180, 760 - $line, 590, 20, 'left', '');
        $line += 10;
        $pdf->showBoxed("{$propertyName}", 180, 760 - $line, 590, 20, 'left', '');
        $line += 10;
        $pdf->showBoxed("Period From: {$periodFrom} To: {$periodTo}", 180, 760 - $line, 590, 22, 'left', '');
        $line += 40;
    }
} // end foreach

$line += 10;
$ownerpayments = -($total_pmts_duringM);
$ownerpayments_display = formatting($ownerpayments);
// $pdf->setlinewidth (0.5);

$pdf->moveto(520, 781 - $line);
$pdf->lineto(570, 781 - $line);
$pdf->stroke();

// CLOSING BALANCE
$closing_cashbalance = $subtotal_balance - $ownerpayments;

$closing_cashbalance_display = formatting($closing_cashbalance);
$pdf->setFontExt($_fonts['Helvetica-Bold'], 9);
$pdf->showBoxed('Closing cash balance', 20, 760 - $line, 590, 20, 'left', '');
$pdf->setFontExt($_fonts['Helvetica-Bold'], 9);
$pdf->showBoxed("{$closing_cashbalance_display}", 470, 760 - $line, 100, 22, 'right', '');

// echo "LINE 730 --- 760- $line<br />";

$line += 10;


$pdf->setFontExt($_fonts['Helvetica'], 7);

$pdf->setFontExt($_fonts['Helvetica'], 7);
$pdf->showBoxed("Page {$page}", 495, 0, 75, 20, 'right', '');

$traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'simpleOwnersReport', A4_PORTRAIT);
$traccFooter->prerender($pdf);

$line += 10;

$pdf->moveto(520, 791 - $line);
$pdf->lineto(570, 791 - $line);
$pdf->stroke();



$pdf->end_page_ext('');

<?php

include_once __DIR__ . '/residentialReportFunctions.php';
include_once __DIR__ . '/simpleReportFunction.php';

global $_fonts, $reportDescription;
global $clientDB;
$pdfLibTextHeightSmall = 20;
$pdfLibTextHeightMedium = 30;
$page++;
$totalGrossReceipts = 0;
$totalGSTReceipts = 0;
$totalNetReceipts = 0;
$totalNetPayments = 0;
$totalUCGSTReceipts = 0;
$totalUCGrossReceipts = 0;
$totalUCNetReceipts = 0;

$totalGST_payments = 0;
$totalGross_payments = 0;

$line = -10;

$pdf->begin_page_ext(842, 595, '');

if ($logo) {
    generateLogo('landscape');
}

$pdf->setFontExt($_fonts['Helvetica'], 8);
$pdf->show_xy('Owner:', 22, 540);
$pdf->continue_text('Property:');
$pdf->continue_text('Report For:');
$pdf->show_xy($client, 70, 540);
$pdf->continue_text($propertyName . " [{$propertyID}]");
$pdf->continue_text($periodDescription);
$pdf->setFontExt($_fonts['Helvetica-Bold'], 9);
$pdf->showBoxed(ucwords(strtolower($reportDescription)), 271, 525, 300, $pdfLibTextHeightMedium, PDF_TEXT_CENTER_ALIGNMENT, '');
$pdf->setFontExt($_fonts['Helvetica'], 8);
$pdf->showBoxed("Period From: {$periodFrom} To: {$periodTo}", 271, 515, 300, $pdfLibTextHeightMedium, PDF_TEXT_CENTER_ALIGNMENT, '');

$line = 470;
$pdf->setFontExt($_fonts['Helvetica-Bold'], 10);
$pdf->showBoxed('Receipts', 20, $line, 590, $pdfLibTextHeightSmall, PDF_TEXT_LEFT_ALIGNMENT, '');
$line -= 10;
$pdf->setFontExt($_fonts['Helvetica-Bold'], 9);
$pdf->showBoxed('Tenant', 20, $line, 590, $pdfLibTextHeightSmall, PDF_TEXT_LEFT_ALIGNMENT, '');
$pdf->showBoxed('Description', 220, $line, 220, $pdfLibTextHeightSmall, PDF_TEXT_LEFT_ALIGNMENT, '');
$pdf->showBoxed('From', 460, $line, 50, $pdfLibTextHeightSmall, PDF_TEXT_LEFT_ALIGNMENT, '');
$pdf->showBoxed('To', 510, $line, 50, $pdfLibTextHeightSmall, PDF_TEXT_LEFT_ALIGNMENT, '');
$pdf->showBoxed('Net', 560, $line, 80, $pdfLibTextHeightSmall, PDF_TEXT_RIGHT_ALIGNMENT, '');
$pdf->showBoxed($_SESSION['country_default']['tax_label'], 640, $line, 80, $pdfLibTextHeightSmall, PDF_TEXT_RIGHT_ALIGNMENT, '');
$pdf->showBoxed('Total', 720, $line, 80, $pdfLibTextHeightSmall, PDF_TEXT_RIGHT_ALIGNMENT, '');

$line -= 20;

$receipts = getSimpleReportReceipts2($propertyID, $periodFrom, $periodTo, false);
if (empty($receipts)) {
    $receipts = getSimpleReportReceipts2($propertyID, $periodFrom, $periodTo, true);
}

$totalReceiptTax = 0;

if ($receipts) {
    foreach ($receipts as $k => $thisRow) {
        $totalReceiptTax += $thisRow['gst'];
        $totalNetReceipts += $thisRow['net'];
        $totalGSTReceipts += $thisRow['gst'];
        $totalGrossReceipts += $thisRow['gross'];
        $simpleReportListLeases[$thisRow['code']] = $thisRow['code'];

        $reportReceipts[$k]['accountCode'] = $thisRow['code'];
        $reportReceipts[$k]['leaseName'] = $thisRow['leaseName'];
        $reportReceipts[$k]['description'] = $thisRow['description'];
        $reportReceipts[$k]['leaseDescription'] = $thisRow['lease_description'];
        $reportReceipts[$k]['fromDate'] = $thisRow['fromDate'];
        $reportReceipts[$k]['sortDate'] = toDateStamp($thisRow['fromDate']);
        $reportReceipts[$k]['toDate'] = $thisRow['toDate'];
        $reportReceipts[$k]['netAmount'] = formatting($thisRow['net']);
        $reportReceipts[$k]['gstAmount'] = formatting($thisRow['gst']);
        $reportReceipts[$k]['grossAmount'] = formatting($thisRow['gross']);

        $previousLease = $thisRow['code'];
        $previousLeaseDescription = $thisRow['lease_description'];
        $previousDescription = $thisRow['description'];
        $previousGross = abs($thisRow['gross']);
        $previousTax = abs($thisRow['gst']);
        $previousNet = abs($thisRow['net']);
    }

    unset($receipts, $previousLeaseDescription);

    if ($view->items['zeroReceipting']) {
        $simpleReportLeases = dbLeaseList($propertyID, false);
        $simpleReportTempCount = count($reportReceipts) + 1;
        foreach ($simpleReportLeases as $v) {
            if (! $simpleReportListLeases[$v['leaseID']]) {
                $reportReceipts[$simpleReportTempCount]['accountCode'] = '';
                $reportReceipts[$simpleReportTempCount]['leaseName'] = $v['leaseName'];
                $reportReceipts[$simpleReportTempCount]['description'] = '';
                $reportReceipts[$simpleReportTempCount]['leaseDescription'] = $v['description'];
                $reportReceipts[$simpleReportTempCount]['fromDate'] = '';
                $reportReceipts[$simpleReportTempCount]['sortDate'] = toDateStamp($periodFrom);
                $reportReceipts[$simpleReportTempCount]['toDate'] = '';
                $reportReceipts[$simpleReportTempCount]['netAmount'] = formatting(0);
                $reportReceipts[$simpleReportTempCount]['gstAmount'] = formatting(0);
                $reportReceipts[$simpleReportTempCount]['grossAmount'] = formatting(0);
                $simpleReportTempCount++;
            }
        }
    }

    $reportReceipts = array_orderby($reportReceipts, 'leaseDescription', 'ASC', 'accountCode', 'ASC', 'sortDate', 'ASC');

    foreach ($reportReceipts as $v) {
        if (simpleReportPDFOverlapChecker($line)) {
            $pdf->setFontExt($_fonts['Helvetica'], 7);
            $pdf->showBoxed('Page ' . $page, 745, 5, 75, $pdfLibTextHeightMedium, PDF_TEXT_RIGHT_ALIGNMENT, '');

            $traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'simpleOwnersReport', A4_LANDSCAPE);
            $traccFooter->prerender($pdf);

            $pdf->end_page_ext('');
            $page++;

            $pdf->begin_page_ext(842, 595, '');
            if ($logo) {
                generateLogo('landscape');
            }

            $pdf->setFontExt($_fonts['Helvetica-Bold'], 9);
            $pdf->showBoxed(ucwords(strtolower($reportDescription)) . ' (...)', 271, 525, 300, $pdfLibTextHeightMedium, PDF_TEXT_CENTER_ALIGNMENT, '');
            $pdf->setFontExt($_fonts['Helvetica'], 8);
            $pdf->showBoxed("Period From: {$periodFrom} To: {$periodTo}", 271, 515, 300, $pdfLibTextHeightMedium, PDF_TEXT_CENTER_ALIGNMENT, '');
            $line = 470;

            $pdf->setFontExt($_fonts['Helvetica-Bold'], 10);
            $pdf->showBoxed('Receipts (...)', 20, $line, 590, $pdfLibTextHeightSmall, PDF_TEXT_LEFT_ALIGNMENT, '');
            $line -= 10;
            $pdf->setFontExt($_fonts['Helvetica-Bold'], 9);
            $pdf->showBoxed('Tenant', 20, $line, 590, $pdfLibTextHeightSmall, PDF_TEXT_LEFT_ALIGNMENT, '');
            $pdf->showBoxed('Description', 220, $line, 220, $pdfLibTextHeightSmall, PDF_TEXT_LEFT_ALIGNMENT, '');
            $pdf->showBoxed('From', 460, $line, 50, $pdfLibTextHeightSmall, PDF_TEXT_LEFT_ALIGNMENT, '');
            $pdf->showBoxed('To', 510, $line, 50, $pdfLibTextHeightSmall, PDF_TEXT_LEFT_ALIGNMENT, '');
            $pdf->showBoxed('Net', 560, $line, 80, $pdfLibTextHeightSmall, PDF_TEXT_RIGHT_ALIGNMENT, '');
            $pdf->showBoxed($_SESSION['country_default']['tax_label'], 640, $line, 80, $pdfLibTextHeightSmall, PDF_TEXT_RIGHT_ALIGNMENT, '');
            $pdf->showBoxed('Total', 720, $line, 80, $pdfLibTextHeightSmall, PDF_TEXT_RIGHT_ALIGNMENT, '');

            $line -= 20;
        }

        if ($previousLeaseDescription != $v['leaseDescription']) {
            if (! $previousLeaseDescription) {
                $line -= 5;
            }

            $pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
            $pdf->showBoxed($v['leaseDescription'], 20, $line, 590, $pdfLibTextHeightSmall, PDF_TEXT_LEFT_ALIGNMENT, '');
            $line -= 10;
        }

        $pdf->setFontExt($_fonts['Helvetica'], 8);
        $pdf->showBoxed($v['leaseName'], 20, $line, 148, $pdfLibTextHeightSmall, PDF_TEXT_LEFT_ALIGNMENT, '');
        $pdf->showBoxed($v['description'], 220, $line, 220, $pdfLibTextHeightSmall, PDF_TEXT_LEFT_ALIGNMENT, '');
        $pdf->showBoxed($v['fromDate'], 460, $line, 50, $pdfLibTextHeightSmall, PDF_TEXT_LEFT_ALIGNMENT, '');
        $pdf->showBoxed($v['toDate'], 510, $line, 50, $pdfLibTextHeightSmall, PDF_TEXT_LEFT_ALIGNMENT, '');
        $pdf->showBoxed($v['netAmount'], 560, $line, 80, $pdfLibTextHeightSmall, PDF_TEXT_RIGHT_ALIGNMENT, '');
        $pdf->showBoxed($v['gstAmount'], 640, $line, 80, $pdfLibTextHeightSmall, PDF_TEXT_RIGHT_ALIGNMENT, '');
        $pdf->showBoxed($v['grossAmount'], 720, $line, 80, $pdfLibTextHeightSmall, PDF_TEXT_RIGHT_ALIGNMENT, '');
        $line -= 10;

        $previousLeaseDescription = $v['leaseDescription'];
    }

    unset($reportReceipts);
}

$pdf->setFontExt($_fonts['Helvetica-Bold'], 9);
$netReceipts = $totalNetReceipts + $totalUCNetReceipts;
$gstReceipts = $totalGSTReceipts + $totalUCGSTReceipts;
$grossReceipts = $totalGrossReceipts + $totalUCGrossReceipts;
$totalNetReceiptsDisplay = formatting($netReceipts);
$totalGSTReceiptsDisplay = formatting($gstReceipts);
$totalGrossReceiptsDisplay = formatting($grossReceipts);

$line += 15;
$pdf->moveto(580, $line);
$pdf->lineto(640, $line);
$pdf->stroke();

$pdf->moveto(660, $line);
$pdf->lineto(720, $line);
$pdf->stroke();

$pdf->moveto(740, $line);
$pdf->lineto(800, $line);
$pdf->stroke();
$line -= 10;
$pdf->moveto(580, $line);
$pdf->lineto(640, $line);
$pdf->stroke();

$pdf->moveto(660, $line);
$pdf->lineto(720, $line);
$pdf->stroke();

$pdf->moveto(740, $line);
$pdf->lineto(800, $line);
$pdf->stroke();
$line -= 10;
$pdf->showBoxed("{$totalNetReceiptsDisplay}", 560, $line, 80, 21, PDF_TEXT_RIGHT_ALIGNMENT, '');
$pdf->showBoxed("{$totalGSTReceiptsDisplay}", 640, $line, 80, 21, PDF_TEXT_RIGHT_ALIGNMENT, '');
$pdf->showBoxed("{$totalGrossReceiptsDisplay}", 720, $line, 80, 21, PDF_TEXT_RIGHT_ALIGNMENT, '');
$pdf->setFontExt($_fonts['Helvetica'], 7);



$line -= 25;

if (simpleReportPDFOverlapChecker($line)) {
    $pdf->setFontExt($_fonts['Helvetica'], 7);
    $pdf->showBoxed('Page ' . $page, 745, 5, 75, $pdfLibTextHeightMedium, PDF_TEXT_RIGHT_ALIGNMENT, '');

    $traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'simpleOwnersReport', A4_LANDSCAPE);
    $traccFooter->prerender($pdf);

    $pdf->end_page_ext('');
    $page++;

    $pdf->begin_page_ext(842, 595, '');
    if ($logo) {
        generateLogo('landscape');
    }

    $pdf->setFontExt($_fonts['Helvetica-Bold'], 9);
    $pdf->showBoxed(ucwords(strtolower($reportDescription)) . ' (...)', 271, 525, 300, $pdfLibTextHeightMedium, PDF_TEXT_CENTER_ALIGNMENT, '');
    $pdf->setFontExt($_fonts['Helvetica'], 8);
    $pdf->showBoxed("Period From: {$periodFrom} To: {$periodTo}", 271, 515, 300, $pdfLibTextHeightMedium, PDF_TEXT_CENTER_ALIGNMENT, '');
    $line = 470;
}

$pdf->setFontExt($_fonts['Helvetica-Bold'], 10);
$pdf->showBoxed('Payments', 20, $line, 590, $pdfLibTextHeightSmall, PDF_TEXT_LEFT_ALIGNMENT, '');
$line -= 10;
$pdf->setFontExt($_fonts['Helvetica-Bold'], 9);
$pdf->showBoxed('Supplier', 20, $line, 590, $pdfLibTextHeightSmall, PDF_TEXT_LEFT_ALIGNMENT, '');
$pdf->showBoxed('Description', 220, $line, 220, $pdfLibTextHeightSmall, PDF_TEXT_LEFT_ALIGNMENT, '');
$pdf->showBoxed('Invoice', 460, $line, 50, $pdfLibTextHeightSmall, PDF_TEXT_LEFT_ALIGNMENT, '');
$pdf->showBoxed('Paid', 510, $line, 50, $pdfLibTextHeightSmall, PDF_TEXT_LEFT_ALIGNMENT, '');
$pdf->showBoxed('Net', 560, $line, 80, $pdfLibTextHeightSmall, PDF_TEXT_RIGHT_ALIGNMENT, '');
$pdf->showBoxed($_SESSION['country_default']['tax_label'], 640, $line, 80, $pdfLibTextHeightSmall, PDF_TEXT_RIGHT_ALIGNMENT, '');
$pdf->showBoxed('Total', 720, $line, 80, $pdfLibTextHeightSmall, PDF_TEXT_RIGHT_ALIGNMENT, '');

$batchPaymentResult = getPayBatchNrs($propertyID, $periodFrom, $periodTo);
$batchByAccountGroup =  [];

foreach ($batchPaymentResult as $k => $v) {
    $accountGroup = ucwords(strtolower($v['accountGroup']));
    $batchByAccountGroup[$accountGroup][$k] = $v;
}

$line -= 10;

$totalPaymentsTax = 0;

foreach ($batchByAccountGroup as $k => $v) {
    $pdf->setFontExt($_fonts['Helvetica-Bold'], 8);
    $pdf->showBoxed($k, 20, $line, 590, $pdfLibTextHeightSmall, PDF_TEXT_LEFT_ALIGNMENT, '');
    $line -= 20;

    foreach ($v as $thisLine) {
        $batch = $thisLine['pmxc_t_batch'];
        $linen = $thisLine['pmxc_t_line'];
        $transDate = $thisLine['paymentDate'];
        $allocationAmount = $thisLine['pmxc_alloc_amt'];
        $taxAmount = $thisLine['pmxc_tax_amt'];

        $detailsResult = getPaymentAccountDetails($batch, $linen, $propertyID);
        $description   = $detailsResult['description'];
        $dateFrom  = $detailsResult['spare_date_1'];
        $dateTo   = $detailsResult['spare_date_2'];
        $supplierCode = $detailsResult['creditor_code'];
        $payeeName =  $detailsResult['creditor_name'];
        $invoice = $detailsResult['ref_1'];
        $netAmount = $allocationAmount * (-1) - $taxAmount;
        $dateFrom = dateFormattingPrintShort($dateFrom);
        $dateTo = dateFormattingPrintShort($dateTo);
        $netAmountDisplay = formatting($netAmount);
        $taxAmountDisplay  = formatting($taxAmount);
        $grossAmount = $allocationAmount * (-1);
        $grossAmount_display = formatting($grossAmount);
        $totalPaymentsTax += $taxAmount;
        $totalNetPayments += $netAmount;
        $totalGST_payments += $taxAmount;
        $totalGross_payments += $grossAmount;
        $invoice = substr($invoice, 0, 10);

        $pdf->setFontExt($_fonts['Helvetica'], 8);
        $pdf->showBoxed("{$payeeName}", 20, $line, 148, $pdfLibTextHeightMedium, PDF_TEXT_LEFT_ALIGNMENT, '');
        $pdf->showBoxed("{$description}", 220, $line, 220, $pdfLibTextHeightMedium, PDF_TEXT_LEFT_ALIGNMENT, '');
        $pdf->showBoxed("{$invoice}", 460, $line, 50, $pdfLibTextHeightMedium, PDF_TEXT_LEFT_ALIGNMENT, '');
        $pdf->showBoxed("{$transDate}", 510, $line, 50, $pdfLibTextHeightMedium, PDF_TEXT_LEFT_ALIGNMENT, '');
        $pdf->showBoxed("{$netAmountDisplay}", 560, $line, 80, $pdfLibTextHeightMedium, PDF_TEXT_RIGHT_ALIGNMENT, '');
        $pdf->showBoxed("{$taxAmountDisplay}", 640, $line, 80, $pdfLibTextHeightMedium, PDF_TEXT_RIGHT_ALIGNMENT, '');
        $pdf->showBoxed("{$grossAmount_display}", 720, $line, 80, $pdfLibTextHeightMedium, PDF_TEXT_RIGHT_ALIGNMENT, '');


        $line -= 10;

        if (simpleReportPDFOverlapChecker($line)) {
            $pdf->setFontExt($_fonts['Helvetica'], 7);
            $pdf->showBoxed('Page ' . $page, 745, 5, 75, $pdfLibTextHeightMedium, PDF_TEXT_RIGHT_ALIGNMENT, '');

            $traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'simpleOwnersReport', A4_LANDSCAPE);
            $traccFooter->prerender($pdf);

            $pdf->end_page_ext('');
            $page++;

            $pdf->begin_page_ext(842, 595, '');
            if ($logo) {
                generateLogo('landscape');
            }

            $pdf->setFontExt($_fonts['Helvetica-Bold'], 9);
            $pdf->showBoxed(ucwords(strtolower($reportDescription)) . ' (...)', 271, 525, 300, $pdfLibTextHeightMedium, PDF_TEXT_CENTER_ALIGNMENT, '');
            $pdf->setFontExt($_fonts['Helvetica'], 8);
            $pdf->showBoxed("Period From: {$periodFrom} To: {$periodTo}", 271, 515, 300, $pdfLibTextHeightMedium, PDF_TEXT_CENTER_ALIGNMENT, '');
            $line = 470;

            $pdf->setFontExt($_fonts['Helvetica-Bold'], 10);
            $pdf->showBoxed('Payments (...)', 20, $line, 590, $pdfLibTextHeightSmall, PDF_TEXT_LEFT_ALIGNMENT, '');
            $line -= 10;
            $pdf->setFontExt($_fonts['Helvetica-Bold'], 9);
            $pdf->showBoxed('Supplier', 20, $line, 590, $pdfLibTextHeightSmall, PDF_TEXT_LEFT_ALIGNMENT, '');
            $pdf->showBoxed('Description', 220, $line, 220, $pdfLibTextHeightSmall, PDF_TEXT_LEFT_ALIGNMENT, '');
            $pdf->showBoxed('Invoice', 460, $line, 50, $pdfLibTextHeightSmall, PDF_TEXT_LEFT_ALIGNMENT, '');
            $pdf->showBoxed('Paid', 510, $line, 50, $pdfLibTextHeightSmall, PDF_TEXT_LEFT_ALIGNMENT, '');
            $pdf->showBoxed('Net', 560, $line, 80, $pdfLibTextHeightSmall, PDF_TEXT_RIGHT_ALIGNMENT, '');
            $pdf->showBoxed($_SESSION['country_default']['tax_label'], 640, $line, 80, $pdfLibTextHeightSmall, PDF_TEXT_RIGHT_ALIGNMENT, '');
            $pdf->showBoxed('Total', 720, $line, 80, $pdfLibTextHeightSmall, PDF_TEXT_RIGHT_ALIGNMENT, '');
            $line -= 25;
        }
    }
}

$line -= 5;

if (simpleReportPDFOverlapChecker($line)) {
    $pdf->setFontExt($_fonts['Helvetica'], 7);
    $pdf->showBoxed('Page ' . $page, 745, 5, 75, $pdfLibTextHeightMedium, PDF_TEXT_RIGHT_ALIGNMENT, '');

    $traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'simpleOwnersReport', A4_LANDSCAPE);
    $traccFooter->prerender($pdf);

    $pdf->end_page_ext('');
    $page++;

    $pdf->begin_page_ext(842, 595, '');
    if ($logo) {
        generateLogo('landscape');
    }

    $pdf->setFontExt($_fonts['Helvetica-Bold'], 9);
    $pdf->showBoxed(ucwords(strtolower($reportDescription)) . ' (...)', 271, 525, 300, $pdfLibTextHeightMedium, PDF_TEXT_CENTER_ALIGNMENT, '');
    $pdf->setFontExt($_fonts['Helvetica'], 8);
    $pdf->showBoxed("Period From: {$periodFrom} To: {$periodTo}", 271, 515, 300, $pdfLibTextHeightMedium, PDF_TEXT_CENTER_ALIGNMENT, '');
    $line = 470;
}

$line += 20;

$pdf->moveto(580, $line);
$pdf->lineto(640, $line);
$pdf->stroke();

$pdf->moveto(660, $line);
$pdf->lineto(720, $line);
$pdf->stroke();

$pdf->moveto(740, $line);
$pdf->lineto(800, $line);
$pdf->stroke();
$line -= 10;
$pdf->moveto(580, $line);
$pdf->lineto(640, $line);
$pdf->stroke();

$pdf->moveto(660, $line);
$pdf->lineto(720, $line);
$pdf->stroke();

$pdf->moveto(740, $line);
$pdf->lineto(800, $line);
$pdf->stroke();
$line -= 10;
$pdf->setFontExt($_fonts['Helvetica-Bold'], 9);
$totalNetPaymentsDisplay = formatting($totalNetPayments);
$totalGSTPaymentsDisplay = formatting($totalGST_payments);
$totalGrossPaymentsDisplay = formatting($totalGross_payments);
$pdf->showBoxed("{$totalNetPaymentsDisplay}", 560, $line, 80, 21, PDF_TEXT_RIGHT_ALIGNMENT, '');
$pdf->showBoxed("{$totalGSTPaymentsDisplay}", 640, $line, 80, 21, PDF_TEXT_RIGHT_ALIGNMENT, '');
$pdf->showBoxed("{$totalGrossPaymentsDisplay}", 720, $line, 80, 21, PDF_TEXT_RIGHT_ALIGNMENT, '');

$pdf->moveto(740, $line);
$pdf->lineto(800, $line);
$pdf->stroke();
$line -= 20;
$cashMovement = $grossReceipts - $totalGross_payments;
$cashMovement_display = formatting($cashMovement);
$pdf->setFontExt($_fonts['Helvetica-Bold'], 9);
$pdf->showBoxed('Cash Movement For The Period', 20, $line, 590, 21, PDF_TEXT_LEFT_ALIGNMENT, '');
$pdf->setFontExt($_fonts['Helvetica-Bold'], 9);
$pdf->showBoxed("{$cashMovement_display}", 720, $line, 80, 21, PDF_TEXT_RIGHT_ALIGNMENT, '');



$line -= 10;

if (simpleReportPDFOverlapChecker($line)) {
    $pdf->setFontExt($_fonts['Helvetica'], 7);
    $pdf->showBoxed('Page ' . $page, 745, 5, 75, $pdfLibTextHeightMedium, PDF_TEXT_RIGHT_ALIGNMENT, '');

    $traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'simpleOwnersReport', A4_LANDSCAPE);
    $traccFooter->prerender($pdf);

    $pdf->end_page_ext('');
    $page++;

    $pdf->begin_page_ext(842, 595, '');
    if ($logo) {
        generateLogo('landscape');
    }

    $pdf->setFontExt($_fonts['Helvetica-Bold'], 9);
    $pdf->showBoxed(ucwords(strtolower($reportDescription)) . ' (...)', 271, 525, 300, $pdfLibTextHeightMedium, PDF_TEXT_CENTER_ALIGNMENT, '');
    $pdf->setFontExt($_fonts['Helvetica'], 8);
    $pdf->showBoxed("Period From: {$periodFrom} To: {$periodTo}", 271, 515, 300, $pdfLibTextHeightMedium, PDF_TEXT_CENTER_ALIGNMENT, '');
    $line = 470;

}

$receipts = total_cash_receipts($propertyID, $startFinancialYear, $previousPeriodTo);
$payments = total_cash_payments($propertyID, $startFinancialYear, $previousPeriodTo);

$receiptsAnnual = total_cash_receipts($propertyID, STARTDATE, $financialPeriodToPY);
$paymentsAnnual = total_cash_payments($propertyID, STARTDATE, $financialPeriodToPY);

$currentPeriodOpeningBalance = $receipts - $payments;
$annualOpeningBalance = $receiptsAnnual - $paymentsAnnual;


$openingBalance = $annualOpeningBalance + $currentPeriodOpeningBalance;
$openingBalanceDisplay = formatting($openingBalance);
$pdf->setFontExt($_fonts['Helvetica-Bold'], 9);
$line -= 10;
$pdf->showBoxed('Opening cash balance', 20, $line, 590, $pdfLibTextHeightMedium, PDF_TEXT_LEFT_ALIGNMENT, '');
$pdf->setFontExt($_fonts['Helvetica-Bold'], 9);
$pdf->showBoxed("{$openingBalanceDisplay}", 720, $line, 80, $pdfLibTextHeightMedium, PDF_TEXT_RIGHT_ALIGNMENT, '');


$subTotalBalance = $cashMovement + $openingBalance;

$line -= 10;

$totalPayment = getPaymentsToOwners($propertyID, $periodFrom, $periodTo);
$creditorResult = getCreditorDetails($propertyID, $periodFrom, $periodTo);

foreach ($creditorResult as $thisRow) {

    $allocationAmount = formatting($thisRow['pmxc_alloc_amt']);
    $creditorName = $thisRow['pmco_name'];
    $allocationDate =  $thisRow['pmxc_alloc_dt'];
    $description = $thisRow['description'];

    $pdf->setFontExt($_fonts['Helvetica'], 9);

    $pdf->showBoxed("{$creditorName}", 20, $line, 148, $pdfLibTextHeightSmall, PDF_TEXT_LEFT_ALIGNMENT, '');
    $pdf->showBoxed("{$description}", 220, $line, 220, $pdfLibTextHeightSmall, PDF_TEXT_LEFT_ALIGNMENT, '');
    $pdf->showBoxed("{$allocationDate}", 510, $line, 50, $pdfLibTextHeightSmall, PDF_TEXT_RIGHT_ALIGNMENT, '');
    $pdf->setFontExt($_fonts['Helvetica-Bold'], 9);
    $pdf->showBoxed("{$allocationAmount}", 720, $line, 80, $pdfLibTextHeightSmall, PDF_TEXT_RIGHT_ALIGNMENT, '');
    $line -= 10;

    if (simpleReportPDFOverlapChecker($line)) {
        $pdf->setFontExt($_fonts['Helvetica'], 7);
        $pdf->showBoxed('Page ' . $page, 745, 5, 75, $pdfLibTextHeightMedium, PDF_TEXT_RIGHT_ALIGNMENT, '');

        $traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'simpleOwnersReport', A4_LANDSCAPE);
        $traccFooter->prerender($pdf);

        $pdf->end_page_ext('');
        $page++;

        $pdf->begin_page_ext(842, 595, '');
        if ($logo) {
            $logoObj = new ClientLogo($logoPath);
            $logoObj->preRender($pdf);
        }

        $line = 10;

        $pdf->setFontExt($_fonts['Helvetica-Bold'], 10);
        $pdf->showBoxed('Owner Statement (...)', 180, $line, 590, $pdfLibTextHeightSmall, PDF_TEXT_LEFT_ALIGNMENT, '');
        $line -= 10;
        $pdf->showBoxed("{$propertyName}", 180, $line, 590, $pdfLibTextHeightSmall, PDF_TEXT_LEFT_ALIGNMENT, '');
        $line -= 10;
        $pdf->showBoxed("Period From: {$periodFrom} To: {$periodTo}", 180, $line, 590, 22, PDF_TEXT_LEFT_ALIGNMENT, '');
        $line -= 40;
    }
}
$ownerPayments = -($totalPayment);
$ownerPaymentsDisplay = formatting($ownerPayments);

$pdf->moveto(740, $line);
$pdf->lineto(800, $line);
$pdf->stroke();
$line -= 10;
$pdf->moveto(740, $line);
$pdf->lineto(800, $line);
$pdf->stroke();
$line -= 10;
$closingCashBalance = $subTotalBalance - $ownerPayments;
$closingCashBalanceDisplay = formatting($closingCashBalance);
$pdf->setFontExt($_fonts['Helvetica-Bold'], 9);
$pdf->showBoxed('Closing cash balance', 20, $line, 590, 21, PDF_TEXT_LEFT_ALIGNMENT, '');
$pdf->setFontExt($_fonts['Helvetica-Bold'], 9);
$pdf->showBoxed("{$closingCashBalanceDisplay}", 720, $line, 80, 21, PDF_TEXT_RIGHT_ALIGNMENT, '');


$line -= 10;
$pdf->setFontExt($_fonts['Helvetica'], 7);
$pdf->showBoxed('Page ' . $page, 745, 5, 75, $pdfLibTextHeightMedium, PDF_TEXT_RIGHT_ALIGNMENT, '');
$traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'simpleOwnersReport', A4_LANDSCAPE);
$traccFooter->prerender($pdf);
$pdf->end_page_ext('');

/**
 * Checks if the given line position overlaps with the predefined Y-axis overlap position in a PDF.
 *
 * @param  int  $line  The Y-axis position of the line to be checked.
 * @return bool Returns true if the line position is less than the overlap position, otherwise false.
 */
function simpleReportPDFOverlapChecker(int $line): bool
{

    $yAxisOverlapPosition = 100;

    return $line < $yAxisOverlapPosition;
}

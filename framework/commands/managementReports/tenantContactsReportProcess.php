<?php

/**
 * Process information gathered in order to generate a Tenant Contacts Report.
 *
 * @param  $context  array Mandatory. Referenced array containing data needed to continue.
 *
 * <AUTHOR> Reyes
 *
 * @since 2013-01-14
 **/
function tenantContactsReportProcess($context)
{
    global $clientDirectory, $pathPrefix;

    if (! isPostback()) {
        $view = new MasterPage(userViews(), '/managementReports/tenantContactsReportProcess.html');
        $view->setSection($context['module']);
    } else {
        $view = new UserControl(userViews(), '/managementReports/tenantContactsReportProcess.html');
    }
    $view->bindAttributesFrom($_REQUEST);

    $_report['reportName'] = 'Tenant Contacts Report';
    $selectBy = $context['selectBy'];
    if ($selectBy == 'property' and $context['propertyID'] and is_array($context['propertyID'])) {
        $tenant = $tenantID =  [];
        foreach ($context['propertyID'] as $propertyID) {
            $tenant = array_merge($tenant, dbLeaseList($propertyID));
        }
        if ($tenant and is_array($tenant)) {
            foreach ($tenant as $v) {
                $tenantID[] = $v['leaseID'];
            }
        }
        $propertyID = $context['propertyID'];
    } elseif ($selectBy == 'lease' and $context['tenantID'] and is_array($context['tenantID'])) {
        $propertyID = ($context['propertyID']) ? [$context['propertyID']] : null;
        $tenantID = $context['tenantID'];
    }

    $reportResult = dbGetTenantContactsReport($propertyID, $tenantID);
    $count = count($reportResult);

    if ($context[IS_TASK]) {
        $view->bindAttributesFrom($context);
        extract($context);
    } else {
        $view->bindAttributesFrom($context);
        $view->bindAttributesFrom($_REQUEST);
        if ($count > THRESHOLD_TENANTCONTACTSREPORT) {
            $queue = new Queue(TASKTYPE_TENANT_CONTACTS_REPORT);
            $queue->add($_SESSION['clientID'], $_SESSION['un'], 'command=' . $view->items['command'] . '&module=' . $view->items['module'], $_REQUEST);
        }
    }

    if ($count and ($context[IS_TASK] || $count <= THRESHOLD_TENANTCONTACTSREPORT)) {
        $format = $view->items['format'];
        $numberFormat = ($format == FILETYPE_XLS) ? false : true;

        if (! $context[DOC_MASTER]) {
            $logoFile = dbGetClientLogo();
            $logoPath = "assets/clientLogos/{$logoFile}";
            $_filePath =  "{$pathPrefix}{$clientDirectory}/{$format}/" . DOC_TENANTCONTACTSREPORT . '/';
            $_downloadPath =  "{$clientDirectory}/{$format}/" . DOC_TENANTCONTACTSREPORT;
            $file =  DOC_TENANTCONTACTSREPORT . '_' . date('Ymd') . ".{$format}";
            $filePath = $_filePath . $file;
            $downloadPath = "{$_downloadPath}/{$file}";
        }

        foreach ($reportResult as $k => $v) {
            $property = $v['propertyID'] . ' (' . $v['propertyName'] . ')';
            $tenants[$property][$k]['leaseID'] = $v['leaseID'];
            $tenants[$property][$k]['leaseName'] = $v['leaseName'];
            $tenants[$property][$k]['contactPerson'] = $v['contactPerson'];
            $tenants[$property][$k]['contactType'] = $v['contactType'];
            $tenants[$property][$k]['contactDetail'] = $v['contactDetail'];
        }

        switch ($format) {
            case FILETYPE_PDF:
                $report = ($context[DOC_MASTER]) ? new PDFDataReport($context[DOC_MASTER], $logoPath, A4_LANDSCAPE) : new PDFDataReport($filePath, $logoPath, A4_LANDSCAPE);
                $report->multiLine = true;
                $report->printRowLines = true;
                $report->printColumnLines = false;
                $report->printBorders = false;
                $report->cache = false;
                $header = new ReportHeader('Tenant Contacts Report');
                $header->xPos = $report->hMargin;
                $header->yPos = $report->pageHeight - $report->vMargin;
                $report->attachObject('header', $header);
                $footer = new TraccFooter(null, $_report['reportName'], $report->pageSize);
                $report->attachObject('footer', $footer);
                break;
            case FILETYPE_XLS:
                $report = new XLSDataReport($filePath, 'Tenant Contacts Report');
                break;
        }

        if ($format != FILETYPE_SCREEN) {
            $report->addColumn('leaseID', 'Lease Code', 50, 'left');
            $report->addColumn('leaseName', 'Lease Name', 100, 'left');
            $report->addColumn('contactPerson', 'Contact Name', 50, 'left');
            $report->addColumn('contactType', 'Contact Type', 50, 'left');
            $report->addColumn('contactDetail', 'Detail', 100, 'left');
            $report->addSubHeaderItem('title', 0, 200, 'left');

            $header->subTitle = ($selectBy == 'lease') ? 'Multiple Leases' : 'Multiple Properties';
            $report->preparePage();
            $report->renderHeader();
            if ($format == FILETYPE_XLS) {
                $excelTitle['description'] = ($selectBy == 'lease') ? 'Multiple Leases' : 'Multiple Properties';
                $report->renderLine($excelTitle);
            }
        }

        if ($tenants and is_array($tenants)) {
            if ($format == FILETYPE_SCREEN) {
                $view->items['tenants'] = $tenants;
            } else {
                foreach ($tenants as $k => $tenant) {
                    $report->setSubHeaderValue('title', $k);
                    if ($format == FILETYPE_XLS) {
                        $excelTitle['description'] = $k;
                        $report->renderLine($excelTitle);
                    } else {
                        $report->renderSubHeader();
                    }
                    if ($tenant) {
                        $report->renderData($tenant);
                    }
                }
            }
        }

        if ($format != FILETYPE_SCREEN) {
            $report->clean();
            $report->endPage();
            $report->close();
        }
    } elseif (empty($count)) {
        $view->items['statusMessage'] = 'No Tenant Contacts Report to print.';
    }

    if ($count > THRESHOLD_TENANTCONTACTSREPORT) {
        $view->items['statusMessage'] = 'Due to its complexity, this report has been scheduled and will be emailed to you on completion.';
    }

    if (! $context[DOC_MASTER]) {
        if ($context[IS_TASK]) {
            $email = new EmailTemplate('views/emails/basicEmail.html', SYSTEMURL);
            $attachment =  [];
            $attachment[0]['file'] = REPORTPATH . '/' . $pdfDownloadLink;
            $attachment[0]['content_type'] = 'application/pdf';
            sendMail($_SESSION['email'], $_SESSION['first_name'] . ' ' . $_SESSION['last_name'], $email->toString(), 'Report', $attachment);
            logData('Emailed report to ' . $_SESSION['email']);
            $context[TASK_COMPLETE] = true;
        } else {
            $view->items['downloadPath'] = $downloadPath;
        }
        $view->render();
    }
}

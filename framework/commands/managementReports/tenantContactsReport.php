<?php

/**
 * Generate a Tenant Contacts Report.
 *
 * @param  $context  array Mandatory. Referenced array containing data needed in order to proceed.
 *
 * <AUTHOR> Reyes
 *
 * @since 2013-01-14
 **/
function tenantContactsReport(&$context)
{
    // Page Template
    if (! isPostback()) {
        $view = new MasterPage(userViews(), '/managementReports/tenantContactsReport.html');
        $view->setSection($context['module']);
    } else {
        $view = new UserControl(userViews(), '/managementReports/tenantContactsReport.html');
    }
    $view->bindAttributesFrom($_REQUEST);

    // Array Call-In
    $view->items['selectByList'] =
    [
        'lease' => 'Lease',
        'property' => 'Property',
    ];
    $view->items['formatList'] =
    [
        FILETYPE_PDF => 'PDF',
        FILETYPE_XLS => 'Excel Spreadsheet',
        FILETYPE_SCREEN => 'Print to Screen',
    ];
    $view->items['propertyManagerList'] = dbGetPropertyManagers();
    $view->items['propertyManagerList'][] =
    [
        'propertyManagerID' => 'all',
        'propertyManagerName' => 'All Properties',
    ];

    // Default Values
    if (empty($view->items['propertyManager']) and ($view->items['propertyID'] and $view->items['selectBy'] == 'lease')) {
        $view->items['propertyManager'] = dbGetPropertyManager($view->items['propertyID']);
    }
    if (! array_key_exists($view->items['format'], $view->items['formatList'])) {
        $view->items['format'] = FILETYPE_PDF;
    }
    if (empty($view->items['propertyManager'])) {
        $view->items['propertyManager'] = 'all';
    }

    // More Array Call-Ins
    $view->items['propertyGroupedList'] = ($view->items['propertyManager'] == 'all') ? dbPropertyGroupList() : dbPropertyGroupList($view->items['propertyManager']);
    if ($view->items['selectBy'] == 'lease') {
        if ($view->items['action'] == 'switch') {
            $view->items['propertyID'] = null;
        }
        $view->items['tenantList'] = ($view->items['propertyID']) ? dbLeaseList($view->items['propertyID']) : dbFullLeaseList();
    }

    // Filter Values
    if ($view->items['tenantID'] and $view->items['selectBy'] == 'lease') {
        $view->items['tenantID'] = deserializeParameters($view->items['tenantID']);
    }
    if ($view->items['propertyID'] and $view->items['selectBy'] == 'property') {
        $view->items['propertyID'] = deserializeParameters($view->items['propertyID']);
    }

    // Action
    if ($view->items['action'] == 'finalise') {
        // Default Values
        if ($view->items['selectBy'] != 'lease') {
            $view->items['selectBy'] = 'property';
        }
        // Validation
        if (empty($view->items['propertyManager'])) {
            $validationErrors[] = 'You need to select a ' . ucwords(strtolower($_SESSION['country_default']['property_manager'])) . '.';
        } elseif (empty($view->items['propertyID']) and $view->items['selectBy'] == 'property') {
            $validationErrors[] = 'You need to select a property.';
            if (! is_array($view->items['propertyID']) and $view->items['selectBy'] == 'property') {
                $validationErrors[] = 'Invalid Property selection.';
            }
        } elseif (empty($view->items['tenantID']) and $view->items['selectBy'] == 'lease') {
            $validationErrors[] = 'You need to select at least one tenant.';
            if (! is_array($view->items['tenantID'])) {
                $validationErrors[] = 'Invalid Tenant selection.';
            }
        }

        // No Errors
        if (noErrors($validationErrors)) {
            if ($view->items['format'] == FILETYPE_SCREEN) {
                $view->render();
            }
            $context = $view->items;
            executeCommand('tenantContactsReportProcess', 'managementReports');
        }
    }

    if ($view->items['format'] != FILETYPE_SCREEN || empty($view->items['action']) || $validationErrors) {
        // Post Feed
        $view->items['validationErrors'] = $validationErrors;

        // Display
        $view->render();
    }
}

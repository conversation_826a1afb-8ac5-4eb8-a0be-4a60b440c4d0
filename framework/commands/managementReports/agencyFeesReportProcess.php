<?php

function agencyFeesReportProcess(&$context)
{

    global $pathPrefix, $clientDirectory;

    if (! isPostback()) {
        $view = new MasterPage(userViews(), '/managementReports/agencyFeesReportProcess.html');
        $view->setSection($context['module']);
    } else {
        $view = new UserControl(userViews(), '/managementReports/agencyFeesReportProcess.html');
    }

    $validationErrors =  [];

    if ($context[IS_TASK]) {
        $view->bindAttributesFrom($context);
        extract($context);
    } else {
        $view->bindAttributesFrom($_REQUEST);

    }
    $view->items['linked_id'] = deserializeParameters($view->items['linked_id']);
    $db = dbGetLinkedDatabase();
    $dbShortName = [];
    foreach ($db as $rec) {
        $dbShortName[$rec['database_name']] = $rec['short_name'];
    }
    if (count($db ?? []) == 1) {
        $view->items['linked_id'] = [$db[0]['database_name']];
    }
    if ($_SESSION['user_type'] != USER_EXECUTIVEMANAGER) {
        $view->items['linked_id'] = [$clientDB];
    }

    $selectedReports = [];
    $selectedReport = false;
    if ($view->items['reportsCSS']) {
        if (! $view->items['subReport']) {
            $view->items['subReport'] = '0';
        }
        $view->items['dataMgtFees'] = getFeesData($view->items['year'], $view->items['subReport'], $view->items['showInactive'], $view->items['linked_id'], $dbShortName);
        $view->items['subReports'] =
        [
            '0' => 'Management Fees By Property',
            '1' => 'Management Fees By ' . ucwords(strtolower($_SESSION['country_default']['portfolio_manager'])),
            '5' => 'Management Fees By Property & ' . ucwords(strtolower($_SESSION['country_default']['portfolio_manager'])),
            '7' => 'Management Fees By Property & Property Group',
            '2' => 'All Fees',
            '3' => 'Management Fees - Actual vs Budget by Property',
            '4' => 'Management Fees - Actual vs Budget by ' . ucwords(strtolower($_SESSION['country_default']['portfolio_manager'])),
            '6'   => 'All Fees By ' . ucwords(strtolower($_SESSION['country_default']['portfolio_manager'])),
        ];
    } else {
        if ($view->items['subReport']) {
            $selectedReport = true;
        }
        if ($view->items['mgtFeesByProperty'] == 'mgtFeesByProperty') {
            if (! $selectedReport) {
                $view->items['subReport'] = '0';
            }
            $selectedReports[0] = 'Management Fees By Property';
            $selectedReport = true;
        }

        if ($view->items['mgtFeesByPropertyPM'] == 'mgtFeesByPropertyPM') {
            if (! $selectedReport) {
                $view->items['subReport'] = '5';
            }
            $selectedReports[5] = 'Management Fees By Property & ' . ucwords(strtolower($_SESSION['country_default']['portfolio_manager']));
            $selectedReport = true;
        }

        if ($view->items['mgtFeesByPropertyGroup'] == 'mgtFeesByPropertyGroup') {
            if (! $selectedReport) {
                $view->items['subReport'] = '7';
            }
            $selectedReports[7] = 'Management Fees By Property & Property Group';
            $selectedReport = true;
        }

        if ($view->items['mgtFeesByPM'] == 'mgtFeesByPM') {
            if (! $selectedReport) {
                $view->items['subReport'] = '1';
            }
            $selectedReports[1] = 'Management Fees By ' . ucwords(strtolower($_SESSION['country_default']['portfolio_manager']));
            $selectedReport = true;
        }

        if ($view->items['mgtFeesAll'] == 'mgtFeesAll') {
            if (! $selectedReport) {
                $view->items['subReport'] = '2';
            }
            $selectedReports[2] = 'All Fees';
            $selectedReport = true;
        }

        if ($view->items['mgtFeesActlByProp'] == 'mgtFeesActlByProp') {
            if (! $selectedReport) {
                $view->items['subReport'] = '3';
            }
            $selectedReports[3] = 'Management Fees - Actual vs Budget by Property';
            $selectedReport = true;
        }

        if ($view->items['mgtFeesActlByPM'] == 'mgtFeesActlByPM') {
            if (! $selectedReport) {
                $view->items['subReport'] = '4';
            }
            $selectedReports[4] = 'Management Fees - Actual vs Budget by PM';
            $selectedReport = true;
        }

        if ($view->items['allFeesByPM'] == 'allFeesByPM') {
            if (! $selectedReport) {
                $view->items['subReport'] = '6';
            }
            $selectedReports[6] = 'All Fees By ' . ucwords(strtolower($_SESSION['country_default']['portfolio_manager']));
            $selectedReport = true;
        }
        $view->items['subReports'] = $selectedReports;
        $view->items['dataMgtFees'] = getFeesData($view->items['year'], $view->items['subReport'], $view->items['showInactive'], $view->items['linked_id'], $dbShortName);
    }


    $format = $view->items['format'];
    $logoFile = dbGetClientLogo();
    $logoPath = "/assets/clientLogos/{$logoFile}";

    $_filePath = "{$pathPrefix}{$clientDirectory}/{$format}/" . DOC_AGENCYFEES . '/';
    $_downloadPath = "{$clientDirectory}/{$format}/" . DOC_AGENCYFEES;

    $file = 'agencyFeesReport_' . date('YmdHis') . ".{$format}";
    $filePath = $_filePath . $file;
    $downloadPath = "{$_downloadPath}/{$file}";
    $indexSheet = 0;
    if ($view->items['format'] != FILETYPE_SCREEN) {
        if ($view->items['format'] != FILETYPE_PDF) {
            $report = new XLSDataReport($filePath, 'Agency Fees Report');
            $report->enableFormatting = true;
        } else {
            $report = ($context[DOC_MASTER]) ? new PDFDataReport($context[DOC_MASTER], $logoPath, A4_LANDSCAPE) : new PDFDataReport($filePath, $logoPath, A4_LANDSCAPE);
        }
        $report->multiLine = true;
        $report->printRowLines = true;
        $report->printColumnLines = false;
        $report->printBorders = false;
        $report->cache = false;
        $header = new ReportHeader('Agency Fees Report');
        $header->xPos = $report->hMargin;
        $header->yPos = $report->pageHeight - $report->vMargin;
        if ($format == FILETYPE_PDF) {
            $report->attachObject('header', $header);
            $footer = new TraccFooter(null, 'Agency Fees Report', $report->pageSize);
            $report->attachObject('footer', $footer);
        }

        // MANAGEMENT FEES BY PROPERTY
        if ($view->items['reportsCSS'] || $view->items['mgtFeesByProperty']) {
            if ($view->items['format'] == FILETYPE_XLS) {
                $report->setSheetDetails($indexSheet, 'Mgt Fees By Property');
                $report->line = 1;
                $indexSheet++;
            }
            $header->subTitle = 'Management Fees By Property - ' . $view->items['year'];
            $report->resetColumns();
            $report->addColumn('label', 'Property', 160, 'left', null);
            $report->addColumn('julyAmount', 'July', 45, 'right', '#,##0.00_);(#,##0.00)');
            $report->addColumn('augustAmount', 'August', 45, 'right', '#,##0.00_);(#,##0.00)');
            $report->addColumn('septemberAmount', 'September', 45, 'right', '#,##0.00_);(#,##0.00)');
            $report->addColumn('octoberAmount', 'October', 45, 'right', '#,##0.00_);(#,##0.00)');
            $report->addColumn('novemberAmount', 'November', 45, 'right', '#,##0.00_);(#,##0.00)');
            $report->addColumn('decemberAmount', 'December', 45, 'right', '#,##0.00_);(#,##0.00)');
            $report->addColumn('januaryAmount', 'January', 45, 'right', '#,##0.00_);(#,##0.00)');
            $report->addColumn('februaryAmount', 'February', 45, 'right', '#,##0.00_);(#,##0.00)');
            $report->addColumn('marchAmount', 'March', 45, 'right', '#,##0.00_);(#,##0.00)');
            $report->addColumn('aprilAmount', 'April', 45, 'right', '#,##0.00_);(#,##0.00)');
            $report->addColumn('mayAmount', 'May', 45, 'right', '#,##0.00_);(#,##0.00)');
            $report->addColumn('juneAmount', 'June', 45, 'right', '#,##0.00_);(#,##0.00)');
            $report->addColumn('totalAmount', 'YTD', 45, 'right', '#,##0.00_);(#,##0.00)');
            $report->preparePage();
            $report->renderHeader();

            $feesData = getFeesData($view->items['year'], 0, $view->items['showInactive'], $view->items['linked_id'], $dbShortName);
            $feesDataTotal = [];
            $porfolio = '';
            foreach ($feesData as $row) {
                $feesDataTotal['julyAmount'] += $row['julyAmount'];
                $feesDataTotal['augustAmount'] += $row['augustAmount'];
                $feesDataTotal['septemberAmount'] += $row['septemberAmount'];
                $feesDataTotal['octoberAmount'] += $row['octoberAmount'];
                $feesDataTotal['novemberAmount'] += $row['novemberAmount'];
                $feesDataTotal['decemberAmount'] += $row['decemberAmount'];
                $feesDataTotal['januaryAmount'] += $row['januaryAmount'];
                $feesDataTotal['februaryAmount'] += $row['februaryAmount'];
                $feesDataTotal['marchAmount'] += $row['marchAmount'];
                $feesDataTotal['aprilAmount'] += $row['aprilAmount'];
                $feesDataTotal['mayAmount'] += $row['mayAmount'];
                $feesDataTotal['juneAmount'] += $row['juneAmount'];
                $feesDataTotal['totalAmount'] += $row['totalAmount'];
                if ($view->items['format'] == FILETYPE_PDF) {
                    $row['julyAmount'] = toMoney($row['julyAmount'], '');
                    $row['augustAmount'] = toMoney($row['augustAmount'], '');
                    $row['septemberAmount'] = toMoney($row['septemberAmount'], '');
                    $row['octoberAmount'] = toMoney($row['octoberAmount'], '');
                    $row['novemberAmount'] = toMoney($row['novemberAmount'], '');
                    $row['decemberAmount'] = toMoney($row['decemberAmount'], '');
                    $row['januaryAmount'] = toMoney($row['januaryAmount'], '');
                    $row['februaryAmount'] = toMoney($row['februaryAmount'], '');
                    $row['marchAmount'] = toMoney($row['marchAmount'], '');
                    $row['aprilAmount'] = toMoney($row['aprilAmount'], '');
                    $row['mayAmount'] = toMoney($row['mayAmount'], '');
                    $row['juneAmount'] = toMoney($row['juneAmount'], '');
                    $row['totalAmount'] = toMoney($row['totalAmount'], '');
                }
                $report->renderLine_custom($row);
            }
            if ($view->items['format'] == FILETYPE_PDF) {
                $feesDataTotal['julyAmount'] = toMoney($feesDataTotal['julyAmount'], '');
                $feesDataTotal['augustAmount'] = toMoney($feesDataTotal['augustAmount'], '');
                $feesDataTotal['septemberAmount'] = toMoney($feesDataTotal['septemberAmount'], '');
                $feesDataTotal['octoberAmount'] = toMoney($feesDataTotal['octoberAmount'], '');
                $feesDataTotal['novemberAmount'] = toMoney($feesDataTotal['novemberAmount'], '');
                $feesDataTotal['decemberAmount'] = toMoney($feesDataTotal['decemberAmount'], '');
                $feesDataTotal['januaryAmount'] = toMoney($feesDataTotal['januaryAmount'], '');
                $feesDataTotal['februaryAmount'] = toMoney($feesDataTotal['februaryAmount'], '');
                $feesDataTotal['marchAmount'] = toMoney($feesDataTotal['marchAmount'], '');
                $feesDataTotal['aprilAmount'] = toMoney($feesDataTotal['aprilAmount'], '');
                $feesDataTotal['mayAmount'] = toMoney($feesDataTotal['mayAmount'], '');
                $feesDataTotal['juneAmount'] = toMoney($feesDataTotal['juneAmount'], '');
                $feesDataTotal['totalAmount'] = toMoney($feesDataTotal['totalAmount'], '');
                $report->renderSubTotal($feesDataTotal);
            } else {
                $report->renderTotal($feesDataTotal);
            }
            if ($view->items['showInactive']) {
                $z['label'] = '* Inactive Property';
                $z['italic'] = true;
                $z['headerStyle'] =
                [
                    'font' => ['italic' => true],
                ];
                $report->renderLine_custom($z);
            }
            $report->clean();
            $report->endPage();
        }
        // MANAGEMENT FEES BY PM
        if ($view->items['reportsCSS'] || $view->items['mgtFeesByPM']) {
            if ($view->items['format'] == FILETYPE_XLS) {
                $report->setSheetDetails($indexSheet, 'Mgt Fees By ' . ucwords(strtolower($_SESSION['country_default']['portfolio_manager'])));
                $report->line = 1;
                $indexSheet++;
            } elseif ($view->items['format'] == FILETYPE_PDF) {
                $footer->footerNote = '';
                $report->attachObject('footer', $footer);
            }
            $header->subTitle = 'Management Fees By ' . ucwords(strtolower($_SESSION['country_default']['portfolio_manager'])) . ' - ' . $view->items['year'];
            $report->resetColumns();
            $report->addColumn('label', ucwords(strtolower($_SESSION['country_default']['portfolio_manager'])), 160, 'left', null);
            $report->addColumn('julyAmount', 'July', 45, 'right', '#,##0.00_);(#,##0.00)');
            $report->addColumn('augustAmount', 'August', 45, 'right', '#,##0.00_);(#,##0.00)');
            $report->addColumn('septemberAmount', 'September', 45, 'right', '#,##0.00_);(#,##0.00)');
            $report->addColumn('octoberAmount', 'October', 45, 'right', '#,##0.00_);(#,##0.00)');
            $report->addColumn('novemberAmount', 'November', 45, 'right', '#,##0.00_);(#,##0.00)');
            $report->addColumn('decemberAmount', 'December', 45, 'right', '#,##0.00_);(#,##0.00)');
            $report->addColumn('januaryAmount', 'January', 45, 'right', '#,##0.00_);(#,##0.00)');
            $report->addColumn('februaryAmount', 'February', 45, 'right', '#,##0.00_);(#,##0.00)');
            $report->addColumn('marchAmount', 'March', 45, 'right', '#,##0.00_);(#,##0.00)');
            $report->addColumn('aprilAmount', 'April', 45, 'right', '#,##0.00_);(#,##0.00)');
            $report->addColumn('mayAmount', 'May', 45, 'right', '#,##0.00_);(#,##0.00)');
            $report->addColumn('juneAmount', 'June', 45, 'right', '#,##0.00_);(#,##0.00)');
            $report->addColumn('totalAmount', 'YTD', 45, 'right', '#,##0.00_);(#,##0.00)');
            $report->preparePage();
            $report->renderHeader();

            $feesData = getFeesData($view->items['year'], 1, $view->items['showInactive'], $view->items['linked_id'], $dbShortName);
            $feesDataTotal = [];
            foreach ($feesData as $row) {
                $feesDataTotal['julyAmount'] += $row['julyAmount'];
                $feesDataTotal['augustAmount'] += $row['augustAmount'];
                $feesDataTotal['septemberAmount'] += $row['septemberAmount'];
                $feesDataTotal['octoberAmount'] += $row['octoberAmount'];
                $feesDataTotal['novemberAmount'] += $row['novemberAmount'];
                $feesDataTotal['decemberAmount'] += $row['decemberAmount'];
                $feesDataTotal['januaryAmount'] += $row['januaryAmount'];
                $feesDataTotal['februaryAmount'] += $row['februaryAmount'];
                $feesDataTotal['marchAmount'] += $row['marchAmount'];
                $feesDataTotal['aprilAmount'] += $row['aprilAmount'];
                $feesDataTotal['mayAmount'] += $row['mayAmount'];
                $feesDataTotal['juneAmount'] += $row['juneAmount'];
                $feesDataTotal['totalAmount'] += $row['totalAmount'];
                if ($view->items['format'] == FILETYPE_PDF) {
                    $row['julyAmount'] = toMoney($row['julyAmount'], '');
                    $row['augustAmount'] = toMoney($row['augustAmount'], '');
                    $row['septemberAmount'] = toMoney($row['septemberAmount'], '');
                    $row['octoberAmount'] = toMoney($row['octoberAmount'], '');
                    $row['novemberAmount'] = toMoney($row['novemberAmount'], '');
                    $row['decemberAmount'] = toMoney($row['decemberAmount'], '');
                    $row['januaryAmount'] = toMoney($row['januaryAmount'], '');
                    $row['februaryAmount'] = toMoney($row['februaryAmount'], '');
                    $row['marchAmount'] = toMoney($row['marchAmount'], '');
                    $row['aprilAmount'] = toMoney($row['aprilAmount'], '');
                    $row['mayAmount'] = toMoney($row['mayAmount'], '');
                    $row['juneAmount'] = toMoney($row['juneAmount'], '');
                    $row['totalAmount'] = toMoney($row['totalAmount'], '');
                }
                $report->renderLine_custom($row);
            }
            if ($view->items['format'] == FILETYPE_PDF) {
                $feesDataTotal['julyAmount'] = toMoney($feesDataTotal['julyAmount'], '');
                $feesDataTotal['augustAmount'] = toMoney($feesDataTotal['augustAmount'], '');
                $feesDataTotal['septemberAmount'] = toMoney($feesDataTotal['septemberAmount'], '');
                $feesDataTotal['octoberAmount'] = toMoney($feesDataTotal['octoberAmount'], '');
                $feesDataTotal['novemberAmount'] = toMoney($feesDataTotal['novemberAmount'], '');
                $feesDataTotal['decemberAmount'] = toMoney($feesDataTotal['decemberAmount'], '');
                $feesDataTotal['januaryAmount'] = toMoney($feesDataTotal['januaryAmount'], '');
                $feesDataTotal['februaryAmount'] = toMoney($feesDataTotal['februaryAmount'], '');
                $feesDataTotal['marchAmount'] = toMoney($feesDataTotal['marchAmount'], '');
                $feesDataTotal['aprilAmount'] = toMoney($feesDataTotal['aprilAmount'], '');
                $feesDataTotal['mayAmount'] = toMoney($feesDataTotal['mayAmount'], '');
                $feesDataTotal['juneAmount'] = toMoney($feesDataTotal['juneAmount'], '');
                $feesDataTotal['totalAmount'] = toMoney($feesDataTotal['totalAmount'], '');
                $report->renderSubTotal($feesDataTotal);
            } else {
                $report->renderTotal($feesDataTotal);
            }
            $report->clean();
            $report->endPage();
        }

        // MANAGEMENT FEES BY PROPERTY & PM
        if ($view->items['reportsCSS'] || $view->items['mgtFeesByPropertyPM']) {
            if ($view->items['format'] == FILETYPE_XLS) {
                $report->setSheetDetails($indexSheet, 'Mgt Fees By Property & ' . ucwords(strtolower($_SESSION['country_default']['portfolio_manager'])));
                $report->line = 1;
                $indexSheet++;
            }
            $header->subTitle = 'Management Fees By Property & ' . ucwords(strtolower($_SESSION['country_default']['portfolio_manager'])) . ' - ' . $view->items['year'];
            $report->resetColumns();
            $report->addColumn('label', 'Property', 160, 'left', null);
            $report->addColumn('julyAmount', 'July', 45, 'right', '#,##0.00_);(#,##0.00)');
            $report->addColumn('augustAmount', 'August', 45, 'right', '#,##0.00_);(#,##0.00)');
            $report->addColumn('septemberAmount', 'September', 45, 'right', '#,##0.00_);(#,##0.00)');
            $report->addColumn('octoberAmount', 'October', 45, 'right', '#,##0.00_);(#,##0.00)');
            $report->addColumn('novemberAmount', 'November', 45, 'right', '#,##0.00_);(#,##0.00)');
            $report->addColumn('decemberAmount', 'December', 45, 'right', '#,##0.00_);(#,##0.00)');
            $report->addColumn('januaryAmount', 'January', 45, 'right', '#,##0.00_);(#,##0.00)');
            $report->addColumn('februaryAmount', 'February', 45, 'right', '#,##0.00_);(#,##0.00)');
            $report->addColumn('marchAmount', 'March', 45, 'right', '#,##0.00_);(#,##0.00)');
            $report->addColumn('aprilAmount', 'April', 45, 'right', '#,##0.00_);(#,##0.00)');
            $report->addColumn('mayAmount', 'May', 45, 'right', '#,##0.00_);(#,##0.00)');
            $report->addColumn('juneAmount', 'June', 45, 'right', '#,##0.00_);(#,##0.00)');
            $report->addColumn('totalAmount', 'YTD', 45, 'right', '#,##0.00_);(#,##0.00)');
            $report->preparePage();
            $report->renderHeader();

            $feesData = getFeesData($view->items['year'], 5, $view->items['showInactive'], $view->items['linked_id'], $dbShortName);
            $feesDataTotal = [];
            $porfolio = '';
            foreach ($feesData as $row) {
                $feesDataTotal['julyAmount'] += $row['julyAmount'];
                $feesDataTotal['augustAmount'] += $row['augustAmount'];
                $feesDataTotal['septemberAmount'] += $row['septemberAmount'];
                $feesDataTotal['octoberAmount'] += $row['octoberAmount'];
                $feesDataTotal['novemberAmount'] += $row['novemberAmount'];
                $feesDataTotal['decemberAmount'] += $row['decemberAmount'];
                $feesDataTotal['januaryAmount'] += $row['januaryAmount'];
                $feesDataTotal['februaryAmount'] += $row['februaryAmount'];
                $feesDataTotal['marchAmount'] += $row['marchAmount'];
                $feesDataTotal['aprilAmount'] += $row['aprilAmount'];
                $feesDataTotal['mayAmount'] += $row['mayAmount'];
                $feesDataTotal['juneAmount'] += $row['juneAmount'];
                $feesDataTotal['totalAmount'] += $row['totalAmount'];

                if ($view->items['format'] == FILETYPE_XLS) {
                    if ($porfolio != $row['portfolio_name'] && $porfolio) {
                        $row2 = [];
                        $row2['totalAmount'] = (array_sum($sub['totalAmount']));
                        $row2['julyAmount'] = (array_sum($sub['julyAmount']));
                        $row2['augustAmount'] = (array_sum($sub['augustAmount']));
                        $row2['septemberAmount'] = (array_sum($sub['septemberAmount']));
                        $row2['octoberAmount'] = (array_sum($sub['octoberAmount']));
                        $row2['novemberAmount'] = (array_sum($sub['novemberAmount']));
                        $row2['decemberAmount'] = (array_sum($sub['decemberAmount']));
                        $row2['januaryAmount'] = (array_sum($sub['januaryAmount']));
                        $row2['februaryAmount'] = (array_sum($sub['februaryAmount']));
                        $row2['marchAmount'] = (array_sum($sub['marchAmount']));
                        $row2['aprilAmount'] = (array_sum($sub['aprilAmount']));
                        $row2['mayAmount'] = (array_sum($sub['mayAmount']));
                        $row2['juneAmount'] = (array_sum($sub['juneAmount']));

                        $report->renderTotal($row2, 'Sub-Total');
                    }

                    if ($porfolio != $row['portfolio_name']) {
                        $row2 = [];
                        $row2['label'] = $row['portfolio_name'];
                        $porfolio = $row['portfolio_name'];
                        $sub = [];
                        $report->renderLine_bold($row2, 0.95);
                    }

                    $sub['totalAmount'][] = $row['totalAmount'];
                    $sub['julyAmount'][] = $row['julyAmount'];
                    $sub['augustAmount'][] = $row['augustAmount'];
                    $sub['septemberAmount'][] = $row['septemberAmount'];
                    $sub['octoberAmount'][] = $row['octoberAmount'];
                    $sub['novemberAmount'][] = $row['novemberAmount'];
                    $sub['decemberAmount'][] = $row['decemberAmount'];
                    $sub['januaryAmount'][] = $row['januaryAmount'];
                    $sub['februaryAmount'][] = $row['februaryAmount'];
                    $sub['marchAmount'][] = $row['marchAmount'];
                    $sub['aprilAmount'][] = $row['aprilAmount'];
                    $sub['mayAmount'][] = $row['mayAmount'];
                    $sub['juneAmount'][] = $row['juneAmount'];
                }

                if ($view->items['format'] == FILETYPE_PDF) {

                    if ($porfolio != $row['portfolio_name'] && $porfolio) {
                        $row2 = [];
                        $row2['label'] = 'Sub-Total';
                        $row2['totalAmount'] = toMoney(array_sum($sub['totalAmount']), '');
                        $row2['julyAmount'] = toMoney(array_sum($sub['julyAmount']), '');
                        $row2['augustAmount'] = toMoney(array_sum($sub['augustAmount']), '');
                        $row2['septemberAmount'] = toMoney(array_sum($sub['septemberAmount']), '');
                        $row2['octoberAmount'] = toMoney(array_sum($sub['octoberAmount']), '');
                        $row2['novemberAmount'] = toMoney(array_sum($sub['novemberAmount']), '');
                        $row2['decemberAmount'] = toMoney(array_sum($sub['decemberAmount']), '');
                        $row2['januaryAmount'] = toMoney(array_sum($sub['januaryAmount']), '');
                        $row2['februaryAmount'] = toMoney(array_sum($sub['februaryAmount']), '');
                        $row2['marchAmount'] = toMoney(array_sum($sub['marchAmount']), '');
                        $row2['aprilAmount'] = toMoney(array_sum($sub['aprilAmount']), '');
                        $row2['mayAmount'] = toMoney(array_sum($sub['mayAmount']), '');
                        $row2['juneAmount'] = toMoney(array_sum($sub['juneAmount']), '');

                        $report->renderSubTotal($row2, 0.8);
                    }

                    if ($porfolio != $row['portfolio_name']) {
                        $row2 = [];
                        $row2['label'] = $row['portfolio_name'];
                        $porfolio = $row['portfolio_name'];
                        $sub = [];
                        $report->renderSubTotal($row2, 0.95);
                    }

                    $sub['totalAmount'][] = $row['totalAmount'];
                    $sub['julyAmount'][] = $row['julyAmount'];
                    $sub['augustAmount'][] = $row['augustAmount'];
                    $sub['septemberAmount'][] = $row['septemberAmount'];
                    $sub['octoberAmount'][] = $row['octoberAmount'];
                    $sub['novemberAmount'][] = $row['novemberAmount'];
                    $sub['decemberAmount'][] = $row['decemberAmount'];
                    $sub['januaryAmount'][] = $row['januaryAmount'];
                    $sub['februaryAmount'][] = $row['februaryAmount'];
                    $sub['marchAmount'][] = $row['marchAmount'];
                    $sub['aprilAmount'][] = $row['aprilAmount'];
                    $sub['mayAmount'][] = $row['mayAmount'];
                    $sub['juneAmount'][] = $row['juneAmount'];

                    $row['julyAmount'] = toMoney($row['julyAmount'], '');
                    $row['augustAmount'] = toMoney($row['augustAmount'], '');
                    $row['septemberAmount'] = toMoney($row['septemberAmount'], '');
                    $row['octoberAmount'] = toMoney($row['octoberAmount'], '');
                    $row['novemberAmount'] = toMoney($row['novemberAmount'], '');
                    $row['decemberAmount'] = toMoney($row['decemberAmount'], '');
                    $row['januaryAmount'] = toMoney($row['januaryAmount'], '');
                    $row['februaryAmount'] = toMoney($row['februaryAmount'], '');
                    $row['marchAmount'] = toMoney($row['marchAmount'], '');
                    $row['aprilAmount'] = toMoney($row['aprilAmount'], '');
                    $row['mayAmount'] = toMoney($row['mayAmount'], '');
                    $row['juneAmount'] = toMoney($row['juneAmount'], '');
                    $row['totalAmount'] = toMoney($row['totalAmount'], '');
                }
                $report->renderLine_custom($row);
            }

            if ($view->items['format'] == FILETYPE_XLS) {
                $row2 = [];
                $row2['totalAmount'] = (array_sum($sub['totalAmount']));
                $row2['julyAmount'] = (array_sum($sub['julyAmount']));
                $row2['augustAmount'] = (array_sum($sub['augustAmount']));
                $row2['septemberAmount'] = (array_sum($sub['septemberAmount']));
                $row2['octoberAmount'] = (array_sum($sub['octoberAmount']));
                $row2['novemberAmount'] = (array_sum($sub['novemberAmount']));
                $row2['decemberAmount'] = (array_sum($sub['decemberAmount']));
                $row2['januaryAmount'] = (array_sum($sub['januaryAmount']));
                $row2['februaryAmount'] = (array_sum($sub['februaryAmount']));
                $row2['marchAmount'] = (array_sum($sub['marchAmount']));
                $row2['aprilAmount'] = (array_sum($sub['aprilAmount']));
                $row2['mayAmount'] = (array_sum($sub['mayAmount']));
                $row2['juneAmount'] = (array_sum($sub['juneAmount']));

                $report->renderTotal($row2, 'Sub-Total');
            }

            if ($view->items['format'] == FILETYPE_PDF) {

                $row2 = [];
                $row2['label'] = 'Sub-Total';
                $row2['totalAmount'] = toMoney(array_sum($sub['totalAmount']), '');
                $row2['julyAmount'] = toMoney(array_sum($sub['julyAmount']), '');
                $row2['augustAmount'] = toMoney(array_sum($sub['augustAmount']), '');
                $row2['septemberAmount'] = toMoney(array_sum($sub['septemberAmount']), '');
                $row2['octoberAmount'] = toMoney(array_sum($sub['octoberAmount']), '');
                $row2['novemberAmount'] = toMoney(array_sum($sub['novemberAmount']), '');
                $row2['decemberAmount'] = toMoney(array_sum($sub['decemberAmount']), '');
                $row2['januaryAmount'] = toMoney(array_sum($sub['januaryAmount']), '');
                $row2['februaryAmount'] = toMoney(array_sum($sub['februaryAmount']), '');
                $row2['marchAmount'] = toMoney(array_sum($sub['marchAmount']), '');
                $row2['aprilAmount'] = toMoney(array_sum($sub['aprilAmount']), '');
                $row2['mayAmount'] = toMoney(array_sum($sub['mayAmount']), '');
                $row2['juneAmount'] = toMoney(array_sum($sub['juneAmount']), '');

                $report->renderSubTotal($row2, 0.8);

                $feesDataTotal['julyAmount'] = toMoney($feesDataTotal['julyAmount'], '');
                $feesDataTotal['augustAmount'] = toMoney($feesDataTotal['augustAmount'], '');
                $feesDataTotal['septemberAmount'] = toMoney($feesDataTotal['septemberAmount'], '');
                $feesDataTotal['octoberAmount'] = toMoney($feesDataTotal['octoberAmount'], '');
                $feesDataTotal['novemberAmount'] = toMoney($feesDataTotal['novemberAmount'], '');
                $feesDataTotal['decemberAmount'] = toMoney($feesDataTotal['decemberAmount'], '');
                $feesDataTotal['januaryAmount'] = toMoney($feesDataTotal['januaryAmount'], '');
                $feesDataTotal['februaryAmount'] = toMoney($feesDataTotal['februaryAmount'], '');
                $feesDataTotal['marchAmount'] = toMoney($feesDataTotal['marchAmount'], '');
                $feesDataTotal['aprilAmount'] = toMoney($feesDataTotal['aprilAmount'], '');
                $feesDataTotal['mayAmount'] = toMoney($feesDataTotal['mayAmount'], '');
                $feesDataTotal['juneAmount'] = toMoney($feesDataTotal['juneAmount'], '');
                $feesDataTotal['totalAmount'] = toMoney($feesDataTotal['totalAmount'], '');
                $report->renderSubTotal($feesDataTotal);
            } else {
                $report->renderTotal($feesDataTotal);
            }

            if ($view->items['showInactive']) {
                $z['label'] = '* Inactive Property';
                $z['italic'] = true;
                $z['headerStyle'] =
                [
                    'font' => ['italic' => true],
                ];
                $report->renderLine_custom($z);
            }

            $report->clean();
            $report->endPage();
        }

        // MANAGEMENT FEES BY PROPERTY & PROPERTY GROUP
        if ($view->items['reportsCSS'] || $view->items['mgtFeesByPropertyGroup']) {
            if ($view->items['format'] == FILETYPE_XLS) {
                $report->setSheetDetails($indexSheet, 'Mgt Fees By Property & Property Group');
                $report->line = 1;
                $indexSheet++;
            }
            $header->subTitle = 'Management Fees By Property & Property Group - ' . $view->items['year'];
            $report->resetColumns();
            $report->addColumn('label', 'Property', 160, 'left', null);
            $report->addColumn('julyAmount', 'July', 45, 'right', '#,##0.00_);(#,##0.00)');
            $report->addColumn('augustAmount', 'August', 45, 'right', '#,##0.00_);(#,##0.00)');
            $report->addColumn('septemberAmount', 'September', 45, 'right', '#,##0.00_);(#,##0.00)');
            $report->addColumn('octoberAmount', 'October', 45, 'right', '#,##0.00_);(#,##0.00)');
            $report->addColumn('novemberAmount', 'November', 45, 'right', '#,##0.00_);(#,##0.00)');
            $report->addColumn('decemberAmount', 'December', 45, 'right', '#,##0.00_);(#,##0.00)');
            $report->addColumn('januaryAmount', 'January', 45, 'right', '#,##0.00_);(#,##0.00)');
            $report->addColumn('februaryAmount', 'February', 45, 'right', '#,##0.00_);(#,##0.00)');
            $report->addColumn('marchAmount', 'March', 45, 'right', '#,##0.00_);(#,##0.00)');
            $report->addColumn('aprilAmount', 'April', 45, 'right', '#,##0.00_);(#,##0.00)');
            $report->addColumn('mayAmount', 'May', 45, 'right', '#,##0.00_);(#,##0.00)');
            $report->addColumn('juneAmount', 'June', 45, 'right', '#,##0.00_);(#,##0.00)');
            $report->addColumn('totalAmount', 'YTD', 45, 'right', '#,##0.00_);(#,##0.00)');
            $report->preparePage();
            $report->renderHeader();

            $feesData = getFeesData($view->items['year'], 7, $view->items['showInactive'], $view->items['linked_id'], $dbShortName);
            $feesDataTotal = [];
            $porfolio = '';
            foreach ($feesData as $row) {
                $feesDataTotal['julyAmount'] += $row['julyAmount'];
                $feesDataTotal['augustAmount'] += $row['augustAmount'];
                $feesDataTotal['septemberAmount'] += $row['septemberAmount'];
                $feesDataTotal['octoberAmount'] += $row['octoberAmount'];
                $feesDataTotal['novemberAmount'] += $row['novemberAmount'];
                $feesDataTotal['decemberAmount'] += $row['decemberAmount'];
                $feesDataTotal['januaryAmount'] += $row['januaryAmount'];
                $feesDataTotal['februaryAmount'] += $row['februaryAmount'];
                $feesDataTotal['marchAmount'] += $row['marchAmount'];
                $feesDataTotal['aprilAmount'] += $row['aprilAmount'];
                $feesDataTotal['mayAmount'] += $row['mayAmount'];
                $feesDataTotal['juneAmount'] += $row['juneAmount'];
                $feesDataTotal['totalAmount'] += $row['totalAmount'];

                if ($view->items['format'] == FILETYPE_XLS) {
                    if ($porfolio != $row['portfolio_name'] && $porfolio) {
                        $row2 = [];
                        $row2['totalAmount'] = (array_sum($sub['totalAmount']));
                        $row2['julyAmount'] = (array_sum($sub['julyAmount']));
                        $row2['augustAmount'] = (array_sum($sub['augustAmount']));
                        $row2['septemberAmount'] = (array_sum($sub['septemberAmount']));
                        $row2['octoberAmount'] = (array_sum($sub['octoberAmount']));
                        $row2['novemberAmount'] = (array_sum($sub['novemberAmount']));
                        $row2['decemberAmount'] = (array_sum($sub['decemberAmount']));
                        $row2['januaryAmount'] = (array_sum($sub['januaryAmount']));
                        $row2['februaryAmount'] = (array_sum($sub['februaryAmount']));
                        $row2['marchAmount'] = (array_sum($sub['marchAmount']));
                        $row2['aprilAmount'] = (array_sum($sub['aprilAmount']));
                        $row2['mayAmount'] = (array_sum($sub['mayAmount']));
                        $row2['juneAmount'] = (array_sum($sub['juneAmount']));

                        $report->renderTotal($row2, 'Sub-Total');
                    }

                    if ($porfolio != $row['portfolio_name']) {
                        $row2 = [];
                        $row2['label'] = $row['portfolio_name'];
                        $porfolio = $row['portfolio_name'];
                        $sub = [];
                        $report->renderLine_bold($row2, 0.95);
                    }

                    $sub['totalAmount'][] = $row['totalAmount'];
                    $sub['julyAmount'][] = $row['julyAmount'];
                    $sub['augustAmount'][] = $row['augustAmount'];
                    $sub['septemberAmount'][] = $row['septemberAmount'];
                    $sub['octoberAmount'][] = $row['octoberAmount'];
                    $sub['novemberAmount'][] = $row['novemberAmount'];
                    $sub['decemberAmount'][] = $row['decemberAmount'];
                    $sub['januaryAmount'][] = $row['januaryAmount'];
                    $sub['februaryAmount'][] = $row['februaryAmount'];
                    $sub['marchAmount'][] = $row['marchAmount'];
                    $sub['aprilAmount'][] = $row['aprilAmount'];
                    $sub['mayAmount'][] = $row['mayAmount'];
                    $sub['juneAmount'][] = $row['juneAmount'];
                }

                if ($view->items['format'] == FILETYPE_PDF) {

                    if ($porfolio != $row['portfolio_name'] && $porfolio) {
                        $row2 = [];
                        $row2['label'] = 'Sub-Total';
                        $row2['totalAmount'] = toMoney(array_sum($sub['totalAmount']), '');
                        $row2['julyAmount'] = toMoney(array_sum($sub['julyAmount']), '');
                        $row2['augustAmount'] = toMoney(array_sum($sub['augustAmount']), '');
                        $row2['septemberAmount'] = toMoney(array_sum($sub['septemberAmount']), '');
                        $row2['octoberAmount'] = toMoney(array_sum($sub['octoberAmount']), '');
                        $row2['novemberAmount'] = toMoney(array_sum($sub['novemberAmount']), '');
                        $row2['decemberAmount'] = toMoney(array_sum($sub['decemberAmount']), '');
                        $row2['januaryAmount'] = toMoney(array_sum($sub['januaryAmount']), '');
                        $row2['februaryAmount'] = toMoney(array_sum($sub['februaryAmount']), '');
                        $row2['marchAmount'] = toMoney(array_sum($sub['marchAmount']), '');
                        $row2['aprilAmount'] = toMoney(array_sum($sub['aprilAmount']), '');
                        $row2['mayAmount'] = toMoney(array_sum($sub['mayAmount']), '');
                        $row2['juneAmount'] = toMoney(array_sum($sub['juneAmount']), '');

                        $report->renderSubTotal($row2, 0.8);
                    }

                    if ($porfolio != $row['portfolio_name']) {
                        $row2 = [];
                        $row2['label'] = $row['portfolio_name'];
                        $porfolio = $row['portfolio_name'];
                        $sub = [];
                        $report->renderSubTotal($row2, 0.95);
                    }

                    $sub['totalAmount'][] = $row['totalAmount'];
                    $sub['julyAmount'][] = $row['julyAmount'];
                    $sub['augustAmount'][] = $row['augustAmount'];
                    $sub['septemberAmount'][] = $row['septemberAmount'];
                    $sub['octoberAmount'][] = $row['octoberAmount'];
                    $sub['novemberAmount'][] = $row['novemberAmount'];
                    $sub['decemberAmount'][] = $row['decemberAmount'];
                    $sub['januaryAmount'][] = $row['januaryAmount'];
                    $sub['februaryAmount'][] = $row['februaryAmount'];
                    $sub['marchAmount'][] = $row['marchAmount'];
                    $sub['aprilAmount'][] = $row['aprilAmount'];
                    $sub['mayAmount'][] = $row['mayAmount'];
                    $sub['juneAmount'][] = $row['juneAmount'];

                    $row['julyAmount'] = toMoney($row['julyAmount'], '');
                    $row['augustAmount'] = toMoney($row['augustAmount'], '');
                    $row['septemberAmount'] = toMoney($row['septemberAmount'], '');
                    $row['octoberAmount'] = toMoney($row['octoberAmount'], '');
                    $row['novemberAmount'] = toMoney($row['novemberAmount'], '');
                    $row['decemberAmount'] = toMoney($row['decemberAmount'], '');
                    $row['januaryAmount'] = toMoney($row['januaryAmount'], '');
                    $row['februaryAmount'] = toMoney($row['februaryAmount'], '');
                    $row['marchAmount'] = toMoney($row['marchAmount'], '');
                    $row['aprilAmount'] = toMoney($row['aprilAmount'], '');
                    $row['mayAmount'] = toMoney($row['mayAmount'], '');
                    $row['juneAmount'] = toMoney($row['juneAmount'], '');
                    $row['totalAmount'] = toMoney($row['totalAmount'], '');
                }
                $report->renderLine_custom($row);
            }

            if ($view->items['format'] == FILETYPE_XLS) {
                $row2 = [];
                $row2['totalAmount'] = (array_sum($sub['totalAmount']));
                $row2['julyAmount'] = (array_sum($sub['julyAmount']));
                $row2['augustAmount'] = (array_sum($sub['augustAmount']));
                $row2['septemberAmount'] = (array_sum($sub['septemberAmount']));
                $row2['octoberAmount'] = (array_sum($sub['octoberAmount']));
                $row2['novemberAmount'] = (array_sum($sub['novemberAmount']));
                $row2['decemberAmount'] = (array_sum($sub['decemberAmount']));
                $row2['januaryAmount'] = (array_sum($sub['januaryAmount']));
                $row2['februaryAmount'] = (array_sum($sub['februaryAmount']));
                $row2['marchAmount'] = (array_sum($sub['marchAmount']));
                $row2['aprilAmount'] = (array_sum($sub['aprilAmount']));
                $row2['mayAmount'] = (array_sum($sub['mayAmount']));
                $row2['juneAmount'] = (array_sum($sub['juneAmount']));

                $report->renderTotal($row2, 'Sub-Total');
            }

            if ($view->items['format'] == FILETYPE_PDF) {

                $row2 = [];
                $row2['label'] = 'Sub-Total';
                $row2['totalAmount'] = toMoney(array_sum($sub['totalAmount']), '');
                $row2['julyAmount'] = toMoney(array_sum($sub['julyAmount']), '');
                $row2['augustAmount'] = toMoney(array_sum($sub['augustAmount']), '');
                $row2['septemberAmount'] = toMoney(array_sum($sub['septemberAmount']), '');
                $row2['octoberAmount'] = toMoney(array_sum($sub['octoberAmount']), '');
                $row2['novemberAmount'] = toMoney(array_sum($sub['novemberAmount']), '');
                $row2['decemberAmount'] = toMoney(array_sum($sub['decemberAmount']), '');
                $row2['januaryAmount'] = toMoney(array_sum($sub['januaryAmount']), '');
                $row2['februaryAmount'] = toMoney(array_sum($sub['februaryAmount']), '');
                $row2['marchAmount'] = toMoney(array_sum($sub['marchAmount']), '');
                $row2['aprilAmount'] = toMoney(array_sum($sub['aprilAmount']), '');
                $row2['mayAmount'] = toMoney(array_sum($sub['mayAmount']), '');
                $row2['juneAmount'] = toMoney(array_sum($sub['juneAmount']), '');

                $report->renderSubTotal($row2, 0.8);

                $feesDataTotal['julyAmount'] = toMoney($feesDataTotal['julyAmount'], '');
                $feesDataTotal['augustAmount'] = toMoney($feesDataTotal['augustAmount'], '');
                $feesDataTotal['septemberAmount'] = toMoney($feesDataTotal['septemberAmount'], '');
                $feesDataTotal['octoberAmount'] = toMoney($feesDataTotal['octoberAmount'], '');
                $feesDataTotal['novemberAmount'] = toMoney($feesDataTotal['novemberAmount'], '');
                $feesDataTotal['decemberAmount'] = toMoney($feesDataTotal['decemberAmount'], '');
                $feesDataTotal['januaryAmount'] = toMoney($feesDataTotal['januaryAmount'], '');
                $feesDataTotal['februaryAmount'] = toMoney($feesDataTotal['februaryAmount'], '');
                $feesDataTotal['marchAmount'] = toMoney($feesDataTotal['marchAmount'], '');
                $feesDataTotal['aprilAmount'] = toMoney($feesDataTotal['aprilAmount'], '');
                $feesDataTotal['mayAmount'] = toMoney($feesDataTotal['mayAmount'], '');
                $feesDataTotal['juneAmount'] = toMoney($feesDataTotal['juneAmount'], '');
                $feesDataTotal['totalAmount'] = toMoney($feesDataTotal['totalAmount'], '');
                $report->renderSubTotal($feesDataTotal);
            } else {
                $report->renderTotal($feesDataTotal);
            }

            if ($view->items['showInactive']) {
                $z['label'] = '* Inactive Property';
                $z['italic'] = true;
                $z['headerStyle'] =
                [
                    'font' => ['italic' => true],
                ];
                $report->renderLine_custom($z);
            }

            $report->clean();
            $report->endPage();
        }

        // ALL FEES
        if ($view->items['reportsCSS'] || $view->items['mgtFeesAll']) {
            if ($view->items['format'] == FILETYPE_XLS) {
                $report->setSheetDetails($indexSheet, 'All Fees');
                $report->line = 1;
                $indexSheet++;
            } elseif ($view->items['format'] == FILETYPE_PDF) {
                $footer->footerNote = '';
                $report->attachObject('footer', $footer);
            }
            $header->subTitle = 'All Fees - ' . $view->items['year'];
            $report->resetColumns();
            $report->addColumn('label', 'Fees', 160, 'left', null);
            $report->addColumn('julyAmount', 'July', 45, 'right', '#,##0.00_);(#,##0.00)');
            $report->addColumn('augustAmount', 'August', 45, 'right', '#,##0.00_);(#,##0.00)');
            $report->addColumn('septemberAmount', 'September', 45, 'right', '#,##0.00_);(#,##0.00)');
            $report->addColumn('octoberAmount', 'October', 45, 'right', '#,##0.00_);(#,##0.00)');
            $report->addColumn('novemberAmount', 'November', 45, 'right', '#,##0.00_);(#,##0.00)');
            $report->addColumn('decemberAmount', 'December', 45, 'right', '#,##0.00_);(#,##0.00)');
            $report->addColumn('januaryAmount', 'January', 45, 'right', '#,##0.00_);(#,##0.00)');
            $report->addColumn('februaryAmount', 'February', 45, 'right', '#,##0.00_);(#,##0.00)');
            $report->addColumn('marchAmount', 'March', 45, 'right', '#,##0.00_);(#,##0.00)');
            $report->addColumn('aprilAmount', 'April', 45, 'right', '#,##0.00_);(#,##0.00)');
            $report->addColumn('mayAmount', 'May', 45, 'right', '#,##0.00_);(#,##0.00)');
            $report->addColumn('juneAmount', 'June', 45, 'right', '#,##0.00_);(#,##0.00)');
            $report->addColumn('totalAmount', 'YTD', 45, 'right', '#,##0.00_);(#,##0.00)');
            $report->preparePage();
            $report->renderHeader();

            $feesData = getFeesData($view->items['year'], 2, $view->items['showInactive'], $view->items['linked_id'], $dbShortName);
            $feesDataTotal = [];
            foreach ($feesData as $row) {
                $feesDataTotal['julyAmount'] += $row['julyAmount'];
                $feesDataTotal['augustAmount'] += $row['augustAmount'];
                $feesDataTotal['septemberAmount'] += $row['septemberAmount'];
                $feesDataTotal['octoberAmount'] += $row['octoberAmount'];
                $feesDataTotal['novemberAmount'] += $row['novemberAmount'];
                $feesDataTotal['decemberAmount'] += $row['decemberAmount'];
                $feesDataTotal['januaryAmount'] += $row['januaryAmount'];
                $feesDataTotal['februaryAmount'] += $row['februaryAmount'];
                $feesDataTotal['marchAmount'] += $row['marchAmount'];
                $feesDataTotal['aprilAmount'] += $row['aprilAmount'];
                $feesDataTotal['mayAmount'] += $row['mayAmount'];
                $feesDataTotal['juneAmount'] += $row['juneAmount'];
                $feesDataTotal['totalAmount'] += $row['totalAmount'];
                if ($view->items['format'] == FILETYPE_PDF) {
                    $row['julyAmount'] = toMoney($row['julyAmount'], '');
                    $row['augustAmount'] = toMoney($row['augustAmount'], '');
                    $row['septemberAmount'] = toMoney($row['septemberAmount'], '');
                    $row['octoberAmount'] = toMoney($row['octoberAmount'], '');
                    $row['novemberAmount'] = toMoney($row['novemberAmount'], '');
                    $row['decemberAmount'] = toMoney($row['decemberAmount'], '');
                    $row['januaryAmount'] = toMoney($row['januaryAmount'], '');
                    $row['februaryAmount'] = toMoney($row['februaryAmount'], '');
                    $row['marchAmount'] = toMoney($row['marchAmount'], '');
                    $row['aprilAmount'] = toMoney($row['aprilAmount'], '');
                    $row['mayAmount'] = toMoney($row['mayAmount'], '');
                    $row['juneAmount'] = toMoney($row['juneAmount'], '');
                    $row['totalAmount'] = toMoney($row['totalAmount'], '');
                }
                $report->renderLine_custom($row);
            }
            if ($view->items['format'] == FILETYPE_PDF) {
                $feesDataTotal['julyAmount'] = toMoney($feesDataTotal['julyAmount'], '');
                $feesDataTotal['augustAmount'] = toMoney($feesDataTotal['augustAmount'], '');
                $feesDataTotal['septemberAmount'] = toMoney($feesDataTotal['septemberAmount'], '');
                $feesDataTotal['octoberAmount'] = toMoney($feesDataTotal['octoberAmount'], '');
                $feesDataTotal['novemberAmount'] = toMoney($feesDataTotal['novemberAmount'], '');
                $feesDataTotal['decemberAmount'] = toMoney($feesDataTotal['decemberAmount'], '');
                $feesDataTotal['januaryAmount'] = toMoney($feesDataTotal['januaryAmount'], '');
                $feesDataTotal['februaryAmount'] = toMoney($feesDataTotal['februaryAmount'], '');
                $feesDataTotal['marchAmount'] = toMoney($feesDataTotal['marchAmount'], '');
                $feesDataTotal['aprilAmount'] = toMoney($feesDataTotal['aprilAmount'], '');
                $feesDataTotal['mayAmount'] = toMoney($feesDataTotal['mayAmount'], '');
                $feesDataTotal['juneAmount'] = toMoney($feesDataTotal['juneAmount'], '');
                $feesDataTotal['totalAmount'] = toMoney($feesDataTotal['totalAmount'], '');
                $report->renderSubTotal($feesDataTotal);
            } else {
                $report->renderTotal($feesDataTotal);
            }
            $report->clean();
            $report->endPage();
        }
        // MANAGEMENT FEES ACTL vs BUDGET BY PROPERTY
        if ($view->items['reportsCSS'] || $view->items['mgtFeesActlByProp']) {
            if ($view->items['format'] == FILETYPE_PDF) {
                $header->subTitle = 'Management Fees Actual vs Budget By Property - ' . $view->items['year'];
                $report->resetColumns();
                $report->addColumn('label', '', 160, 'right', null);
                $report->addColumn('julyAmount', 'July', 45, 'right', '#,##0.00_);(#,##0.00)');
                $report->addColumn('augustAmount', 'August', 45, 'right', '#,##0.00_);(#,##0.00)');
                $report->addColumn('septemberAmount', 'September', 45, 'right', '#,##0.00_);(#,##0.00)');
                $report->addColumn('octoberAmount', 'October', 45, 'right', '#,##0.00_);(#,##0.00)');
                $report->addColumn('novemberAmount', 'November', 45, 'right', '#,##0.00_);(#,##0.00)');
                $report->addColumn('decemberAmount', 'December', 45, 'right', '#,##0.00_);(#,##0.00)');
                $report->addColumn('januaryAmount', 'January', 45, 'right', '#,##0.00_);(#,##0.00)');
                $report->addColumn('februaryAmount', 'February', 45, 'right', '#,##0.00_);(#,##0.00)');
                $report->addColumn('marchAmount', 'March', 45, 'right', '#,##0.00_);(#,##0.00)');
                $report->addColumn('aprilAmount', 'April', 45, 'right', '#,##0.00_);(#,##0.00)');
                $report->addColumn('mayAmount', 'May', 45, 'right', '#,##0.00_);(#,##0.00)');
                $report->addColumn('juneAmount', 'June', 45, 'right', '#,##0.00_);(#,##0.00)');
                $report->addColumn('totalAmount', 'YTD', 45, 'right', '#,##0.00_);(#,##0.00)');
                $report->preparePage();
                $report->renderHeader();

                $feesData = getFeesData($view->items['year'], 3, $view->items['showInactive'], $view->items['linked_id'], $dbShortName);
                foreach ($feesData as $propRow) {
                    $report->addSubHeaderItem('title', 0, 200, 'left');
                    $report->setSubHeaderValue('title', 'Property : ' . $propRow['label'], 200, 'left');
                    $report->renderSubHeader(1);

                    $actualData = [];
                    $actualData['label'] = 'Actual';
                    $actualData['julyAmount'] = toMoney($propRow['julyAmount'], '');
                    $actualData['augustAmount'] = toMoney($propRow['augustAmount'], '');
                    $actualData['septemberAmount'] = toMoney($propRow['septemberAmount'], '');
                    $actualData['octoberAmount'] = toMoney($propRow['octoberAmount'], '');
                    $actualData['novemberAmount'] = toMoney($propRow['novemberAmount'], '');
                    $actualData['decemberAmount'] = toMoney($propRow['decemberAmount'], '');
                    $actualData['januaryAmount'] = toMoney($propRow['januaryAmount'], '');
                    $actualData['februaryAmount'] = toMoney($propRow['februaryAmount'], '');
                    $actualData['marchAmount'] = toMoney($propRow['marchAmount'], '');
                    $actualData['aprilAmount'] = toMoney($propRow['aprilAmount'], '');
                    $actualData['mayAmount'] = toMoney($propRow['mayAmount'], '');
                    $actualData['juneAmount'] = toMoney($propRow['juneAmount'], '');
                    $actualData['totalAmount'] = toMoney($propRow['totalAmount'], '');
                    $report->renderLine_custom($actualData);

                    $budgetData = [];
                    $budgetData['label'] = 'Budget';
                    $budgetData['julyAmount'] = toMoney($propRow['julyBudget'], '');
                    $budgetData['augustAmount'] = toMoney($propRow['augustBudget'], '');
                    $budgetData['septemberAmount'] = toMoney($propRow['septemberBudget'], '');
                    $budgetData['octoberAmount'] = toMoney($propRow['octoberBudget'], '');
                    $budgetData['novemberAmount'] = toMoney($propRow['novemberBudget'], '');
                    $budgetData['decemberAmount'] = toMoney($propRow['decemberBudget'], '');
                    $budgetData['januaryAmount'] = toMoney($propRow['januaryBudget'], '');
                    $budgetData['februaryAmount'] = toMoney($propRow['februaryBudget'], '');
                    $budgetData['marchAmount'] = toMoney($propRow['marchBudget'], '');
                    $budgetData['aprilAmount'] = toMoney($propRow['aprilBudget'], '');
                    $budgetData['mayAmount'] = toMoney($propRow['mayBudget'], '');
                    $budgetData['juneAmount'] = toMoney($propRow['juneBudget'], '');
                    $budgetData['totalAmount'] = toMoney($propRow['totalBudget'], '');
                    $report->renderLine_custom($budgetData);

                    $varianceData = [];
                    $varianceData['label'] = 'Variance';
                    $varianceData['totalAmount'] = toMoney($propRow['totalAmount'] - $propRow['totalBudget'], '');
                    $varianceData['julyAmount'] = toMoney($propRow['julyAmount'] - $propRow['julyBudget'], '');
                    $varianceData['augustAmount'] = toMoney($propRow['augustAmount'] - $propRow['augustBudget'], '');
                    $varianceData['septemberAmount'] = toMoney($propRow['septemberAmount'] - $propRow['septemberBudget'], '');
                    $varianceData['octoberAmount'] = toMoney($propRow['octoberAmount'] - $propRow['octoberBudget'], '');
                    $varianceData['novemberAmount'] = toMoney($propRow['novemberAmount'] - $propRow['novemberBudget'], '');
                    $varianceData['decemberAmount'] = toMoney($propRow['decemberAmount'] - $propRow['decemberBudget'], '');
                    $varianceData['januaryAmount'] = toMoney($propRow['januaryAmount'] - $propRow['januaryBudget'], '');
                    $varianceData['februaryAmount'] = toMoney($propRow['februaryAmount'] - $propRow['februaryBudget'], '');
                    $varianceData['marchAmount'] = toMoney($propRow['marchAmount'] - $propRow['marchBudget'], '');
                    $varianceData['aprilAmount'] = toMoney($propRow['aprilAmount'] - $propRow['aprilBudget'], '');
                    $varianceData['mayAmount'] = toMoney($propRow['mayAmount'] - $propRow['mayBudget'], '');
                    $varianceData['juneAmount'] = toMoney($propRow['juneAmount'] - $propRow['juneBudget'], '');
                    $report->renderSubTotal($varianceData);
                }
            } else {
                if ($view->items['format'] == FILETYPE_XLS) {
                    $report->setSheetDetails($indexSheet, 'Mgt Fees Actual vs Budget By Property');
                    $report->line = 1;
                    $indexSheet++;
                    $report->resetColumns();
                    $report->addColumn('label', 'Property', 160, 'left', null);
                    $report->addColumn('julyAmount', 'July Actual', 45, 'right', '#,##0.00_);(#,##0.00)');
                    $report->addColumn('julyBudget', 'July Budget', 45, 'right', '#,##0.00_);(#,##0.00)');
                    $report->addColumn('augustAmount', 'August Actual', 45, 'right', '#,##0.00_);(#,##0.00)');
                    $report->addColumn('augustBudget', 'August Budget', 45, 'right', '#,##0.00_);(#,##0.00)');
                    $report->addColumn('septemberAmount', 'September Actual', 45, 'right', '#,##0.00_);(#,##0.00)');
                    $report->addColumn('septemberBudget', 'September Budget', 45, 'right', '#,##0.00_);(#,##0.00)');
                    $report->addColumn('octoberAmount', 'October Actual', 45, 'right', '#,##0.00_);(#,##0.00)');
                    $report->addColumn('octoberBudget', 'October Budget', 45, 'right', '#,##0.00_);(#,##0.00)');
                    $report->addColumn('novemberAmount', 'November Actual', 45, 'right', '#,##0.00_);(#,##0.00)');
                    $report->addColumn('novemberBudget', 'November Budget', 45, 'right', '#,##0.00_);(#,##0.00)');
                    $report->addColumn('decemberAmount', 'December Actual', 45, 'right', '#,##0.00_);(#,##0.00)');
                    $report->addColumn('decemberBudget', 'December Budget', 45, 'right', '#,##0.00_);(#,##0.00)');
                    $report->addColumn('januaryAmount', 'January Actual', 45, 'right', '#,##0.00_);(#,##0.00)');
                    $report->addColumn('januaryBudget', 'January Budget', 45, 'right', '#,##0.00_);(#,##0.00)');
                    $report->addColumn('februaryAmount', 'February Actual', 45, 'right', '#,##0.00_);(#,##0.00)');
                    $report->addColumn('februaryBudget', 'February Budget', 45, 'right', '#,##0.00_);(#,##0.00)');
                    $report->addColumn('marchAmount', 'March Actual', 45, 'right', '#,##0.00_);(#,##0.00)');
                    $report->addColumn('marchBudget', 'March Budget', 45, 'right', '#,##0.00_);(#,##0.00)');
                    $report->addColumn('aprilAmount', 'April Actual', 45, 'right', '#,##0.00_);(#,##0.00)');
                    $report->addColumn('aprilBudget', 'April Budget', 45, 'right', '#,##0.00_);(#,##0.00)');
                    $report->addColumn('mayAmount', 'May Actual', 45, 'right', '#,##0.00_);(#,##0.00)');
                    $report->addColumn('mayBudget', 'May Budget', 45, 'right', '#,##0.00_);(#,##0.00)');
                    $report->addColumn('juneAmount', 'June Actual', 45, 'right', '#,##0.00_);(#,##0.00)');
                    $report->addColumn('juneBudget', 'June Budget', 45, 'right', '#,##0.00_);(#,##0.00)');
                    $report->addColumn('totalAmount', 'YTD Actual', 45, 'right', '#,##0.00_);(#,##0.00)');
                    $report->addColumn('totalBudget', 'YTD Budget', 45, 'right', '#,##0.00_);(#,##0.00)');
                    $report->addColumn('totalVariance', 'Variance', 45, 'right', '#,##0.00_);(#,##0.00)');
                    $report->preparePage();
                    $report->renderHeader();
                    $feesData = getFeesData($view->items['year'], 3, $view->items['showInactive'], $view->items['linked_id'], $dbShortName);
                    $feesDataTotal = [];
                    foreach ($feesData as $row) {
                        $report->renderLine_custom($row);
                        $feesDataTotal['julyAmount'] += $row['julyAmount'];
                        $feesDataTotal['julyBudget'] += $row['julyBudget'];
                        $feesDataTotal['augustAmount'] += $row['augustAmount'];
                        $feesDataTotal['augustBudget'] += $row['augustBudget'];
                        $feesDataTotal['septemberAmount'] += $row['septemberAmount'];
                        $feesDataTotal['septemberBudget'] += $row['septemberBudget'];
                        $feesDataTotal['octoberAmount'] += $row['octoberAmount'];
                        $feesDataTotal['octoberBudget'] += $row['octoberBudget'];
                        $feesDataTotal['novemberAmount'] += $row['novemberAmount'];
                        $feesDataTotal['novemberBudget'] += $row['novemberBudget'];
                        $feesDataTotal['decemberAmount'] += $row['decemberAmount'];
                        $feesDataTotal['decemberBudget'] += $row['decemberBudget'];
                        $feesDataTotal['januaryAmount'] += $row['januaryAmount'];
                        $feesDataTotal['januaryBudget'] += $row['januaryBudget'];
                        $feesDataTotal['februaryAmount'] += $row['februaryAmount'];
                        $feesDataTotal['februaryBudget'] += $row['februaryBudget'];
                        $feesDataTotal['marchAmount'] += $row['marchAmount'];
                        $feesDataTotal['marchBudget'] += $row['marchBudget'];
                        $feesDataTotal['aprilAmount'] += $row['aprilAmount'];
                        $feesDataTotal['aprilBudget'] += $row['aprilBudget'];
                        $feesDataTotal['mayAmount'] += $row['mayAmount'];
                        $feesDataTotal['mayBudget'] += $row['mayBudget'];
                        $feesDataTotal['juneAmount'] += $row['juneAmount'];
                        $feesDataTotal['juneBudget'] += $row['juneBudget'];
                        $feesDataTotal['totalAmount'] += $row['totalAmount'];
                        $feesDataTotal['totalBudget'] += $row['totalBudget'];
                        $feesDataTotal['totalVariance'] += $row['totalVariance'];
                    }
                    $report->renderTotal($feesDataTotal);
                }
            }

            if ($view->items['showInactive']) {
                $z['label'] = '* Inactive Property';
                $z['italic'] = true;
                $z['headerStyle'] =
                [
                    'font' => ['italic' => true],
                ];
                $report->resetColumns();
                $report->addColumn('label', '', 160, 'left', null);
                $report->renderLine_custom($z);
            }

            $report->clean();
            $report->endPage();

        }
        // MANAGEMENT FEES ACTL vs BUDGET BY PM
        if ($view->items['reportsCSS'] || $view->items['mgtFeesActlByPM']) {
            if ($view->items['format'] == FILETYPE_PDF) {
                $footer->footerNote = '';
                $report->attachObject('footer', $footer);

                $header->subTitle = 'Management Fees Actual vs Budget By ' . ucwords(strtolower($_SESSION['country_default']['portfolio_manager'])) . ' - ' . $view->items['year'];
                $report->resetColumns();
                $report->addColumn('label', '', 160, 'right', null);
                $report->addColumn('julyAmount', 'July', 45, 'right', '#,##0.00_);(#,##0.00)');
                $report->addColumn('augustAmount', 'August', 45, 'right', '#,##0.00_);(#,##0.00)');
                $report->addColumn('septemberAmount', 'September', 45, 'right', '#,##0.00_);(#,##0.00)');
                $report->addColumn('octoberAmount', 'October', 45, 'right', '#,##0.00_);(#,##0.00)');
                $report->addColumn('novemberAmount', 'November', 45, 'right', '#,##0.00_);(#,##0.00)');
                $report->addColumn('decemberAmount', 'December', 45, 'right', '#,##0.00_);(#,##0.00)');
                $report->addColumn('januaryAmount', 'January', 45, 'right', '#,##0.00_);(#,##0.00)');
                $report->addColumn('februaryAmount', 'February', 45, 'right', '#,##0.00_);(#,##0.00)');
                $report->addColumn('marchAmount', 'March', 45, 'right', '#,##0.00_);(#,##0.00)');
                $report->addColumn('aprilAmount', 'April', 45, 'right', '#,##0.00_);(#,##0.00)');
                $report->addColumn('mayAmount', 'May', 45, 'right', '#,##0.00_);(#,##0.00)');
                $report->addColumn('juneAmount', 'June', 45, 'right', '#,##0.00_);(#,##0.00)');
                $report->addColumn('totalAmount', 'YTD', 45, 'right', '#,##0.00_);(#,##0.00)');
                $report->preparePage();
                $report->renderHeader();

                $feesData = getFeesData($view->items['year'], 4, $view->items['showInactive'], $view->items['linked_id'], $dbShortName);
                foreach ($feesData as $propRow) {
                    $report->addSubHeaderItem('title', 0, 200, 'left');
                    $report->setSubHeaderValue('title', ucwords(strtolower($_SESSION['country_default']['portfolio_manager'])) . ' : ' . $propRow['label'], 200, 'left');
                    $report->renderSubHeader(1);

                    $actualData = [];
                    $actualData['label'] = 'Actual';
                    $actualData['julyAmount'] = toMoney($propRow['julyAmount'], '');
                    $actualData['augustAmount'] = toMoney($propRow['augustAmount'], '');
                    $actualData['septemberAmount'] = toMoney($propRow['septemberAmount'], '');
                    $actualData['octoberAmount'] = toMoney($propRow['octoberAmount'], '');
                    $actualData['novemberAmount'] = toMoney($propRow['novemberAmount'], '');
                    $actualData['decemberAmount'] = toMoney($propRow['decemberAmount'], '');
                    $actualData['januaryAmount'] = toMoney($propRow['januaryAmount'], '');
                    $actualData['februaryAmount'] = toMoney($propRow['februaryAmount'], '');
                    $actualData['marchAmount'] = toMoney($propRow['marchAmount'], '');
                    $actualData['aprilAmount'] = toMoney($propRow['aprilAmount'], '');
                    $actualData['mayAmount'] = toMoney($propRow['mayAmount'], '');
                    $actualData['juneAmount'] = toMoney($propRow['juneAmount'], '');
                    $actualData['totalAmount'] = toMoney($propRow['totalAmount'], '');
                    $report->renderLine_custom($actualData);

                    $budgetData = [];
                    $budgetData['label'] = 'Budget';
                    $budgetData['julyAmount'] = toMoney($propRow['julyBudget'], '');
                    $budgetData['augustAmount'] = toMoney($propRow['augustBudget'], '');
                    $budgetData['septemberAmount'] = toMoney($propRow['septemberBudget'], '');
                    $budgetData['octoberAmount'] = toMoney($propRow['octoberBudget'], '');
                    $budgetData['novemberAmount'] = toMoney($propRow['novemberBudget'], '');
                    $budgetData['decemberAmount'] = toMoney($propRow['decemberBudget'], '');
                    $budgetData['januaryAmount'] = toMoney($propRow['januaryBudget'], '');
                    $budgetData['februaryAmount'] = toMoney($propRow['februaryBudget'], '');
                    $budgetData['marchAmount'] = toMoney($propRow['marchBudget'], '');
                    $budgetData['aprilAmount'] = toMoney($propRow['aprilBudget'], '');
                    $budgetData['mayAmount'] = toMoney($propRow['mayBudget'], '');
                    $budgetData['juneAmount'] = toMoney($propRow['juneBudget'], '');
                    $budgetData['totalAmount'] = toMoney($propRow['totalBudget'], '');
                    $report->renderLine_custom($budgetData);

                    $varianceData = [];
                    $varianceData['label'] = 'Variance';
                    $varianceData['totalAmount'] = toMoney($propRow['totalAmount'] - $propRow['totalBudget'], '');
                    $varianceData['julyAmount'] = toMoney($propRow['julyAmount'] - $propRow['julyBudget'], '');
                    $varianceData['augustAmount'] = toMoney($propRow['augustAmount'] - $propRow['augustBudget'], '');
                    $varianceData['septemberAmount'] = toMoney($propRow['septemberAmount'] - $propRow['septemberBudget'], '');
                    $varianceData['octoberAmount'] = toMoney($propRow['octoberAmount'] - $propRow['octoberBudget'], '');
                    $varianceData['novemberAmount'] = toMoney($propRow['novemberAmount'] - $propRow['novemberBudget'], '');
                    $varianceData['decemberAmount'] = toMoney($propRow['decemberAmount'] - $propRow['decemberBudget'], '');
                    $varianceData['januaryAmount'] = toMoney($propRow['januaryAmount'] - $propRow['januaryBudget'], '');
                    $varianceData['februaryAmount'] = toMoney($propRow['februaryAmount'] - $propRow['februaryBudget'], '');
                    $varianceData['marchAmount'] = toMoney($propRow['marchAmount'] - $propRow['marchBudget'], '');
                    $varianceData['aprilAmount'] = toMoney($propRow['aprilAmount'] - $propRow['aprilBudget'], '');
                    $varianceData['mayAmount'] = toMoney($propRow['mayAmount'] - $propRow['mayBudget'], '');
                    $varianceData['juneAmount'] = toMoney($propRow['juneAmount'] - $propRow['juneBudget'], '');
                    $report->renderSubTotal($varianceData);
                }
            } else {
                if ($view->items['format'] == FILETYPE_XLS) {
                    $report->setSheetDetails($indexSheet, 'Mgt Fees Actual vs Budget By PM');
                    $report->line = 1;
                    $indexSheet++;
                    $report->resetColumns();
                    $report->addColumn('label', ucwords(strtolower($_SESSION['country_default']['portfolio_manager'])), 160, 'left', null);
                    $report->addColumn('julyAmount', 'July Actual', 45, 'right', '#,##0.00_);(#,##0.00)');
                    $report->addColumn('julyBudget', 'July Budget', 45, 'right', '#,##0.00_);(#,##0.00)');
                    $report->addColumn('augustAmount', 'August Actual', 45, 'right', '#,##0.00_);(#,##0.00)');
                    $report->addColumn('augustBudget', 'August Budget', 45, 'right', '#,##0.00_);(#,##0.00)');
                    $report->addColumn('septemberAmount', 'September Actual', 45, 'right', '#,##0.00_);(#,##0.00)');
                    $report->addColumn('septemberBudget', 'September Budget', 45, 'right', '#,##0.00_);(#,##0.00)');
                    $report->addColumn('octoberAmount', 'October Actual', 45, 'right', '#,##0.00_);(#,##0.00)');
                    $report->addColumn('octoberBudget', 'October Budget', 45, 'right', '#,##0.00_);(#,##0.00)');
                    $report->addColumn('novemberAmount', 'November Actual', 45, 'right', '#,##0.00_);(#,##0.00)');
                    $report->addColumn('novemberBudget', 'November Budget', 45, 'right', '#,##0.00_);(#,##0.00)');
                    $report->addColumn('decemberAmount', 'December Actual', 45, 'right', '#,##0.00_);(#,##0.00)');
                    $report->addColumn('decemberBudget', 'December Budget', 45, 'right', '#,##0.00_);(#,##0.00)');
                    $report->addColumn('januaryAmount', 'January Actual', 45, 'right', '#,##0.00_);(#,##0.00)');
                    $report->addColumn('januaryBudget', 'January Budget', 45, 'right', '#,##0.00_);(#,##0.00)');
                    $report->addColumn('februaryAmount', 'February Actual', 45, 'right', '#,##0.00_);(#,##0.00)');
                    $report->addColumn('februaryBudget', 'February Budget', 45, 'right', '#,##0.00_);(#,##0.00)');
                    $report->addColumn('marchAmount', 'March Actual', 45, 'right', '#,##0.00_);(#,##0.00)');
                    $report->addColumn('marchBudget', 'March Budget', 45, 'right', '#,##0.00_);(#,##0.00)');
                    $report->addColumn('aprilAmount', 'April Actual', 45, 'right', '#,##0.00_);(#,##0.00)');
                    $report->addColumn('aprilBudget', 'April Budget', 45, 'right', '#,##0.00_);(#,##0.00)');
                    $report->addColumn('mayAmount', 'May Actual', 45, 'right', '#,##0.00_);(#,##0.00)');
                    $report->addColumn('mayBudget', 'May Budget', 45, 'right', '#,##0.00_);(#,##0.00)');
                    $report->addColumn('juneAmount', 'June Actual', 45, 'right', '#,##0.00_);(#,##0.00)');
                    $report->addColumn('juneBudget', 'June Budget', 45, 'right', '#,##0.00_);(#,##0.00)');
                    $report->addColumn('totalAmount', 'YTD Actual', 45, 'right', '#,##0.00_);(#,##0.00)');
                    $report->addColumn('totalBudget', 'YTD Budget', 45, 'right', '#,##0.00_);(#,##0.00)');
                    $report->addColumn('totalVariance', 'Variance', 45, 'right', '#,##0.00_);(#,##0.00)');
                    $report->preparePage();
                    $report->renderHeader();
                    $feesData = getFeesData($view->items['year'], 4, $view->items['showInactive'], $view->items['linked_id'], $dbShortName);
                    $feesDataTotal = [];
                    foreach ($feesData as $row) {
                        $report->renderLine_custom($row);
                        $feesDataTotal['julyAmount'] += $row['julyAmount'];
                        $feesDataTotal['julyBudget'] += $row['julyBudget'];
                        $feesDataTotal['augustAmount'] += $row['augustAmount'];
                        $feesDataTotal['augustBudget'] += $row['augustBudget'];
                        $feesDataTotal['septemberAmount'] += $row['septemberAmount'];
                        $feesDataTotal['septemberBudget'] += $row['septemberBudget'];
                        $feesDataTotal['octoberAmount'] += $row['octoberAmount'];
                        $feesDataTotal['octoberBudget'] += $row['octoberBudget'];
                        $feesDataTotal['novemberAmount'] += $row['novemberAmount'];
                        $feesDataTotal['novemberBudget'] += $row['novemberBudget'];
                        $feesDataTotal['decemberAmount'] += $row['decemberAmount'];
                        $feesDataTotal['decemberBudget'] += $row['decemberBudget'];
                        $feesDataTotal['januaryAmount'] += $row['januaryAmount'];
                        $feesDataTotal['januaryBudget'] += $row['januaryBudget'];
                        $feesDataTotal['februaryAmount'] += $row['februaryAmount'];
                        $feesDataTotal['februaryBudget'] += $row['februaryBudget'];
                        $feesDataTotal['marchAmount'] += $row['marchAmount'];
                        $feesDataTotal['marchBudget'] += $row['marchBudget'];
                        $feesDataTotal['aprilAmount'] += $row['aprilAmount'];
                        $feesDataTotal['aprilBudget'] += $row['aprilBudget'];
                        $feesDataTotal['mayAmount'] += $row['mayAmount'];
                        $feesDataTotal['mayBudget'] += $row['mayBudget'];
                        $feesDataTotal['juneAmount'] += $row['juneAmount'];
                        $feesDataTotal['juneBudget'] += $row['juneBudget'];
                        $feesDataTotal['totalAmount'] += $row['totalAmount'];
                        $feesDataTotal['totalBudget'] += $row['totalBudget'];
                        $feesDataTotal['totalVariance'] += $row['totalVariance'];
                    }
                    $report->renderTotal($feesDataTotal);
                }
            }
            $report->clean();
            $report->endPage();
        }

        // ALL FEES BY PM
        if ($view->items['reportsCSS'] || $view->items['allFeesByPM']) {
            if ($view->items['format'] == FILETYPE_XLS) {
                $report->setSheetDetails($indexSheet, 'All Fees By ' . ucwords(strtolower($_SESSION['country_default']['portfolio_manager'])));
                $report->line = 1;
                $indexSheet++;
            } elseif ($view->items['format'] == FILETYPE_PDF) {
                $footer->footerNote = '';
                $report->attachObject('footer', $footer);
            }
            $header->subTitle = 'All Fees By ' . ucwords(strtolower($_SESSION['country_default']['portfolio_manager'])) . ' - ' . $view->items['year'];
            $report->resetColumns();
            $report->addColumn('label', ucwords(strtolower($_SESSION['country_default']['portfolio_manager'])), 160, 'left', null);
            $report->addColumn('julyAmount', 'July', 45, 'right', '#,##0.00_);(#,##0.00)');
            $report->addColumn('augustAmount', 'August', 45, 'right', '#,##0.00_);(#,##0.00)');
            $report->addColumn('septemberAmount', 'September', 45, 'right', '#,##0.00_);(#,##0.00)');
            $report->addColumn('octoberAmount', 'October', 45, 'right', '#,##0.00_);(#,##0.00)');
            $report->addColumn('novemberAmount', 'November', 45, 'right', '#,##0.00_);(#,##0.00)');
            $report->addColumn('decemberAmount', 'December', 45, 'right', '#,##0.00_);(#,##0.00)');
            $report->addColumn('januaryAmount', 'January', 45, 'right', '#,##0.00_);(#,##0.00)');
            $report->addColumn('februaryAmount', 'February', 45, 'right', '#,##0.00_);(#,##0.00)');
            $report->addColumn('marchAmount', 'March', 45, 'right', '#,##0.00_);(#,##0.00)');
            $report->addColumn('aprilAmount', 'April', 45, 'right', '#,##0.00_);(#,##0.00)');
            $report->addColumn('mayAmount', 'May', 45, 'right', '#,##0.00_);(#,##0.00)');
            $report->addColumn('juneAmount', 'June', 45, 'right', '#,##0.00_);(#,##0.00)');
            $report->addColumn('totalAmount', 'YTD', 45, 'right', '#,##0.00_);(#,##0.00)');
            $report->preparePage();
            $report->renderHeader();

            $feesData = getFeesData($view->items['year'], 6, $view->items['showInactive'], $view->items['linked_id'], $dbShortName);
            $feesDataTotal = [];
            $pmHeader = '';
            $subTotal = [];
            foreach ($feesData as $row) {
                if ($pmHeader != $row['portfolio_name']) {

                    if ($pmHeader) {
                        $subTotalRender['julyAmount'] = toMoney($subTotal['julyAmount'], '');
                        $subTotalRender['augustAmount'] = toMoney($subTotal['augustAmount'], '');
                        $subTotalRender['septemberAmount'] = toMoney($subTotal['septemberAmount'], '');
                        $subTotalRender['octoberAmount'] = toMoney($subTotal['octoberAmount'], '');
                        $subTotalRender['novemberAmount'] = toMoney($subTotal['novemberAmount'], '');
                        $subTotalRender['decemberAmount'] = toMoney($subTotal['decemberAmount'], '');
                        $subTotalRender['januaryAmount'] = toMoney($subTotal['januaryAmount'], '');
                        $subTotalRender['februaryAmount'] = toMoney($subTotal['februaryAmount'], '');
                        $subTotalRender['marchAmount'] = toMoney($subTotal['marchAmount'], '');
                        $subTotalRender['aprilAmount'] = toMoney($subTotal['aprilAmount'], '');
                        $subTotalRender['mayAmount'] = toMoney($subTotal['mayAmount'], '');
                        $subTotalRender['juneAmount'] = toMoney($subTotal['juneAmount'], '');
                        $subTotalRender['totalAmount'] = toMoney($subTotal['totalAmount'], '');
                        if ($view->items['format'] == FILETYPE_PDF) {
                            $report->renderSubTotal($subTotalRender);
                        } else {
                            $report->renderTotal($subTotalRender, $subTotal['label'], null, 'left');
                        }

                        $subTotal = [];
                    }

                    $report->addSubHeaderItem('title', 0, 200, 'left');
                    $report->setSubHeaderValue('title', $row['portfolio_name'], 200, 'left');
                    $report->renderSubHeader(1, 1, [0, 0, 0], true);
                    $pmHeader = $row['portfolio_name'];
                }

                $subTotalRender['label'] = $subTotal['label'] = $row['portfolio_name'] . ' Sub-Total';
                if ($row['label']) {
                    $subTotal['julyAmount'] += $row['julyAmount'];
                    $subTotal['augustAmount'] += $row['augustAmount'];
                    $subTotal['septemberAmount'] += $row['septemberAmount'];
                    $subTotal['octoberAmount'] += $row['octoberAmount'];
                    $subTotal['novemberAmount'] += $row['novemberAmount'];
                    $subTotal['decemberAmount'] += $row['decemberAmount'];
                    $subTotal['januaryAmount'] += $row['januaryAmount'];
                    $subTotal['februaryAmount'] += $row['februaryAmount'];
                    $subTotal['marchAmount'] += $row['marchAmount'];
                    $subTotal['aprilAmount'] += $row['aprilAmount'];
                    $subTotal['mayAmount'] += $row['mayAmount'];
                    $subTotal['juneAmount'] += $row['juneAmount'];
                    $subTotal['totalAmount'] += $row['totalAmount'];

                    $feesDataTotal['julyAmount'] += $row['julyAmount'];
                    $feesDataTotal['augustAmount'] += $row['augustAmount'];
                    $feesDataTotal['septemberAmount'] += $row['septemberAmount'];
                    $feesDataTotal['octoberAmount'] += $row['octoberAmount'];
                    $feesDataTotal['novemberAmount'] += $row['novemberAmount'];
                    $feesDataTotal['decemberAmount'] += $row['decemberAmount'];
                    $feesDataTotal['januaryAmount'] += $row['januaryAmount'];
                    $feesDataTotal['februaryAmount'] += $row['februaryAmount'];
                    $feesDataTotal['marchAmount'] += $row['marchAmount'];
                    $feesDataTotal['aprilAmount'] += $row['aprilAmount'];
                    $feesDataTotal['mayAmount'] += $row['mayAmount'];
                    $feesDataTotal['juneAmount'] += $row['juneAmount'];
                    $feesDataTotal['totalAmount'] += $row['totalAmount'];
                    if ($view->items['format'] == FILETYPE_PDF) {
                        $row['julyAmount'] = toMoney($row['julyAmount'], '');
                        $row['augustAmount'] = toMoney($row['augustAmount'], '');
                        $row['septemberAmount'] = toMoney($row['septemberAmount'], '');
                        $row['octoberAmount'] = toMoney($row['octoberAmount'], '');
                        $row['novemberAmount'] = toMoney($row['novemberAmount'], '');
                        $row['decemberAmount'] = toMoney($row['decemberAmount'], '');
                        $row['januaryAmount'] = toMoney($row['januaryAmount'], '');
                        $row['februaryAmount'] = toMoney($row['februaryAmount'], '');
                        $row['marchAmount'] = toMoney($row['marchAmount'], '');
                        $row['aprilAmount'] = toMoney($row['aprilAmount'], '');
                        $row['mayAmount'] = toMoney($row['mayAmount'], '');
                        $row['juneAmount'] = toMoney($row['juneAmount'], '');
                        $row['totalAmount'] = toMoney($row['totalAmount'], '');
                    }
                    $report->renderLine_custom($row);
                }
            }

            if ($pmHeader) {
                $subTotalRender['julyAmount'] = toMoney($subTotal['julyAmount'], '');
                $subTotalRender['augustAmount'] = toMoney($subTotal['augustAmount'], '');
                $subTotalRender['septemberAmount'] = toMoney($subTotal['septemberAmount'], '');
                $subTotalRender['octoberAmount'] = toMoney($subTotal['octoberAmount'], '');
                $subTotalRender['novemberAmount'] = toMoney($subTotal['novemberAmount'], '');
                $subTotalRender['decemberAmount'] = toMoney($subTotal['decemberAmount'], '');
                $subTotalRender['januaryAmount'] = toMoney($subTotal['januaryAmount'], '');
                $subTotalRender['februaryAmount'] = toMoney($subTotal['februaryAmount'], '');
                $subTotalRender['marchAmount'] = toMoney($subTotal['marchAmount'], '');
                $subTotalRender['aprilAmount'] = toMoney($subTotal['aprilAmount'], '');
                $subTotalRender['mayAmount'] = toMoney($subTotal['mayAmount'], '');
                $subTotalRender['juneAmount'] = toMoney($subTotal['juneAmount'], '');
                $subTotalRender['totalAmount'] = toMoney($subTotal['totalAmount'], '');
                if ($view->items['format'] == FILETYPE_PDF) {
                    $report->renderSubTotal($subTotalRender);
                } else {
                    $report->renderTotal($subTotalRender, $subTotal['label'], null, 'left');
                }
            }

            if ($view->items['format'] == FILETYPE_PDF) {
                $feesDataTotal['julyAmount'] = toMoney($feesDataTotal['julyAmount'], '');
                $feesDataTotal['augustAmount'] = toMoney($feesDataTotal['augustAmount'], '');
                $feesDataTotal['septemberAmount'] = toMoney($feesDataTotal['septemberAmount'], '');
                $feesDataTotal['octoberAmount'] = toMoney($feesDataTotal['octoberAmount'], '');
                $feesDataTotal['novemberAmount'] = toMoney($feesDataTotal['novemberAmount'], '');
                $feesDataTotal['decemberAmount'] = toMoney($feesDataTotal['decemberAmount'], '');
                $feesDataTotal['januaryAmount'] = toMoney($feesDataTotal['januaryAmount'], '');
                $feesDataTotal['februaryAmount'] = toMoney($feesDataTotal['februaryAmount'], '');
                $feesDataTotal['marchAmount'] = toMoney($feesDataTotal['marchAmount'], '');
                $feesDataTotal['aprilAmount'] = toMoney($feesDataTotal['aprilAmount'], '');
                $feesDataTotal['mayAmount'] = toMoney($feesDataTotal['mayAmount'], '');
                $feesDataTotal['juneAmount'] = toMoney($feesDataTotal['juneAmount'], '');
                $feesDataTotal['totalAmount'] = toMoney($feesDataTotal['totalAmount'], '');
                $report->renderSubTotal($feesDataTotal);
            } else {
                $report->renderTotal($feesDataTotal);
            }
            $report->clean();
            $report->endPage();
        }

        if ($view->items['format'] == FILETYPE_XLS) {
            $report->deleteSheetByName('Agency Fees Report');
        }
        $report->clean();
        $report->close();
    }

    $view->items['downloadPath'] = $downloadPath;
    $view->render();
}

function getFeesData($year, $subReport, $inactive, $linked = null, $shortName = null)
{


    switch ($subReport) {
        case '0':
            return dbGetMangementFeesByProperty($year, $inactive, $linked, $shortName);
            break;
        case '1':
            return dbGetMangementFeesByPM($year, $inactive, $linked, $shortName);
            break;
        case '5':
            return dbGetMangementFeesByPropertyAndPM($year, $inactive, $linked, $shortName);
            break;
        case '7':
            return dbGetMangementFeesByPropertyAndPropertyGroup($year, $inactive, $linked, $shortName);
            break;
        case '2':
            return dbGetMangementFeesAll($year, $inactive, $linked, $shortName);
            break;
        case '3':
            return dbGetMangementFeesACTLvsBUDGETbyProperty($year, $inactive, $linked, $shortName);
            break;
        case '4':
            return dbGetMangementFeesACTLvsBUDGETbyPM($year, $inactive, $linked, $shortName);
            break;
        case '6':
            return dbGetAllFeesByPM($year, $inactive, $linked, $shortName);
            break;

    }
}

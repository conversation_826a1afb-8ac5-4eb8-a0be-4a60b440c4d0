<?php

use PhpOffice\PhpSpreadsheet\Style\Alignment;

/**
 * Process information gathered in order to generate a chart of accounts report.
 *
 * @param  $context  array Mandatory. Referenced array containing data needed to continue.
 *
 * <AUTHOR> Reyes
 *
 * @since 2012-09-21
 *
 * @modified 2012-12-07: Added display on screen option. [Morph]
 * @modified 2015-11-11: New version implemented a per task # 3939181. [Morph]
 **/
function chartOfAccountsProcess($context)
{
    global $clientDirectory, $pathPrefix;

    if (! isPostback()) {
        $view = new MasterPage(userViews(), '/managementReports/chartOfAccountsProcess.html');
        $view->setSection($context['module']);
    } else {
        $view = new UserControl(userViews(), '/managementReports/chartOfAccountsProcess.html');
    }

    $view->bindAttributesFrom($_REQUEST);
    $format = $view->items['format'];
    $reportType = $view->items['reportType'];

    $chartOfAccounts = dbGetChartOfAccount(false, $view->items['sortBy']);
    if ($reportType == 2) {
        foreach ($chartOfAccounts as $k => $v) {
            $accountNameGrouping[$chartOfAccounts[$k]['chartAccountName']][$chartOfAccounts[$k]['chartAccountCode']] = $v;
        }

        foreach ($chartOfAccounts as $k => $v) {
            if ($accountNameGrouping[$v['chartAccountName']]) {
                $coa[$k]['chartAccountName'] = $v['chartAccountName'];
                $coa[$k]['chartAGEXPOWN'] = $coa[$k]['chartAGEXPVO'] = $coa[$k]['chartAGEXPDR'] = $coa[$k]['chartAGINCOWN'] = $coa[$k]['chartAGINCVO'] = $coa[$k]['chartAGINCDR'] = '*';
                foreach ($accountNameGrouping[$v['chartAccountName']] as $x => $y) {
                    switch ($y['chartAccountGroup']) {
                        case 'EXPOWN':$coa[$k]['chartAGEXPOWN'] = $y['chartAccountCode'];
                            break;
                        case 'EXPVO':$coa[$k]['chartAGEXPVO'] = $y['chartAccountCode'];
                            break;
                        case 'EXPDR':$coa[$k]['chartAGEXPDR'] = $y['chartAccountCode'];
                            break;
                        case 'INCOWN':$coa[$k]['chartAGINCOWN'] = $y['chartAccountCode'];
                            break;
                        case 'INCVO':$coa[$k]['chartAGINCVO'] = $y['chartAccountCode'];
                            break;
                        case 'INCDR':$coa[$k]['chartAGINCDR'] = $y['chartAccountCode'];
                            break;
                    }
                }
                unset($accountNameGrouping[$v['chartAccountName']]);
            }
        }
    } else {
        foreach ($chartOfAccounts as $k => $v) {
            switch ($v['chartAccountType']) {
                case 'I':$chartOfAccounts[$k]['chartAccountType'] = 'Income';
                    break;
                case 'E':$chartOfAccounts[$k]['chartAccountType'] = 'Expense';
                    break;
                case 'B':$chartOfAccounts[$k]['chartAccountType'] = 'Balance Sheet';
                    break;
                case 'R':$chartOfAccounts[$k]['chartAccountType'] = 'Retained Income';
                    break;
                case 'IN':$chartOfAccounts[$k]['chartAccountType'] = 'Unknown Income';
                    break;
                default:$chartOfAccounts[$k]['chartAccountType'] = 'Unknown Type (' . $v['chartAccountType'] . ')';
                    break;
            }

            switch ($v['chartAccountGroup']) {
                case 'BS':$chartOfAccounts[$k]['chartAccountGroup'] = 'Balance Sheet';
                    break;
                case 'EXPOWN':$chartOfAccounts[$k]['chartAccountGroup'] = 'Owners Expenditure';
                    break;
                case 'EXPVO':$chartOfAccounts[$k]['chartAccountGroup'] = ucwords(strtolower($_SESSION['country_default']['variable_outgoings'])) . ' Expenditure';
                    break;
                case 'EXPDR':$chartOfAccounts[$k]['chartAccountGroup'] = 'Directly Recoverable Expenditure';
                    break;
                case 'INCOWN':$chartOfAccounts[$k]['chartAccountGroup'] = 'Owners Income';
                    break;
                case 'INCVO':$chartOfAccounts[$k]['chartAccountGroup'] = ucwords(strtolower($_SESSION['country_default']['variable_outgoings'])) . ' Income';
                    break;
                case 'INCDR':$chartOfAccounts[$k]['chartAccountGroup'] = 'Directly Recoverable Income';
                    break;
                default:$chartOfAccounts[$k]['chartAccountGroup'] = 'Unknown Group (' . $v['chartAccountGroup'] . ')';
                    break;
            }
        }

        $chart_ = [];
        foreach ($chartOfAccounts as $k => $v) {
            if (! isset($chart_[$v['chartAccountCode']])) {
                $chart_[$v['chartAccountCode']] = [
                    'chartAccountCode' => $v['chartAccountCode'],
                    'chartAccountName' => $v['chartAccountName'],
                    'chartAccountType' => $v['chartAccountType'],
                    'fieldGroup' => $v['fieldGroup'],
                    'priority' => $v['priority'],
                ];
            }
        }
        $chartOfAccounts = $chart_;
    }

    if ($context[IS_TASK]) {
        $view->bindAttributesFrom($context);
        extract($context);
    } else {
        $view->bindAttributesFrom($context);
        $view->bindAttributesFrom($_REQUEST);
    }

    if (! $context[DOC_MASTER]) {
        $logoFile = dbGetClientLogo();
        $logoPath = "assets/clientLogos/{$logoFile}";
        $_filePath =  "{$pathPrefix}{$clientDirectory}/{$format}/" . DOC_CHARTOFACCOUNTS . '/';
        $_downloadPath =  "{$clientDirectory}/{$format}/" . DOC_CHARTOFACCOUNTS;
        $file =  DOC_CHARTOFACCOUNTS . '_' . date('Ymd') . ".{$format}";
        $filePath = $_filePath . $file;
        $downloadPath = "{$_downloadPath}/{$file}";
    }

    switch ($format) {
        case FILETYPE_PDF:
            $report = ($context[DOC_MASTER]) ? new PDFDataReport($context[DOC_MASTER], $logoPath, A4_PORTRAIT) : new PDFDataReport($filePath, $logoPath, A4_PORTRAIT);
            $report->multiLine = true;
            $report->printRowLines = true;
            $report->printColumnLines = false;
            $report->printBorders = false;
            $report->cache = false;
            $prepared = 'as at ' . date('d M Y');
            $header = new ReportHeader('Chart of Accounts', '', $prepared);
            $header->xPos = $report->hMargin;
            $header->yPos = $report->pageHeight - $report->vMargin;
            $report->attachObject('header', $header);
            $footer = new TraccFooter(null, $_report['reportName'], $report->pageSize);
            $report->attachObject('footer', $footer);
            break;
        case FILETYPE_XLS:
            $report = new XLSDataReport($filePath, 'Chart of Accounts');
            $report->enableFormatting = true;
            switch ($view->items['colorScheme']) {
                case 'Medium Blue':
                    $report->totalFillColor = $report->fillColor = 'e8eaf1';
                    $report->baseColor = '406ec7';
                    break;
                case 'Indigo':
                    $report->totalFillColor = $report->fillColor = 'eae7f0';
                    $report->baseColor = '5a3ec1';
                    break;
                case 'Magenta':
                    $report->totalFillColor = $report->fillColor = 'f1e8f1';
                    $report->baseColor = 'c241ca';
                    break;
                case 'Pinkish Red':
                    $report->totalFillColor = $report->fillColor = 'f2e9ec';
                    $report->baseColor = 'e94b82';
                    break;
                case 'Red-Orange':
                    $report->totalFillColor = $report->fillColor = 'f3ecea';
                    $report->baseColor = 'fe6f52';
                    break;
                case 'Orange':
                    $report->totalFillColor = $report->fillColor = 'f3efea';
                    $report->baseColor = 'fea252';
                    break;
                case 'Yellow-Orange':
                    $report->fillColor = 'fec452';
                    $report->baseColor = '000000';
                    $report->totalFillColor = 'ffe7b8';
                    break;
                case 'Yellow':
                    $report->totalFillColor = $report->fillColor = 'feea52';
                    $report->baseColor = '000000';
                    break;
                case 'Greenish-Yellow':
                    $report->totalFillColor = $report->fillColor = 'f1f2e9';
                    $report->baseColor = 'd8ee4c';
                    break;
                case 'Yellow-Green':
                    $report->totalFillColor = $report->fillColor = 'ebf1e8';
                    $report->baseColor = '7fd143';
                    break;
                case 'Blue-Green':
                    $report->totalFillColor = $report->fillColor = 'e8f1ed';
                    $report->baseColor = '42cd85';
                    break;
                case 'Navy Blue':
                    $report->fillColor = '366092';
                    $report->baseColor = 'ffffff';
                    $report->totalFillColor = 'c0cfe3';
                    break;
                case 'Bluish Cyan':
                default:
                    $report->totalFillColor = $report->fillColor = 'e8eef1';
                    $report->baseColor = '43a5d1';
                    break;
            }
            $report->changeColorScheme($report->baseColor, $report->fillColor, $report->totalFillColor);
            $styleHeaderLeftAlign = array_merge($report->styleHeader, ['alignment' =>  ['vertical' => Alignment::VERTICAL_CENTER,  'horizontal' => Alignment::HORIZONTAL_LEFT]]);
            $styleHeaderCenterAlign = array_merge($report->styleHeader, ['alignment' =>  ['vertical' => Alignment::VERTICAL_CENTER,  'horizontal' => Alignment::HORIZONTAL_CENTER]]);
            break;
    }

    if ($format != FILETYPE_SCREEN) {
        if ($reportType == 2) {
            if ($format == FILETYPE_XLS) {
                $report->addColumn('chartAccountName', '', 123, 'left', '@', $styleHeaderLeftAlign);
                $report->addColumn('chartAGEXPOWN', 'Owner Codes', 60, 'left', '@', $styleHeaderCenterAlign);
                $report->addColumn('chartAGEXPVO', ucwords(strtolower($_SESSION['country_default']['variable_outgoings'])), 60, 'left', '@', $styleHeaderCenterAlign);
                $report->addColumn('chartAGEXPDR', 'Directly Recoverable', 60, 'left', '@', $styleHeaderCenterAlign);
                $report->addColumn('chartAGINCOWN', 'Owner Codes', 60, 'left', '@', $styleHeaderCenterAlign);
                $report->addColumn('chartAGINCVO', ucwords(strtolower($_SESSION['country_default']['variable_outgoings'])), 60, 'left', '@', $styleHeaderCenterAlign);
                $report->addColumn('chartAGINCDR', 'Directly Recoverable', 60, 'left', '@', $styleHeaderCenterAlign);
            } else { // FILETYPE_PDF
                $report->addColumn('chartAccountName', 'Account Name', 123, 'left');
                $report->addColumn('chartAGEXPOWN', 'Owner Expense', 60, 'left');
                $report->addColumn('chartAGEXPVO', strtoupper($_SESSION['country_default']['vo']) . ' Expenditure', 60, 'left');
                $report->addColumn('chartAGEXPDR', 'DR Expenditure', 60, 'left');
                $report->addColumn('chartAGINCOWN', 'Owner Income', 60, 'left');
                $report->addColumn('chartAGINCVO', strtoupper($_SESSION['country_default']['vo']) . ' Income', 60, 'left');
                $report->addColumn('chartAGINCDR', 'DR Income', 60, 'left');
            }
        } else {
            $report->addColumn('chartAccountCode', 'Account Code', 60, 'left', '@', $styleHeaderLeftAlign);
            $report->addColumn('chartAccountName', 'Account Name', 200, 'left', '@', $styleHeaderLeftAlign);
            $report->addColumn('chartAccountType', 'Account Type', 60, 'left', '@', $styleHeaderLeftAlign);
            $report->addColumn('fieldGroup', 'Account Group', 123, 'left', '@', $styleHeaderLeftAlign);
            $report->addColumn('priority', 'Quick Payment Priority', 100, 'center', '@', $styleHeaderLeftAlign);
        }

        $report->preparePage();

        if ($reportType == 2 && $format == FILETYPE_XLS) {
            $line =
            [
                'chartAccountName' => [
                    'value' => 'Account Name',
                    'style' => $styleHeaderLeftAlign,
                ],
                'chartAGEXPOWN' => [
                    'value' => 'Expenditure',
                    'mergecells' => 4,
                ],
                'chartAGINCOWN' => [
                    'value' => 'Income',
                    'mergecells' => 7,
                ],
            ];
            $report->renderCustomLine($line, $styleHeaderCenterAlign);
        }
        $report->renderHeader();

        if ($reportType == 2 && $format == FILETYPE_XLS) {
            $report->excel->getActiveSheet()->mergeCells(cellReference(1, 1) . ':' . cellReference(2, 1));
        }

        if ($reportType == 2) {
            $report->renderData($coa);
        } else {
            $report->renderData($chartOfAccounts);
        }

        $report->clean();
        $report->endPage();

        $report->close();
    } else {
        if ($reportType == 2) {
            $view->items['chartOfAccounts'] = $coa;
        } else {
            $view->items['chartOfAccounts'] = $chartOfAccounts;
        }
    }

    if (! $context[DOC_MASTER]) {
        if ($context[IS_TASK]) {
            $email = new EmailTemplate('views/emails/basicEmail.html', SYSTEMURL);
            $attachment =  [];
            $attachment[0]['file'] = REPORTPATH . '/' . $pdfDownloadLink;
            $attachment[0]['content_type'] = 'application/pdf';
            sendMail($_SESSION['email'], $_SESSION['first_name'] . ' ' . $_SESSION['last_name'], $email->toString(), 'Report', $attachment);
            logData('Emailed report to ' . $_SESSION['email']);
            $context[TASK_COMPLETE] = true;
            $view->items['statusMessage'] = 'Due to its complexity, this report has been scheduled and will be emailed to you on completion.';
        } else {
            $view->items['downloadPath'] = $downloadPath;
        }
        $view->render();
    }
}

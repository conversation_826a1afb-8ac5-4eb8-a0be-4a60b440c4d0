<?php


function budgetPerPortfolio(&$context)
{
    $userpermission = new userpermission();
    $result = $userpermission->userHasPageAccess(__FUNCTION__);
    global $clientDB;
    if (! isPostback()) {
        $view = new MasterPage(userViews(), '/managementReports/budgetPerPortfolio.html');
        $view->setSection($context['module']);
    } else {
        $view = new UserControl(userViews(), '/managementReports/budgetPerPortfolio.html');
    }


    // -- bind the postback and set some standard variables
    $view->bindAttributesFrom($_REQUEST);
    if ($context['last_error']) {
        $view->items['last_error'] = $context['last_error'];
    }
    if ($context['statusMessage']) {
        $view->items['statusMessage'] = $context['statusMessage'];
    }
    $validationErrors = [];


    // -- grab the relevant action and process as required
    switch ($view->items['action']) {
        case 'changeClient':
            $view->items['propertyID'] = null;
            break;
        case 'changeOption':
            $view->items['propertyID'] = '';
            break;
        case 'copy':
            $agentBudget = dbGetAgentBudget($view->items['propertyID'], $view->items['year'], $view->items['linked_id']);

            $data['propertyID'] = $view->items['propertyID'];
            $data['year'] = $view->items['year'];
            foreach ($agentBudget as $rs) {
                $data['accountID'] = $rs['budgetAccount'];
                $data['amount'] = $rs['budgetAmt'];

                $check = dbCheckBudgetPortfolio($view->items['propertyID'], $rs['budgetAccount'], $view->items['year'], $view->items['linked_id']);
                if ($check['total']) {
                    dbUpdateBudgetPortfolio($data, $view->items['linked_id']);
                } else {
                    dbInsertBudgetPortfolio($data, $view->items['linked_id']);
                }


            }

            $view->items['statusMessage'] = 'Budget has been successfully copied.';
            break;
        case 'save':
            if (! $view->items['year']) {
                $validationErrors[] = 'You have not provided a valid financial year';
            }
            foreach ($view->items['amount'] as $key => $amount) {
                if (! isValid($amount, TEXT_FLOAT, true)) {
                    $validationErrors[] = 'You have not provided a valid amount';
                }
            }


            if (noErrors($validationErrors)) {
                $code = $view->items['account'];
                $data['propertyID'] = $view->items['propertyID'];
                $data['year'] = $view->items['year'];
                dbDeleteBudgetPortfolio($data, $view->items['linked_id']);
                foreach ($view->items['amount'] as $key => $amount) {
                    $data['accountID'] = $code[$key];
                    $data['amount'] = $amount;

                    if ($amount) {
                        dbInsertBudgetPortfolio($data, $view->items['linked_id']);
                    }
                    $view->items['statusMessage'] = 'Budget has been successfully updated.';
                }

            }

            break;
    }

    $view->items['LinkedDatabaseListMode'] =  dbGetLinkedDatabase();
    if (count($view->items['LinkedDatabaseListMode']) == 1) {
        $view->items['linked_id'] = $clientDB;
        $view->items['LinkedDatabaseListMode'] = [];
    }


    $calendar = dbGetCalendarYear();
    foreach ($calendar as $data) {
        $yearList[$data['year']] = $data['year'];
    }
    $view->items['yearList'] = $yearList;

    $view->items['amount'] = '';
    if ($view->items['year']) {

        $view->items['propertyList'] = dbPropertyListIncomeBudget(true, null, 1, $view->items['withBudget'], $view->items['linked_id'], $view->items['year']);

        if ($view->items['propertyID']) {
            $rec = dbGetBudgetPortfolio($view->items['propertyID'], $view->items['year'], $view->items['linked_id']);
            $data = [];
            foreach ($rec as $rs) {
                $data[] = ['amount' => $rs['amount'], 'description' => $rs['pmca_name'] . ' - ' . trim($rs['pmca_code']), 'code' => $rs['pmca_code'], 'budgetAmt' => $rs['budgetAmt']];
            }
            $view->items['data'] = $data;

        }

    }

    $view->items['yesNoOption'] =
    [
        '1' => 'Yes',
        '0' => 'No',
    ];

    if (! isset($view->items['withBudget'])) {
        $view->items['withBudget'] = 0;
    }



    // -- add the validation errors to the view and render
    $view->items['validationErrors'] = $validationErrors;
    $view->render();


}

<?php

global $clientDirectory, $pathPrefix, $sess;


use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Fill;

function invoiceActivityProcess(&$context)
{
    global $clientDirectory, $pathPrefix, $sess;

    if (! isPostback()) {
        $view = new MasterPage(userViews(), '/accountingReports/invoiceActivityProcess.html');
        $view->setSection($context['module']);
    } else {
        $view = new UserControl(userViews(), '/accountingReports/invoiceActivityProcess.html');
    }

    $view->bindAttributesFrom($context);
    extract($context);


    $format = $view->items['format'];

    $property = deserializeParameters($view->items['propertyID']);
    $period = dbGetPeriodYear($property[0], $view->items['periodFrom'], $view->items['periodTo']);

    $yearPeriod = [];
    foreach ($period as $data) {
        $id = $data['year'];
        $yearPeriod[$id][] = $data['period'];
    }

    $properties = deserializeParameters($view->items['propertyID']);


    $data =  [];
    $prop = [];
    if (! isEmptyArray($properties)) {
        foreach ($properties as $propertyID) {

            $invoice = dbGetInvoiceActivity($propertyID, $yearPeriod);
            $totals = [];
            $lastAccount = null;
            $acctTotalGross = $acctTotalNet = $acctTotalGST = 0;
            $lastTransactionType = null;
            $transTotalGross = $transTotalNet = $transTotalGST = 0;
            $propGrossIncome = $propNetIncome = $propGSTIncome = 0;
            $propGrossExpense = $propIncomeExpense = $propGSTExpense = 0;

            $typeIncome['transactionDate'] = 'Operating Income';
            $typeIncome['bold'] = true;
            $typeIncome['width']['transactionDate'] = 100;
            $typeIncome['bgcolor'] = [0.8, 0.85, 1];
            $typeIncome['header'] = 'income';
            // used by excel
            $typeIncome['headerStyle'] =
            [
                'fill' => [
                    'type' => Fill::FILL_SOLID,
                    'color' => ['rgb' => 'ccd9ff'],
                ],
                'font' => ['color' => ['rgb' => '000000']],
                'alignment' => ['vertical' => Alignment::VERTICAL_CENTER, 'horizontal' => Alignment::HORIZONTAL_LEFT],
            ];
            $data[$propertyID][] = $typeIncome;
            foreach ($invoice as $row) {
                if ($row['transactionType'] == 'ar' or $row['transactionType'] == 'arbs') {
                    [$day_, $month_, $year_] = explode('/', $row['dueDate']);
                    // $filename = 'tax_invoice_'. '20140701' . '_'. '9646'. '_'. 'ALBANY'. '_'. 'MAST';
                    $filename = 'tax_invoice_' . $year_ . $month_ . $day_ . '_' . $row['invoiceNumber'] . '_' . $row['propertyID'] . '_' . $row['leaseID'];
                    $file = "../reports/{$clientDirectory}/pdf/TaxInvoice/$filename.pdf";
                    // echo $file .'<br>';
                    if (file_exists($file)) {
                        $row['file'] = $file;
                    } else {
                        [$day_, $month_, $year_] = explode('/', $row['transactionDate']);
                        // $filename = 'tax_invoice_'. '20140701' . '_'. '9646'. '_'. 'ALBANY'. '_'. 'MAST';
                        $filename = 'tax_invoice_' . $year_ . $month_ . $day_ . '_' . $row['invoiceNumber'] . '_' . $row['propertyID'] . '_' . $row['leaseID'];
                        $file = "../reports/{$clientDirectory}/pdf/TaxInvoice/$filename.pdf";
                        // echo $file .'<br>';
                        if (file_exists($file)) {
                            $row['file'] = $file;
                        } else {
                            [$day_, $month_, $year_] = explode('/', $row['toDate']);
                            $the_date = date('d/m/Y', strtotime("$month_/$day_/$year_"));
                            [$day_, $month_, $year_] = explode('/', $the_date);
                            // $filename = 'tax_invoice_'. '20140701' . '_'. '9646'. '_'. 'ALBANY'. '_'. 'MAST';
                            $filename = 'tax_invoice_' . $year_ . $month_ . $day_ . '_' . $row['invoiceNumber'] . '_' . $row['propertyID'] . '_' . $row['leaseID'];
                            $file = "../reports/{$clientDirectory}/pdf/TaxInvoice/$filename.pdf";
                            // echo $file .'<br>';
                            if (file_exists($file)) {
                                $row['file'] = $file;
                            }
                        }
                    }
                } elseif ($row['transactionType'] == 'ap' or $row['transactionType'] == 'apbs') {
                    $attachFile = dbGetDocumentInvoiceByBatchAP($row['attached'], $row['propertyID'], $row['leaseID']);
                    $row['file'] =  $attachFile;
                }


                if (! in_array($row['propertyID'], $prop)) {
                    array_push($prop, $row['propertyID']);
                }

                if ($lastAccount !== null and $lastAccount != $row['accountID']) {
                    // add total
                    $acctTotal['invoiceNumber'] = "Account Total for $lastAccount " . dbGetAccountName($lastAccount);
                    if ($format == FILETYPE_SCREEN) {
                        $acct['transactionDate'] = '&nbsp;';
                    } else {
                        $acct['transactionDate'] = '';
                    }

                    $acctTotal['gross'] = ($acctTotalGross);
                    $acctTotal['net'] = ($acctTotalNet);
                    $acctTotal['gst'] = ($acctTotalGST);

                    $acctTotal['bold'] = false;
                    $acctTotal['width']['invoiceNumber'] = 300;
                    $acctTotal['bgcolor'] = [0.9, 0.9, 0.9];


                    // used by excel
                    $acctTotal['headerStyle'] =
                    [
                        'fill' => [
                            'type' => Fill::FILL_SOLID,
                            'color' => ['rgb' => 'e1e1e1'],
                        ],
                        'font' => ['color' => ['rgb' => '000000']],
                        'alignment' => ['vertical' => Alignment::VERTICAL_CENTER, 'horizontal' => Alignment::HORIZONTAL_LEFT],
                    ];

                    $data[$row['propertyID']][] = $acctTotal;
                    if ($lastTransactionType !== null and $lastTransactionType != $row['transactionType']) {
                    } else {
                        $data[$row['propertyID']][] = $acct;
                    }
                    $acctTotalGross = $acctTotalNet = $acctTotalGST = 0;

                }

                if ($lastTransactionType !== null and $lastTransactionType != $row['transactionType']) {
                    // add total
                    //                    if($lastTransactionType == 'ar'){
                    //                        $transType['invoiceNumber'] = 'Income Total';
                    //                    }else{
                    //                        $transType['invoiceNumber'] = 'Expense Total';
                    //                    }

                    switch ($lastTransactionType) {
                        case 'ar':
                            $transType['invoiceNumber'] = 'Operating Income Total';
                            break;
                        case 'ap':
                            $transType['invoiceNumber'] = 'Operating Expenses Total';
                            break;
                        case 'arbs':
                            $transType['invoiceNumber'] = 'Non-Operating Income Total';
                            break;
                        default:
                            $transType['invoiceNumber'] = 'Non-Operating Expenses Total';
                            break;

                    }

                    if ($format == FILETYPE_SCREEN) {
                        $expense['transactionDate'] = '&nbsp;';
                    } else {
                        $expense['transactionDate'] = '';
                    }

                    $transType['gross'] = ($transTotalGross);
                    $transType['net'] = ($transTotalNet);
                    $transType['gst'] = ($transTotalGST);

                    $transType['bold'] = true;
                    $transType['width']['invoiceNumber'] = 200;
                    $transType['bgcolor'] = [220 / 255, 242 / 255, 255 / 255];
                    $transType['transacTotal'] = 'transacTotal';

                    $transType['headerStyle'] =
                    [
                        'fill' => [
                            'type' => Fill::FILL_SOLID,
                            'color' => ['rgb' => 'dcf2ff'],
                        ],
                        'font' => ['bold' => true, 'color' => ['rgb' => '000000']],
                        'alignment' => ['vertical' => Alignment::VERTICAL_CENTER, 'horizontal' => Alignment::HORIZONTAL_LEFT],
                    ];

                    $data[$row['propertyID']][] = $transType;
                    $data[$row['propertyID']][] = $expense;

                    if ($row['transactionType'] == 'ap') {
                        $type['transactionDate'] = 'Operating Expense';
                        $type['bold'] = true;
                        $type['header'] = 'expense';
                        $type['width']['transactionDate'] = 100;
                        $type['bgcolor'] = [0.8, 0.85, 1];
                        $type['headerStyle'] =
                        [
                            'fill' => [
                                'type' => Fill::FILL_SOLID,
                                'color' => ['rgb' => 'ccd9ff'],
                            ],
                            'font' => ['color' => ['rgb' => '000000']],
                            'alignment' => ['vertical' => Alignment::VERTICAL_CENTER, 'horizontal' => Alignment::HORIZONTAL_LEFT],
                        ];
                        $data[$row['propertyID']][] = $type;
                    } elseif ($row['transactionType'] == 'arbs') {
                        $type['transactionDate'] = 'Non-Operating Income';
                        $type['bold'] = true;
                        $type['header'] = 'incomeBS';
                        $type['width']['transactionDate'] = 100;
                        $type['bgcolor'] = [0.8, 0.85, 1];
                        $type['headerStyle'] =
                        [
                            'fill' => [
                                'type' => Fill::FILL_SOLID,
                                'color' => ['rgb' => 'ccd9ff'],
                            ],
                            'font' => ['color' => ['rgb' => '000000']],
                            'alignment' => ['vertical' => Alignment::VERTICAL_CENTER, 'horizontal' => Alignment::HORIZONTAL_LEFT],
                        ];
                        $data[$row['propertyID']][] = $type;
                    } elseif ($row['transactionType'] == 'apbs') {
                        $type['transactionDate'] = 'Non-Operating Expense';
                        $type['bold'] = true;
                        $type['header'] = 'expenseBS';
                        $type['width']['transactionDate'] = 100;
                        $type['bgcolor'] = [0.8, 0.85, 1];
                        $type['headerStyle'] =
                        [
                            'fill' => [
                                'type' => Fill::FILL_SOLID,
                                'color' => ['rgb' => 'ccd9ff'],
                            ],
                            'font' => ['color' => ['rgb' => '000000']],
                            'alignment' => ['vertical' => Alignment::VERTICAL_CENTER, 'horizontal' => Alignment::HORIZONTAL_LEFT],
                        ];
                        $data[$row['propertyID']][] = $type;
                    }

                    if (substr($lastTransactionType, 0, 2) == 'ar') {
                        $propGrossIncome += $transTotalGross;
                        $propNetIncome += $transTotalNet;
                        $propGSTIncome += $transTotalGST;

                    } else {
                        $propGrossIncome -= $transTotalGross;
                        $propNetIncome -= $transTotalNet;
                        $propGSTIncome -= $transTotalGST;
                    }
                    $transTotalGross = $transTotalNet = $transTotalGST = 0;
                }

                if ($row['purchaseNumber'] == 0) {
                    $row['purchaseNumber'] = '';
                } else {
                    $row['purchaseNumber'] =  $row['purchaseNumber'];
                }
                $row['gross'] = ($row['grossAmount']);
                $row['net'] = ($row['netAmount']);
                $row['gst'] = ($row['gstAmount']);

                $acctTotalGross += $row['grossAmount'];
                $acctTotalNet += $row['netAmount'];
                $acctTotalGST += $row['gstAmount'];

                $transTotalGross += $row['grossAmount'];
                $transTotalNet += $row['netAmount'];
                $transTotalGST += $row['gstAmount'];

                $data[$row['propertyID']][] = $row;
                $lastAccount = $row['accountID'];
                $lastTransactionType = $row['transactionType'];

                $count = count($invoice ?? []);

            }

            if ($lastAccount !== null) {
                $acctTotal['invoiceNumber'] = "Account Total for $lastAccount " . dbGetAccountName($lastAccount);
                //  $acct['transactionDate'] = '';
                $acctTotal['gross'] = ($acctTotalGross);
                $acctTotal['net'] = ($acctTotalNet);
                $acctTotal['gst'] = ($acctTotalGST);

                $acctTotal['bold'] = false;
                $acctTotal['width']['invoiceNumber'] = 300;
                $acctTotal['bgcolor'] = [0.9, 0.9, 0.9];

                // used by excel
                $acctTotal['headerStyle'] =
                [
                    'fill' => [
                        'type' => Fill::FILL_SOLID,
                        'color' => ['rgb' => 'e1e1e1'],
                    ],
                    'font' => ['color' => ['rgb' => '000000']],
                    'alignment' => ['vertical' => Alignment::VERTICAL_CENTER, 'horizontal' => Alignment::HORIZONTAL_LEFT],
                ];

                $data[$propertyID][] = $acctTotal;
                //  $data[$propertyID][] = $acct;
                $acctTotalGross = $acctTotalNet = $acctTotalGST = 0;
            }
            if ($lastTransactionType !== null) {
                // add total
                //                if($lastTransactionType == 'ar'){
                //                    $transType['invoiceNumber'] = 'Income Total';
                //                }else{
                //                    $transType['invoiceNumber'] = 'Expense Total';
                //                }

                switch ($lastTransactionType) {
                    case 'ar':
                        $transType['invoiceNumber'] = 'Operating Income Total';
                        break;
                    case 'ap':
                        $transType['invoiceNumber'] = 'Operating Expenses Total';
                        break;
                    case 'arbs':
                        $transType['invoiceNumber'] = 'Non-Operating Income Total';
                        break;
                    default:
                        $transType['invoiceNumber'] = 'Non-Operating Expenses Total';
                        break;

                }
                $transType['gross'] = ($transTotalGross);
                $transType['net'] = ($transTotalNet);
                $transType['gst'] = ($transTotalGST);

                $transType['bold'] = true;
                $transType['width']['invoiceNumber'] = 200;
                $transType['bgcolor'] = [220 / 255, 242 / 255, 255 / 255];
                $transType['transacTotal'] = 'transacTotal';

                $transType['headerStyle'] =
                [
                    'fill' => [
                        'type' => Fill::FILL_SOLID,
                        'color' => ['rgb' => 'dcf2ff'],
                    ],
                    'font' => ['bold' => true, 'color' => ['rgb' => '000000']],
                    'alignment' => ['vertical' => Alignment::VERTICAL_CENTER, 'horizontal' => Alignment::HORIZONTAL_LEFT],
                ];

                $data[$row['propertyID']][] = $transType;
                $propGrossExpense = $transTotalGross;
                $propNetExpense = $transTotalNet;
                $propGSTExpense = $transTotalGST;
                $transTotalGross = $transTotalNet = $transTotalGST = 0;
            }

            $totals['invoiceNumber'] = 'Property Total';
            $totals['bold'] = false;
            $totals['width']['invoiceNumber'] = 300;
            $totals['bgcolor'] = [0.9, 0.9, 0.9];
            $totals['headerStyle'] =
            [
                'fill' => [
                    'type' => Fill::FILL_SOLID,
                    'color' => ['rgb' => 'e1e1e1'],
                ],
                'font' => ['bold' => true, 'color' => ['rgb' => '000000']],
                'alignment' => ['vertical' => Alignment::VERTICAL_CENTER, 'horizontal' => Alignment::HORIZONTAL_LEFT],
            ];
            $totals['gross'] = ($propGrossIncome - $propGrossExpense);
            $totals['net'] = ($propNetIncome - $propNetExpense);
            $totals['gst'] = ($propGSTIncome - $propGSTExpense);
            $data[$propertyID][] = $totals;
            unset($totals);
        }
    }

    $logoFile = dbGetClientLogo();
    $logoPath = "assets/clientLogos/{$logoFile}";

    $_filePath =  "{$pathPrefix}{$clientDirectory}/{$format}/" . DOC_INVOICE . '/';
    $file =  'InvoiceActivity_' . date('YmdHis') . ".{$format}";


    $filePath = $_filePath . $file;

    switch ($format) {
        case FILETYPE_PDF:
            $report = new PDFDataMultiLineReport($filePath, $logoPath, A4_LANDSCAPE);
            $report->multiLine = true;
            $report->printRowLines = true;
            $report->printColumnLines = false;
            $report->printBorders = false;
            $report->cache = false;
            $header = new ReportHeader('Invoice Activity Report', $subtitle, $prepared);
            $header->subText = $view->items['periodFrom'] . ' to ' . $view->items['periodTo'];
            $header->xPos = $report->hMargin;
            $header->yPos = $report->pageHeight - $report->vMargin;
            $report->attachObject('header', $header);
            $footer = new TraccFooter(null, 'Invoice Activity Report', $report->pageSize);
            $report->attachObject('footer', $footer);
            break;
        case FILETYPE_XLS:
            $report = new XLSDataReport($filePath, 'Invoice Activity Report');
            $report->enableFormatting = true;
            break;
    }


    $numberFormat = '_(* #,##0.00_);_(* (#,##0.00);_(* "-"??_);_(@_)';

    if ($format == FILETYPE_PDF) {
        $numberFormat = 2;
    }

    $report->addColumn('transactionDate', 'Date', 40, 'left');
    $report->addColumn('leaseID', 'Lease Code', 40, 'left');
    $report->addColumn('leaseName', 'Lease Name', 65, 'left');
    $report->addColumn('invoiceNumber', 'Invoice #', 60, 'left');
    $report->addColumn('purchaseNumber', 'PO #', 40, 'left');
    $report->addColumn('dueDate', 'Due Date', 40, 'left');
    $report->addColumn('fromDate', 'From Date', 40, 'left');
    $report->addColumn('toDate', 'To Date', 40, 'left');
    $report->addColumn('accountID', 'Acct Code', 40, 'left');
    $report->addColumn('description', 'Description', 65, 'left');
    $report->addColumn('transType', 'Type', 30, 'left');
    $report->addColumn('period', 'Period', 30, 'left');
    $report->addColumn('year', 'Year', 40, 'right');
    $report->addColumn('net', 'Net', 55, 'right');
    $report->addColumn('gst', 'GST', 55, 'right');
    $report->addColumn('gross', 'Gross', 55, 'right');

    $i = 1;
    foreach ($prop as $propertyID) {

        if ($format == FILETYPE_PDF) {
            $subtitle2 = $subtitle . 'for ' . $propertyID . ' - ' . getPropertyName($propertyID);
            $header = new ReportHeader('Invoice Activity Report', $subtitle2, $prepared);
            $header->subText = $view->items['periodFrom'] . ' to ' . $view->items['periodTo'];
            $header->xPos = $report->hMargin;
            $header->yPos = $report->pageHeight - $report->vMargin;
            $report->attachObject('header', $header);
            $footer = new TraccFooter(null, 'Invoice Activity Report', $report->pageSize);
            $report->attachObject('footer', $footer);
        }

        if ($format == FILETYPE_XLS) {
            $xlsPropertyDivider = 'PROPERTY : ' . $propertyID . ' - ' . getPropertyName($propertyID);
            $report->renderLine_bold(['transactionDate' => $xlsPropertyDivider]);
        }
        $report->preparePage();

        $report->renderHeader();
        $report->printRowLines = true;

        //                $report->renderData_custom ($data[$propertyID]);
        foreach ($data[$propertyID] as $row) {
            if ($row['invoiceNumber']) {
                $report->resetColumns();
                $report->addColumn('transactionDate', 'Date', 40, 'left');
                $report->addColumn('leaseID', 'Lease Code', 40, 'left');
                $report->addColumn('leaseName', 'Lease Name', 65, 'left');
                $report->addColumn('invoiceNumber', 'Invoice #', 60, 'left');
                $report->addColumn('purchaseNumber', 'PO #', 40, 'left');
                $report->addColumn('dueDate', 'Due Date', 40, 'left');
                $report->addColumn('fromDate', 'From Date', 40, 'left');
                $report->addColumn('toDate', 'To Date', 40, 'left');
                $report->addColumn('accountID', 'Acct Code', 40, 'left');
                $report->addColumn('description', 'Description', 65, 'left');
                $report->addColumn('transType', 'Type', 30, 'left');
                $report->addColumn('period', 'Period', 30, 'left');
                $report->addColumn('year', 'Year', 40, 'right');
                $report->addColumn('net', 'Net', 55, 'right', $numberFormat);
                $report->addColumn('gst', 'GST', 55, 'right', $numberFormat);
                $report->addColumn('gross', 'Gross', 55, 'right', $numberFormat);
            } else {
                $report->resetColumns();
                $report->addColumn('transactionDate', 'Date', 40, 'left');
                $report->addColumn('leaseID', 'Lease Code', 40, 'left');
                $report->addColumn('leaseName', 'Lease Name', 65, 'left');
                $report->addColumn('invoiceNumber', 'Invoice #', 60, 'left');
                $report->addColumn('purchaseNumber', 'PO #', 40, 'left');
                $report->addColumn('dueDate', 'Due Date', 40, 'left');
                $report->addColumn('fromDate', 'From Date', 40, 'left');
                $report->addColumn('toDate', 'To Date', 40, 'left');
                $report->addColumn('accountID', 'Acct Code', 40, 'left');
                $report->addColumn('description', 'Description', 65, 'left');
                $report->addColumn('transType', 'Type', 30, 'left');
                $report->addColumn('period', 'Period', 30, 'left');
                $report->addColumn('year', 'Year', 40, 'right');
                $report->addColumn('net', 'Net', 55, 'right');
                $report->addColumn('gst', 'GST', 55, 'right');
                $report->addColumn('gross', 'Gross', 55, 'right');
            }
            $report->renderLine_custom($row);
        }
        /******************************end*************************/
        $report->clean();
        $report->endPage();
        $i++;
    }

    $report->close();


    if (! $count) {
        $subtitle2 = $subtitle . 'for ' . $properties[0] . ' - ' . getPropertyName($properties[0]);
        $header = new ReportHeader('Invoice Activity Report', $subtitle2, $prepared);
        $header->xPos = $report->hMargin;
        $header->yPos = $report->pageHeight - $report->vMargin;
        $report->attachObject('header', $header);

        $report->preparePage();

        $report->renderHeader();
        $report->printRowLines = true;

        /******************************end*************************/
        $report->clean();
        $report->endPage();

        $report->close();
    }

    $context['ownerReportFile'] = $filePath;
}

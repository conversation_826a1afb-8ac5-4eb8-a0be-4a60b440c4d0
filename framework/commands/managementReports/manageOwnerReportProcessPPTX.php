<?php

set_time_limit(0);
ini_set('MAX_EXECUTION_TIME', '-1');

use PhpOffice\PhpPresentation\PhpPresentation;
use PhpOffice\PhpPresentation\IOFactory;
use PhpOffice\PhpPresentation\Style\Color as colorStyling;

function manageOwnerReportProcessPPTX(&$context)
{
    global $dbh, $sess;
    $jasperInclude = [];
    $zipCounter = 0;

    include_once 'lib/reportLib/reportincludesAll.php';
    include_once 'lib/ReportLib/commonReportFunctions.php';
    include_once 'functions/ownerReportProcessFunctions.php';
    include_once 'functions/page1PropertyReportFunctions.php';
    include_once 'functions/page2DebtorsDetailFunctions.php';
    include_once 'functions/pdfHeaders.php';

    if (! glob("{$pathPrefix}{$clientDirectory}/pptx/")) {
        mkdir("{$pathPrefix}{$clientDirectory}/pptx/");
        mkdir("{$pathPrefix}{$clientDirectory}/pptx/" . DOC_OWNERSTATEMENT . '/');
    }


    $view = new UserControl(userViews(), '', '');

    $view->bindAttributesFrom($_REQUEST);

    require_once 'lib/phppresentation/src/PhpPresentation/Autoloader.php';
    \PhpOffice\PhpPresentation\Autoloader::register();
    require_once 'lib/phppresentation/src/Common/Autoloader.php';
    \PhpOffice\Common\Autoloader::register();

    $objPHPPowerPoint = new PhpPresentation();

    $textAlign_left = 'l';
    $textAlign_center = 'ctr';
    $verticalAlign_center = 'ctr';

    $fill_solid = 'solid';
    $coloring = new colorStyling();

    $propertyCount = 0;
    if ($view->items['property']) {
        $propertyCount = count(deserializeParameters($view->items['property']));
    }

    if ($propertyCount) {

        $report = dbGetReport_management($reportType);

        $title = $report['description'];
        $date = TODAY;
        $periodFrom = $view->items['periodFrom'];
        $periodTo = $view->items['periodTo'];

        $reportingPeriodFrom = $view->items['reportingPeriodFrom'];
        $reportingPeriodTo = $view->items['reportingPeriodTo'];

        $periodDescription = $view->items['reportPeriod'];
        $reportDescription = $view->items['description'];

        $properties = deserializeParameters($view->items['property']);
        foreach ($properties as $ownerReportIndex => $propertyID) {
            $page = 0;
            $propertyName = getPropName($propertyID);
            $filenameDescription = $view->items['filename_description'];
            $calendar = dbGetPeriod($propertyID, $periodFrom);

            $currentPeriod = $calendar['period'];
            $currentYear = $calendar['year'];

            if (isset($view->items['reporttype'])) {
                $reportType = $view->items['reporttype'];
            }

            // //////INCLUDE FILES
            $includes = dbGetSubReports_management($reportType);
            $subReports = explode(',', $view->items['sub_report']);


            $reportCount = 1;
            foreach ($includes as $s) {
                $sub_report_check = 0;
                if ($view->items['is_merged'] == '1') {
                    $sub_report_check = dbCheckSubreportInclusion($propertyID, $currentPeriod, $currentYear, $reportType, $s['sub_report_id']);
                }

                if (($view->items['is_merged'] != '1' && in_array($s['sub_report_id'], $subReports)) || ($view->items['is_merged'] == '1' && $sub_report_check)) {

                    $powerpointFile = str_replace('.php', 'PPTX.php', $s['sub_report_file_path']);
                    // if(in_array($s['sub_report_file_path'], ["propertyReport/Lease_Equity/holdoverExpiryReport.php"])){
                    if (glob(BASEPATH . '/framework/commands/managementreports/' . $powerpointFile)) {
                        if ($reportCount == 1) {
                            $currentSlide = $objPHPPowerPoint->getActiveSlide();
                        } else {
                            $currentSlide = $objPHPPowerPoint->createSlide();
                        }
                        require str_replace('.php', 'PPTX.php', $s['sub_report_file_path']);
                    }
                    // }

                    $reportCount++;
                }
            }
            // /////END INCLUDE FILES
        }
    }

    $time = time();
    $fileName = 'customPropertyReporting_' . $propertyID . '_' . $time;

    $_filePath = "{$pathPrefix}{$clientDirectory}/pptx/" . DOC_OWNERSTATEMENT . "/{$fileName}.pptx";
    $_downloadPath = "{$clientDirectory}/pptx/" . DOC_OWNERSTATEMENT . "/{$fileName}.pptx";

    $oWriterPPTX = IOFactory::createWriter($objPHPPowerPoint, 'PowerPoint2007');
    $oWriterPPTX->save($_filePath);

    echo urlencode(base64_encode('../reports/' . $_downloadPath));
}

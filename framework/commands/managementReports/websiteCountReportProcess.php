<?php

/**
 * Process information gathered in order to generate a door count report.
 *
 * @param  $context  array Mandatory. Referenced array containing data needed to continue.
 *
 * <AUTHOR> <PERSON>
 *
 * @since 2015-07-31
 **/
function websiteCountReportProcess($context)
{
    global $clientDirectory, $monthName, $pathPrefix;

    if (! isPostback()) {
        $view = new MasterPage(userViews(), '/managementReports/websiteCountReportProcess.html');
        $view->setSection($context['module']);
    } else {
        $view = new UserControl(userViews(), '/managementReports/websiteCountReportProcess.html');
    }

    if ($context[IS_TASK]) {
        $view->bindAttributesFrom($context);
        extract($context);
    } else {
        $view->bindAttributesFrom($_REQUEST);
        $view->bindAttributesFrom($context);
    }

    $yearPrior = $view->items['yearPrevious'] - 1;
    $dataCount = dbCountWebsiteCount($view->items['propertyID'], '01/01/' . $view->items['yearCurrent'], '31/12/' . $view->items['yearCurrent']) + dbCountWebsiteCount($view->items['propertyID'], '01/01/' . $view->items['yearPrevious'], '31/12/' . $view->items['yearPrevious']);

    $queue = new Queue(10);
    if ($dataCount > THRESHOLD_EXCELREPORT) {
        $queue->add($_SESSION['clientID'], $_SESSION['un'], 'command=' . $view->items['command'] . '&module=' . $view->items['module'], $_REQUEST);
    }

    if (($context[IS_TASK] || $dataCount <= THRESHOLD_EXCELREPORT) && $dataCount) {
        // $dateFormat = 'd/mm/yyyy;@';
        $dateFormat = 'mmmm dd (dddd)';
        $format = $view->items['format'];
        $numberFormat = '#,##0_);[Red](#,##0);-_)';
        $percentageFormat = '#,##0.00%;[Red](#,##0.00%);-_)';
        $useFormula =  false;

        if (! $context[DOC_MASTER]) {
            $logoFile = dbGetClientLogo();
            $logoPath = "assets/clientLogos/$logoFile";
            $file =  'DigitalWebsiteCountReport_' . date('Ymd') . '.' . $format;
            $filePath = "{$pathPrefix}{$clientDirectory}/$format/DigitalWebsiteCountReport_/$file";
            $downloadPath = "$clientDirectory/$format/DigitalWebsiteCountReport_/$file";
        }

        switch ($format) {
            case FILETYPE_XLS:
                $report = new XLSDataReport($filePath, 'Digital Website Count Report');
                $report->enableFormatting = true;
                switch ($view->items['colorScheme']) {
                    case 'Medium Blue':
                        $report->totalFillColor = $report->fillColor = 'e8eaf1';
                        $report->baseColor = '406ec7';
                        break;
                    case 'Indigo':
                        $report->totalFillColor = $report->fillColor = 'eae7f0';
                        $report->baseColor = '5a3ec1';
                        break;
                    case 'Magenta':
                        $report->totalFillColor = $report->fillColor = 'f1e8f1';
                        $report->baseColor = 'c241ca';
                        break;
                    case 'Pinkish Red':
                        $report->totalFillColor = $report->fillColor = 'f2e9ec';
                        $report->baseColor = 'e94b82';
                        break;
                    case 'Red-Orange':
                        $report->totalFillColor = $report->fillColor = 'f3ecea';
                        $report->baseColor = 'fe6f52';
                        break;
                    case 'Orange':
                        $report->totalFillColor = $report->fillColor = 'f3efea';
                        $report->baseColor = 'fea252';
                        break;
                    case 'Yellow-Orange':
                        $report->fillColor = 'fec452';
                        $report->baseColor = '000000';
                        $report->totalFillColor = 'ffe7b8';
                        break;
                    case 'Yellow':
                        $report->totalFillColor = $report->fillColor = 'feea52';
                        $report->baseColor = '000000';
                        break;
                    case 'Greenish-Yellow':
                        $report->totalFillColor = $report->fillColor = 'f1f2e9';
                        $report->baseColor = 'd8ee4c';
                        break;
                    case 'Yellow-Green':
                        $report->totalFillColor = $report->fillColor = 'ebf1e8';
                        $report->baseColor = '7fd143';
                        break;
                    case 'Blue-Green':
                        $report->totalFillColor = $report->fillColor = 'e8f1ed';
                        $report->baseColor = '42cd85';
                        break;
                    case 'Navy Blue':
                        $report->fillColor = '366092';
                        $report->baseColor = 'ffffff';
                        $report->totalFillColor = 'c0cfe3';
                        break;
                    case 'Bluish Cyan':
                    default:
                        $report->totalFillColor = $report->fillColor = 'e8eef1';
                        $report->baseColor = '43a5d1';
                        break;
                }
                $report->changeColorScheme($report->baseColor, $report->fillColor, $report->totalFillColor);
                break;
        }

        if (date('L', strtotime("$yearPrior-01-01"))) {
            $dateRange = new DatePeriod(new DateTime("$yearPrior-01-01"), new DateInterval('P1D'), new DateTime($yearPrior + 1 . '-01-01'));
        } elseif (date('L', strtotime($view->items['yearPrevious'] . '-01-01'))) {
            $dateRange = new DatePeriod(new DateTime($view->items['yearPrevious'] . '-01-01'), new DateInterval('P1D'), new DateTime($view->items['yearPrevious'] + 1 . '-01-01'));
        } else {
            $dateRange = new DatePeriod(new DateTime($view->items['yearCurrent'] . '-01-01'), new DateInterval('P1D'), new DateTime($view->items['yearCurrent'] + 1 . '-01-01'));
        }

        $sheetIndex = 0;
        foreach ($view->items['propertyID'] as $propertyID) {
            $dataDailyRender =  [];
            $report->line = 1;
            $propertyInfo = dbGetPropertyDetails($propertyID);

            $dataCurrent = dbGetWebsiteCountReport($propertyID, '01/01/' . $view->items['yearCurrent'], '31/12/' . $view->items['yearCurrent'], 'No');
            $dataPrevious = $view->items['yearPrevious'] != $view->items['yearCurrent'] ? dbGetWebsiteCountReport($propertyID, '01/01/' . $view->items['yearPrevious'], '31/12/' . $view->items['yearPrevious'], 'No') : [];

            foreach ($dateRange as $date) {
                $dataDailyRender[$date->format('md')]['date'] = $date->format('F d (l)');
            }
            ksort($dataDailyRender);

            if (count($dataCurrent) || count($dataPrevious)) {
                $dataPrior = dbGetWebsiteCountReport($propertyID, "01/01/$yearPrior", "31/12/$yearPrior", 'No');



                $dataMonthlyRender = $doors = $doorsCurrent = $doorsPrevious = $doorPrior =  [];
                foreach ($dataCurrent as $v) {
                    $date = date('md', totimestamp($v['date']));
                    $dateMonth = date('F', totimestamp($v['date']));
                    $doors[$v['doorID']] = $v['website'];
                    $dataDailyRender[$date][$v['doorID'] . $view->items['yearCurrent']] = $v['doorCount'];
                    // if (!$useFormula)

                    $doorsCurrent[$v['doorID']] += $v['doorCount'];
                    $dataMonthlyRender[$dateMonth][$v['doorID'] . $view->items['yearCurrent']] += $v['doorCount'];
                    $dataDailyRender[$date]['total' . $view->items['yearCurrent']] += $v['doorCount'];
                    $dataMonthlyRender[$dateMonth]['total' . $view->items['yearCurrent']] += $v['doorCount'];

                }
                foreach ($dataPrevious as $v) {
                    $date = date('md', totimestamp($v['date']));
                    $dateMonth = date('F', totimestamp($v['date']));
                    $doors[$v['doorID']] = $v['website'];
                    $dataDailyRender[$date][$v['doorID'] . $view->items['yearPrevious']] = $v['doorCount'];
                    // if (!$useFormula)

                    $doorsPrevious[$v['doorID']] += $v['doorCount'];
                    $dataMonthlyRender[$dateMonth][$v['doorID'] . $view->items['yearPrevious']] += $v['doorCount'];
                    $dataDailyRender[$date]['total' . $view->items['yearPrevious']] += $v['doorCount'];
                    $dataMonthlyRender[$dateMonth]['total' . $view->items['yearPrevious']] += $v['doorCount'];

                }


                // Monthly
                $report->setSheetDetails($sheetIndex, "$propertyID-Monthly", ($sheetIndex === 0) ? false : true);
                $report->columns =  [];
                $report->printer();
                $report->line = 1;
                $report->renderHeaderDetails($propertyInfo['propertyName'] . "\n" . $propertyInfo['propertyDescription'] . "\nDigital Website Count Report\nFY " . $view->items['yearPrevious'] . ' - ' . $view->items['yearCurrent']);

                if ($useFormula) {
                    $total =  [];
                }
                $line1 = $line2 =  [];

                $columnPosition = ($view->items['yearPrevious'] != $view->items['yearCurrent'] ? 2 : 1);
                $report->addColumn('date', '', 123, 'right', $dateFormat);
                foreach ($doors as $k => $v) {
                    $report->addColumn($k . $view->items['yearPrevious'], $view->items['yearPrevious'], 60, 'right', $numberFormat);
                    $report->addColumn($k . $view->items['yearCurrent'], $view->items['yearCurrent'], 60, 'right', $numberFormat);
                    $report->addColumn($k . 'Var', 'Var %', 60, 'right', $percentageFormat);


                    $linePosition = 9;
                    foreach ($monthName as $monthNumber => $month) {
                        if (! $useFormula) {
                            if (! $dataMonthlyRender[$month][$k . $view->items['yearPrevious']]) {
                                $dataMonthlyRender[$month][$k . $view->items['yearPrevious']] = 0;
                            }
                            if (! $dataMonthlyRender[$month][$k . $view->items['yearCurrent']]) {
                                $dataMonthlyRender[$month][$k . $view->items['yearCurrent']] = 0;
                            }
                            $dataMonthlyRender[$month][$k . 'Var'] = ($dataMonthlyRender[$month][$k . $view->items['yearPrevious']] && $dataMonthlyRender[$month][$k . $view->items['yearCurrent']]) ? ($dataMonthlyRender[$month][$k . $view->items['yearCurrent']] / $dataMonthlyRender[$month][$k . $view->items['yearPrevious']]) - 1 : 0;
                        }


                        if ($view->items['yearPrevious'] == $view->items['yearCurrent']) {
                            $dataMonthlyRender[$month][$k . 'Var']  = 0;
                        }

                        $linePosition++;
                    }

                    if ($view->items['yearPrevious'] != $view->items['yearCurrent']) {
                        $line1[$k . $view->items['yearPrevious']] =
                        [
                            'value' => $v,
                            'mergecells' => $columnPosition + 2,
                        ];
                        $line2[$k . $view->items['yearPrevious']] =
                        [
                            'value' => 'Count',
                            'mergecells' => $columnPosition + 2,
                        ];
                    } else {

                        $line1[$k . $view->items['yearCurrent']] =
                        [
                            'value' => $v,
                            'mergecells' => $columnPosition + 2,
                        ];
                        $line2[$k . $view->items['yearCurrent']] =
                        [
                            'value' => 'Count',
                            'mergecells' => $columnPosition + 2,
                        ];

                    }
                    $columnPosition += ($view->items['yearPrevious'] != $view->items['yearCurrent'] ? 3 : 2);
                }


                $report->renderCustomLine($line1, array_merge($report->styleHeader, $report->styleCenter));


                $report->renderCustomLine($line2, array_merge($report->styleHeader, $report->styleCenter));

                $report->renderHeader();

                $linePosition = 9;
                foreach ($monthName as $month) {
                    $columnPosition = ($view->items['yearPrevious'] != $view->items['yearCurrent'] ? 2 : 1);
                    $dataMonthlyRender[$month]['date'] = $month;
                    if (! $useFormula) {

                        if (! $dataMonthlyRender[$month]['total' . $view->items['yearPrevious']]) {
                            $dataMonthlyRender[$month]['total' . $view->items['yearPrevious']] = 0;
                        }
                        if (! $dataMonthlyRender[$month]['total' . $view->items['yearCurrent']]) {
                            $dataMonthlyRender[$month]['total' . $view->items['yearCurrent']] = 0;
                        }
                        $dataMonthlyRender[$month]['totalVar'] = ($dataMonthlyRender[$month]['total' . $view->items['yearPrevious']] && $dataMonthlyRender[$month]['total' . $view->items['yearCurrent']]) ? ($dataMonthlyRender[$month]['total' . $view->items['yearCurrent']] / $dataMonthlyRender[$month]['total' . $view->items['yearPrevious']]) - 1 : 0;

                        $cellRefPreviousMAT = $cellRefCurrentMAT =  [];
                        foreach ($doors as $k => $v) {
                            $cellRefPreviousMAT[] = cellReference($linePosition, $columnPosition + 4);
                            $cellRefCurrentMAT[] = cellReference($linePosition, $columnPosition + ($view->items['yearPrevious'] != $view->items['yearCurrent'] ? 5 : 4));
                            $columnPosition += ($view->items['yearPrevious'] != $view->items['yearCurrent'] ? 7 : 5);
                        }


                    }

                    $linePosition++;
                }


                foreach ($monthName as $month) {
                    foreach ($dataMonthlyRender as $v) {
                        if ($month == $v['date']) {
                            $report->renderLine($v);
                        }
                    }
                }


                if ($useFormula) {


                } else {
                    $columnPosition = 2;
                    $cellRefPreviousMAT = $cellRefCurrentMAT =  [];
                    foreach ($doors as $k => $v) {
                        $cellRefPreviousMAT[] = cellReference($linePosition, $columnPosition + 4);
                        $cellRefCurrentMAT[] = cellReference($linePosition, $columnPosition + ($view->items['yearPrevious'] != $view->items['yearCurrent'] ? 5 : 3));
                        $columnPosition += ($view->items['yearPrevious'] != $view->items['yearCurrent'] ? 7 : 5);
                    }

                }

                if ($format == FILETYPE_XLS) {
                    $report->renderTotal($total);
                } else {
                    $report->renderSubTotal($total);
                }

                $report->setColumnWidth(A, 11);
                $columnPosition = 2;
                foreach ($doors as $k => $v) {
                    //	$report->hideCell (numberToLetter ($columnPosition));
                    for ($i = 0; $i <= 6; $i++) {
                        $report->setColumnWidth(numberToLetter($columnPosition + $i), 11);
                    }
                    $columnPosition += ($view->items['yearPrevious'] != $view->items['yearCurrent'] ? 7 : 5);
                }
                // $report->hideCell (numberToLetter ($columnPosition));
                for ($i = 0; $i <= 6; $i++) {
                    $report->setColumnWidth(numberToLetter($columnPosition + $i), 13);
                }

                $sheetIndex++;
            }
        }

        if ($format != FILETYPE_SCREEN) {
            $report->clean();
            $report->endPage();
            $report->close();
        }
    } elseif (! $dataCount) {
        $view->items['statusMessage'] = 'Nothing to report.';
    }

    if (! $context[DOC_MASTER]) {
        if ($context[IS_TASK]) {
            $email = new EmailTemplate('views/emails/basicEmail.html', SYSTEMURL);
            $attachment =  [];
            $attachment[0]['file'] = REPORTPATH . '/' . $pdfDownloadLink;
            $attachment[0]['content_type'] = 'application/pdf';
            sendMail($_SESSION['email'], $_SESSION['first_name'] . ' ' . $_SESSION['last_name'], $email->toString(), 'Report', $attachment);
            logData('Emailed report to ' . $_SESSION['email']);
            $context[TASK_COMPLETE] = true;
            $view->items['statusMessage'] = 'Due to its complexity, this report has been scheduled and will be emailed to you on completion.';
        } else {
            $view->items['downloadPath'] = $downloadPath;
        }
        $view->render();
    }
}

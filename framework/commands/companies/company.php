<?php

function company(&$context)
{
    global $taskClass, $taskDescription, $sess;

    $userpermission = new userpermission();
    $result = $userpermission->userHasPageAccess(__FUNCTION__);

    // Redirect from old to new company form
    if (! $view->items['action']) {
        if (isset($_GET['companyID'])) {
            header('Location: ?command=company_v2&module=companies&companyID=' . $_GET['companyID']);
            exit();
        } elseif (isUserType(USER_TRACC)) {
            header('Location: ?command=company_v2&module=companies');
            exit();
        }
    }

    if (! isPostback()) {
        $view = new MasterPage(userViews(), '/companies/company.html');
        $view->setSection($context['module']);
        if (isset($_GET['companyID'])) {   // var_dump($_GET);
            $view->items['companyID'] = $_GET['companyID'];
            $view->items['propertyID'] = $_GET['propertyID'];
            $view->items['leaseID'] = $_GET['leaseID'];
            $view->items['action'] = 'view';
            $view->items['isFromManageLeaseTag'] = true;
        }
    } else {
        $view = new UserControl(userViews(), '/companies/company.html');
    }

    $view->bindAttributesFrom($_REQUEST);
    if ($view->items['isFromManageLeaseTag']) {
        $view->items['isFromManageLease'] = true;
    }

    if (isset($view->items['returnPage']) && $view->items['returnPage'] == 'viewEFT') {
        $view->items['isFromViewEFT'] = true;
    }

    define('COMPANY_EXISTING', 1);
    define('COMPANY_NEW', 2);
    define('COMPANY_REJECTED', 3);

    //	$is_cirrus_fm = false;
    $currDB = dbGetDatabase($_SESSION['clientID']);
    //	if(isset($sess->get('dbList')[$sess->get('databaseID')])){
    //		$currDB = $sess->get('dbList')[$sess->get('databaseID')];
    //		if(isset($currDB['cirrusfm'])){
    $is_cirrus_fm = (bool) $currDB['cirrusfm'];
    //		}
    //	}
    $view->items['is_cirrus_fm'] = $is_cirrus_fm;

    if (! isset($view->items['companyID_'])) {
        $view->items['companyID_'] = $view->items['companyID'];
    }

    // var_dump($view->items);
    // Start Email Address Book
    $email_cen_sendmail_setting = dbGetEmailCenParamSetting('SENDMAIL');
    $email_cen_setup_setting = dbGetEmailCenParamSetting('SETUPEMAIL');
    $view->items['email_cen_send'] = false;
    $view->items['email_cen_setup'] = false;
    if ($email_cen_sendmail_setting && $email_cen_sendmail_setting['parameterDescription'] == '1') {
        $view->items['email_cen_send'] = true;
    }

    if ($email_cen_setup_setting && $email_cen_setup_setting['parameterDescription'] == '1') {
        $view->items['email_cen_setup'] = true;
    }

    // End Email Address Book

    // Start Email Address Book
    $tableName = (isUserType(USER_TRACC)) ? 'pmco_company' : 'temp_pmco_company';
    $paramOwnGenList = dbGetParams('OWN_GEN');
    $paramTenGenList = dbGetParams('TEN_GEN');
    $paramSupGenList = dbGetParams('SUP_GEN');
    $primaryCompanyID = (isUserType(USER_TRACC)) ? dbGetCompanyID($view->items['companyID_']) : dbGetTempCompanyID($view->items['companyID_']);
    // End Email Address Book

    /** Get Security Settings - START */
    $view->items['security_settings'] = dbGetSecuritySettings();
    $view->items['flag_authorizer_submitter'] = ($view->items['security_settings']['authorizer_submitter_flag'] === '1');
    $view->items['flag_default_payment_method'] = ($view->items['security_settings']['default_payment_method_flag'] === '1');
    /** echo 'Authorizer default: '.gettype($view->items['flag_authorizer_submitter']).'</br>';
    echo 'Payment method default: '.$view->items['flag_default_payment_method'].'</br>'; **/
    if (! isset($view->items['orderNumberRequire'])) {
        $view->items['orderNumberRequire'] = 0;
    }

    /** Get Security Settings - END */

    // Client Country
    $client_country = dbGetDefaultCountry();

    switch ($view->items['action']) {
        case 'changeCountry':
            if ($client_country != 'AU' && $view->items['paymentMethod'] == PAY_BPAY) {
                $view->items['paymentMethod'] = PAY_CHQ;
                $view->items['bpayBillerCode'] = '';
            }

            break;
        case 'selectOwner':
            // if ($view->items['owner']) $view->items['supplier'] = true;
            break;
        case 'companyType':
            if ($view->items['companyStatus'] == COMPANY_NEW) {
                $clear =  ['companyID' => '', 'companyName' => '', 'address' => '', 'city' => '', 'state' => '', 'postCode' => '', 'directBanking' => '', 'bsbNumber' => '', 'bankAccountNumber' => '', 'businessNumber' => '', 'taxCode' => '', 'owner' => '', 'ownerPaymentAccount' => '', 'ownerChart' => '', 'supplier' => '', 'supplierType' => '', 'supplierAgent' => '', 'debtor' => '', 'chequeDays' => '', 'active' => '', 'email' => '', 'invoiceMask' => '', 'companyGroup' => ''];
                $view->bindAttributesFrom($clear);
            }

            break;
        case 'view':
            $context['companyID'] = $view->items['companyID'] = $view->items['companyID_'];
            $company = (isUserType(USER_TRACC)) ? dbGetCompany($view->items['companyID']) : dbGetTempCompany($view->items['companyID']);

            $view->bindAttributesFrom($company);
            break;
        case 'load':
            echo 'LOAD';
            // header("Location: ?command=company_v2&module=companies&companyID=" . $view->items['companyID'] . "&action=view");
            // exit();
            $company = dbGetTempCompany($view->items['companyID']);
            // Start Email Address Book
            $tableName = 'temp_pmco_company';
            $primaryCompanyID = dbGetTempCompanyID($view->items['companyID_']);
            // End Email Address Book
            $view->items['review'] = 1;
            $view->items['companyStatus'] = COMPANY_NEW;
            $view->bindAttributesFrom($company);
            $view->items['active'] = 1;
            break;
        case 'checkABN':
            if ($client_country == 'AU') {
                $check = dbGetABN($view->items['businessNumber']);
                if ($check && (! isset($view->items['companyID']) || isset($view->items['companyID']) && $view->items['companyID'] != $check['pmco_code'])) {
                    $view->items['abnMessage'] = 'That ' . $_SESSION['country_default']['business_label'] . " is already assigned to a company in the system ({$check['pmco_code']})";
                }
            }

            break;
        case 'lookupABN':
            $check = dbGetABN($view->items['businessNumber']);
            if ($check && (! isset($view->items['companyID']) || isset($view->items['companyID']) && $view->items['companyID'] != $check['pmco_code'])) {
                $view->items['abnMessage'] = 'That ' . $_SESSION['country_default']['business_label'] . " is already assigned to a company in the system ({$check['pmco_code']})";
            }

            if ($view->items['businessNumber'] != '' && strlen($view->items['businessNumber']) == 11 && $client_country == 'AU') {
                try {
                    $abnlookup = new abnlookup();
                    $result = $abnlookup->searchByABN($view->items['businessNumber']);
                    if (isset($result->ABRPayloadSearchResults->response->businessEntity->mainTradingName)) {
                        $name = $result->ABRPayloadSearchResults->response->businessEntity->mainTradingName->organisationName;
                    }

                    if (isset($result->ABRPayloadSearchResults->response->businessEntity->legalName)) {
                        $name = $result->ABRPayloadSearchResults->response->businessEntity->legalName->familyName . ', ' . $result->ABRPayloadSearchResults->response->businessEntity->legalName->givenName . ' ' . $result->ABRPayloadSearchResults->response->businessEntity->legalName->otherGivenName;
                    }

                    if (isset($result->ABRPayloadSearchResults->response->businessEntity->businessName)) {
                        $name = $result->ABRPayloadSearchResults->response->businessEntity->businessName->organisationName;
                    }

                    if (isset($result->ABRPayloadSearchResults->response->businessEntity->mainName)) {
                        $name = $result->ABRPayloadSearchResults->response->businessEntity->mainName->organisationName;
                    }

                    $ABNDetails = new stdClass();
                    if (empty($view->items['companyName'])) {
                        $view->items['companyName'] =  ucwords(strtolower($name));
                    } else {
                        $ABNDetails->name 	= ucwords(strtolower($name));
                    }

                    if (empty($view->items['state'])) {
                        $view->items['state'] =  $result->ABRPayloadSearchResults->response->businessEntity->mainBusinessPhysicalAddress->stateCode;
                    } else {
                        $ABNDetails->state 	= $result->ABRPayloadSearchResults->response->businessEntity->mainBusinessPhysicalAddress->stateCode;
                    }

                    if (empty($view->items['postCode'])) {
                        $view->items['postCode'] =  $result->ABRPayloadSearchResults->response->businessEntity->mainBusinessPhysicalAddress->postcode;
                    } else {
                        $ABNDetails->postal 	= $result->ABRPayloadSearchResults->response->businessEntity->mainBusinessPhysicalAddress->postcode;
                    }

                    if ($result->ABRPayloadSearchResults->response->businessEntity->goodsAndServicesTax->effectiveFrom && $result->ABRPayloadSearchResults->response->businessEntity->goodsAndServicesTax->effectiveFrom != '') {
                        $abnTaxCode = 'TAXABLE';
                        $abnTaxStatus = $_SESSION['country_default']['tax_label'] . ' TAXED';
                    } else {
                        $abnTaxCode = 'GSTFREE';
                        $abnTaxStatus = $_SESSION['country_default']['tax_label'] . ' FREE';
                    }

                    if (empty($view->items['taxCode'])) {
                        $view->items['taxCode'] =  $abnTaxCode;
                    } else {
                        $ABNDetails->gst 	= $abnTaxCode;
                        $ABNDetails->taxStatus 	= $abnTaxStatus;
                    }

                    $view->items['ABNDetails'] = $ABNDetails;
                    $view->items['ABNResult'] = $result->ABRPayloadSearchResults->response->businessEntity;
                    $view->items['ABNResult']->name = ucwords(strtolower($name));
                    $view->items['ABNResult']->taxStatus = $abnTaxStatus;
                    $view->items['ABNStatus'] = $result->ABRPayloadSearchResults->response->businessEntity->entityStatus->entityStatusCode;
                } catch (Exception $e) {
                    // echo $e->getMessage();
                }
            }

            break;
        case 'lookupCompanyName':
            if ($view->items['companyName'] != '' && $client_country == 'AU') {
                try {
                    $abnlookup = new abnlookup();
                    $results = $abnlookup->searchbyName($view->items['companyName']);
                    $array = [];
                    if ($results->ABRPayloadSearchResults->response->searchResultsList->numberOfRecords > 0) {
                        if ($results->ABRPayloadSearchResults->response->searchResultsList->numberOfRecords > 1) {
                            foreach ($results->ABRPayloadSearchResults->response->searchResultsList->searchResultsRecord as $result) {
                                $item = [];
                                $item['abn'] = $result->ABN->identifierValue;

                                if (isset($result->legalName)) {
                                    $item['formattedName'] = $result->ABN->identifierValue . ' - ' . $result->legalName->fullName;
                                }

                                if (isset($result->otherTradingName)) {
                                    $item['formattedName'] = $result->ABN->identifierValue . ' - ' . $result->otherTradingName->organisationName;
                                }

                                if (isset($result->mainTradingName)) {
                                    $item['formattedName'] = $result->ABN->identifierValue . ' - ' . $result->mainTradingName->organisationName;
                                }

                                if (isset($result->businessName)) {
                                    $item['formattedName'] = $result->ABN->identifierValue . ' - ' . $result->businessName->organisationName;
                                }

                                if (isset($result->mainName)) {
                                    $item['formattedName'] = $result->ABN->identifierValue . ' - ' . $result->mainName->organisationName;
                                }

                                $array[] = $item;
                            }
                        } else {
                            $result = $results->ABRPayloadSearchResults->response->searchResultsList->searchResultsRecord;

                            $item = [];
                            $item['abn'] = $result->ABN->identifierValue;

                            if (isset($result->legalName)) {
                                $item['formattedName'] = $result->ABN->identifierValue . ' - ' . $result->legalName->fullName;
                            }

                            if (isset($result->otherTradingName)) {
                                $item['formattedName'] = $result->ABN->identifierValue . ' - ' . $result->otherTradingName->organisationName;
                            }

                            if (isset($result->mainTradingName)) {
                                $item['formattedName'] = $result->ABN->identifierValue . ' - ' . $result->mainTradingName->organisationName;
                            }

                            if (isset($result->businessName)) {
                                $item['formattedName'] = $result->ABN->identifierValue . ' - ' . $result->businessName->organisationName;
                            }

                            if (isset($result->mainName)) {
                                $item['formattedName'] = $result->ABN->identifierValue . ' - ' . $result->mainName->organisationName;
                            }

                            $array[] = $item;
                        }
                    }

                    $view->items['abnSearchResults'] = $array;
                    $view->items['abnSearchSelected'] = '';
                } catch (Exception $e) {
                    // echo $e->getMessage();
                }
            }

            break;
        case 'selectABN':
            $check = dbGetABN($view->items['selectedABN']);
            if ($check && (! isset($view->items['companyID']) || isset($view->items['companyID']) && $view->items['companyID'] != $check['pmco_code'])) {
                $view->items['abnMessage'] = 'That ' . $_SESSION['country_default']['business_label'] . " is already assigned to a company in the system ({$check['pmco_code']})";
            }

            try {
                $abnlookup = new abnlookup();
                $result = $abnlookup->searchByABN($view->items['selectedABN']);
                if (isset($result->ABRPayloadSearchResults->response->businessEntity->otherTradingName)) {
                    $name = $result->ABRPayloadSearchResults->response->businessEntity->otherTradingName->organisationName;
                }

                if (isset($result->ABRPayloadSearchResults->response->businessEntity->mainTradingName)) {
                    $name = $result->ABRPayloadSearchResults->response->businessEntity->mainTradingName->organisationName;
                }

                if (isset($result->ABRPayloadSearchResults->response->businessEntity->legalName)) {
                    $name = $result->ABRPayloadSearchResults->response->businessEntity->legalName->familyName . ', ' . $result->ABRPayloadSearchResults->response->businessEntity->legalName->givenName . ' ' . $result->ABRPayloadSearchResults->response->businessEntity->legalName->otherGivenName;
                }

                if (isset($result->ABRPayloadSearchResults->response->businessEntity->businessName)) {
                    $name = $result->ABRPayloadSearchResults->response->businessEntity->businessName->organisationName;
                }

                if (isset($result->ABRPayloadSearchResults->response->businessEntity->mainName)) {
                    $name = $result->ABRPayloadSearchResults->response->businessEntity->mainName->organisationName;
                }

                $view->items['businessNumber'] =  $view->items['selectedABN'];
                $view->items['companyName'] =  ucwords(strtolower($name));
                $view->items['state'] =  $result->ABRPayloadSearchResults->response->businessEntity->mainBusinessPhysicalAddress->stateCode;
                $view->items['postCode'] =  $result->ABRPayloadSearchResults->response->businessEntity->mainBusinessPhysicalAddress->postcode;
                $view->items['ABNStatus'] = $result->ABRPayloadSearchResults->response->businessEntity->entityStatus->entityStatusCode;
                if ($result->ABRPayloadSearchResults->response->businessEntity->goodsAndServicesTax->effectiveFrom && $result->ABRPayloadSearchResults->response->businessEntity->goodsAndServicesTax->effectiveFrom != '') {
                    $abnTaxCode = 'TAXABLE';
                    $abnTaxStatus = $_SESSION['country_default']['tax_label'] . ' TAXED';
                } else {
                    $abnTaxCode = 'GSTFREE';
                    $abnTaxStatus = $_SESSION['country_default']['tax_label'] . ' FREE';
                }

                $view->items['taxCode'] =  $abnTaxCode;
                $view->items['ABNResult'] = $result->ABRPayloadSearchResults->response->businessEntity;
                $view->items['ABNResult']->taxStatus = $abnTaxStatus;
                $view->items['ABNResult']->name = ucwords(strtolower($name));
            } catch (Exception $e) {
                // echo $e->getMessage();
            }

            break;
        case 'hideABNResults':
            unset($view->items['abnSearchResults']);
            unset($view->items['abnSearchSelected']);
            break;
        case 'checkCompany':
            // to raise validation error when anything is found
            // check (TEXT_KEY) raise validation error
            if (! isValid($view->items['companyID'], TEXT_CODE, false)) {
                $validationErrors[] = "There is an error with the company code. Please ensure it does not have any spaces or the following characters in the code . ? / _ ; : , \ ' &";
            }

            if (strlen($view->items['companyID']) > 10) {
                $validationErrors[] = 'Company code allows 10 characters only';
            }

            /** Get Security Settings - START */
            // $view->items['security_settings'] = dbGetSecuritySettings();
            // $view->items['flag_authorizer_submitter'] = ($view->items['security_settings']['authorizer_submitter_flag'] === '1' ? true : false);
            // $view->items['flag_default_payment_method'] = ($view->items['security_settings']['default_payment_method_flag'] === '1' ? true : false);
            /*echo 'Authorizer default: '.gettype($view->items['flag_authorizer_submitter']).'</br>';
            echo 'Payment method default: '.$view->items['flag_default_payment_method'].'</br>';*/

            // to fix the bug when apostrophes are added. when the user presses tab and submit. checked. see task #640556
            $companyID = addslashes($view->items['companyID']);
            // to remove the backslash and other symbols
            $companyID = trim($companyID, '\\/"?.;');
            $companyID = rawurlencode(stripslashes(trim($companyID, "'")));
            $companyLive = dbGetCompany($companyID);
            $company = dbGetTempCompany($companyID);
            if (! $company) {
                $company = dbGetCompany($companyID);
                $view->items['existsLive'] = true;
            }

            if ($company || $companyLive) {
                $validationErrors[] = 'Company already exist.';
                $view->items['existsLive'] = true;
                $view->items['companyStatus'] = COMPANY_NEW;
            }

            break;
        case 'switchView':
            $view->items['companyStatus'] = COMPANY_NEW;
            $company = (isUserType(USER_TRACC)) ? dbGetCompany($view->items['companyID']) : dbGetTempCompany($view->items['companyID']);
            $view->bindAttributesFrom($company);
            break;
        case 'reject':
            if (empty($view->items['comments'])) {
                $validationErrors[] = 'You need to enter your feedback.';
            }

            if (count($validationErrors) == 0) {
                dbTempCompanyStatus($view->items['companyID'], 3, $_SESSION['un'], $view->items['comments']);
                $company = dbGetTempCompany($view->items['companyID']);
                $userDetails = dbGetUser($company['createUser']);
                $email = new EmailTemplate('views/emails/rejectCompanyEmail.html', SYSTEMURL);
                $email->items['companyID'] = $view->items['companyID'];
                $email->items['notes'] = explode("\r\n", $view->items['comments']);
                sendMail($userDetails['email'], $userDetails['fullName'], $email->toString(), 'New Company Rejected ' . $view->items['companyID']);
                executeCommand('companyTasks', 'companies');
                exit();
            }

            break;
        case 'approve':
            if (($view->items['oldCompanyID'] != $view->items['companyID']) && ($view->items['companyStatus'] == COMPANY_NEW)) {
                // change old company id to new company id
                if (strlen($view->items['companyID']) > 10) {
                    $validationErrors[] = 'Your company code cannot be longer than 10 characters';
                }

                $tempCompany = dbGetTempCompany($view->items['companyID']);
                if ($tempCompany) {
                    // $validationErrors[] = 'Your company code already exist.';
                    $validationErrors[] = 'The chosen company code already exists.';
                }

                if (noErrors($validationErrors)) {
                    dbUpdateCompanyCode($view->items['oldCompanyID'], $view->items['companyID']);
                    $view->items['oldCompanyID'] = $view->items['companyID'];
                }
            }

            if (noErrors($validationErrors)) {
                // if (!isValid ($view->items['companyID'], TEXT_KEY, false)) $validationErrors[] = 'Company Code is invalid.';
                if (! isValid($view->items['companyID'], TEXT_CODE, false)) {
                    $validationErrors[] = "There is an error with the company code. Please ensure it does not have any spaces or the following characters in the code . ? / _ ; : , \ ' &";
                }

                if ($view->items['companyStatus'] == COMPANY_NEW) {
                    $company = (isUserType(USER_TRACC)) ? dbGetCompany($view->items['companyID']) : dbGetTempCompany($view->items['companyID']);
                    if ($company) {
                        // $validationErrors[] = 'Your company code already exist.';
                        $validationErrors[] = "The chosen company code already exists. Please chose another code and click 'submit' again.";
                    }
                }

                if (! ($view->items['owner'] || $view->items['supplier'] || $view->items['debtor'])) {
                    $validationErrors[] = 'choose a company type';
                }

                if (! isValid($view->items['companyName'], TEXT_HTML, false)) {
                    $validationErrors[] = 'Company Name is invalid.';
                }

                // if (!isValid($view->items['businessNumber'], TEXT_INT, true)) $validationErrors[] = 'ABN is invalid.';

                // ABN VALIDATION
                if (! cdf_validate($view->items['businessNumber'], 'business_length') || ! isValid($view->items['businessNumber'], TEXT_INT, false)) {
                    $validationErrors[] = $_SESSION['country_default']['business_label'] . ' should be ' . $_SESSION['country_default']['business_length'] . ' digits (no spaces)';
                }

                // -- if its an Australian company and the GST status is taxable, force an ABN if they are not a debtor
                if (! $view->items['debtor'] && ($client_country == 'AU' && $view->items['taxCode'] == TAXABLE)) {
                    if (! $view->items['businessNumber']) {
                        $validationErrors[] = $_SESSION['country_default']['business_label'] . ' is required for taxable companies.';
                    }

                    // if (strlen($view->items['businessNumber']) != 11) $validationErrors[] = 'ABN must be 11 digits (no spaces)';
                }

                if ($view->items['email'] && (! isValid($view->items['email'], TEXT_EMAIL_MULTI, false))) {
                    $validationErrors[] = 'One or more email addresses are invalid. Please ensure all email addresses are correctly formatted and separated by semicolons.';
                }

                $comma = ',';
                $colon = ':';
                if ($view->items['email'] && (strpos($view->items['email'], $comma) !== false ||  strpos($view->items['email'], $colon) !== false)) {
                    $validationErrors[] = 'When entering multiple emails, please ensure that emails are separated by ";".';
                }

                if (! isValid($view->items['address'], TEXT_HTML, false)) {
                    $validationErrors[] = 'Address is required.';
                }

                if (! isValid($view->items['city'], TEXT_HTML, false)) {
                    $validationErrors[] = 'City is required.';
                }

                // if (!isValid($view->items['state'], TEXT_HTML, false)) $validationErrors[] = 'State is required.';
                // -- note that overseas addresses can have alpha and dashes in postcodes - need to account for it at some stage
                // if (!isValid($view->items['postCode'],TEXT_INT, false)) $validationErrors[] = 'Post Code must be a number';

                // if (($view->items['country'] == 'AU') && (strlen($view->items['postCode']) != 4) and !isValid($view->items['postCode'], TEXT_INT, false)) $validationErrors[] = 'Post Code must be 4 digits';
                // else if (!isValid($view->items['postCode'], TEXT_LOOSE, false)) $validationErrors[] = 'Post Code may only contain numbers or letters';

                // VALIDATE STATE & POST CODE
                if (cdf_isShown('display_state', $view->items['country']) && ! isValid($view->items['state'], TEXT_LOOSE, false)) {
                    $validationErrors[] = 'State is required.';
                }

                $postcodeValidation = cdf_validate_postcode($view->items['postCode'], $view->items['country']);
                if (! $postcodeValidation->valid) {
                    $validationErrors[] = $postcodeValidation->error;
                }

                // -- email isnt required
                // if (!isValid($view->items['email'],TEXT_EMAIL,true)) $validationErrors[] = 'email address';
                if ($view->items['paymentMethod'] == PAY_EFT) {
                    // if (!isValid($view->items['bsbNumber'], TEXT_INT_ONLY, false)) $validationErrors[] = 'Your BSB is incorrect (must be 6 numbers with no dash or spaces)';
                    // if (strlen($view->items['bsbNumber']) != 6) $validationErrors[] = 'BSB number be 6 digits only.';
                    // if (!isValid($view->items['bankAccountNumber'], TEXT_INT, false)) $validationErrors[] = 'bank account number';
                    if (! isValid($view->items['bankAccountName'], TEXT_ALPHANUMERIC_WHITESPACE, false)) {
                        $validationErrors[] = 'bank account name must be entered as alphanumeric without the following characters / ? * - &';
                    }

                    if (! isValid($view->items['bankName'], TEXT_LOOSE, true)) {
                        $validationErrors[] = 'Bank Name is required for EFT.';
                    }

                    // VALIDATE BSB & BANK ACCOUNT NUMBER
                    if (getDisplayBsbFromSession() &&
                        (! isValid($view->items['bsbNumber'], TEXT_INT_ONLY, false) ||
                            strlen($view->items['bsbNumber']) != getBsbLengthFromSession())
                    ) {
                        $validationErrors[] = 'Your ' . getBsbLabelFromSession(
                        ) . ' is incorrect (must be ' . getBsbLengthFromSession() . ' numbers with no dash or spaces)';
                    }

                    if (! cdf_validate($view->items['bankAccountNumber'], 'bank_account_length') || ! isValid($view->items['bankAccountNumber'], TEXT_INT, false)) {
                        $validationErrors[] = 'Bank Account Number should be ' . $_SESSION['country_default']['bank_account_length'] . ' digits or less.';
                    }
                }

                if ($view->items['paymentMethod'] == PAY_BPAY && ! isValid($view->items['bpayBillerCode'], TEXT_LOOSE, false)) {
                    $validationErrors[] = 'Biller Code is required for BPay.';
                }

                // if ($view->items['owner']) if (!isValid($view->items['ownerPaymentAccount'],TEXT_INT, false)) $validationErrors[] = 'Owner payment account is invalid.';
                if (noErrors($validationErrors)) {
                    if (! isValid($view->items['taxCode'], TEXT_KEY, false) && (! $view->items['debtor'] || $view->items['debtor'] && ($view->items['owner'] || $view->items['supplier']))) {
                        $validationErrors[] = 'Tax Status is invalid.';
                    }

                    $company = [];
                    $company['companyID'] = strtoupper($view->items['companyID']);
                    $company['companyName'] = $view->items['companyName'];
                    $company['address'] = $view->items['address'];
                    $company['city'] = $view->items['city'];
                    $company['state'] = $view->items['state'];
                    $company['country'] = $view->items['country'];
                    $company['postCode'] = $view->items['postCode'];
                    $company['supplier'] = (int) $view->items['supplier'];
                    $company['debtor'] = (int) $view->items['debtor'];
                    // $company['directBanking'] = (int) $view->items['directBanking'];
                    $company['bsbNumber'] = $view->items['bsbNumber'];
                    $company['bankAccountNumber'] = $view->items['bankAccountNumber'];
                    $company['bankAccountName'] = $view->items['bankAccountName'];
                    $company['bankName'] = $view->items['bankName'];
                    $company['businessNumber'] = $view->items['businessNumber'];
                    $company['taxCode'] = $view->items['taxCode'];
                    $company['owner'] = $view->items['owner'];
                    $company['ownerPaymentAccount'] = dbGetOwnerRemittancePaymentAccount();
                    // '2902';
                    $company['ownerChart'] = (int) $view->items['ownerChart'];
                    $company['supplier'] = (int) $view->items['supplier'];
                    $company['supplierType'] = $view->items['supplierType'];
                    $company['supplierAgent'] = $view->items['supplierAgent'];
                    $company['debtor'] = (int) $view->items['debtor'];
                    $company['chequeDays'] = (int) $view->items['chequeDays'];
                    $company['active'] = $view->items['active'];
                    $company['email'] = $view->items['email'];
                    $company['invoiceMask'] = $view->items['invoiceMask'];
                    $company['paymentMethod'] = (int) $view->items['paymentMethod'];
                    $company['directBanking'] = ($company['paymentMethod'] == PAY_CHQ) ? 0 : 1;
                    $company['bpayBillerCode'] = $view->items['bpayBillerCode'];
                    $company['directDebit'] = $view->items['directDebit'];
                    $company['orderNumberRequire'] = (int) $view->items['orderNumberRequire'];
                    $company['companyGroup'] = $view->items['companyGroup'];
                    if (isset($view->items['cirrusfm'])) {
                        $company['cirrusfm'] = (int) $view->items['cirrusfm'];
                    }

                    if (isUserType(USER_TRACC)) {
                        $new_company_id = dbAddCompany($company);
                        dbInsertTempCompanyContact($company['companyID']);
                        // dbInsertTempCompanyContactDetail($company['companyID']);
                        // Start Email Address Book
                        $old_company_id = dbGetTempCompanyID($company['companyID']);
                        dbTempToLiveCompanyEmailCentralise($new_company_id, $old_company_id);
                        dbTempToLiveCompanyPhoneDetails($company['companyID']);
                        // End Email Address Book
                        dbTempCompanyStatus($view->items['companyID'], 1, $_SESSION['un']);

                        $view->items['message'] = 'Company Successfully approved';
                        $company = dbGetTempCompany($view->items['companyID']);
                        $userDetails = dbGetUser($company['createUser']);
                        $email = new EmailTemplate('views/emails/processCompanyEmail.html', SYSTEMURL);
                        $email->items['companyID'] = $view->items['companyID'];
                        sendMail($userDetails['email'], $userDetails['fullName'], $email->toString(), 'New Company Processed ' . $view->items['companyID']);
                    } else {
                        $company['createUser'] = $_SESSION['email'];
                        dbAddTempCompany($company);
                        dbTempCompanyStatus($view->items['companyID'], 0, $_SESSION['un']);
                        $view->items['message'] = 'Company submitted for processing.';
                    }

                    $view->items['companyID'] = null;
                    $view->items['review'] = 0;
                    $view->items['isFromManageLease'] = false;
                    $view->items['isFromManageLeaseTag'] = false;
                } elseif (isUserType(USER_TRACC)) {
                    $view->items['message'] = 'There were problems approving the company.';
                } else {
                    $view->items['message'] = 'There were problems submitting the company.';
                }
            }

            break;
        case 'saveUpdate':
            $origCompanyData = [];

            if (strlen($view->items['companyID']) > 10) {
                $validationErrors[] = 'Your company code cannot be longer than 10 characters';
            }

            if (! isValid($view->items['companyID'], TEXT_CODE, false)) {
                $validationErrors[] = "There is an error with the company code. Please ensure it does not have any spaces or the following characters in the code . ? / _ ; : , \ ' &";
            }

            if ($view->items['companyStatus'] == COMPANY_NEW) {
                if (dbGetCompany($view->items['companyID'])) {
                    // $validationErrors[] = 'Your company code already exists';
                    $validationErrors[] = "The chosen company code already exists. Please choose another code and click 'submit' again.";
                }

                if ((! isUserType(USER_TRACC)) && (dbGetTempCompany(addslashes($view->items['companyID'])))) {
                    // $validationErrors[] = 'Your company code already exists';
                    $validationErrors[] = "The chosen company code already exists. Please choose another code and click 'submit' again.";
                }
            } elseif (isUserType(USER_CLIENT) && $view->items['companyStatus'] != COMPANY_REJECTED) {
                $company = dbGetTempCompany(addslashes($view->items['companyID']));

                if ($company) {
                    // $validationErrors[] = 'Your company code already exist.';
                    $validationErrors[] = "The chosen company code already exists. Please choose another code and click 'submit' again.";
                } else {
                    $company = dbGetCompany($view->items['companyID']);

                    if ($company) {
                        // $validationErrors[] = 'Your company code already exist2.';
                        $validationErrors[] = "The chosen company code already exists. Please choose another code and click 'submit' again.";
                    }
                }
            } elseif (isUserType(USER_CLIENT) && $view->items['companyStatus'] == COMPANY_REJECTED) {
                $company = dbGetCompany($view->items['companyID']);

                if ($company) {
                    // $validationErrors[] = 'Your company code already exist.';
                    $validationErrors[] = "The chosen company code already exists. Please choose another code and click 'submit' again.";
                }
            }

            if (! ($view->items['owner'] || $view->items['supplier'] || $view->items['debtor'])) {
                $validationErrors[] = 'choose a company type';
            }

            // if (!isValid($view->items['companyName'], TEXT_ALPHANUMERIC_WHITESPACE, false)) $validationErrors[] = 'company name';
            if (! isValid($view->items['companyName'], TEXT_HTML, false)) {
                $validationErrors[] = 'Company name is required.';
            }

            if (($view->items['owner'] || $view->items['supplier']) && ! isValid($view->items['businessNumber'], TEXT_INT, false)) {
                $validationErrors[] = $_SESSION['country_default']['business_label'] . ' field is invalid.';
            }

            // ABN VALIDATION
            if (isset($view->items['businessNumber']) && (! cdf_validate($view->items['businessNumber'], 'business_length') || ! isValid($view->items['businessNumber'], TEXT_INT, false))) {
                $validationErrors[] = $_SESSION['country_default']['business_label'] . ' should be ' . $_SESSION['country_default']['business_length'] . ' digits (no spaces)';
            }

            if ($view->items['email'] && (! isValid($view->items['email'], TEXT_EMAIL_MULTI, false))) {
                $validationErrors[] = 'One or more email addresses are invalid. Please ensure all email addresses are correctly formatted and separated by semicolons.';
            }

            $comma = ',';
            $colon = ':';
            if ($view->items['email'] && (strpos($view->items['email'], $comma) !== false ||  strpos($view->items['email'], $colon) !== false)) {
                $validationErrors[] = 'When entering multiple emails, please ensure that emails are separated by ";".';
            }

            if (! isValid($view->items['address'], TEXT_HTML, false)) {
                $validationErrors[] = 'Address is required.';
            }

            if (! isValid($view->items['city'], TEXT_HTML, false)) {
                $validationErrors[] = 'City is required.';
            }

            // if (!isValid($view->items['state'],TEXT_HTML, false)) $validationErrors[] = 'State is required.';
            // -- note that overseas addresses can have alpha and dashes in postcodes - need to account for it at some stage
            // if (!isValid($view->items['postCode'],TEXT_INT, false)) $validationErrors[] = 'Post Code must be a number';

            // if (($view->items['country'] == 'AU') && (strlen($view->items['postCode']) != 4)) $validationErrors[] = 'Post Code must be 4 digits';
            // else if (!isValid($view->items['postCode'],TEXT_LOOSE, false)) $validationErrors[] = 'Post Code may only contain numbers or letters';

            // VALIDATE STATE & POST CODE
            if (cdf_isShown('display_state', $view->items['country']) && ! isValid($view->items['state'], TEXT_LOOSE, false)) {
                $validationErrors[] = 'State is required.';
            }

            $postcodeValidation = cdf_validate_postcode($view->items['postCode'], $view->items['country']);
            if (! $postcodeValidation->valid) {
                $validationErrors[] = $postcodeValidation->error;
            }

            // -- email isnt required
            // if (!isValid($view->items['email'],TEXT_EMAIL,true)) $validationErrors[] = 'email address';
            if ($view->items['paymentMethod'] == PAY_EFT) {
                if ($view->items['companyStatus'] == COMPANY_NEW) {
                    // if (!isValid ($view->items['bsbNumber'], TEXT_INT_ONLY, false)) $validationErrors[] = 'Your BSB is incorrect (must be 6 numbers with no dash or spaces)';
                    // if (strlen($view->items['bsbNumber']) != 6) $validationErrors[] = 'BSB number be 6 digits only.';
                    // if (!isValid($view->items['bankAccountNumber'],TEXT_INT, false)) $validationErrors[] = 'Bank Account Number is invalid.';
                    if (! isValid($view->items['bankAccountName'], TEXT_ALPHANUMERIC_WHITESPACE, false)) {
                        $validationErrors[] = 'bank account name must be entered as alphanumeric without the following characters / ? * - &';
                    }

                    if (! isValid($view->items['bankName'], TEXT_LOOSE, true)) {
                        $validationErrors[] = 'Bank Name is invalid.';
                    }

                    // VALIDATE BSB & BANK ACCOUNT NUMBER
                    if (getDisplayBsbFromSession() &&
                        (! isValid($view->items['bsbNumber'], TEXT_INT_ONLY, false) ||
                            strlen($view->items['bsbNumber']) !== getBsbLengthFromSession())
                    ) {
                        $validationErrors[] = 'Your ' . getBsbLabelFromSession(
                        ) . ' is incorrect (must be ' . getBsbLengthFromSession() . ' numbers with no dash or spaces)';
                    }

                    if (! cdf_validate($view->items['bankAccountNumber'], 'bank_account_length') || ! isValid($view->items['bankAccountNumber'], TEXT_INT, false)) {
                        $validationErrors[] = 'Bank Account Number should be ' . $_SESSION['country_default']['bank_account_length'] . ' digits or less.';
                    }
                } elseif ((! isValid($view->items['bsbNumber'], TEXT_INT_ONLY, false)) ||
                (strlen($view->items['bsbNumber']) != 6) ||
                (! isValid($view->items['bankAccountNumber'], TEXT_INT, false)) ||
                // (!isValid($view->items['bankAccountName'],TEXT_ALPHANUMERIC_WHITESPACE, false)) ||
                (! isValid($view->items['bankName'], TEXT_LOOSE, true))) {
                    $validationErrors[] = 'You need to submit a Change Banking Details form before this change can be applied.';
                }
            }

            if ($view->items['paymentMethod'] == PAY_BPAY) {
                if ($view->items['companyStatus'] == COMPANY_NEW) {
                    if (! isValid($view->items['bpayBillerCode'], TEXT_LOOSE, false)) {
                        $validationErrors[] = 'Biller Code is invalid.';
                    }
                } elseif (! isValid($view->items['bpayBillerCode'], TEXT_LOOSE, false)) {
                    $validationErrors[] = 'You need to submit a Change Banking Details form before this change can be applied.';
                }
            }

            if ($view->items['owner']) {
                //	if (!isValid ($view->items['ownerPaymentAccount'], TEXT_INT, false)) $validationErrors[] = 'owner payment account';
            }

            if (! isValid($view->items['taxCode'], TEXT_KEY, false) && (! $view->items['debtor'] || $view->items['debtor'] && ($view->items['owner'] || $view->items['supplier']))) {
                $validationErrors[] = 'Tax Code is invalid.';
            }

            if (noErrors($validationErrors)) {
                $company = [];
                $company['companyID'] = strtoupper($view->items['companyID']);
                $company['companyName'] = $view->items['companyName'];
                $company['address'] = $view->items['address'];
                $company['city'] = $view->items['city'];
                $company['state'] = $view->items['state'];
                $company['country'] = $view->items['country'];
                $company['postCode'] = $view->items['postCode'];
                $company['supplier'] = (int) $view->items['supplier'];
                $company['debtor'] = (int) $view->items['debtor'];
                // $company['directBanking'] = (int) $view->items['directBanking'];
                $company['bsbNumber'] = $view->items['bsbNumber'];
                $company['bankAccountNumber'] = $view->items['bankAccountNumber'];
                $company['bankAccountName'] = $view->items['bankAccountName'];
                $company['bankName'] = $view->items['bankName'];
                $company['businessNumber'] = $view->items['businessNumber'];

                /*if($view->items['debtor'] && !$view->items['owner'] && !$view->items['supplier']) {
                    $company['taxCode'] = 'GSTFREE';
                } else {*/
                $company['taxCode'] = $view->items['taxCode'];
                // }

                $company['owner'] = $view->items['owner'];
                $company['bpayBillerCode'] = $view->items['bpayBillerCode'];
                $company['ownerPaymentAccount'] = dbGetOwnerRemittancePaymentAccount(); // '2902';
                $company['ownerChart'] = (int) $view->items['ownerChart'];
                $company['supplier'] = (int) $view->items['supplier'];
                $company['supplierType'] = $view->items['supplierType'];
                $company['supplierAgent'] = $view->items['supplierAgent'];
                $company['debtor'] = (int) $view->items['debtor'];
                $company['directDebit'] = (int) $view->items['directDebit'];
                $company['orderNumberRequire'] = (int) $view->items['orderNumberRequire'];
                $company['companyGroup'] = $view->items['companyGroup'];
                // # CHECK IF FM DISABLED/INACTIVED ##
                if (isUserType(USER_TRACC)) {
                    $deactivated = false;
                    $fm_disabled = false;
                    $db_active = dbGetCompanyActives($view->items['companyID']);

                    if ((int) $db_active['is_active'] == 1 && (int) $view->items['active'] == 0) {
                        $company['inactive_date'] = 'GETDATE()';
                        $company['inactive_user'] = $_SESSION['un'];
                    }

                    if ((int) $db_active['is_on_cirrusfm'] == 1 && (! isset($view->items['cirrusfm']) || (int) $view->items['cirrusfm'] == 0)) {
                        $company['cirrusfm_disabled_date'] = 'GETDATE()';
                        $company['cirrusfm_disabled_user'] = $_SESSION['un'];
                    }
                }

                if (isset($view->items['cirrusfm'])) {
                    $set_fm = (int) $view->items['cirrusfm'];
                    $company['cirrusfm'] = $set_fm;
                } else {
                    $company['cirrusfm'] = 0;
                }

                if ($view->items['companyStatus'] == COMPANY_NEW) {
                    if (((int) $view->items['paymentMethod'] == PAY_CHQ)) {
                        $company['chequeDays'] = 4;
                        $company['paymentMethod'] = 3;
                    } else {
                        $company['chequeDays'] = (int) $view->items['chequeDays'];
                        $company['paymentMethod'] = isset($view->items['paymentMethod']) ? (int) $view->items['paymentMethod'] : PAY_CHQ;
                    }
                } else {
                    $company['chequeDays'] = (int) $view->items['chequeDays'];
                    $company['paymentMethod'] = isset($view->items['paymentMethod']) ? (int) $view->items['paymentMethod'] : PAY_CHQ;
                }

                // $company['active'] = $view->items['active'];
                $company['active'] = 1;
                $company['email'] = $view->items['email'];
                $company['invoiceMask'] = $view->items['invoiceMask'];
                $company['directBanking'] = ($company['paymentMethod'] == PAY_CHQ) ? 0 : 1;

                // ***** for new tax invoice added by arjay 6/28/2017*****
                $company['pmcoDisableInvoiceDates'] = intval($view->items['showFromToDate']);
                $company['pmcoInvoiceAddress'] = $view->items['invoiceAddress'];
                // ********************************************************
                if ($view->items['companyStatus'] == COMPANY_NEW || $view->items['companyStatus'] == COMPANY_REJECTED) {
                    if (isUserType(USER_TRACC)) {
                        $company['createUser'] = $_SESSION['un'];
                        $primaryCompanyID = dbAddCompany($company);
                        $view->items['message'] = 'Your company has been created.';
                        // $primaryCompanyID = $primaryCompanyID['NewID'];
                    } else {
                        $company['createUser'] = $_SESSION['un'];

                        if ($view->items['companyStatus'] == COMPANY_REJECTED) {
                            // Log data updates on Company (Temp)
                            $companyTable = 'temp_pmco_company';
                            $companyData = dbGetTempCompany(addslashes($view->items['companyID'])); // Get temp company data from temp_pmco_company
                            $companyUpdate = compareCompanyData($companyData, $company, $view->items['companyID'], $companyTable); // Compare the new data array from data saved in the database
                            dbAddCompanyUpdateLogEntry($companyUpdate); // Record all changes in the pmco_company_update_log

                            dbSaveTempCompany($company);
                            dbTempCompanyStatus($view->items['companyID'], 0, $_SESSION['un'], $view->items['comments']);
                        } else {
                            $company['status'] = 0;
                            $primaryCompanyID = dbAddTempCompany($company);
                        }

                        // $primaryCompanyID = $primaryCompanyID['NewID'];
                        $view->items['message'] = 'Your company has been submitted.';
                        // $view->items['companyID'] = null;
                        $emailAddress = dbGetParam('ACCMGR', 'COMPANY');
                        $email = new EmailTemplate('views/emails/newCompanyEmail.html', SYSTEMURL);
                        $email->bindAttributesFrom($view->items);
                        if (isValid($emailAddress, TEXT_EMAIL, false)) {
                            sendMail($emailAddress, null, $email->toString(), 'New Company on ' . $_SESSION['database'] . ' by ' . $_SESSION['un']);
                        }
                    }

                    // Start Email Address Book
                    $addressBookPrimaryId = $primaryCompanyID;
                    // End Email Address Book
                } else {
                    if (isUserType(USER_TRACC)) {
                        $company['updateUser'] = $_SESSION['un'];

                        // Log data updates on Company
                        $companyTable = 'pmco_company';
                        $companyData = dbGetCompany($view->items['companyID']); // Get company data from pmco_company
                        $companyUpdate = compareCompanyData($companyData, $company, $view->items['companyID'], $companyTable); // Compare the new data array from data saved in the database
                        dbAddCompanyUpdateLogEntry($companyUpdate); // Record all changes in the pmco_company_update_log

                        dbSaveCompany($company);
                        $view->items['message'] = 'Your company has been saved.';
                    } else {
                        // Log data updates on Company (Temp)
                        $companyTable = 'temp_pmco_company';
                        $companyData = dbGetTempCompany(addslashes($view->items['companyID'])); // Get temp company data from temp_pmco_company
                        $companyUpdate = compareCompanyData($companyData, $company, $view->items['companyID'], $companyTable); // Compare the new data array from data saved in the database
                        dbAddCompanyUpdateLogEntry($companyUpdate); // Record all changes in the pmco_company_update_log

                        dbSaveTempCompany($company);
                        $view->items['message'] = 'Your company has been saved.';
                    }

                    // Start Email Address Book
                    $addressBookPrimaryId = $primaryCompanyID;
                    // End Email Address Book
                }

                // Start Email Address Book
                $emailParamTypeCode = '';
                if ($company['debtor'] == 0) {
                    $emailParamTypeCode .= "'TEN',";
                }

                if ($company['supplier'] == 0) {
                    $emailParamTypeCode .= "'SUP',";
                }

                if ($company['owner'] == 0) {
                    $emailParamTypeCode .= "'OWN',";
                }

                $primaryCompanyIDGet = $primaryCompanyID;
                if ($view->items['companyStatus'] == COMPANY_NEW) {
                    $primaryCompanyIDGet = '';
                }

                $emailParamTypeCode = substr($emailParamTypeCode, 0, strlen($emailParamTypeCode) - 1);
                // dbDeleteEmailAddressListContact("pmcj_c_phone", $company['companyID'],$emailParamTypeCode);
                if ($view->items['email_cen_setup']) {
                    dbDeleteEmailAddressList($tableName, $primaryCompanyID);
                    dbInsertEmailAddressList($tableName, $primaryCompanyID, '', $view->items['email']);

                    foreach ($paramOwnGenList as $aRowAddBook) {
                        $parameterID = $aRowAddBook['parameterID'];
                        $concatArr = 'OWN_GEN::' . $parameterID . '::' . $tableName . '::' . $primaryCompanyIDGet;
                        $getArr = $view->items[$concatArr];

                        if ($getArr) {
                            dbInsertEmailAddressList($tableName, $primaryCompanyID, $parameterID, $view->items['email']);
                        }
                    }

                    foreach ($paramTenGenList as $aRowAddBook) {
                        $parameterID = $aRowAddBook['parameterID'];
                        $concatArr = 'TEN_GEN::' . $parameterID . '::' . $tableName . '::' . $primaryCompanyIDGet;
                        $getArr = $view->items[$concatArr];

                        if ($getArr) {
                            dbInsertEmailAddressList($tableName, $primaryCompanyID, $parameterID, $view->items['email']);
                        }
                    }

                    foreach ($paramSupGenList as $aRowAddBook) {
                        $parameterID = $aRowAddBook['parameterID'];
                        $concatArr = 'SUP_GEN::' . $parameterID . '::' . $tableName . '::' . $primaryCompanyIDGet;
                        $getArr = $view->items[$concatArr];

                        if ($getArr) {
                            dbInsertEmailAddressList($tableName, $primaryCompanyID, $parameterID, $view->items['email']);
                        }
                    }
                } else {
                    dbUpdateEmailAddressList($tableName, $primaryCompanyID, $view->items['email']);
                }

                // End Email Address Book

                $updatedCompany = (isUserType(USER_TRACC)) ? dbGetCompany(strtoupper($view->items['companyID'])) : dbGetTempCompany(strtoupper($view->items['companyID']));
                $view->bindAttributesFrom($updatedCompany);
            }

            break;
        case 'backToManageLease':
            $lease_beta_flag = dbGetParam('DASHBETA', 'LEASE'); // default 1
            if ((isset($view->items['isFromManageLease'])) && ($view->items['isFromManageLease']) && $lease_beta_flag != '2') {
                if (isUserType(USER_TRACC)) {
                    clientSideRedirect('?module=leases&command=lease_page_v2&property_code=' . $view->items['propertyID'] . '&lease_code=' . $view->items['leaseID']);
                } else {
                    clientSideRedirect('?module=leases&command=lease_summary_page&property_code=' . $view->items['propertyID'] . '&lease_code=' . $view->items['leaseID']);
                }

                // clientSideRedirect('?module=leases&command=home&propertyID=' . $view->items['propertyID'] . '&leaseID=' . $view->items['leaseID']);
            } else {
                clientSideRedirect('?module=leases&command=home&propertyID=' . $view->items['propertyID'] . '&leaseID=' . $view->items['leaseID']);
            }

            break;
    }


    if ($view->items['companyID']) {
        $limit = 10;
        $current_page = $view->items['current_page'] ? $view->items['current_page'] : 1;
        $properties = dbGetCompanyProperties($view->items['companyID'], $current_page, $limit);
        $total_prop = dbGetCompanyPropertiesCount($view->items['companyID']);
        $view->items['pagination_button'] = displayPaginationButton($total_prop['total'], $limit, $current_page, '&action=view&companyID=' . $view->items['companyID'], 'company');

        $property_data = [];
        $count = 0;
        foreach ($properties as $row) {
            $property_data[$count][] = ['prop_code' => $row['prop_code'],
                'prop_name' => $row['prop_name'],
                'manager' => $row['manager'],
                'file_name' => $row['file_name'],
                'share_owner' => number_format($row['share_owner'], 2)];
            if (count($property_data[$count]) == 2) {
                $count++;
            }
        }

        $view->items['property_list'] = $property_data;
    }

    if (! $view->items['action']) {
        if ($view->items['companyID']) {
            $company = (isUserType(USER_TRACC)) ? dbGetCompanyLogDates($view->items['companyID']) : dbGetTempCompanyLogDates($view->items['companyID']);
            $view->items['createDate'] = $company['createDate'];
            $view->items['updateDate'] = $company['updateDate'];
        }
    } else {
        switch ($view->items['action']) {
            case 'selectOwner':
            case 'checkCompany':
            case 'checkABN':
            case 'lookupABN':
            case 'lookupCompanyName':
            case 'selectABN':
            case 'hideABNResults':
                $company = (isUserType(USER_TRACC)) ? dbGetCompanyLogDates($view->items['companyID']) : dbGetTempCompanyLogDates($view->items['companyID']);
                $view->items['createDate'] = $company['createDate'];
                $view->items['updateDate'] = $company['updateDate'];
                break;
        }
    }

    $view->items['UserControl::Contact'] = fetchCommand('companyContacts');
    $view->items['UserControl::TempContact'] = fetchCommand('companyTempContacts');
    $view->items['UserControl::Document'] = fetchCommand('companyDocument');

    $view->items['statusList']  = [1 => 'Existing Company', 2 => 'New Company'];

    if (empty($view->items['country'])) {
        $view->items['country'] = dbGetParam('PROPDEF', 'COUNTRY');
    }

    // echo 'Company status: '.($view->items['companyStatus'] == 2 ? 'NEW COMPANY' : 'EXISTING COMPANY');
    // ## Check security setting default first - START
    if ($view->items['flag_default_payment_method'] === true) {
        if ($view->items['companyStatus'] == COMPANY_NEW) {
            // Set only option to CHEQUE if creating new company
            $view->items['paymentMethodList']  = [PAY_CHQ => 'Cheque'];
        } else {
            $view->items['paymentMethodList']  = [PAY_EFT => 'Direct Deposit (EFT)', PAY_BPAY => 'BPAY', PAY_CHQ => 'Cheque'];
            if ($client_country != 'AU') {
                unset($view->items['paymentMethodList'][PAY_BPAY]);
            }
        }
    } else {
        $view->items['paymentMethodList']  = [PAY_EFT => 'Direct Deposit (EFT)', PAY_BPAY => 'BPAY', PAY_CHQ => 'Cheque'];

        if ($client_country != 'AU') {
            unset($view->items['paymentMethodList'][PAY_BPAY]);
        }
    }

    // $view->items['paymentMethodList']  = array(PAY_EFT => 'Direct Deposit (EFT)', PAY_BPAY => 'BPAY', PAY_CHQ => 'Cheque'); //ORIGINAL
    if (! isset($view->items['paymentMethod'])) {
        $view->items['paymentMethod'] = PAY_CHQ;
    }

    $view->items['directBankingList']  = ['No', 'Yes'];
    $view->items['dayList']  = ['0' => '0', 1 => 1, 2 => 2, 3 => 3, 4 => 4]; // 5=>5, 6=>6, 7=>7 commented out by jpalala 2012-07-09
    if (! isset($view->items['chequeDays'])) {
        $view->items['chequeDays'] = 4;
    }

    if (! isset($view->items['active']) || empty($view->items['action'])) {
        $view->items['active'] = '1';
    }

    $view->items['yesNoOption'] =
    [
        '1' => 'Yes',
        '0' => 'No',
    ];
    if (isUserType(USER_TRACC)) {
        if (empty($view->items['companyStatus'])) {
            $view->items['companyStatus'] = COMPANY_EXISTING;
        }
    } elseif (empty($view->items['companyStatus'])) {
        $view->items['companyStatus'] = 2;
    }

    if (empty($view->items['ownerPaymentAccount'])) {
        $view->items['ownerPaymentAccount'] = dbGetOwnerRemittancePaymentAccount();
    }

    // '2902';
    $view->items['supplierTypeList'] = mapParameters(dbGetParams('SUPPLIER'));
    $view->items['countryList'] = dbGetCountries();

    $taxRateList = dbGetTaxRates();
    foreach ($taxRateList as $key => $value) {
        $taxRateList[$key]['taxDescription'] = str_replace('GST', $_SESSION['country_default']['tax_label'], $value['taxDescription']);
    }

    $view->items['taxRateList'] = $taxRateList;

    $view->items['compGroupList'] = mapParameters(dbGetParams('COMPGROUP'));
    $view->items['stateList'] = dbGetStates($view->items['country']);
    if (empty($view->items['state'])) {
        $view->items['state'] = dbGetParam('PROPDEF', 'STATE');
    }

    if (empty($view->items['city'])) {
        $view->items['city'] = dbGetParam('PROPDEF', 'CITY');
    }

    $view->items['companyList'] = (isUserType(USER_TRACC)) ? dbGetCompanies() : dbGetTempCompanies();
    $view->items['paymentAccountList'] = dbGetAccountList(EXPENDITURE);
    $view->items['incomeAccountList'] = dbGetAccountList(INCOME);
    if (empty($view->items['oldCompanyID'])) {
        $view->items['oldCompanyID'] = $view->items['companyID'];
    }

    // Start Email Address Book
    $getEmailAddressBookList = dbGetEmailAddressList($tableName, $primaryCompanyID);
    $emailAddressBookListOwn = loadEmailAddressToArr($paramOwnGenList, $getEmailAddressBookList);
    $emailAddressBookListTen = loadEmailAddressToArr($paramTenGenList, $getEmailAddressBookList);
    $emailAddressBookListSup = loadEmailAddressToArr($paramSupGenList, $getEmailAddressBookList);
    $emailAddressBookList = [];
    $emailAddressBookList[] = loadEmailAddressArrToMain('OWN_GEN', 'Owner General', $emailAddressBookListOwn);
    $emailAddressBookList[] = loadEmailAddressArrToMain('TEN_GEN', 'Tenant General', $emailAddressBookListTen);
    $emailAddressBookList[] = loadEmailAddressArrToMain('SUP_GEN', 'Supplier General', $emailAddressBookListSup);
    $view->items['emailAddressBookList'] = $emailAddressBookList;
    $view->items['tableName'] = $tableName;
    // End Email Address Book

    // Start Company Changelog
    $companyTable = isUserType(USER_TRACC) ? 'pmco_company' : 'temp_pmco_company';

    $companyChangelog = dbGetCompanyUpdateLogEntries($companyTable, $view->items['companyID'], 'DESC');

    if (empty($companyChangelog)) {
        $view->items['companyChangelogCount'] = 0;
        $view->items['companyChangelogList'] = [];
    } else {
        $companyChangelogUsers = getUniqueCompanyUpdateLogUsers($companyChangelog);
        $logUserDetails = dbGetCompanyUpdateLogUsers($companyChangelogUsers);
        $companyChangeLogData = addFullnameToArrData($logUserDetails, $companyChangelog);

        $view->items['companyChangelogCount'] = count($companyChangeLogData ?? []);
        $view->items['companyChangelogList'] = $companyChangeLogData;
    }

    // End Company Changelog

    // Default Country
    $view->items['clientCompany'] = $client_country;

    $view->items['validationErrors'] = $validationErrors;
    $view->render();
}

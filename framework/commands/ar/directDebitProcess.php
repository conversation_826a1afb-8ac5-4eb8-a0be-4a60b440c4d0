<?php

use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Fill;

include_once __DIR__ . '/invoicePDF.php';

function buildBSB($bsb)
{
    $start = substr($bsb, 0, 3);
    $end = substr($bsb, -3, 3);

    return "{$start}-{$end}";
}

function directDebitProcess(&$context)
{
    global $sess, $pathPrefix, $clientDirectory;

    if (! isPostback()) {
        $view = new MasterPage(userViews(), '/ar/directDebitProcess.html');
        $view->setSection($context['module']);
    } else {
        $view = new UserControl(userViews(), '/ar/directDebitProcess.html');
    }

    $view->bindAttributesFrom($_REQUEST);

    if ($context[IS_TASK]) {
        $view->bindAttributesFrom($context);
        extract($context);
    } else {
        $view->bindAttributesFrom($_REQUEST);
        $propertyCount = 0;
        if ($view->items['property']) {
            $propertyCount = count(deserializeParameters($view->items['property']));
        }

        $view->items['propertyCount'] = $propertyCount;
        $view->items['command'] = 'directDebitProcess';
        $view->items['module'] = 'ar';
        $queue = new Queue(TASKTYPE_DIRECT_DEBIT);
        if ($propertyCount > THRESHOLD_DIRECTDEBIT) {
            $queue->add($_SESSION['clientID'], $_SESSION['un'], 'command=directDebitProcess&module=ar', $view->items);
        }
    }

    if (($context[IS_TASK]) || ($propertyCount <= THRESHOLD_DIRECTDEBIT)) {
        // ** HANDLE PROCESSING LOGIC
        $properties = deserializeParameters($view->items['property']);
        $endDate = $view->items['toDate'];
        $bank = dbFetchBankDetails($view->items['bankID']);

        $_filePath =  "{$pathPrefix}{$clientDirectory}/aba/" . DOC_DIRECTDEBIT . '/';
        $_downloadPath =  "{$clientDirectory}/aba/" . DOC_DIRECTDEBIT;

        $file =  'directDebit_' . date('Ymd') . '.aba';
        $filePath = $_filePath . $file;
        $downloadPath = "{$_downloadPath}/{$file}";

        $date = toTimeParts($view->items['debitDate']);
        $year = substr($date[DATE_YEAR], -2, 2);
        $date = $date[DATE_DAY] . $date[DATE_MONTH] . $year;

        // -- grab the company details

        $bankBSB = buildBSB($bank['bsbNumber']);
        $aba = new DirectEntry($bankBSB, $bank['accountID'], $bank['bankAccountName'], $filePath);
        $aba->setHeader($bank['shortName'], $bank['debitUserID'], 'tracc DD', $date);

        $total = 0;

        $logoFile = dbGetClientLogo();
        $logoPath = "assets/clientLogos/{$logoFile}";
        $_filePath_pdf = "{$pathPrefix}{$clientDirectory}/pdf/" . DOC_DIRECTDEBIT . '/';
        $_downloadPath_pdf = "{$clientDirectory}/pdf/" . DOC_DIRECTDEBIT;
        $previewDetail = '';
        $previewDetailLabel = '';
        if ($view->items['action'] == 'preview') {
            $previewDetail = '_details';
            $previewDetailLabel = ' Details';
        }

        $file_pdf =  'directDebit_' . $previewDetail . date('Ymd') . '.pdf';
        $filePath_pdf = $_filePath_pdf . $file_pdf;
        $downloadPath_pdf = "{$_downloadPath_pdf}/{$file_pdf}";
        if ($view->items['action'] == 'preview') {
            $report = new PDFDataReport($filePath_pdf, $logoPath, A4_LANDSCAPE);

            $_filePath_xlsx = "{$pathPrefix}{$clientDirectory}/xlsx/" . DOC_DIRECTDEBIT . '/';
            $_downloadPath_xlsx = "{$clientDirectory}/xlsx/" . DOC_DIRECTDEBIT;
            $file_xlsx =  'directDebit_' . $previewDetail . date('Ymd') . '.xlsx';
            $filePath_xlsx = $_filePath_xlsx . $file_xlsx;
            $downloadPath_xlsx = "{$_downloadPath_xlsx}/{$file_xlsx}";
            $xls = new XLSDataReport($filePath_xlsx, 'Direct Debit' . $previewDetailLabel . ' Report');
        } else {
            $report =  new PDFDataReport($filePath_pdf, $logoPath);
        }

        $report->multiLine = true;
        $report->printRowLines = true;
        $report->printColumnLines = true;
        $report->printBorders = true;
        $report->cache = false;
        $header = new ReportHeader('Direct Debit' . $previewDetailLabel . ' Report', $bank['bankAccountName']);
        $header->subText = $view->items['toDate'];
        $header->xPos = $report->hMargin;
        $header->yPos = $report->pageHeight - $report->vMargin;
        $report->attachObject('header', $header);
        $footer = new TraccFooter(null, 'Direct Debit' . $previewDetailLabel . ' Report', $report->pageSize);
        $report->attachObject('footer', $footer);
        if ($view->items['action'] == 'preview') {
            //			$numberFormat = 2;
            $report->addColumn('transactionDate', 'Date', 40, 'left', null, $topHeaderStyle);
            $report->addColumn('dueDate', 'Due Date', 40, 'center', null, $topHeaderStyle);
            $report->addColumn('accountID', 'Account', 30, 'left', '@', $topHeaderStyle);
            $report->addColumn('description', 'Description', 270, 'left', null, $topHeaderStyle);
            $report->addColumn('transactionType', 'Type', 30, 'left', null, $topHeaderStyle);
            $report->addColumn('invoiceNumber', 'Inv No', 30, 'left', null, $topHeaderStyle);
            $report->addColumn('dateFrom', 'From Date', 40, 'center', null, $topHeaderStyle);
            $report->addColumn('dateTo', 'To Date', 40, 'center', null, $topHeaderStyle);
            $report->addColumn('amount', 'Original (' . $_SESSION['country_default']['currency_symbol'] . ')', 60, 'right', $numberFormat, $topHeaderStyle);
            $report->addColumn('unpaidNetAmount', 'Net (' . $_SESSION['country_default']['currency_symbol'] . ')', 60, 'right', $numberFormat, $topHeaderStyle);
            $report->addColumn('unpaidTaxAmount', 'Tax (' . $_SESSION['country_default']['currency_symbol'] . ')', 60, 'right', $numberFormat, $topHeaderStyle);
            $report->addColumn('unpaidAmount', 'Gross (' . $_SESSION['country_default']['currency_symbol'] . ')', 60, 'right', $numberFormat, $topHeaderStyle);
            $report->addSubHeaderItem('property', 0, 200, 'left');

            $xls->enableFormatting = true;
            $topHeaderStyle = [
                'fill' => [
                    'type' => Fill::FILL_SOLID,
                    'color' => ['rgb' => '004c7a'],
                ],
                'font' =>  ['bold' => true, 'color' =>  ['rgb' => 'ffffff']],
                'alignment' =>  ['vertical' => Alignment::VERTICAL_CENTER,  'horizontal' => Alignment::HORIZONTAL_LEFT],
            ];

            $topHeaderRightStyle =
            [
                'fill' => [
                    'type' => Fill::FILL_SOLID,
                    'color' => ['rgb' => '004c7a'],
                ],
                'font' =>  ['bold' => true, 'color' =>  ['rgb' => 'ffffff']],
                'alignment' =>  ['vertical' => Alignment::VERTICAL_CENTER,  'horizontal' => Alignment::HORIZONTAL_RIGHT],

            ];
            $numberFormat = '#,##0.00_);(#,##0.00)';
            $xls->addColumn('transactionDate', 'Date', 40, 'left', null, $topHeaderStyle);
            $xls->addColumn('dueDate', 'Due Date', 40, 'center', null, $topHeaderStyle);
            $xls->addColumn('accountID', 'Account', 30, 'left', '@', $topHeaderStyle);
            $xls->addColumn('description', 'Description', 270, 'left', null, $topHeaderStyle);
            $xls->addColumn('transactionType', 'Type', 30, 'left', null, $topHeaderStyle);
            $xls->addColumn('invoiceNumber', 'Inv No', 30, 'left', null, $topHeaderStyle);
            $xls->addColumn('dateFrom', 'From Date', 40, 'center', null, $topHeaderStyle);
            $xls->addColumn('dateTo', 'To Date', 40, 'center', null, $topHeaderStyle);
            $xls->addColumn('amount', 'Original (' . $_SESSION['country_default']['currency_symbol'] . ')', 60, 'right', $numberFormat, $topHeaderRightStyle);
            $xls->addColumn('unpaidNetAmount', 'Net (' . $_SESSION['country_default']['currency_symbol'] . ')', 60, 'right', $numberFormat, $topHeaderRightStyle);
            $xls->addColumn('unpaidTaxAmount', 'Tax (' . $_SESSION['country_default']['currency_symbol'] . ')', 60, 'right', $numberFormat, $topHeaderRightStyle);
            $xls->addColumn('unpaidAmount', 'Gross (' . $_SESSION['country_default']['currency_symbol'] . ')', 60, 'right', $numberFormat, $topHeaderRightStyle);
            $xls->preparePage();
            $xls->renderHeader();
        }

        if ($view->items['action'] !== 'preview') {
            $report->addColumn('propertyCode', 'Property Code', 80, 'left');
            $report->addColumn('leaseCode', 'Lease Code', 80, 'left');
            $report->addColumn('leaseName', 'Lease Name', 160, 'left');
        }

        if ($view->items['action'] !== 'preview' && getDisplayBsbFromSession()) {
            $report->addColumn('bsb', getBsbLabelFromSession(), 75, 'left');
        }

        if ($view->items['action'] !== 'preview') {
            $report->addColumn('bankAccount', 'Bank Account', 70, 'left');
            $report->addColumn('amount', 'Amount', 75, 'right');
        }


        $report->preparePage();
        $report->renderHeader();
        $details = [];
        foreach ($properties as $propertyID) {

            $tenantList = dbGetTenantsByDirectDebit($propertyID, $view->items['vacantLease'], $view->items['division']);
            if ($tenantList && count($tenantList ?? []) != 0) {
                foreach ($tenantList as $tenant) {
                    $tenant_total = [];
                    $records = fetchOutstandingAmounts($tenant['debtorID'], $tenant['propertyID'], $tenant['leaseID'], $endDate, null, $view->items['filteredBy']);

                    if ($records && count($records ?? []) > 0) {
                        if (! isset($details[$propertyID]['name'])) {

                            $details[$propertyID]['name'] = $propertyID . ' - ' . $tenant['propertyName'];
                            $details[$propertyID]['amount'] = 0;
                            $details[$propertyID]['unpaidNetAmount'] = 0;
                            $details[$propertyID]['unpaidTaxAmount'] = 0;
                            $details[$propertyID]['unpaidAmount'] = 0;
                            $printProperty = false;
                        }

                        $details[$propertyID]['lease'][$tenant['leaseID']] = ['propertyID' => $tenant['propertyID'], 'leaseID' => $tenant['leaseID'],
                            'leaseName' => '(' . $tenant['leaseID'] . ') - ' . $tenant['leaseName'],
                            'data' => []];
                    } else {
                        continue;
                    }

                    if ($view->items['action'] == 'preview') {
                        $unpaidAmount = 0;
                        foreach ($records as $record) {
                            $unpaidAmount = bcadd($unpaidAmount, $record['unpaidAmount'], 2);
                        }

                        if ($unpaidAmount > 0) {
                            foreach ($records as $row) {
                                if ($row['transactionType'] == 'CRE' && $row['netAmount'] == 0) {
                                    $row['netAmount'] = $row['unpaidAmount'];
                                }

                                $y = [];
                                $y['transactionDate'] = $row['transactionDate'];
                                $y['dueDate'] = $row['dueDate'];
                                $y['accountID'] = $row['accountID'];
                                $y['description'] = $row['description'];
                                $y['transactionType'] = $row['transactionType'];
                                $y['invoiceNumber'] = $row['invoiceNumber'];
                                $y['dateFrom'] = $row['dateFrom'];
                                $y['dateTo'] = $row['dateTo'];
                                $y['amount'] = $row['amount'];
                                $y['unpaidNetAmount'] = $row['netAmount'];
                                $y['unpaidTaxAmount'] = $row['taxAmount'];
                                $y['unpaidAmount'] = $row['unpaidAmount'];

                                $details[$propertyID]['lease'][$tenant['leaseID']]['data'][] = $y;

                                $tenant_total[0] += $y['amount'];
                                $tenant_total[1] += $y['unpaidAmount'];
                                $tenant_total[2] += $y['unpaidNetAmount'];
                                $tenant_total[3] += $y['unpaidTaxAmount'];
                            }

                            if ($records && count($records ?? []) > 0) {
                                $totalRow['transactionDate'] = '(' . $tenant['leaseID'] . ') - ' . $tenant['leaseName'] . '  Total : ';
                                $totalRow['amount'] = ($tenant_total[0]);
                                $totalRow['unpaidAmount'] = ($tenant_total[1]);
                                $totalRow['unpaidNetAmount'] = ($tenant_total[2]);
                                $totalRow['unpaidTaxAmount'] = ($tenant_total[3]);

                                $details[$propertyID]['lease'][$tenant['leaseID']]['totalRow'] = $totalRow;


                                $details[$propertyID]['amount'] += $totalRow['amount'];
                                $details[$propertyID]['unpaidAmount'] += $totalRow['unpaidAmount'];
                                $details[$propertyID]['unpaidNetAmount'] += $totalRow['unpaidNetAmount'];
                                $details[$propertyID]['unpaidTaxAmount'] += $totalRow['unpaidTaxAmount'];

                                $grand_total['transactionDate'] = 'Grand Total';
                                $grand_total['amount'] += $totalRow['amount'];
                                $grand_total['unpaidAmount'] += $totalRow['unpaidAmount'];
                                $grand_total['unpaidNetAmount'] += $totalRow['unpaidNetAmount'];
                                $grand_total['unpaidTaxAmount'] += $totalRow['unpaidTaxAmount'];

                            }
                        }

                        if (count($details[$propertyID]['lease']) == 0) {
                            unset($details[$propertyID]);
                        }

                    } else {
                        $unpaidAmount = 0;
                        foreach ($records as $record) {
                            $unpaidAmount = bcadd($unpaidAmount, $record['unpaidAmount'], 2);
                        }

                        if ($unpaidAmount > 0) {
                            // -- fetch the bank account for the debtor of the lease
                            $company = dbGetCompanyBank($tenant['debtorID']);
                            $aba->addLine(
                                buildBSB($company['bsbNumber']),
                                $company['accountNumber'],
                                '13',
                                $unpaidAmount,
                                $company['bankAccountName'],
                                $view->items['description'],
                                0
                            );
                            $total += $unpaidAmount;
                            $data['propertyCode'] = $tenant['propertyID'];
                            $data['leaseCode'] = $tenant['leaseID'];
                            $data['leaseName'] = $tenant['leaseName'];
                            $data['amount'] = toDecimal($unpaidAmount, 2);
                            $data['bankAccount'] = $company['accountNumber'];
                            if (getDisplayBsbFromSession()) {
                                $data['bsb'] = formatWithDelimiter($company['bsbNumber']);
                            }
                            $report->renderLine($data);

                            if ($bank['shortName'] == 'MBL') {
                                $aba->addLine(
                                    buildBSB($bank['bsbNumber']),
                                    $bank['accountID'],
                                    '50',
                                    $unpaidAmount,
                                    $bank['bankAccountName'],
                                    $tenant['crn'],
                                    0
                                );
                            }
                        }
                    }
                }
            }
        }

        if ($view->items['action'] == 'preview') {
            // begin
            //					$report->preparePage ();

            $rep_data =  [];
            $count = 0;
            foreach ($details as $property) {
                if ($property['unpaidAmount'] > 0) {
                    $prop['transactionDate'] = $property['name'];
                    $prop['amount'] = toMoney($property['amount'], '');
                    $prop['unpaidNetAmount'] = toMoney($property['unpaidNetAmount'], '');
                    $prop['unpaidTaxAmount'] = toMoney($property['unpaidTaxAmount'], '');
                    $prop['unpaidAmount'] = toMoney($property['unpaidAmount'], '');

                    $prop['bold'] = true;
                    $prop['width']['transactionDate'] = 487;
                    $prop['bgcolor'] = [142 / 255, 212 / 255, 255 / 255];
                    $prop['headerStyle'] =
                    [
                        'fill' => [
                            'type' => Fill::FILL_SOLID,
                            'color' => ['rgb' => '8ed4ff'],
                        ],
                        'font' => ['bold' => true, 'color' => ['rgb' => '000000']],
                        'alignment' => ['vertical' => Alignment::VERTICAL_CENTER, 'horizontal' => Alignment::HORIZONTAL_LEFT],
                    ];
                    $rep_data[] = $prop;
                    foreach ($property['lease'] as $l) {
                        if ($l['totalRow']['unpaidAmount'] > 0) {
                            $_lease['transactionDate'] = $l['leaseName'];

                            $_lease['leaseID'] = $l['leaseID'];
                            $_lease['bold'] = true;
                            $_lease['width']['transactionDate'] = 487;

                            // bg color for PDF
                            $_lease['bgcolor'] = [220 / 255, 242 / 255, 255 / 255];

                            // header style for excel
                            $_lease['headerStyle'] =
                            [
                                'fill' => [
                                    'type' => Fill::FILL_SOLID,
                                    'color' => ['rgb' => 'dcf2ff'],
                                ],
                                'font' => ['bold' => true, 'color' => ['rgb' => '000000']],
                                'alignment' => ['vertical' => Alignment::VERTICAL_CENTER, 'horizontal' => Alignment::HORIZONTAL_LEFT],
                            ];
                            $rep_data[] = $_lease;

                            foreach ($l['data'] as $y) {

                                //							$y['amount'] = toMoney($y['amount'], '');
                                //							$y['unpaidAmount'] = toMoney($y['unpaidAmount'], '');
                                //							$y['unpaidNetAmount'] = toMoney($y['unpaidNetAmount'], '');
                                //							$y['unpaidTaxAmount'] = toMoney($y['unpaidTaxAmount'], '');

                                $y['bold'] = false;
                                $rep_data[] = $y;
                            }

                            $totalRow['transactionDate'] = $l['leaseName'] . ' total';
                            $totalRow['amount'] = $l['totalRow']['amount'];
                            $totalRow['unpaidNetAmount'] = $l['totalRow']['unpaidNetAmount'];
                            $totalRow['unpaidTaxAmount'] = $l['totalRow']['unpaidTaxAmount'];
                            $totalRow['unpaidAmount'] = $l['totalRow']['unpaidAmount'];

                            $totalRow['amount'] = toDecimal($totalRow['amount']);
                            $totalRow['unpaidNetAmount'] = toDecimal($totalRow['unpaidNetAmount']);
                            $totalRow['unpaidTaxAmount'] = toDecimal($totalRow['unpaidTaxAmount']);
                            $totalRow['unpaidAmount'] = toDecimal($totalRow['unpaidAmount']);


                            $totalRow['bold'] = true;
                            $totalRow['width']['transactionDate'] = 487;
                            $totalRow['bgcolor'] = [0.9, 0.9, 0.9];

                            // header style for excel
                            $totalRow['headerStyle'] =
                            [
                                'fill' => [
                                    'type' => Fill::FILL_SOLID,
                                    'color' => ['rgb' => 'e1e1e1'],
                                ],
                                'font' => ['bold' => true, 'color' => ['rgb' => '000000']],
                                'alignment' => ['vertical' => Alignment::VERTICAL_CENTER, 'horizontal' => Alignment::HORIZONTAL_LEFT],
                            ];

                            $rep_data[] = $totalRow;
                            $rep_data[] = ['transactionDate' => ''];
                        }

                    }
                }
            }

            // end
            //					$report->renderHeader ();
            // $report->renderData ($data);

            $leaseTotal = [];
            $leaseCount = 0;

            foreach ($rep_data as $row) {
                // $report->renderLineFill($row);
                $xls->renderLine_custom($row);
                if ($row['amount'] != 0) {
                    $row['amount'] = toDecimal($row['amount'], 2);
                }

                $report->renderLine_custom($row);

            }

            $grand_total['width']['transactionDate'] = 487;
            $grand_total['amount'] = toMoney($grand_total['amount'], '');
            $grand_total['unpaidNetAmount'] = toMoney($grand_total['unpaidNetAmount'], '');
            $grand_total['unpaidTaxAmount'] = toMoney($grand_total['unpaidTaxAmount'], '');
            $grand_total['unpaidAmount'] = toMoney($grand_total['unpaidAmount'], '');
            $report->renderSubTotal($grand_total, 1);
            $xls->renderTotal($grand_total, 'Grand Total');

        } else {
            $total_record['propertyCode'] = 'Total';
            $total_record['amount'] = toDecimal($total, 2);
            $report->renderSubTotal($total_record);
        }

        $report->clean();
        $report->endPage();
        $report->close();
        if ($view->items['action'] == 'preview') {
            $xls->endPage();
            $xls->close();
        }

        if ($bank['shortName'] != 'WBC' && $bank['shortName'] != 'MBL') {
            $aba->addLine(
                buildBSB($bank['bsbNumber']),
                $bank['accountID'],
                '50',
                $total,
                $bank['paymentName'],
                $view->items['description'],
                0
            );
        }

        $aba->setFooter(0, $total, $total);

        $aba->save();

        // -- if it s a scheduled task - attach the report and email to the requester
        if ($context[IS_TASK]) {
            $email = new EmailTemplate('views/emails/basicEmail.html', SYSTEMURL);
            $attachment = [];
            $attachment[0]['file'] = $filePath;
            $attachment[0]['content_type'] = 'application/pdf';
            sendMail($_SESSION['email'], $_SESSION['first_name'] . ' ' . $_SESSION['last_name'], $email->toString(), 'Direct Debit File', $attachment);
            logData('Emailed direct debit file to ' . $_SESSION['email']);
            $context[TASK_COMPLETE] = true;
        }


    }


    if ($propertyCount > THRESHOLD_DIRECTDEBIT) {
        $view->items['statusMessage'] = 'Due to its complexity, this report has been scheduled and will be emailed to you on completion!';
        $view->render();
    } else {
        $view->items['downloadPath'] = $downloadPath;
        $view->items['downloadPath_pdf'] = $downloadPath_pdf;
        $view->items['downloadPath_xlsx'] = $downloadPath_xlsx;
        $view->render();
    }
}

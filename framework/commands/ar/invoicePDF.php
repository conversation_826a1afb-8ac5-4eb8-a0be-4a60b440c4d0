<?php

include_once SYSTEMPATH . '/lib/enums/InvoiceChargeType.php';
include_once SYSTEMPATH . '/lib/enums/InvoiceStatementType.php';

use enums\InvoiceChargeType;
use enums\InvoiceStatementType;

function bookInvoiceNumber()
{
    $number = dbGetParam('DOCSERIES', 'GSTINVOICE');
    $number++;
    dbSetParam('DOCSERIES', 'GSTINVOICE', $number);

    return $number;
}

// -- function fetchOutstandingAmounts

//   given a debtor, property, lease and invoice date -
//  returns an array of outstanding amounts : used by Receipts, ReceiptsProcess, ViewUnpaidINvoices
function fetchOutstandingAmounts(
    $debtorID,
    $propertyID,
    $leaseID,
    $dueDate,
    $batchNumber = null,
    $filteredBy = 'transactionDate'
) {
    $records = [];
    $outstanding = dbGetInvoiceCharges(
        InvoiceChargeType::INV_CHARGES_OLD,
        $propertyID,
        $leaseID,
        $debtorID,
        $dueDate,
        $batchNumber,
        false,
        'DESC',
        false,
        false,
        true,
        $filteredBy
    );

    if ($outstanding) {
        foreach ($outstanding as $row) {
            // -- offset is all transactions allocated against the reference transaction minus the effect of any adjustments [INV<->CRE  / CRE<->INV] (to prevent a double count)
            $offset = bcadd($row['totalAllocated'], $row['totalReallocated'], 2);
            $row['unallocated'] = round($row['unallocated'] * 1, 2);
            $row['amount'] = round($row['amount'], 2);
            $unpaidAmount = bcsub($row['amount'], $offset, 2);

            if (($unpaidAmount != 0) && ($row['transactionType'] != TYPE_CASH)) {
                $row['unpaidAmount'] = bcsub($row['amount'], $offset, 2);
                $records[] = $row;
            } elseif (($row['transactionType'] == TYPE_CASH) && ($row['unallocated'] != 0)) {
                $unallocated = dbGetUnallocated(
                    $propertyID,
                    $leaseID,
                    $debtorID,
                    $row['batchNumber'],
                    $row['batchLineNumber']
                );
                if ($unallocated) {
                    foreach ($unallocated as $unallocatedItem) {
                        if ($unallocatedItem['amount'] != 0) {
                            $unallocatedItem['unpaidAmount'] = $unallocatedItem['amount'];
                            $unallocatedItem['transactionAmount'] = $unallocatedItem['amount'];
                            $unallocatedItem['batchNumber'] = $row['batchNumber'];
                            $unallocatedItem['batchLineNumber'] = $row['batchLineNumber'];
                            $unallocatedItem['transactionDate'] = $row['transactionDate'];
                            $unallocatedItem['invoiceNumber'] = $row['invoiceNumber'];
                            $unallocatedItem['transactionType'] = TYPE_CASH;
                            $unallocatedItem['unallocated'] = $row['unallocated'];
                            $records[] = $unallocatedItem;
                        }
                    }
                }
            }
        }
    }

    return $records;
}

function fetchOutstandingAmountsWithoutInvoiceNumber($debtorID, $propertyID, $leaseID, $dueDate, $batchNumber = null)
{
    $records = [];
    $outstanding = dbGetInvoiceCharges(
        InvoiceChargeType::INV_CHARGES_NEW_INV_ONLY,
        $propertyID,
        $leaseID,
        $debtorID,
        $dueDate,
        $batchNumber
    );

    if ($outstanding) {
        foreach ($outstanding as $row) {
            // -- offset is all transactions allocated against the reference transaction minus the effect of any adjustments [INV<->CRE  / CRE<->INV] (to prevent a double count)
            $offset = bcadd($row['totalAllocated'], $row['totalReallocated'], 2);
            $row['unallocated'] = round($row['unallocated'] * 1, 2);
            $row['amount'] = round($row['amount'], 2);
            $unpaidAmount = bcsub($row['amount'], $offset, 2);

            if (($unpaidAmount != 0) && ($row['transactionType'] != TYPE_CASH)) {
                $row['unpaidAmount'] = bcsub($row['amount'], $offset, 2);
                $records[] = $row;
            } elseif (($row['transactionType'] == TYPE_CASH) && ($row['unallocated'] != 0)) {
                $unallocated = dbGetUnallocated(
                    $propertyID,
                    $leaseID,
                    $debtorID,
                    $row['batchNumber'],
                    $row['batchLineNumber']
                );
                if ($unallocated) {
                    foreach ($unallocated as $unallocatedItem) {
                        if ($unallocatedItem['amount'] != 0) {
                            $unallocatedItem['unpaidAmount'] = $unallocatedItem['amount'];
                            $unallocatedItem['transactionAmount'] = $unallocatedItem['amount'];
                            $unallocatedItem['batchNumber'] = $row['batchNumber'];
                            $unallocatedItem['batchLineNumber'] = $row['batchLineNumber'];
                            $unallocatedItem['transactionDate'] = $row['transactionDate'];
                            $unallocatedItem['invoiceNumber'] = $row['invoiceNumber'];
                            $unallocatedItem['transactionType'] = TYPE_CASH;
                            $unallocatedItem['unallocated'] = $row['unallocated'];
                            $records[] = $unallocatedItem;
                        }
                    }
                }
            }
        }
    }

    return $records;
}

// -- function fetchInvoiceAmounts

//   given a debtor, property, lease and invoice date -
//  returns the number of outstanding amounts, and the records attached to those amounts in an array split by new (newAmounts) and outstanding (outstandingAmounts)

// -- a more comprehensive version of fetchOutstandingAmounts for retrieving new charges and associated

function fetchInvoiceAmounts(
    $debtorID,
    $propertyID,
    $leaseID,
    $dueDate,
    &$newAmounts,
    &$outstandingAmounts,
    $batchNumber = null,
    $newAmountsOnly = false,
    $AIInterim = false,
    &$check_batchlineNumber = [],
    $upToDate = '',
    $combineDebtor = false,
    $includeUnallocatedCredits = true,
    $lineNumber = null,
    $invoiceNumber = null
) {
    $outstandingAmounts = [];
    $newAmounts = [];
    $check_batchlineNumber = [];

    $newAmounts = dbGetInvoiceCharges(
        InvoiceChargeType::INV_CHARGES_NEW,
        $propertyID,
        $leaseID,
        $debtorID,
        $dueDate,
        $batchNumber,
        $AIInterim,
        'DESC',
        $combineDebtor,
        $newAmountsOnly,
        $includeUnallocatedCredits,
        'transactionDate',
        $lineNumber,
        $invoiceNumber
    );
    $records = count($newAmounts ?? []);

    $withTax = false;

    if ($newAmounts) {
        foreach ($newAmounts as $amount) {
            $receipts = dbGetNewReceipts($amount['batchNumber'], $amount['batchLineNumber'], $dueDate);

            if ($receipts) {
                foreach ($receipts as $receipt) {
                    $receipt['noinvoice'] = 1;
                    $receipt['unallocated'] = $receipt['amount'];
                    $receipt['totalAllocated'] = $receipt['amount']; // -- HACK : causes the offset to not be 0 to pass the checks in the PDF code. should clean up the PDF code instead :)
                    $outstandingAmounts[] = $receipt;
                }
            }

            if ($amount['taxAmount'] > 0) {
                $withTax = true;
            }
        }
    }

    if ($newAmountsOnly) {
        return [$records, $withTax];
    }

    $existing = dbGetInvoiceCharges(
        InvoiceChargeType::INV_CHARGES_OLD,
        $propertyID,
        $leaseID,
        $debtorID,
        ($upToDate ? $upToDate : $dueDate),
        $batchNumber,
        false,
        'desc',
        $combineDebtor,
        false,
        $includeUnallocatedCredits,
        'transactionDate',
        $lineNumber
    );
    if ($existing) {
        foreach ($existing as $row) {
            $offset = bcadd($row['totalAllocated'], $row['totalReallocated'], 2);
            $row['unallocated'] = round(floatval($row['unallocated']), 2);
            $row['amount'] = round($row['amount'] * 1, 2);
            $unpaidAmount = bcsub($row['amount'], $offset, 2);
            if (($unpaidAmount != 0.00) && ($row['transactionType'] != TYPE_CASH)) {
                $row['unpaidAmount'] = $unpaidAmount;
                $check_batchlineNumber[] = $row['batchNumber'] . $row['batchLineNumber'];
                $records++;
                $outstandingAmounts[] = $row;
            } elseif (($row['transactionType'] == TYPE_CASH) && ($row['unallocated'] != 0)) {
                $unallocated = dbGetUnallocated(
                    $propertyID,
                    $leaseID,
                    $debtorID,
                    $row['batchNumber'],
                    $row['batchLineNumber'],
                    $combineDebtor
                );
                // -- PATCH LOGIC : trash switch to not return unallocated amounts on invoices
                if ($unallocated) {
                    foreach ($unallocated as $unallocatedItem) {
                        if ($unallocatedItem['amount'] != 0) {
                            $unallocatedItem['unpaidAmount'] = $unallocatedItem['amount'];
                            $unallocatedItem['transactionAmount'] = $unallocatedItem['amount'];
                            $unallocatedItem['batchNumber'] = $row['batchNumber'];
                            $unallocatedItem['batchLineNumber'] = $row['batchLineNumber'];
                            $unallocatedItem['transactionDate'] = $row['transactionDate'];
                            $unallocatedItem['invoiceNumber'] = $row['invoiceNumber'];
                            $unallocatedItem['transactionType'] = TYPE_CASH;
                            $unallocateditem['unallocated'] = $row['unallocated'];
                            $outstandingAmounts[] = $unallocatedItem;
                            $records++;
                        }
                    }
                }
            }
        }
    }

    return [$records, $withTax];
}

function prepareTaxInvoice(
    $style,
    $leaseID,
    $propertyID,
    $dueDate,
    $toDate,
    &$parentInvoice,
    $batchNumber = null,
    $note = null,
    $logo = true,
    $issueDate = '',
    $newAmountsOnly = false,
    $AIInterim = false,
    $upToDate = '',
    $combineDebtor = false,
    $includeUnallocatedCredits = true,
    $lineNumber = null,
    $invoiceNo = null,
    $ignoreBatchNR = false
) {
    global $clientDirectory, $pathPrefix;
    // -- initialise the variables that will store the transaction amounts
    $total = 0;
    $newAmounts = [];
    $outstandingAmounts = [];
    $StatementTotalAmount = 0;

    // -- this may need revising (suppliers?) - but it grabs the
    $debtorID = dbGetDebtor($propertyID, $leaseID);
    $debtorBanking = dbGetCompanyBank($debtorID);
    $isResidential = dbGetResidential($propertyID, $leaseID);

    $isStrata = dbGetStrata($propertyID);
    $property = dbGetProperty($propertyID);

    // -- if there are transactions to be printed : use the invoice variant of fetchOutstandingAmounts
    $check_batchlineNumber = [];
    [$invoiceAmounts, $withTax] = fetchInvoiceAmounts(
        $debtorID,
        $propertyID,
        $leaseID,
        $toDate,
        $newAmounts,
        $outstandingAmounts,
        $batchNumber,
        $newAmountsOnly,
        $AIInterim,
        $check_batchlineNumber,
        $upToDate,
        $combineDebtor,
        $includeUnallocatedCredits,
        $lineNumber,
        $invoiceNo
    );

    $invoiceLineCount = 0;
    if ($invoiceAmounts != 0) {
        $headerData = stripslashes_deep(dbGetHeaderDetails($propertyID, $leaseID));
        $bankData = dbGetBankDetails($propertyID);
        $victoriaOwnerCorp = $bankData['victoriaOwnerCorp'] && $isStrata ? 'OWNERS CORPORATION FEE NOTICE' : 'TAX INVOICE';
        switch ($style) {
            case InvoiceStatementType::ST_INVOICE:
                $headerTitle = ($headerData['is_ledger'] && ! $withTax) ? 'INVOICE' : $victoriaOwnerCorp;
                $totalTitle = 'INVOICE TOTAL';
                break;
            case InvoiceStatementType::ST_STATEMENT:
                $headerTitle = ($headerData['is_ledger'] && ! $withTax) ? 'INVOICE / STATEMENT OF ACCOUNT' : $victoriaOwnerCorp . ' / STATEMENT OF ACCOUNT';
                $totalTitle = 'STATEMENT TOTAL';
                break;
        }

        [$day, $month, $year] = explode('/', $dueDate);

        // -- if there are new amounts to be invoiced - there wont be an invoice number booked yet! so book one out :)
        $invoiceNumber = null;
        if (count($newAmounts ?? []) > 0) {
            $invoiceNumber = bookInvoiceNumber();
        }

        $invoiceNumber = $invoiceNo ? $invoiceNo : $invoiceNumber;

        // -- will need to store current DB index in session to allow for translation of logos
        $logoFile = dbGetClientLogo();
        $logoPropertyAgent = strtolower($property['propertyAgent']);
        $logoPA = "{$pathPrefix}{$clientDirectory}/{$logoPropertyAgent}.jpg";
        if (file_exists($logoPA)) {
            $logoPath = $logoPA;
        } else {
            $logoPath = ($logo) ? "assets/clientLogos/{$logoFile}" : null;
        }

        $filename = (($headerData['is_ledger'] && ! $withTax) ? 'invoice_' : 'tax_invoice_') . "{$year}{$month}{$day}_{$invoiceNumber}_{$propertyID}_{$leaseID}.pdf";
        $filePath = "{$pathPrefix}{$clientDirectory}/pdf/TaxInvoice/{$filename}";
        $downloadPath = "{$clientDirectory}/pdf/TaxInvoice/{$filename}";

        $invoice = new Invoice($filePath, $logoPath);
        $invoice->is_ledger = $headerData['is_ledger'];
        $invoice->withTax = $withTax;
        $invoice->isResidential = $isResidential;
        $invoice->isStrata = $isStrata;
        logData(serialize($parentInvoice));

        // check for zero amount
        foreach ($newAmounts as $row) {
            if ($row['amount'] != 0) {
                $invoiceLineCount++;
            }
        }

        if ($parentInvoice) {
            $masterInvoice = new Invoice($parentInvoice, $logoPath);
        }

        $leaseAddress = stripslashes_deep(dbGetLeaseMailingAddress($propertyID, $leaseID));
        $agentData = dbGetAgentDetails($property['propertyRemittanceOffice'], $propertyID);

        if ($parentInvoice) {
            $masterInvoice->isResidential = $isResidential;
            $masterInvoice->isStrata = $isStrata;
            $masterInvoice->is_ledger = $headerData['is_ledger'];
            $masterInvoice->withTax = $withTax;
            $masterInvoice->bpayBillerCode = $bankData['bpayBillerCode'];
            $masterInvoice->deft = $bankData['deft'];
            $masterInvoice->payway = $bankData['payway'];
            $masterInvoice->paywayBillerCode = $bankData['paywayBillerCode'];
            $masterInvoice->payID = $bankData['payID'];
            $masterInvoice->payIDCode = $bankData['payIDCode'];
            $masterInvoice->creditCard = $bankData['creditCard'];
            $masterInvoice->linkProvider = $bankData['linkProvider'];
            $masterInvoice->officeState = $agentData['officeState'];
            $masterInvoice->showDueDate = $bankData['showDueDate'];
        }

        $invoice->bpayBillerCode = $bankData['bpayBillerCode'];
        $invoice->deft = $bankData['deft'];
        $invoice->payway = $bankData['payway'];
        $invoice->paywayBillerCode = $bankData['paywayBillerCode'];
        $invoice->payID = $bankData['payID'];
        $invoice->payIDCode = $bankData['payIDCode'];
        $invoice->creditCard = $bankData['creditCard'];
        $invoice->linkProvider = $bankData['linkProvider'];
        $invoice->officeState = $agentData['officeState'];
        $invoice->showDueDate = $bankData['showDueDate'];
        $invoice->propertyID = $propertyID;
        // -- some data is shared across several components in the PDF, so instead of doing multiple db calls and binding locally within the object,
        // -- a single DB call is made and data bound to the object externally

        // -- PDFobjects are usually groups of data repeated statically on every page of the PDF, and as such are loaded once into the PDF and called at render...
        // -- mutating values are accessed from the Invoice object itself such as changing totals, invoice rows, sub titles within the statement

        // -- note that data is bound between array values to the object attributes sharing the same name as the array key - ie
        // -- $this->test = $array['test']

        $header = new InvoiceHeader($propertyID, $leaseID, $dueDate, $headerTitle, $issueDate);
        $header->bindAttributesFrom($headerData);
        $header->bindAttributesFrom($bankData);
        $header->isStrata = $isStrata;
        $invoice->attachObject('header', $header);

        $crn = dbGetCRN($propertyID, $leaseID);
        $footer = new InvoiceFooter($propertyID, $leaseID, $dueDate, $totalTitle, $crn);

        $footer->bpayBillerCode = $bankData['bpayBillerCode'];
        $footer->deft = $bankData['deft'];
        $footer->payway = $bankData['payway'];
        $footer->paywayBillerCode = $bankData['paywayBillerCode'];
        $footer->payID = $bankData['payID'];
        $footer->payIDCode = $bankData['payIDCode'];
        $footer->creditCard = $bankData['creditCard'];
        $footer->linkProvider = $bankData['linkProvider'];
        $footer->directDeposit = ($footer->bpayBillerCode) ? false : $bankData['directDeposit'];
        $footer->directDebitAuth = $debtorBanking['directDebit'];
        $footer->eftReference = $bankData['eftReference'];
        $footer->isStrata = $isStrata;
        $footer->bindAttributesFrom($headerData);
        $footer->bindAttributesFrom($agentData);
        $footer->bindAttributesFrom($leaseAddress);
        $footer->bindAttributesFrom($bankData);
        $invoice->attachObject('footer', $footer);

        $agentDetails = new AgentDetails($propertyID);
        $agentDetails->bindAttributesFrom($agentData);
        $agentDetails->businessLabel = $_SESSION['country_default']['business_label'];
        $invoice->attachObject('agentDetails', $agentDetails);
        $leaseDescription = new LeaseDescription($propertyID, $leaseID);
        $leaseDescription->isStrata = $isStrata;
        $leaseDescription->is_ledger = $headerData['is_ledger'];
        $invoice->attachObject('leaseDescription', $leaseDescription);
        $invoiceLines = new InvoiceLines($totalTitle);
        $invoiceLines->isResidential = $isResidential;
        $invoiceLines->is_ledger = $headerData['is_ledger'];
        $invoiceLines->withTax = $withTax;
        $invoiceLines->showDueDate = $bankData['showDueDate'];
        $invoice->attachObject('statement', $invoiceLines);
        $invoice->attachObject('foldline', new FoldLines());
        $invoice->attachObject('cutoffline', new CutOffLine());
        $invoice->attachObject(
            'mailingAddress',
            new MailAddressWindow($propertyID, $leaseID, $headerData['is_ledger'])
        );
        $invoice->attachObject(
            'traccFooter',
            new TraccFooter(
                'assets/clientLogos/tracc_logo.jpg',
                (($headerData['is_ledger'] && ! $withTax) ? 'Tenant_Invoice' : 'Tenant_Tax_Invoice'),
                1
            )
        );

        if (isset($note)) {
            $invoice->attachObject('note', new InvoiceNote($note));
        }

        if ($parentInvoice) {
            $masterInvoice->objects = $invoice->objects;
        }

        $invoice->preparePage();
        if ($parentInvoice) {
            $masterInvoice->preparePage();
        }

        if (count($newAmounts ?? []) > 0) {
            foreach ($newAmounts as $row) {
                $StatementTotalAmount += ($row['amount'] * 1);
            }
        }


        if ($style == InvoiceStatementType::ST_STATEMENT && count($outstandingAmounts) > 0) {
            foreach ($outstandingAmounts as $row) {
                $offset = floatval($row['totalAllocated']) + floatval($row['totalReallocated']);
                if (($row['invoiceNumber'] == 0) && ($row['transactionType'] != TYPE_CASH)) {
                    if ((! in_array(
                        $row['batchNumber'] . $row['batchLineNumber'],
                        $check_batchlineNumber
                    ) || in_array(
                        $row['batchNumber'] . $row['batchLineNumber'],
                        $check_batchlineNumber
                    ) && ! $row['noinvoice']) && $offset != 0) {
                        $offset *= -1;
                        $StatementTotalAmount += $offset;
                        $invoiceLineCount++;
                    }
                } elseif ((($row['amount'] - $offset) != 0) && ($row['transactionType'] != TYPE_CASH)) {
                    $currentAmount = $row['amount'] - $offset;
                    $StatementTotalAmount += $currentAmount;
                    $invoiceLineCount++;
                } elseif (($row['transactionType'] == TYPE_CASH) && ($row['amount'] != '') && ($row['amount'] != 0)) {
                    $unallocated[0] = $row;
                    foreach ($unallocated as $unallocatedItem) {
                        $unallocatedAmount = ($unallocatedItem['amount'] * 1);
                        $StatementTotalAmount += $unallocatedAmount;
                        $invoiceLineCount++;
                    }
                }
            }
        }

        $gotStatement = false;
        if (count($newAmounts ?? []) > 0) {
            $gotStatement = true;
            $currentTotal = 0;
            $currentNet = 0;
            $currentTax = 0;
            $newAmountLines = 0;

            foreach ($newAmounts as $row) {
                $currentTotal += ($row['amount'] * 1);
                $currentNet += ($row['netAmount'] * 1);
                $currentTax += ($row['taxAmount'] * 1);

                $total += ($row['amount'] * 1);
                if ($agentData['disableInvoiceDates'] == 1) {
                    $row['description'] = "{$row['description']} ";
                } else {
                    $row['description'] = ($row['transDate'] == $row['fromDate'] && $row['transDate'] == $row['toDate']) ? "{$row['description']}" : "{$row['description']} {$row['fromDate']} - {$row['toDate']}";
                }

                if ($row['amount'] != 0) {
                    $newAmountLines++;
                    $invoice->renderInvoiceRow(
                        $row['transactionDate'],
                        $invoiceNumber,
                        $row['description'],
                        toMoney($row['netAmount'], null),
                        toMoney($row['taxAmount'], null),
                        toMoney($row['amount'], null),
                        $StatementTotalAmount,
                        $dueDate
                    );
                }

                if ($parentInvoice && $row['amount'] != 0 && $invoiceLineCount > 0) {
                    $masterInvoice->renderInvoiceRow(
                        $row['transactionDate'],
                        $invoiceNumber,
                        $row['description'],
                        toMoney($row['netAmount'], null),
                        toMoney($row['taxAmount'], null),
                        toMoney($row['amount'], null),
                        $StatementTotalAmount,
                        $dueDate
                    );
                }
            }

            if ($newAmountLines > 0) {
                $invoice->renderSubTotal(
                    ($headerData['is_ledger'] && ! $withTax) ? 'Total For This Invoice' : 'Total For This Tax Invoice',
                    toMoney($currentNet, null),
                    toMoney($currentTax, null),
                    toMoney($currentTotal, null),
                    $StatementTotalAmount,
                    $bankData['showDueDate'] ? 177 : 136
                );
            }

            if ($parentInvoice && $invoiceLineCount > 0 && $newAmountLines > 0) {
                $masterInvoice->renderSubTotal(
                    ($headerData['is_ledger'] && ! $withTax) ? 'Total For This Invoice' : 'Total For This Tax Invoice',
                    toMoney($currentNet, null),
                    toMoney($currentTax, null),
                    toMoney($currentTotal, null),
                    $StatementTotalAmount,
                    $bankData['showDueDate'] ? 177 : 136
                );
            }
        }


        if ($style == InvoiceStatementType::ST_STATEMENT && count($outstandingAmounts) > 0) {
            $titlePrinted = false;
            $count = 0;
            $previousTotal = 0;
            foreach ($outstandingAmounts as $row) {
                $offset = floatval($row['totalAllocated']) + floatval($row['totalReallocated']);
                if (($row['invoiceNumber'] == 0) && ($row['transactionType'] != TYPE_CASH)) {
                    if ((! in_array(
                        $row['batchNumber'] . $row['batchLineNumber'],
                        $check_batchlineNumber
                    ) || in_array(
                        $row['batchNumber'] . $row['batchLineNumber'],
                        $check_batchlineNumber
                    ) && ! $row['noinvoice']) && $offset != 0) {
                        $offset *= -1;
                        $total += $offset;
                        $previousTotal += $offset;
                        if (! $titlePrinted) {
                            $invoice->renderTitle('AMOUNTS PREVIOUSLY INVOICED');
                            if ($parentInvoice && $invoiceLineCount > 0) {
                                $masterInvoice->renderTitle('AMOUNTS PREVIOUSLY INVOICED');
                            }

                            $titlePrinted = true;
                        }

                        $row['description'] = ($row['transDate'] == $row['fromDate'] && $row['transDate'] == $row['toDate']) ? "{$row['description']}" : "{$row['description']} {$row['fromDate']} - {$row['toDate']}";
                        $invoice->renderInvoiceRow(
                            $row['transactionDate'],
                            null,
                            $row['description'],
                            null,
                            null,
                            toMoney($offset, null),
                            $StatementTotalAmount,
                            $row['dueDate']
                        );
                        if ($parentInvoice && $invoiceLineCount > 0) {
                            $masterInvoice->renderInvoiceRow(
                                $row['transactionDate'],
                                null,
                                $row['description'],
                                null,
                                null,
                                toMoney($offset, null),
                                $StatementTotalAmount,
                                $row['dueDate']
                            );
                        }

                        $count++;
                    }
                } elseif ((($row['amount'] - $offset) != 0) && ($row['transactionType'] != TYPE_CASH)) {
                    $currentAmount = $row['amount'] - $offset;
                    $total += $currentAmount;
                    $previousTotal += $currentAmount;
                    if (! $titlePrinted) {
                        $invoice->renderTitle('AMOUNTS PREVIOUSLY INVOICED');
                        if ($parentInvoice && $invoiceLineCount > 0) {
                            $masterInvoice->renderTitle('AMOUNTS PREVIOUSLY INVOICED');
                        }

                        $titlePrinted = true;
                    }

                    $row['description'] = ($row['transDate'] == $row['fromDate'] && $row['transDate'] == $row['toDate']) ? "{$row['description']}" : "{$row['description']} {$row['fromDate']} - {$row['toDate']}";
                    $invoice->renderInvoiceRow(
                        $row['transactionDate'],
                        $row['invoiceNumber'],
                        $row['description'],
                        null,
                        null,
                        toMoney($currentAmount, null),
                        $StatementTotalAmount,
                        $row['dueDate']
                    );
                    if ($parentInvoice && $invoiceLineCount > 0) {
                        $masterInvoice->renderInvoiceRow(
                            $row['transactionDate'],
                            $row['invoiceNumber'],
                            $row['description'],
                            null,
                            null,
                            toMoney($currentAmount, null),
                            $StatementTotalAmount,
                            $row['dueDate']
                        );
                    }

                    $count++;
                } elseif (($row['transactionType'] == TYPE_CASH) && ($row['amount'] != '') && ($row['amount'] != 0)) {
                    $unallocated[0] = $row;
                    foreach ($unallocated as $unallocatedItem) {
                        $unallocatedAmount = ($unallocatedItem['amount'] * 1);
                        $total += $unallocatedAmount;
                        $previousTotal += $unallocatedAmount;
                        if (! $titlePrinted) {
                            $invoice->renderTitle('AMOUNTS PREVIOUSLY INVOICED');
                            if ($parentInvoice && $invoiceLineCount > 0) {
                                $masterInvoice->renderTitle('AMOUNTS PREVIOUSLY INVOICED');
                            }

                            $titlePrinted = true;
                        }

                        $unallocatedItem['description'] = ($unallocatedItem['date'] == $unallocatedItem['fromDate'] && $unallocatedItem['date'] == $unallocatedItem['toDate']) ? "{$unallocatedItem['description']}" : "{$unallocatedItem['description']} {$unallocatedItem['fromDate']} - {$unallocatedItem['toDate']}";
                        $invoice->renderInvoiceRow(
                            $unallocatedItem['date'],
                            null,
                            $unallocatedItem['description'],
                            null,
                            null,
                            toMoney($unallocatedAmount, null),
                            $StatementTotalAmount,
                            $row['dueDate']
                        );
                        if ($parentInvoice && $invoiceLineCount > 0) {
                            $masterInvoice->renderInvoiceRow(
                                $unallocatedItem['date'],
                                null,
                                $unallocatedItem['description'],
                                null,
                                null,
                                toMoney($unallocatedAmount, null),
                                $StatementTotalAmount,
                                $row['dueDate']
                            );
                        }

                        $count++;
                    }
                }
            }

            if ($count > 0) {
                $gotStatement = true;
                $invoice->renderSubTotal(
                    'Total Previously Invoiced',
                    null,
                    null,
                    toMoney($previousTotal, null),
                    $StatementTotalAmount,
                    $bankData['showDueDate'] ? 177 : 136
                );
                if ($parentInvoice && $invoiceLineCount > 0) {
                    $masterInvoice->renderSubTotal(
                        'Total Previously Invoiced',
                        null,
                        null,
                        toMoney($previousTotal, null),
                        $StatementTotalAmount,
                        $bankData['showDueDate'] ? 177 : 136
                    );
                }
            }
        }

        $taxNote = dbGetTaxInvoiceNote($propertyID, $leaseID, $combineDebtor);
        if ($taxNote) {
            $invoice->renderLeaseNote($taxNote, $StatementTotalAmount);
        }

        $invoice->renderTotal($total);
        $invoice->renderRemittanceTotal($total);
        $invoice->renderOCRLine($total);
        $invoice->close();

        if ($parentInvoice && $invoiceLineCount > 0) {
            $masterInvoice->renderTotal($total);
            $masterInvoice->renderRemittanceTotal($total);
            $masterInvoice->renderOCRLine($total);
        }

        if ($parentInvoice) {
            $masterInvoice->close();
        }

        if ($invoiceNumber && ! $invoiceNo) {
            dbCommitTransaction(
                $propertyID,
                $leaseID,
                $invoiceNumber,
                $dueDate,
                $toDate,
                $batchNumber,
                $upToDate,
                $combineDebtor,
                $debtorID,
                $newAmountsOnly,
                $lineNumber
            );

            $skipAp = false;

            $doc['invoice_number'] = $invoiceNumber;
            $doc['invoice_date'] = $dueDate;
            $doc['propertyID'] = $propertyID;
            $doc['leaseID'] = $leaseID;
            $doc['debtorID'] = $debtorID;
            $doc['combineDebtor'] = $combineDebtor;

            // AR PART
            if ($lineNumber && ! $ignoreBatchNR) { // AI goes here
                if ($combineDebtor) {
                    $leases = dbGetAllLease($doc);
                    $doc['leaseID'] = implode("','", $leases);
                }

                $doc['arBatchNumber'] = $batchNumber;
                $doc['arBatchLineNumber'] = $lineNumber;

                dbUpdateInvoiceDocumentAR($doc);
                // end of AR part

                $skipAp = true;
            } else {
                $arBatchAndLines = dbGetTaxInvoiceBatchAndLines($invoiceNumber);

                foreach ($arBatchAndLines as $arBatchAndLine) {
                    $doc['arBatchNumber'] = $arBatchAndLine['batch_nr'];
                    $doc['arBatchLineNumber'] = $arBatchAndLine['batch_line_nr'];

                    dbUpdateInvoiceDocumentAR($doc);

                    // check if the arBatchNumber exist in utility reading
                    $readingId = dbUtilityReadingExist($propertyID, $arBatchAndLine['batch_nr']);
                    // if exist update the invoice_number and due_date
                    if ($readingId) {
                        dbUpdateUtilityReading($readingId, $invoiceNumber, $dueDate);
                    }
                }
            }

            if (! $skipAp) { // AP PART
                $arr = [];

                $getAPBatch = dbGetAPBatchNumberLinked(
                    $propertyID,
                    $leaseID,
                    $invoiceNumber,
                    $dueDate,
                    $toDate,
                    $combineDebtor,
                    $debtorID
                );
                foreach ($getAPBatch as $row) {
                    $apBatch = $row['ap_recovered'];
                    if ($apBatch) {
                        $data = json_decode($apBatch, true);
                        foreach ($data as $val) {
                            $arr[] = [
                                'batch' => $val['batch'],
                                'line' => $val['line'],
                            ];
                        }
                    }
                }

                $var = $arr;

                $length = count($apBatch ?? []);
                if ($length != 0) {
                    $doc['invoice_number'] = $invoiceNumber;
                    $doc['invoice_date'] = $dueDate;
                    $doc['propertyID'] = $propertyID;
                    $doc['leaseID'] = $leaseID;
                    $doc['apBatchNumber'] = $var;
                    $doc['debtorID'] = $debtorID;
                    $doc['combineDebtor'] = $combineDebtor;

                    if ($combineDebtor) {
                        $leases = dbGetAllLease($doc);
                        $doc['leaseID'] = implode("','", $leases);
                    }

                    dbUpdateInvoiceDocumentAP($doc);
                }

                // end of AP part
            }
        }

        if ($isStrata && $bankData['termNoticeStrata'] && is_file(
            "{$pathPrefix}{$clientDirectory}/pdf/Strata/" . $bankData['bankID'] . '.pdf'
        )) {
            $randomFilename = time() . "{$invoiceNumber}_{$propertyID}_{$leaseID}.pdf";
            rename($filePath, "{$pathPrefix}{$clientDirectory}/pdf/TaxInvoice/{$randomFilename}");
            $fileArray[] = "{$pathPrefix}{$clientDirectory}/pdf/TaxInvoice/{$randomFilename}";
            $fileArray[] = "{$pathPrefix}{$clientDirectory}/pdf/Strata/" . $bankData['bankID'] . '.pdf';

            mergePDF($fileArray, false, $filename, $filePath, '');
            unlink("{$pathPrefix}{$clientDirectory}/pdf/TaxInvoice/{$randomFilename}");
        }

        $data = [];
        $data['filePath'] = $filePath;
        $data['downloadPath'] = $downloadPath;
        $data['invoiceNumber'] = $invoiceNumber;
        $data['invoiceLineCount'] = $invoiceLineCount;
        $data['gotStatement'] = $gotStatement;


        return $data;
    } else {
        if ($parentInvoice) {
            $masterInvoice = new Invoice($parentInvoice, '');
        }

        $masterInvoice->preparePage();
        $masterInvoice->close();
    }




}

function prepareTempInvoice(
    $leaseID,
    $propertyID,
    $dueDate,
    $transactionDate,
    $invoiceNumber,
    &$parentInvoice,
    $logo = true,
    $issueDate = '',
    $transactionAmount = null
) {
    global $clientDirectory, $pathPrefix;

    // -- initialise the variables that will store the transaction amounts
    $total = 0;
    $totalAmount = 0;
    $newAmounts = [];

    // -- this may need revising (suppliers?) - but it grabs the
    $debtorID = dbGetDebtor($propertyID, $leaseID);
    $newAmounts = dbGetTempTransactions($propertyID, $leaseID, $transactionAmount);
    $property = dbGetPropertyForInvoice($propertyID);
    $debtorBanking = dbGetCompanyBank($debtorID);
    $withTax = false;
    $isStrata = dbGetStrata($propertyID);

    foreach ($newAmounts as $amount) {
        if ($amount['taxAmount'] != 0) {
            $withTax = true;
        }
    }

    // -- if there are transactions to be printed :
    if ($newAmounts != null) {
        $headerData = stripslashes_deep(dbGetHeaderDetails($propertyID, $leaseID));
        $bankData = dbGetBankDetails($propertyID);
        $victoriaOwnerCorp = $bankData['victoriaOwnerCorp'] && $isStrata ? 'OWNERS CORPORATION FEE NOTICE' : 'TAX INVOICE';
        $headerTitle = ($headerData['is_ledger'] && ! $withTax) ? 'INVOICE' : $victoriaOwnerCorp;
        $totalTitle = 'INVOICE TOTAL';

        [$day, $month, $year] = explode('/', $dueDate);

        $logoFile = dbGetClientLogo();
        $logoPropertyAgent = strtolower($property['propertyAgent']);
        $logoPA = "{$pathPrefix}{$clientDirectory}/{$logoPropertyAgent}.jpg";
        if (file_exists($logoPA)) {
            $logoPath = $logoPA;
        } else {
            $logoPath = ($logo) ? "assets/clientLogos/{$logoFile}" : null;
        }

        $filename = (($headerData['is_ledger'] && ! $withTax) ? 'invoice_' : 'tax_invoice_') . "{$year}{$month}{$day}_{$invoiceNumber}_{$propertyID}_{$leaseID}.pdf";
        $filePath = "{$pathPrefix}{$clientDirectory}/pdf/TaxInvoice/{$filename}";
        $downloadPath = "{$clientDirectory}/pdf/TaxInvoice/{$filename}";

        $invoice = new Invoice($filePath, $logoPath);
        $invoice->is_ledger = $headerData['is_ledger'];
        $invoice->withTax = $withTax;
        if ($parentInvoice) {
            $masterInvoice = new Invoice($parentInvoice, $logoPath);
        }

        $masterInvoice->isResidential = $isResidential;
        $masterInvoice->isStrata = $isStrata;
        $masterInvoice->is_ledger = $headerData['is_ledger'];
        $masterInvoice->withTax = $withTax;


        $agentData = dbGetAgentDetailsPerProperty($property['propertyRemittanceOffice'], $propertyID);
        $leaseAddress = stripslashes_deep(dbGetLeaseMailingAddress($propertyID, $leaseID));
        $invoice->bpayBillerCode = $bankData['bpayBillerCode'];
        $masterInvoice->bpayBillerCode = $bankData['bpayBillerCode'];
        $invoice->deft = $bankData['deft'];
        $masterInvoice->deft = $bankData['deft'];
        $invoice->payway = $bankData['payway'];
        $masterInvoice->payway = $bankData['payway'];
        $invoice->paywayBillerCode = $bankData['paywayBillerCode'];
        $masterInvoice->paywayBillerCode = $bankData['paywayBillerCode'];
        $invoice->payID = $bankData['payID'];
        $masterInvoice->payID = $bankData['payID'];
        $invoice->payIDCode = $bankData['payIDCode'];
        $masterInvoice->payIDCode = $bankData['payIDCode'];
        $invoice->creditCard = $bankData['creditCard'];
        $masterInvoice->creditCard = $bankData['creditCard'];
        $invoice->linkProvider = $bankData['linkProvider'];
        $masterInvoice->linkProvider = $bankData['linkProvider'];
        $invoice->directDeposit = ($masterInvoice->bpayBillerCode) ? false : $bankData['directDeposit'];
        $invoice->directDebitAuth = $debtorBanking['directDebit'];
        $invoice->officeState = $agentData['officeState'];
        $masterInvoice->officeState = $agentData['officeState'];
        $invoice->showDueDate = $bankData['showDueDate'];
        $masterInvoice->showDueDate = $bankData['showDueDate'];
        $invoice->propertyID = $propertyID;

        // -- some data is shared across several components in the PDF, so instead of doing multiple db calls and binding locally within the object,
        // -- a single DB call is made and data bound to the object externally

        // -- PDFobjects are usually groups of data repeated statically on every page of the PDF, and as such are loaded once into the PDF and called at render...
        // -- mutating values are accessed from the Invoice object itself such as changing totals, invoice rows, sub titles within the statement

        // -- note that data is bound between array values to the object attributes sharing the same name as the array key - ie

        $header = new InvoiceHeader($propertyID, $leaseID, $dueDate, $headerTitle, $issueDate);
        $header->bindAttributesFrom($headerData);
        $header->bindAttributesFrom($bankData);
        $header->isStrata = $isStrata;
        $invoice->attachObject('header', $header);


        $crn = dbGetCRN($propertyID, $leaseID);

        $footer = new InvoiceFooter($propertyID, $leaseID, $dueDate, $totalTitle, $crn);
        $footer->bpayBillerCode = $bankData['bpayBillerCode'];
        $footer->deft = $bankData['deft'];
        $footer->payway = $bankData['payway'];
        $footer->paywayBillerCode = $bankData['paywayBillerCode'];
        $footer->payID = $bankData['payID'];
        $footer->payIDCode = $bankData['payIDCode'];
        $footer->creditCard = $bankData['creditCard'];
        $footer->linkProvider = $bankData['linkProvider'];
        $footer->directDebitAuth = $debtorBanking['directDebit'];
        $footer->isStrata = $isStrata;
        $footer->directDeposit = ($footer->bpayBillerCode) ? false : $bankData['directDeposit'];
        $footer->eftReference = $bankData['eftReference'];
        $footer->bindAttributesFrom($headerData);

        $footer->bindAttributesFrom($leaseAddress);
        $footer->bindAttributesFrom($agentData);
        $footer->bindAttributesFrom($debtorBanking);
        $footer->bindAttributesFrom($bankData);
        $invoice->attachObject('footer', $footer);


        $agentDetails = new AgentDetails($propertyID);
        $agentDetails->bindAttributesFrom($agentData);
        $agentDetails->businessLabel = $_SESSION['country_default']['business_label'];
        $invoice->attachObject('agentDetails', $agentDetails);

        $leaseDescription = new LeaseDescription($propertyID, $leaseID);
        $leaseDescription->is_ledger = $headerData['is_ledger'];
        $invoice->attachObject('leaseDescription', $leaseDescription);
        $invoiceLines = new InvoiceLines($totalTitle);
        $invoiceLines->isResidential = $isResidential;
        $invoiceLines->showDueDate = $bankData['showDueDate'];
        $invoiceLines->is_ledger = $headerData['is_ledger'];
        $invoiceLines->withTax = $withTax;
        $invoice->attachObject('statement', $invoiceLines);
        $invoice->attachObject('foldline', new FoldLines());
        $invoice->attachObject('cutoffline', new CutOffLine());
        $invoice->attachObject(
            'mailingAddress',
            new MailAddressWindow($propertyID, $leaseID, $headerData['is_ledger'])
        );
        $invoice->attachObject(
            'traccFooter',
            new TraccFooter(
                'assets/clientLogos/tracc_logo.jpg',
                (($headerData['is_ledger'] && ! $withTax) ? 'Tenant_Invoice' : 'Tenant_Tax_Invoice'),
                1
            )
        );

        if (isset($note)) {
            $invoice->attachObject('note', new InvoiceNote($note));
        }

        if ($parentInvoice) {
            $masterInvoice->objects = $invoice->objects;
        }

        $invoice->preparePage();
        if ($parentInvoice) {
            $masterInvoice->preparePage();
        }

        if (count($newAmounts ?? []) > 0) {
            $currentTotal = 0;
            $currentNet = 0;
            $currentTax = 0;

            $beforeFmtDate = '';
            $toFmtDate = '';
            $fmtFromDate = '';
            $fmtToDate = '';

            foreach ($newAmounts as $row) {
                $currentTotal += ($row['transactionAmount'] * 1);
                $currentNet += ($row['netAmount'] * 1);
                $currentTax += ($row['taxAmount'] * 1);

                $total += ($row['transactionAmount'] * 1);
                $totalAmount = $row['totalTransactionAmount'];

                /** Format from and to dates */
                $beforeFmtDate = str_replace('/', '-', $row['fromDate']);
                $toFmtDate = str_replace('/', '-', $row['toDate']);
                $fmtFromDate = date('d/m/y', strtotime($beforeFmtDate));
                $fmtToDate = date('d/m/y', strtotime($toFmtDate));

                $row['description'] = ($row['transDate'] == $row['fromDate'] && $row['transDate'] == $row['toDate']) ? "{$row['description']}" : "{$row['description']} {$fmtFromDate} - {$fmtToDate}";

                $invoice->renderInvoiceRow(
                    $row['transactionDate'],
                    $invoiceNumber,
                    $row['description'],
                    toMoney($row['netAmount'], null),
                    toMoney($row['taxAmount'], null),
                    toMoney($row['transactionAmount'], null),
                    $row['totalAmount'],
                    $dueDate
                );
                if ($parentInvoice) {
                    $masterInvoice->renderInvoiceRow(
                        $row['transactionDate'],
                        $invoiceNumber,
                        $row['description'],
                        toMoney($row['netAmount'], null),
                        toMoney($row['taxAmount'], null),
                        toMoney($row['transactionAmount'], null),
                        $row['totalAmount'],
                        $dueDate
                    );
                }
            }

            $invoice->renderSubTotal(
                ($headerData['is_ledger'] && ! $withTax) ? 'Total For This Invoice' : 'Total For This Tax Invoice',
                toMoney($currentNet, null),
                toMoney($currentTax, null),
                toMoney($currentTotal, null),
                null,
                $bankData['showDueDate'] ? 177 : 136
            );
            if ($parentInvoice) {
                $masterInvoice->renderSubTotal(
                    ($headerData['is_ledger'] && ! $withTax) ? 'Total For This Invoice' : 'Total For This Tax Invoice',
                    toMoney($currentNet, null),
                    toMoney($currentTax, null),
                    toMoney($currentTotal, null),
                    null,
                    $bankData['showDueDate'] ? 177 : 136
                );
            }
        }

        $taxNote = dbGetTaxInvoiceNote($propertyID, $leaseID);
        if ($taxNote) {
            $invoice->renderLeaseNote($taxNote, $total);
        }


        $invoice->renderTotal($total);
        $invoice->renderRemittanceTotal($total);
        $invoice->renderOCRLine($total);


        if ($parentInvoice) {
            $masterInvoice->renderTotal($total);
            $masterInvoice->renderRemittanceTotal($total);
            $masterInvoice->renderOCRLine($total);
            $masterInvoice->close();
        }


        if (! $invoiceHandle) {
            $invoice->close();

            if ($isStrata && $bankData['termNoticeStrata'] && is_file(
                "{$pathPrefix}{$clientDirectory}/pdf/Strata/" . $bankData['bankID'] . '.pdf'
            )) {
                $randomFilename = time() . "{$invoiceNumber}_{$propertyID}_{$leaseID}.pdf";
                rename($filePath, "{$pathPrefix}{$clientDirectory}/pdf/TaxInvoice/{$randomFilename}");
                $fileArray[] = "{$pathPrefix}{$clientDirectory}/pdf/TaxInvoice/{$randomFilename}";
                $fileArray[] = "{$pathPrefix}{$clientDirectory}/pdf/Strata/" . $bankData['bankID'] . '.pdf';

                mergePDF($fileArray, false, $filename, $filePath, '');
                unlink("{$pathPrefix}{$clientDirectory}/pdf/TaxInvoice/{$randomFilename}");
            }

            $paths['filePath'] = $filePath;
            $paths['downloadPath'] = $downloadPath;

            return $paths;
        } else {
            $invoice->endPage();

            return;
        }
    }




}


function prepareExcelTaxInvoice(
    &$report,
    &$propCheckDuplicate,
    $style,
    $leaseID,
    $propertyID,
    $dueDate,
    $toDate,
    $parentInvoice,
    $batchNumber = null,
    $note = null,
    $logo = true,
    $issueDate = '',
    $newAmountsOnly = false,
    $AIInterim = false,
    $upToDate = '',
    $combineDebtor = false,
    $includeUnallocatedCredits = true,
    $lineNumber = null,
    $invoiceNo = null,
    $ignoreBatchNR = false
) {
    global $clientDirectory, $pathPrefix;
    // -- initialise the variables that will store the transaction amounts
    $total = 0;
    $newAmounts = [];
    $outstandingAmounts = [];
    $StatementTotalAmount = 0;

    // -- this may need revising (suppliers?) - but it grabs the
    $debtorID = dbGetDebtor($propertyID, $leaseID);
    dbGetCompanyBank($debtorID);
    dbGetResidential($propertyID, $leaseID);

    $isStrata = dbGetStrata($propertyID);
    $property = dbGetProperty($propertyID);

    // -- if there are transactions to be printed : use the invoice variant of fetchOutstandingAmounts
    $check_batchlineNumber = [];
    [$invoiceAmounts, $withTax] = fetchInvoiceAmounts(
        $debtorID,
        $propertyID,
        $leaseID,
        $toDate,
        $newAmounts,
        $outstandingAmounts,
        $batchNumber,
        $newAmountsOnly,
        $AIInterim,
        $check_batchlineNumber,
        $upToDate,
        $combineDebtor,
        $includeUnallocatedCredits,
        $lineNumber,
        $invoiceNo
    );

    $invoiceLineCount = 0;
    if ($invoiceAmounts != 0) {
        $headerData = stripslashes_deep(dbGetHeaderDetails($propertyID, $leaseID));
        $bankData = dbGetBankDetails($propertyID);
        $victoriaOwnerCorp = $bankData['victoriaOwnerCorp'] && $isStrata ? 'OWNERS CORPORATION FEE NOTICE' : 'TAX INVOICE';
        switch ($style) {
            case InvoiceStatementType::ST_INVOICE:
                $headerTitle = ($headerData['is_ledger'] && ! $withTax) ? 'INVOICE' : $victoriaOwnerCorp;
                $totalTitle = 'INVOICE TOTAL';
                break;
            case InvoiceStatementType::ST_STATEMENT:
                $headerTitle = ($headerData['is_ledger'] && ! $withTax) ? 'INVOICE / STATEMENT OF ACCOUNT' : $victoriaOwnerCorp . ' / STATEMENT OF ACCOUNT';
                $totalTitle = 'STATEMENT TOTAL';
                break;
        }

        [$day, $month, $year] = explode('/', $dueDate);

        // -- if there are new amounts to be invoiced - there wont be an invoice number booked yet! so book one out :)
        $invoiceNumber = null;
        if (count($newAmounts ?? []) > 0) {
            $invoiceNumber = bookInvoiceNumber();
        }

        $invoiceNumber = $invoiceNo ? $invoiceNo : $invoiceNumber;

        // -- will need to store current DB index in session to allow for translation of logos
        $logoFile = dbGetClientLogo();
        $logoPropertyAgent = strtolower($property['propertyAgent']);
        $logoPA = "{$pathPrefix}{$clientDirectory}/{$logoPropertyAgent}.jpg";
        if (file_exists($logoPA)) {
            $logoPath = $logoPA;
        } else {
            $logoPath = ($logo) ? "assets/clientLogos/{$logoFile}" : null;
        }

        $filename = (($headerData['is_ledger'] && ! $withTax) ? 'invoice_' : 'tax_invoice_') . "{$year}{$month}{$day}_{$invoiceNumber}_{$propertyID}_{$leaseID}.pdf";
        $filePath = "{$pathPrefix}{$clientDirectory}/pdf/TaxInvoice/{$filename}";
        $downloadPath = "{$clientDirectory}/pdf/TaxInvoice/{$filename}";


        // check for zero amount
        foreach ($newAmounts as $row) {
            if ($row['amount'] != 0) {
                $invoiceLineCount++;
            }
        }

        if (count($newAmounts ?? []) > 0) {
            foreach ($newAmounts as $row) {
                $StatementTotalAmount += ($row['amount'] * 1);
            }
        }


        if ($style == InvoiceStatementType::ST_STATEMENT && count($outstandingAmounts) > 0) {
            foreach ($outstandingAmounts as $row) {
                $offset = floatval($row['totalAllocated']) + floatval($row['totalReallocated']);
                if (($row['invoiceNumber'] == 0) && ($row['transactionType'] != TYPE_CASH)) {
                    if ((! in_array(
                        $row['batchNumber'] . $row['batchLineNumber'],
                        $check_batchlineNumber
                    ) || in_array(
                        $row['batchNumber'] . $row['batchLineNumber'],
                        $check_batchlineNumber
                    ) && ! $row['noinvoice']) && $offset != 0) {
                        $offset *= -1;
                        $StatementTotalAmount += $offset;
                        $invoiceLineCount++;
                    }
                } elseif ((($row['amount'] - $offset) != 0) && ($row['transactionType'] != TYPE_CASH)) {
                    $currentAmount = $row['amount'] - $offset;
                    $StatementTotalAmount += $currentAmount;
                    $invoiceLineCount++;
                } elseif (($row['transactionType'] == TYPE_CASH) && ($row['amount'] != '') && ($row['amount'] != 0)) {
                    $unallocated[0] = $row;
                    foreach ($unallocated as $unallocatedItem) {
                        $unallocatedAmount = ($unallocatedItem['amount'] * 1);
                        $StatementTotalAmount += $unallocatedAmount;
                        $invoiceLineCount++;
                    }
                }
            }
        }

        if (count($newAmounts ?? []) > 0) {
            $currentTotal = 0;
            $currentNet = 0;
            $currentTax = 0;
            $newAmountLines = 0;

            foreach ($newAmounts as $row) {
                if (isset($propCheckDuplicate[$row['createDate'] . $propertyID . $leaseID])) {
                    continue;
                }

                $propCheckDuplicate[$row['createDate'] . $propertyID . $leaseID] = 1;

                $currentTotal += ($row['amount'] * 1);
                $currentNet += ($row['netAmount'] * 1);
                $currentTax += ($row['taxAmount'] * 1);

                $total += ($row['amount'] * 1);
                if ($agentData['disableInvoiceDates'] == 1) {
                    $row['description'] = "{$row['description']} ";
                } else {
                    $row['description'] = ($row['transDate'] == $row['fromDate'] && $row['transDate'] == $row['toDate']) ? "{$row['description']}" : "{$row['description']} {$row['fromDate']} - {$row['toDate']}";
                }

                if ($row['amount'] != 0) {
                    $newAmountLines++;
                    $row['property'] = $propertyID;
                    $row['leaseCode'] = $leaseID;
                    $row['dueDate'] = $dueDate;
                    $row['type'] = 'Newly Invoiced';
                    $row['invoiceNumber'] = 'New Invoice';
                    $report->renderLine($row);
                }
            }
        }

        $gotStatement = false;
        if ($style == InvoiceStatementType::ST_STATEMENT && count($outstandingAmounts) > 0) {
            $titlePrinted = false;
            $count = 0;
            $previousTotal = 0;
            foreach ($outstandingAmounts as $row) {
                $offset = floatval($row['totalAllocated']) + floatval($row['totalReallocated']);
                if (($row['invoiceNumber'] == 0) && ($row['transactionType'] != TYPE_CASH)) {
                    if ((! in_array(
                        $row['batchNumber'] . $row['batchLineNumber'],
                        $check_batchlineNumber
                    ) || in_array(
                        $row['batchNumber'] . $row['batchLineNumber'],
                        $check_batchlineNumber
                    ) && ! $row['noinvoice']) && $offset != 0) {
                        $offset *= -1;
                        $total += $offset;
                        $previousTotal += $offset;
                        if (! $titlePrinted) {
                            $titlePrinted = true;
                        }

                        $row['description'] = ($row['transDate'] == $row['fromDate'] && $row['transDate'] == $row['toDate']) ? "{$row['description']}" : "{$row['description']} {$row['fromDate']} - {$row['toDate']}";
                        $row['property'] = $propertyID;
                        $row['leaseCode'] = $leaseID;
                        $row['netAmount'] = '';
                        $row['taxAmount'] = '';
                        $row['amount'] = ($offset);
                        $row['type'] = 'Previously Invoiced';
                        $report->renderLine($row);
                        $count++;
                    }
                } elseif ((($row['amount'] - $offset) != 0) && ($row['transactionType'] != TYPE_CASH)) {
                    $currentAmount = $row['amount'] - $offset;
                    $total += $currentAmount;
                    $previousTotal += $currentAmount;
                    if (! $titlePrinted) {
                        $titlePrinted = true;
                    }

                    $row['description'] = ($row['transDate'] == $row['fromDate'] && $row['transDate'] == $row['toDate']) ? "{$row['description']}" : "{$row['description']} {$row['fromDate']} - {$row['toDate']}";


                    $row['property'] = $propertyID;
                    $row['leaseCode'] = $leaseID;
                    $row['netAmount'] = '';
                    $row['taxAmount'] = '';
                    $row['amount'] = ($currentAmount);
                    $row['type'] = 'Previously Invoiced';
                    $report->renderLine($row);


                    $count++;
                } elseif (($row['transactionType'] == TYPE_CASH) && ($row['amount'] != '') && ($row['amount'] != 0)) {
                    $unallocated[0] = $row;
                    foreach ($unallocated as $unallocatedItem) {
                        $unallocatedAmount = ($unallocatedItem['amount'] * 1);
                        $total += $unallocatedAmount;
                        $previousTotal += $unallocatedAmount;
                        if (! $titlePrinted) {
                            $titlePrinted = true;
                        }

                        $unallocatedItem['description'] = ($unallocatedItem['date'] == $unallocatedItem['fromDate'] && $unallocatedItem['date'] == $unallocatedItem['toDate']) ? "{$unallocatedItem['description']}" : "{$unallocatedItem['description']} {$unallocatedItem['fromDate']} - {$unallocatedItem['toDate']}";
                        $row['netAmount'] = '';
                        $row['taxAmount'] = '';
                        $unallocatedItem['amount'] = $unallocatedAmount;
                        $unallocatedItem['transactionDate'] = $unallocatedItem['date'];
                        $unallocatedItem['property'] = $propertyID;
                        $unallocatedItem['leaseCode'] = $leaseID;
                        $unallocatedItem['type'] = 'Previously Invoiced';
                        $report->renderLine($unallocatedItem);


                        $count++;
                    }
                }
            }
        }

        // -- if there are new transactions being reported - commit the new invoice number to them :)
        // -- modified 20100428 - added check against invoice number (only generated if there are new charges to commit)

        $data = [];
        $data['filePath'] = $filePath;
        $data['downloadPath'] = $downloadPath;
        $data['invoiceNumber'] = $invoiceNumber;
        $data['invoiceLineCount'] = $invoiceLineCount;
        $data['gotStatement'] = $gotStatement;


        return $data;
    } else {
    }




}

/*************************************************************************************************************************/

// INVOICE OBJECTS - repeating groups that appear in each invoice


class InvoiceHeader extends PDFobject
{
    public $asAtDate;

    public $isStrata;

    public $dueDate;

    public $propertyID;

    public $propertyName;

    public $leaseID;

    public $leaseName;

    public $ownerName;

    public $ownerABN;

    public $title;

    public $showOwnerDetail;

    public $showPropertyDetail;

    public $showDueDate;

    public $is_ledger;

    public $business_label;

    public bool $displayBsb;

    public string $bsbLabel;

    public $_name = 'invoiceHeader';

    public function __construct($propertyID, $leaseID, $dueDate, $title, $issueDate = '', $asAtDate = '')
    {
        $this->propertyID = $propertyID;
        $this->leaseID = $leaseID;
        $this->dueDate = $dueDate;
        if (! $issueDate) {
            $issueDate = TODAY;
        }

        $this->issueDate = $issueDate;
        $this->asAtDate = $asAtDate;
        $this->title = $title;

        $this->business_label = $_SESSION['country_default']['business_label'];
        $this->displayBsb = getDisplayBsbFromSession();
        $this->bsbLabel = getBsbLabelFromSession();
    }

    public function preRender(&$pdf)
    {
        // -- main box
        $pdf->setColorExt('both', 'rgb', 0.9, 0.9, 0.9, 0); // Setting the Color to Gray
        $pdf->rect(25, 777, 275, 15);
        $pdf->fill();
        $pdf->setlinewidth(0.5);

        $pdf->setlinewidth(1.1);
        $pdf->setColorExt('both', 'rgb', 0.75, 0.75, 0.75, 0);
        if ($this->showPropertyDetail) {
            $pdf->rect(24, 692, 275, 100);
        } else {
            $pdf->rect(24, 722, 275, 70);
        }

        $pdf->stroke();

        $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);

        $this->setFont($pdf, 'Helvetica-Bold', 8);

        $business_label = $_SESSION['country_default']['business_label'];

        if (strlen($business_label) == 4) {
            $labelCol = 28;
            $labelWidth = 65;
        } elseif (strlen($business_label) > 4) {
            $labelCol = 25;
            $labelWidth = 80;
        } else {
            $labelCol = 30;
            $labelWidth = 60;
        }

        if (! $this->asAtDate) {
            $pdf->showBoxed('Due Date', $labelCol, 762, $labelWidth, 13, 'right', '');
            $pdf->showBoxed('Issue Date', $labelCol, 752, $labelWidth, 13, 'right', '');
        } else {
            $pdf->showBoxed('As of', $labelCol, 762, $labelWidth, 13, 'right', '');
            $pdf->showBoxed('Issue Date', $labelCol, 752, $labelWidth, 13, 'right', '');
        }

        if ($this->showPropertyDetail) {
            if ($this->is_ledger) {
                $pdf->showBoxed('Ledger', $labelCol, 742, $labelWidth, 13, 'right', '');
                $pdf->showBoxed('Sub-Ledger', $labelCol, 722, $labelWidth, 13, 'right', '');
            } else {
                $pdf->showBoxed('Property', $labelCol, 742, $labelWidth, 13, 'right', '');
                $pdf->showBoxed((($this->isStrata) ? 'Owner' : 'Lease'), $labelCol, 722, $labelWidth, 13, 'right', '');
            }
        } elseif ($this->is_ledger) {
            $pdf->showBoxed('Sub-Ledger', $labelCol, 742, $labelWidth, 13, 'right', '');
        } else {
            $pdf->showBoxed((($this->isStrata) ? 'Owner' : 'Lease'), $labelCol, 742, $labelWidth, 13, 'right', '');
        }
    }

    public function render(&$pdf)
    {
        if (strlen($this->ownerName) > 55) {
            $this->ownerName = substr($this->ownerName, 0, 55);
        }

        $this->setFont($pdf, 'Helvetica-Bold', 8);
        $pdf->showBoxed($this->title, 27, 776, 260, 13, 'center', '');

        $ownerCountry = getClientCountry();
        $business_label = $_SESSION['country_default']['business_label'];
        $business_prefix = ($this->ownerABN && $this->ownerABN != '') ? $_SESSION['country_default']['business_prefix'] : '';
        $business_number = ($ownerCountry == 'AU') ? textSpace($this->ownerABN) : $business_prefix . $this->ownerABN;

        if (strlen($business_label) == 4) {
            $labelCol = 28;
            $labelWidth = 65;
            $valueCol = 98;
            $valueWidth = 215;
        } elseif (strlen($business_label) > 4) {
            $labelCol = 25;
            $labelWidth = 80;
            $valueCol = 110;
            $valueWidth = 215;
        } else {
            $labelCol = 30;
            $labelWidth = 60;
            $valueCol = 95;
            $valueWidth = 215;
        }

        $this->setFont($pdf, 'Helvetica', 8);
        $pdf->showBoxed($this->dueDate, $valueCol, 762, $valueWidth, 13, 'left', '');
        $pdf->showBoxed($this->issueDate, $valueCol, 752, $valueWidth, 13, 'left', '');
        if ($this->showPropertyDetail) {
            $pdf->showBoxed($this->propertyID, $valueCol, 742, $valueWidth, 13, 'left', '');
        }

        if ($this->showPropertyDetail) {
            $pdf->showBoxed($this->propertyName, $valueCol, 732, $valueWidth, 13, 'left', '');
        }

        if ($this->showPropertyDetail) {
            $pdf->showBoxed($this->leaseID, $valueCol, 722, $valueWidth, 13, 'left', '');
        }

        $showPropertyDetail = $this->showPropertyDetail ? 0 : -30;
        $pdf->showBoxed($this->leaseName, $valueCol, 712 - $showPropertyDetail, $valueWidth, 13, 'left', '');

        $this->setFont($pdf, 'Helvetica-Bold', 8);
        if ($this->showOwnerDetail) {
            $pdf->showBoxed(
                (($this->isStrata) ? 'Entity' : 'Owner'),
                $labelCol,
                702 - $showPropertyDetail,
                $labelWidth,
                13,
                'right',
                ''
            );
        }

        if ($this->showOwnerDetail) {
            $pdf->showBoxed(
                (($this->isStrata) ? $business_label : 'Owner ' . $business_label),
                $labelCol,
                692 - $showPropertyDetail,
                $labelWidth,
                13,
                'right',
                ''
            );
        }

        $this->setFont($pdf, 'Helvetica', 8);

        if ($this->showOwnerDetail) {
            $pdf->showBoxed($this->ownerName, $valueCol, 702 - $showPropertyDetail, 250, 13, 'left', '');
        }

        if ($this->showOwnerDetail) {
            $pdf->showBoxed($business_number, $valueCol, 692 - $showPropertyDetail, 250, 13, 'left', '');
        }
    }
}

class InvoiceFooter extends PDFobject
{
    public $payway;

    public $payID;

    public $creditCard;

    public $is_ledger;

    public $isStrata;

    public $linkProvider;

    public $payIDCode;

    public $paywayBillerCode;

    public $dueDate;

    public $propertyID;

    public $propertyName;

    public $leaseID;

    public $leaseName;

    public $ownerName;

    public $ownerABN;

    public $business_label;

    public bool $displayBsb;

    public $title;

    public $premises;

    public $crn;

    public $officeName;

    public $officeAddress;

    public $officeCity;

    public $officeState;

    public $officePostCode;

    public $officeNotes;

    public $mailingName;

    public $mailingAddress;

    public $mailingCity;

    public $mailingState;

    public $mailingPostCode;

    public $bankAccount;

    public $bankAccountName;

    public $bankBSB;

    public $bankName;

    public $directDeposit;

    public $directDebitAuth;

    public $bpayBillerCode;

    public $showEftInfo;

    public $showPropertyCode;

    public $deft;

    public $totalAmount;

    public $showOwnerDetail;

    public $showPropertyDetail;

    public $showDueDate;

    public $eftReference;

    public string $bsbLabel;

    public function __construct($propertyID, $leaseID, $dueDate, $title, $crn = null)
    {
        $this->propertyID = $propertyID;
        $this->leaseID = $leaseID;
        $this->dueDate = $dueDate;
        $this->title = $title;
        $this->crn = $crn;
        $this->premises = dbGetLeaseDescription($propertyID, $leaseID);

        $this->business_label = $_SESSION['country_default']['business_label'];
        $this->displayBsb = getDisplayBsbFromSession();
        $this->bsbLabel = getBsbLabelFromSession();
    }

    public function preRender(&$pdf) {}

    public function render(&$pdf)
    {
        $ownereft = dbGetEFTPropertyOwnerDetail($this->propertyID);
        if (isset($ownereft['accountNo'])) {
            $this->bpayBillerCode = false;
            $this->deft = false;
            $this->payway = false;
            $this->payID = false;
            $this->creditCard = false;
        }

        $deft_adj_y = 0;
        if ($this->deft) {
            $deft_adj_y = 27;
        }

        $bpay_adj_y = 0;
        if ($this->bpayBillerCode) {
            $bpay_adj_y = 50;
        }

        $deft_adj_y = 0;
        if ($this->deft) {
            $deft_adj_y = 27;
        }

        $remittance_advice_adj_y = 0;
        $bpay_adj_y = 0;
        if ($this->bpayBillerCode) {
            $bpay_adj_y = 50;
            $remittance_advice_adj_y = 40;
        }

        $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        $pdf->setlinewidth(0.5);
        // Grey Areas...
        $pdf->setColorExt('both', 'rgb', 0.9, 0.9, 0.9, 0); // setting the color to Gray
        $pdf->rect(360, 231 - $remittance_advice_adj_y - $bpay_adj_y, 210, 15);
        $pdf->fill();

        if ($this->showEftInfo) {
            $pdf->rect(25, 240 - $deft_adj_y, 300, 15);
            $pdf->fill();
        }


        // REMITTANCE BOTTOM BAR
        $pdf->setColorExt('both', 'rgb', 0.9, 0.9, 0.9, 0);
        $pdf->rect(360, 100 - $bpay_adj_y, 210, 15);
        $pdf->fill();
        // Horizontal Lines...

        $pdf->setColorExt('both', 'rgb', 0.75, 0.75, 0.75, 0);

        $pdf->moveto(360, 231 - $remittance_advice_adj_y - $bpay_adj_y);
        $pdf->lineto(570, 231 - $remittance_advice_adj_y - $bpay_adj_y);
        $pdf->stroke();

        $pdf->moveto(360, 155 - $remittance_advice_adj_y - $bpay_adj_y);
        $pdf->lineto(570, 155 - $remittance_advice_adj_y - $bpay_adj_y);
        $pdf->stroke();

        if ($this->showEftInfo) {
            $pdf->moveto(25, 240 - $deft_adj_y);
            $pdf->lineto(325, 240 - $deft_adj_y);
            $pdf->stroke();
        }

        $pdf->setlinewidth(1.1);
        $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        $pdf->rect(360, 100 - $bpay_adj_y, 210, 146 - $remittance_advice_adj_y);
        $pdf->stroke();


        if ($this->showEftInfo) {
            $pdf->rect(25, 190 - $deft_adj_y, 300, 65);
            $pdf->stroke();
        }

        // Text
        $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);

        $this->setFont($pdf, 'Helvetica-Bold', 8);
        $pdf->showBoxed('REMITTANCE ADVICE', 360, 230 - $remittance_advice_adj_y - $bpay_adj_y, 210, 12, 'center', '');
        // -- end of codes cut from preRender

        $this->setFont($pdf, 'Helvetica', 8);
        $pdf->showBoxed(
            "{$this->officeName}\r\n{$this->officeAddress}\r\n{$this->officeCity}, {$this->officeState} {$this->officePostCode}",
            95,
            50,
            232,
            42,
            'left',
            ''
        );
        $this->setFont($pdf, 'Helvetica', 7);
        $memoHeight = $this->showEftInfo ? 0 : 70;
        if (strpos($this->officeNotes, "\n") !== false) {
            $pdf->showBoxed("{$this->officeNotes}", 95, $memoHeight, 232, 62, 'left', '');
        } else {
            $pdf->showBoxed(substr($this->officeNotes, 0, 70), 95, $memoHeight, 232, 62, 'left', '');
        }

        $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        $this->setFont($pdf, 'Helvetica', 8);
        $this->setFont($pdf, 'Helvetica-Bold', 7);

        if ($this->is_ledger) {
            if ($this->isStrata) {
                if ($this->bpayBillerCode) {
                    if ($this->showOwnerDetail) {
                        if ($this->showPropertyDetail) {
                            $pdf->showBoxed(
                                "Ledger\n\nSub-Ledger\n\nOwner" . (trim($this->premises) !== '' && trim($this->premises) !== '0' ? "\nPremises" : ''),
                                366,
                                95 - $bpay_adj_y,
                                40,
                                83,
                                'right',
                                ''
                            );
                        } else {
                            $pdf->showBoxed(
                                "Sub-Ledger\nOwner" . (trim($this->premises) !== '' && trim($this->premises) !== '0' ? "\nPremises" : ''),
                                366,
                                95 - $bpay_adj_y,
                                40,
                                83,
                                'right',
                                ''
                            );
                        }
                    } elseif ($this->showPropertyDetail) {
                        $pdf->showBoxed("Ledger\n\nSub-Ledger", 366, 95 - $bpay_adj_y, 40, 83, 'right', '');
                    } else {
                        $pdf->showBoxed('Sub-Ledger', 366, 95 - $bpay_adj_y, 40, 83, 'right', '');
                    }
                } elseif ($this->showOwnerDetail) {
                    if ($this->showPropertyDetail) {
                        $pdf->showBoxed(
                            "Ledger\n\nSub-Ledger\n\nOwner" . (trim($this->premises) !== '' && trim($this->premises) !== '0' ? "\nPremises" : ''),
                            366,
                            140,
                            40,
                            83,
                            'right',
                            ''
                        );
                    } else {
                        $pdf->showBoxed(
                            "Sub-Ledger\nOwner" . (trim($this->premises) !== '' && trim($this->premises) !== '0' ? "\nPremises" : ''),
                            366,
                            140,
                            40,
                            83,
                            'right',
                            ''
                        );
                    }
                } elseif ($this->showPropertyDetail) {
                    $pdf->showBoxed("Ledger\n\nSub-Ledger", 366, 140, 40, 83, 'right', '');
                } else {
                    $pdf->showBoxed('Sub-Ledger', 366, 140, 40, 83, 'right', '');
                }
            } elseif ($this->bpayBillerCode) {
                if ($this->showOwnerDetail) {
                    if ($this->showPropertyDetail) {
                        $pdf->showBoxed(
                            "Ledger\n\nSub-Ledger\n\nOwner" . (trim($this->premises) !== '' && trim($this->premises) !== '0' ? "\nPremises" : ''),
                            362,
                            95 - $bpay_adj_y,
                            40,
                            83,
                            'right',
                            ''
                        );
                    } else {
                        $pdf->showBoxed(
                            "Sub-Ledger\nOwner" . (trim($this->premises) !== '' && trim($this->premises) !== '0' ? "\nPremises" : ''),
                            362,
                            95 - $bpay_adj_y,
                            40,
                            83,
                            'right',
                            ''
                        );
                    }
                } elseif ($this->showPropertyDetail) {
                    $pdf->showBoxed("Ledger\n\nSub-Ledger", 362, 95 - $bpay_adj_y, 40, 83, 'right', '');
                } else {
                    $pdf->showBoxed('Sub-Ledger', 362, 95 - $bpay_adj_y, 40, 83, 'right', '');
                }
            } elseif ($this->showOwnerDetail) {
                if ($this->showPropertyDetail) {
                    $pdf->showBoxed(
                        "Ledger\n\nSub-Ledger\n\nOwner" . (trim($this->premises) !== '' && trim($this->premises) !== '0' ? "\nPremises" : ''),
                        362,
                        140,
                        40,
                        83,
                        'right',
                        ''
                    );
                } else {
                    $pdf->showBoxed(
                        "Sub-Ledger\nOwner" . (trim($this->premises) !== '' && trim($this->premises) !== '0' ? "\nPremises" : ''),
                        362,
                        140,
                        40,
                        83,
                        'right',
                        ''
                    );
                }
            } elseif ($this->showPropertyDetail) {
                $pdf->showBoxed("Ledger\n\nSub-Ledger", 362, 140, 40, 83, 'right', '');
            } else {
                $pdf->showBoxed('Sub-Ledger', 362, 140, 40, 83, 'right', '');
            }
        } elseif ($this->isStrata) {
            if ($this->bpayBillerCode) {
                if ($this->showOwnerDetail) {
                    if ($this->showPropertyDetail) {
                        $pdf->showBoxed(
                            "Property\n\nOwner\n\nEntity\nPremises",
                            360,
                            95 - $bpay_adj_y,
                            45,
                            83,
                            'right',
                            ''
                        );
                    } else {
                        $pdf->showBoxed("Owner\nEntity\nPremises", 360, 95 - $bpay_adj_y, 45, 83, 'right', '');
                    }
                } elseif ($this->showPropertyDetail) {
                    $pdf->showBoxed("Property\n\nLease", 362, 95 - $bpay_adj_y, 40, 83, 'right', '');
                } else {
                    $pdf->showBoxed('Lease', 362, 95 - $bpay_adj_y, 40, 83, 'right', '');
                }
            } elseif ($this->showOwnerDetail) {
                if ($this->showPropertyDetail) {
                    $pdf->showBoxed("Property\n\nOwner\n\nEntity\nPremises", 360, 140, 45, 83, 'right', '');
                } else {
                    $pdf->showBoxed("Owner\nEntity\nPremises", 360, 140, 45, 83, 'right', '');
                }
            } elseif ($this->showPropertyDetail) {
                $pdf->showBoxed("Property\n\nLease", 362, 140, 40, 83, 'right', '');
            } else {
                $pdf->showBoxed('Lease', 362, 140, 40, 83, 'right', '');
            }
        } elseif ($this->bpayBillerCode) {
            if ($this->showOwnerDetail) {
                if ($this->showPropertyDetail) {
                    $pdf->showBoxed(
                        "Property\n\nLease\n\nOwner\nPremises",
                        358,
                        95 - $bpay_adj_y,
                        40,
                        83,
                        'right',
                        ''
                    );
                } else {
                    $pdf->showBoxed("Lease\nOwner\nPremises", 358, 95 - $bpay_adj_y, 40, 83, 'right', '');
                }
            } elseif ($this->showPropertyDetail) {
                $pdf->showBoxed("Property\n\nLease", 358, 95 - $bpay_adj_y, 40, 83, 'right', '');
            } else {
                $pdf->showBoxed('Lease', 358, 95 - $bpay_adj_y, 40, 83, 'right', '');
            }
        } elseif ($this->showOwnerDetail) {
            if ($this->showPropertyDetail) {
                $pdf->showBoxed("Property\n\nLease\n\nOwner\nPremises", 358, 140, 40, 83, 'right', '');
            } else {
                $pdf->showBoxed("Lease\nOwner\nPremises", 358, 140, 40, 83, 'right', '');
            }
        } elseif ($this->showPropertyDetail) {
            $pdf->showBoxed("Property\n\nLease", 358, 140, 40, 83, 'right', '');
        } else {
            $pdf->showBoxed('Lease', 358, 140, 40, 83, 'right', '');
        }

        if ($this->bpayBillerCode) {
            $adjustX = ($this->showEftInfo) ? 0 : 5;
            if (! $this->deft && $this->showEftInfo) {
                $adjustX = 13;
            }

            if ($this->creditCard && ! $this->showEftInfo) {
                $adjustX = 16;
            }

            if ($this->payway && ! $this->showEftInfo) {
                $adjustX = 16;
            }

            if ($this->payID && ! $this->showEftInfo) {
                $adjustX = 16;
            }

            $bpayX = 365;
            $bpayY = 237;
            $bpayW = 180;
            $maxWidth = 201;
            $maxHeight = 70;
            $image = $pdf->load_image('auto', BPAY, '');
            $pdf->fit_image(
                $image,
                364,
                169 + $adjustX,
                'boxsize {' . "{$maxWidth} {$maxHeight}" . '} position={center} fitmethod=entire'
            );
            $this->setFont($pdf, 'Helvetica-Bold', 9);
            $pdf->showBoxed("{$this->bpayBillerCode}", $bpayX + 90, 218 + $adjustX, 100, 13, 'left', '');
            $pdf->showBoxed(
                ($this->deft) ? $this->bankAccount . '' . $this->crn : $this->crn,
                $bpayX + 57,
                208 + $adjustX,
                100,
                13,
                'left',
                ''
            );

            $pdf->setColorExt('both', 'rgb', 0.21, 0.21, 0.21, 0.21);
            $this->setFont($pdf, 'Helvetica', 6);
            $pdf->showBoxed(
                '® Registered to BPAY Pty Ltd ABN **************',
                $bpayX + 2,
                159 + $adjustX,
                200,
                13,
                'left',
                ''
            );
            $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);

            // ®Registered to BPAY Pty Ltd ABN **************

            $pdf->setlinewidth(1.1);
            $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
            $pdf->rect(360, 163 + $adjustX, 210, 80);
            $pdf->stroke();
        }

        $this->setFont($pdf, 'Helvetica-Bold', 7);

        if ($this->showEftInfo) {
            if ($this->displayBsb) {
                $pdf->showBoxed(
                    "Account\nBank\n{$this->bsbLabel}\nAccount No",
                    30,
                    100 - $deft_adj_y,
                    50,
                    138,
                    'right',
                    ''
                );
            } else {
                $pdf->showBoxed("Account\nBank\nAccount No", 30, 100 - $deft_adj_y, 50, 138, 'right', '');
            }
        }


        $this->setFont($pdf, 'Helvetica', 7);
        if (strlen($this->ownerName) > 38) {
            $this->ownerName = substr($this->ownerName, 0, 38);
        }

        if (strlen($this->premises) > 38) {
            $this->premises = substr($this->premises, 0, 38);
        }

        if (strlen($this->leaseName) > 38) {
            $this->leaseName = substr($this->leaseName, 0, 38);
        }

        if ($this->bpayBillerCode) {
            if ($this->showOwnerDetail) {
                if ($this->showPropertyDetail) {
                    $pdf->showBoxed(
                        "{$this->propertyID}\n{$this->propertyName}\n{$this->leaseID}\n{$this->leaseName}\n{$this->ownerName}\n{$this->premises}",
                        408,
                        95 - $bpay_adj_y,
                        415,
                        83,
                        'left',
                        ''
                    );
                } else {
                    $pdf->showBoxed(
                        "{$this->leaseName}\n{$this->ownerName}\n{$this->premises}",
                        408,
                        95 - $bpay_adj_y,
                        415,
                        83,
                        'left',
                        ''
                    );
                }
            } elseif ($this->showPropertyDetail) {
                $pdf->showBoxed(
                    "{$this->propertyID}\n{$this->propertyName}\n{$this->leaseID}\n{$this->leaseName}",
                    408,
                    95 - $bpay_adj_y,
                    415,
                    83,
                    'left',
                    ''
                );
            } else {
                $pdf->showBoxed("{$this->leaseName}", 408, 95 - $bpay_adj_y, 415, 83, 'left', '');
            }
        } elseif ($this->showOwnerDetail) {
            if ($this->showPropertyDetail) {
                $pdf->showBoxed(
                    "{$this->propertyID}\n{$this->propertyName}\n{$this->leaseID}\n{$this->leaseName}\n{$this->ownerName}\n{$this->premises}",
                    408,
                    140,
                    415,
                    83,
                    'left',
                    ''
                );
            } else {
                $pdf->showBoxed(
                    "{$this->leaseName}\n{$this->ownerName}\n{$this->premises}",
                    408,
                    140,
                    415,
                    83,
                    'left',
                    ''
                );
            }
        } elseif ($this->showPropertyDetail) {
            $pdf->showBoxed(
                "{$this->propertyID}\n{$this->propertyName}\n{$this->leaseID}\n{$this->leaseName}",
                408,
                140,
                415,
                83,
                'left',
                ''
            );
        } else {
            $pdf->showBoxed("{$this->leaseName}", 408, 140, 415, 83, 'left', '');
        }

        $this->setFont($pdf, 'Helvetica', 8);

        if ($this->directDebitAuth && $this->directDeposit == 1 && $this->deft != 1 && $this->creditCard != 1 && $this->payway != 1 && $this->payID != 1) {
            $this->setFont($pdf, 'Helvetica-Oblique', 6);
            $pdf->showBoxed(
                '** As you have provided direct debit authority, the total amount payable will be debited on the due date.',
                30,
                140,
                275,
                13,
                'left',
                ''
            );
            $this->setFont($pdf, 'Helvetica', 8);
        }

        // DEFT Reference Number
        if ($this->deft) {
            $deft_adj_y2 = 12;
            $deft_logo = SYSTEMPATH . DIRECTORY_SEPARATOR . 'assets' . DIRECTORY_SEPARATOR . 'images' . DIRECTORY_SEPARATOR . 'logo_deft.jpg';
            if (file_exists($deft_logo)) {
                $image = $pdf->load_image('auto', $deft_logo, '');
                $pdf->fit_image($image, 25, 232, 'scale .35');
            }


            $pdf->setlinewidth(1.1);
            $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
            if (! $this->showEftInfo) {
                $pdf->rect(25, 168, 300, 58);
            } else {
                $pdf->rect(25, 110 - $deft_adj_y2, 300, 58);
            }

            $pdf->stroke();

            $pdf->setlinewidth(0.3);
            $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
            $this->setFont($pdf, 'Helvetica-Bold', 7);
            if (! $this->showEftInfo) {
                $pdf->showBoxed(
                    'DEFT Reference Number : ' . $this->bankAccount . '' . $this->crn,
                    25,
                    190,
                    300,
                    30,
                    'center',
                    ''
                );
            } else {
                $pdf->showBoxed(
                    'DEFT Reference Number : ' . $this->bankAccount . '' . $this->crn,
                    25,
                    136 - $deft_adj_y2,
                    300,
                    30,
                    'center',
                    ''
                );
            }

            $this->setFont($pdf, 'Helvetica', 7);
            if (! $this->showEftInfo) {
                $pdf->showBoxed(
                    "Pay by credit card or registered bank account at www.deft.com.au. \nPayments by credit card will attract a surcharge.",
                    30,
                    180,
                    290,
                    30,
                    'center',
                    ''
                );
            } else {
                $pdf->showBoxed(
                    "Pay by credit card or registered bank account at www.deft.com.au. \nPayments by credit card will attract a surcharge.",
                    30,
                    126 - $deft_adj_y2,
                    290,
                    30,
                    'center',
                    ''
                );
            }

            $cc_logo = BASEPATH . DIRECTORY_SEPARATOR . 'assets' . DIRECTORY_SEPARATOR . 'images' . DIRECTORY_SEPARATOR . 'logo_credit_cards_2.png';
            [$imageWidth, $imageHeight] = getimagesize($cc_logo);
            $image = $pdf->load_image('auto', $cc_logo, '');
            if (! $this->showEftInfo) {
                $pdf->fit_image($image, 72, 170, 'scale ' . 23 / $imageHeight);
            } else {
                $pdf->fit_image($image, 72, 113 - $deft_adj_y2, 'scale ' . 23 / $imageHeight);
            }

            if ($this->deft) {
                $this->setOption($pdf, 11);
                $reference = $this->crn . '' . '<';
                $biller = '+' . $this->bankAccount;
                $this->setOption($pdf, 11);
            }
        }

        if ($this->showEftInfo) {
            if (isset($ownereft['accountNo'])) {
                $bankAccountNameTemp = $ownereft['accountName'];
                $bankNameTemp = $ownereft['bankName'];
                $bankBSBTemp = $ownereft['bsbNo'];
                $bankAccountTemp = $ownereft['accountNo'];
            } else {
                $bankAccountNameTemp = $this->bankAccountName;
                $bankNameTemp = $this->bankName;
                $bankBSBTemp = $this->bankBSB;
                $bankAccountTemp = $this->bankAccount;
            }

            $this->setFont($pdf, 'Helvetica', 7);
            if ($this->displayBsb) {
                $pdf->showBoxed(
                    "{$bankAccountNameTemp}\n{$bankNameTemp}\n" . formatWithDelimiter(
                        $bankBSBTemp
                    ) . "\n" . ($bankAccountTemp),
                    90,
                    100 - $deft_adj_y,
                    250,
                    138,
                    'left',
                    ''
                );
            } else {
                $pdf->showBoxed(
                    "{$bankAccountNameTemp}\n{$bankNameTemp}\n" . ($bankAccountTemp),
                    90,
                    100 - $deft_adj_y,
                    250,
                    138,
                    'left',
                    ''
                );
            }

            $this->setFont($pdf, 'Helvetica-BoldOblique', 7);
            $pdf->showBoxed(
                'If paying electronically please quote - ' . ($this->eftReference == 1 ? (($this->showPropertyCode ? "{$this->propertyID}/" : '') . "{$this->leaseID}") : $this->crn),
                30,
                190 - $deft_adj_y,
                250,
                13,
                'left',
                ''
            );
        }

        $this->setFont($pdf, 'Helvetica-Bold', 8);
        $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0); // Setting the Color to Gray
        $pdf->showBoxed(
            $this->title,
            367,
            92 - $bpay_adj_y,
            185,
            20,
            'left',
            ''
        ); // STATEMENT TOTAL (bottom right under remittance advice)

        if ($this->creditCard) {
            $pdf->setColorExt('both', 'rgb', 0.9, 0.9, 0.9, 0); // Setting the Color to Gray

            if (! $this->showEftInfo) {
                $pdf->rect(25, 243, 300, 15);
            } else {
                $pdf->rect(25, 170, 300, 15);
            }

            $pdf->fill();


            $pdf->setlinewidth(1.1);
            $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
            if (! $this->showEftInfo) {
                $pdf->rect(25, 193, 300, 65);
            } else {
                $pdf->rect(25, 120, 300, 65);
            }

            $pdf->stroke();
            $pdf->setlinewidth(0.3);
            $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
            $this->setFont($pdf, 'Helvetica-Bold', 7);
            if (! $this->showEftInfo) {
                $pdf->showBoxed(' CREDIT CARD PAYMENTS ', 25, 225, 300, 30, 'center', '');
            } else {
                $pdf->showBoxed(' CREDIT CARD PAYMENTS ', 25, 153, 300, 30, 'center', '');
            }

            if (! $this->showEftInfo) {
                $this->setFont($pdf, 'Helvetica-Bold', 7);
                $pdf->showBoxed('Web Link ', 48, 211, 290, 30, 'left', '');
                $pdf->setColorExt('both', 'rgb', 0, 0, 0.75, 0);
                $this->setFont($pdf, 'Helvetica', 7);
                $pdf->showBoxed($this->linkProvider, 90, 211, 290, 30, 'left', '');
            } else {
                $this->setFont($pdf, 'Helvetica-Bold', 7);
                $pdf->showBoxed('Web Link ', 48, 138, 290, 30, 'left', '');
                $pdf->setColorExt('both', 'rgb', 0, 0, 0.75, 0);
                $this->setFont($pdf, 'Helvetica', 7);
                $pdf->showBoxed($this->linkProvider, 90, 138, 290, 30, 'left', '');
            }

            $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
            if (! $this->showEftInfo) {
                $this->setFont($pdf, 'Helvetica-Bold', 7);
                $pdf->showBoxed('Reference ', 45, 204, 290, 30, 'left', '');
                $this->setFont($pdf, 'Helvetica', 7);
                $pdf->showBoxed($this->crn, 90, 204, 290, 30, 'left', '');
            } else {
                $this->setFont($pdf, 'Helvetica-Bold', 7);
                $pdf->showBoxed('Reference ', 45, 130, 290, 30, 'left', '');
                $this->setFont($pdf, 'Helvetica', 7);
                $pdf->showBoxed($this->crn, 90, 130, 290, 30, 'left', '');
            }

            if (! $this->showEftInfo) {
                $pdf->showBoxed('(Credit Card payments attract a % surcharge)', 27, 197, 290, 30, 'left', '');
            } else {
                $pdf->showBoxed('(Credit Card payments attract a % surcharge)', 27, 123, 290, 30, 'left', '');
            }

            $cc_logo = BASEPATH . DIRECTORY_SEPARATOR . 'assets' . DIRECTORY_SEPARATOR . 'images' . DIRECTORY_SEPARATOR . 'logo_credit_cards_2.png';
            [$imageWidth, $imageHeight] = getimagesize($cc_logo);
            $image = $pdf->load_image('auto', $cc_logo, '');
            if (! $this->showEftInfo) {
                $pdf->fit_image($image, 72, 193, 'scale ' . 23 / $imageHeight);
            } else {
                $pdf->fit_image($image, 72, 120, 'scale ' . 23 / $imageHeight);
            }
        }

        if ($this->payID) {
            $pdf->setlinewidth(1.1);
            $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
            if (! $this->showEftInfo) {
                $pdf->rect(25, 216, 300, 42);
            } else {
                $pdf->rect(25, 143, 300, 42);
            }

            $pdf->stroke();
            $pdf->setlinewidth(0.3);
            $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
            $this->setFont($pdf, 'Helvetica-Bold', 9);
            if (! $this->showEftInfo) {
                $pdf->showBoxed(" PayID\u{00AE}:", 46, 222, 300, 30, 'left', '');
                $pdf->showBoxed($this->payIDCode, 90, 222, 300, 30, 'left', '');
            } else {
                $pdf->showBoxed(" PayID\u{00AE}:", 46, 150, 300, 30, 'left', '');
                $pdf->showBoxed($this->payIDCode, 90, 150, 300, 30, 'left', '');
            }

            $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);

            if (! $this->showEftInfo) {
                $pdf->showBoxed('Ref: ', 65, 213, 290, 30, 'left', '');
                $pdf->showBoxed($this->crn, 90, 213, 290, 30, 'left', '');
            } else {
                $pdf->showBoxed('Ref: ', 65, 140, 290, 30, 'left', '');
                $pdf->showBoxed($this->crn, 90, 140, 290, 30, 'left', '');
            }

            $cc_logo = BASEPATH . DIRECTORY_SEPARATOR . 'assets' . DIRECTORY_SEPARATOR . 'images' . DIRECTORY_SEPARATOR . 'payID.png';
            [$imageWidth, $imageHeight] = getimagesize($cc_logo);
            $image = $pdf->load_image('auto', $cc_logo, '');
            if (! $this->showEftInfo) {
                $pdf->fit_image($image, 257, 220, 'scale ' . 70 / $imageHeight);
            } else {
                $pdf->fit_image($image, 257, 149, 'scale ' . 70 / $imageHeight);
            }

            $this->setFont($pdf, 'Helvetica', 6);
            if (! $this->showEftInfo) {
                $pdf->showBoxed(
                    'PayID is a registered trademark of NPP Australia Limited.',
                    30,
                    196,
                    290,
                    30,
                    'left',
                    ''
                );
            } else {
                $pdf->showBoxed(
                    'PayID is a registered trademark of NPP Australia Limited.',
                    30,
                    124,
                    290,
                    30,
                    'left',
                    ''
                );
            }
        }


        if ($this->payway) {
            $pdf->setlinewidth(1.1);
            $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
            if (! $this->showEftInfo) {
                $pdf->rect(25, 216, 300, 42);
            } else {
                $pdf->rect(25, 143, 300, 42);
            }

            $pdf->stroke();
            $pdf->setlinewidth(0.3);
            $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
            $this->setFont($pdf, 'Helvetica-Bold', 7);
            if (! $this->showEftInfo) {
                $pdf->showBoxed(' Credit Card Payments: ', 25, 225, 300, 30, 'left', '');
                $pdf->setColorExt('both', 'rgb', 0, 0, 0.75, 0);
                $pdf->showBoxed('www.payway.com.au', 105, 225, 300, 30, 'left', '');
            } else {
                $pdf->showBoxed(' Credit Card Payments:', 25, 153, 300, 30, 'left', '');
                $pdf->setColorExt('both', 'rgb', 0, 0, 0.75, 0);
                $pdf->showBoxed('www.payway.com.au', 105, 153, 300, 30, 'left', '');
            }

            $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);

            if (! $this->showEftInfo) {
                $this->setFont($pdf, 'Helvetica-Bold', 7);
                $pdf->showBoxed('Biller Code ', 43, 218, 290, 30, 'left', '');
                $this->setFont($pdf, 'Helvetica', 7);
                $pdf->showBoxed($this->paywayBillerCode, 90, 218, 290, 30, 'left', '');
            } else {
                $this->setFont($pdf, 'Helvetica-Bold', 7);
                $pdf->showBoxed('Biller Code ', 43, 145, 290, 30, 'left', '');
                $this->setFont($pdf, 'Helvetica', 7);
                $pdf->showBoxed($this->paywayBillerCode, 90, 145, 290, 30, 'left', '');
            }

            if (! $this->showEftInfo) {
                $this->setFont($pdf, 'Helvetica-Bold', 7);
                $pdf->showBoxed('Tenant No ', 46, 211, 290, 30, 'left', '');
                $this->setFont($pdf, 'Helvetica', 7);
                $pdf->showBoxed($this->crn, 90, 211, 290, 30, 'left', '');
            } else {
                $this->setFont($pdf, 'Helvetica-Bold', 7);
                $pdf->showBoxed('Tenant No ', 46, 137, 290, 30, 'left', '');
                $this->setFont($pdf, 'Helvetica', 7);
                $pdf->showBoxed($this->crn, 90, 137, 290, 30, 'left', '');
            }

            if (! $this->showEftInfo) {
                $this->setFont($pdf, 'Helvetica-Bold', 7);
                $pdf->showBoxed('Acc Name ', 46, 204, 290, 30, 'left', '');
                $this->setFont($pdf, 'Helvetica', 7);
                $pdf->showBoxed($this->bankAccountName, 90, 204, 290, 30, 'left', '');
            } else {
                $this->setFont($pdf, 'Helvetica-Bold', 7);
                $pdf->showBoxed('Acc Name ', 46, 130, 290, 30, 'left', '');
                $this->setFont($pdf, 'Helvetica', 7);
                $pdf->showBoxed($this->bankAccountName, 90, 130, 290, 30, 'left', '');
            }

            $cc_logo = BASEPATH . DIRECTORY_SEPARATOR . 'assets' . DIRECTORY_SEPARATOR . 'images' . DIRECTORY_SEPARATOR . 'westpac.png';
            [$imageWidth, $imageHeight] = getimagesize($cc_logo);
            $image = $pdf->load_image('auto', $cc_logo, '');
            if (! $this->showEftInfo) {
                $pdf->fit_image($image, 257, 237, 'scale ' . 23 / $imageHeight);
            } else {
                $pdf->fit_image($image, 257, 166, 'scale ' . 23 / $imageHeight);
            }

            if (! $this->showEftInfo) {
                $pdf->showBoxed('(Credit Card payments attract a % surcharge)', 27, 196, 290, 30, 'left', '');
            } else {
                $pdf->showBoxed('(Credit Card payments attract a % surcharge)', 27, 124, 290, 30, 'left', '');
            }
        }


        if ($this->showEftInfo) {
            $this->setFont($pdf, 'Helvetica-Bold', 8);
            $pdf->showBoxed('EFT PAYMENT DETAILS', 75, 239 - $deft_adj_y, 210, 12, 'center', '');
        }
    }
}

class MailAddressWindow extends PDFobject
{
    public $attention;

    public $name;

    public $street;

    public $city;

    public $lease;

    public $state;

    public $postcode;

    public $country;

    public function __construct($propertyID, $leaseID, $is_ledger = null)
    {
        if ($is_ledger) {
            $mailAddress = dbGetSubLedgerMailingAddress($propertyID, $leaseID);
        } else {
            $mailAddress = dbGetMailingAddress($propertyID, $leaseID);
            if (! $mailAddress) {
                $mailAddress = dbGetLeaseMailingAddress($propertyID, $leaseID);
            }
        }

        $this->name = stripslashes_deep($mailAddress['mailingName']);
        $this->lease = (isset($mailAddress['mailingLease'])) ? stripslashes_deep($mailAddress['mailingLease']) : null;
        $this->street = $mailAddress['mailingAddress'];
        $this->city = $mailAddress['mailingCity'];
        $this->state = $mailAddress['mailingState'];
        $this->postcode = $mailAddress['mailingPostCode'];
        if ($mailAddress['mailingCountry']) {
            $this->country = ($mailAddress['mailingCountry'] == 'AU') ? '' : dbGetCountryName(
                $mailAddress['mailingCountry']
            );
        }
    }

    public function preRender(&$pdf) {}

    public function render(&$pdf)
    {
        $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        $this->setFont($pdf, 'Helvetica', 10); // This is for the disclaimer information... /
        $address = (($this->attention) ? "{$this->attention}\n" : '') . "{$this->name}\n" . (($this->lease) ? "{$this->lease}\n" : '') . "{$this->street}\n{$this->city} {$this->state} {$this->postcode}\n{$this->country}";
        $pdf->showBoxed($address, 95, 600, 242, 60, 'left', ''); // This is for the from field
    }
}

class LeaseDescription extends PDFObject
{
    public $description;

    public $is_ledger;

    public function __construct($propertyID, $leaseID)
    {
        $this->description = dbGetLeaseDescription($propertyID, $leaseID);
    }

    public function preRender(&$pdf) {}

    public function render(&$pdf)
    {
        $pdf->setColorExt('both', 'rgb', 0.95, 0.95, 0.95, 0);
        $pdf->setlinewidth(0.5);
        $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        $this->setFont($pdf, 'Helvetica-Bold', 9);
        if (! $this->is_ledger || ($this->is_ledger && trim($this->description))) {
            $pdf->showBoxed('PREMISES : ' . $this->description, 30, 583, 535, 22, 'left', '');
        }
    }
}

class InvoiceLines extends PDFobject
{
    public $title;

    public $isResidential;

    public $showDueDate;

    public $is_ledger;

    public $withTax;

    public function __construct($title)
    {
        $this->title = $title;
    }

    public function preRender(&$pdf)
    {
        global $sess;
        $currency = $_SESSION['country_default']['currency_symbol'];
        $tax_label = $_SESSION['country_default']['tax_label'];

        $pdf->setColorExt('both', 'rgb', 0.9, 0.9, 0.9, 0); // Setting the Color to Gray
        $pdf->rect(24, 546, 545, 15);
        $pdf->fill();
        $pdf->rect(24, 285, 410, 15);
        $pdf->fill();
        $pdf->setlinewidth(0.5);

        $pdf->setColorExt('both', 'rgb', 0.75, 0.75, 0.75, 0);
        $pdf->moveto(24, 546);
        $pdf->lineto(569, 546);
        $pdf->stroke();
        $pdf->moveto(24, 300);
        $pdf->lineto(569, 300);
        $pdf->stroke();


        $pdf->setlinewidth(1.1);
        $pdf->rect(24, 285, 545, 276);
        $pdf->stroke();
        $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);

        if (! $this->showDueDate) {
            $pdf->showBoxed('INV. DATE', 30, 546, 45, 13, 'left', '');
            $pdf->showBoxed(
                ($this->is_ledger && ! $this->withTax) ? 'INV. NO.' : 'TAX INV. NO.',
                75,
                546,
                65,
                13,
                'left',
                ''
            );
            $pdf->showBoxed('PARTICULARS', 135, 546, 300, 13, 'left', '');
        } else {
            $pdf->showBoxed('INV. DATE', 27, 546, 43, 13, 'left', '');
            $pdf->showBoxed('DUE DATE', 74, 546, 43, 13, 'left', '');
            $pdf->showBoxed(
                ($this->is_ledger && ! $this->withTax) ? 'INV. NO.' : 'TAX INV. NO.',
                120,
                546,
                58,
                13,
                'left',
                ''
            );
            $pdf->showBoxed('PARTICULARS', 176, 546, 203, 13, 'left', '');
        }

        if (! $this->isResidential && ! $this->is_ledger || $this->is_ledger && $this->withTax) {
            $pdf->showBoxed("NET ({$currency})", 402, 546, 45, 13, 'right', '');
            $pdf->showBoxed($tax_label . " ({$currency})", 462, 546, 45, 13, 'right', '');
        }

        $pdf->showBoxed("TOTAL ({$currency})", 522, 546, 45, 13, 'right', '');

        $this->setFont($pdf, 'Helvetica', 8);
    }

    public function render(&$pdf)
    {
        $this->setFont($pdf, 'Helvetica-Bold', 9);
        $pdf->showBoxed($this->title, 25, 286, 405, 13, 'right', '');
        $this->setFont($pdf, 'Helvetica', 8);
    }
}


class FoldLines extends PDFObject
{
    public function preRender(&$pdf)
    {
        $pdf->setColorExt('both', 'rgb', 0.8, 0.8, 0.8, 0);
        $pdf->setlinewidth(1);
        $pdf->moveto(0, 566);
        $pdf->lineto(2, 566);
        $pdf->stroke();
        $pdf->moveto(593, 566);
        $pdf->lineto(595, 566);
        $pdf->stroke();
        $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
    }

    public function render(&$pdf) {}
}

class CutOffLine extends PDFObject
{
    public function preRender(&$pdf)
    {
        $pdf->setColorExt('both', 'rgb', 0.6, 0.6, 0.6, 0); // Setting the Color to Black
        $pdf->setlinewidth(0.5);
        $pdf->set_graphics_option('dasharray={2 2} dashphase=0');

        $pdf->moveto(0, 281);
        $pdf->lineto(595, 281);
        $pdf->stroke();


        $this->setFont($pdf, 'ZapfDingbats', 18);
        $pdf->showBoxed('#', 25, 269, 20, 20, 'left', '');

        $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        $this->setFont($pdf, 'Helvetica-Oblique', 6);
        $pdf->showBoxed(
            'Please detach this section and return with your payment:',
            0,
            265,
            595,
            10,
            'center',
            ''
        );
    }

    public function render(&$pdf) {}
}

class InvoiceNote extends PDFObject
{
    public $note;

    public function __construct($note)
    {
        $this->note = $note;
    }

    public function preRender(&$pdf) {}

    public function render(&$pdf)
    {
        $pdf->setColorExt('both', 'rgb', 0.95, 0.95, 0.95, 0);
        $pdf->setlinewidth(0.5);
        $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        $this->setFont($pdf, 'Helvetica-Bold', 9);
        $pdf->showBoxed('NOTE: ' . $this->note, 30, 571, 535, 22, 'center', '');
    }
}


/*************************************************************************************************************************/

class Invoice extends PDFReport
{
    public $totalAmount;

    public $pageCount;

    public $resourceList;

    public $lineOffset = 15;

    public $logoFile = false;

    public $isResidential = false;

    public $bpayBillerCode;

    public $deft;

    public $payway;

    public $paywayBillerCode;

    public $payID;

    public $payIDCode;

    public $creditCard;

    public $linkProvider;

    public $officeState;

    public $showDueDate;

    public $is_ledger;

    public $withTax;

    public $propertyID;

    public function __construct(&$dataSource, $logoFile = false)
    {
        parent::__construct($dataSource);
        if ($logoFile) {
            $this->logoFile = $logoFile;
        }

        $this->pageWidth = 595;
        $this->pageHeight = 842;
    }

    public function updateLineOffset($offset, $amount = null)
    {
        $this->lineOffset += $offset;
        if ($this->lineOffset >= 245) {
            $this->renderTotal('continued...');
            $this->renderRemittanceTotal($amount);
            $this->renderOCRLine($amount);
            $this->endPage();
            $this->preparePage();
            $this->render();
        }
    }

    public function renderInvoiceRow(
        $date,
        $invoiceNumber,
        $description,
        $netAmount,
        $gstAmount,
        $grossAmount,
        $amount,
        $dueDate = null
    ) {
        $this->pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        $this->setFont('Helvetica-Bold', 8);
        $amountOffset = 5;

        if (! $this->showDueDate) {
            $this->pdf->showBoxed($date, 31, 546 - $this->lineOffset, 45, 13, 'left', '');
            $this->pdf->showBoxed($invoiceNumber, 76, 546 - $this->lineOffset, 75, 13, 'left', '');
            $this->pdf->showBoxed($description, 136, 546 - $this->lineOffset, 300, 13, 'left', '');
        } else {
            $this->pdf->showBoxed($date, 28, 546 - $this->lineOffset, 43, 13, 'left', '');
            $this->pdf->showBoxed($dueDate, 75, 546 - $this->lineOffset, 43, 13, 'left', '');
            $this->pdf->showBoxed($invoiceNumber, 121, 546 - $this->lineOffset, 58, 13, 'left', '');
            $this->pdf->showBoxed($description, 177, 546 - $this->lineOffset, 300, 13, 'left', '');
        }

        if (! $this->isResidential && ! $this->is_ledger || $this->is_ledger && $this->withTax) {
            $this->pdf->showBoxed($netAmount, 395 - $amountOffset, 546 - $this->lineOffset, 57, 13, 'right', '');
            $this->pdf->showBoxed($gstAmount, 455 - $amountOffset, 546 - $this->lineOffset, 57, 13, 'right', '');
        }

        $this->pdf->showBoxed($grossAmount, 515 - $amountOffset, 546 - $this->lineOffset, 57, 13, 'right', '');

        $this->updateLineOffset(10, $amount);
    }

    public function renderLeaseNote($note, $amount)
    {
        $this->updateLineOffset(10, $amount);
        $text = explode("\n", $note);


        foreach ($text as $key => $data) {
            $rowText = ($key == 0 ? 'NOTE: ' : '') . $data;
            while (strlen($rowText) > 168) {
                $this->pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
                $this->setFont('Helvetica-Oblique', 7);
                $tempText = substr($rowText, 0, 168);
                $rowText = substr($rowText, 168);
                $this->pdf->showBoxed($tempText, 30, 546 - $this->lineOffset, 535, 8, 'center', '');
                $this->updateLineOffset(8, $amount);
            }

            $this->pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
            $this->setFont('Helvetica-Oblique', 7);
            $this->pdf->showBoxed($rowText, 30, 546 - $this->lineOffset, 535, 8, 'center', '');
            $this->updateLineOffset(8, $amount);
        }
    }

    public function renderInvoiceRow2(
        $date,
        $dueDate,
        $invoiceNumber,
        $description,
        $netAmount,
        $gstAmount,
        $grossAmount,
        $amount = null
    ) {
        if ($invoiceNumber == '0') {
            $invoiceNumber = '';
        }

        $this->pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        $this->setFont('Helvetica-Bold', 8);
        $amountOffset = 5;

        if (! $this->showDueDate) {
            $this->pdf->showBoxed($date, 31, 546 - $this->lineOffset, 45, 13, 'left', '');
            $this->pdf->showBoxed($invoiceNumber, 76, 546 - $this->lineOffset, 75, 13, 'left', '');
            $this->pdf->showBoxed($description, 136, 546 - $this->lineOffset, 300, 13, 'left', '');
        } else {
            $this->pdf->showBoxed($date, 28, 546 - $this->lineOffset, 43, 13, 'left', '');
            $this->pdf->showBoxed($dueDate, 75, 546 - $this->lineOffset, 43, 13, 'left', '');
            $this->pdf->showBoxed($invoiceNumber, 121, 546 - $this->lineOffset, 58, 13, 'left', '');
            $this->pdf->showBoxed($description, 177, 546 - $this->lineOffset, 300, 13, 'left', '');
        }

        if (! $this->isResidential && ! $this->is_ledger || $this->is_ledger && $this->withTax) {
            $this->pdf->showBoxed($netAmount, 395 - $amountOffset, 546 - $this->lineOffset, 57, 13, 'right', '');
            $this->pdf->showBoxed($gstAmount, 455 - $amountOffset, 546 - $this->lineOffset, 57, 13, 'right', '');
        }

        $this->pdf->showBoxed($grossAmount, 515 - $amountOffset, 546 - $this->lineOffset, 57, 13, 'right', '');

        $this->updateLineOffset(10, $amount);
    }

    public function renderTotal($amount)
    {
        $this->pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        $this->setFont('Helvetica-Bold', 8);

        if (is_numeric($amount)) {
            $this->pdf->showBoxed(toMoney($amount), 25, 285, 540, 13, 'right', '');
        } else {
            $this->pdf->showBoxed($amount, 25, 285, 540, 13, 'right', '');
        }
    }

    public function renderRemittanceTotal($amount)
    {
        $this->pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        $this->totalAmount = $amount;
        $this->setFont('Helvetica-Bold', 9);

        $ownereft = dbGetEFTPropertyOwnerDetail($this->propertyID);
        if (isset($ownereft['accountNo'])) {
            $this->bpayBillerCode = false;
        }

        if (is_numeric($amount)) {
            $this->pdf->showBoxed(toMoney($amount), 380, 93 - ($this->bpayBillerCode ? 50 : 0), 185, 20, 'right', '');
        } else {
            $this->pdf->showBoxed(
                toMoney($amount, null),
                380,
                93 - ($this->bpayBillerCode ? 50 : 0),
                185,
                20,
                'right',
                ''
            );
        }

        $this->setFont('Helvetica', 8);
    }

    public function renderOCRLine($amount)
    {
        if ($this->deft) {
            $totalAmount = str_pad((floatval($amount) * 100), 9, '0', STR_PAD_LEFT);
            $this->setOption(11);
            if ($this->officeState == 'NSW' || $this->officeState == 'ACT') {
                $state = '<2+';
            } elseif ($this->officeState == 'VIC' || $this->officeState == 'SA' || $this->officeState == 'WA') {
                $state = '<3+';
            } elseif ($this->officeState == 'QLD') {
                $state = '<4+';
            }

            $this->setOption(11);
        }
    }

    public function renderTitle($title, $left = 546, $offset = 20)
    {
        $this->updateLineOffset(10);
        $this->pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        $this->setFont('Helvetica-Bold', 8);
        $this->pdf->showBoxed($title, 30, $left - $this->lineOffset, 400, 13, 'left', '');
        $this->updateLineOffset($offset);
    }

    public function renderSubTotal($title, $netAmount, $gstAmount, $grossAmount, $amount = null, $left = 140)
    {
        $this->pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        $this->pdf->setlinewidth(0.5);

        $amountOffset = 8;

        if (! $this->isResidential && ! $this->is_ledger || $this->is_ledger && $this->withTax) {
            if ($netAmount) {
                $this->pdf->moveto(395, 559 - $this->lineOffset);
                $this->pdf->lineto(447, 559 - $this->lineOffset);
                $this->pdf->stroke();
            }

            if ($gstAmount) {
                $this->pdf->moveto(455, 559 - $this->lineOffset);
                $this->pdf->lineto(507, 559 - $this->lineOffset);
                $this->pdf->stroke();
            }
        }

        if ($grossAmount) {
            $this->pdf->moveto(515, 559 - $this->lineOffset);
            $this->pdf->lineto(567, 559 - $this->lineOffset);
            $this->pdf->stroke();
        }

        $this->setFont('Helvetica-Bold', 8);
        if (! $this->isResidential && ! $this->is_ledger || $this->is_ledger && $this->withTax) {
            $this->pdf->showBoxed($netAmount, 395 - $amountOffset, 546 - $this->lineOffset, 60, 13, 'right', '');
            $this->pdf->showBoxed($gstAmount, 455 - $amountOffset, 546 - $this->lineOffset, 60, 13, 'right', '');
        }

        $this->pdf->showBoxed($grossAmount, 515 - $amountOffset, 546 - $this->lineOffset, 60, 13, 'right', '');

        if ($title) {
            $this->setFont('Helvetica-Bold', 8);
            $this->pdf->showBoxed($title, $left, 546 - $this->lineOffset, 300, 13, 'left', '');
            $this->setFont('Helvetica', 8);
        }

        $this->updateLineOffset(10, $amount);
    }

    public function renderPageNumber()
    {
        $this->setFont('Helvetica-Bold', 8);
        $this->pdf->showBoxed("{$this->pageCount}", $this->pageWidth - 50, 20, 25, 13, 'right', '');
    }

    public function renderTracc()
    {
        if (TRACC_LOGO) {
            $maxWidth = 20;
            $maxHeight = 15;
            [$imageWidth, $imageHeight] = getimagesize(TRACC_LOGO);

            $horizontalScaling = ($imageWidth > $maxWidth) ? $maxWidth / $imageWidth : 1;
            $verticalScaling = ($imageHeight > $maxHeight) ? $maxHeight / $imageHeight : 1;
            $imageScale = ($horizontalScaling < $verticalScaling) ? $horizontalScaling : $verticalScaling;


            $vMargin = 10;
            $hMargin = 25;

            if (! $this->resourceList['traccLogo']) {
                $this->resourceList['traccLogo'] = $this->pdf->load_image('auto', TRACC_LOGO, '');
            }

            $this->pdf->fit_image(
                $this->resourceList['traccLogo'],
                $hMargin,
                $vMargin + $maxHeight,
                "scale {$imageScale}"
            );
        }
    }

    public function renderLogo()
    {
        if (is_file($this->logoFile)) {
            $maxWidth = LOGO_WIDTH_INVOICE;
            $maxHeight = LOGO_HEIGHT_INVOICE;
            [$imageWidth, $imageHeight] = getimagesize($this->logoFile);

            $horizontalScaling = ($imageWidth > $maxWidth) ? $maxWidth / $imageWidth : 1;
            $verticalScaling = ($imageHeight > $maxHeight) ? $maxHeight / $imageHeight : 1;
            $imageScale = ($horizontalScaling < $verticalScaling) ? $horizontalScaling : $verticalScaling;

            $imageScale = round($imageScale, 2);

            $vMargin = 25;
            $hMargin = 25;

            $hPos = $this->pageWidth - ($imageScale * $imageWidth) - $hMargin;
            $vPos = $this->pageHeight - ($imageScale * $imageHeight) - $vMargin;

            if (! $this->resourceList['logo']) {
                $this->resourceList['logo'] = $this->pdf->load_image('auto', $this->logoFile, '');
            }

            $this->pdf->fit_image(
                $this->resourceList['logo'],
                $hPos,
                $vPos,
                'boxsize {' . "{$maxWidth} {$maxHeight}" . '} fitmethod meet'
            );
        }
    }

    public function preparePage($printTemplate = true)
    {
        $this->lineOffset = 15;
        parent::preparePage();
        $this->render();
        $this->renderLogo();
    }

    public function close()
    {
        parent::close();
    }
}

<?php

include_once SYSTEMPATH . '/commands/managementReports/functions/pdfHeaders.php';
global $pdf;


const RESIDENTIAL = 'RESI';

/**
 * Checks the tax status of a property lease.
 *
 * @param  string  $propertyID  The unique identifier of the property.
 * @param  string  $leaseID  The unique identifier of the lease associated with the property.
 * @return bool Returns true if the tax status qualifies as taxable, otherwise false.
 */
function dbCheckTaxStatus(string $propertyID, string $leaseID): bool
{
    $taxDetails = dbGetTaxDetails($propertyID, $leaseID);

    return ($taxDetails['gstCode'] == TAXFREE) || ($taxDetails['tenantType'] == RESIDENTIAL && $taxDetails['propertyType'] == RESIDENTIAL);
}
function fetchReadingData($readingID, $leaseID, $propertyID): array
{
    $readingData = dbGetUtilityReadingData($readingID, $leaseID, $propertyID);

    $withTax = dbCheckTaxStatus($propertyID, $leaseID);
    $tax = dbGetTaxCode(TAXABLE);
    $taxRate = $withTax ? 0 : $tax['taxRate'] / 100;
    $eGST = 0;
    $GST = 0;
    $totalGST = 0;
    $mainAmt = 0;
    $readingTitle = '';
    $readingUnitLabel = '';
    $readingMeterType = '';
    $arBatchNumber = '';
    if ($readingData && count($readingData) > 1) {
        $readingDateTo = $readingData[0]['readDate'];
        $readingTitle = $readingData[0]['readingTitle'];
        $readingUnitLabel = $readingData[0]['readingUnitLabel'];
        $readingMeterType = $readingData[0]['meterType'];
        $arBatchNumber = $readingData[0]['arBatchNumber'];
        [$day, $month, $year] = explode('/', $readingDateTo);

        foreach ($readingData as $row) {
            $eGST += $row['reading_charges_total'];
            $GST += round($row['reading_charges_total'] * $taxRate, 2);
            if ($row['reading_charges_type'] == 'MAIN') {
                $mainAmt += $row['reading_charges_total'];
            }
        }

        $totalGST = $eGST + $GST;
    }

    $no_days = $readingData[0]['reading_charges_days'];

    $readingValues = [
        'eGST' => $eGST,
        'GST' => $GST,
        'totalGST' => $totalGST,
        'mainAmt' => $mainAmt,
        'noDays' => $no_days,
    ];
    $date = date_create($year . '-' . $month . '-' . $day);
    date_sub($date, date_interval_create_from_date_string(($readingData[0]['reading_charges_days'] - 1) . ' days'));
    $readingDateFrom = date_format($date, 'd/m/Y');

    $readingInfo = [
        'readingDateFrom' => $readingDateFrom,
        'readingDateTo' => $readingDateTo,
        'readingTitle' => $readingTitle,
        'readingUnitLabel' => $readingUnitLabel,
        'readingMeterType' => $readingMeterType,
        'arBatchNumber' => $arBatchNumber,
    ];

    return [$readingData, $readingInfo, $readingValues];
}

function prepareUtilityReport(
    $readingID,
    $leaseID,
    $propertyID,
    $invoiceNumber,
    $dueDate,
    $toDate,
    $logo,
    $issueDate,
    &$parentInvoice
) {
    global $clientDirectory, $pathPrefix;

    // -- initialise the variables that will store the transaction amounts
    $total = 0;
    $totalQuantity = 0;

    $property = dbGetProperty($propertyID);
    $meterDetails = dbGetMeterDetails($readingID);
    $meterNumber = $meterDetails['meter_number'];
    $meterID = $meterDetails['meter_id'];
    [$readingData, $readingInfo, $readingValues] = fetchReadingData($readingID, $leaseID, $propertyID);
    $eGST = $readingValues['eGST'];
    $GST = $readingValues['GST'];
    $totalGST = $readingValues['totalGST'];
    $invoiceLineCount = 0;
    if ($totalGST != 0) {
        $headerData = stripslashes_deep(dbGetHeaderDetails($propertyID, $leaseID));
        $totalTitle = 'Tenancy Total';
        [$day, $month, $year] = explode('/', $dueDate);
        $logoPath = '';

        $time = time();
        $filename = 'utilityReport_' . "{$meterNumber}_{$propertyID}_{$leaseID}_{$time}.pdf";
        $filePath = "{$pathPrefix}{$clientDirectory}/pdf/UtilityReport/{$filename}";
        $downloadPath = "{$clientDirectory}/pdf/UtilityReport/{$filename}";

        $invoice = new Utility($filePath, $logoPath);
        logData(serialize($parentInvoice));

        if ($parentInvoice) {
            $masterInvoice = new Utility($parentInvoice, $logoPath);
        }

        $leaseAddress = stripslashes_deep(dbGetLeaseMailingAddress($propertyID, $leaseID));
        $leaseDetails = stripslashes_deep(dbGetLeaseDetails($propertyID, $leaseID));
        $portfolioManager = dbGetParam('PORTMGR', $property['propertyManager']);
        if ($portfolioManager) {
            $portfolioManager = toTitleCase($portfolioManager);
        }

        $headerTitle = $readingInfo['readingTitle'] . ' Statement';
        $clientName = dbGetClientName();
        $header = new UtilityHeader(
            $clientName,
            $propertyID,
            $leaseID,
            $dueDate,
            $headerTitle,
            $issueDate,
            $meterNumber,
            $readingInfo['readingDateFrom'],
            $readingInfo['readingDateTo'],
            $eGST,
            $GST,
            $totalGST
        );
        $header->bindAttributesFrom($headerData);
        $header->bindAttributesFrom($bankData);
        $header->bindAttributesFrom($property);
        $header->bindAttributesFrom($leaseAddress);
        $header->bindAttributesFrom($leaseDetails);
        $header->bindAttributesFrom($readingInfo);
        $header->portfolioManager = $portfolioManager;
        $header->readingID = $readingID;
        $invoice->attachObject('header', $header);

        $invoiceLines = new UtilityLines($totalTitle);
        $invoice->attachObject('statement', $invoiceLines);

        if ($parentInvoice) {
            $masterInvoice->objects = $invoice->objects;
        }

        $invoice->preparePage();
        if ($parentInvoice) {
            $masterInvoice->preparePage();
        }

        if ($readingData && count($readingData) > 0) {
            foreach ($readingData as $row) {
                $totalQuantity += ($row['reading_charges_type'] == 'MAIN' ? ($row['quantity'] * 1) : 0);
                $total += ($row['reading_charges_total'] * 1);
                $row['unit_label'] = ($row['reading_charges_type'] == 'MAIN' ? $readingInfo['readingUnitLabel'] : $row['unit_label']);
                $newAmountLines++;
                $invoice->renderReadingRow(
                    $row['reading_charges_description'],
                    $row['reading_previous'],
                    $readingInfo['readingUnitLabel'],
                    $row['reading_present'],
                    $row['reading_consumption'],
                    $row['quantity'],
                    $row['unit_label'],
                    ($row['reading_charges_type'] != 'MAIN' ? $row['reading_charges_amount'] : $row['reading_unit_charges']),
                    $row['reading_charges_total'],
                    $row['reading_charges_type'],
                    $readingInfo['readingMeterType']
                );
            }
        }

        $invoice->renderTotal($total, $totalQuantity, $readingValues, $readingInfo['readingUnitLabel']);

        $readingHistory = dbGetReadingHistory($meterID, $leaseID);
        $readingString = json_encode(array_reverse($readingHistory));
        $currencySymbol = cdf_displayLabel('currency_symbol');
        $units = $readingInfo['readingUnitLabel'];
        $stringData = "{$readingString}\n{$currencySymbol}\n{$units}";
        $barFilePathx = 'paretochart' . $_SESSION['userID'] . $propertyID . time();
        file_put_contents("amchart/data/{$barFilePathx}.txt", $stringData);
        exec(
            '"C:\Program Files\wkhtmltopdf\bin\wkhtmltoimage.exe" --javascript-delay 1000 --width 1000 --height 300 ' . SYSTEMURL . '/amchart/paretoDiagram.php?path=' . $barFilePathx . ' "amchart/data/' . $barFilePathx . '.jpg"'
        );
        $invoice->generateUtilityChartImage(
            'portrait',
            15,
            270,
            550,
            150,
            SYSTEMPATH . '/amchart/data/' . $barFilePathx . '.jpg'
        );

        $invoice->renderFooter($readingInfo['readingMeterType']);
        $invoice->close();


        if ($parentInvoice) {
            $masterInvoice->close();
        }


        $doc = [];
        $doc['documentTitle'] = $filename;
        $doc['documentType'] = DOC_INV_SUPPLIER;
        $doc['createdBy'] = $_SESSION['un'];
        $doc['primaryID'] = $propertyID;
        $doc['secondaryID'] = $leaseID;
        $doc['filename'] = $downloadPath;
        $doc['documentDescription'] = '';
        $doc['documentBatch'] = '';
        $doc['documentApTag'] = 0;
        $doc['attachAR'] = 1;
        $doc['attachOwnerR'] = 0;
        $documentID = dbAddDocument($doc);
        if ($documentID && ($readingData && count($readingData) > 0)) {
            foreach ($readingData as $row) {
                $invoiceDoc = [];
                $invoiceDoc['propertyID'] = $propertyID;
                $invoiceDoc['leaseID'] = $leaseID;
                $invoiceDoc['invoiceDate'] = $invoiceNumber ? $dueDate : null;
                $invoiceDoc['invoiceNo'] = $invoiceNumber;
                $invoiceDoc['linked'] = $invoiceNumber ? 1 : 0;
                $invoiceDoc['batchNumber'] = null;
                $invoiceDoc['lineNumber'] = null;
                $invoiceDoc['attachAR'] = 1;
                $invoiceDoc['attachOwnerR'] = 0;
                $invoiceDoc['documentID'] = $documentID;
                $invoiceDoc['documentApTag'] = 0;
                $invoiceDoc['arBatchNumber'] = $row['ar_batch_number'];
                $invoiceDoc['arBatchLineNumber'] = $row['ar_batch_line_number'];

                dbInsertLinkedInvoiceDocument($invoiceDoc);
            }
        }

        $data = [];
        $data['filePath'] = $filePath;
        $data['downloadPath'] = $downloadPath;
        $data['invoiceLineCount'] = $invoiceLineCount;

        return $data;
    }




}


class UtilityHeader extends PDFobject
{
    public $issueDate;

    public $meterNumber;

    public $leaseDescription;

    public $readingID;

    public $dueDate;

    public $client;

    public $propertyID;

    public $propertyName;

    public $propertyCity;

    public $propertyPostCode;

    public $propertyState;

    public $propertyAddress;

    public $propertyManager;

    public $leaseID;

    public $leaseName;

    public $ownerName;

    public $ownerABN;

    public $title;

    public $showOwnerDetail;

    public $showPropertyDetail;

    public $showDueDate;

    public $is_ledger;

    public $business_label;

    public $display_bsb;

    public $mailingName;

    public $mailingAddress;

    public $mailingCity;

    public $mailingState;

    public $mailingCountry;

    public $mailingPostCode;

    public $portfolioManager;

    public $readingTitle;

    public $readingFrom;

    public $readingTo;

    public $eGST;

    public $GST;

    public $totalGST;

    public $_name = 'utilityHeader';

    public $filePath;

    public function __construct(
        $client,
        $propertyID,
        $leaseID,
        $dueDate,
        $title,
        $issueDate,
        $meterNumber,
        $readingFrom,
        $readingTo,
        $eGST,
        $GST,
        $totalGST
    ) {
        $this->client = $client;
        $this->propertyID = $propertyID;
        $this->leaseID = $leaseID;
        $this->dueDate = $dueDate;
        $this->readingFrom = $readingFrom;
        $this->readingTo = $readingTo;
        if (! $issueDate) {
            $issueDate = TODAY;
        }

        $this->issueDate = $issueDate;
        $this->title = $title;
        $this->meterNumber = $meterNumber;
        $this->eGST = $eGST;
        $this->GST = $GST;
        $this->totalGST = $totalGST;
    }

    public function preRender(&$pdf)
    {
        // -- main box
        $pdf->setColorExt('both', 'rgb', 0.22, 0.59, 0.86, 1);
        $pdf->rect(25, 800, 545, 15);
        $pdf->fill();
        $pdf->setlinewidth(0.5);


        $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);

        $this->setFont($pdf, 'Helvetica-Bold', 9);

        $pdf->showBoxed('Statement for Meter Number', 32, 780, 545, 15, 'left', '');
        $pdf->showBoxed('Statement Date', 378, 780, 545, 15, 'left', '');

        $pdf->rect(25, 780, 545, .5);
        $pdf->fill();
        $pdf->setlinewidth(.5);

        $this->setFont($pdf, 'Helvetica', 8);
        $pdf->showBoxed('Bill to:', 35, 765, 545, 13, 'left', '');

        $this->setFont($pdf, 'Helvetica-Bold', 7);
        $pdf->showBoxed('This is not a Tax Invoice,', 400, 765, 100, 12, 'center', '');
        $pdf->showBoxed('support information only', 400, 755, 100, 12, 'center', '');

        $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        $this->setFont($pdf, 'Helvetica-Bold', 8);
        $pdf->showBoxed('Statement Summary:', 375, 682, 545, 13, 'left', '');


        $this->setFont($pdf, 'Helvetica', 8);
        $pdf->showBoxed('Property Name:', 35, 672, 545, 13, 'left', '');
        $pdf->showBoxed('Property Address:', 35, 662, 545, 13, 'left', '');
        $pdf->showBoxed('Property Manager:', 35, 652, 545, 13, 'left', '');
        $pdf->showBoxed('Meter Type:', 35, 642, 545, 13, 'left', '');

        $this->setFont($pdf, 'Helvetica-Bold', 8);
        $pdf->showBoxed('Exc ' . cdf_displayLabel('tax_label'), 375, 662, 70, 13, 'right', '');
        $pdf->showBoxed(cdf_displayLabel('tax_label'), 375, 652, 70, 13, 'right', '');

        $pdf->setColorExt('both', 'rgb', 0.22, 0.59, 0.86, 1);
        $pdf->rect(370.5, 635, 100, 15);
        $pdf->fill();
        $pdf->setlinewidth(0.5);

        $pdf->setColorExt('both', 'rgb', 0.98, 0.55, 0, 1);
        $pdf->rect(470.5, 635, 98.5, 15);
        $pdf->fill();
        $pdf->setlinewidth(0.5);


        $pdf->setColorExt('both', 'rgb', 1, 1, 1, 0);
        $pdf->showBoxed('Total Incl. ' . cdf_displayLabel('tax_label'), 375, 635, 70, 13, 'right', '');
        // end
    }

    public function render(&$pdf)
    {
        $pdf->setColorExt('both', 'rgb', 1, 1, 1, 0);
        $this->setFont($pdf, 'Helvetica-Bold', 10);
        $pdf->showBoxed($this->client, 30, 800, 545, 14, 'left', '');
        $pdf->showBoxed($this->title, 378, 800, 545, 14, 'left', '');

        $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        $this->setFont($pdf, 'Helvetica-Bold', 9);
        $pdf->showBoxed($this->meterNumber, 163, 781, 545, 15, 'left', '');
        $pdf->showBoxed($this->issueDate, 448, 781, 545, 15, 'left', '');

        $this->setFont($pdf, 'Helvetica-Bold', 10);
        $pdf->showBoxed($this->mailingName, 35, 752, 545, 14, 'left', '');

        $this->setFont($pdf, 'Helvetica-Bold', 8);
        $pdf->showBoxed($this->leaseDescription, 35, 740, 545, 14, 'left', '');
        $pdf->showBoxed($this->mailingAddress, 35, 730, 545, 14, 'left', '');
        $pdf->showBoxed(
            $this->mailingCity . ' ' . $this->mailingState . ' ' . $this->mailingPostCode,
            35,
            720,
            545,
            14,
            'left',
            ''
        );

        $this->setFont($pdf, 'Helvetica', 8);
        $pdf->showBoxed($this->propertyName, 110, 673, 545, 13, 'left', '');
        $pdf->showBoxed(
            $this->propertyAddress . ' ' . $this->propertyCity . ' ' . $this->propertyState . ' ' . $this->propertyPostCode,
            110,
            663,
            545,
            13,
            'left',
            ''
        );
        $pdf->showBoxed($this->portfolioManager, 110, 653, 545, 13, 'left', '');
        $pdf->showBoxed($this->readingTitle, 110, 643, 545, 13, 'left', '');

        $pdf->showBoxed('For Report Period : ', 376, 673, 200, 13, 'left', '');
        $pdf->showBoxed($this->readingFrom . ' to ' . $this->readingTo, 376, 673, 180, 13, 'right', '');

        $this->setFont($pdf, 'Helvetica-Bold', 8);
        $pdf->showBoxed(toMoney($this->eGST, cdf_displayLabel('currency_symbol')), 425, 663, 130, 13, 'right', '');
        $pdf->showBoxed(toMoney($this->GST, cdf_displayLabel('currency_symbol')), 425, 653, 130, 13, 'right', '');
        $pdf->setColorExt('both', 'rgb', 1, 1, 1, 0);
        $pdf->showBoxed(toMoney($this->totalGST, cdf_displayLabel('currency_symbol')), 425, 636, 130, 13, 'right', '');
    }
}

class UtilityLines extends PDFobject
{
    public $title;

    public $isResidential;

    public $showDueDate;

    public $is_ledger;

    public $withTax;

    public $currencySymbol;

    public function __construct($title)
    {
        $this->title = $title;
    }

    public function preRender(&$pdf)
    {
        global $sess;
        $this->currencySymbol = $sess->get('currencySymbol');
        $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        $pdf->showBoxed('Calculation Details:', 25, 605, 545, 13, 'left', '');
        $pdf->setColorExt('fill', 'rgb', 0.22, 0.59, 0.86, 1);
        $pdf->rect(24, 590, 545, 15);
        $pdf->fill();
        $pdf->setColorExt('fill', 'rgb', 1, 1, 1, 0);
        $pdf->rect(24, 500, 360, 15);
        $pdf->fill();
        $pdf->setlinewidth(0.5);

        $pdf->setColorExt('both', 'rgb', 0.75, 0.75, 0.75, 0);
        $pdf->moveto(24, 590);
        $pdf->lineto(569, 590);
        $pdf->stroke();
        $pdf->moveto(24, 515);
        $pdf->lineto(569, 515);
        $pdf->stroke();

        $pdf->setColorExt('both', 'rgb', 1, 1, 1, 0);
        $this->setFont($pdf, 'Helvetica-Bold', 7);
        $pdf->showBoxed('Item', 30, 590, 135, 13, 'left', '');
        $pdf->showBoxed('Prev Rdg', 170, 590, 45, 13, 'right', '');
        $pdf->showBoxed('Latest Rdg', 255, 590, 45, 13, 'right', '');
        $pdf->showBoxed('Advance', 325, 590, 45, 13, 'right', '');
        $pdf->showBoxed('Quantity', 395, 590, 45, 13, 'right', '');
        $pdf->showBoxed('Rate', 455, 590, 50, 13, 'left', '');
        $pdf->showBoxed('Total', 505, 590, 55, 13, 'right', '');
    }

    public function render(&$pdf)
    {
        $this->setFont($pdf, 'Helvetica-Bold', 10);
        $pdf->setColorExt('both', 'rgb', 0.22, 0.59, 0.86, 1);
        $pdf->showBoxed($this->title, 30, 500, 285, 15, 'left', '');
        $this->setFont($pdf, 'Helvetica', 8);
    }
}

class Utility extends PDFReport
{
    public $pdf;

    public $pageCount;

    public $resourceList;

    public $lineOffset = 15;

    public $logoFile = false;

    public $isResidential = false;

    public $bpayBillerCode;

    public $deft;

    public $payway;

    public $paywayBillerCode;

    public $creditCard;

    public $linkProvider;

    public $officeState;

    public $showDueDate;

    public $is_ledger;

    public $withTax;

    public $propertyID;

    public function __construct(&$dataSource, $logoFile = false)
    {
        parent::__construct($dataSource);
        if ($logoFile) {
            $this->logoFile = $logoFile;
        }

        $this->pageWidth = 595;
        $this->pageHeight = 842;
    }

    public function updateLineOffset($offset, $amount = null)
    {
        $this->lineOffset += $offset;
    }

    public function renderReadingRow(
        $item,
        $prevReading,
        $unitLabel,
        $latestReading,
        $advance,
        $quantity,
        $quantityUnit,
        $rate,
        $total,
        $chargeType,
        $meterType
    ) {
        $this->pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        $this->setFont('Helvetica', 7);

        if ($chargeType != 'MAIN') {
            $this->pdf->showBoxed($item, 35, 590 - $this->lineOffset, 150, 13, 'left', '');
        } else {
            $this->pdf->showBoxed($item, 35, 590 - $this->lineOffset, 150, 13, 'left', '');
            $this->pdf->showBoxed(
                number_format($prevReading, 0, '', ','),
                155,
                590 - $this->lineOffset,
                45,
                13,
                'right',
                ''
            );
            $this->pdf->showBoxed($unitLabel, 190, 590 - $this->lineOffset, 30, 13, 'right', '');
            $this->pdf->showBoxed(
                number_format($latestReading, 0, '', ','),
                235,
                590 - $this->lineOffset,
                45,
                13,
                'right',
                ''
            );
            $this->pdf->showBoxed($unitLabel, 270, 590 - $this->lineOffset, 30, 13, 'right', '');
            $this->pdf->showBoxed(
                number_format($advance, 0, '', ','),
                310,
                590 - $this->lineOffset,
                45,
                13,
                'right',
                ''
            );
            $this->pdf->showBoxed($unitLabel, 345, 590 - $this->lineOffset, 30, 13, 'right', '');
        }

        $this->pdf->showBoxed(number_format($quantity, 0, '', ','), 370, 590 - $this->lineOffset, 55, 13, 'right', '');
        $this->pdf->showBoxed($quantityUnit, 430, 590 - $this->lineOffset, 30, 13, 'left', '');
        $this->pdf->showBoxed(
            toMoney($rate, cdf_displayLabel('currency_symbol'), 4),
            455,
            590 - $this->lineOffset,
            55,
            13,
            'left',
            ''
        );
        $this->pdf->showBoxed(
            toMoney($total, cdf_displayLabel('currency_symbol'), 2),
            500,
            590 - $this->lineOffset,
            60,
            13,
            'right',
            ''
        );

        $this->updateLineOffset(10, 0);
    }

    public function renderTotal($total, $quantity, $readingData, $unitLabel)
    {
        $this->pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        $this->setFont('Helvetica-Bold', 8);
        $this->pdf->showBoxed(number_format($quantity, 0, '', ','), 25, 495, 410, 15, 'right', '');
        $this->pdf->showBoxed($unitLabel, 425, 495, 30, 15, 'right', '');
        $this->pdf->showBoxed(toMoney($total, cdf_displayLabel('currency_symbol')), 455, 495, 100, 15, 'right', '');

        $this->setFont('Helvetica', 8);
        $this->pdf->showBoxed('Average ' . $unitLabel . ' Per Day:', 35, 480, 200, 15, 'left', '');
        $this->pdf->showBoxed(
            'Average ' . cdf_displayLabel('currency_symbol') . ' Per Day:',
            35,
            470,
            200,
            15,
            'left',
            ''
        );
        $averageUnitPerDay = round(($quantity / $readingData['noDays']), 2);
        $averageAmountPerDay = round(($readingData['mainAmt'] / $readingData['noDays']), 2);

        $this->pdf->showBoxed(toDecimal($averageUnitPerDay, 2), 115, 480, 50, 15, 'right', '');
        $this->pdf->showBoxed(toDecimal($averageAmountPerDay, 2), 115, 470, 50, 15, 'right', '');

        $this->pdf->setColorExt('fill', 'rgb', 0.22, 0.59, 0.86, 1);
        $this->pdf->rect(240, 450, 100, 50);
        $this->pdf->fill();
        $this->pdf->setColorExt('fill', 'rgb', 0.98, 0.55, 0, 1);
        $this->pdf->rect(340, 450, 230, 50);
        $this->pdf->fill();

        $this->pdf->setColorExt('both', 'rgb', 1, 1, 1, 0);
        $this->setFont('Helvetica-Bold', 9);
        $this->pdf->showBoxed('Totals', 255, 480, 100, 15, 'left', '');
        $this->pdf->showBoxed(cdf_displayLabel('tax_label'), 255, 465, 100, 15, 'left', '');
        $this->pdf->showBoxed('Total Incl. ' . cdf_displayLabel('tax_label'), 255, 450, 100, 15, 'left', '');

        $this->pdf->setColorExt('both', 'rgb', 1, 1, 1, 0);
        $this->pdf->showBoxed(number_format($quantity, 0, '', ','), 25, 480, 410, 15, 'right', '');
        $this->pdf->showBoxed($unitLabel, 425, 480, 30, 15, 'right', '');

        $this->pdf->showBoxed(
            toMoney($readingData['eGST'], cdf_displayLabel('currency_symbol')),
            455,
            480,
            100,
            15,
            'right',
            ''
        );
        $this->pdf->showBoxed(
            toMoney($readingData['GST'], cdf_displayLabel('currency_symbol')),
            455,
            465,
            100,
            15,
            'right',
            ''
        );
        $this->pdf->showBoxed(
            toMoney($readingData['totalGST'], cdf_displayLabel('currency_symbol')),
            455,
            450,
            100,
            15,
            'right',
            ''
        );
    }

    public function renderTitle($title, $left = 546, $offset = 20)
    {
        $this->updateLineOffset(10);
        $this->pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        $this->setFont('Helvetica-Bold', 8);
        $this->pdf->showBoxed($title, 30, $left - $this->lineOffset, 400, 13, 'left', '');
        $this->updateLineOffset($offset);
    }

    public function renderFooter($meterType)
    {
        $this->pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        $this->setFont('Helvetica-Bold', 10);
        $this->pdf->showBoxed('Account History', 10, 420, 545, 15, 'center', '');
    }

    public function renderPageNumber()
    {
        $this->setFont('Helvetica-Bold', 8);
        $this->pdf->showBoxed("{$this->pageCount}", $this->pageWidth - 50, 20, 25, 13, 'right', '');
    }

    public function renderLogo()
    {
        if (is_file($this->logoFile)) {
            $maxWidth = LOGO_WIDTH_INVOICE;
            $maxHeight = LOGO_HEIGHT_INVOICE;
            [$imageWidth, $imageHeight] = getimagesize($this->logoFile);

            $horizontalScaling = ($imageWidth > $maxWidth) ? $maxWidth / $imageWidth : 1;
            $verticalScaling = ($imageHeight > $maxHeight) ? $maxHeight / $imageHeight : 1;
            $imageScale = ($horizontalScaling < $verticalScaling) ? $horizontalScaling : $verticalScaling;

            $imageScale = round($imageScale, 2);

            $vMargin = 25;
            $hMargin = 25;

            $hPos = $this->pageWidth - ($imageScale * $imageWidth) - $hMargin;
            $vPos = $this->pageHeight - ($imageScale * $imageHeight) - $vMargin;

            if (! $this->resourceList['logo']) {
                $this->resourceList['logo'] = $this->pdf->load_image('auto', $this->logoFile, '');
            }

            $this->pdf->fit_image(
                $this->resourceList['logo'],
                $hPos,
                $vPos,
                'boxsize {' . "{$maxWidth} {$maxHeight}" . '} fitmethod meet'
            );
        }
    }

    public function preparePage($printTemplate = true)
    {
        $this->lineOffset = 15;
        parent::preparePage();
        $this->render();
        $this->renderLogo();
    }

    public function close()
    {
        parent::close();
    }

    public function generateUtilityChartImage($page, $x, $y, $maxWidth, $maxHeight, $file)
    {
        global $pdf;
        if (! file_exists($file)) {
            return;
        }

        $yoffset = 0;
        $pdfimage = $this->pdf->load_image('auto', $file, '');
        $this->pdf->fit_image(
            $pdfimage,
            $x,
            $y -= $yoffset,
            'boxsize {' . "{$maxWidth} {$maxHeight}" . '} position={center} fitmethod=meet'
        );
    }
}

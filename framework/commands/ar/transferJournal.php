<?php

include __DIR__ . '/invoicePDF.php';


/**
 * Validates the transfer details in the provided view object.
 *
 * @param  object  $view  An object containing transfer details, including properties and leases.
 * @return bool Returns true if the transfer conditions are met, otherwise false.
 */
function validateProperties(object $view): bool
{
    return  (
        ($view->items['toPropertyID'] != $view->items['fromPropertyID']) ||
        ($view->items['toLeaseID'] != $view->items['fromLeaseID'])
    )
        && $view->items['toPropertyID']
        && $view->items['transactionDate'];
}

function transferJournal(&$context)
{
    if (! isPostback()) {
        $view = new MasterPage(userViews(), '/ar/transferJournal.html');
        $view->setSection($context['module']);
    } else {
        $view = new UserControl(userViews(), '/ar/transferJournal.html');
    }

    $view->bindAttributesFrom($_REQUEST);
    if (isset($_GET['transactionAmount'])) {
        $view->bindAttributesFrom($_GET);
    }

    // -- set defaults
    $view->items['propertyList'] = dbPropertyList();

    $view->items['fromPropertyIsLedger'] = dbGetPropertyIsLedger($view->items['fromPropertyID']);
    $view->items['toPropertyIsLedger'] = dbGetPropertyIsLedger($view->items['toPropertyID']);

    $view->items['fromLeaseList'] = dbLeaseList($view->items['fromPropertyID'], false, false, true);
    $view->items['toLeaseList'] = dbLeaseList($view->items['toPropertyID'], false, false, true);

    if ($view->items['fromPropertyIsLedger']) {
        $view->items['fromLeaseList'] = dbLeaseList($view->items['fromPropertyID']);
    }

    if ($view->items['toPropertyIsLedger']) {
        $view->items['toLeaseList'] = dbLeaseList($view->items['toPropertyID']);
    }


    if ($view->items['fromPropertyID'] == '') {
        $view->items['fromLeaseID'] = '';
    }

    if ($view->items['toPropertyID'] == '') {
        $view->items['toLeaseID'] = '';
    }

    // -- load the existing transfer journal from temp
    if ($view->items['action'] == 'view') {
        $objectAttributes = dbGetTempTransferJournal($view->items['transferJournalID']);
        $view->bindAttributesFrom($objectAttributes);
        $fromPropertyTax = dbGetTaxStatus($view->items['fromPropertyID']); // -- grabs the tax status of the owner
        $view->items['fromPropertyTaxID'] = $fromPropertyTax['taxCode'];
        $toPropertyTax = dbGetTaxStatus($view->items['toPropertyID']); // -- grabs the tax status of the owner
        $view->items['toPropertyTaxID'] = $toPropertyTax['taxCode'];

        $view->items['toPropertyBalance'] = balanceAsAt(
            $view->items['toPropertyID'],
            $view->items['transactionDate'],
            null,
            dbGetPropertyFundIDs($view->items['toPropertyID'])
        );
        $view->items['fromPropertyBalance'] = balanceAsAt(
            $view->items['fromPropertyID'],
            $view->items['transactionDate'],
            null,
            dbGetPropertyFundIDs($view->items['fromPropertyID'])
        );
    }

    $view->items['taxRateList'] = dbGetTaxRates();
    $taxRates = mapParameters($view->items['taxRateList'], 'taxCode', 'taxRate');
    $fromTax = dbGetCompanyTaxStatus($view->items['fromDebtorID']);
    $toTax = dbGetCompanyTaxStatus($view->items['toDebtorID']);

    if ((! isset($view->items['fromTaxRateID'])) || ($view->items['action'] == 'changePrimary')) {
        $fromPropertyTax = dbGetTaxStatus($view->items['fromPropertyID']); // -- grabs the tax status of the owner
        $view->items['fromPropertyTaxID'] = $fromPropertyTax['taxCode'];
        $view->items['fromTaxRateID'] = ($fromPropertyTax['taxRate'] == 0) ? $fromPropertyTax['taxCode'] : $fromTax['taxCode'];
    }

    if ((! isset($view->items['toTaxRateID'])) || ($view->items['action'] == 'changePrimary')) {
        $toPropertyTax = dbGetTaxStatus($view->items['toPropertyID']); // -- grabs the tax status of the owner
        $view->items['toPropertyTaxID'] = $toPropertyTax['taxCode'];
        $view->items['toTaxRateID'] = ($toPropertyTax['taxRate'] == 0) ? $toPropertyTax['taxCode'] : $toTax['taxCode'];
    }

    if ($view->items['transactionAmount']) {
        $view->items['transactionAmount'] = round($view->items['transactionAmount'], 2);
    }

    $fromTaxRate = $taxRates[$view->items['fromTaxRateID']];
    $toTaxRate = $taxRates[$view->items['toTaxRateID']];
    $view->items['fromTaxAmount'] = round(
        ($fromTaxRate / bcadd(100, $fromTaxRate, 2)) * $view->items['transactionAmount'],
        2
    );
    $view->items['fromNetAmount'] = bcsub($view->items['transactionAmount'], $view->items['fromTaxAmount'], 2);
    $view->items['toTaxAmount'] = round(
        ($toTaxRate / bcadd(100, $toTaxRate, 2)) * $view->items['transactionAmount'],
        2
    );
    $view->items['toNetAmount'] = bcsub($view->items['transactionAmount'], $view->items['toTaxAmount'], 2);

    $fromFundID = dbGetPropertyFundIDFromAccount($view->items['fromPropertyID'], $view->items['fromAccountID']);
    $toFundID = dbGetPropertyFundIDFromAccount($view->items['toPropertyID'], $view->items['toAccountID']);

    // check bank details
    $fromBankDetails = dbGetBankDetails($view->items['fromPropertyID']);
    $toBankDetails = dbGetBankDetails($view->items['toPropertyID']);

    $view->items['fromBankID'] = $fromBankDetails['bankID'];
    $view->items['toBankID'] = $toBankDetails['bankID'];


    if (! $fromFundID) {
        $view->items['fromPropertyBalanceLabel'] = 'Property Balance';
        if (! $view->items['fromPropertyIsLedger']) {
            $view->items['fromPropertyBalance'] = balanceAsAt(
                $view->items['fromPropertyID'],
                $view->items['transactionDate'],
                null,
                dbGetPropertyFundIDs($view->items['fromPropertyID'])
            );
        } elseif ($view->items['fromPropertyIsLedger'] && $view->items['fromLeaseID']) {
            $view->items['fromPropertyBalance'] = leaseBalanceAsAt(
                $view->items['fromPropertyID'],
                $view->items['fromLeaseID'],
                $view->items['transactionDate']
            );
        } else {
            $view->items['fromPropertyBalance'] = 0;
        }
    } else {
        $view->items['fromPropertyBalanceLabel'] = 'Property Fund Balance';
        $view->items['fromPropertyBalance'] = balanceAsAt(
            $view->items['fromPropertyID'],
            $view->items['transactionDate'],
            null,
            [],
            [$fromFundID]
        );
    }

    if (! $toFundID) {
        $view->items['toPropertyBalanceLabel'] = 'Property Balance';
        if (! $view->items['toPropertyIsLedger']) {
            $view->items['toPropertyBalance'] = balanceAsAt(
                $view->items['toPropertyID'],
                $view->items['transactionDate'],
                null,
                dbGetPropertyFundIDs($view->items['toPropertyID'])
            );
        } elseif ($view->items['toPropertyIsLedger']) {
            $view->items['toPropertyBalance'] = leaseBalanceAsAt(
                $view->items['toPropertyID'],
                $view->items['toLeaseID'],
                $view->items['transactionDate']
            );
        } else {
            $view->items['toPropertyBalance'] = 0;
        }
    } else {
        $view->items['toPropertyBalanceLabel'] = 'Property Fund Balance';
        $view->items['toPropertyBalance'] = balanceAsAt(
            $view->items['toPropertyID'],
            $view->items['transactionDate'],
            null,
            [],
            [$toFundID]
        );
    }

    if (in_array($view->items['action'], ['changePrimary', 'tax', 'process']) && ($fromBankDetails['bankID'] != $toBankDetails['bankID'] && $view->items['fromPropertyID'] != '' && $view->items['toPropertyID'] != '')) {
        $validationErrors[] = 'Debit and Credit Properties should have the same bank code';
    }

    if ((! $fromFundID && $view->items['transactionAmount'] > $view->items['fromPropertyBalance']) && ! $fromBankDetails['debitBarred']) {
        $validationWarnings = 'Processing this will cause a negative balance on the from property.';
    }

    switch ($view->items['action']) {
        case 'process':
            $fromBankDetails = dbGetBankDetails($view->items['fromPropertyID']);
            $toBankDetails = dbGetBankDetails($view->items['toPropertyID']);

            if (! isValid($view->items['fromLeaseID'], TEXT_KEY, false)) {
                $validationErrors[] = 'You need to select a debit lease';
            }

            if (! isValid($view->items['toLeaseID'], TEXT_KEY, false)) {
                $validationErrors[] = 'You need to select a credit lease';
            }

            if (! isValid($view->items['fromPropertyID'], TEXT_KEY, false)) {
                $validationErrors[] = 'You need to enter a debit property';
            } else {
                $bankDetails = dbGetBankDetails($view->items['fromPropertyID']);
                if (validateProperties($view)) {
                    if (! $view->items['fromPropertyIsLedger'] && ! $fromFundID) {
                        $balance = balanceAsAt($view->items['fromPropertyID'], $view->items['transactionDate']);
                    } elseif (! $view->items['fromPropertyIsLedger'] && $fromFundID) {
                        $balance = balanceAsAt(
                            $view->items['fromPropertyID'],
                            $view->items['transactionDate'],
                            null,
                            [],
                            [$fromFundID]
                        );
                    } elseif ($view->items['fromPropertyIsLedger']) {
                        $balance = leaseBalanceAsAt(
                            $view->items['fromPropertyID'],
                            $view->items['fromLeaseID'],
                            $view->items['transactionDate']
                        );
                    }

                    if (($bankDetails['debitBarred']) && (isAdmin($context)) && (bccomp(
                        $view->items['transactionAmount'],
                        $balance,
                        2
                    ) === 1)) {
                        $validationErrors[] = 'You do not have enough money in the property ' . $view->items['fromPropertyID'] .
                            ($view->items['fromPropertyIsLedger'] ? ' sub-ledger ' . $view->items['fromLeaseID'] : '') .
                            ' to transfer - current balance ' . toMoney($balance);
                    }

                    $negativeBalanceDate = dbGetNegativeFutureBalance(
                        $view->items['fromPropertyID'],
                        $view->items['transactionDate'],
                        $view->items['transactionAmount']
                    );
                    if ($bankDetails['debitBarred'] && $negativeBalanceDate['allocDate'] != '') {
                        $validationErrors[] = 'Processing is not possible because it would overdraw ' . $view->items['fromPropertyID'] . ' as of ' . $negativeBalanceDate['allocDate'] . ' by ' . toMoney(
                            $negativeBalanceDate['dayBalance'] * -1
                        );
                    }
                }
            }

            if (! isValid($view->items['toPropertyID'], TEXT_KEY, false)) {
                $validationErrors[] = 'You need to enter a credit property';
            }

            if (! isValid($view->items['fromAccountID'], TEXT_KEY, false)) {
                $validationErrors[] = 'You need to enter a debit account';
            }

            if (! isValid($view->items['toAccountID'], TEXT_KEY, false)) {
                $validationErrors[] = 'You need to enter a credit account';
            }

            if (! isValid($view->items['fromTaxRateID'], TEXT_KEY, false)) {
                $validationErrors[] = 'You need to enter a tax rate (debit)';
            }

            if (! isValid($view->items['toTaxRateID'], TEXT_KEY, false)) {
                $validationErrors[] = 'You need to enter a tax rate (credit)';
            }

            if (! isValid($view->items['fromDescription'], TEXT_HTML, false)) {
                $validationErrors[] = 'You need to enter a debit description';
            }

            if (! isValid($view->items['toDescription'], TEXT_HTML, false)) {
                $validationErrors[] = 'You need to enter a credit description';
            }

            if (! isValid($view->items['fromFromDate'], TEXT_SMARTDATE, false)) {
                $validationErrors[] = 'You need to enter a from date  (debit)';
            }

            if (! isValid($view->items['toFromDate'], TEXT_SMARTDATE, false)) {
                $validationErrors[] = 'You need to enter a from date (credit)';
            }

            if (toDateStamp($view->items['fromFromDate']) > toDateStamp($view->items['fromToDate'])) {
                $validationErrors[] = 'Your from date cannot be before your to date';
            }

            if (toDateStamp($view->items['toFromDate']) > toDateStamp($view->items['toToDate'])) {
                $validationErrors[] = 'Your from date cannot be before your to date';
            }

            if (! isValid($view->items['fromToDate'], TEXT_SMARTDATE, false)) {
                $validationErrors[] = 'You need to enter a to date (debit)';
            }

            if (! isValid($view->items['toToDate'], TEXT_SMARTDATE, false)) {
                $validationErrors[] = 'You need to enter a to date (credit)';
            }

            $fromPeriod = dbGetPeriod($view->items['fromPropertyID'], $view->items['transactionDate']);
            if (! $fromPeriod || $fromPeriod['closed']) {
                $validationErrors[] = 'The period is closed for the transaction date chosen on the property ' . $view->items['fromPropertyID'];
            }

            $toPeriod = dbGetPeriod($view->items['toPropertyID'], $view->items['transactionDate']);
            if (! $toPeriod || $toPeriod['closed']) {
                $validationErrors[] = 'The period is closed for the transaction date chosen on the property ' . $view->items['toPropertyID'];
            }

            if (bccomp($view->items['transactionAmount'], 0, 2) !== 1) {
                $validationErrors[] = 'You cannot transfer a zero or negative amount';
            }


            /* check both from and to properties */
            $lastReconciled = dbGetLastReconciledDateForProperty($view->items['fromPropertyID']);
            if (toTimeStamp($view->items['transactionDate']) < toTimeStamp($lastReconciled)) {
                $validationErrors['transferJournal'] = 'You cannot post a transfer prior to the last bank reconciliation date (' . $lastReconciled . ')';
            }

            // -- TO
            $lastReconciled = dbGetLastReconciledDateForProperty($view->items['toPropertyID']);

            if (toTimeStamp($view->items['transactionDate']) < toTimeStamp($lastReconciled)) {
                $validationErrors['transferJournal'] = 'You cannot post a transfer prior to the last bank reconciliation date (' . $lastReconciled . ')';
            }

            // check if overdrawn
            if ($view->items['fromPropertyID'] != $view->items['toPropertyID']) {
                if ($fromBankDetails['debitBarred'] && ! $fromFundID && $view->items['transactionAmount'] > $view->items['fromPropertyBalance']) { // debit barred and !fundID
                    $validationErrors[] = 'The bank account setting does not allow funds to be overdrawn from the property : ' . $view->items['fromPropertyID'];
                }

                if ($fromFundID && $view->items['transactionAmount'] > $view->items['fromPropertyBalance']) { // debit barred and fundID
                    $validationErrors[] = 'Debit property fund should not be overdrawn';
                }
            }

            // ------------------------

            if (noErrors($validationErrors)) {
                $fromDebtorDetails = dbGetLeaseDetails($view->items['fromPropertyID'], $view->items['fromLeaseID']);
                $toDebtorDetails = dbGetLeaseDetails($view->items['toPropertyID'], $view->items['toLeaseID']);
                $view->items['fromDebtorID'] = $fromDebtorDetails['debtorID'];
                $view->items['toDebtorID'] = $toDebtorDetails['debtorID'];

                if (isAdmin($context)) {
                    // FROM
                    // insert into AR trans  (A) INV + // change to CRE then negative amount
                    // insert into AR trans (B) CSH +
                    // get next allocation number
                    // insert into AR alloc (B->A)


                    $batchJournal = dbGetNextJournalBatch('AR');
                    $toBatchNumber = dbGetNextReceivableBatchNumber();
                    $transaction = [];
                    $transaction['batchJournal'] = $batchJournal;
                    $transaction['batchNumber'] = $toBatchNumber;
                    $transaction['lineNumber'] = 1;

                    $transaction['transactionType'] = $view->items['asUnallocated'] ? TYPE_INVOICE : TYPE_CREDIT;


                    $transaction['year'] = $fromPeriod['year'];
                    $transaction['period'] = $fromPeriod['period'];
                    $transaction['transactionYear'] = $fromPeriod['year'];
                    $transaction['transactionPeriod'] = $fromPeriod['period'];
                    $bankDetails = dbGetBankDetails($view->items['fromPropertyID']);
                    $transaction['bankID'] = $bankDetails['bankID'];

                    $transaction['propertyID'] = $view->items['fromPropertyID'];
                    $transaction['leaseID'] = $view->items['fromLeaseID'];
                    $transaction['accountID'] = $view->items['fromAccountID'];
                    $transaction['leaseID'] = $view->items['fromLeaseID'];
                    $transaction['transactionDate'] = $view->items['transactionDate'];
                    $transaction['createUser'] = $_SESSION['un'];
                    $transaction['createDate'] = TODAY;
                    $transaction['debtorID'] = $view->items['fromDebtorID'];
                    $transaction['description'] = $view->items['fromDescription'];

                    $transactionAmount = bcmul($view->items['transactionAmount'], -1, 2);
                    $netAmount = bcmul($view->items['fromNetAmount'], -1, 2);
                    $taxAmount = bcmul($view->items['fromTaxAmount'], -1, 2);

                    if ($view->items['asUnallocated']) {
                        $transaction['transactionAmount'] = 0;
                        $transaction['netAmount'] = 0;
                        $transaction['taxAmount'] = 0;
                    } else {
                        $transaction['transactionAmount'] = $transactionAmount;
                        $transaction['netAmount'] = $netAmount;
                        $transaction['taxAmount'] = $taxAmount;
                    }

                    $transaction['taxCode'] = $view->items['fromTaxRateID'];
                    $transaction['bpayReference'] = '';
                    $transaction['fromDate'] = $view->items['fromFromDate'];
                    $transaction['toDate'] = $view->items['fromToDate'];
                    $transaction['orderNumber'] = null;
                    $transaction['invoiceNumber'] = null;
                    $transaction['dueDate'] = null;
                    $transaction['isJournal'] = 1;

                    dbInsertReceivableTransaction($transaction); // inserts FROM property CREDIT

                    if (GL_ACTIVE) { // GL for CREDIT - FROM
                        $gl = new GeneralLedger();
                        $gl->transactionType = $transaction['transactionType'];
                        $gl->transactionDate = $view->items['transactionDate'];
                        $gl->description = $transaction['description'];
                        $gl->year = $transaction['year'];
                        $gl->period = $transaction['period'];
                        $gl->propertyID = $transaction['propertyID'];
                        $gl->leaseID = $transaction['leaseID'];
                        $gl->companyID = $transaction['debtorID'];
                        $gl->source = GL_SOURCE_AR;

                        $gl->fromDate = $transaction['fromDate'];
                        $gl->toDate = $transaction['toDate'];
                        $gl->batchID = $transaction['batchNumber'];
                        $gl->lineNumber = $transaction['lineNumber'];

                        $t = new Transaction($gl);    // -- detail entry : adopt the data from the

                        $_amount = bcmul($netAmount, -1, 2);
                        if ($view->items['asUnallocated']) {
                            $_amount = 0;
                        }

                        $gl->update($transaction['accountID'], GL_BALANCE_ACCRUALS, $_amount);
                        $t->add($transaction['accountID'], BASIS_ACCRUALS, $_amount);

                        $_amount = bcmul($taxAmount, -1, 2);
                        if ($view->items['asUnallocated']) {
                            $_amount = 0;
                        }

                        $gl->update(glAccount(GL_GST_OUTPUT), GL_BALANCE_ACCRUALS, $_amount);
                        $t->add(glAccount(GL_GST_OUTPUT), BASIS_ACCRUALS, $_amount);

                        $_amount = bcmul($transactionAmount, 1, 2);
                        if ($view->items['asUnallocated']) {
                            $_amount = 0;
                        }

                        $gl->update(glAccount(GL_DEBTORS_CONTROL), GL_BALANCE_ACCRUALS, $_amount);
                        $t->add(glAccount(GL_DEBTORS_CONTROL), BASIS_ACCRUALS, $_amount);
                    }


                    $fromBatchNumber = dbGetNextReceivableBatchNumber();
                    $transaction['batchNumber'] = $fromBatchNumber;
                    $transaction['lineNumber'] = 1;
                    $transaction['transactionType'] = TYPE_CASH;
                    $transaction['transactionAmount'] = $transactionAmount * -1;
                    $transaction['netAmount'] = $netAmount * -1;
                    $transaction['taxAmount'] = $taxAmount * -1;
                    $transaction['referenceNumber'] = 'Journal';

                    $transaction['batchJournal'] = $batchJournal;
                    dbInsertReceivableTransaction($transaction, true);

                    if (GL_ACTIVE) { // GL for cash - FROM
                        $gl = new GeneralLedger();
                        $gl->transactionType = $transaction['transactionType'];
                        $gl->transactionDate = $view->items['transactionDate'];
                        $gl->description = $transaction['description'];
                        $gl->year = $transaction['year'];
                        $gl->period = $transaction['period'];
                        $gl->propertyID = $transaction['propertyID'];
                        $gl->leaseID = $transaction['leaseID'];
                        $gl->companyID = $transaction['debtorID'];
                        $gl->source = GL_SOURCE_AR;

                        $gl->fromDate = $transaction['fromDate'];
                        $gl->toDate = $transaction['toDate'];
                        $gl->batchID = $transaction['batchNumber'];
                        $gl->lineNumber = $transaction['lineNumber'];

                        $t = new Transaction($gl);    // -- detail entry : adopt the data from the

                        $_amount = bcmul($transaction['netAmount'], 1, 2);

                        $gl->update($transaction['accountID'], GL_BALANCE_CASH, $_amount);
                        $t->add($transaction['accountID'], BASIS_CASH, $_amount);

                        $_amount = bcmul($transaction['taxAmount'], 1, 2);

                        $gl->update(glAccount(GL_GST_OUTPUT), GL_BALANCE_CASH, $_amount);
                        $t->add(glAccount(GL_GST_OUTPUT), BASIS_CASH, $_amount);

                        $_amount = bcmul($transaction['transactionAmount'], 1, 2);
                        $gl->update(glAccount(GL_DEBTORS_CONTROL), GL_BALANCE_ACCRUALS, $_amount);
                        $t->add(glAccount(GL_DEBTORS_CONTROL), BASIS_ACCRUALS, $_amount);

                        $_amount = bcmul($transaction['transactionAmount'], -1, 2);

                        $gl->update(glAccount(GL_BANK), GL_BALANCE_CASH, $_amount);
                        $t->add(glAccount(GL_BANK), BASIS_CASH, $_amount);

                        $gl->update(glAccount(GL_BANK), GL_BALANCE_ACCRUALS, $_amount);
                        $t->add(glAccount(GL_BANK), BASIS_ACCRUALS, $_amount);
                    }

                    $transaction['allocationDate'] = $view->items['transactionDate'];
                    $transaction['allocationNumber'] = dbGetNextAllocationNumber();
                    $transaction['fromBatchNumber'] = $fromBatchNumber;
                    $transaction['fromLineNumber'] = 1;
                    $transaction['toBatchNumber'] = $toBatchNumber;
                    $transaction['toLineNumber'] = 1;
                    $transaction['currentDate'] = TODAY;
                    $transaction['currentTime'] = TODAY;
                    $transaction['fromType'] = TYPE_CASH;

                    $transaction['toType'] = $view->items['asUnallocated'] ? TYPE_INVOICE : TYPE_CREDIT;

                    dbAllocateTransaction($transaction);


                    // -- insert into AR trans (C) INV -
                    // -- insert into AR trans (D) CSH -
                    // -- get next allocation number
                    // -- insert into AR alloc (D->C)

                    $toBatchNumber = dbGetNextReceivableBatchNumber();
                    $transaction = [];
                    $transaction['batchNumber'] = $toBatchNumber;
                    $transaction['lineNumber'] = 1;

                    $transaction['transactionType'] = $view->items['asUnallocated'] ? TYPE_CREDIT : TYPE_INVOICE;

                    $transaction['year'] = $toPeriod['year'];
                    $transaction['period'] = $toPeriod['period'];
                    $transaction['transactionYear'] = $toPeriod['year'];
                    $transaction['transactionPeriod'] = $toPeriod['period'];
                    $bankDetails = dbGetBankDetails($view->items['toPropertyID']);
                    $transaction['bankID'] = $bankDetails['bankID'];

                    $transaction['propertyID'] = $view->items['toPropertyID'];
                    $transaction['leaseID'] = $view->items['toLeaseID'];
                    $transaction['accountID'] = $view->items['toAccountID'];
                    $transaction['leaseID'] = $view->items['toLeaseID'];
                    $transaction['transactionDate'] = $view->items['transactionDate'];
                    $transaction['createUser'] = $_SESSION['un'];
                    $transaction['createDate'] = TODAY;
                    $transaction['debtorID'] = $view->items['toDebtorID'];
                    $transaction['description'] = $view->items['toDescription'];

                    $transactionAmount = bcmul(1, $view->items['transactionAmount'], 2);
                    $netAmount = bcmul(1, $view->items['toNetAmount'], 2);
                    $taxAmount = bcmul(1, $view->items['toTaxAmount'], 2);

                    if ($view->items['asUnallocated']) {
                        $transaction['transactionAmount'] = 0;
                        $transaction['netAmount'] = 0;
                        $transaction['taxAmount'] = 0;
                    } else {
                        $transaction['transactionAmount'] = $transactionAmount;
                        $transaction['netAmount'] = $netAmount;
                        $transaction['taxAmount'] = $taxAmount;
                    }


                    $transaction['taxCode'] = $view->items['toTaxRateID'];
                    $transaction['bpayReference'] = '';
                    $transaction['fromDate'] = $view->items['toFromDate'];
                    $transaction['toDate'] = $view->items['toToDate'];
                    $transaction['orderNumber'] = null;
                    $transaction['invoiceNumber'] = null;
                    $transaction['dueDate'] = null;
                    $transaction['isJournal'] = 1;

                    $transaction['batchJournal'] = $batchJournal;
                    dbInsertReceivableTransaction($transaction);

                    if (GL_ACTIVE) { // GL for INVOICE - TO
                        $gl = new GeneralLedger();
                        $gl->transactionType = $transaction['transactionType'];
                        $gl->transactionDate = $view->items['transactionDate'];
                        $gl->description = $transaction['description'];
                        $gl->year = $transaction['year'];
                        $gl->period = $transaction['period'];
                        $gl->propertyID = $transaction['propertyID'];
                        $gl->leaseID = $transaction['leaseID'];
                        $gl->companyID = $transaction['debtorID'];
                        $gl->source = GL_SOURCE_AR;

                        $gl->fromDate = $transaction['fromDate'];
                        $gl->toDate = $transaction['toDate'];
                        $gl->batchID = $transaction['batchNumber'];
                        $gl->lineNumber = $transaction['lineNumber'];

                        $t = new Transaction($gl);    // -- detail entry : adopt the data from the

                        $_amount = bcmul($netAmount, -1, 2);
                        if ($view->items['asUnallocated']) {
                            $_amount = 0;
                        }

                        $gl->update($transaction['accountID'], GL_BALANCE_ACCRUALS, $_amount);
                        $t->add($transaction['accountID'], BASIS_ACCRUALS, $_amount);

                        $_amount = bcmul($taxAmount, -1, 2);
                        if ($view->items['asUnallocated']) {
                            $_amount = 0;
                        }

                        $gl->update(glAccount(GL_GST_OUTPUT), GL_BALANCE_ACCRUALS, $_amount);
                        $t->add(glAccount(GL_GST_OUTPUT), BASIS_ACCRUALS, $_amount);

                        $_amount = bcmul($transactionAmount, 1, 2);
                        if ($view->items['asUnallocated']) {
                            $_amount = 0;
                        }

                        $gl->update(glAccount(GL_DEBTORS_CONTROL), GL_BALANCE_ACCRUALS, $_amount);
                        $t->add(glAccount(GL_DEBTORS_CONTROL), BASIS_ACCRUALS, $_amount);
                    }

                    $fromBatchNumber = dbGetNextReceivableBatchNumber();
                    $transaction['batchNumber'] = $fromBatchNumber;
                    $transaction['lineNumber'] = 1;
                    $transaction['transactionType'] = TYPE_CASH;
                    $transaction['transactionAmount'] = $transactionAmount * -1;
                    $transaction['netAmount'] = $netAmount * -1;
                    $transaction['taxAmount'] = $taxAmount * -1;
                    $transaction['referenceNumber'] = 'Journal';

                    $transaction['batchJournal'] = $batchJournal;
                    dbInsertReceivableTransaction($transaction, true);

                    if (GL_ACTIVE) { // GL for cash - TO
                        $gl = new GeneralLedger();
                        $gl->transactionType = $transaction['transactionType'];
                        $gl->transactionDate = $view->items['transactionDate'];
                        $gl->description = $transaction['description'];
                        $gl->year = $transaction['year'];
                        $gl->period = $transaction['period'];
                        $gl->propertyID = $transaction['propertyID'];
                        $gl->leaseID = $transaction['leaseID'];
                        $gl->companyID = $transaction['debtorID'];
                        $gl->source = GL_SOURCE_AR;

                        $gl->fromDate = $transaction['fromDate'];
                        $gl->toDate = $transaction['toDate'];
                        $gl->batchID = $transaction['batchNumber'];
                        $gl->lineNumber = $transaction['lineNumber'];

                        $t = new Transaction($gl);    // -- detail entry : adopt the data from the

                        $_amount = bcmul($transaction['netAmount'], 1, 2);
                        $gl->update($transaction['accountID'], GL_BALANCE_CASH, $_amount);
                        $t->add($transaction['accountID'], BASIS_CASH, $_amount);

                        $_amount = bcmul($transaction['taxAmount'], 1, 2);

                        $gl->update(glAccount(GL_GST_OUTPUT), GL_BALANCE_CASH, $_amount);
                        $t->add(glAccount(GL_GST_OUTPUT), BASIS_CASH, $_amount);

                        $_amount = bcmul($transaction['transactionAmount'], 1, 2);
                        $gl->update(glAccount(GL_DEBTORS_CONTROL), GL_BALANCE_ACCRUALS, $_amount);
                        $t->add(glAccount(GL_DEBTORS_CONTROL), BASIS_ACCRUALS, $_amount);

                        $_amount = bcmul($transaction['transactionAmount'], -1, 2);

                        $gl->update(glAccount(GL_BANK), GL_BALANCE_CASH, $_amount);
                        $t->add(glAccount(GL_BANK), BASIS_CASH, $_amount);

                        $gl->update(glAccount(GL_BANK), GL_BALANCE_ACCRUALS, $_amount);
                        $t->add(glAccount(GL_BANK), BASIS_ACCRUALS, $_amount);
                    }

                    $transaction['allocationDate'] = $view->items['transactionDate'];
                    $transaction['allocationNumber'] = dbGetNextAllocationNumber();
                    $transaction['fromBatchNumber'] = $fromBatchNumber;
                    $transaction['fromLineNumber'] = 1;
                    $transaction['toBatchNumber'] = $toBatchNumber;
                    $transaction['toLineNumber'] = 1;
                    $transaction['currentDate'] = TODAY;
                    $transaction['currentTime'] = TODAY;
                    $transaction['fromType'] = TYPE_CASH;

                    $transaction['toType'] = $view->items['asUnallocated'] ? TYPE_CREDIT : TYPE_INVOICE;

                    dbAllocateTransaction($transaction);

                    // generate interim invoice
                    $view->items['downloadLink'] = [];
                    if ($view->items['fromPropertyID'] == $view->items['toPropertyID'] && $view->items['fromLeaseID'] == $view->items['toLeaseID']) {
                        // generate 1 interim invoice
                        $masterInvoice = false;
                        $return = prepareTaxInvoice(
                            InvoiceStatementType::ST_INVOICE,
                            $view->items['fromLeaseID'],
                            $view->items['fromPropertyID'],
                            $view->items['transactionDate'],
                            $view->items['transactionDate'],
                            $masterInvoice
                        );
                        $view->items['downloadLink'][] = $return['downloadPath'];
                    } else {
                        // generate 2 interim invoices. 1 each property-lease

                        // from
                        $masterInvoice = false;
                        $return = prepareTaxInvoice(
                            InvoiceStatementType::ST_INVOICE,
                            $view->items['fromLeaseID'],
                            $view->items['fromPropertyID'],
                            $view->items['transactionDate'],
                            $view->items['transactionDate'],
                            $masterInvoice
                        );
                        $view->items['downloadLink'][] = $return['downloadPath'];

                        // to
                        $masterInvoice = false;
                        $return = prepareTaxInvoice(
                            InvoiceStatementType::ST_INVOICE,
                            $view->items['toLeaseID'],
                            $view->items['toPropertyID'],
                            $view->items['transactionDate'],
                            $view->items['transactionDate'],
                            $masterInvoice
                        );
                        $view->items['downloadLink'][] = $return['downloadPath'];
                    }


                    // -- if a transfer journal ID was passed in - it's a temp transfer journal - mark it as processed
                    if ($view->items['transferJournalID']) {
                        dbSetStatusTempTransferJournal($view->items['transferJournalID'], 1);
                    }

                    $view->items['createdBy'] = $_SESSION['un'];
                    dbAddTransferJournalHistory($view->items);

                    $view->items['statusMessage'] = 'Successfully transferred the amount. <br> Links to the interim invoices generated are shown below.';

                    $view->items['fromPropertyID'] = $view->items['toPropertyID'] =
                    $view->items['fromLeaseList'] = $view->items['toLeaseList'] =
                    $view->items['fromBankID'] = $view->items['toBankID'] =
                    $view->items['fromLeaseID'] = $view->items['toLeaseID'] =
                    $view->items['fromAccountID'] = $view->items['toAccountID'] =
                    $view->items['fromDescription'] = $view->items['toDescription'] =
                    $view->items['fromFromDate'] = $view->items['toFromDate'] =
                    $view->items['fromToDate'] = $view->items['toToDate'] =
                    $view->items['transactionAmount'] =
                    $view->items['fromTaxRateID'] =
                    $view->items['toTaxRateID'] =
                    $view->items['fromPropertyBalance'] =
                    $view->items['toPropertyBalance'] = '';
                } else {
                    // escape single quote
                    $view->items['fromDescription'] = str_replace("'", '`', $view->items['fromDescription']);
                    $view->items['toDescription'] = str_replace("'", '`', $view->items['toDescription']);
                    // -- insert into temp
                    $view->items['createdBy'] = $_SESSION['un'];
                    dbAddTempTransferJournal($view->items);

                    dbAddTransferJournalHistory($view->items);

                    $view->items['statusMessage'] = 'Successfully submitted the transfer request';

                    $view->items['fromPropertyID'] = $view->items['toPropertyID'] =
                    $view->items['fromLeaseList'] = $view->items['toLeaseList'] =
                    $view->items['fromBankID'] = $view->items['toBankID'] =
                    $view->items['fromLeaseID'] = $view->items['toLeaseID'] =
                    $view->items['fromAccountID'] = $view->items['toAccountID'] =
                    $view->items['fromDescription'] = $view->items['toDescription'] =
                    $view->items['fromFromDate'] = $view->items['toFromDate'] =
                    $view->items['fromToDate'] = $view->items['toToDate'] =
                    $view->items['transactionAmount'] =
                    $view->items['fromTaxRateID'] =
                    $view->items['toTaxRateID'] =
                    $view->items['fromPropertyBalance'] =
                    $view->items['toPropertyBalance'] = '';
                }
            }

            unset($validationWarnings);

            break;

        case 'tax':

            break;
    }

    $view->items['accountList'] = dbGetGroupedAccounts(INCOME);
    if (! isset($view->items['transactionDate'])) {
        $view->items['transactionDate'] = TODAY;
    }

    if (! isset($view->items['transactionAmount'])) {
        $view->items['transactionAmount'] = 0;
    }


    $view->items['validationErrors'] = $validationErrors;
    $view->items['validationWarnings'] = $validationWarnings;
    $view->render();
}

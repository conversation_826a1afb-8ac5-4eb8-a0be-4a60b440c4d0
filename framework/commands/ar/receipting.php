<?php

include SYSTEMPATH . '/lib/ngForm.php';
include SYSTEMPATH . '/lib/htmler.php';
include_once SYSTEMPATH . '/lib/enums/BankInstitutionCode.php';
include_once SYSTEMPATH . '/lib/enums/BankFileType.php';

use enums\BankFileType;
use enums\BankInstitutionCode;

function buildBSB($bsb)
{
    $start = substr($bsb, 0, 3);
    $end = substr($bsb, -3, 3);

    return (isGreatBritain()) ? $bsb : "{$start}-{$end}";
}

function receipting(&$context)
{
    global $pathPrefix, $clientDirectory;

    $userpermission = new userpermission();
    $result = $userpermission->userHasPageAccess(__FUNCTION__);

    if (! isPostback()) {
        $view = new MasterPage(userViews(), '/ar/receipting.html');
        $view->setSection($context['module']);
    } else {
        $view = new UserControl(userViews(), '/ar/receipting.html');
    }

    $view->bindAttributesFrom($_REQUEST);
    $propMgrLabel = ucwords(strtolower($_SESSION['country_default']['property_manager']));
    $view->items['srchMethods'] = [
        'Please select ...' => '',
        'Debtor' => 'leaseDebtors',
        'Property' => 'properties',
        'Lease' => 'leases',
        $propMgrLabel => 'propertyManagers',
        'Ledger' => 'ledgerProperties',
        'Sub-Ledger' => 'subLedgerProperties',
        'Invoice Number' => 'invoice',
        'Drawer Name' => 'drawers',
        'Direct Deposit Name' => 'efts',
        'CRN' => 'crn',
        'TXN File' => 'txn',
        'NAB BPay File' => 'nab',
        'CBA BPay (Gateway ID)' => 'cba',
        'CBA BTRS File' => 'btrs',
        'CBA BPay (CSV)' => 'cbacsv',
        'QIF File' => 'qif',
        'WBC BPay File' => 'wbc',
        'ANZ BPay File' => 'anz',
        'Bank West BPay File' => 'bwa',
        'OFX File' => 'ofx',
    ];
    $view->items['transactionTypes'] = [
        'Please select...' => '',
        'Invoice' => TYPE_INVOICE,
        'Credit' => TYPE_CREDIT,
    ];
    $view->items['paymentTypes'] = [
        'Please select...' => '',
        'Cheque' => TYPE_CHEQUE,
        'Direct Deposit' => TYPE_DIRECTDEPOSIT,
        'Direct Debit' => TYPE_DIRECTDEBIT,
        // 'BPay' 				=> TYPE_BPAY,
        'EFTPOS' => TYPE_EPOS,
        'Cash' => TYPE_CASH,
    ];


    $view->items['clearDaysDefault'] = htmlspecialchars(
        json_encode(dbGetReceiptingClearDaysList()),
        ENT_QUOTES,
        'UTF-8'
    );
    //
    // BANK Options
    $bankList = [['label' => 'Please Select...', 'value' => '', 'institution' => '']];
    foreach (dbGetBankAccounts() as $opt) {
        $bankList[] = [
            'label' => $opt['bankID'] . ' - ' . $opt['bankAccountName'],
            'value' => $opt['bankID'],
            'institution' => $opt['institution'],
        ];
    }

    $view->items['bankList'] = $bankList;
    //
    // DEBTOR Options
    $leaseDebtorsList = [['label' => 'Select a Debtor', 'value' => '']];
    foreach (dbDebtorList() as $opt) {
        $leaseDebtorsList[] = [
            'label' => $opt['debtorID'] . ' - ' . $opt['debtorName'],
            'value' => $opt['debtorID'],
        ];
    }

    $view->items['leaseDebtorsList'] = $leaseDebtorsList;
    //
    // PROPERTY Options
    $propertiesList = [['label' => 'Select a Property', 'value' => '']];
    foreach (dbPropertyList(true, null, 1) as $opt) {
        $propertiesList[] = [
            'label' => $opt['propertyID'] . ' - ' . $opt['propertyName'],
            'value' => $opt['propertyID'],
            'bank' => $opt['bank'],
        ];
    }

    $view->items['propertiesList'] = $propertiesList;
    //
    //
    // LEDGER PROPERTY Options
    $ledgerPropertiesList = [['label' => 'Select a Ledger', 'value' => '']];
    foreach (dbPropertyList(true, null, 2) as $opt) {
        $ledgerPropertiesList[] = [
            'label' => $opt['propertyID'] . ' - ' . $opt['propertyName'],
            'value' => $opt['propertyID'],
            'bank' => $opt['bank'],
        ];
    }

    $view->items['ledgerPropertiesList'] = $ledgerPropertiesList;
    //
    // LEASE PROPERTY Options
    $leasesList = [['label' => 'Select a Lease', 'value' => '', 'group' => '']];
    foreach (dbFullLeaseList(false, false, null, 1) as $opt) {
        $leasesList[] = [
            'bank' => $opt['bank'],
            'label' => $opt['leaseID'] . ' - ' . $opt['leaseName'],
            'value' => $opt['leaseID'],
            'group' => ($opt['leaseStatus'] == 'C' ? 'Current' : 'Vacated'),
        ];
    }

    $view->items['leasesList'] = $leasesList;
    //
    //
    // LEASE PROPERTY Options
    $subLedgerPropertiesList = [['label' => 'Select a Sub-Ledger', 'value' => '', 'group' => '']];
    foreach (dbFullLeaseList(true, false, null, 2) as $opt) {
        $subLedgerPropertiesList[] = [
            'bank' => $opt['bank'],
            'label' => $opt['leaseID'] . ' - ' . $opt['leaseName'],
            'value' => $opt['leaseID'],
        ];
    }

    $view->items['subLedgerPropertiesList'] = $subLedgerPropertiesList;
    //
    // PROPERTY MANAGER Options
    $propertyManagersList = [['label' => 'Select a ' . $propMgrLabel, 'value' => '']];
    foreach (dbPropertyManagerList() as $opt) {
        $propertyManagersList[] = [
            'label' => $opt['managerID'] . ' - ' . $opt['managerName'],
            'value' => $opt['managerID'],
        ];
    }

    $view->items['propertyManagersList'] = $propertyManagersList;
    //
    // DRAWERS Options
    $drawersList = [['label' => 'Select a Drawer', 'value' => '']];
    foreach (dbChequeList() as $opt) {
        $drawersList[] = [
            'label' => ucwords(strtolower($opt['drawerName'])),
            'value' => strtolower($opt['drawerName']),
        ];
    }

    $view->items['drawersList'] = $drawersList;
    //
    // Direct Deposit Options
    $eftsList = [['label' => 'Select a Direct Deposit Name', 'value' => '']];
    foreach (dbDirectDepositList() as $opt) {
        $eftsList[] = ['label' => $opt['debtorID'] . ' - ' . $opt['depositName'], 'value' => $opt['depositName']];
    }

    $view->items['eftsList'] = $eftsList;
    //
    // DROPDOWN TYPES
    $view->items['drops'] = [
        'leaseDebtors',
        'properties',
        'leases',
        'propertyManagers',
        'drawers',
        'efts',
        'ledgerProperties',
        'subLedgerProperties'];
    $view->items['dropsStr'] = htmlspecialchars(json_encode($view->items['drops']), ENT_QUOTES, 'UTF-8');
    $methodFilter = [];
    foreach ($view->items['srchMethods'] as $label => $value) {
        // skip ledger options if no property on ledger
        if (($value == 'ledgerProperties' || $value == 'subLedgerProperties') && count(
            $ledgerPropertiesList ?? []
        ) <= 1) {
            continue;
        }

        $methodFilter[] = ['label' => $label, 'value' => $value];
    }

    $view->items['methodFilterStr'] = htmlspecialchars(json_encode($methodFilter), ENT_QUOTES, 'UTF-8');

    if ($view->items['action'] == 'generate') {
        $batchNumber = $view->items['batchID'];
        $batch = dbGetPaymentBatchDetails($batchNumber);

        if ($batch) {
            $view->items['bankID'] = $batch['bank'];
        }

        if (strlen(
            $batch['bsbNumber']
        ) != getBsbLengthFromSession() && getDisplayBsbFromSession()) {
            $validationErrors[] = 'EFT cannot be generated. ' . getBsbLabelFromSession(
            ) . ' number must be ' . getBsbLengthFromSession() . ' digits.';
        }

        if (noErrors($validationErrors)) {
            $format = BankInstitutionCode::FILE_FORMAT_EFT[$batch['institution']];
            $fileFormat = $format ?: BankFileType::ABA_FILE_TYPE;

            if (isGreatBritain()) {
                $format = BankInstitutionCode::FILE_FORMAT_EFT_GB[$batch['institution']];
                $fileFormat = $format ?: BankFileType::CSV_FILE_TYPE;
            }

            $_filePath = "{$pathPrefix}{$clientDirectory}/{$fileFormat}/" . DOC_DIRECTDEPOSIT . '/';
            $_downloadPath = "{$clientDirectory}/{$fileFormat}/" . DOC_DIRECTDEPOSIT;

            $file = $batch['institution'] . '_' . 'EFT' . '_' . date('YmdHis') . '.' . $fileFormat;

            $filePath = $_filePath . $file;
            checkDirPath($_filePath);
            $downloadPath = "{$_downloadPath}/{$file}";

            $transactions = dbGetPaymentsForBatch($view->items['batchID']);
            $paymentDate = explode('/', $transactions[0]['transactionDate']);
            $year = substr($paymentDate[2], -2, 2);
            $date = $paymentDate[0] . $paymentDate[1] . $year;

            $eft = (isGreatBritain()) ? new DirectEntryGB(
                $batch['bsbNumber'],
                $batch['accountNumber'],
                $batch['accountName'],
                $filePath
            ) : new DirectEntry(
                buildBSB($batch['bsbNumber']),
                $batch['accountNumber'],
                $batch['accountName'],
                $filePath
            );
            $eft->setHeader($batch['institution'], $batch['paymentID'], 'CIRRUS8', $date);
            $total = 0;
            $importTotal = 0;
            $countRecord = 0;
            foreach ($transactions as $t) {
                $reference = $t['referenceNumber'];
                $amount = bcmul(-1, $t['transactionAmount'], 2);
                if ($amount != $t['cancellationAmount']) {
                    $company = dbGetCompanyBank($t['debtorID']);
                    $countRecord += 1;

                    $eft->addLine(
                        buildBSB($company['bsbNumber']),
                        $company['accountNumber'],
                        '50',
                        $amount * -1,
                        $company['bankAccountName'],
                        $reference,
                        0,
                        $company['companyID'],
                        $batch['paymentName'],
                        $paymentDate,
                        $batch
                    );
                    $total = bcadd($total, $amount, 2);

                    $importTotal += substr($company['accountNumber'], 2, 11);
                }
            }

            if ($total < 0 && $batch['institution'] != BankInstitutionCode::WPN) {
                if (! in_array($batch['institution'], [BankInstitutionCode::BNZ, BankInstitutionCode::ABL, BankInstitutionCode::WB4, BankInstitutionCode::COU, BankInstitutionCode::ASB])) {
                    $eft->addLine(buildBSB($batch['bsbNumber']), $batch['accountNumber'], '13', $total * -1, $batch['paymentName'], $reference, 0);
                }

                $eft->setFooter(0, $total * -1, $total * -1, $importTotal, $countRecord);
            }

            $eft->save();

            return renderDownloadLink($downloadPath, 'class="infoBox"');
        } else {
            $view->items['validationErrors'] = $validationErrors;
        }


        return;
    }

    if ($view->items['action'] == 'print') {
        $cheques = dbGetChequesForBatch($view->items['batchNumber'], $view->items['chequeNumber']);
        if ($cheques) {
            $limitCheck = dbGetParam('TEMPLATE', 'CHQITEMS');
            $timestamp = date('Ymd');
            $path = "{$_SESSION['pathPrefix']}{$_SESSION['clientDirectory']}/pdf/Cheques/CHQ_{$timestamp}.pdf";
            $downloadPath = "{$_SESSION['clientDirectory']}/pdf/Cheques/CHQ_{$timestamp}.pdf";
            $document = dbGetParam('TEMPLATE', 'CHEQUE');
            $suffix = substr($document, 9);
            include_once 'lib/documents/PDFCheque_' . $suffix . '.php';
            $pdf = new $document($path);

            foreach ($cheques as $cheque) {
                $remittance = $cheque['companyName'] . "\n" . $cheque['companyAddress'] . "\n" . $cheque['companyCity'] . ' ' . $cheque['companyState'] . ' ' . $cheque['companyPostCode'];

                $chequeNumber = $cheque['chequeNumber'];
                $creditorID = $cheque['creditorID'];
                $chequeDetails = dbGetChequeDetails($view->items['batchNumber'], $creditorID);


                $pdf->preparePage();

                $offset = 0;
                $count = 0;
                $counter = 0;
                $records = count($chequeDetails ?? []);
                // -- if the total number of cheques minus the number processed to date is still greater than the limit for a page, set the cheque to be cancelled
                $overflow = (($records) > $limitCheck);
                $pdf->printHeader(
                    $cheque['creditorID'],
                    $cheque['transactionDate'],
                    (($overflow) ? '******' : $chequeNumber)
                );

                foreach ($chequeDetails as $details) {
                    if ($counter >= $limitCheck) {
                        $pdf->printNumbers('0.00', $remittance, $cheque['transactionDate'], null, null);
                        $pdf->endPage();
                        $pdf->preparePage();

                        // -- work out whether its the final page
                        $overflow = (($records - $count) > $limitCheck);
                        $pdf->printHeader(
                            $cheque['creditorID'],
                            $cheque['transactionDate'],
                            (($overflow) ? '******' : $chequeNumber)
                        );
                        $counter = 0;
                        $offset = 0;
                    }

                    $count++;
                    $counter++;

                    $transactionAmount = $details['amount'];
                    $propertyName = $details['propertyName'];
                    if (strlen($details['invoiceNumber']) > 15) {
                        $details['invoiceNumber'] = substr($details['invoiceNumber'], 0, 15);
                    }

                    $pdf->printLine(
                        $details['transactionDate'],
                        $details['invoiceNumber'],
                        'Refund: ' . $details['description'],
                        $transactionAmount,
                        $offset,
                        $propertyName
                    );
                    $offset += 12;
                }

                $pdf->printNumbers(
                    toDecimal($cheque['totalAllocated']),
                    ($cheque['totalAllocated']),
                    $remittance,
                    $cheque['transactionDate'],
                    $cheque['companyName'],
                    $chequeNumber
                );


                $pdf->endPage();
            }

            $pdf->close();

            return renderDownloadLink($downloadPath, 'class="infoBox"');
        }

        return;
    }

    // end
    $view->render();

}

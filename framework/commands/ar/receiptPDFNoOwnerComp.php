<?php

/**
 * @param  $o  array Mandatory. Array containing the bankID, batchNumber, includeLogo, lineNumber, receiptNumber, fromDate, toDate, logoPath, filePath, downloadPath, and parentReceipt.
 * @param  $receipts  array Optional.
 *
 * <AUTHOR> Reyes
 *
 * @since 2013-01-18
 **/
function prepareReceipt($o, $receipts = '')
{
    global $clientDirectory, $pathPrefix;
    extract($o);

    if ($includeLogo == 'Yes') {
        $logoFile = dbGetClientLogo();
        $logoPath = "assets/clientLogos/{$logoFile}";
    }

    if ($receiptNumber && $bankID) {
        $filename = "receipt_{$receiptNumber}_{$bankID}.pdf";
    } else {
        [$day, $month, $year] = explode('/', $fromDate);
        $time = time();
        $filename = "receipt_{$year}{$month}{$day}_{$time}.pdf";
    }

    $filePath = "{$pathPrefix}{$clientDirectory}/pdf/Receipt/{$filename}";
    $downloadPath = "{$clientDirectory}/pdf/Receipt/{$filename}";

    $receipt = new Receipt($filePath, $logoPath);
    logData(serialize($receipt));

    if ($receipts) {
        foreach ($receipts as $myReceipt) {
            $currentLease = '';
            $currentLeaseChecker = '';
            $receipt->objects = [];
            $receiptTotal = 0;
            $batchNumber = $myReceipt['batchNumber'];
            $lineNumber = $myReceipt['lineNumber'];

            // Payment Type
            $paymentTypeList =
            [
                'Q' => 'Cheque',
                'K' => 'Cash',
                'C' => 'EFT',
                'D' => 'Direct Debit',
            ];
            if (cdf_isAU()) {
                $paymentTypeList['B'] = 'BPay';
            }

            $details = dbGetReceiptBasic($batchNumber, $lineNumber);
            $paymentType = $paymentTypeList[$details['paymentType']];
            $completedDate = $details['completedDate'];
            $username = $details['username'];
            if ($paymentType === '0') {
                $paymentType = 'EFT';
            }

            $o =
            [

                'batchNumber' => $batchNumber,
                'lineNumber' => $lineNumber,
                'receiptNumber' => $myReceipt['receiptNumber'],
            ];
            $o = array_merge($o, $details);
            $o['paymentType'] = $paymentType;
            $data = dbGetReceiptData($batchNumber, $lineNumber);
            $bankDate = $data[0]['bankDate'];
            $_propertyID = $data[0]['propertyID'];
            $_leaseID = $data[0]['leaseID'];
            // Header
            $header = new ReceiptHeader($data[0]['propertyID'], $data[0]['leaseID']);
            $header->bindAttributesFrom($o);
            if (! $header->propertyID) {
                $header->propertyID = $data[0]['propertyID'];
            }

            $receipt->attachObject('header', $header);

            // Body Lines
            $receiptLines = new receiptLines();
            $receipt->attachObject('receiptLines', $receiptLines);

            // Footer
            $footer = new receiptFooter($paymentType, $completedDate, $username, $bankDate);
            if ($paymentType === 'Cheque') {
                $footer->drawerName = dbGetReceiptDrawer($batchNumber, $lineNumber);
            }

            $receipt->attachObject('footer', $footer);
            $receipt->attachObject('traccFooter', new TraccFooter('assets/clientLogos/tracc_logo.jpg', 'Receipt', 1));

            // Body
            $obj = 0;
            $signitureArr = [];
            foreach ($data as $row) {
                if ($offset >= 380) {
                    $receiptBody = new receiptBody('Continued...', 480, 155, 80, 20, 'right', '', 'Helvetica-Bold', 9);
                    $receipt->attachObject('receiptBody' . $obj++, $receiptBody);

                    $receipt->preparePage();
                    $receipt->endPage();
                    $receipt->objects = [];
                    $offset = 0;

                    // Header
                    $header = new ReceiptHeader($data[0]['propertyID'], $data[0]['leaseID']);
                    $header->bindAttributesFrom($o);
                    if (! $header->propertyID) {
                        $header->propertyID = $data[0]['propertyID'];
                    }

                    $receipt->attachObject('header', $header);

                    // Body Lines
                    $receiptLines = new receiptLines();
                    $receipt->attachObject('receiptLines', $receiptLines);

                    // Footer
                    $footer = new receiptFooter($paymentType, $completedDate, $username, $bankDate);
                    if ($paymentType === 'Cheque') {
                        $footer->drawerName = dbGetReceiptDrawer($batchNumber, $lineNumber);
                    }

                    $receipt->attachObject('footer', $footer);
                    $receipt->attachObject(
                        'traccFooter',
                        new TraccFooter('assets/clientLogos/tracc_logo.jpg', 'Receipt', 1)
                    );
                }

                // Check change in Lease
                if ($currentLeaseChecker !== $row['leaseID'] . $row['propertyID']) {
                    if ($currentLeaseChecker !== '') {
                        $offset += 10;
                        $data = dbReceiptUnallocatedCash($batchNumber, $lineNumber, $currentLease);
                        foreach ($data as $unallocatedCash) {
                            if ($unallocatedCash['unallocatedAmount'] < 0) {
                                if ($offset >= 380) {
                                    $receiptBody = new receiptBody(
                                        'Continued...',
                                        480,
                                        155,
                                        80,
                                        20,
                                        'right',
                                        '',
                                        'Helvetica-Bold',
                                        9
                                    );
                                    $receipt->attachObject('receiptBody' . $obj++, $receiptBody);

                                    $receipt->preparePage();
                                    $receipt->endPage();
                                    $receipt->objects = [];
                                    $offset = 0;

                                    // Header
                                    $header = new ReceiptHeader($data[0]['propertyID'], $data[0]['leaseID']);
                                    $header->bindAttributesFrom($o);
                                    if (! $header->propertyID) {
                                        $header->propertyID = $data[0]['propertyID'];
                                    }

                                    $receipt->attachObject('header', $header);

                                    // Body Lines
                                    $receiptLines = new receiptLines();
                                    $receipt->attachObject('receiptLines', $receiptLines);

                                    // Footer
                                    $footer = new receiptFooter($paymentType, $completedDate, $username, $bankDate);
                                    if ($paymentType === 'Cheque') {
                                        $footer->drawerName = dbGetReceiptDrawer($batchNumber, $lineNumber);
                                    }

                                    $receipt->attachObject('footer', $footer);
                                    $receipt->attachObject(
                                        'traccFooter',
                                        new TraccFooter('assets/clientLogos/tracc_logo.jpg', 'Receipt', 1)
                                    );
                                }

                                if ($currentLease != $unallocatedCash['leaseID']) {
                                    if ($currentLease != '') {
                                        $offset += 10;
                                    }

                                    $currentLease = $unallocatedCash['leaseID'];

                                    extract(
                                        dbReceiptAddress(
                                            $batchNumber,
                                            $lineNumber,
                                            $unallocatedCash['propertyID'],
                                            $unallocatedCash['leaseID']
                                        )
                                    );


                                    $leaseDescription = "{$unallocatedCash['leaseName']} ({$unallocatedCash['leaseID']}) - {$tenancyLocation} ({$unallocatedCash['propertyID']})";

                                    $itemHeight = strlen($leaseDescription) > 136 ? 24 : 20;

                                    $receiptBody = new receiptBody(
                                        $leaseDescription,
                                        34,
                                        555 - $offset,
                                        310,
                                        $itemHeight,
                                        'left',
                                        '',
                                        'Helvetica-Bold'
                                    );
                                    $receipt->attachObject('receiptBody' . $obj++, $receiptBody);


                                    if (strlen($leaseDescription) > 70) {
                                        $offset += 10;
                                    }

                                    $offset += 10;
                                }

                                $amount = -($unallocatedCash['unallocatedAmount']);
                                $receiptTotal += $amount;
                                $receiptBody = new receiptBody(
                                    $unallocatedCash['description'],
                                    34,
                                    555 - $offset,
                                    290,
                                    20,
                                    'left',
                                    ''
                                );
                                $receipt->attachObject('receiptBody' . $obj++, $receiptBody);
                                $receiptBody = new receiptBody('', 334, 555 - $offset, 86, 20, 'left', '');
                                $receipt->attachObject('receiptBody' . $obj++, $receiptBody);
                                $receiptBody = new receiptBody('', 409, 555 - $offset, 246, 20, 'left', '');
                                $receipt->attachObject('receiptBody' . $obj++, $receiptBody);
                                $receiptBody = new receiptBody(
                                    toMoney($amount),
                                    480,
                                    555 - $offset,
                                    80,
                                    20,
                                    'right',
                                    ''
                                );
                                $receipt->attachObject('receiptBody' . $obj++, $receiptBody);
                                $offset += 10;
                            }
                        }
                    }

                    $currentLease = $row['leaseID'];
                    $leaseID = $row['leaseID'];
                    $currentLeaseChecker = $row['leaseID'] . $row['propertyID'];

                    extract(dbReceiptAddress($batchNumber, $lineNumber, $row['propertyID'], $row['leaseID']));


                    $leaseDescription = "{$row['leaseName']} ({$row['leaseID']}) - {$tenancyLocation} ({$row['propertyID']})";

                    $itemHeight = strlen($leaseDescription) > 136 ? 24 : 20;

                    $receiptBody = new receiptBody(
                        $leaseDescription,
                        34,
                        555 - $offset,
                        310,
                        $itemHeight,
                        'left',
                        '',
                        'Helvetica-Bold'
                    );
                    $receipt->attachObject('receiptBody' . $obj++, $receiptBody);
                    if (strlen($leaseDescription) > 70) {
                        $offset += 10;
                    }

                    $offset += 10;

                    $companyDetails = dbGetCompanyByPropertyCode($row['propertyID']);
                    foreach ($companyDetails as $aRow) {
                        $arr = [
                            'code' => $row['propertyID'],
                            'company' => $aRow['pmco_name'],
                        ];
                        if (! in_array($arr, $signitureArr)) {
                            $signitureArr[] = $arr;
                        }
                    }
                }

                if ($row['dishonours'] > 0 && $row['dishonours'] != '') {
                    $receiptBody = new receiptBody(
                        'RECEIPT CANCELLED',
                        34,
                        77,
                        200,
                        19,
                        'left',
                        '',
                        'Helvetica-Bold',
                        12
                    );
                    $receipt->attachObject('receiptBody' . $obj++, $receiptBody);
                }

                $amount = $row['allocatedAmount'];
                $receiptTotal += $amount;
                $receiptBody = new receiptBody($row['description'], 34, 555 - $offset, 290, 20, 'left', '');
                $receipt->attachObject('receiptBody' . $obj++, $receiptBody);
                $receiptBody = new receiptBody($row['fromDate'], 350, 555 - $offset, 86, 20, 'left', '');
                $receipt->attachObject('receiptBody' . $obj++, $receiptBody);
                $receiptBody = new receiptBody($row['toDate'], 420, 555 - $offset, 246, 20, 'left', '');
                $receipt->attachObject('receiptBody' . $obj++, $receiptBody);
                $receiptBody = new receiptBody(toMoney($amount), 480, 555 - $offset, 80, 20, 'right', '');
                $receipt->attachObject('receiptBody' . $obj++, $receiptBody);
                $offset += 10;
            }

            $data = dbReceiptUnallocatedCash($batchNumber, $lineNumber, $currentLease);
            foreach ($data as $unallocatedCash) {
                if ($unallocatedCash['unallocatedAmount'] < 0) {
                    if ($offset >= 380) {
                        $receiptBody = new receiptBody(
                            'Continued...',
                            480,
                            155,
                            80,
                            20,
                            'right',
                            '',
                            'Helvetica-Bold',
                            9
                        );
                        $receipt->attachObject('receiptBody' . $obj++, $receiptBody);

                        $receipt->preparePage();
                        $receipt->endPage();
                        $receipt->objects = [];
                        $offset = 0;

                        // Header
                        $header = new ReceiptHeader($data[0]['propertyID'], $data[0]['leaseID']);
                        $header->bindAttributesFrom($o);
                        if (! $header->propertyID) {
                            $header->propertyID = $data[0]['propertyID'];
                        }

                        $receipt->attachObject('header', $header);

                        // Body Lines
                        $receiptLines = new receiptLines();
                        $receipt->attachObject('receiptLines', $receiptLines);

                        // Footer
                        $footer = new receiptFooter($paymentType, $completedDate, $username, $bankDate);
                        if ($paymentType === 'Cheque') {
                            $footer->drawerName = dbGetReceiptDrawer($batchNumber, $lineNumber);
                        }

                        $receipt->attachObject('footer', $footer);
                        $receipt->attachObject(
                            'traccFooter',
                            new TraccFooter('assets/clientLogos/tracc_logo.jpg', 'Receipt', 1)
                        );
                    }

                    if ($currentLease != $unallocatedCash['leaseID']) {
                        if ($currentLease != '') {
                            $offset += 10;
                        }

                        $currentLease = $unallocatedCash['leaseID'];

                        extract(
                            dbReceiptAddress(
                                $batchNumber,
                                $lineNumber,
                                $unallocatedCash['propertyID'],
                                $unallocatedCash['leaseID']
                            )
                        );


                        $leaseDescription = "{$unallocatedCash['leaseName']} ({$unallocatedCash['leaseID']}) - {$tenancyLocation} ({$unallocatedCash['propertyID']})";

                        $itemHeight = strlen($leaseDescription) > 136 ? 24 : 20;

                        $receiptBody = new receiptBody(
                            $leaseDescription,
                            34,
                            555 - $offset,
                            310,
                            $itemHeight,
                            'left',
                            '',
                            'Helvetica-Bold'
                        );
                        $receipt->attachObject('receiptBody' . $obj++, $receiptBody);

                        $companyDetails = dbGetCompanyByPropertyCode($unallocatedCash['propertyID']);
                        foreach ($companyDetails as $aRow) {
                            $arr = [
                                'code' => $unallocatedCash['propertyID'],
                                'company' => $aRow['pmco_name'],
                            ];
                            if (! in_array($arr, $signitureArr)) {
                                $signitureArr[] = $arr;
                            }
                        }


                        if (strlen($leaseDescription) > 70) {
                            $offset += 10;
                        }

                        $offset += 10;
                    }


                    $amount = -($unallocatedCash['unallocatedAmount']);
                    $receiptTotal += $amount;
                    $receiptBody = new receiptBody(
                        $unallocatedCash['description'],
                        34,
                        555 - $offset,
                        290,
                        20,
                        'left',
                        ''
                    );
                    $receipt->attachObject('receiptBody' . $obj++, $receiptBody);
                    $receiptBody = new receiptBody('', 334, 555 - $offset, 86, 20, 'left', '');
                    $receipt->attachObject('receiptBody' . $obj++, $receiptBody);
                    $receiptBody = new receiptBody('', 409, 555 - $offset, 246, 20, 'left', '');
                    $receipt->attachObject('receiptBody' . $obj++, $receiptBody);
                    $receiptBody = new receiptBody(toMoney($amount), 480, 555 - $offset, 80, 20, 'right', '');
                    $receipt->attachObject('receiptBody' . $obj++, $receiptBody);
                    $offset += 10;
                }
            }

            // Total
            $receiptBody = new receiptBody(toMoney($receiptTotal), 480, 155, 80, 20, 'right', '', 'Helvetica', 9);
            $receipt->attachObject('receiptBody' . $obj++, $receiptBody);

            $receipt->preparePage();

            $receipt->endPage();
            $offset = 0;
            // EFT REMITTANCE ADVICE
            $payments = dbGetReceiptData($batchNumber, $lineNumber);
            $transactions = $payments;
            $transactionDate = $transactions[0]['transactionDate'];
            $referenceNumber = $transactions[0]['referenceNumber2'];
            $creditorID = $transactions[0]['debtorID'];

            $method = dbGetCompany($creditorID);
            if ($method['paymentMethod'] == PAY_EFT && $receiptTotal < 0) {
                $receipt->objects = [];
                $receipt->className = '';

                $header = new RemittanceHeader($referenceNumber, $transactionDate);
                $header->paymentType = $paymentType;
                $receipt->attachObject('header', $header);

                $footer = new RemittanceFooter($creditorID);
                $footer->paymentType = $paymentType;
                $receipt->attachObject('footer', $footer);

                $mailAddress = new MailAddressWindow($creditorID);
                $receipt->attachObject('mailAddress', $mailAddress);

                $lines = new RemittanceLines();
                $receipt->attachObject('lines', $lines);
                $receipt->attachObject(
                    'traccFooter',
                    new TraccFooter('assets/clientLogos/tracc_logo.jpg', 'Remittance_Advice', A4_PORTRAIT)
                );

                $cashTotal = 0;
                $chequeTotal = 0;
                $cashCount = 0;
                $chequeCount = 0;

                if (! isEmptyArray($payments)) {
                    $totalRemittance = 0;
                    $transactions = $payments;

                    $lineOffset = 15;
                    foreach ($transactions as $payment) {
                        $totalRemittance = bcadd($totalRemittance, $payment['allocatedAmount'] * -1, 2);

                        $boxRemittance = new RemittanceAdvice(
                            $payment['allocatedDate'],
                            $payment['invoiceNumber'],
                            $payment['propertyName'],
                            $payment['description'],
                            $payment['allocatedAmount'] * -1,
                            $lineOffset
                        );
                        $receipt->attachObject('boxRemittance' . $lineOffset, $boxRemittance);
                        $lineOffset += 15;
                    }

                    $remittanceTotal = new RemittanceTotal($totalRemittance);
                    $receipt->attachObject('remittanceTotal', $remittanceTotal);
                }

                $receipt->preparePage(0);
                $receipt->endPage();
            }

            // END EFT REMITTANCE ADVICE
        }

        $receipt->close();
    } else {
        $data = dbGetReceiptData($batchNumber, $lineNumber);
        $bankDate = $data[0]['bankDate'];
        $_propertyID = $data[0]['propertyID'];
        $_leaseID = $data[0]['leaseID'];
        // Header
        $header = new ReceiptHeader($data[0]['propertyID'], $data[0]['leaseID']);
        $header->bindAttributesFrom($o);
        if (! $header->propertyID) {
            $header->propertyID = $data[0]['propertyID'];
        }

        $receipt->attachObject('header', $header);

        // Body Lines
        $receiptLines = new receiptLines();
        $receipt->attachObject('receiptLines', $receiptLines);

        // Footer
        $footer = new receiptFooter($paymentType, $completedDate, $username, $bankDate);
        if ($paymentType == 'Cheque') {
            $footer->drawerName = dbGetReceiptDrawer($batchNumber, $lineNumber);
        }

        $receipt->attachObject('footer', $footer);
        $receipt->attachObject('traccFooter', new TraccFooter('assets/clientLogos/tracc_logo.jpg', 'Receipt', 1));

        // Body
        $obj = 0;
        $signitureArr = [];
        foreach ($data as $row) {
            if ($offset >= 380) {
                $receiptBody = new receiptBody('Continued...', 480, 155, 80, 20, 'right', '', 'Helvetica-Bold', 9);
                $receipt->attachObject('receiptBody' . $obj++, $receiptBody);

                $receipt->preparePage();
                $receipt->endPage();
                $receipt->objects = [];
                $offset = 0;

                // Header
                $header = new ReceiptHeader($data[0]['propertyID'], $data[0]['leaseID']);
                $header->bindAttributesFrom($o);
                if (! $header->propertyID) {
                    $header->propertyID = $data[0]['propertyID'];
                }

                $receipt->attachObject('header', $header);

                // Body Lines
                $receiptLines = new receiptLines();
                $receipt->attachObject('receiptLines', $receiptLines);

                // Footer
                $footer = new receiptFooter($paymentType, $completedDate, $username, $bankDate);
                if ($paymentType == 'Cheque') {
                    $footer->drawerName = dbGetReceiptDrawer($batchNumber, $lineNumber);
                }

                $receipt->attachObject('footer', $footer);
                $receipt->attachObject(
                    'traccFooter',
                    new TraccFooter('assets/clientLogos/tracc_logo.jpg', 'Receipt', 1)
                );
            }

            // Check change in Lease
            if ($currentLeaseChecker != $row['leaseID'] . $row['propertyID']) {
                if ($currentLeaseChecker != '') {
                    $offset += 10;
                    $data = dbReceiptUnallocatedCash($batchNumber, $lineNumber, $currentLease);
                    foreach ($data as $unallocatedCash) {
                        if ($unallocatedCash['unallocatedAmount'] < 0) {
                            if ($offset >= 380) {
                                $receiptBody = new receiptBody(
                                    'Continued...',
                                    480,
                                    155,
                                    80,
                                    20,
                                    'right',
                                    '',
                                    'Helvetica-Bold',
                                    9
                                );
                                $receipt->attachObject('receiptBody' . $obj++, $receiptBody);

                                $receipt->preparePage();
                                $receipt->endPage();
                                $receipt->objects = [];
                                $offset = 0;

                                // Header
                                $header = new ReceiptHeader($data[0]['propertyID'], $data[0]['leaseID']);
                                $header->bindAttributesFrom($o);
                                if (! $header->propertyID) {
                                    $header->propertyID = $data[0]['propertyID'];
                                }

                                $receipt->attachObject('header', $header);

                                // Body Lines
                                $receiptLines = new receiptLines();
                                $receipt->attachObject('receiptLines', $receiptLines);

                                // Footer
                                $footer = new receiptFooter($paymentType, $completedDate, $username, $bankDate);
                                if ($paymentType == 'Cheque') {
                                    $footer->drawerName = dbGetReceiptDrawer($batchNumber, $lineNumber);
                                }

                                $receipt->attachObject('footer', $footer);
                                $receipt->attachObject(
                                    'traccFooter',
                                    new TraccFooter('assets/clientLogos/tracc_logo.jpg', 'Receipt', 1)
                                );
                            }

                            if ($currentLease != $unallocatedCash['leaseID']) {
                                if ($currentLease != '') {
                                    $offset += 10;
                                }

                                $currentLease = $unallocatedCash['leaseID'];

                                extract(
                                    dbReceiptAddress(
                                        $batchNumber,
                                        $lineNumber,
                                        $unallocatedCash['propertyID'],
                                        $unallocatedCash['leaseID']
                                    )
                                );


                                $leaseDescription = "{$unallocatedCash['leaseName']} ({$unallocatedCash['leaseID']}) - {$tenancyLocation} ({$unallocatedCash['propertyID']})";

                                $itemHeight = strlen($leaseDescription) > 136 ? 24 : 20;

                                $receiptBody = new receiptBody(
                                    $leaseDescription,
                                    34,
                                    555 - $offset,
                                    310,
                                    $itemHeight,
                                    'left',
                                    '',
                                    'Helvetica-Bold'
                                );
                                $receipt->attachObject('receiptBody' . $obj++, $receiptBody);

                                if (strlen($leaseDescription) > 70) {
                                    $offset += 10;
                                }

                                $offset += 10;
                            }

                            $amount = -($unallocatedCash['unallocatedAmount']);
                            $receiptTotal += $amount;
                            $receiptBody = new receiptBody(
                                $unallocatedCash['description'],
                                34,
                                555 - $offset,
                                290,
                                20,
                                'left',
                                ''
                            );
                            $receipt->attachObject('receiptBody' . $obj++, $receiptBody);
                            $receiptBody = new receiptBody('', 334, 555 - $offset, 86, 20, 'left', '');
                            $receipt->attachObject('receiptBody' . $obj++, $receiptBody);
                            $receiptBody = new receiptBody('', 409, 555 - $offset, 246, 20, 'left', '');
                            $receipt->attachObject('receiptBody' . $obj++, $receiptBody);
                            $receiptBody = new receiptBody(toMoney($amount), 480, 555 - $offset, 80, 20, 'right', '');
                            $receipt->attachObject('receiptBody' . $obj++, $receiptBody);
                            $offset += 10;
                        }
                    }
                }

                $currentLease = $row['leaseID'];
                $leaseID = $row['leaseID'];
                $currentLeaseChecker = $row['leaseID'] . $row['propertyID'];

                extract(dbReceiptAddress($batchNumber, $lineNumber, $row['propertyID'], $row['leaseID']));


                $leaseDescription = "{$row['leaseName']} ({$row['leaseID']}) - {$tenancyLocation} ({$row['propertyID']})";

                $itemHeight = strlen($leaseDescription) > 136 ? 24 : 20;

                $receiptBody = new receiptBody(
                    $leaseDescription,
                    34,
                    555 - $offset,
                    310,
                    $itemHeight,
                    'left',
                    '',
                    'Helvetica-Bold'
                );
                $receipt->attachObject('receiptBody' . $obj++, $receiptBody);
                if (strlen($leaseDescription) > 70) {
                    $offset += 10;
                }

                $offset += 10;

                $companyDetails = dbGetCompanyByPropertyCode($row['propertyID']);
                foreach ($companyDetails as $aRow) {
                    $arr = [
                        'code' => $row['propertyID'],
                        'company' => $aRow['pmco_name'],
                    ];
                    if (! in_array($arr, $signitureArr)) {
                        $signitureArr[] = $arr;
                    }
                }
            }

            if ($row['dishonours'] > 0 && $row['dishonours'] != '') {
                $receiptBody = new receiptBody('RECEIPT CANCELLED', 34, 77, 200, 19, 'left', '', 'Helvetica-Bold', 12);
                $receipt->attachObject('receiptBody' . $obj++, $receiptBody);
            }

            $amount = $row['allocatedAmount'];
            $receiptTotal += $amount;
            $receiptBody = new receiptBody($row['description'], 34, 555 - $offset, 290, 20, 'left', '');
            $receipt->attachObject('receiptBody' . $obj++, $receiptBody);
            $receiptBody = new receiptBody($row['fromDate'], 350, 555 - $offset, 86, 20, 'left', '');
            $receipt->attachObject('receiptBody' . $obj++, $receiptBody);
            $receiptBody = new receiptBody($row['toDate'], 420, 555 - $offset, 246, 20, 'left', '');
            $receipt->attachObject('receiptBody' . $obj++, $receiptBody);
            $receiptBody = new receiptBody(toMoney($amount), 480, 555 - $offset, 80, 20, 'right', '');
            $receipt->attachObject('receiptBody' . $obj++, $receiptBody);
            $offset += 10;
        }

        $data = dbReceiptUnallocatedCash($batchNumber, $lineNumber, $currentLease);
        foreach ($data as $unallocatedCash) {
            if ($unallocatedCash['unallocatedAmount'] < 0) {
                if ($offset >= 380) {
                    $receiptBody = new receiptBody('Continued...', 480, 155, 80, 20, 'right', '', 'Helvetica-Bold', 9);
                    $receipt->attachObject('receiptBody' . $obj++, $receiptBody);

                    $receipt->preparePage();
                    $receipt->endPage();
                    $receipt->objects = [];
                    $offset = 0;

                    // Header
                    $header = new ReceiptHeader($data[0]['propertyID'], $data[0]['leaseID']);
                    $header->bindAttributesFrom($o);
                    if (! $header->propertyID) {
                        $header->propertyID = $data[0]['propertyID'];
                    }

                    $receipt->attachObject('header', $header);

                    // Body Lines
                    $receiptLines = new receiptLines();
                    $receipt->attachObject('receiptLines', $receiptLines);

                    // Footer
                    $footer = new receiptFooter($paymentType, $completedDate, $username, $bankDate);
                    if ($paymentType == 'Cheque') {
                        $footer->drawerName = dbGetReceiptDrawer($batchNumber, $lineNumber);
                    }

                    $receipt->attachObject('footer', $footer);
                    $receipt->attachObject(
                        'traccFooter',
                        new TraccFooter('assets/clientLogos/tracc_logo.jpg', 'Receipt', 1)
                    );
                }

                if ($currentLease != $unallocatedCash['leaseID']) {
                    if ($currentLease != '') {
                        $offset += 10;
                    }

                    $currentLease = $unallocatedCash['leaseID'];

                    extract(
                        dbReceiptAddress(
                            $batchNumber,
                            $lineNumber,
                            $unallocatedCash['propertyID'],
                            $unallocatedCash['leaseID']
                        )
                    );


                    $leaseDescription = "{$unallocatedCash['leaseName']} ({$unallocatedCash['leaseID']}) - {$tenancyLocation} ({$unallocatedCash['propertyID']})";

                    $itemHeight = strlen($leaseDescription) > 136 ? 24 : 20;

                    $receiptBody = new receiptBody(
                        $leaseDescription,
                        34,
                        555 - $offset,
                        310,
                        $itemHeight,
                        'left',
                        '',
                        'Helvetica-Bold'
                    );
                    $receipt->attachObject('receiptBody' . $obj++, $receiptBody);

                    $companyDetails = dbGetCompanyByPropertyCode($unallocatedCash['propertyID']);
                    foreach ($companyDetails as $aRow) {
                        $arr = [
                            'code' => $unallocatedCash['propertyID'],
                            'company' => $aRow['pmco_name'],
                        ];
                        if (! in_array($arr, $signitureArr)) {
                            $signitureArr[] = $arr;
                        }
                    }


                    if (strlen($leaseDescription) > 70) {
                        $offset += 10;
                    }

                    $offset += 10;
                }


                $amount = -($unallocatedCash['unallocatedAmount']);
                $receiptTotal += $amount;
                $receiptBody = new receiptBody(
                    $unallocatedCash['description'],
                    34,
                    555 - $offset,
                    290,
                    20,
                    'left',
                    ''
                );
                $receipt->attachObject('receiptBody' . $obj++, $receiptBody);
                $receiptBody = new receiptBody('', 334, 555 - $offset, 86, 20, 'left', '');
                $receipt->attachObject('receiptBody' . $obj++, $receiptBody);
                $receiptBody = new receiptBody('', 409, 555 - $offset, 246, 20, 'left', '');
                $receipt->attachObject('receiptBody' . $obj++, $receiptBody);
                $receiptBody = new receiptBody(toMoney($amount), 480, 555 - $offset, 80, 20, 'right', '');
                $receipt->attachObject('receiptBody' . $obj++, $receiptBody);
                $offset += 10;
            }
        }

        // Total
        $receiptBody = new receiptBody(toMoney($receiptTotal), 480, 155, 80, 20, 'right', '', 'Helvetica', 9);
        $receipt->attachObject('receiptBody' . $obj++, $receiptBody);

        $receipt->preparePage();

        $receipt->endPage();
        // EFT REMITTANCE ADVICE
        $payments = dbGetReceiptData($batchNumber, $lineNumber);
        $transactions = $payments;
        $transactionDate = $transactions[0]['transactionDate'];
        $referenceNumber = $transactions[0]['referenceNumber2'];
        $creditorID = $transactions[0]['debtorID'];


        $method = dbGetCompany($creditorID);
        if ($method['paymentMethod'] == PAY_EFT && $receiptTotal < 0) {
            $receipt->objects = [];
            $receipt->className = '';


            $header = new RemittanceHeader($referenceNumber, $transactionDate);
            $header->paymentType = $paymentType;
            $receipt->attachObject('header', $header);

            $footer = new RemittanceFooter($creditorID);
            $footer->paymentType = $paymentType;
            $receipt->attachObject('footer', $footer);

            $mailAddress = new MailAddressWindow($creditorID);
            $receipt->attachObject('mailAddress', $mailAddress);

            $lines = new RemittanceLines();
            $receipt->attachObject('lines', $lines);
            $receipt->attachObject(
                'traccFooter',
                new TraccFooter('assets/clientLogos/tracc_logo.jpg', 'Remittance_Advice', A4_PORTRAIT)
            );

            $cashTotal = 0;
            $chequeTotal = 0;
            $cashCount = 0;
            $chequeCount = 0;

            if (! isEmptyArray($payments)) {
                $totalRemittance = 0;
                $transactions = $payments;

                $lineOffset = 15;
                foreach ($transactions as $payment) {
                    $totalRemittance = bcadd($totalRemittance, $payment['allocatedAmount'] * -1, 2);

                    $boxRemittance = new RemittanceAdvice(
                        $payment['allocatedDate'],
                        $payment['invoiceNumber'],
                        $payment['propertyName'],
                        $payment['description'],
                        $payment['allocatedAmount'] * -1,
                        $lineOffset
                    );
                    $receipt->attachObject('boxRemittance' . $lineOffset, $boxRemittance);
                    $lineOffset += 15;
                }

                $remittanceTotal = new RemittanceTotal($totalRemittance);
                $receipt->attachObject('remittanceTotal', $remittanceTotal);
            }

            $receipt->preparePage(0);
            $receipt->endPage();
        }

        // END EFT REMITTANCE ADVICE

        $receipt->close();


        dbMarkReceipt($batchNumber, $lineNumber, $bankID);
    }

    return $downloadPath;
}

class Receipt extends PDFReport
{
    public $pdf;

    public $pageCount;

    public $resourceList;

    public $lineOffset = 15;

    public $logoFile = false;

    public function __construct(&$dataSource, $logoFile = null)
    {
        parent::__construct($dataSource);
        if ($logoFile) {
            $this->logoFile = $logoFile;
        }
    }

    public function updateLineOffset($offset)
    {
        $this->lineOffset += $offset;
        if ($this->lineOffset >= 245) {
            $this->renderTotal('continued...');
            $this->endPage();
            $this->preparePage();
            $this->render();
        }
    }

    public function renderReceiptRow($date, $receiptNumber, $description, $netAmount, $gstAmount, $grossAmount)
    {
        $this->pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        $this->setFont($pdf, 'Helvetica-Bold', 8);

        $this->pdf->showBoxed($date, 20, 546 - $this->lineOffset, 50, 13, 'right', '');
        $this->pdf->showBoxed($receiptNumber, 85, 546 - $this->lineOffset, 45, 13, 'center', '');
        $this->pdf->showBoxed($description, 140, 546 - $this->lineOffset, 300, 13, 'left', '');
        $this->pdf->showBoxed($netAmount, 395, 546 - $this->lineOffset, 50, 13, 'right', '');
        $this->pdf->showBoxed($gstAmount, 455, 546 - $this->lineOffset, 50, 13, 'right', '');
        $this->pdf->showBoxed($grossAmount, 515, 546 - $this->lineOffset, 50, 13, 'right', '');

        $this->updateLineOffset(10);
    }

    public function renderTotal($amount)
    {
        $this->pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        $this->setFont($pdf, 'Helvetica-Bold', 8);

        if (is_numeric($amount)) {
            $this->pdf->showBoxed(toMoney($amount), 25, 285, 540, 13, 'right', '');
        } else {
            $this->pdf->showBoxed($amount, 25, 285, 540, 13, 'right', '');
        }
    }

    public function renderTitle($title)
    {
        $this->pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        $this->setFont($pdf, 'Helvetica-Bold', 8);
        $this->pdf->showBoxed($title, 30, 546 - $this->lineOffset, 400, 13, 'left', '');
        $this->updateLineOffset(20);
    }

    public function renderPageNumber()
    {
        $this->setFont($pdf, 'Helvetica-Bold', 8);
        $this->pdf->showBoxed("{$this->pageCount}", $this->pageWidth - 50, 20, 25, 13, 'right', '');
    }

    public function renderTracc()
    {
        if (TRACC_LOGO) {
            $maxWidth = 20;
            $maxHeight = 15;
            [$imageWidth, $imageHeight] = getimagesize(TRACC_LOGO);

            $horizontalScaling = ($imageWidth > $maxWidth) ? $maxWidth / $imageWidth : 1;
            $verticalScaling = ($imageHeight > $maxHeight) ? $maxHeight / $imageHeight : 1;
            $imageScale = ($horizontalScaling < $verticalScaling) ? $horizontalScaling : $verticalScaling;


            $vMargin = 10;
            $hMargin = 25;

            if (! $this->resourceList['traccLogo']) {
                $this->resourceList['traccLogo'] = $this->pdf->load_image('auto', TRACC_LOGO, '');
            }

            $this->pdf->fit_image(
                $this->resourceList['traccLogo'],
                $hMargin,
                $vMargin + $maxHeight,
                "scale {$imageScale}"
            );
        }
    }

    public function renderLogo()
    {
        if ($this->logoFile && file_exists($this->logoFile)) {
            $maxWidth = LOGO_WIDTH;
            $maxHeight = LOGO_HEIGHT;
            [$imageWidth, $imageHeight] = getimagesize($this->logoFile);

            $horizontalScaling = ($imageWidth > $maxWidth) ? $maxWidth / $imageWidth : 1;
            $verticalScaling = ($imageHeight > $maxHeight) ? $maxHeight / $imageHeight : 1;
            $imageScale = ($horizontalScaling < $verticalScaling) ? $horizontalScaling : $verticalScaling;

            $imageScale = round($imageScale, 2);

            $vMargin = 25;
            $hMargin = 25;

            $hPos = $this->pageWidth - ($imageScale * $imageWidth) - $hMargin;
            $vPos = $this->pageHeight - ($imageScale * $imageHeight) - $vMargin;

            if (! $this->resourceList['logo']) {
                $this->resourceList['logo'] = $this->pdf->load_image('auto', $this->logoFile, '');
            }

            $this->pdf->fit_image($this->resourceList['logo'], $hPos, $vPos, "scale {$imageScale}");
        }
    }

    public function preparePage($printTemplate = true)
    {
        $this->lineOffset = 15;
        parent::preparePage();
        $this->render();
        $this->renderLogo();
    }

    public function close()
    {
        parent::close();
    }
}

class ReceiptHeader extends PDFobject
{
    public $batchNumber;

    public $debtorCode;

    public $lineNumber;

    public $receiptNumber;

    public $transactionDate;

    public $trustAccountDescription;

    public $bankState;

    public $completedDate;

    public $createuser;

    public $companyName;

    public $companyStreet;

    public $companyCity;

    public $companyState;

    public $companyPostCode;

    public $propertyID;

    private $leaseID;

    /**
     * <AUTHOR> Reyes
     *
     * @since 2013-01-22
     **/
    public function __construct($propertyID, $leaseID)
    {
        $this->propertyID = $propertyID;
        $this->leaseID = $leaseID;
    }

    /**
     * <AUTHOR> Reyes
     *
     * @since 2013-01-20
     **/
    public function preRender(&$pdf)
    {
        $pdf->setColorExt('both', 'rgb', 0.8, 0.8, 0.8, 0);
        $pdf->rect(45, 670, 100, 28);
        $pdf->fill();

        // Vertical Lines
        $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        $pdf->setlinewidth(0.5);
        $pdf->moveto(45, 699); // 729
        $pdf->lineto(45, 671);
        $pdf->stroke();
        $pdf->setlinewidth(0.5);
        $pdf->moveto(145, 699);
        $pdf->lineto(145, 671);
        $pdf->stroke();
        $pdf->setlinewidth(0.5);
        $pdf->moveto(245, 699);
        $pdf->lineto(245, 671);
        $pdf->stroke();
        // address
        $pdf->setlinewidth(0.5);
        $pdf->moveto(300, 727);
        $pdf->lineto(300, 624);
        $pdf->stroke();
        $pdf->setlinewidth(0.5);
        $pdf->moveto(550, 727);
        $pdf->lineto(550, 624);
        $pdf->stroke();
        // address
        // Horizontal Lines
        $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        $pdf->setlinewidth(0.5);
        $pdf->moveto(45, 699);
        $pdf->lineto(245, 699);
        $pdf->stroke();
        $pdf->setlinewidth(0.5);
        $pdf->moveto(45, 685);
        $pdf->lineto(245, 685);
        $pdf->stroke();
        $pdf->setlinewidth(0.5);
        $pdf->moveto(45, 671);
        $pdf->lineto(245, 671);
        $pdf->stroke();
        // adress
        $pdf->setlinewidth(0.5);
        $pdf->moveto(300, 727);
        $pdf->lineto(550, 727);
        $pdf->stroke();
        $pdf->setlinewidth(0.5);
        $pdf->moveto(300, 624);
        $pdf->lineto(550, 624);
        $pdf->stroke();
        // adress
    }

    /**
     * <AUTHOR> Reyes
     *
     * @since 2012-01-20
     **/
    public function render(&$pdf)
    {
        $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        // Label
        $this->setFont($pdf, 'Helvetica-Bold', 14);
        $pdf->showBoxed(
            strtoupper($_SESSION['country_default']['trust_account']) . ' RECEIPT',
            45,
            755,
            300,
            20,
            'left',
            ''
        );
        // Receipt Basic Info
        $this->setFont($pdf, 'Helvetica', 12);
        $pdf->showBoxed('Receipt Number', 50, 680, 300, 20, 'left', '');
        $pdf->showBoxed('Receipt Date', 50, 666, 300, 20, 'left', '');
        $pdf->showBoxed($this->receiptNumber, 155, 680, 300, 20, 'left', '');
        $pdf->showBoxed($this->transactionDate, 155, 666, 300, 20, 'left', '');

        $pdf->showBoxed($this->trustAccountDescription, 45, 738, 500, 20, 'left', '');
        if (dbGetLicensee() != '') {
            // Licensee
            $pdf->setColorExt('both', 'rgb', 0.95, 0.95, 0.95, 0);
            $pdf->setlinewidth(0.5);
            $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
            $this->setFont($pdf, 'Helvetica', 10);
            $pdf->showBoxed('Licensee: ' . dbGetLicensee(), 45, 728, 600, 20, 'left', '');
        }

        // $getSate
        if (dbGetAgentLicenseNumber() != '') {
            $pdf->showBoxed('Agent Licence Number ' . dbGetAgentLicenseNumber(), 45, 710, 300, 20, 'left', '');
        }

        if ($this->bankState == 'QLD') {
            $pdf->showBoxed('Agents Financial Administration Act 2014', 45, 697, 300, 20, 'left', '');
        }

        // From
        $address = dbGetReceiptAddress($this->propertyID);
        $abn = $address['ABN'];
        $country = dbGetDefaultCountry();
        if (strlen($address['ABN'] && $country == 'AU') == 11) {
            $abn = $abn[0] . $abn[1] . ' ' . $abn[2] . $abn[3] . $abn[4] . ' ' . $abn[5] . $abn[6] . $abn[7] . ' ' . $abn[8] . $abn[9] . $abn[10];
        }

        $this->setFont($pdf, 'Helvetica-Bold', 12);
        $tradingName = dbGetClientTradingName();
        $pdf->showBoxed(
            "{$address['companyName']}\n",
            307,
            $tradingName ? 704 : 687,
            242,
            $tradingName ? 23 : 40,
            'left',
            ''
        );

        $ypos = 620;

        $business_label = $_SESSION['country_default']['business_label'];
        $business_prefix = ($abn && $abn != '') ? $_SESSION['country_default']['business_prefix'] : '';
        $business_number = $business_prefix . $abn;

        $this->setFont($pdf, 'Helvetica', 10);
        $pdf->showBoxed("\n" . $tradingName, 307, $ypos, 235, 103, 'left', '');
        $pdf->showBoxed("\n\n\n{$business_label} {$business_number}", 307, $ypos, 235, 103, 'left', '');

        $ypos -= 2;

        $this->setFont($pdf, 'Helvetica', 10);
        $pdf->showBoxed(
            "\n\n\n\n{$address['street']}\n{$address['city']} {$address['state']} {$address['postCode']}" . ($address['phone'] ? "\nPhone:  " : '') . ($address['fax'] ? "\nFax:  " : '') . ($address['email'] ? "\nEmail:  " : ''),
            307,
            $ypos,
            235,
            103,
            'left',
            ''
        );
        $pdf->showBoxed(
            "\n\n\n\n\n\n" . ($address['phone'] ? "{$address['phone']}\n" : '') . ($address['fax'] ? "{$address['fax']}\n" : '') . ($address['email'] ? "{$address['email']}" : ''),
            355,
            $ypos,
            235,
            103,
            'left',
            ''
        );
        // Postal Address
        $this->setFont($pdf, 'Helvetica', 9);
        if (dbCheckReceiptMultipleLeases($this->batchNumber, $this->lineNumber)) {
            extract(dbReceiptAddress($this->batchNumber, $this->lineNumber, $this->propertyID, $this->leaseID));
            $pdf->showBoxed(
                "{$companyName}\n{$companyStreet}\n{$companyCity} {$companyState}, {$companyPostCode}",
                50,
                614,
                232,
                42,
                'left',
                ''
            );
        } elseif ($this->companyStreet) {
            $pdf->showBoxed(
                "$this->companyName\n$this->companyStreet\n$this->companyCity $this->companyState, $this->companyPostCode",
                50,
                614,
                232,
                42,
                'left',
                ''
            );
        }
    }
}

class receiptLines extends PDFobject
{
    /**
     * <AUTHOR> Reyes
     *
     * @since 2013-01-21
     **/
    public function preRender(&$pdf)
    {
        $pdf->setColorExt('both', 'rgb', 0.8, 0.8, 0.8, 0);
        $pdf->rect(30, 580, 535, 20);
        $pdf->rect(90, 580, 100, 20);
        $pdf->fill();

        $pdf->rect(30, 160, 450, 20); // 30 60 450 20  // formerly 260 now 160
        $pdf->fill();

        // Horizontal Lines
        $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        $pdf->setlinewidth(0.5);

        $pdf->moveto(30, 160);
        $pdf->lineto(565, 160);
        $pdf->stroke();

        $pdf->setlinewidth(0.5);
        $pdf->moveto(30, 180);
        $pdf->lineto(565, 180);
        $pdf->stroke();

        $pdf->setlinewidth(0.5);
        $pdf->moveto(30, 600);
        $pdf->lineto(565, 600);
        $pdf->stroke();
        $pdf->setlinewidth(0.5);
        $pdf->moveto(30, 580);
        $pdf->lineto(565, 580);
        $pdf->stroke();
        // Vertical Lines
        $pdf->setlinewidth(0.5);
        $pdf->moveto(30, 160);
        $pdf->lineto(30, 600);
        $pdf->stroke();
        $pdf->moveto(340, 180); // old value 280
        $pdf->lineto(340, 600);
        $pdf->stroke();
        $pdf->moveto(405, 180); // old value 280
        $pdf->lineto(405, 600);
        $pdf->stroke();

        $pdf->moveto(480, 160);
        $pdf->lineto(480, 600);
        $pdf->stroke();

        $pdf->moveto(565, 160);
        $pdf->lineto(565, 600); // x y
        $pdf->stroke();
    }

    /**
     * <AUTHOR> Reyes
     *
     * @since 2013-01-21
     **/
    public function render(&$pdf)
    {
        $this->setFont($pdf, 'Helvetica-Bold', 10);
        $pdf->showBoxed('DESCRIPTION', 34, 575, 100, 20, 'left', '');
        $pdf->showBoxed('FROM', 350, 575, 86, 20, 'left', '');
        $pdf->showBoxed('TO', 420, 575, 246, 20, 'left', '');
        $pdf->showBoxed('AMOUNT', 454, 575, 105, 20, 'right', '');
        $pdf->showBoxed('RECEIPT TOTAL', 224, 155, 243, 20, 'right', '');
    }
}

/**
 * <AUTHOR> Reyes
 *
 * @since 2013-01-21
 **/
class receiptBody extends PDFObject
{
    public $show_boxed1;

    public $show_boxed2;

    public $show_boxed3;

    public $show_boxed4;

    public $show_boxed5;

    public $show_boxed6;

    public $show_boxed7;

    public $font;

    public $size;

    /**
     * <AUTHOR> Reyes
     *
     * @since 2013-01-22
     **/
    public function __construct(
        $show_boxed1,
        $show_boxed2,
        $show_boxed3,
        $show_boxed4,
        $show_boxed5,
        $show_boxed6,
        $show_boxed7 = null,
        $font = 'Helvetica',
        $size = 8
    ) {
        $this->show_boxed1 = $show_boxed1;
        $this->show_boxed2 = $show_boxed2;
        $this->show_boxed3 = $show_boxed3;
        $this->show_boxed4 = $show_boxed4;
        $this->show_boxed5 = $show_boxed5;
        $this->show_boxed6 = $show_boxed6;
        $this->show_boxed7 = $show_boxed7;
        $this->font = $font;
        $this->size = $size;
    }

    /**
     * <AUTHOR> Reyes
     *
     * @since 2013-01-21
     **/
    public function render(&$pdf)
    {
        $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        $this->setFont($pdf, $this->font, $this->size);
        $pdf->showBoxed(
            $this->show_boxed1,
            $this->show_boxed2,
            $this->show_boxed3,
            $this->show_boxed4,
            $this->show_boxed5,
            $this->show_boxed6,
            $this->show_boxed7
        );
    }
}

class receiptFooter extends PDFobject
{
    private $paymentType;

    public $drawerName;

    public $completedDate;

    public $username;

    public $bankDate;

    /**
     * <AUTHOR> Reyes
     *
     * @since 2013-01-20
     **/
    public function __construct($paymentType, $completedDate, $username, $bankDate)
    {
        $this->paymentType = $paymentType;
        $this->completedDate = $completedDate;
        $this->username = $username;
        $this->bankDate = $bankDate;
    }

    /**
     * <AUTHOR> Reyes
     *
     * @since 2013-01-19
     **/
    public function preRender(&$pdf)
    {
        /** Confidentiality **/
        $pdf->setColorExt('both', 'rgb', 0.8, 0.8, 0.8, 0);
        $pdf->rect(305, 130, 260, 20);
        $pdf->fill();
        $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        $pdf->setlinewidth(0.5);
        $pdf->moveto(305, 150);
        $pdf->lineto(305, 50);
        $pdf->stroke();
        $pdf->setlinewidth(0.5);
        $pdf->moveto(565, 150);
        $pdf->lineto(565, 50);
        $pdf->stroke();
        $pdf->setlinewidth(0.5);
        $pdf->moveto(565, 150);
        $pdf->lineto(305, 150);
        $pdf->stroke();
        $pdf->setlinewidth(0.5);
        $pdf->moveto(565, 130);
        $pdf->lineto(305, 130);
        $pdf->stroke();
        $pdf->setlinewidth(0.5);
        $pdf->moveto(565, 50);
        $pdf->lineto(305, 50);
        $pdf->stroke();
        $this->setFont($pdf, 'Helvetica-Bold', 10);
        $pdf->showBoxed('CONFIDENTIALITY', 309, 126, 256, 20, 'left', '');
        $this->setFont($pdf, 'Helvetica', 8);
        $pdf->showBoxed(
            "The information contained in this document is confidential and is intended for the exclusive use of the addressee named above. In certain cases it is also legally privileged.\n\nIf you are not the addressee, any disclosure, reproduction, distribution, or any other dissemination or use of this communication is strictly prohibited. If you have received this transmission in error please contact us immediately so that we can arrange for its return.",
            309,
            54,
            252,
            72,
            'left',
            ''
        );
        /** Fold Notch **/
        $pdf->setColorExt('both', 'rgb', 0.8, 0.8, 0.8, 0);
        $pdf->setlinewidth(1);
        $pdf->moveto(0, 566);
        $pdf->lineto(2, 566);
        $pdf->stroke();
        $pdf->moveto(593, 566);
        $pdf->lineto(595, 566);
        $pdf->stroke();
    }

    /**
     * <AUTHOR> Reyes
     *
     * @since 2013-01-19
     **/
    public function render(&$pdf)
    {
        $pdf->setColorExt('both', 'rgb', 0.8, 0.8, 0.8, 0);
        $pdf->rect(30, 130, 265, 20);
        $pdf->fill();
        $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        $this->setFont($pdf, 'Helvetica-Bold', 10);

        if ($this->drawerName) {
            $pdf->showBoxed('PAYMENT DETAILS', 34, 126, 261, 20, 'left', '');
            $this->setFont($pdf, 'Helvetica-Bold', 8);
            $pdf->showBoxed('Payment Method:', 34, 100, 80, 19, 'left', '');
            if ($this->drawerName) {
                $pdf->showBoxed('Drawer:', 34, 90, 80, 19, 'left', '');
            }

            $pdf->showBoxed('Completed by:', 34, 80, 300, 19, 'left', '');
            $pdf->showBoxed('Date Completed:', 34, 70, 80, 19, 'left', '');
            if ($this->bankDate) {
                $pdf->showBoxed('@Bank:', 34, 60, 80, 19, 'left', '');
            }

            $pdf->showBoxed('Signature:', 34, $this->bankDate ? 45 : 52, 80, 19, 'left', '');
            $this->setFont($pdf, 'Helvetica-Bold', 8);
            $pdf->showBoxed($this->paymentType, 120, 100, 80, 19, 'left', '');
            if ($this->drawerName) {
                $pdf->showBoxed($this->drawerName, 120, 90, 180, 19, 'left', '');
            }

            $name = dbGetUserByUserName($this->username);
            $username = $name['fullName'];
            $pdf->showBoxed($username, 120, 80, 300, 20, 'left', '');
            $pdf->showBoxed($this->completedDate, 120, 70, 300, 20, 'left', '');
            if ($this->bankDate) {
                $pdf->showBoxed($this->bankDate, 120, 60, 300, 20, 'left', '');
            }
        } else {
            $pdf->showBoxed('PAYMENT DETAILS', 34, 126, 261, 20, 'left', '');
            $this->setFont($pdf, 'Helvetica-Bold', 8);
            $pdf->showBoxed('Payment Method:', 34, 100, 80, 19, 'left', '');
            $pdf->showBoxed('Completed by:', 34, 90, 300, 19, 'left', '');
            $pdf->showBoxed('Date Completed:', 34, 80, 80, 19, 'left', '');
            if ($this->bankDate) {
                $pdf->showBoxed('@Bank:', 34, 70, 80, 19, 'left', '');
            }

            $pdf->showBoxed('Signature:', 34, 52, 80, 19, 'left', '');
            $this->setFont($pdf, 'Helvetica-Bold', 8);
            $pdf->showBoxed($this->paymentType, 120, 100, 80, 19, 'left', '');
            $name = dbGetUserByUserName($this->username);
            $username = $name['fullName'];
            $pdf->showBoxed($username, 120, 90, 300, 20, 'left', '');
            $pdf->showBoxed($this->completedDate, 120, 80, 300, 20, 'left', '');
            if ($this->bankDate) {
                $pdf->showBoxed($this->bankDate, 120, 70, 300, 20, 'left', '');
            }
        }

        $pdf->setColorExt('both', 'rgb', 0.8, 0.8, 0.8, 0);
        $pdf->setlinewidth(0.5);
        $pdf->moveto(290, $this->bankDate && $this->drawerName ? 57 : 62);
        $pdf->lineto(84, $this->bankDate && $this->drawerName ? 57 : 62);
        $pdf->stroke();
        $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        // Lines
        $pdf->setlinewidth(0.5);
        $pdf->moveto(295, 150);
        $pdf->lineto(295, 50);
        $pdf->stroke();
        $pdf->setlinewidth(0.5);
        $pdf->moveto(30, 150);
        $pdf->lineto(30, 50);
        $pdf->stroke();
        $pdf->setlinewidth(0.5);
        $pdf->moveto(295, 150);
        $pdf->lineto(30, 150);
        $pdf->stroke();
        $pdf->setlinewidth(0.5);
        $pdf->moveto(295, 130);
        $pdf->lineto(30, 130);
        $pdf->stroke();
        $pdf->setlinewidth(0.5);
        $pdf->moveto(295, 50);
        $pdf->lineto(30, 50);
        $pdf->stroke();
    }
}


class MailAddressWindow extends PDFobject
{
    public $attention;

    public $name;

    public $street;

    public $city;

    public $state;

    public $postcode;

    public function __construct($companyID)
    {
        $mailAddress = dbGetCompany($companyID);
        $this->name = $mailAddress['companyName'];
        $this->street = $mailAddress['address'];
        $this->city = $mailAddress['city'];
        $this->state = $mailAddress['state'];
        $this->postcode = $mailAddress['postCode'];
    }

    public function render(&$pdf)
    {
        $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        $this->setFont($pdf, 'Helvetica', 10); // This is for the disclaimer information...
        $address = (($this->attention) ? "{$this->attention}\n" : '') . "{$this->name}\n{$this->street}\n{$this->city} {$this->state},  {$this->postcode}";
        $pdf->showBoxed($address, 95, 622, 232, 46, 'left', ''); // This is for the from field
    }
}

class RemittanceFooter extends PDFobject
{
    public $accountName;

    public $accountNumber;

    public $bsb;

    public $bank;

    public bool $displayBsb;

    public string $bsbLabel;

    public $paymentType; // 2012-10-03: Added to include BPay option [Morph]

    public $crn; // 2012-10-03: Added to include BPay option [Morph]

    public $billerCode; // 2012-10-03: Added to include BPay option [Morph]

    public function __construct($companyID)
    {
        $c = dbGetCompany($companyID);
        $this->accountName = $c['bankAccountName'];
        $this->accountNumber = $c['bankAccountNumber'];
        $this->bsb = $c['bsbNumber'];
        $this->bank = $c['bankName'];
        $this->billerCode = $c['bpayBillerCode'];
        $this->displayBsb = getDisplayBsbFromSession();
        $this->bsbLabel = getBsbLabelFromSession();
        if (empty($this->paymentType)) {
            $this->paymentType = PAY_EFT;
        }
    }

    public function render(&$pdf)
    {
        $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        $this->setFont($pdf, 'Helvetica', 8); // This is for the disclaimer information...
        if ($this->paymentType == PAY_BPAY) {
            $pdf->showBoxed($this->crn, 119, 97, 172, 19, 'left', '');
            $pdf->showBoxed($this->billerCode, 119, 82, 172, 19, 'left', '');
        }

        if ($this->paymentType !== PAY_BPAY) {
            $pdf->showBoxed($this->accountName, 119, 97, 172, 19, 'left', '');
            $pdf->showBoxed('*****' . substr($this->accountNumber, -4, 4), 119, 82, 172, 19, 'left', '');
            $pdf->showBoxed($this->bank, 119, 52, 172, 19, 'left', '');
        }

        if ($this->paymentType !== PAY_BPAY && $this->displayBsb) {
            $pdf->showBoxed(
                formatWithDelimiter($this->bsb),
                119,
                67,
                172,
                19,
                'left',
                ''
            );
        }

        $pdf->setColorExt('both', 'rgb', 0.9, 0.9, 0.9, 0); // Setting the Color to Gray
        $pdf->rect(30, 145, 260, 15);
        $pdf->rect(300, 145, 270, 15);
        $pdf->fill();

        $pdf->setlinewidth(2);
        $pdf->setColorExt('both', 'rgb', 0.75, 0.75, 0.75, 0);
        $pdf->rect(30, 40, 260, 120);
        $pdf->rect(300, 40, 270, 120);
        $pdf->stroke();

        $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        $this->setFont($pdf, 'Helvetica-Bold', 8);

        $pdf->showBoxed('PAYMENT ACCOUNT DETAILS', 40, 144, 150, 13, 'left', '');
        $pdf->showBoxed('CONFIDENTIALITY', 310, 144, 150, 13, 'left', '');


        if ($this->paymentType == PAY_BPAY) {
            $pdf->showBoxed('CRN', 40, 97, 80, 19, 'left', '');
            $pdf->showBoxed('Biller Code', 40, 82, 80, 19, 'left', '');
        }

        if ($this->paymentType !== PAY_BPAY) {
            $pdf->showBoxed('Account Name', 40, 97, 80, 19, 'left', '');
            $pdf->showBoxed('Account Number', 40, 82, 80, 19, 'left', '');
        }

        if ($this->paymentType !== PAY_BPAY && $this->displayBsb) {
            $pdf->showBoxed($this->bsbLabel, 40, 67, 80, 19, 'left', '');
        }

        if ($this->paymentType !== PAY_BPAY && $this->bank) {
            $pdf->showBoxed('Bank', 40, 52, 80, 19, 'left', '');
        }

        $this->setFont($pdf, 'Helvetica', 8);

        $pdf->showBoxed(
            "The information contained in this document is confidential and is intended for the exclusive use of the addressee named above. In certain cases, it is also legally privileged.\n\nIf you are not the addressee, any disclosure, reproduction, distribution, or any other dissemination or use of this communication is strictly prohibited. If you have received this transmission in error please contact us immediately so that we can arrange for its return.",
            315,
            55,
            252,
            72,
            'left',
            ''
        ); // Disclaimer

    }
}

class RemittanceHeader extends PDFobject
{
    public $address1;

    public $address2;

    public $companyName;

    public $address;

    public $phone;

    public $fax;

    public $reference;

    public $date;

    public $paymentType;

    public $_name = 'remittanceHeader';

    public function __construct($reference, $date)
    {
        $company = dbGetPrimaryAgent();
        $this->companyName = $company['companyName'];
        $this->address1 = $company['address'];
        $this->address2 = $company['city'] . ' ' . $company['state'] . ' ' . $company['postCode'];
        $this->phone = $company['phone'];
        $this->fax = $company['fax'];
        $this->date = $date;
        $this->reference = $reference;
        if (empty($this->paymentType)) {
            $this->paymentType = PAY_EFT;
        }
    }

    public function render(&$pdf)
    {
        $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        $this->setFont($pdf, 'Helvetica-Bold', 8);

        $pdf->showBoxed($this->companyName, 335, 702, 215, 13, 'left', '');
        $pdf->showBoxed($this->address1, 335, 692, 215, 13, 'left', '');
        $pdf->showBoxed($this->address2, 335, 682, 215, 13, 'left', '');
        if ($this->phone) {
            $pdf->showBoxed('P: ' . $this->phone, 335, 662, 215, 13, 'left', '');
        }

        if ($this->fax) {
            $pdf->showBoxed('F: ' . $this->fax, 335, 652, 215, 13, 'left', '');
        }


        $pdf->showBoxed($this->reference, 460, 615, 100, 13, 'right', '');
        $pdf->showBoxed($this->date, 460, 590, 100, 13, 'right', '');

        $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        $this->setFont($pdf, 'Helvetica', 14);
        if ($this->paymentType == PAY_BPAY) {
            $pdf->showBoxed('BPAY REMITTANCE ADVICE', 20, 785, 250, 25, 'left', '');
        } else {
            $pdf->showBoxed('EFT REMITTANCE ADVICE', 20, 785, 250, 25, 'left', '');
        }
    }
}


class RemittanceLines extends PDFobject
{
    public $title;

    public function __construct() {}

    public function render(&$pdf)
    {
        $pdf->setColorExt('both', 'rgb', 0.9, 0.9, 0.9, 0); // Setting the Color to Gray
        $pdf->rect(25, 546, 545, 15);
        $pdf->fill();
        $pdf->rect(25, 285, 410, 15);
        $pdf->fill();
        $pdf->setlinewidth(0.5);

        $pdf->setColorExt('both', 'rgb', 0.75, 0.75, 0.75, 0);
        $pdf->moveto(25, 546);
        $pdf->lineto(570, 546);
        $pdf->stroke();
        $pdf->moveto(25, 300);
        $pdf->lineto(570, 300);
        $pdf->stroke();


        $pdf->setlinewidth(2);
        $pdf->rect(25, 285, 545, 276);
        $pdf->stroke();
        $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        // Text

        $this->setFont($pdf, 'Helvetica-Bold', 9);
        $pdf->showBoxed('  DATE', 25, 546, 45, 13, 'center', '');
        $pdf->showBoxed('PROPERTY', 165, 546, 290, 13, 'left', '');
        $pdf->showBoxed('DESCRIPTION', 315, 546, 100, 13, 'left', '');
        $pdf->showBoxed('AMOUNT', 520, 546, 45, 13, 'right', '');

        $this->setFont($pdf, 'Helvetica', 8);

        // -- main box
        $pdf->setColorExt('both', 'rgb', 0.9, 0.9, 0.9, 0); // Setting the Color to Gray
        $pdf->rect(320, 722, 250, 15);
        $pdf->fill();
        $pdf->setlinewidth(0.5);

        $pdf->setColorExt('both', 'rgb', 0.75, 0.75, 0.75, 0);
        $pdf->moveto(320, 638);
        $pdf->lineto(570, 638);
        $pdf->stroke();


        $pdf->setlinewidth(2);
        $pdf->setColorExt('both', 'rgb', 0.75, 0.75, 0.75, 0);
        $pdf->rect(320, 637, 250, 100);
        $pdf->rect(320, 612, 250, 20);
        $pdf->rect(320, 587, 250, 20);
        $pdf->stroke();

        $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        $this->setFont($pdf, 'Helvetica-Bold', 8);


        $pdf->showBoxed('From', 335, 720, 50, 13, 'left', '');
        $pdf->showBoxed('Reference Number', 335, 615, 100, 13, 'left', '');
        $pdf->showBoxed('Payment Date', 335, 590, 100, 13, 'left', '');
    }
}

class RemittanceAdvice extends PDFobject
{
    public $date;

    public $invoiceNumber;

    public $payee;

    public $description;

    public $amount;

    public $lineOffset;

    public $logoFile = false;

    public function __construct($date, $invoiceNumber, $payee, $description, $amount, $lineOffset)
    {
        $this->date = $date;
        $this->invoiceNumber = $invoiceNumber;
        $this->payee = $payee;
        $this->description = $description;
        $this->amount = $amount;
        $this->lineOffset = $lineOffset;
    }

    public function render(&$pdf)
    {
        $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        $this->setFont($pdf, 'Helvetica-Bold', 8);

        $pdf->showBoxed($this->date, 25, 540 - $this->lineOffset, 50, 13, 'right', '');
        $pdf->showBoxed($this->invoiceNumber, 70, 540 - $this->lineOffset, 85, 13, 'right', '');
        $pdf->showBoxed($this->payee, 165, 540 - $this->lineOffset, 150, 13, 'left', '');
        $pdf->showBoxed($this->description, 315, 540 - $this->lineOffset, 200, 13, 'left', '');
        $pdf->showBoxed(
            $this->amount != '' ? toMoney($this->amount) : '',
            470,
            540 - $this->lineOffset,
            90,
            13,
            'right',
            ''
        );
    }
}

class RemittanceTotal extends PDFobject
{
    public $total;

    public $lineOffset = 15;

    public $logoFile = false;

    public function __construct($total)
    {
        $this->total = $total;
    }

    public function render(&$pdf)
    {
        $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        $this->setFont($pdf, 'Helvetica-Bold', 8);
        $pdf->showBoxed(
            'Please allow 3 working days for this payment to be processed',
            35,
            278,
            243,
            20,
            'left',
            ''
        ); // Total
        $pdf->showBoxed('TOTAL', 330, 285, 100, 13, 'right', ''); // Total
        if (is_numeric($this->total)) {
            $pdf->showBoxed(toMoney($this->total), 25, 285, 540, 13, 'right', '');
        } else {
            $pdf->showBoxed($this->total, 25, 285, 540, 13, 'right', '');
        }
    }
}

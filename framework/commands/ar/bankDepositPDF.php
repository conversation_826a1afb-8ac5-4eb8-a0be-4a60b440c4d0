<?php


function prepareBankDepositSlip($bankID, $transactionDate, $receipts = null)
{

    global $clientDirectory, $pathPrefix;

    // -- initialise the variables that will store the transaction amounts


    // -- if there are transactions to be printed :
    if ($receipts) {

        [$day, $month, $year] = explode('/', $transactionDate);        // -- will need to store current DB index in session to allow for translation of logos

        $logoFile = dbGetClientLogo();
        $logoPath = "assets/clientLogos/{$logoFile}";
        // $logoPath = null;

        $time = time();

        $filename = "bank_deposit_slip_{$bankID}_{$year}{$month}{$day}_{$time}.pdf";
        $filePath = "{$pathPrefix}{$clientDirectory}/pdf/BankDepositSlip/{$filename}";
        $downloadPath = "{$clientDirectory}/pdf/BankDepositSlip/{$filename}";

        if (file_exists($filePath)) {
            @unlink($filePath);
        }

        $bankDepositSlip = new BankDepositSlip($filePath, $logoPath);



        // -- some data is shared across several components in the PDF, so instead of doing multiple db calls and binding locally within the object,
        // -- a single DB call is made and data bound to the object externally

        // -- PDFobjects are usually groups of data repeated statically on every page of the PDF, and as such are loaded once into the PDF and called at render...
        // -- mutating values are accessed from the Invoice object itself such as changing totals, invoice rows, sub titles within the statement

        // -- note that data is bound between array values to the object attributes sharing the same name as the array key - ie
        // -- $this->test = $array['test']

        $bankData = dbFetchBankDetails($bankID);
        // $country = $bankData['bankCountry'];
        $country = getClientCountry();
        $header = new BankDepositHeader($transactionDate);
        if ($bankData) {
            $header->bindAttributesFrom($bankData);
        }

        $bankDepositSlip->attachObject('header', $header);

        $lines = new BankDepositLines($country);
        $bankDepositSlip->attachObject('lines', $lines);



        $bankDepositSlip->attachObject('traccFooter', new TraccFooter('assets/clientLogos/tracc_logo.jpg', 'Bank_Deposit_Slip', A4_LANDSCAPE));


        $bankDepositSlip->preparePage();



        $cashTotal = 0;
        $chequeTotal = 0;
        $cashCount = 0;
        $chequeCount = 0;
        // total for directdeposits
        $ddTotal = 0;
        $ddCount = 0;
        // total for bPay
        $bPayTotal = 0;
        $bPayCount = 0;
        // total for direct debits
        $directDebTotal = 0;
        $directDebCount = 0;
        // directdepo and cheque are cheques
        if (! isEmptyArray($receipts)) {

            $currentTotal = 0;
            $count = 0;

            // print_r ($receipts);
            foreach ($receipts as $receipt) {

                $count++;

                $receipt['transactionAmount'] = round($receipt['transactionAmount'], 2);

                if (($receipt['paymentType'] == 'Q') || ($receipt['paymentType'] == TYPE_CHEQUE)) {
                    $chequeTotal = bcadd($chequeTotal, $receipt['transactionAmount'], 2);
                    $chequeCount++;
                }

                // $receipt['paymentType'] == 'K') || it was C (but C is not Cash) before 2012-06-21
                if (($receipt['paymentType'] == TYPE_CASH)  || $receipt['paymentType'] == 'K') {
                    $cashTotal = bcadd($cashTotal, $receipt['transactionAmount'], 2);
                    $cashCount++;
                }

                // eft is directdeposit
                if (($receipt['paymentType'] == TYPE_DIRECTDEPOSIT) || ($receipt['paymentType'] == 'C')) {
                    $ddTotal = bcadd($ddTotal, $receipt['transactionAmount'], 2);
                    $ddCount++;
                }

                if (($receipt['paymentType'] == TYPE_BPAY) || ($receipt['paymentType'] == 'B')) {
                    $bPayTotal = bcadd($bPayTotal, $receipt['transactionAmount'], 2);
                    $bPayCount++;
                }

                if (($receipt['paymentType'] == TYPE_DIRECTDEBIT) || ($receipt['paymentType'] == 'D')) {
                    $directDebTotal = bcadd($directDebTotal, $receipt['transactionAmount'], 2);
                    $directDebCount++;
                }


                $currentTotal = bcadd($currentTotal, $receipt['transactionAmount'], 2);

                if ($receipt['propertyID'] == '') {
                    $receipt['propertyID'] = 'Multiple Properties';
                }

                if ($receipt['leaseID'] == '') {
                    $receipt['leaseID'] = 'Multiple Leases';
                }

                // if payment type, by jpalala 2012-06-19 - direct deposits are appearing as cheque deposit.
                // fetch bank of cheque
                if ($receipt['paymentType'] == 'Q' && empty($receipt['bankID'])) {
                    $receipt['bankID'] = '-';
                }

                // do not show cash in the rows
                if ($receipt['paymentType'] != 'K' && $receipt['paymentType'] != TYPE_CASH) {
                    $bankDepositSlip->renderRow($receipt['chequeNumber'], stripslashes($receipt['drawerName']), $receipt['bankID'], $receipt['bsbNumber'], $receipt['propertyID'] . ' - ' . $receipt['leaseID'], $receipt['transactionAmount'], $country);
                }
            }

            $bankDepositSlip->renderTotalLine();

            if ($chequeTotal > 0) {
                $bankDepositSlip->renderTotal("{$chequeCount} cheque(s)", 'Cheque Total', $chequeTotal);
            }

            if ($cashTotal > 0) {
                $bankDepositSlip->renderTotal('', 'Cash Total', $cashTotal);
            }

            // direct depo
            if ($ddTotal > 0) {
                $bankDepositSlip->renderTotal("{$ddCount} direct deposits(s)", 'Direct Deposit Total', $ddTotal);
            }

            // bpay
            if ($bPayTotal > 0) {
                $bankDepositSlip->renderTotal("{$bPayCount} BPAY(s)", 'BPAY Total', $bPayTotal);
            }

            // direct debit
            if ($directDebtTotal > 0) {
                $bankDepositSlip->renderTotal("{$directDebCount} Direct Debit(s)", 'Direct Debit Total', $directDebTotal);
            }

            // total
            if ($currentTotal > 0) {
                $bankDepositSlip->renderTotal('', 'Deposit Total', $currentTotal);
            }

        }

        $bankDepositSlip->renderSignature();

        $bankDepositSlip->close();
        unset($bankDepositSlip);
        $paths['filePath'] = $filePath;
        $paths['downloadPath'] = $downloadPath;

        return $paths;

    }




}



/*************************************************************************************************************************/

// INVOICE OBJECTS - repeating groups that appear in each invoice


class BankDepositHeader extends PDFobject
{
    public $transactionDate;

    public $bankName;

    public $bsbNumber;

    public $accountID;

    public $bankAccountName;

    public $bankCountry;

    public $_name = 'bankDepositHeader';

    public $client_country;

    public function __construct($transactionDate)
    {
        $this->transactionDate = $transactionDate;
        $this->client_country = getClientCountry();
    }

    public function preRender(&$pdf)
    {
        $pdf->setColorExt('both', 'rgb', 0.95, 0.95, 0.95, 0); // Setting the Color to Gray
        $pdf->rect(0, 515, 842, 80); // Top Grey Box
        $pdf->fill();

        // -- main box
        $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0); // Setting the Color to Black
        $this->setFont($pdf, 'Helvetica-Bold', 14);
        $pdf->showBoxed('Bank Deposit Slip', 20, 515, 842, 50, 'left', '');
        $this->setFont($pdf, 'Helvetica-Bold', 10);
        $pdf->showBoxed('Date:', 20, 515, 842, 14, 'left', '');
        $pdf->showBoxed('Account Name: ', 400, 515, 842, 45, 'left', '');

        $addend = 0;
        if (cdf_isShown('display_bsb', $this->client_country)) {
            $bsb_label = cdf_displayLabel('bsb_label', $this->client_country);
            $pdf->showBoxed($bsb_label . ':', 400, 515, 842, 31, 'left', '');
        } else {
            $addend = 14;
        }

        $pdf->showBoxed('Account no:', 400, 515, 842, (17 + $addend), 'left', '');
    }

    public function render(&$pdf)
    {

        $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0); // Setting the Color to Black
        $this->setFont($pdf, 'Helvetica', 14);
        $pdf->showBoxed($this->bankName, 20, 515, 842, 32, 'left', '');
        $this->setFont($pdf, 'Helvetica', 10);
        $pdf->showBoxed($this->transactionDate, 50, 515, 842, 14, 'left', '');
        $pdf->showBoxed($this->bankAccountName, 500, 515, 842, 45, 'left', '');

        $addend = 0;
        if (cdf_isShown('display_bsb', $this->client_country)) {
            $bsbFormat = cdf_displayLabel('bsb_format', $this->client_country);
            $bsbDelimiter = $bsbFormat['delimiter'];
            $bsbFrequency = $bsbFormat['delimiter_frequency'];
            $pdf->showBoxed(
                formatWithDelimiter($this->bsbNumber, $bsbDelimiter, $bsbFrequency),
                500,
                515,
                842,
                31,
                'left',
                ''
            );
        } else {
            $addend = 14;
        }

        $pdf->showBoxed(textSpace($this->accountID), 500, 515, 842, (17 + $addend), 'left', '');
    }
}






class BankDepositLines extends PDFobject
{
    public $printedOn;

    public $country;

    public function __construct()
    {
        // $this->country = $country;
        $this->country = getClientCountry();
        // $this->printedOn = "Generated on: " . date("d/m/Y") . " at " . date("g:i A");
    }

    public function preRender(&$pdf)
    {
        $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        $pdf->rect(0, 30, 842, 15); // Bottom Grey Box
        $pdf->fill();
        $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0); // Setting the Color to Gray
        $pdf->rect(0, 500, 842, 15); // Collumn Heading Grey Box
        $pdf->fill();
        $pdf->setColorExt('both', 'rgb', 1, 1, 1, 0); // added this in 6 Jul09 as headings were coming out black
        $this->setFont($pdf, 'Helvetica', 10);
        $pdf->showBoxed('Cheque', 5, 500, 80, 14, 'right', '');
        $pdf->showBoxed('Drawer', 100, 500, 75, 14, 'left', '');
        $pdf->showBoxed('Bank', 400, 500, 420, 14, 'left', '');
        if (cdf_isShown('display_bsb', $this->country)) {
            $bsb_label = cdf_displayLabel('bsb_label', $this->country);
            $pdf->showBoxed($bsb_label, 475, 500, 420, 14, 'left', '');
        }

        $pdf->showBoxed('Tenant', 600, 500, 420, 14, 'left', '');
        $pdf->showBoxed('Amount', 732, 500, 90, 14, 'right', '');
    }

    public function render(&$pdf)
    {

        $pdf->setColorExt('both', 'rgb', 1, 1, 1, 0);



        $this->setFont($pdf, 'Helvetica', 10);
        $pdf->showBoxed($this->printedOn, 4, 30, 834, 14, 'left', '');
        $this->setFont($pdf, 'Helvetica', 10);
        // if ($this->page) $pdf->showBoxed ("Page: {$this->page}", 4, 30, 834, 14, "right", "");

    }
}





function draw_grey_outline($offset)
{
    $temp_offset = $offset - 5;
    $pdf->setColorExt('both', 'rgb', 0.95, 0.95, 0.95, 0);
    if ($temp_offset % 20 == 0) {
        $pdf->setColorExt('both', 'rgb', 1, 1, 1, 0);
    }

    $pdf->rect(3, 503 - $offset, 836, 10); // Top Grey Box
    $pdf->fill();
}






/*************************************************************************************************************************/

class BankDepositSlip extends PDFReport
{
    public $pdf;

    public $lineOffset = 15;

    public $logoFile = false;

    public function __construct(&$dataSource, $logoFile = false)
    {
        parent::__construct($dataSource, 842, 595);
        if ($logoFile) {
            $this->logoFile = $logoFile;
        }
    }

    public function updateLineOffset($offset)
    {

        $this->lineOffset += $offset;
        if ($this->lineOffset >= 400) {
            // $this->renderTotal('','','continued...');
            $this->endPage();
            $this->preparePage();
            $this->render();
        }
    }

    public function renderRow($chequeNumber, $drawer, $bank, $bsb, $tenant, $amount, $country)
    {

        $this->pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        $this->setFont('Helvetica-Bold', 8);

        $this->pdf->showBoxed($chequeNumber, 5, 500 - $this->lineOffset, 80, 13, 'right', '');
        $this->pdf->showBoxed($drawer, 100, 500 - $this->lineOffset, 300, 13, 'left', '');
        $this->pdf->showBoxed($bank, 400, 500 - $this->lineOffset, 420, 13, 'left', '');
        if (cdf_isShown('display_bsb', $country)) {
            $bsbFormat = cdf_displayLabel('bsb_format', $country);
            $bsbDelimiter = $bsbFormat['delimiter'];
            $bsbFrequency = $bsbFormat['delimiter_frequency'];
            $this->pdf->showBoxed(
                formatWithDelimiter($bsb, $bsbDelimiter, $bsbFrequency),
                475,
                500 - $this->lineOffset,
                420,
                13,
                'left',
                ''
            );
        }

        $this->pdf->showBoxed($tenant, 600, 500 - $this->lineOffset, 420, 13, 'left', '');
        $this->pdf->showBoxed(toMoney($amount), 732, 500 - $this->lineOffset, 90, 13, 'right', '');

        $this->updateLineOffset(10);
    }

    public function renderTotal($beforeLine, $description, $amount)
    {
        $this->updateLineOffset(3);
        $this->pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        $this->setFont('Helvetica-Bold', 9);
        $this->pdf->showBoxed($beforeLine, 475, 500 - $this->lineOffset, 420, 14, 'left', '');
        $this->pdf->showBoxed($description, 600, 500 - $this->lineOffset, 420, 14, 'left', '');
        $this->pdf->showBoxed(toMoney($amount), 732, 500 - $this->lineOffset, 90, 14, 'right', '');
        $this->updateLineOffset(10);
    }

    public function renderTotalLine()
    {
        $this->updateLineOffset(3);
        $this->pdf->setColorExt('both', 'rgb', 0.9, 0.9, 0.9, 0); // Setting the Color to Black
        $this->pdf->setlinewidth(0.5);
        $this->pdf->moveto(0, 510 - $this->lineOffset); // 2nd Bottom Line (Horizontal)
        $this->pdf->lineto(842, 510 - $this->lineOffset);
        $this->pdf->stroke();
        $this->updateLineOffset(3);
    }

    public function renderSignature()
    {

        $this->pdf->setlinewidth(2);
        $this->pdf->setColorExt('both', 'rgb', 0.75, 0.75, 0.75, 0);
        $this->pdf->rect(10, 480 - $this->lineOffset, 822, 25); // Top Grey Box
        $this->pdf->stroke();



        $this->pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        $this->setFont('Helvetica-Bold', 8);


        $this->pdf->showBoxed('DEPOSITED BY:                                                                                SIGNATURE:                                                                              ', 0, 480 - $this->lineOffset, 842, 18, 'center', '');

    }

    public function renderLogo()
    {
        if ($this->logoFile) {

            $maxWidth = LOGO_WIDTH;
            $maxHeight = LOGO_HEIGHT;
            [$imageWidth, $imageHeight] = getimagesize($this->logoFile);

            $horizontalScaling = ($imageWidth > $maxWidth) ? $maxWidth / $imageWidth : 1;
            $verticalScaling = ($imageHeight > $maxHeight) ? $maxHeight / $imageHeight : 1;
            $imageScale = ($horizontalScaling < $verticalScaling) ? $horizontalScaling : $verticalScaling;

            $imageScale = round($imageScale, 2);

            $vMargin = 25;
            $hMargin = 25;

            $hPos = 595 - ($imageScale * $imageWidth) - $hMargin;
            $vPos = 842 - ($imageScale * $imageHeight) - $vMargin;

            $pdfimage = $this->pdf->load_image('auto', $this->logoFile, '');
            $this->pdf->fit_image($pdfimage, $hPos, $vPos, "scale {$imageScale}");
            $this->pdf->close_image($pdfimage);
        }
    }

    public function preparePage($printTemplate = true)
    {
        $this->lineOffset = 15;
        parent::preparePage();
        $this->render();
        $this->renderLogo();
    }

    public function close()
    {
        parent::close();
    }
}

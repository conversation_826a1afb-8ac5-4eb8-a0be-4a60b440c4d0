<?php

use enums\InvoiceStatementType;

include __DIR__ . '/invoicePDF.php';
require_once SYSTEMPATH . '/lib/fileuploader/class.fileuploader.php';

define('SINGLE_CHARGE', 1);
define('MULTIPLE_CHARGES', 2);
define('MULTIPLECHARGE_UNIT', 1);
define('MULTIPLECHARGE_PERCENTAGE', 2);
define('MULTIPLECHARGE_MANUAL', 3);
define('MULTIPLECHARGE_PROPERTY_SPLIT', 4);

function unsetCreditedInvoice(&$view)
{
    if (isset($view['toBatchNumber'])) {
        unset($view['toBatchNumber']);
    }

    if (isset($view['toLineNumber'])) {
        unset($view['toLineNumber']);
    }

    if (isset($view['invoiceNumber'])) {
        unset($view['invoiceNumber']);
    }
}

function get_transaction_period_days($var)
{
    $datetime1 = new DateTime(date('Y-m-d', strtotime(str_replace('/', '-', $var['toDate']))));
    $datetime2 = new DateTime(date('Y-m-d', strtotime(str_replace('/', '-', $var['fromDate']))));

    $diff = $datetime1->diff($datetime2);

    return $diff->format('%a') + 1;
}

function get_unit_from_to_diff($lease, $var)
{
    $x = 0;

    // echo ' --- '.$lease['unitToDate'] . ' --- ';
    // echo ' --- '.str_replace('/', '-', $lease['unitToDate']) . ' --- ';
    // echo ' --- '.strtotime(str_replace('/', '-', $lease['unitToDate'])) . ' --- ';


    $orig_unitFromDate = $lease['unitFromDate'];
    $lease['unitFromDate'] = new DateTime();
    [$d, $m, $y] = explode('-', str_replace('/', '-', $orig_unitFromDate));
    $lease['unitFromDate']->setDate($y, $m, $d);

    $orig_unitToDate = $lease['unitToDate'];
    $lease['unitToDate'] = new DateTime();
    [$d, $m, $y] = explode('-', str_replace('/', '-', $orig_unitToDate));
    $lease['unitToDate']->setDate($y, $m, $d);


    $orig_fromDate = $var['fromDate'];
    $var['fromDate'] = new DateTime();
    [$d, $m, $y] = explode('-', str_replace('/', '-', $orig_fromDate));
    $var['fromDate']->setDate($y, $m, $d);

    $orig_toDate = $var['toDate'];
    $var['toDate'] = new DateTime();
    [$d, $m, $y] = explode('-', str_replace('/', '-', $orig_toDate));
    $var['toDate']->setDate($y, $m, $d);

    if ($lease['unitToDate'] < $var['toDate'] && $lease['unitFromDate'] > $var['fromDate']) {
        $datetime1 = $lease['unitToDate'];
        $datetime2 = $lease['unitFromDate'];
        // $diff = $lease['unitToDate']-($var['fromDate'])+1;
        $x = 0;
    } elseif ($lease['unitToDate'] < $var['toDate']) {
        // $datetime1 = new DateTime(date('Y-m-d', strtotime(str_replace('/', '-', $lease['unitToDate']))));
        // $datetime2 = new DateTime(date('Y-m-d', strtotime(str_replace('/', '-', $var['fromDate']))));
        $datetime1 = $lease['unitToDate'];
        $datetime2 = $var['fromDate'];
        // $diff = $lease['unitToDate']-($var['fromDate'])+1;
        $x = 1;
    } elseif ($lease['unitFromDate'] > $var['fromDate']) {
        // $diff = $var['toDate']- $lease['unitFromDate'] +1;
        // $datetime1 = new DateTime(date('Y-m-d', strtotime(str_replace('/', '-', $var['toDate']))));
        // $datetime2 = new DateTime(date('Y-m-d', strtotime(str_replace('/', '-', $lease['unitFromDate']))));
        $datetime1 = $var['toDate'];
        $datetime2 = $lease['unitFromDate'];
        $x = 2;
        // echo '>>>'.$lease['unitFromDate']->format('Y-m-d'). ' > '.$var['fromDate']->format('Y-m-d') .'<<<';
    } else {
        // $diff = $var['toDate']- $var['fromDate'] + 1;
        // $datetime1 = new DateTime(date('Y-m-d', strtotime(str_replace('/', '-', $var['toDate']))));
        // $datetime2 = new DateTime(date('Y-m-d', strtotime(str_replace('/', '-', $var['fromDate']))));
        $datetime1 = $var['toDate'];
        $datetime2 = $var['fromDate'];
        $x = 3;
    }

    // echo $datetime1 ' - ' . $datetime2 '<br>';
    $diff = $datetime1->diff($datetime2);

    // echo $lease['leaseName'] . ' -> '.$x.'->' . $datetime1->format('Y-m-d') . ' - ' . $datetime2->format('Y-m-d') .' = ' . ($diff->format('%a') + 1) . '<br>';
    return $diff->format('%a') + 1;
}

// completeTransaction saving invoice data added by Arjay 2017-04-27
// ===== ORIGINAL completeTransaction by Arjay - START
// function completeTransaction(&$view){
//	$js=json_decode($view['invoiceLinesJson'],true);
//	$batchNo=dbGetNextInvoiceAndCreditBatchNumber(GL_SOURCE_AR);
//	$leaseInvoiceArray=array();
//	foreach (array_reverse($js) as $k => $v){
//		$calendar = dbGetPeriod ($v['propertyID'], $v['transactionDate']);
//		$transaction = array ();
//		$transaction['propertyID'] = $v['propertyID'];
//		$transaction['leaseID'] = $v['leaseID'];
//		$transaction['accountID'] = $v['accountID'];
//		$transaction['transactionDate'] = $v['transactionDate'];
//		$transaction['dueDate'] = $v['dueDate'];
//		$transaction['createUser'] = $_SESSION['un'];
//		$transaction['createDate'] = TODAY;
//		$bankDetails = dbGetBankDetails ($v['propertyID']);
//		$transaction['bankID'] = $bankDetails['bankID'];
//		$transaction['description'] = $v['description'];
//
// //		$factor = 1;
// //		if ($v['transactionType'] == TYPE_CREDIT) $factor = -1;
//
//		// $transaction['transactionAmount'] = $view->items['grossAmount'] * $factor;
//		// $transaction['netAmount'] = $view->items['netAmount'] * $factor;
//		// $transaction['taxAmount'] = $view->items['taxAmount'] * $factor;
//
//		$transaction['taxCode'] = $v['taxCode'];
//		$transaction['transactionType'] = $v['transactionType'];
//		$transaction['fromDate'] = $v['fromDate'];
//		$transaction['toDate'] = $v['toDate'];
//        $transaction['toBatchNumber'] = $v['toBatchNumber'];
//        $transaction['toLineNumber'] = $v['toLineNumber'];
//        $transaction['toInvoiceNumber'] = $v['toInvoiceNumber'];
//		$transaction['invoiceNumber'] = '';
//		$transaction['invoiceDate'] = null;
//		$transaction['batchNumber'] = $batchNo;
//		$transaction['transactionYear'] = $calendar['year'];
//		$transaction['transactionPeriod'] = $calendar['period'];
//		$transaction['lineNumber'] = $k+1;
//		$transaction['leaseID'] = $v['leaseID'];
//		$transaction['debtorID'] = $v['debtorID'];
//		$transaction['unitID'] = dbGetUnit ($v['propertyID'], $v['leaseID']);
//		//$factor = 1;
// //		if ($v['transactionType'] == TYPE_CREDIT) $factor = -1;
//		$transaction['transactionAmount'] = $v['transactionAmount']; //* $factor;
//		$transaction['netAmount'] = $v['netAmount'];// * $factor;
//		$transaction['taxAmount'] = $v['taxAmount'];// * $factor;
//		$transaction['dueDate'] = $v['dueDate'];
//
//		/** trial balance **/
//		if (GL_ACTIVE)
//		{
//			$gl = new GeneralLedger ();
//			$gl->transactionType = $transaction['transactionType'];
//			$gl->transactionDate = $transaction['transactionDate'];
//			$gl->description = $transaction['description'];
//			$gl->year = $calendar['year'];
//			$gl->period = $calendar['period'];
//			$gl->propertyID = $transaction['propertyID'];
//			$gl->leaseID = $transaction['leaseID'];
//			$gl->companyID = $transaction['debtorID'];
//			$gl->source = GL_SOURCE_AR;
//			$gl->fromDate = $transaction['fromDate'];
//			$gl->toDate = $transaction['toDate'];
//			$gl->batchID = $transaction['batchNumber'];
//			$gl->lineNumber = $transaction['lineNumber'];
//
//			$t = new Transaction ($gl); //-- detail entry : adopt the data from the
//
//			$_amount = bcmul (-1, $transaction['netAmount'], 2);
//			$gl->update ($v['accountID'], GL_BALANCE_ACCRUALS, $_amount);
//			$t->add ($v['accountID'], BASIS_ACCRUALS, $_amount);
//            $gl->description = $transaction['description'];
//			$_amount = bcmul (1, $transaction['transactionAmount'], 2);
//			$gl->update (glAccount(GL_DEBTORS_CONTROL), GL_BALANCE_ACCRUALS, $_amount);
//			$t->add (glAccount(GL_DEBTORS_CONTROL), BASIS_ACCRUALS, $_amount, true);
//            $gl->description = $transaction['description'];
//			$_amount = bcmul (-1, $transaction['taxAmount'], 2);
//			$gl->update (glAccount(GL_GST_OUTPUT), GL_BALANCE_ACCRUALS, $_amount);
//			$t->add (glAccount(GL_GST_OUTPUT), BASIS_ACCRUALS, $_amount);
//		}
//
//        /**for ap transaction tagging**/
//		$data=json_decode($v['ap_recovered']);
//		$length=count($data);
//		$transaction['recovered']=null;
//		if($length > 0){
//			$transaction['recovered']=1;
//		}
//
//		$transaction['ap_recovered']=$v['ap_recovered'];
//		$transaction['apRecoveredInvoiceNo']=null;
//		$transaction['apRecoveredSupplier']=null;
//
//		/*********end tagging********/
//
//		dbInsertReceivableTransaction ($transaction);
//
//        $transaction['allocationDate'] = $v['transactionDate'];
//        $transaction['allocationNumber'] = dbGetNextAllocationNumber();
//        $transaction['fromBatchNumber'] = $batchNo;
//        $transaction['fromLineNumber'] = $transaction['lineNumber'];
//        $transaction['currentDate'] = TODAY;
//        $transaction['currentTime'] = TODAY;
//        $transaction['fromType'] = $v['transactionType'];
//        $transaction['toType'] = ($v['transactionType'] == TYPE_CREDIT) ? TYPE_INVOICE : TYPE_CREDIT;
//
//        if ($transaction['toBatchNumber'] != '' AND trim($transaction['toBatchNumber']) != 'null')
//        {
//            dbAllocateTransaction($transaction);
//        }
//
//        if($transaction['leaseID']){
//		$arr = array();
//		$apBatch = $transaction['ap_recovered'];
//		if($transaction['ap_recovered']){
//			$data=json_decode($apBatch);
//			foreach ($data as $key=>$val)
//			{
//				array_push($arr,$val->batch);
//			}
//		}
//		$var = $arr;
//		$attachedDocuments =  dbGetDocumentInvoicePerBatchNumber($var,$transaction['propertyID']);
//		if (count($attachedDocuments) > 0) {
//			foreach ($attachedDocuments as $v) {
//				$invoiceDoc = array();
//				$invoiceDoc['propertyID'] = $transaction['propertyID'];
//				$invoiceDoc['batchNumber'] = $v['batchNumber'];
//				$invoiceDoc['attachAR'] = 1;
//				$invoiceDoc['documentID'] = $v['document_id'];
//				$invoiceDoc['documentApTag'] = 1;
//				$invoiceDoc['leaseID'] = $transaction['leaseID'];
//				array_push($leaseInvoiceArray,$invoiceDoc);
//			}
//		}
//	}
//
//		/**for ap transaction tagging**/
//		$length=count($data);
//		$apInfoAr=array();
//		$r=array('batch'=>$transaction['batchNumber'],'line'=>$transaction['lineNumber']);
//
//		array_push($apInfoAr,$r);
//		if($length > 0){
//			$transaction['recovered']=1;
//			foreach($data as $key=>$val){
//
//				dbUpdateTransactionForRecovered ('ap_transaction',1,$val->batch,$val->line,json_encode($apInfoAr),$_SESSION['un']);
//			}
//		}
//
//		$v['jsonAr']=null;
//
//		/************end****************/
//	}
//	foreach ($leaseInvoiceArray as $v){
//		dbInsertARLinkedInvoiceDocument($v);
//	}
//
//	return $batchNo;
// }
// ===== ORIGINAL completeTransaction by Arjay - END

// ===== UPDATED completeTransaction 11 02 2018 - START
function completeTransaction(&$view)
{
    $js = json_decode($view['invoiceLinesJson'], true);
    $batchNo = dbGetNextInvoiceAndCreditBatchNumber(GL_SOURCE_AR);
    $leaseInvoiceArray = [];
    $temp_document_data = [];
    $documentDataArr = [];
    $documentDataInvoiceArr = [];
    $getDocumentId = '';
    $getDocumentInvoiceId = '';

    $attachmentCount = 0;
    $attachment = [];

    $userID = intval($_SESSION['user_id']);

    // $temp_document_data = dbGetTempDocumentRowByDocumentBatch ($view['hiddenTempDocumentBatchNo']); //new
    $temp_document_data = dbGetTempDocumentRowByDocumentBatch($view['tempDocumentBatchNo']); // new

    $header_due_date = ($view['hiddenCreateInterimInvoice'] == 1 ? $view['dueDate'] : null);

    // echo 'create interim invoice:'.$view['hiddenCreateInterimInvoice'];
    // //echo 'BATCH NO OF TEMP DOC:'.$view['hiddenTempDocumentBatchNo'];
    // echo 'BATCH NO OF TEMP DOC:'.$view['tempDocumentBatchNo'];
    // echo 'temp doc title:'.$temp_document_data['documentTitle'].'~';
    // echo 'temp doc filename:'.$temp_document_data['filename'].'~';
    // pre_print_r($temp_document_data);
    // die();

    $propertyLeaseDocuments = [];
    foreach (array_reverse($js) as $k => $v) {
        $calendar = dbGetPeriod($v['propertyID'], $v['transactionDate']);
        $transaction = [];
        $transaction['propertyID'] = $v['propertyID'];
        $transaction['leaseID'] = $v['leaseID'];
        $transaction['accountID'] = $v['accountID'];
        $transaction['transactionDate'] = $v['transactionDate'];
        // $transaction['dueDate'] = ($view['hiddenCreateInterimInvoice'] == 1 ? $v['dueDate'] : NULL);//$v['dueDate']; //--original
        $transaction['dueDate'] = ($view['hiddenCreateInterimInvoice'] == 1 ? $header_due_date : null); // $v['dueDate']; //--original
        $transaction['createUser'] = $_SESSION['un'];
        $transaction['createDate'] = TODAY;
        $bankDetails = dbGetBankDetails($v['propertyID']);
        $transaction['bankID'] = $bankDetails['bankID'];
        $transaction['description'] = $v['description'];

        $v['transactionAmount'] = round($v['transactionAmount'], 2);
        $v['netAmount'] = round($v['netAmount'], 2);
        $v['taxAmount'] = round($v['taxAmount'], 2);

        // ## FOR DOCUMENT TABLE INSERT OF ATTACHMENT - START
        // ----NEW USING FILEUPLOADER WITH MULTIPLE ATTACHMENTS
        //        if($view['hiddenCreateInterimInvoice'] == 1 || $view['hiddenFullFilePath'] != '' ){
        // //if(isset($view['hasFileAttachment'])){ //old11222018
        // # For saving from temp_invoice_documents tbl to documents tbl - NEW ******** - START
        if ($temp_document_data && is_array($temp_document_data)) {
            foreach ($temp_document_data as $v_doc_data) {
                $docuFile = $v_doc_data['filename'];
                $attachmentCount++;
                $attachment[$attachmentCount]['file'] = BASEPATH . '/reports/' . $docuFile;
                $extension = pathinfo($attachment[$attachmentCount]['file'], PATHINFO_EXTENSION);
                $attachment[$attachmentCount]['content_type'] = 'application/pdf';

                // #### DOCUMENTS #####
                //                if(!in_array($v['propertyID'] . '~' . $v['leaseID'] . '~' . $v_doc_data['documentTitle'], $propertyLeaseDocuments )) {
                $documentDataArr['primaryID'] = $v['propertyID'];
                $documentDataArr['secondaryID'] = $v['leaseID'];
                $documentDataArr['documentTitle'] = $v_doc_data['documentTitle'];
                if ($view['chargeEntryType'] == SINGLE_CHARGE) {
                    $documentDataArr['documentDescription'] = 'Single lease file attachment in AR invoice and credit';
                } else {
                    $documentDataArr['documentDescription'] = 'Multiple lease file attachment in AR invoice and credit';
                }

                $documentDataArr['filename'] = trim($v_doc_data['filename']);
                $documentDataArr['createdBy'] = $_SESSION['un'];
                $documentDataArr['user_created'] = $userID;
                $documentDataArr['documentType'] = DOC_INV_SUPPLIER;
                $documentDataArr['documentBatch'] = '';
                $documentDataArr['document_ap_tag'] = 0;
                $documentDataArr['attachAR'] = 1; // NOTE THIS FOR VERIFICATION
                $documentDataArr['attachOwnerR'] = 0;
                $documentDataArr['publishToOwner'] = 0;
                // //$documentDataArr['date_created'] = getdate();
                $getDocumentId = dbInsertToDocuments($documentDataArr); // new
                $propertyLeaseDocuments[] = $v['propertyID'] . '~' . $v['leaseID'] . '~' . $v_doc_data['documentTitle'];


                // #### DOCUMENTS INVOICE #####
                $documentDataInvoiceArr['property_id'] = $v['propertyID'];
                $documentDataInvoiceArr['lease_id'] = $v['leaseID'];
                $documentDataInvoiceArr['document_id'] = $getDocumentId;
                // $documentDataInvoiceArr['invoice_date'] = ($view['hiddenCreateInterimInvoice'] == 1 ? $v['dueDate'] : 'NULL');//$v['dueDate']; //--original
                $documentDataInvoiceArr['invoice_date'] = ($view['hiddenCreateInterimInvoice'] == 1 ? $header_due_date : null);
                // $v['dueDate']; //--original
                // //$documentDataInvoiceArr['invoice_number'] = $v['toInvoiceNumber']; //NOT INCLUDED ON THE INSERT UPON CHECKING
                $documentDataInvoiceArr['linked'] = $view['hiddenCreateInterimInvoice'] == 1 ? 1 : 0;

                $documentDataInvoiceArr['attachAR'] = 1;
                // //$documentDataInvoiceArr['currentDB'] = $_SESSION['currentDB'];
                $documentDataInvoiceArr['user_created'] = $userID;
                // //$documentDataInvoiceArr['batchNumber'] = NULL; //not needed since transaction is AR
                $documentDataInvoiceArr['arBatchNumber'] = $batchNo;
                $documentDataInvoiceArr['arBatchLineNumber'] = $k + 1;
                $getDocumentInvoiceId = dbInsertToDocumentInvoiceAR($documentDataInvoiceArr); // new
                //                }
            }
        }

        $transaction['taxCode'] = $v['taxCode'];
        $transaction['transactionType'] = $v['transactionType'];
        $transaction['fromDate'] = $v['fromDate'];
        $transaction['toDate'] = $v['toDate'];
        $transaction['toBatchNumber'] = $v['toBatchNumber'];
        $transaction['toLineNumber'] = $v['toLineNumber'];
        $transaction['toInvoiceNumber'] = $v['toInvoiceNumber'];
        $transaction['invoiceNumber'] = '';
        $transaction['invoiceDate'] = null;
        $transaction['batchNumber'] = $batchNo;
        $transaction['transactionYear'] = $calendar['year'];
        $transaction['transactionPeriod'] = $calendar['period'];
        $transaction['lineNumber'] = $k + 1;
        $transaction['leaseID'] = $v['leaseID'];
        $transaction['debtorID'] = $v['debtorID'];
        $transaction['unitID'] = dbGetUnit($v['propertyID'], $v['leaseID']);
        // $factor = 1;
        //		if ($v['transactionType'] == TYPE_CREDIT) $factor = -1;
        $transaction['transactionAmount'] = $v['transactionAmount']; // * $factor;
        $transaction['netAmount'] = $v['netAmount']; // * $factor;
        $transaction['taxAmount'] = $v['taxAmount']; // * $factor;
        $transaction['dueDate'] = ($view['hiddenCreateInterimInvoice'] == 1 ? $v['dueDate'] : null); // $v['dueDate']; //--original

        /** trial balance **/
        if (GL_ACTIVE) {
            $gl = new GeneralLedger();
            $gl->transactionType = $transaction['transactionType'];
            $gl->transactionDate = $transaction['transactionDate'];
            $gl->description = $transaction['description'];
            $gl->year = $calendar['year'];
            $gl->period = $calendar['period'];
            $gl->propertyID = $transaction['propertyID'];
            $gl->leaseID = $transaction['leaseID'];
            $gl->companyID = $transaction['debtorID'];
            $gl->source = GL_SOURCE_AR;
            $gl->fromDate = $transaction['fromDate'];
            $gl->toDate = $transaction['toDate'];
            $gl->batchID = $transaction['batchNumber'];
            $gl->lineNumber = $transaction['lineNumber'];

            $t = new Transaction($gl); // -- detail entry : adopt the data from the

            $_amount = bcmul(-1, $transaction['netAmount'], 2);
            $gl->update($v['accountID'], GL_BALANCE_ACCRUALS, $_amount); // UNCOMMENT AFTER
            $t->add($v['accountID'], BASIS_ACCRUALS, $_amount); // UNCOMMENT AFTER
            $gl->description = $transaction['description'];
            $_amount = bcmul(1, $transaction['transactionAmount'], 2);
            $gl->update(glAccount(GL_DEBTORS_CONTROL), GL_BALANCE_ACCRUALS, $_amount); // UNCOMMENT AFTER
            $t->add(glAccount(GL_DEBTORS_CONTROL), BASIS_ACCRUALS, $_amount, true); // UNCOMMENT AFTER
            $gl->description = $transaction['description'];
            $_amount = bcmul(-1, $transaction['taxAmount'], 2);
            $gl->update(glAccount(GL_GST_OUTPUT), GL_BALANCE_ACCRUALS, $_amount); // UNCOMMENT AFTER
            $t->add(glAccount(GL_GST_OUTPUT), BASIS_ACCRUALS, $_amount); // UNCOMMENT AFTER
        }

        /**for ap transaction tagging**/
        $data = json_decode($v['ap_recovered']);
        $length = count($data ?? []);
        $transaction['recovered'] = null;
        if ($length > 0) {
            $transaction['recovered'] = 1;
        }

        $transaction['ap_recovered'] = $v['ap_recovered'];
        $transaction['apRecoveredInvoiceNo'] = null;
        $transaction['apRecoveredSupplier'] = null;

        /*********end tagging********/

        dbInsertReceivableTransaction($transaction); // UNCOMMENT AFTER

        $transaction['allocationDate'] = $v['transactionDate'];
        //        $transaction['allocationNumber'] = dbGetNextAllocationNumber();
        $transaction['fromBatchNumber'] = $batchNo;
        $transaction['fromLineNumber'] = $transaction['lineNumber'];
        $transaction['currentDate'] = TODAY;
        $transaction['currentTime'] = TODAY;
        $transaction['fromType'] = $v['transactionType'];
        $transaction['toType'] = ($v['transactionType'] == TYPE_CREDIT) ? TYPE_INVOICE : TYPE_CREDIT;

        if ($transaction['toBatchNumber'] != '' && trim($transaction['toBatchNumber']) !== 'null') {
            $transaction['allocationNumber'] = dbGetNextAllocationNumber();
            dbAllocateTransaction($transaction); // UNCOMMENT AFTER
        }

        if ($transaction['leaseID']) {
            $arr = [];
            $apBatch = $transaction['ap_recovered'];
            if ($transaction['ap_recovered']) {
                $data = json_decode($apBatch);
                foreach ($data as $val) {
                    $arr[] = $val->batch;
                }
            }

            $var = $arr;
            $attachedDocuments = dbGetDocumentInvoicePerBatchNumber($var, $transaction['propertyID']);
            if (count($attachedDocuments ?? []) > 0) {
                foreach ($attachedDocuments as $v) {
                    $invoiceDoc = [];
                    $invoiceDoc['propertyID'] = $transaction['propertyID'];
                    $invoiceDoc['batchNumber'] = $v['batchNumber'];
                    $invoiceDoc['attachAR'] = 1;
                    $invoiceDoc['documentID'] = $v['document_id'];
                    $invoiceDoc['documentApTag'] = 1;
                    $invoiceDoc['leaseID'] = $transaction['leaseID'];
                    $leaseInvoiceArray[] = $invoiceDoc;
                }
            }
        }

        /**for ap transaction tagging**/
        $length = count($data ?? []);
        $apInfoAr = [];
        $r = ['batch' => $transaction['batchNumber'], 'line' => $transaction['lineNumber']];

        $apInfoAr[] = $r;
        if ($length > 0) {
            $transaction['recovered'] = 1;
            foreach ($data as $val) {
                dbUpdateTransactionForRecovered(
                    'ap_transaction',
                    1,
                    $val->batch,
                    $val->line,
                    json_encode($apInfoAr),
                    $_SESSION['un']
                ); // UNCOMMENT AFTER
            }
        }

        $v['jsonAr'] = null;
        /************end****************/
    }

    foreach ($leaseInvoiceArray as $v) {
        dbInsertARLinkedInvoiceDocument($v); // UNCOMMENT AFTER
    }

    return $batchNo;
}

// ===== UPDATED completeTransaction 11 02 2018 - END

function invoice(&$context)
{
    global $clientDirectory, $pathPrefix, $sess, $clientDB;
    if (! isPostback()) {
        $view = new MasterPage(userViews(), '/ar/invoice.html');
        if (isset($_GET['obID'])) {
            $view->items['obID'] = $_GET['obID'];
        }
    } else {
        $view = new UserControl(userViews(), '/ar/invoice.html', '');
    }

    $view->setSection($context['module']);

    if (isset($sess->items['_onCharge'])) {
        $view->bindAttributesFrom($sess->items['_onCharge']);
        $view->items['chargeEntryType'] = MULTIPLE_CHARGES;
        unset($sess->items['_onCharge']);
    }

    $transactionTypes = [TYPE_INVOICE => 'Invoice', TYPE_CREDIT => 'Credit'];
    $createInterimInvoiceOption = [1 => 'Yes', 0 => 'No'];
    $sendToTenantOption = [1 => 'Yes', 0 => 'No'];
    $addCcToPmOption = [1 => 'Yes', 0 => 'No'];
    $computationTypes = ['NET' => 'Net', 'GROSS' => 'Gross'];

    if (! isset($view->items['tempDocumentBatchNo'])) {
        $view->items['tempDocumentBatchNo'] = uniqid();
    }

    $view->bindAttributesFrom($_REQUEST);

    //    if($view->items['rbgSendToTenant']) {
    //        pre_print_r($view->items['rbgSendToTenant']);
    //    }
    // Start Email Address Book
    $email_cen_sendmail_setting = dbGetEmailCenParamSetting('SENDMAIL');
    $email_cen_send = false;
    if ($email_cen_sendmail_setting && $email_cen_sendmail_setting['parameterDescription'] == '1') {
        $email_cen_send = true;
    }

    // End Email Address Book

    /* [LYN] FROM OPENING BALANCES */
    if (isset($view->items['obID'])) {
        $openingBalanceDtls = dbGetOpeningBalanceDetails($view->items['obID']);
        $view->items['propertyID'] = $openingBalanceDtls['propertyID'];
        $view->items['arDifferenceAmount'] = $openingBalanceDtls['arDifferenceAmount'];
        $view->items['displayApReceipts'] = $openingBalanceDtls['isApRequired'];
    }

    /* [LYN] FROM OPENING BALANCES */
    if ($view->items['action'] == 'completeTransaction1') {
        // echo var_dump($view->items);
        // echo 'FFFFFFFFF~~>';
        // echo 'Full file path: '.$view->items['hiddenFilePath'];

        completeTransaction($view->items);
        echo json_encode(['status' => true]);
        exit();
        // -- added the completeTransaction function to save the invoice data (2017-04-19 - Arjay)
    }

    if ($view->items['action'] == 'completeTransaction') {
        if ($view->items['type'] == TYPE_CREDIT) {
            $context['statusMessage'] = 'You have successfully offset one or more invoices.';
        } else {
            $context['statusMessage'] = 'You have successfully completed this transaction';
        }
    }

    if ($view->items['action'] == 'onCharge') {
        unset($view->items['invoiceNumber']);
    }

    // @ action completeTransaction2 for generate interim invoice added by arjay
    if ($view->items['action'] == 'completeTransaction2') {
        $context['statusMessage'] = 'You have successfully offset one or more invoices.';
        // **** For generate interim invoice added by arjay 2017-08-03
        $logoFile = dbGetClientLogo();
        $logoPath = "assets/clientLogos/{$logoFile}";
        [$day, $month, $year] = explode('/', date('d/m/Y'));
        $filename = "tax_invoice_{$year}{$month}{$day}_" . $view->items['propertyID'] . '.pdf';
        $filePath = "{$pathPrefix}{$clientDirectory}/pdf/TaxInvoice/{$filename}";
        $downloadPath = "{$clientDirectory}/pdf/TaxInvoice/{$filename}";
        $masterInvoice = new PDFReport($filePath, $logoPath);
        // ************************* end ***************************;
        $details = prepareTaxInvoice(
            InvoiceStatementType::ST_INVOICE,
            $_SESSION['forGenerateInterimInvoice']['leaseID'],
            $_SESSION['forGenerateInterimInvoice']['propertyID'],
            $_SESSION['forGenerateInterimInvoice']['dueDate'],
            $_SESSION['forGenerateInterimInvoice']['transactionDate'],
            $masterInvoice,
            $_SESSION['forGenerateInterimInvoice']['batchNumber'],
            null,
            1
        ); // for generate interim invoice added by arjay
        if ($details) {
            $detailsCount++;
        }

        if ($detailsCount) {
            $view->items['downloadLink'] = $downloadPath;
        }

        $masterInvoice->_close();
        $_SESSION['forGenerateInterimInvoice'] = null;
    }

    // -- credited invoices stay resident if you change search criteria, so drop the references if the user changes property/lease etc
    if ($view->items['action'] == 'changePrimary') {
        unsetCreditedInvoice($view->items);
    }

    /* handle loading of recurring invoice templates if a template id has been provided */
    /* passed from 'viewRecurringInvoice' */
    if (isset($view->items['templateID'])) {
        $template = dbGetRecurringInvoice($view->items['templateID']);
        if ($template) {
            $view->bindAttributesFrom($template);
            $view->items['step'] = 2;
            $view->items['selectionMethod'] = 'property';
            $view->items['transactionType'] = TYPE_INVOICE;
        }
    }

    /* handle loading of unpaid invoices for offsetting */
    /* passed from 'viewUnpaidInvoice' */
    if (isset($view->items['toBatchNumber'])) {
        $unpaid = dbGetTransactionByBatch($view->items['toBatchNumber'], $view->items['toLineNumber']);
        if ($unpaid) {
            $view->bindAttributesFrom($unpaid);
            $view->items['step'] = 2;
            $view->items['selectionMethod'] = 'property';
            $view->items['transactionType'] = TYPE_CREDIT;
            $view->items['dueDate'] = $view->items['transactionDate'] = TODAY;
        }
    }

    $view->items['last_error'] = $context['last_error'];
    $view->items['statusMessage'] = $context['statusMessage'];

    /* save the view state to context for use in the tenant selection fragment */
    $context = $view->items;
    $context['container'] = 'content';
    $context['command'] = 'invoice';

    $view->items['UserControl:TenantSelector'] = fetchCommand('selectTenant', 'ar');
    // $view->items['UserControl:LeaseSelector'] = fetchCommand ('leaseSelector', 'ar');

    /* pass the context back into view state */
    $view->bindAttributesFrom($context);

    if (isset($view->items['netAmount'])) {
        $view->items['netAmount'] = round($view->items['netAmount'], 2);
    }

    if (isset($view->items['grossAmount'])) {
        $view->items['grossAmount'] = round($view->items['grossAmount'], 2);
    }

    if (isset($view->items['propertyID']) && ($view->items['chargeEntryType'] == MULTIPLE_CHARGES)) {
        $view->items['tenantType'] = null;
        $view->items['step'] = 2;
        unset($view->items['leaseID']);
    }

    // if a property, lease and debtor have been selected, selectTenant sets 'step' to 2
    if ($view->items['step'] == 2) {
        // -- set the default dates
        if (! isset($view->items['transactionDate'])) {
            $view->items['transactionDate'] = dbNextBillingDate($view->items['propertyID']);
            $view->items['fromDate'] = $view->items['transactionDate'];
            $view->items['toDate'] = $view->items['transactionDate'];
        }

        /* if (!isset ($view->items['dueDate'])) $view->items['dueDate'] = $view->items['transactionDate']; */ // NOTE[04-12-2019] - Removed this because it sets the default value to transaction date even if the actual due date textfield is blank, it causes the generate interim to still proceed

        // -- fetch and set the tax rate details

        // $view->items['taxRateList'] = dbGetTaxRates ();
        $taxRateList = dbGetTaxRates();
        //		if(in_array($view->items['selectionMethod'],['subledger','ledger'])):
        //		foreach($taxRateList as $index=>$rs)
        //            if($rs['taxCode'] != "GSTFREE")
        //                unset($taxRateList[$index]);
        //        $view->items['taxRateID'] = "GSTFREE";
        //        endif;
        $index = array_search('GSTFREE', array_column($taxRateList, 'taxCode'), true);
        //        $view->items['taxRateList'] = ($view->items['tenantType']=='RESI')? [$taxRateList[$index]] :$taxRateList;
        $view->items['taxRateList'] = $taxRateList;
        $taxRates = mapParameters($view->items['taxRateList'], 'taxCode', 'taxRate');
        if (! isset($view->items['taxRateID'])) {
            $tax = dbGetTaxStatus($view->items['propertyID']);
            $view->items['taxRateID'] = ($view->items['tenantType'] == 'RESI') ? $taxRateList[$index]['taxCode'] : $tax['taxCode'];
        }

        $view->items['taxRateDescription'] = $taxRates[$view->items['taxRateID']];
        if (! isset($view->items['computationType'])) {
            $view->items['computationType'] = 'NET';
        }

        if ($view->items['computationType'] == 'NET') {
            // -- calculate the transaction amounts based on the tax rate
            if (! isset($view->items['netAmount'])) {
                $view->items['netAmount'] = '0.00';
            }

            $view->items['taxAmount'] = round(
                ($taxRates[$view->items['taxRateID']] / 100) * $view->items['netAmount'],
                2
            );
            $view->items['grossAmount'] = bcadd($view->items['taxAmount'], $view->items['netAmount'], 2);
        } else {
            // -- calculate the transaction amounts based on the tax rate
            if (! isset($view->items['grossAmount'])) {
                $view->items['grossAmount'] = '0.00';
            }

            $taxRate = 100 / (100 + $taxRates[$view->items['taxRateID']]);
            $view->items['netAmount'] = round($taxRate * $view->items['grossAmount'], 2);
            $view->items['taxAmount'] = bcsub($view->items['grossAmount'], $view->items['netAmount'], 2);
        }

        if (! isset($view->items['transactionType'])) {
            $view->items['transactionType'] = TYPE_INVOICE;
        }


        // -- grab the list of accounts
        $view->items['accountList'] = dbGetGroupedAccounts(INCOME);

        // ///////

        // ///////

        $view->items['accountGroupList'] =
        [
            'INCOWN' => 'Owners Income',
            'INCDR' => 'Directly Recoverable Income',
            'INCVO' => ucwords(strtolower($_SESSION['country_default']['variable_outgoings'])) . ' Income',
        ];
        if (! isset($view->items['accountGroup'])) {
            $view->items['accountGroup'] = 'INCOWN';
        }

        $view->items['groupedAccountList'] = dbGetGroupedAccounts(INCOME, $view->items['accountGroup']);
        $accountDescription = mapParameters($view->items['accountList'], 'accountID', 'accountName');
        $accountCodes = mapParameters($view->items['accountList'], 'accountID', 'accountID');

        // added by arjay for ap recoverable expenses

        $view->items['apRecoverableTransactionList'] = dbGetPayableTransactionByGroupCodeAccountAndProperty(
            'EXP.REC',
            $view->items['propertyID'],
            $view->items['fromDate'],
            $view->items['toDate']
        );
        // ## new - start
        $ap_batches = [];
        $var_data = [];
        $ap_batch_number = '';
        $all_links = '';
        $attachmentCounter = 0;

        foreach ($view->items['apRecoverableTransactionList'] as $key => $val) {
            $allAttachments = dbGetDocumentInvoiceByAPBatch($val['batch']);

            $linkArray = [];

            foreach ($allAttachments as $a_data) {
                $apFile = $a_data['fileName'];

                $attachmentCounter++;
                $attachment[$attachmentCounter]['file'] = BASEPATH . '/reports/' . $apFile;
                $extension = pathinfo($attachment[$attachmentCounter]['file'], PATHINFO_EXTENSION);
                $apFile_basename = pathinfo($apFile, PATHINFO_BASENAME);
                $attachment[$attachmentCounter]['content_type'] = 'application/pdf';

                $linkArray[] = '<a href="#" onclick="location.href = \'download.php?fileID=' . encodeParameter(
                    $apFile
                ) . '\'" title="Click to download file">' . $apFile_basename . '</a>';
            }

            $all_links = implode('</br>', $linkArray);

            $view->items['apRecoverableTransactionList'][$key]['all_attachments'] = $all_links;
        }

        // pre_print_r($view->items['apRecoverableTransactionList']);
        // ## new - end


        $refNumbers = mapParameters($view->items['apRecoverableTransactionList'], 'batch', 'batch');
        // for direct recoverable accounts
        $view->items['accountGroupType'] = null;
        $accountType = dbGetAccountGroupType($view->items['accountID']);
        if ($accountType['groupType'] == 'INC.REC') {
            // $view->items['apRecoverableTransactionList'] = ($view->items['propertyID']!=null && $view->items['propertyID'] !=null)? dbGetArRecoveredTransactionByFromToDate('INC.REC',$view->items['fromDate'],$view->items['toDate'],$view->items['propertyID']): null;
            $view->items['accountGroupType'] = $accountType['groupType'];
        } else {
            $view->items['apRecoverableTransactionList'] = [];
        }

        $man_fee_lease_list = [];
        if ($view->items['chargeEntryType'] == 1) {
            /** Entry type is SINGLE */
            $manFees = dbGetLeaseManagementFeeDetails($view->items['propertyID'], [$view->items['leaseID']]);
            $view->items['managementFee'] = $manFees[0]['managementFee'] == 1;
        } else {
            /** Entry type is MULTIPLE */

            /** O */
            //            foreach($view->items['leaseList']as $v){
            //                $manFees = dbGetLeaseManagementFeeDetails ($view->items['propertyID'], [$v['leaseID']]);
            //                $view->items['managementFee']=($manFees[0]['managementFee']==1)? true:false;
            //                if($view->items['managementFee']==true){break;}
            //            }

            /** U */
            // if($view->items['propertyID']){
            $man_fee_lease_list = dbGetPropertyLeasesPercents($view->items['propertyID']);
            foreach ($man_fee_lease_list as $v) {
                $manFees = dbGetLeaseManagementFeeDetails($view->items['propertyID'], [$v['leaseID']]);
                $view->items['managementFee'] = $manFees[0]['managementFee'] == 1;
                if ($view->items['managementFee'] == true) {
                    break;
                }
            }

            // }
        }
    }

    // #################### Get Lease VO Notes #########################
    if ($view->items['propertyID']) {
        $VONotes = dbGetVOLeaseNotesDesc($view->items['propertyID'], $view->items['leaseID']);

        $view->items['voLeaseNotes'] = $VONotes;
        $view->items['voLeaseNotesCount'] = count($VONotes ?? []);
    }

    // #################################################################

    switch ($view->items['action']) {
        case 'adjustEntryMethod': // changing from single to multiple
        case 'nextStep': // change lease using the table (clicking on a lease)
        case 'changePrimary': // change property
        case 'changeSecondary': // change lease
            if ($view->items['propertyID']) {
                $propVONotes = dbGetVOPropertyNotesDesc($view->items['propertyID']);

                $view->items['voPropertyNotes'] = $propVONotes;
                $view->items['voPropertyNotesCount'] = count($propVONotes ?? []);
            }

            unset($view->items['invoiceLinesJson']); // reset line items
            break;
        case 'energytec':
            $energytec_exist = dbCheckIFEnergytecParamaterExist();
            echo json_encode($energytec_exist[0]);
            exit;
        case 'attachmentTempUpload':
            // echo 'CCCC';
            // echo var_dump($view->items);

            $filePath = "{$pathPrefix}{$clientDirectory}/pdf/TempTaxInvoices/";
            if (! file_exists($filePath)) {
                mkdir($filePath, FILE_PERMISSION, true);
            }

            $FileUploader = new FileUploader('singleAttachment', [
                'limit' => null,
                'maxSize' => null,
                'fileMaxSize' => null,
                'extensions' => null,
                'required' => false,
                'uploadDir' => $filePath,
                // 'title' => $view->items['propertyID'].'_'.'{file_name}'.'_'.time(),
                'title' => $view->items['propertyID'] . '_' . time(),
                'replace' => false,
                'listInput' => true,
                'files' => null,
            ]);
            $data = $FileUploader->upload();

            echo json_encode(
                [
                    'data' => $data,
                    'upload_status' => 1,
                ]
            );
            exit();
        case 'uploadFiles':
            $filePath = "{$pathPrefix}{$clientDirectory}/pdf/SupplierInvoices/";
            // $invoiceNumber = isset($_POST['invoiceNumber']) && !empty($_POST['invoiceNumber']) ? $_POST['invoiceNumber'] : null;
            $propertyID = isset($_POST['propertyID']) && ! empty($_POST['propertyID']) ? $_POST['propertyID'] : null;
            $batchNumber = isset($_POST['batchNumber']) && ! empty($_POST['batchNumber']) ? $_POST['batchNumber'] : null;
            $transactionDate = isset($_POST['transactionDate']) && ! empty($_POST['transactionDate']) ? $_POST['transactionDate'] : null;
            $creditorID = isset($_POST['creditorID']) && ! empty($_POST['creditorID']) ? $_POST['creditorID'] : null;
            $leaseID = isset($_POST['leaseID']) && ! empty($_POST['leaseID']) ? $_POST['leaseID'] : null;
            $documentBatchNumber = isset($_POST['documentBatchNumber']) && ! empty($_POST['documentBatchNumber']) ? $_POST['documentBatchNumber'] : null;
            $tempDocumentBatchNo = isset($_POST['tempDocumentBatchNo']) && ! empty($_POST['tempDocumentBatchNo']) ? $_POST['tempDocumentBatchNo'] : null;
            $fileName = isset($_POST['fileName']) && ! empty($_POST['fileName']) ? pathinfo(
                $_POST['fileName'],
                PATHINFO_FILENAME
            ) : null;

            $newFileName = substr($fileName, 0, 70) . '_' . hrtime(true);

            // initialize FileUploader
            $FileUploader = new FileUploader('singleFile', [
                'limit' => null,
                'maxSize' => null,
                'fileMaxSize' => null,
                'extensions' => null,
                'required' => false,
                'uploadDir' => $filePath,
                // 'title' => $invoiceNumber . '_' . $creditorID . '_' .substr($fileName,0,15) . '_' .mt_rand(0,10000). time(),
                // //$view->items['propertyID'].'_'.$view->items['leaseID'].'_'.time() . '.pdf';
                // 'title' => $propertyID . '_' . $leaseID . '_' .substr($fileName,0,15) . '_' .mt_rand(0,10000). time(),
                'title' => $newFileName,
                'replace' => false,
                'listInput' => true,
                'files' => null,
            ]);
            // call to upload the files
            $data = $FileUploader->upload();

            if ($data['isSuccess']) {
                // get the uploaded files
                $files = $data['files'];
                $id = null;
                foreach ($files as $v) {
                    $filename = $v['title']; // $view->items['batchNumber'] . '_' . $v['name'] . '_'. time();
                    $downloadPath = "{$clientDirectory}/pdf/SupplierInvoices/{$filename}.pdf";

                    $doc = [];
                    $doc['documentTitle'] = $filename;
                    $doc['documentType'] = DOC_INV_SUPPLIER;
                    $doc['createdBy'] = $_SESSION['un'];
                    $doc['primaryID'] = $propertyID;
                    $doc['secondaryID'] = $leaseID;
                    $doc['filename'] = $downloadPath;
                    $doc['documentDescription'] = ''; // $view->items['fileNameDescription'];
                    $doc['documentBatch'] = $tempDocumentBatchNo;
                    $doc['documentApTag'] = 0;
                    $doc['attachAR'] = 0;
                    $doc['attachOwnerR'] = 0;
                    $id = dbAddTempDocument($doc);
                }

                $data['documentID'] = $id;
                $data['attachAR'] = 0;
                $data['attachOwnerR'] = 0;
                // $data['invoiceID'] = $invoiceNumber;

                // new ********
                $data['status'] = 1;
                $data['new_path'] = $downloadPath;
                // $data['client_path'] = $clientDirectoryFile;
                $data['client_path'] = $downloadPath;
                $data['base_file_name'] = $newFileName;
                $data['last_temp_document_id'] = $id;
                // header('Location: ?action=completeTransaction&module=ap&command=invoice');
            }

            echo json_encode($data);
            exit;

        case 'removeFile':
            if (isset($_POST['fileName'])) {
                $documentID = $_POST['documentID'];
                $fileName = $_POST['fileName'];
                $filePath = "{$pathPrefix}{$clientDirectory}/pdf/SupplierInvoices/{$fileName}";
                dbDeleteTempInvoiceDocumentFile($documentID);

                if (file_exists($filePath)) {
                    unlink($filePath);
                }
            }

            $attachedInvoiceList = dbSelectLinkedInvoiceTempDocument($view->items['documentBatchNumber']);
            echo json_encode(count($attachedInvoiceList));
            exit();

        case 'saveAsPermanentFile':
            // ## Note: MainTaxInvoices folder is just used for testing on saving permanent attachments | FOR UPDATE LATER
            /*
            // test
            $clientFolderFilePath = "{$clientDirectory}/pdf/MainTaxInvoices/";
            $filePath = "{$pathPrefix}{$clientDirectory}/pdf/MainTaxInvoices/";
            */

            $clientFolderFilePath = "{$clientDirectory}/pdf/SupplierInvoices/";
            $filePath = "{$pathPrefix}{$clientDirectory}/pdf/SupplierInvoices/";
            if (! file_exists($filePath)) {
                mkdir($filePath, FILE_PERMISSION, true);
            }

            // $permanentFile = $filePath . $new_fc_batch_id . '.pdf';

            $full_file_name = $view->items['propertyID'] . '_' . $view->items['leaseID'] . '_' . time() . '.pdf';

            $doc = explode('.', $full_file_name);
            $base_file_name = $doc[0];
            $file_format = $doc[1];

            $clientDirectoryFile = $clientFolderFilePath . $full_file_name;
            $permanentFile = $filePath . $full_file_name;
            rename($view->items['attachmentPath'], $permanentFile);

            $doc = [];
            $doc['documentTitle'] = $base_file_name;
            $doc['documentType'] = DOC_INV_TENANT;
            $doc['createdBy'] = $_SESSION['un'];
            $doc['primaryID'] = $view->items['propertyID'];
            $doc['secondaryID'] = $view->items['leaseID'];
            $doc['filename'] = $clientDirectoryFile;
            $doc['documentDescription'] = 'Attachment from Single Lease in AR'; // $view->items['fileNameDescription'];
            $doc['documentBatch'] = $view->items['tempDocumentBatchNo'];
            // $doc['documentApTag'] = 0;
            $doc['attachAR'] = 0;
            $doc['attachOwnerR'] = 0;
            $id = dbAddTempDocument($doc);

            echo json_encode(
                [
                    'status' => 1,
                    'new_path' => $permanentFile,
                    'client_path' => $clientDirectoryFile,
                    'base_file_name' => $base_file_name,
                    'last_temp_document_id' => $id,
                ]
            );
            exit();
        case 'manFees':
            if (isset($_GET['leases'])) {
                $leases = explode(',', $_GET['leases']);
                $manFees = dbGetLeaseManagementFeeDetails($view->items['propertyID'], $leases);
                $tax = dbGetTaxStatus($view->items['propertyID']);
                $result = [];

                foreach ($manFees as $value) {
                    $result[$value['lease']] = array_merge($value, $tax);
                    $result[$value['lease']]['managementFeeAmount'] = 0;
                }

                echo json_encode($result);
                exit;
            } else {
                $validationErrors[] = "You don't have a list of invoice line.";
                echo json_encode($validationErrors);
                exit();
            }

            break;
        case 'recovered':
            if ($accountType['groupType'] == 'INC.REC') {
                $view->items['apRecoverableTransactionList'] = dbGetPayableTransactionByGroupCodeAccountAndProperty(
                    'EXP.REC',
                    $view->items['propertyID'],
                    $view->items['fromDate'],
                    $view->items['toDate']
                );
                $view->items['accountGroupType'] = $accountType['groupType'];
            } else {
                $view->items['apRecoverableTransactionList'] = [];
            }

            break;
        case 'updateAccounts':
            if ($accountType['groupType'] == 'INC.REC') {
                $view->items['apRecoverableTransactionList'] = dbGetPayableTransactionByGroupCodeAccountAndProperty(
                    'EXP.REC',
                    $view->items['propertyID'],
                    $view->items['fromDate'],
                    $view->items['toDate']
                );
                $view->items['accountGroupType'] = $accountType['groupType'];
                // $view->items['arRecoverableTransactionList'] = ($view->items['propertyID']!=null && $view->items['propertyID'] !=null)? dbGetArRecoveredTransactionByFromToDate('INC.REC',$view->items['fromDate'],$view->items['toDate'],$view->items['propertyID']): null;
            } else {
                $view->items['apRecoverableTransactionList'] = [];
            }

            // $view->items['description'] = ucfirst(strtolower($accountDescription[$view->items['accountID']])); //O
            $view->items['description'] = $accountDescription[$view->items['accountID']]; // U

            // $view->items['jsonAr']=null;
            break;
        case 'applyInvoice':
            $view->items['transactionType'] = TYPE_CREDIT;
            $view->items['rbgCreateInterimInv'] = 1; // auto set generate interim invoice to YES if credit an invoice option was used
            $view->items['hiddenCreateInterimInvoice'] = 1; // auto set generate interim invoice to YES if credit an invoice option was used
            $dataSet = extractFields(
                $view->items,
                ['batchNumber', 'lineNumber', 'invoiceNumber', 'appliedAmount', 'amount', 'select']
            );
            $calendar = dbGetPeriod($view->items['propertyID'], $view->items['transactionDate']);

            if (! $calendar) {
                $validationErrors[] = 'A period (calendar entry) cannot be found for this transaction date.';
            }

            if ($calendar['closed']) {
                $validationErrors[] = 'Your transaction date is within a closed period. Please review and amend.';
            }

            // -- added 20100430 - check all transactions to make sure the transaction date is after the transaction date of the invoice or credit to be offset
            $invoiceLinesJson = json_decode($view->items['invoiceLinesJson'], true);
            foreach ($dataSet as $row) {
                if ($row['select']) {
                    foreach ($invoiceLinesJson as $lines) {
                        if ($row['batchNumber'] == $lines['toBatchNumber'] && $row['lineNumber'] == $lines['toLineNumber']) {
                            $creditAmount[$row['batchNumber'] . $row['lineNumber']][] = $lines['transactionAmount'];
                        }
                    }

                    if (isset($creditAmount[$row['batchNumber'] . $row['lineNumber']]) && array_sum(
                        $creditAmount[$row['batchNumber'] . $row['lineNumber']]
                    ) * -1 + $row['appliedAmount'] > $row['amount']) {
                        $validationErrors[] = 'One of your applied amounts to credit is greater than the total unpaid amount.';
                    }

                    $transaction = dbGetTransactionByBatch($row['batchNumber'], $row['lineNumber']);
                    if (toDateStamp($view->items['transactionDate']) < toDateStamp($transaction['transactionDate'])) {
                        $validationErrors['date'] = ($transaction['transactionType'] == TYPE_INVOICE) ? 'The transaction date for the credit cannot be prior to the invoice date. Modify the credit transaction date to be at or after the invoice' : 'The invoice date cannot be prior to the credit date. Modify the invoice transaction date to be at or after the credit';
                    }
                }
            }

            if (noErrors($validationErrors)) {
                foreach ($dataSet as $row) {
                    if (($row['select']) && ($row['appliedAmount'] <= $row['amount'])) {
                        if (($row['amount'] > 0) && ($row['appliedAmount'] > 0)) {
                            // if(($row['amount'] < 0) && ($row['appliedAmount'] < 0)){
                            // -- fetch a new batch number if one doesnt already exist
                            if (! isset($view->items['batchNumber']) || trim($view->items['batchNumber']) === '' || trim(
                                $view->items['batchNumber']
                            ) === 'null') {
                                $view->items['batchNumber'] = dbGetNextInvoiceAndCreditBatchNumber(GL_SOURCE_AR);
                            }// dbGetNextReceivableBatchNumber ();

                            $transaction = dbGetTransactionByBatch($row['batchNumber'], $row['lineNumber']);
                            $tax = dbGetTaxCode($transaction['taxCode']);
                            $taxRate = 100 / (100 + $tax['taxRate']);

                            if ($transaction) {
                                $grossAmount = (-1) * $row['appliedAmount'];
                                $netAmount = round($grossAmount * ($taxRate), 2);
                                $taxAmount = $grossAmount - $netAmount;
                                $transaction['toBatchNumber'] = $row['batchNumber'];
                                $transaction['toLineNumber'] = $row['lineNumber'];
                                $transaction['toInvoiceNumber'] = $transaction['invoiceNumber'];
                                $transaction['invoiceNumber'] = null;
                                $transaction['invoiceDate'] = null;
                                $transaction['transactionDate'] = $view->items['transactionDate'];
                                $transaction['dueDate'] = $view->items['dueDate'];
                                // -- edit 20090501 : modified the transaction type to reverse when its a credit vs invoice.
                                $transaction['transactionType'] = ($transaction['transactionType'] == TYPE_CREDIT) ? TYPE_INVOICE : TYPE_CREDIT;
                                $transaction['createUser'] = $_SESSION['un'];
                                $transaction['createDate'] = TODAY;
                                $transaction['transactionAmount'] = $grossAmount;
                                $transaction['netAmount'] = $netAmount;
                                $transaction['taxAmount'] = $taxAmount;
                                $transaction['transactionYear'] = $calendar['year'];
                                $transaction['transactionPeriod'] = $calendar['period'];

                                // -- referenced by GL 3013-11-18
                                $transaction['year'] = $calendar['year'];
                                $transaction['period'] = $calendar['period'];
                                // -- end

                                $transaction['batchNumber'] = $view->items['batchNumber'];
                                $transaction['lineNumber'] = dbGetNextReceivableLineNumber($view->items['batchNumber']);
                                $transaction['dueDate'] = $view->items['dueDate'];
                                if ($view->items['invoiceLinesJson'] != null) {
                                    $js = json_decode($view->items['invoiceLinesJson'], true);
                                    array_unshift($js, $transaction);
                                } else {
                                    $js[] = $transaction;
                                }

                                $view->items['invoiceLinesJson'] = json_encode($js);
                            }
                        } else {
                            $validationErrors[] = 'You cannot enter a negative amount - to credit, simply enter the amount to apply and tick the corresponding box.';
                        }
                    } elseif ($row['amount'] < $row['appliedAmount']) {
                        $validationErrors[] = 'One of your applied amounts to credit is greater than the unpaid amount';
                    }
                }

                //                if(isset($view->items['generateInterimInvoice'])){
                //
                //				    $dataSet_1=array(
                //                        'leaseID'=>$view->items['leaseID'],
                //                        'propertyID'=>$view->items['propertyID'],
                //                        'dueDate'=>$view->items['dueDate'],
                //                        'transactionDate'=>$view->items['transactionDate'],
                //                        'batchNumber'=>$view->items['batchNumber']
                //                    );
                //                    $_SESSION['forGenerateInterimInvoice']=$dataSet_1;
                //                    clientSideRedirect ('?action=completeTransaction2&command=invoice&module=ar'); // added by arjay 04-08-2017
                //                }else{
                //                    if($counter) clientSideRedirect ('?type='.TYPE_CREDIT.'&action=completeTransaction&command=invoice&module=ar'); // added by arjay
                //                }

            }

            break;
        case 'applyInvoice1':
            $view->items['transactionType'] = TYPE_CREDIT;
            $dataSet = extractFields(
                $view->items,
                ['batchNumber', 'lineNumber', 'invoiceNumber', 'appliedAmount', 'amount', 'select']
            );
            $calendar = dbGetPeriod($view->items['propertyID'], $view->items['transactionDate']);

            if (! $calendar) {
                $validationErrors[] = 'A period (calendar entry) cannot be found for this transaction date.';
            }

            if ($calendar['closed']) {
                $validationErrors[] = 'Your transaction date is within a closed period. Please review and amend.';
            }

            // -- added 20100430 - check all transactions to make sure the transaction date is after the transaction date of the invoice or credit to be offset
            foreach ($dataSet as $row) {
                if ($row['select']) {
                    $transaction = dbGetTransactionByBatch($row['batchNumber'], $row['lineNumber']);
                    if (toDateStamp($view->items['transactionDate']) < toDateStamp($transaction['transactionDate'])) {
                        $validationErrors['date'] = ($transaction['transactionType'] == TYPE_INVOICE) ? 'The transaction date for the credit cannot be prior to the invoice date. Modify the credit transaction date to be at or after the invoice' : 'The invoice date cannot be prior to the credit date. Modify the invoice transaction date to be at or after the credit';
                    }
                }
            }

            if (noErrors($validationErrors)) {
                foreach ($dataSet as $row) {
                    if (($row['select']) && ($row['appliedAmount'] <= $row['amount'])) {
                        if (($row['amount'] > 0) && ($row['appliedAmount'] > 0)) {
                            // if(($row['amount'] < 0) && ($row['appliedAmount'] < 0)){
                            // -- fetch a new batch number if one doesnt already exist
                            if (! isset($view->items['batchNumber']) || trim($view->items['batchNumber']) === '' || trim(
                                $view->items['batchNumber']
                            ) === 'null') {
                                $view->items['batchNumber'] = dbGetNextInvoiceAndCreditBatchNumber(GL_SOURCE_AR);
                            }// dbGetNextReceivableBatchNumber ();

                            $transaction = dbGetTransactionByBatch($row['batchNumber'], $row['lineNumber']);
                            $tax = dbGetTaxCode($transaction['taxCode']);
                            $taxRate = 100 / (100 + $tax['taxRate']);

                            if ($transaction) {
                                $grossAmount = (-1) * $row['appliedAmount'];
                                $netAmount = round($grossAmount * ($taxRate), 2);
                                $taxAmount = $grossAmount - $netAmount;
                                $transaction['toBatchNumber'] = $row['batchNumber'];
                                $transaction['toLineNumber'] = $row['lineNumber'];
                                $transaction['toInvoiceNumber'] = $transaction['invoiceNumber'];
                                $transaction['invoiceNumber'] = null;
                                $transaction['invoiceDate'] = null;
                                $transaction['transactionDate'] = $view->items['transactionDate'];
                                $transaction['dueDate'] = $view->items['dueDate'];
                                // -- edit 20090501 : modified the transaction type to reverse when its a credit vs invoice.
                                $transaction['transactionType'] = ($transaction['transactionType'] == TYPE_CREDIT) ? TYPE_INVOICE : TYPE_CREDIT;
                                $transaction['createUser'] = $_SESSION['un'];
                                $transaction['createDate'] = TODAY;
                                $transaction['transactionAmount'] = $grossAmount;
                                $transaction['netAmount'] = $netAmount;
                                $transaction['taxAmount'] = $taxAmount;
                                $transaction['transactionYear'] = $calendar['year'];
                                $transaction['transactionPeriod'] = $calendar['period'];

                                // -- referenced by GL 3013-11-18
                                $transaction['year'] = $calendar['year'];
                                $transaction['period'] = $calendar['period'];
                                // -- end

                                $transaction['batchNumber'] = $view->items['batchNumber'];
                                $transaction['lineNumber'] = dbGetNextReceivableLineNumber($view->items['batchNumber']);
                                $transaction['dueDate'] = $view->items['dueDate'];

                                /** trial balance **/
                                if (GL_ACTIVE) {
                                    $gl = new GeneralLedger();
                                    $gl->transactionType = $transaction['transactionType'];
                                    $gl->transactionDate = $transaction['transactionDate'];
                                    $gl->description = $transaction['description'];
                                    $gl->year = $calendar['year'];
                                    $gl->period = $calendar['period'];
                                    $gl->propertyID = $transaction['propertyID'];
                                    $gl->leaseID = $transaction['leaseID'];
                                    $gl->companyID = $transaction['debtorID'];
                                    $gl->source = GL_SOURCE_AR;
                                    $gl->fromDate = $transaction['fromDate'];
                                    $gl->toDate = $transaction['toDate'];

                                    $gl->batchID = $transaction['batchNumber'];
                                    $gl->lineNumber = $transaction['lineNumber'];

                                    $t = new Transaction($gl);    // -- detail entry : adopt the data from the

                                    $_amount = bcmul(-1, $transaction['netAmount'], 2);
                                    $gl->update($transaction['accountID'], GL_BALANCE_ACCRUALS, $_amount);
                                    $t->add($transaction['accountID'], BASIS_ACCRUALS, $_amount);

                                    $_amount = bcmul(1, $transaction['transactionAmount'], 2);
                                    $gl->update(glAccount(GL_DEBTORS_CONTROL), GL_BALANCE_ACCRUALS, $_amount);
                                    $t->add(glAccount(GL_DEBTORS_CONTROL), BASIS_ACCRUALS, $_amount, true);

                                    $_amount = bcmul(-1, $transaction['taxAmount'], 2);
                                    $gl->update(glAccount(GL_GST_OUTPUT), GL_BALANCE_ACCRUALS, $_amount);
                                    $t->add(glAccount(GL_GST_OUTPUT), BASIS_ACCRUALS, $_amount);
                                }

                                /**for recoverable transaction tagging**/
                                $data = json_decode($view->items['jsonAr']);
                                $transaction['ap_recovered'] = null;
                                $length = count($data ?? []);
                                $transaction['recovered'] = ($length > 0) ? 1 : null;
                                $transaction['ap_recovered'] = $view->items['jsonAr'];
                                $transaction['apRecoveredInvoiceNo'] = null;
                                $transaction['apRecoveredSupplier'] = null;
                                /**end transaction**/

                                dbInsertReceivableTransaction($transaction);

                                /**for ap transaction tag as recovered**/
                                $apInfoAr = [];
                                $r = [
                                    'batch' => $transaction['toBatchNumber'],
                                    'line' => $transaction['toLineNumber'],
                                ];

                                $apInfoAr[] = $r;
                                if ($length > 0) {
                                    foreach ($data as $val) {
                                        dbUpdateTransactionForRecovered(
                                            'ap_transaction',
                                            1,
                                            $val->batch,
                                            $val->line,
                                            json_encode($apInfoAr),
                                            $_SESSION['un']
                                        );
                                    }
                                }

                                $view->items['jsonAr'] = null;
                                /**end ap transaction tag as recovered**/
                                /****************************/

                                $transaction['allocationDate'] = $view->items['transactionDate'];
                                $transaction['allocationNumber'] = dbGetNextAllocationNumber();
                                $transaction['fromBatchNumber'] = $transaction['batchNumber'];
                                $transaction['fromLineNumber'] = $transaction['lineNumber'];
                                $transaction['toBatchNumber'] = $row['batchNumber'];
                                $transaction['toLineNumber'] = $row['lineNumber'];
                                $transaction['currentDate'] = TODAY;
                                $transaction['currentTime'] = TODAY;
                                $transaction['fromType'] = $transaction['transactionType'];
                                $transaction['toType'] = ($transaction['transactionType'] == TYPE_CREDIT) ? TYPE_INVOICE : TYPE_CREDIT;

                                if ($transaction['fromBatchNumber'] != '' && trim(
                                    $transaction['fromBatchNumber']
                                ) !== 'null') {
                                    dbAllocateTransaction($transaction);
                                }

                                // -- allocation
                                //								$transaction['batchNumber'] = $transaction['allocationNumber'];
                                //								$transaction['lineNumber'] = 1;

                                unsetCreditedInvoice($view->items);
                                $counter++; // added by arjay
                            }
                        } else {
                            $validationErrors[] = 'You cannot enter a negative amount - to credit, simply enter the amount to apply and tick the corresponding box.';
                        }
                    } elseif ($row['amount'] < $row['appliedAmount']) {
                        $validationErrors[] = 'One of your applied amounts to credit is greater than the unpaid amount';
                    }
                }

                if (isset($view->items['generateInterimInvoice'])) {
                    $dataSet_1 = [
                        'leaseID' => $view->items['leaseID'],
                        'propertyID' => $view->items['propertyID'],
                        'dueDate' => $view->items['dueDate'],
                        'transactionDate' => $view->items['transactionDate'],
                        'batchNumber' => $view->items['batchNumber'],
                    ];
                    $_SESSION['forGenerateInterimInvoice'] = $dataSet_1;
                    clientSideRedirect(
                        '?action=completeTransaction2&command=invoice&module=ar'
                    ); // added by arjay 04-08-2017
                } else {
                    if ($counter) {
                        clientSideRedirect(
                            '?type=' . TYPE_CREDIT . '&action=completeTransaction&command=invoice&module=ar'
                        );
                    } // added by arjay
                }
            }

            break;
        case 'calculateCharge':
            // -- if you click on the calculate button (multiple charges) calculate the new amount
            $leaseList = extractFields(
                $view->items,
                [
                    'amount',
                    'unitArea',
                    'applyAmount',
                    'leaseID',
                    'leaseName',
                    'leaseType',
                    'percentage',
                    'unitDescription',
                    'unitFromDate',
                    'unitToDate',
                    'daysCharged',
                    'unitID',
                    'grossAmount',
                ]
            );
            $propertySplitList = dbGetPropertyRecoverableList(
                $view->items['propertyID'],
                $view->items['fromDate'],
                $view->items['toDate'],
                $view->items['accountID']
            );
            $totalAmount = 0;
            foreach ($leaseList as $id => $lease) {
                switch ($view->items['percentageType']) {
                    case MULTIPLECHARGE_UNIT:
                    case MULTIPLECHARGE_PERCENTAGE:
                        $leaseList[$id]['amount'] = round($view->items['netAmount'] * ($lease['percentage'] / 100), 2);
                        break;
                    case MULTIPLECHARGE_PROPERTY_SPLIT:

                        if ($propertySplitList) {
                            $leaseList[$id]['amount'] = round(
                                $view->items['netAmount'] * ($lease['percentage'] / 100),
                                2
                            );
                        } else {
                            $leaseList[$id]['percentage'] = 0;
                            $leaseList[$id]['amount'] = 0;
                        }

                        break;
                    case MULTIPLECHARGE_MANUAL:
                        break;
                }

                $totalAmount += $leaseList[$id]['amount'];
            }

            $view->items['totalAmount'] = $totalAmount;
            if ($view->items['percentageType'] == MULTIPLECHARGE_MANUAL) {
                $view->items['netAmount'] = round($totalAmount, 2);
                $view->items['taxAmount'] = round(
                    ($taxRates[$view->items['taxRateID']] / 100) * $view->items['netAmount'],
                    2
                );
                $view->items['grossAmount'] = bcadd($view->items['taxAmount'], $view->items['netAmount'], 2);
            }

            $view->items['leaseList'] = $leaseList;
            break;
        case 'addLine2':
            if (! isValid($view->items['propertyID'], TEXT_KEY, false)) {
                $validationErrors[] = 'You need to select a property';
            }

            // if (!isValid ($view->items['leaseID'], TEXT_KEY, false)) $validationErrors[] = 'You need to select a lease';
            if (! isValid($view->items['accountID'], TEXT_KEY, false)) {
                $validationErrors[] = 'You need to select an account';
            }

            if (! in_array($view->items['accountID'], $accountCodes, true)) {
                $validationErrors[] = 'You may only choose from one of the available account codes';
            } // -- changed to strict to split 101 from 0101
            // if (!isValid ($view->items['refNumber'], TEXT_KEY, false)) $validationErrors[] = 'You need to select an AP Transaction';
            if (isset($view->items['refNumber']) == true && ! in_array($view->items['refNumber'], $refNumbers, true)) {
                $validationErrors[] = 'You may only choose from one of the available AP Transactions';
            }

            if (! isValid($view->items['transactionType'], TEXT_KEY, false)) {
                $validationErrors[] = 'You need to choose a transaction type - invoice or credit';
            }

            if (! isValid($view->items['description'], TEXT_HTML, false)) {
                $validationErrors[] = 'You need to provide a description';
            }

            if (! isValid($view->items['transactionDate'], TEXT_SMARTDATE, false)) {
                $validationErrors[] = 'You need to choose a transaction date';
            }

            if (! isValid($view->items['fromDate'], TEXT_SMARTDATE, false)) {
                $validationErrors[] = 'You need to choose a from date';
            }

            if (! isValid($view->items['toDate'], TEXT_SMARTDATE, false)) {
                $validationErrors[] = 'You need to choose a to date';
            }

            if ($view->items['hiddenCreateInterimInvoice'] == 1) {
                if ($view->items['dueDate'] == '') {
                    $validationErrors[] = 'You need to choose a due date';
                }

                if ($view->items['dueDate'] != '' && dayDiff($view->items['transactionDate'], $view->items['dueDate']) < 0) {
                    $validationErrors[] = 'Due date must be on or after transaction date';
                }
            }

            if (! isValid($view->items['taxRateID'], TEXT_LOOSE, false)) {
                $validationErrors[] = 'You need to choose a tax rate';
            }

            if (! isValid($view->items['netAmount'], TEXT_FLOAT, false)) {
                $validationErrors[] = 'You need to choose an amount to invoice or credit';
            }

            if (toDateStamp($view->items['toDate']) < toDateStamp($view->items['fromDate'])) {
                $validationErrors[] = 'Your to date cannot be before your from date';
            }

            if ($view->items['netAmount'] == 0) {
                $validationErrors[] = 'You need to choose an amount to invoice or credit.';
            } elseif ($view->items['netAmount'] < 0) {
                $validationErrors[] = 'You cannot enter a negative amount - to credit, simply select credit as the transaction type and enter the amount to apply.';
            }

            // -- validate the multiple charge entries
            if ($view->items['chargeEntryType'] == MULTIPLE_CHARGES) {
                $leaseList = extractFields(
                    $view->items,
                    [
                        'amount',
                        'unitArea',
                        'applyAmount',
                        'tenantType',
                        'leaseID',
                        'leaseName',
                        'leaseType',
                        'percentage',
                        'unitDescription',
                        'unitFromDate',
                        'unitToDate',
                        'daysCharged',
                        'unitID',
                        'grossAmount',
                    ]
                );
                $total = 0;
                $grossTotal = 0;

                foreach ($leaseList as $lease) {
                    $total = bcadd($total, $lease['amount'], 2);
                    $grossTotal = bcadd($grossTotal, $lease['grossAmount'], 2);
                }

                if ($view->items['computationType'] != 'NET') {
                    if ((round($grossTotal, 2) !== round(
                        $view->items['grossAmount'],
                        2
                    )) && $view->items['percentageType'] != '4') {
                        $validationErrors[] = 'The sum of all allocated individual charges totaling ' . toMoney(
                            $grossTotal
                        ) . ' does not match the specified Gross Amount of ' . toMoney(
                            $view->items['grossAmount']
                        ) . '.';
                    } // ---U
                } else {
                    if ((round($total, 2) !== round(
                        $view->items['netAmount'],
                        2
                    )) && $view->items['percentageType'] != '4') {
                        $validationErrors[] = 'The sum of all allocated individual charges totaling ' . toMoney(
                            $total
                        ) . ' does not match the specified Net Amount of ' . toMoney(
                            $view->items['netAmount']
                        ) . '.';
                    } // ---U
                }

                // if ((round ($total, 2) != round ($view->items['netAmount'], 2)) && $view->items['percentageType'] != '4') $validationErrors[] = 'The total of all individual charges allocated ' . toMoney ($total) . ' does not equal the net amount specified ' . toMoney ($view->items['netAmount']) . '.'; //---O
            } else {
                if (! isValid($view->items['leaseID'], TEXT_KEY, false)) {
                    $validationErrors[] = 'You have not supplied a valid Lease';
                }

                if (! isValid($view->items['debtorID'], TEXT_KEY, false)) {
                    $validationErrors[] = 'You need to select a debtor';
                }
            }

            // $calendar = getPeriod ($view->items['propertyID'], $view->items['transactionDate']);
            // if (!$calendar) $calendar = dbGetNextOpenPeriod ($view->items['propertyID'], $view->items['transactionDate']);
            $calendar = dbGetPeriod($view->items['propertyID'], $view->items['transactionDate']);

            if (! $calendar) {
                $validationErrors[] = 'A period (calendar entry) cannot be found for this transaction date.';
            }

            if ($calendar['closed']) {
                $validationErrors[] = 'Your transaction date is within a closed period. Please review and amend.';
            }

            // if (!isset ($view->items['batchNumber'])) $view->items['batchNumber'] = dbGetNextInvoiceAndCreditBatchNumber(GL_SOURCE_AR);//dbGetNextReceivableBatchNumber ();

            $transaction = [];
            $transaction['propertyID'] = $view->items['propertyID'];
            // $transaction['leaseID'] = $view->items['leaseID'];
            $transaction['accountID'] = $view->items['accountID'];
            $transaction['transactionDate'] = $view->items['transactionDate'];
            $transaction['dueDate'] = ($view->items['hiddenCreateInterimInvoice'] == 1 ? $view->items['dueDate'] : null); // $view->items['dueDate']; //--original
            $transaction['createUser'] = $_SESSION['un'];
            $transaction['createDate'] = TODAY;
            // $transaction['debtorID'] = $view->items['debtorID'];
            $bankDetails = dbGetBankDetails($view->items['propertyID']);
            $transaction['bankID'] = $bankDetails['bankID'];
            // $transaction['unitID'] = dbGetUnit ($view->items['propertyID'], $view->items['leaseID']);

            $transaction['description'] = $view->items['description'];

            $factor = 1;
            if ($view->items['transactionType'] == TYPE_CREDIT) {
                $factor = -1;
            }

            // $transaction['transactionAmount'] = $view->items['grossAmount'] * $factor;
            // $transaction['netAmount'] = $view->items['netAmount'] * $factor;
            // $transaction['taxAmount'] = $view->items['taxAmount'] * $factor;

            $transaction['taxCode'] = $view->items['taxRateID'];

            $transaction['transactionType'] = $view->items['transactionType'];
            $transaction['fromDate'] = $view->items['fromDate'];
            $transaction['toDate'] = $view->items['toDate'];

            $transaction['invoiceNumber'] = '';
            $transaction['invoiceDate'] = null;

            $transaction['batchNumber'] = $view->items['batchNumber'];

            $transaction['transactionYear'] = $calendar['year'];
            $transaction['transactionPeriod'] = $calendar['period'];

            if (! isset($view->items['tempDocumentBatchNo'])) {
                $view->items['tempDocumentBatchNo'] = uniqid();
            }

            if (noErrors($validationErrors)) {
                switch ($view->items['chargeEntryType']) {
                    case SINGLE_CHARGE:
                        // $transaction['lineNumber'] = //dbGetNextReceivableLineNumber($view->items['batchNumber']);
                        $transaction['leaseID'] = $view->items['leaseID'];
                        $transaction['debtorID'] = $view->items['debtorID'];
                        $transaction['unitID'] = dbGetUnit($view->items['propertyID'], $view->items['leaseID']);
                        $factor = 1;
                        if ($view->items['transactionType'] == TYPE_CREDIT) {
                            $factor = -1;
                        }

                        $transaction['transactionAmount'] = $view->items['grossAmount'] * $factor;
                        $transaction['netAmount'] = round($view->items['netAmount'] * $factor, 2);
                        $transaction['taxAmount'] = $view->items['taxAmount'] * $factor;
                        $transaction['dueDate'] = ($view->items['hiddenCreateInterimInvoice'] == 1 ? $view->items['dueDate'] : null); // $view->items['dueDate']; //--original

                        /**for ap transaction tagging**/
                        // $data=json_decode($view->items['jsonAr']);
                        /**for ap transaction tagging**/
                        $data = json_decode($view->items['jsonAr']);
                        // $transaction['recovered']=null;
                        $transaction['ap_recovered'] = $view->items['jsonAr'];
                        $transaction['apRecoveredInvoiceNo'] = null;
                        $transaction['apRecoveredSupplier'] = null;
                        // $length = count($data);
                        // if ($length > 0) $transaction['recovered'] = 1;
                        /*********end tagging********/

                        // dbInsertReceivableTransaction ($transaction);
                        /**for ap transaction tagging**/
                        // $length = count ($data);
                        // $apInfoAr = array ();
                        // $r = array('batch'=>$transaction['batchNumber'],'line'=>$transaction['lineNumber']);

                        // array_push ($apInfoAr, $r);
                        /* if($length > 0){
                            $transaction['recovered']=1;
                            foreach($data as $key=>$val){

                                dbUpdateTransactionForRecovered ('ap_transaction',1,$val->batch,$val->line,json_encode($apInfoAr),$_SESSION['un']);
                            }
                        }

                        $view->items['jsonAr']=null;*/
                        echo json_encode(
                            [
                                'transaction' => $transaction,
                                'batch' => $view->items['batchNumber'],
                                'documentBatch' => $view->items['tempDocumentBatchNo'],
                            ]
                        );
                        exit();
                    case MULTIPLE_CHARGES:

                        $leaseList = extractFields(
                            $view->items,
                            [
                                'amount',
                                'unitArea',
                                'applyAmount',
                                'leaseID',
                                'leaseName',
                                'leaseType',
                                'percentage',
                                'unitDescription',
                                'unitFromDate',
                                'unitToDate',
                                'daysCharged',
                                'unitID',
                                'grossAmount',
                            ]
                        );
                        $taxRate = $taxRates[$view->items['taxRateID']];

                        /**for ap transaction tagging**/
                        $data = json_decode($view->items['jsonAr']);
                        $transaction['recovered'] = null;
                        $transaction['ap_recovered'] = $view->items['jsonAr'];
                        $transaction['apRecoveredInvoiceNo'] = null;
                        $transaction['apRecoveredSupplier'] = null;
                        $length = count($data);
                        if ($length > 0) {
                            $transaction['recovered'] = 1;
                        }

                        /************end****************/

                        $infoAr = [];
                        /************end****************/

                        foreach ($leaseList as $lease) {
                            if (($lease['leaseID']) && ($lease['amount'] != 0)) {
                                // $transaction['lineNumber'] = dbGetNextReceivableLineNumber ($view->items['batchNumber']);
                                $taxRate = ($lease['tenantType'] == 'RESI') ? $taxRates['GSTFREE'] : $taxRate;
                                $transaction['leaseID'] = $lease['leaseID'];
                                $transaction['debtorID'] = dbGetDebtor($view->items['propertyID'], $lease['leaseID']);
                                $transaction['unitID'] = dbGetUnit($view->items['propertyID'], $lease['leaseID']);
                                $factor = 1;
                                if ($view->items['transactionType'] == TYPE_CREDIT) {
                                    $factor = -1;
                                }

                                $transaction['netAmount'] = $lease['amount'] * $factor;
                                $transaction['taxAmount'] = (round($lease['amount'] * ($taxRate / 100), 2)) * $factor;
                                if ($lease['amount'] < 0 && $transaction['transactionType'] != TYPE_CREDIT) {
                                    $transaction['transactionType'] = TYPE_CREDIT;
                                } else {
                                    $transaction['transactionType'] = $view->items['transactionType'];
                                }

                                $transaction['transactionAmount'] = bcadd(
                                    $transaction['netAmount'],
                                    $transaction['taxAmount'],
                                    2
                                );
                                $transaction['dueDate'] = ($view->items['hiddenCreateInterimInvoice'] == 1 ? $view->items['dueDate'] : null); // $view->items['dueDate']; //--original
                                $infoAr[] = $transaction;
                            }
                        }

                        echo json_encode(
                            [
                                'transaction' => $infoAr,
                                'batch' => $view->items['batchNumber'],
                                'documentBatch' => $view->items['tempDocumentBatchNo'],
                            ]
                        );
                        exit();
                }
            } else {
                // echo json_encode ($validationErrors);//original
                echo json_encode(
                    [
                        'errors' => $validationErrors,
                        'batch' => $view->items['batchNumber'],
                        'documentBatch' => $view->items['tempDocumentBatchNo'],
                    ]
                );
                exit();
            }

            break;
        case 'addLine':
            if (! isValid($view->items['propertyID'], TEXT_KEY, false)) {
                $validationErrors[] = 'You need to select a property';
            }

            // if (!isValid ($view->items['leaseID'], TEXT_KEY, false)) $validationErrors[] = 'You need to select a lease';
            if (! isValid($view->items['accountID'], TEXT_KEY, false)) {
                $validationErrors[] = 'You need to select an account';
            }

            if (! in_array($view->items['accountID'], $accountCodes, true)) {
                $validationErrors[] = 'You may only choose from one of the available account codes';
            } // -- changed to strict to split 101 from 0101
            // if (!isValid ($view->items['refNumber'], TEXT_KEY, false)) $validationErrors[] = 'You need to select an AP Transaction';
            if (isset($view->items['refNumber']) == true && ! in_array($view->items['refNumber'], $refNumbers, true)) {
                $validationErrors[] = 'You may only choose from one of the available AP Transactions';
            }

            if (! isValid($view->items['transactionType'], TEXT_KEY, false)) {
                $validationErrors[] = 'You need to choose a transaction type - invoice or credit';
            }

            if (! isValid($view->items['description'], TEXT_HTML, false)) {
                $validationErrors[] = 'You need to provide a description';
            }

            if (! isValid($view->items['transactionDate'], TEXT_SMARTDATE, false)) {
                $validationErrors[] = 'You need to choose a transaction date';
            }

            if (! isValid($view->items['fromDate'], TEXT_SMARTDATE, false)) {
                $validationErrors[] = 'You need to choose a from date';
            }

            if (! isValid($view->items['toDate'], TEXT_SMARTDATE, false)) {
                $validationErrors[] = 'You need to choose a to date';
            }

            if (! isValid($view->items['taxRateID'], TEXT_LOOSE, false)) {
                $validationErrors[] = 'You need to choose a tax rate';
            }

            if (! isValid($view->items['netAmount'], TEXT_FLOAT, false)) {
                $validationErrors[] = 'You need to choose an amount to invoice or credit';
            }

            if (toDateStamp($view->items['toDate']) < toDateStamp($view->items['fromDate'])) {
                $validationErrors[] = 'Your to date cannot be before your from date';
            }

            if ($view->items['netAmount'] == 0) {
                $validationErrors[] = 'You need to choose an amount to invoice or credit.';
            } elseif ($view->items['netAmount'] < 0) {
                $validationErrors[] = 'You cannot enter a negative amount - to credit, simply select credit as the transaction type and enter the amount to apply.';
            }

            // -- validate the multiple charge entries
            if ($view->items['chargeEntryType'] == MULTIPLE_CHARGES) {
                $leaseList = extractFields(
                    $view->items,
                    [
                        'amount',
                        'unitArea',
                        'applyAmount',
                        'leaseID',
                        'leaseName',
                        'leaseType',
                        'percentage',
                        'unitDescription',
                        'unitFromDate',
                        'unitToDate',
                        'daysCharged',
                        'unitID',
                        'grossAmount',
                    ]
                );
                $total = 0;
                foreach ($leaseList as $lease) {
                    $total = bcadd($total, $lease['amount'], 2);
                }

                if ((round($total, 2) !== round(
                    $view->items['netAmount'],
                    2
                )) && $view->items['percentageType'] != '4') {
                    $validationErrors[] = 'The sum of all allocated individual charges totaling ' . toMoney(
                        $total
                    ) . ' does not match the specified Net Amount of ' . toMoney($view->items['netAmount']) . '.';
                }

                // ---U
                // if ((round ($total, 2) != round ($view->items['netAmount'], 2)) && $view->items['percentageType'] != '4') $validationErrors[] = 'The total of all individual charges allocated ' . toMoney ($total) . ' does not equal the net amount specified ' . toMoney ($view->items['netAmount']) . '.'; //---O
            } else {
                if (! isValid($view->items['leaseID'], TEXT_KEY, false)) {
                    $validationErrors[] = 'You have not supplied a valid Lease';
                }

                if (! isValid($view->items['debtorID'], TEXT_KEY, false)) {
                    $validationErrors[] = 'You need to select a debtor';
                }
            }

            // $calendar = getPeriod ($view->items['propertyID'], $view->items['transactionDate']);
            // if (!$calendar) $calendar = dbGetNextOpenPeriod ($view->items['propertyID'], $view->items['transactionDate']);
            $calendar = dbGetPeriod($view->items['propertyID'], $view->items['transactionDate']);

            if (! $calendar) {
                $validationErrors[] = 'A period (calendar entry) cannot be found for this transaction date.';
            }

            if ($calendar['closed']) {
                $validationErrors[] = 'Your transaction date is within a closed period. Please review and amend.';
            }

            if (! isset($view->items['batchNumber'])) {
                $view->items['batchNumber'] = dbGetNextInvoiceAndCreditBatchNumber(GL_SOURCE_AR);
            }// dbGetNextReceivableBatchNumber ();

            $transaction = [];
            $transaction['propertyID'] = $view->items['propertyID'];
            $transaction['leaseID'] = $view->items['leaseID'];
            $transaction['accountID'] = $view->items['accountID'];
            $transaction['transactionDate'] = $view->items['transactionDate'];
            $transaction['dueDate'] = $view->items['dueDate'];
            $transaction['createUser'] = $_SESSION['un'];
            $transaction['createDate'] = TODAY;
            // $transaction['debtorID'] = $view->items['debtorID'];
            $bankDetails = dbGetBankDetails($view->items['propertyID']);
            $transaction['bankID'] = $bankDetails['bankID'];
            // $transaction['unitID'] = dbGetUnit ($view->items['propertyID'], $view->items['leaseID']);

            $transaction['description'] = $view->items['description'];

            $factor = 1;
            if ($view->items['transactionType'] == TYPE_CREDIT) {
                $factor = -1;
            }

            // $transaction['transactionAmount'] = $view->items['grossAmount'] * $factor;
            // $transaction['netAmount'] = $view->items['netAmount'] * $factor;
            // $transaction['taxAmount'] = $view->items['taxAmount'] * $factor;

            $transaction['taxCode'] = $view->items['taxRateID'];

            $transaction['transactionType'] = $view->items['transactionType'];
            $transaction['fromDate'] = $view->items['fromDate'];
            $transaction['toDate'] = $view->items['toDate'];

            $transaction['invoiceNumber'] = '';
            $transaction['invoiceDate'] = null;

            $transaction['batchNumber'] = $view->items['batchNumber'];

            $transaction['transactionYear'] = $calendar['year'];
            $transaction['transactionPeriod'] = $calendar['period'];

            if (noErrors($validationErrors)) {
                switch ($view->items['chargeEntryType']) {
                    case SINGLE_CHARGE:
                        $transaction['lineNumber'] = dbGetNextReceivableLineNumber($view->items['batchNumber']);
                        $transaction['leaseID'] = $view->items['leaseID'];
                        $transaction['debtorID'] = $view->items['debtorID'];
                        $transaction['unitID'] = dbGetUnit($view->items['propertyID'], $view->items['leaseID']);
                        $factor = 1;
                        if ($view->items['transactionType'] == TYPE_CREDIT) {
                            $factor = -1;
                        }

                        $transaction['transactionAmount'] = $view->items['grossAmount'] * $factor;
                        $transaction['netAmount'] = $view->items['netAmount'] * $factor;
                        $transaction['taxAmount'] = $view->items['taxAmount'] * $factor;
                        $transaction['dueDate'] = $view->items['dueDate'];

                        /** trial balance **/
                        if (GL_ACTIVE) {
                            $gl = new GeneralLedger();
                            $gl->transactionType = $transaction['transactionType'];
                            $gl->transactionDate = $transaction['transactionDate'];
                            $gl->description = $transaction['description'];
                            $gl->year = $calendar['year'];
                            $gl->period = $calendar['period'];
                            $gl->propertyID = $transaction['propertyID'];
                            $gl->leaseID = $transaction['leaseID'];
                            $gl->companyID = $transaction['debtorID'];
                            $gl->source = GL_SOURCE_AR;
                            $gl->fromDate = $transaction['fromDate'];
                            $gl->toDate = $transaction['toDate'];
                            $gl->batchID = $transaction['batchNumber'];
                            $gl->lineNumber = $transaction['lineNumber'];

                            $t = new Transaction($gl); // -- detail entry : adopt the data from the

                            $_amount = bcmul(-1, $transaction['netAmount'], 2);
                            $gl->update($view->items['accountID'], GL_BALANCE_ACCRUALS, $_amount);
                            $t->add($view->items['accountID'], BASIS_ACCRUALS, $_amount);

                            $_amount = bcmul(1, $transaction['transactionAmount'], 2);
                            $gl->update(glAccount(GL_DEBTORS_CONTROL), GL_BALANCE_ACCRUALS, $_amount);
                            $t->add(glAccount(GL_DEBTORS_CONTROL), BASIS_ACCRUALS, $_amount, true);

                            $_amount = bcmul(-1, $transaction['taxAmount'], 2);
                            $gl->update(glAccount(GL_GST_OUTPUT), GL_BALANCE_ACCRUALS, $_amount);
                            $t->add(glAccount(GL_GST_OUTPUT), BASIS_ACCRUALS, $_amount);
                        }

                        /**for ap transaction tagging**/
                        $data = json_decode($view->items['jsonAr']);
                        $transaction['recovered'] = null;
                        $transaction['ap_recovered'] = $view->items['jsonAr'];
                        $transaction['apRecoveredInvoiceNo'] = null;
                        $transaction['apRecoveredSupplier'] = null;
                        /*********end tagging********/

                        dbInsertReceivableTransaction($transaction);


                        $transaction['documentID'] = $view->items['documentID'];
                        $transaction['propertyID'] = $view->items['propertyID'];
                        $transaction['leaseID'] = $view->items['leaseID'];
                        $transaction['apBatchNumber'] = $view->items['apBatchNumber'];
                        dbInsertLinkedInvoiceDocumentFromAP($transaction);
                        // dbDeleteLinkedInvoiceDocumentFromAP($transaction);


                        /**for ap transaction tagging**/
                        $length = count($data ?? []);
                        $apInfoAr = [];
                        $r = ['batch' => $transaction['batchNumber'], 'line' => $transaction['lineNumber']];

                        $apInfoAr[] = $r;
                        if ($length > 0) {
                            $transaction['recovered'] = 1;
                            foreach ($data as $val) {
                                dbUpdateTransactionForRecovered(
                                    'ap_transaction',
                                    1,
                                    $val->batch,
                                    $val->line,
                                    json_encode($apInfoAr),
                                    $_SESSION['un']
                                );
                            }
                        }

                        $view->items['jsonAr'] = null;

                        /************end****************/


                        break;
                    case MULTIPLE_CHARGES:
                        $leaseList = extractFields(
                            $view->items,
                            [
                                'amount',
                                'unitArea',
                                'applyAmount',
                                'leaseID',
                                'leaseName',
                                'leaseType',
                                'percentage',
                                'unitDescription',
                                'unitFromDate',
                                'unitToDate',
                                'daysCharged',
                                'unitID',
                                'grossAmount',
                            ]
                        );
                        $taxRate = $taxRates[$view->items['taxRateID']];

                        /**for ap transaction tagging**/
                        $data = json_decode($view->items['jsonAr']);
                        $transaction['recovered'] = null;
                        $transaction['ap_recovered'] = $view->items['jsonAr'];
                        $transaction['apRecoveredInvoiceNo'] = null;
                        $transaction['apRecoveredSupplier'] = null;
                        $length = count($data ?? []);
                        if ($length > 0) {
                            $transaction['recovered'] = 1;
                        }

                        $apInfoAr = [];
                        /************end****************/

                        foreach ($leaseList as $lease) {
                            if (($lease['leaseID']) && ($lease['amount'])) {
                                $transaction['lineNumber'] = dbGetNextReceivableLineNumber($view->items['batchNumber']);
                                $transaction['leaseID'] = $lease['leaseID'];
                                $transaction['debtorID'] = dbGetDebtor($view->items['propertyID'], $lease['leaseID']);
                                $transaction['unitID'] = dbGetUnit($view->items['propertyID'], $lease['leaseID']);
                                $factor = 1;
                                if ($view->items['transactionType'] == TYPE_CREDIT) {
                                    $factor = -1;
                                }

                                $transaction['netAmount'] = $lease['amount'] * $factor;
                                $transaction['taxAmount'] = (round($lease['amount'] * ($taxRate / 100), 2)) * $factor;
                                if ($view->items['computationType'] == 'NET') {
                                    $transaction['transactionAmount'] = bcadd(
                                        $transaction['netAmount'],
                                        $transaction['taxAmount'],
                                        2
                                    );
                                } else {
                                    $transaction['transactionAmount'] = $lease['grossAmount'];
                                }

                                $transaction['dueDate'] = $view->items['dueDate'];
                                $transaction['apBatchNumber'] = $view->items['apBatchNumber'];
                                $transaction['documentID'] = $view->items['documentID'];
                                $recovered = ['batch' => $view->items['apBatchNumber'], 'line' => null];
                                $transaction['apRecovered'] = json_encode($recovered);

                                /** trial balance **/
                                if (GL_ACTIVE) {
                                    $gl = new GeneralLedger();
                                    $gl->transactionType = $transaction['transactionType'];
                                    $gl->transactionDate = $transaction['transactionDate'];
                                    $gl->description = $transaction['description'];
                                    $gl->year = $calendar['year'];
                                    $gl->period = $calendar['period'];
                                    $gl->propertyID = $transaction['propertyID'];
                                    $gl->leaseID = $transaction['leaseID'];
                                    $gl->companyID = $transaction['debtorID'];
                                    $gl->source = GL_SOURCE_AR;
                                    $gl->fromDate = $transaction['fromDate'];
                                    $gl->toDate = $transaction['toDate'];

                                    $gl->batchID = $transaction['batchNumber'];
                                    $gl->lineNumber = $transaction['lineNumber'];

                                    $t = new Transaction($gl); // -- detail entry: adopt the data from the

                                    $_amount = bcmul(-1, $transaction['netAmount'], 2);
                                    $gl->update($view->items['accountID'], GL_BALANCE_ACCRUALS, $_amount);
                                    $t->add($view->items['accountID'], BASIS_ACCRUALS, $_amount);

                                    $_amount = bcmul(1, $transaction['transactionAmount'], 2);
                                    $gl->update(glAccount(GL_DEBTORS_CONTROL), GL_BALANCE_ACCRUALS, $_amount);
                                    $t->add(glAccount(GL_DEBTORS_CONTROL), BASIS_ACCRUALS, $_amount, true);

                                    $_amount = bcmul(-1, $transaction['taxAmount'], 2);
                                    $gl->update(glAccount(GL_GST_OUTPUT), GL_BALANCE_ACCRUALS, $_amount);
                                    $t->add(glAccount(GL_GST_OUTPUT), BASIS_ACCRUALS, $_amount);
                                }

                                dbInsertReceivableTransaction($transaction);

                                $data = dbGetDocumentInvoicePerBatchNumber(
                                    $view->items['apBatchNumber'],
                                    $view->items['propertyID']
                                );
                                foreach ($data as $doc) {
                                    $transaction['documentID'] = $doc['document_id'];
                                    $transaction['propertyID'] = $view->items['propertyID'];
                                    $transaction['leaseID'] = $lease['leaseID'];
                                    $transaction['apBatchNumber'] = $view->items['apBatchNumber'];
                                    dbInsertLinkedInvoiceDocumentFromAP($transaction);
                                }

                                /**for AP Recoverable**/
                                $r = [
                                    'batch' => $view->items['batchNumber'],
                                    'line' => $transaction['lineNumber'],
                                ];
                                $apInfoAr[] = $r;
                                /********end*******/
                            }
                        }

                        /****update AP Recoverable transaction ***/
                        if ($length > 0) {
                            foreach ($data as $val) {
                                dbUpdateTransactionForRecovered(
                                    'ap_transaction',
                                    1,
                                    $val->batch,
                                    $val->line,
                                    json_encode($apInfoAr),
                                    $_SESSION['un']
                                );
                            }
                        }

                        $view->items['jsonAr'] = null;
                        /***************end****************/

                        break;
                }

                // -- if it is an allocated credit, insert into the allocation table to offset the invoice
                /*
                    if (isset ($view->items['toBatchNumber']))
                    {
                        $transaction['allocationDate'] = TODAY;
                        $transaction['allocationNumber'] = dbGetNextAllocationNumber ();
                        $transaction['fromBatchNumber'] = $transaction['batchNumber'];
                        $transaction['fromLineNumber'] = $transaction['lineNumber'];
                        $transaction['toBatchNumber'] = $view->items['toBatchNumber'];
                        $transaction['toLineNumber'] = $view->items['toLineNumber'];
                        $transaction['currentDate'] = TODAY;
                        $transaction['currentTime'] = TODAY;
                        dbAllocateTransaction ($transaction);
                        $view->items['statusMessage'] = 'You have successfully offset the invoice (#' . $view->items['invoiceNumber'] . ')';
                        unsetCreditedInvoice ($view->items);
                    }
                */
            }

            break;
        case 'generateInvoice':
            // pre_print_r($view->items); die;
            $view->items['batchNumber'] = completeTransaction($view->items);
            $leases = dbGetTransactionLeases($view->items['propertyID']);
            $logoFile = dbGetClientLogo();
            $logoPath = ($view->items['logo']) ? "assets/clientLogos/{$logoFile}" : null;

            $updDocInvData = [];

            // list ($day, $month, $year) = explode ('/', $view->items['transactionDate']);
            [$day, $month, $year] = explode(
                '/',
                ($view->items['dueDate']) ? $view->items['dueDate'] : $view->items['transactionDate']
            );
            $filename = "tax_invoice_{$year}{$month}{$day}_" . $view->items['propertyID'] . '.pdf';
            $filePath = "{$pathPrefix}{$clientDirectory}/pdf/TaxInvoice/{$filename}";
            $downloadPath = "{$clientDirectory}/pdf/TaxInvoice/{$filename}";
            $masterInvoice = new PDFReport($filePath, $logoPath);

            if ($view->items['chargeEntryType'] == SINGLE_CHARGE) {
                $downloadPath = '';
                $success_ctr = 0;
                $fail_ctr = 0;

                $details = prepareTaxInvoice(
                    InvoiceStatementType::ST_INVOICE,
                    $view->items['leaseID'],
                    $view->items['propertyID'],
                    ($view->items['dueDate']) ? $view->items['dueDate'] : $view->items['transactionDate'],
                    $view->items['transactionDate'],
                    $masterInvoice,
                    $view->items['batchNumber'],
                    $view->items['notation'],
                    $view->items['logo']
                );
                // pre_print_r($details); //uncomment only for testing invoice number and download path checking

                // ## Update documents table for the generated invoice number from prepareTaxInvoice() - START
                $updDocInvData['invoiceNumber'] = $details['invoiceNumber'];
                $updDocInvData['arBatchNumber'] = $view->items['batchNumber'];
                dbUpdateInvNoInDocInvoiceViaArBatchNum($updDocInvData);
                // ## Update documents table for the generated invoice number from prepareTaxInvoice() - END
                // ## Send Email AR - START
                // # Check if send email to tenants is true
                if ($details['filePath'] && $view->items['hiddenSendToTenant'] == 1) {
                    $downloadPath = encodeParameter($details['downloadPath']);
                    $leaseDetails = dbGetLeaseDetails($view->items['propertyID'], $view->items['leaseID']);
                    // echo 'send email to tenants is TRUE';
                    if (! is_file(BASEPATH . "/framework/views/emails/{$clientDB}/email_layout3.html")) {
                        $email = new EmailTemplate('views/emails/tenantTaxInvoiceEmail.html', SYSTEMURL);
                    } else {
                        $email = new EmailTemplate("views/emails/{$clientDB}/email_layout3.html", SYSTEMURL);
                    }

                    $email->items['clientName'] = dbGetClientName();
                    $email->items['leaseCode'] = $view->items['leaseID'];
                    // $email->items['leaseName'] = $data_['leaseName'];
                    $email->items['leaseName'] = $view->items['leaseID'];
                    // 'Test Lease Name';
                    $email->items['notes'] = explode("\n", $view->items['notation']);
                    $email->items['PropertyCode'] = $view->items['propertyID'];
                    $email->items['TenantCode'] = $view->items['leaseID'];
                    $email->items['SalutationName'] = dbGetLeaseContactSalutation(
                        $view->items['propertyID'],
                        $view->items['leaseID'],
                        true
                    );
                    $email->items['TenantName'] = $leaseDetails['leaseName'];
                    $email->items['TenantLocation'] = $leaseDetails['leaseDescription'];
                    $subject = dbGetEmailTemplate(3);
                    $subject = str_replace('%PropertyCode%', $view->items['propertyID'], $subject);
                    $subject = str_replace('%TenantCode%', $view->items['leaseID'], $subject);
                    $subject = str_replace('%TenantName%', $leaseDetails['leaseName'], $subject);
                    $subject = str_replace('%TenantLocation%', $leaseDetails['leaseDescription'], $subject);
                    $subject = str_replace('%SalutationName%', $email->items['SalutationName'], $subject);
                    $filePath = $details['filePath'];
                    $attachment = [];
                    $attachmentCount = 0;
                    $attachment[$attachmentCount]['file'] = $filePath;
                    $attachment[$attachmentCount]['content_type'] = 'application/pdf';
                    // $documents = dbGetInvoiceDocuments ($view->items['propertyID'], $view->items['leaseID'], $view->items['transactionDate'], $details['invoiceNumber']); //original
                    $documents = dbGetInvoiceDocuments(
                        $view->items['propertyID'],
                        $view->items['leaseID'],
                        $view->items['dueDate'],
                        $details['invoiceNumber'],
                        false,
                        null,
                        false
                    );
                    if ($documents && is_array($documents)) {
                        foreach ($documents as $v) {
                            $docuFile = $v['filename'];
                            $attachmentCount++;
                            $attachment[$attachmentCount]['file'] = BASEPATH . '/reports/' . $docuFile;
                            $extension = pathinfo($attachment[$attachmentCount]['file'], PATHINFO_EXTENSION);
                            $attachment[$attachmentCount]['content_type'] = 'application/pdf';
                        }
                    }

                    if ($email_cen_send) {
                        $invoiceNumber = $details['invoiceNumber'];
                        $emailType = ($invoiceNumber == '') ? '' : "(#  {$invoiceNumber}) ";
                        $debtorID = dbGetDebtorID($view->items['propertyID'], $view->items['leaseID']);
                        $identifier = ($invoiceNumber) ? emailIdentifier(
                            EMAILREF_INVOICE,
                            $invoiceNumber,
                            $debtorID
                        ) : null;
                        // -- grab the details for the property and get the
                        $office = dbGetOfficeForProperty($view->items['propertyID']);

                        // ***** Retrieve portfolio manager details for CC purposes - START
                        $emailAddressCC = '';
                        $getPortfolioCode = dbGetPropertyPortfolioById($view->items['propertyID']);
                        $emailAddressCC = dbGetParam('PORTMEMAIL', $getPortfolioCode['pmpr_portfolio']); // actual
                        // $emailAddressCC = '<EMAIL>'; //for testing purposes only
                        // ***** Retrieve portfolio manager details for CC purposes - END

                        $emailAddress = dbGetDebtorEmailCentralised(
                            $view->items['propertyID'],
                            $view->items['leaseID'],
                            'TENTAXIN'
                        );
                        $defaultEmailSend = '1';
                        foreach ($emailAddress as $aRowCentralisedEmailCheck) {
                            $defaultEmail = $aRowCentralisedEmailCheck['defaultEmail'];
                            if ($defaultEmail == '0') {
                                $defaultEmailSend = $defaultEmail;
                            }
                        }

                        $email_sent_flag = false;
                        $successfulEmailsMsg = '';
                        foreach ($emailAddress as $aRowCentralisedEmail) {
                            $emailAddressCentral = $aRowCentralisedEmail['emailAddress'];
                            $defaultEmail = $aRowCentralisedEmail['defaultEmail'];
                            if ($defaultEmailSend == $defaultEmail && isValid($emailAddressCentral, TEXT_EMAIL, false)) {
                                $email_sent_flag = true;
                                if ($view->items['hiddenAddCcToPm'] == 1) {
                                    // Yes to CC to PM
                                    if (isValid($emailAddressCC, TEXT_EMAIL, false)) {
                                        // Proceed on CC to PM
                                        // sendMail ($emailAddress, '', $email->toString (),'Tenant Tax Invoice ' .  $emailType . ' from ' . $office['officeName']  . ' for ' . $email->items['leaseName'], $attachment, null, $identifier); //O

                                        $trailLetter =
                                        [
                                            'letterTemplateID' => dbGetLetterCategoryID('Tax Invoice'),
                                            'letterFormat' => 'pdf',
                                            'letterRecipient' => $emailAddressCentral . ($emailAddressCC ? ';' . $emailAddressCC : ''),
                                            'letterEmailSubject' => $subject ? $subject : 'Tenant Tax Invoice ' . $emailType . ' from ' . $office['officeName'] . ' for ' . $email->items['leaseName'],
                                            'letterEmailBody' => $email->toString(),
                                            'letterTemplateBody' => '',
                                            'propertyCode' => $view->items['propertyID'],
                                            'leaseCode' => $view->items['leaseID'],
                                            'userID' => $_SESSION['user_id'],
                                            'dl_path' => $details['downloadPath'],

                                        ];

                                        $parameters['letter_history_id'] = dbInsertTrailLetter($trailLetter);

                                        sendMail(
                                            $emailAddressCentral,
                                            '',
                                            $email->toString(),
                                            $subject ? $subject : 'Tenant Tax Invoice ' . $emailType . ' from ' . $office['officeName'] . ' for ' . $email->items['leaseName'],
                                            $attachment,
                                            null,
                                            $identifier,
                                            false,
                                            $emailAddressCC,
                                            true,
                                            true,
                                            $parameters
                                        ); // U
                                        $successfulEmailsMsg = $successfulEmailsMsg . 'Tenant invoices were successfully sent to the following: <a href="download.php?fileID=' . $downloadPath . '"><img src="' . ASSET_DOMAIN . 'assets/images/icons/pdf.png" alt="Adobe Log" class="icon" /><span style="font-weight: bold;">' . $view->items['leaseID'] . '(' . $emailAddressCentral . ')' . '</span></a><br/>';
                                        $successfulEmailsMsg = $successfulEmailsMsg . 'Tenant invoices were successfully sent to the following: <a href="download.php?fileID=' . $downloadPath . '"><img src="' . ASSET_DOMAIN . 'assets/images/icons/pdf.png" alt="Adobe Log" class="icon" /><span style="font-weight: bold;">' . $view->items['leaseID'] . '(CC:' . $emailAddressCC . ')' . '</span></a><br/>';
                                    } else {
                                        // Send without CC since $currentUserEmailAddress is invalid
                                        $trailLetter =
                                        [
                                            'letterTemplateID' => dbGetLetterCategoryID('Tax Invoice'),
                                            'letterFormat' => 'pdf',
                                            'letterRecipient' => $emailAddressCentral,
                                            'letterEmailSubject' => $subject ? $subject : 'Tenant Tax Invoice ' . $emailType . ' from ' . $office['officeName'] . ' for ' . $email->items['leaseName'],
                                            'letterEmailBody' => $email->toString(),
                                            'letterTemplateBody' => '',
                                            'propertyCode' => $view->items['propertyID'],
                                            'leaseCode' => $view->items['leaseID'],
                                            'userID' => $_SESSION['user_id'],
                                            'dl_path' => $details['downloadPath'],

                                        ];

                                        $parameters['letter_history_id'] = dbInsertTrailLetter($trailLetter);

                                        sendMail(
                                            $emailAddressCentral,
                                            '',
                                            $email->toString(),
                                            $subject ? $subject : 'Tenant Tax Invoice ' . $emailType . ' from ' . $office['officeName'] . ' for ' . $email->items['leaseName'],
                                            $attachment,
                                            null,
                                            $identifier,
                                            false,
                                            false,
                                            true,
                                            true,
                                            $parameters
                                        ); // U
                                        $successfulEmailsMsg = $successfulEmailsMsg . 'Tenant invoices were successfully sent to the following: <a href="download.php?fileID=' . $downloadPath . '"><img src="' . ASSET_DOMAIN . 'assets/images/icons/pdf.png" alt="Adobe Log" class="icon" /><span style="font-weight: bold;">' . $view->items['leaseID'] . '(' . $emailAddressCentral . ')' . '</span></a><br/>';
                                    }
                                } else {
                                    // No to CC to PM
                                    $trailLetter =
                                    [
                                        'letterTemplateID' => dbGetLetterCategoryID('Tax Invoice'),
                                        'letterFormat' => 'pdf',
                                        'letterRecipient' => $emailAddressCentral,
                                        'letterEmailSubject' => $subject ? $subject : 'Tenant Tax Invoice ' . $emailType . ' from ' . $office['officeName'] . ' for ' . $email->items['leaseName'],
                                        'letterEmailBody' => $email->toString(),
                                        'letterTemplateBody' => '',
                                        'propertyCode' => $view->items['propertyID'],
                                        'leaseCode' => $view->items['leaseID'],
                                        'userID' => $_SESSION['user_id'],
                                        'dl_path' => $details['downloadPath'],

                                    ];

                                    $parameters['letter_history_id'] = dbInsertTrailLetter($trailLetter);

                                    sendMail(
                                        $emailAddressCentral,
                                        '',
                                        $email->toString(),
                                        $subject ? $subject : 'Tenant Tax Invoice ' . $emailType . ' from ' . $office['officeName'] . ' for ' . $email->items['leaseName'],
                                        $attachment,
                                        null,
                                        $identifier,
                                        false,
                                        false,
                                        true,
                                        true,
                                        $parameters
                                    ); // U
                                    $successfulEmailsMsg = $successfulEmailsMsg . 'Tenant invoices were successfully sent to the following: <a href="download.php?fileID=' . $downloadPath . '"><img src="' . ASSET_DOMAIN . 'assets/images/icons/pdf.png" alt="Adobe Log" class="icon" /><span style="font-weight: bold;">' . $view->items['leaseID'] . '(' . $emailAddressCentral . ')' . '</span></a><br/>';
                                }
                            }
                        }

                        if (! $email_sent_flag) {
                            $view->items['sendingEmailMsg'] = 'Email address is invalid or not existing. Please check the email settings of the following: <a href="download.php?fileID=' . $downloadPath . '"><img src="' . ASSET_DOMAIN . 'assets/images/icons/pdf.png" alt="Adobe Logo" class="icon" /><span style="font-weight: bold;">' . $view->items['leaseID'] . '</span></a>';
                        } else {
                            $view->items['successfulEmailsMsg'] = $successfulEmailsMsg;
                        }
                    } else {
                        // -- if the debtor email address is valid
                        $emailAddress = dbGetDebtorEmail($view->items['propertyID'], $view->items['leaseID']);
                        // $emailAddress = '<EMAIL>';
                        if (isValid($emailAddress, TEXT_EMAIL, false)) {
                            $invoiceNumber = $details['invoiceNumber'];
                            $emailType = ($invoiceNumber == '') ? '' : "(#  {$invoiceNumber}) ";
                            $debtorID = dbGetDebtorID($view->items['propertyID'], $view->items['leaseID']);
                            $identifier = ($invoiceNumber) ? emailIdentifier(
                                EMAILREF_INVOICE,
                                $invoiceNumber,
                                $debtorID
                            ) : null;
                            // -- grab the details for the property and get the
                            $office = dbGetOfficeForProperty($view->items['propertyID']);

                            // ***** Retrieve portfolio manager details for CC purposes - START
                            $emailAddressCC = '';
                            $getPortfolioCode = dbGetPropertyPortfolioById($view->items['propertyID']);
                            $emailAddressCC = dbGetParam(
                                'PORTMEMAIL',
                                $getPortfolioCode['pmpr_portfolio']
                            ); // actual
                            // $emailAddressCC = '<EMAIL>'; //for testing purposes only
                            // ***** Retrieve portfolio manager details for CC purposes - END

                            if ($view->items['hiddenAddCcToPm'] == 1) {
                                // Yes to CC to PM
                                if (isValid($emailAddressCC, TEXT_EMAIL, false)) {
                                    // Proceed on CC to PM
                                    // sendMail ($emailAddress, '', $email->toString (),'Tenant Tax Invoice ' .  $emailType . ' from ' . $office['officeName']  . ' for ' . $email->items['leaseName'], $attachment, null, $identifier); //O
                                    $trailLetter =
                                    [
                                        'letterTemplateID' => dbGetLetterCategoryID('Tax Invoice'),
                                        'letterFormat' => 'pdf',
                                        'letterRecipient' => $emailAddress . ($emailAddressCC ? ';' . $emailAddressCC : ''),
                                        'letterEmailSubject' => $subject ? $subject : 'Tenant Tax Invoice ' . $emailType . ' from ' . $office['officeName'] . ' for ' . $email->items['leaseName'],
                                        'letterEmailBody' => $email->toString(),
                                        'letterTemplateBody' => '',
                                        'propertyCode' => $view->items['propertyID'],
                                        'leaseCode' => $view->items['leaseID'],
                                        'userID' => $_SESSION['user_id'],
                                        'dl_path' => $details['downloadPath'],

                                    ];

                                    $parameters['letter_history_id'] = dbInsertTrailLetter($trailLetter);

                                    sendMail(
                                        $emailAddress,
                                        '',
                                        $email->toString(),
                                        $subject ? $subject : 'Tenant Tax Invoice ' . $emailType . ' from ' . $office['officeName'] . ' for ' . $email->items['leaseName'],
                                        $attachment,
                                        null,
                                        $identifier,
                                        false,
                                        $emailAddressCC,
                                        true,
                                        true,
                                        $parameters
                                    ); // U
                                } else {
                                    // Send without CC since $currentUserEmailAddress is invalid
                                    $trailLetter =
                                    [
                                        'letterTemplateID' => dbGetLetterCategoryID('Tax Invoice'),
                                        'letterFormat' => 'pdf',
                                        'letterRecipient' => $emailAddress,
                                        'letterEmailSubject' => $subject ? $subject : 'Tenant Tax Invoice ' . $emailType . ' from ' . $office['officeName'] . ' for ' . $email->items['leaseName'],
                                        'letterEmailBody' => $email->toString(),
                                        'letterTemplateBody' => '',
                                        'propertyCode' => $view->items['propertyID'],
                                        'leaseCode' => $view->items['leaseID'],
                                        'userID' => $_SESSION['user_id'],
                                        'dl_path' => $details['downloadPath'],

                                    ];

                                    $parameters['letter_history_id'] = dbInsertTrailLetter($trailLetter);

                                    sendMail(
                                        $emailAddress,
                                        '',
                                        $email->toString(),
                                        $subject ? $subject : 'Tenant Tax Invoice ' . $emailType . ' from ' . $office['officeName'] . ' for ' . $email->items['leaseName'],
                                        $attachment,
                                        null,
                                        $identifier,
                                        false,
                                        false,
                                        true,
                                        true,
                                        $parameters
                                    ); // U
                                }
                            } else {
                                // No to CC to PM
                                $trailLetter =
                                [
                                    'letterTemplateID' => dbGetLetterCategoryID('Tax Invoice'),
                                    'letterFormat' => 'pdf',
                                    'letterRecipient' => $emailAddress,
                                    'letterEmailSubject' => $subject ? $subject : 'Tenant Tax Invoice ' . $emailType . ' from ' . $office['officeName'] . ' for ' . $email->items['leaseName'],
                                    'letterEmailBody' => $email->toString(),
                                    'letterTemplateBody' => '',
                                    'propertyCode' => $view->items['propertyID'],
                                    'leaseCode' => $view->items['leaseID'],
                                    'userID' => $_SESSION['user_id'],
                                    'dl_path' => $details['downloadPath'],

                                ];

                                $parameters['letter_history_id'] = dbInsertTrailLetter($trailLetter);

                                sendMail(
                                    $emailAddress,
                                    '',
                                    $email->toString(),
                                    $subject ? $subject : 'Tenant Tax Invoice ' . $emailType . ' from ' . $office['officeName'] . ' for ' . $email->items['leaseName'],
                                    $attachment,
                                    null,
                                    $identifier,
                                    false,
                                    false,
                                    true,
                                    true,
                                    $parameters
                                ); // U
                            }

                            // $view->items['successfulEmailsMsg'] = "Tenant invoices were successfully sent to the following: <span style='font-weight: bold;'>".implode(',', $mul_leases_has_email)."</span>";
                            $view->items['successfulEmailsMsg'] = 'Tenant invoices were successfully sent to the following: <a href="download.php?fileID=' . $downloadPath . '"><img src="' . ASSET_DOMAIN . 'assets/images/icons/pdf.png" alt="Adobe Log" class="icon" /><span style="font-weight: bold;">' . $view->items['leaseID'] . '</span></a>';
                            $success_ctr++;
                        } else {
                            // if email is not existing or invalid
                            // //$view->items['sendingEmailVal'] = isValid($emailAddress, TEXT_EMAIL, false); //NOTE: uncomment only for testing purposes
                            $view->items['sendingEmailMsg'] = 'Email address is invalid or not existing. Please check the email settings of the following: <a href="download.php?fileID=' . $downloadPath . '"><img src="' . ASSET_DOMAIN . 'assets/images/icons/pdf.png" alt="Adobe Logo" class="icon" /><span style="font-weight: bold;">' . $view->items['leaseID'] . '</span></a>';
                            $fail_ctr++;
                        }
                    }
                }

                $masterInvoice->_close();

                // ## Send Email AR - END

                if ($details) {
                    $view->items['downloadLink'] = $details['downloadPath'];
                }
            } else {
                $mul_emails = [];
                $mul_leases_no_email = [];
                $mul_leases_has_email = [];
                $has_invalid_emails = false;
                $success_ctr = 0;
                $fail_ctr = 0;

                $mul_download_path = '';

                /**
                 * Note: Display full query in fn dbGetInvoiceCharges() in ar/dbInterface.php to check all line entries for tax invoicing in AR Invoice & Credit
                 */

                /**
                 * //echo '<br>[MULTIPLE][BEFORE] Transaction Date:'.$view->items['transactionDate'].'</br>';
                 * //echo '<br>[MULTIPLE][BEFORE] Due Date:'.$view->items['dueDate'].'</br>';
                 **/
                $greater_trans_date = $view->items['transactionDate'];
                $line_json = json_decode($view->items['invoiceLinesJson'], true);
                foreach (array_reverse($line_json) as $v) {
                    $trans = [];
                    $curtimestamp1 = date('Y-m-d', strtotime(str_replace('/', '-', $greater_trans_date)));
                    $curtimestamp2 = date('Y-m-d', strtotime(str_replace('/', '-', $v['transactionDate'])));

                    /**
                     * //echo '<br>===trans date from SINGLE LAST LINE: '.$greater_trans_date.'===</br>';
                     * //echo '<br>===trans date from LOOP: '.$v['transactionDate'].'===</br>';
                     * //echo '<br>===CURTIMESTAMP2: '.$curtimestamp2.' vs CURTIMESTAMP1: '.$curtimestamp1.'===</br>';
                     **/
                    if (strtotime($curtimestamp2) > strtotime($curtimestamp1)) {
                        $greater_trans_date = $v['transactionDate'];
                    }
                }

                /**
                 * //echo '<br>[MULTIPLE][AFTER] Transaction Date:'.$view->items['transactionDate'].'</br>';
                 * //echo '<br>[MULTIPLE][AFTER] Transaction Date:'.$greater_trans_date.'</br>';
                 * //echo '<br>[MULTIPLE][AFTER] Due Date:'.$view->items['dueDate'].'</br>';
                 **/

                /** UPDATE value of $view->items['transactionDate']: **/
                $view->items['transactionDate'] = $greater_trans_date;
                // $view->items['transactionDate'] = '16/12/2020'; //uncomment only when testing

                foreach ($leases as $lease) {
                    // $invoiceNumber = bookInvoiceNumber ();
                    $details = prepareTaxInvoice(
                        InvoiceStatementType::ST_INVOICE,
                        $lease['leaseID'],
                        $view->items['propertyID'],
                        ($view->items['dueDate']) ? $view->items['dueDate'] : $view->items['transactionDate'],
                        $view->items['transactionDate'],
                        $masterInvoice,
                        $view->items['batchNumber'],
                        $view->items['notation'],
                        $view->items['logo']
                    );
                    if ($details) {
                        $detailsCount++;
                    }

                    if ($details['invoiceLineCount']) {
                        $InvoiceArray[] = $details['filePath'];
                    }

                    // $leases[$id]['invoiceNumber'] = $invoiceNumber;

                    // ## Update documents table for the generated invoice number from prepareTaxInvoice() - START
                    $updDocInvData['invoiceNumber'] = $details['invoiceNumber'];
                    $updDocInvData['arBatchNumber'] = $view->items['batchNumber'];
                    $updDocInvData['leaseID'] = $lease['leaseID'];
                    dbUpdateInvNoInDocInvoiceViaArBatchNum($updDocInvData);
                    // ## Update documents table for the generated invoice number from prepareTaxInvoice() - END

                    // ## Send Email AR - START
                    if ($details['filePath']) {
                        $mul_download_path = encodeParameter($details['downloadPath']);

                        // # Check if send email to tenants is true
                        if ($view->items['hiddenSendToTenant'] == 1) {
                            if (! is_file(BASEPATH . "/framework/views/emails/{$clientDB}/email_layout3.html")) {
                                $email = new EmailTemplate('views/emails/tenantTaxInvoiceEmail.html', SYSTEMURL);
                            } else {
                                $email = new EmailTemplate("views/emails/{$clientDB}/email_layout3.html", SYSTEMURL);
                            }

                            $email->items['clientName'] = dbGetClientName();
                            $email->items['leaseCode'] = $lease['leaseID'];
                            $email->items['leaseName'] = $lease['leaseID'];
                            $email->items['notes'] = explode("\n", $view->items['notation']);
                            $email->items['PropertyCode'] = $view->items['propertyID'];
                            $email->items['TenantCode'] = $lease['leaseID'];
                            $email->items['SalutationName'] = dbGetLeaseContactSalutation(
                                $view->items['propertyID'],
                                $lease['leaseID'],
                                true
                            );
                            $email->items['TenantName'] = $lease['leaseName'];
                            $email->items['TenantLocation'] = $lease['pmle_description'];

                            $subject = dbGetEmailTemplate(3);
                            $subject = str_replace('%PropertyCode%', $view->items['propertyID'], $subject);
                            $subject = str_replace('%TenantCode%', $lease['leaseID'], $subject);
                            $subject = str_replace('%TenantName%', $lease['leaseName'], $subject);
                            $subject = str_replace('%TenantLocation%', $lease['pmle_description'], $subject);
                            $subject = str_replace('%SalutationName%', $email->items['SalutationName'], $subject);

                            $filePath = $details['filePath'];
                            $attachment = [];
                            $attachmentCount = 0;
                            $attachment[$attachmentCount]['file'] = $filePath;
                            $attachment[$attachmentCount]['content_type'] = 'application/pdf';

                            $documents = dbGetInvoiceDocuments(
                                $view->items['propertyID'],
                                $lease['leaseID'],
                                $view->items['dueDate'],
                                $details['invoiceNumber'],
                                false,
                                null,
                                false
                            );
                            if ($documents && is_array($documents)) {
                                foreach ($documents as $v) {
                                    $docuFile = $v['filename'];
                                    $attachmentCount++;
                                    $attachment[$attachmentCount]['file'] = BASEPATH . '/reports/' . $docuFile;
                                    $extension = pathinfo($attachment[$attachmentCount]['file'], PATHINFO_EXTENSION);
                                    $attachment[$attachmentCount]['content_type'] = 'application/pdf';
                                }
                            }

                            //
                            // pre_print_r($attachment);
                            if ($email_cen_send) {
                                $invoiceNumber = $details['invoiceNumber'];
                                $emailType = ($invoiceNumber == '') ? '' : "(#  {$invoiceNumber}) ";
                                $debtorID = dbGetDebtorID($view->items['propertyID'], $lease['leaseID']);
                                $identifier = ($invoiceNumber) ? emailIdentifier(
                                    EMAILREF_INVOICE,
                                    $invoiceNumber,
                                    $debtorID
                                ) : null;
                                // -- grab the details for the property and get the
                                $office = dbGetOfficeForProperty($view->items['propertyID']);

                                // ***** Retrieve portfolio manager details for CC purposes - START
                                $emailAddressCC = '';
                                $getPortfolioCode = dbGetPropertyPortfolioById($view->items['propertyID']);
                                $emailAddressCC = dbGetParam(
                                    'PORTMEMAIL',
                                    $getPortfolioCode['pmpr_portfolio']
                                ); // actual
                                // $emailAddressCC = '<EMAIL>'; //for testing purposes only
                                // ***** Retrieve portfolio manager details for CC purposes - END
                                $emailAddress = dbGetDebtorEmailCentralised(
                                    $view->items['propertyID'],
                                    $lease['leaseID'],
                                    'TENTAXIN'
                                );
                                $defaultEmailSend = '1';
                                foreach ($emailAddress as $aRowCentralisedEmailCheck) {
                                    $defaultEmail = $aRowCentralisedEmailCheck['defaultEmail'];
                                    if ($defaultEmail == '0') {
                                        $defaultEmailSend = $defaultEmail;
                                    }
                                }

                                $email_sent_flag = false;
                                $successfulEmailsMsg = '';
                                foreach ($emailAddress as $aRowCentralisedEmail) {
                                    $emailAddressCentral = $aRowCentralisedEmail['emailAddress'];
                                    $defaultEmail = $aRowCentralisedEmail['defaultEmail'];
                                    if ($defaultEmailSend == $defaultEmail && isValid($emailAddressCentral, TEXT_EMAIL, false)) {
                                        $email_sent_flag = true;
                                        if ($view->items['hiddenAddCcToPm'] == 1) {
                                            // Yes to CC to PM
                                            if (isValid($emailAddressCC, TEXT_EMAIL, false)) {
                                                // Proceed on CC to PM
                                                // sendMail ($emailAddress, '', $email->toString (),'Tenant Tax Invoice ' .  $emailType . ' from ' . $office['officeName']  . ' for ' . $email->items['leaseName'], $attachment, null, $identifier); //O
                                                $trailLetter =
                                                [
                                                    'letterTemplateID' => dbGetLetterCategoryID('Tax Invoice'),
                                                    'letterFormat' => 'pdf',
                                                    'letterRecipient' => $emailAddressCentral . ($emailAddressCC ? ';' . $emailAddressCC : ''),
                                                    'letterEmailSubject' => $subject ? $subject : 'Tenant Tax Invoice ' . $emailType . ' from ' . $office['officeName'] . ' for ' . $email->items['leaseName'],
                                                    'letterEmailBody' => $email->toString(),
                                                    'letterTemplateBody' => '',
                                                    'propertyCode' => $view->items['propertyID'],
                                                    'leaseCode' => $view->items['leaseID'],
                                                    'userID' => $_SESSION['user_id'],
                                                    'dl_path' => $details['downloadPath'],

                                                ];

                                                $parameters['letter_history_id'] = dbInsertTrailLetter(
                                                    $trailLetter
                                                );

                                                sendMail(
                                                    $emailAddressCentral,
                                                    '',
                                                    $email->toString(),
                                                    $subject ? $subject : 'Tenant Tax Invoice ' . $emailType . ' from ' . $office['officeName'] . ' for ' . $email->items['leaseName'],
                                                    $attachment,
                                                    null,
                                                    $identifier,
                                                    false,
                                                    $emailAddressCC,
                                                    true,
                                                    true,
                                                    $parameters
                                                ); // U
                                                $mul_leases_has_email[] = '<a href="download.php?fileID=' . $mul_download_path . '"><img src="' . ASSET_DOMAIN . 'assets/images/icons/pdf.png" alt="Adobe Logo" class="icon" /><span style="font-weight: bold;"> ' . $lease['leaseID'] . '(' . $emailAddressCentral . ')' . '</span></a>';
                                                $mul_leases_has_email[] = '<a href="download.php?fileID=' . $mul_download_path . '"><img src="' . ASSET_DOMAIN . 'assets/images/icons/pdf.png" alt="Adobe Logo" class="icon" /><span style="font-weight: bold;"> ' . $lease['leaseID'] . '(CC:' . $emailAddressCC . ')' . '</span></a>';
                                            } else {
                                                $trailLetter =
                                                [
                                                    'letterTemplateID' => dbGetLetterCategoryID('Tax Invoice'),
                                                    'letterFormat' => 'pdf',
                                                    'letterRecipient' => $emailAddressCentral,
                                                    'letterEmailSubject' => $subject ? $subject : 'Tenant Tax Invoice ' . $emailType . ' from ' . $office['officeName'] . ' for ' . $email->items['leaseName'],
                                                    'letterEmailBody' => $email->toString(),
                                                    'letterTemplateBody' => '',
                                                    'propertyCode' => $view->items['propertyID'],
                                                    'leaseCode' => $view->items['leaseID'],
                                                    'userID' => $_SESSION['user_id'],
                                                    'dl_path' => $details['downloadPath'],

                                                ];

                                                $parameters['letter_history_id'] = dbInsertTrailLetter(
                                                    $trailLetter
                                                );

                                                // Send without CC since $currentUserEmailAddress is invalid
                                                sendMail(
                                                    $emailAddressCentral,
                                                    '',
                                                    $email->toString(),
                                                    $subject ? $subject : 'Tenant Tax Invoice ' . $emailType . ' from ' . $office['officeName'] . ' for ' . $email->items['leaseName'],
                                                    $attachment,
                                                    null,
                                                    $identifier,
                                                    false,
                                                    false,
                                                    true,
                                                    true,
                                                    $parameters
                                                ); // U
                                                $mul_leases_has_email[] = '<a href="download.php?fileID=' . $mul_download_path . '"><img src="' . ASSET_DOMAIN . 'assets/images/icons/pdf.png" alt="Adobe Logo" class="icon" /><span style="font-weight: bold;"> ' . $lease['leaseID'] . '(' . $emailAddressCentral . ')' . '</span></a>';
                                            }
                                        } else {
                                            $trailLetter =
                                            [
                                                'letterTemplateID' => dbGetLetterCategoryID('Tax Invoice'),
                                                'letterFormat' => 'pdf',
                                                'letterRecipient' => $emailAddressCentral,
                                                'letterEmailSubject' => $subject ? $subject : 'Tenant Tax Invoice ' . $emailType . ' from ' . $office['officeName'] . ' for ' . $email->items['leaseName'],
                                                'letterEmailBody' => $email->toString(),
                                                'letterTemplateBody' => '',
                                                'propertyCode' => $view->items['propertyID'],
                                                'leaseCode' => $view->items['leaseID'],
                                                'userID' => $_SESSION['user_id'],
                                                'dl_path' => $details['downloadPath'],

                                            ];

                                            $parameters['letter_history_id'] = dbInsertTrailLetter($trailLetter);

                                            // No to CC to PM
                                            sendMail(
                                                $emailAddressCentral,
                                                '',
                                                $email->toString(),
                                                $subject ? $subject : 'Tenant Tax Invoice ' . $emailType . ' from ' . $office['officeName'] . ' for ' . $email->items['leaseName'],
                                                $attachment,
                                                null,
                                                $identifier,
                                                false,
                                                false,
                                                true,
                                                true,
                                                $parameters
                                            ); // U
                                            $mul_leases_has_email[] = '<a href="download.php?fileID=' . $mul_download_path . '"><img src="' . ASSET_DOMAIN . 'assets/images/icons/pdf.png" alt="Adobe Logo" class="icon" /><span style="font-weight: bold;"> ' . $lease['leaseID'] . '(' . $emailAddressCentral . ')' . '</span></a>';
                                        }
                                    }
                                }

                                if (! $email_sent_flag) {
                                    $mul_leases_no_email[] = '<a href="download.php?fileID=' . $mul_download_path . '"><img src="' . ASSET_DOMAIN . 'assets/images/icons/pdf.png" alt="Adobe Logo" class="icon" /><span style="font-weight: bold;"> ' . $lease['leaseID'] . '</span></a>';
                                    $has_invalid_emails = true;
                                    $fail_ctr++;
                                } else {
                                    $success_ctr++;
                                }
                            } else {
                                $emailAddress = dbGetDebtorEmail($view->items['propertyID'], $lease['leaseID']);
                                // -- if the debtor email address is valid
                                if (isValid($emailAddress, TEXT_EMAIL, false)) {
                                    $invoiceNumber = $details['invoiceNumber'];
                                    $emailType = ($invoiceNumber == '') ? '' : "(#  {$invoiceNumber}) ";
                                    $debtorID = dbGetDebtorID($view->items['propertyID'], $lease['leaseID']);
                                    $identifier = ($invoiceNumber) ? emailIdentifier(
                                        EMAILREF_INVOICE,
                                        $invoiceNumber,
                                        $debtorID
                                    ) : null;
                                    // -- grab the details for the property and get the
                                    $office = dbGetOfficeForProperty($view->items['propertyID']);

                                    // ***** Retrieve portfolio manager details for CC purposes - START
                                    $emailAddressCC = '';
                                    $getPortfolioCode = dbGetPropertyPortfolioById($view->items['propertyID']);
                                    $emailAddressCC = dbGetParam(
                                        'PORTMEMAIL',
                                        $getPortfolioCode['pmpr_portfolio']
                                    ); // actual
                                    // $emailAddressCC = '<EMAIL>'; //for testing purposes only
                                    // ***** Retrieve portfolio manager details for CC purposes - END

                                    if ($view->items['hiddenAddCcToPm'] == 1) {
                                        // Yes to CC to PM
                                        if (isValid($emailAddressCC, TEXT_EMAIL, false)) {
                                            // Proceed on CC to PM
                                            // sendMail ($emailAddress, '', $email->toString (),'Tenant Tax Invoice ' .  $emailType . ' from ' . $office['officeName']  . ' for ' . $email->items['leaseName'], $attachment, null, $identifier); //O
                                            $trailLetter =
                                            [
                                                'letterTemplateID' => dbGetLetterCategoryID('Tax Invoice'),
                                                'letterFormat' => 'pdf',
                                                'letterRecipient' => $emailAddress . ($emailAddressCC ? ';' . $emailAddressCC : ''),
                                                'letterEmailSubject' => $subject ? $subject : 'Tenant Tax Invoice ' . $emailType . ' from ' . $office['officeName'] . ' for ' . $email->items['leaseName'],
                                                'letterEmailBody' => $email->toString(),
                                                'letterTemplateBody' => '',
                                                'propertyCode' => $view->items['propertyID'],
                                                'leaseCode' => $view->items['leaseID'],
                                                'userID' => $_SESSION['user_id'],
                                                'dl_path' => $details['downloadPath'],

                                            ];

                                            $parameters['letter_history_id'] = dbInsertTrailLetter($trailLetter);

                                            sendMail(
                                                $emailAddress,
                                                '',
                                                $email->toString(),
                                                $subject ? $subject : 'Tenant Tax Invoice ' . $emailType . ' from ' . $office['officeName'] . ' for ' . $email->items['leaseName'],
                                                $attachment,
                                                null,
                                                $identifier,
                                                false,
                                                $emailAddressCC,
                                                true,
                                                true,
                                                $parameters
                                            ); // U
                                        } else {
                                            $trailLetter =
                                            [
                                                'letterTemplateID' => dbGetLetterCategoryID('Tax Invoice'),
                                                'letterFormat' => 'pdf',
                                                'letterRecipient' => $emailAddress,
                                                'letterEmailSubject' => $subject ? $subject : 'Tenant Tax Invoice ' . $emailType . ' from ' . $office['officeName'] . ' for ' . $email->items['leaseName'],
                                                'letterEmailBody' => $email->toString(),
                                                'letterTemplateBody' => '',
                                                'propertyCode' => $view->items['propertyID'],
                                                'leaseCode' => $view->items['leaseID'],
                                                'userID' => $_SESSION['user_id'],
                                                'dl_path' => $details['downloadPath'],

                                            ];

                                            $parameters['letter_history_id'] = dbInsertTrailLetter($trailLetter);

                                            // Send without CC since $currentUserEmailAddress is invalid
                                            sendMail(
                                                $emailAddress,
                                                '',
                                                $email->toString(),
                                                $subject ? $subject : 'Tenant Tax Invoice ' . $emailType . ' from ' . $office['officeName'] . ' for ' . $email->items['leaseName'],
                                                $attachment,
                                                null,
                                                $identifier,
                                                false,
                                                false,
                                                true,
                                                true,
                                                $parameters
                                            ); // U
                                        }
                                    } else {
                                        // No to CC to PM
                                        $trailLetter =
                                        [
                                            'letterTemplateID' => dbGetLetterCategoryID('Tax Invoice'),
                                            'letterFormat' => 'pdf',
                                            'letterRecipient' => $emailAddress,
                                            'letterEmailSubject' => $subject ? $subject : 'Tenant Tax Invoice ' . $emailType . ' from ' . $office['officeName'] . ' for ' . $email->items['leaseName'],
                                            'letterEmailBody' => $email->toString(),
                                            'letterTemplateBody' => '',
                                            'propertyCode' => $view->items['propertyID'],
                                            'leaseCode' => $view->items['leaseID'],
                                            'userID' => $_SESSION['user_id'],
                                            'dl_path' => $details['downloadPath'],

                                        ];

                                        $parameters['letter_history_id'] = dbInsertTrailLetter($trailLetter);

                                        sendMail(
                                            $emailAddress,
                                            '',
                                            $email->toString(),
                                            $subject ? $subject : 'Tenant Tax Invoice ' . $emailType . ' from ' . $office['officeName'] . ' for ' . $email->items['leaseName'],
                                            $attachment,
                                            null,
                                            $identifier,
                                            false,
                                            false,
                                            true,
                                            true,
                                            $parameters
                                        ); // U
                                    }

                                    // $mul_leases_has_email[] = $lease['leaseID'];
                                    $mul_leases_has_email[] = '<a href="download.php?fileID=' . $mul_download_path . '"><img src="' . ASSET_DOMAIN . 'assets/images/icons/pdf.png" alt="Adobe Logo" class="icon" /><span style="font-weight: bold;"> ' . $lease['leaseID'] . '</span></a>';
                                    $success_ctr++;
                                    // echo 'WENT INSIDE SEND EMAIL FUNCTION IN MULTIPLE LEASES';
                                } else {
                                    // //$mul_leases_no_email[] = $lease['leaseID']; //original
                                    $mul_leases_no_email[] = '<a href="download.php?fileID=' . $mul_download_path . '"><img src="' . ASSET_DOMAIN . 'assets/images/icons/pdf.png" alt="Adobe Logo" class="icon" /><span style="font-weight: bold;"> ' . $lease['leaseID'] . '</span></a>';
                                    $has_invalid_emails = true;
                                    $fail_ctr++;
                                }
                            }
                        }
                    }

                    // ## Send Email AR - END

                }

                $masterInvoice->_close();

                if ($success_ctr > 0) {
                    $view->items['successfulEmailsMsg'] = "Tenant invoices were successfully sent to the following: <span style='font-weight: bold;'>" . implode(
                        ',',
                        $mul_leases_has_email
                    ) . '</span>';
                }

                if ($fail_ctr > 0) {
                    $view->items['sendingEmailMsg'] = "Email address is invalid or not existing for some tenants. Please check the email settings of the following: <span style='font-weight: bold;'>" . implode(
                        ',',
                        $mul_leases_no_email
                    ) . '</span>';
                }

                if ($detailsCount) {
                    $filename = "tax_invoice_{$year}{$month}{$day}_" . $view->items['propertyID'] . '.pdf';
                    $filePath = "{$pathPrefix}{$clientDirectory}/pdf/TaxInvoice/{$filename}";
                    $downloadPath = "{$clientDirectory}/pdf/TaxInvoice/{$filename}";

                    mergePDF($InvoiceArray, false, $filename, $filePath, '');
                    $view->items['downloadLink'] = $downloadPath;
                }
            }

            $view->items['step'] = 1;
            unset($view->items['batchNumber']);
            $view->items['tempDocumentBatchNo'] = uniqid(); // provide new document batch number for the next entry
            unset($view->items['invoiceLinesJson']); // added to unset previous invoice lists. added by arjay
            break;

        case 'delete1':
            if ($view->items['invoiceLinesJson'] != null) {
                $invoiceLines = [];
                $invJson = json_decode($view->items['invoiceLinesJson'], true);
                $result = $invJson;
                foreach ($result as $k => $v) {
                    if (intval($view->items['lineNumber']) != $k) {
                        $invoiceLines[] = $v;
                    }
                }

                $view->items['invoiceLinesJson'] = json_encode(array_reverse($invoiceLines), true);
            }

            break;

        case 'delete':
            $existing = dbGetTransactionByBatch($view->items['batchNumber'], $view->items['lineNumber']);


            // ****for AP Recoverable update linked ap's, set as unrecovered***
            $data = json_decode($existing['ap_recovered']);
            $length = count($data ?? []);
            if ($length > 0) {
                // ***reset AP's recovered status
                foreach ($data as $v) {
                    $apData = dbGetTransactionByBatchAndLine($v->batch, $v->line);
                    $dt = json_decode($apData['arRecovered']);
                    $dtlen = count($dt ?? []);
                    if ($dtlen > 1) {
                        // ***
                        $apInfoAr = [];
                        foreach ($dt as $vl) {
                            $r = ['batch' => $vl->batch, 'line' => $vl->line];
                            $arBatchAndLine = $view->items['batchNumber'] . $view->items['lineNumber'];
                            $apArBatchAndLine = $vl->batch . $vl->line;
                            if ($arBatchAndLine !== $apArBatchAndLine) {
                                $apInfoAr[] = $r;
                            }
                        }

                        dbUpdateApTransactionForRecovered($v->batch, $v->line, json_encode($apInfoAr));
                    } else {
                        dbUpdateTransactionForRecovered('ap_transaction', 0, $v->batch, $v->line, null, null);
                    }
                }
            }

            // ******************************end****************************

            if (GL_ACTIVE) {
                $gl = new GeneralLedger();
                $gl->transactionType = $existing['transactionType'];
                $gl->transactionDate = $existing['transactionDate'];
                $gl->description = $existing['description'];
                $gl->year = $existing['transactionYear'];
                $gl->period = $existing['transactionPeriod'];
                $gl->propertyID = $existing['propertyID'];
                $gl->leaseID = $existing['leaseID'];
                $gl->companyID = $existing['debtorID'];
                $gl->fromDate = $existing['fromDate'];
                $gl->toDate = $existing['toDate'];
                $gl->source = GL_SOURCE_AR;
                $gl->batchID = $view->items['batchNumber'];
                $gl->lineNumber = $view->items['lineNumber'];

                $tr = new Transaction($gl);
                $_amount = bcmul(1, $existing['netAmount'], 2);
                $gl->update($existing['accountID'], GL_BALANCE_ACCRUALS, $_amount);
                $tr->delete($existing['accountID'], BASIS_ACCRUALS, $_amount);

                $_amount = bcmul(-1, $existing['transactionAmount'], 2);
                $gl->update(glAccount(GL_DEBTORS_CONTROL), GL_BALANCE_ACCRUALS, $_amount);
                $tr->delete(glAccount(GL_DEBTORS_CONTROL), BASIS_ACCRUALS, $_amount, true);

                $_amount = bcmul(1, $existing['taxAmount'], 2);
                $gl->update(glAccount(GL_GST_OUTPUT), GL_BALANCE_ACCRUALS, $_amount);
                $tr->delete(glAccount(GL_GST_OUTPUT), BASIS_ACCRUALS, $_amount, true);
            }

            dbDeleteTransaction($view->items['batchNumber'], $view->items['lineNumber']);
            dbDeleteLinkedInvoiceDocument($view->items['debtorID'], $view->items['apBatchNumber']);
            // -- and delete any allocations as well
            if (GL_ACTIVE) {
                $allocatedList = dbGetAllocatedByBatch($view->items['batchNumber'], $view->items['lineNumber']);
                foreach ($allocatedList as $t) {
                    $t['fromDate'] = $existing['fromDate'];
                    $t['toDate'] = $existing['toDate'];
                    $t['description'] = $existing['description'];
                    $t['year'] = $t['transactionYear'];
                    $t['period'] = $t['transactionPeriod'];
                    $t['netAmount'] = bcsub($t['transactionAmount'], $t['taxAmount'], 2);
                    $t['batchNumber'] = $t['allocationNumber'];
                    $t['lineNumber'] = 1;
                    reverseGLReceipt($t);
                }
            }

            dbDeleteAllocations($view->items['batchNumber'], $view->items['lineNumber']);
            $view->items['statusMessage'] = 'Your transaction has been deleted.';
            break;
        case 'saveRecurring':
            $template = [];
            $template['propertyID'] = $view->items['propertyID'];
            $template['leaseID'] = $view->items['leaseID'];
            $template['debtorID'] = $view->items['debtorID'];
            $template['accountID'] = $view->items['accountID'];
            $template['description'] = $view->items['description'];
            $template['taxRateID'] = $view->items['taxRateID'];
            $template['netAmount'] = $view->items['netAmount'];
            $template['grossAmount'] = $view->items['grossAmount'];
            if (noErrors($validationErrors)) {
                dbInsertRecurringInvoice($template);
            }

            $view->items['statusMessage'] = 'These details have been saved as a recurring invoice template';
            break;
        case 'onCharge':
            if ($view->items['propertyID']) {
                $propVONotes = dbGetVOPropertyNotesDesc($view->items['propertyID']);

                $view->items['voPropertyNotes'] = $propVONotes;
                $view->items['voPropertyNotesCount'] = count($propVONotes ?? []);
            }

            break;
    }

    //	if ($view->items['batchNumber'])
    //	{
    //		$totalGrossAmount = 0;
    //		$totalTaxAmount = 0;
    //		$totalNetAmount = 0;
    //		$view->items['invoiceLines'] = dbGetTransactionDetailsByBatch ($view->items['batchNumber']);
    //		foreach ($view->items['invoiceLines'] as &$i)
    //		{
    //			$i['netAmount'] = bcsub ($i['transactionAmount'], $i['taxAmount'], 2);
    //			$totalGrossAmount = bcadd ($totalGrossAmount, $i['transactionAmount'], 2);
    //			$totalTaxAmount = bcadd ($totalTaxAmount, $i['taxAmount'], 2);
    //		}
    //		$view->items['totalGrossAmount'] = $totalGrossAmount;
    //		$view->items['totalNetAmount'] = bcsub ($totalGrossAmount, $tot  alTaxAmount, 2);
    //		$view->items['totalTaxAmount'] = $totalTaxAmount;
    //	}
    $view->items['transactionTypeList'] = $transactionTypes;
    $view->items['computationTypeList'] = $computationTypes;

    $view->items['createInterimInvoiceOption'] = $createInterimInvoiceOption;
    $view->items['sendToTenantOption'] = $sendToTenantOption;
    $view->items['addCcToPmOption'] = $addCcToPmOption;

    // $view->items['tempDocumentBatchNo'] = uniqid();

    $view->items['propertyList'] = dbPropertyList();
    $view->items['chargeEntryTypeList'] = [
        SINGLE_CHARGE => 'Single Lease',
        MULTIPLE_CHARGES => 'Multiple Leases',
    ]; // Single Item -> Single Lease and Multiple Items -> Multiple Leases
    if (! isset($view->items['chargeEntryType'])) {
        $view->items['chargeEntryType'] = SINGLE_CHARGE;
    }

    $view->items['percentageTypeList'] =
    [
        MULTIPLECHARGE_UNIT => '% of unit area',
        MULTIPLECHARGE_PERCENTAGE => 'Equal % split',
        MULTIPLECHARGE_MANUAL => 'Manual allocation',
        MULTIPLECHARGE_PROPERTY_SPLIT => 'Recoverable split',
    ];
    if (! isset($view->items['percentageType'])) {
        $view->items['percentageType'] = MULTIPLECHARGE_UNIT;
    }

    /* calculations on multiple charges */
    // -- if doing a multiple charge and calculating the charges - extract the entries and apply the appropriate calculation method
    if (($view->items['chargeEntryType'] == MULTIPLE_CHARGES) && ($view->items['action'] != 'calculateCharge')) {
        $change_in_unit_area = [];

        $leaseList = extractFields(
            $view->items,
            [
                'amount',
                'unitArea',
                'applyAmount',
                'leaseID',
                'leaseName',
                'leaseType',
                'percentage',
                'unitDescription',
                'unitFromDate',
                'unitToDate',
                'daysCharged',
                'unitID',
                'grossAmount',
            ]
        );

        if ((! $leaseList) || (in_array(
            $view->items['action'],
            ['changePrimary', 'refreshDates']
        )) || $view->items['action2'] == 'refreshDates') {
            $leaseList = dbPropertyUnitList(
                $view->items['propertyID'],
                $view->items['fromDate'],
                $view->items['toDate']
            );
            foreach ($leaseList as $id => $lease) {
                $leaseList[$id]['applyAmount'] = true;
                $leaseList[$id]['tenantType'] = $lease['tenantType'];
            }

            $view->items['applyAmountAll'] = true;
        }

        switch ($view->items['percentageType']) {
            case MULTIPLECHARGE_UNIT:
                // $change_in_unit_area = array();
                $_unitID = null;
                foreach ($leaseList as $lease) {
                    if ($lease['applyAmount']) {
                        if ($lease['unitID'] != $_unitID) {
                            $total += $lease['unitArea'];
                        } elseif ($lease['unitID'] == $_unitID && $lease['unitArea'] != $last_unit_area) {
                            $change_in_unit_area[] = $_unitID . ' : from  ' . ($last_unit_area + 0) . $_SESSION['country_default']['area_unit'] . ' to ' . ($lease['unitArea'] + 0) . $_SESSION['country_default']['area_unit'];
                        }

                        $last_unit_area = $lease['unitArea'];
                        $_unitID = $lease['unitID'];
                    }
                }

                $view->items['totalArea'] = $total;
                $_unitID = null;
                if ($total != 0) {
                    foreach ($leaseList as $id => $lease) {
                        if ($lease['applyAmount']) {
                            $leaseList[$id]['percentage'] = 0;
                            // $leaseList[$id]['percentage'] = round(($lease['unitArea']/$total) * 100,4); // this is the old one
                            $old_percentage = $lease['unitArea'] / $total;

                            // get new amount the get the percentage
                            $leaseList[$id]['daysCharged'] = get_unit_from_to_diff($lease, $view->items);
                            if ($view->items['netAmount'] != 0) {
                                // echo $lease['unitArea'].'<br>';
                                $new_amount = $view->items['netAmount'] * $old_percentage *
                                    $leaseList[$id]['daysCharged'] / get_transaction_period_days($view->items);
                                // echo $new_amount.'<br>';
                                $leaseList[$id]['percentage'] = round(
                                    ($new_amount / $view->items['netAmount']) * 100,
                                    4
                                );
                            }
                        } else {
                            $leaseList[$id]['percentage'] = '0';
                        }

                        $_unitID = $lease['unitID'];
                    }
                }

                break;
            case MULTIPLECHARGE_PERCENTAGE:
                // $change_in_unit_area = array();
                $_unitID = null;
                foreach ($leaseList as $lease) {
                    if (($lease['applyAmount']) && ($lease['unitID'] != $_unitID)) {
                        $total += 1;
                    }

                    $_unitID = $lease['unitID'];
                }

                $_unitID = null;
                if ($total != 0) {
                    foreach ($leaseList as $id => $lease) {
                        // $leaseList[$id]['percentage'] = ($lease['applyAmount'] && ($lease['unitID'] != $_unitID)) ? round((1/$total) * 100,4) : '0';


                        if ($lease['applyAmount']) {
                            $leaseList[$id]['percentage'] = 0;
                            // $leaseList[$id]['percentage'] = round((1/$total) * 100,4); // this is the old one
                            $old_percentage = 1 / $total;
                            $leaseList[$id]['daysCharged'] = get_unit_from_to_diff($lease, $view->items);

                            // get new amount the get the percentage
                            if ($view->items['netAmount'] != 0) {
                                // echo $lease['unitArea'].'<br>';
                                $new_amount = $view->items['netAmount'] * $old_percentage *
                                    $leaseList[$id]['daysCharged'] / get_transaction_period_days($view->items);
                                // echo $new_amount.'<br>';
                                $leaseList[$id]['percentage'] = round(
                                    ($new_amount / $view->items['netAmount']) * 100,
                                    4
                                );
                            }
                        } else {
                            $leaseList[$id]['percentage'] = '0';
                        }
                    }
                }

                break;
            case MULTIPLECHARGE_PROPERTY_SPLIT:
                $_unitID = null;
                $propertySplitList = dbGetPropertyRecoverableList(
                    $view->items['propertyID'],
                    $view->items['fromDate'],
                    $view->items['toDate'],
                    $view->items['accountID']
                );
                foreach ($leaseList as $lease) {
                    if ($lease['applyAmount']) {
                        if ($lease['unitID'] != $_unitID) {
                            $total += $propertySplitList[$lease['leaseID']];
                        } elseif ($lease['unitID'] == $_unitID && $propertySplitList[$lease['leaseID']] != $last_unit_area) {
                            $change_in_unit_area[] = $_unitID . ' : from  ' . ($last_unit_area + 0) . 'm<sup>2</sup> to ' . ($lease['unitArea'] + 0) . 'm<sup>2</sup>';
                        }

                        $last_unit_area = $propertySplitList[$lease['leaseID']];
                        $_unitID = $lease['unitID'];
                    }
                }

                $view->items['totalArea'] = $total;
                $_unitID = null;
                if ($total != 0) {
                    foreach ($leaseList as $id => $lease) {
                        if ($lease['applyAmount']) {
                            $leaseList[$id]['percentage'] = 0;
                            // $leaseList[$id]['percentage'] = round(($lease['unitArea']/$total) * 100,4); // this is the old one
                            $old_percentage = $propertySplitList[$lease['leaseID']] / 100;

                            // get new amount the get the percentage
                            $leaseList[$id]['daysCharged'] = get_unit_from_to_diff($lease, $view->items);
                            if ($view->items['netAmount'] != 0) {
                                // echo $lease['unitArea'].'<br>';
                                $new_amount = $view->items['netAmount'] * $old_percentage *
                                    $leaseList[$id]['daysCharged'] / get_transaction_period_days($view->items);
                                // echo $new_amount.'<br>';
                                $leaseList[$id]['percentage'] = round(
                                    ($new_amount / $view->items['netAmount']) * 100,
                                    4
                                );
                            }
                        } else {
                            $leaseList[$id]['percentage'] = '0';
                        }

                        $_unitID = $lease['unitID'];
                    }
                } else {
                    foreach ($leaseList as $id => $lease) {
                        $leaseList[$id]['percentage'] = 0;
                    }
                }

                break;
        }

        foreach ($leaseList as $id => $lease) {
            $leaseVONotes = dbGetVOLeaseNotesDesc($view->items['propertyID'], $lease['leaseID']);

            $leaseList[$id]['notes'] = $leaseVONotes;
            $leaseList[$id]['notesCount'] = count($leaseVONotes ?? []);
        }

        $view->items['leaseList'] = $leaseList;
        if ($view->items['action'] == 'calculateAmount' || $view->items['action'] == 'updateType') {
            if ($view->items['percentageType'] != MULTIPLECHARGE_MANUAL) {
                foreach ($leaseList as $id => $lease) {
                    $amount = round($view->items['netAmount'] * ($lease['percentage'] / 100), 2);
                    $leaseList[$id]['amount'] = $amount;
                }
            }

            $view->items['leaseList'] = $leaseList;
        }

        $totalAmount = 0;
        /**
         * foreach ($leaseList as $id => $lease) $totalAmount += $lease['amount'];
         * $view->items['totalAmount'] = $totalAmount;
         **/
        foreach ($leaseList as $lease) {
            if ($lease['applyAmount']) {
                $totalAmount += $lease['amount'];
            }
        }

        $view->items['totalAmount'] = $totalAmount;
        if ($view->items['percentageType'] == MULTIPLECHARGE_MANUAL) {
            $view->items['netAmount'] = round($totalAmount, 2);
            $view->items['taxAmount'] = round(
                ($taxRates[$view->items['taxRateID']] / 100) * $view->items['netAmount'],
                2
            );
            $view->items['grossAmount'] = bcadd($view->items['taxAmount'], $view->items['netAmount'], 2);
        }
    }

    if ($view->items['computationType'] != 'NET') {
        $totalGrossAmount = 0;
        foreach ($leaseList as $id => $lease) {
            if ($lease['applyAmount']) {
                $_taxAmount = round(($taxRates[$view->items['taxRateID']] / 100) * $lease['amount'], 2);
                $leaseList[$id]['grossAmount'] = round($_taxAmount + $lease['amount'], 2);
                $totalGrossAmount += $leaseList[$id]['grossAmount'];
            }
        }

        $view->items['leaseList'] = $leaseList;
        $view->items['totalGrossAmount'] = $totalGrossAmount;
    }

    if ($view->items['chargeEntryType'] == SINGLE_CHARGE) {
        $leaseVONotes = dbGetVOLeaseNotesDesc($view->items['propertyID'], $view->items['leaseID']);

        $view->items['voLeaseNotes'] = $leaseVONotes;
        $view->items['voLeaseNotesCount'] = count($leaseVONotes ?? []);
    }

    if ($view->items['propertyID'] && $view->items['leaseID']) {
        $leaseStatus = dbLeaseStatus($view->items['propertyID'], $view->items['leaseID']);
        if (count($leaseStatus) == 1) {
            $view->items['leaseStatus'] = $leaseStatus[0]['leaseStatus'];
        } elseif (count($leaseStatus) > 1) {
            foreach ($leaseStatus as $v) {
                if ($v['leaseStatus'] == 'L') {
                    $view->items['leaseStatus'] = 'L';
                    break;
                }
            }
        } else {
            $view->items['leaseStatus'] = null;
        }
    } elseif ($view->items['propertyID'] && $leaseList) {
        foreach ($leaseList as $k => $v) {
            if ($v['applyAmount'] == 1) {
                if (! isset($v['leaseStatus'])) {
                    $leaseStatus = dbLeaseStatus($view->items['propertyID'], $v['leaseID']);
                    $leaseList[$k]['leaseStatus'] = $leaseStatus[0]['leaseStatus'];
                }

                if ($leaseList[$k]['leaseStatus'] == 'L' || ! isset($v['leaseID'])) {
                    $view->items['leaseStatus'] = 'Y';
                    break;
                }
            }
        }
    }

    /* [LYN] FROM OPENING BALANCES */
    if ($view->items['action'] == 'openingBalancesArReceiptsComplete') {
        $receiptsDifference = bcsub(
            (float) $view->items['arDifferenceAmount'],
            (float) $view->items['totalGrossAmount'],
            2
        );
        if (($receiptsDifference > 0) || ($receiptsDifference < 0)) {
            $validationErrors[] = 'You cannot proceed because the accounts receivable is still out by <strong>' . toMoney(
                $receiptsDifference
            ) . '</strong>.';
        } else {
            completeTransaction($view->items);
            if ((bool) $view->items['displayApReceipts']) {
                clientSideRedirect('?command=invoice&module=ar&obID=' . $view->items['obID']);
            } else {
                clientSideRedirect('?command=openingBalances&module=generalLedger&obID=' . $view->items['obID']);
            }
        }
    }

    /* [LYN] FROM OPENING BALANCES */

    // new ******** - for the file - START
    // if ($view->items['hiddenTempDocumentBatchNo'])
    if ($view->items['tempDocumentBatchNo']) {
        $attachedInvoiceList = dbSelectLinkedInvoiceTempDocument($view->items['tempDocumentBatchNo']);
    }

    // //$attachedInvoiceList = dbSelectLinkedInvoiceTempDocument($view->items['hiddenTempDocumentBatchNo']);


    $filePath = "{$pathPrefix}{$clientDirectory}/pdf/SupplierInvoices/";
    $host = $_SERVER['HTTP_HOST'];
    $uri = rtrim(dirname($_SERVER['PHP_SELF']), '/\\');
    $dir = explode('/', $uri);
    $url = "http://{$host}/{$dir[0]}/reports/{$clientDirectory}/pdf/SupplierInvoices/";

    $appendedFiles = [];

    // scan uploads directory
    if (! file_exists($filePath)) {
        mkdir($filePath, FILE_PERMISSION, true);
    }

    //    $uploadsFiles = array_diff(scandir($filePath), array('.', '..'));
    //    if (is_array($attachedInvoiceList))
    //        $col = array_column($attachedInvoiceList, 'fileName');
    // add files to our array with
    // made to use the correct structure of a file

    // -------------------- invoice attachment part replaced by this
    if (is_array($attachedInvoiceList)) {
        foreach ($attachedInvoiceList as $file) {
            $appendedFiles[] = [
                'name' => $file['fileName'],
                'type' => FileUploader::mime_content_type($filePath . $file['fileName']),
                'size' => filesize($filePath . $file['fileName']),
                'file' => 'download.php?fileID=' . encodeParameter($file['filePath']), // $url . $file,
                'data' => [
                    'url' => $url . $file['fileName'],
                    'documentID' => $file['documentID'],
                    'invoiceID' => null,
                    'fileName' => $file['fileName'],
                ],
            ];
        }
    }

    // ----------------------------

    //    if (is_array($uploadsFiles) AND count($uploadsFiles) > 0)
    //    {
    //        foreach ($uploadsFiles as $file) {
    //            // skip if directory
    //
    //            if (is_dir($file))
    //                continue;
    //
    //            if (is_array($col) AND in_array($file, $col)) {
    //                $key = array_search($file, $attachedInvoiceList);
    //                $fn = explode('_', $attachedInvoiceList[$key]['fileName']);
    //                unset($fn[count($fn) - 1]);
    //                $fn2 = implode("_", array_values($fn));
    //                $attachAr = '';
    //
    //                //if ($attachedInvoiceList[$key]['attachAR'] == 1) $attachAr = 'Attach in related AR  --  ';
    //                $appendedFiles[] = array(
    //                    "name" => $attachAr . $file,
    //                    "type" => FileUploader::mime_content_type($filePath . $file),
    //                    "size" => filesize($filePath . $file),
    //                    "file" => 'download.php?fileID=' . encodeParameter($attachedInvoiceList[$key]['filePath']),//$url . $file,
    //                    "data" => array(
    //                        "url" => $url . $file,
    //                        "documentID" => $attachedInvoiceList[$key]['documentID'],
    //                        "invoiceID" => null,
    //                        "fileName" => $attachedInvoiceList[$key]['fileName']
    //                    )
    //                );
    //            }
    //        }
    //    }
    // convert our array into json string
    // $test =
    $appendedFiles = json_encode($appendedFiles);
    $view->items['appendFiles'] = $appendedFiles;
    // new ******** - for the file - END

    // //pre_print_r($view->items['hiddenTempDocumentBatchNo']);
    // pre_print_r($view->items['tempDocumentBatchNo']);
    if ($view->items['percentageType'] == MULTIPLECHARGE_PROPERTY_SPLIT && $view->items['accountID']) {
        $propertySplitList = dbGetPropertyRecoverableList(
            $view->items['propertyID'],
            $view->items['fromDate'],
            $view->items['toDate'],
            $view->items['accountID']
        );
        if (! $propertySplitList) {
            $validationErrors[] = 'The selected account code does not have a <strong>Property Recoverable Split</strong> setup.';
        }
    }

    if (! isset($view->items['logo'])) {
        $view->items['logo'] = 1;
    }

    $view->items['validationErrors'] = $validationErrors;
    $view->items['change_in_unit_area'] = $change_in_unit_area;
    $view->render();
}

<?php

include_once SYSTEMPATH . '/lib/enums/InvoiceChargeType.php';
include_once SYSTEMPATH . '/lib/enums/InvoiceStatementType.php';

use enums\InvoiceChargeType;
use enums\InvoiceStatementType;

function bookInvoiceNumber()
{
    $number = dbGetParam('DOCSERIES', 'GSTINVOICE');
    $number++;
    dbSetParam('DOCSERIES', 'GSTINVOICE', $number);

    return $number;
}

// -- function fetchInvoiceAmounts

//   given a debtor, property, lease and invoice date -
//  returns the number of outstanding amounts, and the records attached to those amounts in an array split by new (newAmounts) and outstanding (outstandingAmounts)

// -- a more comprehensive version of fetchOutstandingAmounts for retrieving new charges and associated

function fetchInvoiceAmounts(
    $debtorID,
    $propertyID,
    $leaseID,
    $dueDate,
    &$newAmounts,
    &$outstandingAmounts,
    $batchNumber = null
) {
    $outstandingAmounts = [];

    $newAmounts = dbGetInvoiceCharges(
        InvoiceChargeType::INV_CHARGES_NEW,
        $propertyID,
        $leaseID,
        $debtorID,
        $dueDate,
        $batchNumber
    );
    $records = count($newAmounts ?? []);

    if ($newAmounts) {
        foreach ($newAmounts as $id => $amount) {
            $receipts = dbGetNewReceipts($amount['batchNumber'], $amount['batchLineNumber'], $dueDate);
            if ($receipts) {
                $newAmounts[$id]['description'] .= ' *';
            }

            if ($receipts) {
                foreach ($receipts as $receipt) {
                    $receipt['unallocated'] = $receipt['amount'];
                    $receipt['totalAllocated'] = $receipt['amount']; // -- HACK : causes the offset to not be 0 to pass the checks in the PDF code. should clean up the PDF code instead :)
                    $outstandingAmounts[] = $receipt;
                }
            }
        }
    }

    $existing = dbGetInvoiceCharges(
        InvoiceChargeType::INV_CHARGES_OLD,
        $propertyID,
        $leaseID,
        $debtorID,
        $dueDate,
        $batchNumber
    );

    if ($existing) {
        foreach ($existing as $row) {
            $offset = bcadd($row['totalAllocated'], $row['totalReallocated'], 2);
            $row['unallocated'] = round($row['unallocated'] * 1, 2);
            $row['amount'] = round($row['amount'] * 1, 2);
            $unpaidAmount = bcsub($row['amount'], $offset, 2);
            if (($unpaidAmount != 0.00) && ($row['transactionType'] != TYPE_CASH)) {
                $row['unpaidAmount'] = $unpaidAmount;
                $records++;
                $outstandingAmounts[] = $row;
            } elseif (($row['transactionType'] == TYPE_CASH) && ($row['unallocated'] != 0)) {
                $unallocated = dbGetUnallocated(
                    $propertyID,
                    $leaseID,
                    $debtorID,
                    $row['batchNumber'],
                    $row['batchLineNumber']
                );
                // -- PATCH LOGIC : trash switch to not return unallocated amounts on invoices
                if ($unallocated) {
                    foreach ($unallocated as $unallocatedItem) {
                        if ($unallocatedItem['amount'] != 0) {
                            $unallocatedItem['unpaidAmount'] = $unallocatedItem['amount'];
                            $unallocatedItem['transactionAmount'] = $unallocatedItem['amount'];
                            $unallocatedItem['batchNumber'] = $row['batchNumber'];
                            $unallocatedItem['batchLineNumber'] = $row['batchLineNumber'];
                            $unallocatedItem['transactionDate'] = $row['transactionDate'];
                            $unallocatedItem['invoiceNumber'] = $row['invoiceNumber'];
                            $unallocatedItem['transactionType'] = TYPE_CASH;
                            $unallocateditem['unallocated'] = $row['unallocated'];
                            $outstandingAmounts[] = $unallocatedItem;
                            $records++;
                        }
                    }
                }
            }
        }
    }


    return $records;
}

function prepareTaxInvoice(
    $style,
    $leaseID,
    $propertyID,
    $dueDate,
    $toDate,
    &$parentInvoice,
    $batchNumber = null,
    $note = null,
    $logo = true,
    $issueDate = ''
) {
    global $clientDirectory, $pathPrefix;

    // -- initialise the variables that will store the transaction amounts
    $total = 0;
    $newAmounts = [];
    $outstandingAmounts = [];

    // -- this may need revising (suppliers?) - but it grabs the
    $debtorID = dbGetDebtor($propertyID, $leaseID);
    $debtorBanking = dbGetCompanyBank($debtorID);
    $isResidential = dbGetResidential($propertyID, $leaseID);

    $isStrata = dbGetStrata($propertyID);
    $property = dbGetProperty($propertyID);

    // -- if there are transactions to be printed : use the invoice variant of fetchOutstandingAmounts
    $invoiceAmounts = fetchInvoiceAmounts(
        $debtorID,
        $propertyID,
        $leaseID,
        $toDate,
        $newAmounts,
        $outstandingAmounts,
        $batchNumber
    );

    if ($invoiceAmounts != 0) {
        switch ($style) {
            case InvoiceStatementType::ST_INVOICE:
                $headerTitle = 'TAX INVOICE';
                $totalTitle = 'INVOICE TOTAL';
                break;
            case InvoiceStatementType::ST_STATEMENT:
                $headerTitle = 'STATEMENT OF ACCOUNT';
                $totalTitle = 'STATEMENT TOTAL';
                break;
        }

        [$day, $month, $year] = explode('/', $dueDate);

        // -- if there are new amounts to be invoiced - there wont be an invoice number booked yet! so book one out :)
        $invoiceNumber = null;
        if (count($newAmounts ?? []) > 0) {
            $invoiceNumber = bookInvoiceNumber();
        }

        // -- will need to store current DB index in session to allow for translation of logos
        $logoFile = dbGetClientLogo();
        $logoPath = ($logo) ? "assets/clientLogos/{$logoFile}" : null;

        $filename = "tax_invoice_{$year}{$month}{$day}_{$invoiceNumber}_{$propertyID}_{$leaseID}.pdf";
        $filePath = "{$pathPrefix}{$clientDirectory}/pdf/TaxInvoice/{$filename}";
        $downloadPath = "{$clientDirectory}/pdf/TaxInvoice/{$filename}";

        $invoice = new Invoice($filePath, $logoPath);
        $invoice->isResidential = $isResidential;
        $invoice->isStrata = $isStrata;
        logData(serialize($parentInvoice));
        if ($parentInvoice) {
            $masterInvoice = new Invoice($parentInvoice, $logoPath);
            $masterInvoice->isResidential = $isResidential;
            $masterInvoice->isStrata = $isStrata;
        }

        $agentData = dbGetAgentDetails($property['propertyRemittanceOffice'], $propertyID);

        $headerData = stripslashes_deep(dbGetHeaderDetails($propertyID, $leaseID));
        $leaseAddress = stripslashes_deep(dbGetLeaseMailingAddress($propertyID, $leaseID));
        $bankData = dbGetBankDetails($propertyID);

        if ($parentInvoice) {
            $masterInvoice->bpayBillerCode = $bankData['bpayBillerCode'];
            $masterInvoice->deft = $bankData['deft'];
        }

        $invoice->bpayBillerCode = $bankData['bpayBillerCode'];
        $invoice->deft = $bankData['deft'];

        // -- some data is shared across several components in the PDF, so instead of doing multiple db calls and binding locally within the object,
        // -- a single DB call is made and data bound to the object externally

        // -- PDFobjects are usually groups of data repeated statically on every page of the PDF, and as such are loaded once into the PDF and called at render...
        // -- mutating values are accessed from the Invoice object itself such as changing totals, invoice rows, sub titles within the statement

        // -- note that data is bound between array values to the object attributes sharing the same name as the array key - ie

        $header = new InvoiceHeader($propertyID, $leaseID, $dueDate, $headerTitle, $issueDate);
        $header->bindAttributesFrom($headerData);
        $header->bindAttributesFrom($bankData);
        $header->isStrata = $isStrata;
        $invoice->attachObject('header', $header);
        $invoice->propertyID = $propertyID;

        $crn = dbGetCRN($propertyID, $leaseID);
        $footer = new InvoiceFooter($propertyID, $leaseID, $dueDate, $totalTitle, $crn);

        $footer->bpayBillerCode = $bankData['bpayBillerCode'];
        $footer->deft = $bankData['deft'];
        $footer->directDeposit = ($footer->bpayBillerCode) ? false : $bankData['directDeposit'];
        $footer->directDebitAuth = $debtorBanking['directDebit'];
        $footer->isStrata = $isStrata;
        $footer->bindAttributesFrom($headerData);
        $footer->bindAttributesFrom($agentData);
        $footer->bindAttributesFrom($leaseAddress);
        $footer->bindAttributesFrom($bankData);
        $invoice->attachObject('footer', $footer);

        $agentDetails = new AgentDetails($propertyID);
        $agentDetails->bindAttributesFrom($agentData);
        $invoice->attachObject('agentDetails', $agentDetails);

        $leaseDescription = new LeaseDescription($propertyID, $leaseID);
        $leaseDescription->isStrata = $isStrata;
        $invoice->attachObject('leaseDescription', $leaseDescription);
        $invoiceLines = new InvoiceLines($totalTitle);
        $invoiceLines->isResidential = $isResidential;
        $invoice->attachObject('statement', $invoiceLines);
        $invoice->attachObject('foldline', new FoldLines());
        $invoice->attachObject('cutoffline', new CutOffLine());
        $invoice->attachObject('mailingAddress', new MailAddressWindow($propertyID, $leaseID));
        $invoice->attachObject(
            'traccFooter',
            new TraccFooter('assets/clientLogos/tracc_logo.jpg', 'Tenant_Tax_Invoice', 1)
        );

        if (isset($note)) {
            $invoice->attachObject('note', new InvoiceNote($note));
        }

        if ($parentInvoice) {
            $masterInvoice->objects = $invoice->objects;
        }

        $invoice->preparePage();
        if ($parentInvoice) {
            $masterInvoice->preparePage();
        }


        if (count($newAmounts ?? []) > 0) {
            $currentTotal = 0;
            $currentNet = 0;
            $currentTax = 0;

            foreach ($newAmounts as $row) {
                $currentTotal += ($row['amount'] * 1);
                $currentNet += ($row['netAmount'] * 1);
                $currentTax += ($row['taxAmount'] * 1);

                $total += ($row['amount'] * 1);
                $row['description'] = "{$row['description']} {$row['fromDate']} - {$row['toDate']}";

                $invoice->renderInvoiceRow(
                    $row['transactionDate'],
                    $invoiceNumber,
                    $row['description'],
                    toMoney($row['netAmount'], null),
                    toMoney($row['taxAmount'], null),
                    toMoney($row['amount'], null)
                );
                if ($parentInvoice) {
                    $masterInvoice->renderInvoiceRow(
                        $row['transactionDate'],
                        $invoiceNumber,
                        $row['description'],
                        toMoney($row['netAmount'], null),
                        toMoney($row['taxAmount'], null),
                        toMoney($row['amount'], null)
                    );
                }
            }

            $invoice->renderSubTotal(
                'Total For This Tax Invoice',
                toMoney($currentNet, null),
                toMoney($currentTax, null),
                toMoney($currentTotal, null)
            );
            if ($parentInvoice) {
                $masterInvoice->renderSubTotal(
                    'Total For This Tax Invoice',
                    toMoney($currentNet, null),
                    toMoney($currentTax, null),
                    toMoney($currentTotal, null)
                );
            }
        }


        if ($style == InvoiceStatementType::ST_STATEMENT && count($outstandingAmounts) > 0) {
            $titlePrinted = false;
            $count = 0;
            $previousTotal = 0;
            foreach ($outstandingAmounts as $row) {
                $offset = $row['totalAllocated'] + $row['totalReallocated'];
                if (($row['invoiceNumber'] == 0) && ($row['transactionType'] != TYPE_CASH)) {
                    if ($offset != 0) {
                        $offset *= -1;
                        $total += $offset;
                        $previousTotal += $offset;
                        if (! $titlePrinted) {
                            $invoice->renderTitle('OUTSTANDING AMOUNTS PREVIOUSLY INVOICED');
                            if ($parentInvoice) {
                                $masterInvoice->renderTitle('OUTSTANDING AMOUNTS PREVIOUSLY INVOICED');
                            }

                            $titlePrinted = true;
                        }

                        $invoice->renderInvoiceRow(
                            $row['transactionDate'],
                            null,
                            'Receipt/Credit allocated against above invoice(s) *',
                            null,
                            null,
                            toMoney($offset, null)
                        );
                        if ($parentInvoice) {
                            $masterInvoice->renderInvoiceRow(
                                $row['transactionDate'],
                                null,
                                'Receipt/Credit allocated against above invoice(s) *',
                                null,
                                null,
                                toMoney($offset, null)
                            );
                        }

                        $count++;
                    }
                } elseif ((($row['amount'] - $offset) != 0) && ($row['transactionType'] != TYPE_CASH)) {
                    $currentAmount = $row['amount'] - $offset;
                    $total += $currentAmount;
                    $previousTotal += $currentAmount;
                    if (! $titlePrinted) {
                        $invoice->renderTitle('OUTSTANDING AMOUNTS PREVIOUSLY INVOICED');
                        if ($parentInvoice) {
                            $masterInvoice->renderTitle('OUTSTANDING AMOUNTS PREVIOUSLY INVOICED');
                        }

                        $titlePrinted = true;
                    }

                    $row['description'] = "{$row['description']} {$row['fromDate']} - {$row['toDate']}";
                    $invoice->renderInvoiceRow(
                        $row['transactionDate'],
                        $row['invoiceNumber'],
                        $row['description'],
                        null,
                        null,
                        toMoney($currentAmount, null)
                    );
                    if ($parentInvoice) {
                        $masterInvoice->renderInvoiceRow(
                            $row['transactionDate'],
                            $row['invoiceNumber'],
                            $row['description'],
                            null,
                            null,
                            toMoney($currentAmount, null)
                        );
                    }

                    $count++;
                } elseif (($row['transactionType'] == TYPE_CASH) && ($row['amount'] != '') && ($row['amount'] != 0)) {
                    $unallocated[0] = $row;
                    foreach ($unallocated as $unallocatedItem) {
                        $unallocatedAmount = ($unallocatedItem['amount'] * 1);
                        $total += $unallocatedAmount;
                        $previousTotal += $unallocatedAmount;
                        if (! $titlePrinted) {
                            $invoice->renderTitle('OUTSTANDING AMOUNTS PREVIOUSLY INVOICED');
                            if ($parentInvoice) {
                                $masterInvoice->renderTitle('OUTSTANDING AMOUNTS PREVIOUSLY INVOICED');
                            }

                            $titlePrinted = true;
                        }

                        $invoice->renderInvoiceRow(
                            $unallocatedItem['date'],
                            null,
                            $unallocatedItem['description'],
                            null,
                            null,
                            toMoney($unallocatedAmount, null)
                        );
                        if ($parentInvoice) {
                            $masterInvoice->renderInvoiceRow(
                                $unallocatedItem['date'],
                                null,
                                $unallocatedItem['description'],
                                null,
                                null,
                                toMoney($unallocatedAmount, null)
                            );
                        }

                        $count++;
                    }
                }
            }

            if ($count > 0) {
                $invoice->renderSubTotal('Total Previously Invoiced', null, null, toMoney($previousTotal, null));
                if ($parentInvoice) {
                    $masterInvoice->renderSubTotal(
                        'Total Previously Invoiced',
                        null,
                        null,
                        toMoney($previousTotal, null)
                    );
                }
            }
        }

        $invoice->renderTotal($total);
        $invoice->renderRemittanceTotal($total);
        $invoice->close();

        if ($parentInvoice) {
            $masterInvoice->renderTotal($total);
            $masterInvoice->renderRemittanceTotal($total);
            $masterInvoice->close();
        }

        // -- if there are new transactions being reported - commit the new invoice number to them :)
        // -- modified 20100428 - added check against invoice number (only generated if there are new charges to commit)
        if ($invoiceNumber) {
            dbCommitTransaction($propertyID, $leaseID, $invoiceNumber, $dueDate, $toDate, $batchNumber);
        }

        $data['filePath'] = $filePath;
        $data['downloadPath'] = $downloadPath;
        $data['invoiceNumber'] = $invoiceNumber;

        return $data;
    }




}

function prepareTempInvoice(
    $leaseID,
    $propertyID,
    $dueDate,
    $transactionDate,
    $invoiceNumber,
    &$parentInvoice,
    $logo = true,
    $issueDate = ''
) {
    global $clientDirectory, $pathPrefix;

    // -- initialise the variables that will store the transaction amounts
    $total = 0;
    $newAmounts = [];
    // -- this may need revising (suppliers?) - but it grabs the
    dbGetDebtor($propertyID, $leaseID);
    $newAmounts = dbGetTempTransactions($propertyID, $leaseID);
    $property = dbGetPropertyForInvoice($propertyID);


    // -- if there are transactions to be printed :
    if ($newAmounts != null) {
        $headerTitle = 'TAX INVOICE';
        $totalTitle = 'INVOICE TOTAL';

        // $unallocated = dbGetUnallocated ($propertyID, $leaseID, $debtorID, $row['batchNumber'], $row['batchLineNumber']);
        // list ($day, $month, $year) = explode ('/', $transactionDate);
        [$day, $month, $year] = explode('/', $dueDate);

        // -- will need to store current DB index in session to allow for translation of logos
        $logoFile = dbGetClientLogo();
        $logoPath = ($logo) ? "assets/clientLogos/{$logoFile}" : null;

        $filename = "tax_invoice_{$year}{$month}{$day}_{$invoiceNumber}_{$propertyID}_{$leaseID}.pdf";
        $filePath = "{$pathPrefix}{$clientDirectory}/pdf/TaxInvoice/{$filename}";
        $downloadPath = "{$clientDirectory}/pdf/TaxInvoice/{$filename}";

        $invoice = new Invoice($filePath, $logoPath);
        if ($parentInvoice) {
            $masterInvoice = new Invoice($parentInvoice, $logoPath);
            $masterInvoice->isResidential = $isResidential;
            $masterInvoice->isStrata = $isStrata;
        }


        // $unallocated = dbGetUnallocated ($propertyID, $leaseID, $debtorID, $row['batchNumber'], $row['batchLineNumber']);
        // $agentData = dbGetAgentDetails ($property['propertyRemittanceOffice'], $propertyID);
        $agentData = dbGetAgentDetailsPerProperty($property['propertyRemittanceOffice'], $propertyID);
        $headerData = stripslashes_deep(dbGetHeaderDetails($propertyID, $leaseID));
        $leaseAddress = stripslashes_deep(dbGetLeaseMailingAddress($propertyID, $leaseID));
        $bankData = dbGetBankDetails($propertyID);

        // -- some data is shared across several components in the PDF, so instead of doing multiple db calls and binding locally within the object,
        // -- a single DB call is made and data bound to the object externally

        // -- PDFobjects are usually groups of data repeated statically on every page of the PDF, and as such are loaded once into the PDF and called at render...
        // -- mutating values are accessed from the Invoice object itself such as changing totals, invoice rows, sub titles within the statement

        // -- note that data is bound between array values to the object attributes sharing the same name as the array key - ie

        $header = new InvoiceHeader($propertyID, $leaseID, $dueDate, $headerTitle, $issueDate);
        $header->bindAttributesFrom($headerData);
        $header->bindAttributesFrom($bankData);
        $invoice->attachObject('header', $header);

        $crn = dbGetCRN($propertyID, $leaseID);

        $footer = new InvoiceFooter($propertyID, $leaseID, $dueDate, $totalTitle, $crn);
        $footer->bpayBillerCode = $bankData['bpayBillerCode'];
        $footer->directDeposit = ($footer->bpayBillerCode) ? false : $bankData['directDeposit'];
        $footer->bindAttributesFrom($headerData);

        $footer->bindAttributesFrom($leaseAddress);
        $footer->bindAttributesFrom($bankData);
        $footer->bindAttributesFrom($agentData);
        $invoice->attachObject('footer', $footer);


        $agentDetails = new AgentDetails($propertyID);
        $agentDetails->bindAttributesFrom($agentData);
        $invoice->attachObject('agentDetails', $agentDetails);

        $invoice->attachObject('leaseDescription', new LeaseDescription($propertyID, $leaseID));
        $invoiceLines = new InvoiceLines($totalTitle);
        $invoiceLines->isResidential = $isResidential;
        $invoice->attachObject('statement', $invoiceLines);
        $invoice->attachObject('foldline', new FoldLines());
        $invoice->attachObject('cutoffline', new CutOffLine());
        $invoice->attachObject('mailingAddress', new MailAddressWindow($propertyID, $leaseID));
        $invoice->attachObject(
            'traccFooter',
            new TraccFooter('assets/clientLogos/tracc_logo.jpg', 'Tenant_Tax_Invoice', 1)
        );

        if (isset($note)) {
            $invoice->attachObject('note', new InvoiceNote($note));
        }

        if ($parentInvoice) {
            $masterInvoice->objects = $invoice->objects;
        }

        $invoice->preparePage();
        if ($parentInvoice) {
            $masterInvoice->preparePage();
        }

        if (count($newAmounts ?? []) > 0) {
            $currentTotal = 0;
            $currentNet = 0;
            $currentTax = 0;

            foreach ($newAmounts as $row) {
                $currentTotal += ($row['transactionAmount'] * 1);
                $currentNet += ($row['netAmount'] * 1);
                $currentTax += ($row['taxAmount'] * 1);

                $total += ($row['transactionAmount'] * 1);


                $row['description'] = "{$row['description']} {$row['fromDate']} - {$row['toDate']}";

                $invoice->renderInvoiceRow(
                    $row['transactionDate'],
                    $invoiceNumber,
                    $row['description'],
                    toMoney($row['netAmount'], null),
                    toMoney($row['taxAmount'], null),
                    toMoney($row['transactionAmount'], null)
                );
                if ($parentInvoice) {
                    $masterInvoice->renderInvoiceRow(
                        $row['transactionDate'],
                        $invoiceNumber,
                        $row['description'],
                        toMoney($row['netAmount'], null),
                        toMoney($row['taxAmount'], null),
                        toMoney($row['transactionAmount'], null)
                    );
                }
            }

            $invoice->renderSubTotal(
                'Total For This Tax Invoice',
                toMoney($currentNet, null),
                toMoney($currentTax, null),
                toMoney($currentTotal, null)
            );
            if ($parentInvoice) {
                $masterInvoice->renderSubTotal(
                    'Total For This Tax Invoice',
                    toMoney($currentNet, null),
                    toMoney($currentTax, null),
                    toMoney($currentTotal, null)
                );
            }
        }


        $invoice->renderTotal($total);
        $invoice->renderRemittanceTotal($total);


        if ($parentInvoice) {
            $masterInvoice->renderTotal($total);
            $masterInvoice->renderRemittanceTotal($total);
            $masterInvoice->close();
        }


        if (! $invoiceHandle) {
            $invoice->close();
            $paths['filePath'] = $filePath;
            $paths['downloadPath'] = $downloadPath;

            return $paths;
        } else {
            $invoice->endPage();

            return;
        }
    }




}


/*************************************************************************************************************************/

// INVOICE OBJECTS - repeating groups that appear in each invoice

class InvoiceHeader extends PDFobject
{
    public $isStrata;

    public $asAtDate;

    public $issueDate = TODAY;

    public $propertyID;

    public $propertyName;

    public $leaseID;

    public $leaseName;

    public $ownerName;

    public $ownerABN;

    public $title;

    public $_name = 'invoiceHeader';

    public function __construct($title, $propertyID, $propertyName, $leaseID, $leaseName, $asAtDate)
    {
        $this->title = $title;
        $this->propertyID = $propertyID;
        $this->propertyName = $propertyName;
        $this->leaseID = $leaseID;
        $this->leaseName = $leaseName;
        $this->asAtDate = $asAtDate;
    }

    public function preRender(&$pdf)
    {
        // -- main box
        $pdf->setColorExt('both', 'rgb', 0.9, 0.9, 0.9, 0); // Setting the Color to Gray
        $pdf->rect(25, 777, 275, 15);
        $pdf->fill();
        $pdf->setlinewidth(0.5);

        $pdf->setColorExt('both', 'rgb', 0.75, 0.75, 0.75, 0);
        $pdf->moveto(25, 692);
        $pdf->lineto(300, 692);
        $pdf->stroke();

        $pdf->setlinewidth(2);
        $pdf->setColorExt('both', 'rgb', 0.75, 0.75, 0.75, 0);
        $pdf->rect(25, 692, 275, 100);
        $pdf->stroke();

        $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);

        $this->setFont($pdf, 'Helvetica-Bold', 8);
        $pdf->showBoxed($this->title, 25, 776, 260, 13, 'center', '');
        $pdf->showBoxed('As of', 30, 762, 50, 13, 'right', '');
        $pdf->showBoxed('Issue Date', 30, 752, 50, 13, 'right', '');
        $pdf->showBoxed('Property', 30, 742, 50, 13, 'right', '');
        $pdf->showBoxed((($this->isStrata) ? 'Owner' : 'Lease'), 30, 722, 50, 13, 'right', '');

        $pdf->showBoxed((($this->isStrata) ? 'Entity' : 'Owner'), 30, 702, 50, 13, 'right', '');
        $pdf->showBoxed((($this->isStrata) ? 'ABN' : 'Owner ABN'), 30, 692, 50, 13, 'right', '');
    }

    public function render(&$pdf)
    {
        $this->setFont($pdf, 'Helvetica', 8);
        $pdf->showBoxed($this->asAtDate, 85, 762, 215, 13, 'left', '');
        $pdf->showBoxed($this->issueDate, 85, 752, 215, 13, 'left', '');
        $pdf->showBoxed($this->propertyID, 85, 742, 215, 13, 'left', '');
        $pdf->showBoxed($this->propertyName, 85, 732, 215, 13, 'left', '');
        $pdf->showBoxed($this->leaseID, 85, 722, 215, 13, 'left', '');
        $pdf->showBoxed($this->leaseName, 85, 712, 215, 13, 'left', '');
        $pdf->showBoxed($this->ownerName, 85, 702, 215, 13, 'left', '');
        $pdf->showBoxed(textSpace($this->ownerABN), 85, 692, 215, 13, 'left', '');
    }
}

class InvoiceFooter extends PDFobject
{
    public $isStrata;

    public $dueDate;

    public $propertyID;

    public $propertyName;

    public $leaseID;

    public $leaseName;

    public $ownerName;

    public $ownerABN;

    public $title;

    public $premises;

    public $crn;

    public $officeName;

    public $officeAddress;

    public $officeCity;

    public $officeState;

    public $officePostCode;

    public $mailingName;

    public $mailingAddress;

    public $mailingCity;

    public $mailingState;

    public $mailingPostCode;

    public $bankAccount;

    public $bankAccountName;

    public $bankBSB;

    public $bankName;

    public $directDeposit;

    public $directDebitAuth;

    public $bpayBillerCode;

    public $showEftInfo;

    public $deft;

    public function __construct($propertyID, $leaseID, $dueDate, $title, $crn = null)
    {
        $this->propertyID = $propertyID;
        $this->leaseID = $leaseID;
        $this->dueDate = $dueDate;
        $this->title = $title;
        $this->crn = $crn;
        $this->premises = dbGetLeaseDescription($propertyID, $leaseID);
    }

    public function preRender(&$pdf) {}

    public function render(&$pdf)
    {
        $deft_adj_y = 0;
        if ($this->deft) {
            $deft_adj_y = 27;
        }

        $bpay_adj_y = 0;
        if ($this->bpayBillerCode) {
            $bpay_adj_y = 50;
        }

        $deft_adj_y = 0;
        if ($this->deft) {
            $deft_adj_y = 27;
        }

        $remittance_advice_adj_y = 0;
        $bpay_adj_y = 0;
        if ($this->bpayBillerCode) {
            $bpay_adj_y = 50;
            $remittance_advice_adj_y = 40;
        }

        $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        $pdf->setlinewidth(0.5);
        // Grey Areas...
        $pdf->setColorExt('both', 'rgb', 0.9, 0.9, 0.9, 0); // setting the color to Gray
        $pdf->rect(360, 240 - $remittance_advice_adj_y - $bpay_adj_y, 210, 15);
        $pdf->fill();

        if ($this->showEftInfo) {
            $pdf->rect(25, 240 - $deft_adj_y, 300, 15);
            $pdf->fill();
        }

        if ($this->bpayBillerCode) {
            $pdf->setColorExt('both', 'rgb', 0.95, 0.95, 0.95, 0); // setting the color to Gray
            $pdf->rect(360, 130 - $bpay_adj_y, 210, 70);
            $pdf->fill();
        } else {
            $pdf->setColorExt('both', 'rgb', 0.95, 0.95, 0.95, 0); // setting the color to Gray
            $pdf->rect(360, 130, 210, 110);
            $pdf->fill();
        }

        if ($this->showEftInfo) {
            $pdf->rect(25, 190 - $deft_adj_y, 300, 15);
            $pdf->fill();
        }

        $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0); // Setting the Color to Gray
        $pdf->rect(360, 100 - $bpay_adj_y, 210, 30);
        $pdf->fill();
        // Horizontal Lines...

        $pdf->setColorExt('both', 'rgb', 0.75, 0.75, 0.75, 0);

        if ($this->showEftInfo) {
            $pdf->moveto(25, 205 - $deft_adj_y);
            $pdf->lineto(325, 205 - $deft_adj_y);
            $pdf->stroke();
        }

        $pdf->moveto(360, 240 - $remittance_advice_adj_y - $bpay_adj_y);
        $pdf->lineto(570, 240 - $remittance_advice_adj_y - $bpay_adj_y);
        $pdf->stroke();

        if ($this->showEftInfo) {
            $pdf->moveto(25, 240 - $deft_adj_y);
            $pdf->lineto(325, 240 - $deft_adj_y);
            $pdf->stroke();
        }

        $pdf->setlinewidth(2);
        $pdf->setColorExt('both', 'rgb', 0.75, 0.75, 0.75, 0);
        $pdf->rect(360, 100 - $bpay_adj_y, 210, 155 - $remittance_advice_adj_y);
        $pdf->stroke();


        if ($this->showEftInfo) {
            $pdf->rect(25, 190 - $deft_adj_y, 300, 65);
            $pdf->stroke();
        }

        // Text
        $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);

        $this->setFont($pdf, 'Helvetica-Bold', 8);
        $pdf->showBoxed('REMITTANCE ADVICE', 360, 240 - $remittance_advice_adj_y - $bpay_adj_y, 210, 12, 'center', '');
        // -- end of codes cut from preRender

        $this->setFont($pdf, 'Helvetica', 8);
        $pdf->showBoxed(
            "{$this->officeName}\r\n{$this->officeAddress}\r\n{$this->officeCity}, {$this->officeState} {$this->officePostCode}",
            95,
            50,
            232,
            42,
            'left',
            ''
        );
        $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        $this->setFont($pdf, 'Helvetica', 8);
        $this->setFont($pdf, 'Helvetica-Bold', 7);
        if ($this->isStrata) {
            if ($this->bpayBillerCode) {
                $pdf->showBoxed(
                    "Due Date\nProperty\n\nLease\n\nOwner\nPremises",
                    362,
                    105 - $bpay_adj_y,
                    40,
                    83,
                    'right',
                    ''
                );
            } else {
                $pdf->showBoxed("Due Date\nProperty\n\nLease\n\nOwner\nPremises", 362, 140, 40, 83, 'right', '');
            }
        } elseif ($this->bpayBillerCode) {
            $pdf->showBoxed(
                "Due Date\nProperty\n\nLease\n\nOwner\nPremises",
                358,
                105 - $bpay_adj_y,
                40,
                83,
                'right',
                ''
            );
        } else {
            $pdf->showBoxed("Due Date\nProperty\n\nLease\n\nOwner\nPremises", 358, 140, 40, 83, 'right', '');
        }

        if ($this->bpayBillerCode) {
            $bpayX = 365;
            $bpayY = 237;
            $bpayW = 180;
            $image = $pdf->load_image('auto', BPAY, '');
            $pdf->fit_image($image, 357, 170, 'scale 0.25');
            $this->setFont($pdf, 'Helvetica-Bold', 8.5);
            $pdf->showBoxed("{$this->bpayBillerCode}", $bpayX + 140, 232, 250, 13, 'left', '');
            $pdf->showBoxed(
                ($this->deft) ? $this->bankAccount . '' . $this->crn : $this->crn,
                $bpayX + 106,
                221,
                250,
                13,
                'left',
                ''
            );
        }

        $this->setFont($pdf, 'Helvetica-Bold', 7);

        if ($this->showEftInfo) {
            if (getDisplayBsbFromSession()) {
                $pdf->showBoxed(
                    "Account\nBank\n" . getBsbLabelFromSession() . "\nAccount No",
                    30,
                    100 - $deft_adj_y,
                    50,
                    138,
                    'right',
                    ''
                );
            } else {
                $pdf->showBoxed("Account\nBank\nAccount No", 30, 100 - $deft_adj_y, 50, 138, 'right', '');
            }
        }

        $this->setFont($pdf, 'Helvetica', 7);
        if (strlen($this->ownerName) > 38) {
            $this->ownerName = substr($this->ownerName, 0, 38);
        }

        if (strlen($this->premises) > 38) {
            $this->premises = substr($this->premises, 0, 38);
        }

        if ($this->bpayBillerCode) {
            $pdf->showBoxed(
                "{$this->dueDate} \n{$this->propertyID}\n{$this->propertyName}\n{$this->leaseID}\n{$this->leaseName}\n{$this->ownerName}\n{$this->premises}",
                408,
                105 - $bpay_adj_y,
                415,
                83,
                'left',
                ''
            );
        } else {
            $pdf->showBoxed(
                "{$this->dueDate} \n{$this->propertyID}\n{$this->propertyName}\n{$this->leaseID}\n{$this->leaseName}\n{$this->ownerName}\n{$this->premises}",
                408,
                140,
                415,
                83,
                'left',
                ''
            );
        }

        $this->setFont($pdf, 'Helvetica', 8);

        if ($this->directDebitAuth && $this->directDeposit == 1) {
            $this->setFont($pdf, 'Helvetica-Oblique', 6);
            $pdf->showBoxed(
                '** As you have provided direct debit authority, the total amount payable will be debited on the due date.',
                30,
                140,
                275,
                13,
                'left',
                ''
            );
            $this->setFont($pdf, 'Helvetica', 8);
        }

        // DEFT Reference Number
        if ($this->deft) {
            $deft_adj_y2 = 12;
            $deft_logo = SYSTEMPATH . DIRECTORY_SEPARATOR . 'assets' . DIRECTORY_SEPARATOR . 'images' . DIRECTORY_SEPARATOR . 'logo_deft.jpg';
            if (file_exists($deft_logo)) {
                $image = $pdf->load_image('auto', $deft_logo, '');
                $pdf->fit_image($image, 25, 232, 'scale .35');
            }


            $pdf->setlinewidth(0.3);
            $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
            $pdf->rect(25, 110 - $deft_adj_y2, 300, 58);
            $pdf->stroke();
            $this->setFont($pdf, 'Helvetica-Bold', 7);
            $pdf->showBoxed(
                'DEFT Reference Number : ' . $this->bankAccount . ' ' . $this->crn,
                25,
                136 - $deft_adj_y2,
                300,
                30,
                'center',
                ''
            );
            $this->setFont($pdf, 'Helvetica', 7);
            $pdf->showBoxed(
                "Pay by credit card or registered bank account at www.deft.com.au. \nPayments by credit card will attract a surcharge.",
                30,
                126 - $deft_adj_y2,
                290,
                30,
                'center',
                ''
            );

            $cc_logo = BASEPATH . DIRECTORY_SEPARATOR . 'assets' . DIRECTORY_SEPARATOR . 'images' . DIRECTORY_SEPARATOR . 'logo_credit_cards_2.png';
            [$imageWidth, $imageHeight] = getimagesize($cc_logo);
            $image = $pdf->load_image('auto', $cc_logo, '');
            $pdf->fit_image($image, 75, 113 - $deft_adj_y2, 'scale ' . 23 / $imageHeight);
        }

        if ($this->showEftInfo) {
            $this->setFont($pdf, 'Helvetica', 7);

            if (getDisplayBsbFromSession()) {
                $pdf->showBoxed(
                    "{$this->bankAccountName}\n{$this->bankName}\n" . formatWithDelimiter(
                        $this->bankBSB
                    ) . "\n" . textSpace(
                        $this->bankAccount
                    ),
                    90,
                    100 - $deft_adj_y,
                    250,
                    138,
                    'left',
                    ''
                );
            } else {
                $pdf->showBoxed(
                    "{$this->bankAccountName}\n{$this->bankName}\n" . textSpace($this->bankAccount),
                    90,
                    100 - $deft_adj_y,
                    250,
                    138,
                    'left',
                    ''
                );
            }

            $this->setFont($pdf, 'Helvetica-Oblique', 7);
            $pdf->showBoxed(
                "If paying electronically please quote - {$this->propertyID}/{$this->leaseID}",
                30,
                190 - $deft_adj_y,
                250,
                13,
                'left',
                ''
            );
            $this->setFont($pdf, 'Helvetica-Bold', 8);

            $pdf->setColorExt('both', 'rgb', 1, 1, 1, 0); // Setting the Color to Gray
            $pdf->showBoxed(
                $this->title,
                367,
                100 - $bpay_adj_y,
                185,
                20,
                'left',
                ''
            ); // STATEMENT TOTAL (bottom right under remittance advice)
        }
    }
}

class MailAddressWindow extends PDFobject
{
    public $attention;

    public $name;

    public $street;

    public $city;

    public $lease;

    public $state;

    public $postcode;

    public $country;

    public function __construct($propertyID, $leaseID)
    {
        $mailAddress = dbGetMailingAddress($propertyID, $leaseID);
        if (! $mailAddress) {
            $mailAddress = dbGetLeaseMailingAddress($propertyID, $leaseID);
        }

        $this->name = stripslashes_deep($mailAddress['mailingName']);
        $this->lease = (isset($mailAddress['mailingLease'])) ? stripslashes_deep($mailAddress['mailingLease']) : null;
        $this->street = $mailAddress['mailingAddress'];
        $this->city = $mailAddress['mailingCity'];
        $this->state = $mailAddress['mailingState'];
        $this->postcode = $mailAddress['mailingPostCode'];
        if ($mailAddress['mailingCountry']) {
            $this->country = ($mailAddress['mailingCountry'] == 'AU') ? '' : dbGetCountryName(
                $mailAddress['mailingCountry']
            );
        }
    }

    public function preRender(&$pdf) {}

    public function render(&$pdf)
    {
        $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        $this->setFont($pdf, 'Helvetica', 10); // This is for the disclaimer information... /
        // set font to 10 2012-06-15 [jpalala]
        $address = (($this->attention) ? "{$this->attention}\n" : '') . "{$this->name}\n" . (($this->lease) ? "{$this->lease}\n" : '') . "{$this->street}\n{$this->city} {$this->state},  {$this->postcode}\n{$this->country}";
        $pdf->showBoxed($address, 95, 612, 242, 60, 'left', ''); // This is for the from field
    }
}

class LeaseDescription extends PDFObject
{
    public $description;

    public function __construct($propertyID, $leaseID)
    {
        $this->description = dbGetLeaseDescription($propertyID, $leaseID);
    }

    public function preRender(&$pdf) {}

    public function render(&$pdf)
    {
        $pdf->setColorExt('both', 'rgb', 0.95, 0.95, 0.95, 0);
        $pdf->setlinewidth(0.5);
        $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        $this->setFont($pdf, 'Helvetica-Bold', 9);
        $pdf->showBoxed('PREMISES : ' . $this->description, 30, 583, 535, 22, 'left', '');
    }
}

class InvoiceLines extends PDFobject
{
    public $title;

    public $isResidential;

    public function __construct($title)
    {
        $this->title = $title;
    }

    public function preRender(&$pdf)
    {
        global $sess;
        $currency = $sess->get('currencySymbol');

        $pdf->setColorExt('both', 'rgb', 0.9, 0.9, 0.9, 0); // Setting the Color to Gray
        $pdf->rect(25, 546, 545, 15);
        $pdf->fill();
        $pdf->rect(25, 285, 410, 15);
        $pdf->fill();
        $pdf->setlinewidth(0.5);

        $pdf->setColorExt('both', 'rgb', 0.75, 0.75, 0.75, 0);
        $pdf->moveto(25, 546);
        $pdf->lineto(570, 546);
        $pdf->stroke();
        $pdf->moveto(25, 300);
        $pdf->lineto(570, 300);
        $pdf->stroke();

        $pdf->setlinewidth(2);
        $pdf->rect(25, 285, 545, 276);
        $pdf->stroke();
        $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);

        $pdf->showBoxed('INV. DATE', 25, 546, 45, 13, 'center', '');
        $pdf->showBoxed('DUE DATE', 75, 546, 45, 13, 'center', '');
        $pdf->showBoxed('INV. NO.', 115, 546, 55, 13, 'center', '');
        $pdf->showBoxed('PARTICULARS', 175, 546, 203, 13, 'left', '');
        if (! $this->isResidential) {
            $pdf->showBoxed("NET ({$currency})", 400, 546, 45, 13, 'right', '');
            $pdf->showBoxed("GST ({$currency})", 460, 546, 45, 13, 'right', '');
        }

        $pdf->showBoxed("TOTAL ({$currency})", 520, 546, 45, 13, 'right', '');

        $this->setFont($pdf, 'Helvetica', 8);
    }

    public function render(&$pdf)
    {
        $this->setFont($pdf, 'Helvetica-Bold', 9);
        $pdf->showBoxed($this->title, 25, 286, 405, 13, 'right', '');
        $this->setFont($pdf, 'Helvetica', 8);
    }
}


class FoldLines extends PDFObject
{
    public function preRender(&$pdf)
    {
        $pdf->setColorExt('both', 'rgb', 0.8, 0.8, 0.8, 0);
        $pdf->setlinewidth(1);
        $pdf->moveto(0, 566);
        $pdf->lineto(2, 566);
        $pdf->stroke();
        $pdf->moveto(593, 566);
        $pdf->lineto(595, 566);
        $pdf->stroke();
        $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
    }

    public function render(&$pdf) {}
}

class CutOffLine extends PDFObject
{
    public function preRender(&$pdf)
    {
        //    $pdf->save();
        $pdf->setColorExt('both', 'rgb', 0.6, 0.6, 0.6, 0); // Setting the Color to Black
        $pdf->setlinewidth(0.5);
        $pdf->set_graphics_option('dasharray={2 2} dashphase=0');

        $pdf->moveto(0, 281);
        $pdf->lineto(595, 281);
        $pdf->stroke();


        $this->setFont($pdf, 'ZapfDingbats', 18);
        $pdf->showBoxed('#', 25, 269, 20, 20, 'left', '');

        $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        $this->setFont($pdf, 'Helvetica-Oblique', 6);
        $pdf->showBoxed(
            'Please detach this section and return with your payment:',
            0,
            265,
            595,
            10,
            'center',
            ''
        );
    }

    public function render(&$pdf) {}
}

class InvoiceNote extends PDFObject
{
    public $note;

    public function __construct($note)
    {
        $this->note = $note;
    }

    public function preRender(&$pdf) {}

    public function render(&$pdf)
    {
        $pdf->setColorExt('both', 'rgb', 0.95, 0.95, 0.95, 0);
        $pdf->setlinewidth(0.5);
        $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        $this->setFont($pdf, 'Helvetica-Oblique', 7);
        $pdf->showBoxed('NOTE: ' . $this->note, 30, 561, 535, 22, 'left', '');
    }
}


/*************************************************************************************************************************/

class Invoice extends PDFReport
{
    public $propertyID;

    public $pageCount;

    public $resourceList;

    public $lineOffset = 15;

    public $logoFile = false;

    public $isResidential = false;

    public $bpayBillerCode;

    public $deft;

    public function __construct(&$dataSource, $logoFile = false)
    {
        parent::__construct($dataSource);
        if ($logoFile) {
            $this->logoFile = $logoFile;
        }
    }

    public function updateLineOffset($offset)
    {
        $this->lineOffset += $offset;
        if ($this->lineOffset >= 245) {
            $this->renderTotal('continued...');
            $this->renderRemittanceTotal('continued...');
            $this->endPage();
            $this->preparePage();
            $this->render();
        }
    }

    public function renderArrearsLetter()
    {
        $this->prepareBlankPage();
        $this->pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        $this->setFont('Helvetica-Bold', 8);

        $this->pdf->showBoxed('Testing', 20, 546 - $this->lineOffset, 50, 13, 'right', '');

        $this->endPage();
        $this->preparePage();
        $this->render();
    }

    public function renderInvoiceRow($date, $invoiceNumber, $description, $netAmount, $gstAmount, $grossAmount)
    {
        $this->pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        $this->setFont('Helvetica-Bold', 8);

        $this->pdf->showBoxed($date, 20, 546 - $this->lineOffset, 50, 13, 'right', '');
        $this->pdf->showBoxed($invoiceNumber, 85, 546 - $this->lineOffset, 45, 13, 'center', '');
        $this->pdf->showBoxed($description, 140, 546 - $this->lineOffset, 300, 13, 'left', '');
        if (! $this->isResidential) {
            $this->pdf->showBoxed($netAmount, 395, 546 - $this->lineOffset, 50, 13, 'right', '');
            $this->pdf->showBoxed($gstAmount, 455, 546 - $this->lineOffset, 50, 13, 'right', '');
        }

        $this->pdf->showBoxed($grossAmount, 515, 546 - $this->lineOffset, 50, 13, 'right', '');

        $this->updateLineOffset(10);
    }

    public function renderInvoiceRow2($date, $dueDate, $invoiceNumber, $description, $netAmount, $gstAmount, $grossAmount)
    {
        if ($invoiceNumber == '0') {
            $invoiceNumber = '';
        }

        $this->pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        $this->setFont('Helvetica-Bold', 8);

        $this->pdf->showBoxed($date, 25, 546 - $this->lineOffset, 45, 13, 'center', '');
        $this->pdf->showBoxed($dueDate, 75, 546 - $this->lineOffset, 45, 13, 'center', '');
        $this->pdf->showBoxed($invoiceNumber, 115, 546 - $this->lineOffset, 55, 13, 'center', '');
        $this->pdf->showBoxed($description, 175, 546 - $this->lineOffset, 203, 13, 'left', '');
        if (! $this->isResidential) {
            $this->pdf->showBoxed($netAmount, 400, 546 - $this->lineOffset, 45, 13, 'right', '');
            $this->pdf->showBoxed($gstAmount, 460, 546 - $this->lineOffset, 45, 13, 'right', '');
        }

        $this->pdf->showBoxed($grossAmount, 520, 546 - $this->lineOffset, 45, 13, 'right', '');

        $this->updateLineOffset(10);
    }

    public function renderTotal($amount)
    {
        $this->pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        $this->setFont('Helvetica-Bold', 8);

        if (is_numeric($amount)) {
            $this->pdf->showBoxed(toMoney($amount), 25, 285, 540, 13, 'right', '');
        } else {
            $this->pdf->showBoxed($amount, 25, 285, 540, 13, 'right', '');
        }
    }

    public function renderRemittanceTotal($amount)
    {
        $this->pdf->setColorExt('both', 'rgb', 1, 1, 1, 1);
        $this->setFont('Helvetica-Bold', 9);

        $ownereft = dbGetEFTPropertyOwnerDetail($this->propertyID);
        if (isset($ownereft['accountNo'])) {
            $this->bpayBillerCode = false;
        }

        if (is_numeric($amount)) {
            $this->pdf->showBoxed(toMoney($amount), 380, 100 - ($this->bpayBillerCode ? 50 : 0), 185, 20, 'right', '');
        } else {
            $this->pdf->showBoxed(
                toMoney($amount, null),
                380,
                100 - ($this->bpayBillerCode ? 50 : 0),
                185,
                20,
                'right',
                ''
            );
        }

        $this->setFont('Helvetica', 8);
    }

    public function renderTitle($title)
    {
        $this->pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        $this->setFont('Helvetica-Bold', 8);
        $this->pdf->showBoxed($title, 30, 546 - $this->lineOffset, 400, 13, 'left', '');
        $this->updateLineOffset(20);
    }

    public function renderSubTotalLine()
    {
        $this->pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        $this->pdf->setlinewidth(0.5);
        $this->pdf->moveto(520, 559 - $this->lineOffset);
        $this->pdf->lineto(565, 559 - $this->lineOffset);
        $this->pdf->stroke();
    }

    public function renderSubTotal($title, $netAmount, $gstAmount, $grossAmount)
    {
        $this->pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        $this->pdf->setlinewidth(0.5);


        if (! $this->isResidential) {
            if ($netAmount) {
                $this->pdf->moveto(400, 559 - $this->lineOffset);
                $this->pdf->lineto(445, 559 - $this->lineOffset);
                $this->pdf->stroke();
            }

            if ($gstAmount) {
                $this->pdf->moveto(460, 559 - $this->lineOffset);
                $this->pdf->lineto(505, 559 - $this->lineOffset);
                $this->pdf->stroke();
            }
        }

        if ($grossAmount) {
            $this->pdf->moveto(520, 559 - $this->lineOffset);
            $this->pdf->lineto(565, 559 - $this->lineOffset);
            $this->pdf->stroke();
        }

        $this->setFont('Helvetica-Bold', 8);
        if (! $this->isResidential) {
            $this->pdf->showBoxed($netAmount, 395, 546 - $this->lineOffset, 50, 13, 'right', '');
            $this->pdf->showBoxed($gstAmount, 455, 546 - $this->lineOffset, 50, 13, 'right', '');
        }

        $this->pdf->showBoxed($grossAmount, 515, 546 - $this->lineOffset, 50, 13, 'right', '');

        if ($title) {
            $this->setFont('Helvetica-Bold', 8);
            $this->pdf->showBoxed($title, 180, 546 - $this->lineOffset, 300, 13, 'left', '');
            $this->setFont('Helvetica', 8);
        }

        $this->updateLineOffset(10);
    }

    public function renderPageNumber()
    {
        $this->setFont('Helvetica-Bold', 8);
        $this->pdf->showBoxed("{$this->pageCount}", $this->pageWidth - 50, 20, 25, 13, 'right', '');
    }

    public function renderTracc()
    {
        if (TRACC_LOGO) {
            $maxWidth = 20;
            $maxHeight = 15;
            [$imageWidth, $imageHeight] = getimagesize(TRACC_LOGO);

            $horizontalScaling = ($imageWidth > $maxWidth) ? $maxWidth / $imageWidth : 1;
            $verticalScaling = ($imageHeight > $maxHeight) ? $maxHeight / $imageHeight : 1;
            $imageScale = ($horizontalScaling < $verticalScaling) ? $horizontalScaling : $verticalScaling;


            $vMargin = 10;
            $hMargin = 25;

            if (! $this->resourceList['traccLogo']) {
                $this->resourceList['traccLogo'] = $this->pdf->load_image('auto', TRACC_LOGO, '');
            }

            $this->pdf->fit_image(
                $this->resourceList['traccLogo'],
                $hMargin,
                $vMargin + $maxHeight,
                "scale {$imageScale}"
            );
        }
    }

    public function renderLogo()
    {
        if ($this->logoFile && file_exists($this->logoFile)) {
            $maxWidth = LOGO_WIDTH_INVOICE;
            $maxHeight = LOGO_HEIGHT_INVOICE;
            [$imageWidth, $imageHeight] = getimagesize($this->logoFile);

            $horizontalScaling = ($imageWidth > $maxWidth) ? $maxWidth / $imageWidth : 1;
            $verticalScaling = ($imageHeight > $maxHeight) ? $maxHeight / $imageHeight : 1;
            $imageScale = ($horizontalScaling < $verticalScaling) ? $horizontalScaling : $verticalScaling;

            $imageScale = round($imageScale, 2);

            $vMargin = 25;
            $hMargin = 25;

            $hPos = $this->pageWidth - ($imageScale * $imageWidth) - $hMargin;
            $vPos = $this->pageHeight - ($imageScale * $imageHeight) - $vMargin;

            if (! $this->resourceList['logo']) {
                $this->resourceList['logo'] = $this->pdf->load_image('auto', $this->logoFile, '');
            }

            $this->pdf->fit_image($this->resourceList['logo'], $hPos, $vPos, "scale {$imageScale}");
        }
    }

    public function preparePage($printTemplate = true)
    {
        $this->lineOffset = 15;
        parent::preparePage();
        $this->render();
        $this->renderLogo();
    }

    public function prepareBlankPage()
    {
        $this->lineOffset = 15;
    }

    public function close()
    {
        parent::close();
    }
}

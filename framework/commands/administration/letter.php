<?php

function getCompaniesWithEmail($compIDs)
{
    global $dbh, $clientDB;
    $dbh->selectDatabase($clientDB);
    $params = [];
    $sql = "SELECT pmco_code, pmco_email
      FROM pmco_company
      WHERE (pmco_email != '' AND pmco_email IS NOT NULL)
    AND pmco_code IN (" . addSQLParam($params, $compIDs) . ')';

    return $dbh->executeSet($sql, false, true, $params);
}

function getCompaniesWithORWithoutEmail($compIDs)
{
    global $dbh, $clientDB;
    $dbh->selectDatabase($clientDB);
    $params = [];
    $sql = 'SELECT pmco_code, pmco_email
      FROM pmco_company
      WHERE pmco_code IN (' . addSQLParam($params, $compIDs) . ')';

    return $dbh->executeSet($sql, false, true, $params);
}

function getCompaniesWithoutEmail($compIDs)
{
    global $dbh, $clientDB;
    $dbh->selectDatabase($clientDB);
    $params = [];
    $sql = "SELECT pmco_code
      FROM pmco_company
      WHERE (pmco_email = '' OR pmco_email IS NULL)
    AND pmco_code IN (" . addSQLParam($params, $compIDs) . ')';

    return $dbh->executeScalars($sql, $params);
}

function getCompaniesWithoutEmailCentralisation($company_code_array, $property_code_array)
{
    $company_code_list = [];
    foreach ($company_code_array as $company_code) {
        $properties = dbGetOwnerPropertiesUsingOwnerID($company_code, $property_code_array);
        foreach ($properties as $property) {
            $property_code = $property['pmos_prop'];
            $check_if_there_is_valid_email = false;
            $emailAddress = dbGetPropertiesOwnersWithEmailCentralised($property_code, $company_code, 'OWNGEN');
            foreach ($emailAddress as $aRowCentralisedEmailCheck) {
                $emailAddressCentral = $aRowCentralisedEmailCheck['emailAddress'];
                $company_code_lc = $aRowCentralisedEmailCheck['company_code'];
                if ($company_code_lc == $company_code && isValid($emailAddressCentral, TEXT_EMAIL, false)) {
                    $check_if_there_is_valid_email = true;
                }
            }

            if (! $check_if_there_is_valid_email) {
                $is_exist = false;
                foreach ($company_code_list as $row_data) {
                    if ($row_data == $company_code) {
                        $is_exist = true;
                    }
                }

                if (! $is_exist) {
                    $company_code_list[] = $company_code;
                }
            }
        }
    }

    return $company_code_list;
    //    $emailAddress = dbGetPropertiesOwnersWithEmailCentralised($propertyID, "OWNGEN");
}

function getSupplierWithoutEmailCentralisation($compIDs)
{
    // $emailAddress = dbGetCompanyEmailCentralised($suppID, "SUPGEN");
    $arr_list = [];
    $suppliersToUse = getCompaniesWithORWithoutEmail($compIDs);
    foreach ($suppliersToUse as $supplierList_) {
        $suppID = $supplierList_['pmco_code'];
        $emailAddressCentral = '';
        $check_if_there_is_valid_email = false;
        $emailAddress = dbGetCompanyEmailCentralised($suppID, 'SUPGEN');
        foreach ($emailAddress as $aRowCentralisedEmailCheck) {
            $emailAddressCentral = $aRowCentralisedEmailCheck['emailAddress'];
            if (isValid($emailAddressCentral, TEXT_EMAIL, false)) {
                $check_if_there_is_valid_email = true;
            }
        }

        if (! $check_if_there_is_valid_email) {
            $is_exist = false;
            foreach ($arr_list as $row_data) {
                if ($row_data == $suppID) {
                    $is_exist = true;
                }
            }

            if (! $is_exist) {
                $arr_list[] = $suppID;
            }
        }
    }

    return $arr_list;
    //    $emailAddress = dbGetPropertiesOwnersWithEmailCentralised($propertyID, "OWNGEN");
}

function getOwnerCompanyWithoutEmailCentralisation($compIDs)
{
    $arr_list = [];
    $ownersToUse = getCompaniesWithORWithoutEmail($compIDs);
    foreach ($ownersToUse as $ownersList_) {
        $check_if_there_is_valid_email = false;
        $ownerID = $ownersList_['pmco_code'];
        $emailAddressCentral = '';
        $emailAddress = dbGetCompanyEmailCentralised($ownerID, 'OWNGEN');
        foreach ($emailAddress as $aRowCentralisedEmailCheck) {
            $emailAddressCentral = $aRowCentralisedEmailCheck['emailAddress'];
            if (isValid($emailAddressCentral, TEXT_EMAIL, false)) {
                $check_if_there_is_valid_email = true;
            }
        }

        if (! $check_if_there_is_valid_email) {
            $is_exist = false;
            foreach ($arr_list as $row_data) {
                if ($row_data == $ownerID) {
                    $is_exist = true;
                }
            }

            if (! $is_exist) {
                $arr_list[] = $ownerID;
            }
        }
    }

    return $arr_list;
    //    $emailAddress = dbGetPropertiesOwnersWithEmailCentralised($propertyID, "OWNGEN");
}

function getTenantsWithEmail($prop_tenantIDs)
{
    global $dbh, $clientDB;
    $dbh->selectDatabase($clientDB);
    $params = [];
    $sql = "SELECT DISTINCT
			pmle_prop,
			pmle_lease leaseID,
			pmco_email
		FROM
			pmle_lease
		LEFT JOIN
			pmpr_property ON (pmpr_prop = pmle_prop)
		LEFT JOIN
			pmco_company ON (pmle_debtor = pmco_code)
		WHERE
			pmpr_delete=0
		AND LTRIM(RTRIM(pmle_prop)) + ' - ' + LTRIM(RTRIM(pmle_lease)) IN (" . addSQLParam($params, $prop_tenantIDs) . ")
		AND (pmco_email != '' AND pmco_email IS NOT NULL)
		ORDER BY
			pmle_prop,
			pmle_lease";

    return $dbh->executeSet($sql, false, true, $params);
}

function getTenantsWithOrWithoutEmail($prop_tenantIDs)
{
    global $dbh, $clientDB;
    $dbh->selectDatabase($clientDB);
    $params = [];
    $sql = "SELECT DISTINCT
			pmle_prop,
			pmle_lease leaseID,
			pmco_email
		FROM
			pmle_lease
		LEFT JOIN
			pmpr_property ON (pmpr_prop = pmle_prop)
		LEFT JOIN
			pmco_company ON (pmle_debtor = pmco_code)
		WHERE
			pmpr_delete=0
		AND LTRIM(RTRIM(pmle_prop)) + ' - ' + LTRIM(RTRIM(pmle_lease)) IN (" . addSQLParam($params, $prop_tenantIDs) . ')
		ORDER BY
			pmle_prop,
			pmle_lease';

    return $dbh->executeSet($sql, false, true, $params);
}

function getTenantsAll($prop_tenantIDs)
{
    global $dbh, $clientDB;
    $dbh->selectDatabase($clientDB);
    $params = [];
    $sql = "SELECT DISTINCT
			pmle_prop,
			pmle_lease leaseID,
			pmco_email
		FROM
			pmle_lease
		LEFT JOIN
			pmpr_property ON (pmpr_prop = pmle_prop)
		LEFT JOIN
			pmco_company ON (pmle_debtor = pmco_code)
		WHERE
			pmpr_delete=0
		AND LTRIM(RTRIM(pmle_prop)) + ' - ' + LTRIM(RTRIM(pmle_lease)) IN (" . addSQLParam($params, $prop_tenantIDs) . ')
		ORDER BY
			pmle_prop,
			pmle_lease';

    return $dbh->executeSet($sql, false, true, $params);
}

function getTenantsWithoutEmail($prop_tenantIDs)
{
    global $dbh, $clientDB;
    $dbh->selectDatabase($clientDB);
    $params = [];
    $sql = "SELECT DISTINCT
			pmle_prop,
			pmle_lease leaseID,
			pmco_email
		FROM
			pmle_lease
		LEFT JOIN
			pmpr_property ON (pmpr_prop = pmle_prop)
		LEFT JOIN
			pmco_company ON (pmle_debtor = pmco_code)
		WHERE
			pmpr_delete=0
		AND LTRIM(RTRIM(pmle_prop)) + ' - ' + LTRIM(RTRIM(pmle_lease)) IN (" . addSQLParam($params, $prop_tenantIDs) . ")
	    AND (pmco_email = '' OR pmco_email IS NULL)
		ORDER BY
			pmle_prop,
			pmle_lease";

    return $dbh->executeSet($sql, false, true, $params);
}

function getTenantsWithoutEmailCentralisation($prop_tenantsToUse, $email_param)
{
    //    $emailAddress = dbGetDebtorEmailCentralised($propertyID, $leaseID, "TENGEN");
    $prop_tenantsToUse = getTenantsWitEmail($prop_tenantsToUse);
    $arr_list = [];
    foreach ($prop_tenantsToUse as $prop_tenantDetails) {
        $property_code = $prop_tenantDetails['pmle_prop'];
        $lease_code = $prop_tenantDetails['leaseID'];
        $emailAddressCentral = '';
        $check_if_there_is_valid_email = false;
        $emailAddress = dbGetDebtorEmailCentralised($property_code, $lease_code, $email_param);
        foreach ($emailAddress as $aRowCentralisedEmailCheck) {
            $emailAddressCentral = $aRowCentralisedEmailCheck['emailAddress'];
            if (isValid($emailAddressCentral, TEXT_EMAIL, false)) {
                $check_if_there_is_valid_email = true;
            }
        }

        if (! $check_if_there_is_valid_email) {
            $is_exist = false;
            foreach ($arr_list as $row_data) {
                if ($row_data['pmle_prop'] == $property_code && $row_data['leaseID'] == $lease_code && $row_data['pmco_email'] == $emailAddressCentral) {
                    $is_exist = true;
                }
            }

            if (! $is_exist) {
                $arr_list[] = [
                    'pmle_prop' => $property_code,
                    'leaseID' => $lease_code,
                    'pmco_email' => $emailAddressCentral,
                ];
            }
        }
    }

    return $arr_list;
}

function getTenantsWitEmail($prop_tenantIDs)
{
    global $dbh, $clientDB;
    $dbh->selectDatabase($clientDB);
    $params = [];
    $sql = "SELECT DISTINCT
			pmle_prop,
			pmle_lease leaseID,
			pmco_email
		FROM
			pmle_lease
		LEFT JOIN
			pmpr_property ON (pmpr_prop = pmle_prop)
		LEFT JOIN
			pmco_company ON (pmle_debtor = pmco_code)
		WHERE
			pmpr_delete=0
		AND LTRIM(RTRIM(pmle_prop)) + ' - ' + LTRIM(RTRIM(pmle_lease)) IN (" . addSQLParam($params, $prop_tenantIDs) . ")
		--AND (pmco_email = '' OR pmco_email IS NULL)
		ORDER BY
			pmle_prop,
			pmle_lease";

    return $dbh->executeSet($sql, false, true, $params);
}

function dbGetOwnerPropertiesUsingOwnerID($ownerID, $properties)
{
    global $dbh, $clientDB;
    $dbh->selectDatabase($clientDB);
    $params = [];
    if (is_array($properties) && count($properties ?? []) > 0) {
        $properties = implode("','", $properties);
    }

    if (! is_array($ownerID)) {
        $ownerID = explode(',', $ownerID);
    }

    $sql = 'SELECT pmos_owner , pmos_prop FROM pmos_o_share
                  WHERE pmos_owner IN (' . addSQLParam($params, $ownerID) . ")
                  AND pmos_prop IN ('" . $properties . "')";

    return $dbh->executeSet($sql, false, true, $params);
}

function dbGetOwnerPropertyReplacements($ownerID, $propertyID, $plainEmail = false)
{
    global $dbh, $clientDB;
    $dbh->selectDatabase($clientDB);
    $plainEmail = $plainEmail ? 'rtrim(portemail.pmzz_desc)' : "'<a style=\"color:#000000;text-decoration:none\" href=\"mailto:' + rtrim(portemail.pmzz_desc)  + '\">' + rtrim(portemail.pmzz_desc)  + '</a>'";
    $sql = "SELECT pmpr_portfolio as propertyManagerID, portmgr.pmzz_desc as propertyManagerName, porttit.pmzz_desc as propertyManagerTitle,
        {$plainEmail} as propertyManagerEmail,
        portmobile.pmzz_desc as propertyManagerMobileNumber,
        pmpr_name as propertyName,
        pmpr_street,
        pmpr_city,
        pmpr_state,
        pmpr_postcode,
        portproppgroup.pmzz_desc as propertyDescription,
		pmco_name,
		RTRIM(pmco_company.pmco_code) as ownerCode,
		RTRIM(pmco_company.pmco_street) as ownerStreet,
		RTRIM(pmco_company.pmco_city) as ownerSuburb,
		RTRIM(pmco_company.pmco_postcode) as ownerPostCode,
		RTRIM(pmco_company.pmco_state) as ownerState,
		pmpr_prop as prop_code
        FROM pmpr_property
        LEFT OUTER JOIN pmzz_param as portmgr ON (portmgr.pmzz_par_type = 'PORTMGR' AND pmpr_portfolio = portmgr.pmzz_code)
        LEFT OUTER JOIN pmzz_param as porttit ON (porttit.pmzz_par_type = 'PORTTIT' AND pmpr_portfolio = porttit.pmzz_code)
        LEFT OUTER JOIN pmzz_param portemail ON (portemail.pmzz_par_type = 'PORTMEMAIL ' AND portemail.pmzz_code = portmgr.pmzz_code)
        LEFT OUTER JOIN pmzz_param portmobile ON (portmobile.pmzz_par_type = 'PORTMOBILE ' AND portmobile.pmzz_code = portmgr.pmzz_code)
        LEFT JOIN pmzz_param as portproppgroup ON (portproppgroup.pmzz_par_type = 'PROPGROUP' AND pmpr_prop_group = portproppgroup.pmzz_code)
		LEFT OUTER JOIN pmco_company ON (pmco_code = ?)
        WHERE pmpr_prop = ?";

    return $dbh->executeSingle($sql, [$ownerID, $propertyID]);
}

function dbGetCompanyContactSalutation($ownerID)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $sql = "
		  SELECT pmcj_phone_no
          FROM pmct_c_contact
          left join pmcj_c_phone ON pmct_c_contact.pmct_serial = pmcj_c_phone.pmcj_c_serial
          where pmct_company = ?
          and pmcj_company = ?
          and pmct_primary = 1
          and pmcj_ph_code = 'SALU'
          ";

    return $dbh->executeScalar($sql, [$ownerID, $ownerID]);
}

// function dbGetLeaseContactSalutation($propertyID, $leaseID)
// {
//    global $clientDB, $dbh;
//    $dbh->selectDatabase ($clientDB);
//    $sql = "
//        select pmlj_phone_no FROM
//          pmlt_l_contact
//        JOIN pmlj_l_phone ON pmlt_prop=pmlj_prop AND pmlt_lease=pmlj_lease AND pmlt_serial=pmlj_c_serial
//        WHERE pmlt_prop = '{$propertyID}'
//        AND pmlt_lease = '{$leaseID}'
//        AND pmlj_ph_code = 'SALU'
//        ";
//    return $dbh->executeScalar ($sql);
// }

function dbGetSupplierReplacements($suppID)
{
    global $dbh, $clientDB;
    $dbh->selectDatabase($clientDB);

    $replacements = [
        date('j'),
        date('n'),
        date('F'),
        date('Y'),
    ];


    $sql = 'SELECT pmco_name, pmco_street, pmco_city, pmco_state, pmco_postcode
            FROM pmco_company WHERE pmco_code = ?;';
    $supplier_rep = $dbh->executeSingle($sql, [$suppID]);

    $contact = dbGetSupplierContactSalutation($suppID);
    $contactSalutation = ['contactSalutation' => $contact];

    return $replacements + $supplier_rep + $contactSalutation;
}


function dbGetOwnerCompanyReplacements($ownerID)
{
    global $dbh, $clientDB;
    $dbh->selectDatabase($clientDB);

    $replacements = [
        date('j'),
        date('n'),
        date('F'),
        date('Y'),
    ];


    $sql = 'SELECT pmco_code, pmco_name, pmco_street, pmco_city, pmco_state, pmco_postcode
            FROM pmco_company WHERE pmco_code = ?;';
    $supplier_rep = $dbh->executeSingle($sql, [$ownerID]);

    $contact = dbGetSupplierContactSalutation($ownerID);
    $contactSalutation = ['contactSalutation' => $contact];

    return $replacements + $supplier_rep + $contactSalutation;
}

function dbGetTenantReplacements($propertyID, $leaseID, $plainEmail = false)
{
    global $dbh, $clientDB;
    $dbh->selectDatabase($clientDB);

    $replacements = [
        date('j'),
        date('n'),
        date('F'),
        date('Y'),
    ];

    $plainEmail = $plainEmail ? 'rtrim(portemail.pmzz_desc)' : "'<a style=\"color:#000000;text-decoration:none\" href=\"mailto:' + rtrim(portemail.pmzz_desc)  + '\">' + rtrim(portemail.pmzz_desc)  + '</a>'";
    $sql = "SELECT portmgr.pmzz_desc as propertyManagerName, porttit.pmzz_desc as propertyManagerTitle,
            {$plainEmail} as propertyManagerEmail,
            portmobile.pmzz_desc as propertyManagerMobileNumber,
            pmpr_name as propertyName,
            pmpr_street,
            pmpr_city,
            pmpr_state,
            pmpr_postcode,
            pmpr_prop as propertyCode, --07/25/2018
            pmco_name AS principalOwner,
            pmco_gst_no AS principalOwnerABN,
            pmle_name,
            pmle_description,
            pmle_street,
            pmle_city,
            pmle_state,
            pmle_postcode,
            pmle_exp_dt as leaseExpiry, --07/25/2018
            DATEADD(DAY, 1, pmle_exp_dt) as nextTermCommencement,
            pmle_crn as CRN,
            pmle_com_dt as leaseCommencement, --07/25/2018
            pmle_option as leaseOption, --07/25/2018
            pmle_lease as leaseCode, --07/25/2018
            '' as rentPM,
            '' as rentAnnual,
			pmpu_desc as unitDescription,
            pmle_t_name
            FROM pmpr_property
            LEFT JOIN pmco_company t3 ON (pmco_code=pmpr_owner)
            LEFT OUTER JOIN pmzz_param as portmgr ON (portmgr.pmzz_par_type = 'PORTMGR' AND pmpr_portfolio = portmgr.pmzz_code)
            LEFT OUTER JOIN pmzz_param as porttit ON (porttit.pmzz_par_type = 'PORTTIT' AND pmpr_portfolio = porttit.pmzz_code)
            LEFT OUTER JOIN pmzz_param portemail ON (portemail.pmzz_par_type = 'PORTMEMAIL ' AND portemail.pmzz_code = portmgr.pmzz_code)
            LEFT OUTER JOIN pmzz_param portmobile ON (portmobile.pmzz_par_type = 'PORTMOBILE ' AND portmobile.pmzz_code = portmgr.pmzz_code)
            LEFT OUTER JOIN pmle_lease ON (pmpr_prop = pmle_prop)
			LEFT OUTER JOIN pmua_unit_area ON (pmua_lease = pmle_lease AND pmua_prop = pmpr_prop)
			LEFT OUTER JOIN pmpu_p_unit ON (pmpu_unit = pmua_unit AND pmpu_prop = pmua_prop)
            WHERE pmpr_prop = ?
            AND pmle_lease = ?";
    $prop_lease_rep = $dbh->executeSingle($sql, [$propertyID, $leaseID]);

    $leaseContactSalutation = dbGetLeaseContactSalutation($propertyID, $leaseID, true);

    $contactSalutationLease = ['leaseContactSalutation' => $leaseContactSalutation];


    $rentCharge = dbGetLeaseChargeRentReview($propertyID, $leaseID);
    $prop_lease_rep['rentPM'] = number_format(($rentCharge / 12), 2, '.', ',');
    $prop_lease_rep['rentAnnual'] = number_format(($rentCharge ? $rentCharge : 0), 2, '.', ',');


    $rentReviewDate = '
            SELECT
            TOP 1 pmrr_rev_dt
             FROM pmrr_l_rent_rev
            WHERE pmrr_prop = ?
            AND pmrr_lease = ?
            AND pmrr_closed = 0
            ORDER BY pmrr_rev_dt ASC
            ';
    $nextRentReviewDate = $dbh->executeSingle($rentReviewDate, [$propertyID, $leaseID]);
    if (count($nextRentReviewDate ?? []) > 0) {
        $replacements = $replacements + $prop_lease_rep + $contactSalutationLease + $nextRentReviewDate;
    } else {
        $replacements = $replacements + $prop_lease_rep + $contactSalutationLease + ['pmrr_rev_dt' => ''];
    }

    // nextScheduledInspectionDate
    $replacements += [
        'nextScheduledInspectionDate' => dbGetNextScheduledInspectionDate(
            $propertyID,
            $leaseID
        ),
    ];

    $nextInsurance = dbGetNextScheduledInsurance($propertyID, $leaseID);

    $replacements += [
        'nextInsuranceExpiryDate' => $nextInsurance['nextInsuranceExpiryDate'],
        'nextInsuranceExpiryType' => $nextInsurance['nextInsuranceExpiryType'],
        'nextInsuranceExpiryNote' => $nextInsurance['nextInsuranceExpiryNote'],
    ];

    $mailingInfo = dbGetPlaceholderMailingAddress($propertyID, $leaseID);


    return $replacements + [
        'mailingName' => $mailingInfo['mailingName'],
        'mailingAddress' => $mailingInfo['mailingAddress'],
        'mailingCity' => $mailingInfo['mailingCity'],
        'mailingState' => $mailingInfo['mailingState'],
        'mailingPostCode' => $mailingInfo['mailingPostCode'],
        'mailingCountry' => $mailingInfo['mailingCountry'],
    ];
}

function dbGetTenantDiaryReplacements($propertyID, $leaseID, $plainEmail = false)
{
    global $dbh, $clientDB;
    $dbh->selectDatabase($clientDB);

    $replacements = [
        date('j'),
        date('n'),
        date('F'),
        date('Y'),
    ];

    $plainEmail = $plainEmail ? 'rtrim(portemail.pmzz_desc)' : "'<a style=\"color:#000000;text-decoration:none\" href=\"mailto:' + rtrim(portemail.pmzz_desc)  + '\">' + rtrim(portemail.pmzz_desc)  + '</a>'";
    $sql = "SELECT portmgr.pmzz_desc as propertyManagerName, porttit.pmzz_desc as propertyManagerTitle,
            {$plainEmail} as propertyManagerEmail,
            portmobile.pmzz_desc as propertyManagerMobileNumber,
            pmpr_name as propertyName,
            pmpr_street,
            pmpr_city,
            pmpr_state,
            pmpr_postcode,
            pmpr_prop as propertyCode, --07/25/2018
            pmco_name AS principalOwner,
            pmco_gst_no AS principalOwnerABN,
            pmle_name,
            pmle_description,
            pmle_street,
            pmle_city,
            pmle_state,
            pmle_postcode,
            pmle_exp_dt as leaseExpiry, --07/25/2018
            DATEADD(DAY, 1, pmle_exp_dt) as nextTermCommencement,
            pmle_crn as CRN,
            pmle_com_dt as leaseCommencement, --07/25/2018
            pmle_option as leaseOption, --07/25/2018
            pmle_lease as leaseCode, --07/25/2018
			pmpu_desc as unitDescription,
            pmle_t_name
            FROM pmpr_property
            LEFT JOIN pmco_company t3 ON (pmco_code=pmpr_owner)
            LEFT OUTER JOIN pmzz_param as portmgr ON (portmgr.pmzz_par_type = 'PORTMGR' AND pmpr_portfolio = portmgr.pmzz_code)
            LEFT OUTER JOIN pmzz_param as porttit ON (porttit.pmzz_par_type = 'PORTTIT' AND pmpr_portfolio = porttit.pmzz_code)
            LEFT OUTER JOIN pmzz_param portemail ON (portemail.pmzz_par_type = 'PORTMEMAIL ' AND portemail.pmzz_code = portmgr.pmzz_code)
            LEFT OUTER JOIN pmzz_param portmobile ON (portmobile.pmzz_par_type = 'PORTMOBILE ' AND portmobile.pmzz_code = portmgr.pmzz_code)
            LEFT OUTER JOIN pmle_lease ON (pmpr_prop = pmle_prop)
			LEFT OUTER JOIN pmua_unit_area ON (pmua_lease = pmle_lease AND pmua_prop = pmpr_prop)
			LEFT OUTER JOIN pmpu_p_unit ON (pmpu_unit = pmua_unit AND pmpu_prop = pmua_prop)
            WHERE pmpr_prop = ?
            AND pmle_lease = ?";
    $prop_lease_rep = $dbh->executeSingle($sql, [$propertyID, $leaseID]);

    $leaseContactSalutation = dbGetLeaseContactSalutation($propertyID, $leaseID, true);

    $contactSalutationLease = ['leaseContactSalutation' => $leaseContactSalutation];

    $rentReviewDate = "
            SELECT
            TOP 1 pmrr_rev_dt
             FROM pmrr_l_rent_rev
            WHERE pmrr_prop = '{$propertyID}'
            AND pmrr_lease = '{$leaseID}'
            AND pmrr_closed = 0
            ORDER BY pmrr_rev_dt ASC
            ";
    $nextRentReviewDate = $dbh->executeSingle($rentReviewDate, [$propertyID, $leaseID]);
    if (count($nextRentReviewDate ?? []) > 0) {
        $replacements = $replacements + $prop_lease_rep + $contactSalutationLease + $nextRentReviewDate;
    } else {
        $replacements = $replacements + $prop_lease_rep + $contactSalutationLease + ['pmrr_rev_dt' => ''];
    }

    // nextScheduledInspectionDate
    $replacements += [
        'nextScheduledInspectionDate' => dbGetNextScheduledInspectionDate(
            $propertyID,
            $leaseID
        ),
    ];

    $nextInsurance = dbGetNextScheduledInsurance($propertyID, $leaseID);

    return $replacements + [
        'nextInsuranceExpiryDate' => $nextInsurance['nextInsuranceExpiryDate'],
        'nextInsuranceExpiryType' => $nextInsurance['nextInsuranceExpiryType'],
        'nextInsuranceExpiryNote' => $nextInsurance['nextInsuranceExpiryNote'],
    ];
}

function dbGetBudgetReplacements($propertyID, $leaseID, $budgetYear, $budget_type, $plainEmail = false)
{
    global $dbh, $clientDB;
    $dbh->selectDatabase($clientDB);

    $replacements = [
        'day' => date('j'),
        'month' => date('n'),
        'monthName' => date('F'),
        'year' => date('Y'),
    ];

    $plainEmail = $plainEmail ? 'rtrim(portemail.pmzz_desc)' : "'<a style=\"color:#000000;text-decoration:none\" href=\"mailto:' + rtrim(portemail.pmzz_desc)  + '\">' + rtrim(portemail.pmzz_desc)  + '</a>'";
    $sql = "SELECT portmgr.pmzz_desc as propertyManagerName, porttit.pmzz_desc as propertyManagerTitle,
            {$plainEmail} as propertyManagerEmail,
            portmobile.pmzz_desc as propertyManagerMobileNumber,
            pmpr_name as propertyName,
            pmpr_street,
            pmpr_city,
            pmpr_state,
            pmpr_postcode,
            pmpr_owner,
            pmle_lease,
            pmle_name,
            pmle_description,
            pmle_street,
            pmle_city,
            pmle_state,
            pmle_postcode,
			pmpu_desc as unitDescription,
            pmle_t_name
            FROM pmpr_property
            LEFT OUTER JOIN pmzz_param as portmgr ON (portmgr.pmzz_par_type = 'PORTMGR' AND pmpr_portfolio = portmgr.pmzz_code)
            LEFT OUTER JOIN pmzz_param as porttit ON (porttit.pmzz_par_type = 'PORTTIT' AND pmpr_portfolio = porttit.pmzz_code)
            LEFT OUTER JOIN pmzz_param portemail ON (portemail.pmzz_par_type = 'PORTMEMAIL ' AND portemail.pmzz_code = portmgr.pmzz_code)
            LEFT OUTER JOIN pmzz_param portmobile ON (portmobile.pmzz_par_type = 'PORTMOBILE ' AND portmobile.pmzz_code = portmgr.pmzz_code)
            LEFT OUTER JOIN pmle_lease ON (pmpr_prop = pmle_prop)
			LEFT OUTER JOIN pmua_unit_area ON (pmua_lease = pmle_lease AND pmua_prop = pmpr_prop)
			LEFT OUTER JOIN pmpu_p_unit ON (pmpu_unit = pmua_unit AND pmpu_prop = pmua_prop)
            WHERE pmpr_prop = ?
            AND pmle_lease = ?";
    $prop_lease_rep = $dbh->executeSingle($sql, [$propertyID, $leaseID]);

    $replacements += $prop_lease_rep;
    $leaseContactSalutation = dbGetLeaseContactSalutation($propertyID, $leaseID, true);

    $contactSalutationLease = ['leaseContactSalutation' => $leaseContactSalutation];

    $rentReviewDate = '
            SELECT
            TOP 1 pmrr_rev_dt
             FROM pmrr_l_rent_rev
            WHERE pmrr_prop = ?
            AND pmrr_lease = ?
            AND pmrr_closed = 0
            ORDER BY pmrr_rev_dt ASC
            ';
    $nextRentReviewDate = $dbh->executeSingle($rentReviewDate, [$propertyID, $leaseID]);

    if (count($nextRentReviewDate ?? []) > 0) {
        $replacements = $replacements + $contactSalutationLease + $nextRentReviewDate;
    } else {
        $replacements = $replacements + $contactSalutationLease + ['pmrr_rev_dt' => ''];
    }

    $replacements += budgetReplacementExpenses($propertyID, $leaseID, $budgetYear, $budget_type);

    return $replacements + dbGetPlaceholderMailingAddress($propertyID, $leaseID);
}

function budgetReplacementExpenses($propertyID, $leaseID, $budgetYear, $budget_type)
{
    global $dbh, $clientDB;
    $dbh->selectDatabase($clientDB);
    $params = [];
    $replacements = [];
    $sql = '
        SELECT
            CONVERT(varchar,CAST(ROUND(SUM(custom_value),2) AS money), 1) AS newVO ,
            CONVERT(varchar,CAST(SUM(CONVERT(DECIMAL(10,2),ROUND((custom_value / unit_area),2))) AS money), 1) AS newVOSQM ,
            CONVERT(varchar,CAST(SUM(CONVERT(DECIMAL(10,2),ROUND((custom_value),2))) / 12 AS money), 1) AS newVOpm
        FROM property_budget_expense_breakdown
        INNER JOIN pmca_chart
        ON pmca_code = account_code
        WHERE property_code = ' . addSQLParam($params, $propertyID) . '
        AND lease_code = ' . addSQLParam($params, $leaseID) . '
        AND financial_year = ' . addSQLParam($params, $budgetYear) . '
        AND input_type = ' . addSQLParam($params, $budget_type) . '
        AND account_code IN
        (SELECT
        pmep_exp_acc
        FROM pmep_b_exp_per
        WHERE [pmep_b_exp_per].[pmep_prop] = ' . addSQLParam($params, $propertyID) . '
        AND [pmep_b_exp_per].[pmep_year] = ' . addSQLParam($params, $budgetYear) . "
        )
        AND pmca_gl_account_group2 = 'EXP.OUT'
        AND status = 1
    ";
    $prop_lease_rep = $dbh->executeSingle($sql, $params);
    if (count($prop_lease_rep ?? []) > 0) {
        $newVOT = $prop_lease_rep['newVO'];
        $newVOSQM = $prop_lease_rep['newVOSQM'];
        $replacements += $prop_lease_rep;
    } else {
        $newVOT = 0;
        $newVOSQM = 0;
        $replacements += ['newVO' => 0, 'newVOSQM' => 0, 'newVOpm' => 0];
    }


    $sql = "
        SELECT
            CONVERT(varchar,CAST(CONVERT(DECIMAL(10,2),ROUND((SELECT TOP 1 pmla_amt FROM pmla_l_c_amt WHERE pmla_prop = pmlc_prop AND pmla_lease = pmlc_lease AND pmla_unit = pmlc_unit AND pmla_serial=pmlc_serial AND (CONVERT(datetime, GETDATE(), 103) BETWEEN pmla_start_dt AND pmla_end_dt) ORDER BY pmla_end_dt DESC),2)) AS money), 1) AS currentVO,
            CONVERT(varchar,CAST(CONVERT(DECIMAL(10,2),ROUND((SELECT TOP 1 pmla_amt FROM pmla_l_c_amt WHERE pmla_prop = pmlc_prop AND pmla_lease = pmlc_lease AND pmla_unit = pmlc_unit AND pmla_serial=pmlc_serial AND (CONVERT(datetime, GETDATE(), 103) BETWEEN pmla_start_dt AND pmla_end_dt) ORDER BY pmla_end_dt DESC) / CONVERT(DECIMAL(10,2),pmua_area),2)) AS money), 1) AS currentVOSQM,
            CONVERT(varchar,CAST(CONVERT(DECIMAL(10,2),ROUND((SELECT TOP 1 pmla_amt FROM pmla_l_c_amt WHERE pmla_prop = pmlc_prop AND pmla_lease = pmlc_lease AND pmla_unit = pmlc_unit AND pmla_serial=pmlc_serial AND (CONVERT(datetime, GETDATE(), 103) BETWEEN pmla_start_dt AND pmla_end_dt) ORDER BY pmla_end_dt DESC) / 12,2)) AS money), 1) AS currentVOpm
        FROM
            pmlc_l_charge
            inner join [pmca_chart]
            on pmlc_acc = [pmca_chart].[pmca_code]
            INNER JOIN pmua_unit_area ON pmua_lease = pmlc_lease
            AND (CONVERT(datetime, CONVERT(varchar, GETDATE(), 103), 103) BETWEEN pmua_from_dt AND pmua_to_dt)
        WHERE
            pmlc_prop = ?
            AND pmlc_lease = ?
            AND pmca_gl_account_group2 = 'INC.OUT'

    ";
    $prop_lease_rep = $dbh->executeSingle($sql, [$propertyID, $leaseID]);

    if (count($prop_lease_rep ?? []) > 0) {
        $currentVOT = $prop_lease_rep['currentVO'];
        $replacements += $prop_lease_rep;
        $newVOT = str_replace(',', '', $newVOT);
        $currentVOT = str_replace(',', '', $currentVOT);
    } else {
        $currentVOT = 0;
        $replacements += ['currentVO' => 0, 'currentVOSQM' => 0, 'currentVOpm' => 0];
        $newVOT = str_replace(',', '', $newVOT);
        $currentVOT = str_replace(',', '', $currentVOT);
    }


    if ($newVOT != '' && $currentVOT != '' && $newVOT > 0) {
        $sql = "
            SELECT CONVERT(DECIMAL(10,2),ROUND((({$newVOT} - {$currentVOT}) / {$newVOT}) * 100,2)) AS changeVOpercent
        ";
        $prop_lease_rep = $dbh->executeSingle($sql);
        $replacements += $prop_lease_rep;
    } else {
        $replacements += ['changeVOpercent' => 0];
    }


    $params = [];
    $sql = '
        SELECT DISTINCT
            CONVERT(varchar,CAST(ROUND(SUM(custom_value),2) AS money), 1) AS newDR ,
            CONVERT(varchar,CAST(CONVERT(DECIMAL(10,2),ROUND(SUM(custom_value / unit_area),2)) AS money), 1) AS newDRSQM
        FROM property_budget_expense_breakdown
        INNER JOIN pmca_chart
        ON pmca_code = account_code
        WHERE property_code = ' . addSQLParam($params, $propertyID) . '
        AND lease_code = ' . addSQLParam($params, $leaseID) . '
        AND financial_year = ' . addSQLParam($params, $budgetYear) . '
        AND input_type = ' . addSQLParam($params, $budget_type) . '
        AND account_code IN
        (SELECT
        pmep_exp_acc
        FROM pmep_b_exp_per
        INNER JOIN pmca_chart
        ON pmca_code = pmep_exp_acc
        WHERE [pmep_b_exp_per].[pmep_prop] = ' . addSQLParam($params, $propertyID) . '
        AND [pmep_b_exp_per].[pmep_year] = ' . addSQLParam($params, $budgetYear) . "
        AND pmca_gl_account_group2 = 'EXP.REC'
        )
        AND pmca_gl_account_group2 = 'EXP.REC'
        AND status = 1
    ";
    $prop_lease_rep = $dbh->executeSingle($sql, $params);
    if (count($prop_lease_rep ?? []) > 0) {
        $newDRT = $prop_lease_rep['newDR'];
        $newDRSQM = $prop_lease_rep['newDRSQM'];
        $replacements += $prop_lease_rep;
        $newDRT = str_replace(',', '', $newDRT);
        $newDRSQM = str_replace(',', '', $newDRSQM);
    } else {
        $newDRT = 0;
        $newDRSQM = 0;
        $replacements += ['newDR' => 0, 'newDRSQM' => 0];
    }

    $newTotal = $newVOT + $newDRT;
    $newTotalSQM = $newVOSQM + $newDRSQM;
    $newTotal = number_format($newTotal, 2, '.', ',');
    $newTotalSQM = number_format($newTotalSQM, 2, '.', ',');

    return $replacements + [
        'newTotal' => $newTotal,
        'newTotalSQM' => $newTotalSQM,
    ];
}

function budgetReplacementExpensesByProperty($propertyID, $budgetYear, $budget_type)
{
    global $dbh, $clientDB;
    $dbh->selectDatabase($clientDB);
    $params = [];
    $replacements = [];
    $sql = '
        SELECT
            CONVERT(varchar,CAST(ROUND(SUM(custom_value),2) AS money), 1) AS newVO ,
            CONVERT(varchar,CAST(SUM(CONVERT(DECIMAL(10,2),ROUND((custom_value / unit_area),2))) AS money), 1) AS newVOSQM ,
            CONVERT(varchar,CAST(SUM(CONVERT(DECIMAL(10,2),ROUND((custom_value),2))) / 12 AS money), 1) AS newVOpm
        FROM property_budget_expense_breakdown
        INNER JOIN pmca_chart
        ON pmca_code = account_code
        WHERE property_code = ' . addSQLParam($params, $propertyID) . '
        AND financial_year = ' . addSQLParam($params, $budgetYear) . '
        AND input_type = ' . addSQLParam($params, $budget_type) . '
        AND account_code IN
        (SELECT
        pmep_exp_acc
        FROM pmep_b_exp_per
        WHERE [pmep_b_exp_per].[pmep_prop] = ' . addSQLParam($params, $propertyID) . '
        AND [pmep_b_exp_per].[pmep_year] = ' . addSQLParam($params, $budgetYear) . "
        )
        AND pmca_gl_account_group2 = 'EXP.OUT'
        AND status = 1
    ";
    $prop_lease_rep = $dbh->executeSingle($sql, $params);
    if (count($prop_lease_rep ?? []) > 0) {
        $newVOT = $prop_lease_rep['newVO'];
        $newVOSQM = $prop_lease_rep['newVOSQM'];
        $replacements += $prop_lease_rep;
    } else {
        $newVOT = 0;
        $newVOSQM = 0;
        $replacements += ['newVO' => 0, 'newVOSQM' => 0, 'newVOpm' => 0];
    }


    $sql = "
        SELECT
            CONVERT(varchar,CAST(CONVERT(DECIMAL(10,2),ROUND((SELECT TOP 1 pmla_amt FROM pmla_l_c_amt WHERE pmla_prop = pmlc_prop AND pmla_lease = pmlc_lease AND pmla_unit = pmlc_unit AND pmla_serial=pmlc_serial AND (CONVERT(datetime, GETDATE(), 103) BETWEEN pmla_start_dt AND pmla_end_dt) ORDER BY pmla_end_dt DESC),2)) AS money), 1) AS currentVO,
            CONVERT(varchar,CAST(CONVERT(DECIMAL(10,2),ROUND((SELECT TOP 1 pmla_amt FROM pmla_l_c_amt WHERE pmla_prop = pmlc_prop AND pmla_lease = pmlc_lease AND pmla_unit = pmlc_unit AND pmla_serial=pmlc_serial AND (CONVERT(datetime, GETDATE(), 103) BETWEEN pmla_start_dt AND pmla_end_dt) ORDER BY pmla_end_dt DESC) / CONVERT(DECIMAL(10,2),pmua_area),2)) AS money), 1) AS currentVOSQM,
            CONVERT(varchar,CAST(CONVERT(DECIMAL(10,2),ROUND((SELECT TOP 1 pmla_amt FROM pmla_l_c_amt WHERE pmla_prop = pmlc_prop AND pmla_lease = pmlc_lease AND pmla_unit = pmlc_unit AND pmla_serial=pmlc_serial AND (CONVERT(datetime, GETDATE(), 103) BETWEEN pmla_start_dt AND pmla_end_dt) ORDER BY pmla_end_dt DESC) / 12,2)) AS money), 1) AS currentVOpm
        FROM
            pmlc_l_charge
            inner join [pmca_chart]
            on pmlc_acc = [pmca_chart].[pmca_code]
            INNER JOIN pmua_unit_area ON pmua_lease = pmlc_lease
            AND (CONVERT(datetime, CONVERT(varchar, GETDATE(), 103), 103) BETWEEN pmua_from_dt AND pmua_to_dt)
        WHERE
            pmlc_prop = ?
            AND pmca_gl_account_group2 = 'INC.OUT'

    ";
    $prop_lease_rep = $dbh->executeSingle($sql, [$propertyID]);

    if (count($prop_lease_rep ?? []) > 0) {
        $currentVOT = $prop_lease_rep['currentVO'];
        $replacements += $prop_lease_rep;
        $newVOT = str_replace(',', '', $newVOT);
        $currentVOT = str_replace(',', '', $currentVOT);
    } else {
        $currentVOT = 0;
        $replacements += ['currentVO' => 0, 'currentVOSQM' => 0, 'currentVOpm' => 0];
        $newVOT = str_replace(',', '', $newVOT);
        $currentVOT = str_replace(',', '', $currentVOT);
    }


    if ($newVOT != '' && $currentVOT != '' && $newVOT > 0) {
        $sql = "
            SELECT CONVERT(DECIMAL(10,2),ROUND((({$newVOT} - {$currentVOT}) / {$newVOT}) * 100,2)) AS changeVOpercent
        ";
        $prop_lease_rep = $dbh->executeSingle($sql);
        $replacements += $prop_lease_rep;
    } else {
        $replacements += ['changeVOpercent' => 0];
    }


    $params = [];
    $sql = '
        SELECT DISTINCT
            CONVERT(varchar,CAST(ROUND(SUM(custom_value),2) AS money), 1) AS newDR ,
            CONVERT(varchar,CAST(CONVERT(DECIMAL(10,2),ROUND(SUM(custom_value / unit_area),2)) AS money), 1) AS newDRSQM
        FROM property_budget_expense_breakdown
        INNER JOIN pmca_chart
        ON pmca_code = account_code
        WHERE property_code = ' . addSQLParam($params, $propertyID) . '
        AND financial_year = ' . addSQLParam($params, $budgetYear) . '
        AND input_type = ' . addSQLParam($params, $budget_type) . '
        AND account_code IN
        (SELECT
        pmep_exp_acc
        FROM pmep_b_exp_per
        INNER JOIN pmca_chart
        ON pmca_code = pmep_exp_acc
        WHERE [pmep_b_exp_per].[pmep_prop] = ' . addSQLParam($params, $propertyID) . '
        AND [pmep_b_exp_per].[pmep_year] = ' . addSQLParam($params, $budgetYear) . "
        AND pmca_gl_account_group2 = 'EXP.REC'
        )
        AND pmca_gl_account_group2 = 'EXP.REC'
        AND status = 1
    ";
    $prop_lease_rep = $dbh->executeSingle($sql, $params);
    if (count($prop_lease_rep ?? []) > 0) {
        $newDRT = $prop_lease_rep['newDR'];
        $newDRSQM = $prop_lease_rep['newDRSQM'];
        $replacements += $prop_lease_rep;
        $newDRT = str_replace(',', '', $newDRT);
        $newDRSQM = str_replace(',', '', $newDRSQM);
    } else {
        $newDRT = 0;
        $newDRSQM = 0;
        $replacements += ['newDR' => 0, 'newDRSQM' => 0];
    }

    $newTotal = $newVOT + $newDRT;
    $newTotalSQM = $newVOSQM + $newDRSQM;
    $newTotal = number_format($newTotal, 2, '.', ',');
    $newTotalSQM = number_format($newTotalSQM, 2, '.', ',');

    return $replacements + [
        'newTotal' => $newTotal,
        'newTotalSQM' => $newTotalSQM,
    ];
}


function mergeLetterPDF($pdffiles, $goDL = false, $additionalNaming = '')
{
    global $pathPrefix, $clientDirectory;

    $searchpath = "{$pathPrefix}{$clientDirectory}/letters/";
    $filename = 'multiple_letter_' . time() . $additionalNaming . '_' . $_SESSION['user_id'] . '.pdf';
    $outfilename = "{$pathPrefix}{$clientDirectory}/letters/{$filename}";
    $downloadPath = "{$clientDirectory}/letters/{$filename}";
    try {
        $p = new PDFlibExt();


        // This means we must check return values of load_font() etc.
        // $p->set_option("errorpolicy=return");
        // $p->set_option("stringformat=utf8");

        $p->set_option('license=' . PDFLIB_LICENSE);
        $p->set_option('stringformat=utf8');
        $p->set_option('Searchpath={' . $searchpath . '}');

        if ($p->begin_document($outfilename, '') == 0) {
            exit('Error: ' . $p->get_errmsg());
        }

        $p->set_info('Creator', 'cirrus8');
        $p->set_info('Title', $filename);

        foreach ($pdffiles as $pdffile) {
            /* Open the input PDF */
            $indoc = $p->open_pdi_document($pdffile, '');
            if ($indoc == 0) {
                printf("Error: %s\n", $p->get_errmsg());

                continue;
            }

            $endpage = $p->pcos_get_number($indoc, 'length:pages');

            /* Loop over all pages of the input document */
            for ($pageno = 1; $pageno <= $endpage; $pageno++) {
                $page = $p->open_pdi_page($indoc, $pageno, '');

                if ($page == 0) {
                    printf("Error: %s\n", $p->get_errmsg());

                    continue;
                }

                /* Dummy $page size; will be adjusted later */
                $p->begin_page_ext(10, 10, '');

                /* Create a bookmark with the file name */
                if ($pageno == 1) {
                    $p->create_bookmark($pdffile, '');
                }

                /* Place the imported $page on the output $page, and
                 * adjust the $page size
                 */
                $p->fit_pdi_page($page, 0, 0, 'adjustpage');
                $p->close_pdi_page($page);

                $p->end_page_ext('');
            }

            $p->close_pdi_document($indoc);
        }

        $p->end_document('');
    } catch (PDFlibException $e) {
        exit(
            "PDFlib exception occurred in starter_pdfmerge sample:\n" .
            '[' . $e->get_errnum() . '] ' . $e->get_apiname() . ': ' .
            $e->get_errmsg() . "\n"
        );
    } catch (Exception $e) {
        exit($e);
    }

    // ***********************************************************

    if ($goDL) {
        if (isset($view->items['from_api'])) {
            echo json_encode(
                "document.location.href='download.php?fileID=" . encodeParameter(
                    $downloadPath
                ) . "&fileName=multiple_letters.pdf'"
            );
            exit;
        } else {
            echo "<script> document.location.href='download.php?fileID=" . encodeParameter(
                $downloadPath
            ) . "&fileName=multiple_letters.pdf';</script>";
        }
    } else {
        return [$filename];
    }


}

function dbGetOwnerPlaceholderReplacements($ownerID, $propertyID, $plainEmail = false)
{
    $replacements = [
        date('j'),
        date('n'),
        date('F'),
        date('Y'),
        strtoupper(date('F', strtotime('last day of previous month'))),
        date('Y', strtotime('last day of previous month')),
    ];

    $property_rep = dbGetOwnerPropertyReplacements($ownerID, $propertyID, $plainEmail);

    $getSalutation = dbGetCompanyContactSalutation($ownerID);

    $salutation = ['salutation' => $getSalutation];


    // get last month's owner remittance
    $remittanceOwnerLastMonth = toMoney(
        dbGetPastOwnerPaymentsByOwner(
            $propertyID,
            $ownerID,
            date('d-m-Y', strtotime('first day of previous month')),
            date('d-m-Y', strtotime('last day of previous month'))
        ) + 0
    );
    $ownerRemittanceLastMonth = ['ownerLastRemit' => $remittanceOwnerLastMonth];

    // get total owner remittance for the last 3 months
    $remittanceOwnerLastQuarter = toMoney(dbGetOwnerRemittanceLast3Months($propertyID, $ownerID) + 0);
    $ownerRemittanceLast3Months = ['ownerLast3MonthsRemit' => $remittanceOwnerLastQuarter];

    // get total remittance for last month
    $totalRemittanceForPreviousMonth = toMoney(
        dbGetTotalRemittanceForPreviousMonth(
            $propertyID,
            $ownerID,
            date('d-m-Y', strtotime('first day of previous month')),
            date('d-m-Y', strtotime('last day of previous month'))
        ) + 0
    );
    $totalRemittancePreviousMonth = ['totalRemittanceForLastMonth' => $totalRemittanceForPreviousMonth];

    // get total remittance for last 3 months
    $totalRemittanceForLastQuarter = toMoney(dbGetTotalRemittanceForLast3Months($propertyID, $ownerID) + 0);
    $totalRemittanceLast3Months = ['totalRemittanceForLast3Months' => $totalRemittanceForLastQuarter];

    $propertyContactSalutation = [
        'propertyContactSalutation' => dbGetPropertyContactSalutationPrimary(
            $propertyID
        ),
    ];
    //    if($searchMethod == 'Owner Budget') $replacements = $replacements + budgetReplacementExpensesByProperty($propertyID, $budgetYear, $budget_type);

    return $replacements + $property_rep + $salutation + $totalRemittancePreviousMonth + $ownerRemittanceLastMonth + $ownerRemittanceLast3Months + $totalRemittanceLast3Months + $propertyContactSalutation;
}


function createLetterPDF(
    $title1,
    $title2,
    $content,
    $placeholders,
    $replacements,
    $useLetterhead = true,
    $fileName = null
) {
    global $pathPrefix, $clientDirectory;

    if ($fileName) {

        $time = time() + rand();
        $file = '../reports/' . $fileName;
        $newFileName = "letters_{$time}" . $_SESSION['user_id'];
        $move_file = "../reports/{$clientDirectory}/letters/" . $newFileName;
        copy($file, $move_file . '.docx');

        $replacer = [];
        $x = 0;
        foreach ($replacements as $val) {
            if (str_replace('%', '', $placeholders[$x])) {
                $replacer[str_replace('%', '', $placeholders[$x])] = $val;
                $x++;
            }
        }

        $docx = new CreateDocxFromTemplate($move_file . '.docx');
        $docx->setTemplateSymbol('%');


        $docx->replaceVariableByText($replacer);
        $docx->modifyPageLayout('A4');
        $docx->createDocx($move_file);

        $docx = new CreateDocx();
        $docx->transformDocument(
            $move_file . '.docx',
            $move_file . '.pdf',
            'libreoffice',
            ['outdir' => 'C:\libreoffice\temp']
        );
        //       $transform = new TransformDocAdvPDF($move_file . ".docx");
        //     $transform->transform($move_file . ".pdf");

        while (! is_file($move_file . '.pdf')) {
            $docx = new CreateDocx();
            $docx->transformDocument(
                $move_file . '.docx',
                $move_file . '.pdf',
                'libreoffice',
                ['outdir' => 'C:\libreoffice\temp']
            );
        }

        return "{$clientDirectory}/letters/{$newFileName}.pdf";

    }

    $title1Holder = explode(',', $title1);
    $title2Holder = explode(',', $title2);

    if (count($title1Holder ?? []) > 1) {
        $title1 = 'Multiple_Owner';
    }

    if (count($title2Holder ?? []) > 1) {
        $title2 = 'Multiple_Property';
    }

    $filename = $title1 . '-' . $title2 . '_' . 'pdf_' . time() . '_' . $_SESSION['user_id'] . '.pdf';
    $letter_pdf_filePath = "{$pathPrefix}{$clientDirectory}/letters/{$filename}";
    $letter_pdf_dlPath = "{$clientDirectory}/letters/{$filename}";

    if (! file_exists("{$pathPrefix}{$clientDirectory}/letters")) {
        mkdir("{$pathPrefix}{$clientDirectory}/letters", FILE_PERMISSION, true);
    }

    [$text, $temp_img_paths] = replacePlaceholdersAndImages($content, $placeholders, $replacements);

    $letter = new HTMLPage2($letter_pdf_filePath, PDF_PAGE_ORIENTATION, PDF_UNIT, PDF_PAGE_FORMAT, $useLetterhead);
    $letter->SetFont('helvetica', '', 8, '', true);
    $letter->setImageScale(1.53);

    $letter->render($text);

    foreach ($temp_img_paths as $path) {
        unlink($path);
    }

    return $letter_pdf_dlPath;
}

function createLetterPDFBudget(
    $title1,
    $title2,
    $content,
    $content2,
    $placeholders,
    $replacements,
    $useLetterhead = true,
    $fileName = null
) {
    global $pathPrefix, $clientDirectory;

    if ($fileName) {

        $time = time() + rand();
        $file = '../reports/' . $fileName;
        $newFileName = "letters_{$time}" . $_SESSION['user_id'];
        $move_file = "../reports/{$clientDirectory}/letters/" . $newFileName;
        copy($file, $move_file . '.docx');

        $replacer = [];
        $x = 0;

        foreach ($replacements as $val) {
            if (str_replace('%', '', $placeholders[$x])) {
                $replacer[str_replace('%', '', $placeholders[$x])] = $val;
                $x++;
            }
        }

        $docx = new CreateDocxFromTemplate($move_file . '.docx');
        $docx->setTemplateSymbol('%');

        $docx->replaceVariableByText($replacer);
        $docx->modifyPageLayout('A4');
        $docx->createDocx($move_file);

        $docx = new CreateDocx();
        $docx->transformDocument(
            $move_file . '.docx',
            $move_file . '.pdf',
            'libreoffice',
            ['outdir' => 'C:\libreoffice\temp']
        );
        //       $transform = new TransformDocAdvPDF($move_file . ".docx");
        //     $transform->transform($move_file . ".pdf");

        while (! is_file($move_file . '.pdf')) {
            $docx = new CreateDocx();
            $docx->transformDocument(
                $move_file . '.docx',
                $move_file . '.pdf',
                'libreoffice',
                ['outdir' => 'C:\libreoffice\temp']
            );
        }

        $filename = $title1 . '-' . $title2 . '_' . 'pdf_' . time() . '_' . $_SESSION['user_id'] . '.pdf';
        $letter_pdf_filePath = "{$pathPrefix}{$clientDirectory}/letters/{$filename}";
        $letter_pdf_dlPath = "{$clientDirectory}/letters/{$filename}";
        $letter = new HTMLPage2($letter_pdf_filePath, PDF_PAGE_ORIENTATION, PDF_UNIT, PDF_PAGE_FORMAT, $useLetterhead);
        $letter->SetFont('helvetica', '', 8, '', true);
        $letter->setImageScale(1.53);

        $letter->render($content2);

        $budgetLetter = mergeLetterPDF([$newFileName . '.pdf', $filename], false, time());

        unlink($move_file . '.docx');

        return "{$clientDirectory}/letters/" . $budgetLetter[0];

    }

    $filename = $title1 . '-' . $title2 . '_' . 'pdf_' . time() . '_' . $_SESSION['user_id'] . '.pdf';
    $letter_pdf_filePath = "{$pathPrefix}{$clientDirectory}/letters/{$filename}";
    $letter_pdf_dlPath = "{$clientDirectory}/letters/{$filename}";

    if (! file_exists("{$pathPrefix}{$clientDirectory}/letters")) {
        mkdir("{$pathPrefix}{$clientDirectory}/letters", FILE_PERMISSION, true);
    }

    [$text, $temp_img_paths] = replacePlaceholdersAndImages($content, $placeholders, $replacements);
    $letter = new HTMLPage2($letter_pdf_filePath, PDF_PAGE_ORIENTATION, PDF_UNIT, PDF_PAGE_FORMAT, $useLetterhead);
    $letter->SetFont('helvetica', '', 8, '', true);
    $letter->setImageScale(1.53);

    $space_break_after_header = dbGetParam('LETTER', 'HEADSPACE'); // default 5
    $auto_page_break_spaces = $space_break_after_header * 5;
    $letter->renderBudget($text, $content2, $auto_page_break_spaces);

    //    $letter->setImageScale(1.53);
    //    $letter->render("<p>hahaha</p>");
    //
    foreach ($temp_img_paths as $path) {
        unlink($path);
    }

    return $letter_pdf_dlPath;
}

function renderSecondPageContent(
    $propertyID,
    $leaseID,
    $year,
    $budget_type,
    $tenant_detail,
    $budget_type_name,
    $show_annual_property_budget
) {
    $leaseVOData = dbGetExpLeaseBreakdownVO($propertyID, $leaseID, $year, $budget_type);
    $propertyLookUp = dbGetPropertyLookup($propertyID);
    $propertyName = $propertyLookUp['propertyName'];

    $leaseLookUp = dbGetLeaseDetails($propertyID, $leaseID);
    $leaseName = $leaseLookUp['leaseName'];

    $voContent = '';
    $overAllTotalForProperty = 0;
    $totalYear = 0;
    $totalMonth = 0;
    $overAllRatePerSQM = 0;

    $table_colspan = '4';
    if ($show_annual_property_budget == 'Yes') {
        $table_colspan = '5';
    }

    switch ($tenant_detail) {
        case 'show_both':
            $table_colspan = '5';
            if ($show_annual_property_budget == 'Yes') {
                $table_colspan = '6';
            }

            break;
        case 'none':
            $table_colspan = '3';
            if ($show_annual_property_budget == 'Yes') {
                $table_colspan = '4';
            }

            break;
    }

    foreach ($leaseVOData as $dataLeaseVal) {
        $unitCode = $dataLeaseVal['unit_code'];
        $unitName = $dataLeaseVal['unit_name'];
        $unitArea = $dataLeaseVal['unit_area'];
        if ($unitArea == 0) {
            $unitArea = 0.00;
        }

        $accountName = $dataLeaseVal['pmca_name'];
        $yearValue = round($dataLeaseVal['custom_value'], 2);
        $montlyValue = round($dataLeaseVal['custom_value'] / 12, 2);
        $totalForProperty = dbGetExpLeaseBreakdownSumAccount(
            $propertyID,
            $dataLeaseVal['account_code'],
            $year,
            $budget_type
        );
        $ratePerSQM = $unitArea <= 0 ? $yearValue : $yearValue / $unitArea;


        $tenant_percenatage_divisor = $totalForProperty;
        if ($tenant_percenatage_divisor <= 0) {
            $tenant_percenatage_divisor = 1;
        }

        $tenant_percenatage = ($yearValue / $tenant_percenatage_divisor) * 100;
        $show_annual_property_budget_row_data = '';
        if ($show_annual_property_budget == 'Yes') {
            $show_annual_property_budget_row_data = '<td style="text-align:right" align="right">' . number_format(
                $totalForProperty,
                2
            ) . '</td>';
        }

        switch ($tenant_detail) {
            case 'show_sqm':
                $voContent = $voContent . '
                    <tr>
                        <td>' . $accountName . '</td>
                        ' . $show_annual_property_budget_row_data . '
                        <td style="text-align:right" align="right">' . number_format($yearValue, 2) . '</td>
                        <td style="text-align:right" align="right">' . number_format($montlyValue, 2) . '</td>
                        <td style="text-align:right" align="right">' . number_format($ratePerSQM, 2) . '</td>
                    </tr>
                ';
                break;
            case 'show_percentage':
                $voContent = $voContent . '
                    <tr>
                        <td>' . $accountName . '</td>
                        ' . $show_annual_property_budget_row_data . '
                        <td style="text-align:right" align="right">' . number_format($yearValue, 2) . '</td>
                        <td style="text-align:right" align="right">' . number_format($montlyValue, 2) . '</td>
                        <td style="text-align:right" align="right">' . number_format($tenant_percenatage, 2) . '</td>
                    </tr>
                ';
                break;
            case 'show_both':
                $voContent = $voContent . '
                    <tr>
                        <td>' . $accountName . '</td>
                        ' . $show_annual_property_budget_row_data . '
                        <td style="text-align:right" align="right">' . number_format($yearValue, 2) . '</td>
                        <td style="text-align:right" align="right">' . number_format($montlyValue, 2) . '</td>
                        <td style="text-align:right" align="right">' . number_format($ratePerSQM, 2) . '</td>
                        <td style="text-align:right" align="right">' . number_format($tenant_percenatage, 2) . '</td>
                    </tr>
                ';
                break;
            case 'none':
                $voContent = $voContent . '
                    <tr>
                        <td>' . $accountName . '</td>
                        ' . $show_annual_property_budget_row_data . '
                        <td style="text-align:right" align="right">' . number_format($yearValue, 2) . '</td>
                        <td style="text-align:right" align="right">' . number_format($montlyValue, 2) . '</td>
                    </tr>
                ';
                break;
        }

        $totalYear += $yearValue;
        $totalMonth += $montlyValue;
        $overAllTotalForProperty += $totalForProperty;
        $overAllRatePerSQM += $ratePerSQM;
    }

    $leaseVOData = dbGetExpLeaseBreakdownREC($propertyID, $leaseID, $year, $budget_type);
    $recContent = '';
    $totalYearRec = 0;
    $totalMonthRec = 0;
    $overAllTotalForPropertyRec = 0;
    $overAllRatePerSQMRec = 0;
    foreach ($leaseVOData as $dataLeaseVal) {
        $unitCode = $dataLeaseVal['unit_code'];
        $unitName = $dataLeaseVal['unit_name'];
        $unitArea = $dataLeaseVal['unit_area'];
        if ($unitArea == 0) {
            $unitArea = 0.00;
        }

        $accountName = $dataLeaseVal['pmca_name'];
        $yearValue = round($dataLeaseVal['custom_value'], 2);
        $montlyValue = round($dataLeaseVal['custom_value'] / 12, 2);
        $totalForProperty = dbGetExpLeaseBreakdownSumAccount(
            $propertyID,
            $dataLeaseVal['account_code'],
            $year,
            $budget_type
        );
        $ratePerSQM = $unitArea <= 0 ? $yearValue : $yearValue / $unitArea;


        $tenant_percenatage_divisor = $totalForProperty;
        if ($tenant_percenatage_divisor <= 0) {
            $tenant_percenatage_divisor = 1;
        }

        $tenant_percenatage = ($yearValue / $tenant_percenatage_divisor) * 100;
        $show_annual_property_budget_row_data = '';
        if ($show_annual_property_budget == 'Yes') {
            $show_annual_property_budget_row_data = '<td style="text-align:right" align="right">' . number_format(
                $totalForProperty,
                2
            ) . '</td>';
        }

        switch ($tenant_detail) {
            case 'show_sqm':
                $recContent = $recContent . '
                    <tr>
                        <td>' . $accountName . '</td>
                        ' . $show_annual_property_budget_row_data . '
                        <td style="text-align:right" align="right">' . number_format($yearValue, 2) . '</td>
                        <td style="text-align:right" align="right"></td>
                        <td style="text-align:right" align="right">' . number_format($ratePerSQM, 2) . '</td>
                    </tr>
                ';
                break;
            case 'show_percentage':
                $recContent = $recContent . '
                    <tr>
                        <td>' . $accountName . '</td>
                        ' . $show_annual_property_budget_row_data . '
                        <td style="text-align:right" align="right">' . number_format($yearValue, 2) . '</td>
                        <td style="text-align:right" align="right"></td>
                        <td style="text-align:right" align="right">' . number_format($tenant_percenatage, 2) . '</td>
                    </tr>
                ';
                break;
            case 'show_both':
                $recContent = $recContent . '
                    <tr>
                        <td>' . $accountName . '</td>
                        ' . $show_annual_property_budget_row_data . '
                        <td style="text-align:right" align="right">' . number_format($yearValue, 2) . '</td>
                        <td style="text-align:right" align="right"></td>
                        <td style="text-align:right" align="right">' . number_format($ratePerSQM, 2) . '</td>
                        <td style="text-align:right" align="right">' . number_format($tenant_percenatage, 2) . '</td>
                    </tr>
                ';
                break;
            case 'none':
                $recContent = $recContent . '
                    <tr>
                        <td>' . $accountName . '</td>
                        ' . $show_annual_property_budget_row_data . '
                        <td style="text-align:right" align="right">' . number_format($yearValue, 2) . '</td>
                        <td style="text-align:right" align="right"></td>
                    </tr>
                ';
                break;
        }


        $totalYearRec += $yearValue;
        $totalMonthRec += $montlyValue;
        $overAllTotalForPropertyRec += $totalForProperty;
        $overAllRatePerSQMRec += $ratePerSQM;
    }

    $yearRange = ($year - 1) . '/' . substr($year, 2, 2);
    $break_lines_html = '';
    //    for($x = 1; $x <= $space_break_after_header; $x++) {
    //        $break_lines_html .= '<p style="margin-left:0in; margin-right:0in">&nbsp;</p>';
    //    }
    $content2 = '
        ' . $break_lines_html . '
        <table cellpadding="1" cellspacing="1" style="width:500px">
            <tbody>
            <tr><td colspan="3">Property: ' . $propertyName . ' (' . $propertyID . ')</td></tr>
            <tr><td colspan="3">Lease: ' . $leaseName . ' (' . $leaseID . ')</td></tr>
            <tr><td colspan="3">Unit: ' . $unitName . ' (' . $unitCode . ')</td></tr>
            <tr><td colspan="3">Area: ' . $unitArea . ' ' . $_SESSION['country_default']['area_unit_string'] . '</td></tr>
            <tr><td colspan="3">Year: ' . $yearRange . '</td></tr>
            <tr><td colspan="3">Basis: ' . $budget_type_name . '</td></tr>
            <tr><td colspan="3"></td></tr>
            </tbody>
        </table>
    ';

    if ($voContent !== '') {
        $table_header = '';
        $table_subtotal = '';
        $tenant_percenatage_divisor = $overAllTotalForProperty;
        if ($tenant_percenatage_divisor <= 0) {
            $tenant_percenatage_divisor = 1;
        }

        $tenant_percenatage = ($totalYear / $tenant_percenatage_divisor) * 100;
        $show_annual_property_budget_row_data_label = '';
        $show_annual_property_budget_row_data_total = '';
        if ($show_annual_property_budget == 'Yes') {
            $show_annual_property_budget_row_data_label = '<td style="text-align: right" align="right"><strong>Annual Property <br/>Budget (pa)</strong></td>';
            $show_annual_property_budget_row_data_total = '<td style="text-align: right" align="right"><strong>' . number_format(
                $overAllTotalForProperty,
                2
            ) . '</strong></td>';
        }

        switch ($tenant_detail) {
            case 'show_sqm':
                $table_header = '
                    <td><strong>Account Name</strong></td>
                    ' . $show_annual_property_budget_row_data_label . '
                    <td style="text-align: right" align="right"><strong>Tenant Portion (pa)</strong></td>
                    <td style="text-align: right" align="right"><strong>Tenant Portion (pcm)</strong></td>
                    <td style="text-align: right" align="right"><strong>Tenant Rate <br/>(per ' . $_SESSION['country_default']['area_unit_string'] . ')</strong></td>
                ';
                $table_subtotal = '
                    <td style="text-align: right"><strong>SUB TOTAL</strong></td>
                    ' . $show_annual_property_budget_row_data_total . '
                    <td style="text-align: right" align="right"><strong>' . number_format($totalYear, 2) . '</strong></td>
                    <td style="text-align: right" align="right"><strong>' . number_format(
                    round($totalYear / 12, 2),
                    2
                ) . '</strong></td>
                    <td style="text-align: right" align="right"><strong>' . number_format($overAllRatePerSQM, 2) . '</strong></td>
                ';
                break;
            case 'show_percentage':
                $table_header = '
                    <td><strong>Account Name</strong></td>
                    ' . $show_annual_property_budget_row_data_label . '
                    <td style="text-align: right" align="right"><strong>Tenant Portion (pa)</strong></td>
                    <td style="text-align: right" align="right"><strong>Tenant Portion (pcm)</strong></td>
                    <td style="text-align: right" align="right"><strong>Tenant %</strong></td>
                ';
                $table_subtotal = '
                    <td style="text-align: right"><strong>SUB TOTAL</strong></td>
                    ' . $show_annual_property_budget_row_data_total . '
                    <td style="text-align: right" align="right"><strong>' . number_format($totalYear, 2) . '</strong></td>
                    <td style="text-align: right" align="right"><strong>' . number_format(
                    round($totalYear / 12, 2),
                    2
                ) . '</strong></td>
                    <td style="text-align: right" align="right"><strong></strong></td>
                ';
                break;
            case 'show_both':
                $table_header = '
                    <td><strong>Account Name</strong></td>
                    ' . $show_annual_property_budget_row_data_label . '
                    <td style="text-align: right" align="right"><strong>Tenant Portion (pa)</strong></td>
                    <td style="text-align: right" align="right"><strong>Tenant Portion (pcm)</strong></td>
                    <td style="text-align: right" align="right"><strong>Tenant Rate <br/>(per ' . $_SESSION['country_default']['area_unit_string'] . ')</strong></td>
                    <td style="text-align: right" align="right"><strong>Tenant %</strong></td>
                ';
                $table_subtotal = '
                    <td style="text-align: right"><strong>SUB TOTAL</strong></td>
                    ' . $show_annual_property_budget_row_data_total . '
                    <td style="text-align: right" align="right"><strong>' . number_format($totalYear, 2) . '</strong></td>
                    <td style="text-align: right" align="right"><strong>' . number_format(
                    round($totalYear / 12, 2),
                    2
                ) . '</strong></td>
                    <td style="text-align: right" align="right"><strong>' . number_format($overAllRatePerSQM, 2) . '</strong></td>
                    <td style="text-align: right" align="right"><strong></strong></td>
                ';
                break;
            case 'none':
                $table_header = '
                    <td><strong>Account Name</strong></td>
                    ' . $show_annual_property_budget_row_data_label . '
                    <td style="text-align: right" align="right"><strong>Tenant Portion (pa)</strong></td>
                    <td style="text-align: right" align="right"><strong>Tenant Portion (pcm)</strong></td>
                ';
                $table_subtotal = '
                    <td style="text-align: right"><strong>SUB TOTAL</strong></td>
                    ' . $show_annual_property_budget_row_data_total . '
                    <td style="text-align: right" align="right"><strong>' . number_format($totalYear, 2) . '</strong></td>
                    <td style="text-align: right" align="right"><strong>' . number_format(
                    round($totalYear / 12, 2),
                    2
                ) . '</strong></td>
                ';
                break;
        }

        $voContent = '
                <tr><td colspan="' . $table_colspan . '"><strong>' . ucwords(
            strtolower($_SESSION['country_default']['variable_outgoings'])
        ) . '</strong></td></tr>
                <tr><td colspan="' . $table_colspan . '"></td></tr>
                <tr>
                    ' . $table_header . '
                </tr>' . $voContent . '
                <tr>
                    ' . $table_subtotal . '
                </tr>
                <tr><td colspan="' . $table_colspan . '"></td></tr>
                ';
    }

    if ($recContent !== '') {
        $table_header = '';
        $table_subtotal = '';
        $tenant_percenatage_divisor = $overAllTotalForPropertyRec;
        if ($tenant_percenatage_divisor <= 0) {
            $tenant_percenatage_divisor = 1;
        }

        $tenant_percenatage = ($totalYearRec / $tenant_percenatage_divisor) * 100;

        $show_annual_property_budget_row_data_label = '';
        $show_annual_property_budget_row_data_total = '';
        if ($show_annual_property_budget == 'Yes') {
            $show_annual_property_budget_row_data_label = '<td style="text-align: right" align="right"><strong>Annual Property <br/> Budget (pa)</strong></td>';
            $show_annual_property_budget_row_data_total = '<td style="text-align: right" align="right"><strong>' . number_format(
                $overAllTotalForPropertyRec,
                2
            ) . '</strong></td>';
        }

        switch ($tenant_detail) {
            case 'show_sqm':
                $table_header = '
                    <td><strong>Account Name</strong></td>
                    ' . $show_annual_property_budget_row_data_label . '
                    <td style="text-align: right" align="right"><strong>Tenant Portion (pa)</strong></td>
                    <td style="text-align: right" align="right"><strong></strong></td>
                    <td style="text-align: right" align="right"><strong>Tenant Rate <br/>(per ' . $_SESSION['country_default']['area_unit_string'] . ')</strong></td>
                ';
                $table_subtotal = '
                    <td style="text-align: right"><strong>SUB TOTAL</strong></td>
                    ' . $show_annual_property_budget_row_data_total . '
                    <td style="text-align: right" align="right"><strong>' . number_format($totalYearRec, 2) . '</strong></td>
                    <td style="text-align: right" align="right"><strong></strong></td>
                    <td style="text-align: right" align="right"><strong></strong></td>
                ';
                break;
            case 'show_percentage':
                $table_header = '
                    <td><strong>Account Name</strong></td>
                    ' . $show_annual_property_budget_row_data_label . '
                    <td style="text-align: right" align="right"><strong>Tenant Portion (pa)</strong></td>
                    <td style="text-align: right" align="right"><strong></strong></td>
                    <td style="text-align: right" align="right"><strong>Tenant %</strong></td>
                ';
                $table_subtotal = '
                    <td style="text-align: right"><strong>SUB TOTAL</strong></td>
                    ' . $show_annual_property_budget_row_data_total . '
                    <td style="text-align: right" align="right"><strong>' . number_format($totalYearRec, 2) . '</strong></td>
                    <td style="text-align: right" align="right"><strong></strong></td>
                    <td style="text-align: right" align="right"><strong></strong></td>
                ';
                break;
            case 'show_both':
                $table_header = '
                    <td><strong>Account Name</strong></td>
                    ' . $show_annual_property_budget_row_data_label . '
                    <td style="text-align: right" align="right"><strong>Tenant Portion (pa)</strong></td>
                    <td style="text-align: right" align="right"><strong></strong></td>
                    <td style="text-align: right" align="right"><strong>Tenant Rate <br/>(per ' . $_SESSION['country_default']['area_unit_string'] . ')</strong></td>
                    <td style="text-align: right" align="right"><strong>Tenant %</strong></td>
                ';
                $table_subtotal = '
                    <td style="text-align: right"><strong>SUB TOTAL</strong></td>
                    ' . $show_annual_property_budget_row_data_total . '
                    <td style="text-align: right" align="right"><strong>' . number_format($totalYearRec, 2) . '</strong></td>
                    <td style="text-align: right" align="right"><strong></strong></td>
                    <td style="text-align: right" align="right"><strong>' . number_format($overAllRatePerSQMRec, 2) . '</strong></td>
                    <td style="text-align: right" align="right"><strong></strong></td>
                ';
                break;
            case 'none':
                $table_header = '
                    <td><strong>Account Name</strong></td>
                    ' . $show_annual_property_budget_row_data_label . '
                    <td style="text-align: right" align="right"><strong>Tenant Portion (pa)</strong></td>
                    <td style="text-align: right" align="right"><strong></strong></td>
                ';
                $table_subtotal = '
                    <td style="text-align: right"><strong>SUB TOTAL</strong></td>
                    ' . $show_annual_property_budget_row_data_total . '
                    <td style="text-align: right" align="right"><strong>' . number_format($totalYearRec, 2) . '</strong></td>
                    <td style="text-align: right" align="right"><strong></strong></td>
                ';
                break;
        }

        $recContent = '
                <tr><td colspan="' . $table_colspan . '"><strong>Directly Recoverable</strong></td></tr>
                <tr><td colspan="' . $table_colspan . '"></td></tr>
                <tr>
                   ' . $table_header . '
                </tr>' . $recContent . '
                <tr>
                    ' . $table_subtotal . '
                </tr>
                ';
    }

    $overall_total = '';
    $tenant_percenatage_divisor = ($overAllTotalForProperty + $overAllTotalForPropertyRec);
    if ($tenant_percenatage_divisor <= 0) {
        $tenant_percenatage_divisor = 1;
    }

    $show_annual_property_budget_row_data_total = '';
    if ($show_annual_property_budget == 'Yes') {
        $show_annual_property_budget_row_data_total = '<td style="text-align: right" align="right"><strong>' . number_format(
            $overAllTotalForProperty + $overAllTotalForPropertyRec,
            2
        ) . '</strong></td>';
    }

    $show_vo_overall_total = '';
    if ($voContent !== '') {
        $show_vo_overall_total = number_format(round($totalYear / 12, 2), 2);
    }

    switch ($tenant_detail) {
        case 'show_sqm':
            $overall_total = '
                <td style="text-align: right"><strong>TOTAL</strong></td>
                ' . $show_annual_property_budget_row_data_total . '
                <td style="text-align: right" align="right"><strong>' . number_format($totalYear + $totalYearRec, 2) . '</strong></td>
                <td style="text-align: right" align="right"><strong>' . $show_vo_overall_total . '</strong></td>
                <td style="text-align: right" align="right"><strong>' . number_format(
                $overAllRatePerSQM + $overAllRatePerSQMRec,
                2
            ) . '</strong></td>
            ';
            break;
        case 'show_percentage':
            $overall_total = '
                <td style="text-align: right"><strong>TOTAL</strong></td>
                ' . $show_annual_property_budget_row_data_total . '
                <td style="text-align: right" align="right"><strong>' . number_format($totalYear + $totalYearRec, 2) . '</strong></td>
                <td style="text-align: right" align="right"><strong>' . $show_vo_overall_total . '</strong></td>
                <td style="text-align: right" align="right"><strong></strong></td>
            ';
            break;
        case 'show_both':
            $overall_total = '
                <td style="text-align: right"><strong>TOTAL</strong></td>
                ' . $show_annual_property_budget_row_data_total . '
                <td style="text-align: right" align="right"><strong>' . number_format($totalYear + $totalYearRec, 2) . '</strong></td>
                <td style="text-align: right" align="right"><strong>' . $show_vo_overall_total . '</strong></td>
                <td style="text-align: right" align="right"><strong>' . number_format(
                $overAllRatePerSQM + $overAllRatePerSQMRec,
                2
            ) . '</strong></td>
                <td style="text-align: right" align="right"><strong></strong></td>
            ';
            break;
        case 'none':
            $overall_total = '
                <td style="text-align: right"><strong>TOTAL</strong></td>
                ' . $show_annual_property_budget_row_data_total . '
                <td style="text-align: right" align="right"><strong>' . number_format($totalYear + $totalYearRec, 2) . '</strong></td>
                <td style="text-align: right" align="right"><strong></strong></td>
            ';
            break;
    }


    return $content2 . '
        <table cellpadding="1" cellspacing="1">
            <tbody>
                ' . $voContent . '
                ' . $recContent . '
                <tr>
                    ' . $overall_total . '
                </tr>
            </tbody>
        </table>
        <br/>
        <br/>
        <br/>
        <br/>
        <br/>
        <br/>
        <span>Please note the above figures are exclusive of ' . $_SESSION['country_default']['tax_label'] . '</span>
    ';
}

function replacePlaceholdersAndImages($message = '', $placeholders = [], $replacements = [], $skipImg = false)
{
    global $pathPrefix, $clientDirectory;

    $filename = 'letter_temp' . time() . rand() . $_SESSION['user_id'];
    $temp_image_path = [];
    $counter = 0;

    if ($skipImg) {
        return str_replace($placeholders, $replacements, $message);
    }

    if ($message == '') {
        return [str_replace($placeholders, $replacements, $message), $temp_image_path];
    }

    $src = extractImageFromHTMLUsingXML($message);
    foreach ($src as $image_str) {
        $path_info = pathinfo($image_str);
        $letter_filePath = "../reports/{$clientDirectory}/letters/{$filename}_img_" . $counter . '_' . $_SESSION['user_id'] . $path_info['extension'];

        // save image to temp path with the same file name + counter + username. *unlink after pdf has been created
        $dataPieces = explode(',', str_replace('"', '', $image_str));
        $encodedImg = $dataPieces[1];
        $decodedImg = base64_decode($encodedImg);

        // Check if image was properly decoded and save it
        if ($decodedImg !== false && file_put_contents($letter_filePath, $decodedImg) !== false) {
            $temp_image_path[] = $letter_filePath;
        }

        $counter++;
    }

    $message = str_replace($src, $temp_image_path, $message);

    return [str_replace($placeholders, $replacements, $message), $temp_image_path];
}

function letter(&$context)
{
    global $clientDirectory, $pathPrefix;

    require_once __DIR__ . '/letter.php';

    if (! isPostback()) {
        $view = new MasterPage(userViews(), '/administration/letter.html');
        $view->setSection($context['module']);
    } else {
        $view = new UserControl(userViews(), '/administration/letter.html', '');
    }

    $view->bindAttributesFrom($_REQUEST);
    $validationErrors = [];
    $view->items['budget_types'] = [
        '1' => 'Cash Budget',
        '2' => 'Cash Forecast',
        '3' => 'Accruals Budget',
        '4' => 'Accruals Forecast',
    ];
    if (! $view->items['budget_type']) {
        $view->items['budget_type'] = '1';
    }

    if (! $view->items['showAnnualPropertyBudget']) {
        $view->items['showAnnualPropertyBudget'] = 'Yes';
    }

    $view->items['searchMethodList'] =
    [
        'Owner' => 'Property Owner',
        'Owner Company' => 'Owner Company',
        //		'Property' => 'Property',
        'Supplier' => 'Supplier',
        'Tenant' => 'Tenant',
        'Tenant Diary' => 'Tenant Diary',
        'Budget' => 'Budget',
    ];
    $view->items['tenant_detail_options'] =
    [
        'show_sqm' => 'Show ' . $_SESSION['country_default']['area_unit'],
        'show_percentage' => 'Show Percentage',
        'show_both' => 'Both',
        'none' => 'None',
    ];
    if (! isset($view->items['tenant_detail'])) {
        $view->items['tenant_detail'] = 'show_sqm';
    }

    $view->items['yesNoOption'] =
    [
        'Yes' => 'Yes',
        'No' => 'No',
    ];

    $view->items['statusList'] = [
        'A' => 'All Diary Entries',
        'O' => 'Open Diary Entries',
        'C' => 'Closed Diary Entries',
    ];

    $clientSendFrom = null;
    $clientFromName = null;
    $clientReplyTo = null;

    $customEmailList = [];

    $emailSetting = dbGetEmailSettingsCompiled($_SESSION['databaseID']);
    if ($emailSetting && (($emailSetting['use_postmark'] == 1 && $emailSetting['postmark_domain'] && $emailSetting['server_token'] && $emailSetting['DKIM_status'] == 1) || ($emailSetting['use_postmark'] == 0 && $emailSetting['smtp_domain']))) {
        if ($emailSetting['use_postmark'] == 1) {
            $domain = $emailSetting['postmark_domain'];

            if (! isset($view->items['send_from'])) {
                $clientSendFrom = $emailSetting['postmark_send_from'];
                $clientFromName = $emailSetting['postmark_send_name'];
                $clientReplyTo = $emailSetting['postmark_reply_to'];

                $optionSendFrom = $emailSetting['postmark_send_from'];
                $optionSendName = $emailSetting['postmark_send_name'];
                $optionReplyTo = $emailSetting['postmark_reply_to'];
            } else {
                $clientSendFrom = $view->items['send_from'];
                $clientFromName = $view->items['from_name'];
                $clientReplyTo = $view->items['reply_to'];
            }
        } else {
            $domain = $emailSetting['smtp_domain'];

            if (! isset($view->items['send_from'])) {
                $clientSendFrom = $emailSetting['smtp_send_from'];
                $clientFromName = $emailSetting['smtp_send_name'];
                $clientReplyTo = $emailSetting['smtp_reply_to'];

                $optionSendFrom = $emailSetting['smtp_send_from'];
                $optionSendName = $emailSetting['smtp_send_name'];
                $optionReplyTo = $emailSetting['smtp_reply_to'];
            } else {
                $clientSendFrom = $view->items['send_from'];
                $clientFromName = $view->items['from_name'];
                $clientReplyTo = $view->items['reply_to'];
            }
        }

        $item = [];
        $item['id'] = 0;
        $item['send_from'] = $optionSendFrom;
        $item['from_name'] = $optionSendName;
        $item['reply_to'] = $optionReplyTo;
        $item['type'] = 'config';
        $customEmailList[] = $item;

        foreach (dbListEmailConfigFiltered() as $value) {
            if ($value['domain'] == $domain) {
                $custom_item = [];
                $custom_item['id'] = $value['id'];
                $custom_item['send_from'] = $value['send_from'];
                $custom_item['from_name'] = $value['from_name'];
                $custom_item['reply_to'] = $value['reply_to'];
                $custom_item['type'] = 'custom';
                $customEmailList[] = $custom_item;
            }
        }
    } else {
        $clientConfig = dbGetClientEmailConfig();

        if (! empty($clientConfig) && isset($clientConfig['fromEmail']) && $clientConfig['fromEmail'] != '' && isset($clientConfig['fromName']) && $clientConfig['fromName'] != '' && isset($clientConfig['replyTo']) && $clientConfig['replyTo'] != '') {
            if (! isset($view->items['send_from'])) {
                $clientSendFrom = $clientConfig['fromEmail'];
                $clientFromName = $clientConfig['fromName'];
                $clientReplyTo = $clientConfig['replyTo'];
            } else {
                $clientSendFrom = $view->items['send_from'];
                $clientFromName = $view->items['from_name'];
                $clientReplyTo = $view->items['reply_to'];
            }

            $item = [];
            $item['id'] = 0;
            $item['send_from'] = $clientConfig['fromEmail'];
            $item['from_name'] = $clientConfig['fromName'];
            $item['reply_to'] = $clientConfig['replyTo'];
            $item['type'] = 'config';
            $customEmailList[] = $item;

            foreach (dbListEmailConfig([], 'id') as $value) {
                $custom_item = [];
                $custom_item['id'] = $value['id'];
                $custom_item['send_from'] = $value['send_from'];
                $custom_item['from_name'] = $value['from_name'];
                $custom_item['reply_to'] = $value['reply_to'];
                $custom_item['type'] = 'custom';
                $customEmailList[] = $custom_item;
            }
        }
    }

    $view->items['clientSendFrom'] = $clientSendFrom;
    $view->items['clientFromName'] = $clientFromName;
    $view->items['clientReplyTo'] = $clientReplyTo;

    $view->items['customEmailList'] = $customEmailList;

    // Start Email Address Book
    $email_cen_sendmail_setting = dbGetEmailCenParamSetting('SENDMAIL');
    $email_cen_send = false;
    if ($email_cen_sendmail_setting && $email_cen_sendmail_setting['parameterDescription'] == '1') {
        $email_cen_send = true;
    }

    // End Email Address Book
    $letterTemplateID = $view->items['letterID'];
    if ($view->items['ownerID']) {
        if (strpos($view->items['ownerID'], '::')) {
            $ownerIDBreakList = explode('::', $view->items['ownerID']);
        } else {
            $ownerIDBreakList = explode(',', $view->items['ownerID']);
        }

        $ownerIDBreak = [];
        for ($x = 0; $x <= count($ownerIDBreakList ?? []) - 1; $x++) {
            $ownerIDBreakl = explode(' - ', $ownerIDBreakList[$x]);
            $ownerIDBreak[$x] = $ownerIDBreakl[0];
        }

        $ownerIDBreak = implode(',', $ownerIDBreak);
        if (strpos($view->items['ownerID'], '::')) {
            $view->items['ownerID'] = explode('::', $view->items['ownerID']);
        }
    }

    if ($view->items['supplierID']) {
        if (strpos($view->items['ownerID'], '::')) {
            $supplierIDBreakList = explode('::', $view->items['supplierID']);
        } else {
            $supplierIDBreakList = explode(',', $view->items['supplierID']);
        }

        $supplierIDBreak = [];
        for ($x = 0; $x <= count($supplierIDBreakList ?? []) - 1; $x++) {
            $supplierIDBreakl = explode(' - ', $supplierIDBreakList[$x]);
            $supplierIDBreak[$x] = $supplierIDBreakl[0];
        }

        $supplierIDBreak = implode(',', $supplierIDBreak);
        if (strpos($view->items['supplierID'], '::')) {
            $view->items['supplierID'] = explode('::', $view->items['supplierID']);
        }
    }

    if ($view->items['tenantID']) {
        if (strpos($view->items['tenantID'], '::')) {
            $tenantIDBreakList = explode('::', $view->items['tenantID']);
        } else {
            $tenantIDBreakList = explode(',', $view->items['tenantID']);
        }

        $tenantIDBreak = [];
        for ($x = 0; $x <= count($tenantIDBreakList ?? []) - 1; $x++) {
            $tenantIDBreakl = explode(' - ', $tenantIDBreakList[$x]);
            $tenantIDBreak[$x] = $tenantIDBreakl[0] . ' - ' . $tenantIDBreakl[1];
        }

        $tenantIDBreak = implode(',', $tenantIDBreak);
        if (strpos($view->items['tenantID'], '::')) {
            $view->items['tenantID'] = explode('::', $view->items['tenantID']);
        }
    }


    if ($view->items['showCurrentOnly'] != 'No') {
        $view->items['showCurrentOnly'] = 'Yes';
    }

    if ($view->items['showActiveOnly'] != 'Yes') {
        $view->items['showActiveOnly'] = 'No';
    }

    if ($view->items['showContactTypes'] != 'Yes') {
        $view->items['showContactTypes'] = 'No';
    }

    $view->items['categoryList'] = mapParameters(dbGetLetterCategory(), 'letterCategoryID', 'letterCategoryName');
    $categoryName = false;

    if ($view->items['emailsSent']) {
        // $view->items['statusMessage'] = 'The letter has been emailed to : ' . str_replace(';', ' , ', $view->items['emailsSent']);
        $view->items['statusMessage'] = 'The email has been successfully sent.';
    }

    if ($view->items['validationErrors']) {
        $view->items['validationErrors'] = $validationErrors = json_decode($view->items['validationErrors']);
    }

    if ($view->items['searchMethod'] != 'Supplier' && $view->items['searchMethod'] != 'Owner Company') {
        if (! is_array($view->items['propertyManagerID'])) {
            $view->items['propertyManagerID'] = deserializeParameters($view->items['propertyManagerID']);
        }

        $view->items['propertyManagerList'] = dbPropertyManagerList();

        $view->items['propertyList'] = dbPropertyGroupList(
            $view->items['propertyManagerID'],
            ucwords(strtolower($_SESSION['country_default']['property_manager'])),
            $view->items['showActiveOnly'] == 'Yes'
        );
    }

    if ($view->items['showContactTypes'] != 'Yes' || $view->items['action'] == 'changeMethod') {
        unset($view->items['contactTypeID']);
        unset($contactTypeIDs);
    } else {
        if ($view->items['contactTypeID']) {
            $view->items['contactTypeID'] = deserializeParameters(
                $view->items['contactTypeID'],
                (strpos($view->items['contactTypeID'], '::') !== false ? '::' : ',')
            );
        }

        $contactTypeIDs = $view->items['contactTypeID']; // explode(',',$view->items['contactTypeID'][0]);
    }

    $propMgrLabel = ucwords(strtolower($_SESSION['country_default']['property_manager']));
    $view->items['placeholderList'] = [];
    switch ($view->items['searchMethod']) {
        case 'Owner':
        case 'Owner Budget':

            // $view->items['ownerList'] = dbCompanyListWithEmail (OWNER);

            if ($view->items['propertyID']) {
                $view->items['propertyID'] = deserializeParameters($view->items['propertyID']);
            }

            if ($view->items['searchMethod'] == 'Owner Budget' && $view->items['propertyID']) {
                $view->items['budget_year_list'] = dbGetDistinctBudgetYears($view->items['propertyID']);
            }

            // $view->items['ownerList'] = dbGetPropertiesOwnersWithEmailCentralised($view->items['propertyID'] ? $view->items['propertyID'] : array(),'OWNGEN');
            $view->items['ownerList'] = dbGetPropertiesOwnersWithEmail(
                $view->items['propertyID'] ? $view->items['propertyID'] : []
            );
            if ($email_cen_send) {
                $new_temp_list = [];
                foreach ($view->items['ownerList'] as $row_data) {
                    $company_code = $row_data['companyID'];
                    $company_name = $row_data['company_name'];
                    $property_code = $row_data['property_code'];

                    if ($view->items['showContactTypes'] == 'Yes') {
                        $emailAddress = dbGetCompanyRoleEmails($company_code, $contactTypeIDs);
                    } else {
                        $emailAddress = dbGetPropertiesOwnersWithEmailCentralised(
                            $property_code,
                            $company_code,
                            'OWNGEN'
                        );
                    }

                    $defaultEmailSend = '1';
                    foreach ($emailAddress as $aRowCentralisedEmailCheck) {
                        $defaultEmail = $aRowCentralisedEmailCheck['defaultEmail'];
                        if ($defaultEmail == '0') {
                            $defaultEmailSend = $defaultEmail;
                        }
                    }

                    $email_address_all = '';
                    foreach ($emailAddress as $row_data_2) {
                        $email_address = $row_data_2['emailAddress'];
                        $default_email = $row_data_2['defaultEmail'];
                        if ($email_address !== '' && $defaultEmailSend == $default_email) {
                            //                            $email_address = substr($email_address, 0, 3).'***'.substr($email_address, strpos($email_address, "@"));
                            $email_address_all .= $email_address . ', ';
                        }
                    }

                    $email_address_all = rtrim($email_address_all, ', ');
                    // if(strlen($email_address_all) > 65) $email_address_all = substr($email_address_all, 0 , 65)."...";
                    if ($email_address_all !== '') {
                        $email_address_all = ' (' . $email_address_all . ')';
                    }

                    $overall_label = $company_name . $email_address_all;

                    if ($view->items['showContactTypes'] != 'Yes' || $email_address_all !== '' && $view->items['showContactTypes'] == 'Yes') {
                        $new_temp_list[] = [
                            'companyID' => $company_code,
                            'companyNameWithEmail' => $overall_label,
                        ];
                    }
                }

                //                $view->items['ownerList'] = $new_temp_list;
                $view->items['ownerList'] = array_unique($new_temp_list, SORT_REGULAR);
            }

            if ($ownerIDBreak) {
                $ownerIDBreak = deserializeParameters($ownerIDBreak);
            }

            $categoryName = 'Owner Letter';
            if ($view->items['searchMethod'] == 'Owner Budget') {
                $categoryName = 'Owner Budget Letter';
            }

            $view->items['placeholderList'][$view->items['searchMethod']]['Date'] = [
                'day',
                'monthNumber',
                'monthName',
                'year',
                'priorMonth',
                'priorYear',
            ];

            $view->items['placeholderList'][$view->items['searchMethod']][$propMgrLabel] = [
                'propertyManagerID',
                'propertyManagerName',
                'propertyManagerTitle',
                'propertyManagerEmail',
                'propertyManagerMobileNumber',
            ];
            $view->items['placeholderList'][$view->items['searchMethod']]['Property'] = [
                'propertyName',
                'propertyStreet',
                'propertyCity',
                'propertyState',
                'propertyPostCode',
                'propertyDescription',
            ];
            $view->items['placeholderList'][$view->items['searchMethod']]['Miscellaneous'] = [
                'ownerName',
                'ownerCode',
                'ownerStreet',
                'ownerSuburb',
                'ownerPostCode',
                'ownerState',
                'propertyCode',
                'contactSalutation',
                'totalRemittanceForLastMonth',
                'ownerRemitLast',
                'totalOwnerRemittanceForLast3months',
                'totalRemittanceForLast3Months',
                'propertyContactSalutation',
            ];
            $view->items['placeholderList'][$view->items['searchMethod']]['budget'] = [
                'newVO',
                'newVOSQM',
                'newVOpm',
                'currentVO',
                'currentVOSQM',
                'currentVOpm',
                'changeVOpercent',
                'newDR',
                'newDRSQM',
                'newTotal',
                'newTotalSQM',
            ];

            $contactType = 'CROLETYPE';
            break;
            // case 'Property':
            // 	$view->items['propertyList'] = dbPropertyGroupList ();
            // 	if ($view->items['propertyID'])$view->items['propertyID'] = deserializeParameters ($view->items['propertyID']);
            // 	break;
        case 'Owner Company':
            $view->items['companyList'] = dbGetOwnerCompanyWithEmail();
            if ($email_cen_send) {
                $new_temp_list = [];
                foreach ($view->items['companyList'] as $row_data) {
                    $company_code = $row_data['companyID'];
                    $company_name = $row_data['company_name'];
                    $property_code = $row_data['property_code'];

                    if ($view->items['showContactTypes'] == 'Yes') {
                        $emailAddress = dbGetCompanyRoleEmails($company_code, $contactTypeIDs);
                    } else {
                        $emailAddress = dbGetCompanyEmailCentralised($company_code, 'OWNGEN');
                    }

                    $defaultEmailSend = '1';
                    foreach ($emailAddress as $aRowCentralisedEmailCheck) {
                        $defaultEmail = $aRowCentralisedEmailCheck['defaultEmail'];
                        if ($defaultEmail == '0') {
                            $defaultEmailSend = $defaultEmail;
                        }
                    }

                    $email_address_all = '';
                    foreach ($emailAddress as $row_data_2) {
                        $email_address = $row_data_2['emailAddress'];
                        $default_email = $row_data_2['defaultEmail'];
                        if ($email_address !== '' && $defaultEmailSend == $default_email) {
                            //                            $email_address = substr($email_address, 0, 3).'***'.substr($email_address, strpos($email_address, "@"));
                            $email_address_all .= $email_address . ', ';
                        }
                    }

                    $email_address_all = rtrim($email_address_all, ', ');
                    // if(strlen($email_address_all) > 65) $email_address_all = substr($email_address_all, 0 , 65)."...";
                    if ($email_address_all !== '') {
                        $email_address_all = ' (' . $email_address_all . ')';
                    }

                    $overall_label = $company_name . $email_address_all;

                    if ($view->items['showContactTypes'] != 'Yes' || $email_address_all !== '' && $view->items['showContactTypes'] == 'Yes') {
                        $new_temp_list[] = [
                            'companyID' => $company_code,
                            'companyNameWithEmail' => $overall_label,
                        ];
                    }
                }

                //                $view->items['companyList'] = $new_temp_list;
                $view->items['companyList'] = array_unique($new_temp_list, SORT_REGULAR);
            }

            if ($supplierIDBreak) {
                $supplierIDBreak = deserializeParameters($supplierIDBreak);
            }

            $categoryName = 'Owner Company Letter';
            $view->items['placeholderList']['Owner Company']['Date'] = [
                'day',
                'month',
                'monthName',
                'year',
            ];
            $view->items['placeholderList']['Owner Company']['Owner'] = [
                'ownerCode',
                'ownerName',
                'ownerStreet',
                'ownerCity',
                'ownerState',
                'ownerPostCode',
                'contactSalutation',
            ];

            $contactType = 'CROLETYPE';
            break;
        case 'Supplier':
            $view->items['supplierList'] = dbCompanyListWithEmail(SUPPLIER);
            if ($email_cen_send) {
                $new_temp_list = [];
                foreach ($view->items['supplierList'] as $row_data) {
                    $company_code = $row_data['companyID'];
                    $company_name = $row_data['company_name'];
                    $property_code = $row_data['property_code'];

                    if ($view->items['showContactTypes'] == 'Yes') {
                        $emailAddress = dbGetCompanyRoleEmails($company_code, $contactTypeIDs);
                    } else {
                        $emailAddress = dbGetCompanyEmailCentralised($company_code, 'SUPGEN');
                    }

                    $defaultEmailSend = '1';
                    foreach ($emailAddress as $aRowCentralisedEmailCheck) {
                        $defaultEmail = $aRowCentralisedEmailCheck['defaultEmail'];
                        if ($defaultEmail == '0') {
                            $defaultEmailSend = $defaultEmail;
                        }
                    }

                    $email_address_all = '';
                    foreach ($emailAddress as $row_data_2) {
                        $email_address = $row_data_2['emailAddress'];
                        $default_email = $row_data_2['defaultEmail'];
                        if ($email_address !== '' && $defaultEmailSend == $default_email) {
                            //                            $email_address = substr($email_address, 0, 3).'***'.substr($email_address, strpos($email_address, "@"));
                            $email_address_all .= $email_address . ', ';
                        }
                    }

                    $email_address_all = rtrim($email_address_all, ', ');
                    // if(strlen($email_address_all) > 65) $email_address_all = substr($email_address_all, 0 , 65)."...";
                    if ($email_address_all !== '') {
                        $email_address_all = ' (' . $email_address_all . ')';
                    }

                    $overall_label = $company_name . $email_address_all;

                    if ($view->items['showContactTypes'] != 'Yes' || $email_address_all !== '' && $view->items['showContactTypes'] == 'Yes') {
                        $new_temp_list[] = [
                            'companyID' => $company_code,
                            'companyNameWithEmail' => $overall_label,
                        ];
                    }
                }

                //                $view->items['supplierList'] = $new_temp_list;
                $view->items['supplierList'] = array_unique($new_temp_list, SORT_REGULAR);
            }

            // $view->items['supplierList'] = dbCompanyListWithEmailCentralised (SUPPLIER,'SUPGEN');
            if ($supplierIDBreak) {
                $supplierIDBreak = deserializeParameters($supplierIDBreak);
            }

            $categoryName = 'Supplier Letter';

            $view->items['placeholderList']['Supplier']['Date'] = [
                'day',
                'month',
                'monthName',
                'year',
            ];
            $view->items['placeholderList']['Supplier']['Supplier'] = [
                'supplierName',
                'supplierStreet',
                'supplierCity',
                'supplierState',
                'supplierPostCode',
                'contactSalutation',
            ];

            $contactType = 'CROLETYPE';
            break;
        case 'Tenant':

            // $view->items['tenantList'] = dbLeaseListWithEmailCentralised ('','TENGEN');
            $view->items['tenantList'] = dbLeaseListWithEmail('');

            if ($view->items['propertyID']) {
                $view->items['propertyID'] = deserializeParameters($view->items['propertyID']);
                // $view->items['tenantList'] = dbLeaseListWithEmailCentralised($view->items['propertyID'],'TENGEN', $showInactive=false,$view->items['showCurrentOnly'] == 'Yes');
                $view->items['tenantList'] = dbLeaseListWithEmail(
                    $view->items['propertyID'],
                    $showInactive = false,
                    $view->items['showCurrentOnly'] == 'Yes'
                );
            }

            if ($email_cen_send) {
                $new_temp_list = [];
                foreach ($view->items['tenantList'] as $row_data) {
                    $row_arr = $row_data;
                    $property_code = $row_data['propertyID'];
                    $lease_code = $row_data['leaseID'];
                    $prop_leaseID = $row_data['prop_leaseID'];
                    $leaseStatus = $row_data['leaseStatus'];
                    $description = $row_data['description'];
                    $street = $row_data['street'];
                    $company_name = $row_data['leaseName'];

                    if ($view->items['showContactTypes'] == 'Yes') {
                        $emailAddress = dbGetLeaseRoleEmails($property_code, $lease_code, $contactTypeIDs);
                    } else {
                        $emailAddress = dbGetDebtorEmailCentralised($property_code, $lease_code, 'TENGEN');
                    }

                    $defaultEmailSend = '1';
                    foreach ($emailAddress as $aRowCentralisedEmailCheck) {
                        $defaultEmail = $aRowCentralisedEmailCheck['defaultEmail'];
                        if ($defaultEmail == '0') {
                            $defaultEmailSend = $defaultEmail;
                        }
                    }

                    $email_address_all = '';
                    foreach ($emailAddress as $row_data_2) {
                        $email_address = $row_data_2['emailAddress'];
                        $default_email = $row_data_2['defaultEmail'];
                        if ($email_address !== '' && $defaultEmailSend == $default_email) {
                            //                            $email_address = substr($email_address, 0, 3).'***'.substr($email_address, strpos($email_address, "@"));
                            $email_address_all .= $email_address . ', ';
                        }
                    }

                    $email_address_all = rtrim($email_address_all, ', ');
                    $email_address_all = rtrim($email_address_all, ', ');
                    // if(strlen($email_address_all) > 65) $email_address_all = substr($email_address_all, 0 , 65)."...";
                    if ($email_address_all !== '') {
                        $email_address_all = ' (' . $email_address_all . ')';
                    }

                    $overall_label = $lease_code . ' - ' . $company_name . $email_address_all;

                    $row_arr['leaseNameWithEmail'] = $overall_label;

                    if ($view->items['showContactTypes'] != 'Yes' || $email_address_all !== '' && $view->items['showContactTypes'] == 'Yes') {
                        $new_temp_list[] = $row_arr;
                    }
                }

                //                $view->items['tenantList'] = $new_temp_list;
                $view->items['tenantList'] = array_unique($new_temp_list, SORT_REGULAR);
            }

            if ($view->items['resetTenants']) {
                $view->items['tenantID'] = '';
            }

            if ($tenantIDBreak) {
                $tenantIDBreak = deserializeParameters($tenantIDBreak);
            }

            $categoryName = 'Lease Letter';

            $view->items['placeholderList']['Tenant']['Date'] = [
                'day',
                'month',
                'monthName',
                'year',
            ];
            $view->items['placeholderList']['Tenant'][$propMgrLabel] = [
                'propertyManagerName',
                'propertyManagerTitle',
                'propertyManagerEmail',
                'propertyManagerMobileNumber',
            ];
            $view->items['placeholderList']['Tenant']['Property'] = [
                'propertyName',
                'propertyStreet',
                'propertyCity',
                'propertyState',
                'propertyPostCode',
                'propertyCode',
                'principalOwner',
                'principalOwnerABN',
            ];
            $view->items['placeholderList']['Tenant']['Lease'] = [
                'leaseName',
                'leaseDescription',
                'leaseStreet',
                'leaseCity',
                'leaseState',
                'leasePostCode',
                'leaseExpiry',
                'nextTermCommencement',
                'CRN',
                'leaseCommencement',
                'leaseOption',
                'leaseCode',
                'rentPM',
                'rentAnnual',
            ];
            $view->items['placeholderList']['Tenant']['Miscellaneous'] = [
                'unitDescription',
                'tenantName',
                'leaseContactSalutation',
                'nextRentReviewDate',
                'nextScheduledInspectionDate',
                'nextInsuranceExpiryDate',
                'nextInsuranceExpiryType',
                'nextInsuranceExpiryNote',
            ];
            $view->items['placeholderList']['Tenant']['Mail'] = [
                'mailingName',
                'mailingAddress',
                'mailingCity',
                'mailingState',
                'mailingPostCode',
                'mailingCountry',
            ];

            $contactType = 'LROLETYPE';
            break;
        case 'Tenant Diary':
            $view->items['diary_type_list'] = dbDiaryTypeListByProperty('');
            if ($view->items['propertyID']) {
                $view->items['propertyID'] = deserializeParameters($view->items['propertyID']);
                $view->items['diary_type_list'] = dbDiaryTypeListByProperty($view->items['propertyID']);
            }

            $view->items['tenantList'] = dbLeaseListWithEmail('');

            $categoryName = 'Tenant Diary Letter';
            // Default Values
            $month = date('m');
            $year = date('Y');
            if (empty($view->items['fromDate'])) {
                $view->items['fromDate'] = date("01/{$month}/{$year}");
            }

            if (empty($view->items['toDate'])) {
                nextMonth($month, $year);
                $month = str_pad($month, 2, '0', STR_PAD_LEFT);
                $view->items['toDate'] = date("01/{$month}/{$year}");
            }

            if ($view->items['diary_type']) {
                $view->items['tenantList'] = dbDiaryLeaseListWithEmail(
                    $view->items['diary_type'],
                    $view->items['fromDate'],
                    $view->items['toDate'],
                    $view->items['propertyID'],
                    $view->items['status']
                );
            }

            if ($email_cen_send) {
                $new_temp_list = [];
                foreach ($view->items['tenantList'] as $row_data) {
                    $row_arr = $row_data;
                    $property_code = $row_data['propertyID'];
                    $lease_code = $row_data['leaseID'];
                    $prop_leaseID = $row_data['prop_leaseID'];
                    $leaseStatus = $row_data['leaseStatus'];
                    $description = $row_data['description'];
                    $street = $row_data['street'];
                    $company_name = $row_data['leaseName'];

                    if ($view->items['showContactTypes'] == 'Yes') {
                        $emailAddress = dbGetLeaseRoleEmails($property_code, $lease_code, $contactTypeIDs);
                    } else {
                        $emailAddress = dbGetDebtorEmailCentralised($property_code, $lease_code, 'TENGEN');
                    }

                    $defaultEmailSend = '1';
                    foreach ($emailAddress as $aRowCentralisedEmailCheck) {
                        $defaultEmail = $aRowCentralisedEmailCheck['defaultEmail'];
                        if ($defaultEmail == '0') {
                            $defaultEmailSend = $defaultEmail;
                        }
                    }

                    $email_address_all = '';
                    foreach ($emailAddress as $row_data_2) {
                        $email_address = $row_data_2['emailAddress'];
                        $default_email = $row_data_2['defaultEmail'];
                        if ($email_address !== '' && $defaultEmailSend == $default_email) {
                            //                            $email_address = substr($email_address, 0, 3).'***'.substr($email_address, strpos($email_address, "@"));
                            $email_address_all .= $email_address . ', ';
                        }
                    }

                    $email_address_all = rtrim($email_address_all, ', ');
                    // if(strlen($email_address_all) > 65) $email_address_all = substr($email_address_all, 0 , 65)."...";
                    if ($email_address_all !== '') {
                        $email_address_all = ' (' . $email_address_all . ')';
                    }

                    $overall_label = $lease_code . ' - ' . $company_name . $email_address_all;
                    $row_arr['leaseNameWithEmail'] = $overall_label;

                    if ($view->items['showContactTypes'] != 'Yes' || $email_address_all !== '' && $view->items['showContactTypes'] == 'Yes') {
                        $new_temp_list[] = $row_arr;
                    }
                }

                //                $view->items['tenantList'] = $new_temp_list;
                $view->items['tenantList'] = array_unique($new_temp_list, SORT_REGULAR);
            }

            if ($tenantIDBreak) {
                $tenantIDBreak = deserializeParameters($tenantIDBreak);
            }

            $view->items['placeholderList']['Tenant Diary']['Date'] = [
                'day',
                'month',
                'monthName',
                'year',
            ];
            $view->items['placeholderList']['Tenant Diary'][$propMgrLabel] = [
                'propertyManagerName',
                'propertyManagerTitle',
                'propertyManagerEmail',
                'propertyManagerMobileNumber',
            ];
            $view->items['placeholderList']['Tenant Diary']['Property'] = [
                'propertyName',
                'propertyStreet',
                'propertyCity',
                'propertyState',
                'propertyPostCode',
                'propertyCode',
                'principalOwner',
                'principalOwnerABN',
            ];
            $view->items['placeholderList']['Tenant Diary']['Lease'] = [
                'leaseName',
                'leaseDescription',
                'leaseStreet',
                'leaseCity',
                'leaseState',
                'leasePostCode',
                'leaseExpiry',
                'nextTermCommencement',
                'CRN',
                'leaseCommencement',
                'leaseOption',
                'leaseCode',
                'rentPM',
                'rentAnnual',
            ];
            $view->items['placeholderList']['Tenant Diary']['Miscellaneous'] = [
                'unitDescription',
                'tenantName',
                'leaseContactSalutation',
                'nextRentReviewDate',
                'nextScheduledInspectionDate',
                'nextInsuranceExpiryDate',
                'nextInsuranceExpiryType',
                'nextInsuranceExpiryNote',
            ];

            $view->items['placeholderList']['Tenant Diary']['Mail'] = [
                'mailingName',
                'mailingAddress',
                'mailingCity',
                'mailingState',
                'mailingPostCode',
                'mailingCountry',
            ];

            $view->items['placeholderList']['Tenant Diary']['Description'] = [
                'diaryDescription',
            ];

            $contactType = 'LROLETYPE';
            break;
        case 'Budget':
            // $view->items['tenantList'] = dbLeaseListWithEmailCentralised ('','TENBUDGET');
            $view->items['tenantList'] = dbLeaseListWithEmail('');

            if ($view->items['propertyID']) {
                $view->items['propertyID'] = deserializeParameters($view->items['propertyID']);
                // $view->items['tenantList'] = dbLeaseListWithEmailCentralised($view->items['propertyID'],'TENBUDGET', $showInactive=false,$view->items['showCurrentOnly'] == 'Yes');
                $view->items['tenantList'] = dbLeaseListWithEmail(
                    $view->items['propertyID'],
                    $showInactive = false,
                    $view->items['showCurrentOnly'] == 'Yes'
                );
            }

            if ($email_cen_send) {
                $new_temp_list = [];
                foreach ($view->items['tenantList'] as $row_data) {
                    $row_arr = $row_data;
                    $property_code = $row_data['propertyID'];
                    $lease_code = $row_data['leaseID'];
                    $prop_leaseID = $row_data['prop_leaseID'];
                    $leaseStatus = $row_data['leaseStatus'];
                    $description = $row_data['description'];
                    $street = $row_data['street'];
                    $company_name = $row_data['leaseName'];

                    if ($view->items['showContactTypes'] == 'Yes') {
                        $emailAddress = dbGetLeaseRoleEmails($property_code, $lease_code, $contactTypeIDs);
                    } else {
                        $emailAddress = dbGetDebtorEmailCentralised($property_code, $lease_code, 'TENBUDGET');
                    }

                    $defaultEmailSend = '1';
                    foreach ($emailAddress as $aRowCentralisedEmailCheck) {
                        $defaultEmail = $aRowCentralisedEmailCheck['defaultEmail'];
                        if ($defaultEmail == '0') {
                            $defaultEmailSend = $defaultEmail;
                        }
                    }

                    $email_address_all = '';
                    foreach ($emailAddress as $row_data_2) {
                        $email_address = $row_data_2['emailAddress'];
                        $default_email = $row_data_2['defaultEmail'];
                        if ($email_address !== '' && $defaultEmailSend == $default_email) {
                            //                            $email_address = substr($email_address, 0, 3).'***'.substr($email_address, strpos($email_address, "@"));
                            $email_address_all .= $email_address . ', ';
                        }
                    }

                    $email_address_all = rtrim($email_address_all, ', ');
                    // if(strlen($email_address_all) > 65) $email_address_all = substr($email_address_all, 0 , 65)."...";
                    if ($email_address_all !== '') {
                        $email_address_all = ' (' . $email_address_all . ')';
                    }

                    $overall_label = $lease_code . ' - ' . $company_name . $email_address_all;
                    //                    if(strlen($overall_label) > 65) $overall_label = substr($overall_label, 0 , 65)."...";
                    $row_arr['leaseNameWithEmail'] = $overall_label;

                    if ($view->items['showContactTypes'] != 'Yes' || $email_address_all !== '' && $view->items['showContactTypes'] == 'Yes') {
                        $new_temp_list[] = $row_arr;
                    }
                }

                //                $view->items['tenantList'] = $new_temp_list;
                $view->items['tenantList'] = array_unique($new_temp_list, SORT_REGULAR);
            }

            if ($view->items['resetTenants']) {
                $view->items['tenantID'] = '';
            }

            if ($tenantIDBreak) {
                $tenantIDBreak = deserializeParameters($tenantIDBreak);
            }

            $categoryName = 'Budget Letter';

            $view->items['placeholderList']['Budget']['Date'] = [
                'day',
                'month',
                'monthName',
                'year',
            ];
            $view->items['placeholderList']['Budget'][$propMgrLabel] = [
                'propertyManagerName',
                'propertyManagerTitle',
                'propertyManagerEmail',
                'propertyManagerMobileNumber',
            ];
            $view->items['placeholderList']['Budget']['Property'] = [
                'propertyName',
                'propertyStreet',
                'propertyCity',
                'propertyState',
                'propertyPostCode',
                'propertyPrincipalOwnerCompanyCode',
            ];
            $view->items['placeholderList']['Budget']['Lease'] = [
                'leaseCode',
                'leaseName',
                'leaseDescription',
                'leaseStreet',
                'leaseCity',
                'leaseState',
                'leasePostCode',
            ];
            $view->items['placeholderList']['Budget']['Miscellaneous'] = [
                'unitDescription',
                'tenantName',
                'leaseContactSalutation',
                'nextRentReviewDate',
            ];

            $view->items['placeholderList']['Budget']['budget'] = [
                'newVO',
                'newVOSQM',
                'newVOpm',
                'currentVO',
                'currentVOSQM',
                'currentVOpm',
                'changeVOpercent',
                'newDR',
                'newDRSQM',
                'newTotal',
                'newTotalSQM',
            ];
            $view->items['placeholderList']['Budget']['Mail'] = [
                'mailingName',
                'mailingAddress',
                'mailingCity',
                'mailingState',
                'mailingPostCode',
                'mailingCountry',
            ];

            $contactType = 'LROLETYPE';
            break;
    }

    $emails_sent = [];
    $view->items['contactTypeList'] = [];
    if ($email_cen_send) { // client uses email centralization
        $view->items['contactTypeList'] = dbGetParams($contactType, true, true);
    }

    $letterID = $view->items['letterID'];

    if ($letterID && ! $view->items['submit']) {
        $letter_vars = dbGetLetterTemplate($letterID);
        $view->items['emailSubject'] = $letter_vars['letterTemplateSubject'];
        $view->items['letterContent'] = $letter_vars['letterEmailBody'];
        $view->items['usePDF'] = true;
        $view->items['useEMAIL'] = true;
        $view->items['pdfContent'] = $letter_vars['letterTemplateBody'];
        $view->items['letterFilename'] = $letter_vars['useDocxFile'] ? $letter_vars['letterFilename'] : '';
        $view->items['useDocxFile'] = $letter_vars['useDocxFile'];
        unset($view->items['useAttachment']);
    } elseif ((! $letterID && ! $view->items['submit'] || $view->items['changeMethod']) && $view->items['action'] == 'sendEmail') {
        unset($view->items['usePDF']);
        unset($view->items['useAttachment']);
        $view->items['emailSubject'] = $view->items['letterContent'] = $view->items['pdfContent'] = '';
    }

    if ($view->items['submit']) {
        $placeholders = [];
        $replacements = [];
        if (! empty($view->items['placeholderList'][$view->items['searchMethod']])) {
            foreach ($view->items['placeholderList'][$view->items['searchMethod']] as $placeholders_) {
                foreach ($placeholders_ as $placeholder) {
                    $placeholders[] = '%' . $placeholder . '%';
                }
            }
        }
    }

    require_once SYSTEMPATH . '/lib/phpdocx/classes/CreateDocx.php';
    if ($view->items['action'] == 'sendEmail') {
        // check if there are conftact types selected
        if ($view->items['showContactTypes'] == 'Yes' && empty($contactTypeIDs)) {
            $validationErrors[] = 'You need to select a Contact Type';
        }

        $letter_vars = dbGetLetterTemplate($letterID);
        $view->items['letterFilename'] = $letter_vars['useDocxFile'] ? $letter_vars['letterFilename'] : '';
        if ($view->items['usePDF'] == false) {
            $view->items['pdfContent'] = '';
        }

        $attachment = [];
        $subject = $view->items['emailSubject'];
        $useAttachment = false;
        if (isset($_FILES['file']) && $view->items['useAttachment']) {
            $count = count($_FILES['file']['name']);

            for ($i = 0; $i < $count; $i++) {
                $fileName = basename($_FILES['file']['name'][$i]);
                $ext = pathinfo($fileName, PATHINFO_FILENAME);

                $moveFileName = 'emailAttachment_' . $_SESSION['user_id'] . '_' . time() . $fileName;
                $filePath = "{$pathPrefix}{$clientDirectory}/letters/Letter_Attachments/{$moveFileName}";

                if (! file_exists("{$pathPrefix}{$clientDirectory}/letters/Letter_Attachments")) {
                    mkdir("{$pathPrefix}{$clientDirectory}/letters/Letter_Attachments", FILE_PERMISSION, true);
                }

                if (move_uploaded_file($_FILES['file']['tmp_name'][$i], $filePath)) {
                    $attachment[$i]['file'] = $filePath;
                    $attachment[$i]['file_name'] = $fileName;
                    $attachment[$i]['content_type'] = mime_content_type($filePath);
                    $useAttachment = true;
                    $letter_pdf_dlPath = $filePath;
                } else {
                    $validationErrors[] = 'The file failed to upload.';
                }
            }
        }

        $parameters = [];
        if (isset($view->items['emailSpecified']) && $view->items['emailSpecified'] == 1 && isset($view->items['sendFrom'])) {
            $addlEmailConfig = dbGetEmailConfigByValue('send_from', $view->items['sendFrom']);
            if ($addlEmailConfig) {
                $parameters['sendFrom'] = $addlEmailConfig['send_from'];
                $parameters['fromName'] = $addlEmailConfig['from_name'];
                $parameters['replyTo'] = $addlEmailConfig['reply_to'];
            } else {
                $clientEmailConfig = dbGetClientEmailConfig();

                $parameters['sendFrom'] = $clientEmailConfig['fromEmail'];
                $parameters['fromName'] = $clientEmailConfig['fromName'];
                $parameters['replyTo'] = $clientEmailConfig['replyTo'];
            }
        }

        switch ($view->items['searchMethod']) {
            case 'Owner':
            case 'Owner Budget':
                if (empty($ownerIDBreak)) {
                    $validationErrors[] = 'You need to select an owner';
                    break;
                }

                if ($view->items['searchMethod'] == 'Owner Budget' && $view->items['attach_budget_report'] == 'Yes' && (empty($view->items['budgetYear']) || $view->items['budgetYear'] == 'undefined') && ! in_array('Please select a budget year.', $validationErrors)) {
                    $validationErrors[] = 'Please select a budget year.';
                }

                if (! noErrors($validationErrors)) {
                    break;
                }

                $ownerIDs = explode(',', $ownerIDBreak[0]);
                $propertyIDs = explode(',', $view->items['propertyID'][0]);

                $ownersToUse = $email_cen_send ? getCompaniesWithORWithoutEmail($ownerIDs) : getCompaniesWithEmail($ownerIDs);

                $emails_sent = [];
                foreach ($ownersToUse as $ownerList_) {
                    $ownerID = $ownerList_['pmco_code'];
                    $e_address = $ownerList_['pmco_email'];

                    $properties = dbGetOwnerPropertiesUsingOwnerID($ownerID, $propertyIDs);
                    foreach ($properties as $property) {
                        $propertyID = $property['pmos_prop'];
                        $replacements = dbGetOwnerPlaceholderReplacements(
                            $ownerID,
                            $propertyID,
                            $view->items['letterFilename']
                        );
                        if (($view->items['searchMethod'] == 'Owner Budget' && $view->items['attach_budget_report'] == 'Yes') && (! empty($view->items['budgetYear']) && $view->items['budgetYear'] !== 'undefined')) {
                            $replacements += budgetReplacementExpensesByProperty(
                                $propertyID,
                                (int) $view->items['budgetYear'],
                                $view->items['budget_type']
                            );
                        }

                        $email = new EmailTemplate('views/emails/basicEmail.html', SYSTEMURL);
                        $email->items['content'] = replacePlaceholdersAndImages(
                            $view->items['letterContent'],
                            $placeholders,
                            $replacements,
                            true
                        );
                        $subjectNew = replacePlaceholdersAndImages($subject, $placeholders, $replacements, true);

                        if ($view->items['usePDF'] && ($view->items['pdfContent'] || $view->items['letterFilename'])) { // use PDF as attachment
                            $letter_pdf_dlPath = createLetterPDF(
                                $ownerID,
                                $propertyID,
                                $view->items['pdfContent'],
                                $placeholders,
                                $replacements,
                                $view->items['useLetterhead'],
                                $view->items['letterFilename']
                            );
                            $attachment[0]['file'] = $pathPrefix . $letter_pdf_dlPath;
                            $attachment[0]['file_name'] = 'Correspondence.pdf';
                            $attachment[0]['content_type'] = 'pdf';
                        }

                        if ($view->items['searchMethod'] == 'Owner Budget' && $view->items['attach_budget_report'] == 'Yes') {
                            // Check if budget year is missing and add validation error if needed
                            if ((empty($view->items['budgetYear']) || $view->items['budgetYear'] == 'undefined')
                                && ! in_array('Please select a budget year.', $validationErrors)) {
                                $validationErrors[] = 'Please select a budget year.';
                            }

                            // Break if there are validation errors
                            if (! noErrors($validationErrors)) {
                                break;
                            }

                            $view->items['propertyID'] = $propertyID;
                            $view->items['year'] = $view->items['budgetYear'];
                            $view->items['reportType'] = $view->items['budget_type'];
                            $view->items['format'] = $view->items['budget_report_file'];
                            $view->items['colorScheme'] = $view->items['colour_scheme'];
                            $view->items['useFormula'] = $view->items['budget_use_formula'];
                            $view->items['from_api'] = 1;

                            $context = $view->items;
                            if ($view->items['budget_report_file'] == 'xlsx') {
                                if (! @include_once SYSTEMPATH . '/commands/managementReports/budgetReportProcess.php') {
                                    exit('commands/managementReports/budgetReportProcess.php');
                                }

                                $response = budgetReportProcess($context);
                                $file_details = explode('/', $response);
                                $attachment['budget'] = [
                                    'file' => $pathPrefix . $response,
                                    'file_name' => $file_details[count($file_details ?? []) - 1],
                                    'content_type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                                ];
                            } elseif ($view->items['budget_report_file'] == 'pdf') {
                                $cacert = SYSTEMPATH . cacert;
                                $params = [
                                    'user' => $_SESSION['email'],
                                    'app_key' => $_SESSION['sso_key'],
                                    'user_type' => $_SESSION['user_type'],
                                    'property_code' => $propertyID,
                                    'financial_year' => $view->items['budgetYear'],
                                    'report_type' => $view->items['budget_type'],
                                    'report_expenses_group' => $view->items['report_expenses_group'],
                                ];
                                $ch = curl_init();
                                curl_setopt(
                                    $ch,
                                    CURLOPT_URL,
                                    c8_api . 'api/property/budget/process/budget-pdf-report'
                                );
                                curl_setopt($ch, CURLOPT_HEADER, 0);
                                curl_setopt($ch, CURLOPT_CAINFO, $cacert);
                                curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($params));
                                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                                curl_setopt($ch, CURLOPT_HTTPHEADER, [
                                    'Authorization: Bearer ' . $_SESSION['sysApi'],
                                ]);
                                if (LOCAL_SSL_ENABLED) {
                                    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
                                    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
                                }

                                $data = curl_exec($ch);
                                // Check for errors
                                if ($data === false) {
                                    $validationErrors[] = 'Calling API for pdf report failed.';
                                } else {
                                    $data = json_decode($data, true);
                                    $api_file = $data['file']['file'];
                                    $file_details = explode('/', $api_file);
                                    $attachment['budget'] = [
                                        'file' => $pathPrefix . $api_file,
                                        'file_name' => $file_details[count($file_details ?? []) - 1],
                                        'content_type' => 'application/pdf',
                                    ];
                                }

                                curl_close($ch);
                            }
                        }

                        if ($email_cen_send) {
                            // change email address here for contact types
                            $emailAddress = dbGetCompanyRoleEmails($ownerID, $contactTypeIDs);
                            if (count($emailAddress ?? []) == 0) {
                                $emailAddress = dbGetPropertiesOwnersWithEmailCentralised(
                                    $propertyID,
                                    $ownerID,
                                    'OWNGEN'
                                );
                            }

                            $defaultEmailSend = '1';
                            foreach ($emailAddress as $aRowCentralisedEmailCheck) {
                                $defaultEmail = $aRowCentralisedEmailCheck['defaultEmail'];
                                if ($defaultEmail == '0') {
                                    $defaultEmailSend = $defaultEmail;
                                }
                            }

                            foreach ($emailAddress as $aRowCentralisedEmail) {
                                $emailAddressCentral = $aRowCentralisedEmail['emailAddress'];
                                $defaultEmail = $aRowCentralisedEmail['defaultEmail'];
                                if ($defaultEmailSend == $defaultEmail && isValid($emailAddressCentral, TEXT_EMAIL, false)) {
                                    // change salutations here and recreate letter
                                    $replacementsSalutation = dbGetCompanyContactSalutationForCentralizedEmail(
                                        $ownerID,
                                        $emailAddressCentral
                                    );
                                    if ($replacementsSalutation) {
                                        $replacements['salutation'] = $replacementsSalutation;
                                    }

                                    $replacementsPropertyContactSalutation = dbGetPropertyContactSalutationForCentralizedEmail(
                                        $propertyID,
                                        $emailAddressCentral
                                    );
                                    if ($replacementsPropertyContactSalutation) {
                                        $replacements['propertyContactSalutation'] = $replacementsPropertyContactSalutation;
                                    }

                                    $email->items['content'] = replacePlaceholdersAndImages(
                                        $view->items['letterContent'],
                                        $placeholders,
                                        $replacements,
                                        true
                                    );
                                    $subjectNew = replacePlaceholdersAndImages(
                                        $subject,
                                        $placeholders,
                                        $replacements,
                                        true
                                    );
                                    $trailLetter =
                                    [
                                        'letterTemplateID' => $letterTemplateID,
                                        'letterFormat' => 'pdf',
                                        'letterRecipient' => ($cc) ? $_SESSION['email'] . ';' . $emailAddressCentral : $emailAddressCentral,
                                        'letterEmailSubject' => $subjectNew,
                                        'letterEmailBody' => $view->items['letterContent'],
                                        'letterTemplateBody' => $view->items['pdfContent'],
                                        'propertyCode' => $propertyID,
                                        'leaseCode' => '',
                                        'userID' => $_SESSION['user_id'],
                                        'dl_path' => $letter_pdf_dlPath,

                                    ];
                                    $parameters['letter_history_id'] = dbInsertTrailLetter($trailLetter);
                                    sendMail(
                                        $emailAddressCentral,
                                        $emailAddressCentral,
                                        $email->toString(),
                                        $subjectNew,
                                        $attachment,
                                        null,
                                        null,
                                        false,
                                        $view->items['ccme'],
                                        true,
                                        true,
                                        $parameters
                                    );
                                    if (! in_array($emailAddressCentral, $emails_sent)) {
                                        $emails_sent[] = $emailAddressCentral;
                                    }
                                }
                            }
                        } elseif (isValid($e_address, TEXT_EMAIL, false)) {
                            $trailLetter =
                            [
                                'letterTemplateID' => $letterTemplateID,
                                'letterFormat' => 'pdf',
                                'letterRecipient' => ($cc) ? $_SESSION['email'] . ';' . $e_address : $e_address,
                                'letterEmailSubject' => $subjectNew,
                                'letterEmailBody' => $view->items['letterContent'],
                                'letterTemplateBody' => $view->items['pdfContent'],
                                'propertyCode' => $propertyID,
                                'leaseCode' => '',
                                'userID' => $_SESSION['user_id'],
                                'dl_path' => $letter_pdf_dlPath,

                            ];
                            $parameters['letter_history_id'] = dbInsertTrailLetter($trailLetter);
                            sendMail(
                                $e_address,
                                $e_address,
                                $email->toString(),
                                $subjectNew,
                                $attachment,
                                null,
                                null,
                                false,
                                $view->items['ccme'],
                                true,
                                true,
                                $parameters
                            );
                            if (! in_array($e_address, $emails_sent)) {
                                $emails_sent[] = $e_address;
                            }
                        }
                    }
                }

                break;
                // case 'Property':
                //     echo  var_dump($view->items['propertyID']);
                //     break;
            case 'Owner Company':
                if (empty($supplierIDBreak)) {
                    $validationErrors[] = 'You need to select an owner';
                    break;
                }

                $ownerIDs = explode(',', $supplierIDBreak[0]);
                $ownersToUse = $email_cen_send ? getCompaniesWithORWithoutEmail($ownerIDs) : getCompaniesWithEmail($ownerIDs);

                $emails_sent = [];

                foreach ($ownersToUse as $ownerList_) {
                    $ownerID = $ownerList_['pmco_code'];
                    $e_address = $ownerList_['pmco_email'];

                    $replacements = dbGetOwnerCompanyReplacements($ownerID);

                    $email = new EmailTemplate('views/emails/basicEmail.html', SYSTEMURL);
                    $email->items['content'] = replacePlaceholdersAndImages(
                        $view->items['letterContent'],
                        $placeholders,
                        $replacements,
                        true
                    );
                    $subjectNew = replacePlaceholdersAndImages($subject, $placeholders, $replacements, true);

                    if ($view->items['usePDF'] && ($view->items['pdfContent'] || $view->items['letterFilename'])) { // use PDF as attachment
                        $letter_pdf_dlPath = createLetterPDF(
                            'Letter',
                            $ownerID,
                            $view->items['pdfContent'],
                            $placeholders,
                            $replacements,
                            $view->items['useLetterhead'],
                            $view->items['letterFilename']
                        );
                        $attachment[0]['file'] = $pathPrefix . $letter_pdf_dlPath;
                        $attachment[0]['file_name'] = 'Correspondence.pdf';
                        $attachment[0]['content_type'] = 'pdf';
                    }

                    if ($email_cen_send) {
                        // change email address here for contact types
                        $emailAddress = dbGetCompanyRoleEmails($ownerID, $contactTypeIDs);
                        if (count($emailAddress ?? []) == 0) {
                            $emailAddress = dbGetCompanyEmailCentralised($ownerID, 'OWNGEN');
                        }

                        $defaultEmailSend = '1';
                        foreach ($emailAddress as $aRowCentralisedEmailCheck) {
                            $defaultEmail = $aRowCentralisedEmailCheck['defaultEmail'];
                            if ($defaultEmail == '0') {
                                $defaultEmailSend = $defaultEmail;
                            }
                        }

                        foreach ($emailAddress as $aRowCentralisedEmail) {
                            $emailAddressCentral = $aRowCentralisedEmail['emailAddress'];
                            $defaultEmail = $aRowCentralisedEmail['defaultEmail'];
                            if ($defaultEmailSend == $defaultEmail && isValid($emailAddressCentral, TEXT_EMAIL, false)) {
                                // change salutations here and recreate letter
                                $replacementsContactSalutation = dbGetCompanyContactSalutationForCentralizedEmail(
                                    $ownerID,
                                    $emailAddressCentral
                                );
                                if ($replacementsContactSalutation) {
                                    $replacements['contactSalutation'] = $replacementsContactSalutation;
                                }

                                $email->items['content'] = replacePlaceholdersAndImages(
                                    $view->items['letterContent'],
                                    $placeholders,
                                    $replacements,
                                    true
                                );
                                $subjectNew = replacePlaceholdersAndImages(
                                    $subject,
                                    $placeholders,
                                    $replacements,
                                    true
                                );
                                if ($view->items['usePDF'] && ($view->items['pdfContent'] || $view->items['letterFilename'])) { // use PDF as attachment
                                    $letter_pdf_dlPath = createLetterPDF(
                                        'Letter',
                                        $ownerID,
                                        $view->items['pdfContent'],
                                        $placeholders,
                                        $replacements,
                                        $view->items['useLetterhead'],
                                        $view->items['letterFilename']
                                    );
                                    $attachment[0]['file'] = $pathPrefix . $letter_pdf_dlPath;
                                    $attachment[0]['file_name'] = 'Correspondence.pdf';
                                    $attachment[0]['content_type'] = 'pdf';
                                }

                                // ---------------------------------------
                                $trailLetter =
                                [
                                    'letterTemplateID' => $letterTemplateID,
                                    'letterFormat' => 'pdf',
                                    'letterRecipient' => ($cc) ? $_SESSION['email'] . ';' . $emailAddressCentral : $emailAddressCentral,
                                    'letterEmailSubject' => $subjectNew,
                                    'letterEmailBody' => $view->items['letterContent'],
                                    'letterTemplateBody' => $view->items['pdfContent'],
                                    'propertyCode' => $propertyID,
                                    'leaseCode' => '',
                                    'userID' => $_SESSION['user_id'],
                                    'dl_path' => $letter_pdf_dlPath,
                                ];
                                $parameters['letter_history_id'] = dbInsertTrailLetter($trailLetter);
                                sendMail(
                                    $emailAddressCentral,
                                    $emailAddressCentral,
                                    $email->toString(),
                                    $subjectNew,
                                    $attachment,
                                    null,
                                    null,
                                    false,
                                    $view->items['ccme'],
                                    true,
                                    true,
                                    $parameters
                                );
                                if (! in_array($emailAddressCentral, $emails_sent)) {
                                    $emails_sent[] = $emailAddressCentral;
                                }
                            }
                        }
                    } elseif (isValid($e_address, TEXT_EMAIL, false)) {
                        $trailLetter =
                        [
                            'letterTemplateID' => $letterTemplateID,
                            'letterFormat' => 'pdf',
                            'letterRecipient' => ($cc) ? $_SESSION['email'] . ';' . $e_address : $e_address,
                            'letterEmailSubject' => $subjectNew,
                            'letterEmailBody' => $view->items['letterContent'],
                            'letterTemplateBody' => $view->items['pdfContent'],
                            'propertyCode' => $propertyID,
                            'leaseCode' => '',
                            'userID' => $_SESSION['user_id'],
                            'dl_path' => $letter_pdf_dlPath,
                        ];
                        $parameters['letter_history_id'] = dbInsertTrailLetter($trailLetter);
                        sendMail(
                            $e_address,
                            $e_address,
                            $email->toString(),
                            $subjectNew,
                            $attachment,
                            null,
                            null,
                            false,
                            $view->items['ccme'],
                            true,
                            true,
                            $parameters
                        );
                        if (! in_array($e_address, $emails_sent)) {
                            $emails_sent[] = $e_address;
                        }
                    }
                }

                break;
            case 'Supplier':
                // echo  var_dump($view->items['supplierID']);
                if (empty($supplierIDBreak)) {
                    $validationErrors[] = 'You need to select a Supplier';
                    break;
                }

                $suppIDs = explode(',', $supplierIDBreak[0]);
                $suppliersToUse = $email_cen_send ? getCompaniesWithORWithoutEmail($suppIDs) : getCompaniesWithEmail($suppIDs);

                $emails_sent = [];

                foreach ($suppliersToUse as $supplierList_) {
                    $suppID = $supplierList_['pmco_code'];
                    $e_address = $supplierList_['pmco_email'];

                    $replacements = dbGetSupplierReplacements($suppID);

                    $email = new EmailTemplate('views/emails/basicEmail.html', SYSTEMURL);
                    $email->items['content'] = replacePlaceholdersAndImages(
                        $view->items['letterContent'],
                        $placeholders,
                        $replacements,
                        true
                    );
                    $subjectNew = replacePlaceholdersAndImages($subject, $placeholders, $replacements, true);

                    if ($view->items['usePDF'] && ($view->items['pdfContent'] || $view->items['letterFilename'])) { // use PDF as attachment
                        $letter_pdf_dlPath = createLetterPDF(
                            'Letter',
                            $suppID,
                            $view->items['pdfContent'],
                            $placeholders,
                            $replacements,
                            $view->items['useLetterhead'],
                            $view->items['letterFilename']
                        );
                        $attachment[0]['file'] = $pathPrefix . $letter_pdf_dlPath;
                        $attachment[0]['file_name'] = 'Correspondence.pdf';
                        $attachment[0]['content_type'] = 'pdf';
                    }

                    if ($email_cen_send) {
                        // change email address here for contact types
                        $emailAddress = dbGetCompanyRoleEmails($suppID, $contactTypeIDs);
                        if (count($emailAddress ?? []) == 0) {
                            $emailAddress = dbGetCompanyEmailCentralised($suppID, 'SUPGEN');
                        }

                        $defaultEmailSend = '1';
                        foreach ($emailAddress as $aRowCentralisedEmailCheck) {
                            $defaultEmail = $aRowCentralisedEmailCheck['defaultEmail'];
                            if ($defaultEmail == '0') {
                                $defaultEmailSend = $defaultEmail;
                            }
                        }

                        foreach ($emailAddress as $aRowCentralisedEmail) {
                            $emailAddressCentral = $aRowCentralisedEmail['emailAddress'];
                            $defaultEmail = $aRowCentralisedEmail['defaultEmail'];
                            if ($defaultEmailSend == $defaultEmail && isValid($emailAddressCentral, TEXT_EMAIL, false)) {
                                // change salutations here and recreate letter
                                $replacementsContactSalutation = dbGetCompanyContactSalutationForCentralizedEmail(
                                    $suppID,
                                    $emailAddressCentral
                                );
                                if ($replacementsContactSalutation) {
                                    $replacements['contactSalutation'] = $replacementsContactSalutation;
                                }

                                $email->items['content'] = replacePlaceholdersAndImages(
                                    $view->items['letterContent'],
                                    $placeholders,
                                    $replacements,
                                    true
                                );
                                $subjectNew = replacePlaceholdersAndImages(
                                    $subject,
                                    $placeholders,
                                    $replacements,
                                    true
                                );
                                if ($view->items['usePDF'] && ($view->items['pdfContent'] || $view->items['letterFilename'])) { // use PDF as attachment
                                    $letter_pdf_dlPath = createLetterPDF(
                                        'Letter',
                                        $suppID,
                                        $view->items['pdfContent'],
                                        $placeholders,
                                        $replacements,
                                        $view->items['useLetterhead'],
                                        $view->items['letterFilename']
                                    );
                                    $attachment[0]['file'] = $pathPrefix . $letter_pdf_dlPath;
                                    $attachment[0]['file_name'] = 'Correspondence.pdf';
                                    $attachment[0]['content_type'] = 'pdf';
                                }

                                // ---------------------------------------
                                $trailLetter =
                                [
                                    'letterTemplateID' => $letterTemplateID,
                                    'letterFormat' => 'pdf',
                                    'letterRecipient' => ($cc) ? $_SESSION['email'] . ';' . $emailAddressCentral : $emailAddressCentral,
                                    'letterEmailSubject' => $subjectNew,
                                    'letterEmailBody' => $view->items['letterContent'],
                                    'letterTemplateBody' => $view->items['pdfContent'],
                                    'propertyCode' => $propertyID,
                                    'leaseCode' => '',
                                    'userID' => $_SESSION['user_id'],
                                    'dl_path' => $letter_pdf_dlPath,
                                ];
                                $parameters['letter_history_id'] = dbInsertTrailLetter($trailLetter);
                                sendMail(
                                    $emailAddressCentral,
                                    $emailAddressCentral,
                                    $email->toString(),
                                    $subjectNew,
                                    $attachment,
                                    null,
                                    null,
                                    false,
                                    $view->items['ccme'],
                                    true,
                                    true,
                                    $parameters
                                );
                                if (! in_array($emailAddressCentral, $emails_sent)) {
                                    $emails_sent[] = $emailAddressCentral;
                                }
                            }
                        }
                    } elseif (isValid($e_address, TEXT_EMAIL, false)) {
                        $trailLetter =
                        [
                            'letterTemplateID' => $letterTemplateID,
                            'letterFormat' => 'pdf',
                            'letterRecipient' => ($cc) ? $_SESSION['email'] . ';' . $e_address : $e_address,
                            'letterEmailSubject' => $subjectNew,
                            'letterEmailBody' => $view->items['letterContent'],
                            'letterTemplateBody' => $view->items['pdfContent'],
                            'propertyCode' => $propertyID,
                            'leaseCode' => '',
                            'userID' => $_SESSION['user_id'],
                            'dl_path' => $letter_pdf_dlPath,
                        ];
                        $parameters['letter_history_id'] = dbInsertTrailLetter($trailLetter);
                        sendMail(
                            $e_address,
                            $e_address,
                            $email->toString(),
                            $subjectNew,
                            $attachment,
                            null,
                            null,
                            false,
                            $view->items['ccme'],
                            true,
                            true,
                            $parameters
                        );
                        if (! in_array($e_address, $emails_sent)) {
                            $emails_sent[] = $e_address;
                        }
                    }
                }

                break;
            case 'Tenant':
            case 'Tenant Diary':

                if (empty($tenantIDBreak)) {
                    $validationErrors[] = 'You need to select a Tenant';
                    break;
                }

                $prop_tenantIDs = explode(',', $tenantIDBreak[0]);

                if ($email_cen_send) {
                    $prop_tenantsToUse = getTenantsWithOrWithoutEmail($prop_tenantIDs);
                } else {
                    $prop_tenantsToUse = getTenantsWithEmail($prop_tenantIDs);
                }

                $emails_sent = [];
                foreach ($prop_tenantsToUse as $prop_tenantDetails) {
                    $propertyID = $prop_tenantDetails['pmle_prop'];
                    $leaseID = $prop_tenantDetails['leaseID'];
                    $e_address = $prop_tenantDetails['pmco_email'];

                    $replacements = dbGetTenantReplacements($propertyID, $leaseID, $view->items['letterFilename']);
                    $diary_desc = dbGetTenantDiaryText(
                        $propertyID,
                        $leaseID,
                        $view->items['fromDate'],
                        $view->items['toDate'],
                        $view->items['diary_type']
                    );
                    $replacements['diaryDescription'] = $diary_desc['diary_text'];
                    $email = new EmailTemplate('views/emails/basicEmail.html', SYSTEMURL);
                    $email->items['content'] = replacePlaceholdersAndImages(
                        $view->items['letterContent'],
                        $placeholders,
                        $replacements,
                        true
                    );
                    $subjectNew = replacePlaceholdersAndImages($subject, $placeholders, $replacements, true);

                    if ($view->items['usePDF'] && ($view->items['pdfContent'] || $view->items['letterFilename'])) { // use PDF as attachment
                        $letter_pdf_dlPath = createLetterPDF(
                            $propertyID,
                            $leaseID,
                            $view->items['pdfContent'],
                            $placeholders,
                            $replacements,
                            $view->items['useLetterhead'],
                            $view->items['letterFilename']
                        );
                        $attachment[0]['file'] = $pathPrefix . $letter_pdf_dlPath;
                        $attachment[0]['file_name'] = 'Correspondence.pdf';
                        $attachment[0]['content_type'] = 'pdf';
                    }

                    if ($email_cen_send) {
                        // change email address here for contact types
                        $emailAddress = dbGetLeaseRoleEmails($propertyID, $leaseID, $contactTypeIDs);
                        if (count($emailAddress ?? []) == 0) {
                            $emailAddress = dbGetDebtorEmailCentralised($propertyID, $leaseID, 'TENGEN');
                        }

                        $defaultEmailSend = '1';
                        foreach ($emailAddress as $aRowCentralisedEmailCheck) {
                            $defaultEmail = $aRowCentralisedEmailCheck['defaultEmail'];
                            if ($defaultEmail == '0') {
                                $defaultEmailSend = $defaultEmail;
                            }
                        }

                        foreach ($emailAddress as $aRowCentralisedEmail) {
                            $emailAddressCentral = $aRowCentralisedEmail['emailAddress'];
                            $defaultEmail = $aRowCentralisedEmail['defaultEmail'];
                            if ($defaultEmailSend == $defaultEmail && isValid($emailAddressCentral, TEXT_EMAIL, false)) {
                                // change salutations here and recreate letter
                                $replacementsLeaseContactSalutation = dbGetLeaseContactSalutationForCentralizedEmail(
                                    $propertyID,
                                    $leaseID,
                                    $emailAddressCentral
                                );
                                if ($replacementsLeaseContactSalutation) {
                                    $replacements['leaseContactSalutation'] = $replacementsLeaseContactSalutation;
                                }

                                $email->items['content'] = replacePlaceholdersAndImages(
                                    $view->items['letterContent'],
                                    $placeholders,
                                    $replacements,
                                    true
                                );
                                $subjectNew = replacePlaceholdersAndImages(
                                    $subject,
                                    $placeholders,
                                    $replacements,
                                    true
                                );
                                if ($view->items['usePDF'] && ($view->items['pdfContent'] || $view->items['letterFilename'])) { // use PDF as attachment
                                    $letter_pdf_dlPath = createLetterPDF(
                                        $propertyID,
                                        $leaseID,
                                        $view->items['pdfContent'],
                                        $placeholders,
                                        $replacements,
                                        $view->items['useLetterhead'],
                                        $view->items['letterFilename']
                                    );
                                    $attachment[0]['file'] = $pathPrefix . $letter_pdf_dlPath;
                                    $attachment[0]['file_name'] = 'Correspondence.pdf';
                                    $attachment[0]['content_type'] = 'pdf';
                                }

                                // ---------------------------------------
                                $trailLetter =
                                [
                                    'letterTemplateID' => $letterTemplateID,
                                    'letterFormat' => 'pdf',
                                    'letterRecipient' => ($cc) ? $_SESSION['email'] . ';' . $emailAddressCentral : $emailAddressCentral,
                                    'letterEmailSubject' => $subjectNew,
                                    'letterEmailBody' => $view->items['letterContent'],
                                    'letterTemplateBody' => $view->items['pdfContent'],
                                    'propertyCode' => $propertyID,
                                    'leaseCode' => $leaseID,
                                    'userID' => $_SESSION['user_id'],
                                    'dl_path' => $letter_pdf_dlPath,

                                ];
                                $parameters['letter_history_id'] = dbInsertTrailLetter($trailLetter);
                                sendMail(
                                    $emailAddressCentral,
                                    $emailAddressCentral,
                                    $email->toString(),
                                    $subjectNew,
                                    $attachment,
                                    null,
                                    null,
                                    false,
                                    $view->items['ccme'],
                                    true,
                                    true,
                                    $parameters
                                );
                                if (! in_array($emailAddressCentral, $emails_sent)) {
                                    $emails_sent[] = $emailAddressCentral;
                                }
                            }
                        }
                    } elseif (isValid($e_address, TEXT_EMAIL, false)) {
                        $trailLetter =
                        [
                            'letterTemplateID' => $letterTemplateID,
                            'letterFormat' => 'pdf',
                            'letterRecipient' => ($cc) ? $_SESSION['email'] . ';' . $e_address : $e_address,
                            'letterEmailSubject' => $subjectNew,
                            'letterEmailBody' => $view->items['letterContent'],
                            'letterTemplateBody' => $view->items['pdfContent'],
                            'propertyCode' => $propertyID,
                            'leaseCode' => $leaseID,
                            'userID' => $_SESSION['user_id'],
                            'dl_path' => $letter_pdf_dlPath,

                        ];
                        $parameters['letter_history_id'] = dbInsertTrailLetter($trailLetter);
                        sendMail(
                            $e_address,
                            $e_address,
                            $email->toString(),
                            $subjectNew,
                            $attachment,
                            null,
                            null,
                            false,
                            $view->items['ccme'],
                            true,
                            true,
                            $parameters
                        );
                        if (! in_array($e_address, $emails_sent)) {
                            $emails_sent[] = $e_address;
                        }
                    }
                }

                break;
            case 'Budget':
                if (empty($tenantIDBreak)) {
                    $validationErrors[] = 'You need to select a Tenant';
                }

                if (empty($view->items['budgetYear'])) {
                    $validationErrors[] = 'You need to select a Budget year';
                }

                if (empty($view->items['budget_type'])) {
                    $validationErrors[] = 'You need to select a Budget type';
                }

                if (count($validationErrors ?? []) > 0) {
                    //                    http_response_code(404);
                    break;
                }

                $prop_tenantIDs = explode(',', $tenantIDBreak[0]);
                if ($email_cen_send) {
                    $prop_tenantsToUse = getTenantsWithOrWithoutEmail($prop_tenantIDs);
                } else {
                    $prop_tenantsToUse = getTenantsWithEmail($prop_tenantIDs);
                }

                $emails_sent = [];

                foreach ($prop_tenantsToUse as $prop_tenantDetails) {
                    $propertyID = $prop_tenantDetails['pmle_prop'];
                    $leaseID = $prop_tenantDetails['leaseID'];
                    $e_address = $prop_tenantDetails['pmco_email'];
                    //                    $replacements = dbGetTenantReplacements($propertyID,$leaseID);

                    $replacements = dbGetBudgetReplacements(
                        $propertyID,
                        $leaseID,
                        $view->items['budgetYear'],
                        $view->items['budget_type'],
                        $view->items['letterFilename']
                    );

                    $page2Content = renderSecondPageContent(
                        $propertyID,
                        $leaseID,
                        $view->items['budgetYear'],
                        $view->items['budget_type'],
                        $view->items['tenant_detail'],
                        $view->items['budget_types'][$view->items['budget_type']],
                        $view->items['showAnnualPropertyBudget']
                    );


                    $email = new EmailTemplate('views/emails/basicEmail.html', SYSTEMURL);
                    $email->items['content'] = replacePlaceholdersAndImages(
                        $view->items['letterContent'],
                        $placeholders,
                        $replacements,
                        true
                    );
                    $subjectNew = replacePlaceholdersAndImages($subject, $placeholders, $replacements, true);
                    //                    var_dump($subjectNew);
                    if ($view->items['usePDF'] && ($view->items['pdfContent'] || $view->items['letterFilename'])) { // use PDF as attachment
                        //                        $letter_pdf_dlPath = createLetterPDF($propertyID,$leaseID,$view->items['pdfContent'], $placeholders, $replacements);
                        $letter_pdf_dlPath = createLetterPDFBudget(
                            $propertyID,
                            $leaseID,
                            $view->items['pdfContent'],
                            $page2Content,
                            $placeholders,
                            $replacements,
                            $view->items['useLetterhead'],
                            $view->items['letterFilename']
                        );
                        $attachment[0]['file'] = $pathPrefix . $letter_pdf_dlPath;
                        $attachment[0]['file_name'] = 'Budget Letter.pdf';
                        $attachment[0]['content_type'] = 'pdf';
                    }

                    if ($email_cen_send) {
                        // change email address here for contact types
                        $emailAddress = dbGetLeaseRoleEmails($propertyID, $leaseID, $contactTypeIDs);
                        if (count($emailAddress ?? []) == 0) {
                            $emailAddress = dbGetDebtorEmailCentralised($propertyID, $leaseID, 'TENBUDGET');
                        }

                        $defaultEmailSend = '1';
                        foreach ($emailAddress as $aRowCentralisedEmailCheck) {
                            $defaultEmail = $aRowCentralisedEmailCheck['defaultEmail'];
                            if ($defaultEmail == '0') {
                                $defaultEmailSend = $defaultEmail;
                            }
                        }

                        foreach ($emailAddress as $aRowCentralisedEmail) {
                            $emailAddressCentral = $aRowCentralisedEmail['emailAddress'];
                            $defaultEmail = $aRowCentralisedEmail['defaultEmail'];
                            if ($defaultEmailSend == $defaultEmail && isValid($emailAddressCentral, TEXT_EMAIL, false)) {
                                // change salutations here and recreate letter
                                $replacementsLeaseContactSalutation = dbGetLeaseContactSalutationForCentralizedEmail(
                                    $propertyID,
                                    $leaseID,
                                    $emailAddressCentral
                                );
                                if ($replacementsLeaseContactSalutation) {
                                    $replacements['leaseContactSalutation'] = $replacementsLeaseContactSalutation;
                                }

                                $email->items['content'] = replacePlaceholdersAndImages(
                                    $view->items['letterContent'],
                                    $placeholders,
                                    $replacements,
                                    true
                                );
                                $subjectNew = replacePlaceholdersAndImages(
                                    $subject,
                                    $placeholders,
                                    $replacements,
                                    true
                                );
                                if ($view->items['usePDF'] && ($view->items['pdfContent'] || $view->items['letterFilename'])) { // use PDF as attachment
                                    $letter_pdf_dlPath = createLetterPDFBudget(
                                        $propertyID,
                                        $leaseID,
                                        $view->items['pdfContent'],
                                        $page2Content,
                                        $placeholders,
                                        $replacements,
                                        $view->items['useLetterhead'],
                                        $view->items['letterFilename']
                                    );
                                    $attachment[0]['file'] = $pathPrefix . $letter_pdf_dlPath;
                                    $attachment[0]['file_name'] = 'Budget Letter.pdf';
                                    $attachment[0]['content_type'] = 'pdf';
                                }

                                // ---------------------------------------
                                $trailLetter =
                                [
                                    'letterTemplateID' => $letterTemplateID,
                                    'letterFormat' => 'pdf',
                                    'letterRecipient' => ($cc) ? $_SESSION['email'] . ';' . $emailAddressCentral : $emailAddressCentral,
                                    'letterEmailSubject' => $subjectNew,
                                    'letterEmailBody' => $view->items['letterContent'],
                                    'letterTemplateBody' => $view->items['pdfContent'],
                                    'propertyCode' => $propertyID,
                                    'leaseCode' => $leaseID,
                                    'userID' => $_SESSION['user_id'],
                                    'dl_path' => $letter_pdf_dlPath,

                                ];
                                $parameters['letter_history_id'] = dbInsertTrailLetter($trailLetter);
                                sendMail(
                                    $emailAddressCentral,
                                    $emailAddressCentral,
                                    $email->toString(),
                                    $subjectNew,
                                    $attachment,
                                    null,
                                    null,
                                    false,
                                    $view->items['ccme'],
                                    true,
                                    true,
                                    $parameters
                                );
                                if (! in_array($emailAddressCentral, $emails_sent)) {
                                    $emails_sent[] = $emailAddressCentral;
                                }
                            }
                        }
                    } elseif (isValid($e_address, TEXT_EMAIL, false)) {
                        $trailLetter =
                        [
                            'letterTemplateID' => $letterTemplateID,
                            'letterFormat' => 'pdf',
                            'letterRecipient' => ($cc) ? $_SESSION['email'] . ';' . $e_address : $e_address,
                            'letterEmailSubject' => $subjectNew,
                            'letterEmailBody' => $view->items['letterContent'],
                            'letterTemplateBody' => $view->items['pdfContent'],
                            'propertyCode' => $propertyID,
                            'leaseCode' => $leaseID,
                            'userID' => $_SESSION['user_id'],
                            'dl_path' => $letter_pdf_dlPath,

                        ];
                        $parameters['letter_history_id'] = dbInsertTrailLetter($trailLetter);
                        sendMail(
                            $e_address,
                            $e_address,
                            $email->toString(),
                            $subjectNew,
                            $attachment,
                            null,
                            null,
                            false,
                            $view->items['ccme'],
                            true,
                            true,
                            $parameters
                        );
                        if (! in_array($e_address, $emails_sent)) {
                            $emails_sent[] = $e_address;
                        }
                    }
                }


                // foreach($prop_tenantsToUse as $prop_tenantDetails)
                // {
                //     $propertyID = $prop_tenantDetails['pmle_prop'];
                //     $leaseID = $prop_tenantDetails['leaseID'];
                //     $e_address = $prop_tenantDetails['pmco_email'];
                //     // $replacements = dbGetTenantReplacements($propertyID,$leaseID);

                // }
                break;
        }

        if (noErrors($validationErrors)) {
            if (count($emails_sent ?? []) > 0) {
                $str = implode(';', $emails_sent);
                echo $str;
            }
        } else {
            $json = ['validationErrors' => $validationErrors];
            echo json_encode($json);
        }

        exit();
    } elseif ($view->items['action'] == 'dlAll' || $view->items['action'] == 'dlAllNoEmail') {
        $excludeWithEmail = false;
        if ($view->items['action'] == 'dlAllNoEmail') {
            $excludeWithEmail = true;
        }

        $pdf_files = [];
        $docx_files = [];
        switch ($view->items['searchMethod']) {
            case 'Owner':
            case 'Owner Budget':
                if (empty($ownerIDBreak)) {
                    $validationErrors[] = 'You need to select an owner';
                    break;
                }

                $ownerIDs = explode(',', $ownerIDBreak[0]);
                $ownersToUse = $ownerIDBreak;
                if ($email_cen_send) {
                    if ($excludeWithEmail) {
                        $ownersToUse = getCompaniesWithoutEmailCentralisation(
                            $ownerIDs,
                            $view->items['propertyID']
                        );
                    }
                } elseif ($excludeWithEmail) {
                    $ownersToUse = getCompaniesWithoutEmail($ownerIDs);
                }

                foreach ($ownersToUse as $ownerID) {
                    $properties = dbGetOwnerPropertiesUsingOwnerID($ownerID, $view->items['propertyID']);
                    foreach ($properties as $property) {
                        $propertyID = $property['pmos_prop'];

                        $replacements = dbGetOwnerPlaceholderReplacements(
                            $property['pmos_owner'],
                            $propertyID,
                            $view->items['letterFilename']
                        );
                        if (($view->items['searchMethod'] == 'Owner Budget' && $view->items['attach_budget_report'] == 'Yes') && (! empty($view->items['budgetYear']) && $view->items['budgetYear'] !== 'undefined')) {
                            $replacements += budgetReplacementExpensesByProperty(
                                $propertyID,
                                (int) $view->items['budgetYear'],
                                $view->items['budget_type']
                            );
                        }

                        $letter_pdf_dlPath = createLetterPDF(
                            $property['pmos_owner'],
                            $propertyID,
                            $view->items['pdfContent'],
                            $placeholders,
                            $replacements,
                            $view->items['useLetterhead'],
                            $view->items['letterFilename']
                        );
                        $path_info = pathinfo($letter_pdf_dlPath);
                        $pdf_files[] = $path_info['basename'];
                        $docx_files[] = str_replace('.pdf', '.docx', $path_info['basename']);

                        if ($path_info['basename'] !== '' && $path_info['basename'] !== '0') {
                            $trailLetter =
                            [
                                'letterTemplateID' => $view->items['letterID'],
                                'letterFormat' => 'pdf',
                                'letterRecipient' => '',
                                'letterEmailSubject' => '',
                                'letterEmailBody' => '',
                                'letterTemplateBody' => $view->items['pdfContent'],
                                'propertyCode' => $propertyID,
                                'leaseCode' => '',
                                'userID' => $_SESSION['user_id'],
                                'dl_path' => $letter_pdf_dlPath,

                            ];
                            $letter_history_id = dbInsertTrailLetter($trailLetter);
                        }
                    }
                }

                break;
                // case 'Property':
                //     echo  var_dump($view->items['propertyID']);
                //     break;
            case 'Supplier':
                if (empty($supplierIDBreak)) {
                    $validationErrors[] = 'You need to select a Supplier';
                    break;
                }

                $suppIDs = explode(',', $supplierIDBreak[0]);
                $suppliersToUse = $supplierIDBreak;

                if ($email_cen_send) {
                    if ($excludeWithEmail) {
                        $suppliersToUse = getSupplierWithoutEmailCentralisation($suppIDs);
                    }
                } elseif ($excludeWithEmail) {
                    $suppliersToUse = getCompaniesWithoutEmail($suppIDs);
                }

                foreach ($suppliersToUse as $suppID) {
                    $replacements = dbGetSupplierReplacements($suppID);
                    $letter_pdf_dlPath = createLetterPDF(
                        'Letter',
                        $suppID,
                        $view->items['pdfContent'],
                        $placeholders,
                        $replacements,
                        $view->items['useLetterhead'],
                        $view->items['letterFilename']
                    );
                    $path_info = pathinfo($letter_pdf_dlPath);
                    $pdf_files[] = $path_info['basename'];
                    $docx_files[] = str_replace('.pdf', '.docx', $path_info['basename']);

                    if ($path_info['basename'] !== '' && $path_info['basename'] !== '0') {
                        $trailLetter =
                        [
                            'letterTemplateID' => $view->items['letterID'],
                            'letterFormat' => 'pdf',
                            'letterRecipient' => '',
                            'letterEmailSubject' => '',
                            'letterEmailBody' => '',
                            'letterTemplateBody' => $view->items['pdfContent'],
                            'propertyCode' => '',
                            'leaseCode' => '',
                            'userID' => $_SESSION['user_id'],
                            'dl_path' => $letter_pdf_dlPath,

                        ];
                        $letter_history_id = dbInsertTrailLetter($trailLetter);
                    }
                }

                break;
            case 'Owner Company':
                if (empty($supplierIDBreak)) {
                    $validationErrors[] = 'You need to select an owner';
                    break;
                }

                $suppIDs = explode(',', $supplierIDBreak[0]);
                if (isset($view->items['from_api'])) {
                    $suppIDs = $supplierIDBreak;
                }

                $suppliersToUse = $supplierIDBreak;

                if ($email_cen_send) {
                    if ($excludeWithEmail) {
                        $suppliersToUse = getOwnerCompanyWithoutEmailCentralisation($suppIDs);
                    }
                } elseif ($excludeWithEmail) {
                    $suppliersToUse = getCompaniesWithoutEmail($supplierIDBreak);
                }

                foreach ($suppliersToUse as $ownerID) {
                    $replacements = dbGetOwnerCompanyReplacements($ownerID);
                    $letter_pdf_dlPath = createLetterPDF(
                        'Letter',
                        $ownerID,
                        $view->items['pdfContent'],
                        $placeholders,
                        $replacements,
                        $view->items['useLetterhead'],
                        $view->items['letterFilename']
                    );
                    $path_info = pathinfo($letter_pdf_dlPath);
                    $pdf_files[] = $path_info['basename'];
                    $docx_files[] = str_replace('.pdf', '.docx', $path_info['basename']);

                    if ($path_info['basename'] !== '' && $path_info['basename'] !== '0') {
                        $trailLetter =
                        [
                            'letterTemplateID' => $view->items['letterID'],
                            'letterFormat' => 'pdf',
                            'letterRecipient' => '',
                            'letterEmailSubject' => '',
                            'letterEmailBody' => '',
                            'letterTemplateBody' => $view->items['pdfContent'],
                            'propertyCode' => '',
                            'leaseCode' => '',
                            'userID' => $_SESSION['user_id'],
                            'dl_path' => $letter_pdf_dlPath,

                        ];
                        $letter_history_id = dbInsertTrailLetter($trailLetter);
                    }
                }

                break;
            case 'Tenant':
                // echo  var_dump($tenantIDBreak);
                if (empty($tenantIDBreak)) {
                    $validationErrors[] = 'You need to select a Tenant';
                    break;
                }

                // $prop_tenantsToUse = $tenantIDBreak;
                $prop_tenantsToUse = explode(',', $tenantIDBreak[0]);
                // if($email_cen_send){
                //     $prop_tenantsToUse = getTenantsWithOrWithoutEmail($prop_tenantIDs);
                // } else {
                //     $prop_tenantsToUse = getTenantsWithEmail($prop_tenantIDs);
                // }

                if ($email_cen_send) {
                    if ($excludeWithEmail) {
                        $prop_tenantsToUse = getTenantsWithoutEmailCentralisation($prop_tenantsToUse, 'TENGEN');
                    } else {
                        $prop_tenantsToUse = getTenantsWithEmail($prop_tenantsToUse);
                    }
                } elseif ($excludeWithEmail) {
                    $prop_tenantsToUse = getTenantsWithoutEmail($prop_tenantsToUse);
                } else {
                    $prop_tenantsToUse = getTenantsWithEmail($prop_tenantsToUse);
                }

                foreach ($prop_tenantsToUse as $prop_tenantDetails) {
                    // if ($excludeWithEmail)
                    // {
                    $propertyID = $prop_tenantDetails['pmle_prop'];
                    $leaseID = $prop_tenantDetails['leaseID'];
                    // }
                    // else
                    // {
                    //     list($propertyID,$leaseID) = explode(' - ',$prop_tenantDetails);
                    // }

                    $replacements = dbGetTenantReplacements($propertyID, $leaseID, $view->items['letterFilename']);
                    $letter_pdf_dlPath = createLetterPDF(
                        $propertyID,
                        $leaseID,
                        $view->items['pdfContent'],
                        $placeholders,
                        $replacements,
                        $view->items['useLetterhead'],
                        $view->items['letterFilename']
                    );
                    $path_info = pathinfo($letter_pdf_dlPath);
                    $pdf_files[] = $path_info['basename'];
                    $docx_files[] = str_replace('.pdf', '.docx', $path_info['basename']);

                    if ($path_info['basename'] !== '' && $path_info['basename'] !== '0') {
                        $trailLetter =
                        [
                            'letterTemplateID' => $view->items['letterID'],
                            'letterFormat' => 'pdf',
                            'letterRecipient' => '',
                            'letterEmailSubject' => '',
                            'letterEmailBody' => '',
                            'letterTemplateBody' => $view->items['pdfContent'],
                            'propertyCode' => $propertyID,
                            'leaseCode' => $leaseID,
                            'userID' => $_SESSION['user_id'],
                            'dl_path' => $letter_pdf_dlPath,

                        ];
                        $letter_history_id = dbInsertTrailLetter($trailLetter);
                    }
                }

                break;
            case 'Tenant Diary':
                if (empty($tenantIDBreak)) {
                    $validationErrors[] = 'You need to select a Tenant';
                    break;
                }

                $prop_tenantsToUse = explode(',', $tenantIDBreak[0]);

                if ($email_cen_send) {
                    if ($excludeWithEmail) {
                        $prop_tenantsToUse = getTenantsWithoutEmailCentralisation($prop_tenantsToUse, 'TENGEN');
                    } else {
                        $prop_tenantsToUse = getTenantsWithEmail($prop_tenantsToUse);
                    }
                } elseif ($excludeWithEmail) {
                    $prop_tenantsToUse = getTenantsWithoutEmail($prop_tenantsToUse);
                } else {
                    $prop_tenantsToUse = getTenantsWithOrWithoutEmail($prop_tenantsToUse);
                }

                foreach ($prop_tenantsToUse as $prop_tenantDetails) {
                    //                    if ($excludeWithEmail)
                    //                    {
                    $propertyID = $prop_tenantDetails['pmle_prop'];
                    $leaseID = $prop_tenantDetails['leaseID'];
                    //                    }
                    //                    else
                    //                    {
                    //                      list($propertyID,$leaseID) = explode(' - ',$prop_tenantDetails);
                    //                    }

                    $replacements = dbGetTenantReplacements($propertyID, $leaseID, $view->items['letterFilename']);
                    $diary_desc = dbGetTenantDiaryText(
                        $propertyID,
                        $leaseID,
                        $view->items['fromDate'],
                        $view->items['toDate'],
                        $view->items['diary_type']
                    );
                    $replacements['diaryDescription'] = $diary_desc['diary_text'];
                    $letter_pdf_dlPath = createLetterPDF(
                        $propertyID,
                        $leaseID,
                        $view->items['pdfContent'],
                        $placeholders,
                        $replacements,
                        $view->items['useLetterhead'],
                        $view->items['letterFilename']
                    );
                    $path_info = pathinfo($letter_pdf_dlPath);
                    $pdf_files[] = $path_info['basename'];
                    $docx_files[] = str_replace('.pdf', '.docx', $path_info['basename']);

                    if ($path_info['basename'] !== '' && $path_info['basename'] !== '0') {
                        $trailLetter =
                        [
                            'letterTemplateID' => $view->items['letterID'],
                            'letterFormat' => 'pdf',
                            'letterRecipient' => '',
                            'letterEmailSubject' => '',
                            'letterEmailBody' => '',
                            'letterTemplateBody' => $view->items['pdfContent'],
                            'propertyCode' => $propertyID,
                            'leaseCode' => $leaseID,
                            'userID' => $_SESSION['user_id'],
                            'dl_path' => $letter_pdf_dlPath,

                        ];
                        $letter_history_id = dbInsertTrailLetter($trailLetter);
                    }
                }

                break;
            case 'Budget':

                if (empty($tenantIDBreak)) {
                    $validationErrors[] = 'You need to select a Tenant';
                    break;
                }

                if (empty($view->items['budgetYear'])) {
                    $validationErrors[] = 'You need to select a Budget year';
                    break;
                }

                if (empty($view->items['budget_type'])) {
                    $validationErrors[] = 'You need to select a Budget type';
                    break;
                }

                $prop_tenantsToUse = explode(',', $tenantIDBreak[0]);

                // if ($excludeWithEmail)
                // $prop_tenantsToUse  = getTenantsWithoutEmail($prop_tenantsToUse);
                if ($email_cen_send) {
                    if ($excludeWithEmail) {
                        $prop_tenantsToUse = getTenantsWithoutEmailCentralisation($prop_tenantsToUse, 'TENBUDGET');
                    } else {
                        $prop_tenantsToUse = getTenantsAll($prop_tenantsToUse);
                    }
                } elseif ($excludeWithEmail) {
                    $prop_tenantsToUse = getTenantsWithoutEmail($prop_tenantsToUse);
                } else {
                    $prop_tenantsToUse = getTenantsAll($prop_tenantsToUse);
                }

                // $ownerIDs = explode(',',$ownerIDBreak[0]);
                // $ownersToUse = $ownerIDBreak;
                // if ($excludeWithEmail)
                //     $ownersToUse = getCompaniesWithoutEmail($ownerIDs);
                // var_dump($prop_tenantsToUse);
                foreach ($prop_tenantsToUse as $prop_tenantDetails) {
                    // if ($excludeWithEmail)
                    // {
                    $propertyID = $prop_tenantDetails['pmle_prop'];
                    $leaseID = $prop_tenantDetails['leaseID'];
                    // }
                    // else
                    // {
                    //     list($propertyID,$leaseID) = explode(' - ',$prop_tenantDetails);
                    // }
                    // var_dump($prop_tenantDetails);
                    // var_dump($propertyID);
                    // var_dump($leaseID);
                    // $replacements = dbGetTenantReplacements($propertyID,$leaseID);
                    $replacements = dbGetBudgetReplacements(
                        $propertyID,
                        $leaseID,
                        $view->items['budgetYear'],
                        $view->items['budget_type'],
                        $view->items['letterFilename']
                    );

                    $page2Content = renderSecondPageContent(
                        $propertyID,
                        $leaseID,
                        $view->items['budgetYear'],
                        $view->items['budget_type'],
                        $view->items['tenant_detail'],
                        $view->items['budget_types'][$view->items['budget_type']],
                        $view->items['showAnnualPropertyBudget']
                    );
                    // var_dump($replacements);

                    $letter_pdf_dlPath = createLetterPDFBudget(
                        $propertyID,
                        $leaseID,
                        $view->items['pdfContent'],
                        $page2Content,
                        $placeholders,
                        $replacements,
                        $view->items['useLetterhead'],
                        $view->items['letterFilename']
                    );


                    $path_info = pathinfo($letter_pdf_dlPath);
                    $pdf_files[] = $path_info['basename'];
                    $docx_files[] = str_replace('.pdf', '.docx', $path_info['basename']);
                    // $letter_pdf_dlPath = createLetterPDFBudgetSecondPage($propertyID,$leaseID,$view->items['pdfContent'], $placeholders, $replacements);
                    // $path_info = pathinfo ($letter_pdf_dlPath);
                    // $pdf_files[] = $path_info['basename'];

                    if ($path_info['basename'] !== '' && $path_info['basename'] !== '0') {
                        $trailLetter =
                        [
                            'letterTemplateID' => $view->items['letterID'],
                            'letterFormat' => 'pdf',
                            'letterRecipient' => '',
                            'letterEmailSubject' => '',
                            'letterEmailBody' => '',
                            'letterTemplateBody' => $view->items['pdfContent'],
                            'propertyCode' => $propertyID,
                            'leaseCode' => $leaseID,
                            'userID' => $_SESSION['user_id'],
                            'dl_path' => $letter_pdf_dlPath,

                        ];
                        $letter_history_id = dbInsertTrailLetter($trailLetter);
                    }
                }

                break;
        }

        if ($view->items['letterFilename'] && $view->items['downloadDocx']) {
            if (count($docx_files ?? []) > 1) {
                $zip = new ZipArchive();
                $path = "{$pathPrefix}{$clientDirectory}/letters/";
                $zip_name = 'multiple_letter_' . time() . '_' . $_SESSION['user_id'] . '.zip';

                $zip->open($path . $zip_name, ZIPARCHIVE::CREATE);
                foreach ($docx_files as $dFiles) {
                    $zip->addFile($path . $dFiles, $dFiles);
                }

                $zip->close();
                $downloadPath = "{$clientDirectory}/letters/" . $zip_name;
                if (isset($view->items['from_api'])) {
                    echo json_encode(
                        "document.location.href='download.php?fileID=" . encodeParameter(
                            $downloadPath
                        ) . "&fileName=multiple_letters.zip'"
                    );
                    exit;
                } else {
                    echo "<script> setTimeout(function(){document.location.href='download.php?fileID=" . encodeParameter(
                        $downloadPath
                    ) . "&fileName=multiple_letters.zip';},500);</script>";
                }
            }

            if (count($docx_files ?? []) == 1) {
                $downloadPath = "{$clientDirectory}/letters/" . $docx_files[0];
                if (isset($view->items['from_api'])) {
                    echo json_encode(
                        "document.location.href='download.php?fileID=" . encodeParameter(
                            $downloadPath
                        ) . "&fileName=multiple_letters.docx'"
                    );
                    exit;
                } else {
                    echo "<script> setTimeout(function(){document.location.href='download.php?fileID=" . encodeParameter(
                        $downloadPath
                    ) . "&fileName=multiple_letters.docx';},500);</script>";
                }
            }
        }

        if (! empty($pdf_files)) {
            mergeLetterPDF($pdf_files, true);
        } else { // $validationErrors[] = 'No file to download or all selected '. $view->items['searchMethod'] .' have an email.';
            $validationErrors[] = 'No ' . $view->items['searchMethod'] . ' without emails found - there is nothing to download.';
        }
    }

    $view->items['templateList'] = false;
    if ($categoryName) {
        $view->items['templateList'] = dbGetLetterTemplate('', $categoryName);
    }

    if (isset($view->items['propertyID'])) {
        $view->items['budgetYearList'] = dbGetDistinctBudgetYears($view->items['propertyID']);
    }

    $mergeTag = [];
    foreach ($view->items['placeholderList'] as $group => $data) {
        if ($group == $view->items['searchMethod']) {
            foreach ($data as $title => $menu) {
                $menuVal = [];
                foreach ($menu as $row) {
                    switch ($row) {
                        case '%dateFormat1% <em>(' . date('jS F Y') . ')</em>':
                            $row = '%dateFormat1%';
                            break;
                        case '%dateFormat2% <em>(' . date('F j, Y') . ')</em>':
                            $row = '%dateFormat2%';
                            break;
                        case '%dateFormat3% <em>(' . date('M j Y') . ')</em>':
                            $row = '%dateFormat3%';
                            break;
                        case '%dateFormat4% <em>(' . date('d-m-Y') . ')</em>':
                            $row = '%dateFormat4%';
                            break;
                    }

                    $menuVal[] = ['value' => $row, 'title' => $row];
                }

                $mergeTag[] = ['title' => $title, 'menu' => $menuVal];
            }
        }
    }

    $view->items['mergeTag'] = json_encode($mergeTag);

    if (! $view->items['status']) {
        $view->items['status'] = 'A';
    }

    $view->items['email_cen_send'] = $email_cen_send;

    $view->items['validationErrors'] = $validationErrors;
    $view->items['tinyMCEEnabled'] = dbGetParam('TINYMCE', 'ENABLED');
    $view->items['tinyMCEAI'] = dbGetParam('TINYMCE', 'AI');
    //    pre_print_r($view->items['validationErrors']);
    if (isset($view->items['from_api'])) {
        if (! isset($view->items['useDocxFile'])) {
            $view->items['useDocxFile'] = '0';
        }

        if ($view->items['useDocxFile'] == '1') {
            $view->items['letterFilenameEn'] = encodeParameter($view->items['letterFilename']);
        }

        if (($view->items['action'] == 'dlAll' || $view->items['from_api'] == 'dlAllNoEmail') && count(
            $validationErrors
        ) == 0) {
            exit;
        }

        echo json_encode($view->items);
        exit;
    }

    $view->render();
}

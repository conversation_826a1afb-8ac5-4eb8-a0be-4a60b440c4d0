<?php


function reportProcess($context)
{

    $threshold = 3;
    global $sess;


    if (! isPostback()) {
        $view = new MasterPage(userViews(), '/module/report.html');
        $view->setSection($context['module']);
    } else {
        $view = new UserControl(userViews(), '/module/report.html');
    }

    $view->bindAttributesFrom($_REQUEST);
    $validationErrors = [];



    if ($context[IS_TASK]) {
        $view->bindAttributesFrom($context);
        extract($context);
    } else {
        $view->bindAttributesFrom($_REQUEST);
        $count = 0;
        if ($collection) {
            $count = count(deserializeParameters($collection));
        }
        $queue = new Queue(TASKTYPE_REPORT);
        if ($count > THRESHOLD_REPORT) {
            $queue->add($_SESSION['clientID'], $_SESSION['un'], 'command=reportProcess&module=module', $_REQUEST);
        }
    }




    if (($context[IS_TASK]) || ($count <= THRESHOLD_REPORT)) {


        // ** HANDLE PROCESSING LOGIC


        // -- if it s a scheduled task - attach the report and email to the requester
        if ($context[IS_TASK]) {
            $email = new EmailTemplate('views/emails/basicEmail.html', SYSTEMURL);
            $attachment = [];
            $attachment[0]['file'] = REPORTPATH . '/' . $pdfDownloadLink;
            $attachment[0]['content_type'] = 'application/pdf';
            sendMail($_SESSION['email'], $_SESSION['first_name'] . ' ' . $_SESSION['last_name'], $email->toString(), 'Report', $attachment);
            logData('Emailed report to ' . $_SESSION['email']);
            $context[TASK_COMPLETE] = true;
        }


    }


    if ($count > THRESHOLD_REPORT) {
        $view->items['statusMessage'] = 'Due to its complexity, this report has been scheduled and will be emailed to you on completion!';
        $view->render();
    }



}

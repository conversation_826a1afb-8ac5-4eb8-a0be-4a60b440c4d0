<?php

/**
 * <AUTHOR> <PERSON>
 *
 * @since 2017-03-21
 **/
function letterHistory(&$context)
{
    if (! isPostback()) {
        $view = new MasterPage(userViews(), '/configuration/letterHistory.html');
        $view->setSection($context['module']);
    } else {
        $view = new UserControl(userViews(), '/configuration/letterHistory.html', '');
    }

    $view->bindAttributesFrom($_REQUEST);
    $filter = [];
    $letterHistoryID = $view->items['letterHistoryID'];

    $view->items['categoryList'] = dbGetLetterCategory();
    $view->items['propertyGroupedList'] = dbPropertyGroupList();
    $view->items['leaseList'] = dbFullLeaseList();

    if (! empty($view->items['letterCategoryID'])) {
        $filter['categoryID'] = $view->items['letterCategoryID'];
    }

    if (! empty($view->items['propertyCode'])) {
        $filter['propertyCode'] = $view->items['propertyCode'];
    }

    if (! empty($view->items['leaseCode'])) {
        $filter['leaseCode'] = $view->items['leaseCode'];
    }

    if (! empty($view->items['letterRecipient'])) {
        $filter['letterRecipient'] = $view->items['letterRecipient'];
    }

    switch ($view->items['action']) {
        case 'downloadDocument':
            global $clientDirectory, $pathPrefix;

            $letterHistory = dbGetLetterHistory($filter, $letterHistoryID);

            // [Enjo] - check for file path first
            if ($letterHistory['filePath']) {
                echo 'download.php?fileID=' . encodeParameter($letterHistory['filePath']);
                exit;
            } elseif (! empty($letterHistory['letterTemplateBody'])) {
                $label = 'letterChargeReview';
                $file = $letterHistory['propertyCode'] . '-' . $letterHistory['leaseCode'] . '_' . date(
                    'Ymd'
                ) . '.' . $letterHistory['letterFormat'];
                $filePath = $pathPrefix . $clientDirectory . '/' . $letterHistory['letterFormat'] . "/{$label}/{$file}";
                $view->items['downloadPath'] = $clientDirectory . '/' . $letterHistory['letterFormat'] . "/{$label}/{$file}";
                $matches = (simplexml_import_dom(
                    (new DOMDocument())->loadHTML($letterHistory['letterTemplateBody'])
                )->xpath('//img/@src'));
                foreach ($matches as $match) {
                    preg_match('/data:image\/(.*?);base64,(.*)/im', $match, $matchedImage);
                    if (! empty($matchedImage[1]) && ! empty($matchedImage[2])) {
                        $letterFileName = uniqid('letterChargeReview_');
                        $letterFileExtension = $matchedImage[1];
                        $letterImagePath = BASEPATH . "/assets/images/temp/{$letterFileName}.{$letterFileExtension}";
                        $decodedImage = base64_decode($matchedImage[2]);
                        if ($decodedImage && file_put_contents($letterImagePath, $decodedImage) !== false) {
                            $letterPathList[] = $letterImagePath;
                            $replacementList[] = HTTPHOST . "/assets/images/temp/{$letterFileName}.{$letterFileExtension}";
                        }
                    }
                }

                $letterHistory['letterTemplateBody'] = str_replace(
                    $matches,
                    $replacementList,
                    $letterHistory['letterTemplateBody']
                );
                if ($letterHistory['letterFormat'] == FILETYPE_PDF) {
                    $html = new HTMLPage2($filePath, 'P');
                    $html->render($letterHistory['letterTemplateBody']);
                    echo 'download.php?fileID=' . encodeParameter($view->items['downloadPath']);
                    foreach ($letterPathList as $v) {
                        unlink($v);
                    }

                    exit;
                } elseif ($letterHistory['letterFormat'] == FILETYPE_DOC) {
                    $docx = new CreateDocx();
                    $docx->embedHTML($letterHistory['letterTemplateBody']);
                    $docx->createDocx($filePath);
                    echo 'download.php?fileID=' . encodeParameter($view->items['downloadPath']);
                    if (! empty($letterPathList)) {
                        foreach ($letterPathList as $v) {
                            unlink($v);
                        }
                    }

                    exit;
                }
            } else {
                echo 'index.php?module=' . $view->items['module'] . '&command=' . $view->items['command'];
                exit;
            }

            break;
        case 'previewEmail':
        case 'previewDocument':
            $view->bindAttributesFrom(dbGetLetterHistory($filter, $letterHistoryID));
            break;
        case 'resendEmail':
            global $clientDirectory, $pathPrefix;

            $letterHistory = dbGetLetterHistory($filter, $letterHistoryID);

            if ($letterHistory['filePath']) {
                $filePath = $letterHistory['filePath'];
            } else {
                $label = 'letterChargeReview';
                $file = $letterHistory['propertyCode'] . '-' . $letterHistory['leaseCode'] . '_' . date(
                    'Ymd'
                ) . '.' . $letterHistory['letterFormat'];
                $filePath = $pathPrefix . $clientDirectory . '/' . $letterHistory['letterFormat'] . "/{$label}/{$file}";
                $view->items['downloadPath'] = $clientDirectory . '/' . $letterHistory['letterFormat'] . "/{$label}/{$file}";

                $matches = (simplexml_import_dom(
                    (new DOMDocument())->loadHTML($letterHistory['letterTemplateBody'])
                )->xpath('//img/@src'));
                foreach ($matches as $match) {
                    preg_match('/data:image\/(.*?);base64,(.*)/im', $match, $matchedImage);
                    if (! empty($matchedImage[1]) && ! empty($matchedImage[2])) {
                        $letterFileName = uniqid('letterChargeReview_');
                        $letterFileExtension = $matchedImage[1];
                        $letterImagePath = BASEPATH . "/assets/images/temp/{$letterFileName}.{$letterFileExtension}";
                        $decodedImage = base64_decode($matchedImage[2]);
                        if ($decodedImage && file_put_contents($letterImagePath, $decodedImage) !== false) {
                            $letterPathList[] = $letterImagePath;
                            $replacementList[] = HTTPHOST . "/assets/images/temp/{$letterFileName}.{$letterFileExtension}";
                        }
                    }
                }

                $letterHistory['letterTemplateBody'] = str_replace(
                    $matches,
                    $replacementList,
                    $letterHistory['letterTemplateBody']
                );

                if ($letterHistory['letterFormat'] == FILETYPE_PDF) {
                    $html = new HTMLPage2($filePath, 'P');
                    $html->render($letterHistory['letterTemplateBody']);
                    foreach ($letterPathList as $v) {
                        unlink($v);
                    }
                } elseif ($letterHistory['letterFormat'] == FILETYPE_DOC) {
                    $docx = new CreateDocx();
                    $docx->embedHTML($letterHistory['letterTemplateBody']);
                    $docx->createDocx($filePath);
                    if (! empty($letterPathList)) {
                        foreach ($letterPathList as $v) {
                            unlink($v);
                        }
                    }
                }
            }

            $email = new EmailTemplate('views/emails/basicEmail.html', SYSTEMURL);
            $email->items['content'] = $letterHistory['letterEmailBody'];

            $attachment = [];
            $attachment[0]['file'] = $filePath;

            //            pre_print_r($attachment);
            sendMail(
                $letterHistory['letterRecipient'],
                '',
                $email->toString(),
                $letterHistory['letterEmailSubject'],
                $attachment,
                null,
                null,
                false
            );
            $trailLetter =
            [
                'letterTemplateID' => $letterHistory['letterTemplateID'],
                'letterFormat' => $letterHistory['letterFormat'],
                'letterRecipient' => $letterHistory['letterRecipient'],
                'letterEmailSubject' => $letterHistory['letterEmailSubject'],
                'letterEmailBody' => $letterHistory['letterEmailBody'],
                'letterTemplateBody' => $letterHistory['letterTemplateBody'],
                'propertyCode' => $letterHistory['propertyCode'],
                'leaseCode' => $letterHistory['leaseCode'],
                'userID' => $_SESSION['user_id'],
                'dl_path' => $filePath,
            ];
            dbInsertTrailLetter($trailLetter);
            $view->items['statusMessage'] = 'Letter has been sent to the recipients.';
            break;
    }

    $letterHistoryList = [];
    $letter_history_data = dbGetLetterHistory($filter);
    foreach ($letter_history_data as $row_data) {
        $emailLogId = null;
        $emailLogList = getEmailLogList($row_data['letterHistoryID']);
        $emailLogHistory = [];
        $emailStatusMessage = '';
        $postmarkMessageId = null;
        $sentDate = $row_data['dateCreated'];
        $sentDateRaw = $row_data['dateCreated2'];


        foreach ($emailLogList as $row_2_data) {
            $emailLogId = $row_2_data['email_log_id'];
        }

        $letterHistoryList[] = [
            'letterHistoryID' => $row_data['letterHistoryID'],
            'letterTemplateID' => $row_data['letterTemplateID'],
            'letterCategoryName' => $row_data['letterCategoryName'],
            'letterFormat' => $row_data['letterFormat'],
            'letterRecipient' => $row_data['letterRecipient'],
            'letterEmailSubject' => $row_data['letterEmailSubject'],
            'letterEmailBody' => $row_data['letterEmailBody'],
            'letterTemplateBody' => $row_data['letterTemplateBody'],
            'propertyCode' => $row_data['propertyCode'],
            'leaseCode' => $row_data['leaseCode'],
            'downloadStatus' => $row_data['downloadStatus'],
            'dateCreated' => $sentDate,
            'dateCreated2' => $sentDateRaw,
            'userCreated' => $row_data['userCreated'],
            'filePath' => $row_data['filePath'],
            'source' => $row_data['source'],
            'emailLogId' => $emailLogId,
            'postmarkMessageId' => $postmarkMessageId,
            'emailStatusMessage' => $emailStatusMessage,
            'emailLogHistory' => $emailLogHistory,
            'emailLogHistorySize' => count($emailLogHistory ?? []),
            'validateEmailSubject' => validateEmailSubject($row_data['letterEmailSubject']),
        ];
    }

    $view->items['letterHistoryList'] = $letterHistoryList;
    if (empty($view->items['letterHistoryList']) && empty($filter)) {
        $view->items['statusMessage'] = 'Nothing to display. No letter has been sent out.';
    }

    $view->items['validationErrors'] = $validationErrors;
    $view->render();
}

/**
 * Validates the email subject by checking if encoding and decoding operations
 * result in different lengths, indicating presence of special characters or markup.
 *
 * @param  string  $emailSubject  The email subject to validate.
 * @return bool Returns true if the email subject contains special characters
 *              or markup that would change the length after encoding/decoding,
 *              false otherwise.
 */
function validateEmailSubject(string $emailSubject): bool
{
    $emailSubjectEntities = htmlentities($emailSubject);
    $emailSubject = html_entity_decode($emailSubjectEntities, ENT_QUOTES | ENT_HTML5);

    return strlen($emailSubjectEntities) !== strlen($emailSubject);
}

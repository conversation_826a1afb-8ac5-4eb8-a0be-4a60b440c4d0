<?php

/**
 * <AUTHOR> <PERSON>
 *
 * @since 2012-11-13
 **/
require_once SYSTEMPATH . '/lib/fileuploader/class.fileuploader.php';
function submitBankAccount(&$context)
{
    global $pathPrefix , $clientDirectory;
    if (! isPostback()) {
        $view = new MasterPage(userViews(), '/configuration/submitBankAccount.html');
        $view->setSection($context['module']);
    } else {
        $view = new UserControl(userViews(), '/configuration/submitBankAccount.html');
    }

    $view->bindAttributesFrom($_REQUEST);
    $validationErrors =  [];

    // Variable Declaration
    $bankID = $view->items['bankID'];

    if (! dbCheckSuperAdmin($_SESSION['userID']) && ! dbCheckAuthority($_SESSION['userID']) && $bankID && $view->items['action'] != 'add') {
        $view =  new MasterPage(userViews(), '/permissions.html');
        $view->render();
        exit;
    }

    $view->items['eftReferenceList'] =
    [
        1 => 'Property/Lease',
        2 => 'CRN',
    ];

    // Array Declaration
    $view->items['yesNoOption'] =
    [
        1 => 'Yes',
        0 => 'No',
    ];
    $view->items['receiptTypeList'] =
    [
        'A' => 'Automatic',
        'M' => 'Manual',
        'P' => 'When Printed',
        'S' => 'Pre-Printed',
    ];
    $view->items['bankList'] = dbGetBankList();
    $view->items['bankAcountList'] = mapParameters(dbGetParams('STDBANK'));
    $view->items['countryList'] = dbGetCountries();
    $view->items['bankTaxList'] = dbGetBankTaxList();
    $view->items['currencyList'] = dbGetCurrencyList();

    dbGetDefaultCountry();

    // Default Values
    if (empty($view->items['bankCountry'])) {
        $view->items['bankCountry'] = dbGetParam('PROPDEF', 'COUNTRY');
    }

    $view->items['stateList'] = dbGetStates($view->items['bankCountry']);
    if (empty($view->items['bankState']) && $view->items['bankCountry'] == dbGetParam('PROPDEF', 'COUNTRY')) {
        $view->items['bankState'] = dbGetParam('PROPDEF', 'STATE');
    }

    if (empty($view->items['receiptType'])) {
        $view->items['receiptType'] = 'A';
    }

    if (! isset($view->items['showEftInfo'])) {
        $view->items['showEftInfo'] = 1;
    }

    if (! isset($view->items['showPropertyCode'])) {
        $view->items['showPropertyCode'] = 1;
    }

    if (! isset($view->items['showOwnerDetail'])) {
        $view->items['showOwnerDetail'] = 1;
    }

    if (! isset($view->items['victoriaOwnerCorp'])) {
        $view->items['victoriaOwnerCorp'] = 0;
    }

    if (! isset($view->items['showPropertyDetail'])) {
        $view->items['showPropertyDetail'] = 1;
    }

    if (! isset($view->items['eftReference'])) {
        $view->items['eftReference'] = 1;
    }

    if ($view->items['do'] == 'process') {
        // Filter
        if (! array_key_exists($view->items['bankCode'], $view->items['bankAcountList'])) {
            $view->items['bankCode'] = null;
        }

        // Validate
        if (empty($bankID)) {
            $validationErrors[] = 'You have not entered a Bank Code.';
        } elseif ($view->items['action'] == 'add' && dbCheckDuplicateBankAccount($bankID)) {
            $validationErrors[] = 'Bank ID already exists.';
        } elseif (! isValid($bankID, TEXT_INT_ONLY, false)) {
            $validationErrors[] = 'Bank Code must contain numbers only.';
        } elseif (mb_strlen($bankID) > 10) {
            $validationErrors[] = 'Bank Code must not be more than 10 characters.';
        }

        if (empty($view->items['bankCode'])) {
            $validationErrors[] = 'You have not selected a Bank Name.';
        }

        if (empty($view->items['bankAccountName'])) {
            $validationErrors[] = 'You have not entered a Bank Account Name.';
        } elseif (! isValid($view->items['bankAccountName'], TEXT_ALPHANUMERIC_WHITESPACE, false)) {
            $validationErrors[] = 'Bank Account Name must be entered as alphanumeric without the following characters / ? * -';
        }

        // VALIDATE BSB & BANK ACCOUNT NUMBER
        if (getDisplayBsbFromSession() &&
            strlen($view->items['bankBSB']) !== getBsbLengthFromSession()
        ) {
            $validationErrors[] = 'Your ' . getBsbLabelFromSession() . ' is incorrect (must be ' .
                getBsbLengthFromSession() . ' numbers with no dash or spaces)';
        }

        if (! cdf_validate($view->items['bankAccount'], 'bank_account_length', $view->items['bankCountry']) || ! isValid($view->items['bankAccount'], TEXT_INT, false)) {
            $validationErrors[] = 'Account Number should be ' . cdf_displayLabel('bank_account_length', $view->items['bankCountry']) . ' digits or less.';
        }

        if ($view->items['bpayBillerCode'] && mb_strlen($view->items['bpayBillerCode']) > 10) {
            $validationErrors[] = 'BPay Biller Code must not be more than 10 characters.';
        }

        if (empty($view->items['bankBranchName'])) {
            $validationErrors[] = 'You have not entered a Branch Name.';
        }

        if (empty($view->items['bankStreet'])) {
            $validationErrors[] = 'You have not entered a Street.';
        }

        if (empty($view->items['bankCity'])) {
            $validationErrors[] = 'You have not entered a City.';
        }

        // if (empty ($view->items['bankState'])) $validationErrors[] = 'You have not selected a State.';
        // if (empty ($view->items['bankPostCode'])) $validationErrors[] = 'You have not entered a Post Code.';
        // elseif (!isValid ($view->items['bankPostCode'], TEXT_INT_ONLY, false)) $validationErrors[] = 'Post Code must contain numbers only.';

        // VALIDATE STATE & POST CODE
        if (cdf_isShown('display_state', $view->items['bankCountry']) && empty($view->items['bankState'])) {
            $validationErrors[] = 'You have not selected a State.';
        }

        // POST CODE FORMAT VALIDATION
        $postcodeValidation = cdf_validate_postcode($view->items['bankPostCode'], $view->items['bankCountry']);
        if (! $postcodeValidation->valid) {
            $validationErrors[] = $postcodeValidation->error;
        }

        if (empty($view->items['bankCountry'])) {
            $validationErrors[] = 'You have not selected a Country.';
        }

        // elseif (($view->items['bankCountry'] == 'AU' || $view->items['bankCountry'] == 'PH') AND mb_strlen ($view->items['bankPostCode']) != 4) $validationErrors[] = 'Post Code must be 4 digits.';
        if (empty($view->items['bankCurrency'])) {
            $validationErrors[] = 'You have not selected a Local Currency.';
        }

        if (empty($view->items['receiptType'])) {
            $validationErrors[] = 'You have not selected a Receipt Type.';
        }

        if ($view->items['bankUserID'] && ! isValid($view->items['bankUserID'], TEXT_INT_ONLY, false)) {
            $validationErrors[] = 'Your User ID is incorrect (must be 6 numbers with no dash or spaces).';
        }

        if (mb_strlen($view->items['financialInstitution']) > 3) {
            $validationErrors[] = 'Financial Institution must not be more than 3 characters.';
        }

        if ($view->items['bpayBatchID'] && mb_strlen($view->items['bpayBatchID']) > 10) {
            $validationErrors[] = 'BPay Batch ID must not be more than 10 characters.';
        }

        if ($view->items['deft'] + $view->items['payway'] + $view->items['creditCard'] + $view->items['payID'] > 1) {
            $validationErrors[] = 'Only one payment method is allowed.';
        }

        if ($view->items['creditCard'] && ! $view->items['provider']) {
            $validationErrors[] = 'Specify a link to your provider.';
        }

        if ($view->items['payway'] && ! $view->items['paywayBillerCode']) {
            $validationErrors[] = 'Specify a BPAY Biller Code.';
        }

        if ($view->items['payID'] && ! $view->items['payIDCode']) {
            $validationErrors[] = 'Specify a PayID Reference.';
        }

        if (noErrors($validationErrors)) {
            // Filter
            $bankID = $view->items['bankID'] = mb_substr(mb_strtoupper($bankID), 0, 10);
            $view->items['bankBSB'] = mb_substr(
                $view->items['bankBSB'],
                0,
                getBsbLengthFromSession()
            );
            $view->items['bpayBillerCode'] = mb_substr($view->items['bpayBillerCode'], 0, 10);
            $view->items['bankUserID'] = intval($view->items['bankUserID']);
            $view->items['bankName'] = $view->items['bankAcountList'][$view->items['bankCode']];
            // $view->items['bankPostCode'] = intval (mb_substr ($view->items['bankPostCode'], 0, 4));
            $view->items['bpayBatchID'] = mb_substr($view->items['bpayBatchID'], 0, 10);
        }
    }

    switch ($view->items['action']) {
        case 'add':
            if ($view->items['do'] != 'process') {
                // //$bankID = $view->items['bankID'] = $view->items['pmbk_direct_upload_username'] = $view->items['pmbk_direct_upload_password'] = null; //note: this resets bank id when changing the bankname dropdown
                $bankID = $view->items['pmbk_direct_upload_username'] = $view->items['pmbk_direct_upload_password'] = null;

            }

            if (noErrors($validationErrors) && $view->items['do'] == 'process') {

                $filePath = "{$pathPrefix}{$clientDirectory}/pdf/Strata/";
                if (! file_exists($filePath)) {
                    mkdir($filePath, FILE_PERMISSION, true);
                }

                $FileUploader = new FileUploader('NoticeStrataFiles', [
                    'limit' => null,
                    'maxSize' => null,
                    'fileMaxSize' => null,
                    'extensions' => null,
                    'required' => false,
                    'uploadDir' => $filePath,
                    'title' => $bankID,
                    'replace' => false,
                    'listInput' => true,
                    'files' => null,
                ]);

                if ($view->items['termNoticeStrata'] && (empty($FileUploader->getListInput()) && $view->items['deleteFile'] || ! $view->items['deleteFile'] && ! $view->items['NoticeStrataLetter'] && empty($FileUploader->getListInput()))) {
                    $validationErrors[] = 'Terms / Notice PDF File is required.';
                }

                if (noErrors($validationErrors)) {

                    $data = $FileUploader->upload();
                    dbAddBankAccount($view->items);
                    $view->items['statusMessage'] = 'The new Bank Account has been successfully added.';
                    $view->items['success'] = true;
                    $context = $view->items;
                    executeCommand('bankAccount');
                }
            }

            break;
        case 'edit':
            if ($view->items['do'] != 'process' && $view->items['change'] != 'changeCountry') {
                $view->bindAttributesFrom(dbGetBankInfo($bankID, true));
            }

            $view->items['stateList'] = dbGetStates($view->items['bankCountry']);

            $view->items['NoticeStrataLetter'] = is_file($pathPrefix . $clientDirectory . "/pdf/Strata/{$bankID}.pdf") ? $clientDirectory . "/pdf/Strata/{$bankID}.pdf" : '';

            if (noErrors($validationErrors) && $view->items['do'] == 'process') {

                $filePath = "{$pathPrefix}{$clientDirectory}/pdf/Strata/";
                if (! file_exists($filePath)) {
                    mkdir($filePath, FILE_PERMISSION, true);
                }

                $FileUploader = new FileUploader('NoticeStrataFiles', [
                    'limit' => null,
                    'maxSize' => null,
                    'fileMaxSize' => null,
                    'extensions' => null,
                    'required' => false,
                    'uploadDir' => $filePath,
                    'title' => "{$bankID}",
                    'replace' => false,
                    'listInput' => true,
                    'files' => null,
                ]);

                if ($view->items['termNoticeStrata'] && (empty($FileUploader->getListInput()) && $view->items['deleteFile'] || ! $view->items['deleteFile'] && ! $view->items['NoticeStrataLetter'] && empty($FileUploader->getListInput()))) {
                    $validationErrors[] = 'Terms / Notice PDF File is required.';
                }

                if (noErrors($validationErrors)) {

                    if ($view->items['deleteFile']) {
                        unlink($pathPrefix . $clientDirectory . "/pdf/Strata/{$bankID}.pdf");
                    }

                    $data = $FileUploader->upload();

                    dbUpdateBankAccount($view->items);
                    $view->items['statusMessage'] = 'Bank Account has been successfully updated.';
                    $view->items['success'] = true;
                    $context = $view->items;
                    executeCommand('bankAccount');
                }
            }

            break;
        case 'delete':
            if (empty($bankID)) {
                $validationErrors[] = 'You have not selected a bank account.';
            } else {
                $count = dbCheckBankAccount($bankID);
                if ($count) {
                    $validationErrors[] = "Unable to delete bank account. There are {$count} properties using this bank account.";
                }
            }

            if (noErrors($validationErrors)) {
                dbDeleteBankAccount($bankID);
                $view->items['statusMessage'] = "Bank Account {$bankID} has been deleted.";
                $view->items['success'] = true;
            } else {
                $view->items['validationErrors'] = $validationErrors;
            }

            $context = $view->items;


            executeCommand('bankAccount');
            break;
    }

    $view->items['validationErrors'] = $validationErrors;
    $view->render();
}

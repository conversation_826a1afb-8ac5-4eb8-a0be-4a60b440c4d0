
<?php
function trustAccountDetails(&$context)
{
    global $clientDirectory, $pathPrefix;

    //    if (!$_REQUEST['print'])
    //    {
    if (! isPostback()) {
        $view = new MasterPage(userViews(), '/configuration/trustAccountDetails.html');
        $view->setSection($context['module']);
    } else {
        $view = new UserControl(userViews(), '/configuration/trustAccountDetails.html');
    }
    //    }
    //    else $view = new PrintPage (userViews (), '/configuration/trustAccountDetails.html');


    $view->bindAttributesFrom($_REQUEST);
    $validationErrors = [];

    // Array Declaration
    // Not for trust account details - START
    //    $view->items['paymentMethodList'] = array
    //    (
    //        PAY_EFT => 'Direct Deposit (EFT)',
    //        PAY_BPAY => 'BPAY',
    //        PAY_CHQ => 'Cheque'
    //    );
    // Not for trust account details - END

    $country = dbGetDefaultCountry();

    if ($country == 'AU') {
        $view->items['paymentMethodList'] =
        [
            PAY_EFT => 'Direct Deposit (EFT)',
            PAY_BPAY => 'BPAY',
            PAY_CHQ => 'Cheque',
        ];
    } else {
        $view->items['paymentMethodList'] =
        [
            PAY_EFT => 'Direct Deposit (EFT)',
            PAY_CHQ => 'Cheque',
        ];
    }

    $view->items['eftReferenceList'] =
    [
        1 => 'Property/Lease',
        2 => 'CRN',
    ];

    $view->items['yesNoOption'] =
    [
        1 => 'Yes',
        0 => 'No',
    ];
    $view->items['receiptTypeList'] =
    [
        'A' => 'Automatic',
        'M' => 'Manual',
        'P' => 'When Printed',
        'S' => 'Pre-Printed',
    ];

    $view->items['countryList'] = dbGetCountries();
    $view->items['bankTaxList'] = dbGetBankTaxList();
    $view->items['currencyList'] = dbGetCurrencyList();

    $view->items['mappedCountryList'] = mapParameters(dbGetCountries(), 'countryCode', 'countryName');


    // O
    // if (empty ($view->items['bankCountry'])) $view->items['bankCountry'] = dbGetParam ('PROPDEF', 'COUNTRY');
    // $view->items['stateList'] = dbGetStates ($view->items['bankCountry']);
    // if (empty ($view->items['bankState'])) $view->items['bankState'] = dbGetParam ('PROPDEF', 'STATE');

    // U
    if (empty($view->items['bankCountry'])) {
        $view->items['bankCountry'] = dbGetParam('PROPDEF', 'COUNTRY');
    }
    if (empty($view->items['bankState']) && $view->items['bankCountry'] == dbGetParam('PROPDEF', 'COUNTRY')) {
        $view->items['bankState'] = dbGetParam('PROPDEF', 'STATE');
    }

    $view->items['stateList'] = dbGetStates($view->items['bankCountry']);

    if (empty($view->items['receiptType'])) {
        $view->items['receiptType'] = 'A';
    }
    if (! isset($view->items['showEftInfo'])) {
        $view->items['showEftInfo'] = 1;
    }
    if (! isset($view->items['showPropertyCode'])) {
        $view->items['showPropertyCode'] = 1;
    }
    if (! isset($view->items['showOwnerDetail'])) {
        $view->items['showOwnerDetail'] = 1;
    }
    if (! isset($view->items['showPropertyDetail'])) {
        $view->items['showPropertyDetail'] = 1;
    }

    // ## Retrieve bank account list
    $view->items['bankAccountList'] = dbGetBankList();
    $view->items['bankAcountList'] = mapParameters(dbGetParams('STDBANK'));
    // #########

    $view->items['isPM'] = isUserType(USER_CLIENT) ? 1 : 0;

    $view->items['companyList'] = dbGetCompanyList();
    $view->items['userList'] = dbGetAccountUserList();
    foreach ($view->items['userList'] as $k => $v) {
        if ($v['status'] == 'Inactive') {
            unset($view->items['userList'][$k]);
        }
    }
    $view->items['authorised'] = dbCheckAuthority($_SESSION['user_id']);

    /** Get Security Settings - START */
    $view->items['security_settings'] = dbGetSecuritySettings();
    $view->items['flag_authorizer_submitter'] = ($view->items['security_settings']['authorizer_submitter_flag'] === '1');
    $view->items['flag_default_payment_method'] = ($view->items['security_settings']['default_payment_method_flag'] === '1');
    /** Get Security Settings - END */
    if (count($view->items['userList']) === 0) {
        if (! dbCheckSuperAdmin($_SESSION['userID'])) {
            $validationErrors[] = 'No active authoriser found. Contact the administrator to add or activate an authoriser.';
        } else {
            $validationErrors[] = 'No active authorisers found. <a href="index.php?module=configuration&command=users">Click here</a> to add or activate an authoriser.';
        }
        $view->items['noAuthoriser'] = true;
    } else {
        $view->items['noAuthoriser'] = false;
    }

    // pre_print_r($view->items);

    // ## Restrict PM access - START
    // //echo 'Is user type client: '.$view->items['isPM'];
    if (isUserType(USER_CLIENT) && ($view->items['action'] != 'verify' && $view->items['action'] != 'approve' && $view->items['action'] != 'reject' && $view->items['action'] != 'viewApproved' && $view->items['action'] != 'viewRejected')) {
        $view =  new MasterPage(userViews(), '/permissions.html');
        $view->render();
        exit;
    }
    // ## Restrict PM access - END

    // if ($view->items['companyID']) //O
    if ($view->items['bankID']) { // U
        // echo 'Selected trust account id:'.$view->items['bankID'].'</br>';

        // Retrieve Company Bank Details
        // $company = dbGetCompany ($view->items['companyID']); //O
        // $bank = dbGetTrustAccountDetails ($view->items['bankID']); //U
        $bank = dbGetBankInfo($view->items['bankID'], true); // U

        // if ($company)
        if ($bank) {
            // if ($view->items['action'] == 'retrieve' || $view->items['action'] == 'approve') $view->bindAttributesFrom ($company); //O
            if ($view->items['action'] == 'retrieve' || $view->items['action'] == 'approve') {
                $bankID = $view->items['bankID'];

                if ($view->items['change'] != 'changeCountry') {
                    $view->bindAttributesFrom($bank);
                    $view->items['stateList'] = dbGetStates($view->items['bankCountry']);
                }

                $view->items['NoticeStrataLetter'] = is_file($pathPrefix . $clientDirectory . "/pdf/Strata/{$bankID}.pdf") ? $clientDirectory . "/pdf/Strata/{$bankID}.pdf" : '';
                // Default Values
                if (empty($view->items['paymentMethod'])) {
                    $view->items['paymentMethod'] = PAY_CHQ;
                }
                if (empty($view->items['chequeDays'])) {
                    $view->items['chequeDays'] = 4;
                }

                $view->items['directUploadUsername'] = $bank['pmbk_direct_upload_username'];
                $view->items['directUploadPassword'] = $bank['pmbk_direct_upload_password'];
                $view->items['downloadStatementClientID'] = $bank['pmbk_download_statement_client_id'];
                $view->items['downloadStatementClientNumber'] = $bank['pmbk_download_statement_client_number'];
            }

            // Array Declaration
            $view->items['dayList'] =
            [
                0 => 0,
                1 => 1,
                2 => 2,
                3 => 3,
                4 => 4,
            ];

            // echo 'Debit Barred: '.$view->items['debitBarred'].'</br>';

            if ($view->items['action'] == 'submit') {
                $view->items['userDBList'] = dbGetUsersForDatabase(dbGetClientID());

                /*
                if ($view->items['paymentMethod'] == PAY_EFT)
                {
                    if (!isValid ($view->items['bsbNumber'], TEXT_INT_ONLY, false)) $validationErrors[] = 'Your BSB is incorrect (must be 6 numbers with no dash or spaces)';
                    elseif (strlen ($view->items['bsbNumber']) != 6) $validationErrors[] = 'BSB number must have 6 digits.';
                    if (!isValid ($view->items['bankAccountNumber'], TEXT_INT, false)) $validationErrors[] = 'Bank Account Number is invalid.';
                    if (!isValid ($view->items['bankAccountName'], TEXT_ALPHANUMERIC_WHITESPACE, false)) $validationErrors[] = 'Bank Account Name must be entered as alphanumeric without special characters.';
                    if (!isValid ($view->items['bankName'], TEXT_LOOSE, true)) $validationErrors[] = 'Bank Name is invalid.';
                }
                elseif ($view->items['paymentMethod'] == PAY_BPAY && !isValid ($view->items['bpayBillerCode'], TEXT_LOOSE, false)) $validationErrors[] = 'Biller Code is invalid.';
                */

                // ## if ($view->items['do'] == 'process') <- not needed - START
                /*
                if ($view->items['do'] == 'process')
                {
                */

                if (empty($view->items['bankAccountName'])) {
                    $validationErrors[] = 'You have not entered a Bank Account Name.';
                } elseif (! isValid($view->items['bankAccountName'], TEXT_ALPHANUMERIC_WHITESPACE, false)) {
                    $validationErrors[] = 'Bank Account Name must be entered as alphanumeric without the following characters / ? * -';
                }

                if (cdf_isShown('display_bsb', $view->items['bankCountry']) &&
                    (! isValid($view->items['bankBSB'], TEXT_INT_ONLY, false) ||
                        strlen($view->items['bankBSB']) != cdf_displayLabel('bsb_length', $view->items['bankCountry']))
                ) {
                    $validationErrors[] = 'Your ' . cdf_displayLabel(
                        'bsb_label',
                        $view->items['bankCountry']
                    ) . ' is incorrect (must be ' . cdf_displayLabel(
                        'bsb_length',
                        $view->items['bankCountry']
                    ) . ' numbers with no dash or spaces)';
                }

                if (! cdf_validate($view->items['bankAccount'], 'bank_account_length', $view->items['bankCountry']) || ! isValid($view->items['bankAccount'], TEXT_INT, false)) {
                    $validationErrors[] = 'Account Number should be ' . cdf_displayLabel('bank_account_length', $view->items['bankCountry']) . ' digits or less.';
                }

                if ($view->items['bpayBillerCode'] && mb_strlen($view->items['bpayBillerCode']) > 10) {
                    $validationErrors[] = 'BPay Biller Code must not be more than 10 characters.';
                }
                if (empty($view->items['bankBranchName'])) {
                    $validationErrors[] = 'You have not entered a Branch Name.';
                }
                if (empty($view->items['bankStreet'])) {
                    $validationErrors[] = 'You have not entered a Street.';
                }
                if (empty($view->items['bankCity'])) {
                    $validationErrors[] = 'You have not entered a City.';
                }

                // VALIDATE STATE & POST CODE
                // ---O
                // if (empty ($view->items['bankState'])) $validationErrors[] = 'You have not selected a State.';
                // if (empty ($view->items['bankPostCode'])) $validationErrors[] = 'You have not entered a Post Code.';
                // elseif (!isValid ($view->items['bankPostCode'], TEXT_INT_ONLY, false)) $validationErrors[] = 'Post Code must contain numbers only.';

                // ---U
                if (cdf_isShown('display_state', $view->items['bankCountry']) && empty($view->items['bankState'])) {
                    $validationErrors[] = 'You have not selected a State.';
                }

                // POST CODE FORMAT VALIDATION
                $postcodeValidation = cdf_validate_postcode($view->items['bankPostCode'], $view->items['bankCountry']);
                if (! $postcodeValidation->valid) {
                    $validationErrors[] = $postcodeValidation->error;
                }

                if (empty($view->items['bankCountry'])) {
                    $validationErrors[] = 'You have not selected a Country.';
                }
                // elseif (($view->items['bankCountry'] == 'AU' || $view->items['bankCountry'] == 'PH') AND mb_strlen ($view->items['bankPostCode']) != $_SESSION['country_default']['post_code_length']) $validationErrors[] = 'Post Code must be '.$_SESSION['country_default']['post_code_length'].' digits.';
                // //elseif (($view->items['bankCountry'] == 'AU' || $view->items['bankCountry'] == 'PH') AND mb_strlen ($view->items['bankPostCode']) != 4) $validationErrors[] = 'Post Code must be 4 digits.';
                if (empty($view->items['bankCurrency'])) {
                    $validationErrors[] = 'You have not selected a Local Currency.';
                }
                if (empty($view->items['receiptType'])) {
                    $validationErrors[] = 'You have not selected a Receipt Type.';
                }
                if ($view->items['bankUserID'] && ! isValid($view->items['bankUserID'], TEXT_INT_ONLY, false)) {
                    $validationErrors[] = 'Your User ID is incorrect (must be 6 numbers with no dash or spaces).';
                }
                if (mb_strlen($view->items['financialInstitution']) > 3) {
                    $validationErrors[] = 'Financial Institution must not be more than 3 characters.';
                }
                if ($view->items['bpayBatchID'] && mb_strlen($view->items['bpayBatchID']) > 10) {
                    $validationErrors[] = 'BPay Batch ID must not be more than 10 characters.';
                }

                if ($view->items['deft'] + $view->items['payway'] + $view->items['creditCard'] > 1) {
                    $validationErrors[] = 'Only one payment method is allowed.';
                }
                if ($view->items['creditCard'] && ! $view->items['provider']) {
                    $validationErrors[] = 'Specify a link to your provider.';
                }
                if ($view->items['payway'] && ! $view->items['paywayBillerCode']) {
                    $validationErrors[] = 'Specify a BPAY Biller Code.';
                }

                /* } */
                // ## if ($view->items['do'] == 'process') <- not needed - END

                if (empty($view->items['reason'])) {
                    $validationErrors[] = 'You need to enter a reason in order to change ' . strtolower($_SESSION['country_default']['trust_account']) . ' details.';
                }

                // ## Check security setting for authoriser and submitter
                // ## Validate if selected authoriser from dropdown is same with current logged-in user
                if ($view->items['flag_authorizer_submitter'] === true && $view->items['authoriser'] == $_SESSION['user_id']) {
                    $validationErrors[] = 'You cannot set yourself as the authoriser for this request. Please select another user.';
                }

                // Validate e-mails
                if (! $view->items['authoriser']) {
                    $validationErrors[] = 'No authoriser was selected.';
                } else {
                    $recipientEmail = mapParameters($view->items['userDBList'], 'userID', 'email');
                    $recipientEmail = $recipientEmail[$view->items['authoriser']];
                    if (! isValid($recipientEmail, TEXT_EMAIL, false)) {
                        $validationErrors[] = "Authoriser's e-mail address is invalid.";
                    }
                }
                $accountManagerEmail = dbGetParam('ACCMGR', 'MAINT');
                if (! isValid($accountManagerEmail, TEXT_EMAIL, false)) {
                    $validationErrors[] = "Account Manager's e-mail address is invalid.";
                }

                if (noErrors($validationErrors)) {
                    // Filter
                    if (getDisplayBsbFromSession()) {
                        $view->items['bankBSB'] = mb_substr(
                            $view->items['bankBSB'],
                            0,
                            cdf_displayLabel(
                                'bsb_length',
                                $view->items['bankCountry']
                            )
                        );
                    }

                    $view->items['bpayBillerCode'] = mb_substr($view->items['bpayBillerCode'], 0, 10);
                    $view->items['bankName'] = $view->items['bankAcountList'][$view->items['bankCode']];
                    $view->items['bpayBatchID'] = mb_substr($view->items['bpayBatchID'], 0, 10); // newly added

                    /*
                    //$company['originalPaymentMethod'] = $company['paymentMethod'];
                    $company['originalBsbNumber'] = $company['bsbNumber'];
                    $company['originalBankAccountNumber'] = $company['bankAccountNumber'];
                    $company['originalBankAccountName'] = $company['bankAccountName'];
                    $company['originalBankName'] = $company['bankName'];
                    $company['originalBpayBillerCode'] = $company['bpayBillerCode'];
                    $company['originalChequeDays'] = $company['chequeDays'];

                    //$company['paymentMethod'] = (int) $view->items['paymentMethod'];
                    $company['bsbNumber'] = $view->items['bsbNumber'];
                    $company['bankAccountNumber'] = $view->items['bankAccountNumber'];
                    $company['bankAccountName'] = $view->items['bankAccountName'];
                    $company['bankName'] = $view->items['bankName'];
                    $company['bpayBillerCode'] = $view->items['bpayBillerCode'];
                    $company['chequeDays'] = (int) $view->items['chequeDays'];
                    $company['updateUser'] = $_SESSION['un'];
                    $company['reason'] = $view->items['reason'];
                    $company['fileID'] = 0;
                    $company['authoriser'] = $view->items['authoriser'];
                    $company['userID'] = $_SESSION['user_id'];
                    */

                    // Values to be saved
                    $bank['originalDirectUploadUsername'] = $bank['pmbk_direct_upload_username'];
                    $bank['originalDirectUploadPassword'] = $bank['pmbk_direct_upload_password'];
                    $bank['originalDownloadStatementClientID'] = $bank['pmbk_download_statement_client_id'];
                    $bank['originalDownloadStatementPassword'] = $bank['pmbk_download_statement_password'];
                    $bank['originalDownloadStatementClientNumber'] = $bank['pmbk_download_statement_client_number'];

                    $bank['originalBankAccountName'] = $bank['bankAccountName'];

                    // O
                    // if($_SESSION['country_default']['display_bsb']){
                    //    $bank['originalBankBSB'] = $bank['bankBSB'];
                    // }

                    // U
                    $bank['originalBankBSB'] = cdf_isShown('display_bsb', $view->items['bankCountry']) ? $bank['bankBSB'] : '';

                    $bank['originalBankAccount'] = $bank['bankAccount'];
                    $bank['originalBpayBillerCode'] = $bank['bpayBillerCode'];
                    $bank['originalBankName'] = $bank['bankName'];
                    $bank['originalBankBranchName'] = $bank['bankBranchName'];
                    $bank['originalBankCode'] = $bank['bankCode'];

                    $bank['originalBankStreet'] = $bank['bankStreet'];
                    $bank['originalBankCity'] = $bank['bankCity'];

                    // $bank['originalBankState'] = $bank['bankState']; //O

                    // O
                    // //if (cdf_isShown('display_state', $bank['bankCountry'])) {
                    // if (cdf_displayLabel('display_state')) {
                    //    $bank['originalBankState'] = $bank['bankState'];
                    // }

                    // U
                    if (cdf_isShown('display_state', $view->items['bankCountry'])) {
                        $bank['originalBankState'] = $bank['bankState'];
                    } else {
                        $bank['originalBankState'] = ''; // not required
                    }

                    $bank['originalBankPostCode'] = $bank['bankPostCode'];
                    $bank['originalBankCountry'] = $bank['bankCountry'];
                    $bank['originalBankCurrency'] = $bank['bankCurrency'];
                    $bank['originalForeignCurrency'] = $bank['foreignCurrency'];
                    $bank['originalBankTaxCode'] = $bank['bankTaxCode'];
                    $bank['originalReceiptType'] = $bank['receiptType'];
                    $bank['originalDebitBarred'] = $bank['debitBarred'];
                    $bank['originalDeft'] = $bank['deft'];
                    $bank['originalPayway'] = $bank['payway'];
                    $bank['originalPaywayBillerCode'] = $bank['paywayBillerCode'];
                    $bank['originalCreditCard'] = $bank['creditCard'];
                    $bank['originalProvider'] = $bank['provider'];
                    $bank['originalDirectDeposit'] = $bank['directDeposit'];
                    $bank['originalShowOwnerCompanyOnReceipt'] = $bank['showOwnerCompanyOnReceipt'];
                    $bank['originalShowEftInfo'] = $bank['showEftInfo'];
                    $bank['originalShowPropertyCode'] = $bank['showPropertyCode'];
                    $bank['originalShowOwnerDetail'] = $bank['showOwnerDetail'];
                    $bank['originalShowPropertyDetail'] = $bank['showPropertyDetail'];
                    $bank['originalShowDueDate'] = $bank['showDueDate'];
                    $bank['originalBankUserID'] = $bank['bankUserID'];
                    $bank['originalBpayBatchID'] = $bank['bpayBatchID'];
                    $bank['originalBankUserName'] = $bank['bankUserName'];
                    $bank['originalFinancialInstitution'] = $bank['financialInstitution'];
                    $bank['originalBankRemittance'] = $bank['bankRemittance'];
                    $bank['originalBpayUserID'] = $bank['bpayUserID'];
                    $bank['originalvictoriaOwnerCorp'] = $bank['victoriaOwnerCorp'];
                    $bank['originaltermNoticeStrata'] = $bank['termNoticeStrata'];
                    $bank['originaleftReference'] = $bank['eftReference'];

                    $bank['directUploadUsername'] = $view->items['directUploadUsername'];
                    $bank['directUploadPassword'] = $view->items['directUploadPassword'];
                    $bank['downloadStatementClientID'] = $view->items['downloadStatementClientID'];
                    $bank['downloadStatementPassword'] = $view->items['downloadStatementPassword'];
                    $bank['downloadStatementClientNumber'] = $view->items['downloadStatementClientNumber'];
                    $bank['bankStreet'] = $view->items['bankStreet'];
                    $bank['bankCity'] = $view->items['bankCity'];

                    // $bank['bankState'] = $view->items['bankState']; //O

                    // U
                    if (cdf_isShown('display_state', $view->items['bankCountry'])) {
                        $bank['bankState'] = $view->items['bankState'];
                    } else {
                        $bank['bankState'] = ''; // not required
                    }

                    $bank['bankPostCode'] = $view->items['bankPostCode'];
                    $bank['bankCountry'] = $view->items['bankCountry'];
                    $bank['bankCurrency'] = $view->items['bankCurrency'];
                    $bank['foreignCurrency'] = $view->items['foreignCurrency'];
                    $bank['bankTaxCode'] = $view->items['bankTaxCode'];
                    $bank['receiptType'] = $view->items['receiptType'];
                    $bank['debitBarred'] = $view->items['debitBarred'];
                    $bank['deft'] = $view->items['deft'];
                    $bank['payway'] = $view->items['payway'];
                    $bank['paywayBillerCode'] = $view->items['paywayBillerCode'];
                    $bank['creditCard'] = $view->items['creditCard'];
                    $bank['provider'] = $view->items['provider'];
                    $bank['directDeposit'] = $view->items['directDeposit'];
                    $bank['showOwnerCompanyOnReceipt'] = $view->items['showOwnerCompanyOnReceipt'];
                    $bank['showEftInfo'] = $view->items['showEftInfo'];
                    $bank['showPropertyCode'] = $view->items['showPropertyCode'];
                    $bank['showOwnerDetail'] = $view->items['showOwnerDetail'];
                    $bank['showPropertyDetail'] = $view->items['showPropertyDetail'];
                    $bank['showDueDate'] = $view->items['showDueDate'];
                    $bank['bankUserID'] = $view->items['bankUserID'];
                    $bank['bpayBatchID'] = $view->items['bpayBatchID'];
                    $bank['bankUserName'] = $view->items['bankUserName'];
                    $bank['financialInstitution'] = $view->items['financialInstitution'];
                    $bank['bankRemittance'] = $view->items['bankRemittance'];
                    $bank['bpayUserID'] = $view->items['bpayUserID'];
                    $bank['reason'] = $view->items['reason'];

                    $bank['bankID'] = $view->items['bankID'];
                    $bank['bankAccountName'] = $view->items['bankAccountName'];

                    // O
                    // if($_SESSION['country_default']['display_bsb']){
                    //    $bank['bankBSB'] = $view->items['bankBSB'];
                    // }

                    // U
                    $bank['bankBSB'] = cdf_isShown('display_bsb', $view->items['bankCountry']) ? $view->items['bankBSB'] : '';

                    $bank['bankAccount'] = $view->items['bankAccount'];
                    $bank['bpayBillerCode'] = $view->items['bpayBillerCode'];
                    $bank['bankName'] = $view->items['bankName'];
                    $bank['bankCode'] = $view->items['bankCode'];
                    $bank['bankBranchName'] = $view->items['bankBranchName'];
                    $bank['victoriaOwnerCorp'] = $view->items['victoriaOwnerCorp'];
                    $bank['termNoticeStrata'] = $view->items['termNoticeStrata'];
                    $bank['eftReference'] = $view->items['eftReference'];

                    $bank['fileID'] = 0;
                    $bank['authoriser'] = $view->items['authoriser'];
                    // $bank['userID'] = $_SESSION['user_id'];
                    $bank['user_created'] = $_SESSION['user_id'];

                    // Attachment
                    $attachment =  [];
                    if (is_uploaded_file($_FILES['file']['tmp_name'])) {
                        $extension = pathinfo($_FILES['file']['name'], PATHINFO_EXTENSION);
                        // $folder = "{$pathPrefix}{$clientDirectory}/$extension/BankUpdate/"; //O
                        $folder = "{$pathPrefix}{$clientDirectory}/{$extension}/TrustAccountUpdate/"; // U
                        checkDirPath($folder);
                        $fileName = time() . '_' . str_replace(' ', '_', basename($_FILES['file']['name']));
                        $fileName = str_replace("'", '', $fileName);
                        $uploadfile = $folder . $fileName;
                        // $filePath = "{$clientDirectory}/$extension/BankUpdate/" . $fileName; //O
                        $filePath = "{$clientDirectory}/{$extension}/TrustAccountUpdate/" . $fileName; // U

                        if ($_FILES['file']) {
                            $uploadStatus = move_uploaded_file($_FILES['file']['tmp_name'], $uploadfile);
                            if (! $uploadStatus) {
                                $view->items['statusMessage'] = 'Failed to attach the file.';
                            } else {
                                $attachment[0]['file'] = $uploadfile;
                                if ($extension == 'pdf') {
                                    $attachment[0]['content_type'] = 'application/pdf';
                                } elseif ($extension == 'txt') {
                                    $attachment[0]['content_type'] = 'text/plain';
                                }
                                // $doc['documentType'] = DOC_COMPANY_BANK;
                                $doc['documentType'] = DOC_TRUST_ACCOUNT;
                                $doc['createdBy'] = $_SESSION['un'];
                                // $doc['primaryID'] = $view->items['companyID'];
                                $doc['primaryID'] = $view->items['bankID'];
                                $doc['filename'] = $filePath;
                                $fileID = dbAddDocument($doc);
                                // $company['fileID'] = $fileID;
                                $bank['fileID'] = $fileID;
                            }
                        }
                    }

                    // Database
                    // $auditID = dbInsertTrailCompanyBank ($company); //O
                    $auditID = dbInsertToAuditTrustAccount($bank); // U

                    if (is_uploaded_file($_FILES['NoticeStrataFiles']['tmp_name']) && $_FILES['NoticeStrataFiles']) {
                        $filePath = "{$pathPrefix}{$clientDirectory}/pdf/Strata/temp/";
                        if (! file_exists($filePath)) {
                            mkdir($filePath, FILE_PERMISSION, true);
                        }
                        $folder = "{$pathPrefix}{$clientDirectory}/pdf/Strata/temp/";
                        $stratafile = $folder . $auditID . '.pdf';
                        move_uploaded_file($_FILES['NoticeStrataFiles']['tmp_name'], $stratafile);
                    }

                    // Approval E-mail
                    $recipientName = mapParameters($view->items['userDBList'], 'userID', 'fullName');
                    // //$email = new EmailTemplate ('views/emails/updatebankDetailApproval.html', SYSTEMURL);
                    // $email = new EmailTemplate ('views/emails/updateTrustAccountDetailApproval.html', SYSTEMURL);
                    $email = new EmailTemplate('views/emails/updateTrustAccountDetailApprovalWithLink.html', SYSTEMURL);
                    $email->items['recipientName'] = $recipientName[$view->items['authoriser']];
                    // $email->items['companyName'] = $company['companyName'];
                    // $email->items['bankAccountName'] = $bank['bankAccountName']; //O
                    $email->items['bankAccountName'] = $bank['originalBankAccountName']; // U
                    $email->items['reason'] = $view->items['reason'];
                    $email->items['urlApproval'] = SYSTEMURL . '/index.php?module=' . $view->items['module'] . '&command=' . $view->items['command'] . '&action=verify&auditID=' . $auditID;
                    // sendMail ($recipientEmail, $email->items['recipientName'], $email->toString(), 'Banking Detail Update', $attachment);
                    sendMail($recipientEmail, $email->items['recipientName'], $email->toString(), ucwords(strtolower($_SESSION['country_default']['trust_account'])) . ' Details Update', $attachment);

                    // Notification E-mail
                    // $username = dbGetUserName ($view->items['new']['userCreated']);
                    $username = dbGetUserName($_SESSION['user_id']);
                    $requestedByName = $username['fullName'];

                    /** Email notif to all authorisers */
                    $exclude_str = $recipientEmail;
                    $authoriser_emails_list = dbGetAuthorisersEmailList($exclude_str);
                    if (count($authoriser_emails_list ?? []) > 0) {
                        $authoriser_emails = implode('; ', array_values($authoriser_emails_list));
                        $email = new EmailTemplate('views/emails/updateTrustAccountDetailApproval.html', SYSTEMURL);
                        $email->items['bankAccountName'] = $bank['originalBankAccountName']; // U
                        $email->items['reason'] = $view->items['reason'];
                        // $email->items['recipientName'] = $recipientName[$view->items['authoriser']]; //O - uses name
                        $email->items['recipientName'] = $recipientEmail; // U - uses email
                        // $email->items['requestedByName'] = $requestedByName; //O - uses name
                        $email->items['requestedByName'] = $_SESSION['un']; // U - uses email
                        // sendMail ($authoriser_emails, null, $email->toString(), 'Trust Account Details Update on ' . $_SESSION['database'] . ' by ' . $_SESSION['un'], $attachment); //ACTUAL
                        sendMail($authoriser_emails, null, $email->toString(), ucwords(strtolower($_SESSION['country_default']['trust_account'])) . ' Details Update on ' . $_SESSION['database'] . ' by ' . $_SESSION['un'], $attachment); // TESTING-DEBUGGING ONLY - [ALL AUTHORISERS-PENDING APPROVAL]
                    }

                    // ## Commented acct manager sending of emails
                    /**
                    $email = new EmailTemplate ('views/emails/updateTrustAccountDetailNotice.html', SYSTEMURL);
                    //$email->items['bankAccountName'] = $bank['bankAccountName']; //O
                    $email->items['bankAccountName'] = $bank['originalBankAccountName']; //U
                    $email->items['reason'] = $view->items['reason'];
                    $email->items['recipientName'] = $recipientName[$view->items['authoriser']];
                    $email->items['requestedByName'] = $requestedByName;
                    sendMail ($accountManagerEmail, null, $email->toString(), 'Client Account Details Update on ' . $_SESSION['database'] . ' by ' . $_SESSION['un'], $attachment);
                     **/
                    $view->items['statusMessage'] = ucwords(strtolower($_SESSION['country_default']['trust_account'])) . ' details has been submitted for approval.';
                }
            }
        } else {
            $validationErrors[] = ucwords(strtolower($_SESSION['country_default']['trust_account'])) . ' does not exist.';
        } // U
        // else $validationErrors[] = 'Company does not exist.'; //O
    } elseif ($view->items['auditID']) {
        switch ($view->items['action']) {
            case 'verify':
                // $view->items['new'] = dbGetTrailCompanyBank ($view->items['auditID'], 'Pending');
                $view->items['new'] = dbGetAuditTrustAccount($view->items['auditID'], 0);
                $authoriser = $view->items['new']['authoriser'];

                // ## updated checker: Check security setting default first
                if ($view->items['flag_authorizer_submitter'] === true) {
                    if ($_SESSION['user_id'] == $authoriser) {
                        // $view->items['selectedAuthoriser'] = true; //O

                        /** UPDATED: Check if user is approving his/her own request */
                        if ($_SESSION['user_id'] == $view->items['new']['userCreated']) {
                            // $validationErrors[] = 'You are not allowed to approve or reject your own request. Another authoriser is required for processing this entry.';
                            $view->items['selectedAuthoriser'] = false;
                        } else {
                            $view->items['selectedAuthoriser'] = true;
                        }
                    }
                } elseif ($_SESSION['user_id'] == $authoriser) {
                    $view->items['selectedAuthoriser'] = true;
                    // O
                }


                // ## old checker
                //                if($_SESSION['user_id'] == $authoriser) {
                //                    $view->items['selectedAuthoriser'] = true;
                //                }


                if (empty($view->items['new'])) {
                    $validationErrors[] = 'Verifiable data does not exist. It may have already been approved or rejected.';
                } else {
                    // $view->items['original'] = dbGetCompany ($view->items['new']['companyID']);
                    $view->items['original'] = dbGetBankInfo($view->items['new']['bankID'], true); // second param is $includeCredentials=false
                    $username = dbGetUserName($view->items['new']['userCreated']);
                    $view->items['new']['submittedBy'] = $username['fullName'];
                    $username = dbGetUserName($view->items['new']['authoriser']);
                    $view->items['new']['authoriser'] = $username['fullName'];
                    if ($view->items['new']['filename']) {
                        $view->items['new']['downloadLink'] = $view->items['new']['filename'];
                        $view->items['new']['filename'] = basename($view->items['new']['filename']);
                    }

                    $view->items['original']['strataFile'] = is_file($pathPrefix . $clientDirectory . '/pdf/Strata/' . $view->items['new']['bankID'] . '.pdf') ? $clientDirectory . '/pdf/Strata/' . $view->items['new']['bankID'] . '.pdf' : '';
                    $view->items['new']['strataFile'] = is_file($pathPrefix . $clientDirectory . '/pdf/Strata/temp/' . $view->items['auditID'] . '.pdf') ? $clientDirectory . '/pdf/Strata/temp/' . $view->items['auditID'] . '.pdf' : '';
                    /*
                    pre_print_r($view->items['original']);
                    echo '==========<br>';
                    pre_print_r($view->items['new']);
                    */

                }
                break;
            case 'approve':
                if ($view->items['authorised']) {
                    // $updatedCompany = dbGetTrailCompanyBank ($view->items['auditID'], 'Pending');
                    $updatedBank = dbGetAuditTrustAccount($view->items['auditID'], 0);
                    $approverUserID = $updatedBank['authoriser'];
                    $requestorUserID = $updatedBank['userCreated'];
                    $reason = $updatedBank['reason'];
                    // $originalCompany = dbGetCompany ($updatedCompany['companyID']);
                    $originalBank = dbGetBankInfo($updatedBank['bankID'], true);
                    /*
                    if(((bool)$originalCompany['debtor']) && ($updatedCompany['paymentMethod'] == 1)) {
                        $updatedCompany['directBanking'] = 1;
                    }
                    else {
                        $updatedCompany['directBanking'] = 0;
                    }
                    */

                    if ($updatedBank['termNoticeStrata'] && is_file($pathPrefix . $clientDirectory . '/pdf/Strata/temp/' . $view->items['auditID'] . '.pdf')) {
                        copy($pathPrefix . $clientDirectory . '/pdf/Strata/temp/' . $view->items['auditID'] . '.pdf', $pathPrefix . $clientDirectory . '/pdf/Strata/' . $updatedBank['bankID'] . '.pdf');
                    }

                    // dbUpdateCompanyBank ($updatedCompany); //O
                    dbUpdateBankAccount($updatedBank); // U
                    // dbUpdateTrailCompanyBankStatus ($view->items['auditID'], 'Approved'); //O
                    dbUpdateAuditTrustAccountStatus($view->items['auditID'], 1); // U Approved = 1

                    // Retrieve Company Bank Details
                    // $company = dbGetCompany ($updatedCompany['companyID']);
                    $bank = dbGetBankInfo($updatedBank['bankID'], true);

                    // Notification E-mail 1
                    $requestorEmail = dbGetUserByID($requestorUserID);
                    if (isValid($requestorEmail['email'], TEXT_EMAIL, false)) {
                        // $email = new EmailTemplate ('views/emails/updateBankDetailNotify.html', SYSTEMURL);
                        $email = new EmailTemplate('views/emails/updateTrustAccountDetailNotify.html', SYSTEMURL);
                        // $email->items['companyName'] = $company['companyName'];
                        $email->items['bankAccountName'] = $bank['bankAccountName'];
                        $email->items['approvedBy'] = $_SESSION['un'];
                        // $email->items['reason'] = $view->items['new']['reason']; //O
                        $email->items['reason'] = $reason; // U
                        // sendMail ($requestorEmail['email'], null, $email->toString(), 'Banking Detail Approved for ' . $company['companyName']);
                        sendMail($requestorEmail['email'], null, $email->toString(), ucwords(strtolower($_SESSION['country_default']['trust_account'])) . ' Details Approved for ' . $bank['bankAccountName']);
                    }

                    // Notification E-mail 2
                    // ## Commented acct manager sending of emails
                    /**
                    $accountManagerEmail = dbGetParam ('ACCMGR', 'MAINT');
                    if (isValid ($accountManagerEmail, TEXT_EMAIL, false))
                    {
                        //$email = new EmailTemplate ('views/emails/updatebankDetailApproved.html', SYSTEMURL);
                        $email = new EmailTemplate ('views/emails/updateTrustAccountDetailApproved.html', SYSTEMURL);
                        //$email->items['companyName'] = $company['companyName'];
                        $email->items['bankAccountName'] = $bank['bankAccountName'];
                        $email->items['approvedBy'] = $_SESSION['un'];
                        $email->items['reason'] = $view->items['new']['reason'];
                        sendMail ($accountManagerEmail, null, $email->toString(), 'Client Account Details Approved for ' . $bank['bankAccountName'] . ' on ' . $_SESSION['database']);
                    }
                     **/

                    // Notification E-mail 3 - NOTE: This is removed since the approver is included in the authoriser list
                    /*
                    $approverEmail = dbGetUserByID($approverUserID);
                    if (isValid ($approverEmail['email'], TEXT_EMAIL, false))
                    {
                        //$email = new EmailTemplate ('views/emails/updatebankDetailApproved.html', SYSTEMURL);
                        $email = new EmailTemplate ('views/emails/updateTrustAccountDetailApproved.html', SYSTEMURL);
                        //$email->items['companyName'] = $company['companyName'];
                        $email->items['bankAccountName'] = $bank['bankAccountName'];
                        $email->items['approvedBy'] = $_SESSION['un'];
                        $email->items['reason'] = $reason; //U
                        sendMail ($approverEmail['email'], null, $email->toString(), 'Trust Account Details Approved for ' . $bank['bankAccountName'] . ' on ' . $_SESSION['database']);
                    }
                    */

                    // Notification E-mail TO ALL AUTHORISERS - APPROVED NOTIF
                    $exclude_str = $requestorEmail['email'];
                    $authoriser_emails_list = dbGetAuthorisersEmailList($exclude_str);
                    if (count($authoriser_emails_list ?? []) > 0) {
                        $getApprovedByFullName = dbGetUserName($_SESSION['user_id']);
                        $approvedByFullName = $getApprovedByFullName['fullName'];

                        $authoriser_emails = implode('; ', array_values($authoriser_emails_list));
                        $email = new EmailTemplate('views/emails/updateTrustAccountDetailApproved.html', SYSTEMURL);
                        $email->items['bankAccountName'] = $bank['bankAccountName'];
                        $email->items['approvedBy'] = $_SESSION['un']; // use email
                        // $email->items['approvedBy'] = $approvedByFullName; //use fullname
                        $email->items['reason'] = $reason; // U
                        // sendMail ($authoriser_emails, null, $email->toString(), 'Trust Account Details Approved for ' . $bank['bankAccountName'] . ' on ' . $_SESSION['database']); //ACTUAL
                        sendMail($authoriser_emails, null, $email->toString(), ucwords(strtolower($_SESSION['country_default']['trust_account'])) . ' Details Approved for ' . $bank['bankAccountName'] . ' on ' . $_SESSION['database']); // TESTING-DEBUGGING ONLY - [ALL AUTHORISERS-APPROVED NOTIF]
                    }

                    // $view->items['statusMessage'] = 'You have approved changes to the trust account details of this company. <a href="index.php?module=configuration&command=bankDetailList">Click here</a> to return to the previous page.';
                    $view->items['statusMessage'] = 'You have approved changes to the ' . strtolower($_SESSION['country_default']['trust_account']) . ' details of this company. <a href="index.php?module=configuration&command=trustAccountDetailsList">Click here</a> to return to the previous page.';
                } else {
                    $validationErrors[] = 'You are not authorised to approve the modifications made.';
                }
                break;
            case 'reject':
                if ($view->items['authorised']) {
                    // $trail = dbGetTrailCompanyBank ($view->items['auditID'], 'Pending'); //O
                    $trail = dbGetAuditTrustAccount($view->items['auditID'], 0);
                    $approverUserID = $trail['authoriser'];
                    $requestorUserID = $trail['userCreated'];
                    $reason = $trail['reason'];
                    // $company = dbGetCompany ($trail['companyID']);
                    $bank = dbGetBankInfo($trail['bankID'], true);

                    // dbUpdateTrailCompanyBankStatus ($view->items['auditID'], 'Rejected');
                    dbUpdateAuditTrustAccountStatus($view->items['auditID'], 2); // Rejected = 2

                    // Notification E-mail 1
                    $requestorEmail = dbGetUserByID($requestorUserID);
                    if (isValid($requestorEmail['email'], TEXT_EMAIL, false)) {
                        $email = new EmailTemplate('views/emails/updateTrustAccountDetailNotifyRejected.html', SYSTEMURL);
                        // $email->items['companyName'] = $company['companyName'];
                        $email->items['bankAccountName'] = $bank['bankAccountName'];
                        $email->items['rejectedBy'] = $_SESSION['un'];
                        $email->items['reason'] = $reason;
                        // sendMail ($requestorEmail['email'], null, $email->toString(), 'Trust Account Details Update Rejected for ' . $company['companyName']);
                        sendMail($requestorEmail['email'], null, $email->toString(), ucwords(strtolower($_SESSION['country_default']['trust_account'])) . ' Details Update Rejected for ' . $bank['bankAccountName']);
                    }

                    // Notification E-mail 2
                    // ## Commented acct manager sending of emails
                    /**
                    $accountManagerEmail = dbGetParam ('ACCMGR', 'MAINT');
                    if (isValid ($accountManagerEmail, TEXT_EMAIL, false))
                    {
                        //$email = new EmailTemplate ('views/emails/updatebankDetailRejected.html', SYSTEMURL);
                        $email = new EmailTemplate ('views/emails/updateTrustAccountDetailRejected.html', SYSTEMURL);
                        //$email->items['companyName'] = $company['companyName'];
                        $email->items['bankAccountName'] = $bank['bankAccountName'];
                        $email->items['rejectedBy'] = $_SESSION['un'];
                        //sendMail ($accountManagerEmail, null, $email->toString(), 'Banking Detail Update Rejected for ' . $company['companyName'] . ' on ' . $_SESSION['database']);
                        sendMail ($accountManagerEmail, null, $email->toString(), 'Client Account Details Update Rejected for ' . $bank['bankAccountName'] . ' on ' . $_SESSION['database']);
                    }
                     **/

                    // Notification E-mail 3 - NOTE: This is removed since the approver is included in the authoriser list
                    /*
                    $approverEmail = dbGetUserByID($approverUserID);
                    if (isValid ($approverEmail['email'], TEXT_EMAIL, false))
                    {
                        //$email = new EmailTemplate ('views/emails/updatebankDetailRejected.html', SYSTEMURL);
                        $email = new EmailTemplate ('views/emails/updateTrustAccountDetailRejected.html', SYSTEMURL);
                        $email->items['bankAccountName'] = $bank['bankAccountName'];
                        $email->items['rejectedBy'] = $_SESSION['un'];
                        $email->items['reason'] = $reason; //U
                        sendMail ($approverEmail['email'], null, $email->toString(), 'Trust Account Details Update Rejected for ' . $bank['bankAccountName'] . ' on ' . $_SESSION['database']);
                    }
                    */

                    // Notification E-mail TO ALL AUTHORISERS - REJECTED NOTIF
                    // $exclude_str = "'".$requestorEmail['email']."', '".$approverEmail['email']."'";
                    $exclude_str = $requestorEmail['email'];
                    $authoriser_emails_list = dbGetAuthorisersEmailList($exclude_str);
                    if (count($authoriser_emails_list ?? []) > 0) {
                        $getRejectedByFullName = dbGetUserName($_SESSION['user_id']);
                        $rejectedByFullName = $getRejectedByFullName['fullName'];

                        $authoriser_emails = implode('; ', array_values($authoriser_emails_list));
                        $email = new EmailTemplate('views/emails/updateTrustAccountDetailRejected.html', SYSTEMURL);
                        $email->items['bankAccountName'] = $bank['bankAccountName'];
                        $email->items['rejectedBy'] = $_SESSION['un']; // use email
                        // $email->items['rejectedBy'] = $approvedByFullName; //use fullname
                        $email->items['reason'] = $reason; // U
                        // sendMail ($authoriser_emails, null, $email->toString(), 'Trust Account Details Update Rejected for ' . $bank['bankAccountName'] . ' on ' . $_SESSION['database']); //ACTUAL
                        sendMail($authoriser_emails, null, $email->toString(), ucwords(strtolower($_SESSION['country_default']['trust_account'])) . ' Details Update Rejected for ' . $bank['bankAccountName'] . ' on ' . $_SESSION['database']); // TESTING-DEBUGGING ONLY - [ALL AUTHORISERS-REJECTED NOTIF]
                    }

                    $view->items['statusMessage'] = 'Changes to the ' . strtolower($_SESSION['country_default']['trust_account']) . ' details has been rejected. <a href="index.php?module=configuration&command=trustAccountDetailsList">Click here</a> to go back to the list.';
                } else {
                    $validationErrors[] = 'You are not authorised to reject the modifications made.';
                }
                break;
            case 'reassigned':
                // Validate e-mails
                // $view->items['new'] = dbGetTrailCompanyBank ($view->items['auditID'], 'Pending');
                $view->items['new'] = dbGetAuditTrustAccount($view->items['auditID'], 0);
                // $view->items['original'] = dbGetCompany ($view->items['new']['companyID']);
                $view->items['original'] = dbGetBankInfo($view->items['new']['bankID'], true);
                $username = dbGetUserName($view->items['new']['userCreated']);
                $view->items['new']['submittedBy'] = $username['fullName'];
                $userDBList = dbGetUsersForDatabase($_SESSION['clientID']);
                // $company = dbGetCompany ($view->items['new']['companyID']);
                $bank = dbGetBankInfo($view->items['new']['bankID'], true);

                // ## Check security setting for authoriser and submitter
                // ## Validate if selected authoriser from dropdown is same with current logged-in user
                if ($view->items['flag_authorizer_submitter'] === true && $view->items['authoriser'] == $_SESSION['user_id']) {
                    $validationErrors[] = 'You cannot set yourself as the authoriser for this request. Please select another user.';
                }

                if (! $view->items['authoriser']) {
                    $validationErrors[] = 'No authoriser was selected.';
                } else {
                    $recipientEmail = mapParameters($userDBList, 'userID', 'email');
                    $recipientEmail = $recipientEmail[$view->items['authoriser']];
                    if (! isValid($recipientEmail, TEXT_EMAIL, false)) {
                        $validationErrors[] = "Authoriser's e-mail address is invalid.";
                    }
                }

                if ($view->items['authoriser'] == $view->items['new']['authoriser']) {
                    $validationErrors[] = 'You are not reassigning this request. Kindly select another authoriser.';
                }


                if (noErrors($validationErrors)) {
                    // dbUpdateTrailCompanyBankAuthoriser($view->items['auditID'], $view->items['authoriser']); //O
                    dbUpdateAuditTrustAccountAuthoriser($view->items['auditID'], $view->items['authoriser']); // U

                    // Attachment
                    $attachment =  [];
                    $extension = pathinfo(basename($view->items['new']['filename']), PATHINFO_EXTENSION);
                    $attachment[0]['file'] = REPORTPATH . '/' . $view->items['new']['filename'];
                    if ($extension == 'pdf') {
                        $attachment[0]['content_type'] = 'application/pdf';
                    } elseif ($extension == 'txt') {
                        $attachment[0]['content_type'] = 'text/plain';
                    }

                    // Approval E-mail
                    $requestedByName = $username['fullName'];
                    $recipientName = mapParameters($userDBList, 'userID', 'fullName');
                    // //$email = new EmailTemplate ('views/emails/updatebankDetailApproval.html', SYSTEMURL);
                    // $email = new EmailTemplate ('views/emails/updateTrustAccountDetailApproval.html', SYSTEMURL); //O
                    $email = new EmailTemplate('views/emails/updateTrustAccountDetailApprovalWithLink.html', SYSTEMURL); // U
                    // $email->items['recipientName'] = $recipientName[$view->items['authoriser']]; //O - uses name
                    $email->items['recipientName'] = $recipientEmail; // U - uses email
                    // $email->items['requestedByName'] = $requestedByName; //O - uses name
                    $email->items['requestedByName'] = $_SESSION['un']; // U - uses email
                    // $email->items['companyName'] = $company['companyName'];
                    $email->items['bankAccountName'] = $bank['bankAccountName'];
                    $email->items['reason'] = $view->items['new']['reason'];
                    $email->items['urlApproval'] = SYSTEMURL . '/index.php?module=' . $view->items['module'] . '&command=' . $view->items['command'] . '&action=verify&auditID=' . $view->items['auditID'];
                    // sendMail ($recipientEmail, $email->items['recipientName'], $email->toString(), 'Banking Detail Update', $attachment);
                    sendMail($recipientEmail, $email->items['recipientName'], $email->toString(), ucwords(strtolower($_SESSION['country_default']['trust_account'])) . ' Details Update', $attachment);

                    $view->items['statusMessage'] = 'You have successfully reassigned this request.';
                }
                break;
            case 'viewApproved':
            case 'viewRejected':
            case 'view':
            default:
                // if ($view->items['action'] == 'viewApproved') $view->items['new'] = dbGetTrailCompanyBank ($view->items['auditID'], 'Approved');
                // elseif ($view->items['action'] == 'viewRejected') $view->items['new'] = dbGetTrailCompanyBank ($view->items['auditID'], 'Rejected');
                // else $view->items['new'] = dbGetTrailCompanyBank ($view->items['auditID']);
                if ($view->items['action'] == 'viewApproved') {
                    $view->items['new'] = dbGetAuditTrustAccount($view->items['auditID'], 1);
                } elseif ($view->items['action'] == 'viewRejected') {
                    $view->items['new'] = dbGetAuditTrustAccount($view->items['auditID'], 2);
                } else {
                    $view->items['new'] = dbGetSingleAuditTrustAccount($view->items['auditID']);
                }
                if (empty($view->items['new'])) {
                    $validationErrors[] = 'Verifiable data does not exist.';
                } else {
                    if ($view->items['action'] == 'viewApproved') {
                        // $view->items['original'] = dbGetApprovedTrailCompanyBank($view->items['auditID']); //O
                        $view->items['original'] = dbGetAuditTrustAccount($view->items['auditID'], 1); // U
                    } else {
                        // $view->items['original'] = dbGetCompany ($view->items['new']['companyID']); //O
                        $view->items['original'] = dbGetBankInfo($view->items['new']['bankID'], true); // U
                    }

                    $username = dbGetUserName($view->items['new']['userCreated']);
                    $view->items['new']['submittedBy'] = $username['fullName'];
                    $username = dbGetUserName($view->items['new']['userUpdated']);
                    $view->items['new']['modifiedBy'] = $username['fullName'];
                    if ($view->items['new']['filename']) {
                        $view->items['new']['pdfViewPath'] =  REPORTPATH . '/' . $view->items['new']['filename'];
                        $view->items['new']['downloadLink'] = $view->items['new']['filename'];
                        $view->items['new']['filename'] = basename($view->items['new']['filename']);
                    }
                }
                break;
        }
    }
    $view->items['validationErrors'] = $validationErrors;
    $view->render();
}
?>

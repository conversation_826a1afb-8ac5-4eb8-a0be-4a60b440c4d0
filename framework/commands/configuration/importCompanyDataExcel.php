<?php

use PhpOffice\PhpSpreadsheet\IOFactory;

define('TEXT_BSB', '/^\d{6,6}$$/');
if (! function_exists('array_column')) {

    function array_column($array, $column_name)
    {

        return array_map(fn($element) => $element[$column_name], $array);

    }

}

/**
 * @ function dbInsertPropertyContactDetails
 *  save contact details to [pmpj_p_phone] table
 **/
function dbAddImportCompany($company)
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);
    extract($company);
    $sql = "
    INSERT INTO pmco_company (
                    pmco_code,
                    pmco_name,
                    pmco_street,
                    pmco_city,
                    pmco_state,
                    pmco_country,
                    pmco_postcode,
                    pmco_supplier,
                    pmco_debtor,
                    pmco_dir_bank,
                    pmco_b_bsb,
                    pmco_b_acc_no,
                    pmco_b_name,
                    pmco_b_acc_name,
                    pmco_gst_no,
                    pmco_gst_code,
                    pmco_owner,
                    pmco_o_pay_acc,
                    pmco_o_chart,
                    pmco_s_type,
                    pmco_s_agent,
                    pmco_d_chq_days,
                    pmco_active,
                    pmco_email,
                    pmco_invoice_mask,
                    pmco_bpay_ref,
                    pmco_pay_method
               ) VALUES (
                '{$companyID}',
                   '{$companyName}',
                   '{$address}',
                   '{$city}',
                    '{$state}',
                    '{$country}',
                   '{$postCode}',
                    '{$supplier}',
                    '{$debtor}',
                    '{$directBanking}',
                 '{$bsbNumber}',
                    '{$bankAccountNumber}',
                    '{$bankName}',
                    '{$bankAccountName}',
                   '{$businessNumber}',
                    '{$taxCode}',
                  '{$owner}',
                   '{$ownerPaymentAccount}',
                   '{$ownerChart}',
                    '{$supplierType}',
                    '{$supplierAgent}',
                    '{$chequeDays}',
                    '{$active}',
                   '{$email}',
                    '{$invoiceMask}',
                    '{$bpayBillerCode}',
                    '{$paymentMethod}'
               )
    ";

    return str_replace("'',", 'NULL,', $sql);
    // return $dbh->executeNonQuery($sql);
}


/**
 * @ function dbGetTaxRatesByCode
 *  return country sql data
 **/
function dbGetTaxRatesByCode()
{
    global $dbh, $clientDB;
    $dbh->selectDatabase($clientDB);
    $sql = 'SELECT pmgs_code as param FROM pmgs_gst';

    return $dbh->executeSet($sql);
}


/**
 * @ function dbGetAllStates
 *  return states sql data
 **/
function dbGetAllStates($countryCode = 'AU')
{
    global $dbh;
    $dbh->selectDatabase(SYSTEMDB);
    $sql = "SELECT stateCode as code, stateName as name, countryName as country
			FROM states
			JOIN countries ON states.countryCode = countries.countryCode
			WHERE states.countryCode = '" . $countryCode . "'
			order by country, name";

    return $dbh->executeSet($sql);
}

/**
 * @ function dbGetStates
 *  return states sql data
 **/
function dbGetStatesByCountryCode($country)
{
    global $dbh;
    $country = $dbh->prepareField($country);
    $dbh->selectDatabase(SYSTEMDB);
    $sql = "SELECT stateCode as param
			FROM states
			JOIN countries ON states.countryCode = countries.countryCode
			WHERE states.countryCode = '{$country}'"; // AND stateCode = '{$state}'";

    return $dbh->executeSet($sql);

}


/**
 * @ function dbGetParamsByCode
 *  return supplier param type sql data
 **/
function dbGetParamsByCode($type)
{

    global $dbh, $clientDB;
    $dbh->selectDatabase($clientDB);
    $typeID = $dbh->prepareField($type);
    $sql = "SELECT pmzz_code as param FROM pmzz_param
			 WHERE pmzz_par_type = '{$typeID}'";

    return $dbh->executeSet($sql);

}

/**
 * @ function dbGetCompany
 *  return company sql data
 **/
function dbGetCompanyCode()
{

    global $dbh, $clientDB;
    $dbh->selectDatabase($clientDB);
    $sql = 'SELECT pmco_code as param FROM pmco_company';

    return $dbh->executeSet($sql);

}

/**
 * @ function dbGetABN
 *  return company abn number sql data
 **/
function dbGetABN()
{

    global $dbh, $clientDB;
    $dbh->selectDatabase($clientDB);
    $sql = 'SELECT pmco_gst_no as param FROM pmco_company';

    return $dbh->executeSet($sql);

}

function find_array($arr, $param)
{
    foreach ($arr as $c) {
        if ($c['param'] == $param) {
            return true;
        }
    }

    return false;
}

/**
 * @ function get_substitute_values
 *  return substitute values
 **/
function get_substitute_values($view)
{
    $substitute_values = [];

    foreach ($view->items as $key => $value) {

        if (strpos($key, 'err_') === 0) {
            // echo $pos . '~' .$key.'<br> ~';
            [$x, $y] = explode(',', substr($key, 4));
            $substitute_values[$x][$y] = $value;
        }
    }

    return $substitute_values;
}

function validate_file($rowArrData, $substitute_values = '', $ignoreDuplicateABN = false, $country = 'AU')
{

    $error_lines = [];
    $comp_codes = [];
    $abn_no = [];

    //  Loop through each row of the worksheet in turn
    foreach ($rowArrData as $key => $row) {
        //  Read a row of data into an array
        if ($key > 0) {
            $error_lines_temp = [];

            if (isset($substitute_values[$key])) {
                $row = array_replace($row, $substitute_values[$key]);
            }

            $error_lines_temp = check_row($key, $row, $comp_codes, $abn_no, $ignoreDuplicateABN, $country);

            if ($error_lines_temp > 0) {
                $error_lines += $error_lines_temp;
            }

            $comp_codes[] = $row[0];
            $abn_no[] = $row[2];
        }

    }

    return $error_lines;
}

function check_row($key, $row, $comp_codes, $abn_no, $ignoreDuplicateABN = false, $country = 'AU')
{
    $cdf_ABN = cdf_displayLabel('business_label', $country);
    $cdf_displayState = cdf_isShown('display_state', $country);
    $cdf_displayBSB = cdf_isShown('display_bsb', $country);
    $cdf_postCodeLength = cdf_displayLabel('post_code_length', $country);
    $cdf_minPostCodeLength = cdf_displayLabel('post_code_min_length', $country);
    $cdf_businessLength = cdf_displayLabel('business_length', $country);
    $cdf_bankAcctLength = cdf_displayLabel('bank_account_length', $country);
    $cdf_suburb = cdf_isShown('suburb', $country);
    $cdf_validCountryCode = cdf_validCountryCode(
        $country
    ); // if chosen country does not exist in the json file, use the client country
    $bsb_label = cdf_displayLabel('bsb_label', $country);
    $bsb_length = cdf_displayLabel('bsb_length', $country);
    cdf_displayLabel('bsb_format', $country);

    // array deconstruction
    [
        $row_company_code,
        $row_company_name,
        $row_abn,
        $row_address,
        $row_suburb,
        $row_state,
        $row_post_code,
        $row_email,
        $row_tax_status,
        $row_is_debtor,
        $row_is_owner,
        $row_is_supplier,
        $row_is_agent,
        $row_company_type,
        $row_payment_method ,
        $row_bsb,
        $row_bank_account_number,
        $row_account_name,
        $row_bank_name,
        $row_biller_code
    ] = $row;

    $errors = [];
    // check for duplicate within the file
    $error_line_company_code = $key . ',0';
    $line_no = array_search($row_company_code, $comp_codes, true);
    if ($line_no !== false) {
        $errors[$error_line_company_code] = 'Duplicate company code with line : ' . ($line_no + 1);
    }

    // company code
    if (! isValid($row_company_code, TEXT_KEY, false)) {
        $errors[$error_line_company_code] = 'Please make sure your company code is filled out and valid';
    }

    // check if company code exist
    if (isCompanyExist($row_company_code)) {
        $errors[$error_line_company_code] = 'Company Code already exists in the system';
    }

    if (strlen($row_company_code) > 10) {
        $errors[$error_line_company_code] = 'Company Code should be less than 10 characters';
    }

    if (! preg_match('/^[a-zA-Z0-9-]+$/', $row_company_code)) {
        $errors[$error_line_company_code] = 'Please make sure your Company Code is filled out and does not contain special characters ' . "( .?/;:,\\'&_)";
    }

    // check if Company Name contains data
    $error_line_company_name = $key . ',1';
    if ($row_company_name == '') {
        $errors[$error_line_company_name] = 'Invalid Type. Please fill out with valid data';
    }

    // check if ABN Name contains data
    $error_line_abn = $key . ',2';
    $row_abn = trim($row_abn, ' ');
    if (! empty($row_abn) && ! isValid($row_abn, TEXT_INT, false)) {
        $errors[$error_line_abn] = 'Invalid Type. Please fill out with valid data';
    }

    // check for duplicate within the file
    if (! empty($row_abn)) {
        $line_no1 = array_search($row_abn, $abn_no, true);
        if (! cdf_validate($row_abn, 'business_length', $country)) {
            $errors[$error_line_abn] = "{$cdf_ABN} should be " . $cdf_businessLength . ' digits (no spaces)';
        }

        if ($line_no1 !== false && ! $ignoreDuplicateABN) {
            $errors[$error_line_abn] = "Duplicate {$cdf_ABN} Number with line : " . ($line_no1 + 1);
        }

        if (isABNExist($row_abn) && ! $ignoreDuplicateABN) {
            $errors[$error_line_abn] = "{$cdf_ABN} number already exist in the system";
        }

        if (! isValid($row_abn, TEXT_INT, false)) {
            $errors[$error_line_abn] = 'Invalid Type. Please fill out with valid data';
        }
    }

    $error_line_address = $key . ',3';
    if ($row_address == '') {
        $errors[$error_line_address] = 'Please make sure your address is filled out and valid';
    }

    $error_line_suburb = $key . ',4';
    if ($row_suburb == '') {
        $errors[$error_line_suburb] = 'Please make sure your ' . strtolower($cdf_suburb) . ' is filled out and valid';
    }

    // check state if exist check if required
    if ($cdf_displayState) {
        $error_line_state = $key . ',5';
        if (! isValid($row_state, TEXT_KEY, false)) {
            $errors[$error_line_state] = 'Please make sure your state is filled out and valid';
        }

        if (! isStateExist($row_state)) {
            $errors[$error_line_state] = 'State does not exists in the system. Please fill out with valid data';
        }

    }

    // check post code
    $error_line_post_code = $key . ',6';
    if ($cdf_validCountryCode == 'GB') {
        // check if the fetched valid country is 'GB' then allow spaces in the post code value (currently, only GB post codes have spaces)
        if (! isValid($row_post_code, TEXT_ALPHANUMERIC_WHITESPACE, false)) {
            $errors[$error_line_post_code] = 'Please make sure your post code is filled out and valid';
        }
    } elseif (! isValid($row_post_code, TEXT_ALPHANUMERIC, false)) {
        $errors[$error_line_post_code] = 'Please make sure your post code is filled out and valid';
    }

    // check post code length
    $proceedToFormat = false;
    if ($cdf_postCodeLength == $cdf_minPostCodeLength) {
        // post code length is fixed
        if (strlen($row_post_code) != $cdf_postCodeLength) {
            $errors[$error_line_post_code] = 'Please make sure your post code is ' . $cdf_postCodeLength . ' digits and valid';
        } else {
            $proceedToFormat = true;
        }
    } elseif (strlen($row_post_code) >= $cdf_minPostCodeLength && strlen($row_post_code) <= $cdf_postCodeLength) {
        // post code length varies in format
        $proceedToFormat = true;
    } else {
        $errors[$error_line_post_code] = 'Please make sure your post code is ' . $cdf_minPostCodeLength . ' to ' . $cdf_postCodeLength . ' characters and valid';
    }

    if ($proceedToFormat) {
        $postcodeValidation = cdf_validate_postcode($row_post_code, $country);
        if (! $postcodeValidation->valid) {
            $errors[$error_line_post_code] = $postcodeValidation->error;
        }
    }

    // check email
    $error_line_email = $key . ',7';
    if ($row_email != '') {
        if (strpos($row_email, ',') !== false) {
            $errors[$error_line_email] = 'Please make sure your emails are separated by a semicolon ";" ';
        } else {
            $emails_arr = explode(';', $row_email);

            foreach ($emails_arr as $email) {
                // if(!isValid(trim($email),TEXT_EMAIL,false))
                if (! filter_var(trim($email), FILTER_VALIDATE_EMAIL)) {
                    $errors[$error_line_email] .= trim($email) . ' is invalid. ';
                }
            }

        }
    }

    // check tax status
    $error_line_status = $key . ',8';
    if (! isValid($row_tax_status, TEXT_KEY, false)) {
        $errors[$error_line_status] = 'Please make sure your tax status is filled out and valid';
    }

    if (! isTaxStatusExist($row_tax_status)) {
        $errors[$error_line_status] = 'Tax Status does not exists in the system. Please fill out with valid data';
    }

    // check if Debtor is string and contains Yes or No data
    $error_line_is_debtor = $key . ',9';
    if ($row_is_debtor != '' && (strcmp($row_is_debtor, 'Yes') != 0 && strcmp($row_is_debtor, 'No') != 0)) {
        $errors[$error_line_is_debtor] = 'Invalid debtor type (Supply with Yes/No)';
    }

    // check if Owner is string and contains Yes or No data
    $error_line_is_owner = $key . ',10';
    if (! isValid($row_is_owner, TEXT_KEY, false) && (strcmp($row_is_owner, 'Yes') != 0) && strcmp($row_is_owner, 'No') != 0) {
        $errors[$error_line_is_owner] = 'Invalid owner type (Supply with Yes/No)';
    }

    // check if Supplier is string and contains Yes or No data
    $error_line_is_supplier = $key . ',11';
    if (! isValid($row_is_supplier, TEXT_KEY, false) && (strcmp($row_is_supplier, 'Yes') != 0) && strcmp($row_is_supplier, 'No') != 0) {
        $errors[$error_line_is_supplier] = 'Invalid supplier type (Supply with Yes/No)';
    }

    // check if Agent is string and contains Yes or No data
    $error_line_is_agent = $key . ',12';
    if (! isValid($row_is_agent, TEXT_KEY, false) && (strcmp($row_is_agent, 'Yes') != 0) && strcmp($row_is_agent, 'No') != 0) {
        $errors[$error_line_is_agent] = 'Invalid agent type (Supply with Yes/No)';
    }

    // check if company Type contains Yes  data
    $error_line_company_type = $key . ',13';
    if (! empty($row_company_type) && ! isCompanyTypeExist($row_company_type)) {
        // check if type code exist
        $errors[$error_line_company_type] = 'Company Type does not exists in the system. Please fill out with valid data';
    }

    // check payment method
    $error_line_payment_method = $key . ',14';
    if (! isValid($row_payment_method, TEXT_INT, false) || ($row_payment_method < 1 || $row_payment_method > 3)) {
        $errors[$error_line_payment_method] = 'Please make sure your payment method is filled out and valid';
    }

    if ($country != 'AU' && $row_payment_method == 2) {
        $errors[$error_line_payment_method] = 'Please make sure to select a valid payment method';
    }

    if ($cdf_displayBSB && $row_payment_method === 1 && strlen($row_bsb) !== $bsb_length) {
        // check BSB
        $error_line_bsb = $key . ',15';
        $errors[$error_line_bsb] = 'Please make sure your ' . $bsb_label . ' is ' . $bsb_length . ' digits and valid';
    }

    // check Bank Account Number
    $error_line_bank_account_number = $key . ',16';
    if (! isValid($row_bank_account_number, TEXT_KEY, false) && $row_payment_method == 1) {
        $errors[$error_line_bank_account_number] = 'Please make sure your Bank Account Number is filled out and valid';
    }

    if (! cdf_validate($row_bank_account_number, 'bank_account_length', $country) && $row_payment_method == 1) {
        $errors[$error_line_bank_account_number] = 'Bank Account Number should not exceed ' . $cdf_bankAcctLength . ' digits';
    }

    // check Account Name
    $error_line_account_name = $key . ',17';
    // if payment method is 1
    if ($row_payment_method == 1) {
        if ($row_account_name == '') {
            $errors[$error_line_account_name] = 'Please make sure your Account Name is filled out and valid';
        }

        if (! preg_match('/^[a-zA-Z0-9 ]+$/', $row_account_name)) {
            $errors[$error_line_account_name] = 'Please make sure your Account Name does not contain special characters (.?/;:,\\\'&_ )';
        }
    } elseif ($row_account_name) {
        if (! preg_match('/^[a-zA-Z0-9 ]+$/', $row_account_name)) {
            $errors[$error_line_account_name] = 'Please make sure your Account Name does not contain special characters (.?/;:,\\\'&_ )';
        }
    }

    $error_line_biller_code = $key . ',19';
    // check Bpay Biller Code
    if (cdf_displayBPay($country) && (! isValid($row_biller_code, TEXT_KEY, false) && $row_payment_method == 2)) {
        $errors[$error_line_biller_code] = 'Please make sure your BPay Biller Code is filled out and valid';
    }


    return $errors;
}

/**
 * @return bool
 */
function isCompanyExist($row_company_code)
{
    return find_array($_SESSION['importCompany']['company'], $row_company_code);
}

/**
 * @return bool
 */
function isABNExist($row_abn)
{
    return find_array($_SESSION['importCompany']['abn'], $row_abn);
}

/**
 * @return bool
 */
function isStateExist($row_state)
{
    return find_array($_SESSION['importCompany']['statesCode'], $row_state);
}

/**
 * @return bool
 */
function isTaxStatusExist($row_tax_status)
{
    return find_array($_SESSION['importCompany']['taxRateByCode'], $row_tax_status);
}

/**
 * @return bool
 */
function isCompanyTypeExist($row_company_type)
{
    return find_array($_SESSION['importCompany']['params'], strtoupper($row_company_type));
}

function getHiddenCols($country)
{
    $hiddenCols = [];
    if (! cdf_isShown('display_state', $country)) {
        $hiddenCols[] = 5;
    }

    if (! getDisplayBsbFromSession()) {
        $hiddenCols[] = 15;
    }

    if (! cdf_displayBPay($country)) {
        $hiddenCols[] = 19;
    }

    return $hiddenCols;
}


function importCompanyDataExcel(&$context)
{
    global $clientDirectory, $pathPrefix;
    $view = (isPostBack()) ? new UserControl(userViews(), '/configuration/importCompanyDataExcel.html') : new MasterPage(userViews(), '/configuration/importCompanyDataExcel.html');

    $view->setSection($context['module']);
    $view->bindAttributesFrom($context);
    $view->bindAttributesFrom($_REQUEST);

    $view->items['last_error'] = $context['last_error'];

    // for internationalization
    $defaultCountryCode = $_SESSION['country_code'];

    if (! $view->items['country']) {
        $view->items['country'] = $defaultCountryCode;
    }

    $cdf_ABN = cdf_displayLabel('business_label', $view->items['country']);
    $cdf_displayState = cdf_isShown('display_state', $view->items['country']);
    $cdf_displayBSB = cdf_isShown('display_bsb', $view->items['country']);
    cdf_displayBPay($view->items['country']);
    $cdf_businessPrefix = cdf_displayLabel('business_prefix', $view->items['country']);
    $cdf_suburb = cdf_displayLabel('suburb', $view->items['country']);
    $cdf_bsbLabel = cdf_displayLabel('bsb_label', $view->items['country']);

    $error_lines = [];

    $substitute_values = get_substitute_values($view);

    $_SESSION['importCompany']['company'] = dbGetCompanyCode();
    $_SESSION['importCompany']['abn'] = dbGetABN();
    $_SESSION['importCompany']['params'] = dbGetParamsByCode('SUPPLIER');
    $_SESSION['importCompany']['statesCode'] = dbGetStatesByCountryCode($view->items['country']);
    $_SESSION['importCompany']['taxRateByCode'] = dbGetTaxRatesByCode();
    // for DL Template
    if ($view->items['action'] == 'confirmImport') {
        $error_lines = validate_file($_SESSION['importCompany']['sheet'], $substitute_values, true, $view->items['country']); // ignore ABN
        if (count($error_lines) > 0) {
            $validationErrors[0] = 'Validation failed. Place the mouse pointer on the red cell to view its error.';
        }

        $hiddenCols = getHiddenCols($view->items['country']);
        $new_sheet = [];

        // put substitute value with null data..
        $newArray = array_keys($error_lines);
        foreach ($newArray as $val) {
            $arr = explode(',', $val);
            if (array_key_exists($arr[0], $substitute_values)) {
                if (! array_key_exists($arr[1], $substitute_values[$arr[0]])) {
                    $substitute_values[$arr[0]][$arr[1]] = null;
                }
            } else {
                $substitute_values[$arr[0]][$arr[1]] = null;
            }
        }

        // end empty substitute
        foreach ($_SESSION['importCompany']['sheet'] as $key => $v) {

            if (isset($substitute_values[$key])) {
                $v = array_replace($v, $substitute_values[$key]);
                $new_sheet[] = $v;
            } else {
                $new_sheet[] = $v;
            }

        }


        if (noErrors($validationErrors)) {
            global $dbh, $clientDB;
            $dbh->selectDatabase($clientDB);
            $sql = "BEGIN TRANSACTION\n";
            $sql .= "BEGIN TRY\n";
            foreach ($new_sheet as $key => $row) {
                if ($key > 0) {
                    foreach ($hiddenCols as $ind) { // set to blank for hidden columns
                        $row[$ind] = '';
                    }

                    $company =  [];
                    $company['companyID'] = strtoupper($row[0]);
                    $company['companyName'] = str_replace("'", "''", $row[1]);
                    $company['address'] =  str_replace("'", "''", $row[3]);
                    $company['city'] =  str_replace("'", "''", $row[4]);
                    $company['state'] = str_replace("'", "''", $row[5]);
                    $company['country'] =  $view->items['country']; // 'AU';
                    $company['postCode'] =  $row[6];
                    $company['supplier'] =  (strtoupper($row[11]) === 'YES' ? 1 : 0);
                    $company['debtor'] = (strtoupper($row[9]) === 'YES' ? 1 : 0);
                    $company['bsbNumber'] = $row[15];
                    $company['bankAccountNumber'] = $row[16];
                    $company['bankAccountName'] =  str_replace("'", "''", $row[17]);
                    $company['bankName'] = str_replace("'", "''", $row[18]);
                    $company['businessNumber'] = $row[2];
                    $company['taxCode'] = $row[8];
                    $company['owner'] =  (strtoupper($row[10]) === 'YES' ? 1 : 0);
                    $company['ownerPaymentAccount'] = dbGetOwnerRemittancePaymentAccount(); // '2902';
                    $company['ownerChart'] = null;
                    $company['supplierType'] = $row[13];
                    $company['supplierAgent'] = (strtoupper($row[12]) === 'YES' ? 1 : 0);
                    $company['chequeDays'] = 4;
                    $company['active'] = 1;
                    $company['email'] = $row[7];
                    $company['invoiceMask'] = '';
                    $company['paymentMethod'] = (int) $row[14];
                    $company['directBanking'] = ($row[14] == PAY_CHQ) ? 0 : 1;
                    $company['bpayBillerCode'] = $row[19];
                    $sql .=  dbAddImportCompany($company);
                }
            }

            $sql .= "COMMIT TRANSACTION\n";
            $sql .= "END TRY\n";

            $sql .= "BEGIN CATCH\n";
            $sql .= "IF @@TRANCOUNT > 0\n";
            $sql .=	"ROLLBACK TRANSACTION\n";
            $sql .=	"END CATCH\n";
            $dbh->executeNonQuery($sql);
            unset($_SESSION['importCompany']);
            unset($view->items['columnCounter']);

            dbInsertImportLog($view->items['importPage'], decodeParameter($view->items['uploadFileForDL']), $view->items['note'], $view->items['finalImport'], 0);
            $view->items['statusMessage'] = $view->items['importPage'] . ' Imported Successfully';

        } else {
            unset($_SESSION['importCompany']['sheet']);
            unset($substitute_values);
            $_SESSION['importCompany']['sheet'] = $new_sheet;
            $view->items['sheet'] = $new_sheet;
            $view->items['columnCounter'] = $_SESSION['importCompany']['columnCounter'];
        }

    } elseif ($view->items['action'] == 'confirmIgnoreImport') {


        $new_sheet = [];
        $error_sheet = [];
        $rowError = null;
        $view->items['import_type'] = 1;

        $error_lines = validate_file($_SESSION['importCompany']['sheet'], $substitute_values, true, $view->items['country']); // ignore ABN

        foreach ($_SESSION['importCompany']['sheet'] as $key => $v) {


            if ($key <= 0) {
                $error_sheet[] = $v;
            }

            // get the row of the table with error
            $rowKey = null;
            $errorPerRow = 0;
            foreach ($v as $k => $d) {

                if (array_key_exists($key . ',' . $k, $error_lines)) {
                    if ((strpos($error_lines[$key . ',' . $k], 'Duplicate ABN') !== false || strpos($error_lines[$key . ',' . $k], 'ABN number') !== false) && $errorPerRow <= 0) { // Ignore ABN duplication
                        $rowKey = null;
                    } else {
                        $rowKey = $key;
                        $errorPerRow++;
                    }
                }


            }

            if ($rowKey != '') {
                if ($rowKey = $key) {
                    // push the row data with error
                    if (isset($substitute_values[$key])) {
                        $v = array_replace($v, $substitute_values[$key]);
                        $error_sheet[] = $v; // push the row data with error

                    } else {
                        $error_sheet[] = $v;
                        // push the row data with error
                    }

                }
            } elseif (isset($substitute_values[$key])) {
                // push the row data without error to save
                $v = array_replace($v, $substitute_values[$key]);
                $new_sheet[] = $v;
                unset($substitute_values[$key]);
            } else {
                $new_sheet[] = $v;
                unset($substitute_values[$key]);
            }


        }

        if (count($substitute_values ?? []) > 0) {
            $substitute_values = array_combine(range(1, count($substitute_values)), array_values($substitute_values));
        }

        $error_lines = validate_file($error_sheet, $substitute_values, false, $view->items['country']);

        $importData = (count($new_sheet ?? []) - 1);
        if ($importData > 0) {
            global $dbh, $clientDB;
            $dbh->selectDatabase($clientDB);

            $hiddenCols = getHiddenCols($view->items['country']);

            foreach ($new_sheet as $key => $row) {
                if ($key > 0) {
                    foreach ($hiddenCols as $ind) { // set to blank for hidden columns
                        $row[$ind] = '';
                    }

                    $sql = "BEGIN TRANSACTION\n";
                    $sql .= "BEGIN TRY\n";
                    $company =  [];
                    $company['companyID'] = strtoupper($row[0]);
                    $company['companyName'] = str_replace("'", "''", $row[1]);
                    $company['address'] =  str_replace("'", "''", $row[3]);
                    $company['city'] =  str_replace("'", "''", $row[4]);
                    $company['state'] = str_replace("'", "''", $row[5]);
                    $company['country'] =  'AU';
                    $company['postCode'] =  $row[6];
                    $company['supplier'] =  (strtoupper($row[11]) === 'YES' ? 1 : 0);
                    $company['debtor'] = (strtoupper($row[9]) === 'YES' ? 1 : 0);
                    $company['bsbNumber'] = $row[15];
                    $company['bankAccountNumber'] = $row[16];
                    $company['bankAccountName'] =  str_replace("'", "''", $row[17]);
                    $company['bankName'] =  str_replace("'", "''", $row[18]);
                    $company['businessNumber'] = $row[2];
                    $company['taxCode'] = $row[8];
                    $company['owner'] =  (strtoupper($row[10]) === 'YES' ? 1 : 0);
                    $company['ownerPaymentAccount'] = dbGetOwnerRemittancePaymentAccount(); // '2902';
                    $company['ownerChart'] = null;
                    $company['supplierType'] = $row[13];
                    $company['supplierAgent'] = (strtoupper($row[12]) === 'YES' ? 1 : 0);
                    $company['chequeDays'] = 4;
                    $company['active'] = 1;
                    $company['email'] = $row[7];
                    $company['invoiceMask'] = '';
                    $company['paymentMethod'] = (int) $row[14];
                    $company['directBanking'] = ($row[14] == PAY_CHQ) ? 0 : 1;
                    $company['bpayBillerCode'] = $row[19];
                    $sql .=  dbAddImportCompany($company);
                    $sql .= "COMMIT TRANSACTION\n";
                    $sql .= "END TRY\n";

                    $sql .= "BEGIN CATCH\n";
                    $sql .=	"ROLLBACK TRANSACTION\n";
                    $sql .=	"END CATCH\n";

                    $dbh->executeNonQuery($sql);
                }
            }

            unset($_SESSION['importCompany']);
            unset($view->items['columnCounter']);
            $string = 'Row';
            if ($importData > 1) {
                $string = 'Rows';
            }

            dbInsertImportLog($view->items['importPage'], decodeParameter($view->items['uploadFileForDL']), $view->items['note'], $view->items['finalImport'], 1);
            $view->items['statusMessage'] = $view->items['importPage'] . ' Imported Successfully';
        }

        if (count($error_sheet ?? []) > 1) {
            $validationErrors[0] = 'Validation failed. Place the mouse pointer on the red cell to view its error.';
            $view->items['validationErrors'] = $validationErrors;
            unset($_SESSION['importCompany']['sheet']);
            $view->items['substitute_values'] = $substitute_values;
            $_SESSION['importCompany']['sheet'] = $error_sheet;
            $view->items['sheet'] = $error_sheet;
            $view->items['columnCounter'] = $_SESSION['importCompany']['columnCounter'];

        } else {
            $view->items['validationErrors'] = null;
            $view->items['sheet'] = null;
        }



    } elseif ($view->items['action'] == 'DLTemplate') {
        $filePath = UPLOAD_BASE . '/templates/company_template.xlsx';
        $report = new XLSDataReport($filePath, 'Company Upload Template');
        $report->enableFormatting = true;

        $abnHeader = $cdf_ABN;
        if ($cdf_businessPrefix && $cdf_businessPrefix != '') {
            $abnHeader = $abnHeader . ' (do not include the ' . $cdf_businessPrefix . ' prefix)';
        }

        $report->addColumn('company_code', 'Company Code', 200, 'left', '@', $report->styleBold);
        $report->addColumn('company_name', 'Company Name', 200, 'left', '@', $report->styleBold);
        $report->addColumn('abn', $abnHeader, 200, 'left', '@', $report->styleBold);
        $report->addColumn('address', 'Address', 200, 'left', '@', $report->styleBold);
        $report->addColumn('suburb', ucwords(strtolower($cdf_suburb)), 200, 'left', '@', $report->styleBold);
        $report->addColumn('state', 'State', 200, 'left', '@', $report->styleBold);
        $report->addColumn('post_code', 'Post Code', 200, 'left', '@', $report->styleBold);
        $report->addColumn('email', 'Contact Email', 200, 'left', '@', $report->styleBold);
        $report->addColumn('tax_status', 'Tax Status', 200, 'left', '@', $report->styleBold);
        $report->addColumn('debtor', 'is Tenant', 200, 'left', '@', $report->styleBold);
        $report->addColumn('owner', 'is Owner', 200, 'left', '@', $report->styleBold);
        $report->addColumn('supplier', 'is Supplier', 200, 'left', '@', $report->styleBold);
        $report->addColumn('agent', 'is Agent', 200, 'left', '@', $report->styleBold);
        $report->addColumn('type', 'Company Type', 200, 'left', '@', $report->styleBold);
        $report->addColumn(
            'preferred_payment_method',
            'Preferred Payment Method',
            200,
            'left',
            '@',
            $report->styleBold
        );
        $report->addColumn('bsb', $cdf_bsbLabel, 200, 'left', '@', $report->styleBold);
        $report->addColumn('bank_account_no', 'Bank Account Number', 200, 'left', '@', $report->styleBold);
        $report->addColumn('account_name', 'Account Name', 200, 'left', '@', $report->styleBold);
        $report->addColumn('bank_name', 'Bank Name', 200, 'left', '@', $report->styleBold);
        $report->addColumn('bpay', 'Bpay Biller Code', 200, 'left', '@', $report->styleBold);
        // $report->addColumn ('country', 'Country', 200, 'left', '@', $report->styleBold);

        $hiddenCols = getHiddenCols($view->items['country']);

        foreach ($hiddenCols as $ind) { // hide template columns
            $report->hideCell(numberToLetter($ind + 1));
        }

        $report->renderHeader();
        $sheet_ind = 0;
        $c = 0;
        foreach ($report->columns as $cols) {
            $c++;
            $report->setColumnWidth(numberToLetter($c), 'auto');
        }

        // for sample
        $sheet_ind++;
        $report->setSheetDetails($sheet_ind, 'Sample');
        $report->line = 1;
        $report->renderHeader();

        if ($view->items['country'] == 'AU') {
            $sample[] = [
                'company_code' => 'ABCB',
                'company_name' => 'ABC Building',
                'abn' => '***********',
                'address' => 'Unit 1, 165 Main Street',
                'suburb' => 'Perth',
                'state' => 'WA',
                'post_code' => '1560',
                'email' => '<EMAIL>',
                'tax_status' => 'GSTFREE',
                'debtor' => 'No',
                'owner' => 'No',
                'supplier' => 'Yes',
                'agent' => 'No',
                'type' => 'GENERAL',
                'preferred_payment_method' => '1',
                'bsb' => '',
                'bank_account_no' => '',
                'account_name' => '',
                'bank_name' => '',
                'bpay' => '788448',
            ];
        } else {
            $sample[] = [
                'company_code' => 'ABCB',
                'company_name' => 'ABC Building',
                'abn' => '***********',
                'address' => 'Unit 1, 165 Main Street',
                'suburb' => 'Perth',
                'state' => 'WA',
                'post_code' => '1560',
                'email' => '<EMAIL>',
                'tax_status' => 'GSTFREE',
                'debtor' => 'No',
                'owner' => 'No',
                'supplier' => 'Yes',
                'agent' => 'No',
                'type' => 'GENERAL',
                'preferred_payment_method' => '3',
                'bsb' => '',
                'bank_account_no' => '',
                'account_name' => '',
                'bank_name' => '',
            ];
        }

        // hide columns for sample
        if (! $cdf_displayState) {
            $report->hideCell(numberToLetter(6));
        }

        // hide state
        if (! $cdf_displayBSB) {
            $report->hideCell(numberToLetter(16));
        }

        // hide BSB
        if (! cdf_displayBPay($view->items['country'])) {
            $report->hideCell(numberToLetter(20));
        } // hide BSB

        $report->renderData($sample);

        if ($cdf_displayState) {
            // for state
            $sheet_ind++;
            $data = dbGetAllStates($view->items['country']);
            $report->setSheetDetails($sheet_ind, 'State List');
            $report->line = 1;
            $report->resetColumns();
            $report->addColumn('code', 'Code', 200, 'left', '@', $report->styleBold);
            $report->addColumn('name', 'State Name', 200, 'left', '@', $report->styleBold);
            $report->addColumn('country', 'Country', 200, 'left', '@', $report->styleBold);
            $report->renderHeader();
            $report->renderData($data);
        }

        // for Supplier Type
        $sheet_ind++;
        $data = dbGetParams('SUPPLIER');
        $report->setSheetDetails($sheet_ind, 'Supplier');
        $report->line = 1;
        $report->resetColumns();
        $report->addColumn('parameterID', 'Code', 200, 'left', '@', $report->styleBold);
        $report->addColumn('parameterDescription', 'Description', 200, 'left', '@', $report->styleBold);
        $report->renderHeader();
        $report->renderData($data);

        // for Tax
        $sheet_ind++;
        $data = dbGetTaxRates();

        foreach ($data as $key => $item) {
            $data[$key]['taxDescription'] = str_replace('GST', cdf_displayLabel('tax_label', $view->items['country']), $item['taxDescription']);
        }

        $report->setSheetDetails($sheet_ind, 'Tax Codes');
        $report->line = 1;
        $report->resetColumns();
        $report->addColumn('taxCode', 'Tax Code', 200, 'left', '@', $report->styleBold);
        $report->addColumn('taxDescription', 'Tax Description', 200, 'left', '@', $report->styleBold);
        $report->renderHeader();
        $report->renderData($data);

        // for Payment Method
        $sheet_ind++;
        if ($view->items['country'] == 'AU') {
            $data = [
                ['name' => 'Direct Deposit (EFT)', 'id' => 1],
                ['name' => 'BPAY', 'id' => 2],
                ['name' => 'Cheque', 'id' => 3],
            ];
        } else {
            $data = [
                ['name' => 'Direct Deposit (EFT)', 'id' => 1],
                ['name' => 'Cheque', 'id' => 3],
            ];
        }

        $report->setSheetDetails($sheet_ind, 'Preffered Payment Method');
        $report->line = 1;
        $report->resetColumns();
        $report->addColumn('id', 'id', 200, 'left', '@', $report->styleBold);
        $report->addColumn('name', 'Payment Name', 200, 'left', '@', $report->styleBold);
        $report->renderHeader();
        $report->renderData($data);

        $report->clean();
        $report->close();

        echo '<script type="text/javascript">location.href = \'download_uploaded.php?filename=company_template.xlsx&fileID='
            . encodeParameter($filePath) . "';</script>";
        // exit;
    }

    if ($_FILES['importFile']) {
        global $pathPrefix, $clientDirectory;
        $importPage = $view->items['importPage'];
        $country = $view->items['country'];

        unset($view->items);
        unset($substitute_values);
        $file_name = $_FILES['importFile']['name'];
        $ext = pathinfo($file_name, PATHINFO_EXTENSION);
        ini_set('max_execution_time', 300);

        // Checking the file extension
        if ($ext == 'xlsx') {
            $folder = "{$pathPrefix}{$clientDirectory}/xlsx/ImportData/";
            if (! file_exists($folder)) {
                mkdir($folder, FILE_PERMISSION, true);
            }

            $uploadFile = $folder . str_replace(' ', '', $importPage) . '-' . $_FILES['importFile']['name'];
            $uploadFileForDL = "{$clientDirectory}/xlsx/ImportData/" . str_replace(' ', '', $importPage) . '-' . $_FILES['importFile']['name'];

            $uploadStatus = move_uploaded_file($_FILES['importFile']['tmp_name'], $uploadFile);

            $view->items['uploadFileForDL'] = encodeParameter($uploadFileForDL);

            //  Read your Excel workbook
            try {
                $inputFileType = IOFactory::identify($uploadFile);
                $objReader = IOFactory::createReader($inputFileType);
                $objPHPExcel = $objReader->load($uploadFile);
            } catch (Exception $e) {
                exit('Error loading file "' . pathinfo($uploadFile, PATHINFO_BASENAME)
                    . '": ' . $e->getMessage());
            }

            //  Get worksheet dimensions
            $sheet = $objPHPExcel->getSheet(0);
            $highestRow = $sheet->getHighestRow();
            $highestColumn = $sheet->getHighestColumn();
            $rowArrData = [];
            $columnCounter = 0;
            //  Loop through each row of the worksheet in turn
            for ($row = 1; $row <= $highestRow; $row++) {
                //  Read a row of data into an array
                $rowData = $sheet->rangeToArray(
                    'A' . $row . ':' . $highestColumn . $row,
                    null,
                    true,
                    false
                );

                // extract every cell in the selected row for simplicity. You can save the data in database too.
                $rowColData = [];
                foreach ($rowData[0] as $k => $v) {
                    $v = str_replace('_x000D_', ' ', $v);
                    if ($k == 13) {
                        $rowColData[] = trim($v, ' ');
                        // for Supplier Type
                    } else {
                        $rowColData[] = $v;
                    }

                }

                if ($row == 1) {
                    $columnCounter = count($rowData[0]);
                }

                $rowColData[0] = strtoupper($rowColData[0]);

                $rowArrData[] = $rowColData;
            }

            $error_lines = validate_file($rowArrData, $substitute_values, false, $country);

            if (count($error_lines) > 0) {
                $validationErrors[0] = 'Validation failed. Place the mouse pointer on the red cell to view its error.';
            }

            $view->items['columnCounter'] = $columnCounter;
            $view->items['sheet'] = $rowArrData;
            $_SESSION['importCompany']['sheet'] = $rowArrData;
            $_SESSION['importCompany']['columnCounter'] = $columnCounter;


        } else {
            $validationErrors[0] = 'Invalid file format. Please upload valid excel (xlsx) file.';
        }

        $view->items['country'] = $country;

    }

    $view->items['yesNoList'] =
    [
        0 => 'Test Import',
        1 => 'Final Import',
    ];

    if (! isset($view->items['finalImport'])) {
        $view->items['finalImport'] = 0;
    }

    // for internationalization
    $defaultCountryCode = $_SESSION['country_code'];

    if (! $view->items['country']) {
        $view->items['country'] = $defaultCountryCode;
    }

    $view->items['countries'] = dbGetCountries();
    $view->items['hiddenCols'] = getHiddenCols($view->items['country']);
    // --------------------------------------------

    $view->items['importPage'] = 'Company';
    $view->items['validationErrors'] = $validationErrors;
    $view->items['error_lines'] = $error_lines;
    $view->items['substitute_values'] = $substitute_values;

    $view->render();
}

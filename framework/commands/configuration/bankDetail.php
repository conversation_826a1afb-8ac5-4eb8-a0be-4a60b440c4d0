<?php

/**
 * <AUTHOR> Reyes
 *
 * @since 2015-11-27
 **/
function bankDetail(&$context)
{
    global $clientDirectory, $pathPrefix;

    if (! $_REQUEST['print']) {
        if (! isPostback()) {
            $view = new MasterPage(userViews(), '/configuration/bankDetail.html');
            $view->setSection($context['module']);
        } else {
            $view = new UserControl(userViews(), '/configuration/bankDetail.html');
        }
    } else {
        $view = new PrintPage(userViews(), '/configuration/bankDetail.html');
    }


    $view->bindAttributesFrom($_REQUEST);
    $company = [];
    $validationErrors = [];

    // Array Declaration
    $country = dbGetDefaultCountry();

    if ($country == 'AU') {
        $view->items['paymentMethodList'] =
        [
            PAY_EFT => 'Direct Deposit (EFT)',
            PAY_BPAY => 'BPAY',
            PAY_CHQ => 'Cheque',
        ];
    } else {
        $view->items['paymentMethodList'] =
        [
            PAY_EFT => 'Direct Deposit (EFT)',
            PAY_CHQ => 'Cheque',
        ];
    }

    $view->items['yesNoOption'] =
    [
        '1' => 'Yes',
        '0' => 'No',
    ];

    $view->items['companyList'] = dbGetCompanyList();
    $view->items['userList'] = dbGetAccountUserList();
    foreach ($view->items['userList'] as $k => $v) {
        if ($v['status'] == 'Inactive') {
            unset($view->items['userList'][$k]);
        }
    }

    $view->items['authorised'] = dbCheckAuthority($_SESSION['user_id']);

    /** Get Security Settings - START */
    $view->items['security_settings'] = dbGetSecuritySettings();
    $view->items['flag_authorizer_submitter'] = ($view->items['security_settings']['authorizer_submitter_flag'] === '1');
    $view->items['flag_default_payment_method'] = ($view->items['security_settings']['default_payment_method_flag'] === '1');
    /** Get Security Settings - END */
    if (count($view->items['userList']) === 0) {
        if (! dbCheckSuperAdmin($_SESSION['userID'])) {
            $validationErrors[] = 'No active authoriser found. Contact the administrator to add or activate an authoriser.';
        } else {
            $validationErrors[] = 'No active authorisers found. <a href="index.php?module=configuration&command=users">Click here</a> to add or activate an authoriser.';
        }

        $view->items['noAuthoriser'] = true;
    } else {
        $view->items['noAuthoriser'] = false;
    }

    if ($view->items['companyID']) {
        // Retrieve Company Bank Details
        $company = dbGetCompany($view->items['companyID']);

        if ($company) {
            if ($view->items['action'] == 'retrieve' || $view->items['action'] == 'approve') {
                $view->bindAttributesFrom($company);
            }

            // Array Declaration
            $view->items['dayList'] =
            [
                0 => 0,
                1 => 1,
                2 => 2,
                3 => 3,
                4 => 4,
            ];

            // Default Values
            if (empty($view->items['paymentMethod'])) {
                $view->items['paymentMethod'] = PAY_CHQ;
            }

            if (empty($view->items['chequeDays'])) {
                $view->items['chequeDays'] = 4;
            }

            if ($view->items['action'] == 'submit') {
                $view->items['userDBList'] = dbGetUsersForDatabase(dbGetClientID());

                if ($view->items['paymentMethod'] === PAY_EFT && getDisplayBsbFromSession() &&
                    (! isValid($view->items['bsbNumber'], TEXT_INT_ONLY, false) ||
                        strlen($view->items['bsbNumber']) !== getBsbLengthFromSession())
                ) {
                    $validationErrors[] = 'Your ' . getBsbLabelFromSession(
                    ) . ' is incorrect (must be ' . getBsbLengthFromSession() . ' numbers with no dash or spaces)';
                }

                if ($view->items['paymentMethod'] == PAY_EFT) {
                    if (! isValid($view->items['bankAccountNumber'], TEXT_INT, false)) {
                        $validationErrors[] = 'Bank Account Number is invalid.';
                    }

                    if (! isValid($view->items['bankAccountName'], TEXT_ALPHANUMERIC_WHITESPACE, false)) {
                        $validationErrors[] = 'Bank Account Name must be entered as alphanumeric without special characters.';
                    }

                    if (! isValid($view->items['bankName'], TEXT_LOOSE, true)) {
                        $validationErrors[] = 'Bank Name is invalid.';
                    }
                } elseif ($view->items['paymentMethod'] == PAY_BPAY && ! isValid($view->items['bpayBillerCode'], TEXT_LOOSE, false)) {
                    $validationErrors[] = 'Biller Code is invalid.';
                }

                if (empty($view->items['reason'])) {
                    $validationErrors[] = 'You need to enter a reason in order to change banking details.';
                }

                // ## Check security setting for authoriser and submitter
                // ## Validate if selected authoriser from dropdown is same with current logged-in user
                if ($view->items['flag_authorizer_submitter'] === true && $view->items['authoriser'] == $_SESSION['user_id']) {
                    $validationErrors[] = 'You cannot set yourself as the authoriser for this request. Please select another user.';
                }

                // Validate e-mails
                if (! $view->items['authoriser']) {
                    $validationErrors[] = 'No authoriser was selected.';
                } else {
                    $recipientEmail = mapParameters($view->items['userDBList'], 'userID', 'email');
                    $recipientEmail = $recipientEmail[$view->items['authoriser']];
                    if (! isValid($recipientEmail, TEXT_EMAIL, false)) {
                        $validationErrors[] = "Authoriser's e-mail address is invalid.";
                    }
                }

                $accountManagerEmail = dbGetParam('ACCMGR', 'MAINT');
                if (! isValid($accountManagerEmail, TEXT_EMAIL, false)) {
                    $validationErrors[] = "Account Manager's e-mail address is invalid.";
                }

                if (noErrors($validationErrors)) {
                    $company['originalPaymentMethod'] = $company['paymentMethod'];
                    $company['originalBsbNumber'] = $company['bsbNumber'];
                    $company['originalBankAccountNumber'] = $company['bankAccountNumber'];
                    $company['originalBankAccountName'] = $company['bankAccountName'];
                    $company['originalBankName'] = $company['bankName'];
                    $company['originalBpayBillerCode'] = $company['bpayBillerCode'];
                    $company['originalChequeDays'] = $company['chequeDays'];
                    $company['originalDirectDebit'] = $company['directDebit'];

                    $company['paymentMethod'] = (int) $view->items['paymentMethod'];
                    $company['bsbNumber'] = $view->items['bsbNumber'];
                    $company['bankAccountNumber'] = $view->items['bankAccountNumber'];
                    $company['bankAccountName'] = $view->items['bankAccountName'];
                    $company['bankName'] = $view->items['bankName'];
                    $company['bpayBillerCode'] = $view->items['bpayBillerCode'];
                    $company['chequeDays'] = (int) $view->items['chequeDays'];
                    $company['updateUser'] = $_SESSION['un'];
                    $company['reason'] = $view->items['reason'];
                    $company['directDebit'] = $view->items['directDebit'];
                    $company['fileID'] = 0;
                    $company['authoriser'] = $view->items['authoriser'];
                    $company['userID'] = $_SESSION['user_id'];

                    // Attachment
                    $attachment =  [];
                    if (is_uploaded_file($_FILES['file']['tmp_name'])) {
                        $extension = pathinfo($_FILES['file']['name'], PATHINFO_EXTENSION);
                        $folder = "{$pathPrefix}{$clientDirectory}/{$extension}/BankUpdate/";
                        checkDirPath($folder);
                        $fileName = time() . '_' . str_replace(' ', '_', basename($_FILES['file']['name']));
                        $fileName = str_replace("'", '', $fileName);
                        $uploadfile = $folder . $fileName;
                        $filePath = "{$clientDirectory}/{$extension}/BankUpdate/" . $fileName;

                        if ($_FILES['file']) {
                            $uploadStatus = move_uploaded_file($_FILES['file']['tmp_name'], $uploadfile);
                            if (! $uploadStatus) {
                                $view->items['statusMessage'] = 'Failed to attach the file.';
                            } else {
                                $attachment[0]['file'] = $uploadfile;
                                if ($extension == 'pdf') {
                                    $attachment[0]['content_type'] = 'application/pdf';
                                } elseif ($extension == 'txt') {
                                    $attachment[0]['content_type'] = 'text/plain';
                                }

                                $doc['documentType'] = DOC_COMPANY_BANK;
                                $doc['createdBy'] = $_SESSION['un'];
                                $doc['primaryID'] = $view->items['companyID'];
                                $doc['filename'] = $filePath;
                                $fileID = dbAddDocument($doc);
                                $company['fileID'] = $fileID;
                            }
                        }
                    }

                    // Database
                    $auditID = dbInsertTrailCompanyBank($company);

                    // Approval E-mail
                    $recipientName = mapParameters($view->items['userDBList'], 'userID', 'fullName');
                    $email = new EmailTemplate('views/emails/updatebankDetailApproval.html', SYSTEMURL);
                    $email->items['recipientName'] = $recipientName[$view->items['authoriser']];
                    $email->items['companyName'] = $company['companyName'];
                    $email->items['reason'] = $view->items['reason'];
                    $email->items['urlApproval'] = SYSTEMURL . '/index.php?module=' . $view->items['module'] . '&command=' . $view->items['command'] . '&action=verify&auditID=' . $auditID;
                    sendMail($recipientEmail, $email->items['recipientName'], $email->toString(), 'Banking Detail Update', $attachment);

                    // Notification E-mail
                    $email = new EmailTemplate('views/emails/updatebankDetailNotice.html', SYSTEMURL);
                    $email->items['companyName'] = $company['companyName'];
                    $email->items['reason'] = $view->items['reason'];
                    sendMail($accountManagerEmail, null, $email->toString(), 'Banking Detail Update on ' . $_SESSION['database'] . ' by ' . $_SESSION['un'], $attachment);

                    $view->items['statusMessage'] = 'Company bank detail has been submitted for approval.';
                }
            }
        } else {
            $validationErrors[] = 'Company does not exist.';
        }
    } elseif ($view->items['auditID']) {
        switch ($view->items['action']) {
            case 'verify':
                $view->items['new'] = dbGetTrailCompanyBank($view->items['auditID'], 'Pending');
                if (! empty($view->items['new'])) {
                    $authoriser = $view->items['new']['authoriser'];
                    // ## Check security setting default first
                    if ($view->items['flag_authorizer_submitter'] === true) {
                        if ($_SESSION['user_id'] == $authoriser) {
                            /** UPDATED: Check if user is approving his/her own request */
                            if ($_SESSION['user_id'] == $view->items['new']['userCreated']) {
                                $validationErrors[] = 'You are not allowed to approve or reject your own request. Another authoriser is required for processing this entry.';
                                $view->items['selectedAuthoriser'] = false;
                            } else {
                                $view->items['selectedAuthoriser'] = true;
                            }
                        }
                    } elseif ($_SESSION['user_id'] == $authoriser) {
                        $view->items['selectedAuthoriser'] = true;
                    }

                    $view->items['original'] = dbGetCompany($view->items['new']['companyID']);
                    $username = dbGetUserName($view->items['new']['userCreated']);
                    $view->items['new']['submittedBy'] = $username['fullName'];
                    $username = dbGetUserName($view->items['new']['authoriser']);
                    $view->items['new']['authoriser'] = $username['fullName'];
                    if ($view->items['new']['filename']) {
                        $view->items['new']['downloadLink'] = $view->items['new']['filename'];
                        $view->items['new']['filename'] = basename($view->items['new']['filename']);
                    }
                } else {
                    $validationErrors[] = 'Verifiable data does not exist. It may have already been approved or rejected.';
                }

                break;
            case 'approve':
                if ($view->items['authorised']) {
                    $updatedCompany = dbGetTrailCompanyBank($view->items['auditID'], 'Pending');
                    $approverUserID = $updatedCompany['authoriser'];
                    $requestorUserID = $updatedCompany['userCreated'];
                    $originalCompany = dbGetCompany($updatedCompany['companyID']);
                    $updatedCompany['directBanking'] = (bool) $originalCompany['debtor'] && $updatedCompany['paymentMethod'] == 1 ? 1 : 0;

                    $bankingDetailsUpdate = compareCompanyBankingDetails($originalCompany, $updatedCompany, $updatedCompany['companyID'], 'pmco_company');
                    dbAddCompanyUpdateLogEntry($bankingDetailsUpdate);

                    if ($originalCompany['paymentMethod'] != 3 && $updatedCompany['paymentMethod'] == 3 && intval($updatedCompany['chequeDays']) !== intval($originalCompany['chequeDays'])) {
                        $chequeDaysChangeLogEntry = createCompanyChangelogEntry($updatedCompany['companyID'], $originalCompany['chequeDays'], $updatedCompany['chequeDays'], 'pmco_company', 'chequeDays');
                        dbAddCompanyUpdateLogEntry($chequeDaysChangeLogEntry);
                    }

                    dbUpdateCompanyBank($updatedCompany);
                    dbUpdateTrailCompanyBankStatus($view->items['auditID'], 'Approved');

                    // Retrieve Company Bank Details
                    $company = dbGetCompany($updatedCompany['companyID']);

                    // Notification E-mail 1
                    $requestorEmail = dbGetUserByID($requestorUserID);
                    if (isValid($requestorEmail['email'], TEXT_EMAIL, false)) {
                        $email = new EmailTemplate('views/emails/updateBankDetailNotify.html', SYSTEMURL);
                        $email->items['companyName'] = $company['companyName'];
                        $email->items['approvedBy'] = $_SESSION['un'];
                        sendMail($requestorEmail['email'], null, $email->toString(), 'Banking Detail Approved for ' . $company['companyName']);
                    }

                    // Notification E-mail 2
                    $accountManagerEmail = dbGetParam('ACCMGR', 'MAINT');
                    if (isValid($accountManagerEmail, TEXT_EMAIL, false)) {
                        $email = new EmailTemplate('views/emails/updatebankDetailApproved.html', SYSTEMURL);
                        $email->items['companyName'] = $company['companyName'];
                        $email->items['approvedBy'] = $_SESSION['un'];
                        sendMail($accountManagerEmail, null, $email->toString(), 'Banking Detail Approved for ' . $company['companyName'] . ' on ' . $_SESSION['database']);
                    }

                    // Notification E-mail 3
                    $approverEmail = dbGetUserByID($approverUserID);
                    if (isValid($approverEmail['email'], TEXT_EMAIL, false)) {
                        $email = new EmailTemplate('views/emails/updatebankDetailApproved.html', SYSTEMURL);
                        $email->items['companyName'] = $company['companyName'];
                        $email->items['approvedBy'] = $_SESSION['un'];
                        sendMail($approverEmail['email'], null, $email->toString(), 'Banking Detail Approved for ' . $company['companyName'] . ' on ' . $_SESSION['database']);
                    }

                    $view->items['statusMessage'] = 'You have approved changes to the bank account detail of this company. <a href="index.php?module=configuration&command=bankDetailList">Click here</a> to return to the previous page.';
                } else {
                    $validationErrors[] = 'You are not authorised to approve the modifications made.';
                }

                break;
            case 'reject':
                if ($view->items['authorised']) {
                    $trail = dbGetTrailCompanyBank($view->items['auditID'], 'Pending');
                    $approverUserID = $trail['authoriser'];
                    $requestorUserID = $trail['userCreated'];
                    $company = dbGetCompany($trail['companyID']);

                    dbUpdateTrailCompanyBankStatus($view->items['auditID'], 'Rejected');

                    // Notification E-mail 1
                    $requestorEmail = dbGetUserByID($requestorUserID);
                    if (isValid($requestorEmail['email'], TEXT_EMAIL, false)) {
                        $email = new EmailTemplate('views/emails/updateBankDetailNotifyRejected.html', SYSTEMURL);
                        $email->items['companyName'] = $company['companyName'];
                        $email->items['rejectedBy'] = $_SESSION['un'];
                        sendMail($requestorEmail['email'], null, $email->toString(), 'Banking Detail Update Rejected for ' . $company['companyName']);
                    }

                    // Notification E-mail 2
                    $accountManagerEmail = dbGetParam('ACCMGR', 'MAINT');
                    if (isValid($accountManagerEmail, TEXT_EMAIL, false)) {
                        $email = new EmailTemplate('views/emails/updatebankDetailRejected.html', SYSTEMURL);
                        $email->items['companyName'] = $company['companyName'];
                        $email->items['rejectedBy'] = $_SESSION['un'];
                        sendMail($accountManagerEmail, null, $email->toString(), 'Banking Detail Update Rejected for ' . $company['companyName'] . ' on ' . $_SESSION['database']);
                    }

                    // Notification E-mail 3
                    $approverEmail = dbGetUserByID($approverUserID);
                    if (isValid($approverEmail['email'], TEXT_EMAIL, false)) {
                        $email = new EmailTemplate('views/emails/updatebankDetailRejected.html', SYSTEMURL);
                        $email->items['companyName'] = $company['companyName'];
                        $email->items['rejectedBy'] = $_SESSION['un'];
                        sendMail($approverEmail['email'], null, $email->toString(), 'Banking Detail Update Rejected for ' . $company['companyName'] . ' on ' . $_SESSION['database']);
                    }

                    $view->items['statusMessage'] = 'Changes to the banking details has been rejected.';
                } else {
                    $validationErrors[] = 'You are not authorised to reject the modifications made.';
                }

                break;
            case 'reassigned':
                // Validate e-mails
                $view->items['new'] = dbGetTrailCompanyBank($view->items['auditID'], 'Pending');
                $view->items['original'] = dbGetCompany($view->items['new']['companyID']);
                $username = dbGetUserName($view->items['new']['userCreated']);
                $view->items['new']['submittedBy'] = $username['fullName'];
                $userDBList = dbGetUsersForDatabase($_SESSION['clientID']);
                $company = dbGetCompany($view->items['new']['companyID']);

                if (! $view->items['authoriser']) {
                    $validationErrors[] = 'No authoriser was selected.';
                } else {
                    $recipientEmail = mapParameters($userDBList, 'userID', 'email');
                    $recipientEmail = $recipientEmail[$view->items['authoriser']];
                    if (! isValid($recipientEmail, TEXT_EMAIL, false)) {
                        $validationErrors[] = "Authoriser's e-mail address is invalid.";
                    }
                }

                if ($view->items['authoriser'] == $view->items['new']['authoriser']) {
                    $validationErrors[] = 'You are not reassigning this request. Kindly select another authoriser.';
                }


                if (noErrors($validationErrors)) {
                    dbUpdateTrailCompanyBankAuthoriser($view->items['auditID'], $view->items['authoriser']);

                    // Attachment
                    $attachment =  [];
                    $extension = pathinfo(basename($view->items['new']['filename']), PATHINFO_EXTENSION);
                    $attachment[0]['file'] = REPORTPATH . '/' . $view->items['new']['filename'];
                    if ($extension == 'pdf') {
                        $attachment[0]['content_type'] = 'application/pdf';
                    } elseif ($extension == 'txt') {
                        $attachment[0]['content_type'] = 'text/plain';
                    }

                    // Approval E-mail
                    $recipientName = mapParameters($userDBList, 'userID', 'fullName');
                    $email = new EmailTemplate('views/emails/updatebankDetailApproval.html', SYSTEMURL);
                    $email->items['recipientName'] = $recipientName[$view->items['authoriser']];
                    $email->items['companyName'] = $company['companyName'];
                    $email->items['reason'] = $view->items['new']['reason'];
                    $email->items['urlApproval'] = SYSTEMURL . '/index.php?module=' . $view->items['module'] . '&command=' . $view->items['command'] . '&action=verify&auditID=' . $view->items['auditID'];
                    sendMail($recipientEmail, $email->items['recipientName'], $email->toString(), 'Banking Detail Update', $attachment);

                    $view->items['statusMessage'] = 'You have successfully reassigned this request.';
                }

                break;
            case 'viewApproved':
            case 'viewRejected':
            case 'view':
            default:
                if ($view->items['action'] == 'viewApproved') {
                    $view->items['new'] = dbGetTrailCompanyBank($view->items['auditID'], 'Approved');
                } elseif ($view->items['action'] == 'viewRejected') {
                    $view->items['new'] = dbGetTrailCompanyBank($view->items['auditID'], 'Rejected');
                } else {
                    $view->items['new'] = dbGetTrailCompanyBank($view->items['auditID']);
                }

                if (empty($view->items['new'])) {
                    $validationErrors[] = 'Verifiable data does not exist.';
                } else {
                    if ($view->items['action'] == 'viewApproved') {
                        $view->items['original'] = dbGetApprovedTrailCompanyBank($view->items['auditID']);
                    } else {
                        $view->items['original'] = dbGetCompany($view->items['new']['companyID']);
                    }

                    $username = dbGetUserName($view->items['new']['userCreated']);
                    $view->items['new']['submittedBy'] = $username['fullName'];
                    $username = dbGetUserName($view->items['new']['userUpdated']);
                    $view->items['new']['modifiedBy'] = $username['fullName'];
                    if ($view->items['new']['filename']) {
                        $view->items['new']['pdfViewPath'] =  REPORTPATH . '/' . $view->items['new']['filename'];
                        $view->items['new']['downloadLink'] = $view->items['new']['filename'];
                        $view->items['new']['filename'] = basename($view->items['new']['filename']);
                    }
                }

                break;
        }
    }

    $view->items['validationErrors'] = $validationErrors;
    $view->render();
}

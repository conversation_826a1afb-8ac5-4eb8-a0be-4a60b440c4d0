<?php

function preparePaymentReport($batchID)
{
    global $clientDirectory, $pathPrefix;
    // -- will need to store current DB index in session to allow for translation of logos
    $logoFile = dbGetClientLogo();
    $logoPath = "assets/clientLogos/{$logoFile}";

    $date = toTimeParts($batch['batchDate']);
    $dateStamp = $date[DATE_YEAR] . $date[DATE_MONTH] . $date[DATE_DAY];

    $filename = "payments_{$dateStamp}_{$batchID}.pdf";

    $filePath = "{$pathPrefix}{$clientDirectory}/pdf/PaymentsReport/{$filename}";
    $downloadPath = "{$clientDirectory}/pdf/PaymentsReport/{$filename}";

    $data['filePath'] = $filePath;
    $data['downloadPath'] = $downloadPath;

    if (file_exists($filePath)) {
        return $data;
    }

    $report = new PaymentReport($filePath, $logoPath);

    $report->attachObject('traccFooter', new TraccFooter('assets/clientLogos/tracc_logo.jpg', 'Payment_Report', 1));
    $report->attachObject('paymentKey', new PaymentKey());

    $report->preparePage();

    $report->close();

    return $data;
}



/*************************************************************************************************************************/

// OBJECTS - repeating groups that appear in each invoice




class PaymentKey extends PDFObject
{
    public $height = 750;

    public $paymentType = '';

    public function __construct($paymentType)
    {
        $this->paymentType = $paymentType;
    }

    public function preRender(&$pdf)
    {
        $pdf->setColorExt('both', 'rgb', 0.5, 0.5, 0.5, 0);
        $this->setFont($pdf, 'Helvetica-Bold', 7);
        // $pdf->showBoxed ('Type', 20, $this->height, 50, 13, "left", "");
        $pdf->showBoxed('Inv. Date', 20, $this->height, 40, 13, 'left', '');
        $pdf->showBoxed('Inv. No.', 60, $this->height, 70, 13, 'left', '');

        $subtrahend = 0;
        if ($this->paymentType == 'EFT') {
            if (getDisplayBsbFromSession()) {
                $pdf->showBoxed(getBsbLabelFromSession(), 120, $this->height, 40, 13, 'left', '');
            } else {
                $subtrahend = 40;
            }
        } else {
            $pdf->showBoxed('Biller Code', 120, $this->height, 40, 13, 'left', '');
        }

        // $pdf->showBoxed ($this->paymentType == 'EFT' ? 'BSB' : 'Biller Code', 120, $this->height, 40, 13, "left", "");
        $pdf->showBoxed($this->paymentType == 'EFT' ? 'Acct no.' : 'BPAY Ref.', (160 - $subtrahend), $this->height, 70, 13, 'left', '');

        $pdf->showBoxed('Property', (230 - $subtrahend), $this->height, 160, 13, 'left', '');
        $pdf->showBoxed('Description', (330 - $subtrahend), $this->height, 130, 13, 'left', '');
        $pdf->showBoxed('From', (460 - $subtrahend), $this->height, 35, 13, 'left', '');
        $pdf->showBoxed('To', (495 - $subtrahend), $this->height, 35, 13, 'left', '');
        $pdf->showBoxed('Amount', (530), $this->height, 50, 13, 'right', '');
    }

    public function render(&$pdf) {}
}


class PaymentHeader extends PDFObject
{
    public $height = 800;

    public $propertyAgent;

    public $paymentType;

    public $batchNumber;

    public $paymentDate;

    public function __construct($propertyAgent, $paymentType, $batchNumber, $paymentDate)
    {
        $this->propertyAgent = $propertyAgent;
        $this->paymentType = $paymentType;
        $this->batchNumber = $batchNumber;
        $this->paymentDate = $paymentDate;
    }

    public function preRender(&$pdf)
    {
        $pdf->setColorExt('both', 'rgb', 0, 0.3, 0.5, 0);
        $this->setFont($pdf, 'Helvetica-Bold', 10);
        $pdf->showBoxed($this->propertyAgent, 20, $this->height, 300, 13, 'left', '');
        $this->setFont($pdf, 'Helvetica-Bold', 9);
        $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        $pdf->showBoxed('Payment Report', 20, $this->height - 15, 200, 13, 'left', '');
        $pdf->showBoxed('Payment Date', 20, $this->height - 23, 200, 13, 'left', '');
        $this->setFont($pdf, 'Helvetica', 8);

        $pdf->showBoxed("{$this->paymentType} Run {$this->batchNumber}", 100, $this->height - 15, 200, 13, 'left', '');
        $pdf->showBoxed("{$this->paymentDate}", 100, $this->height - 23, 200, 13, 'left', '');
    }

    public function render(&$pdf) {}
}



/*************************************************************************************************************************/

class PaymentReport extends PDFReport
{
    public $paymentType;

    public $pdf;

    public $lineOffset = 15;

    public $logoFile = false;

    public $maxHeight = 750;

    public $minHeight = 50;

    public function __construct(&$dataSource, $logoFile = false, $paymentType = '')
    {
        parent::__construct($dataSource);
        if ($logoFile) {
            $this->logoFile = $logoFile;
        }

        if ($paymentType != '') {
            $this->paymentType = $paymentType;
        }
    }

    public function updateLineOffset($offset)
    {
        $this->lineOffset += $offset;
        if ($this->lineOffset >= ($this->maxHeight - $this->minHeight)) {
            $this->endPage();
            $this->preparePage();
            $this->render();
        }
    }

    public function renderDetail($paymentType, $paymentDate, $reference, $bsb, $bankAccountNumber, $billerCode, $bpayReference, $propertyName, $description, $fromDate, $toDate, $amount)
    {
        $this->pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        $this->setfont('Helvetica', 6);
        // $this->pdf->showBoxed ( $paymentType, 20, $this->maxHeight-$this->lineOffset, 50, 13, "left", "");
        $this->pdf->showBoxed($paymentDate, 20, $this->maxHeight - $this->lineOffset, 40, 13, 'left', '');
        $this->pdf->showBoxed($reference, 60, $this->maxHeight - $this->lineOffset, 70, 13, 'left', '');

        $subtrahend = 0;
        if ($this->paymentType == 'EFT') {
            if (getDisplayBsbFromSession()) {
                $this->pdf->showBoxed(
                    formatWithDelimiter($bsb),
                    120,
                    $this->maxHeight - $this->lineOffset,
                    40,
                    13,
                    'left',
                    ''
                );
            } else {
                $subtrahend = 40;
            }
        } else {
            $this->pdf->showBoxed($billerCode, 120, $this->maxHeight - $this->lineOffset, 40, 13, 'left', '');
        }

        // $this->pdf->showBoxed ( $bsb  ? $bsb : $billerCode, 120, $this->maxHeight-$this->lineOffset, 40, 13, "left", "");
        $this->pdf->showBoxed($bankAccountNumber ? $bankAccountNumber : $bpayReference, (160 - $subtrahend), $this->maxHeight - $this->lineOffset, 70, 13, 'left', '');
        // $this->pdf->showBoxed ( $propertyID, 115, $this->maxHeight-$this->lineOffset, 60, 13, "left", "");
        $this->pdf->showBoxed($propertyName, (230 - $subtrahend), $this->maxHeight - $this->lineOffset, 160, 13, 'left', '');
        $this->pdf->showBoxed($description, (330 - $subtrahend), $this->maxHeight - $this->lineOffset, 130, 13, 'left', '');
        $this->pdf->showBoxed($fromDate, (460 - $subtrahend), $this->maxHeight - $this->lineOffset, 35, 13, 'left', '');
        $this->pdf->showBoxed($toDate, (495 - $subtrahend), $this->maxHeight - $this->lineOffset, 35, 13, 'left', '');
        $this->pdf->showBoxed(toMoney($amount), (530), $this->maxHeight - $this->lineOffset, 50, 13, 'right', '');
        $this->updateLineOffset(10);
    }

    public function renderCreditor($creditorID, $creditorName, $creditorAddress)
    {
        $this->updateLineOffset(15);
        $this->pdf->setColorExt('both', 'rgb', 0, 0.3, 0.5, 0);
        $this->setfont('Helvetica-Bold', 7);
        $this->pdf->showBoxed('Company', 20, $this->maxHeight - $this->lineOffset, 50, 13, 'left', '');
        $this->pdf->showBoxed($creditorID, 55, $this->maxHeight - $this->lineOffset, 50, 13, 'left', '');
        $this->pdf->showBoxed($creditorName, 115, $this->maxHeight - $this->lineOffset, 150, 13, 'left', '');
        $this->pdf->showBoxed($creditorAddress, 350, $this->maxHeight - $this->lineOffset, 250, 13, 'left', '');
        $this->pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        $this->updateLineOffset(15);
    }

    public function renderCreditorTotal($paymentType, $paymentReference, $total)
    {

        $this->setfont('Helvetica-Bold', 7);
        $this->pdf->setlinewidth(0.5);
        // Grey Areas...
        $this->pdf->setColorExt('both', 'rgb', 0.95, 0.95, 0.95, 0); // Setting the Color to Gray
        $this->pdf->rect(0, $this->maxHeight - $this->lineOffset + 2, 595, 12);
        $this->pdf->fill();

        $this->pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);


        $this->pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        $this->setfont('Helvetica-Bold', 6);
        $this->pdf->showBoxed("{$paymentType} Number {$paymentReference}", 20, $this->maxHeight - $this->lineOffset - 1, 100, 13, 'left', '');
        $this->pdf->showBoxed(toMoney($total), 530, $this->maxHeight - $this->lineOffset - 1, 50, 13, 'right', '');
        $this->updateLineOffset(10);
    }

    public function renderPaymentTotal($total)
    {
        $this->updateLineOffset(5);

        $this->setfont('Helvetica-Bold', 7);
        $this->pdf->setlinewidth(0.5);
        // Grey Areas...
        $this->pdf->setColorExt('both', 'rgb', 0.95, 0.95, 0.95, 0); // Setting the Color to Gray
        $this->pdf->rect(0, $this->maxHeight - $this->lineOffset + 2, 595, 12);
        $this->pdf->fill();

        $this->pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);

        $this->pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        $this->setfont('Helvetica-Bold', 7);
        $this->pdf->showBoxed('Payment Total', 20, $this->maxHeight - $this->lineOffset, 100, 13, 'left', '');
        $this->pdf->showBoxed(toMoney($total), 530, $this->maxHeight - $this->lineOffset, 50, 13, 'right', '');
        $this->updateLineOffset(10);
    }

    public function renderLogo()
    {
        if ($this->logoFile) {

            $maxWidth = LOGO_WIDTH;
            $maxHeight = LOGO_HEIGHT;
            [$imageWidth, $imageHeight] = getimagesize($this->logoFile);

            $horizontalScaling = ($imageWidth > $maxWidth) ? $maxWidth / $imageWidth : 1;
            $verticalScaling = ($imageHeight > $maxHeight) ? $maxHeight / $imageHeight : 1;
            $imageScale = ($horizontalScaling < $verticalScaling) ? $horizontalScaling : $verticalScaling;

            $imageScale = round($imageScale, 2);

            $vMargin = 25;
            $hMargin = 25;

            $hPos = 595 - ($imageScale * $imageWidth) - $hMargin;
            $vPos = 842 - ($imageScale * $imageHeight) - $vMargin;

            $pdfimage = $this->pdf->load_image('auto', $this->logoFile, '');
            $this->pdf->fit_image($pdfimage, $hPos, $vPos, "scale {$imageScale}");
            $this->pdf->close_image($pdfimage);
        }
    }

    public function preparePage($printTemplate = true)
    {
        $this->lineOffset = 15;
        parent::preparePage();
        $this->render();
        $this->renderLogo();
    }

    public function close()
    {
        parent::close();
    }
}

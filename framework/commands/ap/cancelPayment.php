<?php

define('BY_CREDITOR', 1);
define('BY_REFERENCE', 2);
define('BY_RANGE', 3);

function multi_search_payments($object, $amount, $propertyID, $creditorID)
{
    foreach ($object as $key => $value) {
        if (array_search($amount, $value, true) && array_search($propertyID, $value, true) && array_search($creditorID, $value, true)) {
            // $tmp = $object[$key]['pmxcAllocNR'];
            return $key;
        }
    }

    return $key;
}

function truncateSet($dataSet, $separator = ', ', $limit = 4)
{
    $count = count($dataSet ?? []);
    if ($count > $limit) {
        $overflow = $count - $limit;
        $dataSet = array_slice($dataSet, 0, $limit);

        return implode($separator, $dataSet) . " and {$overflow} others.";
    } else {
        return implode($separator, $dataSet);
    }
}

function cancel($batchNumber, $lineNumber, $payee, $date) {}

// function caapsAPI_cancel($batchNumber, $lineNumber, $transactionDate, $paymentMethod, $paymentVoucherNumber= '',$paymentBatchNumber=''){
//    /**  CAAPS INTEGRATION **/
//    $caapsCred = dbGetCaapsDetails($_SESSION['clientID']);
//    $caapsMessage = '';
//    $ocr_log_message = '';
//    if($caapsCred['use_caaps']) {
//        // GET OCR DETAILS AND AMOUNT
//        $ocrDtls = dbGetTempOCRDetails_cancel($batchNumber, $lineNumber);
//        if ($ocrDtls) {
//            foreach($ocrDtls as $ocrRaw){
//                $paymentDetail = array();
//                $response_lines = '';
//                // GET CAAPS STATUS
//                $totalInvoice = getCAAPSTotalInvoicePaid($ocrRaw['ap_batch_nr']);
//                $toPaid = getCAAPSToPaid($ocrRaw['ap_batch_nr']);
//                if($totalInvoice['totalPaid'] == 0){
//                    $status_CAAPS = 'CANCELLED';
//                    $totalInvoice['totalPaid'] = 0;
//                }
//                else {
//                    // IF total amount from ap_transaction is not equal to total amount temp_ocr_ap_line_pm
//                    $status_CAAPS = 'PARTPAID';
//                }
//                $caapsID = dbGetCaapsBatchID($ocrRaw['ap_batch_nr']);
//                $paymentDate = str_replace('/', '-', $transactionDate);
//                $paymentDetail['caapsUniqueId'] = $caapsID['fc_batch_id'];
//                $paymentDetail['paymentStatus'] = $status_CAAPS;
//                $paymentDetail['paymentDate'] = date("Y-m-d", strtotime($paymentDate)) . 'T00:00:00';
//                $paymentDetail['paymentAmount'] = $totalInvoice['totalPaid'];
//                $paymentDetail['paymentMethod'] = $paymentMethod;
//                $paymentDetail['paymentVoucherNumber'] = $paymentVoucherNumber;
//                $paymentDetail['paymentBatchNumber'] = $paymentBatchNumber;
//
//                $payment = postCaapsPayment($caapsCred['caaps_key'], json_encode($paymentDetail));
//                $response_lines = json_decode($payment);
//                if ($response_lines->errors) {
//                    foreach ($response_lines->errors as $error) {
//                        $caapsMessage = '<br/>Payment update to Caaps failed for ' . $caapsID['fc_batch_id'] . '. ' . $error[0];
//                    }
//                }
//                if ($response_lines->statusCode == 401) {
//                    $caapsMessage = '<br/>Payment update to Caaps failed for ' . $caapsID['fc_batch_id'] . '. ' . $response_lines->message;
//                }
//
//                if (empty($response_lines->errors) && $response_lines->caapsUniqueId == $caapsID['fc_batch_id']) {
//                    $caapsMessage = '<br/>Successfully post cancel payment update on CAAPS.';
//                }
//
//                // START OF CAAPS LOGS
//                $caapsLogFile = fopen("CAAPS_logs.txt", "a") or die("Unable to open file!");
//                fwrite($caapsLogFile, "\n" . "=============================================================");
//                fwrite($caapsLogFile, "\n" . "CAAPS ".$paymentMethod." PAYMENT Log at: ".date("Y-m-d H:i:s"));
//                fwrite($caapsLogFile, "\n" . "Batch Number : " . $batchNumber . $ocr_log_message);
//                if ($paymentDetail){
//                    fwrite($caapsLogFile, "\n" . "------------------------------------------------");
//                    fwrite($caapsLogFile, "\n" . "Array Data");
//                    fwrite($caapsLogFile, "\n" . json_encode($paymentDetail) );
//                    fwrite($caapsLogFile, "\n" . "------------------------------------------------");
//                }
//                fwrite($caapsLogFile, "\n");
//                if ($response_lines){
//                    fwrite($caapsLogFile, "\n" . "------------------------------------------------");
//                    fwrite($caapsLogFile, "\n" . "CAAPS Response");
//                    fwrite($caapsLogFile, "\n" . json_encode($response_lines));
//                    fwrite($caapsLogFile, "\n" . "------------------------------------------------");
//                }
//                fwrite($caapsLogFile, "\n". $caapsMessage);
//                fwrite($caapsLogFile, "\n" . "=============================================================");
//                fclose($caapsLogFile);
//                // END OF CAAPS LOGS
//            }
//
//        } else {
//            // START OF CAAPS LOGS
//            $ocr_log_message = ' - does not exist in OCR Details ';
//            $caapsLogFile = fopen("CAAPS_logs.txt", "a") or die("Unable to open file!");
//            fwrite($caapsLogFile, "\n" . "=============================================================");
//            fwrite($caapsLogFile, "\n" . "CAAPS ".$paymentMethod." PAYMENT Log at: ".date("Y-m-d H:i:s"));
//            fwrite($caapsLogFile, "\n" . "Batch Number : " . $batchNumber . $ocr_log_message);
//            fclose($caapsLogFile);
//            // END OF CAAPS LOGS
//        }
//    }
//
//    return $caapsMessage;
// }

// function postCaapsPayment($caaps_key, $caapsData){
//
//    $ch = curl_init();
//    curl_setopt($ch, CURLOPT_URL, API_PAYMENT_UPDATE.'?'.'&Subscription-Key='.$caaps_key);
//    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
//    curl_setopt($ch, CURLOPT_HTTPHEADER, array(
//        'Content-Type: application/json',
//        'Connection: Keep-Alive'
//    ));
//    curl_setopt($ch, CURLOPT_POST , 1);
//    curl_setopt($ch, CURLOPT_POSTFIELDS, $caapsData);
//    $output = curl_exec ($ch);
//    curl_close ($ch);
//	return $output;
// }


function cancelPayment(&$context)
{
    if (! isPostback()) {
        $view = new MasterPage(userViews(), '/ap/cancelPayment.html');
        $view->setSection($context['module']);
    } else {
        $view = new UserControl(userViews(), '/ap/cancelPayment.html');
    }

    $view->bindAttributesFrom($context);
    $view->bindAttributesFrom($_REQUEST);

    switch ($view->items['action']) {
        case 'updateMethod':
            $view->items['referenceNumber'] = null;
            break;
        case 'changeSupplier':
            if ($view->items['method'] == BY_CREDITOR) {
                $view->items['paymentType'] = dbGetCompanyPaymentMethod($view->items['supplierID']);
            }

            break;
        case 'cancel':
            // if (toDateStamp ($view->items['cancellationDate']) > toDateStamp (TODAY)) $validationErrors[] = 'You cannot cancel a payment in the future';

            if (! isset($view->items['pmxcAllocNR'])) {
                $view->items['pmxcAllocNR'] = 0;
            }

            if (toDateStamp($view->items['cancellationDate']) < toDateStamp($view->items['startDate'])) {
                $validationErrors[] = 'You cannot cancel a payment prior to ' . $view->items['startDate'];
            }

            // if (toDateStamp ($view->items['cancellationDate']) > toDateStamp ($view->items['endDate'])) $validationErrors[] = 'You must cancel a payment in an open period';
            // if ($view->items['presentedDate']) $validationErrors[] = 'This payment has been presented. Please unpresent first then cancel the payment.';

            switch ($view->items['paymentType']) {
                case PAY_BPAY:
                    $payment = dbGetTransactionByReference(
                        $view->items['referenceNumber'],
                        TRANSACTION_PAYMENT,
                        $view->items['payee'],
                        'Y',
                        $view->items['bankID'],
                        $view->items['pmxcAllocNR'],
                        $view->items['property']
                    );
                    $allocations = dbGetPayableAllocations(
                        $payment['batchNumber'],
                        $payment['lineNumber'],
                        null,
                        null,
                        $payment['bankID'],
                        TYPE_PAYMENT,
                        [TYPE_INVOICE, TYPE_CREDIT],
                        $view->items['payee'],
                        $view->items['property']
                    );
                    break;
                case PAY_EFT:
                    $payment = dbGetTransactionByReference(
                        $view->items['referenceNumber'],
                        TRANSACTION_PAYMENT,
                        $view->items['payee'],
                        'X',
                        $view->items['bankID'],
                        $view->items['pmxcAllocNR'],
                        $view->items['property']
                    );
                    $allocations = dbGetPayableAllocations(
                        $payment['batchNumber'],
                        $payment['lineNumber'],
                        null,
                        null,
                        $payment['bankID'],
                        TYPE_PAYMENT,
                        [TYPE_INVOICE, TYPE_CREDIT],
                        $view->items['payee'],
                        $view->items['property']
                    );
                    break;
                case PAY_CHQ:
                    $payment = dbGetTransactionByReference(
                        $view->items['referenceNumber'],
                        TRANSACTION_PAYMENT,
                        null,
                        'C',
                        $view->items['bankID'],
                        $view->items['pmxcAllocNR'],
                        $view->items['property']
                    );
                    $allocations = dbGetPayableAllocations(
                        $payment['batchNumber'],
                        $payment['lineNumber'],
                        null,
                        null,
                        $payment['bankID'],
                        TYPE_PAYMENT,
                        [TYPE_INVOICE, TYPE_CREDIT],
                        '',
                        $view->items['property']
                    );

                    break;
            }

            foreach ($allocations as $rs) {
                $calendar = dbGetPeriod($rs['propertyID'], $view->items['cancellationDate']);
                if (! isset($calendar['closed']) || $calendar['closed'] == 1) {
                    $nocalendar = 1;
                }

                if (toDateStamp($view->items['cancellationDate']) < toDateStamp(
                    $rs['allocationDate']
                ) && $rs['allocationAmount'] <= 0) {
                    $futuredate = 1;
                }
            }

            if (isset($nocalendar)) {
                $validationErrors[] = 'Cannot cancel payment within a closed calendar period.';
            }

            if (isset($futuredate)) {
                $validationErrors[] = "You can't cancel a payment prior to its payment date";
            }

            //            $validationErrors[] = "test";


            // -- START OF TESTING
            //            pre_print_r($view->items['pmxcAllocNR']);
            //            $payment = dbGetTransactionByReference($view->items['referenceNumber'], TRANSACTION_PAYMENT, $view->items['payee'], 'X', $view->items['bankID'], $view->items['pmxcAllocNR']);
            //            $payee = $view->items['payee'];
            //            $paymentState = 'X';
            //
            //
            //            $allocations = dbGetPayableAllocations($payment['batchNumber'], $payment['lineNumber'], null, null, $payment['bankID'], TYPE_PAYMENT, TYPE_INVOICE, $payee);
            //
            //            // Choose specific data on allocations array from accountID for cancellation
            //            if($view->items['singleTransactionFlag'])
            //            {
            //                $allocationKeyPointer = array_keys(array_column($allocations, 'allocationNumber'), $view->items['pmxcAllocNR']);
            //                pre_print_r(count($allocationKeyPointer));
            //                $allocationKeyPointer = (count($allocationKeyPointer) > 1 ? "" : $allocationKeyPointer[0]);
            //
            //                $tmp_alloc          = $allocations;
            //                $tmp_alloc_single   = $allocations[$allocationKeyPointer];
            //                unset($allocations);
            //
            //                $allocations[]      = $tmp_alloc_single;
            //            }
            //
            //            pre_print_r($allocations);
            // --

            //            $validationErrors[] = 'test';

            //            $allocations = dbGetPayableAllocations($payment['batchNumber'], $payment['lineNumber'],null, null, $payment['bankID'], TYPE_PAYMENT, array(TYPE_INVOICE, TYPE_CREDIT), $view->items['payee']);

            [$allowCancellation, $allowedAmount, $batchError] = dbRecheckAllocations(
                $payment['batchNumber'],
                $payment['lineNumber'],
                $view->items['pmxcAllocNR'],
                $view->items['property']
            );

            if (! $allowCancellation) {
                if ($batchError) {
                    $validationErrors[] = 'Payment Reference : <b>' . $view->items['referenceNumber'] . '</b> amount differs to database allocation. Please contact Cirrus8 Support.';
                } else {
                    $validationErrors[] = 'Cancellation amount is greater than amount allowed for this payment batch : ' . toMoney(
                        $allowedAmount
                    );
                }
            }

            if (noErrors($validationErrors) && $view->items['credit']) {
                $total = 0;
                $paymentState = null;
                switch ($view->items['paymentType']) {
                    case PAY_BPAY:
                        $payment = dbGetTransactionByReference(
                            $view->items['referenceNumber'],
                            TRANSACTION_PAYMENT,
                            $view->items['payee'],
                            'Y',
                            $view->items['bankID'],
                            $view->items['pmxcAllocNR'],
                            $view->items['property']
                        );
                        $payee = $view->items['payee'];
                        $paymentState = 'Y';
                        break;
                    case PAY_EFT:
                        $payment = dbGetTransactionByReference(
                            $view->items['referenceNumber'],
                            TRANSACTION_PAYMENT,
                            $view->items['payee'],
                            'X',
                            $view->items['bankID'],
                            $view->items['pmxcAllocNR'],
                            $view->items['property']
                        );
                        $payee = $view->items['payee'];
                        $paymentState = 'X';
                        break;
                    case PAY_CHQ:
                        $payment = dbGetTransactionByReference(
                            $view->items['referenceNumber'],
                            TRANSACTION_PAYMENT,
                            null,
                            'C',
                            $view->items['bankID'],
                            $view->items['pmxcAllocNR'],
                            $view->items['property']
                        );
                        $payee = null;
                        $paymentState = 'C';
                        break;
                }

                if ($payment['batchNumber'] == '' || $payment['lineNumber'] == '') {
                    $validationErrors[] = 'Invalid reference number.';
                }
            }

            if (noErrors($validationErrors)) {
                $cancel_reference = null;
                // -- if crediting an invoice -
                if ($view->items['credit']) {
                    $allocations = dbGetPayableAllocations(
                        $payment['batchNumber'],
                        $payment['lineNumber'],
                        null,
                        null,
                        $payment['bankID'],
                        TYPE_PAYMENT,
                        TYPE_INVOICE,
                        $payee,
                        $view->items['property']
                    );

                    // Choose specific data on allocations array from accountID for cancellation
                    if ($view->items['singleTransactionFlag']) {
                        $allocationKeyPointer = array_keys(
                            array_column($allocations, 'allocationNumber'),
                            $view->items['pmxcAllocNR']
                        );
                        $allocationKeyPointer = (count(
                            $allocationKeyPointer ?? []
                        ) > 1 ? '' : $allocationKeyPointer[0]);

                        $tmp_alloc = $allocations;
                        $tmp_alloc_single = $allocations[$allocationKeyPointer];
                        unset($allocations);

                        $allocations[] = $tmp_alloc_single;
                    }

                    foreach ($allocations as $o) {
                        $transaction = dbGetTransactionByBatch($o['toBatch'], $o['toLine']);
                        $calendar = dbGetPeriod($transaction['propertyID'], $view->items['cancellationDate']);
                        // -- save the existing batch/line to the 'to'
                        $transaction['toBatchNumber'] = $transaction['batchNumber'];
                        $transaction['toLineNumber'] = $transaction['lineNumber'];
                        $transaction['toType'] = $transaction['transactionType'];
                        // -- set the new batch/line
                        $batchNumber = dbGetNextPayableBatchNumber();
                        $transaction['batchNumber'] = $batchNumber;
                        $transaction['lineNumber'] = 1;
                        $transaction['transactionType'] = ($transaction['transactionType'] == TYPE_INVOICE) ? TYPE_CREDIT : TYPE_INVOICE;
                        // -- copy the new batch/line to the 'from'
                        $transaction['fromBatchNumber'] = $transaction['batchNumber'];
                        $transaction['fromLineNumber'] = $transaction['lineNumber'];
                        $transaction['fromType'] = $transaction['transactionType'];
                        $transaction['transactionDate'] = $view->items['cancellationDate'];
                        $transaction['year'] = $calendar['year'];
                        $transaction['period'] = $calendar['period'];
                        // -- transaction amounts
                        $transaction['transactionAmount'] = bcmul(1, $o['allocationAmount'], 2);
                        $transaction['taxAmount'] = bcmul(-1, $o['taxAmount'], 2);
                        $transaction['netAmount'] = bcmul(1, ($o['allocationAmount'] + $o['taxAmount']), 2);
                        $total = bcadd($total, $transaction['transactionAmount'], 2);
                        // --	credit the invoice

                        /* GL code needs to be after the transaction */
                        dbInsertPayableTransaction($transaction);
                        // --	allocate the credit to the invoice
                        $transaction['allocationNumber'] = dbGetNextAllocationNumber();
                        dbInsertAllocation($transaction);

                        // -- GL CODE (Credit) - DR Expense, DR GST Input, CR Creditors Control
                        if (GL_ACTIVE) {
                            // ** trial balance code **/
                            // -- create a new GL object (common information for a transaction - used for the trial balance)
                            $gl = new GeneralLedger();
                            $gl->transactionType = $transaction['transactionType'];
                            $gl->transactionDate = $transaction['transactionDate'];
                            $gl->description = $transaction['description'];
                            $gl->year = $calendar['year'];
                            $gl->period = $calendar['period'];
                            $gl->propertyID = $transaction['propertyID'];
                            if ($transaction['leaseID']) {
                                $gl->leaseID = $transaction['leaseID'];
                            }

                            $gl->companyID = $transaction['creditorID'];
                            $gl->fromDate = $transaction['fromDate'];
                            $gl->toDate = $transaction['toDate'];
                            $gl->source = GL_SOURCE_AP;
                            $gl->batchID = $transaction['allocationNumber'];
                            $gl->lineNumber = 1;

                            $t = new Transaction($gl);    // -- detail entry : adopt the data from the

                            $_amount = bcmul(1, $transaction['netAmount'], 2);
                            $gl->update($transaction['accountID'], GL_BALANCE_ACCRUALS, $_amount);
                            $t->add($transaction['accountID'], BASIS_ACCRUALS, $_amount);

                            $_amount = bcmul(1, $transaction['taxAmount'], 2);
                            $gl->update(glAccount(GL_GST_INPUT), GL_BALANCE_ACCRUALS, $_amount);
                            $t->add(glAccount(GL_GST_INPUT), BASIS_ACCRUALS, $_amount);

                            $_amount = bcmul(-1, $transaction['transactionAmount'], 2);
                            $gl->update(glAccount(GL_CREDITORS_CONTROL), GL_BALANCE_ACCRUALS, $_amount);
                            $t->add(
                                glAccount(GL_CREDITORS_CONTROL),
                                BASIS_ACCRUALS,
                                $_amount,
                                true
                            ); // -- group bank, debtors control and creditors control
                        }
                    }

                    // -- update AP batch
                    $batch['batchNumber'] = $batchNumber;
                    $batch['lineNumber'] = 1;
                    $batch['uVersion'] = '!';
                    $batch['transactionType'] = TYPE_CREDIT;
                    $batch['bankID'] = $payment['bankID'];
                    $batch['transactionDate'] = $view->items['cancellationDate'];
                    $batch['createUser'] = $_SESSION['un'];
                    $batch['amount'] = $total;
                    $batch['count'] = 0;
                    $batch['paymentType'] = null;
                    $batch['chequePerCreditor'] = 0;
                    $batch['createDate'] = TODAY;
                    dbInsertBatch($batch);
                } /* END - credit invoices */

                $payment = null;
                $allocations = null;
                $batchNumber = dbGetNextPayableBatchNumber();
                switch ($view->items['paymentType']) {
                    case PAY_BPAY:
                        $payment = dbGetTransactionByReference(
                            $view->items['referenceNumber'],
                            TRANSACTION_PAYMENT,
                            $view->items['payee'],
                            'Y',
                            $view->items['bankID'],
                            $view->items['pmxcAllocNR'],
                            $view->items['property']
                        );
                        $allocations = dbGetPayableAllocations(
                            $payment['batchNumber'],
                            $payment['lineNumber'],
                            null,
                            null,
                            $payment['bankID'],
                            TYPE_PAYMENT,
                            [TYPE_INVOICE, TYPE_CREDIT],
                            $view->items['payee'],
                            $view->items['property']
                        );
                        break;
                    case PAY_EFT:
                        $payment = dbGetTransactionByReference(
                            $view->items['referenceNumber'],
                            TRANSACTION_PAYMENT,
                            $view->items['payee'],
                            'X',
                            $view->items['bankID'],
                            $view->items['pmxcAllocNR'],
                            $view->items['property']
                        );
                        $allocations = dbGetPayableAllocations(
                            $payment['batchNumber'],
                            $payment['lineNumber'],
                            null,
                            null,
                            $payment['bankID'],
                            TYPE_PAYMENT,
                            [TYPE_INVOICE, TYPE_CREDIT],
                            $view->items['payee'],
                            $view->items['property']
                        );
                        break;
                    case PAY_CHQ:
                        $payment = dbGetTransactionByReference(
                            $view->items['referenceNumber'],
                            TRANSACTION_PAYMENT,
                            null,
                            'C',
                            $view->items['bankID'],
                            $view->items['pmxcAllocNR'],
                            $view->items['property']
                        );
                        $allocations = dbGetPayableAllocations(
                            $payment['batchNumber'],
                            $payment['lineNumber'],
                            null,
                            null,
                            $payment['bankID'],
                            TYPE_PAYMENT,
                            [TYPE_INVOICE, TYPE_CREDIT],
                            '',
                            $view->items['property']
                        );

                        break;
                }

                /* if it returned the payment and allocations ... */
                if ($payment && $allocations) {
                    $batchAmount = 0;
                    $batchTaxAmount = 0;
                    foreach ($allocations as $allocation) {
                        $batchAmount += bcmul(-1, $allocation['allocationAmount'], 2);
                        $batchTaxAmount += bcmul(-1, $allocation['taxAmount'], 2);
                    }

                    // -- insert a 'CAN' into AP_transaction with inversed amounts
                    $_payment = $payment;
                    $_payment['batchNumber'] = $batchNumber;
                    $_payment['lineNumber'] = 1;
                    $_payment['transactionAmount'] = $batchAmount; // bcmul(-1, $_payment['transactionAmount'],2);
                    $_payment['taxAmount'] = $batchTaxAmount; // bcmul(-1, $_payment['taxAmount'],2);
                    $_payment['netAmount'] = $batchAmount - $batchTaxAmount; // bcmul(-1, $_payment['netAmount'],2);
                    $_payment['transactionType'] = TYPE_CANCELLATION;
                    $_payment['cancellationDate'] = $view->items['cancellationDate']; // - not sure if this is used
                    $_payment['transactionDate'] = $view->items['cancellationDate'];
                    $_payment['createUser'] = $_SESSION['un'];
                    // dbInsertPayableTransaction ($_payment);

                    // --insert into AP batch
                    $batch['batchNumber'] = $batchNumber;
                    $batch['lineNumber'] = 1;
                    $batch['uVersion'] = '!';
                    $batch['transactionType'] = TYPE_CANCELLATION;
                    $batch['bankID'] = $_payment['bankID'];
                    $batch['transactionDate'] = $view->items['cancellationDate'];
                    $batch['createUser'] = $_SESSION['un'];
                    $batch['amount'] = $batchAmount; // $_payment['transactionAmount'];
                    $batch['count'] = '0';
                    $batch['paymentType'] = null;
                    $batch['chequePerCreditor'] = null;
                    $batch['createDate'] = TODAY;
                    // dbInsertBatch ($batch);

                    // -- for each allocation reverse it and insert it
                    if ($allocations) {
                        // Choose specific data on allocations array from accountID for cancellation
                        if ($view->items['singleTransactionFlag']) {
                            $allocationKeyPointer = array_keys(
                                array_column($allocations, 'allocationNumber'),
                                $view->items['pmxcAllocNR']
                            );
                            $allocationKeyPointer = (count(
                                $allocationKeyPointer ?? []
                            ) > 1 ? '' : $allocationKeyPointer[0]);

                            $tmp_alloc = $allocations;
                            $tmp_alloc_single = $allocations[$allocationKeyPointer];
                            unset($allocations);

                            $allocations[] = $tmp_alloc_single;
                            $cancel_reference = $tmp_alloc_single['allocationNumber'];
                        }

                        // Normalize $allocations data by removing positive amount value, meaning it has already been cleared.
                        //                        $normalized_array[] = array_filter($allocations, function ($v) { return $v['allocationAmount'] < 0; });
                        //
                        //					    // Perform transfer
                        //					    $allocations        = $normalized_array;
                        $batchAmount = 0;
                        $batchTaxAmount = 0;
                        foreach ($allocations as $allocation) {
                            $calendar = dbGetPeriod($allocation['propertyID'], $view->items['cancellationDate']);

                            $allocation['allocationNumber'] = dbGetNextAllocationNumber();
                            $allocation['fromBatchNumber'] = $allocation['fromBatch'];
                            $allocation['fromLineNumber'] = $allocation['fromLine'];
                            $allocation['toBatchNumber'] = $allocation['toBatch'];
                            $allocation['toLineNumber'] = $allocation['toLine'];
                            $allocation['transactionAmount'] = bcmul(-1, $allocation['allocationAmount'], 2);
                            $allocation['taxAmount'] = bcmul(-1, $allocation['taxAmount'], 2);
                            $allocation['transactionDate'] = $view->items['cancellationDate'];
                            $allocation['year'] = $calendar['year'];
                            $allocation['period'] = $calendar['period'];
                            $allocation['createUser'] = $_SESSION['un'];
                            $allocation['createDate'] = TODAY;
                            $batchAmount += bcmul(-1, $allocation['allocationAmount'], 2);
                            $batchTaxAmount += bcmul(-1, $allocation['taxAmount'], 2);
                            $tmp_alloc_txn_amount = $allocation['allocationAmount'];
                            $tmp_payment_txn_amount = $allocation['allocationAmount'];
                            $tmp_can_amount = $tmp_alloc_txn_amount;
                            $tmp_alloc_txn_tax_amount = $allocation['taxAmount'];
                            $tmp_payment_tax_amount = $allocation['taxAmount'];

                            dbInsertAllocation($allocation);
                            //                            $caapsMessage = caapsAPI_cancel($allocation['toBatch'], $allocation['toLine'], $allocation['transactionDate'], 'CANCEL', $allocation['allocationNumber'],'');
                            if (GL_ACTIVE) {
                                $gl = new GeneralLedger();
                                $gl->transactionType = TYPE_CANCELLATION; // taking a few liberties here - but applying the FROM type
                                $gl->transactionDate = $view->items['cancellationDate'];
                                $gl->description = 'Cancellation';
                                $gl->year = $calendar['year'];
                                $gl->period = $calendar['period'];
                                $gl->propertyID = $allocation['propertyID'];
                                if ($allocation['leaseID']) {
                                    $gl->leaseID = $allocation['leaseID'];
                                }

                                $gl->companyID = $allocation['creditorID'];
                                $gl->fromDate = $transaction['fromDate'];
                                $gl->toDate = $transaction['toDate'];
                                $gl->source = GL_SOURCE_AP;

                                $gl->batchID = $allocation['allocationNumber'];
                                $gl->lineNumber = 1;

                                $t = new Transaction($gl);    // -- detail entry : adopt the data from the

                                // -- net amount not gross amount
                                $_amount = bcmul(
                                    1,
                                    bcsub($allocation['allocationAmount'], $allocation['taxAmount'], 2),
                                    2
                                );
                                $gl->update($allocation['accountID'], GL_BALANCE_CASH, $_amount);
                                $t->add($allocation['accountID'], BASIS_CASH, $_amount);


                                $_amount = bcmul(1, $allocation['taxAmount'], 2);
                                $gl->update(glAccount(GL_GST_INPUT), GL_BALANCE_CASH, $_amount);
                                $t->add(glAccount(GL_GST_INPUT), BASIS_CASH, $_amount);

                                $_amount = bcmul(1, $allocation['allocationAmount'], 2);
                                $gl->update(glAccount(GL_CREDITORS_CONTROL), GL_BALANCE_ACCRUALS, $_amount);
                                $t->add(glAccount(GL_CREDITORS_CONTROL), BASIS_ACCRUALS, $_amount);

                                $_amount = bcmul(-1, $allocation['allocationAmount'], 2);
                                $gl->update(glAccount(GL_BANK), GL_BALANCE_ACCRUALS, $_amount);
                                $t->add(glAccount(GL_BANK), BASIS_ACCRUALS, $_amount);

                                $_amount = bcmul(-1, $allocation['allocationAmount'], 2);
                                $gl->update(glAccount(GL_BANK), GL_BALANCE_CASH, $_amount);
                                $t->add(glAccount(GL_BANK), BASIS_CASH, $_amount);
                            }
                        }

                        // Should I return original allocations from $tmp_alloc variable?
                    }

                    // -- insert a record CAN->PAY in allocations
                    $cancellation = [];
                    $cancellation['allocationNumber'] = dbGetNextAllocationNumber();
                    $cancellation['fromBatchNumber'] = $batchNumber;
                    $cancellation['fromLineNumber'] = 1;
                    $cancellation['fromType'] = TYPE_CANCELLATION;
                    $cancellation['toBatchNumber'] = $payment['batchNumber'];
                    $cancellation['toLineNumber'] = $payment['lineNumber'];
                    $cancellation['toType'] = $payment['transactionType'];
                    $cancellation['bankID'] = $payment['bankID'];
                    $cancellation['creditorID'] = $payment['creditorID'];
                    $cancellation['transactionAmount'] = $batchAmount; // bcmul(1, $view->items['wholeAmount'], 2);
                    $cancellation['taxAmount'] = $batchTaxAmount; // bcmul(-1, $payment['taxAmount'], 2);
                    $cancellation['transactionDate'] = $view->items['cancellationDate'];
                    $cancellation['createUser'] = $_SESSION['un'];
                    $cancellation['createDate'] = TODAY;

                    // Perform bypass variable if multiple transaction is flagged
                    if ($view->items['singleTransactionFlag']) {
                        $_payment['transactionAmount'] = bcmul(-1, $tmp_can_amount, 2);
                        $_payment['taxAmount'] = bcmul(-1, $tmp_payment_tax_amount, 2);
                        $_payment['netAmount'] = $_payment['transactionAmount'] - $_payment['taxAmount'];

                        $batch['amount'] = bcmul(-1, $tmp_can_amount, 2);

                        $cancellation['transactionAmount'] = bcmul(-1, $tmp_can_amount, 2);
                        $cancellation['taxAmount'] = bcmul(-1, $tmp_alloc_txn_tax_amount, 2);
                    }

                    // After building variables perform INSERT
                    dbInsertPayableTransaction($_payment);
                    dbInsertBatch($batch);
                    dbInsertAllocation($cancellation);
                    //                    $caapsMessage = caapsAPI_cancel($allocation['toBatch'], $allocation['toLine'], $cancellation['transactionDate'], 'CANCEL', $cancellation['allocationNumber'],'');

                    // -- reverse the bank rec tables
                    switch ($view->items['paymentType']) {
                        case PAY_BPAY:
                            $eft = dbGetEFT($view->items['bankID'], $view->items['referenceNumber']);
                            $eft['batchNumber'] = $batchNumber;
                            $eft['amount'] = bcmul(
                                1,
                                ($view->items['singleAmount'] ? ($view->items['singleAmount'] * -1) : $batchAmount * -1),
                                2
                            );
                            $eft['batchDate'] = $view->items['cancellationDate'];
                            // -- insert new pmbg_dir_dep
                            $eft['cancelledBy'] = $_SESSION['un'];
                            $eft['cancelledTime'] = date('Y-m-d H:i:s');
                            $eft['cancelReason'] = $view->items['cancelReason'];
                            dbAddBankRecForEFT($eft);
                            break;

                        case PAY_EFT:
                            $eft = dbGetEFT($view->items['bankID'], $view->items['referenceNumber']);
                            $eft['batchNumber'] = $batchNumber;
                            $eft['amount'] = bcmul(
                                1,
                                ($view->items['singleAmount'] ? ($view->items['singleAmount'] * -1) : $batchAmount * -1),
                                2
                            );
                            $eft['batchDate'] = $view->items['cancellationDate'];
                            $eft['pmbg_cancel_reference'] = $cancel_reference;
                            // -- insert new pmbg_dir_dep
                            $eft['cancelledBy'] = $_SESSION['un'];
                            $eft['cancelReason'] = $view->items['cancelReason'];
                            $eft['cancelledTime'] = date('Y-m-d H:i:s');
                            dbAddBankRecForEFT($eft);
                            break;

                        case PAY_CHQ:
                            // -- grab old pmbc_reco_chq

                            // -- update pmbc_reco_chq
                            $lineNumber = ((int) dbLineNumberForCheque(
                                $view->items['referenceNumber'],
                                $view->items['bankID'],
                                true
                            ));
                            $cheque = dbGetCheque($view->items['bankID'], $view->items['referenceNumber'], true);

                            $insertCheque = false;
                            if ($view->items['singleAmount'] || $batchAmount != $cheque['chequeAmount']) {
                                $view->items['singleAmount'] = ($view->items['singleAmount'] ? $view->items['singleAmount'] : $batchAmount);
                                $countCheque = dbCountCheque(
                                    $view->items['bankID'],
                                    $view->items['referenceNumber'],
                                    $lineNumber
                                );
                                $cheque['amountHistory'] = $countCheque['amountHistory'] + $view->items['singleAmount'];
                                dbSaveCheque($cheque);

                                if ($cheque['chequeAmount'] == ($countCheque['total'] + $view->items['singleAmount'])) {
                                    $insertCheque = true;
                                }

                                $cheque['chequeAmount'] = 0;
                                $cheque['presentedAmount'] = 0;
                                $cheque['presentedDate'] = null;
                                $cheque['history'] = 1;
                                $cheque['cancelledAmount'] = $view->items['singleAmount'];
                                $cheque['cancelledDate'] = $view->items['cancellationDate'];
                                $cheque['cancelledBy'] = $_SESSION['un'];
                                $cheque['cancelledTime'] = date('Y-m-d H:i:s');
                                $cheque['cancelReason'] = $view->items['cancelReason'];
                                dbAddCheque($cheque);
                            } else {
                                $cheque['cancelledAmount'] = $batchAmount;
                                $cheque['cancelledDate'] = $view->items['cancellationDate'];
                                $cheque['cancelledBy'] = $_SESSION['un'];
                                $cheque['cancelledTime'] = date('Y-m-d H:i:s');
                                $cheque['cancelReason'] = $view->items['cancelReason'];

                                // -- update pmbc_reco_chq
                                dbSaveCheque($cheque);
                                $insertCheque = true;
                            }

                            if ($insertCheque) {
                                // -- insert new pmbc_reco_chq
                                $o = [];
                                $o['lineNumber'] = $lineNumber + 1;
                                $o['chequeNumber'] = $view->items['referenceNumber'];
                                $o['bankID'] = $view->items['bankID'];
                                $o['chequeAmount'] = '0';
                                $o['presentedAmount'] = '0';
                                $o['cancelledAmount'] = '0';
                                $o['cancelledPresentedAmount'] = '0';
                                $o['destroyed'] = '0';
                                $o['creditorPaid'] = '0';
                                $o['debtorPaid'] = '0';
                                $o['ownerPaid'] = '0';
                                dbAddCheque($o);
                            }

                            break;
                    }

                    $view->items['message'] = 'Successfully cancelled' . $caapsMessage;
                } else {
                    $view->items['message'] = 'There was a problem cancelling your payment';
                }
            }

            if (! noErrors($validationErrors)) {
                $cancellation_validation_error = true;
            }

            // no break
        case 'search':
            if (! isValid($view->items['method'], TEXT_INT, false)) {
                $validationErrors[] = 'You have not selected a search method.';
            }

            if (! $view->items['bankID']) {
                $validationErrors[] = 'You have not selected a bank account.';
            }

            switch ($view->items['method']) {
                case BY_CREDITOR:
                    if (! isValid($view->items['paymentType'], TEXT_INT, false)) {
                        $validationErrors[] = 'You have not selected a payment type';
                    }

                    if (! isValid($view->items['fromDate'], TEXT_SMARTDATE, false)) {
                        $validationErrors[] = 'You have not entered a from date';
                    }

                    if (! isValid($view->items['toDate'], TEXT_SMARTDATE, false)) {
                        $validationErrors[] = 'You have not entered a to date';
                    }

                    $view->items['referenceNumber'] = null;
                    break;
                case BY_REFERENCE:
                    if (! isValid($view->items['paymentType'], TEXT_INT, false)) {
                        $validationErrors[] = 'You have not selected a payment type.';
                    }

                    if (! isValid($view->items['referenceNumber'], TEXT_LOOSE, false)) {
                        $validationErrors[] = 'You have not selected a reference number.';
                    }

                    break;
                case BY_RANGE:
                    if (! isValid($view->items['fromCheque'], TEXT_INT, false)) {
                        $validationErrors[] = 'You have not selected a from cheque';
                    }

                    if (! isValid($view->items['toCheque'], TEXT_INT, false)) {
                        $validationErrors[] = 'You have not selected a to cheque';
                    }

                    $view->items['referenceNumber'] = null;
                    break;
            }

            if (noErrors($validationErrors) || isset($cancellation_validation_error)) {
                switch ($view->items['method']) {
                    case BY_CREDITOR:
                        switch ($view->items['paymentType']) {
                            case PAY_BPAY:
                                $_payments = dbGetEFTsForDateRange(
                                    $view->items['bankID'],
                                    $view->items['supplierID'],
                                    $view->items['fromDate'],
                                    $view->items['toDate'],
                                    'Y'
                                );
                                $temp_payments = [];
                                if ($_payments) {
                                    foreach ($_payments as $p) {
                                        $properties = dbGetPropertiesForBatch($p['batchNumber'], $p['lineNumber']);
                                        foreach ($properties as $prop) {
                                            // $p['properties'] = truncateSet($properties);
                                            $p['startDate'] = dbGetMinDateForProperties($properties);
                                            $p['endDate'] = dbGetMaxDateForProperties($properties);
                                            $p['properties'] = $prop;
                                            if ($p['transactionAmount'] != $p['cancellationAmount']) {
                                                $temp_payments[] = $p;
                                            }
                                        }
                                    }
                                }

                                break;
                            case PAY_EFT:
                                $_payments = dbGetEFTsForDateRange(
                                    $view->items['bankID'],
                                    $view->items['supplierID'],
                                    $view->items['fromDate'],
                                    $view->items['toDate'],
                                    'X'
                                );
                                $temp_payments = [];
                                if ($_payments) {
                                    foreach ($_payments as $p) {
                                        $properties = dbGetPropertiesForBatch($p['batchNumber'], $p['lineNumber']);
                                        foreach ($properties as $prop) {
                                            // $p['properties'] = truncateSet($properties);
                                            $p['startDate'] = dbGetMinDateForProperties($properties);
                                            $p['endDate'] = dbGetMaxDateForProperties($properties);
                                            $p['properties'] = $prop;
                                            if ($p['transactionAmount'] != $p['cancellationAmount']) {
                                                $temp_payments[] = $p;
                                            }
                                        }
                                    }
                                }

                                break;
                            case PAY_CHQ:
                                $payments = dbGetChequesForDateRange(
                                    $view->items['bankID'],
                                    $view->items['supplierID'],
                                    $view->items['fromDate'],
                                    $view->items['toDate']
                                );
                                $temp_payments = [];
                                if ($payments) {
                                    // Add this to avoid issue of duplicating the re-used cheque
                                    $batchArray = [];
                                    $batchArray2 = [];
                                    foreach ($payments as $index => $rs) {
                                        $batchArray[$rs['referenceNumber']] = $rs['batchNumber'];
                                        $batchArray2[$rs['referenceNumber']] = $rs['ref_assign'];
                                    }

                                    foreach ($payments as $index => $rs) {
                                        if ($batchArray[$rs['referenceNumber']] != $rs['batchNumber']) {
                                            unset($payments[$index]);
                                        } elseif ($batchArray2[$rs['referenceNumber']] != $rs['ref_assign']) {
                                            unset($payments[$index]);
                                        }
                                    }

                                    // END: Add this to avoid issue of duplicating the re-used cheque

                                    foreach ($payments as &$p) {
                                        $properties = dbGetPropertiesForBatch($p['batchNumber'], $p['lineNumber']);
                                        foreach ($properties as $prop) {
                                            // $p['properties'] = truncateSet($properties);
                                            $p['startDate'] = dbGetMinDateForProperties($properties);
                                            $p['endDate'] = dbGetMaxDateForProperties($properties);
                                            $p['properties'] = $prop;
                                            $temp_payments[] = $p;
                                        }
                                    }
                                }

                                break;
                        }

                        $view->items['paymentList'] = $temp_payments;
                        if (empty($view->items['paymentList'])) {
                            $validationErrors[] = 'Could not find any payments matching your search criteria';
                        }

                        break;


                    case BY_REFERENCE:
                        break;


                    case BY_RANGE:
                        $_payments = dbGetChequesForChequeRange(
                            $view->items['bankID'],
                            $view->items['fromCheque'],
                            $view->items['toCheque']
                        );
                        $view->items['paymentType'] = PAY_CHQ;

                        $temp_payments = [];
                        if ($_payments) {
                            // Add this to avoid issue of duplicating the re-used cheque
                            $batchArray = [];
                            $batchArray2 = [];
                            foreach ($_payments as $index => $rs) {
                                $batchArray[$rs['referenceNumber']] = $rs['batchNumber'];
                                $batchArray2[$rs['referenceNumber']] = $rs['ref_assign'];
                            }

                            foreach ($_payments as $index => $rs) {
                                if ($batchArray[$rs['referenceNumber']] != $rs['batchNumber']) {
                                    unset($_payments[$index]);
                                } elseif ($batchArray2[$rs['referenceNumber']] != $rs['ref_assign']) {
                                    unset($_payments[$index]);
                                }
                            }

                            // END: Add this to avoid issue of duplicating the re-used cheque

                            foreach ($_payments as &$p) {
                                $properties = dbGetPropertiesForBatch($p['batchNumber'], $p['lineNumber']);
                                foreach ($properties as $prop) {
                                    // $p['properties'] = truncateSet ($properties);
                                    $p['startDate'] = dbGetMinDateForProperties($properties);
                                    $p['endDate'] = dbGetMaxDateForProperties($properties);
                                    $p['properties'] = $prop;
                                    $temp_payments[] = $p;
                                }
                            }
                        }

                        $view->items['paymentList'] = $temp_payments;
                        if (empty($view->items['paymentList'])) {
                            $validationErrors[] = 'Could not find any payments matching your search criteria';
                        }

                        break;
                }
            }

            break;
    }

    if ($view->items['referenceNumber']) {
        $_payments = null;


        switch ($view->items['paymentType']) {
            case PAY_CHQ:
                $_cheque = dbGetChequeByReference($view->items['bankID'], $view->items['referenceNumber']);
                if ($_cheque) {
                    $_payments[] = $_cheque;
                }

                // Add this to avoid issue of duplicating the re-used cheque
                $batchArray = [];
                $batchArray2 = [];
                foreach ($_payments as $index => $rs) {
                    $batchArray[$rs['referenceNumber']] = $rs['batchNumber'];
                    $batchArray2[$rs['referenceNumber']] = $rs['ref_assign'];
                }

                foreach ($_payments as $index => $rs) {
                    if ($batchArray[$rs['referenceNumber']] != $rs['batchNumber']) {
                        unset($_payments[$index]);
                    } elseif ($batchArray2[$rs['referenceNumber']] != $rs['ref_assign']) {
                        unset($_payments[$index]);
                    }
                }

                // END: Add this to avoid issue of duplicating the re-used cheque

                break;
            case PAY_EFT:
                $_payments = dbGetEFTByReference($view->items['bankID'], $view->items['referenceNumber'], null, 'X');
                break;
            case PAY_BPAY:
                $_payments = dbGetEFTByReference($view->items['bankID'], $view->items['referenceNumber'], null, 'Y');
                break;
                // break;
        }

        if (! empty($_payments)) {
            $temp_payments = [];
            foreach ($_payments as &$p) {
                $properties = dbGetPropertiesForBatch($p['batchNumber'], $p['lineNumber']);
                foreach ($properties as $prop) {
                    // $p['properties'] = truncateSet ($properties);
                    $p['startDate'] = dbGetMinDateForProperties($properties);
                    $p['endDate'] = dbGetMaxDateForProperties($properties);
                    // pre_print_r(array($properties, $p['properties'], $p['startDate'], $p['endDate'], $_payments));
                    $p['properties'] = $prop;
                    $temp_payments[] = $p;
                }
            }

            $view->items['paymentList'] = $temp_payments;
        }

        if (empty($view->items['paymentList'])) {
            $validationErrors[] = 'Could not find any payments matching your search criteria';
        }
    }


    // pre_print_r($view->items['paymentList']);

    // Data parser: Normalizes data fetch only with specific reference #
    // Split with cancelled payment amount
    // DK 2019
    // var_dump($view->items['paymentList'] );
    if (in_array($view->items['method'], [BY_REFERENCE, BY_RANGE, BY_CREDITOR])) {
        $view->items['paymentListConsolidated'] = $view->items['paymentList'];

        $cancel_row = [];
        // Get original payment list, split payable and cancell allocations
        foreach ($view->items['paymentList'] as $value) {
            // echo $value['batchNumber'] . "<br><br>";
            $batchPayment = dbGetPaymentsByBatch($value['batchNumber'], $value['creditorID'], $value['properties']);
            $cancel_zero = [];
            foreach ($batchPayment as $key1 => $value1) {
                if ($value1['paymentAmount'] < 0) {
                    $cancel_row[$value1['fBatch']][$value1['batchNumber']][$value1['lineNumber']][$value1['creditorID']][$value['referenceNumber']][$value1['invoiceNumber']][$value1['propertyID']] = $value1['paymentAmount'];
                }

                $cancel_zero[] = $value1['paymentAmount'];
            }

            if (array_sum($cancel_zero) > 0) {
                foreach ($batchPayment as $value1) {
                    $defaultDate = $value['transactionDate'];
                    $calendar = dbGetPeriod($value1['propertyID'], $value['transactionDate']);
                    if (! isset($calendar['closed']) || $calendar['closed'] == 1) {
                        $defaultDate = date('d/m/Y');
                    }

                    ${($value1['paymentAmount'] > 0) ? 'parsed_payment_object' : 'parsed_payment_object_cancelled'}[$value['referenceNumber'] . $value['properties']][] = [
                        // Static values from original batch content data
                        'batchNumber' => $value['batchNumber'],
                        'lineNumber' => $value['lineNumber'],
                        'referenceNumber' => $value['referenceNumber'],
                        'referenceNumberChq' => $value['referenceNumber'],
                        'transactionDate' => $value['transactionDate'],
                        'defaultDate' => $defaultDate,
                        // Non-static based from batch payments
                        'transactionAmount' => $value1['paymentAmount'],
                        'creditorID' => $value1['creditorID'],
                        'creditorName' => $value1['creditorName'],
                        'presentedDate' => $value['presentedDate'],
                        'properties' => $value1['propertyID'],
                        'startDate' => dbGetLastReconciledDateForProperty($value1['propertyID']),
                        'endDate' => $value1['toDate'],
                        // New value- added
                        'description' => $value1['description'],
                        'pmxcAllocNR' => $value1['pmxcAllocNR'],
                        'invoiceNumber' => $value1['invoiceNumber'],
                        'cancelled' => (isset($cancel_row[$value1['fBatch']][$value1['batchNumber']][$value1['lineNumber']][$value1['creditorID']][$value['referenceNumber']][$value1['invoiceNumber']][$value1['propertyID']]) ? $cancel_row[$value1['fBatch']][$value1['batchNumber']][$value1['lineNumber']][$value1['creditorID']][$value['referenceNumber']][$value1['invoiceNumber']][$value1['propertyID']] * -1 : 0),
                    ];

                    if ($value1['paymentAmount'] < 0 && $value1['transactionType'] == 'CRE') {
                        $view->items['creditTransaction'][$value['referenceNumber'] . $value['creditorID'] . $value['properties']] = $value1['paymentAmount'];
                    }
                }
            }
        }

        //        // Rebuild payment object cancelled, clean and sum all by its key
        //        foreach($parsed_payment_object_cancelled AS $key => $value)
        //        {
        //            $parsed_payment_object_cancelled_clean[$key]['transactionSum'] = array_sum(array_column($value,'transactionAmount'));
        //        }
        //
        //        // Remove cancelled keys
        //        foreach($parsed_payment_object_cancelled AS $key => $value)
        //        {
        //            foreach($value AS $key1 => $value1)
        //            {
        //                $parsed_payment_object_tmp[$key][] = multi_search_payments($parsed_payment_object[$key], ($value1['transactionAmount']*-1), $value1['properties'], $value1['creditorID']);
        //            }
        //        }
        //
        //        $parsed_payment_object_clean = $parsed_payment_object;
        //        foreach($parsed_payment_object_tmp AS $key => $value)
        //        {
        //            foreach($value AS $key1 => $value1)
        //            {
        //                unset($parsed_payment_object_clean[$key][$value1]);
        //            }
        //        }

        if (in_array($view->items['paymentType'], [PAY_EFT, PAY_BPAY]) && $view->items['method'] == BY_REFERENCE) {
            foreach ($view->items['paymentListConsolidated'] as $value) {
                foreach ($parsed_payment_object[$view->items['referenceNumber'] . $value['properties']] as $value1) {
                    if ($value1['creditorID'] == $value['creditorID'] && $value1['properties'] == $value['properties']) {
                        $eft_object[$value['creditorID'] . $value['properties']][] = $value1;
                    }
                }
            }

            unset($parsed_payment_object_clean);
            $parsed_payment_object_clean = $eft_object;
        }

        if ($view->items['isMultipleTransactionPerCheque'] = ! empty($parsed_payment_object)) {
            count(
                $parsed_payment_object
            );
        }

        $view->items['paymentList'] = (empty($parsed_payment_object_clean) ? $parsed_payment_object : $parsed_payment_object_clean);
        $view->items['cancelledPaymentObject'] = $parsed_payment_object_cancelled_clean;
    }

    $view->items['methodList'] =
    [
        BY_CREDITOR => 'By Creditor',
        BY_REFERENCE => 'By Cheque or EFT Number',
        BY_RANGE => 'By Cheque Range',
    ];
    $view->items['bankList'] = dbGetBankAccounts();
    $view->items['supplierList'] = dbCompanyList(SUPPLIER, true, OWNER);

    $view->items['paymentTypes'][PAY_EFT] = 'EFT';
    if (cdf_isAU()) {
        $view->items['paymentTypes'][PAY_BPAY] = 'BPay';
    }

    $view->items['paymentTypes'][PAY_CHQ] = 'Cheque';

    $view->items['validationErrors'] = $validationErrors;
    if (! isset($view->items['method'])) {
        $view->items['method'] = BY_CREDITOR;
    }

    if ($view->items['methodList'] == 'BY_REFERENCE' && ! isset($view->items['paymentType'])) {
        $view->items['paymentType'] = PAY_EFT;
    }

    if (! isset($view->items['cancellationDate'])) {
        $view->items['cancellationDate'] = TODAY;
    }

    if (! isset($view->items['toDate'])) {
        $view->items['toDate'] = TODAY;
    }

    if (! isset($view->items['fromDate'])) {
        $view->items['fromDate'] = oneMonthBefore(TODAY);
    }

    $view->items['ASSET_DOMAIN'] = ASSET_DOMAIN;
    $view->render();
}

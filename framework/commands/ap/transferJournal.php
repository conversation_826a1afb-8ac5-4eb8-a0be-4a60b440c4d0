<?php

include SYSTEMPATH . '/modules/TransferJournal/TransferJournalService.php';

/**
 * Validates the transfer details in the provided view object.
 *
 * @param  object  $view  An object containing transfer details, including properties and leases.
 * @return bool Returns true if the transfer conditions are met, otherwise false.
 */
function validateProperties(object $view): bool
{
    return  (
        ($view->items['toPropertyID'] != $view->items['fromPropertyID']) ||
        ($view->items['toLeaseID'] != $view->items['fromLeaseID'])
    )
        && $view->items['toPropertyID']
        && $view->items['transactionDate'];
}

function transferJournal(&$context)
{
    if (! isPostback()) {
        $view = new MasterPage(userViews(), '/ap/transferJournal.html');
        $view->setSection($context['module']);
    } else {
        $view = new UserControl(userViews(), '/ap/transferJournal.html');
    }

    $view->bindAttributesFrom($_REQUEST);
    if (isset($_GET['transactionAmount'])) {
        $view->bindAttributesFrom($_GET);
    }

    $view->items['accountGroupList'] =
    [
        'EXPOWN' => 'Owners Expenditure',
        'EXPDR' => 'Directly Recoverable Expenditure',
        'EXPVO' => ucwords(strtolower($_SESSION['country_default']['variable_outgoings'])) . ' Expenditure',
    ];

    $params['container'] = 'content';
    $params['url'] = '?command=transferJournal&module=ap';
    $params['keyed'] = true;
    $params['keyField'] = 'accountID';
    $params['valueField'] = 'accountName';
    $params['groupField'] = 'accountGroup';
    $params['groupDescriptions'] = $view->items['accountGroupList'];
    $params['textAttributes'] = 'required = "true"';
    $view->items['params'] = $params;

    $view->items['fromPropertyIsLedger'] = dbGetPropertyIsLedger($view->items['fromPropertyID']);
    $view->items['toPropertyIsLedger'] = dbGetPropertyIsLedger($view->items['toPropertyID']);

    $view->items['fromLeaseList'] = $view->items['fromPropertyIsLedger'] ? dbLeaseList($view->items['fromPropertyID']) : [];

    $view->items['toLeaseList'] = $view->items['toPropertyIsLedger'] ? dbLeaseList($view->items['toPropertyID']) : [];

    // -- load the existing transfer journal from temp
    if ($view->items['action'] == 'view') {
        $view->bindAttributesFrom(dbGetTempTransferJournal($view->items['transferJournalID']));
        $fromPropertyTax = dbGetTaxStatus($view->items['fromPropertyID']); // -- grabs the tax status of the owner
        $view->items['fromPropertyTaxID'] = $fromPropertyTax['taxCode'];
        $toPropertyTax = dbGetTaxStatus($view->items['toPropertyID']); // -- grabs the tax status of the owner
        $view->items['toPropertyTaxID'] = $toPropertyTax['taxCode'];

        if (! $view->items['toPropertyIsLedger']) {
            $view->items['toPropertyBalance'] = balanceAsAt($view->items['toPropertyID'], $view->items['transactionDate']);
        } elseif ($view->items['toPropertyIsLedger']) {
            $view->items['toPropertyBalance'] = leaseBalanceAsAt($view->items['toPropertyID'], $view->items['toLeaseID'], $view->items['transactionDate']);
        } else {
            $view->items['toPropertyBalance'] = 0;
        }

        if (! $view->items['fromPropertyIsLedger']) {
            $view->items['fromPropertyBalance'] = balanceAsAt($view->items['fromPropertyID'], $view->items['transactionDate']);
        } elseif ($view->items['fromPropertyIsLedger'] && $view->items['fromLeaseID']) {
            $view->items['fromPropertyBalance'] = leaseBalanceAsAt($view->items['fromPropertyID'], $view->items['fromLeaseID'], $view->items['transactionDate']);
        } else {
            $view->items['fromPropertyBalance'] = 0;
        }
    }

    $view->items['taxRateList'] = dbGetTaxRates();
    $taxRates = mapParameters($view->items['taxRateList'], 'taxCode', 'taxRate');
    $tax = dbGetCompanyTaxStatus($view->items['creditorID']);

    if ((! isset($view->items['fromTaxRateID'])) || ($view->items['action'] == 'changePrimary')) {
        $fromPropertyTax = dbGetTaxStatus($view->items['fromPropertyID']); // -- grabs the tax status of the owner
        $view->items['fromPropertyTaxID'] = $fromPropertyTax['taxCode'];
        $view->items['fromTaxRateID'] = ($fromPropertyTax['taxRate'] == 0) ? $fromPropertyTax['taxCode'] : $tax['taxCode'];
    }

    if ((! isset($view->items['toTaxRateID']))  || ($view->items['action'] == 'changePrimary')) {
        $toPropertyTax = dbGetTaxStatus($view->items['toPropertyID']); // -- grabs the tax status of the owner
        $view->items['toPropertyTaxID'] = $toPropertyTax['taxCode'];
        $view->items['toTaxRateID'] = ($toPropertyTax['taxRate'] == 0) ? $toPropertyTax['taxCode'] : $tax['taxCode'];
    }

    if ($view->items['transactionAmount']) {
        $view->items['transactionAmount'] = round($view->items['transactionAmount'], 2);
    }

    $fromTaxRate = $taxRates[$view->items['fromTaxRateID']];
    $toTaxRate = $taxRates[$view->items['toTaxRateID']];
    $view->items['fromTaxAmount'] = round(($fromTaxRate / bcadd(100, $fromTaxRate, 2)) * $view->items['transactionAmount'], 2);
    $view->items['fromNetAmount'] = bcsub($view->items['transactionAmount'], $view->items['fromTaxAmount'], 2);
    $view->items['toTaxAmount'] = round(($toTaxRate / bcadd(100, $toTaxRate, 2)) * $view->items['transactionAmount'], 2);
    $view->items['toNetAmount'] = bcsub($view->items['transactionAmount'], $view->items['toTaxAmount'], 2);

    // check bank details
    $fromBankDetails = dbGetBankDetails($view->items['fromPropertyID']);
    $toBankDetails = dbGetBankDetails($view->items['toPropertyID']);

    $view->items['fromBankID'] = $fromBankDetails['bankID'];
    $view->items['toBankID'] = $toBankDetails['bankID'];

    if ($view->items['action'] == 'changePrimary') {

        if (! $view->items['toPropertyIsLedger']) {
            $view->items['toPropertyBalance'] = balanceAsAt($view->items['toPropertyID'], $view->items['transactionDate']);
        } elseif ($view->items['toPropertyIsLedger']) {
            $view->items['toPropertyBalance'] = leaseBalanceAsAt($view->items['toPropertyID'], $view->items['toLeaseID'], $view->items['transactionDate']);
        } else {
            $view->items['toPropertyBalance'] = 0;
        }

        if (! $view->items['fromPropertyIsLedger']) {
            $view->items['fromPropertyBalance'] = balanceAsAt($view->items['fromPropertyID'], $view->items['transactionDate']);
        } elseif ($view->items['fromPropertyIsLedger'] && $view->items['fromLeaseID']) {
            $view->items['fromPropertyBalance'] = leaseBalanceAsAt($view->items['fromPropertyID'], $view->items['fromLeaseID'], $view->items['transactionDate']);
        } else {
            $view->items['fromPropertyBalance'] = 0;
        }

        $attachToOwnerReportFileDefault = dbGetPropertyDefaultAttachToOwner($view->items['fromPropertyID']);
        $view->items['attachOwnerReportFile'] = $attachToOwnerReportFileDefault === 0 ? $view->items['attachOwnerReportFile'] : $attachToOwnerReportFileDefault;
    }

    if ($fromBankDetails['bankID'] != $toBankDetails['bankID'] && $view->items['fromPropertyID'] != '' && $view->items['toPropertyID'] != '') {
        $validationErrors[] = 'Debit and Credit Properties should have the same bank code';
    }

    $transferJournalService = TransferJournalService::getInstance();
    $listOfAttachments = $transferJournalService->listOfAttachments((int) $view->items['transferJournalID']);
    ['attachedDocumentsList' => $attachedDocumentsList, 'attachToARFile' => $attachToARFile, 'attachToOwnerReportFile' => $attachToOwnerReportFile] = $listOfAttachments;

    switch ($view->items['action']) {
        case 'process':
            $view->items['procesed'] = 'false';

            if (! isValid($view->items['creditorID'], TEXT_KEY, false)) {
                $validationErrors[] = 'You need to select a supplier';
            }

            if (! isValid($view->items['fromPropertyID'], TEXT_KEY, false)) {
                $validationErrors[] = 'You need to enter a debit property';
            } else {
                $bankDetails = dbGetBankDetails($view->items['toPropertyID']);
                if (validateProperties($view)) {
                    if (! $view->items['toPropertyIsLedger']) {
                        $balance = balanceAsAt($view->items['toPropertyID'], $view->items['transactionDate']);
                    } elseif ($view->items['toPropertyIsLedger']) {
                        $balance = leaseBalanceAsAt($view->items['toPropertyID'], $view->items['toLeaseID'], $view->items['transactionDate']);
                    }

                    if (($bankDetails['debitBarred']) && (isAdmin($context)) && (bccomp($view->items['transactionAmount'], $balance, 2) === 1)) {
                        $validationErrors[] = 'You do not have enough money in the property ' . $view->items['toPropertyID'] .
                            ($view->items['toPropertyIsLedger'] ? ' sub-ledger ' . $view->items['toLeaseID'] : '') .
                            ' to transfer - current balance ' . toMoney($balance);
                    }

                    $negativeBalanceDate = dbGetNegativeFutureBalance($view->items['toPropertyID'], $view->items['transactionDate'], $view->items['transactionAmount']);
                    if ($bankDetails['debitBarred'] && $negativeBalanceDate['allocDate'] != '') {
                        $validationErrors[] = 'Processing is not possible because it would overdraw ' . $view->items['toPropertyID'] .
                            ($view->items['toPropertyIsLedger'] ? ' sub-ledger ' . $view->items['toLeaseID'] : '') .
                            ' as of ' . $negativeBalanceDate['allocDate'] . ' by ' . toMoney($negativeBalanceDate['dayBalance'] * -1);
                    }
                }
            }

            if (! isValid($view->items['toPropertyID'], TEXT_KEY, false)) {
                $validationErrors[] = 'You need to enter a credit property';
            }

            if ($view->items['fromPropertyIsLedger'] && ! isValid($view->items['fromLeaseID'], TEXT_KEY, false)) {
                $validationErrors[] = 'You need to enter a debit sub-ledger';
            }

            if ($view->items['toPropertyIsLedger'] && ! isValid($view->items['toLeaseID'], TEXT_KEY, false)) {
                $validationErrors[] = 'You need to enter a credit sub-ledger';
            }

            if (! isValid($view->items['fromAccountID'], TEXT_KEY, false)) {
                $validationErrors[] = 'You need to enter a debit account';
            }

            if (! isValid($view->items['toAccountID'], TEXT_KEY, false)) {
                $validationErrors[] = 'You need to enter a credit account';
            }

            if (! isValid($view->items['fromTaxRateID'], TEXT_KEY, false)) {
                $validationErrors[] = 'You need to enter a tax rate (from)';
            }

            if (! isValid($view->items['toTaxRateID'], TEXT_KEY, false)) {
                $validationErrors[] = 'You need to enter a tax rate (to)';
            }

            if (! isValid($view->items['fromDescription'], TEXT_HTML, false)) {
                $validationErrors[] = 'You need to enter a debit description';
            }

            if (! isValid($view->items['toDescription'], TEXT_HTML, false)) {
                $validationErrors[] = 'You need to enter a credit description';
            }

            if (! isValid($view->items['fromFromDate'], TEXT_SMARTDATE, false)) {
                $validationErrors[] = 'You need to enter a debit date  (from)';
            }

            if (! isValid($view->items['toFromDate'], TEXT_SMARTDATE, false)) {
                $validationErrors[] = 'You need to enter a debit date (to)';
            }

            if (toDateStamp($view->items['fromFromDate']) > toDateStamp($view->items['fromToDate'])) {
                $validationErrors[] = 'Your from date cannot be before your to date';
            }

            if (toDateStamp($view->items['toFromDate']) > toDateStamp($view->items['toToDate'])) {
                $validationErrors[] = 'Your from date cannot be before your to date';
            }

            if (! isValid($view->items['fromToDate'], TEXT_SMARTDATE, false)) {
                $validationErrors[] = 'You need to enter a credit date (from)';
            }

            if (! isValid($view->items['toToDate'], TEXT_SMARTDATE, false)) {
                $validationErrors[] = 'You need to enter a credit date (to)';
            }

            $fromPeriod = dbGetPeriod($view->items['fromPropertyID'], $view->items['transactionDate']);
            if (! $fromPeriod || $fromPeriod['closed']) {
                $validationErrors[] = 'The period is closed for the transaction date chosen on the property ' . $view->items['fromPropertyID'];
            }

            $toPeriod = dbGetPeriod($view->items['toPropertyID'], $view->items['transactionDate']);
            if (! $toPeriod || $toPeriod['closed']) {
                $validationErrors[] = 'The period is closed for the transaction date chosen on   the property ' . $view->items['toPropertyID'];
            }

            if (bccomp($view->items['transactionAmount'], 0, 2) !== 1) {
                $validationErrors[] = 'You cannot transfer a zero or negative amount';
            }

            /* check both from and to properties */
            // -- FROM
            $lastReconciled = dbGetLastReconciledDateForProperty($view->items['fromPropertyID']);
            if (toTimeStamp($view->items['transactionDate']) < toTimeStamp($lastReconciled)) {
                $validationErrors['transferJournal'] = 'You cannot post a transfer prior to the last bank reconciliation date (' . $lastReconciled . ')';
            }

            // -- TO
            $lastReconciled = dbGetLastReconciledDateForProperty($view->items['toPropertyID']);
            if (toTimeStamp($view->items['transactionDate']) < toTimeStamp($lastReconciled)) {
                $validationErrors['transferJournal'] = 'You cannot post a transfer prior to the last bank reconciliation date (' . $lastReconciled . ')';
            }

            if (noErrors($validationErrors)) {
                if (isAdmin($context)) {
                    // -- from: insert into AP trans  (A) CRE -
                    // -- from: insert into AP trans (B) PAY +
                    // -- get next allocation number
                    // -- insert into AP alloc (B->A)


                    // CRE and PAY
                    $batchJournal = dbGetNextJournalBatch('AP');
                    $toBatchNumber = dbGetNextPayableBatchNumber();
                    $transaction = [];
                    $transaction['batchJournal'] = $batchJournal;
                    $creBatch = $transaction['batchNumber'] = $toBatchNumber;
                    $creLine = $transaction['lineNumber'] = 1;
                    $transaction['transactionType'] = TYPE_CREDIT;
                    $transaction['year'] = $fromPeriod['year'];
                    $transaction['period'] = $fromPeriod['period'];
                    $transaction['bankID'] = $bankDetails['bankID'];

                    $transaction['propertyID'] = $view->items['fromPropertyID'];

                    if (isset($view->items['fromPropertyUnitID']) && ! $view->items['fromPropertyIsLedger']) {
                        $transaction['unitID'] = $view->items['fromPropertyUnitID'];
                        $transaction['leaseID'] = null;
                    } elseif (isset($view->items['fromLeaseID']) && $view->items['fromPropertyIsLedger']) {
                        $transaction['unitID'] = null;
                        $transaction['leaseID'] = $view->items['fromLeaseID'];
                    }

                    $transaction['accountID'] = $view->items['fromAccountID'];
                    $transaction['transactionDate'] = $view->items['transactionDate'];
                    $transaction['createUser'] = $_SESSION['un'];
                    $transaction['createDate'] = TODAY;
                    $transaction['creditorID'] = $view->items['creditorID'];
                    $transaction['description'] = $view->items['fromDescription'];
                    $transaction['transactionAmount'] = bcmul($view->items['transactionAmount'], -1, 2);
                    $transaction['netAmount'] = bcmul($view->items['fromNetAmount'], -1, 2);
                    $transaction['taxAmount'] = bcmul($view->items['fromTaxAmount'], -1, 2);
                    $transaction['taxCode'] = $view->items['fromTaxRateID'];

                    $transaction['bpayReference'] = '';
                    $transaction['fromDate'] = $view->items['fromFromDate'];
                    $transaction['toDate'] = $view->items['fromToDate'];
                    $transaction['orderNumber'] = null;
                    $transaction['invoiceNumber'] = null;
                    $transaction['reference'] = $toBatchNumber; // -- ******** -  using the batch number for task #1014029 - adding a reference number to ref_1 so that a value appears in the suppliers rec : Andrew
                    $transaction['dueDate'] = null;
                    $transaction['isJournal'] = 1;

                    dbInsertPayableTransaction($transaction);
                    $transaction['attachAR'] = $view->items['attachARFile'];
                    $transaction['attachOwnerReportFile'] = $view->items['attachOwnerReportFile'];
                    $transaction['documentApTag'] = 1;
                    $files = $_FILES['files'] ?? [];
                    $uploadedFilesCredit = $transferJournalService->uploadAttachment($files);
                    $documentIds = [];
                    foreach ($uploadedFilesCredit as $row) {
                        $transaction['file_name'] = "{$row['file_name']}.{$row['extension']}";
                        $lastId = $transferJournalService->addAttachmentToDocumentTable($transaction);
                        $documentIds[] = $lastId;
                        $transaction['lastID'] = $lastId;
                        $transferJournalService->addAttachmentToDocumentInvoiceTable($transaction);
                    }

                    $transferJournalService->updateAttachmentToDocumentTables($attachedDocumentsList, $transaction['batchNumber'], TYPE_CREDIT);

                    if (GL_ACTIVE) { // GL for CREDIT - FROM
                        $gl = new GeneralLedger();
                        $gl->transactionType = $transaction['transactionType'];
                        $gl->transactionDate = $view->items['transactionDate'];
                        $gl->description = $transaction['description'];
                        $gl->year = $transaction['year'];
                        $gl->period = $transaction['period'];
                        $gl->propertyID = $transaction['propertyID'];
                        $gl->leaseID = $transaction['leaseID'];
                        $gl->companyID = $transaction['creditorID'];
                        $gl->source = GL_SOURCE_AP;

                        $gl->fromDate = $transaction['fromDate'];
                        $gl->toDate = $transaction['toDate'];
                        $gl->batchID = $transaction['batchNumber'];
                        $gl->lineNumber = $transaction['lineNumber'];

                        $t = new Transaction($gl);    // -- detail entry : adopt the data from the

                        $_amount = bcmul($transaction['netAmount'], 1, 2);

                        $gl->update($transaction['accountID'], GL_BALANCE_ACCRUALS, $_amount);
                        $t->add($transaction['accountID'], BASIS_ACCRUALS, $_amount);

                        $_amount = bcmul($transaction['taxAmount'], 1, 2);

                        $gl->update(glAccount(GL_GST_INPUT), GL_BALANCE_ACCRUALS, $_amount);
                        $t->add(glAccount(GL_GST_INPUT), BASIS_ACCRUALS, $_amount);

                        $_amount = bcmul($transaction['transactionAmount'], -1, 2);

                        $gl->update(glAccount(GL_CREDITORS_CONTROL), GL_BALANCE_ACCRUALS, $_amount);
                        $t->add(glAccount(GL_CREDITORS_CONTROL), BASIS_ACCRUALS, $_amount);
                    }


                    $fromBatchNumber = dbGetNextPayableBatchNumber();
                    $transaction['batchNumber'] = $fromBatchNumber;
                    $transaction['lineNumber'] = 1;
                    $transaction['transactionType'] = TYPE_PAYMENT;
                    $transaction['transactionAmount'] = $view->items['transactionAmount'];
                    $transaction['netAmount'] = $view->items['fromNetAmount'];
                    $transaction['taxAmount'] = $view->items['fromTaxAmount'];
                    $transaction['reference'] = $fromBatchNumber; // -- ******** -  using the batch number for task #1014029 - adding a reference number to ref_1 so that a value appears in the suppliers rec : Andrew

                    if (GL_ACTIVE) {
                        $gl = new GeneralLedger();
                        $gl->transactionType = $transaction['transactionType'];
                        $gl->transactionDate = $view->items['transactionDate'];
                        $gl->description = $transaction['description'];
                        $gl->year = $transaction['year'];
                        $gl->period = $transaction['period'];
                        $gl->propertyID = $transaction['propertyID'];
                        $gl->leaseID = $transaction['leaseID'];
                        $gl->companyID = $transaction['creditorID'];
                        $gl->source = GL_SOURCE_AP;

                        $gl->fromDate = $transaction['fromDate'];
                        $gl->toDate = $transaction['toDate'];
                        $gl->batchID = $creBatch;
                        $gl->lineNumber = $creLine;

                        $t = new Transaction($gl);	// -- detail entry : adopt the data from the

                        $_amount = bcmul($transaction['netAmount'], -1, 2);

                        $gl->update($transaction['accountID'], GL_BALANCE_CASH, $_amount);
                        $t->add($transaction['accountID'], BASIS_CASH, $_amount);

                        $_amount = bcmul($transaction['taxAmount'], -1, 2);

                        $gl->update(glAccount(GL_GST_INPUT), GL_BALANCE_CASH, $_amount);
                        $t->add(glAccount(GL_GST_INPUT), BASIS_CASH, $_amount);

                        $_amount = bcmul($transaction['transactionAmount'], 1, 2);

                        $gl->update(glAccount(GL_BANK), GL_BALANCE_CASH, $_amount);
                        $t->add(glAccount(GL_BANK), BASIS_CASH, $_amount);

                        $gl->update(glAccount(GL_BANK), GL_BALANCE_ACCRUALS, $_amount);
                        $t->add(glAccount(GL_BANK), BASIS_ACCRUALS, $_amount);

                        $_amount = bcmul($transaction['transactionAmount'], -1, 2);

                        $gl->update(glAccount(GL_CREDITORS_CONTROL), GL_BALANCE_ACCRUALS, $_amount);
                        $t->add(glAccount(GL_CREDITORS_CONTROL), BASIS_ACCRUALS, $_amount);
                    }

                    // remove account code for PAY when inserting to ap_transaction
                    $PAY_accountID = $transaction['accountID'];
                    $transaction['accountID'] = '';

                    $transaction['batchJournal'] = $batchJournal;
                    dbInsertPayableTransaction($transaction);

                    // change to original account after insert
                    $transaction['accountID'] = $PAY_accountID;

                    $transaction['taxAmount'] = (-1) * $transaction['taxAmount'];
                    $transaction['allocationDate'] = $view->items['transactionDate'];
                    $transaction['allocationNumber'] = dbGetNextAllocationNumber();
                    $transaction['fromBatchNumber'] = $fromBatchNumber;
                    $transaction['fromLineNumber'] = 1;
                    $transaction['toBatchNumber'] = $toBatchNumber;
                    $transaction['toLineNumber'] = 1;
                    $transaction['currentDate'] = TODAY;
                    $transaction['currentTime'] = TODAY;
                    $transaction['fromType'] = TYPE_PAYMENT;
                    $transaction['toType'] = TYPE_CREDIT;

                    dbInsertAllocation($transaction);

                    // -- insert into AP trans (C) INV +
                    // -- insert into AP trans (D) PAY -
                    // -- get next allocation number
                    // -- insert into AP alloc (D->C)

                    $toBatchNumber = dbGetNextPayableBatchNumber();
                    $transaction =  [];
                    $invBatch = $transaction['batchNumber'] = $toBatchNumber;
                    $invLine = $transaction['lineNumber'] = 1;
                    $transaction['transactionType'] = TYPE_INVOICE;
                    $transaction['year'] = $toPeriod['year'];
                    $transaction['period'] = $toPeriod['period'];
                    $bankDetails = dbGetBankDetails($view->items['toPropertyID']);
                    $transaction['bankID'] = $bankDetails['bankID'];

                    $transaction['propertyID'] = $view->items['toPropertyID'];
                    //
                    if (isset($view->items['toPropertyUnitID'])) {
                        $transaction['unitID'] = $view->items['toPropertyUnitID'];
                    }

                    if (isset($view->items['toPropertyUnitID']) && ! $view->items['toPropertyIsLedger']) {
                        $transaction['unitID'] = $view->items['toPropertyUnitID'];
                        $transaction['leaseID'] = null;
                    } elseif (isset($view->items['toLeaseID']) && $view->items['toPropertyIsLedger']) {
                        $transaction['unitID'] = null;
                        $transaction['leaseID'] = $view->items['toLeaseID'];
                    }

                    //
                    $transaction['accountID'] = $view->items['toAccountID'];
                    $transaction['transactionDate'] = $view->items['transactionDate'];
                    $transaction['createUser'] = $_SESSION['un'];
                    $transaction['createDate'] = TODAY;
                    $transaction['creditorID'] = $view->items['creditorID'];
                    $transaction['description'] = $view->items['toDescription'];
                    $transaction['transactionAmount'] = $view->items['transactionAmount'];
                    $transaction['netAmount'] = $view->items['toNetAmount'];
                    $transaction['taxAmount'] = $view->items['toTaxAmount'];
                    $transaction['taxCode'] = $view->items['toTaxRateID'];

                    $transaction['bpayReference'] = '';
                    $transaction['fromDate'] = $view->items['toFromDate'];
                    $transaction['toDate'] = $view->items['toToDate'];
                    $transaction['orderNumber'] = null;
                    $transaction['invoiceNumber'] = null;
                    $transaction['reference'] = $toBatchNumber; // -- ******** -  using the batch number for task #1014029 - adding a reference number to ref_1 so that a value appears in the suppliers rec : Andrew
                    $transaction['dueDate'] = null;
                    $transaction['isJournal'] = 1;

                    $transaction['batchJournal'] = $batchJournal;
                    dbInsertPayableTransaction($transaction);
                    $transaction['attachAR'] = $view->items['attachARFile'];
                    $transaction['attachOwnerReportFile'] = $view->items['attachOwnerReportFile'];
                    $transaction['documentApTag'] = 1;

                    foreach ($documentIds as $row) {
                        $transaction['lastID'] = $row;
                        $transferJournalService->addAttachmentToDocumentInvoiceTable($transaction);
                    }

                    $transferJournalService->updateAttachmentToDocumentTables($attachedDocumentsList, $transaction['batchNumber'], TYPE_INVOICE);

                    if (GL_ACTIVE) { // GL for INVOICE - TO
                        $gl = new GeneralLedger();
                        $gl->transactionType = $transaction['transactionType'];
                        $gl->transactionDate = $view->items['transactionDate'];
                        $gl->description = $transaction['description'];
                        $gl->year = $transaction['year'];
                        $gl->period = $transaction['period'];
                        $gl->propertyID = $transaction['propertyID'];
                        $gl->leaseID = $transaction['leaseID'];
                        $gl->companyID = $transaction['creditorID'];
                        $gl->source = GL_SOURCE_AP;

                        $gl->fromDate = $transaction['fromDate'];
                        $gl->toDate = $transaction['toDate'];
                        $gl->batchID = $transaction['batchNumber'];
                        $gl->lineNumber = $transaction['lineNumber'];

                        $t = new Transaction($gl);    // -- detail entry : adopt the data from the

                        $_amount = bcmul($transaction['netAmount'], 1, 2);

                        $gl->update($transaction['accountID'], GL_BALANCE_ACCRUALS, $_amount);
                        $t->add($transaction['accountID'], BASIS_ACCRUALS, $_amount);

                        $_amount = bcmul($transaction['taxAmount'], 1, 2);

                        $gl->update(glAccount(GL_GST_INPUT), GL_BALANCE_ACCRUALS, $_amount);
                        $t->add(glAccount(GL_GST_INPUT), BASIS_ACCRUALS, $_amount);

                        $_amount = bcmul($transaction['transactionAmount'], -1, 2);

                        $gl->update(glAccount(GL_CREDITORS_CONTROL), GL_BALANCE_ACCRUALS, $_amount);
                        $t->add(glAccount(GL_CREDITORS_CONTROL), BASIS_ACCRUALS, $_amount);
                    }

                    $fromBatchNumber = dbGetNextPayableBatchNumber();
                    $transaction['batchNumber'] = $fromBatchNumber;
                    $transaction['lineNumber'] = 1;
                    $transaction['transactionType'] = TYPE_PAYMENT;
                    $transaction['transactionAmount'] = bcmul($view->items['transactionAmount'], -1, 2);
                    $transaction['netAmount'] = bcmul($view->items['toNetAmount'], -1, 2);
                    $transaction['taxAmount'] = bcmul($view->items['toTaxAmount'], -1, 2);
                    $transaction['reference'] = $fromBatchNumber; // -- ******** -  using the batch number for task #1014029 - adding a reference number to ref_1 so that a value appears in the suppliers rec : Andrew

                    if (GL_ACTIVE) {

                        $gl = new GeneralLedger();
                        $gl->transactionType = $transaction['transactionType'];
                        $gl->transactionDate = $view->items['transactionDate'];
                        $gl->description = $transaction['description'];
                        $gl->year = $transaction['year'];
                        $gl->period = $transaction['period'];
                        $gl->propertyID = $transaction['propertyID'];
                        $gl->leaseID = $transaction['leaseID'];
                        $gl->companyID = $transaction['creditorID'];
                        $gl->source = GL_SOURCE_AP;

                        $gl->fromDate = $transaction['fromDate'];
                        $gl->toDate = $transaction['toDate'];
                        $gl->batchID = $invBatch; // $transaction['batchNumber'];
                        $gl->lineNumber = $invLine; // $transaction['lineNumber'];

                        $t = new Transaction($gl);	// -- detail entry : adopt the data from the

                        $_amount = bcmul($transaction['netAmount'], -1, 2);
                        $gl->update($transaction['accountID'], GL_BALANCE_CASH, $_amount);
                        $t->add($transaction['accountID'], BASIS_CASH, $_amount);


                        $_amount = bcmul($transaction['taxAmount'], -1, 2);

                        $gl->update(glAccount(GL_GST_INPUT), GL_BALANCE_CASH, $_amount);
                        $t->add(glAccount(GL_GST_INPUT), BASIS_CASH, $_amount);

                        $_amount = bcmul($transaction['transactionAmount'], 1, 2);

                        $gl->update(glAccount(GL_BANK), GL_BALANCE_CASH, $_amount);
                        $t->add(glAccount(GL_BANK), BASIS_CASH, $_amount);

                        $gl->update(glAccount(GL_BANK), GL_BALANCE_ACCRUALS, $_amount);
                        $t->add(glAccount(GL_BANK), BASIS_ACCRUALS, $_amount);

                        $_amount = bcmul($transaction['transactionAmount'], -1, 2);
                        $gl->update(glAccount(GL_CREDITORS_CONTROL), GL_BALANCE_ACCRUALS, $_amount);
                        $t->add(glAccount(GL_CREDITORS_CONTROL), BASIS_ACCRUALS, $_amount);
                    }

                    // remove account code for PAY when inserting to ap_transaction
                    $PAY_accountID = $transaction['accountID'];
                    $transaction['accountID'] = '';

                    $transaction['batchJournal'] = $batchJournal;
                    dbInsertPayableTransaction($transaction);

                    // change to original account after insert
                    $transaction['accountID'] = $PAY_accountID;

                    $transaction['taxAmount'] = (-1) * $transaction['taxAmount'];
                    $transaction['allocationDate'] = $view->items['transactionDate'];
                    $transaction['allocationNumber'] = dbGetNextAllocationNumber();
                    $transaction['fromBatchNumber'] = $fromBatchNumber;
                    $transaction['fromLineNumber'] = 1;
                    $transaction['toBatchNumber'] = $toBatchNumber;
                    $transaction['toLineNumber'] = 1;
                    $transaction['currentDate'] = TODAY;
                    $transaction['currentTime'] = TODAY;
                    $transaction['fromType'] = TYPE_PAYMENT;
                    $transaction['toType'] = TYPE_INVOICE;

                    dbInsertAllocation($transaction);

                    // -- if a transfer journal ID was passed in - it's a temp transfer journal - mark it as processed
                    if ($view->items['transferJournalID']) {
                        dbSetStatusTempTransferJournal($view->items['transferJournalID'], 1);
                    }

                    $view->items['createdBy'] = $_SESSION['un'];
                    dbAddTransferJournalHistory($view->items);

                    $view->items['statusMessage'] = 'Successfully transferred the amount';
                    $view->items['creditorID'] = null;
                } else {
                    // escape single qoute
                    $view->items['fromDescription'] = 	 str_replace("'", '`', $view->items['fromDescription']);
                    $view->items['toDescription'] = 	 str_replace("'", '`', $view->items['toDescription']);
                    // -- insert into temp
                    $view->items['createdBy'] = $_SESSION['un'];
                    $temporaryID = dbAddTempTransferJournal($view->items);

                    $files = $_FILES['files'] ?? [];
                    $uploadedFilesCredit = $transferJournalService->uploadAttachment($files);
                    $transaction = [];
                    $transaction['propertyID'] = $view->items['fromPropertyID'];
                    $transaction['batchNumber'] = null;
                    $transaction['creditorID'] = $view->items['creditorID'];
                    $transaction['attachAR'] = $view->items['attachARFile'];
                    $transaction['attachOwnerReportFile'] = $view->items['attachOwnerReportFile'];
                    $transaction['lineNumber'] = 1;
                    $transaction['transferJournalTemporaryID'] = $temporaryID;
                    $documentIds = [];
                    foreach ($uploadedFilesCredit as $row) {
                        $transaction['file_name'] = "{$row['file_name']}.{$row['extension']}";
                        $transaction['transactionType'] = TYPE_CREDIT;
                        $lastId = $transferJournalService->addAttachmentToDocumentTable($transaction);
                        $documentIds[] = $lastId;
                        $transaction['lastID'] = $lastId;
                        $transferJournalService->addAttachmentToDocumentInvoiceTable($transaction);
                    }

                    $transaction['propertyID'] = $view->items['toPropertyID'];
                    $transaction['transactionType'] = TYPE_INVOICE;

                    foreach ($documentIds as $row) {
                        $transaction['lastID'] = $row;
                        $transaction['transactionType'] = TYPE_INVOICE;
                        $transferJournalService->addAttachmentToDocumentInvoiceTable($transaction);
                    }

                    $view->items['statusMessage'] = 'Successfully submitted the transfer request';
                    $view->items['creditorID'] = null;
                }

                $view->items['procesed'] = 'true';
            }

            break;
        case 'deleteDocument':
            dbDeleteInvoiceAndCreditDocument($view->items['documentID']);
            $transferJournalService->deleteFile($view->items['filename']);
            $listOfAttachments = $transferJournalService->listOfAttachments((int) $view->items['transferJournalID']);
            ['attachedDocumentsList' => $attachedDocumentsList, 'attachToARFile' => $attachToARFile, 'attachToOwnerReportFile' => $attachToOwnerReportFile] = $listOfAttachments;
            break;
        case 'tax':
            break;

    }

    if ($view->items['procesed'] !== 'true') {
        if (isset($view->items['fromPropertyID']) && isset($view->items['fromAccountID'])) {
            $pmrf_fees_primary = dbGetPropertyManagementFees($view->items['fromPropertyID'], $view->items['transactionDate'], $view->items['fromAccountID']);
            if ($pmrf_fees_primary) {
                $view->items['statusMessage'] = 'Choosing this account will affect the management fee calculation for the month the transaction date falls within.';
            }
        }

        if (isset($view->items['toPropertyID']) && isset($view->items['toAccountID'])) {
            $pmrf_fees_secondary = dbGetPropertyManagementFees($view->items['toPropertyID'], $view->items['transactionDate'], $view->items['toAccountID']);
            if ($pmrf_fees_secondary) {
                $view->items['statusMessage'] = 'Choosing this account will affect the management fee calculation for the month the transaction date falls within.';
            }
        }
    }

    $view->items['propertyList'] = dbPropertyList();
    $view->items['creditorList'] = dbCompanyList(SUPPLIER, true, OWNER);
    $view->items['accountList'] = dbGetGroupedAccounts(EXPENDITURE);
    if (! isset($view->items['transactionDate'])) {
        $view->items['transactionDate'] = TODAY;
    }

    if (! isset($view->items['transactionAmount'])) {
        $view->items['transactionAmount'] = 0;
    }

    $view->items['attachARFile'] = $attachToARFile ?? $view->items['attachARFile'];
    $view->items['attachOwnerReportFile'] = $view->items['action'] !== 'changePrimary' ? ($attachToOwnerReportFile ?? $view->items['attachOwnerReportFile']) : $view->items['attachOwnerReportFile'];
    $view->items['attachedDocuments'] = $attachedDocumentsList;
    $view->items['validationErrors'] = $validationErrors;
    $view->render();
}

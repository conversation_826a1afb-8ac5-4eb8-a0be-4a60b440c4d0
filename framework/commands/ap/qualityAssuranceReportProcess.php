<?php

function qualityAssuranceReportProcess(&$context)
{
    global $pathPrefix, $clientDirectory;

    if (! isPostback()) {
        $view = new MasterPage(userViews(), '/ap/qualityAssuranceReportProcess.html');
        $view->setSection($context['module']);
    } else {
        $view = new UserControl(userViews(), '/ap/qualityAssuranceReportProcess.html');
    }

    $view->bindAttributesFrom($_REQUEST);

    // create the folder if it doesn't exists
    $zip_folder = "{$pathPrefix}{$clientDirectory}/ap_zip";
    if (! file_exists($zip_folder)) {
        mkdir($zip_folder, FILE_PERMISSION, true);
    }

    // DL ZIP
    if ($view->items['DLZip'] == 1) {

        if ($view->items['batch'] && $view->items['propertyID'] && $view->items['creditorID'] && $view->items['invRef']) {
            $batch = decodeParameter($view->items['batch']);
            $propertyID = decodeParameter($view->items['propertyID']);
            $creditorID = decodeParameter($view->items['creditorID']);
            $invRef = decodeParameter($view->items['invRef']);

            $attachFile = dbGetDocumentInvoiceByBatch($batch, $propertyID, $creditorID);
            if (is_array($attachFile)) {
                $zip = new ZipArchive();
                $filePath = "{$zip_folder}/" . $propertyID . '-' . $creditorID . '-' . $invRef . '_attachments.zip';
                $dlPath = "{$clientDirectory}/ap_zip/" . $propertyID . '-' . $creditorID . '-' . $invRef . '_attachments.zip';

                if (file_exists($filePath)) {
                    unlink($filePath);
                }

                if ($zip->open($filePath, ZipArchive::CREATE) !== true) {
                    exit("cannot open <{$filePath}>\n");
                }

                $c = 0;
                foreach ($attachFile as $file) {
                    $c++;
                    if (file_exists($pathPrefix . $file['fileName'])) {
                        $ext = pathinfo($pathPrefix . $file['fileName'], PATHINFO_EXTENSION);
                        $zip->addFile($pathPrefix . $file['fileName'], 'attachment_' . "{$c}.{$ext}");
                    }
                }

                $zip->close();
            }

            echo "<script> document.location.href='download.php?fileID=" . encodeParameter($dlPath) . "';</script>";
        }

        return false;
    }

    // ========================================================

    $view->items['propertyManager'] = deserializeParameters($view->items['propertyManager']);
    $view->items['property'] = deserializeParameters($view->items['property']);

    $data = getAPQA($view->items);

    if (empty($data) && $view->items['format'] != FILETYPE_SCREEN) {
        $context['excelFormatError'] = 'No results found for the given criteria';
    } else {

        // create the Zip ALL ===================================================
        $zip = new ZipArchive();
        $dlAllFilePath = "{$zip_folder}/" . $_SESSION['user_id'] . '_' . time() . '_attachments.zip';
        $dlAllPath = "{$clientDirectory}/ap_zip/" . $_SESSION['user_id'] . '_' . time() . '_attachments.zip';

        if (file_exists($dlAllFilePath)) {
            unlink($dlAllFilePath);
        }

        if ($zip->open($dlAllFilePath, ZipArchive::CREATE) !== true) {
            exit("cannot open <{$dlAllFilePath}>\n");
        }

        // pre_print_r($data);

        $c = 0;
        $view->items['attachmentCount'] = 0;
        foreach ($data as $key => $invoice) {

            $business_prefix = ($data[$key]['supplierABN'] && $data[$key]['supplierABN'] != '') ? $_SESSION['country_default']['business_prefix'] : '';
            $data[$key]['supplierABN'] = $business_prefix . $data[$key]['supplierABN'];
            $data[$key]['suppBSB'] = formatWithDelimiter($data[$key]['suppBSB']);

            if ($data[$key]['workOrderNumber'] == 0) {
                $data[$key]['workOrderNumber'] = '';
            }

            $data[$key]['attachAR'] = $invoice['attachAR'] == 1 ? 'Yes' : 'No';

            $data[$key]['attachOwnerR'] = $invoice['attachOwnerR'] == 1 ? 'Yes' : 'No';

            $supplierInvoice = dbGetSupplierInvoice($invoice['batch']);
            if ($supplierInvoice) {
                $data[$key]['supplierInvoice'] = $supplierInvoice['fileName'];
            }

            $attachFile = dbGetDocumentInvoiceByBatchAP($invoice['batch'], $invoice['propertyID'], $invoice['creditorID']);
            if ($attachFile) {
                $data[$key]['file'] = $attachFile;

                foreach ($attachFile as $file) {
                    if (file_exists($pathPrefix . $file['fileName'])) {
                        $c++;
                        $ext = pathinfo($pathPrefix . $file['fileName'], PATHINFO_EXTENSION);

                        $zipAttachmentName = $invoice['supplierID'] . '_' . $invoice['reference'] . '_' . $invoice['batch'] . ".{$ext}";

                        if (count($attachFile ?? []) > 1) { // add count to file names
                            $zipAttachmentName = $invoice['supplierID'] . '_' . $invoice['reference'] . '_' . $invoice['batch'] . "_{$c}.{$ext}";
                        }

                        $zip->addFile($pathPrefix . $file['fileName'], $zipAttachmentName);

                        $view->items['attachmentCount']++;
                    }
                }

            }
        }

        $zip->close();
        // ========================================================


        if ($view->items['format'] == FILETYPE_SCREEN) {

            $view->items['onScreenHeaders'] = [
                ['label' => 'Invoice Number', 'align' => 'center'],
                ['label' => 'Payment Reference', 'align' => 'center'],
                ['label' => 'Type', 'align' => 'center'],
                ['label' => 'Property Code', 'align' => 'center'],
                ['label' => 'Supplier Code', 'align' => 'center'],
                ['label' => 'Supplier Name', 'align' => 'center'],
                ['label' => 'Supplier ' . $_SESSION['country_default']['business_label'], 'align' => 'center'],
                ['label' => 'Invoice Date', 'align' => 'center'],
                ['label' => 'Due Date', 'align' => 'center'],
                ['label' => 'From Date', 'align' => 'center'],
                ['label' => 'To Date', 'align' => 'center'],
                ['label' => 'Account Code', 'align' => 'center'],
                ['label' => 'Description ', 'align' => 'center'],
                ['label' => 'Tax Amount', 'align' => 'center'],
                ['label' => 'Gross Amount', 'align' => 'center'],
                ['label' => 'Attach to AR Invoice', 'align' => 'center'],
                ['label' => 'Attach to Owner Reports', 'align' => 'center'],
            ];

            $view->items['data'] = $data;
            $view->items['dlAllPath'] = $dlAllPath;
        } elseif ($view->items['format'] == FILETYPE_XLS) {
            global $pathPrefix, $clientDirectory;
            $format = $view->items['format'];
            if (! file_exists("{$pathPrefix}{$clientDirectory}/{$format}")) {
                mkdir("{$pathPrefix}{$clientDirectory}/{$format}", FILE_PERMISSION, true);
            }

            $filename = "Quality Assurance Report.{$format}";
            $filePath = "{$pathPrefix}{$clientDirectory}/{$format}/Quality Assurance Report/{$filename}";
            $downloadLink = "{$clientDirectory}/{$format}/Quality Assurance Report/{$filename}";

            @unlink($filePath);

            $report = new XLSDataReport($filePath, 'Quality Assurance Report');
            $report->enableFormatting = true;
            $currency = $_SESSION['country_default']['currency_symbol'];
            $amountFormat = '_("' . $currency . '"* #,##0.00_);_("' . $currency . '"* \(#,##0.00\);_("' . $currency . '"* "-"??_);_(@_)';

            if ($format == FILETYPE_XLS) {
                $report->addColumn('reference', 'Invoice Number', 30, 'left', '@');
                $report->addColumn('payBatch', 'Payment Run Number', 30, 'left', '@');
                $report->addColumn('payReference', 'Payment Reference', 30, 'left', '@');
                $report->addColumn('type', 'Type', 30, 'left');
                $report->addColumn('propertyID', 'Property Code', 30, 'left', '@');
                $report->addColumn('propertyName', 'Property Name', 30, 'left', '@');
                $report->addColumn('supplierID', 'Supplier Code', 30, 'left', '@');
                $report->addColumn('supplierName', 'Supplier Name', 30, 'left', '@');
                $report->addColumn('supplierABN', $_SESSION['country_default']['business_label'], 30, 'left', '@');
                $report->addColumn('invoiceDate', 'Invoice Date', 30, 'left');
                $report->addColumn('dueDate', 'Due Date', 30, 'left');
                $report->addColumn('fromDate', 'From Date', 30, 'left');
                $report->addColumn('toDate', 'To Date', 30, 'left');
                $report->addColumn('accountCode', 'Account Code', 30, 'left', '@');
                $report->addColumn('description', 'Description', 30, 'left', '@');
                $report->addColumn('taxAmount', 'Tax Amount', 30, 'left', $amountFormat);
                $report->addColumn('grossAmount', 'Gross Amount', 30, 'left', $amountFormat);
                $report->addColumn('suppPayMethod', 'Supplier Payment Method', 30, 'left', '@');

                if (dbGetDefaultCountry() == 'AU') {
                    $report->addColumn('suppBPayBillerCode', 'Supplier BPAY Biller Code', 30, 'left', '@');
                }

                $report->addColumn('invCRN', 'Invoice CRN', 30, 'left', '@');
                $report->addColumn('suppBank', 'Supplier Bank', 30, 'left', '@');

                if (getDisplayBsbFromSession()) {
                    $report->addColumn(
                        'suppBSB',
                        'Supplier ' . getBsbLabelFromSession(),
                        30,
                        'left',
                        '@'
                    );
                }

                $report->addColumn('suppAccNo', 'Supplier Account Number', 30, 'left', '@');
                $report->addColumn('suppAccName', 'Supplier Account Name', 30, 'left', '@');
                $report->addColumn('suppEmail', 'Supplier Email', 30, 'left', '@');
                $report->addColumn('workOrderNumber', 'Work Order Number', 30, 'left', '@');
                $report->addColumn('invDateEntered', 'Invoice Date Entered', 30, 'left');
                $report->addColumn('invCreateUer', 'Invoice Create User (user name)', 30, 'left', '@');
                $report->addColumn('attachAR', 'Attach to AR Invoice', 30, 'left', '@');
                $report->addColumn('attachOwnerR', 'Attach to Owner Reports', 30, 'left', '@');

                $report->renderHeader();
                $report->printRowLines = true;
            }

            $report->renderData($data);
            //			pre_print_r($data);
            $report->close();

            $view->items['downloadLink'] = $view->items['attachmentCount'] > 0 ? [$downloadLink, $dlAllPath] : [$downloadLink];
        }
    }

    $view->render();

}

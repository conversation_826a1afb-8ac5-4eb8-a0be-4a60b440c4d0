<?php

function prepareRemittanceAdvice($bankID, $transactionDate, $referenceNumber, $creditorID, $payments = null, $paymentType = '', $extra_name = null)
{
    global $clientDirectory, $pathPrefix;

    // -- initialise the variables that will store the transaction amounts

    // -- if there are transactions to be printed:
    if ($payments) {
        [$day, $month, $year] = explode('/', $transactionDate); // -- will need to store current DB index in session to allow for translation of logos
        $logoFile = dbGetClientLogo();
        $logoPath = "assets/clientLogos/{$logoFile}";
        // $logoPath = null;
        $time = time() . $extra_name;

        $filename = "remittance_advice_{$bankID}_{$creditorID}_{$year}{$month}{$day}_{$time}.pdf";
        $filePath = "{$pathPrefix}{$clientDirectory}/pdf/RemittanceAdvice/{$filename}";
        $downloadPath = "{$clientDirectory}/pdf/RemittanceAdvice/{$filename}";

        if (file_exists($filePath)) {
            @unlink($filePath);
        }

        $remittanceAdvice = new RemittanceAdvice($filePath, $logoPath);

        // -- some data is shared across several components in the PDF, so instead of doing multiple db calls and binding locally within the object,
        // -- a single DB call is made and data bound to the object externally

        // -- PDFobjects are usually groups of data repeated statically on every page of the PDF, and as such are loaded once into the PDF and called at render...
        // -- mutating values are accessed from the Invoice object itself such as changing totals, invoice rows, sub titles within the statement

        // -- note that data is bound between array values to the object attributes sharing the same name as the array key - ie
        // -- $this->test = $array['test']

        $header = new RemittanceHeader($referenceNumber, $transactionDate);
        $header->paymentType = $paymentType;
        $remittanceAdvice->attachObject('header', $header);


        $footer = new RemittanceFooter($creditorID);
        $footer->paymentType = $paymentType;
        $remittanceAdvice->attachObject('footer', $footer);

        $mailAddress = new MailAddressWindow($creditorID);
        $remittanceAdvice->attachObject('mailAddress', $mailAddress);

        $lines = new RemittanceLines();
        $remittanceAdvice->attachObject('lines', $lines);
        $remittanceAdvice->attachObject('traccFooter', new TraccFooter('assets/clientLogos/tracc_logo.jpg', 'Remittance_Advice', A4_PORTRAIT));

        $remittanceAdvice->preparePage();

        $cashTotal = 0;
        $chequeTotal = 0;
        $cashCount = 0;
        $chequeCount = 0;

        if (! isEmptyArray($payments)) {
            $total = 0;
            $count = 0;
            $isAgent = false;
            $transactions = $payments;


            $groups = [];
            foreach ($payments as $item) {
                $key = $item['accountCode'];

                if ($item['companyType'] == 'AGENT') {
                    $isAgent = true;
                }

                if (! array_key_exists($key, $groups)) {
                    $groups[$key] = [
                        'transactionDate'       => '',
                        'invoiceNumber'         => '',
                        'propertyName'          => 'Various',
                        'description'           => $item['accountCode'] . ' - ' . $item['accountDescription'],
                        'transactionAmount'     => $item['transactionAmount'],
                    ];
                } else {
                    $groups[$key]['transactionAmount'] += $item['transactionAmount'];
                }
            }

            if ($isAgent && dbGetParam('TEMPLATE', 'AGENTREMIT')) {
                $transactions = $groups;
            }

            foreach ($transactions as $payment) {
                $total = bcadd($total, $payment['transactionAmount'], 2);
                $remittanceAdvice->renderRow($payment['transactionDate'], $payment['invoiceNumber'], $payment['propertyName'], $payment['description'], $payment['transactionAmount']);
            }

            $remittanceAdvice->renderTotal($total);
        }

        $remittanceAdvice->close();
        unset($remittanceAdvice);
        $paths['filePath'] = $filePath;
        $paths['downloadPath'] = $downloadPath;
        $paths['total'] = $total;

        return $paths;
    }



}

function prepareRemittanceAdviceForAll($batchID, $paymentForBatch, $batchDetails, $paymentType, $withEmail = true)
{
    global $clientDirectory, $pathPrefix;

    $bankID = $batchDetails['bankID'];
    $transactionDate = $batchDetails['batchDate'];

    [$day, $month, $year] = explode('/', $transactionDate);
    $logoFile = dbGetClientLogo();
    $logoPath = "assets/clientLogos/{$logoFile}";
    $time = time();
    $filename = "remittance_advice_{$bankID}_{$year}{$month}{$day}_{$time}.pdf";
    $filePath = "{$pathPrefix}{$clientDirectory}/pdf/RemittanceAdvice/{$filename}";
    $downloadPath = "{$clientDirectory}/pdf/RemittanceAdvice/{$filename}";
    if (file_exists($filePath)) {
        unlink($filePath);
    }

    $remittanceAdvice = new RemittanceAdvice($filePath, $logoPath);
    foreach ($paymentForBatch as $payments) {
        $creditorID = $payments['creditorID'];
        $company = dbGetCompany($creditorID);
        $transactions = dbGetInvoicesForBatch($batchID, $payments['lineNumber']);

        foreach ($transactions as &$t) {
            $referenceNumber = $t['referenceNumber'];
            $payee = dbGetPayee($t['creditorID']);
            $t['payee'] = $payee;
        }

        if ($transactions && (empty($company['email']) && $withEmail == false || $withEmail == true)) {
            $header = new RemittanceHeader($referenceNumber, $transactionDate);
            $header->paymentType = $paymentType;
            $remittanceAdvice->attachObject('header', $header);

            $footer = new RemittanceFooter($creditorID);
            $footer->paymentType = $paymentType;
            $footer->crn = $payments['customerReference'];

            $remittanceAdvice->attachObject('footer', $footer);

            $mailAddress = new MailAddressWindow($creditorID);
            $remittanceAdvice->attachObject('mailAddress', $mailAddress);

            $lines = new RemittanceLines();
            $remittanceAdvice->attachObject('lines', $lines);
            $remittanceAdvice->attachObject('traccFooter', new TraccFooter('assets/clientLogos/tracc_logo.jpg', 'Remittance_Advice', A4_PORTRAIT));

            $remittanceAdvice->preparePage();
            $cashTotal = 0;
            $chequeTotal = 0;
            $cashCount = 0;
            $chequeCount = 0;
            if ($transactions) {
                $total = 0;
                $count = 0;
                $isAgent = false;
                $_transactions = $transactions;
                $groups = [];
                foreach ($transactions as $item) {
                    $key = $item['accountCode'];

                    if ($item['companyType'] == 'AGENT') {
                        $isAgent = true;
                    }

                    if (! array_key_exists($key, $groups)) {
                        $groups[$key] = [
                            'transactionDate'       => '',
                            'invoiceNumber'         => '',
                            'propertyName'          => 'Various',
                            'description'           => $item['accountCode'] . ' - ' . $item['accountDescription'],
                            'transactionAmount'     => $item['transactionAmount'],
                        ];
                    } else {
                        $groups[$key]['transactionAmount'] += $item['transactionAmount'];
                    }
                }

                if ($isAgent && dbGetParam('TEMPLATE', 'AGENTREMIT')) {
                    $_transactions = $groups;
                }

                foreach ($_transactions as $payment) {
                    $total = bcadd($total, $payment['transactionAmount'], 2);
                    $remittanceAdvice->renderRow($payment['transactionDate'], $payment['invoiceNumber'], $payment['propertyName'], $payment['description'], $payment['transactionAmount']);
                }

                $remittanceAdvice->renderTotal($total);
            }

            $remittanceAdvice->endPage();
            $success++;
        }
    }

    if ($success) {
        $remittanceAdvice->close();
        unset($remittanceAdvice);
        $paths['filePath'] = $filePath;
        $paths['downloadPath'] = $downloadPath;

        return $paths;
    } else {
        return false;
    }
}

/*


/*************************************************************************************************************************/

// INVOICE OBJECTS - repeating groups that appear in each invoice


class RemittanceFooter extends PDFobject
{
    public $accountName;

    public $accountNumber;

    public $bsb;

    public $bank;

    public $paymentType; // 2012-10-03: Added to include BPay option [Morph]

    public $crn; // 2012-10-03: Added to include BPay option [Morph]

    public $billerCode; // 2012-10-03: Added to include BPay option [Morph]

    public function __construct($companyID)
    {
        $c = dbGetCompany($companyID);
        $this->accountName = $c['bankAccountName'];
        $this->accountNumber = $c['bankAccountNumber'];
        $this->bsb = $c['bsbNumber'];
        $this->bank = $c['bankName'];
        $this->billerCode = $c['bpayBillerCode'];
        if (empty($this->paymentType)) {
            $this->paymentType = PAY_EFT;
        }
    }

    public function preRender(&$pdf)
    {
        $pdf->setColorExt('both', 'rgb', 0.9, 0.9, 0.9, 0); // Setting the Color to Gray
        $pdf->rect(30, 145, 260, 15);
        $pdf->rect(300, 145, 270, 15);
        $pdf->fill();

        $pdf->setlinewidth(2);
        $pdf->setColorExt('both', 'rgb', 0.75, 0.75, 0.75, 0);
        $pdf->rect(30, 40, 260, 120);
        $pdf->rect(300, 40, 270, 120);
        $pdf->stroke();

        $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        $this->setFont($pdf, 'Helvetica-Bold', 8);

        $pdf->showBoxed('PAYMENT ACCOUNT DETAILS', 40, 144, 150, 13, 'left', '');
        $pdf->showBoxed('CONFIDENTIALITY', 310, 144, 150, 13, 'left', '');


        if ($this->paymentType == PAY_BPAY) {
            // $pdf->showBoxed ('CRN', 40, 97, 80, 19, 'left', '');
            $pdf->showBoxed('Biller Code', 40, 82, 80, 19, 'left', '');
        } else {
            $pdf->showBoxed('Account Name', 40, 97, 80, 19, 'left', '');
            $pdf->showBoxed('Account Number', 40, 82, 80, 19, 'left', '');
            if (getDisplayBsbFromSession()) {
                $pdf->showBoxed(getBsbLabelFromSession(), 40, 67, 80, 19, 'left', '');
                if ($this->bank) {
                    $pdf->showBoxed('Bank', 40, 52, 80, 19, 'left', '');
                }
            } elseif ($this->bank) {
                $pdf->showBoxed('Bank', 40, 67, 80, 19, 'left', '');
            }
        }


        $this->setFont($pdf, 'Helvetica', 8);

        $pdf->showBoxed("The information contained in this document is confidential and is intended for the exclusive use of the addressee named above. In certain cases, it is also legally privileged.\n\nIf you are not the addressee, any disclosure, reproduction, distribution, or any other dissemination or use of this communication is strictly prohibited. If you have received this transmission in error please contact us immediately so that we can arrange for its return.", 315, 55, 252, 72, 'left', ''); // Disclaimer

    }

    public function render(&$pdf)
    {
        $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        $this->setFont($pdf, 'Helvetica', 8); // This is for the disclaimer information...
        if ($this->paymentType == PAY_BPAY) {
            // $pdf->showBoxed ($this->crn, 119, 97, 172, 19, 'left', '');
            $pdf->showBoxed($this->billerCode, 119, 82, 172, 19, 'left', '');
        } else {
            $pdf->showBoxed($this->accountName, 119, 97, 172, 19, 'left', '');
            $pdf->showBoxed('*****' . substr($this->accountNumber, -4, 4), 119, 82, 172, 19, 'left', '');
            if (getDisplayBsbFromSession()) {
                $pdf->showBoxed(
                    formatWithDelimiter($this->bsb),
                    119,
                    67,
                    172,
                    19,
                    'left',
                    ''
                );
                $pdf->showBoxed($this->bank, 119, 52, 172, 19, 'left', '');
            } else {
                $pdf->showBoxed($this->bank, 119, 67, 172, 19, 'left', '');
            }
        }
    }
}



class MailAddressWindow extends PDFobject
{
    public $attention;

    public $name;

    public $street;

    public $city;

    public $state;

    public $postcode;

    public function __construct($companyID)
    {
        $mailAddress = dbGetCompany($companyID);
        $this->name = $mailAddress['companyName'];
        $this->street = $mailAddress['address'];
        $this->city = $mailAddress['city'];
        $this->state = $mailAddress['state'];
        $this->postcode = $mailAddress['postCode'];
    }

    public function preRender(&$pdf) {}

    public function render(&$pdf)
    {
        $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        $this->setFont($pdf, 'Helvetica', 10); // This is for the disclaimer information...
        $address = (($this->attention) ? "{$this->attention}\n" : '') . "{$this->name}\n{$this->street}\n{$this->city} {$this->state},  {$this->postcode}";
        $pdf->showBoxed($address, 95, 622, 232, 46, 'left', ''); // This is for the from field
    }
}



class RemittanceHeader extends PDFobject
{
    public $address1;

    public $address2;

    public $companyName;

    public $address;

    public $phone;

    public $fax;

    public $reference;

    public $date;

    public $paymentType; // 2012-10-03: Added to include BPay option [Morph]

    public $_name = 'remittanceHeader';

    public function __construct($reference, $date)
    {
        $company = dbGetPrimaryAgent();
        $this->companyName = $company['companyName'];
        $this->address1 = $company['address'];
        $this->address2 = $company['city'] . ' ' . $company['state'] . ' ' . $company['postCode'];
        $this->phone = $company['phone'];
        $this->fax = $company['fax'];
        $this->date = $date;
        $this->reference = $reference;
        if (empty($this->paymentType)) {
            $this->paymentType = PAY_EFT;
        }
    }

    public function preRender(&$pdf)
    {

        $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        $this->setFont($pdf, 'Helvetica', 14);
        if ($this->paymentType == PAY_BPAY) {
            $pdf->showBoxed('BPAY REMITTANCE ADVICE', 20, 785, 250, 25, 'left', '');
        } else {
            $pdf->showBoxed('EFT REMITTANCE ADVICE', 20, 785, 250, 25, 'left', '');
        }


        // -- main box
        $pdf->setColorExt('both', 'rgb', 0.9, 0.9, 0.9, 0); // Setting the Color to Gray
        $pdf->rect(320, 722, 250, 15);
        $pdf->fill();
        $pdf->setlinewidth(0.5);

        $pdf->setColorExt('both', 'rgb', 0.75, 0.75, 0.75, 0);
        $pdf->moveto(320, 638);
        $pdf->lineto(570, 638);
        $pdf->stroke();




        $pdf->setlinewidth(2);
        $pdf->setColorExt('both', 'rgb', 0.75, 0.75, 0.75, 0);
        $pdf->rect(320, 637, 250, 100);
        $pdf->rect(320, 612, 250, 20);
        $pdf->rect(320, 587, 250, 20);
        $pdf->stroke();

        $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        $this->setFont($pdf, 'Helvetica-Bold', 8);



        $pdf->showBoxed('From', 335, 720, 50, 13, 'left', '');
        $pdf->showBoxed('Reference Number', 335, 615, 100, 13, 'left', '');
        $pdf->showBoxed('Payment Date', 335, 590, 100, 13, 'left', '');
    }

    public function render(&$pdf)
    {

        $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        $this->setFont($pdf, 'Helvetica-Bold', 8);

        $pdf->showBoxed($this->companyName, 335, 702, 215, 13, 'left', '');
        $pdf->showBoxed($this->address1, 335, 692, 215, 13, 'left', '');
        $pdf->showBoxed($this->address2, 335, 682, 215, 13, 'left', '');
        if ($this->phone) {
            $pdf->showBoxed('P: ' . $this->phone, 335, 662, 215, 13, 'left', '');
        }

        if ($this->fax) {
            $pdf->showBoxed('F: ' . $this->fax, 335, 652, 215, 13, 'left', '');
        }


        $pdf->showBoxed($this->reference, 460, 615, 100, 13, 'right', '');
        $pdf->showBoxed($this->date, 460, 590, 100, 13, 'right', '');

    }
}






class RemittanceLines extends PDFobject
{
    public $title;

    public function __construct() {}

    public function preRender(&$pdf)
    {

        $pdf->setColorExt('both', 'rgb', 0.9, 0.9, 0.9, 0); // Setting the Color to Gray
        $pdf->rect(25, 546, 545, 15);
        $pdf->fill();
        $pdf->rect(25, 285, 410, 15);
        $pdf->fill();
        $pdf->setlinewidth(0.5);

        $pdf->setColorExt('both', 'rgb', 0.75, 0.75, 0.75, 0);
        $pdf->moveto(25, 546);
        $pdf->lineto(570, 546);
        $pdf->stroke();
        $pdf->moveto(25, 300);
        $pdf->lineto(570, 300);
        $pdf->stroke();


        $pdf->setlinewidth(2);
        $pdf->rect(25, 285, 545, 276);
        $pdf->stroke();
        $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        // Text

        $this->setFont($pdf, 'Helvetica-Bold', 9);
        $pdf->showBoxed('  DATE', 25, 546, 45, 13, 'center', '');
        $pdf->showBoxed('INVOICE NO.', 82, 546, 50, 13, 'right', '');
        $pdf->showBoxed('PROPERTY', 165, 546, 290, 13, 'left', '');
        $pdf->showBoxed('DESCRIPTION', 315, 546, 100, 13, 'left', '');
        $pdf->showBoxed('AMOUNT', 520, 546, 45, 13, 'right', '');

        $this->setFont($pdf, 'Helvetica', 8);
    }

    public function render(&$pdf) {}
}










/*************************************************************************************************************************/

class RemittanceAdvice extends PDFReport
{
    public $pdf;

    public $lineOffset = 15;

    public $logoFile = false;

    public function __construct(&$dataSource, $logoFile = false)
    {
        parent::__construct($dataSource, 595, 842);
        if ($logoFile) {
            $this->logoFile = $logoFile;
        }
    }

    public function updateLineOffset($offset)
    {

        $this->lineOffset += $offset;
        if ($this->lineOffset >= 240) {
            // $this->renderTotal('','','continued...');
            $this->endPage();
            $this->preparePage();
            $this->render();
        }
    }

    public function renderRow($date, $invoiceNumber, $payee, $description, $amount)
    {

        $this->pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        $this->setFont('Helvetica-Bold', 8);

        $this->pdf->showBoxed($date, 25, 540 - $this->lineOffset, 50, 13, 'right', '');
        $this->pdf->showBoxed($invoiceNumber, 70, 540 - $this->lineOffset, 85, 13, 'right', '');
        $this->pdf->showBoxed($payee, 165, 540 - $this->lineOffset, 150, 13, 'left', '');
        $this->pdf->showBoxed($description, 315, 540 - $this->lineOffset, 200, 13, 'left', '');
        $this->pdf->showBoxed($amount != '' ? toMoney($amount) : '', 470, 540 - $this->lineOffset, 90, 13, 'right', '');

        $this->updateLineOffset(10);
    }

    public function renderTotal($amount)
    {
        $this->pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        $this->setFont('Helvetica-Bold', 8);
        $this->pdf->showBoxed('Please allow 3 working days for this payment to be processed', 35, 278, 243, 20, 'left', ''); // Total
        $this->pdf->showBoxed('TOTAL', 330, 285, 100, 13, 'right', ''); // Total
        if (is_numeric($amount)) {
            $this->pdf->showBoxed(toMoney($amount), 25, 285, 540, 13, 'right', '');
        } else {
            $this->pdf->showBoxed($amount, 25, 285, 540, 13, 'right', '');
        }
    }

    public function renderLogo()
    {
        if ($this->logoFile) {

            $maxWidth = LOGO_WIDTH;
            $maxHeight = LOGO_HEIGHTV2;
            [$imageWidth, $imageHeight] = getimagesize($this->logoFile);

            $horizontalScaling = ($imageWidth > $maxWidth) ? $maxWidth / $imageWidth : 1;
            $verticalScaling = ($imageHeight > $maxHeight) ? $maxHeight / $imageHeight : 1;
            $imageScale = ($horizontalScaling < $verticalScaling) ? $horizontalScaling : $verticalScaling;

            $imageScale = round($imageScale, 2);

            $vMargin = 25;
            $hMargin = 25;

            $hPos = 595 - ($imageScale * $imageWidth) - $hMargin;
            $vPos = 842 - ($imageScale * $imageHeight) - $vMargin;

            $pdfimage = $this->pdf->load_image('auto', $this->logoFile, '');
            $this->pdf->fit_image($pdfimage, $hPos, $vPos, "scale {$imageScale}");
            $this->pdf->close_image($pdfimage);
        }
    }

    public function preparePage($printTemplate = true)
    {
        $this->lineOffset = 15;
        parent::preparePage();
        $this->render();
        $this->renderLogo();
    }

    public function close()
    {
        parent::close();
    }
}

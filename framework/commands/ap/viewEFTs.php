<?php

include_once SYSTEMPATH . '/lib/enums/BankInstitutionCode.php';
include_once SYSTEMPATH . '/lib/enums/BankFileType.php';
include_once SYSTEMPATH . '/lib/enums/CountryCode.php';
include_once SYSTEMPATH . '/lib/enums/EnumsHelper.php';

use enums\BankFileType;
use enums\BankInstitutionCode;
use enums\CountryCode;
use enums\EnumsHelper;

function buildBSB($bsb)
{
    $start = substr($bsb, EnumsHelper::OFFSET_0, EnumsHelper::LENGTH_3);
    $end = substr($bsb, EnumsHelper::OFFSET_NEGATIVE_3, EnumsHelper::LENGTH_3);

    return ($_SESSION['country_code'] == CountryCode::GB) ? $bsb : "{$start}-{$end}";
}

function buildCBABSB($bsb)
{
    $start = substr($bsb, EnumsHelper::OFFSET_0, EnumsHelper::LENGTH_3);
    $end = substr($bsb, EnumsHelper::OFFSET_NEGATIVE_3, EnumsHelper::LENGTH_3);

    return "{$start}{$end}";
}

function buildIdentifier($referenceNumber)
{
    return substr($referenceNumber, EnumsHelper::OFFSET_NEGATIVE_3, EnumsHelper::LENGTH_3);
}

function evolveAPI($batchNumber, $transactionDate, $paymentReference)
{
    /**  EVOLVE API **/
    // GET OCR DETAILS AND AMOUNT
    $eMessage = '';
    $withError = '';
    $ocrDtls = dbGetTempOCRDetails($batchNumber);
    if ($ocrDtls && count($ocrDtls ?? []) > 0) {
        $ocr_log_message = '';
        $paymentDetailLog = [];
        $response_linesLog = [];
        foreach ($ocrDtls as $ocrRaw) {
            if ($ocrRaw['ap_batch_nr'] && $ocrRaw['GUID'] != null) {
                $paymentDate = str_replace('/', '-', $transactionDate);
                // EVOLVE API
                $paymentDetail['invoiceID'] = $ocrRaw['GUID'];
                $paymentDetail['invoiceNumber'] = $ocrRaw['invoice_number'];
                $paymentDetail['vendorReference'] = '';
                $paymentDetail['paidDate'] = date('Y-m-d', strtotime($paymentDate)) . 'T00:00:00';
                $paymentDetail['receiptNumber'] = $paymentReference;
                $paymentDetail['journalNumber'] = '';

                $paymentDetailLog[] = $paymentDetail;
                $payment = postPayment($paymentDetail);
                $response_linesLog[$ocrRaw['GUID']] = json_decode($payment);
                $eResponse = json_decode($payment, true);

                if (is_array($eResponse) && array_key_exists('message', $eResponse)) {
                    $withError = ($withError != '' ? $withError . ', ' . $ocrRaw['GUID'] : $ocrRaw['GUID']);
                } else {
                    $eMessage = $eResponse;
                }
            }
        }

        if ($response_linesLog !== []) {
            // START OF EVOLVE LOGS
            $eLogFile = fopen('EVOLVEAPI_logs.txt', 'a') or exit('Unable to open file!');
            fwrite($eLogFile, "\n" . '=============================================================');
            fwrite($eLogFile, "\n" . 'EVOLVE API POST PAYMENT Log at: ' . date('Y-m-d H:i:s'));
            fwrite($eLogFile, "\n" . 'Batch Number : ' . $batchNumber . $ocr_log_message);
            if ($paymentDetailLog !== []) {
                fwrite($eLogFile, "\n" . '------------------------------------------------');
                fwrite($eLogFile, "\n" . 'Array Data');
                fwrite($eLogFile, "\n" . json_encode($paymentDetailLog));
                fwrite($eLogFile, "\n" . '------------------------------------------------');
            }

            fwrite($eLogFile, "\n");
            if ($response_linesLog !== []) {
                fwrite($eLogFile, "\n" . '------------------------------------------------');
                fwrite($eLogFile, "\n" . 'Evolve API Response');
                fwrite($eLogFile, "\n" . json_encode($response_linesLog));
                fwrite($eLogFile, "\n" . '------------------------------------------------');
            }

            //			fwrite($eLogFile, "\n" . json_encode($eMessage));
            fwrite($eLogFile, "\n" . '=============================================================');
            fclose($eLogFile);
            // END OF EVOLVE LOGS
        }
    }

    if ($withError) {
        $errorMsg = 'Request is invalid for the following GUID (' . $withError . ')';
        $eMessage = ($eMessage) ? $eMessage . '. ' . $errorMsg : $errorMsg;
    }

    return $eMessage;
}

function postPayment($Data)
{
    $access = json_decode(getEvolveToken(), true);
    $curl = curl_init();
    $paidData = json_encode([$Data]);

    curl_setopt_array($curl, [
        CURLOPT_URL => EVOLVE_MARK_AS_PAID,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 0,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'POST',
        CURLOPT_POSTFIELDS => $paidData,
        CURLOPT_HTTPHEADER => [
            'accept: application/json',
            'Content-Type: application/json',
            'Authorization: Bearer ' . $access['access_token'],
        ],
    ]);

    $response = curl_exec($curl);
    curl_close($curl);

    return $response;
}

function getEvolveToken()
{
    global $clientDB;
    $curl = curl_init();

    $evolveUser = EVOLVE_USER;
    $evolvePass = EVOLVE_PASS;
    $username = $evolveUser[$clientDB];
    $password = $evolvePass[$clientDB];

    $data = [
        'grant_type' => EVOLVE_GRANT_TYPE,
        'username' => $username,
        'password' => $password,
    ];

    curl_setopt_array($curl, [
        CURLOPT_URL => EVOLVE_TOKEN_URL,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 0,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'POST',
        CURLOPT_POSTFIELDS => http_build_query($data),
        CURLOPT_HTTPHEADER => [
            'Content-Type: application/x-www-form-urlencoded',
        ],
    ]);

    $response = curl_exec($curl);
    curl_close($curl);

    return $response;
}

function viewEFTs(&$context)
{
    global $monthIndex, $pathPrefix, $clientDirectory;

    if (! isPostback()) {
        $view = new MasterPage(userViews(), '/ap/viewEFTs.html');
        $view->setSection($context['module']);
    } else {
        $view = new UserControl(userViews(), '/ap/viewEFTs.html');
    }

    $view->bindAttributesFrom($_REQUEST);
    $view->items['statusList'] = [1 => 'Ungenerated payments', 2 => 'Generated payments', 3 => 'All payments'];

    $view->items['paymentTypes'][PAY_EFT] = 'Direct Deposit (EFT)';
    if (cdf_isAU()) {
        $view->items['paymentTypes'][PAY_BPAY] = 'BPAY';
    }

    // 2013-01-16: Should be replaced with the function monthYear() [Morph]
    $year = date('Y');
    $original_year = $year;
    $month = date('m');
    if ($month >= 10) {
        $year++;
    }

    for ($y = 0; $y < 10; $y++) {
        $yr = $year - $y;
        for ($m = 12; $m > 0; $m--) {
            $date = "{$monthIndex[$m]}/{$yr}";
            $dateList[$date] = $date;
        }
    }

    if (! isset($view->items['paymentType'])) {
        $view->items['paymentType'] = PAY_EFT;
    }

    switch ($view->items['paymentType']) {
        case PAY_EFT:
            $generated = 'X';
            $ungenerated = 'E';
            break;
        case PAY_BPAY:
            $generated = 'Y';
            $ungenerated = 'B';
            break;
    }

    switch ($view->items['status']) {
        case 1:
            $types = [$ungenerated];
            break;
        case 2:
            $types = [$generated];
            break;
        default:
            $types = [$generated, $ungenerated];
            break;
    }

    $view->items['dateList'] = $dateList;

    if (! isset($view->items['date'])) {
        $view->items['date'] = $monthIndex[intval($month)] . '/' . $original_year;
    }

    if (! isset($view->items['status'])) {
        $view->items['status'] = 2;
    }

    if ($view->items['date']) {
        [$month, $year] = explode('/', $view->items['date']);
        $fromDate = "01/{$month}/{$year}";
        $toDate = daysInMonth($month, $year) . "/{$month}/{$year}";

        $payments = dbGetPaymentsByType($types, $fromDate, $toDate);

        $acceptedBanks = ($_SESSION['country_code'] == CountryCode::GB) ? [BankInstitutionCode::COU] : [
            BankInstitutionCode::MBL,
            BankInstitutionCode::NAB,
            BankInstitutionCode::CBA,
            BankInstitutionCode::ANZ,
            BankInstitutionCode::WBC,
            BankInstitutionCode::BWA,
            BankInstitutionCode::WB2,
            BankInstitutionCode::BBL,
            BankInstitutionCode::WB3,
            BankInstitutionCode::WB4,
            BankInstitutionCode::ASB,
        ];

        $list = [];
        if ($payments) {
            foreach ($payments as $p) {
                $p['amount'] = $p['amount'] ?: 0;
                $p['offset'] = $p['offset'] ?: 0;
                $cancelled = (($p['offset'] != 0) && ($p['amount'] + $p['offset'] == 0));
                $p['generated'] = ($p['referenceNumber'] != '');
                $batchNumber = $p['batchNumber'];
                if ($batchNumber) {
                    $paymentBatch = dbGetPaymentBatchDetails($p['batchNumber']);
                    $p['nextReferenceNumber'] = dbNextPaymentReference($paymentBatch['bank']);
                    $p['currentReferenceNumber'] = $p['nextReferenceNumber'] - 1;

                    $cancel_amount = dbGetCancelSinglePayment(
                        $p['referenceNumber'],
                        $paymentBatch['bank'],
                        $p['referenceNumber'],
                        $p['batchNumber']
                    );
                    $p['amount'] += $cancel_amount['cancel_amount'] ?: 0;
                }

                if (! $cancelled) {
                    switch ($view->items['status']) {
                        case 1:
                        case 2:
                        case 3:
                            $list[$batchNumber] = $p;
                            break;
                    }

                    $generatedList[$batchNumber] = $p;
                }
            }
        }
    }

    $evolveAPI = dbCheckEvolveAPICredential($_SESSION['clientID']);
    if ($view->items['action'] == 'generate' || $view->items['action'] == 'regenerate') {
        // -- get the details for the batch (ap batch)
        $batchNumber = $view->items['batchID'];
        $batch = dbGetPaymentBatchDetails($batchNumber);

        if ($batch) {
            $view->items['bankID'] = $batch['bank'];
        }

        if (strlen(
            $batch['bsbNumber']
        ) != getBsbLengthFromSession() && getDisplayBsbFromSession()) {
            $validationErrors[] = 'EFT cannot be generated. ' . getBsbLabelFromSession(
            ) . ' number must be ' . getBsbLengthFromSession() . ' digits.';
        }

        if ($view->items['customReferenceNumber'] && ! isValid(
            $view->items['customReferenceNumber'],
            TEXT_ALPHANUMERIC,
            false
        )) {
            $validationErrors[] = 'You have not entered a valid Payment Reference Number';
        } elseif ($view->items['action'] == 'regenerate' && $view->items['customReferenceNumber'] && dbGetEFT(
            $batch['bank'],
            $view->items['customReferenceNumber'],
            1,
            $view->items['batchReferenceNumber']
        )) {
            $validationErrors[] = 'The reference number you use already exist. Please choose another.';
            $customReferenceNumber = null;
        } elseif ($view->items['action'] != 'regenerate' && $view->items['customReferenceNumber'] && dbGetEFT(
            $batch['bank'],
            $view->items['customReferenceNumber']
        )) {
            $validationErrors[] = 'The reference number you use already exist. Please choose another.';
            $customReferenceNumber = null;
        } elseif ($view->items['customReferenceNumber']) {
            $customReferenceNumber = $view->items['customReferenceNumber'];
        } else {
            $customReferenceNumber = null;
        }

        if (($view->items['paymentType'] == PAY_BPAY) && ((isset($view->items['existing'])) && ($view->items['existing'])) && ((! in_array(
            $batch['institution'],
            $acceptedBanks
        )))) {
            $validationErrors[] = 'A BPAY batch file is not available for ' . $batch['institution'] . '. Please process the BPAYs individually yourself using your internet banking program.';
        }

        if (noErrors($validationErrors)) {
            if ($view->items['action'] == 'regenerate') {
                dbDeleteBankRecForEFT($view->items['batchReferenceNumber'], $view->items['batchID']);
            }

            unset($list[$batchNumber]);
            if ($_SESSION['country_code'] === CountryCode::GB) {
                switch ($batch['institution']) {
                    case BankInstitutionCode::COU:
                        $fileFormat = BankFileType::CSV_FILE_TYPE;
                        break;
                    default:
                        $fileFormat = BankFileType::ABA_FILE_TYPE;
                        break;
                }
            } else {
                switch ($batch['institution']) {
                    case BankInstitutionCode::CBA:
                        $fileFormat = BankFileType::CSV_FILE_TYPE;
                        break;
                    case BankInstitutionCode::ASB:
                        $fileFormat = BankFileType::MT9_FILE_TYPE;
                        break;
                    case BankInstitutionCode::NAB:
                        if ($view->items['paymentType'] == PAY_BPAY) {
                            $fileFormat = BankFileType::BPB_FILE_TYPE;
                        } elseif ($view->items['paymentType'] == PAY_EFT) {
                            $fileFormat = BankFileType::ABA_FILE_TYPE;
                        }

                        break;
                    case BankInstitutionCode::ANZ:
                        if ($view->items['paymentType'] == PAY_BPAY) {
                            $fileFormat = BankFileType::BEM_FILE_TYPE;
                        } elseif ($view->items['paymentType'] == PAY_EFT) {
                            $fileFormat = BankFileType::ABA_FILE_TYPE;
                        }

                        break;
                    case BankInstitutionCode::BWA:
                    case BankInstitutionCode::WB3:
                    case BankInstitutionCode::WB4:
                        if ($view->items['paymentType'] == PAY_BPAY) {
                            $fileFormat = BankFileType::CSV_FILE_TYPE;
                        } elseif ($view->items['paymentType'] == PAY_EFT) {
                            $fileFormat = BankFileType::ABA_FILE_TYPE;
                        }

                        break;
                    case BankInstitutionCode::WB2:
                    case BankInstitutionCode::MBL:
                        if ($view->items['paymentType'] == PAY_BPAY) {
                            $fileFormat = BankFileType::BPAY_FILE_TYPE;
                        } elseif ($view->items['paymentType'] == PAY_EFT) {
                            $fileFormat = BankFileType::ABA_FILE_TYPE;
                        }

                        break;
                    case BankInstitutionCode::BBL:
                        if ($view->items['paymentType'] == PAY_BPAY) {
                            $fileFormat = BankFileType::TXT_FILE_TYPE;
                        } elseif ($view->items['paymentType'] == PAY_EFT) {
                            $fileFormat = BankFileType::ABA_FILE_TYPE;
                        }

                        break;
                    case BankInstitutionCode::ABL:
                        if ($view->items['paymentType'] == PAY_BPAY) {
                            $fileFormat = BankFileType::ABA_FILE_TYPE;
                        } elseif ($view->items['paymentType'] == PAY_EFT) {
                            $fileFormat = BankFileType::TXT_FILE_TYPE;
                        }

                        break;
                    case BankInstitutionCode::BNZ:
                        if ($view->items['paymentType'] == PAY_BPAY) {
                            $fileFormat = BankFileType::ABA_FILE_TYPE;
                        } elseif ($view->items['paymentType'] == PAY_EFT) {
                            $fileFormat = BankFileType::AFI_FILE_TYPE;
                        }

                        break;
                    case BankInstitutionCode::WPN:
                        if ($view->items['paymentType'] == PAY_BPAY || $view->items['paymentType'] == PAY_EFT) {
                            $fileFormat = BankFileType::ABA_FILE_TYPE;
                        }

                        break;
                    default:
                        $fileFormat = BankFileType::ABA_FILE_TYPE;
                        break;
                }
            }

            $_filePath = "{$pathPrefix}{$clientDirectory}/{$fileFormat}/" . DOC_DIRECTDEPOSIT . '/';
            $_downloadPath = "{$clientDirectory}/{$fileFormat}/" . DOC_DIRECTDEPOSIT;

            if ($view->items['paymentType'] == PAY_BPAY) {
                if ($batch['institution'] == BankInstitutionCode::WBC) {
                    $file = $batch['institution'] . '_' . 'BPAY' . '_' . date('YmdHis');
                } else {
                    $file = $batch['institution'] . '_' . 'BPAY' . '_' . date('YmdHis') . '.' . $fileFormat;
                }
            } else {
                $file = $batch['institution'] . '_' . 'EFT' . '_' . date('YmdHis') . '.' . $fileFormat;
            }

            $filePath = $_filePath . $file;
            checkDirPath($_filePath);
            $downloadPath = "{$_downloadPath}/{$file}";

            $date = toTimeParts(TODAY);
            $year = ($_SESSION['country_code'] === CountryCode::GB) ? $date[DATE_YEAR] : substr(
                $date[DATE_YEAR],
                EnumsHelper::OFFSET_NEGATIVE_2,
                EnumsHelper::LENGTH_2
            );
            $date = $date[DATE_DAY] . $date[DATE_MONTH] . $year;


            if ($view->items['paymentDate']) {
                $paymentDate = toTimeParts($view->items['paymentDate']);
                $year = ($_SESSION['country_code'] === CountryCode::GB) ? $paymentDate[DATE_YEAR] : substr(
                    $paymentDate[DATE_YEAR],
                    EnumsHelper::OFFSET_NEGATIVE_2,
                    EnumsHelper::LENGTH_2
                );
                $paymentDate = $paymentDate[DATE_DAY] . $paymentDate[DATE_MONTH] . $year;
            } elseif ($generatedList[$batchNumber]['date']) {
                $paymentDate = toTimeParts($generatedList[$batchNumber]['date']);
                $year = ($_SESSION['country_code'] === CountryCode::GB) ? $paymentDate[DATE_YEAR] : substr(
                    $paymentDate[DATE_YEAR],
                    EnumsHelper::OFFSET_NEGATIVE_2,
                    EnumsHelper::LENGTH_2
                );
                $paymentDate = $paymentDate[DATE_DAY] . $paymentDate[DATE_MONTH] . $year;
            } else {
                $paymentDate = $date;
            }

            // -- grab the company details
            $evolve_response = '';
            if ($view->items['paymentType'] == PAY_EFT) {
                //
                $seperate_owner_amounts = false;
                if (isset($view->items['separate_owner_amounts'])) {
                    $seperate_owner_amounts = (bool) $view->items['separate_owner_amounts'];
                }

                //
                $transactions = dbGetPaymentsForBatch($view->items['batchID']);

                // ##################################################################################################################
                // CHECK IF OWNER PAYMENTS ON SEPERATE #
                if ($seperate_owner_amounts) {
                    $owner_payments = [];
                    // GET PAYMENTS THAT IS FOR OWNER
                    foreach (dbGetPaymentsByBatch($view->items['batchID']) as $py) {
                        if (! isset($owner_payments[$py['creditorID']])) {
                            $owner_payments[$py['creditorID']]['total'] = number_format(
                                (float) $py['paymentAmount'],
                                EnumsHelper::LENGTH_2,
                                '.',
                                ''
                            );
                            $owner_payments[$py['creditorID']]['allocs'] = [$py];
                        } else {
                            $temp = $owner_payments[$py['creditorID']];
                            $temp['total'] += number_format(
                                (float) $py['paymentAmount'],
                                EnumsHelper::LENGTH_2,
                                '.',
                                ''
                            );

                            // check for same invoice then update line payment amount. Delete if zero
                            $lineUpdated = false;

                            foreach ($temp['allocs'] as $key => $alloc) {
                                if ($alloc['forOwner'] != 1) {
                                    continue;
                                }

                                // skip invoice if not for owner
                                if ($alloc['invoiceNumber'] == $py['invoiceNumber']) {
                                    $temp['allocs'][$key]['paymentAmount'] += $py['paymentAmount'];

                                    if (round($temp['allocs'][$key]['paymentAmount'], 2) <= 0) {
                                        unset($temp['allocs'][$key]);
                                    }

                                    $lineUpdated = true;
                                    break;
                                }
                            }

                            if (! $lineUpdated) {
                                $temp['allocs'][] = $py;
                            }

                            $owner_payments[$py['creditorID']] = $temp;
                        }
                    }
                }

                // ##################################################################################################################

                $eft = ($_SESSION['country_code'] === CountryCode::GB) ? new DirectEntryGB(
                    $batch['bsbNumber'],
                    $batch['accountNumber'],
                    ($batch['accountName']),
                    $filePath
                ) : new DirectEntry(
                    buildBSB($batch['bsbNumber']),
                    $batch['accountNumber'],
                    ($batch['accountName']),
                    $filePath
                );

                $eft->setHeader($batch['institution'], $batch['paymentID'], 'CIRRUS8', $view->items['paymentDate']);
                $total = 0;
                $importTotal = 0;
                // -- fetch the bank account for the supplier

                if ($batch['paymentMethod'] == 'E' || $view->items['action'] == 'regenerate') {
                    $reference = ($customReferenceNumber) ? $customReferenceNumber : dbNextPaymentReference(
                        $batch['bank']
                    );
                }

                $countRecord = 0;
                foreach ($transactions as $t) {
                    if (! $reference) {
                        $reference = $t['referenceNumber'];
                    }

                    $amount = bcmul(-1, $t['transactionAmount'], 2);
                    if ($amount != $t['cancellationAmount']) {
                        $company = dbGetCompanyBank($t['creditorID']);
                        $countRecord += 1;
                        // $eft->addLine (buildBSB ($company['bsbNumber']), $company['accountNumber'], '50', $amount, $company['bankAccountName'], $t['chequeNumber'], 0);
                        // CHECK AND ADD MULTIPLE LINES FOR SEPERATE PAYMENTS
                        // && bccomp($owner_payments[$t['creditorID']]['total'], $amount, 2) == 0
                        if ($seperate_owner_amounts && isset($owner_payments) && count(
                            $owner_payments ?? []
                        ) > 0 && isset($owner_payments[$t['creditorID']])) {
                            $total_owner = 0;
                            $total_supp = 0;
                            foreach ($owner_payments[$t['creditorID']]['allocs'] as $alloc) {
                                $alloc_amount = $alloc['paymentAmount'];
                                if ($alloc['forOwner'] == '1') {
                                    $eft->addLine(
                                        buildBSB($company['bsbNumber']),
                                        $company['accountNumber'],
                                        EnumsHelper::PAYMENT_TRANSACTION_CODE,
                                        $alloc_amount,
                                        $company['bankAccountName'],
                                        preg_replace('/[^A-Za-z0-9\-]/', ' ', $alloc['propertyName']),
                                        EnumsHelper::ZERO_AMOUNT,
                                        $company['companyID'],
                                        $batch['paymentName'],
                                        $paymentDate,
                                        $batch
                                    );
                                    $total_owner += $alloc_amount;
                                } else {
                                    $total_supp += $alloc_amount;
                                }

                                $importTotal += substr(
                                    $company['accountNumber'],
                                    EnumsHelper::OFFSET_2,
                                    EnumsHelper::LENGTH_11
                                );
                            }

                            if ($total_supp > 0) {
                                $eft->addLine(
                                    buildBSB($company['bsbNumber']),
                                    $company['accountNumber'],
                                    EnumsHelper::PAYMENT_TRANSACTION_CODE,
                                    $total_supp,
                                    $company['bankAccountName'],
                                    $reference,
                                    EnumsHelper::ZERO_AMOUNT,
                                    $company['companyID'],
                                    $batch['paymentName'],
                                    $paymentDate,
                                    $batch
                                );
                            }

                            $amount = $total_owner + $total_supp;
                        } else {
                            $amount -= $t['cancellationAmount'];
                            $eft->addLine(
                                buildBSB($company['bsbNumber']),
                                $company['accountNumber'],
                                EnumsHelper::PAYMENT_TRANSACTION_CODE,
                                $amount,
                                $company['bankAccountName'],
                                $reference,
                                EnumsHelper::ZERO_AMOUNT,
                                $company['companyID'],
                                $batch['paymentName'],
                                $paymentDate,
                                $batch
                            );

                            $importTotal += substr(
                                $company['accountNumber'],
                                EnumsHelper::OFFSET_2,
                                EnumsHelper::LENGTH_11
                            );
                        }

                        $total = bcadd($total, $amount, 2);
                    }
                }

                // -- if it is an ungenerated payment
                if ($batch['paymentMethod'] == 'E' || $view->items['action'] == 'regenerate') {
                    $transaction = [];
                    $transaction['bank'] = $batch['bank'];
                    $transaction['referenceNumber'] = $reference;
                    $transaction['batchReferenceNumber'] = $view->items['batchReferenceNumber'];
                    $transaction['batchNumber'] = $view->items['batchID'];
                    $transaction['amount'] = $total;
                    $transaction['batchDate'] = $batch['batchDate'];
                    dbAddBankRecForEFT($transaction);
                    dbUpdatePaymentBatch($view->items['batchID'], 'X');
                    dbSetPaymentReferenceForTransactions($view->items['batchID'], $reference);
                    //                    $caaps_response = caapsAPI($view->items['batchID'], $batch['batchDate'],'EFT', $reference,'');
                    if ($evolveAPI['use_evolve_api']) {
                        $evolve_response = evolveAPI($view->items['batchID'], $batch['batchDate'], $reference);
                    }
                }

                if ($total > 0 && ! in_array(
                    $batch['institution'],
                    [BankInstitutionCode::WPN, BankInstitutionCode::COU]
                )) {
                    if (! in_array(
                        $batch['institution'],
                        [
                            BankInstitutionCode::BNZ,
                            BankInstitutionCode::ABL,
                            BankInstitutionCode::WB4,
                            BankInstitutionCode::ASB,
                        ]
                    )) {
                        $eft->addLine(
                            buildBSB($batch['bsbNumber']),
                            $batch['accountNumber'],
                            EnumsHelper::FROM_ACCOUNT_TRANSACTION_CODE,
                            $total,
                            $batch['paymentName'],
                            $reference,
                            EnumsHelper::ZERO_AMOUNT
                        );
                    }

                    $eft->setFooter(EnumsHelper::ZERO_AMOUNT, $total, $total, $importTotal, $countRecord);
                }

                $eft->save();
            } elseif ($view->items['paymentType'] == PAY_BPAY) {
                $transactions = dbGetInvoicesForBatch($view->items['batchID']);
                foreach ($transactions as $t) {
                    if (empty($t['customerReference'])) {
                        $validationErrors['no_crn'] = 'No CRN has been entered for some invoices that have been selected for payment. Please enter the CRN first using invoice adjustments before generating the BPay file.';
                    }
                }

                $value = [];
                $valueUnacceptedBanks = [];
                $result = [];
                $first_index = null;
                $first_ref = null;
                foreach ($transactions as $el) {
                    $company = dbGetCompanyBank($el['creditorID']);
                    $amount = bcmul(1, $el['transactionAmount'], 2);
                    $reference = $el['propertyID'] . ': ' . $el['description'];
                    $reference = preg_replace('%[^\w\d\s\-,.\'&/:()+#]%', '', $reference);
                    $reference = substr($reference, EnumsHelper::OFFSET_0, EnumsHelper::LENGTH_35);

                    $index_ = $company['billerCode'] . $el['customerReference'];
                    $index = 'B' . $el['batchNumber'] . 'L' . $el['lineNumber'];
                    if (! array_key_exists($index_, $result)) {
                        $result[$index_] = [];
                        $first_index = $index;
                        $first_ref = $reference;
                    }

                    $arr = [
                        'index' => $first_index,
                        'billerCode' => $company['billerCode'],
                        'customerReference' => $el['customerReference'],
                        'reference' => $first_ref,
                        'batchNumber' => $el['batchNumber'],
                        'companyName' => $company['companyName'],
                    ];
                    $result[$index_][] = $amount;
                    if (in_array($batch['institution'], $acceptedBanks)) {
                        $value[$index_] = $arr;
                        $value[$index_][$index_] = $result[$index_];
                    } else {
                        $valueUnacceptedBanks[$index_] = $arr;
                        $valueUnacceptedBanks[$index_][$index_] = $result[$index_];
                    }
                }

                $batch['numberOfRecords'] = count($value ?? []); // count ($transactions);
                // if ($batch['institution'] == 'CBA' && $batch['numberOfRecords'] > 200) $validationErrors[''] = 'The maximum limit of payments per file is 200.';
                if (noErrors($validationErrors)) {
                    if ($batch['institution'] == BankInstitutionCode::CBA || $batch['institution'] == BankInstitutionCode::BWA) {
                        // if(in_array($batch['institution'], array('CBA', 'BWA')))
                        $bsbNumber = buildCBABSB($batch['bsbNumber']);
                    } elseif ($batch['institution'] == BankInstitutionCode::BBL) {
                        $bsbNumber = $batch['bsbNumber'];
                    } else {
                        $bsbNumber = buildBSB($batch['bsbNumber']);
                    }

                    // -- note the date is used as the file identifier (one payment file allowed per day)
                    if (in_array($batch['institution'], $acceptedBanks)) {
                        if ($batch['institution'] != BankInstitutionCode::MBL) {
                            $bpay = new BPAYEntry(
                                EnumsHelper::SYSTEM_ID_TRACC1,
                                $batch['institution'],
                                EnumsHelper::VERSION_001,
                                $paymentDate,
                                $filePath,
                                $batch
                            );
                        } else {
                            $bpay = new BPAYEntry(
                                EnumsHelper::SYSTEM_ID_CIRRUS,
                                $batch['institution'],
                                EnumsHelper::VERSION_001,
                                $paymentDate,
                                $filePath,
                                $batch
                            );
                        }

                        $banksWithSameHeader = [
                            BankInstitutionCode::CBA,
                            BankInstitutionCode::ANZ,
                            BankInstitutionCode::WBC,
                            BankInstitutionCode::BWA,
                        ];
                        if (in_array($batch['institution'], $banksWithSameHeader)) {
                            $cbaReferenceNumber = $view->items['referenceNumber'] ? $view->items['referenceNumber'] : $customReferenceNumber;
                            $bpay->setHeader(buildIdentifier($cbaReferenceNumber), 1, $bsbNumber);
                        } else {
                            $bpay->setHeader($view->items['batchID'], EnumsHelper::INDEX_1, $bsbNumber);
                        } // -- file header

                        if ($batch['institution'] == BankInstitutionCode::MBL) {
                            $bpay->setSubHeader(EnumsHelper::INDEX_2);
                        } // , $userNumber, $description, $date
                        // }

                        $total = 0;
                        $batch['numberOfRecords'] = 0;
                        foreach ($value as $k => $v) {
                            $amount = array_sum($v[$k]);
                            $amount = number_format($amount, EnumsHelper::LENGTH_2, '.', '');
                            if ($amount > 0) {
                                if (in_array($batch['institution'], $acceptedBanks)) {
                                    $bpayPaymentDate = '20' . substr(
                                        $paymentDate,
                                        EnumsHelper::OFFSET_4,
                                        EnumsHelper::LENGTH_2
                                    ) . substr($paymentDate, EnumsHelper::LENGTH_2, EnumsHelper::LENGTH_2) . substr(
                                        $paymentDate,
                                        EnumsHelper::OFFSET_0,
                                        EnumsHelper::LENGTH_2
                                    );

                                    $bpay->addLine(
                                        $v['index'],
                                        $v['billerCode'],
                                        $v['customerReference'],
                                        $bsbNumber,
                                        $batch['accountNumber'],
                                        $v['reference'],
                                        $amount,
                                        $generatedList[$v['batchNumber']]['nextReferenceNumber'],
                                        $batch['bankState'],
                                        $batch['bankCurrency'],
                                        $v['companyName'],
                                        ($batch['institution'] == 'ANZ'),
                                        $bpayPaymentDate,
                                        $batch['accountName']
                                    );
                                }

                                $total = bcadd($total, $amount, EnumsHelper::LENGTH_2);

                                $batch['numberOfRecords']++;
                            }
                        }

                        // ----------- set header details after line loop
                        $bpay->setNumberOfRecords($batch['numberOfRecords']);
                        $bpay->setBatchAmount($total);
                        $banksWithSameHeader = [
                            BankInstitutionCode::CBA,
                            BankInstitutionCode::ANZ,
                            BankInstitutionCode::WBC,
                            BankInstitutionCode::BWA,
                        ];
                        if (in_array($batch['institution'], $banksWithSameHeader)) {
                            $cbaReferenceNumber = $view->items['referenceNumber'] ? $view->items['referenceNumber'] : $customReferenceNumber;
                            $bpay->setHeader(buildIdentifier($cbaReferenceNumber), EnumsHelper::INDEX_1, $bsbNumber);
                        } else {
                            $bpay->setHeader($view->items['batchID'], EnumsHelper::INDEX_1, $bsbNumber);
                        } // -- file header
                        // ----------- end of header after counting the lines

                        if ($batch['institution'] == BankInstitutionCode::MBL) {
                            $bpay->setSubHeader(EnumsHelper::INDEX_2);
                        } // , $userNumber, $description, $date

                        if (in_array($batch['institution'], $acceptedBanks)) {
                            if ($batch['institution'] == BankInstitutionCode::MBL) {
                                $bpay->setSubFooter($total, EnumsHelper::INDEX_3);
                            }

                            if ($batch['institution'] == BankInstitutionCode::MBL || $batch['institution'] == BankInstitutionCode::NAB || $batch['institution'] == BankInstitutionCode::ANZ || $batch['institution'] == BankInstitutionCode::WBC || $batch['institution'] == BankInstitutionCode::BBL || $batch['institution'] == BankInstitutionCode::WB3 || $batch['institution'] == BankInstitutionCode::WB4) {
                                $bpay->setFooter(EnumsHelper::INDEX_4);
                            }

                            $bpay->save();
                        }
                    } else {
                        $total = 0;
                        $batch['numberOfRecords'] = 0;
                        foreach ($valueUnacceptedBanks as $k => $v) {
                            $amount = array_sum($v[$k]);
                            if ($amount > 0) {
                                $total = bcadd($total, $amount, EnumsHelper::LENGTH_2);
                                $batch['numberOfRecords']++;
                            }
                        }
                    }

                    // -- if it is an ungenerated payment
                    if ($batch['paymentMethod'] == 'B' || $view->items['action'] == 'regenerate') {
                        $reference = ($customReferenceNumber) ? $customReferenceNumber : dbNextPaymentReference(
                            $batch['bank']
                        );
                        $transaction = [];
                        $transaction['bank'] = $batch['bank'];
                        $transaction['referenceNumber'] = $reference;
                        $transaction['batchReferenceNumber'] = $view->items['batchReferenceNumber'];
                        $transaction['batchNumber'] = $view->items['batchID'];
                        $transaction['amount'] = $total;
                        $transaction['batchDate'] = $batch['batchDate'];
                        dbAddBankRecForEFT($transaction);
                        dbUpdatePaymentBatch($view->items['batchID'], 'Y');
                        dbSetPaymentReferenceForTransactions($view->items['batchID'], $reference);
                        //                        $caaps_response = caapsAPI($view->items['batchID'], $batch['batchDate'], 'BPay', $reference,'');

                        if ($evolveAPI['use_evolve_api']) {
                            $evolve_response = evolveAPI($view->items['batchID'], $batch['batchDate'], $reference);
                        }
                    }
                } else {
                    $view->items['validationErrors'] = $validationErrors;
                }
            }

            if ($view->items['paymentType'] == PAY_BPAY && (! in_array($batch['institution'], $acceptedBanks))) {
                // do not display the download path but display a message
                $view->items['statusMessage'] = 'Payment Reference Number successfully assigned. But since we do not support this bank format, the BPAY batch file cannot be generated. ';
            } else {
                $view->items['downloadPath'] = $downloadPath;
            }

            if ($evolveAPI['use_evolve_api'] && $evolve_response != '') {
                $view->items['evolveAPIMessage'] = 'Evolve API : ' . $evolve_response . '.';
            } else {
                $view->items['evolveAPIMessage'] = '';
            }
        } else {
            $view->items['validationErrors'] = $validationErrors;
        }
    }

    usort($list, function ($a, $b) {
        // PRIORITY is the same, sort by DUE DATE
        if ($a['date'] == $b['date'] && $a['referenceNumber'] < $b['referenceNumber']) {
            return 1;
        }

        return $a['date'] < $b['date'] ? 1 : -1;
    });

    $view->items['paymentList'] = $list;

    //    $bankdetails = dbGetBankInfo ($bankID);
    //    if($bankdetails['bankCode'] == 'MBL') {
    //        include('lib/uploadTransactionFileRequest.php');
    //
    //    }

    $view->render();
}

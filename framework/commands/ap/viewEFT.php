<?php

require_once SYSTEMPATH . '/lib/fileuploader/class.fileuploader.php';

if (! function_exists('buildBSB')) {
    function buildBSB($bsb)
    {
        $start = substr($bsb, 0, 3);
        $end = substr($bsb, -3, 3);

        return "{$start}-{$end}";
    }

}

if (! function_exists('buildCBABSB')) {
    function buildCBABSB($bsb)
    {
        $start = substr($bsb, 0, 3);
        $end = substr($bsb, -3, 3);

        return "{$start}{$end}";
    }
}

if (! function_exists('buildIdentifier')) {
    function buildIdentifier($referenceNumber)
    {
        return substr($referenceNumber, -3, 3);
    }
}

function changeEFT($referenceNumber, $newEFTNumber, $bankID)
{
    $lineNumber = dbLineNumberForEFT($newEFTNumber, $bankID, true); // -- grab the highest EFT number (including used EFTs)
    // -- grab the details of the existing EFT
    $existing = dbGetEFT($bankID, $referenceNumber);

    if ($existing) {

        // -- clear the existing EFT
        $o =  [];
        $o['lineNumber'] = $existing['lineNumber'];
        $o['referenceNumber'] = $referenceNumber;
        $o['bankID'] = $bankID;
        $o['amount'] = '0';
        $o['presentedAmount'] = '0';
        $o['cancelledAmount'] = '0';
        $o['cancelledPresentedAmount'] = '0';
        $o['destroyed'] = '0';
        $o['creditorPaid'] = '0';
        $o['debtorPaid'] = '0';
        $o['ownerPaid'] = '0';

        // -- move the EFT
        $existing['referenceNumber'] = $newEFTNumber;
        $existing['lineNumber'] = $lineNumber;
        dbSaveEFT($existing); // -- save it to the new EFT
        dbSaveEFT($o);

        // -- update the transactions to use the new EFT number
        dbResetTransactionForEFT($referenceNumber, $newEFTNumber);

        return true;
    }

    return false;
}

function nextEFTNumber($referenceNumber, $bankID)
{
    $referenceNumber++;
    while (dbCheckEFTNumber($referenceNumber, $bankID)) {
        $referenceNumber++;
    }

    return $referenceNumber;
}

function viewEFT(&$context)
{
    global $monthIndex, $pathPrefix, $clientDirectory , $clientDB;

    if (! isPostback()) {
        $view = new MasterPage(userViews(), '/ap/viewEFT.html');
        $view->setSection($context['module']);
    } else {
        $view = new UserControl(userViews(), '/ap/viewEFT.html');
    }

    $view->bindAttributesFrom($_REQUEST);

    $SEPLINE = dbGetParam('VPDEFAULT', 'SEPLINE');
    if ($SEPLINE != '' && ! isset($view->items['paymentDate'])) {
        $view->items['separate_owner_amounts'] = $SEPLINE == 1;
    }

    $INCREM = dbGetParam('VPDEFAULT', 'INCREM');
    if ($INCREM != '' && ! isset($view->items['paymentDate'])) {
        $view->items['checked'] = $INCREM == 1;
    }

    if ($INCREM == '') {
        $view->items['checked'] = true;
    }

    if (! isset($_POST['includeOwners']) && $INCREM == '') {
        if (! isset($view->items['defaultHasOwners'])) {
            $view->items['checked'] = false;
        }
    } elseif (isset($_POST['includeOwners']) && $_POST['includeOwners'] == 1) {
        $view->items['checked'] = true;
    }

    if (! isset($view->items['paymentDate'])) {
        $view->items['paymentDate'] = TODAY;
    }

    $transactionsPerPage = dbGetParam('TEMPLATE', 'CHQITEMS');

    $totalEFTs = 0;
    $validationErrors =  [];

    // -- check to see if the EFT has been previously generated or not for that batch
    //	$view->items['processed'] = (dbCheckTransaction ($view->items['batchID']) != 0); //this results to false if the first word is not a number
    $view->items['processed'] = (dbCheckTransaction($view->items['batchID']) != null);

    // -- get the transactions for a batch
    $transactions = dbGetPaymentsForBatch($view->items['batchID']);

    $transCount = 0;
    do {
        $view->items['batchReferenceNumber'] = $transactions[$transCount]['referenceNumber'];
        $transCount += 1;
    } while (isset($transactions[$transCount]['referenceNumber']) && ! $transactions[$transCount - 1]['referenceNumber']);

    // -- get the details for the batch (ap batch)
    $batch = dbGetPaymentBatchDetails($view->items['batchID']);

    if ($batch) {
        $eftDetail = dbGetBankRecForEFT($transactions[0]['referenceNumber'], $view->items['batchID'], $batch['bank']);
        $view->items['reAssignReference'] = $transactions[0]['cancellationAmount'] || $eftDetail['presented'] || $eftDetail['canceled'] ? false : true;
        $view->items['bankID'] = $batch['bank'];
        if ($view->items['action'] != 'upload') {
            $view->items['paymentDate'] = $batch['batchDate'];
        }

        $view->items['nextReferenceNumber'] = dbNextPaymentReference($batch['bank']);
        $view->items['institution'] = $batch['institution'];
        $view->items['mblPaymentID'] = $batch['mblPaymentID'];
        $view->items['mblUploadedBy'] = $batch['mblUploadedBy'];
        $view->items['mbluploadedAt'] = $batch['mbluploadedAt'];

        // check MBL
        $bankInfo = dbGetBankInfo($view->items['bankID'], true);
        //        pre_print_r($bankInfo);
        $view->items['MBL_check'] = ($bankInfo['pmbk_direct_upload_username'] && $bankInfo['pmbk_direct_upload_password']);
    }

    // -- for each transaction - calculate the number of EFTs required

    foreach ($transactions as $i => $t) {
        $allocations = dbNumberOfTransactions($t['batchNumber'], $t['creditorID']);
        $EFTs = ceil($allocations / $transactionsPerPage);
        $totalEFTs += (int) $EFTs;
        $transactions[$i]['transactionAmount'] *=  -1;
        $transactions[$i]['EFTs'] = $EFTs;
        $company = dbGetCompany($t['creditorID']);
        $transactions[$i]['payee'] = $company['companyName'];
        $transactions[$i]['email'] = $company['email'];
        $transactions[$i]['sendRemittance'] = $company['sendRemittance'];

        $identifier =  emailIdentifier(EMAILREF_REMITTANCE, $t['referenceNumber'], $t['creditorID']);
        $emailed = dbEmailLogByIdentifier($identifier);
        $transactions[$i]['log'] = count($emailed ?? []);
    }

    // Start Email Address Book
    $email_cen_sendmail_setting = dbGetEmailCenParamSetting('SENDMAIL');
    $email_cen_send = false;
    if ($email_cen_sendmail_setting && $email_cen_sendmail_setting['parameterDescription'] == '1') {
        $email_cen_send = true;
    }

    // End Email Address Book

    switch ($view->items['action']) {
        case 'upload':

            $fileGenerated = false;
            $acceptedBanks = ['MBL', 'NAB', 'CBA', 'ANZ', 'WBC', 'BWA', 'WB2'];

            // -------------------------- GENERATE FILE - taken from viewEFTs.php
            // -- get the details for the batch (ap batch)
            $batchNumber = $view->items['batchID'];
            $batch = dbGetPaymentBatchDetails($batchNumber);

            if ($batch) {
                $view->items['bankID'] = $batch['bank'];
            }

            if (strlen($batch['bsbNumber']) != getBsbLengthFromSession()) {
                $validationErrors[] = 'EFT cannot be generated. ' . getBsbLabelFromSession(
                ) . ' number must be ' . getBsbLengthFromSession() . ' digits.';
            }

            if ($view->items['customReferenceNumber'] && dbGetEFT($batch['bank'], $view->items['customReferenceNumber'])) {
                $validationErrors[] = 'The reference number you use already exist. Please choose another.';
                $customReferenceNumber = null;
            } elseif ($view->items['customReferenceNumber']) {
                $customReferenceNumber = $view->items['customReferenceNumber'];
            } else {
                $customReferenceNumber = null;
            }

            if (($view->items['paymentType'] == PAY_BPAY) && ((isset($view->items['existing'])) && ($view->items['existing'])) && ((! in_array($batch['institution'], $acceptedBanks)))) {
                $validationErrors[] = 'A BPAY batch file is not available for ' . $batch['institution'] . '. Please process the BPAYs individually yourself using your internet banking program.';
            }

            if (noErrors($validationErrors)) {
                //            unset ($list[$batchNumber]);
                switch ($batch['institution']) {
                    case 'CBA':$fileFormat = 'csv';
                        break;
                    case 'NAB':
                        if ($view->items['paymentType'] == PAY_BPAY) {
                            $fileFormat = 'bpb';
                        } elseif ($view->items['paymentType'] == PAY_EFT) {
                            $fileFormat = 'aba';
                        }

                        break;
                    case 'ANZ':
                        if ($view->items['paymentType'] == PAY_BPAY) {
                            $fileFormat = 'bem';
                        } elseif ($view->items['paymentType'] == PAY_EFT) {
                            $fileFormat = 'aba';
                        }

                        break;
                    case 'BWA':
                    case 'WB2':
                        if ($view->items['paymentType'] == PAY_BPAY) {
                            $fileFormat = 'csv';
                        } elseif ($view->items['paymentType'] == PAY_EFT) {
                            $fileFormat = 'aba';
                        }

                        break;
                    case 'MBL':
                        if ($view->items['paymentType'] == PAY_BPAY) {
                            $fileFormat = 'bpay';
                        } elseif ($view->items['paymentType'] == PAY_EFT) {
                            $fileFormat = 'aba';
                        }

                        break;
                    default:$fileFormat = 'aba';
                        break;
                }

                $_filePath =  "{$pathPrefix}{$clientDirectory}/{$fileFormat}/" . DOC_DIRECTDEPOSIT . '/';
                $_downloadPath =  "{$clientDirectory}/{$fileFormat}/" . DOC_DIRECTDEPOSIT;

                if ($view->items['paymentType'] == PAY_BPAY) {
                    if ($batch['institution'] == 'WBC') {
                        $file = $batch['institution'] . '_' . 'BPAY' . '_' . date('YmdHis');
                    } else {
                        $file = $batch['institution'] . '_' . 'BPAY' . '_' . date('YmdHis') . '.' . $fileFormat;
                    }
                } else {
                    $file = $batch['institution'] . '_' . 'EFT' . '_' . date('YmdHis') . '.' . $fileFormat;
                }

                $filePath = $_filePath . $file;
                checkDirPath($_filePath);
                $downloadPath = "{$_downloadPath}/{$file}";

                $date = toTimeParts(TODAY);
                $year = substr($date[DATE_YEAR], -2, 2);
                $date = $date[DATE_DAY] . $date[DATE_MONTH] . $year;


                if ($view->items['paymentDate']) {
                    $paymentDate = toTimeParts($view->items['paymentDate']);
                    $year = substr($paymentDate[DATE_YEAR], -2, 2);
                    $paymentDate = $paymentDate[DATE_DAY] . $paymentDate[DATE_MONTH] . $year;
                }
                //            elseif ($generatedList[$batchNumber]['date'])
                //            {
                //                $paymentDate = toTimeParts ($generatedList[$batchNumber]['date']);
                //                $year = substr ($paymentDate[DATE_YEAR], -2, 2);
                //                $paymentDate = $paymentDate[DATE_DAY] . $paymentDate[DATE_MONTH] . $year;
                //            }
                else {
                    $paymentDate = $date;
                }

                // -- grab the company details
                if ($view->items['paymentType'] == PAY_EFT) {
                    //
                    $seperate_owner_amounts = false;
                    if (isset($view->items['separate_owner_amounts'])) {
                        $seperate_owner_amounts = (bool) $view->items['separate_owner_amounts'];
                    }

                    //
                    $transactions = dbGetPaymentsForBatch($view->items['batchID']);

                    // ##################################################################################################################
                    // CHECK IF OWNER PAYMENTS ON SEPERATE #
                    if ($seperate_owner_amounts) {
                        $owner_payments = [];
                        // GET PAYMENTS THAT IS FOR OWNER
                        foreach (dbGetPaymentsByBatch($view->items['batchID']) as $py) {

                            if (! isset($owner_payments[$py['creditorID']])) {
                                $owner_payments[$py['creditorID']]['total'] = number_format((float) $py['paymentAmount'], 2, '.', '');
                                $owner_payments[$py['creditorID']]['allocs'] = [$py];
                            } else {
                                $temp = $owner_payments[$py['creditorID']];
                                $temp['total'] += number_format((float) $py['paymentAmount'], 2, '.', '');
                                $temp['allocs'][] = $py;
                                $owner_payments[$py['creditorID']] = $temp;
                            }

                        }
                    }

                    // ##################################################################################################################

                    $eft = new DirectEntry(buildBSB($batch['bsbNumber']), $batch['accountNumber'], $batch['accountName'], $filePath);
                    $eft->setHeader($batch['institution'], $batch['paymentID'], 'CIRRUS8', $paymentDate);
                    $total = 0;
                    // -- fetch the bank account for the supplier

                    if ($batch['paymentMethod'] == 'E') {
                        $reference = ($customReferenceNumber) ? $customReferenceNumber : dbNextPaymentReference($batch['bank']);
                    }

                    foreach ($transactions as $t) {
                        if (! $reference) {
                            $reference = $t['referenceNumber'];
                        }

                        $amount = bcmul(-1, $t['transactionAmount'], 2);
                        if ($amount != $t['cancellationAmount']) {
                            $company =  dbGetCompanyBank($t['creditorID']);

                            // $eft->addLine (buildBSB ($company['bsbNumber']), $company['accountNumber'], '50', $amount, $company['bankAccountName'], $t['chequeNumber'], 0);
                            // CHECK AND ADD MULTIPLE LINES FOR SEPERATE PAYMENTS
                            // && bccomp($owner_payments[$t['creditorID']]['total'], $amount, 2) == 0
                            if ($seperate_owner_amounts && isset($owner_payments) && count($owner_payments ?? []) > 0 && isset($owner_payments[$t['creditorID']])) {

                                $total_owner = 0;
                                $total_supp = 0;
                                foreach ($owner_payments[$t['creditorID']]['allocs'] as $alloc) {
                                    $alloc_amount = $alloc['paymentAmount'];
                                    if ($alloc['forOwner'] == '1') {
                                        $eft->addLine(buildBSB($company['bsbNumber']), $company['accountNumber'], '50', $alloc_amount, $company['bankAccountName'], preg_replace('/[^A-Za-z0-9\-]/', ' ', $alloc['propertyName']), 0);
                                        $total_owner += $alloc_amount;
                                    } else {
                                        $total_supp += $alloc_amount;
                                    }
                                }

                                if ($total_supp > 0) {
                                    $eft->addLine(buildBSB($company['bsbNumber']), $company['accountNumber'], '50', $total_supp, $company['bankAccountName'], $reference, 0);
                                }

                                $amount = $total_owner + $total_supp;
                            } else {
                                $eft->addLine(buildBSB($company['bsbNumber']), $company['accountNumber'], '50', $amount, $company['bankAccountName'], $reference, 0);
                            }

                            $total = bcadd($total, $amount, 2);
                        }
                    }

                    // -- if it is an ungenerated payment
                    if ($batch['paymentMethod'] == 'E') {
                        $transaction =  [];
                        $transaction['bank'] = $batch['bank'];
                        $transaction['referenceNumber'] = $reference;
                        $transaction['batchNumber'] = $view->items['batchID'];
                        $transaction['amount'] = $total;
                        $transaction['batchDate'] = $batch['batchDate'];
                        dbAddBankRecForEFT($transaction);
                        dbUpdatePaymentBatch($view->items['batchID'], 'X');
                        dbSetPaymentReferenceForTransactions($view->items['batchID'], $reference);
                    }

                    if ($total > 0) {
                        $eft->addLine(buildBSB($batch['bsbNumber']), $batch['accountNumber'], '13', $total, $batch['paymentName'], $reference, 0);
                        $eft->setFooter(0, $total, $total);
                    }

                    $eft->save();
                } elseif ($view->items['paymentType'] == PAY_BPAY) {
                    $transactions = dbGetInvoicesForBatch($view->items['batchID']);
                    foreach ($transactions as $t) {
                        if (empty($t['customerReference'])) {
                            $validationErrors['no_crn'] = 'No CRN has been entered for some invoices that have been selected for payment. Please enter the CRN first using invoice adjustments before generating the BPay file.';
                        }
                    }

                    $value = [];
                    $result = [];
                    $first_index = null;
                    $first_ref = null;
                    foreach ($transactions as $el) {
                        $company = dbGetCompanyBank($el['creditorID']);
                        $amount = bcmul(1, $el['transactionAmount'], 2);
                        $reference =  $el['propertyID'] . ': ' . $el['description'];
                        $reference = preg_replace('%[^\w\d\s\-,.\'&/:()+#]%', '', $reference);
                        $reference = substr($reference, 0, 35);

                        if (in_array($batch['institution'], $acceptedBanks)) {
                            $index_ = $company['billerCode'] . $el['customerReference'];
                            $index = 'B' . $el['batchNumber'] . 'L' . $el['lineNumber'];
                            if (! array_key_exists($index_, $result)) {
                                $result[$index_] = [];
                                $first_index = $index;
                                $first_ref = $reference;
                            }

                            $arr = [
                                'index' => $first_index,
                                'billerCode' => $company['billerCode'],
                                'customerReference' => $el['customerReference'],
                                'reference' => $first_ref,
                                'batchNumber' => $el['batchNumber'],
                                'companyName' => $company['companyName'],
                            ];
                            $result[$index_][] = $amount;
                            $value[$index_] = $arr;
                            $value[$index_][$index_] = $result[$index_];
                        }
                    }

                    $batch['numberOfRecords'] = count($value ?? []); // count ($transactions);
                    // if ($batch['institution'] == 'CBA' && $batch['numberOfRecords'] > 200) $validationErrors[''] = 'The maximum limit of payments per file is 200.';
                    if (noErrors($validationErrors)) {
                        if ($batch['institution'] == 'CBA' || $batch['institution'] == 'BWA') {
                            // if(in_array($batch['institution'], array('CBA', 'BWA')))
                            $bsbNumber = buildCBABSB($batch['bsbNumber']);
                        } else {
                            $bsbNumber = buildBSB($batch['bsbNumber']);
                        }

                        // -- note the date is used as the file identifier (one payment file allowed per day)
                        if (in_array($batch['institution'], $acceptedBanks)) {

                            if ($batch['institution'] != 'MBL') {
                                $bpay = new BPAYEntry('Tracc1', $batch['institution'], '001', $paymentDate, $filePath, $batch);
                            } else {
                                $bpay = new BPAYEntry('Cirrus', $batch['institution'], '001', $paymentDate, $filePath, $batch);
                            }

                            $banksWithSameHeader = ['CBA', 'ANZ', 'WBC', 'BWA'];
                            if (in_array($batch['institution'], $banksWithSameHeader)) {
                                $cbaReferenceNumber = $view->items['referenceNumber'] ? $view->items['referenceNumber'] : $customReferenceNumber;
                                $bpay->setHeader(buildIdentifier($cbaReferenceNumber), 1, $bsbNumber);
                            } else {
                                $bpay->setHeader($view->items['batchID'], 1, $bsbNumber);
                            } // -- file header

                            if ($batch['institution'] == 'MBL') {
                                $bpay->setSubHeader(2);
                            } // , $userNumber, $description, $date
                            // }

                            $total = 0;
                            $batch['numberOfRecords'] = 0;
                            foreach ($value as $k => $v) {
                                $amount = array_sum($v[$k]);
                                $amount = number_format($amount, 2, '.', '');
                                if ($amount > 0) {
                                    if (in_array($batch['institution'], $acceptedBanks)) {
                                        $bpay->addLine(
                                            $v['index'],
                                            $v['billerCode'],
                                            $v['customerReference'],
                                            $bsbNumber,
                                            $batch['accountNumber'],
                                            $v['reference'],
                                            $amount,
                                            $generatedList[$v['batchNumber']]['nextReferenceNumber'],
                                            $batch['bankState'],
                                            $batch['bankCurrency'],
                                            $v['companyName'],
                                            ($batch['institution'] == 'ANZ')
                                        );
                                    }

                                    $total = bcadd($total, $amount, 2);

                                    $batch['numberOfRecords']++;
                                }
                            }

                            // ----------- set header details after line loop
                            $bpay->setNumberOfRecords($batch['numberOfRecords']);
                            $bpay->setBatchAmount($total);
                            $banksWithSameHeader = ['CBA', 'ANZ', 'WBC', 'BWA'];
                            if (in_array($batch['institution'], $banksWithSameHeader)) {
                                $cbaReferenceNumber = $view->items['referenceNumber'] ? $view->items['referenceNumber'] : $customReferenceNumber;
                                $bpay->setHeader(buildIdentifier($cbaReferenceNumber), 1, $bsbNumber);
                            } else {
                                $bpay->setHeader($view->items['batchID'], 1, $bsbNumber);
                            } // -- file header
                            // ----------- end of header after counting the lines

                            if ($batch['institution'] == 'MBL') {
                                $bpay->setSubHeader(2);
                            } // , $userNumber, $description, $date

                            if (in_array($batch['institution'], $acceptedBanks)) {
                                if ($batch['institution'] == 'MBL') {
                                    $bpay->setSubFooter($total, 3);
                                }

                                if ($batch['institution'] == 'MBL' || $batch['institution'] == 'NAB' || $batch['institution'] == 'ANZ' || $batch['institution'] == 'WBC') {
                                    $bpay->setFooter(4);
                                }

                                $bpay->save();
                            }
                        } else {
                            $total = 0;
                            $batch['numberOfRecords'] = 0;
                            foreach ($value as $k => $v) {
                                $amount = array_sum($v[$k]);
                                if ($amount > 0) {
                                    $total = bcadd($total, $amount, 2);
                                    $batch['numberOfRecords']++;
                                }
                            }
                        }

                        // -- if it is an ungenerated payment
                        if ($batch['paymentMethod'] == 'B') {
                            $reference = ($customReferenceNumber) ? $customReferenceNumber : dbNextPaymentReference($batch['bank']);
                            $transaction =  [];
                            $transaction['bank'] = $batch['bank'];
                            $transaction['referenceNumber'] = $reference;
                            $transaction['batchNumber'] = $view->items['batchID'];
                            $transaction['amount'] = $total;
                            $transaction['batchDate'] = $batch['batchDate'];
                            dbAddBankRecForEFT($transaction);
                            dbUpdatePaymentBatch($view->items['batchID'], 'Y');
                            dbSetPaymentReferenceForTransactions($view->items['batchID'], $reference);
                        }
                    } else {
                        $view->items['validationErrors'] = $validationErrors;
                    }
                }

                if ($view->items['paymentType'] == PAY_BPAY && (! in_array($batch['institution'], $acceptedBanks))) {
                    // do not display the download path but display a message
                    $view->items['statusMessage'] = 'Payment Reference Number successfully assigned. But since we do not support this bank format, the BPAY batch file cannot be generated.';
                } else { // SUCCESS
                    // $view->items['downloadPath'] = $downloadPath;
                    $fileGenerated = true;
                }
            } else {
                $view->items['validationErrors'] = $validationErrors;
            }

            // -------------------------- END of file generation

            if ($fileGenerated) {
                //            $directory = REPORTPATH . '/' . str_replace (' ', '', $_SESSION['database']) . '/bankUpload/';
                //            if (!is_dir($directory)) {
                //                mkdir($directory);
                //            }
                //
                //            $filename = substr(substr($_FILES['mbl_upload']['name'], 0, strrpos($_FILES['mbl_upload']['name'], ".")), 0, 15) . '_' .mt_rand(0,10000). time();
                //
                //            $FileUploader = new FileUploader('mbl_upload', array(
                //                'uploadDir' => $directory,
                //                'title'     => $filename,
                //            ));
                //            $FileUploader->upload();

                //          require_once ('lib/uploadTransactionFileRequest.php');
                //          $upload = new uploadTransactionFileRequest();
                //          $upload->upload($directory.$filename.'.'.$view->items['fileFormat'], $view->items['fileFormat'], $view->items['institution']);
                //          return true;

                require_once SYSTEMPATH . '/lib/uploadTransactionFileRequest.php';
                $upload = new uploadTransactionFileRequest();
                //          $response = $upload->upload_beSimple($filename, strtoupper($view->items['fileFormat']), $view->items['institution'], $view->items['batchID']);
                //          echo json_encode($response);
                //          return true;
                $response = $upload->upload_beSimple($filePath, $view->items['batchID']);
                $response = $response->response;

                if ($response->status == 1) {
                    $view->items['statusMessage'] = 'File successfully uploaded. <b>Payment Id : ' . $response->paymentId . '</b>';
                } elseif (isset($response->errors->description)) {
                    //                uploadError : '1',
                    //                errorType : response.errors.type,
                    //                errorCode : response.errors.code,
                    //                errorCategory : response.errors.category,
                    //                errorDescription : response.errors.description
                    $validationErrors[] = $response->errors->category . ' : ' . $response->errors->description;
                } else {
                    $validationErrors[] = 'MBL Upload Error.';
                }

                $batch = dbGetPaymentBatchDetails($view->items['batchID']);

                if ($batch) {
                    $view->items['bankID'] = $batch['bank'];
                    //                if ($view->items['action'] != 'upload')
                    //                    $view->items['paymentDate'] = $batch['batchDate'];
                    $view->items['nextReferenceNumber'] = dbNextPaymentReference($batch['bank']);
                    $view->items['institution'] = $batch['institution'];
                    $view->items['mblPaymentID'] = $batch['mblPaymentID'];
                    $view->items['mblUploadedBy'] = $batch['mblUploadedBy'];
                    $view->items['mbluploadedAt'] = $batch['mbluploadedAt'];
                }
            }

            break;

        case 'print':
            $context['batchNumber'] = $view->items['batchID'];
            $context['referenceNumber'] = $view->items['referenceNumber'];
            $context['bankID'] = $view->items['bankID'];
            $view->items['Component:PrintEFT'] = fetchCommand('printEFTs', 'ap');
            break;

        case 'reset':
            // -- same as for changing a single EFT - grab a list of all EFTs and process starting from the first EFT number
            foreach ($transactions as $id => $t) {


                dbResetTransactionForEFT($t['referenceNumber']); // -- if you dont pass in a new EFT number it will reset the EFT numbers in ap_transaction
                $lineNumber = dbLineNumberForEFT($t['referenceNumber'], $view->items['bankID'], true);
                // -- clear the EFT number associated
                $clear = [];
                $clear['lineNumber'] = $lineNumber;
                $clear['referenceNumber'] = $t['referenceNumber'];
                $clear['bankID'] = $view->items['bankID'];
                $clear['EFTAmount'] = '0';
                $clear['presentedAmount'] = '0';
                $clear['cancelledAmount'] = '0';
                $clear['cancelledPresentedAmount'] = '0';
                $clear['destroyed'] = '0';
                $clear['creditorPaid'] = '0';
                $clear['debtorPaid'] = '0';
                $clear['ownerPaid'] = '0';
                dbSaveEFT($clear);

                $transactions[$id]['referenceNumber'] = null;
            }

            $view->items['processed'] = (dbCheckTransaction($view->items['batchID']) != 0);
            break;

        case 'change':
            if (dbCheckEFTNumber($view->items['nextEFT'], $view->items['bankID'])) {
                $validationErrors[] = 'That EFT number is already assigned - the next available reference number is ' . dbNextEFTNumber($view->items['bankID']);
            }

            if ($view->items['referenceNumber'] == $view->items['nextEFT']) {
                $validationErrors[] = 'You cannot assign a EFT to itself';
            }

            // -- get the current line number for the new EFT
            if (noErrors($validationErrors)) {
                changeEFT($view->items['referenceNumber'], $view->items['nextEFT'], $view->items['bankID']);
            }

            $view->items['nextEFT'] = dbNextEFTNumber($view->items['bankID']);
            break;
        case 'assign':

            // -- check if the EFT number given has been used (it may have been changed to not be the next available)
            if (dbCheckEFTNumber($view->items['nextEFT'], $view->items['bankID'])) {
                $validationErrors[] = 'That EFT number is already assigned - the next available reference number is ' . dbNextEFTNumber($view->items['bankID']);
            }

            if ($view->items['processed']) {
                $validationErrors[] = 'The batch you have designated has already been assigned to a EFT';
            }

            if (noErrors($validationErrors)) {
                $referenceNumber = null;

                // -- go through each transaction
                foreach ($transactions as $id => $t) {

                    $lineNumber = null;
                    for ($i = 1; $i <= $t['EFTs']; $i++) {
                        $referenceNumber = ($referenceNumber) ? nextEFTNumber($referenceNumber, $view->items['bankID']) : $view->items['nextEFT'];
                        $lineNumber = dbLineNumberForEFT($referenceNumber, $view->items['bankID']);
                        $transactions[$id]['referenceNumber'] = $referenceNumber;


                        $bank = [];
                        $bank['EFTAmount'] = $t['transactionAmount'];
                        $bank['EFTDate'] = $t['transactionDate'];
                        $bank['creditorPaid'] = 1;
                        $bank['creditorID'] = $t['creditorID'];
                        $bank['bankID'] =  $view->items['bankID'];
                        $bank['referenceNumber'] = $referenceNumber;
                        $bank['lineNumber'] = $lineNumber;

                        if ($i < $t['EFTs']) {
                            // -- format it as an 'additional page'
                            $bank['cancelledAmount'] = $t['transactionAmount'];
                            $bank['cancelledDate'] = $t['transactionDate'];
                        }

                        dbUpdateBankRecForEFT($bank);
                    }

                    $transaction = [];
                    $transaction['batchNumber'] = $t['batchNumber'];
                    $transaction['lineNumber'] = $t['lineNumber'];
                    $transaction['modifiedUser'] = $_SESSION['un'];
                    $transaction['referenceNumber'] = $referenceNumber;
                    dbUpdateTransactionForEFT($transaction);

                    /* not sure this is even required - its only updating the modified dates
                        $command = 	"UPDATE pmxc_ap_alloc SET
                                        pmxc_mod_user	= '$create_user',
                                        pmxc_mod_time	= '$current_time',
                                        pmxc_mod_date	= CONVERT(datetime, '$current_date', 103)
                                    WHERE (pmxc_f_batch = '$batch_number') AND (pmxc_f_line = '$batch_line_number') AND (pmxc_f_type = 'PAY');";
                    */
                }

                $view->items['statusMessage'] = 'Successfully assigned the EFT';
                $view->items['processed'] = true;
            }


            break;

        case 'remittance':
            $_transactions = dbGetInvoicesForBatch($view->items['batchID'], $view->items['lineNumber']);

            $batch = dbGetPaymentBatchDetails($view->items['batchID']);
            foreach ($_transactions as &$t) {
                $creditorID = $t['creditorID'];
                $referenceNumber = $t['referenceNumber'];
                $t['payee'] = dbGetPayee($t['creditorID']);
            }


            @include_once __DIR__ . '/remittanceAdvicePDF.php';
            $remittanceAdvice = prepareRemittanceAdvice($batch['bankID'], $batch['batchDate'], $referenceNumber, $creditorID, $_transactions, $view->items['paymentType']);
            if ($remittanceAdvice) {
                $view->bindAttributesFrom($remittanceAdvice);
            }

            break;

        case 'email':
            $_transactions = dbGetInvoicesForBatch($view->items['batchID'], $view->items['lineNumber']);
            $batch = dbGetPaymentBatchDetails($view->items['batchID']);
            foreach ($_transactions as &$t) {
                $referenceNumber = $t['referenceNumber'];
                $payee = dbGetPayee($t['creditorID']);
                $t['payee'] = $payee;
            }

            @include_once __DIR__ . '/remittanceAdvicePDF.php';
            $remittanceAdvice = prepareRemittanceAdvice($batch['bankID'], $batch['batchDate'], $referenceNumber, $view->items['creditorID'], $_transactions, $view->items['paymentType']);
            if ($remittanceAdvice) {
                $company = dbGetCompany($view->items['creditorID']);
                if (! is_file(BASEPATH . "/framework/views/emails/{$clientDB}/email_layout2.html")) {
                    $email = new EmailTemplate('views/emails/remittanceEmail.html', SYSTEMURL);
                } else {
                    $email = new EmailTemplate("views/emails/{$clientDB}/email_layout2.html", SYSTEMURL);
                }

                $email->bindAttributesFrom($view->items);
                $email->items['CompanyName'] = $company['companyName'];
                $email->items['SalutationName'] = dbGetSupplierContactSalutation($view->items['creditorID']);

                $subject = dbGetEmailTemplate(2);
                $subject = str_replace('%CompanyName%', $company['companyName'], $subject);
                $subject = str_replace('%SalutationName%', $email->items['SalutationName'], $subject);

                //
                if ($email_cen_send) {
                    $attachment =  [];
                    $attachment[0]['file'] = $remittanceAdvice['filePath'];
                    $attachment[0]['content_type'] = returnMIMEType($remittanceAdvice['filePath']);
                    $identifier =  emailIdentifier(EMAILREF_REMITTANCE, $referenceNumber, $view->items['creditorID']);
                    $emailAddress = dbGetCompanyEmailCentralised($view->items['creditorID'], ['OWNREM', 'SUPREM']);
                    $defaultEmailSend = '1';
                    foreach ($emailAddress as $aRowCentralisedEmailCheck) {
                        $defaultEmail = $aRowCentralisedEmailCheck['defaultEmail'];
                        if ($defaultEmail == '0') {
                            $defaultEmailSend = $defaultEmail;
                        }
                    }

                    $email_sent_flag = false;
                    $view->items['statusMessage'] = 'The remittance advice was successfully sent:';
                    $email_send_message = '';
                    foreach ($emailAddress as $aRowCentralisedEmail) {
                        $emailAddressCentral = $aRowCentralisedEmail['emailAddress'];
                        $defaultEmail = $aRowCentralisedEmail['defaultEmail'];
                        if ($defaultEmailSend == $defaultEmail && isValid($emailAddressCentral, TEXT_EMAIL, false)) {
                            $email_sent_flag = true;
                            sendMail($emailAddressCentral, null, $email->toString(), $subject ? $subject : 'Remittance Advice for ' . $payee, $attachment, null, $identifier);
                            $email_send_message = $email_send_message . '<br/>' . $emailAddressCentral;
                        }
                    }

                    if ($view->items['action'] != 'emailAll') {
                        $view->items['statusMessage'] .= $email_send_message;
                    }

                } else {
                    $emailAddress = $company['email'];
                    if (isValid($emailAddress, TEXT_EMAIL, false) && $remittanceAdvice['total'] > 0) {
                        $attachment =  [];
                        $attachment[0]['file'] = $remittanceAdvice['filePath'];
                        $attachment[0]['content_type'] = returnMIMEType($remittanceAdvice['filePath']);
                        $identifier =  emailIdentifier(EMAILREF_REMITTANCE, $referenceNumber, $view->items['creditorID']);
                        sendMail($emailAddress, null, $email->toString(), $subject ? $subject : 'Remittance Advice for ' . $payee, $attachment, null, $identifier);
                        $view->items['statusMessage'] = 'The remittance advice was successfully sent';
                    }
                }
            }

            break;

        case 'emailAll':
            @include_once __DIR__ . '/remittanceAdvicePDF.php';
            $a = dbGetPaymentsForBatch($view->items['batchID'], null, $view->items['checked']);
            $batch = dbGetPaymentBatchDetails($view->items['batchID']);
            foreach ($a as $s) {
                $remittanceAdvice = null;
                $payee = null;
                $_transactions = dbGetInvoicesForBatch($view->items['batchID'], $s['lineNumber']);
                foreach ($_transactions as &$t) {
                    $referenceNumber = $t['referenceNumber'];
                    $payee = dbGetPayee($t['creditorID']);
                    $t['payee'] = $payee;
                }

                if ($_transactions) {
                    $remittanceAdvice = prepareRemittanceAdvice($batch['bankID'], $batch['batchDate'], $referenceNumber, $s['creditorID'], $_transactions, $view->items['paymentType'], $s['lineNumber']);
                }

                if ($remittanceAdvice) {
                    $company = dbGetCompany($s['creditorID']);
                    if ($company && $company['sendRemittance'] == 1) {
                        if (! is_file(BASEPATH . "/framework/views/emails/{$clientDB}/email_layout2.html")) {
                            $email = new EmailTemplate('views/emails/remittanceEmail.html', SYSTEMURL);
                        } else {
                            $email = new EmailTemplate("views/emails/{$clientDB}/email_layout2.html", SYSTEMURL);
                        }

                        $email->bindAttributesFrom($view->items);
                        $email->items['CompanyName'] = $company['companyName'];
                        $email->items['SalutationName'] = dbGetSupplierContactSalutation($s['creditorID']);

                        $subject = dbGetEmailTemplate(2);
                        $subject = str_replace('%CompanyName%', $company['companyName'], $subject);
                        $subject = str_replace('%SalutationName%', $email->items['SalutationName'], $subject);

                        //
                        if ($email_cen_send) {
                            $attachment =  [];
                            $attachment[0]['file'] = $remittanceAdvice['filePath'];
                            $attachment[0]['content_type'] = returnMIMEType($remittanceAdvice['filePath']);
                            $identifier =  emailIdentifier(EMAILREF_REMITTANCE, $referenceNumber, $s['creditorID']);
                            $emailAddress = dbGetCompanyEmailCentralised($s['creditorID'], ['OWNREM', 'SUPREM']);
                            $defaultEmailSend = '1';
                            foreach ($emailAddress as $aRowCentralisedEmailCheck) {
                                $defaultEmail = $aRowCentralisedEmailCheck['defaultEmail'];
                                if ($defaultEmail == '0') {
                                    $defaultEmailSend = $defaultEmail;
                                }
                            }

                            $email_sent_flag = false;
                            $view->items['statusMessage'] = 'The remittance advice was successfully sent';
                            $email_send_message = '';
                            foreach ($emailAddress as $aRowCentralisedEmail) {
                                $emailAddressCentral = $aRowCentralisedEmail['emailAddress'];
                                $defaultEmail = $aRowCentralisedEmail['defaultEmail'];
                                if ($defaultEmailSend == $defaultEmail && isValid($emailAddressCentral, TEXT_EMAIL, false)) {
                                    $email_sent_flag = true;
                                    sendMail($emailAddressCentral, null, $email->toString(), $subject ? $subject : 'Remittance Advice for ' . $payee, $attachment, null, $identifier);
                                    $email_send_message = $email_send_message . '<br/>' . $emailAddressCentral;
                                }
                            }

                            if ($view->items['action'] != 'emailAll') {
                                $view->items['statusMessage'] .= $email_send_message;
                            }
                        } else {
                            $emailAddress = $company['email'];
                            if (isValid($emailAddress, TEXT_EMAIL, false) && $remittanceAdvice['total'] > 0) {
                                $attachment =  [];
                                $attachment[0]['file'] = $remittanceAdvice['filePath'];
                                $attachment[0]['content_type'] = returnMIMEType($remittanceAdvice['filePath']);
                                $identifier =  emailIdentifier(EMAILREF_REMITTANCE, $referenceNumber, $s['creditorID']);
                                sendMail($emailAddress, null, $email->toString(), $subject ? $subject : 'Remittance Advice for ' . $payee, $attachment, null, $identifier);
                                $view->items['statusMessage'] = 'The remittance advice was successfully sent';
                            }
                        }
                    }
                }
            }

            break;
        case 'oneDocumentAll':
        case 'oneDocumentNoEmail':
            include_once __DIR__ . '/remittanceAdvicePDF.php';
            $paymentForBatch = dbGetPaymentsForBatch($view->items['batchID'], null, $view->items['checked']);
            $batchDetails = dbGetPaymentBatchDetails($view->items['batchID']);

            $remittanceAdvice = ($view->items['action'] == 'oneDocumentAll') ? prepareRemittanceAdviceForAll($view->items['batchID'], $paymentForBatch, $batchDetails, $view->items['paymentType'], true) : prepareRemittanceAdviceForAll($view->items['batchID'], $paymentForBatch, $batchDetails, $view->items['paymentType'], false);

            $view->items['filePath'] = $remittanceAdvice['filePath'];
            $view->items['downloadPath'] = $remittanceAdvice['downloadPath'];
            break;
    }

    /* refresh the list of EFTs */
    if (in_array($view->items['action'], ['assign', 'change', 'reset', 'email', 'emailAll'])) {
        // -- get the transactions for a batch
        $transactions = dbGetPaymentsForBatch($view->items['batchID']);

        // -- get the details for the batch (ap batch)
        $batch = dbGetPaymentBatchDetails($view->items['batchID']);

        if ($batch) {
            $view->items['bankID'] = $batch['bank'];
            $view->items['paymentDate'] = $batch['batchDate'];
            $view->items['nextReferenceNumber'] = dbNextPaymentReference($batch['bank']);
        }

        // -- for each transaction - calculate the number of EFTs required

        foreach ($transactions as $i => $t) {
            if ($transactions[$i]['transactionAmount'] != 0.00) {
                $allocations = dbNumberOfTransactions($t['batchNumber'], $t['creditorID']);
                $EFTs = ceil($allocations / $transactionsPerPage);
                $totalEFTs += (int) $EFTs;
                $transactions[$i]['transactionAmount'] *= -1;
                $transactions[$i]['EFTs'] = $EFTs;
                // dbGetPayee ($t['creditorID']);
                $company = dbGetCompany($t['creditorID']);
                $transactions[$i]['payee'] = $company['companyName'];
                $transactions[$i]['email'] = $company['email'];
                $transactions[$i]['sendRemittance'] = $company['sendRemittance'];


                $identifier =  emailIdentifier(EMAILREF_REMITTANCE, $t['referenceNumber'], $t['creditorID']);
                $emailed = dbEmailLogByIdentifier($identifier);
                $transactions[$i]['log'] = count($emailed ?? []);

            }
        }

    }

    // if (!isset ($view->items['nextEFT'])) $view->items['nextEFT'] = dbNextEFTNumber ($view->items['bankID']);




    $view->items['endEFT'] = $view->items['nextEFT'] + ($totalEFTs - 1);
    $view->items['totalEFTs'] = $totalEFTs;
    $view->items['paymentList'] = $transactions;
    $view->items['validationErrors'] = $validationErrors;

    $view->render();
}

<?php

function fetchOutstandingAmounts($propertyID, $runDate, $accountList, &$outstandingAmounts)
{
    $totalOutstanding = 0;
    $unpaidInvoices = dbGetUnpaidInvoices($propertyID, $runDate, null, $accountList);
    foreach ($unpaidInvoices as $invoice) {
        $amount = $invoice['transactionAmount'];

        $offset = bcadd($invoice['totalAllocated'], $invoice['totalReallocated'], 2);
        // -- still doesnt take into account adjustments yet (offset_4)
        $amount = bcsub($invoice['transactionAmount'], $offset, 2);

        if ($amount != 0) {
            $invoice['amount'] = $amount;
            $outstandingAmounts[] = $invoice;
            $totalOutstanding += ($amount * 1);
        }
    }

    return $totalOutstanding;
}


function prepareAgentTaxInvoice(
    $propertyID,
    $invoiceDate,
    &$parentInvoice,
    $outstandingAmounts,
    $issueDate = null,
    $batchNumber = null
) {
    global $clientDirectory, $pathPrefix;


    // -- initialise the variables that will store the transaction amounts
    $total = 0;
    // $outstandingAmounts = array();

    // -- this may need revising (suppliers?) - but it grabs the
    // $debtorID = dbGetDebtor($propertyID,$leaseID);
    //


    // -- if there are transactions to be printed :
    //  if (fetchOutstandingAmounts($propertyID, $invoiceDate, $itemList, &$outstandingAmounts) != 0) {


    $headerTitle = 'TAX INVOICE';
    $totalTitle = 'INVOICE TOTAL';


    if (! $issueDate) {
        $issueDate = TODAY;
    }

    [$day, $month, $year] = explode('/', $invoiceDate);
    $dueDate = $invoiceDate;

    // -- if there are new amounts to be invoiced - there wont be an invoice number booked yet! so book one out :)

    $invoiceNumber = null;
    // if (count($newAmounts) > 0)  $invoiceNumber = bookInvoiceNumber();


    // -- will need to store current DB index in session to allow for translation of logos
    $logoFile = dbGetClientLogo();
    $logoPath = "assets/clientLogos/{$logoFile}";

    $filename = "agent_tax_invoice_{$year}{$month}{$day}_{$propertyID}.pdf";
    $filePath = "{$pathPrefix}{$clientDirectory}/pdf/AgentTaxInvoice/{$filename}";
    $downloadPath = "{$clientDirectory}/pdf/AgentTaxInvoice/{$filename}";

    $invoice = new Invoice($filePath, $logoPath);
    if ($parentInvoice) {
        $masterInvoice = new Invoice($parentInvoice, $logoPath);
    }


    $agentData = dbGetAgentDetails();
    $headerData = dbGetHeaderDetails($propertyID); // -- principal owner
    $leaseAddress = dbGetCompanyMailingAddress($propertyID); // -- principal owner
    $bankData = dbGetBankDetails($propertyID);

    // -- some data is shared across several components in the PDF, so instead of doing multiple db calls and binding locally within the object,
    // -- a single DB call is made and data bound to the object externally

    // -- PDFobjects are usually groups of data repeated statically on every page of the PDF, and as such are loaded once into the PDF and called at render...
    // -- mutating values are accessed from the Invoice object itself such as changing totals, invoice rows, sub titles within the statement

    // -- note that data is bound between array values to the object attributes sharing the same name as the array key - ie
    // -- $this->test = $array['test']

    $header = new InvoiceHeader($propertyID, $dueDate, $issueDate, $headerTitle);
    $header->bindAttributesFrom($headerData);

    $invoice->attachObject('header', $header);

    $footer = new InvoiceFooter($propertyID, $dueDate, $issueDate, $totalTitle);
    $footer->bindAttributesFrom($headerData);
    $footer->bindAttributesFrom($agentData);
    $footer->bindAttributesFrom($leaseAddress);
    $footer->bindAttributesFrom($bankData);

    $invoice->attachObject('footer', $footer);


    $agentDetails = new AgentDetails($propertyID);
    $agentDetails->bindAttributesFrom($agentData);

    $invoice->attachObject('agentDetails', $agentDetails);

    $invoice->attachObject('leaseDescription', new LeaseDescription($propertyID, $leaseID));
    $invoice->attachObject('statement', new InvoiceLines($totalTitle));
    $invoice->attachObject('foldline', new FoldLines());
    $invoice->attachObject('cutoffline', new CutOffLine());
    $invoice->attachObject('mailingAddress', new MailAddressWindow($propertyID));
    $invoice->attachObject('traccFooter', new TraccFooter('assets/clientLogos/tracc_logo.jpg', 'Agent_Tax_Invoice', 1));

    if (isset($note)) {
        $invoice->attachObject('note', new InvoiceNote($note));
    }

    if ($parentInvoice) {
        $masterInvoice->objects = $invoice->objects;
    }

    $invoice->preparePage();
    if ($parentInvoice) {
        $masterInvoice->preparePage();
    }


    if (count($outstandingAmounts ?? []) > 0) {
        $currentTotal = 0;
        $currentNet = 0;
        $currentTax = 0;

        foreach ($outstandingAmounts as $row) {
            $currentTotal += ($row['appliedAmount'] * 1);
            $currentNet += ($row['netAmount'] * 1);
            $currentTax += ($row['taxAmount'] * 1);

            $total += ($row['appliedAmount'] * 1);

            $invoice->renderInvoiceRow(
                $row['transactionDate'],
                $invoiceNumber,
                $row['description'],
                toMoney($row['netAmount'], null),
                toMoney($row['taxAmount'], null),
                toMoney($row['appliedAmount'], null)
            );
            if ($parentInvoice) {
                $masterInvoice->renderInvoiceRow(
                    $row['transactionDate'],
                    $invoiceNumber,
                    $row['description'],
                    toMoney($row['netAmount'], null),
                    toMoney($row['taxAmount'], null),
                    toMoney($row['appliedAmount'], null)
                );
            }
        }

        $invoice->renderSubTotal(
            'Total For This Tax Invoice',
            toMoney($currentNet, null),
            toMoney($currentTax, null),
            toMoney($currentTotal, null)
        );
        if ($parentInvoice) {
            $masterInvoice->renderSubTotal(
                'Total For This Tax Invoice',
                toMoney($currentNet, null),
                toMoney($currentTax, null),
                toMoney($currentTotal, null)
            );
        }
    }


    $invoice->renderTotal($total);
    $invoice->renderRemittanceTotal($total);
    $invoice->close();

    if ($parentInvoice) {
        $masterInvoice->renderTotal($total);
        $masterInvoice->renderRemittanceTotal($total);
        $masterInvoice->close();
    }

    // -- if there are new transactions being reported - commit the new invoice number to them :)
    // if (count($newAmounts) > 0) dbCommitTransaction($propertyID, $leaseID, $invoiceNumber, $invoiceDate);
    $data['filePath'] = $filePath;
    $data['downloadPath'] = $downloadPath;
    $data['invoiceNumber'] = $invoiceNumber;

    return $data;
    //   }


}


/*************************************************************************************************************************/

// INVOICE OBJECTS - repeating groups that appear in each invoice


class LeaseDescription extends PDFObject
{
    public $description;

    public function __construct($propertyID, $leaseID)
    {
        $this->description = dbGetLeaseDescription($propertyID, $leaseID);
    }

    public function preRender(&$pdf) {}

    public function render(&$pdf)
    {
        $pdf->save();
        $pdf->setColorExt('both', 'rgb', 0.95, 0.95, 0.95, 0);
        $pdf->setlinewidth(0.5);
        // $pdf->rect(25,563,545,25); $pdf->fill();
        $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        $this->setFont($pdf, 'Helvetica-Bold', 9);
        $pdf->showBoxed('PREMISES : ' . $this->description, 30, 583, 535, 22, 'left', '');
        $pdf->restore();
    }
}


class InvoiceHeader extends PDFobject
{
    public $dueDate;

    public $issueDate;

    public $propertyID;

    public $propertyName;

    public $ownerName;

    public $ownerABN;

    public $title;

    public $business_label;

    public bool $displayBsb;

    public $_name = 'invoiceHeader';

    public function __construct($propertyID, $dueDate, $issueDate, $title)
    {
        $this->propertyID = $propertyID;
        $this->issueDate = $issueDate;
        $this->dueDate = $dueDate;
        $this->title = $title;

        // [CIR-2628] - settings for different client based on country
        $this->business_label = $_SESSION['country_default']['business_label'];
        $this->displayBsb = getDisplayBsbFromSession();
    }

    public function preRender(&$pdf)
    {
        // -- main box
        $pdf->setColorExt('both', 'rgb', 0.9, 0.9, 0.9, 0); // Setting the Color to Gray
        $pdf->rect(25, 777, 275, 15);
        $pdf->fill();
        $pdf->setlinewidth(0.5);

        $pdf->setColorExt('both', 'rgb', 0.75, 0.75, 0.75, 0);
        $pdf->moveto(25, 692);
        $pdf->lineto(300, 692);
        $pdf->stroke();

        $pdf->setlinewidth(2);
        $pdf->setColorExt('both', 'rgb', 0.75, 0.75, 0.75, 0);
        $pdf->rect(25, 692, 275, 100);
        $pdf->stroke();

        $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        $this->setFont($pdf, 'Helvetica-Bold', 8);
        $pdf->showBoxed('Due Date', 30, 762, 50, 13, 'right', '');
        $pdf->showBoxed('Issue Date', 30, 752, 50, 13, 'right', '');
        $pdf->showBoxed('Property', 30, 742, 50, 13, 'right', '');
        $pdf->showBoxed('Lease', 30, 722, 50, 13, 'right', '');

        $pdf->showBoxed('Owner', 30, 702, 50, 13, 'right', '');
        $pdf->showBoxed('Owner ' . $this->business_label, 30, 692, 50, 13, 'right', '');
    }

    public function render(&$pdf)
    {
        $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        $this->setFont($pdf, 'Helvetica-Bold', 8);
        $pdf->showBoxed($this->title, 25, 776, 260, 13, 'center', '');

        $pdf->showBoxed($this->dueDate, 85, 762, 215, 13, 'left', '');
        $pdf->showBoxed($this->issueDate, 85, 752, 215, 13, 'left', '');
        $pdf->showBoxed($this->propertyID, 85, 742, 215, 13, 'left', '');
        $pdf->showBoxed($this->propertyName, 85, 732, 215, 13, 'left', '');

        $pdf->showBoxed($this->ownerName, 85, 702, 215, 13, 'left', '');
        $pdf->showBoxed(textSpace($this->ownerABN), 85, 692, 215, 13, 'left', '');
    }
}

class InvoiceFooter extends PDFobject
{
    public $dueDate;

    public $issueDate;

    public $propertyID;

    public $propertyName;

    public $leaseID;

    public $leaseName;

    public $ownerName;

    public $ownerABN;

    public $title;

    public $officeName;

    public $officeAddress;

    public $officeCity;

    public $officeState;

    public $officePostCode;

    public $mailingName;

    public $mailingAddress;

    public $mailingCity;

    public $mailingState;

    public $mailingPostCode;

    public $bankAccount;

    public $bankAccountName;

    public $bankBSB;

    public $bankName;

    public $business_label;

    public bool $displayBsb;

    public string $bsbLabel;

    public function __construct($propertyID, $dueDate, $issueDate, $title)
    {
        $this->propertyID = $propertyID;
        $this->issueDate = $issueDate;
        $this->dueDate = $dueDate;
        $this->title = $title;
        // [CIR-2628] - settings for different client based on country
        $this->business_label = $_SESSION['country_default']['business_label'];
        $this->displayBsb = getDisplayBsbFromSession();
        $this->bsbLabel = getBsbLabelFromSession();
    }

    public function preRender(&$pdf)
    {
        $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        $pdf->setlinewidth(0.5);
        // Grey Areas...
        $pdf->setColorExt('both', 'rgb', 0.9, 0.9, 0.9, 0); // Setting the Color to Gray
        $pdf->rect(360, 240, 210, 15);
        $pdf->fill();
        $pdf->rect(25, 240, 300, 15);
        $pdf->fill();
        $pdf->setColorExt('both', 'rgb', 0.95, 0.95, 0.95, 0); // Setting the Color to Gray
        $pdf->rect(360, 130, 210, 70);
        $pdf->fill();
        $pdf->rect(25, 160, 300, 15);
        $pdf->fill();
        $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0); // Setting the Color to Gray
        $pdf->rect(360, 100, 210, 30);
        $pdf->fill();
        // Horizontal Lines...

        $pdf->setColorExt('both', 'rgb', 0.75, 0.75, 0.75, 0);
        $pdf->moveto(25, 175);
        $pdf->lineto(325, 175);
        $pdf->stroke();

        $pdf->moveto(360, 200);
        $pdf->lineto(570, 200);
        $pdf->stroke();
        $pdf->moveto(360, 240);
        $pdf->lineto(570, 240);
        $pdf->stroke();
        $pdf->moveto(25, 240);
        $pdf->lineto(325, 240);
        $pdf->stroke();

        $pdf->setlinewidth(2);
        $pdf->setColorExt('both', 'rgb', 0.75, 0.75, 0.75, 0);
        $pdf->rect(360, 100, 210, 155);
        $pdf->stroke();
        $pdf->rect(25, 160, 300, 95);
        $pdf->stroke();

        // Text
        $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        $this->setFont($pdf, 'Helvetica-Bold', 8);
        $pdf->showBoxed('BANKING DETAILS', 25, 240, 300, 12, 'center', '');
        $pdf->showBoxed('REMITTANCE ADVICE', 360, 240, 210, 12, 'center', '');

        if ($this->displayBsb) {
            $pdf->showBoxed("Account\nBank\n{$this->bsbLabel}\nAccount No", 30, 86, 50, 138, 'right', '');
        } else {
            $pdf->showBoxed("Account\nBank\nAccount No", 30, 86, 50, 138, 'right', '');
        }

        $this->setFont($pdf, 'Helvetica-Oblique', 7);
        $pdf->showBoxed("Due Date\nProperty\n\nOwner", 362, 105, 40, 83, 'right', '');
        $pdf->showBoxed('If paying electronically please quote - ', 30, 160, 250, 13, 'left', '');
    }

    public function render(&$pdf)
    {
        // Text Address...
        $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        $this->setFont($pdf, 'Helvetica', 8);

        $pdf->showBoxed(
            "{$this->officeName}\r\n{$this->officeAddress}\r\n{$this->officeCity}, {$this->officeState} {$this->officePostCode}",
            95,
            50,
            232,
            42,
            'left',
            ''
        );
        $pdf->showBoxed(
            "{$this->mailingName}\n{$this->mailingAddress}\n{$this->mailingCity} {$this->mailingState},  {$this->mailingPostCode}",
            367,
            98,
            200,
            135,
            'left',
            ''
        );
        if ($this->displayBsb) {
            $pdf->showBoxed(
                "{$this->bankAccountName}\n{$this->bankName}\n" . formatWithDelimiter(
                    $this->bankBSB
                ) . "\n" . textSpace(
                    $this->bankAccount
                ),
                90,
                86,
                250,
                138,
                'left',
                ''
            );
        } else {
            $pdf->showBoxed(
                "{$this->bankAccountName}\n{$this->bankName}\n" . textSpace($this->bankAccount),
                90,
                86,
                250,
                138,
                'left',
                ''
            );
        }

        $pdf->showBoxed("{$this->propertyID}", 220, 160, 100, 13, 'right', '');

        $wrap_owner_name = wrapText($this->ownerName, 40);
        $this->setFont($pdf, 'Helvetica', 7);
        $pdf->showBoxed(
            "{$this->dueDate} \n{$this->propertyID}\n{$this->propertyName}\n{$wrap_owner_name}",
            408,
            105,
            415,
            83,
            'left',
            ''
        );
        $this->setFont($pdf, 'Helvetica', 8);

        $pdf->setColorExt('both', 'rgb', 1, 1, 1, 0); // Setting the Color to Gray
        $pdf->showBoxed($this->title, 367, 100, 185, 20, 'left', '');
    }
}


class MailAddressWindow extends PDFobject
{
    public $attention;

    public $name;

    public $street;

    public $city;

    public $state;

    public $postcode;

    public function __construct($propertyID)
    {
        $mailAddress = dbGetPropertyAddress($propertyID);
        $this->name = $mailAddress['mailingName'];
        $this->street = $mailAddress['mailingAddress'];
        $this->city = $mailAddress['mailingCity'];
        $this->state = $mailAddress['mailingState'];
        $this->postcode = $mailAddress['mailingPostCode'];
    }

    public function preRender(&$pdf) {}

    public function render(&$pdf)
    {
        $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        $this->setFont($pdf, 'Helvetica', 8); // This is for the disclaimer information...
        $address = (($this->attention) ? "{$this->attention}\n" : '') . "{$this->name}\n{$this->street}\n{$this->city} {$this->state},  {$this->postcode}";
        $pdf->showBoxed($address, 95, 622, 232, 46, 'left', ''); // This is for the from field
    }
}


class InvoiceLines extends PDFobject
{
    public $title;

    public function __construct($title)
    {
        $this->title = $title;
    }

    public function preRender(&$pdf)
    {
        $pdf->setColorExt('both', 'rgb', 0.9, 0.9, 0.9, 0); // Setting the Color to Gray
        $pdf->rect(25, 546, 545, 15);
        $pdf->fill();
        $pdf->rect(25, 285, 410, 15);
        $pdf->fill();
        $pdf->setlinewidth(0.5);

        $pdf->setColorExt('both', 'rgb', 0.75, 0.75, 0.75, 0);
        $pdf->moveto(25, 546);
        $pdf->lineto(570, 546);
        $pdf->stroke();
        $pdf->moveto(25, 300);
        $pdf->lineto(570, 300);
        $pdf->stroke();


        $pdf->setlinewidth(2);
        $pdf->rect(25, 285, 545, 276);
        $pdf->stroke();
        $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        // Text

        $this->setFont($pdf, 'Helvetica-Bold', 9);
        $pdf->showBoxed('  DATE', 25, 546, 45, 13, 'center', '');
        $pdf->showBoxed('INVOICE #', 85, 546, 45, 13, 'right', '');
        $pdf->showBoxed('PARTICULARS', 140, 546, 300, 13, 'left', '');
        $pdf->showBoxed('NET', 400, 546, 45, 13, 'right', '');
        $pdf->showBoxed($_SESSION['country_default']['tax_label'], 460, 546, 45, 13, 'right', '');
        $pdf->showBoxed('TOTAL', 520, 546, 45, 13, 'right', '');

        $this->setFont($pdf, 'Helvetica', 8);
    }

    public function render(&$pdf)
    {
        $this->setFont($pdf, 'Helvetica-Bold', 9);
        $pdf->showBoxed($this->title, 25, 286, 405, 13, 'right', '');
    }
}


class FoldLines extends PDFObject
{
    public function preRender(&$pdf)
    {
        $pdf->setColorExt('both', 'rgb', 0.8, 0.8, 0.8, 0);
        $pdf->setlinewidth(1);
        $pdf->moveto(0, 566);
        $pdf->lineto(2, 566);
        $pdf->stroke();
        $pdf->moveto(593, 566);
        $pdf->lineto(595, 566);
        $pdf->stroke();
        $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
    }

    public function render(&$pdf) {}
}

class CutOffLine extends PDFObject
{
    public function preRender(&$pdf)
    {
        $pdf->setColorExt('both', 'rgb', 0.6, 0.6, 0.6, 0); // Setting the Color to Black
        $pdf->setlinewidth(0.5);
        $pdf->set_graphics_option('dasharray={2 2} dashphase=0');


        $pdf->moveto(0, 281);
        $pdf->lineto(595, 281);
        $pdf->stroke();


        $this->setFont($pdf, 'ZapfDingbats', 18);
        $pdf->showBoxed('#', 25, 269, 20, 20, 'left', '');

        $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        $this->setFont($pdf, 'Helvetica-Oblique', 6);
        $pdf->showBoxed(
            'Please detach this section and return with your payment:',
            0,
            265,
            595,
            10,
            'center',
            ''
        ); // Date

    }

    public function render(&$pdf) {}
}


class InvoiceNote extends PDFObject
{
    public $note;

    public function __construct($note)
    {
        $this->note = $note;
    }

    public function preRender(&$pdf) {}

    public function render(&$pdf)
    {
        $pdf->setColorExt('both', 'rgb', 0.95, 0.95, 0.95, 0);
        $pdf->setlinewidth(0.5);
        // $pdf->rect(25,563,545,25); $pdf->fill();
        $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        $this->setFont($pdf, 'Helvetica-Oblique', 7);
        $pdf->showBoxed('NOTE: ' . $this->note, 30, 561, 535, 22, 'left', '');
    }
}

/*
class AgentDetails extends PDFObject {

    var $portfolioManagerID;
    var $portfolioManager;
    var $portfolioEmail;

    var $agentName;
    var $agentAddress;
    var $agentState;
    var $agentCity;
    var $agentPostCode;
    var $agentABN;
    var $agentPhone;
    var $agentFax;


    function AgentDetails($propertyID) {
        $this->portfolioManagerID = dbGetPropertyManager($propertyID);
        echo $this->portFolioManagerID;
        $this->portfolioManager = dbGetParam('PORTMGR',$this->portfolioManagerID);
        $this->portfolioEmail = dbGetParam('PORTMEMAIL',$this->portfolioManagerID);
        if ($this->portfolioManager) $this->portfolioManager = ucwords(strtolower($this->portfolioManager));
    }

    function preRender(&$pdf) {

    }

    function render(&$pdf) {
        $pdf->setColorExt("both", "rgb", 0, 0, 0, 0);
    $this->setFont("Helvetica-Bold", 8);
    $output = "{$this->agentAddress}\n{$this->agentCity}, {$this->agentState} {$this->agentPostCode}";
        if ($this->agentPhone) $output .= "\n{$this->agentPhone}";
        if ($this->agentFax) $output .= "\n{$this->agentFax}";
    $output .= "\n\nProperty Manager - {$this->portfolioManager}";
        $output .= "\nEmail - {$this->portfolioEmail}";
        if ($this->agentLicensee) $output .= "\n\n{$this->agentLicensee}";
        if ($this->agentABN) $output .= "\nABN: " . textSpace($this->agentABN);
    $pdf->showBoxed ($output, 300, 600, 270, 120, "right", "");
    }
}
*/


/*************************************************************************************************************************/

class Invoice extends PDFReport
{
    public $lineOffset = 15;

    public $logoFile = false;

    public function __construct(&$dataSource, $logoFile = false)
    {
        parent::__construct($dataSource);
        if ($logoFile) {
            $this->logoFile = $logoFile;
        }
    }

    public function updateLineOffset($offset)
    {
        $this->lineOffset += $offset;
        if ($this->lineOffset >= 245) {
            $this->renderTotal('continued...');
            $this->renderRemittanceTotal('continued...');
            $this->endPage();
            $this->preparePage();
            $this->render();
        }
    }

    public function renderInvoiceRow($date, $invoiceNumber, $description, $netAmount, $gstAmount, $grossAmount)
    {
        $this->pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        $this->setFont('Helvetica-Bold', 8);

        $this->pdf->showBoxed($date, 20, 546 - $this->lineOffset, 50, 13, 'right', '');
        $this->pdf->showBoxed($invoiceNumber, 85, 546 - $this->lineOffset, 45, 13, 'center', '');
        $this->pdf->showBoxed($description, 140, 546 - $this->lineOffset, 300, 13, 'left', '');
        $this->pdf->showBoxed($netAmount, 395, 546 - $this->lineOffset, 50, 13, 'right', '');
        $this->pdf->showBoxed($gstAmount, 455, 546 - $this->lineOffset, 50, 13, 'right', '');
        $this->pdf->showBoxed($grossAmount, 515, 546 - $this->lineOffset, 50, 13, 'right', '');

        $this->updateLineOffset(10);
    }

    public function renderTotal($amount)
    {
        $this->pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        $this->setFont('Helvetica-Bold', 8);

        if (is_numeric($amount)) {
            $this->pdf->showBoxed(toMoney($amount), 25, 285, 540, 13, 'right', '');
        } else {
            $this->pdf->showBoxed($amount, 25, 285, 540, 13, 'right', '');
        }
    }

    public function renderRemittanceTotal($amount)
    {
        $this->pdf->setColorExt('both', 'rgb', 1, 1, 1, 0);
        $this->setFont('Helvetica-Bold', 9);
        if (is_numeric($amount)) {
            $this->pdf->showBoxed(toMoney($amount), 380, 100, 185, 20, 'right', '');
        } else {
            $this->pdf->showBoxed($amount, 380, 100, 185, 20, 'right', '');
        }

        $this->setFont('Helvetica', 8);
    }

    public function renderTitle($title)
    {
        $this->pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        $this->setFont('Helvetica-Bold', 8);
        $this->pdf->showBoxed($title, 30, 546 - $this->lineOffset, 400, 13, 'left', '');
        $this->updateLineOffset(20);
    }

    public function renderSubTotal($title, $netAmount, $gstAmount, $grossAmount)
    {
        $this->pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        $this->pdf->setlinewidth(0.5);

        if ($netAmount) {
            $this->pdf->moveto(400, 559 - $this->lineOffset);
            $this->pdf->lineto(445, 559 - $this->lineOffset);
            $this->pdf->stroke();
        }

        if ($gstAmount) {
            $this->pdf->moveto(460, 559 - $this->lineOffset);
            $this->pdf->lineto(505, 559 - $this->lineOffset);
            $this->pdf->stroke();
        }

        if ($grossAmount) {
            $this->pdf->moveto(520, 559 - $this->lineOffset);
            $this->pdf->lineto(565, 559 - $this->lineOffset);
            $this->pdf->stroke();
        }

        $this->setFont('Helvetica-Bold', 8);
        $this->pdf->showBoxed($netAmount, 395, 546 - $this->lineOffset, 50, 13, 'right', '');
        $this->pdf->showBoxed($gstAmount, 455, 546 - $this->lineOffset, 50, 13, 'right', '');
        $this->pdf->showBoxed($grossAmount, 515, 546 - $this->lineOffset, 50, 13, 'right', '');

        if ($title) {
            $this->setFont('Helvetica-Bold', 8);
            $this->pdf->showBoxed($title, 140, 546 - $this->lineOffset, 300, 13, 'left', '');
            $this->setFont('Helvetica', 8);
        }

        $this->updateLineOffset(10);
    }

    public function renderLogo()
    {
        if ($this->logoFile) {
            $maxWidth = LOGO_WIDTH;
            $maxHeight = LOGO_HEIGHT;
            [$imageWidth, $imageHeight] = getimagesize($this->logoFile);

            $horizontalScaling = ($imageWidth > $maxWidth) ? $maxWidth / $imageWidth : 1;
            $verticalScaling = ($imageHeight > $maxHeight) ? $maxHeight / $imageHeight : 1;
            $imageScale = ($horizontalScaling < $verticalScaling) ? $horizontalScaling : $verticalScaling;

            $imageScale = round($imageScale, 2);

            $vMargin = 25;
            $hMargin = 25;

            $hPos = 595 - ($imageScale * $imageWidth) - $hMargin;
            $vPos = 842 - ($imageScale * $imageHeight) - $vMargin;

            $pdfimage = $this->pdf->load_image('auto', $this->logoFile, '');
            $this->pdf->fit_image($pdfimage, $hPos, $vPos, "scale {$imageScale}");
            $this->pdf->close_image($pdfimage);
        }
    }

    public function preparePage($printTemplate = true)
    {
        $this->lineOffset = 15;
        parent::preparePage();
        $this->render();
        $this->renderLogo();
    }

    public function close()
    {
        parent::close();
    }
}

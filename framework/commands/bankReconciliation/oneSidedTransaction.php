<?php

/**
 * <AUTHOR>
 *
 * @since 2013-01-08
 *
 * @modified 2017-11-23: Modified foreach of existingCheque to fix bug
 **/
function oneSidedTransaction(&$context)
{
    // Page Template
    if (! isPostback()) {
        $view = new MasterPage(userViews(), '/bankReconciliation/oneSidedTransaction.html');
        $view->setSection($context['module']);
    } else {
        $view = new UserControl(userViews(), '/bankReconciliation/oneSidedTransaction.html');
    }
    $view->bindAttributesFrom($_REQUEST);

    // Array Call-In
    $view->items['bankList'] = dbGetBankAccounts();
    $view->items['transactionTypeList'] =
    [
        'Cheque' => 'Cheque',
        'Deposit' => 'Deposit',
        'EFT' => 'EFT',
    ];
    $view->items['debtorList'] = dbCompanyList(DEBTOR);
    $view->items['supplierList'] = dbCompanyList(SUPPLIER);

    $bankID = $view->items['bankID'];
    $transactionType = $view->items['transactionType'];

    // Action
    switch ($view->items['action']) {
        case 'finalise':
            // Validation
            if (empty($bankID) or ! isValid($bankID, TEXT_INT, false)) {
                $validationErrors[] = 'You need to select a ' . ucwords(strtolower($_SESSION['country_default']['trust_account'])) . '.';
            }
            if (empty($transactionType) or ! array_key_exists($transactionType, $view->items['transactionTypeList'])) {
                $validationErrors[] = 'You need to select a Transaction Type.';
            }
            if (empty($view->items['lastDate']) or $view->items['lastDate'] and ! isValid($view->items['lastDate'], TEXT_SMARTDATE, false)) {
                $validationErrors[] = 'The date you entered is invalid (dd/mm/yyyy).';
            }
            if (empty($view->items['amount']) or ! isValid($view->items['amount'], TEXT_FLOAT, false)) {
                $validationErrors[] = 'You need to enter a valid Amount.';
            }
            if ($transactionType == 'EFT') {
                if (empty($view->items['referenceNumber']) or ! isValid($view->items['referenceNumber'], TEXT_KEY, false)) {
                    $validationErrors[] = 'You need to enter a valid Reference Number.';
                }
            } else {
                if (empty($view->items['referenceNumber']) or ! isValid($view->items['referenceNumber'], TEXT_INT, false)) {
                    $validationErrors[] = 'You need to enter a valid Reference Number.';
                }
            }

            if ($transactionType == 'Cheque' and empty($view->items['creditorID'])) {
                $validationErrors[] = 'You need to select a valid Creditor.';
                $view->items['debtorID'] = null;
            } elseif ($transactionType == 'Deposit' and empty($view->items['debtorID'])) {
                $validationErrors[] = 'You need to select a valid Debtor.';
                $view->items['creditorID'] = null;
            } else {
                // $view->items['creditorID'] = $view->items['debtorID'] = null;
            }
            // No Errors
            if (noErrors($validationErrors)) {
                switch ($transactionType) {
                    case 'Cheque':
                        $existingCheque = dbCheckUnusedCheque($bankID, $view->items['referenceNumber']);
                        foreach ($existingCheque as $index => $data) {
                            if ($data['chequeAmount'] != 0) {
                                $available = 'no';
                            }
                            $chequeReference = $data['chequeReference'];
                            $chequeCount++;
                        }
                        if (empty($chequeCount)) {
                            $validationErrors[] = 'Unable to add cheque because there is no cheque with that number for ' . strtolower($_SESSION['country_default']['trust_account']) . ": $bankID in the database.";
                        } elseif ($available == 'no') {
                            $validationErrors[] = 'Unable to add cheque because the cheque number you used is already been used.';
                        } else {
                            dbUpdateOneSidedCheque($bankID, $view->items['lastDate'], $view->items['amount'], $view->items['referenceNumber'], $view->items['creditorID'], intval($chequeReference));
                            $view->items['statusMessage'] = 'The Cheque you have entered has been added.';
                        }
                        break;
                    case 'Deposit':
                        $bankReference = dbGetNextBankReference($transactionDate, $bankID);
                        if (empty($bankReference)) {
                            $bankReference = 0;
                        }
                        dbInsertOneSidedDeposit($bankID, $view->items['lastDate'], $view->items['amount'], $bankReference, $view->items['debtorID']);
                        $view->items['statusMessage'] = 'The Deposit you have entered has been added.';
                        break;
                    case 'EFT':
                        $sqlResult = dbCheckExistingEFT($bankID, $view->items['referenceNumber']);
                        if ($sqlResult) {
                            $validationErrors[] = 'Unable to add EFT because the EFT number you used is already been used.';
                        } else {
                            dbInsertOneSidedEFT($bankID, $view->items['lastDate'], $view->items['amount'], $view->items['referenceNumber']);
                            $view->items['statusMessage'] = 'The EFT you have entered has been added.';
                        }
                        break;
                }
            }
            break;
    }

    // Post Feed
    $view->items['validationErrors'] = $validationErrors;

    // Display
    $view->render();
}

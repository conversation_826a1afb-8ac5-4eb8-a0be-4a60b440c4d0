<?php

include 'lib/ngForm.php';
include 'lib/htmler.php';

function bankStatementInputBeta(&$context)
{

    if (! isPostback()) {
        $view = new MasterPage(userViews(), '/bankReconciliation/bankStatementInputBeta.html');
        $view->setSection($context['module']);
    } else {
        $view = new UserControl(userViews(), '/bankReconciliation/bankStatementInputbeta.html');
    }
    $view->bindAttributesFrom($_REQUEST);
    $view->render();
}

<?php

use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Fill;

if ($fileFormat == FILETYPE_PDF) {
    $page = 0;
    $pdf->begin_page_ext(842, 595, '');
    bank_balance_header('new', $trustAccCode, $trustAccName);
    $line = 480;
}

if ($fileFormat == FILETYPE_XLS) {
    $report->resetColumns();
    $report->line = 1;
    $report->enableFormatting = true;
    $indexSheet++;
    $report->setSheetDetails($indexSheet, 'Bank Balance');

    $numberFormat = '_(* #,##0.00_);_(* (#,##0.00);_(* "-"??_);_(@_)';
    $report->enableFormatting = true;

    $topHeaderStyle = [
        'fill' => [
            'type' => Fill::FILL_SOLID,
            'color' => ['rgb' => '004c7a'],
        ],
        'font' => ['bold' => true, 'color' => ['rgb' => 'ffffff']],
        'alignment' => [
            'vertical' => Alignment::VERTICAL_CENTER,
            'horizontal' => Alignment::HORIZONTAL_LEFT,
        ],
    ];

    $report->addColumn('property', 'Property', 50, 'left', '@', $topHeaderStyle);
    $report->addColumn('pname', 'Property Address', 150, 'left', '@', $topHeaderStyle);
    $report->addColumn(
        'opening',
        $view->items['from_date'] . ' Opening Balance',
        50,
        'right',
        $numberFormat,
        $topHeaderStyle
    );
    $report->addColumn('receipts', 'Receipts', 50, 'right', $numberFormat, $topHeaderStyle);
    $report->addColumn('payments', 'Payments', 50, 'right', $numberFormat, $topHeaderStyle);
    $report->addColumn(
        'closing',
        $view->items['to_date'] . ' Closing Balance',
        50,
        'right',
        $numberFormat,
        $topHeaderStyle
    );
    $report->addColumn('uncleared', 'Less Uncleared Funds', 70, 'right', $numberFormat, $topHeaderStyle);
    $report->addColumn('period1', '1 day', 55, 'right', $numberFormat, $topHeaderStyle);
    $report->addColumn('period2', '2 days', 55, 'right', $numberFormat, $topHeaderStyle);
    $report->addColumn('period3', '3 days', 55, 'right', $numberFormat, $topHeaderStyle);
    $report->addColumn('period4', '+4 days', 55, 'right', $numberFormat, $topHeaderStyle);
    $report->addColumn('cleared', 'Cleared Funds Balance', 70, 'right', $numberFormat, $topHeaderStyle);
    $report->renderHeader();

    $sheet_ind = $c = 0;
    foreach ($report->columns as $cols) {
        $c++;
        $report->setColumnWidth(numberToLetter($c), 'auto');
    }
}


// BANK BALANCE START
$periodTo = $view->items['to_date'];
[$cur_day, $cur_month, $cur_year] = explode('/', $periodTo);
$current_date = gregoriantojd($cur_month, $cur_day, $cur_year);

$fdate = $view->items['from_date'];

$total_opening = 0;
$total_receipts = 0;
$total_payments = 0;
$total_current_balance = 0;
$total_uncleared_funds = 0;
$total_1_day = 0;
$total_2_day = 0;
$total_3_day = 0;
$total_4_day = 0;
$total_available = 0;

$line = 482;
$prop_name = dbGetProperties('', $trustAccCode, '', 1);
foreach ($prop_name as $pn) {
    $pname = $pn['propertyName'];
    $propertyID = $pn['propertyID'];

    $receipts = dbgGetReceipts($propertyID, $fdate);

    $opening_balance = dbGetOpeningBalance($propertyID, $fdate) - $receipts;

    $payments = dbGetAllocPayments($propertyID, $fdate, $periodTo);

    $receipts = dbGetAllocReceipts($propertyID, $fdate, $periodTo);

    $current_balance = $opening_balance + $payments + $receipts;

    $cur_day_of_week = date('w', mktime(0, 0, 0, $cur_month, $cur_day, $cur_year));

    $clearance_date = date('d/m/Y', mktime(0, 0, 0, $cur_month, $cur_day - 6, $cur_year));

    // UNCLEARED FUNDS
    $uncleared_funds = 0;
    $uncleared_funds_1 = 0;
    $uncleared_funds_2 = 0;
    $uncleared_funds_3 = 0;
    $uncleared_funds_4 = 0;

    $unclearfund = dbGetUnclearedWithAccount($propertyID, $clearance_date, $periodTo);

    foreach ($unclearfund as $row) {
        $alloc_amt = $row['allocationAmount'];
        $reference = $row['payment'];
        $alloc_date = $row['allocationDate'];
        [$a_day, $a_month, $a_year] = explode('/', $alloc_date);

        $al_date = gregoriantojd($a_month, $a_day, $a_year);
        $t_amount = $alloc_amt * (-1);

        $f_batch = $row['pmxd_f_batch'];
        $t_batch = $row['pmxd_t_batch'];
        $f_line_batch = $row['pmxd_f_line'];
        $t_line_batch = $row['pmxd_t_line'];
        $invoice_reference = $row['invoice'];

        $check_dishonoured_result = dbGetCheckDishonoured(
            $t_batch,
            $t_line_batch,
            $t_amount,
            $invoice_reference,
            $reference,
            $periodTo
        );

        if (! $check_dishonoured_result && $row['clearanceDate'] != '') {
            $multi_amt = dbGetAllocAmount($f_batch, $f_line_batch);
            $UCmulti_amt = dbGetUnAllocAmount($f_batch, $f_line_batch, $multi_amt);

            if (! $UCmulti_amt) {
                $uncleared_funds = $uncleared_funds + $alloc_amt;

                $date_diff = 0;
                [$c_day, $c_month, $c_year] = explode('/', $row['clearanceDate']);
                $clear_date = strtotime($c_year . '-' . $c_month . '-' . $c_day);
                [$cur_day, $cur_month, $cur_year] = explode('/', $periodTo);
                $curr_date = strtotime($cur_year . '-' . $cur_month . '-' . $cur_day);
                $date_diff = abs(round(($curr_date - $clear_date) / (60 * 60 * 24)));
                // CHECK FOR PUBLIC HOLIDAYS
                $future_date = date('Ymd', mktime(0, 0, 0, $a_month, $a_day + $no_of_days, $a_year));
                // al_date is still julian so convert to YMD so we can select
                $allocated_date = date('Ymd', mktime(0, 0, 0, $a_month, $a_day, $a_year));
                $pubhol_result = dbGetPublicHol($allocated_date, $future_date);
                // print_r($pubhol_result);
                $date_diff = $date_diff + count($pubhol_result ?? []);

                if ($date_diff == 1) {
                    $uncleared_funds_1 = $uncleared_funds_1 + $alloc_amt;
                }
                if ($date_diff == 2) {
                    $uncleared_funds_2 = $uncleared_funds_2 + $alloc_amt;
                }
                if ($date_diff == 3) {
                    $uncleared_funds_3 = $uncleared_funds_3 + $alloc_amt;
                }
                if ($date_diff >= 4) {
                    $uncleared_funds_4 = $uncleared_funds_4 + $alloc_amt;
                }
            }
        }
    }

    $UnAllocunclearfund = dbGetUnAllocUnclearedFund($propertyID, $clearance_date, $periodTo);
    foreach ($UnAllocunclearfund as $row) {
        $alloc_amt = (empty($row['amount']) ? 0 : $row['amount']);
        $batch = (empty($row['pmuc_batch']) ? 0 : $row['pmuc_batch']);
        $batch_line = (empty($row['pmuc_line']) ? 0 : $row['pmuc_line']);
        $reference = (empty($row['ref_1']) ? 0 : $row['ref_1']);
        $alloc_date = $row['alloc_date'];

        $clearanceDays = $row['clearancedays'];

        [$a_month, $a_day, $a_year] = explode('/', $alloc_date);
        $al_date = gregoriantojd($a_month, $a_day, $a_year);

        if (in_array($row['payment_type'], ['D', 'Q'])) {
            $alloc_amt_neg = $alloc_amt * (-1);
            $check_dishonoured_result = dbGetUnAllocCheckDishonoured(
                $batch,
                $batch_line,
                $alloc_amt_neg,
                $reference,
                $periodTo
            );

            if (! $check_dishonoured_result) {
                $alloc_day_of_week = date('w', mktime(0, 0, 0, $a_month, $a_day, $a_year));

                switch ($alloc_day_of_week) {
                    case 0:
                        $no_of_days = 4;
                        break;

                    case 1:
                        $no_of_days = 4;
                        break;

                    case 6:
                        $no_of_days = 5;
                        break;

                    default:
                        $no_of_days = 6;
                        break;
                }

                $uncleared_funds = $uncleared_funds + ($alloc_amt);
                $date_diff = $al_date + $no_of_days - $current_date;

                $adj_date = 0;

                for ($a = 0; $a <= $date_diff; $a++) {
                    $future_day_of_the_week = date('w', mktime(0, 0, 0, $cur_month, $cur_day + $a, $cur_year));
                    if ($future_day_of_the_week == 6 || $future_day_of_the_week == 0) {
                        $adj_date++;
                    }
                }

                $date_diff = $date_diff - $adj_date;

                if ($date_diff == 1) {
                    $uncleared_funds_1 = $uncleared_funds_1 + $alloc_amt;
                }
                if ($date_diff == 2) {
                    $uncleared_funds_2 = $uncleared_funds_2 + $alloc_amt;
                }
                if ($date_diff == 3) {
                    $uncleared_funds_3 = $uncleared_funds_3 + $alloc_amt;
                }
                if ($date_diff == 4) {
                    $uncleared_funds_4 = $uncleared_funds_4 + $alloc_amt;
                }
            }
        }
    }

    $current_cleared_balance = $current_balance + $uncleared_funds;

    $payments_display = ($payments);
    $receipts_display = ($receipts);
    $opening_balance_display = ($opening_balance);
    $current_balance_display = ($current_balance);
    $uncleared_funds_display = ($uncleared_funds);
    $uncleared_funds_1_display = ($uncleared_funds_1);
    $uncleared_funds_2_display = ($uncleared_funds_2);
    $uncleared_funds_3_display = ($uncleared_funds_3);
    $uncleared_funds_4_display = ($uncleared_funds_4);
    $current_cleared_balance_display = ($current_cleared_balance);

    $total_opening = $total_opening + $opening_balance;
    $total_receipts = $total_receipts + $receipts;
    $total_payments = $total_payments + $payments;
    $total_current_balance = $total_current_balance + $current_balance;
    $total_uncleared_funds = $total_uncleared_funds + $uncleared_funds;
    $total_1_day = $total_1_day + $uncleared_funds_1;
    $total_2_day = $total_2_day + $uncleared_funds_2;
    $total_3_day = $total_3_day + $uncleared_funds_3;
    $total_4_day = $total_4_day + $uncleared_funds_4;
    $total_available = $total_available + $current_cleared_balance;

    $data['property'] = $propertyID;
    $data['pname'] = $pname;
    $data['opening'] = $opening_balance_display;
    $data['receipts'] = $receipts_display;
    $data['payments'] = $payments_display;
    $data['closing'] = $current_balance_display;

    $data['uncleared'] = $uncleared_funds_display;
    $data['period1'] = $uncleared_funds_1_display;
    $data['period2'] = $uncleared_funds_2_display;
    $data['period3'] = $uncleared_funds_3_display;
    $data['period4'] = $uncleared_funds_4_display;
    $data['cleared'] = $current_cleared_balance_display;

    $view->items['data'][] = $data;

    if ($fileFormat == FILETYPE_PDF) {
        if ($line <= 70) {
            $pdf->setColorExt('both', 'rgb', 0.75, 0.75, 0.75, 0);
            $pdf->setlinewidth(0.5);

            $pdf->moveto(5, 495);
            $pdf->lineto(5, $line + 9);
            $pdf->stroke();

            $pdf->moveto(61, 495);
            $pdf->lineto(61, $line + 9);
            $pdf->stroke();

            $pdf->moveto(217, 495);
            $pdf->lineto(217, $line + 9);
            $pdf->stroke();

            $pdf->moveto(273, 495);
            $pdf->lineto(273, $line + 9);
            $pdf->stroke();

            $pdf->moveto(329, 495);
            $pdf->lineto(329, $line + 9);
            $pdf->stroke();

            $pdf->moveto(385, 495);
            $pdf->lineto(385, $line + 9);
            $pdf->stroke();

            $pdf->moveto(441, 495);
            $pdf->lineto(441, $line + 9);
            $pdf->stroke();

            $pdf->moveto(517, 495);
            $pdf->lineto(517, $line + 9);
            $pdf->stroke();

            $pdf->moveto(578, 495);
            $pdf->lineto(578, $line + 9);
            $pdf->stroke();

            $pdf->moveto(639, 495);
            $pdf->lineto(639, $line + 9);
            $pdf->stroke();

            $pdf->moveto(700, 495);
            $pdf->lineto(700, $line + 9);
            $pdf->stroke();

            $pdf->moveto(761, 495);
            $pdf->lineto(761, $line + 9);
            $pdf->stroke();

            $pdf->moveto(837, 495);
            $pdf->lineto(837, $line + 9);
            $pdf->stroke();

            $pdf->moveto(5, $line + 9);
            $pdf->lineto(837, $line + 9);
            $pdf->stroke();

            $pdf->setColorExt('both', 'rgb', 0.3, 0.3, 0.3, 0);
            $pdf->setFontExt($_fonts['Helvetica-Bold'], 5);
            $pdf->showBoxed('Page ' . $page, 755, 5, 75, 8, 'right', '');

            $traccFooter = new TraccFooter(
                'assets/clientLogos/tracc_logo_footer.jpg',
                'bank balance report',
                A4_LANDSCAPE
            );
            $traccFooter->prerender($pdf);


            $pdf->end_page_ext('');
            $pdf->begin_page_ext(842, 595, '');
            bank_balance_header('cont', $trustAccCode, $trustAccName);

            $line = 482;
        }

        $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        $pdf->setFontExt($_fonts['Helvetica'], 7);
        $pdf->showBoxed($data['property'], 8, $line, 50, 10, 'left', '');
        $pdf->showBoxed($data['pname'], 64, $line, 150, 10, 'left', '');
        $pdf->showBoxed(formatting($data['opening']), 220, $line, 50, 10, 'right', '');
        $pdf->showBoxed(formatting($data['receipts']), 276, $line, 50, 10, 'right', '');
        $pdf->showBoxed(formatting($data['payments']), 332, $line, 50, 10, 'right', '');
        $pdf->showBoxed(formatting($data['closing']), 388, $line, 50, 10, 'right', '');
        $pdf->showBoxed(formatting($data['uncleared']), 444, $line, 70, 10, 'right', '');
        $pdf->showBoxed(formatting($data['period1']), 520, $line, 55, 10, 'right', '');
        $pdf->showBoxed(formatting($data['period2']), 581, $line, 55, 10, 'right', '');
        $pdf->showBoxed(formatting($data['period3']), 642, $line, 55, 10, 'right', '');
        $pdf->showBoxed(formatting($data['period4']), 703, $line, 55, 10, 'right', '');
        $pdf->showBoxed(formatting($data['cleared']), 764, $line, 70, 10, 'right', '');
        $line -= 13;
    } else {
        $report->renderLine_custom($data);
    }
}

$totals['property'] = 'TOTAL';
$totals['opening'] = $total_opening;
$totals['receipts'] = $total_receipts;
$totals['payments'] = $total_payments;
$totals['closing'] = $total_current_balance;

$totals['uncleared'] = $total_uncleared_funds;
$totals['period1'] = $total_1_day;
$totals['period2'] = $total_2_day;
$totals['period3'] = $total_3_day;
$totals['period4'] = $total_4_day;
$totals['cleared'] = $total_available;

$view->items['total'] = $totals;

if ($fileFormat == FILETYPE_PDF) {
    if ($line <= 70) {
        $pdf->setColorExt('both', 'rgb', 0.75, 0.75, 0.75, 0);
        $pdf->setlinewidth(0.5);

        $pdf->moveto(5, 495);
        $pdf->lineto(5, $line + 9);
        $pdf->stroke();

        $pdf->moveto(61, 495);
        $pdf->lineto(61, $line + 9);
        $pdf->stroke();

        $pdf->moveto(217, 495);
        $pdf->lineto(217, $line + 9);
        $pdf->stroke();

        $pdf->moveto(273, 495);
        $pdf->lineto(273, $line + 9);
        $pdf->stroke();

        $pdf->moveto(329, 495);
        $pdf->lineto(329, $line + 9);
        $pdf->stroke();

        $pdf->moveto(385, 495);
        $pdf->lineto(385, $line + 9);
        $pdf->stroke();

        $pdf->moveto(441, 495);
        $pdf->lineto(441, $line + 9);
        $pdf->stroke();

        $pdf->moveto(517, 495);
        $pdf->lineto(517, $line + 9);
        $pdf->stroke();

        $pdf->moveto(578, 495);
        $pdf->lineto(578, $line + 9);
        $pdf->stroke();

        $pdf->moveto(639, 495);
        $pdf->lineto(639, $line + 9);
        $pdf->stroke();

        $pdf->moveto(700, 495);
        $pdf->lineto(700, $line + 9);
        $pdf->stroke();

        $pdf->moveto(761, 495);
        $pdf->lineto(761, $line + 9);
        $pdf->stroke();

        $pdf->moveto(837, 495);
        $pdf->lineto(837, $line + 9);
        $pdf->stroke();

        $pdf->moveto(5, $line + 9);
        $pdf->lineto(837, $line + 9);
        $pdf->stroke();

        $pdf->setColorExt('both', 'rgb', 0.3, 0.3, 0.3, 0);
        $pdf->setFontExt($_fonts['Helvetica-Bold'], 5);
        $pdf->showBoxed('Page ' . $page, 755, 5, 75, 8, 'right', '');

        $traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'bank balance report', A4_LANDSCAPE);
        $traccFooter->prerender($pdf);


        $pdf->end_page_ext('');
        $pdf->begin_page_ext(842, 595, '');
        bank_balance_header('cont', $trustAccCode, $trustAccName);

        $line = 482;
    }


    $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
    $pdf->setFontExt($_fonts['Helvetica-Bold'], 7);
    $pdf->showBoxed($totals['property'], 8, $line, 50, 10, 'left', '');

    $pdf->showBoxed(formatting($totals['opening']), 220, $line, 50, 10, 'right', '');
    $pdf->showBoxed(formatting($totals['receipts']), 276, $line, 50, 10, 'right', '');
    $pdf->showBoxed(formatting($totals['payments']), 332, $line, 50, 10, 'right', '');
    $pdf->showBoxed(formatting($totals['closing']), 388, $line, 50, 10, 'right', '');
    $pdf->showBoxed(formatting($totals['uncleared']), 444, $line, 70, 10, 'right', '');
    $pdf->showBoxed(formatting($totals['period1']), 520, $line, 55, 10, 'right', '');
    $pdf->showBoxed(formatting($totals['period2']), 581, $line, 55, 10, 'right', '');
    $pdf->showBoxed(formatting($totals['period3']), 642, $line, 55, 10, 'right', '');
    $pdf->showBoxed(formatting($totals['period4']), 703, $line, 55, 10, 'right', '');
    $pdf->showBoxed(formatting($totals['cleared']), 764, $line, 70, 10, 'right', '');
} else {
    $report->setColumnWidth('C', '18');
    $report->setColumnWidth('F', '20');
    $report->setColumnWidth('G', '25');
    $report->setColumnWidth('L', '23');
    $report->renderTotal($totals, 'TOTAL');
}
// end for bank balance data

if ($fileFormat == FILETYPE_PDF) {
    $pdf->setColorExt('both', 'rgb', 0.75, 0.75, 0.75, 0);
    $pdf->setlinewidth(0.5);

    $pdf->moveto(5, 495);
    $pdf->lineto(5, $line);
    $pdf->stroke();

    $pdf->moveto(61, 495);
    $pdf->lineto(61, $line);
    $pdf->stroke();

    $pdf->moveto(217, 495);
    $pdf->lineto(217, $line);
    $pdf->stroke();

    $pdf->moveto(273, 495);
    $pdf->lineto(273, $line);
    $pdf->stroke();

    $pdf->moveto(329, 495);
    $pdf->lineto(329, $line);
    $pdf->stroke();

    $pdf->moveto(385, 495);
    $pdf->lineto(385, $line);
    $pdf->stroke();

    $pdf->moveto(441, 495);
    $pdf->lineto(441, $line);
    $pdf->stroke();

    $pdf->moveto(517, 495);
    $pdf->lineto(517, $line);
    $pdf->stroke();

    $pdf->moveto(578, 495);
    $pdf->lineto(578, $line);
    $pdf->stroke();

    $pdf->moveto(639, 495);
    $pdf->lineto(639, $line);
    $pdf->stroke();

    $pdf->moveto(700, 495);
    $pdf->lineto(700, $line);
    $pdf->stroke();

    $pdf->moveto(761, 495);
    $pdf->lineto(761, $line);
    $pdf->stroke();

    $pdf->moveto(837, 495);
    $pdf->lineto(837, $line);
    $pdf->stroke();

    $pdf->moveto(5, $line);
    $pdf->lineto(837, $line);
    $pdf->stroke();

    $pdf->setColorExt('both', 'rgb', 0.3, 0.3, 0.3, 0);
    $pdf->setFontExt($_fonts['Helvetica-Bold'], 5);
    $pdf->showBoxed('Page ' . $page, 755, 5, 75, 8, 'right', '');

    $traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'bank balance report', A4_LANDSCAPE);
    $traccFooter->prerender($pdf);

    $pdf->end_page_ext('');
}

function bank_balance_header($cont, $trustAccCode, $trustAccName)
{
    global $pdf;
    global $propertyName;
    global $line;
    global $description;
    global $periodDescription;
    global $client;
    global $page;
    global $periodFrom;
    global $periodTo;
    global $date;
    global $logo;
    global $_fonts;


    $offset = 16;
    $pdf->setFontExt($_fonts['Helvetica'], 12);
    $pdf->setColorExt('both', 'rgb', 0, 0.3, 0.5, 0);
    $pdf->showBoxed('Bank Balances', 6, 575, 550, 16, 'left', '');


    $pdf->setColorExt('both', 'rgb', 0.4, 0.4, 0.4, 0);
    $pdf->setFontExt($_fonts['Helvetica-Bold'], 10);
    $pdf->showBoxed('Bank Account: ' . $trustAccName . " ($trustAccCode)", 6, 560, 550, 12, 'left', '');

    $_fonts['Helvetica-Oblique'] = $pdf->load_font('Helvetica-Oblique', 'host', '');
    $pdf->setColorExt('both', 'rgb', 0.3, 0.3, 0.3, 0);
    $pdf->setFontExt($_fonts['Helvetica-Oblique'], 8);
    $pdf->showBoxed("as at $periodTo", 6, 547, 550, 12, 'left', '');


    $page++;

    $pdf->setColorExt('both', 'rgb', 0, 0.3, 0.5, 0);
    $pdf->rect(5, 495, 832, 13);
    $pdf->fill();

    $pdf->setColorExt('both', 'rgb', 1, 1, 1, 0);
    $pdf->setFontExt($_fonts['Helvetica-Bold'], 7);
    $pdf->showBoxed('Property', 8, 495, 50, 10, 'left', '');
    $pdf->showBoxed('Property Address', 64, 495, 150, 10, 'left', '');
    $pdf->setFontExt($_fonts['Helvetica-Bold'], 5);
    $pdf->showBoxed($periodFrom . " Opening\nBalance", 220, 495, 50, 12, 'right', '');
    $pdf->setFontExt($_fonts['Helvetica-Bold'], 7);
    $pdf->showBoxed('Receipts', 276, 495, 50, 10, 'right', '');
    $pdf->showBoxed('Payments', 332, 495, 50, 10, 'right', '');
    $pdf->setFontExt($_fonts['Helvetica-Bold'], 5);
    $pdf->showBoxed($periodTo . " Closing\nBalance", 388, 495, 50, 12, 'right', '');
    $pdf->showBoxed("Less Uncleared\nFunds", 444, 495, 70, 12, 'right', '');
    $pdf->setFontExt($_fonts['Helvetica-Bold'], 7);
    $pdf->showBoxed('1 day', 520, 495, 55, 10, 'right', '');
    $pdf->showBoxed('2 days', 581, 495, 55, 10, 'right', '');
    $pdf->showBoxed('3 days', 642, 495, 55, 10, 'right', '');
    $pdf->showBoxed('+4 days', 703, 495, 55, 10, 'right', '');
    $pdf->setFontExt($_fonts['Helvetica-Bold'], 5);
    $pdf->showBoxed("Cleared Funds\nBalance", 764, 495, 70, 12, 'right', '');


    // START LOGO
    $logoFile = dbGetClientLogo();
    $logoPath = realpath("assets/clientLogos/{$logoFile}");

    if ($logoPath and file_exists($logoPath)) {
        $maxWidth = LOGO_WIDTH;
        $maxHeight = LOGO_HEIGHT;
        [$imageWidth, $imageHeight] = getimagesize($logoPath);

        $horizontalScaling = ($imageWidth > $maxWidth) ? $maxWidth / $imageWidth : 1;
        $verticalScaling = ($imageHeight > $maxHeight) ? $maxHeight / $imageHeight : 1;
        $imageScale = ($horizontalScaling < $verticalScaling) ? $horizontalScaling : $verticalScaling;

        $imageScale = round($imageScale, 2);

        $hPos = 842 - ($imageScale * $imageWidth) - 5;
        $vPos = 595 - ($imageScale * $imageHeight) - 5;


        $pdfimage = $pdf->load_image('auto', $logoPath, '');
        $pdf->fit_image($pdfimage, $hPos, $vPos, "scale {$imageScale}");
    }
    // END LOGO


    $pdf->setFontExt($_fonts['Helvetica'], 8);
    $pdf->showBoxed("Page {$page}", 750, 0, 75, 30, 'right', '');

    $line = 480;
}

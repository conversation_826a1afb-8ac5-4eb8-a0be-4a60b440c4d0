<?php

include BASEPATH . '/lib/ngForm.php';
include_once BASEPATH . '/lib/reportLib/reportIncludesAll.php';
include BASEPATH . '/lib/reportLib/commonReportFunctions.php';

function convert_YYYYMMDD_to_DDMMYYYY($some_date)
{
    $day = substr($some_date, 0, 2);
    $month = substr($some_date, 2, 2);
    $year = substr($some_date, 4, 4);
    $temp_date = $day . '/' . $month . '/' . $year;

    return $temp_date;
}

function getSortTimeStamp($some_date)
{
    $tempDate = $some_date;
    $day = substr($some_date, 0, 2);
    $month = substr($some_date, 2, 2);
    $year = substr($some_date, 4, 4);
    $tempDate = "$year$month$day";

    return $tempDate;
}

function oldBankRecReports(&$context)
{
    global $pathPrefix, $clientDirectory, $globalDownloadLink;

    $userpermission = new userpermission();
    $result = $userpermission->userHasPageAccess(__FUNCTION__);

    // Page Template
    if (! isPostback()) {
        $view = new MasterPage(userViews(), '/bankReconciliation/oldBankRecReports.html');
        $view->setSection($context['module']);
    } else {
        $view = new UserControl(userViews(), '/bankReconciliation/oldBankRecReports.html');
    }
    $view->bindAttributesFrom($_REQUEST);

    $bank_recon_download_link = "$globalDownloadLink/pdf/BankReconciliation";
    $bank_recon_path = "{$pathPrefix}{$clientDirectory}/pdf/BankReconciliation/";

    $handle = opendir($bank_recon_path);
    if (! isset($view->items['type'])) {
        $view->items['type'] = 1;
    }

    $fileArray = [];
    if ($handle) {
        while (false !== ($file = readdir($handle))) {
            $file_name = $file;
            if ($file_name != '..' && $file_name != '.') {
                @[$name, $db, $date, $balance_word, $balance, $trust_account_code, $timeStamp] = @explode(
                    '_',
                    $file_name
                );

                $trans_date = convert_YYYYMMDD_to_DDMMYYYY($date);

                $timeStamp = rtrim($timeStamp, '.pdf');
                $sortTimeStamp = getSortTimeStamp($date);
                if ($balance < 0) {
                    $balance = $balance * -1;
                }
                $balanceF = formatting($balance);
                $pdfDownloadLink = "$bank_recon_download_link/$file_name";
                $tempRow = [];
                $successFlag = false;

                if ($view->items['type'] == 2) {
                    $successFlag = true;
                } else {
                    if ($view->items['type'] == 3) {
                        if (substr($trans_date, 3, 7) == $view->items['year']) {
                            $successFlag = true;
                        }
                    } else {
                        if ($balance == 0) {
                            $successFlag = true;
                        }
                    }
                }

                if ($successFlag) {
                    $tempRow = [
                        'sortTimeStamp' => $sortTimeStamp,
                        'trans_date' => $trans_date,
                        'balance' => $balanceF,
                        'acc_code' => $trust_account_code,
                        'downloadLink' => $pdfDownloadLink,
                    ];

                    $fileArray[] = $tempRow;
                }
            }
        }

        array_multisort(array_column($fileArray, 'sortTimeStamp'), SORT_DESC, $fileArray);
    }


    $view->items['optionList'] = [1 => 'View Balanced Files Only', 2 => 'View All Files', 3 => 'Files from'];
    $yearList = [];
    for ($x = 0; $x < 10; $x++) {
        $some_year = date('Y');
        $some_year = $some_year - $x;
        if ($some_year == date('Y')) {
            for ($y = date('n'); $y > 0; $y--) {
                $some_month = $y;
                if (strlen($some_month) == 1) {
                    $some_month = '0' . $some_month;
                }
                $month_year = $some_month . '/' . $some_year;
                $yearList[$month_year] = $month_year;
            }
        } else { // If it is not the current year start with the 12th month
            for ($y = 12; $y > 0; $y--) {
                $some_month = $y;
                if (strlen($some_month) == 1) {
                    $some_month = '0' . $some_month;
                } // In case the day is only one digit
                $month_year = $some_month . '/' . $some_year;
                $yearList[$month_year] = $month_year;
            }
        }
    }

    $view->items['yearList'] = $yearList;
    $view->items['fileArray'] = $fileArray;
    $view->render();
}

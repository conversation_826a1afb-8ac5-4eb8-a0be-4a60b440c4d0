<?php

use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Fill;

if ($fileFormat == FILETYPE_PDF) {
    $page = 0;
    $pdf->begin_page_ext(842, 595, '');
    cashbook_header('new', $trustAccCode, $trustAccName);
    $line = 480;
}

if ($fileFormat == FILETYPE_XLS) {
    $report->resetColumns();
    $report->line = 1;
    $report->enableFormatting = true;
    $indexSheet++;
    $report->setSheetDetails($indexSheet, 'Cash Book Report');

    $report->renderLineText("$trustAccCode($trustAccName) $periodFrom - To: $periodTo", 'styleLineTitle', 15);
    $report->renderHeader();

    $numberFormat = '_(* #,##0.00_);_(* (#,##0.00);_(* "-"??_);_(@_)';
    $report->enableFormatting = true;

    $clientCountryCode = $_SESSION['country_code'];

    $report->addColumn('transactionDate', 'Date', 36, 'left');
    $report->addColumn('propertyID', 'Property', 48, 'left');
    $report->addColumn('leaseSupplierID', 'Lease/Supplier', 50, 'left');
    $report->addColumn('chequeNumber', 'AP Invoice#', 70, 'left');
    $report->addColumn('createDate', 'Processing Date', 70, 'left');
    $report->addColumn('bankDate', '@Bank', 70, 'left');
    $report->addColumn('leaseSupplierName', 'Tenant/Payee', 75, 'left');
    $report->addColumn('description', 'Description', 50, 'left');
    $report->addColumn('fromDate', 'From', 40, 'left');
    $report->addColumn('toDate', 'To', 40, 'left');
    $report->addColumn('unitCode', 'Unit Code', 40, 'left');
    $report->addColumn('unitDescription', 'Unit', 40, 'left');
    $report->addColumn('accountID', 'Acct', 20, 'left', '@');
    $report->addColumn('ChequeEFTNumber', "CHQ/\nEFT#", 40, 'right');
    $report->addColumn('receiptNumber', "Receipt/\nPayment #", 40, 'right');

    if ($clientCountryCode == 'AU') {
        $report->addColumn('suppBsbBillerCode', 'Payee BSB/Biller Code', 60, 'right', '@');
    }

    $report->addColumn('suppAcctNoCrn', 'Account number/CRN', 60, 'right', '@');
    $report->addColumn('receiptNetAmount', 'Net', 47, 'right', $numberFormat);
    $report->addColumn('receiptGSTAmount', $_SESSION['country_default']['tax_label'], 47, 'right', $numberFormat);
    $report->addColumn('receiptTotalAmount', 'Received', 47, 'right', $numberFormat);


    $report->addColumn('paymentsNetAmount', 'Net', 47, 'right', $numberFormat);
    $report->addColumn('paymentsGSTAmount', $_SESSION['country_default']['tax_label'], 47, 'right', $numberFormat);
    $report->addColumn('paymentsTotalAmount', 'Paid', 47, 'right', $numberFormat);
    $report->addColumn('balance', 'Balance', 50, 'right', $numberFormat);
    $report->renderHeader();

    $sheet_ind = $c = 0;
    foreach ($report->columns as $cols) {
        $c++;
        $report->setColumnWidth(numberToLetter($c), 'auto');
    }

    $format = $fileFormat;
}

$reportType = 'bank';
$dataID = $trustAccCode;

$transactionOption = 'allTransactions';
$openBalance['transactionDate'] = 'Opening Balance';
$propNetChange['transactionDate'] = 'Property Net Change';
$closedBalance['transactionDate'] = 'Closing Balance';
$fromDate = $view->items['from_date'];
$toDate = $view->items['to_date'];
$allDates = 'No';
$pageBreak = ($context['pageBreak'] == 'Yes') ? true : false;
$showReceiptNo = ($context['showReceiptNo'] == 'Yes') ? true : false;

$reportResult = dbGetCashBook($reportType, $dataID, $transactionOption, $allDates, $accountID, $fromDate, $toDate, $leaseID, $supplierID, 'sortDate');
$count = count($reportResult ?? []);

if ($transactionOption != 'receiptsOnly' && $transactionOption != 'paymentsOnly') {
    $OBResult = dbGetCashBookOpeningBalance($reportType, $dataID, $accountID, $fromDate);
    foreach ($OBResult as $k => $v) {
        $property = ($reportType == 'bank') ? 'bank' : $v['propertyID'];

        $closingBalance[$property]['balance'] = $openingBalance[$property]['balance'] = $v['amount'];
        $closingBalance[$property]['netAmountAll'] = $openingBalance[$property]['netAmountAll'] = $v['net'];
        $closingBalance[$property]['gstAmountAll'] = $openingBalance[$property]['gstAmountAll'] = $v['gst'];
        $closingBalance[$property]['totalAmountAll'] = $openingBalance[$property]['totalAmountAll'] = $v['amount'];
    }
}


$accountTotalNet = $accountTotalGST = $accountTotalAmt = $k = 0;
$incomeTotalNet = $incomeTotalGST = $incomeTotalAmt = 0;
$expenseTotalNet = $expenseTotalGST = $expenseTotalAmt = 0;
$lastAccountCode = $lastProperty = $lastTransType = '';

foreach ($reportResult as $v) {
    $k++;

    $property = ($reportType == 'property') ? $v['propertyID'] : 'bank';
    $properties[$property][$k]['transactionDate'] = $v['transactionDate'];
    $properties[$property][$k]['propertyID'] = $v['propertyID'];
    $properties[$property][$k]['description'] = $v['description'];
    if ($format == FILETYPE_XLS) {
        $properties[$property][$k]['chequeNumber'] = $v['chequeNumber'];
        $properties[$property][$k]['createDate'] = $v['createDate'];
        $properties[$property][$k]['bankDate'] = $v['bankDate'];
    } else {
        if ($view->items['dateSort'] == '') {
            $properties[$property][$k]['chequeNumber'] = $v['chequeNumber'];
        } elseif ($view->items['dateSort'] == 'ProcDate') {
            $properties[$property][$k]['chequeNumber'] = $v['createDate'];
        } elseif ($view->items['dateSort'] == '@Bank') {
            $properties[$property][$k]['chequeNumber'] = $v['bankDate'];
        }
    }

    $properties[$property][$k]['accountID'] = $v['accountID'];
    $properties[$property][$k]['fromDate'] = $v['fromDate'];
    $properties[$property][$k]['toDate'] = $v['toDate'];
    $properties[$property][$k]['leaseSupplierID'] = $v['leaseSupplierID'];
    $properties[$property][$k]['leaseSupplierName'] = $v['leaseSupplierName'];

    if ($format == FILETYPE_XLS) {
        if ($clientCountryCode == 'AU') {
            $properties[$property][$k]['suppBsbBillerCode'] = $v['suppBsbBillerCode'];
        }

        $properties[$property][$k]['suppAcctNoCrn'] = $v['suppAcctNoCrn'];
    }

    $properties[$property][$k]['unitID'] = $v['unitID'];
    $properties[$property][$k]['unitDescription'] = $v['unitDescription'];
    $properties[$property][$k]['receiptNumber2'] = $v['receiptNumber2'];

    if ($v['transactionType'] == 'receipt') {
        $properties[$property][$k]['unitCode'] = $v['unitCode'];
        if ($format == FILETYPE_XLS) {
            $properties[$property][$k]['netAmountAll'] = $v['netAmount'];
            $properties[$property][$k]['gstAmountAll'] = $v['gstAmount'];
            $properties[$property][$k]['totalAmountAll'] = $v['totalAmount'];
        } else {
            $properties[$property][$k]['netAmountAll'] = toDecimal($v['netAmount'], 2);
            $properties[$property][$k]['gstAmountAll'] = toDecimal($v['gstAmount'], 2);
            $properties[$property][$k]['totalAmountAll'] = toDecimal($v['totalAmount'], 2);
        }
        $closingBalance[$property]['netAmountAll'] += $v['netAmount'];
        $closingBalance[$property]['gstAmountAll'] += $v['gstAmount'];
        $closingBalance[$property]['totalAmountAll'] += $v['totalAmount'];
    } else {
        if ($format == FILETYPE_XLS) {
            $properties[$property][$k]['netAmountAll'] = -$v['netAmount'];
            $properties[$property][$k]['gstAmountAll'] = -$v['gstAmount'];
            $properties[$property][$k]['totalAmountAll'] = -$v['totalAmount'];
        } else {
            $properties[$property][$k]['netAmountAll'] = toDecimal(-$v['netAmount'], 2);
            $properties[$property][$k]['gstAmountAll'] = toDecimal(-$v['gstAmount'], 2);
            $properties[$property][$k]['totalAmountAll'] = toDecimal(-$v['totalAmount'], 2);
        }
        $closingBalance[$property]['netAmountAll'] -= $v['netAmount'];
        $closingBalance[$property]['gstAmountAll'] -= $v['gstAmount'];
        $closingBalance[$property]['totalAmountAll'] -= $v['totalAmount'];
    }


    if ($v['transactionType'] == 'receipt') {
        $properties[$property][$k]['leaseID'] = $v['leaseID'];
        $properties[$property][$k]['leaseName'] = $v['leaseName'];
        $properties[$property][$k]['supplierID'] = $v['supplierID'];
        $properties[$property][$k]['supplierName'] = $v['supplierName'];



        if ($format == FILETYPE_XLS) {
            $properties[$property][$k]['chequeNumber'] = dbGetChequeNumber($v['transactionType'], ['debtorID' => $v['debtorID'], 'fromDate' => $fromDate, 'toDate' => $toDate]);
            $properties[$property][$k]['createDate'] = $v['createDate'];
            $properties[$property][$k]['bankDate'] = $v['bankDate'];
        } else {
            if ($view->items['dateSort'] == '') {
                $properties[$property][$k]['chequeNumber'] = dbGetChequeNumber($v['transactionType'], ['debtorID' => $v['debtorID'], 'fromDate' => $fromDate, 'toDate' => $toDate]);
            } elseif ($view->items['dateSort'] == 'ProcDate') {
                $properties[$property][$k]['chequeNumber'] = $v['createDate'];
            } elseif ($view->items['dateSort'] == '@Bank') {
                $properties[$property][$k]['chequeNumber'] = $v['bankDate'];
            }
        }

        $properties[$property][$k]['ReceiptPaymentNumber'] = dbGetChequeNumber($v['transactionType'], ['debtorID' => $v['debtorID'], 'transactionDate' => $v['transactionDate'], 'propertyID' => $v['propertyID'], 'batchNumber' => $v['paymentsExtraLine']]);

        $properties[$property][$k]['ChequeEFTNumber'] = $v['paymentReference'];
        //                $properties[$property][$k]['ChequeEFTNumber'] = dbGetChequeEFTNumber($v['transactionType'], array('propertyID' => $v['propertyID'],'debtorID' => $v['debtorID'], 'fromDate' => $fromDate, 'toDate' => $toDate));
        if ($properties[$property][$k]['ChequeEFTNumber'] == 'DIRECT DEPOSIT') {
            $properties[$property][$k]['ChequeEFTNumber'] = 'EFT';
        }


        $properties[$property][$k]['receiptNumber'] = $v['receiptNumber2'] ? $v['receiptNumber2'] : $v['receiptNumber'];
        if ($format == FILETYPE_XLS) {
            $properties[$property][$k]['receiptNetAmount'] = $v['netAmount'];
            $properties[$property][$k]['receiptGSTAmount'] = $v['gstAmount'];
            $properties[$property][$k]['receiptTotalAmount'] = $v['totalAmount'];
        } else {
            $properties[$property][$k]['receiptNetAmount'] = toDecimal($v['netAmount'], 2);
            $properties[$property][$k]['receiptGSTAmount'] = toDecimal($v['gstAmount'], 2);
            $properties[$property][$k]['receiptTotalAmount'] = toDecimal($v['totalAmount'], 2);
        }
        $accountTotalNet += $v['netAmount'];
        $accountTotalGST += $v['gstAmount'];
        $accountTotalAmt += $v['totalAmount'];

        $incomeTotalNet += $v['netAmount'];
        $incomeTotalGST += $v['gstAmount'];
        $incomeTotalAmt += $v['totalAmount'];

        $closingBalance[$property]['receiptNetAmount'] += $v['netAmount'];
        $closingBalance[$property]['receiptGSTAmount'] += $v['gstAmount'];
        $closingBalance[$property]['receiptTotalAmount'] = bcadd($closingBalance[$property]['receiptTotalAmount'], $v['totalAmount'], 2);
        if ($properties[$property][$previousKey]['transactionDate']) {
            $properties[$property][$previousKey]['subTotal'] = ($v['receiptNumber'] != $previousReceipt && $properties[$property][$previousKey] && $reportResult[$previousKey]['receiptNumber']) ? (($format == FILETYPE_XLS) ? $subTotal : toDecimal($subTotal, 2)) : '';
        }
        if ($v['receiptNumber'] == $previousReceipt) {
            $subTotal = bcadd($subTotal, -$v['totalAmount'], 2);
        } else {
            $subTotal = -$v['totalAmount'];
        }
        $previousKey = $k;
        $previousReceipt = $v['receiptNumber'];
        if ($subTotal && $properties[$property][$previousKey] && $reportResult[$previousKey]['receiptNumber']) {
            $properties[$property][$previousKey]['subTotal'] = ($format == FILETYPE_XLS) ? $subTotal : toDecimal($subTotal, 2);
        }

        $closingBalance[$property]['balance'] = bcadd($closingBalance[$property]['balance'], $v['totalAmount'], 2);
        $properties[$property][$k]['balance'] = ($format == FILETYPE_XLS) ? $closingBalance[$property]['balance'] : toDecimal($closingBalance[$property]['balance'], 2);

    } else {
        $properties[$property][$k]['supplierID'] = $v['supplierID'];
        $properties[$property][$k]['supplierName'] = $v['supplierName'];

        if ($format == FILETYPE_XLS) {
            if ($clientCountryCode == 'AU') {
                $properties[$property][$k]['suppBsbBillerCode'] = $v['suppBsbBillerCode'];
            }
            $properties[$property][$k]['suppAcctNoCrn'] = $v['suppAcctNoCrn'];
        }

        $properties[$property][$k]['receiptNumber'] = $v['receiptNumber'];
        $properties[$property][$k]['receiptNumber'] = dbGetChequeNumber($v['transactionType'], ['batchNumber' => $v['paymentsExtraLine'], 'lineNumber' => $v['sequenceNumber']]);
        $properties[$property][$k]['ReceiptPaymentNumber'] = dbGetChequeNumber($v['transactionType'], ['batchNumber' => $v['paymentsExtraLine'], 'lineNumber' => $v['sequenceNumber']]);

        $properties[$property][$k]['ChequeEFTNumber'] = '';

        $properties[$property][$k]['ChequeEFTNumber'] = dbGetChequeEFTNumber($v['transactionType'], ['batchNumber' => $v['paymentsExtraLine']]);
        if ($properties[$property][$k]['ChequeEFTNumber'] == 'X') {
            $properties[$property][$k]['ChequeEFTNumber'] = 'EFT';
        }
        if ($properties[$property][$k]['ChequeEFTNumber'] == 'C') {
            $properties[$property][$k]['ChequeEFTNumber'] = 'CHQ';
        }
        if ($properties[$property][$k]['ChequeEFTNumber'] == 'Y') {
            $properties[$property][$k]['ChequeEFTNumber'] = 'BPAY';
        }
        if ($format == FILETYPE_XLS) {
            $properties[$property][$k]['paymentsNetAmount'] = -$v['netAmount'];
            $properties[$property][$k]['paymentsGSTAmount'] = -$v['gstAmount'];
            $properties[$property][$k]['paymentsTotalAmount'] = -$v['totalAmount'];
        } else {
            $properties[$property][$k]['paymentsNetAmount'] = toDecimal(-$v['netAmount'], 2);
            $properties[$property][$k]['paymentsGSTAmount'] = toDecimal(-$v['gstAmount'], 2);
            $properties[$property][$k]['paymentsTotalAmount'] = toDecimal(-$v['totalAmount'], 2);
        }
        $accountTotalNet += $v['netAmount'];
        $accountTotalGST += $v['gstAmount'];
        $accountTotalAmt += $v['totalAmount'];

        $expenseTotalNet += $v['netAmount'];
        $expenseTotalGST += $v['gstAmount'];
        $expenseTotalAmt += $v['totalAmount'];

        $closingBalance[$property]['paymentsNetAmount'] -= $v['netAmount'];
        $closingBalance[$property]['paymentsGSTAmount'] -= $v['gstAmount'];
        $closingBalance[$property]['paymentsTotalAmount'] = bcsub($closingBalance[$property]['paymentsTotalAmount'], $v['totalAmount'], 2);


        if ($properties[$property][$previousKey]['transactionDate']) {
            $properties[$property][$previousKey]['subTotal'] = ($v['chequeNumber'] != $previousCheque && $properties[$property][$previousKey] && $reportResult[$previousKey]['chequeNumber']) ? (($format == FILETYPE_XLS) ? $subTotal : toDecimal($subTotal, 2)) : '';
        }
        if ($v['chequeNumber'] == $previousCheque) {
            $subTotal = bcadd($subTotal, -$v['totalAmount'], 2);
        } else {
            $subTotal = -$v['totalAmount'];
        }
        $previousKey = $k;
        $previousCheque = $v['chequeNumber'];
        if ($subTotal && $properties[$property][$previousKey] && $reportResult[$previousKey]['chequeNumber']) {
            $properties[$property][$previousKey]['subTotal'] = ($format == FILETYPE_XLS) ? $subTotal : toDecimal($subTotal, 2);
        }

        $closingBalance[$property]['balance'] = bcadd($closingBalance[$property]['balance'], $v['totalAmount'], 2);
        $properties[$property][$k]['balance'] = ($format == FILETYPE_XLS) ? $closingBalance[$property]['balance'] : toDecimal($closingBalance[$property]['balance'], 2);
    }

    $lastProperty = $property;
    $lastAccountCode = $v['accountID'];
    $lastTransType = $v['transactionType'];
}

// display last account code
if ($lastAccountCode != '' and $view->items['sortOption'] == 'sortAccountID') {
    $k++;
    // display account code total if sorted by account code
    $properties[$lastProperty][$k]['bold'] = false;
    $properties[$lastProperty][$k]['width']['transactionDate'] = 300;
    $properties[$lastProperty][$k]['bgcolor'] = [0.9, 0.9, 0.9];
    //          $properties[$lastProperty][$k]['accountID'] = $lastAccountCode;
    $properties[$lastProperty][$k]['leaseName'] = '';
    $properties[$lastProperty][$k]['supplierName'] = '';
    $properties[$lastProperty][$k]['transactionDate'] = "Account Total for $lastAccountCode " . dbGetAccountName($lastAccountCode);

    // used by excel
    $properties[$lastProperty][$k]['headerStyle'] =
    [
        'fill' => [
            'type' => Fill::FILL_SOLID,
            'color' => ['rgb' => 'e1e1e1'],
        ],
        'font' => ['color' => ['rgb' => '000000']],
        'alignment' => ['vertical' => Alignment::VERTICAL_CENTER, 'horizontal' => Alignment::HORIZONTAL_LEFT],
    ];

    if ($lastTransType == 'receipt') {
        $properties[$lastProperty][$k]['receiptNetAmount'] = $format == FILETYPE_XLS ? $accountTotalNet : toDecimal($accountTotalNet, 2);
        $properties[$lastProperty][$k]['receiptGSTAmount'] = $format == FILETYPE_XLS ? $accountTotalGST : toDecimal($accountTotalGST, 2);
        $properties[$lastProperty][$k]['receiptTotalAmount'] = $format == FILETYPE_XLS ? $accountTotalAmt : toDecimal($accountTotalAmt, 2);

        $properties[$lastProperty][$k]['netAmountAll'] = $format == FILETYPE_XLS ? $accountTotalNet : toDecimal($accountTotalNet, 2);
        $properties[$lastProperty][$k]['gstAmountAll'] = $format == FILETYPE_XLS ? $accountTotalGST : toDecimal($accountTotalGST, 2);
        $properties[$lastProperty][$k]['totalAmountAll'] = $format == FILETYPE_XLS ? $accountTotalAmt : toDecimal($accountTotalAmt, 2);
    } else {
        $properties[$lastProperty][$k]['paymentsNetAmount'] = $format == FILETYPE_XLS ? -$accountTotalNet : toDecimal(-$accountTotalNet, 2);
        $properties[$lastProperty][$k]['paymentsGSTAmount'] = $format == FILETYPE_XLS ? -$accountTotalGST : toDecimal(-$accountTotalGST, 2);
        $properties[$lastProperty][$k]['paymentsTotalAmount'] = $format == FILETYPE_XLS ? -$accountTotalAmt : toDecimal(-$accountTotalAmt, 2);

        $properties[$lastProperty][$k]['netAmountAll'] = $format == FILETYPE_XLS ? -$accountTotalNet : toDecimal(-$accountTotalNet, 2);
        $properties[$lastProperty][$k]['gstAmountAll'] = $format == FILETYPE_XLS ? -$accountTotalGST : toDecimal(-$accountTotalGST, 2);
        $properties[$lastProperty][$k]['totalAmountAll'] = $format == FILETYPE_XLS ? -$accountTotalAmt : toDecimal(-$accountTotalAmt, 2);
    }


    $k++;

    $properties[$lastProperty][$k]['bold'] = true;
    $properties[$lastProperty][$k]['bgcolor'] = [220 / 255, 242 / 255, 255 / 255];

    if ($format != FILETYPE_SCREEN) {
        $properties[$lastProperty][$k]['leaseName'] =
        $properties[$lastProperty][$k]['supplierName'] =
        $properties[$lastProperty][$k]['transactionDate'] = $lastTransType == 'receipt' ? 'Income Total' : 'Expense Total';
        $properties[$lastProperty][$k]['width']['transactionDate'] = 300;
    } else {
        $properties[$lastProperty][$k]['transactionDate'] = $lastTransType == 'receipt' ? 'Income Total' : 'Expense Total';
    }

    // used by excel
    $properties[$lastProperty][$k]['headerStyle'] =
    [
        'fill' => [
            'type' => Fill::FILL_SOLID,
            'color' => ['rgb' => 'dcf2ff'],
        ],
        'font' => ['bold' => true, 'color' => ['rgb' => '000000']],
        'alignment' => ['vertical' => Alignment::VERTICAL_CENTER, 'horizontal' => Alignment::HORIZONTAL_LEFT],
    ];


    if ($lastTransType == 'receipt') {
        $properties[$lastProperty][$k]['receiptNetAmount'] = $format == FILETYPE_XLS ? $incomeTotalNet : toDecimal($incomeTotalNet, 2);
        $properties[$lastProperty][$k]['receiptGSTAmount'] = $format == FILETYPE_XLS ? $incomeTotalGST : toDecimal($incomeTotalGST, 2);
        $properties[$lastProperty][$k]['receiptTotalAmount'] = $format == FILETYPE_XLS ? $incomeTotalAmt : toDecimal($incomeTotalAmt, 2);

        $properties[$lastProperty][$k]['netAmountAll'] = $format == FILETYPE_XLS ? $incomeTotalNet : toDecimal($incomeTotalNet, 2);
        $properties[$lastProperty][$k]['gstAmountAll'] = $format == FILETYPE_XLS ? $incomeTotalGST : toDecimal($incomeTotalGST, 2);
        $properties[$lastProperty][$k]['totalAmountAll'] = $format == FILETYPE_XLS ? $incomeTotalAmt : toDecimal($incomeTotalAmt, 2);
    } else {
        $properties[$lastProperty][$k]['paymentsNetAmount'] = $format == FILETYPE_XLS ? -$expenseTotalNet : toDecimal(-$expenseTotalNet, 2);
        $properties[$lastProperty][$k]['paymentsGSTAmount'] = $format == FILETYPE_XLS ? -$expenseTotalGST : toDecimal(-$expenseTotalGST, 2);
        $properties[$lastProperty][$k]['paymentsTotalAmount'] = $format == FILETYPE_XLS ? -$expenseTotalAmt : toDecimal(-$expenseTotalAmt, 2);

        $properties[$lastProperty][$k]['netAmountAll'] = $format == FILETYPE_XLS ? -$expenseTotalNet : toDecimal(-$expenseTotalNet, 2);
        $properties[$lastProperty][$k]['gstAmountAll'] = $format == FILETYPE_XLS ? -$expenseTotalGST : toDecimal(-$expenseTotalGST, 2);
        $properties[$lastProperty][$k]['totalAmountAll'] = $format == FILETYPE_XLS ? -$expenseTotalAmt : toDecimal(-$expenseTotalAmt, 2);

    }

}

if ($format == FILETYPE_XLS) {
    if ($properties[$property][$previousKey] && $reportResult[$previousKey]['receiptNumber']) {
        $properties[$property][$previousKey]['subTotal'] = $subTotal;
    }
    if ($properties[$property][$previousKey] && $reportResult[$previousKey]['chequeNumber']) {
        $properties[$property][$previousKey]['subTotal'] = $subTotal;
    }
} else {
    if ($properties[$property][$previousKey] && $reportResult[$previousKey]['receiptNumber']) {
        $properties[$property][$previousKey]['subTotal'] = toDecimal($subTotal, 2);
    }
    if ($properties[$property][$previousKey] && $reportResult[$previousKey]['chequeNumber']) {
        $properties[$property][$previousKey]['subTotal'] = toDecimal($subTotal, 2);
    }
}


$line = 469;
if ($properties && is_array($properties)) {

    foreach ($properties as $k => $property) {
        $propertyName = $propertyList[$k]['name'];
        $ownerCode = dbGetPrimaryOwner($k);
        $ownerName = dbGetOwnerName($k);

        $openBalance['balance'] = ($openingBalance[$k]['balance']) ? (($format == FILETYPE_XLS) ? $openingBalance[$k]['balance'] : toDecimal($openingBalance[$k]['balance'], 2)) : '0.00';
        $openBalance['netAmountAll'] = ($openingBalance[$k]['netAmountAll']) ? (($format == FILETYPE_XLS) ? $openingBalance[$k]['netAmountAll'] : toDecimal($openingBalance[$k]['netAmountAll'], 2)) : '0.00';
        $openBalance['gstAmountAll'] = ($openingBalance[$k]['gstAmountAll']) ? (($format == FILETYPE_XLS) ? $openingBalance[$k]['gstAmountAll'] : toDecimal($openingBalance[$k]['gstAmountAll'], 2)) : '0.00';
        $openBalance['totalAmountAll'] = ($openingBalance[$k]['totalAmountAll']) ? (($format == FILETYPE_XLS) ? $openingBalance[$k]['totalAmountAll'] : toDecimal($openingBalance[$k]['totalAmountAll'], 2)) : '0.00';

        $openBalance['width']['transactionDate'] = 300;

        if ($fileFormat == FILETYPE_PDF) {
            $pdf->setColorExt('both', 'rgb', 0.9, 0.9, 0.9, 0);
            $pdf->rect(5, 482, 832, 13);
            $pdf->fill();

            $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
            $pdf->setFontExt($_fonts['Helvetica-Bold'], 7);
            $pdf->showBoxed('Opening Balance', 8, 482, 100, 10, 'left', '');
            $pdf->showBoxed($openBalance['totalAmountAll'], 779, 482, 50, 10, 'right', '');

            foreach ($property as $row) {
                if ($line <= 70) {
                    $pdf->setColorExt('both', 'rgb', 0.3, 0.3, 0.3, 0);
                    $pdf->setFontExt($_fonts['Helvetica-Bold'], 5);
                    $pdf->showBoxed('Page ' . $page, 755, 5, 75, 8, 'right', '');

                    $traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'Cash Book', A4_LANDSCAPE);
                    $traccFooter->prerender($pdf);


                    $pdf->end_page_ext('');
                    $pdf->begin_page_ext(842, 595, '');
                    cashbook_header('cont', $trustAccCode, $trustAccName);

                    $line = 482;
                }

                $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
                $pdf->setFontExt($_fonts['Helvetica'], 7);

                $pdf->showBoxed($row['transactionDate'], 8, $line, 40, 10, 'left', '');
                $pdf->showBoxed($row['propertyID'], 50, $line, 48, 10, 'left', '');
                $pdf->showBoxed($row['leaseSupplierID'], 104, $line, 50, 10, 'left', '');
                $pdf->showBoxed($row['chequeNumber'], 160, $line, 40, 10, 'left', '');
                $pdf->showBoxed($row['leaseSupplierName'], 206, $line, 75, 10, 'left', '');
                $pdf->showBoxed($row['description'], 287, $line, 50, 10, 'left', '');
                $pdf->showBoxed($row['accountID'], 343, $line, 20, 10, 'left', '');
                $pdf->showBoxed($row['ChequeEFTNumber'], 369, $line, 40, 10, 'right', '');
                $pdf->showBoxed($row['receiptNumber'], 415, $line, 40, 10, 'right', '');
                $pdf->showBoxed($row['receiptNetAmount'], 461, $line, 47, 10, 'right', '');
                $pdf->showBoxed($row['receiptGSTAmount'], 514, $line, 47, 10, 'right', '');
                $pdf->showBoxed($row['receiptTotalAmount'], 567, $line, 47, 10, 'right', '');
                $pdf->showBoxed($row['paymentsNetAmount'], 620, $line, 47, 10, 'right', '');
                $pdf->showBoxed($row['paymentsGSTAmount'], 673, $line, 47, 10, 'right', '');
                $pdf->showBoxed($row['paymentsTotalAmount'], 726, $line, 47, 10, 'right', '');
                $pdf->showBoxed($row['balance'], 779, $line, 50, 10, 'right', '');
                $line -= 13;

            }
        } else {
            $report->renderLine($openBalance);
            $report->renderData_custom($property);
        }

        $closedBalance['receiptNetAmount'] = $closingBalance[$k]['receiptNetAmount'];
        $closedBalance['receiptGSTAmount'] = $closingBalance[$k]['receiptGSTAmount'];
        $closedBalance['receiptTotalAmount'] = $closingBalance[$k]['receiptTotalAmount'];
        $closedBalance['paymentsNetAmount'] = $closingBalance[$k]['paymentsNetAmount'];
        $closedBalance['paymentsGSTAmount'] = $closingBalance[$k]['paymentsGSTAmount'];
        $closedBalance['paymentsTotalAmount'] = $closingBalance[$k]['paymentsTotalAmount'];
        $closedBalance['balance'] = $closingBalance[$k]['balance'];

        $openBalance['netAmountAll'] = $openingBalance[$k]['netAmountAll'];
        $openBalance['gstAmountAll'] = $openingBalance[$k]['gstAmountAll'];
        $openBalance['totalAmountAll'] = $openingBalance[$k]['totalAmountAll'];

        $propNetChange['netAmountAll'] = ($closingBalance[$k]['receiptNetAmount']) + (-$closingBalance[$k]['paymentsNetAmount']);
        $propNetChange['gstAmountAll'] = ($closingBalance[$k]['receiptGSTAmount']) + (-$closingBalance[$k]['paymentsGSTAmount']);
        $propNetChange['totalAmountAll'] = ($closingBalance[$k]['receiptTotalAmount']) + (-$closingBalance[$k]['paymentsTotalAmount']);

        $closedBalance['netAmountAll'] = $openingBalance[$k]['netAmountAll'] + ($closingBalance[$k]['receiptNetAmount']) + (-$closingBalance[$k]['paymentsNetAmount']);
        $closedBalance['gstAmountAll'] = $openingBalance[$k]['gstAmountAll'] + ($closingBalance[$k]['receiptGSTAmount']) + (-$closingBalance[$k]['paymentsGSTAmount']);
        $closedBalance['totalAmountAll'] = $openingBalance[$k]['totalAmountAll'] + ($closingBalance[$k]['receiptTotalAmount']) + (-$closingBalance[$k]['paymentsTotalAmount']);

        $openBalance['netAmountAll'] = toDecimal($openBalance['netAmountAll'], 2);
        $openBalance['gstAmountAll'] = toDecimal($openBalance['gstAmountAll'], 2);
        $openBalance['totalAmountAll'] = toDecimal($openBalance['totalAmountAll'], 2);

        $propNetChange['netAmountAll'] = toDecimal($propNetChange['netAmountAll'], 2);
        $propNetChange['gstAmountAll'] = toDecimal($propNetChange['gstAmountAll'], 2);
        $propNetChange['totalAmountAll'] = toDecimal($propNetChange['totalAmountAll'], 2);

        $closedBalance['netAmountAll'] = toDecimal($closedBalance['netAmountAll'], 2);
        $closedBalance['gstAmountAll'] = toDecimal($closedBalance['gstAmountAll'], 2);
        $closedBalance['totalAmountAll'] = toDecimal($closedBalance['totalAmountAll'], 2);

        $closedBalance['receiptNetAmount'] = toDecimal($closedBalance['receiptNetAmount'], 2);
        $closedBalance['receiptGSTAmount'] = toDecimal($closedBalance['receiptGSTAmount'], 2);
        $closedBalance['receiptTotalAmount'] = toDecimal($closedBalance['receiptTotalAmount'], 2);
        $closedBalance['paymentsNetAmount'] = toDecimal($closedBalance['paymentsNetAmount'], 2);
        $closedBalance['paymentsGSTAmount'] = toDecimal($closedBalance['paymentsGSTAmount'], 2);
        $closedBalance['paymentsTotalAmount'] = toDecimal($closedBalance['paymentsTotalAmount'], 2);
        $closedBalance['balance'] = toDecimal($closedBalance['balance'], 2);

        if ($fileFormat == FILETYPE_PDF) {
            if ($line <= 70) {
                $pdf->setColorExt('both', 'rgb', 0.3, 0.3, 0.3, 0);
                $pdf->setFontExt($_fonts['Helvetica-Bold'], 5);
                $pdf->showBoxed('Page ' . $page, 755, 5, 75, 8, 'right', '');

                $traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'Cash Book', A4_LANDSCAPE);
                $traccFooter->prerender($pdf);


                $pdf->end_page_ext('');
                $pdf->begin_page_ext(842, 595, '');
                cashbook_header('cont', $trustAccCode, $trustAccName);

                $line = 482;
            }

            $pdf->setColorExt('both', 'rgb', 0.9, 0.9, 0.9, 0);
            $pdf->rect(5, $line, 832, 13);
            $pdf->fill();

            $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
            $pdf->setFontExt($_fonts['Helvetica-Bold'], 7);
            $pdf->showBoxed('Closing Balance', 8, $line, 100, 10, 'left', '');
            $pdf->showBoxed($closedBalance['receiptNetAmount'], 461, $line, 47, 10, 'right', '');
            $pdf->showBoxed($closedBalance['receiptGSTAmount'], 514, $line, 47, 10, 'right', '');
            $pdf->showBoxed($closedBalance['receiptTotalAmount'], 567, $line, 47, 10, 'right', '');
            $pdf->showBoxed($closedBalance['paymentsNetAmount'], 620, $line, 47, 10, 'right', '');
            $pdf->showBoxed($closedBalance['paymentsGSTAmount'], 673, $line, 47, 10, 'right', '');
            $pdf->showBoxed($closedBalance['paymentsTotalAmount'], 726, $line, 47, 10, 'right', '');
            $pdf->showBoxed($closedBalance['balance'], 779, $line, 50, 10, 'right', '');
        } else {
            $report->renderLine($closedBalance);
        }
    }
}

if ($fileFormat == FILETYPE_PDF) {
    $pdf->setColorExt('both', 'rgb', 0.3, 0.3, 0.3, 0);
    $pdf->setFontExt($_fonts['Helvetica-Bold'], 5);
    $pdf->showBoxed('Page ' . $page, 755, 5, 75, 8, 'right', '');

    $traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'Cash Book', A4_LANDSCAPE);
    $traccFooter->prerender($pdf);

    $pdf->end_page_ext('');
}

function cashbook_header($cont, $trustAccCode, $trustAccName)
{
    global $pdf;
    global $propertyName;
    global $line;
    global $description;
    global $periodDescription;
    global $client;
    global $page;
    global $periodFrom;
    global $periodTo;
    global $date;
    global $logo;
    global  $_fonts;


    $offset = 16;
    $pdf->setFontExt($_fonts['Helvetica'], 12);
    $pdf->setColorExt('both', 'rgb', 0, 0.3, 0.5, 0);
    $pdf->showBoxed('Cash Book Report', 6, 575, 550, 16, 'left', '');


    $pdf->setColorExt('both', 'rgb', 0.4, 0.4, 0.4, 0);
    $pdf->setFontExt($_fonts['Helvetica-Bold'], 10);
    $pdf->showBoxed($trustAccName . " ($trustAccCode)", 6, 560, 550, 12, 'left', '');

    $_fonts['Helvetica-Oblique'] = $pdf->load_font('Helvetica-Oblique', 'host', '');
    $pdf->setColorExt('both', 'rgb', 0.3, 0.3, 0.3, 0);
    $pdf->setFontExt($_fonts['Helvetica-Oblique'], 8);
    $pdf->showBoxed("$periodFrom to $periodTo", 6, 547, 550, 12, 'left', '');


    $page++;

    $pdf->setColorExt('both', 'rgb', 0, 0.3, 0.5, 0);
    $pdf->rect(5, 495, 832, 13);
    $pdf->fill();

    $pdf->setColorExt('both', 'rgb', 1, 1, 1, 0);
    $pdf->setFontExt($_fonts['Helvetica-Bold'], 7);
    $pdf->showBoxed('Date', 8, 495, 40, 10, 'left', '');
    $pdf->showBoxed('Property', 50, 495, 48, 10, 'left', '');
    $pdf->showBoxed('Lease/Supplier', 104, 495, 50, 10, 'left', '');
    $pdf->showBoxed('AP Invoice#', 160, 495, 40, 10, 'left', '');
    $pdf->showBoxed('Tenant/Payee', 206, 495, 75, 10, 'left', '');
    $pdf->showBoxed('Description', 287, 495, 50, 10, 'left', '');
    $pdf->showBoxed('Acct', 343, 495, 20, 10, 'left', '');
    $pdf->setFontExt($_fonts['Helvetica-Bold'], 5);
    $pdf->showBoxed("CHQ/\nEFT#", 369, 495, 40, 12, 'right', '');
    $pdf->showBoxed("Receipt/\nPayment #", 415, 495, 40, 12, 'right', '');
    $pdf->setFontExt($_fonts['Helvetica-Bold'], 7);
    $pdf->showBoxed('Net', 461, 495, 47, 10, 'right', '');
    $pdf->showBoxed($_SESSION['country_default']['tax_label'], 514, 495, 47, 10, 'right', '');
    $pdf->showBoxed('Received', 567, 495, 47, 10, 'right', '');
    $pdf->showBoxed('Net', 620, 495, 47, 10, 'right', '');
    $pdf->showBoxed($_SESSION['country_default']['tax_label'], 673, 495, 47, 10, 'right', '');
    $pdf->showBoxed('Paid', 726, 495, 47, 10, 'right', '');
    $pdf->showBoxed('Balance', 779, 495, 50, 10, 'right', '');


    // START LOGO
    $logoFile = dbGetClientLogo();
    $logoPath = realpath("assets/clientLogos/{$logoFile}");

    if ($logoPath and file_exists($logoPath)) {

        $maxWidth = LOGO_WIDTH;
        $maxHeight = LOGO_HEIGHT;
        [$imageWidth, $imageHeight] = getimagesize($logoPath);

        $horizontalScaling = ($imageWidth > $maxWidth) ? $maxWidth / $imageWidth : 1;
        $verticalScaling = ($imageHeight > $maxHeight) ? $maxHeight / $imageHeight : 1;
        $imageScale = ($horizontalScaling < $verticalScaling) ? $horizontalScaling : $verticalScaling;

        $imageScale = round($imageScale, 2);

        $hPos = 842 - ($imageScale * $imageWidth) - 5;
        $vPos = 595 - ($imageScale * $imageHeight) - 5;


        $pdfimage = $pdf->load_image('auto', $logoPath, '');
        $pdf->fit_image($pdfimage, $hPos, $vPos, "scale {$imageScale}");
    }
    // END LOGO


    $pdf->setFontExt($_fonts['Helvetica'], 8);
    $pdf->showBoxed("Page {$page}", 750, 0, 75, 30, 'right', '');

    $line = 480;
}

<?php

function home(&$context)
{
    // secureAccess();

    if (! isPostback()) {
        $view = new MasterPage(userViews(), '/bankReconciliation/home.html');
    } else {
        $view = new UserControl(userViews(), '/bankReconciliation/home.html', '');
    }
    $view->setSection($context['module']);

    $view->bindAttributesFrom($context);
    $view->bindAttributesFrom($_REQUEST);

    $view->setSection($context['module']);
    $view->items['last_error'] = $context['last_error'];

    $view->render();
}

<?php

include 'lib/ngForm.php';
include 'lib/htmler.php';

function bankRecon(&$context)
{
    if (! isPostback()) {
        $view = new MasterPage(userViews(), '/bankReconciliation/bankRecon.html');
        $view->setSection($context['module']);
    } else {
        $view = new UserControl(userViews(), '/bankReconciliation/bankRecon.html');
    }
    $view->bindAttributesFrom($_REQUEST);
    $view->render();
}

<?php

include 'lib/ngForm.php';
function bankStatements(&$context)
{
    global $clientDirectory, $sess, $pathPrefix;

    if (isUserType(USER_TRACC)) {
        $view = new MasterPage(userViews(), '/bankReconciliation/bankStatements.html', 'views_vuejs');
    } else {
        $view = new MasterPage(userViews(), '/bankReconciliation/bankStatements.html', 'views_vuejs');
    }
    $view->setSection($context['module']);

    $view->bindAttributesFrom($_REQUEST);
    $view->items['url'] = c8_api . 'bank-statements';

    $view->render();
}

<?php

/**
 * <AUTHOR>
 *
 * @since 2013-01-07
 **/
function addCheques(&$context)
{
    $userpermission = new userpermission();
    $result = $userpermission->userHasPageAccess(__FUNCTION__);

    // Page Template
    if (! isPostback()) {
        $view = new MasterPage(userViews(), '/bankReconciliation/addCheques.html');
        $view->setSection($context['module']);
    } else {
        $view = new UserControl(userViews(), '/bankReconciliation/addCheques.html');
    }
    $view->bindAttributesFrom($_REQUEST);

    // Array Call-In
    $view->items['bankList'] = dbGetBankAccounts();

    // Action
    switch ($view->items['action']) {
        case 'finalise':
            $chequeCount = ($view->items['chequeRange1'] and $view->items['chequeRange2'] and $view->items['bankID']) ? dbCheckChequeRange($view->items['bankID'], $view->items['chequeRange1'], $view->items['chequeRange2']) : 0;
            // Validation
            if (empty($view->items['bankID']) or ! isValid($view->items['bankID'], TEXT_INT, false)) {
                $validationErrors[] = 'You need to select a ' . ucwords(strtolower($_SESSION['country_default']['trust_account'])) . '.';
            }
            if (empty($view->items['chequeRange1']) or ! isValid($view->items['chequeRange1'], TEXT_INT, false)) {
                $validationErrors[] = 'You need to enter a starting cheque number for the cheque range.';
            }
            if (empty($view->items['chequeRange2']) or ! isValid($view->items['chequeRange2'], TEXT_INT, false)) {
                $validationErrors[] = 'You need to enter an ending cheque number for the cheque range.';
            } elseif (($view->items['chequeRange2'] > **********)) {
                $validationErrors[] = 'You need to enter a valid ending number for the cheque range.';
            }
            if ($view->items['chequeRange1'] and $view->items['chequeRange2'] and $view->items['chequeRange1'] > $view->items['chequeRange2']) {
                $validationErrors[] = 'To cheque range must come after (or equal to) the From cheque range.';
            }
            if ($chequeCount) {
                $validationErrors[] = "The cheque range you entered already has $chequeCount cheques within it.";
            }
            // No Errors
            if (noErrors($validationErrors)) {
                $count = dbInsertCheques($view->items['bankID'], $view->items['chequeRange1'], $view->items['chequeRange2']);
                if ($count) {
                    $view->items['statusMessage'] = 'The cheque range you have entered has been added into the system for ' . ucwords(strtolower($_SESSION['country_default']['trust_account'])) . ' ' . $view->items['bankID'] . ". $count cheques has been added.";
                } else {
                    $validationErrors[] = 'An unexpected error occurred while processing the cheque range. Please try again.';
                }
            }
            break;
    }

    // Post Feed
    $view->items['validationErrors'] = $validationErrors;

    // Display
    $view->render();
}

<?php

include 'lib/ngForm.php';
function bankRecs(&$context)
{
    global $clientDirectory, $sess, $pathPrefix;
    if (isUserType(USER_TRACC)) {
        $view = new MasterPage(userViews(), '/bankReconciliation/bankRecs.html', 'views_vuejs');
    } else {
        $view = new MasterPage(userViews(), '/bankReconciliation/bankRecs.html', 'views_vuejs');
    }
    $view->setSection($context['module']);

    $view->bindAttributesFrom($_REQUEST);
    $view->items['url'] = c8_api . 'bank-recons';

    $view->render();
}

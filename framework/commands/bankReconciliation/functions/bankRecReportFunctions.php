<?php

function getBankAccounts()
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);

    $sqlquery = 'SELECT pmbk_code AS acc_code, pmbk_acc_name as acc_name FROM pmbk_bank';

    return $dbh->executeSet($sqlquery);
}

function getBankAccName($accNo)
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);
    $sqlquery = 'SELECT pmbk_acc_name as acc_name FROM pmbk_bank WHERE pmbk_code=?';

    return $dbh->executeScalar($sqlquery, [$accNo]);
}


// #####################First page Form Functions #########################################################
// #####################First page Form Functions #########################################################

function getCurrentYear()
{
    return date('Y', time());
}


// #################################PDF Functions #####################################

function initializePDF($title, $fileName, $path, $downloadLink, $bankName)
{ // echo " Prop: $propNme " ;
    global $pdf;
    $openFileSuccess = false;
    $success = check_dirpath($path);

    // prepare the file
    $timestamp = date('dmYHis');
    $ext = '.pdf';
    $fullFileName = $fileName . $timestamp . $ext;

    // Full logical Path for pdf creation
    $fullPath = $path . $fullFileName;
    $pdfDownloadLink = "$downloadLink/$fullFileName";


    $pdf = new PDFlibExt();
    $pdf->set_option('license=' . PDFLIB_LICENSE);
    $pdf->set_option('stringformat=utf8');
    $openFileSccess = $pdf->begin_document($fullPath, '');

    if ($openFileSccess) {
        renderDownloadLink($pdfDownloadLink);
        $pdf->set_info('Author', 'cirrus8 Pty Ltd | cirrus8.com.au - Online Commercial Property Management Solution');
        $pdf->set_info('Title', $title);
    } else {
        return false;
    }

    return $pdfDownloadLink;
}

function closePdfFile()
{
    global $pdf;
    $pdf->end_document('');
}

// ###################################################################################

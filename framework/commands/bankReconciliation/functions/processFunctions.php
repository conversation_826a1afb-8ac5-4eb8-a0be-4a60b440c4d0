<?php

/**
 * @modified 2012-09-11 Modified to include cancellation date in the range as per task # 476979 [Morph]
 **/
function getChqRegisterData($trustAccCode, $periodFromSql, $periodToSql, $unPresentOnlyFlag = false)
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);
    $params = $subQuery_params = $subQuery2_params = [];
    $result = [];
    $subQuery = '';
    $subQuery2 = '';
    if ($unPresentOnlyFlag) {
        $subQuery = ' AND (pmbc_ppdate > ' . addSQLParam($subQuery_params, $periodToSql) . ' OR pmbc_reco_chq.pmbc_ppdate Is Null)';
        $subQuery2 = ' AND (pmbc_cppdate > ' . addSQLParam($subQuery2_params, $periodToSql) . ' OR pmbc_reco_chq.pmbc_cppdate Is Null)';
    }
    $sql = '
        Select chq_no ,payee_code ,payee_name , cheque_date , cheque_amount , pres_date , pres_value , cancel_date , cancel_value , unpresented_amount, cancel_by, cancelled_date, cancel_reason
            FROM (
        SELECT 
                pmbc_pdate , 
				pmbc_reco_chq.pmbc_chq AS chq_no, 
				pmbc_reco_chq.pmbc_creditor AS payee_code, 
				pmco_company.pmco_name AS payee_name, 
				CONVERT(char(10), pmbc_reco_chq.pmbc_pdate, 103) as cheque_date, 
				pmbc_reco_chq.pmbc_pval as cheque_amount, 
				CONVERT(char(10), pmbc_reco_chq.pmbc_ppdate, 103) as pres_date,  
				pmbc_reco_chq.pmbc_ppval AS pres_value, 
				null AS  cancel_date,
				0.00 AS cancel_value,
				(pmbc_reco_chq.pmbc_pval - isnull(pmbc_reco_chq.pmbc_ppval,0) ) AS unpresented_amount,
				pmbc_reco_chq.pmbc_cancel_by AS cancel_by, 
				pmbc_reco_chq.pmbc_cancel_date AS cancelled_date, 
				pmbc_reco_chq.pmbc_cancel_reason AS cancel_reason
		FROM 
				pmbc_reco_chq, 
				pmco_company  
		WHERE	
				pmbc_reco_chq.pmbc_creditor = pmco_company.pmco_code 
			AND (
					((pmbc_reco_chq.pmbc_pdate BETWEEN ' . addSQLParam($params, $periodFromSql) . ' AND ' . addSQLParam($params, $periodToSql) . ')
					)
					AND (pmbc_reco_chq.pmbc_bank=' . addSQLParam($params, $trustAccCode) . ")
					$subQuery " . addSQLParam($params, $subQuery_params, false) . '
				)
				AND pmbc_tenant_refund is NULL 
				AND pmbc_reco_chq.pmbc_pval <> 0
				
				UNION ALL 
		
		SELECT 
		        pmbc_pdate , 
				pmbc_reco_chq.pmbc_chq AS chq_no, 
				pmbc_reco_chq.pmbc_creditor AS payee_code, 
				pmco_company.pmco_name AS payee_name, 
				CONVERT(char(10), pmbc_reco_chq.pmbc_pdate, 103) as cheque_date, 
				0.00 as cheque_amount, 
				CONVERT(char(10), pmbc_reco_chq.pmbc_cppdate, 103) as pres_date,  
				(pmbc_reco_chq.pmbc_cppval * -1) AS pres_value, 
				CONVERT(char(10), pmbc_reco_chq.pmbc_cpdate, 103) AS  cancel_date,
				(pmbc_reco_chq.pmbc_cpval * -1) AS cancel_value,
				((pmbc_reco_chq.pmbc_cpval - isnull(pmbc_reco_chq.pmbc_cppval,0) )*-1) AS unpresented_amount,
				pmbc_reco_chq.pmbc_cancel_by AS cancel_by, 
				pmbc_reco_chq.pmbc_cancel_date AS cancelled_date, 
				pmbc_reco_chq.pmbc_cancel_reason AS cancel_reason
		FROM 
				pmbc_reco_chq, 
				pmco_company  
		WHERE	
				pmbc_reco_chq.pmbc_creditor = pmco_company.pmco_code 
			AND (
					((pmbc_reco_chq.pmbc_pdate BETWEEN ' . addSQLParam($params, $periodFromSql) . ' AND ' . addSQLParam($params, $periodToSql) . ')
					OR (pmbc_reco_chq.pmbc_cpdate BETWEEN ' . addSQLParam($params, $periodFromSql) . ' AND ' . addSQLParam($params, $periodToSql) . ')
					)
					AND (pmbc_reco_chq.pmbc_bank=' . addSQLParam($params, $trustAccCode) . ")
					$subQuery2 " . addSQLParam($params, $subQuery2_params, false) . '
				)
				AND pmbc_tenant_refund is NULL 
				AND pmbc_reco_chq.pmbc_cpval <> 0
		) as summary
				ORDER BY pmbc_pdate asc, chq_no asc,cheque_amount desc
				';

    // echo $sql;// need to bring in cancellations in another period but zero out payment amount OR (pmbc_reco_chq.pmbc_cpdate BETWEEN '$periodFromSql' AND '$periodToSql')
    // if($unPresentOnlyFlag) { $subQuery = " AND (pmbc_ppval = '0' OR (pmbc_reco_chq.pmbc_ppval Is Null))  AND pmbc_cpval = '0'";}
    $result =  $dbh->executeSet($sql, false, true, $params); // echo "RESULT : BEFORE";



    //	if($result)
    //	{
    //		foreach($result as $key=>$row)
    //		{
    //			//echo "BEFORE: ".$result[$key]['unpresented_amount']."<br />";
    //			$strLenPresDate = @strlen($row['pres_date']);
    //			if(($strLenPresDate == 0) && ($row['pres_value'] == 0))
    //			{
    //				if($row['cancel_value'] == '0')	{	$result[$key]['unpresented_amount'] = $row['cheque_amount'];}
    //                if($row['single_cancel_amount'])	{	$result[$key]['unpresented_amount'] = ($row['cheque_amount'] - $row['single_cancel_amount']) ;}
    //			}
    //			//echo "AFTER: ".$result[$key]['unpresented_amount']."<br />";
    //
    //            unset($result[$key]['single_cancel_amount']);
    //		}
    //	}
    // else
    // { $result = false; }

    return $result;
}

function getChqRegisterRefundData($trustAccCode, $periodFromSql, $periodToSql, $unPresentOnlyFlag = false)
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);
    $params = $subQuery_params = $subQuery2_params = [];
    $result = [];
    $subQuery = '';
    $subQuery2 = '';
    if ($unPresentOnlyFlag) {
        $subQuery = ' AND (pmbc_ppdate > ' . addSQLParam($subQuery_params, $periodToSql) . ' OR pmbc_reco_chq.pmbc_ppdate Is Null)';
        $subQuery2 = ' AND (pmbc_cppdate > ' . addSQLParam($subQuery2_params, $periodToSql) . ' OR pmbc_reco_chq.pmbc_cppdate Is Null)';
    }
    $sql = "Select chq_no ,payee_code ,payee_name , cheque_date , cheque_amount , pres_date , pres_value , cancel_date , cancel_value , unpresented_amount, 
    		case  when cancel_value <> 0 then cancel_by else '' end as cancel_by,
    		case  when cancel_value <> 0 then cancelled_date else null end as cancelled_date,
    		case when cancel_value <> 0 then cancel_reason else '' end as cancel_reason
            FROM (
        SELECT pmbc_pdate,
				pmbc_reco_chq.pmbc_chq AS chq_no, 
				pmbc_reco_chq.pmbc_creditor AS payee_code, 
				pmco_company.pmco_name AS payee_name, 
				CONVERT(char(10), pmbc_reco_chq.pmbc_pdate, 103) as cheque_date, 
				pmbc_reco_chq.pmbc_pval as cheque_amount, 
				CONVERT(char(10), pmbc_reco_chq.pmbc_ppdate, 103) as pres_date,  
				pmbc_reco_chq.pmbc_ppval AS pres_value, 
				null AS  cancel_date,
				0 AS cancel_value,
				(pmbc_reco_chq.pmbc_pval - isnull(pmbc_reco_chq.pmbc_ppval,0) ) AS unpresented_amount,
				pmbc_reco_chq.pmbc_cancel_by AS cancel_by, 
				pmbc_reco_chq.pmbc_cancel_date AS cancelled_date, 
				pmbc_reco_chq.pmbc_cancel_reason AS cancel_reason
		FROM 
				pmbc_reco_chq, 
				pmco_company  
		WHERE	
				pmbc_reco_chq.pmbc_creditor = pmco_company.pmco_code 
			AND (
					((pmbc_reco_chq.pmbc_pdate BETWEEN " . addSQLParam($params, $periodFromSql) . ' AND ' . addSQLParam($params, $periodToSql) . ')
					)
					AND (pmbc_reco_chq.pmbc_bank=' . addSQLParam($params, $trustAccCode) . ")
					$subQuery " . addSQLParam($params, $subQuery_params, false) . '
				)
				
				AND pmbc_tenant_refund = 1 
				AND pmbc_reco_chq.pmbc_pval <> 0
				
				UNION ALL 
				
				SELECT pmbc_pdate,
				pmbc_reco_chq.pmbc_chq AS chq_no, 
				pmbc_reco_chq.pmbc_creditor AS payee_code, 
				pmco_company.pmco_name AS payee_name, 
				CONVERT(char(10), pmbc_reco_chq.pmbc_pdate, 103) as cheque_date, 
				0 as cheque_amount, 
				CONVERT(char(10), pmbc_reco_chq.pmbc_cppdate, 103) as pres_date,  
				(pmbc_reco_chq.pmbc_cppval * -1) AS pres_value, 
				CONVERT(char(10), pmbc_reco_chq.pmbc_cpdate, 103) AS  cancel_date,
				(pmbc_reco_chq.pmbc_cpval * -1) AS cancel_value,
				((pmbc_reco_chq.pmbc_cpval - isnull(pmbc_reco_chq.pmbc_cppval,0) )*-1) AS unpresented_amount,
				pmbc_reco_chq.pmbc_cancel_by AS cancel_by, 
				pmbc_reco_chq.pmbc_cancel_date AS cancelled_date, 
				pmbc_reco_chq.pmbc_cancel_reason AS cancel_reason
		FROM 
				pmbc_reco_chq, 
				pmco_company  
		WHERE	
				pmbc_reco_chq.pmbc_creditor = pmco_company.pmco_code 
			AND (
					((pmbc_reco_chq.pmbc_pdate BETWEEN ' . addSQLParam($params, $periodFromSql) . ' AND ' . addSQLParam($params, $periodToSql) . ')
					OR (pmbc_reco_chq.pmbc_cpdate BETWEEN ' . addSQLParam($params, $periodFromSql) . ' AND ' . addSQLParam($params, $periodToSql) . ')
					)
					AND (pmbc_reco_chq.pmbc_bank=' . addSQLParam($params, $trustAccCode) . ")
					$subQuery2 " . addSQLParam($params, $subQuery2_params, false) . '
				)
				
				AND pmbc_tenant_refund = 1 
				AND pmbc_reco_chq.pmbc_cpval <> 0
			) as summary
				ORDER BY pmbc_pdate asc, chq_no asc,cheque_amount desc
				';

    // echo $sql;// need to bring in cancellations in another period but zero out payment amount OR (pmbc_reco_chq.pmbc_cpdate BETWEEN '$periodFromSql' AND '$periodToSql')
    // if($unPresentOnlyFlag) { $subQuery = " AND (pmbc_ppval = '0' OR (pmbc_reco_chq.pmbc_ppval Is Null))  AND pmbc_cpval = '0'";}
    $result =  $dbh->executeSet($sql, false, true, $params); // echo "RESULT : BEFORE";



    //    if($result)
    //    {
    //        foreach($result as $key=>$row)
    //        {
    //            //echo "BEFORE: ".$result[$key]['unpresented_amount']."<br />";
    //            $strLenPresDate = @strlen($row['pres_date']);
    //            if(($strLenPresDate == 0) && ($row['pres_value'] == 0))
    //            {
    //                if($row['cancel_value'] == '0')	{	$result[$key]['unpresented_amount'] = $row['cheque_amount'];}
    //            }
    //            //echo "AFTER: ".$result[$key]['unpresented_amount']."<br />";
    //        }
    //    }
    // else
    // { $result = false; }

    return $result;
}



function getEftRegisterData($trustAccCode, $periodFromSql, $periodToSql, $unPresentOnlyFlag = false)
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);
    $params = $subQuery_params = [];
    $result = [];
    $subQuery = '';
    if ($unPresentOnlyFlag) {
        $subQuery = ' AND (pmbg_ppdate > ' . addSQLParam($subQuery_params, $periodToSql) . " OR ((pmbg_ppval Is Null)) OR (pmbg_ppval = '0'))";
    }

    $sql = 'SELECT pmbg_userref as eft_no, 
			CONVERT(char(10), pmbg_pdate, 103) as eft_date, 
			pmbg_pval as eft_amount, 
			CONVERT(char(10), pmbg_ppdate, 103) as pres_date, 
			pmbg_ppval as pres_value,
			0.00 as unpresented_amount,
			pmbg_cancel_by AS cancel_by, 
			pmbg_cancel_date AS cancel_date, 
			pmbg_cancel_reason AS cancel_reason
	FROM	pmbg_dir_dep  
	WHERE	(pmbg_bank=' . addSQLParam($params, $trustAccCode) . ')
	AND (pmbg_pdate BETWEEN ' . addSQLParam($params, $periodFromSql) . ' AND ' . addSQLParam($params, $periodToSql) . ") 
	$subQuery " . addSQLParam($params, $subQuery_params, false) . '
	AND pmbg_tenant_refund is NULL
	ORDER BY pmbg_pdate ASC, pmbg_userref ASC';
    // echo $sql;
    // if($unPresentOnlyFlag) { $subQuery = " AND (pmbg_ppval = '0' OR (pmbg_ppval Is Null))";}



    $result =  $dbh->executeSet($sql, false, true, $params);
    if ($result) {
        foreach ($result as $key => $row) {
            // echo "BEFORE: ".$result[$key]['unpresented_amount']."<br />";
            $strLenPresDate = @strlen($row['pres_date']);
            if (($strLenPresDate == 0) && ($row['pres_value'] == 0)) {
                $result[$key]['unpresented_amount'] = $row['eft_amount'];
            }
            // echo "AFTER: ".$result[$key]['unpresented_amount']."<br />";
        }
    }
    // else
    // { $result = false; }

    return $result;
}

function getEftRefundRegisterData($trustAccCode, $periodFromSql, $periodToSql, $unPresentOnlyFlag = false)
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);
    $params = $subQuery_params = [];
    $result = [];
    $subQuery = '';
    if ($unPresentOnlyFlag) {
        $subQuery = ' AND (pmbg_ppdate > ' . addSQLParam($subQuery_params, $periodToSql) . " OR ((pmbg_ppval Is Null)) OR (pmbg_ppval = '0'))";
    }

    $sql = 'SELECT pmbg_userref as eft_no, 
			CONVERT(char(10), pmbg_pdate, 103) as eft_date, 
			pmbg_pval as eft_amount, 
			CONVERT(char(10), pmbg_ppdate, 103) as pres_date, 
			pmbg_ppval as pres_value,
			0.00 as unpresented_amount,
			pmbg_cancel_by AS cancel_by, 
			pmbg_cancel_date AS cancel_date, 
			pmbg_cancel_reason AS cancel_reason
	FROM	pmbg_dir_dep  
	WHERE	(pmbg_bank=' . addSQLParam($params, $trustAccCode) . ')
	AND (pmbg_pdate BETWEEN ' . addSQLParam($params, $periodFromSql) . ' AND ' . addSQLParam($params, $periodToSql) . ") 
	$subQuery " . addSQLParam($params, $subQuery_params, false) . '
	AND pmbg_tenant_refund = 1
	ORDER BY pmbg_pdate ASC, pmbg_userref ASC';
    // echo $sql;
    // if($unPresentOnlyFlag) { $subQuery = " AND (pmbg_ppval = '0' OR (pmbg_ppval Is Null))";}



    $result =  $dbh->executeSet($sql, false, true, $params);
    if ($result) {
        foreach ($result as $key => $row) {
            // echo "BEFORE: ".$result[$key]['unpresented_amount']."<br />";
            $strLenPresDate = @strlen($row['pres_date']);
            if (($strLenPresDate == 0) && ($row['pres_value'] == 0)) {
                $result[$key]['unpresented_amount'] = $row['eft_amount'];
            }
            // echo "AFTER: ".$result[$key]['unpresented_amount']."<br />";
        }
    }
    // else
    // { $result = false; }

    return $result;
}


function getDepositRegisterData($trustAccCode, $periodFromSql, $periodToSql, $unPresentOnlyFlag = false)
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);
    $params = $subQuery_params = [];
    $result = [];

    $subQuery = '';
    if ($unPresentOnlyFlag) {
        $subQuery = ' AND ((pmbb_ppdate > ' . addSQLParam($subQuery_params, $periodToSql) . ") OR (pmbb_ppval = '0')) ";
    }

    $sql = 'SELECT pmbb_debtor as debtor_code, 
					pmbb_s_type as payment_method,
					CONVERT(char(10), pmbb_date, 103) AS dep_date, 
					pmbb_pval dep_amount, 
					CONVERT(char(10), pmbb_ppdate, 103) as pres_date, 
					pmbb_ppval as pres_value,
					0.00 as unpresented_amount,
					pmbb_cancel_by AS cancel_by, 
					pmbb_cancel_date AS cancel_date, 
					pmbb_cancel_reason AS cancel_reason
			FROM	pmbb_reco_dep  
			WHERE 
					(pmbb_date BETWEEN ' . addSQLParam($params, $periodFromSql) . ' AND ' . addSQLParam($params, $periodToSql) . ') 
			AND (pmbb_bank=' . addSQLParam($params, $trustAccCode) . ") AND (pmbb_pval <> 0) 
			$subQuery " . addSQLParam($params, $subQuery_params, false) . '
			ORDER BY pmbb_date ASC';

    // Note: removed pmbb_s_ref field and instead using pmbb_s_type now
    // pmbb_s_ref payment_method,
    // if($unPresentOnlyFlag) { $subQuery = " AND pmbb_ppval = '0' ";}
    $result =  $dbh->executeSet($sql, false, true, $params);
    if ($result) {
        foreach ($result as $key => $row) {
            // echo "BEFORE: ".$result[$key]['unpresented_amount']."<br />";
            // initialise vars
            $strLenPresDate = 0;
            $strlenDebCode = 0;
            $paymentMethod = '';
            // get values
            $strLenPresDate = @strlen($row['pres_date']);
            $strLenDebCode = @strlen($row['debtor_code']); // echo "$strLenDebCode - ".$row['debtor_code']."<br />";
            $paymentMethod = @trim($row['payment_method']);

            // check for unpresented amounts
            if (($strLenPresDate == 0) && ($row['pres_value'] == 0)) {
                $result[$key]['unpresented_amount'] = $row['dep_amount'];
            }
            if ($strLenDebCode == 0) {
                $result[$key]['debtor_code'] = 'VARIOUS';
            }

            // change wording of payment method
            if ($paymentMethod == 'DP') {
                $result[$key]['payment_method'] = ' EFT';
            } elseif ($paymentMethod == 'CO') {
                $result[$key]['payment_method'] = ' CHEQUE';
            } elseif ($paymentMethod == 'D') {
                $result[$key]['payment_method'] = ' CHEQUES';
            } elseif ($paymentMethod == 'C') {
                $result[$key]['payment_method'] = ' CASH';
            }
        }
    }
    // else
    // { $result = false; }

    return $result;
}


function getAdjustments($trustAccCode, $periodFromSql, $periodToSql, $unPresentOnlyFlag = false)
{
    global $dbh;
    global $clientDB;
    $params = [];
    $dbh->selectDatabase($clientDB);
    $result = [];

    $subQuery = '';
    $orderBy = ' ORDER BY pmbd_date ASC';
    // if($unPresentOnlyFlag) { $subQuery = " AND pmbb_ppval = '0' ";}
    // (pmbd_date BETWEEN '$periodFromSql' AND '$periodToSql')

    $sql = 'SELECT  CONVERT(char(10), pmbd_date, 103) AS date, 
						CONVERT(char(10), pmbd_remove_date, 103) AS remove_date, 
						pmbd_ref as ref,
						pmbd_desc as adj_desc,
						pmbd_val as adj_amount,
						pmbd_created_by as created_by,
						pmbd_created_at as created_at,
						pmbd_updated_by as modified_by,
						pmbd_updated_at as modified_at
				FROM	pmbd_reco_adj 
				WHERE 
						(pmbd_date <= ' . addSQLParam($params, $periodToSql) . ')  
				AND (pmbd_remove_date IS NULL OR (pmbd_remove_date >= ' . addSQLParam($params, $periodFromSql) . ') )	 				
				AND (pmbd_bank=' . addSQLParam($params, $trustAccCode) . ") 
				$subQuery"; // echo $sql;
    $sql .= ' ' . $orderBy;
    $result =  $dbh->executeSet($sql, false, true, $params);

    return $result;
}


/*function getColumnTotalSql()
{
    $sumChqAmt = 0;
    $sumPresVal = 0;
    $sumCancelVal = 0;
}
*/

function getColumnTotal_DataSet($dataSet, $columnName = 'unpresented_amount')
{
    $total = 0;
    foreach ($dataSet as $key => $row) {
        $total = bcadd($total, $row[$columnName], 2);	/* echo "TOTAL: $total ".$row['payee_code']."<hr />"; */
    }

    return $total;
}


function resetColumn()
{
    global $columnArray;
    $columnArray = [];
}

function addColumn($name, $width)
{
    global $columnArray;
    // if(!is_array($columnArray)) { $columnArray = Array(); }
    $temp = ['width' => $width, 'name' => $name];

    $columnArray[] = $temp;

    return $columnArray;
}


// echo "test";
function newPage($columnNames, $reportHeader1 = '', $reportHeader2 = '', $startNewPage = false, $reportHeader3 = null)
{


    if (! is_array($columnNames)) {
        echo "<div class='infoBox'> Error No column Names supplied</div>";
        exit();
    }

    global $pdf;
    global $currentYCoordDefault;
    global $currentYCoord; // echo "$currentYCoord <br />";
    global $currentXcoord;
    global $pageXCoord;
    global $pageYCoord;
    global $lineHeight;
    global $page;
    global $date;
    global $_fonts;


    global $periodFrom;
    global $periodTo;
    global $logo;
    global $defaultStartX;

    $currentYCoord = $currentYCoordDefault;

    if ($startNewPage) {
        $pdf->begin_page_ext($pageXCoord, $pageYCoord, '');
        $page_header_1 = "$reportHeader1";
        $page_header_2 =  "$reportHeader2";
    } else {
        $page_header_1 =  "$reportHeader1 - (CONT'D)";
        $page_header_2 =  "$reportHeader2";
        $pdf->end_page_ext('');
        $page++;
        // STAR NEW PAGE
        $pdf->begin_page_ext($pageXCoord, $pageYCoord, '');
    }


    // ###################################################################################
    // echo "Y - $currentYCoord <br />";
    $pdf->setFontExt($_fonts['Helvetica-Bold'], 9);
    $pdf->showBoxed("$page_header_1", 100, $currentYCoord, 450, 20, 'center', '');

    $currentYCoord = newLine($currentYCoord, 10);  // echo "Y - $currentYCoord <br />";
    $pdf->showBoxed("$page_header_2", 100, $currentYCoord, 450, 20, 'center', '');

    if ($reportHeader3) {
        $currentYCoord = newLine($currentYCoord, 10);  // echo "Y - $currentYCoord <br />";
        $pdf->showBoxed("$reportHeader3", 100, $currentYCoord, 450, 20, 'center', '');
    }

    $pdf->setFontExt($_fonts['Helvetica'], 10);


    $currentYCoord = newLine($currentYCoord, 40); // echo "Y - $currentYCoord <br />";



    // ###############INSERT LOGO HERE ############################################

    if ($logo) {
        generateLogo();
    }

    // ###############INSERT LOGO HERE ############################################

    // Generate Column Names Here

    if (! isset($startX)) {
        $startX = $defaultStartX;
    }
    $align = 'left';
    global $columnPadding;
    if (! isset($columnPadding)) {
        $columnPadding = 5;
    }
    $realOffset = $startX; // / initialise realOffset here
    $defaultHeight = 45;

    $pdf->setFontExt($_fonts['Helvetica-Bold'], 6);

    foreach ($columnNames as $key => $thisColumn) {
        $thisWidth = $thisColumn['width'];
        $name = $thisColumn['name'];

        $pdf->showBoxed($name, $realOffset, $currentYCoord, $thisWidth, $defaultHeight, $align, '');

        $realOffset = $realOffset + $thisWidth + $columnPadding;  // echo "Y: $currentYCoord   - X: $startX  - WIDTH: $thisWidth- realoffset: $realOffset   <br />";


    }
    // $currentYCoord = newLine($currentYCoord, 20); echo "Y - $currentYCoord <br />";

    $_pdfline = 0;

    if ($reportHeader3) {
        $pdf->setlinewidth(1);
        $pdf->moveto(17, 787);
        $pdf->lineto(581, 787);
        $pdf->stroke();
    } else {
        $pdf->setlinewidth(1);
        $pdf->moveto(17, 797);
        $pdf->lineto(581, 797);
        $pdf->stroke();
    }

    // footer

    $pdf->setFontExt($_fonts['Helvetica'], 7);
    // $pdf->showBoxed ("Printed on $date", 30+9, 2, 275, 30, "left", "");
    $pdf->showBoxed("Page $page", 520 + 9, 2, 75, 30, 'left', '');

    // insert tracc header
    $traccFooter = new TraccFooter('assets/clientLogos/tracc_logo_footer.jpg', 'bankRecReport', A4_PORTRAIT);
    $traccFooter->prerender($pdf);


    // #######################################################################################

    $currentYCoord = 770;
    // $currentXcoord = 50;
    // $lineHeight = 12;

    // echo "<b>END OF NEW PAGE - Y - $currentYCoord : X - $currentXcoord </b><br />";
    return true;


}

// function below checks if a number is truely an integer
function is_int_val($data)
{
    //
    if (is_int($data) === true) {
        return true;
    }
    //
    elseif (is_string($data) === true && is_numeric($data) === true) {
        //
        return strpos($data, '.') === false;
        //
    }

    //
    return false;
    //
}

function printRows($dataSet, $columnArray, $columnWidths, $totalsArray, $reportHeader1, $reportHeader2)
{
    global $pdf;
    global $_fonts;
    global $currentYCoord;
    global $currentXcoord; // echo "<b> DATA PRINT ROWS - Y - $currentYCoord : X - $currentXcoord </b><br />";
    global $pageXCoord;
    global $pageYCoord;
    global $lineHeight;
    global $page;
    global $defaultStartX;

    $numRows = count($dataSet ?? []);
    $rowCount = 0;
    $bgColorCode = 1;


    $pageSuccess = newPage($columnArray, $reportHeader1, $reportHeader2, true); // echo "PAGE SUCCESS - $pageSuccess <br />";


    foreach ($dataSet as $key => $thisRow) {
        // echo "OUT - currentYCoord - $currentYCoord <br />";

        if ($currentYCoord <= 35) {
            // echo "IN - currentYCoord - $currentYCoord <br />";

            $pageSuccess = newPage($columnArray, $reportHeader1, $reportHeader2, false);
        }


        if (! isset($startX)) {
            $startX = $defaultStartX;
        }
        $align = 'left';
        // $columnPadding = 5;
        global $columnPadding;
        if (! isset($columnPadding)) {
            $columnPadding = 5;
        }
        $realOffset = $startX; // / initialise realOffset here

        // echo "COLOR: $bgColorCode <br />";
        $successBg = bgline($bgColorCode, $currentYCoord - 1, $defaultStartX, 563, $lineHeight + 1);
        $firstColumnFlag = true;

        $pdf->setFontExt($_fonts['Helvetica'], 7);

        foreach ($thisRow as $key => $val) {
            if (! isset($columnWidths[$key])) {
                continue;
            }
            $thisWidth = ($columnWidths[$key]) * 1; // to convert to numeric or integer

            // if((is_numeric($val))) { $align = "right"; } else { $align = "left"; }

            // line below applies number formatting only to real numbers with a decimal place
            // this ensures that checque numbers eft numbers are not right aligned

            if ((is_numeric($val)) && (strpos($val, '.') !== false)) {
                $val = formatting($val);
                $align = 'right';
            } else {
                $align = 'left';
            }

            // echo "$val - $thisWidth - $align - $currentYcoord, $lineHeight ";
            $pdf->showBoxed($val, $realOffset, $currentYCoord, $thisWidth, $lineHeight, $align, '');

            // $startX += $thisWidth;
            $firstColumnFlag = false; // to skip number formatting for the first column
            $realOffset = $realOffset + $thisWidth + $columnPadding;  // echo "Y: $currentYCoord   - X: $startX  - WIDTH: $thisWidth- realoffset: $realOffset   <br />";

        }

        if ($bgColorCode == 1) {
            $bgColorCode = 3;
        } else {
            $bgColorCode = 1;
        }

        $currentYCoord = newLine($currentYCoord, 9);

        $rowCount++;

        // echo " $realOffset<hr />";

        if ($rowCount == $numRows) {
            // $currentYCoord = newLine($currentYCoord, 10);

            $pdf->setFontExt($_fonts['Helvetica-Bold'], 6);
            // $currentYCoord = newLine($currentYCoord, -10);
            $realOffset = $startX;

            $pdf->setlinewidth(0.7);
            $pdf->moveto($startX, $currentYCoord);
            $pdf->lineto(578, $currentYCoord);
            $pdf->stroke();

            $currentYCoord = newLine($currentYCoord, 12);

            $pdf->moveto($startX, $currentYCoord);
            $pdf->lineto(578, $currentYCoord);
            $pdf->stroke();

            $currentYCoord = newLine($currentYCoord, -2);

            foreach ($totalsArray as $key => $val) {

                $thisWidth = ($columnWidths[$key]) * 1; // to convert to numeric or integer
                if ($thisWidth == 0) {
                    $thisWidth = 42;
                } // echo "$thisWidth <br />";
                // if((is_numeric($val))) { $align = "right"; } else { $align = "left"; }

                $pdf->showBoxed($val, $realOffset, $currentYCoord, $thisWidth, $lineHeight, 'right', '');

                $realOffset = $realOffset + $thisWidth + $columnPadding;  // echo "Y: $currentYCoord   - X: $startX  - WIDTH: $thisWidth- realoffset: $realOffset   <br />";

            }  // end for each torals Array
        } // end if rowcount

    } // / end for each dataset
} // end function


// use this function for shading alternate rows
function bgline($bgcolorCode, $yCoord, $xCoord, $width, $height)
{

    global $pdf;

    $mode = 'both';
    if ($bgcolorCode == 1) {
        $pdf->setColorExt($mode, 'rgb', 0.95, 0.95, 0.95, 0);
    } elseif ($bgcolorCode == 3) {
        $pdf->setColorExt($mode, 'rgb', 0.90, 0.90, 0.90, 0);
    } else {
        $pdf->setColorExt($mode, 'rgb', 0.99, 0.99, 0.99, 0);
    }


    $pdf->rect($xCoord, $yCoord, $width, $height);

    // bool PDF_rect  (resource $p  , float $x  , float $y  , float $width  , float $height )

    $pdf->fill();

    // set color back to white
    $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);

    return true;
}

function printRows2($dataSet, $columnArray, $columnWidths, $totalsArray, $reportHeader1, $reportHeader2, $reportHeader3)
{
    global $pdf;
    global $_fonts;
    global $currentYCoord;
    global $currentXcoord; // echo "<b> DATA PRINT ROWS - Y - $currentYCoord : X - $currentXcoord </b><br />";
    global $pageXCoord;
    global $pageYCoord;
    global $lineHeight;
    global $page;
    global $defaultStartX;

    $numRows2 = count($dataSet ?? []);
    $rowCount2 = 0;
    $bgColorCode = 1;

    if ($currentYCoord > 50) {
        if ($currentYCoord != 800) {
            $currentYCoord = newLine($currentYCoord, 30);
            $pdf->setFontExt($_fonts['Helvetica-Bold'], 9);
        } else {
            $pdf->begin_page_ext($pageXCoord, $pageYCoord, '');
            $pdf->setFontExt($_fonts['Helvetica-Bold'], 9);

            $pdf->showBoxed($reportHeader1, 100, $currentYCoord, 450, 20, 'center', '');

            $currentYCoord = newLine($currentYCoord, 10);
            $pdf->showBoxed($reportHeader2, 100, $currentYCoord, 450, 20, 'center', '');

            $currentYCoord = newLine($currentYCoord, 10);
        }


        $pdf->showBoxed($reportHeader3, 100, $currentYCoord, 450, 20, 'center', '');

        $currentYCoord = newLine($currentYCoord, 40);

        if (! isset($startX)) {
            $startX = $defaultStartX;
        }
        $align = 'left';
        global $columnPadding;
        if (! isset($columnPadding)) {
            $columnPadding = 5;
        }
        $realOffset = $startX; // / initialise realOffset here
        $defaultHeight = 45;

        $pdf->setFontExt($_fonts['Helvetica-Bold'], 6);

        foreach ($columnArray as $key => $thisColumn) {
            $thisWidth = $thisColumn['width'];
            $name = $thisColumn['name'];

            $pdf->showBoxed($name, $realOffset, $currentYCoord, $thisWidth, $defaultHeight, $align, '');

            $realOffset = $realOffset + $thisWidth + $columnPadding;  // echo "Y: $currentYCoord   - X: $startX  - WIDTH: $thisWidth- realoffset: $realOffset   <br />";


        }
        $currentYCoord = newLine($currentYCoord, -20);

        $pdf->setlinewidth(1);
        $pdf->moveto(17, $currentYCoord + 28);
        $pdf->lineto(581, $currentYCoord + 28);
        $pdf->stroke();
    } else {
        $currentYCoord = newLine($currentYCoord, 30);
    }

    foreach ($dataSet as $key => $thisRow) {
        // echo "OUT - currentYCoord - $currentYCoord <br />";

        if ($currentYCoord <= 35) {
            // echo "IN - currentYCoord - $currentYCoord <br />";

            $pageSuccess = newPage($columnArray, $reportHeader1, $reportHeader2, false, $reportHeader3);
        }


        if (! isset($startX)) {
            $startX = $defaultStartX;
        }
        $align = 'left';
        // $columnPadding = 5;
        global $columnPadding;
        if (! isset($columnPadding)) {
            $columnPadding = 5;
        }
        $realOffset = $startX; // / initialise realOffset here

        // echo "COLOR: $bgColorCode <br />";
        $successBg = bgline($bgColorCode, $currentYCoord - 1, $defaultStartX, 563, $lineHeight + 1);
        $firstColumnFlag = true;

        $pdf->setFontExt($_fonts['Helvetica'], 7);

        foreach ($thisRow as $key => $val) {
            if (! isset($columnWidths[$key])) {
                continue;
            }
            $thisWidth = ($columnWidths[$key]) * 1; // to convert to numeric or integer

            // if((is_numeric($val))) { $align = "right"; } else { $align = "left"; }

            // line below applies number formatting only to real numbers with a decimal place
            // this ensures that checque numbers eft numbers are not right aligned

            if ((is_numeric($val)) && (strpos($val, '.'))) {
                $val = formatting($val);
                $align = 'right';
            } else {
                $align = 'left';
            }

            // echo "$val - $thisWidth - $align - $currentYcoord, $lineHeight ";
            $pdf->showBoxed($val, $realOffset, $currentYCoord, $thisWidth, $lineHeight, $align, '');

            // $startX += $thisWidth;
            $firstColumnFlag = false; // to skip number formatting for the first column
            $realOffset = $realOffset + $thisWidth + $columnPadding;  // echo "Y: $currentYCoord   - X: $startX  - WIDTH: $thisWidth- realoffset: $realOffset   <br />";

        }

        if ($bgColorCode == 1) {
            $bgColorCode = 3;
        } else {
            $bgColorCode = 1;
        }

        $currentYCoord = newLine($currentYCoord, 9);

        $rowCount2++;

        // echo " $realOffset<hr />";

        if ($rowCount2 == $numRows2) {
            // $currentYCoord = newLine($currentYCoord, 10);

            $pdf->setFontExt($_fonts['Helvetica-Bold'], 6);
            // $currentYCoord = newLine($currentYCoord, -10);
            $realOffset = $startX;

            $pdf->setlinewidth(0.7);
            $pdf->moveto($startX, $currentYCoord);
            $pdf->lineto(578, $currentYCoord);
            $pdf->stroke();

            $currentYCoord = newLine($currentYCoord, 12);

            $pdf->moveto($startX, $currentYCoord);
            $pdf->lineto(578, $currentYCoord);
            $pdf->stroke();

            $currentYCoord = newLine($currentYCoord, -2);

            foreach ($totalsArray as $key => $val) {

                $thisWidth = ($columnWidths[$key]) * 1; // to convert to numeric or integer
                if ($thisWidth == 0) {
                    $thisWidth = 42;
                } // echo "$thisWidth <br />";
                // if((is_numeric($val))) { $align = "right"; } else { $align = "left"; }

                $pdf->showBoxed($val, $realOffset, $currentYCoord, $thisWidth, $lineHeight, 'right', '');

                $realOffset = $realOffset + $thisWidth + $columnPadding;  // echo "Y: $currentYCoord   - X: $startX  - WIDTH: $thisWidth- realoffset: $realOffset   <br />";

            }  // end for each torals Array
        } // end if rowcount

    } // / end for each dataset
} // end function

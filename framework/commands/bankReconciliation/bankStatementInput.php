<?php

/**
 * <AUTHOR> <PERSON>
 *
 * @since 2013-01-30
 *
 * @todo ******** - isolate the action for the adjustments [Morph]
 **/
function bankStatementInput(&$context)
{
    $userpermission = new userpermission();
    $result = $userpermission->userHasPageAccess(__FUNCTION__);

    // Page Template
    if (! isPostback()) {
        $view = new MasterPage(userViews(), '/bankReconciliation/bankStatementInput.html');
        $view->setSection($context['module']);
    } else {
        $view = new UserControl(userViews(), '/bankReconciliation/bankStatementInput.html');
    }
    $view->bindAttributesFrom($_REQUEST);

    // Array Call-In
    $view->items['bankList'] = dbGetBankAccounts();

    $bankID = $view->items['bankID'];
    $bankStatementDate = $view->items['bankStatementDate'];
    if (empty($view->items['bankStatementBalance'])) {
        $view->items['bankStatementBalance'] = '0.00';
    }
    if ($bankID) {
        $view->items['lastDate'] = dbGetLastReconciledDate($bankID);
    }
    // elseif (!$bankID AND count ($view->items['bankList']) == 1) $bankID = $view->items['bankID'] = $view->items['bankList'][0]['bankID'];
    elseif (! $bankID) {
        $bankID = $view->items['bankID'] = $view->items['bankList'][0]['bankID'];
    }
    if (empty($view->items['bankStatementDate'])) {
        $view->items['bankStatementDate'] = date('d/m/Y', time() - 86400);
    }
    $view->items['openingBalance'] = 0;
    $showData = false;

    // Action
    switch ($view->items['action']) {
        case 'continue':
            // Validation
            if (empty($bankID) or ! isValid($bankID, TEXT_INT, false)) {
                $validationErrors[] = 'You need to select a ' . ucwords(strtolower($_SESSION['country_default']['trust_account'])) . '.';
            }
            if (empty($bankStatementDate)) {
                $validationErrors[] = 'Bank Statement Date is required.';
            } elseif ($bankStatementDate and ! isValid($bankStatementDate, TEXT_SMARTDATE, false)) {
                $validationErrors[] = 'The date you entered is invalid (dd/mm/yyyy).';
            }
            // No Errors
            if (noErrors($validationErrors)) {
                $showData = true;
            }
            break;
        case 'addAdjustment':
            if (empty($bankID) or ! isValid($bankID, TEXT_INT, false)) {
                $validationErrors[] = 'You need to select a ' . ucwords(strtolower($_SESSION['country_default']['trust_account'])) . '.';
            }
            if ($view->items['adjustmentDate'] and ! isValid($view->items['adjustmentDate'], TEXT_SMARTDATE, false)) {
                $validationErrors[] = 'The adjustment date you have entered is invalid (dd/mm/yyyy).';
            }
            if (empty($view->items['adjustmentValue']) or ! isValid($view->items['adjustmentValue'], TEXT_INT, false)) {
                $validationErrors[] = 'You need to enter an adjustment value.';
            }
            if (empty($view->items['adjustmentDescription'])) {
                $validationErrors[] = 'You need to enter an adjustment description.';
            }
            if (noErrors($validationErrors)) {
                dbAddBankReconAdjustments($bankID, $view->items['adjustmentDate'], $view->items['referenceNumber'], $view->items['adjustmentValue'], $view->items['adjustmentDescription'], dbGetNextSerial());
                $view->items['statusMessage'] = 'The adjustment you have entered has been added.';
            }
            $showData = true;
            break;
        case 'finalise':
            // Validation
            if (empty($bankID) or ! isValid($bankID, TEXT_INT, false)) {
                $validationErrors[] = 'You need to select a ' . ucwords(strtolower($_SESSION['country_default']['trust_account'])) . '.';
            }
            if ($bankStatementDate and ! isValid($bankStatementDate, TEXT_SMARTDATE, false)) {
                $validationErrors[] = 'The date you entered is invalid (dd/mm/yyyy).';
            }
            // No Errors
            if (noErrors($validationErrors)) {
                // Cheques
                if ($view->items['chequeDate'] and is_array($view->items['chequeDate'])) {
                    foreach ($view->items['chequeDate'] as $k => $v) {
                        if ($v != 'present' && $v != 'unpresent') {
                            $recentCheck = dbCheckUnusedCheque($bankID, $k, true);
                            dbUpdateBankReconCheque($bankID, $k, $recentCheck['chequeReference'], $v, $view->items['chequeAmount'][$k]);
                            $view->items['chequeUpdateCount']++;
                            $view->items['updateCount']++;
                        }
                    }
                }
                // EFTs and BPays
                if ($view->items['EFTDate'] and is_array($view->items['EFTDate'])) {
                    foreach ($view->items['EFTDate'] as $k => $v) {
                        if ($v != 'present' && $v != 'unpresent') {
                            dbUpdateBankReconEFT($bankID, $view->items['EFTReferenceNumber'][$k], $k, $v, $view->items['EFTAmount'][$k]);
                            $view->items['EFTUpdateCount']++;
                            $view->items['updateCount']++;
                        }
                    }
                }
                // Deposits
                if ($view->items['depositDate'] and is_array($view->items['depositDate'])) {
                    foreach ($view->items['depositDate'] as $k => $v) {
                        if ($v != 'present' && $v != 'unpresent') {
                            dbUpdateBankReconDeposit($bankID, $k, $view->items['depositIssueDate'][$k], $v, $view->items['depositAmount'][$k]);
                            $view->items['depositUpdateCount']++;
                            $view->items['updateCount']++;
                        }
                    }
                }
                // Adjustments
                if ($view->items['adjustmentValue'] and is_array($view->items['adjustmentValue'])) {
                    dbUpdateBankReconAdjustments($bankID, $view->items['issueDate'], $view->items['referenceNumber'], $view->items['adjustmentValue'], $view->items['adjustmentDescription'], $view->items['adjustmentDeletion']);
                    if ($view->items['adjustmentDeletion'] and is_array($view->items['adjustmentDeletion'])) {
                        $view->items['adjustmentUpdateCount'] = 1;
                        $view->items['updateCount']++;
                    }
                }
                $view->items['statusMessage'] = ($view->items['updateCount']) ? 'The changes you made have been recorded.' : 'No changes were made.';
                $view->items['dataSubmitted'] = true;
            } else {
                $showData = true;
            }
            break;
    }

    if ($showData === true) {
        $view->items['openingBalance'] = dbGetBankStatementOpeningBalance($bankID, $bankStatementDate);
        // Cheques
        $view->items['cheques'] = dbGetBankStatementCheques($bankID, $bankStatementDate);
        foreach ($view->items['cheques'] as $k => $v) {
            $totalChequeAmount = $totalChequeAmount + $v['amount'];
            if ($v['previouslyPresentedDate'] == '' && (toTimestamp($v['previouslyPresentedDate']) <= toTimestamp($bankStatementDate))) {
                $totalUnpresentedCheque = $totalUnpresentedCheque + $v['amount'];
            }

            if ($view->items['cheques'][$k]['amount'] == 0) {
                unset($view->items['cheques'][$k]);
            }
        }
        $view->items['totalChequeAmount'] = $totalChequeAmount;
        // EFTS
        $view->items['EFTs'] = dbGetBankStatementEFTs($bankID, $bankStatementDate);
        foreach ($view->items['EFTs'] as $k => $v) {
            $totalEFTAmount = $totalEFTAmount + $v['amount'];
            if ($v['previouslyPresentedDate'] == '' && (toTimestamp($v['previouslyPresentedDate']) <= toTimestamp($bankStatementDate))) {
                $totalUnpresentedEFT = $totalUnpresentedEFT + $v['amount'];
            }
            if (empty($v['creditorName']) and $v['amount'] > 0) {
                $view->items['EFTs'][$k]['creditorName'] = 'VARIOUS';
            } elseif (empty($v['creditorName']) and $v['amount'] < 0) {
                $view->items['EFTs'][$k]['creditorName'] = 'UNKNOWN - CANCELLED';
            }
        }
        $view->items['totalEFTAmount'] = $totalEFTAmount;
        // Deposits
        $view->items['deposits'] = dbGetBankStatementDeposits($bankID, $bankStatementDate);
        foreach ($view->items['deposits'] as $k => $v) {
            $totalDepositAmount = $totalDepositAmount + $v['amount'];
            if ($v['previouslyPresentedDate'] == '' && (toTimestamp($v['previouslyPresentedDate']) <= toTimestamp($bankStatementDate))) {
                $totalUnpresentedDeposit = $totalUnpresentedDeposit + $v['amount'];
            }
            if (empty($v['debtorName'])) {
                $view->items['deposits'][$k]['debtorName'] = 'CHEQUE(S)';
            }
        }
        $view->items['totalDepositAmount'] = $totalDepositAmount;
        // Adjustments
        $view->items['adjustments'] = dbGetBankStatementAdjustments($bankID, $bankStatementDate);
        foreach ($view->items['adjustments'] as $k => $v) {
            $totalAdjustmentAmount = $totalAdjustmentAmount + $v['amount'];
        }
        $view->items['totalAdjustmentAmount'] = $totalAdjustmentAmount;
        $totalUnpresentedAdjustment = $totalUnpresentedAdjustment + $v['amount'];
        $view->items['totalAdjustments'] = $totalUnpresentedAdjustment;
        // Combo
        $view->items['totalUnpresentedAmount'] = $totalUnpresentedCheque + $totalUnpresentedEFT - $totalDepositAmount;
    }

    // Post Feed
    $view->items['validationErrors'] = $validationErrors;

    // Display
    $view->render();
}

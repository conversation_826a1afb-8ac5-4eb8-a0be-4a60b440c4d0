<?php

/**
 * <AUTHOR>
 *
 * @since 2013-01-07
 **/
function createUpdateHeader(&$context)
{
    $userpermission = new userpermission();
    $result = $userpermission->userHasPageAccess(__FUNCTION__);

    // Page Template
    if (! isPostback()) {
        $view = new MasterPage(userViews(), '/bankReconciliation/createUpdateHeader.html');
        $view->setSection($context['module']);
    } else {
        $view = new UserControl(userViews(), '/bankReconciliation/createUpdateHeader.html');
    }
    $view->bindAttributesFrom($_REQUEST);

    // Array Call-In
    $view->items['bankList'] = dbGetBankAccounts();

    // Default Values
    if (empty($view->items['lastDate'])) {
        $view->items['lastDate'] = date('d/m/Y', time() - 86400);
    }

    // Action
    switch ($view->items['action']) {
        case 'finalise':
            // Validation
            if ($view->items['lastDate'] and ! isValid($view->items['lastDate'], TEXT_SMARTDATE, false)) {
                $validationErrors[] = 'The date you entered is invalid (dd/mm/yyyy).';
            }
            if (empty($view->items['bankID'])) {
                $validationErrors[] = 'You need to select a ' . ucwords(strtolower($_SESSION['country_default']['trust_account'])) . '.';
            }

            if (empty($view->items['bankStatementBalance'])) {
                $validationErrors[] = 'You need to enter a Bank Statement Balance.';
            }
            if (empty($view->items['outstandingDeposits'])) {
                $validationErrors[] = 'You need to enter any Outstanding Deposit amount.';
            }
            if (empty($view->items['unpresentedCheques'])) {
                $validationErrors[] = 'You need to enter any Unpresented Cheque amount.';
            }
            if (empty($view->items['unpresentedEFTs'])) {
                $validationErrors[] = 'You need to enter any Unpresented EFT amount.';
            }
            if (empty($view->items['reconciliationAdjustments'])) {
                $validationErrors[] = 'You need to enter a value for Reconciliation Adjustments.';
            }

            // No Errors

            if ($view->items['bankID']) {
                $bank = dbGetLastReconciledDate($view->items['bankID']);
                if ($bank) {
                    $validationErrors[] = 'A header for the ' . strtolower($_SESSION['country_default']['trust_account']) . ', ' . $view->items['bankID'] . ' , already exist.';
                }
            }

            if (noErrors($validationErrors)) {
                $sqlResult = dbInsertBankReconciliationHeader($view->items['bankID'], $view->items['lastDate'], (int) $view->items['bankStatementBalance'], (int) $view->items['outstandingDeposits'], (int) $view->items['unpresentedCheques'], (int) $view->items['unpresentedEFTs'], (int) $view->items['reconciliationAdjustments']);
                if ($sqlResult) {
                    $view->items['statusMessage'] = 'A header for the ' . strtolower($_SESSION['country_default']['trust_account']) . ': ' . $view->items['bankID'] . ' has been created';
                } else {
                    $validationErrors[] = 'A header for the ' . strtolower($_SESSION['country_default']['trust_account']) . ', ' . $view->items['bankID'] . ' , already exist.';
                }
            }
            break;
    }

    // Post Feed
    $view->items['validationErrors'] = $validationErrors;

    // Display
    $view->render();
}

<?php

if ($fileFormat == FILETYPE_XLS) {
    $report->resetColumns();
    $report->line = 1;
    $report->enableFormatting = true;
    $indexSheet++;
    $report->setSheetDetails($indexSheet, 'Cheque register');
    // $report->setActiveSheetIndex (0);

    $report->renderLineText("Bank Reconciliation - Cheque register $unPresentText", 'styleLineTitle', 15);
    $report->renderLineText("Period From : $periodFrom - To: $periodTo", 'styleLineSubTitle', 15);
    $report->renderLineText(ucwords(strtolower($_SESSION['country_default']['trust_account'])) . ": $trustAccCode($trustAccName)", 'styleLineSubTitle', 15);
    $report->renderHeader();

    $numberFormat = '_(* #,##0.00_);_(* (#,##0.00);_(* "-"??_);_(@_)';

    $report->addColumn('chq_no', 'Chq Number', 104, 'left', '@', $report->styleBold);
    $report->addColumn('payee_code', 'Payee Code', 104, 'left', '@', $report->styleBold);
    $report->addColumn('payee_name', 'Payee Name', 104, 'left', $numberFormat, $report->styleBold);
    $report->addColumn('cheque_date', 'Cheque Date', 104, 'left', '@', $report->styleBold);
    $report->addColumn('cheque_amount', 'Cheque Amount', 104, 'right', $numberFormat, $report->styleBold);
    $report->addColumn('pres_date', 'Presentation Date', 104, 'right', '@', $report->styleBold);
    $report->addColumn('pres_value', 'Presentation Value', 104, 'right', $numberFormat, $report->styleBold);
    $report->addColumn('cancel_date', 'Cancellation Date', 104, 'right', '@', $report->styleBold);
    $report->addColumn('cancel_value', 'Cancellation Value', 104, 'right', $numberFormat, $report->styleBold);
    $report->addColumn('unpresented_amount', 'Unpresent Amount', 104, 'right', $numberFormat, $report->styleBold);
    $report->addColumn('cancel_by', 'Cancelled By', 104, 'left', '@', $report->styleBold);
    $report->addColumn('cancelled_date', 'Cancelled Date', 104, 'left', '@', $report->styleBold);
    $report->addColumn('cancel_reason', 'Cancelled Reason', 104, 'left', '@', $report->styleBold);
    $report->renderHeader();

    $sheet_ind = $c = 0;
    foreach ($report->columns as $cols) {
        $c++;
        $report->setColumnWidth(numberToLetter($c), 'auto');
    }

    if (count($dataSetChq)) {
        $chqAmtTotalRaw = getColumnTotal_DataSet($dataSetChq, 'cheque_amount');
        $presValTotalRaw = getColumnTotal_DataSet($dataSetChq, 'pres_value');
        $cancelValTotalRaw = getColumnTotal_DataSet($dataSetChq, 'cancel_value');   // echo" cancelValTotal :
        $unpresentedTotalRaw = getColumnTotal_DataSet($dataSetChq, 'unpresented_amount');
        $netChqPays = bcadd($chqAmtTotalRaw, $cancelValTotalRaw, 2);

        $report->renderData($dataSetChq);

        $total = ['cheque_amount' => $chqAmtTotalRaw, 'pres_value' => $presValTotalRaw, 'cancel_value' => $cancelValTotalRaw, 'cheque_date' => formatting($netChqPays), 'unpresented_amount' => $unpresentedTotalRaw];
        $report->renderTotal($total);
        $report->renderLine([]);
    }

    if (count($dataSet2Chq)) {
        $report->renderLine_bold(['chq_no' => 'Debtor Refund']);
        $report->renderHeader();

        $chqAmtTotalRaw = getColumnTotal_DataSet($dataSet2Chq, 'cheque_amount');
        $presValTotalRaw = getColumnTotal_DataSet($dataSet2Chq, 'pres_value');
        $cancelValTotalRaw = getColumnTotal_DataSet($dataSet2Chq, 'cancel_value');
        $unpresentedTotalRaw = getColumnTotal_DataSet($dataSet2Chq, 'unpresented_amount');
        $netChqPays = bcadd($chqAmtTotalRaw, $cancelValTotalRaw, 2);

        $report->renderData($dataSet2Chq);

        $total = ['cheque_amount' => $chqAmtTotalRaw, 'pres_value' => $presValTotalRaw, 'cancel_value' => $cancelValTotalRaw, 'cheque_date' => formatting($netChqPays), 'unpresented_amount' => $unpresentedTotalRaw];
        $report->renderTotal($total);
        $report->renderLine([]);
    }

} else {
    $page = 1;
    // $page++;
    // declare this in the main script, not the include ....
    global $pageXCoord;
    $pageXCoord = 595;
    global $pageYCoord;
    $pageYCoord = 842;
    global $currentYCoordDefault;
    $currentYCoordDefault = 800;
    global $currentYCoord;
    $currentYCoord = 800;
    global $currentXcoord;
    $currentXcoord = 50;
    global $lineHeight;
    $lineHeight = 8;
    global $defaultStartX;
    $defaultStartX = 15;
    global $columnPadding;
    $columnPadding = 5;
    $totalsArray = [];
    $columnWidths = [];

    $reportHeader1 = "Bank Reconciliation - Cheque register $unPresentText. Period From : $periodFrom - To: $periodTo";
    $reportHeader2 = ucwords(strtolower($_SESSION['country_default']['trust_account'])) . ": $trustAccCode($trustAccName)";
    $defaultWidth = '42';


    // ADD Columns here NB: MAKe sure number of columns are same in dataSet as well as column indexes
    resetColumn();
    $columnArray = addColumn('Chq Number', $defaultWidth);
    $columnArray = addColumn('Payee Code', $defaultWidth);
    $columnArray = addColumn('Payee Name', '135');
    $columnArray = addColumn('Cheque Date', $defaultWidth);
    $columnArray = addColumn('Cheque Amount', $defaultWidth);
    $columnArray = addColumn('Presentation Date', $defaultWidth);
    $columnArray = addColumn('Presentation Value', $defaultWidth);
    $columnArray = addColumn('Cancellation Date', $defaultWidth);
    $columnArray = addColumn('Cancellation Value', $defaultWidth);
    $columnArray = addColumn('Unpresent Amount', $defaultWidth);

    // print Rows
    $columnWidths =  [
        'chq_no' => $defaultWidth,
        'payee_code' => $defaultWidth,
        'payee_name' => '135',
        'cheque_date' => $defaultWidth,
        'cheque_amount' => $defaultWidth,
        'pres_date' => 38,
        'pres_value' => 46,
        'cancel_date' => 38,
        'cancel_value' => 46,
        'unpresented_amount' => 46,
    ];

    if (count($dataSetChq)) {
        $chqAmtTotalRaw = getColumnTotal_DataSet($dataSetChq, 'cheque_amount');   // echo"<br /> chqAmtTotal : $chqAmtTotal <br />";
        $chqAmtTotal = formatting($chqAmtTotalRaw);

        $presValTotalRaw = getColumnTotal_DataSet($dataSetChq, 'pres_value');   // echo" presValTotal : $presValTotal <br />";
        $presValTotal = formatting($presValTotalRaw);

        $cancelValTotalRaw = getColumnTotal_DataSet($dataSetChq, 'cancel_value');   // echo" cancelValTotal : $cancelValTotal <br />";
        $cancelValTotal = formatting($cancelValTotalRaw);

        $unpresentedTotalRaw = getColumnTotal_DataSet($dataSetChq, 'unpresented_amount');   // echo" <br />unpresentedTotal : $unpresentedTotal <br />";
        $unpresentedTotal = formatting($unpresentedTotalRaw);

        $netChqPays = bcadd($chqAmtTotalRaw, $cancelValTotalRaw, 2); // 2012-09-12: As per task # 476979 [Morph]
        // if ($netChqPays < 0) $netChqPays = 0; // 2012-09-12: As per discussion regarding task # 476979 [Morph]
        $netChqPayments = formatting($netChqPays);

        $defaultVal = '';
        $totalsArray = ['chq_no' => 'TOTALS', 'payee_code' => $defaultVal, 'payee_name' => 'NET PAYMENTS', 'cheque_date' => $netChqPayments, 'cheque_amount' => $chqAmtTotal, 'pres_date' => $defaultVal, 'pres_value' => $presValTotal, 'cancel_date' => $defaultVal, 'cancel_value' => $cancelValTotal, 'unpresented_amount' => $unpresentedTotal];
        // CHANGED ABOVE LINE ON 4 MAY 2009 TO ADD IN NET TOTAL  - REQUIRED MORE FORMATTING
        $successData = printRows($dataSetChq, $columnArray, $columnWidths, $totalsArray, $reportHeader1, $reportHeader2);
    }

    if (count($dataSet2Chq)) {
        $chqAmtTotalRaw = getColumnTotal_DataSet($dataSet2Chq, 'cheque_amount');   // echo"<br /> chqAmtTotal : $chqAmtTotal <br />";
        $chqAmtTotal = formatting($chqAmtTotalRaw);

        $presValTotalRaw = getColumnTotal_DataSet($dataSet2Chq, 'pres_value');   // echo" presValTotal : $presValTotal <br />";
        $presValTotal = formatting($presValTotalRaw);

        $cancelValTotalRaw = getColumnTotal_DataSet($dataSet2Chq, 'cancel_value');   // echo" cancelValTotal : $cancelValTotal <br />";
        $cancelValTotal = formatting($cancelValTotalRaw);

        $unpresentedTotalRaw = getColumnTotal_DataSet($dataSet2Chq, 'unpresented_amount');   // echo" <br />unpresentedTotal : $unpresentedTotal <br />";
        $unpresentedTotal = formatting($unpresentedTotalRaw);

        $netChqPays = bcadd($chqAmtTotalRaw, $cancelValTotalRaw, 2); // 2012-09-12: As per task # 476979 [Morph]
        // if ($netChqPays < 0) $netChqPays = 0; // 2012-09-12: As per discussion regarding task # 476979 [Morph]
        $netChqPayments = formatting($netChqPays);

        $defaultVal = '';
        $totalsArray = ['chq_no' => 'TOTALS', 'payee_code' => $defaultVal, 'payee_name' => 'NET PAYMENTS', 'cheque_date' => $netChqPayments, 'cheque_amount' => $chqAmtTotal, 'pres_date' => $defaultVal, 'pres_value' => $presValTotal, 'cancel_date' => $defaultVal, 'cancel_value' => $cancelValTotal, 'unpresented_amount' => $unpresentedTotal];
        // CHANGED ABOVE LINE ON 4 MAY 2009 TO ADD IN NET TOTAL  - REQUIRED MORE FORMATTING

        $successData = printRows2($dataSet2Chq, $columnArray, $columnWidths, $totalsArray, $reportHeader1, $reportHeader2, 'Debtor Refund');
    }

    $pdf->end_page_ext('');

}

<?php

if ($fileFormat == FILETYPE_XLS) {
    $report->resetColumns();
    $report->line = 1;
    $report->enableFormatting = true;
    $indexSheet++;
    $report->setSheetDetails($indexSheet, 'Adjustments');
    // $report->setActiveSheetIndex (0);

    $report->renderLineText('Bank Reconciliation - Adjustments', 'styleLineTitle', 15);
    $report->renderLineText("Period From : $periodFrom - To: $periodTo", 'styleLineSubTitle', 15);
    $report->renderLineText(ucwords(strtolower($_SESSION['country_default']['trust_account'])) . ": $trustAccCode($trustAccName)", 'styleLineSubTitle', 15);
    $report->renderHeader();

    $numberFormat = '_(* #,##0.00_);_(* (#,##0.00);_(* "-"??_);_(@_)';

    $report->addColumn('date', 'Date', 104, 'left', '@', $report->styleBold);
    $report->addColumn('remove_date', 'Remove Date', 104, 'left', '@', $report->styleBold);
    $report->addColumn('ref', 'Reference', 104, 'left', '@', $report->styleBold);
    $report->addColumn('adj_desc', 'Description', 104, 'left', '@', $report->styleBold);
    $report->addColumn('adj_amount', 'Amount', 104, 'left', $numberFormat, $report->styleBold);
    $report->addColumn('adj_balance', 'Balance', 104, 'right', $numberFormat, $report->styleBold);
    $report->addColumn('created_by', 'Created By', 104, 'left', '@', $report->styleBold);
    $report->addColumn('created_at', 'Created At', 104, 'left', '@', $report->styleBold);
    $report->addColumn('modified_by', 'Modified By', 104, 'left', '@', $report->styleBold);
    $report->addColumn('modified_at', 'Modified At', 104, 'left', '@', $report->styleBold);
    $report->renderHeader();

    $sheet_ind = $c = 0;
    foreach ($report->columns as $cols) {
        $c++;
        $report->setColumnWidth(numberToLetter($c), 'auto');
    }

    if (count($dataSetADJ)) {
        $to_date_str = explode('/', $periodTo);
        $to_date = $to_date_str[2] . '-' . $to_date_str[1] . '-' . $to_date_str[0];
        foreach ($dataSetADJ as $key => $data) {
            $data['adj_balance'] = $data['adj_amount'];
            if ($data['remove_date'] != '') {
                $remove_date_str = explode('/', $data['remove_date']);
                $rem_date = $remove_date_str[2] . '-' . $remove_date_str[1] . '-' . $remove_date_str[0];
                if (strtotime($rem_date) <= strtotime($to_date)) {
                    $data['adj_balance'] = str_pad('-', 30, ' ', STR_PAD_LEFT);
                }
            }
            $dataSetADJ[$key] = $data;
        }

        $adj_balance = getColumnTotal_DataSet($dataSetADJ, 'adj_balance');

        $report->renderData($dataSetADJ);

        $total = ['adj_balance' => $adj_balance];
        $report->renderTotal($total);
        $report->renderLine([]);
    }

} else {
    $page = 1;
    // $page++;
    // declare this in the main script, not the include ....
    global $pageXCoord;
    $pageXCoord = 595;
    global $pageYCoord;
    $pageYCoord = 842;
    global $currentYCoordDefault;
    $currentYCoordDefault = 800;
    global $currentYCoord;
    $currentYCoord = 800;
    global $currentXcoord;
    $currentXcoord = 50;
    global $lineHeight;
    $lineHeight = 8;
    global $defaultStartX;
    $defaultStartX = 20;
    global $columnPadding;
    $columnPadding = 5;
    $totalsArray = [];
    $columnWidths = [];

    $reportHeader1 = "Bank Reconciliation - Adjustments. Period From : $periodFrom - To: $periodTo";
    $reportHeader2 = ucwords(strtolower($_SESSION['country_default']['trust_account'])) . ": $trustAccCode($trustAccName)";
    $defaultWidth = '42';


    // ADD Columns here NB: MAKe sure number of columns are same in dataSet as well as column indexes
    resetColumn();
    $columnArray = addColumn('Date', $defaultWidth);
    $columnArray = addColumn('Remove Date', $defaultWidth);
    $columnArray = addColumn('Reference', $defaultWidth);
    $columnArray = addColumn('Description', '100');
    $columnArray = addColumn(str_pad('Amount', 28, ' ', STR_PAD_LEFT), '60');
    $columnArray = addColumn(str_pad('Balance', 28, ' ', STR_PAD_LEFT), '60');

    // print Rows
    $columnWidths = ['date' => $defaultWidth,
        'remove_date' => $defaultWidth,
        'ref' => $defaultWidth,
        'adj_desc' => '100',
        'adj_amount' => '60',
        'adj_balance' => '60',
    ];

    $to_date_str = explode('/', $periodTo);
    $to_date = $to_date_str[2] . '-' . $to_date_str[1] . '-' . $to_date_str[0];
    foreach ($dataSetADJ as $key => $data) {
        $data['adj_balance'] = $data['adj_amount'];
        if ($data['remove_date'] != '') {
            $remove_date_str = explode('/', $data['remove_date']);
            $rem_date = $remove_date_str[2] . '-' . $remove_date_str[1] . '-' . $remove_date_str[0];
            if (strtotime($rem_date) <= strtotime($to_date)) {
                $data['adj_balance'] = str_pad('-', 30, ' ', STR_PAD_LEFT);
            }
        }
        $dataSetADJ[$key] = $data;
    }
    $adjAmtTotalRaw = getColumnTotal_DataSet($dataSetADJ, 'adj_balance');   // echo"<br /> chqAmtTotal : $chqAmtTotal <br />";
    $adjAmtTotal = formatting($adjAmtTotalRaw);

    $defaultVal = '';

    $totalsArray = ['date' => $defaultVal,
        'remove_date' => $defaultVal,
        'ref' => 'TOTAL   ',
        'adj_desc' => $defaultVal,
        'adj_amount' => $defaultVal,
        'adj_balance' => $adjAmtTotal,
    ];

    $successData = printRows($dataSetADJ, $columnArray, $columnWidths, $totalsArray, $reportHeader1, $reportHeader2);


    $pdf->end_page_ext('');


}

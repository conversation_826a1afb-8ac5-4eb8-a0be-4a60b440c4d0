<?php

function bankRecReport(&$context)
{
    $userpermission = new userpermission();
    $result = $userpermission->userHasPageAccess(__FUNCTION__);

    require_once 'lib/reportLib/reportIncludesAll.php';
    require_once 'functions/bankRecReportFunctions.php';
    require_once 'functions/processFunctions.php';
    global $pdf, $_fonts;

    if (! isPostback()) {
        $view = new MasterPage(userViews(), '/bankReconciliation/bankRecReport.html');
        $view->setSection($context['module']);
    } else {
        $view = new UserControl(userViews(), '/bankReconciliation/bankRecReport.html', '');
    }

    $view->bindAttributesFrom($_REQUEST);

    // echo print_array($view->items,true);

    $view->items['last_error'] = $context['last_error'];

    // ##################################################################################

    $bankAccounts = getBankAccounts();


    if (count($bankAccounts ?? []) == 1) {
        $view->items['text'] = $bankAccounts[0]['acc_code'];
        $view->items['trust_account'] = $bankAccounts[0]['acc_code'];
    }

    $reportTypes = [
        'chq' => 'Cheque Register',
        'eft' => 'EFT Register',
        'dep' => 'Deposit Register',
        'adj' => 'Adjustments',
    ];
    if ($view->items['action'] == 'submit') {
        $PDFtitle = 'Bank Reconcilliation Reports';
        $fileName = 'bankReconciliation_';

        global $periodFrom;
        global $periodTo;
        global $periodDescription;
        global $filenameDescription;
        global $prop_name;
        global $client;
        global $client_street;
        global $client_city;
        global $client_state;
        global $client_postcode;
        global $line;
        global $startline;
        global $date;
        global $page;
        global $pdf;
        global $trustAccCode;
        global $trusAccName;
        global $unPresentOnlyFlag;
        $unPresentOnlyFlag = false; // initialise to false


        $page = 1;
        $date = date('d/m/Y', time());
        $periodFrom = $view->items['from_date'];
        $periodFromSql = toSQLDate($periodFrom);
        $periodTo = $view->items['to_date'];
        $periodToSql = toSQLDate($periodTo);
        $trustAccCode = $view->items['trust_account'];   // echo "CODE:$trustAccCode" ;
        $errorFlag = true;
        $unPresentOnlyFlag = $view->items['unpresentOnly']; // echo $unPresentOnlyFlag;
        $unPresentText = '';
        if ($unPresentOnlyFlag) {
            $unPresentText = '(UNPRESENTED)';
        }

        if (toDateStamp($view->items['to_date']) < toDateStamp($view->items['from_date'])) {
            $validationErrors[] = 'Your to date cannot be before your from date (reporting period).';
        }
        if (! isset($view->items['chq']) && ! isset($view->items['eft']) && ! isset($view->items['dep'])
            && ! isset($view->items['adj']) && ! isset($view->items['cashBook']) && ! isset($view->items['bankBalance'])) {
            $validationErrors[] = 'Please select a report type.';
        }

        if (noErrors($validationErrors)) {
            if (strlen($trustAccCode) > 0) {
                $trustAccName = getBankAccName($trustAccCode);
                $dataSet = [];
                // switch ($view->items['report_type']) {
                //									case 'chq' :
                if (isset($view->items['chq']) && $view->items['chq']) {
                    $dataSetChq = getChqRegisterData(
                        $trustAccCode,
                        $periodFromSql,
                        $periodToSql,
                        $unPresentOnlyFlag
                    ); // echo count($dataSet);
                    $dataSet2Chq = getChqRegisterRefundData(
                        $trustAccCode,
                        $periodFromSql,
                        $periodToSql,
                        $unPresentOnlyFlag
                    ); // echo count($dataSet);
                    if (count($dataSetChq ?? []) > 0 || count($dataSet2Chq ?? []) > 0) {
                        $errorFlag = false;
                    }
                }
                //									break;
                //									case 'eft':
                if (isset($view->items['eft']) && $view->items['eft']) {
                    $dataSetEFT = getEftRegisterData($trustAccCode, $periodFromSql, $periodToSql, $unPresentOnlyFlag);
                    $dataSet2EFT = getEftRefundRegisterData(
                        $trustAccCode,
                        $periodFromSql,
                        $periodToSql,
                        $unPresentOnlyFlag
                    );
                    if (count($dataSetEFT ?? []) > 0 || count($dataSet2EFT ?? []) > 0) {
                        $errorFlag = false;
                    }
                }
                //									break;
                //									case 'dep':
                if (isset($view->items['dep']) && $view->items['dep']) {
                    $dataSetDEP = getDepositRegisterData(
                        $trustAccCode,
                        $periodFromSql,
                        $periodToSql,
                        $unPresentOnlyFlag
                    );
                    if (count($dataSetDEP ?? []) > 0) {
                        $errorFlag = false;
                    }
                }
                //									break;
                //									case 'adj':
                if (isset($view->items['adj']) && $view->items['adj']) {
                    $dataSetADJ = getAdjustments($trustAccCode, $periodFromSql, $periodToSql, $unPresentOnlyFlag);
                    if (count($dataSetADJ ?? []) > 0) {
                        $errorFlag = false;
                    }
                }
                //									break;
                //				 }

                $fileFormat = $view->items['format'];
                if (! $errorFlag && (count($dataSetChq ?? []) || count($dataSet2Chq ?? []) || count(
                    $dataSetEFT ?? []
                ) || count($dataSet2EFT ?? []) || count($dataSetDEP ?? []) || count(
                    $dataSetADJ ?? []
                )) || isset($view->items['cashBook']) || isset($view->items['bankBalance'])) { // echo $trustAccName ;
                    if ($fileFormat == FILETYPE_PDF) {
                        $pdfDownloadLink = initializePDF(
                            $PDFtitle,
                            $fileName,
                            $bankRecReport_path,
                            $bankRecReport_download_link,
                            $trustAccName
                        );
                        $_fonts['Helvetica'] = $pdf->load_font('Helvetica', 'host', '');
                        $_fonts['Helvetica-Bold'] = $pdf->load_font('Helvetica-Bold', 'host', '');
                    } else {
                        $file = 'BankReconciliationReport_' . time() . '.xlsx';
                        $fullPath = $path = "{$pathPrefix}{$clientDirectory}/xlsx/bankRecReport/{$file}";
                        $downloadPath = "{$clientDirectory}/xlsx/bankRecReport/{$file}";

                        $report = new XLSDataReport($fullPath, 'Sheet1');
                        renderDownloadLink($downloadPath);
                    }
                    $indexSheet = -1;
                    //   switch ($view->items['report_type']) {
                    //			case 'chq' :
                    if (isset($view->items['chq']) && $view->items['chq'] && (count($dataSetChq ?? []) || count(
                        $dataSet2Chq
                    ))) {
                        include 'chqReportInclude.php';
                    }
                    //			break;
                    //			case 'eft':
                    if (isset($view->items['eft']) && $view->items['eft'] && (count($dataSet2EFT ?? []) || count(
                        $dataSetEFT ?? []
                    ))) {
                        include 'eftReportInclude.php';
                    }
                    //			break;
                    //			case 'dep':
                    if (isset($view->items['dep']) && $view->items['dep'] && count($dataSetDEP)) {
                        include 'depositReportInclude.php';
                    }
                    //			break;
                    //			case 'adj':
                    if (isset($view->items['adj']) && $view->items['adj'] && count($dataSetADJ)) {
                        include 'adjReportInclude.php';
                    }
                    //			break;
                    // }
                    if (isset($view->items['cashBook']) && $view->items['cashBook']) {
                        include 'cashbookRecon.php';
                    }

                    if (isset($view->items['bankBalance']) && $view->items['bankBalance']) {
                        include 'bankBalanceRecon.php';
                    }

                    if ($fileFormat == FILETYPE_PDF) {
                        closePdfFile();
                    } else {
                        $report->clean();
                        $report->endPage();
                        $report->close();
                    }
                } else {
                    $view->items['statusMessage'] = 'No Transactions found for this date range.';
                }
            } else {
                $view->items['statusMessage'] = 'Error: Please Select A valid Bank Account.';
            }
        }
        $view->items['enableForm'] = true;
    }
    $view->items['validationErrors'] = $validationErrors;
    $view->items['bankAccounts'] = $bankAccounts;
    $view->items['reportTypes'] = $reportTypes;
    $view->items['formatList'] =
    [
        FILETYPE_PDF => 'PDF',
        FILETYPE_XLS => 'Excel Spreadsheet',
    ];

    if (! isset($view->items['format'])) {
        $view->items['chq'] = true;
    }
    if (! isset($view->items['format'])) {
        $view->items['format'] = FILETYPE_PDF;
    }

    $view->render();
}

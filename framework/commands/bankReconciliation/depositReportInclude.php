<?php

if ($fileFormat == FILETYPE_XLS) {
    $report->resetColumns();
    $report->line = 1;
    $report->enableFormatting = true;
    $indexSheet++;
    $report->setSheetDetails($indexSheet, 'Deposit Register');
    // $report->setActiveSheetIndex (0);

    $report->renderLineText("Bank Reconciliation - Deposit Register $unPresentText", 'styleLineTitle', 15);
    $report->renderLineText("Period From : $periodFrom - To: $periodTo", 'styleLineSubTitle', 15);
    $report->renderLineText(ucwords(strtolower($_SESSION['country_default']['trust_account'])) . ": $trustAccCode($trustAccName)", 'styleLineSubTitle', 15);
    $report->renderHeader();

    $numberFormat = '_(* #,##0.00_);_(* (#,##0.00);_(* "-"??_);_(@_)';

    $report->addColumn('debtor_code', 'Debtor Code', 104, 'left', '@', $report->styleBold);
    $report->addColumn('payment_method', 'Payment Method', 104, 'left', '@', $report->styleBold);
    $report->addColumn('dep_date', 'Deposit Date', 104, 'left', '@', $report->styleBold);
    $report->addColumn('dep_amount', 'Deposit Amount', 104, 'right', $numberFormat, $report->styleBold);
    $report->addColumn('pres_date', 'Presentation Date', 104, 'left', '@', $report->styleBold);
    $report->addColumn('pres_value', 'Presentation Value', 104, 'right', $numberFormat, $report->styleBold);
    $report->addColumn('unpresented_amount', 'Unpresent Amount', 104, 'right', $numberFormat, $report->styleBold);
    $report->addColumn('cancel_by', 'Cancelled By', 104, 'left', '@', $report->styleBold);
    $report->addColumn('cancel_date', 'Cancelled Date', 104, 'left', '@', $report->styleBold);
    $report->addColumn('cancel_reason', 'Cancelled Reason', 104, 'left', '@', $report->styleBold);
    $report->renderHeader();

    $sheet_ind = $c = 0;
    foreach ($report->columns as $cols) {
        $c++;
        $report->setColumnWidth(numberToLetter($c), 'auto');
    }

    if (count($dataSetDEP)) {
        $depAmtTotalRaw = getColumnTotal_DataSet($dataSetDEP, 'dep_amount');
        $presValTotalRaw = getColumnTotal_DataSet($dataSetDEP, 'pres_value');
        $unpresentedTotalRaw = getColumnTotal_DataSet($dataSetDEP, 'unpresented_amount');   // echo" <

        $report->renderData($dataSetDEP);

        $total = ['dep_amount' => $depAmtTotalRaw, 'unpresented_amount' => $unpresentedTotalRaw, 'pres_value' => $presValTotalRaw];
        $report->renderTotal($total);
        $report->renderLine([]);
    }

} else {
    $page = 1;
    // $page++;
    // declare this in the main script, not the include ....
    global $pageXCoord;
    $pageXCoord = 595;
    global $pageYCoord;
    $pageYCoord = 842;
    global $currentYCoordDefault;
    $currentYCoordDefault = 800;
    global $currentYCoord;
    $currentYCoord = 800;
    global $currentXcoord;
    $currentXcoord = 50;
    global $lineHeight;
    $lineHeight = 8;
    global $defaultStartX;
    $defaultStartX = 20;
    global $columnPadding;
    $columnPadding = 5;
    $totalsArray = [];
    $columnWidths = [];

    $reportHeader1 = "Bank Reconciliation - Deposit Register $unPresentText. Period From : $periodFrom - To: $periodTo";
    $reportHeader2 = ucwords(strtolower($_SESSION['country_default']['trust_account'])) . ": $trustAccCode($trustAccName)";
    $defaultWidth = '55';


    // ADD Columns here NB: MAKe sure number of columns are same in dataSet as well as column indexes
    resetColumn();
    $columnArray = addColumn('DebtorCode', $defaultWidth);
    $columnArray = addColumn('Payment Method', '100');
    $columnArray = addColumn('Deposit Date', $defaultWidth);
    $columnArray = addColumn('Deposit Amount', $defaultWidth);
    $columnArray = addColumn('Presentation Date', $defaultWidth);
    $columnArray = addColumn('Presentation Value', $defaultWidth);
    $columnArray = addColumn('Unpresent Amount', $defaultWidth);


    // print Rows

    $columnWidths = ['debtor_code' => $defaultWidth,
        'payment_method' => '100',
        'dep_date' => $defaultWidth,
        'dep_amount' => $defaultWidth,
        'pres_date' => $defaultWidth,
        'pres_value' => $defaultWidth,
        'unpresented_amount' => $defaultWidth,
    ];

    foreach ($dataSetDEP as $key => $thisRow) {

        // echo $key;  echo "<hr />";
    }


    $depAmtTotalRaw = getColumnTotal_DataSet($dataSetDEP, 'dep_amount');   // echo"<br /> chqAmtTotal : $chqAmtTotal <br />";
    $depAmtTotal = formatting($depAmtTotalRaw);

    $presValTotalRaw = getColumnTotal_DataSet($dataSetDEP, 'pres_value');   // echo" presValTotal : $presValTotal <br />";
    $presValTotal = formatting($presValTotalRaw);

    $unpresentedTotalRaw = getColumnTotal_DataSet($dataSetDEP, 'unpresented_amount');   // echo" <br />unpresentedTotal : $unpresentedTotal <br />";
    $unpresentedTotal = formatting($unpresentedTotalRaw);

    $defaultVal = '';

    $totalsArray = ['debtor_code' => $defaultVal,
        'payment_method' => 'TOTALS ',
        'dep_date' => $defaultVal,
        'dep_amount' => $depAmtTotal,
        'pres_date' => $defaultVal,
        'pres_value' => $presValTotal,
        'unpresented_amount' => $unpresentedTotal,
    ];

    $successData = printRows($dataSetDEP, $columnArray, $columnWidths, $totalsArray, $reportHeader1, $reportHeader2);



    $pdf->end_page_ext('');


}

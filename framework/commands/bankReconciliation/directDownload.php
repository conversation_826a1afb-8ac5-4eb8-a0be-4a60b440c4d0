<?php

function directDownload(&$context)
{
    global $pathPrefix, $clientDirectory;
    if (! isPostback()) {
        $view = new MasterPage(userViews(), '/bankReconciliation/directDownload.html');
        $view->setSection($context['module']);
    } else {
        $view = new UserControl(userViews(), '/bankReconciliation/directDownload.html');
    }

    $view->bindAttributesFrom($context);
    $view->bindAttributesFrom($_REQUEST);

    if ($view->items['action'] == 'directDownload') {
        if (empty($view->items['username'])) {
            $validationErrors[] = 'You need to enter your MAC Username.';
        }
        if (empty($view->items['password'])) {
            $validationErrors[] = 'Password field is required.';
        }
        if (empty($view->items['customerNumber'])) {
            $validationErrors[] = 'You need to supply your customer number.';
        }
        if (empty($view->items['fileDate'])) {
            $validationErrors[] = 'You need to supply the date.';
        }
        switch ($view->items['fileType']) {
            case 'B':
                $fileType = FILE_B;
                $appType = 'bpay';
                break;
            case 'T':
                $fileType = FILE_T;
                $appType = 'transaction';
                break;
            case 'P':
                $fileType = FILE_P;
                $appType = 'payment';
                break;
            case 'A':
                $fileType = FILE_A;
                $appType = 'acc';
                break;
            default:
                $validationErrors[] = 'Invalid file type.';
                break;
        }

        if (noErrors($validationErrors)) {
            $headers = [
                'Content-Type: application/mbldeft-' . $appType,
                'Content-Length: 89',
                'Content-Disposition: attachment; filename=' . str_replace('-', '', $view->items['fileDate'] . '.' . $fileType),
            ];

            $soap_request = "
                <form class=\"data-grid\">
                    <table border=\"0\">
                        <tbody>
                            <tr class=\"<?=alternateNextRow()?>\">
                                <td class=\"title\"><label for=\"user\">Username (MAC): </label></td>
                                <td class=\"required\">*</td>
                                <td><?=renderTextbox ('username', $view->items['username'])?></td>
                            </tr>
                            <tr class=\"<?=alternateNextRow()?>\">
                                <td class=\"title\"><label for=\"password\">Password: </label></td>
                                <td class=\"required\">*</td>
                                <td><?=renderPasswordBox ('password', $view->items['password'])?></td>
                            </tr>
                            <tr class=\"<?=alternateNextRow()?>\">
                                <td class=\"title\"><label for=\"customerNumber\">Client/Customer Number: </label></td>
                                <td class=\"required\">*</td>
                                <td><?=renderTextbox ('customerNumber', $view->items['customerNumber'])?></td>
                            </tr>
                            <tr class=\"<?=alternateNextRow()?>\">
                                <td class=\"title\"><label for=\"fileType\">File Type: </label></td>
                                <td class=\"required\">*</td>
                                <td><?=renderTextbox ('fileType',$view->items['fileType'])?></td>
                            </tr>
                            <tr class=\"<?=alternateNextRow()?>\">
                                <td class=\"title\"><label for=\"fileDate\">Date (YYYY-MM-DD): </label></td>
                                <td class=\"required\">*</td>
                                <td><?=renderTextbox ('fileDate', $view->items['fileDate'])?></td>
                            </tr>
                            <tr class=\"<?=alternateNextRow()?>\">
                                <td class=\"title\"></td>
                                <td class=\"required\"></td>
                                <td><? renderButton('btnRequest','Download &darr;')\"'); ?></td>
                            </tr>
                        </tbody>
                    </table>
                    </form>

            ";

            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, MBL_DD . 'username=' . $view->items['username'] . '&password=' . $view->items['password'] . '&customerNumber=' . $view->items['customerNumber'] . '&fileDate=' . $view->items['fileDate'] . '&fileType=' . $view->items['fileType']);
            curl_setopt($ch, CURLOPT_CERTINFO, CACERT);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 15);
            curl_setopt($ch, CURLOPT_TIMEOUT, 15);
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
            curl_setopt($ch, CURLOPT_VERBOSE, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $soap_request);

            $contents  = curl_exec($ch);

            $_filePath =  "{$pathPrefix}{$clientDirectory}/$fileType/" . DOC_DIRECTDOWNLOAD . '/';
            $_downloadPath =  "{$clientDirectory}/$fileType/" . DOC_DIRECTDOWNLOAD;
            $filePath = $_filePath . $clientDirectory . str_replace('-', '', $view->items['fileDate'] . '.' . $fileType);
            checkDirPath($_filePath);
            $downloadPath = "{$_downloadPath}/" . $clientDirectory . str_replace('-', '', $view->items['fileDate'] . '.' . $fileType);

            if (is_writable($pathPrefix)) {
                $destination = $_filePath . $clientDirectory . str_replace('-', '', $view->items['fileDate'] . '.' . $fileType);
                if (! $ch = fopen($destination, 'w+')) {
                    $view->items['response'] = "Cannot open file ($destination)";
                    exit;
                }
                if (fwrite($ch, $contents) === false) {
                    $view->items['response'] = "Cannot write to file ($destination)";
                    exit;
                }
                $view->items['success'] = $_filePath . $clientDirectory . str_replace('-', '', $view->items['fileDate'] . '.' . $fileType);
                $view->items['downloadPath'] = $downloadPath;
                fclose($ch);
            } else {
                $view->items['readonly'] = 'The file ' . $clientDirectory . str_replace('-', '', $view->items['fileDate'] . '.' . $fileType) . ' is not writable';
            }
        }
        $view->items['validationErrors'] = $validationErrors;
    }
    $view->render();


}

function formatXmlString($xml)
{
    $xml = preg_replace('/(>)(<)(\/*)/', "$1\n$2$3", $xml);
    $token      = strtok($xml, "\n");
    $result     = '';
    $pad        = 0;
    $matches    = [];
    while ($token !== false) {
        if (preg_match('/.+<\/\w[^>]*>$/', $token, $matches)) {
            $indent = 0;
        } elseif (preg_match('/^<\/\w/', $token, $matches)) {
            $pad--;
            $indent = 0;
        } elseif (preg_match('/^<\w[^>]*[^\/]>.*$/', $token, $matches)) {
            $indent = 1;
        } else {
            $indent = 0;
        }
        $line    = str_pad($token, strlen($token) + $pad, ' ', STR_PAD_LEFT);
        $result .= $line . "\n";
        $token   = strtok("\n");
        $pad    += $indent;
    }

    return $result;
}

<?php

if ($fileFormat == FILETYPE_XLS) {
    $report->resetColumns();
    $report->line = 1;
    $report->enableFormatting = true;
    $indexSheet++;
    $report->setSheetDetails($indexSheet, 'EFT register');
    // $report->setActiveSheetIndex (0);

    $report->renderLineText("Bank Reconciliation - EFT register $unPresentText", 'styleLineTitle', 15);
    $report->renderLineText("Period From : $periodFrom - To: $periodTo", 'styleLineSubTitle', 15);
    $report->renderLineText(ucwords(strtolower($_SESSION['country_default']['trust_account'])) . ": $trustAccCode($trustAccName)", 'styleLineSubTitle', 15);
    $report->renderHeader();

    $numberFormat = '_(* #,##0.00_);_(* (#,##0.00);_(* "-"??_);_(@_)';

    $report->addColumn('eft_no', 'Eft Number', 104, 'left', '@', $report->styleBold);
    $report->addColumn('eft_date', 'Eft Date', 104, 'left', '@', $report->styleBold);
    $report->addColumn('eft_amount', 'Eft Amount', 104, 'right', $numberFormat, $report->styleBold);
    $report->addColumn('pres_date', 'PresentationDate', 104, 'left', '@', $report->styleBold);
    $report->addColumn('pres_value', 'PresentationValue', 104, 'right', $numberFormat, $report->styleBold);
    $report->addColumn('unpresented_amount', 'Unpresent Amount', 104, 'right', $numberFormat, $report->styleBold);
    $report->addColumn('cancel_by', 'Cancelled By', 104, 'left', '@', $report->styleBold);
    $report->addColumn('cancel_date', 'Cancelled Date', 104, 'left', '@', $report->styleBold);
    $report->addColumn('cancel_reason', 'Cancelled Reason', 104, 'left', '@', $report->styleBold);
    $report->renderHeader();

    $sheet_ind = $c = 0;
    foreach ($report->columns as $cols) {
        $c++;
        $report->setColumnWidth(numberToLetter($c), 'auto');
    }

    if (count($dataSetEFT)) {
        $eftAmtTotalRaw = getColumnTotal_DataSet($dataSetEFT, 'eft_amount');
        $presValTotalRaw = getColumnTotal_DataSet($dataSetEFT, 'pres_value');
        $unpresentedTotalRaw = getColumnTotal_DataSet($dataSetEFT, 'unpresented_amount');

        $report->renderData($dataSetEFT);

        $total = ['eft_amount' => $eftAmtTotalRaw, 'unpresented_amount' => $unpresentedTotalRaw, 'pres_value' => $presValTotalRaw];
        $report->renderTotal($total);
        $report->renderLine([]);
    }

    if (count($dataSet2EFT)) {
        $report->renderLine_bold(['eft_no' => 'Debtor Refund']);
        $report->renderHeader();
        $report->renderData($dataSet2EFT);

        $eftAmtTotalRaw = getColumnTotal_DataSet($dataSet2EFT, 'eft_amount');
        $presValTotalRaw = getColumnTotal_DataSet($dataSet2EFT, 'pres_value');
        $unpresentedTotalRaw = getColumnTotal_DataSet($dataSet2EFT, 'unpresented_amount');

        $total = ['eft_amount' => $eftAmtTotalRaw, 'unpresented_amount' => $unpresentedTotalRaw, 'pres_value' => $presValTotalRaw];
        $report->renderTotal($total);
    }

} else {
    $page = 1;
    // $page++;
    // declare this in the main script, not the include ....
    global $pageXCoord;
    $pageXCoord = 595;
    global $pageYCoord;
    $pageYCoord = 842;
    global $currentYCoordDefault;
    $currentYCoordDefault = 800;
    global $currentYCoord;
    $currentYCoord = 800;
    global $currentXcoord;
    $currentXcoord = 50;
    global $lineHeight;
    $lineHeight = 8;
    global $defaultStartX;
    $defaultStartX = 20;
    global $columnPadding;
    $columnPadding = 5;
    $totalsArray = [];
    $columnWidths = [];

    $reportHeader1 = "Bank Reconciliation - EFT register $unPresentText. Period From : $periodFrom - To: $periodTo";
    $reportHeader2 = ucwords(strtolower($_SESSION['country_default']['trust_account'])) . ": $trustAccCode($trustAccName)";
    $defaultWidth = '42';


    // ADD Columns here NB: MAKe sure number of columns are same in dataSet as well as column indexes
    resetColumn();
    $columnArray = addColumn('Eft Number', $defaultWidth);
    $columnArray = addColumn('Eft Date', $defaultWidth);
    $columnArray = addColumn('Eft Amount', $defaultWidth);
    $columnArray = addColumn('Presentation Date', $defaultWidth);
    $columnArray = addColumn('Presentation Value', $defaultWidth);
    $columnArray = addColumn('Unpresent Amount', $defaultWidth);


    // print Rows
    $columnWidths = ['eft_no' => $defaultWidth,
        'eft_date' => $defaultWidth,
        'eft_amount' => $defaultWidth,
        'pres_date' => $defaultWidth,
        'pres_value' => $defaultWidth,
        'unpresented_amount' => $defaultWidth,
    ];

    if (count($dataSetEFT)) {
        $eftAmtTotalRaw = getColumnTotal_DataSet($dataSetEFT, 'eft_amount');   // echo"<br /> chqAmtTotal : $chqAmtTotal <br />";
        $eftAmtTotal = formatting($eftAmtTotalRaw);

        $presValTotalRaw = getColumnTotal_DataSet($dataSetEFT, 'pres_value');   // echo" presValTotal : $presValTotal <br />";
        $presValTotal = formatting($presValTotalRaw);

        // $cancelValTotalRaw = getColumnTotal_DataSet($dataSetEFT, "cancel_value");   //echo" cancelValTotal : $cancelValTotal <br />";
        // $cancelValTotal = formatting($cancelValTotalRaw);

        $unpresentedTotalRaw = getColumnTotal_DataSet($dataSetEFT, 'unpresented_amount');   // echo" <br />unpresentedTotal : $unpresentedTotal <br />";
        $unpresentedTotal = formatting($unpresentedTotalRaw);



        $defaultVal = '';



        $totalsArray = ['eft_no' => $defaultVal,
            'eft_date' => $defaultVal,
            'eft_amount' => $eftAmtTotal,
            'pres_date' => $defaultVal,
            'pres_value' => $presValTotal,
            'unpresented_amount' => $unpresentedTotal,
        ];

        $successData = printRows($dataSetEFT, $columnArray, $columnWidths, $totalsArray, $reportHeader1, $reportHeader2);

    }

    if (count($dataSet2EFT)) {
        $eftAmtTotalRaw = getColumnTotal_DataSet($dataSet2EFT, 'eft_amount');   // echo"<br /> chqAmtTotal : $chqAmtTotal <br />";
        $eftAmtTotal = formatting($eftAmtTotalRaw);

        $presValTotalRaw = getColumnTotal_DataSet($dataSet2EFT, 'pres_value');   // echo" presValTotal : $presValTotal <br />";
        $presValTotal = formatting($presValTotalRaw);

        // $cancelValTotalRaw = getColumnTotal_DataSet($dataSet2EFT, "cancel_value");   //echo" cancelValTotal : $cancelValTotal <br />";
        // $cancelValTotal = formatting($cancelValTotalRaw);

        $unpresentedTotalRaw = getColumnTotal_DataSet($dataSet2EFT, 'unpresented_amount');   // echo" <br />unpresentedTotal : $unpresentedTotal <br />";
        $unpresentedTotal = formatting($unpresentedTotalRaw);



        $defaultVal = '';



        $totalsArray = ['eft_no' => $defaultVal,
            'eft_date' => $defaultVal,
            'eft_amount' => $eftAmtTotal,
            'pres_date' => $defaultVal,
            'pres_value' => $presValTotal,
            'unpresented_amount' => $unpresentedTotal,
        ];

        $successData = printRows2($dataSet2EFT, $columnArray, $columnWidths, $totalsArray, $reportHeader1, $reportHeader2, 'Debtor Refund');

    }


    $pdf->end_page_ext('');


}

<?php

if (! defined('INCOME')) {
    define('INCOME', 'I');
}
if (! defined('EXPENDITURE')) {
    define('EXPENDITURE', 'E');
}
if (! defined('LEASE_EXISTING')) {
    define('LEASE_EXISTING', 1);
}
if (! defined('LEASE_NEW')) {
    define('LEASE_NEW', 2);
}
if (! defined('LEASE_ASSIGNMENT')) {
    define('LEASE_ASSIGNMENT', 3);
}
if (! defined('LEASE_RENEWAL')) {
    define('LEASE_RENEWAL', 4);
}
if (! defined('LEASE_OWNERSHIP')) {
    define('LEASE_OWNERSHIP', 5);
}

function simpleWordDate($denomination, $value = 0)
{
    $output = '';
    if ($value > 0) {
        $output = '<strong>' . $value . '</strong> ' . $denomination;
    }
    if ($value > 1) {
        $output .= 's';
    }

    return $output;
}

function home(&$context)
{
    global $stateList, $taskClass, $taskDescription;

    if (! isPostback()) {
        $view = new MasterPage(userViews(), '/leases/home.html');
        $view->setSection($context['module']);
        if (isset($_GET['propertyID'])) {
            $view->items['propertyID'] = $_GET['propertyID'];
            $view->items['leaseID'] = $_GET['leaseID'];
            $view->items['requestID'] = $_GET['requestID'];
            $view->items['selected'] = true;
            $view->items['selectionMethod'] = 'property';
        }
    } else {
        $view = new UserControl(userViews(), '/leases/home.html', '');
    }

    $view->bindAttributesFrom($context);
    $view->bindAttributesFrom($_REQUEST);

    // redirect to new manage lease page for UK SERVER
    if (CDF_COUNTRY_CODE == 'GB') {
        if ($view->items['propertyID'] and $view->items['leaseID']) {
            header('Location: ' . HTTPHOST . '/framework/index.php?module=leases&command=lease_page_v2&property_code=' . $view->items['propertyID'] . '&lease_code=' . $view->items['leaseID']);
        } else {
            header('Location: ' . HTTPHOST . '/framework/index.php?module=leases&command=lease_page_v2');
        }
    }

    $view->items['validationErrors'] = [];
    if ($context['assignment']) {
        $view->bindAttributesFrom($context);
    }
    if ($context['renewal'] || $view->items['renewal'] == 1) {
        $view->items['renewal'] = 1;
    } else {
        $view->items['renewal'] = 0;
    }

    //    echo $view->items['leaseStatus'];
    if ($view->items['action'] == 'adjustEntryMethod') {
        $leaseEntryType = $view->items['leaseEntryType'];
        $view->items = [];
        $view->items['leaseEntryType'] = $leaseEntryType;
        if ($leaseEntryType == LEASE_NEW) {
            $view->items['leaseStatus'] = 'L';
        }
    }


    // Default (no condition)
    $view->items['last_error'] = $context['last_error'];
    $view->items['statusMessage'] = $context['statusMessage'];

    $view->items['chargeTypes'] =
    [
        'D' => 'Debtor',
        'S' => 'Supplier',
    ];
    $view->items['intro'] = 'Please fill out each section completely before submitting this form.';
    $view->items['leaseEntryTypeList'] =
    [
        LEASE_EXISTING => 'Existing Lease',
        LEASE_NEW => 'New Lease',
    ];
    $view->items['leaseFormTypesList'] =
    [
        LEASE_NEW => 'New Lease',
        LEASE_ASSIGNMENT => 'Lease Assignment',
        LEASE_RENEWAL => 'Lease Renewal',
        LEASE_OWNERSHIP => ucwords($_SESSION['country_default']['strata']) . ' Change of Ownership',
    ];

    $view->items['propertySourceList'] =
    [
        4 => 'Draft',
        DATA_LIVE => 'Processed',
        DATA_TEMP => 'Unprocessed',
        3 => 'Rejected',

    ];
    $view->items['statusList'] =
    [
        'C' => 'Current',
        'L' => 'Vacant',
    ];
    $view->items['taskTypeList'] =
    [
        1 => 'New Lease',
        2 => 'Lease Renewal',
    ];
    $view->items['yesNoOption'] =
    [
        'Yes' => 'Yes',
        'No' => 'No',
    ];
    $view->items['insideActOption'] =
    [
        '1' => 'Inside',
        '0' => 'Outside',
    ];

    $view->items['YesNo'] =
    [
        1 => 'Yes',
        0 => 'No',
    ];

    $view->items['communicationHistory'] = [
        'E' => 'Email',
        'S' => 'SMS',
    ];


    // Default (database)
    $view->items['accountList'] = dbGetGroupedAccountsByCodeandName(INCOME);
    $view->items['countryList'] = mapParameters(dbGetCountries(), 'countryCode', 'countryName');
    $view->items['accountGroupList'] = [
        'INCOWN' => 'Owners Income',
        'INCDR' => 'Directly Recoverable Income',
        'INCVO' => ucwords(strtolower($_SESSION['country_default']['variable_outgoings'])) . ' Income',
        'EXPOWN' => 'Owners Expenses',
        'EXPDR' => 'Directly Recoverable Expenses',
        'EXPVO' => ucwords(strtolower($_SESSION['country_default']['variable_outgoings'])) . ' Expenses'];

    // Default (with condition)
    if ($view->items['action'] == 'nextStep') {
        $view->items['step'] = $_GET['step'];
    }

    if (! isset($view->items['leaseFormType'])) {
        $view->items['leaseFormType'] = LEASE_NEW;
    }
    if (! isset($view->items['leaseEntryType'])) {
        $view->items['leaseEntryType'] = LEASE_EXISTING;
    }
    if ((! isset($view->items['propertyID'])) && ! isAdmin($context)) {
        $view->items['leaseEntryType'] = LEASE_NEW;
    }

    if (! array_key_exists($view->items['chargeInterest'], $view->items['yesNoOption'])) {
        $view->items['chargeInterest'] = 'No';
    }
    if (! array_key_exists($view->items['leaseInsideAct'], $view->items['insideActOption'])) {
        $view->items['leaseInsideAct'] = '0';
    }
    if (empty($view->items['interestRate'])) {
        $view->items['interestRate'] = 0.00;
    }
    if (empty($view->items['invoiceDescription'])) {
        $view->items['invoiceDescription'] = 'Interest on Arrears';
    }


    if (isset($view->items['interestAccount2'])) {
        $view->items['interestAccount2'] = deserializeParameters($view->items['interestAccount2']);
    }

    // Transform
    $view->items['leaseID'] = strtoupper($view->items['leaseID']);

    if ($view->items['leaseEntryType'] == LEASE_EXISTING) {
        /* save the view state to context for use in the tenant selection fragment */
        if (isAdmin($context)) {
            $context = $view->items;
            $context['container'] = 'content';
            $context['command'] = 'home';
            $context['module'] = 'leases';
            $context['otherDataSource'] = (! isAdmin($context));
            if ($view->items['taskType'] == 2) {
                $context['otherDataSource'] = (isAdmin($context));
            }
            /* pass the context back into view state */
            $view->items['UserControl:LeaseSelector'] = fetchCommand('leaseSelector', 'leases');
            $view->bindAttributesFrom($context);
        } else {
            $view->items['propertyList'] = (isAdmin($context)) ? dbPropertyList(true, null, 1) : arrayUnique(dbCombinedPropertyList());
            $view->items['leaseCheck'] = true;
        }

    } else {
        $view->items['propertyList'] = (isAdmin($context)) ? dbPropertyList(true, null, 1) : arrayUnique(dbCombinedPropertyList(true, true));
        if (isAdmin($context)) {
            $context = $view->items;
            $context['container'] = 'content';
            $context['command'] = 'home';
            $context['module'] = 'leases';
            $context['otherDataSource'] = (! isAdmin($context));
            $view->items['leaseCheck'] = true;
            $view->bindAttributesFrom($context);
        }
    }

    $view->items['propertySource'] = dbCheckProperty($view->items['propertyID']);
    // -- process any actions that have been registered
    $view->items['validationErrors'] = [];
    switch ($view->items['action']) {
        case 'computeLeaseTerm':
            [$lease['leaseTermDays'], $lease['leaseTermMonths'], $lease['leaseTermYears']] = dateDiff($view->items['leaseCommencementDate'], $view->items['leaseExpiryDate']);
            $leaseTerm = [];
            $leaseTerm[0] = simpleWordDate('year', $lease['leaseTermYears']);
            $leaseTerm[1] = simpleWordDate('month', $lease['leaseTermMonths']);
            $leaseTerm[2] = simpleWordDate('day', $lease['leaseTermDays']);
            $view->items['leaseTerm'] = implode('  ', $leaseTerm);
            break;
        case 'selectProperty':
            $view->items['leaseCheck'] = false;
            break;
        case 'adjustEntryMethod':
            $view->items['leaseCheck'] = false;
            break;
        case 'toggleStatus':
            $leaseStatus = dbGetLeaseStatus($view->items['propertyID'], $view->items['leaseID'], $view->items['versionID']);
            if ($leaseStatus) {
                $status = ($leaseStatus == 'C') ? 'L' : 'C';
                dbUpdateLeaseStatus($view->items['propertyID'], $view->items['leaseID'], $status);
                if (isAdmin($context)) {
                    if ($status == 'C') {
                        dbInsertLeaseStatusLog($view->items['propertyID'], $view->items['leaseID'], intval($_SESSION['user_id']), 'CURRENT');
                    } elseif ($status == 'L') {
                        dbInsertLeaseStatusLog($view->items['propertyID'], $view->items['leaseID'], intval($_SESSION['user_id']), 'VACATED');
                    }
                }
            } else {
                $view->items['validationErrors'][] = 'Your lease doesnt currently have an assigned status - has it been created yet?';
            }
            break;
        case 'createLease':
            if (! isValid($view->items['leaseID'], TEXT_CODE, false)) {
                $view->items['validationErrors'][] = 'Please use only alpha numeric characters for the lease code';
            }
            if (strlen($view->items['leaseID']) > 10) {
                $view->items['validationErrors'][] = 'Lease code allows 10 characters only';
            }

            if (count($view->items['validationErrors']) == 0) {
                $taskDescription[0] = 'Draft';
                $taskDescription[1] = 'Unprocessed';
                $taskDescription[2] = 'Rejected';
                $taskDescription[3] = 'Processed';
                $checkTempLease = dbCheckTempLease($view->items['propertyID'], $view->items['leaseID']);
                if ($checkTempLease) {
                    $view->items['validationErrors'][] = 'The lease ID you have chosen already exists (' . $view->items['leaseID'] . ' on ' . $view->items['propertyID'] . ') and the status is ' . $taskDescription[$checkTempLease['pmle_process_state']] . '';
                }
            }
            if (count($view->items['validationErrors']) == 0) {
                // -- EDIT 20120404 - check to see if its a tracc user, then just check if the lease code is unique against the property - otherwise do a wider check if it's a property manager
                $lookup = (isUserType(USER_TRACC)) ? dbGetLease($view->items['propertyID'], strtoupper($view->items['leaseID']), $view->items['versionID'])
                    : dbCheckLeaseCode(strtoupper($view->items['leaseID']), $view->items['propertyID']);
                if (($view->items['leaseID']) && (! $lookup)) {
                    $view->items['leaseCheck'] = true;
                } else {
                    $view->items['leaseCheck'] = false;
                    if (isUserType(USER_TRACC)) {
                        $view->items['validationErrors'][] = 'The lease ID you have chosen already exists (' . $lookup['leaseID'] . ' on ' . $lookup['propertyID'] . ')';
                    } else {
                        $view->items['validationErrors'][] = 'The lease ID you have chosen already exists (' . $lookup['pmle_lease'] . ' on ' . $lookup['pmle_prop'] . ')';
                    }
                }

            } else {
                $view->items['leaseCheck'] = false;
            }

            break;
        case 'deleteLease':
            if (dbCheckCharges($view->items['propertyID'], $view->items['leaseID'])) {
                $view->items['statusMessage'] = 'This lease already has charges against it and cannot be deleted.';
            } else {
                $view->items['statusMessage'] = 'Lease successfully deleted.';
                dbDeleteLease($view->items['propertyID'], $view->items['leaseID']);
            }
            break;
        case 'finalise':
            if (! isUserType(USER_TRACC)) {
                clientSideRedirect(SYSTEMURL . '/?command=viewLease&module=leases&propertyID=' . $view->items['propertyID'] . '&leaseID=' . $view->items['leaseID']);
            }
            $view->items = [];
            $view->items['UserControl:LeaseSelector'] = fetchCommand('leaseSelector', 'leases');
            $view->items['UserControl:LeaseSelectorBond'] = fetchCommand('leaseSelectorBond');
            $view->items['statusMessage'] = 'Lease successfully saved.';
            $view->items['leaseEntryTypeList'] = [LEASE_EXISTING => 'Existing Lease', LEASE_NEW => 'New Lease'];
            if (! isset($view->items['leaseEntryType'])) {
                $view->items['leaseEntryType'] = LEASE_EXISTING;
            }
            break;
        case 'diariseOption':
            if ($view->items['leaseOption'] != '') {
                $diary = [];
                $diary['propertyID'] = $view->items['propertyID'];
                $diary['leaseID'] = $view->items['leaseID'];
                $diary['versionID'] = $view->items['versionID'];
                $diary['diaryDate'] = $view->items['leaseExpiryDate'];
                $diary['diaryUser'] = $_SESSION['user_name'];
                $diary['diaryEntryType'] = 'EXPOPT';
                $diary['diaryEntry'] = 'Lease Option: ' . $view->items['leaseOption'];
                dbInsertLeaseDiaryEntry($diary);
            } else {
                $view->items['validationErrors'][] = 'You have not added a lease option yet.';
            }
            [$lease['leaseTermDays'], $lease['leaseTermMonths'], $lease['leaseTermYears']] = dateDiff($view->items['leaseCommencementDate'], $view->items['leaseExpiryDate']);
            $leaseTerm = [];
            $leaseTerm[0] = simpleWordDate('year', $lease['leaseTermYears']);
            $leaseTerm[1] = simpleWordDate('month', $lease['leaseTermMonths']);
            $leaseTerm[2] = simpleWordDate('day', $lease['leaseTermDays']);
            $view->items['leaseTerm'] = implode('  ', $leaseTerm);
            break;
        case 'diariseLease':
            $diary = [];
            $diary['propertyID'] = $view->items['propertyID'];
            $diary['leaseID'] = $view->items['leaseID'];
            $diary['versionID'] = $view->items['versionID'];
            $diary['diaryDate'] = $view->items['leaseExpiryDate'];
            $diary['diaryUser'] = $_SESSION['user_name'];
            $diary['diaryEntryType'] = 'LEASEEXP';
            $diary['diaryEntry'] = 'Lease Expires';
            dbInsertLeaseDiaryEntry($diary);
            [$lease['leaseTermDays'], $lease['leaseTermMonths'], $lease['leaseTermYears']] = dateDiff($view->items['leaseCommencementDate'], $view->items['leaseExpiryDate']);
            $leaseTerm = [];
            $leaseTerm[0] = simpleWordDate('year', $lease['leaseTermYears']);
            $leaseTerm[1] = simpleWordDate('month', $lease['leaseTermMonths']);
            $leaseTerm[2] = simpleWordDate('day', $lease['leaseTermDays']);
            $view->items['leaseTerm'] = implode('  ', $leaseTerm);
            break;
        case 'countryList':
            $view->items['stateList'] = $stateList;
            if ($view->items['leaseCountry'] == '') {
                $view->items['leaseCountry'] = 'AU';
            }
            $view->items['countryList'] = mapParameters(dbGetCountries(), 'countryCode', 'countryName');
            $view->items['leaseStateList'] = mapParameters(dbGetStates($view->items['leaseCountry']), 'stateCode', 'stateName');
            break;
        case 'save':
            // -- if its a tracc user, check against live tables
            // -- if its a property manager, check against temp tables
            if (isUserType(USER_TRACC)) {
                $lookup = dbGetLease($view->items['propertyID'], strtoupper($view->items['leaseID']), $view->items['versionID']);
            } else {
                $lookup = dbCheckTempLeaseCode(strtoupper($view->items['leaseID']), $view->items['versionID']);
            }
            if (isUserType(USER_TRACC)) {
                switch ($view->items['leaseEntryType']) {
                    case 1:
                        if (! $lookup) {
                            $view->items['validationErrors'][] = 'You cannot save an existing lease under a new code';
                        }
                        break;
                    case 2:
                        // -- MISCELLANEOUS IS A SUPER CODE
                        if ($lookup && ($view->items['leaseID'] != MISCELLANEOUS)) {
                            $view->items['leaseCheck'] = false;
                            $view->items['validationErrors'][] = 'The lease code you have entered already exists (' . $lookup['pmle_lease'] . ' on ' . $lookup['pmle_prop'] . ')';
                        }
                        break;
                }
            }
            // Validations
            if (! isValid($view->items['propertyID'], TEXT_KEY, false)) {
                $view->items['validationErrors'][] = 'You have not specified a valid property code.';
            }
            if (! isValid($view->items['propertyID'], TEXT_KEY, false)) {
                $view->items['validationErrors'][] = 'You have not specified a valid property code.';
            }
            if (! isValid($view->items['leaseName'], TEXT_LOOSE, false)) {
                $view->items['validationErrors'][] = 'You have not specified a lease name.';
            }
            if (isset($view->items['leaseCommencementDate']) && isset($view->items['leaseExpiryDate']) && (toDateStamp($view->items['leaseCommencementDate']) > toDateStamp($view->items['leaseExpiryDate']))) {
                $view->items['validationErrors'][] = 'A lease cannot expire before it commences - check your dates';
            }

            $view->items['tenantID'] = strtoupper($view->items['tenantID']);

            if ($view->items['compType'] == 1) {
                $checkCompany = dbCheckCompanyCode($view->items['tenantID']);
                if ($checkCompany) {
                    $view->items['validationErrors'][] = 'The Debtor Code you have entered already exists.';
                }
                if (empty($view->items['tenantID'])) {
                    $view->items['validationErrors'][] = 'You have not specified a valid Debtor Code';
                } elseif (! isValid($view->items['tenantID'], TEXT_KEY, false)) {
                    $view->items['validationErrors'][] = 'You have not specified a valid Debtor code';
                }
            } elseif (empty($view->items['tenantID'])) {
                $view->items['validationErrors'][] = 'You have not specified a valid Debtor code';
            }

            // validation for tenant section
            $validationErrors = [];
            $tenant = [];
            $tenant['tenantID'] = $view->items['tenantID'];
            $tenant['tenantName'] = $view->items['tenantName'];
            $address = $view->items['tenantAddress1'] . PHP_EOL . $view->items['tenantAddress2'];
            $tenant['tenantAddress'] = $view->items['tenantAddress1'] . PHP_EOL . $view->items['tenantAddress2'];
            $tenant['tenantCity'] = $view->items['tenantCity'];
            $tenant['tenantPostCode'] = $view->items['tenantPostCode'];
            $tenant['tenantState'] = $view->items['tenantState'];
            $tenant['tenantCountry'] = $view->items['tenantCountry'];
            $tenant['tenantEmail'] = $view->items['tenantEmail'];
            if (! isValid($tenant['tenantID'], TEXT_KEY, false)) {
                $view->items['validationErrors'][] = 'You have not provided a valid Debtor code';
            }
            if (! isValid($tenant['tenantName'], TEXT_LOOSE, false)) {
                $view->items['validationErrors'][] = 'You have not provided a valid tenant name';
            }
            if (! isValid($tenant['tenantAddress'], TEXT_LOOSE, false)) {
                $view->items['validationErrors'][] = 'You have not provided a valid tenant address';
            }
            if (! isValid($tenant['tenantCity'], TEXT_LOOSE, false)) {
                $view->items['validationErrors'][] = 'You have not provided a valid ' . strtolower($_SESSION['country_default']['suburb']);
            }
            if ($tenant['tenantEmail'] && ! isValid($tenant['tenantEmail'], TEXT_EMAIL_MULTI, false)) {
                $view->items['validationErrors'][] = 'One or more email addresses are invalid. Please ensure all email addresses are correctly formatted and separated by semicolons';
            }
            // POST CODE FORMAT VALIDATION
            $postcodeValidation = cdf_validate_postcode($tenant['tenantPostCode'], $tenant['tenantCountry']);
            if (! $postcodeValidation->valid) {
                $view->items['validationErrors'][] = $postcodeValidation->error;
            }
            // VALIDATE STATE
            if (cdf_isShown('display_state', $tenant['tenantCountry'])) {
                if (! isValid($tenant['tenantState'], TEXT_LOOSE, false)) {
                    $view->items['validationErrors'][] = 'You have not provided a valid state';
                }
            }
            if (! isValid($tenant['tenantCountry'], TEXT_LOOSE, true)) {
                $view->items['validationErrors'][] = 'You have not provided a valid country';
            }

            if ($view->items['isMailingAddress'] == 0) {
                $mailingAddress['leaseID'] = $view->items['leaseID'];
                $mailingAddress['propertyID'] = $view->items['propertyID'];
                $mailingAddress['mailingName'] = $view->items['mailingName'];
                $mailingAddress['mailingAddress'] = $view->items['mailingAddress1'] . PHP_EOL . $view->items['mailingAddress2'];
                $mailingAddress['mailingCity'] = $view->items['mailingCity'];
                $mailingAddress['mailingState'] = $view->items['mailingState'];
                $mailingAddress['mailingCountry'] = $view->items['mailingCountry'];
                $mailingAddress['mailingPostCode'] = $view->items['mailingPostCode'];
                $mailingAddress['versionID'] = $view->items['versionID'];

                // VALIDATIONS
                if (! isValid($mailingAddress['mailingAddress'], TEXT_LOOSE, false)) {
                    $view->items['validationErrors'][] = 'You have not provided a valid mailing address';
                }
                if (! isValid($mailingAddress['mailingCity'], TEXT_LOOSE, false)) {
                    $view->items['validationErrors'][] = 'You have not provided a valid city';
                }

                // POST CODE FORMAT VALIDATION
                $postcodeValidation = cdf_validate_postcode($view->items['mailingPostCode'], $view->items['mailingCountry']);
                if (! $postcodeValidation->valid) {
                    $view->items['validationErrors'][] = $postcodeValidation->error;
                }
                if (cdf_isShown('display_state', $mailingAddress['mailingCountry'])) {
                    if (! isValid($mailingAddress['mailingState'], TEXT_LOOSE, false)) {
                        $view->items['validationErrors'][] = 'You have not provided a valid state';
                    }
                }
                if (! isValid($mailingAddress['mailingCountry'], TEXT_LOOSE, true)) {
                    $view->items['validationErrors'][] = 'You have not provided a valid country';
                }
            }

            //  if ($view->items['chargeInterest'] AND isUserType(USER_TRACC))
            if ($view->items['chargeInterest'] == 'Yes') {
                if (! isValid($view->items['invoiceDescription'], TEXT_LOOSE, false)) {
                    $view->items['validationErrors'][] = 'You have not specified a description for interest on arrears.';
                }
                if (! $view->items['interestAccount']) {
                    $view->items['validationErrors'][] = 'You have not selected an interest account for interest on arrears.';
                }
                if (count($view->items['interestAccount2']) == 0) {
                    $view->items['validationErrors'][] = 'You have not selected accounts for interest on arrears.';
                }
                if ($view->items['interestRate'] <= 0) {
                    $view->items['validationErrors'][] = 'You have not specified a valid fixed interest rate for interest on arrears.';
                }
            }

            $lease = [];
            $lease['propertyID'] = $view->items['propertyID'];
            $lease['leaseID'] = strtoupper($view->items['leaseID']);
            $lease['versionID'] = $view->items['versionID'];

            if (isUserType(USER_TRACC)) {
                if ($view->items['leaseChargeType'] == 'D') {
                    $lease['debtorID'] = $view->items['tenantID'];
                }
                if ($view->items['leaseChargeType'] == 'S') {
                    $lease['supplierID'] = $view->items['tenantID'];
                }
            } elseif ($view->items['tenantID']) {
                if ($view->items['leaseChargeType'] == 'D') {
                    $lease['debtorID'] = $view->items['tenantID'];
                }
                if ($view->items['leaseChargeType'] == 'S') {
                    $lease['supplierID'] = $view->items['tenantID'];
                }
            } elseif ($view->items['leaseChargeType'] == 'D' && ($view->items['debtorID'] || $lease['debtorID'])) {
                $lease['debtorID'] = $view->items['tenantID'] = ($lease['debtorID']) ? $lease['debtorID'] : $view->items['debtorID'];
            } elseif ($view->items['leaseChargeType'] == 'S' && ($view->items['supplierID'] || $lease['supplierID'])) {
                $lease['supplierID'] = $view->items['tenantID'] = ($lease['supplierID']) ? $lease['supplierID'] : $view->items['supplierID'];
            } elseif ($view->items['leaseChargeType'] == 'D' && $lookup['pmle_debtor']) {
                $view->items['tenantID'] = $view->items['debtorID'] = $lease['debtorID'] = $lookup['pmle_debtor'];
            } elseif ($view->items['leaseChargeType'] == 'S' && $lookup['pmle_supplier']) {
                $view->items['tenantID'] = $view->items['supplierID'] = $lease['supplierID'] = $lookup['pmle_supplier'];
            }
            $lease['badDebtProvision'] = (int) $view->items['badDebtProvision'];
            $lease['tenantID'] = strtoupper($view->items['tenantID']);
            $lease['leaseChargeType'] = $view->items['leaseChargeType'];
            $lease['leaseName'] = $view->items['leaseName'];
            $lease['leaseAddress1'] = $view->items['leaseAddress1'] . PHP_EOL . $view->items['leaseAddress2'];
            $lease['leaseCity'] = $view->items['leaseCity'];
            $lease['leasePostCode'] = $view->items['leasePostCode'];
            $lease['leaseState'] = $view->items['leaseState'];
            $lease['leaseLeaseState'] = $view->items['leaseLeaseState'];
            $lease['leaseCountry'] = $view->items['leaseCountry'];
            $lease['leaseDescription'] = $view->items['leaseDescription'];
            $lease['tenantName'] = $view->items['tenantName'];
            $lease['tenantAddress'] = $view->items['tenantAddress1'] . PHP_EOL . $view->items['tenantAddress2'];
            $lease['tenantCity'] = $view->items['tenantCity'];

            $tenantState = null;
            if (cdf_isShown('display_state', $view->items['tenantCountry'])) {
                $tenantState = $view->items['tenantState'];
            }
            $lease['tenantState'] = $tenantState;

            $lease['tenantCountry'] = $view->items['tenantCountry'];
            $lease['tenantPostCode'] = $view->items['tenantPostCode'];
            $lease['headCount'] = $view->items['headCount'];
            $lease['deskCount'] = $view->items['deskCount'];
            $lease['statDesc'] = $view->items['statDesc'];
            $lease['leaseClass'] = $view->items['leaseClass'];
            $lease['tenantEmail'] = $view->items['tenantEmail']; // used on client side - tenant email address is stored under the pmco_email when live
            $lease['chargeInterest'] = ($view->items['chargeInterest'] == 'Yes') ? 1 : 0;
            $lease['leaseInsideAct'] = ($view->items['leaseInsideAct'] == '1') ? 1 : 0;
            $lease['invoiceDescription'] = trim($view->items['invoiceDescription']);
            $lease['interestAccount'] = $view->items['interestAccount'];
            $lease['gracePeriod'] = (int) $view->items['gracePeriod'];
            $lease['interestRate'] = (float) $view->items['interestRate'];
            $lease['propertyIDBond'] = $view->items['propertyIDBond'];
            $lease['leaseIDBond'] = $view->items['leaseIDBond'];

            $lease['landlordID'] = $view->items['landlordID'];
            $lease['landlordName'] = $view->items['landlordName'];
            $lease['landlordAddress'] = $view->items['landlordAddress'];
            $lease['landlordCity'] = $view->items['landlordCity'];
            $lease['landlordState'] = $view->items['landlordState'];
            $lease['landlordCountry'] = $view->items['landlordCountry'];
            $lease['landlordPostCode'] = $view->items['landlordPostCode'];
            $lease['landlordEmailAdd'] = $view->items['landlordEmailAdd'];

            $lease['leaseCommencementDate'] = $view->items['leaseCommencementDate'];
            $lease['leaseExpiryDate'] = $view->items['leaseExpiryDate'];
            $lease['leaseStopDate'] = FINALDATE;
            $lease['leaseOption'] = $view->items['leaseOption'];
            $lease['leaseType'] = $view->items['leaseType'];
            $lease['division'] = $view->items['division'];
            $lease['leaseTenantType'] = $view->items['leaseTenantType'];
            $lease['retailCategory'] = $view->items['retailCategory'];
            $lease['retailSubCategory'] = $view->items['retailSubCategory'];
            $lease['retailFineCategory'] = $view->items['retailFineCategory'];
            $lease['vacantArea'] = $view->items['vacantArea'];
            $lease['leaseExecutionDate'] = $view->items['leaseExecutionDate'];
            $lease['compType'] = $view->items['compType'];
            if ($view->items['leaseStatus'] != 'C' && $view->items['leaseStatus'] != 'L') {
                $view->items['leaseStatus'] = 'L';
            }
            $lease['leaseStatus'] = ($view->items['leaseStatus'] != 'C') ? 'L' : 'C'; // -- bug fix : lease status not being transferred
            $lease['leaseFormType'] = $view->items['leaseFormType'];
            $lease['leaseStatusDescription'] = $view->items['leaseStatusDescription'];
            $lease['processState'] = '0';
            $lease['mainLease'] = $view->items['isMainLease'];

            [$lease['leaseTermDays'], $lease['leaseTermMonths'], $lease['leaseTermYears']] = dateDiff($lease['leaseCommencementDate'], $lease['leaseExpiryDate']);

            $leaseTerm = [];
            $leaseTerm[0] = simpleWordDate('year', $lease['leaseTermYears']);
            $leaseTerm[1] = simpleWordDate('month', $lease['leaseTermMonths']);
            $leaseTerm[2] = simpleWordDate('day', $lease['leaseTermDays']);

            if (! isUserType(USER_TRACC)) {
                $lease['createdBy'] = $_SESSION['un'];
                if ($view->items['comments']) {
                    $lease['comments'] = $view->items['comments'];
                }
            }

            // BELOW lookup: as tracc - and as client selects from pmle_lease AS leaseID,
            $lookup = dbGetLease($view->items['propertyID'], $view->items['leaseID'], $view->items['versionID']); // sees if record is already in live.

            $view->items['leaseTerm'] = implode('  ', $leaseTerm);

            if (noErrors($view->items['validationErrors'])) {
                if ($view->items['compType'] == 1) {
                    dbUpdateTempCompany($lease);
                }
                if ($view->items['leaseFormType'] == LEASE_ASSIGNMENT) {
                    $lease['debtorID'] = $lease['supplierID'] = null;
                }

                if (isUserType(USER_TRACC)) {
                    if ($view->items['isMainLease']) {
                        dbUpdateMainLease($lease);
                    } else {
                        $countMainLease = dbGetMainLease($lease['propertyID'], $lease['debtorID'], $lease['leaseID']);
                        if (! $countMainLease) {
                            $lease['mainLease'] = 1;
                        }
                    }
                }

                if ($lookup == null) {
                    // this part just inserts a new lease record to pmle_lease.
                    dbInsertLease($lease);
                    if (isAdmin($context)) {
                        dbInsertLeaseStatusLog($view->items['propertyID'], $view->items['leaseID'], intval($_SESSION['user_id']), 'CREATED');
                    }
                    // insert interest on arrears account
                    if (count($view->items['interestAccount2'] ?? []) > 0) {
                        if (isUserType(USER_TRACC)) {
                            dbInsertLeaseInterestOnArrears($view->items['propertyID'], $view->items['leaseID'], $view->items['interestAccount2']);
                        } else {
                            dbInsertTempLeaseInterestOnArrears($view->items['propertyID'], $view->items['leaseID'], $view->items['interestAccount2'], $view->items['versionID']);
                        }
                    }


                    if ($view->items['compType'] == 1) {
                        dbAddTempTenantCompany($lease);
                    }
                    if ($view->items['leaseEntryType'] == LEASE_NEW) {
                        // -- switch to the existing lease view to continue the process
                        $view->items['selectionMethod'] = 'property';
                        $view->items['leaseEntryType'] = LEASE_EXISTING;
                        $view->items['selected'] = true;
                        $view->items['leaseCheck'] = true;

                        if (isAdmin($context)) {
                            $context = $view->items;
                            $context['container'] = 'content';
                            $context['command'] = 'home';
                            $context['module'] = 'leases';
                            $context['otherDataSource'] = (! isAdmin($context));
                            if ($view->items['taskType'] == 2) {
                                $context['otherDataSource'] = (isAdmin($context));
                            }
                            /* pass the context back into view state */
                            $view->items['UserControl:LeaseSelector'] = fetchCommand('leaseSelector', 'leases');
                            $view->bindAttributesFrom($context);
                        } else {
                            $view->items['propertyList'] = (isAdmin($context)) ? dbPropertyList(true, null, 1) : arrayUnique(dbCombinedPropertyList(true));
                            $view->items['leaseCheck'] = true;
                        }
                    }

                    // if you are tracc - it will save a CRN... if you aren't it new table.ing?
                    if (isUserType(USER_TRACC)) {
                        dbSaveCRN($view->items['propertyID'], $view->items['leaseID'], dbGetClientID());
                    }
                    // DBsaveCRN doesnt exist in client. maybe only on approval of tracc user.
                    // above else added [jpalala] - updates te
                } else {
                    // else it won't update the CRN list.
                    // UPDATE's temp_pmle_lease - if property manager??
                    // in a client - affects temp_pmle
                    // in a tracc - affects pmle live
                    dbUpdateLease($lease);
                    if (! isUserType(USER_TRACC)) {
                        $getTempCompany = dbGetTempCompany($view->items['propertyID'], $view->items['leaseID']);
                        if ($view->items['compType'] == 1) {
                            if ($getTempCompany) {
                                dbUpdateTempTenantCompany($lease);
                            } else {
                                dbAddTempTenantCompany($lease);
                            }
                        }
                    }
                }

                // insert interest on arrears account

                if (count($view->items['interestAccount2'] ?? []) > 0) {
                    if (isUserType(USER_TRACC)) {
                        dbInsertLeaseInterestOnArrears($view->items['propertyID'], $view->items['leaseID'], $view->items['interestAccount2']);
                    } else {
                        dbInsertTempLeaseInterestOnArrears($view->items['propertyID'], $view->items['leaseID'], $view->items['interestAccount2'], $view->items['versionID']);
                    }
                }

                // -- trigger: next step for client users
                if (! isUserType(USER_TRACC)) {
                    $view->items['step'] = 2;
                }
            }
            break;
    }

    // -- if a propertyID and leaseID have been entered, grab the base details for the lease
    // -- removed && ($view->items['action'] != 'retailCategory')
    if (($view->items['leaseCheck'])) {
        if (isUserType(USER_TRACC)) {
            $lookup = dbGetLease($view->items['propertyID'], strtoupper($view->items['leaseID']), $view->items['versionID']);
        } else {
            $lookup = dbGetLease($view->items['propertyID'], $view->items['leaseID'], $view->items['versionID']);
        }

        if ($view->items['action'] == 'submit' && ! isUserType(USER_TRACC)) {

            $mainLease = dbTempGetLease($view->items['propertyID'], strtoupper($view->items['leaseID']), $view->items['versionID']);
            $lookup['isMainLease'] = $mainLease['isMainLease'];
        }

        $view->items['crn'] = dbGetCRN($view->items['propertyID'], $view->items['leaseID']);
        if (($lookup['processStatus'] == 1) || ($lookup['processStatus'] == 3)) {
            unset($lookup);
            $view->items['leaseCheck'] = false;
        }
        $leaseList = mapByKey($view->items['leaseList'], 'leaseID');
        $mailing = dbGetMailingAddress($view->items['propertyID'], $view->items['leaseID']);
    }

    // -- if the lease lookup was successful, then set leaseExists to true and bind details for display
    if ($lookup != null) {
        $view->items['leaseExists'] = true;

        if (! in_array($view->items['action'], ['diariseLease', 'diariseOption', 'retailCategory', 'computeLeaseTerm'])) {
            $view->bindAttributesFrom($lookup);
            if (! $view->items['tenantID']) {
                if ($view->items['leaseChargeType'] == 'D') {
                    $view->items['tenantID'] = $view->items['debtorID'];
                }
                if ($view->items['leaseChargeType'] == 'S') {
                    $view->items['tenantID'] = $view->items['supplierID'];
                }
            }

            $leaseTerm = [];

            $leaseTerm[0] = simpleWordDate('year', $view->items['leaseTermYears']);
            $leaseTerm[1] = simpleWordDate('month', $view->items['leaseTermMonths']);
            $leaseTerm[2] = simpleWordDate('day', $view->items['leaseTermDays']);
            $view->items['leaseTerm'] = implode('  ', $leaseTerm);

            $view->items['chargeInterest'] = ($view->items['chargeInterest'] == 1) ? 'Yes' : 'No';
            $view->items['leaseInsideAct'] = ($view->items['leaseInsideAct'] == '1') ? '1' : '0';
            if (isUserType(USER_TRACC)) {
                $view->items['interestAccount2'] = dbGetLeaseInterestOnArrearsAccounts($view->items['propertyID'], $view->items['leaseID']);
            } else {
                $view->items['interestAccount2'] = dbGetLeaseTempInterestOnArrearsAccounts($view->items['propertyID'], $view->items['leaseID'], $view->items['versionID']);
            }

        }

    } else {
        $view->items['leaseExists'] = false;
    }
    $view->items['leaseTypeList'] = mapParameters(dbGetParams('LEASE'));
    $view->items['leaseTenantTypeList'] = mapParameters(dbGetParams('TENANT'));
    $view->items['statusDescList'] = mapParameters(dbGetParams('LEASESTA'));
    $view->items['leaseClassList'] = mapParameters(dbGetParams('LEASECLASS'));
    $view->items['retailCategoryList'] = mapParameters(dbRetailCategoryList(), 'categoryID', 'categoryName');
    $view->items['retailSubCategoryList'] = mapParameters(dbRetailSubCategories($view->items['retailCategory']), 'subCategoryID', 'subCategoryName');
    if (count($view->items['retailSubCategoryList']) == 1) {
        $keys = array_keys($view->items['retailSubCategoryList']);
        $view->items['retailSubCategory'] = $keys[0];
    }
    $view->items['retailFineCategoryList'] = mapParameters(dbRetailFineCategories($view->items['retailSubCategory']), 'fineCategoryID', 'fineCategoryName');
    if (count($view->items['retailFineCategoryList']) == 1) {
        $keys = array_keys($view->items['retailFineCategoryList']);
        $view->items['retailFineCategory'] = $keys[0];
    }
    $view->items['divisionList'] = mapParameters(dbGetParams('DIVISION'));
    $view->items['stateList'] = $stateList;
    if (empty($view->items['leaseCountry'])) {
        $view->items['leaseCountry'] = 'AU';
    }
    $view->items['leaseStateList'] = mapParameters(dbGetStates($view->items['leaseCountry']), 'stateCode', 'stateName');
    if ($view->items['vacantArea'] != 'C') {
        $view->items['vacantArea'] = 'L';
    }

    // -- if no commencement or expiry dates have been provided, default them to today's date
    if (! isset($view->items['leaseCommencementDate'])) {
        $view->items['leaseCommencementDate'] = date('d/m/Y', time());
    }
    if (! isset($view->items['leaseExpiryDate'])) {
        $view->items['leaseExpiryDate'] = date('d/m/Y', time());
    }
    if ($view->items['leaseFormType'] == 2) {
        if (! isset($view->items['compType'])) {
            $view->items['compType'] = 1;
        }
    } elseif (! isset($view->items['compType'])) {
        $view->items['compType'] = 0;
    }
    // -- if it is a client user and no step has been activated - its a fresh form! so set the step to 1
    if ((! isUserType(USER_TRACC)) && (! isset($view->items['step']))) {
        $view->items['step'] = 1;
    }
    $view->items['useTemp'] = true;
    $view->items['taskClassList'] = $taskClass;
    $view->items['taskNameList'] = $taskDescription;

    if (isset($view->items['compTypeOverride'])) {
        $view->items['compType'] = $view->items['compTypeOverride'];
    }

    $propertyMngfeeDetails = dbGetPropertyForInvoice($view->items['propertyID']);
    $view->items['propertyMgmtFeeMethod'] = $propertyMngfeeDetails['propertyMgmtFeeMethod'];
    $view->items['managementFeesPercentageRecDef'] = $propertyMngfeeDetails['propertyMgmtFeePercentage'];
    $view->items['managementFeesPercentageRec'] = $propertyMngfeeDetails['propertyMgmtFeePercentage'];

    if (isUserType(USER_TRACC)) {
        if ($view->items['propertyID'] && $view->items['leaseID']) {
            $manFee = dbGetManagementFeeDetails($view->items['propertyID'], [$view->items['leaseID']]);
            $view->items['managementFee'] = ($manFee['managementFee'] == 0) ? 'No' : 'Yes';
            $view->items['managementFeesAccount'] = $manFee['managementFeesAccount'];
            $view->items['managementFeesDescription'] = $manFee['managementFeesDescription'];
            $view->items['managementFeesFrom'] = $manFee['managementFeesFrom'];
            $view->items['managementFeesTo'] = $manFee['managementFeesTo'];
            $view->items['managementFeesPercentage'] = $manFee['managementFeesPercentage'];
            if ($manFee['managementFeesPercentageRec'] != null) {
                $view->items['managementFeesPercentage'] = $manFee['managementFeesPercentageRec'];
            }
        }
    } elseif ($view->items['propertyID'] && $view->items['leaseID']) {
        $manFee = dbGetLeaseTempManagementFeeDetails($view->items['propertyID'], [$view->items['leaseID']], $view->items['versionID']);
        $view->items['managementFee'] = ($manFee['managementFee'] == 0) ? 'No' : 'Yes';
        $view->items['managementFeesAccount'] = $manFee['managementFeesAccount'];
        $view->items['managementFeesDescription'] = $manFee['managementFeesDescription'];
        $view->items['managementFeesFrom'] = $manFee['managementFeesFrom'];
        $view->items['managementFeesTo'] = $manFee['managementFeesTo'];
        $view->items['managementFeesPercentage'] = $manFee['managementFeesPercentage'];
        if ($manFee['managementFeesPercentageRec'] != null) {
            $view->items['managementFeesPercentage'] = $manFee['managementFeesPercentageRec'];
        }
    }
    // -- save the data in context for use in other modules
    $context = $view->items;

    // -- import all child modules of this page
    $view->items['UserControl:Tenant'] = fetchCommand('tenant', 'leases');
    $view->items['UserControl:MailingAddress'] = fetchCommand('mailingAddress', 'leases');
    $view->items['UserControl:Contact'] = fetchCommand('leasecontact', 'leases');
    $view->items['UserControl:LeaseInsurance'] = fetchCommand('leaseInsurance', 'leases');
    $view->items['UserControl:LeaseInspection'] = fetchCommand('leaseInspection', 'leases');
    $view->items['UserControl:LeaseGuarantee'] = fetchCommand('leaseGuarantee', 'leases');
    // -- if a lease exists [scroll up for the check] then initialise the other components
    if ($view->items['leaseExists']) {
        $view->items['UserControl:Unit'] = fetchCommand('unit', 'leases');
        $view->items['UserControl:UnitCharges'] = fetchCommand('unitCharges', 'leases');
        $view->items['UserControl:leaseManagementFees'] = fetchCommand('leaseManagementFees', 'leases');
        if (! isUserType(USER_TRACC)) {
            $view->items['UserControl:leaseTempManagementFees'] = fetchCommand('leaseTempManagementFees', 'leases');
        }
        $view->items['UserControl:Diary'] = fetchCommand('diary', 'leases');
        $view->items['UserControl:LeaseNotes'] = fetchCommand('leaseNotes', 'leases');
        $view->items['UserControl:RentReview'] = fetchCommand('rentReview', 'leases');
        $view->items['UserControl:Document'] = fetchCommand('leaseDocument', 'leases');
        $view->items['letterHistoryPage'] = 'index.php?module=configuration&command=letterHistory&propertyCode=' . $view->items['propertyID'] . '&leaseCode=' . $view->items['leaseID'] . '&viewOnly=1';
    }

    if (! $view->items['versionID'] && ! isUserType(USER_TRACC)) {
        $view->items['versionID'] = dbGetLastVersionID($view->items['propertyID'], $view->items['leaseID'], true);
    }

    $view->items['formErrors'] = $context['formErrors'];

    if (is_array($context['formErrors']) && (count($context['formErrors']) > 0) && (($view->items['action'] == 'nextStep') || ($view->items['action'] == 'save')) && $view->items['step'] > 1) {
        $view->items['step']--;
    }

    if (! isset($view->items['commHistory'])) {
        $view->items['commHistory'] = 'E';
    }

    if ($view->items['commHistory'] == 'S') {
        $view->items['smsMessage'] = dbGetSmsMessagePerLease($view->items['propertyID'], $view->items['leaseID']);
    }

    if (isAdmin($context)) { // check if TA
        $view->items['UserControl:LeaseAuditStatistics'] = fetchCommand('leaseAuditStatistics', 'leases');
    }
    if (isAdmin($context)) {

        $view->items['UserControl:LeaseSelectorBond'] = fetchCommand('leaseSelectorBond');
    }
    if ($view->items['leaseFormType'] == LEASE_ASSIGNMENT) {
        $_SESSION['enable_email_cen'] = '0';
    } elseif ($view->items['leaseFormType'] == LEASE_RENEWAL) {
        $_SESSION['enable_email_cen'] = '0';
    } else {
        $_SESSION['enable_email_cen'] = '1';
    }

    if (! isset($view->items['leaseInsideAct'])) {
        $view->items['leaseInsideAct'] = '0';
    }
    $view->items['showUKField'] = dbGetDefaultCountry() == 'GB';
    $view->render();
}

<?php

/**
 * This function allows multilevel searching using dropboxes for leases. HTML will be rendered.
 *
 * @param  array  $context  Mandatory.
 *
 * @modified 2012-04-27: Code cleaning and descriptive text. [Morph]
 **/
function signRequest($method, $uri, $body, $timestamp, $secretKey)
{
    $string = implode("\n", [
        $method,
        $uri,
        $body,
        $timestamp,
    ]);

    return hash_hmac('sha256', $string, $secretKey);
}

function submitAssuroRequestV2($item_data, $lease_guarantee_id)
{
    // //$jsonData = json_encode($item_data);

    $method = 'post';

    // Assuro Config
    $uri = ASSURO_URI;
    $clientKey = ASSURO_CLIENT_KEY;
    $secretKey = ASSURO_SECRET_KEY;

    // Current Unix timestamp in seconds
    $utcNow = gmdate('D, d M Y H:i:s ') . 'GMT';
    $content = json_encode($item_data);

    $contentHash = base64_encode(hash('sha256', $content, true));
    $url = parse_url($uri);

    $stringToSign = implode("\n", [
        strtoupper($method),
        $url['path'],
        "{$utcNow};" . $url['host'] . ";{$contentHash}",
    ]);

    $base64SecretKey = base64_decode($secretKey);
    $credential = $clientKey;

    $signedHeaders = 'x-ms-date;host;x-ms-content-sha256';
    $signature = base64_encode(hash_hmac('sha256', $stringToSign, $base64SecretKey, true));

    $headers = [
        // "Accept: application/json",
        'Content-Type: application/json',
        "x-ms-date: {$utcNow}",
        "x-ms-content-sha256: {$contentHash}",
        "Authorization: HMAC-SHA256 Credential={$credential}&SignedHeaders={$signedHeaders}&Signature={$signature}",
    ];

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $uri);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $content);

    $season_data = curl_exec($ch);

    if (curl_errno($ch) !== 0) {
        echo 'Error: ' . curl_error($ch);
        exit();
    }

    $json = $season_data;
    // var_dump([curl_getinfo($ch), $json]);

    /** Uncomment only for testing purposes */

    /**
    pre_print_r('Curl Info ===> '.curl_getinfo($ch));
    pre_print_r('Response ===>'.$json);
     **/
    $return_data = json_decode($json);
    /** Uncomment only for testing purposes */
    /*
    pre_print_r($return_data->url);
    */

    curl_close($ch);

    $response_data = [];
    $return_msg = '';

    if (property_exists($return_data, 'errorMessage')) {
        // ---delete lease guarantee since it returned error response
        // -----pre_print_r('HAS ERROR MESSAGE');
        dbDeleteLeaseGuarantee($lease_guarantee_id);

        $return_msg = $return_data->errorMessage;

        $response_data = [
            'status' => 'error',
            'status_message' => $return_msg,
        ];

    } else {
        // ---proceed since there is no error response from assuro
        // -----pre_print_r('has NO error message');
        /** Updates assuro_link and assuro_id column in pmgu_l_guarantee */
        dbUpdateAssuroFields($lease_guarantee_id, $return_data->url, $return_data->id);

        // O
        //        return '
        //        <p>Thank you for applying for a new bank guarantee.</p>
        //
        //        <p>You can view your application in <a href="'.$return_data->url.'" target="_blank" title="Click this link to go to Assuro"><img src="assets/images/assuro_logo.png" alt="Assuro Logo" class="icon" width="80" height="15" style="border-bottom: none !important; margin-top: 0px;" /></a>.</p>
        //        ';

        $return_msg = '
        <p>Thank you for applying for a new bank guarantee.</p>
        
        <p>You can view your application in <a href="' . $return_data->url . '" target="_blank" title="Click this link to go to Assuro"><img src="assets/images/assuro_logo.png" alt="Assuro Logo" class="icon" width="80" height="15" style="border-bottom: none !important; margin-top: 0px;" /></a>.</p>
        ';

        $response_data = [
            'status' => 'success',
            'status_message' => $return_msg,
        ];
    }

    return $response_data;



}

function getAssuroStatusUpdate()
{
    // //$jsonData = json_encode($item_data); //O
    // $jsonData = '{"requestType":"draftStatusUpdate", "data":{"id":"08d9c451-a4f5-4bb8-8e6d-f8e2feb8c032","draftType":"bankGuarantee","draftReferenceId":"10042-1162","draftStatus":"established","url":"https://uat.billd.dev/originations/08d9c451-a4f5-4bb8-8e6d-f8e2feb8c032"}}'; //O
    $jsonData = '{"requestType":"draftStatusUpdate", "data":{"id":"08d9d3d1-369a-4354-8dfc-135cfff56448","draftType":"bankGuarantee","draftReferenceId":"10042-1163","draftStatus":"established","url":"https://uat.billd.dev/originations/08d9d3d1-369a-4354-8dfc-135cfff56448"}}'; // O
    // $content = json_encode($jsonData);

    $baseUrl = 'https://api.uat.billd.dev/api/v1';

    $uri = $baseUrl . '/'; // POST

    $params = [];

    $uri .= '?' . http_build_query($params);

    // O
    //    $headers = [
    //        'Accept: application/json'
    //    ];

    // U
    $headers = [
        'Content-Type: application/json',
    ];

    // pre_print_r($uri);

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $uri);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HEADER, false);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $jsonData);
    // curl_setopt($ch, CURLOPT_POSTFIELDS, $content);

    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

    //    curl_setopt($ch, CURLOPT_HTTPHEADER, array(
    //        "Content-Type: application/json"
    //    ));

    curl_exec($ch);

    // TO DISPLAY RESPONSE:
    // pre_print_r($response);
    // pre_print_r(curl_getinfo($ch));

    curl_close($ch);

    // pre_print_r($response);

}

function submitAssuroRequest($item_data)
{
    /**
    Client id: 08d9b520-1011-4196-819b-fdeb36572395
    Api Key: NDk5M2ZmZDItZjdmYy00ZDAyLWE3MGYtN2Y3MDM1ZmQ5NzI3
     **/
    $jsonData = json_encode($item_data); // O
    // $jsonData = '{"channelPartnerId":"08d9b520-1011-4196-819b-fdeb36572395","draftType":"bankGuarantee","draftReferenceId":"10042-1139","organisationReferenceId":"10042","organisationName":"Demo","organisationAbnOrAcn":"w392398729356","organisationBusinessAddress":"Demo Street, Newtown VIC 3220","organisationPostalAddress":"Demo Street, Newtown VIC 3220","amount":57,"purpose":"rentalBond","agreementContent":"Test description with 57 amount","expiryDate":null,"expectedReturnDate":null,"parties":[{"type":"issuer","name":"Smith and Co","abnOrAcn":"***********","address":"Main st, Sydney, NSW 2000","representatives":[{"givenName":"Jane","familyName":"Doe","email":"<EMAIL>"}]},{"type":"beneficiary","name":"Large Owner and Co","abnOrAcn":"***********","address":"First Avenue, Suburb, NSW 2000","representatives":[{"givenName":"Louise","familyName":"Test","email":"<EMAIL>"},{"givenName":"Mary","familyName":"Smith","email":"<EMAIL>"}]},{"type":"other","name":"Demo","abnOrAcn":"w392398729356","address":"Demo Street, Newtown VIC 3220","representatives":[{"givenName":"Allyn","familyName":"Macalinao","email":"<EMAIL>"}]}]}';

    // pre_print_r('Updated JSON Data: '.$jsonData);

    /********** test HMAC - START **********/
    $baseUrl = 'https://api.uat.billd.dev/api/v1'; // sandbox
    // $baseUrl = 'https://private-anon-0f5024e63b-assuro.apiary-mock.com/api/v1'; //mock server
    // $baseUrl = 'https://www.kaufland.de/api/v1';
    // //$baseUrl = 'https://www.kaufland.de/api-docs/v1/';

    // Credentials for the API
    /** from https://www.kaufland.de/api/v1/?page=code-examples **/
    // $clientKey = '35f5dba43b471cd2ad28fd68e4825e2b';
    // $secretKey = 'b3575f222f7b768c25160b879699118b331c6883dd6010864b7ead130be77cd5';

    // Actual for cirrus8
    $clientKey = '08d9b520-1011-4196-819b-fdeb36572395';
    $secretKey = 'NDk5M2ZmZDItZjdmYy00ZDAyLWE3MGYtN2Y3MDM1ZmQ5NzI3';

    // For Sandbox
    // $clientKey = '08d9b520-1011-4196-819b-fdeb36572395';
    // $secretKey = 'd6b35644-4066-40a1-99d8-2e110050c274';

    // We are going to get information about the product with ID 20574181
    // (at the URL https://www.kaufland.de/product/20574181/) //sample only
    // $uri = $baseUrl . '/items/20574181/';
    // $uri = $baseUrl . '/draft/list/'; //GET
    $uri = $baseUrl . '/draft/create/'; // POST

    /**
    pre_print_r('clientKey: '.$clientKey);
    pre_print_r('secretKey: '.$secretKey);
    pre_print_r('POST req: '.$uri);
     **/

    // Also include the item's category and units in the response
    // $params = [
    //    'embedded' => 'category,units',
    // ];
    $params = [];

    // The query parameters must be URL-encoded, but the "=" and "&" signs should not be encoded
    $uri .= '?' . http_build_query($params);

    // Current Unix timestamp in seconds
    $timestamp = time();

    // pre_print_r(signRequest('GET', $uri, '', $timestamp, $secretKey));

    // Define all the mandatory headers
    // Use for GET request
    /**
    $headers = [
    'Accept: application/json',
    'Hm-Client: ' . $clientKey,
    'Hm-Timestamp: ' . $timestamp,
    'Hm-Signature: ' . signRequest('GET', $uri, '', $timestamp, $secretKey),
    ];
     **/

    // -----GET - START
    /*
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $uri);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

    $item = json_decode(curl_exec($ch), true);
    var_export($item);
    curl_close($ch);
    */
    // -----GET - END

    // Use for POST request
    // O
    $headers = [
        'Accept: application/json',
        'Hm-Client: ' . $clientKey,
        'Hm-Timestamp: ' . $timestamp,
        'Hm-Signature: ' . signRequest('POST', $uri, '', $timestamp, $secretKey),
    ];

    // U
    //    $headers = [
    //        {name: "x-ms-date", value: utcNow },
    //        {name: "x-ms-content-sha256", value: contentHash},
    //        {name: "Authorization", value: "HMAC-SHA256 Credential=" + credential + "&SignedHeaders=" + signedHeaders + "&Signature=" + signature},
    //
    //    'Accept: application/json',
    //        'Hm-Client: ' . $clientKey,
    //        'Hm-Timestamp: ' . $timestamp,
    //        'Hm-Signature: ' . signRequest('POST', $uri, '', $timestamp, base64_decode($secretKey)),
    //    ];

    // For POST - START
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $uri);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $jsonData);
    curl_setopt($ch, CURLOPT_HEADER, 1);
    curl_setopt($ch, CURLINFO_HEADER_OUT, 1);

    // ---------------
    $response = curl_exec($ch);
    [$header, $body] = explode("\r\n\r\n", $response, 2);
    echo "{$header}\n\n";

    echo '</br>';

    $responseCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    echo "HTTP Response Code: {$responseCode}\n";
    curl_close($ch);
    // ---------------
    // For POST - END
    /********** test HMAC - END   **********/
}

function leaseAssuro(&$context)
{
    global $sess;

    //    $view = new UserControl (userViews () , '/leases/leaseSelectorBond.html');
    $view = new UserControl(userViews(), '/leases/leaseAssuro.html');
    $view->bindAttributesFrom($_REQUEST);
    $view->bindAttributesFrom($context);

    $validationErrors = [];

    $view->items['user_is_superadmin'] = dbCheckSuperAdmin($_SESSION['userID']);

    $view->items['propertyBankAccountList'] = mapByKey(dbGetBankAccounts(), 'bankID');

    // ---new placement:
    $existing_issuers = json_decode($view->items['debtor_contacts_with_email_json'], true);
    $existing_beneficiaries  = json_decode($view->items['property_owners_with_email_json'], true);

    switch ($view->items['action']) {
        case 'addNewIssuer':

            // $existing_issuers = json_decode($view->items['debtor_contacts_with_email_json'], true);
            // $existing_beneficiaries  = json_decode($view->items['property_owners_with_email_json'], true);

            $issuer_ctr = count($existing_issuers ?? []);

            // ## Validate existing count
            /*
            if($issuer_ctr == 0) {
                $validationErrors[] = 'Please add an issuer representative first.';
            }
            */

            // ## Validate name
            if ($view->items['newIssuerCompanyContactName'] == '') {
                $validationErrors[] = 'Please provide a valid contact name';
            }

            // ## Validate email
            if (in_array($view->items['newIssuerCompanyContactEmail'], array_column($existing_issuers, 'contactEmail')) || in_array($view->items['newIssuerCompanyContactEmail'], array_column($existing_beneficiaries, 'contactEmail'))) {
                $validationErrors[] = 'Email <b>' . $view->items['newIssuerCompanyContactEmail'] . '</b> has been used multiple times. Please use a different email.';
            } elseif ($view->items['newIssuerCompanyContactEmail'] == $_SESSION['user_name']) {
                $validationErrors[] = 'Email <b>' . $view->items['newIssuerCompanyContactEmail'] . '</b> cannot be used since it is the same with the current user email. Please use a different email.';
            } elseif ($view->items['newIssuerCompanyContactEmail'] == '' || ! filter_var($view->items['newIssuerCompanyContactEmail'], FILTER_VALIDATE_EMAIL)) {
                $validationErrors[] = 'Email <b>' . $view->items['newIssuerCompanyContactEmail'] . '</b> is invalid. Please provide a valid email address';
            } else {
                // All valid so proceed
                $existing_issuers[] = [
                    'contactCheckboxStatus' => 0,
                    'contactName' => $view->items['newIssuerCompanyContactName'],
                    'contactEmail' => $view->items['newIssuerCompanyContactEmail'],
                    'isOwner' => false,
                ];

                // Clear fields for next add
                $view->items['newIssuerCompanyContactName'] = '';
                $view->items['newIssuerCompanyContactEmail'] = '';
            }

            break;
        case 'addNewBeneficiary':
            // pre_print_r($view->items['debtor_contacts_with_email_json']);

            // $existing_issuers = json_decode($view->items['debtor_contacts_with_email_json'], true);
            // $existing_beneficiaries = json_decode($view->items['property_owners_with_email_json'], true);

            $beneficiaries_ctr = count($existing_beneficiaries ?? []);

            // pre_print_r($beneficiaries_ctr);

            // ## Validate existing count
            /*
            if($beneficiaries_ctr == 0) {
                $validationErrors[] = 'Please add a beneficiary representative first.';
            }
            */

            // ## Validate name
            if ($view->items['newBeneficiaryCompanyContactName'] == '') {
                $validationErrors[] = 'Please provide a valid contact name';
            }

            // ## Validate email
            if (in_array($view->items['newBeneficiaryCompanyContactEmail'], array_column($existing_beneficiaries, 'contactEmail')) || in_array($view->items['newBeneficiaryCompanyContactEmail'], array_column($existing_issuers, 'contactEmail'))) {
                $validationErrors[] = 'Email <b>' . $view->items['newBeneficiaryCompanyContactEmail'] . '</b> has been used multiple times. Please use a different email.';
            } elseif ($view->items['newBeneficiaryCompanyContactEmail'] == $_SESSION['user_name']) {
                $validationErrors[] = 'Email <b>' . $view->items['newBeneficiaryCompanyContactEmail'] . '</b> cannot be used since it is the same with the current user email. Please use a different email.';
            } elseif ($view->items['newBeneficiaryCompanyContactEmail'] == '' || ! filter_var($view->items['newBeneficiaryCompanyContactEmail'], FILTER_VALIDATE_EMAIL)) {
                $validationErrors[] = 'Email <b>' . $view->items['newBeneficiaryCompanyContactEmail'] . '</b> is invalid. Please provide a valid email address';
            } else {
                // All valid so proceed
                $existing_beneficiaries[] = [
                    'contactCheckboxStatus' => 0,
                    'contactName' => $view->items['newBeneficiaryCompanyContactName'],
                    'contactEmail' => $view->items['newBeneficiaryCompanyContactEmail'],
                    'isOwner' => false,
                ];

                // Clear fields for next add
                $view->items['newBeneficiaryCompanyContactName'] = '';
                $view->items['newBeneficiaryCompanyContactEmail'] = '';
            }

            break;
        case 'assuroSaveC8Guarantee':
            $propertyID = $view->items['propertyID'];
            $leaseID = $view->items['leaseID'];
            $amount = $view->items['assuro_amount'];
            $agreementContent = $view->items['agreementContent'];
            $databaseID = dbGetClientID();
            $currentUser = $_SESSION['user_name'];
            $currentUserFull = $_SESSION['first_name'] . ' ' . $_SESSION['last_name'];
            $leaseGuaranteeID = '';

            $guaranteeType = 'BANKG';
            $guaPerson = $leaseID;
            $guaAmount = $view->items['assuro_amount'];
            $guaAccruedInterest = 0.00;
            $guaBankName = 'MBL';
            $guaExpDate = null;
            $guaNotes = $agreementContent;

            $debtor_con_ctr = (int) $view->items['debtor_contacts_with_email_ctr'];
            $prop_own_con_ctr = (int) $view->items['property_owners_with_email_ctr'];

            // $guaranteeDiarise = $view->items['guaranteeDiarise'];
            // $versionID = $view->items['versionID'];

            //            $view->items['api_url'] = c8_api.'administrator/management-reports/property-report-page';
            //
            //            if (!isValid($propertyID,TEXT_KEY,false)) $validationErrors[] = 'You have not entered a valid property';
            //            if (!isValid($leaseID,TEXT_KEY,false)) $validationErrors[] = 'You have not entered a valid lease';
            //            if (!isValid($guaranteeType,TEXT_KEY,false)) $validationErrors[] = 'You have not entered a valid guarantee type';
            //            if (!isValid($guaPerson,TEXT_LOOSE,false)) $validationErrors[] = 'You have not entered a valid person name';
            //            if (!isValid($guaAmount,TEXT_FLOATV2,false)) $validationErrors[] = 'You have not used a valid amount('.$guaAmount.')';
            //            if (!isValid($guaBankName,TEXT_LOOSE,false)) $validationErrors[] = 'You have not entered a valid bank name';
            //
            //            if(!empty($guaAccruedInterest)){
            //                if (!isValid($guaAccruedInterest,TEXT_FLOATV2,false)) $validationErrors[] = 'You have not used a valid amount('.$guaAmount.')';
            //            }
            //            if(!empty($guaExpDate)){
            //                if (!isValid($guaExpDate,TEXT_SMARTDATE,false)) $validationErrors[] =  'You have not selected a valid to date';
            //            }

            if (! isValid($guaAmount, TEXT_FLOATV2, false)) {
                $validationErrors[] = 'You have not entered a valid amount.';
            }

            if ($guaAmount < 25000) {
                $validationErrors[] = 'Bank guarantee amount should be at least <b>25,000.00</b>';
            }

            if (empty($guaNotes)) {
                $validationErrors[] = 'You have not entered a valid description.';
            }

            //            if ($debtor_con_ctr < 0) $validationErrors[] = 'You have not chosen an issuer contact.';
            //            if ($prop_own_con_ctr < 0) $validationErrors[] = 'You have not chosen a beneficiary contact.';
            //
            //            pre_print_r($debtor_con_ctr);
            //            pre_print_r(gettype($debtor_con_ctr));

            if (noErrors($validationErrors)) {
                $o = [];
                $o['guaranteeType'] = $guaranteeType;
                $o['guaPerson'] = $guaPerson;
                $o['guaAmount'] = $guaAmount;
                $o['guaAccruedInterest'] = $guaAccruedInterest;
                $o['guaBankName'] = $guaBankName;
                $o['guaExpDate'] = $guaExpDate;
                $o['guaNotes'] = $guaNotes;
                $o['pmgu_file_link'] = '';
                $o['pmgu_prop'] = $propertyID;
                $o['pmgu_lease'] = $leaseID;
                $o['versionID'] = $versionID;
                // $getId = (isUserType(USER_TRACC)) ? dbAddLeaseGuarantee($o) : dbAddTempLeaseGuarantee($o);
                $getLeaseGuaranteeID = dbAddLeaseGuaranteeReturnID($o);

                $view->items['guaranteeType'] = '';
                $view->items['guaPerson'] = '';
                $view->items['guaAmount'] = '';
                $view->items['guaAccruedInterest'] = '';
                $view->items['guaBankName'] = '';
                $view->items['guaExpDate'] = '';
                $view->items['guaNotes'] = '';
                $view->items['guaranteeDiarise'] = false;

                // reload list
                $view->items['guaranteeList'] = dbGetLeaseGuarantee($view->items['propertyID'], $view->items['leaseID']);

                $debtor_contact_ctr = $view->items['debtor_contacts_with_email_ctr'];
                // $prop_own_contact_ctr = $view->items['property_owners_ctr'];
                $prop_own_contact_ctr = $view->items['property_owners_with_email_ctr'];

                $cms_config = dbGetCmsConfig();

                $for_assuro_details = [
                    // 'channelPartnerId' => '08d9b520-1011-4196-819b-fdeb36572395', //dev
                    'channelPartnerId' => $channel_partner_id, // depends on specified env flag
                    'draftType' =>  'bankGuarantee',
                    'draftReferenceId' =>  $databaseID . '-' . $getLeaseGuaranteeID,
                    'organisationReferenceId' => $databaseID,
                    'organisationName' => $cms_config['organisationName'],
                    'organisationAbnOrAcn' => $cms_config['organisationAbnOrAcn'],
                    // "organisationIndustry" => "construction",
                    'organisationBusinessAddress' => $cms_config['organisationBusinessAddress'],
                    'organisationPostalAddress' => $cms_config['organisationPostalAddress'],
                    'amount' => ($amount + 0),
                    'purpose' => 'rentalBond', // always
                    // "agreementContent" => "Bond for 100 Market St Sydney 2000",
                    'agreementContent' => $agreementContent,
                    'expiryDate' => null,
                    'expectedReturnDate' => null,
                ];

                /**
                "parties": [
                {
                "type": "issuer",
                "name": "Freeman PTY LTD",
                "abnOrAcn": "***********",
                "address": "38 Adelaide St, Fremantle 6160, WA",
                "representatives": [
                {
                "givenName": "Alex",
                "familyName": "Bower",
                "email": "<EMAIL>",
                "isOwner": true
                },
                {
                "givenName": "Mark",
                "familyName": "Sullivan",
                "email": "<EMAIL>",
                "isOwner": false
                }
                ]
                },
                {
                "type": "beneficiary",
                "name": "Example Organisation",
                "abnOrAcn": "***********",
                "address": "100 Market St, Sydney 2000, NSW",
                "representatives": [
                {
                "givenName": "Gerry",
                "familyName": "Larkson",
                "email": "<EMAIL>",
                "isOwner": false
                }
                ]
                },
                {
                "type": "beneficiary",
                "name": "Magic Perfume",
                "abnOrAcn": "**************",
                "address": "Dorney Esp, Champion Lakes 6111, WA",
                "representatives": null
                }
                ]
                 */
                $parties = [];

                // ## ISSUER - START
                $issuer_representatives = [];
                $debtor_details = $view->items['debtor_details'];
                $issuer_name_parts = '';
                $issuer_given_name = '';
                $issuer_family_name = '';
                for ($i = 0; $i < $debtor_contact_ctr; $i++) {
                    // pre_print_r($view->items['debtorContactCheckboxStatus_'.$i]);

                    if ($view->items['debtorContactCheckboxStatus_' . $i]) {
                        $issuer_name_parts = explode(' ', $view->items['contactName_' . $i]);
                        $issuer_given_name = $issuer_name_parts[0];
                        $issuer_family_name = $issuer_name_parts[1];

                        $issuer_representatives[] = [
                            // "givenName" => $view->items['contactName_'.$i],
                            'givenName' => $issuer_given_name,
                            'familyName' => $issuer_family_name,
                            'email' => $view->items['contactEmail_' . $i],
                            'isOwner' => false,
                            // "isOwner" => ($view->items['isOwner_'.$i] ? true: false)
                        ];
                    }
                }

                // pre_print_r(gettype(count($issuer_representatives)));

                if (count($issuer_representatives ?? []) > 0) {
                    $parties[] = [
                        'type' => 'issuer',
                        'name' => $view->items['debtor_detail_name'],
                        'abnOrAcn' => $view->items['debtor_detail_abn'],
                        'address' => $view->items['debtor_detail_address'],
                        'representatives' => $issuer_representatives,
                    ];
                } else {
                    $validationErrors[] = 'Please select an issuer contact.';
                    dbDeleteLeaseGuarantee($getLeaseGuaranteeID);
                }

                // ## ISSUER - END

                // ## BENEFICIARIES - START
                $beneficiary_representatives = [];
                $beneficiary_details = $view->items['beneficiary_details'];
                $beneficiary_name_parts = '';
                $beneficiary_given_name = '';
                $beneficiary_family_name = '';
                for ($i = 0; $i < $prop_own_contact_ctr; $i++) {
                    // pre_print_r($view->items['debtorContactCheckboxStatus_'.$i]);

                    if ($view->items['bCompanyContactCheckboxStatus_' . $i]) {
                        // pre_print_r($view->items['contactName_'.$i]);

                        $beneficiary_name_parts = explode(' ', $view->items['bContactName_' . $i]);
                        $beneficiary_given_name = $beneficiary_name_parts[0];
                        $beneficiary_family_name = $beneficiary_name_parts[1];


                        $beneficiary_representatives[] = [
                            // "givenName" => $view->items['contactName_'.$i],
                            'givenName' => $beneficiary_given_name,
                            'familyName' => $beneficiary_family_name,
                            'email' => $view->items['bContactEmail_' . $i],
                            'isOwner' => false,
                            // "isOwner" => ($view->items['bIsOwner_'.$i] ? true: false)
                        ];
                    }
                }

                // pre_print_r(gettype(count($beneficiary_representatives)));

                if (count($beneficiary_representatives ?? []) > 0) {
                    $parties[] = [
                        'type' => 'beneficiary',
                        'name' => $view->items['beneficiary_detail_name'],
                        'abnOrAcn' => $view->items['beneficiary_detail_abn'],
                        'address' => $view->items['beneficiary_detail_address'],
                        'representatives' => $beneficiary_representatives,
                    ];
                } else {
                    $validationErrors[] = 'Please select a beneficiary contact.';
                    dbDeleteLeaseGuarantee($getLeaseGuaranteeID);
                }

                // ## BENEFICIARIES - END

                // ## OTHER - START
                /** From Alex Vlas: the owner is the person that will have an account on assuro (the logged-in user of cirrus8)
                 * Other section: is usually the owner so isOwner will always be set to TRUE
                 */
                $other_representatives = [];
                $other_representatives[] = [
                    'givenName' => $_SESSION['first_name'],
                    'familyName' => $_SESSION['last_name'],
                    'email' => $currentUser,
                    'isOwner' => true,
                ];

                if (count($other_representatives ?? []) > 0) {
                    $parties[] = [
                        'type' => 'other',
                        'name' => $cms_config['organisationName'],
                        'abnOrAcn' => $cms_config['organisationAbnOrAcn'],
                        'address' => $cms_config['organisationBusinessAddress'],
                        'representatives' => $other_representatives,
                    ];
                }

                // ## OTHER - END

                $for_assuro_details['parties'] = $parties;

                if (noErrors($validationErrors)) {
                    // ---Submit Assuro Request
                    // //$view->items['statusMessage'] = submitAssuroRequestV2($for_assuro_details, $getLeaseGuaranteeID); //O
                    $response_list = submitAssuroRequestV2($for_assuro_details, $getLeaseGuaranteeID);

                    if ($response_list['status'] == 'error') {
                        $validationErrors[] = $response_list['status_message'];
                    } else {
                        $view->items['statusMessage'] = $response_list['status_message'];
                    }
                } else {
                    // ---Had no issuer or beneficiary selected so delete the initial entry
                    dbDeleteLeaseGuarantee($getLeaseGuaranteeID);
                }



                $json_for_assuro_details = json_encode($for_assuro_details);
                // pre_print_r($json_for_assuro_details);

            }

            // //            if (noErrors($validationErrors)) {
            //                $o = array();
            //                $o['guaranteeType'] = $guaranteeType;
            //                $o['guaPerson'] = $guaPerson;
            //                $o['guaAmount'] = $guaAmount;
            //                $o['guaAccruedInterest'] = $guaAccruedInterest;
            //                $o['guaBankName'] = $guaBankName;
            //                $o['guaExpDate'] = $guaExpDate;
            //                $o['guaNotes'] = $guaNotes;
            //                $o['pmgu_file_link'] = '';
            //                $o['pmgu_prop'] = $propertyID;
            //                $o['pmgu_lease'] = $leaseID;
            //                $o['versionID'] = $versionID;
            //                //$getId = (isUserType(USER_TRACC)) ? dbAddLeaseGuarantee($o) : dbAddTempLeaseGuarantee($o);
            //                $getLeaseGuaranteeID = dbAddLeaseGuaranteeReturnID($o);
            //
            //                $view->items['guaranteeType'] = '';
            //                $view->items['guaPerson'] = '';
            //                $view->items['guaAmount'] = '';
            //                $view->items['guaAccruedInterest'] = '';
            //                $view->items['guaBankName'] = '';
            //                $view->items['guaExpDate'] = '';
            //                $view->items['guaNotes'] = '';
            //                $view->items['guaranteeDiarise'] = false;
            //
            //                //reload list
            //                $view->items['guaranteeList'] = dbGetLeaseGuarantee($view->items['propertyID'],$view->items['leaseID']);
            //
            // //            }
            //
            //            $debtor_contact_ctr = $view->items['debtor_contacts_with_email_ctr'];
            //            //$prop_own_contact_ctr = $view->items['property_owners_ctr'];
            //            $prop_own_contact_ctr = $view->items['property_owners_with_email_ctr'];
            //
            //            $cms_config = dbGetCmsConfig();
            //
            //            $for_assuro_details = array();
            //
            //            $for_assuro_details = array(
            //                //'channelPartnerId' => '08d9b520-1011-4196-819b-fdeb36572395', //dev
            //                'channelPartnerId' => $channel_partner_id, //depends on specified env flag
            //                "draftType" =>  "bankGuarantee",
            //                "draftReferenceId" =>  $databaseID.'-'.$getLeaseGuaranteeID,
            //                "organisationReferenceId" => $databaseID,
            //                "organisationName" => $cms_config['organisationName'],
            //                "organisationAbnOrAcn" => $cms_config['organisationAbnOrAcn'],
            //                //"organisationIndustry" => "construction",
            //                "organisationBusinessAddress" => $cms_config['organisationBusinessAddress'],
            //                "organisationPostalAddress" => $cms_config['organisationPostalAddress'],
            //                "amount" => ($amount + 0),
            //                "purpose" => "rentalBond", //always
            //                //"agreementContent" => "Bond for 100 Market St Sydney 2000",
            //                "agreementContent" => $agreementContent,
            //                "expiryDate" => null,
            //                "expectedReturnDate" => null,
            //            );
            //
            //            /**
            //            "parties": [
            //                {
            //                "type": "issuer",
            //                "name": "Freeman PTY LTD",
            //                "abnOrAcn": "***********",
            //                "address": "38 Adelaide St, Fremantle 6160, WA",
            //                "representatives": [
            //                        {
            //                            "givenName": "Alex",
            //                            "familyName": "Bower",
            //                            "email": "<EMAIL>",
            //                            "isOwner": true
            //                            },
            //                            {
            //                            "givenName": "Mark",
            //                            "familyName": "Sullivan",
            //                            "email": "<EMAIL>",
            //                            "isOwner": false
            //                        }
            //                    ]
            //                },
            //                {
            //                "type": "beneficiary",
            //                "name": "Example Organisation",
            //                "abnOrAcn": "***********",
            //                "address": "100 Market St, Sydney 2000, NSW",
            //                "representatives": [
            //                        {
            //                        "givenName": "Gerry",
            //                        "familyName": "Larkson",
            //                        "email": "<EMAIL>",
            //                        "isOwner": false
            //                        }
            //                    ]
            //                },
            //                {
            //                "type": "beneficiary",
            //                "name": "Magic Perfume",
            //                "abnOrAcn": "**************",
            //                "address": "Dorney Esp, Champion Lakes 6111, WA",
            //                "representatives": null
            //                }
            //            ]
            //             */
            //
            //            $parties = array();
            //
            //            ### ISSUER
            //            $issuer_representatives = array();
            //            $debtor_details = $view->items['debtor_details'];
            //            $issuer_name_parts = '';
            //            $issuer_given_name = '';
            //            $issuer_family_name = '';
            //            for($i=0; $i<$debtor_contact_ctr; $i++){
            //                //pre_print_r($view->items['debtorContactCheckboxStatus_'.$i]);
            //
            //                if ($view->items['debtorContactCheckboxStatus_'.$i])
            //                {
            //                    $issuer_name_parts = explode(' ', $view->items['contactName_'.$i]);
            //                    $issuer_given_name = $issuer_name_parts[0];
            //                    $issuer_family_name = $issuer_name_parts[1];
            //
            //                    $issuer_representatives[] = array(
            //                        //"givenName" => $view->items['contactName_'.$i],
            //                        "givenName" => $issuer_given_name,
            //                        "familyName" => $issuer_family_name,
            //                        "email" => $view->items['contactEmail_'.$i],
            //                        "isOwner" => false
            //                        //"isOwner" => ($view->items['isOwner_'.$i] ? true: false)
            //                    );
            //                }
            //            }
            //
            //            if(count($issuer_representatives) > 0){
            //                $parties[] = array(
            //                    "type" => "issuer",
            //                    "name" => $view->items['debtor_detail_name'],
            //                    "abnOrAcn" => $view->items['debtor_detail_abn'],
            //                    "address" => $view->items['debtor_detail_address'],
            //                    "representatives" => $issuer_representatives
            //                );
            //            }
            //
            //            ### BENEFICIARIES - NEW
            //            $beneficiary_representatives = array();
            //            $beneficiary_details = $view->items['beneficiary_details'];
            //            $beneficiary_name_parts = '';
            //            $beneficiary_given_name = '';
            //            $beneficiary_family_name = '';
            //            for($i=0; $i<$prop_own_contact_ctr; $i++){
            //                //pre_print_r($view->items['debtorContactCheckboxStatus_'.$i]);
            //
            //                if ($view->items['bCompanyContactCheckboxStatus_'.$i])
            //                {
            //                    //pre_print_r($view->items['contactName_'.$i]);
            //
            //                    $beneficiary_name_parts = explode(' ', $view->items['bContactName_'.$i]);
            //                    $beneficiary_given_name = $beneficiary_name_parts[0];
            //                    $beneficiary_family_name = $beneficiary_name_parts[1];
            //
            //
            //                    $beneficiary_representatives[] = array(
            //                        //"givenName" => $view->items['contactName_'.$i],
            //                        "givenName" => $beneficiary_given_name,
            //                        "familyName" => $beneficiary_family_name,
            //                        "email" => $view->items['bContactEmail_'.$i],
            //                        "isOwner" => false
            //                        //"isOwner" => ($view->items['bIsOwner_'.$i] ? true: false)
            //                    );
            //                }
            //            }
            //
            //            if(count($beneficiary_representatives) > 0){
            //                $parties[] = array(
            //                    "type" => "beneficiary",
            //                    "name" => $view->items['beneficiary_detail_name'],
            //                    "abnOrAcn" => $view->items['beneficiary_detail_abn'],
            //                    "address" => $view->items['beneficiary_detail_address'],
            //                    "representatives" => $beneficiary_representatives
            //                );
            //            }
            //
            // //            ### BENEFICIARIES
            // //            for($i=0; $i<$prop_own_contact_ctr; $i++){
            // //                $beneficiary_representatives = array();
            // //
            // //                //pre_print_r($view->items['companyContactCheckboxStatus_'.$i]);
            // //
            // //
            // //                $beneficiary_name_parts = '';
            // //                $beneficiary_given_name = '';
            // //                $beneficiary_family_name = '';
            // //                $representative_ctr = $view->items['prop_own_contacts_with_email_ctr_'.$i];
            // //                for($j=0; $j<$representative_ctr; $j++){
            // //                    if ($view->items['companyContactCheckboxStatus_'.$i.'_'.$j])
            // //                    {
            // //                        $beneficiary_name_parts = explode(' ', $view->items['contactName_'.$i.'_'.$j]);
            // //                        $beneficiary_given_name = $beneficiary_name_parts[0];
            // //                        $beneficiary_family_name = $beneficiary_name_parts[1];
            // //
            // //                        //pre_print_r('Ctr parts:');
            // //                        //pre_print_r(count($beneficiary_name_parts));
            // //
            // //                        $beneficiary_representatives[] = array(
            // //                            //"givenName" => $view->items['contactName_'.$i.'_'.$j],
            // //                            "givenName" => $beneficiary_given_name,
            // //                            "familyName" => $beneficiary_family_name,
            // //                            "email" => $view->items['contactEmail_'.$i.'_'.$j]
            // //                            //"isOwner" => ($view->items['isOwner_'.$i.'_'.$j] ? true: false)
            // //                        );
            // //                    }
            // //                }
            // //
            // //                if(count($beneficiary_representatives) > 0){
            // //                    $parties[] = array(
            // //                        "type" => "beneficiary",
            // //                        "name" => $view->items['prop_own_name_'.$i],
            // //                        "abnOrAcn" => $view->items['prop_own_name_abn_'.$i],
            // //                        "address" => $view->items['prop_own_name_address_'.$i],
            // //                        "representatives" => $beneficiary_representatives
            // //                    );
            // //                }
            // //
            // //            }
            //
            //            ### OTHER
            //            /** From Alex Vlas: the owner is the person that will have an account on assuro (the logged-in user of cirrus8)
            //             * Other section: is usually the owner so isOwner will always be set to TRUE
            //             */
            //            $other_representatives = array();
            //            $other_representatives[] = array(
            //                "givenName" => $_SESSION['first_name'],
            //                "familyName" => $_SESSION['last_name'],
            //                "email" => $currentUser,
            //                "isOwner" => true
            //            );
            //
            //            if(count($other_representatives) > 0){
            //                $parties[] = array(
            //                    "type" => "other",
            //                    "name" => $cms_config['organisationName'],
            //                    "abnOrAcn" => $cms_config['organisationAbnOrAcn'],
            //                    "address" => $cms_config['organisationBusinessAddress'],
            //                    "representatives" => $other_representatives
            //                );
            //            }
            //
            //
            //            $for_assuro_details['parties'] = $parties;
            //
            //            //Submit Assuro Request
            //            //$view->items['statusMessage'] = submitAssuroRequestV2($for_assuro_details, $getLeaseGuaranteeID); //O
            //            $response_list = submitAssuroRequestV2($for_assuro_details, $getLeaseGuaranteeID);
            //
            //            if($response_list['status'] == 'error'){
            //                $validationErrors[] = $response_list['status_message'];
            //            }else{
            //                $view->items['statusMessage'] = $response_list['status_message'];
            //            }
            //
            //
            //
            //            $json_for_assuro_details = json_encode($for_assuro_details);
            //
            //            //pre_print_r($json_for_assuro_details);

            break;

        case 'insertProperty':
            // check for duplicate property codebankPartitioning
            $lookup = dbGetPropertyDetails($view->items['bondPropertyID']);
            if ($lookup) {
                $validationErrors[] = 'The bond/deposit property code you entered already exists.';
            }

            if (! $view->items['bondPropertyBankAccount']) {
                $validationErrors[] = 'You have not selected a bank account';
            }

            // check for duplicate property name
            if (noErrors($validationErrors)) {
                dbInsertBondProperty($view->items['bondOriginalPropertyID'], $view->items['bondPropertyID'], $view->items['bondPropertyName'], $view->items['bondPropertyBankAccount']);
                $view->items['statusMessage'] = 'Bond/Deposit Property  : ' . $view->items['bondPropertyID'] . ' has been added';
            }

            break;

        case 'insertLease':
            // check for duplicate lease
            $lookup = dbGetLease($view->items['propertyIDBond'], $view->items['bondLeaseID']);
            if ($lookup) {
                $validationErrors[] = 'The bond/deposit lease code you entered already exists.';
            }

            // check for duplicate property name
            if (noErrors($validationErrors)) {
                dbInsertBondLease($view->items['bondOriginalPropertyID'], $view->items['bondOriginalLeaseID'], $view->items['propertyIDBond'], $view->items['bondLeaseID'], $view->items['bondLeaseName']);
                $view->items['statusMessage'] = 'Bond/Deposit Lease  : ' . $view->items['bondLeaseID'] . ' has been added';

                $view->items['lastAddedBondPropertyID'] = $view->items['propertyIDBond'];
                $view->items['lastAddedBondLeaseID'] = $view->items['bondLeaseID'];
            }

            break;

    }

    switch ($view->items['mode']) {
        case 'new':

            $contacts = dbGetLeaseContacts($view->items['propertyID'], $view->items['leaseID']);

            $contacts_with_email = [];
            foreach ($contacts as $val_contact) {
                // echo "this contact id: ".$val_contact['contactID'];
                // $contacts[$key_contact]->ocr_ap_id = $val->temp_ocr_ap_ai_id;
                // $contacts[$key_contact]->transaction_type = $val->trans_type;

                $contactsDetails = dbGetLeaseContactEmailDetails($view->items['propertyID'], $view->items['leaseID'], $val_contact['contactID']);

                foreach ($contactsDetails as $detail) {
                    $contacts_with_email[] = [
                        'contactCheckboxStatus' => 0, // for initial checkbox value
                        'contactName' => $val_contact['contactName'],
                        'contactID' => $val_contact['contactID'],
                        'contactRole' => $val_contact['contactRole'],
                        'contactRoleDesc' => $val_contact['contactRoleDesc'],
                        'contactDetailId' => $detail['contactDetailID'],
                        'contactEmail' => $detail['detail'],
                    ];
                }
            }

            // pre_print_r($contacts);
            // pre_print_r($contacts_with_email);

            $lease_details = dbGetLeaseDetails($view->items['propertyID'], $view->items['leaseID']);

            // pre_print_r($lease_details['debtorID']);

            $company_contacts = dbGetCompanyContactsWithRoleDesc($lease_details['debtorID']);

            $company_contacts_with_email = [];
            foreach ($company_contacts as $val_co_contact) {
                $company_contact_details = dbGetCompanyContactEmailDetails($lease_details['debtorID'], $val_co_contact['contactID']);

                foreach ($company_contact_details as $co_detail) {
                    $company_contacts_with_email[] = [
                        'contactCheckboxStatus' => 0, // for initial checkbox value
                        'contactName' => $val_co_contact['contactName'],
                        'contactID' => $val_co_contact['contactID'],
                        'contactRole' => $val_co_contact['contactRole'],
                        'contactRoleDesc' => $val_co_contact['contactRoleDesc'],
                        // 'contactDetailId' => $co_detail['contactDetailID'],
                        'contactDetailId' => $co_detail['primaryPhoneID'],
                        'contactEmail' => $co_detail['detail'],
                    ];
                }
            }

            // pre_print_r($company_contacts);
            // pre_print_r($company_contacts_with_email);
            // ## Get lease debtor - START
            $main_lease_details = dbGetLeaseDebtorDetails($view->items['propertyID'], $view->items['leaseID']);
            $debtor_contacts = dbGetCompanyContactsWithRoleDesc($main_lease_details['debtorID']);

            $debtor_contacts_with_email = [];
            foreach ($debtor_contacts as $val_debtor_contact) {
                $debtor_contact_details = dbGetCompanyContactEmailDetails($main_lease_details['debtorID'], $val_debtor_contact['contactID']);

                if (! $debtor_contact_details['detail']) {
                    continue;
                }

                $debtor_contacts_with_email[] = [
                    'contactCheckboxStatus' => 0, // for initial checkbox value
                    'contactName' => $val_debtor_contact['contactName'],
                    'contactID' => $val_debtor_contact['contactID'],
                    'contactRole' => $val_debtor_contact['contactRole'],
                    'contactRoleDesc' => $val_debtor_contact['contactRoleDesc'],
                    // 'contactDetailId' => $debtor_contact_details['contactDetailID'],
                    'contactDetailId' => $debtor_contact_details['primaryPhoneID'],
                    'contactEmail' => $debtor_contact_details['detail'],
                    'isOwner' => $val_debtor_contact['contactRole'] == 'OWNER',
                ];

            }

            // $view->items['debtor_details'] = $main_lease_details;
            // $view->items['debtor_contacts'] = $debtor_contacts;

            if ($view->items['action'] != 'assuroSaveC8Guarantee') {
                $view->items['debtor_detail_name'] = $main_lease_details['companyName'];
                $view->items['debtor_detail_abn'] = $main_lease_details['companyABN'];
                $view->items['debtor_detail_address'] = $main_lease_details['companyAddress'];
                $view->items['debtor_contacts_with_email'] = $debtor_contacts_with_email;
            } else {
                $view->items['debtor_contacts_with_email'] = $existing_issuers;
            }

            // ## Get lease debtor - END

            // ## Get property owners - START
            // $prop_owners = dbGetPropertyOwnersDetailed($view->items['propertyID']); //retrieves from pmos_o_share
            $prop_owner_details = dbGetMainPropertyOwnerDetailed($view->items['propertyID']); // retrieves from pmpr_property.pmpr_owner
            $prop_owner_contacts = dbGetCompanyContactsWithRoleDesc($prop_owner_details['companyID']);

            $prop_owner_contacts_with_email = [];
            foreach ($prop_owner_contacts as $val_contact) {
                $prop_owner_contact_details = dbGetCompanyContactEmailDetails($prop_owner_details['companyID'], $val_contact['contactID']);

                // pre_print_r($prop_owner_contact_details);

                if (! $prop_owner_contact_details['detail']) {
                    continue;
                }

                $prop_owner_contacts_with_email[] = [
                    'contactCheckboxStatus' => 0, // for initial checkbox value
                    'contactName' => $val_contact['contactName'],
                    'contactID' => $val_contact['contactID'],
                    'contactRole' => $val_contact['contactRole'],
                    'contactRoleDesc' => $val_contact['contactRoleDesc'],
                    // 'contactDetailId' => $prop_owner_contact_details['contactDetailID'],
                    'contactDetailId' => $prop_owner_contact_details['primaryPhoneID'],
                    'contactEmail' => $prop_owner_contact_details['detail'],
                    'isOwner' => $val_contact['contactRole'] == 'OWNER',
                ];

            }

            // ---
            //            //foreach($prop_owners as $key_prop_owner => $prop_owner){
            //                $prop_owner_contacts = dbGetCompanyContactsWithRoleDesc($prop_owner['companyID']);
            //
            //                //loop contacts with email only
            //                foreach($prop_owner_contacts as $prop_owner_co_detail){
            //                    $prop_owner_contact_details = dbGetCompanyContactEmailDetails($prop_owner['companyID'], $prop_owner_co_detail['contactID']);
            //
            //
            //                        if (!$prop_owner_contact_details['detail'])
            //                            continue;
            //
            //                        $prop_owners[$key_prop_owner]['representatives'][] = array(
            //                            'contactCheckboxStatus' => 0, //for initial checkbox value
            //                            'contactName' => $prop_owner_co_detail['contactName'],
            //                            'contactID' => $prop_owner_co_detail['contactID'],
            //                            'contactRole' => $prop_owner_co_detail['contactRole'],
            //                            'contactRoleDesc' => $prop_owner_co_detail['contactRoleDesc'],
            //                            //'contactDetailId' => $prop_owner_contact_details['contactDetailID'],
            //                            'contactDetailId' => $prop_owner_contact_details['primaryPhoneID'],
            //                            'contactEmail' => $prop_owner_contact_details['detail'],
            //                            'isOwner' => $prop_owner_co_detail['contactRole'] == 'OWNER'
            //                        );
            //                }
            //            //}
            // ## Get property owners - END

            // $view->items['beneficiary_details'] = $prop_owner_details;
            // $view->items['property_owners'] = $prop_owners;

            if ($view->items['action'] != 'assuroSaveC8Guarantee') {
                $view->items['beneficiary_detail_name'] = $prop_owner_details['companyName'];
                $view->items['beneficiary_detail_abn'] = $prop_owner_details['companyABN'];
                $view->items['beneficiary_detail_address'] = $prop_owner_details['companyAddress'];
                $view->items['property_owners_with_email'] = $prop_owner_contacts_with_email;
            } else {
                $view->items['property_owners_with_email'] = $existing_beneficiaries;
            }

            $view->items['contacts'] = $contacts;
            $view->items['contacts_with_email'] = $contacts_with_email;
            $view->items['roleList'] = mapParameters(dbGetParams('LROLETYPE'));

            $view->items['newContactID'] = 0;
            $view->items['newContactName'] = null;
            $view->items['newContactRole'] = null;
            $view->items['newContactEmail'] = null;

            $view->items['company_contacts'] = $company_contacts;
            $view->items['company_contacts_with_email'] = $company_contacts_with_email;
            $view->items['json_company_contacts_with_email'] = json_encode($company_contacts_with_email);
            $view->items['companyRoleList'] = mapParameters(dbGetParams('CROLETYPE'));

            $view->items['newCompanyContactID'] = 0;
            $view->items['newCompanyContactName'] = null;
            $view->items['newCompanyContactRole'] = null;
            $view->items['newCompanyContactEmail'] = null;

            break;
        case 'reload':
            $view->items['debtor_contacts_with_email'] = $existing_issuers;
            $view->items['property_owners_with_email'] = $existing_beneficiaries;

            // reload list
            $view->items['guaranteeList'] = dbGetLeaseGuarantee($view->items['propertyID'], $view->items['leaseID']);
            //            if(isUserType(USER_TRACC)){
            //                $view->items['guaranteeList'] = dbGetLeaseGuarantee($view->items['propertyID'],$view->items['leaseID']);
            //            }else{
            //                $view->items['guaranteeList'] = dbGetTempLeaseGuarantee($view->items['propertyID'],$view->items['leaseID'],$view->items['versionID']);
            //            }

            break;
    }

    if ($validationErrors !== []) {
        $view->items['validationErrors'] = $validationErrors;
    }

    $view->render();

}

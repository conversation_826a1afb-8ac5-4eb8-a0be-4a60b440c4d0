<?php

if (! defined('INCOME')) {
    define('INCOME', 'I');
}

if (! defined('EXPENDITURE')) {
    define('EXPENDITURE', 'E');
}

if (! defined('PROPERTY_EXISTING')) {
    define('PROPERTY_EXISTING', 1);
}

if (! defined('PROPERTY_NEW')) {
    define('PROPERTY_NEW', 2);
}

require_once SYSTEMPATH . '/lib/fileuploader/class.fileuploader.php';
function home(&$context)
{
    global $pathPrefix, $clientDirectory;
    $userpermission = new userpermission();
    $result = $userpermission->userHasPageAccess(__FUNCTION__);

    global $stateList, $taskClass, $taskDescription;

    if (! isPostback()) {
        $view = new MasterPage(userViews(), '/properties/home.html');
        $view->setSection($context['module']);
    } else {
        $view = new UserControl(userViews(), '/properties/home.html');
    }

    $view->bindAttributesFrom($_REQUEST);

    // redirect to new manage lease page for UK SERVER
    if (CDF_COUNTRY_CODE == 'GB') {
        if ($view->items['propertyID']) {
            header('Location: ' . HTTPHOST . '/framework/index.php?module=properties&command=v2_manage_property_page&property_code=' . $view->items['propertyID']);
        } else {
            header('Location: ' . HTTPHOST . '/framework/index.php?module=properties&command=v2_manage_property_page');
        }
    }

    $view->items['last_error'] = $context['last_error'];
    $view->items['statusMessage'] = $context['statusMessage'];

    $warningList =  [];

    $view->items['propStatus'] =
    [
        '0' => 'Active',
        '1' => 'Inactive',
    ];
    $view->items['accountingBasisList'] =
    [
        'C' => 'Cash',
        'A' => 'Accruals',
    ];
    $view->items['propertyLeaseTypeList'] = dbGetParams('PROP_LEASE');
    $view->items['propertyLeaseTypeMap'] = dbGetParams('PROP_LEASE', false, true);
    $view->items['yesNoOptionBoolean'] =
    [
        1 => 'Yes',
        0 => 'No',
    ];
    $view->items['propertyInspectionFrequencyList'] =
    [
        'M' => 'Monthly',
        'Q' => 'Quarterly',
        'S' => 'Semi-Annual',
        'A' => 'Annually',
    ];
    $view->items['communicationHistory'] = [
        'E' => 'Email',
        'S' => 'SMS',
    ];

    if (! isset($view->items['commHistory'])) {
        $view->items['commHistory'] = 'E';
    }

    $view->items['smsMessage'] = dbGetSmsMessage($view->items['propertyID']);
    $view->items['propertyInspectionAccountList'] = $view->items['RentReviewFeeAccountListExp'] = dbGetGroupedAccountsByCodeandName(EXPENDITURE);
    $view->items['propertyInspectionAccountListInc'] = dbGetGroupedAccountsByCodeandName(INCOME, null, null);

    if (! isset($view->items['propertyCountry'])) {
        $view->items['propertyCountry'] = dbGetParam('PROPDEF', 'COUNTRY');
    }

    $view->items['stateList'] = dbGetStates($view->items['propertyCountry']);
    $view->items['countryList'] = dbGetCountries();
    $propertyTypes = [PROPERTY_EXISTING => 'Existing Property', PROPERTY_NEW => 'New Property'];
    $view->items['propertyEntryTypeList'] = $propertyTypes;
    if (! isset($view->items['propertyEntryType'])) {
        $view->items['propertyEntryType'] = (isAdmin($context)) ? PROPERTY_EXISTING : PROPERTY_NEW;
    }

    if ($view->items['action'] == 'selectProperty') {
        $view->items['propertyMgmtFeeMethod'] = null;
        unset($view->items['bondPropertyID']);
        unset($_REQUEST['bondPropertyID']);
        unset($view->items['managementFeesID']);
        unset($view->items['FeesID']);
        unset($view->items['showFees']);
    }


    if ($view->items['action'] == 'activate' || $view->items['action'] == 'submit') {
        if ($view->items['propertyInactive'] == 1) {
            if (! $view->items['propertyInactiveDate']) {
                $view->items['propertyInactiveDate'] = TODAY;
            }

            if (dayDiff(date('d/m/Y'), $view->items['propertyInactiveDate']) > 0) {
                $validationErrors[] = "Inactive date can not be after today's date";
            } else {
                dbToggleProperty($view->items['propertyID'], $view->items['propertyInactive'], $view->items['propertyInactiveDate']);
            }
        } else {
            dbToggleProperty($view->items['propertyID'], $view->items['propertyInactive'], '');
        }

    }

    if ($view->items['action'] == 'adjustEntryMethod') {
        $view->items['propertyID'] = null;
    }

    if ($view->items['action'] == 'createProperty') {
        if (isAdmin($context)) {
            $lookup = dbGetProperty($view->items['propertyID']);
        } else {
            $lookup = dbGetPMProperty($view->items['propertyID']);
            if (! $lookup) {
                $lookup = dbGetTempProperty($view->items['propertyID']);
            }
        }

        if (! isValid($view->items['propertyID'], TEXT_CODE)) {
            $validationErrors[] = 'Your property code is not valid';
        }

        if ($lookup) {
            $view->items['propertyID'] = null;
            $validationErrors[] = 'The property code you entered already exist.';
        }

        if (! isUserType(USER_TRACC)) {
            $liveProperty = dbGetPMProperty($view->items['propertyID']);
            if ($liveProperty) {
                $validationErrors[] = 'The property code you entered already exist.';
            }
        }

        if (strlen($view->items['propertyID']) > 10) {
            $validationErrors[] = 'Property code allows 10 characters only';
        }


        if (isAdmin($context) && noErrors($validationErrors)) {
            dbdeleteManagementFees($view->items['propertyID']);
            $managementFeesData['account'] = '';
            $managementFeesData['method'] = 1;
            $managementFeesData['amount'] = 0;
            $managementFeesData['percentage'] = 0;
            $managementFeesData['start'] = '01/01/1900';
            $managementFeesData['end'] = '31/12/2999';
            $managementFeesData['prop'] = $view->items['propertyID'];

            $newID = dbInsertManagementFees($managementFeesData);
        }
    }


    /* LYN BANK PARTITION */
    if ($view->items['propertyID'] && noErrors($validationErrors)) {
        $calendar = dbGetPeriod($view->items['propertyID'], TODAY);
        $ownersBalance = 0;
        $voBalance = 0;
        $drBalance = 0;
        $trialBalanceOpeningBalance = 0;
        $ownersClosingBalance = 0;
        $voClosingBalance = 0;
        $drClosingBalance = 0;
        $trialBalanceClosingBalance = 0;

        if (count($calendar ?? []) > 0) {
            $openingBalanceBreakdown = dbGetOpeningTrialBalanceForPeriodByAccountSubGroup([$view->items['propertyID']], $calendar['period'], $calendar['year']);
            if ($openingBalanceBreakdown) {
                $ownersBalance = (float) $openingBalanceBreakdown['incomeBalance'];
                $voBalance = (float) $openingBalanceBreakdown['voBalance'];
                $drBalance = (float) $openingBalanceBreakdown['drBalance'];
                $trialBalanceOpeningBalance = ($ownersBalance + $voBalance + $drBalance);
            }

            $incomeOwners = 0;
            $incomeVO = 0;
            $incomeDR = 0;
            $incomeTotal = 0;

            $expensesOwners = 0;
            $expensesVO = 0;
            $expensesDR = 0;
            $expensesTotal = 0;

            $expensesDistributionsOwners = 0;
            $expensesDistributionsOwnersTotal = 0;

            $gstOwners = 0;
            $gstOwnersTotal = 0;

            $retainedEarningsOwners = 0;
            $retainedEarningsOwnersTotal = 0;

            $ownersClosingBalanceCounter = 0;
            $voClosingBalanceCounter = 0;
            $drClosingBalanceCounter = 0;

            $openingBalanceDifference = 0;
            $closingBalanceDifference = 0;

            $closingBalanceBreakdown = dbGetDetailedClosingTrialBalanceForPeriodByAccountSubGroup([$view->items['propertyID']], $calendar['period'], $calendar['year']);
            if ($closingBalanceBreakdown) {
                $linkedAccounts = dbGetLinkedAccounts();
                foreach ($closingBalanceBreakdown as $closingBalanceBreakdownDetails) {
                    $closingBalanceAccountGroup = $closingBalanceBreakdownDetails['accountGroup'];
                    switch ($closingBalanceAccountGroup) {
                        case 'INC.OWN':
                            $incomeOwners += $closingBalanceBreakdownDetails['balanceCash'];
                            $ownersClosingBalanceCounter += $closingBalanceBreakdownDetails['balanceCash'];
                            break;
                        case 'INC.OUT':
                            $incomeVO += $closingBalanceBreakdownDetails['balanceCash'];
                            $voClosingBalanceCounter += $closingBalanceBreakdownDetails['balanceCash'];
                            break;
                        case 'INC.REC':
                            $incomeDR += $closingBalanceBreakdownDetails['balanceCash'];
                            $drClosingBalanceCounter += $closingBalanceBreakdownDetails['balanceCash'];
                            break;
                        case 'EXP.OWN':
                            $expensesOwners += $closingBalanceBreakdownDetails['balanceCash'];
                            $ownersClosingBalanceCounter += $closingBalanceBreakdownDetails['balanceCash'];
                            break;
                        case 'EXP.OUT':
                            $expensesVO += $closingBalanceBreakdownDetails['balanceCash'];
                            $voClosingBalanceCounter += $closingBalanceBreakdownDetails['balanceCash'];
                            break;
                        case 'EXP.REC':
                            $expensesDR += $closingBalanceBreakdownDetails['balanceCash'];
                            $drClosingBalanceCounter += $closingBalanceBreakdownDetails['balanceCash'];
                            break;
                        case 'ASS.CUR':
                        case 'ASS.FIX':
                        case 'ASS.NCA':
                        case 'ASS.BAN':
                        case 'EXP.DIS':
                        case 'EQU.RET':
                            if (! in_array($closingBalanceBreakdownDetails['accountCode'], $linkedAccounts)) {
                                $expensesDistributionsOwners += $closingBalanceBreakdownDetails['balanceCash'];
                                $ownersClosingBalanceCounter += $closingBalanceBreakdownDetails['balanceCash'];
                            }

                            break;
                        case 'LIA.CUR':
                        case 'LIA.NCL':

                            if (in_array($closingBalanceBreakdownDetails['accountCode'], $linkedAccounts)) {
                                $gstOwners += $closingBalanceBreakdownDetails['balanceCash'];
                                $ownersClosingBalanceCounter += $closingBalanceBreakdownDetails['balanceCash'];
                            } else {
                                $expensesDistributionsOwners += $closingBalanceBreakdownDetails['balanceCash'];
                                $ownersClosingBalanceCounter += $closingBalanceBreakdownDetails['balanceCash'];
                            }

                            break;
                    }
                }
            }

            $incomeTotal = ($incomeOwners + $incomeVO + $incomeDR);
            $expensesTotal = ($expensesOwners + $expensesVO + $expensesDR);
            $expensesDistributionsOwnersTotal = ($expensesDistributionsOwners);
            $gstOwnersTotal = ($gstOwners);
            $retainedEarningsOwnersTotal = ($retainedEarningsOwners);

            $ownersClosingBalance = ($ownersBalance + $ownersClosingBalanceCounter);
            $voClosingBalance = ($voBalance + $voClosingBalanceCounter);
            $drClosingBalance = ($drBalance + $drClosingBalanceCounter);
            $trialBalanceClosingBalance = ($ownersClosingBalance + $voClosingBalance + $drClosingBalance);


            // ***********get the partition fund per property added by arjay*******
            $trialBalanceClosingBalanceMain = $trialBalanceClosingBalance;
            $prop = $view->items['propertyID'];
            $fundID = dbGetTrialBalanceFund($prop, $calendar['startDate'], $calendar['endDate']);
            $f = dbGetPropertyFundAccounts($view->items['propertyID']);
            foreach ($fundID as $p) {
                $fundName = dbGetFundNameByPartitionAndProperty($view->items['propertyID'], $p['partition']);
                $p['propertyAddress'] = $fundName['fundName'];
                $p['ownerName'] = $fundName['fundName'];
                $p['fundPropertyCode'] = $prop;
                $p['propertyCode'] = $p['partition'];
                $fund[$p['partition']] = $p;
                $ownersClosingBalance -= $p['ownerAmount'];
                $voClosingBalance -= $p['outgoingAmount'];
                $drClosingBalance -= $p['recoverableAmount'];
                $trialBalanceClosingBalanceMain -= ($p['ownerAmount'] + $p['outgoingAmount'] + $p['recoverableAmount']);
            }

            // ***********************end fund partition***************************

            $view->items['ownersIncomeClosing'] = $ownersClosingBalance;
            $view->items['voClosing'] = $voClosingBalance;
            $view->items['directRecoverablesClosing'] = $drClosingBalance;
            $view->items['trialBalanceClosingBalance'] = $trialBalanceClosingBalance;
            $view->items['trialBalanceClosingBalanceMain'] = $trialBalanceClosingBalanceMain;
            $view->items['fund'] = $fund;
        }
    }

    if (isset($view->items['propertyID']) && noErrors($validationErrors)) {
        // ***********************end fund partition***************************

        switch ($view->items['action']) {
            case 'submit':
                $propertyData = [];
                if ($view->items['propertyInspection']) {
                    if ((! isValid($view->items['propertyInspectionAmount'], TEXT_FLOAT, false))) {
                        $validationErrors[] = 'You have not entered a valid inspection amount';
                    }

                    if ((! isValid($view->items['propertyInspectionAccount'], TEXT_KEY, false))) {
                        $validationErrors[] = 'You have not entered a valid inspection account';
                    }

                    if ((! isValid($view->items['propertyInspectionFrequency'], TEXT_KEY, false))) {
                        $validationErrors[] = 'You have not entered a valid inspection frequency';
                    }

                    if ($view->items['propertyInspectionRecoverable']) {
                        if ((! isValid($view->items['propertyInspectionRecoverableAccount'], TEXT_KEY, false))) {
                            $validationErrors[] = 'You have not entered a valid inspection recoverable account';
                        }

                        $propertyData['propertyInspectionRecoverableAccount'] = $view->items['propertyInspectionRecoverableAccount'];
                    } else {
                        $propertyData['propertyInspectionRecoverableAccount'] = '';
                    }

                    $propertyData['propertyInspection'] = $view->items['propertyInspection'];
                    $propertyData['propertyInspectionAmount'] = $view->items['propertyInspectionAmount'];
                    $propertyData['propertyInspectionAccount'] = $view->items['propertyInspectionAccount'];
                    $propertyData['propertyInspectionFrequency'] = $view->items['propertyInspectionFrequency'];
                    $propertyData['propertyInspectionRecoverable'] = $view->items['propertyInspectionRecoverable'];

                } else {
                    $propertyData['propertyInspection'] = '0';
                    $propertyData['propertyInspectionAmount'] = 0;
                    $propertyData['propertyInspectionAccount'] = '';
                    $propertyData['propertyInspectionFrequency'] = 'M';
                    $propertyData['propertyInspectionRecoverable'] = '0';
                    $propertyData['propertyInspectionRecoverableAccount'] = '';
                }

                if ($view->items['rentReviewFee']) {
                    if ((! isValid($view->items['rentReviewFeeAmount'], TEXT_FLOAT, false))) {
                        $validationErrors[] = 'You have not entered a valid rent review amount';
                    }

                    if ((! isValid($view->items['rentReviewFeeExpenseAccount'], TEXT_KEY, false))) {
                        $validationErrors[] = 'You have not entered a valid rent review account';
                    }

                    $propertyData['rentReviewFee'] = $view->items['rentReviewFee'];
                    $propertyData['rentReviewFeeAmount'] = $view->items['rentReviewFeeAmount'];
                    $propertyData['rentReviewFeeExpenseAccount'] = $view->items['rentReviewFeeExpenseAccount'];
                    $propertyData['rentReviewFeeDescription'] = $view->items['rentReviewFeeDescription'];

                } else {
                    $propertyData['rentReviewFee'] = '0';
                    $propertyData['rentReviewFeeAmount'] = 0;
                    $propertyData['rentReviewFeeExpenseAccount'] = '';
                    $propertyData['rentReviewFeeDescription'] = '';
                }

                $propCheck = dbGetProperty($view->items['propertyID']);
                $bankCheck = dbGetPropertyBankAccount($view->items['propertyID']);

                $propertyData['propertyAccountingBasis'] = $view->items['propertyAccountingBasis'];
                $propertyData['propertyLeaseType'] = $view->items['propertyLeaseType'];
                // validate and prepare the data
                if (isValid($view->items['propertyID'], TEXT_CODE, false)) {
                    $propertyData['propertyID'] = $view->items['propertyID'];
                } else {
                    $validationErrors[] = 'Property Codes can only include letters and numbers (A-Z and 0-9), if you are saving an existing property, please use Property Tools to recode the Property Code before saving.';
                }

                if (isValid($view->items['propertyName'], TEXT_LOOSE, false)) {
                    $propertyData['propertyName'] =  $view->items['propertyName'];
                } else {
                    $validationErrors[] = 'Please make sure your property name is filled out and valid';
                }

                if (isValid($view->items['propertyAddress'], TEXT_LOOSE, false)) {
                    $propertyData['propertyAddress'] =  $view->items['propertyAddress'];
                } else {
                    $validationErrors[] = 'Please make sure your property address is filled out and valid';
                }

                if (isValid($view->items['propertyCity'], TEXT_STRICTV2, false)) {
                    $propertyData['propertyCity'] =  $view->items['propertyCity'];
                } else {
                    $validationErrors[] = 'Please make sure your city is filled out and valid';
                }

                if (cdf_isShown('display_state', $view->items['propertyCountry'])) {
                    if (! isValid($view->items['propertyState'], TEXT_LOOSE, false)) {
                        $validationErrors[] = 'State is required.';
                    } else {
                        $propertyData['propertyState'] =  $view->items['propertyState'];
                    }
                } else {
                    $propertyData['propertyState'] = '';
                }

                // POST CODE FORMAT VALIDATION
                $postcodeValidation = cdf_validate_postcode($view->items['propertyPostCode'], $view->items['propertyCountry']);
                if (! $postcodeValidation->valid) {
                    $validationErrors[] = $postcodeValidation->error;
                } else {
                    $propertyData['propertyPostCode'] = $view->items['propertyPostCode'];
                }

                $propertyData['propertyCountry'] =  $view->items['propertyCountry'];

                if (isValid($view->items['propertyOwner'], TEXT_KEY, false)) {
                    $propertyData['propertyOwner'] = $view->items['propertyOwner'];
                } else {
                    $validationErrors[] = 'Please make sure you have selected a principal owner';
                }

                if (isValid($view->items['propertyRemittanceOffice'], TEXT_KEY, true)) {
                    $propertyData['propertyRemittanceOffice'] = $view->items['propertyRemittanceOffice'];
                } else {
                    $validationErrors[] = 'Please make sure you have selected a remittance office';
                }

                if (isValid($view->items['propertyManager'], TEXT_KEY, false)) {
                    $propertyData['propertyManager'] = $view->items['propertyManager'];
                } else {
                    $validationErrors[] = 'Please make sure you have selected a ' . ucwords(strtolower($_SESSION['country_default']['property_manager']));
                }

                if (isValid($view->items['propertyAgent'], TEXT_KEY, true)) {
                    $propertyData['propertyAgent'] = $view->items['propertyAgent'];
                } else {
                    $validationErrors[] = 'Please make sure you have selected a property agent';
                }

                if (isValid($view->items['propertyType'], TEXT_LOOSE, true)) {
                    $propertyData['propertyType'] = $view->items['propertyType'];
                } else {
                    $validationErrors[] = 'Please make sure you have selected a property type';
                }

                if (isValid($view->items['propertyReportType'], TEXT_LOOSE, true)) {
                    $propertyData['propertyReportType'] = $view->items['propertyReportType'];
                } else {
                    $validationErrors[] = 'Please make sure you have selected a report type';
                }

                if (isValid($view->items['propertyGroupType'], TEXT_LOOSE, true)) {
                    $propertyData['propertyGroupType'] = $view->items['propertyGroupType'];
                } else {
                    $validationErrors[] = 'Please make sure you have selected a property group';
                }

                if (isValid($view->items['paymentGroup'], TEXT_LOOSE, true)) {
                    $propertyData['paymentGroup'] = $view->items['paymentGroup'];
                } else {
                    $validationErrors[] = 'Please make sure you have selected a property payment run group';
                }

                if (isValid($view->items['propertyChargeDate'], TEXT_LOOSE, true)) {
                    $propertyData['propertyChargeDate'] = $view->items['propertyChargeDate'];
                } else {
                    $validationErrors[] = 'Please make sure you have selected a charge date';
                }

                if (isAdmin($context)) {
                    if (! dbGetManagementFeeDetail($view->items['propertyID'])) {
                        $validationErrors[] = 'Please make sure you have management fee setup';
                    }
                } else {

                    if ((isValid($view->items['propertyMgmtFeeAccount'], TEXT_KEY, false)) || ($view->items['propertyMgmtFeeMethod'] == 1)) {
                        $propertyData['propertyMgmtFeeAccount'] = $view->items['propertyMgmtFeeAccount'];
                    } else {
                        $validationErrors[] = 'Your management fee account has invalid characters';
                    }

                    if (isValid($view->items['propertyMgmtFeeMethod'], TEXT_INT, false)) {
                        $propertyData['propertyMgmtFeeMethod'] = $view->items['propertyMgmtFeeMethod'];
                    } else {
                        $validationErrors[] = 'Your management fee method has invalid characters';
                    }

                }

                if (isset($view->items['propertyMgmtFeeAmount'])) {
                    if (isValid($view->items['propertyMgmtFeeAmount'], TEXT_FLOATV2, true)) {
                        $propertyData['propertyMgmtFeeAmount'] =  $view->items['propertyMgmtFeeAmount'];
                    } else {
                        $validationErrors[] = 'Your management fee amount must be numeric';
                    }
                }

                if (isset($view->items['propertyMgmtFeePercentage'])) {
                    if (isValid($view->items['propertyMgmtFeePercentage'], TEXT_FLOATV2, true)) {
                        $propertyData['propertyMgmtFeePercentage'] =  $view->items['propertyMgmtFeePercentage'];
                    } else {
                        $validationErrors[] = 'Your management fee percentage must be numeric';
                    }
                }

                if (isValid($view->items['propertyGSTBasis'], TEXT_LOOSE, false)) {
                    $propertyData['propertyGSTBasis'] =  $view->items['propertyGSTBasis'];
                } else {
                    $validationErrors[] = 'Please make sure you have selected a ' . $_SESSION['country_default']['tax_label'] . ' basis';
                }

                if (isValid($view->items['propertyGSTReportPeriod'], TEXT_LOOSE, false)) {
                    $propertyData['propertyGSTReportPeriod'] = $view->items['propertyGSTReportPeriod'];
                } else {
                    $validationErrors[] = 'Please make sure you have selected a ' . $_SESSION['country_default']['tax_label'] . ' report period';
                }

                if (isValid($view->items['propertyComments'], TEXT_LOOSE, true)) {
                    $propertyData['propertyComments'] = $view->items['propertyComments'];
                } else {
                    $validationErrors[] = 'Please check your comments!';
                }

                if (isValid($view->items['propertyRetail'], TEXT_LOOSE, true)) {
                    $propertyData['propertyRetail'] = $view->items['propertyRetail'];
                }

                if ($view->items['withholdOwnerAmount'] > 99999999.99) {
                    $validationErrors[] = 'Maximum amount to withhold from Owner is 99999999.99';
                }

                // ## For propertyAttachToOwnerReport - Default Setting: Attach Supplier Invoices to Owner Report
                if (isValid($view->items['propertyAttachToOwnerReport'], TEXT_LOOSE, true)) {
                    $propertyData['propertyAttachToOwnerReport'] = $view->items['propertyAttachToOwnerReport'];
                }

                if (isValid($view->items['propertyStrata'], TEXT_LOOSE, true)) {
                    $propertyData['propertyStrata'] = $view->items['propertyStrata'];
                }

                if ($propertyData['propertyStrata']) {
                    $view->items['bankPartitioning'] = false;
                }

                $calDetails = dbGetCalendarDetails($view->items['propertyEnd']);
                // if client
                if ($_SESSION['user_type'] == 'C') {
                    $view->items['propertyFirstPeriodFrom'] = '01/01/1900';
                    $propertyData['propertyFirstPeriodFrom'] = $view->items['propertyFirstPeriodFrom'];
                    $propertyData['propertyEnd'] = $calDetails['calCode'];
                    $a_date = $view->items['propertyFirstPeriodFrom'];
                    $a_date = str_replace('/', '-', $a_date);
                    $periodTo =  date('t/m/Y', strtotime($a_date));
                    $propertyData['propertyFirstPeriodTo'] = $periodTo;

                    $temp = explode('/', $view->items['propertyFirstPeriodFrom']);
                    $enteredmonth = $temp[1];
                    $enteredyear = $temp[2];
                    $year = $enteredyear + 1;
                    if ($calDetails['calCode'] == 'F') {
                        $propertyData['propertyYearEnd'] = $enteredmonth >= 07 ? '30/06/' . $year : '31/12/' . $enteredyear;
                    } else {
                        $propertyData['propertyYearEnd'] = '31/12/' . $enteredyear;
                    }

                    if (isValid($view->items['propertyComments'], TEXT_LOOSE, true)) {
                        $propertyData['propertyComments'] = $view->items['propertyComments'];
                    } else {
                        $validationErrors[] = 'Please check your comments!';
                    }

                    $propertyData['propertyUser'] = $_SESSION['user_name'];
                }

                $propertyData['propertyParkingLeased'] = (int) $view->items['propertyParkingLeased'];
                $propertyData['propertyParkingLicensed'] = (int) $view->items['propertyParkingLicensed'];
                $propertyData['propertyParkingCasual'] = (int) $view->items['propertyParkingCasual'];

                $propertyData['propertyInactive'] = (int) $view->items['propertyInactive'];

                if (! $view->items['propertyBankAccount']) {
                    $validationErrors[] = 'Bank account is required.';
                }

                if (! $view->items['propertyEnd']) {
                    $validationErrors[] = 'Property calendar is required.';
                }

                if (isValid($view->items['propertyBankAccount'], TEXT_LOOSE, true)) {
                    $propertyBankAccount = $view->items['propertyBankAccount'];
                } else {
                    $validationErrors[] = 'Your property bank account has invalid characters.';
                }

                $propertyAutoPayOwner = $view->items['propertyAutoPayOwner'] ? 1 : 0;

                //  check if the property exists - if so, update!
                require_once SYSTEMPATH . '/lib/googleClass.php';
                $googleClass = new googleClass();
                $address = $propertyData['propertyAddress'];
                $components['locality'] = $propertyData['propertyCity'];
                $components['administrative_area'] = $propertyData['propertyState'];
                $components['postal_code'] = $propertyData['propertyPostCode'];
                $components['country'] = dbGetCountryName($propertyData['propertyCountry']);
                $params['region'] = $propertyData['propertyCountry'];

                $result = $googleClass->get_coordinate($address, $components, $params);
                if ($result->geometry->location->lat && $result->geometry->location->lng) {
                    $propertyData['propertyLatitude'] = (float) $result->geometry->location->lat;
                    $propertyData['propertyLongitude'] = (float) $result->geometry->location->lng;
                }

                $propertyData['withholdOwnerAmount'] = $view->items['withholdOwnerAmount'] + 0;
                $propertyData['withholdOwnerComment'] = $view->items['withholdOwnerComment'];
                $propertyData['bondPropertyID'] = $view->items['bondPropertyID'];
                $propertyData['propertyUseEFTOnInvoice'] = $view->items['propertyUseEFTOnInvoice'];
                if ($propertyData['propertyUseEFTOnInvoice']) {
                    $get_company_details = dbGetCompanyDetails($view->items['propertyOwner']);
                    if ($get_company_details['companyPayMethod']) {
                        $acc_number = $get_company_details['companyAccountNumber'];
                        if (empty($acc_number) && $get_company_details['companyPayMethod'] == '1' || $get_company_details['companyPayMethod'] != '1') {
                            $validationErrors[] = 'Principal Owner company (' . $view->items['propertyOwner'] . ') has no EFT details set.';
                            $propertyData['propertyUseEFTOnInvoice'] = 0;
                        }
                    }
                }

                $propertyData['propertyCalendarUsed'] = $calDetails['calCode']; // $view->items['propertyEnd'];

                if ($propCheck != null) {
                    if (noErrors($validationErrors)) {
                        $view->items['statusMessage'] = 'The property (<strong>' . $view->items['propertyID'] . '</strong>) has been successfully updated.';

                        dbUpdateProperty($view->items['propertyID'], $propertyData);
                        if ($bankCheck) {
                            dbUpdatePropertyBankAccount($view->items['propertyID'], $propertyBankAccount, $propertyAutoPayOwner, $view->items['bankPartitioning']);
                        } else {
                            dbInsertPropertyBankAccount($view->items['propertyID'], $propertyBankAccount, $propertyAutoPayOwner, $view->items['bankPartitioning']);
                        }

                        if ($_SESSION['user_type'] == 'C') {
                            $lookup = dbGetProperty($view->items['propertyID']);
                            $view->bindAttributesFrom($lookup);
                        }

                        $view->items['saved'] = true; // used to detect if an 'initial' save has taken place - allowing the user to fill out the rest of the form

                    }
                } elseif (noErrors($validationErrors)) {
                    $view->items['statusMessage'] = 'The property (<strong>' . $view->items['propertyID'] . '</strong>) has been successfully created.';
                    dbInsertProperty($view->items['propertyID'], $propertyData);
                    if ($bankCheck) {
                        dbUpdatePropertyBankAccount($view->items['propertyID'], $propertyBankAccount, $propertyAutoPayOwner, $view->items['bankPartitioning']);
                    } else {
                        dbInsertPropertyBankAccount($view->items['propertyID'], $propertyBankAccount, $propertyAutoPayOwner, $view->items['bankPartitioning']);
                    }

                    if ($_SESSION['user_type'] == 'C') {
                        $lookup = dbGetProperty($view->items['propertyID']);
                        $view->bindAttributesFrom($lookup);
                    }

                    $masterCalendarType = $calDetails['calName'];
                    $calendarYear = date('Y');
                    // new way to get calendar year base on the cal types period
                    if ($calDetails['calName'] != 'Calendar') {
                        $calendarPeriod = explode('/', $calDetails['calStart']);
                        $calendarMonth = $calendarPeriod[1];
                        if (date('m') >= $calendarMonth) {
                            $calendarYear += 1;
                        }
                    }

                    $calendarPeriods = 12;
                    $lastYear = dbGetLastCalendarYear($view->items['propertyID']);
                    $currentYear = $calendarYear;
                    $currentPeriod = dbGetLastCalendarPeriod($view->items['propertyID'], $calendarYear);
                    $nextPeriods = dbGetMasterCalendar($calendarYear, $masterCalendarType);
                    if ($nextPeriods == null) {
                        $validationErrors[] = 'There are no periods in the master calendar for the date you have selected';
                    }

                    $getYear = dbGetYearPerProperty($calendarYear, $view->items['propertyID']);
                    if ($getYear['year'] != 0) {
                        $validationErrors[] = 'Selected year already exist';
                    }

                    $masterPeriods = getMasterPeriods($nextPeriods, $currentPeriod, $currentYear, $calendarPeriods);
                    if (noErrors($validationErrors)) {
                        dbInsertCalendarWithCloseFlag($masterPeriods, $view->items['propertyID']);
                    }

                    $view->items['saved'] = true;
                    // used to detect if an 'initial' save has taken place - allowing the user to fill out the rest of the form
                }

                if ($view->items['propertyEntryType'] == 1) {
                    $view->items['saved'] = true;
                }

                if ((! noErrors($validationErrors)) && (isset($view->items['step']))) {
                    $view->items['step']--;
                }

                break;
            case 'deleteProperty':
                $propertyID = $view->items['propertyID'];
                $propertiesAffectedTables = dbCheckPropertyBeforeDelete($propertyID);

                foreach ($propertiesAffectedTables as $field) {
                    if (isset($field['num']) && ($field['num'] > 0)) {
                        $validationErrors[] = 'Delete failed. This will affect ' . $field['tbl'] . ' which  has ' . $field['num'] . ' items. ';
                    }
                }

                if (noErrors($validationErrors)) {
                    dbDeleteProperty($view->items['propertyID']);
                    $view->items['statusMessage'] = 'Property (<strong>' . $propertyID . '</strong>) successfully deleted';
                    $view->items['propertyList'] = dbPropertyList(true, null, 1);
                    $view->items['validationErrors'] = $validationErrors;
                    $view->render();
                    exit();
                }

                break;
            case 'upload':
                $doc =  [];
                $doc['documentTitle'] = $view->items['documentTitle'];
                $doc['documentDescription'] = $view->items['documentDescription'];
                $doc['documentType'] = $view->items['documentType'];
                $doc['createdBy'] = $_SESSION['un'];

                $paths[$view->items['documentType']] = 'PropertyDocuments';
                $uploaddir = "{$pathPrefix}{$clientDirectory}/pdf/" . $paths[$view->items['documentType']] . '/';
                checkDirPath($uploaddir);
                $fileName = time() . '_' . str_replace(' ', '_', basename($_FILES['file']['name']));
                $fileName = str_replace("'", '', $fileName);
                $uploadfile = $uploaddir . $fileName;
                $filePath = "{$clientDirectory}/pdf/" . $paths[$view->items['documentType']] . '/' . $fileName;

                if ($_FILES['file']) {
                    if (move_uploaded_file($_FILES['file']['tmp_name'], $uploadfile)) {
                        $view->items['statusMessage'] = 'File is valid and was successfully uploaded.';
                    } else {
                        $view->items['statusMessage'] =  'File failed to upload!';
                    }

                    $doc['documentType'] = '15';
                    $doc['primaryID'] = $view->items['propertyID'];
                    $doc['secondaryID'] = null;
                    $doc['filename'] = $filePath;
                    dbAddDocument($doc);
                }

                break;
        }


        // LOGIC: load a new fragment based on the general HTML template
        $userControl = new UserControl(userViews(), '/properties/general.html');
        $userControl->items = &$view->items;

        $lookup = dbGetProperty($view->items['propertyID']);


        // if a property exists, bind it's details  -otherwise check if it is a 'first time entry' for a new
        // property code - in which case clear any previously submitted data

        if (($lookup != null) && ($userControl->items['action'] != 'submit') && ($userControl->items['action'] != 'finalise') && $userControl->items['action'] != 'selectManagementFee' && $userControl->items['action'] != 'isRetailStrata') {
            // && ($userControl->items['pid'] != $userControl->items['propertyID'])) {
            if ($userControl->items['action'] == 'stateList' || $userControl->items['action'] == 'changeCountry' || $userControl->items['action'] == 'changeState') {
                $userControl->items['propertyState'] = $view->items['propertyState'];
                $userControl->items['propertyCountry'] = $view->items['propertyCountry'];
            } else {
                $userControl->bindAttributesFrom($lookup);
                $userControl->items['stateList'] = dbGetStates($lookup['propertyCountry']);
                $userControl->items['saved'] = true;
            }

            if ($propertyMgmtFeeMethod) {
                $view->items['propertyMgmtFeeMethod'] = $propertyMgmtFeeMethod;
            }
        } elseif (($userControl->items['action'] != 'submit') && ($userControl->items['action'] != 'finalise') && $userControl->items['action'] != 'selectManagementFee' && $userControl->items['action'] != 'isRetailStrata') {
            $userControl->items['propertyID'] = $_POST['propertyID'];
            $userControl->items['propertyMgmtFeeMethod'] = (empty($propertyMgmtFeeMethod)) ? 1 : $propertyMgmtFeeMethod;
            $userControl->items['propertyGSTBasis'] = 'C';
            $userControl->items['propertyGSTReportPeriod'] = 'M';
            $userControl->items['propertyParkingLeased'] = 0;
            $userControl->items['propertyParkingLicensed'] = 0;
            $userControl->items['propertyParkingCasual'] = 0;
            $thisMonth = date('01/m/Y', time());
            $currentMonth = date('m');
            $nextMonth = oneMonthAfter($thisMonth);
            $userControl->items['propertyChargeDate'] = $nextMonth;
            $userControl->items['propertyFirstPeriodFrom'] = $thisMonth;
            $userControl->items['propertyFirstPeriodTo'] = oneDayBefore($nextMonth);
            $userControl->items['propertyYearEnd'] = ($currentMonth < 6) ? '30/06/' . (date('Y', time())) : '30/06/' . (date('Y', time()) + 1);
        }

        if (($_SESSION['user_type'] == 'C') && (! isset($userControl->items['step']))) {
            $userControl->items['step'] = 1;
        }


        $userControl->items['pid'] = $userControl->items['propertyID'];

        $userControl->items['taskNameList'] = $taskDescription;
        $userControl->items['taskClassList'] = $taskClass;

        if (($lookup != null) && $userControl->items['action'] == 'isRetailStrata') {
            $userControl->items['saved'] = true;
        }

        if ((isset($view->items['propertyStrata'])) && ($view->items['propertyStrata'])) {
            $view->items['allowBankPartitionFlag'] = false;
        } else {
            $view->items['allowBankPartitionFlag'] = true;
        }

        if (($lookup != null) && $userControl->items['action'] == 'selectManagementFee') {
            $userControl->items['saved'] = true;
        }

        // ---- STEP 1
        if (isAdmin($context) || ($view->items['step'] == 1)) {
            $userControl->items['propertyAgentList'] = dbGetAgents();
            $userControl->items['propertyManagerList'] = dbGetParams('PORTMGR');
            $userControl->items['propertyOwnerList'] = (isUserType(USER_TRACC)) ? dbGetOwners() : dbGetAllOwners();

            $userControl->items['propertyTypeList'] = dbGetParams('PROPERTY');
            $userControl->items['propertyBankAccountList'] = dbGetBankAccounts();

            $userControl->items['ownerGSTstatus'] = dbGetOwnerGST($view->items['propertyOwner']);
            if ($userControl->items['ownerGSTstatus'] == '') {
                $userControl->items['ownerGSTstatus'] = 'The ' . $_SESSION['country_default']['tax_label'] . ' status of the owner will be shown once the new owner is saved';
            }

            $bankAccount = dbGetPropertyBankAccount($view->items['propertyID']);
            if ($bankAccount) {
                $userControl->items['propertyBankAccount'] = $bankAccount['bankAccountID'];
                $bankName = dbGetBankAccountsName($bankAccount['bankAccountID']);
                $userControl->items['propertyBankAccountName'] = $bankName['bankAccountName'];
                $userControl->items['propertyAutoPayOwner'] = $bankAccount['bankAccountPayOwner'] ? 1 : 0;
                $userControl->items['propertyRentOnly'] = $bankAccount['bankAccountRentOnly'];
                if ($userControl->items['propertyRentOnly']) {
                    $userControl->items['propertyRentOnlyYesNo'] = 1;
                }

                $userControl->items['bankPartitioning'] = $bankAccount['bankPartitioning'];
            } elseif (! $view->items['propertyAutoPayOwner']) {
                $view->items['propertyAutoPayOwner'] = 1;
            }

            $userControl->items['propertyMgmtFeeMethodList'] = ['1' => 'Nil', '2' => 'Fixed', '3' => 'Percentage of Total Receipts', '4' => 'Percentage of account ranges', '5' => 'Percentage of Total Receipts (Lease)'];
            $userControl->items['propertyFeeTypeList'] = ['1' => 'Property Inspection Fee', '2' => 'Rent Review Fee'];
            $userControl->items['rentOnlyOption'] =
            [
                1 => 'Exclude GST',
                2 => 'Include GST',
            ];
            $userControl->items['propertyReportGSTBasisList'] = ['C' => 'Cash', 'A' => 'Accrual'];
            $userControl->items['propertyReportGSTPeriodList'] = ['M' => 'Monthly', 'Q' => 'Quarterly'];
            $masterCalendarList = dbGetMasterCalendars();
            if (empty($userControl->items['propertyEnd'])) {
                // set default value to financial if exist in calendar types
                foreach ($masterCalendarList as $masterCalendar) {
                    if ($masterCalendar['description'] == 'Financial') {
                        $userControl->items['propertyEnd'] = $masterCalendar['masterCode'];
                    }
                }
            }

            $userControl->items['YearEnd'] = mapParameters($masterCalendarList, 'masterCode', 'description');
            $userControl->items['propertyRemittanceOfficeList'] = dbGetOffices();
            $userControl->items['propertyGroupTypeList'] = mapParameters(dbGetParams('PROPGROUP'));
            $userControl->items['propertyPayGroupList'] = mapParameters(dbGetParams('PAYGROUP'));
            $reportTypeList =  dbGetReports(1);
            $reportTypeList = mapParameters($reportTypeList, 'reportID', 'reportName');

            foreach ($reportTypeList as $key => $value) {
                if (stripos($value, 'strata') !== false) {
                    $reportTypeList[$key] = ucwords(str_replace('strata', $_SESSION['country_default']['strata'], strtolower($value)));
                }
            }

            $userControl->items['propertyReportTypeList'] = $reportTypeList;

            $userControl->items['propertyMgmtFeeAccountList'] = dbGetFeeAccounts(EXPENDITURE);
            $view->items['accountGroupList'] =
            [
                'EXPOWN' => 'Owner Expenditure',
                'EXPVO' => ucwords(strtolower($_SESSION['country_default']['variable_outgoings'])) . ' Expenditure',
                'EXPDR' => 'Directly Recoverable Expenditure',
                'EXPENDITURE' => 'EXPENDITURE',
            ];
            $view->items['accountGroupList2'] =
            [
                'INCOWN' => 'Owners Income',
                'INCDR' => 'Directly Recoverable Income',
                'INCVO' => ucwords(strtolower($_SESSION['country_default']['variable_outgoings'])) . ' Income',
            ];

            $view->items['bondPropertyList'] = dbBondPropertyList();

            if (isAdmin($context)) {
                $userControl->items['UserControl::managementFeesPeriod'] = fetchCommand('manageFee');
            }

            $userControl->items['UserControl::ManagementAgreement'] = fetchCommand('managementAgreement');
            $userControl->items['UserControl::Image'] = fetchCommand('propertyImage');

            switch ($userControl->items['propertyMgmtFeeMethod']) {
                case 4:        $userControl->items['UserControl::ManagementFees'] = fetchCommand('accountPercentages');
                    break;
                case 3:
                case 5:	      $mgmtControl = new UserControl(userViews(), '/properties/percentageTotal.html');
                    $mgmtControl->items = &$userControl->items;
                    $userControl->items['UserControl::ManagementFees'] = $mgmtControl->toString();
                    break;
                case 2:        $mgmtControl = new UserControl(userViews(), '/properties/fixedAmount.html');
                    $mgmtControl->items = &$userControl->items;
                    $userControl->items['UserControl::ManagementFees'] = $mgmtControl->toString();
                    break;
                case 1:        $userControl->items['UserControl::ManagementFees'] = '';
                    break;
            }

            if ($userControl->items['propertyAgent'] == '') {
                $userControl->items['propertyAgent'] = dbGetParam('PROPDEF', 'AGENT');
            }

            if ($userControl->items['propertyCountry'] == '') {
                $userControl->items['propertyCountry'] = dbGetParam('PROPDEF', 'COUNTRY');
            }

            if ($userControl->items['propertyRemittanceOffice'] == '') {
                $userControl->items['propertyRemittanceOffice'] = dbGetParam('PROPDEF', 'OFFICE');
            }

            if ($userControl->items['propertyMgmtFeeAccount'] == '') {
                $userControl->items['propertyMgmtFeeAccount'] = dbGetParam('PROPDEF', 'MGMTACCT');
            }

            $userControl->items['UserControl::FundPartition'] = fetchCommand('fundPartition');
            $userControl->items['UserControl::BondProperty'] = fetchCommand('bondProperty');

            if (! isset($userControl->items['propertyInspection']) || (isset($userControl->items['propertyInspection']) && (int) $userControl->items['propertyInspection'] == 0)) {
                $userControl->items['propertyInspectionAccount'] = dbGetParam('PROPDEF', 'INSPECACC');
                $userControl->items['propertyInspectionRecoverableAccount'] = dbGetParam('PROPDEF', 'INSPECACCR');
                $userControl->items['propertyInspectionAmount'] = dbGetParam('PROPDEF', 'INSPECAMT');
            }

            if (! isset($userControl->items['rentReviewFee']) || (isset($userControl->items['rentReviewFee']) && (int) $userControl->items['rentReviewFee'] == 0)) {
                $userControl->items['rentReviewFeeExpenseAccount'] = dbGetParam('PROPDEF', 'RENTREVACC');
                $userControl->items['rentReviewFeeAmount'] = dbGetParam('PROPDEF', 'RENTREVAMT');
            }

            $view->items['managementFeeList'] = (isAdmin($context)) ? dbGetManagementFeeList($userControl->items['propertyID']) : [];
            if ($view->items['action'] == 'newManagementFees') {
                $view->items['showFees'] = 1;
                $view->items['FeesID'] = 0;
            }

            if ($view->items['action'] == 'selectManagementFeePeriod') {
                $view->items['showFees'] = 0;
                $view->items['FeesID'] = $view->items['managementFeesID'];
            }

            if (! isset($userControl->items['propertyInspection']) || (isset($userControl->items['propertyInspection']) && (int) $userControl->items['propertyInspection'] == 0)) {
                $userControl->items['propertyInspectionAccount'] = dbGetParam('PROPDEF', 'INSPECACC');
                $userControl->items['propertyInspectionRecoverableAccount'] = dbGetParam('PROPDEF', 'INSPECACCR');
                $userControl->items['propertyInspectionAmount'] = dbGetParam('PROPDEF', 'INSPECAMT');
            }

            if (! isset($userControl->items['rentReviewFee']) || (isset($userControl->items['rentReviewFee']) && (int) $userControl->items['rentReviewFee'] == 0)) {
                $userControl->items['rentReviewFeeExpenseAccount'] = dbGetParam('PROPDEF', 'RENTREVACC');
                $userControl->items['rentReviewFeeAmount'] = dbGetParam('PROPDEF', 'RENTREVAMT');
            }

        }

        if ($_SESSION['user_type'] == 'C') {
            $feedback = dbGetRejectedComment($userControl->items['propertyID']);
            $userControl->items['rejectedComment'] = $feedback['rejectedComment'];
            $userControl->items['propStat'] = $feedback['propertyStatus'];
        }

        // ---- STEP 2

        if (isAdmin($context) || ($view->items['step'] == 2)) {

            $userControl->items['UserControl::OwnerShares'] = fetchCommand('ownerShares');
            $userControl->items['UserControl::Floors'] = fetchCommand('floors');
            $userControl->items['UserControl::Units'] = fetchCommand('units');
            $userControl->items['UserControl::Keys'] = fetchCommand('keys');
            $userControl->items['UserControl::PropertyRecoverableSplit'] = fetchCommand('PropertyRecoverableSplit');
        }

        // ---- STEP 3

        if (isAdmin($context) || ($view->items['step'] == 3)) {

            $userControl->items['UserControl::Sundry'] = fetchCommand('sundryCharges');
            $userControl->items['UserControl::Notes'] = fetchCommand('propertyNotes');
            $userControl->items['UserControl::Diary'] = fetchCommand('diary');

            if (! isset($view->items['masterCalendarType'])) {
                $view->items['masterCalendarType'] = $lookup['propertyCalendarUsed'];
            }

            $userControl->items['UserControl::Calendar'] = fetchCommand('calendar');
            $userControl->items['UserControl::OpeningBalances'] = fetchCommand('openingBalances');
            $userControl->items['UserControl::OwnerShareReport'] = fetchCommand('ownerShareReport');
            $userControl->items['UserControl::Document'] = fetchCommand('propertyDocument');
            $userControl->items['UserControl::Image'] = fetchCommand('propertyImage');
            $userControl->items['UserControl::Contact'] = fetchCommand('propertyContact');

            $userControl->items['UserControl::PropertyInspection'] = fetchCommand('propertyInspection');
            $userControl->items['UserControl::PropertyInsurance'] = fetchCommand('propertyInsurance');
            $userControl->items['UserControl::PropertyGuarantee'] = fetchCommand('propertyGuarantee');

        }

        if ($view->items['propertyEntryType'] == 1) {
            //        echo $view->items['propertyEntryType'] . "-".PROPERTY_EXISTING;
            $userControl->items['letterHistoryPage'] = 'index.php?module=configuration&command=letterHistory&propertyCode=' . $view->items['propertyID'] . '&viewOnly=1';
        }

        // -- RENDER IT
        // LOGIC: add the HTML fragment as a parameter of the master template
        if (isAdmin($context) || ($view->items['step'] > 0)) {
            $view->items['UserControl::General'] = $userControl->toString();
        }

    }


    $view->items['propertyList'] = (isAdmin($context)) ? dbPropertyList(false, null, 1) : dbTempPropertyList();
    $view->items['propertyGroupedList'] = dbPropertyGroupList('', ucwords(strtolower($_SESSION['country_default']['property_manager'])), true, 1);

    $view->items['validationErrors'] = $validationErrors;
    $view->items['warningList'] = $warnings;

    $view->render();
}

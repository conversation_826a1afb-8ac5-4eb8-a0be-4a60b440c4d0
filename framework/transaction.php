<?php
/*instructions
specify the old and new codes at the bottom of this file
specify the database on line 14
login the the system and change the hrl to below - then enter
https://client.cirrus8.com.au/framework/dbChangeSupplierCode.php

*/
function chunk($code, $index) {
        $number = substr($code, 0, -1);
        $letter = substr($code, -1);
        return ($number+$index)  . $letter;
}

function repairBatch($start = -1000000)
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase('Allegro');

/*
SELECT    TOP 1 COUNT(hash) as ex, hash
FROM         dbo.ap_transaction
GROUP BY hash
ORDER BY ex DESC
*/

/*AP non unique
2000548 / 2000549
*/


/*
ALTER TABLE ap_transaction ADD rid int IDENTITY(1,1);
ALTER TABLE ar_transaction ADD rid int IDENTITY(1,1);
ALTER TABLE pmxd_ar_alloc ADD rid int IDENTITY(1,1);
ALTER TABLE pmxc_ap_alloc ADD rid int IDENTITY(1,1);
ALTER TABLE pmuc_unall_csh ADD rid int IDENTITY(1,1);
ALTER TABLE pmrc_receipt ADD rid int IDENTITY(1,1);
ALTER TABLE ap_batch ADD rid int IDENTITY(1,1);
ALTER TABLE ar_batch ADD rid int IDENTITY(1,1);
*/

/* THIS CODE GENERATES THE HASH INDEXES - WHICH ARE UNIQUE FOR EVERY ROW IN THE DB

   $o = $dbh->executeSet("SELECT * FROM ap_transaction");
    foreach ($o as $t) {
		$hash = md5($t['creditor_code'] . $t['trans_date'] . $t['ref_1'] . $t['ref_2'] . $t['ref_3'] . $t['trans_amt'] . $t['description'] . $t['aptr_create_time']);
        $dbh->executeNonQuery("UPDATE ap_transaction SET hash = '{$hash}' WHERE rid = '{$t[rid]}'");
    }


   $o = $dbh->executeSet("SELECT * FROM ar_transaction");
    foreach ($o as $t) {
		$hash = md5($t['debtor_code'] . $t['trans_date'] . $t['ref_1'] . $t['batch_line_nr'] . $t['ref_2'] . $t['ref_3'] . $t['ref_4'] . $t['trans_amt'] . $t['description'] . $t['artr_create_time']);
        $dbh->executeNonQuery("UPDATE ar_transaction SET hash = '{$hash}' WHERE rid = '{$t[rid]}'");
    }


   $o = $dbh->executeSet("SELECT * FROM pmxc_ap_alloc");
    foreach ($o as $t) {
		$hash = md5($t['pmxc_alloc_nr'] . $t['pmxc_alloc_amt'] . $t['pmxc_f_type'] . $t['pmxc_f_line'] . $t['pmxc_mod_time'] . $t['pmxc_t_line'] . $t['pmxc_create_date'] . $t['pmxc_t_type'] . $t['pmxc_prop'] . $t['pmxc_acc'] . $t['pmxc_s_creditor']);
        $dbh->executeNonQuery("UPDATE pmxc_ap_alloc SET hash = '{$hash}' WHERE rid = '{$t[rid]}'");
    }


   $o = $dbh->executeSet("SELECT * FROM pmxd_ar_alloc");
    foreach ($o as $t) {
		$hash = md5($t['pmxd_alloc_nr'] . $t['pmxd_create_sess'] . $t['pmxd_alloc_amt'] . $t['pmxd_f_type'] . $t['pmxd_alloc_dt'] . $t['pmxd_t_line'] . $t['pmxd_t_type'] . $t['pmdc_prop'] . $t['pmxd_acc'] . $t['pmxd_s_debtor']);
        $dbh->executeNonQuery("UPDATE pmxd_ar_alloc SET hash = '{$hash}' WHERE rid = '{$t[rid]}'");
    }


   $o = $dbh->executeSet("SELECT * FROM pmuc_unall_csh");
    foreach ($o as $t) {
		$hash = md5($t['pmuc_line'] . $t['pmuc_ref_1'] . $t['pmuc_serial'] . $t['pmuc_lease'] . $t['pmuc_acc'] . $t['pmuc_amt'] . $t['pmuc_mod_date'] . $t['pmuc_create_sess']);
        $dbh->executeNonQuery("UPDATE pmuc_unall_csh SET hash = '{$hash}' WHERE rid = '{$t[rid]}'");
    }


   $o = $dbh->executeSet("SELECT * FROM pmrc_receipt");
    foreach ($o as $t) {
		$hash = md5($t['pmrc_receipt_no']. $t['pmrc_print_dt']);
        $dbh->executeNonQuery("UPDATE pmrc_receipt SET hash = '{$hash}' WHERE rid = '{$t[rid]}'");
    }

*/


/*
THIS CODE FETCHES THE BATCH NUMBERS ON THE REFERENCE DATABASE AND APPLIES THEM TO THE LIVE DATABASE WHERE THE HASH INDEXES MATCH


//-- fetch all rows from reference DB
$dbh->selectDatabase('Allegro');
$o = $dbh->executeSet("SELECT batch_nr, hash, rid FROM ap_transaction");
//-- update rows in live DB
$dbh->selectDatabase('Diploma');
foreach ($o as $i) {
	$dbh->executeNonQuery("UPDATE ap_transaction SET batch_nr = '{$i[batch_nr]}' WHERE hash = '{$i['hash']}'");
}


//-- fetch all rows from reference DB
$dbh->selectDatabase('Allegro');
$o = $dbh->executeSet("SELECT batch_nr, hash, rid FROM ar_transaction");
//-- update rows in live DB
$dbh->selectDatabase('Diploma');
foreach ($o as $i) {
	$dbh->executeNonQuery("UPDATE ar_transaction SET batch_nr = '{$i[batch_nr]}' WHERE hash = '{$i['hash']}'");
}


//-- fetch all rows from reference DB
$dbh->selectDatabase('Allegro');
$o = $dbh->executeSet("SELECT pmxd_f_batch, pmxd_t_batch, hash, rid FROM pmxd_ar_alloc");
//-- update rows in live DB
$dbh->selectDatabase('Diploma');
foreach ($o as $i) {
	$dbh->executeNonQuery("UPDATE pmxd_ar_alloc SET pmxd_f_batch = '{$i[pmxd_f_batch]}', pmxd_t_batch = '{$i[pmxd_t_batch]}' WHERE hash = '{$i['hash']}'");
}

//-- fetch all rows from reference DB
$dbh->selectDatabase('Allegro');
$o = $dbh->executeSet("SELECT pmxc_f_batch, pmxc_t_batch, hash, rid FROM pmxc_ap_alloc");
//-- update rows in live DB
$dbh->selectDatabase('Diploma');
foreach ($o as $i) {
	$dbh->executeNonQuery("UPDATE pmxc_ap_alloc SET pmxc_f_batch = '{$i[pmxc_f_batch]}', pmxc_t_batch = '{$i[pmxc_t_batch]}' WHERE hash = '{$i['hash']}'");
}

*/

//-- fetch all rows from reference DB
$dbh->selectDatabase('Allegro');
$o = $dbh->executeSet("SELECT pmuc_batch, hash, rid FROM pmuc_unall_csh");
//-- update rows in live DB
$dbh->selectDatabase('Diploma');
foreach ($o as $i) {
	if ($i['hash']) $dbh->executeNonQuery("UPDATE pmuc_unall_csh SET pmuc_batch = '{$i[pmuc_batch]}' WHERE hash = '{$i['hash']}'");
}

/*
//-- fetch all rows from reference DB
$dbh->selectDatabase('Allegro');
$o = $dbh->executeSet("SELECT pmrc_batch, hash, rid FROM pmrc_receipt");
//-- update rows in live DB
$dbh->selectDatabase('Diploma');
foreach ($o as $i) {
	$dbh->executeNonQuery("UPDATE pmrc_receipt SET pmrc_batch = '{$i[pmrc_batch]}' WHERE hash = '{$i['hash']}'");
}




*/
}



  //-- include and invoke the session handler
  include 'lib/classes/Session.php';
  $sess = new Session();


  //-- if ob_get_clean doesnt exist, define the function
  if (!function_exists("ob_get_clean")) {
    function ob_get_clean() {
        $ob_contents = ob_get_contents();
        ob_end_clean();
        return $ob_contents;
    }
}


  include('config.php');



  //-- invoke the database handler and create a new connection to the system database
  $dbh = new MSSQL(SYSTEMDSN);


repairBatch(1000000);







?>
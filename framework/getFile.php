<?php
//-- include and invoke the session handler
include 'lib/classes/Session.php';
$sess = new Session ();
//$dispatch = array();

//session_start();
ob_start();

if (!function_exists("ob_get_clean")) {
    function ob_get_clean() {
        $ob_contents = ob_get_contents();
        ob_end_clean();
        return $ob_contents;
    }
}

include('config.php');


date_default_timezone_set ('Australia/Perth');
define ('TODAY', date ('d/m/Y'));

$referrer = 'https://' . $_SERVER['HTTP_HOST'];


if ($referrer == HTTPHOST)
{
    $sess->set ('referrer', HTTPHOST . $_SERVER['SCRIPT_NAME'] . '?' . $_SERVER['QUERY_STRING']);
    // echo HTTPHOST . $_SERVER['SCRIPT_NAME'] . '?' .$_SERVER['QUERY_STRING'];
}
else $sess->drop ('referrer');

$sess->drop ('queries');

$clientDB = $sess->get ('currentDB');
$clientDSN = COREDSN . $sess->get ('currentDB');
// define ('CLIENTDB', $sess->get ('currentDB'));
// define ('CLIENTDSN', COREDSN . $sess->get ('currentDB'));

//-- invoke the database handler and create a new connection to the system database

$dbh = new MSSQL (SYSTEMDSN);

if(SSO_ENABLED){
    if ($_GET['command'] == 'noAccess'){
        executeCommand ('noAccess');
        exit();
    }
    else if ($_GET['command'] == 'home' && $_GET['action'] == 'login' && $_GET['type'] == 'baseauth' && isset($_GET['token'])){
        executeCommand ('baseauth');
    }
    // else if ($_GET['command'] == 'login' && isset($_GET['token'])){
    // 	executeCommand ('baseauth');
    // }
    else{
        if (!$sess->get ('userID')){
            header('Location: '.SSO_LOGIN);
        }
        else{
            if($sess->get ('userID')){
                $userID = $sess->get ('userID');
                //$verifyLogin = dbGetLoginStats ($userID);
                if ($userID) { // just check if logged in regardless of timestamp
                    // download content --- start

                    global $pathPrefix;

                    if ($_GET['fileID']) {
                        $filename = $_GET['fileID'];
                        $filename = base64_decode(urldecode($filename)); //U
                        $filePath =  $filename;

                        $hasAccess = false;

                        $dbNameAccess = mapParameters(dbGetDatabasePermissions($userID),'databaseID','description');

                        //$userDBDirectories = array('LJHookerCommercialToowoomba', 'OfficeofTownshipLeasingNT'); //used for testing

                        if (substr($filename,0,15) == 'administration/')
                            $hasAccess = true;
                        else
                        {
                            foreach ($dbNameAccess as $dbName) {
                                $dbNameSlash = str_replace(' ','', $dbName).'/';

                                if (strpos($filename, $dbNameSlash) !== false) {
                                    $hasAccess = true;
                                    break;
                                }

                            }
                        }

//                        pre_print_r($hasAccess);
//                        echo $filename; die();

                        if (file_exists($filePath) AND $hasAccess) {

                            $extension = substr($filePath, strrpos($filePath,'.')+1);

                            // required for IE, otherwise Content-disposition is ignored
                            ini_set('zlib.output_compression','Off');

                            switch($extension)
                            {
                                case "pdf": $ctype="application/pdf"; break;
                                case "exe": $ctype="application/octet-stream"; break;
                                case "zip": $ctype="application/zip"; break;
                                case "doc": $ctype="application/msword"; break;
                                case "xls": $ctype="application/vnd.ms-excel"; break;
                                case "xlsx": $ctype="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"; break;
                                case "ppt": $ctype="application/vnd.ms-powerpoint"; break;
                                case "gif": $ctype="image/gif"; break;
                                case "png": $ctype="image/png"; break;
                                case "jpeg": $ctype="image/jpeg"; break;
                                case "jpg": $ctype="image/jpg"; break;
                                case "aba" : $ctype="text/plain"; break;
                                case "txn" : $ctype="application/mbldeft-transaction"; break;
                                case "brf" : $ctype="application/mbldeft-bpay"; break;
                                case "pay" : $ctype="application/mbldeft-payment"; break;
                                case "acc" : $ctype="application/mbldeft-acc"; break;
                                default: $ctype="application/force-download";
                            }


                            header("Content-Type: $ctype");
                            header("Content-Disposition: inline; filename=filename.pdf");
                            // echo $filePath;
                            @readfile($filePath);
                            exit();
                        } else {

                            exit();
                            $host  = $_SERVER['HTTP_HOST'];
                            $uri   = rtrim(dirname($_SERVER['PHP_SELF']), '/\\');
                            $extra = 'index.php';
                            header("Location: http://$host$uri/$extra?command=error");
                            exit();

                            echo 'File cannot be found - please contact Cirrus8 Support';
                        }
                    }
                    // -----------------------
                }
                else {
                    if (!SSO_ENABLED && $userID){
                        $sess->destroy ();
                        header("Location: ".logout_url);
                        exit ();
                    }
                }
            }
        }
    }
}
else{ //SSO_ENABLED should be true even on local
    executeCommand ('login');
    exit ();
}

//$host  = $_SERVER['HTTP_HOST'];
//$uri   = rtrim(dirname($_SERVER['PHP_SELF']), '/\\');
//$extra = 'index.php';
//
//
//header("Location: http://$host$uri/$extra?command=error");


?>
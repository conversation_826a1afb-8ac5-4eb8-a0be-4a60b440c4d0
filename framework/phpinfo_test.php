<?php
echo "<h1>PHP Configuration Test</h1>";

// Check if session extension is loaded
echo "<h2>Session Extension Check:</h2>";
if (extension_loaded('session')) {
    echo "<p style='color: green;'>✅ Session extension is loaded</p>";
} else {
    echo "<p style='color: red;'>❌ Session extension is NOT loaded</p>";
}

// Check session-related functions
echo "<h2>Session Functions Available:</h2>";
$session_functions = ['session_start', 'session_id', 'session_status', 'session_destroy'];
foreach ($session_functions as $func) {
    if (function_exists($func)) {
        echo "<p style='color: green;'>✅ $func() is available</p>";
    } else {
        echo "<p style='color: red;'>❌ $func() is NOT available</p>";
    }
}

// Show current session configuration
echo "<h2>Session Configuration:</h2>";
$session_configs = [
    'session.auto_start',
    'session.use_cookies', 
    'session.use_only_cookies',
    'session.save_path',
    'session.cookie_httponly',
    'session.cookie_secure'
];

foreach ($session_configs as $config) {
    echo "<p>$config: " . ini_get($config) . "</p>";
}

// Show full phpinfo
echo "<h2>Full PHP Info:</h2>";
phpinfo();
?>

<!doctype html>
<html>
<head>
	<title>User Registration - <?=$var['Master::Section']?></title>
	<link rel="icon" type="image/vnd.microsoft.icon" href="favicon.ico" />
		<link rel="stylesheet" href="<?=ASSET_DOMAIN?>assets/css/core.css?1473812725" type="text/css" />
		<link rel="stylesheet" type="text/css" href="<?=ASSET_DOMAIN?>assets/css/lightview.css?1473812725" />

		<link type="text/css" rel="stylesheet" media="all" href="<?=ASSET_DOMAIN?>assets/css/chat.css?1473812725" />
		<link type="text/css" rel="stylesheet" media="all" href="<?=ASSET_DOMAIN?>assets/css/screen.css?1473812725" />
		<link type="text/css" rel="stylesheet" media="all" href="<?=ASSET_DOMAIN?>assets/css/fg.menu.css?1473812725" />

		<link type="text/css" href="<?=ASSET_DOMAIN?>assets/css/custom-theme/jquery-ui.css?1473812725" rel="stylesheet" />

		<script type="text/javascript" src="<?=ASSET_DOMAIN?>assets/js/jquery-min.js?1473812725"></script>
		<script type="text/javascript" src="<?=ASSET_DOMAIN?>assets/js/jquery-migrate.js?1473812725"></script>

		<script type="text/javascript" src="<?=ASSET_DOMAIN?>assets/js/FusionCharts.js?1473812725"></script>
		<script type="text/javascript" src="<?=ASSET_DOMAIN?>assets/js/FusionCharts.jqueryplugin.js?1473812725"></script>
<!--		<script type="text/javascript" src="<?=ASSET_DOMAIN?>assets/js/chat.js?1473812725"></script>-->
		<script type="text/javascript" src="<?=ASSET_DOMAIN?>assets/js/jquery-ui-min.js?1473812725"></script>
		<script type="text/javascript" src="<?=ASSET_DOMAIN?>assets/js/fg.menu.js?1473812725"></script>

		<script type="text/javascript" src="<?=ASSET_DOMAIN?>assets/tiny_mce/tiny_mce.js?1473812725"></script>
		<!-- <script type="text/javascript" src="<?=ASSET_DOMAIN?>assets/editor/scripts/innovaeditor.js"></script> -->
		<script type="text/javascript" src="<?=ASSET_DOMAIN?>assets/js/validation.js?1473812725"></script>

		<!-- fusion was here -->

		<script type="text/javascript" src="<?=ASSET_DOMAIN?>assets/js/addon/periodicalupdater.js?1473812725"></script>
		<script type="text/javascript" src="assets/js/date.js?1473812725"></script> <!-- ui above need to run date.js .datepicker-->

		
		<script type="text/javascript" src="assets/js/addon/jquery.serializeall.js?1473812725"></script>

		<script type="text/javascript" src="assets/js/core.js?1473812725"></script>
		<script type="text/javascript" src="assets/js/custom.js?1473812725"></script>

		<script type="text/javascript" src="assets/js/loader.js?1473812725"></script>
		<script type="text/javascript" src="assets/js/dropdown.js?1473812725"></script>
		<script type="text/javascript" src="assets/js/smart_date.js?1473812725"></script>
		<style>
		h2 {
		display: block;
		font-size: 2em;
		margin-top: 0.83em;
		margin-bottom: 0.83em;
		margin-left: 0;
		margin-right: 0;
		}
		</style>
</head>

<?php
// md5('JLL Programmed Group'.'cirrus8');
session_start ();

require_once ('config.php');

$dbh = new MSSQL (SYSTEMDSN);

if ($_GET['sname'] != '')
{
	echo "<center>
	<br/><br/>
	<h2>User ".$_GET['sname']." has been successfully registered and is now waiting to be approved. 
	<br><br>Once approved, you will be notified by email using the email address you provided.
	</h2><br></center>";
	die;
	
}

if (isset($_POST['submit']))
{
	// check and post data
	
	$first_name = $_POST['firstName'];
	$last_name = $_POST['lastName'];
	$email = $_POST['email'];
	$password = $_POST['password'];
	$confirm_password = $_POST['confirmPassword'];
	$company_name = $_POST['company_name'];
	$divisions = $_POST['divisions'];
	$business_units = $_POST['business_units'];
	$title = $_POST['title'];
	$telephone = $_POST['telephone'];
	$location = $_POST['location'];
	
	$validationErrors = array();
	
	if (!isValid($first_name,TEXT_LOOSE,false)) $validationErrors[] = 'You have not entered a valid first name';
	if (!isValid($last_name,TEXT_LOOSE,false)) $validationErrors[] = 'You have not entered a valid last name';
	if (!isValid($email,TEXT_EMAIL,false)) $validationErrors[] = 'You have not entered a valid email address';
	
	if (!isValid($password,TEXT_LOOSE,false)) $validationErrors[] = 'You have not entered a valid password';
	if (mb_strlen ($password) < 8) $validationErrors[] = 'Password must be more than or equal to 8 characters.';
	if (!preg_match('/[A-Za-z]/', $password) OR !preg_match('/[0-9]/', $password)) $validationErrors[] = 'Password must contain a number and a letter';
	
	if ($password != $confirm_password)
		$validationErrors[] = 'Your password and confirm password did not match';

	if (!isValid($company_name,TEXT_LOOSE,false)) $validationErrors[] = 'You have not entered a valid company name';
	if(count($divisions)==0) $validationErrors[] = 'You have not picked a division';
	if(count($business_units)==0) $validationErrors[] = 'You have not picked a business unit';
	
	if (noErrors($validationErrors))
	{
		$divisions_sql = implode(',',$divisions);
		$business_units_sql = implode(',',$business_units);
		
		$divisions_email = implode('<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;',$divisions);
		$business_units_email = implode('<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;',$business_units);
		$sql = "INSERT INTO temp_users (first_name, last_name, email, password, company_name, divisions, business_units, title, telephone, location)
				VALUES ('{$first_name}','{$last_name}','{$email}','{$password}','{$company_name}',
					'{$divisions_sql}','{$business_units_sql}','{$title}','{$telephone}','{$location}')";
		// echo $sql;
		$dbh->executeNonQuery($sql);
		
		//add email here
		$message = "Hello,<br><br>";
		$message .= "A user registered with the following details:<br><br>";
		
		$message .= "<b>First Name : </b>$first_name <br>";
		$message .= "<b>Last Name : </b>$last_name <br>";
		$message .= "<b>Email Address : </b>$email <br>";
		$message .= "<b>Password : </b>$password <br>";
		$message .= "<b>Company Name : </b>$company_name <br>";
		$message .= "<b>Divisions : </b><br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;$divisions_email <br>";
		$message .= "<b>Business Units : </b><br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;$business_units_email <br>";
		$message .= "<b>Title : </b>$title <br>";
		$message .= "<b>Telephone : </b>$telephone <br>";
		$message .= "<b>Location : </b>$location <br>";
		
		
		// $toAddress = '<EMAIL>';
		$toAddress = '<EMAIL>';
		$subject = 'User Registration';
		/* EMAIL*/
		$email = new EmailTemplate('views/emails/basicEmail.html',SYSTEMURL);
		$email->items['content'] = $message;
		sendMail ($toAddress, 'Cirrus8', $email->toString (), $subject);
		/* end EMAIL*/
		
		$signup_name = $first_name . ' ' . $last_name;
		header("Location: signup.php?sname=$signup_name");
		// exit;
	}
}

//check client_request_id
if (isset($_GET['client_request_id']))
	$_POST['client_request_id'] = $_GET['client_request_id'];

$cri = $_POST['client_request_id'];
if ($cri)
{	
	$sql = "SELECT * FROM signup_companies 
			WHERE client_request_id='{$cri}'";
	// echo $sql;
	$client = $dbh->executeSingle($sql);
}

if (!isset($_GET['client_request_id']) OR !$client)
{
	header("Location: /framework/index.php");
}

$division_list = array(
	'PMSL - Programmed Maintenance Services Ltd',
	'POM - Programmed Operations and Maintenance',
	'PPS - Programmed Property Services',
	'PSW - Programmed Skilled Workforce',
	'PP - Programmed Professionals');
	
$businessUnit_list = array(
	'PRG - Programmed Maintenance Services',
	'PRG - Programmed Safety',
	'PFM - Programmed Facility Management',
	'PM - Programmed Marine',
	'PIM - ATIVO',
	'PIM - Thomas and Coffey',
	'PPS - Programmed Property Services',
	'PET - Programmed Electrical Technologies',
	'PTP - Programmed Turnpoint',
	'PPS - Programmed Property Services',
	'PSW - Programmed Skilled Workforce',
	'PP - Programmed Professionals',
	'PHP - Programmed Health Professionals');

?>

<body class="login">
<div id="container"><br /><br />
		
	<style type="text/css">

	#loginBox {
		width: 700px;
		display: table;
	}
	.login-container {
		background: #00BAF2;
		height: 524px;
		margin: 0;
		padding:0;
		border-radius: 5px 0 0 5px;
		-webkit-border-radius: 5px 0 0 5px;
		-moz-border-radius: 5px 0 0 5px;
		width: 300px;
		display: table-cell;
	}
	.login-logo {
		background-image: url('assets/images/cirrus8.png');
		height:286px;
		width:294px;
		background-size: 100% 100%;
		background-repeat: no-repeat;
		border-radius:10px;
		-webkit-border-radius: 10px;
		-moz-border-radius: 10px;
	}
	
	.login-button-container {
		background: #387181;
		width: 100%;
		border: 0;
		color: #FFF;
		height: 30px;	
		border-radius: 5px;
		-webkit-border-radius: 5px;
		-moz-border-radius: 5px;
	}
	#btnLogin:focus {
		background: #387181 !important;
	}
	#btnLogin:active {
		background: #387181 !important;
	}

	.warning-box {
		border: 2px solid #c20022;
		background: #FEE5E9;
		color: #F1002b;
		width: 400px;
		padding: 10px;
	}
	.info-box {
		border: 2px solid #FFB812;
		background: #FFE3A1;
		color: #553C00;
		width: 400px;
		padding: 10px;
	}
	.checkbox-confirmation {
		background: #A7ECFF;
		margin: 10px;
		padding: 10px;
		border-radius: 5px;
		-webkit-border-radius: 5px;
		-moz-border-radius: 5px;
		text-align: left;
	}
	.checkbox-confirmation a{
		color: #023442;
	}
	.slogan {
		width: 500px;
		display: table-cell;
		background: #F3F3F3;
		vertical-align: top;
		border-radius: 0 5px 5px 0;
		-webkit-border-radius: 0 5px 5px 0;
		-moz-border-radius: 0 5px 5px 0;
	}
	
	.updates-container {
		border: 2px solid #c2c2c2;
		background: #F3F3F3;
		width: 500px;
		padding: 10px;
		text-align: left;
		box-shadow: 5px 5px 5px #888888;
		-moz-box-shadow: 5px 5px 5px #888888;
		-webkit-box-shadow: 5px 5px 5px #888888;
		font-size : 13px;
	}
	.updates-container table {
		background: transparent;
		overflow-y: auto;
		max-height: 100px;
		min-height: 100px;
		display: block;
	}
	.updates-container table tr td ul {
		padding-right: 0;
		margin-right: 0;
		margin-left: 0;
	}
	.updates-container table tr td:nth-child(1) {
		width : 80px;
	}
	.updates-container table tr td:nth-child(2) , .updates-container table tr td:nth-child(4) {
		width : 10px;
	}
	.updates-container table tr td:nth-child(3) {
		width : 100px;
	}
	.updates-container table tr td:nth-child(5) {
		width : 300px;
	}
	
	.required{
		color:red;
	}
</style>
<div align="center">
		<br />
	<div id="javaWarning">
		<div class="warning-box"><b>You currently have javascript disabled, or are using an unsupported browser.</b><br />The cirrus8 website is designed to support <a style="color:#00BAF2;border-bottom:1px dotted white;" href="http://developer.yahoo.com/yui/articles/gbs/#gbschart">A-Grade browsers</a> to provide the best possible user experience. Please enable Javascript or upgrade your browser to continue.</div>
		<br />
	</div>

	<div id="cookieWarning" style="display:none">
		<div class="warning-box"><b>You currently have cookies disabled, or are using an unsupported browser.</b><br />The cirrus8 website is designed to support <a style="color:#00BAF2;border-bottom:1px dotted white;" href="http://developer.yahoo.com/yui/articles/gbs/#gbschart">A-Grade browsers</a> to provide the best possible user experience. Please enable cookies or upgrade your browser to continue.</div>
		<br />
	</div>
	
	<script type="text/javascript">
	if (!isCookie()) $('#cookieWarning').css('display','block');
	</script>
	
	<script type="text/javascript">var javaWarn = document.getElementById('javaWarning'); javaWarn.style.display = 'none';</script>
				
		<br/>
	<div id="loginBox" class="messageBox">
		<form id="form" name="form" method="POST" action="#">
			<div class="login-container">
				<div class="login-logo"></div>				
			</div>
			<div class="slogan">
			
			<div>
				<br>
				<h1 style="text-align: center; color:#111">User Registration</h1>
				<font style="color:red; text-align: left;"><?=renderErrors($validationErrors)?></font>
			</div>

			<br/>
			<table style="background: #F3F3F3;" cellspacing="5">
				<tr>
					<td nowrap>First Name : <label class="required">&nbsp;&nbsp;*</label><td><?=renderTextBox('firstName',$_POST['firstName'], 'size=42')?></td>
					
				</tr>
				<tr>
					<td nowrap>Last Name : <label class="required">&nbsp;&nbsp;*</label><td><?=renderTextBox('lastName',$_POST['lastName'], 'size=42')?></td>
					
				</tr>
				<tr>
					<td nowrap>Email Address : <label class="required">&nbsp;&nbsp;*</label><td><?=renderTextBox('email',$_POST['email'], 'size=42')?></td>
				</tr>
				<tr>
					<td nowrap>Password : <label class="required">&nbsp;&nbsp;*</label><td><?=renderPasswordBox('password','', 'size=42')?></td>
				</tr>
				<tr>
					<td nowrap>Confirm Password : <label class="required">&nbsp;&nbsp;*</label><td><?=renderPasswordBox('confirmPassword','', 'size=42')?></td>
				</tr>

				<tr>
					<td nowrap>Company Name : <label class="required">&nbsp;&nbsp;*</label><td><b><?=$client['company_name']?></b>
					<?=renderHidden('company_name',$client['company_name'])?>
					<?=renderHidden('client_request_id',$client['client_request_id'])?>
					</td>
				</tr>

				<tr>
					<td nowrap>Divisions : <label class="required">&nbsp;&nbsp;*</label>
					<td><select name="divisions[]" multiple="multiple"  size="<?=count($division_list)?>" style="width: 283px;">
							<?php
								foreach($division_list as $division)
									echo '<option value="'.$division.'" '. (in_array($division,$_POST['divisions']) ? 'selected=true' : '') .'>'.$division.'</option>';
							?>
						</select>
					</td>
				</tr>
				<tr>
					<td nowrap>Business Units : <label class="required">&nbsp;&nbsp;*</label>
					<td><select name="business_units[]" multiple="multiple" size="<?=count($businessUnit_list)?>" style="width: 283px;">
							<?php
								foreach($businessUnit_list as $businessUnit)
									echo '<option value="'.$businessUnit.'" '. (in_array($businessUnit,$_POST['business_units']) ? 'selected=true' : '') .'>'.$businessUnit.'</option>';
							?>
						</select>
					</td>
				</tr>

				<tr>
					<td nowrap>Title : <td><?=renderTextBox('title',$_POST['title'], 'size=42')?></td>
				</tr>
				<tr>
					<td nowrap>Telephone : <td><?=renderTextBox('telephone',$_POST['telephone'], 'size=42')?></td>
				</tr>
				<tr>
					<td nowrap>Location : <td><?=renderTextBox('location',$_POST['location'], 'size=42')?></td>
				</tr>
				<tr>
					<td colspan="2" align="center"><br>
					<input class="login-button-container" type="submit" name="submit" value="Submit">
					<br>
					<br>
					</td>
				</tr>

				</table>
			</div>
		</form>
	</div>
	
	<a href="https://client.cirrus8.com.au/framework/?command=termsOfUse" target="blank">Cirrus8's Terms and Conditions</a>
</body>
</html>
<?php

function fetchOutstandingAmounts($propertyID, $runDate, $accountList, &$outstandingAmounts)
{
    $totalOutstanding = 0;
    $unpaidInvoices = dbGetUnpaidInvoices($propertyID, $runDate, null, $accountList);
    foreach ($unpaidInvoices as $invoice) {
        $amount = $invoice['transactionAmount'];
        switch ($invoice['transactionType']) {
            case 'INV':
                $amount += ($invoice['offset_3'] * 1);
                $amount += (($amount != 0) ? $invoice['offset_4'] : 0);
                break;
            case 'CRE':
                $amount += ($invoice['offset_2'] + $invoice['offset_4'] - $invoice['offset_1']);
                break;
        }

        if ($amount != 0) {
            $invoice['amount'] = $amount;
            $outstandingAmounts[] = $invoice;
            $totalOutstanding += ($amount * 1);
        }
    }

    return $totalOutstanding;
}

/*************************************************************************************************************************/

// INVOICE OBJECTS - repeating groups that appear in each invoice

class InvoiceHeader extends PDFobject
{
    public $leaseID;

    public $leaseName;

    public $dueDate;

    public $issueDate;

    public $propertyID;

    public $propertyName;

    public $ownerName;

    public $ownerABN;

    public $title;

    public $isLedger;

    public $_name = 'invoiceHeader';

    public function __construct($propertyID, $dueDate, $issueDate, $title)
    {
        $this->propertyID = $propertyID;
        $this->issueDate = $issueDate;
        $this->dueDate = $dueDate;
        $this->title = $title;
        $this->isLedger = dbGetPropertyIsLedger($propertyID);
    }

    public function preRender(&$pdf) {}

    public function render(&$pdf)
    {
        // -- main box
        $pdf->setColorExt('both', 'rgb', 0.9, 0.9, 0.9, 0); // Setting the Color to Gray
        $pdf->rect(25, 777, 275, 15);
        $pdf->fill();
        $pdf->setlinewidth(0.5);

        $pdf->setColorExt('both', 'rgb', 0.75, 0.75, 0.75, 0);
        $pdf->moveto(25, 692);
        $pdf->lineto(300, 692);
        $pdf->stroke();

        $pdf->setlinewidth(2);
        $pdf->setColorExt('both', 'rgb', 0.75, 0.75, 0.75, 0);
        $pdf->rect(25, 692, 275, 100);
        $pdf->stroke();

        $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        $this->setFont($pdf, 'Helvetica-Bold', 8);
        $pdf->showBoxed($this->title, 25, 776, 260, 13, 'center', '');

        $ownerCountry = getClientCountry();
        $business_label = $_SESSION['country_default']['business_label'];
        $business_prefix = ($this->ownerABN && $this->ownerABN != '') ? $_SESSION['country_default']['business_prefix'] : '';
        $business_number = ($ownerCountry == 'AU') ? textSpace($this->ownerABN) : $business_prefix . $this->ownerABN;

        if (strlen($business_label) == 4) {
            $labelCol = 28;
            $labelWidth = 65;
            $valueCol = 98;
            $valueWidth = 215;
        } elseif (strlen($business_label) > 4) {
            $labelCol = 25;
            $labelWidth = 80;
            $valueCol = 110;
            $valueWidth = 215;
        } else {
            $labelCol = 30;
            $labelWidth = 50;
            $valueCol = 85;
            $valueWidth = 215;
        }

        $pdf->showBoxed('Due Date', $labelCol, 762, $labelWidth, 13, 'right', '');
        $pdf->showBoxed('Issue Date', $labelCol, 752, $labelWidth, 13, 'right', '');
        $pdf->showBoxed(($this->isLedger ? 'Ledger' : 'Property'), $labelCol, 742, $labelWidth, 13, 'right', '');

        if ($this->isLedger) {
            $pdf->showBoxed('Sub-ledger', $labelCol, 722, $labelWidth, 13, 'right', '');
        }


        $pdf->showBoxed($this->isLedger ? 'Bill To' : 'Owner', $labelCol, 702, $labelWidth, 13, 'right', '');

        if ($ownerCountry == 'AU') {
            $pdf->showBoxed($this->isLedger ? 'ABN' : 'Owner ABN', $labelCol, 692, $labelWidth, 13, 'right', '');
        } else {
            $pdf->showBoxed(
                $this->isLedger ? $business_label : 'Owner ' . $business_label,
                $labelCol,
                692,
                $labelWidth,
                13,
                'right',
                ''
            );
        }

        $this->setFont($pdf, 'Helvetica', 8);

        $pdf->showBoxed($this->dueDate, $valueCol, 762, $valueWidth, 13, 'left', '');
        $pdf->showBoxed($this->issueDate, $valueCol, 752, $valueWidth, 13, 'left', '');
        $pdf->showBoxed($this->propertyID, $valueCol, 742, $valueWidth, 13, 'left', '');
        $pdf->showBoxed($this->propertyName, $valueCol, 732, $valueWidth, 13, 'left', '');

        if ($this->isLedger) {
            $pdf->showBoxed($this->leaseID, $valueCol, 722, $valueWidth, 13, 'left', '');
            $pdf->showBoxed($this->leaseName, $valueCol, 712, $valueWidth, 13, 'left', '');
        }

        $pdf->showBoxed($this->ownerName, $valueCol, 702, $valueWidth, 13, 'left', '');
        $pdf->showBoxed($business_number, $valueCol, 692, $valueWidth, 13, 'left', '');
    }
}

class InvoiceFooter extends PDFobject
{
    public $dueDate;

    public $issueDate;

    public $propertyID;

    public $propertyName;

    public $leaseID;

    public $leaseName;

    public $ownerName;

    public $ownerABN;

    public $title;

    public $officeName;

    public $officeAddress;

    public $officeCity;

    public $officeState;

    public $officePostCode;

    public $mailingName;

    public $mailingAddress;

    public $mailingCity;

    public $mailingState;

    public $mailingPostCode;

    public $bankAccount;

    public $bankAccountName;

    public $bankBSB;

    public $bankName;

    public $isLedger;

    public $hideEFTDetails;

    public string $bsbLabel;

    public function __construct($propertyID, $dueDate, $issueDate, $title)
    {
        $this->propertyID = $propertyID;
        $this->issueDate = $issueDate;
        $this->dueDate = $dueDate;
        $this->title = $title;
        $this->isLedger = dbGetPropertyIsLedger($propertyID);
        $this->hideEFTDetails = dbCheckEFTDetailsAgent($propertyID);
        $this->bsbLabel = getBsbLabelFromSession();
    }

    public function preRender(&$pdf) {}

    public function render(&$pdf)
    {
        $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        $pdf->setlinewidth(0.5);
        // Grey Areas...
        $pdf->setColorExt('both', 'rgb', 0.9, 0.9, 0.9, 0); // Setting the Color to Gray
        $pdf->rect(360, 240, 210, 15);
        $pdf->fill();
        if (! $this->hideEFTDetails) {
            $pdf->rect(25, 240, 300, 15);
            $pdf->fill();
        }

        $pdf->setColorExt('both', 'rgb', 0.95, 0.95, 0.95, 0); // Setting the Color to Gray
        $pdf->rect(360, 130, 210, 70);
        $pdf->fill();
        if (! $this->hideEFTDetails) {
            $pdf->rect(25, 160, 300, 15);
            $pdf->fill();
        }

        $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0); // Setting the Color to Gray
        $pdf->rect(360, 100, 210, 30);
        $pdf->fill();
        // Horizontal Lines...

        $pdf->setColorExt('both', 'rgb', 0.75, 0.75, 0.75, 0);
        if (! $this->hideEFTDetails) {
            $pdf->moveto(25, 175);
            $pdf->lineto(325, 175);
            $pdf->stroke();
        }

        $pdf->moveto(360, 200);
        $pdf->lineto(570, 200);
        $pdf->stroke();
        $pdf->moveto(360, 240);
        $pdf->lineto(570, 240);
        $pdf->stroke();
        if (! $this->hideEFTDetails) {
            $pdf->moveto(25, 240);
            $pdf->lineto(325, 240);
            $pdf->stroke();
        }

        $pdf->setlinewidth(2);
        $pdf->setColorExt('both', 'rgb', 0.75, 0.75, 0.75, 0);
        $pdf->rect(360, 100, 210, 155);
        $pdf->stroke();
        if (! $this->hideEFTDetails) {
            $pdf->rect(25, 160, 300, 95);
            $pdf->stroke();
        }

        // Text
        $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        $this->setFont($pdf, 'Helvetica-Bold', 8);
        if (! $this->hideEFTDetails) {
            $pdf->showBoxed('BANKING DETAILS', 25, 240, 300, 12, 'center', '');
        }

        $pdf->showBoxed('REMITTANCE ADVICE', 360, 240, 210, 12, 'center', '');

        // PDF_set_value('leading',20);

        // Text Address...
        $this->setFont($pdf, 'Helvetica', 8);
        $pdf->showBoxed(
            "{$this->officeName}\r\n{$this->officeAddress}\r\n{$this->officeCity}, {$this->officeState} {$this->officePostCode}",
            95,
            50,
            232,
            42,
            'left',
            ''
        );
        $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        $this->setFont($pdf, 'Helvetica', 8);
        $pdf->showBoxed(
            "{$this->mailingName}\n{$this->mailingAddress}\n{$this->mailingCity} {$this->mailingState},  {$this->mailingPostCode}",
            367,
            98,
            200,
            135,
            'left',
            ''
        );
        $this->setFont($pdf, 'Helvetica-Bold', 7);

        if (! $this->isLedger) {
            $pdf->showBoxed("Due Date\nProperty\n\nOwner", 362, 105, 40, 83, 'right', '');
        } else {
            $pdf->showBoxed("Due Date\nLedger\n\nSub-ledger\n\nBill To", 362, 105, 40, 83, 'right', '');
        }

        $this->setFont($pdf, 'Helvetica-Bold', 8);

        if (! $this->hideEFTDetails) {
            if (getDisplayBsbFromSession()) {
                $pdf->showBoxed("Account\nBank\n{$this->bsbLabel}\nAccount No", 30, 86, 50, 138, 'right', '');
            } else {
                $pdf->showBoxed("Account\nBank\nAccount No", 30, 86, 50, 138, 'right', '');
            }
        }

        $this->setFont($pdf, 'Helvetica-Bold', 8);
        $this->setFont($pdf, 'Helvetica', 7);
        $wrap_owner_name = wrapText($this->ownerName, 40);

        if (! $this->isLedger) {
            $pdf->showBoxed(
                "{$this->dueDate} \n{$this->propertyID}\n{$this->propertyName}\n{$wrap_owner_name}",
                408,
                105,
                415,
                83,
                'left',
                ''
            );
        } else {
            $pdf->showBoxed(
                "{$this->dueDate} \n{$this->propertyID}\n{$this->propertyName}\n{$this->leaseID}\n{$this->leaseName}\n{$wrap_owner_name}",
                408,
                105,
                415,
                83,
                'left',
                ''
            );
        }

        $this->setFont($pdf, 'Helvetica', 8);


        if (! $this->hideEFTDetails) {
            if (getDisplayBsbFromSession()) {
                $pdf->showBoxed(
                    "{$this->bankAccountName}\n{$this->bankName}\n" . formatWithDelimiter(
                        $this->bankBSB
                    ) . "\n" . textSpace(
                        $this->bankAccount
                    ),
                    90,
                    86,
                    250,
                    138,
                    'left',
                    ''
                );
            } else {
                $pdf->showBoxed(
                    "{$this->bankAccountName}\n{$this->bankName}\n" . textSpace($this->bankAccount),
                    90,
                    86,
                    250,
                    138,
                    'left',
                    ''
                );
            }

            $this->setFont($pdf, 'Helvetica-Oblique', 7);
            $pdf->showBoxed('If paying electronically please quote - ', 30, 160, 250, 13, 'left', '');
            $this->setFont($pdf, 'Helvetica-Bold', 8);
            $pdf->showBoxed(
                $this->propertyID . ($this->leaseID ? ' - ' . $this->leaseID : ''),
                170,
                160,
                150,
                13,
                'right',
                ''
            );
        }

        $this->setFont($pdf, 'Helvetica-Bold', 8);
        $pdf->setColorExt('both', 'rgb', 1, 1, 1, 0); // Setting the Color to Gray
        $pdf->showBoxed($this->title, 367, 100, 185, 20, 'left', '');
    }
}


class MailAddressWindow extends PDFobject
{
    public $attention;

    public $name;

    public $street;

    public $city;

    public $state;

    public $postcode;

    public function __construct($propertyID, $leaseID = '')
    {
        $mailAddress = $leaseID ? dbGetLeaseAddress($propertyID, $leaseID) : dbGetPropertyAddress($propertyID);

        $this->name = $mailAddress['mailingName'];
        $this->street = $mailAddress['mailingAddress'];
        $this->city = $mailAddress['mailingCity'];
        $this->state = $mailAddress['mailingState'];
        $this->postcode = $mailAddress['mailingPostCode'];
    }

    public function preRender(&$pdf) {}

    public function render(&$pdf)
    {
        $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        $this->setFont($pdf, 'Helvetica', 8); // This is for the disclaimer information...
        $address = (($this->attention) ? "{$this->attention}\n" : '') . "{$this->name}\n{$this->street}\n{$this->city} {$this->state},  {$this->postcode}";
        $pdf->showBoxed($address, 95, 622, 232, 46, 'left', ''); // This is for the from field
    }
}


class InvoiceLines extends PDFobject
{
    public $title;

    public function __construct($title)
    {
        $this->title = $title;
    }

    public function preRender(&$pdf) {}

    public function render(&$pdf)
    {
        $pdf->setColorExt('both', 'rgb', 0.9, 0.9, 0.9, 0); // Setting the Color to Gray
        $pdf->rect(25, 546, 545, 15);
        $pdf->fill();
        $pdf->rect(25, 285, 410, 15);
        $pdf->fill();
        $pdf->setlinewidth(0.5);

        $pdf->setColorExt('both', 'rgb', 0.75, 0.75, 0.75, 0);
        $pdf->moveto(25, 546);
        $pdf->lineto(570, 546);
        $pdf->stroke();
        $pdf->moveto(25, 300);
        $pdf->lineto(570, 300);
        $pdf->stroke();


        $pdf->setlinewidth(2);
        $pdf->rect(25, 285, 545, 276);
        $pdf->stroke();
        $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        // Text

        $this->setFont($pdf, 'Helvetica-Bold', 9);
        $pdf->showBoxed($this->title, 25, 286, 405, 13, 'right', '');
        $pdf->showBoxed('  DATE', 25, 546, 45, 13, 'center', '');
        $pdf->showBoxed('INVOICE NO.', 85, 546, 45, 13, 'right', '');
        $pdf->showBoxed('PARTICULARS', 140, 546, 300, 13, 'left', '');
        $pdf->showBoxed('NET', 400, 546, 45, 13, 'right', '');
        $pdf->showBoxed($_SESSION['country_default']['tax_label'], 460, 546, 45, 13, 'right', '');
        $pdf->showBoxed('TOTAL', 520, 546, 45, 13, 'right', '');

        $this->setFont($pdf, 'Helvetica', 8);
    }
}


class FoldLines extends PDFObject
{
    public function render(&$pdf)
    {
        $pdf->setColorExt('both', 'rgb', 0.8, 0.8, 0.8, 0);
        $pdf->setlinewidth(1);
        $pdf->moveto(0, 566);
        $pdf->lineto(2, 566);
        $pdf->stroke();
        $pdf->moveto(593, 566);
        $pdf->lineto(595, 566);
        $pdf->stroke();
        $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
    }

    public function preRender(&$pdf) {}
}

class CutOffLine extends PDFObject
{
    public function preRender(&$pdf) {}

    public function render(&$pdf)
    {
        $pdf->setColorExt('both', 'rgb', 0.6, 0.6, 0.6, 0); // Setting the Color to Black
        $pdf->setlinewidth(0.5);
        $pdf->set_graphics_option('dasharray={2 2} dashphase=0');

        $pdf->moveto(0, 281);
        $pdf->lineto(595, 281);
        $pdf->stroke();


        $this->setFont($pdf, 'ZapfDingbats', 18);
        $pdf->showBoxed('#', 25, 269, 20, 20, 'left', '');

        $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        $this->setFont($pdf, 'Helvetica-Oblique', 6);
        $pdf->showBoxed(
            'Please detach this section and return with your payment:',
            0,
            265,
            595,
            10,
            'center',
            ''
        ); // Date

    }
}


class InvoiceNote extends PDFObject
{
    public $note;

    public function __construct($note)
    {
        $this->note = $note;
    }

    public function preRender(&$pdf) {}

    public function render(&$pdf)
    {
        $pdf->setColorExt('both', 'rgb', 0.95, 0.95, 0.95, 0);
        $pdf->setlinewidth(0.5);
        $pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        $this->setFont($pdf, 'Helvetica-Oblique', 7);
        $pdf->showBoxed('NOTE: ' . $this->note, 30, 561, 535, 22, 'left', '');
    }
}

/*************************************************************************************************************************/

class Invoice extends PDFReport
{
    public $pdf;

    public $lineOffset = 15;

    public $logoFile = false;

    public function __construct(&$dataSource, $logoFile = false)
    {
        parent::__construct($dataSource);
        if ($logoFile) {
            $this->logoFile = $logoFile;
        }
    }

    public function updateLineOffset($offset)
    {
        $this->lineOffset += $offset;
        if ($this->lineOffset >= 245) {
            $this->renderTotal('continued...');
            $this->renderRemittanceTotal('continued...');
            $this->endPage();
            $this->preparePage();
            $this->render();
        }
    }

    public function renderInvoiceRow($date, $invoiceNumber, $description, $netAmount, $gstAmount, $grossAmount)
    {
        $this->pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        $this->setFont('Helvetica-Bold', 8);

        $this->pdf->showBoxed($date, 20, 546 - $this->lineOffset, 50, 13, 'right', '');
        $this->pdf->showBoxed($invoiceNumber, 85, 546 - $this->lineOffset, 45, 13, 'center', '');
        $this->pdf->showBoxed($description, 140, 546 - $this->lineOffset, 300, 13, 'left', '');
        $this->pdf->showBoxed($netAmount, 395, 546 - $this->lineOffset, 50, 13, 'right', '');
        $this->pdf->showBoxed($gstAmount, 455, 546 - $this->lineOffset, 50, 13, 'right', '');
        $this->pdf->showBoxed($grossAmount, 515, 546 - $this->lineOffset, 50, 13, 'right', '');

        $this->updateLineOffset(10);
    }

    public function renderTotal($amount)
    {
        $this->pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        $this->setFont('Helvetica-Bold', 8);

        if (is_numeric($amount)) {
            $this->pdf->showBoxed(toMoney($amount), 25, 285, 540, 13, 'right', '');
        } else {
            $this->pdf->showBoxed($amount, 25, 285, 540, 13, 'right', '');
        }
    }

    public function renderRemittanceTotal($amount)
    {
        $this->pdf->setColorExt('both', 'rgb', 1, 1, 1, 0);
        $this->setFont('Helvetica-Bold', 9);
        if (is_numeric($amount)) {
            $this->pdf->showBoxed(toMoney($amount), 380, 100, 185, 20, 'right', '');
        } else {
            $this->pdf->showBoxed($amount, 380, 100, 185, 20, 'right', '');
        }

        $this->setFont('Helvetica', 8);
    }

    public function renderTitle($title)
    {
        $this->pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        $this->setFont('Helvetica-Bold', 8);
        $this->pdf->showBoxed($title, 30, 546 - $this->lineOffset, 400, 13, 'left', '');

        $this->updateLineOffset(20);
    }

    public function renderSubTotal($title, $netAmount, $gstAmount, $grossAmount)
    {
        $this->pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        $this->pdf->setlinewidth(0.5);

        if ($netAmount) {
            $this->pdf->moveto(400, 559 - $this->lineOffset);
            $this->pdf->lineto(445, 559 - $this->lineOffset);
            $this->pdf->stroke();
        }

        if ($gstAmount) {
            $this->pdf->moveto(460, 559 - $this->lineOffset);
            $this->pdf->lineto(505, 559 - $this->lineOffset);
            $this->pdf->stroke();
        }

        if ($grossAmount) {
            $this->pdf->moveto(520, 559 - $this->lineOffset);
            $this->pdf->lineto(565, 559 - $this->lineOffset);
            $this->pdf->stroke();
        }

        $this->setFont('Helvetica-Bold', 8);
        $this->pdf->showBoxed($netAmount, 395, 546 - $this->lineOffset, 50, 13, 'right', '');
        $this->pdf->showBoxed($gstAmount, 455, 546 - $this->lineOffset, 50, 13, 'right', '');
        $this->pdf->showBoxed($grossAmount, 515, 546 - $this->lineOffset, 50, 13, 'right', '');

        if ($title) {
            $this->setFont('Helvetica-Bold', 8);
            $this->pdf->showBoxed($title, 140, 546 - $this->lineOffset, 300, 13, 'left', '');
            $this->setFont('Helvetica', 8);
        }

        $this->updateLineOffset(10);
    }

    public function renderLogo()
    {
        if ($this->logoFile) {
            $maxWidth = LOGO_WIDTH_INVOICE;
            $maxHeight = LOGO_HEIGHT_INVOICE;
            [$imageWidth, $imageHeight] = getimagesize($this->logoFile);

            $horizontalScaling = ($imageWidth > $maxWidth) ? $maxWidth / $imageWidth : 1;
            $verticalScaling = ($imageHeight > $maxHeight) ? $maxHeight / $imageHeight : 1;
            $imageScale = ($horizontalScaling < $verticalScaling) ? $horizontalScaling : $verticalScaling;

            $imageScale = round($imageScale, 2);

            $vMargin = 25;
            $hMargin = 25;

            $hPos = 595 - ($imageScale * $imageWidth) - $hMargin;
            $vPos = 842 - ($imageScale * $imageHeight) - $vMargin;

            $pdfimage = $this->pdf->load_image('auto', $this->logoFile, '');
            $this->pdf->fit_image($pdfimage, $hPos, $vPos, 'boxsize {' . "{$maxWidth} {$maxHeight}" . '} fitmethod meet');
            $this->pdf->close_image($pdfimage);
        }
    }

    public function preparePage($printTemplate = true)
    {
        $this->lineOffset = 15;
        parent::preparePage();
        $this->render();
        $this->renderLogo();
    }

    public function close()
    {
        parent::close();
    }
}

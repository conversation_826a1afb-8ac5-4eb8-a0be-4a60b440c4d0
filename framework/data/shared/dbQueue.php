<?php

function dbAddQueueItem($type, $databaseID, $user, $command, $data)
{
    global $dbh, $clientDB;
    $dbh->selectDatabase(SYSTEMDB);
    $params = [];
    if (intval($databaseID) === 0) {
        $databaseID = dbGetClientID();
    }

    $type = intval($type);
    $databaseID = intval($databaseID);

    // Get suffix for next scheduledTask table to be inserted

    switch (true) {
        case in_array($clientDB, ['Allard_Shelton', 'Allard_Shelton_test']):
            $next_tbl_suffix = 'Allard';
            break;
        case in_array($clientDB, ['Fitzroys', 'Fitzroys_test']):
            $next_tbl_suffix = 'Fitzroys';
            break;
        case in_array($clientDB, ['Facey_Property', 'Facey_Property_test']):
            $next_tbl_suffix = 'Facey_Property';
            break;
        case in_array($clientDB, ['CPG_Southern_Sydney', 'CPG_Southern_Sydney_test']):
            $next_tbl_suffix = 'CSS';
            break;
        case in_array($clientDB, ['Te<PERSON>_Carson', 'Teska_Carson_test']):
            $next_tbl_suffix = 'Teska';
            break;
        case in_array($clientDB, ['Nichols_Crowder', 'Nichols_Crowder_test']):
            $next_tbl_suffix = 'Nichols';
            break;
        case in_array($clientDB, ['Crabtrees_RE', 'Crabtrees_RE_test']):
            $next_tbl_suffix = 'Crabtree';
            break;
        case in_array($clientDB, ['BR_WA', 'BR_WA_test']):
            $next_tbl_suffix = 'BRWA';
            break;
        case in_array($clientDB, ['BR_Vic', 'BR_Vic_test']):
            $next_tbl_suffix = 'BRMelbourne';
            break;
        case dbGetScheduledTaskExist('', $databaseID):
            $next_tbl_suffix = '';
            break;
        case dbGetScheduledTaskExist('2', $databaseID):
            $next_tbl_suffix = '2';
            break;
        case dbGetScheduledTaskExist('3', $databaseID):
            $next_tbl_suffix = '3';
            break;
        default:
            $next_tbl_suffix = dbGetScheduledTaskTable();
    }

    $sql = "
		INSERT INTO
			scheduledTasks{$next_tbl_suffix}
			(type, databaseID, createdBy, command, data, createdDate)
		VALUES
			(?,?,?,?,?, GETDATE())";
    $params = [$type, $databaseID, $user, $command, $data];

    return $dbh->executeNonQuery2($sql, $params);
}

function dbGetScheduledTaskExist($next_tbl_suffix, $database)
{
    global $dbh;
    $dbh->selectDatabase(SYSTEMDB);

    $sql = " SELECT COUNT(*) AS countrow  FROM scheduledTasks{$next_tbl_suffix} where databaseID =?";

    $row = $dbh->executeSingle($sql, [$database]);

    return $row['countrow'];
}

function dbGetScheduledTaskTable()
{
    global $dbh;
    $dbh->selectDatabase(SYSTEMDB);

    $sql = "
        SELECT TOP 1 *
        FROM
            (
                SELECT COUNT(*) AS countrow, 1 as seq, '' as suffix FROM scheduledTasks
                UNION ALL
                SELECT COUNT(*) AS countrow, 2 as seq, '2' as suffix FROM scheduledTasks2
                UNION ALL
                SELECT COUNT(*) AS countrow, 3 as seq, '3' as suffix FROM scheduledTasks3
            ) as schedTasks
        ORDER BY countrow, seq
    ";

    $row = $dbh->executeSingle($sql);

    return $row['suffix'];
}

function dbAddQueueItem_Original($type, $databaseID, $user, $command, $data)
{
    global $dbh;
    $dbh->selectDatabase(SYSTEMDB);

    $type = intval($type);
    $databaseID = intval($databaseID);
    $user = $dbh->prepareField($user);
    $command = $dbh->prepareField($command);
    $data = $dbh->prepareField($data);

    $sql = "
		INSERT INTO
			scheduledTasks
			(type, databaseID, createdBy, command, data, createdDate)
		VALUES
			('{$type}', '{$databaseID}', '{$user}', '{$command}', '{$data}', GETDATE())";

    return $dbh->executeNonQuery($sql);
}


function dbIncrementQueueItem($queueID, $suffix = '')
{
    global $dbh;
    $dbh->selectDatabase(SYSTEMDB);
    $sql = "
						UPDATE  scheduledTasks{$suffix}
						SET count=(count+1),
						lastAccessed = GETDATE()
						WHERE queueID=?
					";

    return $dbh->executeNonQuery2($sql, [$queueID]);
}

function dbErrorCodeQueueItem($queueID, $suffix = '', $error_Code = '')
{
    global $dbh;
    $dbh->selectDatabase(SYSTEMDB);
    $sql = "
						UPDATE  scheduledTasks{$suffix}
						SET error_code = ?
						WHERE queueID=?
					";

    return $dbh->executeNonQuery2($sql, [$error_Code, $queueID]);
}

function dbGetQueueItemsByType($threshold, $timeFrame = null, $type = null, $suffix = '')
{
    global $dbh;
    $dbh->selectDatabase(SYSTEMDB);
    $params = [$threshold];
    $typeSQL = ($type) ? ' AND type=' . addSQLParam($params, $type) : '';
    if ($timeFrame) {
        $timeFrameSQL = ' AND ((lastAccessed IS NULL) OR (DATEDIFF(second, lastAccessed, getdate()) > ' . addSQLParam(
            $params,
            $timeFrame
        ) . '))';
    }

    $sql = "
                    SELECT
                        queueID,
                        type,
                        databaseID,
                        command,
                        data,
                        CONVERT(char(10), createdDate, 103) AS createdDate,
                        createdBy,
                        count
                    FROM scheduledTasks{$suffix}
                    WHERE count < ?
                    {$typeSQL}
                    {$timeFrameSQL}
                    ORDER BY
                    count ASC,
                    createdDate ASC
                ";

    return $dbh->executeSet($sql, false, true, $params);
}

function dbGetNextQueueItemByType($threshold, $timeFrame = null, $type = null, $suffix = '')
{
    global $dbh;
    $dbh->selectDatabase(SYSTEMDB);
    $params = [$threshold];
    $typeSQL = ($type) ? ' AND type=' . addSQLParam($params, $type) : '';
    if ($timeFrame) {
        $timeFrameSQL = ' AND ((lastAccessed IS NULL) OR (DATEDIFF(second, lastAccessed, getdate()) > ' . addSQLParam(
            $params,
            $timeFrame
        ) . '))';
    }

    $sql = "
                    SELECT TOP 1
                        queueID,
                        type,
                        databaseID,
                        command,
                        data,
                        CONVERT(char(10), createdDate, 103) AS createdDate,
                        createdBy,
                        count,
                        createdDate AS createdDateTime
                    FROM scheduledTasks{$suffix}
                    WHERE count < ?
                    {$typeSQL}
                    {$timeFrameSQL}
                     ORDER BY
                    count ASC,
                    createdDateTime ASC
                ";

    return $dbh->executeSingle($sql, $params);
}

function dbDeleteQueueItem($queueID, $suffix = '')
{
    global $dbh;
    $dbh->selectDatabase(SYSTEMDB);
    $sql = "INSERT INTO scheduledTasksLog{$suffix} (queueID, databaseID, type, command, data, count, createdDate, createdBy, lastAccessed,error_code)
    SELECT queueID, databaseID, type, command, data, count, createdDate, createdBy, lastAccessed, error_code FROM scheduledTasks{$suffix} WHERE queueID=?";
    $dbh->executeNonQuery2($sql, [$queueID]);
    $sql = "DELETE FROM scheduledTasks{$suffix} WHERE queueID=?";
    $dbh->executeNonQuery2($sql, [$queueID]);
}

<?php

include_once SYSTEMPATH . '/lib/enums/InvoiceChargeType.php';

use enums\InvoiceChargeType;

function dbResetInvoiceNumber($batchNumber, $lineNumber, $invoiceNumber)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $params = [];

    $sql = '
		UPDATE
			ar_transaction
		SET
			artr_gst_inv_no = 0,
			due_date = NULL
		WHERE
			batch_nr = ' . addSQLParam($params, $batchNumber) . '
            AND batch_line_nr in (' . addSQLParam($params, $lineNumber) . ')
            AND artr_gst_inv_no = ' . addSQLParam($params, $invoiceNumber);

    return $dbh->executeNonQuery2($sql, $params);
}

function dbCountInvoiceNumber($invoiceNumber)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $sql = '
		SELECT COUNT(artr_gst_inv_no) AS count
		FROM
		  ar_transaction
		WHERE
             artr_gst_inv_no = ?';

    return $dbh->executeSingle($sql, [$invoiceNumber]);
}

function dbTransactionsForInvoiceNumber($invoiceNumber)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $sql = "
		SELECT
			batch_nr AS batchNumber,
			batch_line_nr AS lineNumber,
			description,
			ref_1 AS reference,
			debtor_code AS debtorID,
			pmco_name AS debtorName,
			CONVERT(char(10),artr_gst_inv_dt, 103) AS invoiceDate,
			CONVERT(char(10),trans_date, 103) AS transactionDate,
			ref_2 AS propertyID,
			ref_4 AS leaseID,
			artr_gst_inv_no AS invoiceNumber,
			trans_amt AS transactionAmount
		FROM
			ar_transaction,
			pmco_company
		WHERE
			debtor_code = pmco_code
			AND artr_gst_inv_no > 0
			AND trans_type IN ('CRE', 'INV')
			AND artr_gst_inv_no = ?
		ORDER BY
			artr_gst_inv_no";

    return $dbh->executeSet($sql, false, true, [$invoiceNumber]);
}

function dbTransactionsByInvoiceNumber(
    $propertyID = null,
    $leaseID = null,
    $fromInvoice = null,
    $toInvoice = null,
    $fromDate = null,
    $toDate = null
) {
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $params = [];

    $conditions = [];
    if ($fromDate) {
        $conditions[] = 'trans_date >= CONVERT(datetime, ' . addSQLParam($params, $fromDate) . ', 103)';
    }

    if ($toDate) {
        $conditions[] = 'trans_date <= CONVERT(datetime, ' . addSQLParam($params, $toDate) . ', 103)';
    }

    if ($fromInvoice) {
        $conditions[] = 'artr_gst_inv_no >= RTRIM(' . addSQLParam($params, $fromInvoice) . ')';
    }

    if ($toInvoice) {
        $conditions[] = 'artr_gst_inv_no <= RTRIM(' . addSQLParam($params, $toInvoice) . ')';
    }

    if ($propertyID) {
        $conditions[] = 'ref_2 = ' . addSQLParam($params, $propertyID);
    }

    if ($leaseID) {
        $conditions[] = 'ref_4 = ' . addSQLParam($params, $leaseID);
    }

    $conditionsSQL = implode(' AND ', $conditions);
    $sql = "
		SELECT
			debtor_code AS debtorID,
			pmco_name AS debtorName,
			CONVERT (char(10),artr_gst_inv_dt, 103) AS invoiceDate,
			CONVERT (char(10),due_date, 103) AS dueDate,
			ref_2 AS propertyID,
			ref_4 AS leaseID,
			artr_gst_inv_no AS invoiceNumber,
			SUM (trans_amt) AS transactionAmount,
			COUNT(artr_gst_inv_no) AS transactions
		FROM
			ar_transaction,
			pmco_company
		WHERE
			debtor_code = pmco_code
			AND artr_gst_inv_no > 0
			AND trans_type IN ('CRE','INV')
			" . ($conditionsSQL !== '' && $conditionsSQL !== '0' ? " AND {$conditionsSQL} " : '') . '
		GROUP BY
			ref_2,
			ref_4,
			artr_gst_inv_no,
			artr_gst_inv_dt,
			pmco_name,
			debtor_code,
			due_date
		ORDER BY
			artr_gst_inv_no';

    return $dbh->executeSet($sql, false, true, $params);
}

function dbReceiptsListingV2($debtorCode, $fromDate, $toDate, $propertyID = null, $leaseID = null, $bankID = null)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $params = [];
    $addwhere_params = [];
    $addwhere = $debtorCode ? ' AND (ar_transaction.debtor_code = ' . addSQLParam(
        $addwhere_params,
        $debtorCode
    ) . ') ' : '';

    if ($bankID) {
        $addwhere .= ' AND (ar_transaction.bank = ' . addSQLParam($addwhere_params, $bankID) . ') ';
    }

    if ($propertyID && ! is_array($propertyID)) {
        $addwhere .= ' AND (ar_transaction.ref_2 = ' . addSQLParam($addwhere_params, $propertyID) . ') ';
    } elseif ($propertyID && is_array($propertyID)) {
        $propertyID = implode("','", $propertyID);
        $addwhere .= " AND ar_transaction.ref_2 IN ('{$propertyID}') ";
    }

    if ($propertyID && $leaseID && ! is_array($leaseID)) {
        $addwhere .= is_array($leaseID) ? '' : ' AND (ar_transaction.ref_4 = ' . addSQLParam(
            $addwhere_params,
            $leaseID
        ) . ')  ';
    } elseif ($propertyID && $leaseID && is_array($leaseID)) {
        $leaseID = implode("','", $leaseID);
        $addwhere .= " AND ar_transaction.ref_4 IN ('{$leaseID}') ";
    }

    $sql = "
		SELECT

			artr_journal AS is_journal,
            ar_transaction.ref_2 AS propertyID,
            pmco_company.pmco_name AS propertyName,
            ar_transaction.ref_4 AS leaseID,
            ar_transaction.debtor_code AS debtorID,
            pmco_name AS debtorName,
            CONVERT (char(10),trans_date, 103) AS receiptDate,
            ar_transaction.ref_1 AS chequeNumber,
            ar_transaction.spare_1 AS receiptNumber,
            COALESCE(SUM((pmxd_alloc_amt) * -1), 0) AS amountReceived ,
            ar_transaction.trans_date,
            pmbk_db_inst AS institution,
            batch_nr , pmbk_code , pay_bsb , pay_chk_number,pmbk_code as bank

		FROM
			ar_transaction

		JOIN pmco_company ON ar_transaction.debtor_code = pmco_code
		JOIN pmbk_bank ON bank = pmbk_code
		JOIN pmxd_ar_alloc ON batch_nr = pmxd_f_batch and batch_line_nr = pmxd_f_line AND pmxd_f_type != 'REV'
		JOIN pmle_lease ON pmxd_prop = pmle_prop AND pmxd_lease = pmle_lease

		WHERE trans_date >= CONVERT (datetime, " . addSQLParam($params, $fromDate) . ', 103)
		AND trans_date <= CONVERT (datetime, ' . addSQLParam($params, $toDate) . ", 103)
		AND ar_transaction.trans_amt <> 0.00
		AND ar_transaction.trans_type = 'CSH'

		{$addwhere}" . addSQLParam($params, $addwhere_params, false) . '

        GROUP BY artr_journal , ref_2 , pmco_name , ref_4 , debtor_code , pmco_name , trans_date , ref_1 , spare_1,
         pmbk_db_inst , batch_nr , pmbk_code , pay_bsb , pay_chk_number
		ORDER BY
			ar_transaction.trans_date DESC';

    return $dbh->executeSet($sql, false, true, $params);
}

if (! function_exists('getOffset')) {
    function getOffset(
        $invoiceReceipted,
        $invoiceCredited,
        $invoiceAdjusted,
        $creditReceipted,
        $creditAdjusted,
        $creditAllocated
    ) {
        $invoiceReceipted *= 1;
        $invoiceCredited *= 1;
        $invoiceAdjusted *= 1;
        $creditReceipted *= 1;
        $creditAdjusted *= 1;
        $creditAllocated *= 1;

        $total = $invoiceReceipted + $invoiceCredited + $invoiceAdjusted + $creditReceipted + $creditAdjusted + $creditAllocated;

        return round($total, 2);
    }
}

function dbGetInvoices($propertyID = '', $leaseID = '', $debtorID = '', $startDate = '', $endDate = '')
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $params = [];

    $conditionSQL = "(trans_type = 'INV')";
    if ($debtorID != '') {
        $conditionSQL .= ' AND (debtor_code = ' . addSQLParam($params, $debtorID) . ')';
    }

    if ($propertyID != '') {
        $conditionSQL .= ' AND ((ref_2 = ' . addSQLParam($params, $propertyID) . '))';
    }

    if ($leaseID != '') {
        $conditionSQL .= ' AND ((ref_4 = ' . addSQLParam($params, $leaseID) . '))';
    }

    $conditionSQL .= ' AND (trans_date BETWEEN CONVERT(datetime, ' . addSQLParam($params, $startDate) . ', 103)
		AND CONVERT(datetime, ' . addSQLParam($params, $endDate) . ', 103))';


    $sql = "SELECT
				COALESCE(SUM(artr_tax_amt), 0) AS taxAmount,
				debtor_code AS debtorID,
				ref_2 AS propertyID,
				ref_4 AS leaseID,
				COALESCE(SUM(trans_amt), 0) AS amount,
				COALESCE(SUM(artr_net_amt), 0) AS netAmount,
				artr_gst_inv_no as invoiceNumber,
				CONVERT(char(10), artr_gst_inv_dt, 103) as invoiceDate,
				COUNT(artr_gst_inv_no) AS lines
				FROM ar_transaction
				WHERE {$conditionSQL}
				GROUP BY debtor_code, ref_2, ref_4, artr_gst_inv_no, artr_gst_inv_dt
				 ORDER BY artr_gst_inv_no DESC";

    return $dbh->executeSet($sql, false, true, $params);
}


function dbGetOutstanding($propertyID = '', $leaseID = '', $debtorID = '', $startDate = '', $endDate = '')
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $params = [$endDate, $endDate, $endDate, $endDate, $endDate, $leaseID, $propertyID, $endDate];
    $conditionSQL = "(trans_type <> 'REV') AND (trans_type <> 'ADJ')";
    if ($debtorID != '') {
        $conditionSQL .= ' AND (debtor_code = ' . addSQLParam($params, $debtorID) . ')';
    }

    if ($propertyID != '') {
        $conditionSQL .= ' AND ((ref_2 = ' . addSQLParam($params, $propertyID) . ") OR (trans_type = 'CSH'))";
    }

    if ($leaseID != '') {
        $conditionSQL .= ' AND ((ref_4 = ' . addSQLParam($params, $leaseID) . ") OR (trans_type = 'CSH'))";
    }

    $conditionSQL .= ' AND (trans_date BETWEEN CONVERT(datetime, ' . addSQLParam(
        $params,
        $startDate
    ) . ', 103) AND CONVERT(datetime, ' . addSQLParam($params, $endDate) . ', 103))';

    $sql = "
		SELECT
			batch_nr AS batchNumber,
			batch_line_nr AS batchLineNumber,
			artr_tax_amt AS taxAmount,
			debtor_code AS debtorID,
			ref_2 AS propertyID,
			ref_3 AS accountID,
			ref_4 AS leaseID,
			description AS description,
			artr_gst_code AS taxCode,
			trans_amt AS amount,
			CONVERT(char(10), spare_date_1, 103) AS fromDate,
			CONVERT(char(10), spare_date_2, 103) AS toDate,
			artr_net_amt AS netAmount,
			CONVERT(char(10), trans_date, 103) AS transactionDate,
			artr_gst_inv_no as invoiceNumber,
			CONVERT(char(10), artr_gst_inv_dt, 103) as invoiceDate,
			trans_type AS transactionType,
			(
				SELECT
					COALESCE(SUM(pmxd_alloc_amt), 0) * - 1 AS invoiceTotalReceipted
				FROM
					pmxd_ar_alloc
				WHERE
					(pmxd_t_batch=ar_transaction.batch_nr)
					AND (pmxd_t_line=ar_transaction.batch_line_nr)
					AND (pmxd_f_type='CSH')
					AND (pmxd_t_type='INV')
					AND (pmxd_alloc_dt<=CONVERT(datetime, '{$endDate}',103))
			) AS invoiceTotalReceipted,
			(SELECT COALESCE(SUM(pmxd_alloc_amt), 0) * - 1 AS invoiceTotalCredited FROM pmxd_ar_alloc WHERE (pmxd_t_batch = ar_transaction.batch_nr) AND (pmxd_t_line = ar_transaction.batch_line_nr) AND (pmxd_f_type = 'CRE') AND (pmxd_t_type = 'INV') AND (pmxd_alloc_dt <= CONVERT(datetime, ?,103))) AS invoiceTotalCredited,
			(SELECT COALESCE(SUM(pmxd_alloc_amt), 0) * - 1 AS invoiceTotalAdjusted FROM pmxd_ar_alloc WHERE (pmxd_t_batch = ar_transaction.batch_nr) AND (pmxd_t_line = ar_transaction.batch_line_nr) AND (pmxd_f_type = 'ADJ') AND (pmxd_t_type = 'INV') AND (pmxd_alloc_dt <= CONVERT(datetime, ?,103))) AS invoiceTotalAdjusted,
			(SELECT COALESCE(SUM(pmxd_alloc_amt), 0) * - 1 AS creditTotalReceipted FROM pmxd_ar_alloc WHERE (pmxd_t_batch = ar_transaction.batch_nr) AND (pmxd_t_line = ar_transaction.batch_line_nr) AND (pmxd_f_type = 'CSH') AND (pmxd_t_type = 'CRE') AND (pmxd_alloc_dt <= CONVERT(datetime, ?,103))) AS creditTotalReceipted,
			(SELECT COALESCE(SUM(pmxd_alloc_amt), 0) AS creditTotalAdjusted FROM pmxd_ar_alloc WHERE (pmxd_f_batch = ar_transaction.batch_nr) AND (pmxd_f_line = ar_transaction.batch_line_nr) AND (pmxd_t_type = 'ADJ') AND (pmxd_f_type = 'CRE')  AND (pmxd_alloc_dt <= CONVERT(datetime, ?,103))) AS creditTotalAdjusted,
			(SELECT COALESCE(SUM(pmxd_alloc_amt), 0) AS creditTotalAllocated FROM pmxd_ar_alloc WHERE (pmxd_f_batch = ar_transaction.batch_nr) AND (pmxd_f_line = ar_transaction.batch_line_nr) AND (pmxd_t_type = 'INV') AND (pmxd_f_type = 'CRE') AND (pmxd_alloc_dt <= CONVERT(datetime, ?,103))) AS creditTotalAllocated,
			(SELECT COALESCE(SUM(pmuc_amt), 0) AS total_unallocated FROM pmuc_unall_csh WHERE (pmuc_lease=? AND pmuc_prop=? AND pmuc_unall_csh.pmuc_batch = RTRIM(ar_transaction.batch_nr)) AND (pmuc_unall_csh.pmuc_line = ar_transaction.batch_line_nr) AND (pmuc_rcpt_dt <= CONVERT(datetime, ?, 103))) AS unallocated,
			(SELECT TOP 1 pmuc_lease FROM pmuc_unall_csh WHERE (pmuc_unall_csh.pmuc_batch = RTRIM(ar_transaction.batch_nr)) AND (pmuc_unall_csh.pmuc_line = ar_transaction.batch_line_nr)) AS pmuc_lease
		FROM
			ar_transaction
		WHERE
			{$conditionSQL}
		ORDER BY
			spare_date_1 DESC,
			ref_3,
			description";
    $existing = $dbh->executeSet($sql, false, true, $params);

    $outstandingAmounts = [];
    $total = 0;
    foreach ($existing as $row) {
        $offset = getOffset(
            $row['invoiceTotalReceipted'],
            $row['invoiceTotalCredited'],
            $row['invoiceTotalAdjusted'],
            $row['creditTotalReceipted'],
            $row['creditTotalAdjusted'],
            $row['creditTotalAllocated']
        );
        $row['unallocated'] = (float) $row['unallocated'];
        $row['amount'] = (float) $row['amount'];
        if ((($row['amount'] - $offset) != 0) && ($row['transactionType'] != 'CSH')) {
            $row['unpaidAmount'] = round($row['amount'] - $offset, 2);
            $total += $row['unpaidAmount'];
            $outstandingAmounts[] = $row;
        } elseif (($row['transactionType'] == 'CSH') && ($row['unallocated'] != '') && ($row['unallocated'] != 0)) {
            $unallocated = dbGetAgedUnallocated(
                $row['propertyID'],
                $row['leaseID'],
                $row['debtorID'],
                $row['batchNumber'],
                $row['batchLineNumber'],
                $endDate
            );
            $row['unpaidAmount'] = $row['unallocated'];
            $total += ($row['unallocated'] * 1);
            $outstandingAmounts[] = $row;
        }
    }

    $sql = "
		SELECT
			batch_nr AS batchNumber,
			batch_line_nr AS batchLineNumber,
			debtor_code AS debtorID,
			ref_2 AS propertyID,
			ref_3 AS accountID,
			ref_4 AS leaseID,
			description AS description,
			artr_gst_code AS taxCode,
			CONVERT(char(10),pmxd_alloc_dt , 103) AS transactionDate,
			CONVERT(char(10), spare_date_1, 103) AS fromDate,
			CONVERT(char(10), spare_date_2, 103) AS toDate,
			pmxd_alloc_amt AS amount,
			pmxd_alloc_amt AS unpaidAmount,
			artr_gst_inv_no as invoiceNumber,
			CONVERT(char(10), artr_gst_inv_dt, 103) as invoiceDate,
			trans_type AS transactionType
		FROM
			ar_transaction,
			pmxd_ar_alloc
		WHERE
			pmxd_t_batch = batch_nr
			AND pmxd_t_line = batch_line_nr
			AND (pmxd_lease=?)
			AND ((pmxd_f_type='CSH') OR (pmxd_f_type='REV'))
			AND trans_date>CONVERT(datetime, ?, 103)
			AND (pmxd_alloc_dt <= CONVERT(datetime, ?, 103))
			AND (pmxd_prop=?)";

    $params = [$leaseID, $endDate, $endDate, $propertyID];

    $futureReceipts = $dbh->executeSet($sql, false, true, $params);
    foreach ($futureReceipts as $row) {
        $total += ($row['amount'] * 1);
        $outstandingAmounts[] = $row;
    }

    return $outstandingAmounts;
}

function dbDebtorsByDrawerList($drawerName)
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);
    mssqlString($drawerName);
    $sql = 'SELECT
	  pmqd_debtor AS debtorID,
	  pmco_name AS debtorName
	  FROM pmqd_chq_def, pmco_company, pmle_lease, pmpr_property
	  WHERE pmqd_debtor=pmle_debtor
	  AND pmle_prop = pmpr_prop
	  AND pmpr_delete = 0
	  AND pmqd_name = ?
	  AND pmco_code = pmqd_debtor
			  ';

    return $dbh->executeSet($sql, false, true, [$drawerName]);
}

function dbDebtorsByDepositList($depositName)
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);
    mssqlString($depositName);

    $sql = 'SELECT
  DISTINCT
	  pmqd_debtor AS debtorID,
	  pmco_name AS debtorName
	  FROM pmqd_eft_dep, pmco_company, pmle_lease, pmpr_property
	  WHERE pmqd_debtor=pmle_debtor
	  AND pmle_prop = pmpr_prop
	  AND pmpr_delete = 0
	  AND pmqd_name = ?
	  AND pmco_code = pmqd_debtor
			  ';

    return $dbh->executeSet($sql, false, true, [$depositName]);
}

function dbNextBillingDate($propertyID)
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);

    $sql = 'SELECT CONVERT(char(10), pmpr_m_upd_dt, 103) AS nextBillingDate
			  FROM pmpr_property
			  WHERE pmpr_prop=?';

    return $dbh->executeScalar($sql, [$propertyID]);
}

if (! function_exists('dbGetTenantDetails')) {
    function dbGetTenantDetails($leaseID, $propertyID, $debtorID)
    {
        global $dbh, $clientDB;
        $dbh->selectDatabase($clientDB);

        $sql = "SELECT pmle_lease AS leaseID,
					pmle_name AS leaseName,
					pmle_debtor AS debtorID,
					pmco_name AS debtorName,
					pmle_prop AS propertyID,
					pmpr_name AS propertyName
					FROM pmle_lease, pmpr_property, pmco_company
				WHERE
					pmle_lease = ?
					AND pmle_prop = ?
					AND pmle_debtor = ?
					AND (pmle_prop IN (
					SELECT DISTINCT pmpr_prop
					FROM pmpr_property
					WHERE (pmpr_delete <> '1')
					))
					AND pmco_code=pmle_debtor
					AND pmpr_prop=pmle_prop
				";

        return $dbh->executeSingle($sql, [$leaseID, $propertyID, $debtorID]);
    }
}

/**
 * @param  $vacantLease  boolean Optional. Whether to include Vacant Leases or not.
 *
 * @modified 2012-07-05: Added new paramenter $vacantLease as per task # 676873 [Morph]
 **/
function dbGetTenantsByDirectDebit($propertyID, $vacantLease = false, $leaseDivision = null)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $conditionalSQL = '';
    if (! $vacantLease) {
        $conditionalSQL = " AND pmle_lease.pmle_status!='L' ";
    }

    if ($leaseDivision) {
        $conditionalSQL = " AND pmle_lease.pmle_division = '{$leaseDivision}' ";
    }

    $sql = "SELECT pmle_lease AS leaseID,
			pmle_name AS leaseName,
			pmle_debtor AS debtorID,
			pmco_name AS debtorName,
			pmle_prop AS propertyID,
			pmpr_name AS propertyName,
			pmle_crn AS crn
		FROM pmle_lease, pmpr_property, pmco_company
		WHERE pmle_prop = ?
			{$conditionalSQL}
			AND pmco_code=pmle_debtor
			AND pmpr_prop=pmle_prop
			AND pmco_direct_debit = 1
			AND pmco_dir_bank = 1";

    return $dbh->executeSet($sql, false, true, [$propertyID]);
}

function dbGetNextAllocationNumber()
{
    return dbGetNextTransactionBatchNumberByType(GL_SOURCE_AR_ALLOC);
}

function dbDeleteTransaction($batchNumber, $lineNumber)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $sql = '
		DELETE FROM
			ar_transaction
		WHERE
			batch_nr=?
	  	AND batch_line_nr=?';

    return $dbh->executeNonQuery2($sql, [$batchNumber, $lineNumber]);
}

function dbDeleteAllocations($fromBatchNumber, $fromLineNumber)
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);
    $sql = 'DELETE FROM pmxd_ar_alloc
						WHERE pmxd_f_batch=?
						AND pmxd_f_line=?
						';

    return $dbh->executeNonQuery2($sql, [$fromBatchNumber, $fromLineNumber]);
}


/**
 * @param  $transaction  ID Mandatory
 * @return array
 *
 * @modified: 2017-02-10: Added year and period in the update query [Arjay] For AP Recoverable
 **/
function dbGetTransactionByTransactionId($transactionID)
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);
    $sql = 'SELECT * FROM ar_transaction WHERE transaction_id=?';

    return $dbh->executeSingle($sql, [$transactionID]);
}


/**
 * @param  array  $transaction  Mandatory. Array containing the data that needs to be updated.
 * @return array
 *
 * @modified: 2017-02-16: Added year and period in the update query [Arjay] For AP Recoverable
 **/
function dbUpdateApRecoverable($transaction, $recoveredBy)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    extract($transaction);
    $column = null;
    $null = null;
    if (isset($recoveredBy)) {
        $column = 'recovered_by=' . "'{$null}'" . ',';
    }

    $sql = "UPDATE ap_transaction
		SET {$column}ar_recovered = ?, recovered = ?
		WHERE batch_nr=? AND batch_line_nr=?";

    $params = [$jsonAr, $recovered, $batchNumber, $lineNumber];

    return $dbh->executeNonQuery2($sql, $params);
}


/**
 * @param  array  $transaction  Mandatory. Array containing the data that needs to be updated.
 * @return array
 *
 * @modified: 2012-06-13: Added year and period in the update query as per bug # 414345. [Morph]
 **/
function dbUpdateTransaction($transaction)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);

    extract($transaction);

    $fundID = dbGetPropertyFundIDFromAccount($transaction['propertyID'], $transaction['accountID']);
    $sql = 'UPDATE ar_transaction SET
				ref_3 = ' . addSQLParam($params, $accountID) . ',
				description = ' . addSQLParam($params, $description) . ',
				trans_date = CONVERT(datetime, ' . addSQLParam($params, $transactionDate) . ', 103),
				spare_date_1 = CONVERT(datetime, ' . addSQLParam($params, $fromDate) . ', 103),
				spare_date_2 = CONVERT(datetime, ' . addSQLParam($params, $toDate) . ', 103),
				artr_gst_code = ' . addSQLParam($params, $taxCode) . ',
				artr_tax_amt = ' . addSQLParam($params, $taxAmount) . ',
				artr_net_amt = ' . addSQLParam($params, $netAmount) . ',
				artr_mod_user = ' . addSQLParam($params, $username) . ',
				trans_amt = ' . addSQLParam($params, $transactionAmount) . ',
				artr_mod_date = GETDATE(),
				artr_mod_time = GETDATE(),
				artr_year = ' . addSQLParam($params, $year) . ',
				artr_period = ' . addSQLParam($params, $period) . ',
				fund = ' . addSQLParam($params, $fundID) . ',
				due_date = CONVERT(datetime, ' . addSQLParam($params, $dueDate) . ', 103)
			WHERE batch_nr = ' . addSQLParam($params, $batchNumber) . '
			AND batch_line_nr = ' . addSQLParam($params, $lineNumber);

    return $dbh->executeNonQuery2($sql, $params, true);
}


function dbFinaliseTempTransaction(
    $propertyID,
    $leaseID,
    $batchNumber,
    $invoiceNumber = null,
    $transactionAmount = null
) {
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);
    $params = [];

    $sql = 'UPDATE temp_ar_transaction
				SET transactionStatus=1,
				batchNumber=' . addSQLParam($params, $batchNumber) . '
			' . ($invoiceNumber ? ', invoiceNumber = ' . addSQLParam(
        $params,
        $invoiceNumber
    ) . ', invoiceDate = GETDATE()' : '') . '
			WHERE propertyID=' . addSQLParam($params, $propertyID) . '
			AND leaseID=' . addSQLParam($params, $leaseID) . '
			AND transactionStatus=0';

    $sql .= ($transactionAmount ? ' and transactionAmount = ' . addSQLParam($params, $transactionAmount) : '');
    $dbh->executeNonQuery2($sql, $params);
}

function dbStatusTempTransaction($batchNumber, $status, $approvalNote = '')
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $params = [];
    $sql = 'UPDATE
			temp_ar_transaction
		SET
			transactionStatus=' . addSQLParam($params, $status) . ',
			modifyUser=' . addSQLParam($params, $_SESSION['un']) . ',
			modifyDate=GETDATE(),
			approvalNote = ' . addSQLParam($params, $approvalNote) . '
		WHERE
			batchNumber=' . addSQLParam($params, $batchNumber);
    $dbh->executeNonQuery2($sql, $params);
}

function dbGetStandingBatch($batchID)
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);
    $sql = 'SELECT
								CONVERT(char(10),batchDate, 103) AS batchDate,
								description,
								CONVERT(char(10),fromDate, 103) AS fromDate,
								CONVERT(char(10),toDate, 103) AS toDate,
								notifyUser,
								createUser,
								CONVERT(char(10),createDate, 103) AS createDate,
								frequency,
       							processStatus
				FROM temp_standing_batch
				WHERE batchID=?
				';

    return $dbh->executeSingle($sql, [$batchID]);
}

function dbInsertStandingBatch($fromDate, $toDate, $description, $user, $createUser, $frequency)
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);
    $params = [];
    $sql = 'INSERT INTO temp_standing_batch (batchDate, description, fromDate, toDate, notifyUser, createUser, createDate, frequency) VALUES (
				GETDATE(),
				' . addSQLParam($params, $description) . ',
				CONVERT(datetime,' . addSQLParam($params, $fromDate) . ',103),
				CONVERT(datetime,' . addSQLParam($params, $toDate) . ',103),
				' . addSQLParam($params, $user) . ',
				' . addSQLParam($params, $createUser) . ',
				GETDATE(),
				' . addSQLParam($params, $frequency) . '
			   )
				';
    $query = "SET NOCOUNT ON {$sql} SELECT @@IDENTITY AS NewID SET NOCOUNT OFF";

    return $dbh->executeScalar($query, $params);
}

function dbUpdateStandingBatch($batchID, $details = [])
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);
    $params = [];
    $sql = 'UPDATE temp_standing_batch SET ';
    $sets = '';
    foreach ($details as $key => $val) {
        $sets .= $key . ' = ' . addSQLParam($params, $val) . ',';
    }

    $sets = substr($sets, 0, -1);
    $sql .= $sets . ' WHERE batchID = ' . addSQLParam($params, $batchID);
    $dbh->executeNonQuery2($sql, $params);
}

function dbDeleteStandingBatch($batchID)
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);
    $sql = 'DELETE FROM temp_standing_batch WHERE batchID=?';
    $dbh->executeNonQuery2($sql, [$batchID]);
}

function dbInsertStandingReport($batchID, $propertyID, $leaseID, $accountID, $description, $note)
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);
    $sql = 'INSERT INTO temp_standing_report (batchID, propertyID, leaseID, accountID, description, note) VALUES (
				?,?,?,?,?,?)';
    $query = "SET NOCOUNT ON {$sql} SELECT @@IDENTITY AS NewID SET NOCOUNT OFF";
    $params = [
        $batchID,
        $propertyID,
        $leaseID,
        $accountID,
        $description,
        $note,
    ];

    return $dbh->executeScalar($query, $params);
}

function dbUpdateChargeDates($propertyID, $leaseID, $unitID, $accountID, $nextDate, $lastDate, $frequency, $serial = 0)
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);
    $params = [];


    $sql = 'UPDATE pmlc_l_charge SET pmlc_next_dt = CONVERT(datetime, ' . addSQLParam($params, $nextDate) . ', 103),
				pmlc_last_dt = CONVERT(datetime, ' . addSQLParam($params, $lastDate) . ', 103)
			WHERE pmlc_prop = ' . addSQLParam($params, $propertyID) . '
			AND pmlc_lease = ' . addSQLParam($params, $leaseID) . '
			AND pmlc_acc = ' . addSQLParam($params, $accountID) . '
			AND pmlc_freq = ' . addSQLParam($params, $frequency) . '
			' . ($serial != 0 ? ' AND pmlc_serial = ' . addSQLParam($params, $serial) : '');

    return $dbh->executeNonQuery2($sql, $params);
}


function dbInsertStandingCharge($transaction)
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);
    $params = [];
    extract($transaction);
    $sql = 'INSERT INTO temp_standing_charges (
				debtorID,
				transactionDate,
				transactionType,
				bankID,
				propertyID,
				accountID,
				leaseID,
				unitID,
				description,
				transactionAmount,
				fromDate,
				toDate,
				taxCode,
				taxAmount,
				netAmount,
				transactionYear,
				transactionPeriod,
				frequency,
				batchID,
				reportID,
				partial,
				sequence,
				serial)
			VALUES (
				' . addSQLParam($params, $debtorID) . ',
				CONVERT(datetime, ' . addSQLParam($params, $transactionDate) . ', 103),
				' . addSQLParam($params, $transactionType) . ',
				' . addSQLParam($params, $bankID) . ',
				' . addSQLParam($params, $propertyID) . ',
				' . addSQLParam($params, $accountID) . ',
				' . addSQLParam($params, $leaseID) . ',
				' . addSQLParam($params, $unitID) . ',
				' . addSQLParam($params, $description) . ',
				CONVERT(money, ' . addSQLParam($params, $transactionAmount) . '),
				CONVERT(datetime, ' . addSQLParam($params, $fromDate) . ', 103),
				CONVERT(datetime,' . addSQLParam($params, $toDate) . ', 103),
				' . addSQLParam($params, $taxCode) . ',
				CONVERT(money, ' . addSQLParam($params, $taxAmount) . '),
				CONVERT(money, ' . addSQLParam($params, $netAmount) . '),
				' . addSQLParam($params, $transactionYear) . ',
				' . addSQLParam($params, $transactionPeriod) . ',
				' . addSQLParam($params, $frequency) . ',
				' . addSQLParam($params, $batchID) . ',
				' . addSQLParam($params, $reportID) . ',
				' . addSQLParam($params, $partial) . ',
				' . addSQLParam($params, $sequence) . ',
				' . addSQLParam($params, $serial) . '
				)
				 ';

    return $dbh->executeNonQuery2($sql, $params);
}

function dbGetCurrentStandingCharges()
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);
    $sql = 'SELECT description,
	CONVERT(char(10), batchDate, 103) AS batchDate,
	CONVERT(char(10), fromDate, 103) AS fromDate,
	CONVERT(char(10), toDate, 103) AS toDate,
	RTRIM(createUser) as createdBy,
	batchDate AS oDate,
	batchID,
	processStatus,
	notifyUser,
	comments from
	temp_standing_batch
	ORDER BY processStatus ASC, oDate DESC';

    return $dbh->executeSet($sql);
}

function dbGetListStandingCharges($batchID = null, $fromDate = null, $toDate = null, $status = null)
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);
    $params = [];
    $where = 'WHERE batchID IS NOT NULL ';
    if ($batchID != null) {
        $where .= ' AND batchID = ' . addSQLParam($params, $batchID);
    }

    if ($fromDate != null) {
        $where .= ' AND fromDate >= CONVERT(datetime, ' . addSQLParam($params, $fromDate) . ', 103) ';
    }

    if ($toDate != null) {
        $where .= ' AND toDate <= CONVERT(datetime, ' . addSQLParam($params, $toDate) . ', 103) ';
    }

    if ($status != null) {
        if (is_array($status)) {
            $where .= ' AND processStatus in (' . addSQLParam($params, $status) . ') ';
        } else {
            $where .= ' AND processStatus = ' . addSQLParam($params, $status);
        }
    }

    $sql = "SELECT
				description,
				CONVERT(char(10), batchDate, 103) AS batchDate,
				CONVERT(char(10), fromDate, 103) AS fromDate,
				CONVERT(char(10), toDate, 103) AS toDate,
				RTRIM(createUser) as createdBy,
				batchDate AS oDate,
				batchID,
				processStatus,
				notifyUser,
				comments
			FROM
				temp_standing_batch
			{$where}
			ORDER BY processStatus ASC,
					 oDate DESC";

    return $dbh->executeSet($sql, false, true, $params);
}

function dbGetTransferJournalHistory($fromDate = null, $toDate = null, $fromPropertyID = null, $toPropertyID = null)
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);
    $params = [];
    $where = "WHERE tjh_source = 'ar' AND tjh_trans_date between CONVERT(datetime, " . addSQLParam(
        $params,
        $fromDate
    ) . ', 103) and CONVERT(datetime, ' . addSQLParam($params, $toDate) . ', 103)';

    if ($fromPropertyID != '') {
        $where .= ' AND tjh_from_prop = ' . addSQLParam($params, $fromPropertyID);
    }

    if ($toPropertyID != '') {
        $where .= ' AND tjh_to_prop = ' . addSQLParam($params, $toPropertyID);
    }


    $sql = "SELECT
				CONVERT(char(10), tjh_trans_date, 103) AS transDate,
				concat(RTRIM(fp.pmpr_prop) , ' - ' , fp.pmpr_name) as fromProperty,
				concat(RTRIM(tp.pmpr_prop) , ' - ' , tp.pmpr_name) as toProperty,
				tjh_from_prop as fromPropertyID,
				tjh_to_prop as toPropertyID,
				tjh_from_lease as fromLeaseID,
				tjh_to_lease as toLeaseID,
				tjh_from_account as fromAccountID,
				tjh_to_account as toAccountID,
				tjh_from_desc as fromDesc,
				tjh_to_desc as toDesc,
				CONVERT(char(10), tjh_from_fdate, 103) AS fromFromDate,
				CONVERT(char(10), tjh_from_tdate, 103) AS toFromDate,
				CONVERT(char(10), tjh_to_fdate, 103) AS fromToDate,
				CONVERT(char(10), tjh_to_tdate, 103) AS toToDate,
				tjh_from_tax as fromTaxRateID,
				tjh_to_tax as toTaxRateID,
				tjh_from_amount as transactionAmount,
				tjh_created_by as createdBy,
				CONVERT(char(10), tjh_created_date, 103) AS createDate
			FROM
				tj_history
			JOIN pmpr_property fp on fp.pmpr_prop =  tjh_from_prop
			JOIN pmpr_property tp on tp.pmpr_prop =  tjh_to_prop
			{$where}
			ORDER BY tjh_trans_date desc";

    return $dbh->executeSet($sql, false, true, $params);
}

/**
 * @todo One function that summarises each tenants invoice, one which returns each individual line of the invoice
 *
 * @modified 2012-06-22: Added lastTransactionAmount in the selection. Used LEFT JOIN. [Morph]
 **/
function dbGetPropertyTotalsForBatch($batchID, $onlyChanged = false)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $changeSQL = '';
    if ($onlyChanged) {
        $changeSQL = ' AND lastAmount != transactionAmount';
    }

    $sql = "SELECT
			propertyID,
			pmpr_name AS propertyName,
			COALESCE(SUM(transactionAmount), 0) as transactionAmount,
			COALESCE(SUM(netAmount), 0) AS netAmount,
			COALESCE(SUM(taxAmount), 0) as taxAmount,
			COALESCE(SUM(lastAmount), 0) as lastTransactionAmount
		FROM v_standing_charges LEFT JOIN pmpr_property ON (propertyID=pmpr_prop)
		WHERE
			batchID=?
			 {$changeSQL}
		GROUP BY propertyID, pmpr_name
		ORDER BY propertyID";

    return $dbh->executeSet($sql, false, true, [$batchID]);
}

function dbGetPropertiesForBatch($batchID)
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);
    $sql = 'SELECT DISTINCT propertyID,  pmpr_name AS propertyName, CONVERT(char(10), pmpr_m_upd_dt, 103) AS propertyDate
			FROM v_standing_charges, pmpr_property WHERE batchID=? AND propertyID=pmpr_prop ORDER BY propertyID';

    return $dbh->executeSet($sql, false, true, [$batchID]);
}

function dbUpdateNextBillingDate($date, $propertyID)
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);
    $sql = 'UPDATE pmpr_property SET pmpr_m_upd_dt = CONVERT(datetime, ?, 103) WHERE pmpr_prop = ?';
    $dbh->executeNonQuery2($sql, [$date, $propertyID]);
}

/**
 * @return array
 *
 * @modified 2012-05-23: Added new sum total column lastTransactionAmount [Morph]
 **/
function dbGetStandingChargeSummaryForBatch($batchID, $propertyID, $onlyChanged = false)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $params = [];
    $changeSQL = '';
    if ($onlyChanged) {
        $changeSQL = ' AND lastAmount != transactionAmount';
    }

    $sql = 'SELECT DISTINCT
		propertyID,
		leaseID,
		debtorID,
		unitID,
		pmle_description AS leaseName,
		COALESCE(SUM(transactionAmount), 0) as transactionAmount,
		COALESCE(SUM(lastAmount), 0) as lastTransactionAmount,
		COALESCE(SUM(netAmount), 0) AS netAmount,
		COALESCE(SUM(taxAmount), 0) as taxAmount,
		processStatus,
		sequence,
		pmle_t_name AS tenantName
		FROM
			v_standing_charges,
			pmle_lease
			WHERE
			pmle_lease=leaseID
			AND pmle_prop=' . addSQLParam($params, $propertyID) . '
			AND propertyID=' . addSQLParam($params, $propertyID) . '
			AND batchID=' . addSQLParam($params, $batchID) . "
			{$changeSQL}
		GROUP BY propertyID, unitID, leaseID, debtorID, pmle_description, processStatus, sequence, pmle_t_name
		ORDER BY propertyID, unitID, leaseID ASC";

    return $dbh->executeSet($sql, false, true, $params);
}

function dbGetStandingChargeDetailsForBatch($batchID, $propertyID, $leaseID, $onlyChanged = false)
{
    global $clientDB, $dbh;
    $changeSQL = '';
    if ($onlyChanged) {
        $changeSQL = ' AND lastAmount != transactionAmount';
    }

    $dbh->selectDatabase($clientDB);
    $sql = "SELECT
		* FROM
		v_standing_charges s
		WHERE s.batchID=? AND s.propertyID=? AND s.leaseID=?
		{$changeSQL}";

    return $dbh->executeSet($sql, false, true, [$batchID, $propertyID, $leaseID]);
}

function dbGetAllStandingChargesForBatch($batchID, $onlyChanged = false, $noRejected = false)
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);
    $params = [];
    $changeSQL = '';
    if ($onlyChanged) {
        $changeSQL .= ' AND lastAmount != transactionAmount ';
    }

    if ($noRejected) {
        $changeSQL .= ' AND processStatus = 0 ';
    }

    if (is_array($batchID)) {
        $where = ' WHERE batchID IN(' . addSQLParam($params, $batchID) . ') ';
    } else {
        $where = ' WHERE batchID=' . addSQLParam($params, $batchID);
    }

    $sql = "SELECT
				 debtorID,
				 transactionDate,
				 transactionType,
				 bankID,
				 propertyID,
				 accountID,
				 leaseID,
				 unitID,
				 description,
				 transactionAmount,
				 fromDate,
				 toDate,
				 taxCode,
				 taxAmount,
				 netAmount,
				 transactionYear,
				 transactionPeriod,
				 frequency,
				 batchID,
				 processStatus,
       			 serial
	from v_standing_charges
	{$where}
	{$changeSQL}
	";

    return $dbh->executeSet($sql, false, true, $params);
}

function dbToggleStandingCharge($transactionID)
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);
    $sql = 'UPDATE temp_standing_charges SET processStatus = (1-processStatus) WHERE transactionID = ?';

    return $dbh->executeNonQuery2($sql, [$transactionID]);
}

function dbRejectChargeLines($batchID, $propertyID, $leaseID, $comments)
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);
    $params = [];
    $sql = 'UPDATE temp_standing_charges
			SET
				processStatus = 1,
				comments = ' . addSQLParam($params, $comments) . '
			WHERE batchID = ' . addSQLParam($params, $batchID) . '
			AND  leaseID = ' . addSQLParam($params, $leaseID) . '
			AND propertyID =' . addSQLParam($params, $propertyID);

    return $dbh->executeNonQuery2($sql, $params);
}

function dbCountUnapprovedCharges($batchID)
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);
    $sql = 'SELECT COUNT(processStatus) FROM temp_standing_charges WHERE batchID=? AND processStatus=1';

    return $dbh->executeScalar($sql, [$batchID]);
}

function dbModifyStatus($batchID, $comments = '', $status = 0)
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);
    $sql = 'UPDATE temp_standing_batch SET processStatus = ?, comments=? WHERE  batchID = ?';
    $dbh->executeNonQuery2($sql, [$status, $comments, $batchID]);
}

function dbDeleteStandingChargeBatch($batchID)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $sql = 'DELETE FROM temp_standing_batch WHERE  batchID = ?';
    $dbh->executeNonQuery2($sql, [$batchID]);
}

function dbInsertTempReceivableTransaction($transaction)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $params = [];
    extract($transaction);
    $sql = '
		INSERT INTO
			temp_ar_transaction
			(
				debtorID,
				transactionDate,
				transactionType,
				bankID,
				propertyID,
				accountID,
				leaseID,
				unitID,
				description,
				transactionAmount,
				fromDate,
				toDate,
				taxCode,
				taxAmount,
				netAmount,
				invoiceNumber,
				invoiceDate,
				createUser,
				createDate,
				transactionYear,
				transactionPeriod,
				transactionStatus,
				batchNumber,
				transactionSource,
				toBatchNumber,
				toLineNumber,
				dueDate,
				ap_recovered,
				apRecoveredInvoiceNo,
				apRecoveredSupplier
			)
			VALUES
			(
				' . addSQLParam($params, $debtorID) . ",
				CONVERT(datetime, '" . $transactionDate . "', 103),
				" . addSQLParam($params, $transactionType) . ',
				' . addSQLParam($params, $bankID) . ',
				' . addSQLParam($params, $propertyID) . ',
				' . addSQLParam($params, $accountID) . ',
				' . addSQLParam($params, $leaseID) . ',
				' . addSQLParam($params, $unitID) . ',
				' . addSQLParam($params, $description) . ',
				CONVERT(money, ' . addSQLParam($params, $transactionAmount) . '),
				CONVERT(datetime, ' . addSQLParam($params, $fromDate) . ', 103),
				CONVERT(datetime, ' . addSQLParam($params, $toDate) . ', 103),
                ' . addSQLParam($params, $taxCode) . ',
				CONVERT(money, ' . addSQLParam($params, $taxAmount) . '),
				CONVERT(money, ' . addSQLParam($params, $netAmount) . '),
				' . addSQLParam($params, $invoiceNumber) . ',
				CONVERT(datetime, ' . addSQLParam($params, $invoiceDate) . ',103),
				' . addSQLParam($params, $createUser) . ',
				GETDATE(),
				' . addSQLParam($params, $transactionYear) . ',
				' . addSQLParam($params, $transactionPeriod) . ',
				' . addSQLParam($params, $transactionStatus) . ',
				' . addSQLParam($params, $batchNumber) . ',
				' . addSQLParam($params, $source) . ',
				' . addSQLParam($params, $toBatchNumber) . ',
				' . addSQLParam($params, $toLineNumber) . ',
				CONVERT(datetime, ' . addSQLParam($params, $dueDate) . ', 103),
				' . addSQLParam($params, $jsonAr) . ',
				' . addSQLParam($params, $apInvoiceNo) . ',
				' . addSQLParam($params, $apSupplierCode) . '
			)';

    return $dbh->executeNonQuery2($sql, $params, true);
}

function dbGetTempTransactions($propertyID, $leaseID, $transactionAmount = null)
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);
    $sql = 'SELECT
				 transactionID,
				 debtorID,
				 CONVERT(char(10), transactionDate, 103) AS transactionDate,
				 transactionType,
				 bankID,
				 propertyID,
				 accountID,
				 leaseID,
				 unitID,
				 description,
				 transactionAmount,
				 CONVERT(char(10), fromDate, 103) AS fromDate,
				 CONVERT(char(10), toDate, 103) AS toDate,
				 taxCode,
				 taxAmount,
				 netAmount,
				 invoiceNumber,
				 CONVERT(char(10), invoiceDate, 103) AS invoiceDate,
				 createUser,
				 CONVERT(char(10), createDate, 103) AS createDate,
				 transactionYear,
				 transactionPeriod
				 FROM temp_ar_transaction WHERE propertyID=? AND leaseID=? AND transactionStatus=0';

    $params = [$propertyID, $leaseID];
    $sql .= ($transactionAmount ? ' and transactionAmount = ' . addSQLParam($params, $transactionAmount) : '');

    return $dbh->executeSet($sql, false, true, $params);
}

function dbGetTempTransaction($transactionID)
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);
    $sql = 'SELECT * FROM temp_ar_transaction WHERE transactionID=?';

    return $dbh->executeSingle($sql, [$transactionID]);
}

function dbGetHeaderDetails($propertyID, $leaseID)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    /** 2012-06-11: Same thing as the one below but with LEFT JOIN [Morph]
     * $sql = "SELECT pmpr_name AS propertyName, pmco_name AS ownerName, pmco_gst_no AS ownerABN, pmle_name AS leaseName  FROM pmpr_property, pmle_lease, pmco_company WHERE pmpr_prop = '{$propertyID}' AND pmle_prop=pmpr_prop AND pmle_lease='{$leaseID}' AND pmco_code=pmpr_owner";
     **/
    $sql = 'SELECT
            pmpr_is_ledger AS is_ledger,
			pmpr_name AS propertyName,
			pmco_name AS ownerName,
			pmco_gst_no AS ownerABN,
			pmle_name AS leaseName,
			pmco_country AS ownerCountry
		FROM pmpr_property t1
			LEFT JOIN pmle_lease t2 ON (pmle_prop=pmpr_prop)
			LEFT JOIN pmco_company t3 ON (pmco_code=pmpr_owner)
		WHERE pmpr_prop = ?
			AND pmle_lease=?';

    return $dbh->executeSingle($sql, [$propertyID, $leaseID]);
}

function dbGetLeaseMailingAddress($propertyID, $leaseID)
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);

    $sql = 'SELECT
			  pmle_t_name AS mailingName,
			  pmle_street AS mailingAddress,
			  pmle_city AS mailingCity,
			  pmle_state AS mailingState,
			  pmle_country AS mailingCountry,
			  pmle_postcode AS mailingPostCode
			  FROM pmle_lease
			  WHERE pmle_prop=?
			  AND pmle_lease=?';

    return $dbh->executeSingle($sql, [$propertyID, $leaseID]);
}

function dbGetDebtorEmail($propertyID, $leaseID)
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);
    $sql = 'SELECT pmco_email AS email FROM  pmco_company, pmle_lease WHERE (pmle_prop = ?) AND (pmle_lease = ?) AND pmco_code=pmle_debtor';

    return $dbh->executeScalar($sql, [$propertyID, $leaseID]);
}

function dbGetTransactionDetailsByBatch($batchNumber)
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);
    $sql = 'SELECT batch_line_nr AS lineNumber,
		batch_nr as batchNumber,
		trans_type AS transactionType,
				CONVERT(char(10), trans_date, 103) AS transactionDate,
				debtor_code AS debtorID,
		ref_4 AS leaseID,
		ref_3 AS accountID,
		ref_2 AS propertyID,
				pmca_name AS accountName,
				trans_amt AS transactionAmount,
		artr_tax_amt AS taxAmount,
				description,
		(SELECT pmxd_t_batch FROM pmxd_ar_alloc WHERE pmxd_f_batch = batch_nr AND pmxd_f_line = batch_line_nr) AS toBatchNumber,
		(SELECT pmxd_t_line FROM pmxd_ar_alloc WHERE pmxd_f_batch = batch_nr AND pmxd_f_line = batch_line_nr) AS toLineNumber
		FROM ar_transaction, pmca_chart
				WHERE (batch_nr = ?)
				AND pmca_code=ref_3
				ORDER BY batch_line_nr ASC';

    return $dbh->executeSet($sql, false, true, [$batchNumber]);
}

function dbDeleteTransactionByBatch($batchNumber, $lineNumber)
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);
    $sql = 'DELETE
				 FROM ar_transaction WHERE batch_nr=? AND batch_line_nr=?';

    return $dbh->executeNonQuery2($sql, [$batchNumber, $lineNumber]);
}

function dbGetTransactionByBatch($batchNumber, $lineNumber)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $sql = '
		SELECT
			debtor_code AS debtorID,
			CONVERT(char(10), trans_date, 103) AS transactionDate,
			CONVERT(char(10), due_date, 103) AS dueDate,
			trans_type AS transactionType,
			payment_type AS paymentType,
			ref_1 AS reference,
			bank AS bankID,
			ref_2 AS propertyID,
			ref_3 AS accountID,
			ref_4 AS leaseID,
			ref_5 AS unitID,
			description,
			trans_amt AS transactionAmount,
			trans_amt AS old_transactionAmount,
			CONVERT(char(10), spare_date_1, 103) AS fromDate,
			CONVERT(char(10), spare_date_2, 103) AS toDate,
			spare_1 AS receiptNumber,
			artr_gst_code AS taxCode,
			artr_tax_amt AS taxAmount,
			artr_net_amt AS netAmount,
			artr_tax_amt AS old_taxAmount,
			artr_net_amt AS old_netAmount,
			artr_gst_inv_no AS invoiceNumber,
			CONVERT(char(10), artr_gst_inv_dt, 103) AS invoiceDate,
			artr_create_user AS  createUser,
			CONVERT(char(10), artr_create_date, 103) AS createDate,
            CONVERT(varchar, artr_create_time, 24) AS createTime,
			artr_mod_user as modifiedUser,
			CONVERT(char(10), artr_mod_date, 103) AS modifiedDate,
			CONVERT(varchar, artr_mod_time, 24) AS modifiedTime,
			artr_year AS transactionYear,
			artr_period AS transactionPeriod,
			apRecoveredInvoiceNo,
			apRecoveredSupplier,
			ap_recovered,
			(
				SELECT TOP 1
					pmcp_closed
				FROM
					pmcp_prop_cal
				WHERE
					pmcp_prop = ref_2
					AND pmcp_start_dt <= trans_date
					AND pmcp_end_dt >= trans_date
			) AS closed,
			pmpr_is_ledger AS is_ledger,
			( SELECT COALESCE(SUM(pmxd_alloc_amt), 0) FROM  pmxd_ar_alloc WHERE batch_nr = pmxd_t_batch and batch_line_nr = pmxd_t_line AND pmxd_prop = ref_2 AND pmxd_lease = ref_4  ) AS allocated
		FROM
			ar_transaction
		JOIN pmpr_property on pmpr_prop = ref_2
		WHERE
			batch_nr=?
			AND batch_line_nr = ?';

    return $dbh->executeSingle($sql, [$batchNumber, $lineNumber]);
}

function dbGetTransactionByBatch_ForCancelling($batchNumber, $lineNumber)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $sql = '
		SELECT
			debtor_code AS debtorID,
			CONVERT(char(10), trans_date, 103) AS transactionDate,
			CONVERT(char(10), due_date, 103) AS dueDate,
			trans_type AS transactionType,
			trans_amt AS trans_amount,
			payment_type AS paymentType,
			ref_1 AS reference,
			bank AS bankID,
			ref_2 AS propertyID,
			ref_3 AS accountID,
			ref_4 AS leaseID,
			ref_5 AS unitID,
			description,
			COALESCE(SUM(pmxd_alloc_amt), 0) AS transactionAmount,
			CONVERT(char(10), spare_date_1, 103) AS fromDate,
			CONVERT(char(10), spare_date_2, 103) AS toDate,
			spare_1 AS receiptNumber,
			artr_gst_code AS taxCode,
			artr_tax_amt AS taxAmount,
			artr_net_amt AS netAmount,
			artr_gst_inv_no AS invoiceNumber,
			CONVERT(char(10), artr_gst_inv_dt, 103) AS invoiceDate,
			artr_create_user AS  createUser,
			CONVERT(char(10), artr_create_date, 103) AS createDate,
			artr_mod_user as modifiedUser,
			CONVERT(char(10), artr_mod_date, 103) AS modifiedDate,
			artr_year AS transactionYear,
			artr_period AS transactionPeriod,
			apRecoveredInvoiceNo,
			apRecoveredSupplier,
			ap_recovered,
			(
				SELECT TOP 1
					pmcp_closed
				FROM
					pmcp_prop_cal
				WHERE
					pmcp_prop = ref_2
					AND pmcp_start_dt <= trans_date
					AND pmcp_end_dt >= trans_date
			) AS closed
		FROM
			ar_transaction
		LEFT JOIN pmxd_ar_alloc ON batch_nr = pmxd_f_batch and batch_line_nr = pmxd_f_line
		WHERE
			batch_nr=?
			AND batch_line_nr = ?
			GROUP BY debtor_code , trans_date , due_date , trans_type , payment_type , ref_1 , bank , ref_2 , ref_3
		,ref_4 , ref_5 , description , trans_amt , spare_date_1 , spare_date_2 , spare_1 , artr_gst_code , artr_tax_amt , artr_net_amt
		, artr_gst_inv_no , artr_gst_inv_dt , artr_create_user , artr_create_date , artr_mod_user , artr_mod_date , artr_year , artr_period,
			apRecoveredInvoiceNo, apRecoveredSupplier,ap_recovered';

    return $dbh->executeSingle($sql, [$batchNumber, $lineNumber]);
}

function dbPaymentMethod($companyID)
{
    global $dbh, $clientDB;
    $dbh->selectDatabase($clientDB);
    $sql = 'SELECT pmco_pay_method FROM pmco_company WHERE pmco_code = ?';

    return $dbh->executeScalar($sql, [$companyID]);
}

function dbAddBankRecForEFT($transaction)
{
    global $dbh, $clientDB;
    $dbh->selectDatabase($clientDB);
    $params = [];
    $cancelValue_params = [];

    extract($transaction);
    if (! isset($pmbg_cancel_reference)) {
        $pmbg_cancel_reference = 0;
    } elseif ($pmbg_cancel_reference == '') {
        $pmbg_cancel_reference = 0;
    }

    $cancelInsert = '';
    $cancelValue = '';
    if (isset($cancelledBy)) {
        $cancelInsert = 'pmbg_cancel_by , pmbg_cancel_date,pmbg_cancel_reason,';

        $cancelValue = '' . addSQLParam($cancelValue_params, $cancelledBy) . ',
         ' . addSQLParam($cancelValue_params, $cancelledTime) . ',
         ' . addSQLParam($cancelValue_params, $cancelReason) . ',';
    }

    $sql = "INSERT INTO pmbg_dir_dep
			( {$cancelInsert} pmbg_bank,
			pmbg_userref,
			pmbg_ref,
			pmbg_pval,
			pmbg_pdate,
			pmbg_ppval,
			pmbg_ppdate,
			pmbg_reco,
			pmbg_cpval,
			pmbg_cpdate,
			pmbg_cppval,
			pmbg_cppdate,
			pmbg_creco,
			foreign_pmbg_pval,
			foreign_pmbg_cpval,
			pmbg_cancel_reference,
			pmbg_tenant_refund
			)
		VALUES
			( {$cancelValue} " . addSQLParam($params, $cancelValue_params, false) . '
			' . addSQLParam($params, $bank) . ',
			' . addSQLParam($params, $referenceNumber) . ',
			' . addSQLParam($params, $batchNumber) . ',
			' . addSQLParam($params, $amount) . ',
			CONVERT(datetime, ' . addSQLParam($params, $batchDate) . ", 103),
			'0',
			NULL,
			NULL,
			'0',
			NULL,
			'0',
			NULL,
			NULL,
			NULL,
			NULL,
			{$pmbg_cancel_reference},
			{$pmbg_cancel_refund}
			)";

    return $dbh->executeNonQuery2($sql, $params, true);
}

function dbRefundCheque($transaction)
{
    global $dbh, $clientDB;
    $dbh->selectDatabase($clientDB);
    $params = [];
    extract($transaction);

    $sql = '
				UPDATE pmbc_reco_chq SET
		    pmbc_cpval 		= ' . addSQLParam($params, $transactionAmount + 0) . ',
			pmbc_cpdate 	= CONVERT(datetime,' . addSQLParam($params, $transactionDate) . ',103),
			pmbc_cancel_by = ' . addSQLParam($params, $cancelledBy) . ',
			pmbc_cancel_date = ' . addSQLParam($params, $cancelledTime) . ',
			pmbc_cancel_reason = ' . addSQLParam($params, $cancelReason) . '
		WHERE (pmbc_bank = ' . addSQLParam($params, $bankID) . ') AND (pmbc_creditor = ' . addSQLParam(
        $params,
        $debtorID
    ) . ') AND (pmbc_chq=' . addSQLParam($params, $reference) . ')';

    return $dbh->executeNonQuery2($sql, $params, true);
}

function dbGetEFT($bankID, $referenceNumber)
{
    global $dbh, $clientDB;
    $dbh->selectDatabase($clientDB);
    $sql = '
						SELECT
						pmbg_bank AS bank,
			pmbg_userref AS referenceNumber
						FROM pmbg_dir_dep
			WHERE
						pmbg_ref = ?
						AND pmbg_pval > 0
						AND pmbg_bank = ?
			ORDER BY
						pmbg_userref DESC
						';

    return $dbh->executeSingle($sql, [$referenceNumber, $bankID]);
}

function dbGetStandingChargeForBatch($batchID)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $sql = '
		SELECT
			notifyUser,
			frequency,
			description, CONVERT(char(10), batchDate, 103) AS batchDate,
			CONVERT(char(10), fromDate, 103) AS fromDate,
			CONVERT(char(10), toDate, 103) AS toDate,
			batchID,
			processStatus,
			comments
		FROM
			temp_standing_batch
		WHERE
			batchID=?
		ORDER BY
			fromDate ASC';

    return $dbh->executeSingle($sql, [$batchID]);
}

function dbGetGrandTotalForBatch($batchID)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $sql = 'SELECT COALESCE(SUM(transactionAmount), 0) as transactionAmount, COALESCE(SUM(netAmount), 0) AS netAmount, COALESCE(SUM(taxAmount), 0) as taxAmount from temp_standing_charges WHERE batchID=?';

    return $dbh->executeSingle($sql, [$batchID]);
}

function dbGetNewReceipts($batchNumber, $lineNumber, $allocationDate = null)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $params = [];
    $sql = '
		SELECT
			batch_nr AS batchNumber,
			batch_line_nr AS batchLineNumber,
			artr_tax_amt AS taxAmount,
			debtor_code AS debtorID,
			ref_2 AS propertyID,
			ref_3 AS accountID,
			ref_4 AS leaseID,
			description AS description,
			artr_gst_code AS taxCode,
			trans_amt AS amount,
			artr_net_amt AS netAmount,
			CONVERT(char(10), pmxd_alloc_dt, 103) AS transactionDate,
			artr_gst_inv_no as invoiceNumber,
			CONVERT(char(10), artr_gst_inv_dt, 103) as invoiceDate,
			trans_type AS transactionType,
			CONVERT(char(10), spare_date_1, 003) AS fromDate,
			CONVERT(char(10), spare_date_2, 003) AS toDate
		FROM ar_transaction, pmxd_ar_alloc
				WHERE
		batch_nr=' . addSQLParam($params, $batchNumber) . '
		AND batch_line_nr=' . addSQLParam($params, $lineNumber) . '
		AND ((pmxd_t_batch = batch_nr
		AND pmxd_t_line = batch_line_nr) OR (pmxd_f_batch = batch_nr
		AND pmxd_f_line = batch_line_nr))
		' . ($allocationDate ? ' AND pmxd_alloc_dt <= CONVERT(datetime, ' . addSQLParam(
        $params,
        $allocationDate
    ) . ', 103)' : '') . '
		ORDER BY pmxd_alloc_dt DESC, ref_3, description';

    return $dbh->executeSet($sql, false, true, $params);
}


function dbGetAllocationsForInvoice($batchNumber, $lineNumber, $invoiceDate)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);

    $sql = "
		SELECT
			trans_amt AS transactionAmount,
			artr_tax_amt AS taxAmount,
			artr_net_amt AS netAmount,
			(SELECT COALESCE(SUM(pmxd_alloc_amt), 0) * -1 FROM pmxd_ar_alloc WHERE pmxd_t_batch = batch_nr AND pmxd_t_line = batch_line_nr AND pmxd_t_type IN ('INV','CRE')
				AND pmxd_alloc_dt <= CONVERT(datetime, ?, 103)) AS totalAllocated,
			(SELECT COALESCE(SUM(pmxd_alloc_amt), 0) FROM pmxd_ar_alloc WHERE (pmxd_f_batch = batch_nr) AND (pmxd_f_line = batch_line_nr) AND (pmxd_f_type IN ('INV','CRE')
			  	AND pmxd_t_type IN ('INV','CRE')) AND (pmxd_alloc_dt <= CONVERT(datetime, ?,103))) AS totalReallocated
		FROM
			ar_transaction
		WHERE
			batch_nr = ?
			AND batch_line_nr = ?";

    return $dbh->executeSingle($sql, [$invoiceDate, $invoiceDate, $batchNumber, $lineNumber]);
}

function dbGetInvoiceCharges(
    $type,
    $propertyID,
    $leaseID,
    $debtorID,
    $invoiceDate,
    $batchNumber = null,
    $AIInterim = false,
    $orderTransSQL = 'DESC',
    $combineDebtors = false,
    $newAmountsOnly = false,
    $includeUnallocatedCredits = true,
    $filteredBy = 'transactionDate',
    $lineNumber = null,
    $invoiceNumber = null
) {
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $params = [];
    $conditionSQL_params = [];
    $filteredByDate_params = [];

    $conditionSQL = '';
    $types = "'INV','CRE'";
    if ($lineNumber && ! is_array($lineNumber)) {
        $lineNumber = [$lineNumber];
    }

    switch ($type) {
        case InvoiceChargeType::INV_CHARGES_NEW:
            if ($invoiceNumber) {
                if (! $combineDebtors) {
                    $conditionSQL = "(((trans_type IN ({$types})) AND (artr_gst_inv_no = " . addSQLParam(
                        $conditionSQL_params,
                        $invoiceNumber
                    ) . ') AND (ref_4 = ' . addSQLParam(
                        $conditionSQL_params,
                        $leaseID
                    ) . ') AND (ref_2 = ' . addSQLParam($conditionSQL_params, $propertyID) . ')))';
                } else {
                    $conditionSQL = "(((trans_type IN ({$types})) AND (artr_gst_inv_no = " . addSQLParam(
                        $conditionSQL_params,
                        $invoiceNumber
                    ) . ') AND (debtor_code = ' . addSQLParam(
                        $conditionSQL_params,
                        $debtorID
                    ) . ') AND (ref_2 = ' . addSQLParam($conditionSQL_params, $propertyID) . ')))';
                }
            } elseif (! $combineDebtors) {
                $conditionSQL = "(((trans_type IN ({$types})) AND (artr_gst_inv_no = '0') AND (ref_4 = " . addSQLParam(
                    $conditionSQL_params,
                    $leaseID
                ) . ') AND (ref_2 = ' . addSQLParam($conditionSQL_params, $propertyID) . ')))';
            } else {
                $conditionSQL = "(((trans_type IN ({$types})) AND (artr_gst_inv_no = '0') AND (debtor_code = " . addSQLParam(
                    $conditionSQL_params,
                    $debtorID
                ) . ') AND (ref_2 = ' . addSQLParam($conditionSQL_params, $propertyID) . ')))';
            }

            if ($newAmountsOnly) {
                $conditionSQL .= " and trans_amt != '0.00'";
            }

            if (! $includeUnallocatedCredits) {
                $conditionSQL .= " and trans_type != 'CRE'";
            }

            break;
        case InvoiceChargeType::INV_CHARGES_NEW_INV_ONLY:
            $types = "'INV'";
            $conditionSQL = "(trans_type = {$types} AND artr_gst_inv_no = '0' AND ref_4 = " . addSQLParam(
                $conditionSQL_params,
                $leaseID
            ) . ' AND ref_2 = ' . addSQLParam($conditionSQL_params, $propertyID) . ')';
            break;
        case InvoiceChargeType::INV_CHARGES_OLD:
            $conditionSQL = "(trans_type NOT IN ('REV','ADJ'))";
            if ($debtorID != '') {
                $conditionSQL .= ' AND (debtor_code = ' . addSQLParam($conditionSQL_params, $debtorID) . ')';
            }

            if ($propertyID != '') {
                $conditionSQL .= ' AND ((ref_2 = ' . addSQLParam($conditionSQL_params, $propertyID) . '))';
            }

            if ($leaseID != '' && ! $combineDebtors) {
                $conditionSQL .= ' AND ((ref_4 = ' . addSQLParam($conditionSQL_params, $leaseID) . '))';
            }

            if (! $includeUnallocatedCredits) {
                $conditionSQL .= " and trans_type != 'CRE'";
            }

            break;
        case InvoiceChargeType::INV_CHARGES_OLD_WITH_INV_NUMBER_ONLY:
            $conditionSQL = "((trans_type NOT IN ('REV','ADJ','INV'))
	                OR (trans_type = 'INV' AND artr_gst_inv_no != 0))";
            if ($debtorID != '') {
                $conditionSQL .= ' AND (debtor_code = ' . addSQLParam($conditionSQL_params, $debtorID) . ')';
            }

            if ($propertyID != '') {
                $conditionSQL .= ' AND ((ref_2 = ' . addSQLParam($conditionSQL_params, $propertyID) . '))';
            }

            if ($leaseID != '' && ! $combineDebtors) {
                $conditionSQL .= ' AND ((ref_4 = ' . addSQLParam($conditionSQL_params, $leaseID) . '))';
            }

            break;
    }

    if ($invoiceDate == FINALDATE) {
        $orderSQL = ' DESC ';
    }

    if ($batchNumber) {
        if (strpos($batchNumber, ',')) {
            $bn = explode(',', $batchNumber);
            $conditionSQL .= ' AND batch_nr in (' . addSQLParam($conditionSQL_params, $bn) . ')';
        } else {
            $conditionSQL .= ' AND batch_nr=' . addSQLParam($conditionSQL_params, $batchNumber);
        }
    }

    if ($lineNumber) {
        if (count($lineNumber ?? []) > 0) {
            $conditionSQL .= ' AND batch_line_nr IN (' . addSQLParam($conditionSQL_params, $lineNumber) . ')';
        } else {
            $conditionSQL .= ' AND batch_line_nr=' . addSQLParam($conditionSQL_params, $lineNumber);
        }
    }

    if ($AIInterim) {
        $conditionSQL .= ' AND ai_interim = 1';
    }

    $filteredByDate = '';
    if (! $invoiceNumber) {
        $filteredByDate = $filteredBy == 'transactionDate' ? 'AND (trans_date <= CONVERT(datetime, ' . addSQLParam(
            $filteredByDate_params,
            $invoiceDate
        ) . ', 103))' : 'AND (due_date <= CONVERT(datetime, ' . addSQLParam(
            $filteredByDate_params,
            $invoiceDate
        ) . ', 103))';
    }

    $sql = "
		SELECT
			batch_nr AS batchNumber,
			batch_line_nr AS batchLineNumber,
			artr_tax_amt AS taxAmount,
			debtor_code AS debtorID,
			ref_2 AS propertyID,
			ref_3 AS accountID,
			ref_4 AS leaseID,
			description AS description,
			artr_gst_code AS taxCode,
			trans_amt AS amount,
			artr_net_amt AS netAmount,
			CONVERT(char(24), artr_create_date, 13) AS createDate,
			CONVERT(char(10), spare_date_1, 003) AS fromDate,
			CONVERT(char(10), spare_date_2, 003) AS toDate,
			CONVERT(char(10), trans_date, 003) AS transDate,
			CONVERT(char(10), trans_date, 103) AS transactionDate,
			CONVERT(char(10), due_date, 103) AS dueDate,
            CONVERT(char(10), spare_date_1, 103) AS dateFrom,
			CONVERT(char(10), spare_date_2, 103) AS dateTo,
			artr_gst_inv_no as invoiceNumber,
			CONVERT(char(10), artr_gst_inv_dt, 103) as invoiceDate,
			trans_type AS transactionType,
			(SELECT pmco_name FROM pmco_company WHERE (pmco_code = debtor_code)) AS debtorName,
			(SELECT pmpr_name FROM pmpr_property WHERE (pmpr_prop = ref_2)) AS propertyName,
			(SELECT pmle_name FROM pmle_lease WHERE (pmle_lease = ref_4) AND (pmle_prop = ref_2)) AS leaseName,
			(SELECT pmca_name FROM pmca_chart WHERE (pmca_code = ref_3)) AS accountName,
			(SELECT COALESCE(SUM(pmxd_alloc_amt), 0) * -1 FROM pmxd_ar_alloc WHERE pmxd_t_batch = batch_nr AND pmxd_t_line = batch_line_nr AND pmxd_t_type IN ({$types}) AND pmxd_alloc_dt <= CONVERT(datetime, " . addSQLParam(
        $params,
        $invoiceDate
    ) . ", 103)) AS totalAllocated,
			(SELECT COALESCE(SUM(pmxd_alloc_amt), 0) FROM pmxd_ar_alloc WHERE (pmxd_f_batch = batch_nr) AND (pmxd_f_line = batch_line_nr) AND (pmxd_f_type IN ({$types}) AND pmxd_t_type IN ('INV','CRE')) AND (pmxd_alloc_dt <= CONVERT(datetime, " . addSQLParam(
        $params,
        $invoiceDate
    ) . ",103))) AS totalReallocated,
			(SELECT TOP 1 pmcp_closed FROM pmcp_prop_cal WHERE (pmcp_prop = ref_2) AND (pmcp_start_dt <= trans_date) AND (pmcp_end_dt >= trans_date)) AS closed,
			(SELECT COALESCE(SUM(pmuc_amt * - 1), 0) AS total_unallocated FROM pmuc_unall_csh WHERE (pmuc_batch = RTRIM(batch_nr)) AND (pmuc_line = batch_line_nr)) AS unallocated,
			(SELECT TOP 1 pmuc_lease FROM pmuc_unall_csh WHERE (pmuc_unall_csh.pmuc_batch = RTRIM(batch_nr)) AND (pmuc_unall_csh.pmuc_line = batch_line_nr)) AS pmuc_lease,
		    (SELECT COALESCE(SUM(trans_amt), 0)
		    FROM
			ar_transaction
		    WHERE
			{$conditionSQL} " . addSQLParam(
        $params,
        $conditionSQL_params,
        false
    ) . ' AND (trans_date <= CONVERT(datetime, ' . addSQLParam($params, $invoiceDate) . ", 103)))  AS totalAmount,
			pmpr_is_ledger as is_ledger
		FROM
			ar_transaction
			JOIN pmpr_property ON pmpr_prop = ref_2
		WHERE
			{$conditionSQL} " . addSQLParam($params, $conditionSQL_params, false) . "
			{$filteredByDate} " . addSQLParam($params, $filteredByDate_params, false) . "
		ORDER BY trans_date {$orderTransSQL}, ref_3 {$orderSQL}, spare_date_1 DESC, description
		";

    return $dbh->executeSet($sql, false, true, $params);
}

function dbAllocateTransaction($transaction)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    extract($transaction);
    $params = [];

    $fundID = dbGetPropertyFundIDFromAccount($propertyID, $accountID);

    $sql = '
		INSERT INTO
			pmxd_ar_alloc
			(
				pmxd_alloc_nr,
				pmxd_f_batch,
				pmxd_f_line,
				pmxd_t_batch,
				pmxd_t_line,
				pmxd_alloc_dt,
				pmxd_alloc_amt,
				pmxd_f_type,
				pmxd_t_type,
				pmxd_s_bank,
				pmxd_prop,
				pmxd_lease,
				pmxd_acc,
				pmxd_bank_type,
				pmxd_s_debtor,
				pmxd_gl_posted,
				pmxd_create_sess,
				pmxd_create_user,
				pmxd_create_cmpn,
				pmxd_create_date,
				pmxd_create_time,
				pmxd_mod_user,
				pmxd_mod_cmpn,
				pmxd_mod_date,
				pmxd_mod_time,
				pmxd_tax_amt,
				pmxd_year,
				pmxd_period,
				pmxd_exmgmtfee,
				fund
			)
			VALUES
			(
				' . addSQLParam($params, $allocationNumber) . ',
				' . addSQLParam($params, $fromBatchNumber) . ',
				' . addSQLParam($params, $fromLineNumber) . ',
				' . addSQLParam($params, $toBatchNumber) . ',
				' . addSQLParam($params, $toLineNumber) . ',
				CONVERT(datetime, ' . addSQLParam($params, $allocationDate) . ', 103),
				' . addSQLParam($params, $transactionAmount) . ',
				' . addSQLParam($params, $fromType) . ',
				' . addSQLParam($params, $toType) . ',
				' . addSQLParam($params, $bankID) . ',
				' . addSQLParam($params, $propertyID) . ',
				' . addSQLParam($params, $leaseID) . ',
				' . addSQLParam($params, $accountID) . ",
				'ALLACCTS',
				" . addSQLParam($params, $debtorID) . ",
				'0',
				NULL,
				" . addSQLParam($params, $createUser) . ",
				'PMTZ4003',
				CONVERT(datetime, " . addSQLParam($params, $currentDate) . ', 103),
				CONVERT(datetime, ' . addSQLParam($params, $currentTime) . ', 103),
				' . addSQLParam($params, $createUser) . ",
				'PMTZ4003',
				CONVERT(datetime, " . addSQLParam($params, $currentDate) . ', 103),
				CONVERT(datetime, ' . addSQLParam($params, $currentTime) . ', 103),
				' . addSQLParam($params, $taxAmount) . ',
				' . addSQLParam($params, $transactionYear) . ',
				' . addSQLParam($params, $transactionPeriod) . ",
				'0',
				" . addSQLParam($params, $fundID) . '
			);';

    return $dbh->executeNonQuery2($sql, $params, true);
}

function dbGetAgentDetails($officeID = null, $propertyID = '')
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $params = [];
    $propertySQL = ($propertyID) ? ' AND pmpr_prop = ' . addSQLParam($params, $propertyID) : '';
    $officeSQL = ($officeID) ? 'WHERE pmol_code = ' . addSQLParam($params, $officeID) : '';

    $sql = "SELECT
			pmco_name AS agentName,
			pmco_invoice_address AS invoiceAddress,
			pmco_disable_invoice_dates AS disableInvoiceDates,
			cms_sy_street AS agentAddress,
			cms_sy_city AS agentCity,
			cms_sy_state AS agentState,
			cms_sy_post AS agentPostCode,
			cms_sy_phone AS agentPhone,
			cms_sy_fax AS agentFax,
			pmco_gst_no as agentABN,
			pmol_name AS officeName,
			pmol_street AS officeAddress,
			pmol_city AS officeCity,
			pmol_state AS officeState,
			pmol_postcode AS officePostCode,
			pmol_phone AS officePhone,
			pmol_fax AS officeFax,
			SUBSTRING(pmol_notes,1,140) as officeNotes
		FROM cms_system
		CROSS JOIN pmol_office_loc
		LEFT JOIN pmpr_property ON (pmpr_remit_off=pmol_code) {$propertySQL}
		LEFT JOIN pmco_company ON (pmco_code=pmpr_agent)
		{$officeSQL}";

    return $dbh->executeSingle($sql, $params);
}

function dbGetAgedUnallocated($propertyID, $leaseID, $debtorID, $batchNumber, $batchLineNumber, $endDate)
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);
    $params = [];

    $sql = 'SELECT COALESCE(SUM(pmuc_amt), 0) AS amount FROM pmuc_unall_csh
			WHERE (pmuc_batch = ' . addSQLParam($params, $batchNumber) . ')
			AND (pmuc_line = ' . addSQLParam($params, $batchLineNumber) . ') ' .
        ($debtorID != '' ? ' AND (pmuc_s_debtor = ' . addSQLParam($params, $debtorID) . ')' : '') .
        ($propertyID != '' ? ' AND (pmuc_prop = ' . addSQLParam($params, $propertyID) . ')' : '') .
        ($leaseID != '' ? ' AND (pmuc_lease = ' . addSQLParam($params, $leaseID) . ')' : '') . '
			AND pmuc_rcpt_dt <= CONVERT(datetime, ' . addSQLParam($params, $endDate) . ', 103)';

    return $dbh->executeSet($sql, false, true, $params);
}


function dbGetUnallocated(
    $propertyID,
    $leaseID,
    $debtorID,
    $batchNumber,
    $batchLineNumber,
    $combineDebtor = false
) {
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $params = [];
    $conditions_params = [];

    $conditions = '';
    if ($debtorID != '') {
        $conditions .= ' AND (u.pmuc_s_debtor = ' . addSQLParam($conditions_params, $debtorID) . ')';
    }

    if ($propertyID != '') {
        $conditions .= ' AND (u.pmuc_prop = ' . addSQLParam($conditions_params, $propertyID) . ')';
    }

    if ($leaseID != '' && ! $combineDebtor) {
        $conditions .= ' AND (u.pmuc_lease = ' . addSQLParam($conditions_params, $leaseID) . ')';
    }

    $sql = 'SELECT DISTINCT
			u.pmuc_prop AS propertyID,
			u.pmuc_s_bank AS bankID,
			u.pmuc_s_debtor AS debtorID,
			u.pmuc_lease AS leaseID,
			u.pmuc_acc AS accountID,
			u.pmuc_desc AS description,
			u.pmuc_gst_code AS taxCode,

				 (SELECT pmpr_name FROM pmpr_property WHERE (pmpr_prop = u.pmuc_prop)) AS propertyName,
             (SELECT pmca_name FROM pmca_chart WHERE (pmca_code = u.pmuc_acc)) AS accountName,
             (SELECT TOP 1 CONVERT(char(10), u.pmuc_rcpt_dt, 103)  FROM pmuc_unall_csh u WHERE (u.pmuc_batch = ' . addSQLParam(
        $params,
        $batchNumber
    ) . ') AND (u.pmuc_line = ' . addSQLParam($params, $batchLineNumber) . ") {$conditions} " . addSQLParam(
        $params,
        $conditions_params,
        false
    ) . ') AS date,
             (SELECT TOP 1 p.pmuc_serial FROM pmuc_unall_csh p WHERE (p.pmuc_batch = ' . addSQLParam(
        $params,
        $batchNumber
    ) . ') AND (p.pmuc_line = ' . addSQLParam($params, $batchLineNumber) . ") {$conditions} " . addSQLParam(
        $params,
        $conditions_params,
        false
    ) . ' AND p.pmuc_desc=u.pmuc_desc AND p.pmuc_acc = u.pmuc_acc AND p.pmuc_s_debtor=u.pmuc_s_debtor AND p.pmuc_prop=u.pmuc_prop AND p.pmuc_lease=u.pmuc_lease AND p.pmuc_rev_dt IS NULL) AS serial,
             COALESCE(SUM(u.pmuc_amt), 0) as amount,
             COALESCE(SUM(u.pmuc_tax_amt), 0) as taxAmount,
             COALESCE(SUM(u.pmuc_net_amt), 0) as netAmount,
             spare_date_1 AS fromDate,
			 spare_date_2 AS toDate
		FROM pmuc_unall_csh u
		LEFT JOIN ar_transaction on pmuc_batch = batch_nr AND pmuc_line = batch_line_nr
		WHERE (u.pmuc_batch = ' . addSQLParam($params, $batchNumber) . ') AND (u.pmuc_line = ' . addSQLParam(
        $params,
        $batchLineNumber
    ) . ")
		{$conditions} " . addSQLParam($params, $conditions_params, false) . '
		GROUP BY u.pmuc_prop, u.pmuc_s_bank, u.pmuc_s_debtor, u.pmuc_lease, u.pmuc_acc, u.pmuc_desc, u.pmuc_gst_code , spare_date_1 , spare_date_2
		';

    return $dbh->executeSet($sql, false, true, $params);
}

function dbBankReconciliationHeader($bankID)
{
    global $dbh, $clientDB;
    $dbh->selectDatabase($clientDB);
    $sql = 'SELECT
		 pmba_bank AS bankID,
		 CONVERT(char(10), pmba_lreco, 103) AS reconciledDate,
		 pmba_lreco_bal AS balance
		 FROM pmba_reco_hdr
		 WHERE
		 pmba_bank = ?
		';

    return $dbh->executeSingle($sql, [$bankID]);
}

/**
 * @modified 2012-05-30: Updated query to include modifyUser and modifyDate. [Morph]
 **/
function dbUpdateTransactionDescriptionById($transactionID, $description)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $sql = 'UPDATE temp_ar_transaction SET
		description=?,
		modifyUser=?,
		modifyDate=GETDATE()
		WHERE transactionID=?';
    $params = [$description, $_SESSION['un'], $transactionID];
    $dbh->executeNonQuery2($sql, $params);
}

/**
 * @modified 2012-05-30: Updated query to include modifyUser and modifyDate. [Morph]
 **/
function dbUpdateTransactionAccountById($transactionID, $accountID)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $sql = 'UPDATE temp_ar_transaction SET
		accountID=?,
		modifyUser=?,
		modifyDate=GETDATE()
		WHERE transactionID=?';
    $params = [$accountID, $_SESSION['un'], $transactionID];
    $dbh->executeNonQuery2($sql, $params);
}

/**
 * used in viewReceipt to update the transaction date. should eventually roll into a wider receipt update query
 *
 * @param  int  $receiptID  Mandatory.
 * @param  string  $date  Mandatory.
 **/
function dbUpdateTempReceiptDate($receiptID, $date)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $sql = 'UPDATE temp_receipt SET transactionDate=CONVERT(datetime, ?, 103) WHERE receiptID=?';
    $dbh->executeNonQuery2($sql, [$date, $receiptID]);
}

function dbUpdateTempReceiptClearanceDate($receiptID, $days, $date)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $params = [];
    if ($date != '') {
        $sql = 'UPDATE temp_receipt SET clearDate=CONVERT(datetime, ' . addSQLParam(
            $params,
            $date
        ) . ', 103), clearDays=' . addSQLParam($params, $days) . ' WHERE receiptID=' . addSQLParam(
            $params,
            $receiptID
        );
    } else {
        $sql = 'UPDATE temp_receipt SET clearDate=null, clearDays=' . addSQLParam(
            $params,
            $days
        ) . ' WHERE receiptID=' . addSQLParam($params, $receiptID);
    }

    $dbh->executeNonQuery2($sql, $params);
}

function dbGetTransactionTasks($fromDate = '', $toDate = '', $status = '', $user = '', $portfolio = false)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $params = [];
    $conditionSQL = '';
    if (! $portfolio) {
    } elseif ($user) {
        $conditions[] = ' (temp_pmpr_property.pmpr_portfolio=' . addSQLParam(
            $params,
            $user
        ) . ' OR pmpr_property.pmpr_portfolio=' . addSQLParam($params, $user) . ') ';
    }

    if ($fromDate) {
        $conditions[] = 't.createDate >= CONVERT(datetime, ' . addSQLParam($params, $fromDate) . ', 103)';
    }

    if ($toDate) {
        $conditions[] = 't.createDate <= CONVERT(datetime, ' . addSQLParam($params, $toDate) . ', 103)';
    }

    if ($status != '') {
        $conditions[] = 't.transactionStatus=' . addSQLParam($params, $status);
    }

    if (count($conditions ?? []) > 0) {
        $conditionSQL = ' AND ' . implode(' AND ', $conditions);
    }

    $sql = "SELECT
       propertyID,
       CONVERT(char(10), t.transactionDate, 103) AS transactionDate,
       COALESCE(SUM(t.netAmount), 0) AS netAmount,
       COALESCE(SUM(t.taxAmount), 0) AS taxAmount,
       COALESCE(SUM(t.transactionAmount), 0) AS transactionAmount,
       COUNT(DISTINCT t.leaseID) AS leases,
       t.batchNumber,
       t.invoiceNumber,
       t.createUser,
       t.transactionStatus,
       t.transactionSource,
       t.modifyUser,
       CONVERT(char(10), t.createDate, 103) As createDate,
       CONVERT(char(10), t.modifyDate, 103) As modifyDate
        FROM temp_ar_transaction t
        LEFT JOIN pmpr_property
        ON  t.propertyID = pmpr_property.pmpr_prop
        LEFT JOIN temp_pmpr_property
        ON  t.propertyID = temp_pmpr_property.pmpr_prop
        WHERE 1 = 1
            {$conditionSQL}
            and rentReviewID is null
      GROUP BY
      propertyID, batchNumber, createUser, transactionStatus, transactionDate, transactionSource, invoiceNumber, modifyUser, CONVERT(char(10), t.createDate, 103), CONVERT(char(10), t.modifyDate, 103)
      ORDER BY transactionStatus ASC, transactionDate DESC";

    return $dbh->executeSet($sql, false, true, $params);
}

function dbGetTempTransactionLeaseByBatch($batchNumber)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $sql = 'SELECT
		DISTINCT leaseID
		FROM temp_ar_transaction
		WHERE batchNumber = ?';
    $results = $dbh->executeSet($sql, false, true, [$batchNumber]);
    if (count($results ?? []) == 1) {
        return $results[0]['leaseID'];
    }



}

function dbGetTempTransactionsByBatch($batchNumber)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $sql = '
		SELECT
			transactionID,
			debtorID,
			CONVERT(char(10), transactionDate, 103) AS transactionDate,
			CASE
				WHEN dueDate IS NULL
					THEN CONVERT(char(10), transactionDate, 103)
				ELSE
					CONVERT(char(10), dueDate, 103)
			END AS dueDate,
			transactionType,
			propertyID,
			bankID,
			accountID,
			leaseID,
			unitID,
			description,
			transactionAmount,
			CONVERT(char(10), fromDate, 103) AS fromDate,
			CONVERT(char(10), toDate, 103) AS toDate,
			taxCode,
			taxAmount,
			netAmount,
			invoiceNumber,
			CONVERT(char(10),invoiceDate, 103) AS invoiceDate,
			createUser,
			CONVERT(char(10), createDate, 103) AS createDate,
			transactionYear,
			transactionPeriod,
			transactionSource,
			toBatchNumber,
			toLineNumber,
			transactionStatus,
			ap_recovered as jsonAr,
			apRecoveredInvoiceNo,
			apRecoveredSupplier,
			sendOnApproval
		FROM
			temp_ar_transaction
		WHERE
			batchNumber = ?
			and rentReviewID is null';

    return $dbh->executeSet($sql, false, true, [$batchNumber]);
}

function dbGetTransactions($transactionType, $propertyID, $leaseID, $debtorID, $invoiceNumber = null)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $params = [];
    $sql = 'SELECT batch_nr AS batchNumber,
				 batch_line_nr AS lineNumber,
				 debtor_code AS debtorID,
				 ref_2 AS propertyID,
				 ref_3 AS accountID,
				 ref_4 AS leaseID,
				 ref_5 AS unitID,
				 description,
				 artr_gst_code AS taxCode,
				 trans_amt AS transactionAmount,
				 artr_net_amt AS netAmount,
				 artr_tax_amt AS taxAmount,
				 trans_type AS transType,
				 CONVERT(char(10), trans_date, 103) as transactionDate,
				 artr_gst_inv_no AS invoiceNumber,
				 CONVERT(char(10), artr_gst_inv_dt, 103) AS invoiceDate,
		(SELECT pmco_name FROM pmco_company WHERE (pmco_code = ar.debtor_code)) AS debtorName,
		(SELECT pmpr_name FROM pmpr_property WHERE (pmpr_prop = ar.ref_2)) AS propertyName,
		(SELECT pmle_name FROM pmle_lease WHERE (pmle_lease = ar.ref_4) AND (pmle_prop = ar.ref_2)) AS leaseName,
		(SELECT pmca_name FROM pmca_chart WHERE (pmca_code = ar.ref_3)) AS accountName,
		0 AS totalAllocated,
		0 AS totalReallocated,
		(SELECT pmcp_closed FROM pmcp_prop_cal WHERE (pmcp_prop = ar.ref_2) AND (pmcp_start_dt <= ar.trans_date) AND (pmcp_end_dt >= ar.trans_date)) AS closed
		FROM ar_transaction ar
		WHERE ref_2 = ' . addSQLParam($params, $propertyID) .
        ($transactionType ? ' AND (trans_type = ' . addSQLParam($params, $transactionType) . ') ' : '') . '
		AND ref_4 = ' . addSQLParam($params, $leaseID) . '
		AND debtor_code = ' . addSQLParam($params, $debtorID) .
        ($invoiceNumber ? ' AND artr_gst_inv_no = ' . addSQLParam($params, $invoiceNumber) : '') . '
		ORDER BY trans_date DESC';

    return $dbh->executeSet($sql, false, true, $params);
}

function dbGetRecurringInvoices()
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);
    $sql = 'SELECT templateID,
				debtorID,
				propertyID,
				leaseID,
				accountID,
				description,
				taxRateID,
				grossAmount,
				netAmount
				FROM recurring_trans_ar
				ORDER BY debtorID';

    return $dbh->executeSet($sql);
}

function dbGetRecurringInvoice($templateID)
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);
    $sql = 'SELECT templateID,
				debtorID,
				propertyID,
				leaseID,
				accountID,
				description,
				taxRateID,
				grossAmount,
				netAmount
				FROM recurring_trans_ar
				WHERE templateID=?';

    return $dbh->executeSingle($sql, [$templateID]);
}

function dbDeleteRecurringInvoice($templateID)
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);
    $sql = 'DELETE FROM recurring_trans_ar
				WHERE templateID=?';

    return $dbh->executeNonQuery2($sql, [$templateID]);
}

function dbInsertRecurringInvoice($invoice)
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);

    $sql = 'INSERT INTO recurring_trans_ar
				(debtorID,
				propertyID,
				leaseID,
				accountID,
				description,
				taxRateID,
				grossAmount,
				netAmount)
				VALUES
				(?,?,?,?,?,?,?,?)
				';
    $params = [
        ($invoice['debtorID'] ? $invoice['debtorID'] : ''),
        ($invoice['propertyID'] ? $invoice['propertyID'] : ''),
        ($invoice['leaseID'] ? $invoice['leaseID'] : ''),
        $invoice['accountID'],
        $invoice['description'],
        $invoice['taxRateID'],
        $invoice['grossAmount'],
        $invoice['netAmount'],
    ];

    return $dbh->executeNonQuery2($sql, $params);
}

if (! function_exists('dbGetCurrentUnit')) {
    function dbGetCurrentUnit($propertyID, $leaseID, $atDate = null)
    {
        global $dbh;
        global $clientDB;
        $dbh->selectDatabase($clientDB);
        $params = [];
        $sql = 'SELECT u.pmua_unit AS unitID
					FROM pmua_unit_area u
					WHERE u.pmua_prop=' . addSQLParam($params, $propertyID) . '
					AND u.pmua_lease=' . addSQLParam($params, $leaseID) .
            (($atDate) ? ' AND CONVERT(datetime, ' . addSQLParam(
                $params,
                $atDate
            ) . ', 103) BETWEEN pmua_from_dt AND pmua_to_dt' : '');

        return $dbh->executeScalar($sql, $params);
    }
}

function dbGetChargeCountForDate($propertyID, $leaseID, $fromDate, $toDate, $chargeType = 1)
{
    global $dbh, $clientDB;
    $dbh->selectDatabase($clientDB);

    switch ($chargeType) {
        case 2:
            $chargeSQL = " AND pmlc_freq IN ('M','Q','Y','S')";
            break;
        case 3:
            $chargeSQL = " AND pmlc_freq IN ('W','F','K')";
            break;
        default:
            $chargeSQL = '';
            break;
    }

    $sql = "SELECT COUNT(pmlc_serial) FROM pmlc_l_charge
				WHERE pmlc_prop=?
				AND pmlc_lease=?
				AND pmlc_stop_dt >= CONVERT(datetime, ?, 103)
				AND pmlc_next_dt BETWEEN CONVERT(datetime, ?, 103) AND CONVERT(datetime, ?, 103)
				{$chargeSQL}
				";
    $params = [$propertyID, $leaseID, $fromDate, $fromDate, $toDate];

    return $dbh->executeScalar($sql, $params);
}


function dbGetNextChargeDate($chargeType = 1)
{
    global $dbh, $clientDB;
    $dbh->selectDatabase($clientDB);

    switch ($chargeType) {
        case 2:
            $chargeSQL = "pmlc_freq IN ('M','Q','Y')";
            break;
        case 3:
            $chargeSQL = "pmlc_freq IN ('W','F','K')";
            break;
        default:
            $chargeSQL = '1=1';
            break;
    }

    $sql = "SELECT COUNT(pmlc_serial) AS count,
		CONVERT(char(10), pmlc_next_dt, 103) AS nextDate
		FROM pmlc_l_charge
				WHERE
				{$chargeSQL}
		GROUP BY pmlc_next_dt
		ORDER BY count DESC
				";

    return $dbh->executeSingle($sql);
}

function dbGetNextBillingDateForProperty($propertyID)
{
    global $dbh, $clientDB;
    $dbh->selectDatabase($clientDB);

    $sql = 'SELECT CONVERT(char(10), pmlc_next_dt, 103) nextBillingDate


		FROM pmlc_l_charge
				WHERE pmlc_prop = ?
			AND pmlc_next_dt < pmlc_stop_dt
		AND pmlc_stop = 0
		ORDER BY pmlc_next_dt ASC
		  ';

    return $dbh->executeScalar($sql, [$propertyID]);
}

function dbGetNextBillingDateForPropertyAgainstBatch($propertyID, $batchID)
{
    global $dbh, $clientDB;
    $dbh->selectDatabase($clientDB);

    $sql = '
		SELECT TOP 1 COALESCE(CONVERT(char(10),DATEADD(day, 1, s.toDate), 103),CONVERT(char(10), c.pmlc_next_dt, 103)) as nextBillingDate
		FROM pmlc_l_charge c
		INNER JOIN temp_standing_charges s
		ON
		(
		c.pmlc_prop=s.propertyID
		AND c.pmlc_lease=s.leaseID
		AND c.pmlc_unit=s.unitID
		AND c.pmlc_acc=s.accountID
		AND (DATEADD(day, 1, s.toDate) <= c.pmlc_stop_dt)
		AND s.batchID = ?
		)
		WHERE
		c.pmlc_prop = ?
		AND c.pmlc_stop = 0
		AND c.pmlc_next_dt < c.pmlc_stop_dt
		ORDER BY
		COALESCE(DATEADD(day, 1, s.toDate),c.pmlc_next_dt) DESC
		';

    return $dbh->executeScalar($sql, [$batchID, $propertyID]);
}

function dbGetChargesForLease($propertyID, $leaseID, $unitID, $fromDate = null)
{
    global $dbh, $clientDB;
    $dbh->selectDatabase($clientDB);
    $params = [];

    $sql = 'SELECT
				pmlc_prop AS propertyID,
				pmlc_lease AS leaseID,
				pmlc_serial AS unitChargeID,
				pmlc_acc AS accountID,
				pmlc_desc AS unitChargeDescription,
				pmlc_chg_type AS unitChargeType,
				pmlc_part_rent AS unitChargePartial,
				pmlc_freq AS unitChargeFrequency,
				pmlc_gst_code AS gst_code,
				CONVERT(char(10), pmlc_next_dt, 103) AS unitChargeNextDate,
				CONVERT(char(10), pmlc_start_dt, 103) AS unitChargeStartDate,
				CONVERT(char(10), pmlc_last_dt, 103) AS unitChargeLastDate,
				CONVERT(char(10), pmlc_stop_dt, 103) AS unitChargeStopDate,
				force_gst_free As forceGSTFree
				FROM  pmlc_l_charge
				WHERE pmlc_prop = ' . addSQLParam($params, $propertyID) . '
			AND pmlc_lease = ' . addSQLParam($params, $leaseID) . '
			AND pmlc_unit = ' . addSQLParam($params, $unitID) .
        ($fromDate ? ' AND pmlc_stop_dt > CONVERT(datetime, ' . addSQLParam($params, $fromDate) . ', 103)
            AND pmlc_next_dt >= CONVERT(datetime, ' . addSQLParam($params, $fromDate) . ', 103)' : '');

    return $dbh->executeSet($sql, false, true, $params);
}


function dbGetChargeAtDate($propertyID, $leaseID, $unitID, $unitChargeID, $date)
{
    global $dbh, $clientDB;
    $dbh->selectDatabase($clientDB);

    $sql = 'SELECT
			  pmla_amt AS amount,
		  CONVERT(char(10), pmla_start_dt, 103) AS startDate,
			  CONVERT(char(10), pmla_end_dt, 103) AS endDate
			  FROM pmla_l_c_amt
			  WHERE pmla_prop = ?
			  AND pmla_lease = ?
			  AND pmla_unit = ?
			  AND pmla_serial=?
		  AND (CONVERT(datetime, ?, 103) BETWEEN pmla_start_dt AND pmla_end_dt)';
    $params = [$propertyID, $leaseID, $unitID, $unitChargeID, $date];

    return $dbh->executeSingle($sql, $params);
}

function dbGetAgentDetailsPerProperty($officeID = null, $propertyID = '')
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $propertySQL = ($propertyID) ? "AND pmpr_prop = '{$propertyID}'" : '';
    $officeSQL = ($officeID) ? "WHERE pmol_code = '{$officeID}'" : '';

    $sql = "SELECT
			pmco_name AS agentName,
			pmco_invoice_address AS invoiceAddress,
			pmco_disable_invoice_dates AS disableInvoiceDates,
			cms_sy_street AS agentAddress,
			cms_sy_city AS agentCity,
			cms_sy_state AS agentState,
			cms_sy_post AS agentPostCode,
			cms_sy_phone AS agentPhone,
			cms_sy_fax AS agentFax,
			pmco_gst_no as agentABN,
			pmol_name AS officeName,
			pmol_street AS officeAddress,
			pmol_city AS officeCity,
			pmol_state AS officeState,
			pmol_postcode AS officePostCode,
			pmol_phone AS officePhone,
			pmol_fax AS officeFax,
			SUBSTRING(pmol_notes,1,140) as officeNotes
		FROM cms_system
		CROSS JOIN pmol_office_loc
		LEFT JOIN pmpr_property ON (pmpr_remit_off=pmol_code) {$propertySQL}
		LEFT JOIN pmco_company ON (pmco_code=pmpr_agent)
		{$officeSQL}";

    return $dbh->executeSingle($sql);
}

function dbPropertyTaxLookup()
{
    global $dbh, $clientDB;
    $dbh->selectDatabase($clientDB);
    $sql = 'SELECT pmpr_prop as propertyID, pmco_gst_code as taxCode FROM pmpr_property, pmco_company WHERE pmpr_owner=pmco_code';

    return $dbh->executeSet($sql);
}

function dbUpdateStandingBatchUser($batchID, $user)
{
    global $dbh, $clientDB;
    $dbh->selectDatabase($clientDB);
    $sql = 'UPDATE temp_standing_batch SET notifyUser=? WHERE batchID=?';

    return $dbh->executeNonQuery2($sql, [$user, $batchID]);
}

function dbGetStandingReport($batchID)
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);
    $sql = 'SELECT * FROM temp_standing_report WHERE batchID=?';

    return $dbh->executeSet($sql, false, true, [$batchID]);
}

// ##################### RECEIPTS ##################################

function dbGetNextReceiptNumber($bankID)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $sql = 'SELECT pmbk_last_rcpt FROM pmbk_bank WHERE pmbk_code = ?';
    $receiptNumber = $dbh->executeScalar($sql, [$bankID]);
    if (! $receiptNumber) {
        $receiptNumber = 0;
    }

    $receiptNumber++;

    return $receiptNumber;
}

function dbUpdateLeaseNumber($batchNumber, $lineNumber)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $params = [$batchNumber, $lineNumber];
    $sql = 'SELECT COUNT(DISTINCT pmxd_lease) AS leases FROM pmxd_ar_alloc WHERE (pmxd_f_batch = ?) AND (pmxd_f_line = ?)';
    $leases = $dbh->executeScalar($sql, $params);

    $params = [$leases, $batchNumber, $lineNumber];
    $sql = 'UPDATE pmrc_receipt SET pmrc_no_leases	= ? WHERE (pmrc_batch = ?) AND (pmrc_line = ?)';

    return $dbh->executeNonQuery2($sql, $params);
}

function dbUpdateReceiptNumber($bankID, $receiptNumber)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $sql = 'UPDATE pmbk_bank SET pmbk_last_rcpt = ? WHERE pmbk_code = ?';

    return $dbh->executeNonQuery2($sql, [$receiptNumber, $bankID]);
}

function dbInsertReceipt($receipt)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $params = [];
    extract($receipt);

    $sql = 'INSERT INTO pmrc_receipt
		(pmrc_batch,
		pmrc_line,
		pmrc_bank,
		pmrc_receipt_no,
		pmrc_print_dt,
		pmrc_type,
		pmrc_rcpt_op_bal,
		pmrc_rcpt_u_dt,
		pmrc_no_leases,
		pmrc_printed,
		pmrc_pages,
		pmrc_payer_name,
		pmrc_payer_address,
		pmrc_payer_suburb,
		pmrc_payer_state,
		pmrc_clear_days,
		pmrc_clear_date
		)
	VALUES
		(' . addSQLParam($params, $batchNumber) . ',
		' . addSQLParam($params, $lineNumber) . ',
		' . addSQLParam($params, $bankID) . ',
		' . addSQLParam($params, $receiptNumber) . ",
		NULL,
		'A',
		'0',
		'0',
		'1',
		'0',
		'0',
		" . addSQLParam($params, $payerName) . ',
		' . addSQLParam($params, $payerAddress) . ',
		' . addSQLParam($params, $payerSuburb) . ',
		' . addSQLParam($params, $payerState) . ',
		' . addSQLParam($params, $clearDays) . ',
		CONVERT(datetime, ' . addSQLParam($params, $clearDate) . ',103)
		)';

    return $dbh->executeNonQuery2($sql, $params, true);
}

function dbInsertBatch($transaction)
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);
    $params = [];
    extract($transaction);
    $sql = '
	INSERT INTO ar_batch
	(
	  batch_nr,
	  u_version,
	  bank,
	  batch_date,
	  batch_ref,
	  trans_type,
	  user_id,
	  batch_amt,
	  entries,
	  trans_amt,
	  trans_entries,
	  balanced_flag,
	  ok_to_post, posted_flag,
	  last_line_nr,
	  direct_deposit_flag,
	  date_created,
	  branch_code,
	  bank_deposit_id
   )
	VALUES
	(
	  ' . addSQLParam($params, $batchNumber) . ",
	  '!',
	  " . addSQLParam($params, $bankID) . ',
	  CONVERT(datetime, ' . addSQLParam($params, $transactionDate) . ", 103),
	  '" . date('ymd') . "',
	  'CSH',
	  " . addSQLParam($params, $createUser) . ',
	  ' . addSQLParam($params, $transactionAmount) . ',
	  ' . addSQLParam($params, $lineNumber) . ',
	  ' . addSQLParam($params, $transactionAmount) . ',
	  ' . addSQLParam($params, $lineNumber) . ",
	  'Y',
	  'Y',
	  'N',
	  " . addSQLParam($params, $lineNumber) . ",
	  'N',
	  CONVERT(datetime, " . addSQLParam($params, $transactionDate) . ', 103),
	  NULL,
	  NULL
   )
	';

    return $dbh->executeNonQuery2($sql, $params, true);
}


function dbInsertPaymentDetail($transaction)
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);
    extract($transaction);
    $sql = "
	INSERT INTO ar_payment_detail
	(
	batch_nr,
	batch_line_nr,
	u_version,
	drawer,
	bank_code,
	bank_branch
   )
	VALUES
	(
	?,
	?,
	'!',
	?,
	?,
	?
   )
	";
    $params = [$batchNumber, $lineNumber, $drawerName, $bankID, $bsbNumber];

    return $dbh->executeNonQuery2($sql, $params);
}

function dbUpdateDefaultCheque($transaction)
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);
    extract($transaction);
    $sql = 'SELECT pmqd_debtor FROM pmqd_chq_def WHERE pmqd_debtor=?';
    $check = $dbh->executeScalar($sql, [$debtorID]);
    if (! $check) {
        $sql = "INSERT INTO pmqd_chq_def (pmqd_debtor, pmqd_name, pmqd_par_type, pmqd_bank, pmqd_branch) VALUES
				(?,?,'STDBANK',?,?)";
        $params = [$debtorID, $drawerName, $bankID, $bsbNumber];
    } else {
        $sql = "UPDATE pmqd_chq_def
				SET pmqd_debtor 	= 	?,
				  pmqd_name		=	?,
				  pmqd_par_type	=	'STDBANK',
				  pmqd_bank		=	?,
				  pmqd_branch		=	?
				  WHERE (pmqd_debtor = ?)";
        $params = [$debtorID, $drawerName, $bankID, $bsbNumber, $debtorID];
    }

    return $dbh->executeNonQuery2($sql, $params);
}

function dbGetDefaultCheque($debtorID)
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);
    $sql = '
	 SELECT
	  pmqd_debtor AS debtorID,
	  pmqd_name AS drawerName,
	  pmqd_par_type AS parameterType,
	  pmqd_bank AS bankID,
	  pmqd_branch AS bsbNumber
	  FROM pmqd_chq_def
	  WHERE (pmqd_debtor = ?)
	 ';

    return $dbh->executeSingle($sql, [$debtorID]);
}

function dbGetDefaultEFT($debtorID)
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);
    $sql = '
	 SELECT
	  pmqd_debtor AS debtorID,
	  pmqd_name AS depositName
	  FROM pmqd_eft_dep
	  WHERE (pmqd_debtor = ?);
	 ';

    return $dbh->executeSingle($sql, [$debtorID]);
}

function dbUpdateDefaultEft($debtorID, $drawerName)
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);
    $params = [];
    $check = $dbh->executeScalar('SELECT pmqd_debtor FROM pmqd_eft_dep WHERE pmqd_debtor=?', [$debtorID]);

    $sql = ($check) ? 'UPDATE pmqd_eft_dep SET pmqd_name = ' . addSQLParam(
        $params,
        $drawerName
    ) . ' WHERE (pmqd_debtor = ' . addSQLParam($params, $debtorID) . ')' :
        'INSERT INTO pmqd_eft_dep (pmqd_debtor, pmqd_name) VALUES (' . addSQLParam(
            $params,
            $debtorID
        ) . ',' . addSQLParam($params, $drawerName) . ')';

    return $dbh->executeNonQuery2($sql, $params);
}


function dbUpdateBankReconciliationDeposits($transaction)
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);
    extract($transaction);
    $sql = '
	  UPDATE  pmbb_reco_dep
	  SET pmbb_pval= ?,
	  pmbb_debtor = NULL
	  WHERE	(pmbb_bank = ?) AND (pmbb_date = CONVERT(datetime, ?, 103)) AND (pmbb_ref = ?)
	  ';
    $params = [$transactionAmount, $bankID, $transactionDate, $bankReference];

    return $dbh->executeNonQuery2($sql, $params);
}

function dbInsertBankReconciliationDeposits($transaction)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    extract($transaction);

    $cancelledBy = $cancelledBy ?: null;
    $cancelledTime = $cancelledTime ?: null;
    $cancelReason = $cancelReason ?: null;

    $sql = '
		INSERT INTO
			pmbb_reco_dep
		(
			pmbb_cancel_by,
		    pmbb_cancel_date,
		    pmbb_cancel_reason,
			pmbb_bank,
			pmbb_date,
			pmbb_ref,
			pmbb_pval,
			pmbb_ppval,
			pmbb_ppdate,
			pmbb_reco,
			pmbb_prop,
			pmbb_debtor,
			pmbb_creditor,
			pmbb_owner,
			pmbb_crpaid,
			pmbb_dbpaid,
			pmbb_s_ref,
			pmbb_s_type,
			pmbb_owpaid,
			batch_nr,
			batch_line_nr
		)
		VALUES
		(
			' . addSQLParam($params, $cancelledBy) . ',
		    ' . addSQLParam($params, $cancelledTime) . ',
		    ' . addSQLParam($params, $cancelReason) . ',
			' . addSQLParam($params, $bankID) . ',
			CONVERT(datetime, ' . addSQLParam($params, $transactionDate) . ',103),
			' . addSQLParam($params, $bankReference) . ',
			' . addSQLParam($params, $transactionAmount) . ",
			'0',
			NULL,
			NULL,
			NULL,
			" . addSQLParam($params, $debtorID) . ",
			NULL,
			NULL,
			'0',
			" . addSQLParam($params, $paid) . ',
			' . addSQLParam($params, $reference) . ',
			' . addSQLParam($params, $type) . ",
			'0',
			" . addSQLParam($params, $batchNumber) . ',
			' . addSQLParam($params, $lineNumber) . '
		)';

    return $dbh->executeNonQuery2($sql, $params, true);
}

function dbInsertUnallocated($transaction)
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);
    $params = [];
    extract($transaction);
    $sql = '
	 INSERT INTO pmuc_unall_csh	 (
		pmuc_batch,
		pmuc_line,
		pmuc_serial,
		pmuc_s_bank,
		pmuc_s_debtor,
		pmuc_ref_1,
		pmuc_prop,
		pmuc_lease,
		pmuc_acc,
		pmuc_bank_type,
		pmuc_rcpt_dt,
		pmuc_desc,
		pmuc_amt,
		pmuc_rev_dt,
		pmuc_gst_code,
		pmuc_tax_amt,
		pmuc_net_amt,
		pmuc_gl_posted,
		pmuc_create_sess,
		pmuc_create_user,
		pmuc_create_cmpn,
		pmuc_create_date,
		pmuc_create_time,
		pmuc_mod_user,
		pmuc_mod_cmpn,
		pmuc_mod_date,
		pmuc_mod_time,
		pmuc_year,
		pmuc_period,
		pmuc_gst_inv_no
		)
	VALUES (
		' . addSQLParam($params, $batchNumber) . ',
		' . addSQLParam($params, $lineNumber) . ',
		' . addSQLParam($params, $serial) . ',
		' . addSQLParam($params, $bankID) . ',
		' . addSQLParam($params, $debtorID) . ',
		CONCAT(' . addSQLParam($params, $batchNumber) . ",'/'," . addSQLParam($params, $serial) . '),
		' . addSQLParam($params, $propertyID) . ',
		' . addSQLParam($params, $leaseID) . ',
		' . addSQLParam($params, $accountID) . ",
		'ALLACCTS',
		CONVERT(datetime, " . addSQLParam($params, $transactionDate) . ', 103),
		' . addSQLParam($params, $description) . ',
		' . addSQLParam($params, $transactionAmount) . ',
		CONVERT(datetime, ' . addSQLParam($params, $reversalDate) . ', 103),
		' . addSQLParam($params, $taxCode) . ',
		' . addSQLParam($params, $taxAmount) . ',
		' . addSQLParam($params, $netAmount) . ",
		'0',
		" . addSQLParam($params, $batchNumber) . ',
		' . addSQLParam($params, $createUser) . ",
		'PMTZ4004',
		GETDATE(),
		GETDATE(),
		" . addSQLParam($params, $createUser) . ",
		'PMTZ4004',
		GETDATE(),
		GETDATE(),
		" . addSQLParam($params, $year) . ',
		' . addSQLParam($params, $period) . ',
		NULL
		)
	 ';

    return $dbh->executeNonQuery2($sql, $params, true);
}


function dbInsertAllocated($transaction)
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);
    $params = [];
    extract($transaction);

    $fundID = dbGetPropertyFundIDFromAccount($propertyID, $accountID);

    $sql = 'INSERT INTO pmxd_ar_alloc
	 (
	 pmxd_alloc_nr,
	 pmxd_f_batch,
	 pmxd_f_line,
	 pmxd_t_batch,
	 pmxd_t_line,
	 pmxd_alloc_dt,
	 pmxd_alloc_amt,
	 pmxd_f_type,
	 pmxd_t_type,
	 pmxd_s_bank,
	 pmxd_prop,
	 pmxd_lease,
	 pmxd_acc,
	 pmxd_bank_type,
	 pmxd_s_debtor,
	 pmxd_gl_posted,
	 pmxd_create_sess,
	 pmxd_create_user,
	 pmxd_create_cmpn,
	 pmxd_create_date,
	 pmxd_create_time,
	 pmxd_mod_user,
	 pmxd_mod_cmpn,
	 pmxd_mod_date,
	 pmxd_mod_time,
	 pmxd_tax_amt,
	 pmxd_year,
	 pmxd_period,
	 pmxd_partial,
	 fund,
	 pmxd_exmgmtfee
	)
	 VALUES
	 (
	 ' . addSQLParam($params, $allocationNumber) . ',
	 ' . addSQLParam($params, $fromBatchNumber) . ',
	 ' . addSQLParam($params, $fromLineNumber) . ',
	 ' . addSQLParam($params, $toBatchNumber) . ',
	 ' . addSQLParam($params, $toLineNumber) . ',
	 CONVERT(datetime, ' . addSQLParam($params, $transactionDate) . ', 103),
	 ' . addSQLParam($params, $transactionAmount) . ',
	 ' . addSQLParam($params, $fromType) . ',
	 ' . addSQLParam($params, $toType) . ',
	 ' . addSQLParam($params, $bankID) . ',
	 ' . addSQLParam($params, $propertyID) . ',
	 ' . addSQLParam($params, $leaseID) . ',
	 ' . addSQLParam($params, $accountID) . ",
	 'ALLACCTS',
	 " . addSQLParam($params, $debtorID) . ",
	 '0',
	 " . addSQLParam($params, $allocationNumber) . ',
	 ' . addSQLParam($params, $createUser) . ",
	 'PMTZ4003',
	 GETDATE(),
	 GETDATE(),
	 " . addSQLParam($params, $createUser) . ",
	 'PMTZ4003',
	 GETDATE(),
	 GETDATE(),
	 " . addSQLParam($params, $taxAmount) . ',
	 ' . addSQLParam($params, $year) . ',
	 ' . addSQLParam($params, $period) . ',
	 ' . addSQLParam($params, $partial) . ',
	 ' . addSQLParam($params, $fundID) . ",
	 '0'
	)
	 ";

    return $dbh->executeNonQuery2($sql, $params, true);
}


function dbUpdateUnallocated($transaction)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $params = [];
    extract($transaction);
    $sql = '
		UPDATE
			pmuc_unall_csh
		SET
			pmuc_amt = ' . addSQLParam($params, $transactionAmount) . ', ' .
        ($reversalDate ? 'pmuc_rev_dt = CONVERT(datetime, ' . addSQLParam($params, $reversalDate) . ', 103),' : '') . '
			pmuc_tax_amt = ' . addSQLParam($params, $taxAmount) . ',
			pmuc_net_amt = ' . addSQLParam($params, $netAmount) . ',
			pmuc_mod_user = ' . addSQLParam($params, $modifiedUser) . ',
			pmuc_mod_date = GETDATE(),
			pmuc_mod_time = GETDATE()
		WHERE
			pmuc_batch = ' . addSQLParam($params, $batchNumber) . '
			AND pmuc_line = ' . addSQLParam($params, $lineNumber) . '
			AND pmuc_serial = ' . addSQLParam($params, $serial) . '
			AND (pmuc_rev_dt IS NULL)';

    return $dbh->executeNonQuery2($sql, $params, true);
}

function dbGetNextBankReference($transactionDate, $bankID)
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);
    $sql = 'SELECT MAX(pmbb_ref) + 1 as max FROM pmbb_reco_dep WHERE (pmbb_date = CONVERT(datetime, ?, 103)) AND (pmbb_bank = ?);';
    $ref = $dbh->executeScalar($sql, [$transactionDate, $bankID]);
    if (! $ref) {
        $ref = 0;
    }

    return $ref;
}

function dbGetChequePayments($transactionDate, $bankID)
{
    global $dbh, $clientDB;
    $dbh->selectDatabase($clientDB);
    $sql = "SELECT * FROM pmbb_reco_dep WHERE (pmbb_bank = ?) AND (pmbb_s_type = 'D') AND (pmbb_date = CONVERT(datetime, ?, 103)) AND (pmbb_ppval = '0') AND pmbb_debtor IS NULL";

    return $dbh->executeSet($sql, false, true, [$bankID, $transactionDate]);
}

function dbGetBankValue($transactionDate, $bankID)
{
    global $dbh, $clientDB;
    $dbh->selectDatabase($clientDB);
    $sql = "SELECT pmbb_pval FROM pmbb_reco_dep WHERE (pmbb_bank = ?) AND (pmbb_s_type = 'D') AND (pmbb_date = CONVERT(datetime, ?, 103)) AND (pmbb_ppval = '0') AND pmbb_debtor IS NULL";

    return $dbh->executeScalar($sql, [$bankID, $transactionDate]);
}

function dbGetBankReference($transactionDate, $bankID)
{
    global $dbh, $clientDB;
    $dbh->selectDatabase($clientDB);
    $sql = "SELECT pmbb_ref FROM pmbb_reco_dep WHERE (pmbb_bank = ?) AND (pmbb_date = CONVERT(datetime, ?, 103)) AND (pmbb_s_type = 'D') AND (pmbb_ppval = 0) AND pmbb_debtor IS NULL";
    $ref = $dbh->executeScalar($sql, [$bankID, $transactionDate]);
    if (! $ref) {
        $ref = 0;
    }

    return $ref;
}

function dbGetNextUnallocatedSerial($batchNumber, $lineNumber = null)
{
    global $dbh, $clientDB;
    $dbh->selectDatabase($clientDB);
    $params = [];
    $sql = 'SELECT MAX(pmuc_serial) + 1 AS serial FROM pmuc_unall_csh WHERE (pmuc_batch = ' . addSQLParam(
        $params,
        $batchNumber
    ) . ')';
    if ($lineNumber) {
        $sql .= ' AND (pmuc_line = ' . addSQLParam($params, $lineNumber) . ')';
    }

    $serial = $dbh->executeScalar($sql, $params);
    if (! $serial) {
        $serial = 1;
    }

    return $serial;
}

function dbGetTransactionAmountByBatch($batchNumber, $lineNumber)
{
    global $dbh, $clientDB;
    $dbh->selectDatabase($clientDB);
    $sql = '
		  SELECT trans_amt as transactionAmount,
		  artr_net_amt as netAmount,
		  artr_tax_amt as taxAmount,
		  artr_gst_code as taxCode
		  FROM ar_transaction
		  WHERE (batch_nr = ?)
		  AND (batch_line_nr = ?)
		  ';

    return $dbh->executeSingle($sql, [$batchNumber, $lineNumber]);
}

function dbGetTotalUnallocated($batchNumber, $lineNumber, $total = true)
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);
    $totalSQL = ($total) ? ' AND (pmuc_amt < 0)' : ' AND (pmuc_amt > 0)';
    $sql = "
				SELECT COALESCE(SUM(ABS(pmuc_amt), 0)) AS transactionAmount, COALESCE(SUM(ABS(pmuc_tax_amt), 0)) AS taxAmount
				FROM pmuc_unall_csh
				WHERE (pmuc_batch = ?)
				AND (pmuc_line = ?)
				{$totalSQL}
			  ";

    return $dbh->executeSingle($sql, [$batchNumber, $lineNumber]);
}


function dbGetTotalTaxed($batchNumber, $lineNumber, $transactionType)
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);
    $sql = '
				SELECT COALESCE(SUM(pmxd_alloc_amt), 0) AS totalAllocated, COALESCE(SUM(pmxd_tax_amt), 0) AS totalTax
				FROM pmxd_ar_alloc
				WHERE (pmxd_t_batch = ?)
				AND (pmxd_t_line = ?)
				AND (pmxd_t_type = ?)
			  ';

    return $dbh->executeSingle($sql, [$batchNumber, $lineNumber, $transactionType]);
}

function dbGetUnallocatedByBatch($batchNumber, $lineNumber, $serial)
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);
    $sql = 'SELECT
	pmuc_batch as batchNumber,
	 pmuc_line as lineNumber,
	 pmuc_serial as serial,
	 pmuc_s_bank as bankID,
	 pmuc_s_debtor as debtorID,
	 pmuc_ref_1 as reference,
	 pmuc_prop as propertyID,
	 pmuc_lease as leaseID,
	 pmuc_acc as accountID,
	 pmuc_bank_type as bankType,
	 CONVERT(char(10), pmuc_rcpt_dt, 103) as transactionDate,
	 pmuc_desc as description,
	 pmuc_amt as transactionAmount,
	 CONVERT(char(10), pmuc_rev_dt, 103) as reversalDate,
	 pmuc_gst_code as taxCode,
	 pmuc_tax_amt as taxAmount,
	 pmuc_net_amt as netAmount,
	 pmuc_gl_posted as isPosted,
	 pmuc_create_user as createUser,
	 CONVERT(char(10), pmuc_create_date, 103) as createDate,
	 CONVERT(char(10), pmuc_create_time, 103) as createTime,
	 pmuc_mod_user as modifiedUser,
	 CONVERT(char(10), pmuc_mod_date, 103) as modifiedDate,
	 CONVERT(char(10), pmuc_mod_time, 103) as modifiedTime,
	 pmuc_year as year,
	 pmuc_period as period,
	 pmuc_gst_inv_no as invoiceNumber
	FROM pmuc_unall_csh
	WHERE pmuc_batch=? AND pmuc_line=? AND pmuc_serial=? AND (pmuc_rev_dt IS NULL)';

    return $dbh->executeSingle($sql, [$batchNumber, $lineNumber, $serial]);
}


function dbGetUnallocatedListByBatch($batchNumber, $lineNumber)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $sql = '
		SELECT
			pmuc_batch as batchNumber,
			pmuc_line as lineNumber,
			pmuc_serial as serial,
			pmuc_s_bank as bankID,
			pmuc_s_debtor as debtorID,
			pmuc_ref_1 as reference,
			pmuc_prop as propertyID,
			pmuc_lease as leaseID,
			pmuc_acc as accountID,
			pmuc_bank_type as bankType,
			CONVERT(char(10), pmuc_rcpt_dt, 103) as transactionDate,
			pmuc_desc as description,
			pmuc_amt as transactionAmount,
			CONVERT(char(10), pmuc_rev_dt, 103) as reversalDate,
			pmuc_gst_code as taxCode,
			pmuc_tax_amt as taxAmount,
			pmuc_net_amt as netAmount,
			pmuc_gl_posted as isPosted,
			pmuc_create_user as createUser,
			CONVERT(char(10), pmuc_create_date, 103) as createDate,
			CONVERT(char(10), pmuc_create_time, 103) as createTime,
			pmuc_mod_user as modifiedUser,
			CONVERT(char(10), pmuc_mod_date, 103) as modifiedDate,
			CONVERT(char(10), pmuc_mod_time, 103) as modifiedTime,
			pmuc_year as year,
			pmuc_period as period,
			pmuc_gst_inv_no as invoiceNumber
		FROM
			pmuc_unall_csh
		WHERE
			pmuc_batch=?
			AND pmuc_line=?
			AND (pmuc_rev_dt IS NULL)';

    return $dbh->executeSet($sql, false, true, [$batchNumber, $lineNumber]);
}

function dbFetchTempReceipts($fromDate = '', $toDate = '', $status = '', $user = '', $portfolio = false)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $params = [];
    $conditionSQL = '';
    if ($user && $portfolio) {
        $conditions[] = ' (temp_pmpr_property.pmpr_portfolio=' . addSQLParam(
            $params,
            $user
        ) . ' OR pmpr_property.pmpr_portfolio=' . addSQLParam($params, $user) . ') ';
    }

    if ($fromDate) {
        $conditions[] = 'transactionDate >= CONVERT(datetime, ' . addSQLParam($params, $fromDate) . ', 103)';
    }

    if ($toDate) {
        $conditions[] = 'transactionDate <= CONVERT(datetime, ' . addSQLParam($params, $toDate) . ', 103)';
    }

    if ($status != '') {
        $conditions[] = 'processStatus=' . addSQLParam($params, $status);
    }

    if (count($conditions ?? []) > 0) {
        $conditionSQL = 'WHERE ' . implode(' AND ', $conditions);
    }

    $sql = "
		SELECT
			r.receiptID,
			r.propertyID,
			r.leaseID,
			r.debtorID,
			r.accountID,
			r.paymentType,
			r.drawerName,
			r.depositName,
			r.chequeNumber,
			r.bankID,
			r.bsbNumber,
			CONVERT(char(10), r.transactionDate, 103) as transactionDate,
			r.transactionAmount,
			r.receiptNumber,
			r.createUser,
			CONVERT(char(10), r.createDate, 103) as createDate,
			r.processStatus
		FROM
			temp_receipt r
			LEFT JOIN pmpr_property
            ON  r.propertyID = pmpr_property.pmpr_prop
            LEFT JOIN temp_pmpr_property
            ON  r.propertyID = temp_pmpr_property.pmpr_prop
		{$conditionSQL}";

    return $dbh->executeSet($sql, false, true, $params);
}

/**
 * @modified 2012-09-06 Added LEFT JOIN selection for the pmbk_bank (for column pmbk_acc_name) [Morph]
 **/
function dbFetchTempReceiptByID($receiptID)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $sql = '
		SELECT
			a.receiptID,
			a.propertyID,
			a.leaseID,
			a.debtorID,
			a.accountID,
			b.pmbk_acc_name as accountName,
			a.paymentType,
			a.drawerName,
			a.depositName,
			a.chequeNumber,
			a.bankID,
			a.bsbNumber,
			CONVERT(char(10), a.transactionDate, 103) as transactionDate,
			a.transactionAmount,
			a.receiptNumber,
			a.createUser,
			CONVERT(char(10), a.createDate, 103) as createDate,
			a.processStatus,
			a.clearDays,
			a.clearDate
		FROM
			temp_receipt a
		LEFT JOIN
			pmbk_bank as b on (b.pmbk_code = a.accountID)
		WHERE
			a.receiptID=?';

    return $dbh->executeSingle($sql, [$receiptID]);
}

function dbFetchTempReceiptLines($receiptID)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $sql = '
		SELECT
			receiptID,
			batchNumber,
			lineNumber,
			propertyID,
			leaseID,
			CONVERT(char(10), transactionDate, 103) as transactionDate,
			transactionType,
			invoiceNumber,
			accountID,
			temp_receipt_line.description,
			amount,
			appliedAmount,
			serial,
			pmpr_is_ledger,
			trans_amt
		FROM
			temp_receipt_line
		JOIN pmpr_property on pmpr_prop = propertyID
		JOIN ar_transaction on batch_nr = batchNumber and lineNumber = batch_line_nr
		WHERE
			receiptID=?';

    return $dbh->executeSet($sql, false, true, [$receiptID]);
}

/**
 * @modified 2012-05-31: Added column unallocatedID from the selection. [Morph]
 **/
function dbFetchTempReceiptUnallocated($receiptID)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $sql = '
		SELECT
			receiptID,
			propertyID,
			leaseID,
			accountID,
			description,
			unallocatedAmount,
			taxAmount,
			taxCode,
			unallocatedID
		FROM
			temp_receipt_unallocated
		WHERE
			receiptID=?';

    return $dbh->executeSet($sql, false, true, [$receiptID]);
}

function dbDeleteTempReceipt($receiptID)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $sql = 'DELETE FROM temp_receipt WHERE receiptID = ?';

    return $dbh->executeNonQuery2($sql, [$receiptID]);
}

function dbUpdateTempReceiptStatus($receiptID, $processStatus, $comments = '')
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $sql = 'UPDATE temp_receipt SET processStatus=?, comments=? WHERE receiptID=?';

    return $dbh->executeNonQuery2($sql, [$processStatus, $comments, $receiptID]);
}

/**
 * @modified 2012-04-23: Removed (******** andrew) AND trans_amt != 0
 * @modified 2012-06-19: changed A aliases to A, B and C.
 * @modified 2012-06-26 removed trans_amt < 0 OR before the ref_1 != 'cancellation'
 * @modified 2012-08-13 : added trans_amt != 0 (to filter out 0 receipts) and changed payment_type check to IN()
 **/
function dbFetchReceiptsForDate($bankID, $transactionDate)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);

    $sql = "
            SELECT
                ar_transaction.batch_nr AS batchNumber,
                ar_transaction.batch_line_nr AS lineNumber,
                payment_type AS paymentType,
                artr_create_date AS createDate,
                artr_create_time AS createTime,
                debtor_code AS debtorID,
                ref_1 AS chequeNumber,
                ref_2 AS propertyID,
                ref_4 AS leaseID,
                trans_amt * -1 AS transactionAmount,
                A.drawer as drawerName,
                A.bank_code as bankID,
                A.bank_branch as bsbNumber
            FROM
                ar_transaction
            LEFT JOIN ar_payment_detail A on (A.batch_nr = ar_transaction.batch_nr) AND (A.batch_line_nr = ar_transaction.batch_line_nr)
            WHERE
            payment_type IN ('Q','C','K','B','D')
			AND bank = ?
			AND trans_date = CONVERT(datetime, ?, 103)
			AND trans_amt <> 0
            AND trans_type = 'CSH'
			AND (
				spare_1 NOT LIKE 'DIS:%'
				OR spare_1 IS NULL
				)
		    AND (
                (payment_type = 'Q' AND A.drawer IS NOT NULL)
                OR (payment_type = 'Q' AND A.drawer IS NULL AND trans_amt > 0)
                OR (payment_type != 'Q')
                )
		ORDER BY
			payment_type DESC,
			artr_create_date ASC,
			artr_create_time ASC";

    return $dbh->executeSet($sql, false, true, [$bankID, $transactionDate]);
}

/**
 * @modified 2012-07-04: Added condition for $propertyID and $leaseID due to bug # 632246 [Morph]
 **/
function dbFetchReceiptsFiltered($debtorID = '', $propertyID = '', $leaseID = '', $fromDate = '', $toDate = '')
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $params = [];
    $conditionSQL = '';
    if ($propertyID != '') {
        $conditionSQL .= ' AND (ref_2 = ' . addSQLParam($params, $propertyID) . ') ';
    }

    if ($leaseID != '') {
        $conditionSQL .= ' AND (ref_4 = ' . addSQLParam($params, $leaseID) . ') ';
    }

    if ($debtorID != '') {
        $conditionSQL .= ' AND (debtor_code = ' . addSQLParam($params, $debtorID) . ') ';
    }

    if ($fromDate != '') {
        $conditionSQL .= ' AND trans_date >= CONVERT(datetime, ' . addSQLParam($params, $fromDate) . ', 103) ';
    }

    if ($toDate != '') {
        $conditionSQL .= ' AND trans_date <= CONVERT(datetime, ' . addSQLParam($params, $toDate) . ', 103) ';
    }

    $sql = "SELECT batch_nr AS batchNumber,
            bank,
			batch_line_nr AS lineNumber,
			payment_type AS paymentType,
			trans_type AS transactionType,
			CONVERT(char(10), trans_date, 103) AS transactionDate,
			artr_create_date AS createDate,
			artr_create_time AS createTime,
			debtor_code AS debtorID,
			ref_1 AS  chequeNumber,
			ref_2 AS propertyID,
			ref_4 AS leaseID,
			COALESCE(SUM(pmxd_alloc_amt), 0) * - 1 AS transactionAmount,
			artr_tax_amt * -1 AS taxAmount,
			artr_net_amt * -1 AS netAmount,
			pmrc_zero_receipt_no  as zeroReceiptNumber,
			(SELECT B.drawer FROM ar_payment_detail B WHERE (B.batch_nr = ar_transaction.batch_nr) AND (B.batch_line_nr = ar_transaction.batch_line_nr)) as drawerName,
			(SELECT B.bank_code FROM ar_payment_detail B WHERE (B.batch_nr = ar_transaction.batch_nr) AND (B.batch_line_nr = ar_transaction.batch_line_nr)) as bankID,
			(SELECT B.bank_branch FROM ar_payment_detail B WHERE (B.batch_nr = ar_transaction.batch_nr) AND (B.batch_line_nr = ar_transaction.batch_line_nr)) as bsbNumber,
			(CASE WHEN spare_1 ='' THEN 'Zero Receipt' ELSE spare_1 END) AS receiptNumber
		FROM
			ar_transaction
    LEFT JOIN pmxd_ar_alloc ON batch_nr = pmxd_f_batch and batch_line_nr = pmxd_f_line
    LEFT JOIN  pmrc_receipt x
        ON
          x.pmrc_batch = ar_transaction.batch_nr
               AND x.pmrc_line = ar_transaction.batch_line_nr and x.pmrc_bank = ar_transaction.bank and pmrc_zero_receipt_no is not null
		WHERE
			trans_type = 'CSH'
			AND ((spare_1 NOT LIKE 'DIS:%') OR (spare_1 IS NULL))
			AND ar_transaction.artr_journal = 0
			{$conditionSQL}
			GROUP BY batch_nr ,batch_line_nr , payment_type , trans_type , trans_date , artr_create_date , artr_create_time , debtor_code,
				ref_1 , ref_2 , ref_4 , trans_amt , artr_tax_amt , artr_net_amt , spare_1 , bank,pmrc_zero_receipt_no
			ORDER BY ar_transaction.batch_nr DESC, trans_date DESC";

    return $dbh->executeSet($sql, false, true, $params);
}

function dbCheckZeroReceipt($batchNumber, $lineNumber)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $sql = "SELECT pmrc_zero_receipt_no as zeroReceiptNumber
            FROM pmrc_receipt r
			LEFT JOIN ar_transaction t
				ON r.pmrc_batch = t.batch_nr
			WHERE r.pmrc_batch IN
			(SELECT pmxd_f_batch
				FROM pmxd_ar_alloc
				WHERE pmxd_t_batch = ?
					AND pmxd_t_type = 'CRE'
					)
			AND r.pmrc_zero_receipt_no IS NOT NULL
			AND t.spare_1 NOT LIKE 'DIS:%'
			AND pmrc_line = ?";

    return $dbh->executeSet($sql, false, true, [$batchNumber, $lineNumber]);
}

function dbFetchTempReceiptsForDate($bankID, $transactionDate)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $sql = "
		SELECT
			receiptID,
			batchNumber,
			lineNumber,
			paymentType,
			createDate,
			createTime,
			debtorID,
			chequeNumber,
			propertyID,
			leaseID,
			transactionAmount,
			drawerName,
			bankID,
			bsbNumber
		FROM
			temp_receipt
		WHERE
			((paymentType = 'Q') OR (paymentType = 'C'))
			AND (bankID = ?)
			AND (transactionDate = CONVERT(datetime, ?, 103))
			AND (transactionAmount < 0)
		ORDER BY
			paymentType DESC,
			createDate ASC,
			createTime ASC";

    return $dbh->executeSet($sql, false, true, [$bankID, $transactionDate]);
}

function dbCountCheques($bankID, $transactionDate)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $sql = "
		SELECT
			COUNT(DISTINCT ref_1) AS chequeNumber
		FROM
			ar_transaction
		WHERE
			payment_type = 'Q'
			AND (bank = ?)
			AND (trans_date = CONVERT(datetime, ?, 103))
			AND (trans_amt < 0)";

    return $dbh->executeScalar($sql, [$bankID, $transactionDate]);
}

function dbFetchBankDetails($bankID)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $sql = '
		SELECT
			pmbk_bsb AS bsbNumber,
			pmbk_code AS bankID,
			pmbk_acc_name AS bankAccountName,
			pmbk_bank_name AS bankName,
			pmbk_country AS bankCountry,
			pmbk_bsb AS bsbNumber,
			pmbk_short_name AS shortName,
			pmbk_db_id AS userID,
			pmbk_account AS accountID,
			pmbk_db_remit AS paymentName,
			pmbk_debit_id AS debitUserID
		FROM
			pmbk_bank
		WHERE
			pmbk_code = ?';

    return $dbh->executeSingle($sql, [$bankID]);
}

function dbUpdateTransactionForReceipt($transaction)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    extract($transaction);
    $sql = '
		UPDATE
			ar_transaction
		SET
			spare_1 = ?,
			artr_mod_user = ?,
			artr_mod_date = GETDATE(),
			artr_mod_time = GETDATE()
		WHERE
			batch_nr=?
			AND batch_line_nr=?';
    $params = [$receiptNumber, $modifiedUser, $batchNumber, $lineNumber];

    return $dbh->executeNonQuery2($sql, $params);
}

function dbGetAllocatedByBatch($batchNumber, $lineNumber)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $sql = "
		SELECT
			pmxd_alloc_nr AS allocationNumber,
			pmxd_f_batch AS fromBatchNumber,
			pmxd_f_line AS fromLineNumber,
			pmxd_t_batch AS toBatchNumber,
			pmxd_t_line AS toLineNumber,
			CONVERT(char(10), pmxd_alloc_dt , 103) as transactionDate,
			pmxd_alloc_amt AS transactionAmount,
			pmxd_f_type as fromType,
			pmxd_t_type as toType,
			pmxd_s_bank AS bankID,
			pmxd_prop AS propertyID,
			pmxd_lease AS leaseID,
			pmxd_acc AS accountID,
			pmxd_s_debtor as debtorID,
			pmxd_create_user as createUser,
			CONVERT(char(10), pmxd_create_date , 103) as createDate,
			pmxd_mod_user AS modifiedUser,
			CONVERT(char(10), pmxd_mod_date , 103) as modifiedDate,
			pmxd_tax_amt AS taxAmount,
			pmxd_year as transactionYear,
			pmxd_period as transactionPeriod,
			pmxd_exmgmtfee as managementFee,
			pmpr_is_ledger as is_ledger
		FROM
			pmxd_ar_alloc
			JOIN pmpr_property ON pmpr_prop = pmxd_prop
		WHERE
			(pmxd_f_batch = ?)
			AND (pmxd_f_line = ?)
			AND (
				(pmxd_f_type = 'CSH' AND pmxd_t_type = 'INV') OR
				(pmxd_f_type='CSH' AND pmxd_t_type='CRE') OR
				(pmxd_f_type='INV' AND pmxd_t_type='CRE') OR
				(pmxd_f_type='CRE' AND pmxd_t_type='INV')
			)";

    return $dbh->executeSet($sql, false, true, [$batchNumber, $lineNumber]);
}

function dbGetAllocatedAndUnallocatedByBatch($batchNumber, $lineNumber)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $sql = "SELECT DISTINCT bankID,propertyID
            FROM (SELECT DISTINCT
                pmxd_s_bank AS bankID,
                pmxd_prop AS propertyID
            FROM
                pmxd_ar_alloc
            WHERE
                (pmxd_f_batch = ?)
                AND (pmxd_f_line = ?)
                AND (
                    (pmxd_f_type = 'CSH' AND pmxd_t_type = 'INV') OR
                    (pmxd_f_type='CSH' AND pmxd_t_type='CRE') OR
                    (pmxd_f_type='INV' AND pmxd_t_type='CRE') OR
                    (pmxd_f_type='CRE' AND pmxd_t_type='INV')
                )
                UNION
            SELECT DISTINCT pmuc_s_bank AS bankID, pmuc_prop AS bankID
            FROM pmuc_unall_csh
            WHERE pmuc_batch = ?
              AND pmuc_line = ?) x";

    return $dbh->executeSet($sql, false, true, [$batchNumber, $lineNumber, $batchNumber, $lineNumber]);
}

function dbGetAllocatedToBatch($batchNumber, $lineNumber, $noType = false)
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);

    $noTypeSql = $noType ? '' : " AND ((pmxd_f_type = 'CSH' AND pmxd_t_type = 'INV') OR (pmxd_f_type='CSH' AND pmxd_t_type='CRE'))";

    $sql = "SELECT
							  pmxd_alloc_nr AS allocationNumber,
							 pmxd_f_batch AS fromBatchNumber,
							 pmxd_f_line AS fromLineNumber,
							 pmxd_t_batch AS toBatchNumber,
							 pmxd_t_line AS toLineNumber,
							 CONVERT(char(10), pmxd_alloc_dt , 103) as transactionDate,
							 pmxd_alloc_amt AS transactionAmount,
							 pmxd_f_type as fromType,
							 pmxd_t_type as toType,
							 pmxd_s_bank AS bankID,
							 pmxd_prop AS propertyID,
							 pmxd_lease AS leaseID,
							 pmxd_acc AS accountID,
							 pmxd_s_debtor as debtorID,
							 pmxd_create_user as createUser,
							 CONVERT(char(10), pmxd_create_date , 103) as createDate,
							 pmxd_mod_user AS modifiedUser,
							 CONVERT(char(10), pmxd_mod_date , 103) as modifiedDate,
							 pmxd_tax_amt AS taxAmount,
							 pmxd_year as transactionYear,
							 pmxd_period as transactionPeriod,
							 pmxd_exmgmtfee as managementFee,
							 spare_1 as receiptNumber
				FROM pmxd_ar_alloc
				JOIN ar_transaction on batch_nr = pmxd_f_batch and batch_line_nr = pmxd_f_line
				WHERE (pmxd_t_batch = ?)
				AND (pmxd_t_line = ?)
				{$noTypeSql}
				";

    return $dbh->executeSet($sql, false, true, [$batchNumber, $lineNumber]);
}

function dbAmountPerProperty($propertyID, $batchNumber, $lineNumber)
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);

    $sql = 'SELECT COALESCE(SUM(pmxd_alloc_amt),0) FROM pmxd_ar_alloc WHERE (pmxd_f_batch = ?)
            AND (pmxd_f_line = ?) AND (pmxd_prop = ?)';
    $allocated = $dbh->executeScalar($sql, [$batchNumber, $lineNumber, $propertyID]);

    $sql = 'SELECT COALESCE(SUM(pmuc_amt),0) FROM pmuc_unall_csh WHERE (pmuc_batch = ?)
            AND (pmuc_line = ?) AND (pmuc_prop = ?)';
    $unallocated = $dbh->executeScalar($sql, [$batchNumber, $lineNumber, $propertyID]);
    $allocated *= -1;
    $unallocated *= -1;

    return round($allocated, 2) + round($unallocated, 2);
}

function dbBankBalanceForProperty($propertyID, $transactionDate = null)
{
    global $dbh, $clientDB;
    $dbh->selectDatabase($clientDB);

    if ($transactionDate == null) {
        $transactionDate = TODAY;
    }

    $sql = "SELECT COALESCE(SUM(pmxd_alloc_amt),0) amount  FROM pmxd_ar_alloc WHERE (pmxd_f_type = 'CSH') AND (pmxd_prop = ?) AND (pmxd_acc<>'') AND (pmxd_alloc_dt <=  CONVERT(datetime, ?, 103))";
    $allocated = $dbh->executeScalar($sql, [$propertyID, $transactionDate]);

    $sql = 'SELECT COALESCE(SUM(pmuc_amt),0) as amount FROM pmuc_unall_csh WHERE (pmuc_prop = ?) AND (pmuc_rcpt_dt <= CONVERT(datetime, ?, 103))';
    $unallocated = $dbh->executeScalar($sql, [$propertyID, $transactionDate]);

    $sql = "SELECT COALESCE(SUM(pmxc_alloc_amt),0) as amount FROM  pmxc_ap_alloc WHERE (pmxc_prop = ?) AND (pmxc_alloc_dt <=  CONVERT(datetime, ?, 103)) AND (pmxc_f_type = 'PAY') AND (pmxc_acc <> '')";
    $payments = $dbh->executeScalar($sql, [$propertyID, $transactionDate]);

    $allocated = round($allocated, 2);
    $unallocated = round($unallocated, 2);
    $payments = round($payments, 2);

    return $payments - ($allocated + $unallocated);
}

function dbBankDebitStatus($bankID)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $sql = 'SELECT pmbk_dr_bar FROM pmbk_bank WHERE pmbk_code=?';

    return $dbh->executeScalar($sql, [$bankID]);
}

function dbGetProperty($propertyID)
{
    global $dbh, $clientDB;
    $dbh->selectDatabase($clientDB);
    $sql = 'SELECT pmpr_prop AS propertyID,
			  pmpr_name AS propertyName,
			  pmpr_street AS propertyAddress,
			  pmpr_city AS propertyCity,
			  pmpr_postcode AS propertyPostCode,
			  pmpr_state AS propertyState,
			  pmpr_owner AS propertyOwner,
			  pmpr_remit_off AS propertyRemittanceOffice,
			  pmpr_portfolio AS propertyManager,
			  pmpr_agent AS propertyAgent,
			  pmpr_prop_type AS propertyType,
			  pmpr_prop_group AS propertyReportType,
			  pmpr_c_acc AS propertyMgmtFeeAccount,
			  pmpr_c_type AS propertyMgmtFeeMethod,
			  pmpr_c_val AS propertyMgmtFeeAmount,
			  pmpr_c_pct AS propertyMgmtFeePercentage,
			  pmpr_or_basis AS propertyGSTBasis,
			  pmpr_or_gstper AS propertyGSTReportPeriod,
			  pmpr_p_lease AS propertyParkingLeased,
			  pmpr_p_lic AS propertyParkingLicensed,
			  pmpr_p_cas AS propertyParkingCasual,
			  pmpr_delete as propertyInactive,
			  pmpr_retail AS propertyRetail,
			  pmpr_strata AS propertyStrata,
			  CONVERT(char(10), pmpr_m_upd_dt, 103)  AS propertyChargeDate,
			  pmpr_is_ledger as is_ledger
			  FROM pmpr_property
			  WHERE pmpr_prop = ?';

    return $dbh->executeSingle($sql, [$propertyID]);
}

function dbCheckIsAfterBankRecon($bankID, $cancellationDate)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $sql = 'SELECT pmba_lreco as recordDate
    FROM pmba_reco_hdr r
    WHERE
    r.pmba_bank = ?';
    $recordDate = $dbh->executeScalar($sql, [$bankID]);

    return dayDiff($recordDate, $cancellationDate) > 0;
}

function dbCheckBatchLine($propertyID, $leaseID, $batchNo, $lineNo, $transactionType)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $sql = 'SELECT pmxd_t_batch
		FROM  pmxd_ar_alloc


		WHERE pmxd_prop=?
		AND pmxd_lease=?
		AND ((pmxd_t_line=? AND pmxd_t_batch=? AND pmxd_t_type = ?)
			OR (pmxd_f_line=? AND pmxd_f_batch=? AND pmxd_f_type = ?))
		';
    $params = [$propertyID, $leaseID, $lineNo, $batchNo, $transactionType, $lineNo, $batchNo, $transactionType];

    return $dbh->executeScalar($sql, $params);
}

/**
 * Check if Batch and Line number exist in ar_transaction.
 *
 * @param  string  $propertyID  Mandatory.
 * @param  string  $leaseID  Mandatory.
 * @param  int  $batchNo  Mandatory.
 * @param  int  $lineNo  Mandatory.
 * @return int
 *
 * <AUTHOR> Reyes
 *
 * @since 2012-09-11
 **/
function dbCheckBatchLineTransaction($propertyID, $leaseID, $batchNo, $lineNo)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $sql = 'SELECT batch_nr
		FROM  ar_transaction
		WHERE batch_nr=?
			AND batch_line_nr=?
			AND ref_2=?';

    return $dbh->executeScalar($sql, [$batchNo, $lineNo, $propertyID]);
}

function dbUpdateTransactionClosed($trans, $period, $year)
{
    global $clientDB, $dbh;

    $dbh->selectDatabase($clientDB);

    if (isset($trans['newAccountCode']) && isset($trans['newDescription'])) {
        extract($trans);

        if (! isset($username)) {
            return 'you must set the transaction[username]';
        }

        if (! isset($newAccountCode) || ! isset($newFromDate) || ! isset($newToDate)) {
            return 'Check the data in $transaction';
        }

        $fundID = dbGetPropertyFundIDFromAccount($propertyID, $newAccountCode);

        $sql = 'UPDATE ar_transaction
			SET ref_3 = ?,
				description = ?,
				spare_date_1 = CONVERT(datetime, ?,103),
				spare_date_2 = CONVERT(datetime, ?,103),
				artr_mod_user = ?,
				artr_mod_date = GETDATE(),
				artr_mod_time = GETDATE(),
			    fund = ?
			WHERE
				ref_2 = ?
			AND batch_nr = ?
			AND batch_line_nr = ?';
        $params = [
            $newAccountCode,
            $newDescription,
            $newFromDate,
            $newToDate,
            $username,
            $fundID,
            $propertyID,
            $batchNumber,
            $lineNumber,
        ];
        $result_x = $dbh->executeNonQuery2($sql, $params, true);

        $sql1 = 'UPDATE  pmxd_ar_alloc
			SET  pmxd_acc = ?,
				 pmxd_mod_user = ?,
				 pmxd_mod_date = GETDATE(),
				 pmxd_mod_time = GETDATE(),
			     fund = ?
			WHERE
				(pmxd_lease=?)
			AND (pmxd_prop=?)
			AND (pmxd_t_batch=?)
			AND (pmxd_t_line=?)';
        $params = [$newAccountCode, $username, $fundID, $leaseID, $propertyID, $batchNumber, $lineNumber];
        $result_y = $dbh->executeNonQuery2($sql1, $params, true);

        if ($result_x && $result_y) {
            return true;
        }
    } else {
        return false;
    }


}

/**
 * Updates the transaction date of the AR transaction submitted by the client. Being used in OneOffChargeProcess.
 *
 * @param  int  $transactionID  Mandatory. The Transaction ID.
 * @param  mixed  $transactionDate  Mandatory. The new transaction date.
 *
 * <AUTHOR> Reyes
 *
 * @since 2012-05-29
 **/
function dbUpdateTransactionApRecoveredById($transactionID, $data)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $sql = 'UPDATE temp_ar_transaction SET
		ap_recovered=?,
		modifyUser=?,
		modifyDate=GETDATE()
		WHERE transactionID=?';
    $dbh->executeNonQuery2($sql, [$data, $_SESSION['un'], $transactionID]);
}

/**
 * Updates the transaction date of the AR transaction submitted by the client. Being used in OneOffChargeProcess.
 *
 * @param  int  $transactionID  Mandatory. The Transaction ID.
 * @param  mixed  $transactionDate  Mandatory. The new transaction date.
 *
 * <AUTHOR> Reyes
 *
 * @since 2012-05-29
 **/
function dbUpdateTransactionDateById($transactionID, $transactionDate)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);

    $sql = 'UPDATE temp_ar_transaction SET
		transactionDate=CONVERT(DATETIME, ?, 103),
		modifyUser=?,
		modifyDate=GETDATE()
		WHERE transactionID=?';
    $dbh->executeNonQuery2($sql, [$transactionDate, $_SESSION['un'], $transactionID]);
}

/**
 * Updates the from date of the AR transaction submitted by the client. Being used in OneOffChargeProcess.
 *
 * @param  int  $transactionID  Mandatory. The Transaction ID.
 * @param  mixed  $fromDate  Mandatory. The new transaction date.
 *
 * <AUTHOR> Reyes
 *
 * @since 2012-05-29
 **/
function dbUpdateTransactionFromDateById($transactionID, $fromDate)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $sql = 'UPDATE temp_ar_transaction SET
		fromDate=CONVERT(DATETIME, ?, 103),
		modifyUser=?,
		modifyDate=GETDATE()
		WHERE transactionID=?';
    $dbh->executeNonQuery2($sql, [$fromDate, $_SESSION['un'], $transactionID]);
}

/**
 * Updates the to date of the AR transaction submitted by the client. Being used in OneOffChargeProcess.
 *
 * @param  int  $transactionID  Mandatory. The Transaction ID.
 * @param  mixed  $toDate  Mandatory. The new transaction date.
 *
 * <AUTHOR> Reyes
 *
 * @since 2012-05-29
 **/
function dbUpdateTransactionToDateById($transactionID, $toDate)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $sql = 'UPDATE temp_ar_transaction SET
		toDate=CONVERT(DATETIME, ?, 103),
		modifyUser=?,
		modifyDate=GETDATE()
		WHERE transactionID=?';
    $dbh->executeNonQuery2($sql, [$toDate, $_SESSION['un'], $transactionID]);
}

/**
 * Update the description of the unallocated cash of an unprocessed receipt.
 *
 * @param  int  $receiptID  Mandatory. The receipt ID.
 * @param  int  $unallocatedID  Mandatory. The unallocated ID to uniquely identify the receipt.
 * @param  string  $description  Mandatory. The updated description.
 *
 * <AUTHOR> Reyes
 *
 * @since 2012-05-31
 **/
function dbUpdateTempReceiptDescription($receiptID, $unallocatedID, $description)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $sql = 'UPDATE temp_receipt_unallocated SET description=?
                WHERE receiptID=? AND unallocatedID=?';
    $dbh->executeNonQuery2($sql, [$description, $receiptID, $unallocatedID]);
}

function dbGetRetailCalendarPeriods($propertyID)
{
    global $dbh, $clientDB;
    $dbh->selectDatabase($clientDB);
    $sql = 'SELECT
				pmcp_year as calendarYear,
				pmcp_period AS calendarPeriod,
				 convert(char(10), pmcp_r_start_dt,103) AS calendarStartDate,
				 convert(char(10), pmcp_r_end_dt,103) AS calendarEndDate,
				pmcp_closed AS calendarPeriodClosed
				FROM pmcp_prop_cal
				WHERE (pmcp_prop = ?)
				ORDER BY pmcp_year, pmcp_period';

    return $dbh->executeSet($sql, false, true, [$propertyID]);
}

function dbGetLastRetailCalendarYear($propertyID)
{
    global $dbh, $clientDB;
    $dbh->selectDatabase($clientDB);
    $sql = 'SELECT MAX(pmcp_year) AS currentYear
									FROM pmcp_prop_cal
									WHERE (pmcp_prop = ?)
				';

    return $dbh->executeScalar($sql, [$propertyID]);
}

function dbUpdateAccountCodeTransaction($transaction)
{
    global $clientDB, $dbh;

    $dbh->selectDatabase($clientDB);

    if (isset($transaction['newAccountCode'])) {
        extract($transaction);

        if (! isset($username)) {
            return 'you must set the transaction[username]';
        }

        if (strlen($username) > 10) {
            $username = substr($username, 0, 10);
        }

        $fundID = dbGetPropertyFundIDFromAccount($propertyID, $accountID);

        $sql = 'UPDATE  pmxd_ar_alloc
		SET  pmxd_acc = ?,
			 pmxd_mod_user = ?,
			 pmxd_mod_date = GETDATE(),
			 pmxd_mod_time = GETDATE(),
			 fund = ?
		WHERE
			(pmxd_lease=?)
		AND (pmxd_prop=?)
		AND (pmxd_t_batch=?)
		AND (pmxd_t_line=?)';

        $params = [$newAccountCode, $username, $fundID, $leaseID, $propertyID, $batchNumber, $lineNumber];

        return $dbh->executeNonQuery2($sql, $params, true);
    } else {
        echo 'something went wrong with dbUpdateAccountCodeTransaction -';
        exit;
    }
}

/**
 * @param  $bankID  integer Mandatory.
 * @param  $displayOption  string Mandatory. Which will be converted to integer and placed into a WHERE clause.
 *
 * @fromDate string Mandatory. mm/dd/yy format.
 *
 * @toDate string Mandatory. mm/dd/yy format.
 *
 * @fromRange integer Optional.
 *
 * @toRange integer Optional.
 *
 * <AUTHOR> Reyes
 *
 * @since 2013-01-16
 **/
function dbReceiptsPrinting(
    $bankID,
    $displayOption,
    $fromDate,
    $toDate,
    $fromRange = '',
    $toRange = '',
    $selected = '',
    $otherOptions = ''
) {
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $params = [];
    $fromRange = intval($fromRange);
    $toRange = intval($toRange);
    switch ($displayOption) {
        case 'ungenerated':
            $whereClause[] = "t1.pmrc_printed='0'";
            break;
        case 'generated':
            $whereClause[] = "t1.pmrc_printed='1'";
            break;
        case 'both':
            $whereClause[] = "t1.pmrc_printed IN ('0','1')";
            break;
    }

    $sql = '
		SELECT
		    t1.pmrc_payer_name payerName,
		    t1.pmrc_payer_address payerAddress,
		    t1.pmrc_payer_suburb payerSuburb,
		    t1.pmrc_payer_state payerState,
			t1.pmrc_batch batchNumber,
			t1.pmrc_line lineNumber,
			t1.pmrc_bank bankID,
			t1.pmrc_receipt_no receiptNumber,
			t1.pmrc_zero_receipt_no zeroReceiptNumber,
			t2.ref_2 propertyID,
			t2.ref_4 leaseID,
			t2.artr_create_user userID,
			CONVERT(char(10), t2.trans_date, 103) transactionDate,
			( CASE when  t1.pmrc_receipt_no IS NULL THEN
            (select TOP 1 pmxd_alloc_amt from pmxd_ar_alloc x where x.pmxd_f_batch = t1.pmrc_batch)
            ELSE t2.trans_amt * -1 END) as totalAmount,
			t1.pmrc_printed printedReceipt,
			t5.pmbk_state bankState
		FROM
			pmrc_receipt t1
			INNER JOIN ar_transaction t2 ON
					t1.pmrc_batch = t2.batch_nr
					AND t1.pmrc_line = t2.batch_line_nr
			INNER JOIN pmbk_bank t5 ON t1.pmrc_bank = t5.pmbk_code
		WHERE
			pmrc_bank=' . addSQLParam($params, $bankID);

    $whereClause[] = ' t1.pmrc_receipt_no IS NOT NULL';
    if ($fromRange && $toRange) {
        $whereClause[] = ' t1.pmrc_receipt_no BETWEEN ' . addSQLParam($params, $fromRange) . ' AND ' . addSQLParam(
            $params,
            $toRange
        );
    }

    if ($selected) {
        $whereClause[] = ' t1.pmrc_receipt_no IN (' . addSQLParam($params, $selected) . ')';
    }

    if ($otherOptions['propertyID']) {
        $whereClause[] = ' t2.ref_2=' . addSQLParam($params, $otherOptions['propertyID']);
    }

    if ($otherOptions['leaseID']) {
        $whereClause[] = ' t2.ref_4=' . addSQLParam($params, $otherOptions['leaseID']);
    }

    $whereClause = ($whereClause && is_array($whereClause)) ? 'AND ' . implode(' AND ', $whereClause) : '';

    $sql .= " {$whereClause}
			AND trans_date BETWEEN CONVERT(datetime, " . addSQLParam($params, $fromDate) . ', 103)
			AND CONVERT(datetime, ' . addSQLParam($params, $toDate) . ', 103)
		ORDER BY
			receiptNumber DESC,
			transactionDate DESC';

    return $dbh->executeSet($sql, false, true, $params);
}

/**
 * For use in Receipt Printing
 *
 * <AUTHOR> Reyes
 *
 * @since 2013-01-20
 **/
function dbGetReceiptBasic($batchNumber, $lineNumber)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $sql = '
		SELECT
		    t1.spare_1 AS receiptNumber,
			t1.debtor_code debtorCode,
			t1.payment_type paymentType,
			CONVERT(char(10), t1.trans_date, 103) transactionDate,
			t1.trans_amt * -1 amount,
			t2.pmco_email email,
			t2.pmco_name companyName,
			t2.pmco_street companyStreet,
			t2.pmco_city companyCity,
			t2.pmco_state companyState,
			t2.pmco_postcode companyPostCode,
			t5.pmbk_state bankState,
			t1.artr_create_user as username,
			CONVERT(char(10), t1.artr_create_date, 103) completedDate,
			t1.ref_2 propertyID
		FROM
			ar_transaction t1
			INNER JOIN pmco_company t2 ON (t2.pmco_code=t1.debtor_code)
			INNER JOIN pmbk_bank t5 ON (t1.bank = t5.pmbk_code)
		WHERE
			t1.batch_nr=?
			AND t1.batch_line_nr=?';

    return $dbh->executeSingle($sql, [$batchNumber, $lineNumber]);
}

/**
 * For use in Receipt Printing
 *
 * <AUTHOR> Reyes
 *
 * @since 2013-01-20
 **/
function dbGetReceiptDrawer($batchNumber, $lineNumber)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $sql = '
		SELECT
			drawer
		FROM
			ar_payment_detail
		WHERE
			batch_nr=?
			AND batch_line_nr=?';

    return $dbh->executeScalar($sql, [$batchNumber, $lineNumber]);
}

/**
 * For use in Receipt Printing
 *
 * @param  $accountName  integer Mandatory.
 *
 * <AUTHOR> Reyes
 *
 * @since 2013-01-20
 **/
function dbGetReceiptTrustAccount($accountNumber)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $sql = '
		SELECT
			pmbk_acc_name accountName,
			pmbk_acc_desc accountDescription
		FROM
			pmbk_bank
		WHERE
			pmbk_code=?';

    return $dbh->executeSingle($sql, [$accountNumber]);
}

/**
 * For use in Receipt Printing
 *
 * <AUTHOR> Reyes
 *
 * @since 2013-01-20
 **/
function dbGetLicensee()
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $sql = '
		SELECT
			cms_sy_sign
		FROM
			cms_system';

    return $dbh->executeScalar($sql);
}

function dbGetAgentLicenseNumber()
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $sql = '
		SELECT
			cms_license_num as agentLicenseNum
		FROM
			cms_system';

    return $dbh->executeScalar($sql);
}

/**
 * For use in Receipt Printing
 *
 * <AUTHOR> Reyes
 *
 * @since 2013-01-20
 **/
function dbGetReceiptAddress($PropertyCode)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $sql = '
		SELECT
			t1.pmol_name companyName,
			t2.pmco_gst_no ABN,
			t2.pmco_country country,
			t1.pmol_street street,
			t1.pmol_city city,
			t1.pmol_state state,
			t1.pmol_postcode postCode,
			t1.pmol_phone phone,
			t1.pmol_fax fax,
			t1.pmol_email email
		FROM
			pmol_office_loc t1
		INNER JOIN pmpr_property t3
			ON (t3.pmpr_remit_off=t1.pmol_code)
		INNER JOIN pmco_company  t2
			ON (pmco_code = t3.pmpr_agent)
		WHERE t3.pmpr_prop = ?';

    return $dbh->executeSingle($sql, [$PropertyCode]);
}

/**
 * For use in Receipt Printing
 *
 * @param  $batchNumber  integer Mandatory.
 * @param  $lineNumber  integer Mandatory.
 *
 * <AUTHOR> Reyes
 *
 * @since 2013-01-21
 **/
function dbCheckReceiptMultipleLeases($batchNumber, $lineNumber)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $sql = '
		SELECT
			pmrc_no_leases noLeases
		FROM
			pmrc_receipt
		WHERE
			pmrc_batch=?
			AND pmrc_line=?';
    $result = $dbh->executeSet($sql, false, true, [$batchNumber, $lineNumber]);
    foreach ($result as $v) {
        if ($v['noLeases'] == 1) {
            return true;
        }
    }

    return false;
}

/**
 * For use in Receipt Printing
 *
 * <AUTHOR> Reyes
 *
 * @since 2013-01-18
 **/
function dbGetReceiptData($batchNumber, $lineNumber)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $sql = "
		SELECT
			t1.pmxd_alloc_amt * - 1 allocatedAmount,
			t1.pmxd_prop propertyID,
			t3.pmpr_name propertyName,
			t1.pmxd_lease leaseID,
			t1.pmxd_s_debtor debtorID,
			t2.pmle_name leaseName,
			CONVERT(char(10), pmxd_alloc_dt, 103) allocatedDate,
			t4.description description,
			CONVERT(char(10), t4.spare_date_1, 103) fromDate,
			CONVERT(char(10), t4.spare_date_2, 103) toDate,
			CONVERT(char(10),t5.trans_date, 103) transactionDate,
			t5.ref_1 AS referenceNumber2,
			case pmxd_bank_date when null then '' else CONVERT(char(10),t1.pmxd_bank_date, 103) end as bankDate,
			t3.pmpr_city as propertyCity,
			t3.pmpr_state as propertyState,
			t3.pmpr_postcode as propertyPostCode,
			(
				SELECT
					COUNT(*) dishons
				FROM
					pmxd_ar_alloc
				WHERE
					pmxd_f_batch = ?
					AND pmxd_f_line = ?
					AND pmxd_f_type='CSH'
					AND pmxd_t_type='INV'
					AND pmxd_alloc_amt>0
			) dishonours,
			pmle_sub_ledger_type subledgerType
		FROM
			pmxd_ar_alloc t1
			INNER JOIN pmle_lease t2 ON (t2.pmle_prop=t1.pmxd_prop AND t2.pmle_lease=t1.pmxd_lease)
			INNER JOIN pmpr_property t3 ON (t3.pmpr_prop=t1.pmxd_prop)
			INNER JOIN ar_transaction t4 ON (t4.batch_nr=t1.pmxd_t_batch AND t4.batch_line_nr=t1.pmxd_t_line)
			INNER JOIN ar_transaction t5 ON (t5.batch_nr=t1.pmxd_f_batch AND t5.batch_line_nr=t1.pmxd_f_line)
		WHERE
			pmxd_f_batch = ?
			AND pmxd_f_line = ?
			AND pmxd_f_type='CSH'
			AND pmxd_t_type IN ('INV','CRE')
		ORDER BY
			pmxd_lease,
			pmxd_prop";
    $params = [$batchNumber, $lineNumber, $batchNumber, $lineNumber];

    return $dbh->executeSet($sql, false, true, $params);
}

/**
 * For use in Receipt Printing
 *
 * @param  $batchNumber  integer Mandatory.
 * @param  $lineNumber  integer Mandatory.
 * @param  $leaseID  string Optional.
 *
 * <AUTHOR> Reyes
 *
 * @since 2013-01-22
 **/
function dbReceiptUnallocatedCash($batchNumber, $lineNumber, $leaseID = '')
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $params = [];
    $sql = '
		SELECT DISTINCT
			t1.pmuc_desc description,
			t1.pmuc_prop propertyID,
			t2.pmpr_name propertyName,
			t1.pmuc_lease leaseID,
			t3.pmle_name leaseName,
			COALESCE(SUM(pmuc_amt), 0) AS unallocatedAmount
		FROM
			pmuc_unall_csh t1
		INNER JOIN
			pmpr_property t2
				ON (t2.pmpr_prop=t1.pmuc_prop)
		INNER JOIN
			pmle_lease t3
				ON (t3.pmle_prop=t1.pmuc_prop AND t3.pmle_lease=t1.pmuc_lease)
		WHERE
			t1.pmuc_batch = ' . addSQLParam($params, $batchNumber) . '
			AND t1.pmuc_line = ' . addSQLParam($params, $lineNumber) .
        (($leaseID) ? ' AND pmuc_lease = ' . addSQLParam($params, $leaseID) : '') . '
		GROUP BY
			t1.pmuc_desc,
			t1.pmuc_prop,
			t2.pmpr_name,
			t1.pmuc_lease,
			t3.pmle_name
		ORDER BY
			t1.pmuc_lease';

    return $dbh->executeSet($sql, false, true, $params);
}

/**
 * For use in Receipt Printing
 *
 * @param  $batchNumber  integer Mandatory.
 * @param  $lineNumber  integer Mandatory.
 * @param  $propertyID  string Mandatory.
 * @param  $leaseID  string Mandatory.
 *
 * <AUTHOR> Reyes
 *
 * @since 2013-01-22
 **/
function dbReceiptAddress($batchNumber, $lineNumber, $propertyID, $leaseID)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $sql = '
		SELECT
			t2.pmle_name companyName,
			t1.pmma_street companyStreet,
			t1.pmma_city companyCity,
			t1.pmma_state companyState,
			t1.pmma_postcode companyPostCode,
			t2.pmle_description tenancyLocation
		FROM
			pmma_mail_addr t1
		INNER JOIN
			pmle_lease t2 ON (t2.pmle_lease=t1.pmma_lease and t2.pmle_prop=t1.pmma_prop)
		WHERE
			pmma_prop=?
			AND pmma_lease=?';
    $result = $dbh->executeSingle($sql, [$propertyID, $leaseID]);
    if (! $result) {
        $sql = '
			SELECT
				pmle_t_name companyName,
				pmle_street companyStreet,
				pmle_city companyCity,
				pmle_state companyState,
				pmle_postcode companyPostCode,
				pmle_description tenancyLocation
			FROM
				pmle_lease
			WHERE
				pmle_prop=?
				AND pmle_lease=?';
        $result = $dbh->executeSingle($sql, [$propertyID, $leaseID]);
    }

    if (! $result['companyName']) {
        $sql = '
		SELECT
			pmco_name AS companyName,
			pmco_street AS companyStreet,
			pmco_city AS companyCity,
			pmco_state AS companyState,
			pmco_postcode AS companyPostCode,
			pmle_description AS tenancyLocation
		FROM pmle_lease
        JOIN pmco_company
            ON pmle_debtor = pmco_code
		WHERE pmle_prop = ?
		AND pmle_lease = ?';
        $result = $dbh->executeSingle($sql, [$propertyID, $leaseID]);
    }

    return $result;
}

/**
 * @param  $batchNumber  integer Mandatory.
 * @param  $lineNumber  integer Mandatory.
 * @param  $bankID  integer Mandatory.
 *
 * <AUTHOR> Reyes
 *
 * @since 2013-01-28
 **/
function dbMarkReceipt($batchNumber, $lineNumber, $bankID)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $date = date('m/d/Y');
    $sql = "
		UPDATE
			pmrc_receipt
		SET
			pmrc_printed='1',
			pmrc_print_dt='{$date}'
		WHERE
			pmrc_batch = ?
			AND pmrc_line = ?
			AND pmrc_bank = ?";

    return $dbh->executeNonQuery2($sql, [$batchNumber, $lineNumber, $bankID]);
}

function dbGetTransactionLeases($propertyID)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $sql = '
		SELECT DISTINCT
			t.ref_2 as propertyID,
			t.ref_4 as leaseID,
			l.pmle_name as leaseName,
			l.pmle_description
		FROM
			ar_transaction t
		LEFT JOIN
			pmle_lease l ON (t.ref_4 = l.pmle_lease)
		WHERE
			t.ref_2 = l.pmle_prop
			AND t.ref_2=?';

    return $dbh->executeSet($sql, false, true, [$propertyID]);
}

function dbInsertLinkedInvoiceDocument(array $data)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $params = [];
    $valueSQL_params = [];
    $userCreated = intval($_SESSION['user_id']);
    $insertSQL = '';
    $valueSQL = '';
    if (isset($data['arBatchNumber']) && isset($data['arBatchLineNumber'])) {
        $insertSQL = ',arBatchNumber,arBatchLineNumber';
        $valueSQL = ',' . addSQLParam($valueSQL_params, $data['arBatchNumber']) . ',' . addSQLParam(
            $valueSQL_params,
            $data['arBatchLineNumber']
        );
    }

    $sql = '
        INSERT INTO
            document_invoice
            (
                property_id,
                lease_id,
                document_id,
                invoice_date,
                ' . ($data['invoiceNo'] ? 'invoice_number,' : '') . "
                linked,
                date_created,
                user_created,
                date_updated,
                user_updated,
                attachAR,
                attachOwnerR
                {$insertSQL}
            )
            VALUES
            (
                " . addSQLParam($params, $data['propertyID']) . ',
                ' . addSQLParam($params, $data['leaseID']) . ',
                ' . addSQLParam($params, $data['documentID']) . ',
                CONVERT(datetime,' . addSQLParam($params, $data['invoiceDate']) . ',103),
                ' . ($data['invoiceNo'] ? addSQLParam($params, $data['invoiceNo']) . ',' : '') . '
                ' . addSQLParam($params, $data['linked']) . ',
                GETDATE(),
                ' . addSQLParam($params, $userCreated) . ',
                null,
                null,
                ' . addSQLParam($params, ($data['attachAR'] + 0)) . ',
                ' . addSQLParam($params, ($data['attachOwnerR'] + 0)) . "
                {$valueSQL} " . addSQLParam($params, $valueSQL_params, false) . '
            )';

    return $dbh->executeNonQuery2($sql, $params);
}

function dbGetLinkedInvoiceDocument(array $data)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $params = [];
    $sql = 'SELECT DISTINCT(documentTitle), MIN(document_id) AS documentID
                FROM document_invoice
                LEFT JOIN documents ON documentID=document_invoice.document_id
                WHERE property_id = ' . addSQLParam($params, $data['propertyID']) . '
                AND lease_id = ' . addSQLParam($params, $data['leaseID']) . '
                AND invoice_date = CONVERT(datetime, ' . addSQLParam($params, $data['invoiceDate']) . ',103) ' .
        ($data['invoiceNo'] ? ' AND invoice_number = ' . addSQLParam(
            $params,
            $data['invoiceNo']
        ) : ' AND invoice_number IS NULL') . '
                AND linked = 1
                GROUP BY documentTitle';

    $documentIDs = $dbh->executeSet($sql, false, true, $params);
    foreach ($documentIDs as $v) {
        $documentID[$v['documentID']] = $v['documentID'];
    }

    return $documentID;
}

function dbCountLinkedInvoiceDocument(array $data)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $params = [$data['propertyID'], $data['leaseID'], $data['invoiceDate']];

    if ($data['invoiceNo']) {
        $whereClause[] = ' invoice_number = ' . addSQLParam($params, $data['invoiceNo']);
    } else {
        $whereClause[] = ' invoice_number IS NULL';
    }

    if ($data['documentID']) {
        $whereClause[] = ' document_id = ' . addSQLParam($params, $data['documentID']);
    }

    if (isset($data['linked'])) {
        $whereClause[] = ' linked = ' . addSQLParam($params, $data['linked']);
    }

    $whereClause = ($whereClause && is_array($whereClause)) ? ' AND ' . implode(' AND ', $whereClause) : '';
    $sql = 'SELECT COUNT(DISTINCT documentTitle)
            FROM document_invoice
            LEFT JOIN documents ON documentID=document_id
            WHERE property_id = ' . addSQLParam($params, $data['propertyID']) . '
            AND lease_id = ' . addSQLParam($params, $data['leaseID']) . '
            AND invoice_date = CONVERT(datetime, ' . addSQLParam($params, $data['invoiceDate']) . ",103)
            {$whereClause}";

    return $dbh->executeScalar($sql, $params);
}

function dbChangeInvoiceDocument(array $data, $status = 0)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $params = [];
    $status = intval($status);
    $sql = 'UPDATE document_invoice
		    SET linked = ' . addSQLParam($params, $status) . ',
			    attachAR = ' . addSQLParam($params, $status) . ',
			date_updated = GETDATE(),
			    user_updated = ' . addSQLParam($params, $data['userID']) . '
		WHERE document_id = ' . addSQLParam($params, $data['documentID']) . '
			AND property_id = ' . addSQLParam($params, $data['propertyID']) . '
			AND lease_id = ' . addSQLParam($params, $data['leaseID']) . '
			AND document_id = ' . addSQLParam($params, $data['documentID']) . '
			AND invoice_date = CONVERT(datetime, ' . addSQLParam($params, $data['invoiceDate']) . ',103)' .
        ($data['invoiceNo'] ? ' AND invoice_number = ' . addSQLParam(
            $params,
            $data['invoiceNo']
        ) : 'AND invoice_number IS NULL');

    return $dbh->executeNonQuery2($sql, $params);
}

function dbGetInvoiceDocuments(
    $propertyID,
    $leaseID,
    $invoiceDate,
    $invoiceNumber = null,
    $combineDebtor = false,
    $leaseList = null,
    $includeDocumentID = true
) {
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $params = [$propertyID, $invoiceDate];

    if (is_array($leaseList) && count($leaseList ?? []) > 0) {
        $leaseList = implode("','", $leaseList);
    }

    $condition = $combineDebtor ? " AND lease_id IN ('{$leaseList}')" :
        ' AND lease_id = ' . addSQLParam($params, $leaseID);
    $where = ($invoiceNumber) ? ' AND invoice_number = ' . addSQLParam($params, $invoiceNumber) : '';

    $sql = '
		SELECT
		    DISTINCT
			' . ($includeDocumentID ? 't1.document_id documentID,' : '') . "
			t2.filename
		FROM
			document_invoice t1
		LEFT JOIN
			documents t2 ON (t2.documentID = t1.document_id)
		WHERE t1.property_id = ?
			AND invoice_date = CONVERT(datetime, ?,103)
			AND linked = 1
			AND t1.attachAR = 1
			{$condition}
			{$where}";

    return $dbh->executeSet($sql, false, true, $params);
}

function dbCheckUnallocated($batchNumber, $lineNumber)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $sql = '
		SELECT
			COUNT(*)
		FROM
			pmuc_unall_csh
		WHERE
			pmuc_batch=?
			AND pmuc_line=?
			AND pmuc_rev_dt IS NOT NULL';

    return $dbh->executeSet($sql, false, true, [$batchNumber, $lineNumber]);
}

function dbDeleteUnallocated($batchNumber, $lineNumber)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $sql = '
		DELETE FROM
			pmuc_unall_csh
		WHERE
			pmuc_batch = ?
			AND pmuc_line = ?
			AND pmuc_rev_dt IS NOT NULL';

    return $dbh->executeNonQuery2($sql, [$batchNumber, $lineNumber]);
}

function dbDeleteBankReconciliationDeposits($batchNumber, $lineNumber)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $sql = '
		DELETE FROM
			pmbb_reco_dep
		WHERE
			batch_nr=?
			AND batch_line_nr=?';

    return $dbh->executeNonQuery2($sql, [$batchNumber, $lineNumber]);
}

function dbTenantDetailList($propertyID, $tenantIDs = '')
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $params = [$propertyID];

    $additionalSql = '';
    if (is_array($tenantIDs) && count($tenantIDs ?? []) > 0) {
        $additionalSql = " AND pmle_lease IN('" . implode("','", $tenantIDs) . "')";
    }

    $sql = "SELECT DISTINCT
                pmle_prop as propertyID,
                pmle_lease as leaseID,
                pmle_name AS leaseName,
                pmle_debtor AS debtorID,
                pmle_description AS description,
                pmle_t_name AS tradingName,
                pmpr_name AS propertyName,
                pmle_bad_debt AS badDebtProvision
            FROM pmle_lease
	        LEFT OUTER JOIN pmua_unit_area
	        ON pmua_lease = pmle_lease
            AND pmle_prop = pmua_prop
	        INNER JOIN pmpr_property
	        ON pmpr_prop = pmle_prop
		WHERE pmle_prop=?
		{$additionalSql}
		ORDER BY leaseID";

    return $dbh->executeSet($sql, false, true, $params);
}

function dbGetArrearsLetterTemplateByDaysOverdue($days_overdue)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);

    $sql = 'SELECT letter_template_id template_id,
				letter_template_name template_name,
				range_start,
				range_end,
				letter_template_body template_content,
				letter_template_id,
				letter_filename,
				letter_use_docx
			FROM letter_template
			WHERE range_start <= ?
			AND CASE WHEN range_end = 0 THEN 99999 ELSE range_end END >= ?
			AND is_custom = 0
			AND letter_category_id = 1
			ORDER BY range_start';
    $res = $dbh->executeSingle($sql, [$days_overdue, $days_overdue]);

    if (! empty($res)) {
        return $res;
    } else {
        $sql = 'SELECT TOP 1 letter_template_id template_id,
					letter_template_name template_name,
					range_start,
					range_end,
					letter_template_body template_content,
					letter_template_id,
					letter_filename,
					letter_use_docx
				FROM letter_template
				WHERE letter_category_id = 1
				ORDER BY range_end DESC, letter_template_id';

        return $dbh->executeSingle($sql);
    }
}

function dbGetArrearsLetterEmailSubjectAndContentByID($id)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);

    $sql = 'SELECT letter_template_subject email_subject, letter_email_body email_content
			FROM letter_template
			WHERE letter_template_id = ?
			ORDER BY range_start';

    $row = $dbh->executeSingle($sql, [$id]);

    return [$row['email_subject'], $row['email_content']];
}

function dbGetArrearsLetterTemplates($id = 0, $is_custom = null)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $params = [];

    $where = '';
    if ($id != 0) {
        $where = ' AND letter_template_id = ' . addSQLParam($params, $id);
    }

    if ($is_custom !== null) {
        $where .= ' AND is_custom = ' . addSQLParam($params, $is_custom);
    }

    $sql = "SELECT letter_template_id template_id,
				letter_template_name template_name ,
				range_start,
				range_end,
				letter_template_body template_content,
				letter_template_id,
				letter_filename,
				letter_use_docx
			FROM letter_template
			WHERE letter_category_id = 1
			{$where}
			ORDER BY range_start";

    if ($id != 0) {
        return $dbh->executeSingle($sql, $params);
    } else {
        return $dbh->executeSet($sql, false, true, $params);
    }
}

function dbInsertLinkedInvoiceDocumentFromAP($document)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $params = [];

    extract($document);
    $userCreated = intval($_SESSION['user_id']);
    $sql = '
			INSERT INTO
				document_invoice
				(
					property_id,
					lease_id,
					document_id,
					linked,
					date_created,
					user_created,
					batchNumber,
					attachAR
				)
				VALUES
				(
					' . addSQLParam($params, $propertyID) . ',
					' . addSQLParam($params, $leaseID) . ',
					' . addSQLParam($params, $documentID) . ',
					  1,
					GETDATE(),
				   ' . addSQLParam($params, $userCreated) . ',
				   ' . addSQLParam($params, $apBatchNumber) . ',
				   1
				)';

    return $dbh->executeNonQuery2($sql, $params);
}

function dbDeleteLinkedInvoiceDocumentFromAP($document)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    extract($document);
    $sql = "DELETE FROM document_invoice WHERE batchNumber = ? AND property_id = ? AND linked = '0'";

    return $dbh->executeNonQuery2($sql, [$apBatchNumber, $propertyID]);
}

function dbGetDocumentInvoicePerBatchNumber($batchNumber, $propertyID)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $params = [];
    $sql = 'SELECT document_id,batchNumber
			FROM document_invoice WHERE batchNumber IN (' . addSQLParam($params, $batchNumber) . ')
			AND attachAR = 1 AND property_id = ' . addSQLParam(
        $params,
        $propertyID
    ) . " AND (lease_id = '' OR lease_id is NULL)";

    return $dbh->executeSet($sql, false, true, $params);
}

function dbDeleteLinkedInvoiceDocument($batchNumber, $propertyID)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $sql = "DELETE FROM document_invoice WHERE batchNumber = ?
            AND (lease_id = '' OR lease_id IS NULL) AND property_id = ?";

    return $dbh->executeNonQuery2($sql, [$batchNumber, $propertyID]);
}

function dbInsertARLinkedInvoiceDocument($document)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $params = [];
    extract($document);
    $userCreated = intval($_SESSION['user_id']);
    $sql = '
			INSERT INTO
				document_invoice
				(
					property_id,
					lease_id,
					document_id,
					linked,
					date_created,
					user_created,
					batchNumber,
					attachAR,
				 	arBatchNumber,
					arBatchLineNumber
				)
				VALUES
				(
					' . addSQLParam($params, $propertyID) . ',
					' . addSQLParam($params, $leaseID) . ',
					' . addSQLParam($params, $documentID) . ',
					0,
					GETDATE(),
				   	' . addSQLParam($params, $userCreated) . ',
				   	' . addSQLParam($params, $batchNumber) . ',
				  	' . addSQLParam($params, $attachAR) . ',
				 	' . addSQLParam($params, $arBatchNumber) . ',
					' . addSQLParam($params, $arBatchLineNumber) . '
				)';

    return $dbh->executeNonQuery2($sql, $params);
}

function dbGetArTransactionLeaseByBatch($batchNumber)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $sql = 'SELECT
		ref_4 as leaseID
		FROM ar_transaction
		WHERE batch_nr = ?';

    return $dbh->executeSet($sql, false, true, [$batchNumber]);
}


function dbAddTempTransferJournal($o)
{
    extract($o);
    global $dbh, $clientDB;
    $dbh->selectDatabase($clientDB);
    $params = [];
    $sql = 'INSERT INTO temp_ar_transfer_journal (
                debtorID,
                transactionDate,
                fromPropertyID,
                fromAccountID,
                fromDescription,
                fromFromDate,
                fromToDate,
                transactionAmount,
                fromTaxRateID,
                toPropertyID,
                toAccountID,
                toDescription,
                toFromDate,
                toToDate,
                toTaxRateID,
                status,
                createdBy,
                createdDate
            )
            VALUES
            (
                ' . addSQLParam($params, $debtorID) . ',
                CONVERT(datetime, ' . addSQLParam($params, $transactionDate) . ', 103),
                ' . addSQLParam($params, $fromPropertyID) . ',
                ' . addSQLParam($params, $fromAccountID) . ',
                ' . addSQLParam($params, $fromDescription) . ',
                CONVERT(datetime, ' . addSQLParam($params, $fromFromDate) . ', 103),
                CONVERT(datetime, ' . addSQLParam($params, $fromToDate) . ', 103),
                ' . addSQLParam($params, $transactionAmount) . ',
                ' . addSQLParam($params, $fromTaxRateID) . ',
                ' . addSQLParam($params, $toPropertyID) . ',
                ' . addSQLParam($params, $toAccountID) . ',
                ' . addSQLParam($params, $toDescription) . ',
                CONVERT(datetime, ' . addSQLParam($params, $toFromDate) . ', 103),
                CONVERT(datetime, ' . addSQLParam($params, $toToDate) . ', 103),
                ' . addSQLParam($params, $toTaxRateID) . ',
                ' . addSQLParam($params, $status) . ',
                ' . addSQLParam($params, $createdBy) . ',
                GETDATE()
            )';

    $dbh->executeNonQuery2($sql, $params);
}


function dbAddTransferJournalHistory($o)
{
    extract($o);
    global $dbh, $clientDB;
    $dbh->selectDatabase($clientDB);
    $params = [];
    $sql = 'INSERT INTO tj_history (
                tjh_trans_date,
                tjh_from_prop,
                tjh_from_lease,
                tjh_from_account,
                tjh_from_desc,
                tjh_from_fdate,
                tjh_from_tdate,
                tjh_from_amount,
                tjh_from_tax,
                tjh_from_net,
                tjh_from_gst,
                tjh_to_prop,
                tjh_to_lease,
                tjh_to_account,
                tjh_to_desc,
                tjh_to_fdate,
                tjh_to_tdate,
                tjh_to_tax,
                tjh_to_net,
                tjh_to_gst,
                tjh_created_by,
                tjh_created_date,
                tjh_source
            )
            VALUES
            (
                CONVERT(datetime, ' . addSQLParam($params, $transactionDate) . ', 103),
                ' . addSQLParam($params, $fromPropertyID) . ',
                ' . addSQLParam($params, $fromLeaseID) . ',
                ' . addSQLParam($params, $fromAccountID) . ',
                ' . addSQLParam($params, $fromDescription) . ',
                CONVERT(datetime, ' . addSQLParam($params, $fromFromDate) . ', 103),
                CONVERT(datetime, ' . addSQLParam($params, $fromToDate) . ', 103),
                ' . addSQLParam($params, $transactionAmount) . ',
                ' . addSQLParam($params, $fromTaxRateID) . ',
                ' . addSQLParam($params, $fromNetAmount) . ',
                ' . addSQLParam($params, $fromTaxAmount) . ',
                ' . addSQLParam($params, $toPropertyID) . ',
                ' . addSQLParam($params, $toLeaseID) . ',
                ' . addSQLParam($params, $toAccountID) . ',
                ' . addSQLParam($params, $toDescription) . ',
                CONVERT(datetime, ' . addSQLParam($params, $toFromDate) . ', 103),
                CONVERT(datetime, ' . addSQLParam($params, $toToDate) . ', 103),
                ' . addSQLParam($params, $toTaxRateID) . ',
                ' . addSQLParam($params, $toNetAmount) . ',
                ' . addSQLParam($params, $toTaxAmount) . ',
                ' . addSQLParam($params, $createdBy) . ",
                GETDATE(),
                'ar'
            )";

    $dbh->executeNonQuery2($sql, $params);
}

function dbGetTempTransferJournal($id)
{
    global $dbh, $clientDB;
    $dbh->selectDatabase($clientDB);

    $sql = 'SELECT
                transferJournalID,
                debtorID,
                CONVERT(char(10), transactionDate, 103) AS transactionDate,
                fromPropertyID,
                fromAccountID,
                fromDescription,
                CONVERT(char(10), fromFromDate, 103) AS fromFromDate,
                CONVERT(char(10), fromToDate, 103) AS fromToDate,
                transactionAmount,
                fromTaxRateID,
                toPropertyID,
                toAccountID,
                toDescription,
                CONVERT(char(10), toFromDate, 103) AS toFromDate,
                CONVERT(char(10), toToDate, 103) AS toToDate,
                toTaxRateID,
                status,
                createdBy,
                CONVERT(char(10), createdDate, 103) AS createdDate
            FROM temp_ar_transfer_journal
            WHERE transferJournalID = ?';

    return $dbh->executeSingle($sql, [$id]);
}

function dbSetStatusTempTransferJournal($transferJournalID, $status)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $sql = 'UPDATE
			temp_transfer_journal
		SET
			status=?,
			modifiedBy=?,
			modifiedDate=GETDATE()
		WHERE
			transferJournalID=?';
    $dbh->executeNonQuery2($sql, [$status, $_SESSION['un'], $transferJournalID]);
}

/**
 * @param  string  $propertyID  Mandatory.
 * @param  bool  $showInactive  Optional.
 * @param  bool  $chargeInterest  Optional.
 *
 * <AUTHOR> Reyes
 *
 * @since 2017-07-27
 **/
function dbGetLeaseByInterestStatus($propertyID, $showInactive = false, $chargeInterest = 1)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $params = [];

    if (is_array($propertyID) && count($propertyID ?? []) > 0) {
        $propertyID = implode("','", $propertyID);
        $whereClause[] = "t1.pmle_prop IN ('{$propertyID}')";
    } else {
        $whereClause[] = 't1.pmle_prop=' . addSQLParam($params, $propertyID);
    }

    if (empty($showInactive)) {
        $whereClause[] = 't2.pmpr_delete=0';
    }

    $whereClause[] = ($chargeInterest === 1) ? 't1.pmle_interest=1' : 't1.pmle_interest=0';
    $whereClause = implode(' AND ', $whereClause);

    $sql = "
		SELECT
			t1.pmle_lease leaseID,
			t1.pmle_name leaseName,
			t1.pmle_status leaseStatus,
			t1.pmle_prop propertyID,
			t1.pmle_description description,
			t1.pmle_l_street street
		FROM
			pmle_lease t1
		LEFT JOIN
			pmpr_property t2 ON (t2.pmpr_prop=t1.pmle_prop)
		WHERE
			{$whereClause}
		ORDER BY
			t1.pmle_status,
			t1.pmle_lease";

    return $dbh->executeSet($sql, false, true, $params);
}

function dbGetFilterResetInvNo($batchNumber, $lineNumber)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $sql = 'SELECT       COALESCE(SUM(pmxd_alloc_amt), 0)
            FROM         pmxd_ar_alloc
            WHERE (pmxd_t_batch = ? AND pmxd_t_line = ?) OR
            pmxd_f_batch = ? AND pmxd_f_line = ?';

    return $dbh->executeScalar($sql, [$batchNumber, $lineNumber, $batchNumber, $lineNumber]);
}

function dbGetInterestOnArrears($propertyIDs, $leaseIDs, $interestFrom, $interestTo)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $params = [];
    $allocSummarySql_INV_params = [];
    if (! is_array($propertyIDs)) {
        $propertyIDs = [$propertyIDs];
    }

    $rawInterestTo = $interestTo;
    [$day, $month, $year] = explode('/', $interestFrom);
    $interestFrom = "{$year}-{$month}-{$day}";
    [$day, $month, $year] = explode('/', $interestTo);
    $interestTo = "{$year}-{$month}-{$day}";

    $allocSummarySql_INV = '(SELECT pmxd_t_batch, pmxd_t_line, pmxd_acc, COALESCE(SUM(pmxd_alloc_amt), 0) as pmxd_alloc_amt, pmxd_alloc_dt
                FROM (SELECT pmxd_t_batch as pmxd_t_batch, pmxd_t_line as pmxd_t_line, pmxd_acc as pmxd_acc,
                        (CASE
                            WHEN pmxd_alloc_dt < ' . addSQLParam($allocSummarySql_INV_params, $interestFrom) . '
                            THEN DATEADD(day, -1, ' . addSQLParam($allocSummarySql_INV_params, $interestFrom) . ")
                            ELSE pmxd_alloc_dt
                            END) as pmxd_alloc_dt,
                        COALESCE(SUM(pmxd_alloc_amt), 0) as pmxd_alloc_amt
                    FROM pmxd_ar_alloc
                    WHERE pmxd_prop IN ('" . implode("','", $propertyIDs) . "')
                    AND pmxd_lease IN ('" . implode("','", $leaseIDs) . "')
                    AND pmxd_alloc_dt <= " . addSQLParam($allocSummarySql_INV_params, $interestTo) . '
                    GROUP BY pmxd_t_batch, pmxd_t_line, pmxd_acc, pmxd_alloc_dt
                    HAVING COALESCE(SUM(pmxd_alloc_amt),0) != 0
                    UNION ALL
                    SELECT pmxd_f_batch as pmxd_t_batch, pmxd_f_line as pmxd_t_line, pmxd_acc as pmxd_acc,
                        (CASE
                            WHEN pmxd_alloc_dt < ' . addSQLParam($allocSummarySql_INV_params, $interestFrom) . '
                            THEN DATEADD(day, -1, ' . addSQLParam($allocSummarySql_INV_params, $interestFrom) . ")
                            ELSE pmxd_alloc_dt
                            END) as pmxd_alloc_dt,
                        COALESCE(SUM(pmxd_alloc_amt*-1), 0) as pmxd_alloc_amt
                    FROM pmxd_ar_alloc
                    WHERE pmxd_prop IN ('" . implode("','", $propertyIDs) . "')
                    AND pmxd_lease IN ('" . implode("','", $leaseIDs) . "')
                    AND pmxd_alloc_dt <= " . addSQLParam($allocSummarySql_INV_params, $interestTo) . '
                    GROUP BY pmxd_f_batch, pmxd_f_line, pmxd_acc, pmxd_alloc_dt
                    HAVING COALESCE(SUM(pmxd_alloc_amt),0) != 0

                )
                as allocations
                GROUP BY pmxd_t_batch, pmxd_t_line, pmxd_acc, pmxd_alloc_dt)';

    $selectAndGroupBy = [
        [
            "CASE
                WHEN inv.trans_type = 'INV'
                    THEN 0
                ELSE 1
                    END",
            'transTypeOrder',
            false,
        ],
        ['inv.trans_type', 'transType', true],
        ['pmle_prop', 'propertyID', true],
        ['pmpr_name', 'propertyName', true],
        ['pmle_lease', 'leaseID', true],
        ['pmle_name', 'leaseName', true],
        ['inv.trans_date', 'transDate', true],
        ['inv.artr_gst_inv_no', 'invoiceNo', true],
        ['inv.batch_nr', 'batch', true],
        ['inv.batch_line_nr', 'batchLineNumber', true],
        [
            "(CASE
                WHEN inv.trans_type = 'INV'
                    THEN inv.due_date
                ELSE inv.trans_date
                END)",
            'dueDate',
            true,
        ],
        ['inv.ref_3', 'account', true],
        [
            "(SELECT COALESCE(SUM(alloc2.pmxd_alloc_amt), 0) FROM {$allocSummarySql_INV} " . addSQLParam(
                $params,
                $allocSummarySql_INV_params,
                false
            ) . 'alloc2
                WHERE alloc2.pmxd_t_batch = alloc.pmxd_t_batch
                AND alloc2.pmxd_t_line = alloc.pmxd_t_line
                AND alloc2.pmxd_acc = alloc.pmxd_acc
                AND alloc2.pmxd_alloc_dt < alloc.pmxd_alloc_dt
                GROUP BY alloc2.pmxd_t_batch,alloc2.pmxd_t_line,alloc2.pmxd_acc
                )',
            'totalAllocationBeforePayment',
            false,
        ],
        ['inv.trans_amt', 'invoiceTotal', true],
        [
            "(SELECT COALESCE(SUM(alloc2.pmxd_alloc_amt), 0) FROM {$allocSummarySql_INV} " . addSQLParam(
                $params,
                $allocSummarySql_INV_params,
                false
            ) . 'alloc2
                WHERE alloc2.pmxd_t_batch = alloc.pmxd_t_batch
                AND alloc2.pmxd_t_line = alloc.pmxd_t_line
                AND alloc2.pmxd_acc = alloc.pmxd_acc
                AND alloc2.pmxd_alloc_dt <= alloc.pmxd_alloc_dt
                GROUP BY alloc2.pmxd_t_batch,alloc2.pmxd_t_line,alloc2.pmxd_acc
                )',
            'dateAllocationTotal',
            false,
        ],
        [
            'inv.trans_amt + COALESCE(SUM(
                CASE
                    WHEN alloc.pmxd_alloc_dt < ' . addSQLParam($params, $interestFrom) . '
                        THEN alloc.pmxd_alloc_amt
                    ELSE 0
                        END
                ), 0)',
            'outstandingToDate',
            false,
        ],
        [
            'CASE WHEN ' . addSQLParam($params, $interestFrom) . " < (CASE
                                            WHEN inv.trans_type = 'INV'
                                                THEN inv.due_date
                                            ELSE inv.trans_date
                                            END)
                THEN (CASE
                    WHEN inv.trans_type = 'INV'
                        THEN inv.due_date
                    ELSE inv.trans_date
                    END)
                ELSE " . addSQLParam($params, $interestFrom) . ' END',
            'interestFrom',
            false,
        ],
        [addSQLParam($params, $rawInterestTo), 'interestTo', false],
        [
            'DATEDIFF(DAY, CASE WHEN ' . addSQLParam($params, $interestFrom) . " < (CASE
                                                        WHEN inv.trans_type = 'INV'
                                                            THEN inv.due_date
                                                        ELSE inv.trans_date
                                                        END)
                            THEN (CASE
                                WHEN inv.trans_type = 'INV'
                                    THEN inv.due_date
                                ELSE inv.trans_date
                                END)
                            ELSE " . addSQLParam($params, $interestFrom) . ' END, ' . addSQLParam(
                $params,
                $interestTo
            ) . ') + 1',
            'daysDiffFromAndTo',
            false,
        ],
        ['pmle_i_fixed', 'interestRate', true],
        ['alloc.pmxd_alloc_dt', 'allocationDate', true],
        ['inv.description', 'description', true],
        ['pmle_i_acc', 'interestAccount', true],
        ['pmle_debtor', 'debtorID', true],
        ['pmle_base_desc', 'interestOnArrearsDescription', true],
        ['pmle_i_grace', 'gracePeriod', true],
    ];
    $select = [];
    $groupBy = [];
    foreach ($selectAndGroupBy as $x) {
        $select[] = $x[0] . ' as ' . $x[1];
        if ($x[2]) {
            $groupBy[] = $x[0];
        }
    }

    $sql_INV = 'SELECT
               ' . implode(',', $select) . "

            FROM ar_transaction inv

            JOIN pmle_lease
                ON pmle_prop = inv.ref_2 AND pmle_lease = inv.ref_4

            JOIN pmpr_property
                ON pmpr_prop = pmle_prop

            JOIN pmlia_l_interest
                ON pmlia_property = pmle_prop
                AND pmlia_lease = pmle_lease
                AND pmlia_account = inv.ref_3


            LEFT JOIN {$allocSummarySql_INV} " . addSQLParam($params, $allocSummarySql_INV_params, false) . "alloc
                ON (alloc.pmxd_t_batch = inv.batch_nr
                AND inv.batch_line_nr = alloc.pmxd_t_line)

            WHERE inv.trans_type IN ('INV', 'CRE')
            AND inv.ref_2 IN ('" . implode("','", $propertyIDs) . "')
            AND pmle_lease IN ('" . implode("','", $leaseIDs) . "')
            AND (CASE
                WHEN inv.trans_type = 'INV'
                    THEN inv.due_date
                ELSE inv.trans_date
                END) <= DATEADD(day, -(pmle_i_grace), " . addSQLParam($params, $interestTo) . ')
            AND (
                    (alloc.pmxd_alloc_dt IS NOT NULL
                        AND alloc.pmxd_alloc_dt <= ' . addSQLParam($params, $interestTo) . ')
                    OR alloc.pmxd_alloc_dt IS NULL
                )

            GROUP BY alloc.pmxd_t_batch,
                     alloc.pmxd_t_line,
                     alloc.pmxd_acc,' . implode(',', $groupBy) . '
            HAVING inv.trans_amt + COALESCE(SUM(
            CASE
                WHEN alloc.pmxd_alloc_dt < ' . addSQLParam($params, $interestFrom) . '
                    THEN alloc.pmxd_alloc_amt
                ELSE 0
                    END
            ), 0) != 0';

    $selectAndGroupByUnallocatedCash = [
        ['2', 'transTypeOrder', false],
        ["'Unallocated Cash'", 'transType', false],
        ['pmpr_prop', 'propertyID', true],
        ['pmpr_name', 'propertyName', true],
        ['pmle_lease', 'leaseID', true],
        ['pmle_name', 'leaseName', true],
        ['pmuc_rcpt_dt', 'transDate', true],
        ['0', 'invoiceNo', false],
        ['pmuc_batch', 'batch', true],
        ['pmuc_line', 'batchLineNumber', true],
        ['pmuc_rcpt_dt', 'dueDate', true],
        ['pmuc_acc', 'account', true],
        ['pmuc_amt', 'totalAllocationBeforePayment', false],
        ['pmuc_amt', 'invoiceTotal', true],
        ['0', 'dateAllocationTotal', false],
        ['pmuc_amt', 'outstandingToDate', false],
        [
            'CASE WHEN ' . addSQLParam(
                $params,
                $interestFrom
            ) . ' < pmuc_rcpt_dt THEN pmuc_rcpt_dt ELSE ' . addSQLParam($params, $interestFrom) . ' END',
            'interestFrom',
            false,
        ],
        [addSQLParam($params, $rawInterestTo), 'interestTo', false],
        [
            'DATEDIFF(DAY, CASE WHEN ' . addSQLParam(
                $params,
                $interestFrom
            ) . ' < pmuc_rcpt_dt THEN pmuc_rcpt_dt ELSE ' . addSQLParam(
                $params,
                $interestFrom
            ) . ' END, ' . addSQLParam($params, $interestTo) . ') + 1',
            'daysDiffFromAndTo',
            false,
        ],
        ['pmle_i_fixed', 'interestRate', true],

        ["''", 'allocationDate', false],
        ['pmuc_desc', 'description', true],
        ['pmle_i_acc', 'interestAccount', true],
        ['pmle_debtor', 'debtorID', true],
        ['pmle_base_desc', 'interestOnArrearsDescription', true],
        ['pmle_i_grace', 'gracePeriod', true],
    ];
    $select = [];
    $groupBy = [];
    foreach ($selectAndGroupByUnallocatedCash as $x) {
        $select[] = $x[0] . ' as ' . $x[1];
        if ($x[2]) {
            $groupBy[] = $x[0];
        }
    }

    $sqlUnallocatedCash = 'SELECT
               ' . implode(',', $select) . "

            FROM pmuc_unall_csh inv

            JOIN pmle_lease
                ON pmle_prop = pmuc_prop AND pmle_lease = pmuc_lease

            JOIN pmpr_property
                ON pmpr_prop = pmle_prop

            JOIN pmlia_l_interest
                ON pmlia_property = inv.pmuc_prop
                AND pmlia_lease = inv.pmuc_lease
                AND pmlia_account = inv.pmuc_acc

            WHERE pmuc_prop IN ('" . implode("','", $propertyIDs) . "')
            AND pmuc_lease IN ('" . implode("','", $leaseIDs) . "')
            AND pmuc_rev_dt IS NULL
            GROUP BY " . implode(',', $groupBy);

    $sql = $sql_INV . ' UNION ' . $sqlUnallocatedCash . ' ORDER BY  propertyID,leaseID,transTypeOrder, artr_gst_inv_no,
        batch, batchLineNumber, dueDate, ref_3, allocationDate, daysDiffFromAndTo DESC';

    return $dbh->executeSet($sql, false, true, $params);
}

function dbInsertInterestDetails($data)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);

    if (count($data ?? []) > 0) {
        $sql = 'INSERT INTO pmliad_interest_details (
                pmliad_batch,
                pmliad_prop,
                pmliad_lease,
                pmliad_inv,
                pmliad_description,
                pmliad_due_date,
                pmliad_account,
                pmliad_interest_from,
                pmliad_interest_to,
                pmliad_date_diff,
                pmliad_payment_date,
                pmliad_rate,
                pmliad_original_amount,
                pmliad_paid_amount,
                pmliad_outstanding_amount,
                pmliad_interest_calculated,
                pmliad_amount_applied,
                history_id)
                VALUES ';

        foreach ($data as $row) {
            $params = [];
            $insertVal = '(
                ' . addSQLParam($params, $row['pmliad_batch']) . ',
                ' . addSQLParam($params, $row['pmliad_prop']) . ',
                ' . addSQLParam($params, $row['pmliad_lease']) . ',
                ' . addSQLParam($params, $row['pmliad_inv']) . ',
                ' . addSQLParam($params, $row['pmliad_description']) . ',
                ' . ($row['pmliad_due_date'] ? 'CONVERT(datetime, ' . addSQLParam(
                $params,
                $row['pmliad_due_date']
            ) . ', 103)' : 'NULL') . ",
                '" . $row['pmliad_account'] . "',
                " . ($row['pmliad_interest_from'] ? 'CONVERT(datetime, ' . addSQLParam(
                $params,
                $row['pmliad_interest_from']
            ) . ', 103)' : 'NULL') . ',
                ' . ($row['pmliad_interest_to'] ? 'CONVERT(datetime, ' . addSQLParam(
                $params,
                $row['pmliad_interest_to']
            ) . ', 103)' : 'NULL') . ',
                ' . $row['pmliad_date_diff'] . ',
                ' . ($row['pmliad_payment_date'] ? 'CONVERT(datetime, ' . addSQLParam(
                $params,
                $row['pmliad_payment_date']
            ) . ', 103)' : 'NULL') . ',
                ' . addSQLParam($params, $row['pmliad_rate']) . ',
                ' . addSQLParam($params, $row['pmliad_original_amount']) . ',
                ' . addSQLParam($params, $row['pmliad_paid_amount']) . ',
                ' . addSQLParam($params, $row['pmliad_outstanding_amount']) . ',
                ' . addSQLParam($params, $row['pmliad_interest_calculated']) . ',
                ' . addSQLParam($params, $row['pmliad_amount_applied']) . ',
                ' . addSQLParam($params, $row['history_id']) . '
            )';
            $dbh->executeNonQuery2($sql . $insertVal, $params, true);
        }
    }
}

function dbGetInterestDetails($historyID)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);

    $sql = 'SELECT * FROM pmliad_interest_details
             WHERE history_id = ?
             ORDER BY pmliad_prop DESC, pmliad_lease, pmliad_batch, pmliad_interest_from, pmliad_interest_to';

    return $dbh->executeSet($sql, false, true, [$historyID]);
}

function dbPropertyGroupListWithInterest($filter = '', $showInactive = true)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $params = [];
    $whereClause = '';

    if ($filter) {
        $whereClause = ' AND pmpr_portfolio=' . addSQLParam($params, $filter);
        if (is_array($filter)) {
            $whereClause = ' AND pmpr_portfolio IN (' . addSQLParam($params, $filter) . ')';
        }

        if (! $showInactive) {
            $whereClause .= ' AND pmpr_delete=0';
        }
    } elseif (! $showInactive) {
        $whereClause .= ' AND  pmpr_delete=0';
    }

    $sql = "
        SELECT DISTINCT
            pmpr_prop AS propertyID,
            pmpr_name AS propertyName,
            pmpr_delete as propertyStatus
        FROM
            pmpr_property
        JOIN pmle_lease ON pmle_prop = pmpr_prop
        WHERE pmle_interest = 1
        {$whereClause}
        ORDER BY
            pmpr_delete ASC,
            pmpr_prop ASC";

    return $dbh->executeSet($sql, false, true, $params);
}

function dbGetGstInvNo($invoiceNumber)
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);
    $sql = 'SELECT
			  batch_nr, batch_line_nr
            FROM
                ar_transaction
            WHERE
                artr_gst_inv_no=?';

    return $dbh->executeSet($sql, false, true, [$invoiceNumber]);
}

function dbGetGstInvNoWithJoin($invoiceNumber)
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);
    $sql = 'SELECT
                ar_transaction.batch_nr, ar_transaction.batch_line_nr
            FROM
                ar_transaction
            INNER JOIN
                pmxd_ar_alloc
            ON
                ar_transaction.batch_nr = pmxd_ar_alloc.pmxd_t_batch
            AND
                ar_transaction.batch_line_nr = pmxd_ar_alloc.pmxd_t_line
            WHERE
                ar_transaction.artr_gst_inv_no = ?';

    return $dbh->executeSet($sql, false, true, [$invoiceNumber]);
}

function dbGetBatchPropertiesAndLeases($batchNumber, $is_energytec)
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);

    if ($is_energytec) {
        $sql = 'SELECT ref_2 AS propertyID, ref_4 AS leaseID, MIN(trans_date) AS transactionDate, MIN(due_date) AS dueDate, pmle_name AS leaseName
                FROM ar_transaction
                JOIN pmle_lease
                  ON ref_2 = pmle_prop
                  AND ref_4 = pmle_lease
                WHERE batch_nr = ?
                GROUP BY ref_2, ref_4, pmle_name
                ORDER BY ref_2, ref_4, pmle_name';
        $params = [$batchNumber];
    } else {
        $sql = '
            (SELECT
                temp_ocr_ar_pm.property_code AS propertyID,
                temp_ocr_ar_line_pm.lease_code AS leaseID,
                MAX(temp_ocr_ar_pm.transaction_date) as transactionDate,
                temp_ocr_ar_pm.due_date AS dueDate,
                pmle_lease.pmle_name AS leaseName
            FROM temp_ocr_ar_pm
            JOIN temp_ocr_ar_line_pm ON temp_ocr_ar_pm.id = temp_ocr_ar_line_pm.temp_ocr_ar_pm_id
            JOIN pmle_lease
                ON temp_ocr_ar_pm.property_code = pmle_lease.pmle_prop
                AND temp_ocr_ar_line_pm.lease_code = pmle_lease.pmle_lease
            WHERE
                temp_ocr_ar_line_pm.ar_batch_nr = ?
                AND temp_ocr_ar_pm.ai_interim = 1
            GROUP BY temp_ocr_ar_pm.property_code, temp_ocr_ar_line_pm.lease_code, pmle_lease.pmle_name, temp_ocr_ar_pm.due_date)

            UNION

            (SELECT
                property_code AS propertyID,
                lease_code AS leaseID,
                MAX(transaction_date) as transactionDate,
                due_date AS dueDate,
                pmle_name AS leaseName
            FROM temp_ocr_ar_pm
            JOIN pmle_lease
                ON property_code = pmle_prop
                AND lease_code = pmle_lease
            WHERE
                ar_batch_nr = ?
                AND ai_interim = 1
            GROUP BY property_code, lease_code, pmle_name, due_date)

            ORDER BY propertyID, leaseID, dueDate
        ';
        $params = [$batchNumber, $batchNumber];
    }

    return $dbh->executeSet($sql, false, true, $params);
}

function dbGetAiArPmBatchLinesByDueDate($batchNumber, $dueDate, $lease = '')
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);

    $params = [$batchNumber, $dueDate];

    $leaseSQL = '';
    if ($lease) {
        $leaseSQL = ' AND ref_4 = ' . addSQLParam($params, $lease);
    }

    // ## Version that uses AR transaction table to get batch line number
    $sql = "
    SELECT
        batch_line_nr as line
    FROM
        ar_transaction
    WHERE
        batch_nr = ?
        AND due_date = CONVERT(datetime, ?, 103)
        {$leaseSQL}";

    return $dbh->executeScalars($sql, $params);
}

function dbGetInvoiceChargesForArrears2(
    $propertyID,
    $leaseID,
    $debtorID,
    $invoiceDate,
    $batchNumber = null
) {
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);

    $conditionSQL = "(transactionType NOT IN ('REV','ADJ','INV') OR (transactionType = 'INV' AND invoiceNumber != 0)) AND balance != 0";
    if ($debtorID != '') {
        $conditionSQL .= " AND (debtorID = '{$debtorID}')";
    }

    if ($propertyID != '') {
        $conditionSQL .= " AND ((propertyID = '{$propertyID}'))";
    }

    if ($leaseID != '') {
        $conditionSQL .= " AND ((leaseID = '{$leaseID}'))";
    }

    $orderSQL = '';
    if ($invoiceDate == FINALDATE) {
        $orderSQL = ' DESC ';
    }

    if ($batchNumber) {
        $conditionSQL .= " AND batch_nr='{$batchNumber}'";
    }

    $sql = "SELECT *
            FROM (SELECT
              X0.batch_nr AS batchNumber,
              X0.batch_line_nr AS batchLineNumber,
              X0.artr_tax_amt AS taxAmount,
              X0.debtor_code AS debtorID,
              X0.ref_2 AS propertyID,
              X0.ref_3 AS accountID,
              X0.ref_4 AS leaseID,
              X0.ref_5 AS unitID,
              COALESCE(pmpu_desc, '') AS unitDescription,
              X0.description AS description,
              X0.artr_gst_code AS taxCode,
              X0.trans_amt AS amount,
              X0.artr_net_amt AS netAmount,
              X0.spare_date_1 AS fromDate,
              X0.spare_date_2 AS toDate,
              X0.trans_date AS transactionDate,
              X0.due_date AS dueDate,
              X0.artr_gst_inv_no AS invoiceNumber,
              X0.artr_gst_inv_dt AS invoiceDate,
              X0.trans_type AS transactionType,
              X3.pmco_name AS debtorName,
              X4.pmpr_name AS propertyName,
              X5.pmle_name AS leaseName,
              X6.pmca_name AS accountName,
              X0.trans_amt - (COALESCE(SUM(-X1.pmxd_alloc_amt), CONVERT(money, 0.00)) +
                              COALESCE(
                                  (SELECT COALESCE(SUM(pmxd_alloc_amt), 0) FROM pmxd_ar_alloc WHERE (pmxd_f_batch = batch_nr) AND (pmxd_f_line = batch_line_nr) AND (pmxd_f_type IN ('INV','CRE') AND pmxd_t_type IN ('INV','CRE')) AND (pmxd_alloc_dt <= CONVERT(datetime, '{$invoiceDate}',103)))
                                  , CONVERT(money, 0.00))) AS balance,
              CASE
                WHEN X0.trans_type IN ('INV','CRE')
                THEN COALESCE(SUM(-X1.pmxd_alloc_amt), CONVERT(money, 0.00))
                ELSE  0
              END AS totalAllocated,

              CASE
                WHEN X0.trans_type IN ('INV','CRE')
                THEN COALESCE(
                        (SELECT COALESCE(SUM(pmxd_alloc_amt), 0) FROM pmxd_ar_alloc WHERE (pmxd_f_batch = batch_nr) AND (pmxd_f_line = batch_line_nr) AND (pmxd_f_type IN ('INV','CRE') AND pmxd_t_type IN ('INV','CRE')) AND (pmxd_alloc_dt <= CONVERT(datetime, '{$invoiceDate}',103)))
                    , CONVERT(money, 0.00))
                ELSE  0
              END AS totalReallocated,

              CASE
                WHEN X0.trans_type IN ('INV','CRE')
                THEN COALESCE(SUM(-X1.pmxd_tax_amt), CONVERT(money, 0.00))
                ELSE  0
              END AS totalAllocatedTax,

              CASE
                WHEN X0.trans_type IN ('INV','CRE')
                THEN COALESCE(SUM(X1.pmxd_alloc_amt-X1.pmxd_tax_amt) * -1, CONVERT(money, 0.00))
                ELSE  0
              END AS totalAllocatedNet
            FROM dbo.ar_transaction AS X0
            LEFT OUTER JOIN dbo.pmxd_ar_alloc AS X1
              ON X0.batch_nr = X1.pmxd_t_batch
              AND X0.batch_line_nr = X1.pmxd_t_line
              AND X1.pmxd_alloc_dt <= CONVERT(datetime, '" . FINALDATE . "', 103)
              AND X1.pmxd_prop = X0.ref_2 AND X1.pmxd_lease = X0.ref_4


            LEFT OUTER JOIN dbo.pmpu_p_unit AS X2
              ON X0.ref_5 = X2.pmpu_unit
              AND X0.ref_2 = X2.pmpu_prop
            LEFT OUTER JOIN dbo.pmco_company AS X3
              ON X0.debtor_code = X3.pmco_code
            JOIN dbo.pmpr_property AS X4
              ON X0.ref_2 = X4.pmpr_prop
            JOIN dbo.pmle_lease AS X5
              ON X0.ref_2 = X5.pmle_prop
		      AND X0.ref_4 = X5.pmle_lease
            LEFT OUTER JOIN dbo.pmca_chart AS X6
              ON X0.ref_3 = X6.pmca_code

            GROUP BY X0.batch_nr,
                     X0.batch_line_nr,
                     X0.artr_tax_amt,
                     X0.debtor_code,
                     X0.ref_2,
                     X0.ref_3,
                     X0.ref_4,
                     X0.ref_5,
                     X2.pmpu_desc,
                     X0.description,
                     X0.artr_gst_code,
                     X0.trans_amt,
                     X0.artr_net_amt,
                     X0.spare_date_1,
                     X0.spare_date_2,
                     X0.trans_date,
                     X0.due_date,
                     X0.artr_gst_inv_no,
                     X0.artr_gst_inv_dt,
                     X0.trans_type,
                     X3.pmco_name,
                     X4.pmpr_name,
                     X5.pmle_name,
                     X6.pmca_name) AS x

            WHERE {$conditionSQL}
			AND (dueDate <= CONVERT(datetime, '{$invoiceDate}', 103)
			    OR (transactionType = 'CRE' AND CONVERT(date, transactionDate) <= CONVERT(datetime, '{$invoiceDate}', 103))
				OR (dueDate IS NULL AND (totalAllocated != 0 OR totalReallocated != 0) AND transactionType = 'CRE' AND CONVERT(date, transactionDate) <= CONVERT(datetime, '{$invoiceDate}', 103)))
			ORDER BY transactionDate DESC, accountID {$orderSQL}, fromDate DESC, description ";

    return $dbh->executeSet($sql);
}


function dbResetDeleteInvoicePDF($propertyID, $leaseID, $lineNumber, $invoiceNumber, $batchNum)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $params = [];
    $sql = '
		SELECT trans_date as trans_date,
		ref_2 as property_id,
		ref_4 as lease_id,
		due_date as due_date
		FROM ar_transaction
		WHERE
			 batch_line_nr in (' . addSQLParam($params, $lineNumber) . ')
			 AND batch_nr = ' . addSQLParam($params, $batchNum) . '
			AND artr_gst_inv_no in (' . addSQLParam($params, $invoiceNumber) . ')';

    return $dbh->executeSet($sql, false, true, $params);
}

function dbGetARTransactionByBatchNumber($arBatchNumber)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);

    $sql = 'SELECT
    		*
		FROM
			ar_transaction
		WHERE
			batch_nr = ?';

    return $dbh->executeSet($sql, false, true, [$arBatchNumber]);
}

function dbGetTempARTransactionByBatchNumber($tempArBatchNumber)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $sql = 'SELECT
    		*
		FROM
			temp_ar_transaction
		WHERE
			batch_nr = ?';

    return $dbh->executeSet($sql, false, true, [$tempArBatchNumber]);
}

function dbGetPaymentBatchDetails($batchNumber)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $sql = 'SELECT
		b.batch_nr AS batchNumber,
		b.bank AS bank,
		CONVERT(char(10), b.batch_date, 103) AS batchDate,
		b.trans_type AS transactionType,
		b.user_id AS userID,
		(b.batch_amt*-1) AS batchAmount,
		b.entries AS entries,
		k.pmbk_acc_name AS agentName,
		k.pmbk_db_remit AS paymentName,
		k.pmbk_db_inst AS institution,
		k.pmbk_db_id AS paymentID,
		k.pmbk_debit_id AS debitUserID,
		k.pmbk_account AS accountNumber,
		k.pmbk_db_name AS accountName,
		k.pmbk_db_wb_name AS wbAccountName,
		k.pmbk_bsb AS bsbNumber,
		k.pmbk_bpay_batch_id AS bpayBatchID,
		k.pmbk_state as bankState,
		k.pmbk_currency as bankCurrency,
		k.pmbk_bpay_user_id as bpayUserID
	FROM
		ar_batch b,
		pmbk_bank k
	WHERE
		b.bank=k.pmbk_code
		AND batch_nr=?';

    return $dbh->executeSingle($sql, [$batchNumber]);
}


function dbGetPaymentsForBatch($batchID, $line = null)
{
    global $dbh, $clientDB;
    $dbh->selectDatabase($clientDB);
    $params = [];
    $sql = "SELECT
				a.ref_1 AS referenceNumber,
				CONVERT(char(10), a.trans_date, 103) AS transactionDate,
				a.trans_amt AS transactionAmount,
				a.debtor_code AS debtorID,
				a.batch_line_nr AS lineNumber,
				a.batch_nr AS batchNumber,
				a.description,
				a.ref_5 AS customerReference,
				a.description,
				a.ref_3 as accountCode,
            e.pmca_name as accountDescription,
            p.pmpr_name AS propertyName,
		(SELECT COALESCE(SUM(x.trans_amt), 0) FROM ar_transaction x WHERE x.trans_type = 'CAN' AND x.bank = a.bank AND x.debtor_code = a.debtor_code AND x.ref_1 = a.ref_1) AS cancellationAmount
		FROM
				ar_transaction a
		LEFT JOIN pmca_chart e on  e.pmca_code = a.ref_3
		LEFT JOIN pmpr_property p on  a.ref_2 = pmpr_prop
		WHERE (a.batch_nr = " . addSQLParam($params, $batchID) . ")
        AND a.trans_type='CSH' AND a.trans_amt!=0 " .
        ($line ? ' AND a.batch_line_nr = ' . addSQLParam($params, $line) : '') . '
				ORDER BY a.ref_1, a.debtor_code';

    return $dbh->executeSet($sql, false, true, $params);
}

function dbGetChequesForBatch($batchNumber, $chequeNumber = null)
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);
    $params = [];
    $sql = "
  SELECT chequeNumber  , creditorID , transactionDate , COALESCE(SUM(totalAllocated), 0) as totalAllocated ,
  companyName , companyAddress , companyCity , companyState , companyPostCode
    FROM (
	SELECT DISTINCT pmbc_chq AS chequeNumber, debtor_code AS creditorID, CONVERT(char(10), trans_date, 103) AS transactionDate, COALESCE(SUM(pmxd_alloc_amt), 0) AS totalAllocated,
	pmco_name AS companyName,
							pmco_street AS companyAddress,
							pmco_city AS companyCity,
							pmco_state AS companyState,
							pmco_postcode AS companyPostCode
	FROM ar_transaction,pmxd_ar_alloc, pmco_company, pmbc_reco_chq
	WHERE
	pmbc_chq = pay_chk_number
	AND pmbc_bank = bank
	AND debtor_code = pmbc_creditor
	AND pmbc_cpdate IS NULL
	AND pmxd_f_batch=batch_nr
	AND pmxd_f_line=batch_line_nr
	AND trans_type='CSH'
	AND batch_nr = " . addSQLParam($params, $batchNumber) .
        ($chequeNumber ? ' AND pmbc_chq = ' . addSQLParam($params, $chequeNumber) : '') . "

	AND pmco_code = debtor_code
	GROUP BY pmbc_chq, debtor_code,trans_date, pmco_name, pmco_city, pmco_state, pmco_postcode,pmco_street


	UNION

	SELECT DISTINCT pmbc_chq AS chequeNumber, debtor_code AS creditorID, CONVERT(char(10), trans_date, 103) AS transactionDate, COALESCE(SUM(pmuc_amt), 0) AS totalAllocated,
            pmco_name AS companyName,
                                    pmco_street AS companyAddress,
                                    pmco_city AS companyCity,
                                    pmco_state AS companyState,
                                    pmco_postcode AS companyPostCode
            FROM ar_transaction,pmuc_unall_csh, pmco_company, pmbc_reco_chq
            WHERE
            pmbc_chq = pay_chk_number AND
            pmbc_bank = bank
            AND debtor_code = pmbc_creditor
            AND pmbc_cpdate IS NULL
            AND trans_type='CSH'
            AND batch_nr = " . addSQLParam($params, $batchNumber) . '
            and batch_nr = pmuc_rev_batch_nr
			and batch_line_nr = pmuc_rev_line' .
        ($chequeNumber ? ' AND pmbc_chq = ' . addSQLParam($params, $chequeNumber) : '') . '
            AND pmco_code = debtor_code
            GROUP BY pmbc_chq, debtor_code,trans_date, pmco_name, pmco_city, pmco_state, pmco_postcode,pmco_street
          ) T
          GROUP BY chequeNumber  , creditorID , transactionDate ,  companyName , companyAddress , companyCity , companyState , companyPostCode
	';

    return $dbh->executeSet($sql, false, true, $params);
}

function dbGetChequeDetails($batchNumber, $creditorID)
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);
    $sql = "	SELECT
		DISTINCT
				a.ref_5 AS customerReference,
		        p.pmpr_name AS propertyName,
				a.trans_type,
				a.ref_2 AS propertyID,
				a.description AS description,
				b.ref_1 AS referenceNumber,
		        a.ref_1 AS invoiceNumber,
				CONVERT(char(10), a.trans_date, 103) AS transactionDate,
				pmxd_alloc_amt AS amount,
				a.debtor_code AS creditorID,
				a.batch_line_nr AS lineNumber,
				a.batch_nr AS batchNumber
		FROM
				ar_transaction a, ar_transaction b, pmxd_ar_alloc c, pmpr_property p,pmbc_reco_chq
		WHERE
				b.batch_nr = ?
		AND a.ref_2 = pmpr_prop
				AND c.pmxd_t_batch = a.batch_nr
				AND c.pmxd_t_line = a.batch_line_nr
				AND c.pmxd_t_type IN('INV','CRE')
				AND c.pmxd_f_batch = b.batch_nr
				AND c.pmxd_f_line = b.batch_line_nr
				AND c.pmxd_f_type = 'CSH'
				AND a.trans_type IN ('INV', 'CRE')
				AND b.trans_type = 'CSH'
				AND pmxd_s_debtor = ?
				AND CAST(pmbc_chq as varchar) = cast(b.pay_chk_number as varchar)
		 ORDER BY a.debtor_code

					";

    $sql2 = "		SELECT
		DISTINCT
		        p.pmpr_name AS propertyName,
				b.trans_type,
				c.pmuc_desc AS description,
		        b.ref_1 AS invoiceNumber,
				CONVERT(char(10), b.trans_date, 103) AS transactionDate,
				pmuc_amt AS amount,
				b.debtor_code AS creditorID,
				b.batch_line_nr AS lineNumber,
				b.batch_nr AS batchNumber
		FROM
				 ar_transaction b, pmuc_unall_csh c, pmpr_property p,pmbc_reco_chq
		WHERE
				b.batch_nr = ?
		AND b.ref_2 = pmpr_prop
				and batch_nr = pmuc_rev_batch_nr
			    and batch_line_nr = pmuc_rev_line
				AND b.trans_type = 'CSH'
				AND pmuc_s_debtor = ?
				AND CAST(pmbc_chq as varchar) = cast(b.pay_chk_number as varchar)
		 ORDER BY b.debtor_code
					";

    return array_merge(
        $dbh->executeSet($sql, false, true, [$batchNumber, $creditorID]),
        $dbh->executeSet($sql2, false, true, [$batchNumber, $creditorID])
    );
}

function dbGetLeaseOutstandingCredits($propertyID, $leaseID)
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);
    $sql = "SELECT batch_number AS batchNumber,
              batch_line_number AS lineNumber,
              property_code AS propertyID,
              lease_code AS leaseID,
              debtor_code AS debtorID,
              transaction_date AS transactionDate,
              invoice_number AS invoiceNo,
              description,
              account_code AS accountID,
              gross_amount * -1 AS originalAmount,
              outstanding_gross * -1 AS outstandingAmount
            FROM outstanding_ar
            WHERE property_code = ?
            AND lease_code = ?
            AND transaction_type = 'CRE'
            AND gross_amount != 0";

    return $dbh->executeSet($sql, false, true, [$propertyID, $leaseID]);
}

function dbGetLeaseAllocatedCredits($propertyID, $leaseID)
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);
    $sqlCRECRE = "SELECT pmxd_t_batch AS batchNumber, pmxd_t_line AS lineNumber, ar_trans.ref_2 AS propertyID, ar_trans.ref_4 AS leaseID, ar_trans.debtor_code AS debtorID,
                pmxd_alloc_dt AS transactionDate, ar_trans.artr_gst_inv_no AS invoiceNo, ar_trans.description, ar_trans.ref_3 AS accountID,
                ar_trans.trans_amt * -1 AS originalAmount, COALESCE(SUM(pmxd_alloc_amt), 0) AS allocatedAmount,
                (COALESCE(SUM(pmxd_alloc_amt), 0) * -1 + COALESCE(outstanding_ar.outstanding_gross,0)) * -1 AS outstandingAmount,
                'CRECRE' as allocType
            FROM pmxd_ar_alloc
            JOIN ar_transaction ar_trans
                ON ar_trans.batch_nr = pmxd_t_batch AND ar_trans.batch_line_nr = pmxd_t_line
            LEFT JOIN outstanding_ar
                ON outstanding_ar.batch_number = pmxd_t_batch
                AND outstanding_ar.batch_line_number = pmxd_t_line
                AND outstanding_ar.transaction_type = 'CRE'
            AND ar_trans.batch_line_nr = pmxd_f_line
            WHERE pmxd_f_type = 'CRE'
            AND pmxd_t_type = 'CRE'
            AND pmxd_ar_alloc.pmxd_prop = ?
            AND pmxd_ar_alloc.pmxd_lease = ?
            AND ar_trans.trans_amt != 0
            GROUP BY pmxd_t_batch, pmxd_t_line, ar_trans.ref_2, ar_trans.ref_4, ar_trans.debtor_code,
                pmxd_alloc_dt, ar_trans.artr_gst_inv_no, ar_trans.description, ar_trans.ref_3, ar_trans.trans_amt, outstanding_ar.outstanding_gross
                        ";

    $sqlCREINV = "SELECT pmxd_f_batch AS batchNumber, pmxd_f_line AS lineNumber, ar_trans.ref_2 AS propertyID, ar_trans.ref_4 AS leaseID, ar_trans.debtor_code AS debtorID,
                pmxd_alloc_dt AS transactionDate, ar_trans.artr_gst_inv_no AS invoiceNo, ar_trans.description, ar_trans.ref_3 AS accountID,
                ar_trans.trans_amt * -1 AS originalAmount, COALESCE(SUM(pmxd_alloc_amt), 0) * -1 AS allocatedAmount,
                (COALESCE(SUM(pmxd_alloc_amt), 0) + COALESCE(outstanding_ar.outstanding_gross,0)) * -1 AS outstandingAmount,
                'CREINV' as allocType
            FROM pmxd_ar_alloc
            JOIN ar_transaction ar_trans
                ON ar_trans.batch_nr = pmxd_f_batch
                AND ar_trans.batch_line_nr = pmxd_f_line
            LEFT JOIN outstanding_ar
                ON outstanding_ar.batch_number = pmxd_f_batch
                AND outstanding_ar.batch_line_number = pmxd_f_line
                AND outstanding_ar.transaction_type = 'CRE'
            AND ar_trans.batch_line_nr = pmxd_f_line
            WHERE pmxd_f_type = 'CRE'
            AND pmxd_t_type = 'INV'
            AND pmxd_ar_alloc.pmxd_prop = ?
            AND pmxd_ar_alloc.pmxd_lease = ?
            AND ar_trans.trans_amt != 0
            GROUP BY pmxd_f_batch, pmxd_f_line, ar_trans.ref_2, ar_trans.ref_4, ar_trans.debtor_code,
                pmxd_alloc_dt, ar_trans.artr_gst_inv_no, ar_trans.description, ar_trans.ref_3, ar_trans.trans_amt, outstanding_ar.outstanding_gross
                        ";


    return $dbh->executeSet(
        $sqlCRECRE . ' UNION ' . $sqlCREINV,
        false,
        true,
        [$propertyID, $leaseID, $propertyID, $leaseID]
    );
}

function dbGetLeaseOutstandingCreditDetails($batchNumber, $lineNumber)
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);
    $sql = 'SELECT ar_trans.batch_nr AS batchNumber,
              ar_trans.batch_line_nr AS lineNumber,
              ar_trans.ref_2 AS propertyID,
              ar_trans.ref_4 AS leaseID,
              ar_trans.debtor_code AS debtorID,
              ar_trans.trans_date AS  transactionDate,
              ar_trans.artr_gst_inv_no AS invoiceNo,
              ar_trans.description,
              ar_trans.ref_3 AS accountID,
              COALESCE(ar_trans.trans_amt * -1, 0) AS originalAmount,
              COALESCE(outstanding_gross * -1, 0) AS outstandingAmount,
              pmpr_name AS propertyName,
              pmle_name AS leaseName,
              pmbp_bank AS bankID,
              ar_trans.artr_gst_code AS taxCode
            FROM ar_transaction ar_trans
            LEFT JOIN outstanding_ar
                ON batch_nr = batch_number
                AND batch_line_nr = batch_line_number
            JOIN pmpr_property
                ON pmpr_prop = ar_trans.ref_2
            JOIN pmle_lease
                ON pmle_lease = ar_trans.ref_4
                AND pmle_prop = ar_trans.ref_2
            JOIN pmbp_p_bank bank
                ON pmbp_prop = ar_trans.ref_2
            WHERE batch_nr = ?
            AND batch_line_nr = ?';

    return $dbh->executeSingle($sql, [$batchNumber, $lineNumber]);
}

function dbGetCreditAllocatedInvoices($batch, $line, $allocType)
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);

    if ($allocType == 'CRECRE') {
        $sql = "SELECT b.pmxd_t_batch AS batchNumber, b.pmxd_t_line AS batchLine, b.pmxd_alloc_amt AS amount
                FROM pmxd_ar_alloc a
                JOIN pmxd_ar_alloc b
                ON b.pmxd_f_batch = a.pmxd_f_batch
                    AND b.pmxd_f_line = a.pmxd_f_line
                    AND b.pmxd_t_type = 'INV'
                WHERE a.pmxd_t_batch = ?
                AND a.pmxd_t_line = ?
                AND a.pmxd_t_type = 'CRE'
                AND a.pmxd_f_type = 'CRE'";
    } else {
        $sql = "SELECT pmxd_t_batch AS batchNumber, pmxd_t_line AS batchLine, pmxd_alloc_amt AS amount
            FROM pmxd_ar_alloc
            WHERE pmxd_f_batch = ?
            AND pmxd_f_line = ?
            AND pmxd_t_type = 'INV'";
    }

    return $dbh->executeSet($sql, false, true, [$batch, $line]);
}

function dbDeleteCreditInvoiceAllocations($batch, $line, $allocType)
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);

    if ($allocType == 'CRECRE') {
        $sql = "SELECT pmxd_f_batch, pmxd_f_line FROM pmxd_ar_alloc
            WHERE pmxd_f_type = 'CRE'
            AND pmxd_t_type = 'CRE'
            AND pmxd_t_batch = ?
            AND pmxd_t_line = ?";
        $creditAllocations = $dbh->executeSet($sql, false, true, [$batch, $line]);

        foreach ($creditAllocations as $creditAllocation) {
            $sql = 'DELETE
            FROM pmxd_ar_alloc
            WHERE pmxd_f_batch = ?
            AND pmxd_f_line = ?';
            $dbh->executeNonQuery2($sql, [$creditAllocation['pmxd_f_batch'], $creditAllocation['pmxd_f_line']]);
        }
    } else {
        $sql = "DELETE
            FROM pmxd_ar_alloc
            WHERE pmxd_f_batch = ?
            AND pmxd_f_line = ?
            AND pmxd_t_type = 'INV'";
        $dbh->executeNonQuery2($sql, [$batch, $line]);
    }
}

function dbGetSubLedgerMailingAddress($propertyID, $leaseID)
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);

    $sql = 'SELECT
			  pmco_name AS mailingName,
			  pmco_street AS mailingAddress,
			  pmco_city AS mailingCity,
			  pmco_state AS mailingState,
			  pmco_country AS mailingCountry,
			  pmco_postcode AS mailingPostCode
			  FROM pmle_lease
			  JOIN pmco_company on pmco_code = pmle_debtor
			  WHERE pmle_prop=?
			  AND pmle_lease=?';

    return $dbh->executeSingle($sql, [$propertyID, $leaseID]);
}

function dbReceiptsInfo($batchID)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $sql = "
		SELECT pmxd_alloc_amt * - 1 AS amount , pmxd_t_type , artr_gst_inv_no, ref_3 as account, description,
			ref_2 as prop , ref_4 as lease , due_date, CONVERT(char(10),trans_date, 103) AS trans_date
		from pmxd_ar_alloc
		LEFT JOIN ar_transaction on batch_nr = pmxd_t_batch and batch_line_nr = pmxd_t_line
		where pmxd_f_batch = ?
		AND pmxd_f_type != 'REV'";

    return $dbh->executeSet($sql, false, true, [$batchID]);
}

function getDistinctPropertyFromLease($tenantIDs)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $sql = "SELECT DISTINCT pmle_prop from pmle_lease
			where pmle_lease IN ('" . implode("','", $tenantIDs) . "')";
    $res = $dbh->executeSet($sql);

    $ret = [];
    foreach ($res as $row) {
        $ret[] = $row['pmle_prop'];
    }

    return $ret;
}

function dbSelectARDocumentInvoiceByBatch($batchNumber, $lineNumber = null, $propertyID = null)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $params = [];

    $where = ($lineNumber != null) ? ' WHERE arBatchNumber = ' . addSQLParam($params, $batchNumber) . '
	    AND arBatchLineNumber  = ' . addSQLParam($params, $lineNumber) : 'WHERE arBatchNumber = ' . addSQLParam(
        $params,
        $batchNumber
    );
    $and = ($propertyID != null) ? ' AND property_id = ' . addSQLParam($params, $propertyID) : '';

    $sql = "SELECT DISTINCT arBatchNumber,document_id FROM document_invoice {$where} {$and}";

    return $dbh->executeSet($sql, false, true, $params);
}

function dbSelectARDocumentsByDocumentId($documentID)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $sql = 'SELECT * FROM documents
		WHERE documentID = ?';

    return $dbh->executeSingle($sql, [$documentID]);
}

function dbDeleteARInvoiceAndCreditDocument($propertyID, $documentID, $batchNumber, $lineNumber)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $params = [];
    $sql = 'DELETE FROM document_invoice
            WHERE document_id = ' . addSQLParam($params, $documentID) . ' AND arBatchNumber = ' . addSQLParam(
        $params,
        $batchNumber
    ) . '
              AND arBatchLineNumber = ' . addSQLParam($params, $lineNumber) . '
             AND property_id = ' . addSQLParam($params, $propertyID);

    return $dbh->executeNonQuery2($sql, $params);
}

function dbUpdateARTransactionDocument($transaction)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $params = [];

    extract($transaction);

    $sql = 'UPDATE ar_transaction
		SET
			artr_mod_date = GETDATE(),
			artr_mod_time = GETDATE(),
			artr_mod_user = ' . addSQLParam($params, $updateUser) . '
            WHERE
			batch_nr = ' . addSQLParam($params, $batchNumber);

    return $dbh->executeNonQuery2($sql, $params);
}

function dbCheckDocumentIsStillUsing($documentID)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $sql = 'SELECT count(document_id) FROM document_invoice
            WHERE document_id = ?';

    return $dbh->executeScalar($sql, [$documentID]);
}

function dbGetTempTransactionInvoicesToBeSentOnApprovalByBatch($batchNumber)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $sql = 'SELECT DISTINCT propertyID, leaseID, invoiceNumber, dueDate
			FROM temp_ar_transaction WHERE sendOnApproval = 1 AND batchNumber = ?';

    return $dbh->executeSet($sql, false, true, [$batchNumber]);
}

function dbInsertInterestOnArrearsHistory($data)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $params = [];

    $sql = '
	INSERT INTO interest_on_arrears_history (processed_date, created_by, from_date, to_date, filepath)
  	VALUES (GETDATE(), ' . addSQLParam($params, $data['createdBy']) . ',
  	    CONVERT(datetime,' . addSQLParam($params, $data['fromDate']) . ', 103),
  	    CONVERT(datetime,' . addSQLParam($params, $data['toDate']) . ', 103),
  	    ' . addSQLParam($params, $data['filepath']) . ')
	';

    $dbh->executeNonQuery2($sql, $params);

    return $dbh->lastID();
}

function dbUpdateInterestOnArrearsHistoryFile($id, $filepath)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);

    $sql = 'UPDATE interest_on_arrears_history SET filepath = ?
        WHERE id = ?';

    $dbh->executeNonQuery2($sql, [$filepath, $id]);

    return $dbh->lastID();
}

function dbInsertInterestOnArrearsHistoryDetails($data)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $params = [];

    $sql = '
	INSERT INTO interest_on_arrears_history_details (header_id, property_code, amount)
  	VALUES (' . addSQLParam($params, $data['headerId']) . ', ' . addSQLParam($params, $data['propertyCode']) . ',
  	    ' . addSQLParam($params, $data['amount']) . ')';

    $dbh->executeNonQuery2($sql, $params);

    return $dbh->lastID();
}

function dbGetInterestOnArrearsHistory($fromDate, $toDate)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $params = [];

    $sql = '
		SELECT
			id AS id,
			processed_date AS processed_date,
			created_by AS created_by,
			CONVERT(char(10),from_date, 103) AS from_date,
			CONVERT(char(10),to_date, 103) AS to_date,
			filepath AS filepath
		FROM interest_on_arrears_history
		WHERE
		    CAST(processed_date AS date) >= CONVERT(datetime, ' . addSQLParam($params, $fromDate) . ', 103)
			AND CAST(processed_date AS date) <= CONVERT(datetime, ' . addSQLParam($params, $toDate) . ", 103)
			AND filepath IS NOT NULL AND filepath != ''
		ORDER BY processed_date DESC
		";

    return $dbh->executeSet($sql, true, true, $params);
}

function dbGetPropertyBankAccount($propertyID)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $sql = '
		SELECT TOP 1
			pmbp_prop AS propertyID,
			pmbp_bank AS bankAccountID,
			pmbp_pay_owner AS bankAccountPayOwner,
			pmbp_partitioning AS bankPartitioning
		FROM
			pmbp_p_bank
		WHERE
			pmbp_prop = ?';

    return $dbh->executeSingle($sql, [$propertyID]);
}

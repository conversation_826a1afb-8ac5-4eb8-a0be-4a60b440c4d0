<?php

function arrearsForDatesForProperty($startDate, $endDate, $propertyID)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);

    $params = [$propertyID];
    $conditionSQL = ($startDate) ? ' AND (pmvdf_date>=CONVERT(datetime, ' . addSQLParam(
        $params,
        $startDate
    ) . ', 103))' : '';
    $conditionSQL .= ($endDate) ? ' AND (pmvdf_date<=CONVERT(datetime, ' . addSQLParam(
        $params,
        $endDate
    ) . ', 103))' : '';

    $sql = "SELECT
				COALESCE(SUM(pmvdf_bal), 0) AS arrears,
				pmvdf_lease AS leaseID
			FROM pmpr_property, pmvdf_ar_bal
			WHERE (pmvdf_prop=pmpr_prop)
				AND (pmpr_delete=0)
				AND pmpr_prop=?
				{$conditionSQL}
			GROUP BY pmvdf_lease";

    $arrearsResult = $dbh->executeSet($sql, false, true, $params);
    $arrears = mapParameters($arrearsResult, 'leaseID', 'arrears');

    $params = [$propertyID];
    $conditionSQL = ($startDate) ? ' AND (pmuc_rcpt_dt>=CONVERT(datetime, ' . addSQLParam(
        $params,
        $startDate
    ) . ', 103))' : '';
    $conditionSQL .= ($endDate) ? ' AND (pmuc_rcpt_dt<=CONVERT(datetime, ' . addSQLParam(
        $params,
        $endDate
    ) . ', 103))' : '';

    $sql = " SELECT COALESCE(SUM(pmuc_amt), 0) AS unallocated,
				pmuc_lease AS leaseID
						FROM pmpr_property,
								pmuc_unall_csh
						WHERE pmpr_prop = pmuc_prop
						AND (pmpr_delete=0)
								AND pmpr_prop=?
								{$conditionSQL}
								AND (pmuc_rev_dt IS NULL)
								GROUP BY pmuc_lease";

    $unallocatedResult = $dbh->executeSet($sql, false, true, $params);
    $unallocated = mapParameters($unallocatedResult, 'leaseID', 'unallocated');

    $result = [];
    foreach ($arrears as $leaseID => $arrear) {
        $result[$leaseID] = $arrear + $unallocated[$leaseID];
    }

    return $result;
}

function dbGetCompanyMailingAddress($propertyID)
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);

    $sql = 'SELECT
			  pmpr_prop AS propertyID,
			  pmco_name AS mailingName,
			  pmco_street AS mailingAddress,
			  pmco_city AS mailingCity,
			  pmco_state AS mailingState,
			  pmco_postcode AS mailingPostCode,
			  pmco_email As mailEmailAddress,
			  pmco_code As companyOwner
			  FROM pmco_company, pmpr_property
			  WHERE pmpr_prop=?
			  AND pmpr_owner=pmco_code
			  ';

    return $dbh->executeSingle($sql, [$propertyID]);
}

function dbGetUnpaidInvoicesToPeriod($propertyID, $period, $year, $runDate = null, $supplierID = null)
{
    global $dbh, $clientDB;
    $dbh->selectDatabase($clientDB);
    $params = [];

    $maxDueDays = dbGetParam('AP', 'MAXDUE') + 0;

    $sql = '
    SELECT
    a.trans_amt AS transactionAmount,
    a.aptr_net_amt AS transactionNet,
    a.aptr_tax_amt AS transactionTax,
    a.due_date AS dueDate,
    a.batch_nr AS batchNumber,
    a.batch_line_nr AS lineNumber,
    a.ref_1 as reference,
    CONVERT(CHAR(10), a.trans_date, 103) AS transactionDate,
    a.description AS description,
    a.ref_2 AS propertyID,
    a.ref_3 AS accountID,
    a.trans_type AS transactionType,
    CONVERT(CHAR(10), a.due_date, 103) AS dueDate,
    CONVERT(CHAR(10), a.spare_date_1, 103) AS fromDate,
    CONVERT(CHAR(10), a.spare_date_2, 103) AS toDate,
    a.creditor_code AS creditorID,
    a.aptr_year AS year,
    a.aptr_period AS period,
    a.aptr_gst_group AS taxCode,
    c.pmco_name AS creditorName,
    COALESCE(-b1.total_allocated, CONVERT(money, 0.00)) AS totalAllocated,
    COALESCE(b2.total_allocated, CONVERT(money, 0.00)) AS totalReallocated,
    COALESCE(-b1.total_tax_allocated, CONVERT(money, 0.00)) AS totalTaxAllocated,
    COALESCE(b2.total_tax_allocated, CONVERT(money, 0.00)) AS totalTaxReallocated
    FROM ap_transaction a

    LEFT OUTER JOIN (
        SELECT pmxc_t_batch, pmxc_t_line, COALESCE(SUM(pmxc_alloc_amt), 0) as total_allocated , COALESCE(SUM(pmxc_tax_amt), 0) as total_tax_allocated
        FROM pmxc_ap_alloc WHERE ' .
        (($runDate) ? ' pmxc_alloc_dt <= CONVERT(datetime, ' . addSQLParam($params, $runDate) . ', 103)' : '') .
        (($propertyID) ? ' AND (pmxc_prop = ' . addSQLParam($params, $propertyID) . ')' : '') . '
        GROUP BY pmxc_t_batch, pmxc_t_line
        ) AS b1
      ON a.batch_nr = b1.pmxc_t_batch
      AND a.batch_line_nr = b1.pmxc_t_line

    LEFT OUTER JOIN (
        SELECT pmxc_f_batch, pmxc_f_line, COALESCE(SUM(pmxc_alloc_amt), 0) as total_allocated , COALESCE(SUM(pmxc_tax_amt), 0) as total_tax_allocated
        FROM pmxc_ap_alloc WHERE ' .
        (($runDate) ? ' pmxc_alloc_dt <= CONVERT(datetime, ' . addSQLParam($params, $runDate) . ', 103)' : '') .
        (($propertyID) ? ' AND (pmxc_prop = ' . addSQLParam($params, $propertyID) . ')' : '') . "
        GROUP BY pmxc_f_batch, pmxc_f_line
        ) AS b2
      ON a.batch_nr = b2.pmxc_f_batch
      AND a.batch_line_nr = b2.pmxc_f_line

    LEFT OUTER JOIN dbo.pmco_company AS c
      ON a.creditor_code = c.pmco_code

    WHERE
    	a.trans_type IN ('INV','CRE') " .
        (($propertyID) ? ' AND (a.ref_2 = ' . addSQLParam($params, $propertyID) . ')' : '') .
        (($supplierID) ? ' AND a.creditor_code = ' . addSQLParam($params, $supplierID) : '') .
        (($maxDueDays > 0) ? " AND a.due_date <= DATEADD(day, {$maxDueDays}, CONVERT(datetime, " . addSQLParam(
            $params,
            $runDate
        ) . ', 103))' : '') . '
    AND ((a.aptr_year = ' . addSQLParam($params, $year) . '
    AND a.aptr_period <= ' . addSQLParam($params, $period) . ') OR (a.aptr_year < ' . addSQLParam($params, $year) . '))
    GROUP BY
        a.trans_amt,
        a.due_date,
        a.batch_nr,
        a.batch_line_nr,
        a.ref_1,
        a.trans_date,
        a.description,
        a.ref_2,
        a.ref_3,
        a.trans_type,
        a.due_date,
        a.spare_date_1,
        a.spare_date_2,
        a.creditor_code,
        a.aptr_year,
        a.aptr_period,
        a.aptr_gst_group,
        c.pmco_name,
        b1.total_allocated,
        b2.total_allocated,
        b1.total_tax_allocated,
        b2.total_tax_allocated,
      	a.aptr_net_amt,
      	a.aptr_tax_amt

    ORDER BY
    a.due_date ASC,
    a.trans_date ASC,
    a.creditor_code,
    a.ref_1,
    a.description
    ';

    return $dbh->executeSet($sql, false, true, $params);
}

function dbGetPropertyManagerDetails($propertyID)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);

    $sql = "
		SELECT
			t2.pmzz_par_type type,
			t1.pmpr_portfolio managerID,
			t2.pmzz_desc managerName,
			t3.pmzz_desc as managerEmail,
			t4.pmzz_desc as managerMobileNumber
		FROM
			pmpr_property t1
			INNER JOIN pmzz_param t2 ON (t2.pmzz_code=t1.pmpr_portfolio)
			LEFT JOIN pmzz_param t3 ON (t3.pmzz_code=t1.pmpr_portfolio and t3.pmzz_par_type = 'PORTMEMAIL')
		    LEFT JOIN pmzz_param t4 ON (t4.pmzz_code=t1.pmpr_portfolio and t4.pmzz_par_type = 'PORTMOBILE')
		WHERE
			t1.pmpr_prop=?
			AND t2.pmzz_par_type IN ('PORTMGR','PORTTIT')";
    $result = $dbh->executeSet($sql, false, true, [$propertyID]);
    if ($result) {
        foreach ($result as $v) {
            $type = $v['type'];
            $propertyManager[$type]['managerID'] = $v['managerID'];
            $propertyManager[$type]['managerName'] = $v['managerName'];
            $propertyManager[$type]['managerEmail'] = $v['managerEmail'];
            $propertyManager[$type]['managerMobileNumber'] = $v['managerMobileNumber'];
        }

        return $propertyManager;
    }


}

function dbGetOutstanding($propertyID = '', $leaseID = '', $debtorID = '', $startDate = '', $endDate = '')
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);
    $params = [$leaseID, $propertyID, $endDate];
    $conditionSQL = "(trans_type <> 'REV') AND (trans_type <> 'ADJ')";
    if ($debtorID != '') {
        $conditionSQL .= ' AND (debtor_code = ' . addSQLParam($params, $debtorID) . ')';
    }

    if ($propertyID != '') {
        $conditionSQL .= ' AND ((ref_2 = ' . addSQLParam($params, $propertyID) . ") OR (trans_type = 'CSH'))";
    }

    if ($leaseID != '') {
        $conditionSQL .= ' AND ((ref_4 = ' . addSQLParam($params, $leaseID) . ") OR (trans_type = 'CSH'))";
    }

    $conditionSQL .= ' AND (trans_date BETWEEN CONVERT(datetime, ' . addSQLParam(
        $params,
        $startDate
    ) . ', 103) AND CONVERT(datetime, ' . addSQLParam($params, $endDate) . ', 103))';

    $sql = "SELECT batch_nr AS batchNumber,
				batch_line_nr AS batchLineNumber,
				artr_tax_amt AS taxAmount,
				debtor_code AS debtorID,
				ref_2 AS propertyID,
				ref_3 AS accountID,
				ref_4 AS leaseID,
				description AS description,
				artr_gst_code AS taxCode,
				trans_amt AS amount,
				CONVERT(char(10), spare_date_1, 103) AS fromDate,
				CONVERT(char(10), spare_date_2, 103) AS toDate,
				artr_net_amt AS netAmount,
				CONVERT(char(10), trans_date, 103) AS transactionDate,
				artr_gst_inv_no as invoiceNumber,
				CONVERT(char(10), artr_gst_inv_dt, 103) as invoiceDate,
				trans_type AS transactionType,
		        0 AS invoiceTotalReceipted,
				0 AS invoiceTotalCredited,
				0 AS invoiceTotalAdjusted,
				0 AS creditTotalReceipted,
				0 AS creditTotalAdjusted,
				0 AS creditTotalAllocated,
				0 AS invoiceTotalReceiptedOpposite,
				0 AS invoiceTotalCreditedOpposite,
				0 AS invoiceTotalAdjustedOpposite,
				0 AS creditTotalReceiptedOpposite,
				0 AS creditTotalAdjustedOpposite,
				0 AS creditTotalAllocatedOpposite,
				(SELECT COALESCE(SUM(pmuc_amt), 0) AS total_unallocated FROM pmuc_unall_csh WHERE (pmuc_lease=? AND pmuc_prop=? AND pmuc_unall_csh.pmuc_batch = RTRIM(ar_transaction.batch_nr)) AND (pmuc_unall_csh.pmuc_line = ar_transaction.batch_line_nr) AND (pmuc_rcpt_dt <= CONVERT(datetime, ?, 103))) AS unallocated,
				(SELECT TOP 1 pmuc_lease FROM pmuc_unall_csh WHERE (pmuc_unall_csh.pmuc_batch = RTRIM(ar_transaction.batch_nr)) AND (pmuc_unall_csh.pmuc_line = ar_transaction.batch_line_nr)) AS pmuc_lease
				FROM ar_transaction
				WHERE {$conditionSQL}
				 ORDER BY spare_date_1 DESC, ref_3, description";

    $existing = $dbh->executeSet($sql, false, true, $params);
    $params = [$endDate];
    $pmxdConditionSQL = '';
    if ($propertyID != '') {
        $pmxdConditionSQL .= ' AND pmxd_prop = ' . addSQLParam($params, $propertyID);
    }

    if ($leaseID != '') {
        $pmxdConditionSQL .= ' AND pmxd_lease = ' . addSQLParam($params, $leaseID);
    }

    $pmxd_sql = "SELECT pmxd_alloc_amt , pmxd_t_batch ,pmxd_t_line , pmxd_f_type , pmxd_t_type , pmxd_alloc_dt  ,
         pmxd_f_batch , pmxd_f_line FROM pmxd_ar_alloc WHERE (pmxd_alloc_dt  <= CONVERT(datetime, ? ,103)) {$pmxdConditionSQL}";

    $t_batch = [];
    $f_batch = [];
    $execute_pmxd = $dbh->executeSet($pmxd_sql, false, true, $params);
    foreach ($execute_pmxd as $row) {
        $t_batch[$row['pmxd_t_batch']][$row['pmxd_t_line']][$row['pmxd_f_type']][$row['pmxd_t_type']][] = $row['pmxd_alloc_amt'];
        $f_batch[$row['pmxd_f_batch']][$row['pmxd_f_line']][$row['pmxd_t_type']][$row['pmxd_f_type']][] = $row['pmxd_alloc_amt'];
    }

    $outstandingAmounts = [];
    $total = 0;
    foreach ($existing as $row) {
        $row['invoiceTotalReceipted'] = -1 * ((isset($t_batch[$row['batchNumber']][$row['batchLineNumber']]['CSH']['INV']) ? array_sum(
            $t_batch[$row['batchNumber']][$row['batchLineNumber']]['CSH']['INV']
        ) : 0) + (isset($t_batch[$row['batchNumber']][$row['batchLineNumber']]['CRE']['INV']) ? array_sum(
            $t_batch[$row['batchNumber']][$row['batchLineNumber']]['CRE']['INV']
        ) : 0));
        $row['creditTotalReceipted'] = -1 * (isset($t_batch[$row['batchNumber']][$row['batchLineNumber']]['CSH']['CRE']) ? array_sum(
            $t_batch[$row['batchNumber']][$row['batchLineNumber']]['CSH']['CRE']
        ) : 0);
        $row['creditTotalAllocatedOpposite'] = -1 * (isset($t_batch[$row['batchNumber']][$row['batchLineNumber']]['INV']['CRE']) ? array_sum(
            $t_batch[$row['batchNumber']][$row['batchLineNumber']]['INV']['CRE']
        ) : 0);

        $row['creditTotalAllocated'] = (isset($f_batch[$row['batchNumber']][$row['batchLineNumber']]['INV']['CRE']) ? array_sum(
            $f_batch[$row['batchNumber']][$row['batchLineNumber']]['INV']['CRE']
        ) : 0);
        $row['invoiceTotalReceiptedOpposite'] = (isset($f_batch[$row['batchNumber']][$row['batchLineNumber']]['CSH']['INV']) ? array_sum(
            $f_batch[$row['batchNumber']][$row['batchLineNumber']]['CSH']['INV']
        ) : 0) + (isset($f_batch[$row['batchNumber']][$row['batchLineNumber']]['CRE']['INV']) ? array_sum(
            $f_batch[$row['batchNumber']][$row['batchLineNumber']]['CRE']['INV']
        ) : 0);
        $row['creditTotalReceiptedOpposite'] = (isset($f_batch[$row['batchNumber']][$row['batchLineNumber']]['CSH']['CRE']) ? array_sum(
            $f_batch[$row['batchNumber']][$row['batchLineNumber']]['CSH']['CRE']
        ) : 0);


        $offset = getAgedDebtorsOffset(
            $row['invoiceTotalReceipted'],
            $row['invoiceTotalCredited'],
            $row['invoiceTotalAdjusted'],
            $row['creditTotalReceipted'],
            $row['creditTotalAdjusted'],
            $row['creditTotalAllocated'],
            $row['invoiceTotalReceiptedOpposite'],
            $row['invoiceTotalCreditedOpposite'],
            $row['invoiceTotalAdjustedOpposite'],
            $row['creditTotalReceiptedOpposite'],
            $row['creditTotalAdjustedOpposite'],
            $row['creditTotalAllocatedOpposite']
        );
        $row['unallocated'] = (float) $row['unallocated'];
        $row['amount'] = (float) $row['amount'];
        if ((($row['amount'] - $offset) != 0) && ($row['transactionType'] != 'CSH')) {
            $row['unpaidAmount'] = round($row['amount'] - $offset, 2);
            $total += $row['unpaidAmount'];
            $outstandingAmounts[] = $row;
        } elseif (($row['transactionType'] == 'CSH') && ($row['unallocated'] != '') && ($row['unallocated'] != 0)) {
            $unallocated = dbGetAgedUnallocated(
                $row['propertyID'],
                $row['leaseID'],
                $row['debtorID'],
                $row['batchNumber'],
                $row['batchLineNumber'],
                $endDate
            );
            $row['unpaidAmount'] = $row['unallocated'];
            $total += ($row['unallocated'] * 1);
            $outstandingAmounts[] = $row;
        }
    }

    $sql = "
        SELECT batch_nr AS batchNumber,
            batch_line_nr AS batchLineNumber,
            debtor_code AS debtorID,
            ref_2 AS propertyID,
            ref_3 AS accountID,
            ref_4 AS leaseID,
            description AS description,
            artr_gst_code AS taxCode,
            CONVERT(char(10),pmxd_alloc_dt , 103) AS transactionDate,
            CONVERT(char(10), spare_date_1, 103) AS fromDate,
            CONVERT(char(10), spare_date_2, 103) AS toDate,
            pmxd_alloc_amt AS amount,
            pmxd_alloc_amt AS unpaidAmount,
            artr_gst_inv_no as invoiceNumber,
            CONVERT(char(10), artr_gst_inv_dt, 103) as invoiceDate,
            trans_type AS transactionType
        FROM ar_transaction, pmxd_ar_alloc
        WHERE
        pmxd_t_batch = batch_nr
        AND pmxd_t_line = batch_line_nr
        AND (pmxd_lease=?)
        AND ((pmxd_f_type='CSH') OR (pmxd_f_type='REV'))
        AND trans_date>CONVERT(datetime, ?, 103)
        AND (pmxd_alloc_dt <= CONVERT(datetime, ?, 103))
        AND (pmxd_prop=?)
        ";

    $params = [$leaseID, $endDate, $endDate, $propertyID];

    $futureReceipts = $dbh->executeSet($sql, false, true, $params);
    foreach ($futureReceipts as $row) {
        $total += ($row['amount'] * 1);
        $outstandingAmounts[] = $row;
    }


    return $outstandingAmounts;
}

function dbGetAgentDetails()
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);
    $sql = 'SELECT
         cms_sy_name AS agentName,
         cms_sy_street AS agentAddress,
         cms_sy_city AS agentCity,
         cms_sy_state AS agentState,
         cms_sy_post AS agentPostCode,
         cms_sy_phone AS agentPhone,
         cms_sy_fax AS agentFax,
         cms_sy_gst_abn as agentABN,
         cms_sy_country as agentCountry,
         pmol_name AS officeName,
         pmol_street AS officeAddress,
         pmol_city AS officeCity,
         pmol_state AS officeState,
         pmol_postcode AS officePostCode,
         pmol_phone AS officePhone,
         pmol_fax AS officeFax,
         cms_sy_trading_name AS tradeMark,
         cms_sy_title AS officeTitle
         FROM cms_system
         CROSS JOIN pmol_office_loc';

    return $dbh->executeSingle($sql);
}

function dbGetPeriodDate($propertyID, $period, $year)
{
    global $dbh, $clientDB;
    $dbh->selectDatabase($clientDB);
    $sql = 'SELECT CONVERT(char(10), pmcp_start_dt, 103) AS startDate,CONVERT(char(10), pmcp_end_dt, 103) AS endDate FROM pmcp_prop_cal WHERE pmcp_prop=? AND pmcp_period=? AND pmcp_year=?';

    return $dbh->executeSingle($sql, [$propertyID, $period, $year]);
}

function dbGetBudgetYears($propertyID)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $params = [];
    if (is_array($propertyID) && count($propertyID ?? []) > 0) {
        $where1 = "b.pmrp_prop IN ('" . implode("','", $propertyID) . "')";
        $where2 = "b.pmep_prop IN ('" . implode("','", $propertyID) . "')";
    } else {
        $where1 = "b.pmrp_prop='" . $propertyID . "'";
        $where2 = "b.pmep_prop='" . $propertyID . "'";
    }

    $sql = "
			SELECT DISTINCT
				b.pmrp_year AS year
			FROM
				pmrp_b_rev_per b
			WHERE
				{$where1}
		UNION
			SELECT DISTINCT
				b.pmep_year AS year
			FROM
				pmep_b_exp_per b
			WHERE
				{$where2}";

    return $dbh->executeSet($sql, false, true, $params);
}

function dbBudgetIncomeAccounts($propertyCode, $year, $withLease = false)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);

    $year = intval($year);
    if ($withLease) {
        $sql = 'SELECT DISTINCT
				t1.pmca_code accountID,
				t1.pmca_name accountName,
				t1.pmca_type accountType,
				t2.pmrp_lease leaseCode,
				t2.pmrp_comment comment,
				CONVERT(char(10), t2.date_updated, 103) as commentDate,
				t2.user_updated as commentUser
			FROM
				pmca_chart t1
			LEFT JOIN
				pmrp_b_rev_per t2 ON (t2.pmrp_acc = t1.pmca_code)
			WHERE
				t2.pmrp_prop = ?
				AND t2.pmrp_year = ?
				ORDER BY t1.pmca_code';
    } else {
        $sql = 'SELECT DISTINCT
				c.pmca_code accountID,
				c.pmca_name accountName,
				c.pmca_type accountType,
				b.pmrp_comment comment,
				CONVERT(char(10), b.date_updated, 103) as commentDate,
				b.user_updated as commentUser
			FROM
				pmca_chart c
			LEFT JOIN
				pmrp_b_rev_per b ON (b.pmrp_acc = c.pmca_code)
			WHERE
				b.pmrp_prop = ?
				AND b.pmrp_year = ?
				ORDER BY c.pmca_code';
    }

    return $dbh->executeSet($sql, false, true, [$propertyCode, $year]);
}

function dbBudgetIncomeAccountsWitoutComment($propertyCode, $year, $withLease = false)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $year = intval($year);
    if ($withLease) {
        $sql = 'SELECT DISTINCT
				t1.pmca_code accountID,
				t1.pmca_name accountName,
				t1.pmca_type accountType,
				t2.pmrp_unit unitCode,
				t2.pmrp_lease leaseCode,
				t3.pmle_name leaseName,
				CONVERT(char(10), t2.date_updated, 103) as commentDate,
				t2.user_updated as commentUser
			FROM
				pmca_chart t1
			LEFT JOIN
				pmrp_b_rev_per t2 ON (t2.pmrp_acc = t1.pmca_code)
			LEFT JOIN
				pmle_lease t3 ON (t2.pmrp_prop = t3.pmle_prop AND t2.pmrp_lease = t3.pmle_lease)
			WHERE
				t2.pmrp_prop = ?
				AND t2.pmrp_year = ?
            ORDER BY t1.pmca_code
				';
    } else {
        $sql = 'SELECT DISTINCT
				c.pmca_code accountID,
				c.pmca_name accountName,
				c.pmca_type accountType,
				CONVERT(char(10), b.date_updated, 103) as commentDate,
				b.user_updated as commentUser
			FROM
				pmca_chart c
			LEFT JOIN
				pmrp_b_rev_per b ON (b.pmrp_acc = c.pmca_code)
			WHERE
				b.pmrp_prop = ?
				AND b.pmrp_year = ?
            ORDER BY c.pmca_code
				';
    }

    return $dbh->executeSet($sql, false, true, [$propertyCode, $year]);
}

function dbBudgetIncomeAccountsGetComment($propertyCode, $year, $accountCode, $leaseCode)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);

    $year = intval($year);
    $sql = 'SELECT DISTINCT
				t2.pmrp_comment comment
			FROM
				pmca_chart t1
			LEFT JOIN
				pmrp_b_rev_per t2 ON (t2.pmrp_acc = t1.pmca_code)
			WHERE
			    t1.pmca_code = ?
				AND t2.pmrp_prop = ?
				AND t2.pmrp_lease = ?
				AND t2.pmrp_year = ?';

    return $dbh->executeSet($sql, false, true, [$accountCode, $propertyCode, $leaseCode, $year]);
}

function dbBudgetIncomeAccountsGetCommentTop1($propertyCode, $year, $accountCode)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $year = intval($year);
    $sql = 'SELECT TOP 1
				t2.pmrp_comment comment
			FROM
				pmca_chart t1
			LEFT JOIN
				pmrp_b_rev_per t2 ON (t2.pmrp_acc = t1.pmca_code)
			WHERE
			    t1.pmca_code = ?
				AND t2.pmrp_prop = ?
				AND t2.pmrp_year = ?';

    return $dbh->executeSet($sql, false, true, [$accountCode, $propertyCode, $year]);
}

function dbBudgetExpensesAccountsGetComment($propertyCode, $year, $accountCode)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);

    $year = intval($year);
    $sql = "SELECT DISTINCT
                CASE WHEN t2.pmep_comment <> '' AND t2.pmep_comment IS NOT NULL THEN t2.pmep_comment ELSE t3.comment END comment,
                CONVERT(char(10), t2.date_updated, 103) as commentDate,
                t2.user_updated as commentUser
			FROM
				pmca_chart t1
			LEFT JOIN
				pmep_b_exp_per t2 ON (t2.pmep_exp_acc = t1.pmca_code)
            LEFT JOIN property_expenses_budget t3
			ON t3.property_code = t2.pmep_prop AND t3.financial_year = t2.pmep_year AND t3.account_code = t1.pmca_code
			WHERE
			    t1.pmca_code = ?
				AND t2.pmep_prop = ?
				AND t2.pmep_year = ?";

    return $dbh->executeSet($sql, false, true, [$accountCode, $propertyCode, $year]);
}

function dbBudgetIncomeAmounts($propertyCode, $year, $accountCode, $reportType = '', $leaseCode = '', $unit_code = '')
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $year = intval($year);

    switch ($reportType) {
        case '2':
            $column = 'pmrp_f_c_amt';
            break;
        case '3':
            $column = 'pmrp_b_a_amt';
            break;
        case '4':
            $column = 'pmrp_f_a_amt';
            break;
        default:
            $column = 'pmrp_b_c_amt';
            break;
    }

    if ($leaseCode) {
        $sql = "
			SELECT
				pmrp_per AS period,
				{$column} AS amount
			FROM
				pmrp_b_rev_per
			WHERE
				pmrp_prop=?
				AND pmrp_year=?
				AND pmrp_acc=?
				AND pmrp_lease=?
				AND pmrp_unit=?
				";
        $params = [$propertyCode, $year, $accountCode, $leaseCode, $unit_code];
    } else {
        $sql = "
			SELECT
				pmrp_per AS period,
				COALESCE(SUM({$column}), 0) AS amount
			FROM
				pmrp_b_rev_per
			WHERE
				pmrp_prop=?
				AND pmrp_year=?
				AND pmrp_acc=?
			GROUP BY
				pmrp_per";
        $params = [$propertyCode, $year, $accountCode];
    }

    return $dbh->executeSet($sql, false, true, $params);
}

function dbBudgetExpensesAccounts($propertyID, $year, $accountSubGroup)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);

    $year = intval($year);
    $sql = 'SELECT DISTINCT
			c.pmca_code accountID,
			c.pmca_name accountName,
			c.pmca_type accountType,
			b.pmep_comment comment,
			CONVERT(char(10), b.date_updated, 103) as commentDate,
			b.user_updated as commentUser
		FROM
			pmca_chart c
		LEFT JOIN
			pmep_b_exp_per b ON (b.pmep_exp_acc = c.pmca_code)
		LEFT JOIN
			pmrcg_chart_grp g ON (CONVERT(int, c.pmca_code) >= CONVERT(int, g.pmrcg_acc) AND CONVERT(int, c.pmca_code) <= CONVERT(int, g.pmrcg_acc_to))
		WHERE
			b.pmep_prop = ?
			AND b.pmep_year = ?
			AND g.pmrcg_subgrp = ?';

    return $dbh->executeSet($sql, false, true, [$propertyID, $year, $accountSubGroup]);
}

function dbBudgetExpensesAccountsWithoutComment($propertyID, $year, $accountSubGroup)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $year = intval($year);

    $sql = 'SELECT DISTINCT
			c.pmca_code accountID,
			c.pmca_name accountName,
			c.pmca_type accountType
		FROM
			pmca_chart c
		LEFT JOIN
			pmep_b_exp_per b ON (b.pmep_exp_acc = c.pmca_code)
		LEFT JOIN
			pmrcg_chart_grp g ON (CONVERT(int, c.pmca_code) >= CONVERT(int, g.pmrcg_acc) AND CONVERT(int, c.pmca_code) <= CONVERT(int, g.pmrcg_acc_to))
		WHERE
			b.pmep_prop = ?
			AND b.pmep_year = ?
			AND g.pmrcg_subgrp = ?';

    return $dbh->executeSet($sql, false, true, [$propertyID, $year, $accountSubGroup]);
}

function dbBudgetExpensesAmounts($propertyID, $year, $accountCode, $reportType = '')
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    switch ($reportType) {
        case '2':
            $column = 'b.pmep_f_c_amt';
            break;
        case '3':
            $column = 'b.pmep_b_a_amt';
            break;
        case '4':
            $column = 'b.pmep_f_a_amt';
            break;
        default:
            $column = 'b.pmep_b_c_amt';
            break;
    }

    $sql = "SELECT DISTINCT {$column} AS amount, pmep_per AS period, b.pmep_prop, b.pmep_year, b.pmep_exp_acc
		FROM pmep_b_exp_per b
		WHERE (b.pmep_prop=?)
			AND (b.pmep_year=?)
			AND (b.pmep_exp_acc=?)
			AND {$column} IS NOT NULL";

    return $dbh->executeSet($sql, false, true, [$propertyID, $year, $accountCode]);
}

function dbGetTenantContacts($propertyID, $leaseID)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $sql = "SELECT
			c.pmlt_name AS contactName,
			z.pmzz_desc AS description,
			p.pmlj_phone_no AS phoneNumber
		FROM
			pmlj_l_phone p,
			pmlt_l_contact c,
			pmzz_param z
		WHERE
			p.pmlj_lease = c.pmlt_lease
			AND p.pmlj_prop = c.pmlt_prop
			AND p.pmlj_ph_code = z.pmzz_code
			AND p.pmlj_c_serial = c.pmlt_serial
			AND c.pmlt_lease=?
			AND p.pmlj_prop=?
			AND z.pmzz_par_type='PHONETYPE'";

    return $dbh->executeSet($sql, false, true, [$leaseID, $propertyID]);
}

function dbGetLedgerForProperties($properties, $basis, $periodFrom, $yearFrom, $periodTo, $yearTo, $accounts = [])
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);
    $params = [];
    $accountSQL = (isEmptyArray($accounts)) ? '' : ' AND a.pmca_code IN (' . addSQLParam($params, $accounts) . ')';
    if (is_array($properties) && count($properties ?? []) > 0) {
        $properties = implode("','", $properties);
    }

    $sql = "
	SELECT
	a.pmca_code AS accountID,
	a.pmca_name AS accountName,
	a.pmca_type AS accountType,
	t.source AS source,
	CONVERT(char(10), t.transaction_date, 103) AS transactionDate,
	t.company_id AS companyID,
	t.property_id AS propertyID,
	t.transaction_type AS transactionType,
	t.transaction_amount AS transactionAmount,
	t.reference AS reference,
	t.period AS period,
	t.year AS year,
	t.description AS description,
	CONVERT(char(10), t.from_date, 103) AS fromDate,
	CONVERT(char(10), t.to_date, 103) AS toDate,
	t.method AS method,
    u.pmua_unit AS unit
	FROM
	pmca_chart a,
	gl_transaction t
    LEFT JOIN pmua_unit_area u
    ON t.property_id = u.pmua_prop AND t.lease_id = u.pmua_lease AND (t.transaction_date BETWEEN pmua_from_dt AND pmua_to_dt)
	AND t.lease_id IS NOT NULL AND t.lease_id != ''
	WHERE
	 t.account_id = a.pmca_code
	 {$accountSQL}
	 AND t.property_id IN ('" . $properties . "')
	 AND t.year=" . addSQLParam($params, $yearFrom) . '
	 AND t.period BETWEEN ' . addSQLParam($params, $periodFrom) . ' AND ' . addSQLParam($params, $periodTo) . '
	 AND t.method = ' . addSQLParam($params, $basis) . '
	ORDER BY
	t.account_ID ASC,
	t.transaction_date ASC
	';

    return $dbh->executeSet($sql, false, true, $params);
}

/**
 * @param  $propertyID  integer Mandatory.
 * @param  $year  integer Mandatory.
 * @param  $dataType  integer Mandatory.
 * @return array
 *
 * <AUTHOR> Reyes
 *
 * @since 2013-02-08
 **/
function dbGetBudgetForPeriod(
    $properties,
    $year,
    $period,
    $periodTo = null,
    $accountGroup = 'TRACC2',
    $ownerAccountCode = null
) {
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $params = [];
    if (! $periodTo) {
        $periodTo = $period;
    }


    $sql = '
				SELECT DISTINCT
					pmrp_acc accountID,
					pmca_type AS accountType,
					pmca_name AS accountName,

					COALESCE(SUM(pmrp_b_c_amt), 0) AS budgetCash,
					COALESCE(SUM(pmrp_b_a_amt), 0) AS budgetAccruals
					,pmoc_owner_acc as ownerAccountCode,
                    pmoc_desc AS ownerAccountDesc
				FROM
					pmrp_b_rev_per,
					pmcg_chart_grp,
					pmca_chart
				Left Join pmoc_o_chart on pmoc_owner = ' . addSQLParam($params, $ownerAccountCode) . " AND pmoc_acc = pmca_code
				WHERE
					pmrp_prop  IN ('" . implode("','", $properties) . "')
					AND pmcg_acc = pmca_code
					AND pmcg_grp = " . addSQLParam($params, $accountGroup) . '
					AND pmrp_acc = pmca_code
					AND pmrp_year=' . addSQLParam($params, $year) . '
					AND pmrp_per BETWEEN ' . addSQLParam($params, $period) . ' AND ' . addSQLParam($params, $periodTo) . '
				GROUP BY pmrp_acc, pmca_type, pmca_name, pmcg_subgrp ,pmoc_owner_acc,pmoc_desc
			UNION ALL
				SELECT DISTINCT
					pmep_exp_acc accountID,
					pmca_type AS accountType,
					pmca_name AS accountName,

					COALESCE(SUM(pmep_b_c_amt), 0) AS budgetCash,
					COALESCE(SUM(pmep_b_a_amt), 0) AS budgetAccruals
					,pmoc_owner_acc as ownerAccountCode,
                    pmoc_desc AS ownerAccountDesc
				FROM
					pmep_b_exp_per,
					pmcg_chart_grp,
					pmca_chart
				Left Join pmoc_o_chart on pmoc_owner = ' . addSQLParam($params, $ownerAccountCode) . " AND pmoc_acc = pmca_code
				WHERE
					pmep_prop IN ('" . implode("','", $properties) . "')
					AND pmcg_acc = pmca_code
					AND pmcg_grp = " . addSQLParam($params, $accountGroup) . '
					AND pmep_exp_acc = pmca_code
					AND pmep_year=' . addSQLParam($params, $year) . '
					AND pmep_per BETWEEN ' . addSQLParam($params, $period) . ' AND ' . addSQLParam($params, $periodTo) . '
				GROUP BY pmep_exp_acc, pmca_type, pmca_name, pmcg_subgrp ,pmoc_owner_acc,pmoc_desc
			ORDER BY
				pmrp_acc ASC';

    return $dbh->executeSet($sql, false, true, $params);
}

function dbGetBudgetForYTD($properties, $year, $period, $accountGroup = 'TRACC2', $ownerAccountCode = null)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $params = [];

    $sql = 'SELECT a.* FROM
                (
                    SELECT DISTINCT
                        pmrp_acc accountID,
                        pmca_type AS accountType,
                        pmca_name AS accountName,
                        pmcg_subgrp AS accountGroup,
                        COALESCE(SUM(pmrp_b_c_amt), 0) AS budgetCash,
                        COALESCE(SUM(pmrp_b_a_amt), 0) AS budgetAccruals
                        ,pmoc_owner_acc as ownerAccountCode,
                        pmoc_desc AS ownerAccountDesc
                    FROM
                        pmrp_b_rev_per,
                        pmcg_chart_grp,
                        pmca_chart
                        Left Join pmoc_o_chart on pmoc_owner = ' . addSQLParam($params, $ownerAccountCode) . " AND pmoc_acc = pmca_code
                    WHERE
                        pmrp_prop  IN ('" . implode("','", $properties) . "')
                        AND pmcg_acc = pmca_code
                        AND pmcg_grp = " . addSQLParam($params, $accountGroup) . '
                        AND pmrp_acc = pmca_code
                        AND pmrp_year=' . addSQLParam($params, $year) . '
                        AND pmrp_per <= ' . addSQLParam($params, $period) . '
                        GROUP BY
                        pmrp_acc, pmca_type, pmca_name, pmcg_subgrp ,pmoc_owner_acc ,pmoc_desc
                  UNION ALL
                    SELECT DISTINCT
                        pmep_exp_acc accountID,
                        pmca_type AS accountType,
                        pmca_name AS accountName,
                        pmcg_subgrp AS accountGroup,
                        COALESCE(SUM(pmep_b_c_amt), 0) AS budgetCash,
                        COALESCE(SUM(pmep_b_a_amt), 0) AS budgetAccruals
                        ,pmoc_owner_acc as ownerAccountCode,
                        pmoc_desc AS ownerAccountDesc
                    FROM
                        pmep_b_exp_per,
                        pmcg_chart_grp,
                        pmca_chart
                        Left Join pmoc_o_chart on pmoc_owner = ' . addSQLParam($params, $ownerAccountCode) . " AND pmoc_acc = pmca_code
                    WHERE
                        pmep_prop IN ('" . implode("','", $properties) . "')
                        AND pmcg_acc = pmca_code
                        AND pmcg_grp = " . addSQLParam($params, $accountGroup) . '
                        AND pmep_exp_acc = pmca_code
                        AND pmep_year=' . addSQLParam($params, $year) . '
                        AND pmep_per<=' . addSQLParam($params, $period) . "
                    GROUP BY
                        pmep_exp_acc, pmca_type, pmca_name, pmcg_subgrp ,pmoc_owner_acc ,pmoc_desc
              ) as a
                JOIN pmas_acc_subgrp b
                ON b.pmas_class = a.accountType
                AND b.pmas_subgrp = a.accountGroup
                AND b.pmas_grp = 'BSHEET'
                ORDER BY accountID";

    return $dbh->executeSet($sql, false, true, $params);
}

function dbGLGetOwnerShares($propertyID)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $sql = '
		SELECT
			pmos_owner AS ownerName,
			pmos_pct AS ownerPercentage,
			pmco_name AS ownerDescription,
			pmco_o_pay_acc AS accountID,
			pmos_amount ownerAmount
		FROM
			pmos_o_share, pmco_company
		WHERE
			pmos_prop = ?
			AND pmco_code = pmos_owner';

    return $dbh->executeSet($sql, false, true, [$propertyID]);
}

function dbGetTrialBalanceYTDByAccountGroup(
    $properties,
    $accountGroup,
    $period,
    $year,
    $type = null,
    $forecastPeriodFrom = null,
    $ownerAccountCode = null
) {
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);
    $params = [$ownerAccountCode];
    $accountSQL = ($accountGroup) ? 'a.pmca_type = ' . addSQLParam($params, $accountGroup) . '' : '';


    if ($type != 5) {
        $sql = "SELECT
            a.pmca_code AS accountID,
            a.pmca_name AS accountName,
            COALESCE(SUM(b.balanceCash), 0)  AS balanceCash ,
            COALESCE(SUM(b.balanceAccruals), 0) AS balanceAccruals
            ,pmoc_owner_acc as ownerAccountCode,
            pmoc_desc AS ownerAccountDesc
            FROM
            pmca_chart a,
            gl_trial_balance b
            Left Join pmoc_o_chart on pmoc_owner = ? AND pmoc_acc = accountID
            WHERE
            {$accountSQL}
            AND b.accountID = a.pmca_code AND b.propertyID IN ('" . implode(
            "','",
            $properties
        ) . "') AND b.year=" . addSQLParam($params, $year) . ' AND b.period <= ' . addSQLParam($params, $period) . '
            GROUP BY
            a.pmca_code,
            a.pmca_name
            ,pmoc_owner_acc
            ,pmoc_desc
            ORDER BY
            a.pmca_code
            ';

        return $dbh->executeSet($sql, false, true, $params);
    } else {
        $return = [];

        $actuals = dbGetTrialBalanceForPeriodByAccountGroup(
            $properties,
            $accountGroup,
            1,
            $year,
            $forecastPeriodFrom - 1,
            $ownerAccountCode
        );

        foreach ($actuals as $actual) {
            $return[$actual['accountID']]['ownerAccountCode'] = $actual['ownerAccountCode'];
            $return[$actual['accountID']]['ownerAccountDesc'] = $actual['ownerAccountDesc'];
            $return[$actual['accountID']]['accountID'] = $actual['accountID'];
            $return[$actual['accountID']]['accountName'] = $actual['accountName'];
            $return[$actual['accountID']]['balanceCash'] += $actual['balanceCash'];
            $return[$actual['accountID']]['balanceAccruals'] += $actual['balanceAccruals'];
        }


        $forecasts = dbGetForecastForPeriodByAccountGroup(
            $properties,
            $accountGroup,
            $forecastPeriodFrom,
            $year,
            $period,
            $ownerAccountCode
        );

        foreach ($forecasts as $forecast) {
            $return[$forecast['accountID']]['ownerAccountCode'] = $forecast['ownerAccountCode'];
            $return[$forecast['accountID']]['ownerAccountDesc'] = $forecast['ownerAccountDesc'];
            $return[$forecast['accountID']]['accountID'] = $forecast['accountID'];
            $return[$forecast['accountID']]['accountName'] = $forecast['accountName'];
            $return[$forecast['accountID']]['balanceCash'] += $forecast['balanceCash'];
            $return[$forecast['accountID']]['balanceAccruals'] += $forecast['balanceAccruals'];
        }

        return $return;
    }
}

if (! function_exists('dbGetTrialBalanceYTDByAccount')) {
    function dbGetTrialBalanceYTDByAccount($properties, $account, $period, $year)
    {
        global $dbh;
        global $clientDB;
        $dbh->selectDatabase($clientDB);

        $params = [];
        $accountSQL = ($account) ? 'a.pmca_code = ' . addSQLParam($params, $account) : '';
        if (is_array($properties) && count($properties ?? []) > 0) {
            $properties = implode("','", $properties);
        }

        $sql = "
            SELECT
            a.pmca_code AS accountID,
            a.pmca_name AS accountName,
            COALESCE(SUM(b.balanceCash), 0)  AS balanceCash ,
            COALESCE(SUM(b.balanceAccruals), 0) AS balanceAccruals
            FROM
            pmca_chart a,
            gl_trial_balance b
            WHERE
            {$accountSQL}
            AND b.accountID = a.pmca_code
            AND b.propertyID IN ('" . $properties . "')
            AND b.year=" . addSQLParam($params, $year) . ' AND b.period <= ' . addSQLParam($params, $period) . '
            GROUP BY a.pmca_code, a.pmca_name
            ORDER BY a.pmca_code ';

        return $dbh->executeSingle($sql, $params);
    }
}

if (! function_exists('dbGetOpeningTrialBalanceYTDByAccount')) {
    function dbGetOpeningTrialBalanceYTDByAccount($properties, $account, $period, $year)
    {
        global $dbh;
        global $clientDB;
        $dbh->selectDatabase($clientDB);

        $params = [];
        $accountSQL = ($account) ? 'a.pmca_code = ' . addSQLParam($params, $account) . '' : '';
        if (is_array($properties) && count($properties ?? []) > 0) {
            $properties = implode("','", $properties);
        }

        $sql = "
            SELECT
            a.pmca_code AS accountID,
            a.pmca_name AS accountName,
            COALESCE(SUM(b.balanceCash), 0)  AS balanceCash ,
            COALESCE(SUM(b.balanceAccruals), 0) AS balanceAccruals
            FROM
            pmca_chart a,
            gl_trial_balance b
            WHERE
            {$accountSQL}
            AND b.accountID = a.pmca_code
            AND b.propertyID IN ('" . $properties . "')
            AND b.year=" . addSQLParam($params, $year) . ' AND b.period < ' . addSQLParam($params, $period) . '
            GROUP BY a.pmca_code, a.pmca_name
            ORDER BY a.pmca_code ';

        return $dbh->executeSingle($sql, $params);
    }
}

function dbGetTrialBalanceForPeriodByAccountGroup(
    $properties,
    $accountGroup,
    $period,
    $year,
    $periodTo = null,
    $ownerAccountCode = null
) {
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);


    if (! $periodTo) {
        $periodTo = $period;
    }

    $params = [$ownerAccountCode];
    $accountSQL = ($accountGroup) ? 'a.pmca_type = ' . addSQLParam($params, $accountGroup) : '';


    $sql = "
	SELECT
	a.pmca_code AS accountID,
	a.pmca_name AS accountName,
	a.pmca_type AS accountType,
	COALESCE(SUM(b.balanceCash), 0)  AS balanceCash ,
	COALESCE(SUM(b.balanceAccruals), 0) AS balanceAccruals
	,pmoc_owner_acc as ownerAccountCode,
    pmoc_desc AS ownerAccountDesc
	FROM
	pmca_chart a,
	gl_trial_balance b
	Left Join pmoc_o_chart on pmoc_owner = ? AND pmoc_acc = accountID
	WHERE
	{$accountSQL}
	AND b.accountID = a.pmca_code AND b.propertyID IN ('" . implode(
        "','",
        $properties
    ) . "') AND b.year=" . addSQLParam($params, $year) . ' AND b.period BETWEEN ' . addSQLParam(
        $params,
        $period
    ) . ' AND ' . addSQLParam($params, $periodTo) . '
	GROUP BY
	a.pmca_code, a.pmca_name, a.pmca_type,pmoc_owner_acc,pmoc_desc
	ORDER BY
	a.pmca_code
	';

    return $dbh->executeSet($sql, false, true, $params);
}

if (! function_exists('dbGetTrialBalanceYTDAccountGroup')) {
    function dbGetTrialBalanceYTDAccountGroup($properties, $accountGroup, $period, $year)
    {
        global $dbh;
        global $clientDB;
        $dbh->selectDatabase($clientDB);
        $params = [];
        $accountSQL = ($accountGroup) ? 'a.pmca_type = ' . addSQLParam($params, $accountGroup) : '';
        if (is_array($properties) && count($properties ?? []) > 0) {
            $properties = implode("','", $properties);
        }

        $sql = "
            SELECT
                a.pmca_code AS accountID,
                a.pmca_name AS accountName,
                a.pmca_type AS accountType,
                COALESCE(SUM(b.balanceCash), 0)  AS balanceCash ,
                COALESCE(SUM(b.balanceAccruals), 0) AS balanceAccruals,
                a.pmca_gl_account_group2 AS accountGroup2
            FROM
                pmca_chart a,
                gl_trial_balance b
            WHERE
            {$accountSQL}
            AND b.accountID = a.pmca_code
            AND b.propertyID IN ('" . $properties . "')
            AND b.year=" . addSQLParam($params, $year) . ' AND b.period <= ' . addSQLParam($params, $period) . '
            GROUP BY
            a.pmca_code, a.pmca_name, a.pmca_type,pmca_gl_account_group2
            ORDER BY a.pmca_code ';

        return $dbh->executeSet($sql, false, true, $params);
    }
}

if (! function_exists('dbGetForecastForPeriodByAccountGroup')) {
    function dbGetForecastForPeriodByAccountGroup(
        $properties,
        $accountGroup,
        $period,
        $year,
        $periodTo = null,
        $ownerAccountCode = null
    ) {
        global $dbh;
        global $clientDB;
        $dbh->selectDatabase($clientDB);


        if (! $periodTo) {
            $periodTo = $period;
        }

        $params = [$ownerAccountCode];

        $sql = "SELECT a.pmca_code as accountID,
            a.pmca_name as accountName,
            a.pmca_type as accountType,
            COALESCE(SUM(b.pmrp_f_c_amt), 0)*-1 as balanceCash,
            COALESCE(SUM(b.pmrp_f_a_amt), 0)*-1 as balanceAccruals
            ,pmoc_owner_acc as ownerAccountCode,
            pmoc_desc AS ownerAccountDesc
        FROM pmrp_b_rev_per b
        JOIN pmca_chart a ON b.pmrp_acc = a.pmca_code
        Left Join pmoc_o_chart on pmoc_owner = ? AND pmoc_acc = a.pmca_code
        WHERE (b.pmrp_f_c_amt != 0 OR b.pmrp_f_a_amt != 0)
        AND b.pmrp_prop IN ('" . implode("','", $properties) . "') AND b.pmrp_year=" . addSQLParam(
            $params,
            $year
        ) . ' AND b.pmrp_per BETWEEN ' . addSQLParam($params, $period) . ' AND ' . addSQLParam($params, $periodTo) . '
        ' . (($accountGroup) ? ' AND a.pmca_type = ' . addSQLParam($params, $accountGroup) : '') . '
		GROUP BY a.pmca_code, a.pmca_name, a.pmca_type, b.pmrp_year ,pmoc_owner_acc
            ,pmoc_desc

        UNION ALL

        SELECT a.pmca_code as accountID,
            a.pmca_name as accountName,
            a.pmca_type as accountType,
            COALESCE(SUM(b.pmep_f_c_amt), 0) as balanceCash,
            COALESCE(SUM(b.pmep_f_a_amt), 0) as balanceAccruals
            ,pmoc_owner_acc as ownerAccountCode,
            pmoc_desc AS ownerAccountDesc
        FROM pmep_b_exp_per b
        JOIN pmca_chart a ON b.pmep_exp_acc = a.pmca_code
        Left Join pmoc_o_chart on pmoc_owner = ' . addSQLParam($params, $ownerAccountCode) . " AND pmoc_acc = a.pmca_code
        WHERE
        (b.pmep_f_c_amt != 0 OR b.pmep_f_a_amt != 0)
        AND b.pmep_prop IN ('" . implode("','", $properties) . "') AND b.pmep_year=" . addSQLParam(
            $params,
            $year
        ) . ' AND b.pmep_per BETWEEN ' . addSQLParam($params, $period) . ' AND ' . addSQLParam($params, $periodTo) . '
        ' . (($accountGroup) ? ' AND a.pmca_type = ' . addSQLParam($params, $accountGroup) : '') . '
        GROUP BY a.pmca_code, a.pmca_name, a.pmca_type, b.pmep_year ,pmoc_owner_acc
            ,pmoc_desc

        ORDER BY a.pmca_code';

        return $dbh->executeSet($sql, false, true, $params);
    }
}

if (! function_exists('dbGetTrialBalanceForPeriodByAccount')) {
    function dbGetTrialBalanceForPeriodByAccount($properties, $account, $period, $year, $periodTo = null)
    {
        global $dbh;
        global $clientDB;
        $dbh->selectDatabase($clientDB);


        if (! $periodTo) {
            $periodTo = $period;
        }

        $params = [];
        $accountSQL = ($account) ? 'a.pmca_code = ' . addSQLParam($params, $account) : '';
        if (is_array($properties) && count($properties ?? []) > 0) {
            $properties = implode("','", $properties);
        }

        $sql = "
            SELECT
            a.pmca_code AS accountID,
            a.pmca_name AS accountName,
            a.pmca_type AS accountType,
            COALESCE(SUM(b.balanceCash), 0)  AS balanceCash ,
            COALESCE(SUM(b.balanceAccruals), 0) AS balanceAccruals
            FROM
            pmca_chart a,
            gl_trial_balance b
            WHERE
            {$accountSQL}
            AND b.accountID = a.pmca_code
            AND b.propertyID IN ('" . $properties . "')
            AND b.year=" . addSQLParam($params, $year) . ' AND b.period BETWEEN ' . addSQLParam(
            $params,
            $period
        ) . ' AND ' . addSQLParam($params, $periodTo) . '
            GROUP BY a.pmca_code, a.pmca_name, a.pmca_type
            ORDER BY a.pmca_code ';

        return $dbh->executeSingle($sql, $params);
    }
}

function dbGetTrialBalanceForPropertyByAccountGroup(
    $propertyID,
    $accountGroup,
    $periodFrom,
    $yearFrom,
    $periodTo,
    $yearTo
) {
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);
    $params = [];

    $sql = '
	SELECT
	a.pmca_code AS accountID,
	a.pmca_name AS accountName,
	a.pmca_type AS accountType,
	(SELECT COALESCE(SUM(b.balanceCash), 0) FROM  gl_trial_balance b WHERE  b.accountID = a.pmca_code AND b.propertyID=' . addSQLParam(
        $params,
        $propertyID
    ) . ' AND b.year=' . addSQLParam($params, $yearFrom) . ' AND b.period BETWEEN ' . addSQLParam(
        $params,
        $periodFrom
    ) . ' AND ' . addSQLParam($params, $periodTo) . ')  AS balanceCash ,
	(SELECT COALESCE(SUM(b.balanceAccruals), 0) FROM  gl_trial_balance b WHERE  b.accountID = a.pmca_code AND b.propertyID=' . addSQLParam(
        $params,
        $propertyID
    ) . ' AND b.year=' . addSQLParam($params, $yearFrom) . ' AND b.period BETWEEN ' . addSQLParam(
        $params,
        $periodFrom
    ) . ' AND ' . addSQLParam($params, $periodTo) . ') AS balanceAccruals,
	(SELECT COALESCE(SUM(tb.balanceCash), 0) FROM gl_trial_balance tb WHERE tb.accountID=a.pmca_code AND tb.propertyID=' . addSQLParam(
        $params,
        $propertyID
    ) . ' AND tb.year=' . addSQLParam($params, $yearFrom) . ' AND tb.period < ' . addSQLParam(
        $params,
        $periodFrom
    ) . ') AS openingCash,
	(SELECT COALESCE(SUM(tb.balanceAccruals), 0) FROM gl_trial_balance tb WHERE  tb.accountID=a.pmca_code AND tb.propertyID=' . addSQLParam(
        $params,
        $propertyID
    ) . ' AND tb.year=' . addSQLParam($params, $yearFrom) . ' AND tb.period < ' . addSQLParam(
        $params,
        $periodFrom
    ) . ') AS openingAccruals
	FROM
	pmca_chart a
	WHERE ' .
        ($accountGroup ? 'a.pmca_type = ' . addSQLParam($params, $accountGroup) : '') . '
	AND a.pmca_code IN (SELECT c.accountID FROM gl_trial_balance c WHERE c.propertyID=' . addSQLParam(
            $params,
            $propertyID
        ) . ' AND (c.year = ' . addSQLParam($params, $yearTo) . ' AND c.period <= ' . addSQLParam($params, $periodTo) . '))
	ORDER BY
	a.pmca_code
	';

    return $dbh->executeSet($sql, false, true, $params);
}

function dbGetTrialBalanceForAccount($propertyID, $accountID, $periodFrom, $yearFrom, $periodTo, $yearTo)
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);
    $params = [];

    $dateSQL = ($yearFrom == $yearTo) ? ' AND year=' . addSQLParam(
        $params,
        $yearFrom
    ) . ' AND period BETWEEN ' . addSQLParam($params, $periodFrom) . ' AND ' . addSQLParam($params, $periodTo) : '
	AND ((year = ' . addSQLParam($params, $yearFrom) . ' AND period > ' . addSQLParam(
        $params,
        $periodFrom
    ) . ') OR (year > ' . addSQLParam($params, $yearFrom) . ' AND year < ' . addSQLParam(
        $params,
        $yearTo
    ) . ') OR (year = ' . addSQLParam($params, $yearTo) . ' AND period <= ' . addSQLParam($params, $periodTo) . '))
	';
    $sql = '
	SELECT
	COALESCE(SUM(balanceCash), 0) AS balanceCash,
	COALESCE(SUM(balanceAccruals), 0) AS balanceAccruals
	FROM
	gl_trial_balance,
	pmca_chart a
	WHERE accountID = a.pmca_code
	' . addSQLParam($params, $dateSQL) . '
	AND (propertyID=' . addSQLParam($params, $propertyID) . ')
	AND (accountID=' . addSQLParam($params, $accountID) . ')
	';

    return $dbh->executeSingle($sql, $params);
}

function dbGetGLTransactions($propertyID, $year, $period, $accountID = null)
{
    global $dbh, $clientDB;
    $dbh->selectDatabase($clientDB);
    $params = [];
    $sql = '
	SELECT
		v.source,
		CONVERT(char(10), v.transactionDate, 103) AS transactionDate,
		v.propertyID,
		v.accountID,
		v.companyID,
		v.reference,
		v.description,
		v.transactionType,
		v.transactionAmount,
		v.netAmount,
		v.taxAmount,
		v.year,
		v.period
	FROM v_transactions_accruals v
	WHERE
		v.propertyID = ' . addSQLParam($params, $propertyID) . '
		AND v.year = ' . addSQLParam($params, $year) . '
		AND v.period = ' . addSQLParam($params, $period) .
        ($accountID ? ' AND v.accountID = ' . addSQLParam($params, $accountID) : '') . '
	ORDER BY
		v.accountID, v.year, v.period, v.transactionDate
	';

    return $dbh->executeSet($sql, false, true, $params);
}

function dbGetYTDTrialBalanceByAccount($propertyID, $accountID, $period, $year)
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);

    $sql = '
	SELECT
	COALESCE(SUM(balanceCash), 0) as balanceCash,
	COALESCE(SUM(balanceAccruals), 0) as balanceAccruals
	FROM
	gl_trial_balance
	WHERE accountID = ?
	AND (year=?)
	AND (period <= ?)
	AND (propertyID=?)
	';

    $params = [$accountID, $year, $period, $propertyID];

    return $dbh->executeSingle($sql, $params);
}

function dbGetOpeningTrialBalanceByAccount($properties, $accountID, $period, $year, $useYearOpening = false)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);

    $params = [];
    if (is_array($properties) && count($properties ?? []) > 0) {
        $properties = implode("','", $properties);
    }

    $sql = '
		SELECT
			COALESCE(SUM(balanceCash), 0) as balanceCash,
			COALESCE(SUM(balanceAccruals), 0) as balanceAccruals
		FROM
			gl_trial_balance
		WHERE
			accountID = ' . addSQLParam($params, $accountID) . '
			AND ((year=' . addSQLParam($params, $year) . ')
			AND (period< ' . addSQLParam($params, $period) . ')' .
        ($useYearOpening ? '' : ' OR year < ' . addSQLParam($params, $year)) . "
			    )
			AND (propertyID IN ('" . $properties . "'))";

    return $dbh->executeSingle($sql, $params);
}

function dbGetOpeningBalanceForProperty($propertyID, $accountID, $year)
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);

    $sql = '
	SELECT
	pmpb_bal AS openingBalance
	FROM
	pmpb_p_bal
	WHERE pmpb_acc = ?
	AND pmpb_year= ?
	AND pmpb_prop= ?
	';

    return $dbh->executeScalar($sql);
}

function dbGetDoorCountUpdate($propertyID, $fromDate = '', $toDate = '', $allDates = 'No')
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $params = [];
    if (is_array($propertyID) && count($propertyID ?? []) > 0) {
        $propertyID = implode("','", $propertyID);
    }

    if ($propertyID) {
        $whereClause[] = "pmdc_prop IN ('" . $propertyID . "')";
    }

    if ($allDates != 'Yes' && $toDate && $fromDate) {
        $whereClause[] = 'pmdc_date BETWEEN CONVERT(datetime, ' . addSQLParam(
            $params,
            $fromDate
        ) . ', 103) AND CONVERT(datetime, ' . addSQLParam($params, $toDate) . ', 103)';
    }

    $whereClause = ($whereClause && is_array($whereClause)) ? implode(' AND ', $whereClause) : '';
    $sql = "
		SELECT
			pmdc_prop propertyID,
			pmpr_name propertyName,
			pmdc_door doorID,
			CONVERT(char(10), pmdc_date, 103) date,
			pmdc_count doorCount
		FROM
			pmdc_door_count
		LEFT JOIN
			pmpr_property ON (pmpr_prop=pmdc_prop)
		WHERE
			{$whereClause}";

    return $dbh->executeSet($sql, false, true, $params);
}

function dbGetDoorYearsUpdate($propertyID)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $params = [];

    if (is_array($propertyID) && count($propertyID ?? []) > 0) {
        $propertyID = implode("','", $propertyID);
        $where = "pmdc_prop IN ('{$propertyID}')";
    } else {
        $where = 'pmdc_prop=' . addSQLParam($params, $propertyID);
    }

    $sql = "
		SELECT DISTINCT
			YEAR(pmdc_date) year
		FROM
			pmdc_door_count
		WHERE
			{$where}
		ORDER BY
			year ASC";

    return $dbh->executeSet($sql, false, true, $params);
}

function dbCountDoorCountUpdate($propertyID, $fromDate = '', $toDate = '', $allDates = 'No')
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $params = [];
    if (is_array($propertyID) && count($propertyID ?? []) > 0) {
        $propertyID = implode("','", $propertyID);
    }

    if ($propertyID) {
        $whereClause[] = "pmdc_prop IN ('" . $propertyID . "')";
    }

    if ($allDates != 'Yes' && $toDate && $fromDate) {
        $whereClause[] = 'pmdc_date BETWEEN CONVERT(datetime, ' . addSQLParam(
            $params,
            $fromDate
        ) . ', 103) AND CONVERT(datetime, ' . addSQLParam($params, $toDate) . ', 103)';
    }

    $whereClause = ($whereClause && is_array($whereClause)) ? implode(' AND ', $whereClause) : '';
    $sql = "
		SELECT
			COUNT(*) count
		FROM
			pmdc_door_count
		LEFT JOIN
			pmpr_property ON (pmpr_prop=pmdc_prop)
		WHERE
			{$whereClause}";

    return $dbh->executeScalar($sql, $params);
}

function dbGetOpeningBalancesPerAccount($properties, $year, $accountID, $period, $accountGroup)
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase($clientDB);
    $params = [];

    $propertySQL = $properties;

    switch ($accountGroup) {
        case 'I':
        case 'E':
            $sql = "SELECT
						gl_trial_balance.accountID,
						pmca_chart.pmca_type,
						COALESCE(SUM(gl_trial_balance.balanceCash), 0) AS balanceCash,
						COALESCE(SUM(gl_trial_balance.balanceAccruals), 0) AS balanceAccruals,
						0 AS currentYearBalanceCash,
						0 AS currentYearBalanceAccruals
					FROM gl_trial_balance gl_trial_balance, pmca_chart pmca_chart
					WHERE gl_trial_balance.accountID = pmca_chart.pmca_code
					AND ((gl_trial_balance.propertyID IN ('" . implode("','", $propertySQL) . "'))
					AND (gl_trial_balance.year = " . addSQLParam($params, $year) . ')
					AND (gl_trial_balance.accountID = ' . addSQLParam($params, $accountID) . ')
					AND (gl_trial_balance.period < ' . addSQLParam($params, $period) . '))
					GROUP BY gl_trial_balance.accountID, pmca_chart.pmca_type';
            break;
        default:
            if ($period > 1) {
                $sql = "SELECT
							gl_trial_balance.accountID,
							pmca_chart.pmca_type,
							(SELECT
							COALESCE(SUM(gl_trial_balance.balanceCash), 0) AS balanceCash
							FROM gl_trial_balance gl_trial_balance, pmca_chart pmca_chart
							WHERE gl_trial_balance.accountID = pmca_chart.pmca_code
							AND ((gl_trial_balance.propertyID IN ('" . implode(
                    "','",
                    $propertySQL
                ) . "')) AND (gl_trial_balance.year < " . addSQLParam(
                    $params,
                    $year
                ) . ') AND (gl_trial_balance.accountID = ' . addSQLParam($params, $accountID) . "))
							) AS balanceCash,
							(SELECT
							COALESCE(SUM(gl_trial_balance.balanceAccruals), 0) AS balanceAccruals
							FROM gl_trial_balance gl_trial_balance, pmca_chart pmca_chart
							WHERE gl_trial_balance.accountID = pmca_chart.pmca_code
							AND ((gl_trial_balance.propertyID IN ('" . implode(
                    "','",
                    $propertySQL
                ) . "')) AND (gl_trial_balance.year < " . addSQLParam(
                    $params,
                    $year
                ) . ') AND (gl_trial_balance.accountID = ' . addSQLParam($params, $accountID) . "))
							) AS balanceAccruals,
							(SELECT
							COALESCE(SUM(gl_trial_balance.balanceCash), 0) AS balanceCash
							FROM gl_trial_balance gl_trial_balance, pmca_chart pmca_chart
							WHERE gl_trial_balance.accountID = pmca_chart.pmca_code
							AND ((gl_trial_balance.propertyID IN ('" . implode(
                    "','",
                    $propertySQL
                ) . "')) AND  (gl_trial_balance.accountID = " . addSQLParam(
                    $params,
                    $accountID
                ) . ') AND (gl_trial_balance.period < ' . addSQLParam(
                    $params,
                    $period
                ) . ') AND (gl_trial_balance.year = ' . addSQLParam($params, $year) . "))
							) AS currentYearBalanceCash,
							(SELECT
							COALESCE(SUM(gl_trial_balance.balanceAccruals), 0) AS balanceAccruals
							FROM gl_trial_balance gl_trial_balance, pmca_chart pmca_chart
							WHERE gl_trial_balance.accountID = pmca_chart.pmca_code
							AND ((gl_trial_balance.propertyID IN ('" . implode(
                    "','",
                    $propertySQL
                ) . "')) AND  (gl_trial_balance.accountID = " . addSQLParam(
                    $params,
                    $accountID
                ) . ') AND (gl_trial_balance.period < ' . addSQLParam(
                    $params,
                    $period
                ) . ') AND (gl_trial_balance.year = ' . addSQLParam($params, $year) . "))
							) AS currentYearBalanceAccruals
						FROM gl_trial_balance gl_trial_balance, pmca_chart pmca_chart
						WHERE gl_trial_balance.accountID = pmca_chart.pmca_code AND gl_trial_balance.propertyID IN ('" . implode(
                    "','",
                    $propertySQL
                ) . "') AND (gl_trial_balance.accountID = " . addSQLParam($params, $accountID) . ')
						GROUP BY gl_trial_balance.accountID, pmca_chart.pmca_type';
            } else {
                $sql = "SELECT
							gl_trial_balance.accountID,
							pmca_chart.pmca_type,
							(SELECT
							COALESCE(SUM(gl_trial_balance.balanceCash), 0) AS balanceCash
							FROM gl_trial_balance gl_trial_balance, pmca_chart pmca_chart
							WHERE gl_trial_balance.accountID = pmca_chart.pmca_code
							AND ((gl_trial_balance.propertyID IN ('" . implode(
                    "','",
                    $propertySQL
                ) . "')) AND (gl_trial_balance.year < " . addSQLParam(
                    $params,
                    $year
                ) . ') AND (gl_trial_balance.accountID = ' . addSQLParam($params, $accountID) . "))
							) AS balanceCash,
							(SELECT
							COALESCE(SUM(gl_trial_balance.balanceAccruals), 0) AS balanceAccruals
							FROM gl_trial_balance gl_trial_balance, pmca_chart pmca_chart
							WHERE gl_trial_balance.accountID = pmca_chart.pmca_code
							AND ((gl_trial_balance.propertyID IN ('" . implode(
                    "','",
                    $propertySQL
                ) . "')) AND (gl_trial_balance.year < " . addSQLParam(
                    $params,
                    $year
                ) . ') AND (gl_trial_balance.accountID = ' . addSQLParam($params, $accountID) . "))
							) AS balanceAccruals,
							0 AS currentYearBalanceCash,
							0 AS currentYearBalanceAccruals
						FROM gl_trial_balance gl_trial_balance, pmca_chart pmca_chart
						WHERE gl_trial_balance.accountID = pmca_chart.pmca_code AND gl_trial_balance.propertyID IN ('" . implode(
                    "','",
                    $propertySQL
                ) . "') AND (gl_trial_balance.accountID = " . addSQLParam($params, $accountID) . ')
						GROUP BY gl_trial_balance.accountID, pmca_chart.pmca_type';
            }

            break;
    }

    $resultSQL = $dbh->executeSingle($sql, $params);

    return [
        'accountID' => $resultSQL['accountID'],
        'pmca_type' => $resultSQL['pmca_type'],
        'balanceCash' => ($resultSQL['balanceCash'] + $resultSQL['currentYearBalanceCash']),
        'balanceAccruals' => ($resultSQL['balanceAccruals'] + $resultSQL['currentYearBalanceAccruals']),
    ];
}

function dbCheckAccountIdExpDis($accountID)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);

    $sql = "SELECT * FROM pmca_chart WHERE pmca_code = ? AND pmca_gl_account_group1 = 'EXP' AND pmca_gl_account_group2 = 'EXP.DIS'";

    return $dbh->executeSet($sql, false, true, [$accountID]);
}

function dbCheckAccountIdExpDisOrBS($accountID)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);

    $sql = "SELECT * FROM pmca_chart WHERE pmca_code = ? AND pmca_gl_account_group2 IN ('ASS.CUR','ASS.FIX','ASS.NCA','EXP.DIS','LIA.CUR','EQU.RET')";

    return $dbh->executeSet($sql, false, true, [$accountID]);
}

function dbGetUserByID($userID)
{
    global $dbh;
    $dbh->selectDatabase(SYSTEMDB);
    $sql = "SELECT
			user_id AS userID,
			first_name AS firstName,
			last_name AS lastName,
			CONVERT(varchar(100), LTRIM(RTRIM(first_name)) + ' ' + LTRIM(RTRIM(last_name))) AS fullName,
			user_name as username,
			password AS password,
			user_type AS userType,
			user_sub_type AS userSubType,
			default_db AS defaultDB,
			email AS email,
			liscence_end AS licenseExpiry,
			terms_agreed AS termsAgreed,
			terms_date_agreed As termsAgreedDate,
			enable_chat AS enableChat
			FROM user_list
			WHERE user_id=?
			  ";

    return $dbh->executeSingle($sql, [$userID]);
}

function dbGetAllGLAccountGroups()
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $sql = 'SELECT gl_account_group AS accountGroup, gl_group_description AS accountDescription
			FROM gl_account_groups';

    return $dbh->executeSet($sql);
}

if (! function_exists('dbGetBalanceSheetDistinctAccountGroup2')) {
    function dbGetBalanceSheetDistinctAccountGroup2($properties, $accountGroup1)
    {
        global $clientDB, $dbh;
        $dbh->selectDatabase($clientDB);

        $sql = 'SELECT DISTINCT A.pmca_gl_account_group2 AS accountGroup2
			FROM pmca_chart A
			INNER JOIN gl_account_groups B ON A.pmca_gl_account_group1 = B.gl_account_group
			WHERE B.gl_account_group = ?';

        return $dbh->executeSet($sql, false, true, [$accountGroup1]);
    }
}

if (! function_exists('dbGetAccountGroup')) {
    function dbGetAccountGroup($accountID)
    {
        global $clientDB, $dbh;
        $dbh->selectDatabase($clientDB);
        $whereSql = $accountID ? 'WHERE A.pmca_code = ?' : '';

        $sql = "SELECT B.gl_account_group AS accountGroup, B.gl_group_description AS accountDescription
			FROM pmca_chart A INNER JOIN gl_account_groups B ON A.pmca_gl_account_group2 = B.gl_account_group
			{$whereSql}";

        if ($accountID) {
            return $dbh->executeSingle($sql, [$accountID]);
        } else {
            return $dbh->executeSet($sql, false, true, [$accountID]);
        }
    }
}

if (! function_exists('dbGetTrialBalanceYTDByAccounts')) {
    function dbGetTrialBalanceYTDByAccounts($properties, $account, $period, $year)
    {
        global $dbh;
        global $clientDB;
        $dbh->selectDatabase($clientDB);
        $params = [];
        $accountSQL = 'a.pmca_code IN (' . addSQLParam($params, $account) . ')';
        if (is_array($properties) && count($properties ?? []) > 0) {
            $properties = implode("','", $properties);
        }

        $sql = "
            SELECT
            COALESCE(SUM(b.balanceCash), 0)  AS balanceCash ,
            COALESCE(SUM(b.balanceAccruals), 0) AS balanceAccruals
            FROM
            pmca_chart a,
            gl_trial_balance b
            WHERE
            {$accountSQL}
            AND b.accountID = a.pmca_code
            AND b.propertyID IN ('" . $properties . "')
            AND b.year=" . addSQLParam($params, $year) . ' AND b.period <= ' . addSQLParam($params, $period) . '';

        return $dbh->executeSingle($sql, $params);
    }
}

if (! function_exists('dbGetTrialBalanceForPeriodByAccounts')) {
    function dbGetTrialBalanceForPeriodByAccounts($properties, $account, $period, $year, $periodTo = null)
    {
        global $dbh;
        global $clientDB;
        $dbh->selectDatabase($clientDB);


        if (! $periodTo) {
            $periodTo = $period;
        }

        $params = [];
        $accountSQL = 'a.pmca_code IN (' . addSQLParam($params, $account) . ')';
        if (is_array($properties) && count($properties ?? []) > 0) {
            $properties = implode("','", $properties);
        }

        $sql = "
            SELECT
            COALESCE(SUM(b.balanceCash), 0)  AS balanceCash ,
            COALESCE(SUM(b.balanceAccruals), 0) AS balanceAccruals
            FROM
            pmca_chart a,
            gl_trial_balance b
            WHERE
            {$accountSQL}
            AND b.accountID = a.pmca_code
            AND b.propertyID IN ('" . $properties . "')
            AND b.year=" . addSQLParam($params, $year) . ' AND b.period BETWEEN ' . addSQLParam(
            $params,
            $period
        ) . ' AND ' . addSQLParam($params, $periodTo) . '';

        return $dbh->executeSingle($sql, $params);
    }
}

if (! function_exists('dbGetCalendarYear')) {
    function dbGetCalendarYear()
    {
        global $clientDB, $dbh;
        $dbh->selectDatabase($clientDB);
        $sql = '
			SELECT DISTINCT
				pmcp_year as year
			FROM
				pmcp_prop_cal
		    ORDER BY pmcp_year DESC';

        return $dbh->executeSet($sql);
    }
}

if (! function_exists('dbGetKeysList')) {
    function dbGetKeysList($propertyIDs, $keyStatus)
    {
        global $clientDB, $dbh;
        $dbh->selectDatabase($clientDB);
        $params = [];

        if (is_array($propertyIDs) && count($propertyIDs ?? []) > 0) {
            $propertyIDs = implode("','", $propertyIDs);
        }

        if ($keyStatus) {
            if ($keyStatus == 'D') {
                $_keyStatus = " pmkd_key_detail.pmkd_return_due < CONVERT(VARCHAR(10), getdate(), 111) AND pmpk_p_key.pmpk_status = 'O' AND ";
            } else {
                $_keyStatus = ' pmpk_p_key.pmpk_status = ' . addSQLParam($params, $keyStatus) . ' AND ';
            }
        }

        $sql = "
		SELECT
            pmpk_p_key.pmpk_id AS keyID,
            pmpk_p_key.pmpk_prop as keyProperty,
            pmpk_p_key.pmpk_key as keyReference,
            pmpk_p_key.pmpk_desc as keyDescription,
            pmpk_p_key.pmpk_status as keyStatusCode,
            CASE
                WHEN pmpk_p_key.pmpk_status = 'I' THEN 'Checked In'
                WHEN pmpk_p_key.pmpk_status = 'O' THEN 'Checked Out'
                WHEN pmpk_p_key.pmpk_status = 'X' THEN 'Inactive'
            END as keyStatus
        FROM pmpk_p_key
        LEFT JOIN pmkd_key_detail
            ON pmpk_p_key.pmpk_prop = pmkd_key_detail.pmkd_prop
                AND pmpk_p_key.pmpk_detail_id = pmkd_key_detail.pmkd_id
                AND pmpk_p_key.pmpk_key = pmkd_key_detail.pmkd_key
        WHERE pmpk_p_key.pmpk_deleted_at IS NULL AND
        	{$_keyStatus}
			pmpk_p_key.pmpk_prop IN ('" . $propertyIDs . "')
		ORDER BY
			pmpk_p_key.pmpk_status, pmpk_p_key.pmpk_key";

        return $dbh->executeSet($sql, false, true, $params);
    }
}

if (! function_exists('dbGenerateList')) {
    function dbGenerateList($propertyIDs, $keyStatus)
    {
        global $clientDB, $dbh;
        $dbh->selectDatabase($clientDB);
        if (is_array($propertyIDs) && count($propertyIDs ?? []) > 0) {
            $propertyIDs = implode("','", $propertyIDs);
        }

        $params = [];
        if ($keyStatus) {
            if ($keyStatus == 'D') {
                $_keyStatus = " pmkd_key_detail.pmkd_return_due < CONVERT(VARCHAR(10), getdate(), 111) AND pmpk_p_key.pmpk_status = 'O' AND ";
            } else {
                $_keyStatus = ' pmpk_p_key.pmpk_status = ' . addSQLParam($params, $keyStatus) . ' AND ';
            }
        }

        $sql = "
		SELECT
            pmpk_p_key.pmpk_id AS keyID,
            pmpk_p_key.pmpk_prop as keyProperty,
            pmpr_property.pmpr_name as KeyPropertyName,
            pmpk_p_key.pmpk_key as keyReference,
            pmpk_p_key.pmpk_desc as keyDescription,
            pmpk_p_key.pmpk_status as keyStatusCode,
            CASE
                WHEN pmpk_p_key.pmpk_status = 'I' THEN 'Checked In'
                WHEN pmpk_p_key.pmpk_status = 'O' THEN 'Checked Out'
                WHEN pmpk_p_key.pmpk_status = 'X' THEN 'Inactive'
            END as keyStatus,
            pmpk_p_key.pmpk_detail_id as keyDetailID,
            CASE
                WHEN pmpk_p_key.pmpk_status = 'O' THEN pmkd_key_detail.pmkd_key_taker

            END as keyTaker,
            CASE
                WHEN pmpk_p_key.pmpk_status = 'O' THEN pmkd_key_detail.pmkd_checked_out

            END as keyCheckOut,
            CASE
                WHEN pmpk_p_key.pmpk_status = 'O' THEN pmkd_key_detail.pmkd_return_due

            END as keyReturnDue,
            pmkd_key_detail.pmkd_date_returned as keyDateReturned
        FROM pmpk_p_key
        LEFT JOIN pmpr_property
			ON pmpk_p_key.pmpk_prop = pmpr_property.pmpr_prop
        LEFT JOIN pmkd_key_detail
            ON pmpk_p_key.pmpk_prop = pmkd_key_detail.pmkd_prop
                AND pmpk_p_key.pmpk_detail_id = pmkd_key_detail.pmkd_id
                AND pmpk_p_key.pmpk_key = pmkd_key_detail.pmkd_key
        WHERE pmpk_p_key.pmpk_deleted_at IS NULL AND
			{$_keyStatus}
			pmpk_p_key.pmpk_prop IN ('{$propertyIDs}')
		ORDER BY
			pmpk_p_key.pmpk_prop, pmpk_p_key.pmpk_key";

        return $dbh->executeSet($sql, false, true, $params);
    }
}

if (! function_exists('dbTenantDetailList')) {
    function dbTenantDetailList($propertyID, $tenantIDs = '')
    {
        global $clientDB, $dbh;
        $dbh->selectDatabase($clientDB);
        $params = [$propertyID];

        $additionalSql = '';
        if (is_array($tenantIDs) && count($tenantIDs ?? []) > 0) {
            $additionalSql = " AND pmle_lease IN('" . implode("','", $tenantIDs) . "')";
        }

        $sql = "SELECT DISTINCT
                pmle_prop as propertyID,
                pmle_lease as leaseID,
                pmle_name AS leaseName,
                pmle_debtor AS debtorID,
                pmle_description AS description,
                pmle_t_name AS tradingName,
                pmpr_name AS propertyName,
                pmle_bad_debt AS badDebtProvision
            FROM pmle_lease
	        LEFT OUTER JOIN pmua_unit_area
	        ON pmua_lease = pmle_lease
            AND pmle_prop = pmua_prop
	        INNER JOIN pmpr_property
	        ON pmpr_prop = pmle_prop
		WHERE pmle_prop=?
		{$additionalSql}
		ORDER BY leaseID";

        return $dbh->executeSet($sql, false, true, $params);
    }
}

if (! function_exists('dbGetAgedUnallocated')) {
    function dbGetAgedUnallocated(
        $propertyID,
        $leaseID,
        $debtorID,
        $batchNumber,
        $batchLineNumber,
        $endDate
    ) {
        global $clientDB, $dbh;
        $dbh->selectDatabase($clientDB);
        $params = [$batchNumber, $batchLineNumber];
        $conditions = '';
        if ($debtorID != '') {
            $conditions .= ' AND (pmuc_s_debtor = ' . addSQLParam($params, $debtorID) . ')';
        }

        if ($propertyID != '') {
            $conditions .= ' AND (pmuc_prop = ' . addSQLParam($params, $propertyID) . ')';
        }

        if ($leaseID != '') {
            $conditions .= ' AND (pmuc_lease = ' . addSQLParam($params, $leaseID) . ')';
        }

        $sql = "
		SELECT
			COALESCE(SUM(pmuc_amt), 0) AS amount,
			pmuc_acc AS accountID
		FROM
			pmuc_unall_csh
		WHERE
			(pmuc_batch = ?)
			AND (pmuc_line = ?)
			{$conditions}
			AND pmuc_rcpt_dt <= CONVERT(datetime, " . addSQLParam($params, $endDate) . ', 103)
		GROUP BY
			pmuc_acc';

        return $dbh->executeSet($sql, false, true, $params);
    }
}

if (! function_exists('dbGetOutstandingAge')) {
    function dbGetOutstandingAge($propertyID = '', $leaseID = '', $debtorID = '', $startDate = '', $endDate = '')
    {
        global $dbh;
        global $clientDB;
        $dbh->selectDatabase($clientDB);
        $params = [$leaseID, $propertyID, $endDate];
        $conditionSQL = "((trans_type NOT IN ('REV','ADJ','INV'))
	                OR (trans_type = 'INV' AND artr_gst_inv_no != 0))";
        if ($debtorID != '') {
            $conditionSQL .= ' AND (debtor_code = ' . addSQLParam($params, $debtorID) . ')';
        }

        if ($propertyID != '') {
            $conditionSQL .= ' AND (ref_2 = ' . addSQLParam($params, $propertyID) . ')';
        }

        if ($leaseID != '') {
            $conditionSQL .= ' AND (ref_4 = ' . addSQLParam($params, $leaseID) . ')';
        }

        $conditionSQL .= ' AND (trans_date BETWEEN CONVERT(datetime, ' . addSQLParam(
            $params,
            $startDate
        ) . ', 103) AND CONVERT(datetime, ' . addSQLParam($params, $endDate) . ', 103))';


        $sql = "SELECT batch_nr AS batchNumber,
				batch_line_nr AS batchLineNumber,
				artr_tax_amt AS taxAmount,
				debtor_code AS debtorID,
				ref_2 AS propertyID,
				ref_3 AS accountID,
				ref_4 AS leaseID,
				description AS description,
				artr_gst_code AS taxCode,
				trans_amt AS amount,
				CONVERT(char(10), spare_date_1, 103) AS fromDate,
				CONVERT(char(10), spare_date_2, 103) AS toDate,
				artr_net_amt AS netAmount,
				CONVERT(char(10), trans_date, 103) AS transactionDate,
				artr_gst_inv_no as invoiceNumber,
				CONVERT(char(10), artr_gst_inv_dt, 103) as invoiceDate,
				trans_type AS transactionType,
				0 AS invoiceTotalReceipted,
				0 AS invoiceTotalCredited,
				0 AS invoiceTotalAdjusted,
				0 AS creditTotalReceipted,
				0 AS creditTotalAdjusted,
				0 AS creditTotalAllocated,
				0 AS invoiceTotalReceiptedOpposite,
				0 AS invoiceTotalCreditedOpposite,
				0 AS invoiceTotalAdjustedOpposite,
				0 AS creditTotalReceiptedOpposite,
				0 AS creditTotalAdjustedOpposite,
				0 AS creditTotalAllocatedOpposite,
				(SELECT COALESCE(SUM(pmuc_amt), 0) AS total_unallocated FROM pmuc_unall_csh WHERE (pmuc_lease=? AND pmuc_prop=? AND pmuc_unall_csh.pmuc_batch = RTRIM(ar_transaction.batch_nr)) AND (pmuc_unall_csh.pmuc_line = ar_transaction.batch_line_nr) AND (pmuc_rcpt_dt <= CONVERT(datetime, ?, 103))) AS unallocated,
				(SELECT TOP 1 pmuc_lease FROM pmuc_unall_csh WHERE (pmuc_unall_csh.pmuc_batch = RTRIM(ar_transaction.batch_nr)) AND (pmuc_unall_csh.pmuc_line = ar_transaction.batch_line_nr)) AS pmuc_lease
				FROM ar_transaction
				WHERE {$conditionSQL}
				 ORDER BY spare_date_1 DESC, ref_3, description";

        $existing = $dbh->executeSet($sql, false, true, $params);
        $params = [$endDate];
        $pmxdConditionSQL = '';
        if ($propertyID != '') {
            $pmxdConditionSQL .= ' AND pmxd_prop = ' . addSQLParam($params, $propertyID);
        }

        if ($leaseID != '') {
            $pmxdConditionSQL .= ' AND pmxd_lease = ' . addSQLParam($params, $leaseID);
        }

        $pmxd_sql = "SELECT pmxd_alloc_amt , pmxd_t_batch ,pmxd_t_line , pmxd_f_type , pmxd_t_type , pmxd_alloc_dt  ,
         pmxd_f_batch , pmxd_f_line FROM pmxd_ar_alloc WHERE (pmxd_alloc_dt  <= CONVERT(datetime, ? ,103)) {$pmxdConditionSQL}";

        $t_batch = [];
        $f_batch = [];
        $execute_pmxd = $dbh->executeSet($pmxd_sql, false, true, $params);
        foreach ($execute_pmxd as $row) {
            $t_batch[$row['pmxd_t_batch']][$row['pmxd_t_line']][$row['pmxd_f_type']][$row['pmxd_t_type']][] = $row['pmxd_alloc_amt'];
            $f_batch[$row['pmxd_f_batch']][$row['pmxd_f_line']][$row['pmxd_t_type']][$row['pmxd_f_type']][] = $row['pmxd_alloc_amt'];
        }

        $outstandingAmounts = [];
        $total = 0;
        foreach ($existing as $row) {
            $row['invoiceTotalReceipted'] = -1 * ((isset($t_batch[$row['batchNumber']][$row['batchLineNumber']]['CSH']['INV']) ? array_sum(
                $t_batch[$row['batchNumber']][$row['batchLineNumber']]['CSH']['INV']
            ) : 0) + (isset($t_batch[$row['batchNumber']][$row['batchLineNumber']]['CRE']['INV']) ? array_sum(
                $t_batch[$row['batchNumber']][$row['batchLineNumber']]['CRE']['INV']
            ) : 0));
            $row['creditTotalReceipted'] = -1 * (isset($t_batch[$row['batchNumber']][$row['batchLineNumber']]['CSH']['CRE']) ? array_sum(
                $t_batch[$row['batchNumber']][$row['batchLineNumber']]['CSH']['CRE']
            ) : 0);
            $row['creditTotalAllocatedOpposite'] = -1 * (isset($t_batch[$row['batchNumber']][$row['batchLineNumber']]['INV']['CRE']) ? array_sum(
                $t_batch[$row['batchNumber']][$row['batchLineNumber']]['INV']['CRE']
            ) : 0);

            $row['creditTotalAllocated'] = (isset($f_batch[$row['batchNumber']][$row['batchLineNumber']]['INV']['CRE']) ? array_sum(
                $f_batch[$row['batchNumber']][$row['batchLineNumber']]['INV']['CRE']
            ) : 0);
            $row['invoiceTotalReceiptedOpposite'] = (isset($f_batch[$row['batchNumber']][$row['batchLineNumber']]['CSH']['INV']) ? array_sum(
                $f_batch[$row['batchNumber']][$row['batchLineNumber']]['CSH']['INV']
            ) : 0) + (isset($f_batch[$row['batchNumber']][$row['batchLineNumber']]['CRE']['INV']) ? array_sum(
                $f_batch[$row['batchNumber']][$row['batchLineNumber']]['CRE']['INV']
            ) : 0);
            $row['creditTotalReceiptedOpposite'] = (isset($f_batch[$row['batchNumber']][$row['batchLineNumber']]['CSH']['CRE']) ? array_sum(
                $f_batch[$row['batchNumber']][$row['batchLineNumber']]['CSH']['CRE']
            ) : 0);

            $offset = getOffset(
                $row['invoiceTotalReceipted'],
                $row['invoiceTotalCredited'],
                $row['invoiceTotalAdjusted'],
                $row['creditTotalReceipted'],
                $row['creditTotalAdjusted'],
                $row['creditTotalAllocated'],
                $row['invoiceTotalReceiptedOpposite'],
                $row['invoiceTotalCreditedOpposite'],
                $row['invoiceTotalAdjustedOpposite'],
                $row['creditTotalReceiptedOpposite'],
                $row['creditTotalAdjustedOpposite'],
                $row['creditTotalAllocatedOpposite']
            );
            $row['unallocated'] = (float) $row['unallocated'];
            $row['amount'] = (float) $row['amount'];
            if ((($row['amount'] - $offset) != 0) && ($row['transactionType'] != 'CSH')) {
                $row['unpaidAmount'] = round($row['amount'] - $offset, 2);
                $total += $row['unpaidAmount'];
                $outstandingAmounts[] = $row;
            } elseif (($row['transactionType'] == 'CSH') && ($row['unallocated'] != '') && ($row['unallocated'] != 0)) {
                $unallocated = dbGetAgedUnallocated(
                    $row['propertyID'],
                    $row['leaseID'],
                    $row['debtorID'],
                    $row['batchNumber'],
                    $row['batchLineNumber'],
                    $endDate
                );
                $row['unpaidAmount'] = $row['unallocated'];
                $total += ($row['unallocated'] * 1);
                $outstandingAmounts[] = $row;
            }
        }

        $sql = "SELECT batch_nr AS batchNumber,
					batch_line_nr AS batchLineNumber,
					debtor_code AS debtorID,
					ref_2 AS propertyID,
					ref_3 AS accountID,
					ref_4 AS leaseID,
					description AS description,
					artr_gst_code AS taxCode,
					CONVERT(char(10),pmxd_alloc_dt , 103) AS transactionDate,
					CONVERT(char(10), spare_date_1, 103) AS fromDate,
					CONVERT(char(10), spare_date_2, 103) AS toDate,
					pmxd_alloc_amt AS amount,
					pmxd_alloc_amt AS unpaidAmount,
					artr_gst_inv_no as invoiceNumber,
					CONVERT(char(10), artr_gst_inv_dt, 103) as invoiceDate,
					trans_type AS transactionType
				FROM ar_transaction, pmxd_ar_alloc
				WHERE pmxd_t_batch = batch_nr
				AND pmxd_t_line = batch_line_nr
				AND (pmxd_lease=?)
				AND ((pmxd_f_type='CSH') OR (pmxd_f_type='REV'))
				AND trans_date>CONVERT(datetime, ?, 103)
				AND (pmxd_alloc_dt <= CONVERT(datetime, ?, 103))
				AND (pmxd_prop=?)
				";

        $params = [$leaseID, $endDate, $endDate, $propertyID];
        $futureReceipts = $dbh->executeSet($sql, false, true, $params);
        foreach ($futureReceipts as $row) {
            $total += ($row['amount'] * 1);
            $outstandingAmounts[] = $row;
        }

        return $outstandingAmounts;
    }
}

if (! function_exists('dbGetInvoiceChargesUnpaidDebtors')) {
    function dbGetInvoiceChargesUnpaidDebtors(
        $propertyID,
        $leaseID,
        $debtorID,
        $dueDate,
        $receiptsToDate = ''
    ) {
        global $clientDB, $dbh;
        $dbh->selectDatabase($clientDB);

        if ($receiptsToDate == '') {
            $receiptsToDate = $dueDate;
        }

        $params = [$receiptsToDate, $receiptsToDate, $receiptsToDate];

        $conditionSQL = "((trans_type NOT IN ('REV','ADJ','INV'))
	                OR (trans_type = 'INV' AND artr_gst_inv_no != 0))";
        if ($debtorID != '') {
            $conditionSQL .= ' AND (debtor_code = ' . addSQLParam($params, $debtorID) . ')';
        }

        if ($propertyID != '') {
            $conditionSQL .= ' AND (ref_2 = ' . addSQLParam($params, $propertyID) . ')';
        }

        if ($leaseID != '') {
            $conditionSQL .= ' AND (ref_4 = ' . addSQLParam($params, $leaseID) . ')';
        }

        $sql = "
        SELECT * FROM (
            SELECT
                batch_nr AS batchNumber,
                batch_line_nr AS batchLineNumber,
                artr_tax_amt AS taxAmount,
                debtor_code AS debtorID,
                ref_2 AS propertyID,
                ref_3 AS accountID,
                ref_4 AS leaseID,
                pmpu_unit AS unitID,
                pmpu_desc AS unitDescription,
                description AS description,
                artr_gst_code AS taxCode,
                trans_amt AS amount,
                artr_net_amt AS netAmount,

                spare_date_1 AS fromDate,
                spare_date_2 AS toDate,
                trans_date AS transactionDate,
                due_date AS dueDate,
                artr_gst_inv_dt AS invoiceDate,

                artr_gst_inv_no as invoiceNumber,
                trans_type AS transactionType,

                pmco_name AS debtorName,
                pmpr_name AS propertyName,
                pmle_name AS leaseName,
                pmca_name AS accountName,

                (COALESCE(SUM(pmxd_tax_amt), 0) * -1) AS totalAllocatedTax,
                (COALESCE(SUM(pmxd_alloc_amt), 0) * -1) AS totalAllocated,
                (COALESCE(SUM(pmxd_alloc_amt-pmxd_tax_amt), 0) * -1 ) AS totalAllocatedNet,
                (SELECT COALESCE(SUM(x_0.pmxd_alloc_amt), 0) FROM pmxd_ar_alloc x_0 WHERE (x_0.pmxd_f_batch = batch_nr) AND (x_0.pmxd_f_line = batch_line_nr) AND (x_0.pmxd_f_type IN ('INV','CRE') AND x_0.pmxd_t_type IN ('INV','CRE')) AND (x_0.pmxd_alloc_dt <= CONVERT(datetime,?,103))) AS totalReallocated,
                (SELECT COALESCE(SUM(pmuc_amt * - 1), 0) AS total_unallocated FROM pmuc_unall_csh WHERE (pmuc_batch = RTRIM(batch_nr)) AND (pmuc_line = batch_line_nr) AND pmuc_rcpt_dt <= CONVERT(datetime, ? , 103)) AS unallocated
            FROM
                ar_transaction
            JOIN pmco_company
              ON pmco_code = debtor_code

            JOIN pmpr_property
              ON pmpr_prop = ref_2

            JOIN pmle_lease
              ON pmle_lease = ref_4
              AND pmle_prop = ref_2

            LEFT JOIN pmca_chart
              ON pmca_code = ref_3

            LEFT JOIN pmpu_p_unit
                  ON ref_5 = pmpu_unit
                  AND ref_2 = pmpu_prop

            LEFT JOIN pmxd_ar_alloc X_1 ON X_1.pmxd_t_batch = batch_nr AND X_1.pmxd_t_line = batch_line_nr AND X_1.pmxd_t_type IN ('INV','CRE') AND X_1.pmxd_alloc_dt <= CONVERT(datetime, ? , 103)

            WHERE
                {$conditionSQL}

            GROUP BY
            batch_nr , batch_line_nr , artr_tax_amt, debtor_code , ref_2, ref_3,
             ref_4, pmpu_unit, pmpu_desc, description, artr_gst_code , trans_amt  ,
              artr_net_amt, spare_date_1 , spare_date_2,
               trans_date , due_date , artr_gst_inv_no ,
             artr_gst_inv_dt , trans_type, pmco_name , pmpr_name,
             pmle_name , pmca_name
        ) AS x
        WHERE (dueDate <= CONVERT(datetime, " . addSQLParam($params, $dueDate) . ", 103)
                OR (transactionType = 'CSH' AND unallocated != 0)
			    OR (transactionType = 'CRE' AND CONVERT(date, transactionDate) <= CONVERT(datetime, " . addSQLParam(
            $params,
            $dueDate
        ) . ", 103))
				OR (dueDate IS NULL AND (totalAllocated != 0 OR totalReallocated != 0) AND transactionType = 'CRE' AND CONVERT(date, transactionDate) <= CONVERT(datetime, " . addSQLParam(
            $params,
            $dueDate
        ) . ', 103)))
		ORDER BY
			transactionDate DESC,
			accountID,
			fromDate DESC,
			description';

        return array_unique($dbh->executeSet($sql, false, true, $params), SORT_REGULAR);
    }
}

if (! function_exists('dbGetUnallocated')) {
    function dbGetUnallocated(
        $propertyID,
        $leaseID,
        $debtorID,
        $batchNumber,
        $batchLineNumber,
        $invoiceDate
    ) {
        global $clientDB, $dbh;
        $dbh->selectDatabase($clientDB);
        $params = [];
        $conditions_params = [];

        $conditions = '';
        if ($debtorID != '') {
            $conditions .= ' AND (u.pmuc_s_debtor = ' . addSQLParam($conditions_params, $debtorID) . ')';
        }

        if ($propertyID != '') {
            $conditions .= ' AND (u.pmuc_prop = ' . addSQLParam($conditions_params, $propertyID) . ')';
        }

        if ($leaseID != '') {
            $conditions .= ' AND (u.pmuc_lease = ' . addSQLParam($conditions_params, $leaseID) . ')';
        }

        $sql = '
		SELECT DISTINCT
			u.pmuc_prop AS propertyID,
			u.pmuc_s_bank AS bankID,
			u.pmuc_s_debtor AS debtorID,
			u.pmuc_lease AS leaseID,
			u.pmuc_acc AS accountID,
			u.pmuc_desc AS description,
			u.pmuc_gst_code AS taxCode,
			(SELECT pmpr_name FROM pmpr_property WHERE (pmpr_prop = u.pmuc_prop)) AS propertyName,
			(SELECT pmca_name FROM pmca_chart WHERE (pmca_code = u.pmuc_acc)) AS accountName,
			(SELECT TOP 1 CONVERT(char(10), u.pmuc_rcpt_dt, 103)  FROM pmuc_unall_csh u WHERE (u.pmuc_batch = ' . addSQLParam(
            $params,
            $batchNumber
        ) . ') AND (u.pmuc_line = ' . addSQLParam($params, $batchLineNumber) . ") {$conditions} " . addSQLParam(
            $params,
            $conditions_params,
            false
        ) . ') AS date,
			(SELECT TOP 1 p.pmuc_serial FROM pmuc_unall_csh p WHERE (p.pmuc_batch = ' . addSQLParam(
            $params,
            $batchNumber
        ) . ') AND (p.pmuc_line = ' . addSQLParam($params, $batchLineNumber) . ") {$conditions} " . addSQLParam(
            $params,
            $conditions_params,
            false
        ) . ' AND p.pmuc_desc=u.pmuc_desc AND p.pmuc_acc = u.pmuc_acc AND p.pmuc_s_debtor=u.pmuc_s_debtor AND p.pmuc_prop=u.pmuc_prop AND p.pmuc_lease=u.pmuc_lease AND p.pmuc_rev_dt IS NULL) AS serial,
			COALESCE(SUM(u.pmuc_amt), 0) as amount,
			COALESCE(SUM(u.pmuc_tax_amt), 0) as taxAmount,
			COALESCE(SUM(u.pmuc_net_amt), 0) as netAmount
		FROM
			pmuc_unall_csh u
		WHERE
			(u.pmuc_batch = ' . addSQLParam($params, $batchNumber) . ') AND (u.pmuc_line = ' . addSQLParam(
            $params,
            $batchLineNumber
        ) . ')
			AND (pmuc_rcpt_dt <= CONVERT(datetime, ' . addSQLParam($params, $invoiceDate) . ", 103))
			{$conditions} " . addSQLParam($params, $conditions_params, false) . '
		GROUP BY
			u.pmuc_prop,
			u.pmuc_s_bank,
			u.pmuc_s_debtor,
			u.pmuc_lease,
			u.pmuc_acc,
			u.pmuc_desc,
			u.pmuc_gst_code';

        return $dbh->executeSet($sql, false, true, $params);
    }
}

if (! function_exists('dbGetPropertiesCurrentLeaseCount')) {
    function dbGetPropertiesCurrentLeaseCount($portfolio = '')
    {
        global $dbh;
        global $clientDB;
        $dbh->selectDatabase($clientDB);
        $sql = "SELECT COUNT(pmle_lease) as total FROM pmle_lease WHERE  pmle_status='C' and pmle_prop =  ? ";

        return $dbh->executeSingle($sql, [$portfolio]);
    }
}

if (! function_exists('dbGetCountFloors')) {
    function dbGetCountFloors($portfolio = '')
    {
        global $dbh;
        global $clientDB;
        $dbh->selectDatabase($clientDB);
        $sql = 'SELECT COUNT(pmpf_prop) as total FROM pmpf_p_floor WHERE  pmpf_prop =  ? ';

        return $dbh->executeSingle($sql, [$portfolio]);
    }
}

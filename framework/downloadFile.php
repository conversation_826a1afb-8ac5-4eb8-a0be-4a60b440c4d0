<?php
//-- include and invoke the session handler
include 'lib/classes/Session.php';
$sess = new Session ();
//$dispatch = array();

//session_start();
ob_start();

  if (!function_exists("ob_get_clean")) {
    function ob_get_clean() {
        $ob_contents = ob_get_contents();
        ob_end_clean();
        return $ob_contents;
    }
}

include('config.php');


date_default_timezone_set ('Australia/Perth');
define ('TODAY', date ('d/m/Y'));

$referrer = 'https://' . $_SERVER['HTTP_HOST'];


if ($referrer == HTTPHOST)
{
    $sess->set ('referrer', HTTPHOST . $_SERVER['SCRIPT_NAME'] . '?' . $_SERVER['QUERY_STRING']);
    // echo HTTPHOST . $_SERVER['SCRIPT_NAME'] . '?' .$_SERVER['QUERY_STRING'];
}
else $sess->drop ('referrer');

$sess->drop ('queries');

downloadFile();

?>
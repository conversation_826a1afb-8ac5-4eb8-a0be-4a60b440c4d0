<?php

function dbGetAllDiaryDates($portfolio = '', $completed = true)
{
    global $dbh, $clientDB;
    $dbh->selectDatabase($clientDB);
    $params = [];

    $conditionSQL = ($portfolio == '') ? '' : 'pmpr_portfolio=' . addSQLParam($params, $portfolio) . ' AND';
    $completedSQL = ($completed) ? '' : 'pmdy_comp_stamp IS NULL AND';
    $sql = "SELECT CONVERT(char(10), pmdy_date, 103) AS diaryDate,
                  pmdy_par_code AS diaryCode,
                  pmdy_text AS diaryText,
                  pmdy_prop as propertyID,
                  pmdy_lease as leaseID,
                  pmdy_entered_by AS diaryUser,
                  CONVERT(char(10), pmdy_comp_stamp,103) AS diaryCompleted,
                  pmdy_comp_user AS diaryCompletedBy,
                  pmdy_id AS diaryID,
                  (SELECT pmpr_name FROM pmpr_property WHERE pmpr_prop=pmdy_prop) AS propertyName,
                  (SELECT pmle_name FROM pmle_lease WHERE pmle_lease=pmdy_lease AND pmle_prop=pmdy_prop) AS leaseName
                FROM pmdy_diary
                WHERE
                {$completedSQL}
               ((pmdy_prop = '') OR (pmdy_prop IN (SELECT pmpr_prop FROM pmpr_property WHERE {$conditionSQL} pmpr_delete=0)))
                ORDER BY pmdy_date ASC,
                pmdy_prop ASC,
                pmdy_lease ASC
                ";

    return $dbh->executeSet($sql, false, true, $params);
}

function dbGetAllTraccDates()
{
    global $dbh, $clientDB;
    $dbh->selectDatabase($clientDB);
    $sql = "SELECT CONVERT(char(10), pmdy_date, 103) AS diaryDate,
                  pmdy_par_code AS diaryCode,
                  pmdy_text AS diaryText,
                  pmdy_entered_by AS diaryUser,
                  CONVERT(char(10), pmdy_comp_stamp,103) AS diaryCompleted,
                  pmdy_comp_user AS diaryCompletedBy
                FROM pmdy_diary, pmzz_param
                WHERE
                pmdy_prop = ''
                AND pmdy_lease = ''
                AND pmdy_par_code = pmzz_code
                AND pmzz_par_type = 'TRACCDIARY'
                ORDER BY pmdy_date ASC,
                pmdy_prop ASC,
                pmdy_lease ASC
                ";

    return $dbh->executeSet($sql);
}


function dbGetUserByID($userID)
{
    global $dbh;
    $dbh->selectDatabase(SYSTEMDB);
    $sql = "SELECT first_name AS firstName,
                last_name AS lastName,
                CONVERT(varchar(100), LTRIM(RTRIM(first_name)) + ' ' + LTRIM(RTRIM(last_name))) AS fullName,
                user_name as username,
                password AS password,
                user_type AS userType,
                user_sub_type AS userSubType,
                email AS email,
                user_id AS userID
                FROM user_list
                WHERE user_id = ?
              ";

    return $dbh->executeSingle($sql, [$userID]);
}




include_once __DIR__ . '/config.php';

error_reporting(0);

$dbh = new MSSQL(SYSTEMDSN);

$o = decodeObject($_REQUEST['meta']);
$databaseID = $o['databaseID'];
$userID = $o['userID'];

// $databaseID = $_REQUEST['databaseID'];
// $userID = $_REQUEST['userID'];


$database = dbGetDatabase($databaseID);
if ($database) {
    $clientDB = $database['database_name'];
    $clientDSN = COREDSN . $database['database_name'];
}



// -- set up the user
// $sess->set('registered',true);



// -- need to register in the database that a user has logged in!
$userDetails = dbGetUserByID($userID);
$portfolio = dbGetPortfolio($userID, $databaseID);

$diary = dbGetAllDiaryDates($portfolio, false);


$config    = [
    kigkonsult\iCalcreator\util\util::$UNIQUE_ID => 'cirrus8.com.au',
    kigkonsult\iCalcreator\util\util::$DELIMITER => '/',
    kigkonsult\iCalcreator\util\util::$TZID => 'Australia/Perth',
    kigkonsult\iCalcreator\util\util::$DIRECTORY => CALPATH,
];
$v = new kigkonsult\iCalcreator\vcalendar($config);

$v->setProperty('method', 'PUBLISH');
$v->setProperty('X-WR-CALNAME', 'Property Management Calendar');
$v->setProperty('X-WR-CALDESC', 'Tracc Calendar');
$v->setProperty('X-WR-TIMEZONE', 'Australia/Perth');

$diaryTypeList = mapParameters(dbGetParams('DIARYTYPE'));
$eventTypeList = mapParameters(dbGetParams('EVENTTYPE'));


foreach ($diary as $d) {
    // echo $d['diaryDate'] . '<br/>';
    if ($d['diaryDate'] != FINALDATE) {
        // $e = $vcalendar->newComponent( kigkonsult\iCalcreator\util\util::$LCVEVENT );
        $e = & $v->newComponent('vevent');
        if (! isset($diaryTypeList[$d['diaryCode']])) {
            $e->setProperty('categories', 'Tracc Event');
        } elseif ($d['leaseID']) {
            $e->setProperty('categories', 'Lease');
        } else {
            $e->setProperty('categories', 'Property');
        }

        $date = explode('/', $d['diaryDate']);
        if (count($date) == 3) {
            $start = ['year' => $date[DATE_YEAR], 'month' => $date[DATE_MONTH], 'day' => $date[DATE_DAY], 'hour' => 00, 'minute' => 00, 'second' => 00];
            $date = $date[DATE_YEAR] . $date[DATE_MONTH] . $date[DATE_DAY];
            $e->setProperty('dtstart', $date, ['VALUE' => 'DATE']);   // 24 dec 2007 19.30
            $title =  (isset($diaryTypeList[$d['diaryCode']])) ? $diaryTypeList[$d['diaryCode']] : $d['diaryText'];
            $e->setProperty('summary', $title);
            $e->setProperty('description', $d['diaryText']);    // describe the event
            $e->setProperty('location', $d['propertyName']);
            $v->setComponent($e);
        }
    }
}

$v->returnCalendar();

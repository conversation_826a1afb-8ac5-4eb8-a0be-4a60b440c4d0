<?php

// all database code is stored in this file. The "Abstraction layer" for data is contained in the driver code.
// We can connect the same database code up to multiple different databases, provided the SQL is standard
// Things like TOP / LIMIT (mssql/mysql) will cause problems though
function dbLogin($login)
{
    global $dbh;

    $dbh->selectDatabase(SYSTEMDB);

    $sql = 'SELECT u.userID AS userID, u.username AS username, u.email AS email, u.level AS level FROM users u WHERE u.username=? AND u.password=PASSWORD(?)';

    return $dbh->executeSingle($sql, [$login['txtUsername'], $login['txtPassword']]);
}

/**
 * @modified 2012-09-04: Cleansed username. [Morph]
 **/
function dbGetUser($username)
{
    global $dbh;
    $dbh->selectDatabase(SYSTEMDB);
    $sql = "SELECT first_name AS firstName,
				last_name AS lastName,
				CONVERT(varchar(100), LTRIM(RTRIM(first_name)) + ' ' + <PERSON><PERSON><PERSON>(RTRIM(last_name))) AS fullName,
				user_name as username,
				password AS password,
				user_type AS userType,
				user_sub_type AS userSubType,
				email AS email,
				user_id AS userID
				FROM user_list
				WHERE user_name = ?
			  ";

    return $dbh->executeSingle($sql, [$username]);
}


function dbGetModules($userGroup = null)
{
    global $dbh;
    $dbh->selectDatabase(SYSTEMDB);

    $params = [];
    $userSQL = ($userGroup) ? 'WHERE permissions LIKE ' . addSQLParam($params, '%' . $userGroup . '%') : null;

    $sql = "SELECT moduleID, name
  FROM modules
  {$userSQL}
  ORDER BY name";

    return $dbh->executeSet($sql, false, true, $params);
}


function dbGetMenuGroups($userGroup = null)
{
    global $dbh;
    $dbh->selectDatabase(SYSTEMDB);

    $params = [];
    $userSQL = ($userGroup) ? 'WHERE permissions LIKE ' . addSQLParam($params, '%' . $userGroup . '%') : null;

    $sql = "
		SELECT
			menuGroupID,
			name
		FROM
			menuGroups
		{$userSQL}
		ORDER BY
			name";

    return $dbh->executeSet($sql, false, true, $params);
}


function dbTogglePageDisplay($pageID)
{
    global $dbh;
    $dbh->selectDatabase(SYSTEMDB);
    $sql = 'UPDATE pages SET display = 1 - display WHERE pageID = ?';

    return $dbh->executeNonQuery2($sql, [$pageID]);
}

function dbTogglePageAdministrator($pageID)
{
    global $dbh;
    $dbh->selectDatabase(SYSTEMDB);
    $sql = 'UPDATE pages SET administrator = 1 - administrator WHERE pageID = ?';

    return $dbh->executeNonQuery2($sql, [$pageID]);
}

function dbGetCommandsByMenuGroup($menuGroupID)
{
    global $dbh;
    $dbh->selectDatabase(SYSTEMDB);
    $sql = 'SELECT m.name, p.command, p.pageID, p.title, p.sequence, p.link, p.moduleID, p.display, p.administrator, p.readOnlyPage
  FROM pages p, modules m
  WHERE
	p.moduleID = m.moduleID
	AND p.menuGroupID = ?
  ORDER BY p.sequence ASC
			  ';

    return $dbh->executeSet($sql, false, true, [$menuGroupID]);
}


function dbGetCommands($moduleID)
{
    global $dbh;
    $dbh->selectDatabase(SYSTEMDB);
    $sql = 'SELECT command, pageID, title
  FROM pages
  WHERE
	moduleID = ?
	AND command IS NOT NULL
  ORDER BY title
			  ';

    return $dbh->executeSet($sql, false, true, [$moduleID]);
}

function dbGetPage($pageID)
{
    global $dbh;
    $dbh->selectDatabase(SYSTEMDB);
    $sql = 'SELECT *
  FROM pages
  WHERE
	pageID = ?

			  ';

    return $dbh->executeSingle($sql, [$pageID]);
}


function dbAddPage($o)
{
    global $dbh;
    $dbh->selectDatabase(SYSTEMDB);

    $params = [];
    $sql = 'INSERT INTO pages
		(
		title,
		sequence,
		link,
		command,
		moduleID,
		menuGroupID,
		readOnlyPage
		) VALUES (
		' . addSQLParam($params, $o->title) . ',
		' . addSQLParam($params, $o->sequence) . ',
		' . addSQLParam($params, $o->link) . ',
		' . addSQLParam($params, $o->command) . ',
		' . addSQLParam($params, $o->moduleID) . ',
		' . addSQLParam($params, $o->menuGroupID) . ',
		0
		)
			  ';

    return $dbh->executeNonQuery2($sql, $params);
}

function dbUserLicense($username)
{
    global $dbh;

    $dbh->selectDatabase(SYSTEMDB);

    $sql = 'SELECT DATEDIFF(D, GETDATE(), liscence_end) AS daysLeft,  CONVERT(CHAR(10), liscence_end, 103) AS endDate FROM user_list WHERE (user_name = ?)';

    return $dbh->executeSingle($sql, [$username]);
}

function dbUserList()
{
    global $dbh;

    $dbh->selectDatabase(SYSTEMDB);

    $sql = 'SELECT  first_name,last_name,user_name, user_id FROM user_list';

    return $dbh->executeSet($sql);
}


function dbUserGroupList()
{
    global $dbh;
    $dbh->selectDatabase(SYSTEMDB);
    $sql = 'SELECT * FROM userGroups';

    return $dbh->executeSet($sql);
}


function dbDeletePage($pageID)
{
    global $dbh;
    $dbh->selectDatabase(SYSTEMDB);
    $sql = 'DELETE FROM pages WHERE pageID = ?';

    return $dbh->executeNonQuery2($sql, [$pageID]);
}


function dbGetMenuGroupForCommand($userGroup, $command, $module)
{
    global $dbh;
    $dbh->selectDatabase(SYSTEMDB);
    $sql = 'SELECT
			p.menuGroupID,
			p.pageID
			FROM
			modules m,
			pages p,
			menuGroups g,
			userGroups u
			WHERE
				g.menuGroupID = p.menuGroupID
				AND g.groupID = u.groupID
				AND u.groupCode = ?
				AND p.moduleID = m.moduleID
				AND m.name = ?
				AND p.command = ?
			';

    return $dbh->executeObject($sql, [$userGroup, $module, $command]);
}

function dbGetMenuForGroup($menuGroupID)
{
    global $dbh;
    $dbh->selectDatabase(SYSTEMDB);
    $superSQL = ($_SESSION['super']) ? '' : 'AND p.administrator = 0';
    $readonlySQL = ($_SESSION['readOnlyAccess']) ? 'AND p.readOnlyPage = 0' : '';
    $APUserSQL = ($_SESSION['user_sub_type'] == 'AP_1') ? " AND pageID IN ('126','127','679')" : '';
    $PMPLUSUserSQL = ($_SESSION['user_sub_type'] == 'PM_PLUS') ? " AND pageID IN ('1','215')" : '';
    $sql = "SELECT
			p.title,
			p.permissions,
			p.link,
			p.command,
			m.name,
			p.pageID
			FROM
			modules m,
			pages p
			WHERE
				p.display = 1
				{$superSQL}
				{$readonlySQL}
				AND p.moduleID = m.moduleID
				AND p.menuGroupID = ?
				{$APUserSQL}
				{$PMPLUSUserSQL}
			ORDER BY p.sequence ASC";

    return $dbh->executeSet($sql, false, true, [$menuGroupID]);
}

function dbGetMenuForGroupRedis($interval, $menuGroupID)
{
    try {
        global $dbh, $redis;
        $dbh->selectDatabase(SYSTEMDB);
        $redisKey = 'cirrus:menuForGroupCache_' . $menuGroupID . '_' . $_SESSION['super'] . '_' . $_SESSION['readOnlyAccess'] . '_' . $_SESSION['user_sub_type']; // Redis Key must be unique

        $superSQL = ($_SESSION['super']) ? '' : 'AND p.administrator = 0';
        $readonlySQL = ($_SESSION['readOnlyAccess']) ? 'AND p.readOnlyPage = 0' : '';
        $APUserSQL = ($_SESSION['user_sub_type'] == 'AP_1') ? " AND pageID IN ('126','127','679')" : '';
        $PMPLUSUserSQL = ($_SESSION['user_sub_type'] == 'PM_PLUS') ? " AND pageID IN ('1','215')" : '';
        if (! $redis->exists($redisKey)) {
            $sql = "SELECT
			p.title,
			p.permissions,
			p.link,
			p.command,
			m.name,
			p.pageID
			FROM
			modules m,
			pages p
			WHERE
				p.display = 1
				{$superSQL}
				{$readonlySQL}
				AND p.moduleID = m.moduleID
				AND p.menuGroupID = ?
				{$APUserSQL}
				{$PMPLUSUserSQL}
			ORDER BY p.sequence ASC";
            $data = $dbh->executeSet($sql, false, true, [$menuGroupID]);

            $redis->pipeline()->set($redisKey, json_encode($data, true))->execute();
            $redis->expireat($redisKey, strtotime('+' . $interval . ' minute'));
        }

        $dataRedis = $redis->pipeline()->get($redisKey)->execute();
        foreach ($dataRedis as $aDataRedis) {
            return json_decode($aDataRedis, true);
        }
    } catch (Exception $exception) {
        return dbGetMenuForGroup($menuGroupID);
    }


}

function dbGetMaxMenuSequence($menuGroupID)
{
    global $dbh;
    $dbh->selectDatabase(SYSTEMDB);
    $sql = 'SELECT MAX(sequence) FROM pages WHERE menuGroupID = ?';

    return $dbh->executeScalar($sql, [$menuGroupID]);
}

function dbUpdateMenuSequenceByID($pageID, $sequence)
{
    global $dbh;
    $dbh->selectDatabase(SYSTEMDB);
    $sql = 'UPDATE pages SET sequence = ? WHERE pageID = ?';

    return $dbh->executeNonQuery2($sql, [$sequence, $pageID]);
}

function dbUpdateMenuSequenceBySequence($menuGroupID, $oldsequence, $sequence)
{
    global $dbh;
    $dbh->selectDatabase(SYSTEMDB);
    $sql = 'UPDATE pages SET sequence = ? WHERE menuGroupID = ? AND sequence = ?';

    return $dbh->executeNonQuery2($sql, [$sequence, $menuGroupID, $oldsequence]);
}


function dbGetMenu()
{
    global $dbh;

    $dbh->selectDatabase(SYSTEMDB);

    $sql = 'SELECT name,title,permissions,link FROM modules WHERE menuItem=1 ORDER BY priority ASC';

    return $dbh->executeSet($sql);
}

function dbGetMenuRedis($interval)
{
    try {
        global $dbh, $redis;
        $dbh->selectDatabase(SYSTEMDB);
        $redisKey = 'cirrus:menuCache'; // Redis Key must be unique

        if (! $redis->exists($redisKey)) {
            $sql = 'SELECT name,title,permissions,link FROM modules WHERE menuItem=1 ORDER BY priority ASC';
            $data = $dbh->executeSet($sql);

            $redis->pipeline()->set($redisKey, json_encode($data, true))->execute();
            $redis->expireat($redisKey, strtotime('+' . $interval . ' minute'));
        }

        $dataRedis = $redis->pipeline()->get($redisKey)->execute();
        foreach ($dataRedis as $aDataRedis) {
            return json_decode($aDataRedis, true);
        }
    } catch (Exception $exception) {
        return dbGetMenu();
    }


}

function dbGetSubMenu($module)
{
    global $dbh;

    $dbh->selectDatabase(SYSTEMDB);

    $sql = 'SELECT p.title,p.permissions,p.link,p.command,m.name FROM modules m, pages p WHERE m.menuItem=1 AND p.moduleID=m.moduleID AND m.name=? ORDER BY sequence ASC';

    return $dbh->executeSet($sql, false, true, [$module]);
}

function dbPageAccess($page)
{
    global $dbh;

    $dbh->selectDatabase(SYSTEMDB);

    $sql = 'SELECT permissions FROM modules WHERE name=?';

    return $dbh->executeSingle($sql, [$page]);
}

function dbGetPortfolio($userID, $databaseID)
{
    global $dbh;
    $dbh->selectDatabase(SYSTEMDB);
    $sql = 'SELECT portfolio FROM user_dbaccess WHERE user_id=? AND db_id=?';

    return $dbh->executeScalar($sql, [$userID, $databaseID]);
}

/**
 * @2013-07-10: Added additional parameter, $parameterMapping. [Morph]
 **/
function dbGetParams($param, $sort = true, $parameterMapping = false, $orderBySql = '', $linked_database = false)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    if ($linked_database) {
        $dbh->selectDatabase($linked_database);
    }

    $sortSQL = '';
    if ($sort) {
        $sortSQL = ' ORDER BY pmzz_desc';
    }

    if ($orderBySql) {
        $sortSQL = $orderBySql;
    }

    $sql = "
		SELECT
			pmzz_code as parameterID,
			pmzz_desc as parameterDescription,
			pmzz_type as paramType,
			pmzz_percentage as paramPercentage
		FROM
			pmzz_param
		WHERE
			pmzz_par_type = ?
		{$sortSQL}";
    $result = $dbh->executeSet($sql, false, true, [$param]);
    if ($parameterMapping) {
        $newResult = false;
        foreach ($result as $v) {
            $newResult[$v['parameterID']] = $v['parameterDescription'];
        }

        return $newResult;
    }

    return $result;
}

function dbGetReceiptingClearDaysList()
{
    $clear_days = [
        TYPE_CHEQUE => 3,
        TYPE_DIRECTDEPOSIT => 0,
        TYPE_DIRECTDEBIT => 3,
        TYPE_BPAY => 1,
        TYPE_EFTPOS => 1,
        TYPE_CASH => 0,
    ];
    $param_clear_days = dbGetParams('RCLEARDAYS');
    if (count($param_clear_days ?? []) > 0) {
        foreach ($param_clear_days as $par) {
            $code = (int) str_replace('CD-', '', $par['parameterID']);
            $days = trim($par['parameterDescription']);
            if (isset($clear_days[$code]) && ctype_digit($days) && (int) $days >= 0 && (int) $days <= 6) {
                $clear_days[$code] = $days;
            }
        }
    }

    return $clear_days;
}

function dbGetParamDetail($param, $code)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);

    $sql = '
		SELECT
			pmzz_desc as parameter,
			pmzz_type as paramType,
			pmzz_percentage as paramPercentage
		FROM
			pmzz_param
		WHERE
			pmzz_par_type=?
			AND pmzz_code=?';

    return $dbh->executeSingle($sql, [$param, $code]);
}

function dbGetParam($param, $code)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);

    $sql = '
		SELECT
			pmzz_desc as parameter
		FROM
			pmzz_param
		WHERE
			pmzz_par_type=?
			AND pmzz_code=?';

    return $dbh->executeScalar($sql, [$param, $code]);
}

function dbGetParamRedis($interval, $param, $code)
{
    try {
        global $clientDB, $dbh, $redis;

        if (! $clientDB) {
            return false;
        }

        $dbh->selectDatabase($clientDB);
        $redisKey = 'cirrus:paramForLocalizationCache_' . $_SESSION['currentDB'] . '_' . $param . '_' . $code; // Redis Key must be unique

        if (! $redis->exists($redisKey)) {
            $sql = '
            SELECT
                pmzz_desc as parameter
            FROM
                pmzz_param
            WHERE
                pmzz_par_type=?
			AND pmzz_code=?';
            $data = $dbh->executeScalar($sql, [$param, $code]);

            $redis->pipeline()->set($redisKey, json_encode($data, true))->execute();
            $redis->expireat($redisKey, strtotime('+' . $interval . ' minute'));
        }

        $dataRedis = $redis->pipeline()->get($redisKey)->execute();
        foreach ($dataRedis as $aDataRedis) {
            return json_decode($aDataRedis, true);
        }
    } catch (Exception $exception) {
        return dbGetParam($param, $code);
    }


}

function dbSetParam($param, $code, $value = '', $type = '', $percentage = '')
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);

    $percentage = $percentage ? $percentage : 0;
    $type = $type ? $type : 0;
    $sql = '
		UPDATE
			pmzz_param
		SET
			pmzz_desc=?,
			pmzz_type=?,
			pmzz_percentage=?
		WHERE
			pmzz_par_type=?
			AND pmzz_code=?';

    return $dbh->executeScalar($sql, [$value, $type, $percentage, $param, $code]);
}

function dbGetDatabaseList($excludeTestDatabase = false)
{
    global $dbh;

    $exclude = $excludeTestDatabase ? 'where development = 0' : '';
    $sql = 'SELECT * FROM ' . SYSTEMDB . ".dbo.database_list {$exclude} ORDER BY development, description";

    return $dbh->executeMappedSet($sql, 'database_id');
}

function dbGetDatabases()
{
    global $dbh;

    $sql = 'SELECT * FROM ' . SYSTEMDB . '.dbo.database_list  ORDER BY development, description';

    return $dbh->executeSet($sql);
}


function dbGetDatabase($databaseID)
{
    global $dbh;

    $sql = 'SELECT * FROM ' . SYSTEMDB . '.dbo.database_list WHERE database_id=?';

    return $dbh->executeSingle($sql, [$databaseID]);
}

function dbGetDatabaseRedis($interval, $databaseID)
{
    try {
        global $dbh, $redis;
        $dbh->selectDatabase(SYSTEMDB);
        $redisKey = 'cirrus:databaseListByIDCache_' . $databaseID; // Redis Key must be unique

        if (! $redis->exists($redisKey)) {
            $sql = 'SELECT * FROM database_list WHERE database_id=?';
            $data = $dbh->executeSingle($sql, [$databaseID]);

            $redis->pipeline()->set($redisKey, json_encode($data, true))->execute();
            $redis->expireat($redisKey, strtotime('+' . $interval . ' minute'));
        }

        $dataRedis = $redis->pipeline()->get($redisKey)->execute();
        foreach ($dataRedis as $aDataRedis) {
            return json_decode($aDataRedis, true);
        }
    } catch (Exception $exception) {
        return dbGetDatabase($databaseID);
    }


}

/**
 * @modified 2012-06-29: Added $useEmail parameter as an option to login using user's e-mail address [Morph]
 * @modified 2012-09-05: Added new columns (for terms of agreement) [Morph]
 **/
function dbGetUserByEmail($username, $password = null, $useEmail = true)
{
    global $dbh;
    $dbh->selectDatabase(SYSTEMDB);
    $params = [];

    $whereClause[] = ($useEmail) ? 'email=' . addSQLParam(
        $params,
        $username
    ) : 'user_name=' . addSQLParam($params, $username);

    $whereClause = implode(' AND ', $whereClause);

    $sql = "SELECT
			user_name,
			password,
			DATEDIFF(D, GETDATE(), liscence_end) AS licenseEndDays,
			CONVERT(CHAR(10), liscence_end, 103) AS licenseEndDate,
			user_id AS userID,
			terms_agreed as termsAgreed,
			terms_id as termsVersion,
			first_name,
			last_name,
			password_hash,
			attempt_count
		FROM
			user_list
		WHERE
			{$whereClause}";

    return $dbh->executeSingle($sql, $params);
}

/**
 * @modified 2012-06-29: Added $useEmail parameter as an option to login using user's e-mail address [Morph]
 * @modified 2012-09-05: Added new columns (for terms of agreement) [Morph]
 **/
function dbCheckLogin($username, $password, $useEmail = true, $authByEmail = false)
{
    global $dbh;
    $dbh->selectDatabase(SYSTEMDB);

    $whereClause[] = 'email=?';

    $whereClause = implode(' AND ', $whereClause);

    $sql = "SELECT
			user_name,
			email,
			password,
			DATEDIFF(D, GETDATE(), liscence_end) AS licenseEndDays,
			CONVERT(CHAR(10), liscence_end, 103) AS licenseEndDate,
			user_id AS userID,
			terms_agreed as termsAgreed,
			terms_id as termsVersion,
			first_name,
			last_name,
			password_hash,
			attempt_count
		FROM
			user_list
		WHERE
			{$whereClause}";
    $data = $dbh->executeSingle($sql, [$username]);

    if (! $authByEmail && ! hash_match($password, $data['password_hash'])) {
        return false;
    }

    return $data;
}


function hash_match($input, $password)
{
    return password_verify($input, $password);
}

function dbGetLoginForUser($userID)
{
    global $dbh;
    $dbh->selectDatabase(SYSTEMDB);
    $sql = 'SELECT email AS username,
		password AS password
		FROM user_list WHERE
		user_id = ?';

    return $dbh->executeSingle($sql, [$userID]);
}

function dbLogout($username, $loginTime)
{
    global $dbh;
    $dbh->selectDatabase(SYSTEMDB);
    $sql = "UPDATE user_log SET logout = 'yes' WHERE (user_name = ? AND login_time = ?)";

    return $dbh->executeNonQuery2($sql, [$username, $loginTime]);
}

function dbLogUser($userID)
{
    global $dbh;
    $dbh->selectDatabase(SYSTEMDB);
    $sql = 'UPDATE userHistory SET loggedIn=0 WHERE userID=?';

    return $dbh->executeNonQuery2($sql, [$userID]);
}

function dbSsoLogout($userID)
{
    global $dbh;
    $dbh->selectDatabase(SYSTEMDB);
    $user = $dbh->executeSingle('SELECT email from user_list where user_id = ?', [$userID]);
    if (isset($user['email'])) {
        $email = $user['email'];
        $sql = 'UPDATE sso_logs SET logout_at=GETDATE() WHERE logout_at is NULL AND email=?';

        return $dbh->executeNonQuery2($sql, [$email]);
    } else {
        return false;
    }
}

function dbGetHourDiffSSOLog(string $user_email, string $session_id): ?int
{
    global $dbh;
    $dbh->selectDatabase(SYSTEMDB);
    $user = $dbh->executeSingle(
        'SELECT
       CAST(CASE WHEN last_active_at IS NULL THEN GETDATE() ELSE last_active_at END AS DATETIME) AS last_active_at,
       GETDATE() AS date_today,
       DATEDIFF(HOUR, CASE WHEN last_active_at IS NULL THEN GETDATE() ELSE last_active_at END, GETDATE()) AS hour_diff
       FROM sso_logs WHERE session_id = ? AND email = ? AND logout_at IS NULL',
        [$session_id, $user_email]
    );

    return $user['hour_diff'];
}

function dbUpdateSSOLastActive($user_email, $session_id)
{
    global $dbh;
    $dbh->selectDatabase(SYSTEMDB);

    return $dbh->executeNonQuery2(
        'UPDATE sso_logs set last_active_at=GETDATE() WHERE session_id = ? AND email = ? AND logout_at IS NULL',
        [$session_id, $user_email]
    );
}

function dbGetLoginStats($userID)
{
    global $dbh;
    $dbh->selectDatabase(SYSTEMDB);
    $sql = 'SELECT userHistoryID, userID, userAgent, remoteIP, remoteAddress, loginTime, sessionID, loggedIn, authKey FROM userHistory WHERE userID=? AND loggedIn=1';

    return $dbh->executeSingle($sql, [$userID]);
}

/**
 * @param  int  $userID  Mandatory.
 * @param  int  $userGroupID  Mandatory.
 * @param  string  $location  Optional.
 *
 * @modified 2012-09-20: Selection for $location column and its equivalent parameter. Cleansed the variables. [Morph]
 **/
function pingUser(int $userID, int $userGroupID, string $location = '')
{
    global $dbh;
    $dbh->selectDatabase(SYSTEMDB);
    $databaseID = $_SESSION['databaseID'];
    $sql = 'UPDATE userHistory
		SET
			pid = ' . (int) getmypid() . ',
			lastActive = GETDATE(),
			userType=?,
			location=?,
			databaseID=?
		WHERE
			userID=?
			AND loggedIn=1';

    return $dbh->executeNonQuery2($sql, [$userGroupID, $location, $databaseID, $userID]);
}

/**
 * @modified 2012-08-15: Added selection of user_name column. [Morph]
 **/
function dbGetUsersOnline($userID, $userGroup, $databaseID, $tolerance = 15)
{
    global $dbh;
    $dbh->selectDatabase(SYSTEMDB);
    $params = [];
    $userGroupSQL = ($userGroup) ? ' AND g.groupCode = ?' : '';
    $sql = "SELECT  DISTINCT h.userID, l.first_name, l.last_name, l.user_name
	FROM user_list l, userHistory h,  userPermissions p, subGroups s, userGroups g, user_dbaccess d
				  WHERE h.loggedIn=1
				  AND h.userID = p.userID
				  AND p.subGroupID=s.subGroupID
				  AND s.groupID=g.groupID
				  {$userGroupSQL}
				  AND g.groupID = h.userType
				  AND d.user_id = h.userID
				  AND d.db_id = ?
				  AND l.user_id = h.userID
				  AND DATEDIFF(minute, h.lastActive, GETDATE()) < ?";

    if ($userGroup) {
        $params[] = $userGroup;
    }

    $params[] = $databaseID;
    $params[] = $tolerance;

    return $dbh->executeSet($sql, false, true, $params);
}

/**
 * Check the list of users who are currently online (for the last 20 minutes).
 *
 * <AUTHOR> Reyes
 *
 * @since 2012-07-23
 *
 * @modified 2012-08-17: Transferred to lib/dbInterface for use in sessions. [Morph]
 * @modified 2013-07-31: Remove group name usage (including its SQL connection.) [Morph]
 * @modified 2013-08-05: Modified to limit viewable user list. [Morph]
 **/
function dbOnlineUsers()
{
    global $dbh;
    $dbh->selectDatabase(SYSTEMDB);
    $userID = intval($_SESSION['user_id']);
    $whereClause = " AND u.user_type='A'";
    $sql = "
		SELECT
			u.user_id AS userID,
			u.user_name as userName,
			u.first_name AS firstName,
			u.last_name AS lastName,
			h.remoteAddress,
			DATEDIFF(minute, h.lastActive, GETDATE()) AS lastActiveTime,
			h.pid,
			enable_chat AS enableChat,
			h.location as userLocation
		FROM
			user_list u
		LEFT JOIN
			userHistory h ON (u.user_id=h.userID)
		INNER JOIN
			user_dbaccess d ON (d.user_id=u.user_id)
		WHERE
			u.protected=0
			AND u.enable_chat=1
			AND DATEDIFF(minute, h.lastActive, GETDATE()) <= 20
			{$whereClause}
			AND u.user_id NOT IN (1,?)
			AND loggedIn = 1
		ORDER BY
			h.lastActive DESC";

    return $dbh->executeSet($sql, false, true, [$userID]);
}

function dbCheckLogins($userID)
{
    global $dbh;
    $dbh->selectDatabase(SYSTEMDB);
    $sql = 'SELECT COUNT(loggedIn) FROM userHistory WHERE userID=? AND loggedIn=1';

    return $dbh->executeScalar($sql, [$userID]);
}

function dbClearCurrentLogin($userID)
{
    global $dbh;
    $dbh->selectDatabase(SYSTEMDB);
    $sql = 'UPDATE userHistory SET loggedIn = 0 WHERE userID = ?';

    return $dbh->executeNonQuery2($sql, [$userID]);
}

function dbInsertLoginStats($userID, $userAgent, $remoteIP, $remoteAddress, $sessionID, $authKey, $timestamp)
{
    global $dbh;
    $dbh->selectDatabase(SYSTEMDB);
    $params = [];
    $sql = 'INSERT INTO userHistory
			  (userID,
			  userAgent,
			  remoteIP,
			  remoteAddress,
			  loginTime,
			  sessionID,
			  authKey,
			  timestamp
			 )
			  VALUES
			  (
				' . addSQLParam($params, $userID) . ',
				' . addSQLParam($params, $userAgent) . ',
				' . addSQLParam($params, $remoteIP) . ',
				' . addSQLParam($params, $remoteAddress) . ',
				GETDATE(),
				' . addSQLParam($params, $sessionID) . ',
				' . addSQLParam($params, $authKey) . ',
				' . addSQLParam($params, $timestamp) . '
			 )
			  ';

    return $dbh->executeNonQuery2($sql, $params);
}


function dbCheckEmail($recipient, $command = null, $module = null, $attachment = null)
{
    global $dbh, $context, $dispatch, $clientID;
    $dbh->selectDatabase(SYSTEMDB);
    $params = [];

    $params[] = $recipient;

    if ($command) {
        $commandSQL = ' AND command = ?';
        $params[] = $command;
    }

    if ($module) {
        $moduleSQL = ' AND module = ?';
        $params[] = $module;
    }

    if ($attachment) {
        $attachmentSQL = ' AND attachment = ?';
        $params[] = $attachment;
    }

    $sql = "SELECT
			  recipient,
			  client,
			  subject,
			  date,
			  errorCode,
			  command,
			  module,
			  attachment
			  FROM  email_log
			  WHERE
			  recipient = ?
			  {$commandSQL}
			  {$moduleSQL}
			  {$attachmentSQL}
			  ";

    return $dbh->executeSet($sql, false, true, $params);
}


function dbCheckLastEmail($recipient, $command = null, $module = null, $attachment = null)
{
    global $dbh, $context, $dispatch, $clientID;
    $dbh->selectDatabase(SYSTEMDB);
    $params = [];

    $params[] = $recipient;
    $params[] = dbGetClientID();

    if ($command) {
        $commandSQL = ' AND command = ?';
        $params[] = $command;
    }

    if ($module) {
        $moduleSQL = ' AND module = ?';
        $params[] = $module;
    }

    if ($attachment) {
        $attachmentSQL = ' AND attachment = ?';
        $params[] = $attachment;
    }

    $sql = "SELECT
			recipient,
			client,
			subject,
			date,
			errorCode,
			command,
			module,
			attachment
		FROM email_log
		WHERE
			recipient = ?
		    AND client = ?
			{$commandSQL}
			{$moduleSQL}
			{$attachmentSQL}
		ORDER BY date DESC";

    return $dbh->executeSingle($sql, $params);
}

function dbCheckLastEmailByEmailLogAttachment($recipient, $command = null, $module = null, $attachment = null)
{
    global $dbh, $clientDB, $clientDirectory, $pathPrefix;
    $dbh->selectDatabase(SYSTEMDB);
    $params = [];

    $params[] = $recipient;

    if ($command) {
        $commandSQL = ' AND command = ?';
        $params[] = $command;
    }

    if ($module) {
        $moduleSQL = ' AND module = ?';
        $params[] = $module;
    }

    if ($attachment) {
        $attachmentSQL = ' AND emailAttachment LIKE ?';
        $params[] = $attachment;
    }

    $sql = "SELECT
			recipient,
			client,
			subject,
			date,
			errorCode,
			command,
			module,
			emailAttachment as attachment
		FROM email_log
		LEFT JOIN email_log_attachment ON email_log.emailID = email_log_attachment.emailID
		WHERE
			recipient = '{$recipient}'
			AND client = " . dbGetClientID() . "
			{$commandSQL}
			{$moduleSQL}
			{$attachmentSQL}
		ORDER BY date DESC";

    return $dbh->executeSingle($sql, $params);
}

/**
 * Logs outgoing mail.
 *
 * @param  string  $recipient  Mandatory. E-mail address of the recipient.
 * @param  string  $headers  Mandatory. Unused/Unknown.
 * @param  string  $content  Mandatory. Lease name or description.
 * @param  string  $errorCode  Mandatory. Unknown usage.
 * @param  string  $attachment  Optional. Filename of the attachment for the e-mail.
 *
 * @modified 2012-04-26: Fixed unescaped apostrophe problem for $content. Added decriptives for this function. // Morph
 **/
function dbLogEmail(
    $recipient,
    $headers,
    $content,
    $errorCode,
    $attachment = null,
    $identifier = null,
    $sender = null,
    $postmarkID = null,
    $cc_recipient = null
) {
    global $dbh, $context, $dispatch, $clientID;
    $dbh->selectDatabase(SYSTEMDB);
    $params = [];

    $sql = 'INSERT INTO email_log
			(recipient, client, subject, date, errorCode, command, module, identifier, cc_recipient, sender, postmark_message_id)
		VALUES
			(' . addSQLParam($params, $recipient) . ',
            ' . ($clientID != '' ? addSQLParam($params, $clientID) : addSQLParam($params, dbGetClientID())) . ',
            ' . addSQLParam($params, $content) . ',
            GETDATE(),
            ' . addSQLParam($params, $errorCode) . ',
            ' . addSQLParam($params, $dispatch['command']) . ',
            ' . addSQLParam($params, $context['module']) . ',
            ' . addSQLParam($params, $identifier) . ',
            ' . addSQLParam($params, $cc_recipient) . ',
            ' . addSQLParam($params, $sender) . ',
            ' . addSQLParam($params, $postmarkID) . ')
        ';
    $dbh->executeNonQuery2($sql, $params);

    return $dbh->lastID();
}

function dbLogEmailDevelopment(
    $recipient,
    $headers,
    $content,
    $errorCode,
    $attachment = null,
    $identifier = null,
    $overwritten_recipient = null,
    $cc_recipient = null,
    $test_cc_recipient = null,
    $sender = null,
    $postmarkID = null
) {
    global $dbh, $context, $dispatch, $clientID;
    $dbh->selectDatabase(SYSTEMDB);
    $params = [];

    $sql = 'INSERT INTO email_log
			(recipient, client, subject, date, errorCode, command, module, identifier, overwritten_recipient, cc_recipient, test_cc_recipient, sender, postmark_message_id)
		VALUES
			(' . addSQLParam($params, $recipient) . ',
            ' . ($clientID != '' ? addSQLParam($params, $clientID) : addSQLParam($params, dbGetClientID())) . ',
            ' . addSQLParam($params, $content) . ',
            GETDATE(),
            ' . addSQLParam($params, $errorCode) . ',
            ' . addSQLParam($params, $dispatch['command']) . ',
            ' . addSQLParam($params, $context['module']) . ',
            ' . addSQLParam($params, $identifier) . ',
            ' . addSQLParam($params, $overwritten_recipient) . ',
            ' . addSQLParam($params, $cc_recipient) . ',
            ' . addSQLParam($params, $test_cc_recipient) . ',
            ' . addSQLParam($params, $sender) . ',
            ' . addSQLParam($params, $postmarkID) . ')
		';
    $dbh->executeNonQuery2($sql, $params);

    return $dbh->lastID();
}

function dbInsertEmailAttachment($emailID, $emailAttachment, $fileName = null, $contentType = null)
{
    global $dbh;
    $dbh->selectDatabase(SYSTEMDB);
    $params = [];
    if (! $fileName) {
        $fileName = basename($emailAttachment);
    }

    $sql = 'INSERT INTO email_log_attachment
            (
                emailID,
                emailAttachment,
                file_name,
                content_type
            )
            VALUES
            (
                ' . addSQLParam($params, $emailID) . ',
                ' . addSQLParam($params, $emailAttachment) . ',
                ' . addSQLParam($params, $fileName) . ',
                ' . addSQLParam($params, $contentType) . '
            )';

    return $dbh->executeNonQuery2($sql, $params);
}

function dbInsertLetterHistoryEmailLog($email_id, $letter_history_id)
{
    global $dbh, $clientDB;
    $dbh->selectDatabase($clientDB);
    $params = [];

    $sql = 'INSERT INTO letter_history_email_log
            (
                letter_history_id,
                email_log_id
            )
            VALUES
            (
                ' . addSQLParam($params, $letter_history_id) . ',
                ' . addSQLParam($params, $email_id) . '
            )';

    return $dbh->executeNonQuery2($sql, $params);
}

function dbGetLetterHistoryEmailLogLetterId($email_id)
{
    global $dbh, $clientDB;
    $dbh->selectDatabase($clientDB);
    $sql = 'SELECT TOP 1 letter_history_id
		FROM letter_history_email_log
		WHERE email_log_id=?';

    return $dbh->executeScalar($sql, [$email_id]);
}

function dbGetLetterHistoryPostmarkId($letter_history_id)
{
    global $dbh, $clientDB;
    $dbh->selectDatabase($clientDB);
    $sql = 'SELECT TOP 1 postmark_message_id
		FROM letter_history_email_log
		LEFT JOIN [npms].dbo.email_log ON emailID = email_log_id
		WHERE letter_history_id=?';

    return $dbh->executeScalar($sql, [$letter_history_id]);
}

function dbLogEmail2($recipient, $headers, $content, $errorCode, $attachment = null, $identifier = null)
{
    global $dbh, $context, $dispatch, $clientID, $clientDB;
    $dbh->selectDatabase($clientDB);
    $params = [];

    $sql = 'INSERT INTO email_log
			(recipient, client, subject, date, errorCode, command, module, attachment, identifier)
		VALUES
			(' . addSQLParam($params, $recipient) . ',
            ' . addSQLParam($params, $clientID) . ',
            ' . addSQLParam($params, $content) . ',
            GETDATE(),
            ' . addSQLParam($params, $errorCode) . ',
            ' . addSQLParam($params, $dispatch['command']) . ',
            ' . addSQLParam($params, $context['module']) . ',
            ' . addSQLParam($params, $attachment) . ',
            ' . addSQLParam($params, $identifier) . ')
		';

    return $dbh->executeNonQuery2($sql, $params);
}


function dbEmailLogByIdentifier($identifier)
{
    global $dbh;
    $dbh->selectDatabase(SYSTEMDB);

    $sql = 'SELECT
			recipient,
			client,
			subject,
			date,
			errorCode,
			command,
			module,
			attachment,
			identifier
		FROM email_log WHERE identifier = ?
		AND client = ' . dbGetClientID() . '
		ORDER BY date DESC
		';

    return $dbh->executeSet($sql, false, true, [$identifier]);
}


function dbLoggedIn($username)
{
    global $dbh;
    $dbh->selectDatabase(SYSTEMDB);
    $sql = "SELECT count(logout) FROM user_log WHERE (user_name = ?) AND logout='no'";

    return $dbh->executeScalar($sql, [$username]);
}

/**
 * @modified 2012-06-29: Added $useEmail parameter as an option to login using user's e-mail address [Morph]
 **/
function dbGetUserDetails($username, $useEmail = true)
{
    global $dbh;
    $dbh->selectDatabase(SYSTEMDB);

    $params = [];
    $whereClause = ($useEmail) ? 'u.email = ' . addSQLParam(
        $params,
        $username
    ) : 'u.user_name = ' . addSQLParam($params, $username);
    $sql = "SELECT
			u.first_name,
			u.last_name,
			u.user_name,
			u.password,
			u.user_type,
			u.user_sub_type,
			u.old_sys_user_id,
			u.trans_limit,
			u.liscence_end,
			u.email,
			u.user_id,
			u.super,
			u.default_db,
			u.must_login_with,
			g.groupID,
			g.groupName,
			g.groupCode,
			u.readOnlyAccess
		FROM user_list u, userGroups g
		WHERE ({$whereClause})
		AND g.groupCode COLLATE DATABASE_DEFAULT
		= u.user_type COLLATE DATABASE_DEFAULT";

    return $dbh->executeSingle($sql, $params);
}

function dbGetUserPortals($email)
{
    global $dbh;
    $dbh->selectDatabase(SYSTEMDB);
    $sql = 'SELECT subGroup_routes.*
		FROM userPermissions
		JOIN user_list on user_list.user_id = userPermissions.userID
		JOIN subGroup_routes on userPermissions.subGroupID = subGroup_routes.sub_group_id
		WHERE (user_list.email = ?)';
    $portals_raw = $dbh->executeSet($sql, false, true, [$email]);
    $portals = [];
    foreach ($portals_raw as $port) {
        if (! in_array($port['route_code'], $portals)) {
            $portals[] = $port['route_code'];
        }
    }

    return $portals;
}

function dbGetCirrus8RequiredRoleIDs()
{
    global $dbh;
    $dbh->selectDatabase(SYSTEMDB);
    $sql = "SELECT subGroup_routes.sub_group_id
			FROM subGroup_routes
			WHERE (route_code = 'cirrus8')";
    $exec = $dbh->executeSet($sql);
    $req_roles = [];
    foreach ($exec as $row) {
        $req_roles[] = $row['sub_group_id'];
    }

    return $req_roles;
}

function dbGetUserType($subType)
{
    global $dbh;
    $dbh->selectDatabase(SYSTEMDB);
    $sql = 'SELECT g.groupName,
							g.groupCode AS user_type,
							s.subGroupName AS user_level,
							s.subGroupCode AS user_sub_type,
							s.subGroupID AS sub_group_id
			  FROM userGroups g, subGroups s
			  WHERE s.subGroupCode=?
			  AND s.groupID=g.groupID';

    return $dbh->executeSingle($sql, [$subType]);
}

function dbGetUserTypeByID($subTypeID)
{
    global $dbh;
    $dbh->selectDatabase(SYSTEMDB);
    $sql = 'SELECT g.groupName,
							g.groupCode AS user_type,
							s.subGroupName AS user_level,
							s.subGroupCode AS user_sub_type
			  FROM userGroups g, subGroups s
			  WHERE s.subGroupID=?
			  AND s.groupID=g.groupID';

    return $dbh->executeSingle($sql, [$subTypeID]);
}


function dbGetUserContactList($userGroup = null)
{
    global $dbh, $clientDB;
    $dbh->selectDatabase(SYSTEMDB);

    $params = [];
    $groupSQL = ($userGroup) ? ' AND s.groupID= ' . addSQLParam($params, $userGroup) : '';

    $sql = "SELECT DISTINCT
				u.user_name AS userName,
				u.first_name + ' ' + u.last_name AS fullName,
				u.email AS email
				FROM user_list u
				JOIN userPermissions p
					ON u.user_id = p.userID
					AND u.is_staff = 0
					AND u.liscence_end > GETDATE()
				JOIN subGroups s
					ON p.subGroupID=s.subGroupID
					AND s.subGroupID <> 19
					{$groupSQL}
				JOIN user_dbaccess a
					ON u.user_id = a.user_id
					AND (a.access_expire_date is null OR a.access_expire_date > GETDATE())
				JOIN database_list d
					ON a.db_id = d.database_id
					AND d.database_name = '" . $clientDB . "'
				JOIN user_db_role_locks l
					ON l.database_id = d.database_id
					AND l.user_id = u.user_id
					AND l.sub_group_id = s.subGroupID";

    return $dbh->executeSet($sql, false, true, $params);
}

function dbGetUserPermissions($userID, $sso_enabled = false)
{
    global $dbh, $redis;

    $dbh->selectDatabase(SYSTEMDB);
    $sql = 'SELECT g.groupID,
							g.groupCode,
							g.groupName,
							s.subGroupID,
							s.subGroupName,
							s.subGroupCode
			  FROM userGroups g , subGroups s, userPermissions p
			  WHERE g.groupID = s.groupID
			  AND s.subGroupID=p.subGroupID
			  ' . ($sso_enabled ? " AND is_hidden='0' " : '') . '
 			  AND p.userID=?
			  ';

    return $dbh->executeSet($sql, false, true, [$userID]);
}

function dbGetUserLockRoles($userID, $databaseID = null, $subGroupID = null)
{
    global $dbh, $redis;
    $dbh->selectDatabase(SYSTEMDB);

    $use_roles_only = [];
    $params = [];
    $sql = 'SELECT locks.*, role.subGroupName, role.subGroupCode
		  FROM user_db_role_locks locks, subGroups role
		  WHERE locks.sub_group_id = role.subGroupID
		  AND user_id = ' . addSQLParam($params, $userID);

    if ($databaseID != null) {
        $sql .= ' AND database_id = ' . addSQLParam($params, $databaseID);
    }

    if ($subGroupID != null) {
        $sql .= ' AND sub_group_id = ' . addSQLParam($params, $subGroupID);
    }

    $set = $dbh->executeSet($sql, false, true, $params);
    foreach ($set as $rl) {
        $use_roles_only[] = (int) $rl['sub_group_id'];
    }

    return $use_roles_only;
}

function dbGetUserLockRolesDetailed($userID, $databaseID = null, $subGroupID = null)
{
    global $dbh, $redis;
    $dbh->selectDatabase(SYSTEMDB);
    $params = [];
    $sql = 'SELECT locks.*, role.subGroupName, role.subGroupCode
		  FROM user_db_role_locks locks, subGroups role
		  WHERE locks.sub_group_id = role.subGroupID
		  AND user_id = ' . addSQLParam($params, $userID);

    if ($databaseID != null) {
        $sql .= ' AND database_id = ' . addSQLParam($params, $databaseID);
    }

    if ($subGroupID != null) {
        $sql .= ' AND sub_group_id = ' . addSQLParam($params, $subGroupID);
    }

    return $dbh->executeSet($sql, false, true, $params);
}

function dbGetUserPermissionsRedis($interval, $userID)
{
    try {
        global $dbh, $redis;
        $dbh->selectDatabase(SYSTEMDB);
        $redisKey = 'cirrus:userGroupListCache_' . $userID; // Redis Key must be unique

        if (! $redis->exists($redisKey)) {
            $sql = 'SELECT g.groupID,
							g.groupCode,
							g.groupName,
							s.subGroupID,
							s.subGroupName,
							s.subGroupCode
			  FROM userGroups g , subGroups s, userPermissions p
			  WHERE g.groupID = s.groupID
			  AND s.subGroupID=p.subGroupID
			  AND p.userID=?
			  ';
            $data = $dbh->executeSet($sql, false, true, [$userID]);

            $redis->pipeline()->set($redisKey, json_encode($data, true))->execute();
            $redis->expireat($redisKey, strtotime('+' . $interval . ' minute'));
        }

        $dataRedis = $redis->pipeline()->get($redisKey)->execute();
        foreach ($dataRedis as $aDataRedis) {
            return json_decode($aDataRedis, true);
        }
    } catch (Exception $exception) {
        return dbGetMenu();
    }


}

function dbGetDatabasePermissions($userID)
{
    global $dbh;
    $dbh->selectDatabase(SYSTEMDB);
    $sql = "SELECT u.db_id AS databaseID,
				d.database_name AS databaseName,
				d.development AS development,
				d.description AS description,
				REPLACE(d.description,' ','') as description_no_space,
				u.portfolio AS portfolio,
				u.access_expire_date AS access_expire_date,
				d.timezone
				FROM database_list d, user_dbaccess u
				WHERE d.database_id=u.db_id
				AND u.user_id = ?
				ORDER BY d.development, d.description";

    return $dbh->executeSet($sql, false, true, [$userID]);
}

function dbGetDatabasePermissionsRedis($interval, $userID)
{
    try {
        global $dbh, $redis;
        $dbh->selectDatabase(SYSTEMDB);
        $redisKey = 'cirrus:databaseListCache_' . $userID; // Redis Key must be unique

        if (! $redis->exists($redisKey)) {
            $sql = 'SELECT u.db_id AS databaseID,
				d.database_name AS databaseName,
				d.development AS development,
				d.description AS description,
				u.portfolio AS portfolio
				FROM database_list d, user_dbaccess u
				WHERE d.database_id=u.db_id
				AND u.user_id = ?
				ORDER BY d.development, d.description';
            $data = $dbh->executeSet($sql, false, true, [$userID]);

            $redis->pipeline()->set($redisKey, json_encode($data, true))->execute();
            $redis->expireat($redisKey, strtotime('+' . $interval . ' minute'));
        }

        $dataRedis = $redis->pipeline()->get($redisKey)->execute();
        foreach ($dataRedis as $aDataRedis) {
            return json_decode($aDataRedis, true);
        }
    } catch (Exception $exception) {
        return dbGetDatabasePermissions($userID);
    }


}

function dbGetHolidaysBetweenDates($fromDate, $toDate)
{
    global $dbh, $clientDB;
    $dbh->selectDatabase($clientDB);

    $sql = "SELECT COUNT(pmzz_desc) FROM pmzz_param WHERE pmzz_par_type='PUBHOL' AND (pmzz_code BETWEEN CONVERT(datetime, ?, 103) AND CONVERT(datetime, ?, 103))";
    $dbh->executeScalar($sql, [$fromDate, $toDate]);
}

function dbGetHolidayDetailsBetweenDates($fromDate, $toDate)
{
    global $dbh, $clientDB;
    $dbh->selectDatabase($clientDB);
    $sql = "SELECT pmzz_code AS date, pmzz_desc AS name FROM pmzz_param WHERE pmzz_par_type='PUBHOL' AND (pmzz_code BETWEEN CONVERT(datetime, ?, 103) AND CONVERT(datetime, ?, 103))";

    return $dbh->executeMappedSet($sql, 'date', [$fromDate, $toDate]);
}

function dbFetchDebtorEmailByLease($propertyID, $leaseID)
{
    global $dbh, $clientDB;
    $dbh->selectDatabase($clientDB);
    $sql = ' SELECT pmco_company.pmco_email
		FROM pmco_company
		INNER JOIN pmle_lease ON pmco_company.pmco_code = pmle_lease.pmle_debtor
		WHERE (pmle_lease.pmle_lease = ?) AND (pmle_lease.pmle_prop = ?)';

    return $dbh->executeScalar($sql, [$leaseID, $propertyID]);
}

function dbGetCountries()
{
    global $dbh, $clientDB;
    $dbh->selectDatabase(SYSTEMDB);
    $sql = 'SELECT countryCode, countryName, countryID, IsNull(priority,0) AS isNull
		FROM countries
		ORDER BY countryName ASC,isNull DESC, priority ASC, countryCode';

    return $dbh->executeSet($sql);
}

function dbGetCountryName($countryCode)
{
    global $dbh, $clientDB;
    $dbh->selectDatabase(SYSTEMDB);
    $sql = 'SELECT countryName
		FROM countries
		WHERE countryCode=?';

    return $dbh->executeScalar($sql, [$countryCode]);
}

function dbGetStates($countryCode)
{
    global $dbh, $clientDB;
    $dbh->selectDatabase(SYSTEMDB);
    $sql = 'SELECT * FROM states WHERE countryCode=? ORDER BY stateName ASC';

    return $dbh->executeSet($sql, false, true, [$countryCode]);
}

function dbGetStateName($state, $country)
{
    global $dbh, $clientDB;
    $dbh->selectDatabase(SYSTEMDB);
    $sql = 'SELECT stateName
		FROM states
        WHERE countryCode = ?
        AND stateCode = ?';

    return $dbh->executeScalar($sql, [$country, $state]);
}

function dbGetCities($stateCode, $postCode = false, $suburbCode = false)
{
    global $dbh, $clientDB;
    $dbh->selectDatabase(SYSTEMDB);
    $params = [];

    if ($suburbCode) {
        $sql = "SELECT suburb, suburb + ', ' + state + ' ' + postcode as suburbList FROM suburb
			WHERE state=" . addSQLParam($params, $stateCode) . ' AND postcode = ' . addSQLParam(
            $params,
            $postCode
        ) . ' AND suburb = ' . addSQLParam($params, $suburbCode) . ' ORDER BY suburb ASC, postcode ASC';
    } else {
        if (empty($stateCode)) {
            $stateCode = 'WA';
        }

        $sql = "SELECT suburb, suburb + ', ' + state + ' ' + postcode as suburbList FROM suburb
				WHERE state=" . addSQLParam($params, $stateCode) . '
				ORDER BY suburb ASC, postcode ASC';
    }

    return $dbh->executeSet($sql, false, true, $params);
}

function dbGetLocale($location)
{
    global $dbh, $clientDB;
    $dbh->selectDatabase(SYSTEMDB);
    $sql = 'SELECT * FROM locale WHERE localeID=?';

    return $dbh->executeSingle($sql, [$location]);
}

function dbGetLocaleRedis($interval, $location)
{
    try {
        global $dbh, $redis;
        $dbh->selectDatabase(SYSTEMDB);
        $redisKey = 'cirrus:clientLocalizationByIDCache_' . $location; // Redis Key must be unique

        if (! $redis->exists($redisKey)) {
            $sql = 'SELECT * FROM locale WHERE localeID=?';
            $data = $dbh->executeSingle($sql, [$location]);

            $redis->pipeline()->set($redisKey, json_encode($data, true))->execute();
            $redis->expireat($redisKey, strtotime('+' . $interval . ' minute'));
        }

        $dataRedis = $redis->pipeline()->get($redisKey)->execute();
        foreach ($dataRedis as $aDataRedis) {
            return json_decode($aDataRedis, true);
        }
    } catch (Exception $exception) {
        return dbGetLocale($location);
    }


}

function dbUpdateStatistic($stat, $value)
{
    global $dbh, $clientDB;
    $prevDB = $dbh->_settings['Database'];
    $dbh->selectDatabase(SYSTEMDB);
    $sql = 'UPDATE [statistics] SET value=? WHERE statistic=?';
    $return = $dbh->executeNonQuery2($sql, [$value, $stat]);
    if ($prevDB) {
        $dbh->selectDatabase($prevDB);
    }

    return $return;
}

function dbGetStatistic($stat)
{
    global $dbh, $clientDB;
    $prevDB = $dbh->_settings['Database'];
    $dbh->selectDatabase(SYSTEMDB);
    $sql = 'SELECT value FROM [statistics] WHERE statistic=?';
    $return = $dbh->executeScalar($sql, [$stat]);
    if ($prevDB) {
        $dbh->selectDatabase($prevDB);
    }

    return $return;
}

function dbGetLastAnnouncement()
{
    global $dbh, $clientDB;
    $prevDB = $dbh->_settings['Database'];
    $dbh->selectDatabase(SYSTEMDB);
    $sql = 'SELECT  CONVERT(char(10), announcementDate, 103) AS announcementDate, announcement FROM announcements WHERE DateDiff(d, announcementDate, GETDATE()) < 7 ORDER BY announcementDate DESC ';
    $return = $dbh->executeSingle($sql);
    if ($prevDB) {
        $dbh->selectDatabase($prevDB);
    }

    return $return;
}

function dbGetUserSuperAccess($user_id)
{
    global $dbh, $clientDB;
    $dbh->selectDatabase(SYSTEMDB);
    $sql = 'SELECT * FROM user_super_access WHERE user_id = ?';

    return $dbh->executeSet($sql, false, true, [$user_id]);
}

function dbCheckUserSuperAccess($user_id)
{
    $has_access = false;
    $access = dbGetUserSuperAccess($user_id);
    foreach ($access as $sa) {
        if (in_array($sa['access_code'], SUPER_USER_CODES)) {
            $has_access = true;
            break;
        }
    }

    return $has_access;
}


function dbCheckSuperAdmin($user_id, $super_per_access = false)
{
    if (! isset($_SESSION['super_enabled']) || ! $_SESSION['super_enabled']) {
        return false;
    }

    global $dbh, $clientDB;
    $dbh->selectDatabase(SYSTEMDB);

    $sql = ' SELECT  super FROM user_list WHERE user_id = ?';

    $super = (int) $dbh->executeScalar($sql, [$user_id]);
    if ($super_per_access) {
        return dbCheckUserSuperAccess($user_id);
    }

    return $super;
}

function dbCheckLoginSecure($sso_key)
{
    global $dbh, $clientDB;
    $dbh->selectDatabase(SYSTEMDB);
    $sql = ' SELECT  is_secure FROM sso_logs WHERE session_id = ?';

    return (bool) $dbh->executeScalar($sql, [$sso_key]);
}

/**
 * @param  $userID  int Mandatory. User ID not the Username.
 *
 * <AUTHOR> Reyes
 *
 * @since 2012-06-28
 *
 * @note 2012-09-05 Not sure if this function is still being use. In login.php, I no longer use this function. It might still be use elsewhere. [Morph]
 **/
function dbCountLogins($userID)
{
    global $dbh;
    $dbh->selectDatabase(SYSTEMDB);
    $sql = 'SELECT COUNT(loggedIn) FROM userHistory WHERE userID=?';

    return $dbh->executeScalar($sql, [$userID]);
}

/**
 *Check users whose license will expire soon (base on $days).
 *
 * @param  $days  string Mandatory. The days use to compare the expired license from.
 *
 * <AUTHOR> Reyes
 *
 * @since 2012-07-06
 **/
function dbGetExpiringUsers($days)
{
    global $dbh;
    $dbh->selectDatabase(SYSTEMDB);

    $sql = "SELECT
			CONVERT(varchar(100), LTRIM(RTRIM(first_name)) + ' ' + LTRIM(RTRIM(last_name))) AS fullName,
			user_name as username,
			email AS email,
			liscence_end,
			DATEDIFF(DAY, GETDATE(), liscence_end) as daysToExpire
		FROM user_list
		WHERE liscence_end>=GETDATE() AND liscence_end<=GETDATE()+?
		ORDER BY liscence_end ASC";

    return $dbh->executeSet($sql, false, true, [$days]);
}

/**
 * Get the latest Terms of Use form the database.
 *
 * @return array
 *
 **@since 2012-09-04
 *
 * <AUTHOR> Reyes
 */
function dbGetTermsOfUse()
{
    global $dbh;
    $dbh->selectDatabase(SYSTEMDB);
    $sql = 'SELECT TOP 1
		terms_id as termsVersion,
		content_text,
		content_html,
		date_modified as dateModified
		FROM terms
		ORDER BY date_modified DESC, terms_id DESC';

    return $dbh->executeSingle($sql);
}

/**
 * User has agreed to the terms and conditions.
 *
 * @param  $userID  int Mandatory. The ID of the user that agreed to the terms.
 *
 * <AUTHOR> Reyes
 *
 * @since 2012-09-05
 **/
function dbAgreeToTerms($userID)
{
    global $dbh;
    $dbh->selectDatabase(SYSTEMDB);
    $terms = dbGetTermsofUse();
    $termsVersion = $terms['termsVersion'];
    if ($userID) {
        $sql = 'UPDATE user_list SET terms_agreed=1, terms_id=?, terms_date_agreed=GETDATE() WHERE user_id=?';

        return $dbh->executeNonQuery2($sql, [$termsVersion, $userID]);
    }


}


function dbAddDelayedTask($task)
{
    global $dbh, $clientDB;
    $dbh->selectDatabase(SYSTEMDB);

    extract($task);

    $params = [];
    $sql = 'INSERT INTO delayedTasks
			  (
				databaseID,
				day,
				command,
				data,
				frequency,
				createdDate,
				createdBy,
				active,
				recurring
				)
				VALUES
				(
				' . addSQLParam($params, $databaseID) . ',
				' . addSQLParam($params, $day) . ',
				' . addSQLParam($params, $command) . ',
				' . addSQLParam($params, $data) . ',
				' . addSQLParam($params, $frequency) . ',
				CONVERT(datetime, ' . addSQLParam($params, $createdDate) . ', 103),
				' . addSQLParam($params, $createdBy) . ',
				' . addSQLParam($params, $active) . ',
				' . addSQLParam($params, $recurring) . '
				)
				';

    return $dbh->executeNonQuery2($sql, $params);
}


function dbGetDelayedTasks($databaseID = null)
{
    global $dbh;
    $dbh->selectDatabase(SYSTEMDB);

    $params = [];
    if ($databaseID) {
        $databaseSQL = 'WHERE databaseID=' . addSQLParam($params, $databaseID);
    }

    $sql = "SELECT
				taskID,
				databaseID,
				day,
				command,
				data,
				frequency,
				createdDate,
				createdBy,
				active,
				recurring
				FROM delayedTasks
				{$databaseSQL}
				ORDER BY recurring DESC, databaseID ASC
				";

    return $dbh->executeSet($sql, false, true, $params);
}


function dbGetDelayedTask($taskID)
{
    global $dbh;
    $dbh->selectDatabase(SYSTEMDB);
    $sql = 'SELECT
				taskID,
				databaseID,
				day,
				command,
				data,
				frequency,
				createdDate,
				createdBy,
				active,
				recurring
				FROM delayedTasks
				WHERE taskID = ?
				ORDER BY recurring DESC, databaseID ASC
				';

    return $dbh->executeSingle($sql, [$taskID]);
}


function dbDeleteDelayedTask($taskID)
{
    global $dbh;
    $dbh->selectDatabase(SYSTEMDB);
    $sql = 'DELETE FROM delayedTasks WHERE taskID=?';

    return $dbh->executeNonQuery2($sql, [$taskID]);
}

function dbToggleDelayedTask($taskID)
{
    global $dbh;
    $dbh->selectDatabase(SYSTEMDB);
    $sql = 'UPDATE delayedTasks SET active=(1-active) WHERE taskID=?';

    return $dbh->executeNonQuery2($sql, [$taskID]);
}

/*
 * Added by : Lyn Salazar
 * Added on : 02-17-2016
*/
function dbGetSystemUpdates()
{
    global $dbh;
    $dbh->selectDatabase(SYSTEMDB);
    $sql = 'SELECT update_desc , CONVERT(VARCHAR(19),update_date,103) AS update_date , update_user_type , update_area , update_effect_desc
		  FROM system_updates
		  ORDER BY CONVERT(VARCHAR(19),update_date,102) DESC';

    return $dbh->executeSet($sql);
}

function dbGetAttemptCount($userName)
{
    global $dbh;
    $dbh->selectDatabase(SYSTEMDB);

    $sql = '
		SELECT
			attempt_count
		FROM
			user_list
		WHERE
			user_name = ?';

    return $dbh->executeScalar($sql, [$userName]);
}


function dbFailedAttemptCount($userName)
{
    global $dbh;
    $dbh->selectDatabase(SYSTEMDB);

    $sql = '
		UPDATE
			user_list
		SET
			attempt_count = attempt_count + 1
		WHERE
			user_name = ?';

    return $dbh->executeNonQuery2($sql, [$userName]);
}


function dbResetUserAttempt($userID)
{
    global $dbh;
    $dbh->selectDatabase(SYSTEMDB);
    $userID = intval($userID);
    $sql = '
		UPDATE
			user_list
		SET
			attempt_count = 0
		WHERE
			user_id = ?';

    return $dbh->executeNonQuery2($sql, [$userID]);
}

function dbTogglePageReadonly($pageID)
{
    global $dbh;
    $dbh->selectDatabase(SYSTEMDB);
    $sql = 'UPDATE pages SET readOnlyPage = 1 - readOnlyPage WHERE pageID = ?';

    return $dbh->executeNonQuery2($sql, [$pageID]);
}

function dbGetCurrentDatabaseName($databaseName)
{
    global $dbh;
    $dbh->selectDatabase(SYSTEMDB);
    $sql = 'SELECT database_id FROM database_list WHERE database_name=?';

    return $dbh->executeScalar($sql, [$databaseName]);
}


function dbAllOptions()
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $sql = "
		SELECT RTRIM(pmle_lease.pmle_prop)    AS propertyID,
		       RTRIM(pmpr_property.pmpr_name) AS propertyName,
		       RTRIM(pmle_lease.pmle_lease)   AS leaseID,
		       RTRIM(pmle_lease.pmle_name)    AS leaseName,
		       RTRIM(pmle_lease.pmle_debtor)  AS debtorID,
		       RTRIM(pmco_company.pmco_name)  AS debtorName,
		       RTRIM(pmqd_chq_def.pmqd_name)  AS drawerName,
		       RTRIM(pmle_lease.pmle_crn)     AS crn,
		       RTRIM(pmle_status)             AS status,
			   (CASE WHEN (pmle_status = 'C')
				     THEN 'Current'
					 ELSE 'Vacated'
					 END) as category
		FROM   [pmle_lease]
		       INNER JOIN [pmpr_property]
		               ON [pmle_lease].[pmle_prop] = [pmpr_property].[pmpr_prop]
		                  AND [pmpr_property].[pmpr_delete] = 0
		       INNER JOIN [pmco_company]
		               ON [pmle_lease].[pmle_debtor] = [pmco_company].[pmco_code]
		       LEFT JOIN [pmqd_chq_def]
		              ON [pmle_lease].[pmle_debtor] = [pmqd_chq_def].[pmqd_debtor]
		ORDER BY pmle_status ASC,
				 pmpr_prop ASC,
				 pmle_lease ASC
	";

    return $dbh->executeSet($sql);
}


function dbGetOwnerExpenses($propertyAssign, $dtFrom, $dtTo)
{
    global $clientDB, $dbh;

    if (is_array($propertyAssign) && count($propertyAssign ?? []) > 0) {
        $propertyAssign = implode("','", $propertyAssign);
    }

    $params = [];

    $dbh->selectDatabase($clientDB);
    $sql = "SELECT
                COALESCE(SUM(gl_trial_balance.balanceCash), 0) AS expenses
                ,DATENAME(MONTH, pmcp_prop_cal.pmcp_start_dt) + ' ' + cast(gl_trial_balance.year as varchar) AS monthYear
            FROM  gl_trial_balance
            LEFT JOIN pmca_chart
                ON gl_trial_balance.accountID = pmca_chart.pmca_code
            LEFT JOIN pmcp_prop_cal
                ON gl_trial_balance.propertyID = pmcp_prop_cal.pmcp_prop
                    AND gl_trial_balance.year = pmcp_prop_cal.pmcp_year
                    AND gl_trial_balance.period = pmcp_prop_cal.pmcp_period
            WHERE pmca_chart.pmca_type IN ('E')
                AND gl_trial_balance.propertyID IN ('" . $propertyAssign . "')
                AND pmcp_prop_cal.pmcp_start_dt BETWEEN " . addSQLParam($params, $dtFrom) . ' AND ' . addSQLParam(
        $params,
        $dtTo
    ) . '
            GROUP BY gl_trial_balance.year, pmcp_prop_cal.pmcp_start_dt, DATENAME(MONTH, pmcp_prop_cal.pmcp_start_dt)
            ORDER BY gl_trial_balance.year, pmcp_prop_cal.pmcp_start_dt
    ';

    return $dbh->executeSet($sql, false, true, $params);
}

function dbGetOwnerIncome($propertyAssign, $dtFrom, $dtTo)
{
    global $clientDB, $dbh;

    if (is_array($propertyAssign) && count($propertyAssign ?? []) > 0) {
        $propertyAssign = implode("','", $propertyAssign);
    }

    $params = [];

    $dbh->selectDatabase($clientDB);
    $sql = "SELECT
                 COALESCE(SUM(gl_trial_balance.balanceCash * -1), 0)  AS income
                ,DATENAME(MONTH, pmcp_prop_cal.pmcp_start_dt) + ' ' + cast(gl_trial_balance.year as varchar) AS monthYear
            FROM  gl_trial_balance
            LEFT JOIN pmca_chart
                ON gl_trial_balance.accountID = pmca_chart.pmca_code
            LEFT JOIN pmcp_prop_cal
                ON gl_trial_balance.propertyID = pmcp_prop_cal.pmcp_prop
                    AND gl_trial_balance.year = pmcp_prop_cal.pmcp_year
                    AND gl_trial_balance.period = pmcp_prop_cal.pmcp_period
            WHERE pmca_chart.pmca_type IN ('I')
                AND gl_trial_balance.propertyID IN ('" . $propertyAssign . "')
                AND pmcp_prop_cal.pmcp_start_dt BETWEEN " . addSQLParam($params, $dtFrom) . ' AND ' . addSQLParam(
        $params,
        $dtTo
    ) . '
            GROUP BY gl_trial_balance.year, pmcp_prop_cal.pmcp_start_dt, DATENAME(MONTH, pmcp_prop_cal.pmcp_start_dt)
            ORDER BY gl_trial_balance.year, pmcp_prop_cal.pmcp_start_dt
    ';

    return $dbh->executeSet($sql, false, true, $params);
}

function dbGetOwnerRemittance($propertyAssign, $dtFrom, $dtTo)
{
    global $clientDB, $dbh;

    if (is_array($propertyAssign) && count($propertyAssign ?? []) > 0) {
        $propertyAssign = implode("','", $propertyAssign);
    }

    $params = [];

    $dbh->selectDatabase($clientDB);
    $sql = "SELECT
                COALESCE(SUM(gl_trial_balance.balanceCash), 0) AS remittance
                ,DATENAME(MONTH, pmcp_prop_cal.pmcp_start_dt) + ' ' + cast(gl_trial_balance.year as varchar) AS monthYear
            FROM  gl_trial_balance
            LEFT JOIN pmca_chart
                ON gl_trial_balance.accountID = pmca_chart.pmca_code
            LEFT JOIN pmcp_prop_cal
                ON gl_trial_balance.propertyID = pmcp_prop_cal.pmcp_prop
                    AND gl_trial_balance.year = pmcp_prop_cal.pmcp_year
                    AND gl_trial_balance.period = pmcp_prop_cal.pmcp_period
            LEFT JOIN pmoc_o_chart
	            ON pmca_chart.pmca_code = pmoc_o_chart.pmoc_acc
            WHERE pmca_chart.pmca_type IN ('E')
                AND gl_trial_balance.propertyID IN ('" . $propertyAssign . "')
                AND pmcp_prop_cal.pmcp_start_dt BETWEEN " . addSQLParam($params, $dtFrom) . ' AND ' . addSQLParam(
        $params,
        $dtTo
    ) . '
            GROUP BY gl_trial_balance.year, pmcp_prop_cal.pmcp_start_dt, DATENAME(MONTH, pmcp_prop_cal.pmcp_start_dt)
            ORDER BY gl_trial_balance.year, pmcp_prop_cal.pmcp_start_dt
    ';

    return $dbh->executeSet($sql, false, true, $params);
}

function dbGetOwnerData($propertyAssign, $dtFrom, $dtTo, $partitioned)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);
    $subSql = "select pmzz_desc from pmzz_param where pmzz_code = 'OWNREMIT' AND pmzz_par_type = 'ACCOUNTS'";
    $remittanceCode = $dbh->executeScalar($subSql);
    if ($remittanceCode == '') {
        $subSql = "select pmzz_desc from pmzz_param where pmzz_code = 'ACCTCODE' AND pmzz_par_type = 'OWNREACC'";
        $remittanceCode = $dbh->executeScalar($subSql);
    }

    if ($partitioned) {
        $expense = " AND pmca_gl_account_group2 = 'EXP.OWN'";
        $income = " AND pmca_gl_account_group2 = 'INC.OWN'";
    }

    if (is_array($propertyAssign) && count($propertyAssign ?? []) > 0) {
        $propertyAssign = implode("','", $propertyAssign);
    }

    $params = [];
    $sql = "SELECT
                 DATENAME(MONTH, pmcp_prop_cal.pmcp_start_dt) + ' ' + cast(YEAR(pmcp_prop_cal.pmcp_start_dt) as varchar) AS monthYear
                ,SUM( ( case when pmca_chart.pmca_type = 'E' {$expense} then gl_trial_balance.balanceCash else 0 end ) ) AS expenses
                ,SUM( ( case when pmca_chart.pmca_type = 'I' {$income} then (-1 * gl_trial_balance.balanceCash) else 0 end ) ) AS income
                ,SUM( ( case when gl_trial_balance.accountID = '{$remittanceCode}' then gl_trial_balance.balanceCash else 0 end ) ) AS remittance
            FROM pmcp_prop_cal pmcp_prop_cal
            LEFT JOIN gl_trial_balance gl_trial_balance
                ON pmcp_prop_cal.pmcp_prop = gl_trial_balance.propertyID
                AND pmcp_prop_cal.pmcp_year = gl_trial_balance.year
                AND pmcp_prop_cal.pmcp_period = gl_trial_balance.period
            LEFT JOIN pmca_chart pmca_chart
                ON gl_trial_balance.accountID = pmca_chart.pmca_code
            WHERE pmcp_prop_cal.pmcp_start_dt BETWEEN " . addSQLParam($params, $dtFrom) . ' AND ' . addSQLParam(
        $params,
        $dtTo
    ) . "
                    AND pmcp_prop_cal.pmcp_prop IN ('" . $propertyAssign . "')
            GROUP BY pmcp_prop_cal.pmcp_start_dt
            ORDER BY pmcp_prop_cal.pmcp_start_dt
    ";

    return $dbh->executeSet($sql, false, true, $params);
}

function dbGetNavMenuGroup($userGroup)
{
    global $dbh;
    $dbh->selectDatabase(SYSTEMDB);
    $sql = 'SELECT
            distinct
			p.menuGroupID, g.title, g.menuItem
			FROM
			modules m,
			pages p,
			menuGroups g,
			userGroups u
			WHERE
				g.menuGroupID = p.menuGroupID
				AND g.groupID = u.groupID
				AND u.groupCode = ?
				AND p.moduleID=m.moduleID
			';

    return $dbh->executeSet($sql, false, true, [$userGroup]);
}

function dbGetNavMenuForGroupRedis($interval, $menuGroupID)
{
    try {
        global $dbh, $redis;
        $dbh->selectDatabase(SYSTEMDB);
        $redisKey = 'cirrus:menuForGroupCache_' . $menuGroupID . '_' . $_SESSION['super'] . '_' . $_SESSION['readOnlyAccess'] . '_' . $_SESSION['user_sub_type']; // Redis Key must be unique

        $superSQL = ($_SESSION['super']) ? '' : 'AND p.administrator = 0';
        $readonlySQL = ($_SESSION['readOnlyAccess']) ? 'AND p.readOnlyPage = 0' : '';
        $APUserSQL = ($_SESSION['user_sub_type'] == 'AP_1') ? " AND pageID IN ('126','127','679')" : '';
        $PMPLUSUserSQL = ($_SESSION['user_sub_type'] == 'PM_PLUS') ? " AND pageID IN ('1','215')" : '';
        if (! $redis->exists($redisKey)) {
            $sql = "SELECT
			p.title,
			p.permissions,
			p.link,
			p.command,
			m.name,
			p.pageID
			FROM
			modules m,
			pages p
			WHERE
				p.display = 1
				{$superSQL}
				{$readonlySQL}
				AND p.moduleID = m.moduleID
				AND p.menuGroupID IN ?
				{$APUserSQL}
				{$PMPLUSUserSQL}
			ORDER BY p.sequence ASC";
            $data = $dbh->executeSet($sql, false, true, [$menuGroupID]);

            $redis->pipeline()->set($redisKey, json_encode($data, true))->execute();
            $redis->expireat($redisKey, strtotime('+' . $interval . ' minute'));
        }

        $dataRedis = $redis->pipeline()->get($redisKey)->execute();
        foreach ($dataRedis as $aDataRedis) {
            return json_decode($aDataRedis, true);
        }
    } catch (Exception $exception) {
        return dbGetMenuForGroup($menuGroupID);
    }


}

function dbGetClientLogo()
{
    global $dbh, $clientDB;
    $prevDB = $dbh->_settings['Database'];
    $dbh->selectDatabase(SYSTEMDB);
    $sql = 'SELECT logo_url
				FROM database_list
				WHERE database_name = ?';
    $return = $dbh->executeScalar($sql, [$clientDB]);
    if ($prevDB) {
        $dbh->selectDatabase($prevDB);
    }

    return $return;
}

function dbGetClientName()
{
    global $dbh, $clientDB;
    $prevDB = $dbh->_settings['Database'];
    $dbh->selectDatabase(SYSTEMDB);
    $sql = 'SELECT description
				FROM database_list
				WHERE database_name = ?';
    $return = $dbh->executeScalar($sql, [$clientDB]);
    if ($prevDB) {
        $dbh->selectDatabase($prevDB);
    }

    return $return;
}


function dbGetClientID()
{
    global $dbh, $clientDB;
    $prevDB = $dbh->_settings['Database'];
    $dbh->selectDatabase(SYSTEMDB);
    $sql = 'SELECT database_id
				FROM database_list
				WHERE database_name = ?';
    $return = $dbh->executeScalar($sql, [$clientDB]);
    if ($prevDB) {
        $dbh->selectDatabase($prevDB);
    }

    return $return;
}

function dbGetClientTimezone()
{
    global $dbh, $clientDB;
    $prevDB = $dbh->_settings['Database'];
    $dbh->selectDatabase(SYSTEMDB);
    $sql = 'SELECT php_timezone
				FROM database_list
				WHERE database_name = ?';
    $return = $dbh->executeScalar($sql, [$clientDB]);
    if ($prevDB) {
        $dbh->selectDatabase($prevDB);
    }

    return $return;
}

function dbGetSystemAlertForUser()
{
    global $dbh, $clientDB;
    $prevDB = $dbh->_settings['Database'];
    $dbh->selectDatabase(SYSTEMDB);
    $sql = 'SELECT TOP 1 * FROM [system_alerts] WHERE [deleted_at] IS NULL AND [is_active] = 1 ORDER BY [is_active] DESC, [id] DESC';
    $return = $dbh->executeSingle($sql);
    if ($prevDB) {
        $dbh->selectDatabase($prevDB);
    }

    return $return;
}

function dbGetSysAlertUserView($alert_id, $user_id)
{
    global $dbh, $clientDB;
    $prevDB = $dbh->_settings['Database'];
    $dbh->selectDatabase(SYSTEMDB);
    $sql = 'SELECT TOP 1 * FROM [system_alert_views] WHERE alert_id = ? AND user_id = ? ORDER BY [id] DESC';
    $return = $dbh->executeSingle($sql, [$alert_id, $user_id]);
    if ($prevDB) {
        $dbh->selectDatabase($prevDB);
    }

    return $return;
}

function dbSeenSysAlert($alert_id, $user_id, $database_id)
{
    global $dbh, $clientDB;
    $prevDB = $dbh->_settings['Database'];
    $dbh->selectDatabase(SYSTEMDB);

    $params = [];
    $sql = 'INSERT INTO [system_alert_views](
				alert_id,
				is_seen,
				seen_at,
				user_id,
				database_id
			)
			VALUES (
				' . addSQLParam($params, $alert_id) . ",
				'1',
				GETDATE(),
				" . addSQLParam($params, $user_id) . ',
				' . addSQLParam($params, $database_id) . '
			)';
    $return = $dbh->executeNonQuery2($sql, $params);
    if ($prevDB) {
        $dbh->selectDatabase($prevDB);
    }

    return $return;
}

function dbDissmissSysAlert($alert_id, $user_id)
{
    global $dbh, $clientDB;
    $prevDB = $dbh->_settings['Database'];
    $dbh->selectDatabase(SYSTEMDB);
    $sql = 'UPDATE [system_alert_views] SET is_dismissed = 1, dismissed_at = GETDATE() WHERE alert_id = ? AND user_id = ?';
    $return = $dbh->executeNonQuery2($sql, [$alert_id, $user_id]);
    if ($prevDB) {
        $dbh->selectDatabase($prevDB);
    }

    return $return;
}


function dbGetUserSubGroupList()
{
    global $dbh;

    $dbh->selectDatabase(SYSTEMDB);

    $sql = '
        SELECT *
        FROM subGroups
        WHERE is_hidden = 0
        ORDER BY groupID ASC
    ';

    return $dbh->executeSet($sql);
}

function dbGetUserSubGroup($id)
{
    global $dbh;

    $dbh->selectDatabase(SYSTEMDB);

    $sql = '
        SELECT *
        FROM subGroups
        WHERE subGroupID = ?
    ';

    return $dbh->executeSingle($sql, [$id]);
}

function dbGetPageCategoriesBySubGroup($subgroup_id)
{
    global $dbh;

    $dbh->selectDatabase(SYSTEMDB);

    $sql = '
        SELECT *
        FROM page_categories
        WHERE status = 1
        AND user_subgroup_id = ?
        ORDER BY [page_categories].[order] ASC
    ';

    return $dbh->executeSet($sql, false, true, [$subgroup_id]);
}

function dbGetPageCategory($id)
{
    global $dbh;

    $dbh->selectDatabase(SYSTEMDB);

    $sql = '
        SELECT *
        FROM page_categories
        WHERE id = ?
    ';

    return $dbh->executeSingle($sql, [$id]);
}

function dbGetPageCategoryBySubGroupAndName($subgroup_id, $name)
{
    global $dbh;

    $dbh->selectDatabase(SYSTEMDB);

    $sql = '
        SELECT TOP 1 *
        FROM page_categories
        WHERE status = 1
        AND user_subgroup_id = ?
        AND name = ?
        ORDER BY [page_categories].[order] ASC
    ';

    return $dbh->executeSingle($sql, [$subgroup_id, $name]);
}

function dbAddPageCategory($item)
{
    global $dbh;

    $dbh->selectDatabase(SYSTEMDB);

    $params = [];
    $sql = '
        INSERT INTO page_categories (
            [page_categories].[user_subgroup_id],
            [page_categories].[name],
            [page_categories].[slug],
            [page_categories].[link],
            [page_categories].[icon],
            [page_categories].[module],
            [page_categories].[status],
            [page_categories].[order]
        )
        OUTPUT inserted.id
        VALUES (
            ' . addSQLParam($params, $item->user_subgroup_id) . ',
            ' . addSQLParam($params, $item->name) . ',
            ' . addSQLParam($params, $item->slug) . ',
            ' . addSQLParam($params, $item->link) . ',
            ' . addSQLParam($params, $item->icon) . ',
            ' . addSQLParam($params, $item->module) . ',
            1,
            ' . addSQLParam($params, $item->order) . '
        )
    ';

    return $dbh->executeSingle($sql, $params);
}

function dbUpdatePageCategory($item)
{
    global $dbh;

    $dbh->selectDatabase(SYSTEMDB);

    $params = [];
    $sql = '
        UPDATE page_categories
        SET name = ' . addSQLParam($params, $item->name) . ',
        slug = ' . addSQLParam($params, $item->slug) . ',
        link = ' . addSQLParam($params, $item->link) . ',
        icon = ' . addSQLParam($params, $item->icon) . ',
        module = ' . addSQLParam($params, $item->module) . '
        WHERE id = ' . addSQLParam($params, $item->id);

    return $dbh->executeNonQuery2($sql, $params);
}

function dbUpdatePageCategorySlug($item)
{
    global $dbh;

    $dbh->selectDatabase(SYSTEMDB);

    $sql = '
        UPDATE page_categories
        SET slug = ?
        WHERE id = ?
    ';

    return $dbh->executeNonQuery2($sql, [$item->slug, $item->id]);
}

function dbUpdatePageCategoryOrder($id, $order)
{
    global $dbh;

    $dbh->selectDatabase(SYSTEMDB);

    $sql = '
        UPDATE page_categories
        SET [page_categories].[order] = ?
        WHERE id = ?
    ';

    return $dbh->executeNonQuery2($sql, [$order, $id]);
}

function dbDeletePageCategory($id)
{
    global $dbh;

    $dbh->selectDatabase(SYSTEMDB);

    $sql = '
        UPDATE page_categories
        SET status = 0
        WHERE id = ?
    ';

    return $dbh->executeNonQuery2($sql, [$id]);
}

function dbGetMaxPageCategoryOrder($subgroup_id)
{
    global $dbh;

    $dbh->selectDatabase(SYSTEMDB);

    $sql = '
        SELECT MAX([page_categories].[order])
        FROM page_categories
        WHERE user_subgroup_id = ?
    ';

    return $dbh->executeScalar($sql, [$subgroup_id]);
}

function dbGetpageSubCategoriesByPageCategory($page_category_id, $param_redis = false)
{
    try {
        global $dbh, $redis;
        $dbh->selectDatabase(SYSTEMDB);

        $sql = '
            SELECT *
            FROM page_subcategories
            WHERE status = 1
            AND page_category_id = ?
            ORDER BY [page_subcategories].[order] ASC
        ';

        if ($param_redis && USE_REDIS) {
            $redisKey = 'c8:dbGetpageSubCategoriesByPageCategory-' . $page_category_id . '_' . $_SESSION['super'] . '_' . $_SESSION['readOnlyAccess'] . '_' . $_SESSION['user_sub_type'];
            if (! $redis->exists($redisKey)) {
                $data = $dbh->executeSet($sql, false, true, [$page_category_id]);
                $redis->pipeline()->set($redisKey, json_encode($data, true))->execute();
                $redis->expireat($redisKey, strtotime('+1440 minute'));
            }

            $dataRedis = $redis->pipeline()->get($redisKey)->execute();
            foreach ($dataRedis as $aDataRedis) {
                return json_decode($aDataRedis, true);
            }
        } else {
            return $dbh->executeSet($sql, false, true, [$page_category_id]);
        }
    } catch (Exception $exception) {
        return dbGetpageSubCategoriesByPageCategory($page_category_id, false);
    }


}

function dbGetPageSubCategory($id)
{
    global $dbh;

    $dbh->selectDatabase(SYSTEMDB);

    $sql = '
        SELECT *
        FROM page_subcategories
        WHERE id = ?
    ';

    return $dbh->executeSingle($sql, [$id]);
}

function dbAddPageSubCategory($item)
{
    global $dbh;

    $dbh->selectDatabase(SYSTEMDB);

    $params = [];
    $sql = '
        INSERT INTO page_subcategories (
            [page_subcategories].[page_category_id],
            [page_subcategories].[name],
            [page_subcategories].[slug],
            [page_subcategories].[display],
            [page_subcategories].[status],
            [page_subcategories].[order]
        )
        OUTPUT inserted.id
        VALUES (
            ' . addSQLParam($params, $item->page_category_id) . ',
            ' . addSQLParam($params, $item->name) . ',
            ' . addSQLParam($params, $item->slug) . ',
            ' . addSQLParam($params, $item->display) . ',
            1,
            ' . addSQLParam($params, $item->order) . '
        )
    ';

    return $dbh->executeSingle($sql, $params);
}

function dbUpdatePageSubCategory($item)
{
    global $dbh;

    $dbh->selectDatabase(SYSTEMDB);

    $sql = '
        UPDATE page_subcategories
        SET name = ?,
        display = ?
        WHERE id = ?
    ';

    return $dbh->executeNonQuery2($sql, [$item->name, $item->display, $item->id]);
}

function dbUpdatePageSubCategorySlug($item)
{
    global $dbh;

    $dbh->selectDatabase(SYSTEMDB);

    $sql = '
        UPDATE page_subcategories
        SET slug = ?
        WHERE id = ?
    ';

    return $dbh->executeNonQuery2($sql, [$item->slug, $item->id]);
}

function dbUpdatePageSubCategoryOrder($id, $order)
{
    global $dbh;

    $dbh->selectDatabase(SYSTEMDB);

    $sql = '
        UPDATE page_subcategories
        SET [page_subcategories].[order] = ?
        WHERE id = ?
    ';

    return $dbh->executeNonQuery2($sql, [$order, $id]);
}

function dbDeletePageSubCategory($id)
{
    global $dbh;

    $dbh->selectDatabase(SYSTEMDB);

    $sql = '
        UPDATE page_subcategories
        SET status = 0
        WHERE id = ?
    ';

    return $dbh->executeNonQuery2($sql, [$id]);
}

function dbGetMaxPageSubCategoryOrder($category_id)
{
    global $dbh;

    $dbh->selectDatabase(SYSTEMDB);

    $sql = '
        SELECT COALESCE( MAX([page_subcategories].[order]),0 )
        FROM page_subcategories
        WHERE page_category_id = ?
    ';

    return $dbh->executeScalar($sql, [$category_id]);
}

function dbGetPageRoutesByPageCategory($category_id, $hidden = true, $param_redis = true)
{
    try {
        global $dbh, $redis, $clientDB;
        $dbh->selectDatabase(SYSTEMDB);

        $hiddenSubQuery = $hidden ? 'display IN (0, 1)' : 'display = 1';
        $sql = "
            SELECT *
            FROM page_routes
            WHERE status = 1
            AND id NOT IN (SELECT page_route_id FROM route_client_exception WHERE client_code = ? )
            AND page_category_id = ?
            AND {$hiddenSubQuery}
            ORDER BY [page_routes].[order] ASC
        ";

        if ($param_redis && USE_REDIS) {
            $redisKey = 'c8:dbGetPageRoutesByPageCategory-' . $category_id . '_' . $hidden . '_' . $_SESSION['super'] . '_' . $_SESSION['readOnlyAccess'] . '_' . $_SESSION['user_sub_type'] . '_' . $clientDB;
            if (! $redis->exists($redisKey)) {
                $data = $dbh->executeSet($sql, false, true, [$clientDB, $category_id]);
                $redis->pipeline()->set($redisKey, json_encode($data, true))->execute();
                $redis->expireat($redisKey, strtotime('+1440 minute'));
            }

            $dataRedis = $redis->pipeline()->get($redisKey)->execute();
            foreach ($dataRedis as $aDataRedis) {
                return json_decode($aDataRedis, true);
            }
        } else {
            return $dbh->executeSet($sql, false, true, [$clientDB, $category_id]);
        }
    } catch (Exception $exception) {
        return dbGetPageRoutesByPageCategory($category_id, $hidden, false);
    }


}

function dbGetPageRoutesByPageCategoryAndSubCategory($category_id, $subcategory_id, $hidden = true, $param_redis = true)
{
    try {
        global $dbh, $redis, $clientDB;
        $dbh->selectDatabase(SYSTEMDB);

        $hiddenSubQuery = $hidden ? 'display IN (0, 1)' : 'display = 1';
        $sql = "
            SELECT *
            FROM page_routes
            WHERE status = 1
            AND id NOT IN (SELECT page_route_id FROM route_client_exception WHERE client_code = '{$clientDB}' )
            AND page_category_id = ?
            AND page_subcategory_id = ?
            AND {$hiddenSubQuery}
            ORDER BY [page_routes].[order] ASC
        ";

        if ($param_redis && USE_REDIS) {
            $redisKey = 'c8:dbGetPageRoutesByPageCategoryAndSubCategory-' . $category_id . '_' . $subcategory_id . '_' . $hidden . '_' . $_SESSION['super'] . '_' . $_SESSION['readOnlyAccess'] . '_' . $_SESSION['user_sub_type'] . '_' . $clientDB;
            if (! $redis->exists($redisKey)) {
                $data = $dbh->executeSet($sql, false, true, [$category_id, $subcategory_id]);
                $redis->pipeline()->set($redisKey, json_encode($data, true))->execute();
                $redis->expireat($redisKey, strtotime('+1440 minute'));
            }

            $dataRedis = $redis->pipeline()->get($redisKey)->execute();
            foreach ($dataRedis as $aDataRedis) {
                return json_decode($aDataRedis, true);
            }
        } else {
            return $dbh->executeSet($sql, false, true, [$category_id, $subcategory_id]);
        }
    } catch (Exception $exception) {
        return dbGetPageRoutesByPageCategoryAndSubCategory($category_id, $subcategory_id, $hidden, false);
    }


}

function dbGetPageRoutesByPageCategoryJoinSubCategory($category_id, $hidden = true)
{
    $hiddenSubQuery = $hidden ? 'pr.display IN (0, 1)' : 'pr.display = 1';

    global $dbh;

    $dbh->selectDatabase(SYSTEMDB);

    $sql = "
        SELECT pr.*, psc.name AS page_subcategory_name
        FROM page_routes pr
        LEFT JOIN page_subcategories psc ON  pr.page_subcategory_id = psc.id
        WHERE pr.status = 1
		AND pr.page_category_id = ?
		AND {$hiddenSubQuery}
        ORDER BY pr.[order] ASC
    ";

    return $dbh->executeSet($sql, false, true, [$category_id]);
}

function dbGetPageRoute($id)
{
    global $dbh;

    $dbh->selectDatabase(SYSTEMDB);

    $sql = '
        SELECT *
        FROM page_routes
        WHERE id = ?
    ';

    return $dbh->executeSingle($sql, [$id]);
}

function dbAddPageRoute($item)
{
    global $dbh;

    $dbh->selectDatabase(SYSTEMDB);

    $params = [];
    $sql = '
        INSERT INTO page_routes (
            [page_routes].[page_category_id],
            [page_routes].[page_subcategory_id],
            [page_routes].[name],
            [page_routes].[slug],
            [page_routes].[route],
            [page_routes].[status],
            [page_routes].[order],
            [page_routes].[featured],
            [page_routes].[admin],
            [page_routes].[display]
        )
        OUTPUT inserted.id
        VALUES (
            ' . addSQLParam($params, $item->page_category_id) . ',
            ' . (($item->page_subcategory_id && $item->page_subcategory_id != 'NULL') ? addSQLParam(
        $params,
        $item->page_subcategory_id
    ) : 'NULL') . ',
            ' . addSQLParam($params, $item->name) . ',
            ' . addSQLParam($params, $item->slug) . ',
            ' . addSQLParam($params, $item->route) . ',
            1,
            ' . addSQLParam($params, $item->order) . ',
            ' . addSQLParam($params, $item->featured) . ',
            ' . addSQLParam($params, $item->admin) . ',
            ' . addSQLParam($params, $item->display) . '
        )
    ';

    return $dbh->executeSingle($sql, $params);
}

function dbUpdatePageRoute($item)
{
    global $dbh;

    $dbh->selectDatabase(SYSTEMDB);

    $params = [];
    $sql = '
        UPDATE page_routes
        SET name = ' . addSQLParam($params, $item->name) . ',
        page_subcategory_id = ' . addSQLParam($params, $item->page_subcategory_id) . ',
        slug = ' . addSQLParam($params, $item->slug) . ',
        route = ' . addSQLParam($params, $item->route) . ',
        display = ' . addSQLParam($params, $item->display) . ',
        featured = ' . addSQLParam($params, $item->featured) . ',
        admin = ' . addSQLParam($params, $item->admin) . '
        WHERE id = ' . addSQLParam($params, $item->id) . '
    ';

    return $dbh->executeNonQuery2($sql, $params);
}

function dbUpdatePageRouteSlug($item)
{
    global $dbh;

    $dbh->selectDatabase(SYSTEMDB);

    $sql = '
        UPDATE page_routes
        SET slug = ?
        WHERE id = ?
    ';

    return $dbh->executeNonQuery2($sql, [$item->slug, $item->id]);
}

function dbUpdatePageRouteOrder($id, $order)
{
    global $dbh;

    $dbh->selectDatabase(SYSTEMDB);

    $sql = '
        UPDATE page_routes
        SET [page_routes].[order] = ?
        WHERE id = ?
    ';

    return $dbh->executeNonQuery2($sql, [$order, $id]);
}

function dbDeletePageRoute($id)
{
    global $dbh;

    $dbh->selectDatabase(SYSTEMDB);

    $sql = '
        UPDATE page_routes
        SET status = 0
        WHERE id = ?
    ';

    return $dbh->executeNonQuery2($sql, [$id]);
}

function dbGetMaxPageRouteOrder($category_id)
{
    global $dbh;

    $dbh->selectDatabase(SYSTEMDB);

    $sql = '
        SELECT MAX([page_routes].[order])
        FROM page_routes
        WHERE page_category_id = ?
    ';

    return $dbh->executeScalar($sql, [$category_id]);
}

/* FUNCTIONS FOR REVISED MENU FRONT END */

function dbGetPageCategoriesBySubGroupName($subgroup_name, $modules = true)
{
    global $dbh;
    $dbh->selectDatabase(SYSTEMDB);

    $module_subquery = $modules ? 1 : 0;

    $sql = '
        SELECT pc.*
        FROM page_categories pc
        JOIN subGroups sg ON  pc.user_subgroup_id = sg.subGroupID
        WHERE pc.status = 1
        AND pc.module = ?
        AND sg.subGroupName = ?
        ORDER BY [pc].[order] ASC
    ';

    $redisKey = 'c8:dbGetPageCategoriesBySubGroupName-' . $subgroup_name . '_' . $modules . '_' . $_SESSION['super'] . '_' . $_SESSION['readOnlyAccess'] . '_' . $_SESSION['user_sub_type'];

    return getDataFromRedisOrSet($redisKey, fn() => $dbh->executeSet($sql, false, true, [$module_subquery, $subgroup_name]));
}

function dbGetPageCategoryDefaultLink($category_id)
{
    global $dbh;
    $dbh->selectDatabase(SYSTEMDB);
    $redisKey = 'c8:dbGetPageCategoryDefaultLink-' . $category_id . '_' . $_SESSION['super'] . '_' . $_SESSION['readOnlyAccess'] . '_' . $_SESSION['user_sub_type'];

    $sql = '
        SELECT TOP 1 route
        FROM page_routes
        WHERE page_category_id = ?
        AND featured = 1
        AND status = 1
        AND display = 1
        ORDER by [page_routes].[order] ASC
    ';

    return getDataFromRedisOrSet($redisKey, fn() => $dbh->executeSingle($sql, [$category_id]));
}

function dbGetPageCategoryFirstLink($category_id, $param_redis = true)
{
    try {
        global $dbh, $redis;
        $dbh->selectDatabase(SYSTEMDB);

        $sql = '
            SELECT TOP 1 route
            FROM page_routes
            WHERE page_category_id = ?
            AND status = 1
            AND display = 1
            ORDER by [page_routes].[order] ASC
        ';

        if ($param_redis && USE_REDIS) {
            $redisKey = 'c8:dbGetPageCategoryFirstLink-' . $category_id . '_' . $_SESSION['super'] . '_' . $_SESSION['readOnlyAccess'] . '_' . $_SESSION['user_sub_type'];
            if (! $redis->exists($redisKey)) {
                $data = $dbh->executeSingle($sql, [$category_id]);
                $redis->pipeline()->set($redisKey, json_encode($data, true))->execute();
                $redis->expireat($redisKey, strtotime('+1440 minute'));
            }

            $dataRedis = $redis->pipeline()->get($redisKey)->execute();
            foreach ($dataRedis as $aDataRedis) {
                return json_decode($aDataRedis, true);
            }
        } else {
            return $dbh->executeSingle($sql, [$category_id]);
        }
    } catch (Exception $exception) {
        return dbGetPageCategoryFirstLink($category_id, false);
    }


}

function dbGetUserSubGroupByName($name)
{
    global $dbh;

    $dbh->selectDatabase(SYSTEMDB);

    $sql = '
        SELECT *
        FROM subGroups
        WHERE subGroupName = ?
    ';

    return $dbh->executeSingle($sql, [$name]);
}

function dbGetPageRouteByLink($link)
{
    global $dbh;
    $dbh->selectDatabase(SYSTEMDB);

    $sql = '
            SELECT pr.*
            FROM page_routes pr
            JOIN page_categories pc ON  pr.page_category_id = pc.id
            AND pr.route = ?
            AND pr.display = 1
            AND pr.status = 1
            ORDER BY [pr].[order] ASC
        ';

    $redisKey = 'c8:dbGetPageRouteByLink-' . $link . '_' . $_SESSION['super'] . '_' . $_SESSION['readOnlyAccess'] . '_' . $_SESSION['user_sub_type'];

    return getDataFromRedisOrSet($redisKey, fn() => $dbh->executeSingle($sql, [$link]));
}

function dbGetPageRouteByUserSubgroupAndLink($group, $link, $shown = true)
{
    global $dbh;
    $removeSpaceOnGroup = str_replace(' ', '', $group);
    $dbh->selectDatabase(SYSTEMDB);
    $displayRedisKey = '';
    $shown_subquery = '';
    if ($shown) {
        $shown_subquery = ' AND pr.display = 1';
        $displayRedisKey = 'showDisplay';
    }

    $sql = "
            SELECT pr.*
            FROM page_routes pr
            JOIN page_categories pc ON  pr.page_category_id = pc.id
            JOIN subGroups sg ON  pc.user_subgroup_id = sg.subGroupID
            WHERE sg.subGroupName = ?
            AND pr.route = ?
            {$shown_subquery}
            AND pr.status = 1
            ORDER BY [pr].[order] ASC
        ";

    $redisKey = 'c8:dbGetPageRouteByUserSubgroupAndLink-' . $removeSpaceOnGroup . '_' . $link . '_' . $displayRedisKey;

    return getDataFromRedisOrSet($redisKey, fn() => $dbh->executeSingle($sql, [$group, $link]));
}

function dbGetPageRouteByUserSubgroupAndLinkWithSubGroup($group, $link)
{
    global $dbh, $clientDB;

    $dbh->selectDatabase(SYSTEMDB);

    $sql = "
            SELECT pr.*
            FROM page_routes pr
            JOIN page_categories pc ON  pr.page_category_id = pc.id
            JOIN subGroups sg ON  pc.user_subgroup_id = sg.subGroupID
            WHERE sg.subGroupName = ?
            AND pr.id NOT IN (SELECT page_route_id FROM route_client_exception WHERE client_code = '{$clientDB}' )
            AND pr.route = ?
            AND pr.display = 1
            AND pr.status = 1
            AND pr.page_subcategory_id > 0
            ORDER BY [pr].[order] ASC
        ";

    $redisKey = 'c8:dbGetPageRouteByUserSubgroupAndLinkWithSubGroup-' . $group . '_' . $link . '_' . $_SESSION['super'] . '_' . $_SESSION['readOnlyAccess'] . '_' . $_SESSION['user_sub_type'] . '_' . $clientDB;

    return getDataFromRedisOrSet($redisKey, fn() => $dbh->executeSingle($sql, [$group, $link]));
}

/**
 * Get the data from the callback if saved in redis, else first save it then return the data.
 *
 * @param  string  $redisKey  the key to get or set the data on
 * @param  callable  $dataCallback  the callback to fetch the data from
 * @param  string  $expiryTime  the expire time as a string converted to strtotime()
 *
 * @returns mixed the data parses from redis
 */
function getDataFromRedisOrSet(string $redisKey, callable $dataCallback, string $expiryTime = '+1440 minute')
{
    global $redis;

    try {
        if (! USE_REDIS) {
            return $dataCallback();
        }

        if (! $redis->exists($redisKey)) {

            $data = $dataCallback();
            $redis->pipeline()->set($redisKey, json_encode($data, true))->execute();
            $redis->expireat($redisKey, strtotime($expiryTime));

            return $data;
        }

        $dataRedis = $redis->pipeline()->get($redisKey)->execute();
        foreach ($dataRedis as $aDataRedis) {
            return json_decode($aDataRedis, true);
        }

    } catch (Exception $exception) {
        myErrorHandlerV2($exception, $exception->getMessage(), $exception->getFile(), $exception->getLine());

        return $dataCallback();
    }
}

function dbGetPageRetailSalesPlus()
{
    global $dbh, $clientDB;
    $dbh->selectDatabase(SYSTEMDB);

    $sql = "
        SELECT *
        FROM page_routes
        WHERE status = 1
        AND id NOT IN (SELECT page_route_id FROM route_client_exception WHERE client_code = ? )
        AND page_subcategory_id IN (SELECT ID FROM page_subcategories WHERE name = 'Retail Plus' )
        AND display = 1
        ORDER BY [page_routes].[order] ASC
    ";

    $redisKey = 'c8:dbGetPageRouteNotInClientException-_' . $clientDB;

    return getDataFromRedisOrSet($redisKey, fn() => $dbh->executeSet($sql, false, true, [$clientDB]));
}

function dbGetPageCategoryByUserSubgroupAndLink($group, $link)
{
    global $dbh;

    $dbh->selectDatabase(SYSTEMDB);

    $sql = '
        SELECT pc.*
        FROM page_categories pc
        JOIN subGroups sg ON  pc.user_subgroup_id = sg.subGroupID
        WHERE subGroupName = ?
        AND pc.link = ?
        AND pc.status = 1
        ORDER BY [pc].[order] ASC
    ';

    $redisKey = 'c8:dbGetPageCategoryByUserSubgroupAndLink-' . $group . '_' . $link . '_' . $_SESSION['super'] . '_' . $_SESSION['readOnlyAccess'] . '_' . $_SESSION['user_sub_type'];

    return getDataFromRedisOrSet($redisKey, fn() => $dbh->executeSingle($sql, [$group, $link]));
}

function dbIsPageCategoryModule($group, $link)
{
    global $dbh;
    $dbh->selectDatabase(SYSTEMDB);

    $sql = '
        SELECT pc.*
        FROM page_categories pc
        JOIN subGroups sg ON  pc.user_subgroup_id = sg.subGroupID
        WHERE subGroupName = ?
        AND pc.link = ?
        AND pc.status = 1
        AND pc.module = 0
        ORDER BY [pc].[order] ASC
    ';

    $redisKey = 'c8:dbIsPageCategoryModule-' . $group . '_' . $link . '_' . $_SESSION['super'] . '_' . $_SESSION['readOnlyAccess'] . '_' . $_SESSION['user_sub_type'];

    return getDataFromRedisOrSet($redisKey, fn() => $dbh->executeSingle($sql, [$group, $link]));
}

function dbGetPageRoutesByLink($link)
{
    global $dbh;

    $dbh->selectDatabase(SYSTEMDB);

    $sql = '
        SELECT pr.*, pc.user_subgroup_id
		FROM page_routes pr
		JOIN page_categories pc ON  pr.page_category_id = pc.id
		JOIN subGroups sg ON  pc.user_subgroup_id = sg.subGroupID
		AND pr.route = ?
		AND pr.display = 1
		AND pr.status = 1
		ORDER BY [pr].[order] ASC
	';

    return $dbh->executeSet($sql, false, true, [$link]);
}

function dbGetPageCategoriesByLink($link)
{
    global $dbh;

    $dbh->selectDatabase(SYSTEMDB);

    $sql = '
        SELECT pc.*
		FROM page_categories pc
		JOIN subGroups sg ON  pc.user_subgroup_id = sg.subGroupID
		AND pc.link = ?
		AND pc.status = 1
		ORDER BY [pc].[order] ASC
	';

    return $dbh->executeSingle($sql, [$link]);
}

function ownerMenuPermission($command)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase(SYSTEMDB);
    $sql = "SELECT owner_page_id as pageID
            FROM owner_menu
            LEFT JOIN pages on owner_page_id = pageID
            WHERE owner_client_name = '{$clientDB}'
            AND command = ?";

    return $dbh->executeScalar($sql, [$command]);
}

function dbGetUsePortfolio()
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);

    $sql = "
		SELECT
			pmzz_code as parameterID,
			pmzz_desc as parameterDescription
		FROM
			pmzz_param
		WHERE
			pmzz_par_type = 'PORTMGR'
			and pmzz_code in (SELECT pmpr_portfolio from pmpr_property where pmpr_delete = 0)
		ORDER BY pmzz_desc
		";

    return $dbh->executeSet($sql);
}

function owner_menu_access($pageID)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase(SYSTEMDB);
    $sql = "SELECT owner_page_id as pageID FROM owner_menu WHERE owner_client_name = '{$clientDB}' and owner_page_id = ?";

    return $dbh->executeScalar($sql, [$pageID]);
}

function ownerMenuCountAccess($title, $userGroup)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase(SYSTEMDB);
    $sql = "SELECT
            count(o.owner_page_id) as cnt
			FROM
			modules m,
			pages p,
			menuGroups g,
			userGroups u,
			owner_menu o
			WHERE
				g.menuGroupID = p.menuGroupID
				AND g.groupID = u.groupID
				AND u.groupCode = ?
				AND p.moduleID=m.moduleID
				AND p.pageID = o.owner_page_id
				AND g.title = ?
				AND o.owner_client_name = '{$clientDB}'
            ";

    return $dbh->executeSet($sql, false, true, [$userGroup, $title]);
}

function ownerAccessRestriction($command)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase(SYSTEMDB);
    $sql = "SELECT
            count(o.owner_page_id) as cnt
			FROM
			modules m,
			pages p,
			menuGroups g,
			userGroups u,
			owner_menu o
			WHERE
				g.menuGroupID = p.menuGroupID
				AND g.groupID = u.groupID
				AND u.groupCode = 'O'
				AND p.moduleID=m.moduleID
				AND p.pageID = o.owner_page_id
				AND p.command = ?
				AND o.owner_client_name = '{$clientDB}'
				and g.title in ('Financials','Reporting')
            ";

    return $dbh->executeSet($sql, false, true, [$command]);
}


function dbGetExecutiveDashboardSummaryDrilldown($database, $selectedDB, $code, $year, $period)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);

    $sql = "Select pmcm_start_dt from pmcm_mast_cal where pmcm_code = 'financial' and pmcm_year = ? and pmcm_period = 1";
    $query = $dbh->executeSingle($sql, [$year]);
    $currentFirstPeriod = $query['pmcm_start_dt'];

    $sql = "Select pmcm_end_dt from pmcm_mast_cal where pmcm_code = 'financial' and pmcm_year = ? and pmcm_period = ?";
    $query = $dbh->executeSingle($sql, [$year, $period]);
    $currentEndPeriod = $query['pmcm_end_dt'];

    $lastYear = $year - 1;
    $sql = "Select pmcm_start_dt from pmcm_mast_cal where pmcm_code = 'financial' and pmcm_year = ? and pmcm_period = 1";
    $query = $dbh->executeSingle($sql, [$lastYear]);
    $lastYearFirstPeriod = $query['pmcm_start_dt'];
    $sql = "Select pmcm_end_dt from pmcm_mast_cal where pmcm_code = 'financial' and pmcm_year = ? and pmcm_period = 12";
    $query = $dbh->executeSingle($sql, [$lastYear]);
    $lastYearEndPeriod = $query['pmcm_end_dt'];
    $params = [];
    $filterManager_params = [];
    $filterManager = $code ? ' And pmzz_code = ' . addSQLParam($filterManager_params, $code) : '';

    foreach ($database as $rs) {
        $db = $rs['database_name'];
        if (! $selectedDB || $selectedDB == $db) {
            $longName = $rs['description'];
            $dbName = count($database ?? []) > 1 ? '(' . $rs['short_name'] . ')' : '';
            $queries[] = '
		 Select pmzz_param.pmzz_code AS portfolio,   LTRIM(RTRIM(pmzz_param.pmzz_code))  +  ' . addSQLParam(
                $params,
                $dbName
            ) . " as label,
		 LTRIM(RTRIM(pmzz_param.pmzz_code))  as manager,
		 '{$db}' as shortName,
		  " . addSQLParam($params, $longName) . ' as databaseName ,
		' . addSQLParam($params, $currentFirstPeriod) . ' as currentFirstPeriod,
		' . addSQLParam($params, $lastYearFirstPeriod) . " as lastYearFirstPeriod,
		 (select count(1) AS current_year from {$db}.dbo.pmpr_property  where (pmpr_inactive_date > CONVERT(datetime, " . addSQLParam(
                $params,
                $currentEndPeriod
            ) . ', 103) or pmpr_inactive_date is null)   and pmpr_create_dt <= CONVERT(datetime, ' . addSQLParam(
                $params,
                $currentEndPeriod
            ) . ", 103)  and pmpr_portfolio = pmzz_code and pmpr_is_ledger != 1 ) as current_year,
		 (select count(1) AS property_gain from {$db}.dbo.pmpr_property  where pmpr_create_dt between  CONVERT(datetime, " . addSQLParam(
                $params,
                $currentFirstPeriod
            ) . ', 103) and CONVERT(datetime, ' . addSQLParam($params, $currentEndPeriod) . ", 103)  and pmpr_portfolio = pmzz_param.pmzz_code  and pmpr_is_ledger != 1 ) as property_gain,
		 (select count(1) AS property_loses  from {$db}.dbo.pmpr_property where pmpr_inactive_date between  CONVERT(datetime, " . addSQLParam(
                $params,
                $currentFirstPeriod
            ) . ', 103) and   CONVERT(datetime, ' . addSQLParam($params, $currentEndPeriod) . ", 103)  and pmpr_portfolio = pmzz_param.pmzz_code  and pmpr_is_ledger != 1 ) as property_loses,
		 (select count(1) AS last_year from {$db}.dbo.pmpr_property where  (pmpr_inactive_date > CONVERT(datetime, " . addSQLParam(
                $params,
                $lastYearEndPeriod
            ) . ', 103) or pmpr_inactive_date is null)  and pmpr_create_dt <= CONVERT(datetime, ' . addSQLParam(
                $params,
                $lastYearEndPeriod
            ) . ", 103)  and pmpr_portfolio = pmzz_code and pmpr_is_ledger != 1 ) as last_year,
		 (select count(1) AS active_current_unit from {$db}.dbo.pmpr_property  JOIN {$db}.dbo.pmua_unit_area on pmua_prop = pmpr_prop and pmua_status = 'O' and pmua_from_dt < CONVERT(datetime, " . addSQLParam(
                $params,
                $currentEndPeriod
            ) . ', 103) and pmua_to_dt >= CONVERT(datetime, ' . addSQLParam($params, $currentEndPeriod) . ', 103)
		 	JOIN pmpu_p_unit on pmpu_prop = pmua_prop and pmpu_unit = pmua_unit
		 	where (pmpr_inactive_date > CONVERT(datetime, ' . addSQLParam(
                $params,
                $currentEndPeriod
            ) . ', 103) or pmpr_inactive_date is null)  and pmpr_create_dt <= CONVERT(datetime, ' . addSQLParam(
                $params,
                $currentEndPeriod
            ) . ", 103)  and pmpr_portfolio = pmzz_code and pmpr_is_ledger != 1 and pmpu_tenancy = 0) as active_current_year,
		 (select count(1) AS active_last_year_unit from {$db}.dbo.pmpr_property  JOIN {$db}.dbo.pmua_unit_area on pmua_prop = pmpr_prop and pmua_status = 'O' and pmua_from_dt < CONVERT(datetime, " . addSQLParam(
                $params,
                $lastYearEndPeriod
            ) . ', 103) and pmua_to_dt >= CONVERT(datetime, ' . addSQLParam($params, $lastYearEndPeriod) . ', 103)
		 	JOIN pmpu_p_unit on pmpu_prop = pmua_prop and pmpu_unit = pmua_unit
		 	where (pmpr_inactive_date > CONVERT(datetime, ' . addSQLParam(
                $params,
                $lastYearEndPeriod
            ) . ', 103) or pmpr_inactive_date is null) and pmpr_create_dt <=  CONVERT(datetime, ' . addSQLParam(
                $params,
                $lastYearEndPeriod
            ) . ", 103)  and pmpr_portfolio = pmzz_code and pmpr_is_ledger != 1  and pmpu_tenancy = 0) as active_last_year,
         (select count(1) AS vacant_units from {$db}.dbo.pmpr_property  JOIN {$db}.dbo.pmua_unit_area on pmua_prop = pmpr_prop and pmua_status = 'V' and pmua_from_dt < CONVERT(datetime, " . addSQLParam(
                $params,
                $currentEndPeriod
            ) . ', 103) and pmua_to_dt > CONVERT(datetime, ' . addSQLParam($params, $currentEndPeriod) . ', 103)
         	JOIN pmpu_p_unit on pmpu_prop = pmua_prop and pmpu_unit = pmua_unit
         	where (pmpr_inactive_date > CONVERT(datetime, ' . addSQLParam(
                $params,
                $currentEndPeriod
            ) . ', 103) or pmpr_inactive_date is null) and pmpr_create_dt <= CONVERT(datetime, ' . addSQLParam(
                $params,
                $currentEndPeriod
            ) . ", 103)  and pmpr_portfolio = pmzz_code and pmpr_is_ledger != 1  and pmpu_tenancy = 0) as vacant,
     	 (select count(1) AS current_share_owner from ( select distinct pmpr_owner as portfolio from  {$db}.dbo.pmpr_property where (pmpr_inactive_date >  CONVERT(datetime, " . addSQLParam(
                $params,
                $currentEndPeriod
            ) . ', 103)  or pmpr_inactive_date is null) and pmpr_create_dt <= CONVERT(datetime, ' . addSQLParam(
                $params,
                $currentEndPeriod
            ) . ", 103)  and pmpr_portfolio = pmzz_code   and pmpr_is_ledger != 1 ) as total) as current_share_owner,
     	 (select count(1) AS last_share_owner from ( select distinct pmpr_owner as portfolio from  {$db}.dbo.pmpr_property where (pmpr_inactive_date >  CONVERT(datetime, " . addSQLParam(
                $params,
                $lastYearEndPeriod
            ) . ', 103)  or pmpr_inactive_date is null) and pmpr_create_dt <= CONVERT(datetime, ' . addSQLParam(
                $params,
                $lastYearEndPeriod
            ) . ", 103)  and pmpr_portfolio = pmzz_code  and pmpr_is_ledger != 1 {$filterManager} " . addSQLParam(
                $params,
                $filterManager_params,
                false
            ) . " ) as total) as last_share_owner,
     	 (select count(1) AS current_total_owner from ( select distinct pmpr_owner as portfolio from  {$db}.dbo.pmpr_property
     	 join {$db}.dbo.pmzz_param on pmzz_code = pmpr_portfolio and pmzz_param.pmzz_par_type='PORTMGR' where (pmpr_inactive_date >  CONVERT(datetime, " . addSQLParam(
                $params,
                $currentEndPeriod
            ) . ', 103)  or pmpr_inactive_date is null) and pmpr_create_dt <= CONVERT(datetime, ' . addSQLParam(
                $params,
                $currentEndPeriod
            ) . ", 103)   and pmpr_is_ledger != 1  and pmpr_portfolio != 'SL' {$filterManager} " . addSQLParam(
                $params,
                $filterManager_params,
                false
            ) . " ) as total) as current_total_owner,
     	 (select count(1) AS last_total_owner from ( select distinct pmpr_owner as portfolio from  {$db}.dbo.pmpr_property
     	 join {$db}.dbo.pmzz_param on pmzz_code = pmpr_portfolio and pmzz_param.pmzz_par_type='PORTMGR' where (pmpr_inactive_date >  CONVERT(datetime, " . addSQLParam(
                $params,
                $lastYearEndPeriod
            ) . ', 103)  or pmpr_inactive_date is null) and pmpr_create_dt <= CONVERT(datetime, ' . addSQLParam(
                $params,
                $lastYearEndPeriod
            ) . ", 103)   and pmpr_is_ledger != 1 and pmpr_portfolio != 'SL' {$filterManager} " . addSQLParam(
                $params,
                $filterManager_params,
                false
            ) . " ) as total) as last_total_owner
		 FROM {$db}.dbo.pmzz_param pmzz_param
		 where pmzz_param.pmzz_par_type='PORTMGR' {$filterManager} " . addSQLParam(
                $params,
                $filterManager_params,
                false
            ) . "
		 and pmzz_code in (Select pmpr_portfolio from {$db}.dbo.pmpr_property where pmpr_portfolio != 'SL')
";
        }
    }

    $sql = implode(' UNION ALL ', $queries);

    return $dbh->executeSet($sql, false, true, $params);
}

function dbGetExecutiveOwnerSharesTotal($database, $date)
{
    global $dbh;
    $params = [];
    $sql = ' select count(1) AS share_owner from
        ( select distinct pmos_owner as portfolio from ' . addSQLParam($params, $database) . '.dbo.pmpr_property
        join ' . addSQLParam($params, $database) . '.dbo.pmos_o_share on pmos_prop = pmpr_prop
        where (pmpr_inactive_date >=  CONVERT(datetime, ' . addSQLParam($params, $date) . ', 103)
        or pmpr_inactive_date is null)  )  as last_share_owner  ';

    return $dbh->executeSingle($sql, $params);
}

function dbGetLinkedDatabase()
{
    global $dbh;
    global $clientDB;
    $current = $_SESSION['clientID'];
    $database_name = $_SESSION['currentDB'];
    $dbh->selectDatabase('npms');
    $sql = ' select database_name , description,short_name from linked_Database
 join database_list on database_id = link_database  where link_db_id = ?';

    $result = $dbh->executeSet($sql, false, true, [$current]);
    $db = dbGetDatabase($_SESSION['clientID']);

    $arr[] = ['database_name' => $clientDB, 'description' => $database_name, 'short_name' => $db['short_name']];

    return array_merge($result, $arr);
}

function dbGetTimezone($clientTimezone_code)
{
    global $dbh;
    $dbh->selectDatabase(SYSTEMDB);
    $sql = 'SELECT * FROM timezones WHERE timezone_code = ?';

    return $dbh->executeSingle($sql, [$clientTimezone_code]);
}

function dbGetDefaultCountry()
{
    return $_SESSION['country_code'];
}

function addSQLParam(&$params, $newParams, $withReturn = true)
{
    $qmarks = '?';

    if (is_array($newParams)) {
        if (count($newParams) > 0) {
            $qmarks = implode(',', array_fill(0, count($newParams ?? []), '?'));
            $params = array_merge($params, $newParams);
        } elseif ($withReturn) {
            return "''";
        } // prevents error from empty array when using IN statement

    } else {
        $params[] = $newParams;
    }

    if ($withReturn) {
        return $qmarks;
    }


}


function isValidProperties($property, $dieOnFalse = false)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);

    if ($property == '') {
        return true;
    }

    if (is_array($property)) {
        foreach ($property as $prop) {
            $sql = 'SELECT count(pmpr_name) as count
			FROM pmpr_property
			WHERE pmpr_prop =?';
            $res = $dbh->executeScalar($sql, [$prop]);
            if ($res == 0) {
                if (! $dieOnFalse) {
                    return false;
                } else {
                    executeCommand('noAccess', 'home');
                }
            }

            break;
        }
    } else {
        $sql = 'SELECT count(pmpr_name) as count
			FROM pmpr_property
			WHERE pmpr_prop =?';
        $res = $dbh->executeScalar($sql, [$property]);

        if ($res == 0) {
            if (! $dieOnFalse) {
                return false;
            } else {
                executeCommand('noAccess', 'home');
            }
        }
    }

    return true;
}

function isValidLeases($lease, $propertyID, $withDie = false)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);

    if ($lease == '') {
        return true;
    }

    if (is_array($lease)) {
        foreach ($lease as $ls) {
            $sql = 'SELECT count(*) as count FROM pmle_lease WHERE pmle_lease = ? AND pmle_prop = ? ';
            $res = $dbh->executeScalar($sql, [$ls, $propertyID]);
            if ($res == 0) {
                if (! $withDie) {
                    return false;
                } else {
                    executeCommand('noAccess', 'home');
                }
            }
        }
    } else {
        $sql = 'SELECT count(*) as count FROM pmle_lease WHERE pmle_lease = ? AND pmle_prop = ? ';
        $res = $dbh->executeScalar($sql, [$lease, $propertyID]);
        if ($res == 0) {
            if (! $withDie) {
                return false;
            } else {
                executeCommand('noAccess', 'home');
            }
        }
    }

    return true;
}

function isValidAccountID($account, $withDie = false)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);

    if ($account == '') {
        return true;
    }

    if (is_array($account)) {
        foreach ($account as $ac) {
            $sql = 'SELECT count(*) as count FROM pmca_chart WHERE pmca_code = ?';
            $res = $dbh->executeScalar($sql, [$ac]);
            if ($res == 0) {
                if (! $withDie) {
                    return false;
                } else {
                    executeCommand('noAccess', 'home');
                }
            }
        }
    } else {
        $sql = 'SELECT count(*) as count FROM pmca_chart WHERE pmca_code = ?';
        $res = $dbh->executeScalar($sql, [$account]);
        if ($res == 0) {
            if (! $withDie) {
                return false;
            } else {
                executeCommand('noAccess', 'home');
            }
        }
    }

    return true;
}

function isValidPropertyManager($propMgr, $withDie = false)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);

    if ($propMgr == '') {
        return true;
    }

    if (is_array($propMgr)) {
        foreach ($propMgr as $pMgr) {
            $sql = "SELECT count(*) as count FROM pmzz_param WHERE pmzz_par_type IN ('PORTMGR') AND pmzz_code = ?";
            $res = $dbh->executeScalar($sql, [$pMgr]);
            if ($res == 0) {
                if (! $withDie) {
                    return false;
                } else {
                    executeCommand('noAccess', 'home');
                }
            }
        }
    } else {
        $sql = "SELECT count(*) as count FROM pmzz_param WHERE pmzz_par_type IN ('PORTMGR') AND pmzz_code = ?";
        $res = $dbh->executeScalar($sql, [$propMgr]);
        if ($res == 0) {
            if (! $withDie) {
                return false;
            } else {
                executeCommand('noAccess', 'home');
            }
        }
    }

    return true;
}

function isValidSupplier($supplier, $withDie = false)
{
    global $clientDB, $dbh;
    $dbh->selectDatabase($clientDB);

    if ($supplier == '') {
        return true;
    }

    if (is_array($supplier)) {
        foreach ($supplier as $supp) {
            $sql = 'SELECT count(*)
			FROM pmco_company
			WHERE
				(pmco_supplier=1 or pmco_owner=1)
			    AND pmco_code = ?
			order by pmco_active DESC, pmco_code ';
            $res = $dbh->executeScalar($sql, [$supp]);
            if ($res == 0) {
                if (! $withDie) {
                    return false;
                } else {
                    executeCommand('noAccess', 'home');
                }
            }
        }
    } else {
        $sql = 'SELECT count(*) as count FROM pmco_company WHERE (pmco_supplier=1 or pmco_owner=1)
			    AND pmco_code = ?';
        $res = $dbh->executeScalar($sql, [$supplier]);
        if ($res == 0) {
            if (! $withDie) {
                return false;
            } else {
                executeCommand('noAccess', 'home');
            }
        }
    }

    return true;
}

function prepareSQLParams($params, $emptyToNull = false)
{
    foreach ($params as $ind => $value) {
        $params[$ind] = ($value === '' && $emptyToNull || $value === null || $value === 'NULL') ? null : strval($value);
    }

    return $params;
}


function toClientTimeZone($dateTimeStr, $returnFormat = 'd/m/Y h:i A')
{
    $timezone = dbGetClientTimezone();

    $date1 = date_create($dateTimeStr);

    if ($timezone && $timezone != date_default_timezone_get()) {
        $date1->setTimezone(new DateTimeZone($timezone));
    }

    return $date1->format($returnFormat);
}

function dbGetClientCountryCode($clientDB)
{
    global $dbh;
    $dbh->selectDatabase($clientDB);

    $sql = '
	SELECT cms_sy_country
	FROM cms_system
	WHERE cms_sy_key = 1';
    $result = $dbh->executeSingle($sql);

    $country = $result['cms_sy_country'];
    if (! $country) {
        $country = 'AU';
    }

    return $country;
}

function dbGetEmailPageSourceId()
{
    global $dbh, $context, $dispatch;
    $dbh->selectDatabase(SYSTEMDB);

    $sql = "
	SELECT lablelID
	FROM email_log_lable
	WHERE module = '{$context['module']}' AND command = '{$dispatch['command']}'";
    $result = $dbh->executeSingle($sql);

    $id = $result['lablelID'];
    if (! $id) {
        $id = null;
    }

    return $id;
}

function dbGetInsertQueueEmailId($insert_param)
{
    global $dbh;
    $dbh->selectDatabase(SYSTEMDB);
    $sql = 'INSERT INTO queue_email (
                         database_id,
                         created_by,
                         email_log_label_id,
                         batch_no,
                         sender_email,
                         sender_name,
                         subject,
                         body,
                         identifier
			 ) OUTPUT INSERTED.id VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?);';

    return $dbh->executeMarkerQuery($sql, $insert_param, true);
}

function dbGetInsertQueueEmailActivityLog($queue_email_id, $email_address)
{
    global $dbh;
    $dbh->selectDatabase(SYSTEMDB);
    $sql = 'INSERT INTO queue_email_activity_log (recipient_email,queue_email_id) OUTPUT INSERTED.id VALUES (?, ?);';

    return $dbh->executeMarkerQuery($sql, [$email_address, $queue_email_id], true);
}

function dbGetInsertQueueEmailAttachmentId($insert_param)
{
    global $dbh;
    $dbh->selectDatabase(SYSTEMDB);
    $sql = 'INSERT INTO queue_email_attachments (
                         created_by,
                         queue_email_id,
                         file_name,
                         file_path,
                         file_content_type
			 ) OUTPUT INSERTED.id VALUES (?, ?, ?, ?, ?);';

    return $dbh->executeMarkerQuery($sql, $insert_param, true);
}

function dbGetInsertQueueEmailRecipientId($insert_param)
{
    global $dbh;
    $dbh->selectDatabase(SYSTEMDB);
    $sql = 'INSERT INTO queue_email_recipient (
                         created_by,
                         queue_email_id,
                         recipient_email,
                         overwritten_recipient,
                         recipient_type
			 ) OUTPUT INSERTED.id VALUES (?, ?, ?, ?, ?);';

    return $dbh->executeMarkerQuery($sql, $insert_param, true);
}

function dbGetInsertQueueEmailCCId($insert_param)
{
    global $dbh;
    $dbh->selectDatabase(SYSTEMDB);
    $sql = 'INSERT INTO queue_email_cc (
                         created_by,
                         queue_email_id,
                         cc_email,
                         cc_email_overwritten
			 ) OUTPUT INSERTED.id VALUES (?, ?, ?, ?);';

    return $dbh->executeMarkerQuery($sql, $insert_param, true);
}

function checkOwnerPropertyForDownload($fileName, $propertyID = '')
{
    global $dbh, $clientDB;
    $dbh->selectDatabase($clientDB);
    $userID = intval($_SESSION['user_id']);

    if ($propertyID != '') {
        $sql = 'SELECT COUNT(*) FROM pmow_owner
            WHERE user_id = ?
            AND property_id = ?';
        $params = [$userID, $propertyID];
    } else {
        $sql = 'SELECT COUNT(documentID) FROM documents
            JOIN pmow_owner
            ON property_id = primaryID
            AND user_id = ?
            WHERE filename = ?';
        $params = [$userID, $fileName];
    }

    return $dbh->executeScalar($sql, $params);
}


function dbCreateTempTableForInStatement($data)
{
    $values = [];

    if (is_numeric($data)) {
        $values = [$data];
    }

    if (is_array($data)) {
        foreach ($data as $value) {
            if (is_numeric($data)) {
                $values[] = $value;
            } else {
                $unpacked = unpack('H*hex', $value);
                if (is_array($unpacked)) {
                    $values[] = '0x' . $unpacked['hex'];
                }
            }
        }
    }

    $valuesStr = '(' . implode('),(', $values) . ')';

    return "select * FROM (values {$valuesStr}) as x(propID)";
}

function dbDeleteOlderQueueEmail()
{
    global $dbh;
    $dbh->selectDatabase(SYSTEMDB);
    $sql = '
    DELETE FROM queue_email_attachments WHERE queue_email_id IN (SELECT id FROM queue_email WHERE sending_schedule_date < DATEADD(DAY, -10, GETDATE()) AND sent_at IS NOT NULL);
    DELETE FROM queue_email_cc WHERE queue_email_id IN (SELECT id FROM queue_email WHERE sending_schedule_date < DATEADD(DAY, -10, GETDATE()) AND sent_at IS NOT NULL);
    DELETE FROM queue_email_recipient WHERE queue_email_id IN (SELECT id FROM queue_email WHERE sending_schedule_date < DATEADD(DAY, -10, GETDATE()) AND sent_at IS NOT NULL);
    DELETE FROM queue_email WHERE sending_schedule_date < DATEADD(DAY, -10, GETDATE()) AND sent_at IS NOT NULL;
  ';

    return $dbh->executeNonQuery($sql);
}

function dbUpdateQueuedEmailSentDetails($id)
{
    global $dbh;
    $dbh->selectDatabase(SYSTEMDB);
    $sql = "
    UPDATE queue_email SET sent_at = GETDATE(), status = 3 WHERE id = {$id} AND database_id = {$_SESSION['databaseID']}
  ";

    return $dbh->executeNonQuery($sql);
}

function dbUpdateQueuedEmailActivityLog($queueEmailId, $recipient, $email_log_id, $status)
{
    global $dbh;
    $dbh->selectDatabase(SYSTEMDB);
    $sql = "
    UPDATE queue_email_activity_log SET sent_at = GETDATE(), email_log_id = {$email_log_id}, status = {$status} WHERE recipient_email = '{$recipient}' AND queue_email_id = {$queueEmailId} AND sent_at IS NULL
  ";

    return $dbh->executeNonQuery($sql);
}

function dbUpdateErrorQueuedEmailActivityLog($queueEmailId, $recipient, $error_message)
{
    global $dbh;
    $dbh->selectDatabase(SYSTEMDB);
    $sql = "
    UPDATE queue_email_activity_log SET status = 2, error_message = '{$error_message}' WHERE recipient_email = '{$recipient}' AND queue_email_id = {$queueEmailId} AND sent_at IS NULL
  ";
    $dbh->executeNonQuery($sql);
}

function dbUpdateQueuedEmailRecipientStatus($recipient, $queue_email_id, $error_message)
{
    global $dbh;
    $dbh->selectDatabase(SYSTEMDB);
    $error_message_lc = 'null';
    $is_failed = 0;
    if ($error_message) {
        $error_message_lc = "'{$error_message}'";
        $is_failed = 1;
    }

    $sql = "
    UPDATE queue_email_recipient SET is_failed = {$is_failed}, error_message = " . $error_message_lc . " WHERE queue_email_id = {$queue_email_id} AND recipient_email = '{$recipient}'
  ";

    return $dbh->executeNonQuery($sql);
}

/**
 * @param  string  $fileName
 * @return bool
 */
function checkOwnerManagementAgreementForDownload($fileName)
{
    global $dbh, $clientDB;
    $dbh->selectDatabase($clientDB);
    $userID = intval($_SESSION['user_id']);
    $sql = 'SELECT COUNT(*) FROM pmow_owner owner
            INNER JOIN pmpr_property property
                ON owner.property_id = property.pmpr_prop
            INNER JOIN pmpm_p_management AS agreement
                ON agreement.pmpm_prop = owner.property_id
                AND agreement.pmpm_publish_to_owner = 1
            WHERE
                owner.user_id = ?
                AND property.pmpr_delete = 0
                AND pmpm_file_link = ?';
    $params = [$userID, $fileName];

    return $dbh->executeScalar($sql, $params);
}

/**
 * @param  string  $fileName
 * @return bool
 */
function checkOwnerPropertyInsuranceForDownload($fileName)
{
    global $dbh, $clientDB;
    $dbh->selectDatabase($clientDB);
    $userID = intval($_SESSION['user_id']);

    $sql = 'SELECT COUNT(*)
            FROM pmow_owner owner
            INNER JOIN pmpr_property property
                ON owner.property_id = property.pmpr_prop
            INNER JOIN pmin_p_insurance AS insurance
                ON insurance.pmin_prop = owner.property_id
                AND insurance.pmin_publish_to_owner = 1
            WHERE
                owner.user_id = ?
                AND property.pmpr_delete=0
                AND pmin_file_link = ?';

    $params = [$userID, $fileName];

    return $dbh->executeScalar($sql, $params);
}

/**
 * @param  string  $fileName
 * @return bool
 */
function checkOwnerLeaseInsuranceForDownload($fileName)
{
    global $dbh, $clientDB;
    $dbh->selectDatabase($clientDB);
    $userID = intval($_SESSION['user_id']);

    $sql = 'SELECT COUNT(*)
            FROM pmow_owner owner
            INNER JOIN pmpr_property property
                ON owner.property_id = property.pmpr_prop
            INNER JOIN pmle_lease lease
                ON property.pmpr_prop = lease.pmle_prop
            INNER JOIN pmin_l_insurance AS insurance
                ON insurance.pmin_prop = owner.property_id
                AND lease.pmle_lease = insurance.pmin_lease
                AND insurance.pmin_publish_to_owner = 1
            WHERE
                owner.user_id = ?
                AND property.pmpr_delete=0
                AND pmin_file_link = ?';

    $params = [$userID, $fileName];

    return $dbh->executeScalar($sql, $params);
}

/**
 * @param  string  $fileName
 * @return bool
 */
function checkOwnerLeaseGuaranteeForDownload($fileName)
{
    global $dbh, $clientDB;
    $dbh->selectDatabase($clientDB);
    $userID = intval($_SESSION['user_id']);

    $sql = 'SELECT COUNT(*)
            FROM pmow_owner owner
            INNER JOIN pmpr_property property
                ON owner.property_id = property.pmpr_prop
            INNER JOIN pmle_lease lease
                ON property.pmpr_prop = lease.pmle_prop
            INNER JOIN pmgu_l_guarantee AS guarantee
                ON guarantee.pmgu_prop = owner.property_id
                AND lease.pmle_lease = guarantee.pmgu_lease
                AND guarantee.pmgu_publish_to_owner = 1
            WHERE
                owner.user_id = ?
                AND property.pmpr_delete = 0
                AND pmgu_file_link = ?';

    $params = [$userID, $fileName];

    return $dbh->executeScalar($sql, $params);
}

<?php

declare(strict_types=1);

/**
 * The main session class
 */
class Session
{
    public const SESSION_STARTED = true;

    public const SESSION_NOT_STARTED = false;

    public array $items = [];

    private bool $sessionState = self::SESSION_NOT_STARTED;

    private static ?Session $instance = null;

    public function exists(): bool
    {
        return session_id() !== '';
    }

    public function __construct()
    {
        if ($_SERVER['HTTP_HOST'] === parse_url(HTTPHOST, PHP_URL_HOST)) {
            $this->set('referrer', HTTPHOST . $_SERVER['SCRIPT_NAME'] . '?' . $_SERVER['QUERY_STRING']);
        } else {
            $this->drop('referrer');
        }
        $this->drop('queries');
    }

    public function bindAttributesFrom($object): void
    {
        foreach ($object as $key => $val) {
            $this->items[$key] = $val;
        }
    }

    public function destroy(): bool
    {
        if ($this->sessionState == self::SESSION_STARTED) {
            $this->sessionState = ! session_destroy();
            unset($_SESSION);

            return ! $this->sessionState;
        }

        return false;
    }

    public function set(string $key, $value): void
    {
        $this->items[$key] = $value;
    }

    public function get(string $key)
    {
        return (isset($this->items[$key])) ? $this->items[$key] : null;
    }

    public function drop(string $key): void
    {
        if (isset($this->items[$key])) {
            unset($this->items[$key]);
        }
    }

    public function clear(): void
    {
        session_unset();
    }

    public static function getInstance(): self
    {
        if (! isset(self::$instance)) {
            self::$instance = new self();
        }

        self::$instance->items = $_SESSION;
        self::$instance->startSession();

        return self::$instance;
    }

    /**
     * (Re)starts the session.
     *
     * @return void if the session has been initialised
     */
    private function startSession(): void
    {
        if ($this->sessionState == self::SESSION_NOT_STARTED) {
            $this->sessionState = session_start();
        }

    }

    public function loggedIn(): bool
    {
        return $this->exists() && $this->get('registered');
    }

    public function getClientDB(): ?string
    {
        return $this->get('currentDB');
    }

    public function getClientDSN(): ?string
    {
        return COREDSN . $this->get('currentDB');
    }
}

<?php

class Template
{
    public array $items = [];

    public string $file;

    public function __construct(string $file)
    {
        $this->file = $file;
        // help links
        $this->items['HELPLINKS'] = unserialize(HELPLINKS, ['allowed_classes' => false]);
    }

    public function getFile(): string
    {
        return $this->file;
    }

    public function bindAttributesFrom(&$object)
    {
        $config = HTMLPurifier_Config::createDefault();
        $htmlPurifier = new HTMLPurifier($config);
        // do not purify
        $bypassPurify = [
            'letterContent',
            'pdfContent',
            'letterEmailBody',
            'letterTemplateBody',
        ];

        if (is_array($object)) {
            foreach ($object as $key => $val) {
                if (in_array($key, $bypassPurify)) {
                    $this->items[$key] = $val;
                } elseif (! is_array($val) && ! is_object($val)) {
                    $this->items[$key] = substr(html_entity_decode($htmlPurifier->purify($val . ' ')), 0, -1);
                } else {
                    $this->items[$key] = $val;
                }
            }
        }
    }

    public function clear($object)
    {
        if (is_array($object)) {
            foreach ($object as $key) {
                if (isset($this->items[$key])) {
                    unset($this->items[$key]);
                }
            }
        }
    }

    public function _render()
    {
        $var = & $this->items;
        echo "
            <script>
                var sso_key = '" . $_SESSION['sso_key'] . "';
                var sso_enabled = '" . SSO_ENABLED . "';
                var sso_url = '" . sso_check . "';
                var sso_logout = '" . logout_url . "';
            </script>
        ";
        require $this->file;
    }

    public function renderString()
    {
        $var = & $this->items;
        require $this->file;
    }

    public function render()
    {
        $this->_render();
        $this->setTitleOfPage();
    }

    public function toString()
    {
        ob_start();
        //		$this->_render();
        $this->renderString();

        return ob_get_clean();
    }

    public function __toString()
    {
        ob_start();
        //		$this->_render();
        $this->renderString();

        return ob_get_clean();
    }

    public function setTitleOfPage()
    {
        $new_title = 'Cirrus8: Online Commercial Property Management Solution';
        $code_description = '';
        $company_code = $this->sanitizeCode(['companyID', 'companyCode', 'company_code']);
        $property_code = $this->sanitizeCode(['propertyID', 'propertyCode', 'property_code', 'pid']);
        $lease_code = $this->sanitizeCode(['leaseID', 'leaseCode', 'lease_code']);
        $check_param = $this->checkIfTitleChangeBasedOnParameter(
            $this->items['module'],
            $this->items['command'],
            $this->items['action']
        );

        if ($check_param) {
            if ($company_code) {
                $holder_details = dbGetCompany($company_code) ?? dbGetTempSharedCompany($company_code);
                $company_name = $holder_details['companyName'];
                $code_description = $company_code . $company_name !== '' ? ' - ' . $company_name . ' - ' : ' ';
                if (! $company_name) {
                    $code_description = '';
                }
            }

            if ($lease_code) {
                $property_name = '';
                $lease_name = '';
                $holder_details = dbGetLeaseName($property_code, $lease_code) ?? dbGetTempLeaseName(
                    $property_code,
                    $lease_code
                );
                if ($holder_details) {
                    $lease_name = $holder_details;
                    $code_description = $code_description . $lease_code . '/' . $property_code . ' - ' . $lease_name . ' - ';
                }
            } elseif ($property_code) {
                $property_name = '';
                $holder_details = dbGetPropertyName($property_code) ?? dbGetTempPropertyName($property_code);
                if ($holder_details) {
                    $property_name = $holder_details . ' - ';
                } else {
                    $property_code = '';
                }

                if ($property_name !== '' && $property_name !== '0') {
                    $code_description = $code_description . $property_code . ' - ' . $property_name;
                }
            }
        }

        $link = 'module=' . $this->items['module'] . '&command=' . $this->items['command'];
        $current = dbGetPageRouteByUserSubgroupAndLink($_SESSION['user_level'], $link);
        if ($code_description === '' && $current['name']) {
            $code_description = $current['name'] . ' - ';
        }

        if ($current['name'] || $check_param) {
            $new_title = $code_description . $new_title;
            $sanitized_title = json_encode($new_title, JSON_HEX_TAG);
            echo "<script>document.title = {$sanitized_title};</script>";
        }
    }

    public function sanitizeCode($code_arr)
    {
        $parameter_code = '';
        foreach ($code_arr as $row_data) {
            if (isset($this->items[$row_data]) && $this->items[$row_data]) {
                $parameter_code = $this->items[$row_data];
            }
        }

        return $parameter_code;
    }

    public function checkIfTitleChangeBasedOnParameter($module, $command, $action): bool
    {
        $title_change_param_list = [
            ['module' => 'properties', 'command' => 'home', 'action' => []],
            ['module' => 'properties', 'command' => 'view', 'action' => []],
            ['module' => 'properties', 'command' => 'viewProperty', 'action' => []],
            ['module' => 'leases', 'command' => 'home', 'action' => []],
            ['module' => 'companies', 'command' => 'company_v2', 'action' => []],
            ['module' => 'companies', 'command' => 'company', 'action' => []],
        ];
        foreach ($title_change_param_list as $row_data) {
            if ($module == $row_data['module'] && $command == $row_data['command'] && (count(
                $row_data['action']
            ) === 0 || in_array($action, $row_data['action']))) {
                return true;
            }
        }

        return false;
    }
}

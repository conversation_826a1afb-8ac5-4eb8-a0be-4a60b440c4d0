<?php

include SYSTEMPATH . '/data/shared/dbQueue.php';

class Queue
{
    public $_apiURL = 'https://client.cirrus8.com.au/framework/index.php?';          // uncomment if LIVE

    public $_type;

    public $_suffix = '';

    public $_list;

    public $_currentID;

    public $_timeframe = 300;

    public $totalItems;

    public function __construct($type, $apiURL = null, $suffix = '')
    {
        $this->_type = $type;
        $this->_suffix = $suffix;
        if ($apiURL) {
            $this->_apiURL = $apiURL;
        }
    }

    public function encode($data)
    {
        if ($data) {
            return base64_encode(gzdeflate(serialize($data), 9));
        }



    }

    public function decode($data)
    {
        if ($data) {
            return unserialize(gzinflate(base64_decode($data)), ['allowed_classes' => false]);
        }



    }

    public function add($clientID, $user, $command, $data)
    {
        dbAddQueueItem($this->_type, $clientID, $user, $command, $this->encode($data));
    }

    public function fetch($threshold = 5)
    {
        $this->_list = dbGetQueueItemsByType($threshold, $this->_timeframe, $this->_type, $this->_suffix);
        $this->totalItems = ($this->_list) ? (count($this->_list)) : 0;

        return $this->_list;
    }

    public function next($threshold = 5)
    {
        $next = dbGetNextQueueItemByType($threshold, $this->_timeframe, $this->_type, $this->_suffix);

        if ($next) {
            $next['data'] = $this->decode($next['data']);
            $this->_currentID = $next['queueID'];
            $this->increment();

            return $next;
        } else {
            $this->_currentID = null;

            return;
        }
    }

    public function delete($id = null)
    {
        if ($id) {
            return dbDeleteQueueItem($id, $this->_suffix);
        }

        if ($this->_currentID) {
            return dbDeleteQueueItem($this->_currentID, $this->_suffix);
        }

        $this->totalItems--;

    }

    public function increment()
    {
        if ($this->_currentID) {
            return dbIncrementQueueItem($this->_currentID, $this->_suffix);
        }

        return false;
    }

    public function add_error_code($error_code)
    {
        if ($this->_currentID) {
            return dbErrorCodeQueueItem($this->_currentID, $this->_suffix, $error_code);
        }

        return false;
    }
}

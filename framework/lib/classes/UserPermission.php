<?php

class UserPermission
{
    public $super;

    public $roles;

    public function __construct()
    {
        $this->super = &$_SESSION['super'];
    }

    public function userHasPageAccess(): bool
    {
        if (isset($_REQUEST['from_api'])) {
            return true;
        }

        $user_sub_group = $_SESSION['user_level'];
        $link           = 'module=' . $_REQUEST['module'] . '&command=' . $_REQUEST['command'];
        $link           = str_replace("'", '', $link);

        $page = dbGetPageRouteByUserSubgroupAndLink($user_sub_group, $link, false);
        $category = dbGetPageCategoryByUserSubgroupAndLink($user_sub_group, $link);

        $redirect = SYSTEMURL . '/index.php';

        if ($_SESSION['user_type'] == 'O'  && ownerMenuPermission($_REQUEST['command'])) {
            return true;
        }

        // CHECK IF HAS SUPER ROLE ACCESS
        if (defined('SUPER_USER_CODES')) {
            foreach (SUPER_USER_CODES as $code) {
                $super_role_name = strtolower('super_' . $code);

                if ((defined(strtoupper($super_role_name)) && in_array($page['slug'], constant(strtoupper($super_role_name)))) &&
                    (! isset($_SESSION[$super_role_name]) || $_SESSION[$super_role_name] != 1)
                ) {
                    header("Location: {$redirect}");
                    exit();
                }

                return true;
            }

            return true;
        }

        if (empty(dbGetPageRouteByLink($link))) {
            return true;
        }

        if (! $page && ! $category) {
            header("Location: {$redirect}");
            exit();
        }

        if ($page && $page['admin'] == 1 && ! $this->super) {
            header("Location: {$redirect}");
            exit();
        }

        return true;
    }
}

<?php

class MasterPage extends Template
{
    public string $title;

    public string $section;

    public string $controlContainer;

    public string $branch;

    public string $currentTab;

    private string $_directory;

    public function __construct(string $branch, string $file, string $main_folder = 'views')
    {
        global $context;

        if (! $main_folder) {
            $main_folder = 'views';
        }

        $this->branch = $branch;
        $this->_directory = '../framework/' . $main_folder . $branch . $file;
        parent::__construct($main_folder . $this->branch . $file);
    }

    public function setSection($section = '')
    {
        $this->section = strtolower($section);
    }

    public function setContainer($controlContainer = false)
    {
        $this->controlContainer = $controlContainer;
    }

    public function setCurrentTab($currentTab = '')
    {
        $this->currentTab = strtolower($currentTab);
    }

    public function render($drkmd = true)
    {
        $file_exists = file_exists($this->_directory);
        if (! $file_exists) {
            $redirect = SYSTEMURL . '/index.php';

            header("Location: {$redirect}");
            exit();
        }

        global $sess;
        $this->items['Master::Section'] = $this->section;
        $this->items['Master::Body'] = parent::toString();
        $this->items['Master::ControlContainer'] = $this->controlContainer;
        $this->items['Master::CurrentTab'] = $this->currentTab ?? $this->section;
        $mp = new Template('views' . $this->branch . '/master_layout.html');

        // FOR WORK TRACC LINK IF IT WILL GO TO OLD WORK TRACC OR CIRRUSFM
        $this->items['useDbCirrusFM'] = 0;
        $db = dbGetDatabase($sess->get('databaseID'));
        if (($sess->get('user_type') == 'C' || $sess->get('user_type') == 'A') && $sess->get('databaseID') && is_array(
            $db
        )) {
            //            foreach ($sess->get('dbList') as $db) {
            if ($db['cirrusfm'] == 1) {
                $this->items['useDbCirrusFM'] = $db['database_name'];
            }

            //            }
        }

        $mp->items = $this->items;

        // MAKE CIRRUS8 STANDARD MODE
        if ($this->items['module'] == 'configuration' && $this->items['command'] == 'manageLetters') {
            echo '<!doctype html>';
        } elseif ($this->items['module'] == 'administration' && $this->items['command'] == 'letter') {
            echo '<!doctype html>';
        } elseif ($this->items['module'] == 'administration' && $this->items['command'] == 'letter_v2') {
            echo '<!doctype html>';
        } elseif ($this->items['module'] == 'leases' && $this->items['command'] == 'chargeReview_v2') {
            echo '<!doctype html>';
        }

        echo "
            <script>
                localStorage.setItem('Authentication', '" . $_SESSION['sysApi'] . "');
                sessionStorage.setItem('sso_key', '" . $_SESSION['sso_key'] . "');
                sessionStorage.setItem('user_name', '" . $_SESSION['user_name'] . "');

                var token = localStorage.getItem('Authentication');
                var cirrus8ApiUrl = '" . c8_api . "';
                var assetDomain = '" . ASSET_DOMAIN . "';
                localStorage.setItem('cirrus8ApiUrl', cirrus8ApiUrl);
                localStorage.setItem('cirrus8_api_url', cirrus8ApiUrl);

                var user_type = '" . $_SESSION['user_type'] . "';
                var http_host = '" . $_SERVER['HTTP_HOST'] . $_SERVER['PHP_SELF'] . "';


                localStorage.setItem('user_type', user_type);


                localStorage.setItem('http_host', http_host);

                var user_level = '" . $_SESSION['user_level'] . "';
                localStorage.setItem('user_level', user_level);

                window.addEventListener('storage', storageEventHandler, false);
                function storageEventHandler(evt){
                    switch(evt.key){
                        case 'user_level':
                            //location.reload();
                            location.href = 'index.php';
                        break;
                        case 'currentDBID':
                            //location.reload();
                            location.href = 'index.php';
                        break;
                    }
                }

                var dark_mode_setting = '" . json_encode($drkmd) . "';
                localStorage.setItem('dark_mode_setting', dark_mode_setting);
            </script>
        ";

        $mp->render();
    }
}

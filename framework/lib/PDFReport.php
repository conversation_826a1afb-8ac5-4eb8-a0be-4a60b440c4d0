<?php

class PDFobject
{
    public $fonts = [];

    public function bindAttributesFrom(&$dataSet)
    {
        if (count($dataSet ?? []) > 0) {
            foreach ($dataSet as $key => $value) {
                $this->$key = $value;
            }
        }
    }

    // -- used for rendering dynamic content to a PDF (usually text)
    public function render(&$pdf) {}

    // -- used for rendering static content to a PDF - this is called by prepare page within the PDF Report and compiled into a template object
    public function preRender(&$pdf) {}

    public function setFont(&$pdf, $name, $size)
    {
        $fontHandle = $pdf->load_font($name, 'host', PDF_FONT_SETTINGS);
        if ($fontHandle) {
            $pdf->setFontExt($fontHandle, $size);
        }
    }

    public function setOption(&$pdf, $size)
    {
        //        $pdf->set_option("FontOutline={OCR=C:\Windows\Fonts\OCRB.ttf}");
        //        $font = $pdf->load_font('OCR', 'host', '');
        //        $pdf->setFontExt ($font, $size);
    }
}

class PDFresource {}

function getPageSize($pageSize)
{
    $page = new stdClass();
    switch ($pageSize) {
        case A4_PORTRAIT:
            $page->width = 595;
            $page->height = 842;
            break;
        case A4_LANDSCAPE:
            $page->width = 842;
            $page->height = 595;
            break;
    }

    return $page;
}

define('PDF_MASTER', 1);
define('PDF_SLAVE', 2);


if (! defined('REPORT_MONEY')) {
    define('REPORT_MONEY', 1);
}

if (! defined('REPORT_DECIMAL')) {
    define('REPORT_DECIMAL', 2);
}

if (! defined('REPORT_PERCENTAGE')) {
    define('REPORT_PERCENTAGE', 3);
}

if (! defined('REPORT_AREA')) {
    define('REPORT_AREA', 4);
}

if (! defined('REPORT_INTEGER')) {
    define('REPORT_INTEGER', 5);
}

class PDFDataReport extends PDFReport
{
    public $pdf;

    public $lineOffset;

    public $pageCount;

    public $multiLine;

    public $resourceList;

    public $data;

    public $columns = [];

    public $subheader = [];

    public $keyheader = [];

    public $_keyHeaderHeight = 0;

    public $pageSize = A4_PORTRAIT;

    public $printRowLines = false;

    public $printColumnLines = true;

    public $printBorders = true;

    public $startPosition;

    public $logoFile;

    public $height;

    public $lineHeight;

    public $pageNumbers = true;

    public $verifyColumns = false;

    public $checkList = [];

    public $lineWidth = 0.3;

    public $vMargin = 5;

    public $hMargin = 5;

    public $fontSize = 7;

    public $padding = 3;

    public $header = 100;

    public $footer = 75;

    public $assignedWidth;

    public $availableWidth;

    public $keyHeader = 1;

    public $renderPageNumberFontSize = 5;

    public $doctype = '';

    public $limitedShade = false;

    // -- creates a new PDF - dataSource can be another report or a filename
    public function __construct(&$dataSource, $logoFile = null, $pageSize = null)
    {
        if ($pageSize) {
            $this->pageSize = $pageSize;
        }

        $page = getPageSize($this->pageSize);
        parent::__construct($dataSource, $page->width, $page->height);
        if ($logoFile) {
            $this->logoFile = $logoFile;
        }

        $this->_setWorkingArea();
        $this->_setFontSize($this->fontSize);
    }

    public function generateImage($page = 'portrait', $x = 10, $y = 550, $maxWidth = 200, $maxHeight = 200, $file = '')
    {
        global $pdf;
        if (! file_exists($file)) {
            return;
        }

        $yoffset = 0;
        $pdfimage = $this->pdf->load_image('auto', $file, '');
        $this->pdf->fit_image(
            $pdfimage,
            $x,
            $y -= $yoffset,
            'boxsize {' . "{$maxWidth} {$maxHeight}" . '} position={center} fitmethod=meet'
        );
    }

    public function _setFontSize($fontSize)
    {
        $this->fontSize = $fontSize;
        $this->lineHeight = $this->fontSize + ($this->padding * 2);
    }

    public function _setWorkingArea()
    {
        $this->startPosition = $this->pageHeight - $this->header;
        $this->height = $this->pageHeight - ($this->header + $this->footer);
        $this->availableWidth = $this->pageWidth - ($this->hMargin * 2);
    }

    public function _format($value, $format)
    {
        // if ($value) {
        switch ($format) {
            case REPORT_MONEY:
                $value = toMoney($value);
                break;
            case REPORT_DECIMAL:
                $value = toDecimal($value);
                break;
            case REPORT_INTEGER:
                $value = toDecimal($value, 0);
                break;
            case REPORT_PERCENTAGE:
                $value = round($value, 1);
                break;
        }

        // }
        return $value;
    }

    public function setHeaderHeight($height)
    {
        $this->header = $height;
        $this->setWorkingArea();
    }

    public function setFooterHeight($height)
    {
        $this->footer = $height;
        $this->setWorkingArea();
    }

    public function addColumn($key, $name, $width, $alignment, $format = null, $headerStyle = null, $overwidth = '')
    {
        // Enjo - $headerStyle was added to prevent conflict on excel's function
        //        $this->setFont("Helvetica-Bold", 5);
        if ($this->assignedWidth + $width + ($this->padding * 2) <= $this->availableWidth) {
            if ($this->verifyColumns) {
                if ($this->checkList[$key]) {
                    $column = new stdClass();
                    $column->overwidth = $width;
                    $column->width = $width;
                    if ($overwidth) {
                        $column->overwidth = $overwidth;
                    }

                    $column->name = $name;

                    $column->alignment = $alignment;
                    $column->format = $format;
                    $this->columns[$key] = $column;
                    $this->assignedWidth += $width + ($this->padding * 2);

                    return true;
                } else {
                    return false;
                }
            } else {
                $column = new stdClass();
                $column->overwidth = $width;
                $column->width = $width;
                if ($overwidth) {
                    $column->overwidth = $overwidth;
                }

                $column->name = $name;
                $column->alignment = $alignment;
                $column->format = $format;
                $this->columns[$key] = $column;
                $this->assignedWidth += $width + ($this->padding * 2);

                return true;
            }
        } else {
            return false;
        }
    }

    public function resetColumns()
    {
        $this->assignedWidth = 0;
        $this->columns = [];
    }

    public function addKeyHeaderItem($key, $position, $width, $alignment, $value = null)
    {
        $column = new stdClass();
        $column->width = $width;
        $column->position = $position;
        $column->alignment = $alignment;
        $column->value = $value;
        $this->keyheader[$key] = $column;
    }

    public function addKeyHeaders($row, $keyHeader = 1)
    {
        $this->keyHeader = $keyHeader;

        $position = 0;
        foreach ($this->columns as $key => $column) {
            $k = $column;
            $k->value = $row[$key];
            $k->position = $position;
            $position += (($column->width * 1) + ($this->padding * 2));
            if ($row[$key]) {
                $this->keyheader[$key] = $k;
            }
        }
    }

    public function setKeyHeaderValue($key, $value)
    {
        $this->keyheader[$key]->value = $value;
    }

    public function addSubHeaderItem($key, $position, $width, $alignment)
    {
        $column = new stdClass();
        $column->width = $width;
        $column->position = $position;
        $column->alignment = $alignment;
        $this->subheader[$key] = $column;
    }

    public function setSubHeaderValue($key, $value)
    {
        $this->subheader[$key]->value = $value;
    }

    public function _checkLineOffset($offset)
    {
        if (($this->lineOffset + $offset) >= $this->height) {
            $this->endPage();
            $this->preparePage();
            $this->render();
        }
    }

    public function _updateLineOffset($offset)
    {
        $this->lineOffset += $offset;
    }

    public function updateLineOffset($offset)
    {
        $this->_checkLineOffset($offset);
        $this->_updateLineOffset($offset);
    }

    public function _hr($yPos, $startX, $endX, $color = 0.75, $lineWidth = '')
    {
        $this->pdf->setColorExt('both', 'rgb', $color, $color, $color, 0);
        $this->pdf->setlinewidth($lineWidth == '' ? $this->lineWidth : $lineWidth);
        $this->pdf->moveto($startX, $yPos);
        $this->pdf->lineto($endX, $yPos);
        $this->pdf->stroke();

        // set back after use
        $this->pdf->setlinewidth($this->lineWidth);
    }

    public function _vr($xPos, $startY, $endY, $color = 0.75)
    {
        $this->pdf->setColorExt('both', 'rgb', $color, $color, $color, 0);
        $this->pdf->setlinewidth($this->lineWidth);
        $this->pdf->moveto($xPos, $startY);
        $this->pdf->lineto($xPos, $endY);
        $this->pdf->stroke();
    }

    public function hr($yPos, $color = 0.75, $lineWidth = '')
    {
        $shadeWidth = 0;
        foreach ($this->columns as $column) {
            $shadeWidth += $column->width + $this->padding + 3;
        }

        if ($this->limitedShade) {
            $this->_hr($yPos, $this->hMargin, $shadeWidth + 20, $color, $lineWidth);
        } else {
            $this->_hr($yPos, $this->hMargin, $this->pageWidth - $this->hMargin, $color, $lineWidth);
        }
    }

    public function vr($xPos, $color = 0.75)
    {
        $this->_vr($xPos, $this->startPosition, $this->startPosition - $this->height, $color);
    }

    public function renderTable()
    {
        if ($this->printBorders) {
            $this->hr($this->startPosition);
            $this->hr($this->startPosition - $this->height);
            $xPos = $this->hMargin;
            foreach ($this->columns as $column) {
                if ($this->printColumnLines) {
                    $this->vr($xPos);
                }

                $xPos += $column->width + ($this->padding * 2);
            }

            $this->vr($this->hMargin);
            $this->vr($this->pageWidth - $this->hMargin);
        }
    }

    // -- erases lines drawn on the final page - bit of a hack
    public function clean()
    {
        if ($this->printBorders) {
            $this->hr($this->startPosition - $this->height, 1);
            $xPos = $this->hMargin;
            foreach ($this->columns as $column) {
                $this->_vr($xPos, $this->startPosition - $this->lineOffset, $this->startPosition - $this->height, 1);
                $xPos += $column->width + ($this->padding * 2);
            }

            $this->_vr(
                $this->hMargin,
                $this->startPosition - $this->lineOffset,
                $this->startPosition - $this->height,
                1
            );
            $this->_vr(
                $this->pageWidth - $this->hMargin,
                $this->startPosition - $this->lineOffset,
                $this->startPosition - $this->height,
                1
            );
        }
    }

    public function renderHeader($background = [0, 0.3, 0.5])
    {
        $xPos = $this->hMargin;
        // $this->pdf->setColorExt("both", "rgb",0.1, 0.1, 0.1);
        $this->pdf->setColorExt('both', 'rgb', $background[0], $background[1], $background[2], 0);
        $shadeWidth = 0;
        foreach ($this->columns as $column) {
            $shadeWidth += $column->width + $this->padding + 3;
        }

        if ($this->limitedShade) {
            $this->pdf->rect($this->hMargin, $this->startPosition, $shadeWidth + 20, $this->lineHeight);
        } else {
            $this->pdf->rect(
                $this->hMargin,
                $this->startPosition,
                $this->pageWidth - ($this->hMargin * 2),
                $this->lineHeight
            );
        }

        $this->pdf->fill();

        $this->pdf->setColorExt('both', 'rgb', 1, 1, 1, 0);
        $this->setFont('Helvetica-Bold', $this->fontSize);

        foreach ($this->columns as $column) {
            if (strstr($column->name, "\n")) {
                $this->setFont('Helvetica-Bold', 5);
                $xPos += $this->padding;
                $this->pdf->showBoxed(
                    $column->name,
                    $xPos,
                    $this->startPosition - $this->padding,
                    $column->width,
                    $this->lineHeight + 2,
                    ($column->alignment2 ? $column->alignment2 : $column->alignment),
                    ''
                );
                $xPos += $column->width + ($this->padding);
            } else {
                $this->setFont('Helvetica-Bold', $this->fontSize);
                $xPos += $this->padding;
                $this->pdf->showBoxed(
                    $column->name,
                    $xPos,
                    $this->startPosition - $this->padding,
                    $column->width,
                    $this->lineHeight,
                    ($column->alignment2 ? $column->alignment2 : $column->alignment),
                    ''
                );
                $xPos += $column->width + ($this->padding);
            }

            // if (!$column->width || !$column->sequence || !$column->alignment) die('please check the column definitions for this report');

        }
    }

    public function renderPageNumber()
    {
        $this->pdf->setColorExt('both', 'rgb', 0.3, 0.3, 0.3, 0);
        $this->setFont('Helvetica-Bold', $this->renderPageNumberFontSize);

        if ($this->doctype) {
            $this->pdf->showBoxed('Page ' . $this->pageCount, 750, 0, 75, 30, 'right', '');

            switch ($this->pageSize) {
                case A4_PORTRAIT:
                    $width = 595;
                    break;
                case A4_LANDSCAPE:
                    $width = 842;
                    break;
            }


            $date = date('Ymd\\\Hi');
            $this->pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
            $this->setFont('Helvetica', 6);
            $this->pdf->showBoxed('', 30 + 10, 3, 400, 13, 'left', '');
            $this->pdf->showBoxed('Powered by Cirrus8 Software', 170 + 10, 3, $width - 350, 13, 'center', '');
            $this->pdf->showBoxed("cirrus8\\{$date}\\{$this->doctype}", 100 + 10, 3, $width - 150, 13, 'right', '');
        } else {
            $width = 70;
            $this->pdf->showBoxed(
                'Page ' . $this->pageCount,
                $this->pageWidth - $this->hMargin - $width - 7,
                $this->hMargin,
                $width,
                8,
                'right',
                ''
            );
        }
    }

    public function renderSubHeader($color = 0.95, $size = 1, $text = [0, 0, 0])
    {
        $xPos = $this->hMargin + $this->padding;
        $lineHeight = $this->lineHeight + $size;
        $this->_checkLineOffset($lineHeight);
        $this->pdf->setColorExt('both', 'rgb', $color, $color, $color, 0);
        $shadeWidth = 0;
        foreach ($this->columns as $column) {
            $shadeWidth += $column->width + $this->padding + 3;
        }

        if ($this->limitedShade) {
            $this->pdf->rect(
                $this->hMargin,
                $this->startPosition - ($this->lineOffset + $lineHeight),
                $shadeWidth + 20,
                $lineHeight
            );
        } else {
            $this->pdf->rect(
                $this->hMargin + $this->lineWidth,
                $this->startPosition - ($this->lineOffset + $lineHeight),
                $this->pageWidth - ($this->hMargin * 2) - ($this->lineWidth * 2),
                $lineHeight
            );
        }

        $this->pdf->fill();
        $this->hr($this->startPosition - ($this->lineOffset));
        $this->pdf->setColorExt('both', 'rgb', $text[0], $text[1], $text[2], 0);
        $this->setFont('Helvetica-Bold', $this->fontSize + $size);
        foreach ($this->subheader as $item) {
            $this->pdf->showBoxed(
                $item->value,
                $xPos + $item->position,
                $this->startPosition - ($this->lineOffset + $lineHeight + $this->padding),
                $item->width,
                $lineHeight,
                $item->alignment,
                ''
            );
        }

        $this->hr($this->startPosition - ($this->lineOffset + $lineHeight));
        $this->_updateLineOffset($lineHeight);
    }

    public function renderKeyHeader2($color = 0.98, $size = -1, $text = [0, 0, 0])
    {
        $xPos = $this->hMargin + $this->padding;
        $lineHeight = ($this->lineHeight + $size);
        $this->_checkLineOffset($lineHeight);
        $this->pdf->setColorExt('both', 'rgb', 0, 0.3, 0.5, 0);
        $this->pdf->rect(
            $this->hMargin + $this->lineWidth,
            $this->startPosition - ($this->lineOffset + $lineHeight),
            $this->pageWidth - ($this->hMargin * 2) - ($this->lineWidth * 2),
            $lineHeight
        );
        $this->pdf->fill();
        $this->hr($this->startPosition - ($this->lineOffset));
        $this->pdf->setColorExt('both', 'rgb', 1, 1, 1, 0);
        $this->setFont('Helvetica-Bold', $this->fontSize);
        foreach ($this->keyheader as $item) {
            $this->pdf->showBoxed(
                $item->value,
                $xPos + $item->position,
                $this->startPosition - ($this->lineOffset + $lineHeight + $this->padding),
                $item->width,
                $lineHeight,
                $item->alignment,
                ''
            );
        }

        $this->hr($this->startPosition - ($this->lineOffset + $lineHeight));
        $this->_updateLineOffset($lineHeight);
    }

    public function renderKeyHeader($color = 0.98, $size = -1, $text = [0, 0, 0])
    {
        $xPos = $this->hMargin + $this->padding;
        $lineHeight = ($this->lineHeight + $size);
        $this->_checkLineOffset($lineHeight);
        $this->pdf->setColorExt('both', 'rgb', $color, $color, $color, 0);
        $this->pdf->rect(
            $this->hMargin + $this->lineWidth,
            $this->startPosition - ($this->lineOffset + $lineHeight),
            $this->pageWidth - ($this->hMargin * 2) - ($this->lineWidth * 2),
            $lineHeight
        );
        $this->pdf->fill();
        $this->hr($this->startPosition - ($this->lineOffset));
        $this->pdf->setColorExt('both', 'rgb', $text[0], $text[1], $text[2], 0);
        $this->setFont('Helvetica', $this->fontSize + $size);
        foreach ($this->keyheader as $item) {
            $this->pdf->showBoxed(
                $item->value,
                $xPos + $item->position,
                $this->startPosition - ($this->lineOffset + $lineHeight + $this->padding),
                $item->width,
                $lineHeight,
                $item->alignment,
                ''
            );
        }

        $this->hr($this->startPosition - ($this->lineOffset + $lineHeight));
        $this->_updateLineOffset($lineHeight);
    }

    public function renderSubTotal($row, $color = 0.9, $size = 0)
    {
        $row = (array) $row;
        $lineHeight = $this->lineHeight + $size;
        $this->_checkLineOffset($lineHeight);
        $xPos = $this->hMargin;
        // -- draw the filled in box
        $this->pdf->setColorExt('both', 'rgb', $color, $color, $color, 0);

        $shadeWidth = 0;
        foreach ($this->columns as $key => $column) {
            $shadeWidth += $column->width + $this->padding + 3;
        }

        if ($this->limitedShade) {
            $this->pdf->rect(
                $this->hMargin,
                $this->startPosition - ($this->lineOffset + $lineHeight),
                $shadeWidth + 20,
                $lineHeight
            );
        } else {
            $this->pdf->rect(
                $this->hMargin + $this->lineWidth,
                $this->startPosition - ($this->lineOffset + $lineHeight),
                $this->pageWidth - ($this->hMargin * 2) - (2 * $this->lineWidth),
                $lineHeight
            );
        }

        $this->pdf->fill();
        $this->hr($this->startPosition - ($this->lineOffset));
        $this->hr($this->startPosition - ($this->lineOffset + $lineHeight));

        // -- print the columns
        $this->pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        $this->setFont('Helvetica-Bold', $this->fontSize + $size);
        foreach ($this->columns as $key => $column) {
            if (! isset($row['format'][$key]) && ! $row['format'][$key]) {
                $value = (is_object($row)) ? $this->_format($row->$key, $column->format) : $this->_format(
                    $row[$key],
                    $column->format
                );
            } else {
                $value = (is_object($row)) ? $this->_format($row->$key, $row['format'][$key]) : $this->_format(
                    $row[$key],
                    $row['format'][$key]
                );
            }

            $xPos += $this->padding;
            $this->pdf->showBoxed(
                $value . $row['last'][$key],
                $xPos,
                $this->startPosition - ($this->lineOffset + $lineHeight + $this->padding),
                ($row['width'][$key] ? $row['width'][$key] : $column->width),
                $lineHeight,
                ($row['align'][$key] ? $row['align'][$key] : $column->alignment),
                ''
            );
            $xPos += $column->width + $this->padding;
        }

        $this->_updateLineOffset($lineHeight);
    }

    public function renderLine($row, $shareColumn = false)
    {
        $row = (array) $row;
        // -- if its a multiline PDF - calculate the largest number of lines required for the offset
        $offset = $this->lineHeight;

        // -- check to make sure the row will fit on the page
        $this->_checkLineOffset($offset);
        $xPos = $this->hMargin;
        foreach ($this->columns as $key => $column) {
            if (! isset($row['format'][$key]) && ! $row['format'][$key]) {
                $value = (is_object($row)) ? $this->_format($row->$key, $column->format) : $this->_format(
                    $row[$key],
                    $column->format
                );
            } else {
                $value = (is_object($row)) ? $this->_format($row->$key, $row['format'][$key]) : $this->_format(
                    $row[$key],
                    $row['format'][$key]
                );
            }

            $xPos += $this->padding;

            if (in_array($key, ['CVariance%', 'YVariance%', 'FVariance%']) && $value && $shareColumn) {
                $color = '0.94';
                $this->pdf->setColorExt('both', 'rgb', $color, $color, $color, 0);
                $this->pdf->rect(
                    $xPos,
                    $this->startPosition - ($this->lineOffset + $offset + $this->padding) - ($row['offset'][$key]),
                    ($row['width'][$key] ? $row['width'][$key] : $column->width),
                    ($row['height'][$key] ? $row['height'][$key] : $offset)
                );
                $this->pdf->fill();
            }

            $this->pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
            $this->setFont('Helvetica', $this->fontSize);
            $this->pdf->showBoxed(
                $value . $row['last'][$key],
                $xPos,
                $this->startPosition - ($this->lineOffset + $offset + $this->padding) - ($row['offset'][$key]),
                ($row['width'][$key] ? $row['width'][$key] : $column->width),
                ($row['height'][$key] ? $row['height'][$key] : $offset),
                ($row['align'][$key] ? $row['align'][$key] : $column->alignment),
                ''
            );
            $xPos += $column->width + $this->padding;
        }

        if ($this->printRowLines) {
            $this->hr($this->startPosition - ($this->lineOffset + $offset), 0.90);
        }

        $this->_updateLineOffset($offset);
    }

    public function renderTenantWithReviews($row)
    {
        $row = (array) $row;
        // -- if its a multiline PDF - calculate the largest number of lines required for the offset
        $offset = $this->lineHeight;

        // -- check to make sure the row will fit on the page
        $this->_checkLineOffset($offset);
        $xPos = $this->hMargin;
        $this->pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        foreach ($this->columns as $key => $column) {
            if (in_array($key, ['Unit', 'Description'])) {
                $this->setFont('Helvetica-Bold', $this->fontSize);
            } else {
                $this->setFont('Helvetica', $this->fontSize);
            }


            $value = (is_object($row)) ? $this->_format($row->$key, $column->format) : $this->_format(
                $row[$key],
                $column->format
            );

            if ($key == 'Description') {
                $width = $column->width + $this->columns[1]->width + $this->columns[2]->width;
                $xPos += $this->padding;
                $this->pdf->showBoxed(
                    $value,
                    $xPos,
                    $this->startPosition - ($this->lineOffset + $offset + $this->padding),
                    $width,
                    $offset,
                    ($row['align'][$key] ? $row['align'][$key] : $column->alignment),
                    ''
                );
                $xPos += $width + $this->padding;
            } elseif (! in_array($key, [1, 2])) {
                $xPos += $this->padding;
                $this->pdf->showBoxed(
                    $value,
                    $xPos,
                    $this->startPosition - ($this->lineOffset + $offset + $this->padding),
                    $column->width,
                    $offset,
                    ($row['align'][$key] ? $row['align'][$key] : $column->alignment),
                    ''
                );
                $xPos += $column->width + $this->padding;
            }
        }

        if ($this->printRowLines) {
            $this->hr($this->startPosition - ($this->lineOffset + $offset), 0.90);
        }

        $this->_updateLineOffset($offset);
    }

    public function renderEmptyLine()
    {
        // -- if its a multiline PDF - calculate the largest number of lines required for the offset
        $offset = $this->lineHeight;

        // -- check to make sure the row will fit on the page
        $this->_checkLineOffset($offset);

        if ($this->printRowLines) {
            $this->hr($this->startPosition - ($this->lineOffset + $offset), 0.90);
        }

        $this->_updateLineOffset($offset);
    }

    // @ renderLineNotes @param array added by arjay for lease notes display
    public function renderLineNotes($row)
    {
        // -- if its a multiline PDF - calculate the largest number of lines required for the offset
        if ($this->multiLine) {
            $offset = $this->_calculateOffset($row) * ($this->fontSize) + ($this->padding * 2);
        } else {
            // -- otherwise use the single line height
            $offset = $this->lineHeight;
        }

        // -- check to make sure the row will fit on the page
        $this->_checkLineOffset($offset);
        $xPos = $this->hMargin;
        $this->pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        $this->setFont('Helvetica', $this->fontSize, 'host');

        foreach ($this->columns as $key => $column) {
            if (strtoupper($key) === 'TENANT' || strtoupper($key) === 'LEASEIDNAME') {
                $column->overwidth = '700';
                $value = $this->_format($row[$key], $column->format);
                $xPos += $this->padding;
                $this->pdf->showBoxed(
                    $value,
                    $xPos,
                    $this->startPosition - ($this->lineOffset + $offset + $this->padding),
                    $column->overwidth,
                    $offset,
                    $column->alignment,
                    ''
                );
                $xPos += $column->width + $this->padding;
            }
        }

        if ($this->printRowLines) {
            $this->hr($this->startPosition - ($this->lineOffset + $offset), 1);
        }

        $this->_updateLineOffset($offset);
    }

    // @ renderLineNotes2 @param array added by arjay for lease notes display
    public function renderLineNotes2($row)
    {
        // -- if its a multiline PDF - calculate the largest number of lines required for the offset
        if ($this->multiLine) {
            $offset = $this->_calculateOffset($row) * ($this->fontSize) + ($this->padding * 2);
        } else {
            // -- otherwise use the single line height
            $offset = $this->lineHeight;
        }

        // -- check to make sure the row will fit on the page
        $this->_checkLineOffset($offset);
        $xPos = $this->hMargin;
        $this->pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        $this->setFont('Helvetica', $this->fontSize, 'host');
        foreach ($this->columns as $key => $column) {
            if (strtoupper($key) === 'TENANT' || strtoupper($key) === 'LEASEIDNAME') {
                $column->overwidth = '700';
                $value = $this->_format($row[$key], $column->format);
                $xPos += $this->padding;
                $this->pdf->showBoxed(
                    $value,
                    $xPos,
                    $this->startPosition - ($this->lineOffset + $offset + $this->padding),
                    $column->overwidth,
                    $offset,
                    $column->alignment,
                    ''
                );
                $xPos += $column->width + $this->padding;
            }
        }

        if ($this->printRowLines) {
            $this->hr($this->startPosition - ($this->lineOffset + $offset), 0.9);
        }

        $this->_updateLineOffset($offset);
    }

    // @ renderLineRentReviews @param array added by arjay for rent reviews display
    public function renderLineRentReviews($row)
    {
        // -- if its a multiline PDF - calculate the largest number of lines required for the offset
        if ($this->multiLine) {
            $offset = $this->_calculateOffset($row) * ($this->fontSize) + ($this->padding * 2);
        } else {
            // -- otherwise use the single line height
            $offset = $this->lineHeight;
        }

        // -- check to make sure the row will fit on the page
        $this->_checkLineOffset($offset);
        $xPos = $this->hMargin;
        $this->pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        $this->setFont('Helvetica', $this->fontSize, 'host');
        foreach ($this->columns as $key => $column) {
            if (strtoupper($key) === 'TENANT' || strtoupper($key) === 'LEASEIDNAME') {
                $column->overwidth = '700';
                $value = $this->_format($row[$key], $column->format);
                $xPos += $this->padding;
                $this->pdf->showBoxed(
                    $value,
                    $xPos,
                    $this->startPosition - ($this->lineOffset + $offset + $this->padding),
                    $column->overwidth,
                    $offset,
                    $column->alignment,
                    ''
                );
                $xPos += $column->width + $this->padding;
            }
        }

        if ($row['notes'] > 0) {
            if ($this->printRowLines) {
                $this->hr($this->startPosition - ($this->lineOffset + $offset), 1);
            }

            $this->_updateLineOffset($offset);
        } else {
            if ($this->printRowLines) {
                $this->hr($this->startPosition - ($this->lineOffset + $offset), 0.9);
            }

            $this->_updateLineOffset($offset);
        }
    }

    public function insertImage($id, $logoPath, $maxWidth = 842, $maxHeight = 595, $x = 0, $y = 0)
    {
        $this->endPage();
        parent::preparePage(false);

        $pdfimage = $this->pdf->load_image('auto', $logoPath, '');
        $this->pdf->fit_image(
            $pdfimage,
            $x,
            $y,
            'boxsize {' . "{$maxWidth} {$maxHeight}" . '} position={center} fitmethod={meet}'
        );
        $this->renderPageNumber();
    }

    public function renderLine_bold($row)
    {
        $row = (array) $row;
        // -- if its a multiline PDF - calculate the largest number of lines required for the offset
        $offset = $this->lineHeight;
        // -- check to make sure the row will fit on the page
        $this->_checkLineOffset($offset);
        $xPos = $this->hMargin;
        $this->pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        $this->setFont('Helvetica-Bold', $this->fontSize);

        foreach ($this->columns as $key => $column) {
            if (strtoupper($key) === 'TENANT' || strtoupper($key) === 'LEASEIDNAME') {
                $column->overwidth = '700';

                $value = (is_object($row)) ? $this->_format($row->$key, $column->format) : $this->_format(
                    $row[$key],
                    $column->format
                );
                $xPos += $this->padding;
                $this->pdf->showBoxed(
                    $value,
                    $xPos,
                    $this->startPosition - ($this->lineOffset + $offset + $this->padding),
                    $column->width,
                    $offset,
                    $column->alignment,
                    ''
                );
                $xPos += $column->width + $this->padding;
            }
        }

        if ($this->printRowLines) {
            $this->hr($this->startPosition - ($this->lineOffset + $offset), 1);
        }

        $this->_updateLineOffset($offset);

        $this->setFont('Helvetica', $this->fontSize);
    }

    public function renderLine_custom($row)
    {
        $row = (array) $row;
        $this->setFont('Helvetica', $this->fontSize);
        $lineHeight = $this->lineHeight;

        if ($row['multiLine']) {
            $lineHeight = $this->_calculateOffset($row) * ($this->fontSize) + ($this->padding * 2);
        }

        $this->_checkLineOffset($lineHeight);
        $xPos = $this->hMargin;
        if ($row['bgcolor']) {
            // -- draw the filled in box
            $this->pdf->setColorExt('both', 'rgb', $row['bgcolor'][0], $row['bgcolor'][1], $row['bgcolor'][2], 0);
            $this->pdf->rect(
                $this->hMargin + $this->lineWidth,
                $this->startPosition - ($this->lineOffset + $lineHeight),
                $this->pageWidth - ($this->hMargin * 2) - (2 * $this->lineWidth),
                $lineHeight
            );
            $this->pdf->fill();
            $this->hr($this->startPosition - ($this->lineOffset));
            $this->hr($this->startPosition - ($this->lineOffset + $lineHeight));
        }

        // -- print the columns
        $this->pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);

        if ($row['bold']) {
            $this->setFont('Helvetica-Bold', $this->fontSize);
        } elseif ($row['italic']) {
            $this->setFont('Helvetica-Oblique', $this->fontSize);
        } else {
            $this->setFont('Helvetica', $this->fontSize);
        }

        foreach ($this->columns as $key => $column) {
            $value = (is_object($row)) ? $this->total($row->$key, $column->format) : $this->_format(
                $row[$key],
                $column->format
            );
            if ($row['format'][$key]) {
                $value = $this->_format($row->$key, $row['format'][$key]);
            }

            $xPos += $this->padding;
            $this->pdf->showBoxed(
                $value,
                $xPos,
                $this->startPosition - ($this->lineOffset + $lineHeight + $this->padding),
                ($row['width'][$key] ? $row['width'][$key] : $column->width),
                $lineHeight,
                $row['alignment'][$key] ? $row['alignment'][$key] : $column->alignment,
                ''
            );
            $xPos += $column->width + $this->padding;
        }

        $this->_updateLineOffset($lineHeight);

        $this->setFont('Helvetica', $this->fontSize);
    }

    public function renderData($data)
    {
        foreach ($data as $row) {
            $this->renderLine($row);
        }
    }

    public function renderData_custom($data)
    {
        foreach ($data as $row) {
            $this->renderLine_custom($row);
        }
    }

    // @ renderDataNotes @ param array added by arjay for lease notes and rent reviews display
    public function renderDataNotes($data, $includes)
    {
        foreach ($data as $row) {
            $row['includeLeaseNotes'] = $includes['includeLeaseNotes'];
            $row['includeRentReviews'] = $includes['includeRentReviews'];
            $row['merge'] = $includes['merge'];

            $this->renderLine($row);

            if ($includes['includeRentReviews'] != null && count($row['rr']) > 0) {
                $rentReviewData = '';
                if (count($row['rr']['rentData']) > 0) {
                    foreach ($row['rr']['rentData'] as $k => $v) {
                        $rentReviewData .= "{$v} - {$row['rr']['rentType'][$k]}    ";
                    }
                }

                $row['rr'] = "{$rentReviewData}";

                $this->renderLine_bold(['tenant' => 'Rent Reviews', 'leaseIDName' => 'Rent Reviews']);
                $this->renderLineRentReviews(
                    ['tenant' => $row['rr'], 'notes' => count($row['nt']), 'leaseIDName' => $row['rr']]
                );
            }

            if ($includes['includeLeaseNotes'] != null && count($row['nt']) > 0) {
                $this->renderLine_bold(['tenant' => 'Lease Notes', 'leaseIDName' => 'Lease Notes']);
                $kk = 1;
                foreach ($row['nt'] as $v) {
                    if (count($row['nt']) === $kk) {
                        $this->renderLineNotes2(['tenant' => $v, 'leaseIDName' => $v]);
                    } else {
                        $this->renderLineNotes(['tenant' => $v, 'leaseIDName' => $v]);
                    }

                    $kk += 1;
                }
            }
        }
    }

    public function renderLogo()
    {
        if ($this->logoFile && file_exists($this->logoFile)) {
            $maxWidth = LOGO_WIDTH;
            $maxHeight = LOGO_HEIGHT;
            [$imageWidth, $imageHeight] = getimagesize($this->logoFile);

            $horizontalScaling = ($imageWidth > $maxWidth) ? $maxWidth / $imageWidth : 1;
            $verticalScaling = ($imageHeight > $maxHeight) ? $maxHeight / $imageHeight : 1;
            $imageScale = ($horizontalScaling < $verticalScaling) ? $horizontalScaling : $verticalScaling;

            $imageScale = round($imageScale, 2);

            $hPos = $this->pageWidth - ($imageScale * $imageWidth) - $this->hMargin;
            $vPos = $this->pageHeight - ($imageScale * $imageHeight) - $this->vMargin;

            if (! $this->resourceList['logo']) {
                $this->resourceList['logo'] = $this->pdf->load_image('auto', $this->logoFile, '');
            }

            // $this->pdf->open_image_file
            $this->pdf->fit_image($this->resourceList['logo'], $hPos, $vPos, "scale {$imageScale}");
        }
    }

    public function preparePage($printTemplate = true)
    {
        $this->lineOffset = 0;
        parent::preparePage($printTemplate);
        $this->render();
        $this->renderLogo();
        $this->renderTable();
        $this->renderHeader();
        if (! empty($this->keyheader) && $this->keyHeader == 1) {
            $this->renderKeyHeader();
        }

        if (! empty($this->keyheader) && $this->keyHeader == 2) {
            $this->renderKeyHeader2();
        }

        if ($this->pageNumbers) {
            $this->renderPageNumber();
        }
    }

    public function newLine($x = 1)
    {
        $this->updateLineOffset($this->lineHeight * $x);
    }
}

class PDFDataMultiLineReport extends PDFDataReport
{
    public $pdf;

    public $lineOffset;

    public $_keyHeaderHeight = 0;

    public $multiLine = true;

    // -- creates a new PDF - dataSource can be another report or a filename
    public function __construct(&$dataSource, $logoFile = null, $pageSize = null)
    {
        parent::__construct($dataSource, $logoFile, $pageSize);
    }

    public function _calculateOffset($row)
    {
        $max = 0;
        foreach ($this->columns as $key => $item) {
            $count = 0;
            $lines = explode("\n", $row[$key]);
            if ($this->multiLine) {
                foreach ($lines as $line) {
                    $valueWordWrap = wordwrap(
                        $line,
                        ($row['width'][$key] ?? $item->overwidth) / ($this->fontSize - $this->padding),
                        '<br/>'
                    ); // echo $valueWordWrap . '<br/>';
                    $lines2 = explode('<br/>', $valueWordWrap);
                    $count2 = count($lines2 ?? []);
                    $count += $count2;
                }
            } else {
                $count = count($lines ?? []);
            }

            if ($count > $max) {
                $max = $count;
            }
        }

        return $max;
    }

    public function _calculateOffsetItem($item)
    {
        $lines = explode("\n", $item);

        return count($lines ?? []);
    }

    public function addKeyHeaders($row, $keyHeader = 1)
    {
        parent::addKeyHeaders($row);
        $this->_keyHeaderHeight = $this->_calculateOffset($row);
    }

    public function renderKeyHeader($color = 0.98, $size = -1, $text = [0, 0, 0])
    {
        $xPos = $this->hMargin + $this->padding;
        $lineHeight = ($this->lineHeight + $size) * $this->_keyHeaderHeight * 0.7;
        $this->_checkLineOffset($lineHeight);
        $this->pdf->setColorExt('both', 'rgb', $color, $color, $color, 0);
        $this->pdf->rect(
            $this->hMargin + $this->lineWidth,
            $this->startPosition - ($this->lineOffset + $lineHeight),
            $this->pageWidth - ($this->hMargin * 2) - ($this->lineWidth * 2),
            $lineHeight
        );
        $this->pdf->fill();
        $this->hr($this->startPosition - ($this->lineOffset));
        $this->pdf->setColorExt('both', 'rgb', $text[0], $text[1], $text[2], 0);
        $this->setFont('Helvetica', $this->fontSize + $size, 'host');
        foreach ($this->keyheader as $item) {
            $this->pdf->showBoxed(
                $item->value,
                $xPos + $item->position,
                $this->startPosition - ($this->lineOffset + $lineHeight + $this->padding),
                $item->width,
                $lineHeight,
                $item->alignment,
                ''
            );
        }

        $this->hr($this->startPosition - ($this->lineOffset + $lineHeight));
        $this->_updateLineOffset($lineHeight);
    }

    public function renderLine($row, $shareColumn = false)
    {
        // -- if its a multiline PDF - calculate the largest number of lines required for the offset
        if ($this->multiLine) {
            $offset = $this->_calculateOffset($row) * ($this->fontSize) + ($this->padding * 2);
        } else {
            // -- otherwise use the single line height
            $offset = $this->lineHeight;
        }

        // -- check to make sure the row will fit on the page
        $this->_checkLineOffset($offset);
        $xPos = $this->hMargin;
        $this->pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        $this->setFont('Helvetica', $this->fontSize, 'host');

        foreach ($this->columns as $key => $column) {
            $value = $this->_format($row[$key], $column->format);
            $xPos += $this->padding;
            $this->pdf->showBoxed(
                $value,
                $xPos,
                $this->startPosition - ($this->lineOffset + $offset + $this->padding),
                $column->overwidth,
                $offset,
                $column->alignment,
                ''
            );
            $xPos += $column->width + $this->padding;
        }

        if ($row['includeLeaseNotes'] == 1 && count($row['notes'] ?? []) > 0) {
            if ($this->printRowLines) {
                $this->hr($this->startPosition - ($this->lineOffset + $offset), 1);
            }

            // for lease notes added by arjay
            $this->_updateLineOffset($offset);
        } elseif ($row['includeRentReviews'] == 1 && count($row['rentReviews'] ?? []) > 0) {
            if ($this->printRowLines) {
                $this->hr($this->startPosition - ($this->lineOffset + $offset), 1);
            }

            // for lease notes added by arjay
            $this->_updateLineOffset($offset);
        } else {
            if ($this->printRowLines) {
                $this->hr($this->startPosition - ($this->lineOffset + $offset), 0.90);
            }

            $this->_updateLineOffset($offset);
        }
    }

    public function renderLineSub($row, $color = 0.9, $size = 0)
    {
        $row = (array) $row;
        $lineHeight = $this->lineHeight + 14;
        $this->_checkLineOffset($lineHeight);
        $xPos = $this->hMargin;
        // -- draw the filled in box
        $this->pdf->setColorExt('both', 'rgb', $color, $color, $color, 0);
        $this->pdf->rect(
            $this->hMargin + $this->lineWidth,
            $this->startPosition - ($this->lineOffset + $lineHeight),
            $this->pageWidth - ($this->hMargin * 2) - (2 * $this->lineWidth),
            $lineHeight
        );
        $this->pdf->fill();
        $this->hr($this->startPosition - ($this->lineOffset));
        $this->hr($this->startPosition - ($this->lineOffset + $lineHeight));


        // -- print the columns
        $this->pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
        $this->setFont('Helvetica-Bold', $this->fontSize + $size);
        foreach ($this->columns as $key => $column) {
            $value = (is_object($row)) ? $this->_format($row->$key, $column->format) : $this->_format(
                $row[$key],
                $column->format
            );
            $xPos += $this->padding;
            $this->pdf->showBoxed(
                $value,
                $xPos,
                $this->startPosition - ($this->lineOffset + $lineHeight + $this->padding),
                $column->width,
                $lineHeight,
                $column->alignment,
                ''
            );
            $xPos += $column->width + $this->padding;
        }

        $this->_updateLineOffset($lineHeight);
    }

    public function renderDataDynamicHeight($data)
    {
        foreach ($data as $row) {
            $this->renderLineDynamicHeight($row);
        }
    }

    public function renderLineDynamicHeight($row, $shareColumn = false)
    {
        //
        $row = (array) $row;
        // -- if its a multiline PDF - calculate the largest number of lines required for the offset
        $offset = $this->_calculateOffset($row) * ($this->fontSize) + ($this->padding * 2);

        // -- check to make sure the row will fit on the page
        $this->_checkLineOffset($offset);
        $xPos = $this->hMargin;
        foreach ($this->columns as $key => $column) {
            if (! isset($row['format'][$key]) && ! $row['format'][$key]) {
                $value = (is_object($row)) ? $this->_format($row->$key, $column->format) : $this->_format(
                    $row[$key],
                    $column->format
                );
            } else {
                $value = (is_object($row)) ? $this->_format($row->$key, $row['format'][$key]) : $this->_format(
                    $row[$key],
                    $row['format'][$key]
                );
            }

            $xPos += $this->padding;
            $this->pdf->setColorExt('both', 'rgb', 0, 0, 0, 0);
            $this->setFont('Helvetica', $this->fontSize);
            $this->pdf->showBoxed(
                $value . $row['last'][$key],
                $xPos,
                $this->startPosition - ($this->lineOffset + $offset + $this->padding) - ($row['offset'][$key]),
                ($row['width'][$key] ? $row['width'][$key] : $column->width),
                ($row['height'][$key] ? $row['height'][$key] : $offset),
                ($row['align'][$key] ? $row['align'][$key] : $column->alignment),
                ''
            );
            $xPos += $column->width + $this->padding;
        }

        if ($this->printRowLines) {
            $this->hr($this->startPosition - ($this->lineOffset + $offset), 0.90);
        }

        $this->_updateLineOffset($offset);
    }
}

/*



class PDFDataMultiLineReport extends PDFDataReport {

    var $data;
    var $columns = array();
    var $subheader = array();
    var $keyheader = array();
    var $_keyHeaderHeight = 0;

    var $pageSize = A4_PORTRAIT;
    var $multiLine = true;
    var $printRowLines = false;
    var $printColumnLines = true;

    var $startPosition = null;
    var $logoFile = null;
    var $height = null;
    var $lineHeight = null;
    var $pageNumbers = true;
    var $verifyColumns = false;
    var $checkList = array();


    var $lineWidth = 0.3;
    var $vMargin = 25;
    var $hMargin = 25;
    var $fontSize = 7;
    var $padding = 3;
    var $header = 100;
    var $footer = 75;

    var $assignedWidth = null;
    var $availableWidth = null;

    //-- creates a new PDF - dataSource can be another report or a filename
    function PDFDataMultiLineReport(&$dataSource, $logoFile = null,  $pageSize = null) {
    if ($pageSize) $this->pageSize = $pageSize;
    $page = getPageSize($this->pageSize);
    parent::__construct($dataSource,$page->width,$page->height);
        if ($logoFile) $this->logoFile = $logoFile;
    $this->_setWorkingArea();
    $this->_setFontSize($this->fontSize);
    }

    function _setFontSize($fontSize) {
    $this->fontSize = $fontSize;
    $this->lineHeight = $this->fontSize + ($this->padding*2);
    }

    function _setWorkingArea() {
    $this->startPosition = $this->pageHeight-$this->header;
    $this->height = $this->pageHeight-($this->header+$this->footer);
    $this->availableWidth = $this->pageWidth-($this->hMargin*2);
    }

    function _calculateOffset($row) {
    $max = 0;
    foreach($this->columns as $key => $item) {
        $lines = explode("\n",$row[$key]);
        $count = count($lines);
        if ($count > $max) $max = $count;
    }
    return $max;
    }

    function _calculateOffsetItem($item) {
        $lines = explode("\n",$item);
        return count($lines);
     }

    function _format($value, $format) {
    if ($value) {
        switch($format) {
            case REPORT_MONEY : $value = toMoney($value); break;
            case REPORT_DECIMAL : $value = toDecimal($value); break;
            case REPORT_INTEGER : $value = toDecimal($value,0); break;
            case REPORT_PERCENTAGE : $value = round($value,1); break;
        }
    }
    return $value;
    }


    function setHeaderHeight($height) {
    $this->header = $height;
    $this->setWorkingArea();
    }

    function setFooterHeight($height) {
    $this->footer = $height;
    $this->setWorkingArea();
    }

    function addColumn($key,$name,$width,$alignment, $format = null) {
    if ($this->assignedWidth + $width + ($this->padding*2) <= $this->availableWidth) {
        if ($this->verifyColumns) {
        if ($this->checkList[$key]) {
        $column = new stdClass();
        $column->width = $width;
        $column->name = $name;

        $column->alignment = $alignment;
        $column->format = $format;
        $this->columns[$key] = $column;
        $this->assignedWidth += $width + ($this->padding*2);

        return true;
        } else {
        return false;
        }
        } else {
        $column = new stdClass();
        $column->width = $width;
        $column->name = $name;
        $column->alignment = $alignment;
        $column->format = $format;
        $this->columns[$key] = $column;
        $this->assignedWidth += $width + ($this->padding*2);

        return true;

        }
    } else {
        return false;
    }
    }

    function resetColumns() {
    $this->assignedWidth = 0;
    $this->columns = array();
    }


    function addKeyHeaderItem($key,$position, $width,$alignment, $value = null) {
    $column = new stdClass();
    $column->width = $width;
    $column->position = $position;
    $column->alignment = $alignment;
    $column->value = $value;
    $this->keyheader[$key] = $column;
    }

    function addKeyHeaders($row) {
    $position = 0;//$this->padding;
    foreach ($this->columns as $key => $column) {
        $k = $column;
        $k->value = $row[$key];
        $k->position = $position;
        $position += (($column->width * 1) +  ($this->padding*2));
        if ($row[$key]) $this->keyheader[$key] = $k;
    }
    $this->_keyHeaderHeight = $this->_calculateOffset($row);
    }

    function setKeyHeaderValue($key,$value) {
    $this->keyheader[$key]->value = $value;
    }


    function addSubHeaderItem($key,$position, $width,$alignment) {
    $column = new stdClass();
    $column->width = $width;
    $column->position = $position;
    $column->alignment = $alignment;
    $this->subheader[$key] = $column;
    }

    function setSubHeaderValue($key,$value) {
    $this->subheader[$key]->value = $value;
    }



    function _checkLineOffset($offset) {
        if (($this->lineOffset+$offset) >= $this->height) {
            $this->endPage();
            $this->preparePage();
            $this->render();
        }
    }

    function _updateLineOffset($offset) {
    $this->lineOffset += $offset;
    }

    function updateLineOffset($offset) {
    $this->_checkLineOffset($offset);
    $this->_updateLineOffset($offset);
    }

    function _hr($yPos, $startX, $endX, $color = 0.75) {
        $this->pdf->setColorExt("both", "rgb", $color, $color, $color);
        $this->pdf->setlinewidth ($this->lineWidth);
    $this->pdf->moveto($startX, $yPos);
    $this->pdf->lineto($endX, $yPos);
    $this->pdf->stroke($this->pdf);
    }

    function _vr($xPos,$startY,$endY,$color = 0.75) {
        $this->pdf->setColorExt("both", "rgb", $color, $color, $color);
        $this->pdf->setlinewidth ($this->lineWidth);
    $this->pdf->moveto($xPos, $startY);
    $this->pdf->lineto($xPos, $endY);
    $this->pdf->stroke($this->pdf);
    }

    function hr($yPos, $color = 0.75) {
    $this->_hr($yPos, $this->hMargin, $this->pageWidth-$this->hMargin,$color);
    }

    function vr($xPos,$color = 0.75) {
    $this->_vr($xPos, $this->startPosition, $this->startPosition-$this->height, $color);
    }

    function renderTable() {
    $this->hr($this->startPosition);
    $this->hr($this->startPosition-$this->height);
        $xPos = $this->hMargin;
    foreach($this->columns as $column) {
        if ($this->printColumnLines) $this->vr($xPos);
        $xPos +=  $column->width+($this->padding*2);
    }
    $this->vr($this->hMargin);
    $this->vr($this->pageWidth-$this->hMargin);
    }

    //-- erases lines drawn on the final page - bit of a hack
    function clean() {
    $this->hr($this->startPosition-$this->height,1);
    $xPos = $this->hMargin;
    foreach($this->columns as $column) {
        $this->_vr($xPos,$this->startPosition-$this->lineOffset,$this->startPosition-$this->height,1);
        $xPos +=  $column->width+($this->padding*2);
    }
    $this->_vr($this->hMargin,$this->startPosition-$this->lineOffset,$this->startPosition-$this->height,1);
    $this->_vr($this->pageWidth-$this->hMargin,$this->startPosition-$this->lineOffset,$this->startPosition-$this->height,1);
    }

    function renderHeader($background = array(0,0.3,0.5)) {
    $xPos = $this->hMargin;
    //$this->pdf->setColorExt("both", "rgb",0.1, 0.1, 0.1);
    $this->pdf->setColorExt("both", "rgb", $background[0], $background[1], $background[2]);
    $this->pdf->rect($this->hMargin,$this->startPosition,$this->pageWidth-($this->hMargin*2),$this->lineHeight);
    $this->pdf->fill($this->pdf);

    $this->pdf->setColorExt("both", "rgb", 1, 1, 1);
        $this->pdf->set_font("Helvetica-Bold", $this->fontSize, "host");

    foreach($this->columns as $column) {
        $xPos +=  $this->padding;
        $this->pdf->showBoxed  ($column->name, $xPos  , $this->startPosition-$this->padding, $column->width, $this->lineHeight, $column->alignment);
        $xPos +=  $column->width+($this->padding);
    }
    }

    function renderPageNumber() {
    $this->pdf->setColorExt("both", "rgb", 0, 0, 0);
        $this->pdf->set_font("Helvetica-Bold", 6 , "host");
    $width = 10;
    $this->pdf->showBoxed  ($this->pageCount, $this->pageWidth-$this->hMargin-$width  , $this->hMargin-10, $width, 6, 'right');
    }

    function renderSubHeader($color = 0.95, $size = 1, $text = array(0,0,0)) {
    $xPos = $this->hMargin+$this->padding;
    $lineHeight = $this->lineHeight+$size;
    $this->_checkLineOffset($lineHeight);
    $this->pdf->setColorExt("both", "rgb",$color, $color, $color);
    $this->pdf->rect($this->hMargin+$this->lineWidth,$this->startPosition-($this->lineOffset+$lineHeight),$this->pageWidth-($this->hMargin*2)-($this->lineWidth*2),$lineHeight);
    $this->pdf->fill($this->pdf);
       $this->hr($this->startPosition-($this->lineOffset));
    $this->pdf->setColorExt("both", "rgb", $text[0], $text[1], $text[2]);
        $this->pdf->set_font("Helvetica-Bold", $this->fontSize+$size, "host");
    foreach($this->subheader as $item) $this->pdf->showBoxed  ( $item->value, $xPos+$item->position, $this->startPosition-($this->lineOffset+$lineHeight+$this->padding), $item->width  , $lineHeight, $item->alignment);
    $this->hr($this->startPosition-($this->lineOffset+$lineHeight));
    $this->_updateLineOffset($lineHeight);
    }


    function renderKeyHeader($color = 0.98, $size = -1, $text = array(0,0,0)) {
    $xPos = $this->hMargin+$this->padding;
    $lineHeight = ($this->lineHeight+$size)*$this->_keyHeaderHeight * 0.7;
    $this->_checkLineOffset($lineHeight);
    $this->pdf->setColorExt("both", "rgb",$color, $color, $color);
    $this->pdf->rect($this->hMargin+$this->lineWidth,$this->startPosition-($this->lineOffset+$lineHeight),$this->pageWidth-($this->hMargin*2)-($this->lineWidth*2),$lineHeight);
    $this->pdf->fill($this->pdf);
       $this->hr($this->startPosition-($this->lineOffset));
    $this->pdf->setColorExt("both", "rgb", $text[0], $text[1], $text[2]);
        $this->pdf->set_font("Helvetica", $this->fontSize+$size, "host");
    foreach($this->keyheader as $item) $this->pdf->showBoxed  ( $item->value, $xPos+$item->position, $this->startPosition-($this->lineOffset+$lineHeight+$this->padding), $item->width  , $lineHeight, $item->alignment);
    $this->hr($this->startPosition-($this->lineOffset+$lineHeight));
    $this->_updateLineOffset($lineHeight);
    }

    function renderSubTotal($row, $color = 0.9, $size = 0) {
    $lineHeight = $this->lineHeight+$size;
    $this->_checkLineOffset($lineHeight);
    $xPos = $this->hMargin;
    //-- draw the filled in box
    $this->pdf->setColorExt("both", "rgb",$color, $color, $color);
    $this->pdf->rect($this->hMargin+$this->lineWidth,$this->startPosition-($this->lineOffset+$lineHeight),$this->pageWidth-($this->hMargin*2)-(2*$this->lineWidth),$lineHeight);
    $this->pdf->fill($this->pdf);
        $this->hr($this->startPosition-($this->lineOffset));
    $this->hr($this->startPosition-($this->lineOffset+$lineHeight));

    //-- print the columns
    $this->pdf->setColorExt("both", "rgb", 0, 0, 0);
        $this->pdf->set_font("Helvetica-Bold", $this->fontSize+$size, "host");
    foreach($this->columns as $key => $column) {
        $value = $this->_format($row[$key],$column->format);
        $xPos += $this->padding;
        $this->pdf->showBoxed  ( $value, $xPos  , $this->startPosition-($this->lineOffset+$lineHeight+$this->padding), $column->width , $lineHeight, $column->alignment);
        $xPos +=  $column->width+$this->padding;
    }
   $this->_updateLineOffset($lineHeight);
    }

    function renderLine($row) {
    //-- if its a multiline PDF - calculate the largest number of lines required for the offset
    if ($this->multiLine) {
        $offset = $this->_calculateOffset($row)*($this->fontSize)+($this->padding*2);
        if (($this->lineOffset+$offset) >= $this->height) {
        $this->endPage();
        $this->preparePage();
        $this->render();
        }
    } else {
        //-- otherwise use the single line height
        $offset = $this->lineHeight;
    }

    //-- check to make sure the row will fit on the page
    $this->_checkLineOffset($offset);
    $xPos = $this->hMargin;
    $this->pdf->setColorExt("both", "rgb", 0, 0, 0);
        $this->pdf->set_font("Helvetica", $this->fontSize, "host");
    foreach($this->columns as $key => $column) {
            $value = $this->_format($row[$key], $column->format);
            $xPos +=  $this->padding;
            $this->pdf->showBoxed($value, $xPos, $this->startPosition-($this->lineOffset+$offset+$this->padding), $column->width, $offset, $column->alignment);
            $xPos +=  $column->width+$this->padding;
    }
    if ($this->printRowLines) $this->hr($this->startPosition-($this->lineOffset+$offset),0.90);
    $this->_updateLineOffset($offset);

    }


    function renderData($data) {

    foreach($data as $row) {
        $this->renderLine($row);
    }
    }

 function renderLogo() {
       if ($this->logoFile) {

            $maxWidth = LOGO_WIDTH;
            $maxHeight = LOGO_HEIGHT;
            list($imageWidth, $imageHeight) = getimagesize($this->logoFile);

            $horizontalScaling = ($imageWidth > $maxWidth) ? $maxWidth/$imageWidth : 1;
            $verticalScaling = ($imageHeight > $maxHeight) ? $maxHeight/$imageHeight : 1;
            $imageScale = ($horizontalScaling < $verticalScaling) ? $horizontalScaling : $verticalScaling;

        $imageScale = round($imageScale,2);

        $hPos = $this->pageWidth-($imageScale*$imageWidth)- $this->hMargin;
        $vPos = $this->pageHeight-($imageScale*$imageHeight)- $this->vMargin;

            if (!$this->resourceList['logo'])
                $this->resourceList['logo'] = $this->pdf->open_image_file("jpeg", $this->logoFile);
            $this->pdf->place_image($this->resourceList['logo'], $hPos,$vPos, "scale {$imageScale}");
        }
    }

        function preparePage()
    {
                $this->lineOffset = 0;
                parent::preparePage();
                $this->render();
                $this->renderLogo();
        $this->renderTable();
        $this->renderHeader();
        $this->renderKeyHeader();
        if ($this->pageNumbers) $this->renderPageNumber();

    }

    function endPage() {

        parent::endPage();
    }

    function close()
    {
        parent::close();
    }

}
 */


/***
 *
 *    PDF Report - Base Class
 *
 ***/
class PDFReport
{
    public $objects = [];

    public $pdf;

    public $path;

    public $className;

    public $resourceList = [];

    public $fontList = [];

    public $cache = true;

    public $template;

    public $pageWidth;

    public $pageHeight;

    public $pageCount = 0;

    public $mode = PDF_MASTER;

    public $_page = false;

    // -- creates a new PDF - dataSource can be another report or a filename
    public function __construct(&$dataSource, $pageWidth = 595, $pageHeight = 842)
    {
        $this->className = get_class($this);
        if (is_object($dataSource)) {
            // -- if its based on this parent class, inherit the master document
            $this->pdf = &$dataSource->pdf;
            $this->resourceList = &$dataSource->resourceList;
            $this->pageCount = &$dataSource->pageCount;
            $this->mode = PDF_SLAVE;
        } elseif (is_string($dataSource)) {
            try {
                $this->pdf = new PDFLibExt();
                $this->pdf->set_option('license=' . PDFLIB_LICENSE);
                $this->pdf->set_option('stringformat=utf8');
                $this->path = $dataSource;
                $success = true;
                if (file_exists($dataSource)) {
                    $success = @unlink($dataSource);
                }

                if (! $success) {
                    exit('Unable to proceed. File is currently in use');
                }

                $success = @checkDirPath($dataSource, true);
                if (! $success) {
                    exit('Unable to proceed. Problems with the file path');
                }


                // $this->pdf->set_value('compress',9);
                $this->pdf->set_option('SearchPath=' . realpath(BASEPATH . '/framework/'));
                $this->pdf->set_option('errorpolicy=exception');


                if ($this->pdf->begin_document($dataSource, '') == 0) {
                    throw new Exception('Error: ' . $this->pdf->get_errmsg());
                }
            } catch (PDFlibException $e) {
                exit(
                    "PDFlib exception:\n" .
                    '[' . $e->get_errnum() . '] ' . $e->get_apiname() . ': ' .
                    $e->get_errmsg() . "\n"
                );
            }


            /* Set an output path according to the name of the topic */
            // if (DEBUG) echo 'open PDF';
        } else {
            // -- if its not a string (for the path) or an instance of a child of PDFReport (for instance if a PDFLib resource is passed) -- throw an error
            exit('Invalid resource has been passed - ' . get_resource_type($dataSource));
        }

        $this->pageWidth = $pageWidth;
        $this->pageHeight = $pageHeight;
    }

    // -- attaches an object : each object should have a render method that is called at render for the PDF
    // -- objects are essentially repeating blocks on a page
    public function attachObject($objectName, $object)
    {
        $this->objects[$objectName] = $object;
    }

    // -- ends a page in the PDF
    public function endPage()
    {
        // if (DEBUG) echo 'end PDF page';
        if ($this->_page) {
            $this->pdf->end_page_ext('');
            $this->_page = false;
        }
    }

    // -- renders all objects attached to the report
    public function render()
    {
        if ($this->_page) {
            foreach ($this->objects as $p) {
                $p->render($this->pdf);
            }
        }
    }

    public function setFont($font, $fontsize)
    {
        if (! isset($this->fontList[$font])) {
            $this->fontList[$font] = $this->pdf->load_font($font, 'host', '');
        }

        // if (DEBUG) echo 'set font - ' . $font;
        $this->pdf->setFontExt($this->fontList[$font], $fontsize);
    }

    public function setOption($size)
    {
        //        $this->pdf->set_option("FontOutline={OCR=C:\Windows\Fonts\OCRB.ttf}");
        //        $font = $this->pdf->load_font('OCR', 'host', '');
        //        $this->pdf->setColorExt( "both", "rgb", 0, 0, 0,0);
        //        $this->pdf->setFontExt ($font, $size);
    }

    // -- creates a fresh page and renders pre-loaded content to the page
    public function preparePage($printTemplate = true)
    {
        // -- uses the class name to specify a resource within the PDF - creates a template using the pre-render of each PDF Object
        if ($printTemplate && (! $this->resourceList[$this->className] || ! $this->cache)) {
            $this->resourceList[$this->className] = $this->pdf->begin_template_ext(
                $this->pageWidth,
                $this->pageHeight,
                ''
            );
            foreach ($this->objects as $p) {
                $p->preRender($this->pdf);
            }

            $this->pdf->end_template_ext($this->pageWidth, $this->pageHeight);
        }

        // if (DEBUG) echo 'begin PDF page';
        // -- begin a page then render the template
        $this->pdf->begin_page_ext($this->pageWidth, $this->pageHeight, '');
        if ($printTemplate && $this->resourceList[$this->className]) {
            $this->pdf->fit_image($this->resourceList[$this->className], 1, 1, 'scale 1');
        }

        $this->pageCount++;

        $this->_page = true;
    }

    // -- closes the PDF
    public function close()
    {
        if ($this->pageCount > 0) {
            // if (DEBUG) echo 'end PDF page (close)';
            if ($this->_page) {
                $this->pdf->end_page_ext('');
                $this->_page = false;
            }

            if ($this->mode === PDF_MASTER) {
                $this->_close();
            }
        }
    }

    // -- note : some PDFs were being closed prematurely
    public function _close()
    {
        if ($this->pageCount > 0) {
            // if (DEBUG) echo 'close PDF file';
            $this->pdf->end_document('');
        } else {
            if (DEBUG) {
                echo 'PDF delete';
            }

            // $this->pdf->delete($this->pdf);
        }
    }

    // -- note : some PDFs were being closed prematurely
    public function _close_gti()
    {
        if ($this->pageCount > 0) {
            // if (DEBUG) echo 'close PDF file';
            $this->pdf->end_document('');
            $return = true;
        } else {
            if (DEBUG) {
                $return = false;
            }

            // $this->pdf->delete($this->pdf);
        }

        return $return;
    }
}

// -- single PDF page for inclusion in a PDF

class PDFPage
{
    public $pageCount;

    public $objects = [];

    public $pdf;

    public $page;

    public $doc;

    public $resourceHandle;

    public $className;

    public $resourceList = [];

    public $template;

    public $pageWidth;

    public $pageHeight;

    public $vMargin = 25;

    public $hMargin = 25;

    public $usePDI = false;

    public $filename;

    public $pdiPage = 1;

    // -- creates a new PDF page - passing a pdf handle and the page at which this is to be inserted
    public function __construct(&$pdf, $page, $pageWidth = 595, $pageHeight = 842)
    {
        $this->className = get_class($this);
        $this->pdf = $pdf;
        $this->page = $page;
        $this->pageWidth = $pageWidth;
        $this->pageHeight = $pageHeight;
        // function_exists('$this->pdf->open_pdi');
    }

    // -- attaches an object : each object should have a render method that is called at render for the PDF
    // -- objects are essentially repeating blocks on a page
    public function attachObject($objectName, $object)
    {
        $this->objects[$objectName] = $object;
    }

    // -- ends a page in the PDF
    public function endPage()
    {
        $this->pdf->end_page_ext('');
    }

    // -- renders all objects attached to the report
    public function render()
    {
        if ($this->usePDI) {
            $this->pdf->fit_pdi_page($this->resourceHandle, 0, 0, ''); // , $this->pageWidth, $this->pageHeight
            $this->pdf->close_pdi_page($this->resourceHandle);
            $this->pdf->close_pdi($this->doc);
        } else {
            $this->pdf->resume_page("pagenumber {$this->page}");
        }

        foreach ($this->objects as $p) {
            $p->render($this->pdf);
        }
    }

    // -- creates a fresh page and renders pre-loaded content to the page
    public function preparePage()
    {
        // -- uses the class name to specify a resource within the PDF - creates a template using the pre-render of each PDF Object

        if ($this->usePDI) {
            $this->doc = $this->pdf->open_pdi_document($this->filename, null);
            $this->resourceHandle = $this->pdf->open_pdi_page($this->doc, $this->pdiPage, null);

            if (! $this->resourceList[$this->className]) {
                $this->resourceList[$this->className] = $this->pdf->begin_template_ext(
                    $this->pageWidth,
                    $this->pageHeight,
                    ''
                );
                foreach ($this->objects as $p) {
                    $p->preRender($this->pdf);
                }

                $this->pdf->end_template();
            }
        } else {
            // -- uses the class name to specify a resource within the PDF - creates a template using the pre-render of each PDF Object
            if (! $this->resourceList[$this->className]) {
                $this->resourceList[$this->className] = $this->pdf->begin_template_ext(
                    $this->pageWidth,
                    $this->pageHeight,
                    ''
                );
                foreach ($this->objects as $p) {
                    $p->preRender($this->pdf);
                }

                $this->pdf->end_template();
            }

            // -- begin a page then render the template

        }

        $this->pdf->begin_page_ext($this->pageWidth, $this->pageHeight, '');
        if ($this->resourceList[$this->className]) {
            $this->pdf->fit_image($this->resourceList[$this->className], 1, 1, 'scale 1');
        }

        // -- reserve the first page
        if (! $this->usePDI) {
            $this->pdf->suspend_page('');
        }

        $this->pageCount++;
    }

    // -- closes the PDF
    public function close()
    {
        // if ($this->pageCount > 0)
        $this->pdf->end_page_ext('');
    }
}

// -- single PDF page for inclusion in a PDF

class PDIMultiPage
{
    public $pageCount;

    public $objects = [];

    public $pdf;

    public $doc;

    public $resourceHandle;

    public $className;

    public $resourceList = [];

    public $template;

    public $pageWidth;

    public $pageHeight;

    public $vMargin = 25;

    public $hMargin = 25;

    public $filename;

    public $pdiPage = 1;

    // -- creates a new PDF page - passing a pdf handle and the page at which this is to be inserted
    public function __construct(&$pdf, $filename, $pageWidth = 595, $pageHeight = 842)
    {
        $this->className = get_class($this);
        $this->pdf = $pdf;
        $this->pageWidth = $pageWidth;
        $this->pageHeight = $pageHeight;
        $this->filename = $filename;
        $this->doc = $this->pdf->open_pdi_document($this->filename, '');
    }

    // -- attaches an object : each object should have a render method that is called at render for the PDF
    // -- objects are essentially repeating blocks on a page
    public function attachObject($objectName, $object)
    {
        $this->objects[$objectName] = $object;
    }

    // -- ends a page in the PDF
    public function endPage()
    {
        $this->pdf->close_pdi_page($this->resourceHandle);
        $this->pdf->end_page_ext('');
    }

    // -- renders all objects attached to the report
    public function render()
    {
        $this->pdf->fit_pdi_page($this->resourceHandle, 0, 0, ''); // , $this->pageWidth, $this->pageHeight

        foreach ($this->objects as $p) {
            $p->render($this->pdf);
        }
    }

    public function loadPage($page)
    {
        //        if (DEBUG) echo $page;
        $this->resourceHandle = $this->pdf->open_pdi_page($this->doc, $page, '');

        //        if (DEBUG) echo ' >' . $this->resourceHandle . '< ';
        return (bool) $this->resourceHandle;
    }

    // -- creates a fresh page and renders pre-loaded content to the page
    public function preparePage()
    {
        if (! $this->resourceList[$this->className]) {
            $this->resourceList[$this->className] = $this->pdf->begin_template_ext(
                $this->pageWidth,
                $this->pageHeight,
                ''
            );
            foreach ($this->objects as $p) {
                $p->preRender($this->pdf);
            }

            $this->pdf->end_template_ext($this->pageWidth, $this->pageHeight);
        }

        $this->pdf->begin_page_ext($this->pageWidth, $this->pageHeight, '');
        if ($this->resourceList[$this->className]) {
            $this->pdf->fit_image($this->resourceList[$this->className], 1, 1, 'scale 1');
        }

        $this->pageCount++;
    }

    // -- closes the PDF
    public function close()
    {
        $this->pdf->close_pdi_document($this->doc);
    }
}

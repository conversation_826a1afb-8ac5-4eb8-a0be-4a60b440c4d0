<?php

include SYSTEMPATH . '/lib/enums/CountryCode.php';

use enums\CountryCode;
use enums\DefaultBSBValue;
use Phpass\Strength;
use Phpass\Strength\Adapter\Wolfram;

// a few base regex expressions for text validation
define('TEXT_ALPHA', '/^[a-zA-Z]*$/');
define('TEXT_ALPHANUMERIC', '/^[a-zA-Z0-9]*$/'); // 2017-03-16: Added for numeric only [Ar<PERSON>]
define('TEXT_INT', "/^[0-9\-]*$/");
define('TEXT_INT_ONLY', '/^\d*$/'); // 2012-05-17: Added for numeric only [Morph]
define('TEXT_FLOAT', "/^[0-9.\-]*$/");
define('TEXT_FLOATV2', '/^[+-]?(\d+([.]\d*)?|[.]\d+)$/'); // not accept 1.1.1
define('TEXT_KEY', "/^[\w&\-]*$/"); /* edit 20090525 : added slash for property codes on GW with / */
define('TEXT_KEY_STRICT', "/^[\w\-]*$/");
define('TEXT_KEY_EXTENDED', "/^[\w\/]*$/");
define('TEXT_ALPHANUMERIC_WHITESPACE', "/^[\w\s]*$/");
define('TEXT_STRICT', '/^[\w,.\s]*$/');
define('TEXT_STRICTV2', '/^[\w,.\s\' -]*$/');
define('TEXT_LOOSE', "|^[\'\w\s@&,\$\";:\-\(\)\*\'\+\?\.\\\/]*$|");
define('TEXT_LOOSE_WITHOUT_AMPERSAND', "|^[\'\w\s@,\$\";:\-\(\)\*\'\+\?\.\\\/]*$|");
define('TEXT_SMARTDATE', '/^(\d{2}\/\d{2}\/\d{4})+$/');
define('TEXT_EMAIL', "/^.+@[^\.].*\.[a-z]{2,}$/");
define('TEXT_EMAIL_SINGLE', "/^[a-zA-Z0-9._%+&-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/");
define(
    'TEXT_EMAIL_MULTI',
    "/^([a-zA-Z0-9._%+&-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,};\s*)*[a-zA-Z0-9._%+&-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/"
);
define('TEXT_HTML', '|[`]*$|');
define('TEXT_LOOSE_WITH_PERCENT_SIGN', "|^[\'\w\s@&,\$\";:%\-\(\)\*\'\+\?\.\\\/]*$|");
define('TEXT_CODE', "/^[a-zA-Z0-9\-]*$/");
define('INVOICE_NUMBER', "/^[\w\-\/.]*$/");
define('TEXT_ALPHANUMERIC_CAPS', '/^[A-Z0-9]*$/');
define('CSV_TEXT', "/^[^=+@,;\'\"-]*$/");


include __DIR__ . '/dateFunctions.php';


define('EMAILREF_REMITTANCE', 1);
define('EMAILREF_INVOICE', 2);
define('EMAILREF_REPORT', 3);

function emailIdentifier($type, $reference, $targetID)
{
    return sha1($_SESSION['databaseID'] . '.' . $type . '.' . $reference . '.' . strtoupper($targetID));
}

function joinPaths(...$paths): string
{
    $flatPaths = [];
    array_walk_recursive($paths, function ($p) use (&$flatPaths) { $flatPaths[] = $p; });
    $paths = array_filter($flatPaths, fn($p) => $p !== '' && $p !== null);

    return preg_replace('+/{2,}+', '/', implode('/', $paths));
}


function numberToLetter($number)
{
    $output = '';
    $index = 65;
    $n = $number - 1;
    $first = floor($n / 26);
    if ($first !== 0.0) {
        $output .= chr($index + $first - 1);
    }

    return $output . chr($index + ($n % 26));
}


function cellReference($y, $x)
{
    return numberToLetter($x) . $y;
}


function mssqlString(&$field)
{
    $field = str_replace("'", "''", $field);
}

function _exec($cmd)
{
    $WshShell = new COM('WScript.Shell');
    $oExec = $WshShell->Run($cmd, 0, false);

    return $oExec == 0;
}


function returnMIMEType($filename)
{
    preg_match("|\.([a-z0-9]{2,4})$|i", $filename, $fileSuffix);

    switch (strtolower($fileSuffix[1])) {
        case 'js':
            return 'application/x-javascript';

        case 'json':
            return 'application/json';

        case 'jpg':
        case 'jpeg':
        case 'jpe':
            return 'image/jpg';

        case 'png':
        case 'gif':
        case 'bmp':
        case 'tiff':
            return 'image/' . strtolower($fileSuffix[1]);

        case 'css':
            return 'text/css';

        case 'xml':
            return 'application/xml';

        case 'doc':
        case 'docx':
            return 'application/msword';

        case 'xls':
        case 'xlt':
        case 'xlm':
        case 'xld':
        case 'xla':
        case 'xlc':
        case 'xlw':
        case 'xll':
            return 'application/vnd.ms-excel';

        case 'ppt':
        case 'pps':
            return 'application/vnd.ms-powerpoint';

        case 'rtf':
            return 'application/rtf';

        case 'pdf':
            return 'application/pdf';

        case 'html':
        case 'htm':
        case 'php':
            return 'text/html';

        case 'txt':
            return 'text/plain';

        case 'mpeg':
        case 'mpg':
        case 'mpe':
            return 'video/mpeg';

        case 'mp3':
            return 'audio/mpeg3';

        case 'wav':
            return 'audio/wav';

        case 'aiff':
        case 'aif':
            return 'audio/aiff';

        case 'avi':
            return 'video/msvideo';

        case 'wmv':
            return 'video/x-ms-wmv';

        case 'mov':
            return 'video/quicktime';

        case 'zip':
            return 'application/zip';

        case 'tar':
            return 'application/x-tar';

        case 'swf':
            return 'application/x-shockwave-flash';

        default:
            if (function_exists('mime_content_type')) {
                $fileSuffix = mime_content_type($filename);
            }

            return 'unknown/' . trim($fileSuffix[0], '.');
    }
}


function mod10($number)
{
    for ($x = 0, $xMax = strlen($number); $x < $xMax; $x++) {
        $digit = substr($number, $x, 1);
        if (strlen($number) % 2 == 1) {
            if ($x / 2 == floor($x / 2)) {
                $digit *= 2;
            }
        } elseif ($x / 2 == floor($x / 2)) {
            $digit *= 1;
        } else {
            $digit *= 2;
        }

        if (strlen($digit) == 2) {
            $digit = substr($digit, 0, 1) + substr($digit, 1, 1);
        }

        $mysum += $digit;
    }

    $rem = $mysum % 10;

    return ($rem == 0) ? 0 : 10 - $rem;
}

/**
 * Reference number check digit calculation (Modulus 9)
 *
 * @param  int  $number  Mandatory.
 *
 * <AUTHOR> Reyes
 *
 * @since 2015-07-30
 *
 * @reference http://wiki.tcl.tk/18143
 */
function mod9($number)
{
    $pattern = [13, 11, 7, 5, 3, 2, 1, 13, 11, 7, 5, 3, 2, 1, 13, 11, 7, 5, 3, 2];
    $count = 19;
    $total = 0;

    $number = preg_replace("/\D/", '', $number);

    if (! is_numeric($number)) {
        return false;
    }

    if ($number <= 0) {
        return false;
    }

    $len = strlen($number);

    for ($i = $len - 1; $i >= 0; $i--) {
        $total += $number[$i] * $pattern[$count];
        $count--;
    }

    $remainder = fmod($total, 9);

    return 9 - $remainder;
}

/**
 * @param  int  $i  Mandatory.
 * @param  int  $modulus  Optional.
 *
 * @modified 2015-07-30: Adde $modulus parameter as per task # 3828476. Removed unused parameter ($length). [Morph]
 **/
function buildCRN($i, $modulus = 10)
{
    $i = 5000000 + $i;

    return ($modulus == 10) ? $i . mod10($i) : $i . mod9($i);
}

function hashColumn($source, $columns)
{
    $a = [];
    foreach ($columns as $c) {
        $a[] = $source[$c];
    }

    return md5(serialize($a));
}

function reduceColumns($source, $columns)
{
    $a = [];
    if (is_array($source)) {
        foreach ($columns as $c) {
            $a[$c] = $source[$c];
        }

        return $a;
    } else {
        return;
    }
}


function arrayOrderBy(...$args)
{
    $data = array_shift($args);
    foreach ($args as $n => $field) {
        if (is_string($field)) {
            $tmp = [];
            foreach ($data as $key => $row) {
                $tmp[$key] = $row[$field];
            }

            $args[$n] = $tmp;
        }
    }

    $args[] = &$data;
    array_multisort(...$args);

    return array_pop($args);
}


function columnDiff($new, $old, $columns = [], $showAll = false)
{
    $o = [];

    // -- reduce the arrays to only the information you need (columns) and then create a hash index for each
    foreach ($new as $i => $row) {
        $_a[$i] = reduceColumns($row, $columns);
        $_ah[$i] = md5(serialize($_a[$i]));
    }

    foreach ($old as $i => $row) {
        $_b[$i] = reduceColumns($row, $columns);
        $_bh[$i] = md5(serialize($_b[$i]));
    }


    // -- diff the hashes
    $diff = array_diff(array_merge($_ah, $_bh), array_intersect($_ah, $_bh));


    foreach ($_bh as $i => $hash) {
        if (in_array($hash, $diff)) {
            $_b[$i]['_deleted'] = true;
            $o[] = $_b[$i];
        } elseif ($showAll) {
            $o[] = $_b[$i];
        }
    }


    foreach ($_ah as $i => $hash) {
        if (in_array($hash, $diff)) {
            $_a[$i]['_added'] = true;
            $o[] = $_a[$i];
        }
    }


    return $o;
}


function authKey($loginTime)
{
    return md5($loginTime . $_SERVER['HTTP_USER_AGENT'] . $_SERVER['REMOTE_ADDR']);
}

function isUserType($userType)
{
    global $sess;

    return $sess->get('user_type') == $userType;
}

function userViews()
{
    global $sess, $systemLocation;

    return (isset($systemLocation[$sess->get('user_type')])) ? $systemLocation[$sess->get('user_type')] : '/core';
}

function userData()
{
    global $sess, $systemLocation;

    return (isset($systemLocation[$sess->get('user_type')])) ? $systemLocation[$sess->get('user_type')] : '';
}


function arrayUnique($input)
{
    $tmp = [];
    foreach ($input as $a => $b) {
        $tmp[$a] = serialize($b);
    }

    $newinput = [];
    foreach (array_keys(array_unique($tmp)) as $a) {
        $newinput[$a] = $input[$a];
    }

    return $newinput;
}


function arrayColumn($key, $array)
{
    if (is_array($key) || ! is_array($array)) {
        return [];
    }

    $funct = create_function(
        '$e',
        'return is_array($e) && array_key_exists("' . $key . '",$e) ? $e["' . $key . '"] : null;'
    );

    return array_map($funct, $array);
}


function mapParameters($dataSet, $key = 'parameterID', $value = 'parameterDescription')
{
    $output = [];
    foreach ($dataSet as $dataRow) {
        //	    $output[$dataRow[$key]] = $dataRow[$value];
        $_value = '';
        $_key = '';
        foreach ($dataRow as $_dataRowKey => $_dataRowValue) {
            if ($_dataRowKey == $key) {
                $_key = $_dataRowValue;
            }

            if ($_dataRowKey == $value) {
                $_value = $_dataRowValue;
            }
        }

        $output[$_key] = $_value;
    }

    return $output;
}

function extractParameters($dataSet, $key = 'parameterID', $value = 'parameterDescription', $extra_parameter = null)
{
    $output = [];
    $index = 0;
    foreach ($dataSet as $dataRow) {
        $output[$index][$value] = $dataRow[$value];
        $output[$index][$key] = $dataRow[$key];
        if ($extra_parameter) {
            $output[$index][$extra_parameter] = $dataRow[$extra_parameter];
        }

        $index++;
    }

    return $output;
}


function textSpace($text, $frequency = 3)
{
    $length = strlen($text);
    if ($length <= $frequency) {
        return $text;
    }

    $textArray = str_split($text);
    $mappedArray = array_map(
        fn($char, $index) => ((($length - $index) % $frequency) === 0 && $index > 0) ? '  ' . $char : $char,
        $textArray,
        array_keys($textArray)
    );

    return implode('', $mappedArray);
}


function checkDirPath($path, $includesFilename = false)
{
    if (is_dir($path)) {
        $return = true;
    } else {
        $return = true;
        $directoryList = explode('/', $path);
        $directoryPath = '';
        $elements = count($directoryList ?? []) - 1;
        foreach ($directoryList as $index => $directory) {
            if (($includesFilename) && ($index == $elements)) {
                return $return;
            }

            $directoryPath .= $directory . '/';
            if (! is_dir($directoryPath)) {
                $return = @mkdir($directoryPath, FILE_PERMISSION);
            }
        }
    }

    return $return;
}


// -- given a 2 dimensional associative array, outputs it as a table :)
function table_array($dataSet)
{
    $output = '';
    if ((is_array($dataSet)) && (is_array($dataSet[0]))) {
        $keys = array_keys($dataSet[0]);
        $output = '<table cellpadding="3" cellspacing="0" border="0" class="form"><tr class="fieldDescription">';
        foreach ($keys as $key) {
            $output .= "<td>{$key}</td>";
        }

        $output .= '</tr>';
        foreach ($dataSet as $dataRow) {
            $output .= '<tr class = "' . alternateNextRow() . '">';
            foreach ($dataRow as $value) {
                $output .= "<td>{$value}</td>";
            }

            $output .= '</tr>';
        }

        $output .= '</table>';
    }

    return $output;
}


// checks that an array exists and prints it out as key value pairs. if linebreak is specified, inserts a linebreak between pairs otherwise space delimits.
function print_array($array, $lineBreak = false)
{
    $outputBuffer = '';
    if ((is_array($array) || is_object($array)) && (isset($array))) {
        foreach ($array as $key => $value) {
            $outputBuffer .= (is_array($value) || is_object(
                $value
            )) ? '<strong>' . $key . '</strong> => ' . (($lineBreak) ? '<br /> ' : ' ') . print_array(
                $value,
                $lineBreak
            ) . (($lineBreak) ? '<br /> ' : ' ') : '<strong>' . $key . '</strong> => ' . $value . (($lineBreak) ? '<br /> ' : ' ');
        }
    }

    return $outputBuffer;
}


// checks that an array exists and prints it out as key value pairs. if linebreak is specified, inserts a linebreak between pairs otherwise space delimits.
function show_array($array, $lineBreak = false)
{
    $outputBuffer = '';
    if ((is_array($array)) && (isset($array))) {
        foreach ($array as $key => $value) {
            $outputBuffer .= (is_array($value)) ? $key . ' => ' . (($lineBreak) ? "\n" : ' ') . print_array(
                $value,
                $lineBreak
            ) . (($lineBreak) ? "\n" : ' ') : $key . ' => ' . $value . (($lineBreak) ? "\n" : ' ');
        }
    }

    return $outputBuffer;
}


// FUNCTION toMoney - given a currency symbol and a value, formats it into a correct display format
function toMoney($money, $symbol = '$', $decimalPlaces = 2, $decimalPadding = '0')
{
    global $sess;
    if (! $money || is_nan($money)) {
        $money = 0;
    }

    if ($symbol != null) {
        $symbol = $_SESSION['country_default']['currency_symbol'];
    }

    $negative = false;
    $formatted = floatval($money);
    if ($formatted < 0) {
        $negative = true;
        $formatted = abs($formatted);
    }

    $formatted = number_format($formatted, $decimalPlaces);
    $split = explode('.', $formatted);
    $decimal = str_pad($split[1], $decimalPlaces, $decimalPadding);

    return $symbol . (($negative) ? '(' : '') . $split[0] . ($decimal || $decimalPlaces > 0 ? '.' : '') . $decimal . (($negative) ? ')' : '');
}


function toMoneyCurrencyInsideBrackets($money, $symbol = '$', $decimalPlaces = 2, $decimalPadding = '0')
{
    global $sess;
    $money = (float) $money;
    if (is_nan($money)) {
        $money = 0;
    }

    if ($symbol != null) {
        $symbol = $_SESSION['country_default']['currency_symbol'];
    }

    $negative = false;
    $formatted = floatval($money);
    if ($formatted < 0) {
        $negative = true;
        $formatted = abs($formatted);
    }

    $formatted = number_format($formatted, $decimalPlaces);
    $split = explode('.', $formatted);
    $decimal = str_pad($split[1], $decimalPlaces, $decimalPadding);

    return (($negative) ? '(' : '') . $symbol . $split[0] . ($decimal || $decimalPlaces > 0 ? '.' : '') . $decimal . (($negative) ? ')' : '');
}

function toMoneyCDF($money, $symbol = '$', $decimalPlaces = 2, $decimalPadding = '0')
{
    global $sess;
    if (is_nan($money)) {
        $money = 0;
    }

    if ($symbol != null) {
        $symbol = $sess->get('currencySymbol');
        if ($sess->get('user_type') == USER_OWNER) {
            $symbol = $_SESSION['country_default']['currency_symbol'];
        }
    }

    $negative = false;
    $formatted = floatval($money);
    if ($formatted < 0) {
        $negative = true;
        $formatted = abs($formatted);
    }

    $formatted = number_format($formatted, $decimalPlaces);
    $split = explode('.', $formatted);
    $decimal = str_pad($split[1], $decimalPlaces, $decimalPadding);

    return $symbol . (($negative) ? '(' : '') . $split[0] . ($decimal || $decimalPlaces > 0 ? '.' : '') . $decimal . (($negative) ? ')' : '');
}

function toMoneyCurrencyInsideBracketsCDF($money, $symbol = '$', $decimalPlaces = 2, $decimalPadding = '0')
{
    global $sess;
    if (is_nan($money)) {
        $money = 0;
    }

    if ($symbol != null) {
        $symbol = $sess->get('currencySymbol');
        if ($sess->get('user_type') == USER_OWNER) {
            $symbol = $_SESSION['country_default']['currency_symbol'];
        }
    }

    $negative = false;
    $formatted = floatval($money);
    if ($formatted < 0) {
        $negative = true;
        $formatted = abs($formatted);
    }

    $formatted = number_format($formatted, $decimalPlaces);
    $split = explode('.', $formatted);
    $decimal = str_pad($split[1], $decimalPlaces, $decimalPadding);

    return (($negative) ? '(' : '') . $symbol . $split[0] . ($decimal || $decimalPlaces > 0 ? '.' : '') . $decimal . (($negative) ? ')' : '');
}

function thousandDivision($money)
{
    return (float) $money / 1000;
}

/**
 * @param  $money  integer Mandatory. the number that needs to be formatted.
 * @param  $decimalPlaces  integer Optional. The number of decimal places to be shown on screen (will padd it with zeroes by default is the decimal numbers are less than the given amount.
 * @param  $decimalPadding  string Optional. The padding in this instance will pad the decimal places with zero depending on the how many $decimalPlaces were given.
 * @param  $format  boolean Optional. If set to false, will not format the numbers by comman strings and decimal padding but will continue to round up the numbers by $decimalPlaces. Default sets to TRUE.
 *
 * @modified 2012-12-19: Added additional parameter, $format. Was created to solve the format option where this function is being used by several options but the other option (i.e. Excel) needs not be formatted. [Morph]
 *
 * @return string
 **/
function toDecimal($money, $decimalPlaces = 2, $decimalPadding = '0', $format = true)
{
    $negative = false;
    if ($money == null) {
        return '0.00';
    }

    if ($money == 0) {
        return '0.00';
    }

    $money = (float) str_replace(',', '', $money);
    $formatted = round($money, $decimalPlaces);
    if ($format == true) {
        if ($formatted < 0) {
            $negative = true;
            $formatted = abs($formatted);
        }

        $formatted = number_format($formatted, 2);
        $split = explode('.', $formatted);
        if ($decimalPlaces > 0) {
            $decimal = '.' . str_pad($split[1], $decimalPlaces, $decimalPadding);
        }

        return (($negative) ? '(' : '') . $split[0] . $decimal . (($negative) ? ')' : '');
    } else {
        return $formatted;
    }
}

// FUNCTION toNumber - formats a number to 2 decimal places (now with commas)
function toNumber($amount, $decimalPlaces = 2, $decimalPadding = '0')
{
    $amount = (float) str_replace(',', '', $amount);
    $formatted = round($amount, $decimalPlaces);
    $split = explode('.', $formatted);
    if ($decimalPlaces > 0) {
        $decimal = '.' . str_pad($split[1], $decimalPlaces, $decimalPadding);
    }

    return $split[0] . $decimal;
}

function toChange($money, $decimalPlaces = 0, $format = true)
{
    $negative = false;
    $money = (float) str_replace(',', '', $money);
    $formatted = round($money, $decimalPlaces);
    if ($format == true) {
        if ($formatted < 0) {
            $negative = true;
            $formatted = abs($formatted);
        }

        $formatted = number_format($formatted, 2);
        $split = explode('.', $formatted);

        $result = (($negative) ? '(' : '') . $split[0] . (($negative) ? ')' : '');

        if ($money != 0) {
            $result .= $money > 0 ? '<img width="20"   src="assets/images/icons/arrow-up-green.png" />' : '<img width="20"   src="assets/images/icons/arrow-down-red.png" />';
        }

        return $result;
    } else {
        return $formatted;
    }
}

function toTitleCase($text)
{
    return ucwords($text);
}

function alternateNextRow()
{
    static $state = false;
    $class = 'row';
    if ($state) {
        $class = 'altRow';
    }

    $state = ! $state;

    return $class;
}

function getCookieValue($key)
{
    if (isset($_COOKIE[$key])) {
        return $_COOKIE[$key];
    }



}

function deserializeParameters($parameter, $separator = '::')
{
    if (is_string($parameter)) {
        return explode($separator, $parameter);
    }


}

function simpleSerialize($array, $separator = '::')
{
    if ($array == null) {
        return '';
    }

    return implode($separator, $array);
}

/**
 * @modified 2013-07-10: Clean non-queryStyle output. [Morph]
 **/
function serializeParameters($parameters, $queryStyle = true)
{
    $tempArray = [];
    foreach ($parameters as $key => $value) {
        $tempArray[] = ($queryStyle) ? "{$key}=" . urlencode($value) : "{$key}: '" . addslashes(
            htmlentities($value)
        ) . "'";
    }

    return ($queryStyle) ? '?' . implode('&', $tempArray) : implode(', ', $tempArray);
}

// a slow (can be optimised) way to search for an index value in a multi-dimensional array and return the
// row in which the index value exists
function extractRow($dataSet, $indexField, $indexValue)
{
    foreach ($dataSet as $dataRow) {
        if ($dataRow[$indexField] == $indexValue) {
            return $dataRow;
        }
    }

    return false;
}

// fieldName(separator)uniqueIndex eg ownerSharePercentage_1, ownerSharePercentage_2
function extractID($data, $field, $separator = '_')
{
    $parsedList = [];
    foreach ($data as $key => $value) {
        $parts = explode('_', $key);
        if ((isset($parts[1])) && ($parts[0] == $field)) {
            $parsedList[$parts[1]] = $value;
        }
    }

    return $parsedList;
}


// searches a given dataset for one or more repeating fields and extracts them,
// converting a single dimensional array into a multi dimensional array
// if an index is specified - returns only that row
// fieldName(separator)uniqueIndex eg ownerSharePercentage_1, ownerSharePercentage_2
function extractFields($data, $fieldList, $separator = '_', $removeCommas = false)
{
    $parsedList = [];
    if (is_array($fieldList)) {
        foreach ($data as $key => $value) {
            $keyParts = explode($separator, $key);
            if ((isset($keyParts[1])) && ($keyParts[1] != '')) {
                foreach ($fieldList as $fieldName) {
                    if ($keyParts[0] == $fieldName) {
                        $parsedList[$keyParts[1]][$keyParts[0]] = $removeCommas ? str_replace(',', '', $value) : $value;
                    }
                }
            }
        }
    } else {
        foreach ($data as $key => $value) {
            if (strpos($key, $fieldList) === 0) {
                $parsedList[$key] = $value;
            }
        }
    }

    return $parsedList;
}

function parseFields($dataSet, $template, $separator = null)
{
    $parsedList = [];

    if (is_array($dataSet)) {
        foreach ($dataSet as $key => $dataRow) {
            $parsedString = $template;
            foreach ($dataRow as $fieldName => $fieldValue) {
                $parsedString = str_replace('{' . $fieldName . '}', $fieldValue, $parsedString);
            }

            $parsedList[$key] = $parsedString;
        }

        return ($separator === null) ? $parsedList : implode($separator, $parsedList);
    }


}

function duplicateKeyExists($dataSet, $fieldName, $matchValue, $caseInsensitive = false)
{
    $found = 0;
    foreach ($dataSet as $dataRow) {
        if ($caseInsensitive) {
            if (strtoupper($dataRow[$fieldName]) === strtoupper($matchValue)) {
                $found++;
            }
        } elseif ($dataRow[$fieldName] == $matchValue) {
            $found++;
        }
    }

    return $found > 1;
}


function convertDictionaryToArray(&$dictionary)
{
    // converts each association value to an array element
    $array = [];
    $size = count($dictionary ?? []);
    for ($i = 0; $i < $size; $i++) {
        foreach ($dictionary[$i] as $val) {
            $array[] = $val;
        }
    }

    return $array;
}

function bindGetAttributesTo(&$object)
{
    foreach ($_GET as $key => $val) {
        $object[$key] = $val;
    }
}

function bindPostAttributesTo(&$object)
{
    foreach ($_POST as $key => $val) {
        $object[$key] = $val;
    }
}

function bindAttributes(&$object, $source)
{
    foreach ($source as $key => $val) {
        $object[$key] = $val;
    }
}


function queryStringValue($key, $default = null)
{
    if (isset($_GET[$key])) {
        return htmlspecialchars($_GET[$key]);
    }

    return htmlspecialchars($default);
}

function queryStringKeyExists($key)
{
    return array_key_exists($key, $_GET);
}

function checkArray(&$haystack, $needle)
{
    $results = [];
    foreach ($needle as $id => $item) {
        if (array_key_exists($id, $haystack)) {
            $results[$id] = $item;
        }
    }

    return $results;
}

function postValue($key, $default = null)
{
    if (isset($_POST[$key])) {
        return $_POST[$key];
    }

    return $default;
}

function postKeyExists($key)
{
    return array_key_exists($key, $_POST);
}

function notNumeric($object)
{
    return $object == null || strlen($object) == 0 || ! is_numeric($object);
}

function isPositiveNumber($object)
{
    return is_numeric($object) && ($object >= 0);
}

function isPercentage($object)
{
    return is_numeric($object) && valueInRange($object, 0, 100);
}


function isValid($object, $filter = TEXT_LOOSE, $allowNulls = true)
{
    if (($allowNulls == false) && (($object == null) || ($object == ''))) {
        return false;
    }

    return preg_match($filter, $object);
}

function noErrors($errorList)
{
    return empty($errorList);
}

function listDo(&$list, $function)
{
    foreach ($list as $key => $val) {
        $function($list[$key]);
    }

    return $list;
}

// Serialize an object. Compress it, base64 it.
function encodeObject($object)
{
    return base64_encode(gzdeflate(serialize($object), 9));
}

function decodeObject($object)
{
    /** @noinspection UnserializeExploitsInspection */
    return unserialize(gzinflate(base64_decode($object)));
}

function encodeParameter($data)
{
    return urlencode(base64_encode($data));
}

function decodeParameter($data)
{
    return base64_decode(urldecode($data));
}

function dumpAndDie(&$object)
{
    /** @noinspection ForgottenDebugOutputInspection */
    var_dump($object);
    exit('');
}


function mapByKey($data, $key)
{
    $keyedList = [];
    if (is_array($data)) {
        foreach ($data as $row) {
            $keyedList[$row[$key]] = $row;
        }
    }

    return $keyedList;
}


// -- compares the columns of two rows and returns false if the rows dont match across all columns
function compareRow($dataRow1, $dataRow2, $fields = [])
{
    if (count($fields ?? []) > 0) {
        foreach ($fields as $fieldName) {
            if ($dataRow1[$fieldName] != $dataRow2[$fieldName]) {
                return false;
            }
        }
    }

    return true;
}

// removes trailing 0's from a number
function cleanNumber($number)
{
    return $number * 1;
}

function setProgressBar($percentage = 0, $task = '')
{
    $_SESSION['progressBar'] = $percentage;
    $_SESSION['progressTask'] = $task;
}

function getProgressBar()
{
    return $_SESSION['progressBar'];
}

function getProgressTask()
{
    return $_SESSION['progressTask'];
}

// function isEmpty($string)
// {
//	return ($string === null || strlen($string) == 0);
// }

function isEmptyArray($array) // ---Note: Can be replaced with empty()
{
    return is_array($array) && (count($array) == 1) && ($array[0] == null);
}

function isPostback()
{
    return $_SERVER['HTTP_X_REQUESTED_WITH'] == 'XMLHttpRequest' || isset($_REQUEST['postBackOverride']);
}

function isExpandedControl()
{
    return (isset($_POST['controlContainer'])) || (isset($_GET['controlContainer']));
}

function getControlContainer()
{
    if (isset($_POST['controlContainer'])) {
        return $_POST['controlContainer'];
    }

    if (isset($_GET['controlContainer'])) {
        return $_GET['controlContainer'];
    }


}

function isAjax()
{
    return $_SERVER['HTTP_X_REQUESTED_WITH'] == 'XMLHttpRequest';
}

function valueInRange($value, $floor, $ceiling)
{
    return $value >= $floor && $value <= $ceiling;
}

function redirectUser($url)
{
    header('Location: ' . $url);
    exit();
}

function redirectCommand($command = DEFAULT_COMMAND, $module = DEFAULT_MODULE)
{
    header('Location: ?module=' . $module . 'command=' . $command);
    exit();
}

function redirectURL($url)
{
    header('Location: ' . $url);
    exit();
}


function clientSideRedirect($url)
{
    echo "<script type=\"text/javascript\">\n";
    echo 'window.location.href = "' . $url . '";';
    echo "</script>\n";
    echo "If your browser isn't redirected automatically, please <a href=\"{$url}\">click here</a>\n";
    exit;
}


function loggedIn()
{
    global $sess;
    if (! $sess->exists()) {
        return false;
    }

    return (bool) $sess->get('registered');
}

function dispatchRequest()
{
    $command = queryStringValue('command', DEFAULT_COMMAND);
    $module = queryStringValue('module', DEFAULT_MODULE);

    if (! loggedIn()) {
        $command = 'login';
        $module = 'home';
    }

    if (ENVIRONMENT == 'LIVE') {
        try {
            executeCommand($command, $module);
        } catch (Error $e) {
            myErrorHandlerV2(1, $e->getMessage(), $e->getFile(), $e->getLine());
        }
    } else {
        executeCommand($command, $module);
    }
}


function clientDetails()
{
    return file_get_contents(SYSTEMURL . "\details.html", 'r');
}


/*define('FILE_APPEND', 1);
function file_put_contents($n, $d, $flag = false) {
    $mode = ($flag == FILE_APPEND || strtoupper($flag) == 'FILE_APPEND') ? 'a' : 'w';
    $f = @fopen($n, $mode);
    if ($f === false) {
        return 0;
    } else {
        if (is_array($d)) $d = serialize($d);
        $bytes_written = fwrite($f, $d);
        fclose($f);
        return $bytes_written;
    }
}*/


function fetchCommand($command, $module = null)
{
    ob_start();
    executeCommand($command, $module);

    return ob_get_clean();
}


function myErrorHandler($errno, $errstr, $errfile, $errline)
{
    $trace = debug_backtrace();
    $_message = '';
    if (is_array($trace)) {
        foreach ($trace as $file) {
            $_message .= 'Backtrace: ' . $file['file'] . ' - Line ' . $file['line'] . ' - ' . $file['function'] . '()' . "\n";
        }
    }

    $_message .= 'Error: ' . $errstr;

    if ($errno === E_PARSE) {
        echo '<div style="z-index:9999" class="warningBox"><b>An Error Has Occurred</b><br/>We apologise for the inconvenience - Tracc has been notified and this will be resolved as soon as possible.<br/></div>';
        sendMail(
            ADMINMAIL_ADDRESS,
            ADMINMAIL_NAME,
            $errno . ' ' . $errstr . ' in ' . $errfile . ' on line ' . $errline,
            "{$errno}:  {$errfile} ({$errline})"
        );
    }

    if (($errno & (E_ERROR | E_PARSE)) !== 0) {
        sendMail(
            ADMINMAIL_ADDRESS,
            ADMINMAIL_NAME,
            $errno . ' ' . $errstr . ' in ' . $errfile . ' on line ' . $errline,
            "{$errno}:  {$errfile} ({$errline})"
        );
        logError($_message);
    }
}

// new error hanlder with database error log
function myErrorHandlerV2($errno, $errstr, $errfile, $errline, $returnErrorCode = false)
{
    if ($errno == '1' || $errno == '4') {
        if ((DEBUG || $_SESSION['super'] == 1) && ! $returnErrorCode) {
            $msg = 'code : ' . $errno;
            $msg .= '<br>file : ' . $errfile . '<br>line : ' . $errline;
            $msg .= '<br>mess : ' . $errstr;
            echo $msg . '<br>';
            $trace = debug_backtrace();
            $_message = '';
            if (is_array($trace)) {
                foreach ($trace as $file) {
                    $_message .= 'Backtrace: ' . $file['file'] . ' - Line ' . $file['line'] . ' - ' . $file['function'] . '()' . "\n";
                }
            }

            $_message .= 'Error: ' . $errstr;
            echo $_message;
        } else {
            $viewError = new UserControl(userViews(), '/errorHandler.html');

            $codeHeader = 'C8';
            $codeDateCode = date('y') . date('m') . date('d');
            $codeTime = date('H') . date('i') . date('s');

            $getErrorCtr = getErrorCtr();
            $codeCtr = $getErrorCtr + 1;
            if ($codeCtr < 10) {
                $codeCtr = '0' . $codeCtr;
            }

            $errorCode = $codeHeader . '-' . $codeDateCode . $codeTime . $codeCtr;
            $currentDB = $_SESSION['currentDB'];
            $user_type = $_SESSION['user_type'];
            $username = $_SESSION['un'];
            $moduleError = $_REQUEST['module'];
            $commandError = $_REQUEST['command'];
            $urlError = $_SERVER['REQUEST_URI'];
            $errorType = 'PHP';
            $error_message = 'File: ' . $errfile . ' Line: ' . $errline . ' Msg: ' . $errstr;
            $checkErrorEx = dbCheckErrorLog($errorType, $urlError, $user_type, $currentDB, $error_message);
            $o = [];
            $o['error_code'] = $errorCode;
            $o['error_type'] = $errorType;
            $o['user_type'] = $user_type;
            $o['database_name'] = $currentDB;
            $o['url'] = $urlError;
            $o['error_message'] = $error_message;
            if ($checkErrorEx) {
                $errorCode = $checkErrorEx;
                $o['error_code'] = $errorCode;
                dbUpdateErrorLog($o);
            } else {
                dbAddErrorLog($o);
            }

            if ($returnErrorCode) {
                return $errorCode;
            } else {
                $viewError->items['errorCode'] = $errorCode;
                if (isset($_SESSION['errorURL'])) {
                    if ($_SESSION['errorURL'] != $urlError) {
                        $viewError->render();
                    } else {
                        $_SESSION['errorURL'] = $urlError;
                    }
                } else {
                    $viewError->render();
                    $_SESSION['errorURL'] = $urlError;
                    $viewError->render();
                }
            }
        }
    }


}

function SchedulerErrorHandler($errno, $errstr, $errfile, $errline)
{
    if (DEBUG) {
        $trace = debug_backtrace();
        $_message = '';
        if (is_array($trace)) {
            foreach ($trace as $file) {
                $_message .= 'Backtrace: ' . $file['file'] . ' - Line ' . $file['line'] . ' - ' . $file['function'] . '()' . "\n";
            }
        }

        $_message .= 'Error: ' . $errstr;
        echo $_message;
    } else {
        $viewError = new UserControl(userViews(), '/errorHandler.html');

        $codeHeader = 'C8';
        $codeDateCode = date('y') . date('m') . date('d');
        $codeTime = date('H') . date('i') . date('s');

        $getErrorCtr = getErrorCtr();
        $codeCtr = $getErrorCtr + 1;
        if ($codeCtr < 10) {
            $codeCtr = '0' . $codeCtr;
        }

        $errorCode = $codeHeader . '-' . $codeDateCode . $codeTime . $codeCtr;
        $currentDB = $_SESSION['currentDB'];
        $user_type = $_SESSION['user_type'];
        $username = $_SESSION['un'];
        $moduleError = $_REQUEST['module'];
        $commandError = $_REQUEST['command'];
        $urlError = $_SERVER['REQUEST_URI'];
        $errorType = 'PHP';
        $error_message = 'File: ' . $errfile . ' Line: ' . $errline . ' Msg: ' . $errstr;
        $checkErrorEx = dbCheckErrorLog($errorType, $urlError, $user_type, $currentDB, $error_message);
        $o = [];
        $o['error_code'] = $errorCode;
        $o['error_type'] = $errorType;
        $o['user_type'] = $user_type;
        $o['database_name'] = $currentDB;
        $o['url'] = $urlError;
        $o['error_message'] = $error_message;
        if ($checkErrorEx) {
            $errorCode = $checkErrorEx;
            $o['error_code'] = $errorCode;
            dbUpdateErrorLog($o);
        } else {
            dbAddErrorLog($o);
        }

        return $errorCode;
    }


}

function SchedulerErrorHandler2($errorMessage)
{
    if (DEBUG) {
        $trace = debug_backtrace();
        $_message = '';
        if (is_array($trace)) {
            foreach ($trace as $file) {
                $_message .= 'Backtrace: ' . $file['file'] . ' - Line ' . $file['line'] . ' - ' . $file['function'] . '()' . "\n";
            }
        }

        $_message .= 'Error: ' . $errorMessage;
        echo $_message;

        return true;
    } else {
        $viewError = new UserControl(userViews(), '/errorHandler.html');

        $codeHeader = 'C8';
        $codeDateCode = date('y') . date('m') . date('d');
        $codeTime = date('H') . date('i') . date('s');

        $getErrorCtr = getErrorCtr();
        $codeCtr = $getErrorCtr + 1;
        if ($codeCtr < 10) {
            $codeCtr = '0' . $codeCtr;
        }

        $errorCode = $codeHeader . '-' . $codeDateCode . $codeTime . $codeCtr;
        $currentDB = $_SESSION['currentDB'];
        $user_type = $_SESSION['user_type'];
        $username = $_SESSION['un'];
        $moduleError = $_REQUEST['module'];
        $commandError = $_REQUEST['command'];
        $urlError = $_SERVER['REQUEST_URI'];
        $errorType = 'Scheduler';

        $checkErrorEx = dbCheckErrorLog($errorType, $urlError, $user_type, $currentDB, $errorMessage);
        $o = [];
        $o['error_code'] = $errorCode;
        $o['error_type'] = $errorType;
        $o['user_type'] = $user_type;
        $o['database_name'] = $currentDB;
        $o['url'] = $urlError;
        $o['error_message'] = $errorMessage;
        if ($checkErrorEx) {
            $errorCode = $checkErrorEx;
            $o['error_code'] = $errorCode;
            dbUpdateErrorLog($o);
        } else {
            dbAddErrorLog($o);
        }

        return $errorCode;
    }
}


function logItem($datafile, $data)
{
    return false;
}

function logSQLError($data)
{
    return false;
}


function logError($data)
{
    return false;
}

function logData($data)
{
    return false;
}

function logSQL($data)
{
    return false;
}

function logScheduler($data)
{
    return true;
}

function sum(&$from, &$to, $fields = null)
{
    /*  echo '<br/>';
       echo 'from > ' . print_array($from) . '<br/>';
       echo 'to  > ' . print_array($to) . '<br/>'; */

    if ($fields && is_array($fields) && (count($fields) > 0)) {
        foreach ($fields as $field) {
            if (is_numeric($from[$field])) {
                $to[$field] += ((float) $from[$field]);
            }
        }
    } else {
        foreach ($from as $id => $value) {
            if (is_numeric($value)) {
                $to[$id] += ((float) $value);
            }
        }
    }
}

function total(&$from, &$to, $fields = null)
{
    if (is_array($from)) {
        foreach ($from as $f) {
            foreach ($fields as $field) {
                $to[$field] += (float) $f[$field];
            }
        }
    }
}

function checkValuesFor(&$array, $fieldList)
{
    foreach ($fieldList as $f) {
        $value = floatval($array[$f]);
        if (! empty($value)) {
            return true;
        }
    }

    return false;
}

function variance($a, $b)
{
    $a = sprintf('%f', $a);
    $b = sprintf('%f', $b);

    return bcsub((float) $a, (float) $b, 2);
}

function variancePercentage($variance, $amount)
{
    if ($amount != 0) {
        return round(($variance / $amount) * 100, 2);
    } else {
        return 100;
    }
}

function variancePercentageReturnZero($variance, $amount)
{
    if ($variance == 0 && $amount == 0) {
        return 0;
    }

    if ($amount != 0) {
        return round(($variance / $amount) * 100, 2);
    } else {
        return 100;
    }
}

function varianceDecimal($variance, $amount)
{
    if ($amount != 0) {
        return round(($variance / $amount), 2);
    } else {
        return 1;
    }
}


function executeCommand($command, $module = null)
{
    $userPermission = new UserPermission();
    $userPermission->userHasPageAccess();

    global $context, $dispatch;
    static $executionCount = 0;

    if ($module === null) {
        $module = (isset($context['module'])) ? $context['module'] : 'home';
    } else {
    }

    $retailPlusRoute = dbGetPageRetailSalesPlus();
    foreach ($retailPlusRoute as $page) {
        $retail_plus_validation[] = $page['route'];
    }

    $db = dbGetDatabase($_SESSION['clientID']);
    $retailSalesPlus = $db['retail_sales_plus'];
    if ($retailSalesPlus == false && ! empty($retail_plus_validation)) {
        foreach ($retail_plus_validation as $page_invalid) {
            $page_invalid = str_replace('&', '=', $page_invalid);
            $page_invalid_array = explode('=', $page_invalid);
            if (in_array($command, $page_invalid_array)) {
                header('Location: ?module=home&command=home');
            }
        }
    }

    $restrict_owner = ownerAccessRestriction($command);
    if ($_SESSION['user_sub_type'] == 'OWA' && $restrict_owner[0]['cnt']) {
        header('Location: ?module=home&command=home');
    }

    $file = ($module != '') ? sprintf('commands/%s/%s.php', $module, $command) : sprintf('commands/%s.php', $command);

    $dbFile = ($module != '') ? sprintf('data' . userData() . '/%s/dbInterface.php', $module) : 'data' . userData(
    ) . '/dbInterface.php';


    $function = str_replace('.', '_', $command);
    if (! function_exists($function)) {
        if (file_exists($file)) {
            if (file_exists($dbFile)) {
                include_once $dbFile;
            } else {
                $context['last_error'] = 'ERROR: No Database file present';
            }

            include_once $file;
        } else {
            if (DEBUG) {
                logError($context);
            }

            executeCommand('error', DEFAULT_MODULE);
            exit();
        }
    }

    $executionCount += 1;
    if ($executionCount > 5000) {
        exit('Stack Overflow');
    }

    $pathCheck = strpos($module, '/');
    if (! isset($context['module'])) {
        if ($pathCheck !== false) {
            $path = substr($module, 0, $pathCheck - 1);
            $context['module'] = $path;
        } else {
            $context['module'] = $module;
        }
    }

    $dispatch['command'] = $command;

    return $function($context);
}

function cdf_displayBPay($country = null)
{
    return $country == 'AU';
}

function cdf_displayLabel($field, $country = null)
{
    $countrydefaults = new countrydefaults($country);

    return $countrydefaults->displayLabel($field);
}

function cdf_isShown($field, $country = null)
{
    $countrydefaults = new countrydefaults($country);

    return $countrydefaults->isShown($field);
}

function cdf_validate($value, $field, $country = null)
{
    $countrydefaults = new countrydefaults($country);

    return $countrydefaults->validate($value, $field);
}

function cdf_isAU()
{
    return $_SESSION['country_code'] == 'AU';
}

function cdf_validate_postcode($value, $country = null)
{
    $countrydefaults = new countrydefaults($country);

    return $countrydefaults->validatePostCode($value);
}

function cdf_validCountryCode($country)
{ // returns a valid country code (ex. if the specified country does not exist in the country_default_settings.json, it will return the client country)
    $countrydefaults = new countrydefaults($country);

    return $countrydefaults->validCountryCode;
}

function formatNumber($str, $decimal_places = '2', $decimal_padding = '0')
{
    /* firstly format number and shorten any extra decimal places */
    /* Note this will round off the number pre-format $str if you dont want this fucntionality */
    $str = number_format($str, $decimal_places, '.', '');    // will return 12345.67
    $number = explode('.', $str);
    $number[1] = (isset($number[1])) ? $number[1] : ''; // to fix the PHP Notice error if str does not contain a decimal placing.
    $decimal = str_pad($number[1], $decimal_places, $decimal_padding);

    return (float) $number[0] . '.' . $decimal;
}

function isAdmin(&$context)
{
    if ($_SESSION['user_type'] == 'A') {
        return true;
    }

    $context['last_error'] = 'You do not have permission to access this resource';

    return false;
}

function titleCase($string)
{
    $len = strlen($string);
    $i = 0;
    $last = '';
    $new = '';
    $string = strtoupper($string);
    while ($i < $len) {
        $char = substr($string, $i, 1);
        $new .= (preg_match('/[A-Z]/', $last)) ? strtolower($char) : strtoupper($char);
        $last = $char;
        $i++;
    }

    return $new;
}

function prune(&$data)
{
    if (is_array($data)) {
        foreach ($data as $key => $value) {
            if (is_array($value)) {
                $data[$key] = prune($value);
            } elseif (! is_object($value)) {
                $data[$key] = trim($value);
            }
        }

        return $data;
    } elseif (! is_object($data)) {
        return trim($data);
    } else {
        return;
    }
}

function prettify($formData, $exceptions = null)
{
    if (is_array($formData)) {
        $pruned = [];
        foreach ($formData as $key => $value) {
            $pruned[$key] = ucwords(strtolower($value));
        }

        return $pruned;
    } else {
        return;
    }
}

function selectAll($formName)
{
    return '<SCRIPT LANGUAGE="JavaScript">
function doCheckAll()
{
  with (document.' . $formName . ') {
	for (var i=0; i < elements.length; i++) {
		if (elements[i].type == \'checkbox\')
		   elements[i].checked = true;
	}
  }
}

</SCRIPT>';
}

function sendMail(
    $toAddress,
    $toName,
    $message,
    $subject,
    $attachments = false,
    $replyTo = null,
    $identifier = null,
    $forceLive = false,
    $ccMe = false,
    $log = true,
    $ccToSessionUser = true,
    $emailSpecified = [],
    $batchNo = ''
) {

    $initialToAddress = $toAddress;
    $initialCCAddress = $ccMe;
    $fromAddress = SYSTEMMAIL_ADDRESS;
    $fromName = SYSTEMMAIL_NAME;
    $replyAddress = ($replyTo) ? $replyTo : $fromAddress;
    $replyName = ($replyTo) ? '' : $fromName;

    $usePostmark = false;
    $attachmentDelivery = 'standard';
    $attachmentValidity = '2999-12-31';
    $attachmentMaxAllow = 8.6;
    $postmarkID = null;
    $links = [];
    $attachmentsSize = 0;
    if (! empty($attachments) && is_array($attachments)) {
        foreach ($attachments as $key => $attachment) {
            // Check if file is valid and readable
            if (isset($attachment['file']) && is_file($attachment['file']) && is_readable($attachment['file'])) {
                $fileSize = filesize($attachment['file']);

                // Check for zero-sized files
                if ($fileSize > 0) {
                    $attachmentsSize += $fileSize;
                } else {
                    // Zero-sized file detected, remove it
                    unset($attachments[$key]);
                }
            } else {
                // Invalid or unreadable file, remove it
                unset($attachments[$key]);
            }
        }
    }

    $emailSetting = dbGetEmailSettingsCompiled($_SESSION['databaseID']);
    if ($emailSetting) {
        if ($emailSetting['attachment_delivery']) {
            $attachmentSizeMB = round($attachmentsSize / 1024 / 1024, 4);
            if (($emailSetting['attachment_delivery'] == 'exceed' && ($attachmentSizeMB > $attachmentMaxAllow)) || $emailSetting['attachment_delivery'] == 'link') {
                $attachmentDelivery = 'link';
            }
        }

        if ($emailSetting['attachment_validity'] && $attachmentDelivery === 'link') {
            $current = date('Y-m-d');

            if ($emailSetting['attachment_validity'] != 0) {
                $attachmentValidity = date('Y-m-d', strtotime('+' . $emailSetting['attachment_validity'] . ' days'));
            }
        }

        if ($emailSetting['use_postmark'] == 1 && $emailSetting['postmark_domain'] && $emailSetting['server_token'] && $emailSetting['DKIM_status'] == 1) {
            $usePostmark = true;
            $fromAddress = $emailSetting['postmark_send_from'];
            $replyName = $emailSetting['postmark_send_name'];
            $fromName = $emailSetting['postmark_send_name'];
            $replyAddress = $emailSetting['postmark_reply_to'];
        } elseif ($emailSetting['use_postmark'] == 0 && $emailSetting['smtp_domain']) {
            $fromAddress = $emailSetting['smtp_send_from'];
            $replyName = $emailSetting['smtp_send_name'];
            $fromName = $emailSetting['smtp_send_name'];
            $replyAddress = $emailSetting['smtp_reply_to'];
        }
    } else {
        $clientEmailConfig = dbGetClientEmailConfig();
        if ($clientEmailConfig['fromEmail']) {
            $fromAddress = $clientEmailConfig['fromEmail'];
        }

        if ($clientEmailConfig['fromName']) {
            $replyName = $clientEmailConfig['fromName'];
            $fromName = $clientEmailConfig['fromName'];
        }

        if ($clientEmailConfig['replyTo']) {
            $replyAddress = $clientEmailConfig['replyTo'];
        }
    }

    $letter_history_id = null;
    if (! empty($emailSpecified)) {
        if (array_key_exists('sendFrom', $emailSpecified) && trim($emailSpecified['sendFrom']) !== '') {
            $fromAddress = $emailSpecified['sendFrom'];
        }

        if (array_key_exists('fromName', $emailSpecified) && trim($emailSpecified['fromName']) !== '') {
            $replyName = $emailSpecified['fromName'];
            $fromName = $emailSpecified['fromName'];
        }

        if (array_key_exists('replyTo', $emailSpecified) && trim($emailSpecified['replyTo']) !== '') {
            $replyAddress = $emailSpecified['replyTo'];
        }

        if (array_key_exists('letter_history_id', $emailSpecified) && trim(
            $emailSpecified['letter_history_id']
        ) !== '') {
            $letter_history_id = $emailSpecified['letter_history_id'];
        }
    }


    $original_address = '';
    $original_cc_address = '';
    if ($_SESSION['development'] && $forceLive === false || ENVIRONMENT == 'DEV') {
        $original_address = $toAddress;
        $toAddress = ADMINMAIL_ADDRESS;

        if ($ccMe && (isValid($ccMe, TEXT_EMAIL, false) || is_array($ccMe))) {
            $original_cc_address = $ccMe;
            $ccMe = ADMINMAIL_CC_ADDRESS;
        }
    }


    $mail = new PHPMailer();
    $mail->IsSMTP();
    // Enable SMTP debugging
    // 0 = off (for production use)
    // 1 = client messages
    // 2 = client and server messages
    $mail->SMTPDebug = 0;
    // Ask for HTML-friendly debug output
    $mail->Debugoutput = 'html';
    $mail->Host = 'localhost';
    $mail->Port = 25;
    $mail->SMTPAuth = false;
    // $mail->Username = '<EMAIL>';
    // $mail->Password = 'yourpassword';

    $mail->SetFrom($fromAddress, $fromName);

    $postmarkFrom = $fromName . ' <' . $fromAddress . '>';

    // Set an alternative reply-to address
    $mail->AddReplyTo($replyAddress, $replyName);
    $postmarkReplyTo = $replyAddress;

    // Set who the message is to be sent to

    $postmarkToArray = [];
    $addresses = preg_split("/[\s,;]+/", $toAddress);
    foreach ($addresses as $address) {
        $to = trim($address);
        $to = strtolower(str_replace("'", '', $to));
        if (isValid($to, TEXT_EMAIL, false)) {
            $mail->AddAddress($to, $to);
            $postmarkToArray[] = $to;
        }
    }

    $postmarkCCArray = [];
    $original_cc_address_array = [];
    if ($ccMe && $ccMe !== null) {
        if (! is_array($ccMe)) {
            $ccMe = explode(';', $ccMe);
        }

        if (! is_array($original_cc_address)) {
            $original_cc_address = explode(';', $original_cc_address);
        }

        foreach ($ccMe as $ccAdd) {
            $ccAdd = strtolower(str_replace("'", '', $ccAdd));
            if (isValid($ccAdd, TEXT_EMAIL, false)) {
                if ($toAddress) {
                    $mail->AddCC($ccAdd);
                    $postmarkCCArray[] = $ccAdd;
                } else {
                    $mail->AddAddress($ccAdd, $ccAdd); // just send cc email if toAddress is invalid
                    $postmarkToArray[] = $ccAdd;
                }
            } elseif ($ccToSessionUser) {
                /** Note: $ccToSessionUser by default is true. When sending diary reminder emails, this parameter will be set to false  */
                if ($toAddress) {
                    $mail->AddCC($_SESSION['email']);
                    $postmarkCCArray[] = $_SESSION['email'];
                } else {
                    $mail->AddAddress(
                        $_SESSION['email'],
                        $_SESSION['email']
                    ); // just send to user if toAddress is invalid
                    $postmarkToArray[] = $_SESSION['email'];
                }
            }
        }

        foreach ($original_cc_address as $ccAdd) {
            if (isValid($ccAdd, TEXT_EMAIL, false)) {
                $original_cc_address_array[] = $ccAdd;
            } elseif ($ccToSessionUser) {
                $original_cc_address_array[] = $_SESSION['email'];
            }
        }
    }

    $postmarkCC = empty($postmarkCCArray) ? '' : implode(';', $postmarkCCArray);

    $original_cc_address = empty($original_cc_address_array) ? '' : implode(';', $original_cc_address_array);

    // Set content-type and charset headers
    $mail->addCustomHeader('MIME-Version: 1.0');
    $mail->addCustomHeader('Content-Type: text/html; charset=UTF-8');

    // Set the subject line
    $mail->Subject = $subject;
    // Read an HTML message body from an external file, convert referenced images to embedded, convert HTML into a basic plain-text alternative body

    $_message = $message;
    $mail->MsgHTML($message);
    // Replace the plain text body with one created manually
    $mail->AltBody = 'This is a HTML message. Please use a HTML capable mail program to read this message.';
    // Attach an image file
    if ($attachments) {
        $S3 = new S3FileHandler(POSTMARK_AWS_S3_BUCKET);
        $C8File = new Cirrus8FileHandler();
        foreach ($attachments as $attachment) {
            if (is_file($attachment['file'])) {
                if ($attachmentDelivery === 'standard') {
                    $mail->AddAttachment($attachment['file'], $attachment['file_name']);
                } elseif ($attachmentDelivery === 'link') {
                    $doc = [];
                    $doc['documentTitle'] = urlencode($attachment['file_name']);
                    $doc['filename'] = str_replace(REPORTPATH, '', $attachment['file']);
                    $doc['primaryID'] = '';
                    $doc['secondaryID'] = null;
                    $doc['documentType'] = '0';
                    $doc['createdBy'] = $_SESSION['un'];
                    $docID = dbAddAttachmentAsDocument($doc);
                    $S3File = $S3->copyFile($docID, 'Attachments', basename($doc['filename']), false);
                    $postmarkAttachmentURL = $C8File->getDownloadUrl(
                        $docID,
                        null,
                        $attachmentValidity,
                        'external',
                        true,
                        'postmark'
                    );
                    $links[] = $postmarkAttachmentURL;
                }
            }
        }

        if (! empty($links)) {
            $_message = AddAttachmentLink($_message, $links, $attachmentValidity);
            $attachments = [];

            if (! $usePostmark) {
                $mail->MsgHTML($_message);
            }
        }
    }

    $processedToAddress = $initialToAddress;
    $processedCCAddress = $initialCCAddress;
    $is_test_environment = false;
    if ($_SESSION['development'] && $forceLive === false || ENVIRONMENT == 'DEV') {
        $is_test_environment = true;
    }

    if ($is_test_environment) {
        $processedToAddress = ADMINMAIL_ADDRESS;
        if ($ccMe && (isValid($ccMe, TEXT_EMAIL, false) || is_array($ccMe))) {
            $processedCCAddress = ADMINMAIL_CC_ADDRESS;
        }
    }

    $processedToAddress = sanitiseEmailAddress($processedToAddress);
    sanitiseEmailAddress($processedCCAddress);
    if (empty($batchNo)) {
        $batchNo = '';
    }

    $insertArray = [];
    $insertArray['database_id'] = $_SESSION['databaseID'];
    $insertArray['created_by'] = $_SESSION['userID'];
    $insertArray['sender_email'] = $fromAddress;
    $insertArray['sender_name'] = $fromName;
    $insertArray['subject'] = $subject;
    $insertArray['body'] = $_message;
    $queueEmailPageSourceId = dbGetEmailPageSourceId();
    $emailId = dbGetInsertQueueEmailId([
        $insertArray['database_id'],
        $insertArray['created_by'],
        $queueEmailPageSourceId,
        $batchNo,
        $insertArray['sender_email'],
        $insertArray['sender_name'],
        $insertArray['subject'],
        $insertArray['body'],
        $identifier,
    ]);
    if (! $emailId) {
        return false;
    }

    foreach ($processedToAddress['email_address'] as $row_data) {
        dbGetInsertQueueEmailRecipientId([
            $insertArray['created_by'],
            $emailId,
            $row_data,
            $initialToAddress,
            'recipient',
        ]);
        $email_activity_log_id = dbGetInsertQueueEmailActivityLog($emailId, $row_data);
    }

    foreach ($postmarkCCArray as $row_data) {
        dbGetInsertQueueEmailRecipientId([
            $insertArray['created_by'],
            $emailId,
            $row_data,
            $initialToAddress,
            'cc',
        ]);
    }

    if ($attachments) {
        foreach ($attachments as $attachment) {
            $file = ($attachment['db_filename'] ?? $attachment['file']);
            $fileName = ($attachment['file_name'] ?? null);
            $contentType = ($attachment['content_type'] ?? null);
            dbGetInsertQueueEmailAttachmentId([
                $insertArray['created_by'],
                $emailId,
                $fileName,
                $file,
                $contentType,
            ]);
        }
    }

    if (dbGetEmailSettingValueByCategoryAndField($_SESSION['databaseID'], 'config', 'QUEUE_ENABLED') == '1') {

        // First ensure you have the library installed via composer:
        // composer require rmccue/requests

        $params = [
            'app_key' => $_SESSION['sso_key'],
            'user_type' => $_SESSION['user_type'],
            'queueId' => $emailId,
        ];

        $headers = [
            'Authorization' => 'Bearer ' . $_SESSION['sysApi'],
        ];

        $options = [
            'verify' => LOCAL_SSL_ENABLED ? false : SYSTEMPATH . cacert,
        ];

        try {
            $response = Requests::post(
                c8_api . 'api/administration/email/process/dispatch-email-job-by-queue-id',
                $headers,
                $params,
                $options
            );

            if (! $response->success) {
                throw new Exception('Request failed: ' . $response->status_code);
            }

        } catch (Exception $e) {
            exit('Request Error: ' . $e->getMessage());
        }

        return true;
    }

    $error = null;
    if ($usePostmark) {
        foreach ($postmarkToArray as $recipient) {
            $recipient = strtolower(str_replace("'", '', $recipient));
            $result = postmarkSendEmail(
                $emailSetting,
                $postmarkFrom,
                $recipient,
                $subject,
                $_message,
                $mail->AltBody,
                $postmarkReplyTo,
                $postmarkCC,
                null,
                $attachments,
                $links,
                $postmarkToArray
            );

            $postmarkID = @$result['postmarkID'];


            if (@$result['code'] != 0) {
                $error = @$result['message'];
                if ($error) {
                    dbUpdateErrorQueuedEmailActivityLog($emailId, $recipient, $error);
                    //          dbUpdateQueuedEmailRecipientStatus($recipient, $emailId, $error);
                }
            }

            if (! $postmarkID) {
                dbUpdateQueuedEmailRecipientStatus($recipient, $emailId, 'postmark failed.');
            }

            if ($log) {
                if ($_SESSION['development'] && $forceLive === false || ENVIRONMENT == 'DEV') {
                    $getLastEmailID = dbLogEmailDevelopment(
                        $recipient,
                        null,
                        $subject,
                        $error,
                        null,
                        $identifier,
                        $original_address,
                        $original_cc_address,
                        $postmarkCC,
                        $fromAddress,
                        $postmarkID
                    );
                } else {
                    $getLastEmailID = dbLogEmail(
                        $recipient,
                        null,
                        $subject,
                        $error,
                        null,
                        $identifier,
                        $fromAddress,
                        $postmarkID,
                        $postmarkCC
                    );
                }

                // for attachment per emailID
                if ($attachments) {
                    foreach ($attachments as $attachment) {
                        $file = ($attachment['db_filename'] ?? $attachment['file']);
                        $fileName = ($attachment['file_name'] ?? null);
                        $contentType = ($attachment['content_type'] ?? null);

                        dbInsertEmailAttachment($getLastEmailID, $file, $fileName, $contentType);
                    }
                }

                if ($letter_history_id) {
                    dbInsertLetterHistoryEmailLog($getLastEmailID, $letter_history_id);
                }

                if ($postmarkID) {
                    dbUpdateQueuedEmailSentDetails($emailId);
                }

                //        dbUpdateQueuedEmailRecipientStatus($recipient, $emailId, null);
                dbUpdateQueuedEmailActivityLog($emailId, $recipient, $getLastEmailID, 2);
            }
        }
    } else {
        $error = ($mail->Send()) ? null : $mail->ErrorInfo;
        if ($error) {
            dbUpdateErrorQueuedEmailActivityLog($emailId, $toAddress, $error);
            dbUpdateQueuedEmailRecipientStatus($toAddress, $emailId, $error);
        }

        if ($log) {
            if ($_SESSION['development'] && $forceLive === false || ENVIRONMENT == 'DEV') {
                $getLastEmailID = dbLogEmailDevelopment(
                    $toAddress,
                    null,
                    $subject,
                    $error,
                    null,
                    $identifier,
                    $original_address,
                    $original_cc_address,
                    $postmarkCC,
                    $fromAddress,
                    $postmarkID
                );
            } else {
                $getLastEmailID = dbLogEmail(
                    $toAddress,
                    null,
                    $subject,
                    $error,
                    null,
                    $identifier,
                    $fromAddress,
                    $postmarkID,
                    $postmarkCC
                );
            }

            // for attachment per emailID
            if ($attachments) {
                foreach ($attachments as $attachment) {
                    $file = ($attachment['db_filename'] ?? $attachment['file']);
                    $fileName = ($attachment['file_name'] ?? null);
                    $contentType = ($attachment['content_type'] ?? null);

                    dbInsertEmailAttachment($getLastEmailID, $file, $fileName, $contentType);
                }
            }

            if ($letter_history_id) {
                dbInsertLetterHistoryEmailLog($getLastEmailID, $letter_history_id);
            }

            if ($postmarkID) {
                dbUpdateQueuedEmailSentDetails($emailId);
            }

            //      dbUpdateQueuedEmailRecipientStatus($toAddress, $emailId, null);
            dbUpdateQueuedEmailActivityLog($emailId, $toAddress, $getLastEmailID, 2);
        }
    }

    if (strcmp($error, '') !== 0) {
        return false;
    } else {
        return true;
    }
}

function sumColumns($source, $fields, $decimals = 2)
{
    $output = [];
    if (is_array($source) && is_array($fields)) {
        foreach ($source as $s) {
            foreach ($fields as $f) {
                $output[$f] = bcadd($output[$f], $s[$f], $decimals);
            }
        }
    }

    return $output;
}

// reshuffles the sequence if an item has been deleted to fill the hole
function shuffleItems($parentID, $fieldName, $listFunction, $sequenceFunction)
{
    $itemList = $listFunction($parentID);
    if (count($itemList ?? []) > 1) {
        for ($i = 0, $iMax = count($itemList ?? []); $i < $iMax; $i++) {
            $sequenceFunction($parentID, $itemList[$i][$fieldName], $i + 1);
        }
    }
}

// moves an item up the list - does a fresh DB query rather than relying on posted fields to avoid conflicts
function itemUp($parentID, $fieldName, $listFunction, $sequenceFunction, $sequenceID)
{
    $itemList = $listFunction($parentID);
    if (($sequenceID > 1) && (count($itemList ?? []) > 1)) {
        $index = $sequenceID - 1;
        $sequenceFunction($parentID, $itemList[$index][$fieldName], $sequenceID - 1);
        $sequenceFunction($parentID, $itemList[$index - 1][$fieldName], $sequenceID);
    }
}

// moves an item down the list - does a fresh DB query rather than relying on posted fields to avoid conflicts
function itemDown($parentID, $fieldName, $listFunction, $sequenceFunction, $sequenceID)
{
    $itemList = $listFunction($parentID);
    if (($sequenceID < count($itemList)) && (count($itemList ?? []) > 1)) {
        $index = $sequenceID - 1;
        $sequenceFunction($parentID, $itemList[$index][$fieldName], $sequenceID + 1);
        $sequenceFunction($parentID, $itemList[$index + 1][$fieldName], $sequenceID);
    }
}

function renderProgressBar($id)
{
    echo '<script type="text/javascript">
		   new Ajax.PeriodicalUpdater(\'' . $id . '\', \'progressBar.php\' , {
		onComplete:function(){ $(\'' . $id . '\').style.display = \'none\';
													},
		onSuccess:function(){ },
		asynchronous:true,
		evalScripts:true,
		method: \'post\'
		}
	   );
		</script>
		<div id="' . $id . '"></div>';
}


// --- calculates the tax amount given a gross amount and a tax rate, where the tax rate is an integer (the function converts to a percentage)
function taxAmount($amount, $rate)
{
    return round($amount * ($rate / (100 + $rate)), 2);
}

// --- calculates the tax amount given a net amount and a tax rate, where the tax rate is an integer (the function converts to a percentage)
function netTaxAmount($amount, $rate)
{
    return round(($rate / 100) * $amount, 2);
}


// this is to check if IE for the login.
// @[jpalala]
function ae_detect_ie()
{
    if (isset($_SERVER['HTTP_USER_AGENT']) &&
        (strpos($_SERVER['HTTP_USER_AGENT'], 'MSIE') !== false)) {
        return true;
    } else {
        return false;
    }
}

function rest_helper($url, $params = null, $verb = 'GET', $format = 'json')
{
    $cparams = [
        'http' => [
            'method' => $verb,
            'ignore_errors' => true,
        ],
    ];
    if ($params !== null) {
        $params = http_build_query($params);
        if ($verb == 'POST') {
            $cparams['http']['content'] = $params;
        } else {
            $url .= '?' . $params;
        }
    }

    $context = stream_context_create($cparams);
    $fp = fopen($url, 'rb', false, $context);
    $res = $fp ? stream_get_contents($fp) : false;

    if ($res === false) {
        throw new Exception("{$verb} {$url} failed: {$php_errormsg}");
    }

    switch ($format) {
        case 'json':
            $r = json_decode($res);
            if ($r === null) {
                throw new Exception("failed to decode {$res} as json");
            }

            return $r;

        case 'xml':
            $r = simplexml_load_string($res);
            if ($r === null) {
                throw new Exception("failed to decode {$res} as xml");
            }

            return $r;
    }

    return $res;
}

/**
 * @param  string  $num  Mandatory. The string to convert to float.
 *
 * <AUTHOR>
 *
 * @since 2014-02-26
 *
 * @modified 2015-09-13: Added here. [Morph]
 **/
function toFloat($num)
{
    $dotPos = strrpos($num, '.');
    $commaPos = strrpos($num, ',');
    $sep = (($dotPos > $commaPos) && $dotPos) ? $dotPos :
        ((($commaPos > $dotPos) && $commaPos) ? $commaPos : false);

    if (! $sep) {
        return floatval(preg_replace('/[^0-9]/', '', $num));
    }

    return floatval(
        preg_replace('/[^0-9]/', '', substr($num, 0, $sep)) . '.' .
        preg_replace('/[^0-9]/', '', substr($num, $sep + 1, strlen($num)))
    );
}

function toFloat2($amount)
{
    return (float) str_replace(',', '', $amount);
}

/**
 * <AUTHOR> Reyes
 *
 * @since 2016-04-17
 **/
function validatePassword($currentPassword, $newPassword1, $newPassword2, $ses = null)
{
    $validationErrors = [];
    $ses = ($ses) ? $ses : $_SESSION;
    // Batch 1 validation
    $dbPassword = checkCurrentPassword($ses['user_id']);
    if (empty($currentPassword)) {
        $validationErrors[] = 'Please enter your current password.';
    } // encrypt check below
    elseif ($dbPassword != $currentPassword) {
        $validationErrors[] = 'Your current password does not match your password from the database.';
    }

    if (empty($newPassword1)) {
        $validationErrors[] = 'Please enter your new password.';
    } elseif (mb_strlen($newPassword1) < 8) {
        $validationErrors[] = 'Password must be more than or equal to 8 characters.';
    }

    if (empty($newPassword2)) {
        $validationErrors[] = 'Please enter your new password for verification.';
    }

    if ($newPassword1 && $newPassword2 && $newPassword1 != $newPassword2) {
        $validationErrors[] = 'Your new password password does not match the new password verification.';
    }

    // encrypt check below
    if ($currentPassword == $newPassword1) {
        $validationErrors[] = 'New password must be different from your current password.';
    }

    // Batch 2 validation
    if (noErrors($validationErrors)) {
        if (! preg_match('/[a-z]/', $newPassword1)) {
            $validationErrors[] = 'New password does not have a lower case letter.';
        } elseif (! preg_match('/[A-Z]/', $newPassword1)) {
            $validationErrors[] = 'New password does not have an upper case letter.';
        }

        if (! preg_match('/\d/', $newPassword1)) {
            $validationErrors[] = 'New password does not have a number.';
        }

        if (! preg_match('/[^a-zA-Z\d]/', $newPassword1)) {
            $validationErrors[] = 'New password does not have a symbol.';
        }

        if (max(array_count_values(str_split($newPassword1))) > 1) {
            $validationErrors[] = 'New password must not contain repeating characters.';
        }

        if ($ses['user_name'] == $newPassword1) {
            $validationErrors[] = 'You cannot use your username as a password.';
        } elseif (stripos($ses['user_name'], $newPassword1) !== false) {
            $validationErrors[] = 'Your password must not contain your username.';
        } elseif (soundex($ses['user_name']) === soundex($newPassword1)) {
            $validationErrors[] = 'Your username and password has a similarity.';
        } elseif ($ses['first_name'] == $newPassword1) {
            $validationErrors[] = 'You cannot use your first name as a password.';
        } elseif (stripos($ses['first_name'], $newPassword1) !== false) {
            $validationErrors[] = 'Your password must not contain your first name.';
        } elseif (soundex($ses['first_name']) === soundex($newPassword1)) {
            $validationErrors[] = 'Your first name and password has a similarity.';
        } elseif ($ses['last_name'] == $newPassword1) {
            $validationErrors[] = 'You cannot use your last name as a password.';
        } elseif (stripos($ses['last_name'], $newPassword1) !== false) {
            $validationErrors[] = 'Your password must not contain your last name.';
        } elseif (soundex($ses['last_name']) === soundex($newPassword1)) {
            $validationErrors[] = 'Your last name and password has a similarity.';
        } elseif ($newPassword1 == $ses['first_name'] . ' ' . $ses['last_name']) {
            $validationErrors[] = 'You cannot use your first name and last name as a password.';
        } elseif (stripos($ses['first_name'] . ' ' . $ses['last_name'], $newPassword1) !== false) {
            $validationErrors[] = 'Your password must not contain your first name and last name.';
        } elseif (soundex($ses['first_name'] . ' ' . $ses['last_name']) === soundex($newPassword1)) {
            $validationErrors[] = 'Your full name and password has a similarity.';
        } elseif ($newPassword1 == 'cirrus8') {
            $validationErrors[] = 'You cannot use the system name as a password.';
        } elseif (stripos('cirrus8', $newPassword1) !== false) {
            $validationErrors[] = 'Your password must not contain the name of the system.';
        } elseif (soundex('cirrus8') === soundex($newPassword1)) {
            $validationErrors[] = 'The system name and password has a similarity.';
        } elseif ($newPassword1 == 'Tracc') {
            $validationErrors[] = 'You cannot use the system name as a password.';
        } elseif (stripos('Tracc', $newPassword1) !== false) {
            $validationErrors[] = 'Your password must not contain the name of the system.';
        } elseif (soundex('Tracc') === soundex($newPassword1)) {
            $validationErrors[] = 'The system name and password has a similarity.';
        } elseif ($newPassword1 == 'cirrusbooks') {
            $validationErrors[] = 'You cannot use the system name as a password.';
        } elseif (stripos('cirrusbooks', $newPassword1) !== false) {
            $validationErrors[] = 'Your password must not contain the name of the system.';
        } elseif (soundex('cirrusbooks') === soundex($newPassword1)) {
            $validationErrors[] = 'The system name and password has a similarity.';
        }

        require_once SYSTEMPATH . '/lib/Phpass.php';
        $phpassStrength = new Strength();
        if ($phpassStrength->calculate($newPassword1) < 23) {
            $validationErrors[] = 'Your password is weak.';
        } else {
            $adapter = new Wolfram();
            $phpassStrength = new Strength($adapter);
            if ($phpassStrength->calculate($newPassword1) < 30) {
                $validationErrors[] = 'Your password is weak.';
            }
        }
    }

    return $validationErrors;
}

/*
 LYN
 09-07-2016
*/
function getAddressLatitudeLongitude($address)
{
    if (! empty($address)) {
        $addressSearch = str_replace(' ', '+', $address);
        $json = file_get_contents(
            'https://maps.googleapis.com/maps/api/geocode/json?address=' . $addressSearch . '&key=AIzaSyB0K5npf7MhhfPM94-xJhL362U-5HysVgQ'
        );
        $json = json_decode($json, true);

        if (! empty($json)) {
            $geoLocationStatus = $json['status'];

            switch ($geoLocationStatus) {
                case 'OK':
                    $latitude = $json['results'][0]['geometry']['location']['lat'];
                    $longitude = $json['results'][0]['geometry']['location']['lng'];
                    $addressSearchResult = ['status' => 'success', 'lat' => $latitude, 'lng' => $longitude];
                    break;
                case 'ZERO_RESULTS':
                    $addressSearchResult = [
                        'status' => 'failed',
                        'error_message' => 'The address that you provided is not existing.',
                    ];
                    break;
                case 'OVER_QUERY_LIMIT':
                    $addressSearchResult = [
                        'status' => 'failed',
                        'error_message' => 'Request exceeds quota limit.',
                    ];
                    break;
                case 'REQUEST_DENIED':
                    $addressSearchResult = [
                        'status' => 'failed',
                        'error_message' => $json['error_message'],
                    ];
                    break;
                case 'INVALID_REQUEST':
                    $addressSearchResult = [
                        'status' => 'failed',
                        'error_message' => 'Kindly provide address to search.',
                    ];
                    break;
                case 'UNKNOWN_ERROR':
                    $addressSearchResult = [
                        'status' => 'failed',
                        'error_message' => 'Request could not be processed due to server error.',
                    ];
                    break;
            }

            return $addressSearchResult;
        }
    }

    return false;
}

// ** function convertExcelToDate @ param string return date added by Arjay 24/03/2017**/
function convertExcelToDate($data)
{
    if ($data != null) {
        if (is_numeric($data)) {
            $timestamp = ($data - 25569) * 86400;
            $d = new DateTime('@' . $timestamp);
            $dateFormat = $d->format('d/m/Y');
        } elseif ((bool) strtotime($data)) {
            $d = new DateTime($data);
            $dateFormat = $d->format('d/m/Y');
        } else {
            $dateFormat = $data;
        }
    } else {
        $dateFormat = $data;
    }

    return $dateFormat;
}

// ** function convertStringDate @ param string return date added by Arjay 14/11/2017**/
function convertStringDate($data)
{
    $ds = str_replace('/', '.', $data);
    $d = new DateTime($ds);

    return $d->format('d/m/Y');
}

function pre_print_r($data)
{
    echo '<pre>';
    /** @noinspection ForgottenDebugOutputInspection */
    print_r($data);
    echo '</pre><hr>';
}

function mergePDF($pdffiles, $goDL, $file_name, $outPutFileName, $download_Path, $searchpath = '')
{
    global $pathPrefix, $clientDirectory;

    if (! $searchpath) {
        $searchpath = "{$pathPrefix}{$clientDirectory}/letters/";
    }

    $filename = $file_name;
    $outfilename = $outPutFileName;
    $downloadPath = $download_Path;
    try {
        $p = new PDFlibExt();

        $p->set_option('license=' . PDFLIB_LICENSE);
        $p->set_option('stringformat=utf8');
        $p->set_option('Searchpath={' . $searchpath . '}');

        if ($p->begin_document($outfilename, '') == 0) {
            exit('Error: ' . $p->get_errmsg());
        }

        $p->set_info('Creator', 'cirrus8');
        $p->set_info('Title', $filename);

        foreach ($pdffiles as $pdffile) {
            /* Open the input PDF */
            $indoc = $p->open_pdi_document($pdffile, '');
            if ($indoc == 0) {
                printf("Error: %s\n", $p->get_errmsg());

                continue;
            }

            $endpage = $p->pcos_get_number($indoc, 'length:pages');

            /* Loop over all pages of the input document */
            for ($pageno = 1; $pageno <= $endpage; $pageno++) {
                $page = $p->open_pdi_page($indoc, $pageno, '');

                if ($page == 0) {
                    printf("Error: %s\n", $p->get_errmsg());

                    continue;
                }

                /* Dummy $page size; will be adjusted later */
                $p->begin_page_ext(10, 10, '');

                /* Create a bookmark with the file name */
                if ($pageno == 1) {
                    $p->create_bookmark($pdffile, '');
                }

                /* Place the imported $page on the output $page, and
                 * adjust the $page size
                 */
                $p->fit_pdi_page($page, 0, 0, 'adjustpage');
                $p->close_pdi_page($page);

                $p->end_page_ext('');
            }

            $p->close_pdi_document($indoc);
        }

        $p->end_document('');
    } catch (PDFlibException $e) {
        myErrorHandlerV2(1, $e->get_errmsg(), 'lib/functions.php', '');
    } catch (Exception $e) {
        exit($e);
    }

    if ($goDL) {
        echo "<script> document.location.href='download.php?fileID=" . encodeParameter(
            $downloadPath
        ) . "&fileName=multiple_letters.pdf';</script>";
    } else {
        return [$filename];
    }


}

function management_report(
    &$pdf,
    $_fonts,
    $contents,
    $x,
    $y,
    $width,
    $height,
    $alignment,
    $manualKey = null,
    $fontFamily = null
) {
    $content = $contents['comment'];
    $bold = $contents['bold'];
    $font = $contents['size'];
    $bullet = $contents['bullet'];
    $listed = $contents['listed'];

    if ($content) {
        switch ($fontFamily) {
            case 'Avenir':
                if ($bold) {
                    $pdf->setFontExt($_fonts['AvenirNextLTPro-Bold'], $font);
                } else {
                    $pdf->setFontExt($_fonts['AvenirNextLTPro'], $font);
                }

                break;
            case 'Calibri':
                if ($bold) {
                    $pdf->setFontExt($_fonts['Calibri-Bold'], $font);
                } else {
                    $pdf->setFontExt($_fonts['Calibri'], $font);
                }

                break;
            case 'Panton':
                if ($bold) {
                    $pdf->setFontExt($_fonts['Panton-Bold'], $font);
                } else {
                    $pdf->setFontExt($_fonts['Panton-Light'], $font);
                }

                break;
            default:
                if ($bold) {
                    $pdf->setFontExt($_fonts['Helvetica-Bold'], $font);
                } else {
                    $pdf->setFontExt($_fonts['Helvetica'], $font);
                }

                break;
        }
    }

    if ($bullet || $listed) {
        $items = explode("\n", $content);

        $num_optlist =
            "fontname=Symbol fontsize={$font} encoding=unicode escapesequence " .
            'fillcolor=black leftindent=0 charref=true';

        switch ($fontFamily) {
            case 'Panton':
                $fontname = $bold ? 'Panton-Bold' : 'Panton-Light';

                break;
            case 'Calibri':
                $fontname = $bold ? 'Calibri-Bold' : 'Calibri';

                break;
            default:
                $fontname = $bold ? 'Helvetica-Bold' : 'Helvetica';

                break;
        }

        $item_optlist =
            "fontname={$fontname} fontsize={$font} encoding=unicode " .
            'fillcolor={gray 0} alignment=justify leading=110% leftindent=5 ';

        $list_optlist =
            "fontname={$fontname} fontsize={$font} encoding=unicode escapesequence " .
            'fillcolor=black leftindent=0 charref=true';

        $tf = 0;

        for ($i = 0, $iMax = count($items ?? []); $i < $iMax; $i++) {
            if ($bullet) {
                $tf = $pdf->add_textflow($tf, '&#x2022;', $num_optlist);
            } elseif ($listed) {
                $tf = $pdf->add_textflow($tf, ($manualKey ? $manualKey : $i) + 1 . ') ', $list_optlist);
            }

            $tf = $pdf->add_textflow($tf, $items[$i] . "\n", $item_optlist);
        }

        $pdf->fit_textflow($tf, $x, $y + $height, $width + $x, $y, '');
        $pdf->delete_textflow($tf);
    } else {
        $pdf->showBoxed($content, $x, $y, $width, $height, $alignment, '');
    }
}

function response($status, $status_message, $data)
{
    header('HTTP/1.1 ' . $status);
    $response['status'] = $status;
    $response['status_message'] = $status_message;
    $response['data'] = $data;
    $json_response = json_encode($response);
    echo $json_response;
}

function multiSearch(array $array, array $pairs)
{
    $found = [];
    foreach ($array as $aKey => $aVal) {
        $coincidences = 0;
        foreach ($pairs as $pKey => $pVal) {
            if (array_key_exists($pKey, $aVal) && $aVal[$pKey] == $pVal) {
                $coincidences++;
            }
        }

        if ($coincidences === count($pairs)) {
            $found[$aKey] = $aVal;
        }
    }

    return $found;
}

/**
 * Calculates how many months is past between two timestamps.
 *
 * @param  int  $start  Start timestamp.
 * @param  int  $end  Optional end timestamp.
 * @return int
 */
function get_month_diff($start, $end = false)
{
    $end || $end = time();
    $start = new DateTime("@{$start}");
    $end = new DateTime("@{$end}");
    $diff = $start->diff($end);

    return $diff->format('%y') * 12 + $diff->format('%m');
}

function loadEmailAddressToArr($paramGenList, $getEmailAddressBookList)
{
    $emailAddressBookList = [];
    foreach ($paramGenList as $aRowOwn) {
        $status = '0';
        foreach ($getEmailAddressBookList as $aRow) {
            if ($aRow['email_purpose_param'] === $aRowOwn['parameterID']) {
                $status = '1';
            }
        }

        $emailAddressBookList[] = [
            'parameterID' => $aRowOwn['parameterID'],
            'parameterDescription' => $aRowOwn['parameterDescription'],
            'parameterValue' => $status,
        ];
    }

    return $emailAddressBookList;
}

function loadEmailAddressArrToMain($paramType, $paramName, $paramArr)
{
    return [
        'paramType' => $paramType,
        'paramName' => $paramName,
        'paramArr' => $paramArr,
    ];
}

function setSmsContent($content, $values)
{
    $codes = ['%salutation%', '%arrearsTotal%', '%leaseName%', '%leaseDescription%', '%DEFTLink%'];

    return str_replace($codes, $values, $content);
}

function replaceCommaForCSV($content)
{
    return '"' . $content . '"';
}

function displayPaginationButton($total, $limit, $current_page = 1, $attr = '', $command = 'companySummary')
{
    if ($total) {
        $onclick = "onclick=\"return ajaxContainer(this.id,'content','?command={$command}&module=companies&current_page=1" . $attr . "')\"";
        $button = '<div i class="dataTables_wrapper no-footer">
			<div class="dataTables_paginate paging_simple_numbers"  >
				<a ' . $onclick . '  class="paginate_button previous ' . ($current_page == 1 ? 'disabled' : '') . '" >Previous</a>
				<span>';

        $btn = ceil($total / $limit);

        for ($x = 1; $x <= $btn; $x++) {
            $onclick = "onclick=\"return ajaxContainer(this.id,'content','?command={$command}&module=companies&current_page={$x}" . $attr . "')\"";
            $button .= '<a ' . $onclick . ' class="paginate_button ' . ($current_page == $x ? 'current' : '') . '" >' . $x . '</a>';
        }

        $onclick = "onclick=\"return ajaxContainer(this.id,'content','?command={$command}&module=companies&current_page={$btn}" . $attr . "')\"";

        return $button . ('</span>
				<a  ' . $onclick . ' class="paginate_button next ' . $btn == $current_page ? 'disabled' : '' . '">Next</a>
			</div>
			</div>');
    }


}

function ownerRedirect($command)
{
    $restrict_owner = ownerAccessRestriction($command);
    if ($restrict_owner[0]['cnt'] == 0) {
        header('Location: ?module=home&command=home');
        exit();
    }
}

function slug($string)
{
    return strtolower(trim(preg_replace('/[^A-Za-z0-9-]+/', '-', $string), '-'));
}

/**
 * Compares values of two data array to check if a change has been done
 *
 * @param  array  $origData  (existing data fetched from the database)
 * @param  array  $newData  (updated data fetched from management forms)
 * @return array
 */
function compareCompanyData($origData, $newData, $companyID, $table)
{
    $updates = [];
    $counter = 0;

    // Declare which array keys to record
    $arrayKeys = [
        'companyName',
        'address',
        'city',
        'state',
        'state2',
        'postCode',
        'country',
        'businessNumber',
        'taxCode',
        'active',
        'email',
        'debtor',
        'supplier',
        'owner',
        'supplierAgent',
        'supplierType',
        'cirrusfm',
        'orderNumberRequire',
        'showFromToDate',
        'invoiceAddress',
        'paymentMethod',
        'chequeDays',
    ];

    foreach ($origData as $key => $origValue) {
        $newArrayKey = getArrayKeyCounterpart($key);

        if (array_key_exists($newArrayKey, $newData)) {
            $newValue = $newData[$newArrayKey];

            if (in_array($key, $arrayKeys)) {
                switch ($key) {
                    case 'debtor':
                    case 'supplier':
                    case 'owner':
                    case 'supplierAgent':
                    case 'orderNumberRequire':
                    case 'showFromToDate':
                        if ($newValue != null) {
                            if (trim($newValue) === '') {
                                $newValue = 0;
                            }
                        } else {
                            $newValue = 0;
                        }

                        break;
                }

                if (trim($origValue) !== trim($newValue)) {
                    $updates[$counter]['companyID'] = (strpos($companyID, "'") !== false) ? str_replace(
                        "'",
                        "''",
                        $companyID
                    ) : $companyID;
                    $updates[$counter]['table'] = $table;
                    $updates[$counter]['column'] = $key;
                    $updates[$counter]['oldValue'] = (strpos($origValue, "'") !== false) ? str_replace(
                        "'",
                        "''",
                        $origValue
                    ) : $origValue;
                    $updates[$counter]['newValue'] = (strpos($newValue, "'") !== false) ? str_replace(
                        "'",
                        "''",
                        $newValue
                    ) : $newValue;
                    $updates[$counter]['userID'] = $_SESSION['userID'];

                    $counter++;
                }
            }
        }
    }

    return $updates;
}

function compareCompanyBankingDetails($origData, $newData, $companyID, $table)
{
    $updates = [];
    $counter = 0;

    $arrayKeys = [
        'paymentMethod',
        'bsbNumber',
        'bankAccountNumber',
        'bankAccountName',
        'bankName',
        'bpayBillerCode',
        'directDebit',
        'directBanking',
    ];

    foreach ($origData as $key => $origValue) {
        if (in_array($key, $arrayKeys)) {
            $newValue = $newData[$key];

            if (trim($origValue) !== trim($newValue)) {
                $updates[$counter]['companyID'] = (strpos($companyID, "'") !== false) ? str_replace(
                    "'",
                    "''",
                    $companyID
                ) : $companyID;
                $updates[$counter]['table'] = $table;
                $updates[$counter]['column'] = $key;
                $updates[$counter]['oldValue'] = (strpos($origValue, "'") !== false) ? str_replace(
                    "'",
                    "''",
                    $origValue
                ) : $origValue;
                $updates[$counter]['newValue'] = (strpos($newValue, "'") !== false) ? str_replace(
                    "'",
                    "''",
                    $newValue
                ) : $newValue;
                $updates[$counter]['userID'] = $_SESSION['userID'];

                $counter++;
            }
        }
    }

    return $updates;
}

function getArrayKeyCounterpart($key)
{
    $key_counterpart = [
        'showFromToDate' => 'pmcoDisableInvoiceDates',
        'invoiceAddress' => 'pmcoInvoiceAddress',
    ];
    if (array_key_exists($key, $key_counterpart)) {
        return $key_counterpart[$key];
    } else {
        return $key;
    }
}

/**
 * Create a single entry company changelog
 *
 * @param  string  $oldCode
 * @param  string  $newCode
 * @return array
 */
function createCompanyChangelogEntry($companyID, $oldCode, $newCode, $table, $column)
{
    $recode = [];

    $recode[0]['companyID'] = (strpos($companyID, "'") !== false) ? str_replace("'", "''", $companyID) : $companyID;
    $recode[0]['table'] = $table;
    $recode[0]['column'] = $column;
    $recode[0]['oldValue'] = (strpos($oldCode, "'") !== false) ? str_replace("'", "''", $oldCode) : $oldCode;
    $recode[0]['newValue'] = (strpos($newCode, "'") !== false) ? str_replace("'", "''", $newCode) : $newCode;
    $recode[0]['userID'] = $_SESSION['userID'];

    return $recode;
}

/**
 * Create a single entry company changelog for company code update
 *
 * @param  string  $oldCode
 * @param  string  $newCode
 * @return array
 */
function createRecodeChangelogEntry($oldCode, $newCode, $table, $column)
{
    $recode = [];

    $recode[0]['companyID'] = (strpos($newCode, "'") !== false) ? str_replace("'", "''", $newCode) : $newCode;
    $recode[0]['table'] = $table;
    $recode[0]['column'] = $column;
    $recode[0]['oldValue'] = (strpos($oldCode, "'") !== false) ? str_replace("'", "''", $oldCode) : $oldCode;
    $recode[0]['newValue'] = (strpos($newCode, "'") !== false) ? str_replace("'", "''", $newCode) : $newCode;
    $recode[0]['userID'] = $_SESSION['userID'];

    return $recode;
}

/**
 * Convert lease data array keys
 *
 * @param  array  $leaseData
 * @return array
 */
function convertLeaseDataKeys($leastData = [])
{
    $newArray = [];
    $newArrayKeys = [
        'tenantName' => 'companyName',
        'tenantAddress' => 'address',
        'tenantCity' => 'city',
        'tenantState' => 'state',
        'tenantCountry' => 'country',
        'tenantEmail' => 'email',
        'tenantPostCode' => 'postCode',
    ];

    foreach ($leastData as $key => $data) {
        if (array_key_exists($key, $newArrayKeys)) {
            $newKey = $newArrayKeys[$key];
            $newArray[$newKey] = $data;
        }
    }

    return $newArray;
}

/**
 * Get all unique username from the company update log records
 *
 * @param  array  $updateLog
 * @return array
 */
function getUniqueCompanyUpdateLogUsers($updateLog)
{
    $userArray = [];

    foreach ($updateLog as $value) {
        $userArray[] = $value['modified_by'];
    }

    return array_unique($userArray);
}

/**
 * Prepare fetched user log array data
 *
 * @param  array  $users
 * @return array
 */
function prepareLogUserArrData($users)
{
    $userData = [];

    foreach ($users as $value) {
        $userData[$value['user_id']] = $value['first_name'] . ' ' . $value['last_name'];
    }

    return $userData;
}

/**
 * Assign field name for specified column
 *
 * @param  string  $column
 */
function assignLogFieldName($column)
{
    $fieldNames = [
        'companyName' => 'Company Name',
        'address' => 'Address',
        'company_street' => 'Address',
        'city' => ucwords(strtolower($_SESSION['country_default']['suburb'])),
        'state' => 'State',
        'state2' => 'State',
        'postCode' => 'Post Code',
        'country' => 'Country',
        'businessNumber' => 'ABN',
        'taxCode' => 'Tax Status',
        'active' => 'Is Company Active?',
        'email' => 'Email Address',
        'debtor' => 'Company Type: Debtor',
        'supplier' => 'Company Type: Supplier',
        'owner' => 'Company Type: Owner',
        'supplierType' => 'Supplier Type',
        'supplierAgent' => 'Supplier Agent',
        'cirrusfm' => 'Enable on CirrusFM',
        'pmco_code' => 'Company Code',
        'orderNumberRequire' => 'Require order number for all invoices',
        'showFromToDate' => "Don't Show From/To Dates on Invoice",
        'invoiceAddress' => 'Invoice Address',
        'paymentMethod' => 'Payment Method',
        'chequeDays' => 'Cheque Days',
        'bsbNumber' => 'BSB',
        'bankAccountNumber' => 'Bank Account Number',
        'bankAccountName' => 'Account Name',
        'bankName' => 'Bank Name',
        'bpayBillerCode' => 'BPAY Biller Code',
        'directDebit' => 'Direct Debit',
        'directBanking' => 'Direct Banking',
    ];

    return $fieldNames[$column];
}

/**
 * Get tax code value
 *
 * @param  string  $taxCode
 */
function getTaxCodeValue($taxCode)
{
    if ($taxCode == 'GSTFREE') {
        return $_SESSION['country_default']['tax_label'] . ' FREE';
    } elseif ($taxCode == 'TAXABLE') {
        return $_SESSION['country_default']['tax_label'] . ' TAXED';
    } else {
        return '-';
    }
}

/**
 * Get company type value
 *
 * @param  int  $value
 */
function getCompanyTypeValue($value)
{
    if ($value != null) {
        if ($value == 0 || $value == '') {
            return 'No';
        } else {
            return 'Yes';
        }
    } else {
        return 'No';
    }
}

function getPaymentMethodName($method)
{
    $payment_methods = [
        1 => 'Direct Deposit (EFT)',
        2 => 'BPAY',
        3 => 'Cheque',
    ];

    return $payment_methods[$method];
}

/**
 * Add the fullname to the update log array data based on the user_name value
 *
 * @param  array  $users
 * @param  array  $updateLog
 */
function addFullnameToArrData($users, $updateLog)
{
    $updateLogData = [];
    $userData = prepareLogUserArrData($users);
    $indexCounter = 1;

    foreach ($updateLog as $value) {
        $oldValue = '';
        $newValue = '';
        $userID = $value['modified_by'];

        switch ($value['column_name']) {
            case 'active':
                $oldValue = ($value['old_value'] == 1) ? 'Yes' : 'No';
                $newValue = ($value['new_value'] == 1) ? 'Yes' : 'No';
                break;
            case 'taxCode':
                $oldValue = getTaxCodeValue($value['old_value']);
                $newValue = getTaxCodeValue($value['new_value']);
                break;
            case 'debtor':
            case 'supplier':
            case 'owner':
            case 'supplierAgent':
            case 'cirrusfm':
            case 'orderNumberRequire':
            case 'showFromToDate':
            case 'directDebit':
            case 'directBanking':
                $oldValue = getCompanyTypeValue($value['old_value']);
                $newValue = getCompanyTypeValue($value['new_value']);
                break;
            case 'paymentMethod':
                $oldValue = getPaymentMethodName($value['old_value']);
                $newValue = getPaymentMethodName($value['new_value']);
                break;
            default:
                $oldValue = ($value['old_value'] == '') ? '-' : $value['old_value'];
                $newValue = ($value['new_value'] == '') ? '-' : $value['new_value'];
                break;
        }

        if (trim($oldValue) !== trim($newValue)) {
            $updateLogData[] = [
                'index' => $indexCounter,
                'field_name' => assignLogFieldName($value['column_name']),
                'old_value' => $oldValue,
                'new_value' => $newValue,
                'modified_by' => $userData[$userID],
                'modified_at' => $value['modified_date'],
            ];
            $indexCounter++;
        }
    }

    return $updateLogData;
}

function createZip($filepaths, $saveFolderLocation, $saveFileName)
{
    global $pathPrefix, $clientDirectory;
    $zip = new ZipArchive();
    $zip_folder = "{$pathPrefix}{$clientDirectory}/{$saveFolderLocation}";
    $downloadPath = "{$clientDirectory}/{$saveFolderLocation}/" . $saveFileName . '_' . date('YmdHis') . '.zip';
    $filePath = "{$zip_folder}/" . $saveFileName . '_' . date('YmdHis') . '.zip';
    if (file_exists($filePath)) {
        unlink($filePath);
    }

    if (! file_exists($zip_folder)) {
        mkdir($zip_folder, FILE_PERMISSION, true);
    }

    if ($zip->open($filePath, ZipArchive::CREATE) !== true) {
        exit("cannot open <{$filePath}>\n");
    }

    foreach ($filepaths as $filepath) {
        $zip->addFile($filepath, basename($filepath));
    }

    return $downloadPath;
}

function getClientCountry()
{
    return dbGetDefaultCountry();
}

function showSQLWithParams($sql, $params, $return = false)
{
    if (! DEBUG && $_SESSION['super'] != 1 && ! $return) {
        return false;
    }

    foreach ($params as $param) {
        if (is_string($param)) {
            $param = "'{$param}'";
        }

        $sql = implode($param, explode('?', $sql, 2));
    }

    if (! $return) {
        pre_print_r($sql);
    } else {
        return $sql;
    }


}

function sanitiseEmailAddress($emailAddress): array
{
    $returnValue = [];
    $isOneEmail = false;
    if (is_string($emailAddress)) {
        $emailAddress = strtolower(trim($emailAddress));
        if (strpos($emailAddress, ';') !== false) {
            $emailAddressArray = explode(';', $emailAddress);
            foreach ($emailAddressArray as $email) {
                if (filter_var(trim($email), FILTER_VALIDATE_EMAIL)) {
                    $returnValue[] = trim($email);
                }
            }

            if (count($returnValue ?? []) > 0) {
                $isOneEmail = true;
            }
        } elseif (filter_var(trim($emailAddress), FILTER_VALIDATE_EMAIL)) {
            $returnValue[] = trim($emailAddress);
        }
    }

    if (is_array($emailAddress)) {
        foreach ($emailAddress as $email) {
            if (filter_var(trim($email), FILTER_VALIDATE_EMAIL)) {
                $returnValue[] = trim($email);
            }
        }
    }

    return ['is_one_email' => $isOneEmail, 'email_address' => array_unique($returnValue)];
}

function downloadFile()
{
    global $pathPrefix;

    $filename = $_SESSION['downloadFile'];
    $filePath = $pathPrefix . $filename;

    if ($filename && file_exists($filePath)) {
        ob_start();
        $extension = substr($filePath, strrpos($filePath, '.') + 1);

        // required for IE, otherwise Content-disposition is ignored
        ini_set('zlib.output_compression', 'Off');

        switch ($extension) {
            case 'pdf':
                $ctype = 'application/pdf';
                break;
            case 'exe':
                $ctype = 'application/octet-stream';
                break;
            case 'zip':
                $ctype = 'application/zip';
                break;
            case 'doc':
                $ctype = 'application/msword';
                break;
            case 'xls':
                $ctype = 'application/vnd.ms-excel';
                break;
            case 'xlsx':
                $ctype = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
                break;
            case 'ppt':
                $ctype = 'application/vnd.ms-powerpoint';
                break;
            case 'pptx':
                $ctype = 'application/vnd.openxmlformats-officedocument.presentationml.presentation';
                break;
            case 'gif':
                $ctype = 'image/gif';
                break;
            case 'png':
                $ctype = 'image/png';
                break;
            case 'jpeg':
                $ctype = 'image/jpeg';
                break;
            case 'jpg':
                $ctype = 'image/jpg';
                break;
            case 'aba' :
                $ctype = 'text/plain';
                break;
            case 'txn' :
                $ctype = 'application/mbldeft-transaction';
                break;
            case 'brf' :
                $ctype = 'application/mbldeft-bpay';
                break;
            case 'pay' :
                $ctype = 'application/mbldeft-payment';
                break;
            case 'acc' :
                $ctype = 'application/mbldeft-acc';
                break;
            default:
                $ctype = 'application/force-download';
        }

        header('Pragma: public'); // required
        header('Expires: 0');
        header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
        header('Cache-Control: private', false); // required for certain browsers
        header("Content-Type: {$ctype}");
        header('Content-Transfer-Encoding: none');
        // change, added quotes to allow spaces in filenames
        header(
            'Content-Disposition: attachment; filename="' . basename(
                $_REQUEST['fileName'] ? $_REQUEST['fileName'] : $filePath
            ) . '";'
        );

        header('Content-Length: ' . filesize($filePath));
        while (ob_get_level()) {
            ob_end_clean();
        }

        unset($_SESSION['downloadFile']);
        readfile($filePath);

        unlink($filePath);

        exit();
    }

    return false;
}

function wrapText(string $text, int $maxWidth): string
{
    return wordwrap($text, $maxWidth, "\n", true);
}

/**
 * Check if the current country is Great Britain.
 */
function isGreatBritain(): bool
{
    return $_SESSION['country_code'] == CountryCode::GB;
}

/**
 * Calculates the percentage of a given value.
 *
 * @param  float  $part  The portion or value to calculate the percentage for.
 * @param  float  $total  The total or whole value.
 * @return float The calculated percentage
 */
function get_percentage(float $num_amount, float $num_total): float
{
    $count1 = $num_amount / $num_total;
    $count2 = $count1 * 100;

    return number_format($count2, 2);
}

/**
 * Displays a block of long text within a defined area on a PDF document.
 *
 * @param  object  $pdf  A reference to the PDF object to render the text onto.
 * @param  string  $text  The text content to display.
 * @param  int  $xAxis  The X-axis coordinate where the text block starts.
 * @param  int  $yAxis  The Y-axis coordinate where the text block starts.
 * @param  int  $width  The width of the text block.
 * @param  int  $height  The height of the text block.
 * @param  string  $alignment  The alignment of the text
 * @param  int  $numberOfCharacters  The maximum number of characters per line.
 * @return void
 */
function display_long_text(
    object &$pdf,
    string $text,
    int $xAxis,
    int $yAxis,
    int $width,
    int $height,
    string $alignment,
    int $numberOfCharacters
) {
    $yAxis = strlen($text) > $numberOfCharacters ? $yAxis + 4 : $yAxis - 4;

    $pdf->showBoxed($text, $xAxis, $yAxis, $width, $height, $alignment, '');
}

function requestApiPost(string $url, array $parameters): void
{
    try {
        $content = [
            'app_key' => $_SESSION['sso_key'],
            'user_type' => $_SESSION['user_type'],
            'currentDB' => $_SESSION['currentDB'],
        ];

        $content = array_merge($content, $parameters);
        $headers = [
            'Authorization' => 'Bearer ' . $_SESSION['sysApi'],
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
        ];

        $fiveMinutes = 300;
        $content = json_encode($content);
        Requests::post(
            c8_api . $url,
            $headers,
            $content,
            ['timeout' => $fiveMinutes]
        );
    } catch (Exception $exception) {
        if (ENVIRONMENT == 'LIVE') {
            myErrorHandlerV2(1, $exception->getMessage(), $exception->getFile(), $exception->getLine());
        } else {
            $msg = 'code : ' . $exception->getCode() . PHP_EOL;
            $msg .= 'file : ' . $exception->getFile() . 'line : ' . $exception->getLine() . PHP_EOL;
            $msg .= 'mess : ' . $exception->getMessage() . PHP_EOL;
            pre_print_r($msg);
        }

    }
}

/**
 * Extracts image source URLs from HTML content using XML parsing
 *
 * This function takes HTML content, parses it using DOM and SimpleXML,
 * then returns an array of all image source URLs found in img tags.
 *
 * @param  string  $html  The HTML content to parse
 * @return array An array of image source URLs extracted from img tags
 */
function extractImageFromHTMLUsingXML(string $html = ''): array
{
    // Configure libxml to handle errors properly
    $previousLibXmlUseInternalErrors = libxml_use_internal_errors(true);

    // Create and configure the DOM document
    $dom = new DOMDocument();
    $dom->loadHTML($html, LIBXML_HTML_NOIMPLIED | LIBXML_HTML_NODEFDTD);

    // Convert to SimpleXML and execute XPath
    $simpleXml = simplexml_import_dom($dom);
    $src = $simpleXml instanceof SimpleXMLElement ? $simpleXml->xpath('//img/@src') : [];

    libxml_use_internal_errors($previousLibXmlUseInternalErrors);

    return $src;
}

function AddAttachmentLink($message, $urls, $expiry)
{
    $validUntil = '';
    if ($expiry != '2999-12-31') {
        $validUntil = 'Link will expire on ' . $expiry . '.';
    }

    $attachmentContent = '<table style="width: 100%; border: 1px solid #ececec; margin-bottom: 10px;" align="left">';
    foreach ($urls as $index => $url) {
        if ($index >= 1) {
            $identifier = intval($index) + 1;
            $addlStyle = 'padding: 0 10px 7px 10px;';
            $addlStyleMso = 'mso-padding-alt: 0 10px 7px 10px;';
        } else {
            $identifier = '';
            $addlStyle = 'padding: 7px 10px 7px 10px;';
            $addlStyleMso = 'mso-padding-alt: 7px 10px 7px 10px;';
        }

        $attachmentContent .= '<tr style="' . $addlStyleMso . '">';
        $attachmentContent .= '<td>';
        $attachmentContent .= '<table align="left" style="width: 100%; ' . $addlStyle . '">';
        $attachmentContent .= '<tr>';
        $attachmentContent .= '<td style="width: 66px; text-align: center;" align="center">';
        $attachmentContent .= '<a target="_blank" href="' . $url . '" style="display: inline-block;font-decoration:none; color: #00BAF2; background-color: #ffffff; border: solid 1px #00BAF2; border-radius: 5px; box-sizing: border-box; cursor: pointer; text-decoration: none; font-size: 14px; font-weight: bold; margin: 0; padding: 10px 20px; text-transform: capitalize; border-color: #00BAF2;">Download</a>';
        $attachmentContent .= '</td>';
        $attachmentContent .= '<td style="padding-left: 10px;">';
        $attachmentContent .= '<p style="font-family: tahoma, sans-serif !important; font-size: 13px; margin: 0;"> Attachment ' . $identifier . ' </p>';
        $attachmentContent .= '<p style="font-family: tahoma, sans-serif !important; font-size: 11px; margin: 0; color: #999;">' . $validUntil . '</p>';
        $attachmentContent .= '</td>';
        $attachmentContent .= '</tr>';
        $attachmentContent .= '</table>';
        $attachmentContent .= '</td>';
        $attachmentContent .= '</tr>';
    }

    $attachmentContent .= '</table>';

    $found = false;
    $html = str_get_html($message);

    // ASSUMING THAT THE EMAIL TEMPLATE HAS td.content
    $elements = $html->find('td[class=content]');
    foreach ($elements as $index => $e) {
        if ($index == 0) {
            $found = true;
            // $e->innertext = $e->innertext . $attachmentContent;
            $e->innertext = $attachmentContent . $e->innertext;
        }
    }

    if (! $found) {
        $elements = $html->find('table tbody');
        foreach ($elements as $index => $e) {
            if ($index == 0) {
                $found = true;
                $e->innertext = '<tr><td>' . $attachmentContent . '</td></tr>' . $e->innertext;
            }
        }

        if (! $found) {
            $html = $attachmentContent . $html;
        }

        // $target = $html->find('table tbody', 0);

        // if ($target) {
        //   $e = $html->find('table tbody', 0);

        //   // ATTACHMENT SECTION IS ALWAYS DISPLAYED FIRST
        //   $e->innertext = '<tr><td>' . $attachmentContent . '</td></tr>' . $e->innertext;
        //   // $e = $html->find('table tbody', 0)->find('tr', 0)->find('td', 0);
        //   // $e->innertext = $attachmentContent . $e->innertext;
        // } else {
        //   $html = $attachmentContent . $html;
        // }
    }

    // CHECK IF RETURN IS STRING
    if (gettype($html) === 'string') {
        return $html;
    } else {
        return $html->outertext;
    }
}

/**
 * Checks if the given file format matches the XLS file type.
 *
 * @param  string  $format  The file format to be checked.
 * @return bool If the given file format is XLS or not.
 */
function fileFormatIsXLS(string $format): bool
{
    return $format === FILETYPE_XLS;
}

/**
 * Check and retrieve the `display_bsb` value from the session's country default data.
 *
 * @return bool Value of `display_bsb`; defaults to false if none is set.
 **/
function getDisplayBsbFromSession(): bool
{
    return $_SESSION['country_default']['display_bsb'] ?? false;
}

/**
 * Retrieves the BSB label from the session.
 *
 * @return string The BSB label stored in the session or a default value of 'BSB' if not found.
 */
function getBsbLabelFromSession(): string
{
    return $_SESSION['country_default']['bsb_label'] ?? DefaultBSBValue::LABEL;
}

/**
 * Retrieves the BSB length from the session.
 *
 * @return int The BSB length stored in the session or a default value of 6 if not found.
 */
function getBsbLengthFromSession(): int
{
    return $_SESSION['country_default']['bsb_length'] ?? DefaultBSBValue::LENGTH;
}

/**
 * Retrieves the BSB delimiter from the session.
 *
 * @return string The BSB delimiter stored in the session or a default value of '-' if not found.
 **/
function getBsbDelimiterFromSession(): string
{
    return $_SESSION['country_default']['bsb_format']['delimiter'] ?? DefaultBSBValue::DELIMITER;
}

/**
 * Fetches the BSB delimiter frequency from the current session. If not set, returns a default value.
 *
 * @return int The delimiter frequency value from the session or the default value of 4 if not set.
 **/
function getBsbDelimiterFrequencyFromSession(): int
{
    return $_SESSION['country_default']['bsb_format']['delimiter_frequency'] ?? DefaultBSBValue::DELIMITER_FREQUENCY;
}

/**
 * Formats a given text string by inserting a specified delimiter at a specified frequency.
 *
 * @param  string  $text  Mandatory. The text to be formatted with a specific delimiter.
 * @param  string|null  $delimiter  Optional. The character or string to be used as a delimiter. If not provided, a session-based default delimiter will be used.
 * @param  int|null  $frequency  Optional. The frequency at which the delimiter is added. If not provided, a session-based default frequency will be used.
 * @return string The formatted text with the specified delimiter added at the defined frequency or session-based defaults if applicable.
 **/
function formatWithDelimiter($text, $delimiter = null, $frequency = null): string
{
    if (empty($text)) {
        return '';
    }

    $delimiter ??= getBsbDelimiterFromSession();
    $frequency ??= getBsbDelimiterFrequencyFromSession();

    $length = strlen($text);
    if ($length <= $frequency) {
        return $text;
    }

    $textArray = str_split($text);
    $mappedArray = array_map(
        fn($char, $index) => ((($length - $index) % $frequency) === 0 && $index > 0) ? $delimiter . $char : $char,
        $textArray,
        array_keys($textArray)
    );

    return implode('', $mappedArray);
}

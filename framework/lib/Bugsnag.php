<?php

class Bugsnag
{
    private static string $UNKNOWN = 'UNKNOWN';

    /**
     * Render the bugsnag tracking and performance scripts
     */
    public static function render(): string
    {

        if (! defined('BUGSNAG_API_KEY')) {
            return '';
        }

        $version = Bugsnag::getAppVersion();
        $apiKey = BUGSNAG_API_KEY;
        $env = defined('ENVIRONMENT') ? ENVIRONMENT : Bugsnag::$UNKNOWN;

        return <<<DATA
                <script src='https://d2wy8f7a9ursnm.cloudfront.net/v8.4/bugsnag.min.js'></script>
                <script>
                    window.Bugsnag.start({
                        apiKey: '$apiKey',
                        appVersion: '$version',
                        releaseStage: '$env',
                    })
                </script>
                <script type="module">
                    import BugsnagPerformance from 'https://d2wy8f7a9ursnm.cloudfront.net/v2.14/bugsnag-performance.min.js'
                    BugsnagPerformance.start({
                          apiKey: '$apiKey',
                          appVersion: '$version',
                          releaseStage: '$env',
                    });
                </script>
            DATA;
    }

    /**
     * Read the VERSION file and get the version number
     */
    private static function getAppVersion(): string
    {
        $contents = file_get_contents(BASEPATH . '/VERSION', true);

        if ($contents === false) {
            return Bugsnag::$UNKNOWN;
        }

        return $contents;
    }
}

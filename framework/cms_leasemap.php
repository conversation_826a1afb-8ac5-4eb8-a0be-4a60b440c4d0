<?php

//error_reporting(E_ALL ^ E_NOTICE);

  session_start();

  if (!function_exists("ob_get_clean")) {
    function ob_get_clean() {
        $ob_contents = ob_get_contents();
        ob_end_clean();
        return $ob_contents;
    }
}


include('config.php');


define('CLIENTDB',$_SESSION['currentDB']);
define('CLIENTDSN',COREDSN . $_SESSION['currentDB']);
if (!isset($_SESSION['user_name'])) { redirectUser(NPMSURL); }

$dbh = new MSSQL(CLIENTDSN);
$dbh->selectDatabase(CLIENTDB);


        echo '<link rel="stylesheet" href="'.ASSET_DOMAIN.'assets/css/data.css" type="text/css" />
              <b>mapping lease info</b><br /><br />
              <ul>';

        //-- fetch all units
        $sql = "SELECT * FROM pmpu_p_unit ORDER BY pmpu_prop, pmpu_unit";
        $units = $dbh->executeSet($sql);
        //-- fetch all leases for that unit
        $counter=0;
        $count = 0;
        
        //add columns pmua_vacant, pmua_status
        $dbh->executeNonQuery("ALTER TABLE pmua_unit_area ADD pmua_vacant bit");
        $dbh->executeNonQuery("ALTER TABLE pmua_unit_area ADD pmua_status char(1)");
        $dbh->executeNonQuery("ALTER TABLE pmua_unit_area ADD pmua_serial char(10)");
        
                foreach($units as $unit) {
                    $sql = "SELECT * FROM pmlu_l_unit WHERE pmlu_prop = '{$unit[pmpu_prop]}' AND pmlu_unit='{$unit[pmpu_unit]}' ORDER BY pmlu_rent_to_dt";    
                    $leases = $dbh->executeSet($sql);
                    if ($leases) {
                        foreach ($leases as $lease) {
                            $sql = "SELECT * FROM pmle_lease WHERE pmle_prop='{$lease[pmlu_prop]}' AND pmle_lease='{$lease[pmlu_lease]}'";
                            $leasedetails = $dbh->executeSingle($sql);
                            if ($leasedetails) {
                                //-- cool we have lease
                                //-- if the lease is current and we only have one lease - well set the end date to be the end of time
                        
                                if ($leasedetails['pmle_status'] == 'C') {
                                    $endDate = '31 Dec 2999 12:00AM';
                                    $vacant = 0;
                                    $status = 'O';
                                } else {
                                    $vacant =1;
                                    $status = 'V';
                                    $endDate = $lease['pmlu_rent_to_dt'];
                                }
                                $startDate =  $leasedetails['pmle_com_dt'];
                                //-- if a record exists in pmua - update it, else insert it
                                $unitarea = $dbh->executeSingle("SELECT * FROM pmua_unit_area  WHERE pmua_prop='{$lease[pmlu_prop]}' AND pmua_unit='{$lease[pmlu_unit]}' AND pmua_from_dt='{$startDate}'");
                                if ($unitarea) {
                                $dbh->executeNonQuery("UPDATE pmua_unit_area SET pmua_vacant='{$vacant}', pmua_status='{$status}' WHERE pmua_prop='{$lease[pmlu_prop]}' AND pmua_unit='{$lease[pmlu_unit]}'");
                                } else {
                                    $sql = "
                                    INSERT INTO pmua_unit_area (pmua_prop, pmua_unit, pmua_from_dt, pmua_to_dt, pmua_area, pmua_vacant,pmua_status) VALUES
                                    (
                                        '{$lease[pmlu_prop]}',
                                        '{$lease[pmlu_unit]}',
                                        '{$startDate}',
                                        '{$endDate}',
                                        '{$unit[pmpu_area]}',
                                        '{$vacant}',
                                        '{$status}'
                                   )
                                    ";
                                    echo $sql;
                                    $dbh->executeNonQuery($sql);
                                }
                                
                                echo $leasedetails['pmle_prop']  . ' ' .  $lease['pmlu_unit'] .  ' ' . $leasedetails['pmle_lease'] . ' ' .  $startDate . ' to ' . $endDate . '<br/>';
                                $counter++;
                                
                            } 
                            
                            
                            //--- hmm some leases dont have rent to dates - grab the
                            if (!$lease['pmlu_rent_to_dt']) echo '<h1>' . $lease['pmlu_prop'] . ' / ' . $lease['pmlu_lease'] . ' / ' . $lease['pmlu_unit'] . ' has no rent to date</h1>';
                            
                            $sql = "UPDATE pmlu_l_unit SET pmlu_occ_from_dt = '{$startDate}', pmlu_occ_to_dt='{$endDate}' WHERE pmlu_prop = '{$lease[pmlu_prop]}' AND  pmlu_lease = '{$lease[pmlu_lease]}' AND pmlu_unit = '{$lease[pmlu_unit]}' ";
                            $dbh->executeNonQuery($sql);
                        }
                    } else {
                       
                        echo $unit['pmpu_prop'] . ' ' . $unit['pmpu_unit'] . '<br/>';
                    }
                    //echo print_array($leases,true);
                    $count += count($leases);
                    //echo '<br/><br/>';
                }
                echo $counter;
                echo $count . ' => ' . count($units);
                echo '<br/><br/>';
                
            // -- if there are leases for the unit, order them based on pmle_com_dt  to the pmlu_rent_to_dt
                    $sql = "";
            //-- if there are no leases, add a vacant PMUA entry from 1/1/1900 to 31/12/2999
                    $sql = "";



?>
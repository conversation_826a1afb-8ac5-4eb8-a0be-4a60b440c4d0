<?php

include_once(SYSTEMPATH . '/lib/enums/InvoiceChargeType.php');
include_once(SYSTEMPATH . '/lib/enums/InvoiceStatementType.php');

use enums\InvoiceChargeType;
use enums\InvoiceStatementType;

session_start();

if (!function_exists("ob_get_clean")) {
    function ob_get_clean()
    {
        $ob_contents = ob_get_contents();
        ob_end_clean();
        return $ob_contents;
    }
}

include(__DIR__ . '/config.php');


define('CLIENTDB', $_SESSION['currentDB']);
define('CLIENTDSN', COREDSN . $_SESSION['currentDB']);
$dbh = new MSSQL(SYSTEMDSN);


include_once __DIR__ . '/lib/PDFReport.php';
include __DIR__ . '/data/administrator/ar/dbInterface.php';
include __DIR__ . '/commands/ar/invoicePDF.php';


$clientDirectory = str_replace(' ', '', $_SESSION['database']);
$pathPrefix = REPORTPATH . "/";
$globalDownloadLink = "reports/" . $clientDirectory;


/****************************************************************************************************************/

function bookInvoiceNumber()
{
    $number = dbGetParam('DOCSERIES', 'GSTINVOICE');
    $number++;
    dbSetParam('DOCSERIES', 'GSTINVOICE', $number);
    return $number;
}

function calculateOffset(
    $invoiceReceipted,
    $invoiceCredited,
    $invoiceAdjusted,
    $creditReceipted,
    $creditAdjusted,
    $creditAllocated
) {
    //-- cast as numbers
    $invoiceReceipted *= 1;
    $invoiceCredited *= 1;
    $invoiceAdjusted *= 1;
    $creditReceipted *= 1;
    $creditAdjusted *= 1;
    $creditAllocated *= 1;

    //-- sum and return
    $total = 0;
    $total = $invoiceReceipted + $invoiceCredited + $invoiceAdjusted + $creditReceipted + $creditAdjusted + $creditAllocated;
    $total = round($total, 2);

    return $total;
}

//-- function fetchOutstandingAmounts

//   given a debtor, property, lease and invoice date -
//  returns the number of outstanding amounts, and the records attached to those amounts in an array split by new (newAmounts) and outstanding (outstandingAmounts)

function fetchOutstandingAmounts($debtorID, $propertyID, $leaseID, $invoiceDate, &$newAmounts, &$outstandingAmounts)
{
    $newAmounts = dbGetInvoiceCharges(
        InvoiceChargeType::INV_CHARGES_NEW,
        $propertyID,
        $leaseID,
        $debtorID,
        $invoiceDate
    );
    $records = count($newAmounts);

    $outstandingAmounts = array();
    $existing = dbGetInvoiceCharges(InvoiceChargeType::INV_CHARGES_OLD, $propertyID, $leaseID, $debtorID, $invoiceDate);
    foreach ($existing as $row) {
        $offset = calculateOffset(
            $row['invoiceTotalReceipted'],
            $row['invoiceTotalCredited'],
            $row['invoiceTotalAdjusted'],
            $row['creditTotalReceipted'],
            $row['creditTotalAdjusted'],
            $row['creditTotalAllocated']
        );

        if ((($row['amount'] - $offset) != 0) && ($row['transactionType'] != 'CSH')) {
            $records++;
            $outstandingAmounts[] = $row;
        } else {
            if (($row['transactionType'] == 'CSH') && ($row['unallocated'] != '') && ($row['unallocated'] != '0')) {
            }
        }
    }

    return $records;
}


function prepareInvoice(
    $style = InvoiceStatementType::ST_INVOICE,
    $leaseID,
    $propertyID,
    $invoiceDate,
    $issueDate = null
) {
    global $clientDirectory, $pathPrefix;

    //-- initialise the variables that will store the transaction amounts
    $total = 0;
    $newAmounts = array();
    $outstandingAmounts = array();

    //-- this may need revising (suppliers?) - but it grabs the
    $debtorID = dbGetDebtor($propertyID, $leaseID);

    //-- if there are transactions to be printed :
    if (fetchOutstandingAmounts(
            $debtorID,
            $propertyID,
            $leaseID,
            $invoiceDate,
            $newAmounts,
            $outstandingAmounts
        ) != 0) {
        switch ($style) {
            case InvoiceStatementType::ST_INVOICE :
                $headerTitle = 'TAX INVOICE';
                $totalTitle = 'INVOICE TOTAL';
                break;
            case InvoiceStatementType::ST_STATEMENT :
                $headerTitle = 'STATEMENT OF ACCOUNT';
                $totalTitle = 'STATEMENT TOTAL';
                break;
        }

        if (!$issueDate) {
            $issueDate = TODAY;
        }
        list($day, $month, $year) = explode('/', $invoiceDate);
        $dueDate = $invoiceDate;

        //$invoiceNumber = dbGetLastInvoiceNumber();
        $invoiceNumber = 'INV';


        $filename = "tax_invoice_{$year}{$month}{$day}_{$invoiceNumber}_{$propertyID}_{$leaseID}.pdf";
        $filePath = "{$pathPrefix}{$clientDirectory}/pdf/TaxInvoice/{$filename}";

        echo $filePath;

        //-- will need to store current DB index in session to allow for translation of logos
        $logoFile = dbGetClientLogo();
        $logoPath = "assets/clientLogos/{$logoFile}";
        //$logoPath = null;

        $invoice = new Invoice($filePath, $logoPath);

        $note = 'TEST INVOICE';
        $agentData = dbGetAgentDetails();
        $headerData = dbGetHeaderDetails($propertyID, $leaseID);
        $leaseAddress = dbGetLeaseMailingAddress($propertyID, $leaseID);
        $bankData = dbGetBankDetails($propertyID);

        //-- some data is shared across several components in the PDF, so instead of doing multiple db calls and binding locally within the object,
        //-- a single DB call is made and data bound to the object externally

        //-- PDFobjects are usually groups of data repeated statically on every page of the PDF, and as such are loaded once into the PDF and called at render...
        //-- mutating values are accessed from the Invoice object itself such as changing totals, invoice rows, sub titles within the statement

        //-- note that data is bound between array values to the object attributes sharing the same name as the array key - ie
        //-- $this->test = $array['test']

        $header = new InvoiceHeader($propertyID, $leaseID, $dueDate, $issueDate, $headerTitle);
        $header->bindAttributesFrom($headerData);
        $invoice->attachObject('header', $header);

        $footer = new InvoiceFooter($propertyID, $leaseID, $dueDate, $issueDate, $totalTitle);
        $footer->bindAttributesFrom($headerData);
        $footer->bindAttributesFrom($agentData);
        $footer->bindAttributesFrom($leaseAddress);
        $footer->bindAttributesFrom($bankData);
        $invoice->attachObject('footer', $footer);


        $agentDetails = new AgentDetails($propertyID);
        $agentDetails->bindAttributesFrom($agentData);
        $invoice->attachObject('agentDetails', $agentDetails);

        $invoice->attachObject('statement', new InvoiceLines($totalTitle));
        $invoice->attachObject('foldline', new FoldLines());
        $invoice->attachObject('cutoffline', new CutOffLine());
        $invoice->attachObject('mailingAddress', new MailAddressWindow($propertyID, $leaseID));
        $invoice->attachObject(
            'traccFooter',
            new TraccFooter("assets/clientLogos/tracc_logo.jpg", 'Tenant_Tax_Invoice', 1)
        );

        if (isset($note)) {
            $invoice->attachObject('note', new InvoiceNote($note));
        }
        $invoice->preparePage();


        if (count($newAmounts) > 0) {
            $currentTotal = 0;
            $currentNet = 0;
            $currentTax = 0;

            foreach ($newAmounts as $row) {
                $offset = calculateOffset(
                    $row['invoiceTotalReceipted'],
                    $row['invoiceTotalCredited'],
                    $row['invoiceTotalAdjusted'],
                    $row['creditTotalReceipted'],
                    $row['creditTotalAdjusted'],
                    $row['creditTotalAllocated']
                );

                $currentTotal += ($row['amount'] * 1);
                $currentNet += ($row['netAmount'] * 1);
                $currentTax += ($row['taxAmount'] * 1);

                $total += ($row['amount'] * 1);

                $invoice->renderInvoiceRow(
                    $row['transactionDate'],
                    $invoiceNumber,
                    $row['description'],
                    toMoney($row['netAmount'], null),
                    toMoney($row['taxAmount'], null),
                    toMoney($row['amount'], null)
                );
            }
            $invoice->renderSubTotal(
                'Total For This Tax Invoice',
                toMoney($currentNet, null),
                toMoney($currentTax, null),
                toMoney($currentTotal, null)
            );
        }

        //-- test for page runovers :    for ($x=0; $x<20; $x++) $invoice->renderInvoiceRow(null,null,null,'X','X','X');

        if ($style == InvoiceStatementType::ST_STATEMENT) {
            $invoice->renderTitle('OUTSTANDING AMOUNTS PREVIOUSLY INVOICED');

            if (count($outstandingAmounts) > 0) {
                $previousTotal = 0;

                foreach ($outstandingAmounts as $row) {
                    $offset = calculateOffset(
                        $row['invoiceTotalReceipted'],
                        $row['invoiceTotalCredited'],
                        $row['invoiceTotalAdjusted'],
                        $row['creditTotalReceipted'],
                        $row['creditTotalAdjusted'],
                        $row['creditTotalAllocated']
                    );
                    if (($row['invoiceNumber'] == 0) && ($row['transactionType'] != 'CSH')) {
                        if ($offset != 0) {
                            $offset *= -1;
                            $total += $offset;
                            $previousTotal += $offset;
                            $invoice->renderInvoiceRow(
                                $row['transactionDate'],
                                null,
                                'RECEIPT ALLOCATED AGAINST ABOVE INVOICE(S) *',
                                null,
                                null,
                                toMoney($offset, null)
                            );
                        }
                    } elseif ((($row['amount'] - $offset) != 0) && ($row['transactionType'] != 'CSH')) {
                        $currentAmount = $row['amount'] - $offset;
                        $total += $currentAmount;
                        $previousTotal += $currentAmount;
                        $invoice->renderInvoiceRow(
                            $row['transactionDate'],
                            $row['invoiceNumber'],
                            $row['description'],
                            null,
                            null,
                            toMoney($currentAmount, null)
                        );
                    } elseif (($row['transactionType'] == 'CSH') && ($row['unallocated'] != '') && ($row['unallocated'] != 0)) {
                        $unallocated = dbGetUnallocated(
                            $propertyID,
                            $leaseID,
                            $debtorID,
                            $row['batchNumber'],
                            $row['batchLineNumber']
                        );
                        if ($unallocated) {
                            foreach ($unallocated as $unallocatedItem) {
                                $unallocatedAmount = ($unallocatedItem['amount'] * 1);
                                $total += $unallocatedAmount;
                                $previousTotal += $unallocatedAmount;
                                $invoice->renderInvoiceRow(
                                    $unallocatedItem['date'],
                                    null,
                                    $unallocatedItem['description'],
                                    null,
                                    null,
                                    toMoney($unallocatedAmount, null)
                                );
                            }
                        }
                    }
                }
                $invoice->renderSubTotal('Total Previously Invoiced', null, null, toMoney($previousTotal, null));
            }
        }

        $invoice->renderTotal($total);
        $invoice->renderRemittanceTotal($total);
        $invoice->close();
    }
}


/*******************************************************/


function get_debtors_email($clientID, $debtor_code)
{
    $return_value = '';
    $sqlquery = "SELECT TOP 1 pmco_email, (SELECT pmzz_desc FROM pmzz_param WHERE (pmzz_par_type = 'ACCMGR') AND (pmzz_code = 'TINV')) AS alternative_email FROM pmco_company WHERE (pmco_code = '" . $debtor_code . "')";
    $results = mssql_query($sqlquery);
    while ($row = mssql_fetch_array($results)) {
        prune($row);
        $return_value = $row['pmco_email'];
        if ($return_value == '') {
            $return_value = $row['alternative_email'];
        }
    }
    return $return_value;
}


/*********************************************************/


$propertyID = (isset($_REQUEST['propertyID'])) ? $_REQUEST['propertyID'] : 'BENN79';
$leaseID = (isset($_REQUEST['leaseID'])) ? $_REQUEST['leaseID'] : 'COMS';
$debtorID = (isset($_REQUEST['debtorID'])) ? $_REQUEST['debtorID'] : 'COMS';
$invoiceDate = (isset($_REQUEST['invoiceDate'])) ? $_REQUEST['invoiceDate'] : '01/01/2008';

echo '<link rel="Stylesheet" href="' . ASSET_DOMAIN . 'assets/css/data.css" />';
echo '<form action="dbInvoice.php" method="post">';
echo '<table><tr><td>P: <input type="text" name="propertyID" value="' . $propertyID . '" /></td><td>L: <input type="text" name="leaseID" value="' . $leaseID . '" /></td><td>D: <input type="text" name="debtorID" value="' . $debtorID . '" /></td><td>Date: <input type="text" name="invoiceDate" value="' . $invoiceDate . '" /></td><td><input type="submit" name="submit" value="go &raquo;" /></td></tr></table>';
echo '</form>';

echo '<h1>Test</h1>';
echo '<table>';
echo prepareInvoice(InvoiceStatementType::ST_INVOICE, $leaseID, $propertyID, $invoiceDate);
echo '</table>';
?>
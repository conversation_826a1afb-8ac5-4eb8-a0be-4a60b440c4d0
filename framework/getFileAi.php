<?php


if ($_GET['fileID']) {
        $filename = $_GET['fileID'];
//        if ($filename) $filename = decodeParameter($filename);
        if ($filename) $filename = base64_decode(urldecode($filename));
        $filePath =  $filename;

        
        if (file_exists($filePath)) {
			$extension = substr($filePath, strrpos($filePath,'.')+1);

            // required for IE, otherwise Content-disposition is ignored
            ini_set('zlib.output_compression','Off'); 

			switch($extension)
			{
			  case "pdf": $ctype="application/pdf"; break;
			  case "exe": $ctype="application/octet-stream"; break;
			  case "zip": $ctype="application/zip"; break;
			  case "doc": $ctype="application/msword"; break;
			  case "xls": $ctype="application/vnd.ms-excel"; break;
			  case "xlsx": $ctype="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"; break;
			  case "ppt": $ctype="application/vnd.ms-powerpoint"; break;
			  case "gif": $ctype="image/gif"; break;
			  case "png": $ctype="image/png"; break;
			  case "jpeg": $ctype="image/jpeg"; break;
			  case "jpg": $ctype="image/jpg"; break;
			  case "aba" : $ctype="text/plain"; break;
			  case "txn" : $ctype="application/mbldeft-transaction"; break;
			  case "brf" : $ctype="application/mbldeft-bpay"; break;
			  case "pay" : $ctype="application/mbldeft-payment"; break;
			  case "acc" : $ctype="application/mbldeft-acc"; break;
			  default: $ctype="application/force-download";
			}

 
			header("Content-Type: $ctype");
			header("Content-Disposition: inline; filename=filename.pdf");
			// echo $filePath;
			@readfile($filePath);
        }

}
?>
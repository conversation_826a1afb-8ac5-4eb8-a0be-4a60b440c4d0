<?



  //-- include and invoke the session handler
  include 'lib/classes/Session.php';
  $sess = new Session();

  //-- if ob_get_clean doesnt exist, define the function
  if (!function_exists("ob_get_clean")) {
    function ob_get_clean() {
        $ob_contents = ob_get_contents();
        ob_end_clean();
        return $ob_contents;
    }
}


  include('config.php');

  $referrer = 'http://' . $_SERVER['HTTP_HOST'];

  if ($referrer == HTTPHOST) {
    $sess->set('referrer',HTTPHOST . $_SERVER['SCRIPT_NAME'] . '?' .$_SERVER['QUERY_STRING']);
  } else {
    $sess->drop('referrer');
  }

  $sess->drop('queries');

  define('CLIENTDB',$sess->get('currentDB'));
  define('CLIENTDSN',COREDSN . $sess->get('currentDB'));

  //-- invoke the database handler and create a new connection to the system database
  $dbh = new MSSQL(SYSTEMDSN);

  if ($sess->exists('session_login_time')) {
    $authKey = authKey($sess->get('session_login_time'));
  }
  
  echo 'test';
  



?>
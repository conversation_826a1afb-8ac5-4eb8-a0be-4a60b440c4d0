<?php



error_reporting(E_ALL ^ E_NOTICE);

  //-- if ob_get_clean doesnt exist, define the function
  if (!function_exists("ob_get_clean")) {
    function ob_get_clean() {
        $ob_contents = ob_get_contents();
        ob_end_clean();
        return $ob_contents;
    }
}


  //-- include and invoke the session handler
  include 'lib/classes/Session.php';
  $sess = new Session(); 
  include_once 'config.php';
  
  global $context;

  //-- invoke the database handler and create a new connection to the system database
  $dbh = new MSSQL(SYSTEMDSN);
  $dbList = dbGetDatabaseList();
  
  $worktracc_url = 'http://workorder.tracc-accounting.com.au/api/';

  foreach ($dbList as $db) {
    if ($db['worktracc_api']) {
  
    $clientDB = $db['database_name'];  
    $properties = dbGetRecentProperties(100);
    $tenants = dbGetRecentTenants(1);
    $suppliers = dbGetRecentSuppliers(1);
    $supplierAccounts = dbGetSupplierAccountCodes();
    

    
    try {
      
    
        if ($supplierAccounts)  {
          
          
          
          
          $count = ceil(count($supplierAccounts) / 100);

         
          for ($i=0; $i<$count; $i++) {
            $_accounts = array_slice($supplierAccounts, $i*100, 100);
            $s['key'] = $db['worktracc_api'];
            $s['params'] = $_accounts;
            $result = rest_helper($worktracc_url . 'services/', $s, 'POST');
        if (!$result->error) echo $result->message . '<br/>';
       }
    }
    

    
    if ($properties)  
    foreach ($properties as $p) {
	$p['key'] = $db['worktracc_api'];
	$p['country'] = 'AU';
	$p['propertyType'] = 1;
	$result = rest_helper($worktracc_url . 'property/', $p, 'POST');
	if (!$result->error) echo $result->message . '<br/>';
	
	//-- check the owner!
	
    }
    
    
    if ($tenants) 
    foreach ($tenants as $t) {
	$t['key'] = $db['worktracc_api'];
	$t['country'] = 'AU';
	$t['companyType'] = 3;
	$result = rest_helper($worktracc_url . 'tenant/', $t, 'POST');
	if (!$result->error) echo $result->message . '<br/>';
    }
    

    if ($suppliers)
    foreach ($suppliers as $s) {
	$s['key'] = $db['worktracc_api'];
	$s['country'] = 'AU';
	$s['companyType'] = 3;
	$result = rest_helper($worktracc_url . 'supplier/', $s, 'POST');
	if (!$result->error) echo $result->message . '<br/>';
    }
    
    
    
    
    } catch (Exception $e) {
    echo 'Caught exception: ',  $e->getMessage(), "\n";
}

    }
  }
  


?>
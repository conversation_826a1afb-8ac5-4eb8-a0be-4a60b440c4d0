<?php
/*instructions
specify the old and new codes at the bottom of this file
specify the database on line 14
login the the system and change the hrl to below - then enter
https://client.cirrus8.com.au/framework/dbChangeSupplierCode.php

*/
function chunk($code, $index) {
        $number = substr($code, 0, -1);
        $letter = substr($code, -1);
        return ($number+$index)  . $letter;
}


function replace($code, $index) {
        $number = substr($code, 0, -1);
        $letter = substr($code, -1);
        return ($index)  . $letter;
}


function repairBatch($start = -1000000)
{
    global $dbh;
    global $clientDB;
    $dbh->selectDatabase('Diploma');


    $o = $dbh->executeSet("SELECT batch_nr FROM ar_transaction");
    
    foreach ($o as $t) {
        $s = $t['batch_nr'];
        $value = (is_numeric($s)) ? $s + $start : chunk($s,$start);
        $dbh->executeNonQuery("UPDATE ar_transaction SET batch_nr = '{$value}' WHERE batch_nr = '{$s}'");
    }

   $o = $dbh->executeSet("SELECT batch_nr FROM ap_transaction");
    foreach ($o as $t) {
        $s = $t['batch_nr'];
        $value = (is_numeric($s)) ? $s + $start : chunk($s,$start);
        $dbh->executeNonQuery("UPDATE ap_transaction SET batch_nr = '{$value}' WHERE batch_nr = '{$s}'");
    }

    $o = $dbh->executeSet("SELECT batch_nr FROM ar_batch");
    
    foreach ($o as $t) {
        $s = $t['batch_nr'];
        $value = (is_numeric($s)) ? $s + $start : chunk($s,$start);
        $dbh->executeNonQuery("UPDATE ar_batch SET batch_nr = '{$value}' WHERE batch_nr = '{$s}'");
    }

   $o = $dbh->executeSet("SELECT batch_nr FROM ap_batch");
    foreach ($o as $t) {
        $s = $t['batch_nr'];
        $value = (is_numeric($s)) ? $s + $start : chunk($s,$start);
        $dbh->executeNonQuery("UPDATE ap_batch SET batch_nr = '{$value}' WHERE batch_nr = '{$s}'");
    }




   $o = $dbh->executeSet("SELECT pmxd_f_batch,pmxd_t_batch FROM pmxd_ar_alloc");
    foreach ($o as $a) {
        $f = $a['pmxd_f_batch'];
		$t = $a['pmxd_t_batch'];
        $fv = (is_numeric($f)) ? $f + $start : chunk($f,$start);
		$tv = (is_numeric($t)) ? $t + $start : chunk($t,$start);

        $dbh->executeNonQuery("UPDATE pmxd_ar_alloc SET pmxd_f_batch = '{$fv}' WHERE pmxd_f_batch = '{$f}'");
        $dbh->executeNonQuery("UPDATE pmxd_ar_alloc SET pmxd_t_batch = '{$tv}' WHERE pmxd_t_batch = '{$t}'");
    }


   $o = $dbh->executeSet("SELECT pmxc_f_batch, pmxc_t_batch FROM pmxc_ap_alloc");
    foreach ($o as $a) {
        $f = $a['pmxc_f_batch'];
        $fv = (is_numeric($f)) ? $f + $start : chunk($f,$start);
		$t = $a['pmxc_t_batch'];
		$tv = (is_numeric($t)) ? $t + $start : chunk($t,$start);
		$dbh->executeNonQuery("UPDATE pmxc_ap_alloc SET pmxc_f_batch = '{$fv}' WHERE pmxc_f_batch = '{$f}'");
        $dbh->executeNonQuery("UPDATE pmxc_ap_alloc SET pmxc_t_batch = '{$tv}' WHERE pmxc_t_batch = '{$t}'");
    }



   $o = $dbh->executeSet("SELECT pmuc_batch FROM pmuc_unall_csh");
    foreach ($o as $t) {
        $s = $t['pmuc_batch'];
        $value = (is_numeric($s)) ? $s + $start : chunk($s,$start);
        $dbh->executeNonQuery("UPDATE pmuc_unall_csh SET pmuc_batch = '{$value}' WHERE pmuc_batch = '{$s}'");
    }

   $o = $dbh->executeSet("SELECT pmrc_batch FROM pmrc_receipt");
    foreach ($o as $t) {
        $s = $t['pmrc_batch'];
        $value = (is_numeric($s)) ? $s + $start : chunk($s,$start);
        $dbh->executeNonQuery("UPDATE pmrc_receipt SET pmrc_batch = '{$value}' WHERE pmrc_batch = '{$s}'");
    }




/*
UPDATE ar_transaction SET batch_nr = batch_nr + 1000000;
UPDATE ap_transaction SET batch_nr = batch_nr + 1000000;
UPDATE pmxd_ar_alloc SET pmxd_f_batch = pmxd_f_batch + 1000000, pmxd_t_batch = pmxd_t_batch + 1000000;
UPDATE pmxc_ap_alloc SET pmxc_f_batch = pmxc_f_batch + 1000000, pmxc_t_batch = pmxc_t_batch + 1000000;
UPDATE ap_batch SET batch_nr = batch_nr + 1000000;
UPDATE ar_batch SET batch_nr = batch_nr + 1000000;
UPDATE pmuc_unall_csh SET pmuc_batch = pmuc_batch + 1000000;
UPDATE pmrc_receipt SET pmrc_batch = pmrc_batch + 1000000;




$sql[] = "UPDATE    ap_transaction
        SET              creditor_code = '$newCode',
        supplier_code = '$newCode'
        WHERE     (creditor_code = '$oldCode')";

$sql[] = "UPDATE accrued_expenditure
   SET [lease_code] = '$newCode'
 WHERE [lease_code] = '$oldCode'";

$sql[] ="UPDATE pmcj_c_phone
   SET [pmcj_company] = '$newCode'
 WHERE [pmcj_company] = '$oldCode'";

$sql[] ="UPDATE pmct_c_contact
   SET [pmct_company] = '$newCode'
 WHERE [pmct_company] = '$oldCode' ";

$sql[] ="UPDATE pmcw_c_role
   SET [pmcw_company] = '$newCode'
 WHERE [pmcw_company] = '$oldCode'";

$sql[] = "UPDATE future_costs
   SET [lease_code] = '$newCode'
 WHERE [lease_code] = '$oldCode'";

$sql[] = "UPDATE pmbc_reco_chq
   SET [pmbc_creditor] = '$newCode'
      ,[pmbc_owner] = '$newCode'
   WHERE [pmbc_creditor] = '$oldCode'";

$sql[] ="UPDATE pmos_o_share
   SET [pmos_owner] = '$newCode'
 WHERE pmos_owner = '$oldCode'";

$sql[] ="UPDATE pmpr_property
   SET [pmpr_owner] = '$newCode'
 WHERE [pmpr_owner] = '$oldCode'";

$sql[] ="UPDATE pmpr_property
   SET [pmpr_agent] = '$newCode'
 WHERE [pmpr_agent] = '$oldCode'";

$sql[] ="UPDATE pmoc_o_chart
   SET [pmoc_owner] = '$newCode'
 WHERE [pmoc_owner] = '$oldCode'";

$sql[] ="UPDATE pmxc_ap_alloc
   SET [pmxc_s_creditor] = '$newCode'
 WHERE [pmxc_s_creditor] = '$oldCode'";

$sql[] ="UPDATE pmco_company
   SET [pmco_code] = '$newCode',
      [pmco_e_company] = '$newCode',
      [pmco_e_debtor] = '$newCode',
      [pmco_e_customer] = '$newCode',
      [pmco_e_supplier] = '$newCode',
      [pmco_e_creditor] = '$newCode'
 WHERE [pmco_code] = '$oldCode'";

$sql[] ="UPDATE pmle_lease
   SET [pmle_supplier] = '$newCode'
 WHERE [pmle_supplier] = '$oldCode'";

$sql[] ="UPDATE newcompanyhistory_temp
   SET [pmco_code] = '$newCode'
       WHERE [pmco_code] = '$oldCode'";

$sql[] ="UPDATE pmco_company_change_logg
   SET [company] = '$newCode'
   WHERE [company] = '$oldCode'";

$sql[] ="UPDATE temp_ap_owner
   SET [ownerID] = '$newCode'
 WHERE [ownerID] = '$oldCode'";

$sql[] ="UPDATE temp_pm_payment
   SET [creditor] = '$newCode'
 WHERE [creditor] = '$oldCode'";

$sql[] ="UPDATE temp_pmos_o_share
   SET [pmos_owner] = '$newCode'
 WHERE [pmos_owner] = '$oldCode'";

$sql[] ="UPDATE temp_pmpr_property
   SET [pmpr_owner] = '$newCode'
 WHERE [pmpr_owner] = '$oldCode'";

$sql[] ="UPDATE temp_pmpr_property
   SET [pmpr_agent] = '$newCode'
 WHERE [pmpr_agent] = '$oldCode'";



	return $dbh->executeNonQuery($sql);
*/
}



  //-- include and invoke the session handler
  include 'lib/classes/Session.php';
  $sess = new Session();


  //-- if ob_get_clean doesnt exist, define the function
  if (!function_exists("ob_get_clean")) {
    function ob_get_clean() {
        $ob_contents = ob_get_contents();
        ob_end_clean();
        return $ob_contents;
    }
}


  include('config.php');



  //-- invoke the database handler and create a new connection to the system database
  $dbh = new MSSQL(SYSTEMDSN);


repairBatch(1000000);







?>
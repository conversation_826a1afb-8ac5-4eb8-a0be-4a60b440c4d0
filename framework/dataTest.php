<?

include 'config.php';



  $dbh = new MSSQL(COREDSN . 'GW_npms');
  
  $sql = "SELECT TOP 1 * FROM ap_transaction";
$result = $dbh->executeSingle($sql);
print_r($result);


/*
 
 at the moment prune functions to strip out the datetime objects returned by SQL_SRV and format them as australian dates
 
 in time the best policy would be to maintain the dates in the DateTime object as much as possible - formatting only for print purposes - and converting dates coming from $_REQUEST back into datetime objects (possibly through parsing on bind)
 
 this would in time enable multiple (and transparent) timezone support for other countries - but is reliant on PHP DateTime remaining as the output format used by the SQL_SRV driver
 
*/


?>
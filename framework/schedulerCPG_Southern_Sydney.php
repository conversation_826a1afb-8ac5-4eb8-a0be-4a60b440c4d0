<?php
error_reporting (E_ALL ^ E_NOTICE);

 //-- if ob_get_clean doesnt exist, define the function
if (!function_exists ("ob_get_clean"))
{
	function ob_get_clean ()
	{
		$ob_contents = ob_get_contents();
		ob_end_clean();
		return $ob_contents;
	}
}

function unsetAll(&$items)
{
	foreach ($items as $id => $item) unset ($items[$id]);
}

//-- include and invoke the session handler
include 'lib/classes/Session.php';
$sess = new Session();

include_once 'config.php';

require 'vendor/autoload.php';


global $context;

$referrer = 'http://' . $_SERVER['HTTP_HOST'];
if ($referrer == HTTPHOST)
{
	$sess->set ('referrer', HTTPHOST . $_SERVER['SCRIPT_NAME'] . '?' . $_SERVER['QUERY_STRING']);
}
else $sess->drop ('referrer');
$sess->drop ('queries');

//-- invoke the database handler and create a new connection to the system database
$dbh = new MSSQL (SYSTEMDSN);
$dbList = dbGetDatabaseList ();

//-- set a type if you want to limit the tasks being processed to a single type (ie - AR/AP/standing charges/whatever - anything can be assigned an arbitrary type at the insertion stage)
if (isset ($_REQUEST['type'])) $type = $_REQUEST['type'];

////$queue = new Queue ($type);
$queue = new Queue ($type, null, 'CSS');
$queue->fetch (TASK_THRESHOLD);
$startList =  $queue->totalItems;

date_default_timezone_set ('Australia/Perth');
define ('TODAY', date ('d/m/Y'));

logScheduler (date ('h:i:sA d/m/Y') . ' <b>Scheduler started (' . $startList . ' items)...</b>');
dbUpdateStatistic ('SchedulerLastRun', time ());

$userID = 1;
$currentTime = time ();
$authKey = md5 ($userID . session_id () . $_SERVER['HTTP_USER_AGENT'] . $currentTime);
// dbInsertLoginStats ($userID, $_SERVER['HTTP_USER_AGENT'], $_SERVER['REMOTE_ADDR'], $_SERVER['REMOTE_HOST'], session_id(), $authKey, $currentTime);
pingUser ($userID, 1);
$currentTime = time();
do
{
    $workTime = time();
    if($currentTime + 240 >= $workTime  )
	$task = $queue->next (TASK_THRESHOLD);
    else  $task = null;

	if ($task)
	{
		$databaseID = $task['databaseID'];
		$database = dbGetDatabase ($databaseID);

		// logData (print_array ($database));
        if ($database)
		{
			$clientDB = $database['database_name'];
			$clientDSN = COREDSN . $database['database_name'];
			// logData ('DATABASE => ' . $clientDB);
        }

		//-- set up the user
		$sess->set ('registered', true);

		//-- need to register in the database that a user has logged in!
		$userDetails = dbGetUserDetails ($task['createdBy'],false);

		if(!$userDetails)
		    continue;

        if ($userDetails) {
			$sess->bindAttributesFrom ($userDetails);
			$sess->set ('user_level', $userDetails['groupName']);

            //set user_type to TA (run reports as TA)
            $sess->set ('user_type', 'A');
		}
        $sess->set ('dbList', $dbList);
        $sess->set ('un',$sess->get ('user_name'));
        $sess->set ('clientID', $databaseID);
        $sess->set ('old_client_id', $databaseID);
		$sess->set ('userID',$sess->get ('un'));
		$sess->set ('databaseID', $databaseID);
        $sess->set ('currentDB', $database['database_name']);
        $sess->set ('database', $database['description']);
		$sess->set ('development', $database['development']);

        $sess->set ('country_code', $database['country_code']);
        $cdf_content = json_decode(file_get_contents(CDF_JSON_FILE), true);
        $sess->set('country_default', $cdf_content[$database['country_code']]);

        //$timezone = dbGetTimezone($dbList[$database]['timezone']);
        //$sess->set ('clientTimezone', $timezone['timezone_info']);

		//-- set up global variables used for determining report paths
		$clientDirectory = str_replace (' ', '', $database['description']);
		$pathPrefix = REPORTPATH . '/';
		$sess->set ('pathPrefix', $pathPrefix);
		$sess->set ('clientDirectory', $clientDirectory);
		$globalDownloadLink = '' . $clientDirectory;

		extract ($sess->items, EXTR_OVERWRITE);

		// logData (print_array (get_defined_vars ()));
		// print_r (get_defined_vars ());

		$context = $task['data'];
		$GLOBALS = $context;

		$context[IS_TASK] = true;
		$context[TASK_COMPLETE] = false;

		// sendMail (ADMINMAIL_ADDRESS,ADMINMAIL_NAME, print_array ($context, true) ,"Scheduler Test " . $context['command']);

		//-- if a module and command is set (framework prerequisite) then fetch the command
		if (isset ($context['module']) && isset ($context['command']))
		{
			logScheduler ('<b>Scheduled Task</b> ' . $context['command']);
			try
			{
				$output = fetchCommand($context['command'],$context['module']);
			}
			catch (PDFlibException $e)
			{
				$msg ='PDFLIB' . PHP_EOL ;
				$msg .='code : '.$e->getCode() . PHP_EOL ;
				$msg .='file : '.$e->getFile() . 'line : '.$e->getLine() . PHP_EOL ;
				$msg .='mess : '.$e->getMessage() . PHP_EOL ;
				$msg .='detl : '.$e . PHP_EOL ;
				$msg .='PDFLIB';
				sendMail (ADMINMAIL_ADDRESS, ADMINMAIL_NAME, $msg ,'Scheduler Failed');
			}
			catch (Exception $e)
			{
				$msg = 'Exception' . PHP_EOL ;
				$msg .= 'code : '.$e->getCode() . PHP_EOL ;
				$msg .='file : '.$e->getFile() . 'line : '.$e->getLine() . PHP_EOL ;
				$msg .='mess : '.$e->getMessage() . PHP_EOL ;
				$msg .='detl : '.$e. PHP_EOL ;
				$msg .='Exception';
				sendMail (ADMINMAIL_ADDRESS, ADMINMAIL_NAME, $msg ,'Scheduler Failed');
			}
            catch (Error $e)
            {
                $error_code = SchedulerErrorHandler( 1 , $e->getMessage()  , $e->getFile(), $e->getLine() );
                try
                {
                    if($error_code) $queue->add_error_code( $error_code );
                }
                catch (Error $e)
                {}
            }
			if (DEBUG) echo $output;
			//-- a successful run will return TASK_COMPLETE = true in context
			if ($context[TASK_COMPLETE])
			{
				$queue->delete();
				logScheduler('<b>Task Complete</b>');
			}
			$context[TASK_COMPLETE] = false;
		}
	}
}
while ($task != null);

// dbLogUser (1);

//-- on success work out how many items remain and send a report to the admin
$queue->fetch (TASK_THRESHOLD);
$endList = $queue->totalItems;
$completed = $startList-$endList;
// sendMail (ADMINMAIL_ADDRESS, ADMINMAIL_NAME, '&nbsp;', "{$startList} items scheduled, {$completed} items completed - {$endList} items remaining");

/*
error_reporting (E_ALL ^ E_NOTICE);

//-- if ob_get_clean doesnt exist, define the function
if (!function_exists("ob_get_clean"))
{
	function ob_get_clean()
	{
		$ob_contents = ob_get_contents ();
		ob_end_clean ();
		return $ob_contents;
	}
}


function unsetAll(&$items)
{
	foreach ($items as $id => $item) unset($items[$id]);
}

//-- include and invoke the session handler
include 'lib/classes/Session.php';
$sess = new Session ();

include_once 'config.php';

$referrer = 'http://' . $_SERVER['HTTP_HOST'];
if ($referrer == HTTPHOST)
{
	$sess->set('referrer',HTTPHOST . $_SERVER['SCRIPT_NAME'] . '?' .$_SERVER['QUERY_STRING']);
}
else
{
	$sess->drop('referrer');
}
$sess->drop ('queries');

//-- invoke the database handler and create a new connection to the system database
$dbh = new MSSQL (SYSTEMDSN);
$dbList = dbGetDatabaseList ();

//-- set a type if you want to limit the tasks being processed to a single type (ie - AR/AP/standing charges/whatever - anything can be assigned an arbitrary type at the insertion stage)
if (isset ($_REQUEST['type'])) $type = $_REQUEST['type'];

$queue = new Queue ($type);
$queue->fetch (TASK_THRESHOLD);
$startList =  $queue->totalItems;

logScheduler (date ('h:i:sA d/m/Y') . ' <b>Scheduler started (' . $startList . ' items)...</b>');
dbUpdateStatistic ('SchedulerLastRun', time ());

$userID = 1;
$currentTime = time ();
$authKey = md5($userID . session_id() . $_SERVER['HTTP_USER_AGENT'] . $currentTime);
dbInsertLoginStats ($userID, $_SERVER['HTTP_USER_AGENT'], $_SERVER['REMOTE_ADDR'], $_SERVER['REMOTE_HOST'], session_id (), $authKey, $currentTime);
pingUser ($userID, 1);

do
{
	$task = $queue->next (TASK_THRESHOLD);

	if ($task)
	{
        $databaseID = $task['databaseID'];
        $database = dbGetDatabase ($databaseID);
        // logData (print_array ($database));
        if ($database)
		{
			$clientDB = $database['database_name'];
			$clientDSN = COREDSN . $database['database_name'];
			// logData ('DATABASE => ' . $clientDB);
		}

		//-- set up the user
		$sess->set ('registered', true);

		//-- need to register in the database that a user has logged in!
		$userDetails = dbGetUserDetails ($task['createdBy']);
        if ($userDetails) $sess->bindAttributesFrom ($userDetails);
        $sess->set ('dbList', $dbList);
        $sess->set ('un', $sess->get ('user_name'));
        $sess->set ('clientID', $databaseID);
        $sess->set ('old_client_id', $databaseID);
        $sess->set ('userID',$sess->get ('un'));
        $sess->set ('currentDB', $database['database_name']);
        $sess->set ('database', $database['description']);
		$sess->set ('development', $database['development']);

		//-- set up global variables used for determining report paths
        $clientDirectory = str_replace (' ', '', $database['description']);
        $pathPrefix = REPORTPATH . '/';
        $sess->set ('pathPrefix', $pathPrefix);
        $sess->set ('clientDirectory', $clientDirectory);
        $globalDownloadLink = '' . $clientDirectory;

        extract ($sess->items, EXTR_OVERWRITE);

		// logData (print_array (get_defined_vars ()));
        // print_r (get_defined_vars ());

		$context = $task['data'];
		$GLOBALS = $context;

		$context[IS_TASK] = true;
		$context[TASK_COMPLETE] = false;

		//-- if a module and command is set (framework prerequisite) then fetch the command
        if (isset ($context['module']) && isset ($context['command']))
		{
			logScheduler ('<b>Scheduled Task</b> ' . $context['command']);
			$output = fetchCommand ($context['command'], $context['module']);
			//-- a successful run will return TASK_COMPLETE = true in context
			if ($context[TASK_COMPLETE])
			{
				$queue->delete ();
				logScheduler ('<b>Task Complete</b>');
			}
			$context[TASK_COMPLETE] = false;
        }
    }

}
while ($task != null);

//-- on success work out how many items remain and send a report to the admin
$queue->fetch (TASK_THRESHOLD);
$endList = $queue->totalItems;
$completed = $startList-$endList;
sendMail (ADMINMAIL_ADDRESS, ADMINMAIL_NAME, '&nbsp;', "{$startList} items scheduled, {$completed} items completed - {$endList} items remaining");
*/
?>

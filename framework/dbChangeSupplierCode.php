<?php
/*instructions
specify the old and new codes at the bottom of this file
specify the database on line 14
login the the system and change the hrl to below - then enter
https://client.cirrus8.com.au/framework/dbChangeSupplierCode.php

*/
function changeSupplierCode ($oldCode, $newCode)
{

global $dbh;
	global $clientDB;
$dbh->selectDatabase('FPS_npms');

$sql[] = "UPDATE    ap_transaction
        SET              creditor_code = '$newCode',
        supplier_code = '$newCode'
        WHERE     (creditor_code = '$oldCode')";

$sql[] = "UPDATE accrued_expenditure
   SET [lease_code] = '$newCode'      
 WHERE [lease_code] = '$oldCode'";
 
$sql[] ="UPDATE pmcj_c_phone
   SET [pmcj_company] = '$newCode'      
 WHERE [pmcj_company] = '$oldCode'";
 
$sql[] ="UPDATE pmct_c_contact
   SET [pmct_company] = '$newCode'      
 WHERE [pmct_company] = '$oldCode' ";
 
$sql[] ="UPDATE pmcw_c_role
   SET [pmcw_company] = '$newCode'      
 WHERE [pmcw_company] = '$oldCode'";

$sql[] = "UPDATE future_costs
   SET [lease_code] = '$newCode'      
 WHERE [lease_code] = '$oldCode'";

$sql[] = "UPDATE pmbc_reco_chq
   SET [pmbc_creditor] = '$newCode'
      ,[pmbc_owner] = '$newCode'
   WHERE [pmbc_creditor] = '$oldCode'";

$sql[] ="UPDATE pmos_o_share
   SET [pmos_owner] = '$newCode'      
 WHERE pmos_owner = '$oldCode'";
 
$sql[] ="UPDATE pmpr_property
   SET [pmpr_owner] = '$newCode'      
 WHERE [pmpr_owner] = '$oldCode'";
 
$sql[] ="UPDATE pmpr_property
   SET [pmpr_agent] = '$newCode'      
 WHERE [pmpr_agent] = '$oldCode'";
 
$sql[] ="UPDATE pmoc_o_chart
   SET [pmoc_owner] = '$newCode'
 WHERE [pmoc_owner] = '$oldCode'";
 
$sql[] ="UPDATE pmxc_ap_alloc
   SET [pmxc_s_creditor] = '$newCode'    
 WHERE [pmxc_s_creditor] = '$oldCode'"; 
 
$sql[] ="UPDATE pmco_company
   SET [pmco_code] = '$newCode',
      [pmco_e_company] = '$newCode',
      [pmco_e_debtor] = '$newCode',
      [pmco_e_customer] = '$newCode',
      [pmco_e_supplier] = '$newCode',
      [pmco_e_creditor] = '$newCode'     
 WHERE [pmco_code] = '$oldCode'";
 
$sql[] ="UPDATE pmle_lease
   SET [pmle_supplier] = '$newCode'     
 WHERE [pmle_supplier] = '$oldCode'";
 
$sql[] ="UPDATE newcompanyhistory_temp
   SET [pmco_code] = '$newCode'
       WHERE [pmco_code] = '$oldCode'";
       
$sql[] ="UPDATE pmco_company_change_logg
   SET [company] = '$newCode'
   WHERE [company] = '$oldCode'";
   
$sql[] ="UPDATE temp_ap_owner
   SET [ownerID] = '$newCode'     
 WHERE [ownerID] = '$oldCode'"; 
 
$sql[] ="UPDATE temp_pm_payment
   SET [creditor] = '$newCode'      
 WHERE [creditor] = '$oldCode'"; 
 
$sql[] ="UPDATE temp_pmos_o_share
   SET [pmos_owner] = '$newCode'      
 WHERE [pmos_owner] = '$oldCode'";
 
$sql[] ="UPDATE temp_pmpr_property
   SET [pmpr_owner] = '$newCode' 
 WHERE [pmpr_owner] = '$oldCode'"; 
 
$sql[] ="UPDATE temp_pmpr_property
   SET [pmpr_agent] = '$newCode'      
 WHERE [pmpr_agent] = '$oldCode'"; 
 
 
 
	return $dbh->executeNonQuery($sql);
}
  
  

  //-- include and invoke the session handler
  include 'lib/classes/Session.php';
  $sess = new Session();


  //-- if ob_get_clean doesnt exist, define the function
  if (!function_exists("ob_get_clean")) {
    function ob_get_clean() {
        $ob_contents = ob_get_contents();
        ob_end_clean();
        return $ob_contents;
    }
}


  include('config.php');



  //-- invoke the database handler and create a new connection to the system database
  $dbh = new MSSQL(SYSTEMDSN);
  
  $oldCode = 'V&PL';
$newCode = 'VPLA';
changeSupplierCode ($oldCode, $newCode);
  



          
            
            
?>
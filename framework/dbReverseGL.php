<?

  //-- include and invoke the session handler
  include 'lib/classes/Session.php';
  $sess = new Session();


  //-- if ob_get_clean doesnt exist, define the function
  if (!function_exists("ob_get_clean")) {
    function ob_get_clean() {
        $ob_contents = ob_get_contents();
        ob_end_clean();
        return $ob_contents;
    }
}


  include('config.php');



  //-- invoke the database handler and create a new connection to the system database
  $dbh = new MSSQL(SYSTEMDSN);
  

  

$databases = $dbh->executeObjects("SELECT * from database_list");



foreach ($databases as $db) {
	
	echo "<h1>{$db->description}</h1>";
	$dbh->selectDatabase($db->database_name);
	$sql = "select
transaction_id,
account_id,
transaction_amount,
period,
year,
property_id
from gl_transaction WHERE
source = 'AR'
AND transaction_type = 'INV'
AND ((account_id < 10000 AND transaction_amount > 0) OR (account_id = 10001 AND transaction_amount < 0) OR (account_id = 10003 AND transaction_amount > 0))
";
	$transactions = $dbh->executeObjects($sql);

	if ($transactions) foreach ($transactions as $t) {
		$sql = "UPDATE gl_transaction SET transaction_amount = transaction_amount * -1 WHERE transaction_id = '{$t->transaction_id}'";
	//	echo "{$sql}<br/>";
		$dbh->executeNonQuery($sql);
		$reversedAmount = bcmul($t->transaction_amount,2, 2);
		$sql = "UPDATE gl_trial_balance SET balanceAccruals = balanceAccruals - {$reversedAmount} WHERE accountID = {$t->account_id} AND year={$t->year} AND period = {$t->period} AND propertyID = '{$t->property_id}'";
		//echo "{$sql}<br/>";
		$dbh->executeNonQuery($sql);
	}
	
	
}




?>
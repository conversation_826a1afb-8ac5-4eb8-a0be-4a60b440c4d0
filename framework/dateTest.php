<?

include 'lib/functions.php';
//include 'lib/dateFunctions.php';



$date = '31/01/2010';

echo $date . '<br/>';
echo oneDayAfter($date) . '<br/>';
echo oneDayBefore($date) . '<br/>';
echo weeksAfter($date,1) . '<br/>';
echo weeksAfter($date,2) . '<br/>';
echo weeksBefore($date,1) . '<br/>';
echo weeksBefore($date,2) . '<br/>';
echo monthsAfter($date,1) . '<br/>';
echo monthsAfter($date,2) . '<br/>';
echo monthsAfter($date,3) . '<br/>';
echo monthsAfter($date,24) . '<br/>';
echo monthsBefore($date,1) . '<br/>';
echo monthsBefore($date,2) . '<br/>';
echo monthsBefore($date,3) . '<br/>';
echo monthsBefore($date,24) . '<br/>';




?>
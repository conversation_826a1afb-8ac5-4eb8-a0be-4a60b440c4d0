<?php

session_start();

  if (!function_exists("ob_get_clean")) {
    function ob_get_clean() {
        $ob_contents = ob_get_contents();
        ob_end_clean();
        return $ob_contents;
    }
}

include('config.php');




if ($_REQUEST['fileID']) {
        $filename = $_REQUEST['fileID'];
        if ($filename) $filename = decodeParameter($filename);
        $filePath = FILEBROWSER_BASE . '/' . $filename;
        



        if (@file_exists($filePath)) {


                // required for IE, otherwise Content-disposition is ignored
                if(ini_get('zlib.output_compression')) ini_set('zlib.output_compression', 'Off');

        switch($extension)
        {
          case "pdf": $ctype="application/pdf"; break;
          case "exe": $ctype="application/octet-stream"; break;
          case "zip": $ctype="application/zip"; break;
          case "doc": $ctype="application/msword"; break;
          case "xls": $ctype="application/vnd.ms-excel"; break;
          case "ppt": $ctype="application/vnd.ms-powerpoint"; break;
          case "gif": $ctype="image/gif"; break;
          case "png": $ctype="image/png"; break;
          case "jpeg":
          case "jpg": $ctype="image/jpg"; break;
            case "aba" : $ctype="text/plain"; break;
          default: $ctype="application/force-download";
}

        header("Pragma: public"); // required
        header("Expires: 0");
        header("Cache-Control: must-revalidate, post-check=0, pre-check=0");
        header("Cache-Control: private",false); // required for certain browsers
        header("Content-Type: $ctype");
        // change, added quotes to allow spaces in filenames
        header("Content-Disposition: attachment; filename=\"".basename($filePath)."\";");
        header("Content-Transfer-Encoding: binary");
        header("Content-Length: ".filesize($filePath));
        readfile($filePath);
        exit();


        } else {
                $host  = $_SERVER['HTTP_HOST'];
                $uri   = rtrim(dirname($_SERVER['PHP_SELF']), '/\\');
                $extra = 'index.php';
                header("Location: http://$host$uri/$extra?command=error");
                exit();

                echo 'File cannot be found - please contact a Tracc Administrator'; }
}

                $host  = $_SERVER['HTTP_HOST'];
                $uri   = rtrim(dirname($_SERVER['PHP_SELF']), '/\\');
                $extra = 'index.php';
                header("Location: http://$host$uri/$extra?command=error");


?>
APP_ENV=local
DISPLAY_ERRORS=1
DEBUG=true
DEBUG_SQL=false
BASEPATH=D:/www/cirrus8
CALPATH=D:/www/cirrus8/ical
APP_PATH=D:/www/cirrus8/framework
APP_HTTPHOST=https://client.cirrus8.com.au
APP_ICALURL=webcal://client.cirrus8.com.au/framework

DB_HOST=***********
DB_DATABASE=dbname
DB_USERNAME=dbuser
DB_PASSWORD=ABC123

FM_DB=cirrus_fm

PDFLIB_LICENSE=ABC123

SSO_ENABLED=true
AUTHENTICATOR=sso
ASSET_DOMAIN=https://client.cirrus8.com.au/framework/
UPLOADED_ASSET_DOMAIN=https://client.cirrus8.com.au/framework/
SSO_TOKEN=https://c8-api.cirrus8.com.au/sso/session/fetchTokenData?token=
SSO_CHECK=https://c8-api.cirrus8.com.au/sso/session/checkSess
SSO_GET=https://c8-api.cirrus8.com.au/sso/session/OneLogin
LOGOUT_URL=https://c8-api.cirrus8.com.au/sso/session/destroy?app=cirrus8
MY_C8_API=https://my-c8-api.cirrus8.com.au/api/
C8_API_URL=https://c8-api.cirrus8.com.au/;
SSO_LOGIN=https://login.cirrus8.com.au/?returnTo=cirrus8
CFM_USE_CLIENT_URL=https://fm.cirrus8.com.au/load/
MY_ACCOUNT_URL=https://admin.cirrus8.com.au/profile
FM_URL=https://fm.cirrus8.com.au/
HELPJUICE_KEY=ABC123
HELPJUICE_JWT_URL=https://helpjuice.com/jwt/cirrus8
C8_SECURE_LOGIN=https://login.cirrus8.com.au
POSTMARK_AWS_S3_BUCKET=cirrus8-file-live
ADMIN_URL=https://admin.cirrus8.com/

## AWS S3
AWS_S3_ENABLED=false
AWS_S3_KEY=ABC123
AWS_S3_SECRET=ABC123
AWS_S3_REGION=ap-southeast-2
AWS_S3_BUCKET=cirrus8
AWS_S3_BUCKET_MGMT=cirrus8-mgmt-reports

## POSTMARK
POSTMARK_TOKEN=ABC123
POSTMARK_TIMEOUT=400

## ASSURO
ASSURO_URI=https://api.assuro.com.au/api/v1/draft/create
ASSURO_CLIENT_KEY=ABC123
ASSURO_SECRET_KEY=ABC123
ASSURO_CHANNEL_PARTNER_ID=ABC123


## JASPER REPORT
JASPER_SERVER=http://***********/jasperserver
JASPER_USER=jasperadmin
JASPER_PASS=jasperadmin

## SMS
SMS_USERNAME=smsUser
SMS_PASSWORD=ABC123
SMS_URL=https://api.smsbroadcast.com.au/api-adv.php
SMS_SERVICE=tallbob
SMS2_URL=https://api.tallbob.com/v2/sms/send
SMS2_TOKEN=NABC123

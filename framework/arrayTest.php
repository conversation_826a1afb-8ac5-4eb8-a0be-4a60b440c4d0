<?
include 'lib/functions.php';

$units[0] = array('unitStatus' => 'V', 'unitFromDate' => '01/01/1900', 'unitToDate' => '31/12/1999');
$units[1] = array('unitStatus' => 'V', 'unitFromDate' => '01/01/2000', 'unitToDate' => '31/12/2001');
$units[2] = array('unitStatus' => 'O', 'unitFromDate' => '01/01/2002', 'unitToDate' => '31/12/2003');
$units[3] = array('unitStatus' => 'V', 'unitFromDate' => '01/01/2004', 'unitToDate' => '31/12/2005');
$units[4] = array('unitStatus' => 'V', 'unitFromDate' => '01/01/2006', 'unitToDate' => '31/12/2007');
$units[5] = array('unitStatus' => 'O', 'unitFromDate' => '01/01/2008', 'unitToDate' => '31/12/2009');

echo '<br/><br/>';
		$search = true;
		$dataSet = $units;  //- assumes unit history is chronological
		$flag = array();
		while ($search) {
				$search = false; // force it to stop on the next pass [if no match is found]
				$lastUnit = null;
				foreach ($dataSet as $i => $unit) {
						if ($lastUnit != null) {
								if (($unit['unitStatus'] == $lastUnit['unitStatus']) && ($unit['unitStatus'] == 'V'))
								{
									$unitFromDate = toDateStamp($unit['unitFromDate']);
									$lastFromDate = toDateStamp($lastUnit['unitFromDate']);
									if ($lastFromDate < $unitFromDate) { $dataSet[$i]['unitFromDate'] = $lastUnit['unitFromDate']; $flag[$i] = true; $flag[$lastIndex] = true;
									unset($dataSet[$lastIndex]);
									}
									 $search = true; // do another pass
								}
						}
						$lastUnit = $dataSet[$i];
						$lastIndex =  $i;
				}
		}
		echo print_array($dataSet,true);
		echo print_array($flag,true);
		echo print_array(array_diff_assoc($units,$dataSet),true);



$dates[1] = '10/09/1980';
$dates[2] = '27/02/2007';
$dates[3] = '28/03/2007';
$dates[4] = '31/12/2099';
$dates[5] = '31/12/2999';
/*

echo '<br/> unsetting date 3<br/>';
unset($dates[3]);
*/
?>
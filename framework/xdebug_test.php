<?php
// Session Test File
include_once 'config.php';
include_once 'lib/classes/Session.php';

echo "<h1>Session Test</h1>";

// Test session functionality
echo "<h2>Session ID Test:</h2>";
$session = Session::getInstance();

echo "<p>session_id() before: '" . session_id() . "'</p>";
echo "<p>Session exists(): " . ($session->exists() ? 'true' : 'false') . "</p>";

// Test setting a session variable
$session->set('test_key', 'test_value');
echo "<p>Set test_key = 'test_value'</p>";

// Test getting the session variable
$retrieved_value = $session->get('test_key');
echo "<p>Retrieved test_key: '" . $retrieved_value . "'</p>";

// Check if it's in $_SESSION
echo "<p>\$_SESSION['test_key']: '" . (isset($_SESSION['test_key']) ? $_SESSION['test_key'] : 'NOT SET') . "'</p>";

echo "<p>session_id() after: '" . session_id() . "'</p>";
echo "<p>Session exists() after: " . ($session->exists() ? 'true' : 'false') . "</p>";

// Display all session data
echo "<h2>Session Data:</h2>";
echo "<h3>\$_SESSION contents:</h3>";
echo "<pre>" . print_r($_SESSION, true) . "</pre>";

echo "<h3>Session->items contents:</h3>";
echo "<pre>" . print_r($session->items, true) . "</pre>";

// Test breakpoint - set a breakpoint on the next line
$test_variable = "Set a breakpoint here!";
echo "<p>Test variable: " . $test_variable . "</p>";
?>

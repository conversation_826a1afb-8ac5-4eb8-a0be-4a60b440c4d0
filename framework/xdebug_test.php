<?php
// Session Test File
echo "<h1>Session Debug Test</h1>";

// Test 1: Check session status before any includes
echo "<h2>Test 1: Before any includes</h2>";
echo "<p>session_status(): " . session_status() . " (1=disabled, 2=active, 3=none)</p>";
echo "<p>session_id(): '" . session_id() . "'</p>";
echo "<p>\$_SESSION isset: " . (isset($_SESSION) ? 'true' : 'false') . "</p>";

// Test 2: Try manual session_start
echo "<h2>Test 2: Manual session_start()</h2>";
$manual_start_result = session_start();
echo "<p>session_start() result: " . ($manual_start_result ? 'true' : 'false') . "</p>";
echo "<p>session_status() after manual start: " . session_status() . "</p>";
echo "<p>session_id() after manual start: '" . session_id() . "'</p>";

// Test 3: Include config and check again
echo "<h2>Test 3: After including config.php</h2>";
include_once 'config.php';
echo "<p>session_status(): " . session_status() . "'</p>";
echo "<p>session_id(): '" . session_id() . "'</p>";
echo "<p>\$_SESSION contents after config:</p>";
echo "<pre>" . print_r($_SESSION, true) . "</pre>";

// Test 4: Include Session class and test
echo "<h2>Test 4: Session class test</h2>";
include_once 'lib/classes/Session.php';

$session = Session::getInstance();
echo "<p>Session->getSessionState(): " . ($session->getSessionState() ? 'true' : 'false') . "</p>";
echo "<p>session_id() after Session::getInstance(): '" . session_id() . "'</p>";
echo "<p>Session exists(): " . ($session->exists() ? 'true' : 'false') . "</p>";

// Test 5: Check session configuration and permissions
echo "<h2>Test 5: Session Configuration & Permissions</h2>";
$save_path = ini_get('session.save_path');
echo "<p>session.save_path: " . $save_path . "</p>";
echo "<p>session.cookie_httponly: " . ini_get('session.cookie_httponly') . "</p>";
echo "<p>session.cookie_secure: " . ini_get('session.cookie_secure') . "</p>";
echo "<p>session.use_cookies: " . ini_get('session.use_cookies') . "</p>";
echo "<p>session.use_only_cookies: " . ini_get('session.use_only_cookies') . "</p>";

// Check if save path exists and is writable
if ($save_path) {
    if (is_dir($save_path)) {
        echo "<p style='color: green;'>✅ Save path directory exists</p>";
        if (is_writable($save_path)) {
            echo "<p style='color: green;'>✅ Save path is writable</p>";
        } else {
            echo "<p style='color: red;'>❌ Save path is NOT writable</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ Save path directory does NOT exist</p>";
    }
} else {
    echo "<p style='color: orange;'>⚠️ No save path configured</p>";
}

// Test 6: Check for errors
echo "<h2>Test 6: Error Check</h2>";
$last_error = error_get_last();
if ($last_error) {
    echo "<p style='color: red;'>Last error: " . $last_error['message'] . "</p>";
} else {
    echo "<p style='color: green;'>No errors detected</p>";
}

// Test breakpoint - set a breakpoint on the next line
$test_variable = "Set a breakpoint here!";
echo "<p>Test variable: " . $test_variable . "</p>";
?>

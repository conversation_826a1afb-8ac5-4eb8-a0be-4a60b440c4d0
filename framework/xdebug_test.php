<?php
// Xdebug 2.9.8 Test File
echo "<h1>Xdebug 2.9.8 Test</h1>";

// Check if Xdebug is loaded
if (extension_loaded('xdebug')) {
    echo "<p style='color: green;'>✅ Xdebug is loaded!</p>";
    echo "<p>Xdebug version: " . phpversion('xdebug') . "</p>";
    
    // Display Xdebug settings
    echo "<h2>Xdebug Configuration:</h2>";
    echo "<ul>";
    echo "<li>remote_enable: " . (ini_get('xdebug.remote_enable') ? 'On' : 'Off') . "</li>";
    echo "<li>remote_autostart: " . (ini_get('xdebug.remote_autostart') ? 'On' : 'Off') . "</li>";
    echo "<li>remote_host: " . ini_get('xdebug.remote_host') . "</li>";
    echo "<li>remote_port: " . ini_get('xdebug.remote_port') . "</li>";
    echo "<li>idekey: " . ini_get('xdebug.idekey') . "</li>";
    echo "</ul>";
} else {
    echo "<p style='color: red;'>❌ Xdebug is NOT loaded!</p>";
}

// Test breakpoint - set a breakpoint on the next line
$test_variable = "Set a breakpoint here!";
echo "<p>Test variable: " . $test_variable . "</p>";

// Display phpinfo for Xdebug section
echo "<h2>PHP Info (Xdebug section):</h2>";
ob_start();
phpinfo(INFO_MODULES);
$phpinfo = ob_get_contents();
ob_end_clean();

// Extract only Xdebug section
preg_match_all('#<h2><a name="module_xdebug">xdebug</a></h2>.*?(?=<h2>|$)#s', $phpinfo, $matches);
if (!empty($matches[0])) {
    echo $matches[0][0];
} else {
    echo "<p>No Xdebug section found in phpinfo()</p>";
}
?>

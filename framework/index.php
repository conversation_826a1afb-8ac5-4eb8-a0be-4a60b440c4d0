<?php

use C8Utils\RedisClient;
use <PERSON>8<PERSON><PERSON>s\RequestHandler;

include_once __DIR__ . '/config.php';
include_once __DIR__ . '/lib/classes/Session.php';

/**
 * Session var for backwards compatibility
 *
 * @deprecated use Session::getInstance() instead
 */
$sess = new Session();

/**
 * The current client DB for backwards compatibility
 *
 * @deprecated use Session::getInstance()->getClientDB(); instead
 */
$clientDB = Session::getInstance()->getClientDB();

/**
 * The current client DSN for backwards compatibility
 *
 * @deprecated use Session::getInstance()->getClientDSN(); instead
 */
$clientDSN = Session::getInstance()->getClientDSN();

/**
 * The current Redis client for backwards compatibility
 *
 * @deprecated use RedisClient::getRedis(); instead
 */
$redis = RedisClient::getRedis();

// -- invoke the database handler and create a new connection to the system database
$dbh = new MSSQL(SYSTEMDSN);

$request = new RequestHandler();
$request->handleRequest();

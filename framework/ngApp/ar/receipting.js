MainApp.requires.push('autoCompleteModule');
MainApp.requires.push('datatables');
MainApp.requires.push('ngAnimate');
MainApp.controller('receipt', [
    '$scope',
    '$http',
    '$timeout',
    'htmler',
    '$filter',
    'DTOptionsBuilder',
    'DTColumnDefBuilder',
    function ($scope, $http, $timeout, htmler, $filter, DTOptionsBuilder, DTColumnDefBuilder) {
        // ["$scope", "$http", "$timeout", "htmler", "$filter", "DTOptionsBuilder", "DTColumnDefBuilder",'$location', '$anchorScroll',
        // function($scope, $http, $timeout, htmler, $filter, DTOptionsBuilder, DTColumnDefBuilder, $location, $anchorScroll) {
        $scope.clearBankReceiptChargesView = function () {
            $scope.debtor_id = '';
            $scope.lease_id = '';
            $scope.property_id = '';
            $scope.rec_list = [];
        };
        // $scope.$watch('bank_acct_id', function (newValue, oldValue, scope) {
        //     if($scope.method == "nab" && newValue)
        // {
        //         $scope.loadFileNoReceiptTrans();
        // }
        // });
        $scope.$watch(
            'rec_date',
            function (newValue, oldValue, scope) {
                $scope.unalloc_date_from = newValue;
                $scope.unalloc_date_to = newValue;
            },
            true,
        );
        $scope.loadFileNoReceiptTrans = function (lastDate, file_trans_id) {
            $scope.clearBankReceiptChargesView();
            $scope.file_trans_key_to_adj = null;
            $scope.file_trans_key = null;

            // console.log(file_trans_id)

            if ($scope.bank_acct_id) {
                const params = {
                    bank_code: $scope.bank_acct_id,
                };
                htmler.request('AR/getFileNoReceiptTrans', params).then((data) => {
                    const list = [];
                    let auto_load_key = null;
                    for (let i = 0; i < data.length; i++) {
                        const row = data[i];
                        row.amount = parseFloat(row.amount);
                        row.amountTxt = $filter('htmlerFormatAmt')(parseFloat(row.amount));
                        list.push(row);

                        // CHECK IF NEED TO AUTO LOAD A FILE TRANSACTION
                        if (parseInt(row.file_trans_id) == parseInt(file_trans_id)) {
                            auto_load_key = i;
                        }
                    }

                    $scope.file_trans_list = list;
                    $scope.file_trans_list_grp = $scope.groupFileNoReceipTrans(list);
                    if (lastDate) {
                        let indx = 0;
                        for (dt in $scope.file_trans_list_grp) {
                            if (lastDate == dt) {
                                $scope.activedion = indx;
                                break;
                            }
                            indx++;
                        }
                    }
                    if ($scope.allOptions.length == 0) {
                        htmler.request('dropDowns/allOptions').then(function (data) {
                            // $scope.allOptions = data;
                            $scope.allOptions = [];
                            for (let i = 0; i < data.length; i++) {
                                const row = data[i];
                                let category = 'Current';
                                if (row.category == 'L') {
                                    category = 'Vacated';
                                }
                                row.leaseCategory = category;
                                $scope.allOptions.push(row);
                            }
                        });
                    }
                    // AUTOLOAD THE FILE LINE TRANS
                    if (auto_load_key != null) {
                        this.populateReceiptCharges(auto_load_key, true);
                    }
                });
            }
            $scope.clearBankReceiptChargesView();
        };
        $scope.groupFileNoReceipTrans = function (list) {
            const grp = {};
            let indx = 1;
            for (let i = 0; i < list.length; i++) {
                const row = list[i];
                row.list_key = i;
                row.srchShow = true;
                // row.edit = false;
                row.onEdit = false;
                row.ch = false;
                row.processed = false;
                // row.runMatched = false;

                row.suggestedDrop = [];
                row.leaseDrop = [];
                row.propertyDrop = [];
                row.debtorDrop = [];

                row.debtorID = '';
                row.leaseID = '';
                row.propertyID = '';
                row.suggestedID = '';

                row.lastData = {};
                if (!grp.hasOwnProperty(row.trans_date)) {
                    grp[row.trans_date] = {
                        datas: [row],
                        curPage: 0,
                        perPage: 10,
                        chAll: false,
                        showDeleted: false,
                        indx: indx,
                        filters: { keyword: '' },
                    };
                    angular.element(`#check-all-${indx}`).prop('indeterminate', false);
                    indx++;
                } else grp[row.trans_date].datas.push(row);
            }
            return $scope.orderGroupFileNoReceipTrans(grp);
            // return grp;
        };
        $scope.orderGroupFileNoReceipTrans = function (grp) {
            angular.forEach(grp, function (dt, key) {
                const rows = dt.datas;
                rows.sort(function (rwA, rwB) {
                    // let valDebtorA = rwA.debtor_code && !rwA.deleted ? 0 : 1;
                    // let valDebtorB = rwB.debtor_code && !rwA.deleted ? 0 : 1;
                    // if(valDebtorA == valDebtorB){
                    // 	return parseFloat(rwA.amount) - parseFloat(rwB.amount);
                    // }
                    // return valDebtorB - valDebtorA;
                    return parseFloat(rwA.amount) - parseFloat(rwB.amount);
                });
                dt.datas = rows;
            });
            return grp;
        };
        $scope.getDeletedBankReceipt = function (transDate) {
            const listGrp = $scope.file_trans_list_grp[transDate].datas;
            const showDeleted = $scope.file_trans_list_grp[transDate].showDeleted;
            if (showDeleted) {
                const params = {
                    bank_code: $scope.bank_acct_id,
                    trans_date: transDate,
                    get_deleted: true,
                };
                // console.log(params);
                htmler.request('AR/getFileNoReceiptTrans', params).then(function (data) {
                    angular.forEach(data, function (row) {
                        row.amount = parseFloat(row.amount);
                        row.amountTxt = $filter('htmlerFormatAmt')(parseFloat(row.amount));
                        $scope.file_trans_list.push(row);
                        row.list_key = $scope.file_trans_list.length - 1;
                        row.srchShow = true;
                        row.edit = false;
                        row.ch = false;
                        row.processed = false;
                        row.runMatched = false;
                        row.showDeleted = false;
                        row.leaseDrop = [];
                        row.propertyDrop = [];
                        row.debtorDrop = [];
                        row.suggestedDrop = [];
                        row.debtorID = '';
                        row.leaseID = '';
                        row.propertyID = '';
                        row.suggestedID = '';
                        row.lastData = {};
                        $scope.file_trans_list_grp[transDate].datas.push(row);
                    });
                    $scope.file_trans_list_grp[transDate].datas.sort(function (rwA, rwB) {
                        const valDebtorA = rwA.debtor_code && !rwA.deleted ? 0 : 1;
                        const valDebtorB = rwB.debtor_code && !rwA.deleted ? 0 : 1;
                        if (valDebtorA == valDebtorB) {
                            return parseFloat(rwA.amount) - parseFloat(rwB.amount);
                        }
                        return valDebtorB - valDebtorA;
                    });
                    $scope.populateBankReceiptDropDowns(transDate);
                });
            } else {
                for (var i = 0; i < $scope.file_trans_list_grp[transDate].datas.length; i++) {
                    const row = $scope.file_trans_list_grp[transDate].datas[i];
                    if (row.deleted) {
                        $scope.file_trans_list_grp[transDate].datas.splice(i, 1);
                    }
                }
                for (var i = 0; i < $scope.file_trans_list.length; i++) {
                    const row = $scope.file_trans_list[i];
                    if (row.trans_date == transDate && row.deleted) {
                        $scope.file_trans_list.splice(i, 1);
                    }
                }
            }
        };
        $scope.groupFileNoReceipTransCheckAll = function (transDate) {
            angular.forEach($scope.file_trans_list_grp[transDate].datas, function (row, key) {
                row.ch = $scope.file_trans_list_grp[transDate].chAll;
            });
        };
        $scope.groupFileNoReceipTransCheckWatch = function (transDate) {
            const listGrp = $scope.file_trans_list_grp[transDate];

            const elem = angular.element(`#check-all-${listGrp.indx}`);
            const num = listGrp.datas.length;
            let checked = 0;
            let none = 0;
            angular.forEach(listGrp.datas, function (row, key) {
                if (row.ch) checked++;
                else none++;
            });
            if (checked == num) {
                listGrp.chAll = true;
                elem.prop('indeterminate', false);
            } else if (none == num) {
                listGrp.chAll = false;
                elem.prop('indeterminate', false);
            } else {
                listGrp.chAll = true;
                elem.prop('indeterminate', true);
            }
        };
        $scope.methodChange = function (adjusted, file_id) {
            $scope.hideByCRN = false;
            $scope.downloadSundry = false;
            $scope.downloadReceipt = false;
            const method = $scope.method;
            $scope.method_id = '';
            $scope.methodShow = false;
            $scope.invoiceShow = false;
            $scope.crnShow = false;
            $scope.txnShow = false;
            $scope.txnButton = false;
            $scope.refineSrchList = [];

            // if(!adjusted)
            // 	$scope.bank_acct_id = "";
            $scope.debtor_id = '';
            $scope.lease_id = '';
            $scope.property_id = '';
            $scope.pay_add = '';
            $scope.pay_name = '';
            $scope.pay_state = '';
            $scope.pay_suburb = '';
            $scope.rec_no = '';
            $scope.crn_no = '';
            $scope.rec_list = [];
            $scope.allOptions = [];
            //	$scope.bankAccountsShow = false;
            $scope.leaseDebtorsSrchShow = false;
            $scope.leasesSrchShow = false;
            $scope.propertiesSrchShow = false;
            $scope.file_trans_list = [];
            $scope.file_trans_key = null;
            $scope.cheque_number = '';
            $scope.prn_number = '';
            $('#fileDownload').empty();
            if (!adjusted) {
                $scope.txnDlShow = false;
            }
            $scope.selected = {};

            if ($scope.dropsCodes && $scope.dropsCodes.length > 0) {
                for (let i = 0; i < $scope.dropsCodes.length; i++) {
                    const drop = $scope.dropsCodes[i];
                    if ($scope[`method_${drop}_id`]) {
                        $scope[`method_${drop}_id`] = '';
                    }
                }
            }

            switch (method) {
                case 'invoice':
                    $scope.invoiceShow = true;
                    $scope.invoice_no = '';
                    break;
                case 'crn':
                    $scope.crnShow = true;
                    $scope.crn_no = '';
                    break;
                case 'ofx':
                    $scope.txnShow = true;
                    $scope.file_trans_key = null;
                    $scope.txn_file_bank = '';
                    var today = new moment().subtract(1, 'days');
                    while ([0, 6].indexOf(today.day()) !== -1) {
                        today = today.subtract(1, 'days');
                    }
                    $scope.txn_file_date = today.format('DD/MM/YYYY');
                    setTimeout(function () {
                        $scope.loadFileNoReceiptTrans();
                    }, 500);
                    break;
                case 'qif':
                    $scope.txnShow = true;
                    $scope.file_trans_key = null;
                    $scope.txn_file_bank = '';
                    var today = new moment().subtract(1, 'days');
                    while ([0, 6].indexOf(today.day()) !== -1) {
                        today = today.subtract(1, 'days');
                    }
                    $scope.txn_file_date = today.format('DD/MM/YYYY');
                    setTimeout(function () {
                        $scope.loadFileNoReceiptTrans();
                    }, 500);
                    break;
                case 'cba':
                    $scope.txnShow = true;
                    $scope.file_trans_key = null;
                    $scope.txn_file_bank = '';
                    var today = new moment().subtract(1, 'days');
                    while ([0, 6].indexOf(today.day()) !== -1) {
                        today = today.subtract(1, 'days');
                    }
                    $scope.txn_file_date = today.format('DD/MM/YYYY');
                    setTimeout(function () {
                        $scope.loadFileNoReceiptTrans();
                    }, 500);
                    break;
                case 'btrs':
                case 'cbacsv':
                    $scope.txnShow = true;
                    $scope.file_trans_key = null;
                    $scope.txn_file_bank = '';
                    var today = new moment().subtract(1, 'days');
                    while ([0, 6].indexOf(today.day()) !== -1) {
                        today = today.subtract(1, 'days');
                    }
                    $scope.txn_file_date = today.format('DD/MM/YYYY');
                    setTimeout(function () {
                        $scope.loadFileNoReceiptTrans();
                    }, 500);
                    break;
                case 'nab':
                    $scope.txnShow = true;
                    //	$scope.bankAccountsShow = true;
                    $scope.file_trans_key = null;
                    $scope.txn_file_bank = '';
                    var today = new moment().subtract(1, 'days');
                    while ([0, 6].indexOf(today.day()) !== -1) {
                        today = today.subtract(1, 'days');
                    }
                    $scope.txn_file_date = today.format('DD/MM/YYYY');
                    // if($scope.bankAccounts.length <= 0){
                    // 	htmler.getDropDownsList('bankAccounts').then(function(data){
                    // 		var list = htmler.selectDflt('Please select...',data);
                    // 		$scope.bankAccounts = list;
                    // 		if(data.length == 1){
                    // 			$scope.bank_acct_id = data[0].value;
                    // 			$scope.loadSearchDetails('bankSrch');
                    // 		}
                    // 	});
                    // }
                    // else{
                    // 	if($scope.bankAccounts.length == 2){
                    // 		$scope.bank_acct_id = $scope.bankAccounts[1].value;
                    // 		$scope.loadSearchDetails('bankSrch');
                    // 	}
                    // }

                    setTimeout(function () {
                        $scope.loadFileNoReceiptTrans();
                    }, 500);
                    break;
                case 'wbc':
                    $scope.txnShow = true;
                    $scope.file_trans_key = null;
                    $scope.txn_file_bank = '';
                    var today = new moment().subtract(1, 'days');
                    while ([0, 6].indexOf(today.day()) !== -1) {
                        today = today.subtract(1, 'days');
                    }
                    $scope.txn_file_date = today.format('DD/MM/YYYY');
                    setTimeout(function () {
                        $scope.loadFileNoReceiptTrans();
                    }, 500);
                    break;
                case 'anz':
                    $scope.txnShow = true;
                    $scope.file_trans_key = null;
                    $scope.txn_file_bank = '';
                    var today = new moment().subtract(1, 'days');
                    while ([0, 6].indexOf(today.day()) !== -1) {
                        today = today.subtract(1, 'days');
                    }
                    $scope.txn_file_date = today.format('DD/MM/YYYY');
                    setTimeout(function () {
                        $scope.loadFileNoReceiptTrans();
                    }, 500);
                    break;
                case 'bwa':
                    $scope.txnShow = true;
                    $scope.file_trans_key = null;
                    $scope.txn_file_bank = '';
                    var today = new moment().subtract(1, 'days');
                    while ([0, 6].indexOf(today.day()) !== -1) {
                        today = today.subtract(1, 'days');
                    }
                    $scope.txn_file_date = today.format('DD/MM/YYYY');
                    setTimeout(function () {
                        $scope.loadFileNoReceiptTrans();
                    }, 500);
                    break;
                case 'txn':
                    $scope.txnShow = true;
                    $scope.txnButton = true;
                    $scope.bankAccountsShow = true;
                    $scope.file_trans_key = null;
                    $scope.txn_file_bank = '';
                    var today = new moment().subtract(1, 'days');
                    while ([0, 6].indexOf(today.day()) !== -1) {
                        today = today.subtract(1, 'days');
                    }
                    $scope.txn_file_date = today.format('DD/MM/YYYY');
                    // if($scope.bankAccounts.length <= 0){
                    // 	htmler.getDropDownsList('bankAccounts').then(function(data){
                    // 		var list = htmler.selectDflt('Please select...',data);
                    // 		$scope.bankAccounts = list;
                    // 		if(data.length == 1){
                    // 			$scope.bank_acct_id = data[0].value;
                    // 			$scope.loadSearchDetails('bankSrch');
                    // 		}
                    // 	});
                    // }
                    // else{
                    // 	if($scope.bankAccounts.length == 2){
                    // 		$scope.bank_acct_id = $scope.bankAccounts[1].value;
                    // 		$scope.loadSearchDetails('bankSrch');
                    // 	}
                    // }
                    $scope.loadFileNoReceiptTrans(false, file_id);
                    break;
                default:
                    var label = '';
                    // angular.forEach($scope.dropDowns,function(row,key){
                    // 	if(key == method){
                    // 		label = row;
                    // 		return false;
                    // 	}
                    // });
                    // if($scope[method+'List'].length <= 0){
                    // 	if(method == "leases"){
                    // 		htmler.getDropDownsList(method).then(function(data){
                    // 			// var list = htmler.selectDflt('Select a '+label,data);
                    // 			var list = [];
                    // 			list.push({label:'Select a '+label,value:'',category:''});
                    // 			angular.forEach(data,function(row,ctr){
                    // 				let category = "Current";
                    // 				if(row.category == "L"){
                    // 					category = "Vacated"
                    // 				}
                    // 				list.push({label:row.label,value:row.value,category:category});
                    // 			});

                    // 			$scope.methodList = list;
                    // 			$scope[method+'List'] = list;
                    // 		});
                    // 	}
                    // 	else{
                    // 		htmler.getDropDownsList(method).then(function(data){
                    // 			var list = htmler.selectDflt('Select a '+label,data);
                    // 			$scope.methodList = list;
                    // 			$scope[method+'List'] = list;
                    // 		});
                    // 	}
                    // }
                    // else{
                    // 	$scope.methodList = $scope[method+'List'];
                    // }
                    if (method == 'subLedgerProperties') {
                        if (angular.element('#useLeaseID').val() !== '') {
                            $scope.method_subLedgerProperties_id = angular.element('#useLeaseID').val();
                            $scope.method_id = angular.element('#useLeaseID').val();
                            //console.log($('#method_subLedgerProperties_id'));
                            $('#method_subLedgerProperties_id').trigger('chosen:updated');
                            $('#btn-method-subLedgerProperties').trigger('click');
                        }
                    }

                    $scope.methodShow = true;
                    break;
            }
        };
        $scope.onReady = function (briefReload) {
            $scope.dropDowns = {
                leaseDebtors: 'Debtor',
                properties: 'Property',
                leases: 'Lease',
                propertyManagers: $scope.propertyManagerLabel,
                invoice: 'Invoice Number',
                drawers: 'Drawer Name',
                efts: 'Direct Deposit Name',
                crn: 'CRN',
                txn: 'TXN File',
            };
            const dropsInit = ['leaseDebtors', 'properties', 'leases', 'propertyManagers', 'drawers', 'efts'];
            for (let i = 0; i < dropsInit.length; i++) {
                $scope[`method_${dropsInit[i]}_id`] = '';
            }

            // var methodFilter =  [
            // 	{label: 'Please select ...',value: '',},
            //       	{label: 'Debtor',value: 'leaseDebtors',},
            //        {label: 'Property',value: 'properties',},
            //        {label: 'Lease',value: 'leases',},
            //        {label: 'Property Manager',value: 'propertyManagers',},
            //        {label: 'Ledger',value: 'ledgerProperties',},
            //        {label: 'Sub-Ledger',value: 'subLedgerProperties',},
            //        {label: 'Invoice Number',value: 'invoice',},
            //        {label: 'Drawer Name',value: 'drawers',},
            //        {label: 'Direct Deposit Name',value: 'efts',},
            //        {label: 'CRN',value: 'crn',},
            //        {label: 'TXN File',value: 'txn',},
            //        {label: 'NAB Bpay File',value: 'nab',},
            //        {label: 'CBA Bpay File',value: 'cba',}
            //       ];
            let methodFilter = {};
            if (angular.element('#methodFilter').val() != '') {
                methodFilter = JSON.parse(angular.element('#methodFilter').val());
            }

            $scope.withBonds = false;
            $scope.downloadSundry = false;
            $scope.downloadReceipt = false;
            $scope.methodFilterList = methodFilter;
            $scope.methodFilter = methodFilter;
            $scope.dir_depo_name = '';
            $scope.isCheckVacated = false;
            $scope.isCheckFuture = false;
            $scope.rec_amounts = [];
            $scope.rec_list = [];
            $scope.rec_list_notes = {};
            $scope.rec_list_notes_show = false;
            $scope.chequeBankList = [];
            $scope.taxRatesList = [];
            $scope.showUnallocTbl = false;
            $scope.unallocList = [];
            $scope.total_ln_unpaid = (0).toFixed(2);
            $scope.applied_total = (0).toFixed(2);
            $scope.unpaid_bal = (0).toFixed(2);
            $scope.apply_bal = (0).toFixed(2);
            $scope.selected = '';
            // $scope.method = "";
            $scope.method_id = '';
            $scope.unalloc_acct = '';
            $scope.lastReconDate = '';
            $scope.lastReconDateMsg = '';
            $scope.clearance_days = '0';
            $scope.clearance_date = '';
            $scope.bankAccounts = [];
            $scope.methodShow = false;
            $scope.invoiceShow = false;
            $scope.crnShow = false;
            $scope.creditForceEnable = false;
            $scope.refineSrchList = [];
            $scope.methodSelectShow = false;
            $scope.companiesSrch = [];
            $scope.subLedgerTypeSrch = [];
            if (!briefReload) {
                angular.forEach($scope.dropDowns, function (label, val) {
                    $scope[`${val}List`] = [];
                });
            }
            if (!briefReload) {
                //$scope.method = "properties";
                const urlMethod = angular.element('#useMethod').val();
                $scope.method = urlMethod != '' ? urlMethod : 'properties';
                $scope.allOptions = [];
            }
            $scope.dropsCodes = JSON.parse(angular.element('#dropsCodes').val()) || [];
            $scope.clearDaysDefaults = JSON.parse(angular.element('#clearDaysDefault').val()) || [];
            $scope.propertyManagerLabel = angular.element('#propertyManagerLabel').val() || 'Property Manager';
            // $scope.method = "txn";
            $scope.pay_add = '';
            $scope.pay_name = '';
            $scope.pay_state = '';
            $scope.pay_suburb = '';
            $scope.rec_no = '';
            $scope.methodChange();
            $scope.cheque_number = '';
            $scope.prn_number = '';
            $scope.leaseLabel = 'Lease';
            $('#fileDownload').empty();
            $scope.payHistory = {
                propertyID: '',
                leaseID: '',
                debtorID: '',
                transactionDate: '',
                description: '',
                TotalAmount: 0,
                allocTotal: 0,
                list: [],
            };
            $scope.apDetails = {
                propertyID: '',
                leaseID: '',
                list: [],
            };
            $scope.payment_type_list = [
                { label: 'Please select...', value: '' },
                { label: 'Cheque', value: 'CHQ' },
                { label: 'Direct Deposit', value: 'DIR' },
                { label: 'Direct Debit', value: 'DDR' },
                { label: 'BPay', value: 'BPA' },
                { label: 'EFTPOS', value: 'EPO' },
                { label: 'Cash', value: 'CSH' },
            ];
            $scope.receiptingNote = {
                desc: '',
                property: '',
                lease: '',
                timestamp: '',
                user: '',
            };
            $scope.apDetailsHoldAll = false;
            angular.element('#apDetails-hold-all').prop('indeterminate', false);

            $scope.txn_file_bank = '';
            $scope.txn_file_date = '';
            let today = new moment().subtract(1, 'days');
            while ([0, 6].indexOf(today.day()) !== -1) {
                today = today.subtract(1, 'days');
            }
            $scope.txn_file_date = today.format('DD/MM/YYYY');

            $scope.txn_list = [];
            $scope.txn_temp_list = [];
            $scope.txn_temp_ch_ctr = 0;
            $scope.txn_temp_tbl = {
                datas: [],
                curPage: 0,
                perPage: 10,
                filters: { keyword: '' },
            };
            $scope.txn_file = '';
            $scope.temp_drop_list = [];
            $scope.txnTempLease = '';
            $scope.txnTempLeasesList = [];
            $scope.txnTempDebtorList = [];
            $scope.txnTempProperties = '';
            $scope.txnTempPropertiesList = [];
            $scope.txnTempChAll = false;
            $scope.txnTempBankDetail = '';
            $scope.txnTempTransDate = '';
            $scope.file_trans_list = [];
            $scope.file_trans_list_grp = {};
            $scope.activedion = 0;
            $scope.file_trans_key = null;
            $scope.file_trans_key_to_adj = null;

            $scope.txnMatching = {
                enabled: false,
                by: '',
                methods: [
                    { label: 'Please select...', value: '' },
                    { label: 'Suggested Match', value: 'suggested' },
                    { label: 'Debtor', value: 'debtor' },
                    { label: 'Property', value: 'property' },
                    { label: 'Lease', value: 'lease' },
                    { label: 'Invoice Number', value: 'invoice' },
                    { label: 'Drawer Name', value: 'drawers' },
                    { label: 'Direct Deposit Name', value: 'efts' },
                    { label: 'CRN', value: 'crn' },
                ],
                mainVal: '',
                mainDrop: [],
            };

            $scope.unalloc_new_leaseID = '';
            $scope.unalloc_new_propertyID = '';

            $scope.vacateds = [];
            $scope.vacatedsNextFile = false;

            $scope.tenant_act_tbl = {
                from: moment().add(-1, 'month').startOf('month').format('DD/MM/YYYY'),
                to: moment().format('DD/MM/YYYY'),
                datas: [],
                details: {},
            };

            //DEFINE COMPANIES
            htmler.getDropDownsList('companies', { all: 1 }).then(function (data) {
                const list = htmler.selectDflt('Please select...', data.data);
                $scope.companiesSrch = list;
            });

            //LEDGER TYPE
            htmler.getDropDownsList('paramTypes', { paramType: 'SLTYPE' }).then(function (data) {
                const list = htmler.selectDflt('Please select...', data);
                $scope.subLedgerTypeSrch = list;
            });

            setTimeout(function () {
                $scope.bank_acct_id = $('#bank_acct_id option:nth-child(2)').val();

                let on_load = {};
                // console.log(angular.element('#useMethod').val())
                if (angular.element('#useMethod').val() == 'file') {
                    on_load.method = 'txn';
                    on_load.file_id = angular.element('#useFileTransID').val() || false;
                } else on_load = false;
                $scope.loadSearchDetails('bankSrch', on_load);
                $('#showTahle').css('display', '');
            }, 1000);
        };
        $scope.onReady();
        $scope.loadSearch = function (methodType) {
            const method = $scope.method;
            $scope.method_id = $scope[`method_${methodType}_id`];
            //$scope.bankAccountsShow = false;
            $scope.refineSrchList = [];
            $scope.vacateds = [];
            $scope.file_trans_list = [];
            $scope.file_trans_key = null;
            $scope.downloadSundry = false;
            $scope.downloadReceipt = false;
            switch (method) {
                case 'leaseDebtors':
                    // if($scope.bankAccounts.length <= 0)
                    // 	htmler.getDropDownsList('bankAccounts').then(function(data){ var list = htmler.selectDflt('Please select...',data); $scope.bankAccounts = list;});
                    // $scope.bankAccountsShow = true;

                    //	$scope.bank_acct_id = "";
                    $scope.leaseDebtorsSrch = htmler.selectDflt('Please select...', []);
                    $scope.leaseDebtorsSrchShow = false;

                    $scope.lease_id = '';
                    $scope.leasesSrch = htmler.selectDflt('Please select...', []);
                    $scope.leasesSrchShow = true;

                    $scope.property_id = '';
                    $scope.propertiesSrch = htmler.selectDflt('Please select...', []);
                    $scope.propertiesSrchShow = true;

                    if ($scope.bank_acct_id) $scope.loadSearchDetails('leaseDebtors');

                    break;
                case 'properties':
                    //	$scope.bank_acct_id = "";
                    //	$scope.bankAccountsShow = false;
                    $scope.debtor_id = '';
                    $scope.leaseDebtorsSrch = htmler.selectDflt('Please select...', []);
                    $scope.leaseDebtorsSrchShow = true;

                    $scope.lease_id = '';
                    $scope.leasesSrch = htmler.selectDflt('Please select...', []);
                    $scope.leasesSrchShow = true;

                    $scope.propertiesSrch = htmler.selectDflt('Please select...', []);
                    $scope.propertiesSrchShow = false;

                    $scope.loadSearchDetails('properties');
                    break;
                case 'ledgerProperties':
                    $scope.debtor_id = '';
                    $scope.leaseDebtorsSrch = htmler.selectDflt('Please select...', []);
                    $scope.leaseDebtorsSrchShow = true;

                    $scope.lease_id = '';
                    $scope.leasesSrch = htmler.selectDflt('Please select...', []);
                    $scope.leasesSrchShow = true;

                    $scope.propertiesSrch = htmler.selectDflt('Please select...', []);
                    $scope.propertiesSrchShow = false;

                    $scope.loadSearchDetails('properties');
                    break;
                case 'leases':
                    //	$scope.bank_acct_id = "";
                    //	$scope.bankAccountsShow = true;

                    $scope.debtor_id = '';
                    $scope.leaseDebtorsSrch = htmler.selectDflt('Please select...', []);
                    $scope.leaseDebtorsSrchShow = true;

                    $scope.leasesSrch = htmler.selectDflt('Please select...', []);
                    $scope.leasesSrchShow = false;

                    $scope.property_id = '';
                    $scope.propertiesSrch = htmler.selectDflt('Please select...', []);
                    $scope.propertiesSrchShow = true;

                    $scope.loadSearchDetails('leases');
                    break;
                case 'subLedgerProperties':
                    $scope.debtor_id = '';
                    $scope.leaseDebtorsSrch = htmler.selectDflt('Please select...', []);
                    $scope.leaseDebtorsSrchShow = true;

                    $scope.leasesSrch = htmler.selectDflt('Please select...', []);
                    $scope.leasesSrchShow = false;

                    $scope.property_id = '';
                    $scope.propertiesSrch = htmler.selectDflt('Please select...', []);
                    $scope.propertiesSrchShow = true;
                    $scope.loadSearchDetails('subLedgers');
                    break;
                case 'propertyManagers':
                    //  	$scope.bank_acct_id = "";
                    $scope.property_id = '';
                    $scope.debtor_id = '';
                    $scope.leaseDebtorsSrchShow = false;
                    $scope.lease_id = '';
                    $scope.leasesSrchShow = false;
                    htmler.getDropDownsList('properties', { managerID: $scope.method_id }).then(function (data) {
                        const list = htmler.selectDflt('Please select...', data);
                        $scope.propertiesSrch = list;
                        if (data.length > 0) $scope.propertiesSrchShow = true;
                    });
                    break;
                case 'drawers':
                    htmler
                        .getDropDownsList('leaseDebtorsByDrawer', { drawerID: $scope.method_id })
                        .then(function (data) {
                            const list = htmler.selectDflt('Please select...', data);
                            $scope.leaseDebtorsSrch = list;

                            $scope.leaseDebtorsSrchShow = true;
                            $scope.property_id = '';
                            $scope.lease_id = '';
                            $scope.leasesSrch = htmler.selectDflt('Please select...', []);
                            $scope.propertiesSrch = htmler.selectDflt('Please select...', []);
                            $scope.leasesSrchShow = false;
                            $scope.propertiesSrchShow = false;

                            if (data.length > 0) {
                                if (data.length == 1) {
                                    $scope.debtor_id = data[0]['value'];
                                    $scope.leasesSrchShow = true;
                                    $scope.propertiesSrchShow = true;
                                    $scope.leaseDebtorsSrchShow = true;
                                    $scope.loadSearchDetails('drawers');
                                }
                            }
                        });
                    break;
                case 'efts':
                    //$scope.bank_acct_id = "";
                    //	$scope.bankAccountsShow = true;

                    $scope.debtor_id = '';
                    $scope.leaseDebtorsSrch = htmler.selectDflt('Please select...', []);
                    $scope.leaseDebtorsSrchShow = true;

                    $scope.leasesSrch = htmler.selectDflt('Please select...', []);
                    $scope.leasesSrchShow = false;

                    $scope.property_id = '';
                    $scope.propertiesSrch = htmler.selectDflt('Please select...', []);
                    $scope.propertiesSrchShow = true;

                    htmler.getDropDownsList('leaseDebtorsByEfts', { eftID: $scope.method_id }).then(function (data) {
                        const list = htmler.selectDflt('Please select...', data);
                        $scope.leaseDebtorsSrch = list;
                        if (data.length > 0) {
                            if (data.length == 1) $scope.debtor_id = data[0]['value'];
                            //	$scope.bankAccountsShow = true;
                            $scope.leaseDebtorsSrchShow = true;
                            $scope.leasesSrchShow = true;
                            $scope.propertiesSrchShow = true;
                            $scope.loadSearchDetails('efts');
                        }
                    });
                    break;
            }
        };
        $scope.loadCountryDefaults = function (mode) {
            // var formData = htmler.httpi();
            // formData.append('bank', $scope.bank_acct_id);
            // formData.append('country', '');
            // $http.post(cirrus8ApiUrl + "admin/country_defaults/load", formData)
            // .then(function (response) {
            // 	// console.log(response.data.default);
            // 	$scope.country_default = response.data.default;
            // });
            const params = {};
            params['country'] = '';

            /* mode: address or general */
            if (mode == 'address') {
                params['bank'] = $scope.bank_acct_id;
            }

            htmler.request('country_defaults/load', params, 'admin').then(function (data) {
                switch (mode) {
                    case 'address':
                        $scope.address_cdf = data.default;
                        break;
                    case 'general':
                        $scope.general_cdf = data.default;
                        break;
                    default:
                        $scope.general_cdf = data.default;
                        break;
                }
            });
        };

        $scope.loadSearchDetails = function (action, on_load) {
            document.getElementById('download_link_div').innerHTML = '';
            let params = {};
            const method = $scope.method;
            // if($scope.bankAccounts.length <= 0)
            // 	htmler.getDropDownsList('bankAccounts').then(function(data){ var list = htmler.selectDflt('Please select...',data); $scope.bankAccounts = list;});
            //
            // if($scope.debtor_id || $scope.lease_id){
            // 	$scope.bankAccountsShow = true;
            // }
            angular.element('#goTopBtn').css({ bottom: 80 });
            let noPropertySrch = false;
            let noDebtorSrch = false;

            if (action == 'bankSrch') {
                $('#method_properties_id option').addClass('hidden');
                $(`#method_properties_id option[bank='${$scope.bank_acct_id}']`).removeClass('hidden');
                $('#method_properties_id').trigger('chosen:updated');

                $('#method_ledgerProperties_id option').addClass('hidden');
                $(`#method_ledgerProperties_id option[bank='${$scope.bank_acct_id}']`).removeClass('hidden');
                $('#method_ledgerProperties_id').trigger('chosen:updated');

                $('#method_leases_id option').addClass('hidden');
                $(`#method_leases_id option[bank='${$scope.bank_acct_id}']`).removeClass('hidden');
                $('#method_leases_id').trigger('chosen:updated');

                $('#method_subLedgerProperties_id option').addClass('hidden');
                $(`#method_subLedgerProperties_id option[bank='${$scope.bank_acct_id}']`).removeClass('hidden');
                $('#method_subLedgerProperties_id').trigger('chosen:updated');
            }

            switch (action) {
                case 'bankSrch':
                    $scope.methodSelectShow = true;
                    $scope.methodFilter = [];

                    var insti = $(`#bank_acct_id option[value='${$scope.bank_acct_id}']`).attr('institution');
                    angular.forEach($scope.methodFilterList, function (row, ctr) {
                        if (
                            (row.value == 'bwa' && (insti != 'BWA' || !insti)) ||
                            (row.value == 'anz' && (insti != 'ANZ' || !insti)) ||
                            (row.value == 'wbc' && (insti != 'WBC' || !insti)) ||
                            (row.value == 'cbacsv' && (insti != 'CBA' || !insti)) ||
                            (row.value == 'btrs' && (insti != 'CBA' || !insti)) ||
                            (row.value == 'cba' && (insti != 'CBA' || !insti)) ||
                            (row.value == 'nab' && (insti != 'NAB' || !insti)) ||
                            (row.value == 'txn' && (insti != 'MBL' || !insti))
                        ) {
                        } else $scope.methodFilter.push({ label: row.label, value: row.value });
                    });
                    // $scope.method = "";
                    // $scope.method_properties_id = "";
                    // $scope.method_leases_id = "";
                    // $scope.method_leaseDebtors_id = "";
                    // $scope.method_propertyManagers_id = "";
                    // $scope.invoice_no = "";
                    // $scope.method_drawers_id = "";
                    // $scope.method_efts_id = "";
                    // $scope.crn_no = "";
                    $scope.txnShow = false;
                    $scope.txnButton = false;

                    let file_id = false;
                    if (on_load && on_load.method) {
                        $scope.method = 'txn';
                    }
                    if (on_load && on_load.file_id) {
                        file_id = on_load.file_id;
                    }
                    $scope.methodChange(false, file_id);

                    $scope.loadCountryDefaults('address'); //fetch country defaults for the state display based on the bank ID
                    $scope.loadCountryDefaults('general'); //fetch country defaults for the BSB based on the client country

                    break;
                case 'leaseDebtors':
                    $scope.debtor_id = $scope.method_id;
                    params['debtorID'] = $scope.method_id;
                    params['bankID'] = $scope.bank_acct_id;
                    break;
                case 'properties':
                    $scope.property_id = $scope.method_id;
                    params['propertyID'] = $scope.method_id;
                    if (method == 'ledgerProperties') {
                        params['ledger_only'] = true;
                    }
                    break;
                case 'invoiceSrch':
                    $scope.leasesSrchShow = true;
                    $scope.propertiesSrchShow = true;
                    $scope.leaseDebtorsSrchShow = true;
                    if ($scope.debtor_id) params['debtorID'] = $scope.debtor_id;
                    if ($scope.lease_id) params['leaseID'] = $scope.lease_id;
                    if ($scope.property_id) params['propertyID'] = $scope.property_id;
                    break;
                case 'crnSrch':
                    $scope.leasesSrchShow = true;
                    $scope.propertiesSrchShow = true;
                    $scope.leaseDebtorsSrchShow = true;
                    if ($scope.debtor_id) params['debtorID'] = $scope.debtor_id;
                    if ($scope.lease_id) params['leaseID'] = $scope.lease_id;
                    if ($scope.property_id) params['propertyID'] = $scope.property_id;
                    if ($scope.crn_no) params['crn'] = $scope.crn_no;
                    break;
                default:
                    if (method == 'leaseDebtors') {
                        $scope.debtor_id = $scope.method_id;
                    }
                    if (method == 'properties') $scope.property_id = $scope.method_id;
                    if (method == 'leases') $scope.lease_id = $scope.method_id;
                    if (method == 'subLedgerProperties') {
                        $scope.lease_id = $scope.method_id;
                        params['ledger_only'] = true;
                    }
                    if (method == 'propertyManagers') noPropertySrch = true;
                    if (method == 'propertyManagers' && action == 'propertySrch') {
                        $scope.leasesSrchShow = true;
                        $scope.leaseDebtorsSrchShow = true;
                        noPropertySrch = true;
                    }
                    if (method == 'drawers') noDebtorSrch = true;
                    if (method == 'drawers' && action == 'debtorSrch') {
                        $scope.leasesSrchShow = true;
                        $scope.propertiesSrchShow = true;
                        noDebtorSrch = true;
                    }

                    if ($scope.bank_acct_id) params['bankID'] = $scope.bank_acct_id;
                    if ($scope.debtor_id) params['debtorID'] = $scope.debtor_id;
                    if ($scope.lease_id) params['leaseID'] = $scope.lease_id;
                    if ($scope.property_id) params['propertyID'] = $scope.property_id;
                    if ($scope.crn_no) params['crn'] = $scope.crn_no;

                    if (action == 'bankSrch' && method == 'txn') {
                        params = {};
                        $scope.txnDlShow = false;
                        const bank = $scope.bank_acct_id;
                        const banks = $scope.bankAccounts;
                        if (banks.length > 0) {
                            for (let i = banks.length - 1; i >= 0; i--) {
                                if (bank == banks[i].value && banks[i].bank_type == 'MBL') {
                                    $scope.txnDlShow = true;

                                    break;
                                }
                            }
                        }
                        if ($scope.bank_acct_id) {
                            $scope.loadFileNoReceiptTrans();
                        }
                    }

                    break;
            }

            if ($scope.bank_acct_id) params['bankID'] = $scope.bank_acct_id;

            if (
                Object.keys(params).length > 0 &&
                (typeof params.debtorID !== 'undefined' ||
                    typeof params.leaseID !== 'undefined' ||
                    typeof params.propertyID !== 'undefined' ||
                    typeof params.crn !== 'undefined')
            ) {
                $('#fileDownload').empty();
                htmler.request('AR/getTenantDetails', params).then(function (data) {
                    $scope.leasesSrch = htmler.selectDflt('Please select...', []);
                    if (!noPropertySrch) {
                        $scope.propertiesSrch = htmler.selectDflt('Please select...', []);
                    }
                    if (!noDebtorSrch) {
                        $scope.leaseDebtorsSrch = htmler.selectDflt('Please select...', []);
                    }

                    $scope.refineSrchList = [];
                    if (data.length > 0) {
                        if (data[0].inactive == 1) $scope.hideByCRN = true;
                        else $scope.hideByCRN = false;

                        const indxProp = [];
                        const indxLea = [];
                        $scope.leaseLabel = 'Sub-Ledger';
                        angular.forEach(data, function (row, ctr) {
                            if (row.is_ledger == 0) $scope.leaseLabel = 'Lease';

                            let category = 'Current';
                            if (row.leaseStatus == 'L') {
                                category = 'Vacated';
                            }
                            $scope.leasesSrch.push({
                                label: `${row.leaseID} - ${row.leaseName}`,
                                value: row.leaseID,
                                category: category,
                                property: row.propertyID,
                            });
                            $scope.leasesSrchCopy = $scope.leasesSrch;
                            if (!noPropertySrch && indxProp.indexOf(row.propertyID) == -1) {
                                $scope.propertiesSrch.push({
                                    label: `${row.propertyID} - ${row.propertyName}`,
                                    value: row.propertyID,
                                });
                                indxProp.push(row.propertyID);
                            }
                            if (!noDebtorSrch && indxLea.indexOf(row.debtorID) == -1) {
                                $scope.leaseDebtorsSrch.push({
                                    label: `${row.debtorID} - ${row.debtorName}`,
                                    value: row.debtorID,
                                });
                                indxProp.push(row.debtorID);
                            }
                        });
                        // 	if(data.length > 0){
                        // 		if(!$scope.bank_acct_id)
                        // 			$scope.bank_acct_id = data[0]['bankID'];
                        // //		if($scope.bank_acct_id)
                        // 	//		$scope.bankAccountsShow = true;
                        // 	}
                        if (data.length == 1) {
                            if (!noPropertySrch) {
                                $scope.property_id = data[0]['propertyID'];
                            }
                            if (!noDebtorSrch) {
                                $scope.debtor_id = data[0]['debtorID'];
                            }
                            $scope.lease_id = data[0]['leaseID'];
                            // if($scope.debtor_id && $scope.property_id && $scope.lease_id){
                            // 	$scope.loadSelected(data[0]);
                            // }
                        }
                        // && $scope.lease_id
                        if ($scope.debtor_id) {
                            $scope.loadSelected(data[0], method, action);
                        }

                        $scope.chequeNo = data[0]['chkNo'];
                        $scope.PaymentRefNo = data[0]['PaymentReferenceNo'];
                        $scope.refineSrchList = data;
                    }
                });
            }
            // else{
            // 	console.log('Invalid search no parameters');
            // }
            angular.element('#invoice_no').blur();
            angular.element('#crn_no').blur();
        };
        $scope.initRecList = function (arr, allocateAmt) {
            $scope.vacateds = [];
            $scope.vacatedsNextFile = false;
            let file_trans = {};
            let file_trans_total = 0;
            if ($scope.file_trans_list[$scope.file_trans_key]) {
                file_trans = $scope.file_trans_list[$scope.file_trans_key];
                file_trans_total = parseFloat(file_trans.amount);
            } else if (allocateAmt) {
                file_trans_total = parseFloat(allocateAmt);
            }
            let total_unpaid = 0;
            let total_inv = 0;
            angular.forEach(arr, function (row, key) {
                // if (!row.hasOwnProperty('line_amount')){
                row.unpaidAmount = parseFloat(row.unpaidAmount);
                total_unpaid += row.unpaidAmount;
                if (row.transactionType == 'INV') total_inv += row.unpaidAmount;
                row.amount = parseFloat(row.amount);
                if (file_trans && file_trans_total > 0 && row.invoiceNumber && row.invoiceNumber != '0') {
                    let match = false;
                    // if ther are amounts THAT IS EQUAL to file transaction amount
                    if (
                        row.debtorID == file_trans.debtor_code &&
                        row.leaseID == file_trans.lease_code &&
                        row.propertyID == file_trans.property_code &&
                        row.unpaidAmount == file_trans.amount
                    ) {
                        match = true;
                    }
                    if (match) {
                        row.line_amount = parseFloat(file_trans_total).toFixed(2);
                        row.file_trans_id = file_trans.file_trans_id;
                        file_trans_total = 0;
                    } else row.line_amount = 0.0;
                } else {
                    if (
                        file_trans_total > 0 &&
                        row.invoiceNumber &&
                        row.invoiceNumber != '0' &&
                        row.unpaidAmount == file_trans_total
                    ) {
                        row.line_amount = parseFloat(file_trans_total).toFixed(2);
                        row.file_trans_id = file_trans.file_trans_id;
                        file_trans_total = 0;
                    } else row.line_amount = 0.0;
                }
                // }
            });
            // group by invoice number and check for its total THAT IS EQUAL TO matched amount
            file_trans_total = parseFloat(file_trans_total).toFixed(2);
            total_unpaid = parseFloat(total_unpaid).toFixed(2);
            total_inv = parseFloat(total_inv).toFixed(2);
            // if(file_trans_total > 0 && total_unpaid > 0){
            if (file_trans_total > 0 && total_inv > 0) {
                const invGrp = {};
                angular.forEach(arr, function (row, key) {
                    if (row.invoiceNumber && row.invoiceNumber != '0') {
                        if (!invGrp[row.invoiceNumber]) {
                            invGrp[row.invoiceNumber] = parseFloat(row.unpaidAmount);
                        } else {
                            invGrp[row.invoiceNumber] += parseFloat(row.unpaidAmount);
                        }
                    }
                });
                let matchedInv = null;
                for (const inv in invGrp) {
                    if (invGrp.hasOwnProperty(inv) && invGrp[inv].toFixed(2) == file_trans_total) {
                        matchedInv = inv;
                        break;
                    }
                }
                angular.forEach(arr, function (row, key) {
                    if (file_trans_total > 0) {
                        if (row.invoiceNumber == matchedInv) {
                            if (file_trans.file_trans_id) row.file_trans_id = file_trans.file_trans_id;
                            row.line_amount = parseFloat(row.unpaidAmount).toFixed(2);
                            file_trans_total = (parseFloat(file_trans_total) - row.unpaidAmount).toFixed(2);
                        }
                    }
                });
                // console.log(matchedInv);
            }
            // allocate all amounts and put to unallocated the remaining
            file_trans_total = parseFloat(file_trans_total).toFixed(2);
            // if(file_trans_total > 0 && total_unpaid > 0){
            if (file_trans_total > 0 && total_inv > 0) {
                if (total_unpaid > file_trans_total) {
                    let noMore = false;
                    let ctr = 1;
                    while (!noMore) {
                        const closest = $scope.closestAmount(arr, file_trans_total);
                        if (closest.unpaidAmount && !isNaN(closest.unpaidAmount)) {
                            if (closest.unpaidAmount > file_trans_total) {
                                closest.line_amount = parseFloat(file_trans_total).toFixed(2);
                                if (file_trans.file_trans_id) closest.file_trans_id = file_trans.file_trans_id;
                                total_unpaid -= closest.unpaidAmount;
                                file_trans_total -= parseFloat(closest.unpaidAmount).toFixed(2);
                                noMore = true;
                            } else {
                                closest.line_amount = parseFloat(closest.unpaidAmount).toFixed(2);
                                if (file_trans.file_trans_id) closest.file_trans_id = file_trans.file_trans_id;
                                file_trans_total -= parseFloat(closest.unpaidAmount).toFixed(2);
                            }
                        } else {
                            noMore = true;
                        }
                        // for stoppung never ending loop in case
                        ctr++;
                        if (ctr > 3000) noMore = true;
                    }
                    // if there are still file trans total to allocate it to the next oldest
                    file_trans_total = parseFloat(file_trans_total).toFixed(2);
                    for (var i = 0; i < arr.length; i++) {
                        if (
                            arr[i].invoiceNumber &&
                            arr[i].invoiceNumber != '0' &&
                            arr[i].line_amount == 0 &&
                            file_trans_total > 0
                        ) {
                            if (arr[i].unpaidAmount && !isNaN(arr[i].unpaidAmount)) {
                                if (arr[i].unpaidAmount >= file_trans_total) {
                                    arr[i].line_amount = parseFloat(file_trans_total).toFixed(2);
                                    if (file_trans.file_trans_id) arr[i].file_trans_id = file_trans.file_trans_id;
                                    file_trans_total -= parseFloat(file_trans_total).toFixed(2);
                                } else {
                                    arr[i].line_amount = parseFloat(arr[i].unpaidAmount).toFixed(2);
                                    if (file_trans.file_trans_id) arr[i].file_trans_id = file_trans.file_trans_id;
                                    file_trans_total -= parseFloat(arr[i].unpaidAmount).toFixed(2);
                                }
                            }
                        }
                    }
                } else {
                    for (var i = 0; i < arr.length; i++) {
                        file_trans_total = parseFloat(file_trans_total).toFixed(2);
                        if (
                            arr[i].invoiceNumber &&
                            arr[i].invoiceNumber != '0' &&
                            arr[i].line_amount == 0 &&
                            file_trans_total > 0
                        ) {
                            if (arr[i].unpaidAmount && !isNaN(arr[i].unpaidAmount)) {
                                if (arr[i].unpaidAmount >= file_trans_total) {
                                    arr[i].line_amount = parseFloat(file_trans_total).toFixed(2);
                                    if (file_trans.file_trans_id) arr[i].file_trans_id = file_trans.file_trans_id;
                                    file_trans_total -= parseFloat(file_trans_total).toFixed(2);
                                } else {
                                    arr[i].line_amount = parseFloat(arr[i].unpaidAmount).toFixed(2);
                                    if (file_trans.file_trans_id) arr[i].file_trans_id = file_trans.file_trans_id;
                                    file_trans_total -= parseFloat(arr[i].unpaidAmount).toFixed(2);
                                }
                            }
                        }
                    }
                }
            }
            file_trans_total = parseFloat(file_trans_total).toFixed(2);
            if (file_trans_total > 0) {
                let usePropID = '';
                let useLeaseID = '';
                if (file_trans.property_code) {
                    usePropID = file_trans.property_code;
                    useLeaseID = file_trans.lease_code;
                } else {
                    usePropID = $scope.selected.propertyID;
                    useLeaseID = $scope.selected.leaseID;
                }
                // file_trans.trans_date
                const row = {
                    propertyID: usePropID,
                    leaseID: useLeaseID,
                    account: $scope.unalloc_acct,
                    fromDate: file_trans.trans_date,
                    toDate: file_trans.trans_date,
                    description: 'Amount in Credit',
                    taxType: 'GSTFREE',
                    sundry: false,
                    amount: parseFloat(file_trans_total).toFixed(2),
                };
                if (useLeaseID) {
                    $scope.unallocList.push(row);
                    $scope.showUnallocTbl = true;
                }
            }
            return arr;
        };
        $scope.allocateAuto = function () {
            if ($scope.rec_amount) {
                const amount = parseFloat($scope.rec_amount).toFixed(2);
                $scope.rec_list = $scope.initRecList(JSON.parse(JSON.stringify($scope.rec_list)), amount);
                $scope.calculateTotals();
            }
        };
        $scope.closestAmount = function (records, amount) {
            const fromRange = amount - (1 / 100) * amount;
            const toRange = amount + (1 / 100) * amount;
            let closest = {};
            for (let i = 0; i < records.length; i++) {
                if (records[i].invoiceNumber && records[i].invoiceNumber != '0') {
                    const unpaid = records[i].unpaidAmount;
                    if (unpaid >= fromRange && unpaid <= toRange) {
                        closest = records[i];
                        break;
                    }
                }
            }
            return closest;
        };
        $scope.closestAmountOLD = function (records, amount) {
            const values = [];
            angular.forEach(records, function (row, key) {
                values.push(row.unpaidAmount);
            });
            smallestDiff = Math.abs(amount - values[0]);
            closest = 0;
            for (let i = 1; i < values.length; i++) {
                currentDiff = Math.abs(amount - values[i]);
                if (currentDiff < smallestDiff) {
                    smallestDiff = currentDiff;
                    closest = i;
                }
            }
            return (closest = records[closest]);
        };
        $scope.populateReceiptCharges = function (key, cancelEdit, srchByDebtor) {
            const file_trans = $scope.file_trans_list[key];
            $scope.file_trans_key = key;
            const loaded = false;
            if (cancelEdit) {
                $scope.txnMatching.enabled = false;
                $scope.returnAllFileTransToLastData(key);
                // loaded = $scope.editCurrentFileTrans(true);
            }
            if (file_trans.debtor_code != '') {
                // if(!loaded){
                file_trans.debtorID = file_trans.debtor_code;
                file_trans.propertyID = file_trans.property_code;
                file_trans.leaseID = `${file_trans.property_code}|${file_trans.lease_code}`;
                $scope.selectSrchResult(
                    file_trans.debtor_code,
                    file_trans.property_code,
                    file_trans.lease_code,
                    file_trans.bank_code,
                    srchByDebtor,
                );
                // }
            } else {
                $scope.clearBankReceiptChargesView();
                $scope.editCurrentFileTrans();
                document.getElementById('main-header-form').scrollIntoView();
                // $location.hash('main-header-form');
                // $anchorScroll();
            }
        };
        $scope.reDatasBankReceipts = function (transDate, nextKey) {
            for (var i = 0; i < $scope.file_trans_list_grp[transDate].datas.length; i++) {
                const row = $scope.file_trans_list_grp[transDate].datas[i];
                if (row.processed) {
                    $scope.file_trans_list_grp[transDate].datas.splice(i, 1);
                }
            }

            let ctrPage = 1;
            let curPage = 0;
            for (var i = 0; i < $scope.file_trans_list_grp[transDate].datas.length; i++) {
                const row = $scope.file_trans_list_grp[transDate].datas[i];
                if (nextKey == row.list_key) {
                    break;
                }
                if (ctrPage == $scope.file_trans_list_grp[transDate].perPage) {
                    curPage += 1;
                    ctrPage = 0;
                }
                ctrPage++;
            }
            $scope.file_trans_list_grp[transDate].curPage = curPage;
        };
        $scope.getNextBankReceiptLine = function () {
            const currentKey = $scope.file_trans_key;
            let next_row = null;
            for (const grp in $scope.file_trans_list_grp) {
                if ($scope.file_trans_list_grp.hasOwnProperty(grp)) {
                    for (let i = 0; i < $scope.file_trans_list_grp[grp].datas.length; i++) {
                        const row = $scope.file_trans_list_grp[grp].datas[i];
                        if (row.list_key == currentKey && !row.deleted) {
                            if ($scope.file_trans_list_grp[grp].datas[i + 1]) {
                                next_row = $scope.file_trans_list_grp[grp].datas[i + 1];
                            }
                            break;
                        }
                    }
                }
            }
            return next_row;
        };
        $scope.skipNextBankReceipt = function () {
            const next_row = $scope.getNextBankReceiptLine();
            if (next_row) {
                $scope.fileMethodReInit();
                $scope.file_trans_key = next_row.list_key;
                $scope.reDatasBankReceipts(next_row.trans_date, next_row.list_key);
                $scope.populateReceiptCharges($scope.file_trans_key, true);
            } else {
                $scope.file_trans_key = null;
                $scope.fileMethodReInit();
                $scope.clearFileTransGrp();
            }
        };
        $scope.countCheckedBankReceipt = function (transDate) {
            const listGrp = $scope.file_trans_list_grp[transDate].datas;
            let ctr = 0;
            for (let i = 0; i < listGrp.length; i++) {
                if (listGrp[i].ch) {
                    ctr++;
                }
            }
            return ctr;
        };
        $scope.viewFileTransTenantAct = function () {
            const file_trans = $scope.file_trans_list[$scope.file_trans_key];
            if (file_trans.lease_code) {
                // let from = $scope.tenant_act_tbl.from;
                // let to = $scope.tenant_act_tbl.to;

                const from = $('#tenant_act_from').val();
                const to = $('#tenant_act_to').val();
                if (moment(from, 'DD/MM/YYYY').isValid() && moment(to, 'DD/MM/YYYY').isValid()) {
                    const params = {
                        leaseID: file_trans.lease_code,
                        propertyID: file_trans.property_code,
                        from: from,
                        to: to,
                    };
                    $scope.tenant_act_tbl = {
                        datas: [],
                        details: file_trans,
                        from: from,
                        to: to,
                        opening: 0,
                        closing: 0,
                    };
                    htmler.request('AR/getTenantAct', params).then(function (data) {
                        angular.element('#tenant-act-modal').addClass('show');
                        angular
                            .element('html')
                            .append(
                                '<div class="htmler-backgrounder" style="position:absolute;top:0;bottom:0;left:0;right:0;background-color:rgba(0, 0, 0, 0.2);"></div>',
                            );
                        $scope.tenant_act_tbl.datas = data.rows;
                        $scope.tenant_act_tbl.opening = data.opening;
                        $scope.tenant_act_tbl.closing = data.closing;
                    });
                }
            }
        };
        // #########################################################################################
        // ## EDIT MATCHING OF TXN
        $scope.returnAllFileTransToLastData = function (exceptKey) {
            for (let i = 0; i < $scope.file_trans_list.length; i++) {
                const file_trans = $scope.file_trans_list[i];
                file_trans.onEdit = false;
                if (file_trans.list_key != exceptKey && Object.keys(file_trans.lastData).length > 0) {
                    file_trans.debtorID = file_trans.lastData.debtorID;
                    file_trans.debtor_code = file_trans.lastData.debtor_code;
                    file_trans.debtor_name = file_trans.lastData.debtor_name;
                    file_trans.leaseID = file_trans.lastData.leaseID;
                    file_trans.lease_code = file_trans.lastData.lease_code;
                    file_trans.lease_name = file_trans.lastData.lease_name;
                    file_trans.propertyID = file_trans.lastData.propertyID;
                    file_trans.property_code = file_trans.lastData.property_code;
                    file_trans.property_name = file_trans.lastData.property_name;
                    file_trans.debtorOnly = file_trans.lastData.debtorOnly;
                    file_trans.crn = file_trans.lastData.crn;
                    file_trans.suggestedID = file_trans.lastData.suggestedID;
                    file_trans.lastData = {};
                }
            }
        };
        $scope.editCurrentFileTrans = function (cancelEdit) {
            const file_trans = $scope.file_trans_list[$scope.file_trans_key];
            if (cancelEdit) {
                // WHEN EDITING IS CANCELLED
                // RESET TO LAST DATA
                file_trans.onEdit = false;
                $scope.txnMatching.enabled = false;
                if (Object.keys(file_trans.lastData).length > 0) {
                    file_trans.debtorID = file_trans.lastData.debtorID;
                    file_trans.debtor_code = file_trans.lastData.debtor_code;
                    file_trans.debtor_name = file_trans.lastData.debtor_name;
                    file_trans.leaseID = file_trans.lastData.leaseID;
                    file_trans.lease_code = file_trans.lastData.lease_code;
                    file_trans.lease_name = file_trans.lastData.lease_name;
                    file_trans.propertyID = file_trans.lastData.propertyID;
                    file_trans.property_code = file_trans.lastData.property_code;
                    file_trans.property_name = file_trans.lastData.property_name;
                    file_trans.debtorOnly = file_trans.lastData.debtorOnly;
                    file_trans.crn = file_trans.lastData.crn;
                    file_trans.suggestedID = file_trans.lastData.suggestedID;
                    if (file_trans.debtor_code == '') {
                        $scope.clearBankReceiptChargesView();
                    } else {
                        $scope.selectSrchResult(
                            file_trans.debtor_code,
                            file_trans.property_code,
                            file_trans.lease_code,
                            file_trans.bank_code,
                        );
                    }
                }
            } else {
                // WHEN EDITING IS STARTED
                file_trans.onEdit = true;
                // SAVE LAST DATA
                file_trans.lastData = {
                    debtorID: file_trans.debtorID,
                    debtor_code: file_trans.debtor_code,
                    debtor_name: file_trans.debtor_name,
                    leaseID: file_trans.leaseID,
                    lease_code: file_trans.lease_code,
                    lease_name: file_trans.lease_name,
                    propertyID: file_trans.propertyID,
                    property_code: file_trans.property_code,
                    property_name: file_trans.property_name,
                    crn: file_trans.crn,
                    suggestedID: file_trans.suggestedID,
                    debtorOnly: file_trans.debtorOnly,
                };
                const file_trans_parsed = JSON.parse(JSON.stringify(file_trans));
                delete file_trans_parsed.debtorDrop;
                delete file_trans_parsed.propertyDrop;
                delete file_trans_parsed.leaseDrop;
                delete file_trans_parsed.lastData;
                const params = { toEdits: JSON.stringify([file_trans_parsed]) };
                htmler.request('AR/getSuggesteds', params).then(function (data) {
                    if (data[0] && data[0].file_trans_id == file_trans.file_trans_id) {
                        const suggestedRows = data[0].matches;
                        $scope.setCurrentFileTransDropDowns(suggestedRows);
                        file_trans.matches = data[0].matches;
                        $scope.txnMatchingLoad('suggested');
                        if (angular.element('#useFileTransID') != '')
                            document.getElementById('main-header-form').scrollIntoView();
                    }
                });
            }
        };
        $scope.txnMatchingLoad = function (useBy) {
            const file_trans = $scope.file_trans_list[$scope.file_trans_key];
            const match = $scope.txnMatching;

            $scope.clearBankReceiptChargesView();
            $scope.clearCurrentFileTransValues();
            $scope.setCurrentFileTransDropDowns();

            match.enabled = true;
            match.by = useBy;
            match.mainVal = '';
            match.mainDrop = [];

            switch (useBy) {
                case 'suggested':
                    if (file_trans.matches.length > 0) {
                        $scope.txnMatching.by = 'suggested';
                        $scope.txnMatching.mainDrop = file_trans.suggestedDrop;

                        if (file_trans.matches.length == 1) {
                            $scope.txnMatching.mainVal = file_trans.matches[0].value;
                            $scope.setCurrentFileTransValues(file_trans.matches[0]);
                            $scope.populateReceiptCharges($scope.file_trans_key);
                            $scope.filterCurrentFileTransDropDowns('leaseID', file_trans.matches[0].leaseID);
                        }
                    }
                    break;
                case 'drawers':
                    if ($scope['drawersList'].length <= 0) {
                        htmler.getDropDownsList('drawers').then(function (data) {
                            const setDatas = [];
                            for (let i = 0; i < data.length; i++) {
                                if (data[i].label) {
                                    const strSplit = data[i].label.split(' - ');
                                    setDatas.push({ value: `${strSplit[0]}|${data[i].value}`, label: data[i].label });
                                }
                            }
                            const list = htmler.selectDflt('Select a drawer', setDatas);
                            $scope['drawersList'] = list;
                            match.mainDrop = list;
                        });
                    } else match.mainDrop = $scope['drawersList'];
                    break;
                case 'efts':
                    if ($scope['eftsList'].length <= 0) {
                        const elem = document.getElementById('method_efts_id');
                        const options = [];
                        for (i = 0; i < elem.length; i++) {
                            // txt = txt + "\n" + elem.options[i].text;
                            const opt = { label: elem.options[i].text, value: elem.options[i].value };
                            options.push(opt);
                        }
                        $scope['eftsList'] = options;
                        match.mainDrop = options;
                        // htmler.getDropDownsList('drawers').then(function(data){
                        // 	// console.log(data);
                        // 	let setDatas = [];
                        // 	for (var i = 0; i < data.length; i++) {
                        // 		if(data[i].label){
                        // 			let strSplit = data[i].label.split(' - ');
                        // 			setDatas.push({value: strSplit[0]+'|'+data[i].value, label: data[i].label});
                        // 		}
                        // 	}
                        // 	var list = htmler.selectDflt('Select a Direct Deposit Name',setDatas);
                        // 	$scope['eftsList'] = list;
                        // 	match.mainDrop = list;
                        // });
                    } else match.mainDrop = $scope['eftsList'];
                    break;
                case 'crn':
                    file_trans.propertyDrop = [];
                    file_trans.leaseDrop = [];
                    file_trans.debtorDrop = [];
                    break;
                case 'invoice':
                    file_trans.propertyDrop = [];
                    file_trans.leaseDrop = [];
                    file_trans.debtorDrop = [];
                    break;
                default:
                    const drop = `${useBy}Drop`;
                    if (file_trans[drop] && file_trans[drop].length > 0) {
                        match.mainDrop = file_trans[drop];
                    }
                    break;
            }
        };
        $scope.onChangeTxnMatchingDrop = function (dropdown, selectedAutoComplete) {
            const file_trans = $scope.file_trans_list[$scope.file_trans_key];
            const match = $scope.txnMatching;
            if (match.enabled) {
                switch (dropdown) {
                    case 'method':
                        $scope.txnMatchingLoad(match.by);
                        break;
                    case 'main':
                        if (match.mainVal != '') {
                            let mainDropDetails = {};
                            for (var i = 0; i < match.mainDrop.length; i++) {
                                if (match.mainDrop[i].value == match.mainVal) {
                                    mainDropDetails = match.mainDrop[i];
                                    break;
                                }
                            }
                            $scope.clearCurrentFileTransValues();
                            switch (match.by) {
                                case 'suggested':
                                    $scope.setCurrentFileTransValues(mainDropDetails);
                                    $scope.populateReceiptCharges($scope.file_trans_key);
                                    $scope.filterCurrentFileTransDropDowns('leaseID', mainDropDetails.leaseID);
                                    break;
                                case 'debtor':
                                    $scope.setCurrentFileTransValues(mainDropDetails, false, true, true);
                                    $scope.populateReceiptCharges($scope.file_trans_key, false, true);
                                    $scope.filterCurrentFileTransDropDowns('debtorID', mainDropDetails.debtorID);
                                    break;
                                case 'property':
                                    $scope.setCurrentFileTransValues(mainDropDetails, true, false, true);
                                    $scope.filterCurrentFileTransDropDowns('propertyID', mainDropDetails.propertyID);
                                    break;
                                case 'lease':
                                    $scope.setCurrentFileTransValues(mainDropDetails);
                                    $scope.populateReceiptCharges($scope.file_trans_key);
                                    $scope.filterCurrentFileTransDropDowns('leaseID', mainDropDetails.leaseID);
                                    break;
                                case 'drawers':
                                    htmler
                                        .getDropDownsList('leaseDebtorsByDrawer', {
                                            drawerID: match.mainVal.split('|')[1],
                                        })
                                        .then(function (data) {
                                            const debIDs = [];
                                            for (let i = 0; i < data.length; i++) {
                                                debIDs.push(data[i].value);
                                            }
                                            if (debIDs.length > 0) {
                                                $scope.filterCurrentFileTransDropDowns('debtorID', debIDs);
                                                file_trans.propertyDrop = [];
                                                file_trans.leaseDrop = [];
                                            }
                                        });
                                    break;
                                case 'efts':
                                    htmler
                                        .getDropDownsList('leaseDebtorsByDrawer', {
                                            drawerID: match.mainVal.split('|')[1],
                                        })
                                        .then(function (data) {
                                            const debIDs = [];
                                            for (let i = 0; i < data.length; i++) {
                                                debIDs.push(data[i].value);
                                            }
                                            if (debIDs.length > 0) {
                                                $scope.filterCurrentFileTransDropDowns('debtorID', debIDs);
                                                file_trans.propertyDrop = [];
                                                file_trans.leaseDrop = [];
                                            }
                                        });
                                    break;
                                case 'crn':
                                    file_trans.propertyDrop = [];
                                    file_trans.leaseDrop = [];
                                    file_trans.debtorDrop = [];
                                case 'invoice':
                                    file_trans.propertyDrop = [];
                                    file_trans.leaseDrop = [];
                                    file_trans.debtorDrop = [];
                                    break;
                            }
                        } else {
                            $scope.clearBankReceiptChargesView();
                            $scope.clearCurrentFileTransValues();
                        }
                        break;
                    case 'debtor':
                        if (file_trans.debtorID) {
                            let dropDetails = {};
                            for (var i = 0; i < file_trans.debtorDrop.length; i++) {
                                if (file_trans.debtorDrop[i].value == file_trans.debtorID) {
                                    dropDetails = file_trans.debtorDrop[i];
                                    break;
                                }
                            }
                            $scope.setCurrentFileTransValues(dropDetails, false, false, false);
                            if (match.by == 'drawers' || match.by == 'efts') {
                                $scope.filterCurrentFileTransDropDowns(
                                    'debtorID',
                                    dropDetails.debtorID,
                                    true,
                                    false,
                                    false,
                                );
                            } else $scope.filterCurrentFileTransLeaseDropDown();
                        } else {
                            file_trans.leaseID = '';
                        }
                        if (file_trans.leaseID && file_trans.debtorID) {
                            $scope.populateReceiptCharges($scope.file_trans_key);
                        }
                        break;
                    case 'property':
                        if (file_trans.propertyID != '') {
                            let dropDetails = {};
                            for (var i = 0; i < file_trans.propertyDrop.length; i++) {
                                if (file_trans.propertyDrop[i].value == file_trans.propertyID) {
                                    dropDetails = file_trans.propertyDrop[i];
                                    break;
                                }
                            }
                            $scope.setCurrentFileTransValues(dropDetails, false, false, true);
                            $scope.filterCurrentFileTransLeaseDropDown();
                        } else {
                            file_trans.leaseID = '';
                        }
                        if (file_trans.propertyID && file_trans.leaseID && file_trans.leaseID) {
                            $scope.populateReceiptCharges($scope.file_trans_key);
                        }
                        break;
                    case 'lease':
                        if (file_trans.leaseID != '') {
                            let dropDetails = {};
                            for (var i = 0; i < file_trans.leaseDrop.length; i++) {
                                if (file_trans.leaseDrop[i].value == file_trans.leaseID) {
                                    dropDetails = file_trans.leaseDrop[i];
                                    break;
                                }
                            }
                            $scope.setCurrentFileTransValues(dropDetails);
                        }
                        if (file_trans.propertyID && file_trans.leaseID) {
                            $scope.populateReceiptCharges($scope.file_trans_key);
                        }
                        break;
                    case 'crn':
                        let found = false;
                        file_trans.propertyDrop = [];
                        file_trans.leaseDrop = [];
                        file_trans.debtorDrop = [];
                        if (match.mainVal && selectedAutoComplete && Object.keys(selectedAutoComplete).length > 0) {
                            let row = {};
                            for (var opt = 0; opt < $scope.allOptions.length; opt++) {
                                const option = $scope.allOptions[opt];
                                if (
                                    selectedAutoComplete.leaseID == option.leaseID &&
                                    selectedAutoComplete.propertyID == option.propertyID
                                ) {
                                    found = true;
                                    row = option;
                                    break;
                                }
                            }
                            $scope.setCurrentFileTransValues(row);
                            $scope.filterCurrentFileTransDropDowns('leaseID', row.leaseID);
                            if (file_trans.propertyID && file_trans.leaseID) {
                                $scope.populateReceiptCharges($scope.file_trans_key);
                            }
                        }
                        if (!found) {
                            file_trans.propertyDrop = [];
                            file_trans.leaseDrop = [];
                            file_trans.debtorDrop = [];
                        }
                        break;
                    case 'invoice':
                        let foundInv = false;
                        file_trans.propertyDrop = [];
                        file_trans.leaseDrop = [];
                        file_trans.debtorDrop = [];
                        if (match.mainVal && selectedAutoComplete && Object.keys(selectedAutoComplete).length > 0) {
                            let row = {};
                            for (var opt = 0; opt < $scope.allOptions.length; opt++) {
                                const option = $scope.allOptions[opt];
                                if (
                                    selectedAutoComplete.leaseID == option.leaseID &&
                                    selectedAutoComplete.propertyID == option.propertyID
                                ) {
                                    foundInv = true;
                                    row = option;
                                    break;
                                }
                            }
                            $scope.setCurrentFileTransValues(row);
                            $scope.filterCurrentFileTransDropDowns('leaseID', row.leaseID);
                            if (file_trans.propertyID && file_trans.leaseID) {
                                $scope.populateReceiptCharges($scope.file_trans_key);
                            }
                        }
                        if (!foundInv) {
                            file_trans.propertyDrop = [];
                            file_trans.leaseDrop = [];
                            file_trans.debtorDrop = [];
                        }
                        break;
                }
            }
        };
        $scope.setCurrentFileTransValues = function (details, noDebtor, noProperty, noLease) {
            const file_trans = $scope.file_trans_list[$scope.file_trans_key];
            if (!noDebtor) {
                file_trans.debtorID = details.debtorID;
                file_trans.debtor_code = details.debtorID;
                file_trans.debtor_name = details.debtorName;
            }
            if (!noProperty) {
                file_trans.propertyID = details.propertyID;
                file_trans.property_code = details.propertyID;
                file_trans.property_name = details.propertyName;
            }
            if (!noLease) {
                file_trans.leaseID = `${details.propertyID}|${details.leaseID}`;
                file_trans.lease_code = details.leaseID;
                file_trans.lease_name = details.leaseName;
                file_trans.crn = details.crn;
            }
        };
        $scope.clearCurrentFileTransValues = function (noDebtor, noProperty, noLease) {
            const file_trans = $scope.file_trans_list[$scope.file_trans_key];
            if (!noDebtor) {
                file_trans.debtorID = '';
                file_trans.debtor_code = '';
                file_trans.debtor_name = '';
            }
            if (!noProperty) {
                file_trans.propertyID = '';
                file_trans.property_code = '';
                file_trans.property_name = '';
            }
            if (!noLease) {
                file_trans.leaseID = '';
                file_trans.lease_code = '';
                file_trans.lease_name = '';
                file_trans.crn = '';
            }
        };
        $scope.filterCurrentFileTransLeaseDropDown = function () {
            const row = $scope.file_trans_list[$scope.file_trans_key];
            row.leaseDrop = [];
            if (row.propertyID && row.debtorID) {
                row.leaseDrop = [
                    {
                        label: 'Select a Lease',
                        value: '',
                        propertyID: '',
                        propertyName: '',
                        debtorID: '',
                        debtorName: '',
                        leaseID: '',
                        leaseName: '',
                        crn: '',
                        category: '',
                    },
                ];
                const leaseIn = [];
                for (let opt = 0; opt < $scope.allOptions.length; opt++) {
                    const option = $scope.allOptions[opt];
                    // let category = "Current";
                    // if(option.category == "L"){
                    // 	category = "Vacated"
                    // }
                    if (
                        option.propertyID == row.propertyID &&
                        option.debtorID == row.debtorID &&
                        leaseIn.indexOf(`${option.propertyID}|${option.leaseID}`) == -1
                    ) {
                        row.leaseDrop.push({
                            label: `(${option.propertyID}) ${option.leaseID} - ${option.leaseName} [${option.crn}]`,
                            value: `${option.propertyID}|${option.leaseID}`,
                            propertyID: option.propertyID,
                            propertyName: option.propertyName,
                            debtorID: option.debtorID,
                            debtorName: option.debtorName,
                            leaseID: option.leaseID,
                            leaseName: option.leaseName,
                            crn: option.crn,
                            category: option.leaseCategory,
                        });
                        leaseIn.push(`${option.propertyID}|${option.leaseID}`);
                    }
                }
            }
        };
        $scope.filterCurrentFileTransDropDowns = function (filterBy, filterVal, noDebtor, noProperty, noLease) {
            const row = $scope.file_trans_list[$scope.file_trans_key];
            let inVal = [];
            if (filterVal.constructor === Array) {
                inVal = filterVal;
            } else {
                inVal.push(filterVal);
            }
            if (!noDebtor) {
                row.debtorDrop = [];
                row.debtorDrop = [
                    {
                        label: 'Select a Debtor',
                        value: '',
                        propertyID: '',
                        propertyName: '',
                        debtorID: '',
                        debtorName: '',
                        leaseID: '',
                        leaseName: '',
                        crn: '',
                    },
                ];
                const debtorIn = [];
                for (var opt = 0; opt < $scope.allOptions.length; opt++) {
                    const option = $scope.allOptions[opt];
                    if (inVal.indexOf(option[filterBy]) != -1 && debtorIn.indexOf(option.debtorID) == -1) {
                        row.debtorDrop.push({
                            label: `${option.debtorID} - ${option.debtorName}`,
                            value: option.debtorID,
                            propertyID: option.propertyID,
                            propertyName: option.propertyName,
                            debtorID: option.debtorID,
                            debtorName: option.debtorName,
                            leaseID: option.leaseID,
                            leaseName: option.leaseName,
                            crn: option.crn,
                        });
                        debtorIn.push(option.debtorID);
                    }
                }
            }
            if (!noProperty) {
                row.propertyDrop = [];
                row.propertyDrop = [
                    {
                        label: 'Select a Property',
                        value: '',
                        propertyID: '',
                        propertyName: '',
                        debtorID: '',
                        debtorName: '',
                        leaseID: '',
                        leaseName: '',
                        crn: '',
                    },
                ];
                const propertyIn = [];
                for (var opt = 0; opt < $scope.allOptions.length; opt++) {
                    const option = $scope.allOptions[opt];
                    if (inVal.indexOf(option[filterBy]) != -1 && propertyIn.indexOf(option.propertyID) == -1) {
                        row.propertyDrop.push({
                            label: `${option.propertyID} - ${option.propertyName}`,
                            value: option.propertyID,
                            propertyID: option.propertyID,
                            propertyName: option.propertyName,
                            debtorID: option.debtorID,
                            debtorName: option.debtorName,
                            leaseID: option.leaseID,
                            leaseName: option.leaseName,
                            crn: option.crn,
                        });
                        propertyIn.push(option.propertyID);
                    }
                }
            }
            if (!noLease) {
                row.leaseDrop = [];
                row.leaseDrop = [
                    {
                        label: 'Select a Lease',
                        value: '',
                        propertyID: '',
                        propertyName: '',
                        debtorID: '',
                        debtorName: '',
                        leaseID: '',
                        leaseName: '',
                        crn: '',
                        category: '',
                    },
                ];
                const leaseIn = [];
                for (var opt = 0; opt < $scope.allOptions.length; opt++) {
                    const option = $scope.allOptions[opt];
                    // let category = "Current";
                    // if(option.category == "L"){
                    // 	category = "Vacated"
                    // }
                    if (
                        inVal.indexOf(option[filterBy]) != -1 &&
                        leaseIn.indexOf(`${option.propertyID}|${option.leaseID}`) == -1
                    ) {
                        row.leaseDrop.push({
                            label: `(${option.propertyID}) ${option.leaseID} - ${option.leaseName} [${option.crn}]`,
                            value: `${option.propertyID}|${option.leaseID}`,
                            propertyID: option.propertyID,
                            propertyName: option.propertyName,
                            debtorID: option.debtorID,
                            debtorName: option.debtorName,
                            leaseID: option.leaseID,
                            leaseName: option.leaseName,
                            crn: option.crn,
                            category: option.leaseCategory,
                        });
                        leaseIn.push(`${option.propertyID}|${option.leaseID}`);
                    }
                }
            }
        };
        $scope.setCurrentFileTransDropDowns = function (suggestedRows) {
            const row = $scope.file_trans_list[$scope.file_trans_key];
            // if(row.property_code != ""){
            // 	row.propertyID = row.property_code;
            // }
            // if(row.lease_code != ""){
            // 	row.leaseID = row.property_code+'|'+row.lease_code;
            // }
            // if(row.debtor_code != ""){
            // 	row.debtorID = row.debtor_code;
            // }

            row.debtorDrop = [];
            row.propertyDrop = [];
            row.leaseDrop = [];

            row.debtorDrop = [
                {
                    label: 'Select a Debtor',
                    value: '',
                    propertyID: '',
                    propertyName: '',
                    debtorID: '',
                    debtorName: '',
                    leaseID: '',
                    leaseName: '',
                    crn: '',
                },
            ];
            const debtorIn = [];
            for (var opt = 0; opt < $scope.allOptions.length; opt++) {
                const option = $scope.allOptions[opt];
                if (debtorIn.indexOf(option.debtorID) == -1) {
                    row.debtorDrop.push({
                        label: `${option.debtorID} - ${option.debtorName}`,
                        value: option.debtorID,
                        propertyID: option.propertyID,
                        propertyName: option.propertyName,
                        debtorID: option.debtorID,
                        debtorName: option.debtorName,
                        leaseID: option.leaseID,
                        leaseName: option.leaseName,
                        crn: option.crn,
                    });
                    debtorIn.push(option.debtorID);
                }
            }

            row.propertyDrop = [
                {
                    label: 'Select a Property',
                    value: '',
                    propertyID: '',
                    propertyName: '',
                    debtorID: '',
                    debtorName: '',
                    leaseID: '',
                    leaseName: '',
                    crn: '',
                },
            ];
            const propertyIn = [];
            for (var opt = 0; opt < $scope.allOptions.length; opt++) {
                const option = $scope.allOptions[opt];
                if (propertyIn.indexOf(option.propertyID) == -1) {
                    row.propertyDrop.push({
                        label: `${option.propertyID} - ${option.propertyName}`,
                        value: option.propertyID,
                        propertyID: option.propertyID,
                        propertyName: option.propertyName,
                        debtorID: option.debtorID,
                        debtorName: option.debtorName,
                        leaseID: option.leaseID,
                        leaseName: option.leaseName,
                        crn: option.crn,
                    });
                    propertyIn.push(option.propertyID);
                }
            }

            row.leaseDrop = [
                {
                    label: 'Select a Lease',
                    value: '',
                    propertyID: '',
                    propertyName: '',
                    debtorID: '',
                    debtorName: '',
                    leaseID: '',
                    leaseName: '',
                    crn: '',
                    category: '',
                },
            ];
            const leaseIn = [];
            for (var opt = 0; opt < $scope.allOptions.length; opt++) {
                const option = $scope.allOptions[opt];
                // let category = "Current";
                // if(option.category == "L"){
                // 	category = "Vacated"
                // }
                if (leaseIn.indexOf(`${option.propertyID}|${option.leaseID}`) == -1) {
                    row.leaseDrop.push({
                        label: `(${option.propertyID}) ${option.leaseID} - ${option.leaseName} [${option.crn}]`,
                        value: `${option.propertyID}|${option.leaseID}`,
                        propertyID: option.propertyID,
                        propertyName: option.propertyName,
                        debtorID: option.debtorID,
                        debtorName: option.debtorName,
                        leaseID: option.leaseID,
                        leaseName: option.leaseName,
                        crn: option.crn,
                        // category 	: option.leaseCategory,
                    });
                    leaseIn.push(`${option.propertyID}|${option.leaseID}`);
                }
            }

            if (suggestedRows && suggestedRows.length > 0) {
                row.suggestedDrop = [
                    {
                        label: 'Select a Suggestion',
                        value: '',
                        propertyID: '',
                        propertyName: '',
                        debtorID: '',
                        debtorName: '',
                        leaseID: '',
                        leaseName: '',
                        crn: '',
                    },
                ];
                for (let i = 0; i < suggestedRows.length; i++) {
                    suggestedRows[i].label = `(${suggestedRows[i].propertyID}) ${suggestedRows[i].leaseID} - ${
                        suggestedRows[i].leaseName
                    }`;
                    suggestedRows[i].value = `${suggestedRows[i].propertyID}|${suggestedRows[i].leaseID}`;
                    row.suggestedDrop.push(suggestedRows[i]);
                }
            }
        };
        // $scope.filterCurrentFileTransLeaseDropDown = function(propertyID){
        // 	return function (item) {
        // 	    if (item.propertyID == propertyID || !item.propertyID || !item) {
        // 	        return true;
        // 	    }
        // 	    return false;
        // 	};
        // }
        $scope.onChangeCurrentFileTransDropDown = function (changeDrop) {
            const file_trans = $scope.file_trans_list[$scope.file_trans_key];
            switch (changeDrop) {
                case 'debtorOnly':
                    if (file_trans.debtorOnly) {
                        file_trans.debtorID = '';
                        file_trans.debtor_code = '';
                        file_trans.debtor_name = '';
                        file_trans.leaseID = '';
                        file_trans.lease_code = '';
                        file_trans.lease_name = '';
                        file_trans.propertyID = '';
                        file_trans.property_code = '';
                        file_trans.property_name = '';
                        file_trans.crn = '';
                        file_trans.suggestedID = '';
                        $scope.clearBankReceiptChargesView();
                    }
                    break;
                case 'debtor':
                    if (file_trans.debtorOnly) {
                        const debtorDrop = file_trans.debtorDrop;
                        let selectedDebtor = {};
                        for (var i = 0; i < debtorDrop.length; i++) {
                            if (debtorDrop[i].value == file_trans.debtorID) {
                                selectedDebtor = debtorDrop[i];
                                break;
                            }
                        }

                        file_trans.debtorID = selectedDebtor.debtorID;
                        file_trans.debtor_code = selectedDebtor.debtorID;
                        file_trans.debtor_name = selectedDebtor.debtorName;

                        file_trans.leaseID = '';
                        file_trans.lease_code = '';
                        file_trans.lease_name = '';
                        file_trans.propertyID = '';
                        file_trans.property_code = '';
                        file_trans.property_name = '';
                        file_trans.crn = '';
                        file_trans.suggestedID = '';

                        if (file_trans.debtor_code)
                            $scope.selectSrchResult(
                                file_trans.debtor_code,
                                file_trans.property_code,
                                file_trans.lease_code,
                                file_trans.bank_code,
                            );
                        else $scope.clearBankReceiptChargesView();
                    }
                    break;
                case 'property':
                    if (!file_trans.suggestedID) {
                        file_trans.leaseID = '';
                    } else {
                        const suggestedDrop = file_trans.suggestedDrop;
                        let selectedSuggest = {};
                        for (var i = 0; i < suggestedDrop.length; i++) {
                            if (suggestedDrop[i].value == file_trans.suggestedID) {
                                selectedSuggest = suggestedDrop[i];
                                break;
                            }
                        }
                        if (selectedSuggest.propertyID != file_trans.propertyID) {
                            file_trans.suggestedID = '';
                        }
                    }
                    $scope.clearBankReceiptChargesView();
                    break;
                case 'lease':
                    if (file_trans.propertyID && file_trans.leaseID) {
                        const leaseDrop = file_trans.leaseDrop;
                        let selectedLease = {};
                        for (var i = 0; i < leaseDrop.length; i++) {
                            if (leaseDrop[i].value == file_trans.leaseID) {
                                selectedLease = leaseDrop[i];
                                break;
                            }
                        }
                        file_trans.debtorID = selectedLease.debtorID;
                        file_trans.debtor_code = selectedLease.debtorID;
                        file_trans.debtor_name = selectedLease.debtorName;
                        file_trans.leaseID = `${selectedLease.propertyID}|${selectedLease.leaseID}`;
                        file_trans.lease_code = selectedLease.leaseID;
                        file_trans.lease_name = selectedLease.leaseName;
                        file_trans.propertyID = selectedLease.propertyID;
                        file_trans.property_code = selectedLease.propertyID;
                        file_trans.property_name = selectedLease.propertyName;
                        file_trans.crn = selectedLease.crn;
                        file_trans.suggestedID = `${selectedLease.propertyID}|${selectedLease.leaseID}`;
                        $scope.selectSrchResult(
                            file_trans.debtor_code,
                            file_trans.property_code,
                            file_trans.lease_code,
                            file_trans.bank_code,
                        );
                    } else $scope.clearBankReceiptChargesView();
                    break;
                case 'suggested':
                    if (file_trans.suggestedID) {
                        const suggestedDrop = file_trans.suggestedDrop;
                        let selectedSuggest = {};
                        for (var i = 0; i < suggestedDrop.length; i++) {
                            if (suggestedDrop[i].value == file_trans.suggestedID) {
                                selectedSuggest = suggestedDrop[i];
                                break;
                            }
                        }
                        file_trans.debtorID = selectedSuggest.debtorID;
                        file_trans.debtor_code = selectedSuggest.debtorID;
                        file_trans.debtor_name = selectedSuggest.debtorName;
                        file_trans.leaseID = `${selectedSuggest.propertyID}|${selectedSuggest.leaseID}`;
                        file_trans.lease_code = selectedSuggest.leaseID;
                        file_trans.lease_name = selectedSuggest.leaseName;
                        file_trans.propertyID = selectedSuggest.propertyID;
                        file_trans.property_code = selectedSuggest.propertyID;
                        file_trans.property_name = selectedSuggest.propertyName;
                        file_trans.crn = selectedSuggest.crn;
                        file_trans.suggestedID = `${selectedSuggest.propertyID}|${selectedSuggest.leaseID}`;
                        $scope.selectSrchResult(
                            file_trans.debtor_code,
                            file_trans.property_code,
                            file_trans.lease_code,
                            file_trans.bank_code,
                        );
                    } else $scope.clearBankReceiptChargesView();
                    break;
                default:
                    break;
            }
        };
        // #########################################################################################
        $scope.editBankReceipt = function (transDate, key, cancelEdit) {
            const listGrp = $scope.file_trans_list_grp[transDate].datas;
            let editRow = {};
            for (let i = 0; i < listGrp.length; i++) {
                if (listGrp[i].list_key == key) {
                    editRow = listGrp[i];
                    break;
                }
            }
            if (cancelEdit) {
                editRow.edit = false;
                editRow.debtorID = editRow.lastData.debtorID;
                editRow.debtor_code = editRow.lastData.debtor_code;
                editRow.debtor_name = editRow.lastData.debtor_name;
                editRow.leaseID = editRow.lastData.leaseID;
                editRow.lease_code = editRow.lastData.lease_code;
                editRow.lease_name = editRow.lastData.lease_name;
                editRow.propertyID = editRow.lastData.propertyID;
                editRow.property_code = editRow.lastData.property_code;
                editRow.property_name = editRow.lastData.property_name;
                editRow.debtorOnly = editRow.lastData.debtorOnly;
                editRow.crn = editRow.lastData.crn;
                editRow.suggestedID = editRow.lastData.suggestedID;
                $scope.clearBankReceiptChargesView();
            } else {
                let usePropID = '';
                let useLeaseID = '';
                let useDebtorID = '';
                $scope.clearBankReceiptChargesView();
                if (editRow.property_code != '') {
                    editRow.propertyID = editRow.property_code;
                    usePropID = editRow.property_code;
                }
                if (editRow.lease_code != '') {
                    editRow.leaseID = `${editRow.property_code}|${editRow.lease_code}`;
                    useLeaseID = `${editRow.property_code}|${editRow.lease_code}`;
                }
                if (editRow.debtor_code != '') {
                    editRow.debtorID = editRow.debtor_code;
                    useDebtorID = editRow.property_code;
                }
                editRow.lastData = {
                    debtorID: editRow.debtorID,
                    debtor_code: editRow.debtor_code,
                    debtor_name: editRow.debtor_name,
                    leaseID: editRow.leaseID,
                    lease_code: editRow.lease_code,
                    lease_name: editRow.lease_name,
                    propertyID: editRow.propertyID,
                    property_code: editRow.property_code,
                    property_name: editRow.property_name,
                    crn: editRow.crn,
                    suggestedID: editRow.suggestedID,
                    debtorOnly: editRow.debtorOnly,
                };
                editRow.edit = true;

                if (!editRow.runMatched) {
                    const paramEdit = JSON.parse(JSON.stringify(editRow));
                    delete paramEdit.debtorDrop;
                    delete paramEdit.propertyDrop;
                    delete paramEdit.leaseDrop;
                    const params = { toEdits: JSON.stringify([paramEdit]) };
                    htmler.request('AR/getSuggesteds', params).then(function (data) {
                        if (data[0].matches.length > 0) {
                            const listMatches = [
                                {
                                    label: 'Select a Suggestion',
                                    value: '',
                                    propertyID: '',
                                    propertyName: '',
                                    debtorID: '',
                                    debtorName: '',
                                    leaseID: '',
                                    leaseName: '',
                                    crn: '',
                                },
                            ];
                            const first = data[0].matches[0];
                            for (let i = 0; i < data[0].matches.length; i++) {
                                data[0].matches[i].label = `(${data[0].matches[i].propertyID}) ${
                                    data[0].matches[i].leaseID
                                } - ${data[0].matches[i].leaseName}`;
                                data[0].matches[i].value =
                                    `${data[0].matches[i].propertyID}|${data[0].matches[i].leaseID}`;
                                listMatches.push(data[0].matches[i]);
                            }
                            editRow.matches = listMatches;
                            if (useDebtorID == '') {
                                $scope.setBankReceiptLineValues(editRow, first);
                                usePropID = first.propertyID;
                                useDebtorID = first.debtorID;
                                useLeaseID = `${first.propertyID}|${first.leaseID}`;
                            }
                        }

                        if (usePropID != '') {
                            editRow.leaseDrop = [{}];
                            editRow.leaseDrop.push({
                                label: 'Select a Lease',
                                value: '',
                                propertyID: '',
                                propertyName: '',
                                debtorID: '',
                                debtorName: '',
                                leaseID: '',
                                leaseName: '',
                                crn: '',
                            });
                            const leaseIn = [];
                            for (let opt = 0; opt < $scope.allOptions.length; opt++) {
                                const option = $scope.allOptions[opt];
                                if (
                                    leaseIn.indexOf(`${option.propertyID}|${option.leaseID}`) == -1 &&
                                    option.propertyID == usePropID
                                ) {
                                    editRow.leaseDrop.push({
                                        label: `(${option.propertyID}) ${option.leaseID} - ${option.leaseName} [${
                                            option.crn
                                        }]`,
                                        value: `${option.propertyID}|${option.leaseID}`,
                                        propertyID: option.propertyID,
                                        propertyName: option.propertyName,
                                        debtorID: option.debtorID,
                                        debtorName: option.debtorName,
                                        leaseID: option.leaseID,
                                        leaseName: option.leaseName,
                                        crn: option.crn,
                                    });
                                    leaseIn.push(`${option.propertyID}|${option.leaseID}`);
                                }
                            }
                        }
                        if (useLeaseID != '') {
                            editRow.leaseID = useLeaseID;
                        }
                        editRow.runMatched = true;
                    });
                } else {
                    if (editRow.matches.length >= 2) {
                        const first = editRow.matches[1];
                        if (useDebtorID == '') {
                            $scope.setBankReceiptLineValues(editRow, first);
                            usePropID = first.propertyID;
                            useDebtorID = first.debtorID;
                            useLeaseID = `${first.propertyID}|${first.leaseID}`;
                        }
                    }
                    if (usePropID != '') {
                        editRow.leaseDrop = [{}];
                        editRow.leaseDrop.push({
                            label: 'Select a Lease',
                            value: '',
                            propertyID: '',
                            propertyName: '',
                            debtorID: '',
                            debtorName: '',
                            leaseID: '',
                            leaseName: '',
                            crn: '',
                            category: '',
                        });
                        const leaseIn = [];
                        for (let opt = 0; opt < $scope.allOptions.length; opt++) {
                            const option = $scope.allOptions[opt];
                            // let category = "Current";
                            // if(option.category == "L"){
                            // 	category = "Vacated"
                            // }
                            if (
                                leaseIn.indexOf(`${option.propertyID}|${option.leaseID}`) == -1 &&
                                option.propertyID == usePropID
                            ) {
                                editRow.leaseDrop.push({
                                    label: `(${option.propertyID}) ${option.leaseID} - ${option.leaseName} [${
                                        option.crn
                                    }]`,
                                    value: `${option.propertyID}|${option.leaseID}`,
                                    propertyID: option.propertyID,
                                    propertyName: option.propertyName,
                                    debtorID: option.debtorID,
                                    debtorName: option.debtorName,
                                    leaseID: option.leaseID,
                                    leaseName: option.leaseName,
                                    crn: option.crn,
                                    category: option.leaseCategory,
                                });
                                leaseIn.push(`${option.propertyID}|${option.leaseID}`);
                            }
                        }
                    }
                    if (useLeaseID != '') {
                        editRow.leaseID = useLeaseID;
                    }
                }
                // console.log(editRow);
            }
        };
        $scope.populateBankReceiptDropDowns = function (transDate) {
            if ($scope.allOptions.length == 0) {
                htmler.request('dropDowns/allOptions').then(function (data) {
                    // $scope.allOptions = data;
                    $scope.allOptions = [];
                    for (let i = 0; i < data.length; i++) {
                        const row = data[i];
                        let category = 'Current';
                        if (row.category == 'L') {
                            category = 'Vacated';
                        }
                        row.leaseCategory = category;
                        $scope.allOptions.push(row);
                    }
                    $scope.setBankReceiptDropDowns(transDate);
                });
            } else {
                $scope.setBankReceiptDropDowns(transDate);
            }
        };
        $scope.setBankReceiptDropDowns = function (onlyTransDate) {
            for (transDate in $scope.file_trans_list_grp) {
                let loadIt = true;
                if (onlyTransDate) {
                    if (transDate != onlyTransDate) loadIt = false;
                }
                if (loadIt) {
                    const listGrpRows = $scope.file_trans_list_grp[transDate].datas;
                    for (let i = 0; i < listGrpRows.length; i++) {
                        const row = listGrpRows[i];
                        row.debtorDrop = [];
                        row.propertyDrop = [];
                        row.leaseDrop = [];

                        row.debtorDrop = [
                            {
                                label: 'Select a Debtor',
                                value: '',
                                propertyID: '',
                                propertyName: '',
                                debtorID: '',
                                debtorName: '',
                                leaseID: '',
                                leaseName: '',
                                crn: '',
                            },
                        ];
                        const debtorIn = [];
                        for (var opt = 0; opt < $scope.allOptions.length; opt++) {
                            const option = $scope.allOptions[opt];
                            if (debtorIn.indexOf(option.debtorID) == -1) {
                                row.debtorDrop.push({
                                    label: `${option.debtorID} - ${option.debtorName}`,
                                    value: option.debtorID,
                                    propertyID: option.propertyID,
                                    propertyName: option.propertyName,
                                    debtorID: option.debtorID,
                                    debtorName: option.debtorName,
                                    leaseID: option.leaseID,
                                    leaseName: option.leaseName,
                                    crn: option.crn,
                                });
                                debtorIn.push(option.debtorID);
                            }
                        }

                        row.propertyDrop = [
                            {
                                label: 'Select a Property',
                                value: '',
                                propertyID: '',
                                propertyName: '',
                                debtorID: '',
                                debtorName: '',
                                leaseID: '',
                                leaseName: '',
                                crn: '',
                            },
                        ];
                        const propertyIn = [];
                        for (var opt = 0; opt < $scope.allOptions.length; opt++) {
                            const option = $scope.allOptions[opt];
                            if (propertyIn.indexOf(option.propertyID) == -1) {
                                row.propertyDrop.push({
                                    label: `${option.propertyID} - ${option.propertyName}`,
                                    value: option.propertyID,
                                    propertyID: option.propertyID,
                                    propertyName: option.propertyName,
                                    debtorID: option.debtorID,
                                    debtorName: option.debtorName,
                                    leaseID: option.leaseID,
                                    leaseName: option.leaseName,
                                    crn: option.crn,
                                });
                                propertyIn.push(option.propertyID);
                            }
                        }

                        row.leaseDrop = [
                            {
                                label: 'Select a Lease',
                                value: '',
                                propertyID: '',
                                propertyName: '',
                                debtorID: '',
                                debtorName: '',
                                leaseID: '',
                                leaseName: '',
                                crn: '',
                                category: '',
                            },
                        ];
                        const leaseIn = [];
                        for (var opt = 0; opt < $scope.allOptions.length; opt++) {
                            const option = $scope.allOptions[opt];
                            // let category = "Current";
                            // if(option.category == "L"){
                            // 	category = "Vacated"
                            // }
                            if (leaseIn.indexOf(`${option.propertyID}|${option.leaseID}`) == -1) {
                                row.leaseDrop.push({
                                    label: `(${option.propertyID}) ${option.leaseID} - ${option.leaseName} [${
                                        option.crn
                                    }]`,
                                    value: `${option.propertyID}|${option.leaseID}`,
                                    propertyID: option.propertyID,
                                    propertyName: option.propertyName,
                                    debtorID: option.debtorID,
                                    debtorName: option.debtorName,
                                    leaseID: option.leaseID,
                                    leaseName: option.leaseName,
                                    crn: option.crn,
                                    category: option.leaseCategory,
                                });
                                leaseIn.push(`${option.propertyID}|${option.leaseID}`);
                            }
                        }
                    }
                }
            }
        };
        $scope.setBankReceiptLineValues = function (editRow, useDetails, noProperty, noLease, noDebtor) {
            if (!noDebtor) {
                editRow.debtorID = useDetails.debtorID;
                editRow.debtor_code = useDetails.debtorID;
                editRow.debtor_name = useDetails.debtorName;
            }

            if (!noLease) {
                editRow.leaseID = `${useDetails.propertyID}|${useDetails.leaseID}`;
                editRow.lease_code = useDetails.leaseID;
                editRow.lease_name = useDetails.leaseName;
            }

            if (!noProperty) {
                editRow.propertyID = useDetails.propertyID;
                editRow.property_code = useDetails.propertyID;
                editRow.property_name = useDetails.propertyName;
                editRow.crn = useDetails.crn;
            }
            editRow.suggestedID = `${useDetails.propertyID}|${useDetails.leaseID}`;
            // console.log(useDetails);
            // console.log(editRow);
        };
        $scope.setBankReceiptLineClear = function (editRow) {
            editRow.debtorID = '';
            editRow.debtor_code = '';
            editRow.debtor_name = '';
            editRow.leaseID = '';
            editRow.lease_code = '';
            editRow.lease_name = '';
            editRow.propertyID = '';
            editRow.property_code = '';
            editRow.property_name = '';
            editRow.crn = '';
            editRow.suggestedID = '';
        };
        $scope.selectBankReceiptSuggestedDropDown = function (transDate, key) {
            const listGrp = $scope.file_trans_list_grp[transDate].datas;
            let editRow = {};
            for (var i = 0; i < listGrp.length; i++) {
                if (listGrp[i].list_key == key) {
                    editRow = listGrp[i];
                    break;
                }
            }
            const matches = editRow.matches;
            let matchesDropDetails = {};
            for (var i = 0; i < matches.length; i++) {
                if (matches[i].value == editRow.suggestedID) {
                    matchesDropDetails = matches[i];
                    break;
                }
            }
            editRow.leaseID = '';
            editRow.leaseDrop = [{}];
            editRow.leaseDrop.push({
                label: 'Select a Lease',
                value: '',
                propertyID: '',
                propertyName: '',
                debtorID: '',
                debtorName: '',
                leaseID: '',
                leaseName: '',
                crn: '',
                category: '',
            });
            const leaseIn = [];
            for (let opt = 0; opt < $scope.allOptions.length; opt++) {
                const option = $scope.allOptions[opt];
                // let category = "Current";
                // if(option.category == "L"){
                // 	category = "Vacated"
                // }
                if (
                    leaseIn.indexOf(`${option.propertyID}|${option.leaseID}`) == -1 &&
                    option.propertyID == matchesDropDetails.propertyID
                ) {
                    editRow.leaseDrop.push({
                        label: `(${option.propertyID}) ${option.leaseID} - ${option.leaseName} [${option.crn}]`,
                        value: `${option.propertyID}|${option.leaseID}`,
                        propertyID: option.propertyID,
                        propertyName: option.propertyName,
                        debtorID: option.debtorID,
                        debtorName: option.debtorName,
                        leaseID: option.leaseID,
                        leaseName: option.leaseName,
                        crn: option.crn,
                        category: option.leaseCategory,
                    });
                    leaseIn.push(`${option.propertyID}|${option.leaseID}`);
                }
            }
            $scope.setBankReceiptLineValues(editRow, matchesDropDetails);
        };
        $scope.selectBankReceiptLeaseDropDown = function (transDate, key) {
            const listGrp = $scope.file_trans_list_grp[transDate].datas;
            let editRow = {};
            for (var i = 0; i < listGrp.length; i++) {
                if (listGrp[i].list_key == key) {
                    editRow = listGrp[i];
                    break;
                }
            }
            const leaseDrop = editRow.leaseDrop;
            let leaseDropDetails = {};
            for (var i = 0; i < leaseDrop.length; i++) {
                if (leaseDrop[i].value == editRow.leaseID) {
                    leaseDropDetails = leaseDrop[i];
                    break;
                }
            }
            $scope.setBankReceiptLineValues(editRow, leaseDropDetails, true);
        };
        $scope.selectBankReceiptDebtorDropDown = function (transDate, key) {
            const listGrp = $scope.file_trans_list_grp[transDate].datas;
            let editRow = {};
            for (var i = 0; i < listGrp.length; i++) {
                if (listGrp[i].list_key == key) {
                    editRow = listGrp[i];
                    break;
                }
            }
            const debtorDrop = editRow.debtorDrop;
            let debtorDropDetails = {};
            for (var i = 0; i < debtorDrop.length; i++) {
                if (debtorDrop[i].value == editRow.debtorID) {
                    debtorDropDetails = debtorDrop[i];
                    break;
                }
            }
            // console.log(debtorDropDetails);
            $scope.setBankReceiptLineValues(editRow, debtorDropDetails, true, true, false);
        };
        $scope.selectBankReceiptPropertyDropDown = function (transDate, key) {
            const listGrp = $scope.file_trans_list_grp[transDate].datas;
            let editRow = {};
            for (var i = 0; i < listGrp.length; i++) {
                if (listGrp[i].list_key == key) {
                    editRow = listGrp[i];
                    break;
                }
            }
            const propertyDrop = editRow.propertyDrop;
            let propertyDropDetails = {};
            for (var i = 0; i < propertyDrop.length; i++) {
                if (propertyDrop[i].value == editRow.propertyID) {
                    propertyDropDetails = propertyDrop[i];
                    break;
                }
            }
            editRow.leaseID = '';
            editRow.leaseDrop = [{}];
            editRow.leaseDrop.push({
                label: 'Select a Lease',
                value: '',
                propertyID: '',
                propertyName: '',
                debtorID: '',
                debtorName: '',
                leaseID: '',
                leaseName: '',
                crn: '',
            });
            const leaseIn = [];
            for (let opt = 0; opt < $scope.allOptions.length; opt++) {
                const option = $scope.allOptions[opt];
                if (
                    leaseIn.indexOf(`${option.propertyID}|${option.leaseID}`) == -1 &&
                    option.propertyID == editRow.propertyID
                ) {
                    editRow.leaseDrop.push({
                        label: `(${option.propertyID}) ${option.leaseID} - ${option.leaseName} [${option.crn}]`,
                        value: `${option.propertyID}|${option.leaseID}`,
                        propertyID: option.propertyID,
                        propertyName: option.propertyName,
                        debtorID: option.debtorID,
                        debtorName: option.debtorName,
                        leaseID: option.leaseID,
                        leaseName: option.leaseName,
                        crn: option.crn,
                    });
                    leaseIn.push(`${option.propertyID}|${option.leaseID}`);
                }
            }
            // console.log(propertyDropDetails);
            if (propertyDropDetails && propertyDropDetails.propertyID == '') {
                $scope.setBankReceiptLineClear(editRow);
            } else $scope.setBankReceiptLineValues(editRow, propertyDropDetails, false, true, true);
        };
        $scope.selectBankReceiptDebtorOnly = function (transDate, key) {
            const listGrp = $scope.file_trans_list_grp[transDate].datas;
            let editRow = {};
            for (let i = 0; i < listGrp.length; i++) {
                if (listGrp[i].list_key == key) {
                    editRow = listGrp[i];
                    break;
                }
            }
            if (editRow.debtorOnly) {
                editRow.debtor_name = '';
                editRow.leaseID = '';
                editRow.lease_code = '';
                editRow.lease_name = '';
                editRow.propertyID = '';
                editRow.property_code = '';
                editRow.property_name = '';
                editRow.crn = '';
                editRow.suggestedID = '';
            } else {
                editRow.debtor_code = '';
                editRow.debtor_name = '';
                if (editRow.matches.length > 2) {
                    const first = editRow.matches[1];
                    $scope.setBankReceiptLineValues(editRow, first);
                }
            }
        };
        $scope.confirmBankReceiptLine = function (transDate, key) {
            const listGrp = $scope.file_trans_list_grp[transDate].datas;
            let editRow = {};
            for (let i = 0; i < listGrp.length; i++) {
                if (listGrp[i].list_key == key) {
                    editRow = listGrp[i];
                    break;
                }
            }

            const errors = [];
            // if(!editRow.debtorID){
            // 	errors.push('Invalid match, Please select at least a debtor.');
            // }
            if (errors.length > 0) {
                htmler.alert($scope, 'error', errors, 10000);
            } else {
                editRow.lastData = {
                    debtorID: editRow.debtorID,
                    debtor_code: editRow.debtor_code,
                    debtor_name: editRow.debtor_name,
                    leaseID: editRow.leaseID,
                    lease_code: editRow.lease_code,
                    lease_name: editRow.lease_name,
                    propertyID: editRow.propertyID,
                    property_code: editRow.property_code,
                    property_name: editRow.property_name,
                    crn: editRow.crn,
                    suggestedID: editRow.suggestedID,
                };
                const toSave = [];
                const row = JSON.parse(JSON.stringify(editRow, (k, v) => (v === undefined ? null : v)));
                delete row.debtorDrop;
                delete row.matches;
                delete row.propertyDrop;
                delete row.leaseDrop;
                delete row.words;
                delete row.lastData;
                row.leaseID = row.lease_code;
                toSave.push(row);
                const params = {
                    trans: JSON.stringify(toSave),
                };
                // console.log(toSave);
                htmler.request('AR/saveTempTxnFileTrans', params).then(function (data) {
                    htmler.alert($scope, 'success', ['Match successfully update'], 5000);
                    editRow.edit = false;
                    if (editRow.debtor_code == '') $scope.clearBankReceiptChargesView();
                });
            }
        };
        // #########################################################################################
        $scope.editMultipleFileTrans = function (transDate) {
            const listGrp = $scope.file_trans_list_grp[transDate];
            const transIDs = [];
            const tempLists = [];
            angular.element('#txn-temp-modal').addClass('show');
            angular
                .element('html')
                .append(
                    '<div class="htmler-backgrounder" style="position:absolute;top:0;bottom:0;left:0;right:0;background-color:rgba(0, 0, 0, 0.2);"></div>',
                );
            for (let i = 0; i < listGrp.datas.length; i++) {
                const file_trans = listGrp.datas[i];
                if (file_trans.ch) {
                    let debtorOnly = false;
                    if (file_trans.debtor_code && !file_trans.property_code && !file_trans.lease_code)
                        debtorOnly = true;
                    const file_temp = {
                        trans_date: file_trans.trans_date,
                        trans_type: file_trans.trans_type,
                        trans_type_txt: file_trans.trans_type_name,
                        trans_desc1: file_trans.description,
                        trans_desc2: '',
                        trans_amount: file_trans.amount,
                        debtorID: file_trans.debtor_code ? file_trans.debtor_code : '',
                        debtorName: file_trans.debtor_name ? file_trans.debtor_name : '',
                        debtorOnly: debtorOnly,
                        propertyID: file_trans.property_code ? file_trans.property_code : '',
                        propertyName: file_trans.property_name ? file_trans.property_name : '',
                        leaseID: file_trans.lease_code ? file_trans.lease_code : '',
                        leaseName: file_trans.lease_name ? file_trans.lease_name : '',
                        toUpdate: true,
                        updateOnly: true,
                        srchShow: true,
                        line_key: i,
                        file_trans_id: file_trans.file_trans_id,
                        matches: [
                            {
                                debtorID: file_trans.debtor_code,
                                debtorName: file_trans.debtor_name,
                                leaseID: file_trans.lease_code,
                                leaseName: file_trans.lease_name,
                                propertyID: file_trans.property_code,
                                propertyName: file_trans.property_name,
                            },
                        ],
                    };
                    tempLists.push(file_temp);
                    $scope.txnTempTransDate = file_trans.trans_date;
                    $scope.txnTempBankDetail = `${file_trans.bank_code} ${file_trans.bank_name} ${file_trans.bank_account_no}`;
                }
            }
            $scope.txn_temp_list = tempLists;
            $scope.txn_temp_tbl.datas = tempLists;
            $scope.populateTempDropDowns();
        };
        $scope.editFileTrans = function (key) {
            const file_trans = $scope.file_trans_list[key];
            $scope.txnTempTransDate = file_trans.trans_date;
            $scope.txnTempBankDetail = `${file_trans.bank_code} ${file_trans.bank_name} ${file_trans.bank_account_no}`;
            angular.element('#txn-temp-modal').addClass('show');
            angular
                .element('html')
                .append(
                    '<div class="htmler-backgrounder" style="position:absolute;top:0;bottom:0;left:0;right:0;background-color:rgba(0, 0, 0, 0.2);"></div>',
                );
            let debtorOnly = false;
            if (file_trans.debtor_code && !file_trans.property_code && !file_trans.lease_code) debtorOnly = true;
            const file_temp = {
                trans_date: file_trans.trans_date,
                trans_type: file_trans.trans_type,
                trans_type_txt: file_trans.trans_type_name,
                trans_desc1: file_trans.description,
                trans_desc2: '',
                trans_amount: file_trans.amount,
                debtorID: file_trans.debtor_code,
                debtorName: file_trans.debtor_name,
                debtorOnly: debtorOnly,
                propertyID: file_trans.property_code,
                propertyName: file_trans.property_name,
                leaseID: file_trans.lease_code,
                leaseName: file_trans.lease_name,
                toUpdate: true,
                updateOnly: true,
                srchShow: true,
                line_key: 0,
                file_trans_id: file_trans.file_trans_id,
                matches: [
                    {
                        debtorID: file_trans.debtor_code,
                        debtorName: file_trans.debtor_name,
                        leaseID: file_trans.lease_code,
                        leaseName: file_trans.lease_name,
                        propertyID: file_trans.property_code,
                        propertyName: file_trans.property_name,
                    },
                ],
            };
            const tempLists = [];
            // $scope.txn_temp_list = $scope.txn_temp_tbl.datas = [];
            tempLists.push(file_temp);
            $scope.txn_temp_list = tempLists;
            $scope.txn_temp_tbl.datas = tempLists;
            $scope.populateTempDropDowns();
        };
        $scope.fileTransAdjustmentAsk = function (key) {
            $scope.file_trans_key_to_adj = key;
            angular.element('#ask-to-adj-modal').addClass('show');
            angular
                .element('html')
                .append(
                    '<div class="htmler-backgrounder" style="position:absolute;top:0;bottom:0;left:0;right:0;background-color:rgba(0, 0, 0, 0.2);"></div>',
                );
        };
        $scope.fileTransAdjustment = function () {
            const file_trans = $scope.file_trans_list[$scope.file_trans_key_to_adj];
            let useAdjDate = file_trans.trans_date;
            if ($scope.rec_date) useAdjDate = $scope.rec_date;

            if (!$scope.lastReconDate) {
                htmler.request('AR/getLastReconDate', { bankID: file_trans.bank_code }).then(function (data) {
                    $scope.lastReconDate = data.lastReconDate;
                    $scope.lastReconDateMsg = data.lastReconDateMsg;
                });
            }

            const lastReconDate = moment($scope.lastReconDate, 'DD/MM/YYYY');
            const recDate = moment(useAdjDate, 'DD/MM/YYYY');

            let valid = true;
            const msgs = [];
            if (recDate.isValid() && lastReconDate.isValid() && recDate <= lastReconDate) {
                valid = false;
                msgs.push('You cannot post an adjustment on or before the last bank reconciliation date');
            }
            if (valid) {
                const params = {
                    file_trans_id: file_trans.file_trans_id,
                    propertyID: file_trans.property_code,
                    bankID: file_trans.bank_code,
                    // 'issueDate' 			: file_trans.trans_date,
                    issueDate: useAdjDate,
                    referenceNumber: file_trans.trans_type_name,
                    adjustmentDescription: file_trans.description,
                    amount: file_trans.amount,
                };
                htmler.request('AR/fileTransToAdjustments', params).then(function (data) {
                    if (data.errors && data.errors.length > 0) {
                        htmler.alert($scope, 'error', data.errors, 5000);
                    } else {
                        htmler.alert($scope, 'success', ['File transaction is updated to Bank Recon Adjustment'], 5000);

                        for (const grp in $scope.file_trans_list_grp) {
                            if ($scope.file_trans_list_grp.hasOwnProperty(grp)) {
                                for (let i = 0; i < $scope.file_trans_list_grp[grp].datas.length; i++) {
                                    const row = $scope.file_trans_list_grp[grp].datas[i];
                                    if (row.list_key == $scope.file_trans_key_to_adj) {
                                        row.processed = true;
                                        break;
                                    }
                                }
                            }
                        }

                        $scope.skipNextBankReceipt();
                        // $scope.methodChange(true);
                    }
                    angular.element(document.querySelector('#ask-to-adj-modal')).removeClass('show');
                    angular.element(document.querySelector('.htmler-backgrounder')).remove();
                });
            } else {
                htmler.alert($scope, 'error', msgs, 10000);
            }
        };
        $scope.clearanceDaysChange = function () {
            if (
                $scope.payment_type &&
                $scope.clearDaysDefaults &&
                $scope.clearDaysDefaults.hasOwnProperty($scope.payment_type)
            ) {
                $scope.clearance_days = `${$scope.clearDaysDefaults[$scope.payment_type]}`;
            }

            $scope.clearanceDaysSetDate();
        };
        $scope.clearanceDaysSetDate = function () {
            $scope.clearance_date = '';
            if (parseInt($scope.clearance_days) > 0) {
                let clear_date = new moment($scope.rec_date, 'DD/MM/YYYY');
                for (let i = 0; i < $scope.clearance_days; i++) {
                    const check = clear_date.add(1, 'days');
                    if ([0, 6].indexOf(check.day()) !== -1) {
                        while ([0, 6].indexOf(clear_date.day()) !== -1) {
                            clear_date = clear_date.add(1, 'days');
                        }
                    } else {
                        clear_date = check;
                    }
                }
                $scope.clearance_date = clear_date.format('DD/MM/YYYY');
            }
        };
        $scope.paymentTypeChange = function () {
            switch ($scope.payment_type) {
                case 'DIR':
                    $scope.grp_depo_slip = true;
                    $scope.rec_date = moment().add(-1, 'days').format('DD/MM/YYYY');
                    $scope.bank_date = moment().add(-1, 'days').format('DD/MM/YYYY');
                    $scope.unalloc_date_from = moment().add(-1, 'days').format('DD/MM/YYYY');
                    $scope.unalloc_date_to = moment().add(-1, 'days').format('DD/MM/YYYY');
                    // $scope.clearance_days = '0'
                    break;
                case 'CHQ':
                    $scope.grp_depo_slip = true;
                    $scope.rec_date = moment().format('DD/MM/YYYY');
                    $scope.bank_date = moment().format('DD/MM/YYYY');
                    $scope.unalloc_date_from = moment().format('DD/MM/YYYY');
                    $scope.unalloc_date_to = moment().format('DD/MM/YYYY');
                    // $scope.rec_date = moment().add(-1, 'days').format("DD/MM/YYYY");
                    htmler
                        .request('defaults/getDebtorDfltCheque', { debtorID: $scope.selected.debtorID })
                        .then(function (data) {
                            if (data.length > 0) {
                                $scope.chq_drawer_name = data[0].pmqd_name.trim();
                                $scope.chq_number = '';
                                $scope.chq_bank_id = data[0].pmqd_bank.trim();
                                $scope.chq_bsb = data[0].pmqd_branch.trim();
                            }
                        });
                    // $scope.clearance_days = '3';
                    break;
                case 'BPA':
                    $scope.grp_depo_slip = true;
                    // $scope.rec_date = moment().format("DD/MM/YYYY");
                    $scope.rec_date = moment().add(-1, 'days').format('DD/MM/YYYY');
                    $scope.bank_date = moment().add(-1, 'days').format('DD/MM/YYYY');
                    $scope.unalloc_date_from = moment().add(-1, 'days').format('DD/MM/YYYY');
                    $scope.unalloc_date_to = moment().add(-1, 'days').format('DD/MM/YYYY');
                    // $scope.clearance_days = '1';
                    break;
                case 'EPO':
                    $scope.grp_depo_slip = true;
                    // $scope.rec_date = moment().format("DD/MM/YYYY");
                    $scope.rec_date = moment().add(-1, 'days').format('DD/MM/YYYY');
                    $scope.bank_date = moment().add(-1, 'days').format('DD/MM/YYYY');
                    $scope.unalloc_date_from = moment().add(-1, 'days').format('DD/MM/YYYY');
                    $scope.unalloc_date_to = moment().add(-1, 'days').format('DD/MM/YYYY');
                    // $scope.clearance_days = '1';
                    break;
                case 'DDR':
                    // $scope.clearance_days = '3';
                    break;
                default:
                    $scope.grp_depo_slip = false;
                    $scope.rec_date = moment().format('DD/MM/YYYY');
                    $scope.bank_date = moment().format('DD/MM/YYYY');
                    $scope.unalloc_date_from = moment().format('DD/MM/YYYY');
                    $scope.unalloc_date_to = moment().format('DD/MM/YYYY');
                    // $scope.clearance_days = '0'
                    break;
            }

            $('#clearance_days').trigger('chosen:updated');
            $scope.clearanceDaysChange();

            if ($scope.file_trans_key && $scope.file_trans_list[$scope.file_trans_key]) {
                $scope.rec_date = $scope.file_trans_list[$scope.file_trans_key].trans_date;
                $scope.bank_date = $scope.file_trans_list[$scope.file_trans_key].trans_date;
                $scope.unalloc_date_from = $scope.file_trans_list[$scope.file_trans_key].trans_date;
                $scope.unalloc_date_to = $scope.file_trans_list[$scope.file_trans_key].trans_date;
            }
        };
        $scope.calculateTotals = function () {
            let applied_total = 0;
            let unpaid_bal = 0;
            let main_amt = $scope.rec_amount;
            let unalloc_total = 0;
            if (isNaN(main_amt)) main_amt = 0;
            if ($scope.rec_list.length > 0) {
                angular.forEach($scope.rec_list, function (row, key) {
                    if (!isNaN(parseFloat(row.line_amount))) applied_total += parseFloat(row.line_amount);
                    if (!isNaN(parseFloat(row.unpaidAmount))) unpaid_bal += parseFloat(row.unpaidAmount);
                });
            }
            if ($scope.unallocList.length > 0) {
                angular.forEach($scope.unallocList, function (row, key) {
                    if (!isNaN(parseFloat(row.amount))) unalloc_total += parseFloat(row.amount);
                });
            }
            $scope.applied_total = applied_total + unalloc_total;
            $scope.unpaid_bal = unpaid_bal - applied_total;
            $scope.apply_bal = main_amt - $scope.applied_total.toFixed(2);
        };
        $scope.showEnableLineCreditsWarningMsg = function () {
            angular.element('#about-credits-modal').addClass('show');
            angular
                .element('html')
                .append(
                    '<div class="htmler-backgrounder" style="position:absolute;top:0;bottom:0;left:0;right:0;background-color:rgba(0, 0, 0, 0.2);"></div>',
                );
        };
        $scope.enableLineCredits = function () {
            $scope.creditForceEnable = !$scope.creditForceEnable;
            angular.element(document.querySelector('#about-credits-modal')).removeClass('show');
            angular.element(document.querySelector('.htmler-backgrounder')).remove();
        };
        $scope.clearLnAmount = function (key) {
            $scope.rec_list[key].line_amount = 0;
            $scope.calculateTotals();
        };
        $scope.allLnAmount = function (key) {
            const unpaid = $scope.rec_list[key].unpaidAmount;
            $scope.rec_list[key].line_amount = unpaid;
            $scope.calculateTotals();
        };
        $scope.allocateAll = function () {
            let total_allocated = 0;
            angular.forEach($scope.rec_list, function (row, key) {
                row.line_amount = row.unpaidAmount;
                if (!isNaN(parseFloat(row.unpaidAmount))) total_allocated += parseFloat(row.unpaidAmount);
            });

            if ($scope.unallocList.length > 0) {
                angular.forEach($scope.unallocList, function (row, key) {
                    if (!isNaN(parseFloat(row.amount))) total_allocated += parseFloat(row.amount);
                });
            }

            // if(total_allocated < 0)
            // 	$scope.rec_amount = 0;
            // else
            // 	$scope.rec_amount = total_allocated.toFixed(2);

            $scope.calculateTotals();
        };
        $scope.allocateAllAndFill = function () {
            let total_allocated = 0;
            angular.forEach($scope.rec_list, function (row, key) {
                row.line_amount = row.unpaidAmount;
                if (!isNaN(parseFloat(row.unpaidAmount))) total_allocated += parseFloat(row.unpaidAmount);
            });

            if ($scope.unallocList.length > 0) {
                angular.forEach($scope.unallocList, function (row, key) {
                    if (!isNaN(parseFloat(row.amount))) total_allocated += parseFloat(row.amount);
                });
            }

            if (total_allocated < 0) $scope.rec_amount = 0;
            else $scope.rec_amount = total_allocated.toFixed(2);

            $scope.calculateTotals();
        };
        $scope.unallocateFill = function () {
            let total_rec_amt = parseFloat($scope.rec_amount);
            angular.forEach($scope.rec_list, function (row, key) {
                if (!isNaN(parseFloat(row.line_amount))) total_rec_amt -= parseFloat(row.line_amount);
            });

            if ($scope.unallocList.length > 0) {
                angular.forEach($scope.unallocList, function (row, key) {
                    if (!isNaN(parseFloat(row.amount))) total_rec_amt -= parseFloat(row.amount);
                });
            }

            if (total_rec_amt < 0) $scope.unalloc_new_amount = 0;
            else $scope.unalloc_new_amount = total_rec_amt.toFixed(2);
        };
        $scope.allocateNone = function () {
            angular.forEach($scope.rec_list, function (row, key) {
                row.line_amount = 0;
            });
            $scope.calculateTotals();
        };
        $scope.allocateOldest = function () {
            let total_allocated = parseFloat($scope.rec_amount);

            if (!isNaN(parseFloat(total_allocated)) && total_allocated > 0) {
                angular.forEach($scope.rec_list, function (row, key) {
                    if (total_allocated > 0) {
                        const amount = total_allocated - row.unpaidAmount;
                        if (amount > 0) {
                            row.line_amount = row.unpaidAmount.toFixed(2);
                            total_allocated -= row.unpaidAmount;
                        } else {
                            row.line_amount = total_allocated.toFixed(2);
                            total_allocated -= row.unpaidAmount;
                        }
                    }
                });
            } else {
                angular.forEach($scope.rec_list, function (row, key) {
                    row.line_amount = 0;
                });
            }

            $scope.calculateTotals();
        };
        $scope.validateInvoice = function () {
            if ($scope.rec_amount < 0) {
                angular.forEach($scope.rec_list, function (row, key) {
                    if (row.transactionType != 'CRE' && row.transactionType != 'CSH') {
                        $scope.rec_list[key].line_amount = 0;
                    }
                });
                $scope.removeAllUnalloc();
            }
        };
        $scope.checkLnAmount = function (key) {
            let valid = true;
            const msgs = [];
            const unpaid_amount = parseFloat($scope.rec_list[key].unpaidAmount);
            const amount = parseFloat($scope.rec_list[key].line_amount);
            const type = $scope.rec_list[key].transactionType;

            if (!isNaN(amount)) {
                if (unpaid_amount >= 0 && amount > unpaid_amount) {
                    valid = false;
                    msgs.push(
                        `<b>#${
                            key + 1
                        }</b> - You tried to apply more than the total outstanding <b>unpaid amount ${$filter(
                            'htmlerFormatAmt',
                        )(unpaid_amount)}</b>`,
                    );
                }
                switch (type) {
                    case 'INV':
                        if (amount < 0) {
                            valid = false;
                            msgs.push(`<b>#${key + 1}</b> - You cannot allocate a negative amount to an invoice`);
                        }
                        break;
                    case 'CRE':
                        if (amount > 0) {
                            valid = false;
                            msgs.push(`<b>#${key + 1}</b> - You cannot allocate a postive amount to a credit`);
                        }
                        break;
                    case 'CSH':
                        if (unpaid_amount < 0 && amount > 0) {
                            valid = false;
                            msgs.push(
                                `<b>#${key + 1}</b> - You cannot allocate a positive amount to a negative cash amount`,
                            );
                        }
                        if (unpaid_amount > 0 && amount < 0) {
                            valid = false;
                            msgs.push(
                                `<b>#${key + 1}</b> - You cannot allocate a negative amount to a positive cash amount`,
                            );
                        }
                        if ($scope.unallocList.length > 0 && amount != 0) {
                            valid = false;
                            msgs.push(
                                `<b>#${
                                    key + 1
                                }</b> - You cannot apply unallocated cash while adding new unallocated cash amounts`,
                            );
                        }
                        break;
                }
                if (unpaid_amount < 0 && amount < unpaid_amount) {
                    valid = false;
                    msgs.push(
                        `<b>#${
                            key + 1
                        }</b> - You tried to apply more than the total outstanding <b>unpaid amount ${$filter(
                            'htmlerFormatAmt',
                        )(unpaid_amount)}</b>`,
                    );
                }
            } else {
                valid = false;
                msgs.push(`Line ${key + 1} - Entered amount is invalid.`);
            }
            if (!valid) {
                htmler.alert($scope, 'error', msgs, 10000);
                $scope.rec_list[key].line_amount = 0;
            } else {
                $scope.calculateTotals();
            }
        };
        $scope.removeAllUnalloc = function () {
            $scope.unallocList = [];
            $scope.showUnallocTbl = false;
            $scope.calculateTotals();
        };
        $scope.addUnalloc = function () {
            let valid = true;
            const msgs = [];
            if (!$scope.unalloc_new_account) {
                valid = false;
                msgs.push('Please select an Account');
            }
            if (!$scope.unalloc_new_description) {
                valid = false;
                msgs.push('Please enter a Description');
            }
            if (!$scope.unalloc_new_tax) {
                valid = false;
                msgs.push('Please select a Tax Type');
            }
            if (!$scope.unalloc_new_propertyID) {
                valid = false;
                msgs.push('Please select a Property');
            }
            if (!$scope.unalloc_new_leaseID) {
                valid = false;
                msgs.push('Please select a Lease');
            }

            if (!$scope.unalloc_date_from) {
                valid = false;
                msgs.push('Please enter From Date');
            }

            if (!$scope.unalloc_date_to) {
                valid = false;
                msgs.push('Please enter To Date');
            }

            if ($scope.unalloc_date_from && $scope.unalloc_date_to) {
                const from_date = moment($scope.unalloc_date_from, 'DD/MM/YYYY');
                const to_date = moment($scope.unalloc_date_to, 'DD/MM/YYYY');

                if (from_date > to_date) {
                    valid = false;
                    msgs.push('Your to date cannot be before your from date');
                }
            }

            if (!$scope.unalloc_new_amount) {
                valid = false;
                msgs.push('Please enter an Amount');
            } else {
                if (!isNaN(parseFloat($scope.unalloc_new_amount)) && parseFloat($scope.unalloc_new_amount) <= 0) {
                    valid = false;
                    msgs.push('Please enter a valid unallocated Amount and the amount must not be negative');
                }
            }

            if (valid) {
                const row = {
                    // 'propertyID' 	: $scope.selected.propertyID,
                    // 'leaseID' 		: $scope.selected.leaseID,
                    propertyID: $scope.unalloc_new_propertyID,
                    leaseID: $scope.unalloc_new_leaseID,
                    account: $scope.unalloc_new_account,
                    description: $scope.unalloc_new_description,
                    taxType: $scope.unalloc_new_tax,
                    amount: $scope.unalloc_new_amount,
                    sundry: $scope.unalloc_new_sundry,
                    fromDate: $scope.unalloc_date_from,
                    toDate: $scope.unalloc_date_to,
                };
                $scope.unallocList.push(row);

                $scope.unalloc_new_description = 'Amount in Credit';
                $scope.unalloc_new_amount = 0;
                $scope.unalloc_new_sundry = false;

                const has = $scope.newUnllocAccuntList.filter((row) => {
                    return row.value == $scope.unalloc_acct;
                });
                if (has && has.length > 0) $scope.unalloc_new_account = $scope.unalloc_acct;
                else $scope.unalloc_new_account = null;

                $scope.unalloc_new_tax = 'GSTFREE';
                $scope.calculateTotals();
            } else {
                htmler.alert($scope, 'error', msgs, 10000);
            }
        };
        $scope.showAddNewUnallocRow = function () {
            $scope.showUnallocTbl = true;
        };
        $scope.matchUnallocPropLease = function (changeDrop) {
            const prop = $scope.unalloc_new_propertyID;
            const lease = $scope.unalloc_new_leaseID;
            if (changeDrop == 'property') {
                if (prop)
                    $scope.leasesSrch = $scope.leasesSrchCopy.filter((row) => {
                        return row.property == prop;
                    });
                else $scope.leasesSrch = $scope.leasesSrchCopy;

                const leases = $scope.leasesSrch.filter((row) => {
                    return row.property == prop;
                });
                if (leases) $scope.unalloc_new_leaseID = leases[0].value;
            } else {
                const currLease = $scope.leasesSrch.filter((row) => {
                    return row.value == lease;
                });
                if (currLease) $scope.unalloc_new_propertyID = currLease[0].property;
            }
        };
        $scope.removeUnalloc = function (key) {
            if (key > -1) {
                $scope.unallocList.splice(key, 1);
            }
            $scope.calculateTotals();
        };
        $scope.showHideRecListNotes = function () {
            $scope.rec_list_notes_show = !$scope.rec_list_notes_show;
        };
        $scope.loadSelected = function (selected, method, action) {
            const params = {};
            if (selected) $scope.selected = selected;

            $scope.payment_type = 'DIR';
            $scope.grp_depo_slip = true;
            $scope.print_receipt = false;
            $scope.email_receipt = false;

            $('#clearance_days').trigger('chosen:updated');
            $scope.clearanceDaysChange();

            let before_date = new moment().subtract(1, 'days');
            while ([0, 6].indexOf(before_date.day()) !== -1) {
                before_date = before_date.subtract(1, 'days');
            }
            $scope.rec_date = before_date.format('DD/MM/YYYY');
            $scope.bank_date = before_date.format('DD/MM/YYYY');
            $scope.unalloc_date_from = before_date.format('DD/MM/YYYY');
            $scope.unalloc_date_to = before_date.format('DD/MM/YYYY');

            // $scope.rec_date = moment().add(-1, 'days').format("DD/MM/YYYY");
            // $scope.unalloc_date_from = moment().add(-1, 'days').format("DD/MM/YYYY");
            // $scope.unalloc_date_to = moment().add(-1, 'days').format("DD/MM/YYYY");

            params['bankID'] = $scope.selected.bankID;
            params['debtorID'] = $scope.selected.debtorID;
            params['propertyID'] = $scope.selected.propertyID;
            params['leaseID'] = $scope.selected.leaseID;
            params['leaseName'] = $scope.selected.leaseName;
            if ($scope.selected.leaseStatus != 'C') $scope.vacateds.push($scope.selected.leaseID);
            if ((method && method == 'leaseDebtors' && !$scope.property_id) || action == 'srchResultDebtor')
                delete params['propertyID'];
            if ((method && method == 'leaseDebtors' && !$scope.lease_id) || action == 'srchResultDebtor')
                delete params['leaseID'];

            $scope.dir_depo_name = '';

            $scope.chq_drawer_name = '';
            $scope.chq_number = '';
            $scope.chq_bank_id = '';
            $scope.chq_bsb = '';
            $scope.cheque_number = '';
            $scope.prn_number = '';
            //    $("#fileDownload").empty();

            $scope.rec_amount = 0;
            if ($scope.file_trans_list[$scope.file_trans_key]) {
                const file_trans = $scope.file_trans_list[$scope.file_trans_key];
                $scope.rec_amount = file_trans.amount.toFixed(2);
                $scope.payment_type = file_trans.trans_type;
                $scope.rec_date = file_trans.trans_date;
                $scope.bank_date = file_trans.trans_date;
                params['file_trans_amount'] = file_trans.amount;

                if (file_trans.trans_type == 'CHQ') {
                    $scope.grp_depo_slip = false;
                }
            }
            params['rec_date'] = $scope.rec_date;

            $scope.applied_total = 0;
            $scope.unpaid_bal = 0;
            $scope.apply_bal = 0;

            $scope.rec_list = [];
            $scope.rec_list_notes = {};
            $scope.rec_list_notes_show = false;
            $scope.rec_amounts = [];

            $scope.unalloc_new_account = '';
            $scope.unalloc_new_description = 'Amount in Credit';
            $scope.unalloc_new_amount = 0;
            if ($scope.propertiesSrch.length == 2) $scope.unalloc_new_propertyID = $scope.propertiesSrch[1].value;
            if ($scope.leasesSrch.length == 2) $scope.unalloc_new_leaseID = $scope.leasesSrch[1].value;

            $scope.showUnallocTbl = false;
            $scope.unallocList = [];
            htmler.request('AR/loadReceiptForm', params).then(function (data) {
                $scope.print_receipt = parseFloat(data.print_receipt) ? true : false;
                $scope.email_receipt = parseFloat(data.email_receipt) ? true : false;
                if (data.eftDflt[0]) {
                    if (data.eftDflt[0].pmqd_name) $scope.dir_depo_name = data.eftDflt[0].pmqd_name.trim();
                }
                $scope.unalloc_acct = data.unalloc_acct;
                $scope.rec_list = $scope.initRecList(data.records);
                $scope.rec_list_notes = data.leaseNotes;
                $scope.withBonds = data.with_bonds;
                if ($scope.method == 'wbc') $scope.payment_type = 'BPA';

                if (data.use_date) {
                    $scope.rec_date = data.use_date;
                    $scope.bank_date = data.use_date;
                }
                if ($scope.file_trans_list[$scope.file_trans_key]) {
                    let use_date = new moment($scope.rec_date, 'DD/MM/YYYY');
                    while ([0, 6].indexOf(use_date.day()) !== -1) {
                        use_date = use_date.add(1, 'days');
                    }
                    $scope.rec_date = use_date.format('DD/MM/YYYY');
                    $scope.bank_date = use_date.format('DD/MM/YYYY');
                }

                if (Object.keys($scope.rec_list_notes).length > 0) $scope.rec_list_notes_show = true;

                let total_ln_unpaid = 0;
                if ($scope.rec_list.length > 0) {
                    angular.forEach($scope.rec_list, function (row) {
                        const amount = parseFloat(row.unpaidAmount);
                        if (!isNaN(amount)) {
                            total_ln_unpaid += amount;
                        }
                    });
                }
                $scope.total_ln_unpaid = total_ln_unpaid.toFixed(2);
                $scope.unpaid_bal = total_ln_unpaid.toFixed(2);

                $scope.lastReconDate = data.lastReconDate;
                $scope.lastReconDateMsg = data.lastReconDateMsg;

                if ($scope['chequeBankList'].length <= 0) {
                    htmler.getDropDownsList('paramTypes', { paramType: 'STDBANK' }).then(function (data) {
                        const list = htmler.selectDflt('Please select...', data);
                        $scope['chequeBankList'] = list;
                    });
                }
                if ($scope['taxRatesList'].length <= 0) {
                    htmler.getDropDownsList('taxRates').then(function (data) {
                        const list = htmler.selectDflt('Please select...', data);
                        $scope['taxRatesList'] = list;
                        $scope.unalloc_new_tax = 'GSTFREE';
                    });
                }

                htmler
                    .getDropDownsList('groupedAccountsExclPropertyFunds', {
                        propertyID: $scope.selected.propertyID,
                        acountType: 'I',
                    })
                    .then(function (data) {
                        const list = htmler.selectDflt('Please select...', data);
                        $scope['newUnllocAccuntList'] = list;
                        // 0125 DEFAULT
                        const has = list.filter((row) => {
                            return row.value == $scope.unalloc_acct;
                        });
                        if (has && has.length > 0) $scope.unalloc_new_account = $scope.unalloc_acct;
                    });
                angular.element('#goTopBtn').css({ bottom: 80 });
                $scope.calculateTotals();
                // if($scope.file_trans_list[$scope.file_trans_key]){
                // angular.element('#process-btn').focus();
                // window.scrollTo(0,document.body.scrollHeight);
                // $location.hash('main-header-form');
                // $anchorScroll();
                document.getElementById('main-header-form').scrollIntoView();
                // }
            });
        };
        $scope.selectSrchResult = function (debtor_id, property_id, lease_id, bank_id, srchByDebtor) {
            $scope.property_id = property_id;
            $scope.lease_id = lease_id;
            $scope.debtor_id = debtor_id;
            // if(bank_id){
            // 	$scope.bank_acct_id = bank_id;
            // }
            let method = 'srchResult';
            if (srchByDebtor) method = 'srchResultDebtor';
            $scope.loadSearchDetails(method);
        };
        $scope.loadInvoiceDetails = function () {
            const formData = htmler.httpi();
            if ($scope.txnMatching.enabled) {
                if ($scope.txnMatching.mainVal) {
                    formData.append('search', $scope.txnMatching.mainVal);
                    $http.post(`${cirrus8ApiUrl}api/autoComplete/invoices`, formData).then(function (response) {
                        const selected = response.data;
                        if (selected.length > 0) {
                            $scope.onChangeTxnMatchingDrop('invoice', selected[0]);
                        } else {
                            htmler.alert($scope, 'error', ['Invoice number not found'], '5000');
                        }
                        angular.element('#invoice_no').blur();
                    });
                }
            } else {
                if ($scope.invoice_no) {
                    formData.append('search', $scope.invoice_no);
                    $http.post(`${cirrus8ApiUrl}api/autoComplete/invoices`, formData).then(function (response) {
                        const selected = response.data;
                        if (selected.length > 0) {
                            $scope.property_id = selected[0].propertyID;
                            $scope.debtor_id = selected[0].debtorID;
                            $scope.lease_id = selected[0].leaseID;
                            $scope.loadSearchDetails('invoiceSrch');
                        } else {
                            htmler.alert($scope, 'error', ['Invoice number not found'], '5000');
                        }
                        angular.element('#invoice_no').blur();
                    });
                }
            }
        };
        $scope.clearInvoiceDetails = function () {
            $scope.invoice_no = '';
            //$scope.bankAccountsShow = false;
            //	$scope.bank_acct_id = "";
            $scope.property_id = '';
            $scope.debtor_id = '';
            $scope.lease_id = '';
            $scope.leasesSrchShow = false;
            $scope.leaseDebtorsSrchShow = false;
            $scope.propertiesSrchShow = false;
            $scope.txnMatching.mainVal = '';
            if ($scope.txnMatching.enabled && $scope.file_trans_list[$scope.file_trans_key]) {
                const file_trans = $scope.file_trans_list[$scope.file_trans_key];
                file_trans.propertyDrop = [];
                file_trans.leaseDrop = [];
                file_trans.debtorDrop = [];
            }
        };
        $scope.loadCRNDetails = function () {
            const formData = htmler.httpi();
            if ($scope.txnMatching.enabled) {
                if ($scope.txnMatching.mainVal) {
                    formData.append('search', $scope.txnMatching.mainVal);
                    $http.post(`${cirrus8ApiUrl}api/autoComplete/crn`, formData).then(function (response) {
                        const selected = response.data;
                        if (selected.length > 0) {
                            $scope.onChangeTxnMatchingDrop('crn', selected[0]);
                        } else {
                            htmler.alert($scope, 'error', ['CRN not found'], '5000');
                        }
                    });
                }
            } else {
                if ($scope.crn_no) {
                    formData.append('search', $scope.crn_no);
                    $http.post(`${cirrus8ApiUrl}api/autoComplete/crn`, formData).then(function (response) {
                        const selected = response.data;
                        if (selected.length > 0) {
                            $scope.property_id = selected[0].propertyID;
                            $scope.debtor_id = selected[0].debtorID;
                            $scope.lease_id = selected[0].leaseID;
                            $scope.hideByCRN = false;
                            $scope.loadSearchDetails('crnSrch');
                        } else {
                            htmler.alert($scope, 'error', ['CRN not found'], '5000');
                        }
                    });
                }
            }
        };
        $scope.clearCRNDetails = function () {
            $scope.crn_no = '';
            //	$scope.bankAccountsShow = false;
            //	$scope.bank_acct_id = "";
            $scope.property_id = '';
            $scope.debtor_id = '';
            $scope.lease_id = '';
            $scope.leasesSrchShow = false;
            $scope.leaseDebtorsSrchShow = false;
            $scope.propertiesSrchShow = false;
            $scope.txnMatching.mainVal = '';
            if ($scope.txnMatching.enabled && $scope.file_trans_list[$scope.file_trans_key]) {
                const file_trans = $scope.file_trans_list[$scope.file_trans_key];
                file_trans.propertyDrop = [];
                file_trans.leaseDrop = [];
                file_trans.debtorDrop = [];
            }
        };
        $scope.clearFileTransGrp = function () {
            for (const grp in $scope.file_trans_list_grp) {
                if ($scope.file_trans_list_grp.hasOwnProperty(grp)) {
                    // console.log($scope.file_trans_list_grp[grp].datas);
                    for (let i = 0; i < $scope.file_trans_list_grp[grp].datas.length; i++) {
                        const row = $scope.file_trans_list_grp[grp].datas[i];
                        if (row.processed) {
                            // console.log(row);
                            $scope.file_trans_list_grp[grp].datas.splice(i, 1);
                        }
                    }
                }
            }
        };
        $scope.initPayHistoryList = function (arr) {
            angular.forEach(arr, function (row, key) {
                row.transDate = moment(row.transDate).format('DD/MM/YYYY');
                row.modDate = moment(row.modDate).format('DD/MM/YYYY');
            });
            return arr;
        };
        $scope.payHistoryLoadModal = function (line) {
            $scope.payHistory = {
                propertyID: line.propertyID,
                leaseID: line.leaseID,
                debtorID: line.debtorID,
                transactionDate: line.transactionDate,
                description: line.description,
                totalAmount: line.amount,
                allocTotal: 0,
                list: [],
            };
            const params = {
                batchNo: line.batchNumber,
                lineNo: line.batchLineNumber,
            };
            htmler.request('AR/getLinePaymentHistory', params).then(function (data) {
                const list = $scope.initPayHistoryList(data);
                let total = 0;
                angular.forEach(list, function (row) {
                    total += parseFloat(row.allocAmount);
                });
                $scope.payHistory.list = list;
                $scope.payHistory.allocTotal = total;
            });
        };
        $scope.initApDetailsList = function (arr) {
            angular.forEach(arr, function (row, key) {
                row.transDate = moment(row.transDate).format('DD/MM/YYYY');
                row.dueDate = moment(row.dueDate).format('DD/MM/YYYY');
                if (row.hold == 'N') row.hold = false;
                else row.hold = true;
            });
            return arr;
        };
        $scope.apDetailsLoadModal = function (line) {
            $scope.apDetailsHoldAll = false;
            angular.element('#apDetails-hold-all').prop('indeterminate', false);
            $scope.apDetails = {
                propertyID: line.propertyID,
                leaseID: line.leaseID,
                list: [],
            };
            const params = {
                batchNo: line.batchNumber,
                lineNo: line.batchLineNumber,
                linkedAps: line.linkedAps,
            };
            htmler.request('AR/getApDetails', params).then(function (data) {
                const list = $scope.initApDetailsList(data);
                $scope.apDetails.list = list;
                $scope.apDetailsHoldAllChWatch();
            });
        };
        $scope.apDetailsHoldAllCh = function () {
            angular.forEach($scope.apDetails.list, function (row) {
                if ($scope.apDetailsHoldAll) row.hold = true;
                else row.hold = false;
            });
        };
        $scope.apDetailsHoldAllChWatch = function () {
            const master = 'apDetailsHoldAll';
            const elem = angular.element('#apDetails-hold-all');
            const num = $scope.apDetails.list.length;
            let checked = 0;
            let none = 0;
            angular.forEach($scope.apDetails.list, function (row, key) {
                if (row.hold) checked++;
                else none++;
            });
            if (checked == num) {
                $scope[master] = true;
                elem.prop('indeterminate', false);
            } else if (none == num) {
                $scope[master] = false;
                elem.prop('indeterminate', false);
            } else {
                $scope[master] = true;
                elem.prop('indeterminate', true);
            }
        };
        $scope.updateApDetails = function () {
            const params = {
                ap_linked_list: JSON.stringify($scope.apDetails.list),
            };
            htmler.request('AR/updateLinkedApDetails', params).then(function (data) {
                if (data.errors && data.errors.length > 0) {
                    htmler.alert($scope, 'error', data.errors, 10000);
                } else {
                    htmler.alert($scope, 'success', ['AP(s) successfully Updated'], 10000);
                }
            });
        };
        $scope.autoCompleteCRN = {
            minimumChars: 1,
            data: function (searchText) {
                if ($scope.file_trans_list[$scope.file_trans_key]) {
                    const file_trans = $scope.file_trans_list[$scope.file_trans_key];
                    file_trans.propertyDrop = [];
                    file_trans.leaseDrop = [];
                    file_trans.debtorDrop = [];
                }
                if (searchText) {
                    const formData = htmler.httpi();
                    formData.append('search', searchText);
                    formData.append('disabledLoading', true); // DISABLE LOADER
                    return $http.post(`${cirrus8ApiUrl}api/autoComplete/crn`, formData).then(function (response) {
                        return response.data;
                    });
                } else {
                    //		$scope.bankAccountsShow = false;
                    //	$scope.bank_acct_id = "";
                    $scope.property_id = '';
                    $scope.debtor_id = '';
                    $scope.lease_id = '';
                    $scope.leasesSrchShow = false;
                    $scope.leaseDebtorsSrchShow = false;
                    $scope.propertiesSrchShow = false;
                }
            },
            renderItem: function (item) {
                return {
                    value: item.crn,
                    label: "<span ng-bind-html='entry.item.crn'></span>",
                };
            },
            itemSelected: function (data) {
                const selected = data.item;
                if (!$scope.txnMatching.enabled) {
                    $scope.property_id = selected.propertyID;
                    $scope.debtor_id = selected.debtorID;
                    $scope.lease_id = selected.leaseID;
                    $scope.loadSearchDetails('crnSrch');
                } else {
                    $scope.onChangeTxnMatchingDrop('crn', selected);
                }
            },
            ready: function (events) {
                angular.element(document.querySelector('#main_container')).bind('scroll', function () {
                    angular.element('.auto-complete-container').addClass('ng-hide');
                    angular.element('#crn').blur();
                });
            },
        };
        $scope.autoCompleteInvoices = {
            minimumChars: 1,
            data: function (searchText) {
                if ($scope.file_trans_list[$scope.file_trans_key]) {
                    const file_trans = $scope.file_trans_list[$scope.file_trans_key];
                    file_trans.propertyDrop = [];
                    file_trans.leaseDrop = [];
                    file_trans.debtorDrop = [];
                }
                if (searchText) {
                    const formData = htmler.httpi();
                    formData.append('search', searchText);
                    formData.append('disabledLoading', true); // DISABLE LOADER
                    return $http.post(`${cirrus8ApiUrl}api/autoComplete/invoices`, formData).then(function (response) {
                        return response.data;
                    });
                } else {
                    //     		$scope.bankAccountsShow = false;
                    //		$scope.bank_acct_id = "";
                    $scope.property_id = '';
                    $scope.debtor_id = '';
                    $scope.lease_id = '';
                    $scope.leasesSrchShow = false;
                    $scope.leaseDebtorsSrchShow = false;
                    $scope.propertiesSrchShow = false;
                }
            },
            renderItem: function (item) {
                return {
                    value: item.invoiceID,
                    label: "<span ng-bind-html='entry.item.invoiceID'></span>",
                };
            },
            itemSelected: function (data) {
                const selected = data.item;
                if (!$scope.txnMatching.enabled) {
                    $scope.property_id = selected.propertyID;
                    $scope.debtor_id = selected.debtorID;
                    $scope.lease_id = selected.leaseID;
                    $scope.loadSearchDetails('invoiceSrch');
                } else {
                    $scope.onChangeTxnMatchingDrop('invoice', selected);
                }
            },
            ready: function (events) {
                angular.element(document.querySelector('#main_container')).bind('scroll', function () {
                    angular.element('.auto-complete-container').addClass('ng-hide');
                    angular.element('#invoice_no').blur();
                });
            },
        };
        $scope.formatNumber = function (number) {
            if ($scope[number]) $scope[number] = parseFloat($scope[number]).toFixed(2);
        };
        $scope.formatNumberRpt = function (code, key, name) {
            if ($scope[code][key][name]) $scope[code][key][name] = parseFloat($scope[code][key][name]).toFixed(2);
        };
        $scope.selectSuggestion = function (key, useLeaseID) {
            const codeSplit = useLeaseID.split('{|}');
            $scope.txn_temp_list[key].leaseID = codeSplit[0];
            $scope.txn_temp_list[key].propertyID = codeSplit[1];
            $scope.txn_temp_tbl.datas[key].leaseID = codeSplit[0];
            $scope.txn_temp_tbl.datas[key].propertyID = codeSplit[1];
            $scope.populateLeaseTempDrop(key);
            const details = $scope.getDropDetails(key, 'lease', 'property');
            if (details) {
                $scope.txn_temp_list[key].leaseName = details.leaseName;
                $scope.txn_temp_list[key].propertyID = details.propertyID;
                $scope.txn_temp_list[key].propertyName = details.propertyName;
                $scope.txn_temp_list[key].debtorID = details.debtorID;
                $scope.txn_temp_list[key].debtorName = details.debtorName;
                $scope.txn_temp_list[key].crn = details.crn;

                $scope.txn_temp_tbl.datas[key].leaseName = details.leaseName;
                $scope.txn_temp_tbl.datas[key].propertyName = details.propertyName;
                $scope.txn_temp_tbl.datas[key].debtorID = details.debtorID;
                $scope.txn_temp_tbl.datas[key].debtorName = details.debtorName;
                $scope.txn_temp_tbl.datas[key].crn = details.crn;
            }
        };
        $scope.saveFileTempTrans = function () {
            const toSave = [];
            for (let i = 0; i < $scope.txn_temp_tbl.datas.length; i++) {
                const row = JSON.parse(
                    JSON.stringify($scope.txn_temp_tbl.datas[i], (k, v) => (v === undefined ? null : v)),
                );
                delete row.debtorDrop;
                delete row.matches;
                delete row.propertyDrop;
                delete row.leaseDrop;
                delete row.words;
                if (row.ch) toSave.push(row);
            }
            if (toSave.length == 0) {
                htmler.alert($scope, 'error', ['Nothing matched selected to save'], 5000);
                return false;
            }
            const params = {
                // 'temp_trans' : JSON.stringify($scope.txn_temp_list),
                temp_trans: JSON.stringify(toSave),
                temp_file: $scope.txn_file,
            };
            htmler.request('AR/saveTempTxnFileTrans', params).then(function (data) {
                htmler.alert(
                    $scope,
                    'success',
                    ['Confirmed matches between bank statement and cirrus8 have been successful.'],
                    10000,
                );
                if (data.update_id) {
                    $scope.loadFileNoReceiptTrans();
                    angular.forEach($scope.file_trans_list, function (file_trans, key) {
                        if (data.update_id == file_trans.file_trans_id) {
                            $scope.file_trans_key = key;
                            file_trans.debtor_code = data.update_debtor;
                            $scope.selectSrchResult(
                                file_trans.debtor_code,
                                file_trans.property_code,
                                file_trans.lease_code,
                            );
                            return false;
                        }
                    });
                } else {
                    $scope.loadFileNoReceiptTrans();
                }
                angular.element(document.querySelector('#txn-temp-modal')).removeClass('show');
                angular.element(document.querySelector('.htmler-backgrounder')).remove();
            });
        };
        $scope.fileMethodReInit = function () {
            // $scope.bank_acct_id = "";
            $scope.debtor_id = '';
            $scope.lease_id = '';
            $scope.property_id = '';
            $scope.pay_add = '';
            $scope.pay_name = '';
            $scope.pay_state = '';
            $scope.pay_suburb = '';
            $scope.rec_no = '';
            $scope.rec_list = [];
            $scope.cheque_number = '';
            $scope.prn_number = '';
            $('#fileDownload').empty();

            // $scope.bankAccountsShow = false;
            $scope.leaseDebtorsSrchShow = false;
            $scope.leasesSrchShow = false;
            $scope.propertiesSrchShow = false;
            $scope.selected = '';
        };
        $scope.fileLineDebtoryOnly = function (key) {
            const file_line = $scope.txn_temp_list[key];
            if (file_line.debtorOnly) {
                file_line.propertyID = '';
                file_line.propertyName = '';
                file_line.leaseID = '';
                file_line.leaseName = '';
                file_line.useLeaseID = '';
                file_line.useLeaseName = '';
                file_line.crn = '';
            } else {
                if (file_line.matches.length > 0) {
                    let match = {};
                    angular.forEach(file_line.matches, function (row, key) {
                        if (row.debtorID == file_line.debtorID) {
                            match = row;
                            return false;
                        }
                    });
                    if (Object.keys(match).length === 0) {
                        if (file_line.matches.length > 0) {
                            match = file_line.matches[1];
                        }
                    }
                    if (Object.keys(match).length > 0) {
                        file_line.useLeaseID = `${match.leaseID}{|}${match.propertyID}`;
                        file_line.useLeaseName = `(${match.propertyID}) ${match.leaseID} ${match.leaseName}`;
                        file_line.leaseID = match.leaseID;
                        const details = $scope.getDropDetails(key, 'lease');
                        $scope.txn_temp_list[key].leaseName = details.leaseName;
                        $scope.txn_temp_list[key].propertyID = details.propertyID;
                        $scope.txn_temp_list[key].propertyName = details.propertyName;
                        $scope.txn_temp_list[key].debtorID = details.debtorID;
                        $scope.txn_temp_list[key].debtorName = details.debtorName;
                        $scope.txn_temp_list[key].crn = details.crn;
                        $scope.txn_temp_tbl.datas[key].leaseName = details.leaseName;
                        $scope.txn_temp_tbl.datas[key].propertyID = details.propertyID;
                        $scope.txn_temp_tbl.datas[key].propertyName = details.propertyName;
                        $scope.txn_temp_tbl.datas[key].debtorID = details.debtorID;
                        $scope.txn_temp_tbl.datas[key].debtorName = details.debtorName;
                        $scope.txn_temp_tbl.datas[key].crn = details.crn;
                    } else {
                        $scope.txn_temp_list[key].useLeaseID = '';
                        $scope.txn_temp_list[key].useLeaseName = '';
                        $scope.txn_temp_list[key].leaseID = '';
                        $scope.txn_temp_list[key].leaseName = '';
                        $scope.txn_temp_list[key].propertyID = '';
                        $scope.txn_temp_list[key].propertyName = '';
                        $scope.txn_temp_list[key].debtorID = '';
                        $scope.txn_temp_list[key].debtorName = '';
                        $scope.txn_temp_list[key].crn = '';
                        $scope.txn_temp_tbl.datas[key].useLeaseID = '';
                        $scope.txn_temp_tbl.datas[key].useLeaseName = '';
                        $scope.txn_temp_tbl.datas[key].leaseID = '';
                        $scope.txn_temp_tbl.datas[key].leaseName = '';
                        $scope.txn_temp_tbl.datas[key].propertyID = '';
                        $scope.txn_temp_tbl.datas[key].propertyName = '';
                        $scope.txn_temp_tbl.datas[key].debtorID = '';
                        $scope.txn_temp_tbl.datas[key].debtorName = '';
                        $scope.txn_temp_tbl.datas[key].crn = '';
                    }
                } else {
                    $scope.txn_temp_list[key].useLeaseID = '';
                    $scope.txn_temp_list[key].useLeaseName = '';
                    $scope.txn_temp_list[key].leaseID = '';
                    $scope.txn_temp_list[key].leaseName = '';
                    $scope.txn_temp_list[key].propertyID = '';
                    $scope.txn_temp_list[key].propertyName = '';
                    $scope.txn_temp_list[key].debtorID = '';
                    $scope.txn_temp_list[key].debtorName = '';
                    $scope.txn_temp_list[key].crn = '';
                    $scope.txn_temp_tbl.datas[key].useLeaseID = '';
                    $scope.txn_temp_tbl.datas[key].useLeaseName = '';
                    $scope.txn_temp_tbl.datas[key].leaseID = '';
                    $scope.txn_temp_tbl.datas[key].leaseName = '';
                    $scope.txn_temp_tbl.datas[key].propertyID = '';
                    $scope.txn_temp_tbl.datas[key].propertyName = '';
                    $scope.txn_temp_tbl.datas[key].debtorID = '';
                    $scope.txn_temp_tbl.datas[key].debtorName = '';
                    $scope.txn_temp_tbl.datas[key].crn = '';
                }
            }
        };
        $scope.tempTxnFileCheckAll = function () {
            angular.forEach($scope.txn_temp_tbl.datas, function (row, key) {
                if (!row.edit) row.ch = $scope.txnTempChAll;
            });
            $scope.tempTxnFileCheckWatch();
        };
        $scope.tempTxnFileCheckWatch = function () {
            const listTbl = $scope.txn_temp_tbl.datas;

            const elem = angular.element('#check-all-temp');
            const num = listTbl.length;
            let checked = 0;
            let none = 0;
            angular.forEach(listTbl, function (row, key) {
                if (row.ch) checked++;
                else none++;
            });
            $scope.txn_temp_ch_ctr = checked;
            if (checked == num) {
                $scope.txnTempChAll = true;
                elem.prop('indeterminate', false);
            } else if (none == num) {
                $scope.txnTempChAll = false;
                elem.prop('indeterminate', false);
            } else {
                $scope.txnTempChAll = true;
                elem.prop('indeterminate', true);
            }
        };
        $scope.populateTempDropDowns = function () {
            if ($scope.temp_drop_list.length == 0) {
                htmler.request('dropDowns/allOptions').then(function (data) {
                    $scope.temp_drop_list = data;
                    $scope.setTempDropDowns($scope.temp_drop_list);
                });
            } else {
                $scope.setTempDropDowns($scope.temp_drop_list);
            }
        };
        $scope.setTempDropDowns = function (options) {
            const drops = ['debtor', 'lease', 'property'];
            for (let i = 0; i < $scope.txn_temp_tbl.datas.length; i++) {
                const row = $scope.txn_temp_tbl.datas[i];
                for (let d = 0; d < drops.length; d++) {
                    const forName = drops[d];
                    const dropList = [];
                    const label = `Select a ${forName}`;
                    dropList.push({ label: label, value: '' });
                    angular.forEach(options, function (row, key) {
                        if (dropList.indexOf(row[`${forName}ID`]) == -1) {
                            // let crn = "";
                            // if(row.crn)
                            // 	crn = crn.substring(0, crn.length - 1);
                            dropList.push({
                                label: `${row[`${forName}ID`]} - ${row[`${forName}Name`]}`,
                                value: row[`${forName}ID`],
                                propertyID: row.propertyID,
                                propertyName: row.propertyName,
                                debtorID: row.debtorID,
                                debtorName: row.debtorName,
                                leaseID: row.leaseID,
                                leaseName: row.leaseName,
                                crn: row.crn,
                            });
                            // dropList.push(row[forName+'ID']);
                        }
                    });
                    row[`${forName}Drop`] = dropList;
                }
            }
        };
        $scope.populateLeaseTempDrop = function (key) {
            $scope.txn_temp_tbl.datas[key].leaseDrop = [];
            const rows = [];
            const forName = 'lease';
            const label = 'Select a lease';
            rows.push({ leaseID: '', leaseName: '', propertyID: '', label: label, value: '' });
            for (let i = $scope.temp_drop_list.length - 1; i >= 0; i--) {
                if ($scope.temp_drop_list[i].propertyID == $scope.txn_temp_tbl.datas[key].propertyID) {
                    const row = $scope.temp_drop_list[i];
                    rows.push({
                        label: `${row[`${forName}ID`]} - ${row[`${forName}Name`]}`,
                        value: row[`${forName}ID`],
                        propertyID: row.propertyID,
                        propertyName: row.propertyName,
                        debtorID: row.debtorID,
                        debtorName: row.debtorName,
                        leaseID: row.leaseID,
                        leaseName: row.leaseName,
                        crn: row.crn,
                        category: row.category,
                    });
                }
            }
            $scope.txn_temp_tbl.datas[key].leaseDrop = rows;
        };
        $scope.propertyChangeTempDrop = function (key) {
            $scope.populateLeaseTempDrop(key);
            $scope.txn_temp_tbl.datas[key].propertyName = $scope.getDropDetails(key, 'property').propertyName;
            $scope.txn_temp_tbl.datas[key].leaseID = '';
            $scope.txn_temp_tbl.datas[key].leaseName = '';
            $scope.txn_temp_tbl.datas[key].debtorID = '';
            $scope.txn_temp_tbl.datas[key].debtorName = '';
            $scope.txn_temp_tbl.datas[key].useLeaseID = '';
            $scope.txn_temp_tbl.datas[key].useLeaseName = '';
            $scope.txn_temp_tbl.datas[key].crn = '';
            $scope.txn_temp_tbl.datas[key].useLeaseID = '';
        };
        $scope.debtorChangeTempDrop = function (key) {
            $scope.txn_temp_tbl.datas[key].debtorName = $scope.getDropDetails(key, 'debtor').debtorName;
        };
        $scope.leaseChangeTempDrop = function (key) {
            const leaseDetails = $scope.getDropDetails(key, 'lease', 'property');
            $scope.txn_temp_tbl.datas[key].crn = leaseDetails.crn;
            $scope.txn_temp_tbl.datas[key].leaseName = leaseDetails.leaseName;
            $scope.txn_temp_tbl.datas[key].propertyID = leaseDetails.propertyID;
            for (let i = 0; i < $scope.txn_temp_tbl.datas[key].matches.length; i++) {
                const row = $scope.txn_temp_tbl.datas[key].matches[i];
                if (
                    row.leaseID == $scope.txn_temp_tbl.datas[key].leaseID &&
                    row.propertyID == $scope.txn_temp_tbl.datas[key].propertyID
                ) {
                    $scope.txn_temp_tbl.datas[key].useLeaseID = `${row.leaseID}{|}${row.propertyID}`;
                    break;
                }
            }
            if ($scope.txn_temp_tbl.datas[key].useLeaseID != '')
                $scope.txn_temp_tbl.datas[key].useLeaseName =
                    `(${leaseDetails.propertyID}) ${leaseDetails.leaseID} ${leaseDetails.leaseName}`;

            $scope.txn_temp_tbl.datas[key].debtorID = leaseDetails.debtorID;
            $scope.txn_temp_tbl.datas[key].debtorName = $scope.getDropDetails(key, 'debtor').debtorName;
        };
        $scope.getDropDetails = function (key, drop, drop2) {
            let details = {};
            const dropList = $scope.txn_temp_tbl.datas[key][`${drop}Drop`];
            for (let i = 0; i < dropList.length; i++) {
                if (!drop2) {
                    if ($scope.txn_temp_tbl.datas[key][`${drop}ID`] == dropList[i].value) {
                        details = dropList[i];
                        break;
                    }
                } else {
                    if (
                        $scope.txn_temp_tbl.datas[key][`${drop}ID`] == dropList[i].value &&
                        $scope.txn_temp_tbl.datas[key][`${drop2}ID`] == dropList[i][`${drop2}ID`]
                    ) {
                        details = dropList[i];
                        break;
                    }
                }
            }
            return details;
        };
        $scope.filterTempDropDowns = function (by, scp) {
            return function (item) {
                if (item[by] == scp || !item[by]) {
                    return true;
                }
                return false;
            };
        };
        $scope.deleteTxnTransMultiple = function (transDate) {
            const listGrp = $scope.file_trans_list_grp[transDate];
            const transIDs = [];
            for (let i = listGrp.datas.length - 1; i >= 0; i--) {
                const row = listGrp.datas[i];
                if (row.ch) {
                    transIDs.push(row.file_trans_id);
                }
            }
            const params = {
                file_trans_id: JSON.stringify(transIDs),
                multiple: true,
            };
            htmler.request('AR/deleteFileNoReceiptTrans', params).then(function (data) {
                if (data.deleted) {
                    htmler.alert($scope, 'success', ['file transaction(s) successfully deleted'], 5000);
                    $scope.loadFileNoReceiptTrans();
                } else {
                    htmler.alert($scope, 'error', ['Delete failed. Please try again'], 5000);
                }
                $scope.clearBankReceiptChargesView();
            });
        };
        $scope.deleteTxnTrans = function (key) {
            const file_trans = $scope.file_trans_list[key];
            const params = {
                file_trans_id: file_trans.file_trans_id,
            };
            htmler.request('AR/deleteFileNoReceiptTrans', params).then(function (data) {
                if (data.deleted) {
                    htmler.alert($scope, 'success', ['file transaction successfully deleted'], 5000);
                    $scope.loadFileNoReceiptTrans();
                } else {
                    htmler.alert($scope, 'error', ['Delete failed. Please try again'], 5000);
                }
                $scope.clearBankReceiptChargesView();
            });
        };
        $scope.tempLineconfirm = function (key, val) {
            if (val) {
                $scope.txn_temp_tbl.datas[key].edit = false;
                $scope.txn_temp_tbl.datas[key].ch = true;
            } else {
                $scope.txn_temp_tbl.datas[key].edit = true;
                $scope.txn_temp_tbl.datas[key].ch = false;
            }
            $scope.tempTxnFileCheckWatch();
        };
        $scope.filterTxnTempTbl = function () {
            const filters = $scope.txn_temp_tbl.filters;
            // SET ROW KEY TO SEARCH FOR KEYWORD
            const wordsKey = ['trans_desc1', 'trans_desc2'];
            angular.forEach($scope.txn_temp_tbl.datas, function (row, key) {
                let wordFound = true;
                if (filters) {
                    if (filters.hasOwnProperty('keyword'))
                        wordFound = htmler.matchRegex(row, wordsKey, filters.keyword);
                }
                if (wordFound) row.srchShow = true;
                else row.srchShow = false;
            });
        };
        $scope.clearKeywordFilterTxnTempTbl = function () {
            $scope.txn_temp_tbl.filters.keyword = '';
            $scope.filterTxnTempTbl();
        };
        $scope.filterTxnDateListTbl = function (transDate) {
            const filters = $scope.file_trans_list_grp[transDate].filters;
            // SET ROW KEY TO SEARCH FOR KEYWORD
            const wordsKey = ['description'];
            angular.forEach($scope.file_trans_list_grp[transDate].datas, function (row, key) {
                let wordFound = true;
                if (filters) {
                    if (filters.hasOwnProperty('keyword'))
                        wordFound = htmler.matchRegex(row, wordsKey, filters.keyword);
                }
                if (wordFound) row.srchShow = true;
                else row.srchShow = false;
            });
        };
        $scope.clearKeywordFilterTxnDateListTbl = function (transDate) {
            $scope.file_trans_list_grp[transDate].filters = '';
            $scope.filterTxnDateListTbl(transDate);
        };
        $scope.submitForm = function (nextFile) {
            angular.element(document.querySelector('#vacated-warning-modal')).removeClass('show');
            angular.element(document.querySelector('#future-warning-modal')).removeClass('show');
            angular.element(document.querySelector('.htmler-backgrounder')).remove();
            let valid = true;
            const msgs = [];

            const lastReconDate = moment($scope.lastReconDate, 'DD/MM/YYYY');
            const recDate = moment($scope.rec_date, 'DD/MM/YYYY');
            const today = moment();

            angular.element('#input-cheque-modal').removeClass('show');
            angular.element('#input-prn-modal').removeClass('show');
            let file_trans_parsed = {};
            if ($scope.file_trans_list[$scope.file_trans_key]) {
                file_trans_parsed = JSON.parse(JSON.stringify($scope.file_trans_list[$scope.file_trans_key]));
                delete file_trans_parsed.debtorDrop;
                delete file_trans_parsed.propertyDrop;
                delete file_trans_parsed.leaseDrop;
                delete file_trans_parsed.lastData;
                file_trans_parsed.leaseID = file_trans_parsed.lease_code;
                file_trans_parsed.propertyID = file_trans_parsed.property_code;
                file_trans_parsed.debtorID = file_trans_parsed.debtor_code;
            }

            if (recDate.isValid() && recDate <= lastReconDate) {
                valid = false;
                msgs.push('You cannot post a receipt on or before the last bank reconciliation date');
            }
            if (!$scope.payment_type) {
                valid = false;
                msgs.push('You must choose a payment type');
            } else {
                let foundPtype = false;
                for (let i = $scope.payment_type_list.length - 1; i >= 0; i--) {
                    if ($scope.payment_type_list[i].value == $scope.payment_type) {
                        foundPtype = true;
                        break;
                    }
                }
                if (!foundPtype) {
                    valid = false;
                    msgs.push('You must choose a payment type');
                }
            }
            if (!isNaN(parseFloat($scope.rec_amount)) && parseFloat($scope.rec_amount) < 0) {
                // valid = false;
                // msgs.push('Entered amount is invalid. Amount must not be negatives');
            } else {
                const amt = parseFloat($scope.rec_amount) * 100;
                if (amt > 0) {
                    const amtRnd = Math.round(amt);
                    if ($scope.payment_type == 'CSH' && amtRnd % 5 != 0) {
                        valid = false;
                        msgs.push('You have to use multiples of 5 cents if you are using Cash Payment');
                    }
                }
            }
            if ($scope.apply_bal != 0) {
                valid = false;
                msgs.push(
                    'You havent applied the full amount specified against transactions. You may adjust the total amount to reflect the sum of the allocations or alternatively apply the remaining amount as unallocated. Please review the amount to match the total allocated, or apply the amount as unallocated cash',
                );
            }

            angular.forEach($scope.rec_list, function (row) {
                if (!isNaN(parseFloat(row.line_amount)) && parseFloat(row.line_amount) != 0) {
                    if (row.transactionType == 'CSH' && $scope.unallocList.length > 0) {
                        valid = false;
                        msgs.push(
                            'You cannot receipt new unallocated cash and take up existing unallocated cash at the same time.',
                        );
                        return false;
                    }
                }
            });

            angular.forEach($scope.unallocList, function (row) {
                if (!row.fromDate || typeof row.fromDate === 'undefined') {
                    valid = false;
                    msgs.push('Please enter From Date of Unallocated Cash');
                    return false;
                }

                if (!row.toDate || typeof row.toDate === 'undefined') {
                    valid = false;
                    msgs.push('Please enter To Date of Unallocated Cash');
                    return false;
                }
            });

            let refund = 1;
            let refund_method = 0;
            angular.forEach($scope.rec_list, function (row, key) {
                if (!isNaN(parseFloat(row.line_amount)) && parseFloat(row.line_amount) != 0) {
                    if (row.transactionType == 'INV' && row.invoiceNumber == '0') {
                        valid = false;
                        msgs.push(
                            `Line #${
                                key + 1
                            } An amount cannot be applied to an invoice that hasn't been assigned an invoice number.`,
                        );
                    } else if (row.transactionType == 'CRE' && row.invoiceNumber == '0' && row.trans_amt != 0) {
                        valid = false;
                        msgs.push(
                            `Line #${
                                key + 1
                            } An amount cannot be applied to a credit that hasn't been assigned an invoice number.`,
                        );
                    } else if (row.transactionType == 'INV') {
                        refund = 0;
                    }

                    refund_method = row.method;
                }
            });

            if ($scope.rec_amount == '' || $scope.rec_amount == 0) {
                refund = 0;
            }

            if ($scope.rec_amount < 0 && refund_method == 3 && !$scope.cheque_number) {
                angular.element('#input-cheque-modal').addClass('show');
                return false;
            }

            if ($scope.rec_amount < 0 && refund_method == 1 && !$scope.prn_number) {
                angular.element('#input-prn-modal').addClass('show');
                return false;
            }

            if (Object.keys(file_trans_parsed).length > 0 && !file_trans_parsed.debtorID) {
                valid = false;
                msgs.push('Incomplete line details. No debtor found');
            }
            // if(Object.keys(file_trans_parsed).length > 0 && !file_trans_parsed.debtorOnly && !file_trans_parsed.propertyID){
            // 	valid = false;
            // 	msgs.push('Incomplete TXN details. No property found');
            // }

            switch ($scope.payment_type) {
                case 'CHQ':
                    if (!$scope.chq_drawer_name) {
                        valid = false;
                        msgs.push('You have not entered a drawer name');
                    } else if ($scope.chq_drawer_name.length > 40) {
                        valid = false;
                        msgs.push('Drawer Name character length is too long, must only contain up to 40 characters');
                    }
                    if (!$scope.chq_number || !/^[0-9]+$/.test($scope.chq_number)) {
                        valid = false;
                        msgs.push('You have not entered a valid cheque number');
                    } else if ($scope.chq_number.length > 20) {
                        valid = false;
                        msgs.push('You have entered an invalid cheque number, must only contain up to 20 characters');
                    }
                    if (!$scope.chq_bank_id) {
                        valid = false;
                        msgs.push('You have not selected a valid bank');
                    }
                    if (!$scope.chq_bsb && $scope.general_cdf && $scope.general_cdf.display_bsb) {
                        valid = false;
                        msgs.push(`You have not entered a valid ${$scope.general_cdf.bsb_label} number`);
                    } else {
                        if (
                            !$filter('htmlerCheckBSB')($scope.chq_bsb) &&
                            $scope.general_cdf &&
                            $scope.general_cdf.display_bsb
                        ) {
                            valid = false;
                            msgs.push(`You have not entered a valid ${$scope.general_cdf.bsb_label} number`);
                        }
                    }
                    if ($scope.pay_name.length > 200) {
                        valid = false;
                        msgs.push('Payer Name character length is too long, maximum of 200 characters only');
                    }
                    if ($scope.pay_add.length > 200) {
                        valid = false;
                        msgs.push('Payer Address character length is too long, maximum of 200 characters only');
                    }
                    if ($scope.pay_suburb.length > 60) {
                        valid = false;
                        msgs.push('Payer Suburb character length is too long, maximum of 60 characters only');
                    }
                    if ($scope.pay_state.length > 50) {
                        valid = false;
                        msgs.push('Payer State character length is too long, maximum of 50 characters only');
                    }
                    break;
                case 'DIR':
                    if (!$scope.dir_depo_name) {
                        valid = false;
                        msgs.push('You have not entered a drawer name');
                    } else if ($scope.dir_depo_name.length > 40) {
                        valid = false;
                        msgs.push('Direct Deposit Name character length is too long');
                    }
                    break;
            }

            if ($scope.rec_no) {
                const numbers = /^[0-9]+$/;
                if (!$scope.rec_no.match(numbers)) {
                    valid = false;
                    msgs.push('You have not entered a valid Receipt Number');
                }
            }

            if (valid && !$scope.isCheckFuture) {
                if (recDate.isValid() && recDate > today) {
                    $scope.vacatedsNextFile = nextFile;
                    $scope.isCheckFuture = true;
                    angular.element('#future-warning-modal').addClass('show');
                    angular
                        .element('html')
                        .append(
                            '<div class="htmler-backgrounder" style="position:absolute;top:0;bottom:0;left:0;right:0;background-color:rgba(0, 0, 0, 0.2);"></div>',
                        );
                    return false;
                }
            }

            // CHECK IF THERE ARE VACATED LEASES
            if (valid && !$scope.isCheckVacated) {
                angular.forEach($scope.rec_list, function (row, key) {
                    if (row.leaseStatus != 'C' && $scope.vacateds.indexOf(row.leaseID) == -1) {
                        $scope.vacateds.push(row.leaseID);
                    }
                });
                if ($scope.vacateds.length > 0) {
                    $scope.vacatedsNextFile = nextFile;
                    $scope.isCheckVacated = true;
                    angular.element('#vacated-warning-modal').addClass('show');
                    angular
                        .element('html')
                        .append(
                            '<div class="htmler-backgrounder" style="position:absolute;top:0;bottom:0;left:0;right:0;background-color:rgba(0, 0, 0, 0.2);"></div>',
                        );
                    return false;
                }
            }

            if (valid) {
                $scope.vacatedsNextFile = false;
                const params = {
                    leaseOption: $scope.refineSrchList.length,
                    propertyID: $scope.selected.propertyID,
                    leaseID: $scope.selected.leaseID,
                    bankID: $scope.selected.bankID,
                    debtorID: $scope.selected.debtorID,
                    lastReconDate: $scope.lastReconDate,
                    payment_type: $scope.payment_type,
                    pay_name: $scope.pay_name,
                    pay_add: $scope.pay_add,
                    pay_suburb: $scope.pay_suburb,
                    pay_state: $scope.pay_state,
                    grp_depo_slip: $scope.grp_depo_slip,
                    dir_depo_name: $scope.dir_depo_name,
                    chq_drawer_name: $scope.chq_drawer_name,
                    chq_number: $scope.chq_number,
                    chq_bank_id: $scope.chq_bank_id,
                    chq_bsb: $scope.chq_bsb,
                    rec_date: $scope.rec_date,
                    bank_date: $scope.bank_date,
                    rec_amount: $scope.rec_amount,
                    rec_no: $scope.rec_no,
                    clearance_days: $scope.clearance_days,
                    clearance_date: $scope.clearance_date,
                    rec_list: JSON.stringify($scope.rec_list),
                    unallocList: JSON.stringify($scope.unallocList),
                    cheque_number: $scope.cheque_number,
                    refund: refund && $scope.unallocList.length == 0 ? 1 : 0,
                    refund_payment_method: refund_method,
                    prn_number: $scope.prn_number,
                    is_ledger: $scope.leaseLabel,
                };
                if (Object.keys(file_trans_parsed).length > 0) {
                    params.file_trans = JSON.stringify(file_trans_parsed);
                }

                htmler.request('AR/processReceiptForm', params).then(function (data) {
                    if (data.errors && data.errors.length > 0) {
                        htmler.alert($scope, 'error', data.errors, 10000);
                        if (data.clear_cheque) $scope.cheque_number = '';
                        $scope.prn_number = '';
                    } else {
                        if (data.generateInvoice) {
                            params.generateInvoiceBatchNo = data.generateInvoiceBatchNo;
                            params.generateInvoiceBatchData = data.generateInvoiceBatchData;
                            document.getElementById('download_link_div').innerHTML =
                                '<img src="assets/images/loader.gif"> Generating download link/s...';
                            $('#main_container').scrollTop(0);
                            $.ajax({
                                data: params,
                                type: 'POST',
                                url: '?action=generateSundryRemainder&module=ar&command=generateTaxInvoice',
                                success: function (data) {
                                    res = JSON.parse(data);
                                    //console.log(res.downloadPath,'download');
                                    $scope.downloadSundry = '';
                                    for (let i = 0; i < res.downloadPath.length; i++) {
                                        $scope.downloadSundry =
                                            `${
                                                $scope.downloadSundry
                                            }<div class="infoBox" ><img src="assets/images/icons/pdf.png" alt="Adobe Logo">&nbsp;<a  href="` +
                                            `download.php?fileID=${
                                                res.downloadPath[i]
                                            }"  ><b>Download in printable format (PDF file)</b> left click to download or view</a></div>`;
                                    }
                                },
                            });
                        }

                        if ($scope.print_receipt) {
                            $('#loading').css('display', 'block');
                            params.print_receipt = true;
                            params.email_receipt = $scope.email_receipt ? 1 : 0;
                            params.batchNumber = data.batchNo;
                            params.lineNumber = data.lineNo;
                            params.receiptNumber = data.receiptNo;
                            $.ajax({
                                data: params,
                                type: 'POST',
                                url: '?action=generate&module=ar&command=receiptsPrinting',
                                success: function (data) {
                                    res = JSON.parse(data);
                                    $scope.downloadReceipt =
                                        `<div class="infoBox" ><img src="assets/images/icons/pdf.png" alt="Adobe Logo">&nbsp;<a  href="` +
                                        `download.php?fileID=${
                                            res.downloadPath
                                        }"  ><b>Download in printable format (PDF file)</b> left click to download or view</a></div>`;
                                    $('#loading').css('display', 'none');
                                },
                            });
                        }

                        if (data.fileLink) {
                            $('#loading').css('display', 'block');
                            $.ajax(data.fileLink, {
                                complete: function (file) {},
                                success: function (file) {
                                    $('#fileDownload').empty().append(file);
                                    $('#loading').css('display', 'none');
                                },
                                type: 'post',
                                data: params,
                            });
                        }

                        htmler.alert($scope, 'success', ['Receipt successfully Processed.'], 5000);

                        if ($scope.file_trans_list[$scope.file_trans_key]) {
                            const usedTXNDate = $scope.file_trans_list[$scope.file_trans_key].trans_date;
                            const last_key = $scope.file_trans_key;
                            let last_grp = null;
                            let next_row = null;
                            // UPDATE THE LAST ROW TO PROCESSED
                            for (const grp in $scope.file_trans_list_grp) {
                                if ($scope.file_trans_list_grp.hasOwnProperty(grp)) {
                                    for (var i = 0; i < $scope.file_trans_list_grp[grp].datas.length; i++) {
                                        const row = $scope.file_trans_list_grp[grp].datas[i];
                                        if (row.list_key == last_key) {
                                            row.processed = true;
                                            last_grp = grp;
                                            if ($scope.file_trans_list_grp[grp].datas[i + 1]) {
                                                next_row = $scope.file_trans_list_grp[grp].datas[i + 1];
                                            }
                                            break;
                                        }
                                    }
                                }
                            }
                            if (nextFile) {
                                if (next_row) {
                                    $scope.fileMethodReInit();
                                    $scope.file_trans_key = next_row.list_key;
                                    $scope.reDatasBankReceipts(next_row.trans_date, next_row.list_key);
                                    $scope.populateReceiptCharges($scope.file_trans_key);
                                } else {
                                    //FIND NEXT GRP
                                    let next_grp = null;
                                    let use_next = false;
                                    let indexI = 0;
                                    let activeIndex = null;
                                    for (const grp in $scope.file_trans_list_grp) {
                                        if (use_next) {
                                            next_grp = grp;
                                            activeIndex = indexI;
                                            break;
                                        }
                                        if (last_grp == grp) {
                                            use_next = true;
                                        }
                                        indexI++;
                                    }
                                    if (next_grp && $scope.file_trans_list_grp[next_grp]) {
                                        const row = $scope.file_trans_list_grp[next_grp].datas[0];
                                        $scope.fileMethodReInit();
                                        $scope.file_trans_key = row.list_key;
                                        $scope.populateReceiptCharges($scope.file_trans_key);
                                        $scope.activedion = activeIndex;
                                    } else {
                                        $scope.activedion = 0;
                                        let use_zero = false;
                                        const zero_row = $scope.file_trans_list[0];
                                        for (
                                            var i = 0;
                                            i < $scope.file_trans_list_grp[zero_row.trans_date].datas.length;
                                            i++
                                        ) {
                                            const row = $scope.file_trans_list_grp[zero_row.trans_date].datas[i];
                                            if (row.list_key == 0 && !row.processed && row.debtor_code) {
                                                use_zero = true;
                                                break;
                                            }
                                        }
                                        if (use_zero) {
                                            $scope.fileMethodReInit();
                                            $scope.file_trans_key = 0;
                                            $scope.populateReceiptCharges($scope.file_trans_key);
                                        } else {
                                            $scope.file_trans_key = null;
                                            $scope.fileMethodReInit();
                                            //$scope.loadFileNoReceiptTrans();
                                            $scope.clearFileTransGrp();
                                        }
                                    }
                                }
                            } else {
                                $scope.file_trans_key = null;
                                $scope.fileMethodReInit();
                                //$scope.loadFileNoReceiptTrans();
                                $scope.clearFileTransGrp();
                            }
                        } else {
                            // if($scope.method == "crn")
                            // $scope.loadSearchDetails("crnSrch");
                            // else
                            // $scope.loadSearchDetails();
                            // // $scope.onReady(true);

                            //clear
                            $scope.method_id = '';
                            if ($scope.method != 'invoice') $scope.invoiceShow = false;
                            if ($scope.method != 'crn') $scope.crnShow = false;
                            $scope.txnShow = false;
                            $scope.refineSrchList = [];
                            $scope.debtor_id = '';
                            $scope.lease_id = '';
                            $scope.property_id = '';
                            $scope.pay_add = '';
                            $scope.pay_name = '';
                            $scope.pay_state = '';
                            $scope.pay_suburb = '';
                            $scope.rec_no = '';
                            $scope.crn_no = '';
                            $scope.rec_list = [];
                            $scope.allOptions = [];
                            $scope.leaseDebtorsSrchShow = false;
                            $scope.leasesSrchShow = false;
                            $scope.propertiesSrchShow = false;
                            $scope.file_trans_list = [];
                            $scope.file_trans_key = null;
                            $scope.cheque_number = '';
                            $scope.prn_number = '';
                            $scope.txnDlShow = false;

                            // $scope.method_properties_id = "";
                            // $scope.method_leases_id = "";
                            // $scope.method_leaseDebtors_id = "";
                            // $scope.method_propertyManagers_id = "";
                            // $scope.invoice_no = "";
                            // $scope.method_drawers_id = "";
                            // $scope.method_efts_id = "";

                            if ($scope.dropsCodes && $scope.dropsCodes.length > 0) {
                                for (var i = 0; i < $scope.dropsCodes.length; i++) {
                                    const drop = $scope.dropsCodes[i];
                                    if ($scope[`method_${drop}_id`]) {
                                        $scope[`method_${drop}_id`] = '';
                                    }
                                }
                            }
                        }
                    }
                });
            } else {
                htmler.alert($scope, 'error', msgs, 10000);
            }
        };
        $scope.downloadTxnFile = function () {
            const date = moment($scope.txn_file_date, 'DD/MM/YYYY').format('YYYY-MM-DD');
            const bank = $scope.bank_acct_id;
            const bank_details = {};
            const errors = [];
            if (!date) {
                errors.push('Please select a business date');
            }
            if (!bank) {
                errors.push('Please select a bank account');
            }
            const checkDate = moment($scope.txn_file_date, 'DD/MM/YYYY').weekday();
            if (checkDate == 6 || checkDate == 0) {
                errors.push('Statement Download is only available for business days, not weekends');
            }
            if (errors.length == 0) {
                const params = {
                    date: date,
                    bank: bank,
                    fileRead: true,
                    download: true,
                };
                /*
    		htmler.request('AR/txnFileProcess',params).then(function(data){
				if(data.trans.length > 0){
	            	angular.forEach(data.trans,function(row,key){
	            		row.line_key = key;
	            		if(row.matches.length > 1){
	            			row.useLeaseID = row.leaseID+'{|}'+row.propertyID;
	            			var list = [];
	            			list.push({leaseID:'',leaseName:'',propertyID:'',value:'',label:'Select Suggestion'});
	            			angular.forEach(row.matches,function(rw,ctr){
	            				rw.label = '('+rw.propertyID+') '+rw.leaseID+' '+rw.leaseName;
	            				rw.value = rw.leaseID+'{|}'+rw.propertyID;
	            				list.push(rw);
	            			});
	            			row.matches = list;
	            		}
	            		row.srchShow = true;
                		row.ch = false;
                		if(row.matches.length == 1)
                			row.ch = true;
                		row.edit = false;
                		if(row.matches.length == 0 || row.matches.length > 1)
	                		row.edit = true;

	                	row.debtorDrop = [];
	                	row.leaseDrop = [];
	                	row.propertyDrop = [];
	            	});
	            	$scope.txnTempBankDetail = data.bank_detail;
	            	$scope.txnTempTransDate = data.trans_date;
	            	$scope.txn_temp_list = data.trans;
	            	$scope.txn_temp_tbl.datas = data.trans;
	            	$scope.txn_temp_tbl.curPage = 0;
	            	$scope.tempTxnFileCheckWatch();
	            	$scope.txn_file = data.file;
	            	$scope.temp_drop_list = data.options;
	            	$scope.populateTempDropDowns();
	            	angular.element('#download-txn-modal').removeClass('show');
	            	angular.element('#txn-temp-modal').addClass('show');
	            	angular.element('html').append('<div class="htmler-backgrounder" style="position:absolute;top:0;bottom:0;left:0;right:0;background-color:rgba(255, 255, 255, 0.8);"></div>');
	        	}
	        */
                htmler.request('AR/saveTempTxnFileTrans', params).then(function (data) {
                    // console.log(data);
                    htmler.alert(
                        $scope,
                        'success',
                        ['Confirmed matches between bank statement and cirrus8 have been successful.'],
                        5000,
                    );
                    $scope.loadFileNoReceiptTrans(data.trans_date);
                    if (data.errors.length > 0) {
                        htmler.alert($scope, 'error', data.errors, 5000);
                    }
                });
            } else htmler.alert($scope, 'error', errors, 5000);
        };
        $scope.saveSubLedger = function () {
            if (!$scope.unalloc_new_propertyID) {
                htmler.alert($scope, 'error', ['No property selected.'], 5000);
                return false;
            }

            if (!$scope.newSubLedgerCode) {
                htmler.alert($scope, 'error', ['You have not entered a valid code.'], 5000);
                return false;
            }

            if (!$scope.newSubLedgerName) {
                htmler.alert($scope, 'error', ['You have not entered a valid name.'], 5000);
                return false;
            }

            if (!$scope.newSubLedgerCompany) {
                htmler.alert($scope, 'error', ['You have not selected a valid company	.'], 5000);
                return false;
            }

            if (!$scope.newSubLedgerType) {
                htmler.alert($scope, 'error', ['You have not selected a valid type.'], 5000);
                return false;
            }

            const params = {
                property_code: $scope.unalloc_new_propertyID,
                sub_ledger_code: $scope.newSubLedgerCode,
                sub_ledger_name: $scope.newSubLedgerName,
                company_code: $scope.newSubLedgerCompany,
                sub_ledger_type: $scope.newSubLedgerType,
                form_mode: 1,
            };
            htmler.request('sales-trust/ledger/update-or-create-sub-ledger', params).then(function (data) {
                if (data.status == 'success') {
                    htmler.alert($scope, 'success', ['Successfully added.'], 10000);
                    angular.element('#sub-Ledger-details-modal').removeClass('show');
                    angular.element('.htmler-backgrounder').remove();

                    $scope.lease_id = $scope.newSubLedgerCode;
                    $scope.loadSearchDetails('leaseSrch');

                    $scope.newSubLedgerCode = '';
                    $scope.newSubLedgerName = '';
                    $scope.newSubLedgerCompany = '';
                    $scope.newSubLedgerType = '';

                    setTimeout(function () {
                        $scope.showAddNewUnallocRow();
                    }, 2000);
                } else {
                    htmler.alert($scope, 'error', [data.validation_errors], 10000);
                }
            });
        };
        $scope.uploadImportFile = function () {
            const params = {
                txn: $scope.import_file,
                fileRead: true,
                bpayFile: $scope.method,
                bank: $scope.bank_acct_id,
            };

            htmler.request('AR/saveTempTxnFileTrans', params).then(function (data) {
                htmler.alert(
                    $scope,
                    'success',
                    ['Confirmed matches between bank statement and cirrus8 have been successful.'],
                    5000,
                );
                $scope.loadFileNoReceiptTrans(data.trans_date);
                if (data.errors.length > 0) {
                    htmler.alert($scope, 'error', data.errors, 5000);
                }
                // $scope.import_file = null
                document.getElementById('import_file').value = '';
            });
        };
        $scope.receiptingNoteLoadModal = function (data) {
            const noteID = data.id;
            const itemRef = `note-${noteID}`;
            const desc = data.desc;
            const property = data.propertyID;
            const lease = data.leaseID;
            const timestamp = data.date;
            const user = data.by;

            $scope.receiptingNote = {
                id: noteID,
                desc: desc,
                property: property,
                lease: lease,
                timestamp: timestamp,
                user: user,
            };
        };
        $scope.updateNote = function (event) {
            const btn = angular.element('#update-receipting-note-btn');
            btn.text('Please wait...');
            btn.attr('disabled', true);

            const formData = htmler.httpi();
            formData.append('property_code', $scope.receiptingNote.property);
            formData.append('lease_code', $scope.receiptingNote.lease);
            formData.append('note_description', $scope.receiptingNote.desc);
            formData.append('note_id', $scope.receiptingNote.id);
            formData.append('note_heading', 'RECEIPTING');

            htmler.request('lease/update-or-create/note', formData).then(function (response) {
                console.log(response);

                if (response.status == 'Success') {
                    btn.text('Update');
                    btn.removeAttr('disabled');

                    angular.forEach($scope.rec_list_notes, function (row, key) {
                        angular.forEach(row.notes, function (note, counter) {
                            if (note.id == $scope.receiptingNote.id) {
                                note.desc = response.updated.desc;
                                note.date = response.updated.stamp;
                                note.by = response.updated.by;
                            }
                        });
                    });

                    angular.element(document.querySelector('#receipting-note-modal')).removeClass('show');
                    angular.element(document.querySelector('.htmler-backgrounder')).remove();
                }
            });
        };
        $scope.openConfirmDeleteDialog = function (data) {
            modalConfirm2(
                'Are you sure you want to delete this note?',
                '',
                function () {
                    $('#loading').css('display', 'block');
                    $('button.ui-dialog-titlebar-close.ui-confirm2-close-btn').css({
                        background: 'transparent',
                        'font-size': '20px',
                        'font-weight': 'bold',
                    });

                    const formData = htmler.httpi();
                    formData.append('note_id', data.id);
                    formData.append('property_code', data.propertyID);
                    formData.append('lease_code', data.leaseID);

                    htmler.request('lease/delete/note', formData).then(function (response) {
                        if (response.status == 'Success') {
                            angular.forEach($scope.rec_list_notes, function (row, key) {
                                angular.forEach(row.notes, function (note, counter) {
                                    if (note.id == data.id) {
                                        row.notes.splice(counter, 1);
                                    }
                                });
                            });

                            $('#loading').css('display', 'none');
                        }
                    });
                },
                null,
            );
        };
    },
]).directive('uploadFile', function (htmler) {
    return {
        restrict: 'A',
        scope: true,
        link: function (scope, element, attr) {
            element.bind('change', function () {
                if (element[0].files[0]) {
                    const params = {
                        txn: element[0].files[0],
                        fileRead: true,
                        bpayFile: scope.method,
                        bank: scope.bank_acct_id,
                    };
                    // htmler.request('AR/txnFileProcess',params).then(function(data){
                    // if(data.trans.length > 0){
                    // 	angular.forEach(data.trans,function(row,key){
                    // 		row.line_key = key;
                    // 		if(row.matches.length > 1){
                    // 			row.useLeaseID = row.leaseID+'{|}'+row.propertyID;
                    // 			row.useLeaseName = '('+row.propertyID+') '+row.leaseID+' '+row.leaseName;
                    // 			var list = [];
                    // 			list.push({leaseID:'',leaseName:'',propertyID:'',value:'',label:'Select Suggestion'});
                    // 			angular.forEach(row.matches,function(rw,ctr){
                    // 				rw.label = '('+rw.propertyID+') '+rw.leaseID+' '+rw.leaseName;
                    // 				rw.value = rw.leaseID+'{|}'+rw.propertyID;
                    // 				list.push(rw);
                    // 			});
                    // 			row.matches = list;
                    // 		}
                    // 		row.srchShow = true;
                    // 		row.ch = false;
                    // 		if(row.matches.length == 1)
                    // 			row.ch = true;
                    // 		row.edit = false;
                    // 		if(row.matches.length == 0 || row.matches.length > 1)
                    //  		row.edit = true;

                    //  	row.debtorDrop = [];
                    //  	row.leaseDrop = [];
                    //  	row.propertyDrop = [];
                    // 	});
                    // 	scope.$parent.txnTempBankDetail = data.bank_detail;
                    // 	scope.$parent.txnTempTransDate = data.trans_date;
                    // 	scope.$parent.txn_temp_list = data.trans;
                    // 	scope.$parent.txn_temp_tbl.datas = data.trans;
                    // 	scope.$parent.txn_temp_tbl.curPage = 0;
                    // 	scope.$parent.tempTxnFileCheckWatch();
                    // 	scope.$parent.txn_file = data.file;
                    // 	scope.$parent.temp_drop_list = data.options;
                    // 	scope.$parent.populateTempDropDowns();
                    // 	angular.element('#txn-temp-modal').addClass('show');
                    // 	angular.element('html').append('<div class="htmler-backgrounder" style="position:absolute;top:0;bottom:0;left:0;right:0;background-color:rgba(255, 255, 255, 0.8);"></div>');
                    // }
                    htmler.request('AR/saveTempTxnFileTrans', params).then(function (data) {
                        // console.log(data);
                        htmler.alert(
                            scope.$parent,
                            'success',
                            ['Confirmed matches between bank statement and cirrus8 have been successful.'],
                            5000,
                        );
                        scope.$parent.loadFileNoReceiptTrans(data.trans_date);
                        if (data.errors.length > 0) {
                            htmler.alert(scope.$parent, 'error', data.errors, 5000);
                        }
                        $(element[0]).val(null);
                    });
                }
            });
        },
    };
});

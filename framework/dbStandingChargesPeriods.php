<?

  //-- include and invoke the session handler
  include 'lib/classes/Session.php';
  $sess = new Session();


  //-- if ob_get_clean doesnt exist, define the function
  if (!function_exists("ob_get_clean")) {
    function ob_get_clean() {
        $ob_contents = ob_get_contents();
        ob_end_clean();
        return $ob_contents;
    }
}


  include('config.php');



  //-- invoke the database handler and create a new connection to the system database
  $dbh = new MSSQL(SYSTEMDSN);
  

  

$databases = $dbh->executeObjects("SELECT * from database_list WHERE database_id = 10001");



foreach ($databases as $db) {
	
	echo "<h1>{$db->description}</h1>";
	

	$dbh->selectDatabase($db->database_name);

	
		$dbh->executeNonQuery('DROP TABLE gl_trial_balance_2');
	$dbh->executeNonQuery("CREATE TABLE gl_trial_balance_2 (
	[accountID] [varchar](50) NULL,
	[ownerAccountID] [varchar](50) NULL,
	[year] [numeric](5, 0) NULL,
	[period] [numeric](2, 0) NULL,
	[propertyID] [varchar](50) NULL,
	[balanceCash] [numeric](18, 2) NULL DEFAULT 0.00,
	[balanceCashFX] [numeric](18, 2) NULL DEFAULT 0.00,
	[balanceAccruals] [numeric](18, 2) NULL DEFAULT 0.00,
	[balanceAccrualsFX] [numeric](18, 2) NULL DEFAULT 0.00,
	[openingBalanceCash] [numeric](18, 2) NULL  DEFAULT 0.00,
	[openingBalanceAccruals] [numeric](18, 2) NULL  DEFAULT 0.00,
	[preLastYear] [numeric](18, 2) NULL  DEFAULT 0.00,
	[lastYearOpeningBalance] [numeric](18, 2) NULL DEFAULT 0.00,
	[thisYearOpeningBalance] [numeric](18, 2) NULL DEFAULT 0.00,
	[postThisYearActivity] [numeric](18, 2) NULL DEFAULT 0.00,
	[trialBalanceID] [int] IDENTITY(1,1) NOT NULL
)");
	
	
	$sql = "SELECT g.* FROM gl_transaction g";
	$transactions = $dbh->executeObjects($sql);
	$i = 0;
	if ($transactions) foreach ($transactions as $t) {
	  $i++;
		//$sql = "UPDATE gl_transaction SET year = '{$t->artr_year}', period='{$t->artr_period}' WHERE transaction_id = '{$t->transaction_id}'";
		//print_r($t); echo '<br/>';
		//echo "{$sql}<br/>";
		//$dbh->executeNonQuery($sql);
		
		$node = null;
		switch ($t->method) {
		    case METHOD_ACCRUALS : $node = 'balanceAccruals'; break;
		    case METHOD_CASH : $node = 'balanceCash'; break;
		}
		
		if (strlen($t->account_id) == 3) $t->account_id = "0{$t->account_id}";
		
		if ($node) {
		  $existing = $dbh->executeSingle("SELECT * FROM gl_trial_balance_2 WHERE accountID = '{$t->account_id}' AND year='{$t->year}' AND period = '{$t->period}' AND propertyID = '{$t->property_id}'");
		  if ($existing) {
		    $sql = "UPDATE gl_trial_balance_2 SET {$node} = {$node} + {$t->transaction_amount} WHERE accountID = '{$t->account_id}' AND year='{$t->year}' AND period = '{$t->period}' AND propertyID = '{$t->property_id}'";
		  } else {
		    $sql = "INSERT INTO gl_trial_balance_2 (propertyID,accountID,year,period,{$node}) VALUES ('{$t->property_id}','{$t->account_id}', '{$t->year}','{$t->period}','{$t->transaction_amount}')";
		  }
		//  echo "{$sql}<br/>";
		  $dbh->executeNonQuery($sql);
		}
	}
	
	echo $i . ' transactions<br/>';
	
	
}




?>
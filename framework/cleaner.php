<?php

if (!function_exists('file_put_contents')) {
    function file_put_contents($filename, $data) {
        $f = @fopen($filename, 'w');
        if (!$f) {
            return false;
        } else {
            $bytes = fwrite($f, $data);
            fclose($f);
            return $bytes;
        }
    }
}


function listFiles($from = '.', $filter = array())
{
    if(! is_dir($from))
        return false;
   
    $files = array();
    $dirs = array($from);
    while(NULL !== ($dir = array_pop($dirs)))
    {
        if($dh = opendir($dir))
        {
            while(false !== ($file = readdir($dh)))
            {
                if($file == '.' || $file == '..')
                    continue;
                $path = $dir . '/' . $file;
                if(is_dir($path))
                    $dirs[] = $path;
                else
                    $extension = end(explode('.', $path)); 
                    if (in_array($extension, $filter) && !is_dir($path))  $files[] = $path;
            }
            closedir($dh);
        }
    }
    return $files;
}

$files = listFiles(getcwd(),array('php','html'));
$count = 0;
foreach ($files as $file) {
    $content =  file_get_contents($file);
    //if ($content == trim($content)) echo $file . 'has no whitespace <br/>';
    $trimmed = trim($content);
    if ($content != $trimmed) {
        file_put_contents($file, $trimmed);
        echo 'fixed ' . $file . '...<br/>';
        $count++;
    }
}
    echo '<br/><h1>fixed ' . $count . ' files</h1>';

//print_r($files);

?>
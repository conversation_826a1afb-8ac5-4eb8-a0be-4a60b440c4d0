/* CUSTOM.JS - Common Javascript functions that are specific to the tracc project and contain references specific to it's architecture */
/* version 2 : modified the function to pass a hash as a parameter rather than a vanilla array. provides better merge and debugging support */
var finaliseToggleState = '';
var countryDefaults = {currencySymbol : '$'};

function auto_grow(element) {
    element.style.height = "5px";
    element.style.height = ((element.scrollHeight)+3)+"px";
}

function ajaxCallback(parent) {
		 $("button, input:submit, input:button").button();
}

function isCookie()
{
	var cookieEnabled = (navigator.cookieEnabled) ? true : false;

	if (typeof navigator.cookieEnabled == "undefined" && !cookieEnabled)
	{ 
		document.cookie="testcookie";
		cookieEnabled = (document.cookie.indexOf("testcookie") != -1) ? true : false;
	}
	return (cookieEnabled);
}

/**
* @param message
* @param confirmCallback
* @modified 2015-05-28: Fixed not creating modal. Fixed callback needs to take action first. [Morph]
* @modified 2015-09-16: Added dialogID to fix issue regarding multiple modal in 1 page. [Morph]
*/
var modalConfirm = function(message, confirmCallback, dialogID, modalContainer = 'body')
{
	if (!dialogID) var dialogID = '#dialog-confirm';
	else var dialogID = '#' + dialogID;
	var tag = $(dialogID);
	if (tag.length == 0) tag = $('<div id="' + dialogID + '" title=""><div class="ui-icon ui-icon-alert" style="float: left; margin: 0 7px 20px 0;"></div><div style="margin-left: 23px;">' + message + '</div></div>').appendTo(modalContainer);

	tag.dialog({
			resizable: false,
			modal: true,
			buttons: {
				'Ok': function() {
					confirmCallback ();
					$(this).dialog('close');
				},
				Cancel: function() {
					$(this).dialog('close');
				}
			}
		});

	return false;
}
var modalConfirmReason = function(message,dateDefault, confirmCallback, dialogID, modalContainer = 'body')
{
	const dateID = Math.floor(Math.random() * 100000);
	if (!dialogID) var dialogID = '#dialog-confirm';
	else var dialogID = '#' + dialogID;
	var tag = $(dialogID);
	var datepik = '<span style="white-space:nowrap;"><input type="text" size="10" style="width:70px" name="cancellationDate" id="cancelDate' + dateID + '"  class="input" value="' + dateDefault + '" onChange="smartDate(this);" />&nbsp; <input type="image" tabindex="-1" class="icon" src="' + ASSET_DOMAIN + 'assets/images/icons/calendar_view_month.png" onclick="displayDatePicker(\'cancelDate' + dateID + '\' , this); return false;" id="btn_cancelDate' + dateID + '" name="btn_cancelDate' + dateID + '"></span>';
	if (tag.length == 0) tag = $('<div id="' + dialogID + '" title=""><div class="ui-icon ui-icon-alert" style="float: left; margin: 0 7px 20px 0;"></div><div style="margin-left: 23px;">' + message + datepik + '?</div><div><br>Reason for Cancelation:<input type="text" id="cancelReason" name="cancelReason" placeholder="State your reason" maxlength=250 /></div></div>').appendTo(modalContainer);

	tag.dialog({
			resizable: false,
			modal: true,
			width:450,
			buttons: {
				'Ok': function() {
					confirmCallback ();
					$(this).dialog('close');
				},
				Cancel: function() {
					$(this).dialog('close');
				}
			}
		});

	return false;
}
var modalConfirm2 = function(message, titletext, confirmCallback, dialogID, modalContainer = 'body')
{
	if (!dialogID) var dialogID = '#dialog-confirm2';
	else var dialogID = '#' + dialogID;
	var tag = $(dialogID);
	if (tag.length == 0) tag = $('<div id="' + dialogID + '" title="">' + message + '</div></div>').appendTo(modalContainer);

	tag.dialog({
		resizable: false,
		modal: true,
		buttons: {
			'Ok': function() {
				confirmCallback ();
				$(this).dialog('close');
			},
			Close: function() {
				$(this).dialog('close');
			}
		},
		create: function(event, ui) { 
			var widget = $(this).dialog("widget");
			$(".ui-dialog-titlebar-close").addClass('ui-confirm2-close-btn');
			$(".ui-dialog-titlebar-close span", widget).remove();
			// $(".ui-dialog-titlebar-close").html('<i class="fas fa-times"></i>');
			$(".ui-dialog-titlebar-close").html('<span class="close">&times;</span>');
		 }
	});

	tag.data( "uiDialog" )._title = function(title) {
		title.html( this.options.title );
	};

	switch (titletext.toLowerCase()) {
		case 'warning':
			tag.dialog('option', 'title', '<i class="fas fa-exclamation-triangle"></i> <span style="font-size:13px;">' + titletext.toUpperCase() + '</span>');
			break;
	}

	return false;
}
var modalConfirmYesNo = function(message, confirmCallback, dialogID)
{
    if (!dialogID) var dialogID = '#dialog-confirm';
    else var dialogID = '#' + dialogID;
    var tag = $(dialogID);
    if (tag.length == 0) tag = $('<div id="' + dialogID + '" title=""><div class="ui-icon ui-icon-alert" style="float: left; margin: 0 7px 20px 0;"></div><div style="margin-left: 23px;">' + message + '</div></div>').appendTo('body');

    tag.dialog({
        resizable: false,
        modal: true,
        buttons: {
            'Yes': function() {
                confirmCallback ();
                $(this).dialog('close');
            },
            'No': function() {
                $(this).dialog('close');
            }
        }
    });

    return false;
}
var modalAlert = function(message) {
    // if (!dialogID) var dialogID = '#dialog-alert';
    // else var dialogID = '#' + dialogID;
    // var tag = $(dialogID);

    var tag = $("#modalAlert");
    if (tag.length == 0) tag = $('<div id="modalAlert"></div>').appendTo('body');

    var tag2 = $('<div id="dialog-alert" title=""><p><span class="ui-icon ui-icon-alert" style="float: left; margin: 0 7px 20px 0;"></span>' + message + '</p></div>').appendTo('body');
    tag.html(tag2);
	
	tag.dialog({
			resizable: false,
			height:150,
		width:400,
			modal: true,
			buttons: {
				"Ok": function() {
					$(this).dialog( "destroy" ).remove();
				}
			}
		});
	
	return false;
}


var modalDialog = function(url, options)
{
	options = options || {modal:true};

	var tag = $("#modal");
	if (tag.length == 0) tag = $('<div id="modal"></div>').appendTo('body');

	$.ajax({
		url: url,
		type: (options.type || 'GET'),
		beforeSend: options.beforeSend,
		error: options.error,
		data: options.data,
		complete: options.complete,
		success: function(data, textStatus, jqXHR)
		{
			if(typeof data == 'object' && data.html)
			{ //response is assumed to be JSON
				tag.html(data.html);
				tag.dialog({modal: options.modal, title: data.title}).dialog('open');
			}
			else
			{ //response is assumed to be HTML
				tag.html(data);
				tag.dialog(options).dialog('open');
			}
			$.isFunction(options.success) && (options.success)(data, textStatus, jqXHR);

			//get top offset of the #modal element
			let topPos = tag.offset().top; 
			
			//if top offset position is less than 0, dialog box's header is not in viewport
			if (topPos < 0) {
				$('.ui-dialog.ui-draggable').css('top', '0px');
			}
	  }
	});
}

var modalDialog2 = function(url, options)
{
	options = options || {modal:true};

	var tag = $("#modal2");
	if (tag.length == 0) tag = $('<div id="modal2"></div>').appendTo('body');

	$.ajax({
		url: url,
		type: (options.type || 'GET'),
		beforeSend: options.beforeSend,
		error: options.error,
		data: options.data,
		complete: options.complete,
		success: function(data, textStatus, jqXHR)
		{
			if(typeof data == 'object' && data.html)
			{ //response is assumed to be JSON
				tag.html(data.html);
				tag.dialog({modal: options.modal, title: data.title}).dialog('open');
			}
			else
			{ //response is assumed to be HTML
				tag.html(data);
				tag.dialog(options).dialog('open');
			}
			$.isFunction(options.success) && (options.success)(data, textStatus, jqXHR);
		}
	});
}


var modalDialogCenterTop = function(url, options)
{
	options = options || {modal:true};

	var tag = $("#modal");
	if (tag.length == 0) tag = $('<div id="modal"></div>').appendTo('body');

	$.ajax({
		url: url,
		type: (options.type || 'GET'),
		beforeSend: options.beforeSend,
		error: options.error,
		data: options.data,
		complete: options.complete,
		success: function(data, textStatus, jqXHR)
		{
			if(typeof data == 'object' && data.html)
			{ //response is assumed to be JSON
				tag.html(data.html);
				tag.dialog({modal: options.modal, title: data.title,position: { my: "center top", at: "center top", of: window }}).dialog('open');
			}
			else
			{ //response is assumed to be HTML
				tag.html(data);
				tag.dialog({position: { my: "center top", at: "center top", of: window }}).dialog(options).dialog('open');
			}
			$.isFunction(options.success) && (options.success)(data, textStatus, jqXHR);
		}
	});
}

var modalConfirmDelete = function(message, confirmCallback, dialogID)
{

    if (!dialogID) var dialogID = '#dialog-confirm';
    else var dialogID = '#' + dialogID;
    var tag = $(dialogID);

    if(message == 'warning_1') {
    	message = 'The invoice you are deleting has an invoice number that is currently in use, please reset the invoice number before deleting the invoice. Are you sure you want to delete this invoice?';
        var buttons = {
            "Ok": function() {
                confirmCallback ();
                $(this).dialog('close');
            },
            Cancel: function() {
                $(this).dialog('close');
            }
        };
	} else if(message == 'warning_2') {
		message = 'The invoice you are deleting has been receipted against, it should not be deleted. Are you sure you want to delete this invoice?';
        var button = '';
        var buttons = {
            "Delete Anyway": function() {
                confirmCallback ();
                $(this).dialog('close');
            },
            Cancel: function() {
                $(this).dialog('close');
            }
        };
	} else if(message == 'warning_3') {
        message = 'The invoice you are deleting has been receipted against, it should not be deleted. Are you sure you want to delete this invoice?';
        var buttons = {
            "Ok": function () {
                confirmCallback();
                $(this).dialog('close');
            },
            Cancel: function () {
                $(this).dialog('close');
            }
        };
    } else if(message == 'message_4') {
        message = 'Are you sure you want to delete this invoice?';
        var buttons = {
            "Ok": function() {
                confirmCallback ();
                $(this).dialog('close');
            },
            Cancel: function() {
                $(this).dialog('close');
            }
        };
	} else {
    	message = 'Are you sure you want to delete this invoice?';
        var buttons = {
            "Ok": function() {
                confirmCallback ();
                $(this).dialog('close');
            },
            Cancel: function() {
                $(this).dialog('close');
            }
        };
	}

    if (tag.length == 0) tag = $('<div id="' + dialogID + '" title=""><div class="ui-icon ui-icon-alert" style="float: left; margin: 0 7px 20px 0;"></div><div style="margin-left: 23px;">' + message + '</div></div>').appendTo('body');
    tag.dialog({
        resizable: false,
        modal: true,
        buttons: buttons
    });

    return false;
}

function closeDialog() { $("#modal").dialog("close"); }

function closeDialogWithTimeout() {
    var opened = $("#modal").dialog('isOpen');
    if(opened){
        setTimeout(function(){
            $("#modal").dialog('close');
        }, 2000);
    }
}


/* FUNCTION serializeContainer
* SUMMARY given an element (such as a div), extracts all form elements contained within it and adds the values (if set) to a hash
* PARAMS	container 	:  the name of the element containing the form elements
* RETURNS	results 	:  a hash containing the id <-> value pairs of data
*/
function serializeContainer(container)
{
	var results = { };

	// grab a nodelist of elements within the container
	var obj = getElementsByTagNames ('input,select,textarea', document.getElementById(container));
//$('#' + container)
	if (obj)
	{
		for (var i=0; i < obj.length; i++)
		{
			var element = obj[i];

			// if the element is a radio button or checkbox, it's selected if the 'checked' attribute is set to true
			if ((element.type == 'radio') || (element.type == 'checkbox'))
			{
				if (element.checked) results[element.name] = element.value;
				// if the multiple attribute of the element is true (valid for multiple line select boxes) ...
			}
			else if (element.multiple)
			{
				var children = element.getElementsByTagName ('option');

				// grab the children, check if they are 'selected' and build a concatenated string of values
				if (children)
				{
					var count = 0;
					var output = '';
					for (var j=0; j<children.length; j++)
					{
						var child = children[j];
						if (child.selected)
						{
							if (count == 0) { output = child.value; }
							else { output += '::' + child.value; }
							count++;
						}
					}
					if (output != '') results[element.name] = output;
				}

				//-- otherwise check if a value exists for the field (is not empty) and if so, add it to the hash
			}
			else if (element.value != '') results[element.name] = element.value;
		}
	}
	return results;
}


/* FUNCTION validateThenSubmit
 * SUMMARY given an element, valides the child form elements through validateForm and then submits the form
 * RETURNS	true 	: if it passes all validation checks
 * 			false	: if it fails any validation checks
 *@deprecate: 2012-07-30 - Might no longer be used. Tried grepping and found not being used in files. [Morph, Joe]
 */
function validateThenSubmit(form)
{
	if (validateForm(form))
	{
		form.submit ();
		return true;
	}
	else return false;
}

/*
* @deprecate: 2012-07-30 - Might no longer be used. Tried grepping and found to be used only in report~ folder. [Morph, Joe]
*/
var ajaxSubmission = function(trigger,form,div,url) {
	triggerValue = trigger.value;
	trigger.val('Saving ...');
	showDiv('submitIndicator');
	// if no URL is given, set it to post back
	if (!url) url = document.location.search;
	if (!div) div = 'content';
	toggleControl(trigger);
	if (validateForm(form)) {
		var formData = serializeContainer(form);
		formData['action'] = 'submit';
			showDiv('modaloverlay');
			StartTimer();
		var ajax = $.ajax (url , {
				complete:function(){
					hideDiv ('modaloverlay');
					toggleControl (trigger);
					StopTimer ();
				},
				success: function() { $('#'+div).html (data); },
				data: formData
				}
				);
	hideDiv('submitIndicator');
	trigger.val(triggerValue);
	return false;
   } else { trigger.val(triggerValue); toggleControl(trigger); hideDiv('submitIndicator'); return false; }

}

/*
*@deprecate: 2012-08-11 - Might no longer be used. Tried grepping and found not being used in files. [Morph]
*/
var validateThenAjax = function(form,div,url) {
  // if no URL is given, set it to post back
  //if (url === null) url = document.location.search;
   if (validateForm(form)) {
		StartTimer();
		var ajax = $.ajax (url, {complete:function(){ StopTimer(); ;},success: function() { $('#'+div).html (data); }, data: $.param(serializeContainer(div))});
	  return false;
   } else { return false; }
}

// enterKeyPostback : passed an event object, it checks to see which character code was pressed onKeyPress
// and if that character was the 'Enter' key - triggers a postback.
function enterKeyPostback(e) {
	var characterCode // literal character code will be stored in this variable
	characterCode = (typeof (e.which) != 'undefined') ? e.which : window.event.keyCode;
	if (characterCode == 13) { // if enter was pressed
		// var content = document.getElementById('content');
		StartTimer ();
		var ajax = $.ajax (document.location.search,
			{
			complete:function(){ StopTimer (); },
			success: function() { $('#content').html (data); },
			type: 'post',
			data: $.param (serializeContainer ('#content'))}
			);
		return false;
	}
	else { return true; }  //needed otherwise keypresses arent registered!
}

/*
*@deprecate: 2012-08-11 - Might no longer be used. Tried grepping and found to be used only in report~ folder. [Morph]
// tri/*
*/
function ajaxPostback () {
	showDiv ('modaloverlay');
	StartTimer ();
	var ajax = $.ajax (document.location.search , {
		success: function() { $('#content').html (data); },
		complete: function(){
			new Effect.Highlight (div);
			StopTimer ();
			hideDiv ('modaloverlay');
			},
		parameters: Form.serialize (document.postback)
		});
	return false;
}

/*
*@deprecate: 2012-08-11 - Might no longer be used. Tried grepping and found not being used in files. [Morph]
*/
var hideTips = function (className) {
	$(className).each (
		function(element) { if (element.prototip) element.prototip.hide ();
		});
}

// ajaxCall : a more generic function that can be hooked into DOM events to trigger ajax calls
// params   is a list of key-value pairs 'var=value&var=value'
// url		is the url to request
// div		is the div to be updated with content from the call
var ajaxCall = function (source, div, url, params)
{
	StartTimer ();
	if (typeof (params) == 'object') params = $.param(params);
	if (($('#' + source)) && ($('#' + source).attr('type') != 'radio')) {
		$('#'+source).attr ('disabled', 'disabled');
	}

	$.ajax(url , {
		complete: function()
		{
			StopTimer();

		},
		success: function(data) {
			$('#' + div).html(data);
			 ajaxCallback(div);
			if ($('#' + source)) $('#' + source).focus();
		},
		type: 'post',
		data: params
	});
	
//$('#' + div).scrollTop(lastScrollPos);
	return false;
}

var ajaxPeriodical = function (div, url, params) {
	if (typeof (params) == 'object') params = $(params).param ();
	new $.PeriodicalUpdater(url , {
		success: function(data) { $('#'+div).html (data);  ajaxCallback(div); },
		type: 'get',
		parameters: params,
		frequency: 2,
		decay: 1
		});
	  return false;
}

var editor = function() {
 if (window.oUtil)  {
	window.oUtil.initializeEditor("richtext",
	{
		width:"100%",
		height:"300px",
		mode : "XHTMLBody",
		useBR: true,
		btnSpellCheck: true
	}
	);
		
}
}

	var saveEditor = function() {
	
	if (window.oUtil) 
	for(var i=0;i<window.oUtil.arrEditor.length;i++)
	{
		var oEdit=eval(window.oUtil.arrEditor[i]);    
		var oEditor=eval("idContent"+oEdit.oName);

		if(oEdit.mode=="HTMLBody")sContent=oEdit.getHTMLBody();
		if(oEdit.mode=="HTML")sContent=oEdit.getHTML();
		if(oEdit.mode=="XHTMLBody")sContent=oEdit.getXHTMLBody();
		if(oEdit.mode=="XHTML")sContent=oEdit.getXHTML();
		$(oEdit.idTextArea).val(sContent);
	}
		}

var showPagi = function(pagiList,pagiNo) {
    let total = parseInt($(pagiList+" li .pagi-txt-of").text());
    let pagiPer = $(pagiList).attr('pagi-per');
    if(pagiPer && total){
	    $(pagiList+" li .pagi-txt-current").text(pagiNo);        
	    if(pagiNo > 1){
	        $(".pagi-btn.prev").show();
	    }
	    else{
	        $(".pagi-btn.prev").hide();
	    }

	    if(pagiNo < total){
	        $(".pagi-btn.next").show();
	    }
	    else{
	        $(".pagi-btn.next").hide();
	    }
	    $(pagiList).closest("table").find(".pagi-row").hide();
	    $(pagiList+" li .pagi-num").removeClass("current");
	    $(pagiList+" li #pagi-num-"+pagiNo).addClass("current");
	    if($(".pagi-row").length > pagiPer){
	        $(".pagi-row").each(function(n) {
	            if (n >= pagiPer * (pagiNo - 1) && n < pagiPer * pagiNo)
	                $(this).show();
	        });        
	    }
    }
    else{
    	$(pagiList).closest("table").find(".pagi-row").show();
    }
}
var pagiList = function(id) {
    showPagi(id,1);
    $(id+" li.pagi-btn button").click(function() {
        let current = parseInt($(id+" li .pagi-txt-current").text());
        if($(this).parent().hasClass('prev')){
            showPagi(id,current-1); 
        }
        else{
            showPagi(id,current+1); 
        }
    });              
}
// ajaxContainer : a more specialised version of ajaxCall - given a div, submits all field elements within that div and then updates it
// url is the url to request
// div is the div to be updated with content from the call
var ajaxContainer = function (source, div, url)
{

	window.StartTimer ();
	//if (typeof window.tinyMCE != 'undefined') window.tinyMCE.triggerSave ();
	var obj = $('#' + source) || false;
	if (obj)
	{
		obj.attr ('disabled', 'disabled');
		if (obj.hasClass('ui-button')) obj.button({ disabled: true });
	}
	 var lastScrollPos = $(window).scrollTop();
	$('#btnUpdate').attr ('disabled', 'disabled');
	$("#btnUpdate").addClass("ui-state-hover");
	$.ajax(url,
	{
		complete: function () {
		   if (obj) obj.focus ();
		   StopTimer();
			$('#btnUpdate').attr ('disabled', false);
			$("#btnUpdate").removeClass("ui-state-hover");
		 },
		success: function (data) {
			$('#' + div).html(data);
			ajaxCallback(div);
		},
		type: 'post',
		data: $.param (serializeContainer (div))
	});

	return false;
}

var ajaxContainerEmailSuppliersContainer = function (source, div, url)
{
	window.StartTimer ();
    //if (typeof window.tinyMCE != 'undefined') window.tinyMCE.triggerSave ();
    var obj = $('#' + source) || false;
    if (obj)
    {
        obj.attr ('disabled', 'disabled');
        if (obj.hasClass('ui-button')) obj.button({ disabled: true });
    }
    var lastScrollPos = $(window).scrollTop();

    $.ajax(url,
        {
            complete: function () {
                if (obj) obj.focus ();
                obj.attr ('disabled', false);
                StopTimer();
            },
            success: function (data) {
                $('#' + div).html(data);
                obj.attr ('disabled', false);
                modalAlert('Remittance advice has been sent to all with an email address.');
                ajaxCallback(div);
            },
            type: 'post',
            data: $.param (serializeContainer (div))
        });

    return false;
}
/*
* 2012-08-11: So far, only seen used in viewUnpaidInvoices (if testing, you can use oneOffCharge in AR) [Morph]
*/
function ajaxGroup (group, div, url) {
	var dataSet = serializeContainer (group[0]);
	for (var i = 1; i < group.length; ++i) { dataSet = $.merge (dataSet, serializeContainer (group[i])); }
	StartTimer ();
	$.ajax (url , {
		complete: function () { StopTimer (); },
		success: function (data) { $('#'+div).html (data);  ajaxCallback(div); },
		type: 'post',
		data: $.param (dataSet)
	});
	return false;
}

/*
*@deprecate: 2012-08-11 - Might no longer be used. Tried grepping and found not being used in files. [Morph]
*/
function sendContainer (div, url)
{
	StartTimer ();
	$.ajax (url, {
		complete: function () { StopTimer (); },
		type: 'post',
		data: $.param (serializeContainer (div))
		});
	return false;
}

var ajaxConfirmContainer = function(source, msg, div, url, dialogID, modalContainer = 'body')
{
	modalConfirm (msg, function () {
		ajaxContainer(source, div, url)
		}, dialogID, modalContainer);
	return false;
}
var ajaxConfirmContainerYesNo = function(source, msg, div, url, dialogID)
{
    modalConfirmYesNo (msg, function () {
        ajaxContainer(source, div, url)
    }, dialogID);
    return false;
}
var ajaxConfirmCall = function(source, msg,div,url, params) {
	modalConfirm(msg, function() { ajaxCall(source, div,url, params)});
}

var ajaxConfirmContainerReason = function(source, msg,dateDefault=null, div, url, dialogID, modalContainer = 'body')
{
	modalConfirmReason (msg,dateDefault, function () {
		ajaxContainer(source, div, url)
		}, dialogID, modalContainer);
	return false;
}

var ajaxContainers = function(source, divData, divUpdate, url)
{
	if ($('#'+source)) $('#'+source).attr ('disabled', 'disabled');
	
	StartTimer();
	$.ajax(url, {
		complete: function() {
			StopTimer();

			if ($('#'+source)) $('#'+source).focus();
			},
		success: function(data) {
			$('#'+divUpdate).html (data);
			 ajaxCallback(divUpdate);
			},
		type: 'post',
		data: $.param(serializeContainer (divData))
	});
	return false;
}

var ajaxConfirmContainerDelete = function(source, msg, div, url, dialogID)
{
    modalConfirmDelete (msg, function () {
        ajaxContainer(source, div, url)
    }, dialogID);
    return false;
}



/**
* <AUTHOR> Reyes
* @since 2015-09-16
**/
var ajaxConfirmContainers = function(source, msg, divData, divUpdate, url, dialogID)
{
	// var check = confirm (msg);
	// if (check) ajaxContainers(source, divData, divUpdate, url);
	modalConfirm (msg, function () {
		ajaxContainers(source, divData, divUpdate, url)
		}, dialogID);
	return false;
}

function toggleControl (controlName) {
	if ($('#' + controlName)) {
		if ($('#' + controlName).prop ('disabled') == true) $('#' + controlName).prop ('disabled', false);
		else $('#' + controlName).prop ('disabled', true);
		}
}

/*
*@modified 2012-08-12: Fixed error occuring in interimPayment. [Morph]
*/
var toMoney = function (amount,decimalPoint=2)
{
	if (amount) {
		amount = parseFloat(amount);
		amount = (parseFloat (amount.toFixed(decimalPoint)) == 0) ? Math.abs(amount).toFixed(decimalPoint) : amount.toFixed(decimalPoint);
		var sRegExp = new RegExp ('(-?[0-9]+)([0-9]{3})');
		while (sRegExp.test (amount)) { amount = amount.replace (sRegExp, '$1,$2'); }
		// return '$' + amount;
		return countryDefaults.currencySymbol + amount;
	}
	else return decimalPoint ? countryDefaults.currencySymbol + '0.00' : countryDefaults.currencySymbol + "0";
}

var toMoneyFormat = function (amount,decimalPoint=2,currency='$')
{
	var symbol = (currency) ? countryDefaults.currencySymbol : '';

	if (amount) {
		old = amount;
		amount = Math.abs(parseFloat(amount));
		amount = (parseFloat (amount.toFixed(decimalPoint)) == 0) ? Math.abs(amount).toFixed(decimalPoint) : amount.toFixed(decimalPoint);
		var sRegExp = new RegExp ('(-?[0-9]+)([0-9]{3})');
		while (sRegExp.test (amount)) { amount = amount.replace (sRegExp, '$1,$2'); }

		if (old < 0)
		{
			// var result = '(' + currency + amount + ')';
			var result = '(' + symbol + amount + ')';
		}
		else
		{
			// var result = currency + amount;
			var result = symbol + amount;
		}

		return result;
		}
	else return decimalPoint ? symbol + '0.00' : symbol + "0";
}

/*
 * [Enjo] - uses accounting.js library
 */
var toMoney2 = function (amount)
{
	amount = +amount;
	// alert('amount in function : ' + amount.toString());

	if (amount) {
		// return accounting.formatMoney(amount, "$", 2, ",", ".");
		return accounting.formatMoney(amount, countryDefaults.currencySymbol, 2, ",", ".");
	}
	else return countryDefaults.currencySymbol + '0.00';
}

function toggleClass (name, status) {
	$('tr_' + name).className =  ((status == true) ? 'row' : 'warning');
}


/*
function ajaxModalCall(div, url,params)
gets div, updates div with the url's request, sends to url the parameters.
modified 2012-08-13
jpalala
*/
function ajaxModalCall(div,url,params) {
	
}


/* Disables form elements given a form, and a comma delimited list of controls to leave alone :)
* 2012-08-12: Found disableForm function in bankRecReport which might conflict with this function. [Morph]
* 2012-08-12: Might no longer be actively use. Two usage are from chargeReviewProcess (leases) by admin when its only being use by client. Another usage in general (properties) but does not have an equivalent PHP file. [Morph]
*/

function disableForm (formName, disable, exceptions) {
	//elementList = document.getElementById (formName).elements;
	var elementList = getElementsByTagNames ('input,select,textarea', document.getElementById (formName));
	// convert the comma delimited string to an array indexed against the ids
	var exceptionList = new Array ();
	var checkList = new Array ();
	if (exceptions) exceptionList = exceptions.split (',');
	if (exceptionList)
	{
		for (index in exceptionList) { checkList[exceptionList[index]] = true; }
	}
	for(i in elementList)
	{
		if (!checkList[elementList[i].id])
		{
			if ((elementList[i].type == 'image') || (elementList[i].type == 'button'))
			{
				elementList[i].css ('display', (disable) ? 'none' : 'inline');
			}
			else elementList[i].disabled = disable;
		}
	}
}

var textTrunc = function(field, maxlimit, sendTo) {
	if (typeof (field) == 'string') field = $('#'+field);
	if (typeof (sendTo) == 'string') sendTo = $('#'+sendTo);
	var trunc = new String(field.value);
	var truncated = (field.value.length > maxlimit) ? trunc.substring (0, maxlimit - 1) : field.value;
	sendTo.html (truncated);
}



function textCounter(field,maxlimit) {
	if (typeof(field) == "string") field = document.getElementById(field);

var s = field.val().split("\r\n");
var tooLong = false;
for (i=0; i<s.length;i++) {
	if (s[i].length > maxlimit) tooLong = true;
}

var msg = (tooLong) ? "one of your lines is too long to print" : "";
multiMessage(field,msg,"count");

}

function limitTextarea (el, maxLines, maxChar) {
	if (!el.x) {
		el.x = uniqueInt ();
		el.onblur = function () { clearInterval(window['int'+el.x]) }
		}
	window['int'+el.x] = setInterval (function () {
		var lines = el.value.replace (/\r/g, '').split('\n');
		var i = lines.length;
		var lines_removed = 0;
		var char_removed = 0;
		if (maxLines && i > maxLines){
			alert ('You can not enter more than ' + maxLines + ' lines.');
			lines = lines.slice (0, maxLines);
			lines_removed = 1;
			}
		if (maxChar) {
			i = lines.length;
			while (i-->0) {
				if (lines[i].length > maxChar) {
					lines[i] = lines[i].slice (0, maxChar);
					char_removed = 1;
					}
				if (char_removed) alert ('You can not enter more than ' + maxChar + ' characters per line.');
				}
			}
		if (char_removed || lines_removed) $(el).val (lines.join ('\n'));
		}, 50);
}

/*
* 2012-08-12: Use in this file only. Couldn't find any other use in the HTML files. [Morph]
*/
function uniqueInt () {
	var num, maxNum = 100000;
	if (!uniqueInt.a || maxNum <= uniqueInt.a.length)uniqueInt.a = [];
	do num = Math.ceil (Math.random () * maxNum);
	while (uniqueInt.a.hasMember (num)) uniqueInt.a[uniqueInt.a.length] = num;
	return num;
	}

/*
* 2012-08-12: Use in this file only. Couldn't find any other use in the HTML files. [Morph]
Array.prototype.hasMember = function (testItem)
{
	var i = this.length;
	while (i-->0) if (testItem == this[i])return 1;
	return 0
};
*/

/*
* 2012-08-11: So far, only seen used in viewUnpaidInvoices (if testing, you can use oneOffCharge in AR) [Morph]
* @deprecate 2012-08-11 Still being use by several files but should be using toggleClass instead of this. [Morph]
* @modified 2012-08-11 This function itself can be replace and use toggleClass directly from those html files. [Morph]
*/
function toggleRow (toggle, rowID)
{
	if (typeof (rowID) == 'string') rowID = $('#' + rowID);
	$(rowID).toggleClass ('selectedRow'); // non-toggled version uses selectRow as class name. 
}

function finaliseToggle (toggle, obj)
{
	if (typeof (obj) == 'string') obj = $('#' + obj);
	if (toggle.checked) $(obj).val ('Save As Final ...');
	else $(obj).val ('Save As Draft ...');
}

var triggerValue = '';

/*
* @deprecate 2012-08-12 Only use in renderToggleSubmit (forms.php) but that function is no longer being use (last seen general in properties but is not commented out). [Morph]
*/
var ajaxFinalSubmission = function (finalCheck, trigger, form, div, url)
{
	if (typeof (finalCheck) == 'string') finalCheck = $('#' + finalCheck);
	var result = null;
	if (finalCheck.checked) result = modalConfirm("You are about to save the form and mark it as final. In doing so, you will no longer be able to edit it again. Please ensure that *all* fields are filled out prior to submitting the form");
	if (result === false) return false;
	showDiv ('submitIndicator');
	// trigger.disabled = true;
	// if no URL is given, set it to post back
	if (!url) url = document.location.search;
	if (!div) div = 'content';

	toggleControl (trigger);
	if (validateForm (form))
	{
		var formData = serializeContainer(form);
		formData["action"] = "submit";
		showDiv ('modaloverlay');
		StartTimer();
		var ajax = $.ajax(url , {
			complete: function ()
				{
				hideDiv ('modaloverlay');
				toggleControl (trigger);
				StopTimer ();
				},
			success: function (data) { $('#'+div).html (data); },
			parameters: formData
			});
		hideDiv ('submitIndicator');
		return false;
	}
	else
	{
		toggleControl (trigger);
		hideDiv ('submitIndicator');
		return false;
		}
}

/*
* 2012-08-12: Only found in ar/generateTaxInvoice [Morph]
*/
var textCount = function (source, object)
{
	var _s = source.value;
	var _t = '';
	for (_i = 0; _i < _s.length; _i++)
	{
		if (_s.charCodeAt (_i) != 13 && _s.charCodeAt (_i) != 10)
		_t += _s.charAt (_i);
	}
	source.value = _t;
	$('#' + object).html ('Length: ' + source.value.length + ' of 200');
	source.value = source.value.slice (0, 200);
}

/*
* prevent console.log errors in IE browser [jpalala]
*/
var debug = function (log_txt) {
	if (window.console != undefined) {
		console.log(log_txt);
	}
}

/**
* @param source
* @param div
* @param url
* @returns {Boolean}
* <AUTHOR> Reyes
* @since 2017-03-22
**/
var ajaxDownload = function (source, div, url)
{
	window.StartTimer ();
	var obj = $('#' + source) || false;
	if (obj)
	{
		obj.attr ('disabled', 'disabled');
		if (obj.hasClass('ui-button')) obj.button({ disabled: true });
	}
	var lastScrollPos = $(window).scrollTop();
	$.ajax(url,
	{
		complete: function () {
			if (obj) obj.focus ();
			obj.attr ('enabled', 'enabled');
			if (obj.hasClass('ui-button')) obj.button({ disabled: false });
			StopTimer();
		},
		success: function (data) {
			window.location.replace(data);
		},
		type: 'post',
		data: $.param (serializeContainer (div))
	});
	return false;
}

function fixChosenZeroWidth()
{
    $('.chosen-container').each(function(){
        if ($(this).attr('style') == 'width: 0px;')
        	$(this).attr('style','width:300px');
	})
}

var smsCount = function (source, object)
{
    var _s = source.value;
    var _t = '';
    for (_i = 0; _i < _s.length; _i++)
    {
        if (_s.charCodeAt (_i) != 13 && _s.charCodeAt (_i) != 10)
            _t += _s.charAt (_i);
    }
    source.value = _t;
    $('#' + object).html ('Length: ' + source.value.length + ' of 300');
    source.value = source.value.slice (0, 300);
}

var replaceABNDetails = function (id, field, value, type)
{
    $('#'+field).val(value);

    if (type == 'select') {
        // destroy select
        $('#'+field).trigger("liszt:updated");
        $('#'+field).trigger("chosen:updated");
    }

    $('#'+id).addClass('hidden');
    $('#'+id).next().addClass('hidden');
    
    return false;
}

var fetchCurrencySymbol = function() 
{
	$.ajax({ 
		url: '.?module=configuration&command=countryDefaults&action=currency', 
		async: false,
		success: function(response) {
			if (response) {
				countryDefaults.currencySymbol = response;
			} else {
				countryDefaults.currencySymbol = '$';
			}
		},
		error: function() {
			countryDefaults.currencySymbol = '$';
		}
	});
}

var appendCurrencySymbol = function(label)
{
	return label + ' (' + countryDefaults.currencySymbol + ')';
}

$(document).ready(function() {
	fixChosenZeroWidth();
	//fetchCurrencySymbol();
});

const image_upload_handler = (url) => (blobInfo, progress) => new Promise((resolve, reject) => {

	const formData = new FormData();
	const token  = 'Bearer ' + localStorage.getItem('Authentication');
	formData.append('file', blobInfo.blob(), blobInfo.filename());
	formData.append('app_key', sessionStorage.getItem('sso_key'));
	formData.append('user_type', localStorage.getItem('user_type'));

	fetch(url, {
		method: 'POST',
		body: formData,
		headers: {
			'Authorization': token,
		}
	})
		.then(response => response.json())
		.then(data => {
			resolve(data.location);
		})
		.catch(error => {
			reject('Image upload failed: ' + error.message);
		});



});
var mapOptions = {};
var infowindow = {};
var geocoder = {};
var map = {};
var addresses = {};

function initMap() {
	var mapType = $('input[name=mapType]:checked', '#mapContainer').val();
	var divisionID = $('#division').val();
	var propertyID = $('#propertyID').val();

	if(mapType == 7) {
		$('#propertyID').css('display', 'none');
		if($('#division').css('display') == 'none')
			$('#division').css('display', 'block');
	}
	else if(mapType == 8) {
		$('#division').css('display', 'none');
		if($('#propertyID').css('display') == 'none')
			$('#propertyID').css('display', 'block');
	}
	else {
		$('#division').css('display', 'none');
		$('#propertyID').css('display', 'none');
	}
	
	if(((mapType == 7) && (divisionID == "")) || ((mapType == 8) && (propertyID == ""))) {
		
	}
	else {
		mapOptions = {
			center: {lat: -30.833578, lng: 145.190872},
			zoom: 4
		};

		infowindow = new google.maps.InfoWindow();
		geocoder = new google.maps.Geocoder();
		map = new google.maps.Map(document.getElementById("map"), mapOptions);

		$.when(jQuery.ajax({
			url : '?module=home&command=leaseManagerMap&action=fetchAddresses',
			dataType : 'json',
			type : 'POST',
			data : { 
				mapType : mapType,
				divisionID : divisionID,
				propertyID : propertyID
			},
			success : function(response){
				addresses = {};
				addresses = response;
			},
			error : function(err){
				console.error(err);
			}
		})).done(function() { 
			if(addresses.length > 0) { 
				for (i = 0; i < addresses.length; i++) {
					loadMap(addresses[i]);
				}
			}
		});
	}
	
}

function loadMap(value) {
	var addressArray = value;
	var address = addressArray.address.replace(/ /g, '+');
	var fullAddress = addressArray.markerLabel;
	
	var reviewListMessage = '';
	if(addressArray.hasOwnProperty('reviewList')) {
		var reviewList = addressArray.reviewList;
		for(var i = 0 ; i < reviewList.length ; i++) {
			var review = reviewList[i];
			if(review.reviewComplete == '0') var reviewComplete = 'INC'; else var reviewComplete = '';
			reviewListMessage += review.reviewDate + ' ' + review.reviewDescription + ' ' + reviewComplete;
		}
	}
	
	var unitChargeListMessage = '';
	if(addressArray.hasOwnProperty('unitChargeList')) {
		var unitChargeList = addressArray.unitChargeList;
		for(var i = 0 ; i < unitChargeList.length ; i++) {
			var unitCharge = unitChargeList[i].chargeList;
			var unitChargeItemAmountPerMonth = (parseFloat(unitCharge.unitChargeItemAmount) / 12);
			unitChargeItemAmountPerMonth = unitChargeItemAmountPerMonth.toFixed(2);
			if(unitChargeList.length == 1) {
				unitChargeListMessage += '<div style="font-weight: bold;">' + ' ' + toMoney(parseFloat(unitCharge.unitChargeItemAmount)) + ' p.a.</div>';
			}
			else if(i == 0) {
				unitChargeListMessage += '<div style="font-weight: bold;">' + unitCharge.description + ' ' + toMoney(parseFloat(unitCharge.unitChargeItemAmount)) + ' p.a.</div>';
			}
			else {
				unitChargeListMessage += unitCharge.description + ' ' + toMoney(parseFloat(unitCharge.unitChargeItemAmount)) + ' p.a.<br/>';
			}
		}
	}
	
	var businessUnitsMessage = '';
	if(addressArray.hasOwnProperty('businessUnits')) {
		var businessUnits = addressArray.businessUnits;
		for(var i = 0 ; i < businessUnits.length ; i++) {
			var businessUnit = businessUnits[i];
			businessUnitsMessage += businessUnit.bussUnitDesc + '<br/>';
		}
	}
	
	var popUp = "<table style='max-width: 400px;'>" + 
					"<tr>" +
						"<td style='color: #7A378B; text-decoration: underline; font-weight: bold;'>" + addressArray.leaseID + "</td>" +
						"<td style='font-weight: bold;'>" + addressArray.chargeTypeDescription + "</td>" +
					"</tr>" +
					"<tr>" +
						"<td style='font-weight: bold; word-wrap: break-word;' colspan='2'>" + 
							addressArray.leaseSteet + ' ' + addressArray.leaseCity + ", " + addressArray.leaseState + ", " + addressArray.leasePostcode + 
						"</td>" +
					"</tr>" +
					"<tr>" +
						"<td style='font-weight: bold;'>Lessee Entity: </td>" +
						"<td>" + addressArray.propertyName + "</td>" +
					"</tr>" +
					"<tr>" +
						"<td style='font-weight: bold;'>Division: </td>" +
						"<td>" + addressArray.division + "</td>" +
					"</tr>" +
					"<tr>" +
						"<td style='font-weight: bold;'>Business Unit: </td>" +
						"<td>" + businessUnitsMessage + "</td>" +
					"</tr>" +
					"<tr>" +
						"<td style='font-weight: bold;'>Property Type: </td>" +
						"<td>" + addressArray.propertyType + "</td>" +
					"</tr>" +
					"<tr>" +
						"<td style='font-weight: bold;'>Lease Class: </td>" +
						"<td>" + addressArray.leaseClass + "</td>" +
					"</tr>" +
					"<tr>" +
						"<td style='font-weight: bold;'>Lease Status: </td>" +
						"<td>" + addressArray.leaseStatusDescription + "</td>" +
					"</tr>" +
					"<tr>" +
						"<td style='font-weight: bold;'>Head Count: </td>" +
						"<td>" + addressArray.leaseHeadCount + "</td>" +
					"</tr>" +
					"<tr>" +
						"<td style='font-weight: bold;'>Desk Count: </td>" +
						"<td>" + addressArray.leaseDeskCount + "</td>" +
					"</tr>" +
					"<tr>" +
						"<td colspan='2'>&nbsp;</td>" +
					"</tr>" +
					"<tr>" +
						"<td style='font-weight: bold;'>Lease Term: " + "</td>" +
						"<td>" + addressArray.leaseStartDate + '-' + addressArray.leaseEndDate + " (" + addressArray.leaseTerm + ")</td>" +
					"</tr>" +
					"<tr>" +
						"<td style='font-weight: bold;'>Options: </td>" +
						"<td>" + addressArray.leaseOption + "</td>" +
					"</tr>" +
					"<tr>" +
						"<td style='font-weight: bold;'><b>Area: </td>" +
						"<td>" + addressArray.leaseUnitArea + " " + addressArray.leaseAreaUnit +"&sup2;</td>" +
					"</tr>" +
					"<tr>" +
						"<td style='font-weight: bold; vertical-align: top;'>Current Rent: </td>" +
						"<td style='vertical-align: top;'>" + unitChargeListMessage + "</td>" +
					"</tr>" +
					"<tr>" +
						"<td style='font-weight: bold; vertical-align: top;'>Next Rent Review: </td>" +
						"<td style='vertical-align: top;'>" + reviewListMessage + "</td>" +
					"</tr>" +
					"<tr>" +
						"<td colspan='2'><a style='color: #0000FF !important; text-decoration: underline; font-weight: bold;' href='/framework/index.php?module=leases&command=view&propertyID=" + addressArray.propertyID + "&leaseID=" + addressArray.leaseID + "'>View Lease</a></td>" +
					"</tr>" +
				"</table>";
	var latitude = addressArray.leaseLatitude;
	var longitude = addressArray.leaseLongitude;
	var name = addressArray.name;

	if((latitude.length > 0) && (longitude.length > 0)) {
		var myLatLng = {lat: parseFloat(latitude), lng: parseFloat(longitude)};
		var image = {
			url: 'https://maps.google.com/mapfiles/kml/paddle/grn-blank.png',
			scaledSize: new google.maps.Size(30, 30),
			origin: new google.maps.Point(0, 0),
			anchor: new google.maps.Point(0, 20)
		};

		var marker = new google.maps.Marker({
			position: myLatLng,
			map: map,
			title: addressArray.address,
			icon : image
		});

		google.maps.event.addListener(marker,'click', (function(marker,popUp,infowindow){ 
			return function() {
				if (infowindow) {
					infowindow.close();
				}
				
				infowindow.setContent(popUp);
				infowindow.open(map,marker);
			};
		})(marker,popUp,infowindow));
	}		
}
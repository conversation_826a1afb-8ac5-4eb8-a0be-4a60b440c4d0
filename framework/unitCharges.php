<?

  session_start();
include 'config.php';

define('CLIENTDB',$_SESSION['currentDB']);
define('CLIENTDSN',COREDSN . $_SESSION['currentDB']);
if (!isset($_SESSION['user_name'])) { redirectUser(NPMSURL); }

$dbh = new MSSQL(CLIENTDSN);

$today =  toDateStamp(TODAY);

 $count = 0;

            $sql = "SELECT
            pmla_prop,
            pmla_lease,
            pmla_unit,
            pmla_serial
            FROM pmla_l_c_amt
            GROUP BY
            pmla_prop, pmla_lease, pmla_unit, pmla_serial
            ORDER BY pmla_prop, pmla_lease, pmla_unit, pmla_serial
            ";

            $charges = $dbh->executeSet($sql);

foreach ($charges as $charge) {
             $sql = "SELECT TOP 1
            CONVERT(char(10), pmla_end_dt, 103) AS endDate
            FROM pmla_l_c_amt
            WHERE
            pmla_prop = '{$charge[pmla_prop]}'
            AND pmla_lease = '{$charge[pmla_lease]}'
            AND pmla_unit = '{$charge[pmla_unit]}'
            AND pmla_serial = '{$charge[pmla_serial]}'
            ORDER BY
            pmla_start_dt DESC
            ";
            $date = $dbh->executeScalar($sql);
            
            if (toDateStamp($date) < $today) {
            
                        $nextDay = oneDayAfter($date);
                        $lastDate = FINALDATE;
                        
                        $sql = "INSERT INTO pmla_l_c_amt
                        (
                                    pmla_prop,
                                    pmla_lease,
                                    pmla_unit,
                                    pmla_serial,
                                    pmla_amt,
                                    pmla_start_dt,
                                    pmla_end_dt
                       ) VALUES (
                        '{$charge[pmla_prop]}',
                        '{$charge[pmla_lease]}',
                        '{$charge[pmla_unit]}',
                        '{$charge[pmla_serial]}',
                        0,
                        CONVERT(datetime, '{$nextDay}', 103),
                        CONVERT(datetime, '{$lastDate}', 103)
                       )
            ";
            
            $dbh->executeNonQuery($sql);

            } else {
            $sql = "SELECT TOP 1
            pmla_id
            FROM pmla_l_c_amt
            WHERE
            pmla_prop = '{$charge[pmla_prop]}'
            AND pmla_lease = '{$charge[pmla_lease]}'
            AND pmla_unit = '{$charge[pmla_unit]}'
            AND pmla_serial = '{$charge[pmla_serial]}'
            ORDER BY
            pmla_start_dt DESC
            ";

            $id = $dbh->executeScalar($sql);
            
            $sql = "UPDATE pmla_l_c_amt SET pmla_end_dt = CONVERT(datetime,'" . FINALDATE . "',103) WHERE pmla_id='{$id}'";
            $dbh->executeNonQuery($sql);
                        
            }
            
            
            echo $sql . '<br/>';
            
            }
            



?>
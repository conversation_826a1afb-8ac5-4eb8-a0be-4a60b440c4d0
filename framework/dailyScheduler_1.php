<?php
/*
This script checks the recurring schedule table and schedules into the main scheduler tasks that need to be performed that day
*/
error_reporting (E_ALL ^ E_NOTICE);
//-- if ob_get_clean doesnt exist, define the function
if (!function_exists("ob_get_clean"))
{
	function ob_get_clean ()
	{
		$ob_contents = ob_get_contents ();
		ob_end_clean ();
		return $ob_contents;
    }
}


function unsetAll (&$items)
{
	foreach ($items as $id => $item) unset ($items[$id]);
}



  //-- include and invoke the session handler
  include 'lib/classes/Session.php';
  $sess = new Session();
  
  include_once 'config.php';


  $referrer = 'http://' . $_SERVER['HTTP_HOST'];
  if ($referrer == HTTPHOST) {
    $sess->set('referrer',HTTPHOST . $_SERVER['SCRIPT_NAME'] . '?' .$_SERVER['QUERY_STRING']);
  } else {
    $sess->drop('referrer');
  }
  $sess->drop('queries');

  //-- invoke the database handler and create a new connection to the system database
  $dbh = new MSSQL(SYSTEMDSN);
 
   logScheduler(date('h:i:sA d/m/Y') . ' <b>Daily scheduler started ...</b>');
   dbUpdateStatistic('DailySchedulerLastRun',time());
  
  //-- set a type if you want to limit the tasks being processed to a single type (ie - AR/AP/standing charges/whatever - anything can be assigned an arbitrary type at the insertion stage)
  $type = $_REQUEST['type'];
      
  	$queue = new Queue (TASKTYPE_RECURRING);
        $weekDay = date ('w');
        $monthDay = date ('j');
        $dailyList = $dbh->executeSet ("SELECT command, day, data, databaseID, frequency, createdBy FROM delayedTasks WHERE frequency='D' AND active=1 AND recurring = 1");
        $weeklyList = $dbh->executeSet ("SELECT command, day, data, databaseID, frequency, createdBy FROM delayedTasks WHERE frequency='W' AND day='$weekDay' AND active=1 AND recurring = 1");
        $monthlyList = $dbh->executeSet ("SELECT command, day, data, databaseID, frequency, createdBy FROM delayedTasks WHERE frequency='M' AND day='$monthDay' AND active=1 AND recurring = 1");
	$singleList = $dbh->executeSet ("SELECT taskID, command, day, data, databaseID, frequency, createdBy FROM delayedTasks WHERE recurring = 0");
            
	if ($singleList)
	{
		foreach ($singleList as $item)
		{
			parse_str ($item['data'], $data);
			$queue->add ($item['databaseID'], $item['createdBy'], $item['command'] . $item['data'], $data);
			dbDeleteDelayedTask($item['taskID']);
        }
	}

	if ($dailyList)
	{
		foreach ($dailyList as $item)
		{
			parse_str ($item['data'], $data);
			$queue->add ($item['databaseID'], $item['createdBy'], $item['command'] . $item['data'], $data);
        }
	}
	if ($weeklyList)
	{
		foreach ($weeklyList as $item)
		{
			parse_str ($item['data'], $data);
			$queue->add ($item['databaseID'], $item['createdBy'], $item['command'] . $item['data'], $data);
        }
	}
	if ($monthlyList)
	{
		foreach ($monthlyList as $item)
		{
			parse_str ($item['data'], $data);
			$queue->add ($item['databaseID'], $item['createdBy'], $item['command'] . $item['data'], $data);
		}
	}
?>
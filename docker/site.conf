server {
    error_log  /var/log/nginx/error.log;
    access_log /var/log/nginx/access.log;

    root /app;
    listen 80 default_server;

    index index.php;

    location ~ \.php$ {
        try_files $uri =404;
        fastcgi_split_path_info ^(.+\.php)(/.+)$;
        fastcgi_pass framework-php:9000;
        fastcgi_index index.php;
        include fastcgi_params;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        fastcgi_param PATH_INFO $fastcgi_path_info;

        # Increase timeouts for Xdebug
        fastcgi_read_timeout 3600;    # 1 hour
        fastcgi_connect_timeout 3600; # 1 hour
        fastcgi_send_timeout 3600;    # 1 hour
    }

    location / {
        try_files $uri $uri/ /index.php?$query_string;
        gzip_static on;
    }
}

[xdebug]
zend_extension=xdebug.so

; Xdebug 2.9.8 Configuration
; Enable remote debugging
xdebug.remote_enable=1
xdebug.remote_autostart=1
xdebug.remote_connect_back=0
xdebug.remote_host=************
xdebug.remote_port=9013

; Profiling (optional)
xdebug.profiler_enable=0
xdebug.profiler_enable_trigger=1
xdebug.profiler_output_dir=/tmp

; Coverage (optional)
xdebug.coverage_enable=1

; Logging
xdebug.remote_log=/tmp/xdebug.log

; IDE Key (optional - useful for multiple developers)
xdebug.idekey=PHPSTORM

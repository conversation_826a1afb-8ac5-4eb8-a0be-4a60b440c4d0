# Changelog

All notable changes to this project will be documented in this file. See [commit-and-tag-version](https://github.com/absolute-version/commit-and-tag-version) for commit guidelines.

## [1.3.0-alpha.0](https://github.com/cirrus-8/framework/compare/v1.2.0...v1.3.0-alpha.0) (2025-08-13)

### Features

- add bugsnag js tracking ([#210](https://github.com/cirrus-8/framework/issues/210)) ([d8ce88d](https://github.com/cirrus-8/framework/commit/d8ce88db426439d3640aa3db08ddd33f986d1213))

### Bug Fixes

- client simple report ([#204](https://github.com/cirrus-8/framework/issues/204)) ([a30d66d](https://github.com/cirrus-8/framework/commit/a30d66d92821261eb09ada5e1f4fd2c02b8ce00e)), closes [#CIR-5049](https://github.com/cirrus-8/framework/issues/CIR-5049) [#CIR-5049](https://github.com/cirrus-8/framework/issues/CIR-5049)

## [1.2.0](https://github.com/cirrus-8/framework/compare/v1.2.0-alpha.15...v1.2.0) (2025-08-06)

## [1.2.0-alpha.15](https://github.com/cirrus-8/framework/compare/v1.2.0-alpha.14...v1.2.0-alpha.15) (2025-08-06)

### Bug Fixes

- property financial summary ([#197](https://github.com/cirrus-8/framework/issues/197)) ([25bf614](https://github.com/cirrus-8/framework/commit/25bf6141cbc427a06c0c1840d32f13411c1ed15d)), closes [#CIR-5037](https://github.com/cirrus-8/framework/issues/CIR-5037)

## [1.2.0-alpha.14](https://github.com/cirrus-8/framework/compare/v1.2.0-alpha.13...v1.2.0-alpha.14) (2025-08-04)

## [1.2.0-alpha.13](https://github.com/cirrus-8/framework/compare/v1.2.0-alpha.12...v1.2.0-alpha.13) (2025-08-04)

### ⚠ BREAKING CHANGES

- -remove vendor/autoload.php on index.php

* add redis initialization on config.php

Refs: CIR-5028

### Bug Fixes

- ap journal transfers ([#182](https://github.com/cirrus-8/framework/issues/182)) ([572c89b](https://github.com/cirrus-8/framework/commit/572c89b191427ca5d4fa61707ad0cc84569f2fc9))
- changes to Company Banking Details view ([#177](https://github.com/cirrus-8/framework/issues/177)) ([989c5d7](https://github.com/cirrus-8/framework/commit/989c5d7374600f390b4e6b48ea79571757b7e949))
- cir-4998-fix-mt9-payment-file ([#187](https://github.com/cirrus-8/framework/issues/187)) ([71c37f2](https://github.com/cirrus-8/framework/commit/71c37f2cbb74a839ce3f58de9b302292800c2737))
- energytec upload ([#191](https://github.com/cirrus-8/framework/issues/191)) ([3ca8d7d](https://github.com/cirrus-8/framework/commit/3ca8d7d24172c6bbc239676cfa3fd7914e512ce0))
- redis fix ([#190](https://github.com/cirrus-8/framework/issues/190)) ([6760c20](https://github.com/cirrus-8/framework/commit/6760c2003130bf192f84ab996a4c738859cf18ae))
- remove recursive function for nav redis ([#189](https://github.com/cirrus-8/framework/issues/189)) ([f8d7701](https://github.com/cirrus-8/framework/commit/f8d7701918623ac897aaca2053988ad3f7dd517c))

## [1.2.0-alpha.12](https://github.com/cirrus-8/framework/compare/v1.2.0-alpha.11...v1.2.0-alpha.12) (2025-07-28)

### Bug Fixes

- missing enum use statements ([#167](https://github.com/cirrus-8/framework/issues/167)) ([fe972d8](https://github.com/cirrus-8/framework/commit/fe972d8f564197682713bf6537a33809bf914963))
- view efts ([#175](https://github.com/cirrus-8/framework/issues/175)) ([69f7ba5](https://github.com/cirrus-8/framework/commit/69f7ba53ce7fae780af1d8c9c4420b88153eb14e))

## [1.2.0-alpha.11](https://github.com/cirrus-8/framework/compare/v1.2.0-alpha.10...v1.2.0-alpha.11) (2025-07-25)

### Bug Fixes

- client custom excel report ([#173](https://github.com/cirrus-8/framework/issues/173)) ([e4bf79e](https://github.com/cirrus-8/framework/commit/e4bf79e4f02b595ce917caccefd0c851f694e598)), closes [#CIR-4474](https://github.com/cirrus-8/framework/issues/CIR-4474)
- company approval ([#172](https://github.com/cirrus-8/framework/issues/172)) ([2276847](https://github.com/cirrus-8/framework/commit/22768479379e6b5e2aa7f00eefbf8714f1367e8e))
- gh actions and lint ([1a5901a](https://github.com/cirrus-8/framework/commit/1a5901aa3adfce147f7a704c485ec07780587983))
- remittance advice and remove lucida grande font (not used) ([#174](https://github.com/cirrus-8/framework/issues/174)) ([aacc63f](https://github.com/cirrus-8/framework/commit/aacc63fa36fee3eb76998b16bde378389faedd00))

## [1.2.0-alpha.10](https://github.com/cirrus-8/framework/compare/v1.2.0-alpha.9...v1.2.0-alpha.10) (2025-07-24)

### Bug Fixes

- exclude PM read only on the list ([#170](https://github.com/cirrus-8/framework/issues/170)) ([a271afd](https://github.com/cirrus-8/framework/commit/a271afd86c0c6992f2007dfb644f13715e03d059))
- pdf, includes and optimize file/folder checking ([c012087](https://github.com/cirrus-8/framework/commit/c01208706ad75dd2416d7c9da0f555f95ee714ae))

## [1.2.0-alpha.9](https://github.com/cirrus-8/framework/compare/v1.2.0-alpha.8...v1.2.0-alpha.9) (2025-07-24)

### Bug Fixes

- includes and requires ([#169](https://github.com/cirrus-8/framework/issues/169)) ([6b15f20](https://github.com/cirrus-8/framework/commit/6b15f20f055d9d0b1292899576eb58567f928fca))

## [1.2.0-alpha.8](https://github.com/cirrus-8/framework/compare/v1.2.0-alpha.7...v1.2.0-alpha.8) (2025-07-24)

### Bug Fixes

- path of xslDataReport ([bcc717a](https://github.com/cirrus-8/framework/commit/bcc717a891a91dd3b2131df89152745401ec6e48))

## [1.2.0-alpha.7](https://github.com/cirrus-8/framework/compare/v1.2.0-alpha.6...v1.2.0-alpha.7) (2025-07-23)

### Features

- cache dbGetPageRetailSalesPlus ([#165](https://github.com/cirrus-8/framework/issues/165)) ([5cb196e](https://github.com/cirrus-8/framework/commit/5cb196e2496499241f305e09fdc9aebc6ccbcf5d))

## [1.2.0-alpha.6](https://github.com/cirrus-8/framework/compare/v1.2.0-alpha.5...v1.2.0-alpha.6) (2025-07-21)

### Features

- cir-4952 optimize getEmailLogDetailById ([#157](https://github.com/cirrus-8/framework/issues/157)) ([802c659](https://github.com/cirrus-8/framework/commit/802c6594d020b847e69ee4055c7c1a3fb7806585))

### Bug Fixes

- fix lease recode bug affecting multiple properties with same lease code ([#154](https://github.com/cirrus-8/framework/issues/154)) ([def6a38](https://github.com/cirrus-8/framework/commit/def6a38ce0460f5931138b9864065a2cd4ee4f2a))

## [1.2.0-alpha.5](https://github.com/cirrus-8/framework/compare/v1.2.0-alpha.4...v1.2.0-alpha.5) (2025-07-18)

### Features

- improve error handling for empty exception messages in Postmark integration ([#150](https://github.com/cirrus-8/framework/issues/150)) ([34623b5](https://github.com/cirrus-8/framework/commit/34623b5b8fc311b3c04577d0a4f099711442f21e))

### Bug Fixes

- manage calendar changes ([#147](https://github.com/cirrus-8/framework/issues/147)) ([630d63e](https://github.com/cirrus-8/framework/commit/630d63ecd120ab977f1ca43fd96b301f5dcd9e6d))
- phpspreadsheet number format ([#153](https://github.com/cirrus-8/framework/issues/153)) ([176b0f3](https://github.com/cirrus-8/framework/commit/176b0f3bb9754188ca4f21c690559f3a14c87c91))
- selected tenants validation ([#156](https://github.com/cirrus-8/framework/issues/156)) ([9a3d5dd](https://github.com/cirrus-8/framework/commit/9a3d5dda378a3580786a24360ccf7d3e649e1db2))

## [1.2.0-alpha.4](https://github.com/cirrus-8/framework/compare/v1.2.0-alpha.3...v1.2.0-alpha.4) (2025-07-15)

### Bug Fixes

- query optimization ([#148](https://github.com/cirrus-8/framework/issues/148)) ([f0faa8c](https://github.com/cirrus-8/framework/commit/f0faa8c196bae7106697705958efe51c626ca43c))
- update dbinterface ([#149](https://github.com/cirrus-8/framework/issues/149)) ([400c66a](https://github.com/cirrus-8/framework/commit/400c66ad895fbb4e7668d000439f1379088af450))

## [1.2.0-alpha.3](https://github.com/cirrus-8/framework/compare/v1.2.0-alpha.2...v1.2.0-alpha.3) (2025-07-14)

### Bug Fixes

- remove version compare and match ([#146](https://github.com/cirrus-8/framework/issues/146)) ([0722796](https://github.com/cirrus-8/framework/commit/0722796e679e9535dc6e0d592b6e72989c96f77b))

## [1.2.0-alpha.2](https://github.com/cirrus-8/framework/compare/v1.2.0-alpha.1...v1.2.0-alpha.2) (2025-07-14)

### Bug Fixes

- call to undefined function dbGetQueueItemsByType() ([#145](https://github.com/cirrus-8/framework/issues/145)) ([2b13006](https://github.com/cirrus-8/framework/commit/2b13006d46156030d07774556cf655c521012c9a))

## [1.2.0-alpha.1](https://github.com/cirrus-8/framework/compare/v1.2.0-alpha.0...v1.2.0-alpha.1) (2025-07-14)

### Bug Fixes

- remove duplicate function and switch for php7 compatibility ([#144](https://github.com/cirrus-8/framework/issues/144)) ([9c26456](https://github.com/cirrus-8/framework/commit/9c26456a4500e3b3f4c5750373c753c48bbe7cb7))

## [1.2.0-alpha.0](https://github.com/cirrus-8/framework/compare/v1.1.0-alpha.12...v1.2.0-alpha.0) (2025-07-14)

### ⚠ BREAKING CHANGES

-   - add to config.php : include('lib/PDFLibExt.php');
    - should update files that use these functions pdflib_font_family, pdflib_font_color, pdflib_show_boxed, pdf_show_boxed
    - pdf->setcolor should be changed to pdf->setcolorExt

# Conflicts:

# framework/lib/PDFReport.php

# framework/lib/documents/PDFCheque_BC.php

# framework/lib/documents/PDFCheque_BR.php

# framework/lib/documents/PDFCheque_BW.php

# framework/lib/documents/PDFCheque_CBA.php

# framework/lib/documents/PDFCheque_GW.php

# framework/lib/documents/PDFCheque_JLL.php

# framework/lib/documents/PDFCheque_KF.php

# framework/lib/documents/PDFCheque_MET.php

# framework/lib/documents/PDFCheque_PC.php

# framework/lib/documents/PDFCheque_RH.php

# framework/lib/documents/PDFCheque_RHCP.php

# framework/lib/documents/PDFCheque_RN.php

# framework/lib/documents/PDFCheque_RW.php

# framework/lib/documents/PDFCheque_SJ.php

# framework/lib/documents/PDFDiaryReminder.php

# framework/lib/documents/RemittanceAdviceCheque.php

Refs: CIR-4416

- refactor: PDFLib upgrade

* reapply changes to PDFCheque_CBA.php
* reverted fit_textline to show_boxed
* used setcolorExt instead of setcolor

Refs: CIR-4416

- refactor: PDFLibExt

* add function documentation

Refs: CIR-4416

# Conflicts:

# framework/lib/PDFLibExt.php

- fix: PDFLibExt

* $font -> $fontFamily
* default values for class variables
* add function and parameter descriptions
* setcolorExt to setColorExt

Refs: CIR-4416

- refactor: PDFLib upgrade on 7.4

* Extended PDFLib with function showBoxed
* used PDFLibExt (replacing PDFLib) for all reports
* replace new PDFLib to new PDFLibEXt
* replace ->show_boxed to ->showBoxed
* replace set_parameter('license',PDFLIB_LICENSE); to set_option('license=' . PDFLIB_LICENSE);
* replace set_parameter('stringformat', 'utf8'); to set_option('stringformat=utf8');
* replace set_parameter("SearchPath", realpath(BASEPATH . '/framework/')); to set_option("SearchPath=" . realpath(BASEPATH . '/framework/'));
* replace set_parameter("errorpolicy", "exception"); to set_option("errorpolicy=exception");
* replace pdf->setFont to pdf->setFontExt
* replace pdf->setcolor to pdf->setColorExt

-   - add to config.php : include('lib/PDFLibExt.php');

Refs: CIR-4416

- refactor: PDFLib upgrade on 7.4

* use $pdf->setColorExt("both" instead of $pdf->setColorExt("fill" when changing font color
* parent::setfont($fontFamily, $fontsize); to $this->setfont($fontFamily, $fontsize);
* parent::setcolor($fstype, $colorspace, $c1, $c2, $c3, $c4); to $this->setcolor($fstype, $colorspace, $c1, $c2, $c3, $c4);

Refs: CIR-4416

- fix: PDFLib upgrade on 7.4 and 8.3

* removed extra } on PDFDiaryReminder.php
* show_boxed to showBoxed on PDFCheque_CBA.php

Refs: CIR-4416

- chore: replace usage of ISNULL to COALESCE

* in preparation for searching and updating of SUM without COALESCE

Refs: CIR-3760

- fix: null return of SUM

* add COALESCE to SUM

Refs: CIR-3760

- fix: shared/dbInterface.php

* add missing brackets

Refs: CIR-3760

- fix: database function prune (used by select queries)

* trim causes the value to be a string, so type check is added
* autoformat on save

Refs: CIR-3760

- fix: PDFLib function setdash (deprecated)

* update usages to set_graphics_option

Refs: CIR-3760

- fix: change array_COALESCE(SUM to array_sum

Refs: CIR-3760

- Epic/cir 3760 php 8 upgrade feature (#139) ([51e7a55](https://github.com/cirrus-8/framework/commit/51e7a55e3b83722db28433c3b1243908075327fe)), closes [#139](https://github.com/cirrus-8/framework/issues/139) [#7641](https://github.com/cirrus-8/framework/issues/7641) [#7638](https://github.com/cirrus-8/framework/issues/7638) [#7646](https://github.com/cirrus-8/framework/issues/7646) [#7645](https://github.com/cirrus-8/framework/issues/7645) [#7648](https://github.com/cirrus-8/framework/issues/7648) [#7651](https://github.com/cirrus-8/framework/issues/7651) [#7653](https://github.com/cirrus-8/framework/issues/7653) [#7655](https://github.com/cirrus-8/framework/issues/7655) [#7671](https://github.com/cirrus-8/framework/issues/7671) [#7675](https://github.com/cirrus-8/framework/issues/7675) [#6](https://github.com/cirrus-8/framework/issues/6) [#11](https://github.com/cirrus-8/framework/issues/11) [#27](https://github.com/cirrus-8/framework/issues/27) [#24](https://github.com/cirrus-8/framework/issues/24) [#22](https://github.com/cirrus-8/framework/issues/22) [#31](https://github.com/cirrus-8/framework/issues/31) [#32](https://github.com/cirrus-8/framework/issues/32) [#33](https://github.com/cirrus-8/framework/issues/33) [#34](https://github.com/cirrus-8/framework/issues/34) [#38](https://github.com/cirrus-8/framework/issues/38) [#45](https://github.com/cirrus-8/framework/issues/45) [#46](https://github.com/cirrus-8/framework/issues/46) [#47](https://github.com/cirrus-8/framework/issues/47) [#50](https://github.com/cirrus-8/framework/issues/50) [#48](https://github.com/cirrus-8/framework/issues/48) [#49](https://github.com/cirrus-8/framework/issues/49) [#63](https://github.com/cirrus-8/framework/issues/63) [#56](https://github.com/cirrus-8/framework/issues/56) [#57](https://github.com/cirrus-8/framework/issues/57) [#60](https://github.com/cirrus-8/framework/issues/60) [#61](https://github.com/cirrus-8/framework/issues/61) [#65](https://github.com/cirrus-8/framework/issues/65) [#66](https://github.com/cirrus-8/framework/issues/66) [#67](https://github.com/cirrus-8/framework/issues/67) [#79](https://github.com/cirrus-8/framework/issues/79) [#78](https://github.com/cirrus-8/framework/issues/78) [#76](https://github.com/cirrus-8/framework/issues/76) [#74](https://github.com/cirrus-8/framework/issues/74) [#73](https://github.com/cirrus-8/framework/issues/73) [#72](https://github.com/cirrus-8/framework/issues/72) [#98](https://github.com/cirrus-8/framework/issues/98) [#94](https://github.com/cirrus-8/framework/issues/94) [#91](https://github.com/cirrus-8/framework/issues/91) [#87](https://github.com/cirrus-8/framework/issues/87) [#77](https://github.com/cirrus-8/framework/issues/77) [#101](https://github.com/cirrus-8/framework/issues/101) [#104](https://github.com/cirrus-8/framework/issues/104) [#107](https://github.com/cirrus-8/framework/issues/107) [#96](https://github.com/cirrus-8/framework/issues/96) [#116](https://github.com/cirrus-8/framework/issues/116) [#108](https://github.com/cirrus-8/framework/issues/108) [#105](https://github.com/cirrus-8/framework/issues/105) [#100](https://github.com/cirrus-8/framework/issues/100) [#99](https://github.com/cirrus-8/framework/issues/99) [#97](https://github.com/cirrus-8/framework/issues/97) [#110](https://github.com/cirrus-8/framework/issues/110) [#120](https://github.com/cirrus-8/framework/issues/120) [#119](https://github.com/cirrus-8/framework/issues/119) [#88](https://github.com/cirrus-8/framework/issues/88) [#125](https://github.com/cirrus-8/framework/issues/125) [#130](https://github.com/cirrus-8/framework/issues/130)

### Features

- cir-4774 fix property manager mobile number ([#129](https://github.com/cirrus-8/framework/issues/129)) ([aef44b8](https://github.com/cirrus-8/framework/commit/aef44b8c7b5e3041ace345268e6eca16b7d33188))
- invoice note in ap unpaid invoice and property balances beta ([#118](https://github.com/cirrus-8/framework/issues/118)) ([8378677](https://github.com/cirrus-8/framework/commit/83786778c00a07a8c4b9904268e4c94ac939fb72))
- property manager mobile number on email template ([#113](https://github.com/cirrus-8/framework/issues/113)) ([f16fafb](https://github.com/cirrus-8/framework/commit/f16fafb95200a6fc7722ddcdd4c87e0fb439ddc7))

### Bug Fixes

- back charge automation ([#114](https://github.com/cirrus-8/framework/issues/114)) ([14455c1](https://github.com/cirrus-8/framework/commit/14455c1cf50e2d2bacca64339575cc48da0527a1)), closes [#CIR-4719](https://github.com/cirrus-8/framework/issues/CIR-4719)
- back charge automation ([#128](https://github.com/cirrus-8/framework/issues/128)) ([af74094](https://github.com/cirrus-8/framework/commit/af74094d325baf1748f6983ae2818c0a1cb34d8d)), closes [#CIR-4817](https://github.com/cirrus-8/framework/issues/CIR-4817)
- back charge automation ([#136](https://github.com/cirrus-8/framework/issues/136)) ([fca3985](https://github.com/cirrus-8/framework/commit/fca3985e3f5a4987193bc8586f816dbde7136d75)), closes [#CIR-4817](https://github.com/cirrus-8/framework/issues/CIR-4817)
- dynamic coverpage ([#138](https://github.com/cirrus-8/framework/issues/138)) ([85bac9a](https://github.com/cirrus-8/framework/commit/85bac9a14c40a54eb0378e3352374cd37f2faf10)), closes [#CIR-4827](https://github.com/cirrus-8/framework/issues/CIR-4827) [#CIR-4827](https://github.com/cirrus-8/framework/issues/CIR-4827)
- fix property manager mobile number on lease charge review ([#142](https://github.com/cirrus-8/framework/issues/142)) ([3942fb9](https://github.com/cirrus-8/framework/commit/3942fb977c28955f84411c31fca11e2a46594772))
- loading of new company form vue page ([bfecf48](https://github.com/cirrus-8/framework/commit/bfecf48b8d9635294503d5eb0d250e00ab9d5421))
- restore deleted function for removing invoice line note in inv and cre adj ([#140](https://github.com/cirrus-8/framework/issues/140)) ([a57ff87](https://github.com/cirrus-8/framework/commit/a57ff87dd091bee53221bdbe3e82e61a2502d115))
- send email : attachment ([#141](https://github.com/cirrus-8/framework/issues/141)) ([cb50358](https://github.com/cirrus-8/framework/commit/cb50358e05dfce23eff7d43bb783b9e618266b75))

## [1.1.0](https://github.com/cirrus-8/framework/compare/v1.1.0-alpha.12...v1.1.0) (2025-06-25)

### Features

- property manager mobile number on email template ([#113](https://github.com/cirrus-8/framework/issues/113)) ([f16fafb](https://github.com/cirrus-8/framework/commit/f16fafb95200a6fc7722ddcdd4c87e0fb439ddc7))

## [1.1.0-alpha.12](https://github.com/cirrus-8/framework/compare/v1.1.0-alpha.11...v1.1.0-alpha.12) (2025-06-25)

### Features

- property manager mobile number on email template ([#112](https://github.com/cirrus-8/framework/issues/112)) ([940f986](https://github.com/cirrus-8/framework/commit/940f986826eef3252e037d7c05408f6fadb98219))

## [1.1.0-alpha.11](https://github.com/cirrus-8/framework/compare/v1.1.0-alpha.10...v1.1.0-alpha.11) (2025-06-24)

### Features

- invoice note in ap unpaid invoice and property balances ([#89](https://github.com/cirrus-8/framework/issues/89)) ([304d374](https://github.com/cirrus-8/framework/commit/304d374c0ef2a1287ead29fc2c5ccf60d0c24181))

## [1.1.0-alpha.10](https://github.com/cirrus-8/framework/compare/v1.1.0-alpha.9...v1.1.0-alpha.10) (2025-06-24)

### Features

- new files for accessing the new company form in company summary on ([f3f6c22](https://github.com/cirrus-8/framework/commit/f3f6c222ad3e63b55b8132edb75a8e51fdbc19e8))
- new files for accessing the new company form in company summary on ([f2d39e7](https://github.com/cirrus-8/framework/commit/f2d39e74567a57d74d56808705377a1064e554aa))

### Bug Fixes

- back charge automation ([#103](https://github.com/cirrus-8/framework/issues/103)) ([54553fc](https://github.com/cirrus-8/framework/commit/54553fc30b3bc86baeca556b61e92155fcabde5f)), closes [#CIR-3848](https://github.com/cirrus-8/framework/issues/CIR-3848) [#CIR-3848](https://github.com/cirrus-8/framework/issues/CIR-3848)
- run prettier for new_company_page.html ([81b12a5](https://github.com/cirrus-8/framework/commit/81b12a51ac1f280bc8f1f27be6bcb320e7527ee7))

## [1.1.0-alpha.9](https://github.com/cirrus-8/framework/compare/v1.1.0-alpha.8...v1.1.0-alpha.9) (2025-06-19)

### Bug Fixes

- change label from default to lease code ([8998f04](https://github.com/cirrus-8/framework/commit/8998f049292c4bd3840fe8db5b4ff745bf830a06))

## [1.1.0-alpha.8](https://github.com/cirrus-8/framework/compare/v1.1.0-alpha.7...v1.1.0-alpha.8) (2025-06-19)

### Features

- add total transfer fund row in finance tab ([#102](https://github.com/cirrus-8/framework/issues/102)) ([3871d4b](https://github.com/cirrus-8/framework/commit/3871d4b35bf762d05f11eeb931643e48ff2a2540))

## [1.1.0-alpha.7](https://github.com/cirrus-8/framework/compare/v1.1.0-alpha.6...v1.1.0-alpha.7) (2025-06-19)

### Features

- dispatch email using api ([4050fdb](https://github.com/cirrus-8/framework/commit/4050fdb373f4e646948ab88b8054f2e5f9bcae6a))
- integrate `rmccue/requests` for email dispatch and refactor email processing logic ([d6e1827](https://github.com/cirrus-8/framework/commit/d6e182725b4e2314d5b3fafbfe5818cbe29906e2))

## [1.1.0-alpha.6](https://github.com/cirrus-8/framework/compare/v1.1.0-alpha.5...v1.1.0-alpha.6) (2025-06-18)

### Bug Fixes

- fitzroy custom long report ([#85](https://github.com/cirrus-8/framework/issues/85)) ([00f4df8](https://github.com/cirrus-8/framework/commit/00f4df89a5f84e2fc6f8fbd10de84fa9d377732c)), closes [#CIR-4625](https://github.com/cirrus-8/framework/issues/CIR-4625)
- fixed invoice and repeating invoice errors ([#86](https://github.com/cirrus-8/framework/issues/86)) ([cffcc1b](https://github.com/cirrus-8/framework/commit/cffcc1b70f20d358d0423fab9d45050f1cfb61c9))
- pims report ytd total income ([#92](https://github.com/cirrus-8/framework/issues/92)) ([2dfeacb](https://github.com/cirrus-8/framework/commit/2dfeacb65a4b09a1fda9f52bebdbf4d006bc15cf))
- remove unnecessary !important from dark theme CSS rule for property budget ([abb0580](https://github.com/cirrus-8/framework/commit/abb0580a8044c8e38de1906f7e3b3a5d929bb9ac))
- wording in ar one off charge ([#95](https://github.com/cirrus-8/framework/issues/95)) ([4c23057](https://github.com/cirrus-8/framework/commit/4c230576daab13847678a69f8e261338b13d661d))

## [1.1.0-alpha.5](https://github.com/cirrus-8/framework/compare/v1.1.0-alpha.4...v1.1.0-alpha.5) (2025-06-13)

### Features

- fix internal reference field ([#83](https://github.com/cirrus-8/framework/issues/83)) ([42d61b1](https://github.com/cirrus-8/framework/commit/42d61b1abc396157c6bf347a27c2e4c0538d689d))

## [1.1.0-alpha.4](https://github.com/cirrus-8/framework/compare/v1.1.0-alpha.3...v1.1.0-alpha.4) (2025-06-13)

### Features

- fix internal reference field ([afeba58](https://github.com/cirrus-8/framework/commit/afeba5844304c2f6ed6f2cedfb8aa8274372c6e2))

### Bug Fixes

- hotfix include account 10030 in dot report ([#80](https://github.com/cirrus-8/framework/issues/80)) ([0cdf159](https://github.com/cirrus-8/framework/commit/0cdf159a179e8a0f4d015365f3f6bca27e1b29bd))
- sorting logic in arrears and debtors reports ([4c639a6](https://github.com/cirrus-8/framework/commit/4c639a645b045414cc50919de05df80bea10f8cd))

## [1.1.0-alpha.3](https://github.com/cirrus-8/framework/compare/v1.1.0-alpha.2...v1.1.0-alpha.3) (2025-06-12)

### Features

- fix testing issues ([#71](https://github.com/cirrus-8/framework/issues/71)) ([6efb5ab](https://github.com/cirrus-8/framework/commit/6efb5abece9469558d095875587511a48873db4d))

## [1.1.0-alpha.2](https://github.com/cirrus-8/framework/compare/v1.1.0-alpha.1...v1.1.0-alpha.2) (2025-06-12)

## [1.1.0-alpha.1](https://github.com/cirrus-8/framework/compare/v1.1.0-alpha.0...v1.1.0-alpha.1) (2025-06-11)

## [1.1.0-alpha.0](https://github.com/cirrus-8/framework/compare/v1.0.0...v1.1.0-alpha.0) (2025-06-10)

### Features

- add release scripts and versions ([7a374e1](https://github.com/cirrus-8/framework/commit/7a374e16bcd9884807c5a3c84b57be9af757f7c7))
- **packages:** add phpexcel and bugsnag ([a6dbcf4](https://github.com/cirrus-8/framework/commit/a6dbcf4d1d978a79cdc01cf004005060706fb875))

### Bug Fixes

- fix array access in fastJson ([8115d91](https://github.com/cirrus-8/framework/commit/8115d917e500bcef8da8f74eebe35d0a569e24e6))
- fix column count for Portfolio manage income graph ([15ed6b2](https://github.com/cirrus-8/framework/commit/15ed6b2b95103fbd1b4444ed62989a899ef55a58))
- improve prettier ignore and ts lib ([585e641](https://github.com/cirrus-8/framework/commit/585e641420adb849a94b385597775d9643361adb))
- issue in budget query when year is undefined ([368153e](https://github.com/cirrus-8/framework/commit/368153eed1f894c2bef0231b429cb3821d410fe4))
- missing css ([#35](https://github.com/cirrus-8/framework/issues/35)) ([e1b9f48](https://github.com/cirrus-8/framework/commit/e1b9f48c5282d51a7a9eedebd7668a28e3abe507))

## 1.0.0 (2025-05-27)

### Features

- change the source data of year list in owner budget and make the year mandatory when sending Owner budget. ([2f97700](https://github.com/cirrus-8/framework/commit/2f97700f1d4878ac46b82c08b39cd4f39d667ac1))
- disable permission check ([5f8873c](https://github.com/cirrus-8/framework/commit/5f8873cb8f3a5ec3ea10ef987c622d1e516fc262))
- enable the AnnounceKit widget for all users ([ccc5fd0](https://github.com/cirrus-8/framework/commit/ccc5fd0965f4461bb30def5569d89b774fae276e))
- **packages:** add phpexcel and bugsnag ([0ce6c2a](https://github.com/cirrus-8/framework/commit/0ce6c2ad0d069435c11e9c30ff4eb66862cd07b6))

### Bug Fixes

- Business Intelligence graph not displaying ([#19](https://github.com/cirrus-8/framework/issues/19)) ([87368ce](https://github.com/cirrus-8/framework/commit/87368ce7d80e647ce0e521ea779328110ee443ef))
- commit live changes to main ([#18](https://github.com/cirrus-8/framework/issues/18)) ([c911bc5](https://github.com/cirrus-8/framework/commit/c911bc53af2e5ec9c81f946294a818ef331951ee))
- do not execute function if propertyID is empty ([66b11ee](https://github.com/cirrus-8/framework/commit/66b11ee7b9ee234bfadfc809d86282f186960ad9))
- fix array access in fastJson ([dc1926e](https://github.com/cirrus-8/framework/commit/dc1926e8301bcfad4392c77828d1f2b9df4db21a))
- improve prettier ignore and ts lib ([c6069e8](https://github.com/cirrus-8/framework/commit/c6069e80d5c3ee6909d3c69c2009809e074f08b0))
- missing budget placeholder in property owner budget ([0c6f168](https://github.com/cirrus-8/framework/commit/0c6f1688fd8560416a440768eb554311ef619400))
- missing budget placeholder in property owner budget ([b227905](https://github.com/cirrus-8/framework/commit/b227905ab7720d800781fb189ca9125a787f0f1f))
- missing budget placeholder in property owner budget ([a6ad1d2](https://github.com/cirrus-8/framework/commit/a6ad1d2d20a3d64683f803d62a79e1c8135f2c04))
- parameter is string or array ([03371b5](https://github.com/cirrus-8/framework/commit/03371b5645ddae735ff43667537d0a189c71a3d6))
- remove parameter type ([c64dd8a](https://github.com/cirrus-8/framework/commit/c64dd8af3054827853f57c40ada2051d68756ec0))
- remove two parameter type ([60179f3](https://github.com/cirrus-8/framework/commit/60179f32205cbabf41564a4fc56b1fd865d0988f))
- show distinct budget year in budget year list ([fa06a84](https://github.com/cirrus-8/framework/commit/fa06a84f0e582df4c659932117484b63e5b10d02))
- show distinct budget year in budget year list ([f3ed7ba](https://github.com/cirrus-8/framework/commit/f3ed7ba5698f1b9ed0b8ba3e07c1792f7ef97bb4))
- show distinct budget year in budget year list ([97b72d6](https://github.com/cirrus-8/framework/commit/97b72d6ac0329fa0a9b133bfd56b3f7dedb449c4))

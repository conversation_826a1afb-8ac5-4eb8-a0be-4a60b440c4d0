# Changelog

All notable changes to this project will be documented in this file. See [commit-and-tag-version](https://github.com/absolute-version/commit-and-tag-version) for commit guidelines.

## [1.1.0](https://github.com/cirrus-8/framework/compare/v1.1.0-alpha.12...v1.1.0) (2025-06-25)

### Features

- property manager mobile number on email template ([#113](https://github.com/cirrus-8/framework/issues/113)) ([f16fafb](https://github.com/cirrus-8/framework/commit/f16fafb95200a6fc7722ddcdd4c87e0fb439ddc7))

## [1.1.0-alpha.12](https://github.com/cirrus-8/framework/compare/v1.1.0-alpha.11...v1.1.0-alpha.12) (2025-06-25)

### Features

- property manager mobile number on email template ([#112](https://github.com/cirrus-8/framework/issues/112)) ([940f986](https://github.com/cirrus-8/framework/commit/940f986826eef3252e037d7c05408f6fadb98219))

## [1.1.0-alpha.11](https://github.com/cirrus-8/framework/compare/v1.1.0-alpha.10...v1.1.0-alpha.11) (2025-06-24)

### Features

- invoice note in ap unpaid invoice and property balances ([#89](https://github.com/cirrus-8/framework/issues/89)) ([304d374](https://github.com/cirrus-8/framework/commit/304d374c0ef2a1287ead29fc2c5ccf60d0c24181))

## [1.1.0-alpha.10](https://github.com/cirrus-8/framework/compare/v1.1.0-alpha.9...v1.1.0-alpha.10) (2025-06-24)

### Features

- new files for accessing the new company form in company summary on ([f3f6c22](https://github.com/cirrus-8/framework/commit/f3f6c222ad3e63b55b8132edb75a8e51fdbc19e8))
- new files for accessing the new company form in company summary on ([f2d39e7](https://github.com/cirrus-8/framework/commit/f2d39e74567a57d74d56808705377a1064e554aa))

### Bug Fixes

- back charge automation ([#103](https://github.com/cirrus-8/framework/issues/103)) ([54553fc](https://github.com/cirrus-8/framework/commit/54553fc30b3bc86baeca556b61e92155fcabde5f)), closes [#CIR-3848](https://github.com/cirrus-8/framework/issues/CIR-3848) [#CIR-3848](https://github.com/cirrus-8/framework/issues/CIR-3848)
- run prettier for new_company_page.html ([81b12a5](https://github.com/cirrus-8/framework/commit/81b12a51ac1f280bc8f1f27be6bcb320e7527ee7))

## [1.1.0-alpha.9](https://github.com/cirrus-8/framework/compare/v1.1.0-alpha.8...v1.1.0-alpha.9) (2025-06-19)

### Bug Fixes

- change label from default to lease code ([8998f04](https://github.com/cirrus-8/framework/commit/8998f049292c4bd3840fe8db5b4ff745bf830a06))

## [1.1.0-alpha.8](https://github.com/cirrus-8/framework/compare/v1.1.0-alpha.7...v1.1.0-alpha.8) (2025-06-19)

### Features

- add total transfer fund row in finance tab ([#102](https://github.com/cirrus-8/framework/issues/102)) ([3871d4b](https://github.com/cirrus-8/framework/commit/3871d4b35bf762d05f11eeb931643e48ff2a2540))

## [1.1.0-alpha.7](https://github.com/cirrus-8/framework/compare/v1.1.0-alpha.6...v1.1.0-alpha.7) (2025-06-19)

### Features

- dispatch email using api ([4050fdb](https://github.com/cirrus-8/framework/commit/4050fdb373f4e646948ab88b8054f2e5f9bcae6a))
- integrate `rmccue/requests` for email dispatch and refactor email processing logic ([d6e1827](https://github.com/cirrus-8/framework/commit/d6e182725b4e2314d5b3fafbfe5818cbe29906e2))

## [1.1.0-alpha.6](https://github.com/cirrus-8/framework/compare/v1.1.0-alpha.5...v1.1.0-alpha.6) (2025-06-18)

### Bug Fixes

- fitzroy custom long report ([#85](https://github.com/cirrus-8/framework/issues/85)) ([00f4df8](https://github.com/cirrus-8/framework/commit/00f4df89a5f84e2fc6f8fbd10de84fa9d377732c)), closes [#CIR-4625](https://github.com/cirrus-8/framework/issues/CIR-4625)
- fixed invoice and repeating invoice errors ([#86](https://github.com/cirrus-8/framework/issues/86)) ([cffcc1b](https://github.com/cirrus-8/framework/commit/cffcc1b70f20d358d0423fab9d45050f1cfb61c9))
- pims report ytd total income ([#92](https://github.com/cirrus-8/framework/issues/92)) ([2dfeacb](https://github.com/cirrus-8/framework/commit/2dfeacb65a4b09a1fda9f52bebdbf4d006bc15cf))
- remove unnecessary !important from dark theme CSS rule for property budget ([abb0580](https://github.com/cirrus-8/framework/commit/abb0580a8044c8e38de1906f7e3b3a5d929bb9ac))
- wording in ar one off charge ([#95](https://github.com/cirrus-8/framework/issues/95)) ([4c23057](https://github.com/cirrus-8/framework/commit/4c230576daab13847678a69f8e261338b13d661d))

## [1.1.0-alpha.5](https://github.com/cirrus-8/framework/compare/v1.1.0-alpha.4...v1.1.0-alpha.5) (2025-06-13)

### Features

- fix internal reference field ([#83](https://github.com/cirrus-8/framework/issues/83)) ([42d61b1](https://github.com/cirrus-8/framework/commit/42d61b1abc396157c6bf347a27c2e4c0538d689d))

## [1.1.0-alpha.4](https://github.com/cirrus-8/framework/compare/v1.1.0-alpha.3...v1.1.0-alpha.4) (2025-06-13)

### Features

- fix internal reference field ([afeba58](https://github.com/cirrus-8/framework/commit/afeba5844304c2f6ed6f2cedfb8aa8274372c6e2))

### Bug Fixes

- hotfix include account 10030 in dot report ([#80](https://github.com/cirrus-8/framework/issues/80)) ([0cdf159](https://github.com/cirrus-8/framework/commit/0cdf159a179e8a0f4d015365f3f6bca27e1b29bd))
- sorting logic in arrears and debtors reports ([4c639a6](https://github.com/cirrus-8/framework/commit/4c639a645b045414cc50919de05df80bea10f8cd))

## [1.1.0-alpha.3](https://github.com/cirrus-8/framework/compare/v1.1.0-alpha.2...v1.1.0-alpha.3) (2025-06-12)

### Features

- fix testing issues ([#71](https://github.com/cirrus-8/framework/issues/71)) ([6efb5ab](https://github.com/cirrus-8/framework/commit/6efb5abece9469558d095875587511a48873db4d))

## [1.1.0-alpha.2](https://github.com/cirrus-8/framework/compare/v1.1.0-alpha.1...v1.1.0-alpha.2) (2025-06-12)

## [1.1.0-alpha.1](https://github.com/cirrus-8/framework/compare/v1.1.0-alpha.0...v1.1.0-alpha.1) (2025-06-11)

## [1.1.0-alpha.0](https://github.com/cirrus-8/framework/compare/v1.0.0...v1.1.0-alpha.0) (2025-06-10)

### Features

- add release scripts and versions ([7a374e1](https://github.com/cirrus-8/framework/commit/7a374e16bcd9884807c5a3c84b57be9af757f7c7))
- **packages:** add phpexcel and bugsnag ([a6dbcf4](https://github.com/cirrus-8/framework/commit/a6dbcf4d1d978a79cdc01cf004005060706fb875))

### Bug Fixes

- fix array access in fastJson ([8115d91](https://github.com/cirrus-8/framework/commit/8115d917e500bcef8da8f74eebe35d0a569e24e6))
- fix column count for Portfolio manage income graph ([15ed6b2](https://github.com/cirrus-8/framework/commit/15ed6b2b95103fbd1b4444ed62989a899ef55a58))
- improve prettier ignore and ts lib ([585e641](https://github.com/cirrus-8/framework/commit/585e641420adb849a94b385597775d9643361adb))
- issue in budget query when year is undefined ([368153e](https://github.com/cirrus-8/framework/commit/368153eed1f894c2bef0231b429cb3821d410fe4))
- missing css ([#35](https://github.com/cirrus-8/framework/issues/35)) ([e1b9f48](https://github.com/cirrus-8/framework/commit/e1b9f48c5282d51a7a9eedebd7668a28e3abe507))

## 1.0.0 (2025-05-27)

### Features

- change the source data of year list in owner budget and make the year mandatory when sending Owner budget. ([2f97700](https://github.com/cirrus-8/framework/commit/2f97700f1d4878ac46b82c08b39cd4f39d667ac1))
- disable permission check ([5f8873c](https://github.com/cirrus-8/framework/commit/5f8873cb8f3a5ec3ea10ef987c622d1e516fc262))
- enable the AnnounceKit widget for all users ([ccc5fd0](https://github.com/cirrus-8/framework/commit/ccc5fd0965f4461bb30def5569d89b774fae276e))
- **packages:** add phpexcel and bugsnag ([0ce6c2a](https://github.com/cirrus-8/framework/commit/0ce6c2ad0d069435c11e9c30ff4eb66862cd07b6))

### Bug Fixes

- Business Intelligence graph not displaying ([#19](https://github.com/cirrus-8/framework/issues/19)) ([87368ce](https://github.com/cirrus-8/framework/commit/87368ce7d80e647ce0e521ea779328110ee443ef))
- commit live changes to main ([#18](https://github.com/cirrus-8/framework/issues/18)) ([c911bc5](https://github.com/cirrus-8/framework/commit/c911bc53af2e5ec9c81f946294a818ef331951ee))
- do not execute function if propertyID is empty ([66b11ee](https://github.com/cirrus-8/framework/commit/66b11ee7b9ee234bfadfc809d86282f186960ad9))
- fix array access in fastJson ([dc1926e](https://github.com/cirrus-8/framework/commit/dc1926e8301bcfad4392c77828d1f2b9df4db21a))
- improve prettier ignore and ts lib ([c6069e8](https://github.com/cirrus-8/framework/commit/c6069e80d5c3ee6909d3c69c2009809e074f08b0))
- missing budget placeholder in property owner budget ([0c6f168](https://github.com/cirrus-8/framework/commit/0c6f1688fd8560416a440768eb554311ef619400))
- missing budget placeholder in property owner budget ([b227905](https://github.com/cirrus-8/framework/commit/b227905ab7720d800781fb189ca9125a787f0f1f))
- missing budget placeholder in property owner budget ([a6ad1d2](https://github.com/cirrus-8/framework/commit/a6ad1d2d20a3d64683f803d62a79e1c8135f2c04))
- parameter is string or array ([03371b5](https://github.com/cirrus-8/framework/commit/03371b5645ddae735ff43667537d0a189c71a3d6))
- remove parameter type ([c64dd8a](https://github.com/cirrus-8/framework/commit/c64dd8af3054827853f57c40ada2051d68756ec0))
- remove two parameter type ([60179f3](https://github.com/cirrus-8/framework/commit/60179f32205cbabf41564a4fc56b1fd865d0988f))
- show distinct budget year in budget year list ([fa06a84](https://github.com/cirrus-8/framework/commit/fa06a84f0e582df4c659932117484b63e5b10d02))
- show distinct budget year in budget year list ([f3ed7ba](https://github.com/cirrus-8/framework/commit/f3ed7ba5698f1b9ed0b8ba3e07c1792f7ef97bb4))
- show distinct budget year in budget year list ([97b72d6](https://github.com/cirrus-8/framework/commit/97b72d6ac0329fa0a9b133bfd56b3f7dedb449c4))

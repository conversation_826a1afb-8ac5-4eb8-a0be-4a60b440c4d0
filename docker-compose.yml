services:
  framework-web:
    image: cgr.dev/chainguard/nginx:latest
    restart: unless-stopped
    ports:
      - '8081:80'
    volumes:
      - ./framework:/app
      - ./docker/site.conf:/etc/nginx/conf.d/site.conf
    depends_on:
      - framework-php
    networks:
      - framework_network

  framework-php:
    build: .
    volumes:
      - ./framework:/app
      - ./docker/docker-php-ext-xdebug.ini:/usr/local/etc/php/conf.d/docker-php-ext-xdebug.ini
    restart: unless-stopped
    extra_hosts:
      - "host.docker.internal:host-gateway"
    networks:
      - framework_network
    working_dir: /app
    command: php-fpm
    environment:
      - XDEBUG_MODE=debug
#      - XDEBUG_CONFIG=client_host=host.docker.internal client_port=9003 idekey=PHPSTORM
      - XDEBUG_CONFIG=remote_host=host.docker.internal remote_port=9003 idekey=PHPSTORM
      - PHP_IDE_CONFIG=serverName=framework-php
#    environment:
      # Xdebug 2.9 uses remote_* keys in XDEBUG_CONFIG
      # This helps PhpStorm pick the right server entry (match your Servers name)
    expose:
      - 9000
      - 9003


  framework-redis:
    image: cgr.dev/chainguard/redis:latest
    ports:
      # Different port to api
      - '6399:6379'
    restart: unless-stopped
    volumes:
      - redis-data:/data
    networks:
      - framework_network

volumes:
  redis-data:

networks:
  framework_network:
    name: c8_network
    external: true

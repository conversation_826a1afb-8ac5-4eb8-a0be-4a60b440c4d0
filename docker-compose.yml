services:
  framework-web:
    image: cgr.dev/chainguard/nginx:latest
    restart: unless-stopped
    ports:
      - '8081:80'
    volumes:
      - ./framework:/app
      - ./docker/site.conf:/etc/nginx/conf.d/site.conf
    depends_on:
      - framework-php
    networks:
      - framework_network

  framework-php:
    build: .
    volumes:
      - ./framework:/app
      - ./docker/docker-php-ext-xdebug.ini:/usr/local/etc/php/conf.d/docker-php-ext-xdebug.ini
    restart: unless-stopped
    extra_hosts:
      host.docker.internal: host-gateway
    networks:
      - framework_network
    working_dir: /app
    command: php-fpm
    expose:
      - 9000

  framework-redis:
    image: cgr.dev/chainguard/redis:latest
    ports:
      # Different port to api
      - '6399:6379'
    restart: unless-stopped
    volumes:
      - redis-data:/data
    networks:
      - framework_network

volumes:
  redis-data:

networks:
  framework_network:
    name: c8_network
    external: true

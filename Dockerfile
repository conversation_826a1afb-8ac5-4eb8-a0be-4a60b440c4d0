# Base image with PHP 7.4
FROM php:7.4-fpm AS php-base

# Update and install prerequisites
RUN apt-get update && apt-get install -y \
    gnupg2 \
    wget \
    unixodbc-dev \
    libxml2-dev \
    libssl-dev \
    tdsodbc \
    gcc \
    g++ \
    make \
    libc-dev \
    lsb-release \
    libgssapi-krb5-2 \
    autoconf \
    curl \
    libcurl4 \
    libjpeg62-turbo-dev \
    libfreetype6-dev \
    libpng-dev \
    zlib1g-dev \
    libzip-dev \
    iputils-ping

# Install Microsoft ODBC Driver 18 for SQL Server and sqlcmd CLI
RUN curl https://packages.microsoft.com/keys/microsoft.asc | apt-key add - && \
    curl https://packages.microsoft.com/config/debian/11/prod.list > /etc/apt/sources.list.d/mssql-release.list && \
    apt-get update && ACCEPT_EULA=Y apt-get install -y msodbcsql18 mssql-tools18 && \
    echo 'export PATH="$PATH:/opt/mssql-tools18/bin"' >> ~/.bashrc && \
    ln -s /opt/mssql-tools18/bin/sqlcmd /usr/bin/sqlcmd

# Download and compile PDFlib from source
RUN wget -O /tmp/PDFlib.tar.gz https://www.pdflib.com/binaries/PDFlib/931/PDFlib-9.3.1p5-Linux-aarch64-php.tar.gz && \
    mkdir -p /usr/src/pdflib && \
    tar -xzf /tmp/PDFlib.tar.gz -C /usr/src/pdflib --strip-components=1 && \
    mkdir -p $(php-config --extension-dir) && \
    cp /usr/src/pdflib/bind/php/php-740-nts/php_pdflib.so $(php-config --extension-dir)/ && \
    rm -rf /usr/src/pdflib /tmp/PDFlib.tar.gz

# Enable the PDFlib extension,  Install SQL Server drivers for PHP
# Dont enable xdebug - mount in with docker compose
RUN pecl install xdebug-2.9.8 redis sqlsrv-5.10.1 pdo_sqlsrv-5.10.1 && \
    docker-php-ext-configure intl && \
    docker-php-ext-install -j$(nproc) intl && \
    docker-php-ext-enable xdebug php_pdflib && \
    docker-php-ext-install gd && \
    docker-php-ext-install opcache && \
    docker-php-ext-install calendar && \
    docker-php-ext-install bcmath && \
    docker-php-ext-install soap && \
    docker-php-ext-install zip && \
    docker-php-ext-install session && \
    docker-php-ext-enable zip session && \
    docker-php-ext-enable redis && \
    docker-php-ext-enable sqlsrv pdo_sqlsrv

# Install Composer
COPY --from=composer:2 /usr/bin/composer /usr/bin/composer

FROM php-base AS php-app

WORKDIR /app

COPY . .

EXPOSE 9000
CMD ["php-fpm"]
